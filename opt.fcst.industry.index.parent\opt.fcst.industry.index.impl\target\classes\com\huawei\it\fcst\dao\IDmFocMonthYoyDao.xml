<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IDmFocMonthYoyDao">
    <resultMap type="com.huawei.it.fcst.industry.index.vo.month.DmFocMonthYoyVO" id="resultMap">
        <result property="id" column="id"/>
        <result property="versionId" column="version_id"/>
        <result property="periodYear" column="period_year"/>
        <result property="periodId" column="period_id"/>
        <result property="basePeriodId" column="base_period_id"/>
        <result property="prodRndTeamCode" column="prod_rnd_team_code"/>
        <result property="prodRndTeamCnName" column="prod_rnd_team_cn_name"/>
        <result property="groupCode" column="group_code"/>
        <result property="groupCnName" column="group_cn_name"/>
        <result property="groupLevel" column="group_level"/>
        <result property="yoyRate" column="yoy_rate"/>
        <result property="yoyPercent" column="yoy_percent"/>
        <result property="yoyFlag" column="RATE_FLAG"/>
        <result property="parentCode" column="parent_code"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="l1Name" column="l1_name"/>
        <result property="l2Name" column="l2_name"/>
        <result property="dmsCode" column="dms_code"/>
        <result property="dmsCnName" column="dms_cn_name"/>
        <result property="coaCode" column="coa_code"/>
        <result property="coaCnName" column="coa_cn_name"/>
        <result property="dimensionCode" column="dimension_code"/>
        <result property="dimensionCnName" column="dimension_cn_name"/>
        <result property="dimensionSubCategoryCode" column="dimension_subcategory_code"/>
        <result property="dimensionSubCategoryCnName" column="dimension_subcategory_cn_name"/>
        <result property="dimensionSubDetailCode" column="dimension_sub_detail_code"/>
        <result property="dimensionSubDetailCnName" column="dimension_sub_detail_cn_name"/>
        <result property="spartCode" column="spart_code"/>
        <result property="spartCnName" column="spart_cn_name"/>
        <result property="customCnName" column="custom_cn_name"/>
        <result property="costType" column="cost_type"/>
    </resultMap> 

    <sql id="allField">
        <if test ='searchParamsVO.granularityType == "P"'>
            l1_name, l2_name,
        </if>
        <if test ='searchParamsVO.granularityType == "D"'>
            dms_code,dms_cn_name,
            <if test='searchParamsVO.industryOrg == "ENERGY"'>
                coa_code, coa_cn_name,
            </if>
            dimension_code,dimension_cn_name,dimension_subcategory_code,
            dimension_subcategory_cn_name,dimension_sub_detail_code,
            dimension_sub_detail_cn_name,spart_code,spart_cn_name,
        </if>
        prod_rnd_team_cn_name,
        group_cn_name,
        group_level,
        group_code,
        period_year,
        period_id,
        ROUND(RATE, 3) AS yoy_rate,
        ROUND(RATE * 100, 2) || '%' AS yoy_percent,
        RATE_FLAG,
        version_id,
        'P' AS cost_type
    </sql>

    <select id="findDmFocMonthYoyVOList" resultMap="resultMap">
        SELECT
        <include refid="allField"/>
        <if test='searchParamsVO.granularityType == "U"'>
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_month_rate_t
        </if>
        <if test='searchParamsVO.granularityType == "P"'>
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_pft_month_rate_t
        </if>
        <if test='searchParamsVO.granularityType == "D"'>
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_dms_month_rate_t
        </if>
        WHERE del_flag = 'N'
        <if test='searchParamsVO.lv0ProdListCode != null'>
            and lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.yoyFlag != null'>
            AND RATE_FLAG = #{searchParamsVO.yoyFlag}
        </if>
        <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
            AND caliber_Flag = #{searchParamsVO.caliberFlag}
        </if>
        <if test='searchParamsVO.versionId != null'>
            AND version_id = #{searchParamsVO.versionId}
        </if>
        <if test='searchParamsVO.coaCodeList != null and searchParamsVO.coaCodeList.size() > 0'>
            <foreach collection='searchParamsVO.coaCodeList' item="code" open="AND coa_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionCodeList != null and searchParamsVO.dimensionCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionCodeList' item="code" open="AND dimension_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubcategoryCodeList != null and searchParamsVO.dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubcategoryCodeList' item="code" open="AND dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubDetailCodeList != null and searchParamsVO.dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubDetailCodeList' item="code" open="AND dimension_sub_detail_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.spartCodeList != null and searchParamsVO.spartCodeList.size() > 0'>
            <foreach collection='searchParamsVO.spartCodeList' item="code" open="AND spart_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.prodRndTeamCodeList != null and searchParamsVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='searchParamsVO.prodRndTeamCodeList' item="code" open="AND prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l1NameList != null and searchParamsVO.l1NameList.size() > 0'>
            <foreach collection='searchParamsVO.l1NameList' item="code" open="AND l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l2NameList != null and searchParamsVO.l2NameList.size() > 0'>
            <foreach collection='searchParamsVO.l2NameList' item="code" open="AND l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupCodeList != null and searchParamsVO.groupCodeList != ""'>
            <foreach collection='searchParamsVO.groupCodeList' item="code" open="AND group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.periodStartTime != null and searchParamsVO.periodStartTime != ""'>
            AND period_id <![CDATA[ >= ]]> #{searchParamsVO.periodStartTime}
        </if>
        <if test='searchParamsVO.periodEndTime != null and searchParamsVO.periodEndTime != ""'>
            AND period_id <![CDATA[ <= ]]> #{searchParamsVO.periodEndTime}
        </if>
        <if test='searchParamsVO.groupLevel =="LV1" and searchParamsVO.lv1DimensionSet != null and searchParamsVO.lv1DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv1DimensionSet' item="code" open="AND prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupLevel =="LV2" and searchParamsVO.lv2DimensionSet != null and searchParamsVO.lv2DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv2DimensionSet' item="code" open="AND prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.overseaFlag != null'>
            and oversea_flag = #{searchParamsVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.viewFlag != null'>
            AND view_flag = #{searchParamsVO.viewFlag}
        </if>
        <if test='searchParamsVO.groupLevel != null'>
            AND group_level = #{searchParamsVO.groupLevel}
        </if>
        ORDER BY period_id
    </select>

    <select id="findDmFocMonthCombYoyVOList" resultMap="resultMap">
        SELECT
        custom_cn_name,
        group_level,
        group_code,
        period_year,
        period_id,
        group_level,
        group_cn_name,
        ROUND(RATE, 3) AS yoy_rate,
        ROUND(RATE * 100, 2) || '%' AS yoy_percent,
        RATE_FLAG,
        version_id,
        'P' AS cost_type
        FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_custom_month_rate_t
        WHERE del_flag = 'N'
        <if test='searchParamsVO.granularityType != null'>
            AND granularity_type = #{searchParamsVO.granularityType}
        </if>
        <if test='searchParamsVO.lv0ProdListCode != null'>
            and lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.yoyFlag != null'>
            AND rate_flag = #{searchParamsVO.yoyFlag}
        </if>
        <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
            AND caliber_Flag = #{searchParamsVO.caliberFlag}
        </if>
        <if test='searchParamsVO.versionId != null'>
            AND version_id = #{searchParamsVO.versionId}
        </if>
        <if test='searchParamsVO.combinaCodeList != null and searchParamsVO.combinaCodeList != ""'>
            <foreach collection='searchParamsVO.combinaCodeList' item="code" open="AND custom_id || '_##' || group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.customIdList != null and searchParamsVO.customIdList != ""'>
            <foreach collection='searchParamsVO.customIdList' item="code" open="AND custom_id IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.periodStartTime != null and searchParamsVO.periodStartTime != ""'>
            AND period_id <![CDATA[ >= ]]> #{searchParamsVO.periodStartTime}
        </if>
        <if test='searchParamsVO.periodEndTime != null and searchParamsVO.periodEndTime != ""'>
            AND period_id <![CDATA[ <= ]]> #{searchParamsVO.periodEndTime}
        </if>
        <if test='searchParamsVO.overseaFlag != null'>
            and oversea_flag = #{searchParamsVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.viewFlag != null'>
            AND view_flag = #{searchParamsVO.viewFlag}
        </if>
        <if test='searchParamsVO.groupLevel != null'>
            AND group_level = #{searchParamsVO.groupLevel}
        </if>
        ORDER BY period_id
    </select>

</mapper>
