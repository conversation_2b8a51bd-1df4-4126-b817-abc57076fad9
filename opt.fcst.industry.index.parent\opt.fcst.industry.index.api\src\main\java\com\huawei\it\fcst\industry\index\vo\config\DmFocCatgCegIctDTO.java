/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.config;

import com.fasterxml.jackson.annotation.JsonFormat;

import com.huawei.it.fcst.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * DmFocCatgCegIctDTO Class
 *
 * <AUTHOR>
 * @since 2023/3/13
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "品类专家团实体类映射表")
public class DmFocCatgCegIctDTO extends BaseVO {
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private Long  versionId;

    /**
     * 品类编码
     **/
    @ApiModelProperty(value = "品类编码")
    private String categoryCode;

    /**
     * 品类名称
     **/
    @ApiModelProperty(value = "品类名称")
    private String categoryCnName;

    /**
     * 专家团（GROUP LV3全称）
     **/
    @ApiModelProperty(value = "专家团（GROUP LV3全称")
    private String l3CegCnName;

    /**
     * 专家团编码
     **/
    @ApiModelProperty(value = "专家团编码")
    private String l3CegCode;

    /**
     * 专家团简称（Group LV3简称）
     **/
    @ApiModelProperty(value = "专家团（Group LV3简称）")
    private String l3CegShortCnName;

    /**
     * 模块（GROUP LV4全称)
     **/
    @ApiModelProperty(value = "模块（GROUP LV4全称")
    private String l4CegCnName;

    /**
     * 模块编码
     **/
    @ApiModelProperty(value = "模块编码")
    private String l4CegCode;

    /**
     * 模块简称（Group LV4简称）
     **/
    @ApiModelProperty(value = "模块（Group LV4简称）")
    private String l4CegShortCnName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "修改时间")
    private Date lastUpdatedDate;

    /**
     * 保存方式（P:页面保存、A:后台补录）
     **/
    @ApiModelProperty("save_method")
    private String saveMethod;

    /**
     * 组织机构区分
     **/
    @ApiModelProperty("industryOrg")
    private String industryOrg;

    /**
     * 表名前缀
     **/
    @ApiModelProperty("tablePreFix")
    private String tablePreFix;


}
