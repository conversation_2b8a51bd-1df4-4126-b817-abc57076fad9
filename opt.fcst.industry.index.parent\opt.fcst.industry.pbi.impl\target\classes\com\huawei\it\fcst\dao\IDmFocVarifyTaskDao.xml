<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.pbi.dao.IDmFocVarifyTaskDao">
    <resultMap type="com.huawei.it.fcst.industry.pbi.vo.common.DmFocVarifyTaskVO" id="resultTaskMap">
        <result property="taskId" column="task_id" />
        <result property="status" column="status" />
        <result property="periodId" column="period_id" />
        <result property="taskType" column="task_type" />
        <result property="combStatus" column="comb_status" />
        <result property="creationDate" column="creation_date" />
        <result property="lastUpdateDate" column="creation_date" />
    </resultMap>

    <select id="searchVerifyTask" resultMap="resultTaskMap">
        SELECT task_id,period_id,status,del_flag,task_type,comb_status,creation_date,last_update_date
        FROM fin_dm_opt_foi.dm_foc_varify_task_t
        WHERE task_id = #{varifyTaskVO.taskId}
        AND task_type =#{varifyTaskVO.taskType}
    </select>

    <select id="getVerifyTaskId" resultType="java.lang.Long" flushCache="true" useCache="false">
        SELECT fin_dm_opt_foi.dm_foc_varify_task_s.NEXTVAL
    </select>

    <insert id="insertVerifyTask">
        INSERT INTO fin_dm_opt_foi.dm_foc_varify_task_t(task_id,status,period_id,creation_date,del_flag,task_type,comb_status)
        VALUES (#{taskId},#{status},#{periodId},now(),'N',#{taskType},#{combStatus})
    </insert>

    <update id="updateVerifyTask">
        UPDATE fin_dm_opt_foi.dm_foc_varify_task_t SET
        <if test='status != null'>
            status =#{status},
        </if>
        <if test='combStatus != null'>
            comb_status =#{combStatus},
        </if>
        last_update_date =#{lastUpdateDate}
        WHERE task_id = #{taskId}
    </update>

</mapper>
