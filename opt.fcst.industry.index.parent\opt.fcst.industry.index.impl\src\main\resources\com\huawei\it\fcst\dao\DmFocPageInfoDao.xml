<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IDmFocPageInfoDao">
    <resultMap type="com.huawei.it.fcst.industry.index.vo.month.DmFocPageInfoVO" id="resultMap">
        <result property="pageId" column="page_id"/>
        <result property="versionId" column="version_id"/>
        <result property="delFlag" column="del_flag"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="pageFlag" column="page_flag"/>
        <result property="userId" column="user_id"/>
        <result property="pageName" column="page_name"/>
        <result property="defaultFlag" column="default_flag"/>
        <result property="creationDate" column="creation_date"/>
        <result property="createdBy" column="created_by"/>
        <result property="saveThreshold" column="save_threshold"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="roleId" column="role_id"/>
        <result property="dataRange" column="data_range"/>
        <result property="caliberFlag" column="caliber_flag"/>

    </resultMap>

    <sql id="allFields">
        page_id,
        version_id,
        del_flag,
        last_updated_by,
        page_flag,
        user_id,
        page_name,
        default_flag,
        creation_date,
        created_by,
        save_threshold,
        last_update_date,
        role_id,
        data_range,
        caliber_flag
    </sql>

    <sql id="allValues">
        #{pageId,jdbcType=NUMERIC},
        #{versionId,jdbcType=NUMERIC},
        'N',
        #{lastUpdatedBy,jdbcType=NUMERIC},
        #{pageFlag,jdbcType=VARCHAR},
        #{userId,jdbcType=VARCHAR},
        #{pageName,jdbcType=VARCHAR},
        #{defaultFlag,jdbcType=VARCHAR},
        NOW(),
        #{createdBy,jdbcType=NUMERIC},
        #{saveThreshold,jdbcType=VARCHAR},
        NOW(),
        #{roleId,jdbcType=VARCHAR},
        #{dataRange,jdbcType=VARCHAR},
        #{caliberFlag,jdbcType=VARCHAR}
    </sql>

    <sql id="uniqueKeyField">
        page_id=#{pageId,jdbcType=NUMERIC}
    </sql>

    <sql id="setValues">
        <if test='pageId != null'>
            page_id = #{pageId,jdbcType=NUMERIC},
        </if>
        <if test='versionId != null'>
            version_id = #{versionId,jdbcType=NUMERIC},
        </if>
        <if test='delFlag != null'>
            del_flag = #{delFlag,jdbcType=VARCHAR},
        </if>
        <if test='pageFlag != null and pageFlag != ""'>
            page_flag = #{pageFlag,jdbcType=VARCHAR},
        </if>
        <if test='userId != null and userId != ""'>
            user_id = #{userId,jdbcType=VARCHAR},
        </if>
        <if test='pageName != null and pageName != ""'>
            page_name = #{pageName,jdbcType=VARCHAR},
        </if>
        <if test='defaultFlag != null and defaultFlag != ""'>
            default_flag = #{defaultFlag,jdbcType=VARCHAR},
        </if>
        <if test='saveThreshold != null and saveThreshold != ""'>
            save_threshold = #{saveThreshold,jdbcType=VARCHAR},
        </if>
        last_update_date = NOW(),
        <if test='lastUpdatedBy != null and lastUpdatedBy != ""'>
            last_updated_by = #{lastUpdatedBy,jdbcType=NUMERIC},
        </if>
        <if test='roleId != null and roleId != ""'>
            role_id = #{roleId,jdbcType=VARCHAR},
        </if>
        <if test='dataRange != null and dataRange != ""'>
            data_range = #{dataRange,jdbcType=VARCHAR},
        </if>
        <if test='caliberFlag != null and caliberFlag != ""'>
            caliber_flag = #{caliberFlag,jdbcType=VARCHAR},
        </if>
    </sql>

    <sql id="searchFields">
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='_parameter.get("0").pageId != null'>
                AND page_id=#{0.pageId,jdbcType=NUMERIC}
            </if>
            <if test='_parameter.get("0").delFlag != null'>
                AND del_flag LIKE CONCAT(CONCAT('%', #{0.delFlag,jdbcType=VARCHAR}) ,'%')
            </if>
            <if test='_parameter.get("0").lastUpdatedBy != null'>
                AND last_updated_by LIKE CONCAT(CONCAT('%', #{0.lastUpdatedBy,jdbcType=NUMERIC}) ,'%')
            </if>
            <if test='_parameter.get("0").pageFlag != null'>
                AND page_flag LIKE CONCAT(CONCAT('%', #{0.pageFlag,jdbcType=VARCHAR}) ,'%')
            </if>
            <if test='_parameter.get("0").userId != null'>
                AND user_id=#{0.userId,jdbcType=VARCHAR}
            </if>
            <if test='_parameter.get("0").pageName != null'>
                AND page_name LIKE CONCAT(CONCAT('%', #{0.pageName,jdbcType=VARCHAR}) ,'%')
            </if>
            <if test='_parameter.get("0").creationDate != null'>
                AND creation_date=#{0.creationDate,jdbcType=TIMESTAMP}
            </if>
            <if test='_parameter.get("0").createdBy != null'>
                AND created_by LIKE CONCAT(CONCAT('%', #{0.createdBy,jdbcType=NUMERIC}) ,'%')
            </if>
            <if test='_parameter.get("0").saveThreshold != null'>
                AND save_threshold LIKE CONCAT(CONCAT('%', #{0.saveThreshold,jdbcType=VARCHAR}) ,'%')
            </if>
            <if test='_parameter.get("0").lastUpdateDate != null'>
                AND last_update_date=#{0.lastUpdateDate,jdbcType=TIMESTAMP}
            </if>
            <if test='_parameter.get("0").roleId != null and _parameter.get("0").roleId != ""'>
                AND role_id = #{0.roleId,jdbcType=VARCHAR}
            </if>
        </trim>

    </sql>

    <select id="findPageInfoVOList" resultMap="resultMap">
        SELECT
        <include refid="allFields"/>
        FROM fin_dm_opt_foi.${pageInfoVO.tablePreFix}_page_info_t
        WHERE del_flag = 'N'
        <if test='pageInfoVO.pageId != null'>
            AND page_id = #{pageInfoVO.pageId,jdbcType=NUMERIC}
        </if>
        <if test='pageInfoVO.versionId != null'>
            AND version_id = #{pageInfoVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='pageInfoVO.caliberFlag != null and pageInfoVO.caliberFlag != ""'>
            AND caliber_flag = #{pageInfoVO.caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='pageInfoVO.pageFlag != null and pageInfoVO.pageFlag != ""'>
            AND page_flag = #{pageInfoVO.pageFlag,jdbcType=VARCHAR}
        </if>
        <if test='pageInfoVO.userId != null and pageInfoVO.userId != ""'>
            AND UPPER(user_id) = UPPER(#{pageInfoVO.userId,jdbcType=VARCHAR})
        </if>
        <if test='pageInfoVO.pageName != null and pageInfoVO.pageName != ""'>
            AND page_name LIKE CONCAT(CONCAT('%', #{pageInfoVO.pageName,jdbcType=VARCHAR}) ,'%')
        </if>
        <if test='pageInfoVO.defaultFlag != null and pageInfoVO.defaultFlag != ""'>
            AND default_flag = #{pageInfoVO.defaultFlag,jdbcType=VARCHAR}
        </if>
        <if test='pageInfoVO.roleId != null and pageInfoVO.roleId != ""'>
            AND role_id = #{pageInfoVO.roleId,jdbcType=VARCHAR}
        </if>
        ORDER BY creation_date
    </select>

    <select id="findDmFocPageInfoVOById" parameterType="java.lang.Long" resultMap="resultMap">
        SELECT 
        <include refid="allFields"/>
        FROM fin_dm_opt_foi.${tablePreFix}_page_info_t
        WHERE del_flag = 'N'
        AND page_id=#{pageId,jdbcType=NUMERIC}
    </select>

    <insert id="createDmFocPageInfoVO" parameterType="com.huawei.it.fcst.industry.index.vo.month.DmFocPageInfoVO">
        <selectKey keyProperty="pageId" order="BEFORE" resultType="java.lang.Long">
            SELECT fin_dm_opt_foi.${tablePreFix}_page_info_s.nextval
        </selectKey>
        INSERT INTO fin_dm_opt_foi.${tablePreFix}_page_info_t
        (<include refid="allFields"/>) 
        VALUES 
        (<include refid="allValues"/>) 
    </insert>

    <insert id="createDmFocPageInfoVOList" parameterType="java.util.List">
            INSERT INTO fin_dm_opt_foi.${tablePreFix}_page_info_t
            (<include refid="allFields"/>) 
            VALUES 
            <foreach collection="list" item="item" separator=",">
                fin_dm_opt_foi.${tablePreFix}_page_info_s.nextval,
                 'N',
                 #{item.lastUpdatedBy,jdbcType=NUMERIC},
                 #{item.pageFlag,jdbcType=VARCHAR},
                 #{item.userId,jdbcType=VARCHAR},
                 #{item.pageName,jdbcType=VARCHAR},
                 #{item.creationDate,jdbcType=TIMESTAMP},
                 #{item.createdBy,jdbcType=NUMERIC},
                 #{item.saveThreshold,jdbcType=VARCHAR},
                 #{item.lastUpdateDate,jdbcType=TIMESTAMP})
            </foreach> 
    </insert>

    <update id="updateDmFocPageInfoVO" parameterType="com.huawei.it.fcst.industry.index.vo.month.DmFocPageInfoVO">
        UPDATE fin_dm_opt_foi.${tablePreFix}_page_info_t
        <set> <include refid="setValues"/></set>
        WHERE 
        <include refid="uniqueKeyField"/>
    </update>

    <update id="updateDmFocPageInfoVOList" parameterType="java.util.List">
         <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            UPDATE fin_dm_opt_foi.${tablePreFix}_page_info_t
            <set> 
                <if test='item.pageId != null'>
                    page_id = #{item.pageId,jdbcType=NUMERIC},
                </if>
                <if test='item.delFlag != null'>
                    del_flag = #{item.delFlag,jdbcType=VARCHAR},
                </if>
                <if test='item.lastUpdatedBy != null'>
                    last_updated_by = #{item.lastUpdatedBy,jdbcType=NUMERIC},
                </if>
                <if test='item.pageFlag != null'>
                    page_flag = #{item.pageFlag,jdbcType=VARCHAR},
                </if>
                <if test='item.userId != null'>
                    user_id = #{item.userId,jdbcType=VARCHAR},
                </if>
                <if test='item.pageName != null'>
                    page_name = #{item.pageName,jdbcType=VARCHAR},
                </if>
                <if test='item.saveThreshold != null'>
                    save_threshold = #{item.saveThreshold,jdbcType=VARCHAR},
                </if>
                <if test='item.lastUpdateDate != null'>
                    last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP},
                </if>
            </set>
            WHERE 
            page_id = #{item.pageId,jdbcType=NUMERIC}
        </foreach> 
    </update>

    <delete id="deleteDmFocPageInfoVO" parameterType="String">
        DELETE 
        FROM fin_dm_opt_foi.${tablePreFix}_page_info_t
        WHERE 
        <include refid="uniqueKeyField"/>
    </delete>

    <delete id="deleteDmFocPageInfoVOList" parameterType="java.util.List">
        DELETE 
        FROM fin_dm_opt_foi.${tablePreFix}_page_info_t
        WHERE page_id IN 
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.pageId,jdbcType=NUMERIC}
         </foreach>
    </delete>
</mapper>
