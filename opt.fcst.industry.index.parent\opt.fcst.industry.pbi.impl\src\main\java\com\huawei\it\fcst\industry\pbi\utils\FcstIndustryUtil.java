/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2018-2019. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.utils;

import cn.hutool.core.util.NumberUtil;
import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.industry.pbi.enums.GroupLevelEnum;
import com.huawei.it.fcst.industry.pbi.vo.common.CommonBaseVO;
import com.huawei.it.fcst.industry.pbi.vo.month.IctMonthAnalysisVO;
import com.huawei.it.fcst.util.UserInfoUtils;
import com.huawei.it.fcst.vo.UploadInfoVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * ParseData.
 *
 * <AUTHOR>
 * @since 2019年12月16日
 */
public class FcstIndustryUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(FcstIndustryUtil.class);

    private static final Pattern PATTERN_SPECIAL = Pattern.compile("[!@#$%^&?\\\":{}|<>]");

    // group层级下一层级映射
    private static Map<String, String> viewTwoGroupLevelMap = new HashMap<>(6);

    static {
        viewTwoGroupLevelMap.put(GroupLevelEnum.LV0.getValue(), GroupLevelEnum.LV1.getValue());
        viewTwoGroupLevelMap.put(GroupLevelEnum.LV1.getValue(), GroupLevelEnum.LV2.getValue());
        viewTwoGroupLevelMap.put(GroupLevelEnum.LV2.getValue(), GroupLevelEnum.LV3.getValue());
        viewTwoGroupLevelMap.put(GroupLevelEnum.LV3.getValue(), GroupLevelEnum.LV4.getValue());
        viewTwoGroupLevelMap.put(GroupLevelEnum.LV4.getValue(), GroupLevelEnum.DIMENSION.getValue());
        viewTwoGroupLevelMap.put(GroupLevelEnum.DIMENSION.getValue(), GroupLevelEnum.SUBCATEGORY.getValue());
        viewTwoGroupLevelMap.put(GroupLevelEnum.SUBCATEGORY.getValue(), GroupLevelEnum.SUB_DETAIL.getValue());
        viewTwoGroupLevelMap.put(GroupLevelEnum.SUB_DETAIL.getValue(), GroupLevelEnum.SUB_DETAIL.getValue());
    }

    private static Map<String, String> viewOneGroupLevelMap = new HashMap<>(6);

    static {
        viewOneGroupLevelMap.put(GroupLevelEnum.LV0.getValue(), GroupLevelEnum.LV1.getValue());
        viewOneGroupLevelMap.put(GroupLevelEnum.LV1.getValue(), GroupLevelEnum.LV2.getValue());
        viewOneGroupLevelMap.put(GroupLevelEnum.LV2.getValue(), GroupLevelEnum.LV3.getValue());
        viewOneGroupLevelMap.put(GroupLevelEnum.LV3.getValue(), GroupLevelEnum.LV4.getValue());
        viewOneGroupLevelMap.put(GroupLevelEnum.LV4.getValue(), GroupLevelEnum.SPART.getValue());
    }

    /**
     * 获取下一层级的Group层级 :路径1：PROD_SPART/路径2：DIMENSION
     *
     * @return String 下一层级的Group层级
     */
    public static String getViewNextGroupLevel(CommonBaseVO commonBaseVO) {
        if ("PROD_SPART".equals(commonBaseVO.getViewFlag())) {
            return viewOneGroupLevelMap.get(commonBaseVO.getGroupLevel());
        }
        return viewTwoGroupLevelMap.get(commonBaseVO.getGroupLevel());
    }

    public static Map<String, String> getNextGroupLevel(CommonBaseVO commonBaseVO) {
        Map<String, String> map = new HashMap();
        String nextGroupLevel = getViewNextGroupLevel(commonBaseVO);
        String nextGroupName = GroupLevelEnum.getInstance(nextGroupLevel).getName();
        map.put("nextGroupLevel", nextGroupLevel);
        map.put("nextGroupName", nextGroupName);
        return map;
    }

    /**
     * 获取虚化的下一层级
     *
     * @param commonBaseVO vo
     * @return map
     */
    public static Map<String, String> getBlurNextGroupLevel(CommonBaseVO commonBaseVO) {
        Map<String, String> map = new HashMap();
        // 路径1：Spart 子层级都是 Spart 路径2：量纲  子层级都是 量纲子类明细
        String nextGroupLevel = "PROD_SPART".equals(commonBaseVO.getViewFlag())
                ? GroupLevelEnum.SPART.getValue() : GroupLevelEnum.SUB_DETAIL.getValue();
        String nextGroupName = GroupLevelEnum.getInstance(nextGroupLevel).getName();
        map.put("nextGroupLevel", nextGroupLevel);
        map.put("nextGroupName", nextGroupName);
        return map;
    }

    public static void setLvCode(CommonBaseVO commonBaseVO) {
        String lvCodeStr = null;
        // 只取最后一层级重量级团队编码
        if (CollectionUtils.isNotEmpty(commonBaseVO.getLv0ProdRndTeamCodeList())) {
            commonBaseVO.setParentLevel("LV0");
            lvCodeStr = commonBaseVO.getLv0ProdRndTeamCodeList().stream().collect(Collectors.joining(","));
        }
        if (CollectionUtils.isNotEmpty(commonBaseVO.getLv1ProdRndTeamCodeList())) {
            commonBaseVO.setParentLevel("LV1");
            lvCodeStr = commonBaseVO.getLv1ProdRndTeamCodeList().stream().collect(Collectors.joining(","));
        }
        if (CollectionUtils.isNotEmpty(commonBaseVO.getLv2ProdRndTeamCodeList())) {
            commonBaseVO.setParentLevel("LV2");
            lvCodeStr = commonBaseVO.getLv2ProdRndTeamCodeList().stream().collect(Collectors.joining(","));
        }
        if (CollectionUtils.isNotEmpty(commonBaseVO.getLv3ProdRndTeamCodeList())) {
            commonBaseVO.setParentLevel("LV3");
            lvCodeStr = commonBaseVO.getLv3ProdRndTeamCodeList().stream().collect(Collectors.joining(","));
        }
        if (CollectionUtils.isNotEmpty(commonBaseVO.getLv4ProdRndTeamCodeList())) {
            commonBaseVO.setParentLevel("LV4");
            lvCodeStr = commonBaseVO.getLv4ProdRndTeamCodeList().stream().collect(Collectors.joining(","));
        }
        lvCodeStr = setLvCodeForReplace(commonBaseVO, lvCodeStr);
        if (StringUtils.isNotBlank(lvCodeStr)) {
            commonBaseVO.setLvCode("'" + lvCodeStr + "'");
        }
    }

    public static String setLvCodeForReplace(CommonBaseVO commonBaseVO, String lvCodeStr) {
        // 只取最后一层级重量级团队编码
        if (StringUtils.isNotBlank(commonBaseVO.getLv0ProdRndTeamCode())) {
            commonBaseVO.setParentLevel("LV0");
            lvCodeStr = commonBaseVO.getLv0ProdRndTeamCode();
        }
        if (StringUtils.isNotBlank(commonBaseVO.getLv1ProdRndTeamCode())) {
            commonBaseVO.setParentLevel("LV1");
            lvCodeStr = commonBaseVO.getLv1ProdRndTeamCode();
        }
        if (StringUtils.isNotBlank(commonBaseVO.getLv2ProdRndTeamCode())) {
            commonBaseVO.setParentLevel("LV2");
            lvCodeStr = commonBaseVO.getLv2ProdRndTeamCode();
        }
        if (StringUtils.isNotBlank(commonBaseVO.getLv3ProdRndTeamCode())) {
            commonBaseVO.setParentLevel("LV3");
            lvCodeStr = commonBaseVO.getLv3ProdRndTeamCode();
        }
        if (StringUtils.isNotBlank(commonBaseVO.getLv4ProdRndTeamCode())) {
            commonBaseVO.setParentLevel("LV4");
            lvCodeStr = commonBaseVO.getLv4ProdRndTeamCode();
        }
        return lvCodeStr;
    }

    public static void setRegionCode(CommonBaseVO commonBaseVO) {
        setSpecailCode(commonBaseVO);
        if ("Y".equals(commonBaseVO.getMainFlag()) && StringUtils.isBlank(commonBaseVO.getCodeAttributes())) {
            commonBaseVO.setCodeAttributes("全选");
        }
    }

    public static void setSpecailCode(CommonBaseVO commonBaseVO) {
        if ("GLOBAL".equals(commonBaseVO.getRegionCode()) && !"ALL".equals(commonBaseVO.getRepofficeCode())) {
            commonBaseVO.setRegionCode(null);
        }
    }

    public static List<String> getPeriod(String version) {
        String[] splitVersion = version.split("-");
        List<String> yearPeriodList = new ArrayList<>();
        if (splitVersion.length != 0) {
            int count = 3;
            String yearStr = splitVersion[0].substring(0, 4);
            int endYear = NumberUtil.parseInt(yearStr);
            String periodStr = endYear - 1 + "+" + endYear + "YTD";
            for (int idx = count; idx > 0; idx--) {
                int startYear = endYear - idx;
                yearPeriodList.add(String.valueOf(startYear));
            }
            yearPeriodList.add(endYear + "YTD");
            yearPeriodList.add(periodStr);
        }
        return yearPeriodList;
    }

    public static boolean verifySpecialCharacter(String value) {
        if (StringUtils.isBlank(value)) {
            return false;
        }
        return PATTERN_SPECIAL.matcher(value).find();
    }

    /**
     * 获取报告期范围内（过去2年+当年YTD，36个月）的开始和结束时间
     * 默认基期开始时间为（默认基期为（预测年份-2）年的1月份）
     * 默认基期结束时间为（预测年份的12月份或者预测年份的06月）
     *
     * @return List
     */
    public static List<Integer> getPeriodScope(String actualMonth) {
        String periodStartTime;
        String periodEndTime;
        // 0 代表空值
        if (StringUtils.isNotBlank(actualMonth) && !"0".equals(actualMonth)) {
            String yearStr = actualMonth.substring(0, 4);
            int year = Integer.valueOf(yearStr);
            periodStartTime = (year - 2) + "01";
            periodEndTime = actualMonth;
            List<Integer> periodTimeList = new ArrayList<>();
            if (null != periodStartTime && null != periodEndTime) {
                periodTimeList = Arrays.asList(Integer.valueOf(periodStartTime), Integer.valueOf(periodEndTime));
            }
            return periodTimeList;
        }
        return null;
    }

    /**
     * 处理报告期36个月的开始和结束时间
     * 得到基期的开始时间和结束时间
     */
    public static void handlePeriod(CommonBaseVO commonBaseVO, String actualMonth) {
        if (ObjectUtils.isEmpty(commonBaseVO.getPeriodStartTime())
                && ObjectUtils.isEmpty(commonBaseVO.getPeriodEndTime())) {
            List<Integer> periodScope = getPeriodScope(actualMonth);
            if (CollectionUtils.isNotEmpty(periodScope)) {
                commonBaseVO.setPeriodStartTime(periodScope.get(0));
                commonBaseVO.setPeriodEndTime(periodScope.get(1));
            }
        }
    }

    /**
     * 处理报告期范围内（过去1年+当年YTD）24个月的开始和结束时间
     * 得到基期的开始时间和结束时间
     *
     * @param monthAnalysisVO 参数
     */
    public static void handlePeriodForMonthYoy(IctMonthAnalysisVO monthAnalysisVO, String actualMonthMum) {
        String periodStartTime;
        if (StringUtils.isNotBlank(actualMonthMum)) {
            String yearStr = actualMonthMum.substring(0, 4);
            int year = Integer.valueOf(yearStr);
            periodStartTime = (year - 1) + "01";
            monthAnalysisVO.setPeriodStartTime(Integer.valueOf(periodStartTime));
            monthAnalysisVO.setPeriodEndTime(Integer.valueOf(actualMonthMum));
        }
    }

    public static String getActualMonth(String dataStr) {
        if (StringUtils.isBlank(dataStr) || dataStr.length() < 6) {
            return null;
        }
        // 定义输入时间格式
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyyMM");
        // 定义输出时间格式
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy年MM月");
        try {
            // 将字符串时间解析为Date对象
            Date date = inputFormat.parse(dataStr);
            // 将Date对象格式化为指定格式的字符串时间
            return outputFormat.format(date);
        } catch (ParseException ex) {
            LOGGER.error(">>>FcstIndustryUtil#getActualMonth ParseException ==> {}", ex.getMessage());
        }
        return null;
    }

    public static void checkTablePreFixParam(CommonBaseVO commonBaseVO) throws CommonApplicationException {
        if (StringUtils.isNotBlank(commonBaseVO.getCostType())) {
            if (!CommonConstant.COST_TYPE_LIST.stream().anyMatch(cost -> cost.equals(commonBaseVO.getCostType()))) {
                throw new CommonApplicationException("成本类型错误");
            }
        }
        if (!CommonConstant.GRANULARITY_TYPE_LIST.stream().anyMatch(cost -> cost.equals(commonBaseVO.getGranularityType()))) {
            throw new CommonApplicationException("目录树错误");
        }
    }

    public static void setCommonTablePreFix(CommonBaseVO commonBaseVO) throws CommonApplicationException {
        checkTablePreFixParam(commonBaseVO);
        commonBaseVO.setTablePreFix(commonBaseVO.getCostType() + "_" + commonBaseVO.getGranularityType());
    }

    public static UploadInfoVO getUploadInfoVO(Attachment attachment, Long versionId, Map<String, Object> params,
                                               String moduleName) throws IOException {
        UploadInfoVO uploadInfoVO = new UploadInfoVO();
        uploadInfoVO.setFileSize(attachment.getDataHandler().getInputStream().available() / 1024);
        uploadInfoVO.setFileName(getFileName(attachment).concat(String.valueOf(System.currentTimeMillis())));
        params.put("moduleName", moduleName);
        uploadInfoVO.setParams(params);
        uploadInfoVO.setUserId(UserInfoUtils.getUserId());
        uploadInfoVO.setVersionId(versionId);
        uploadInfoVO.setCreationDate(getCurTime());
        return uploadInfoVO;
    }

    public static Timestamp getCurTime() {
        Date dt = new Date();
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String nowTime = df.format(dt);
        return Timestamp.valueOf(nowTime);
    }

    public static String getFileName(Attachment attachment) throws UnsupportedEncodingException {
        String fileName = new String(attachment.getDataHandler().getName().getBytes("ISO8859-1"), "UTF-8");
        int index = fileName.indexOf(".");
        return fileName.substring(0, index);
    }

}
