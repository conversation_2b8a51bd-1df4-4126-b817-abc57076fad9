/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.combination;

import com.huawei.it.fcst.industry.index.vo.common.CommonViewVO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;
import java.sql.Timestamp;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * CombinationVO Class
 *
 * <AUTHOR>
 * @since 2023/8/1
 */
@Data
@NoArgsConstructor
public class CombinationVO extends CommonViewVO {

    private static final long serialVersionUID = 3214140520956979373L;

    // 汇总组合名称
    private String customCnName;

    private String periodYear;

    // 如果同步则传ALL，如果不同步分两种情况，从年度页面创建的则是:ANNUAL,从月度页面创建传：MONTH
    private String pageFlag;

    private Long customId;

    // 某个重量级团队code是否已失效
    private String subEnableFlag;

    // 组合整体失效
    private String enableFlag;

    // 业务口径（R:收入时点/C：发货成本）
    private String caliberFlag;

    private String viewFlag;

    // 维度类型（U：通用/P：盈利颗粒度/D：量纲颗粒度）
    private String granularityType;

    private String teamLevel;

    // 国内海外标识(I:国内/O:海外/G:全球)
    private String overseaFlag;

    private String lv0ProdListCode;

    private String lv0ProdListCnName;

    // 页面进入的标志,从年度页面传ANNUAL，月度页面传MONTH
    private String pageSymbol;

    private String groupLevel;

    private String groupCode;

    private Long versionId;

    private Long monthVersionId;

    private Long energyVersionId;

    private Long energyMonthVersionId;

    private String granularityPageSymbol;

    private String roleId;

    private String userId;

    private String filterGroupLevel;

    // 是否解除过同步
    private String isSeparate;

    private String oldPageFlag;

    private String createdBy;

    private String lastUpdatedBy;

    private Timestamp creationDate;

    private Timestamp lastupdatedDate;

    private Set<String> lv0DimensionSet = new HashSet<>();

    private Set<String> lv1DimensionSet  = new HashSet<>();

    private Set<String> lv2DimensionSet  = new HashSet<>();

    private Set<String> lv3DimensionSet  = new HashSet<>();

    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<DmCustomCombVO> customVOList;

    // 页面上过滤后的list
    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<DmCustomCombVO> filterCustomVOList;

    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<DmCustomCombVO> parentCustomVOList;

    // 改变selectFlag状态的父类list
    private List<DmCustomCombVO> changeCustomVOList;

    private String lv0ProdRndTeamCode;

    private String lv1ProdRndTeamCode;

    private String lv2ProdRndTeamCode;

    private String lv3ProdRndTeamCode;

    private String l1Name;

    private String l2Name;

    private String dimensionCode;

    private String dimensionSubcategoryCode;

    private String dimensionSubDetailCode;

    private String l3CegCode;

    private String l4CegCode;

    private String categoryCode;

    private String connectCode;

    private Set<String> connectCodeSet;

    private String connectParentCode;

    // Y表示是第一次全新创建，N表示非全新空白创建
    private String firstFlag;

    // left 向左移动，right向右移动
    private String removeFlag;

}
