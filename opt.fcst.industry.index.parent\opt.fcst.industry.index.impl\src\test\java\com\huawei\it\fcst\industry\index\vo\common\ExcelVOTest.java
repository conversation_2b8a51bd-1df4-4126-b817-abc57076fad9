/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.common;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

/**
 * ExcelVOTest Class
 *
 * <AUTHOR>
 * @since 2023/4/22
 */
public class ExcelVOTest extends BaseVOCoverUtilsTest<ExcelVO> {

    @Override
    protected Class<ExcelVO> getTClass() {
        return ExcelVO.class;
    }

    @Test
    public void testMethod() {
        ExcelVO dmFocActualCostVO = new ExcelVO();
        dmFocActualCostVO.setHeadName("name");
        dmFocActualCostVO.getHeadName();
        dmFocActualCostVO.setHeadType("type");
        dmFocActualCostVO.getHeadType();
        dmFocActualCostVO.setHeadValue("value");
        dmFocActualCostVO.getHeadType();
        dmFocActualCostVO.getHeadValue();
        Assert.assertNotNull(dmFocActualCostVO);
    }
}