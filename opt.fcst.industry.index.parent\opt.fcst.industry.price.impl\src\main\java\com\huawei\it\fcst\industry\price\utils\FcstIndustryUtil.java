/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2018-2019. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.utils;

import cn.hutool.core.util.NumberUtil;
import com.huawei.it.fcst.industry.price.enums.GroupLevelEnum;
import com.huawei.it.fcst.industry.price.vo.common.CommonPriceBaseVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * FcstIndustryUtil Class
 *
 * <AUTHOR>
 * @since 2024/11/6
 */
public class FcstIndustryUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(FcstIndustryUtil.class);

    private static Map<String, String> viewOneGroupLevelMap = new HashMap<>(6);

    static {
        viewOneGroupLevelMap.put(GroupLevelEnum.LV0.getValue(), GroupLevelEnum.LV1.getValue());
        viewOneGroupLevelMap.put(GroupLevelEnum.LV1.getValue(), GroupLevelEnum.LV2.getValue());
        viewOneGroupLevelMap.put(GroupLevelEnum.LV2.getValue(), GroupLevelEnum.LV3.getValue());
        viewOneGroupLevelMap.put(GroupLevelEnum.LV3.getValue(), GroupLevelEnum.LV4.getValue());
        viewOneGroupLevelMap.put(GroupLevelEnum.LV4.getValue(), GroupLevelEnum.SPART.getValue());
    }

    /**
     * 获取下一层级的Group层级
     *
     * @return String 下一层级的Group层级
     */
    public static String getViewNextGroupLevel(CommonPriceBaseVO commonBaseVO) {
        return viewOneGroupLevelMap.get(commonBaseVO.getGroupLevel());
    }

    public static Map<String, String> getNextGroupLevel(CommonPriceBaseVO commonBaseVO) {
        Map<String, String> map = new HashMap();
        String nextGroupLevel = getViewNextGroupLevel(commonBaseVO);
        String nextGroupName = GroupLevelEnum.getInstance(nextGroupLevel).getName();
        map.put("nextGroupLevel", nextGroupLevel);
        map.put("nextGroupName", nextGroupName);
        return map;
    }

    /**
     * 获取虚化的下一层级
     *
     * @param commonBaseVO vo
     * @return map
     */
    public static Map<String, String> getBlurNextGroupLevel(CommonPriceBaseVO commonBaseVO) {
        Map<String, String> map = new HashMap();
        String nextGroupName = GroupLevelEnum.getInstance("SPART").getName();
        map.put("nextGroupLevel", "SPART");
        map.put("nextGroupName", nextGroupName);
        return map;
    }

    public static void setLvCode(CommonPriceBaseVO commonBaseVO) {
        String lvCodeStr = null;
        // 只取最后一层级重量级团队编码
        if (CollectionUtils.isNotEmpty(commonBaseVO.getLv0ProdListCodeList())) {
            commonBaseVO.setParentLevel("LV0");
            lvCodeStr = commonBaseVO.getLv0ProdListCodeList().stream().collect(Collectors.joining(","));
        }
        if (CollectionUtils.isNotEmpty(commonBaseVO.getLv1ProdListCodeList())) {
            commonBaseVO.setParentLevel("LV1");
            lvCodeStr = commonBaseVO.getLv1ProdListCodeList().stream().collect(Collectors.joining(","));
        }
        if (CollectionUtils.isNotEmpty(commonBaseVO.getLv2ProdListCodeList())) {
            commonBaseVO.setParentLevel("LV2");
            lvCodeStr = commonBaseVO.getLv2ProdListCodeList().stream().collect(Collectors.joining(","));
        }
        if (CollectionUtils.isNotEmpty(commonBaseVO.getLv3ProdListCodeList())) {
            commonBaseVO.setParentLevel("LV3");
            lvCodeStr = commonBaseVO.getLv3ProdListCodeList().stream().collect(Collectors.joining(","));
        }
        if (CollectionUtils.isNotEmpty(commonBaseVO.getLv4ProdListCodeList())) {
            commonBaseVO.setParentLevel("LV4");
            lvCodeStr = commonBaseVO.getLv4ProdListCodeList().stream().collect(Collectors.joining(","));
        }
        if (StringUtils.isNotBlank(lvCodeStr)) {
            commonBaseVO.setLvCode("'" + lvCodeStr + "'");
        }
    }

    public static void setSpecailCode(CommonPriceBaseVO commonBaseVO) {
        if ("GLOBAL".equals(commonBaseVO.getRegionCode()) && !"ALL".equals(commonBaseVO.getRepofficeCode())) {
            commonBaseVO.setRegionCode(null);
        }
    }

    public static List<String> getPeriod(String version) {
        String[] splitVersion = version.split("-");
        List<String> yearPeriodList = new ArrayList<>();
        if (splitVersion.length != 0) {
            int count = 3;
            String yearStr = splitVersion[0].substring(0, 4);
            int endYear = NumberUtil.parseInt(yearStr);
            for (int idx = count; idx > 0; idx--) {
                int startYear = endYear - idx;
                yearPeriodList.add(String.valueOf(startYear));
            }
            yearPeriodList.add(String.valueOf(endYear));
        }
        return yearPeriodList;
    }

    /**
     * 获取报告期范围内（过去2年+当年YTD，36个月）的开始和结束时间
     * 默认基期开始时间为（默认基期为（预测年份-2）年的1月份）
     * 默认基期结束时间为（预测年份的12月份或者预测年份的06月）
     *
     * @return List
     */
    public static List<Integer> getPeriodScope(String actualMonth) {
        String periodStartTime;
        String periodEndTime;
        // 0 代表空值
        if (StringUtils.isNotBlank(actualMonth) && !"0".equals(actualMonth)) {
            String yearStr = actualMonth.substring(0, 4);
            int year = Integer.parseInt(yearStr);
            periodStartTime = (year - 2) + "01";
            periodEndTime = actualMonth;
            List<Integer> periodTimeList = new ArrayList<>();
            if (null != periodStartTime) {
                periodTimeList = Arrays.asList(Integer.valueOf(periodStartTime), Integer.valueOf(periodEndTime));
            }
            return periodTimeList;
        }
        return null;
    }

    /**
     * 处理报告期36个月的开始和结束时间
     * 得到基期的开始时间和结束时间
     *
     */
    public static void handlePeriod(CommonPriceBaseVO commonBaseVO, String actualMonth) {
        if (ObjectUtils.isEmpty(commonBaseVO.getPeriodStartTime())
                && ObjectUtils.isEmpty(commonBaseVO.getPeriodEndTime())) {
            List<Integer> periodScope = getPeriodScope(actualMonth);
            if (CollectionUtils.isNotEmpty(periodScope)) {
                commonBaseVO.setPeriodStartTime(periodScope.get(0));
                commonBaseVO.setPeriodEndTime(periodScope.get(1));
            }
        }
    }

    /**
     * 处理报告期范围内（过去1年+当年YTD）24个月的开始和结束时间
     * 得到基期的开始时间和结束时间
     *
     * @param commonBaseVO 参数
     */
    public static void handlePeriodForMonthYoy(CommonPriceBaseVO commonBaseVO, String actualMonthMum) {
        String periodStartTime;
        if (StringUtils.isNotBlank(actualMonthMum) && !"0".equals(actualMonthMum)) {
            String yearStr = actualMonthMum.substring(0, 4);
            int year = Integer.valueOf(yearStr);
            periodStartTime = (year - 1) + "01";
            commonBaseVO.setPeriodStartTime(Integer.valueOf(periodStartTime));
            commonBaseVO.setPeriodEndTime(Integer.valueOf(actualMonthMum));
        }
    }

    public static String getActualMonth(String dataStr) {
        if (StringUtils.isBlank(dataStr) || dataStr.length() < 6) {
            return null;
        }
        // 定义输入时间格式
        SimpleDateFormat inputDateFormat = new SimpleDateFormat("yyyyMM");
        // 定义输出时间格式
        SimpleDateFormat outputDateFormat = new SimpleDateFormat("yyyy年MM月");
        try {
            // 将字符串时间解析为Date对象
            Date date = inputDateFormat.parse(dataStr);
            // 将Date对象格式化为指定格式的字符串时间
            return outputDateFormat.format(date);
        } catch (ParseException ex) {
            LOGGER.error(">>>FcstIndustryUtil#getActualMonth ParseException ==> {}", ex.getMessage());
        }
        return null;
    }

}
