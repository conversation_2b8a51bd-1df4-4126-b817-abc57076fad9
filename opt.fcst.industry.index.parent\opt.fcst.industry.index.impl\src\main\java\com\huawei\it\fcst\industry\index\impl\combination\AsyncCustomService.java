/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.combination;

import com.huawei.it.fcst.industry.index.dao.IDmFocCustomCombDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocEnergyCustomCombDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocEnergyMadeCustomCombDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeCustomCombDao;
import com.huawei.it.fcst.industry.index.enums.IndustryIndexEnum;
import com.huawei.it.fcst.industry.index.utils.ObjectCopyUtil;
import com.huawei.it.fcst.industry.index.vo.combination.CombTransformVO;
import com.huawei.it.fcst.industry.index.vo.combination.CombinationVO;
import com.huawei.it.fcst.industry.index.vo.combination.DmCustomCombVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Named;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * AsyncCustomService Class
 *
 * <AUTHOR>
 * @since 2023/11/3
 */
@Slf4j
@EnableAsync
@Named(value = "asyncCustomService")
public class AsyncCustomService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CustomService.class);

    private static final String SUCCESS = "SUCCESS";

    private static final String SUCCESS_STATUS = "1";

    @Autowired
    private IDmFocCustomCombDao dmFocCustomCombDao;

    @Autowired
    private IDmFocMadeCustomCombDao dmFocMadeCustomCombDao;

    @Autowired
    private IDmFocEnergyCustomCombDao dmFocEnergyCustomCombDao;

    @Autowired
    private IDmFocEnergyMadeCustomCombDao dmFocEnergyMadeCustomCombDao;

    @Async("asyncServiceExecutor")
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Future<Boolean> granularityTypePageCondition(List<DmCustomCombVO> allDmCustomCombList, CombinationVO combination, String granularityTypePageFlag) throws InterruptedException {

        if (CollectionUtils.isNotEmpty(allDmCustomCombList)) {
            CombinationVO combinationVO = ObjectCopyUtil.copy(combination, CombinationVO.class);
            Map<Long, List<DmCustomCombVO>> allDmCustomCombMap = allDmCustomCombList.stream().collect(Collectors.groupingBy(DmCustomCombVO::getCustomId));
            for (Map.Entry<Long, List<DmCustomCombVO>> allDmCustomCombEntry : allDmCustomCombMap.entrySet()) {
                List<DmCustomCombVO> dmCustomCombList = allDmCustomCombEntry.getValue();
                combinationVO.setCaliberFlag(dmCustomCombList.get(0).getCaliberFlag());
                combinationVO.setOverseaFlag(dmCustomCombList.get(0).getOverseaFlag());
                combinationVO.setViewFlag(dmCustomCombList.get(0).getViewFlag());
                combinationVO.setLv0ProdListCode(dmCustomCombList.get(0).getLv0ProdListCode());
                combinationVO.setCustomId(dmCustomCombList.get(0).getCustomId());
                combinationVO.setPageFlag(dmCustomCombList.get(0).getPageFlag());
                Map<String, List<DmCustomCombVO>> groupLevelKey = dmCustomCombList.stream().collect(Collectors.groupingBy(DmCustomCombVO::getGroupLevel));

                List<DmCustomCombVO> allGroupCodeList = new ArrayList<>();
                for (Map.Entry<String, List<DmCustomCombVO>> groupLevelEntry : groupLevelKey.entrySet()) {
                    String groupLevel = groupLevelEntry.getKey();
                    List<DmCustomCombVO> dmCustomCombVOList = groupLevelEntry.getValue();
                    combinationVO.setGroupLevel(groupLevel);
                    setPermissionGroupCode(dmCustomCombVOList, combinationVO);
                    if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(combinationVO.getCostType())) {
                        getManufacutreViewInfoList(granularityTypePageFlag, allGroupCodeList, combinationVO);
                    } else {
                        getViewInfoList(granularityTypePageFlag, allGroupCodeList, combinationVO);
                    }
                }
                // 如果组合的某些code不在维表中，需要设置失效
                List<DmCustomCombVO> diffGroupCodeList = dmCustomCombList.stream().filter(custom -> !allGroupCodeList.stream().map(all -> {
                    return all.getConnectCode();
                }).collect(Collectors.toList()).contains(custom.getConnectCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(diffGroupCodeList)) {
                    // 更新custom_id整个失效
                    dmFocCustomCombDao.updateCustomEnableByCustomId(combinationVO);
                    // 更新具体某个code失效
                    // 更新单个sub_enable_flag
                    if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(combinationVO.getCostType())) {
                        dmFocCustomCombDao.updateManufacutreCustomSubEnableList(combinationVO,diffGroupCodeList);
                    } else {
                        dmFocCustomCombDao.updateCustomSubEnableList(combinationVO, diffGroupCodeList);
                    }
                }
            }
        }
        return new AsyncResult<>(Boolean.TRUE);
    }

    private void setPermissionGroupCode(List<DmCustomCombVO> dmCustomCombVOList, CombinationVO combinationVO) {
        List<String> lv1CodeList = dmCustomCombVOList.stream().map(DmCustomCombVO::getLv1ProdRndTeamCode).distinct().collect(Collectors.toList());
        lv1CodeList.remove(null);
        combinationVO.setLv1CodeList(lv1CodeList);
        List<String> lv2CodeList = dmCustomCombVOList.stream().map(DmCustomCombVO::getLv2ProdRndTeamCode).distinct().collect(Collectors.toList());
        lv2CodeList.remove(null);
        combinationVO.setLv2CodeList(lv2CodeList);
        List<String> lv3CodeList = dmCustomCombVOList.stream().map(DmCustomCombVO::getLv3ProdRndTeamCode).distinct().collect(Collectors.toList());
        lv3CodeList.remove(null);
        combinationVO.setLv3CodeList(lv3CodeList);

        List<String> l1NameList = dmCustomCombVOList.stream().map(DmCustomCombVO::getL1Name).distinct().collect(Collectors.toList());
        l1NameList.remove(null);
        combinationVO.setL1NameList(l1NameList);
        List<String> l2NameList = dmCustomCombVOList.stream().map(DmCustomCombVO::getL2Name).distinct().collect(Collectors.toList());
        l2NameList.remove(null);
        combinationVO.setL2NameList(l2NameList);

        List<String> coaCodeList = dmCustomCombVOList.stream().map(DmCustomCombVO::getCoaCode).distinct().collect(Collectors.toList());
        coaCodeList.remove(null);
        combinationVO.setCoaCodeList(coaCodeList);

        List<String> dimensionCodeList = dmCustomCombVOList.stream().map(DmCustomCombVO::getDimensionCode).distinct().collect(Collectors.toList());
        dimensionCodeList.remove(null);
        combinationVO.setDimensionCodeList(dimensionCodeList);
        List<String> dimensionSubCategoryCodeList = dmCustomCombVOList.stream().map(DmCustomCombVO::getDimensionSubCategoryCode).distinct().collect(Collectors.toList());
        dimensionSubCategoryCodeList.remove(null);
        combinationVO.setDimensionSubcategoryCodeList(dimensionSubCategoryCodeList);
        List<String> dimensionSubDetailCodeList = dmCustomCombVOList.stream().map(DmCustomCombVO::getDimensionSubDetailCode).distinct().collect(Collectors.toList());
        dimensionSubDetailCodeList.remove(null);
        combinationVO.setDimensionSubDetailCodeList(dimensionSubDetailCodeList);
        List<String> spartCodeList = dmCustomCombVOList.stream().map(DmCustomCombVO::getSpartCode).distinct().collect(Collectors.toList());
        spartCodeList.remove(null);
        combinationVO.setSpartCodeList(spartCodeList);
    }

    private void getViewInfoList(String granularityTypePageFlag, List<DmCustomCombVO> allGroupCodeList, CombinationVO combinationVO) {
        switch (granularityTypePageFlag) {
            case "U_ANNUAL" :
                List<DmCustomCombVO> groupCodeForGeneralAnnualList = dmFocCustomCombDao.groupCodeForGeneralAnnualList(combinationVO);
                allGroupCodeList.addAll(groupCodeForGeneralAnnualList);
                break;
            case "U_MONTH" :
                List<DmCustomCombVO> groupCodeForGeneralMonthList = dmFocCustomCombDao.groupCodeForGeneralMonthList(combinationVO);
                allGroupCodeList.addAll(groupCodeForGeneralMonthList);
                break;
            case "P_ANNUAL" :
                List<DmCustomCombVO> groupCodeForProfitAnnualList = dmFocCustomCombDao.groupCodeForProfitAnnualList(combinationVO);
                allGroupCodeList.addAll(groupCodeForProfitAnnualList);
                break;
            case "P_MONTH" :
                List<DmCustomCombVO> groupCodeForProfitMonthList = dmFocCustomCombDao.groupCodeForProfitMonthList(combinationVO);
                allGroupCodeList.addAll(groupCodeForProfitMonthList);
                break;
            case "D_ANNUAL" :
                if ("ICT".equals(combinationVO.getIndustryOrg())) {
                    List<DmCustomCombVO> groupCodeForDimensionAnnualList = dmFocCustomCombDao.groupCodeForDimensionAnnualList(combinationVO);
                    allGroupCodeList.addAll(groupCodeForDimensionAnnualList);
                } else {
                    // 数字能源与IAS
                    List<DmCustomCombVO> groupCodeForDimensionAnnualList = dmFocEnergyCustomCombDao.groupCodeForEnergyDimensionAnnualList(combinationVO);
                    allGroupCodeList.addAll(groupCodeForDimensionAnnualList);
                }
                break;
            case "D_MONTH" :
                if ("ICT".equals(combinationVO.getIndustryOrg())) {
                    List<DmCustomCombVO> groupCodeForDimensionMonthList = dmFocCustomCombDao.groupCodeForDimensionMonthList(combinationVO);
                    allGroupCodeList.addAll(groupCodeForDimensionMonthList);
                } else {
                    // 数字能源与IAS
                    List<DmCustomCombVO> groupCodeForDimensionMonthList = dmFocEnergyCustomCombDao.groupCodeForEnergyDimensionMonthList(combinationVO);
                    allGroupCodeList.addAll(groupCodeForDimensionMonthList);
                }
                break;
            default:
                break;
        }
    }

    private void getManufacutreViewInfoList(String granularityTypePageFlag, List<DmCustomCombVO> allGroupCodeList, CombinationVO combinationVO) {
        switch (granularityTypePageFlag) {
            case "U_ANNUAL" :
                List<DmCustomCombVO> groupCodeForGeneralAnnualList = dmFocMadeCustomCombDao.groupCodeForManuGeneralAnnualList(combinationVO);
                allGroupCodeList.addAll(groupCodeForGeneralAnnualList);
                break;
            case "U_MONTH" :
                List<DmCustomCombVO> groupCodeForGeneralMonthList = dmFocMadeCustomCombDao.groupCodeForManuGeneralMonthList(combinationVO);
                allGroupCodeList.addAll(groupCodeForGeneralMonthList);
                break;
            case "P_ANNUAL" :
                List<DmCustomCombVO> groupCodeForProfitAnnualList = dmFocMadeCustomCombDao.groupCodeForManuProfitAnnualList(combinationVO);
                allGroupCodeList.addAll(groupCodeForProfitAnnualList);
                break;
            case "P_MONTH" :
                List<DmCustomCombVO> groupCodeForProfitMonthList = dmFocMadeCustomCombDao.groupCodeForManuProfitMonthList(combinationVO);
                allGroupCodeList.addAll(groupCodeForProfitMonthList);
                break;
            case "D_ANNUAL" :
                if ("ICT".equals(combinationVO.getIndustryOrg())) {
                    List<DmCustomCombVO> groupCodeForDimensionAnnualList = dmFocMadeCustomCombDao.groupCodeForManuDimensionAnnualList(combinationVO);
                    allGroupCodeList.addAll(groupCodeForDimensionAnnualList);
                } else {
                    // 数字能源和IAS
                    List<DmCustomCombVO> groupCodeForDimensionAnnualList = dmFocEnergyMadeCustomCombDao.groupCodeForEnergyManuDimensionAnnualList(combinationVO);
                    allGroupCodeList.addAll(groupCodeForDimensionAnnualList);
                }
                break;
            case "D_MONTH" :
                if ("ICT".equals(combinationVO.getIndustryOrg())) {
                    List<DmCustomCombVO> groupCodeForDimensionMonthList = dmFocMadeCustomCombDao.groupCodeForManuDimensionMonthList(combinationVO);
                    allGroupCodeList.addAll(groupCodeForDimensionMonthList);
                } else {
                    // 数字能源和IAS
                    List<DmCustomCombVO> groupCodeForDimensionMonthList = dmFocEnergyMadeCustomCombDao.groupCodeForEnergyManuDimensionMonthList(combinationVO);
                    allGroupCodeList.addAll(groupCodeForDimensionMonthList);
                }
                break;
            default:
                break;
        }
    }

    @Async("asyncServiceExecutor")
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Future<Boolean> callCustomAnnual(CombTransformVO combTransformVO) throws CommonApplicationException {
        String cusViewAnnualFlag = dmFocCustomCombDao.cusViewAnnualCost(combTransformVO);
        LOGGER.info("调用f_dm_foc_cus_view_annl_cost_t函数:{}", cusViewAnnualFlag);
        if (!SUCCESS.equals(cusViewAnnualFlag) && !SUCCESS_STATUS.equals(cusViewAnnualFlag)) {
            throw new CommonApplicationException("调用f_dm_foc_cus_view_annl_cost_t函数失败");
        }
        String customAnnualFlag = dmFocCustomCombDao.customAnnual(combTransformVO);
        LOGGER.info("调用f_dm_foc_custom_annual函数:{}", customAnnualFlag);
        if (!SUCCESS.equals(customAnnualFlag) && !SUCCESS_STATUS.equals(customAnnualFlag)) {
            throw new CommonApplicationException("调用f_dm_foc_custom_annual函数失败");
        }
        return new AsyncResult<>(Boolean.TRUE);
    }

    @Async("asyncServiceExecutor")
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Future<Boolean> callCustomMonth(CombTransformVO combTransformVO) throws CommonApplicationException {
        String cusItemDtlFlag = dmFocCustomCombDao.cusItemDtlDecode(combTransformVO);
        LOGGER.info("调用f_dm_foc_cus_item_dtl_decode函数:{}", cusItemDtlFlag);
        if (!SUCCESS.equals(cusItemDtlFlag) && !SUCCESS_STATUS.equals(cusItemDtlFlag)) {
            throw new CommonApplicationException("调用f_dm_foc_cus_item_dtl_decode函数失败");
        }
        String cusItemAppendFlag = dmFocCustomCombDao.cusItemAppend(combTransformVO);
        LOGGER.info("调用f_dm_foc_cus_item_append函数:{}", cusItemAppendFlag);
        if (!SUCCESS.equals(cusItemAppendFlag) && !SUCCESS_STATUS.equals(cusItemAppendFlag)) {
            throw new CommonApplicationException("调用f_dm_foc_cus_item_append函数失败");
        }
        String customMonthCombFlag = dmFocCustomCombDao.customMonthComb(combTransformVO);
        LOGGER.info("调用F_DM_FOC_MONTH_CUSTOM_COMB函数:{}", customMonthCombFlag);
        if (!SUCCESS.equals(customMonthCombFlag) && !SUCCESS_STATUS.equals(customMonthCombFlag)) {
            throw new CommonApplicationException("调用F_DM_FOC_MONTH_CUSTOM_COMB函数失败");
        }
        return new AsyncResult<>(Boolean.TRUE);
    }

    @Async("asyncServiceExecutor")
    public Future<Boolean> callMadeCustomAnnual(CombTransformVO combTransformVO) throws CommonApplicationException {
        String cusMadeViewAnnualFlag = dmFocMadeCustomCombDao.cusMadeViewAnnualCost(combTransformVO);
        LOGGER.info("调用f_dm_foc_made_cus_view_annl_cost_t函数:{}", cusMadeViewAnnualFlag);
        if (!SUCCESS.equals(cusMadeViewAnnualFlag) && !SUCCESS_STATUS.equals(cusMadeViewAnnualFlag)) {
            throw new CommonApplicationException("调用f_dm_foc_made_cus_view_annl_cost_t函数失败");
        }
        String customMadeAnnualFlag = dmFocMadeCustomCombDao.customMadeAnnual(combTransformVO);
        LOGGER.info("调用f_dm_foc_made_custom_annual函数:{}", customMadeAnnualFlag);
        if (!SUCCESS.equals(customMadeAnnualFlag) && !SUCCESS_STATUS.equals(customMadeAnnualFlag)) {
            throw new CommonApplicationException("调用f_dm_foc_made_custom_annual函数失败");
        }
        return new AsyncResult<>(Boolean.TRUE);
    }

    @Async("asyncServiceExecutor")
    public Future<Boolean> callMadeCustomMonth(CombTransformVO combTransformVO) throws CommonApplicationException {
        String cusMadeItemDtlFlag = dmFocMadeCustomCombDao.cusMadeItemDtlDecode(combTransformVO);
        LOGGER.info("调用f_dm_foc_made_cus_item_dtl_decode函数:{}", cusMadeItemDtlFlag);
        if (!SUCCESS.equals(cusMadeItemDtlFlag) && !SUCCESS_STATUS.equals(cusMadeItemDtlFlag)) {
            throw new CommonApplicationException("调用f_dm_foc_made_cus_item_dtl_decode函数失败");
        }
        String cusMadeItemAppendFlag = dmFocMadeCustomCombDao.cusMadeItemAppend(combTransformVO);
        LOGGER.info("调用f_dm_foc_made_cus_item_append函数:{}", cusMadeItemAppendFlag);
        if (!SUCCESS.equals(cusMadeItemAppendFlag) && !SUCCESS_STATUS.equals(cusMadeItemAppendFlag)) {
            throw new CommonApplicationException("调用f_dm_foc_made_cus_item_append函数失败");
        }
        String customMadeMonthCombFlag = dmFocMadeCustomCombDao.customMadeMonthComb(combTransformVO);
        LOGGER.info("调用F_DM_FOC_MADE_MONTH_CUSTOM_COMB函数:{}", customMadeMonthCombFlag);
        if (!SUCCESS.equals(customMadeMonthCombFlag) && !SUCCESS_STATUS.equals(customMadeMonthCombFlag)) {
            throw new CommonApplicationException("调用F_DM_FOC_MADE_MONTH_CUSTOM_COMB函数失败");
        }
        return new AsyncResult<>(Boolean.TRUE);
    }

}
