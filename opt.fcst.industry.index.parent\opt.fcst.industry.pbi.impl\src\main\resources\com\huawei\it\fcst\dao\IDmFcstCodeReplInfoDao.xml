<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.pbi.dao.IDmFcstCodeReplInfoDao">
    <resultMap type="com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstDimInfoVO" id="resultMap">
        <result property="id" column="id"/>
        <result property="bgCode" column="bg_code"/>
        <result property="bgCnName" column="bg_cn_name"/>
        <result property="dimensionCode" column="dimension_code"/>
        <result property="dimensionCnName" column="dimension_cn_name"/>
        <result property="dimensionSubCategoryCode" column="dimension_subCategory_code"/>
        <result property="dimensionSubCategoryCnName" column="dimension_subCategory_cn_name"/>
        <result property="dimensionSubDetailCode" column="dimension_sub_detail_code"/>
        <result property="dimensionSubDetailCnName" column="dimension_sub_detail_cn_name"/>
        <result property="newSpartCode" column="new_spart_code"/>
        <result property="oldSpartCode" column="old_spart_code"/>
        <result property="regionCode" column="region_code"/>
        <result property="regionCnName" column="region_cn_name"/>
        <result property="repofficeCode" column="repoffice_code"/>
        <result property="repofficeCnName" column="repoffice_cn_name"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="groupCode" column="groupCode"/>
        <result property="groupLevel" column="groupLevel"/>
        <result property="groupCnName" column="groupCnName"/>
        <result property="groupLevel" column="group_level"/>
        <result property="relationType" column="relation_type"/>
        <result property="associatedFlag" column="associatedFlag"/>
        <result property="replaceRelationType" column="replace_relation_type"/>
        <result property="replaceRelationName" column="replace_relation_name"/>
        <result property="lv4Code" column="lv4_code"/>
        <result property="lv4Num" column="lv4Num"/>
    </resultMap>

    <select id="getCodeReplInfoList"  resultMap="resultMap">
        select
        <choose>
            <when test='nextGroupLevel == "REPLACE_TYPE"'>
                DISTINCT new_spart_code,old_spart_code,replace_relation_type AS groupCode, replace_relation_type AS groupCnName, 'REPLACE_TYPE' AS groupLevel
            </when>
            <when test='nextGroupLevel == "REPLACE_NAME"'>
                DISTINCT new_spart_code,old_spart_code,replace_relation_name AS groupCode, replace_relation_name AS groupCnName, 'REPLACE_NAME' AS groupLevel
            </when>
            <when test='nextGroupLevel == "RELATION_TYPE"'>
                DISTINCT new_spart_code,old_spart_code,relation_type AS groupCode, relation_type AS groupCnName, 'RELATION_TYPE' AS groupLevel
            </when>
            <when test='nextGroupLevel == "NEW_SPART"'>
                DISTINCT new_spart_code AS groupCode, new_spart_desc AS groupCnName, 'NEW_SPART' AS groupLevel
            </when>
            <when test='nextGroupLevel == "OLD_SPART"'>
                DISTINCT old_spart_code AS groupCode, old_spart_desc AS groupCnName, 'OLD_SPART' AS groupLevel
            </when>
            <when test='nextGroupLevel == "LV4"'>
                DISTINCT lv4_code AS groupCode, lv4_cn_name AS groupCnName, 'LV4' AS groupLevel
            </when>
            <when test='nextGroupLevel == "LV3"'>
                DISTINCT lv3_code AS groupCode, lv3_cn_name AS groupCnName, 'LV3' AS groupLevel
            </when>
            <when test='nextGroupLevel == "LV2"'>
                DISTINCT lv2_code AS groupCode, lv2_cn_name AS groupCnName,'LV2' AS groupLevel
            </when>
            <when test='nextGroupLevel == "LV1"'>
                DISTINCT lv1_code AS groupCode, lv1_cn_name AS groupCnName,'LV1' AS groupLevel
            </when>
            <otherwise>
                new_spart_code,old_spart_code,replace_relation_name AS groupCode, replace_relation_name AS groupCnName, 'REPLACE_NAME' AS groupLevel
            </otherwise>
        </choose>
        <if test='costType == "PSP"'>
            from fin_dm_opt_foi.dm_fcst_ict_psp_code_repl_info_dim_t
        </if>
        <if test='costType == "STD"'>
            from fin_dm_opt_foi.dm_fcst_ict_std_code_repl_info_dim_t
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            del_flag ='N'
           and replace_relation_name !='SNULL'
           and gts_type = 'PROD'
            <if test='costType == "PSP" and softwareMark!=null and softwareMark!=""'>
                and software_mark = #{softwareMark,jdbcType=VARCHAR}
            </if>
            <if test='bgCode!=null and bgCode!=""'>
                and bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='overseaFlag!=null and overseaFlag!=""'>
                and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='regionCode!=null and regionCode!=""'>
                and region_code = #{regionCode,jdbcType=VARCHAR}
            </if>
            <if test='repofficeCode!=null and repofficeCode!=""'>
                and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='lv1ProdRndTeamCode!=null and lv1ProdRndTeamCode!=""'>
                and lv1_code = #{lv1ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv2ProdRndTeamCode!=null and lv2ProdRndTeamCode!=""'>
                and lv2_code = #{lv2ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv3ProdRndTeamCode!=null and lv3ProdRndTeamCode!=""'>
                and lv3_code = #{lv3ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv4ProdRndTeamCode!=null and lv4ProdRndTeamCode!=""'>
                and lv4_code = #{lv4ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='replaceRelationName!=null and replaceRelationName!=""'>
                and replace_relation_name = #{replaceRelationName,jdbcType=VARCHAR}
            </if>
            <if test='replaceRelationType!=null and replaceRelationType!=""'>
                and replace_relation_type = #{replaceRelationType,jdbcType=VARCHAR}
            </if>
            <if test='relationType != null and relationType!=""'>
                and relation_type = #{relationType,jdbcType=VARCHAR}
            </if>
            <if test='oldSpartCode!=null and oldSpartCode!=""'>
                and old_spart_code = #{oldSpartCode,jdbcType=VARCHAR}
            </if>
            <if test='newSpartCode!=null and newSpartCode!=""'>
                and new_spart_code = #{newSpartCode,jdbcType=VARCHAR}
            </if>
        </trim>
    </select>

    <select id="getCoceReplLv4Num"  resultType="java.lang.Integer">
        select
        <choose>
            <when test='groupLevel == "REPLACE_NAME"'>
                count(lv4_code) AS lv4Num
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='costType == "PSP"'>
            from fin_dm_opt_foi.dm_fcst_ict_psp_code_repl_info_dim_t
        </if>
        <if test='costType == "STD"'>
            from fin_dm_opt_foi.dm_fcst_ict_std_code_repl_info_dim_t
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            del_flag ='N'
            and replace_relation_name !='SNULL'
            and gts_type = 'PROD'
            <if test='costType == "PSP" and softwareMark!=null and softwareMark!=""'>
                and software_mark = #{softwareMark,jdbcType=VARCHAR}
            </if>
            <if test='bgCode!=null and bgCode!=""'>
                and bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='overseaFlag!=null and overseaFlag!=""'>
                and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='regionCode!=null and regionCode!=""'>
                and region_code = #{regionCode,jdbcType=VARCHAR}
            </if>
            <if test='repofficeCode!=null and repofficeCode!=""'>
                and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='lv1ProdRndTeamCode!=null and lv1ProdRndTeamCode!=""'>
                and lv1_code = #{lv1ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv2ProdRndTeamCode!=null and lv2ProdRndTeamCode!=""'>
                and lv2_code = #{lv2ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv3ProdRndTeamCode!=null and lv3ProdRndTeamCode!=""'>
                and lv3_code = #{lv3ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv4ProdRndTeamCode!=null and lv4ProdRndTeamCode!=""'>
                and lv4_code = #{lv4ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='replaceRelationName !=null and replaceRelationName !=""'>
                and replace_relation_name = #{replaceRelationName,jdbcType=VARCHAR}
            </if>
            <if test='replaceRelationType !=null and replaceRelationType !=""'>
                and replace_relation_type = #{replaceRelationType,jdbcType=VARCHAR}
            </if>
            <if test='relationType != null and relationType!=""'>
                and relation_type = #{relationType,jdbcType=VARCHAR}
            </if>
            <if test='oldSpartCode!=null and oldSpartCode!=""'>
                and old_spart_code = #{oldSpartCode,jdbcType=VARCHAR}
            </if>
            <if test='newSpartCode!=null and newSpartCode!=""'>
                and new_spart_code = #{newSpartCode,jdbcType=VARCHAR}
            </if>
        </trim>
    </select>

    <select id="getCoceReplLv4Code"  resultMap="resultMap">
        select lv4_code
        <if test='costType == "PSP"'>
            from fin_dm_opt_foi.dm_fcst_ict_psp_code_repl_info_dim_t
        </if>
        <if test='costType == "STD"'>
            from fin_dm_opt_foi.dm_fcst_ict_std_code_repl_info_dim_t
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            del_flag ='N'
            and replace_relation_name !='SNULL'
            and gts_type = 'PROD'
            <if test='costType == "PSP" and softwareMark!=null and softwareMark!=""'>
                and software_mark = #{softwareMark,jdbcType=VARCHAR}
            </if>
            <if test='bgCode!=null and bgCode!=""'>
                and bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='overseaFlag!=null and overseaFlag!=""'>
                and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='regionCode!=null and regionCode!=""'>
                and region_code = #{regionCode,jdbcType=VARCHAR}
            </if>
            <if test='repofficeCode!=null and repofficeCode!=""'>
                and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='lv1ProdRndTeamCode!=null and lv1ProdRndTeamCode!=""'>
                and lv1_code = #{lv1ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv2ProdRndTeamCode!=null and lv2ProdRndTeamCode!=""'>
                and lv2_code = #{lv2ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv3ProdRndTeamCode!=null and lv3ProdRndTeamCode!=""'>
                and lv3_code = #{lv3ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv4ProdRndTeamCode!=null and lv4ProdRndTeamCode!=""'>
                and lv4_code = #{lv4ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='replaceRelationName !=null and replaceRelationName !=""'>
                and replace_relation_name = #{replaceRelationName,jdbcType=VARCHAR}
            </if>
            <if test='replaceRelationType !=null and replaceRelationType !=""'>
                and replace_relation_type = #{replaceRelationType,jdbcType=VARCHAR}
            </if>
            <if test='relationType != null and relationType!=""'>
                and relation_type = #{relationType,jdbcType=VARCHAR}
            </if>
            <if test='oldSpartCode!=null and oldSpartCode!=""'>
                and old_spart_code = #{oldSpartCode,jdbcType=VARCHAR}
            </if>
            <if test='newSpartCode!=null and newSpartCode!=""'>
                and new_spart_code = #{newSpartCode,jdbcType=VARCHAR}
            </if>
        </trim>
    </select>

    <select id="getReplaceNameNum"  resultMap="resultMap">
        select replace_relation_name
        <if test='costType == "PSP"'>
            from fin_dm_opt_foi.dm_fcst_ict_psp_code_repl_info_dim_t
        </if>
        <if test='costType == "STD"'>
            from fin_dm_opt_foi.dm_fcst_ict_std_code_repl_info_dim_t
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            del_flag ='N'
            and replace_relation_name !='SNULL'
            and gts_type = 'PROD'
            <if test='costType == "PSP" and softwareMark!=null and softwareMark!=""'>
                and software_mark = #{softwareMark,jdbcType=VARCHAR}
            </if>
            <if test='bgCode!=null and bgCode!=""'>
                and bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='overseaFlag!=null and overseaFlag!=""'>
                and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='regionCode!=null and regionCode!=""'>
                and region_code = #{regionCode,jdbcType=VARCHAR}
            </if>
            <if test='repofficeCode!=null and repofficeCode!=""'>
                and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='replaceRelationName !=null and replaceRelationName !=""'>
                and replace_relation_name = #{replaceRelationName,jdbcType=VARCHAR}
            </if>
            <if test='replaceRelationType !=null and replaceRelationType !=""'>
                and replace_relation_type = #{replaceRelationType,jdbcType=VARCHAR}
            </if>
            <if test='relationType != null and relationType!=""'>
                and relation_type = #{relationType,jdbcType=VARCHAR}
            </if>
        </trim>
    </select>
</mapper>