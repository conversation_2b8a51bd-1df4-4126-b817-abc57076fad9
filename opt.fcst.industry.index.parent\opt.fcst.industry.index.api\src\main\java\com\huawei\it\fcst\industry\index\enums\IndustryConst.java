/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2023/3/13
 */
public interface IndustryConst {
    enum INDUSTRY_ORG_PAGE {
        ICT_MODEL ("成本指数-产业-ICT-","成本指数-产业-ICT-"),
        ENERGY_MODEL("成本指数-产业-数字能源-","成本指数-产业-数字能源-"),
        IAS_MODEL("成本指数-产业-IAS-","成本指数-产业-IAS-");
        @Getter
        @Setter
        private String value;
        @Getter
        @Setter
        private String desc;
        INDUSTRY_ORG_PAGE(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }

        public static String getValue(String industryOrg) {
            String val;
            switch (industryOrg) {
                case "ICT":
                    val = IndustryConst.INDUSTRY_ORG_PAGE.ICT_MODEL.getValue();
                    break;
                case "IAS":
                    val = IndustryConst.INDUSTRY_ORG_PAGE.IAS_MODEL.getValue();
                    break;
                case "ENERGY":
                    val = IndustryConst.INDUSTRY_ORG_PAGE.ENERGY_MODEL.getValue();
                    break;
                default:
                    val = "";
            }
            return val;
        }
    }

    enum INDUSTRY_ORG {
        ICT ("ICT","ICT产业"),
        ICT_NEW ("ICT_NEW","ICT产业"),
        ICT_PRICE("ICT_PRICE","ICT定价"),
        ENERGY("ENERGY","数字能源产业"),
        IAS("IAS","IAS");
        @Getter
        @Setter
        private String value;
        @Getter
        @Setter
        private String desc;
        INDUSTRY_ORG(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }
    }
    enum INDUSTRY_ORG_FUNC {
        I ("I","ICT产业"),
        E("E","数字能源产业"),
        IAS("IAS","IAS产业");
        @Getter
        @Setter
        private String value;
        @Getter
        @Setter
        private String desc;
        INDUSTRY_ORG_FUNC(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }
    }
    enum TABLE_NAME {
        ICT_TABLE ("dm_foc","dm_foc"),
        ICT_NEW_TABLE ("dm_foc_fcst_ict","dm_foc_fcst_ict"),
        ICT_PRICE_TABLE ("dm_fcst_price","dm_fcst_price"),
        ENERGY_TABLE("dm_foc_energy","dm_foc_energy"),
        IAS_TABLE("dm_foc_ias","dm_foc_ias");
        @Getter
        @Setter
        private String value;
        @Getter
        @Setter
        private String desc;
        TABLE_NAME(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }

    }
    enum OVERSEA_FLAG {
        G("G","全球"),
        I("I","国内"),
        O("O","海外");
        @Getter
        @Setter
        private String value;
        @Getter
        @Setter
        private String desc;
        OVERSEA_FLAG(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    enum PAGE_FLAG {
        ANNUAL("ANNUAL","年度页面"),
        MONTH("MONTH","月度"),
        CONFIG("CONFIG","配置管理");
        @Getter
        @Setter
        private String value;
        private String desc;
        PAGE_FLAG(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }
    }


    enum STATUS {
        IS_STATUS(1L,"已刷新"),
        NOT_STATUS(0L,"未刷新");
        @Getter
        @Setter
        private Long value;
        private String desc;
        STATUS(Long value,String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    enum TopFlag {
        IS_YES_EN("Y","是top品类,规格品"),
        IS_NOT_EN("N","不是top品类,不是规格品"),
        IS_YES_CN("是","是top品类,规格品"),
        IS_NOT_CN("否","不是top品类,不是规格品");
        @Getter
        @Setter
        private String value;
        private String desc;
        TopFlag(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    enum VersionType {
        AUTO("AUTO"),
        ADJUST("ADJUST"),
        FINAL("FINAL");
        @Getter
        @Setter
        private String value;
        VersionType(String value) {
            this.value = value;
        }
    }

    enum OtherCeg {
        NAME("其他"),
        CODE("OTHER");
        @Getter
        @Setter
        private String value;
        OtherCeg(String value) {
            this.value = value;
        }
    }

    enum DataType {
        CATE("CATEGORY","品类"),
        ITEM("ITEM","item"),
        DIM("DIMENSION","维度"),
        DIM_MADE("DIMENSION_MADE","制造维度"),
        REVIEW_PURC_C("REVIEW_PURC_C","采购成本-底层数据审视-发货"),
        REVIEW_MADE_R("REVIEW_MADE_R","制造成本-底层数据审视-收入"),
        REVIEW_PURC_R("REVIEW_PURC_R","采购成本-底层数据审视-收入"),
        REVIEW_MADE_C("REVIEW_MADE_C","制造成本-底层数据审视-发货");
        @Getter
        @Setter
        private String value;
        private String desc;
        DataType(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    enum DataReview {
        MODIFY("M","ITEM异常数据录入"),
        RECORD("R","历史修改记录");
        @Getter
        @Setter
        private String value;
        private String desc;
        DataReview(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }
    }


    enum TaskStatus {
        TASK_FAIL("FAILED","失败"),
        TASK_SUCCESS("SUCCESS","成功"),
        TASK_PROCESSING("PROCESSING","进行中");
        @Getter
        @Setter
        private String value;
        private String desc;
        TaskStatus(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    static IndustryConst.OVERSEA_FLAG getOverseaFlag(String key){
        for (IndustryConst.OVERSEA_FLAG overSea : IndustryConst.OVERSEA_FLAG.values()) {
            if (overSea.getValue().equalsIgnoreCase(key)) {
                return overSea;
            }
        }
        return null;
    }

    static IndustryConst.INDUSTRY_ORG getIndustryOrgName(String key){
        for (IndustryConst.INDUSTRY_ORG industryOrg : IndustryConst.INDUSTRY_ORG.values()) {
            if (industryOrg.getValue().equalsIgnoreCase(key)) {
                return industryOrg;
            }
        }
        return null;
    }
}
