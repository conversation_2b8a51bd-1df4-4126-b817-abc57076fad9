/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.combination;

import com.huawei.it.fcst.industry.index.constant.Constant;
import com.huawei.it.fcst.industry.index.dao.IDmFocCustomCombDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocCustomCombTempDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocDataRefreshStatusDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocEnergyCustomCombTempDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocIasCustomCombDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocIasCustomCombTempDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeCustomCombDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocVersionDao;
import com.huawei.it.fcst.industry.index.enums.GroupLevelAllEnum;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumU;
import com.huawei.it.fcst.industry.index.enums.IndustryConst;
import com.huawei.it.fcst.industry.index.enums.IndustryIndexEnum;
import com.huawei.it.fcst.industry.index.impl.annual.AnnualCommonService;
import com.huawei.it.fcst.industry.index.impl.common.CommonService;
import com.huawei.it.fcst.industry.index.impl.iauth.DataDimensionService;
import com.huawei.it.fcst.industry.index.service.combination.ICustomCombService;
import com.huawei.it.fcst.config.ConfigUtil;
import com.huawei.it.fcst.industry.index.utils.FcstIndexMadeUtil;
import com.huawei.it.fcst.industry.index.utils.FcstIndexUtil;
import com.huawei.it.fcst.industry.index.utils.ObjectCopyUtil;
import com.huawei.it.fcst.industry.index.utils.UserInfoUtils;
import com.huawei.it.fcst.industry.index.vo.combination.CombTransformVO;
import com.huawei.it.fcst.industry.index.vo.combination.CombinationVO;
import com.huawei.it.fcst.industry.index.vo.combination.DmCustomCombVO;
import com.huawei.it.fcst.industry.index.vo.combination.DmFocDataRefreshStatus;
import com.huawei.it.fcst.industry.index.vo.common.CommonViewVO;
import com.huawei.it.fcst.industry.index.vo.common.DataPermissionsVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.common.TableNameVO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoDTO;
import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;
import com.huawei.it.fcst.industry.index.vo.view.ViewInfoVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.annotation.NoJalorTransation;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.security.RoleVO;
import com.huawei.it.jalor5.security.UserVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Named;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * CustomCombService Class
 *
 * <AUTHOR>
 * @since 2023/8/1
 */
@Named("customCombService")
@JalorResource(code = "customCombService", desc = "自定义汇总组合")
public class CustomCombService implements ICustomCombService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CustomCombService.class);

    private static Map<String, String> allPageFlag = new HashMap<>(4);

    static {
        allPageFlag.put("ANNUAL", "MONTH");
        allPageFlag.put("MONTH", "ANNUAL");
        allPageFlag.put("ALL_ANNUAL", "ALL_MONTH");
        allPageFlag.put("ALL_MONTH", "ALL_ANNUAL");
    }

    private static final String PAGE_FLAG_ALL = "ALL";

    private static final String INIT = "TASK_INIT";

    private static final String IS_NOT = "N";

    private static final String ENABLE_FLAG_Y = "Y";

    private static final String ENABLE_FLAG_N = "N";

    private static final String INIT_COMB = "INIT_COMB";

    private static final String COMB = "COMB";

    @Autowired
    private IDmFocCustomCombDao dmFocCustomCombDao;

    @Autowired
    private IDmFocCustomCombTempDao dmFocCustomCombTempDao;

    @Autowired
    private IDmFocEnergyCustomCombTempDao dmFocEnergyCustomCombTempDao;

    @Autowired
    private IDmFocIasCustomCombTempDao dmFocIasCustomCombTempDao;

    @Autowired
    private IDmFocMadeCustomCombDao dmFocMadeCustomCombDao;

    @Autowired
    private IDmFocIasCustomCombDao dmFocIasCustomCombDao;

    @Autowired
    private CommonService commonService;

    @Autowired
    private CustomCommonService customCommonService;

    @Autowired
    private AsyncService asyncService;

    @Autowired
    private IDmFocVersionDao dmFocVersionDao;

    @Autowired
    private IDmFocDataRefreshStatusDao dataRefreshStatusDao;

    @Autowired
    private DataDimensionService dataDimensionService;

    @Autowired
    private AnnualCommonService annualCommonService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @JalorOperation(code = "createCombination", desc = "新增汇总组合")
    @Audit(module = "customCombService-createCombination", operation = "createCombination",
            message = "新增汇总组合")
    public ResultDataVO createCombination(CombinationVO combinationVO) throws CommonApplicationException {
        Long userId = UserInfoUtils.getUserId();
        String userIdStr = String.valueOf(userId);
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        String roleId = String.valueOf(currentRole.getRoleId());

        combinationVO.setRoleId(roleId);
        combinationVO.setUserId(userIdStr);

        // 查询所有汇总组合名称
        List<DmCustomCombVO> customCombNameList = dmFocCustomCombDao.getCustomCombListByName(combinationVO);
        if (CollectionUtils.isNotEmpty(customCombNameList)) {
            throw new CommonApplicationException("组合名称已存在");
        }
        List<DmCustomCombVO> customVOList = new ArrayList<>();

        // 根据临时表id获取列表
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(combinationVO.getCostType())) {
            customVOList = dmFocCustomCombTempDao.getTempCustomCombList(combinationVO);
        } else {
            customVOList = dmFocCustomCombTempDao.getTempManufactureCustomCombList(combinationVO);
        }
        List<DmCustomCombVO> otherCustomVOList = new ArrayList<>();

        if (combinationVO.getPageFlag().contains(PAGE_FLAG_ALL)) {
            List<DmCustomCombVO> customOneList = new ArrayList<>();
            customOneList.addAll(customVOList);
            // 如果同步，需要查询出另一个页面的code，因为采购层级的code两个页面不一致
            combinationVO.setExpandFlag("Y");
            otherCustomVOList = customCommonService.filterAnotherPageData(combinationVO, customOneList);
        }
        // 获取序列
        Timestamp time = new Timestamp(System.currentTimeMillis());
        Long customId = Long.parseLong(new SimpleDateFormat("yyyyMMddHHmmsss").format(time));

        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        // 组装当前传入的数据
        List<DmCustomCombVO> oneCustomList = getCurrentPageCustomCombList(combinationVO, userIdStr, roleId, customVOList, customId, timestamp);
        // 组装另一个页面code
        combineOtherPageGroupCode(combinationVO, userIdStr, roleId, otherCustomVOList, customId, timestamp);
        DmFocDataRefreshStatus dataRefreshStatus = new DmFocDataRefreshStatus();
        dataRefreshStatus.setStatus(INIT);
        dataRefreshStatus.setDelFlag(IS_NOT);
        dataRefreshStatus.setCreatedBy(userId);
        dataRefreshStatus.setTaskFlag(COMB);
        dataRefreshStatus.setLastUpdatedBy(userId);
        dataRefreshStatus.setCreationDate(new Date());
        dataRefreshStatus.setTaskId(dataRefreshStatusDao.getDataRefrashKey());
        dataRefreshStatus.setRoleId(currentRole.getRoleId());
        // 创建异步任务记录，进行新增操作
        dataRefreshStatusDao.createDmFocDataRefreshStatus(dataRefreshStatus);
        CombTransformVO combTransformVO = getCombTransformVO(combinationVO, userId, customId, dataRefreshStatus);
        asyncService.asyncCreateCustom(oneCustomList, combTransformVO, otherCustomVOList);
        // 进行新增操作
        return ResultDataVO.success(dataRefreshStatus);
    }

    @NotNull
    private CombTransformVO getCombTransformVO(CombinationVO combinationVO, Long userId, Long customId, DmFocDataRefreshStatus dataRefreshStatus) {
        CombTransformVO combTransformVO = new CombTransformVO();
        combTransformVO.setUserId(userId);
        combTransformVO.setTaskId(dataRefreshStatus.getTaskId());
        combTransformVO.setCustomId(customId);
        combTransformVO.setGranularityType(combinationVO.getGranularityType());
        combTransformVO.setIndustryOrg(combinationVO.getIndustryOrg());
        combTransformVO.setTablePreFix(combinationVO.getTablePreFix());
        // 获取加密key
        String plainText = ConfigUtil.getInstance().get16PlainText();
        combTransformVO.setEncryptKey(plainText);
        combTransformVO.setPageFlag(combinationVO.getPageFlag());
        // 获取最新版本号
        DmFocVersionInfoDTO dmFocVersionVO = dmFocVersionDao.findVersionIdByDataType("CATEGORY", TableNameVO.getTablePreFix(combinationVO.getIndustryOrg()));
        combTransformVO.setVersionId(dmFocVersionVO.getVersionId());
        DmFocVersionInfoDTO monthVersionVO = dmFocVersionDao.findVersionIdByDataType("ITEM", TableNameVO.getTablePreFix(combinationVO.getIndustryOrg()));
        combTransformVO.setMonthVersionId(monthVersionVO.getVersionId());
        combTransformVO.setCostType(combinationVO.getCostType());
        return combTransformVO;
    }

    private List<DmCustomCombVO> getTempCurrentPageCustomCombList(CombinationVO combinationVO, List<DmCustomCombVO> customVOList, Long id, Timestamp timestamp) {
        String userIdStr = combinationVO.getUserId();
        String roleId = combinationVO.getRoleId();
        return customVOList.stream().map(customVO -> {
            DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
            BeanUtils.copyProperties(customVO, dmCustomCombVO);
            dmCustomCombVO.setId(id);
            dmCustomCombVO.setCustomCnName(String.valueOf(id));
            dmCustomCombVO.setCreatedBy(userIdStr);
            dmCustomCombVO.setCreationDate(timestamp);
            dmCustomCombVO.setLastUpdatedBy(userIdStr);
            dmCustomCombVO.setLastUpdateDate(timestamp);
            dmCustomCombVO.setRoleId(roleId);
            dmCustomCombVO.setUserId(userIdStr);
            dmCustomCombVO.setEnableFlag(ENABLE_FLAG_Y);
            dmCustomCombVO.setSubEnableFlag(ENABLE_FLAG_Y);
            dmCustomCombVO.setIsSeparate("N");
            dmCustomCombVO.setViewFlag(combinationVO.getViewFlag());
            dmCustomCombVO.setPageFlag(combinationVO.getPageSymbol());
            dmCustomCombVO.setOverseaFlag(combinationVO.getOverseaFlag());
            dmCustomCombVO.setCaliberFlag(combinationVO.getCaliberFlag());
            dmCustomCombVO.setGranularityType(combinationVO.getGranularityType());
            dmCustomCombVO.setLv0ProdListCnName(combinationVO.getLv0ProdListCnName());
            dmCustomCombVO.setLv0ProdListCode(combinationVO.getLv0ProdListCode());
            return dmCustomCombVO;
        }).collect(Collectors.toList());
    }

    @NotNull
    private List<DmCustomCombVO> getCurrentPageCustomCombList(CombinationVO combinationVO, String userIdStr, String roleId, List<DmCustomCombVO> customVOList, Long customId, Timestamp timestamp) {
        return customVOList.stream().map(customVO -> {
            DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
            BeanUtils.copyProperties(customVO, dmCustomCombVO);
            dmCustomCombVO.setCustomId(customId);
            dmCustomCombVO.setCustomCnName(combinationVO.getCustomCnName());
            dmCustomCombVO.setCreatedBy(userIdStr);
            dmCustomCombVO.setCreationDate(timestamp);
            dmCustomCombVO.setLastUpdatedBy(userIdStr);
            dmCustomCombVO.setLastUpdateDate(timestamp);
            dmCustomCombVO.setRoleId(roleId);
            dmCustomCombVO.setUserId(userIdStr);
            dmCustomCombVO.setEnableFlag(ENABLE_FLAG_Y);
            dmCustomCombVO.setSubEnableFlag(ENABLE_FLAG_Y);
            dmCustomCombVO.setIsSeparate("N");
            dmCustomCombVO.setViewFlag(combinationVO.getViewFlag());
            dmCustomCombVO.setOverseaFlag(combinationVO.getOverseaFlag());
            dmCustomCombVO.setCaliberFlag(combinationVO.getCaliberFlag());
            if (PAGE_FLAG_ALL.equals(combinationVO.getPageFlag())) {
                dmCustomCombVO.setPageFlag(combinationVO.getPageFlag() + "_" + combinationVO.getPageSymbol());
            } else {
                dmCustomCombVO.setPageFlag(combinationVO.getPageFlag());
            }
            dmCustomCombVO.setGranularityType(combinationVO.getGranularityType());
            dmCustomCombVO.setLv0ProdListCnName(combinationVO.getLv0ProdListCnName());
            dmCustomCombVO.setLv0ProdListCode(combinationVO.getLv0ProdListCode());
            return dmCustomCombVO;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @JalorOperation(code = "renameCombination", desc = "汇总组合重命名")
    @Audit(module = "customCombService-renameCombination", operation = "renameCombination",
            message = "汇总组合重命名")
    public ResultDataVO renameCombination(CombinationVO combinationVO) throws CommonApplicationException {
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        int roleId = currentRole.getRoleId();
        Long userId = UserInfoUtils.getUserId();
        combinationVO.setRoleId(String.valueOf(roleId));
        combinationVO.setUserId(String.valueOf(userId));
        // 查询所有汇总组合名称
        List<DmCustomCombVO> customCombNameList = dmFocCustomCombDao.getCustomCombListByName(combinationVO);
        if (CollectionUtils.isNotEmpty(customCombNameList)) {
            throw new CommonApplicationException("组合名称已存在");
        }
        // 如果数据不是同步的
        if (!combinationVO.getPageFlag().contains(PAGE_FLAG_ALL)) {
            // 更新原pageFlag和名称
            dmFocCustomCombDao.renameCombination(combinationVO);
        } else {
            // 如果数据是同步,更新原pageFlag和名称
            dmFocCustomCombDao.renameCombination(combinationVO);
            // 原数据也是同步的,更新另一个页面的pageFlag和名称
            combinationVO.setPageFlag("ALL_" + allPageFlag.get(combinationVO.getPageSymbol()));
            dmFocCustomCombDao.renameCombination(combinationVO);
        }
        DmFocDataRefreshStatus dataRefreshStatus = new DmFocDataRefreshStatus();
        dataRefreshStatus.setStatus(INIT);
        dataRefreshStatus.setDelFlag(IS_NOT);
        dataRefreshStatus.setCreatedBy(userId);
        dataRefreshStatus.setLastUpdatedBy(userId);
        dataRefreshStatus.setCreationDate(new Date());
        dataRefreshStatus.setTaskFlag(COMB);
        dataRefreshStatus.setTaskId(dataRefreshStatusDao.getDataRefrashKey());
        dataRefreshStatus.setRoleId(currentRole.getRoleId());
        // 创建异步任务记录，进行新增操作
        dataRefreshStatusDao.createDmFocDataRefreshStatus(dataRefreshStatus);
        // 调用函数刷新数据
        CombTransformVO combTransformVO = new CombTransformVO();
        callFunctionRefreshData(combinationVO, userId, dataRefreshStatus, combTransformVO);
        asyncService.asyncCombRename(combTransformVO);
        return ResultDataVO.success(dataRefreshStatus);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @JalorOperation(code = "updateCombination", desc = "编辑汇总组合")
    @Audit(module = "customCombService-updateCombination", operation = "updateCombination",
            message = "编辑汇总组合")
    public ResultDataVO updateCombination(CombinationVO combinationVO) throws CommonApplicationException {
        Long userLongId = UserInfoUtils.getUserId();
        String userId = String.valueOf(userLongId);
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        String roleId = String.valueOf(currentRole.getRoleId());
        combinationVO.setUserId(userId);
        combinationVO.setRoleId(roleId);

        DmFocDataRefreshStatus dataRefreshStatus = new DmFocDataRefreshStatus();
        dataRefreshStatus.setStatus(INIT);
        dataRefreshStatus.setDelFlag(IS_NOT);
        dataRefreshStatus.setCreatedBy(userLongId);
        dataRefreshStatus.setLastUpdatedBy(userLongId);
        dataRefreshStatus.setCreationDate(new Date());
        dataRefreshStatus.setTaskFlag(COMB);
        dataRefreshStatus.setTaskId(dataRefreshStatusDao.getDataRefrashKey());
        dataRefreshStatus.setRoleId(currentRole.getRoleId());
        // 创建异步任务记录，进行新增操作
        dataRefreshStatusDao.createDmFocDataRefreshStatus(dataRefreshStatus);
        DataPermissionsVO currentRoleDataPermission = commonService.getCurrentRoleDataPermission(combinationVO.getIndustryOrg());
        combinationVO.setLv0DimensionSet(currentRoleDataPermission.getLv0DimensionSet());
        combinationVO.setLv1DimensionSet(currentRoleDataPermission.getLv1DimensionSet());
        combinationVO.setLv2DimensionSet(currentRoleDataPermission.getLv2DimensionSet());
        // 调用函数刷新数据
        CombTransformVO combTransformVO = new CombTransformVO();
        callFunctionRefreshData(combinationVO, userLongId, dataRefreshStatus, combTransformVO);
        asyncService.asyncCombUpdate(combinationVO, combTransformVO);
        return ResultDataVO.success(dataRefreshStatus);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @JalorOperation(code = "deleteCombination", desc = "删除汇总组合")
    @Audit(module = "customCombService-deleteCombination", operation = "deleteCombination",
            message = "删除汇总组合")
    public ResultDataVO deleteCombination(CombinationVO combinationVO) {
        // 如果选择不同步删除，那么只删除此页面的数据
        if (!combinationVO.getPageFlag().contains(PAGE_FLAG_ALL)) {
            // 未勾选同步
            dmFocCustomCombDao.deleteCustomList(combinationVO);
            // 原数据不是同步的,直接删除原数据，并查看同个customId下是否另一个页面有数据，如果有，则需要更新另一个页面的isSeparate为N
            String otherPageFlag = allPageFlag.get(combinationVO.getPageFlag());
            combinationVO.setPageFlag(otherPageFlag);
            combinationVO.setIsSeparate("N");
            dmFocCustomCombDao.updateCustomSeparate(combinationVO);
        } else {
            // 原数据是同步的，并勾选同步，那么两个页面数据都要删除
            dmFocCustomCombDao.deleteCustomList(combinationVO);
            String otherPageFlag = allPageFlag.get(combinationVO.getPageFlag());
            combinationVO.setOldPageFlag(otherPageFlag);
            dmFocCustomCombDao.deleteCustomList(combinationVO);
        }
        return ResultDataVO.success();
    }

    @Override
    @JalorOperation(code = "getProdRndTeamTree", desc = "汇总组合左侧树查询")
    public ResultDataVO getProdRndTeamTree(CommonViewVO commonViewVO) {
        // 设置数据权限
        getPermissionForProdTeamCodeList(commonViewVO);
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        commonViewVO.setRoleId(String.valueOf(currentRole.getRoleId()));
        commonViewVO.setUserId(String.valueOf(currentUser.getUserId()));
        // 获取最新的top品version_id
        commonViewVO.setVersionId(dmFocVersionDao.findAnnualVersion(commonViewVO.getTablePreFix()).getVersionId());
        // 计算最近的三年
        List<String> threeYears = annualCommonService.getThreeYears(commonViewVO.getCostType(), commonViewVO.getIndustryOrg());
        // 获取当前年份
        if (CollectionUtils.isNotEmpty(threeYears)) {
            int countYear = threeYears.size() - 1;
            commonViewVO.setPeriodYear(threeYears.get(countYear));
        }
        // 获取最新的规格品version_id
        commonViewVO.setMonthVersionId(commonService.getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(), commonViewVO.getTablePreFix()));
        String granularityType = commonViewVO.getGranularityType();
        String granularityPageSymbol = granularityType + "_" + commonViewVO.getPageSymbol();
        commonViewVO.setGranularityPageSymbol(granularityPageSymbol);
        List<DmFocViewInfoVO> dmCustomCombList = getProdRndTeamCodeTree(commonViewVO);
        // 无权限的重量级团队设置标识
        List<DmFocViewInfoVO> allGroupLevelConditionList = new ArrayList<>();
        setPermissionFlag(commonViewVO, dmCustomCombList, allGroupLevelConditionList);
        return ResultDataVO.success(dmCustomCombList);
    }

    @Override
    @JalorOperation(code = "getCombinationList", desc = "根据汇总组合名称查询列表")
    public ResultDataVO getCombinationList(CommonViewVO commonViewVO) {
        List<DmFocViewInfoVO> allGroupCodeList = new ArrayList<>();
        // 没有传颗粒度，说明是空列表
        if (StringUtils.isEmpty(commonViewVO.getGranularityType())) {
            return ResultDataVO.success();
        }
        List<DmFocViewInfoVO> dmCustomCombList;
        // 如果是展开查询，需要获取下一层级groupLevel
        findNextGroupLevel(commonViewVO);
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
            dmCustomCombList = dmFocCustomCombDao.getCombinationList(commonViewVO);
        } else {
            dmCustomCombList = dmFocMadeCustomCombDao.getManufacutreCombinationList(commonViewVO);
        }
        // 如果是初次查询，只查询重量级团队
        List<DmFocViewInfoVO> otherLevelList = firstQueryList(commonViewVO, dmCustomCombList);

        String granularityType = commonViewVO.getGranularityType();
        String granularityPageSymbol = granularityType + "_" + commonViewVO.getPageSymbol();
        commonViewVO.setGranularityPageSymbol(granularityPageSymbol);
        // 获取最新的规格品version_id
        commonViewVO.setMonthVersionId(commonService.getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(), commonViewVO.getTablePreFix()));
        // 获取组合的父层级
        List<DmFocViewInfoVO> customCombList = new ArrayList<>();
        customCombList.addAll(dmCustomCombList);
        if (commonViewVO.getPageFlag().contains("ALL")) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
                customCombList.removeIf(dm -> "CEG".equals(dm.getGroupLevel()) || "MODL".equals(dm.getGroupLevel()) || "CATEGORY".equals(dm.getGroupLevel()));
            } else {
                customCombList.removeIf(dm -> "SHIPPING_OBJECT".equals(dm.getGroupLevel()) || "MANUFACTURE_OBJECT".equals(dm.getGroupLevel()));
            }
        }
        customCombList.addAll(otherLevelList);
        Map<String, List<DmFocViewInfoVO>> groupLevelMap = customCombList.stream().collect(Collectors.groupingBy(DmFocViewInfoVO::getGroupLevel));
        // 获取当前custom_id的父列表
        if (StringUtils.isBlank(commonViewVO.getLv0ProdRndTeamCode())) {
            // 当非展开层级的时候，才需要去获取父层级
            List<DmFocViewInfoVO> parentCodeList = getParentCodeList(commonViewVO, groupLevelMap, customCombList, true);
            Set<String> combList = dmCustomCombList.stream().map(DmFocViewInfoVO::getConnectCode).collect(Collectors.toSet());
            Set<String> parentList = parentCodeList.stream().map(DmFocViewInfoVO::getConnectCode).collect(Collectors.toSet());
            // 交集
            Set<String> intersectionList = combList.stream().filter(comb -> parentList.contains(comb)).collect(Collectors.toSet());
            allGroupCodeList.addAll(parentCodeList);
            if (CollectionUtils.isNotEmpty(intersectionList)) {
                allGroupCodeList.removeIf(all -> intersectionList.contains(all.getConnectCode()) && null == all.getCustomId());
            }
        }
        allGroupCodeList.addAll(dmCustomCombList);

        // 把父和子汇总后的list通过group_level层级，分层级展示
        List<DmFocViewInfoVO> allGroupLevelConditionList = new ArrayList<>();
        Map<String, List<DmFocViewInfoVO>> viewInfoMap = allGroupCodeList.stream().collect(Collectors.groupingBy(DmFocViewInfoVO::getGroupLevel));

        allGroupLevelCondition(commonViewVO, viewInfoMap, allGroupLevelConditionList);

        return ResultDataVO.success(allGroupLevelConditionList);
    }

    @Override
    @JalorOperation(code = "getCombinationNameList", desc = "汇总组合名称下拉列表")
    public ResultDataVO getCombinationNameList(CombinationVO combinationVO) {
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        combinationVO.setRoleId(String.valueOf(currentRole.getRoleId()));
        combinationVO.setUserId(String.valueOf(UserInfoUtils.getUserId()));
        // 查询下拉框汇总组合名称
        List<DmCustomCombVO> customCombNameList = dmFocCustomCombDao.getCustomCombNameList(combinationVO);
        return ResultDataVO.success(customCombNameList);
    }

    @Override
    @JalorOperation(code = "getGroupLevelList", desc = "获取当前数据权限groupLevel")
    public ResultDataVO getGroupLevelList(CombinationVO combinationVO) {
        // 获取数据权限
        getPermissionForProdTeamCodeList(combinationVO);
        Set<String> lv2DimensionSet = combinationVO.getLv2DimensionSet();
        List<String> groupLevelList = new ArrayList<>();
        boolean lv2PermissionFlag = lv2DimensionSet.size() == 0 || !lv2DimensionSet.contains("NO_PERMISSION");
        getProdTeamLevel(groupLevelList, combinationVO);

        profitGroupLevel(combinationVO, lv2PermissionFlag, groupLevelList);
        dimensionGroupLevel(combinationVO, groupLevelList);
        // 去重
        List<String> newGroupLevelList = groupLevelList.stream().distinct().collect(Collectors.toList());
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(combinationVO.getCostType())) {
            newGroupLevelList.add(GroupLevelAllEnum.SHIPPING_OBJECT.getValue());
            newGroupLevelList.add(GroupLevelAllEnum.MANUFACTURE_OBJECT.getValue());
        }
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(combinationVO.getCostType())) {
            newGroupLevelList.add(GroupLevelAllEnum.CEG.getValue());
            newGroupLevelList.add(GroupLevelAllEnum.MODL.getValue());
            newGroupLevelList.add(GroupLevelAllEnum.CATEGORY.getValue());
        }
        return ResultDataVO.success(newGroupLevelList);
    }

    @Override
    @JalorOperation(code = "initEnableFlag", desc = "初始化部分失效和整体失效标识")
    @Audit(module = "customCombService-initEnableFlag", operation = "initEnableFlag",
            message = "初始化部分失效和整体失效标识")
    @NoJalorTransation
    public ResultDataVO initEnableFlag() {

        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        int roleId = currentRole.getRoleId();
        Long userId = UserInfoUtils.getUserId();

        DmFocDataRefreshStatus refreshStatus = new DmFocDataRefreshStatus();
        refreshStatus.setTaskFlag(INIT_COMB);
        refreshStatus.setUserId(userId);
        refreshStatus.setRoleId(roleId);
        // 判断是否已经有轮询任务，如果有直接返回
        DmFocDataRefreshStatus dmFocDataRefreshStatus = dataRefreshStatusDao.findDmFocDataRefreshStatus(refreshStatus);
        // 判断当天是否已经执行过任务了，如果执行过不再二次执行
        DmFocDataRefreshStatus dmFocDataRefreshStatusByDay = dataRefreshStatusDao.findDmFocDataRefreshStatusByDay(refreshStatus);
        if (null != dmFocDataRefreshStatus || null != dmFocDataRefreshStatusByDay) {
            return ResultDataVO.success();
        }
        // 同个用户，同个角色，切换不同数据范围时，查询需要先判断是否整体失效，还是部分失效，更新这两个标识到数据库中
        CombinationVO combinationVO = new CombinationVO();
        // 采购成本-ICT
        List<DmCustomCombVO> customCombList = getCustomCombListByPerson(combinationVO, userId, roleId);
        // 采购成本-数字能源
        List<DmCustomCombVO> customCombEnergyList = dmFocCustomCombDao.getEnergyCustomCombList(combinationVO);
        // 采购成本-ias
        List<DmCustomCombVO> customCombIasList = dmFocIasCustomCombDao.getIasCustomCombList(combinationVO);
        // 制造成本-ICT
        List<DmCustomCombVO> manufactureCustomCombList = dmFocCustomCombDao.getManufactureCustomCombList(combinationVO);
        // 制造成本-数字能源
        List<DmCustomCombVO> manufactureCustomCombEnergyList = dmFocCustomCombDao.getManufactureEnergyCustomCombList(combinationVO);
        // 制造成本-ias
        List<DmCustomCombVO> customCombIasMadeList = dmFocIasCustomCombDao.getManufactureIasCustomCombList(combinationVO);
        boolean flag = CollectionUtils.isEmpty(customCombList) && CollectionUtils.isEmpty(manufactureCustomCombList) && CollectionUtils.isEmpty(customCombEnergyList);

        if (flag && CollectionUtils.isEmpty(manufactureCustomCombEnergyList) && CollectionUtils.isEmpty(customCombIasList) && CollectionUtils.isEmpty(customCombIasMadeList)) {
            return ResultDataVO.success();
        }
        DmFocVersionInfoDTO monthVersionVO = dmFocVersionDao.findVersionIdByDataType("ITEM", "dm_foc");
        combinationVO.setMonthVersionId(monthVersionVO.getVersionId());

        DmFocVersionInfoDTO energyMonthVersionVO = dmFocVersionDao.findVersionIdByDataType("ITEM", "dm_foc_energy");
        combinationVO.setEnergyMonthVersionId(energyMonthVersionVO.getVersionId());

        DmFocDataRefreshStatus dataRefreshStatus = new DmFocDataRefreshStatus();
        dataRefreshStatus.setStatus(INIT);
        dataRefreshStatus.setDelFlag(IS_NOT);
        dataRefreshStatus.setCreatedBy(userId);
        dataRefreshStatus.setLastUpdatedBy(userId);
        dataRefreshStatus.setCreationDate(new Date());
        dataRefreshStatus.setTaskFlag(INIT_COMB);
        dataRefreshStatus.setTaskId(dataRefreshStatusDao.getDataRefrashKey());
        dataRefreshStatus.setRoleId(roleId);

        CombTransformVO combTransformVO = new CombTransformVO();
        combTransformVO.setUserId(userId);
        combTransformVO.setTaskId(dataRefreshStatus.getTaskId());
        combTransformVO.setCustomCombIasList(customCombIasList);
        combTransformVO.setCustomCombIasMadeList(customCombIasMadeList);

        // 创建异步任务记录，进行新增操作
        dataRefreshStatusDao.createDmFocDataRefreshStatus(dataRefreshStatus);
        asyncService.asyncInitData(customCombList, manufactureCustomCombList, customCombEnergyList, manufactureCustomCombEnergyList, combinationVO, combTransformVO);
        return ResultDataVO.success(dataRefreshStatus);
    }

    @Override
    @Audit(module = "customCombService-createTempTable", operation = "createTempTable", message = "汇总组合-创建临时表")
    @JalorOperation(code = "createTempTable", desc = "汇总组合-创建临时表")
    public ResultDataVO createTempTable(CombinationVO combinationVO) {
        Long userId = UserInfoUtils.getUserId();
        String userIdStr = String.valueOf(userId);
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        String roleId = String.valueOf(currentRole.getRoleId());

        combinationVO.setRoleId(roleId);
        combinationVO.setUserId(userIdStr);

        // 父层级的list设置为parent
        List<DmCustomCombVO> parentCustomVOList = combinationVO.getParentCustomVOList();

        parentCustomVOList.stream().forEach(parent -> parent.setSelectFlag("parent"));

        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        Long id = Long.parseLong(new SimpleDateFormat("yyyyMMddHHmmsss").format(timestamp));

        List<DmCustomCombVO> oneCustomList = getTempCurrentPageCustomCombList(combinationVO, parentCustomVOList, id, timestamp);

        insertTempCustomRecursion(oneCustomList, combinationVO.getCostType(), combinationVO.getIndustryOrg(), 0L, 500L);
        // 把custom_id的数据插入到临时表
        combinationVO.setId(String.valueOf(id));
        if (Constant.StrEnum.ICT_ORG.getValue().equals(combinationVO.getIndustryOrg())) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(combinationVO.getCostType())) {
                dmFocCustomCombTempDao.createTempCustomCombByCustomId(combinationVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(combinationVO.getCostType())) {
                dmFocCustomCombTempDao.createMadeTempCustomCombByCustomId(combinationVO);
            }
        }
        if (Constant.StrEnum.ENERGY_ORG.getValue().equals(combinationVO.getIndustryOrg())) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(combinationVO.getCostType())) {
                dmFocEnergyCustomCombTempDao.createEnergyTempCustomCombByCustomId(combinationVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(combinationVO.getCostType())) {
                dmFocEnergyCustomCombTempDao.createEnergyMadeTempCustomCombByCustomId(combinationVO);
            }
        }
        if (Constant.StrEnum.IAS_ORG.getValue().equals(combinationVO.getIndustryOrg())) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(combinationVO.getCostType())) {
                dmFocIasCustomCombTempDao.createIasTempCustomCombByCustomId(combinationVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(combinationVO.getCostType())) {
                dmFocIasCustomCombTempDao.createIasMadeTempCustomCombByCustomId(combinationVO);
            }
        }
        return ResultDataVO.success();
    }

    @Override
    @JalorOperation(code = "deleteTempTable", desc = "汇总组合-删除临时表")
    @Audit(module = "customCombService-deleteTempTable", operation = "deleteTempTable", message = "汇总组合-删除临时表")
    public ResultDataVO deleteTempTable(CombinationVO combinationVO) {
        Long userId = UserInfoUtils.getUserId();
        String userIdStr = String.valueOf(userId);
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        String roleId = String.valueOf(currentRole.getRoleId());
        combinationVO.setUserId(userIdStr);
        combinationVO.setRoleId(roleId);
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(combinationVO.getCostType())) {
            dmFocCustomCombTempDao.deleteCustomTemp(combinationVO);
        } else {
            dmFocCustomCombTempDao.deleteManufacutreCustomTemp(combinationVO);
        }
        return ResultDataVO.success();
    }

    @Override
    @JalorOperation(code = "getTempTableList", desc = "汇总组合-获取临时表列表")
    public ResultDataVO getTempTableList(CombinationVO combinationVO) {
        setUserParam(combinationVO);
        // 没有传颗粒度，说明是空列表
        if (StringUtils.isEmpty(combinationVO.getGranularityType())) {
            return ResultDataVO.success();
        }
        List<DmCustomCombVO> dmCustomCombList;
        List<DmCustomCombVO> allDmCustomCombList = new ArrayList<>();
        if (StringUtils.isNotEmpty(combinationVO.getLv0ProdRndTeamCode())) {
            String groupLevel;
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(combinationVO.getCostType())) {
                groupLevel = FcstIndexMadeUtil.getNextGroupLevelByView(combinationVO.getViewFlag(),
                        combinationVO.getGroupLevel(), combinationVO.getGranularityType(), combinationVO.getIndustryOrg());
            } else {
                groupLevel = FcstIndexUtil.getNextGroupLevelByView(combinationVO.getViewFlag(),
                        combinationVO.getGroupLevel(), combinationVO.getGranularityType(), combinationVO.getIndustryOrg());
            }
            combinationVO.setGroupLevel(groupLevel);
        }
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(combinationVO.getCostType())) {
            dmCustomCombList = dmFocCustomCombTempDao.getTempTableCustomCombList(combinationVO);
        } else {
            dmCustomCombList = dmFocCustomCombTempDao.getTempTableManufactureCustomCombList(combinationVO);
        }
        allDmCustomCombList.addAll(dmCustomCombList);
        Map customCombMap = new HashMap();
        if (StringUtils.isEmpty(combinationVO.getLv0ProdRndTeamCode())) {
            // 设置需要展示的list
            showTree(dmCustomCombList, combinationVO);
            // 判断是否只有采购层级或者制造层级的数据
            List<DmCustomCombVO> allDmCustomCombVOList = allDmCustomCombList.stream().filter(all -> "current".equals(all.getSelectFlag())).collect(Collectors.toList());
            Set<String> allGroupLevelSet = allDmCustomCombVOList.stream().map(DmCustomCombVO::getGroupLevel).collect(Collectors.toSet());
            Set<String> purchaseMadeGroupLevelSet = new HashSet<>();
            purchaseMadeGroupLevelSet.add(GroupLevelAllEnum.CEG.getValue());
            purchaseMadeGroupLevelSet.add(GroupLevelAllEnum.MODL.getValue());
            purchaseMadeGroupLevelSet.add(GroupLevelAllEnum.CATEGORY.getValue());
            purchaseMadeGroupLevelSet.add(GroupLevelAllEnum.SHIPPING_OBJECT.getValue());
            purchaseMadeGroupLevelSet.add(GroupLevelAllEnum.MANUFACTURE_OBJECT.getValue());
            // 如果只有采购层级或者制造层级，则同步复选框需要置灰
            if (purchaseMadeGroupLevelSet.containsAll(allGroupLevelSet)) {
                customCombMap.put("syncFlag", "N");
            } else {
                customCombMap.put("syncFlag", "Y");
            }
        }
        Map<String, List<DmCustomCombVO>> viewInfoMap = dmCustomCombList.stream().collect(Collectors.groupingBy(DmCustomCombVO::getGroupLevel));
        List<DmCustomCombVO> allGroupLevelConditionList = new ArrayList<>();
        allTempGroupLevelCondition(combinationVO, viewInfoMap, allGroupLevelConditionList);
        customCombMap.put("list", allGroupLevelConditionList);
        return ResultDataVO.success(customCombMap);
    }

    @Override
    @JalorOperation(code = "removeTempTableList", desc = "汇总组合-左右移动列表")
    @Audit(module = "customCombService-removeTempTableList", operation = "removeTempTableList", message = "汇总组合-左右移动列表")
    public ResultDataVO removeTempTableList(CombinationVO combinationVO) throws InterruptedException {

        // 设置参数
        setMoveParam(combinationVO);
        List<DmCustomCombVO> customVOList = combinationVO.getCustomVOList();
        // 获取折叠的list
        List<DmCustomCombVO> foldCustomList = customVOList.stream().filter(custom -> "Y".equals(custom.getFoldFlag())).collect(Collectors.toList());
        // 未折叠的list
        List<DmCustomCombVO> notFoldCustomList = customVOList.stream().filter(custom -> "N".equals(custom.getFoldFlag())).collect(Collectors.toList());
        List<DmCustomCombVO> customCombList = new ArrayList<>();
        IRequestContext requestContext = RequestContext.getCurrent();
        // 关键字筛选后的list或者层级筛选后的list
        List<DmCustomCombVO> filterCustomVOList = combinationVO.getFilterCustomVOList();
        if (CollectionUtils.isNotEmpty(filterCustomVOList) && "right".equals(combinationVO.getRemoveFlag())) {
            CombinationVO combinationParamVO = new CombinationVO();
            BeanUtils.copyProperties(combinationVO, combinationParamVO);
            // 如果筛选出来LV0下只有一个LV1，则不能移动，直接返回前端给出提示语
            Map<String, List<DmCustomCombVO>> dmCustomMap = filterCustomVOList.stream().collect(Collectors.groupingBy(DmCustomCombVO::getGroupLevel));
            combinationParamVO.setGranularityPageSymbol(combinationParamVO.getGranularityType() + "_" + combinationParamVO.getPageSymbol());
            // 如果选中了LV0下的某个grouplevel的所有code，则表示选中了LV0
            String selectLv0Data = getSelectCodeByLevel(combinationParamVO, dmCustomMap);
            if (StringUtils.isNotEmpty(selectLv0Data)) {
                ResultDataVO.success(selectLv0Data);
            }
        }
        // 折叠后的list获取到的全部子项
        Future<Boolean> groupCodeFlag = asyncService.getFoldGroupCodeList(combinationVO, foldCustomList, customCombList, requestContext);

        List<DmCustomCombVO> findParentList = new ArrayList<>();
        List<DmCustomCombVO> remainList = new ArrayList<>();
        Future<Boolean> filterGroupCodeFlag = new AsyncResult<>(Boolean.TRUE);
        if (CollectionUtils.isNotEmpty(filterCustomVOList)) {
            // 判断filterCustomVOList各个code之间，是否是父子关系
            findParentChildList(filterCustomVOList, findParentList, remainList, customCombList);
            // 获取折叠后的子层级
            filterGroupCodeFlag = asyncService.getFoldGroupCodeList(combinationVO, remainList, customCombList, requestContext);
        }
        while (true) {
            if (groupCodeFlag.isDone() && filterGroupCodeFlag.isDone()) {
                break;
            }
        }
        customCombList.stream().forEach(cust -> cust.setSelectFlag("current"));
        if (CollectionUtils.isNotEmpty(findParentList)) {
            // 获取筛选后list的父层级，设置SelectFlag为parent
            findParentByFilterList(findParentList, combinationVO, customCombList);
        }

        customCombList.addAll(foldCustomList);
        customCombList.addAll(notFoldCustomList);
        if ("right".equals(combinationVO.getRemoveFlag())) {
            // id不为空，表示临时表已经有数据了，需要过滤掉已在临时表中的connectCode
            removeByConnectCode(combinationVO, customCombList);
        }
        // 如果有某些父级的code改变了状态
        updateChangeListSelectFlag(combinationVO);
        // 左移或者右移
        leftOrRightMoveList(combinationVO, customCombList, filterCustomVOList);
        return ResultDataVO.success();
    }

    private String getSelectCodeByLevel(CombinationVO combinationParamVO, Map<String, List<DmCustomCombVO>> dmCustomMap) {
        // 设置数据权限
        getPermissionForProdTeamCodeList(combinationParamVO);
        annualCommonService.getLv1andLv2DimensionSet(combinationParamVO);
        combinationParamVO.setLv0Flag("N");
        if (combinationParamVO.getLv0DimensionSet().size() == 0 || !combinationParamVO.getLv0DimensionSet().contains("NO_PERMISSION")) {
            combinationParamVO.setLv0Flag("Y");
        }
        List<DmFocViewInfoVO> lv1GroupCodeList = new ArrayList<>();
        for (Map.Entry<String, List<DmCustomCombVO>> dmCustomEntry : dmCustomMap.entrySet()) {
            combinationParamVO.setGroupLevel(dmCustomEntry.getKey());
            List<DmFocViewInfoVO> allGroupCodeList = new ArrayList<>();
            // LV1,LV2需要权限控制
            if (GroupLevelAllEnum.LV1.getValue().equals(dmCustomEntry.getKey())) {
                customCommonService.getSubProdTeamCodeList(combinationParamVO, allGroupCodeList, lv1GroupCodeList, true);
            }
            if (GroupLevelAllEnum.LV2.getValue().equals(dmCustomEntry.getKey())) {
                customCommonService.getSubIndustryGroupCode(lv1GroupCodeList, combinationParamVO, allGroupCodeList, true);
            }
            if (!GroupLevelAllEnum.LV1.getValue().equals(dmCustomEntry.getKey()) && !GroupLevelAllEnum.LV2.getValue().equals(dmCustomEntry.getKey())) {
                customCommonService.getDbListForAllCondition(combinationParamVO, allGroupCodeList);
            }
            // 如果相等，说明选择了LV0下所有的当前这个grouplevel的code，则代表选中了lv0，直接返回
            if (allGroupCodeList.size() == dmCustomEntry.getValue().size()) {
                return "exist";
            }
        }
        return null;
    }

    private void updateChangeListSelectFlag(CombinationVO combinationVO) {
        List<DmCustomCombVO> changeCustomVOList = combinationVO.getChangeCustomVOList();
        if (CollectionUtils.isNotEmpty(changeCustomVOList)) {
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(combinationVO.getCostType())) {
                dmFocCustomCombTempDao.updateMadeChageSelectFlagList(combinationVO, changeCustomVOList);
            } else {
                dmFocCustomCombTempDao.updateChageSelectFlagList(combinationVO, changeCustomVOList);
            }
        }
    }

    private void setUserParam(CombinationVO combinationVO) {
        Long userId = UserInfoUtils.getUserId();
        String userIdStr = String.valueOf(userId);
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        String roleId = String.valueOf(currentRole.getRoleId());
        combinationVO.setUserId(userIdStr);
        combinationVO.setRoleId(roleId);
    }

    private void showTree(List<DmCustomCombVO> dmCustomCombList, CombinationVO combinationVO) {
        if (StringUtils.isEmpty(combinationVO.getKeyWord())) {
            Set<String> customCombGroupLevelSet = dmCustomCombList.stream().map(DmFocViewInfoVO::getGroupLevel).collect(Collectors.toSet());
            Set<String> prodTeamCodeGroupLevelSet = new HashSet<>();
            prodTeamCodeGroupLevelSet.add(GroupLevelEnumU.LV0.getValue());
            prodTeamCodeGroupLevelSet.add(GroupLevelEnumU.LV1.getValue());
            prodTeamCodeGroupLevelSet.add(GroupLevelEnumU.LV2.getValue());
            prodTeamCodeGroupLevelSet.add(GroupLevelEnumU.LV3.getValue());
            // 判断原始数据是否有 重量级团队，如果有就只展示重量级团队的树，如果没有重量级团队，则全部展示
            Set<String> containsList = prodTeamCodeGroupLevelSet.stream().filter(customCombGroupLevelSet::contains).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(containsList)) {
                dmCustomCombList.removeIf(dm -> !prodTeamCodeGroupLevelSet.contains(dm.getGroupLevel()));
            }
        }
    }

    private void findNextGroupLevel(CommonViewVO commonViewVO) {
        if (StringUtils.isNotEmpty(commonViewVO.getLv0ProdRndTeamCode())) {
            // 获取下一层级
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
                MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
                monthAnalysisVO.setViewFlag(commonViewVO.getViewFlag());
                monthAnalysisVO.setGroupLevel(commonViewVO.getGroupLevel());
                monthAnalysisVO.setGranularityType(commonViewVO.getGranularityType());
                monthAnalysisVO.setIndustryOrg(commonViewVO.getIndustryOrg());
                Map map = FcstIndexMadeUtil.getNextGroupLevel(monthAnalysisVO);
                commonViewVO.setGroupLevel(map.get("nextGroupLevel").toString());
            } else {
                MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
                monthAnalysisVO.setViewFlag(commonViewVO.getViewFlag());
                monthAnalysisVO.setGroupLevel(commonViewVO.getGroupLevel());
                monthAnalysisVO.setGranularityType(commonViewVO.getGranularityType());
                monthAnalysisVO.setIndustryOrg(commonViewVO.getIndustryOrg());
                Map map = FcstIndexUtil.getNextGroupLevel(monthAnalysisVO);
                commonViewVO.setGroupLevel(map.get("nextGroupLevel").toString());
            }
        }
    }

    private List<DmFocViewInfoVO> firstQueryList(CommonViewVO commonViewVO, List<DmFocViewInfoVO> dmCustomCombList) {
        if (StringUtils.isEmpty(commonViewVO.getLv0ProdRndTeamCode())) {
            Set<String> customCombGroupLevelSet = dmCustomCombList.stream().map(DmFocViewInfoVO::getGroupLevel).collect(Collectors.toSet());
            Set<String> prodTeamCodeGroupLevelSet = new HashSet<>();
            prodTeamCodeGroupLevelSet.add(GroupLevelEnumU.LV0.getValue());
            prodTeamCodeGroupLevelSet.add(GroupLevelEnumU.LV1.getValue());
            prodTeamCodeGroupLevelSet.add(GroupLevelEnumU.LV2.getValue());
            prodTeamCodeGroupLevelSet.add(GroupLevelEnumU.LV3.getValue());
            // 判断原始数据是否有 重量级团队，如果有就只展示重量级团队的树，如果没有重量级团队，则全部展示
            Set<String> containsList = prodTeamCodeGroupLevelSet.stream().filter(customCombGroupLevelSet::contains).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(containsList)) {
                dmCustomCombList.removeIf(dm -> !prodTeamCodeGroupLevelSet.contains(dm.getGroupLevel()));
            }
        }
        return dmCustomCombList;
    }

    private void setMoveParam(CombinationVO combinationVO) {
        Long userId = UserInfoUtils.getUserId();
        String userIdStr = String.valueOf(userId);
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        String roleId = String.valueOf(currentRole.getRoleId());
        combinationVO.setUserId(userIdStr);
        combinationVO.setRoleId(roleId);
        // 获取最新的top品version_id
        combinationVO.setVersionId(dmFocVersionDao.findAnnualVersion(combinationVO.getTablePreFix()).getVersionId());
        // 计算最近的三年
        List<String> threeYears = annualCommonService.getThreeYears(combinationVO.getCostType(), combinationVO.getIndustryOrg());
        // 获取当前年份
        if (CollectionUtils.isNotEmpty(threeYears)) {
            int countYear = threeYears.size() - 1;
            combinationVO.setPeriodYear(threeYears.get(countYear));
        }
        // 获取最新的规格品version_id
        combinationVO.setMonthVersionId(commonService.getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(), combinationVO.getTablePreFix()));
    }

    private void findParentChildList(List<DmCustomCombVO> filterCustomVOList, List<DmCustomCombVO> findParentList, List<DmCustomCombVO> remainList, List<DmCustomCombVO> customCombList) {
        Set<String> combList = filterCustomVOList.stream().map(DmFocViewInfoVO::getConnectCode).collect(Collectors.toSet());
        Set<String> parentList = filterCustomVOList.stream().map(DmFocViewInfoVO::getConnectParentCode).collect(Collectors.toSet());
        // 交集，父的connectcode不需要再去求子集
        Set<String> intersectionList = combList.stream().filter(comb -> parentList.contains(comb)).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(intersectionList)) {
            List<DmCustomCombVO> findFilterParentList = filterCustomVOList.stream().filter(cust -> intersectionList.contains(cust.getConnectCode())).collect(Collectors.toList());
            findParentList.addAll(findFilterParentList);
            // 还需要去查找子集的list
            List<DmCustomCombVO> remainChildList = filterCustomVOList.stream().filter(cust -> !intersectionList.contains(cust.getConnectCode())).collect(Collectors.toList());
            remainList.addAll(remainChildList);
            customCombList.addAll(remainChildList);
        } else {
            findParentList.addAll(filterCustomVOList);
            remainList.addAll(filterCustomVOList);
        }
    }

    private void findParentByFilterList(List<DmCustomCombVO> findParentList, CombinationVO combinationVO, List<DmCustomCombVO> customCombList) {
        List<DmFocViewInfoVO> parentCustomCombList = new ArrayList<>();
        Map<String, List<DmFocViewInfoVO>> groupLevelMap = findParentList.stream().collect(Collectors.groupingBy(DmFocViewInfoVO::getGroupLevel));
        combinationVO.setGranularityPageSymbol(combinationVO.getGranularityType() + "_" + combinationVO.getPageSymbol());
        parentCustomCombList = getParentCodeList(combinationVO, groupLevelMap, findParentList, false);
        parentCustomCombList.stream().forEach(cust -> cust.setSelectFlag("parent"));
        Set<String> combList = customCombList.stream().map(DmFocViewInfoVO::getConnectCode).collect(Collectors.toSet());
        Set<String> parentList = parentCustomCombList.stream().map(DmFocViewInfoVO::getConnectCode).collect(Collectors.toSet());
        // 交集
        Set<String> intersectionList = combList.stream().filter(comb -> parentList.contains(comb)).collect(Collectors.toSet());

        if (CollectionUtils.isNotEmpty(intersectionList)) {
            parentCustomCombList.removeIf(all -> intersectionList.contains(all.getConnectCode()));
        }
        List<DmCustomCombVO> parentCustomVOList = new ArrayList<>();
        parentCustomCombList.stream().forEach(parent -> {
            DmCustomCombVO dmCustomCombVO = ObjectCopyUtil.copy(parent, DmCustomCombVO.class);
            parentCustomVOList.add(dmCustomCombVO);
        });
        customCombList.addAll(parentCustomVOList);
        customCombList.addAll(findParentList);
    }

    private void leftOrRightMoveList(CombinationVO combinationVO, List<DmCustomCombVO> customCombList, List<DmCustomCombVO> filterCustomVOList) {
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        Long id;
        if ("Y".equals(combinationVO.getFirstFlag())) {
            id = Long.parseLong(new SimpleDateFormat("yyyyMMddHHmmsss").format(timestamp));
        } else {
            id = Long.valueOf(combinationVO.getId());
        }
        if ("right".equals(combinationVO.getRemoveFlag())) {
            // 组装当前传入的数据
            List<DmCustomCombVO> oneCustomList = getTempCurrentPageCustomCombList(combinationVO, customCombList, id, timestamp);
            insertTempCustomRecursion(oneCustomList, combinationVO.getCostType(), combinationVO.getIndustryOrg(), 0L, 500L);
        } else {
            // 向左移动，需要删除传入的list
            leftRemove(customCombList, combinationVO, filterCustomVOList);
        }
    }

    private void removeByConnectCode(CombinationVO combinationVO, List<DmCustomCombVO> customCombList) {
        if (StringUtils.isNotEmpty(combinationVO.getId())) {
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(combinationVO.getCostType())) {
                List<String> tempParentCustomCombList = dmFocCustomCombTempDao.getTempParentManufactureCustomCombList(combinationVO);
                customCombList.removeIf(cust -> tempParentCustomCombList.contains(cust.getConnectCode()));
            } else {
                List<String> tempParentCustomCombList = dmFocCustomCombTempDao.getTempParentCustomCombList(combinationVO);
                customCombList.removeIf(cust -> tempParentCustomCombList.contains(cust.getConnectCode()));
            }
        }
    }

    private void leftRemove(List<DmCustomCombVO> customCombList, CombinationVO combinationVO, List<DmCustomCombVO> filterCustomVOList) {
        List<DmCustomCombVO> currentCustomList = customCombList.stream().filter(comb -> "current".equals(comb.getSelectFlag())).collect(Collectors.toList());

        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(combinationVO.getCostType())) {
            dmFocCustomCombTempDao.deleteManufacutreCustomTempByConnectCode(combinationVO, currentCustomList);
        } else {
            dmFocCustomCombTempDao.deleteCustomTempByConnectCode(combinationVO, currentCustomList);
        }
        // 如果子节点已经没有数据，父节点也需要被移除
        if (CollectionUtils.isNotEmpty(filterCustomVOList)) {
            List<DmCustomCombVO> parentSelectFlagList = customCombList.stream().filter(comb -> "parent".equals(comb.getSelectFlag())).collect(Collectors.toList());

            parentSelectFlagList.stream().forEach(parent -> {
                combinationVO.setConnectCode(parent.getConnectCode());
                if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(combinationVO.getCostType())) {
                    Integer count = dmFocCustomCombTempDao.getCountManufactureCustomComb(combinationVO);
                    if (count == 0) {
                        dmFocCustomCombTempDao.deleteManufacutreCustomTemp(combinationVO);
                    }
                } else {
                    Integer count = dmFocCustomCombTempDao.getCountCustomComb(combinationVO);
                    if (count == 0) {
                        dmFocCustomCombTempDao.deleteCustomTemp(combinationVO);
                    }
                }
            });
        }
    }

    private void insertTempCustomRecursion(List<DmCustomCombVO> customVOList, String costType, String industryOrg, Long start, Long limit) {
        List<DmCustomCombVO> customSubList =
                customVOList.stream().skip(start).limit(limit).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(customSubList)) {
            return;
        }
        // 插入数据
        if (Constant.StrEnum.ICT_ORG.getValue().equals(industryOrg)) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(costType)) {
                dmFocCustomCombTempDao.createTempCustomCombList(customSubList);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(costType)) {
                dmFocCustomCombTempDao.createTempManufactureCustomcombList(customSubList);
            }
        }
        if (Constant.StrEnum.ENERGY_ORG.getValue().equals(industryOrg)) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(costType)) {
                dmFocCustomCombTempDao.createTempEnergyCustomCombList(customSubList);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(costType)) {
                dmFocCustomCombTempDao.createTempEnergyManufactureCustomcombList(customSubList);
            }
        }
        if (Constant.StrEnum.IAS_ORG.getValue().equals(industryOrg)) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(costType)) {
                dmFocCustomCombTempDao.createTempIasCustomCombList(customSubList);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(costType)) {
                dmFocCustomCombTempDao.createTempIasManufactureCustomcombList(customSubList);
            }
        }
        insertTempCustomRecursion(customVOList, costType, industryOrg, start + limit, limit);
    }

    private void profitGroupLevel(CombinationVO combinationVO, boolean lv2PermissionFlag, List<String> groupLevelList) {
        if (IndustryIndexEnum.GRANULARITY_TYPE.PROFITS.getValue().equals(combinationVO.getGranularityType()) && IndustryIndexEnum.VIEW_FLAG_D.VIEW4.getValue().equals(combinationVO.getViewFlag()) && lv2PermissionFlag) {
            groupLevelList.add(GroupLevelAllEnum.L1.getValue());
        }
        if (IndustryIndexEnum.GRANULARITY_TYPE.PROFITS.getValue().equals(combinationVO.getGranularityType()) && IndustryIndexEnum.VIEW_FLAG_D.VIEW5.getValue().equals(combinationVO.getViewFlag()) && lv2PermissionFlag) {
            groupLevelList.add(GroupLevelAllEnum.L1.getValue());
            groupLevelList.add(GroupLevelAllEnum.L2.getValue());
        }
    }

    private void dimensionGroupLevel(CombinationVO combinationVO, List<String> groupLevelList) {
        if (Constant.StrEnum.ENERGY_ORG.getValue().equals(combinationVO.getIndustryOrg()) && IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(combinationVO.getGranularityType()) && IndustryIndexEnum.VIEW_FLAG_D.VIEW13.getValue().equals(combinationVO.getViewFlag())) {
            groupLevelList.add(GroupLevelAllEnum.COA.getValue());
        }
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(combinationVO.getGranularityType())) {
            // DIMENSION (量纲)/SUBCATEGORY (量纲子类)/SUB_DETAIL (量纲子类明)
            groupLevelList.add(GroupLevelAllEnum.DIMENSION.getValue());
        }

        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(combinationVO.getGranularityType()) && !IndustryIndexEnum.VIEW_FLAG_D.VIEW1.getValue().equals(combinationVO.getViewFlag()) && !IndustryIndexEnum.VIEW_FLAG_D.VIEW4.getValue().equals(combinationVO.getViewFlag()) && !IndustryIndexEnum.VIEW_FLAG_D.VIEW7.getValue().equals(combinationVO.getViewFlag())) {
            groupLevelList.add(GroupLevelAllEnum.SUBCATEGORY.getValue());
        }
        boolean flag = IndustryIndexEnum.VIEW_FLAG_D.VIEW3.getValue().equals(combinationVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_D.VIEW6.getValue().equals(combinationVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_D.VIEW9.getValue().equals(combinationVO.getViewFlag());
        boolean spartFlag = IndustryIndexEnum.VIEW_FLAG_D.VIEW10.getValue().equals(combinationVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_D.VIEW11.getValue().equals(combinationVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_D.VIEW12.getValue().equals(combinationVO.getViewFlag());
        boolean threeFlag = flag || spartFlag || IndustryIndexEnum.VIEW_FLAG_D.VIEW13.getValue().equals(combinationVO.getViewFlag());
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(combinationVO.getGranularityType()) && threeFlag) {
            groupLevelList.add(GroupLevelAllEnum.SUB_DETAIL.getValue());
        }
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(combinationVO.getGranularityType()) && (spartFlag || IndustryIndexEnum.VIEW_FLAG_D.VIEW13.getValue().equals(combinationVO.getViewFlag()))) {
            groupLevelList.add(GroupLevelAllEnum.SPART.getValue());
        }
    }

    private List<DmCustomCombVO> getCustomCombListByPerson(CombinationVO combinationVO, Long userId, int roleId) {

        combinationVO.setRoleId(String.valueOf(roleId));
        combinationVO.setUserId(String.valueOf(userId));
        return dmFocCustomCombDao.getCustomCombList(combinationVO);
    }

    private void getPermissionForProdTeamCodeList(CommonViewVO commonViewVO) {
        // 判断是否有LV0权限
        List<ViewInfoVO> allProdDimensionList = dataDimensionService.getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(), commonViewVO.getTablePreFix());
        commonService.getCommonViewVO(allProdDimensionList, commonViewVO);

        if (CollectionUtils.isEmpty(commonViewVO.getLv0DimensionSet()) && CollectionUtils.isEmpty(commonViewVO.getLv1DimensionSet())
                && CollectionUtils.isEmpty(commonViewVO.getLv2DimensionSet())) {
            commonViewVO.setLv0DimensionSet(commonViewVO.getLv0DimensionSet());
        } else {
            Set<String> lv0DimensionSet = new HashSet<>();
            lv0DimensionSet.add("NO_PERMISSION");
            commonViewVO.setLv0DimensionSet(lv0DimensionSet);
        }
    }

    private void getProdTeamLevel(List<String> groupLevelList, CombinationVO combinationVO) {

        Set<String> lv0DimensionSet = combinationVO.getLv0DimensionSet();
        Set<String> lv1DimensionSet = combinationVO.getLv1DimensionSet();
        Set<String> lv2DimensionSet = combinationVO.getLv2DimensionSet();
        boolean lv0PermissionFlag = lv0DimensionSet.size() == 0 || !lv0DimensionSet.contains("NO_PERMISSION");
        boolean lv1PermissionFlag = lv1DimensionSet.size() == 0 || !lv1DimensionSet.contains("NO_PERMISSION");
        boolean lv2PermissionFlag = lv2DimensionSet.size() == 0 || !lv2DimensionSet.contains("NO_PERMISSION");

        if (lv0PermissionFlag) {
            groupLevelList.add(GroupLevelAllEnum.LV0.getValue());
        }
        if (!IndustryIndexEnum.VIEW_FLAG_D.VIEW1.getValue().equals(combinationVO.getViewFlag()) && lv1PermissionFlag) {
            groupLevelList.add(GroupLevelAllEnum.LV1.getValue());
        }
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(combinationVO.getGranularityType()) && lv1PermissionFlag) {
            groupLevelList.add(GroupLevelAllEnum.LV1.getValue());
        }
        if (!IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(combinationVO.getGranularityType()) && !IndustryIndexEnum.VIEW_FLAG_D.VIEW1.getValue().equals(combinationVO.getViewFlag()) && !IndustryIndexEnum.VIEW_FLAG_D.VIEW2.getValue().equals(combinationVO.getViewFlag()) && lv2PermissionFlag) {
            groupLevelList.add(GroupLevelAllEnum.LV2.getValue());
        }
        getSubProdTeamLevel(groupLevelList, combinationVO, lv2PermissionFlag);
    }

    private void getSubProdTeamLevel(List<String> groupLevelList, CombinationVO combinationVO, boolean lv2PermissionFlag) {

        Set<String> lv3DimensionSet = combinationVO.getLv3DimensionSet();
        boolean lv3PermissionFlag = lv3DimensionSet.size() == 0 || !lv3DimensionSet.contains("NO_PERMISSION");
        boolean lv2DimensionFlag = !IndustryIndexEnum.VIEW_FLAG_D.VIEW1.getValue().equals(combinationVO.getViewFlag()) && !IndustryIndexEnum.VIEW_FLAG_D.VIEW2.getValue().equals(combinationVO.getViewFlag()) && !IndustryIndexEnum.VIEW_FLAG_D.VIEW3.getValue().equals(combinationVO.getViewFlag());
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(combinationVO.getGranularityType()) && lv2DimensionFlag && !"9".equals(combinationVO.getViewFlag())) {
            if (lv2PermissionFlag) {
                groupLevelList.add(GroupLevelAllEnum.LV2.getValue());
            }
        }
        prodTeamCodeLv3(groupLevelList, combinationVO, lv3PermissionFlag);
        prodTeamCodeLv4(groupLevelList, combinationVO);
    }

    private void prodTeamCodeLv3(List<String> groupLevelList, CombinationVO combinationVO, boolean lv3PermissionFlag) {
        if (IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(combinationVO.getGranularityType()) && (IndustryIndexEnum.VIEW_FLAG_P.VIEW4.getValue().equals(combinationVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_U.VIEW8.getValue().equals(combinationVO.getViewFlag())) && lv3PermissionFlag) {
            groupLevelList.add(GroupLevelAllEnum.LV3.getValue());
        }
        boolean lv3DimensionFlag = IndustryIndexEnum.VIEW_FLAG_D.VIEW7.getValue().equals(combinationVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_D.VIEW8.getValue().equals(combinationVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_D.VIEW9.getValue().equals(combinationVO.getViewFlag());
        boolean lv3Flag = lv3DimensionFlag || IndustryIndexEnum.VIEW_FLAG_D.VIEW12.getValue().equals(combinationVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_D.VIEW13.getValue().equals(combinationVO.getViewFlag());
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(combinationVO.getGranularityType()) && lv3Flag) {
            if (lv3PermissionFlag) {
                groupLevelList.add(GroupLevelAllEnum.LV3.getValue());
            }
        }
    }

    private void prodTeamCodeLv4(List<String> groupLevelList, CombinationVO combinationVO) {
        if (IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(combinationVO.getGranularityType()) && IndustryIndexEnum.VIEW_FLAG_U.VIEW8.getValue().equals(combinationVO.getViewFlag())) {
            groupLevelList.add(GroupLevelAllEnum.LV4.getValue());
        }
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(combinationVO.getGranularityType()) && IndustryIndexEnum.VIEW_FLAG_D.VIEW13.getValue().equals(combinationVO.getViewFlag())) {
            groupLevelList.add(GroupLevelAllEnum.LV4.getValue());
        }
    }

    private List<DmFocViewInfoVO> getProdRndTeamCodeTree(CommonViewVO commonViewVO) {

        List<DmFocViewInfoVO> allGroupCodeList = new ArrayList<>();
        CommonViewVO commonVO = new CommonViewVO();
        BeanUtils.copyProperties(commonViewVO, commonVO);
        commonVO.setReverseFlag(false);
        commonVO.setPageFlag(commonViewVO.getPageSymbol());
        commonVO.setIsMultipleSelect(false);

        if (StringUtils.isNotBlank(commonVO.getLv0ProdRndTeamCode())) {
            commonVO.setExpandFlag("Y");
            // 获取下一层级
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonVO.getCostType())) {
                MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
                monthAnalysisVO.setViewFlag(commonVO.getViewFlag());
                monthAnalysisVO.setGroupLevel(commonVO.getGroupLevel());
                monthAnalysisVO.setGranularityType(commonVO.getGranularityType());
                monthAnalysisVO.setIndustryOrg(commonVO.getIndustryOrg());
                Map map = FcstIndexMadeUtil.getNextGroupLevel(monthAnalysisVO);
                commonVO.setGroupLevel(map.get("nextGroupLevel").toString());
            } else {
                MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
                monthAnalysisVO.setViewFlag(commonVO.getViewFlag());
                monthAnalysisVO.setGroupLevel(commonVO.getGroupLevel());
                monthAnalysisVO.setGranularityType(commonVO.getGranularityType());
                monthAnalysisVO.setIndustryOrg(commonVO.getIndustryOrg());
                Map map = FcstIndexUtil.getNextGroupLevel(monthAnalysisVO);
                commonVO.setGroupLevel(map.get("nextGroupLevel").toString());
            }
        } else {
            commonVO.setExpandFlag("N");
        }
        // 重量级团队code,l1,l2，量纲code汇总
        customCommonService.getIndustryGroupCode(commonVO, allGroupCodeList, true);
        commonViewVO.setLv0Flag(commonVO.getLv0Flag());
        commonViewVO.setExpandFlag(commonVO.getExpandFlag());
        commonViewVO.setGroupLevel(commonVO.getGroupLevel());
        // 采购层级和制造层级汇总
        setProdTeamCodeList(commonViewVO, allGroupCodeList);
        customCommonService.getGroupCodeForPurchaseLevel(commonViewVO, allGroupCodeList, true);
        // 移除汇总组合中已有的记录
        removeListByCustomId(commonViewVO, allGroupCodeList);
        // 如果同时筛选groupLevel和关键字，需要在这里增加层级筛选
        if (StringUtils.isNotEmpty(commonViewVO.getKeyWord()) && StringUtils.isNotEmpty(commonViewVO.getFilterGroupLevel())) {
            allGroupCodeList.removeIf(all -> !commonViewVO.getFilterGroupLevel().equals(all.getGroupLevel()));
        }
        return allGroupCodeList;
    }

    private void setPermissionFlag(CommonViewVO commonViewVO, List<DmFocViewInfoVO> dmCustomCombList, List<DmFocViewInfoVO> allGroupLevelConditionList) {
        List<String> lv1NoPermissList = commonViewVO.getLv1NoPermissList();
        List<String> lv2NoPermissList = commonViewVO.getLv2NoPermissList();
        if (!(CollectionUtils.isEmpty(lv1NoPermissList) && CollectionUtils.isEmpty(lv2NoPermissList))) {
            dmCustomCombList.stream().forEach(dm -> {
                if (lv1NoPermissList.contains(dm.getGroupCode())) {
                    dm.setPermissionFlag("no");
                }
                if (lv2NoPermissList.contains(dm.getGroupCode())) {
                    dm.setPermissionFlag("no");
                }
            });
        }
        Map<String, List<DmFocViewInfoVO>> viewInfoMap = dmCustomCombList.stream().collect(Collectors.groupingBy(DmFocViewInfoVO::getGroupLevel));

        allGroupLevelCondition(commonViewVO, viewInfoMap, allGroupLevelConditionList);
    }

    private void setProdTeamCodeList(CommonViewVO commonViewVO, List<DmFocViewInfoVO> allGroupCodeList) {
        List<String> lv1CodeList = new ArrayList<>();
        List<String> lv2CodeList = new ArrayList<>();
        List<String> lv3CodeList = new ArrayList<>();
        for (DmFocViewInfoVO dmFocViewInfoVO : allGroupCodeList) {
            if (null != dmFocViewInfoVO.getLv1ProdRndTeamCode()) {
                lv1CodeList.add(dmFocViewInfoVO.getLv1ProdRndTeamCode());
            }
            if (null != dmFocViewInfoVO.getLv2ProdRndTeamCode()) {
                lv2CodeList.add(dmFocViewInfoVO.getLv2ProdRndTeamCode());
            }
            if (null != dmFocViewInfoVO.getLv3ProdRndTeamCode()) {
                lv3CodeList.add(dmFocViewInfoVO.getLv3ProdRndTeamCode());
            }
        }
        List<String> lv1ProdTeamCodeList = lv1CodeList.stream().distinct().collect(Collectors.toList());
        List<String> lv2ProdTeamCodeList = lv2CodeList.stream().distinct().collect(Collectors.toList());
        List<String> lv3ProdTeamCodeList = lv3CodeList.stream().distinct().collect(Collectors.toList());

        commonViewVO.setLv1CodeList(lv1ProdTeamCodeList);
        commonViewVO.setLv2CodeList(lv2ProdTeamCodeList);
        commonViewVO.setLv3CodeList(lv3ProdTeamCodeList);
    }

    private List<DmFocViewInfoVO> getParentCodeList(CommonViewVO commonViewVO, Map<String, List<DmFocViewInfoVO>> groupLevelMap, List<? extends DmFocViewInfoVO> dmCustomCombList, boolean flag) {
        setCommonViewVO(commonViewVO, dmCustomCombList, flag);
        if ("D".equals(commonViewVO.getGranularityType())) {
            Set<String> connectDimensionCodeList = new HashSet<>();
            if (flag) {
                dmCustomCombList.stream().forEach(dm -> {
                    String lv2ProdRndTeamCode = dm.getLv2ProdRndTeamCode() == null ? "" : dm.getLv2ProdRndTeamCode();
                    String lv3ProdRndTeamCode = dm.getLv3ProdRndTeamCode() == null ? "" : dm.getLv3ProdRndTeamCode();
                    String dimensionCode = dm.getDimensionCode() == null ? "" : dm.getDimensionCode();
                    String dimensionSubCategoryCode = dm.getDimensionSubCategoryCode() == null ? "" : dm.getDimensionSubCategoryCode();
                    String dimensionSubDetailCode = dm.getDimensionSubDetailCode() == null ? "" : dm.getDimensionSubDetailCode();
                    String spartCode = dm.getSpartCode() == null ? "" : dm.getSpartCode();
                    String connectDimensionCode;
                    if (Constant.StrEnum.ICT_ORG.getValue().equals(commonViewVO.getIndustryOrg())) {
                        connectDimensionCode = dm.getLv1ProdRndTeamCode() + "#*#" + lv2ProdRndTeamCode + "#*#" + lv3ProdRndTeamCode + "#*#" + dimensionCode + "#*#" + dimensionSubCategoryCode + "#*#" + dimensionSubDetailCode + "#*#" + spartCode;
                    } else if (Constant.StrEnum.ENERGY_ORG.getValue().equals(commonViewVO.getIndustryOrg())) {
                        String coaCode = dm.getCoaCode() == null ? "" : dm.getCoaCode();
                        connectDimensionCode = dm.getLv1ProdRndTeamCode() + "#*#" + lv2ProdRndTeamCode + "#*#" + lv3ProdRndTeamCode + "#*#" + coaCode + "#*#" + dimensionCode + "#*#" + dimensionSubCategoryCode + "#*#" + dimensionSubDetailCode + "#*#" + spartCode;
                    } else {
                        String lv4ProdRndTeamCode = dm.getLv4ProdRndTeamCode() == null ? "" : dm.getLv4ProdRndTeamCode();
                        connectDimensionCode = dm.getLv1ProdRndTeamCode() + "#*#" + lv2ProdRndTeamCode + "#*#" + lv3ProdRndTeamCode + "#*#" + lv4ProdRndTeamCode + "#*#" + dimensionCode + "#*#" + dimensionSubCategoryCode + "#*#" + dimensionSubDetailCode + "#*#" + spartCode;
                    }
                    connectDimensionCodeList.add(connectDimensionCode);
                });
                connectDimensionCodeList.remove(null);
                commonViewVO.setConnectDimensionCodeList(connectDimensionCodeList);
            }
        }
        List<DmFocViewInfoVO> allParentList = new ArrayList<>();

        for (Map.Entry<String, List<DmFocViewInfoVO>> groupLevelEntry : groupLevelMap.entrySet()) {
            String groupLevel = groupLevelEntry.getKey();
            Set<String> lv1ProdRndTeamCodeSet = groupLevelEntry.getValue().stream().map(DmFocViewInfoVO::getLv1ProdRndTeamCode).collect(Collectors.toSet());
            Set<String> lv2ProdRndTeamCodeSet = groupLevelEntry.getValue().stream().map(DmFocViewInfoVO::getLv2ProdRndTeamCode).collect(Collectors.toSet());
            Set<String> lv3ProdRndTeamCodeSet = groupLevelEntry.getValue().stream().map(DmFocViewInfoVO::getLv3ProdRndTeamCode).collect(Collectors.toSet());
            Set<String> lv4ProdRndTeamCodeSet = groupLevelEntry.getValue().stream().map(DmFocViewInfoVO::getLv4ProdRndTeamCode).collect(Collectors.toSet());
            List<String> groupCodeList = groupLevelEntry.getValue().stream().map(DmFocViewInfoVO::getGroupCode).distinct().collect(Collectors.toList());
            lv1ProdRndTeamCodeSet.remove(null);
            lv2ProdRndTeamCodeSet.remove(null);
            lv3ProdRndTeamCodeSet.remove(null);
            lv4ProdRndTeamCodeSet.remove(null);
            commonViewVO.setGroupCodeList(groupCodeList);
            commonViewVO.setLv1ProdRndTeamCodeSet(lv1ProdRndTeamCodeSet);
            commonViewVO.setLv2ProdRndTeamCodeSet(lv2ProdRndTeamCodeSet);
            commonViewVO.setLv3ProdRndTeamCodeSet(lv3ProdRndTeamCodeSet);
            commonViewVO.setLv4ProdRndTeamCodeSet(lv4ProdRndTeamCodeSet);
            // 根据当前group_level，获取上一层级grouplevel
            List<String> groupLevelList = new ArrayList<>();
            getAllNormalParentList(commonViewVO, groupLevel, groupLevelList, commonViewVO.getGranularityPageSymbol(), allParentList);
        }
        // 整体去重
        Set<String> prodTeamCodeGroupLevelSet = new HashSet<>();
        prodTeamCodeGroupLevelSet.add(GroupLevelEnumU.LV0.getValue());
        prodTeamCodeGroupLevelSet.add(GroupLevelEnumU.LV1.getValue());
        prodTeamCodeGroupLevelSet.add(GroupLevelEnumU.LV2.getValue());
        prodTeamCodeGroupLevelSet.add(GroupLevelEnumU.LV3.getValue());
        return allParentList.stream().filter(all -> prodTeamCodeGroupLevelSet.contains(all.getGroupLevel())).distinct().collect(Collectors.toList());
    }

    private void setCommonViewVO(CommonViewVO commonViewVO, List<? extends DmFocViewInfoVO> dmCustomCombList, boolean flag) {
        // 设置数据权限
        getPermissionForProdTeamCodeList(commonViewVO);
        commonViewVO.setLv0Flag("N");
        if (commonViewVO.getLv0DimensionSet().size() == 0 || !commonViewVO.getLv0DimensionSet().contains("NO_PERMISSION")) {
            commonViewVO.setLv0Flag("Y");
        }
        List<String> l1NameList = dmCustomCombList.stream().map(DmFocViewInfoVO::getL1Name).distinct().collect(Collectors.toList());
        List<String> l2NameList = dmCustomCombList.stream().map(DmFocViewInfoVO::getL2Name).distinct().collect(Collectors.toList());
        List<String> dimensionCodeList = dmCustomCombList.stream().map(DmFocViewInfoVO::getDimensionCode).distinct().collect(Collectors.toList());
        List<String> dimensionSubCategoryCodeList = dmCustomCombList.stream().map(DmFocViewInfoVO::getDimensionSubCategoryCode).distinct().collect(Collectors.toList());
        List<String> dimensionSubDetailCodeList = dmCustomCombList.stream().map(DmFocViewInfoVO::getDimensionSubDetailCode).distinct().collect(Collectors.toList());
        List<String> spartCodeList = dmCustomCombList.stream().map(DmFocViewInfoVO::getSpartCode).distinct().collect(Collectors.toList());
        List<String> coaCodeList = dmCustomCombList.stream().map(DmFocViewInfoVO::getCoaCode).distinct().collect(Collectors.toList());
        l1NameList.remove(null);
        l2NameList.remove(null);
        dimensionCodeList.remove(null);
        dimensionSubCategoryCodeList.remove(null);
        dimensionSubDetailCodeList.remove(null);
        spartCodeList.remove(null);
        coaCodeList.remove(null);
        commonViewVO.setL1NameList(l1NameList);
        commonViewVO.setL2NameList(l2NameList);
        commonViewVO.setDimensionCodeList(dimensionCodeList);
        commonViewVO.setDimensionSubcategoryCodeList(dimensionSubCategoryCodeList);
        commonViewVO.setDimensionSubDetailCodeList(dimensionSubDetailCodeList);
        commonViewVO.setSpartCodeList(spartCodeList);
        commonViewVO.setCoaCodeList(coaCodeList);
        // 设置sql是否需要筛选group_level
        commonViewVO.setIsGroupLevelFlag(flag);
        if (!flag) {
            List<String> shippingObjectList = dmCustomCombList.stream().map(DmFocViewInfoVO::getShippingObjectCode).distinct().collect(Collectors.toList());
            List<String> manufactureObjectCodeList = dmCustomCombList.stream().map(DmFocViewInfoVO::getManufactureObjectCode).distinct().collect(Collectors.toList());
            List<String> l3CegCodeList = dmCustomCombList.stream().map(DmFocViewInfoVO::getL3CegCode).distinct().collect(Collectors.toList());
            List<String> l4CegCodeList = dmCustomCombList.stream().map(DmFocViewInfoVO::getL4CegCode).distinct().collect(Collectors.toList());
            List<String> categoryCodeList = dmCustomCombList.stream().map(DmFocViewInfoVO::getCategoryCode).distinct().collect(Collectors.toList());
            shippingObjectList.remove(null);
            manufactureObjectCodeList.remove(null);
            l3CegCodeList.remove(null);
            l4CegCodeList.remove(null);
            categoryCodeList.remove(null);
            commonViewVO.setShippingObjectCodeList(shippingObjectList);
            commonViewVO.setManufactureObjectCodeList(manufactureObjectCodeList);
            commonViewVO.setL3CegCodeList(l3CegCodeList);
            commonViewVO.setL4CegCodeList(l4CegCodeList);
            commonViewVO.setCategoryCodeList(categoryCodeList);
        }
    }

    private void getAllNormalParentList(CommonViewVO commonViewVO, String groupLevel, List<String> groupLevelList, String granularityPageSymbol, List<DmFocViewInfoVO> allParentList) {
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
            getManufactureBeforeGroupLevel(groupLevel, commonViewVO, groupLevelList);
            // 查询数据库
            queryManufactureParentCodeListFromDB(commonViewVO, granularityPageSymbol, allParentList, groupLevelList);
        } else {
            getBeforeGroupLevel(groupLevel, commonViewVO, groupLevelList);
            // 查询数据库
            queryParentCodeListFromDB(commonViewVO, granularityPageSymbol, allParentList, groupLevelList);
        }
    }

    private void queryParentCodeListFromDB(CommonViewVO commonViewVO, String granularityPageSymbol, List<DmFocViewInfoVO> allParentList, List<String> groupLevelList) {
        List<String> subGroupCodeList = new ArrayList<>();
        for (String beforeGroupLevel : groupLevelList) {
            if (!"LV0".equals(beforeGroupLevel)) {
                List<DmFocViewInfoVO> parentList = new ArrayList<>();
                List<DmFocViewInfoVO> parentInfoList = new ArrayList<>();
                commonViewVO.setGroupLevel(beforeGroupLevel);
                switch (granularityPageSymbol) {
                    case "U_ANNUAL":
                        parentList = dmFocCustomCombDao.getParentCodeListGeneralAnnual(commonViewVO);
                        parentInfoList = parentList.stream().distinct().collect(Collectors.toList());
                        subGroupCodeList = parentInfoList.stream().map(DmFocViewInfoVO::getGroupCode).collect(Collectors.toList());
                        commonViewVO.setGroupCodeList(subGroupCodeList);
                        allParentList.addAll(parentInfoList);
                        break;
                    case "U_MONTH":
                        parentList = dmFocCustomCombDao.getParentCodeListGeneralMonth(commonViewVO);
                        parentInfoList = parentList.stream().distinct().collect(Collectors.toList());
                        subGroupCodeList = parentInfoList.stream().map(DmFocViewInfoVO::getGroupCode).collect(Collectors.toList());
                        commonViewVO.setGroupCodeList(subGroupCodeList);
                        allParentList.addAll(parentInfoList);
                        break;
                    case "P_ANNUAL":
                        parentList = dmFocCustomCombDao.getParentCodeListProfitAnnual(commonViewVO);
                        parentInfoList = parentList.stream().distinct().collect(Collectors.toList());
                        subGroupCodeList = parentInfoList.stream().map(DmFocViewInfoVO::getGroupCode).collect(Collectors.toList());
                        commonViewVO.setGroupCodeList(subGroupCodeList);
                        allParentList.addAll(parentInfoList);
                        break;
                    case "P_MONTH":
                        parentList = dmFocCustomCombDao.getParentCodeListProfitMonth(commonViewVO);
                        parentInfoList = parentList.stream().distinct().collect(Collectors.toList());
                        subGroupCodeList = parentInfoList.stream().map(DmFocViewInfoVO::getGroupCode).collect(Collectors.toList());
                        commonViewVO.setGroupCodeList(subGroupCodeList);
                        allParentList.addAll(parentInfoList);
                        break;
                    case "D_ANNUAL":
                        getDimensionAnnual(commonViewVO, allParentList);
                        break;
                    case "D_MONTH":
                        getDimensionMonth(commonViewVO, allParentList);
                        break;
                    default:
                        break;
                }
            }
        }
    }

    private void queryManufactureParentCodeListFromDB(CommonViewVO commonViewVO, String granularityPageSymbol, List<DmFocViewInfoVO> allParentList, List<String> groupLevelList) {
        List<String> subGroupCodeList = new ArrayList<>();
        for (String beforeGroupLevel : groupLevelList) {
            if (!"LV0".equals(beforeGroupLevel)) {
                List<DmFocViewInfoVO> parentList = new ArrayList<>();
                List<DmFocViewInfoVO> parentInfoList = new ArrayList<>();
                commonViewVO.setGroupLevel(beforeGroupLevel);
                switch (granularityPageSymbol) {
                    case "U_ANNUAL":
                        parentList = dmFocMadeCustomCombDao.getManuParentCodeListGeneralAnnual(commonViewVO);
                        parentInfoList = parentList.stream().distinct().collect(Collectors.toList());
                        subGroupCodeList = parentInfoList.stream().map(DmFocViewInfoVO::getGroupCode).collect(Collectors.toList());
                        commonViewVO.setGroupCodeList(subGroupCodeList);
                        allParentList.addAll(parentInfoList);
                        break;
                    case "U_MONTH":
                        parentList = dmFocMadeCustomCombDao.getManuParentCodeListGeneralMonth(commonViewVO);
                        parentInfoList = parentList.stream().distinct().collect(Collectors.toList());
                        subGroupCodeList = parentInfoList.stream().map(DmFocViewInfoVO::getGroupCode).collect(Collectors.toList());
                        commonViewVO.setGroupCodeList(subGroupCodeList);
                        allParentList.addAll(parentInfoList);
                        break;
                    case "P_ANNUAL":
                        parentList = dmFocMadeCustomCombDao.getManuParentCodeListProfitAnnual(commonViewVO);
                        parentInfoList = parentList.stream().distinct().collect(Collectors.toList());
                        subGroupCodeList = parentInfoList.stream().map(DmFocViewInfoVO::getGroupCode).collect(Collectors.toList());
                        commonViewVO.setGroupCodeList(subGroupCodeList);
                        allParentList.addAll(parentInfoList);
                        break;
                    case "P_MONTH":
                        parentList = dmFocMadeCustomCombDao.getManuParentCodeListProfitMonth(commonViewVO);
                        parentInfoList = parentList.stream().distinct().collect(Collectors.toList());
                        subGroupCodeList = parentInfoList.stream().map(DmFocViewInfoVO::getGroupCode).collect(Collectors.toList());
                        commonViewVO.setGroupCodeList(subGroupCodeList);
                        allParentList.addAll(parentInfoList);
                        break;
                    case "D_ANNUAL":
                        getManufactureDimensionAnnual(commonViewVO, allParentList);
                        break;
                    case "D_MONTH":
                        getManufactureDimensionMonth(commonViewVO, allParentList);
                        break;
                    default:
                        break;
                }
            }
        }
    }

    private void getDimensionAnnual(CommonViewVO commonViewVO, List<DmFocViewInfoVO> allParentList) {
        List<DmFocViewInfoVO> parentList = dmFocCustomCombDao.getParentCodeListDimensionAnnual(commonViewVO);
        List<DmFocViewInfoVO> parentInfoList = parentList.stream().distinct().collect(Collectors.toList());
        List<String> subGroupCodeList = parentInfoList.stream().map(DmFocViewInfoVO::getGroupCode).collect(Collectors.toList());
        commonViewVO.setGroupCodeList(subGroupCodeList);
        allParentList.addAll(parentInfoList);
    }

    private void getDimensionMonth(CommonViewVO commonViewVO, List<DmFocViewInfoVO> allParentList) {
        List<DmFocViewInfoVO> parentList = dmFocCustomCombDao.getParentCodeListDimensionMonth(commonViewVO);
        List<DmFocViewInfoVO> parentInfoList = parentList.stream().distinct().collect(Collectors.toList());
        List<String> subGroupCodeList = parentInfoList.stream().map(DmFocViewInfoVO::getGroupCode).collect(Collectors.toList());
        commonViewVO.setGroupCodeList(subGroupCodeList);
        allParentList.addAll(parentInfoList);
    }

    private void getManufactureDimensionAnnual(CommonViewVO commonViewVO, List<DmFocViewInfoVO> allParentList) {
        List<DmFocViewInfoVO> parentList = dmFocMadeCustomCombDao.getManuParentCodeListDimensionAnnual(commonViewVO);
        List<DmFocViewInfoVO> parentInfoList = parentList.stream().distinct().collect(Collectors.toList());
        List<String> subGroupCodeList = parentInfoList.stream().map(DmFocViewInfoVO::getGroupCode).collect(Collectors.toList());
        commonViewVO.setGroupCodeList(subGroupCodeList);
        allParentList.addAll(parentInfoList);
    }

    private void getManufactureDimensionMonth(CommonViewVO commonViewVO, List<DmFocViewInfoVO> allParentList) {
        List<DmFocViewInfoVO> parentList = dmFocMadeCustomCombDao.getManuParentCodeListDimensionMonth(commonViewVO);
        List<DmFocViewInfoVO> parentInfoList = parentList.stream().distinct().collect(Collectors.toList());
        List<String> subGroupCodeList = parentInfoList.stream().map(DmFocViewInfoVO::getGroupCode).collect(Collectors.toList());
        commonViewVO.setGroupCodeList(subGroupCodeList);
        allParentList.addAll(parentInfoList);
    }

    private void getBeforeGroupLevel(String beforeGroupLevel, CommonViewVO commonViewVO, List<String> groupLevelList) {
        String granularityType = commonViewVO.getGranularityType();
        String viewFlag = commonViewVO.getViewFlag();
        // 本身的groupLevel，且ICT不需要加入
        groupLevelList.add(beforeGroupLevel);
        if ("U".equals(granularityType)) {
            switch (viewFlag) {
                case "0":
                    getBeforeZeroGroupLevel(beforeGroupLevel, groupLevelList);
                    break;
                case "1":
                    getBeforeOneGroupLevel(beforeGroupLevel, groupLevelList);
                    break;
                case "2":
                    getBeforeUniversalTwo(beforeGroupLevel, groupLevelList);
                    break;
                case "3":
                    getBeforeUniversalThree(beforeGroupLevel, groupLevelList);
                    break;
                case "7":
                    getBeforeUniversalSeven(beforeGroupLevel, groupLevelList);
                    break;
                default:
                    break;
            }
        } else {
            getBeforeProfitAndDimensionGroupLevel(beforeGroupLevel, commonViewVO, groupLevelList);
        }
    }

    private void getBeforeZeroGroupLevel(String beforeGroupLevel, List<String> groupLevelList) {
        if ("CATEGORY".equals(beforeGroupLevel)) {
            groupLevelList.add("MODL");
            groupLevelList.add("CEG");
        }
        if ("MODL".equals(beforeGroupLevel)) {
            groupLevelList.add("CEG");
        }
    }

    private void getBeforeOneGroupLevel(String beforeGroupLevel, List<String> groupLevelList) {
        if ("CATEGORY".equals(beforeGroupLevel)) {
            groupLevelList.add("MODL");
            groupLevelList.add("CEG");
            groupLevelList.add("LV1");
        }
        if ("MODL".equals(beforeGroupLevel)) {
            groupLevelList.add("CEG");
            groupLevelList.add("LV1");
        }
        if ("CEG".equals(beforeGroupLevel)) {
            groupLevelList.add("LV1");
        }
    }

    private void getManufactureBeforeGroupLevel(String groupLevel, CommonViewVO commonViewVO, List<String> groupLevelList) {
        String granularityType = commonViewVO.getGranularityType();
        String viewFlag = commonViewVO.getViewFlag();
        // 本身的groupLevel，且ICT不需要加入
        groupLevelList.add(groupLevel);
        if ("U".equals(granularityType)) {
            switch (viewFlag) {
                case "0":
                    if ("MANUFACTURE_OBJECT".equals(groupLevel)) {
                        groupLevelList.add("SHIPPING_OBJECT");
                    }
                    break;
                case "1":
                    getManufactureUniversalOne(groupLevel, groupLevelList);
                    break;
                case "2":
                    getManufactureUniversalTwo(groupLevel, groupLevelList);
                    break;
                case "3":
                    getManufactureUniversalThree(groupLevel, groupLevelList);
                    break;
                case "7":
                    getIasUniversalSeven(groupLevel, groupLevelList);
                    break;
                default:
                    break;
            }
        } else {
            getManufactureProfitAndDimensionGroupLevel(groupLevel, commonViewVO, groupLevelList);
        }
    }

    private void getManufactureUniversalOne(String groupLevel, List<String> groupLevelList) {
        if ("MANUFACTURE_OBJECT".equals(groupLevel)) {
            groupLevelList.add("SHIPPING_OBJECT");
            groupLevelList.add("LV1");
        }
        if ("SHIPPING_OBJECT".equals(groupLevel)) {
            groupLevelList.add("LV1");
        }
    }

    private void getBeforeUniversalTwo(String groupLevel, List<String> groupLevelList) {
        if ("CATEGORY".equals(groupLevel)) {
            groupLevelList.add("MODL");
            groupLevelList.add("CEG");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("MODL".equals(groupLevel)) {
            groupLevelList.add("CEG");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("CEG".equals(groupLevel)) {
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("LV2".equals(groupLevel)) {
            groupLevelList.add("LV1");
        }
    }

    private void getManufactureUniversalTwo(String groupLevel, List<String> groupLevelList) {
        if ("MANUFACTURE_OBJECT".equals(groupLevel)) {
            groupLevelList.add("SHIPPING_OBJECT");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("SHIPPING_OBJECT".equals(groupLevel)) {
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("LV2".equals(groupLevel)) {
            groupLevelList.add("LV1");
        }
    }

    private void getBeforeUniversalThree(String threeGroupLevel, List<String> groupLevelList) {
        if ("CATEGORY".equals(threeGroupLevel)) {
            groupLevelList.add("MODL");
            groupLevelList.add("CEG");
            groupLevelList.add("LV3");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("MODL".equals(threeGroupLevel)) {
            groupLevelList.add("CEG");
            groupLevelList.add("LV3");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("CEG".equals(threeGroupLevel)) {
            groupLevelList.add("LV3");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("LV3".equals(threeGroupLevel)) {
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("LV2".equals(threeGroupLevel)) {
            groupLevelList.add("LV1");
        }
    }

    private void getBeforeUniversalSeven(String groupLevel, List<String> groupLevelList) {
        if ("CATEGORY".equals(groupLevel)) {
            groupLevelList.add("MODL");
            groupLevelList.add("CEG");
            prodRndTeamLevelIas(groupLevelList);
        }
        if ("MODL".equals(groupLevel)) {
            groupLevelList.add("CEG");
            prodRndTeamLevelIas(groupLevelList);
        }
        if ("CEG".equals(groupLevel)) {
            prodRndTeamLevelIas(groupLevelList);
        }
        if ("LV4".equals(groupLevel)) {
            prodRndTeamLevelLv3(groupLevelList);
        }
        if ("LV3".equals(groupLevel)) {
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("LV2".equals(groupLevel)) {
            groupLevelList.add("LV1");
        }
    }

    private void getManufactureUniversalThree(String manufacutreGroupLevel, List<String> groupLevelList) {
        if ("MANUFACTURE_OBJECT".equals(manufacutreGroupLevel)) {
            groupLevelList.add("SHIPPING_OBJECT");
            prodRndTeamLevelLv3(groupLevelList);
        }
        if ("SHIPPING_OBJECT".equals(manufacutreGroupLevel)) {
            prodRndTeamLevelLv3(groupLevelList);
        }
        getManufactureUniversalSub(manufacutreGroupLevel, groupLevelList);
    }

    private void getIasUniversalSeven(String manufacutreGroupLevel, List<String> groupLevelList) {
        if ("MANUFACTURE_OBJECT".equals(manufacutreGroupLevel)) {
            groupLevelList.add("SHIPPING_OBJECT");
            prodRndTeamLevelIas(groupLevelList);
        }
        if ("SHIPPING_OBJECT".equals(manufacutreGroupLevel)) {
            prodRndTeamLevelIas(groupLevelList);
        }
        if ("LV4".equals(manufacutreGroupLevel)) {
            prodRndTeamLevelLv3(groupLevelList);
        }
        getManufactureUniversalSub(manufacutreGroupLevel, groupLevelList);
    }

    private void prodRndTeamLevelIas(List<String> groupLevelList) {
        groupLevelList.add("LV4");
        prodRndTeamLevelLv3(groupLevelList);
    }

    private void prodRndTeamLevelLv3(List<String> groupLevelList) {
        groupLevelList.add("LV3");
        groupLevelList.add("LV2");
        groupLevelList.add("LV1");
    }

    private void getManufactureUniversalSub(String manufacutreGroupLevel, List<String> groupLevelList) {
        if ("LV3".equals(manufacutreGroupLevel)) {
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("LV2".equals(manufacutreGroupLevel)) {
            groupLevelList.add("LV1");
        }
    }

    private void getBeforeProfitAndDimensionGroupLevel(String beforeProGroupLevel, CommonViewVO commonViewVO, List<String> groupLevelList) {
        String granularityType = commonViewVO.getGranularityType();
        String viewFlag = commonViewVO.getViewFlag();
        if ("P".equals(granularityType)) {
            switch (viewFlag) {
                case "0":
                    getBeforeProfitSubZeroGroupLevel(beforeProGroupLevel, groupLevelList);
                    break;
                case "1":
                    getBeforeProfitSubOneGroupLevel(beforeProGroupLevel, groupLevelList);
                    break;
                case "2":
                    getBeforeProfitSubTwoGroupLevel(beforeProGroupLevel, groupLevelList);
                    break;
                case "3":
                    getBeforeProfitSubThreeGroupLevel(beforeProGroupLevel, groupLevelList);
                    break;
                case "4":
                    getBeforeProfitSubFourGroupLevel(beforeProGroupLevel, groupLevelList);
                    break;
                default:
                    break;
            }
        } else {
            getBeforeDimensionGroupLevel(beforeProGroupLevel, commonViewVO, groupLevelList);
        }
    }

    private void getBeforeProfitSubZeroGroupLevel(String beforeProGroupLevel, List<String> groupLevelList) {
        if ("CATEGORY".equals(beforeProGroupLevel)) {
            groupLevelList.add("MODL");
            groupLevelList.add("CEG");
        }
        if ("MODL".equals(beforeProGroupLevel)) {
            groupLevelList.add("CEG");
        }
    }

    private void getBeforeProfitSubOneGroupLevel(String beforeProGroupLevel, List<String> groupLevelList) {
        if ("CATEGORY".equals(beforeProGroupLevel)) {
            groupLevelList.add("MODL");
            groupLevelList.add("CEG");
            groupLevelList.add("LV1");
        }
        if ("MODL".equals(beforeProGroupLevel)) {
            groupLevelList.add("CEG");
            groupLevelList.add("LV1");
        }
        if ("CEG".equals(beforeProGroupLevel)) {
            groupLevelList.add("LV1");
        }
    }

    private void getManufactureProfitAndDimensionGroupLevel(String proGroupLevel, CommonViewVO commonViewVO, List<String> groupLevelList) {
        String granularityType = commonViewVO.getGranularityType();
        String viewFlag = commonViewVO.getViewFlag();
        if ("P".equals(granularityType)) {
            switch (viewFlag) {
                case "0":
                    if ("MANUFACTURE_OBJECT".equals(proGroupLevel)) {
                        groupLevelList.add("SHIPPING_OBJECT");
                    }
                    break;
                case "1":
                    getManufactureProfitSubOneGroupLevel(proGroupLevel, groupLevelList);
                    break;
                case "2":
                    getManufactureProfitSubTwoGroupLevel(proGroupLevel, groupLevelList);
                    break;
                case "3":
                    getManufactureProfitSubThreeGroupLevel(proGroupLevel, groupLevelList);
                    break;
                case "4":
                    getManufactureProfitSubFourGroupLevel(proGroupLevel, groupLevelList);
                    break;
                default:
                    break;
            }
        } else {
            getManufactureDimensionGroupLevel(proGroupLevel, commonViewVO, groupLevelList);
        }
    }

    private void getManufactureProfitSubOneGroupLevel(String proGroupLevel, List<String> groupLevelList) {
        if ("MANUFACTURE_OBJECT".equals(proGroupLevel)) {
            groupLevelList.add("SHIPPING_OBJECT");
            groupLevelList.add("LV1");
        }
        if ("SHIPPING_OBJECT".equals(proGroupLevel)) {
            groupLevelList.add("LV1");
        }
    }

    private void getManufactureProfitSubTwoGroupLevel(String proGroupLevel, List<String> groupLevelList) {
        if ("MANUFACTURE_OBJECT".equals(proGroupLevel)) {
            groupLevelList.add("SHIPPING_OBJECT");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("SHIPPING_OBJECT".equals(proGroupLevel)) {
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("LV2".equals(proGroupLevel)) {
            groupLevelList.add("LV1");
        }
    }

    private void getBeforeProfitSubTwoGroupLevel(String beforeProGroupLevel, List<String> groupLevelList) {
        if ("CATEGORY".equals(beforeProGroupLevel)) {
            groupLevelList.add("MODL");
            groupLevelList.add("CEG");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("MODL".equals(beforeProGroupLevel)) {
            groupLevelList.add("CEG");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("CEG".equals(beforeProGroupLevel)) {
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("LV2".equals(beforeProGroupLevel)) {
            groupLevelList.add("LV1");
        }
    }

    private void getBeforeProfitSubThreeGroupLevel(String groupLevel, List<String> groupLevelList) {
        if ("CATEGORY".equals(groupLevel)) {
            groupLevelList.add("MODL");
            groupLevelList.add("CEG");
            groupLevelList.add("L1");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("MODL".equals(groupLevel)) {
            groupLevelList.add("CEG");
            groupLevelList.add("L1");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("CEG".equals(groupLevel)) {
            groupLevelList.add("L1");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("L1".equals(groupLevel)) {
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("LV2".equals(groupLevel)) {
            groupLevelList.add("LV1");
        }
    }

    private void getManufactureProfitSubThreeGroupLevel(String manuGroupLevel, List<String> beforeGroupLevelList) {
        if ("MANUFACTURE_OBJECT".equals(manuGroupLevel)) {
            beforeGroupLevelList.add("SHIPPING_OBJECT");
            beforeGroupLevelList.add("L1");
            beforeGroupLevelList.add("LV2");
            beforeGroupLevelList.add("LV1");
        }
        if ("SHIPPING_OBJECT".equals(manuGroupLevel)) {
            beforeGroupLevelList.add("L1");
            beforeGroupLevelList.add("LV2");
            beforeGroupLevelList.add("LV1");
        }
        if ("L1".equals(manuGroupLevel)) {
            beforeGroupLevelList.add("LV2");
            beforeGroupLevelList.add("LV1");
        }
        if ("LV2".equals(manuGroupLevel)) {
            beforeGroupLevelList.add("LV1");
        }
    }

    private void getManufactureProfitSubFourGroupLevel(String fourGroupLevel, List<String> groupLevelList) {
        if ("MANUFACTURE_OBJECT".equals(fourGroupLevel)) {
            groupLevelList.add("SHIPPING_OBJECT");
            groupLevelList.add("L2");
            groupLevelList.add("L1");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("SHIPPING_OBJECT".equals(fourGroupLevel)) {
            groupLevelList.add("L2");
            groupLevelList.add("L1");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("L2".equals(fourGroupLevel)) {
            groupLevelList.add("L1");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("L1".equals(fourGroupLevel)) {
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("LV2".equals(fourGroupLevel)) {
            groupLevelList.add("LV1");
        }
    }

    private void getBeforeProfitSubFourGroupLevel(String fourGroupLevel, List<String> beforeGroupLevelList) {
        if ("CATEGORY".equals(fourGroupLevel)) {
            beforeGroupLevelList.add("MODL");
            beforeGroupLevelList.add("CEG");
            beforeGroupLevelList.add("L2");
            beforeGroupLevelList.add("L1");
            beforeGroupLevelList.add("LV2");
            beforeGroupLevelList.add("LV1");
        }
        if ("MODL".equals(fourGroupLevel)) {
            beforeGroupLevelList.add("CEG");
            beforeGroupLevelList.add("L2");
            beforeGroupLevelList.add("L1");
            beforeGroupLevelList.add("LV2");
            beforeGroupLevelList.add("LV1");
        }
        if ("CEG".equals(fourGroupLevel)) {
            beforeGroupLevelList.add("L2");
            beforeGroupLevelList.add("L1");
            beforeGroupLevelList.add("LV2");
            beforeGroupLevelList.add("LV1");
        }
        if ("L2".equals(fourGroupLevel)) {
            beforeGroupLevelList.add("L1");
            beforeGroupLevelList.add("LV2");
            beforeGroupLevelList.add("LV1");
        }
        if ("L1".equals(fourGroupLevel)) {
            beforeGroupLevelList.add("LV2");
            beforeGroupLevelList.add("LV1");
        }
        if ("LV2".equals(fourGroupLevel)) {
            beforeGroupLevelList.add("LV1");
        }
    }

    private void getBeforeDimensionGroupLevel(String groupLevel, CommonViewVO commonViewVO, List<String> groupLevelList) {
        String granularityType = commonViewVO.getGranularityType();
        String viewFlag = commonViewVO.getViewFlag();
        if ("D".equals(granularityType)) {
            switch (viewFlag) {
                case "0":
                    if ("CATEGORY".equals(groupLevel)) {
                        groupLevelList.add("MODL");
                        groupLevelList.add("CEG");
                        groupLevelList.add("DIMENSION");
                        groupLevelList.add("LV1");
                    }
                    if ("MODL".equals(groupLevel)) {
                        groupLevelList.add("CEG");
                        groupLevelList.add("DIMENSION");
                        groupLevelList.add("LV1");
                    }
                    if ("CEG".equals(groupLevel)) {
                        groupLevelList.add("DIMENSION");
                        groupLevelList.add("LV1");
                    }
                    if ("DIMENSION".equals(groupLevel)) {
                        groupLevelList.add("LV1");
                    }
                    break;
                case "1":
                    beforeDimensionOneGroupLevel(groupLevel, groupLevelList);
                    break;
                case "2":
                    beforeDimensionTwoGroupLevel(groupLevel, groupLevelList);
                    break;
                default:
                    break;
            }
            getFrontDimensionGroupLevel(groupLevel, commonViewVO, groupLevelList);
            getBeforeSubDimensionGroupLevel(groupLevel, commonViewVO, groupLevelList);
        }
    }

    private void getManufactureDimensionGroupLevel(String groupLevel, CommonViewVO commonViewVO, List<String> groupLevelList) {
        String granularityType = commonViewVO.getGranularityType();
        String viewFlag = commonViewVO.getViewFlag();
        if ("D".equals(granularityType)) {
            switch (viewFlag) {
                case "0":
                    if ("MANUFACTURE_OBJECT".equals(groupLevel)) {
                        groupLevelList.add("SHIPPING_OBJECT");
                        groupLevelList.add("DIMENSION");
                        groupLevelList.add("LV1");
                    }
                    if ("SHIPPING_OBJECT".equals(groupLevel)) {
                        groupLevelList.add("DIMENSION");
                        groupLevelList.add("LV1");
                    }
                    if ("DIMENSION".equals(groupLevel)) {
                        groupLevelList.add("LV1");
                    }
                    break;
                case "1":
                    manufactureDimensionOneGroupLevel(groupLevel, groupLevelList);
                    break;
                case "2":
                    manufactureDimensionTwoGroupLevel(groupLevel, groupLevelList);
                    break;
                default:
                    break;
            }
            getFrontManufactureDimensionGroupLevel(groupLevel, commonViewVO, groupLevelList);
            getBeforeManuDimensionGroupLevel(groupLevel, commonViewVO, groupLevelList);
        }
    }

    private void beforeDimensionOneGroupLevel(String groupLevel, List<String> groupLevelList) {
        if ("CATEGORY".equals(groupLevel)) {
            groupLevelList.add("MODL");
            groupLevelList.add("CEG");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV1");
        }
        if ("MODL".equals(groupLevel)) {
            groupLevelList.add("CEG");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV1");
        }
        if ("CEG".equals(groupLevel)) {
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV1");
        }
        getSubCategoryGroupLevel(groupLevel, groupLevelList);
    }

    private void getSubCategoryGroupLevel(String groupLevel, List<String> groupLevelList) {
        if ("SUBCATEGORY".equals(groupLevel)) {
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV1");
        }
        if ("DIMENSION".equals(groupLevel)) {
            groupLevelList.add("LV1");
        }
    }

    private void manufactureDimensionOneGroupLevel(String groupLevel, List<String> groupLevelList) {
        if ("MANUFACTURE_OBJECT".equals(groupLevel)) {
            groupLevelList.add("SHIPPING_OBJECT");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV1");
        }
        if ("SHIPPING_OBJECT".equals(groupLevel)) {
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV1");
        }
        getSubCategoryGroupLevel(groupLevel, groupLevelList);
    }

    private void beforeDimensionTwoGroupLevel(String groupLevel, List<String> groupLevelList) {
        if ("CATEGORY".equals(groupLevel)) {
            groupLevelList.add("MODL");
            groupLevelList.add("CEG");
            groupLevelList.add("SUB_DETAIL");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV1");
        }
        if ("MODL".equals(groupLevel)) {
            groupLevelList.add("CEG");
            groupLevelList.add("SUB_DETAIL");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV1");
        }
        if ("CEG".equals(groupLevel)) {
            groupLevelList.add("SUB_DETAIL");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV1");
        }
        if ("SUB_DETAIL".equals(groupLevel)) {
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV1");
        }
        getSubCategoryGroupLevel(groupLevel, groupLevelList);
    }

    private void manufactureDimensionTwoGroupLevel(String groupLevel, List<String> beforeGroupLevelList) {
        if ("MANUFACTURE_OBJECT".equals(groupLevel)) {
            beforeGroupLevelList.add("SHIPPING_OBJECT");
            beforeGroupLevelList.add("SUB_DETAIL");
            beforeGroupLevelList.add("SUBCATEGORY");
            beforeGroupLevelList.add("DIMENSION");
            beforeGroupLevelList.add("LV1");
        }
        if ("SHIPPING_OBJECT".equals(groupLevel)) {
            beforeGroupLevelList.add("SUB_DETAIL");
            beforeGroupLevelList.add("SUBCATEGORY");
            beforeGroupLevelList.add("DIMENSION");
            beforeGroupLevelList.add("LV1");
        }
        if ("SUB_DETAIL".equals(groupLevel)) {
            beforeGroupLevelList.add("SUBCATEGORY");
            beforeGroupLevelList.add("DIMENSION");
            beforeGroupLevelList.add("LV1");
        }
        getSubCategoryGroupLevel(groupLevel, beforeGroupLevelList);
    }

    private void getFrontDimensionGroupLevel(String frontGroupLevel, CommonViewVO commonViewVO, List<String> groupLevelList) {
        String granularityType = commonViewVO.getGranularityType();
        String viewFlag = commonViewVO.getViewFlag();
        if ("D".equals(granularityType)) {
            switch (viewFlag) {
                case "3":
                    purchaseGroupLevel(frontGroupLevel, groupLevelList);
                    if ("DIMENSION".equals(frontGroupLevel)) {
                        groupLevelList.add("LV2");
                        groupLevelList.add("LV1");
                    }
                    if ("LV2".equals(frontGroupLevel)) {
                        groupLevelList.add("LV1");
                    }
                    break;
                case "4":
                    getBeforeDimensionFourGroupLevel(frontGroupLevel, groupLevelList);
                    break;
                case "5":
                    getBeforeDimensionFiveGroupLevel(frontGroupLevel, groupLevelList);
                    break;
                default:
                    break;
            }
        }
    }

    private void purchaseGroupLevel(String frontGroupLevel, List<String> groupLevelList) {
        if ("CATEGORY".equals(frontGroupLevel)) {
            groupLevelList.add("MODL");
            groupLevelList.add("CEG");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("MODL".equals(frontGroupLevel)) {
            groupLevelList.add("CEG");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("CEG".equals(frontGroupLevel)) {
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
    }

    private void purchaseNineGroupLevel(String frontGroupLevel, List<String> groupLevelList) {
        if ("CATEGORY".equals(frontGroupLevel)) {
            groupLevelList.add("MODL");
            groupLevelList.add("CEG");
            groupLevelList.add("SPART");
            groupLevelList.add("SUB_DETAIL");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV1");
        }
        if ("MODL".equals(frontGroupLevel)) {
            groupLevelList.add("CEG");
            groupLevelList.add("SPART");
            groupLevelList.add("SUB_DETAIL");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV1");
        }
        if ("CEG".equals(frontGroupLevel)) {
            groupLevelList.add("SPART");
            groupLevelList.add("SUB_DETAIL");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV1");
        }
        purchaseThreeSubGroupLevel(frontGroupLevel, groupLevelList);
    }

    private void purchaseThreeSubGroupLevel(String frontGroupLevel, List<String> groupLevelList) {
        if ("SPART".equals(frontGroupLevel)) {
            groupLevelList.add("SUB_DETAIL");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV1");
        }
        if ("SUB_DETAIL".equals(frontGroupLevel)) {
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV1");
        }
        getSubCategoryGroupLevel(frontGroupLevel, groupLevelList);
    }

    private void getFrontManufactureDimensionGroupLevel(String beforeGroupLevel, CommonViewVO commonViewVO, List<String> groupLevelList) {
        String granularityType = commonViewVO.getGranularityType();
        String viewFlag = commonViewVO.getViewFlag();
        if ("D".equals(granularityType)) {
            switch (viewFlag) {
                case "3":
                    getBeforeManuDimensionThreeGroupLevel(beforeGroupLevel, groupLevelList);
                    break;
                case "4":
                    getBeforeManuDimensionFourGroupLevel(beforeGroupLevel, groupLevelList);
                    break;
                case "5":
                    getBeforeManuDimensionFiveGroupLevel(beforeGroupLevel, groupLevelList);
                    break;
                case "6":
                    if ("MANUFACTURE_OBJECT".equals(beforeGroupLevel)) {
                        groupLevelList.add("SHIPPING_OBJECT");
                        groupLevelList.add("DIMENSION");
                        groupLevelList.add("LV3");
                        groupLevelList.add("LV2");
                        groupLevelList.add("LV1");
                    }
                    if ("SHIPPING_OBJECT".equals(beforeGroupLevel)) {
                        groupLevelList.add("DIMENSION");
                        groupLevelList.add("LV3");
                        groupLevelList.add("LV2");
                        groupLevelList.add("LV1");
                    }
                    getBeforeManuDimensionSixGroupLevel(beforeGroupLevel, groupLevelList);
                    break;
                default:
                    break;
            }
        }
    }

    private void getBeforeManuDimensionNineGroupLevel(String beforeGroupLevel, List<String> groupLevelList) {
        if ("MANUFACTURE_OBJECT".equals(beforeGroupLevel)) {
            groupLevelList.add("SHIPPING_OBJECT");
            groupLevelList.add("SPART");
            groupLevelList.add("SUB_DETAIL");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV1");
        }
        if ("SHIPPING_OBJECT".equals(beforeGroupLevel)) {
            groupLevelList.add("SPART");
            groupLevelList.add("SUB_DETAIL");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV1");
        }
        if ("SPART".equals(beforeGroupLevel)) {
            groupLevelList.add("SUB_DETAIL");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV1");
        }
        if ("SUB_DETAIL".equals(beforeGroupLevel)) {
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV1");
        }
        getSubCategoryGroupLevel(beforeGroupLevel, groupLevelList);
    }

    private void getBeforeManuDimensionThreeGroupLevel(String beforeGroupLevel, List<String> groupLevelList) {
        if ("MANUFACTURE_OBJECT".equals(beforeGroupLevel)) {
            groupLevelList.add("SHIPPING_OBJECT");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("SHIPPING_OBJECT".equals(beforeGroupLevel)) {
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("DIMENSION".equals(beforeGroupLevel)) {
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("LV2".equals(beforeGroupLevel)) {
            groupLevelList.add("LV1");
        }
    }

    private void getBeforeDimensionFourGroupLevel(String groupLevel, List<String> groupLevelList) {
        if ("CATEGORY".equals(groupLevel)) {
            groupLevelList.add("MODL");
            groupLevelList.add("CEG");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("MODL".equals(groupLevel)) {
            groupLevelList.add("CEG");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("CEG".equals(groupLevel)) {
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        subDimensionMethod(groupLevel, groupLevelList);
    }

    private void getBeforeManuDimensionFourGroupLevel(String groupLevel, List<String> groupLevelList) {
        if ("MANUFACTURE_OBJECT".equals(groupLevel)) {
            groupLevelList.add("SHIPPING_OBJECT");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("SHIPPING_OBJECT".equals(groupLevel)) {
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        subDimensionMethod(groupLevel, groupLevelList);
    }

    private void subDimensionMethod(String groupLevel, List<String> groupLevelList) {
        if ("SUBCATEGORY".equals(groupLevel)) {
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("DIMENSION".equals(groupLevel)) {
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("LV2".equals(groupLevel)) {
            groupLevelList.add("LV1");
        }
    }

    private void getBeforeDimensionFiveGroupLevel(String groupLevel, List<String> groupLevelList) {
        if ("CATEGORY".equals(groupLevel)) {
            groupLevelList.add("MODL");
            groupLevelList.add("CEG");
            groupLevelList.add("SUB_DETAIL");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("MODL".equals(groupLevel)) {
            groupLevelList.add("CEG");
            groupLevelList.add("SUB_DETAIL");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("CEG".equals(groupLevel)) {
            groupLevelList.add("SUB_DETAIL");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("SUB_DETAIL".equals(groupLevel)) {
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        subDimensionMethod(groupLevel, groupLevelList);
    }

    private void getBeforeDimensionTenSubGroupLevel(String groupLevel, List<String> groupLevelList) {
        if ("CATEGORY".equals(groupLevel)) {
            groupLevelList.add("MODL");
            groupLevelList.add("CEG");
            groupLevelList.add("SPART");
            groupLevelList.add("SUB_DETAIL");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("MODL".equals(groupLevel)) {
            groupLevelList.add("CEG");
            groupLevelList.add("SPART");
            groupLevelList.add("SUB_DETAIL");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("CEG".equals(groupLevel)) {
            groupLevelList.add("SPART");
            groupLevelList.add("SUB_DETAIL");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("SPART".equals(groupLevel)) {
            groupLevelList.add("SUB_DETAIL");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("SUB_DETAIL".equals(groupLevel)) {
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        subDimensionMethod(groupLevel, groupLevelList);
    }

    private void getBeforeDimensionElevenGroupLevel(String groupLevel, List<String> groupLevelList) {
        if ("CATEGORY".equals(groupLevel)) {
            groupLevelList.add("MODL");
            groupLevelList.add("CEG");
            groupLevelList.add("SPART");
            groupLevelList.add("SUB_DETAIL");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV3");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("MODL".equals(groupLevel)) {
            groupLevelList.add("CEG");
            groupLevelList.add("SPART");
            groupLevelList.add("SUB_DETAIL");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV3");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("CEG".equals(groupLevel)) {
            groupLevelList.add("SPART");
            groupLevelList.add("SUB_DETAIL");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV3");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        getBeforeDimensionElevenGroupLevelOther(groupLevel, groupLevelList);
    }

    private void getBeforeDimensionTwelveGroupLevel(String groupLevel, List<String> coaGroupLevelList) {
        if ("CATEGORY".equals(groupLevel)) {
            coaGroupLevelList.add("MODL");
            coaGroupLevelList.add("CEG");
            coaGroupLevelList.add("SPART");
            coaGroupLevelList.add("SUB_DETAIL");
            coaGroupLevelList.add("SUBCATEGORY");
            coaGroupLevelList.add("DIMENSION");
            coaGroupLevelList.add("COA");
            coaGroupLevelList.add("LV3");
            coaGroupLevelList.add("LV2");
            coaGroupLevelList.add("LV1");
        }
        if ("MODL".equals(groupLevel)) {
            coaGroupLevelList.add("CEG");
            coaGroupLevelList.add("SPART");
            coaGroupLevelList.add("SUB_DETAIL");
            coaGroupLevelList.add("SUBCATEGORY");
            coaGroupLevelList.add("DIMENSION");
            coaGroupLevelList.add("COA");
            coaGroupLevelList.add("LV3");
            coaGroupLevelList.add("LV2");
            coaGroupLevelList.add("LV1");
        }
        if ("CEG".equals(groupLevel)) {
            coaGroupLevelList.add("SPART");
            coaGroupLevelList.add("SUB_DETAIL");
            coaGroupLevelList.add("SUBCATEGORY");
            coaGroupLevelList.add("DIMENSION");
            coaGroupLevelList.add("COA");
            coaGroupLevelList.add("LV3");
            coaGroupLevelList.add("LV2");
            coaGroupLevelList.add("LV1");
        }
        getBeforeDimensionTwelveGroupLevelOther(groupLevel, coaGroupLevelList);
    }

    private void getBeforeDimensionTwelveGroupLevelSub(String groupLevel, List<String> coaGroupLevelList) {
        if ("CATEGORY".equals(groupLevel)) {
            coaGroupLevelList.add("MODL");
            coaGroupLevelList.add("CEG");
            commonIasDimension(coaGroupLevelList);
        }
        if ("MODL".equals(groupLevel)) {
            coaGroupLevelList.add("CEG");
            commonIasDimension(coaGroupLevelList);
        }
        if ("CEG".equals(groupLevel)) {
            commonIasDimension(coaGroupLevelList);
        }
        getBeforeDimensionTwelveGroupLevelIas(groupLevel, coaGroupLevelList);
    }

    private void getBeforeDimensionTwelveGroupLevelIas(String groupLevel, List<String> groupLevelList) {

        getBeforeDimensionTwelveCommonGroupLevel(groupLevel, groupLevelList);
        getBeforeDimensionTwelveIasGroupLevel(groupLevel, groupLevelList);
    }

    private void getBeforeDimensionTwelveGroupLevelOther(String groupLevel, List<String> groupLevelList) {
        if ("SPART".equals(groupLevel)) {
            groupLevelList.add("SUB_DETAIL");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("COA");
            groupLevelList.add("LV3");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("SUB_DETAIL".equals(groupLevel)) {
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("COA");
            groupLevelList.add("LV3");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("SUBCATEGORY".equals(groupLevel)) {
            groupLevelList.add("DIMENSION");
            groupLevelList.add("COA");
            groupLevelList.add("LV3");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("DIMENSION".equals(groupLevel)) {
            groupLevelList.add("COA");
            groupLevelList.add("LV3");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("COA".equals(groupLevel)) {
            groupLevelList.add("LV3");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("LV3".equals(groupLevel)) {
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("LV2".equals(groupLevel)) {
            groupLevelList.add("LV1");
        }
    }

    private void getBeforeDimensionElevenGroupLevelOther(String groupLevel, List<String> groupLevelList) {
        if ("SPART".equals(groupLevel)) {
            groupLevelList.add("SUB_DETAIL");
            groupLevelList.add("SUBCATEGORY");
            getBeforeDimensionLevel(groupLevelList);
        }
        if ("SUB_DETAIL".equals(groupLevel)) {
            groupLevelList.add("SUBCATEGORY");
            getBeforeDimensionLevel(groupLevelList);
        }
        if ("SUBCATEGORY".equals(groupLevel)) {
            getBeforeDimensionLevel(groupLevelList);
        }
        if ("DIMENSION".equals(groupLevel)) {
            groupLevelList.add("LV3");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("LV3".equals(groupLevel)) {
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("LV2".equals(groupLevel)) {
            groupLevelList.add("LV1");
        }
    }

    private void getBeforeDimensionLevel(List<String> dimensionLevelList) {
        dimensionLevelList.add("DIMENSION");
        dimensionLevelList.add("LV3");
        dimensionLevelList.add("LV2");
        dimensionLevelList.add("LV1");
    }

    private void getBeforeManuDimensionFiveGroupLevel(String groupLevel, List<String> manuGroupLevelList) {
        if ("MANUFACTURE_OBJECT".equals(groupLevel)) {
            manuGroupLevelList.add("SHIPPING_OBJECT");
            manuGroupLevelList.add("SUB_DETAIL");
            getManuDimensionGroupLevel(manuGroupLevelList);
        }
        if ("SHIPPING_OBJECT".equals(groupLevel)) {
            manuGroupLevelList.add("SUB_DETAIL");
            getManuDimensionGroupLevel(manuGroupLevelList);
        }
        getDimensionGroupLevel(groupLevel, manuGroupLevelList);
    }

    private void getDimensionGroupLevel(String groupLevel, List<String> manuGroupLevelList) {
        if ("SUB_DETAIL".equals(groupLevel)) {
            getManuDimensionGroupLevel(manuGroupLevelList);
        }
        if ("SUBCATEGORY".equals(groupLevel)) {
            manuGroupLevelList.add("DIMENSION");
            manuGroupLevelList.add("LV2");
            manuGroupLevelList.add("LV1");
        }
        if ("DIMENSION".equals(groupLevel)) {
            manuGroupLevelList.add("LV2");
            manuGroupLevelList.add("LV1");
        }
        if ("LV2".equals(groupLevel)) {
            manuGroupLevelList.add("LV1");
        }
    }

    private void getManuDimensionGroupLevel(List<String> madeGroupLevelList) {
        madeGroupLevelList.add("SUBCATEGORY");
        madeGroupLevelList.add("DIMENSION");
        madeGroupLevelList.add("LV2");
        madeGroupLevelList.add("LV1");
    }

    private void getBeforeManuDimensionTenGroupLevel(String groupLevel, List<String> manuGroupLevelList) {
        if ("MANUFACTURE_OBJECT".equals(groupLevel)) {
            manuGroupLevelList.add("SHIPPING_OBJECT");
            manuGroupLevelList.add("SPART");
            manuGroupLevelList.add("SUB_DETAIL");
            manuGroupLevelList.add("SUBCATEGORY");
            manuGroupLevelList.add("DIMENSION");
            manuGroupLevelList.add("LV2");
            manuGroupLevelList.add("LV1");
        }
        if ("SHIPPING_OBJECT".equals(groupLevel)) {
            manuGroupLevelList.add("SPART");
            manuGroupLevelList.add("SUB_DETAIL");
            manuGroupLevelList.add("SUBCATEGORY");
            manuGroupLevelList.add("DIMENSION");
            manuGroupLevelList.add("LV2");
            manuGroupLevelList.add("LV1");
        }
        if ("SPART".equals(groupLevel)) {
            manuGroupLevelList.add("SUB_DETAIL");
            manuGroupLevelList.add("SUBCATEGORY");
            manuGroupLevelList.add("DIMENSION");
            manuGroupLevelList.add("LV2");
            manuGroupLevelList.add("LV1");
        }
        getDimensionGroupLevel(groupLevel, manuGroupLevelList);
    }

    private void getBeforeSubDimensionGroupLevel(String beforeSubGroupLevel, CommonViewVO commonViewVO, List<String> groupLevelList) {
        String viewFlag = commonViewVO.getViewFlag();
        switch (viewFlag) {
            case "6":
                getBeforPurchaseGroupLevel(beforeSubGroupLevel, groupLevelList);
                if ("DIMENSION".equals(beforeSubGroupLevel)) {
                    groupLevelList.add("LV3");
                    groupLevelList.add("LV2");
                    groupLevelList.add("LV1");
                }
                if ("LV3".equals(beforeSubGroupLevel)) {
                    groupLevelList.add("LV2");
                    groupLevelList.add("LV1");
                }
                if ("LV2".equals(beforeSubGroupLevel)) {
                    groupLevelList.add("LV1");
                }
                break;
            case "7":
                getBeforeDimensionSevenGroupLevel(beforeSubGroupLevel, groupLevelList);
                break;
            case "8":
                getBeforeDimensionEightGroupLevel(beforeSubGroupLevel, groupLevelList);
                break;
            case "9":
                purchaseNineGroupLevel(beforeSubGroupLevel, groupLevelList);
                break;
            case "10":
                getBeforeDimensionTenSubGroupLevel(beforeSubGroupLevel, groupLevelList);
                break;
            case "11":
                getBeforeDimensionElevenGroupLevel(beforeSubGroupLevel, groupLevelList);
                break;
            case "12":
                if (IndustryConst.INDUSTRY_ORG.ENERGY.getValue().equals(commonViewVO.getIndustryOrg())) {
                    getBeforeDimensionTwelveGroupLevel(beforeSubGroupLevel, groupLevelList);
                } else {
                    getBeforeDimensionTwelveGroupLevelSub(beforeSubGroupLevel, groupLevelList);
                }
                break;
            default:
                break;
        }
    }

    private void getBeforPurchaseGroupLevel(String beforeSubGroupLevel, List<String> groupLevelList) {
        if ("CATEGORY".equals(beforeSubGroupLevel)) {
            groupLevelList.add("MODL");
            groupLevelList.add("CEG");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV3");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("MODL".equals(beforeSubGroupLevel)) {
            groupLevelList.add("CEG");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV3");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("CEG".equals(beforeSubGroupLevel)) {
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV3");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
    }

    private void getBeforeManuDimensionGroupLevel(String beforeSubGroupLevel, CommonViewVO commonViewVO, List<String> groupLevelList) {
        String viewFlag = commonViewVO.getViewFlag();
        switch (viewFlag) {
            case "7":
                getBeforeManuDimensionSevenGroupLevel(beforeSubGroupLevel, groupLevelList);
                break;
            case "8":
                getBeforeManuDimensionEightGroupLevel(beforeSubGroupLevel, groupLevelList);
                break;
            case "9":
                getBeforeManuDimensionNineGroupLevel(beforeSubGroupLevel, groupLevelList);
                break;
            case "10":
                getBeforeManuDimensionTenGroupLevel(beforeSubGroupLevel, groupLevelList);
                break;
            case "11":
                getBeforeManuDimensionElevenGroupLevel(beforeSubGroupLevel, groupLevelList);
                break;
            case "12":
                if (IndustryConst.INDUSTRY_ORG.ENERGY.getValue().equals(commonViewVO.getIndustryOrg())) {
                    getBeforeManuDimensionTwelveGroupLevel(beforeSubGroupLevel, groupLevelList);
                } else {
                    getBeforeIasDimensionTwelveGroupLevel(beforeSubGroupLevel, groupLevelList);
                }
                break;
            default:
                break;
        }
    }

    private void getBeforeManuDimensionSixGroupLevel(String beforeGroupLevel, List<String> manuDimensionGroupLevelList) {
        if ("DIMENSION".equals(beforeGroupLevel)) {
            manuDimensionGroupLevelList.add("LV3");
            manuDimensionGroupLevelList.add("LV2");
            manuDimensionGroupLevelList.add("LV1");
        }
        if ("LV3".equals(beforeGroupLevel)) {
            manuDimensionGroupLevelList.add("LV2");
            manuDimensionGroupLevelList.add("LV1");
        }
        if ("LV2".equals(beforeGroupLevel)) {
            manuDimensionGroupLevelList.add("LV1");
        }
    }

    private void getBeforeDimensionSevenGroupLevel(String groupLevel, List<String> groupLevelList) {
        if ("CATEGORY".equals(groupLevel)) {
            groupLevelList.add("MODL");
            groupLevelList.add("CEG");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV3");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("MODL".equals(groupLevel)) {
            groupLevelList.add("CEG");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV3");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("CEG".equals(groupLevel)) {
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV3");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("SUBCATEGORY".equals(groupLevel)) {
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV3");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("DIMENSION".equals(groupLevel)) {
            groupLevelList.add("LV3");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("LV3".equals(groupLevel)) {
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("LV2".equals(groupLevel)) {
            groupLevelList.add("LV1");
        }
    }

    private void getBeforeManuDimensionSevenGroupLevel(String groupLevel, List<String> sevenGroupLevelList) {
        if ("MANUFACTURE_OBJECT".equals(groupLevel)) {
            sevenGroupLevelList.add("SHIPPING_OBJECT");
            sevenGroupLevelList.add("SUBCATEGORY");
            sevenGroupLevelList.add("DIMENSION");
            sevenGroupLevelList.add("LV3");
            sevenGroupLevelList.add("LV2");
            sevenGroupLevelList.add("LV1");
        }
        if ("SHIPPING_OBJECT".equals(groupLevel)) {
            sevenGroupLevelList.add("SUBCATEGORY");
            sevenGroupLevelList.add("DIMENSION");
            sevenGroupLevelList.add("LV3");
            sevenGroupLevelList.add("LV2");
            sevenGroupLevelList.add("LV1");
        }
        if ("SUBCATEGORY".equals(groupLevel)) {
            sevenGroupLevelList.add("DIMENSION");
            sevenGroupLevelList.add("LV3");
            sevenGroupLevelList.add("LV2");
            sevenGroupLevelList.add("LV1");
        }
        if ("DIMENSION".equals(groupLevel)) {
            sevenGroupLevelList.add("LV3");
            sevenGroupLevelList.add("LV2");
            sevenGroupLevelList.add("LV1");
        }
        if ("LV3".equals(groupLevel)) {
            sevenGroupLevelList.add("LV2");
            sevenGroupLevelList.add("LV1");
        }
        if ("LV2".equals(groupLevel)) {
            sevenGroupLevelList.add("LV1");
        }
    }

    private void getBeforeDimensionEightGroupLevel(String groupLevel, List<String> groupLevelList) {
        if ("CATEGORY".equals(groupLevel)) {
            groupLevelList.add("MODL");
            groupLevelList.add("CEG");
            groupLevelList.add("SUB_DETAIL");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV3");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("MODL".equals(groupLevel)) {
            groupLevelList.add("CEG");
            groupLevelList.add("SUB_DETAIL");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV3");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("CEG".equals(groupLevel)) {
            groupLevelList.add("SUB_DETAIL");
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV3");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        if ("SUB_DETAIL".equals(groupLevel)) {
            groupLevelList.add("SUBCATEGORY");
            groupLevelList.add("DIMENSION");
            groupLevelList.add("LV3");
            groupLevelList.add("LV2");
            groupLevelList.add("LV1");
        }
        getBeforeDimensionEightSubGroupLevel(groupLevel, groupLevelList);
    }

    private void getBeforeManuDimensionEightGroupLevel(String groupLevel, List<String> manuGroupLevelList) {
        if ("MANUFACTURE_OBJECT".equals(groupLevel)) {
            manuGroupLevelList.add("SHIPPING_OBJECT");
            manuGroupLevelList.add("SUB_DETAIL");
            manuGroupLevelList.add("SUBCATEGORY");
            manuGroupLevelList.add("DIMENSION");
            manuGroupLevelList.add("LV3");
            manuGroupLevelList.add("LV2");
            manuGroupLevelList.add("LV1");
        }
        if ("SHIPPING_OBJECT".equals(groupLevel)) {
            manuGroupLevelList.add("SUB_DETAIL");
            manuGroupLevelList.add("SUBCATEGORY");
            manuGroupLevelList.add("DIMENSION");
            manuGroupLevelList.add("LV3");
            manuGroupLevelList.add("LV2");
            manuGroupLevelList.add("LV1");
        }
        if ("SUB_DETAIL".equals(groupLevel)) {
            manuGroupLevelList.add("SUBCATEGORY");
            manuGroupLevelList.add("DIMENSION");
            manuGroupLevelList.add("LV3");
            manuGroupLevelList.add("LV2");
            manuGroupLevelList.add("LV1");
        }
        getBeforeDimensionEightSubGroupLevel(groupLevel, manuGroupLevelList);
    }

    private void getBeforeManuDimensionElevenGroupLevel(String groupLevel, List<String> eightGroupLevelList) {
        if ("MANUFACTURE_OBJECT".equals(groupLevel)) {
            eightGroupLevelList.add("SHIPPING_OBJECT");
            eightGroupLevelList.add("SPART");
            eightGroupLevelList.add("SUB_DETAIL");
            eightGroupLevelList.add("SUBCATEGORY");
            eightGroupLevelList.add("DIMENSION");
            eightGroupLevelList.add("LV3");
            eightGroupLevelList.add("LV2");
            eightGroupLevelList.add("LV1");
        }
        if ("SHIPPING_OBJECT".equals(groupLevel)) {
            eightGroupLevelList.add("SPART");
            eightGroupLevelList.add("SUB_DETAIL");
            eightGroupLevelList.add("SUBCATEGORY");
            eightGroupLevelList.add("DIMENSION");
            eightGroupLevelList.add("LV3");
            eightGroupLevelList.add("LV2");
            eightGroupLevelList.add("LV1");
        }
        if ("SPART".equals(groupLevel)) {
            eightGroupLevelList.add("SUB_DETAIL");
            eightGroupLevelList.add("SUBCATEGORY");
            eightGroupLevelList.add("DIMENSION");
            eightGroupLevelList.add("LV3");
            eightGroupLevelList.add("LV2");
            eightGroupLevelList.add("LV1");
        }
        if ("SUB_DETAIL".equals(groupLevel)) {
            eightGroupLevelList.add("SUBCATEGORY");
            eightGroupLevelList.add("DIMENSION");
            eightGroupLevelList.add("LV3");
            eightGroupLevelList.add("LV2");
            eightGroupLevelList.add("LV1");
        }
        getBeforeDimensionEightSubGroupLevel(groupLevel, eightGroupLevelList);
    }

    private void getBeforeManuDimensionTwelveGroupLevel(String groupLevel, List<String> coaGroupLevelList) {
        if ("MANUFACTURE_OBJECT".equals(groupLevel)) {
            coaGroupLevelList.add("SHIPPING_OBJECT");
            coaGroupLevelList.add("SPART");
            coaGroupLevelList.add("SUB_DETAIL");
            coaGroupLevelList.add("SUBCATEGORY");
            coaGroupLevelList.add("DIMENSION");
            coaGroupLevelList.add("COA");
            coaGroupLevelList.add("LV3");
            coaGroupLevelList.add("LV2");
            coaGroupLevelList.add("LV1");
        }
        if ("SHIPPING_OBJECT".equals(groupLevel)) {
            coaGroupLevelList.add("SPART");
            coaGroupLevelList.add("SUB_DETAIL");
            coaGroupLevelList.add("SUBCATEGORY");
            coaGroupLevelList.add("DIMENSION");
            coaGroupLevelList.add("COA");
            coaGroupLevelList.add("LV3");
            coaGroupLevelList.add("LV2");
            coaGroupLevelList.add("LV1");
        }
        if ("SPART".equals(groupLevel)) {
            coaGroupLevelList.add("SUB_DETAIL");
            coaGroupLevelList.add("SUBCATEGORY");
            coaGroupLevelList.add("DIMENSION");
            coaGroupLevelList.add("COA");
            coaGroupLevelList.add("LV3");
            coaGroupLevelList.add("LV2");
            coaGroupLevelList.add("LV1");
        }
        if ("SUB_DETAIL".equals(groupLevel)) {
            coaGroupLevelList.add("SUBCATEGORY");
            coaGroupLevelList.add("DIMENSION");
            coaGroupLevelList.add("COA");
            coaGroupLevelList.add("LV3");
            coaGroupLevelList.add("LV2");
            coaGroupLevelList.add("LV1");
        }
        getBeforeDimensionTwelveSubGroupLevel(groupLevel, coaGroupLevelList);
    }

    private void getBeforeIasDimensionTwelveGroupLevel(String groupLevel, List<String> iasGroupLevelList) {
        if ("MANUFACTURE_OBJECT".equals(groupLevel)) {
            iasGroupLevelList.add("SHIPPING_OBJECT");
            commonIasDimension(iasGroupLevelList);
        }
        if ("SHIPPING_OBJECT".equals(groupLevel)) {
            commonIasDimension(iasGroupLevelList);
        }
        getBeforeDimensionTwelveCommonGroupLevel(groupLevel, iasGroupLevelList);
        getBeforeDimensionTwelveIasGroupLevel(groupLevel, iasGroupLevelList);
    }

    private void commonIasDimension(List<String> iasGroupLevelList) {
        iasGroupLevelList.add("SPART");
        iasGroupLevelList.add("SUB_DETAIL");

        commonIasSubDimension(iasGroupLevelList);
    }

    private void commonIasSubDimension(List<String> iasGroupLevelList) {

        iasGroupLevelList.add("SUBCATEGORY");
        iasGroupLevelList.add("DIMENSION");
        prodRndTeamLevelIas(iasGroupLevelList);
    }

    private void getBeforeDimensionTwelveCommonGroupLevel(String groupLevel, List<String> subGroupLevelList) {
        if ("SPART".equals(groupLevel)) {
            subGroupLevelList.add("SUB_DETAIL");
            commonIasSubDimension(subGroupLevelList);
        }
        if ("SUB_DETAIL".equals(groupLevel)) {
            commonIasSubDimension(subGroupLevelList);
        }
    }

    private void getBeforeDimensionTwelveIasGroupLevel(String groupLevel, List<String> subGroupLevelList) {
        if ("SUBCATEGORY".equals(groupLevel)) {
            subGroupLevelList.add("DIMENSION");
            prodRndTeamLevelIas(subGroupLevelList);
        }
        if ("DIMENSION".equals(groupLevel)) {
            prodRndTeamLevelIas(subGroupLevelList);
        }
        if ("LV4".equals(groupLevel)) {
            subGroupLevelList.add("LV3");
            subGroupLevelList.add("LV2");
            subGroupLevelList.add("LV1");
        }
        getManufactureUniversalSub(groupLevel, subGroupLevelList);
    }

    private void getBeforeDimensionTwelveSubGroupLevel(String groupLevel, List<String> subGroupLevelList) {
        if ("SUBCATEGORY".equals(groupLevel)) {
            subGroupLevelList.add("DIMENSION");
            subGroupLevelList.add("COA");
            subGroupLevelList.add("LV3");
            subGroupLevelList.add("LV2");
            subGroupLevelList.add("LV1");
        }
        if ("DIMENSION".equals(groupLevel)) {
            subGroupLevelList.add("COA");
            subGroupLevelList.add("LV3");
            subGroupLevelList.add("LV2");
            subGroupLevelList.add("LV1");
        }
        if ("COA".equals(groupLevel)) {
            subGroupLevelList.add("LV3");
            subGroupLevelList.add("LV2");
            subGroupLevelList.add("LV1");
        }
        if ("LV3".equals(groupLevel)) {
            subGroupLevelList.add("LV2");
            subGroupLevelList.add("LV1");
        }
        if ("LV2".equals(groupLevel)) {
            subGroupLevelList.add("LV1");
        }
    }

    private void getBeforeDimensionEightSubGroupLevel(String groupLevel, List<String> subGroupLevelList) {
        if ("SUBCATEGORY".equals(groupLevel)) {
            subGroupLevelList.add("DIMENSION");
            subGroupLevelList.add("LV3");
            subGroupLevelList.add("LV2");
            subGroupLevelList.add("LV1");
        }
        if ("DIMENSION".equals(groupLevel)) {
            subGroupLevelList.add("LV3");
            subGroupLevelList.add("LV2");
            subGroupLevelList.add("LV1");
        }
        if ("LV3".equals(groupLevel)) {
            subGroupLevelList.add("LV2");
            subGroupLevelList.add("LV1");
        }
        if ("LV2".equals(groupLevel)) {
            subGroupLevelList.add("LV1");
        }
    }

    private void removeListByCustomId(CommonViewVO commonViewVO, List<DmFocViewInfoVO> allProdTeamCodeList) {
        // 移除右侧临时表已有的code
        if (StringUtils.isNotEmpty(commonViewVO.getId())) {
            List<DmCustomCombVO> customCodeList = new ArrayList<>();
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
                customCodeList = dmFocCustomCombTempDao.getTempCustomCombList(commonViewVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
                customCodeList = dmFocCustomCombTempDao.getTempManufactureCustomCombList(commonViewVO);
            }
            Set<String> connectCodeSet = customCodeList.stream().map(DmCustomCombVO::getConnectCode).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(connectCodeSet)) {
                allProdTeamCodeList.removeIf(all -> connectCodeSet.contains(all.getConnectCode()));
            }
        }
    }

    private void combineOtherPageGroupCode(CombinationVO combinationVO, String userIdStr, String roleId, List<DmCustomCombVO> otherCustomVOList, Long customId, Timestamp timestamp) {
        if (CollectionUtils.isNotEmpty(otherCustomVOList)) {
            otherCustomVOList.stream().forEach(customVO -> {
                customVO.setCustomId(customId);
                customVO.setCustomCnName(combinationVO.getCustomCnName());
                customVO.setCreatedBy(userIdStr);
                customVO.setCreationDate(timestamp);
                customVO.setLastUpdatedBy(userIdStr);
                customVO.setLastUpdateDate(timestamp);
                customVO.setRoleId(roleId);
                customVO.setUserId(userIdStr);
                customVO.setEnableFlag(ENABLE_FLAG_Y);
                customVO.setSubEnableFlag(ENABLE_FLAG_Y);
                customVO.setIsSeparate("N");
                customVO.setViewFlag(combinationVO.getViewFlag());
                customVO.setOverseaFlag(combinationVO.getOverseaFlag());
                customVO.setCaliberFlag(combinationVO.getCaliberFlag());
                customVO.setPageFlag(combinationVO.getPageFlag() + "_" + allPageFlag.get(combinationVO.getPageSymbol()));
                customVO.setGranularityType(combinationVO.getGranularityType());
                customVO.setLv0ProdListCnName(combinationVO.getLv0ProdListCnName());
                customVO.setLv0ProdListCode(combinationVO.getLv0ProdListCode());
            });
        }
    }

    private void callFunctionRefreshData(CombinationVO combinationVO, Long userId, DmFocDataRefreshStatus dataRefreshStatus, CombTransformVO combTransformVO) {
        combTransformVO.setUserId(userId);
        combTransformVO.setTaskId(dataRefreshStatus.getTaskId());
        combTransformVO.setCustomId(combinationVO.getCustomId());
        combTransformVO.setGranularityType(combinationVO.getGranularityType());
        combTransformVO.setPageFlag(combinationVO.getPageFlag());
        combTransformVO.setIndustryOrg(combinationVO.getIndustryOrg());
        // 获取加密key
        String plainText = ConfigUtil.getInstance().get16PlainText();
        combTransformVO.setEncryptKey(plainText);
        // 获取最新版本号
        DmFocVersionInfoDTO dmFocVersionVO = dmFocVersionDao.findVersionIdByDataType("CATEGORY", TableNameVO.getTablePreFix(combinationVO.getIndustryOrg()));
        combTransformVO.setVersionId(dmFocVersionVO.getVersionId());
        DmFocVersionInfoDTO monthVersionVO = dmFocVersionDao.findVersionIdByDataType("ITEM", TableNameVO.getTablePreFix(combinationVO.getIndustryOrg()));
        combTransformVO.setMonthVersionId(monthVersionVO.getVersionId());
        combTransformVO.setCostType(combinationVO.getCostType());
    }

    private void allGroupLevelCondition(CommonViewVO commonViewVO, Map<String, List<DmFocViewInfoVO>> viewInfoMap, List<DmFocViewInfoVO> allGroupLevelConditionList) {
        List<String> groupLevelList = new ArrayList<>();
        setAllLevel(groupLevelList, commonViewVO);
        for (String groupLevel : groupLevelList) {
            for (Map.Entry<String, List<DmFocViewInfoVO>> groupLevelEntry : viewInfoMap.entrySet()) {
                String groupLevelKey = groupLevelEntry.getKey();
                if (groupLevel.equals(groupLevelKey)) {
                    allGroupLevelConditionList.addAll(groupLevelEntry.getValue());
                }
            }
        }
    }

    private void allTempGroupLevelCondition(CommonViewVO commonViewVO, Map<String, List<DmCustomCombVO>> viewInfoMap, List<DmCustomCombVO> allGroupLevelConditionList) {
        List<String> allGroupLevelList = new ArrayList<>();
        setAllLevel(allGroupLevelList, commonViewVO);
        for (String groupLevel : allGroupLevelList) {
            for (Map.Entry<String, List<DmCustomCombVO>> groupLevelEntry : viewInfoMap.entrySet()) {
                String groupLevelKey = groupLevelEntry.getKey();
                if (groupLevel.equals(groupLevelKey)) {
                    allGroupLevelConditionList.addAll(groupLevelEntry.getValue());
                }
            }
        }
    }

    private void setAllLevel(List<String> allGroupLevelList, CommonViewVO commonViewVO) {
        allGroupLevelList.add(GroupLevelAllEnum.LV0.getValue());
        allGroupLevelList.add(GroupLevelAllEnum.LV1.getValue());
        allGroupLevelList.add(GroupLevelAllEnum.LV2.getValue());
        allGroupLevelList.add(GroupLevelAllEnum.LV3.getValue());
        allGroupLevelList.add(GroupLevelAllEnum.LV4.getValue());
        allGroupLevelList.add(GroupLevelAllEnum.L1.getValue());
        allGroupLevelList.add(GroupLevelAllEnum.L2.getValue());
        allGroupLevelList.add(GroupLevelAllEnum.COA.getValue());
        allGroupLevelList.add(GroupLevelAllEnum.DIMENSION.getValue());
        allGroupLevelList.add(GroupLevelAllEnum.SUBCATEGORY.getValue());
        allGroupLevelList.add(GroupLevelAllEnum.SUB_DETAIL.getValue());
        allGroupLevelList.add(GroupLevelAllEnum.SPART.getValue());
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
            allGroupLevelList.add(GroupLevelAllEnum.SHIPPING_OBJECT.getValue());
            allGroupLevelList.add(GroupLevelAllEnum.MANUFACTURE_OBJECT.getValue());
        } else {
            allGroupLevelList.add(GroupLevelAllEnum.CEG.getValue());
            allGroupLevelList.add(GroupLevelAllEnum.MODL.getValue());
            allGroupLevelList.add(GroupLevelAllEnum.CATEGORY.getValue());
        }
    }
}

