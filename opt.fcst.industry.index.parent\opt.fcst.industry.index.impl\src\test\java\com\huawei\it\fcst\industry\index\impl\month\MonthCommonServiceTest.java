/*
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.month;

import com.huawei.it.fcst.industry.index.config.ExecutorConfig;
import com.huawei.it.fcst.industry.index.constant.CommonConstant;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeMonthYoyDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeRecMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMonthYoyDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocRecMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTotalMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTotalMonthYoyDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocVersionDao;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumD;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumMadeD;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumP;
import com.huawei.it.fcst.industry.index.enums.IndustryIndexEnum;
import com.huawei.it.fcst.industry.index.impl.annual.AnnualCommonService;
import com.huawei.it.fcst.industry.index.service.common.ICommonService;
import com.huawei.it.fcst.industry.index.utils.FcstIndexMadeUtil;
import com.huawei.it.fcst.industry.index.utils.FcstIndexUtil;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoDTO;
import com.huawei.it.fcst.industry.index.vo.month.CompareAnalysisVO;
import com.huawei.it.fcst.industry.index.vo.month.DmFocActualCostVO;
import com.huawei.it.fcst.industry.index.vo.month.DmFocMonthCostIdxVO;
import com.huawei.it.fcst.industry.index.vo.month.DmFocMonthWeightVO;
import com.huawei.it.fcst.industry.index.vo.month.DmFocMonthYoyVO;
import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.security.UserVO;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executor;

import static org.assertj.core.api.AssertionsForClassTypes.assertThatNoException;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 * @since 2023/4/13
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest( {FcstIndexUtil.class,FcstIndexMadeUtil.class})
public class MonthCommonServiceTest {

    @InjectMocks
    private MonthCommonService monthCommonService;
    @Mock
    private IDmFocMonthCostIdxDao dmFocMonthCostIdxDao;
    @Mock
    private IDmFocMadeMonthCostIdxDao dmFocMadeMonthCostIdxDao;
    @Mock
    private IDmFocTotalMonthCostIdxDao dmFocTotalMonthCostIdxDao;
    @Mock
    private IDmFocMadeRecMonthCostIdxDao dmFocMadeRecMonthCostIdxDao;
    @Mock
    private IDmFocMonthYoyDao dmFocMonthYoyDao;
    @Mock
    private ICommonService commonService;
    @Mock
    private IDmFocRecMonthCostIdxDao dmFocRecMonthCostIdxDao;
    @Mock
    private AsyncQueryService asyncQueryService;
    @Mock
    private IDmFocMadeMonthYoyDao dmFocMadeMonthYoyDao;
    @Mock
    private IDmFocTotalMonthYoyDao dmFocTotalMonthYoyDao;
    @Mock
    private AnnualCommonService annualCommonService;
    @Mock
    private IDmFocVersionDao dmFocVersionDao;

    @Mock
    private ExecutorConfig executorConfig;

    @Mock
    private Executor executor;

    @Before
    public void before() throws Exception {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);
    }

    @Test
    public void getIndustryCostIndexChartForComb() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupCodeList(Arrays.asList("104364_##11"));
        monthAnalysisVO.setBasePeriodId(202301);
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setIndustryOrg("ICT");
        // 汇总组合测试
        monthAnalysisVO.setIsContainComb(true);
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setViewFlag("1");
        monthAnalysisVO.setType("exp");
        when(commonService.getVersionId(anyString(),anyString())).thenReturn(1L);
        when(dmFocMonthCostIdxDao.findActualMonthNum(anyString())).thenReturn(202406L);
        List<DmFocMonthCostIdxVO> combIndexVOList = getCombIndexVOList();
        when(dmFocMonthCostIdxDao.findDmFocCombPriceIndexVOList(any())).thenReturn(combIndexVOList);
        Assertions.assertNotNull(monthCommonService.getIndustryCostIndexChart(monthAnalysisVO));

        monthAnalysisVO.setCostType("M");
        monthAnalysisVO.setGroupCodeList(Arrays.asList("104364_##11"));
        when(dmFocMadeMonthCostIdxDao.findStartEndTime(any(),any())).thenReturn(getStringLongMap());
        Assertions.assertNotNull(monthCommonService.getIndustryCostIndexChart(monthAnalysisVO));

        monthAnalysisVO.setGroupCodeList(Arrays.asList("104364"));
        monthAnalysisVO.setCompareAnalysisVO(CompareAnalysisVO.builder().isCompareFlag(true).build());
        Assertions.assertNotNull(monthCommonService.getIndustryCostIndexChart(monthAnalysisVO));

        monthAnalysisVO.setCompareAnalysisVO(CompareAnalysisVO.builder().isCompareFlag(false).build());
        Assertions.assertNotNull(monthCommonService.getIndustryCostIndexChart(monthAnalysisVO));

        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setCompareAnalysisVO(CompareAnalysisVO.builder().isCompareFlag(true).build());
        Assertions.assertNotNull(monthCommonService.getIndustryCostIndexChart(monthAnalysisVO));

        monthAnalysisVO.setCompareAnalysisVO(CompareAnalysisVO.builder().isCompareFlag(false).build());
        Assertions.assertNotNull(monthCommonService.getIndustryCostIndexChart(monthAnalysisVO));

        // 反向视角测试
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setViewFlag("6");
        Assertions.assertNotNull(monthCommonService.getIndustryCostIndexChart(monthAnalysisVO));

        monthAnalysisVO.setCostType("M");
        Assertions.assertNotNull(monthCommonService.getIndustryCostIndexChart(monthAnalysisVO));

        // 正向视角测试
        monthAnalysisVO.setViewFlag("1");
        monthAnalysisVO.setIsMultipleSelect(true);
        Assertions.assertNotNull(monthCommonService.getIndustryCostIndexChart(monthAnalysisVO));

        monthAnalysisVO.setCompareAnalysisVO(CompareAnalysisVO.builder().isCompareFlag(true).build());
        Assertions.assertNotNull(monthCommonService.getIndustryCostIndexChart(monthAnalysisVO));

        monthAnalysisVO.setCostType("P");
        Assertions.assertNotNull(monthCommonService.getIndustryCostIndexChart(monthAnalysisVO));

        monthAnalysisVO.setCompareAnalysisVO(CompareAnalysisVO.builder().isCompareFlag(false).build());
        Assertions.assertNotNull(monthCommonService.getIndustryCostIndexChart(monthAnalysisVO));

        monthAnalysisVO.setCostType("T");
        when(dmFocVersionDao.findAnnualVersion(anyString())).thenReturn(DmFocVersionInfoDTO.builder().versionId(1L).build());
        Assertions.assertNotNull(monthCommonService.getIndustryCostIndexChart(monthAnalysisVO));

        monthAnalysisVO.setType("");
        monthAnalysisVO.setCostSubType("P");
        Assertions.assertNotNull(monthCommonService.getIndustryCostIndexChart(monthAnalysisVO));

        monthAnalysisVO.setCostSubType("M");
        Assertions.assertNotNull(monthCommonService.getIndustryCostIndexChart(monthAnalysisVO));

        monthAnalysisVO.setCostSubType("T");
        Assertions.assertNotNull(monthCommonService.getIndustryCostIndexChart(monthAnalysisVO));

        monthAnalysisVO.setIsMultipleSelect(false);
        Assertions.assertNotNull(monthCommonService.getIndustryCostIndexChart(monthAnalysisVO));
    }

    @Test
    public void getIndustryCostIndexChart() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupCodeList(Arrays.asList("104364_##11"));
        monthAnalysisVO.setBasePeriodId(202301);
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setIndustryOrg("ICT");
        // 正向视角测试
        monthAnalysisVO.setViewFlag("1");
        monthAnalysisVO.setIsMultipleSelect(true);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("M");
        monthAnalysisVO.setCompareAnalysisVO(CompareAnalysisVO.builder().isCompareFlag(false).build());
        when(commonService.getVersionId(anyString(),anyString())).thenReturn(1L);
        when(dmFocMonthCostIdxDao.findActualMonthNum(anyString())).thenReturn(202406L);
        Mockito.when(dmFocMadeMonthCostIdxDao.findStartEndTime(any(),any())).thenReturn(getStringLongMap());
        Assertions.assertNotNull(monthCommonService.getIndustryCostIndexChart(monthAnalysisVO));

        monthAnalysisVO.setCompareAnalysisVO(CompareAnalysisVO.builder().isCompareFlag(true).build());
        Assertions.assertNotNull(monthCommonService.getIndustryCostIndexChart(monthAnalysisVO));

        monthAnalysisVO.setCostType("P");
        Assertions.assertNotNull(monthCommonService.getIndustryCostIndexChart(monthAnalysisVO));

        monthAnalysisVO.setCompareAnalysisVO(CompareAnalysisVO.builder().isCompareFlag(false).build());
        Assertions.assertNotNull(monthCommonService.getIndustryCostIndexChart(monthAnalysisVO));

        monthAnalysisVO.setCostType("T");
        when(dmFocVersionDao.findAnnualVersion(anyString())).thenReturn(DmFocVersionInfoDTO.builder().versionId(1L).build());
        Assertions.assertNotNull(monthCommonService.getIndustryCostIndexChart(monthAnalysisVO));

        monthAnalysisVO.setType("");
        monthAnalysisVO.setCostSubType("P");
        Assertions.assertNotNull(monthCommonService.getIndustryCostIndexChart(monthAnalysisVO));

        monthAnalysisVO.setCostSubType("M");
        Assertions.assertNotNull(monthCommonService.getIndustryCostIndexChart(monthAnalysisVO));

        monthAnalysisVO.setCostSubType("T");
        Assertions.assertNotNull(monthCommonService.getIndustryCostIndexChart(monthAnalysisVO));

        monthAnalysisVO.setIsMultipleSelect(false);
        Assertions.assertNotNull(monthCommonService.getIndustryCostIndexChart(monthAnalysisVO));
    }

    @NotNull
    private List<DmFocMonthCostIdxVO> getCombIndexVOList() {
        List<DmFocMonthCostIdxVO> combPriceIndexVOList = new ArrayList<>();
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setCostIndex(0.36);
        costIdxVO.setGroupCnName("ti");
        costIdxVO.setCustomCnName("cu");
        costIdxVO.setGroupCode("11");
        combPriceIndexVOList.add(costIdxVO);
        return combPriceIndexVOList;
    }

    @NotNull
    private Map<String, Long> getStringLongMap() {
        Map<String, Long> startEndTime = new HashMap<>();
        startEndTime.put("start", 2022301L);
        startEndTime.put("end", 2022303L);
        return startEndTime;
    }

    @Test
    public void getCompareIndexChartTest() throws InterruptedException {
        List<MonthAnalysisVO> monthAnalysisVolist = new ArrayList<>();
        Mockito.when(executorConfig.asyncServiceExecutor()).thenReturn(executor);
        ResultDataVO result = monthCommonService.getCompareIndexChart(monthAnalysisVolist);
        Assert.assertNotNull(result);
    }

    @Test
    public void compareIndexParamsTest() throws Exception {
        List<String> list = new ArrayList<>();
        list.add("test");
        List<MonthAnalysisVO> monthAnalysisVolist = new ArrayList<>();
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setLv0ProdRndTeamCode("Lv0");
        monthAnalysisVO.setLv0ProdRndTeamCode("Lv0");
        monthAnalysisVO.setLv1ProdRndTeamCode("Lv1");
        monthAnalysisVO.setLv2ProdRndTeamCode("Lv2");
        monthAnalysisVO.setLv3ProdRndTeamCode("Lv3");
        monthAnalysisVO.setLv4ProdRndTeamCode("Lv4");
        monthAnalysisVO.setGroupCodeList(list);
        MonthAnalysisVO monthAnalysisVO1 = new MonthAnalysisVO();
        monthAnalysisVO1.setIsContainComb(false);
        MonthAnalysisVO monthAnalysisVO2 = new MonthAnalysisVO();
        monthAnalysisVO2.setIsContainComb(true);
        monthAnalysisVolist.add(monthAnalysisVO);
        monthAnalysisVolist.add(monthAnalysisVO1);
        monthAnalysisVolist.add(monthAnalysisVO2);
        Whitebox.invokeMethod(monthCommonService, "compareIndexParams", monthAnalysisVolist);
        Assert.assertNotNull(monthAnalysisVolist);
    }

    @Test
    public void compareChangeBasePeriodIdTest() throws CommonApplicationException {
        Map map = new HashMap();
        map.put("nextGroupLevel","LV1");
        map.put("nextGroupName","LV2");
        mockStatic(FcstIndexMadeUtil.class);
        when(FcstIndexMadeUtil.getNextGroupLevel(any())).thenReturn(map);
        mockStatic(FcstIndexUtil.class);
        when(FcstIndexUtil.getNextGroupLevel(any())).thenReturn(map);
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setCostType("T");
        Mockito.when(dmFocTotalMonthCostIdxDao.findTotalPriceIdxByBasePeriodId(any())).thenReturn(1);
        Assert.assertThrows(NullPointerException.class,
                () -> monthCommonService.compareChangeBasePeriodId(monthAnalysisVO));
    }

    @Test
    public void compareChangeBasePeriodId1Test() throws CommonApplicationException {
        Map map = new HashMap();
        map.put("nextGroupLevel","LV1");
        map.put("nextGroupName","LV2");
        mockStatic(FcstIndexMadeUtil.class);
        when(FcstIndexMadeUtil.getNextGroupLevel(any())).thenReturn(map);
        mockStatic(FcstIndexUtil.class);
        when(FcstIndexUtil.getNextGroupLevel(any())).thenReturn(map);
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setVersionId(1L);
        monthAnalysisVO.setCostType("M");
        monthAnalysisVO.setBasePeriodId(202301);
        List<Long> list = new ArrayList<>();
        list.add(1L);
        monthAnalysisVO.setGroupLevel("Lv1");
        monthAnalysisVO.setCustomIdList(list);
        monthAnalysisVO.setIndustryOrg("IAS");
        List<String> threeYears  = new ArrayList<>();
        threeYears.add("2023");
        Mockito.when(annualCommonService.getYearList(any(),any())).thenReturn(threeYears);
        Mockito.when(dmFocMonthCostIdxDao.insertPriceIndexWithFun(any())).thenReturn(CommonConstant.SUCCESS);
        monthCommonService.compareChangeBasePeriodId(monthAnalysisVO);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @Test
    public void compareChangeBasePeriodId2Test() throws CommonApplicationException {
        Map map = new HashMap();
        map.put("nextGroupLevel","LV1");
        map.put("nextGroupName","LV2");
        mockStatic(FcstIndexMadeUtil.class);
        when(FcstIndexMadeUtil.getNextGroupLevel(any())).thenReturn(map);
        mockStatic(FcstIndexUtil.class);
        when(FcstIndexUtil.getNextGroupLevel(any())).thenReturn(map);
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setVersionId(1L);
        monthAnalysisVO.setCostType("M");
        monthAnalysisVO.setBasePeriodId(202301);
        monthAnalysisVO.setIsContainComb(true);
        List<String> list1 = new ArrayList<>();
        list1.add("test");
        monthAnalysisVO.setGroupCodeList(list1);
        List<Long> list = new ArrayList<>();
        list.add(1L);
        monthAnalysisVO.setGroupLevel("Lv1");
        monthAnalysisVO.setCustomIdList(list);
        monthAnalysisVO.setIndustryOrg("IAS");
        List<String> threeYears  = new ArrayList<>();
        threeYears.add("2023");
        Mockito.when(annualCommonService.getYearList(any(),any())).thenReturn(threeYears);
        Mockito.when(dmFocMonthCostIdxDao.insertPriceIndexWithFun(any())).thenReturn(CommonConstant.SUCCESS);
        monthCommonService.compareChangeBasePeriodId(monthAnalysisVO);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @Test
    public void indexChangeBasePeriodIdTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setReverseViewFlag(false);
        monthAnalysisVO.setGroupCodeList(Arrays.asList("104364"));
        monthAnalysisVO.setCombinaCodeList(Arrays.asList("104364_##11"));
        when(commonService.findActualMonthNum(anyString())).thenReturn(202406L);
        when(dmFocMonthCostIdxDao.insertPriceIndexWithFun(any())).thenReturn(CommonConstant.SUCCESS);
        Whitebox.invokeMethod(monthCommonService, "indexChangeBasePeriodId", monthAnalysisVO);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @Test
    public void changePeriodIdFlagTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setGranularityType(IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue());
        monthAnalysisVO.setViewFlag(IndustryIndexEnum.VIEW_FLAG_U.VIEW5.getValue());
        monthAnalysisVO.setCostType("P");
        List<String> list1 = new ArrayList<>();
        list1.add("test");
        monthAnalysisVO.setSubGroupCodeList(list1);
        monthAnalysisVO.setBasePeriodId(202301);
        monthAnalysisVO.setGroupCodeList(Arrays.asList("11"));
        Mockito.when(dmFocMonthCostIdxDao.insertPriceIndexWithFun(any())).thenReturn(CommonConstant.SUCCESS);
        Mockito.when(commonService.findActualMonthNum(anyString())).thenReturn(202406L);
        Mockito.when(dmFocRecMonthCostIdxDao.findRecPriceIndexByBasePeriodId(any())).thenReturn(0);
        Whitebox.invokeMethod(monthCommonService, "changePeriodIdFlag", monthAnalysisVO);
        Assert.assertNotNull(monthAnalysisVO);

        monthAnalysisVO.setViewFlag(IndustryIndexEnum.VIEW_FLAG_U.VIEW6.getValue());
        Mockito.when(dmFocRecMonthCostIdxDao.findRecPriceIndexByBasePeriodId(any())).thenReturn(0);
        Whitebox.invokeMethod(monthCommonService, "changePeriodIdFlag", monthAnalysisVO);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @Test
    public void changePeriodIdFlag1Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setCostType("P");
        List<String> list1 = new ArrayList<>();
        list1.add("test");
        monthAnalysisVO.setSubGroupCodeList(list1);
        monthAnalysisVO.setBasePeriodId(202301);
        Mockito.when(dmFocMonthCostIdxDao.insertPriceIndexWithFun(any())).thenReturn("SUCCESS");
        Mockito.when(commonService.findActualMonthNum(anyString())).thenReturn(202406L);
        Mockito.when(dmFocRecMonthCostIdxDao.findRecPriceIndexByBasePeriodId(any())).thenReturn(0);
        Whitebox.invokeMethod(monthCommonService, "changePeriodIdFlag", monthAnalysisVO);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @Test
    public void compareRefreshIndustryIndexDataTest() throws CommonApplicationException {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setReverseViewFlag(false);
        monthAnalysisVO.setCostType("T");
        monthAnalysisVO.setIndustryOrg("IAS");
        Assert.assertThrows(CommonApplicationException.class, () ->
                monthCommonService.compareRefreshIndustryIndexData(monthAnalysisVO));

        Mockito.when(dmFocMonthCostIdxDao.insertTotalPriceIdxWithFun(any())).thenReturn(CommonConstant.SUCCESS);
        Mockito.when(dmFocMonthCostIdxDao.insertPriceIndexWithFun(any())).thenReturn(null);
        Assert.assertThrows(CommonApplicationException.class, () ->
                monthCommonService.compareRefreshIndustryIndexData(monthAnalysisVO));

        Map map = new HashMap();
        map.put("nextGroupLevel","LV1");
        map.put("nextGroupName","LV2");
        mockStatic(FcstIndexMadeUtil.class);
        when(FcstIndexMadeUtil.getNextGroupLevel(any())).thenReturn(map);
        Mockito.when(dmFocMonthCostIdxDao.insertPriceIndexWithFun(any())).thenReturn(CommonConstant.SUCCESS);
        monthCommonService.compareRefreshIndustryIndexData(monthAnalysisVO);
        Assert.assertNotNull(monthAnalysisVO);

        monthAnalysisVO.setCostType("M");
        monthCommonService.compareRefreshIndustryIndexData(monthAnalysisVO);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @Test
    public void compareRefreshIndustryIndexData1Test() throws CommonApplicationException {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setReverseViewFlag(false);
        monthAnalysisVO.setCostType("M");
        Assert.assertThrows(CommonApplicationException.class, () ->
                monthCommonService.compareRefreshIndustryIndexData(monthAnalysisVO));

        Mockito.when(dmFocMonthCostIdxDao.insertPriceIndexWithFun(any())).thenReturn(CommonConstant.SUCCESS);
        monthCommonService.compareRefreshIndustryIndexData(monthAnalysisVO);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @Test
    public void getCombAnalysisVOTest() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        monthCommonService.getCombAnalysisVO(monthAnalysisVO, monthAnalysisVO);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @Test
    public void setDimensionCnNameTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel(GroupLevelEnumD.SPART.getValue());
        List<DmFocActualCostVO> heatMapList = new ArrayList<>();
        DmFocActualCostVO dmFocActualCostVO = new DmFocActualCostVO();
        heatMapList.add(dmFocActualCostVO);
        Whitebox.invokeMethod(monthCommonService, "setDimensionCnName", monthAnalysisVO, heatMapList);
        Assert.assertNotNull(heatMapList);
    }

    @Test
    public void setSingChartCegOrShipingObjectTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel(GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue());
        List<DmFocMonthCostIdxVO> priceIndexVOList = new ArrayList<>();
        DmFocMonthCostIdxVO dmFocMonthCostIdxVO = new DmFocMonthCostIdxVO();
        priceIndexVOList.add(dmFocMonthCostIdxVO);
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList = new ArrayList<>();
        DmFocMonthYoyVO dmFocMonthYoyVO = new DmFocMonthYoyVO();
        yoyAndPopIndexVOList.add(dmFocMonthYoyVO);
        Whitebox.invokeMethod(monthCommonService, "setSingChartCegOrShipingObject", monthAnalysisVO,
                priceIndexVOList, yoyAndPopIndexVOList);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @Test
    public void setSingChartCegOrShipingObject1Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel(GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue());
        List<DmFocMonthCostIdxVO> priceIndexVOList = getIndexVOList();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList = getYoyAndPopIndexVOList();
        Whitebox.invokeMethod(monthCommonService, "setSingChartCegOrShipingObject", monthAnalysisVO,
                priceIndexVOList, yoyAndPopIndexVOList);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @Test
    public void setSingleChartL2NameTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel(GroupLevelEnumP.L2.getValue());
        List<DmFocMonthCostIdxVO> priceIndexVOList = getIndexVOList();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList = getYoyAndPopIndexVOList();
        Whitebox.invokeMethod(monthCommonService, "setSingleChartL2Name", monthAnalysisVO,
                priceIndexVOList, yoyAndPopIndexVOList);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @Test
    public void setSingleChartL2Name1Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel(GroupLevelEnumP.L2.getValue());
        List<DmFocMonthCostIdxVO> priceIndexVOList = new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList = new ArrayList<>();
        Whitebox.invokeMethod(monthCommonService, "setSingleChartL2Name", monthAnalysisVO,
                priceIndexVOList, yoyAndPopIndexVOList);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @Test
    public void setSingleChartL1NameTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel(GroupLevelEnumP.L1.getValue());
        List<DmFocMonthCostIdxVO> priceIndexVOList = getIndexVOList();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList = getYoyAndPopIndexVOList();
        Whitebox.invokeMethod(monthCommonService, "setSingleChartL1Name", monthAnalysisVO,
                priceIndexVOList, yoyAndPopIndexVOList);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @Test
    public void dimensionSpartCnNameTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setParentLevel(GroupLevelEnumD.SPART.getValue());
        List<DmFocMonthCostIdxVO> priceIndexVOList = getIndexVOList();
        List<DmFocActualCostVO> heatMapList = getHeatList();
        List<DmFocMonthWeightVO> weightList = getWeightList();
        Whitebox.invokeMethod(monthCommonService, "dimensionSpartCnName", monthAnalysisVO,
                priceIndexVOList, heatMapList, weightList);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @Test
    public void dimensionSpartCnName1Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setParentLevel(GroupLevelEnumD.SPART.getValue());
        List<DmFocMonthCostIdxVO> priceIndexVOList = new ArrayList<>();
        List<DmFocActualCostVO> heatMapList = new ArrayList<>();
        List<DmFocMonthWeightVO> weightList = new ArrayList<>();
        Whitebox.invokeMethod(monthCommonService, "dimensionSpartCnName", monthAnalysisVO,
                priceIndexVOList, heatMapList, weightList);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @Test
    public void dimensionSubDetailParentCnNameTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setParentLevel(GroupLevelEnumD.SUB_DETAIL.getValue());
        List<DmFocMonthCostIdxVO> priceIndexVOList = getIndexVOList();
        List<DmFocActualCostVO> heatMapList = getHeatList();
        List<DmFocMonthWeightVO> weightList = getWeightList();
        Whitebox.invokeMethod(monthCommonService, "dimensionSubDetailParentCnName", monthAnalysisVO,
                priceIndexVOList, heatMapList, weightList);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @Test
    public void dimensionSubDetailParentCnName1Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setParentLevel(GroupLevelEnumD.SUB_DETAIL.getValue());
        List<DmFocMonthCostIdxVO> priceIndexVOList = new ArrayList<>();
        List<DmFocActualCostVO> heatMapList = new ArrayList<>();
        List<DmFocMonthWeightVO> weightList = getWeightList();
        Whitebox.invokeMethod(monthCommonService, "dimensionSubDetailParentCnName", monthAnalysisVO,
                priceIndexVOList, heatMapList, weightList);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @Test
    public void dimensionSubCateParnetCnNameTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setParentLevel(GroupLevelEnumD.SUBCATEGORY.getValue());
        List<DmFocMonthCostIdxVO> priceIndexVOList = getIndexVOList();
        List<DmFocActualCostVO> heatMapList = getHeatList();
        List<DmFocMonthWeightVO> weightList = getWeightList();
        Whitebox.invokeMethod(monthCommonService, "dimensionSubCateParnetCnName", monthAnalysisVO,
                priceIndexVOList, heatMapList, weightList);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @Test
    public void dimensionSubCateParnetCnName1Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setParentLevel(GroupLevelEnumD.SUBCATEGORY.getValue());
        List<DmFocMonthCostIdxVO> priceIndexVOList = new ArrayList<>();
        List<DmFocActualCostVO> heatMapList = new ArrayList<>();
        List<DmFocMonthWeightVO> weightList = getWeightList();
        Whitebox.invokeMethod(monthCommonService, "dimensionSubCateParnetCnName", monthAnalysisVO,
                priceIndexVOList, heatMapList, weightList);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @Test
    public void dimensionParentCnNameTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setParentLevel(GroupLevelEnumD.DIMENSION.getValue());
        List<DmFocMonthCostIdxVO> priceIndexVOList = getIndexVOList();
        List<DmFocActualCostVO> heatMapList = getHeatList();
        List<DmFocMonthWeightVO> weightList = getWeightList();
        Whitebox.invokeMethod(monthCommonService, "dimensionParentCnName", monthAnalysisVO,
                priceIndexVOList, heatMapList, weightList);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @NotNull
    private List<DmFocMonthCostIdxVO> getIndexVOList() {
        List<DmFocMonthCostIdxVO> priceIndexVOList = new ArrayList<>();
        DmFocMonthCostIdxVO dmFocMonthCostIdxVO = new DmFocMonthCostIdxVO();
        dmFocMonthCostIdxVO.setL1Name("L1");
        dmFocMonthCostIdxVO.setL2Name("L1");
        priceIndexVOList.add(dmFocMonthCostIdxVO);
        return priceIndexVOList;
    }

    @NotNull
    private List<DmFocMonthWeightVO> getWeightList() {
        List<DmFocMonthWeightVO> weightList = new ArrayList<>();
        weightList.add(DmFocMonthWeightVO.builder().groupLevel("COA").coaCode("1L").coaCnName("AA").build());
        weightList.add(DmFocMonthWeightVO.builder().parentCode("1L").parentCnName("AA").build());
        return weightList;
    }

    @NotNull
    private List<DmFocActualCostVO> getHeatList() {
        List<DmFocActualCostVO> heatMapList = new ArrayList<>();
        DmFocActualCostVO dmFocActualCostVO = new DmFocActualCostVO();
        dmFocActualCostVO.setGroupLevel("COA");
        dmFocActualCostVO.setL1Name("L1");
        dmFocActualCostVO.setL2Name("L1");
        heatMapList.add(dmFocActualCostVO);
        return heatMapList;
    }

    @Test
    public void dimensionParentCnName1Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setParentLevel(GroupLevelEnumD.DIMENSION.getValue());
        List<DmFocMonthCostIdxVO> priceIndexVOList = new ArrayList<>();
        List<DmFocActualCostVO> heatMapList = new ArrayList<>();
        List<DmFocMonthWeightVO> weightList = getWeightList();
        Whitebox.invokeMethod(monthCommonService, "dimensionParentCnName", monthAnalysisVO,
                priceIndexVOList, heatMapList, weightList);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @Test
    public void spartCnNameTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel(GroupLevelEnumD.SPART.getValue());
        List<DmFocMonthCostIdxVO> priceIndexVOList = getIndexVOList();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList = getYoyAndPopIndexVOList();
        Whitebox.invokeMethod(monthCommonService, "spartCnName", monthAnalysisVO,
                priceIndexVOList, yoyAndPopIndexVOList);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @Test
    public void spartCnName1Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel(GroupLevelEnumD.SPART.getValue());
        List<DmFocMonthCostIdxVO> priceIndexVOList = new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList = new ArrayList<>();
        Whitebox.invokeMethod(monthCommonService, "spartCnName", monthAnalysisVO,
                priceIndexVOList, yoyAndPopIndexVOList);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @Test
    public void subDetailGroupCnNameTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel(GroupLevelEnumD.SUB_DETAIL.getValue());
        List<DmFocMonthCostIdxVO> priceIndexVOList = getIndexVOList();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList = getYoyAndPopIndexVOList();
        Whitebox.invokeMethod(monthCommonService, "subDetailGroupCnName", monthAnalysisVO,
                priceIndexVOList, yoyAndPopIndexVOList);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @NotNull
    private List<DmFocMonthYoyVO> getYoyAndPopIndexVOList() {
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList = new ArrayList<>();
        DmFocMonthYoyVO dmFocMonthYoyVO = new DmFocMonthYoyVO();
        dmFocMonthYoyVO.setL1Name("L1");
        dmFocMonthYoyVO.setL2Name("L1");
        yoyAndPopIndexVOList.add(dmFocMonthYoyVO);
        return yoyAndPopIndexVOList;
    }

    @Test
    public void subDetailGroupCnName1Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel(GroupLevelEnumD.SUB_DETAIL.getValue());
        List<DmFocMonthCostIdxVO> priceIndexVOList = new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList = new ArrayList<>();
        Whitebox.invokeMethod(monthCommonService, "subDetailGroupCnName", monthAnalysisVO,
                priceIndexVOList, yoyAndPopIndexVOList);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @Test
    public void subCateGroupCnNameTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel(GroupLevelEnumD.SUBCATEGORY.getValue());
        List<DmFocMonthCostIdxVO> priceIndexVOList = getIndexVOList();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList = getYoyAndPopIndexVOList();
        Whitebox.invokeMethod(monthCommonService, "subCateGroupCnName", monthAnalysisVO,
                priceIndexVOList, yoyAndPopIndexVOList);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @Test
    public void subCateGroupCnName1Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel(GroupLevelEnumD.SUBCATEGORY.getValue());
        List<DmFocMonthCostIdxVO> priceIndexVOList = new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList = new ArrayList<>();
        Whitebox.invokeMethod(monthCommonService, "subCateGroupCnName", monthAnalysisVO,
                priceIndexVOList, yoyAndPopIndexVOList);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @Test
    public void cegOrShippingObjectGroupCnNameTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel(GroupLevelEnumD.CEG.getValue());
        List<DmFocMonthCostIdxVO> priceIndexVOList = getIndexVOList();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList = getYoyAndPopIndexVOList();
        Whitebox.invokeMethod(monthCommonService, "cegOrShippingObjectGroupCnName", monthAnalysisVO,
                priceIndexVOList, yoyAndPopIndexVOList);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @Test
    public void cegOrShippingObjectGroupCnName1Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel(GroupLevelEnumD.CEG.getValue());
        List<DmFocMonthCostIdxVO> priceIndexVOList = new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList = new ArrayList<>();
        Whitebox.invokeMethod(monthCommonService, "cegOrShippingObjectGroupCnName", monthAnalysisVO,
                priceIndexVOList, yoyAndPopIndexVOList);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @Test
    public void getDimensionTest() throws Exception {
        DmFocMonthYoyVO yoyVO = new DmFocMonthYoyVO();
        yoyVO.setDimensionCode("code");
        yoyVO.setDimensionCnName("cnName");
        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setDimensionCode("code");
        weightVO.setDimensionCnName("cnName");
        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setDimensionCode("code");
        actualCostVO.setDimensionCnName("cnName");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setDimensionCode("code");
        costIdxVO.setDimensionCnName("cnName");
        String dmsCnName = null;
        String result = Whitebox.invokeMethod(monthCommonService, "getDimension", yoyVO, weightVO, actualCostVO,
                costIdxVO, dmsCnName);
        Assert.assertNotNull(result);
    }

    @Test
    public void getSubCategoryTest() throws Exception {
        DmFocMonthYoyVO yoyVO = new DmFocMonthYoyVO();
        yoyVO.setDimensionSubCategoryCode("code");
        yoyVO.setDimensionSubCategoryCnName("cnName");
        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setDimensionSubCategoryCode("code");
        weightVO.setDimensionSubCategoryCnName("cnName");
        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setDimensionSubCategoryCode("code");
        actualCostVO.setDimensionSubCategoryCnName("cnName");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setDimensionSubCategoryCode("code");
        costIdxVO.setDimensionSubCategoryCnName("cnName");
        String dmsCnName = null;
        String result = Whitebox.invokeMethod(monthCommonService, "getSubCategory", yoyVO, weightVO, actualCostVO,
                costIdxVO, dmsCnName);
        Assert.assertNotNull(result);
    }

    @Test
    public void getSubDetailTest() throws Exception {
        DmFocMonthYoyVO yoyVO = new DmFocMonthYoyVO();
        yoyVO.setDimensionSubDetailCode("code");
        yoyVO.setDimensionSubDetailCnName("cnName");
        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setDimensionSubDetailCode("code");
        weightVO.setDimensionSubDetailCnName("cnName");
        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setDimensionSubDetailCode("code");
        actualCostVO.setDimensionSubDetailCnName("cnName");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setDimensionSubDetailCode("code");
        costIdxVO.setDimensionSubDetailCnName("cnName");
        String dmsCnName = null;
        String result = Whitebox.invokeMethod(monthCommonService, "getSubDetail", yoyVO, weightVO, actualCostVO,
                costIdxVO, dmsCnName);
        Assert.assertNotNull(result);
    }

    @Test
    public void getSpartTest() throws Exception {
        DmFocMonthYoyVO yoyVO = new DmFocMonthYoyVO();
        yoyVO.setDimensionSubDetailCode("code");
        yoyVO.setDimensionSubDetailCnName("cnName");
        yoyVO.setSpartCode("code");
        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setDimensionSubDetailCode("code");
        weightVO.setDimensionSubDetailCnName("cnName");
        weightVO.setSpartCode("code");
        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setDimensionSubDetailCode("code");
        actualCostVO.setDimensionSubDetailCnName("cnName");
        actualCostVO.setSpartCode("code");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setDimensionSubDetailCode("code");
        costIdxVO.setDimensionSubDetailCnName("cnName");
        costIdxVO.setSpartCode("code");
        String dmsCnName = null;
        String result = Whitebox.invokeMethod(monthCommonService, "getSpart", yoyVO, weightVO, actualCostVO,
                costIdxVO, dmsCnName);
        Assert.assertNotNull(result);
    }

    @Test
    public void setParamsTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setReverseViewFlag(true);
        List<String> purCodeList = new ArrayList<>();
        purCodeList.add("_##string");
        purCodeList.add("_##string1");
        monthAnalysisVO.setPurCodeList(purCodeList);
        monthAnalysisVO.setGroupCodeList(purCodeList);
        monthAnalysisVO.setProdRndTeamCodeList(purCodeList);
        List<Long> customIdList = new ArrayList<>();
        customIdList.add(1L);
        monthAnalysisVO.setCustomIdList(customIdList);
        monthAnalysisVO.setCombinaCodeList(purCodeList);
        Whitebox.invokeMethod(monthCommonService, "setParams", monthAnalysisVO);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @Test
    public void setParams1Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setReverseViewFlag(true);
        List<String> purCodeList = new ArrayList<>();
        monthAnalysisVO.setPurCodeList(purCodeList);
        monthAnalysisVO.setGroupCodeList(purCodeList);
        Whitebox.invokeMethod(monthCommonService, "setParams", monthAnalysisVO);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @Test
    public void setOtherParamsTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        List<String> parentCodeList = new ArrayList<>();
        parentCodeList.add("_##string");
        parentCodeList.add("_##string1");
        monthAnalysisVO.setCombParentCodeList(parentCodeList);
        monthAnalysisVO.setParentCodeList(parentCodeList);
        monthAnalysisVO.setDmsCodeList(parentCodeList);
        monthAnalysisVO.setSpartCodeList(parentCodeList);
        monthAnalysisVO.setL1NameList(parentCodeList);
        monthAnalysisVO.setL2NameList(parentCodeList);
        Whitebox.invokeMethod(monthCommonService, "setOtherParams", monthAnalysisVO);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @Test
    public void dataCompareTest() throws Exception {
        String startDate = "2024-01";
        String endDate = "2024-02";
        int result = Whitebox.invokeMethod(monthCommonService, "dataCompare", startDate, endDate);
        Assert.assertNotNull(result);
    }

    @Test
    public void dataCompare1Test() throws Exception {
        String startDate = "01";
        String endDate = "2024-02";
        int result = Whitebox.invokeMethod(monthCommonService, "dataCompare", startDate, endDate);
        Assert.assertNotNull(result);
    }

    @Test
    public void getMonthCountTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("IAS");
        Map<String, Long> startEndTime = new HashMap<>();
        startEndTime.put("start", 1L);
        startEndTime.put("end", 1L);
        Mockito.when(dmFocMadeMonthCostIdxDao.findStartEndTime(any(),any())).thenReturn(startEndTime);
        Mockito.when(commonService.findActualMonthNum(anyString())).thenReturn(202406L);
        Assert.assertNotNull(Whitebox.invokeMethod(monthCommonService, "getMonthCount", monthAnalysisVO));

        monthAnalysisVO.setCostType("M");
        Assert.assertNotNull(Whitebox.invokeMethod(monthCommonService, "getMonthCount", monthAnalysisVO));

        monthAnalysisVO.setCostType("T");
        Assert.assertNotNull(Whitebox.invokeMethod(monthCommonService, "getMonthCount", monthAnalysisVO));
    }

    @Test
    public void getMonthCount1Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setPeriodStartTime(0);
        Map<String, Long> startEndTime = new HashMap<>();
        startEndTime.put("start", 1L);
        startEndTime.put("end", 1L);
        monthAnalysisVO.setCostType(IndustryIndexEnum.COST_TYPE.T.getValue());
        Mockito.when(dmFocMadeMonthCostIdxDao.findStartEndTime(any(),any())).thenReturn(startEndTime);
        int result = Whitebox.invokeMethod(monthCommonService, "getMonthCount", monthAnalysisVO);
        Assert.assertNotNull(result);
    }

    @Test
    public void getIndustryCostYoyAndPopChart() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setCostType("P");
        List<String> ProdRndTeamCodeList = new ArrayList<>();
        ProdRndTeamCodeList.add("104364");
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setProdRndTeamCodeList(ProdRndTeamCodeList);
        monthAnalysisVO.setBasePeriodId(202201);
        PowerMockito.doReturn(91L).when(commonService).getVersionId(anyString(),anyString());
        List<DmFocMonthYoyVO> yoyList = new ArrayList<>();
        DmFocMonthYoyVO focMonthYoyVO = new DmFocMonthYoyVO();
        focMonthYoyVO.setBasePeriodId(202201L);
        focMonthYoyVO.setYoyRate(1.1d);
        yoyList.add(focMonthYoyVO);
        PowerMockito.doReturn(yoyList).when(dmFocMonthYoyDao).findDmFocMonthYoyVOList(monthAnalysisVO);

        ResultDataVO periodYearList2 = new ResultDataVO();
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList2);
        List<DmFocMonthYoyVO> popList = new ArrayList<>();
        DmFocMonthYoyVO focMonthPopVO = new DmFocMonthYoyVO();
        focMonthPopVO.setBasePeriodId(202201L);
        focMonthPopVO.setYoyRate(2.1d);
        popList.add(focMonthPopVO);
        when(dmFocMonthCostIdxDao.findActualMonthNum(anyString())).thenReturn(202406L);
        PowerMockito.doReturn(popList).when(dmFocMonthYoyDao).findDmFocMonthYoyVOList(monthAnalysisVO);
        Assertions.assertNotNull(monthCommonService.getIndustryCostYoyAndPopChart(monthAnalysisVO));

        monthAnalysisVO.setYoyOrPop("YOY");
        Assertions.assertNotNull(monthCommonService.getIndustryCostYoyAndPopChart(monthAnalysisVO));

        monthAnalysisVO.setYoyOrPop("POP");
        Assertions.assertNotNull(monthCommonService.getIndustryCostYoyAndPopChart(monthAnalysisVO));

        monthAnalysisVO.setCostType("M");
        ResultDataVO periodYearList = new ResultDataVO();
        List<String> yearList = new ArrayList<>();
        yearList.add("2013");
        periodYearList.setData(yearList);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList);
        Mockito.when(dmFocMadeMonthCostIdxDao.findStartEndTime(any(),any())).thenReturn(getStringLongMap());
        Mockito.when(dmFocMadeMonthYoyDao.findMadeMonthYoyVOList(any())).thenReturn(popList);
        Assertions.assertNotNull(monthCommonService.getIndustryCostYoyAndPopChart(monthAnalysisVO));

        monthAnalysisVO.setYoyOrPop("YOY");
        Assertions.assertNotNull(monthCommonService.getIndustryCostYoyAndPopChart(monthAnalysisVO));

        monthAnalysisVO.setYoyOrPop("");
        Assertions.assertNotNull(monthCommonService.getIndustryCostYoyAndPopChart(monthAnalysisVO));

        monthAnalysisVO.setCostType("T");
        ResultDataVO periodYearList1 = new ResultDataVO();
        periodYearList1.setData(yearList);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList1);
        Mockito.when(dmFocTotalMonthYoyDao.findTotalMonthYoyVOList(any())).thenReturn(popList);
        Assertions.assertNotNull(monthCommonService.getIndustryCostYoyAndPopChart(monthAnalysisVO));

        monthAnalysisVO.setIsMultipleSelect(true);
        Assertions.assertNotNull(monthCommonService.getIndustryCostYoyAndPopChart(monthAnalysisVO));
    }

    @Test
    public void getIndustryCostYoyAndPopChartForComb() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364_##885D");
        groupCodeList.add("104365");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setCostType("P");
        List<String> ProdRndTeamCodeList = new ArrayList<>();
        ProdRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(ProdRndTeamCodeList);
        monthAnalysisVO.setViewFlag("3");
        monthAnalysisVO.setVersionId(88L);
        monthAnalysisVO.setIsContainComb(true);
        monthAnalysisVO.setIsMultipleSelect(true);
        List<Long> list = new ArrayList<>();
        list.add(10L);
        monthAnalysisVO.setCustomIdList(list);
        ResultDataVO periodYearList = new ResultDataVO();
        List<String> yearList = new ArrayList<>();
        yearList.add("2013");
        periodYearList.setData(yearList);
        when(dmFocMonthCostIdxDao.findActualMonthNum(anyString())).thenReturn(202406L);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList);
        List<DmFocMonthYoyVO> combYoyVOList = new ArrayList<>();
        DmFocMonthYoyVO dmFocMonthYoyVO = new DmFocMonthYoyVO();
        dmFocMonthYoyVO.setCustomCnName("cus");
        dmFocMonthYoyVO.setGroupCnName("group");
        combYoyVOList.add(dmFocMonthYoyVO);
        Mockito.when(dmFocMonthYoyDao.findDmFocMonthCombYoyVOList(any())).thenReturn(combYoyVOList);
        Assertions.assertNotNull(monthCommonService.getIndustryCostYoyAndPopChart(monthAnalysisVO));

        monthAnalysisVO.setYoyOrPop("YOY");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        Assertions.assertNotNull(monthCommonService.getIndustryCostYoyAndPopChart(monthAnalysisVO));

        monthAnalysisVO.setYoyOrPop("POP");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        Assertions.assertNotNull(monthCommonService.getIndustryCostYoyAndPopChart(monthAnalysisVO));

        monthAnalysisVO.setCostType("M");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        ResultDataVO periodYearList1 = new ResultDataVO();
        periodYearList1.setData(yearList);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList1);
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        Mockito.when(dmFocMadeMonthCostIdxDao.findStartEndTime(any(),any())).thenReturn(getStringLongMap());
        Mockito.when(commonService.findActualMonthNum(any())).thenReturn(202406L);
        Mockito.when(dmFocMadeMonthYoyDao.findMadeMonthCombYoyVOList(any())).thenReturn(combYoyVOList);
        Assertions.assertNotNull(monthCommonService.getIndustryCostYoyAndPopChart(monthAnalysisVO));

        monthAnalysisVO.setYoyOrPop("YOY");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        Assertions.assertNotNull(monthCommonService.getIndustryCostYoyAndPopChart(monthAnalysisVO));

        monthAnalysisVO.setYoyOrPop("");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        Assertions.assertNotNull(monthCommonService.getIndustryCostYoyAndPopChart(monthAnalysisVO));
    }

    @Test
    public void getMonthYoyListForExp() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setCostType("P");
        List<String> ProdRndTeamCodeList = new ArrayList<>();
        ProdRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(ProdRndTeamCodeList);
        monthAnalysisVO.setBasePeriodId(202201);
        PowerMockito.doReturn(91L).when(commonService).getVersionId(anyString(),anyString());
        List<DmFocMonthYoyVO> allMonthYoyList = new ArrayList<>();
        DmFocMonthYoyVO focMonthYoyVO = new DmFocMonthYoyVO();
        focMonthYoyVO.setBasePeriodId(202201L);
        focMonthYoyVO.setGroupCode("1411D");
        focMonthYoyVO.setYoyPercent("10%");
        focMonthYoyVO.setYoyFlag("Y");
        allMonthYoyList.add(focMonthYoyVO);

        DmFocMonthYoyVO focMonthPopVO = new DmFocMonthYoyVO();
        focMonthPopVO.setBasePeriodId(202201L);
        focMonthPopVO.setGroupCode("1411D");
        focMonthPopVO.setYoyPercent("12%");
        focMonthPopVO.setYoyFlag("P");
        allMonthYoyList.add(focMonthPopVO);
        ResultDataVO periodYearList = new ResultDataVO();
        List<String> yearList = new ArrayList<>();
        yearList.add("2013");
        periodYearList.setData(yearList);
        Mockito.when(commonService.findActualMonthNum(anyString())).thenReturn(202406L);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList);
        PowerMockito.doReturn(allMonthYoyList).when(dmFocMonthYoyDao).findDmFocMonthYoyVOList(monthAnalysisVO);
        Assertions.assertNotNull(monthCommonService.getMonthYoyListForExp(monthAnalysisVO));

        monthAnalysisVO.setCostType("M");
        ResultDataVO periodYearList1 = new ResultDataVO();
        periodYearList1.setData(yearList);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList1);
        Mockito.when(dmFocMadeMonthCostIdxDao.findStartEndTime(any(),any())).thenReturn(getStringLongMap());
        Mockito.when(dmFocMadeMonthYoyDao.findMadeMonthYoyVOList(any())).thenReturn(allMonthYoyList);
        Assertions.assertNotNull(monthCommonService.getMonthYoyListForExp(monthAnalysisVO));

        monthAnalysisVO.setCostType("T");
        ResultDataVO periodYearList2 = new ResultDataVO();
        periodYearList2.setData(yearList);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList2);
        Mockito.when(dmFocTotalMonthYoyDao.findTotalMonthYoyVOList(any())).thenReturn(allMonthYoyList);
        Assertions.assertNotNull(monthCommonService.getMonthYoyListForExp(monthAnalysisVO));

    }


    @Test
    public void getMonthYoyListForExp1T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setViewFlag("0");
        List<String> ProdRndTeamCodeList = new ArrayList<>();
        ProdRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(ProdRndTeamCodeList);
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setIsContainComb(false);
        List<DmFocMonthYoyVO> allMonthYoyList = new ArrayList<>();
        DmFocMonthYoyVO focMonthYoyVO = new DmFocMonthYoyVO();
        focMonthYoyVO.setBasePeriodId(202201L);
        focMonthYoyVO.setGroupCode("1411D");
        focMonthYoyVO.setYoyPercent("10%");
        focMonthYoyVO.setYoyFlag("Y");
        allMonthYoyList.add(focMonthYoyVO);

        DmFocMonthYoyVO focMonthPopVO = new DmFocMonthYoyVO();
        focMonthPopVO.setBasePeriodId(202201L);
        focMonthPopVO.setGroupCode("1411D");
        focMonthPopVO.setYoyPercent("12%");
        focMonthPopVO.setYoyFlag("P");
        allMonthYoyList.add(focMonthPopVO);
        ResultDataVO periodYearList = new ResultDataVO();
        List<String> yearList = new ArrayList<>();
        yearList.add("2013");
        periodYearList.setData(yearList);
        Mockito.when(commonService.findActualMonthNum(anyString())).thenReturn(202406L);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList);
        PowerMockito.doReturn(allMonthYoyList).when(dmFocMonthYoyDao).findDmFocMonthYoyVOList(monthAnalysisVO);
        Assertions.assertNotNull(monthCommonService.getMonthYoyListForExp(monthAnalysisVO));
    }

    @Test
    public void getMonthYoyListForExp2T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364_##88855");
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setCostType("P");
        List<String> ProdRndTeamCodeList = new ArrayList<>();
        ProdRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(ProdRndTeamCodeList);
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setIsMultipleSelect(true);
        monthAnalysisVO.setIsContainComb(true);
        List<Long> list = new ArrayList<>();
        list.add(155L);
        monthAnalysisVO.setCustomIdList(list);
        monthAnalysisVO.setCombinaCodeList(ProdRndTeamCodeList);
        List<DmFocMonthYoyVO> allMonthYoyList = new ArrayList<>();
        DmFocMonthYoyVO focMonthYoyVO = new DmFocMonthYoyVO();
        focMonthYoyVO.setBasePeriodId(202201L);
        focMonthYoyVO.setGroupCode("1411D");
        focMonthYoyVO.setYoyPercent("10%");
        focMonthYoyVO.setYoyFlag("Y");
        focMonthYoyVO.setCustomCnName("cus");
        focMonthYoyVO.setGroupCnName("group");
        allMonthYoyList.add(focMonthYoyVO);

        DmFocMonthYoyVO focMonthPopVO = new DmFocMonthYoyVO();
        focMonthPopVO.setBasePeriodId(202201L);
        focMonthPopVO.setGroupCode("1411D");
        focMonthPopVO.setYoyPercent("12%");
        focMonthPopVO.setYoyFlag("P");
        focMonthPopVO.setCustomCnName("cus");
        focMonthPopVO.setGroupCnName("group");
        allMonthYoyList.add(focMonthPopVO);
        ResultDataVO periodYearList = new ResultDataVO();
        List<String> yearList = new ArrayList<>();
        yearList.add("2013");
        periodYearList.setData(yearList);
        Mockito.when(commonService.findActualMonthNum(anyString())).thenReturn(202406L);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList);
        PowerMockito.doReturn(allMonthYoyList).when(dmFocMonthYoyDao).findDmFocMonthYoyVOList(monthAnalysisVO);
        Mockito.when(dmFocMonthYoyDao.findDmFocMonthCombYoyVOList(any())).thenReturn(allMonthYoyList);
        Assertions.assertNotNull(monthCommonService.getMonthYoyListForExp(monthAnalysisVO));

        monthAnalysisVO.setCostType("M");
        ResultDataVO periodYearList1 = new ResultDataVO();
        periodYearList1.setData(yearList);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList1);
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        Mockito.when(dmFocMadeMonthCostIdxDao.findStartEndTime(any(),any())).thenReturn(getStringLongMap());
        Mockito.when(dmFocMadeMonthYoyDao.findMadeMonthCombYoyVOList(any())).thenReturn(allMonthYoyList);
        Assertions.assertNotNull(monthCommonService.getMonthYoyListForExp(monthAnalysisVO));

    }

    @Test
    public void getMonthYoyListForExp3T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364_##88855");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setViewFlag("0");
        List<String> ProdRndTeamCodeList = new ArrayList<>();
        ProdRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(ProdRndTeamCodeList);
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setIsMultipleSelect(true);
        monthAnalysisVO.setIsContainComb(false);
        List<Long> list = new ArrayList<>();
        list.add(155L);
        monthAnalysisVO.setCustomIdList(list);

        monthAnalysisVO.setCombinaCodeList(ProdRndTeamCodeList);


        List<DmFocMonthYoyVO> allMonthYoyList = new ArrayList<>();
        DmFocMonthYoyVO focMonthYoyVO = new DmFocMonthYoyVO();
        focMonthYoyVO.setBasePeriodId(202201L);
        focMonthYoyVO.setGroupCode("1411D");
        focMonthYoyVO.setYoyPercent("10%");
        focMonthYoyVO.setYoyFlag("Y");
        focMonthYoyVO.setCustomCnName("cus");
        focMonthYoyVO.setGroupCnName("group");
        allMonthYoyList.add(focMonthYoyVO);

        DmFocMonthYoyVO focMonthPopVO = new DmFocMonthYoyVO();
        focMonthPopVO.setBasePeriodId(202201L);
        focMonthPopVO.setGroupCode("1411D");
        focMonthPopVO.setYoyPercent("12%");
        focMonthPopVO.setYoyFlag("P");
        focMonthPopVO.setCustomCnName("cus");
        focMonthPopVO.setGroupCnName("group");
        allMonthYoyList.add(focMonthPopVO);
        ResultDataVO periodYearList = new ResultDataVO();
        List<String> yearList = new ArrayList<>();
        yearList.add("2013");
        periodYearList.setData(yearList);
        Mockito.when(commonService.findActualMonthNum(anyString())).thenReturn(202406L);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList);
        PowerMockito.doReturn(allMonthYoyList).when(dmFocMonthYoyDao).findDmFocMonthYoyVOList(monthAnalysisVO);
        Mockito.when(dmFocMonthYoyDao.findDmFocMonthCombYoyVOList(any())).thenReturn(allMonthYoyList);
        Assertions.assertNotNull(monthCommonService.getMonthYoyListForExp(monthAnalysisVO));
    }

    @Test
    public void reverseDataIsOk() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setIndustryOrg("IAS");
        Assertions.assertNotNull(monthCommonService.reverseDataIsOk(monthAnalysisVO));
    }

    @Test
    public void reverseDataIsOk2T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setIsMultipleSelect(true);
        List<String> list = new ArrayList<>();
        list.add("24324");
        monthAnalysisVO.setGroupCodeList(list);
        monthAnalysisVO.setIndustryOrg("IAS");
        Mockito.when(dmFocRecMonthCostIdxDao.findRecPriceIndexByBasePeriodId(any())).thenReturn(48);
        Assertions.assertNotNull(monthCommonService.reverseDataIsOk(monthAnalysisVO));
    }

    @Test
    public void reverseDataIsOk3T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        List<String> list = new ArrayList<>();
        list.add("56");
        monthAnalysisVO.setSubGroupCodeList(list);
        Mockito.when(dmFocRecMonthCostIdxDao.findRecPriceIndexByBasePeriodId(any())).thenReturn(48);
        Assertions.assertNotNull(monthCommonService.reverseDataIsOk(monthAnalysisVO));
    }

    @Test
    public void reverseDataIsOk4T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<String> list = new ArrayList<>();
        list.add("56");
        monthAnalysisVO.setSubGroupCodeList(list);
        monthAnalysisVO.setIndustryOrg("IAS");
        Assertions.assertNotNull(monthCommonService.reverseDataIsOk(monthAnalysisVO));
    }

    @Test
    public void isDataIsOk() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setIndustryOrg("IAS");
        Assertions.assertNotNull(monthCommonService.isDataIsOk(monthAnalysisVO));
    }

    @Test
    public void isDataIsOk2T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setIsMultipleSelect(true);
        List<String> list = new ArrayList<>();
        list.add("24324");
        monthAnalysisVO.setGroupCodeList(list);
        monthAnalysisVO.setIndustryOrg("IAS");
        Mockito.when(dmFocMonthCostIdxDao.findPriceIndexByBasePeriodId(any())).thenReturn(48);
        Assertions.assertNotNull(monthCommonService.isDataIsOk(monthAnalysisVO));
    }

    @Test
    public void isDataIsOk3T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<String> list = new ArrayList<>();
        list.add("56");
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setSubGroupCodeList(list);
        Mockito.when(dmFocMonthCostIdxDao.findPriceIndexByBasePeriodId(any())).thenReturn(48);
        Assertions.assertNotNull(monthCommonService.isDataIsOk(monthAnalysisVO));
    }

    @Test
    public void isDataIsOk4T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<String> list = new ArrayList<>();
        list.add("56");
        monthAnalysisVO.setSubGroupCodeList(list);
        monthAnalysisVO.setIndustryOrg("IAS");
        Assertions.assertNotNull(monthCommonService.isDataIsOk(monthAnalysisVO));
    }

    @Test
    public void isCombDataIsOk() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setIndustryOrg("IAS");
        Assertions.assertNotNull(monthCommonService.isCombDataIsOk(monthAnalysisVO));
    }

    @Test
    public void isCombDataIsOk2T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setIsMultipleSelect(true);
        List<String> list = new ArrayList<>();
        list.add("24324");
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setGroupCodeList(list);
        Mockito.when(dmFocMonthCostIdxDao.findCombPriceIndexByBasePeriodId(any())).thenReturn(48);
        Assertions.assertNotNull(monthCommonService.isCombDataIsOk(monthAnalysisVO));
    }

    @Test
    public void isCombDataIsOk3T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<String> list = new ArrayList<>();
        list.add("56");
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setCombinaSubGroupCodeList(list);
        Mockito.when(dmFocMonthCostIdxDao.findCombPriceIndexByBasePeriodId(any())).thenReturn(48);
        Assertions.assertNotNull(monthCommonService.isCombDataIsOk(monthAnalysisVO));
    }

    @Test
    public void isCombDataIsOk4T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<String> list = new ArrayList<>();
        list.add("56");
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setCombinaSubGroupCodeList(list);
        Assertions.assertNotNull(monthCommonService.isCombDataIsOk(monthAnalysisVO));
    }

    // 通用
    @Test
    public void mutilSelectGroupCnName() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="indexCurrentLevel";
        String typeFlag="";

        monthAnalysisVO.setGroupLevel("MODL");
        monthAnalysisVO.setGranularityType("U");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName2T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="mutilIndexCost";
        String typeFlag="search";

        monthAnalysisVO.setGroupLevel("MODL");
        monthAnalysisVO.setGranularityType("U");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName3T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="mutilIndexCost";
        String typeFlag="";

        monthAnalysisVO.setGroupLevel("MODL");
        monthAnalysisVO.setGranularityType("U");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    // 盈利
    @Test
    public void mutilSelectGroupCnName4T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="mutilIndexCost";
        String typeFlag="search";

        monthAnalysisVO.setGroupLevel("MODL");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setMultiLevel("L1");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName7T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="mutilIndexCost";
        String typeFlag="search";

        monthAnalysisVO.setGroupLevel("MODL");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setMultiLevel("L1");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName5T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="mutilIndexCost";
        String typeFlag="";

        monthAnalysisVO.setGroupLevel("MODL");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setMultiLevel("L1");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName6T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="mutilIndexCost";
        String typeFlag="";

        monthAnalysisVO.setGroupLevel("MODL");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setMultiLevel("L1");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName8T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="indexCurrentLevel";
        String typeFlag="search";

        monthAnalysisVO.setGroupLevel("MODL");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setMultiLevel("L1");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName9T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="indexCurrentLevel";
        String typeFlag="search";

        monthAnalysisVO.setGroupLevel("MODL");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setMultiLevel("L1");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    // L2
    @Test
    public void mutilSelectGroupCnName10T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="mutilIndexCost";
        String typeFlag="search";

        monthAnalysisVO.setGroupLevel("MODL");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setMultiLevel("L2");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他2");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName11T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="mutilIndexCost";
        String typeFlag="search";

        monthAnalysisVO.setGroupLevel("MODL");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setMultiLevel("L2");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他2");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName12T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="mutilIndexCost";
        String typeFlag="";

        monthAnalysisVO.setGroupLevel("MODL");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setMultiLevel("L2");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他2");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName13T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="mutilIndexCost";
        String typeFlag="";

        monthAnalysisVO.setGroupLevel("MODL");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setMultiLevel("L2");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他2");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName14T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="indexCurrentLevel";
        String typeFlag="search";

        monthAnalysisVO.setGroupLevel("ITEM");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setMultiLevel("L2");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他2");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName15T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="indexCurrentLevel";
        String typeFlag="search";

        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setMultiLevel("L2");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName16T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="indexCurrentLevel";
        String typeFlag="search";

        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setMultiLevel("L3");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    // 量纲
    @Test
    public void mutilSelectGroupCnName17T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="indexCurrentLevel";
        String typeFlag="search";

        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setMultiLevel("L3");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName18T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="mutilIndexCost";
        String typeFlag="search";

        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setMultiLevel("L3");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName19T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="mutilIndexCost";
        String typeFlag="";

        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setMultiLevel("L3");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName20T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="weightList";
        String typeFlag="search";

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");
        weightList.add(weightVO);

        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setMultiLevel("L3");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName21T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="weightList";
        String typeFlag="searchs";

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");
        weightList.add(weightVO);

        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setMultiLevel("L3");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName22T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="weightList";
        String typeFlag="search";

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");
        weightList.add(weightVO);

        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setMultiLevel("L3");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName23T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="weightList";
        String typeFlag="exp";

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");
        weightList.add(weightVO);

        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setMultiLevel("L3");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName24T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="heatMapList";
        String typeFlag="search";

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");
        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setMultiLevel("L3");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName25T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="heatMapList";
        String typeFlag="exp";

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");
        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setMultiLevel("L3");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName26T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="heatMapList";
        String typeFlag="exp";

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");
        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setMultiLevel("L3");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName27T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="heatMapList";
        String typeFlag="search";

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");
        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setMultiLevel("L3");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName28T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="weightList";
        String typeFlag="search";

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");
        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setMultiLevel("L3");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName29T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="weightList";
        String typeFlag="search";

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");
        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setMultiLevel("L1");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName30T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="weightList";
        String typeFlag="search";

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");
        weightVO.setL1Name("其他");
        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setMultiLevel("L1");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName31T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="weightList";
        String typeFlag="search";

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");
        weightVO.setL1Name("其他");
        weightVO.setL2Name("其他");
        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setMultiLevel("L2");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName32T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="weightList";
        String typeFlag="search";

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");
        weightVO.setL1Name("其他");
        weightVO.setL2Name("其他2");
        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setMultiLevel("L2");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }


    @Test
    public void mutilSelectGroupCnName33T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="heatMapList";
        String typeFlag="search";

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");
        weightVO.setL1Name("其他");
        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        actualCostVO.setL1Name("其他2");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setMultiLevel("L1");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName34T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="heatMapList";
        String typeFlag="search";

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");
        weightVO.setL1Name("其他");
        weightVO.setL2Name("其他");
        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        actualCostVO.setL1Name("其他");
        actualCostVO.setL2Name("其他");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setMultiLevel("L2");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName35T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="heatMapList";
        String typeFlag="search";

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");

        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        actualCostVO.setL1Name("其他");
        actualCostVO.setL2Name("其他2");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setMultiLevel("L2");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName36T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="heatMapList";
        String typeFlag="search";

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");

        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        actualCostVO.setL1Name("其他");
        actualCostVO.setL2Name("其他2");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setMultiLevel("L3");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    // yoyAndPopIndexCost
    @Test
    public void mutilSelectGroupCnName37T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="yoyAndPopIndexCost";
        String typeFlag="search";

        DmFocMonthYoyVO yoyVO = new DmFocMonthYoyVO();
        yoyVO.setYoyRate(0.6);
        yoyAndPopIndexVOList.add(yoyVO);

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");

        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        actualCostVO.setL1Name("其他");
        actualCostVO.setL2Name("其他2");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setMultiLevel("L2");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName38T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="yoyAndPopIndexCost";
        String typeFlag="search";

        DmFocMonthYoyVO yoyVO = new DmFocMonthYoyVO();
        yoyVO.setYoyRate(0.6);
        yoyAndPopIndexVOList.add(yoyVO);

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");

        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        actualCostVO.setL1Name("其他");
        actualCostVO.setL2Name("其他2");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setMultiLevel("L1");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName39T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="yoyAndPopIndexCost";
        String typeFlag="search";

        DmFocMonthYoyVO yoyVO = new DmFocMonthYoyVO();
        yoyVO.setYoyRate(0.6);
        yoyVO.setL1Name("其他");
        yoyAndPopIndexVOList.add(yoyVO);

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");

        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        actualCostVO.setL1Name("其他");
        actualCostVO.setL2Name("其他2");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setMultiLevel("L1");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName40T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="yoyAndPopIndexCost";
        String typeFlag="search";

        DmFocMonthYoyVO yoyVO = new DmFocMonthYoyVO();
        yoyVO.setYoyRate(0.6);
        yoyVO.setL1Name("其他");
        yoyVO.setL2Name("其他");
        yoyAndPopIndexVOList.add(yoyVO);

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");

        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        actualCostVO.setL1Name("其他");
        actualCostVO.setL2Name("其他2");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setMultiLevel("L2");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName41T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="yoyAndPopIndexCost";
        String typeFlag="search";

        DmFocMonthYoyVO yoyVO = new DmFocMonthYoyVO();
        yoyVO.setYoyRate(0.6);
        yoyVO.setL1Name("其他");
        yoyVO.setL2Name("其他2");
        yoyAndPopIndexVOList.add(yoyVO);

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");

        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        actualCostVO.setL1Name("其他");
        actualCostVO.setL2Name("其他2");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setMultiLevel("L2");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName42T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="yoyAndPopIndexCost";
        String typeFlag="search";

        DmFocMonthYoyVO yoyVO = new DmFocMonthYoyVO();
        yoyVO.setYoyRate(0.6);
        yoyVO.setL1Name("其他");
        yoyVO.setL2Name("其他2");
        yoyAndPopIndexVOList.add(yoyVO);

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");

        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        actualCostVO.setL1Name("其他");
        actualCostVO.setL2Name("其他2");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setMultiLevel("L3");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName43T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="yoyAndPopIndexCost";
        String typeFlag="search";

        DmFocMonthYoyVO yoyVO = new DmFocMonthYoyVO();
        yoyVO.setYoyRate(0.6);
        yoyVO.setL1Name("其他");
        yoyVO.setL2Name("其他2");
        yoyAndPopIndexVOList.add(yoyVO);

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");

        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        actualCostVO.setL1Name("其他");
        actualCostVO.setL2Name("其他2");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setMultiLevel("L3");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName44T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="yoyAndPopIndexCost";
        String typeFlag="search";

        DmFocMonthYoyVO yoyVO = new DmFocMonthYoyVO();
        yoyVO.setYoyRate(0.6);
        yoyVO.setL1Name("其他");
        yoyVO.setL2Name("其他2");
        yoyAndPopIndexVOList.add(yoyVO);

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");

        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        actualCostVO.setL1Name("其他");
        actualCostVO.setL2Name("其他2");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("L1");
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setMultiLevel("L3");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName45T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="yoyAndPopIndexCost";
        String typeFlag="search";

        DmFocMonthYoyVO yoyVO = new DmFocMonthYoyVO();
        yoyVO.setYoyRate(0.6);
        yoyVO.setL1Name("其他");
        yoyVO.setL2Name("其他");
        yoyAndPopIndexVOList.add(yoyVO);

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");

        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        actualCostVO.setL1Name("其他");
        actualCostVO.setL2Name("其他2");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("L2");
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setMultiLevel("L3");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName46T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="mutilIndexCost";
        String typeFlag="search";

        DmFocMonthYoyVO yoyVO = new DmFocMonthYoyVO();
        yoyVO.setYoyRate(0.6);
        yoyVO.setL1Name("其他");
        yoyVO.setL2Name("其他");
        yoyAndPopIndexVOList.add(yoyVO);

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");
        weightVO.setL1Name("其他");
        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        actualCostVO.setL1Name("其他");
        actualCostVO.setL2Name("其他2");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("L1");
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setMultiLevel("L3");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName47T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="heatMapList";
        String typeFlag="search";

        DmFocMonthYoyVO yoyVO = new DmFocMonthYoyVO();
        yoyVO.setYoyRate(0.6);
        yoyVO.setL1Name("其他");
        yoyVO.setL2Name("其他");
        yoyAndPopIndexVOList.add(yoyVO);

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");
        weightVO.setL1Name("其他");
        weightVO.setL2Name("其他");
        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        actualCostVO.setL1Name("其他");
        actualCostVO.setL2Name("其他");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("L2");
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setMultiLevel("L3");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName48T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="yoyAndPopIndexCost";
        String typeFlag="search";

        DmFocMonthYoyVO yoyVO = new DmFocMonthYoyVO();
        yoyVO.setYoyRate(0.6);
        yoyVO.setL1Name("其他");
        yoyVO.setL2Name("其他");
        yoyAndPopIndexVOList.add(yoyVO);

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");
        weightVO.setL1Name("其他");
        weightVO.setL2Name("其他");
        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        actualCostVO.setL1Name("其他");
        actualCostVO.setL2Name("其他");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("DIMENSION");
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setMultiLevel("L3");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName49T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="yoyAndPopIndexCost";
        String typeFlag="search";

        DmFocMonthYoyVO yoyVO = new DmFocMonthYoyVO();
        yoyVO.setYoyRate(0.6);
        yoyVO.setL1Name("其他");
        yoyVO.setL2Name("其他");
        yoyAndPopIndexVOList.add(yoyVO);

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");
        weightVO.setL1Name("其他");
        weightVO.setL2Name("其他");
        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        actualCostVO.setL1Name("其他");
        actualCostVO.setL2Name("其他");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("SUBCATEGORY");
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setMultiLevel("L3");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName50T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="indexCurrentLevel";
        String typeFlag="search";

        DmFocMonthYoyVO yoyVO = new DmFocMonthYoyVO();
        yoyVO.setYoyRate(0.6);
        yoyVO.setL1Name("其他");
        yoyVO.setL2Name("其他");
        yoyAndPopIndexVOList.add(yoyVO);

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");
        weightVO.setL1Name("其他");
        weightVO.setL2Name("其他");
        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        actualCostVO.setL1Name("其他");
        actualCostVO.setL2Name("其他");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("SUB_DETAIL");
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setMultiLevel("L3");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName51T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="mutilIndexCost";
        String typeFlag="search";

        DmFocMonthYoyVO yoyVO = new DmFocMonthYoyVO();
        yoyVO.setYoyRate(0.6);
        yoyVO.setL1Name("其他");
        yoyVO.setL2Name("其他");
        yoyAndPopIndexVOList.add(yoyVO);

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");
        weightVO.setL1Name("其他");
        weightVO.setL2Name("其他");
        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        actualCostVO.setL1Name("其他");
        actualCostVO.setL2Name("其他");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("SUB_DETAIL");
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setMultiLevel("L3");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName52T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="mutilIndexCost";
        String typeFlag="search";

        DmFocMonthYoyVO yoyVO = new DmFocMonthYoyVO();
        yoyVO.setYoyRate(0.6);
        yoyVO.setL1Name("其他");
        yoyVO.setL2Name("其他");
        yoyAndPopIndexVOList.add(yoyVO);

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");
        weightVO.setL1Name("其他");
        weightVO.setL2Name("其他");
        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        actualCostVO.setL1Name("其他");
        actualCostVO.setL2Name("其他");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("DIMENSION");
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setMultiLevel("L3");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName53T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="heatMapList";
        String typeFlag="search";

        DmFocMonthYoyVO yoyVO = new DmFocMonthYoyVO();
        yoyVO.setYoyRate(0.6);
        yoyVO.setL1Name("其他");
        yoyVO.setL2Name("其他");
        yoyAndPopIndexVOList.add(yoyVO);

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");
        weightVO.setL1Name("其他");
        weightVO.setL2Name("其他");
        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        actualCostVO.setL1Name("其他");
        actualCostVO.setL2Name("其他");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("SUBCATEGORY");
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setMultiLevel("L3");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName54T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="weightList";
        String typeFlag="search";

        DmFocMonthYoyVO yoyVO = new DmFocMonthYoyVO();
        yoyVO.setYoyRate(0.6);
        yoyVO.setL1Name("其他");
        yoyVO.setL2Name("其他");
        yoyAndPopIndexVOList.add(yoyVO);

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");
        weightVO.setL1Name("其他");
        weightVO.setL2Name("其他");
        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        actualCostVO.setL1Name("其他");
        actualCostVO.setL2Name("其他");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("SUBCATEGORY");
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setMultiLevel("L3");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName55T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="weightList";
        String typeFlag="search";

        DmFocMonthYoyVO yoyVO = new DmFocMonthYoyVO();
        yoyVO.setYoyRate(0.6);
        yoyVO.setL1Name("其他");
        yoyVO.setL2Name("其他");
        yoyAndPopIndexVOList.add(yoyVO);

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");
        weightVO.setL1Name("其他");
        weightVO.setL2Name("其他");
        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        actualCostVO.setL1Name("其他");
        actualCostVO.setL2Name("其他");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("DIMENSION");
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setMultiLevel("L3");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName56T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="weightList";
        String typeFlag="search";

        DmFocMonthYoyVO yoyVO = new DmFocMonthYoyVO();
        yoyVO.setYoyRate(0.6);
        yoyVO.setL1Name("其他");
        yoyVO.setL2Name("其他");
        yoyAndPopIndexVOList.add(yoyVO);

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");
        weightVO.setL1Name("其他");
        weightVO.setL2Name("其他");
        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        actualCostVO.setL1Name("其他");
        actualCostVO.setL2Name("其他");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("SUB_DETAIL");
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setMultiLevel("L3");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName57T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="weightList";
        String typeFlag="SEARCH";

        DmFocMonthYoyVO yoyVO = new DmFocMonthYoyVO();
        yoyVO.setYoyRate(0.6);
        yoyVO.setL1Name("其他");
        yoyVO.setL2Name("其他");
        yoyAndPopIndexVOList.add(yoyVO);

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");
        weightVO.setL1Name("其他");
        weightVO.setL2Name("其他");
        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        actualCostVO.setL1Name("其他");
        actualCostVO.setL2Name("其他");
        heatMapList.add(actualCostVO);

        //monthAnalysisVO.setGroupLevel("SUB_DETAIL");
        monthAnalysisVO.setGroupLevel("ITEM");
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setMultiLevel("L3");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName58T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="weightList";
        String typeFlag="SEARCH";

        DmFocMonthYoyVO yoyVO = new DmFocMonthYoyVO();
        yoyVO.setYoyRate(0.6);
        yoyVO.setL1Name("其他");
        yoyVO.setL2Name("其他");
        yoyAndPopIndexVOList.add(yoyVO);

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");
        weightVO.setL1Name("其他");
        weightVO.setL2Name("其他");
        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        actualCostVO.setL1Name("其他");
        actualCostVO.setL2Name("其他");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("ITEM");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setMultiLevel("L3");
        monthAnalysisVO.setParentLevel("L1");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void mutilSelectGroupCnName59T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        List<DmFocMonthCostIdxVO> priceIndexVOList=new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList=new ArrayList<>();
        List< DmFocMonthWeightVO > weightList=new ArrayList<>();
        List< DmFocActualCostVO > heatMapList=new ArrayList<>();

        String methodFlag="weightList";
        String typeFlag="SEARCH";

        DmFocMonthYoyVO yoyVO = new DmFocMonthYoyVO();
        yoyVO.setYoyRate(0.6);
        yoyVO.setL1Name("其他");
        yoyVO.setL2Name("其他");
        yoyAndPopIndexVOList.add(yoyVO);

        DmFocMonthWeightVO weightVO = new DmFocMonthWeightVO();
        weightVO.setProdRndTeamCnName("prod");
        weightVO.setDmsCode("3366");
        weightVO.setL1Name("其他");
        weightVO.setL2Name("其他");
        weightList.add(weightVO);

        DmFocActualCostVO actualCostVO = new DmFocActualCostVO();
        actualCostVO.setGroupCnName("cn");
        actualCostVO.setL1Name("其他");
        actualCostVO.setL2Name("其他");
        heatMapList.add(actualCostVO);

        monthAnalysisVO.setGroupLevel("ITEM");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setMultiLevel("L3");
        monthAnalysisVO.setParentLevel("L2");
        DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
        costIdxVO.setProdRndTeamCnName("prod");
        costIdxVO.setGroupCnName("group");
        costIdxVO.setL1Name("其他");
        costIdxVO.setL2Name("其他");
        priceIndexVOList.add(costIdxVO);
        monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,
                weightList,heatMapList,methodFlag);
        assertThatNoException();
    }

    @Test
    public void hasCombNormalDataIsOk() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setIsMultipleSelect(true);
        List<String> list = new ArrayList<>();
        list.add("24324");
        monthAnalysisVO.setGroupCodeList(list);
        boolean b = monthCommonService.hasCombNormalDataIsOk(monthAnalysisVO);
        Assert.assertFalse(b);
    }

    @Test
    public void hasCombNormalDataIsOk2t() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        List<String> list = new ArrayList<>();
        list.add("8814H");
        monthAnalysisVO.setSubGroupCodeList(list);
        Assert.assertTrue(monthCommonService.hasCombNormalDataIsOk(monthAnalysisVO));
    }

    @Test
    public void hasCombNormalDataIsOk3t() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        List<String> list = new ArrayList<>();
        list.add("8814H");
        monthAnalysisVO.setSubGroupCodeList(list);
        monthAnalysisVO.setCostType("P");
        List<DmFocMonthCostIdxVO> priceIndexNormalChartByMultiDim=new ArrayList<>();
        for (int i=0; i<50;i++){
            DmFocMonthCostIdxVO costIdxVO = new DmFocMonthCostIdxVO();
            costIdxVO.setL1Name("镭射"+i);
            priceIndexNormalChartByMultiDim.add(costIdxVO);
        }
        Mockito.when(commonService.findActualMonthNum(anyString())).thenReturn(202406L);
        when(dmFocMonthCostIdxDao.findPriceIndexNormalChartByMultiDim(any())).thenReturn(priceIndexNormalChartByMultiDim);
        boolean b = monthCommonService.hasCombNormalDataIsOk(monthAnalysisVO);
        Assert.assertTrue(b);

        monthAnalysisVO.setCostType(IndustryIndexEnum.COST_TYPE.M.getValue());
        when(dmFocMadeMonthCostIdxDao.findMadePriceIndexNormalByMultiDim(any())).thenReturn(priceIndexNormalChartByMultiDim);
        boolean flag = monthCommonService.hasCombNormalDataIsOk(monthAnalysisVO);
        Assert.assertTrue(flag);
    }

    @Test
    public void heatMapMutilSelectGroupCnName() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("L1");
        List<DmFocActualCostVO> heatMapList = new ArrayList<>();
        DmFocActualCostVO dmFocActualCostVO = new DmFocActualCostVO();
        dmFocActualCostVO.setGroupCnName("颗粒度L1");
        heatMapList.add(dmFocActualCostVO);
        monthCommonService.heatMapMutilSelectGroupCnName(monthAnalysisVO, heatMapList);
        Assert.assertNull(null);

        monthAnalysisVO.setGroupLevel("L2");
        dmFocActualCostVO.setGroupCnName("颗粒度L2");
        heatMapList.clear();
        heatMapList.add(dmFocActualCostVO);
        monthCommonService.heatMapMutilSelectGroupCnName(monthAnalysisVO, heatMapList);
        Assert.assertNull(null);

        monthAnalysisVO.setGroupLevel("DIMENSION");
        dmFocActualCostVO.setGroupCnName("量纲");
        heatMapList.clear();
        heatMapList.add(dmFocActualCostVO);
        monthCommonService.heatMapMutilSelectGroupCnName(monthAnalysisVO, heatMapList);
        Assert.assertNull(null);

        monthAnalysisVO.setGroupLevel("SUBCATEGORY");
        dmFocActualCostVO.setGroupCnName("量纲子类");
        heatMapList.clear();
        heatMapList.add(dmFocActualCostVO);
        monthCommonService.heatMapMutilSelectGroupCnName(monthAnalysisVO, heatMapList);
        Assert.assertNull(null);

        monthAnalysisVO.setGroupLevel("SUB_DETAIL");
        dmFocActualCostVO.setGroupCnName("量纲子类明细");
        heatMapList.clear();
        heatMapList.add(dmFocActualCostVO);
        monthCommonService.heatMapMutilSelectGroupCnName(monthAnalysisVO, heatMapList);
        Assert.assertNull(null);

        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setGranularityType("U");
        dmFocActualCostVO.setGroupCnName("专项采购认证部");
        heatMapList.clear();
        heatMapList.add(dmFocActualCostVO);
        monthCommonService.heatMapMutilSelectGroupCnName(monthAnalysisVO, heatMapList);
        Assert.assertNull(null);

        monthAnalysisVO.setGranularityType("P");
        dmFocActualCostVO.setL1Name("L1Name");
        dmFocActualCostVO.setL2Name("L2Name");
        dmFocActualCostVO.setDimensionCode("123");
        dmFocActualCostVO.setDimensionCnName("test");
        dmFocActualCostVO.setDimensionSubCategoryCode("123");
        dmFocActualCostVO.setDimensionSubCategoryCnName("test");
        dmFocActualCostVO.setDimensionSubDetailCode("123");
        dmFocActualCostVO.setDimensionSubDetailCnName("test");
        heatMapList.clear();
        heatMapList.add(dmFocActualCostVO);
        monthCommonService.heatMapMutilSelectGroupCnName(monthAnalysisVO, heatMapList);
        Assert.assertNull(null);

        monthAnalysisVO.setGranularityType("D");
        monthCommonService.heatMapMutilSelectGroupCnName(monthAnalysisVO, heatMapList);
        Assert.assertNull(null);
    }

    @Test
    public void splicingWeightCodeAndNameTest() throws Exception {
        List<DmFocMonthWeightVO> weightList = getWeightList();
        Whitebox.invokeMethod(monthCommonService, "splicingWeightCodeAndName", weightList);
        Assert.assertNotNull(weightList);
    }

    @Test
    public void splicingWeightParentCodeAndNameTest() throws Exception {
        List<DmFocMonthWeightVO> weightList = getWeightList();
        Whitebox.invokeMethod(monthCommonService, "splicingWeightParentCodeAndName", weightList);
        Assert.assertNotNull(weightList);
    }

    @Test
    public void splicingHeatMapCodeAndNameTest() throws Exception {
        List<DmFocActualCostVO> heatMapList = getHeatList();
        Whitebox.invokeMethod(monthCommonService, "splicingHeatMapCodeAndName", heatMapList);
        Assert.assertNotNull(heatMapList);
    }

    @Test
    public void coaParentCnNameTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setParentLevel("COA");
        List<DmFocMonthCostIdxVO> priceIndexVOList = getIndexVOList();
        List<DmFocActualCostVO> heatMapList = getHeatList();
        List<DmFocMonthWeightVO> weightList = getWeightList();
        Whitebox.invokeMethod(monthCommonService, "coaParentCnName", monthAnalysisVO, priceIndexVOList, heatMapList, weightList);
        Assert.assertNotNull(monthAnalysisVO);
    }

    @Test
    public void coaGroupCnNameTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("COA");
        List<DmFocMonthCostIdxVO> priceIndexVOList = getIndexVOList();
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList = getYoyAndPopIndexVOList();
        Whitebox.invokeMethod(monthCommonService, "coaGroupCnName", monthAnalysisVO, priceIndexVOList, yoyAndPopIndexVOList);
        Assert.assertNotNull(monthAnalysisVO);
    }

}