/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.dao;

import com.huawei.it.fcst.industry.price.vo.month.PriceMonthAnalysisVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * IctMonthCostIdxDao Interface
 *
 * <AUTHOR>
 * @since 2024-07-05
 */
public interface IPriceMonthCostIdxDao {

    /**
     * 查询综合指数分析-定价指数图（多子项）正常维度多选下拉框
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<PriceMonthAnalysisVO> findMultiBoxList(@Param("monthAnalysisVO") PriceMonthAnalysisVO monthAnalysisVO);

    /**
     * 查询综合指数分析-定价指数图（多子项）虚化维度多选下拉框
     *
     * @param monthAnalysisVO 查询参数VO
     * @param pageVO 分页对象VO
     * @return PagedResult
     */
    PagedResult<PriceMonthAnalysisVO> findBlurMultiBoxList(@Param("monthAnalysisVO") PriceMonthAnalysisVO monthAnalysisVO, @Param("pageVO") PageVO pageVO);

    /**
     * 查询实际数的月份
     *
     * @return 实际数的月份
     */
    String findActualMonth();

    /**
     * 查询定价指数
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<PriceMonthAnalysisVO> findPriceIndexVOList(@Param("monthAnalysisVO") PriceMonthAnalysisVO monthAnalysisVO);


    /**
     * 查询定价指数（虚化）
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list PriceMonthAnalysisVOs
     */
    List<PriceMonthAnalysisVO> findBlurPriceIndexVOList(@Param("monthAnalysisVO") PriceMonthAnalysisVO monthAnalysisVO);

    /**
     * 查询定价指数（多子项）
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<PriceMonthAnalysisVO> findMultiPriceIndexVOList(@Param("monthAnalysisVO") PriceMonthAnalysisVO monthAnalysisVO);


    /**
     * 查询定价指数（多子项）虚化
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<PriceMonthAnalysisVO> findBlurMultiPriceIndexVOList(@Param("monthAnalysisVO") PriceMonthAnalysisVO monthAnalysisVO);

    /**
     * 查询默认基期
     *
     * @param monthAnalysisVO
     * @return basePeriodId
     */
    String findBasePeriodId(PriceMonthAnalysisVO monthAnalysisVO);

    int findCostIndexCount(@Param("monthAnalysisVO") PriceMonthAnalysisVO monthAnalysisVO);

    int findBlurIndexCount(@Param("monthAnalysisVO") PriceMonthAnalysisVO monthAnalysisVO);

    int findMultiCostIndexCount(@Param("monthAnalysisVO") PriceMonthAnalysisVO monthAnalysisVO);

    int findBlurMultiIndexCount(@Param("monthAnalysisVO") PriceMonthAnalysisVO monthAnalysisVO);

    /**
     * 定价指数-综合指数分析-切换基期
     *
     * @param monthAnalysisVO 页面入参VO
     * @return String 成功-SUCCESS，失败-FAILED
     */
    String callFuncRefreshData(PriceMonthAnalysisVO monthAnalysisVO);

}