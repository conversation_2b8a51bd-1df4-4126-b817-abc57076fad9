/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.service.config;

import com.huawei.it.fcst.industry.pbi.vo.config.DmFcstIctProdMainCodeDimVO;
import com.huawei.it.fcst.industry.pbi.vo.config.IctProdMainCodeDimVO;
import com.huawei.it.fcst.industry.pbi.vo.version.DmFcstVersionInfoVO;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.cxf.jaxrs.ext.multipart.Multipart;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.Consumes;
import javax.ws.rs.FormParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;

/**
 * 产业成本指数（ICT）配置管理页面API接口类
 *
 * <AUTHOR>
 * @since 2024-07-10
 */
@Path("/ictConfigManagement")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface IIctConfigManagementService {

    /**
     * [查询版本列表信息]
     *
     * @return ResultDataVO
     * @throws ApplicationException
     */
    @POST
    @Path("/version/list")
    ResultDataVO getVersionList(DmFcstVersionInfoVO versionInfoVO) throws ApplicationException;

    /**
     * [查询主力产品主力编码各层级下拉框列表]
     *
     * @return ResultDataVO
     * @throws ApplicationException
     */
    @POST
    @Path("/mainCode/dropbox/list")
    ResultDataVO getMainCodeDropboxList(IctProdMainCodeDimVO prodMainCodeDimVO) throws ApplicationException;

    /**
     * [查询配置管理-主力产品主力编码-新增/编辑时的各层级下拉框列表]
     *
     * @return ResultDataVO
     * @throws ApplicationException
     */
    @POST
    @Path("/mainCode/edit/dropbox/list")
    ResultDataVO getMainCodeEditDropboxList(IctProdMainCodeDimVO prodMainCodeDimVO) throws ApplicationException;

    /**
     * [分页查询配置管理-主力产品主力编码-新增/编辑时SPART层级下拉框列表]
     *
     * @return ResultDataVO
     * @throws ApplicationException
     */
    @POST
    @Path("/mainCode/spart/dropbox/list")
    ResultDataVO getMainCodeSpartDropboxList(IctProdMainCodeDimVO prodMainCodeDimVO) throws ApplicationException;

    /**
     * [分页查询主力产品主力编码列表]
     *
     * @return ResultDataVO
     * @throws ApplicationException
     */
    @POST
    @Path("/mainCode/list")
    ResultDataVO getProdMainCodeDimVOList(IctProdMainCodeDimVO prodMainCodeDimVO) throws ApplicationException;

    /**
     * [批量新增/编辑主力产品主力编码记录]
     *
     * @return ResultDataVO
     * @throws ApplicationException
     */
    @POST
    @Path("/mainCode/update")
    ResultDataVO updateProdMainCodeDimVOList(DmFcstIctProdMainCodeDimVO ictProdMainCodeDimVO) throws ApplicationException;

    /**
     * [批量删除主力产品主力编码记录]
     *
     * @return ResultDataVO
     * @throws ApplicationException
     */
    @POST
    @Path("/mainCode/delete")
    ResultDataVO deleteProdMainCodeDimVOList(DmFcstIctProdMainCodeDimVO ictProdMainCodeDimVO) throws ApplicationException;

    /**
     * [导出主力产品主力编码记录]
     *
     * @return ResultDataVO
     * @throws ApplicationException
     */
    @POST
    @Path("/mainCode/export")
    ResultDataVO exportProdMainCodeDimVOList(IctProdMainCodeDimVO prodMainCodeDimVO, @Context HttpServletResponse response) throws ApplicationException;

    /**
     * [导入主力产品主力编码记录]
     *
     * @param versionId
     * @return ResultDataVO
     */
    @POST
    @Path("/mainCode/import")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    ResultDataVO importProdMainCodeDimVOList(@Multipart("files") Attachment attachment, @FormParam("versionId") Long versionId) throws Exception;

}