/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.combination;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

import java.util.Date;

/**
 * DmFocDataRefreshStatusTest Class
 *
 * <AUTHOR>
 * @since 2023/9/19
 */
public class DmFocDataRefreshStatusTest extends BaseVOCoverUtilsTest<DmFocDataRefreshStatus> {

    @Override
    protected Class<DmFocDataRefreshStatus> getTClass() {
        return DmFocDataRefreshStatus.class;
    }

    @Test
    public void testMethod() {
        DmFocDataRefreshStatus dmFocActualCostVO = new DmFocDataRefreshStatus();
        dmFocActualCostVO.setRoleId(10);
        dmFocActualCostVO.getRoleId();
        dmFocActualCostVO.getDelFlag();
        dmFocActualCostVO.getLastUpdateDate();
        dmFocActualCostVO.getLastUpdatedBy();
        dmFocActualCostVO.getTaskFlag();
        dmFocActualCostVO.getUserId();
        dmFocActualCostVO.setCreatedBy(11L);
        dmFocActualCostVO.getCreatedBy();
        dmFocActualCostVO.setCreationDate(new Date());
        dmFocActualCostVO.getCreationDate();
        Assert.assertNotNull(dmFocActualCostVO);
    }
}