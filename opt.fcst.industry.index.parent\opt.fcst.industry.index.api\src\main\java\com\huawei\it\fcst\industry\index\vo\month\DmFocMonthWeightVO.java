/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.month;

import com.fasterxml.jackson.annotation.JsonFormat;

import com.huawei.it.fcst.annotations.ExportAttribute;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * The Entity of DmFocMonthWeightT
 *
 * <AUTHOR>
 * @since 2023/03/10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DmFocMonthWeightVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("版本ID")
    private Long versionId;

    @ApiModelProperty("会计年(区间值, 例如:2022-2023, 2021，2022，2023)")
    private String periodYear;

    @ApiModelProperty("区间类型(S:综合多年, U:单年)")
    private String periodYearType;

    @ApiModelProperty("重量级团队CODE")
    private String prodRndTeamCode;

    @ApiModelProperty("重量级团队中文名称")
    private String prodRndTeamCnName;

    @ApiModelProperty("分层级CODE")
    @ExportAttribute(sort = 3)
    private String groupCode;

    @ApiModelProperty("分层级中文名称")
    @ExportAttribute(sort = 2)
    private String groupCnName;

    @ApiModelProperty("GROUP层级(ICT/LV1/LV2/CEG/CATEGORY/ITEM)")
    private String groupLevel;

    @ApiModelProperty("GROUP层级(ICT/LV1/LV2/CEG/CATEGORY/ITEM)")
    private String parentLevel;

    @ApiModelProperty("权重")
    private Double weightRate;
    private String weightRateStr;

    @ApiModelProperty("权重占比")
    @ExportAttribute(sort = 4)
    private String weightPercent;

    @ApiModelProperty("权重排序")
    private String weightOrder;

    @ApiModelProperty("父级CODE")
    private String parentCode;

    @ApiModelProperty("父级name")
    @ExportAttribute(sort = 1)
    private String parentCnName;

    @ApiModelProperty("创建人")
    private String createdBy;

    @ApiModelProperty("最后更新人")
    private String lastUpdatedBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    @ApiModelProperty("创建日期")
    private Timestamp creationDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    @ApiModelProperty("最后更新日期")
    private Timestamp lastUpdateDate;

    @ApiModelProperty("删除标识(未删除：N，已删除：Y)")
    private String delFlag;

    @ApiModelProperty("视角标识，用于区分不同视角下的品类数据(0: ICT层级, 1:ICT-LV1层级, 2:ICT-LV1-LV2层级)")
    private String viewFlag;

    @ApiModelProperty("l1")
    private String l1Name;

    @ApiModelProperty("l2")
    private String l2Name;

    @ApiModelProperty("组合id")
    private Long customId;

    private String customCnName;

    @ApiModelProperty("量纲颗粒度code")
    private String dimensionSubCategoryCode;

    @ApiModelProperty("量纲颗粒度名称")
    private String dimensionSubCategoryCnName;

    @ApiModelProperty("量纲颗粒度code")
    private String dmsCode;

    @ApiModelProperty("量纲颗粒度名称")
    private String dmsCnName;

    @ApiModelProperty("量纲颗粒度code")
    private String dimensionCode;

    @ApiModelProperty("量纲颗粒度名称")
    private String dimensionCnName;

    @ApiModelProperty("量纲颗粒度code")
    private String dimensionSubDetailCode;

    @ApiModelProperty("量纲颗粒度名称")
    private String dimensionSubDetailCnName;

    @ApiModelProperty("COA编码")
    private String coaCode;

    @ApiModelProperty("COA名称")
    private String coaCnName;

    @ApiModelProperty("spart code")
    private String spartCode;

    @ApiModelProperty("spart名称")
    private String spartCnName;

    @ApiModelProperty("顶部的成本类型下拉框: 制造成本：MANUFACTURE，采购成本：PURCHASE，总成本：TOTAL")
    @ExportAttribute(sort = 0)
    private String costType;
}
