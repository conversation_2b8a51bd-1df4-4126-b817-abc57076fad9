/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.service.annual;

import com.huawei.it.fcst.industry.pbi.vo.annual.AnnualAnalysisVO;
import com.huawei.it.fcst.industry.pbi.vo.version.DmFcstVersionInfoDTO;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.List;

/**
 * IAnnualAmpService Class
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
@Path("/annual")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface IAnnualAmpPbiService {
    /**
     * 查询当前层级涨跌幅
     *
     * @param annualAnalysisVO 参数
     * @return 结果
     */
    @Path("/allIndustryPbiCost")
    @POST
    ResultDataVO allIndustryPbiCost(AnnualAnalysisVO annualAnalysisVO) throws CommonApplicationException;

    /**
     * 查询当前层级的子项涨跌幅
     *
     * @param annualAnalysisVO 参数
     * @return 结果
     */
    @Path("/multiIndustryPbiCostChart")
    @POST
    ResultDataVO multiIndustryPbiCostChart(AnnualAnalysisVO annualAnalysisVO) throws CommonApplicationException;

    /**
     * 查询当前层级的子项涨跌幅和权重
     *
     * @param annualAnalysisVO 参数
     * @return 结果
     */
    @Path("/industryPbiCostList")
    @POST
    ResultDataVO industryCostList(AnnualAnalysisVO annualAnalysisVO) throws CommonApplicationException;


    /**
     * 查询当前层级的成本分布图
     *
     * @param annualAnalysisVO 参数
     * @return 结果
     */
    @Path("/distributePbiCostChart")
    @POST
    ResultDataVO distributePbiCostChart(AnnualAnalysisVO annualAnalysisVO) throws CommonApplicationException;

    /**
     * 查询对比分析-当前层级涨跌图
     *
     * @param annualAnalysisList 参数
     * @return 结果
     */
    @Path("/getComparePbiAmpCost")
    @POST
    ResultDataVO getComparePbiAmpCost(List<AnnualAnalysisVO> annualAnalysisList) throws InterruptedException;

    @POST
    @Path("/exportDetail")
    ResultDataVO exportAnnualDetail(@Context HttpServletResponse response, AnnualAnalysisVO annualAnalysisVO) throws ApplicationException;

    /**
     * 查询年度分析页面版本列表
     *
     * @param versionInfoDTO 参数
     * @return 结果
     */
    @POST
    @Path("/versionList")
    ResultDataVO getAnnualVersionList(DmFcstVersionInfoDTO versionInfoDTO) throws ApplicationException;

    /**
     * 查询年度实际数的开始和结束时间
     *
     */
    @POST
    @Path("/actual/periodId")
    ResultDataVO findActualPeriodId(DmFcstVersionInfoDTO versionInfoDTO) throws ApplicationException;

}
