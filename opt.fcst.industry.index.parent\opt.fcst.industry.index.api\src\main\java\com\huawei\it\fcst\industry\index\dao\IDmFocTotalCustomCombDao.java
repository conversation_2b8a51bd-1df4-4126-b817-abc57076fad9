/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.common.CommonViewVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;

import java.util.List;

/**
 * IDmFocCustomCombDao Class
 *
 * <AUTHOR>
 * @since 2023/8/1
 */
public interface IDmFocTotalCustomCombDao {

    List<DmFocViewInfoVO> prodTeamCodeForGeneral(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> prodTeamCodeForProfit(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> prodTeamCodeForDimension(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> prodEnergyTeamCodeForDimension(CommonViewVO commonViewVO);
}
