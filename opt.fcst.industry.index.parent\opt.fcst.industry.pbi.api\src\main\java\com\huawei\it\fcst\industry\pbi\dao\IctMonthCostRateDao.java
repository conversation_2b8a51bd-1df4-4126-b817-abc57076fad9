/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.dao;

import com.huawei.it.fcst.industry.pbi.vo.month.IctMonthAnalysisVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * IctMonthCostRateDao Interface
 *
 * <AUTHOR>
 * @since 2024-07-05
 */
public interface IctMonthCostRateDao {
    /**
     * 查询产业成本指数（ICT）同比/环比
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<IctMonthAnalysisVO> findCostRateVOList(@Param("monthAnalysisVO") IctMonthAnalysisVO monthAnalysisVO);

    /**
     * 查询产业成本指数（ICT）同比/环比汇总组合
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<IctMonthAnalysisVO> findDmFcstMonthCombYoyVOList(@Param("monthAnalysisVO") IctMonthAnalysisVO monthAnalysisVO);

    /**
     * 查询产业成本指数（ICT）虚化同比/环比
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<IctMonthAnalysisVO> findBlurCostRateVOList(@Param("monthAnalysisVO") IctMonthAnalysisVO monthAnalysisVO);

}