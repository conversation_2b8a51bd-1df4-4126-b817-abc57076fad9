/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.industry.index.impl.iauth;

import com.alibaba.fastjson.JSON;
import com.huawei.it.fcst.industry.index.constant.CommonConstant;
import com.huawei.it.fcst.industry.index.dao.IProdGroupsViewDao;
import com.huawei.it.fcst.industry.index.enums.IndustryIndexEnum;
import com.huawei.it.fcst.industry.index.service.iauth.IDataDimensionService;
import com.huawei.it.fcst.industry.index.vo.view.DimensionInfo;
import com.huawei.it.fcst.industry.index.vo.view.ProcessApprover;
import com.huawei.it.fcst.industry.index.vo.view.ViewInfoVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.util.CollectionUtil;
import com.huawei.it.jalor5.lookup.LookupItemVO;
import com.huawei.it.jalor5.lookup.service.ILookupItemQueryService;
import com.huawei.it.jalor5.security.RoleVO;
import com.huawei.it.jalor5.security.service.IRoleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Named;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023年03月10日
 */
@Slf4j
@Named("dataDimensionService")
@JalorResource(code = "DataDimensionService", desc = "iauth权限对接")
public class DataDimensionService implements IDataDimensionService {

    private static final Logger LOGGER = LoggerFactory.getLogger(DataDimensionService.class);
    @Autowired
    private IProdGroupsViewDao prodGroupsViewDao;

    @Autowired
    private IRoleService iRoleService;
    @Autowired
    private ILookupItemQueryService lookupItemQueryService;

    @Override
    @JalorOperation(code = "getDimensionWithTree", desc = "维度树型上报")
    public List<ViewInfoVO> getDimensionWithTree(List<String> parameter) throws CommonApplicationException {
        if (CollectionUtils.isEmpty(parameter)) {
            return Collections.EMPTY_LIST;
        }
        if (!CollectionUtil.isNullOrEmpty(parameter) && parameter.size() > com.huawei.it.fcst.constant.Constant.IntegerEnum.MAX_SIZE.getValue()) {
            throw new CommonApplicationException( "操作的数据过多！");
        }
        List<ViewInfoVO> dimensionWithTree = prodGroupsViewDao.getDimensionWithTree(parameter);
        dimensionWithTree.addAll(prodGroupsViewDao.getIctWithTree(parameter));
        return dimensionWithTree;
    }

    @Override
    @JalorOperation(code = "getProdDimensionWithTree", desc = "ICT经管维度树型上报")
    public List<ViewInfoVO> getProdDimensionWithTree(List<String> parameter) throws CommonApplicationException {
        if (CollectionUtils.isEmpty(parameter)) {
            return Collections.EMPTY_LIST;
        }
        if (!CollectionUtil.isNullOrEmpty(parameter) && parameter.size() > com.huawei.it.fcst.constant.Constant.IntegerEnum.MAX_SIZE.getValue()) {
            throw new CommonApplicationException( "操作的数据过多！");
        }
        return prodGroupsViewDao.getIctProdWithTree(parameter);
    }

    @Override
    @JalorOperation(code = "getPriceDimensionWithTree", desc = "定价维度树型上报")
    public List<ViewInfoVO> getPriceDimensionWithTree(List<String> parameter) throws CommonApplicationException {
        if (CollectionUtils.isEmpty(parameter)) {
            return Collections.EMPTY_LIST;
        }
        if (!CollectionUtil.isNullOrEmpty(parameter) && parameter.size() > com.huawei.it.fcst.constant.Constant.IntegerEnum.MAX_SIZE.getValue()) {
            throw new CommonApplicationException( "操作的数据过多！");
        }
        return prodGroupsViewDao.getPriceProdWithTree(parameter);
    }

    @Override
    @JalorOperation(code = "getCostType", desc = "成本类型上报")
    public List<ViewInfoVO> getCostType(List<String> parameter) throws CommonApplicationException {
        List<ViewInfoVO> costTypeDimensionList = new ArrayList<>();
        if (!CollectionUtil.isNullOrEmpty(parameter) && parameter.size() > com.huawei.it.fcst.constant.Constant.IntegerEnum.MAX_SIZE.getValue()) {
            throw new CommonApplicationException( "操作的数据过多！");
        }
        getCostTypeWithRole(costTypeDimensionList, "COST_TYPE_WITH_ROLE");
        return costTypeDimensionList;
    }

    private void getCostTypeWithRole(List<ViewInfoVO> costTypeDimensionList, String cost_type_with_role) {
        try {
            List<LookupItemVO> costTypeWithRole = lookupItemQueryService.findItemListByClassify(cost_type_with_role);
            for (LookupItemVO lookupItemVO : costTypeWithRole) {
                ViewInfoVO costTypeDimension = new ViewInfoVO();
                costTypeDimension.setDimensionValue(lookupItemVO.getItemCode());
                costTypeDimension.setDimensionDisplayValue(lookupItemVO.getItemName());
                costTypeDimensionList.add(costTypeDimension);
            }
        } catch (ApplicationException ex) {
            log.error("get lookupItemQueryService error: {}", ex.getMessage());
        }
    }

    @Override
    @JalorOperation(code = "getPspCostType", desc = "PSP成本类型上报")
    public List<ViewInfoVO> getPspCostType(List<String> parameter) throws CommonApplicationException {
        List<ViewInfoVO> costTypeDimensionList = new ArrayList<>();
        if (!CollectionUtil.isNullOrEmpty(parameter) && parameter.size() > com.huawei.it.fcst.constant.Constant.IntegerEnum.MAX_SIZE.getValue()) {
            throw new CommonApplicationException( "操作的数据过多！");
        }
        getCostTypeWithRole(costTypeDimensionList, "PSP_COST_TYPE");
        return costTypeDimensionList;
    }

    @Override
    @JalorOperation(code = "getDimensionWithTreePageList", desc = "维度树型上报分页查询")
    public PagedResult<ViewInfoVO> getDimensionWithTree(ViewInfoVO viewInfoVO, PageVO pageVO) throws CommonApplicationException {
        return prodGroupsViewDao.getDimensionWithTreePageList(viewInfoVO, pageVO);
    }

    @Override
    @JalorOperation(code = "getProdDimensionWithTreePageList", desc = "销售目录维度树型上报分页查询")
    public PagedResult<ViewInfoVO> getProdDimensionWithTree(ViewInfoVO viewInfoVO, PageVO pageVO) throws CommonApplicationException {
        return prodGroupsViewDao.getProdDimensionWithTreePageList(viewInfoVO, pageVO);
    }

    @Override
    @JalorOperation(code = "getPriceDimensionWithTreePageList", desc = "定价维度树型上报分页查询")
    public PagedResult<ViewInfoVO> getPriceDimensionWithTree(ViewInfoVO viewInfoVO, PageVO pageVO) throws CommonApplicationException {
        return prodGroupsViewDao.getPriceDimensionWithTreePageList(viewInfoVO, pageVO);
    }

    @Override
    @JalorOperation(code = "getCostTypePageList", desc = "成本类型上报分页查询")
    public PagedResult<ViewInfoVO> getCostType(ViewInfoVO viewInfoVO, PageVO pageVO) throws CommonApplicationException {
        PagedResult<ViewInfoVO> costTypeDimensionPageList = new PagedResult<>();
        List<ViewInfoVO> costTypeDimensionList = new ArrayList<>();
        getCostTypeWithRole(costTypeDimensionList, "COST_TYPE_WITH_ROLE");
        pageVO.setTotalRows(costTypeDimensionList.size());
        costTypeDimensionPageList.setPageVO(pageVO);
        costTypeDimensionPageList.setResult(costTypeDimensionList);
        return costTypeDimensionPageList;
    }

    @Override
    @JalorOperation(code = "getPspCostTypePageList", desc = "PSP成本类型上报分页查询")
    public PagedResult<ViewInfoVO> getPspCostType(ViewInfoVO viewInfoVO, PageVO pageVO) throws CommonApplicationException {
        PagedResult<ViewInfoVO> costTypeDimensionPageList = new PagedResult<>();
        List<ViewInfoVO> costTypeDimensionList = new ArrayList<>();
        getCostTypeWithRole(costTypeDimensionList, "PSP_COST_TYPE");
        pageVO.setTotalRows(costTypeDimensionList.size());
        costTypeDimensionPageList.setPageVO(pageVO);
        costTypeDimensionPageList.setResult(costTypeDimensionList);
        return costTypeDimensionPageList;
    }

    @Override
    @JalorOperation(code = "getLocationWithTree", desc = "国内海外-地区部-代表处上报")
    public List<ViewInfoVO> getLocationWithTree(List<String> parameter) throws CommonApplicationException {
        if (CollectionUtils.isEmpty(parameter)) {
            return Collections.EMPTY_LIST;
        }
        if (!CollectionUtil.isNullOrEmpty(parameter) && parameter.size() > com.huawei.it.fcst.constant.Constant.IntegerEnum.MAX_SIZE.getValue()) {
            throw new CommonApplicationException( "操作的数据过多！");
        }
        return prodGroupsViewDao.getLocationWithTree(parameter);
    }

    @Override
    @JalorOperation(code = "getLocationWithTreePageList", desc = "国内海外-地区部-代表处上报分页查询")
    public PagedResult<ViewInfoVO> getLocationWithTree(ViewInfoVO viewInfoVO, PageVO pageVO) throws CommonApplicationException {
        return prodGroupsViewDao.getLocationWithTreePageList(viewInfoVO, pageVO);
    }

    @Override
    @JalorOperation(code = "getPriceLocationWithTree", desc = "定价国内海外-地区部-代表处上报")
    public List<ViewInfoVO> getPriceLocationWithTree(List<String> parameter) throws CommonApplicationException {
        if(CollectionUtils.isEmpty(parameter)){
            return Collections.EMPTY_LIST;
        }
        if (!CollectionUtil.isNullOrEmpty(parameter) && parameter.size() > com.huawei.it.fcst.constant.Constant.IntegerEnum.MAX_SIZE.getValue()) {
            throw new CommonApplicationException( "操作的数据过多！");
        }
        return prodGroupsViewDao.getPriceLocationWithTree(parameter);
    }

    @Override
    @JalorOperation(code = "getPriceLocationWithTreePageList", desc = "定价国内海外-地区部-代表处上报分页查询")
    public PagedResult<ViewInfoVO> getPriceLocationWithTree(ViewInfoVO viewInfoVO, PageVO pageVO) throws CommonApplicationException {
        return prodGroupsViewDao.getPriceLocationWithTreePageList(viewInfoVO, pageVO);
    }

    @Override
    @JalorOperation(code = "getKeyAndSubAccountWithTree", desc = "定价大T-子网系统-上报")
    public List<ViewInfoVO> getKeyAndSubAccountWithTree(List<String> parameter) throws CommonApplicationException {
        if(CollectionUtils.isEmpty(parameter)){
            return Collections.EMPTY_LIST;
        }
        if (!CollectionUtil.isNullOrEmpty(parameter) && parameter.size() > com.huawei.it.fcst.constant.Constant.IntegerEnum.MAX_SIZE.getValue()) {
            throw new CommonApplicationException( "操作的数据过多！");
        }
        return prodGroupsViewDao.getKeyAndSubAccountWithTree(parameter);
    }

    @Override
    @JalorOperation(code = "getKeyAndSubAccountWithTreePageList", desc = "定价大T-子网系统-上报分页查询")
    public PagedResult<ViewInfoVO> getKeyAndSubAccountWithTree(ViewInfoVO viewInfoVO, PageVO pageVO) throws CommonApplicationException {
        return prodGroupsViewDao.getKeyAndSubAccountWithTreePageList(viewInfoVO, pageVO);
    }

    @Override
    public List<ViewInfoVO> getCurrentLv2ProdRndTeamList(String costType, String tablePreFix) {
        List<ViewInfoVO> currentLv2ProdRndTeamList = new ArrayList<>();
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(costType)) {
            currentLv2ProdRndTeamList = prodGroupsViewDao.getTotalLv2ProdRndTeamList(tablePreFix);
        }
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(costType)) {
            currentLv2ProdRndTeamList = prodGroupsViewDao.getPurchaseLv2ProdRndTeamList(tablePreFix);
        }
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(costType)) {
            currentLv2ProdRndTeamList = prodGroupsViewDao.getManuFactureLv2ProdRndTeamList(tablePreFix);
        }
        return currentLv2ProdRndTeamList;
    }

    @Override
    @JalorOperation(code = "approverBasedOnDatascope", desc = "依据数据范围设置流程审批人")
    public Map approverBasedOnDatascope(ProcessApprover processApprover) {
        LOGGER.info("Begin approverBasedOnDatascope {}", JSON.toJSONString(processApprover));
        // 返回对象设置
        Map<String, Object> returnMap = new HashMap<>();
        String roleApprover = "ROLE_LEVEL2_APPROVER";
        List<LookupItemVO> roleLevel2Approver = getRoleWithApprover(roleApprover);
        // 审批人集合
        List<String> userList = new ArrayList<>();
        for (LookupItemVO lookupItemVO : roleLevel2Approver) {
            if (!lookupItemVO.getScope().equals(processApprover.getRoleId())) {
                continue;
            }
            List<DimensionInfo> dimensionInfo = processApprover.getDimensionInfo();
            if (CollectionUtils.isNotEmpty(dimensionInfo)) {
                for (DimensionInfo info : dimensionInfo) {
                    if (!CommonConstant.DIMENSION_CODE.contains(info.getItemTypeCode())) {
                        continue;
                    }
                    String itemValue = info.getItemValue();
                    if (itemValue.contains("ALL") || itemValue.contains("@ALLCONDITION@")) {
                        allDataRange(userList, lookupItemVO);
                    } else {
                        partialDataRange(userList, lookupItemVO, itemValue);
                    }
                }
            }
        }
        // 审批人集合
        String[] owners = userList.toArray(new String[userList.size()]);
        returnMap.put("msg", "success");
        returnMap.put("data", owners);
        returnMap.put("result", 0);
        LOGGER.info("end approverBasedOnDatascope {}", JSON.toJSONString(returnMap));
        return returnMap;
    }

    @Nullable
    private List<LookupItemVO> getRoleWithApprover(String roleApprover) {
        RoleVO roleVO = new RoleVO();
        roleVO.setScope("fcst.headquarters");
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(100);
        pageVO.setCurPage(1);
        // 获取lookup配置的角色对应二级审批人
        List<LookupItemVO> roleLevel2Approver = null;
        try {
            PagedResult<RoleVO> rolePageResultList = iRoleService.findAllRole(roleVO, pageVO);
            List<RoleVO> roleList = rolePageResultList.getResult();
            roleLevel2Approver = lookupItemQueryService.findItemListByClassify(roleApprover);
            for (LookupItemVO lookupItemVO : roleLevel2Approver) {
                for (RoleVO vo : roleList) {
                    if (lookupItemVO.getItemCode().equals(vo.getRoleNameEn())) {
                        lookupItemVO.setScope(String.valueOf(vo.getRoleId()));
                    }
                }
            }
        } catch (ApplicationException ex) {
            log.error("get lookupItemQueryService error: {}", ex.getMessage());
        }
        return roleLevel2Approver;
    }

    private void allDataRange(List<String> userList, LookupItemVO lookupItemVO) {
        // ict组织的审批人需要特殊处理
        if (StringUtils.isNotBlank(lookupItemVO.getItemDesc())) {
            setApprover(userList, lookupItemVO.getItemDesc());
        } else {
            setApprover(userList, lookupItemVO.getItemAttr1());
        }
        // 数字能源组织审批人需要特殊处理
        if (StringUtils.isNotBlank(lookupItemVO.getItemAttr4())) {
            setApprover(userList, lookupItemVO.getItemAttr4());
        } else {
            setApprover(userList, lookupItemVO.getItemAttr2());
        }
        // ias组织的审批人设置
        setApprover(userList, lookupItemVO.getItemAttr3());
        // ict-new组织的审批人设置
        setApprover(userList, lookupItemVO.getItemAttr5());
        // 定价组织的审批人设置
        setApprover(userList, lookupItemVO.getItemAttr6());
    }

    private void allDataRangeThree(List<String> userList, LookupItemVO lookupItemVO) {
        // ict组织的审批人需要特殊处理
        if (StringUtils.isNotBlank(lookupItemVO.getItemDesc())) {
            setApprover(userList, lookupItemVO.getItemDesc());
        } else {
            setApprover(userList, lookupItemVO.getItemAttr1());
        }
        // ias组织的审批人设置
        setApprover(userList, lookupItemVO.getItemAttr2());
    }

    private void partialDataRange(List<String> userList, LookupItemVO lookupItemVO, String itemValue) {
        List<String> itemValueList = Arrays.asList(itemValue.split("\\|"));
        // 申请了ICT权限，获取ICT当前角色对应的审批人
        if (StringUtils.isNotBlank(lookupItemVO.getItemDesc())) {
            setApproverWithOrg(userList, prodGroupsViewDao.getIctDimensionValue(itemValueList), prodGroupsViewDao.getIctDimensionValue(null), lookupItemVO.getItemDesc(), lookupItemVO.getItemAttr1(), lookupItemVO.getItemCode());
        }
        // 申请了数字能源权限，获取数字能源当前角色对应的审批人
        if (StringUtils.isNotBlank(lookupItemVO.getItemAttr4())) {
            setApproverWithOrg(userList, prodGroupsViewDao.getEnergyDimensionValue(itemValueList), prodGroupsViewDao.getEnergyDimensionValue(null), lookupItemVO.getItemAttr4(), lookupItemVO.getItemAttr2(), lookupItemVO.getItemCode());
        }
        if (StringUtils.isNotBlank(lookupItemVO.getItemAttr3())) {
            List<String> iasDimensionWithTree = prodGroupsViewDao.getIasDimensionValue(itemValueList);
            // 申请了ias权限，获取IAS当前角色对应的审批人
            if (CollectionUtils.isNotEmpty(iasDimensionWithTree)) {
                setApprover(userList, lookupItemVO.getItemAttr3());
            }
        }
        if (StringUtils.isNotBlank(lookupItemVO.getItemAttr5())) {
            List<String> ictNewDimensionWithTree = getIctNewDimensionValue(itemValueList);
            // 申请了ict-new权限，获取ict-new 当前角色对应的审批人
            if (CollectionUtils.isNotEmpty(ictNewDimensionWithTree)) {
                setApprover(userList, lookupItemVO.getItemAttr5());
            }
        }
        if (StringUtils.isNotBlank(lookupItemVO.getItemAttr6())) {
            List<String> priceDimensionValue = prodGroupsViewDao.getPriceDimensionValue(itemValueList);
            // 申请了定价权限，获取定价 当前角色对应的审批人
            if (CollectionUtils.isNotEmpty(priceDimensionValue)) {
                setApprover(userList, lookupItemVO.getItemAttr6());
            }
        }
    }

    private void partialDataRangeThree(List<String> userList, LookupItemVO lookupItemVO, String itemValue) {
        List<String> itemValueList = Arrays.asList(itemValue.split("\\|"));
        // 申请了ICT权限，获取ICT当前角色对应的审批人
        setApproverWithOrgThree(userList, prodGroupsViewDao.getIctDimensionValue(itemValueList), prodGroupsViewDao.getIctDimensionValue(null), lookupItemVO.getItemDesc(), lookupItemVO.getItemAttr1(), lookupItemVO.getItemCode());
        // 申请了数字能源权限，获取数字能源当前角色对应的审批人
        if ("Manufacture analyst_Ind".equals(lookupItemVO.getItemCode())) {
            setApproverWithOrgThree(userList, prodGroupsViewDao.getEnergyDimensionValue(itemValueList), prodGroupsViewDao.getEnergyDimensionValue(null), lookupItemVO.getItemAttr4(), lookupItemVO.getItemAttr2(), lookupItemVO.getItemCode());
        }
        List<String> iasDimensionWithTree = prodGroupsViewDao.getIasDimensionValue(itemValueList);
        // 申请了ias权限，获取IAS当前角色对应的审批人
        if (CollectionUtils.isNotEmpty(iasDimensionWithTree)) {
            setApprover(userList, lookupItemVO.getItemAttr3());
        }
    }

    private void setApproverWithOrgThree(List<String> userList, List<String> searchDimensionValue, List<String> allDimensionValue, String itemDesc, String itemAttr, String roleName) {
        List<String> dimensionWithItem = searchDimensionValue;
        List<String> dimensionWithTree = allDimensionValue;
        if (dimensionWithItem.containsAll(dimensionWithTree)) {
            // 代表勾选了的所有数据范围
            if (StringUtils.isNotBlank(itemDesc)) {
                setApprover(userList, itemDesc);
            } else {
                setApprover(userList, itemAttr);
            }
        } else {
            setPartScopeUserThree(userList, itemAttr, roleName, dimensionWithItem);
        }
    }

    private void setApproverWithOrg(List<String> userList, List<String> searchDimensionValue, List<String> allDimensionValue, String itemDesc, String itemAttr, String roleName) {
        List<String> dimensionWithItem = searchDimensionValue;
        List<String> dimensionWithTree = allDimensionValue;
        if (dimensionWithItem.containsAll(dimensionWithTree)) {
            // 代表勾选了的所有数据范围
            if (StringUtils.isNotBlank(itemDesc)) {
                setApprover(userList, itemDesc);
            } else {
                setApprover(userList, itemAttr);
            }
        } else {
            setPartScopeUser(userList, itemAttr, roleName, dimensionWithItem);
        }
    }

    private void setPartScopeUserThree(List<String> userList, String itemAttr, String roleName, List<String> dimensionWithItem) {
        // 申请了部分数据范围权限，获取当前角色对应的审批人
        if (CollectionUtils.isNotEmpty(dimensionWithItem)) {
            // 数据范围如果全选的话，审批人需要特殊处理
            String[] userIndustry = itemAttr.split(",");
            if ("Manufacture analyst_Ind".equals(roleName)) {
                ictPsUserList(userList, dimensionWithItem, userIndustry);
                energyUserList(userList, dimensionWithItem, userIndustry);
            } else {
                setApprover(userList, itemAttr);
            }
        }
    }

    private void setPartScopeUser(List<String> userList, String itemAttr, String roleName, List<String> dimensionWithItem) {
        // 申请了部分数据范围权限，获取当前角色对应的审批人
        if (CollectionUtils.isNotEmpty(dimensionWithItem)) {
            // 数据范围如果全选的话，审批人需要特殊处理
            String[] userIndustry = itemAttr.split(",");
            if ("Industry_Analyst_Ind".equals(roleName)) {
                ictPsUserList(userList, dimensionWithItem, userIndustry);
                energyUserList(userList, dimensionWithItem, userIndustry);
            } else if ("Procurement_Analyst_Ind".equals(roleName)) {
                energyUserList(userList, dimensionWithItem, userIndustry);
            } else {
                setApprover(userList, itemAttr);
            }
        }
    }

    private void ictPsUserList(List<String> userList, List<String> dimensionWithItem, String[] userIndustry) {
        // 权限值申请了LV2,需要找对应的LV1
        List<String> ictLv1DimensionValue = prodGroupsViewDao.getIctLv1DimensionValue(dimensionWithItem);
        List<String> ictLv1DimensionDistinct = ictLv1DimensionValue.stream().distinct().collect(Collectors.toList());
        dimensionWithItem.addAll(ictLv1DimensionDistinct);
        // ict的数组长度是6个
        if (userIndustry.length == 6) {
            // 光
            if (dimensionWithItem.contains("134557_LV1")) {
                userList.add(userIndustry[0]);
            }
            // 计算
            if (dimensionWithItem.contains("133277_LV1")) {
                userList.add(userIndustry[1]);
            }
            // 数据存储
            if (dimensionWithItem.contains("101775_LV1")) {
                userList.add(userIndustry[2]);
            }
            // 数据通信
            if (dimensionWithItem.contains("137565_LV1")) {
                userList.add(userIndustry[3]);
            }
            // 无线
            if (dimensionWithItem.contains("100001_LV1")) {
                userList.add(userIndustry[4]);
            }
            // 云核
            if (dimensionWithItem.contains("100011_LV1")) {
                userList.add(userIndustry[5]);
            }
        }
    }

    private void energyUserList(List<String> userList, List<String> dimensionWithItem, String[] userIndustry) {
        // 权限值申请了LV2,需要找对应的LV1
        List<String> energyLv1DimensionValue = prodGroupsViewDao.getEnergyLv1DimensionValue(dimensionWithItem);
        List<String> energyLv1DimensionDistinct = energyLv1DimensionValue.stream().distinct().collect(Collectors.toList());
        dimensionWithItem.addAll(energyLv1DimensionDistinct);
        // 数字能源的数组长度是6个
        if (userIndustry.length == 3) {
            // 数据中心能源及关键供电产品线
            if (dimensionWithItem.contains("164174_L1_E")) {
                userList.add(userIndustry[0]);
            }
            // 智能电动产品线
            if (dimensionWithItem.contains("164176_L1_E")) {
                userList.add(userIndustry[1]);
            }
            // 智能光伏产品线
            if (dimensionWithItem.contains("164178_L1_E")) {
                userList.add(userIndustry[2]);
            }
        }
    }

    private void setApprover(List<String> userList, String itemDesc) {
        String user = itemDesc;
        String[] userArray = user.split(",");
        userList.addAll(Arrays.asList(userArray));
    }

    @NotNull
    public List<String> getIctNewDimensionValue(List<String> dimensionList) {
        List<String> ictNewDimensionValue = new ArrayList<>();
        List<String> ictIrbDimensionValue = prodGroupsViewDao.getIctIrbDimensionValue(dimensionList);
        ictNewDimensionValue.addAll(ictIrbDimensionValue);
        List<String> ictProdDimensionValue = prodGroupsViewDao.getIctProdDimensionValue(dimensionList);
        ictNewDimensionValue.addAll(ictProdDimensionValue);
        List<String> ictIndusDimensionValue = prodGroupsViewDao.getIctIndusDimensionValue(dimensionList);
        ictNewDimensionValue.addAll(ictIndusDimensionValue);
        return ictNewDimensionValue;
    }

    @Override
    @JalorOperation(code = "approverThirdBasedOnDatascope", desc = "依据数据范围设置三级流程审批人")
    public Map approverThirdBasedOnDatascope(ProcessApprover processApprover) {
        LOGGER.info("Begin approverThirdBasedOnDatascope {}", JSON.toJSONString(processApprover));
        // 返回对象设置
        Map<String, Object> returnMap = new HashMap<>();
        String roleApprover = "ROLE_LEVEL3_APPROVER";
        List<LookupItemVO> roleLevel3Approver = getRoleWithApprover(roleApprover);
        // 审批人集合
        List<String> userList = new ArrayList<>();
        for (LookupItemVO lookupItemVO : roleLevel3Approver) {
            if (!lookupItemVO.getScope().equals(processApprover.getRoleId())) {
                continue;
            }
            List<DimensionInfo> dimensionInfo = processApprover.getDimensionInfo();
            if (CollectionUtils.isNotEmpty(dimensionInfo)) {
                for (DimensionInfo info : dimensionInfo) {
                    if (!CommonConstant.DIMENSION_CODE.contains(info.getItemTypeCode())) {
                        continue;
                    }
                    String itemValue = info.getItemValue();
                    if (itemValue.contains("ALL") || itemValue.contains("@ALLCONDITION@")) {
                        allDataRangeThree(userList, lookupItemVO);
                    } else {
                        partialDataRangeThree(userList, lookupItemVO, itemValue);
                    }
                }
            }
        }
        // 审批人集合
        String[] owners = userList.toArray(new String[userList.size()]);
        returnMap.put("msg", "success");
        returnMap.put("data", owners);
        returnMap.put("result", 0);
        LOGGER.info("end approverThirdBasedOnDatascope {}", JSON.toJSONString(returnMap));
        return returnMap;
    }
}
