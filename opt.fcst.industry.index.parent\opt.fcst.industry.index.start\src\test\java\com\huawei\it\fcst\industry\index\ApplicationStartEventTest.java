package com.huawei.it.fcst.industry.index;

import static org.assertj.core.api.Assertions.assertThatNoException;

import com.huawei.it.fcst.industry.index.impl.mqs.MessageConsumer;
import com.huawei.it.fcst.industry.index.impl.mqs.MessageProducer;
import com.huawei.it.jalor5.core.ioc.Jalor;
import com.huawei.it.jalor5.core.ioc.delegate.JalorApplicationContext;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.security.UserVO;

import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.slf4j.Logger;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({Jalor.class})
public class ApplicationStartEventTest  {
	@InjectMocks
	private ApplicationStartEvent applicationStartEvent=new ApplicationStartEvent(); 
	@Mock
	private MessageConsumer consumer; 

	@Mock
	private MessageProducer producer; 

	@Mock
	private Logger log; 

    @Before
    public void before() throws Exception {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);
    }

	@Test
 	public void getOrderTest() {
		int result = applicationStartEvent.getOrder();
        Assertions.assertNotNull(result);
	}

	@Test
 	public void executeTest() throws Exception {
        PowerMockito.mockStatic(Jalor.class);
        JalorApplicationContext context = PowerMockito.mock(JalorApplicationContext.class);
        PowerMockito.when(Jalor.getContext()).thenReturn(context);
        MessageProducer message = PowerMockito.mock(MessageProducer.class);
        MessageConsumer messageConsumer = PowerMockito.mock(MessageConsumer.class);
        PowerMockito.when(context.getBean("messageProducer", MessageProducer.class)).thenReturn(message);
        PowerMockito.when(context.getBean("messageConsumer", MessageConsumer.class)).thenReturn(messageConsumer);
        applicationStartEvent.execute(null, null);
        assertThatNoException();
	}

}