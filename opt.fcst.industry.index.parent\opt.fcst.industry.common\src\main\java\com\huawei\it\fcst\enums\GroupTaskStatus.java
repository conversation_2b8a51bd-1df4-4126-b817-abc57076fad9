/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.enums;

import lombok.Getter;

/**
 * Group层级枚举类
 *
 * <AUTHOR>
 * @since 202407
 */
public enum GroupTaskStatus {
    /**
     * 草稿
     */
    DRAFT("D", "草稿"),
    /**
     * 执行中
     */
    ING("ING", "执行中"),
    /**
     * 成功
     */
    SUCCESS("Y", "成功"),
    /**
     * 失败
     */
    FAIL("N", "失败");

    @Getter
    private String code;

    @Getter
    private String desc;

    GroupTaskStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
