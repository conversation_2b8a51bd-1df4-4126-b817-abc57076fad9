/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.config;

import com.huawei.it.fcst.vo.BaseVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * DmFocVersionInfoVO Class
 *
 * <AUTHOR>
 * @since 2023/3/13
 */
@NoArgsConstructor
@Getter
@Setter
@Builder
@AllArgsConstructor
public class DmFocVersionInfoVO extends BaseVO {
    /**
     * 数据类型（category：TOP品类、item：规格品）
     **/
    private String dataType;

    /**
     * 状态（1：已刷新、0：未刷新）
     **/
    private Long status;

    private Long step;

    private String lastUpdateStr;

    private String industryOrg;

    private String tablePreFix;

}
