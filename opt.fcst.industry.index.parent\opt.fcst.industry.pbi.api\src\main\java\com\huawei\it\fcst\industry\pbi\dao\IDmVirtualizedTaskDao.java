/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.dao;

import com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstBaseCusDimVO;
import org.apache.ibatis.annotations.Param;

/**
 * IDmFocViewInfoDao
 *
 * <AUTHOR>
 * @since 2024/7/23
 */
public interface IDmVirtualizedTaskDao {

    /**
     * 执行年度虚化任务
     *
     * @param dmFcstBaseCusDimVO 执行参数
     * @param key 加解密key
     * @return
     */
    String callAnnualFuncTask(@Param("query")DmFcstBaseCusDimVO dmFcstBaseCusDimVO, @Param("keyStr") String key);

    /**
     * 执行月度虚化任务
     *
     * @param dmFcstBaseCusDimVO 执行参数
     * @param key 加解密key
     * @return
     */
    String callMonthFuncTask(@Param("query")DmFcstBaseCusDimVO dmFcstBaseCusDimVO, @Param("keyStr") String key);

    /**
     * 月度成本分布图函数
     * F_DM_FCST_MON_COST_AMT
     * f_cost_type 成本类型
     * f_granularity_type  PBI维
     * f_view_flag  路径
     * f_keystr   密钥
     * f_customization_id 组合id
     *
     * @param dmFcstBaseCusDimVO
     * @param key
     * @return
     */
    String callMontCostAmtFunc(@Param("query")DmFcstBaseCusDimVO dmFcstBaseCusDimVO, @Param("keyStr") String key);


    /**
     * 月度成本分布图累积函数
     * F_DM_FCST_MON_COST_AMT
     * f_cost_type 成本类型
     * f_granularity_type  PBI维
     * f_view_flag  路径
     * f_keystr   密钥
     * f_customization_id 组合id
     *
     * @param dmFcstBaseCusDimVO
     * @param key
     * @return
     */
    String callMontCostYtdAmtFunc(@Param("query")DmFcstBaseCusDimVO dmFcstBaseCusDimVO, @Param("keyStr") String key);

    /**
     * 编码替代的实时月度虚化函数：f_dm_fcst_ict_real_time_base_cus_mon_repl_cost_info_t(f_cost_type character varying ,  f_granularity_type character varying , f_keystr text DEFAULT NULL::character varying,f_custom_id integer, f_version_id integer DEFAULT NULL::integer, OUT x_result_status text)
     *
     * @param dmFcstBaseCusDimVO 执行参数
     * @param key 加解密key
     * @return
     */
    String callReplaceCodeMonthFunc(@Param("query")DmFcstBaseCusDimVO dmFcstBaseCusDimVO, @Param("keyStr") String key);

    /**
     * 编码替代的实时月累计虚化函数：f_dm_fcst_ict_real_time_base_cus_ytd_repl_cost_info_t(f_cost_type character varying ,  f_granularity_type character varying , f_keystr text DEFAULT NULL::character varying, f_custom_id integer,f_version_id integer DEFAULT NULL::integer, OUT x_result_status text)
     *
     * @param dmFcstBaseCusDimVO
     * @param key
     * @return
     */
    String callReplaceCodeMonthYtdFuncTask(@Param("query")DmFcstBaseCusDimVO dmFcstBaseCusDimVO, @Param("keyStr") String key);

    /**
     * Top-Spart的发货量、成本金额的实时月度虚化函数：f_dm_fcst_ict_top_base_cus_mon_repl_cost_info_t(f_cost_type character varying ,  f_granularity_type character varying , f_keystr text DEFAULT NULL::character varying, f_custom_id integer,f_version_id integer DEFAULT NULL::integer, OUT x_result_status text)
     *
     * @param dmFcstBaseCusDimVO
     * @param key
     * @return
     */
    String callMonthTopSpartCostAmtFunc(@Param("query")DmFcstBaseCusDimVO dmFcstBaseCusDimVO, @Param("keyStr") String key);

    /**
     * Top-Spart的发货量、成本金额的实时月累计虚化函数：f_dm_fcst_ict_top_base_cus_ytd_repl_cost_info_t(f_cost_type character varying ,  f_granularity_type character varying , f_keystr text DEFAULT NULL::character varying, f_custom_id integer,f_version_id integer DEFAULT NULL::integer, OUT x_result_status text)
     *
     * @param dmFcstBaseCusDimVO
     * @param key
     * @return
     */
    String callMonthTopSpartYtdCostAmtFunc(@Param("query")DmFcstBaseCusDimVO dmFcstBaseCusDimVO, @Param("keyStr") String key);
}
