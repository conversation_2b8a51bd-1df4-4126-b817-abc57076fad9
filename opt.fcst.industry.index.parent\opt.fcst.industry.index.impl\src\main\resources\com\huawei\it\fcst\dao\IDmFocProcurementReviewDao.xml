<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IDmFocProcurementReviewDao">
    <resultMap type="com.huawei.it.fcst.industry.index.vo.config.ProcurementBottomVO" id="resultMap">
        <result property="versionId" column="version_id"/>
        <result property="l3CegCode" column="l3_ceg_code"/>
        <result property="l3CegShortCnName" column="l3_ceg_short_cn_name"/>
        <result property="l4CegCode" column="l4_ceg_code"/>
        <result property="l4CegShortCnName" column="l4_ceg_short_cn_name"/>
        <result property="categoryCode" column="category_code"/>
        <result property="categoryCnName" column="category_cn_name"/>
        <result property="itemCode" column="item_code"/>
        <result property="itemCnName" column="item_cn_name"/>
        <result property="periodId" column="period_id"/>
        <result property="startPeriod" column="start_period"/>
        <result property="endPeriod" column="end_period"/>
        <result property="modifyReason" column="modify_reason"/>
        <result property="modifyType" column="modify_type"/>
        <result property="impactQty" column="impact_qty"/>
        <result property="crmAvgCnt" column="rmb_avg_cnt"/>
        <result property="caliberFlag" column="caliber_flag"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="id" column="id"/>
    </resultMap>

    <sql id="allFields">
        version_id,
        l3_ceg_code,
        l3_ceg_short_cn_name,
        l4_ceg_code,
        l4_ceg_short_cn_name,
        category_code,
        category_cn_name,
        item_code,
        item_cn_name,
        start_period,
        end_period,
        modify_reason,
        modify_type,
        impact_qty,
        caliber_flag,
        created_by,
        creation_date,
        last_updated_by,
        last_update_date,
        del_flag,
        page_flag
    </sql>

    <sql id="allFieldsByPage">
        t1.version_id,
        t1.l3_ceg_code,
        t1.l3_ceg_short_cn_name,
        t1.l4_ceg_code,
        t1.l4_ceg_short_cn_name,
        t1.category_code,
        t1.category_cn_name,
        t1.item_code,
        t1.item_cn_name,
        t1.start_period,
        t1.end_period,
        t1.modify_reason,
        t1.modify_type,
        t1.impact_qty,
        t1.caliber_flag,
        t1.created_by,
        t1.creation_date,
        t1.last_updated_by,
        t1.last_update_date,
        t1.del_flag
    </sql>

    <sql id="searchFields">
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='procurementBottomVO.versionId != null'>
                AND t1.version_id=#{procurementBottomVO.versionId,jdbcType=BIGINT}
            </if>
            <if test='procurementBottomVO.pageFlag != null and procurementBottomVO.pageFlag!= ""'>
                AND (t1.page_flag = #{procurementBottomVO.pageFlag,jdbcType=VARCHAR} or t1.page_flag is null)
            </if>
            <if test='procurementBottomVO.l3CegCode != null and procurementBottomVO.l3CegCode!= ""'>
                AND t1.l3_ceg_code=#{procurementBottomVO.l3CegCode,jdbcType=VARCHAR}
            </if>
            <if test='procurementBottomVO.l3CegShortCnName != null and procurementBottomVO.l3CegShortCnName != ""'>
                AND t1.l3_ceg_short_cn_name=#{procurementBottomVO.l3CegShortCnName,jdbcType=VARCHAR}
            </if>
            <if test='procurementBottomVO.l4CegCode != null and procurementBottomVO.l4CegCode != ""'>
                AND t1.l4_ceg_code=#{procurementBottomVO.l4CegCode,jdbcType=VARCHAR}
            </if>
            <if test='procurementBottomVO.l4CegShortCnName != null and procurementBottomVO.l4CegShortCnName != ""'>
                AND t1.l4_ceg_short_cn_name=#{procurementBottomVO.l4CegShortCnName,jdbcType=VARCHAR}
            </if>
            <if test='procurementBottomVO.categoryCode != null and procurementBottomVO.categoryCode != ""'>
                AND t1.category_code=#{procurementBottomVO.categoryCode,jdbcType=VARCHAR}
            </if>
            <if test='procurementBottomVO.categoryCnName != null and procurementBottomVO.categoryCnName != ""'>
                AND t1.category_cn_name=#{procurementBottomVO.categoryCnName,jdbcType=VARCHAR}
            </if>
            <if test='procurementBottomVO.itemCode != null and procurementBottomVO.itemCode != ""'>
                AND t1.item_code=#{procurementBottomVO.itemCode,jdbcType=VARCHAR}
            </if>
            <if test='procurementBottomVO.itemCnName != null and procurementBottomVO.itemCnName != ""'>
                AND t1.item_cn_name=#{procurementBottomVO.itemCnName,jdbcType=VARCHAR}
            </if>
            <if test='procurementBottomVO.startPeriod != null'>
                AND t1.start_period=#{procurementBottomVO.startPeriod,jdbcType=BIGINT}
            </if>
            <if test='procurementBottomVO.endPeriod != null'>
                AND t1.end_period=#{procurementBottomVO.endPeriod,jdbcType=BIGINT}
            </if>
            <if test='procurementBottomVO.modifyReason != null and procurementBottomVO.modifyReason != ""'>
                AND t1.modify_reason=#{procurementBottomVO.modifyReason,jdbcType=VARCHAR}
            </if>
            <if test='procurementBottomVO.modifyType != null and procurementBottomVO.modifyType != ""'>
                AND t1.modify_type=#{procurementBottomVO.modifyType,jdbcType=VARCHAR}
            </if>
            <if test='procurementBottomVO.impactQty != null'>
                AND t1.impact_qty=#{procurementBottomVO.impactQty,jdbcType=BIGINT}
            </if>
            <if test='procurementBottomVO.caliberFlag != null and procurementBottomVO.caliberFlag != ""'>
                AND t1.caliber_flag=#{procurementBottomVO.caliberFlag,jdbcType=VARCHAR}
            </if>
            <if test='procurementBottomVO.delFlag != null and procurementBottomVO.delFlag != ""'>
                AND t1.del_flag=#{procurementBottomVO.delFlag,jdbcType=VARCHAR}
            </if>
        </trim>

    </sql>

    <select id="findByPage" resultMap="resultMap">
        SELECT n1.version,<include refid="allFieldsByPage"/>
        FROM fin_dm_opt_foi.${procurementBottomVO.tablePreFix}_base_data_review_info_t t1
        left join
        fin_dm_opt_foi.${procurementBottomVO.tablePreFix}_version_info_t n1
        on t1.version_id = n1.version_id
        <include refid="searchFields"/>
        <if test='procurementBottomVO.pageFlag!= null and procurementBottomVO.pageFlag == "abnormal"'>
            AND t1.modify_type !='REVOKE'
        </if>
        <if test='procurementBottomVO.dataType != null and procurementBottomVO.dataType != ""'>
            AND n1.data_type=#{procurementBottomVO.dataType,jdbcType=VARCHAR}
        </if>
        and n1.del_flag ='N'
        LIMIT #{pageVO.pageSize} OFFSET #{pageVO.mysqlStartIndex}
    </select>

    <select id="findByPageCount" resultType="int">
        SELECT COUNT(1)
        FROM fin_dm_opt_foi.${procurementBottomVO.tablePreFix}_base_data_review_info_t t1
        <include refid="searchFields"/>
        <if test='procurementBottomVO.pageFlag!= null and procurementBottomVO.pageFlag == "abnormal"'>
            AND t1.modify_type !='REVOKE'
        </if>
    </select>

    <select id="getConfigItemDropDown" resultMap="resultMap">
        SELECT DISTINCT
        <if test='l3CegCode != null and l3CegCode !=""'>
            l4_ceg_code,l4_ceg_short_cn_name,
        </if>
        <if test='l4CegCode != null and l4CegCode !=""'>
            category_code,category_cn_name,
        </if>
        <if test='categoryCode != null and categoryCode !=""'>
            item_code,item_cn_name,
        </if>
        l3_ceg_code,l3_ceg_short_cn_name
        FROM
        fin_dm_opt_foi.${tablePreFix}_view_info_d
        WHERE
        del_flag = 'N'
        AND group_level = 'ITEM'
        <if test='l3CegCode != null and l3CegCode!=""'>
            AND l3_ceg_code=#{l3CegCode,jdbcType=VARCHAR}
        </if>
        <if test='l4CegCode != null and l4CegCode!=""'>
            AND l4_ceg_code=#{l4CegCode,jdbcType=VARCHAR}
        </if>
        <if test='categoryCode != null and categoryCode!=""'>
            AND category_code=#{categoryCode,jdbcType=VARCHAR}
        </if>
        <if test='caliberFlag != null and caliberFlag!=""'>
            AND caliber_flag=#{caliberFlag,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="checkStartPeriod" resultMap="resultMap">
        SELECT DISTINCT period_id
        FROM fin_dm_opt_foi.${tablePreFix}_mid_month_item_t
        WHERE
        del_flag = 'N'
        <if test='itemCode != null and itemCode!=""'>
            AND item_code=#{itemCode,jdbcType=VARCHAR}
        </if>
        <if test='periodId != null'>
            AND period_id>#{periodId,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getImpactQty" resultMap="resultMap">
        SELECT
        COUNT(1) OVER() AS CNT
        ,COUNT(1) OVER(PARTITION BY RMB_AVG_AMT ORDER BY RMB_AVG_AMT DESC) AS RMB_AVG_CNT
        FROM(SELECT
        SUM(TO_NUMBER(GS_DECRYPT(RMB_COST_AMT,#{keyStr,jdbcType=VARCHAR}, 'AES128', 'CBC', 'SHA256')))/SUM(SHIP_QUANTITY) AS RMB_AVG_AMT
        FROM fin_dm_opt_foi.${tablePreFix}_mid_month_item_t
        WHERE
        <if test='itemCode != null'>
            item_code=#{itemCode,jdbcType=VARCHAR}
        </if>
        <if test='startPeriod != null and endPeriod != null'>
            AND period_id
            BETWEEN #{startPeriod,jdbcType=BIGINT} AND #{endPeriod,jdbcType=BIGINT}
        </if>
        <if test='caliberFlag != null'>
            AND caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        GROUP BY LV0_PROD_LIST_CODE
        ,OVERSEA_FLAG
        ,LV0_PROD_RND_TEAM_CODE
        ,LV1_PROD_RND_TEAM_CODE
        ,LV2_PROD_RND_TEAM_CODE
        ,LV3_PROD_RND_TEAM_CODE

        UNION ALL
        SELECT
        SUM(TO_NUMBER(GS_DECRYPT(RMB_COST_AMT,#{keyStr,jdbcType=VARCHAR}, 'AES128', 'CBC', 'SHA256')))/SUM(SHIP_QUANTITY) AS RMB_AVG_AMT
        FROM fin_dm_opt_foi.${tablePreFix}_pft_mid_month_item_t
        WHERE
        <if test='itemCode != null'>
            item_code=#{itemCode,jdbcType=VARCHAR}
        </if>
        <if test='startPeriod != null and endPeriod != null'>
            AND period_id
            BETWEEN #{startPeriod,jdbcType=BIGINT} AND #{endPeriod,jdbcType=BIGINT}
        </if>
        <if test='caliberFlag != null'>
            AND caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        GROUP BY LV0_PROD_LIST_CODE
        ,OVERSEA_FLAG
        ,LV0_PROD_RND_TEAM_CODE
        ,LV1_PROD_RND_TEAM_CODE
        ,LV2_PROD_RND_TEAM_CODE
        ,LV3_PROD_RND_TEAM_CODE
        ,L1_NAME
        ,L2_NAME

        UNION ALL
        SELECT
        SUM(TO_NUMBER(GS_DECRYPT(RMB_COST_AMT,#{keyStr,jdbcType=VARCHAR}, 'AES128', 'CBC', 'SHA256')))/SUM(SHIP_QUANTITY) AS RMB_AVG_AMT
        FROM fin_dm_opt_foi.${tablePreFix}_dms_mid_month_item_t
        WHERE
        <if test='itemCode != null'>
            item_code=#{itemCode,jdbcType=VARCHAR}
        </if>
        <if test='startPeriod != null and endPeriod != null'>
            AND period_id
            BETWEEN #{startPeriod,jdbcType=BIGINT} AND #{endPeriod,jdbcType=BIGINT}
        </if>
        <if test='caliberFlag != null'>
            AND caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        GROUP BY LV0_PROD_LIST_CODE
        ,OVERSEA_FLAG
        ,LV0_PROD_RND_TEAM_CODE
        ,LV1_PROD_RND_TEAM_CODE
        ,LV2_PROD_RND_TEAM_CODE
        ,LV3_PROD_RND_TEAM_CODE
        ,DIMENSION_CODE
        ,DIMENSION_SUBCATEGORY_CODE
        ,DIMENSION_SUB_DETAIL_CODE
        ) LIMIT 1
    </select>

    <select id="getNewImpactQty" resultMap="resultMap">
        SELECT
        COUNT(1) OVER() AS CNT
        ,COUNT(1) OVER(PARTITION BY RMB_AVG_AMT ORDER BY RMB_AVG_AMT DESC) AS RMB_AVG_CNT
        FROM(SELECT
        SUM(TO_NUMBER(GS_DECRYPT(RMB_COST_AMT,#{keyStr,jdbcType=VARCHAR}, 'AES128', 'CBC', 'SHA256'))) AS RMB_AVG_AMT
        FROM fin_dm_opt_foi.${tablePreFix}_base_detail_item_fcst_t
        WHERE
        <if test='itemCode != null'>
            item_code=#{itemCode,jdbcType=VARCHAR}
        </if>
        <if test='startPeriod != null and endPeriod != null'>
            AND period_id
            BETWEEN #{startPeriod,jdbcType=BIGINT} AND #{endPeriod,jdbcType=BIGINT}
        </if>
        <if test='caliberFlag != null'>
            AND caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        GROUP BY LV0_PROD_LIST_CODE
        ,OVERSEA_FLAG
        ,LV0_PROD_RND_TEAM_CODE
        ,LV1_PROD_RND_TEAM_CODE
        ,LV2_PROD_RND_TEAM_CODE
        ,LV3_PROD_RND_TEAM_CODE

        UNION ALL
        SELECT
        SUM(TO_NUMBER(GS_DECRYPT(RMB_COST_AMT,#{keyStr,jdbcType=VARCHAR}, 'AES128', 'CBC', 'SHA256'))) AS RMB_AVG_AMT
        FROM fin_dm_opt_foi.${tablePreFix}_pft_base_detail_item_fcst_t
        WHERE
        <if test='itemCode != null'>
            item_code=#{itemCode,jdbcType=VARCHAR}
        </if>
        <if test='startPeriod != null and endPeriod != null'>
            AND period_id
            BETWEEN #{startPeriod,jdbcType=BIGINT} AND #{endPeriod,jdbcType=BIGINT}
        </if>
        <if test='caliberFlag != null'>
            AND caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        GROUP BY LV0_PROD_LIST_CODE
        ,OVERSEA_FLAG
        ,LV0_PROD_RND_TEAM_CODE
        ,LV1_PROD_RND_TEAM_CODE
        ,LV2_PROD_RND_TEAM_CODE
        ,L1_NAME
        ,L2_NAME

        UNION ALL
        SELECT
        SUM(TO_NUMBER(GS_DECRYPT(RMB_COST_AMT,#{keyStr,jdbcType=VARCHAR}, 'AES128', 'CBC', 'SHA256'))) AS RMB_AVG_AMT
        FROM fin_dm_opt_foi.${tablePreFix}_dms_base_detail_item_fcst_t
        WHERE
        <if test='itemCode != null'>
            item_code=#{itemCode,jdbcType=VARCHAR}
        </if>
        <if test='startPeriod != null and endPeriod != null'>
            AND period_id
            BETWEEN #{startPeriod,jdbcType=BIGINT} AND #{endPeriod,jdbcType=BIGINT}
        </if>
        <if test='caliberFlag != null'>
            AND caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        GROUP BY LV0_PROD_LIST_CODE
        ,OVERSEA_FLAG
        ,LV0_PROD_RND_TEAM_CODE
        ,LV1_PROD_RND_TEAM_CODE
        ,LV2_PROD_RND_TEAM_CODE
        ,LV3_PROD_RND_TEAM_CODE
        ,DIMENSION_CODE
        ,DIMENSION_SUBCATEGORY_CODE
        ,DIMENSION_SUB_DETAIL_CODE
        ) LIMIT 1
    </select>

    <select id="findBottomReviewList" resultType="java.util.Map">
        SELECT
        n1.*,n2.version
        FROM(
        SELECT
        "version_id",
        "l3_ceg_code",
        "l3_ceg_short_cn_name",
        "l4_ceg_code",
        "l4_ceg_short_cn_name",
        "category_code",
        "category_cn_name",
        "item_code",
        "item_cn_name",
        "start_period",
        "end_period",
        "modify_reason",
        "modify_type",
        (CASE WHEN modify_type = 'INSERT' THEN modify_reason END)  as modify_reason_i,
        (CASE WHEN modify_type = 'MODIFY' THEN modify_reason END)  as modify_reason_m,
        (CASE WHEN modify_type = 'REVOKE' THEN modify_reason END)  as modify_reason_r,
        "impact_qty",
        "caliber_flag",
        "last_updated_by",
        "last_update_date"
        FROM fin_dm_opt_foi.${tablePreFix}_base_data_review_info_t
        WHERE
        del_flag = 'N'
        <if test='versionId != null'>
            AND version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='caliberFlag != null'>
            AND caliber_flag=#{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='l3CegCode != null and l3CegCode!=""'>
            AND l3_ceg_code=#{l3CegCode,jdbcType=VARCHAR}
        </if>
        <if test='pageFlag != null and pageFlag!=""'>
            AND (page_flag=#{pageFlag,jdbcType=VARCHAR} or page_flag is null)
        </if>
        <if test='l4CegCode != null and l4CegCode!=""'>
            AND l4_ceg_code=#{l4CegCode,jdbcType=VARCHAR}
        </if>
        <if test='categoryCode != null and categoryCode!=""'>
            AND category_code=#{categoryCode,jdbcType=VARCHAR}
        </if>
        <choose>
            <when test='pageFlag!= null and pageFlag == "abnormal"'>
                AND modify_type !='REVOKE'
            </when>
            <otherwise>
            </otherwise>
        </choose>
        )n1
        LEFT JOIN (
        SELECT version_id, version
        FROM fin_dm_opt_foi.${tablePreFix}_version_info_t
        WHERE del_flag = 'N'
        <if test='dataType != null and dataType!=""'>
            AND data_type=#{dataType,jdbcType=VARCHAR}
        </if>) n2
        ON n1.version_id = n2.version_id
        ORDER BY version
    </select>

    <select id="findBottomReviewVOList" resultMap="resultMap">
        SELECT
        <include refid="allFields"/>
        FROM fin_dm_opt_foi.${tablePreFix}_base_data_review_info_t
        WHERE
        del_flag = 'N' and (page_flag ='abnormal' or page_flag is null)
        <if test='versionId != null'>
            and version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='caliberFlag != null'>
            AND caliber_flag=#{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='l3CegCode != null and l3CegCode!=""'>
            AND l3_ceg_code=#{l3CegCode,jdbcType=VARCHAR}
        </if>
        <if test='l4CegCode != null and l4CegCode!=""'>
            AND l4_ceg_code=#{l4CegCode,jdbcType=VARCHAR}
        </if>
        <if test='categoryCode != null and categoryCode!=""'>
            AND category_code=#{categoryCode,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="findBaseDropDown" resultMap="resultMap">
        SELECT DISTINCT
        <if test='l3CegCode != null and l3CegCode !=""'>
            l4_ceg_code,l4_ceg_short_cn_name,
        </if>
        <if test='l4CegCode != null and l4CegCode !=""'>
            category_code,category_cn_name,
        </if>
        <if test='categoryCode != null and categoryCode !=""'>
            item_code,item_cn_name,
        </if>
        l3_ceg_code,l3_ceg_short_cn_name
        FROM fin_dm_opt_foi.${tablePreFix}_base_data_review_info_t
        WHERE
        del_flag = 'N'
        <if test='versionId != null'>
            and version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='caliberFlag != null'>
            AND caliber_flag=#{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='pageFlag != null and pageFlag!=""'>
            AND (page_flag=#{pageFlag,jdbcType=VARCHAR} or page_flag is null)
        </if>
        <if test='l3CegCode != null and l3CegCode!=""'>
            AND l3_ceg_code=#{l3CegCode,jdbcType=VARCHAR}
        </if>
        <if test='l4CegCode != null and l4CegCode!=""'>
            AND l4_ceg_code=#{l4CegCode,jdbcType=VARCHAR}
        </if>
        <if test='categoryCode != null and categoryCode!=""'>
            AND category_code=#{categoryCode,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getItemImpactQty" resultMap="resultMap">
        SELECT id, cnt, rmb_avg_cnt, item_code, start_period, end_period
        from fin_dm_opt_foi.${tablePreFix}_impact_item_qty_t
        where id = #{id}
    </select>

    <insert id="createProcurementList" parameterType="java.util.List">
        INSERT INTO fin_dm_opt_foi.${tablePreFix}_base_data_review_info_t
        (<include refid="allFields"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.versionId,jdbcType=NUMERIC},
            #{item.l3CegCode,jdbcType=VARCHAR},
            #{item.l3CegShortCnName,jdbcType=VARCHAR},
            #{item.l4CegCode,jdbcType=VARCHAR},
            #{item.l4CegShortCnName,jdbcType=VARCHAR},
            #{item.categoryCode,jdbcType=VARCHAR},
            #{item.categoryCnName,jdbcType=VARCHAR},
            #{item.itemCode,jdbcType=VARCHAR},
            #{item.itemCnName,jdbcType=VARCHAR},
            #{item.startPeriod,jdbcType=BIGINT},
            #{item.endPeriod,jdbcType=BIGINT},
            #{item.modifyReason,jdbcType=VARCHAR},
            #{item.modifyType,jdbcType=VARCHAR},
            #{item.impactQty,jdbcType=BIGINT},
            #{item.caliberFlag,jdbcType=VARCHAR},
            #{item.createdBy,jdbcType=VARCHAR},
            #{item.creationDate,jdbcType=TIMESTAMP},
            #{item.lastUpdatedBy,jdbcType=VARCHAR},
            #{item.lastUpdateDate,jdbcType=TIMESTAMP},
            #{item.delFlag,jdbcType=VARCHAR},
            #{item.pageFlag,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <insert id="insertItemImpactQty">
        INSERT INTO fin_dm_opt_foi.${tablePreFix}_impact_item_qty_t
        (id, cnt, rmb_avg_cnt, item_code, start_period, end_period,user_id,role_id)
        VALUES (#{taskId},
                #{cnt},
                #{crmAvgCnt},
                #{itemCode},
                #{startPeriod},
                #{endPeriod},
                #{userId},
                #{roleId})
    </insert>

</mapper>
