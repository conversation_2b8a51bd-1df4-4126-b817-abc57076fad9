/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.annual;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

/**
 * AnnualAnalysisVOTest Class
 *
 * <AUTHOR>
 * @since 2023/4/13
 */
public class AnnualAnalysisVOTest extends BaseVOCoverUtilsTest<AnnualAnalysisVO> {

    @Override
    protected Class<AnnualAnalysisVO> getTClass() {
        return AnnualAnalysisVO.class;
    }
}