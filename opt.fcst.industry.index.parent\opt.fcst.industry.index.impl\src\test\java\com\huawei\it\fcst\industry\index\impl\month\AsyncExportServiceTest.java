/*
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.month;

import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.when;

import com.huawei.it.fcst.industry.index.constant.Constant;
import com.huawei.it.fcst.industry.index.dao.IDataCipherTextDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocActualCostDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeActualCostDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeMonthWeightDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeRecMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMonthWeightDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocRecMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTotalActualCostDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTotalMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTotalMonthWeightDao;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumP;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumU;
import com.huawei.it.fcst.industry.index.impl.annual.AnnualCommonService;
import com.huawei.it.fcst.industry.index.service.common.ICommonService;
import com.huawei.it.fcst.industry.index.utils.ExcelExportUtil;
import com.huawei.it.fcst.industry.index.utils.FcstIndexMadeUtil;
import com.huawei.it.fcst.industry.index.utils.FcstIndexUtil;
import com.huawei.it.fcst.industry.index.utils.ObjectCopyUtil;
import com.huawei.it.fcst.industry.index.vo.ciphertext.VarifyTaskVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.month.CompareAnalysisVO;
import com.huawei.it.fcst.industry.index.vo.month.DmFocActualCostVO;
import com.huawei.it.fcst.industry.index.vo.month.DmFocMonthCostIdxVO;
import com.huawei.it.fcst.industry.index.vo.month.DmFocMonthWeightVO;
import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.lookup.LookupItemVO;
import com.huawei.it.jalor5.lookup.service.ILookupItemQueryService;
import com.huawei.it.jalor5.security.UserVO;

import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @since 2023/4/14
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest( {FcstIndexUtil.class, FcstIndexMadeUtil.class})
public class AsyncExportServiceTest {

    @InjectMocks
    private AsyncExportService asyncExportService;

    @Mock
    private IDmFocMonthCostIdxDao dmFocMonthCostIdxDao;

    @Mock
    private IDmFocMonthWeightDao dmFocMonthWeightDao;

    @Mock
    private IDmFocActualCostDao dmFocActualCostDao;

    @Mock
    private ICommonService commonService;

    @Mock
    private MonthCommonService monthCommonService;

    @Mock
    private ILookupItemQueryService lookupItemQueryService;

    @Mock
    private AnnualCommonService annualCommonService;

    @Mock
    private IDmFocRecMonthCostIdxDao dmFocRecMonthCostIdxDao;

    @Mock
    private IDataCipherTextDao iDataCipherTextDao;

    @Mock
    private IDmFocMadeActualCostDao dmFocMadeActualCostDao;

    @Mock
    private IDmFocTotalActualCostDao dmFocTotalActualCostDao;

    @Mock
    private IDmFocMadeMonthCostIdxDao dmFocMadeMonthCostIdxDao;

    @Mock
    private IDmFocTotalMonthCostIdxDao dmFocTotalMonthCostIdxDao;

    @Mock
    private IDmFocMadeMonthWeightDao dmFocMadeMonthWeightDao;

    @Mock
    private IDmFocTotalMonthWeightDao dmFocTotalMonthWeightDao;

    @Mock
    private IDmFocMadeRecMonthCostIdxDao dmFocMadeRecMonthCostIdxDao;

    @Before
    public void before() throws Exception {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);
    }

    @Test
    public void fillHeapTemplate1Sheet() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setIsContainComb(false);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setCaliberFlag("R");
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setCostType("P");
        String groupCnName = "重量级团队LV1";
        List<LookupItemVO> universalItemList = new ArrayList<>();
        LookupItemVO lookupItemVO = new LookupItemVO();
        lookupItemVO.setItemCode("1");
        lookupItemVO.setItemName("ICT-专项采购认证部-品类");
        universalItemList.add(lookupItemVO);
        PowerMockito.doReturn(universalItemList).when(lookupItemQueryService).findItemListByClassify(Constant.StrEnum.PURCHASE_LOOKUP_UNIVERSAL_VIEW.getValue());
        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","L2");
        map.put("nextGroupName","name1");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);
        IRequestContext current = RequestContextManager.getCurrent();
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        resultDataVO.setData(bgInfoList);
        PowerMockito.doReturn(resultDataVO).when(annualCommonService).getBgInfoList(any());
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate1.xlsx");
        ResultDataVO periodYearList = new ResultDataVO();
        List<String> yearList = new ArrayList<>();
        yearList.add("2013");
        periodYearList.setData(yearList);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList);
        Assertions.assertNotNull(asyncExportService.fillHeapTemplate1Sheet(monthAnalysisVO,workbook,2,groupCnName,current));
    }

    @Test
    public void fillHeapTemplate1Sheet2test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("1");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setIsContainComb(false);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setCaliberFlag("C");
        monthAnalysisVO.setGranularityType("P");
        String groupCnName = "重量级团队LV1";
        List<LookupItemVO> universalItemList = new ArrayList<>();
        LookupItemVO lookupItemVO = new LookupItemVO();
        lookupItemVO.setItemCode("1");
        lookupItemVO.setItemName("ICT-专项采购认证部-品类");
        universalItemList.add(lookupItemVO);
        PowerMockito.doReturn(universalItemList).when(lookupItemQueryService).findItemListByClassify(Constant.StrEnum.PURCHASE_LOOKUP_UNIVERSAL_VIEW.getValue());
        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","CEG");
        map.put("nextGroupName","name1");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        resultDataVO.setData(bgInfoList);
        PowerMockito.doReturn(resultDataVO).when(annualCommonService).getBgInfoList(any());
        IRequestContext current = RequestContextManager.getCurrent();
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate1.xlsx");
        ResultDataVO periodYearList = new ResultDataVO();
        List<String> yearList = new ArrayList<>();
        yearList.add("2013");
        periodYearList.setData(yearList);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList);
        Assertions.assertNotNull(asyncExportService.fillHeapTemplate1Sheet(monthAnalysisVO,workbook,2,groupCnName,current));
    }

    @Test
    public void fillHeapTemplate1Sheet3test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setGroupLevel("LV1");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("2");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIsContainComb(false);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        String groupCnName = "重量级团队LV2";
        monthAnalysisVO.setCaliberFlag("R");
        monthAnalysisVO.setGranularityType("U");
        List<LookupItemVO> universalItemList = new ArrayList<>();
        LookupItemVO lookupItemVO = new LookupItemVO();
        lookupItemVO.setItemCode("1");
        lookupItemVO.setItemName("ICT-专项采购认证部-品类");
        universalItemList.add(lookupItemVO);
        PowerMockito.doReturn(universalItemList).when(lookupItemQueryService).findItemListByClassify(Constant.StrEnum.PURCHASE_LOOKUP_UNIVERSAL_VIEW.getValue());
        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","CEG");
        map.put("nextGroupName","name1");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        resultDataVO.setData(bgInfoList);
        PowerMockito.doReturn(resultDataVO).when(annualCommonService).getBgInfoList(any());
        IRequestContext current = RequestContextManager.getCurrent();
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate1.xlsx");
        ResultDataVO periodYearList = new ResultDataVO();
        List<String> yearList = new ArrayList<>();
        yearList.add("2013");
        periodYearList.setData(yearList);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList);
        Assertions.assertNotNull(asyncExportService.fillHeapTemplate1Sheet(monthAnalysisVO,workbook,2,groupCnName,current));
    }

    @Test
    public void fillHeapTemplate1Sheet4test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setGroupLevel("LV1");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("2");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIsContainComb(false);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        String groupCnName = "重量级团队LV2";
        monthAnalysisVO.setCaliberFlag("C");
        monthAnalysisVO.setGranularityType("U");
        List<LookupItemVO> universalItemList = new ArrayList<>();
        LookupItemVO lookupItemVO = new LookupItemVO();
        lookupItemVO.setItemCode("1");
        lookupItemVO.setItemName("ICT-专项采购认证部-品类");
        universalItemList.add(lookupItemVO);
        PowerMockito.doReturn(universalItemList).when(lookupItemQueryService).findItemListByClassify(Constant.StrEnum.PURCHASE_LOOKUP_UNIVERSAL_VIEW.getValue());
        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","CEG");
        map.put("nextGroupName","name1");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        resultDataVO.setData(bgInfoList);
        PowerMockito.doReturn(resultDataVO).when(annualCommonService).getBgInfoList(any());
        IRequestContext current = RequestContextManager.getCurrent();
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate1.xlsx");
        ResultDataVO periodYearList = new ResultDataVO();
        List<String> yearList = new ArrayList<>();
        yearList.add("2013");
        periodYearList.setData(yearList);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList);
        Assertions.assertNotNull(asyncExportService.fillHeapTemplate1Sheet(monthAnalysisVO,workbook,2,groupCnName,current));
    }

    @Test
    public void fillHeapTemplate1Sheet5test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(true);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("P");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        String groupCnName = "重量级团队LV1";
        monthAnalysisVO.setCaliberFlag("R");
        monthAnalysisVO.setGranularityType("U");
        List<LookupItemVO> universalItemList = new ArrayList<>();
        LookupItemVO lookupItemVO = new LookupItemVO();
        lookupItemVO.setItemCode("1");
        lookupItemVO.setItemName("ICT-专项采购认证部-品类");
        universalItemList.add(lookupItemVO);
        PowerMockito.doReturn(universalItemList).when(lookupItemQueryService).findItemListByClassify(Constant.StrEnum.PURCHASE_LOOKUP_UNIVERSAL_VIEW.getValue());
        String refreshMonth = "202305";
        Map map = new HashMap();
        map.put("nextGroupLevel","CEG");
        map.put("nextGroupName","name1");
        mockStatic(FcstIndexUtil.class);
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        resultDataVO.setData(bgInfoList);
        PowerMockito.doReturn(resultDataVO).when(annualCommonService).getBgInfoList(any());
        IRequestContext current = RequestContextManager.getCurrent();
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        XSSFSheet sheetAt = workbook.getSheetAt(2);
        sheetAt.setRowBreak(7);
        sheetAt.createRow(7);
        XSSFRow row = sheetAt.getRow(7);
        row.createCell(5);
        ResultDataVO periodYearList = new ResultDataVO();
        List<String> yearList = new ArrayList<>();
        yearList.add("2013");
        periodYearList.setData(yearList);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList);
        Assertions.assertNotNull(asyncExportService.fillHeapTemplate1Sheet(monthAnalysisVO,workbook,2,groupCnName,current));
    }

    @Test
    public void fillHeapTemplate1Sheet6test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(true);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("M");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        String groupCnName = "重量级团队LV1";
        monthAnalysisVO.setCaliberFlag("R");
        monthAnalysisVO.setGranularityType("U");
        List<LookupItemVO> universalItemList = new ArrayList<>();
        LookupItemVO lookupItemVO = new LookupItemVO();
        lookupItemVO.setItemCode("1");
        lookupItemVO.setItemName("ICT-专项采购认证部-品类");
        universalItemList.add(lookupItemVO);
        PowerMockito.doReturn(universalItemList).when(lookupItemQueryService).findItemListByClassify(Constant.StrEnum.PURCHASE_LOOKUP_UNIVERSAL_VIEW.getValue());

       mockMadeNextGroupLevel();
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        resultDataVO.setData(bgInfoList);
        PowerMockito.doReturn(resultDataVO).when(annualCommonService).getBgInfoList(any());
        IRequestContext current = RequestContextManager.getCurrent();
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        XSSFSheet sheetAt = workbook.getSheetAt(2);
        sheetAt.setRowBreak(7);
        sheetAt.createRow(7);
        XSSFRow row = sheetAt.getRow(7);
        row.createCell(5);
        ResultDataVO periodYearList = new ResultDataVO();
        List<String> yearList = new ArrayList<>();
        yearList.add("2013");
        periodYearList.setData(yearList);
        Mockito.when(dmFocMonthCostIdxDao.findActualMonthNum(anyString())).thenReturn(202406L);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList);
        Assertions.assertNotNull(asyncExportService.fillHeapTemplate1Sheet(monthAnalysisVO,workbook,2,groupCnName,current));
    }

    @Test
    public void fillHeapTemplate1Sheet7test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(true);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("T");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        String groupCnName = "重量级团队LV1";
        monthAnalysisVO.setCaliberFlag("R");
        monthAnalysisVO.setGranularityType("U");
        List<LookupItemVO> universalItemList = new ArrayList<>();
        LookupItemVO lookupItemVO = new LookupItemVO();
        lookupItemVO.setItemCode("1");
        lookupItemVO.setItemName("ICT-专项采购认证部-品类");
        universalItemList.add(lookupItemVO);
        PowerMockito.doReturn(universalItemList).when(lookupItemQueryService).findItemListByClassify(Constant.StrEnum.PURCHASE_LOOKUP_UNIVERSAL_VIEW.getValue());
        Map map = new HashMap();
        map.put("nextGroupLevel","CEG");
        map.put("nextGroupName","name1");
        mockStatic(FcstIndexUtil.class);
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        resultDataVO.setData(bgInfoList);
        PowerMockito.doReturn(resultDataVO).when(annualCommonService).getBgInfoList(any());
        IRequestContext current = RequestContextManager.getCurrent();
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        XSSFSheet sheetAt = workbook.getSheetAt(2);
        sheetAt.setRowBreak(7);
        sheetAt.createRow(7);
        XSSFRow row = sheetAt.getRow(7);
        row.createCell(5);
        ResultDataVO periodYearList = new ResultDataVO();
        List<String> yearList = new ArrayList<>();
        yearList.add("2013");
        periodYearList.setData(yearList);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList);
        Assertions.assertNotNull(asyncExportService.fillHeapTemplate1Sheet(monthAnalysisVO,workbook,2,groupCnName,current));
    }

    @Test
    public void fillHeapTemplate2Sheet() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setFileName("月度分析-明细数据-2023-04-12 16_06_05");
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("2");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIsContainComb(false);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("19382");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("135229");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setCaliberFlag("R");
        monthAnalysisVO.setGranularityType("U");
        String groupCnName = "品类";
        IRequestContext current = RequestContextManager.getCurrent();
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        List<DmFocActualCostVO> heapMapExpData = new ArrayList<>();
        DmFocActualCostVO focActualCostVO = new DmFocActualCostVO();
        focActualCostVO.setActualCostAmt(12d);
        focActualCostVO.setPeriodId(202201L);
        focActualCostVO.setGroupCode("1411");
        focActualCostVO.setGroupCnName("普通端子");
        focActualCostVO.setCostType("P");
        heapMapExpData.add(focActualCostVO);

        List<LookupItemVO> universalItemList = new ArrayList<>();
        LookupItemVO lookupItemVO = new LookupItemVO();
        lookupItemVO.setItemCode("2");
        lookupItemVO.setItemName("ICT");
        universalItemList.add(lookupItemVO);

        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        resultDataVO.setData(bgInfoList);
        PowerMockito.doReturn(resultDataVO).when(annualCommonService).getBgInfoList(any());
        PowerMockito.doReturn(heapMapExpData).when(dmFocActualCostDao).findHeapMapExpData(any());
        //PowerMockito.when(commonService).setViewFlagValueWithLookUp(any());
        PowerMockito.doReturn(universalItemList).when(lookupItemQueryService).findItemListByClassify(any());
        Future<Integer> future = null;
        try {
            future = asyncExportService.fillHeapTemplate2Sheet(monthAnalysisVO,workbook,2,groupCnName,current);
        } catch (Exception e) {
        }
        Assertions.assertNull(future);
    }

    @Test
    public void fillHeapTemplate2Sheet1test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setFileName("月度分析-明细数据-2023-04-12 16_06_05");
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("4");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(true);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("M");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("19382");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("135229");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        String groupCnName = "品类";
        monthAnalysisVO.setCaliberFlag("R");
        monthAnalysisVO.setGranularityType("P");
        IRequestContext current = RequestContextManager.getCurrent();
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        List<DmFocActualCostVO> heapMapExpData = new ArrayList<>();
        DmFocActualCostVO focActualCostVO = new DmFocActualCostVO();
        focActualCostVO.setActualCostAmt(12d);
        focActualCostVO.setPeriodId(202201L);
        focActualCostVO.setGroupCode("1411");
        focActualCostVO.setGroupCnName("普通端子");
        focActualCostVO.setCostType("M");
        heapMapExpData.add(focActualCostVO);
        PowerMockito.doReturn(heapMapExpData).when(dmFocActualCostDao).findHeapMapExpData(any());
        List<LookupItemVO> universalItemList = new ArrayList<>();
        LookupItemVO lookupItemVO = new LookupItemVO();
        lookupItemVO.setItemCode("1");
        lookupItemVO.setItemName("ICT-专项采购认证部-品类");
        universalItemList.add(lookupItemVO);
        PowerMockito.doReturn(universalItemList).when(lookupItemQueryService).findItemListByClassify(Constant.StrEnum.PURCHASE_LOOKUP_UNIVERSAL_VIEW.getValue());
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        resultDataVO.setData(bgInfoList);
        PowerMockito.doReturn(resultDataVO).when(annualCommonService).getBgInfoList(any());
        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","L2");
        map.put("nextGroupName","name1");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);
        Future<Integer> future = null;
        try {
            future = asyncExportService.fillHeapTemplate2Sheet(monthAnalysisVO,workbook,2,groupCnName,current);
        } catch (Exception e) {
        }
        Assertions.assertNull(future);
    }

    @Test
    public void fillHeapTemplate2Sheet2test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setFileName("月度分析-明细数据-2023-04-12 16_06_05");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("4");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(true);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("M");
        monthAnalysisVO.setGroupLevel(GroupLevelEnumP.L2.getValue());
        monthAnalysisVO.setCaliberFlag("C");
        monthAnalysisVO.setGranularityType("U");
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        String groupCnName = "品类";
        IRequestContext current = RequestContextManager.getCurrent();

        ResultDataVO periodYearList = new ResultDataVO();
        List<String> annualPeriodYear = new ArrayList<>();
        annualPeriodYear.add("2021");
        annualPeriodYear.add("2022");
        periodYearList.setData(annualPeriodYear);
        Mockito.when(annualCommonService.getAnnualPeriodYear(Mockito.anyString(),any())).thenReturn(periodYearList);
        HashMap map = new HashMap();
        map.put("nextGroupLevel",GroupLevelEnumP.L2.getValue());
        map.put("nextGroupName",GroupLevelEnumP.L2.getName());
        mockStatic(FcstIndexMadeUtil.class);
        when(FcstIndexMadeUtil.class, "getNextGroupLevel", any()).thenReturn(map);
        ResultDataVO resultDataVO1 = new ResultDataVO();
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        resultDataVO1.setData(bgInfoList);
        Mockito.when(annualCommonService.getBgInfoList(any())).thenReturn(resultDataVO1);
        List<DmFocActualCostVO> heapMapExpData = getHeapMapExpData();
        Mockito.when(dmFocMonthCostIdxDao.findActualMonthNum(anyString())).thenReturn(202406L);
        Mockito.when(dmFocActualCostDao.findHeapMapExpData(any())).thenReturn(heapMapExpData);
        Future<Integer> future = null;
        // CostType为M
        future = asyncExportService.fillHeapTemplate2Sheet(monthAnalysisVO,workbook,2,groupCnName,current);
        Assertions.assertNotNull(future);
        // CostType为P
        monthAnalysisVO.setCostType("P");
        mockStatic(FcstIndexUtil.class);
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);
        Mockito.when(dmFocMadeActualCostDao.findHeapMapExpData(any())).thenReturn(heapMapExpData);
        future = asyncExportService.fillHeapTemplate2Sheet(monthAnalysisVO,workbook,2,groupCnName,current);
        Assertions.assertNotNull(future);
    }

    @Test
    public void fillHeapTemplate2Sheet3test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setFileName("月度分析-明细数据-2023-04-12 16_06_05");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("4");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(true);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("M");
        monthAnalysisVO.setGroupLevel(GroupLevelEnumP.L2.getValue());
        monthAnalysisVO.setCaliberFlag("C");
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setIndustryOrg("ICT");
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        String groupCnName = "品类";
        IRequestContext current = RequestContextManager.getCurrent();

        ResultDataVO periodYearList = new ResultDataVO();
        List<String> annualPeriodYear = new ArrayList<>();
        annualPeriodYear.add("2021");
        annualPeriodYear.add("2022");
        periodYearList.setData(annualPeriodYear);
        Mockito.when(annualCommonService.getAnnualPeriodYear(Mockito.anyString(),any())).thenReturn(periodYearList);
        HashMap map = new HashMap();
        map.put("nextGroupLevel",GroupLevelEnumP.L2.getValue());
        map.put("nextGroupName",GroupLevelEnumP.L2.getName());
        mockStatic(FcstIndexMadeUtil.class);
        when(FcstIndexMadeUtil.class, "getNextGroupLevel", any()).thenReturn(map);
        ResultDataVO resultDataVO1 = new ResultDataVO();
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        resultDataVO1.setData(bgInfoList);
        Mockito.when(annualCommonService.getBgInfoList(any())).thenReturn(resultDataVO1);
        List<DmFocActualCostVO> heapMapExpData = getHeapMapExpData();
        Mockito.when(dmFocActualCostDao.findHeapMapExpData(any())).thenReturn(heapMapExpData);
        Future<Integer> future = null;
        mockStatic(FcstIndexUtil.class);
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);
        // CostType为T
        monthAnalysisVO.setCostType("T");
        future = asyncExportService.fillHeapTemplate2Sheet(monthAnalysisVO,workbook,2,groupCnName,current);
        Assertions.assertNotNull(future);
    }

    @Test
    public void fillHeapTemplate2Sheet4test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setFileName("月度分析-明细数据-2023-04-12 16_06_05");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("4");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("M");
        monthAnalysisVO.setGroupLevel(GroupLevelEnumP.L2.getValue());
        monthAnalysisVO.setCaliberFlag("C");
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setIndustryOrg("ICT");
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        String groupCnName = "品类";
        IRequestContext current = RequestContextManager.getCurrent();

        ResultDataVO periodYearList = new ResultDataVO();
        List<String> annualPeriodYear = new ArrayList<>();
        periodYearList.setData(annualPeriodYear);
        Mockito.when(annualCommonService.getAnnualPeriodYear(Mockito.anyString(),any())).thenReturn(periodYearList);
        HashMap map = new HashMap();
        map.put("nextGroupLevel",GroupLevelEnumP.L2.getValue());
        map.put("nextGroupName",GroupLevelEnumP.L2.getName());
        mockStatic(FcstIndexMadeUtil.class);
        when(FcstIndexMadeUtil.class, "getNextGroupLevel", any()).thenReturn(map);
        ResultDataVO resultDataVO1 = new ResultDataVO();
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        resultDataVO1.setData(bgInfoList);
        Mockito.when(annualCommonService.getBgInfoList(any())).thenReturn(resultDataVO1);
        List<DmFocActualCostVO> heapMapExpData = getHeapMapExpData();
        Mockito.when(dmFocActualCostDao.findHeapMapExpData(any())).thenReturn(heapMapExpData);
        Future<Integer> future = null;
        mockStatic(FcstIndexUtil.class);
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);
        // CostType为T
        monthAnalysisVO.setCostType("T");
        future = asyncExportService.fillHeapTemplate2Sheet(monthAnalysisVO,workbook,2,groupCnName,current);
        Assertions.assertNotNull(future);
    }

    @Test
    public void fillDistributeCostSheet1Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setFileName("月度分析-明细数据-2023-04-12 16_06_05");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("4");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(true);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("M");
        monthAnalysisVO.setGroupLevel(GroupLevelEnumP.L2.getValue());
        monthAnalysisVO.setCaliberFlag("C");
        monthAnalysisVO.setGranularityType("U");
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        String groupCnName = "品类";
        IRequestContext current = RequestContextManager.getCurrent();
        List<DmFocActualCostVO> costDistributionList = new ArrayList<>();
        DmFocActualCostVO dmFocActualCostVO = new DmFocActualCostVO();
        dmFocActualCostVO.setPurWeight(0.1);
        dmFocActualCostVO.setMadeWeight(0.2);
        costDistributionList.add(dmFocActualCostVO);
        Mockito.when(dmFocActualCostDao.findCostDistributionList(any())).thenReturn(costDistributionList);
        ResultDataVO resultDataVO1 = new ResultDataVO();
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        resultDataVO1.setData(bgInfoList);
        Mockito.when(annualCommonService.getBgInfoList(any())).thenReturn(resultDataVO1);
        Future<Integer> future = null;
        ResultDataVO periodYearList = new ResultDataVO();
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2023");
        periodYearList.setData(threeYears);
        Mockito.when(dmFocMonthCostIdxDao.findActualMonthNum(anyString())).thenReturn(202406L);
        Mockito.when(dmFocMonthCostIdxDao.findActualMonthNum(anyString())).thenReturn(202406L);
        Mockito.when(annualCommonService.getAnnualPeriodYear(any(),any())).thenReturn(periodYearList);
        future = asyncExportService.fillDistributeCostSheet(monthAnalysisVO,workbook,2,groupCnName,current);
        Assertions.assertNotNull(future);
    }

    @Test
    public void fillDistributeCostSheet2Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setFileName("月度分析-明细数据-2023-04-12 16_06_05");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("4");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("M");
        monthAnalysisVO.setGroupLevel(GroupLevelEnumP.L2.getValue());
        monthAnalysisVO.setCaliberFlag("C");
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setIndustryOrg("ICT");
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        String groupCnName = "品类";
        IRequestContext current = RequestContextManager.getCurrent();

        List<DmFocActualCostVO> costDistributionList = new ArrayList<>();
        DmFocActualCostVO dmFocActualCostVO = new DmFocActualCostVO();
        dmFocActualCostVO.setPurWeight(0.1);
        dmFocActualCostVO.setMadeWeight(0.2);
        costDistributionList.add(dmFocActualCostVO);
        Mockito.when(dmFocActualCostDao.findCostDistributionList(any())).thenReturn(costDistributionList);
        ResultDataVO resultDataVO1 = new ResultDataVO();
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        resultDataVO1.setData(bgInfoList);
        Mockito.when(annualCommonService.getBgInfoList(any())).thenReturn(resultDataVO1);
        Future<Integer> future = null;
        ResultDataVO periodYearList = new ResultDataVO();
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2023");
        periodYearList.setData(threeYears);
        Mockito.when(dmFocMonthCostIdxDao.findActualMonthNum(anyString())).thenReturn(202406L);
        Mockito.when(annualCommonService.getAnnualPeriodYear(any(),any())).thenReturn(periodYearList);
        future = asyncExportService.fillDistributeCostSheet(monthAnalysisVO,workbook,2,groupCnName,current);
        Assertions.assertNotNull(future);
    }

    @Test
    public void fillPriceIdxSheet() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        List<MonthAnalysisVO>  compareMonthVO = new ArrayList<>();
        compareMonthVO.add(monthAnalysisVO);
        CompareAnalysisVO compareAnalysisVO = new CompareAnalysisVO();
        compareAnalysisVO.setCompareMonthVO(compareMonthVO);
        compareAnalysisVO.setIsCompareFlag(true);
        monthAnalysisVO.setCompareAnalysisVO(compareAnalysisVO);
        List<String> groupCnNameList = new ArrayList<>();
        groupCnNameList.add("test");
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setGroupCnNameList(groupCnNameList);
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setIsContainComb(false);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setCostType("P");
        String groupCnName = "重量级团队LV1";
        monthAnalysisVO.setCaliberFlag("R");
        monthAnalysisVO.setGranularityType("U");
        IRequestContext current = RequestContextManager.getCurrent();
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        XSSFSheet sheetAt = workbook.getSheetAt(0);
        sheetAt.createRow(7).createCell(4);
        sheetAt.getRow(3).createCell(4);
        sheetAt.getRow(4).createCell(4);
        sheetAt.getRow(5).createCell(4);
        sheetAt.getRow(6).createCell(4);
        List<DmFocMonthCostIdxVO> priceIndexVOList = new ArrayList<>();
        DmFocMonthCostIdxVO focMonthCostIdxVO = new DmFocMonthCostIdxVO();
        focMonthCostIdxVO.setCostIndex(12d);
        focMonthCostIdxVO.setPeriodId(202201L);
        focMonthCostIdxVO.setCostType("P");
        priceIndexVOList.add(focMonthCostIdxVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        resultDataVO.setData(priceIndexVOList);
        PowerMockito.doReturn(resultDataVO).when(monthCommonService).getCompareIndexChart(any());
        List<LookupItemVO> universalItemList = new ArrayList<>();
        LookupItemVO lookupItemVO = new LookupItemVO();
        lookupItemVO.setItemCode("1");
        lookupItemVO.setItemName("ICT-专项采购认证部-品类");
        universalItemList.add(lookupItemVO);
        PowerMockito.doReturn(universalItemList).when(lookupItemQueryService).findItemListByClassify(Constant.StrEnum.PURCHASE_LOOKUP_UNIVERSAL_VIEW.getValue());
        ResultDataVO resultDataVO1 = new ResultDataVO();
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        resultDataVO1.setData(bgInfoList);
        PowerMockito.doReturn(resultDataVO1).when(annualCommonService).getBgInfoList(any());
        String actualMonth ="202305";
        PowerMockito.doReturn(actualMonth).when(dmFocMonthCostIdxDao).findActualMonth(any());
        Assertions.assertNotNull(asyncExportService.fillPriceIdxSheet(workbook,0,groupCnName,monthAnalysisVO,current));
    }

    @Test
    public void fillPriceIdxSheet1test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        CompareAnalysisVO compareAnalysisVO = new CompareAnalysisVO();
        monthAnalysisVO.setCompareAnalysisVO(compareAnalysisVO);
        compareAnalysisVO.setIsCompareFlag(false);
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("M");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        String groupCnName = "重量级团队LV1";
        monthAnalysisVO.setCaliberFlag("R");
        monthAnalysisVO.setGranularityType("U");
        IRequestContext current = RequestContextManager.getCurrent();
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate1UT.xlsx");
        XSSFSheet sheetAt = workbook.getSheetAt(0);
        sheetAt.createRow(7).createCell(4);
        sheetAt.getRow(3).createCell(4);
        sheetAt.getRow(4).createCell(4);
        sheetAt.getRow(5).createCell(4);
        sheetAt.getRow(6).createCell(4);
        List<DmFocMonthCostIdxVO> priceIndexVOList = new ArrayList<>();
        DmFocMonthCostIdxVO focMonthCostIdxVO = new DmFocMonthCostIdxVO();
        focMonthCostIdxVO.setCostIndex(12d);
        focMonthCostIdxVO.setPeriodId(202201L);
        focMonthCostIdxVO.setCostType("M");
        priceIndexVOList.add(focMonthCostIdxVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        resultDataVO.setData(priceIndexVOList);
        PowerMockito.doReturn(resultDataVO).when(monthCommonService).getIndustryCostIndexChart(any());
        List<LookupItemVO> universalItemList = new ArrayList<>();
        LookupItemVO lookupItemVO = new LookupItemVO();
        lookupItemVO.setItemCode("1");
        lookupItemVO.setItemName("ICT-专项采购认证部-品类");
        universalItemList.add(lookupItemVO);
        PowerMockito.doReturn(universalItemList).when(lookupItemQueryService).findItemListByClassify(Constant.StrEnum.PURCHASE_LOOKUP_UNIVERSAL_VIEW.getValue());
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        ResultDataVO resultDataVO1 = new ResultDataVO();
        resultDataVO1.setData(bgInfoList);
        PowerMockito.doReturn(resultDataVO1).when(annualCommonService).getBgInfoList(any());
        String actualMonth ="202305";
        PowerMockito.doReturn(actualMonth).when(dmFocMonthCostIdxDao).findActualMonth(any());
        Assertions.assertNotNull(asyncExportService.fillPriceIdxSheet(workbook,0,groupCnName,monthAnalysisVO,current));
    }

    @Test
    public void fillPriceIdxSheet2test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        CompareAnalysisVO compareAnalysisVO = new CompareAnalysisVO();
        monthAnalysisVO.setCompareAnalysisVO(compareAnalysisVO);
        compareAnalysisVO.setIsCompareFlag(false);
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("T");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        String groupCnName = "重量级团队LV1";
        monthAnalysisVO.setCaliberFlag("R");
        monthAnalysisVO.setGranularityType("U");
        IRequestContext current = RequestContextManager.getCurrent();
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate1UT.xlsx");
        XSSFSheet sheetAt = workbook.getSheetAt(0);
        sheetAt.createRow(7).createCell(4);
        sheetAt.getRow(3).createCell(4);
        sheetAt.getRow(4).createCell(4);
        sheetAt.getRow(5).createCell(4);
        sheetAt.getRow(6).createCell(4);
        List<DmFocMonthCostIdxVO> priceIndexVOList = new ArrayList<>();
        DmFocMonthCostIdxVO focMonthCostIdxVO = new DmFocMonthCostIdxVO();
        focMonthCostIdxVO.setCostIndex(12d);
        focMonthCostIdxVO.setPeriodId(202201L);
        focMonthCostIdxVO.setCostType("T");
        priceIndexVOList.add(focMonthCostIdxVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        resultDataVO.setData(priceIndexVOList);
        PowerMockito.doReturn(resultDataVO).when(monthCommonService).getIndustryCostIndexChart(any());
        List<LookupItemVO> universalItemList = new ArrayList<>();
        LookupItemVO lookupItemVO = new LookupItemVO();
        lookupItemVO.setItemCode("1");
        lookupItemVO.setItemName("ICT-专项采购认证部-品类");
        universalItemList.add(lookupItemVO);
        PowerMockito.doReturn(universalItemList).when(lookupItemQueryService).findItemListByClassify(Constant.StrEnum.PURCHASE_LOOKUP_UNIVERSAL_VIEW.getValue());
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        ResultDataVO resultDataVO1 = new ResultDataVO();
        resultDataVO1.setData(bgInfoList);
        PowerMockito.doReturn(resultDataVO1).when(annualCommonService).getBgInfoList(any());
        String actualMonth ="202305";
        PowerMockito.doReturn(actualMonth).when(dmFocMonthCostIdxDao).findActualMonth(any());
        Assertions.assertNotNull(asyncExportService.fillPriceIdxSheet(workbook,0,groupCnName,monthAnalysisVO,current));
    }

    @Test
    public void fillPriceIdxSheet3Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        List<MonthAnalysisVO>  compareMonthVO = new ArrayList<>();
        CompareAnalysisVO compareAnalysisVO = new CompareAnalysisVO();
        compareAnalysisVO.setCompareMonthVO(compareMonthVO);
        compareAnalysisVO.setIsCompareFlag(true);
        monthAnalysisVO.setCompareAnalysisVO(compareAnalysisVO);
        List<String> groupCnNameList = new ArrayList<>();
        groupCnNameList.add("test");
        monthAnalysisVO.setGroupCnNameList(groupCnNameList);
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setIsContainComb(false);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setCostType("P");
        String groupCnName = "重量级团队LV1";
        monthAnalysisVO.setCaliberFlag("R");
        monthAnalysisVO.setGranularityType("U");
        IRequestContext current = RequestContextManager.getCurrent();
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        XSSFSheet sheetAt = workbook.getSheetAt(0);
        sheetAt.createRow(7).createCell(4);
        sheetAt.getRow(3).createCell(4);
        sheetAt.getRow(4).createCell(4);
        sheetAt.getRow(5).createCell(4);
        sheetAt.getRow(6).createCell(4);
        List<DmFocMonthCostIdxVO> priceIndexVOList = new ArrayList<>();
        DmFocMonthCostIdxVO dmFocMonthCostIdxVO = new DmFocMonthCostIdxVO();
        dmFocMonthCostIdxVO.setCostIndex(12d);
        dmFocMonthCostIdxVO.setPeriodId(202201L);
        dmFocMonthCostIdxVO.setCostType("P");
        priceIndexVOList.add(dmFocMonthCostIdxVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        resultDataVO.setData(priceIndexVOList);
        PowerMockito.doReturn(resultDataVO).when(monthCommonService).getCompareIndexChart(any());
        List<LookupItemVO> universalItemList = new ArrayList<>();
        LookupItemVO lookupItemVO = new LookupItemVO();
        lookupItemVO.setItemCode("1");
        lookupItemVO.setItemName("ICT-专项采购认证部-品类");
        universalItemList.add(lookupItemVO);
        PowerMockito.doReturn(universalItemList).when(lookupItemQueryService).findItemListByClassify(Constant.StrEnum.PURCHASE_LOOKUP_UNIVERSAL_VIEW.getValue());
        ResultDataVO resultDataVO1 = new ResultDataVO();
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        resultDataVO1.setData(bgInfoList);
        PowerMockito.doReturn(resultDataVO1).when(annualCommonService).getBgInfoList(any());
        String actualMonth ="202305";
        PowerMockito.doReturn(actualMonth).when(dmFocMonthCostIdxDao).findActualMonth(any());
        Assertions.assertNotNull(asyncExportService.fillPriceIdxSheet(workbook,0,groupCnName,monthAnalysisVO,current));
    }
    @Test
    public void addCombMutilPriceIndexChartListTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIsContainComb(false);
        List<String> strings = new ArrayList<>();
        strings.add("test");
        monthAnalysisVO.setParentCodeList(strings);
        monthAnalysisVO.setCombinaCodeList(strings);
        List<DmFocMonthCostIdxVO> priceIndexChartList = new ArrayList<>();
        List<DmFocMonthCostIdxVO> priceIndexCombChartByMultiDim = new ArrayList<>();
        DmFocMonthCostIdxVO dmFocMonthCostIdxVO = new DmFocMonthCostIdxVO();
        dmFocMonthCostIdxVO.setCostIndex(12d);
        dmFocMonthCostIdxVO.setPeriodId(202201L);
        dmFocMonthCostIdxVO.setCostType("P");
        dmFocMonthCostIdxVO.setCustomCnName("test");
        priceIndexCombChartByMultiDim.add(dmFocMonthCostIdxVO);

        Mockito.when(dmFocMonthCostIdxDao.findPriceIndexNormalChartByMultiDim(any())).thenReturn(priceIndexChartList);
        Mockito.when(dmFocMonthCostIdxDao.findPriceIndexCombChartByMultiDim(any())).thenReturn(priceIndexCombChartByMultiDim);
        Whitebox.invokeMethod(asyncExportService, "addCombMutilPriceIndexChartList", monthAnalysisVO,
                priceIndexChartList);
        assertThatNoException();
    }

    @Test
    public void addCombMutilPriceIndexChartList2Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setCostType("M");
        monthAnalysisVO.setIsContainComb(false);
        List<String> strings = new ArrayList<>();
        strings.add("test");
        monthAnalysisVO.setParentCodeList(strings);
        monthAnalysisVO.setCombinaCodeList(strings);
        List<DmFocMonthCostIdxVO> priceIndexChartList = new ArrayList<>();
        List<DmFocMonthCostIdxVO> priceIndexCombChartByMultiDim = new ArrayList<>();
        DmFocMonthCostIdxVO dmFocMonthCostIdxVO = new DmFocMonthCostIdxVO();
        dmFocMonthCostIdxVO.setCostIndex(12d);
        dmFocMonthCostIdxVO.setPeriodId(202201L);
        dmFocMonthCostIdxVO.setCostType("P");
        dmFocMonthCostIdxVO.setCustomCnName("test1");
        dmFocMonthCostIdxVO.setParentCnName("test");
        priceIndexCombChartByMultiDim.add(dmFocMonthCostIdxVO);

        Mockito.when(dmFocMonthCostIdxDao.findPriceIndexNormalChartByMultiDim(any())).thenReturn(priceIndexChartList);
        Mockito.when(dmFocMadeMonthCostIdxDao.findMadePriceIndexCombByMultiDim(any())).thenReturn(priceIndexCombChartByMultiDim);
        Whitebox.invokeMethod(asyncExportService, "addCombMutilPriceIndexChartList", monthAnalysisVO,
                priceIndexChartList);
        assertThatNoException();
    }

    @Test
    public void getDmFocMonthWeightListTest() throws Exception {
        MonthAnalysisVO paramsVO = new MonthAnalysisVO();
        paramsVO.setCostType("P");
        paramsVO.setIsMultipleSelect(true);
        paramsVO.setIsContainComb(true);
        List<String> strings = new ArrayList<>();
        strings.add("test");
        strings.add("_##null");
        strings.add("_##test");
        paramsVO.setCombinaCodeList(strings);
        paramsVO.setParentCodeList(strings);
        paramsVO.setGroupLevel(GroupLevelEnumU.ITEM.getValue());

        List<DmFocMonthWeightVO> weightList = new ArrayList<>();
        DmFocMonthWeightVO dmFocMonthWeightVO = new DmFocMonthWeightVO();
        dmFocMonthWeightVO.setCostType("P");
        dmFocMonthWeightVO.setCustomCnName("test");
        dmFocMonthWeightVO.setParentCnName("test1");
        dmFocMonthWeightVO.setWeightRate(0.1);
        DmFocMonthWeightVO dmFocMonthWeightVO1 = new DmFocMonthWeightVO();
        dmFocMonthWeightVO1.setCostType("P");
        dmFocMonthWeightVO1.setCustomCnName("test");
        dmFocMonthWeightVO1.setWeightRate(0.1);
        weightList.add(dmFocMonthWeightVO);
        weightList.add(dmFocMonthWeightVO1);

        List<DmFocMonthWeightVO> result = Whitebox.invokeMethod(asyncExportService, "getDmFocMonthWeightList", paramsVO, weightList);
        Assert.assertNotNull(result);
    }

    @Test
    public void getDmFocMonthWeightList1Test() throws Exception {
        MonthAnalysisVO paramsVO = new MonthAnalysisVO();
        paramsVO.setCostType("M");
        paramsVO.setIsMultipleSelect(true);
        paramsVO.setIsContainComb(true);
        List<String> strings = new ArrayList<>();
        strings.add("test");
        paramsVO.setCombinaCodeList(strings);
        paramsVO.setParentCodeList(strings);
        paramsVO.setGroupLevel(GroupLevelEnumU.ITEM.getValue());
        List<DmFocMonthWeightVO> weightList = new ArrayList<>();

        List<DmFocMonthWeightVO> result = Whitebox.invokeMethod(asyncExportService, "getDmFocMonthWeightList", paramsVO, weightList);
        Assert.assertNotNull(result);
    }

    @Test
    public void getDmFocMonthWeightList2Test() throws Exception {
        MonthAnalysisVO paramsVO = new MonthAnalysisVO();
        paramsVO.setCostType("P");
        paramsVO.setIsMultipleSelect(true);
        paramsVO.setIsContainComb(false);
        paramsVO.setGroupLevel(GroupLevelEnumU.ITEM.getValue());

        List<DmFocMonthWeightVO> weightList = new ArrayList<>();
        DmFocMonthWeightVO dmFocMonthWeightVO = new DmFocMonthWeightVO();
        dmFocMonthWeightVO.setCostType("P");
        dmFocMonthWeightVO.setCustomCnName("test");
        dmFocMonthWeightVO.setParentCnName("test1");
        dmFocMonthWeightVO.setWeightRate(0.1);
        weightList.add(dmFocMonthWeightVO);

        Mockito.when(dmFocMonthWeightDao.findWeightList(any())).thenReturn(weightList);
        List<DmFocMonthWeightVO> result = Whitebox.invokeMethod(asyncExportService, "getDmFocMonthWeightList", paramsVO, weightList);
        Assert.assertNotNull(result);
    }

    @Test
    public void getDmFocMonthWeightList3Test() throws Exception {
        MonthAnalysisVO paramsVO = new MonthAnalysisVO();
        paramsVO.setCostType("M");
        paramsVO.setIsMultipleSelect(true);
        paramsVO.setIsContainComb(false);
        paramsVO.setGroupLevel(GroupLevelEnumU.ITEM.getValue());

        List<DmFocMonthWeightVO> weightList = new ArrayList<>();
        DmFocMonthWeightVO dmFocMonthWeightVO = new DmFocMonthWeightVO();
        dmFocMonthWeightVO.setCostType("P");
        dmFocMonthWeightVO.setCustomCnName("test");
        dmFocMonthWeightVO.setParentCnName("test1");
        dmFocMonthWeightVO.setWeightRate(0.1);
        weightList.add(dmFocMonthWeightVO);

        Mockito.when(dmFocMadeMonthWeightDao.findMadeWeightList(any())).thenReturn(weightList);
        List<DmFocMonthWeightVO> result1 = Whitebox.invokeMethod(asyncExportService, "getDmFocMonthWeightList", paramsVO, weightList);
        Assert.assertNotNull(result1);
    }

    @Test
    public void getDmFocMonthWeightList4Test() throws Exception {
        MonthAnalysisVO paramsVO = new MonthAnalysisVO();
        paramsVO.setCostType("T");
        paramsVO.setIsMultipleSelect(true);
        paramsVO.setIsContainComb(false);
        paramsVO.setGroupLevel(GroupLevelEnumU.ITEM.getValue());

        List<DmFocMonthWeightVO> weightList1 = new ArrayList<>();
        List<DmFocMonthWeightVO> weightList = new ArrayList<>();
        DmFocMonthWeightVO dmFocMonthWeightVO = new DmFocMonthWeightVO();
        dmFocMonthWeightVO.setCostType("P");
        dmFocMonthWeightVO.setCustomCnName("test");
        dmFocMonthWeightVO.setParentCnName("test1");
        dmFocMonthWeightVO.setWeightRate(0.1);
        weightList.add(dmFocMonthWeightVO);

        paramsVO.setCostType("T");
        Mockito.when(dmFocMonthWeightDao.findWeightList(any())).thenReturn(weightList);
        List<DmFocMonthWeightVO> result2 = Whitebox.invokeMethod(asyncExportService, "getDmFocMonthWeightList", paramsVO, weightList1);
        Assert.assertNotNull(result2);
    }

    @Test
    public void fillMonthYoySheet() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIsContainComb(false);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        MonthAnalysisVO yoyParamsVO = ObjectCopyUtil.copy(monthAnalysisVO, MonthAnalysisVO.class);
        String groupCnName = "重量级团队LV1";
        monthAnalysisVO.setCaliberFlag("R");
        monthAnalysisVO.setGranularityType("U");
        IRequestContext current = RequestContextManager.getCurrent();
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        focViewInfoVO.setCostType("P");
        bgInfoList.add(focViewInfoVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        resultDataVO.setData(bgInfoList);
        PowerMockito.doReturn(resultDataVO).when(annualCommonService).getBgInfoList(any());
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        XSSFSheet sheetAt = workbook.getSheetAt(2);
        sheetAt.setRowBreak(7);
        sheetAt.createRow(7);
        XSSFRow row = sheetAt.getRow(7);
        row.createCell(5);
        List<Map> monthYoyListForExp = new ArrayList<>();
        Map map = new HashMap();
        map.put("groupCnName","12324");
        map.put("periodId",202201);
        map.put("YOY","1.1%");
        map.put("POP","1.2%");
        map.put("costType", "P");
        monthYoyListForExp.add(map);
        PowerMockito.doReturn(monthYoyListForExp).when(monthCommonService).getMonthYoyListForExp(any());
        Assertions.assertNotNull(asyncExportService.fillMonthYoySheet(workbook,2,groupCnName,monthAnalysisVO,current));
    }

    @Test
    public void fillMonthYoySheet1test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setIsContainComb(false);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        monthAnalysisVO.setCostType("T");
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        MonthAnalysisVO yoyParamsVO = ObjectCopyUtil.copy(monthAnalysisVO, MonthAnalysisVO.class);
        String groupCnName = "重量级团队LV1";
        monthAnalysisVO.setCaliberFlag("R");
        monthAnalysisVO.setGranularityType("U");
        IRequestContext current = RequestContextManager.getCurrent();
        List<LookupItemVO> universalItemList = new ArrayList<>();
        LookupItemVO lookupItemVO = new LookupItemVO();
        lookupItemVO.setItemCode("1");
        lookupItemVO.setItemName("ICT-专项采购认证部-品类");
        universalItemList.add(lookupItemVO);
        PowerMockito.doReturn(universalItemList).when(lookupItemQueryService).findItemListByClassify(Constant.StrEnum.PURCHASE_LOOKUP_UNIVERSAL_VIEW.getValue());
        String actualMonth ="202305";
        PowerMockito.doReturn(actualMonth).when(dmFocMonthCostIdxDao).findActualMonth(any());
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        resultDataVO.setData(bgInfoList);
        PowerMockito.doReturn(resultDataVO).when(annualCommonService).getBgInfoList(any());
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate1UT.xlsx");
        XSSFSheet sheetAt = workbook.getSheetAt(2);
        sheetAt.setRowBreak(7);
        sheetAt.createRow(7);
        XSSFRow row = sheetAt.getRow(7);
        sheetAt.getRow(3).createCell(5);
        sheetAt.getRow(4).createCell(5);
        sheetAt.getRow(5).createCell(5);
        sheetAt.getRow(6).createCell(5);
        row.createCell(5);
        List<Map> monthYoyListForExp = new ArrayList<>();
        Map map = new HashMap();
        map.put("periodId",202201);
        map.put("parentCnName","121424");
        map.put("YOY","1.1%");
        map.put("POP","1.2%");
        monthYoyListForExp.add(map);
        PowerMockito.doReturn(monthYoyListForExp).when(monthCommonService).getMonthYoyListForExp(yoyParamsVO);
        Assertions.assertNotNull(asyncExportService.fillMonthYoySheet(workbook,2,groupCnName,monthAnalysisVO,current));
    }

    @Test
    public void fillMonthYoySheet2test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setIsContainComb(false);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        monthAnalysisVO.setCostType("M");
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        MonthAnalysisVO yoyParamsVO = ObjectCopyUtil.copy(monthAnalysisVO, MonthAnalysisVO.class);
        String groupCnName = "重量级团队LV1";
        monthAnalysisVO.setCaliberFlag("R");
        monthAnalysisVO.setGranularityType("U");
        IRequestContext current = RequestContextManager.getCurrent();
        List<LookupItemVO> universalItemList = new ArrayList<>();
        LookupItemVO lookupItemVO = new LookupItemVO();
        lookupItemVO.setItemCode("1");
        lookupItemVO.setItemName("ICT-专项采购认证部-品类");
        universalItemList.add(lookupItemVO);
        PowerMockito.doReturn(universalItemList).when(lookupItemQueryService).findItemListByClassify(Constant.StrEnum.PURCHASE_LOOKUP_UNIVERSAL_VIEW.getValue());
        String actualMonth ="202305";
        PowerMockito.doReturn(actualMonth).when(dmFocMonthCostIdxDao).findActualMonth(any());
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        resultDataVO.setData(bgInfoList);
        PowerMockito.doReturn(resultDataVO).when(annualCommonService).getBgInfoList(any());
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate1UT.xlsx");
        XSSFSheet sheetAt = workbook.getSheetAt(2);
        sheetAt.createRow(7);
        XSSFRow row = sheetAt.getRow(7);
        sheetAt.getRow(3).createCell(5);
        sheetAt.getRow(4).createCell(5);
        sheetAt.getRow(5).createCell(5);
        sheetAt.getRow(6).createCell(5);
        row.createCell(5);
        List<Map> monthYoyListForExp = new ArrayList<>();
        Map map = new HashMap();
        map.put("periodId",202201);
        map.put("parentCnName","121424");
        map.put("YOY","1.1%");
        map.put("POP","1.2%");
        monthYoyListForExp.add(map);
        PowerMockito.doReturn(monthYoyListForExp).when(monthCommonService).getMonthYoyListForExp(yoyParamsVO);
        Assertions.assertNotNull(asyncExportService.fillMonthYoySheet(workbook,2,groupCnName,monthAnalysisVO,current));
    }

    @Test
    public void fillMultiIndexTemplate1Sheet() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setReverseViewFlag(true);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("P");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        String groupCnName = "重量级团队LV1";
        monthAnalysisVO.setCaliberFlag("R");
        monthAnalysisVO.setGranularityType("U");
        IRequestContext current = RequestContextManager.getCurrent();
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        XSSFSheet sheetAt = workbook.getSheetAt(2);
        sheetAt.createRow(7);
        sheetAt.getRow(7).createCell(5);
        List<DmFocMonthCostIdxVO> priceIndexExpData = new ArrayList<>();
        DmFocMonthCostIdxVO monthCostIdxVO = new DmFocMonthCostIdxVO();
        monthCostIdxVO.setPeriodId(202201L);
        monthCostIdxVO.setGroupCnName("计算机");
        monthCostIdxVO.setCostIndex(2.2d);
        monthCostIdxVO.setParentCnName("112324");
        monthCostIdxVO.setCostType("P");
        priceIndexExpData.add(monthCostIdxVO);
        PowerMockito.doReturn(priceIndexExpData).when(dmFocMonthCostIdxDao).findPriceIndexExpData(any());
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        resultDataVO.setData(bgInfoList);
        PowerMockito.doReturn(resultDataVO).when(annualCommonService).getBgInfoList(any());
        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","L2");
        map.put("nextGroupName","name1");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);
        Assertions.assertNotNull(asyncExportService.fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook,2,groupCnName,current));
    }

    @Test
    public void fillMultiIndexTemplate1Sheet2T() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setReverseViewFlag(true);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("M");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        String groupCnName = "重量级团队LV1";
        monthAnalysisVO.setCaliberFlag("R");
        monthAnalysisVO.setGranularityType("U");
        IRequestContext current = RequestContextManager.getCurrent();
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        XSSFSheet sheetAt = workbook.getSheetAt(2);
        sheetAt.createRow(7);
        sheetAt.getRow(7).createCell(5);
        List<DmFocMonthCostIdxVO> priceIndexExpData = new ArrayList<>();
        DmFocMonthCostIdxVO monthCostIdxVO = new DmFocMonthCostIdxVO();
        monthCostIdxVO.setPeriodId(202201L);
        monthCostIdxVO.setGroupCnName("计算机");
        monthCostIdxVO.setCostIndex(2.2d);
        monthCostIdxVO.setParentCnName("112324");
        monthCostIdxVO.setCostType("M");
        priceIndexExpData.add(monthCostIdxVO);
        PowerMockito.doReturn(priceIndexExpData).when(dmFocMonthCostIdxDao).findPriceIndexExpData(any());
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        resultDataVO.setData(bgInfoList);
        PowerMockito.doReturn(resultDataVO).when(annualCommonService).getBgInfoList(any());
        mockMadeNextGroupLevel();
        Assertions.assertNotNull(asyncExportService.fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook,2,groupCnName,current));
    }

    @Test
    public void fillMultiIndexTemplate1Sheet3T() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setReverseViewFlag(true);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("T");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        String groupCnName = "重量级团队LV1";
        monthAnalysisVO.setCaliberFlag("R");
        monthAnalysisVO.setGranularityType("U");
        IRequestContext current = RequestContextManager.getCurrent();
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        XSSFSheet sheetAt = workbook.getSheetAt(2);
        sheetAt.createRow(7);
        sheetAt.getRow(7).createCell(5);
        List<DmFocMonthCostIdxVO> priceIndexExpData = new ArrayList<>();
        DmFocMonthCostIdxVO monthCostIdxVO = new DmFocMonthCostIdxVO();
        monthCostIdxVO.setPeriodId(202201L);
        monthCostIdxVO.setGroupCnName("计算机");
        monthCostIdxVO.setCostIndex(2.2d);
        monthCostIdxVO.setParentCnName("112324");
        monthCostIdxVO.setCostType("T");
        priceIndexExpData.add(monthCostIdxVO);
        PowerMockito.doReturn(priceIndexExpData).when(dmFocMonthCostIdxDao).findPriceIndexExpData(any());
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        resultDataVO.setData(bgInfoList);
        PowerMockito.doReturn(resultDataVO).when(annualCommonService).getBgInfoList(any());
        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","L2");
        map.put("nextGroupName","name1");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);
        Assertions.assertNotNull(asyncExportService.fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook,2,groupCnName,current));
    }

    @Test
    public void fillMultiIndexTemplate1Sheet1test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(true);
        monthAnalysisVO.setReverseViewFlag(true);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("P");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        String groupCnName = "重量级团队LV1";
        monthAnalysisVO.setCaliberFlag("R");
        monthAnalysisVO.setGranularityType("U");
        IRequestContext current = RequestContextManager.getCurrent();
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate1UT.xlsx");
        XSSFSheet sheetAt = workbook.getSheetAt(2);
        sheetAt.createRow(7);
        XSSFRow row = sheetAt.getRow(7);
        sheetAt.getRow(3).createCell(5);
        sheetAt.getRow(4).createCell(5);
        sheetAt.getRow(5).createCell(5);
        sheetAt.getRow(6).createCell(5);
        row.createCell(5);
        List<DmFocMonthCostIdxVO> priceIndexExpData = new ArrayList<>();
        DmFocMonthCostIdxVO monthCostIdxVO = new DmFocMonthCostIdxVO();
        monthCostIdxVO.setPeriodId(202201L);
        monthCostIdxVO.setGroupCnName("计算机");
        monthCostIdxVO.setCostIndex(2.2d);
        monthCostIdxVO.setParentCnName("112324");
        monthCostIdxVO.setCostType("P");
        priceIndexExpData.add(monthCostIdxVO);
        PowerMockito.doReturn(priceIndexExpData).when(dmFocMonthCostIdxDao).findPriceIndexExpData(any());
        PowerMockito.doReturn(priceIndexExpData).when(dmFocRecMonthCostIdxDao).findRevPriceIndexExpData(any());
        List<LookupItemVO> universalItemList = new ArrayList<>();
        LookupItemVO lookupItemVO = new LookupItemVO();
        lookupItemVO.setItemCode("1");
        lookupItemVO.setItemName("ICT-专项采购认证部-品类");
        universalItemList.add(lookupItemVO);
        PowerMockito.doReturn(universalItemList).when(lookupItemQueryService).findItemListByClassify(Constant.StrEnum.PURCHASE_LOOKUP_UNIVERSAL_VIEW.getValue());
        String actualMonth ="202305";
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        resultDataVO.setData(bgInfoList);
        mockNextGroupLevel();
        PowerMockito.doReturn(resultDataVO).when(annualCommonService).getBgInfoList(any());
        PowerMockito.doReturn(actualMonth).when(dmFocMonthCostIdxDao).findActualMonth(any());
        Assertions.assertNotNull(asyncExportService.fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook,2,groupCnName,current));
        mockNextGroupLevel();
        groupCnName = "重量级团队LV1-dfdsafdsa";
        monthAnalysisVO.setViewFlag("5");
        Assertions.assertNotNull(asyncExportService.fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook,2,groupCnName,current));

        monthCostIdxVO.setParentCnName(null);
        monthAnalysisVO.setGroupLevel("LV1");
        monthAnalysisVO.setViewFlag("4");
        Assertions.assertNotNull(asyncExportService.fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook,2,groupCnName,current));

        monthAnalysisVO.setReverseViewFlag(false);
        monthAnalysisVO.setViewFlag("3");
        Assertions.assertNotNull(asyncExportService.fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook,2,groupCnName,current));
    }

    @Test
    public void fillMultiIndexTemplate2Sheet() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("P");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setCaliberFlag("C");
        monthAnalysisVO.setGranularityType("P");
        String groupCnName = "重量级团队LV1";
        IRequestContext current = RequestContextManager.getCurrent();
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        resultDataVO.setData(bgInfoList);
        PowerMockito.doReturn(resultDataVO).when(annualCommonService).getBgInfoList(any());
        String actualMonth ="202305";
        PowerMockito.doReturn(actualMonth).when(dmFocMonthCostIdxDao).findActualMonth(any());
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        XSSFSheet sheetAt = workbook.getSheetAt(2);
        sheetAt.createRow(7);
        sheetAt.getRow(7).createCell(6);
        sheetAt.getRow(5).createCell(6);
        sheetAt.getRow(6).createCell(6);
        mockNextGroupLevel();
        Assertions.assertNotNull(asyncExportService.fillMultiIndexTemplate2Sheet(monthAnalysisVO,workbook,2,groupCnName,current));
    }

    @Test
    public void fillMultiIndexTemplate2Sheet2T() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("M");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setCaliberFlag("C");
        monthAnalysisVO.setGranularityType("P");
        String groupCnName = "重量级团队LV1";
        IRequestContext current = RequestContextManager.getCurrent();
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        resultDataVO.setData(bgInfoList);
        PowerMockito.doReturn(resultDataVO).when(annualCommonService).getBgInfoList(any());
        String actualMonth ="202305";
        PowerMockito.doReturn(actualMonth).when(dmFocMonthCostIdxDao).findActualMonth(any());
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        XSSFSheet sheetAt = workbook.getSheetAt(2);
        sheetAt.createRow(7);
        sheetAt.getRow(7).createCell(6);
        sheetAt.getRow(5).createCell(6);
        sheetAt.getRow(6).createCell(6);
        mockMadeNextGroupLevel();
        Assertions.assertNotNull(asyncExportService.fillMultiIndexTemplate2Sheet(monthAnalysisVO,workbook,2,groupCnName,current));
    }

    @Test
    public void fillMultiIndexTemplate2Sheet1test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(true);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("T");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        String groupCnName = "重量级团队LV1";
        monthAnalysisVO.setCaliberFlag("C");
        monthAnalysisVO.setGranularityType("P");
        IRequestContext current = RequestContextManager.getCurrent();
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        XSSFSheet sheetAt = workbook.getSheetAt(2);
        sheetAt.createRow(7);
        sheetAt.getRow(7).createCell(6);
        sheetAt.getRow(5).createCell(6);
        sheetAt.getRow(6).createCell(6);
        List<LookupItemVO> universalItemList = new ArrayList<>();
        LookupItemVO lookupItemVO = new LookupItemVO();
        lookupItemVO.setItemCode("1");
        lookupItemVO.setItemName("ICT-专项采购认证部-品类");
        universalItemList.add(lookupItemVO);
        PowerMockito.doReturn(universalItemList).when(lookupItemQueryService).findItemListByClassify(Constant.StrEnum.PURCHASE_LOOKUP_UNIVERSAL_VIEW.getValue());
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        resultDataVO.setData(bgInfoList);
        PowerMockito.doReturn(resultDataVO).when(annualCommonService).getBgInfoList(any());
        String actualMonth ="202305";
        PowerMockito.doReturn(actualMonth).when(dmFocMonthCostIdxDao).findActualMonth(any());
        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","L2");
        map.put("nextGroupName","name1");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);
        Assertions.assertNotNull(asyncExportService.fillMultiIndexTemplate2Sheet(monthAnalysisVO,workbook,2,groupCnName,current));

        monthAnalysisVO.setIsContainComb(true);
        Assertions.assertNotNull(asyncExportService.fillMultiIndexTemplate2Sheet(monthAnalysisVO,workbook,2,groupCnName,current));
        monthAnalysisVO.setCustomIdList(Arrays.asList(1L,2L,3L));
        Assertions.assertNotNull(asyncExportService.fillMultiIndexTemplate2Sheet(monthAnalysisVO,workbook,2,groupCnName,current));
        monthAnalysisVO.setCombinaCodeList(Arrays.asList("1L","2L","3L"));
        List<DmFocMonthCostIdxVO> priceIndexExpData = new ArrayList<>();
        DmFocMonthCostIdxVO monthCostIdxVO = new DmFocMonthCostIdxVO();
        monthCostIdxVO.setPeriodId(202201L);
        monthCostIdxVO.setGroupCnName("test");
        monthCostIdxVO.setCostIndex(2.2d);
        monthCostIdxVO.setParentCnName("test");
        priceIndexExpData.add(monthCostIdxVO);
        when(dmFocMonthCostIdxDao.findPriceIndexCombChartByMultiDim(any(MonthAnalysisVO.class))).thenReturn(priceIndexExpData);
        Assertions.assertNotNull(asyncExportService.fillMultiIndexTemplate2Sheet(monthAnalysisVO,workbook,2,groupCnName,current));
        monthCostIdxVO.setParentCnName("test1");
        Assertions.assertNotNull(asyncExportService.fillMultiIndexTemplate2Sheet(monthAnalysisVO,workbook,2,groupCnName,current));
        monthCostIdxVO.setParentCnName(null);
        Assertions.assertNotNull(asyncExportService.fillMultiIndexTemplate2Sheet(monthAnalysisVO,workbook,2,groupCnName,current));
    }

    @Test
    public void distinguishIfCombineTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setParentCodeList(Arrays.asList("dddd", "ddd_##null", "ddd_##test"));
        Whitebox.invokeMethod(asyncExportService, "distinguishIfCombine", monthAnalysisVO);
        assertThatNoException();
    }

    @Test
    public void fillWeightTemplate1Sheet() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("P");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        String groupCnName = "重量级团队LV1";
        monthAnalysisVO.setCaliberFlag("R");
        monthAnalysisVO.setGranularityType("U");
        IRequestContext current = RequestContextManager.getCurrent();
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate1UT.xlsx");
        XSSFSheet sheetAt = workbook.getSheetAt(2);
        sheetAt.createRow(7);
        sheetAt.getRow(7).createCell(4);
        List<DmFocMonthWeightVO> weightList = new ArrayList<>();
        DmFocMonthWeightVO monthWeightVO = new DmFocMonthWeightVO();
        monthWeightVO.setGroupCnName("计算机");
        monthWeightVO.setWeightPercent("100%");
        monthWeightVO.setCostType("P");
        weightList.add(monthWeightVO);
        PowerMockito.doReturn(weightList).when(dmFocMonthWeightDao).findWeightList(any());
        mockStatic(FcstIndexUtil.class);
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        resultDataVO.setData(bgInfoList);
        PowerMockito.doReturn(resultDataVO).when(annualCommonService).getBgInfoList(any());
        Map map = new HashMap();
        map.put("nextGroupLevel","L2");
        map.put("nextGroupName","name1");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);
        Assertions.assertNotNull(asyncExportService.fillWeightTemplate1Sheet(monthAnalysisVO,workbook,2,groupCnName,current));
    }

    @Test
    public void fillWeightTemplate1Sheet1test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(true);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("M");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        String groupCnName = "重量级团队LV1";
        monthAnalysisVO.setCaliberFlag("R");
        monthAnalysisVO.setGranularityType("U");
        IRequestContext current = RequestContextManager.getCurrent();
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate1UT.xlsx");
        XSSFSheet sheetAt = workbook.getSheetAt(2);
        sheetAt.createRow(7);
        sheetAt.getRow(7).createCell(4);
        List<DmFocMonthWeightVO> weightList = new ArrayList<>();
        DmFocMonthWeightVO monthWeightVO = new DmFocMonthWeightVO();
        monthWeightVO.setGroupCnName("计算机");
        monthWeightVO.setWeightPercent("100%");
        monthWeightVO.setCostType("M");
        weightList.add(monthWeightVO);
        mockMadeNextGroupLevel();
        PowerMockito.doReturn(weightList).when(dmFocMonthWeightDao).findWeightList(any());
        List<LookupItemVO> universalItemList = new ArrayList<>();
        LookupItemVO lookupItemVO = new LookupItemVO();
        lookupItemVO.setItemCode("1");
        lookupItemVO.setItemName("ICT-专项采购认证部-品类");
        universalItemList.add(lookupItemVO);
        PowerMockito.doReturn(universalItemList).when(lookupItemQueryService).findItemListByClassify(Constant.StrEnum.PURCHASE_LOOKUP_UNIVERSAL_VIEW.getValue());
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        resultDataVO.setData(bgInfoList);
        PowerMockito.doReturn(resultDataVO).when(annualCommonService).getBgInfoList(any());
        String actualMonth ="202305";
        PowerMockito.doReturn(actualMonth).when(dmFocMonthCostIdxDao).findActualMonth(any());
        Assertions.assertNotNull(asyncExportService.fillWeightTemplate1Sheet(monthAnalysisVO,workbook,2,groupCnName,current));
    }

    private void mockNextGroupLevel() throws Exception {
        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","LV1");
        map.put("nextGroupName","LV1重量级团队");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);
    }

    private void mockMadeNextGroupLevel() throws Exception {
        mockStatic(FcstIndexMadeUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","LV1");
        map.put("nextGroupName","LV1重量级团队");
        when(FcstIndexMadeUtil.class, "getNextGroupLevel", any()).thenReturn(map);
    }

    @Test
    public void fillWeightTemplate1Sheet2test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(true);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("T");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        String groupCnName = "重量级团队LV1";
        monthAnalysisVO.setCaliberFlag("R");
        monthAnalysisVO.setGranularityType("U");
        IRequestContext current = RequestContextManager.getCurrent();
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate1UT.xlsx");
        XSSFSheet sheetAt = workbook.getSheetAt(2);
        sheetAt.createRow(7);
        sheetAt.getRow(7).createCell(4);
        List<DmFocMonthWeightVO> weightList = new ArrayList<>();
        DmFocMonthWeightVO monthWeightVO = new DmFocMonthWeightVO();
        monthWeightVO.setGroupCnName("计算机");
        monthWeightVO.setWeightPercent("100%");
        monthWeightVO.setCostType("T");
        weightList.add(monthWeightVO);
        PowerMockito.doReturn(weightList).when(dmFocMonthWeightDao).findWeightList(any());
        List<LookupItemVO> universalItemList = new ArrayList<>();
        LookupItemVO lookupItemVO = new LookupItemVO();
        lookupItemVO.setItemCode("1");
        lookupItemVO.setItemName("ICT-专项采购认证部-品类");
        universalItemList.add(lookupItemVO);
        PowerMockito.doReturn(universalItemList).when(lookupItemQueryService).findItemListByClassify(Constant.StrEnum.PURCHASE_LOOKUP_UNIVERSAL_VIEW.getValue());
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        resultDataVO.setData(bgInfoList);
        PowerMockito.doReturn(resultDataVO).when(annualCommonService).getBgInfoList(any());
        String actualMonth ="202305";
        mockNextGroupLevel();
        PowerMockito.doReturn(actualMonth).when(dmFocMonthCostIdxDao).findActualMonth(any());
        Assertions.assertNotNull(asyncExportService.fillWeightTemplate1Sheet(monthAnalysisVO,workbook,2,groupCnName,current));
    }

    @Test
    public void fillWeightTemplate2Sheet() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setCaliberFlag("R");
        monthAnalysisVO.setGranularityType("U");
        String groupCnName = "ITEM";
        IRequestContext current = RequestContextManager.getCurrent();
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        XSSFSheet sheetAt = workbook.getSheetAt(3);
        sheetAt.createRow(7);
        sheetAt.getRow(5).createCell(5);
        sheetAt.getRow(6).createCell(5);
        sheetAt.getRow(7).createCell(5);
        List<DmFocMonthWeightVO> weightList = new ArrayList<>();
        DmFocMonthWeightVO monthWeightVO = new DmFocMonthWeightVO();
        monthWeightVO.setGroupCnName("计算机");
        monthWeightVO.setWeightPercent("100%");
        monthWeightVO.setCostType("P");
        weightList.add(monthWeightVO);
        PowerMockito.doReturn(weightList).when(dmFocMonthWeightDao).findWeightList(any());
        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","L2");
        map.put("nextGroupName","name1");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        resultDataVO.setData(bgInfoList);
        PowerMockito.doReturn(resultDataVO).when(annualCommonService).getBgInfoList(any());
        Assertions.assertNotNull(asyncExportService.fillWeightTemplate2Sheet(monthAnalysisVO,workbook,3,groupCnName,current));
    }

    @Test
    public void fillWeightTemplate2Sheet1test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("ITEM");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("5");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setOverseaFlag("G");
        monthAnalysisVO.setLv0ProdListCode("GR");
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setIsContainComb(true);
        monthAnalysisVO.setCostType("M");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setCaliberFlag("R");
        monthAnalysisVO.setGranularityType("U");
        String groupCnName = "ITEM";
        IRequestContext current = RequestContextManager.getCurrent();
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        XSSFSheet sheetAt = workbook.getSheetAt(3);
        sheetAt.createRow(7);
        sheetAt.getRow(5).createCell(5);
        sheetAt.getRow(6).createCell(5);
        sheetAt.getRow(7).createCell(5);
        List<DmFocMonthWeightVO> weightList = new ArrayList<>();
        DmFocMonthWeightVO monthWeightVO = new DmFocMonthWeightVO();
        monthWeightVO.setGroupCnName("计算机");
        monthWeightVO.setWeightPercent("100%");
        monthWeightVO.setCostType("M");
        weightList.add(monthWeightVO);
        PowerMockito.doReturn(weightList).when(dmFocMonthWeightDao).findWeightList(any());
        List<LookupItemVO> universalItemList = new ArrayList<>();
        LookupItemVO lookupItemVO = new LookupItemVO();
        lookupItemVO.setItemCode("1");
        lookupItemVO.setItemName("ICT-专项采购认证部-品类");
        universalItemList.add(lookupItemVO);
        PowerMockito.doReturn(universalItemList).when(lookupItemQueryService).findItemListByClassify(Constant.StrEnum.PURCHASE_LOOKUP_UNIVERSAL_VIEW.getValue());
        List<DmFocViewInfoVO> bgInfoList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setLv0ProdListCnName("集团");
        bgInfoList.add(focViewInfoVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        resultDataVO.setData(bgInfoList);
        PowerMockito.doReturn(resultDataVO).when(annualCommonService).getBgInfoList(any());
        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","ITEM");
        map.put("nextGroupName","name1");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);
        Future<Integer> future = null;
        try {
            future=asyncExportService.fillWeightTemplate2Sheet(monthAnalysisVO,workbook,2,groupCnName,current);
        } catch (Exception e) {
        }
        Assertions.assertNull(future);
    }

    @Test
    public void refreshIndustryIndexDataTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setReverseViewFlag(false);
        VarifyTaskVO varifyTaskVO = new VarifyTaskVO();
        asyncExportService.refreshIndustryIndexData(monthAnalysisVO, varifyTaskVO);
        Mockito.verify(iDataCipherTextDao, Mockito.atLeastOnce()).updateVerifyTask(any(VarifyTaskVO.class));

        monthAnalysisVO.setReverseViewFlag(true);
        monthAnalysisVO.setGroupCodeList(new ArrayList<>());
        varifyTaskVO.setStatus("PROCESSING");
        varifyTaskVO.setCombStatus("PROCESSING");
        when(dmFocMonthCostIdxDao.insertPriceIndexWithFun(any(MonthAnalysisVO.class))).thenReturn("SUCCESS");
        asyncExportService.refreshIndustryIndexData(monthAnalysisVO, varifyTaskVO);
        Mockito.verify(iDataCipherTextDao, Mockito.atLeastOnce()).updateVerifyTask(any(VarifyTaskVO.class));

        varifyTaskVO.setStatus("PROCESSING");
        varifyTaskVO.setCombStatus("PROCESSING");
        monthAnalysisVO.setPurCodeList(Arrays.asList("test"));
        monthAnalysisVO.setGroupCodeList(Arrays.asList("test"));
        monthAnalysisVO.setProdRndTeamCodeList(Arrays.asList("test"));
        monthAnalysisVO.setCombinaCodeList(Arrays.asList("123_##123"));
        monthAnalysisVO.setParentCodeList(Arrays.asList("123"));
        monthAnalysisVO.setCombParentCodeList(Arrays.asList("123_##123"));
        monthAnalysisVO.setDmsCodeList(Arrays.asList("test"));
        monthAnalysisVO.setL1NameList(Arrays.asList("test"));
        monthAnalysisVO.setL2NameList(Arrays.asList("test"));
        monthAnalysisVO.setCustomIdList(Arrays.asList(1L));
        when(dmFocMonthCostIdxDao.insertPriceIndexWithFun(any(MonthAnalysisVO.class))).thenReturn("FAILED");
        asyncExportService.refreshIndustryIndexData(monthAnalysisVO, varifyTaskVO);
        Mockito.verify(iDataCipherTextDao, Mockito.atLeastOnce()).updateVerifyTask(any(VarifyTaskVO.class));
        assertThatNoException();
    }

    @Test
    public void refreshIndustryIndexData1Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setCostType("T");
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setReverseViewFlag(false);
        VarifyTaskVO varifyTaskVO = new VarifyTaskVO();
        HashMap map = new HashMap();
        map.put("nextGroupLevel",GroupLevelEnumP.L2.getValue());
        map.put("nextGroupName",GroupLevelEnumP.L2.getName());
        mockStatic(FcstIndexMadeUtil.class);
        when(FcstIndexMadeUtil.class, "getNextGroupLevel", any()).thenReturn(map);
        asyncExportService.refreshIndustryIndexData(monthAnalysisVO, varifyTaskVO);
        Assert.assertNotNull(monthAnalysisVO);
    }

    private List<DmFocActualCostVO> getHeapMapExpData() {
        List<DmFocActualCostVO> heapMapExpData = new ArrayList<>();
        DmFocActualCostVO dmFocActualCostVO = new DmFocActualCostVO();
        dmFocActualCostVO.setActualCostAmt(12d);
        dmFocActualCostVO.setPeriodId(202201L);
        dmFocActualCostVO.setGroupCode("1411");
        dmFocActualCostVO.setGroupCnName("普通端子");
        dmFocActualCostVO.setCostType("P");
        heapMapExpData.add(dmFocActualCostVO);
        return heapMapExpData;
    }
}