/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.impl.config;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.huawei.it.fcst.dao.IFcstDataRefreshStatusDao;
import com.huawei.it.fcst.enums.BgCodeEnum;
import com.huawei.it.fcst.export.IExportProcessorService;
import com.huawei.it.fcst.industry.price.dao.IDmFcstPriceVersionInfoDao;
import com.huawei.it.fcst.industry.price.dao.IPriceTopSpartInfoDao;
import com.huawei.it.fcst.industry.price.enums.GroupLevelEnum;
import com.huawei.it.fcst.industry.price.impl.template.HistorySpartListTemplateEnum;
import com.huawei.it.fcst.industry.price.service.common.IPriceCommonService;
import com.huawei.it.fcst.industry.price.service.config.IHistorySpartListService;
import com.huawei.it.fcst.industry.price.utils.FcstIndustryUtil;
import com.huawei.it.fcst.industry.price.vo.config.PriceHistorySpartSearchVO;
import com.huawei.it.fcst.industry.price.vo.version.DmFcstVersionInfoVO;
import com.huawei.it.fcst.vo.DmFcstDataRefreshStatus;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import javax.inject.Inject;
import javax.inject.Named;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * HistorySpartListService Class
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Slf4j
@Named("historySpartListService")
@JalorResource(code = "HistorySpartListService", desc = "定价指数-配置管理-历史spart清单")
public class HistorySpartListService implements IHistorySpartListService {

    @Inject
    private IDmFcstPriceVersionInfoDao versionInfoDao;

    @Inject
    private IPriceTopSpartInfoDao priceTopSpartInfoDao;

    @Inject
    private IPriceCommonService priceCommonService;

    @Inject
    private IExportProcessorService exportProcessorService;

    @Inject
    private IFcstDataRefreshStatusDao dataRefreshStatusDao;

    @Override
    @JalorOperation(code = "getVersionList", desc = "查询版本列表信息")
    public ResultDataVO getVersionList(DmFcstVersionInfoVO versionInfoVO) {
        log.info("==>Begin HistorySpartListService#getVersionList");
        return ResultDataVO.success(versionInfoDao.findVersionList(versionInfoVO));
    }

    @Override
    @JalorOperation(code = "getDropdownList", desc = "查询历史SPART清单各层级下拉框列表")
    public ResultDataVO getDropdownList(PriceHistorySpartSearchVO historySpartSearchVO) throws CommonApplicationException {
        log.info("==>Begin HistorySpartListService#getDropdownList");
        if (ObjectUtil.isNull(historySpartSearchVO.getVersionId())) {
            throw new CommonApplicationException("入参清单数据版本为空");
        }
        if (ObjectUtil.isEmpty(historySpartSearchVO.getGroupLevel())) {
            throw new CommonApplicationException("层级不能为空");
        }
        if (GroupLevelEnum.LV4.getValue().equals(historySpartSearchVO.getGroupLevel()) && ObjectUtil.isEmpty(historySpartSearchVO.getLv3ProdListCode())) {
            throw new CommonApplicationException("L3.5层级查询入参缺少L3限制");
        }
        if (GroupLevelEnum.SPART.getValue().equals(historySpartSearchVO.getGroupLevel()) && ObjectUtil.isEmpty(historySpartSearchVO.getLv4ProdListCode())) {
            throw new CommonApplicationException("spart层级查询入参缺少L3.5限制");
        }
        return ResultDataVO.success(priceTopSpartInfoDao.findDropdownList(historySpartSearchVO));
    }

    @Override
    @JalorOperation(code = "getTopSpartPageList", desc = "分页查询历史SPART清单")
    public ResultDataVO getTopSpartPageList(PriceHistorySpartSearchVO historySpartSearchVO) throws ApplicationException {
        log.info("==>Begin HistorySpartListService#getTopSpartPageList");
        DmFcstVersionInfoVO versionVO = checkInputParam(historySpartSearchVO);
        // 注：权重时间范围最多展示最近4年，如当前为2024年，则展示2021、2022、2023、2024（YTD）权重
        List<String> yearPeriodList = FcstIndustryUtil.getPeriod(versionVO.getVersion());
        setHistorySpartVO(historySpartSearchVO, yearPeriodList);
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(historySpartSearchVO.getPageSize());
        pageVO.setCurPage(historySpartSearchVO.getPageIndex());
        return ResultDataVO.success(priceTopSpartInfoDao.findTopSpartByPage(historySpartSearchVO, pageVO));
    }

    private void setHistorySpartVO(PriceHistorySpartSearchVO historySpartSearchVO, List<String> yearPeriodList) {
        historySpartSearchVO.setYearPeriodList(yearPeriodList);
        if (CollectionUtils.isNotEmpty(yearPeriodList)) {
            historySpartSearchVO.setPeriodYear0(yearPeriodList.get(0));
            historySpartSearchVO.setPeriodYear1(yearPeriodList.get(1));
            historySpartSearchVO.setPeriodYear2(yearPeriodList.get(2));
            historySpartSearchVO.setPeriodYear3(yearPeriodList.get(3));
        }
    }

    @Override
    @JalorOperation(code = "historySpartListExport", desc = "历史SPART清单导出")
    @Audit(module = "historySpartListService-historySpartListExport", operation = "historySpartListExport", message = "历史SPART清单导出")
    public ResultDataVO historySpartListExport(HttpServletResponse response, PriceHistorySpartSearchVO historySpartSearchVO) throws ApplicationException {
        log.info("==>Begin HistorySpartListService#historySpartListExport");
        DmFcstVersionInfoVO versionVO = checkInputParam(historySpartSearchVO);
        // 设置版本名称
        historySpartSearchVO.setVersionName(versionVO.getVersion());
        // 注：权重时间范围最多展示最近4年，如当前为2024年，则展示2021、2022、2023、2024（YTD）权重
        List<String> yearPeriodList = FcstIndustryUtil.getPeriod(versionVO.getVersion());
        setHistorySpartVO(historySpartSearchVO, yearPeriodList);
        // 获取导出模板
        HistorySpartListTemplateEnum historySpartListTemplateEnum = HistorySpartListTemplateEnum.CONFIG_01;
        // 设置任务刷新状态
        DmFcstDataRefreshStatus dataRefreshStatus = priceCommonService.saveDataRefreshStatus("TOP_SPART_EXPORT");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("exportFileName", "配置管理-历史SPART清单_" + System.currentTimeMillis());
        paramMap.put("exportModuleName", "价格指数-产业-ICT-SPART清单");
        paramMap.put("taskId", dataRefreshStatus.getTaskId());
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(10000);
        exportProcessorService.asyncFillEasyExcelExport(historySpartListTemplateEnum, historySpartSearchVO, paramMap, pageVO);
        return ResultDataVO.success(dataRefreshStatus);
    }

    @NotNull
    private DmFcstVersionInfoVO checkInputParam(PriceHistorySpartSearchVO historySpartSearchVO) throws CommonApplicationException {
        if (ObjectUtil.isNull(historySpartSearchVO.getVersionId())) {
            throw new CommonApplicationException("入参清单数据版本为空");
        }
        // 必填项校验
        if (ObjectUtil.isEmpty(historySpartSearchVO.getBgCode())) {
            throw new CommonApplicationException("入参BG编码为空");
        }
        // 运营商网络时，两条路径都可以选
        if (BgCodeEnum.PDCG901159.getCode().equals(historySpartSearchVO.getBgCode())) {
            boolean regionAndRepoffice = StringUtils.isAnyBlank(historySpartSearchVO.getOverseaFlag(), historySpartSearchVO.getRegionCode(), historySpartSearchVO.getRepofficeCode());
            boolean signSubAndTop = StringUtils.isAnyBlank(historySpartSearchVO.getSignSubsidiaryCustcatgCnName(), historySpartSearchVO.getSignTopCustCategoryCode());
           if (regionAndRepoffice && signSubAndTop) {
               throw new CommonApplicationException("必填参数为空");
           }
        }
        // 政企时的校验
        boolean viewOne = BgCodeEnum.PDCG901160.getCode().equals(historySpartSearchVO.getBgCode()) && StringUtils.isAnyBlank(historySpartSearchVO.getOverseaFlag(), historySpartSearchVO.getRegionCode(), historySpartSearchVO.getRepofficeCode());
        if (viewOne) {
            throw new CommonApplicationException("必填参数为空");
        }
        DmFcstVersionInfoVO versionVO = versionInfoDao.findDmFocVersionById(historySpartSearchVO.getVersionId());
        if (StrUtil.isBlank(versionVO.getVersion())) {
            throw new CommonApplicationException("入参清单数据版本名称为空");
        }
        return versionVO;
    }

    @Override
    @JalorOperation(code = "queryDataRefreshStatus", desc = "查询轮询任务状态")
    public ResultDataVO queryDataRefreshStatus(DmFcstDataRefreshStatus dataRefreshStatus) throws CommonApplicationException {
        log.info("==>Begin HistorySpartListService#queryDataRefreshStatus");
        if (null == dataRefreshStatus.getTaskId() || dataRefreshStatus.getTaskId() <= 0L) {
            throw new CommonApplicationException("该任务ID有误!");
        }
        return ResultDataVO.success(dataRefreshStatusDao.findDmFcstDataRefreshStatusById(dataRefreshStatus));
    }
}