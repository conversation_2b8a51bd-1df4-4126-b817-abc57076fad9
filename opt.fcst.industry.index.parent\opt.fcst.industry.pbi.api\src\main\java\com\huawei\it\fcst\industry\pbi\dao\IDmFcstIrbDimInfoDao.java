/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.dao;

import com.huawei.it.fcst.industry.pbi.vo.common.CommonBaseVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstDimInfoVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * IDmFocViewInfoDao
 *
 * <AUTHOR>
 * @since 2023/3/14
 */
public interface IDmFcstIrbDimInfoDao {

    List<DmFcstDimInfoVO> getBgList(CommonBaseVO commonViewVO);

    List<DmFcstDimInfoVO> getBgCodePermissionList(CommonBaseVO commonViewVO);

    Set<String> getBgCodeForSpecial(CommonBaseVO commonViewVO);

    List<DmFcstDimInfoVO> getRegionCodeList(CommonBaseVO commonViewVO);

    List<DmFcstDimInfoVO> getRepofficeCodeList(CommonBaseVO commonViewVO);

    List<DmFcstDimInfoVO> getOverseaFlagList(CommonBaseVO commonViewVO);

    List<DmFcstDimInfoVO> getMainFlagList(CommonBaseVO commonViewVO);

    List<DmFcstDimInfoVO> findCostGapSpartCodeList(CommonBaseVO commonBaseVO);

    Integer findCostGapSpartCodeNum(CommonBaseVO commonBaseVO);

    List<DmFcstDimInfoVO> baseIrbDimInfoList(@Param("searchVO")CommonBaseVO commonViewVO);

    PagedResult<DmFcstDimInfoVO> baseIrbSpartDimInfoList(@Param("searchVO") CommonBaseVO commonViewVO, @Param("pageVO") PageVO pageVO);

    List<DmFcstDimInfoVO> mainFlagDimInfoList(@Param("searchVO") CommonBaseVO commonViewVO);

    PagedResult<DmFcstDimInfoVO> mainFlagSpartDimInfoList(@Param("searchVO") CommonBaseVO commonViewVO, @Param("pageVO") PageVO pageVO);

    List<DmFcstDimInfoVO> mainFlagAnnualDimInfoList(@Param("searchVO")CommonBaseVO commonViewVO);

    PagedResult<DmFcstDimInfoVO> mainFlagAnnualSpartDimInfoList(@Param("searchVO") CommonBaseVO commonViewVO, @Param("pageVO") PageVO pageVO);

    Integer getIrbSpartOrDimensionNum(CommonBaseVO commonViewVO);

    Integer getYtdProdTeamCodeNum(CommonBaseVO commonViewVO);

    List<DmFcstDimInfoVO> getYtdProdTeamCode(CommonBaseVO commonViewVO);

    List<DmFcstDimInfoVO> getLv4CodeWithSpartOrDimension(CommonBaseVO commonViewVO);

    List<DmFcstDimInfoVO> getLv1AndLv2CodeList(CommonBaseVO commonViewVO);

    List<DmFcstDimInfoVO> reverseFindLv1Code(CommonBaseVO commonViewVO);

    List<DmFcstDimInfoVO> reverseFindLv1CodeMonth(CommonBaseVO commonViewVO);

    List<DmFcstDimInfoVO> getLv0Code(CommonBaseVO commonViewVO);

    DmFcstDimInfoVO findRefreshTime();

}
