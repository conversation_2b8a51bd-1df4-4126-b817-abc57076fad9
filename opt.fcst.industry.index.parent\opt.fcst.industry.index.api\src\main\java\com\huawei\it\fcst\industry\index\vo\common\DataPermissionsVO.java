/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

/**
 * DataPermissionsVO Class
 *
 * <AUTHOR>
 * @since 2022-11-10
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "数据权限维度VO")
public class DataPermissionsVO implements Serializable {
    private static final long serialVersionUID = 6110390781244573970L;

    private int roleId;

    private String roleName;

    @ApiModelProperty(value = "ICT产业集合")
    private Set<String> lv0DimensionSet = new HashSet<>();

    @ApiModelProperty(value = "重量级团队LV1集合")
    private Set<String> lv1DimensionSet  = new HashSet<>();

    @ApiModelProperty(value = "重量级团队LV2集合")
    private Set<String> lv2DimensionSet  = new HashSet<>();


}