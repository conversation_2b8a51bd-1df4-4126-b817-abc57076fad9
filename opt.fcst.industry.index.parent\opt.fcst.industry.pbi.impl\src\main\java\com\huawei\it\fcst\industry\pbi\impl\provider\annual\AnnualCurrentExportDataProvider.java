/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.provider.annual;

import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.industry.pbi.enums.IndustryConstEnum;
import com.huawei.it.fcst.industry.pbi.impl.annual.AnnualAmpPbiService;
import com.huawei.it.fcst.industry.pbi.impl.common.IctExecutorConfig;
import com.huawei.it.fcst.export.IExcelExportDataProvider;
import com.huawei.it.fcst.industry.pbi.vo.annual.AnnualAnalysisVO;
import com.huawei.it.fcst.industry.pbi.vo.annual.DmFocAnnualAmpVO;
import com.huawei.it.fcst.export.vo.ExcelExportContext;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import javax.inject.Inject;
import javax.inject.Named;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;

/**
 * 导出，成本涨跌图
 */
@Named("IExcelExport.AnnualExportDataProvider")
public class AnnualCurrentExportDataProvider implements IExcelExportDataProvider {

    @Inject
    private AnnualAmpPbiService annualAmpPbiService;

    @Inject
    private IctExecutorConfig ictExecutorConfig;

    private static final Logger LOGGER = LoggerFactory.getLogger(AnnualCurrentExportDataProvider.class);

    @Override
    public List<?> getData(Serializable conditionObject, PageVO pageVO) throws CommonApplicationException, InterruptedException {
        AnnualAnalysisVO annualVO = (AnnualAnalysisVO) conditionObject;
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        BeanUtils.copyProperties(annualVO, annualAnalysisVO);

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        // 对比分析
        if (annualAnalysisVO.getIsComparFlag()) {
            IRequestContext requestContext = RequestContext.getCurrent();
            List<AnnualAnalysisVO> annualParamList = annualAnalysisVO.getAnnualParamList();
            Executor executorPool = ictExecutorConfig.ictAsyncServiceExecutor();
            CountDownLatch countDownLatch = new CountDownLatch(annualParamList.size());
            for (AnnualAnalysisVO annualParamVO : annualParamList) {
                Runnable runnable = () -> {
                    RequestContextManager.setCurrent(requestContext);
                    try {
                        annualAmpPbiService.setCommonTablePreFix(annualParamVO);
                        if (CommonConstant.PROD_GROUP_LEVEL.contains(annualParamVO.getGroupLevel())) {
                            annualParamVO.setViewFlag(IndustryConstEnum.VIEW_FLAG.PROD_SPART.getValue());
                        }
                        annualAmpPbiService.getCurrentAmpList(annualParamVO, dmFocAnnualAmpVOList);
                    } catch (Exception exception) {
                        LOGGER.error("error typeCurrentAmp getData Method:{} ", exception.getLocalizedMessage());
                    } finally {
                        countDownLatch.countDown();
                        RequestContextManager.removeCurrent();
                        LOGGER.info("剩余线程个数：{},当前线程名称：{}", countDownLatch.getCount(), Thread.currentThread().getName());
                    }
                };
                executorPool.execute(runnable);
            }
            countDownLatch.await();
        } else {
            // 非对比分析查询
            annualAmpPbiService.getCurrentAmpList(annualAnalysisVO, dmFocAnnualAmpVOList);
        }
        // 设置无效的涨跌幅提示语
        annualAmpPbiService.setNoEffectiveAmp(dmFocAnnualAmpVOList, annualAnalysisVO, "excel");
        annualAmpPbiService.setGroupCnNameDimensionLevel(dmFocAnnualAmpVOList);
        return dmFocAnnualAmpVOList;
    }

    @Override
    public Map<String, Object> getHeader(Serializable conditionObject, Map<String, Object> parameters) {
        ExcelExportContext context = (ExcelExportContext) conditionObject;
        AnnualAnalysisVO annualVO = (AnnualAnalysisVO) context.getConditionObject();

        // 自定义表头
        Map<String, Object> headMap = new HashMap<>();
        headMap.put("displayName", annualVO.getDisplayName());
        headMap.put("name", annualVO.getName());
        headMap.put("costTypeCnName", annualVO.getCostTypeCnName());
        headMap.put("granularityTypeCnName", annualVO.getGranularityTypeCnName());
        headMap.put("softwareMarkCnName", annualVO.getSoftwareMarkCnName());
        headMap.put("overseaFlagCnName", annualVO.getOverseaFlagCnName());
        headMap.put("bgCnName", annualVO.getBgCnName());
        headMap.put("actualMonth", annualVO.getActualMonth());
        headMap.put("regionCnName", annualVO.getRegionCnName());
        headMap.put("repofficeCnName", annualVO.getRepofficeCnName());
        headMap.put("mainFlagCnName", annualVO.getMainFlagCnName());
        headMap.put("codeAttributesCnName", annualVO.getCodeAttributesCnName());
        headMap.put("ytdFlagCnName", annualVO.getYtdFlagCnName());
        return headMap;
    }
}
