/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.common;

import com.huawei.it.fcst.enums.CommonConstEnum;
import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstBaseCusDimDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstCodeReplInfoDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstCustomCombDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstIndusDimInfoDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstIrbDimInfoDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstProdDimInfoDao;
import com.huawei.it.fcst.industry.pbi.enums.GroupLevelEnum;
import com.huawei.it.fcst.industry.pbi.enums.IndustryConstEnum;
import com.huawei.it.fcst.industry.pbi.service.common.IDropDownService;
import com.huawei.it.fcst.industry.pbi.utils.FcstIndustryUtil;
import com.huawei.it.fcst.industry.pbi.vo.common.CommonBaseVO;
import com.huawei.it.fcst.industry.pbi.vo.common.DataPermissionsVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.BaseCusDimVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.CodeReplInfoVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstBaseCusDimVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstDimInfoVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.InterLockInfoVO;
import com.huawei.it.fcst.util.ObjectCopyUtil;
import com.huawei.it.fcst.util.UserInfoUtils;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.security.RoleVO;
import com.huawei.it.jalor5.security.UserVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Named;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/6/28
 */
@Named("dropDownService")
@JalorResource(code = "dropDownService", desc = "NEW ICT-各层级下拉框")
public class DropDownService implements IDropDownService {

    @Autowired
    private IDmFcstIrbDimInfoDao dmFcstIrbDimInfoDao;

    @Autowired
    private IDmFcstIndusDimInfoDao dmFcstIndusDimInfoDao;

    @Autowired
    private IDmFcstProdDimInfoDao dmFcstProdDimInfoDao;

    @Autowired
    private IDmFcstBaseCusDimDao dmFcstBaseCusDimDao;

    @Autowired
    private IDmFcstCodeReplInfoDao iDmFcstCodeReplInfoDao;

    @Autowired
    private DataPermissionService dataPermissionService;

    @Autowired
    private IctCommonService commonService;

    @Autowired
    private IDmFcstCustomCombDao dmFcstCustomCombDao;

    @Override
    @JalorOperation(code = "getBgInfoList", desc = "BG下拉框")
    public ResultDataVO getBgInfoList(CodeReplInfoVO commonBaseVO) throws CommonApplicationException {
        // 获取版本号
        if (null == commonBaseVO.getVersionId()) {
            setVersionId(commonBaseVO);
        }
        List<DmFcstDimInfoVO> bgList = getBgCodeFromDb(commonBaseVO);
        // 排序
        Collections.sort(bgList, new Comparator<DmFcstDimInfoVO>() {
            @Override
            public int compare(DmFcstDimInfoVO o1, DmFcstDimInfoVO o2) {
                return CommonConstant.BG_CODE_RULE.getOrDefault(o1.getBgCode(), Integer.MAX_VALUE)
                        - CommonConstant.BG_CODE_RULE.getOrDefault(o2.getBgCode(), Integer.MAX_VALUE);
            }
        });
        if (IndustryConstEnum.GRANULARITY_TYPE.PROD.getValue().equals(commonBaseVO.getGranularityType())) {
            // 销售目录屏蔽掉集团
            bgList.removeIf(model -> model.getBgCode().equals(CommonConstant.GR));
            // 获取BG数据权限
            DataPermissionsVO currentRoleDataPermission = dataPermissionService.getCurrentRoleDataPermission();
            commonBaseVO.setLv2DimensionSet(currentRoleDataPermission.getLv2DimensionSet());
            commonBaseVO.setLv3DimensionSet(currentRoleDataPermission.getLv3DimensionSet());
            Set<String> bgDimensionSet = currentRoleDataPermission.getBgDimensionSet();
            Map<String, Map<String, Set<String>>> locationMap = currentRoleDataPermission.getLocationMap();
            Map<String, Set<String>> regionSetMap = locationMap.get(commonBaseVO.getOverseaFlag());
            Set<String> bgSet = new HashSet<>();
            if (null != regionSetMap) {
                bgSet = regionSetMap.keySet();
            }
            if (bgDimensionSet.contains("NO_PERMISSION")) {
                List<DmFcstDimInfoVO> bgCodePermissionList = dmFcstIrbDimInfoDao.getBgCodePermissionList(commonBaseVO);
                Set<String> bgCodeSetList = bgCodePermissionList.stream().map(DmFcstDimInfoVO::getBgCode).collect(Collectors.toSet());
                bgList = bgList.stream().filter(bg -> bgCodeSetList.contains(bg.getBgCode())).collect(Collectors.toList());
            } else if (bgDimensionSet.size() == 0) {
                return ResultDataVO.success(bgList);
            } else {
                bgList = bgList.stream().filter(ele -> bgDimensionSet.contains(ele.getBgCode())).collect(Collectors.toList());
            }
            UserVO currentUser = UserInfoUtils.getCurrentUser();
            String roleName = currentUser.getCurrentRole().getRoleName();
            if (CommonConstant.REGION_ANALYST_IND.equals(roleName) && CollectionUtils.isNotEmpty(bgSet)) {
                Set<String> bgCodeSet = new HashSet<>();
                bgCodeSet.addAll(bgSet);
                bgList = bgList.stream().filter(ele -> bgCodeSet.contains(ele.getBgCode())).collect(Collectors.toList());
            }
        }
        return ResultDataVO.success(bgList);
    }

    public void setVersionId(CommonBaseVO commonBaseVO) {
        if (IndustryConstEnum.PAGE_TYPE.MONTH.getValue().equals(commonBaseVO.getPageType())
                || IndustryConstEnum.PAGE_TYPE.ANNUAL.getValue().equals(commonBaseVO.getPageType())
                || IndustryConstEnum.PAGE_TYPE.REPLACE_DIM.getValue().equals(commonBaseVO.getPageType())) {
            commonBaseVO.setVersionId(commonService.getVersionId(commonBaseVO.getPageType()));
        }
        // 量纲维度路径不获取版本
        if (IndustryConstEnum.VIEW_FLAG.DIMENSION.getValue().equals(commonBaseVO.getViewFlag())) {
            commonBaseVO.setVersionId(commonService.getVersionId(IndustryConstEnum.PAGE_TYPE.ANNUAL.getValue()));
        }
    }

    @Override
    @JalorOperation(code = "regionCodeList", desc = "地区部下拉框")
    public ResultDataVO regionCodeList(CodeReplInfoVO commonBaseVO) throws CommonApplicationException {
        // 获取版本号
        DataPermissionsVO currentRoleDataPermission = dataPermissionService.getCurrentRoleDataPermission();
        // 设置参数
        setRegionParam(commonBaseVO, currentRoleDataPermission);
        List<DmFcstDimInfoVO> regionCodeList = getRegionListFromDb(commonBaseVO);
        // 权限处理
        regionCodeList = regionCodePermission(commonBaseVO, currentRoleDataPermission, regionCodeList);
        // 排序
        Collections.sort(regionCodeList, Comparator.comparingInt(o ->
                CommonConstant.REGION_CODE_RULE.getOrDefault(o.getRegionCode(), Integer.MAX_VALUE)));
        return ResultDataVO.success(regionCodeList);
    }

    private List<DmFcstDimInfoVO> regionCodePermission(CodeReplInfoVO commonBaseVO, DataPermissionsVO currentRoleDataPermission, List<DmFcstDimInfoVO> regionCodeList) {
        // 获取权限
        if (commonBaseVO.getIsRegionAnalyst()) {
            Map<String, Map<String, Set<String>>> locationMap = currentRoleDataPermission.getLocationMap();
            Map<String, Set<String>> bgSetMap = locationMap.get(commonBaseVO.getOverseaFlag());
            Set<String> regionCodeOriginDimensionSet = currentRoleDataPermission.getRegionCodeDimensionSet();
            Set<String> regionDimensionTrueSet = currentRoleDataPermission.getRegionCodeDimensionTrueSet();
            Set<String> regionCodeDimensionSet = new HashSet<>();
            if (null != bgSetMap) {
                regionCodeDimensionSet = bgSetMap.get(commonBaseVO.getBgCode());
            } else {
                regionCodeDimensionSet.addAll(regionCodeOriginDimensionSet);
            }
            List<DmFcstDimInfoVO> originRegionCodeList = new ArrayList<>();
            originRegionCodeList.addAll(regionCodeList);
            if (regionCodeDimensionSet.size() != 0) {
                Set<String> regionDimensionSet = new HashSet<>();
                regionDimensionSet.addAll(regionCodeDimensionSet);
                regionCodeList = regionCodeList.stream().filter(re ->
                        regionDimensionSet.contains(re.getRegionCode())).collect(Collectors.toList());
                Set<String> regionCodeSet = originRegionCodeList.stream().filter(origin->
                        !"GLOBAL".equals(origin.getRegionCode())).map(DmFcstDimInfoVO::getRegionCode).collect(Collectors.toSet());
                if (regionDimensionTrueSet.containsAll(regionCodeSet) || regionDimensionTrueSet.size() == 0) {
                    DmFcstDimInfoVO dmFcstDimInfoVO = new DmFcstDimInfoVO();
                    dmFcstDimInfoVO.setRegionCode("GLOBAL");
                    dmFcstDimInfoVO.setRegionCnName("全球");
                    regionCodeList.add(dmFcstDimInfoVO);
                }
            }
        }
        return regionCodeList;
    }

    @Override
    @JalorOperation(code = "repofficeCodeList", desc = "代表处下拉框")
    public ResultDataVO repofficeCodeList(CodeReplInfoVO commonBaseVO) throws CommonApplicationException {
        DataPermissionsVO currentRoleDataPermission = dataPermissionService.getCurrentRoleDataPermission();
        // 设置参数
        setRegionParam(commonBaseVO, currentRoleDataPermission);
        if ("GLOBAL".equals(commonBaseVO.getRegionCode())) {
            commonBaseVO.setRegionCode(null);
        }
        commonBaseVO.setRegionCodeDimensionSet(currentRoleDataPermission.getRegionCodeDimensionSet());
        List<DmFcstDimInfoVO> repofficeCodeList = new ArrayList<>();
        List<String> costTypeList = commonBaseVO.getCostTypeList();
        if (CollectionUtils.isNotEmpty(costTypeList)) {
            for (String costType : costTypeList) {
                commonBaseVO.setCostType(costType);
                FcstIndustryUtil.setCommonTablePreFix(commonBaseVO);
                repofficeCodeList.addAll(dmFcstIrbDimInfoDao.getRepofficeCodeList(commonBaseVO));
            }
        } else {
            FcstIndustryUtil.setCommonTablePreFix(commonBaseVO);
            repofficeCodeList = dmFcstIrbDimInfoDao.getRepofficeCodeList(commonBaseVO);
        }
        // 权限处理
        repofficeCodeList = repofficeCodePermission(commonBaseVO, currentRoleDataPermission, repofficeCodeList.stream().distinct().collect(Collectors.toList()));
        // 排序
        Collections.sort(repofficeCodeList, Comparator.comparingInt(o ->
                CommonConstant.REPOFFICE_CODE_RULE.getOrDefault(o.getRepofficeCode(), Integer.MAX_VALUE)));
        return ResultDataVO.success(repofficeCodeList);
    }

    private List<DmFcstDimInfoVO> repofficeCodePermission(CodeReplInfoVO commonBaseVO, DataPermissionsVO currentRoleDataPermission, List<DmFcstDimInfoVO> repofficeCodeList) {
        if (commonBaseVO.getIsRegionAnalyst()) {
            Set<String> repofficeCodeDimensionSet = currentRoleDataPermission.getRepofficeCodeDimensionSet();
            List<DmFcstDimInfoVO> originRepofficeCodeList = new ArrayList<>();
            originRepofficeCodeList.addAll(repofficeCodeList);
            if (!repofficeCodeDimensionSet.isEmpty()) {
                repofficeCodeList = repofficeCodeList.stream().filter(repo ->
                        repofficeCodeDimensionSet.contains(repo.getRepofficeCode())).collect(Collectors.toList());
                Set<String> repofficeCodeSet = originRepofficeCodeList.stream().filter(code ->
                        !CommonConstant.PAGE_FLAG_ALL.equals(code.getRepofficeCode())).map(DmFcstDimInfoVO::getRepofficeCode).collect(Collectors.toSet());
                if (repofficeCodeDimensionSet.containsAll(repofficeCodeSet)
                        && repofficeCodeSet.size() == repofficeCodeDimensionSet.size()) {
                    DmFcstDimInfoVO dmFcstDimInfoVO = new DmFcstDimInfoVO();
                    dmFcstDimInfoVO.setRepofficeCode(CommonConstant.PAGE_FLAG_ALL);
                    dmFcstDimInfoVO.setRepofficeCnName(CommonConstant.ALL_CH);
                    repofficeCodeList.add(dmFcstDimInfoVO);
                }
            }
        }
        return repofficeCodeList;
    }

    @Override
    @JalorOperation(code = "overseaFlagList", desc = "国内海外下拉框")
    public ResultDataVO overseaFlagList(CodeReplInfoVO commonBaseVO) throws CommonApplicationException {
        if (null == commonBaseVO.getVersionId()) {
            setVersionId(commonBaseVO);
        }
        FcstIndustryUtil.setCommonTablePreFix(commonBaseVO);
        List<DmFcstDimInfoVO> overseaFlagList = dmFcstIrbDimInfoDao.getOverseaFlagList(commonBaseVO);

        UserVO currentUser = UserInfoUtils.getCurrentUser();
        String roleName = currentUser.getCurrentRole().getRoleName();
        if (CommonConstant.REGION_ANALYST_IND.equals(roleName)) {
            DataPermissionsVO currentRoleDataPermission = dataPermissionService.getCurrentRoleDataPermission();
            Set<String> overseaFlagDimensionSet = currentRoleDataPermission.getOverseaFlagDimensionSet();
            if (!overseaFlagDimensionSet.isEmpty()) {
                overseaFlagList = overseaFlagList.stream().filter(repo ->
                        overseaFlagDimensionSet.contains(repo.getOverseaFlag())).collect(Collectors.toList());
            }
            if (overseaFlagDimensionSet.containsAll(com.huawei.it.fcst.constant.CommonConstant.OVERSEA_FLAG) && overseaFlagDimensionSet.size() != 3) {
                DmFcstDimInfoVO dmFcstDimInfoVO = new DmFcstDimInfoVO();
                dmFcstDimInfoVO.setOverseaFlag(CommonConstEnum.OVERSEA_FLAG.G.getValue());
                overseaFlagList.add(dmFcstDimInfoVO);
            }
        }
        // 排序
        Collections.sort(overseaFlagList, Comparator.comparingInt(o ->
                CommonConstant.OVERSEA_FLAG_CODE_RULE.getOrDefault(o.getOverseaFlag(), Integer.MAX_VALUE)));
        return ResultDataVO.success(overseaFlagList);
    }

    @Override
    @JalorOperation(code = "mainFlagList", desc = "主力编码下拉框")
    public ResultDataVO mainFlagList(CodeReplInfoVO commonBaseVO) throws CommonApplicationException {
        // 获取版本号
        if (IndustryConstEnum.PAGE_TYPE.REPLACE_DIM.getValue().equals(commonBaseVO.getPageType())) {
            commonBaseVO.setPageType(IndustryConstEnum.PAGE_TYPE.MONTH.getValue());
        }
        if (null == commonBaseVO.getVersionId()) {
            setVersionId(commonBaseVO);
        }
        FcstIndustryUtil.setSpecailCode(commonBaseVO);
        List<DmFcstDimInfoVO> mainFlagList = new ArrayList<>();
        List<String> costTypeList = commonBaseVO.getCostTypeList();
        if (CollectionUtils.isNotEmpty(costTypeList)) {
            for (String costType : costTypeList) {
                commonBaseVO.setCostType(costType);
                FcstIndustryUtil.setCommonTablePreFix(commonBaseVO);
                mainFlagList.addAll(dmFcstIrbDimInfoDao.getMainFlagList(commonBaseVO));
            }
        } else {
            FcstIndustryUtil.setCommonTablePreFix(commonBaseVO);
            mainFlagList = dmFcstIrbDimInfoDao.getMainFlagList(commonBaseVO);
        }
        mainFlagList = mainFlagList.stream().distinct().collect(Collectors.toList());
        // 排序
        Collections.sort(mainFlagList,new Comparator<DmFcstDimInfoVO>(){
            @Override
            public int compare(DmFcstDimInfoVO o1, DmFcstDimInfoVO o2) {
                return CommonConstant.MAIN_FLAG_RULE.getOrDefault(o1.getMainFlag(),Integer.MAX_VALUE)-CommonConstant.MAIN_FLAG_RULE.getOrDefault(o2.getMainFlag(),Integer.MAX_VALUE);
            }
        });
        return ResultDataVO.success(mainFlagList);
    }

    @Override
    @JalorOperation(code = "dropDownList", desc = "年度月度下拉框")
    public ResultDataVO dropDownList(CommonBaseVO commonBaseVO) throws CommonApplicationException {
        // 获取版本号
        if (null == commonBaseVO.getVersionId()) {
            setVersionId(commonBaseVO);
        }
        // 必填参数校验
        if (StringUtils.isAnyBlank(commonBaseVO.getBgCode(),commonBaseVO.getOverseaFlag(), commonBaseVO.getRegionCode(), commonBaseVO.getRepofficeCode())) {
            throw new CommonApplicationException("必填参数为空");
        }
        FcstIndustryUtil.checkTablePreFixParam(commonBaseVO);
        // 设置数据权限
        setPermissionParameter(commonBaseVO);
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(commonBaseVO.getPageSize());
        pageVO.setCurPage(commonBaseVO.getPageIndex());
        FcstIndustryUtil.setSpecailCode(commonBaseVO);
        // 获取当前登陆人角色和账号
        BaseCusDimVO baseCusDimVO = ObjectCopyUtil.copy(commonBaseVO, BaseCusDimVO.class);
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        baseCusDimVO.setRoleId(String.valueOf(currentRole.getRoleId()));
        Long userId = UserInfoUtils.getUserId();
        baseCusDimVO.setUserId(String.valueOf(userId));
        // 加入相应层级汇总的组合名称
        CommonBaseVO combBaseVO = new CommonBaseVO();
        BeanUtils.copyProperties(commonBaseVO, combBaseVO);
        combBaseVO.setRoleId(String.valueOf(currentRole.getRoleId()));
        combBaseVO.setUserId(String.valueOf(userId));
        combBaseVO.setUserAccount(currentUser.getUserAccount());
        // 需要将汇总组合放在最上面
        List<DmFcstDimInfoVO> allDropDownDimInfoVOList = new ArrayList<>();
        // 获取汇总组合的列表集合
        addCombinationVoList(commonBaseVO, baseCusDimVO, combBaseVO, allDropDownDimInfoVOList);
        // 年度维度表需要处理主力编码字段,清空mainFlag标识
        if (IndustryConstEnum.PAGE_TYPE.ANNUAL.getValue().equals(commonBaseVO.getPageType())) {
            if (IndustryConstEnum.MAIN_FLAG.N.getValue().equals(commonBaseVO.getMainFlag())) {
                commonBaseVO.setMainFlag(null);
            }
        }
        // 获取各层级下拉框数据
        getFcstDimeInfoVOList(commonBaseVO, pageVO, baseCusDimVO, allDropDownDimInfoVOList);
        Map dropDownMap = new LinkedHashMap();
        dropDownMap.put("result", allDropDownDimInfoVOList);
        dropDownMap.put("pageVo", pageVO);
        return ResultDataVO.success(dropDownMap);
    }

    public void setPermissionParameter(CommonBaseVO commonBaseVO) {
        // 获取数据权限
        DataPermissionsVO currentRoleDataPermission = dataPermissionService.getCurrentRoleDataPermission();
        commonBaseVO.setLv0DimensionSet(currentRoleDataPermission.getLv0DimensionSet());
        commonBaseVO.setLv1DimensionSet(currentRoleDataPermission.getLv1DimensionSet());
        commonBaseVO.setLv2DimensionSet(currentRoleDataPermission.getLv2DimensionSet());
        commonBaseVO.setLv3DimensionSet(currentRoleDataPermission.getLv3DimensionSet());
    }

    private void addCombinationVoList(CommonBaseVO commonBaseVO, BaseCusDimVO baseCusDimVO, CommonBaseVO combBaseVO, List<DmFcstDimInfoVO> allDropDownDimInfoVOList) {
        if (IndustryConstEnum.PAGE_TYPE.ANNUAL.getValue().equals(commonBaseVO.getPageType())) {
            if (combBaseVO.getIsHistoryVersion() != null && !combBaseVO.getIsHistoryVersion()) {
                // 各层级的汇总组合
                List<DmFcstDimInfoVO> dmFocCustomCombVOList = new ArrayList<>();
                addCombinationVoList(combBaseVO, dmFocCustomCombVOList);
                allDropDownDimInfoVOList.addAll(dmFocCustomCombVOList);
            }
        } else {
            if (!IndustryConstEnum.PAGE_TYPE.CONFIG.getValue().equals(baseCusDimVO.getPageType())) {
                // 各层级的汇总组合
                List<DmFcstDimInfoVO> dmFocCustomCombVOList = new ArrayList<>();
                addCombinationVoList(combBaseVO, dmFocCustomCombVOList);
                allDropDownDimInfoVOList.addAll(dmFocCustomCombVOList);
            }
        }
    }

    private void getFcstDimeInfoVOList(CommonBaseVO commonBaseVO, PageVO pageVO, BaseCusDimVO baseCusDimVO, List<DmFcstDimInfoVO> allDropDownDimInfoVOList) {
        List<DmFcstDimInfoVO> dmFcstDimInfoVOList = getDmFcstDimInfoVOList(commonBaseVO, pageVO);
        // spart维度关联组合表和量纲维度关联组合表，设置组合状态
        FcstIndustryUtil.setLvCode(baseCusDimVO);
        baseCusDimVO.setCodeType("TOP");
        setSpartAndDimensionStatus(baseCusDimVO, dmFcstDimInfoVOList);
        // 处理Lv1和lv2的权限
        if (!IndustryConstEnum.PAGE_TYPE.CONFIG.getValue().equals(baseCusDimVO.getPageType())) {
            handlePermission(dmFcstDimInfoVOList, commonBaseVO);
        }
        List<DmFcstDimInfoVO> sortedFcstDimInfoVOList = dmFcstDimInfoVOList.stream().sorted(Comparator.comparing(vo -> vo.getStatusFlag(),
                Comparator.nullsLast(String::compareTo))).collect(Collectors.toList());
        Boolean notPage = !CommonConstant.PAGE_FLAG_LIST.contains(commonBaseVO.getPageType()) && !commonBaseVO.getSaveFlag();
        if (notPage && GroupLevelEnum.SPART.getValue().equals(commonBaseVO.getNextGroupLevel())) {
            // 分页
            int count = sortedFcstDimInfoVOList.size();
            sortedFcstDimInfoVOList = getFcstSpartDimInfoPageList(sortedFcstDimInfoVOList, pageVO);
            pageVO.setTotalRows(count);
        }
        allDropDownDimInfoVOList.addAll(sortedFcstDimInfoVOList);
    }

    private void setRegionParam(CodeReplInfoVO commonBaseVO, DataPermissionsVO currentRoleDataPermission) {
        if (IndustryConstEnum.PAGE_TYPE.REPLACE_DIM.getValue().equals(commonBaseVO.getPageType())) {
            commonBaseVO.setPageType(IndustryConstEnum.PAGE_TYPE.MONTH.getValue());
        }
        if (null == commonBaseVO.getVersionId()) {
            setVersionId(commonBaseVO);
        }
        if (StringUtils.isNotBlank(commonBaseVO.getOldSpartCode())) {
            String[] oldSpartArr = commonBaseVO.getOldSpartCode().split(",");
            List<String> oldSpartCodeList = Arrays.asList(oldSpartArr);
            commonBaseVO.setOldSpartCodeList(oldSpartCodeList);
        }

        commonBaseVO.setOverseaFlagDimensionSet(currentRoleDataPermission.getOverseaFlagDimensionSet());
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        String roleName = currentUser.getCurrentRole().getRoleName();
        if (CommonConstant.REGION_ANALYST_IND.equals(roleName)) {
            commonBaseVO.setIsRegionAnalyst(Boolean.TRUE);
        }
    }

    private List<DmFcstDimInfoVO> getBgCodeFromDb(CodeReplInfoVO commonBaseVO) throws CommonApplicationException {
        List<DmFcstDimInfoVO> bgList = new ArrayList<>();
        List<String> costTypeList = commonBaseVO.getCostTypeList();
        if (CollectionUtils.isNotEmpty(costTypeList)) {
            for (String costType : costTypeList) {
                commonBaseVO.setCostType(costType);
                FcstIndustryUtil.setCommonTablePreFix(commonBaseVO);
                bgList.addAll(dmFcstIrbDimInfoDao.getBgList(commonBaseVO));
            }
        } else {
            FcstIndustryUtil.setCommonTablePreFix(commonBaseVO);
            bgList = dmFcstIrbDimInfoDao.getBgList(commonBaseVO);
        }
        return bgList.stream().distinct().collect(Collectors.toList());
    }

    private List<DmFcstDimInfoVO> getRegionListFromDb(CodeReplInfoVO commonBaseVO) throws CommonApplicationException {
        List<DmFcstDimInfoVO> regionCodeList = new ArrayList<>();
        List<String> costTypeList = commonBaseVO.getCostTypeList();
        if (CollectionUtils.isNotEmpty(costTypeList)) {
            for (String costType : costTypeList) {
                commonBaseVO.setCostType(costType);
                FcstIndustryUtil.setCommonTablePreFix(commonBaseVO);
                regionCodeList.addAll(dmFcstIrbDimInfoDao.getRegionCodeList(commonBaseVO));
            }
        } else {
            FcstIndustryUtil.setCommonTablePreFix(commonBaseVO);
            regionCodeList = dmFcstIrbDimInfoDao.getRegionCodeList(commonBaseVO);
        }
        return regionCodeList.stream().distinct().collect(Collectors.toList());
    }

    @NotNull
    private List<DmFcstDimInfoVO> getFcstSpartDimInfoPageList(List<DmFcstDimInfoVO> sortedFcstDimInfoVOList, PageVO pageVO) {
        int count = sortedFcstDimInfoVOList.size();
        int curPage = pageVO.getCurPage() - 1;
        int pageSize = pageVO.getPageSize();
        List<DmFcstDimInfoVO> fcstDimInfoVOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(sortedFcstDimInfoVOList) && count > 0) {
            int fromIndex = curPage * pageSize;
            int totalIndex = (curPage + 1) * pageSize;
            if (totalIndex > count) {
                totalIndex = count;
            }
            fcstDimInfoVOList = sortedFcstDimInfoVOList.subList(fromIndex, totalIndex);
        }
        return fcstDimInfoVOList;
    }

    private List<DmFcstDimInfoVO> getDmFcstDimInfoVOList(CommonBaseVO commonBaseVO, PageVO pageVO) {
        List<DmFcstDimInfoVO> dmFcstDimInfoVOList = new ArrayList<>();
        if (IndustryConstEnum.MAIN_FLAG.Y.getValue().equals(commonBaseVO.getMainFlag())) {
            dmFcstDimInfoVOList = getMainDimInfoVOList(commonBaseVO);
        } else {
            dmFcstDimInfoVOList = getFcstDimInfoVOList(commonBaseVO);
        }
        return dmFcstDimInfoVOList;
    }

    private void addCombinationVoList(CommonBaseVO commonBaseVO, List<DmFcstDimInfoVO> dmFocCustomCombVOList) {
        List<DmFcstDimInfoVO> dmCustomCombVOList = dmFcstCustomCombDao.getCombinationParent(commonBaseVO);
        List<DmFcstDimInfoVO> customCombVOList = dmCustomCombVOList.stream().map(customCombVO -> {
            DmFcstDimInfoVO dmFocViewInfoVO = new DmFcstDimInfoVO();
            dmFocViewInfoVO.setCombId(customCombVO.getCombId());
            dmFocViewInfoVO.setGroupCode(customCombVO.getGroupCode());
            dmFocViewInfoVO.setGroupCnName(customCombVO.getGroupCnName());
            dmFocViewInfoVO.setEnableFlag(customCombVO.getEnableFlag());
            dmFocViewInfoVO.setIsCombination(true);
            return dmFocViewInfoVO;
        }).collect(Collectors.toList());
        dmFocCustomCombVOList.addAll(customCombVOList);
    }


    private void setSpartAndDimensionStatus(BaseCusDimVO baseCusDimVO, List<DmFcstDimInfoVO> dmFcstDimInfoVOList) {
        if (CommonConstant.GROUP_LEVEL_STATUS.contains(baseCusDimVO.getNextGroupLevel())) {
            setCodeAttributes(baseCusDimVO);
            List<DmFcstBaseCusDimVO> fiterDmFcstBaseCusDimStatus = getDmFcstBaseCusDimList(baseCusDimVO);
            for (DmFcstBaseCusDimVO fcstDimInfoStatus : fiterDmFcstBaseCusDimStatus) {
                for (DmFcstDimInfoVO fcstDimInfoVO : dmFcstDimInfoVOList) {
                    setStatusFlag(fcstDimInfoStatus, fcstDimInfoVO);
                }
            }
        }
    }

    private void setStatusFlag(DmFcstBaseCusDimVO fcstDimInfoStatus, DmFcstDimInfoVO fcstDimInfoVO) {
        if (fcstDimInfoStatus.getGroupCode().equals(fcstDimInfoVO.getGroupCode())) {
            if (CommonConstEnum.STATUS_FLAG.D.getValue().equals(fcstDimInfoStatus.getStatusFlag())) {
                fcstDimInfoVO.setStatusFlag(CommonConstEnum.STATUS_FLAG.ING.getValue());
            } else {
                fcstDimInfoVO.setStatusFlag(fcstDimInfoStatus.getStatusFlag());
            }
            fcstDimInfoVO.setCustomId(fcstDimInfoStatus.getCustomId());
        }
    }

    private List<DmFcstDimInfoVO> getFcstDimInfoVOList(CommonBaseVO commonBaseVO) {
        IndustryConstEnum.GRANULARITY_TYPE granularityType = IndustryConstEnum.getGranularityType(commonBaseVO.getGranularityType());
        List<DmFcstDimInfoVO> dmFcstDimInfoVOList = new ArrayList<>();
        switch (granularityType) {
            case IRB:
                dmFcstDimInfoVOList = dmFcstIrbDimInfoDao.baseIrbDimInfoList(commonBaseVO);
                break;
            case INDUS:
                dmFcstDimInfoVOList = dmFcstIndusDimInfoDao.baseIndusDimInfoList(commonBaseVO);
                break;
            case PROD:
                dmFcstDimInfoVOList = dmFcstProdDimInfoDao.baseProdDimInfoList(commonBaseVO);
                break;
        }
        return dmFcstDimInfoVOList;
    }

    private List<DmFcstDimInfoVO> getMainDimInfoVOList(CommonBaseVO commonBaseVO) {
        IndustryConstEnum.GRANULARITY_TYPE granularityType = IndustryConstEnum.getGranularityType(commonBaseVO.getGranularityType());
        List<DmFcstDimInfoVO> mainDimInfoVOList = new ArrayList<>();
        if (commonBaseVO.getVersionId() == null) {
            commonBaseVO.setVersionId(commonService.getVersionId(IndustryConstEnum.PAGE_TYPE.ANNUAL.getValue()));
        }
        commonBaseVO.setMonVersionId(commonService.getVersionId(IndustryConstEnum.PAGE_TYPE.MONTH.getValue()));
        if (IndustryConstEnum.PAGE_TYPE.MONTH.getValue().equals(commonBaseVO.getPageType())) {
            switch (granularityType) {
                case IRB:
                    mainDimInfoVOList = dmFcstIrbDimInfoDao.mainFlagDimInfoList(commonBaseVO);
                    break;
                case INDUS:
                    mainDimInfoVOList = dmFcstIndusDimInfoDao.mainFlagDimInfoList(commonBaseVO);
                    break;
                case PROD:
                    mainDimInfoVOList = dmFcstProdDimInfoDao.mainFlagDimInfoList(commonBaseVO);
                    break;
            }
        } else {
            switch (granularityType) {
                case IRB:
                    mainDimInfoVOList = dmFcstIrbDimInfoDao.mainFlagAnnualDimInfoList(commonBaseVO);
                    break;
                case INDUS:
                    mainDimInfoVOList = dmFcstIndusDimInfoDao.mainFlagAnnualDimInfoList(commonBaseVO);
                    break;
                case PROD:
                    mainDimInfoVOList = dmFcstProdDimInfoDao.mainFlagAnnualDimInfoList(commonBaseVO);
                    break;
            }
        }
        return mainDimInfoVOList;
    }

    @Override
    @JalorOperation(code = "dropDownCodeReplaceList", desc = "编码替换下拉框")
    public ResultDataVO dropDownCodeReplaceList(CodeReplInfoVO codeReplInfoVO) throws CommonApplicationException {
        // 获取版本号
        if (null == codeReplInfoVO.getVersionId()) {
            setVersionId(codeReplInfoVO);
        }
        FcstIndustryUtil.checkTablePreFixParam(codeReplInfoVO);
        FcstIndustryUtil.setSpecailCode(codeReplInfoVO);
        // 设置数据权限
        setPermissionParameter(codeReplInfoVO);
        // 获取当前登陆人角色和账号
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        codeReplInfoVO.setRoleId(String.valueOf(currentRole.getRoleId()));
        Long userId = UserInfoUtils.getUserId();
        codeReplInfoVO.setUserId(String.valueOf(userId));
        BaseCusDimVO baseCusDimVO = ObjectCopyUtil.copy(codeReplInfoVO, BaseCusDimVO.class);
        List<DmFcstDimInfoVO> dmFcstDimInfoVOList;
        Boolean relationFlag = CommonConstant.GROUP_LEVEL_RELATION.contains(codeReplInfoVO.getNextGroupLevel());
        if (StringUtils.isNotBlank(codeReplInfoVO.getRelationType()) || StringUtils.isNotBlank(codeReplInfoVO.getReplaceRelationType()) || StringUtils.isNotBlank(codeReplInfoVO.getReplaceRelationName()) || relationFlag) {
            // 触发编码替代关系维表，获取编码替代关系维表各层级下拉框数据
            codeReplInfoVO.setPageType(IndustryConstEnum.PAGE_TYPE.REPLACE_DIM.getValue());
            dmFcstDimInfoVOList = iDmFcstCodeReplInfoDao.getCodeReplInfoList(codeReplInfoVO);
            dmFcstDimInfoVOList = setSpecialDmInfoVOList(codeReplInfoVO, dmFcstDimInfoVOList);
            // 给替代关系名称设置计算状态
            setReplaceNameStatusFlag(codeReplInfoVO, baseCusDimVO, dmFcstDimInfoVOList);
        } else {
            // 没有触发编码替代关系维表，获取各层级下拉框数据(从TOP_SPART表获取)
            codeReplInfoVO.setMainFlag(IndustryConstEnum.MAIN_FLAG.N.getValue());
            codeReplInfoVO.setPageType(IndustryConstEnum.PAGE_TYPE.MONTH.getValue());
            dmFcstDimInfoVOList = getFcstDimInfoVOList(codeReplInfoVO);
            // 设置spart层级计算状态
            setSpartStatusFlag(codeReplInfoVO, baseCusDimVO, dmFcstDimInfoVOList);
        }
        // 处理Lv1和lv2的权限
        handlePermission(dmFcstDimInfoVOList, codeReplInfoVO);
        List<DmFcstDimInfoVO> sortedFcstDimInfoVOList = dmFcstDimInfoVOList.stream().sorted(Comparator.comparing(vo -> vo.getStatusFlag(),
                Comparator.nullsLast(String::compareTo))).collect(Collectors.toList());
        return ResultDataVO.success(sortedFcstDimInfoVOList);
    }

    @Nullable
    private List<DmFcstDimInfoVO> setSpecialDmInfoVOList(CodeReplInfoVO codeReplInfoVO, List<DmFcstDimInfoVO> dmFcstDimInfoVOList) {
        List<DmFcstDimInfoVO> newFcstDimInfoVOList = new ArrayList<>();
        // 新旧编码spart剔除空值
        if (GroupLevelEnum.NEW_SPART.getValue().equals(codeReplInfoVO.getNextGroupLevel()) || GroupLevelEnum.OLD_SPART.getValue().equals(codeReplInfoVO.getNextGroupLevel())) {
            return dmFcstDimInfoVOList.stream().filter(ele -> null != ele.getGroupCode()).collect(Collectors.toList());
        }
        // pbi维度置空
        if (CommonConstant.PBI_LEVEL_RELATION.contains(codeReplInfoVO.getNextGroupLevel())) {
            if (dmFcstDimInfoVOList.size() > 1) {
                return newFcstDimInfoVOList;
            } else {
                newFcstDimInfoVOList = dmFcstDimInfoVOList;
            }
        }
        // 编码替换3个维度相同编码的值需要处理新旧spart
        if (CommonConstant.GROUP_LEVEL_RELATION.contains(codeReplInfoVO.getNextGroupLevel())) {
            CodeReplInfoVO replInfoVO = new CodeReplInfoVO();
            BeanUtils.copyProperties(codeReplInfoVO, replInfoVO);
            HashMap<String, List<DmFcstDimInfoVO>> collect = dmFcstDimInfoVOList.stream().collect(Collectors.groupingBy(item -> item.getGroupCode(), HashMap::new, Collectors.toList()));
            for (Map.Entry<String, List<DmFcstDimInfoVO>> entry : collect.entrySet()) {
                List<DmFcstDimInfoVO> value = entry.getValue();
                Boolean pbiFlag = StringUtils.isNotBlank(replInfoVO.getLv1ProdRndTeamCode()) || StringUtils.isNotBlank(replInfoVO.getLv2ProdRndTeamCode())
                        || StringUtils.isNotBlank(replInfoVO.getLv3ProdRndTeamCode());
                if (pbiFlag || StringUtils.isNotBlank(replInfoVO.getLv4ProdRndTeamCode())) {
                    replInfoVO.setReplaceRelationName(entry.getKey());
                    replInfoVO.setNextGroupLevel(null);
                    List<DmFcstDimInfoVO> codeReplInfoList = iDmFcstCodeReplInfoDao.getCodeReplInfoList(replInfoVO);
                    List<DmFcstDimInfoVO> replaceNameNum = iDmFcstCodeReplInfoDao.getReplaceNameNum(replInfoVO);
                    if (codeReplInfoList.size() == replaceNameNum.size()) {
                        addReplaceNameDimInfoList(newFcstDimInfoVOList, entry, value);
                    }
                } else {
                    addReplaceNameDimInfoList(newFcstDimInfoVOList, entry, value);
                }
            }
        }
        return newFcstDimInfoVOList;
    }

    private void addReplaceNameDimInfoList(List<DmFcstDimInfoVO> newFcstDimInfoVOList, Map.Entry<String, List<DmFcstDimInfoVO>> entry, List<DmFcstDimInfoVO> value) {
        String newSpartCodeStr = value.stream().map(DmFcstDimInfoVO::getNewSpartCode).distinct().collect(Collectors.joining(","));
        String oldSpartCodeStr = value.stream().map(DmFcstDimInfoVO::getOldSpartCode).distinct().collect(Collectors.joining(","));
        DmFcstDimInfoVO dmFcstDimInfoVO = new DmFcstDimInfoVO();
        dmFcstDimInfoVO.setGroupCnName(entry.getKey());
        dmFcstDimInfoVO.setGroupCode(entry.getKey());
        dmFcstDimInfoVO.setNewSpartCode(newSpartCodeStr);
        dmFcstDimInfoVO.setOldSpartCode(oldSpartCodeStr);
        newFcstDimInfoVOList.add(dmFcstDimInfoVO);
    }


    private void setSpartStatusFlag(CodeReplInfoVO codeReplInfoVO, BaseCusDimVO baseCusDimVO, List<DmFcstDimInfoVO> dmFcstDimInfoVOList) {
        if (GroupLevelEnum.SPART.getValue().equals(codeReplInfoVO.getNextGroupLevel())) {
            FcstIndustryUtil.setLvCode(baseCusDimVO);
            baseCusDimVO.setCodeType("TOP");
            if (CommonConstant.GROUP_LEVEL_STATUS.contains(baseCusDimVO.getNextGroupLevel())) {
                setCodeAttributes(baseCusDimVO);
                List<DmFcstBaseCusDimVO> fiterDmFcstBaseCusDimStatus = getDmFcstBaseCusDimList(baseCusDimVO);
                fiterDmFcstBaseCusDimStatus.stream().forEach(fcstDimInfoStatus ->{
                    dmFcstDimInfoVOList.stream().forEach(fcstDimInfoVO ->{
                        if (fcstDimInfoStatus.getGroupCode().equals(fcstDimInfoVO.getGroupCode())) {
                            if (CommonConstEnum.STATUS_FLAG.D.getValue().equals(fcstDimInfoStatus.getStatusFlag())) {
                                fcstDimInfoVO.setStatusFlag(CommonConstEnum.STATUS_FLAG.ING.getValue());
                            } else {
                                fcstDimInfoVO.setStatusFlag(fcstDimInfoStatus.getStatusFlag());
                            }
                            fcstDimInfoVO.setCustomId(fcstDimInfoStatus.getCustomId());
                        }
                    });
                });
            }
        }
    }

    private void setReplaceNameStatusFlag(CodeReplInfoVO codeReplInfoVO, BaseCusDimVO baseCusDimVO, List<DmFcstDimInfoVO> dmFcstDimInfoVOList) {
        if ("REPLACE_NAME".equals(codeReplInfoVO.getNextGroupLevel())) {
            FcstIndustryUtil.setLvCode(baseCusDimVO);
            setCodeAttributes(baseCusDimVO);
            List<DmFcstBaseCusDimVO> dmFcstBaseCusDimStatus = dmFcstBaseCusDimDao.baseCusDimStatus(baseCusDimVO);
            for (DmFcstBaseCusDimVO fcstDimInfoStatus : dmFcstBaseCusDimStatus) {
                for (DmFcstDimInfoVO fcstDimInfoVO : dmFcstDimInfoVOList) {
                    if (fcstDimInfoStatus.getGroupCode().equals(fcstDimInfoVO.getGroupCode())) {
                        if (CommonConstEnum.STATUS_FLAG.D.getValue().equals(fcstDimInfoStatus.getStatusFlag())) {
                            fcstDimInfoVO.setStatusFlag(CommonConstEnum.STATUS_FLAG.ING.getValue());
                        } else {
                            fcstDimInfoVO.setStatusFlag(fcstDimInfoStatus.getStatusFlag());
                        }
                        fcstDimInfoVO.setCustomId(fcstDimInfoStatus.getCustomId());
                    }
                }
            }
        }
    }

    private void setCodeAttributes(BaseCusDimVO baseCusDimVO) {
        if (StringUtils.isBlank(baseCusDimVO.getCodeAttributes()) && IndustryConstEnum.MAIN_FLAG.Y.getValue().equals(baseCusDimVO.getMainFlag())) {
            baseCusDimVO.setCodeAttributes(CommonConstant.ALL_CH);
        }
    }

    @Override
    @JalorOperation(code = "dropDownInterLockList", desc = "勾稽管理下拉框")
    public ResultDataVO dropDownInterLockList(InterLockInfoVO interLockInfoVO) throws CommonApplicationException {
        // 获取版本号
        setVersionId(interLockInfoVO);
        // 设置数据权限
        setPermissionParameter(interLockInfoVO);
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(interLockInfoVO.getPageSize());
        pageVO.setCurPage(interLockInfoVO.getPageIndex());
        FcstIndustryUtil.setSpecailCode(interLockInfoVO);
        // 获取当前登陆人角色和账号
        BaseCusDimVO baseCusDimVO = ObjectCopyUtil.copy(interLockInfoVO, BaseCusDimVO.class);
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        baseCusDimVO.setRoleId(String.valueOf(currentRole.getRoleId()));
        Long userId = UserInfoUtils.getUserId();
        baseCusDimVO.setUserId(String.valueOf(userId));
        // 获取各层级下拉框数据
        List<String> costTypeList = interLockInfoVO.getCostTypeList();
        List<DmFcstDimInfoVO> dmFcstDimInfoVOList = getFcstDimInfoList(interLockInfoVO, costTypeList);
        //  去掉重复值(两个成本的数据合起来后，需要去重复)
        List<DmFcstDimInfoVO> distinctDimInfoList = dmFcstDimInfoVOList.stream().distinct().collect(Collectors.toList());
        // spart维度关联组合表和量纲维度关联组合表，设置组合状态
        FcstIndustryUtil.setLvCode(baseCusDimVO);
        baseCusDimVO.setCodeType("TOP");
        setInterLockStatusFlag(baseCusDimVO, costTypeList, distinctDimInfoList, interLockInfoVO);
        List<DmFcstDimInfoVO> sortedFcstDimInfoVOList = distinctDimInfoList.stream().sorted(Comparator.comparing(vo -> vo.getPspStatusFlag() + vo.getStdStatusFlag(),
                Comparator.nullsLast(String::compareTo))).collect(Collectors.toList());
        if (!interLockInfoVO.getSaveFlag() && GroupLevelEnum.SPART.getValue().equals(interLockInfoVO.getNextGroupLevel())) {
            // 分页
            int count = sortedFcstDimInfoVOList.size();
            sortedFcstDimInfoVOList = getFcstSpartDimInfoPageList(sortedFcstDimInfoVOList, pageVO);
            pageVO.setTotalRows(count);
        }
        Map interLockInfo = new LinkedHashMap();
        interLockInfo.put("result", sortedFcstDimInfoVOList);
        interLockInfo.put("pageVo", pageVO);
        return ResultDataVO.success(interLockInfo);
    }

    private List<DmFcstDimInfoVO> getFcstDimInfoList(InterLockInfoVO interLockInfoVO, List<String> costTypeList) throws CommonApplicationException {
        List<DmFcstDimInfoVO> dmFcstDimInfoVOList = new ArrayList<>();
        for (String costType : costTypeList) {
            FcstIndustryUtil.checkTablePreFixParam(interLockInfoVO);
            if (IndustryConstEnum.COST_TYPE.STD.getValue().equals(costType)) {
                interLockInfoVO.setSoftwareMark(null);
            }
            interLockInfoVO.setCostType(costType);
            if (IndustryConstEnum.MAIN_FLAG.N.getValue().equals(interLockInfoVO.getMainFlag())) {
                dmFcstDimInfoVOList.addAll(getFcstDimInfoVOList(interLockInfoVO));
                // 处理Lv1和lv2的权限
                handlePermission(dmFcstDimInfoVOList, interLockInfoVO);
            } else {
                dmFcstDimInfoVOList.addAll(getMainDimInfoVOList(interLockInfoVO));
                // 处理Lv1和lv2的权限
                handlePermission(dmFcstDimInfoVOList, interLockInfoVO);
            }
        }
        return dmFcstDimInfoVOList;
    }

    private void setInterLockStatusFlag(BaseCusDimVO baseCusDimVO, List<String> costTypeList, List<DmFcstDimInfoVO> distinctDimInfoList, InterLockInfoVO interLockInfoVO) {
        if (CommonConstant.GROUP_LEVEL_STATUS.contains(baseCusDimVO.getNextGroupLevel())) {
            for (String costType : costTypeList) {
                baseCusDimVO.setCostType(costType);
                if (IndustryConstEnum.COST_TYPE.STD.getValue().equals(costType)) {
                    baseCusDimVO.setSoftwareMark(null);
                }
                setCodeAttributes(baseCusDimVO);
                List<DmFcstBaseCusDimVO> fiterDmFcstBaseCusDimStatus = getDmFcstBaseCusDimList(baseCusDimVO);
                setStdBaseCusDimInfo(distinctDimInfoList, costType, fiterDmFcstBaseCusDimStatus, interLockInfoVO);
                setPspBaseCusDimInfo(distinctDimInfoList, costType, fiterDmFcstBaseCusDimStatus, interLockInfoVO);
            }
        }
    }

    @NotNull
    private List<DmFcstBaseCusDimVO> getDmFcstBaseCusDimList(BaseCusDimVO baseCusDimVO) {
        List<DmFcstBaseCusDimVO> dmFcstBaseCusDimVOList = dmFcstBaseCusDimDao.baseCusDimStatus(baseCusDimVO);
        // 组合的返回值需要处理lvCode字段
        // 由于lv0Code是拼接的，需要将拼接的字符串转换为数组后，
        List<String> paramLv0CodeList = new ArrayList<>();
        if (StringUtils.isNotBlank(baseCusDimVO.getLvCode())) {
            paramLv0CodeList = Arrays.asList(baseCusDimVO.getLvCode().replace("'", "").split(","));
        }
        List<DmFcstBaseCusDimVO> fiterDmFcstBaseCusDimStatus = new ArrayList<>();
        // 获取已有组合
        for (DmFcstBaseCusDimVO dmFcstBaseCusDimVO : dmFcstBaseCusDimVOList) {
            String lv0CodeStr = dmFcstBaseCusDimVO.getLvCode().replace("'", "");
            List<String> lv0CodeList = Arrays.asList(lv0CodeStr.split(","));
            if (paramLv0CodeList.containsAll(lv0CodeList) && paramLv0CodeList.size() == lv0CodeList.size()) {
                fiterDmFcstBaseCusDimStatus.add(dmFcstBaseCusDimVO);
            }
        }
        return fiterDmFcstBaseCusDimStatus;
    }

    private void setPspBaseCusDimInfo(List<DmFcstDimInfoVO> distinctDimInfoList, String costType, List<DmFcstBaseCusDimVO> dmFcstBaseCusDimVOList, InterLockInfoVO interLockInfoVO) {
        if (IndustryConstEnum.COST_TYPE.PSP.getValue().equals(costType)) {
            for (DmFcstBaseCusDimVO fcstDimInfoStatus : dmFcstBaseCusDimVOList) {
                for (DmFcstDimInfoVO fcstDimInfoVO : distinctDimInfoList) {
                    if (fcstDimInfoStatus.getGroupCode().equals(fcstDimInfoVO.getGroupCode())) {
                        if (CommonConstEnum.STATUS_FLAG.D.getValue().equals(fcstDimInfoStatus.getStatusFlag())) {
                            fcstDimInfoVO.setPspStatusFlag(CommonConstEnum.STATUS_FLAG.ING.getValue());
                        } else {
                            fcstDimInfoVO.setPspStatusFlag(fcstDimInfoStatus.getStatusFlag());
                        }
                        fcstDimInfoVO.setPspCustomId(fcstDimInfoStatus.getCustomId());
                    }
                    setIsNeedBlurStatus(interLockInfoVO, fcstDimInfoVO);
                }
            }
        }
    }

    private void setIsNeedBlurStatus(InterLockInfoVO interLockInfoVO, DmFcstDimInfoVO fcstDimInfoVO) {
        // 添加上是否虚化的状态标识(PSP成本)
        if (CommonConstEnum.STATUS_FLAG.Y.getValue().equals(fcstDimInfoVO.getStdStatusFlag()) && StringUtils.isBlank(fcstDimInfoVO.getPspStatusFlag())) {
            interLockInfoVO.setCostType(IndustryConstEnum.COST_TYPE.PSP.getValue());
            setGroupCodeAndLevel(interLockInfoVO, fcstDimInfoVO);
            int pspNum = getSpartOrDimensionNum(interLockInfoVO);
            if (pspNum > 1) {
                fcstDimInfoVO.setPspStatus(true);
            } else {
                fcstDimInfoVO.setPspStatus(false);
            }
        }
        // 添加上是否虚化的状态标识(STD成本)
        if (CommonConstEnum.STATUS_FLAG.Y.getValue().equals(fcstDimInfoVO.getPspStatusFlag()) && StringUtils.isBlank(fcstDimInfoVO.getStdStatusFlag())) {
            interLockInfoVO.setCostType(IndustryConstEnum.COST_TYPE.STD.getValue());
            setGroupCodeAndLevel(interLockInfoVO, fcstDimInfoVO);
            int stdNum = getSpartOrDimensionNum(interLockInfoVO);
            if (stdNum > 1) {
                fcstDimInfoVO.setStdStatus(true);
            } else {
                fcstDimInfoVO.setStdStatus(false);
            }
        }
    }

    private void setGroupCodeAndLevel(InterLockInfoVO interLockInfoVO, DmFcstDimInfoVO fcstDimInfoVO) {
        interLockInfoVO.setGroupLevel(interLockInfoVO.getNextGroupLevel());
        if (GroupLevelEnum.DIMENSION.getValue().equals(fcstDimInfoVO.getGroupLevel())) {
            interLockInfoVO.setDimensionCode(fcstDimInfoVO.getGroupCode());
        }
        if (GroupLevelEnum.SUBCATEGORY.getValue().equals(fcstDimInfoVO.getGroupLevel())) {
            interLockInfoVO.setDimensionSubCategoryCode(fcstDimInfoVO.getGroupCode());
        }
        if (GroupLevelEnum.SUB_DETAIL.getValue().equals(fcstDimInfoVO.getGroupLevel())) {
            interLockInfoVO.setDimensionSubDetailCode(fcstDimInfoVO.getGroupCode());
        }
        if (GroupLevelEnum.SPART.getValue().equals(fcstDimInfoVO.getGroupLevel())) {
            interLockInfoVO.setSpartCode(fcstDimInfoVO.getGroupCode());
        }
    }

    private void setStdBaseCusDimInfo(List<DmFcstDimInfoVO> distinctDimInfoList, String costType, List<DmFcstBaseCusDimVO> dmFcstBaseCusDimVOList, InterLockInfoVO interLockInfoVO) {
        if (IndustryConstEnum.COST_TYPE.STD.getValue().equals(costType)) {
            for (DmFcstBaseCusDimVO fcstDimInfoStatus : dmFcstBaseCusDimVOList) {
                for (DmFcstDimInfoVO fcstDimInfoVO : distinctDimInfoList) {
                    if (fcstDimInfoStatus.getGroupCode().equals(fcstDimInfoVO.getGroupCode())) {
                        if (CommonConstEnum.STATUS_FLAG.D.getValue().equals(fcstDimInfoStatus.getStatusFlag())) {
                            fcstDimInfoVO.setStdStatusFlag(CommonConstEnum.STATUS_FLAG.ING.getValue());
                        } else {
                            fcstDimInfoVO.setStdStatusFlag(fcstDimInfoStatus.getStatusFlag());
                        }
                        fcstDimInfoVO.setStdCustomId(fcstDimInfoStatus.getCustomId());
                    }
                    setIsNeedBlurStatus(interLockInfoVO, fcstDimInfoVO);
                }
            }
        }
    }

    public int getSpartOrDimensionNum(CommonBaseVO commonBaseVO) {
        IndustryConstEnum.GRANULARITY_TYPE granularityType = IndustryConstEnum.getGranularityType(commonBaseVO.getGranularityType());
        int num = 0;
        switch (granularityType) {
            case IRB:
                num = dmFcstIrbDimInfoDao.getIrbSpartOrDimensionNum(commonBaseVO);
                break;
            case INDUS:
                num = dmFcstIndusDimInfoDao.getIndusSpartOrDimensionNum(commonBaseVO);
                break;
            case PROD:
                num = dmFcstProdDimInfoDao.getProdSpartOrDimensionNum(commonBaseVO);
                break;
        }
        return num;
    }

    @Override
    @JalorOperation(code = "interLockSpartList", desc = "勾稽管理Spart列表")
    public ResultDataVO interLockSpartList(InterLockInfoVO interLockInfoVO) {
        // 获取版本号
        setVersionId(interLockInfoVO);
        FcstIndustryUtil.setSpecailCode(interLockInfoVO);
        BaseCusDimVO baseCusDimVO = ObjectCopyUtil.copy(interLockInfoVO, BaseCusDimVO.class);
        String costType = interLockInfoVO.getCostTypeList().stream().collect(Collectors.joining("_"));
        interLockInfoVO.setCostType(costType);
        List<DmFcstDimInfoVO> costGapSpartCodeList = getCostGapSpartCodeList(interLockInfoVO);
        // 分页
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(interLockInfoVO.getPageSize());
        pageVO.setCurPage(interLockInfoVO.getPageIndex());
        // 获取当前登陆人角色和账号
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        baseCusDimVO.setRoleId(String.valueOf(currentRole.getRoleId()));
        Long userId = UserInfoUtils.getUserId();
        baseCusDimVO.setUserId(String.valueOf(userId));
        // spart维度关联组合表和量纲维度关联组合表，设置组合状态
        FcstIndustryUtil.setLvCode(baseCusDimVO);
        baseCusDimVO.setCodeType("TOP");
        setInterLockStatusFlag(baseCusDimVO, interLockInfoVO.getCostTypeList(), costGapSpartCodeList, interLockInfoVO);
        List<DmFcstDimInfoVO> sortedCostGapSpartCodeList = costGapSpartCodeList.stream().sorted(Comparator.comparing(vo -> vo.getPspStatusFlag() + vo.getStdStatusFlag(),
                Comparator.nullsLast(String::compareTo))).collect(Collectors.toList());
        if (!interLockInfoVO.getSaveFlag()) {
            int count = sortedCostGapSpartCodeList.size();
            sortedCostGapSpartCodeList = getFcstSpartDimInfoPageList(sortedCostGapSpartCodeList, pageVO);
            pageVO.setTotalRows(count);
        }
        Map interLockSpartInfo = new LinkedHashMap();
        interLockSpartInfo.put("result", sortedCostGapSpartCodeList);
        interLockSpartInfo.put("pageVo", pageVO);
        return ResultDataVO.success(interLockSpartInfo);
    }

    @Override
    @JalorOperation(code = "interLockSpartVaildNum", desc = "勾稽管理Spart列表数据量")
    public ResultDataVO interLockSpartVaildNum(InterLockInfoVO interLockInfoVO) throws CommonApplicationException {
        // 获取版本号
        setVersionId(interLockInfoVO);
        FcstIndustryUtil.setSpecailCode(interLockInfoVO);
        interLockInfoVO.setCostType(IndustryConstEnum.COST_TYPE.PSP.getValue());
        FcstIndustryUtil.setCommonTablePreFix(interLockInfoVO);
        String costType = interLockInfoVO.getCostTypeList().stream().collect(Collectors.joining("_"));
        interLockInfoVO.setCostType(costType);
        return ResultDataVO.success(dmFcstIrbDimInfoDao.findCostGapSpartCodeNum(interLockInfoVO));
    }

    @JalorOperation(code = "lv0CodeDropdown", desc = "获取lv0编码")
    @Override
    public ResultDataVO lv0CodeDropdown(CommonBaseVO commonViewVO) throws CommonApplicationException {
        if (!CommonConstant.GRANULARITY_TYPE_LIST.stream().anyMatch(cost -> cost.equals(commonViewVO.getGranularityType()))) {
            throw new CommonApplicationException("目录树错误");
        }
        return ResultDataVO.success(dmFcstIrbDimInfoDao.getLv0Code(commonViewVO));
    }

    public List<DmFcstDimInfoVO> getCostGapSpartCodeList(CommonBaseVO commonBaseVO) {
        IndustryConstEnum.GRANULARITY_TYPE granularityType = IndustryConstEnum.getGranularityType(commonBaseVO.getGranularityType());
        List<DmFcstDimInfoVO> dmFcstDimInfoVOList = new ArrayList<>();
        switch (granularityType) {
            case IRB:
                dmFcstDimInfoVOList = dmFcstIrbDimInfoDao.findCostGapSpartCodeList(commonBaseVO);
                break;
            case INDUS:
                dmFcstDimInfoVOList = dmFcstIndusDimInfoDao.findCostGapSpartCodeList(commonBaseVO);
                break;
            case PROD:
                dmFcstDimInfoVOList = dmFcstProdDimInfoDao.findCostGapSpartCodeList(commonBaseVO);
                break;
        }
        return dmFcstDimInfoVOList;
    }

    /**
     * 处理Lv1和lv2的权限
     *
     * @param dmFcstDimInfoList
     * @param commonBaseVO
     */
    public void handlePermission(List<DmFcstDimInfoVO> dmFcstDimInfoList, CommonBaseVO commonBaseVO) {
        // 当有最大权限时，就不用循环校验各子项是否有权限了
        if (commonBaseVO.getLv0DimensionSet().size() == 0 && commonBaseVO.getLv1DimensionSet().size() == 0
                && commonBaseVO.getLv2DimensionSet().size() == 0 && commonBaseVO.getLv3DimensionSet().size() == 0) {
            return;
        }
        List<DmFcstDimInfoVO> allProdDimensionList = dataPermissionService.getLv1AndLv2VOList(commonBaseVO);
        // 处理产品LV0层级数据权限
        for (DmFcstDimInfoVO dmFcstDimInfoVO : dmFcstDimInfoList) {
            if (GroupLevelEnum.LV1.getValue().equals(dmFcstDimInfoVO.getGroupLevel())) {
                Set<String> lv2CodeSet = allProdDimensionList.stream()
                        .filter(item -> dmFcstDimInfoVO.getGroupCode().equals(item.getLv1Code()))
                        .map(DmFcstDimInfoVO::getLv2Code).collect(Collectors.toSet());
                // lv2DimensionSet不为空集合，并且不包含在里面代表没有LV0的权限
                if (commonBaseVO.getLv2DimensionSet().size() != 0 && !commonBaseVO.getLv2DimensionSet().contains(dmFcstDimInfoVO.getGroupCode())) {
                    dmFcstDimInfoVO.setPermissionFlag("no");
                    continue;
                }
                // lv3DimensionSet为空集合，代表拥有LV1的权限
                if (commonBaseVO.getLv3DimensionSet().size() == 0) {
                    dmFcstDimInfoVO.setPermissionFlag("has");
                    continue;
                }
                if (commonBaseVO.getLv3DimensionSet().containsAll(lv2CodeSet)) {
                    dmFcstDimInfoVO.setPermissionFlag("has");
                } else {
                    dmFcstDimInfoVO.setPermissionFlag("no");
                }
            }
        }
        // 补齐LV0层级数据，permissionFlag设置上no
        if (GroupLevelEnum.LV1.getValue().equals(commonBaseVO.getNextGroupLevel())) {
            setLv1DimInfoVO(commonBaseVO, dmFcstDimInfoList);
        }
    }

    private void setLv1DimInfoVO(CommonBaseVO commonBaseVO, List<DmFcstDimInfoVO> dmFcstDimInfoList) {
        List<DmFcstDimInfoVO> lv1DimInfoList = new ArrayList<>();
        if (IndustryConstEnum.PAGE_TYPE.ANNUAL.getValue().equals(commonBaseVO.getPageType())) {
            lv1DimInfoList = dataPermissionService.reverseFindLv1Code(commonBaseVO);
        } else {
            lv1DimInfoList = dataPermissionService.reverseFindLv1CodeMonth(commonBaseVO);
        }
        List<String> lv1DimInfoVOList = dmFcstDimInfoList.stream().map(lv1DimInfoVO -> lv1DimInfoVO.getGroupCode()).collect(Collectors.toList());
        // 无权限的LV0code集合
        DmFcstDimInfoVO dimInfoVO = dmFcstIrbDimInfoDao.getLv0Code(commonBaseVO).stream().findFirst().orElse(new DmFcstDimInfoVO());
        String lv0Code = dimInfoVO.getLv0ProdRndTeamCode();
        HashSet<DmFcstDimInfoVO> Lv1DimInfoVOSet = new HashSet<>();

        lv1DimInfoList.stream().forEach(lv1DimInfoEle -> {
            if (!lv1DimInfoVOList.contains(lv1DimInfoEle.getLv1Code())) {
                DmFcstDimInfoVO dmFcstDimInfoVO = new DmFcstDimInfoVO();
                dmFcstDimInfoVO.setGroupCode(lv1DimInfoEle.getLv1Code());
                dmFcstDimInfoVO.setGroupCnName(lv1DimInfoEle.getLv1CnName());
                dmFcstDimInfoVO.setGroupLevel(GroupLevelEnum.LV1.getValue());
                dmFcstDimInfoVO.setPermissionFlag("no");
                dmFcstDimInfoVO.setLv0Code(lv0Code);
                dmFcstDimInfoVO.setLv1Code(lv1DimInfoEle.getLv1Code());
                dmFcstDimInfoVO.setLv1CnName(lv1DimInfoEle.getLv1CnName());
                dmFcstDimInfoVO.setConnectCode(lv0Code + "#*#" + lv1DimInfoEle.getLv1Code());
                Lv1DimInfoVOSet.add(dmFcstDimInfoVO);
            }
        });
        // 无权限的lv1code添加到集合里
        dmFcstDimInfoList.addAll(Lv1DimInfoVOSet);
    }

}
