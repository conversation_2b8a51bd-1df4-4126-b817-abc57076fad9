/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.dao;

import com.huawei.it.fcst.industry.pbi.vo.common.CommonBaseVO;
import com.huawei.it.fcst.industry.pbi.vo.mix.MixSearchVO;
import com.huawei.it.fcst.industry.pbi.vo.version.DmFcstVersionInfoDTO;
import com.huawei.it.fcst.industry.pbi.vo.version.DmFcstVersionInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/3/13
 */
public interface IDmFcstVersionInfoDao {
    /**
     * 根据数据类型查询对应的最新版本ID
     *
     * @param dataType 数据类型（category：TOP品类、item：规格品）
     * @return DmFoiPlanVersionVO
     */
    DmFcstVersionInfoDTO findVersionIdByDataType(@Param("dataType") String dataType);


    DmFcstVersionInfoDTO findDmFocVersionDTOById(@Param("versionId") Long versionId);

    List<DmFcstVersionInfoDTO> findPlanVersionList(DmFcstVersionInfoDTO versionDto);

    /**
     * 根据版本信息查询版本列表信息
     * @param versionInfoDTO
     * @return
     */
    List<DmFcstVersionInfoDTO> findVersionListByVerName(DmFcstVersionInfoDTO versionInfoDTO);

    /**
     * 刷新维护维表
     *
     * @param versionVO 参数
     * @return int
     */
    int updateDmFcstVersionInfoDTO(DmFcstVersionInfoDTO versionVO);

    int updateRunningStatusFlag();

    int updateStatusFlag(Long versionId);

    int createDmFcstVersionInfoDTO(DmFcstVersionInfoDTO dmFocVersionInfoDTO);

    /**
     * Delete DmFoiPlanVersionVO by id.
     *
     * @param versionId versionId
     * @return int
     */
    int deleteDmFcstVersionInfoById(Long versionId);

    Long getVersionKey();

    String findMixActualMonth(MixSearchVO mixSearchVO);

    String findAnnualActualMonth(@Param("monthAnalysisVO") CommonBaseVO commonBaseVO);

    List<DmFcstVersionInfoDTO> findVersionList(DmFcstVersionInfoVO versionInfoVO);

    List<DmFcstVersionInfoDTO> findMaxDataReviewVersion();

    List<DmFcstVersionInfoDTO> findRefreshTime();

    /**
     * 根据版本名称查询版本信息
     *
     * @param versionInfoDTO 参数
     * @return List
     */
    DmFcstVersionInfoDTO findVersionListByVersion(DmFcstVersionInfoDTO versionInfoDTO);
}
