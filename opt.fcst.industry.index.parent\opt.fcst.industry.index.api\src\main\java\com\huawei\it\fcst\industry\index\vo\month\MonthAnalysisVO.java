/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.month;

import com.huawei.it.fcst.industry.index.vo.common.TableNameVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.Size;
import java.sql.Timestamp;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * The Entity of MonthAnalysisVO
 *
 * <AUTHOR>
 * @since 2023/03/15
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MonthAnalysisVO extends TableNameVO {
    /**
     * 视角标识，用于区分不同视角下的品类数据(0: ICT层级, 1:ICT-LV1层级, 2:ICT-LV1-LV2层级)
     */
    private String viewFlag;

    /**
     * 切换基期时入参，用于区分是ICT还是数字能源，可选值有：I：ICT、E：数字能源
     */
    private String industryFlag;

    public String getIndustryFlag() {
        String industryOrgFlag;
        switch (super.getIndustryOrg()) {
            case "ENERGY":
                industryOrgFlag = "E";
                break;
            case "IAS":
                industryOrgFlag = "IAS";
                break;
            default:
                industryOrgFlag = "I";
        }
        return industryOrgFlag;
    }

    /**
     * 重量级团队CODE
     */
    private List<String> prodRndTeamCodeList;

    /**
     * 重量级团队CODE
     */
    private String prodRndTeamCode;

    /**
     * lv0重量级团队CODE
     */
    private String lv0ProdRndTeamCode;

    /**
     * lv1重量级团队CODE
     */
    private String lv1ProdRndTeamCode;

    /**
     * lv2重量级团队CODE
     */
    private String lv2ProdRndTeamCode;

    /**
     * lv3重量级团队CODE
     */
    private String lv3ProdRndTeamCode;

    private String lv4ProdRndTeamCode;

    /**
     * 重量级团队LV1CODE
     */
    private List<String> lv1ProdRdTeamCnName;

    /**
     * 重量级团队LV2CODE
     */
    private List<String> lv2ProdRdTeamCnName;

    /**
     * 重量级团队LV3CODE
     */
    private List<String> lv3ProdRdTeamCnName;

    private List<String> lv4ProdRdTeamCnName;

    /**
     * lv3CegName
     */
    private String lv3CegCnName;

    /**
     * lv4CegCnName
     */
    private String lv4CegCnName;

    /**
     * categoryCnName
     */
    private String categoryCnName;

    /**
     * 基期
     */
    private Integer basePeriodId;

    /**
     * 分层级子项code编码集合
     */
    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<String> subGroupCodeList;

    /**
     * 分层级code编码
     */
    private List<String> groupCodeList;

    /**
     * 分层级编码对应中文名称
     */
    private List<String> groupCnNameList;

    private String groupCode;

    /**
     * 下个层级中文name
     */
    private String cnName;

    /**
     * 父层级编码
     */
    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<String> parentCodeList;

    private List<String> parentCnNameList;


    /**
     * 父层级组合编码
     */
    private List<String> combParentCodeList;

    /**
     * 父层级code
     */
    private String parentLevel;


    /**
     * 多选层级
     */
    private String multiLevel;

    /**
     * GROUP层级(ICT/LV1/LV2/CEG/CATEGORY/ITEM)
     */
    private String groupLevel;

    private String combGroupLevel;

    /**
     * 数据版本清单
     */
    private Long versionId;

    private Long monthVersionId;

    /**
     * 版本ID，对应dm_foi_top_cate_info_t表的版本ID
     */
    private Long topCateVersionId;

    /**
     * 基期开始时间
     */
    private Integer periodStartTime;

    /**
     * 基期结束时间
     */
    private Integer periodEndTime;

    /**
     * 当前页
     */
    private int curPage;

    /**
     * 每页显示条数
     */
    private int pageSize;

    private String groupCodeOrder;

    /**
     * 同环比标识（Y：同比，P：环比）
     */
    private String yoyFlag;

    private String fileName;

    /**
     * 颗粒度（U：通用，P：盈利，D:量纲）
     */
    private String granularityType;

    /**
     * 业务口径（R:收入时点/C：发货成本）
     */
    private String caliberFlag;

    /**
     * 盈利颗粒度l1Name
     */
    private List<String> l1NameList;

    private String l1Name;

    /**
     * 盈利颗粒度l2Name
     */
    private List<String> l2NameList;

    private String l2Name;

    private String profitsName;

    /**
     * ICT产业集合
     */
    private Set<String> lv0DimensionSet = new HashSet<>();

    /**
     * 重量级团队LV1集合
     */
    private Set<String> lv1DimensionSet  = new HashSet<>();

    /**
     * 重量级团队LV2集合
     */
    private Set<String> lv2DimensionSet  = new HashSet<>();

    /**
     * 国内/海外
     */
    private String overseaFlag;

    /**
     * BG编码
     */
    private String lv0ProdListCode;

    /**
     * 量纲维度code集合
     */
    private List<String> dmsCodeList;

    /**
     * 切换基期函数的量纲入参
     */
    private String  dmsCode;

    /**
     * 量纲
     */
    private List<String> dimensionCnName;

    /**
     * 量纲子类
     */
    private List<String> dimensionSubCategoryCnName;

    /**
     * 量纲子类明细
     */
    private List<String> dimensionSubDetailCnName;

    /**
     * 量纲子类明细
     */
    private List<String> spartCnName;

    /**
     * 是否多选
     */
    private Boolean isMultipleSelect;

    // 是否包含汇总组合
    private Boolean isContainComb;

    /**
     *  汇总组合 customIdList
     */
    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<Long> customIdList;

    private String customId;

    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<String> combinaCodeList;

    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<String> combinaSubGroupCodeList;


    private Boolean reverseViewFlag;

    // COA编码
    private String coaCode;

    // COA编码集合
    private List<String> coaCodeList;

    // COA名称集合
    private List<String> coaCnName;

    /**
     * 量纲code集合
     */
    private List<String> dimensionCodeList;

    /**
     * 量纲子类code集合
     */
    private List<String> dimensionSubcategoryCodeList;

    /**
     * 量纲子类明细code集合
     */
    private List<String> dimensionSubDetailCodeList;

    /**
     * spart code集合
     */
    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<String> spartCodeList;

    /**
     * spart code
     */
    private String spartCode;

    private String teamLevel;

    private String nextMonthGroupLevel;

    private String nextMonthGroupName;

    /**
     * false 表示没有多子项指数图
     * true  表示有多子项指数图
     */
    private Boolean isShowChildContent;

    /**
     * false：表示没有指数图
     * true：表示有指数图
     */
    private Boolean isShowPriceChart;

    /**
     * 只有在总成本下，且不存在子项图时，才传false，其余情况，制造成本，采购成本默认true
     */
    private Boolean isTotalChildChart;

    private List<String> purCodeList;

    private String purCode;

    private  String purLevel;

    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<String> teamCodeList;

    /**
     * 顶部的成本类型下拉框:
     *      制造成本：MANUFACTURE，采购成本：PURCHASE，总成本：TOTAL
     */
    private String costType;

    /**
     * 成本类型: 总成本选all的时候，需要选择成本类型
     */
    private String costSubType;

    /**
     * 发货对象编码
     */
    private String shippingObjectCode;

    /**
     * 发货对象名称
     */
    private String shippingObjectCnName;

    /**
     * 制造对象编码
     */
    private String manufactureObjectCode;

    /**
     * 制造对象名称
     */
    private String manufactureObjectCnName;

    private String type;

    private String pageId;

    /**
     * 对比分析入参
     */
    CompareAnalysisVO compareAnalysisVO;

    private Timestamp creationDate;

    private String yoyOrPop;

    private String begin;

    private String end;

}
