/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.month;

import com.huawei.it.fcst.annotations.ExportAttribute;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * The Entity of DmFocMonthWeightT
 *
 * <AUTHOR>
 * @since 2023/03/10
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExportMonthWeightVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("版本ID")
    private Long versionId;

    @ExportAttribute(sort = 0)
    private String costType;

    @ApiModelProperty("导出时分层级中文名称")
    @ExportAttribute(sort = 1)
    private String parentCnName;

    @ApiModelProperty("导出时分层级中文名称")
    @ExportAttribute(sort = 2)
    private String groupCnName;

    @ApiModelProperty("权重")
    private Double weightRate;

    @ApiModelProperty("权重占比")
    @ExportAttribute(sort = 3)
    private String weightPercent;

}
