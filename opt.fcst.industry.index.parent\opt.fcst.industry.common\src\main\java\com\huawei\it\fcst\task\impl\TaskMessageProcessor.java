/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.task.impl;

import com.huawei.it.fcst.task.ITaskProcessService;
import com.huawei.it.jalor5.async.AsyncMessage;
import com.huawei.it.jalor5.async.IMessageProcessor;
import com.huawei.it.jalor5.core.exception.ApplicationException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.io.Serializable;

/**
 * 执行function消息处理器
 *
 * <AUTHOR>
 * @since 202407
 */

@Slf4j
@Component("taskMessageProcessor")
public class TaskMessageProcessor implements IMessageProcessor {
    @Override
    public void process(AsyncMessage message) throws ApplicationException {
        if (message == null) {
            return;
        }
        Boolean callStatus = Boolean.TRUE;
        ITaskProcessService service = message.getContext(ITaskProcessService.class);
        Serializable content = message.getContent();
        try {
            Serializable before = service.before(content);
            callStatus = service.process(content, before);
        } catch (Exception exception) {
            callStatus = Boolean.FALSE;
        } finally {
            service.after(content, callStatus);
        }
    }

}
