/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.config;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.huawei.it.fcst.industry.index.dao.IDmFocDataRefreshStatusDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocManufactureReviewDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocProcurementReviewDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocVersionDao;
import com.huawei.it.fcst.industry.index.impl.annual.StatisticsExcelService;
import com.huawei.it.fcst.industry.index.impl.combination.AsyncService;
import com.huawei.it.fcst.industry.index.utils.ExcelUtil;
import com.huawei.it.fcst.industry.index.vo.common.DmFoiImpExpRecordVO;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoDTO;
import com.huawei.it.fcst.industry.index.vo.config.ManufactureBottomVO;
import com.huawei.it.fcst.industry.index.vo.config.ProcurementBottomVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.security.UserVO;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.inject.Inject;
import javax.servlet.http.HttpServletResponse;

/**
 * ConfigBottomReviewServiceTest Class
 *
 * <AUTHOR>
 * @since 2023/12/29
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class ConfigBottomReviewServiceTest {
    @InjectMocks
    private ConfigBottomReviewService configBottomReviewService;

    @Mock
    private IDmFocVersionDao dmFocVersionDao;

    @Mock
    private IDmFocProcurementReviewDao procurementReviewDao;

    @Mock
    private IDmFocManufactureReviewDao manufactureReviewDao;

    @Mock
    private IDmFocDataRefreshStatusDao dataRefreshStatusDao;

    @Mock
    private StatisticsExcelService statisticsExcelService;

    @Mock
    private ExcelUtil excelUtil;

    @Mock
    private AsyncService asyncService;

    private static final Logger LOGGER = LoggerFactory.getLogger(ConfigBottomReviewService.class);

    @Before
    public void before() throws Exception {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);
    }

    @Test
    public void getProcurementDropDown() throws CommonApplicationException {
        ProcurementBottomVO procurementBottomVO= new ProcurementBottomVO();
        ResultDataVO procurementDropDown = configBottomReviewService.getProcurementDropDown(procurementBottomVO);
        Assert.assertNotNull(procurementDropDown);
    }

    @Test
    public void getProFormDropDown() throws CommonApplicationException {
        ProcurementBottomVO procurementBottomVO= new ProcurementBottomVO();
        ResultDataVO procurementDropDown = configBottomReviewService.getProFormDropDown(procurementBottomVO);
        Assert.assertNotNull(procurementDropDown);
    }

    @Test
    public void getProFormDropDownTwo() throws CommonApplicationException {
        ProcurementBottomVO procurementBottomVO= new ProcurementBottomVO();
        procurementBottomVO.setItemCode("1156D");
        ResultDataVO procurementDropDown = configBottomReviewService.getProFormDropDown(procurementBottomVO);
        Assert.assertNotNull(procurementDropDown);
    }

    @Test
    public void getProFormDropDownOne() throws CommonApplicationException {
        ProcurementBottomVO procurementBottomVO= new ProcurementBottomVO();
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setStartPeriod(202306L);
        ResultDataVO procurementDropDown = configBottomReviewService.getProFormDropDown(procurementBottomVO);
        Assert.assertNotNull(procurementDropDown);
    }

    @Test
    public void saveProModify() throws Exception {
        List<ProcurementBottomVO> procurementVOList = new ArrayList<>();
        ProcurementBottomVO procurementBottomVO = new ProcurementBottomVO();
        procurementVOList.add(procurementBottomVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveProModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void saveProModifyOne() throws Exception {
        List<ProcurementBottomVO> procurementVOList = new ArrayList<>();
        ProcurementBottomVO procurementBottomVO = new ProcurementBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("REVOKE");
        procurementVOList.add(procurementBottomVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveProModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void saveProModifyTwo() throws Exception {
        List<ProcurementBottomVO> procurementVOList = new ArrayList<>();
        ProcurementBottomVO procurementBottomVO = new ProcurementBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("C");
        procurementVOList.add(procurementBottomVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveProModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void saveProModifyThree() throws Exception {
        List<ProcurementBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ProcurementBottomVO procurementBottomVO = new ProcurementBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setL3CegCode("3122A");
        // 入参2
        ProcurementBottomVO procurementBottomVO2 = new ProcurementBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("MODIFY");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156A");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setL3CegCode("3122A");
        // 入参3
        ProcurementBottomVO procurementBottomVO3 = new ProcurementBottomVO();
        procurementBottomVO3.setVersionId(155L);
        procurementBottomVO3.setModifyType("INSERT");
        procurementBottomVO3.setCaliberFlag("R");
        procurementBottomVO3.setItemCode("1156C");
        procurementBottomVO3.setItemCode("1156C");
        procurementBottomVO3.setStartPeriod(202304L);
        procurementBottomVO3.setEndPeriod(202309L);
        procurementBottomVO3.setL3CegCode("3122A");

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);
        procurementVOList.add(procurementBottomVO3);

        List<ProcurementBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ProcurementBottomVO procurementBottom = new ProcurementBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202304L);
        procurementBottom.setEndPeriod(202309L);

        // 系统数据2
        ProcurementBottomVO procurementBottom2 = new ProcurementBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ProcurementBottomVO procurementBottom3 = new ProcurementBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202302L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        when(procurementReviewDao.findBottomReviewVOList(any())).thenReturn(bottomReviewList);

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveProModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void saveProModify4T() throws Exception {
        List<ProcurementBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ProcurementBottomVO procurementBottomVO = new ProcurementBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setL3CegCode("3122A");
        procurementBottomVO.setL3CegShortCnName("元器件");
        // 入参2
        ProcurementBottomVO procurementBottomVO2 = new ProcurementBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("MODIFY");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156A");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setL3CegCode("3122A");
        procurementBottomVO2.setL3CegShortCnName("元器件");
        // 入参3
        ProcurementBottomVO procurementBottomVO3 = new ProcurementBottomVO();
        procurementBottomVO3.setVersionId(155L);
        procurementBottomVO3.setModifyType("INSERT");
        procurementBottomVO3.setCaliberFlag("R");
        procurementBottomVO3.setItemCode("1156C");
        procurementBottomVO3.setItemCode("1156C");
        procurementBottomVO3.setStartPeriod(202304L);
        procurementBottomVO3.setEndPeriod(202309L);
        procurementBottomVO3.setL3CegCode("3122A");
        procurementBottomVO3.setL3CegShortCnName("元器件");

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);
        procurementVOList.add(procurementBottomVO3);

        List<ProcurementBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ProcurementBottomVO procurementBottom = new ProcurementBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202304L);
        procurementBottom.setEndPeriod(202309L);

        // 系统数据2
        ProcurementBottomVO procurementBottom2 = new ProcurementBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ProcurementBottomVO procurementBottom3 = new ProcurementBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202302L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        when(procurementReviewDao.findBottomReviewVOList(any())).thenReturn(bottomReviewList);

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveProModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void saveProModify5T() throws Exception {
        List<ProcurementBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ProcurementBottomVO procurementBottomVO = new ProcurementBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setL3CegCode("3122A");
        procurementBottomVO.setL3CegShortCnName("元器件");
        procurementBottomVO.setL4CegCode("4122A");
        // 入参2
        ProcurementBottomVO procurementBottomVO2 = new ProcurementBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("MODIFY");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156A");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setL3CegCode("3122A");
        procurementBottomVO2.setL3CegShortCnName("元器件");
        procurementBottomVO2.setL4CegCode("4122A");
        // 入参3
        ProcurementBottomVO procurementBottomVO3 = new ProcurementBottomVO();
        procurementBottomVO3.setVersionId(155L);
        procurementBottomVO3.setModifyType("INSERT");
        procurementBottomVO3.setCaliberFlag("R");
        procurementBottomVO3.setItemCode("1156C");
        procurementBottomVO3.setItemCode("1156C");
        procurementBottomVO3.setStartPeriod(202304L);
        procurementBottomVO3.setEndPeriod(202309L);
        procurementBottomVO3.setL3CegCode("3122A");
        procurementBottomVO3.setL3CegShortCnName("元器件");
        procurementBottomVO3.setL4CegCode("4122A");

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);
        procurementVOList.add(procurementBottomVO3);

        List<ProcurementBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ProcurementBottomVO procurementBottom = new ProcurementBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202304L);
        procurementBottom.setEndPeriod(202309L);

        // 系统数据2
        ProcurementBottomVO procurementBottom2 = new ProcurementBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ProcurementBottomVO procurementBottom3 = new ProcurementBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202302L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        when(procurementReviewDao.findBottomReviewVOList(any())).thenReturn(bottomReviewList);

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveProModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void saveProModify6T() throws Exception {
        List<ProcurementBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ProcurementBottomVO procurementBottomVO = new ProcurementBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setL3CegCode("3122A");
        procurementBottomVO.setL3CegShortCnName("元器件");
        procurementBottomVO.setL4CegCode("4122A");
        procurementBottomVO.setL4CegShortCnName("元器组价件");
        // 入参2
        ProcurementBottomVO procurementBottomVO2 = new ProcurementBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("MODIFY");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156A");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setL3CegCode("3122A");
        procurementBottomVO2.setL3CegShortCnName("元器件");
        procurementBottomVO2.setL4CegCode("4122A");
        procurementBottomVO2.setL4CegShortCnName("元器组价件");
        // 入参3
        ProcurementBottomVO procurementBottomVO3 = new ProcurementBottomVO();
        procurementBottomVO3.setVersionId(155L);
        procurementBottomVO3.setModifyType("INSERT");
        procurementBottomVO3.setCaliberFlag("R");
        procurementBottomVO3.setItemCode("1156C");
        procurementBottomVO3.setItemCode("1156C");
        procurementBottomVO3.setStartPeriod(202304L);
        procurementBottomVO3.setEndPeriod(202309L);
        procurementBottomVO3.setL3CegCode("3122A");
        procurementBottomVO3.setL3CegShortCnName("元器件");
        procurementBottomVO3.setL4CegCode("4122A");
        procurementBottomVO3.setL4CegShortCnName("元器组价件");

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);
        procurementVOList.add(procurementBottomVO3);

        List<ProcurementBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ProcurementBottomVO procurementBottom = new ProcurementBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202304L);
        procurementBottom.setEndPeriod(202309L);

        // 系统数据2
        ProcurementBottomVO procurementBottom2 = new ProcurementBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ProcurementBottomVO procurementBottom3 = new ProcurementBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202302L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        when(procurementReviewDao.findBottomReviewVOList(any())).thenReturn(bottomReviewList);

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveProModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void saveProModify7T() throws Exception {
        List<ProcurementBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ProcurementBottomVO procurementBottomVO = new ProcurementBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setL3CegCode("3122A");
        procurementBottomVO.setL3CegShortCnName("元器件");
        procurementBottomVO.setL4CegCode("4122A");
        procurementBottomVO.setL4CegShortCnName("元器组价件");
        procurementBottomVO.setCategoryCode("5122D");
        // 入参2
        ProcurementBottomVO procurementBottomVO2 = new ProcurementBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("MODIFY");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156A");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setL3CegCode("3122A");
        procurementBottomVO2.setL3CegShortCnName("元器件");
        procurementBottomVO2.setL4CegCode("4122A");
        procurementBottomVO2.setL4CegShortCnName("元器组价件");
        procurementBottomVO2.setCategoryCode("5122D");
        // 入参3
        ProcurementBottomVO procurementBottomVO3 = new ProcurementBottomVO();
        procurementBottomVO3.setVersionId(155L);
        procurementBottomVO3.setModifyType("INSERT");
        procurementBottomVO3.setCaliberFlag("R");
        procurementBottomVO3.setItemCode("1156C");
        procurementBottomVO3.setItemCode("1156C");
        procurementBottomVO3.setStartPeriod(202304L);
        procurementBottomVO3.setEndPeriod(202309L);
        procurementBottomVO3.setL3CegCode("3122A");
        procurementBottomVO3.setL3CegShortCnName("元器件");
        procurementBottomVO3.setL4CegCode("4122A");
        procurementBottomVO3.setL4CegShortCnName("元器组价件");
        procurementBottomVO3.setCategoryCode("5122D");

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);
        procurementVOList.add(procurementBottomVO3);

        List<ProcurementBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ProcurementBottomVO procurementBottom = new ProcurementBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202304L);
        procurementBottom.setEndPeriod(202309L);

        // 系统数据2
        ProcurementBottomVO procurementBottom2 = new ProcurementBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ProcurementBottomVO procurementBottom3 = new ProcurementBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202302L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        when(procurementReviewDao.findBottomReviewVOList(any())).thenReturn(bottomReviewList);

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveProModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void saveProModify8T() throws Exception {
        List<ProcurementBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ProcurementBottomVO procurementBottomVO = new ProcurementBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setL3CegCode("3122A");
        procurementBottomVO.setL3CegShortCnName("元器件");
        procurementBottomVO.setL4CegCode("4122A");
        procurementBottomVO.setL4CegShortCnName("元器组价件");
        procurementBottomVO.setCategoryCode("5122D");
        procurementBottomVO.setCategoryCnName("拼板");
        // 入参2
        ProcurementBottomVO procurementBottomVO2 = new ProcurementBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("MODIFY");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setL3CegCode("3122A");
        procurementBottomVO2.setL3CegShortCnName("元器件");
        procurementBottomVO2.setL4CegCode("4122A");
        procurementBottomVO2.setL4CegShortCnName("元器组价件");
        procurementBottomVO2.setCategoryCode("5122D");
        procurementBottomVO2.setCategoryCnName("拼板");
        // 入参3
        ProcurementBottomVO procurementBottomVO3 = new ProcurementBottomVO();
        procurementBottomVO3.setVersionId(155L);
        procurementBottomVO3.setModifyType("INSERT");
        procurementBottomVO3.setCaliberFlag("R");
        procurementBottomVO3.setStartPeriod(202304L);
        procurementBottomVO3.setEndPeriod(202309L);
        procurementBottomVO3.setL3CegCode("3122A");
        procurementBottomVO3.setL3CegShortCnName("元器件");
        procurementBottomVO3.setL4CegCode("4122A");
        procurementBottomVO3.setL4CegShortCnName("元器组价件");
        procurementBottomVO3.setCategoryCode("5122D");
        procurementBottomVO3.setCategoryCnName("拼板");

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);
        procurementVOList.add(procurementBottomVO3);

        List<ProcurementBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ProcurementBottomVO procurementBottom = new ProcurementBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202304L);
        procurementBottom.setEndPeriod(202309L);

        // 系统数据2
        ProcurementBottomVO procurementBottom2 = new ProcurementBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ProcurementBottomVO procurementBottom3 = new ProcurementBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202302L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        when(procurementReviewDao.findBottomReviewVOList(any())).thenReturn(bottomReviewList);

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveProModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void saveProModify9T() throws Exception {
        List<ProcurementBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ProcurementBottomVO procurementBottomVO = new ProcurementBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setL3CegCode("3122A");
        procurementBottomVO.setL3CegShortCnName("元器件");
        procurementBottomVO.setL4CegCode("4122A");
        procurementBottomVO.setL4CegShortCnName("元器组价件");
        procurementBottomVO.setCategoryCode("5122D");
        procurementBottomVO.setCategoryCnName("拼板");
        // 入参2
        ProcurementBottomVO procurementBottomVO2 = new ProcurementBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("MODIFY");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156A");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setL3CegCode("3122A");
        procurementBottomVO2.setL3CegShortCnName("元器件");
        procurementBottomVO2.setL4CegCode("4122A");
        procurementBottomVO2.setL4CegShortCnName("元器组价件");
        procurementBottomVO2.setCategoryCode("5122D");
        procurementBottomVO2.setCategoryCnName("拼板");
        // 入参3
        ProcurementBottomVO procurementBottomVO3 = new ProcurementBottomVO();
        procurementBottomVO3.setVersionId(155L);
        procurementBottomVO3.setModifyType("INSERT");
        procurementBottomVO3.setCaliberFlag("R");
        procurementBottomVO3.setItemCode("1156C");
        procurementBottomVO3.setItemCode("1156C");
        procurementBottomVO3.setStartPeriod(202304L);
        procurementBottomVO3.setEndPeriod(202309L);
        procurementBottomVO3.setL3CegCode("3122A");
        procurementBottomVO3.setL3CegShortCnName("元器件");
        procurementBottomVO3.setL4CegCode("4122A");
        procurementBottomVO3.setL4CegShortCnName("元器组价件");
        procurementBottomVO3.setCategoryCode("5122D");
        procurementBottomVO3.setCategoryCnName("拼板");

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);
        procurementVOList.add(procurementBottomVO3);

        List<ProcurementBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ProcurementBottomVO procurementBottom = new ProcurementBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202304L);
        procurementBottom.setEndPeriod(202309L);

        // 系统数据2
        ProcurementBottomVO procurementBottom2 = new ProcurementBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ProcurementBottomVO procurementBottom3 = new ProcurementBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202302L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        when(procurementReviewDao.findBottomReviewVOList(any())).thenReturn(bottomReviewList);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveProModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void saveProModify10T() throws Exception {
        List<ProcurementBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ProcurementBottomVO procurementBottomVO = new ProcurementBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setItemCnName("结构件");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setL3CegCode("3122A");
        procurementBottomVO.setL3CegShortCnName("元器件");
        procurementBottomVO.setL4CegCode("4122A");
        procurementBottomVO.setL4CegShortCnName("元器组价件");
        procurementBottomVO.setCategoryCode("5122D");
        procurementBottomVO.setCategoryCnName("拼板");
        // 入参2
        ProcurementBottomVO procurementBottomVO2 = new ProcurementBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("MODIFY");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156A");
        procurementBottomVO2.setItemCnName("结构件2");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setL3CegCode("3122A");
        procurementBottomVO2.setL3CegShortCnName("元器件");
        procurementBottomVO2.setL4CegCode("4122A");
        procurementBottomVO2.setL4CegShortCnName("元器组价件");
        procurementBottomVO2.setCategoryCode("5122D");
        procurementBottomVO2.setCategoryCnName("拼板");
        // 入参3
        ProcurementBottomVO procurementBottomVO3 = new ProcurementBottomVO();
        procurementBottomVO3.setVersionId(155L);
        procurementBottomVO3.setModifyType("INSERT");
        procurementBottomVO3.setCaliberFlag("R");
        procurementBottomVO3.setItemCode("1156C");
        procurementBottomVO3.setItemCnName("结构件3");
        procurementBottomVO3.setItemCode("1156C");
        procurementBottomVO3.setStartPeriod(202304L);
        procurementBottomVO3.setEndPeriod(202309L);
        procurementBottomVO3.setL3CegCode("3122A");
        procurementBottomVO3.setL3CegShortCnName("元器件");
        procurementBottomVO3.setL4CegCode("4122A");
        procurementBottomVO3.setL4CegShortCnName("元器组价件");
        procurementBottomVO3.setCategoryCode("5122D");
        procurementBottomVO3.setCategoryCnName("拼板");

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);
        procurementVOList.add(procurementBottomVO3);

        List<ProcurementBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ProcurementBottomVO procurementBottom = new ProcurementBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202304L);
        procurementBottom.setEndPeriod(202309L);

        // 系统数据2
        ProcurementBottomVO procurementBottom2 = new ProcurementBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ProcurementBottomVO procurementBottom3 = new ProcurementBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202302L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        when(procurementReviewDao.findBottomReviewVOList(any())).thenReturn(bottomReviewList);


        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveProModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void saveProModify11T() throws Exception {
        List<ProcurementBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ProcurementBottomVO procurementBottomVO = new ProcurementBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setItemCnName("结构件");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setL3CegCode("3122A");
        procurementBottomVO.setL3CegShortCnName("元器件");
        procurementBottomVO.setL4CegCode("4122A");
        procurementBottomVO.setL4CegShortCnName("元器组价件");
        procurementBottomVO.setCategoryCode("5122D");
        procurementBottomVO.setCategoryCnName("拼板");
        procurementBottomVO.setStartPeriod(202305L);
        // 入参2
        ProcurementBottomVO procurementBottomVO2 = new ProcurementBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("MODIFY");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156A");
        procurementBottomVO2.setItemCnName("结构件2");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setL3CegCode("3122A");
        procurementBottomVO2.setL3CegShortCnName("元器件");
        procurementBottomVO2.setL4CegCode("4122A");
        procurementBottomVO2.setL4CegShortCnName("元器组价件");
        procurementBottomVO2.setCategoryCode("5122D");
        procurementBottomVO2.setCategoryCnName("拼板");
        procurementBottomVO2.setStartPeriod(202305L);
        // 入参3
        ProcurementBottomVO procurementBottomVO3 = new ProcurementBottomVO();
        procurementBottomVO3.setVersionId(155L);
        procurementBottomVO3.setModifyType("INSERT");
        procurementBottomVO3.setCaliberFlag("R");
        procurementBottomVO3.setItemCode("1156C");
        procurementBottomVO3.setItemCnName("结构件3");
        procurementBottomVO3.setItemCode("1156C");
        procurementBottomVO3.setStartPeriod(202304L);
        procurementBottomVO3.setEndPeriod(202309L);
        procurementBottomVO3.setL3CegCode("3122A");
        procurementBottomVO3.setL3CegShortCnName("元器件");
        procurementBottomVO3.setL4CegCode("4122A");
        procurementBottomVO3.setL4CegShortCnName("元器组价件");
        procurementBottomVO3.setCategoryCode("5122D");
        procurementBottomVO3.setCategoryCnName("拼板");
        procurementBottomVO3.setStartPeriod(202305L);

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);
        procurementVOList.add(procurementBottomVO3);

        List<ProcurementBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ProcurementBottomVO procurementBottom = new ProcurementBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202304L);
        procurementBottom.setEndPeriod(202309L);

        // 系统数据2
        ProcurementBottomVO procurementBottom2 = new ProcurementBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ProcurementBottomVO procurementBottom3 = new ProcurementBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202302L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        when(procurementReviewDao.findBottomReviewVOList(any())).thenReturn(bottomReviewList);


        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveProModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    // 校验结束期
    @Test
    public void saveProModify12T() throws Exception {
        List<ProcurementBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ProcurementBottomVO procurementBottomVO = new ProcurementBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setItemCnName("结构件");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setL3CegCode("3122A");
        procurementBottomVO.setL3CegShortCnName("元器件");
        procurementBottomVO.setL4CegCode("4122A");
        procurementBottomVO.setL4CegShortCnName("元器组价件");
        procurementBottomVO.setCategoryCode("5122D");
        procurementBottomVO.setCategoryCnName("拼板");
        procurementBottomVO.setStartPeriod(202305L);
        procurementBottomVO.setEndPeriod(202308L);
        // 入参2
        ProcurementBottomVO procurementBottomVO2 = new ProcurementBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("MODIFY");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156A");
        procurementBottomVO2.setItemCnName("结构件2");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setL3CegCode("3122A");
        procurementBottomVO2.setL3CegShortCnName("元器件");
        procurementBottomVO2.setL4CegCode("4122A");
        procurementBottomVO2.setL4CegShortCnName("元器组价件");
        procurementBottomVO2.setCategoryCode("5122D");
        procurementBottomVO2.setCategoryCnName("拼板");
        procurementBottomVO2.setStartPeriod(202305L);
        procurementBottomVO2.setEndPeriod(202308L);

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);

        List<ProcurementBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ProcurementBottomVO procurementBottom = new ProcurementBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202304L);
        procurementBottom.setEndPeriod(202309L);

        // 系统数据2
        ProcurementBottomVO procurementBottom2 = new ProcurementBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ProcurementBottomVO procurementBottom3 = new ProcurementBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202302L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        when(procurementReviewDao.findBottomReviewVOList(any())).thenReturn(bottomReviewList);


        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveProModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void saveProModify13T() throws Exception {
        List<ProcurementBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ProcurementBottomVO procurementBottomVO = new ProcurementBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setItemCnName("结构件");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setL3CegCode("3122A");
        procurementBottomVO.setL3CegShortCnName("元器件");
        procurementBottomVO.setL4CegCode("4122A");
        procurementBottomVO.setL4CegShortCnName("元器组价件");
        procurementBottomVO.setCategoryCode("5122D");
        procurementBottomVO.setCategoryCnName("拼板");
        procurementBottomVO.setStartPeriod(202305L);
        procurementBottomVO.setEndPeriod(202308L);
        procurementBottomVO.setModifyReason("修改理由为空");
        // 入参2
        ProcurementBottomVO procurementBottomVO2 = new ProcurementBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("MODIFY");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156A");
        procurementBottomVO2.setItemCnName("结构件2");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setL3CegCode("3122A");
        procurementBottomVO2.setL3CegShortCnName("元器件");
        procurementBottomVO2.setL4CegCode("4122A");
        procurementBottomVO2.setL4CegShortCnName("元器组价件");
        procurementBottomVO2.setCategoryCode("5122D");
        procurementBottomVO2.setCategoryCnName("拼板");
        procurementBottomVO2.setStartPeriod(202305L);
        procurementBottomVO2.setEndPeriod(202308L);
        procurementBottomVO2.setModifyReason("修改理由为空");

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);

        List<ProcurementBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ProcurementBottomVO procurementBottom = new ProcurementBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202304L);
        procurementBottom.setEndPeriod(202309L);

        // 系统数据2
        ProcurementBottomVO procurementBottom2 = new ProcurementBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ProcurementBottomVO procurementBottom3 = new ProcurementBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202302L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        when(procurementReviewDao.findBottomReviewVOList(any())).thenReturn(bottomReviewList);


        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveProModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    // 校验入参同一规格品起始终止时间是否重叠
    @Test
    public void saveProModify14T() throws Exception {
        List<ProcurementBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ProcurementBottomVO procurementBottomVO = new ProcurementBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setItemCnName("结构件");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setL3CegCode("3122A");
        procurementBottomVO.setL3CegShortCnName("元器件");
        procurementBottomVO.setL4CegCode("4122A");
        procurementBottomVO.setL4CegShortCnName("元器组价件");
        procurementBottomVO.setCategoryCode("5122D");
        procurementBottomVO.setCategoryCnName("拼板");
        procurementBottomVO.setStartPeriod(202305L);
        procurementBottomVO.setEndPeriod(202308L);
        procurementBottomVO.setModifyReason("修改理由为空");
        procurementBottomVO.setImpactQty("15");
        // 入参2
        ProcurementBottomVO procurementBottomVO2 = new ProcurementBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("MODIFY");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156D");
        procurementBottomVO2.setItemCnName("结构件2");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setL3CegCode("3122A");
        procurementBottomVO2.setL3CegShortCnName("元器件");
        procurementBottomVO2.setL4CegCode("4122A");
        procurementBottomVO2.setL4CegShortCnName("元器组价件");
        procurementBottomVO2.setCategoryCode("5122D");
        procurementBottomVO2.setCategoryCnName("拼板");
        procurementBottomVO2.setStartPeriod(202306L);
        procurementBottomVO2.setEndPeriod(202309L);
        procurementBottomVO2.setModifyReason("修改理由为空");
        procurementBottomVO2.setImpactQty("15");

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);

        List<ProcurementBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ProcurementBottomVO procurementBottom = new ProcurementBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202304L);
        procurementBottom.setEndPeriod(202309L);

        // 系统数据2
        ProcurementBottomVO procurementBottom2 = new ProcurementBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ProcurementBottomVO procurementBottom3 = new ProcurementBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202302L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        when(procurementReviewDao.findBottomReviewVOList(any())).thenReturn(bottomReviewList);


        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveProModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    // 校验入参数据和系统数据是否重复
    @Test
    public void saveProModify15T() throws Exception {
        List<ProcurementBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ProcurementBottomVO procurementBottomVO = new ProcurementBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setItemCnName("结构件");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setL3CegCode("3122A");
        procurementBottomVO.setL3CegShortCnName("元器件");
        procurementBottomVO.setL4CegCode("4122A");
        procurementBottomVO.setL4CegShortCnName("元器组价件");
        procurementBottomVO.setCategoryCode("5122D");
        procurementBottomVO.setCategoryCnName("拼板");
        procurementBottomVO.setStartPeriod(202305L);
        procurementBottomVO.setEndPeriod(202308L);
        procurementBottomVO.setModifyReason("修改理由为空");
        procurementBottomVO.setImpactQty("15");
        // 入参2
        ProcurementBottomVO procurementBottomVO2 = new ProcurementBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("INSERT");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156A");
        procurementBottomVO2.setItemCnName("结构件2");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setL3CegCode("3122A");
        procurementBottomVO2.setL3CegShortCnName("元器件");
        procurementBottomVO2.setL4CegCode("4122A");
        procurementBottomVO2.setL4CegShortCnName("元器组价件");
        procurementBottomVO2.setCategoryCode("5122D");
        procurementBottomVO2.setCategoryCnName("拼板");
        procurementBottomVO2.setStartPeriod(202302L);
        procurementBottomVO2.setEndPeriod(202303L);
        procurementBottomVO2.setModifyReason("修改理由为空");
        procurementBottomVO2.setImpactQty("15");

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);

        List<ProcurementBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ProcurementBottomVO procurementBottom = new ProcurementBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202304L);
        procurementBottom.setEndPeriod(202309L);

        // 系统数据2
        ProcurementBottomVO procurementBottom2 = new ProcurementBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ProcurementBottomVO procurementBottom3 = new ProcurementBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202302L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        when(procurementReviewDao.findBottomReviewVOList(any())).thenReturn(bottomReviewList);


        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveProModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    // 校验入参规格品和系统相同规格品的起始终止时间是否重叠
    @Test
    public void saveProModify16T() throws Exception {
        List<ProcurementBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ProcurementBottomVO procurementBottomVO = new ProcurementBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("INSERT");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setItemCnName("结构件");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setL3CegCode("3122A");
        procurementBottomVO.setL3CegShortCnName("元器件");
        procurementBottomVO.setL4CegCode("4122A");
        procurementBottomVO.setL4CegShortCnName("元器组价件");
        procurementBottomVO.setCategoryCode("5122D");
        procurementBottomVO.setCategoryCnName("拼板");
        procurementBottomVO.setStartPeriod(202305L);
        procurementBottomVO.setEndPeriod(202308L);
        procurementBottomVO.setModifyReason("修改理由为空");
        procurementBottomVO.setImpactQty("15");
        // 入参2
        ProcurementBottomVO procurementBottomVO2 = new ProcurementBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("INSERT");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156A");
        procurementBottomVO2.setItemCnName("结构件2");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setL3CegCode("3122A");
        procurementBottomVO2.setL3CegShortCnName("元器件");
        procurementBottomVO2.setL4CegCode("4122A");
        procurementBottomVO2.setL4CegShortCnName("元器组价件");
        procurementBottomVO2.setCategoryCode("5122D");
        procurementBottomVO2.setCategoryCnName("拼板");
        procurementBottomVO2.setStartPeriod(202302L);
        procurementBottomVO2.setEndPeriod(202304L);
        procurementBottomVO2.setModifyReason("修改理由为空");
        procurementBottomVO2.setImpactQty("15");

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);

        List<ProcurementBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ProcurementBottomVO procurementBottom = new ProcurementBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202306L);
        procurementBottom.setEndPeriod(202307L);

        // 系统数据2
        ProcurementBottomVO procurementBottom2 = new ProcurementBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ProcurementBottomVO procurementBottom3 = new ProcurementBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202301L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        when(procurementReviewDao.findBottomReviewVOList(any())).thenReturn(bottomReviewList);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveProModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    // 测试版本
    @Test
    public void saveProModify17T() throws Exception {
        List<ProcurementBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ProcurementBottomVO procurementBottomVO = new ProcurementBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setItemCnName("结构件");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setL3CegCode("3122A");
        procurementBottomVO.setL3CegShortCnName("元器件");
        procurementBottomVO.setL4CegCode("4122A");
        procurementBottomVO.setL4CegShortCnName("元器组价件");
        procurementBottomVO.setCategoryCode("5122D");
        procurementBottomVO.setCategoryCnName("拼板");
        procurementBottomVO.setStartPeriod(202305L);
        procurementBottomVO.setEndPeriod(202308L);
        procurementBottomVO.setModifyReason("修改理由为空");
        procurementBottomVO.setImpactQty("15");
        // 入参2
        ProcurementBottomVO procurementBottomVO2 = new ProcurementBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("MODIFY");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156A");
        procurementBottomVO2.setItemCnName("结构件2");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setL3CegCode("3122A");
        procurementBottomVO2.setL3CegShortCnName("元器件");
        procurementBottomVO2.setL4CegCode("4122A");
        procurementBottomVO2.setL4CegShortCnName("元器组价件");
        procurementBottomVO2.setCategoryCode("5122D");
        procurementBottomVO2.setCategoryCnName("拼板");
        procurementBottomVO2.setStartPeriod(202305L);
        procurementBottomVO2.setEndPeriod(202308L);
        procurementBottomVO2.setModifyReason("修改理由为空");
        procurementBottomVO2.setImpactQty("15");

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);

        List<ProcurementBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ProcurementBottomVO procurementBottom = new ProcurementBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202304L);
        procurementBottom.setEndPeriod(202309L);

        // 系统数据2
        ProcurementBottomVO procurementBottom2 = new ProcurementBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ProcurementBottomVO procurementBottom3 = new ProcurementBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202302L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        List<DmFocVersionInfoDTO> versionMap = new ArrayList<>();
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersion("202401");
        versionMap.add(dmFocVersionInfoDTO);

        when(procurementReviewDao.findBottomReviewVOList(any())).thenReturn(bottomReviewList);
        when(dmFocVersionDao.findDmFocVersionDTOList(any())).thenReturn(versionMap);


        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveProModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    // 测试版本
    @Test
    public void saveProModify18T() throws Exception {
        List<ProcurementBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ProcurementBottomVO procurementBottomVO = new ProcurementBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setItemCnName("结构件");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setL3CegCode("3122A");
        procurementBottomVO.setL3CegShortCnName("元器件");
        procurementBottomVO.setL4CegCode("4122A");
        procurementBottomVO.setL4CegShortCnName("元器组价件");
        procurementBottomVO.setCategoryCode("5122D");
        procurementBottomVO.setCategoryCnName("拼板");
        procurementBottomVO.setStartPeriod(202305L);
        procurementBottomVO.setEndPeriod(202308L);
        procurementBottomVO.setModifyReason("修改理由为空");
        procurementBottomVO.setImpactQty("15");
        // 入参2
        ProcurementBottomVO procurementBottomVO2 = new ProcurementBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("MODIFY");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156A");
        procurementBottomVO2.setItemCnName("结构件2");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setL3CegCode("3122A");
        procurementBottomVO2.setL3CegShortCnName("元器件");
        procurementBottomVO2.setL4CegCode("4122A");
        procurementBottomVO2.setL4CegShortCnName("元器组价件");
        procurementBottomVO2.setCategoryCode("5122D");
        procurementBottomVO2.setCategoryCnName("拼板");
        procurementBottomVO2.setStartPeriod(202305L);
        procurementBottomVO2.setEndPeriod(202308L);
        procurementBottomVO2.setModifyReason("修改理由为空");
        procurementBottomVO2.setImpactQty("15");

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);

        List<ProcurementBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ProcurementBottomVO procurementBottom = new ProcurementBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202304L);
        procurementBottom.setEndPeriod(202309L);

        // 系统数据2
        ProcurementBottomVO procurementBottom2 = new ProcurementBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ProcurementBottomVO procurementBottom3 = new ProcurementBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202302L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        List<DmFocVersionInfoDTO> versionMap = new ArrayList<>();
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersion("202401-001");
        versionMap.add(dmFocVersionInfoDTO);

        when(procurementReviewDao.findBottomReviewVOList(any())).thenReturn(bottomReviewList);
        when(dmFocVersionDao.findDmFocVersionDTOList(any())).thenReturn(versionMap);


        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveProModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    // 测试版本
    @Test
    public void saveProModify19T() throws Exception {
        List<ProcurementBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ProcurementBottomVO procurementBottomVO = new ProcurementBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setItemCnName("结构件");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setL3CegCode("3122A");
        procurementBottomVO.setL3CegShortCnName("元器件");
        procurementBottomVO.setL4CegCode("4122A");
        procurementBottomVO.setL4CegShortCnName("元器组价件");
        procurementBottomVO.setCategoryCode("5122D");
        procurementBottomVO.setCategoryCnName("拼板");
        procurementBottomVO.setStartPeriod(202305L);
        procurementBottomVO.setEndPeriod(202308L);
        procurementBottomVO.setModifyReason("修改理由为空");
        procurementBottomVO.setImpactQty("15");
        // 入参2
        ProcurementBottomVO procurementBottomVO2 = new ProcurementBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("MODIFY");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156A");
        procurementBottomVO2.setItemCnName("结构件2");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setL3CegCode("3122A");
        procurementBottomVO2.setL3CegShortCnName("元器件");
        procurementBottomVO2.setL4CegCode("4122A");
        procurementBottomVO2.setL4CegShortCnName("元器组价件");
        procurementBottomVO2.setCategoryCode("5122D");
        procurementBottomVO2.setCategoryCnName("拼板");
        procurementBottomVO2.setStartPeriod(202305L);
        procurementBottomVO2.setEndPeriod(202308L);
        procurementBottomVO2.setModifyReason("修改理由为空");
        procurementBottomVO2.setImpactQty("15");

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);

        List<ProcurementBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ProcurementBottomVO procurementBottom = new ProcurementBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202304L);
        procurementBottom.setEndPeriod(202309L);

        // 系统数据2
        ProcurementBottomVO procurementBottom2 = new ProcurementBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ProcurementBottomVO procurementBottom3 = new ProcurementBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202302L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        List<DmFocVersionInfoDTO> versionMap = new ArrayList<>();
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersion("202401-Auto");
        versionMap.add(dmFocVersionInfoDTO);

        when(procurementReviewDao.findBottomReviewVOList(any())).thenReturn(bottomReviewList);
        when(dmFocVersionDao.findDmFocVersionDTOList(any())).thenReturn(versionMap);

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveProModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    // 测试版本
    @Test
    public void saveProModify20T() throws Exception {
        List<ProcurementBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ProcurementBottomVO procurementBottomVO = new ProcurementBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setItemCnName("结构件");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setL3CegCode("3122A");
        procurementBottomVO.setL3CegShortCnName("元器件");
        procurementBottomVO.setL4CegCode("4122A");
        procurementBottomVO.setL4CegShortCnName("元器组价件");
        procurementBottomVO.setCategoryCode("5122D");
        procurementBottomVO.setCategoryCnName("拼板");
        procurementBottomVO.setStartPeriod(202305L);
        procurementBottomVO.setEndPeriod(202308L);
        procurementBottomVO.setModifyReason("修改理由为空");
        procurementBottomVO.setImpactQty("15");
        // 入参2
        ProcurementBottomVO procurementBottomVO2 = new ProcurementBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("MODIFY");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156A");
        procurementBottomVO2.setItemCnName("结构件2");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setL3CegCode("3122A");
        procurementBottomVO2.setL3CegShortCnName("元器件");
        procurementBottomVO2.setL4CegCode("4122A");
        procurementBottomVO2.setL4CegShortCnName("元器组价件");
        procurementBottomVO2.setCategoryCode("5122D");
        procurementBottomVO2.setCategoryCnName("拼板");
        procurementBottomVO2.setStartPeriod(202305L);
        procurementBottomVO2.setEndPeriod(202308L);
        procurementBottomVO2.setModifyReason("修改理由为空");
        procurementBottomVO2.setImpactQty("15");

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);

        List<ProcurementBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ProcurementBottomVO procurementBottom = new ProcurementBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202304L);
        procurementBottom.setEndPeriod(202309L);

        // 系统数据2
        ProcurementBottomVO procurementBottom2 = new ProcurementBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ProcurementBottomVO procurementBottom3 = new ProcurementBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202302L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        List<DmFocVersionInfoDTO> versionMap = new ArrayList<>();
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersion("202401-12");
        versionMap.add(dmFocVersionInfoDTO);

        when(procurementReviewDao.findBottomReviewVOList(any())).thenReturn(bottomReviewList);
        when(dmFocVersionDao.findDmFocVersionDTOList(any())).thenReturn(versionMap);

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveProModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    // 测试版本
    @Test
    public void saveProModify21T() throws Exception {
        List<ProcurementBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ProcurementBottomVO procurementBottomVO = new ProcurementBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setItemCnName("结构件");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setL3CegCode("3122A");
        procurementBottomVO.setL3CegShortCnName("元器件");
        procurementBottomVO.setL4CegCode("4122A");
        procurementBottomVO.setL4CegShortCnName("元器组价件");
        procurementBottomVO.setCategoryCode("5122D");
        procurementBottomVO.setCategoryCnName("拼板");
        procurementBottomVO.setStartPeriod(202305L);
        procurementBottomVO.setEndPeriod(202308L);
        procurementBottomVO.setModifyReason("修改理由为空");
        procurementBottomVO.setImpactQty("15");
        // 入参2
        ProcurementBottomVO procurementBottomVO2 = new ProcurementBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("MODIFY");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156A");
        procurementBottomVO2.setItemCnName("结构件2");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setL3CegCode("3122A");
        procurementBottomVO2.setL3CegShortCnName("元器件");
        procurementBottomVO2.setL4CegCode("4122A");
        procurementBottomVO2.setL4CegShortCnName("元器组价件");
        procurementBottomVO2.setCategoryCode("5122D");
        procurementBottomVO2.setCategoryCnName("拼板");
        procurementBottomVO2.setStartPeriod(202305L);
        procurementBottomVO2.setEndPeriod(202308L);
        procurementBottomVO2.setModifyReason("修改理由为空");
        procurementBottomVO2.setImpactQty("15");

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);

        List<ProcurementBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ProcurementBottomVO procurementBottom = new ProcurementBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202304L);
        procurementBottom.setEndPeriod(202309L);

        // 系统数据2
        ProcurementBottomVO procurementBottom2 = new ProcurementBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ProcurementBottomVO procurementBottom3 = new ProcurementBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202302L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        List<DmFocVersionInfoDTO> versionMap = new ArrayList<>();
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(52L);
        versionMap.add(dmFocVersionInfoDTO);

        when(procurementReviewDao.findBottomReviewVOList(any())).thenReturn(bottomReviewList);
        when(dmFocVersionDao.findDmFocVersionDTOList(any())).thenReturn(versionMap);

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveProModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void revokeProModify() {
        List<ProcurementBottomVO> procurementVOList = new ArrayList<>();
        ProcurementBottomVO procurementBottomVO = new ProcurementBottomVO();
        procurementVOList.add(procurementBottomVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.revokeProModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void revokeProModify1T() {
        List<ProcurementBottomVO> procurementVOList = new ArrayList<>();
        ProcurementBottomVO procurementBottomVO = new ProcurementBottomVO();
        procurementBottomVO.setVersionId(11L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementVOList.add(procurementBottomVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.revokeProModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void revokeProModify2T() {
        List<ProcurementBottomVO> procurementVOList = new ArrayList<>();
        ProcurementBottomVO procurementBottomVO = new ProcurementBottomVO();
        procurementBottomVO.setVersionId(11L);
        procurementBottomVO.setModifyType("REVOKE");
        procurementBottomVO.setDataType("REVIEW_PURC_C");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1165F");
        procurementBottomVO.setStartPeriod(202306L);
        procurementBottomVO.setEndPeriod(202311L);
        procurementVOList.add(procurementBottomVO);

        List<ProcurementBottomVO> baseReviewVOList = new ArrayList<>();
        baseReviewVOList.add(procurementBottomVO);
        when(procurementReviewDao.findBottomReviewVOList(any())).thenReturn(baseReviewVOList);

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.revokeProModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void revokeProModify3T() {
        List<ProcurementBottomVO> procurementVOList = new ArrayList<>();
        ProcurementBottomVO procurementBottomVO = new ProcurementBottomVO();
        procurementBottomVO.setVersionId(11L);
        procurementBottomVO.setModifyType("REVOKE");
        procurementBottomVO.setDataType("REVIEW_PURC_C");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1165F");
        procurementBottomVO.setStartPeriod(202306L);
        procurementBottomVO.setEndPeriod(202311L);
        procurementVOList.add(procurementBottomVO);

        List<ProcurementBottomVO> baseReviewVOList = new ArrayList<>();
        ProcurementBottomVO procurementBottomVO2 = new ProcurementBottomVO();
        procurementBottomVO2.setItemCode("1165");
        procurementBottomVO2.setStartPeriod(202306L);
        procurementBottomVO2.setEndPeriod(202311L);

        ProcurementBottomVO procurementBottomVO3 = new ProcurementBottomVO();
        procurementBottomVO3.setItemCode("1165F");
        procurementBottomVO3.setStartPeriod(202307L);
        procurementBottomVO3.setEndPeriod(202311L);

        ProcurementBottomVO procurementBottomVO4 = new ProcurementBottomVO();
        procurementBottomVO4.setItemCode("1165F");
        procurementBottomVO4.setStartPeriod(202306L);
        procurementBottomVO4.setEndPeriod(202312L);

        baseReviewVOList.add(procurementBottomVO2);
        baseReviewVOList.add(procurementBottomVO3);
        baseReviewVOList.add(procurementBottomVO4);
        when(procurementReviewDao.findBottomReviewVOList(any())).thenReturn(baseReviewVOList);

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.revokeProModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void exportProcurement() throws Exception {
        ProcurementBottomVO procurementBottomVO=new ProcurementBottomVO();
        HttpServletResponse response=null;
        procurementBottomVO.setModelType("M");
        procurementBottomVO.setCostType("P");
        procurementBottomVO.setCaliberFlag("C");
        procurementBottomVO.setIndustryOrg("ICT");
        DmFoiImpExpRecordVO expRecordVO=new DmFoiImpExpRecordVO();
        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);
        ResultDataVO manufactureDropDown = configBottomReviewService.exportProcurement(procurementBottomVO,response);
        Assert.assertNotNull(manufactureDropDown);
    }

    @Test
    public void exportProcurement1T() throws Exception {
        ProcurementBottomVO procurementBottomVO=new ProcurementBottomVO();
        HttpServletResponse response=null;
        procurementBottomVO.setModelType("M");
        procurementBottomVO.setCaliberFlag("C");
        procurementBottomVO.setCostType("P");
        procurementBottomVO.setIndustryOrg("ICT");
        List<Map> bottomReviewList = new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("modify_reason_i","hahahaha");
        map.put("version","202306-001");
        map.put("caliberFlag","C");
        bottomReviewList.add(map);

        DmFoiImpExpRecordVO expRecordVO=new DmFoiImpExpRecordVO();

        when(procurementReviewDao.findBottomReviewList(any())).thenReturn(bottomReviewList);
        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);

        ResultDataVO manufactureDropDown = configBottomReviewService.exportProcurement(procurementBottomVO,response);
        Assert.assertNotNull(manufactureDropDown);
    }

    @Test
    public void exportProcurement2T() throws Exception {
        ProcurementBottomVO procurementBottomVO=new ProcurementBottomVO();
        HttpServletResponse response=null;
        procurementBottomVO.setModelType("R");
        procurementBottomVO.setCaliberFlag("C");
        procurementBottomVO.setCostType("P");
        procurementBottomVO.setIndustryOrg("ICT");

        List<Map> bottomReviewList = new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("version","202306-001");
        map.put("caliberFlag","C");
        map.put("modify_type", "INSERT");

        Map<String,String> map2 = new HashMap<>();
        map2.put("version","202306-001");
        map2.put("caliberFlag","C");
        map2.put("modify_type", "MODIFY");

        Map<String,String> map3 = new HashMap<>();
        map3.put("version","202306-001");
        map3.put("caliberFlag","C");
        map3.put("modify_type", "REVOKE");
        bottomReviewList.add(map);
        bottomReviewList.add(map2);
        bottomReviewList.add(map3);

        DmFoiImpExpRecordVO expRecordVO=new DmFoiImpExpRecordVO();
        when(procurementReviewDao.findBottomReviewList(any())).thenReturn(bottomReviewList);
        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);

        ResultDataVO manufactureDropDown = configBottomReviewService.exportProcurement(procurementBottomVO,response);
        Assert.assertNotNull(manufactureDropDown);
    }

    @Test
    public void getManufactureDropDown() throws CommonApplicationException {
        ManufactureBottomVO manufactureBottomVO=new ManufactureBottomVO();
        ResultDataVO manufactureDropDown = configBottomReviewService.getManufactureDropDown(manufactureBottomVO);
        Assert.assertNotNull(manufactureDropDown);
    }

    @Test
    public void getMftFormDropDown() throws CommonApplicationException {
        ManufactureBottomVO manufactureBottomVO=new ManufactureBottomVO();
        manufactureBottomVO.setShippingObjectCode("111R");
        ResultDataVO manufactureDropDown = configBottomReviewService.getMftFormDropDown(manufactureBottomVO);
        Assert.assertNotNull(manufactureDropDown);
    }

    @Test
    public void getMftFormDropDown1T() throws CommonApplicationException {
        ManufactureBottomVO manufactureBottomVO=new ManufactureBottomVO();
        manufactureBottomVO.setShippingObjectCode("111R");
        manufactureBottomVO.setItemCode("222T");

        ResultDataVO manufactureDropDown = configBottomReviewService.getMftFormDropDown(manufactureBottomVO);
        Assert.assertNotNull(manufactureDropDown);
    }

    @Test
    public void getMftFormDropDown2T() throws CommonApplicationException {
        ManufactureBottomVO manufactureBottomVO=new ManufactureBottomVO();
        manufactureBottomVO.setShippingObjectCode("111R");
        manufactureBottomVO.setItemCode("222T");
        manufactureBottomVO.setStartPeriod(202301L);
        manufactureBottomVO.setEndPeriod(202309L);

        ResultDataVO manufactureDropDown = configBottomReviewService.getMftFormDropDown(manufactureBottomVO);
        Assert.assertNotNull(manufactureDropDown);
    }

    @Test
    public void saveManufactureModify() throws Exception {
        List<ManufactureBottomVO> procurementVOList = new ArrayList<>();
        ManufactureBottomVO procurementBottomVO = new ManufactureBottomVO();
        procurementVOList.add(procurementBottomVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveManufactureModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void saveManufactureModifyOne() throws Exception {
        List<ManufactureBottomVO> procurementVOList = new ArrayList<>();
        ManufactureBottomVO procurementBottomVO = new ManufactureBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("REVOKE");
        procurementVOList.add(procurementBottomVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveManufactureModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void saveManufactureModifyTwo() throws Exception {
        List<ManufactureBottomVO> procurementVOList = new ArrayList<>();
        ManufactureBottomVO procurementBottomVO = new ManufactureBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("C");
        procurementVOList.add(procurementBottomVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveManufactureModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void saveManufactureModifyThree() throws Exception {
        List<ManufactureBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ManufactureBottomVO procurementBottomVO = new ManufactureBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setShippingObjectCode("3122A");
        // 入参2
        ManufactureBottomVO procurementBottomVO2 = new ManufactureBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("MODIFY");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156A");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setShippingObjectCode("3122A");
        // 入参3
        ManufactureBottomVO procurementBottomVO3 = new ManufactureBottomVO();
        procurementBottomVO3.setVersionId(155L);
        procurementBottomVO3.setModifyType("INSERT");
        procurementBottomVO3.setCaliberFlag("R");
        procurementBottomVO3.setItemCode("1156C");
        procurementBottomVO3.setItemCode("1156C");
        procurementBottomVO3.setStartPeriod(202304L);
        procurementBottomVO3.setEndPeriod(202309L);
        procurementBottomVO3.setShippingObjectCode("3122A");

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);
        procurementVOList.add(procurementBottomVO3);

        List<ManufactureBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ManufactureBottomVO procurementBottom = new ManufactureBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202304L);
        procurementBottom.setEndPeriod(202309L);

        // 系统数据2
        ManufactureBottomVO procurementBottom2 = new ManufactureBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ManufactureBottomVO procurementBottom3 = new ManufactureBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202302L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        when(manufactureReviewDao.findMadeReviewVOList(any())).thenReturn(bottomReviewList);

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveManufactureModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void saveManufactureModify4T() throws Exception {
        List<ManufactureBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ManufactureBottomVO procurementBottomVO = new ManufactureBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setShippingObjectCode("3122A");
        procurementBottomVO.setShippingObjectCnName("元器件");

        // 入参2
        ManufactureBottomVO procurementBottomVO2 = new ManufactureBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("MODIFY");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156A");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setShippingObjectCode("3122A");
        procurementBottomVO2.setShippingObjectCnName("元器件");
        // 入参3
        ManufactureBottomVO procurementBottomVO3 = new ManufactureBottomVO();
        procurementBottomVO3.setVersionId(155L);
        procurementBottomVO3.setModifyType("INSERT");
        procurementBottomVO3.setCaliberFlag("R");
        procurementBottomVO3.setItemCode("1156C");
        procurementBottomVO3.setItemCode("1156C");
        procurementBottomVO3.setStartPeriod(202304L);
        procurementBottomVO3.setEndPeriod(202309L);
        procurementBottomVO3.setShippingObjectCode("3122A");
        procurementBottomVO3.setShippingObjectCnName("元器件");

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);
        procurementVOList.add(procurementBottomVO3);

        List<ManufactureBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ManufactureBottomVO procurementBottom = new ManufactureBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202304L);
        procurementBottom.setEndPeriod(202309L);

        // 系统数据2
        ManufactureBottomVO procurementBottom2 = new ManufactureBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ManufactureBottomVO procurementBottom3 = new ManufactureBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202302L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        when(manufactureReviewDao.findMadeReviewVOList(any())).thenReturn(bottomReviewList);

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveManufactureModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void saveManufactureModify5T() throws Exception {
        List<ManufactureBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ManufactureBottomVO procurementBottomVO = new ManufactureBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setShippingObjectCode("3122A");
        procurementBottomVO.setShippingObjectCnName("元器件");
        procurementBottomVO.setManufactureObjectCode("4122A");
        // 入参2
        ManufactureBottomVO procurementBottomVO2 = new ManufactureBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("MODIFY");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156A");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setShippingObjectCode("3122A");
        procurementBottomVO2.setShippingObjectCnName("元器件");
        procurementBottomVO2.setManufactureObjectCode("4122A");
        // 入参3
        ManufactureBottomVO procurementBottomVO3 = new ManufactureBottomVO();
        procurementBottomVO3.setVersionId(155L);
        procurementBottomVO3.setModifyType("INSERT");
        procurementBottomVO3.setCaliberFlag("R");
        procurementBottomVO3.setItemCode("1156C");
        procurementBottomVO3.setItemCode("1156C");
        procurementBottomVO3.setStartPeriod(202304L);
        procurementBottomVO3.setEndPeriod(202309L);
        procurementBottomVO3.setShippingObjectCode("3122A");
        procurementBottomVO3.setShippingObjectCnName("元器件");
        procurementBottomVO3.setManufactureObjectCode("4122A");

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);
        procurementVOList.add(procurementBottomVO3);

        List<ManufactureBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ManufactureBottomVO procurementBottom = new ManufactureBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202304L);
        procurementBottom.setEndPeriod(202309L);

        // 系统数据2
        ManufactureBottomVO procurementBottom2 = new ManufactureBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ManufactureBottomVO procurementBottom3 = new ManufactureBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202302L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        when(manufactureReviewDao.findMadeReviewVOList(any())).thenReturn(bottomReviewList);

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveManufactureModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void saveManufactureModify6T() throws Exception {
        List<ManufactureBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ManufactureBottomVO procurementBottomVO = new ManufactureBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setShippingObjectCode("3122A");
        procurementBottomVO.setShippingObjectCnName("元器件");
        procurementBottomVO.setManufactureObjectCode("4122A");
        procurementBottomVO.setManufactureObjectCnName("元器组价件");
        // 入参2
        ManufactureBottomVO procurementBottomVO2 = new ManufactureBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("MODIFY");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156A");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setShippingObjectCode("3122A");
        procurementBottomVO2.setShippingObjectCnName("元器件");
        procurementBottomVO2.setManufactureObjectCode("4122A");
        procurementBottomVO2.setManufactureObjectCnName("元器组价件");
        // 入参3
        ManufactureBottomVO procurementBottomVO3 = new ManufactureBottomVO();
        procurementBottomVO3.setVersionId(155L);
        procurementBottomVO3.setModifyType("INSERT");
        procurementBottomVO3.setCaliberFlag("R");
        procurementBottomVO3.setItemCode("1156C");
        procurementBottomVO3.setStartPeriod(202304L);
        procurementBottomVO3.setEndPeriod(202309L);
        procurementBottomVO3.setShippingObjectCode("3122A");
        procurementBottomVO3.setShippingObjectCnName("元器件");
        procurementBottomVO3.setManufactureObjectCode("4122A");
        procurementBottomVO3.setManufactureObjectCnName("元器组价件");

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);
        procurementVOList.add(procurementBottomVO3);

        List<ManufactureBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ManufactureBottomVO procurementBottom = new ManufactureBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202304L);
        procurementBottom.setEndPeriod(202309L);

        // 系统数据2
        ManufactureBottomVO procurementBottom2 = new ManufactureBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ManufactureBottomVO procurementBottom3 = new ManufactureBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202302L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        when(manufactureReviewDao.findMadeReviewVOList(any())).thenReturn(bottomReviewList);

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveManufactureModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void saveManufactureModify7T() throws Exception {
        List<ManufactureBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ManufactureBottomVO procurementBottomVO = new ManufactureBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setShippingObjectCode("3122A");
        procurementBottomVO.setShippingObjectCnName("元器件");
        procurementBottomVO.setManufactureObjectCode("4122A");
        procurementBottomVO.setManufactureObjectCnName("元器组价件");
        // 入参2
        ManufactureBottomVO procurementBottomVO2 = new ManufactureBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("MODIFY");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156A");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setShippingObjectCode("3122A");
        procurementBottomVO2.setShippingObjectCnName("元器件");
        procurementBottomVO2.setManufactureObjectCode("4122A");
        procurementBottomVO2.setManufactureObjectCnName("元器组价件");
        // 入参3
        ManufactureBottomVO procurementBottomVO3 = new ManufactureBottomVO();
        procurementBottomVO3.setVersionId(155L);
        procurementBottomVO3.setModifyType("INSERT");
        procurementBottomVO3.setCaliberFlag("R");
        procurementBottomVO3.setItemCode("1156C");
        procurementBottomVO3.setStartPeriod(202304L);
        procurementBottomVO3.setEndPeriod(202309L);
        procurementBottomVO3.setShippingObjectCode("3122A");
        procurementBottomVO3.setShippingObjectCnName("元器件");
        procurementBottomVO3.setManufactureObjectCode("4122A");
        procurementBottomVO3.setManufactureObjectCnName("元器组价件");

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);
        procurementVOList.add(procurementBottomVO3);

        List<ManufactureBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ManufactureBottomVO procurementBottom = new ManufactureBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202304L);
        procurementBottom.setEndPeriod(202309L);

        // 系统数据2
        ManufactureBottomVO procurementBottom2 = new ManufactureBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ManufactureBottomVO procurementBottom3 = new ManufactureBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202302L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        when(manufactureReviewDao.findMadeReviewVOList(any())).thenReturn(bottomReviewList);

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveManufactureModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void saveManufactureModify9T() throws Exception {
        List<ManufactureBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ManufactureBottomVO procurementBottomVO = new ManufactureBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setShippingObjectCode("3122A");
        procurementBottomVO.setShippingObjectCnName("元器件");
        procurementBottomVO.setManufactureObjectCode("4122A");
        procurementBottomVO.setManufactureObjectCnName("元器组价件");
        // 入参2
        ManufactureBottomVO procurementBottomVO2 = new ManufactureBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("MODIFY");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156A");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setShippingObjectCode("3122A");
        procurementBottomVO2.setShippingObjectCnName("元器件");
        procurementBottomVO2.setManufactureObjectCode("4122A");
        procurementBottomVO2.setManufactureObjectCnName("元器组价件");
        // 入参3
        ManufactureBottomVO procurementBottomVO3 = new ManufactureBottomVO();
        procurementBottomVO3.setVersionId(155L);
        procurementBottomVO3.setModifyType("INSERT");
        procurementBottomVO3.setCaliberFlag("R");
        procurementBottomVO3.setItemCode("1156C");
        procurementBottomVO3.setItemCode("1156C");
        procurementBottomVO3.setStartPeriod(202304L);
        procurementBottomVO3.setEndPeriod(202309L);
        procurementBottomVO3.setShippingObjectCode("3122A");
        procurementBottomVO3.setShippingObjectCnName("元器件");
        procurementBottomVO3.setManufactureObjectCode("4122A");
        procurementBottomVO3.setManufactureObjectCnName("元器组价件");

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);
        procurementVOList.add(procurementBottomVO3);

        List<ManufactureBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ManufactureBottomVO procurementBottom = new ManufactureBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202304L);
        procurementBottom.setEndPeriod(202309L);

        // 系统数据2
        ManufactureBottomVO procurementBottom2 = new ManufactureBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ManufactureBottomVO procurementBottom3 = new ManufactureBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202302L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        when(manufactureReviewDao.findMadeReviewVOList(any())).thenReturn(bottomReviewList);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveManufactureModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void saveManufactureModify10T() throws Exception {
        List<ManufactureBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ManufactureBottomVO procurementBottomVO = new ManufactureBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setItemCnName("结构件");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setShippingObjectCode("3122A");
        procurementBottomVO.setShippingObjectCnName("元器件");
        procurementBottomVO.setManufactureObjectCode("4122A");
        procurementBottomVO.setManufactureObjectCnName("元器组价件");
        // 入参2
        ManufactureBottomVO procurementBottomVO2 = new ManufactureBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("MODIFY");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156A");
        procurementBottomVO2.setItemCnName("结构件2");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setShippingObjectCode("3122A");
        procurementBottomVO2.setShippingObjectCnName("元器件");
        procurementBottomVO2.setManufactureObjectCode("4122A");
        procurementBottomVO2.setManufactureObjectCnName("元器组价件");
        // 入参3
        ManufactureBottomVO procurementBottomVO3 = new ManufactureBottomVO();
        procurementBottomVO3.setVersionId(155L);
        procurementBottomVO3.setModifyType("INSERT");
        procurementBottomVO3.setCaliberFlag("R");
        procurementBottomVO3.setItemCode("1156C");
        procurementBottomVO3.setItemCnName("结构件3");
        procurementBottomVO3.setItemCode("1156C");
        procurementBottomVO3.setStartPeriod(202304L);
        procurementBottomVO3.setEndPeriod(202309L);
        procurementBottomVO3.setShippingObjectCode("3122A");
        procurementBottomVO3.setShippingObjectCnName("元器件");
        procurementBottomVO3.setManufactureObjectCode("4122A");
        procurementBottomVO3.setManufactureObjectCnName("元器组价件");

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);
        procurementVOList.add(procurementBottomVO3);

        List<ManufactureBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ManufactureBottomVO procurementBottom = new ManufactureBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202304L);
        procurementBottom.setEndPeriod(202309L);

        // 系统数据2
        ManufactureBottomVO procurementBottom2 = new ManufactureBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ManufactureBottomVO procurementBottom3 = new ManufactureBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202302L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        when(manufactureReviewDao.findMadeReviewVOList(any())).thenReturn(bottomReviewList);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveManufactureModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void saveManufactureModify11T() throws Exception {
        List<ManufactureBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ManufactureBottomVO procurementBottomVO = new ManufactureBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setItemCnName("结构件");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setShippingObjectCode("3122A");
        procurementBottomVO.setShippingObjectCnName("元器件");
        procurementBottomVO.setManufactureObjectCode("4122A");
        procurementBottomVO.setManufactureObjectCnName("元器组价件");
        procurementBottomVO.setStartPeriod(202305L);
        // 入参2
        ManufactureBottomVO procurementBottomVO2 = new ManufactureBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("MODIFY");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156A");
        procurementBottomVO2.setItemCnName("结构件2");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setShippingObjectCode("3122A");
        procurementBottomVO2.setShippingObjectCnName("元器件");
        procurementBottomVO2.setManufactureObjectCode("4122A");
        procurementBottomVO2.setManufactureObjectCnName("元器组价件");
        procurementBottomVO2.setStartPeriod(202305L);
        // 入参3
        ManufactureBottomVO procurementBottomVO3 = new ManufactureBottomVO();
        procurementBottomVO3.setVersionId(155L);
        procurementBottomVO3.setModifyType("INSERT");
        procurementBottomVO3.setCaliberFlag("R");
        procurementBottomVO3.setItemCode("1156C");
        procurementBottomVO3.setItemCnName("结构件3");
        procurementBottomVO3.setItemCode("1156C");
        procurementBottomVO3.setStartPeriod(202304L);
        procurementBottomVO3.setEndPeriod(202309L);
        procurementBottomVO3.setShippingObjectCode("3122A");
        procurementBottomVO3.setShippingObjectCnName("元器件");
        procurementBottomVO3.setManufactureObjectCode("4122A");
        procurementBottomVO3.setManufactureObjectCnName("元器组价件");
        procurementBottomVO3.setStartPeriod(202305L);

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);
        procurementVOList.add(procurementBottomVO3);

        List<ManufactureBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ManufactureBottomVO procurementBottom = new ManufactureBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202304L);
        procurementBottom.setEndPeriod(202309L);

        // 系统数据2
        ManufactureBottomVO procurementBottom2 = new ManufactureBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ManufactureBottomVO procurementBottom3 = new ManufactureBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202302L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);
        when(manufactureReviewDao.findMadeReviewVOList(any())).thenReturn(bottomReviewList);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveManufactureModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    // 校验结束期
    @Test
    public void saveManufactureModify12T() throws Exception {
        List<ManufactureBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ManufactureBottomVO procurementBottomVO = new ManufactureBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setItemCnName("结构件");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setShippingObjectCode("3122A");
        procurementBottomVO.setShippingObjectCnName("元器件");
        procurementBottomVO.setManufactureObjectCode("4122A");
        procurementBottomVO.setManufactureObjectCnName("元器组价件");
        procurementBottomVO.setStartPeriod(202305L);
        procurementBottomVO.setEndPeriod(202308L);
        // 入参2
        ManufactureBottomVO procurementBottomVO2 = new ManufactureBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("MODIFY");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156A");
        procurementBottomVO2.setItemCnName("结构件2");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setShippingObjectCode("3122A");
        procurementBottomVO2.setShippingObjectCnName("元器件");
        procurementBottomVO2.setManufactureObjectCode("4122A");
        procurementBottomVO2.setManufactureObjectCnName("元器组价件");
        procurementBottomVO2.setStartPeriod(202305L);
        procurementBottomVO2.setEndPeriod(202308L);

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);

        List<ManufactureBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ManufactureBottomVO procurementBottom = new ManufactureBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202304L);
        procurementBottom.setEndPeriod(202309L);

        // 系统数据2
        ManufactureBottomVO procurementBottom2 = new ManufactureBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ManufactureBottomVO procurementBottom3 = new ManufactureBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202302L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        when(manufactureReviewDao.findMadeReviewVOList(any())).thenReturn(bottomReviewList);

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveManufactureModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void saveManufactureModify13T() throws Exception {
        List<ManufactureBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ManufactureBottomVO procurementBottomVO = new ManufactureBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setItemCnName("结构件");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setShippingObjectCode("3122A");
        procurementBottomVO.setShippingObjectCnName("元器件");
        procurementBottomVO.setManufactureObjectCode("4122A");
        procurementBottomVO.setManufactureObjectCnName("元器组价件");
        procurementBottomVO.setStartPeriod(202305L);
        procurementBottomVO.setEndPeriod(202308L);
        procurementBottomVO.setModifyReason("修改理由为空");
        // 入参2
        ManufactureBottomVO procurementBottomVO2 = new ManufactureBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("MODIFY");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156A");
        procurementBottomVO2.setItemCnName("结构件2");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setShippingObjectCode("3122A");
        procurementBottomVO2.setShippingObjectCnName("元器件");
        procurementBottomVO2.setManufactureObjectCode("4122A");
        procurementBottomVO2.setManufactureObjectCnName("元器组价件");
        procurementBottomVO2.setStartPeriod(202305L);
        procurementBottomVO2.setEndPeriod(202308L);
        procurementBottomVO2.setModifyReason("修改理由为空");

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);

        List<ManufactureBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ManufactureBottomVO procurementBottom = new ManufactureBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202304L);
        procurementBottom.setEndPeriod(202309L);

        // 系统数据2
        ManufactureBottomVO procurementBottom2 = new ManufactureBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ManufactureBottomVO procurementBottom3 = new ManufactureBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202302L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        when(manufactureReviewDao.findMadeReviewVOList(any())).thenReturn(bottomReviewList);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveManufactureModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void saveManufactureModify133T() throws Exception {
        List<ManufactureBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ManufactureBottomVO procurementBottomVO = new ManufactureBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setItemCnName("结构件");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setShippingObjectCode("3122A");
        procurementBottomVO.setShippingObjectCnName("元器件");
        procurementBottomVO.setManufactureObjectCode("4122A");
        procurementBottomVO.setManufactureObjectCnName("元器组价件");
        procurementBottomVO.setStartPeriod(202305L);
        procurementBottomVO.setEndPeriod(202308L);
        procurementBottomVO.setModifyReason("修改理由为空");
        procurementBottomVO.setImpactQty("3");
        // 入参2
        ManufactureBottomVO procurementBottomVO2 = new ManufactureBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("MODIFY");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156A");
        procurementBottomVO2.setItemCnName("结构件2");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setShippingObjectCode("3122A");
        procurementBottomVO2.setShippingObjectCnName("元器件");
        procurementBottomVO2.setManufactureObjectCode("4122A");
        procurementBottomVO2.setManufactureObjectCnName("元器组价件");
        procurementBottomVO2.setStartPeriod(202305L);
        procurementBottomVO2.setEndPeriod(202308L);
        procurementBottomVO2.setModifyReason("修改理由为空");
        procurementBottomVO2.setImpactQty("3");

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);

        List<ManufactureBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ManufactureBottomVO procurementBottom = new ManufactureBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202304L);
        procurementBottom.setEndPeriod(202309L);

        // 系统数据2
        ManufactureBottomVO procurementBottom2 = new ManufactureBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ManufactureBottomVO procurementBottom3 = new ManufactureBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202302L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        when(manufactureReviewDao.findMadeReviewVOList(any())).thenReturn(bottomReviewList);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveManufactureModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    // 校验入参同一规格品起始终止时间是否重叠
    @Test
    public void saveManufactureModify14T() throws Exception {
        List<ManufactureBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ManufactureBottomVO procurementBottomVO = new ManufactureBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setItemCnName("结构件");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setShippingObjectCode("3122A");
        procurementBottomVO.setShippingObjectCnName("元器件");
        procurementBottomVO.setManufactureObjectCode("4122A");
        procurementBottomVO.setManufactureObjectCnName("元器组价件");
        procurementBottomVO.setStartPeriod(202305L);
        procurementBottomVO.setEndPeriod(202308L);
        procurementBottomVO.setModifyReason("修改理由为空");
        procurementBottomVO.setImpactQty("15");
        // 入参2
        ManufactureBottomVO procurementBottomVO2 = new ManufactureBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("MODIFY");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156D");
        procurementBottomVO2.setItemCnName("结构件2");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setShippingObjectCode("3122A");
        procurementBottomVO2.setShippingObjectCnName("元器件");
        procurementBottomVO2.setManufactureObjectCode("4122A");
        procurementBottomVO2.setManufactureObjectCnName("元器组价件");
        procurementBottomVO2.setStartPeriod(202306L);
        procurementBottomVO2.setEndPeriod(202309L);
        procurementBottomVO2.setModifyReason("修改理由为空");
        procurementBottomVO2.setImpactQty("15");

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);

        List<ManufactureBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ManufactureBottomVO procurementBottom = new ManufactureBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202304L);
        procurementBottom.setEndPeriod(202309L);

        // 系统数据2
        ManufactureBottomVO procurementBottom2 = new ManufactureBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ManufactureBottomVO procurementBottom3 = new ManufactureBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202302L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        when(manufactureReviewDao.findMadeReviewVOList(any())).thenReturn(bottomReviewList);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveManufactureModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    // 校验入参数据和系统数据是否重复
    @Test
    public void saveManufactureModify15T() throws Exception {
        List<ManufactureBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ManufactureBottomVO procurementBottomVO = new ManufactureBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setItemCnName("结构件");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setShippingObjectCode("3122A");
        procurementBottomVO.setShippingObjectCnName("元器件");
        procurementBottomVO.setManufactureObjectCode("4122A");
        procurementBottomVO.setManufactureObjectCnName("元器组价件");
        procurementBottomVO.setStartPeriod(202305L);
        procurementBottomVO.setEndPeriod(202308L);
        procurementBottomVO.setModifyReason("修改理由为空");
        procurementBottomVO.setImpactQty("15");
        // 入参2
        ManufactureBottomVO procurementBottomVO2 = new ManufactureBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("INSERT");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156A");
        procurementBottomVO2.setItemCnName("结构件2");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setShippingObjectCode("3122A");
        procurementBottomVO2.setShippingObjectCnName("元器件");
        procurementBottomVO2.setManufactureObjectCode("4122A");
        procurementBottomVO2.setManufactureObjectCnName("元器组价件");
        procurementBottomVO2.setStartPeriod(202302L);
        procurementBottomVO2.setEndPeriod(202303L);
        procurementBottomVO2.setModifyReason("修改理由为空");
        procurementBottomVO2.setImpactQty("15");

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);

        List<ManufactureBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ManufactureBottomVO procurementBottom = new ManufactureBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202304L);
        procurementBottom.setEndPeriod(202309L);

        // 系统数据2
        ManufactureBottomVO procurementBottom2 = new ManufactureBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ManufactureBottomVO procurementBottom3 = new ManufactureBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202302L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        when(manufactureReviewDao.findMadeReviewVOList(any())).thenReturn(bottomReviewList);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveManufactureModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    // 校验入参规格品和系统相同规格品的起始终止时间是否重叠
    @Test
    public void saveManufactureModify16T() throws Exception {
        List<ManufactureBottomVO> procurementVOList = new ArrayList<>();
        // 入参1
        ManufactureBottomVO procurementBottomVO = new ManufactureBottomVO();
        procurementBottomVO.setVersionId(155L);
        procurementBottomVO.setModifyType("INSERT");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1156D");
        procurementBottomVO.setItemCnName("结构件");
        procurementBottomVO.setOldItemCode("1156D");
        procurementBottomVO.setOldStartPeriod(202304L);
        procurementBottomVO.setOldEndPeriod(202309L);
        procurementBottomVO.setShippingObjectCode("3122A");
        procurementBottomVO.setShippingObjectCnName("元器件");
        procurementBottomVO.setManufactureObjectCode("4122A");
        procurementBottomVO.setManufactureObjectCnName("元器组价件");
        procurementBottomVO.setStartPeriod(202305L);
        procurementBottomVO.setEndPeriod(202308L);
        procurementBottomVO.setModifyReason("修改理由为空");
        procurementBottomVO.setImpactQty("15");
        // 入参2
        ManufactureBottomVO procurementBottomVO2 = new ManufactureBottomVO();
        procurementBottomVO2.setVersionId(155L);
        procurementBottomVO2.setModifyType("INSERT");
        procurementBottomVO2.setCaliberFlag("R");
        procurementBottomVO2.setItemCode("1156A");
        procurementBottomVO2.setItemCnName("结构件2");
        procurementBottomVO2.setOldItemCode("1156A");
        procurementBottomVO2.setOldStartPeriod(202304L);
        procurementBottomVO2.setOldEndPeriod(202309L);
        procurementBottomVO2.setShippingObjectCode("3122A");
        procurementBottomVO2.setShippingObjectCnName("元器件");
        procurementBottomVO2.setManufactureObjectCode("4122A");
        procurementBottomVO2.setManufactureObjectCnName("元器组价件");

        procurementBottomVO2.setStartPeriod(202302L);
        procurementBottomVO2.setEndPeriod(202304L);
        procurementBottomVO2.setModifyReason("修改理由为空");
        procurementBottomVO2.setImpactQty("15");

        procurementVOList.add(procurementBottomVO);
        procurementVOList.add(procurementBottomVO2);

        List<ManufactureBottomVO> bottomReviewList = new ArrayList<>();
        // 系统数据1
        ManufactureBottomVO procurementBottom = new ManufactureBottomVO();
        procurementBottom.setModifyType("MODIFY");
        procurementBottom.setCaliberFlag("R");
        procurementBottom.setItemCode("1156D");
        procurementBottom.setStartPeriod(202306L);
        procurementBottom.setEndPeriod(202307L);

        // 系统数据2
        ManufactureBottomVO procurementBottom2 = new ManufactureBottomVO();
        procurementBottom2.setModifyType("MODIFY");
        procurementBottom2.setCaliberFlag("R");
        procurementBottom2.setItemCode("1156D");
        procurementBottom2.setStartPeriod(202302L);
        procurementBottom2.setEndPeriod(202303L);

        // 系统数据3
        ManufactureBottomVO procurementBottom3 = new ManufactureBottomVO();
        procurementBottom3.setModifyType("MODIFY");
        procurementBottom3.setCaliberFlag("R");
        procurementBottom3.setItemCode("1156A");
        procurementBottom3.setStartPeriod(202301L);
        procurementBottom3.setEndPeriod(202303L);

        bottomReviewList.add(procurementBottom);
        bottomReviewList.add(procurementBottom2);
        bottomReviewList.add(procurementBottom3);

        when(manufactureReviewDao.findMadeReviewVOList(any())).thenReturn(bottomReviewList);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.saveManufactureModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void revokeManufactureModify() {
        List<ManufactureBottomVO> procurementVOList = new ArrayList<>();
        ManufactureBottomVO procurementBottomVO = new ManufactureBottomVO();
        procurementVOList.add(procurementBottomVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.revokeManufactureModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void revokeManufactureModify1T() {
        List<ManufactureBottomVO> procurementVOList = new ArrayList<>();
        ManufactureBottomVO procurementBottomVO = new ManufactureBottomVO();
        procurementBottomVO.setVersionId(11L);
        procurementBottomVO.setModifyType("MODIFY");
        procurementVOList.add(procurementBottomVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.revokeManufactureModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void revokeManufactureModify2T() {
        List<ManufactureBottomVO> procurementVOList = new ArrayList<>();
        ManufactureBottomVO procurementBottomVO = new ManufactureBottomVO();
        procurementBottomVO.setVersionId(11L);
        procurementBottomVO.setModifyType("REVOKE");
        procurementBottomVO.setDataType("REVIEW_PURC_C");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1165F");
        procurementBottomVO.setStartPeriod(202306L);
        procurementBottomVO.setEndPeriod(202311L);
        procurementVOList.add(procurementBottomVO);

        List<ManufactureBottomVO> baseReviewVOList = new ArrayList<>();
        baseReviewVOList.add(procurementBottomVO);
        when(manufactureReviewDao.findMadeReviewVOList(any())).thenReturn(baseReviewVOList);

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.revokeManufactureModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void revokeManufactureModify3T() {
        List<ManufactureBottomVO> procurementVOList = new ArrayList<>();
        ManufactureBottomVO procurementBottomVO = new ManufactureBottomVO();
        procurementBottomVO.setVersionId(11L);
        procurementBottomVO.setModifyType("REVOKE");
        procurementBottomVO.setDataType("REVIEW_PURC_C");
        procurementBottomVO.setCaliberFlag("R");
        procurementBottomVO.setItemCode("1165F");
        procurementBottomVO.setStartPeriod(202306L);
        procurementBottomVO.setEndPeriod(202311L);
        procurementVOList.add(procurementBottomVO);

        List<ManufactureBottomVO> baseReviewVOList = new ArrayList<>();
        ManufactureBottomVO procurementBottomVO2 = new ManufactureBottomVO();
        procurementBottomVO2.setItemCode("1165");
        procurementBottomVO2.setStartPeriod(202306L);
        procurementBottomVO2.setEndPeriod(202311L);

        ManufactureBottomVO procurementBottomVO3 = new ManufactureBottomVO();
        procurementBottomVO3.setItemCode("1165F");
        procurementBottomVO3.setStartPeriod(202307L);
        procurementBottomVO3.setEndPeriod(202311L);

        ManufactureBottomVO procurementBottomVO4 = new ManufactureBottomVO();
        procurementBottomVO4.setItemCode("1165F");
        procurementBottomVO4.setStartPeriod(202306L);
        procurementBottomVO4.setEndPeriod(202312L);

        baseReviewVOList.add(procurementBottomVO2);
        baseReviewVOList.add(procurementBottomVO3);
        baseReviewVOList.add(procurementBottomVO4);
        when(manufactureReviewDao.findMadeReviewVOList(any())).thenReturn(baseReviewVOList);

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configBottomReviewService.revokeManufactureModify(procurementVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void exportManufacture() throws CommonApplicationException, IOException {
        ManufactureBottomVO procurementBottomVO=new ManufactureBottomVO();
        HttpServletResponse response=null;
        procurementBottomVO.setModelType("M");
        procurementBottomVO.setCostType("P");
        procurementBottomVO.setCaliberFlag("C");
        procurementBottomVO.setIndustryOrg("ICT");
        DmFoiImpExpRecordVO expRecordVO=new DmFoiImpExpRecordVO();
        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);
        ResultDataVO manufactureDropDown = configBottomReviewService.exportManufacture(procurementBottomVO,response);
        Assert.assertNotNull(manufactureDropDown);
    }

    @Test
    public void exportManufacture1T() throws Exception {
        ManufactureBottomVO procurementBottomVO=new ManufactureBottomVO();
        HttpServletResponse response=null;
        procurementBottomVO.setModelType("M");
        procurementBottomVO.setCaliberFlag("C");
        procurementBottomVO.setCostType("P");
        procurementBottomVO.setIndustryOrg("ICT");

        List<Map> bottomReviewList = new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("modify_reason_i","hahahaha");
        map.put("version","202306-001");
        map.put("caliberFlag","C");
        bottomReviewList.add(map);

        DmFoiImpExpRecordVO expRecordVO=new DmFoiImpExpRecordVO();

        when(manufactureReviewDao.findBaseReviewList(any())).thenReturn(bottomReviewList);
        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);

        ResultDataVO manufactureDropDown = configBottomReviewService.exportManufacture(procurementBottomVO,response);
        Assert.assertNotNull(manufactureDropDown);
    }

    @Test
    public void exportManufacture2T() throws Exception {
        ManufactureBottomVO procurementBottomVO=new ManufactureBottomVO();
        HttpServletResponse response=null;
        procurementBottomVO.setModelType("R");
        procurementBottomVO.setCaliberFlag("C");
        procurementBottomVO.setCostType("P");
        procurementBottomVO.setIndustryOrg("ICT");

        List<Map> bottomReviewList = new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("version","202306-001");
        map.put("caliberFlag","C");
        map.put("modify_type", "INSERT");

        Map<String,String> map2 = new HashMap<>();
        map2.put("version","202306-001");
        map2.put("caliberFlag","C");
        map2.put("modify_type", "MODIFY");

        Map<String,String> map3 = new HashMap<>();
        map3.put("version","202306-001");
        map3.put("caliberFlag","C");
        map3.put("modify_type", "REVOKE");
        bottomReviewList.add(map);
        bottomReviewList.add(map2);
        bottomReviewList.add(map3);

        DmFoiImpExpRecordVO expRecordVO=new DmFoiImpExpRecordVO();
        when(manufactureReviewDao.findBaseReviewList(any())).thenReturn(bottomReviewList);
        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);

        ResultDataVO manufactureDropDown = configBottomReviewService.exportManufacture(procurementBottomVO,response);
        Assert.assertNotNull(manufactureDropDown);
    }

    @Test
    public void testGetProModifyListByPage() throws Exception {
        ProcurementBottomVO procurementBottomVO = new ProcurementBottomVO();
        Assert.assertThrows(CommonApplicationException.class,
                () -> configBottomReviewService.getProModifyListByPage(procurementBottomVO));

        procurementBottomVO.setVersionId(111L);
        Assert.assertThrows(CommonApplicationException.class,
                () -> configBottomReviewService.getProModifyListByPage(procurementBottomVO));

        procurementBottomVO.setPageIndex(1);
        Assert.assertThrows(CommonApplicationException.class,
                () -> configBottomReviewService.getProModifyListByPage(procurementBottomVO));

        procurementBottomVO.setPageSize(5);
        PagedResult<ProcurementBottomVO> byPage = new PagedResult<>();
        when(procurementReviewDao.findByPage(any(), any())).thenReturn(byPage);
        ResultDataVO result = configBottomReviewService.getProModifyListByPage(procurementBottomVO);
        Assert.assertEquals(result.getCode(), "200");
    }

    @Test
    public void testGetMftModifyListByPage() throws Exception {
        ManufactureBottomVO manufactureBottomVO = new ManufactureBottomVO();
        Assert.assertThrows(CommonApplicationException.class,
                () -> configBottomReviewService.getMftModifyListByPage(manufactureBottomVO));
        manufactureBottomVO.setVersionId(111L);
        Assert.assertThrows(CommonApplicationException.class,
                () -> configBottomReviewService.getMftModifyListByPage(manufactureBottomVO));
        manufactureBottomVO.setPageIndex(1);
        Assert.assertThrows(CommonApplicationException.class,
                () -> configBottomReviewService.getMftModifyListByPage(manufactureBottomVO));
        manufactureBottomVO.setPageSize(5);
        PagedResult<ManufactureBottomVO> byPage = new PagedResult<>();
        when(manufactureReviewDao.findByPage(any(), any())).thenReturn(byPage);
        ResultDataVO result = configBottomReviewService.getMftModifyListByPage(manufactureBottomVO);
        Assert.assertEquals(result.getCode(), "200");
    }
}