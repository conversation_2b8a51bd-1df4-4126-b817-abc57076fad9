/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.dao;

import com.huawei.it.fcst.industry.pbi.vo.config.DmFcstTopSpartInfoDTO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import org.apache.ibatis.cursor.Cursor;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/3/15
 */
public interface IDmFcstTopSpartInfoDao {
    /**
     * 全品类清单分页查询
     *
     * @param vo 参数
     * @param paramPageVO 参数
     * @return PagedResult<DmFocTopItemInfoDTO>
     */
    PagedResult<DmFcstTopSpartInfoDTO> findTopSpartByPage(DmFcstTopSpartInfoDTO vo, PageVO paramPageVO);

    Cursor<Map> findTopSpartVOList(DmFcstTopSpartInfoDTO build);

    int findTopSpartAllCount(DmFcstTopSpartInfoDTO build);
}
