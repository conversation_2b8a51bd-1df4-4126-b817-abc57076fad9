/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.ciphertext;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

/**
 * VarifyTaskVOTest Class
 *
 * <AUTHOR>
 * @since 2023/11/3
 */
public class VarifyTaskVOTest extends BaseVOCoverUtilsTest<VarifyTaskVO> {
    @Override
    protected Class<VarifyTaskVO> getTClass() {
        return VarifyTaskVO.class;
    }

    @Test
    public void testMethod() {
        VarifyTaskVO dmFocActualCostVO = new VarifyTaskVO();
        dmFocActualCostVO.setPeriodId(2023L);
        dmFocActualCostVO.getPeriodId();
        dmFocActualCostVO.getLastUpdateDate();
        dmFocActualCostVO.setTaskType("type");
        dmFocActualCostVO.getTaskType();
        Assert.assertNotNull(dmFocActualCostVO);
    }
}