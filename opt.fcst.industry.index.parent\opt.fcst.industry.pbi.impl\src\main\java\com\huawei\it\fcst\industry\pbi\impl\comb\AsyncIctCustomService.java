/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.comb;

import com.huawei.it.fcst.industry.pbi.dao.IDmCustomDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstDimInfoDao;
import com.huawei.it.fcst.industry.pbi.enums.IndustryConstEnum;
import com.huawei.it.fcst.industry.pbi.utils.FcstIndustryUtil;
import com.huawei.it.fcst.industry.pbi.vo.comb.CombTransformVO;
import com.huawei.it.fcst.industry.pbi.vo.comb.CommonViewVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstDimInfoVO;
import com.huawei.it.fcst.util.ObjectCopyUtil;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Named;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * AsyncCustomService Class
 *
 * <AUTHOR>
 * @since 2023/11/3
 */
@Slf4j
@EnableAsync
@Named(value = "asyncIctCustomService")
public class AsyncIctCustomService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncIctCustomService.class);

    private static final String SUCCESS = "SUCCESS";

    private static final String SUCCESS_STATUS = "1";

    @Autowired
    private IDmCustomDao dmCustomDao;

    @Autowired
    private IDmFcstDimInfoDao dmFcstDimInfoDao;

    @Autowired
    private IctCustomCommonService ictCustomCommonService;

    @Async("asyncServiceExecutor")
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Future<Boolean> callCustomCombAnnual(CombTransformVO combTransformVO) throws CommonApplicationException {
        String annualFlag = dmCustomDao.getCombAnnual(combTransformVO);
        LOGGER.info("调用F_DM_FCST_CUS_ANNL函数:{}", annualFlag);
        if (!SUCCESS.equals(annualFlag) && !SUCCESS_STATUS.equals(annualFlag)) {
            throw new CommonApplicationException("调用F_DM_FCST_CUS_ANNL函数失败");
        }
        return new AsyncResult<>(Boolean.TRUE);
    }

    @Async("asyncServiceExecutor")
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Future<Boolean> callCustomCombMonth(CombTransformVO combTransformVO) throws CommonApplicationException {
        combTransformVO.setPageSymbol("MONTH");
        String monthFlag = dmCustomDao.getCombMonth(combTransformVO);
        LOGGER.info("调用f_fcst_ict_point_cus_mon_result_t函数:{}", monthFlag);
        if (!SUCCESS.equals(monthFlag) && !SUCCESS_STATUS.equals(monthFlag)) {
            throw new CommonApplicationException("调用f_fcst_ict_point_cus_mon_result_t函数失败");
        }
        return new AsyncResult<>(Boolean.TRUE);
    }

    @Async("asyncServiceExecutor")
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Future<Boolean> initPageCondition(List<DmFcstDimInfoVO> allDmCustomCombList, CommonViewVO commonVO, String pageFlag) throws InterruptedException {

        if (CollectionUtils.isNotEmpty(allDmCustomCombList)) {
            commonVO.setPageFlag(pageFlag);
            CommonViewVO commonViewVO = ObjectCopyUtil.copy(commonVO, CommonViewVO.class);
            FcstIndustryUtil.setSpecailCode(commonViewVO);
            if (IndustryConstEnum.PAGE_TYPE.ANNUAL.getValue().equals(commonViewVO.getPageSymbol())) {
                if ("N".equals(commonViewVO.getMainFlag())) {
                    commonViewVO.setMainFlag(null);
                }
            }
            Map<Long, List<DmFcstDimInfoVO>> allDmCustomCombMap = allDmCustomCombList.stream().collect(Collectors.groupingBy(DmFcstDimInfoVO::getCustomId));
            for (Map.Entry<Long, List<DmFcstDimInfoVO>> allDmCustomCombEntry : allDmCustomCombMap.entrySet()) {
                List<DmFcstDimInfoVO> dmCustomCombList = allDmCustomCombEntry.getValue();
                commonViewVO.setOverseaFlag(dmCustomCombList.get(0).getOverseaFlag());
                commonViewVO.setViewFlag(dmCustomCombList.get(0).getViewFlag());
                commonViewVO.setBgCode(dmCustomCombList.get(0).getBgCode());
                commonViewVO.setSoftwareMark(dmCustomCombList.get(0).getSoftwareMark());
                commonViewVO.setCustomId(dmCustomCombList.get(0).getCustomId());
                commonViewVO.setCostType(dmCustomCombList.get(0).getCostType());
                commonViewVO.setPageFlag(dmCustomCombList.get(0).getPageFlag());
                commonViewVO.setGranularityType(dmCustomCombList.get(0).getGranularityType());
                commonViewVO.setMainFlag(dmCustomCombList.get(0).getMainFlag());
                commonViewVO.setCodeAttributes(dmCustomCombList.get(0).getCodeAttributes());
                commonViewVO.setLv1Code(dmCustomCombList.get(0).getLv1Code());
                commonViewVO.setLv2Code(dmCustomCombList.get(0).getLv2Code());
                commonViewVO.setLv3Code(dmCustomCombList.get(0).getLv3Code());
                commonViewVO.setLv4Code(dmCustomCombList.get(0).getLv4Code());
                Map<String, List<DmFcstDimInfoVO>> groupLevelKey = dmCustomCombList.stream().collect(Collectors.groupingBy(DmFcstDimInfoVO::getGroupLevel));

                List<DmFcstDimInfoVO> allGroupCodeList = new ArrayList<>();
                for (Map.Entry<String, List<DmFcstDimInfoVO>> groupLevelEntry : groupLevelKey.entrySet()) {
                    String groupLevel = groupLevelEntry.getKey();
                    commonViewVO.setGroupLevel(groupLevel);
                    ictCustomCommonService.getDbListForAllPage(commonViewVO, allGroupCodeList);
                }
                // 如果组合的某些code不在维表中，需要设置失效
                List<DmFcstDimInfoVO> diffGroupCodeList = dmCustomCombList.stream().filter(custom -> !allGroupCodeList.stream().map(all -> {
                    return all.getConnectCode();
                }).collect(Collectors.toList()).contains(custom.getConnectCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(diffGroupCodeList)) {
                    // 更新custom_id整个失效
                    dmCustomDao.updateCustomEnableById(commonViewVO);
                    // 更新具体某个code失效
                    // 更新单个sub_enable_flag
                    dmCustomDao.updateCombSubEnableList(commonViewVO, diffGroupCodeList);
                }
            }
        }
        return new AsyncResult<>(Boolean.TRUE);
    }
}
