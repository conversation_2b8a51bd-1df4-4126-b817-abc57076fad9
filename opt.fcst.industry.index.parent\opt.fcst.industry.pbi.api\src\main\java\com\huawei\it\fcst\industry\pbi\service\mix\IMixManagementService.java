/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.service.mix;

import com.huawei.it.fcst.industry.pbi.vo.mix.MixSearchVO;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.concurrent.ExecutionException;

/**
 * IMixManagementService Class
 *
 * <AUTHOR>
 * @since 2024/7/5
 */
@Path("/mixManagement")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface IMixManagementService {

    /**
     * 勾稽管理-成本分布图
     *
     * @param mixSearchVO 参数
     * @return 结果
     */
    @Path("/distribute/Chart")
    @POST
    ResultDataVO distributeChart(MixSearchVO mixSearchVO) throws CommonApplicationException, ExecutionException, InterruptedException;

    /**
     * 勾稽管理-差异明细
     *
     * @param mixSearchVO 参数
     * @return 结果
     */
    @Path("/difference/chart")
    @POST
    ResultDataVO differenceChart(MixSearchVO mixSearchVO) throws CommonApplicationException, ExecutionException, InterruptedException;

    /**
     * 勾稽管理-单指数图
     *
     * @param mixSearchVO 参数
     * @return 结果
     */
    @Path("/current/priceIndex/chart")
    @POST
    ResultDataVO currentPriceIndexChart(MixSearchVO mixSearchVO) throws CommonApplicationException;

    /**
     * 勾稽管理-多指数图
     *
     * @param mixSearchVO 参数
     * @return 结果
     */
    @Path("/multi/priceIndex/chart")
    @POST
    ResultDataVO multiPriceIndexChart(MixSearchVO mixSearchVO) throws CommonApplicationException, ExecutionException, InterruptedException;

    /**
     * 勾稽管理-指数结果表
     *
     * @param mixSearchVO 参数
     * @return 结果
     */
    @Path("/priceIndex/child/list")
    @POST
    ResultDataVO priceIndexChildList(MixSearchVO mixSearchVO) throws CommonApplicationException, ExecutionException, InterruptedException;

    /**
     * ICT-勾稽管理-切换基期
     *
     * @param mixSearchVO 参数VO
     * @return ResultDataVO
     * @throws ApplicationException
     */
    @POST
    @Path("/price/index/switchBasePeriodId")
    ResultDataVO switchIndexBasePeriodId(MixSearchVO mixSearchVO) throws ApplicationException;

    /**
     * ICT-勾稽管理-多选spart时，插入维表，调用函数，返回spart的id
     *
     * @param mixSearchVO 参数VO
     * @return ResultDataVO
     * @throws ApplicationException
     */
    @POST
    @Path("/price/index/multiSpartCalculate")
    ResultDataVO multiSpartCalculate(MixSearchVO mixSearchVO) throws ApplicationException, InterruptedException;

    @POST
    @Path("/mixDetail")
    ResultDataVO mixDetail(@Context HttpServletResponse response, MixSearchVO mixSearchVO) throws Exception;


}
