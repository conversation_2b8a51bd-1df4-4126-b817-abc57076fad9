<?xml version="1.0" encoding="UTF-8" ?><!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IDmFocGuideInfoDao">
    <resultMap type="com.huawei.it.fcst.industry.index.vo.guide.DmFocGuideInfoVO" id="resultMap">
        <result property="id" column="id" />
        <result property="title" column="title" />
        <result property="guideUrl" column="guide_url" />
        <result property="createdBy" column="created_by"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <select id="findGuideInfoList" resultMap="resultMap">
        select id,title,guide_url,created_by,creation_date,last_updated_by,last_update_date,del_flag from
        fin_dm_opt_foi.dm_foc_guide_info_t
        where del_flag = 'N'
        <if test='_parameter.get("0").title != null and _parameter.get("0").title !=""'>
            AND title = #{0.title,jdbcType=VARCHAR}
        </if>
        order by creation_date desc
        LIMIT #{1.pageSize} OFFSET  #{1.startIndex}-1
    </select>

    <select id="findGuideInfoListCount" resultType="java.lang.Integer">
        select count(1) from
        fin_dm_opt_foi.dm_foc_guide_info_t
        where del_flag = 'N'
        <if test='_parameter.get("0").title != null and _parameter.get("0").title !=""'>
            AND title = #{0.title,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getGuideKey" resultType="java.lang.Long">
        select nextval('fin_dm_opt_foi.dm_foc_guide_info_s')
    </select>

    <delete id="deleteGuide">
        delete from fin_dm_opt_foi.dm_foc_guide_info_t
        where del_flag = 'N'
        <if test='id != null'>
            AND id = #{id}
        </if>
    </delete>

    <insert id="saveGuideInfo">
        insert into fin_dm_opt_foi.dm_foc_guide_info_t(id,title,guide_url,
        created_by,
        creation_date,
        last_updated_by,
        last_update_date,del_flag)
        values (#{id},#{title},#{guideUrl},#{createdBy},now(),#{lastUpdatedBy},now(),'N')
    </insert>

</mapper>
