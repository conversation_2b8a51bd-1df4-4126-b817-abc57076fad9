/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.common;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import org.apache.poi.ss.usermodel.CellType;

/**
 * LeafExcelTitleVO Class
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
@Getter
@Setter
@NoArgsConstructor
public class LeafExcelTitleVO extends AbstractExcelTitleVO {
    private CellType dataType ;

    /**
     * 如果dataType是CellType.NUMERIC ,这个字段用主于定义数字的格式
     */
    private String dataFormatStr ;

    private String dataKey;

    /**
     * 列编号，用于选择列时，区分不同的列，每个列编号 都不一样，前台选择那些列，通过这个字段来识别
     */
    private String columnCode;

    public CellType getDataType() {
        return dataType;
    }

    public void setDataType(CellType dataType) {
        this.dataType = dataType;
    }

    public String getDataFormatStr() {
        return dataFormatStr;
    }

    public void setDataFormatStr(String dataFormatStr) {
        this.dataFormatStr = dataFormatStr;
    }

    public String getDataKey() {
        return dataKey;
    }

    public void setDataKey(String dataKey) {
        this.dataKey = dataKey;
    }

    public String getColumnCode() {
        return columnCode;
    }

    public void setColumnCode(String columnCode) {
        this.columnCode = columnCode;
    }

    public LeafExcelTitleVO(String value, Integer width, Boolean isSelected, String dataKey, String columnCode,
                            CellType dataType, String dataFormatStr) {
        this.dataType = dataType;
        this.setValue(value);
        this.setWidth(width);
        this.setSelected(isSelected);
        this.dataKey = dataKey;
        this.columnCode = columnCode;
        this.dataFormatStr = dataFormatStr;
    }

    public LeafExcelTitleVO(String value, Integer width, Boolean isSelected, String dataKey, String columnCode,
                            CellType dataType, String dataFormatStr, Boolean isEditable ) {
        this.dataType = dataType;
        this.setValue(value);
        this.setWidth(width);
        this.setSelected(isSelected);
        this.dataKey = dataKey;
        this.columnCode = columnCode;
        this.dataFormatStr = dataFormatStr;
        this.setEditable(isEditable);
    }

    public LeafExcelTitleVO(String value, Integer width, Boolean isSelected, String dataKey, String columnCode,
                            CellType dataType) {
        this.dataType = dataType;
        this.setValue(value);
        this.setWidth(width);
        this.setSelected(isSelected);
        this.dataKey = dataKey;
        this.columnCode = columnCode;
    }

    public LeafExcelTitleVO(String value, Integer width, Boolean isSelected, String dataKey, String columnCode,
                            CellType dataType, Boolean isEditable) {
        this.dataType = dataType;
        this.setValue(value);
        this.setWidth(width);
        this.setSelected(isSelected);
        this.dataKey = dataKey;
        this.columnCode = columnCode;
        this.setEditable(isEditable);
    }

    public LeafExcelTitleVO(String value, Integer width, String dataKey , String columnCode, CellType dataType,
                            String dataFormatStr) {
        this.dataType = dataType;
        this.setValue(value);
        this.setWidth(width);
        this.setSelected(false);
        this.dataKey = dataKey;
        this.columnCode = columnCode;
        this.dataFormatStr = dataFormatStr;
    }

    public LeafExcelTitleVO(String value, Integer width, String dataKey , String columnCode, CellType dataType) {
        this.dataType = dataType;
        this.setValue(value);
        this.setWidth(width);
        this.setSelected(false);
        this.dataKey = dataKey;
        this.columnCode = columnCode;
    }
}
