/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.ciphertext;

import java.util.Date;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023年03月22日
 */
@Setter
@Getter
public class CipherTextDataVO extends LtsParmaVO{
    private String rmbCostAmt;
    private String rmbFactRateGcAmt;
    private String rmbItemUnitCostAmt;
    private Long periodId;
    private String primaryId ;
    private Date creationDate;
    private String sourceTableName;
    private String targetTableName;
    private List<Long> periodIds;

}
