/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.utils;

import com.huawei.it.fcst.config.ConfigUtil;
import com.huawei.it.soa.util.SoaAppTokenClientUtil;
import com.huawei.kmssdk.api.KmsClient;
import com.huawei.kmssdk.api.KmssdkClient;
import com.huawei.kmssdk.entity.ApiEntity;

/**
 * 单例模式
 */
public enum SdkClient {
    INSTANCE;
    private KmsClient kmsClient;
    private SdkClient(){
        kmsClient = new KmssdkClient();
        // 忽略https认证.isIgnoreCertificate(true)
        // 红版SOA认证模式
        kmsClient.init(ApiEntity.builder()
                .appId(SoaAppTokenClientUtil.getSoaAppId()) // appId - 必选
                .env(ConfigUtil.getInstance().getKmsEnv()) // 环境 测试：beta 生产：pro - 必选
                .netType(0) // 0-内网 1-外网 - 必选
                .credential(SoaAppTokenClientUtil.getSoaCredential()) // soa静态token - 必选
                .build());
    }

    /**
     * 获取单例对象
     * @return KmsClient
     */
    public KmsClient getInstance() {
        return kmsClient;
    }
}