/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.utils;

import java.util.Objects;

import com.huawei.his.mqs.client.consumer.Consumer;
import com.huawei.his.mqs.client.producer.Producer;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023年07月03日
 */
public enum UmpClient {
    INSTANCE;
    private Producer producer = null;
    private Consumer consumer = null;

    /**
     * ump 构造
     */
    private UmpClient(){
        if(Objects.isNull(producer)){
            producer = new Producer();
        }
        if(Objects.isNull(consumer)){
            consumer = new Consumer();
        }
    }

    /**
     * 获取单例对象
     * @return producer
     */
    public Producer getProducer() {
        return producer;
    }

    /**
     * 获取单例对象
     * @return Consumer
     */
    public Consumer getConsumer() {
        return consumer;
    }
}
