/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.service.common;

import com.huawei.it.fcst.industry.pbi.vo.common.DmFcstDataRefreshStatus;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import java.util.Map;

/**
 * IIctCommonService Interface
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Path("/ictCommon")
@Produces(MediaType.APPLICATION_JSON)
public interface IIctCommonService {

    /**
     * [根据数据类型查询版本ID]
     *
     * @param dataType 数据类型
     * @return Long
     */
    Long getVersionId(String dataType);

    /**
     * 查询切换基期实际数的开始和结束时间
     *
     */
    @POST
    @Path("/actual/periodId")
    ResultDataVO findActualPeriodId();

    @GET
    @Path("/refreshTime")
    ResultDataVO findRefreshTime();

    /**
     * 当前用户，当前角色的task_id查询
     *
     * @return 结果
     * @throws CommonApplicationException 异常
     */
    @POST
    @Path("/current/status")
    ResultDataVO currentDataRefreshStatus(DmFcstDataRefreshStatus dataRefreshStatus) throws CommonApplicationException;

    /**
     * 根据数据类型创建版本信息
     *
     * @param dataType
     * @return version id
     * @throws ApplicationException
     */
    Long createNewVersionInfo(String dataType);

    /**
     * 保存任务刷新状态
     *
     * @param taskFlag
     * @return DmFcstDataRefreshStatus
     * @throws ApplicationException
     */
    DmFcstDataRefreshStatus saveDataRefreshStatus(String taskFlag);

    /**
     * 获取模块类型和导入数量限制
     *
     * @param moduleType
     * @return Map
     * @throws ApplicationException
     */
    Map<String, Object> getHeaderMap(String moduleType) throws ApplicationException;

}