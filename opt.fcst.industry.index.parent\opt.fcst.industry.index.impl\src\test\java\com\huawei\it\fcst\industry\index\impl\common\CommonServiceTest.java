/*
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.common;

import com.alibaba.fastjson.JSONObject;
import com.huawei.it.fcst.industry.index.cache.IndustryCacheHandler;
import com.huawei.it.fcst.industry.index.constant.Constant;
import com.huawei.it.fcst.industry.index.dao.IDmFocMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocPageInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTopItemInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocVersionDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocViewInfoDao;
import com.huawei.it.fcst.industry.index.enums.IndustryIndexEnum;
import com.huawei.it.fcst.industry.index.impl.iauth.DataDimensionService;
import com.huawei.it.fcst.industry.index.utils.TestUtils;
import com.huawei.it.fcst.industry.index.utils.UserInfoUtils;
import com.huawei.it.fcst.industry.index.vo.common.DataPermissionsVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoDTO;
import com.huawei.it.fcst.industry.index.vo.month.DmFocPageInfoVO;
import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;
import com.huawei.it.fcst.industry.index.vo.view.ViewInfoVO;
import com.huawei.it.jalor5.core.base.KeyValuePairVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.lookup.LookupItemVO;
import com.huawei.it.jalor5.lookup.service.ILookupItemQueryService;
import com.huawei.it.jalor5.registry.service.IRegistryQueryService;
import com.huawei.it.jalor5.security.DimensionDefinitionVO;
import com.huawei.it.jalor5.security.ProgramItemVO;
import com.huawei.it.jalor5.security.ProgramVO;
import com.huawei.it.jalor5.security.RoleVO;
import com.huawei.it.jalor5.security.UserVO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.mockito.ArgumentMatchers.any;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.when;


/**
 * <AUTHOR>
 * @since 2023/4/18
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest( {UserInfoUtils.class})
public class CommonServiceTest {
    @InjectMocks
    private CommonService commonService;

    @Mock
    private IDmFocPageInfoDao dmFocPageInfoDao;

    @Mock
    private IDmFocTopItemInfoDao dmFocTopItemInfoDao;

    @Mock
    private IDmFocViewInfoDao dmFocViewInfoDao;

    @Mock
    private DataDimensionService dataDimensionService;

    @Mock
    private IDmFocVersionDao dmFocVersionDao;

    @Mock
    private IDmFocMonthCostIdxDao iDmFocMonthCostIdxDao;

    @Mock
    private IRegistryQueryService registryQueryService;

    @Mock
    private ILookupItemQueryService lookupItemQueryService;

    @Mock
    private IndustryCacheHandler industryCacheHandler;

    private JSONObject json;

    @Before
    public void before() throws Exception {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RoleVO role = new RoleVO();
        role.setRoleName("admin");
        role.setRoleId(1000);
        user.setCurrentRole(role);
        user.setCurrentPrograms(new ArrayList<>());
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);
        json = TestUtils.getTestArg("/com/huawei/it/fcst/industry/index/impl/common/CommonService/commonJson.json");
    }

    @Test
    public void getNavigationBarList() throws Exception {
        mockStatic(UserInfoUtils.class);
        String w3Accouont ="WWX1167755";
        when(UserInfoUtils.class, "getUserAccount").thenReturn(w3Accouont);
        UserVO currentUser = new UserVO();
        RoleVO currentRole = new RoleVO();
        currentRole.setRoleId(1213);
        currentRole.setRoleName("ICT_Analysis");
        currentUser.setCurrentRole(currentRole);
        when(UserInfoUtils.class, "getCurrentUser").thenReturn(currentUser);
        DmFocPageInfoVO  dmFocPageInfoVO = new DmFocPageInfoVO();
        ResultDataVO resultDataVO = commonService.getNavigationList(dmFocPageInfoVO);
        Assert.assertEquals("200", resultDataVO.getCode());
    }

    @Test
    public void getNavigationBarList1Test() throws Exception {
        mockStatic(UserInfoUtils.class);
        UserVO currentUser = new UserVO();
        RoleVO currentRole = new RoleVO();
        currentRole.setRoleId(1213);
        currentRole.setRoleName("ICT_Analysis");
        currentUser.setCurrentRole(currentRole);
        currentUser.setUserAccount("11111");
        when(UserInfoUtils.class, "getCurrentUser").thenReturn(currentUser);

        DmFocPageInfoVO  dmFocPageInfoVO = new DmFocPageInfoVO();
        dmFocPageInfoVO.setUserId("11111");
        ResultDataVO resultDataVO = commonService.getNavigationList(dmFocPageInfoVO);
        Assert.assertEquals("200", resultDataVO.getCode());
    }

    @Test
    public void getRefreshTime()  {
        ResultDataVO resultDataVO = commonService.findRefreshTime("ICT");
        Assert.assertEquals("200", resultDataVO.getCode());
    }

    @Test
    public void saveOrUpdatePageInfo() throws Exception {
        mockStatic(UserInfoUtils.class);
        UserVO currentUser = new UserVO();
        RoleVO currentRole = new RoleVO();
        currentRole.setRoleId(1213);
        currentRole.setRoleName("ICT_Analysis");
        currentUser.setCurrentRole(currentRole);
        when(UserInfoUtils.class, "getCurrentUser").thenReturn(currentUser);
        DmFocPageInfoVO dmFocPageInfoVO = new DmFocPageInfoVO();
        dmFocPageInfoVO.setSaveThreshold("{\"goodsCaliber\":{\"formFilters\":{\"periodId\":\"201901\",\"specDept\":\"12251\",\"module\":\"\",\"category\":\"\",\"item\":\"\"},\"chartFilters\":{\"hearChart\":{\"datazoom\":{\"start\":0,\"end\":47},\"datarangeselect\":null,\"pageOv\":{\"currentPage\":1,\"pageSize\":10}},\"priceChart\":{\"datazoom\":{\"start\":0,\"end\":47},\"showValue\":\"no\"},\"priceYearChart\":{\"showValue\":\"no\",\"legendSelected\":{\"2019\":true,\"2020\":true,\"2021\":true,\"2022\":true}},\"priceCEGYearChart\":{\"datazoom\":{\"start\":0,\"end\":47},\"dropSelect\":[\"12299\",\"12297\",\"12298\"],\"showValue\":\"no\",\"legendSelected\":{\"通信IC\":true,\"通用IC\":true,\"处理器IC\":true}},\"specificatChart\":{\"datazoom\":{\"start\":0,\"end\":100},\"showValue\":\"no\",\"legendSelected\":{}},\"singeCodePriceChart\":{\"datazoom\":{\"start\":0,\"end\":47},\"showValue\":\"no\"},\"itemCodePriceChart\":{\"datazoom\":{\"start\":0,\"end\":47},\"dropSelect\":null,\"showValue\":\"no\",\"legendSelected\":{}},\"categoryCodePriceChart\":{\"datazoom\":{\"start\":0,\"end\":47},\"dropSelect\":null,\"showValue\":\"no\",\"legendSelected\":{}}},\"collapseName\":[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\"]},\"orderCaliber\":null}");
        dmFocPageInfoVO.setPageName("月度分析1");
        List<DmFocPageInfoVO> pageInfoVOList = new ArrayList<>();
        DmFocPageInfoVO dmFocPageInfoVO1 = new DmFocPageInfoVO();
        dmFocPageInfoVO1.setPageName("月度分析2");
        pageInfoVOList.add(dmFocPageInfoVO1);
        PowerMockito.doReturn(pageInfoVOList).when(dmFocPageInfoDao).findPageInfoVOList(any());
        DmFocVersionInfoDTO dmFocVersionVO = new DmFocVersionInfoDTO();
        dmFocVersionVO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionVO).when(dmFocVersionDao).findVersionIdByDataType(IndustryIndexEnum.DataType.CATE.getValue(),"ICT");
        ResultDataVO resultDataVO = commonService.saveOrUpdatePageInfo(dmFocPageInfoVO);
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void saveOrUpdatePageInfo1Test() throws Exception {
        mockStatic(UserInfoUtils.class);
        UserVO currentUser = new UserVO();
        RoleVO currentRole = new RoleVO();
        currentRole.setRoleId(1213);
        currentRole.setRoleName("ICT_Analysis");
        currentUser.setCurrentRole(currentRole);
        when(UserInfoUtils.class, "getCurrentUser").thenReturn(currentUser);
        DmFocPageInfoVO dmFocPageInfoVO = new DmFocPageInfoVO();
        dmFocPageInfoVO.setSaveThreshold("{\"goodsCaliber\":{\"formFilters\":{\"periodId\":\"201901\",\"specDept\":\"12251\",\"module\":\"\",\"category\":\"\",\"item\":\"\"},\"chartFilters\":{\"hearChart\":{\"datazoom\":{\"start\":0,\"end\":47},\"datarangeselect\":null,\"pageOv\":{\"currentPage\":1,\"pageSize\":10}},\"priceChart\":{\"datazoom\":{\"start\":0,\"end\":47},\"showValue\":\"no\"},\"priceYearChart\":{\"showValue\":\"no\",\"legendSelected\":{\"2019\":true,\"2020\":true,\"2021\":true,\"2022\":true}},\"priceCEGYearChart\":{\"datazoom\":{\"start\":0,\"end\":47},\"dropSelect\":[\"12299\",\"12297\",\"12298\"],\"showValue\":\"no\",\"legendSelected\":{\"通信IC\":true,\"通用IC\":true,\"处理器IC\":true}},\"specificatChart\":{\"datazoom\":{\"start\":0,\"end\":100},\"showValue\":\"no\",\"legendSelected\":{}},\"singeCodePriceChart\":{\"datazoom\":{\"start\":0,\"end\":47},\"showValue\":\"no\"},\"itemCodePriceChart\":{\"datazoom\":{\"start\":0,\"end\":47},\"dropSelect\":null,\"showValue\":\"no\",\"legendSelected\":{}},\"categoryCodePriceChart\":{\"datazoom\":{\"start\":0,\"end\":47},\"dropSelect\":null,\"showValue\":\"no\",\"legendSelected\":{}}},\"collapseName\":[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\"]},\"orderCaliber\":null}");
        dmFocPageInfoVO.setPageName("月度分析月度分析月度分析月度分析月度分析月度分析月度分析");
        ResultDataVO resultDataVO = commonService.saveOrUpdatePageInfo(dmFocPageInfoVO);
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void saveOrUpdatePageInfo2Test() throws Exception {
        mockStatic(UserInfoUtils.class);
        UserVO currentUser = new UserVO();
        RoleVO currentRole = new RoleVO();
        currentRole.setRoleId(1213);
        currentRole.setRoleName("ICT_Analysis");
        currentUser.setCurrentRole(currentRole);
        when(UserInfoUtils.class, "getCurrentUser").thenReturn(currentUser);
        DmFocPageInfoVO dmFocPageInfoVO = new DmFocPageInfoVO();
        dmFocPageInfoVO.setSaveThreshold("{\"goodsCaliber\":{\"formFilters\":{\"periodId\":\"201901\",\"specDept\":\"12251\",\"module\":\"\",\"category\":\"\",\"item\":\"\"},\"chartFilters\":{\"hearChart\":{\"datazoom\":{\"start\":0,\"end\":47},\"datarangeselect\":null,\"pageOv\":{\"currentPage\":1,\"pageSize\":10}},\"priceChart\":{\"datazoom\":{\"start\":0,\"end\":47},\"showValue\":\"no\"},\"priceYearChart\":{\"showValue\":\"no\",\"legendSelected\":{\"2019\":true,\"2020\":true,\"2021\":true,\"2022\":true}},\"priceCEGYearChart\":{\"datazoom\":{\"start\":0,\"end\":47},\"dropSelect\":[\"12299\",\"12297\",\"12298\"],\"showValue\":\"no\",\"legendSelected\":{\"通信IC\":true,\"通用IC\":true,\"处理器IC\":true}},\"specificatChart\":{\"datazoom\":{\"start\":0,\"end\":100},\"showValue\":\"no\",\"legendSelected\":{}},\"singeCodePriceChart\":{\"datazoom\":{\"start\":0,\"end\":47},\"showValue\":\"no\"},\"itemCodePriceChart\":{\"datazoom\":{\"start\":0,\"end\":47},\"dropSelect\":null,\"showValue\":\"no\",\"legendSelected\":{}},\"categoryCodePriceChart\":{\"datazoom\":{\"start\":0,\"end\":47},\"dropSelect\":null,\"showValue\":\"no\",\"legendSelected\":{}}},\"collapseName\":[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\"]},\"orderCaliber\":null}");
        dmFocPageInfoVO.setPageName("月度分析月度");
        dmFocPageInfoVO.setPageId(12L);
        DmFocPageInfoVO pageInfoVO = new DmFocPageInfoVO();
        String w3Accouont ="WWX1167755";
        when(UserInfoUtils.class, "getUserAccount").thenReturn(w3Accouont);
        pageInfoVO.setUserId(UserInfoUtils.getUserAccount());
        PowerMockito.doReturn(pageInfoVO).when(dmFocPageInfoDao).findDmFocPageInfoVOById(any(),any());
        ResultDataVO resultDataVO = commonService.saveOrUpdatePageInfo(dmFocPageInfoVO);
        Assert.assertNotNull(resultDataVO);
    }
    @Test
    public void saveOrUpdatePageInfo3Test() throws Exception {
        mockStatic(UserInfoUtils.class);
        UserVO currentUser = new UserVO();
        RoleVO currentRole = new RoleVO();
        currentRole.setRoleId(1213);
        currentRole.setRoleName("ICT_Analysis");
        currentUser.setCurrentRole(currentRole);
        when(UserInfoUtils.class, "getCurrentUser").thenReturn(currentUser);
        DmFocPageInfoVO dmFocPageInfoVO = new DmFocPageInfoVO();
        dmFocPageInfoVO.setSaveThreshold("{\"goodsCaliber\":{\"formFilters\":{\"periodId\":\"201901\",\"specDept\":\"12251\",\"module\":\"\",\"category\":\"\",\"item\":\"\"},\"chartFilters\":{\"hearChart\":{\"datazoom\":{\"start\":0,\"end\":47},\"datarangeselect\":null,\"pageOv\":{\"currentPage\":1,\"pageSize\":10}},\"priceChart\":{\"datazoom\":{\"start\":0,\"end\":47},\"showValue\":\"no\"},\"priceYearChart\":{\"showValue\":\"no\",\"legendSelected\":{\"2019\":true,\"2020\":true,\"2021\":true,\"2022\":true}},\"priceCEGYearChart\":{\"datazoom\":{\"start\":0,\"end\":47},\"dropSelect\":[\"12299\",\"12297\",\"12298\"],\"showValue\":\"no\",\"legendSelected\":{\"通信IC\":true,\"通用IC\":true,\"处理器IC\":true}},\"specificatChart\":{\"datazoom\":{\"start\":0,\"end\":100},\"showValue\":\"no\",\"legendSelected\":{}},\"singeCodePriceChart\":{\"datazoom\":{\"start\":0,\"end\":47},\"showValue\":\"no\"},\"itemCodePriceChart\":{\"datazoom\":{\"start\":0,\"end\":47},\"dropSelect\":null,\"showValue\":\"no\",\"legendSelected\":{}},\"categoryCodePriceChart\":{\"datazoom\":{\"start\":0,\"end\":47},\"dropSelect\":null,\"showValue\":\"no\",\"legendSelected\":{}}},\"collapseName\":[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\"]},\"orderCaliber\":null}");
        dmFocPageInfoVO.setPageName("月度分析1");
        List<DmFocPageInfoVO> pageInfoVOList = new ArrayList<>();
        PowerMockito.doReturn(pageInfoVOList).when(dmFocPageInfoDao).findPageInfoVOList(any());
        ResultDataVO resultDataVO = commonService.saveOrUpdatePageInfo(dmFocPageInfoVO);
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void saveOrUpdatePageInfo4Test() throws Exception {
        mockStatic(UserInfoUtils.class);
        UserVO currentUser = new UserVO();
        RoleVO currentRole = new RoleVO();
        currentRole.setRoleId(1213);
        currentRole.setRoleName("ICT_Analysis");
        currentUser.setCurrentRole(currentRole);
        when(UserInfoUtils.class, "getCurrentUser").thenReturn(currentUser);
        DmFocPageInfoVO dmFocPageInfoVO = new DmFocPageInfoVO();
        dmFocPageInfoVO.setSaveThreshold("{\"goodsCaliber\":{\"formFilters\":{\"periodId\":\"201901\",\"specDept\":\"12251\",\"module\":\"\",\"category\":\"\",\"item\":\"\"},\"chartFilters\":{\"hearChart\":{\"datazoom\":{\"start\":0,\"end\":47},\"datarangeselect\":null,\"pageOv\":{\"currentPage\":1,\"pageSize\":10}},\"priceChart\":{\"datazoom\":{\"start\":0,\"end\":47},\"showValue\":\"no\"},\"priceYearChart\":{\"showValue\":\"no\",\"legendSelected\":{\"2019\":true,\"2020\":true,\"2021\":true,\"2022\":true}},\"priceCEGYearChart\":{\"datazoom\":{\"start\":0,\"end\":47},\"dropSelect\":[\"12299\",\"12297\",\"12298\"],\"showValue\":\"no\",\"legendSelected\":{\"通信IC\":true,\"通用IC\":true,\"处理器IC\":true}},\"specificatChart\":{\"datazoom\":{\"start\":0,\"end\":100},\"showValue\":\"no\",\"legendSelected\":{}},\"singeCodePriceChart\":{\"datazoom\":{\"start\":0,\"end\":47},\"showValue\":\"no\"},\"itemCodePriceChart\":{\"datazoom\":{\"start\":0,\"end\":47},\"dropSelect\":null,\"showValue\":\"no\",\"legendSelected\":{}},\"categoryCodePriceChart\":{\"datazoom\":{\"start\":0,\"end\":47},\"dropSelect\":null,\"showValue\":\"no\",\"legendSelected\":{}}},\"collapseName\":[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\"]},\"orderCaliber\":null}");
        List<DmFocPageInfoVO> pageInfoVOList = new ArrayList<>();
        PowerMockito.doReturn(pageInfoVOList).when(dmFocPageInfoDao).findPageInfoVOList(any());
        ResultDataVO resultDataVO = commonService.saveOrUpdatePageInfo(dmFocPageInfoVO);
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void setDefaultFlag() throws Exception {
        mockStatic(UserInfoUtils.class);
        UserVO currentUser = new UserVO();
        RoleVO currentRole = new RoleVO();
        currentRole.setRoleId(1213);
        currentRole.setRoleName("ICT_Analysis");
        currentUser.setCurrentRole(currentRole);
        when(UserInfoUtils.class, "getCurrentUser").thenReturn(currentUser);
        DmFocPageInfoVO dmFocPageInfoVO = new DmFocPageInfoVO();
        dmFocPageInfoVO.setPageId(12L);
        dmFocPageInfoVO.setCaliberFlag("U");
        String w3Accouont ="WWX1167755";
        when(UserInfoUtils.class, "getUserAccount").thenReturn(w3Accouont);
        DmFocPageInfoVO pageInfoVO = DmFocPageInfoVO.builder()
                .userId(UserInfoUtils.getUserAccount())
                .defaultFlag(Constant.StrEnum.STR_Y.getValue()).build();
        List<DmFocPageInfoVO> pageInfoVOList = new ArrayList<>();
        pageInfoVOList.add(dmFocPageInfoVO);
        Mockito.when(dmFocPageInfoDao.findPageInfoVOList(any())).thenReturn(pageInfoVOList);
        PowerMockito.doReturn(pageInfoVO).when(dmFocPageInfoDao).findDmFocPageInfoVOById(any(),any());
        ResultDataVO resultDataVO = commonService.setDefaultFlag(dmFocPageInfoVO);
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void setDefaultFlag1Test() throws Exception {
        mockStatic(UserInfoUtils.class);
        UserVO currentUser = new UserVO();
        RoleVO currentRole = new RoleVO();
        currentRole.setRoleId(1213);
        currentRole.setRoleName("ICT_Analysis");
        currentUser.setCurrentRole(currentRole);
        when(UserInfoUtils.class, "getCurrentUser").thenReturn(currentUser);
        DmFocPageInfoVO dmFocPageInfoVO = new DmFocPageInfoVO();
        dmFocPageInfoVO.setPageId(12L);
        DmFocPageInfoVO newpageInfoVO = new DmFocPageInfoVO();
        String w3Accouont ="WWX1167755";
        when(UserInfoUtils.class, "getUserAccount").thenReturn(w3Accouont);
        newpageInfoVO.setUserId(UserInfoUtils.getUserAccount());
        PowerMockito.doReturn(newpageInfoVO).when(dmFocPageInfoDao).findDmFocPageInfoVOById(any(),any());
        DmFocPageInfoVO pageInfoVO = DmFocPageInfoVO.builder()
                .userId(UserInfoUtils.getUserAccount())
                .roleId(String.valueOf(currentRole.getRoleId()))
                .defaultFlag(Constant.StrEnum.STR_Y.getValue()).build();
        List<DmFocPageInfoVO> pageInfoVOList = new ArrayList<>();
        DmFocPageInfoVO dmFocPageInfoVO1 = new DmFocPageInfoVO();
        dmFocPageInfoVO1.setPageName("月度分析2");
        pageInfoVOList.add(dmFocPageInfoVO1);
        PowerMockito.doReturn(pageInfoVOList).when(dmFocPageInfoDao).findPageInfoVOList(pageInfoVO);
        ResultDataVO resultDataVO = commonService.setDefaultFlag(dmFocPageInfoVO);
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void getInitCondition(){
        ResultDataVO initCondition = commonService.findInitCondition(11L,"ICT");
        Assert.assertNotNull(initCondition);
    }

    @Test
    public void getInitCondition2Test() {
        ResultDataVO initCondition = commonService.findInitCondition(null,"ICT");
        Assert.assertNotNull(initCondition);
    }

    @Test
    public void deletePageInfo() throws Exception {
        mockStatic(UserInfoUtils.class);
        UserVO currentUser = new UserVO();
        RoleVO currentRole = new RoleVO();
        currentRole.setRoleId(1213);
        currentRole.setRoleName("ICT_Analysis");
        currentUser.setCurrentRole(currentRole);
        when(UserInfoUtils.class, "getCurrentUser").thenReturn(currentUser);
        List<DmFocPageInfoVO> pageInfoVOList = new ArrayList<>();
        DmFocPageInfoVO dmFoiPageInfoVO1 = new DmFocPageInfoVO();
        dmFoiPageInfoVO1.setPageName("月度分析3");
        pageInfoVOList.add(dmFoiPageInfoVO1);
        PowerMockito.doReturn(pageInfoVOList).when(dmFocPageInfoDao).findPageInfoVOList(any());
        DmFocPageInfoVO pageInfoVO = new DmFocPageInfoVO();
        String w3Accouont ="WWX1167755";
        when(UserInfoUtils.class, "getUserAccount").thenReturn(w3Accouont);
        pageInfoVO.setUserId(UserInfoUtils.getUserAccount());
        PowerMockito.doReturn(pageInfoVO).when(dmFocPageInfoDao).findDmFocPageInfoVOById(any(),any());
        ResultDataVO deletePageInfo = commonService.deletePageInfo(11L, "Y","C","ICT");
        Assert.assertNotNull(deletePageInfo);
    }

    @Test
    public void deletePageInfo1Test() {
        ResultDataVO deletePageInfo = commonService.deletePageInfo(null, "Y","R","ICT");
        Assert.assertNotNull(deletePageInfo);
    }

    @Test
    public void deletePageInfo2Test() throws Exception {
        mockStatic(UserInfoUtils.class);
        UserVO currentUser = new UserVO();
        RoleVO currentRole = new RoleVO();
        currentRole.setRoleId(1213);
        currentRole.setRoleName("ICT_Analysis");
        currentUser.setCurrentRole(currentRole);
        when(UserInfoUtils.class, "getCurrentUser").thenReturn(currentUser);
        DmFocPageInfoVO pageInfoVO = new DmFocPageInfoVO();
        String w3Accouont ="WWX1167755";
        when(UserInfoUtils.class, "getUserAccount").thenReturn(w3Accouont);
        pageInfoVO.setUserId(UserInfoUtils.getUserAccount());
        PowerMockito.doReturn(pageInfoVO).when(dmFocPageInfoDao).findDmFocPageInfoVOById(any(),any());
        ResultDataVO deletePageInfo = commonService.deletePageInfo(12L, "N","R","ICT");
        Assert.assertNotNull(deletePageInfo);
    }

    @Test
    public void deletePageInfo3Test() throws Exception {
        mockStatic(UserInfoUtils.class);
        UserVO currentUser = new UserVO();
        RoleVO currentRole = new RoleVO();
        currentRole.setRoleId(1213);
        currentRole.setRoleName("ICT_Analysis");
        currentUser.setCurrentRole(currentRole);
        when(UserInfoUtils.class, "getCurrentUser").thenReturn(currentUser);
        List<DmFocPageInfoVO> pageInfoVOList = new ArrayList<>();
        PowerMockito.doReturn(pageInfoVOList).when(dmFocPageInfoDao).findPageInfoVOList(any());
        DmFocPageInfoVO pageInfoVO = new DmFocPageInfoVO();
        String w3Accouont ="WWX1167755";
        when(UserInfoUtils.class, "getUserAccount").thenReturn(w3Accouont);
        pageInfoVO.setUserId(UserInfoUtils.getUserAccount());
        PowerMockito.doReturn(pageInfoVO).when(dmFocPageInfoDao).findDmFocPageInfoVOById(any(),any());
        ResultDataVO deletePageInfo = commonService.deletePageInfo(11L, "Y","R","ICT");
        Assert.assertNotNull(deletePageInfo);
    }

    @Test
    public void findActualMonthNum() {
        Long actualMonthNum = 202305L;
        PowerMockito.doReturn(actualMonthNum).when(iDmFocMonthCostIdxDao).findActualMonthNum(any());
        Long actualMonthNum1 = commonService.findActualMonthNum(any());
        Assert.assertNotNull(actualMonthNum1);
    }

    @Test
    public void getCurrentRoleDataPermission() throws Exception {
        UserVO currentUser = new UserVO();
        mockStatic(UserInfoUtils.class);
        RoleVO currentRole = new RoleVO();
        currentRole.setRoleId(1213);
        currentRole.setRoleName("ICT_Analysis");
        currentUser.setCurrentRole(currentRole);
        List<ProgramVO> program = new ArrayList<>();
        ProgramVO programVO = new ProgramVO();
        List<ProgramItemVO> programItemVOList = new ArrayList<>();
        ProgramItemVO programItemVO = new ProgramItemVO();
        List<KeyValuePairVO> values = new ArrayList<>();
        KeyValuePairVO keyValuePairVO = new KeyValuePairVO();
        keyValuePairVO.setKey("000_LV0");
        KeyValuePairVO keyValuePairVO1 = new KeyValuePairVO();
        keyValuePairVO1.setKey("111_LV1");
        KeyValuePairVO keyValuePairVO2 = new KeyValuePairVO();
        keyValuePairVO2.setKey("222_LV2");
        KeyValuePairVO keyValuePairVO3 = new KeyValuePairVO();
        keyValuePairVO3.setKey("333_LV3");
        values.add(keyValuePairVO);
        values.add(keyValuePairVO1);
        values.add(keyValuePairVO2);
        values.add(keyValuePairVO3);
        programItemVO.setValues(values);
        programItemVOList.add(programItemVO);
        DimensionDefinitionVO dimension = new DimensionDefinitionVO();
        dimension.setDimensionCode("getDimensionWithTree");
        programItemVO.setDimension(dimension);
        programVO.setItems(programItemVOList);
        program.add(programVO);
        currentUser.setCurrentPrograms(program);
        when(UserInfoUtils.class, "getCurrentUser").thenReturn(currentUser);
        Assert.assertNotNull(commonService.getCurrentRoleDataPermission("ICT"));
    }

    @Test
    public void getCurrentRoleDataPermission10Test() throws Exception {
        UserVO currentUser = new UserVO();
        mockStatic(UserInfoUtils.class);
        RoleVO currentRole = new RoleVO();
        currentRole.setRoleId(1213);
        currentRole.setRoleName("ICT_Analysis");
        currentUser.setCurrentRole(currentRole);
        List<ProgramVO> program = new ArrayList<>();
        ProgramVO programVO = new ProgramVO();
        List<ProgramItemVO> programItemVOList = new ArrayList<>();
        ProgramItemVO programItemVO = new ProgramItemVO();
        List<KeyValuePairVO> values = new ArrayList<>();

        KeyValuePairVO keyValuePairVO = new KeyValuePairVO();
        keyValuePairVO.setKey("000");
        KeyValuePairVO keyValuePairVO1 = new KeyValuePairVO();
        keyValuePairVO1.setKey("111");
        KeyValuePairVO keyValuePairVO2 = new KeyValuePairVO();
        keyValuePairVO2.setKey("222");
        KeyValuePairVO keyValuePairVO3 = new KeyValuePairVO();
        keyValuePairVO3.setKey("333");
        values.add(keyValuePairVO);
        values.add(keyValuePairVO1);
        values.add(keyValuePairVO2);
        values.add(keyValuePairVO3);
        programItemVO.setValues(values);
        programItemVOList.add(programItemVO);
        programVO.setItems(programItemVOList);
        DimensionDefinitionVO dimension = new DimensionDefinitionVO();
        dimension.setDimensionCode("getDimensionWithTree");
        programItemVO.setDimension(dimension);
        program.add(programVO);
        currentUser.setCurrentPrograms(program);
        when(UserInfoUtils.class, "getCurrentUser").thenReturn(currentUser);
        DataPermissionsVO dataPermissionsVO = commonService.getCurrentRoleDataPermission("ICT");
        Assert.assertNotNull(dataPermissionsVO);
    }

    @Test
    public void getCurrentRoleDataPermission1Test() throws Exception {
        UserVO currentUser = new UserVO();
        mockStatic(UserInfoUtils.class);
        RoleVO currentRole = new RoleVO();
        currentRole.setRoleId(1213);
        currentRole.setRoleName("ICT_Analysis");
        currentUser.setCurrentRole(currentRole);
        List<ProgramVO> program = new ArrayList<>();
        ProgramVO programVO = new ProgramVO();
        List<ProgramItemVO> programItemVOList = new ArrayList<>();
        programVO.setItems(programItemVOList);
        program.add(programVO);
        currentUser.setCurrentPrograms(program);
        when(UserInfoUtils.class, "getCurrentUser").thenReturn(currentUser);
        DataPermissionsVO dataPermissionsVO = commonService.getCurrentRoleDataPermission("ICT");
        Assert.assertNotNull(dataPermissionsVO);
    }

    @Test
    public void getCurrentRoleDataPermission2Test() throws Exception {
        UserVO currentUser = new UserVO();
        mockStatic(UserInfoUtils.class);
        RoleVO currentRole = new RoleVO();
        currentRole.setRoleId(1213);
        currentRole.setRoleName("ICT_Analysis");
        currentUser.setCurrentRole(currentRole);
        List<ProgramVO> program = new ArrayList<>();
        ProgramVO programVO = new ProgramVO();
        List<ProgramItemVO> programItemVOList = new ArrayList<>();
        ProgramItemVO programItemVO = new ProgramItemVO();
        List<KeyValuePairVO> values = new ArrayList<>();
        KeyValuePairVO keyValuePairVO = new KeyValuePairVO();
        keyValuePairVO.setKey("ALL");
        values.add(keyValuePairVO);
        programItemVO.setValues(values);
        programItemVOList.add(programItemVO);
        DimensionDefinitionVO dimension = new DimensionDefinitionVO();
        dimension.setDimensionCode("getDimensionWithTree");
        programItemVO.setDimension(dimension);
        programVO.setItems(programItemVOList);
        program.add(programVO);
        currentUser.setCurrentPrograms(program);
        when(UserInfoUtils.class, "getCurrentUser").thenReturn(currentUser);
        DataPermissionsVO dataPermissionsVO = commonService.getCurrentRoleDataPermission("ICT");
        Assert.assertNotNull(dataPermissionsVO);
    }

    @Test
    public void getCurrentRoleDataPermission3Test() throws Exception {
        UserVO currentUser = new UserVO();
        mockStatic(UserInfoUtils.class);
        RoleVO currentRole = new RoleVO();
        currentRole.setRoleId(1213);
        currentRole.setRoleName("ICT_Analysis");
        currentUser.setCurrentRole(currentRole);
        List<ProgramVO> program = new ArrayList<>();
        ProgramVO programVO = new ProgramVO();
        List<ProgramItemVO> programItemVOList = new ArrayList<>();
        ProgramItemVO programItemVO = new ProgramItemVO();
        List<KeyValuePairVO> values = new ArrayList<>();
        KeyValuePairVO keyValuePairVO = new KeyValuePairVO();
        keyValuePairVO.setKey("@ALLCONDITION@");
        values.add(keyValuePairVO);
        programItemVO.setValues(values);
        programItemVOList.add(programItemVO);
        DimensionDefinitionVO dimension = new DimensionDefinitionVO();
        dimension.setDimensionCode("getDimensionWithTree");
        programItemVO.setDimension(dimension);
        programVO.setItems(programItemVOList);
        program.add(programVO);
        currentUser.setCurrentPrograms(program);
        when(UserInfoUtils.class, "getCurrentUser").thenReturn(currentUser);
        DataPermissionsVO dataPermissionsVO = commonService.getCurrentRoleDataPermission("ICT");
        Assert.assertNotNull(dataPermissionsVO);
    }

    @Test
    public void hasRolePermission() throws Exception {
        DmFocPageInfoVO dmFocPageInfoVO = new DmFocPageInfoVO();
        String saveThreshold ="{\"costType\":\"P\",\"userView\":\"0\",\"userViewName\":\"ICT-专项采购认证部-品类\",\"lv1\":[\"211414\"],\"lv1Flag\":[\"no\"],\"lv1Name\":\"\",\"lv2\":\"42425\",\"lv2Name\":\"\",\"lv3\":\"1111\",\"lv3Name\":\"\",\"l1\":\"\",\"l1Name\":\"\",\"l2\":\"\",\"l2Name\":\"\",\"specDept\":\"\",\"specDeptName\":\"\",\"category\":\"\",\"categoryName\":\"\"}";
        dmFocPageInfoVO.setSaveThreshold(saveThreshold);
        dmFocPageInfoVO.setCaliberFlag("C");
        dmFocPageInfoVO.setTablePreFix("dm_foc");
        UserVO currentUser = new UserVO();
        mockStatic(UserInfoUtils.class);
        RoleVO currentRole = new RoleVO();
        currentRole.setRoleId(1213);
        currentRole.setRoleName("ICT_Analysis");
        currentUser.setCurrentRole(currentRole);
        List<ViewInfoVO> allProdDimensionList = new ArrayList<>();
        ViewInfoVO ViewInfoVO = new ViewInfoVO();
        ViewInfoVO.setLv1ProdRndTeamCode("12424");
        ViewInfoVO.setLv2ProdRndTeamCode("12r424");
        ViewInfoVO.setLv3ProdRndTeamCode("12r424");
        allProdDimensionList.add(ViewInfoVO);
        PowerMockito.doReturn(allProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(dmFocPageInfoVO.getCostType(),dmFocPageInfoVO.getTablePreFix());
        List<ProgramVO> program = new ArrayList<>();
        ProgramVO programVO = new ProgramVO();
        List<ProgramItemVO> programItemVOList = new ArrayList<>();
        ProgramItemVO programItemVO = new ProgramItemVO();
        List<KeyValuePairVO> values = new ArrayList<>();
        KeyValuePairVO keyValuePairVO = new KeyValuePairVO();
        keyValuePairVO.setKey("000_LV0");
        KeyValuePairVO keyValuePairVO1 = new KeyValuePairVO();
        keyValuePairVO1.setKey("111_LV1");
        KeyValuePairVO keyValuePairVO2 = new KeyValuePairVO();
        keyValuePairVO2.setKey("222_LV2");
        KeyValuePairVO keyValuePairVO3 = new KeyValuePairVO();
        keyValuePairVO3.setKey("333_LV3");
        values.add(keyValuePairVO);
        values.add(keyValuePairVO1);
        values.add(keyValuePairVO2);
        values.add(keyValuePairVO3);
        programItemVO.setValues(values);
        programItemVOList.add(programItemVO);
        DimensionDefinitionVO dimension = new DimensionDefinitionVO();
        dimension.setDimensionCode("getDimensionWithTree");
        programItemVO.setDimension(dimension);
        programVO.setItems(programItemVOList);
        program.add(programVO);
        currentUser.setCurrentPrograms(program);
        when(UserInfoUtils.class, "getCurrentUser").thenReturn(currentUser);
        Boolean permissionFlag = commonService.hasRolePermission(dmFocPageInfoVO);
        Assert.assertEquals(permissionFlag,true);
    }

    @Test
    public void hasRolePermission11Test() throws Exception {
        DmFocPageInfoVO dmFocPageInfoVO = new DmFocPageInfoVO();
        String saveThreshold ="{\"costType\":\"P\",\"userView\":\"0\",\"userViewName\":\"ICT-专项采购认证部-品类\",\"lv1Flag\":[\"no\"],\"lv1\":[\"211414\"],\"lv1Name\":\"\",\"lv2\":\"42425\",\"lv2Name\":\"\",\"lv3\":\"\",\"lv3Name\":\"\",\"l1\":\"\",\"l1Name\":\"\",\"l2\":\"\",\"l2Name\":\"\",\"specDept\":\"\",\"specDeptName\":\"\",\"category\":\"\",\"categoryName\":\"\"}";
        dmFocPageInfoVO.setSaveThreshold(saveThreshold);
        dmFocPageInfoVO.setCaliberFlag("C");
        dmFocPageInfoVO.setCostType("P");
        UserVO currentUser = new UserVO();
        mockStatic(UserInfoUtils.class);
        RoleVO currentRole = new RoleVO();
        currentRole.setRoleId(1213);
        currentRole.setRoleName("ICT_Analysis");
        currentUser.setCurrentRole(currentRole);
        List<ViewInfoVO> allProdDimensionList = new ArrayList<>();
        ViewInfoVO ViewInfoVO = new ViewInfoVO();
        ViewInfoVO.setLv1ProdRndTeamCode("12424");
        ViewInfoVO.setLv2ProdRndTeamCode("12r424");
        ViewInfoVO.setLv3ProdRndTeamCode("12r424");
        allProdDimensionList.add(ViewInfoVO);
        PowerMockito.doReturn(allProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(dmFocPageInfoVO.getCostType(),"dm_foc");
        List<ProgramVO> program = new ArrayList<>();
        ProgramVO programVO = new ProgramVO();
        List<ProgramItemVO> programItemVOList = new ArrayList<>();
        ProgramItemVO programItemVO = new ProgramItemVO();
        List<KeyValuePairVO> values = new ArrayList<>();
        KeyValuePairVO keyValuePairVO = new KeyValuePairVO();
        keyValuePairVO.setKey("111");
        values.add(keyValuePairVO);
        programItemVO.setValues(values);
        programItemVOList.add(programItemVO);
        DimensionDefinitionVO dimension = new DimensionDefinitionVO();
        dimension.setDimensionCode("getDimensionWithTree");
        programItemVO.setDimension(dimension);
        programVO.setItems(programItemVOList);
        program.add(programVO);
        currentUser.setCurrentPrograms(program);
        when(UserInfoUtils.class, "getCurrentUser").thenReturn(currentUser);
        List<ViewInfoVO> allProdDimension = new ArrayList<>();
        ViewInfoVO viewInfoDto = new ViewInfoVO();
        viewInfoDto.setLv1ProdRndTeamCode("12424");
        viewInfoDto.setLv2ProdRndTeamCode("12r424");
        viewInfoDto.setLv3ProdRndTeamCode("12r424");
        allProdDimension.add(viewInfoDto);
        PowerMockito.doReturn(allProdDimension).when(dataDimensionService).getCurrentLv2ProdRndTeamList(dmFocPageInfoVO.getCostType(),dmFocPageInfoVO.getTablePreFix());
        Boolean permissionFlag = commonService.hasRolePermission(dmFocPageInfoVO);
        Assert.assertEquals(permissionFlag,false);
    }
    @Test
    public void hasRolePermission1Test() throws Exception {
        DmFocPageInfoVO dmFocPageInfoVO = new DmFocPageInfoVO();
        String saveThreshold ="{\"costType\":\"P\",\"userView\":\"0\",\"userViewName\":\"ICT-专项采购认证部-品类\",\"lv1\":[\"211414\"],,\"lv1Flag\":[\"\"],\"lv1Name\":\"\",\"lv2\":\"211414\",\"lv2Name\":\"\",\"lv3\":\"\",\"lv3Name\":\"\",\"l1\":\"\",\"l1Name\":\"\",\"l2\":\"\",\"l2Name\":\"\",\"specDept\":\"\",\"specDeptName\":\"\",\"category\":\"\",\"categoryName\":\"\"}";
        dmFocPageInfoVO.setSaveThreshold(saveThreshold);
        dmFocPageInfoVO.setCaliberFlag("C");
        dmFocPageInfoVO.setCostType("P");
        UserVO currentUser = new UserVO();
        mockStatic(UserInfoUtils.class);
        RoleVO currentRole = new RoleVO();
        currentRole.setRoleId(1213);
        currentRole.setRoleName("ICT_Analysis");
        currentUser.setCurrentRole(currentRole);
        List<ViewInfoVO> allProdDimensionList = new ArrayList<>();
        ViewInfoVO ViewInfoVO = new ViewInfoVO();
        ViewInfoVO.setLv1ProdRndTeamCode("12424");
        ViewInfoVO.setLv2ProdRndTeamCode("12r424");
        ViewInfoVO.setLv3ProdRndTeamCode("12r424");
        allProdDimensionList.add(ViewInfoVO);
        PowerMockito.doReturn(allProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(dmFocPageInfoVO.getCostType(),"dm_foc");
        List<ProgramVO> program = new ArrayList<>();
        ProgramVO programVO = new ProgramVO();
        List<ProgramItemVO> programItemVOList = new ArrayList<>();
        ProgramItemVO programItemVO = new ProgramItemVO();
        List<KeyValuePairVO> values = new ArrayList<>();
        KeyValuePairVO keyValuePairVO = new KeyValuePairVO();
        keyValuePairVO.setKey("111");
        values.add(keyValuePairVO);
        programItemVO.setValues(values);
        programItemVOList.add(programItemVO);
        DimensionDefinitionVO dimension = new DimensionDefinitionVO();
        dimension.setDimensionCode("getDimensionWithTree");
        programItemVO.setDimension(dimension);
        programVO.setItems(programItemVOList);
        program.add(programVO);
        currentUser.setCurrentPrograms(program);
        when(UserInfoUtils.class, "getCurrentUser").thenReturn(currentUser);
        List<ViewInfoVO> allProdDimension = new ArrayList<>();
        ViewInfoVO viewInfoDto = new ViewInfoVO();
        viewInfoDto.setLv1ProdRndTeamCode("12424");
        viewInfoDto.setLv2ProdRndTeamCode("12r424");
        viewInfoDto.setLv3ProdRndTeamCode("12r424");
        allProdDimension.add(viewInfoDto);
        PowerMockito.doReturn(allProdDimension).when(dataDimensionService).getCurrentLv2ProdRndTeamList(dmFocPageInfoVO.getCostType(),dmFocPageInfoVO.getTablePreFix());

        Boolean permissionFlag = commonService.hasRolePermission(dmFocPageInfoVO);
        Assert.assertEquals(permissionFlag,false);
    }

    @Test
    public void hasRolePermission2Test() throws Exception {
        DmFocPageInfoVO dmFocPageInfoVO = new DmFocPageInfoVO();
        String saveThreshold ="{\"costType\":\"P\",\"userView\":\"0\",\"userViewName\":\"ICT-专项采购认证部-品类\",\"lv1\":[\"211414\"],,\"lv1Flag\":[\"\"],\"lv1Name\":\"\",\"lv2\":\"\",\"lv2Name\":\"\",\"lv3\":\"\",\"lv3Name\":\"\",\"l1\":\"\",\"l1Name\":\"\",\"l2\":\"\",\"l2Name\":\"\",\"specDept\":\"\",\"specDeptName\":\"\",\"category\":\"\",\"categoryName\":\"\"}";
        dmFocPageInfoVO.setSaveThreshold(saveThreshold);
        dmFocPageInfoVO.setCaliberFlag("C");
        dmFocPageInfoVO.setCostType("P");
        UserVO currentUser = new UserVO();
        mockStatic(UserInfoUtils.class);
        RoleVO currentRole = new RoleVO();
        currentRole.setRoleId(1213);
        currentRole.setRoleName("ICT_Analysis");
        currentUser.setCurrentRole(currentRole);
        List<ViewInfoVO> allProdDimensionList = new ArrayList<>();
        ViewInfoVO ViewInfoVO = new ViewInfoVO();
        ViewInfoVO.setLv1ProdRndTeamCode("12424");
        ViewInfoVO.setLv2ProdRndTeamCode("12r424");
        ViewInfoVO.setLv3ProdRndTeamCode("12r424");
        allProdDimensionList.add(ViewInfoVO);
        PowerMockito.doReturn(allProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(dmFocPageInfoVO.getCostType(),"dm_foc");
        List<ProgramVO> program = new ArrayList<>();
        ProgramVO programVO = new ProgramVO();
        List<ProgramItemVO> programItemVOList = new ArrayList<>();
        ProgramItemVO programItemVO = new ProgramItemVO();
        List<KeyValuePairVO> values = new ArrayList<>();
        KeyValuePairVO keyValuePairVO = new KeyValuePairVO();
        keyValuePairVO.setKey("111");
        values.add(keyValuePairVO);
        programItemVO.setValues(values);
        programItemVOList.add(programItemVO);
        DimensionDefinitionVO dimension = new DimensionDefinitionVO();
        dimension.setDimensionCode("getDimensionWithTree");
        programItemVO.setDimension(dimension);
        programVO.setItems(programItemVOList);
        program.add(programVO);
        currentUser.setCurrentPrograms(program);
        when(UserInfoUtils.class, "getCurrentUser").thenReturn(currentUser);
        List<ViewInfoVO> allProdDimension = new ArrayList<>();
        ViewInfoVO viewInfoDto = new ViewInfoVO();
        viewInfoDto.setLv1ProdRndTeamCode("12424");
        viewInfoDto.setLv2ProdRndTeamCode("12r424");
        viewInfoDto.setLv3ProdRndTeamCode("12r424");
        allProdDimension.add(viewInfoDto);
        PowerMockito.doReturn(allProdDimension).when(dataDimensionService).getCurrentLv2ProdRndTeamList(dmFocPageInfoVO.getCostType(),dmFocPageInfoVO.getTablePreFix());

        Boolean permissionFlag = commonService.hasRolePermission(dmFocPageInfoVO);
        Assert.assertEquals(permissionFlag,false);
    }

    @Test
    public void hasRolePermission3Test() throws Exception {
        DmFocPageInfoVO dmFocPageInfoVO = new DmFocPageInfoVO();
        String saveThreshold ="{\"costType\":\"P\",\"userView\":\"0\",\"userViewName\":\"ICT-专项采购认证部-品类\",\"lv1\":[\"\"],\"lv1Name\":\"\",,\"lv1Flag\":[\"\"],\"lv2\":\"\",\"lv2Name\":\"\",\"lv3\":\"1214\",\"lv3Name\":\"\",\"l1\":\"\",\"l1Name\":\"\",\"l2\":\"\",\"l2Name\":\"\",\"specDept\":\"\",\"specDeptName\":\"\",\"category\":\"\",\"categoryName\":\"\"}";
        dmFocPageInfoVO.setSaveThreshold(saveThreshold);
        dmFocPageInfoVO.setCaliberFlag("C");
        dmFocPageInfoVO.setCostType("P");
        dmFocPageInfoVO.setTablePreFix("dm_foc");
        UserVO currentUser = new UserVO();
        mockStatic(UserInfoUtils.class);
        RoleVO currentRole = new RoleVO();
        currentRole.setRoleId(1213);
        currentRole.setRoleName("ICT_Analysis");
        currentUser.setCurrentRole(currentRole);
        List<ViewInfoVO> allProdDimensionList = new ArrayList<>();
        ViewInfoVO ViewInfoVO = new ViewInfoVO();
        ViewInfoVO.setLv1ProdRndTeamCode("12424");
        ViewInfoVO.setLv2ProdRndTeamCode("12r424");
        ViewInfoVO.setLv3ProdRndTeamCode("12r424");
        allProdDimensionList.add(ViewInfoVO);
        PowerMockito.doReturn(allProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(dmFocPageInfoVO.getCostType(),"dm_foc");
        List<ProgramVO> program = new ArrayList<>();
        ProgramVO programVO = new ProgramVO();
        List<ProgramItemVO> programItemVOList = new ArrayList<>();
        ProgramItemVO programItemVO = new ProgramItemVO();
        List<KeyValuePairVO> values = new ArrayList<>();
        KeyValuePairVO keyValuePairVO = new KeyValuePairVO();
        keyValuePairVO.setKey("111");
        values.add(keyValuePairVO);
        programItemVO.setValues(values);
        programItemVOList.add(programItemVO);
        DimensionDefinitionVO dimension = new DimensionDefinitionVO();
        dimension.setDimensionCode("getDimensionWithTree");
        programItemVO.setDimension(dimension);
        programVO.setItems(programItemVOList);
        program.add(programVO);
        currentUser.setCurrentPrograms(program);
        when(UserInfoUtils.class, "getCurrentUser").thenReturn(currentUser);
        List<ViewInfoVO> allProdDimension = new ArrayList<>();
        ViewInfoVO viewInfoDto = new ViewInfoVO();
        viewInfoDto.setLv1ProdRndTeamCode("12424");
        viewInfoDto.setLv2ProdRndTeamCode("12r424");
        viewInfoDto.setLv3ProdRndTeamCode("12r424");
        allProdDimension.add(viewInfoDto);
        PowerMockito.doReturn(allProdDimension).when(dataDimensionService).getCurrentLv2ProdRndTeamList(dmFocPageInfoVO.getCostType(),dmFocPageInfoVO.getTablePreFix());

        Boolean permissionFlag = commonService.hasRolePermission(dmFocPageInfoVO);
        Assert.assertEquals(permissionFlag,false);
    }

    @Test
    public void hasRolePermission4Test() throws Exception {
        DmFocPageInfoVO dmFocPageInfoVO = new DmFocPageInfoVO();
        String saveThreshold ="{\"costType\":\"P\",\"userView\":\"0\",\"userViewName\":\"ICT-专项采购认证部-品类\",\"lv1\":[\"211414\"],\"lv1Name\":\"\",,\"lv1Flag\":[\"\"],\"lv2\":\"42425\",\"lv2Name\":\"\",\"lv3\":\"1111\",\"lv3Name\":\"\",\"l1\":\"\",\"l1Name\":\"\",\"l2\":\"\",\"l2Name\":\"\",\"specDept\":\"\",\"specDeptName\":\"\",\"category\":\"\",\"categoryName\":\"\"}";
        dmFocPageInfoVO.setSaveThreshold(saveThreshold);
        dmFocPageInfoVO.setCaliberFlag("C");
        dmFocPageInfoVO.setCostType("P");
        dmFocPageInfoVO.setTablePreFix("dm_foc");
        UserVO currentUser = new UserVO();
        mockStatic(UserInfoUtils.class);
        RoleVO currentRole = new RoleVO();
        currentRole.setRoleId(1213);
        currentRole.setRoleName("ICT_Analysis");
        currentUser.setCurrentRole(currentRole);
        List<ViewInfoVO> allProdDimensionList = new ArrayList<>();
        ViewInfoVO ViewInfoVO = new ViewInfoVO();
        ViewInfoVO.setLv1ProdRndTeamCode("12424");
        ViewInfoVO.setLv2ProdRndTeamCode("12r424");
        ViewInfoVO.setLv3ProdRndTeamCode("12r424");
        allProdDimensionList.add(ViewInfoVO);
        PowerMockito.doReturn(allProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(dmFocPageInfoVO.getCostType(),"dm_foc");
        List<ProgramVO> program = new ArrayList<>();
        ProgramVO programVO = new ProgramVO();
        List<ProgramItemVO> programItemVOList = new ArrayList<>();
        ProgramItemVO programItemVO = new ProgramItemVO();
        List<KeyValuePairVO> values = new ArrayList<>();
        KeyValuePairVO keyValuePairVO = new KeyValuePairVO();
        keyValuePairVO.setKey("ALL");
        values.add(keyValuePairVO);
        programItemVO.setValues(values);
        programItemVOList.add(programItemVO);
        DimensionDefinitionVO dimension = new DimensionDefinitionVO();
        dimension.setDimensionCode("getDimensionWithTree");
        programItemVO.setDimension(dimension);
        programVO.setItems(programItemVOList);
        program.add(programVO);
        currentUser.setCurrentPrograms(program);
        when(UserInfoUtils.class, "getCurrentUser").thenReturn(currentUser);
        Boolean permissionFlag = commonService.hasRolePermission(dmFocPageInfoVO);
        Assert.assertEquals(permissionFlag,true);
    }

    @Test
    public void hasRolePermission5Test() throws Exception {
        DmFocPageInfoVO dmFocPageInfoVO = new DmFocPageInfoVO();
        String saveThreshold ="{\"costType\":\"P\",\"userView\":\"0\",\"userViewName\":\"ICT-专项采购认证部-品类\",\"lv1\":[\"211414\"],\"lv1Flag\":[\"\"],\"lv1Name\":\"\",\"lv2\":\"42425\",\"lv2Name\":\"\",\"lv3\":\"1111\",\"lv3Name\":\"\",\"l1\":\"\",\"l1Name\":\"\",\"l2\":\"\",\"l2Name\":\"\",\"specDept\":\"\",\"specDeptName\":\"\",\"category\":\"\",\"categoryName\":\"\"}";
        dmFocPageInfoVO.setSaveThreshold(saveThreshold);
        dmFocPageInfoVO.setCaliberFlag("C");
        dmFocPageInfoVO.setCostType("P");
        dmFocPageInfoVO.setTablePreFix("dm_foc");
        UserVO currentUser = new UserVO();
        mockStatic(UserInfoUtils.class);
        RoleVO currentRole = new RoleVO();
        currentRole.setRoleId(1213);
        currentRole.setRoleName("ICT_Analysis");
        currentUser.setCurrentRole(currentRole);
        List<ViewInfoVO> allProdDimensionList = new ArrayList<>();
        ViewInfoVO ViewInfoVO = new ViewInfoVO();
        ViewInfoVO.setLv1ProdRndTeamCode("111124");
        ViewInfoVO.setLv2ProdRndTeamCode("111124");
        ViewInfoVO.setLv3ProdRndTeamCode("111124");
        allProdDimensionList.add(ViewInfoVO);
        PowerMockito.doReturn(allProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(dmFocPageInfoVO.getCostType(),dmFocPageInfoVO.getTablePreFix());
        List<ProgramVO> program = new ArrayList<>();
        ProgramVO programVO = new ProgramVO();
        List<ProgramItemVO> programItemVOList = new ArrayList<>();
        ProgramItemVO programItemVO = new ProgramItemVO();
        List<KeyValuePairVO> values = new ArrayList<>();
        KeyValuePairVO keyValuePairVO = new KeyValuePairVO();
        keyValuePairVO.setKey("111124_LV1");
        values.add(keyValuePairVO);
        programItemVO.setValues(values);
        programVO.setItems(programItemVOList);
        program.add(programVO);
        currentUser.setCurrentPrograms(program);
        when(UserInfoUtils.class, "getCurrentUser").thenReturn(currentUser);
        Boolean permissionFlag = commonService.hasRolePermission(dmFocPageInfoVO);
        Assert.assertEquals(permissionFlag,false);
    }

    @Test
    public void hasRolePermission6Test() throws Exception {
        DmFocPageInfoVO dmFocPageInfoVO = new DmFocPageInfoVO();
        String saveThreshold ="{\"costType\":\"P\",\"userView\":\"0\",\"userViewName\":\"ICT-专项采购认证部-品类\",\"lv1\":[\"211414\"],\"lv1Flag\":[\"no\"],\"lv1Name\":\"\",\"lv2\":\"42425\",\"lv2Name\":\"\",\"lv3\":\"\",\"lv3Name\":\"\",\"l1\":\"\",\"l1Name\":\"\",\"l2\":\"\",\"l2Name\":\"\",\"specDept\":\"\",\"specDeptName\":\"\",\"category\":\"\",\"categoryName\":\"\"}";
        dmFocPageInfoVO.setSaveThreshold(saveThreshold);
        dmFocPageInfoVO.setCaliberFlag("C");
        dmFocPageInfoVO.setCostType("P");
        dmFocPageInfoVO.setTablePreFix("dm_foc");
        UserVO currentUser = new UserVO();
        mockStatic(UserInfoUtils.class);
        RoleVO currentRole = new RoleVO();
        currentRole.setRoleId(1213);
        currentRole.setRoleName("ICT_Analysis");
        currentUser.setCurrentRole(currentRole);
        List<ViewInfoVO> allProdDimensionList = new ArrayList<>();
        ViewInfoVO ViewInfoVO = new ViewInfoVO();
        ViewInfoVO.setLv1ProdRndTeamCode("12424");
        ViewInfoVO.setLv2ProdRndTeamCode("12r424");
        ViewInfoVO.setLv3ProdRndTeamCode("12r424");
        allProdDimensionList.add(ViewInfoVO);
        PowerMockito.doReturn(allProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(dmFocPageInfoVO.getCostType(),dmFocPageInfoVO.getTablePreFix());
        List<ProgramVO> program = new ArrayList<>();
        ProgramVO programVO = new ProgramVO();
        List<ProgramItemVO> programItemVOList = new ArrayList<>();
        ProgramItemVO programItemVO = new ProgramItemVO();
        List<KeyValuePairVO> values = new ArrayList<>();
        KeyValuePairVO keyValuePairVO = new KeyValuePairVO();
        keyValuePairVO.setKey("4242522_LV3");
        values.add(keyValuePairVO);
        programItemVO.setValues(values);
        programItemVOList.add(programItemVO);
        DimensionDefinitionVO dimension = new DimensionDefinitionVO();
        dimension.setDimensionCode("getDimensionWithTree");
        programItemVO.setDimension(dimension);
        programVO.setItems(programItemVOList);
        program.add(programVO);
        currentUser.setCurrentPrograms(program);
        when(UserInfoUtils.class, "getCurrentUser").thenReturn(currentUser);
        Boolean permissionFlag = commonService.hasRolePermission(dmFocPageInfoVO);
        Assert.assertEquals(permissionFlag,false);
    }

    @Test
    public void hasRolePermission7Test() throws Exception {
        DmFocPageInfoVO dmFocPageInfoVO = new DmFocPageInfoVO();
        String saveThreshold ="{\"costType\":\"P\",\"costType\":\"P\",\"userView\":\"0\",\"userViewName\":\"ICT-专项采购认证部-品类\",\"lv1\":[\"211414\"],\"lv1Name\":\"\",\"lv2\":\"\",\"lv2Name\":\"\",\"lv3\":\"\",\"lv3Name\":\"\",\"l1\":\"\",\"l1Name\":\"\",\"l2\":\"\",\"l2Name\":\"\",\"specDept\":\"\",\"specDeptName\":\"\",\"category\":\"\",\"categoryName\":\"\"}";
        dmFocPageInfoVO.setSaveThreshold(saveThreshold);
        dmFocPageInfoVO.setCaliberFlag("C");
        dmFocPageInfoVO.setCostType("P");
        dmFocPageInfoVO.setTablePreFix("dm_foc");
        UserVO currentUser = new UserVO();
        mockStatic(UserInfoUtils.class);
        RoleVO currentRole = new RoleVO();
        currentRole.setRoleId(1213);
        currentRole.setRoleName("ICT_Analysis");
        currentUser.setCurrentRole(currentRole);

        List<ProgramVO> program = new ArrayList<>();
        ProgramVO programVO = new ProgramVO();
        List<ProgramItemVO> programItemVOList = new ArrayList<>();
        ProgramItemVO programItemVO = new ProgramItemVO();
        List<KeyValuePairVO> values = new ArrayList<>();
        KeyValuePairVO keyValuePairVO = new KeyValuePairVO();
        keyValuePairVO.setKey("211414_LV1");
        values.add(keyValuePairVO);
        programItemVO.setValues(values);
        programItemVOList.add(programItemVO);
        DimensionDefinitionVO dimension = new DimensionDefinitionVO();
        dimension.setDimensionCode("getDimensionWithTree");
        programItemVO.setDimension(dimension);
        programVO.setItems(programItemVOList);
        program.add(programVO);
        currentUser.setCurrentPrograms(program);
        when(UserInfoUtils.class, "getCurrentUser").thenReturn(currentUser);
        List<ViewInfoVO> allProdDimensionList=new ArrayList<>();
        ViewInfoVO viewInfoVO2 = new ViewInfoVO();
        viewInfoVO2.setLv1ProdRndTeamCode("1142A");
        viewInfoVO2.setLv2ProdRndTeamCode("2311S");
        viewInfoVO2.setL3CegCode("3315D");
        allProdDimensionList.add(viewInfoVO2);

        PowerMockito.doReturn(allProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList("P","dm_foc");

        Boolean permissionFlag = commonService.hasRolePermission(dmFocPageInfoVO);
        Assert.assertEquals(permissionFlag,true);
    }

    @Test
    public void setProdRndTeamCode() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setViewFlag("1");
        monthAnalysisVO.setParentLevel("ICT");
        commonService.setProdRndTeamCode(monthAnalysisVO);
        Assert.assertNotNull(true);
    }

    @Test
    public void setProdRndTeamCode11Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setViewFlag("1");
        monthAnalysisVO.setParentLevel("CATEGORY");
        commonService.setProdRndTeamCode(monthAnalysisVO);
        Assert.assertNotNull(true);
    }

    @Test
    public void setProdRndTeamCode1Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setParentLevel("ICT");
        commonService.setProdRndTeamCode(monthAnalysisVO);
        Assert.assertNotNull(true);
    }


    @Test
    public void setProdRndTeamCode2Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setViewFlag("2");
        monthAnalysisVO.setParentLevel("ICT");
        commonService.setProdRndTeamCode(monthAnalysisVO);
        Assert.assertNotNull(true);
    }

    @Test
    public void setProdRndTeamCode21Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setViewFlag("2");
        monthAnalysisVO.setParentLevel("LV1");
        commonService.setProdRndTeamCode(monthAnalysisVO);
        Assert.assertNotNull(true);
    }

    @Test
    public void setProdRndTeamCode22Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setParentLevel("LV1");
        commonService.setProdRndTeamCode(monthAnalysisVO);
        Assert.assertNotNull(true);
    }

    @Test
    public void setProdRndTeamCode3Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setViewFlag("3");
        monthAnalysisVO.setParentLevel("LV2");
        commonService.setProdRndTeamCode(monthAnalysisVO);
        Assert.assertNotNull(true);
    }

    @Test
    public void setProdRndTeamCode31Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setViewFlag("3");
        monthAnalysisVO.setParentLevel("ICT");
        commonService.setProdRndTeamCode(monthAnalysisVO);
        Assert.assertNotNull(true);
    }

    @Test
    public void setProdRndTeamCode32Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setViewFlag("3");
        monthAnalysisVO.setParentLevel("LV1");
        commonService.setProdRndTeamCode(monthAnalysisVO);
        Assert.assertNotNull(true);
    }

    @Test
    public void setProdRndTeamCode33Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setViewFlag("3");
        monthAnalysisVO.setParentLevel("LV5");
        commonService.setProdRndTeamCode(monthAnalysisVO);
        Assert.assertNotNull(true);
    }

    @Test
    public void setProdRndTeamCode4Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setViewFlag("4");
        monthAnalysisVO.setParentLevel("LV2");
        commonService.setProdRndTeamCode(monthAnalysisVO);
        Assert.assertNotNull(true);
    }

    @Test
    public void setProdRndTeamCode41Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setViewFlag("4");
        monthAnalysisVO.setParentLevel("LV1");
        commonService.setProdRndTeamCode(monthAnalysisVO);
        Assert.assertNotNull(true);
    }

    @Test
    public void setProdRndTeamCode42Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setViewFlag("4");
        monthAnalysisVO.setParentLevel("ICT");
        commonService.setProdRndTeamCode(monthAnalysisVO);
        Assert.assertNotNull(true);
    }

    @Test
    public void setProdRndTeamCode43Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setViewFlag("4");
        monthAnalysisVO.setParentLevel("Lv5");
        commonService.setProdRndTeamCode(monthAnalysisVO);
        Assert.assertNotNull(true);
    }

    @Test
    public void getDimensionList() throws Exception {
        UserVO currentUser = new UserVO();
        mockStatic(UserInfoUtils.class);
        RoleVO currentRole = new RoleVO();
        currentRole.setRoleId(1213);
        currentRole.setRoleName("ICT_Analysis");
        currentUser.setCurrentRole(currentRole);
        List<ProgramVO> program = new ArrayList<>();
        ProgramVO programVO = new ProgramVO();
        List<ProgramItemVO> programItemVOList = new ArrayList<>();
        ProgramItemVO programItemVO = new ProgramItemVO();
        List<KeyValuePairVO> values = new ArrayList<>();
        KeyValuePairVO keyValuePairVO = new KeyValuePairVO();
        keyValuePairVO.setKey("111");
        values.add(keyValuePairVO);
        programItemVO.setValues(values);
        programItemVOList.add(programItemVO);
        programVO.setItems(programItemVOList);
        DimensionDefinitionVO dimension = new DimensionDefinitionVO();
        dimension.setDimensionCode("getDimensionWithTree");
        programItemVO.setDimension(dimension);
        program.add(programVO);
        currentUser.setCurrentPrograms(program);
        when(UserInfoUtils.class, "getCurrentUser").thenReturn(currentUser);
        List<ViewInfoVO> lv1ProdVOList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setLv1ProdRndTeamCode("104364");
        lv1ProdVOList.add(viewInfoVO);
        List<ViewInfoVO> lv2ProdVOList = new ArrayList<>();
        ViewInfoVO lv2ViewInfoVO = new ViewInfoVO();
        lv2ViewInfoVO.setLv2ProdRndTeamCode("104344");
        lv2ProdVOList.add(lv2ViewInfoVO);
        List<DmFocViewInfoVO> currentPrograms = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setViewFlag("0");
        dmFocViewInfoVO.setProdRndTeamCode("104364");
        currentPrograms.add(dmFocViewInfoVO);

        DmFocViewInfoVO dmFocViewInfoVO1 = new DmFocViewInfoVO();
        dmFocViewInfoVO1.setViewFlag("1");
        dmFocViewInfoVO1.setProdRndTeamCode("104364");
        currentPrograms.add(dmFocViewInfoVO1);

        DmFocViewInfoVO dmFocViewInfoVO2 = new DmFocViewInfoVO();
        dmFocViewInfoVO2.setViewFlag("2");
        dmFocViewInfoVO2.setProdRndTeamCode("104344");
        currentPrograms.add(dmFocViewInfoVO2);

        List<ViewInfoVO> allProdDimensionList=new ArrayList<>();
        ViewInfoVO viewInfoVO2 = new ViewInfoVO();
        viewInfoVO2.setLv1ProdRndTeamCode("1142A");
        viewInfoVO2.setLv2ProdRndTeamCode("2311S");
        viewInfoVO2.setL3CegCode("3315D");
        allProdDimensionList.add(viewInfoVO);

        PowerMockito.doReturn(allProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList("P","dm_foc");
        DataPermissionsVO dimensionList = commonService.getDimensionList("P","dm_foc","ICT");
        Assert.assertNotNull(dimensionList);
    }

    @Test
    public void getDataDictionaryByPathTest() throws Exception {
        ResultDataVO result = commonService.getDataDictionaryByPath("");
        Assert.assertNotNull(result);
    }

    @Test
    public void getGroupCnName() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("LV0");
        monthAnalysisVO.setIndustryOrg("ICT");
        String groupCnName = commonService.getGroupCnName(monthAnalysisVO);
        Assert.assertNotNull(groupCnName);
    }

    @Test
    public void getGroupCnName1Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("LV1");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> lv1ProdRdTeamCnName = new ArrayList<>();
        lv1ProdRdTeamCnName.add("光");
        monthAnalysisVO.setLv1ProdRdTeamCnName(lv1ProdRdTeamCnName);
        List<String> lv2ProdRdTeamCnName = new ArrayList<>();
        lv2ProdRdTeamCnName.add("TDD");
        monthAnalysisVO.setLv2ProdRdTeamCnName(lv2ProdRdTeamCnName);
        List<String> lv3ProdRdTeamCnName = new ArrayList<>();
        lv3ProdRdTeamCnName.add("TDD-NR");
        monthAnalysisVO.setLv3ProdRdTeamCnName(lv3ProdRdTeamCnName);
        String groupCnName = commonService.getGroupCnName(monthAnalysisVO);
        Assert.assertNotNull(groupCnName);
        monthAnalysisVO.setGranularityType("D");
        groupCnName = commonService.getGroupCnName(monthAnalysisVO);
        Assert.assertNotNull(groupCnName);
    }

    @Test
    public void getGroupCnName2Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("LV2");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> lv1ProdRdTeamCnName = new ArrayList<>();
        lv1ProdRdTeamCnName.add("光");
        monthAnalysisVO.setLv1ProdRdTeamCnName(lv1ProdRdTeamCnName);
        List<String> lv2ProdRdTeamCnName = new ArrayList<>();
        lv2ProdRdTeamCnName.add("TDD");
        monthAnalysisVO.setLv2ProdRdTeamCnName(lv2ProdRdTeamCnName);
        List<String> lv3ProdRdTeamCnName = new ArrayList<>();
        lv3ProdRdTeamCnName.add("TDD-NR");
        monthAnalysisVO.setLv3ProdRdTeamCnName(lv3ProdRdTeamCnName);
        String groupCnName = commonService.getGroupCnName(monthAnalysisVO);
        Assert.assertNotNull(groupCnName);
    }

    @Test
    public void getGroupCnName3Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("LV3");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> lv1ProdRdTeamCnName = new ArrayList<>();
        lv1ProdRdTeamCnName.add("光");
        monthAnalysisVO.setLv1ProdRdTeamCnName(lv1ProdRdTeamCnName);
        List<String> lv2ProdRdTeamCnName = new ArrayList<>();
        lv2ProdRdTeamCnName.add("TDD");
        monthAnalysisVO.setLv2ProdRdTeamCnName(lv2ProdRdTeamCnName);
        List<String> lv3ProdRdTeamCnName = new ArrayList<>();
        lv3ProdRdTeamCnName.add("TDD-NR");
        monthAnalysisVO.setLv3ProdRdTeamCnName(lv3ProdRdTeamCnName);
        String groupCnName = commonService.getGroupCnName(monthAnalysisVO);
        Assert.assertNotNull(groupCnName);
    }

    @Test
    public void getGroupCnName4Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setViewFlag("0");
        String groupCnName = commonService.getGroupCnName(monthAnalysisVO);
        Assert.assertNotNull(groupCnName);
    }

    @Test
    public void getGroupCnName41Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setViewFlag("1");
        List<String> lv1ProdRdTeamCnName = new ArrayList<>();
        lv1ProdRdTeamCnName.add("光");
        monthAnalysisVO.setLv1ProdRdTeamCnName(lv1ProdRdTeamCnName);
        List<String> lv2ProdRdTeamCnName = new ArrayList<>();
        lv2ProdRdTeamCnName.add("TDD");
        monthAnalysisVO.setLv2ProdRdTeamCnName(lv2ProdRdTeamCnName);
        List<String> lv3ProdRdTeamCnName = new ArrayList<>();
        lv3ProdRdTeamCnName.add("TDD-NR");
        monthAnalysisVO.setLv3ProdRdTeamCnName(lv3ProdRdTeamCnName);
        String groupCnName = commonService.getGroupCnName(monthAnalysisVO);
        Assert.assertNotNull(groupCnName);
    }

    @Test
    public void getGroupCnName42Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setViewFlag("2");
        List<String> lv1ProdRdTeamCnName = new ArrayList<>();
        lv1ProdRdTeamCnName.add("光");
        monthAnalysisVO.setLv1ProdRdTeamCnName(lv1ProdRdTeamCnName);
        List<String> lv2ProdRdTeamCnName = new ArrayList<>();
        lv2ProdRdTeamCnName.add("TDD");
        monthAnalysisVO.setLv2ProdRdTeamCnName(lv2ProdRdTeamCnName);
        List<String> lv3ProdRdTeamCnName = new ArrayList<>();
        lv3ProdRdTeamCnName.add("TDD-NR");
        monthAnalysisVO.setLv3ProdRdTeamCnName(lv3ProdRdTeamCnName);
        String groupCnName = commonService.getGroupCnName(monthAnalysisVO);
        Assert.assertNotNull(groupCnName);
    }

    @Test
    public void getGroupCnName43Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setViewFlag("3");
        List<String> lv1ProdRdTeamCnName = new ArrayList<>();
        lv1ProdRdTeamCnName.add("光");
        monthAnalysisVO.setLv1ProdRdTeamCnName(lv1ProdRdTeamCnName);
        List<String> lv2ProdRdTeamCnName = new ArrayList<>();
        lv2ProdRdTeamCnName.add("TDD");
        monthAnalysisVO.setLv2ProdRdTeamCnName(lv2ProdRdTeamCnName);
        List<String> lv3ProdRdTeamCnName = new ArrayList<>();
        lv3ProdRdTeamCnName.add("TDD-NR");
        monthAnalysisVO.setLv3ProdRdTeamCnName(lv3ProdRdTeamCnName);
        String groupCnName = commonService.getGroupCnName(monthAnalysisVO);
        Assert.assertNotNull(groupCnName);
    }

    @Test
    public void getGroupCnName5Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setViewFlag("0");
        String groupCnName = commonService.getGroupCnName(monthAnalysisVO);
        Assert.assertNotNull(groupCnName);
    }

    @Test
    public void getGroupCnName51Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setViewFlag("1");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> lv1ProdRdTeamCnName = new ArrayList<>();
        lv1ProdRdTeamCnName.add("光");
        monthAnalysisVO.setLv1ProdRdTeamCnName(lv1ProdRdTeamCnName);
        String groupCnName = commonService.getGroupCnName(monthAnalysisVO);
        Assert.assertNotNull(groupCnName);
    }

    @Test
    public void getGroupCnName52Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setViewFlag("2");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> lv1ProdRdTeamCnName = new ArrayList<>();
        lv1ProdRdTeamCnName.add("光");
        monthAnalysisVO.setLv1ProdRdTeamCnName(lv1ProdRdTeamCnName);
        List<String> lv2ProdRdTeamCnName = new ArrayList<>();
        lv2ProdRdTeamCnName.add("TDD");
        monthAnalysisVO.setLv2ProdRdTeamCnName(lv2ProdRdTeamCnName);
        String groupCnName = commonService.getGroupCnName(monthAnalysisVO);
        Assert.assertNotNull(groupCnName);
    }

    @Test
    public void getGroupCnName53Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setViewFlag("3");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> lv1ProdRdTeamCnName = new ArrayList<>();
        lv1ProdRdTeamCnName.add("光");
        monthAnalysisVO.setLv1ProdRdTeamCnName(lv1ProdRdTeamCnName);
        List<String> lv2ProdRdTeamCnName = new ArrayList<>();
        lv2ProdRdTeamCnName.add("TDD");
        monthAnalysisVO.setLv2ProdRdTeamCnName(lv2ProdRdTeamCnName);
        List<String> lv3ProdRdTeamCnName = new ArrayList<>();
        lv3ProdRdTeamCnName.add("TDD_NR");
        monthAnalysisVO.setLv3ProdRdTeamCnName(lv3ProdRdTeamCnName);
        String groupCnName = commonService.getGroupCnName(monthAnalysisVO);
        Assert.assertNotNull(groupCnName);
    }

    @Test
    public void getGroupCnName6Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("ITEM");
        String groupCnName = commonService.getGroupCnName(monthAnalysisVO);
        Assert.assertNotNull(groupCnName);
    }

    @Test
    public void getGroupCnName7Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setGroupLevel("ITEM");
        String groupCnName = commonService.getGroupCnName(monthAnalysisVO);
        Assert.assertNotNull(groupCnName);
    }

    @Test
    public void getGroupCnName8Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setIndustryOrg("ICT");
        String groupCnName = commonService.getGroupCnName(monthAnalysisVO);
        Assert.assertNotNull(groupCnName);
    }

    @Test
    public void getGroupCnName81Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setViewFlag("1");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> lv1ProdRdTeamCnName = new ArrayList<>();
        lv1ProdRdTeamCnName.add("光");
        monthAnalysisVO.setLv1ProdRdTeamCnName(lv1ProdRdTeamCnName);
        String groupCnName = commonService.getGroupCnName(monthAnalysisVO);
        Assert.assertNotNull(groupCnName);
    }

    @Test
    public void getGroupCnName82Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setViewFlag("2");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> lv1ProdRdTeamCnName = new ArrayList<>();
        lv1ProdRdTeamCnName.add("光");
        monthAnalysisVO.setLv1ProdRdTeamCnName(lv1ProdRdTeamCnName);
        List<String> lv2ProdRdTeamCnName = new ArrayList<>();
        lv2ProdRdTeamCnName.add("TDD");
        monthAnalysisVO.setLv2ProdRdTeamCnName(lv2ProdRdTeamCnName);
        String groupCnName = commonService.getGroupCnName(monthAnalysisVO);
        Assert.assertNotNull(groupCnName);
    }

    @Test
    public void getGroupCnName83Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setViewFlag("3");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> lv1ProdRdTeamCnName = new ArrayList<>();
        lv1ProdRdTeamCnName.add("光");
        monthAnalysisVO.setLv1ProdRdTeamCnName(lv1ProdRdTeamCnName);
        List<String> lv2ProdRdTeamCnName = new ArrayList<>();
        lv2ProdRdTeamCnName.add("TDD");
        monthAnalysisVO.setLv2ProdRdTeamCnName(lv2ProdRdTeamCnName);
        List<String> l1NameList = new ArrayList<>();
        l1NameList.add("其他");
        monthAnalysisVO.setL1NameList(l1NameList);
        String groupCnName = commonService.getGroupCnName(monthAnalysisVO);
        Assert.assertNotNull(groupCnName);
    }

    @Test
    public void getGroupCnName84Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setViewFlag("4");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> lv1ProdRdTeamCnName = new ArrayList<>();
        lv1ProdRdTeamCnName.add("光");
        monthAnalysisVO.setLv1ProdRdTeamCnName(lv1ProdRdTeamCnName);
        List<String> lv2ProdRdTeamCnName = new ArrayList<>();
        lv2ProdRdTeamCnName.add("TDD");
        monthAnalysisVO.setLv2ProdRdTeamCnName(lv2ProdRdTeamCnName);
        List<String> l1NameList = new ArrayList<>();
        l1NameList.add("其他");
        monthAnalysisVO.setL1NameList(l1NameList);
        List<String> l2NameList = new ArrayList<>();
        l2NameList.add("其他");
        monthAnalysisVO.setL2NameList(l2NameList);
        String groupCnName = commonService.getGroupCnName(monthAnalysisVO);
        Assert.assertNotNull(groupCnName);
    }

    @Test
    public void getGroupCnName9Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setIndustryOrg("ICT");
        String groupCnName = commonService.getGroupCnName(monthAnalysisVO);
        Assert.assertNotNull(groupCnName);
    }

    @Test
    public void getGroupCnName91Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setViewFlag("1");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> lv1ProdRdTeamCnName = new ArrayList<>();
        lv1ProdRdTeamCnName.add("光");
        monthAnalysisVO.setLv1ProdRdTeamCnName(lv1ProdRdTeamCnName);
        String groupCnName = commonService.getGroupCnName(monthAnalysisVO);
        Assert.assertNotNull(groupCnName);
    }

    @Test
    public void getGroupCnName92Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setViewFlag("2");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> lv1ProdRdTeamCnName = new ArrayList<>();
        lv1ProdRdTeamCnName.add("光");
        monthAnalysisVO.setLv1ProdRdTeamCnName(lv1ProdRdTeamCnName);
        List<String> lv2ProdRdTeamCnName = new ArrayList<>();
        lv2ProdRdTeamCnName.add("TDD");
        monthAnalysisVO.setLv2ProdRdTeamCnName(lv2ProdRdTeamCnName);
        String groupCnName = commonService.getGroupCnName(monthAnalysisVO);
        Assert.assertNotNull(groupCnName);
    }

    @Test
    public void getGroupCnName93Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setViewFlag("3");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> lv1ProdRdTeamCnName = new ArrayList<>();
        lv1ProdRdTeamCnName.add("光");
        monthAnalysisVO.setLv1ProdRdTeamCnName(lv1ProdRdTeamCnName);
        List<String> lv2ProdRdTeamCnName = new ArrayList<>();
        lv2ProdRdTeamCnName.add("TDD");
        monthAnalysisVO.setLv2ProdRdTeamCnName(lv2ProdRdTeamCnName);
        List<String> l1NameList = new ArrayList<>();
        l1NameList.add("其他");
        monthAnalysisVO.setL1NameList(l1NameList);
        List<String> l2NameList = new ArrayList<>();
        l2NameList.add("其他");
        monthAnalysisVO.setL2NameList(l2NameList);
        String groupCnName = commonService.getGroupCnName(monthAnalysisVO);
        Assert.assertNotNull(groupCnName);
    }

    @Test
    public void getGroupCnName94Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setViewFlag("4");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> lv1ProdRdTeamCnName = new ArrayList<>();
        lv1ProdRdTeamCnName.add("光");
        monthAnalysisVO.setLv1ProdRdTeamCnName(lv1ProdRdTeamCnName);
        List<String> lv2ProdRdTeamCnName = new ArrayList<>();
        lv2ProdRdTeamCnName.add("TDD");
        monthAnalysisVO.setLv2ProdRdTeamCnName(lv2ProdRdTeamCnName);
        List<String> l1NameList = new ArrayList<>();
        l1NameList.add("其他");
        monthAnalysisVO.setL1NameList(l1NameList);
        List<String> l2NameList = new ArrayList<>();
        l2NameList.add("其他");
        monthAnalysisVO.setL2NameList(l2NameList);
        String groupCnName = commonService.getGroupCnName(monthAnalysisVO);
        Assert.assertNotNull(groupCnName);
    }

    @Test
    public void getGroupCnName10Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setGroupLevel("LV0");
        monthAnalysisVO.setIndustryOrg("ICT");
        String groupCnName = commonService.getGroupCnName(monthAnalysisVO);
        Assert.assertNotNull(groupCnName);
    }

    @Test
    public void getGroupCnName11Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setGroupLevel("LV1");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> lv1ProdRdTeamCnName = new ArrayList<>();
        lv1ProdRdTeamCnName.add("光");
        monthAnalysisVO.setLv1ProdRdTeamCnName(lv1ProdRdTeamCnName);
        List<String> lv2ProdRdTeamCnName = new ArrayList<>();
        lv2ProdRdTeamCnName.add("TDD");
        monthAnalysisVO.setLv2ProdRdTeamCnName(lv2ProdRdTeamCnName);
        String groupCnName = commonService.getGroupCnName(monthAnalysisVO);
        Assert.assertNotNull(groupCnName);
    }

    @Test
    public void getGroupCnName12Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setGroupLevel("LV2");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> lv1ProdRdTeamCnName = new ArrayList<>();
        lv1ProdRdTeamCnName.add("光");
        monthAnalysisVO.setLv1ProdRdTeamCnName(lv1ProdRdTeamCnName);
        List<String> lv2ProdRdTeamCnName = new ArrayList<>();
        lv2ProdRdTeamCnName.add("TDD");
        monthAnalysisVO.setLv2ProdRdTeamCnName(lv2ProdRdTeamCnName);
        String groupCnName = commonService.getGroupCnName(monthAnalysisVO);
        Assert.assertNotNull(groupCnName);
    }

    @Test
    public void getGroupCnName13Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setGroupLevel("L1");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> lv1ProdRdTeamCnName = new ArrayList<>();
        lv1ProdRdTeamCnName.add("光");
        monthAnalysisVO.setLv1ProdRdTeamCnName(lv1ProdRdTeamCnName);
        List<String> lv2ProdRdTeamCnName = new ArrayList<>();
        lv2ProdRdTeamCnName.add("TDD");
        monthAnalysisVO.setLv2ProdRdTeamCnName(lv2ProdRdTeamCnName);
        List<String> l1NameList = new ArrayList<>();
        l1NameList.add("其他");
        monthAnalysisVO.setL1NameList(l1NameList);
        List<String> l2NameList = new ArrayList<>();
        l2NameList.add("其他");
        monthAnalysisVO.setL2NameList(l2NameList);
        String groupCnName = commonService.getGroupCnName(monthAnalysisVO);
        Assert.assertNotNull(groupCnName);
    }

    @Test
    public void getGroupCnName14Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setGroupLevel("L2");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> lv1ProdRdTeamCnName = new ArrayList<>();
        lv1ProdRdTeamCnName.add("光");
        monthAnalysisVO.setLv1ProdRdTeamCnName(lv1ProdRdTeamCnName);
        List<String> lv2ProdRdTeamCnName = new ArrayList<>();
        lv2ProdRdTeamCnName.add("TDD");
        monthAnalysisVO.setLv2ProdRdTeamCnName(lv2ProdRdTeamCnName);
        List<String> l1NameList = new ArrayList<>();
        l1NameList.add("其他");
        monthAnalysisVO.setL1NameList(l1NameList);
        List<String> l2NameList = new ArrayList<>();
        l2NameList.add("其他");
        monthAnalysisVO.setL2NameList(l2NameList);
        String groupCnName = commonService.getGroupCnName(monthAnalysisVO);
        Assert.assertNotNull(groupCnName);
    }

    @Test
    public void setViewFlagValueWithLookUpTest() throws ApplicationException {
        DmFocViewInfoVO viewInfoVO = JSONObject.parseObject(json.getString("DmFocViewInfoVO"),DmFocViewInfoVO.class);
        List<LookupItemVO> lookups = JSONObject.parseArray(json.getString("List<LookupItemVO>"),LookupItemVO.class);
        when(lookupItemQueryService.findItemListByClassify(any(String.class))).thenReturn(lookups);
        viewInfoVO.setGranularityType("D");
        commonService.setViewFlagValueWithLookUp(viewInfoVO);
        assertThatNoException();
        viewInfoVO.setGranularityType("P");
        commonService.setViewFlagValueWithLookUp(viewInfoVO);
        assertThatNoException();
        viewInfoVO.setGranularityType("U");
        commonService.setViewFlagValueWithLookUp(viewInfoVO);
        assertThatNoException();
        viewInfoVO.setViewFlag("ddd");
        viewInfoVO.setGranularityType("D");
        commonService.setViewFlagValueWithLookUp(viewInfoVO);
        assertThatNoException();
        viewInfoVO.setGranularityType("P");
        commonService.setViewFlagValueWithLookUp(viewInfoVO);
        assertThatNoException();
        viewInfoVO.setGranularityType("U");
        commonService.setViewFlagValueWithLookUp(viewInfoVO);
        assertThatNoException();
        viewInfoVO.setGranularityType("U");
        viewInfoVO.setCostType("M");
        commonService.setViewFlagValueWithLookUp(viewInfoVO);
        assertThatNoException();
        viewInfoVO.setGranularityType("U");
        viewInfoVO.setCostType("P");
        commonService.setViewFlagValueWithLookUp(viewInfoVO);
        assertThatNoException();

        viewInfoVO.setGranularityType("P");
        viewInfoVO.setCostType("M");
        commonService.setViewFlagValueWithLookUp(viewInfoVO);
        assertThatNoException();
        viewInfoVO.setGranularityType("P");
        viewInfoVO.setCostType("P");
        commonService.setViewFlagValueWithLookUp(viewInfoVO);
        assertThatNoException();

        viewInfoVO.setGranularityType("D");
        viewInfoVO.setCostType("M");
        commonService.setViewFlagValueWithLookUp(viewInfoVO);
        assertThatNoException();
        viewInfoVO.setGranularityType("D");
        viewInfoVO.setCostType("P");
        commonService.setViewFlagValueWithLookUp(viewInfoVO);
        assertThatNoException();
    }

    @Test
    public void getDimensionCnNameTest() throws Exception {
        MonthAnalysisVO analysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"),MonthAnalysisVO.class);
        analysisVO.setGroupLevel("LV0");
        String result = Whitebox.invokeMethod(commonService, "getDimensionCnName", analysisVO, "test");
        Assertions.assertNotNull(result);
        analysisVO.setGroupLevel("LV1");
        result = Whitebox.invokeMethod(commonService, "getDimensionCnName", analysisVO, "test");
        Assertions.assertNotNull(result);
        analysisVO.setGroupLevel("LV2");
        result = Whitebox.invokeMethod(commonService, "getDimensionCnName", analysisVO, "test");
        Assertions.assertNotNull(result);
        analysisVO.setGroupLevel("LV3");
        result = Whitebox.invokeMethod(commonService, "getDimensionCnName", analysisVO, "test");
        Assertions.assertNotNull(result);
        analysisVO.setGroupLevel("DIMENSION");
        result = Whitebox.invokeMethod(commonService, "getDimensionCnName", analysisVO, "test");
        Assertions.assertNotNull(result);
        analysisVO.setGroupLevel("SUBCATEGORY");
        result = Whitebox.invokeMethod(commonService, "getDimensionCnName", analysisVO, "test");
        Assertions.assertNotNull(result);
        analysisVO.setGroupLevel("SUB_DETAIL");
        result = Whitebox.invokeMethod(commonService, "getDimensionCnName", analysisVO, "test");
        Assertions.assertNotNull(result);
        analysisVO.setGroupLevel("CEG");
        result = Whitebox.invokeMethod(commonService, "getDimensionCnName", analysisVO, "test");
        Assertions.assertNotNull(result);
        analysisVO.setGroupLevel("MODL");
        result = Whitebox.invokeMethod(commonService, "getDimensionCnName", analysisVO, "test");
        Assertions.assertNotNull(result);
        analysisVO.setGroupLevel("CATEGORY");
        result = Whitebox.invokeMethod(commonService, "getDimensionCnName", analysisVO, "test");
        Assertions.assertNotNull(result);
        analysisVO.setGroupLevel("ITEM");
        result = Whitebox.invokeMethod(commonService, "getDimensionCnName", analysisVO, "test");
        Assertions.assertNotNull(result);
    }

    @Test
    public void getPftCnNameTest() throws Exception {
        MonthAnalysisVO analysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"),MonthAnalysisVO.class);
        analysisVO.setGroupLevel("MODL");
        String result = Whitebox.invokeMethod(commonService, "getPftCnName", analysisVO, "test");
        Assertions.assertNotNull(result);
    }

    @Test
    public void getUniversalCnNameTest() throws Exception {
        MonthAnalysisVO analysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"),MonthAnalysisVO.class);
        analysisVO.setGroupLevel("MODL");
        String result = Whitebox.invokeMethod(commonService, "getUniversalCnName", analysisVO, "test");
        Assertions.assertNotNull(result);
    }

    @Test
    public void lv3LevelPathTest() throws Exception {
        MonthAnalysisVO analysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"),MonthAnalysisVO.class);
        analysisVO.getLv3ProdRdTeamCnName().add("dddd");
        String result = Whitebox.invokeMethod(commonService, "lv3LevelPath", analysisVO, "test");
        Assertions.assertNotNull(result);
    }

    @Test
    public void lv2LevelPathTest() throws Exception {
        MonthAnalysisVO analysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"),MonthAnalysisVO.class);
        analysisVO.getLv2ProdRdTeamCnName().add("dddd");
        String result = Whitebox.invokeMethod(commonService, "lv2LevelPath", analysisVO, "test");
        Assertions.assertNotNull(result);
    }

    @Test
    public void lv1LevelPathTest() throws Exception {
        MonthAnalysisVO analysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"),MonthAnalysisVO.class);
        analysisVO.getLv1ProdRdTeamCnName().add("dddd");
        String result = Whitebox.invokeMethod(commonService, "lv1LevelPath", analysisVO, "test");
        Assertions.assertNotNull(result);
    }

    @Test
    public void l1LevelPathTest() throws Exception {
        MonthAnalysisVO analysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"),MonthAnalysisVO.class);
        analysisVO.getL1NameList().add("dddd");
        String result = Whitebox.invokeMethod(commonService, "l1LevelPath", analysisVO);
        Assertions.assertNotNull(result);
    }

    @Test
    public void l2LevelPathTest() throws Exception {
        MonthAnalysisVO analysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"),MonthAnalysisVO.class);
        analysisVO.getL2NameList().add("dddd");
        String result = Whitebox.invokeMethod(commonService, "l2LevelPath", analysisVO);
        Assertions.assertNotNull(result);
    }

    @Test
    public void setDimensionCnNameTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"), MonthAnalysisVO.class);
        monthAnalysisVO.setViewFlag("2");
        String result = Whitebox.invokeMethod(commonService, "setDimensionCnName", monthAnalysisVO, " ", " ",
                IndustryIndexEnum.VIEW_FLAG_D.VIEW1, IndustryIndexEnum.VIEW_FLAG_D.VIEW2,
                IndustryIndexEnum.VIEW_FLAG_D.VIEW3, IndustryIndexEnum.VIEW_FLAG_D.VIEW9, " ");
        Assertions.assertNotNull(result);
        monthAnalysisVO.setViewFlag("1");
        result = Whitebox.invokeMethod(commonService, "setDimensionCnName", monthAnalysisVO, " ", " ",
                IndustryIndexEnum.VIEW_FLAG_D.VIEW1, IndustryIndexEnum.VIEW_FLAG_D.VIEW2,
                IndustryIndexEnum.VIEW_FLAG_D.VIEW3, IndustryIndexEnum.VIEW_FLAG_D.VIEW9," ");
        Assertions.assertNotNull(result);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.getDimensionCnName().clear();
        result = Whitebox.invokeMethod(commonService, "setDimensionCnName", monthAnalysisVO, " ", " ",
                IndustryIndexEnum.VIEW_FLAG_D.VIEW1, IndustryIndexEnum.VIEW_FLAG_D.VIEW2,
                IndustryIndexEnum.VIEW_FLAG_D.VIEW3, IndustryIndexEnum.VIEW_FLAG_D.VIEW9," ");
        Assertions.assertNotNull(result);
    }

    @Test
    public void getDimensionSubCateLevelPathTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"), MonthAnalysisVO.class);

        monthAnalysisVO.setViewFlag("8");
        String result = Whitebox.invokeMethod(commonService, "getDimensionSubCateLevelPath", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
        monthAnalysisVO.setViewFlag("7");
        result = Whitebox.invokeMethod(commonService, "getDimensionSubCateLevelPath", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
        monthAnalysisVO.setViewFlag("6");
        result = Whitebox.invokeMethod(commonService, "getDimensionSubCateLevelPath", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
        monthAnalysisVO.setViewFlag("5");
        result = Whitebox.invokeMethod(commonService, "getDimensionSubCateLevelPath", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
        monthAnalysisVO.setViewFlag("4");
        monthAnalysisVO.getDimensionSubCategoryCnName().add("ddd");
        result = Whitebox.invokeMethod(commonService, "getDimensionSubCateLevelPath", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
        monthAnalysisVO.setViewFlag("3");
        result = Whitebox.invokeMethod(commonService, "getDimensionSubCateLevelPath", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
        monthAnalysisVO.setViewFlag("2");
        result = Whitebox.invokeMethod(commonService, "getDimensionSubCateLevelPath", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
        monthAnalysisVO.setViewFlag("1");
        result = Whitebox.invokeMethod(commonService, "getDimensionSubCateLevelPath", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
        monthAnalysisVO.setViewFlag("0");
        result = Whitebox.invokeMethod(commonService, "getDimensionSubCateLevelPath", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
    }

    @Test
    public void getDimensionSubDetailLevelPathTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"), MonthAnalysisVO.class);
        monthAnalysisVO.setViewFlag("8");
        String result = Whitebox.invokeMethod(commonService, "getDimensionSubDetailLevelPath", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
        monthAnalysisVO.setViewFlag("5");
        monthAnalysisVO.getDimensionSubDetailCnName().add("ddd");
        result = Whitebox.invokeMethod(commonService, "getDimensionSubDetailLevelPath", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
        monthAnalysisVO.setViewFlag("2");
        result = Whitebox.invokeMethod(commonService, "getDimensionSubDetailLevelPath", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
    }

    @Test
    public void getDimensionCegCnNameTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"), MonthAnalysisVO.class);
        monthAnalysisVO.setViewFlag("8");
        String result = Whitebox.invokeMethod(commonService, "getDimensionCegCnName", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
        monthAnalysisVO.setViewFlag("7");
        result = Whitebox.invokeMethod(commonService, "getDimensionCegCnName", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
        monthAnalysisVO.setViewFlag("6");
        result = Whitebox.invokeMethod(commonService, "getDimensionCegCnName", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
        monthAnalysisVO.setViewFlag("5");
        result = Whitebox.invokeMethod(commonService, "getDimensionCegCnName", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
        monthAnalysisVO.setViewFlag("4");
        result = Whitebox.invokeMethod(commonService, "getDimensionCegCnName", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
        monthAnalysisVO.setViewFlag("3");
        result = Whitebox.invokeMethod(commonService, "getDimensionCegCnName", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
        monthAnalysisVO.setViewFlag("2");
        result = Whitebox.invokeMethod(commonService, "getDimensionCegCnName", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
        monthAnalysisVO.setViewFlag("1");
        result = Whitebox.invokeMethod(commonService, "getDimensionCegCnName", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
        monthAnalysisVO.setViewFlag("0");
        result = Whitebox.invokeMethod(commonService, "getDimensionCegCnName", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
    }

    @Test
    public void getUnLv2GroupCnNameTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"), MonthAnalysisVO.class);
        monthAnalysisVO.getLv2ProdRdTeamCnName().add("ddd");
        monthAnalysisVO.setViewFlag("6");
        String result = Whitebox.invokeMethod(commonService, "getUnLv2GroupCnName", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
        monthAnalysisVO.setViewFlag("5");
        result = Whitebox.invokeMethod(commonService, "getUnLv2GroupCnName", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
        monthAnalysisVO.setViewFlag("4");
        result = Whitebox.invokeMethod(commonService, "getUnLv2GroupCnName", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
    }

    @Test
    public void getUnLv1GroupCnNameTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"), MonthAnalysisVO.class);
        monthAnalysisVO.getLv1ProdRdTeamCnName().add("ddd");
        monthAnalysisVO.setViewFlag("6");
        String result = Whitebox.invokeMethod(commonService, "getUnLv1GroupCnName", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
        monthAnalysisVO.setViewFlag("5");
        result = Whitebox.invokeMethod(commonService, "getUnLv1GroupCnName", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
        monthAnalysisVO.setViewFlag("4");
        result = Whitebox.invokeMethod(commonService, "getUnLv1GroupCnName", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
    }

    @Test
    public void getPftCategoryNameTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"), MonthAnalysisVO.class);
        monthAnalysisVO.setViewFlag("5");
        String result = Whitebox.invokeMethod(commonService, "getPftCategoryName", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
        monthAnalysisVO.setViewFlag("4");
        result = Whitebox.invokeMethod(commonService, "getPftCategoryName", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
    }

    @Test
    public void getPftModlCnNameTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"), MonthAnalysisVO.class);
        monthAnalysisVO.setViewFlag("4");
        String result = Whitebox.invokeMethod(commonService, "getPftModlCnName", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
    }

    @Test
    public void getPftCegCnNameTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"), MonthAnalysisVO.class);
        monthAnalysisVO.setViewFlag("0");
        String result = Whitebox.invokeMethod(commonService, "getPftCegCnName", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
        monthAnalysisVO.setViewFlag("6");
        result = Whitebox.invokeMethod(commonService, "getPftCategoryName", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
        monthAnalysisVO.setViewFlag("5");
        result = Whitebox.invokeMethod(commonService, "getPftCategoryName", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
        monthAnalysisVO.setViewFlag("4");
        result = Whitebox.invokeMethod(commonService, "getPftCategoryName", monthAnalysisVO, "test");
        Assertions.assertNotNull(result);
    }

    @Test
    public void getDimensionListTest() throws Exception {
        mockStatic(UserInfoUtils.class);
        List<ViewInfoVO> viewInfoVOList = JSONObject.parseArray(json.getString("List<ViewInfoVO>"),ViewInfoVO.class);
        UserVO userVO = JSONObject.parseObject(json.getString("UserVO"),UserVO.class);
        when(UserInfoUtils.getCurrentUser()).thenReturn(userVO);
        when(dataDimensionService.getCurrentLv2ProdRndTeamList("P","dm_foc")).thenReturn(viewInfoVOList);
        DataPermissionsVO result = commonService.getDimensionList("P","dm_foc","ICT");
        Assertions.assertNotNull(result);

        userVO.getCurrentPrograms().forEach(obj -> obj.getItems().forEach(obj1 -> obj1.getValues().removeIf(obj2 ->
                "ALL".equals(obj2.getKey()))));
        viewInfoVOList.forEach(obj -> obj.setLv3ProdRndTeamCode("ddd"));
        result = commonService.getDimensionList("P","dm_foc","ICT");
        Assertions.assertNotNull(result);
        viewInfoVOList.forEach(obj -> obj.setLv2ProdRndTeamCode("ddd"));
        result = commonService.getDimensionList("P","dm_foc","ICT");
        Assertions.assertNotNull(result);
    }

    @Test
    public void hasRolePermissionTest() {
        mockStatic(UserInfoUtils.class);
        List<ViewInfoVO> viewInfoVOList = JSONObject.parseArray(json.getString("List<ViewInfoVO>"),ViewInfoVO.class);
        DmFocPageInfoVO dmFocPageInfoVO = JSONObject.parseObject(json.getString("DmFocPageInfoVO"),DmFocPageInfoVO.class);
        UserVO userVO = JSONObject.parseObject(json.getString("UserVO"),UserVO.class);
        userVO.getCurrentPrograms().forEach(obj -> obj.getItems().forEach(obj1 -> obj1.getValues().removeIf(obj2 ->
                "ALL".equals(obj2.getKey()))));
        viewInfoVOList.forEach(obj -> obj.setLv3ProdRndTeamCode("ddd"));
        viewInfoVOList.forEach(obj -> obj.setLv2ProdRndTeamCode("ddd"));
        viewInfoVOList.forEach(obj -> obj.setLv1ProdRndTeamCode("ddd"));
        viewInfoVOList.forEach(obj -> obj.setLv0ProdRndTeamCode("ddd"));
        when(UserInfoUtils.getCurrentUser()).thenReturn(userVO);
        when(dataDimensionService.getCurrentLv2ProdRndTeamList(dmFocPageInfoVO.getCostType(),dmFocPageInfoVO.getTablePreFix())).thenReturn(viewInfoVOList);
        Boolean result = commonService.hasRolePermission(dmFocPageInfoVO);
        Assertions.assertFalse(result);
    }

    @Test
    public void setProdRndTeamCodeTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"), MonthAnalysisVO.class);
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setViewFlag("4");
        monthAnalysisVO.setParentLevel("LV1");
        Whitebox.invokeMethod(commonService, "setProdRndTeamCode", monthAnalysisVO);
        assertThatNoException();
        monthAnalysisVO.setViewFlag("3");
        monthAnalysisVO.setParentLevel("ICT");
        Whitebox.invokeMethod(commonService, "setProdRndTeamCode", monthAnalysisVO);
        assertThatNoException();
        monthAnalysisVO.setParentLevel("DDD");
        Whitebox.invokeMethod(commonService, "setProdRndTeamCode", monthAnalysisVO);
        assertThatNoException();
        monthAnalysisVO.setViewFlag("2");
        Whitebox.invokeMethod(commonService, "setProdRndTeamCode", monthAnalysisVO);
        assertThatNoException();
    }

    @Test
    public void setProdTeamCodeWithView4Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"), MonthAnalysisVO.class);
        monthAnalysisVO.setGranularityType("D");
        boolean result = Whitebox.invokeMethod(commonService, "setProdTeamCodeWithView4", monthAnalysisVO, "8", "LV2");
        Assertions.assertTrue(result);
        result = Whitebox.invokeMethod(commonService, "setProdTeamCodeWithView4", monthAnalysisVO, "7", "LV1");
        Assertions.assertTrue(result);
        result = Whitebox.invokeMethod(commonService, "setProdTeamCodeWithView4", monthAnalysisVO, "6", "LV0");
        Assertions.assertTrue(result);
        result = Whitebox.invokeMethod(commonService, "setProdTeamCodeWithView4", monthAnalysisVO, "6", "ddd");
        Assertions.assertFalse(result);
        result = Whitebox.invokeMethod(commonService, "setProdTeamCodeWithView4", monthAnalysisVO, "5", "ddd");
        Assertions.assertFalse(result);
    }

    @Test
    public void setProdTeamCodeWithView3Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"), MonthAnalysisVO.class);
        monthAnalysisVO.setGranularityType("D");
        boolean result = Whitebox.invokeMethod(commonService, "setProdTeamCodeWithView3", monthAnalysisVO, "5", "LV0");
        Assertions.assertTrue(result);
        result = Whitebox.invokeMethod(commonService, "setProdTeamCodeWithView3", monthAnalysisVO, "4", "LV1");
        Assertions.assertTrue(result);
        result = Whitebox.invokeMethod(commonService, "setProdTeamCodeWithView3", monthAnalysisVO, "3", "LV0");
        Assertions.assertTrue(result);
        result = Whitebox.invokeMethod(commonService, "setProdTeamCodeWithView3", monthAnalysisVO, "6", "ddd");
        Assertions.assertFalse(result);
    }

    @Test
    public void setProdTeamCodeWithView2Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"), MonthAnalysisVO.class);
        monthAnalysisVO.setGranularityType("D");
        boolean result = Whitebox.invokeMethod(commonService, "setProdTeamCodeWithView2", monthAnalysisVO, "2", "LV0");
        Assertions.assertTrue(result);
        result = Whitebox.invokeMethod(commonService, "setProdTeamCodeWithView2", monthAnalysisVO, "1", "LV0");
        Assertions.assertTrue(result);
        result = Whitebox.invokeMethod(commonService, "setProdTeamCodeWithView2", monthAnalysisVO, "0", "LV0");
        Assertions.assertTrue(result);
        result = Whitebox.invokeMethod(commonService, "setProdTeamCodeWithView2", monthAnalysisVO, "0", "ddd");
        Assertions.assertFalse(result);
        result = Whitebox.invokeMethod(commonService, "setProdTeamCodeWithView2", monthAnalysisVO, "7", "ddd");
        Assertions.assertFalse(result);
    }

    @Test
    public void getManufactureGroupCnName() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("LV0");
        monthAnalysisVO.setIndustryOrg("ICT");
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName1T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("LV1");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setShippingObjectCnName("电器");
        monthAnalysisVO.setViewFlag("4");
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName11Test() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("LV1");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        list.add("1180A");
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setShippingObjectCnName("电器");
        monthAnalysisVO.setViewFlag("4");
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName12Test() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("LV1");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        list.add("1180A");
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setShippingObjectCnName("电器");
        monthAnalysisVO.setViewFlag("5");
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName13Test() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("LV1");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setShippingObjectCnName("电器");
        monthAnalysisVO.setViewFlag("5");
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName14Test() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("LV1");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setShippingObjectCnName("电器");
        monthAnalysisVO.setViewFlag("1");
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName15Test() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("LV1");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setShippingObjectCnName("电器");
        monthAnalysisVO.setViewFlag("0");
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName16Test() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("LV1");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setShippingObjectCnName("电器");
        monthAnalysisVO.setViewFlag("2");
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName17Test() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("LV1");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        list.add("1180S");
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setShippingObjectCnName("电器");
        monthAnalysisVO.setViewFlag("3");
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }


    @Test
    public void getManufactureGroupCnName2T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("LV2");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        list.add("1180S");
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setLv2ProdRdTeamCnName(list);
        monthAnalysisVO.setViewFlag("4");
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName3T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("LV3");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        list.add("1180S");
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setLv2ProdRdTeamCnName(list);
        monthAnalysisVO.setLv3ProdRdTeamCnName(list);
        monthAnalysisVO.setViewFlag("4");
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName4T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("SHIPPING_OBJECT");
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setViewFlag("0");
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName41T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("SHIPPING_OBJECT");
        monthAnalysisVO.setViewFlag("1");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        list.add("1180S");
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setLv2ProdRdTeamCnName(list);
        monthAnalysisVO.setLv3ProdRdTeamCnName(list);
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName42T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("SHIPPING_OBJECT");
        monthAnalysisVO.setViewFlag("2");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        list.add("1180S");
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setLv2ProdRdTeamCnName(list);
        monthAnalysisVO.setLv3ProdRdTeamCnName(list);
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName43T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("SHIPPING_OBJECT");
        monthAnalysisVO.setViewFlag("3");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        list.add("1180S");
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setLv2ProdRdTeamCnName(list);
        monthAnalysisVO.setLv3ProdRdTeamCnName(list);
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName44T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("SHIPPING_OBJECT");
        monthAnalysisVO.setViewFlag("4");
        monthAnalysisVO.setIndustryOrg("ICT");
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName45T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("SHIPPING_OBJECT");
        monthAnalysisVO.setViewFlag("5");
        monthAnalysisVO.setIndustryOrg("ICT");
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName46T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("SHIPPING_OBJECT");
        monthAnalysisVO.setViewFlag("6");
        monthAnalysisVO.setIndustryOrg("ICT");
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }


    @Test
    public void getManufactureGroupCnName5T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("MANUFACTURE_OBJECT");
        monthAnalysisVO.setIndustryOrg("ICT");
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName6T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("ITEM");
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName7T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setGroupLevel("LV0");
        monthAnalysisVO.setIndustryOrg("ICT");
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName8T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setGroupLevel("LV1");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        list.add("1180S");
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName9T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setGroupLevel("LV2");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        list.add("1180S");
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setLv2ProdRdTeamCnName(list);
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName10T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setGroupLevel("L2");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        list.add("1180S");
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setLv2ProdRdTeamCnName(list);
        monthAnalysisVO.setL1NameList(list);
        monthAnalysisVO.setL2NameList(list);
        monthAnalysisVO.setViewFlag("5");
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName11T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setGroupLevel("L1");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        list.add("1180S");
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setLv2ProdRdTeamCnName(list);
        monthAnalysisVO.setL1NameList(list);
        monthAnalysisVO.setL2NameList(list);
        monthAnalysisVO.setViewFlag("5");
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName12T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setGroupLevel("SHIPPING_OBJECT");
        monthAnalysisVO.setViewFlag("0");
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName121T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setGroupLevel("SHIPPING_OBJECT");
        monthAnalysisVO.setViewFlag("1");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        list.add("1180S");
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setLv2ProdRdTeamCnName(list);
        monthAnalysisVO.setLv3ProdRdTeamCnName(list);
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName122T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setGroupLevel("SHIPPING_OBJECT");
        monthAnalysisVO.setViewFlag("2");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        list.add("1180S");
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setLv2ProdRdTeamCnName(list);
        monthAnalysisVO.setLv3ProdRdTeamCnName(list);
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName123T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setGroupLevel("SHIPPING_OBJECT");
        monthAnalysisVO.setViewFlag("3");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        list.add("1180S");
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setLv2ProdRdTeamCnName(list);
        monthAnalysisVO.setLv3ProdRdTeamCnName(list);
        monthAnalysisVO.setL1NameList(list);
        monthAnalysisVO.setL2NameList(list);
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName124T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setGroupLevel("SHIPPING_OBJECT");
        monthAnalysisVO.setViewFlag("4");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        list.add("1180S");
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setLv2ProdRdTeamCnName(list);
        monthAnalysisVO.setLv3ProdRdTeamCnName(list);
        monthAnalysisVO.setL1NameList(list);
        monthAnalysisVO.setL2NameList(list);
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }



    @Test
    public void getManufactureGroupCnName13T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setGroupLevel("MANUFACTURE_OBJECT");
        monthAnalysisVO.setIndustryOrg("ICT");
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName14T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setGroupLevel("ITEM");
        monthAnalysisVO.setIndustryOrg("ICT");
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName15T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setGroupLevel("ITEM");
        monthAnalysisVO.setIndustryOrg("ICT");
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName16T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setGroupLevel("LV0");
        monthAnalysisVO.setIndustryOrg("ICT");
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName17T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        list.add("1180S");
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setLv2ProdRdTeamCnName(list);
        monthAnalysisVO.setViewFlag("5");
        monthAnalysisVO.setGroupLevel("LV1");
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName18T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setGroupLevel("LV2");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        list.add("1180S");
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setLv2ProdRdTeamCnName(list);
        monthAnalysisVO.setViewFlag("5");
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName19T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setGroupLevel("LV3");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        list.add("1180S");
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setLv2ProdRdTeamCnName(list);
        monthAnalysisVO.setLv3ProdRdTeamCnName(list);
        monthAnalysisVO.setViewFlag("5");
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName20T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setGroupLevel("DIMENSION");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        list.add("1180S");
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setLv2ProdRdTeamCnName(list);
        monthAnalysisVO.setLv3ProdRdTeamCnName(list);
        monthAnalysisVO.setViewFlag("5");
        monthAnalysisVO.setDimensionCnName(list);
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName21T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setGroupLevel("SUBCATEGORY");
        monthAnalysisVO.setIndustryOrg("ICT");
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName22T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setGroupLevel("SUB_DETAIL");
        monthAnalysisVO.setIndustryOrg("ICT");
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName23T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setGroupLevel("SHIPPING_OBJECT");
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName24T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setGroupLevel("MANUFACTURE_OBJECT");
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        list.add("1180S");
        monthAnalysisVO.setDimensionCnName(list);
        monthAnalysisVO.setDimensionSubCategoryCnName(list);
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setLv2ProdRdTeamCnName(list);
        monthAnalysisVO.setLv3ProdRdTeamCnName(list);
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName25T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setGroupLevel("MANUFACTURE_OBJECT");
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setViewFlag("1");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        list.add("1180S");
        monthAnalysisVO.setDimensionCnName(list);
        monthAnalysisVO.setDimensionSubCategoryCnName(list);
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setLv2ProdRdTeamCnName(list);
        monthAnalysisVO.setLv3ProdRdTeamCnName(list);
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName26T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setGroupLevel("MANUFACTURE_OBJECT");
        monthAnalysisVO.setIndustryOrg("ICT");
        monthAnalysisVO.setViewFlag("2");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        list.add("1180S");
        monthAnalysisVO.setDimensionCnName(list);
        monthAnalysisVO.setDimensionSubCategoryCnName(list);
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setLv2ProdRdTeamCnName(list);
        monthAnalysisVO.setLv3ProdRdTeamCnName(list);
        monthAnalysisVO.setDimensionSubDetailCnName(list);
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName27T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setGroupLevel("MANUFACTURE_OBJECT");
        monthAnalysisVO.setViewFlag("3");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        list.add("1180S");
        monthAnalysisVO.setDimensionCnName(list);
        monthAnalysisVO.setDimensionSubCategoryCnName(list);
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setLv2ProdRdTeamCnName(list);
        monthAnalysisVO.setLv3ProdRdTeamCnName(list);
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName28T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setGroupLevel("MANUFACTURE_OBJECT");
        monthAnalysisVO.setViewFlag("4");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        list.add("1180S");
        monthAnalysisVO.setDimensionCnName(list);
        monthAnalysisVO.setDimensionSubCategoryCnName(list);
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setLv2ProdRdTeamCnName(list);
        monthAnalysisVO.setLv3ProdRdTeamCnName(list);
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName29T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setGroupLevel("MANUFACTURE_OBJECT");
        monthAnalysisVO.setViewFlag("5");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        list.add("1180S");
        monthAnalysisVO.setDimensionCnName(list);
        monthAnalysisVO.setDimensionSubCategoryCnName(list);
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setLv2ProdRdTeamCnName(list);
        monthAnalysisVO.setLv3ProdRdTeamCnName(list);
        monthAnalysisVO.setDimensionSubDetailCnName(list);
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName30T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setGroupLevel("MANUFACTURE_OBJECT");
        monthAnalysisVO.setViewFlag("6");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        list.add("1180S");
        monthAnalysisVO.setDimensionCnName(list);
        monthAnalysisVO.setDimensionSubCategoryCnName(list);
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setLv2ProdRdTeamCnName(list);
        monthAnalysisVO.setLv3ProdRdTeamCnName(list);
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName31T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setGroupLevel("MANUFACTURE_OBJECT");
        monthAnalysisVO.setViewFlag("7");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        list.add("1180S");
        monthAnalysisVO.setDimensionCnName(list);
        monthAnalysisVO.setDimensionSubCategoryCnName(list);
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setLv2ProdRdTeamCnName(list);
        monthAnalysisVO.setLv3ProdRdTeamCnName(list);
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }

    @Test
    public void getManufactureGroupCnName32T() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setGroupLevel("MANUFACTURE_OBJECT");
        monthAnalysisVO.setViewFlag("8");
        monthAnalysisVO.setIndustryOrg("ICT");
        List<String> list = new ArrayList<>();
        list.add("1180D");
        list.add("1180S");
        monthAnalysisVO.setDimensionCnName(list);
        monthAnalysisVO.setDimensionSubCategoryCnName(list);
        monthAnalysisVO.setLv1ProdRdTeamCnName(list);
        monthAnalysisVO.setLv2ProdRdTeamCnName(list);
        monthAnalysisVO.setLv3ProdRdTeamCnName(list);
        monthAnalysisVO.setDimensionSubDetailCnName(list);
        Assert.assertNotNull(commonService.getManufactureGroupCnName(monthAnalysisVO));
    }
}