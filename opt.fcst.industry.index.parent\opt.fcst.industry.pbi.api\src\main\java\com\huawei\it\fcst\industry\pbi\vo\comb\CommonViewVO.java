/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.comb;

import com.huawei.it.fcst.industry.pbi.vo.common.CommonBaseVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstDimInfoVO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * CommonViewVO Class
 *
 * <AUTHOR>
 * @since 2023/3/15
 */
// 不能修改成getter和setter
@Data
@NoArgsConstructor
public class CommonViewVO extends CommonBaseVO implements Serializable {

    private static final long serialVersionUID = -3781852458067854849L;

    /**
     * 入参区分是否是获取权限得查询
     */
    private Boolean permissionTag;

    private String monthVersionId;

    private String maxLevel;

    // 组合整体失效
    private String enableFlag;

    private String userId;

    private String userAccount;

    private String roleId;

    private String pageSymbol;

    private String oldPageFlag;

    private String otherOldPageFlag;

    private String expandFlag;

    private String lv0Flag;

    private String lv0Code;

    private String lv1Code;

    private String lv1CnName;

    private String lv2Code;

    private String lv2CnName;

    private String lv3Code;

    private String lv3CnName;

    private String lv4Code;

    private String lv4CnName;

    private String filterGroupLevel;

    private Long customId;

    private String customCnName;

    private String id;

    private String isSeparate;

    // 是否对比分析
    private Boolean isCompareFlag;

    private String removeFlag;

    private String firstFlag;

    private String connectCode;

    private String otherPageSymbol;

    private List<String> lv1CodeList = new ArrayList<>();

    private List<String> lv2CodeList = new ArrayList<>();

    private List<String> lv3CodeList = new ArrayList<>();

    private List<String> lv4CodeList = new ArrayList<>();

    private List<String> groupCodeList = new ArrayList<>();

    private List<DmFcstDimInfoVO> customVOList = new ArrayList<>();

    private List<DmFcstDimInfoVO> parentCustomVOList = new ArrayList<>();

    private List<DmFcstDimInfoVO> filterCustomVOList = new ArrayList<>();

    private List<DmFcstDimInfoVO> changeCustomVOList = new ArrayList<>();

}
