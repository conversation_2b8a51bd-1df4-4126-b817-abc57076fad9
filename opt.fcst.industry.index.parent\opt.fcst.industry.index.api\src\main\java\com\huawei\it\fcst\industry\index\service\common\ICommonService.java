/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.service.common;

import com.huawei.it.fcst.industry.index.vo.common.DataPermissionsVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.month.DmFocPageInfoVO;
import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import io.swagger.annotations.Api;

import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;

/**
 * ICommonService Interface
 *
 * <AUTHOR>
 * @since 2023/03/13
 */
@Path("/save")
@Api(value = "公共服务接口")
@Produces(MediaType.APPLICATION_JSON)
public interface ICommonService {

    /**
     * [查询导航栏列表信息]
     *
     * @param dmFocPageInfoVO DmFocPageInfoVO
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @POST
    @Path("/navigation/list")
    ResultDataVO getNavigationList(DmFocPageInfoVO dmFocPageInfoVO);

    /**
     * [查询TOP品类/规格品清单刷新时间]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @GET
    @Path("/version/refreshTime")
    ResultDataVO findRefreshTime(@QueryParam("industryOrg") String industryOrg);

    /**
     * [保存或修改页面信息]
     *
     * @param dmFocPageInfoVO 参数
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @POST
    @Path("/pages/saveOrUpdate")
    ResultDataVO saveOrUpdatePageInfo(DmFocPageInfoVO dmFocPageInfoVO) ;

    /**
     * [更新页面信息默认标识]
     *
     * @param dmFocPageInfoVO 参数
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @POST
    @Path("/pages/defaultFlag")
    ResultDataVO setDefaultFlag(DmFocPageInfoVO dmFocPageInfoVO);

    /**
     * [查询页面信息初始化条件]
     *
     * @param pageId 页面ID
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @GET
    @Path("/initialize/condition")
    ResultDataVO findInitCondition(@QueryParam("pageId") Long pageId, @QueryParam("industryOrg") String industryOrg) throws CommonApplicationException;

    /**
     * [删除页面信息]
     *
     * @param pageId 页面ID
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @DELETE
    @Path("/pages/delete/{pageId}")
    ResultDataVO deletePageInfo(@PathParam("pageId") Long pageId, @QueryParam("defaultFlag") String defaultFlag,
                                @QueryParam("caliberFlag")String caliberFlag,@QueryParam("industryOrg") String industryOrg);

    /**
     * [查询本期实际数截止月]
     *
     */
    @GET
    @Path("/actual/monthnum")
    Long findActualMonthNum(@QueryParam("industryOrg") String industryOrg);

    /**
     * 查询切换基期的实际数最大月份
     *
     */
    @POST
    @Path("/actual/periodId")
    ResultDataVO findActualPeriodId(MonthAnalysisVO monthAnalysisVO);

    /**
     * 根据group层级Code查询group层级中文名称
     *
     * @param monthAnalysisVO MonthAnalysisVO
     * @return String
     *
     */
    String getGroupCnName(MonthAnalysisVO monthAnalysisVO);

    String getManufactureGroupCnName(MonthAnalysisVO monthAnalysisVO);

    String getReplaceGroupCnName(MonthAnalysisVO monthAnalysisVO);

    /**
     * [查询TOP品类/规格品清单刷新时间所对应的versionId]
     *
     * @param dataType 数据类型（category：TOP品类、item：规格品）
     * @return Long
     */
    Long getVersionId(@QueryParam("dataType") String dataType,@QueryParam("tablePreFix") String tablePreFix);

    @GET
    @Path("/dataDictionary/{path}")
    ResultDataVO getDataDictionaryByPath(@PathParam("path") String path) throws ApplicationException;

    /**
     * 获取登录用户当前角色的数据范围权限
     *
     * @return List
     * @throws ApplicationException
     */
    @GET
    @Path("/industry/role/permissions")
    DataPermissionsVO getCurrentRoleDataPermission(@QueryParam("industryOrg") String industryOrg) throws ApplicationException;

    /**
     * 查询当前角色对应的组织机构
     *
     * @return List
     * @throws ApplicationException
     */
    @POST
    @Path("/distinct/industryOrg")
    ResultDataVO distinctIndustryOrg() throws ApplicationException;

    /**
     * 校验当前用户另存为页面权限
     *
     * @return Boolean
     */
    @POST
    @Path("/pages/haspermissions")
    Boolean hasRolePermission(DmFocPageInfoVO dmFocPageInfoVO);

    void setProdRndTeamCode(MonthAnalysisVO monthAnalysisVO);

    DataPermissionsVO getDimensionList(String costType,String tablePreFix,String industryOrg);

    void setViewFlagValueWithLookUp(DmFocViewInfoVO dmFocViewInfoVO) throws ApplicationException;
}