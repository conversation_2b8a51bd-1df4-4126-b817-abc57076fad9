/*
 *
 *  Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 *
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.combination.DmFocDataRefreshStatus;

/**
 * The DAO to access DmHqPlanDataRefreshStatus entity
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-07-30 11:04:57
 */
public interface IDmFocDataRefreshStatusDao {

    Long getDataRefrashKey();

    /**
     * Find DmHqPlanDataRefreshStatus by id.
     *
     * @param dataRefreshStatus is id
     * @return DmHqPlanDataRefreshStatus
     */
    DmFocDataRefreshStatus findDmFocDataRefreshStatusById(DmFocDataRefreshStatus dataRefreshStatus);

    /**
     * Find DmHqPlanVersionReleaseResult records.
     *
     * @return list
     */
    DmFocDataRefreshStatus findDmFocDataRefreshStatus(DmFocDataRefreshStatus dmHqPlanDataRefreshStatus);

    /**
     * 一天查询一次初始化任务，执行完一次第二天才会执行
     *
     * @param dmHqPlanDataRefreshStatus 参数
     * @return 结果
     */
    DmFocDataRefreshStatus findDmFocDataRefreshStatusByDay(DmFocDataRefreshStatus dmHqPlanDataRefreshStatus);

    /**
     * Insert a new DmHqPlanDataRefreshStatus record.
     *
     * @param dmHqPlanDataRefreshStatus is
     * @return int
     */
    int createDmFocDataRefreshStatus(DmFocDataRefreshStatus dmHqPlanDataRefreshStatus);

    /**
     * Update an existed  DmHqPlanDataRefreshStatus record.
     *
     * @param dmHqPlanDataRefreshStatus is
     * @return int
     */
    int updateDmFocDataRefreshStatus(DmFocDataRefreshStatus dmHqPlanDataRefreshStatus);

}
