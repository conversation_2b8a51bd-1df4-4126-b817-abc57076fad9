/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.month;

import com.huawei.it.fcst.annotations.ExportAttribute;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * The Entity of DmFocActualCostT
 *
 * <AUTHOR>
 * @since 2023/03/10
 */
@Getter
@Setter
@NoArgsConstructor
public class ExportFocActualCostVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("版本ID")
    private Long versionId;

    @ApiModelProperty("会计期")
    @ExportAttribute(sort = 0, dataType = "Number")
    private Long periodId;

    @ExportAttribute(sort = 1)
    private String costType;

    @ApiModelProperty("分层级CODE")
    @ExportAttribute(sort = 4)
    private String groupCode;

    @ApiModelProperty("分层级中文名称")
    @ExportAttribute(sort = 3)
    private String groupCnName;

    @ApiModelProperty("父层级中文名称")
    @ExportAttribute(sort = 2)
    private String parentCnName;

    @ApiModelProperty("实际发货额")
    @ExportAttribute(sort = 5)
    private Double actualCostAmt;

}
