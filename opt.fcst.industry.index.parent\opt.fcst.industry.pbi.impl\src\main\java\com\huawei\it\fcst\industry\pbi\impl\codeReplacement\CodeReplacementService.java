/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.codeReplacement;

import cn.hutool.json.JSONObject;
import com.huawei.it.fcst.config.ConfigUtil;
import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.industry.pbi.dao.ICodeReplacementDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstVersionInfoDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFocVarifyTaskDao;
import com.huawei.it.fcst.industry.pbi.enums.IndustryConstEnum;
import com.huawei.it.fcst.industry.pbi.impl.common.DropDownService;
import com.huawei.it.fcst.industry.pbi.impl.month.AsyncMonthAnalysisService;
import com.huawei.it.fcst.industry.pbi.impl.template.CodeReplaceTemplateEnum;
import com.huawei.it.fcst.industry.pbi.service.codeReplacement.ICodeReplacementService;
import com.huawei.it.fcst.export.IExcelTemplateBeanManager;
import com.huawei.it.fcst.export.IExportProcessorService;
import com.huawei.it.fcst.industry.pbi.utils.CostReductUtils;
import com.huawei.it.fcst.industry.pbi.utils.FcstIndustryUtil;
import com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO;
import com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO;
import com.huawei.it.fcst.industry.pbi.vo.common.DmFocVarifyTaskVO;
import com.huawei.it.fcst.util.UserInfoUtils;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.security.RoleVO;
import com.huawei.it.jalor5.security.UserVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Inject;
import javax.inject.Named;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * codeReplacementService Class
 */
@Named("codeReplacementService")
@JalorResource(code = "codeReplacementService", desc = "newict-编码替换服务")
public class CodeReplacementService implements ICodeReplacementService {

    private static final Logger log = LoggerFactory.getLogger(CodeReplacementService.class);

    @Inject
    private ICodeReplacementDao iCodeReplacementDao;

    @Autowired
    private IExportProcessorService iExportProcessorService;

    @Autowired
    private IDmFocVarifyTaskDao varifyTaskDao;

    @Autowired
    private IDmFcstVersionInfoDao iDmFcstVersionInfoDao;

    @Autowired
    private AsyncMonthAnalysisService asyncMonthAnalysisService;

    @Autowired
    private DropDownService dropDownService;

    private static final String REPL_SAMEL_IST = "replSameList";

    private static final String NEW_SPART_LIST = "newSpartList";

    private static final String OLD_SPART_LIST = "oldSpartList";

    private static final String DATA_LIST = "dataList";

    /**
     * 新老编码月度成本指数图查询功能
     *
     * @param dataVO 参数
     * @return 结果
     * @throws CommonApplicationException 异常
     */
    @JalorOperation(code = "monthlyCostIndexChartQuery", desc = "编码替换服务-新老编码月度成本指数图查询功能")
    @Override
    public ResultDataVO monthlyCostIndexChartQuery(CodeReplacementVO dataVO) throws CommonApplicationException {
        Map resultMap = new HashMap();
        // 获取用户权限
        dropDownService.setPermissionParameter(dataVO);
        Boolean marketingAnalystIndMark = marketingAnalystIndResultMap(dataVO);
        if (marketingAnalystIndMark) {
            return ResultDataVO.success(resultMap);
        }
        verifyMandatoryParameters(dataVO);
        // 选择层级（2+1） || 是否虚化（2）。查询的表不同，总共3*2=6组组合
        String isNeedBlur = dataVO.getIsNeedBlur();
        // 选择层级可分为SPART层级与PBI维度层级，区别在于关系、替代关系与新旧SpartCode是否有值。
        String selectionLevel = determineCurrentSelectionLevel(dataVO);
        // 设置查询年份（过去两年+今年YTD）
        setYearInfo(dataVO);
        // 预处理的虚化
        if (dataVO.getPreCus()) {
            CostReductUtils.getPreCusIndexCostList(dataVO);
            List<CodeReplacementVO> spartIndexCostList = iCodeReplacementDao.getPreBlurSpartIndexCostList(dataVO);
            processList(spartIndexCostList, resultMap);
        } else {
            getReplSameIndexCostList(dataVO, isNeedBlur, selectionLevel, resultMap);
        }
        return ResultDataVO.success(resultMap);
    }

    private void getReplSameIndexCostList(CodeReplacementVO dataVO, String isNeedBlur, String selectionLevel, Map resultMap) {
        List<CodeReplacementVO> spartIndexCostList;
        List<CodeReplacementVO> pbiReplSameIndexCostList;
        // 关系层级
        switch (selectionLevel) {
            case CommonConstant.SELECTION_LEVEL_SPART_LEVEL:
                if (Boolean.FALSE.equals(Boolean.valueOf(isNeedBlur))) {
                    spartIndexCostList = iCodeReplacementDao.getSpartIndexCostList(dataVO,
                            CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()), CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()));
                    // 新旧spart按此格式返回给前端，用codeType区分
                    processList(spartIndexCostList, resultMap);
                    // 同基编码
                    pbiReplSameIndexCostList = iCodeReplacementDao.getReplSameSpartIndexCostList(dataVO, CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()));
                    resultMap.put(REPL_SAMEL_IST, pbiReplSameIndexCostList);
                } else {
                    spartIndexCostList = iCodeReplacementDao.getBlurSpartIndexCostList(dataVO,
                            CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()), CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()));
                    processList(spartIndexCostList, resultMap);
                }
                break;
            case CommonConstant.SELECTION_LEVEL_TOP_SPART_LEVEL:
                // topSpart 有 4情况。 此处做按单个的虚化属性做数据查询
                if (isNeedBlur.split("\\|").length == 2) {
                    resultMap.put(NEW_SPART_LIST, getTopSpartIndexCostList(dataVO, Boolean.valueOf(isNeedBlur.split("\\|")[0]),
                            CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()), dataVO.getNewCustomId(), dataVO.getNewProdTeamCode()));
                    resultMap.put(OLD_SPART_LIST, getTopSpartIndexCostList(dataVO, Boolean.valueOf(isNeedBlur.split("\\|")[1]),
                            CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()), dataVO.getOldCustomId(), dataVO.getOldProdTeamCode()));
                }
                break;
            case CommonConstant.SELECTION_LEVEL_PBI_LEVEL:
                spartIndexCostList = iCodeReplacementDao.getPbiIndexCostList(dataVO);
                processList(spartIndexCostList, resultMap);
                // 同基编码
                pbiReplSameIndexCostList = iCodeReplacementDao.getPbiReplSameIndexCostList(dataVO);
                resultMap.put(REPL_SAMEL_IST, pbiReplSameIndexCostList);
                break;
            default:
                break;
        }
    }

    public List<CodeReplacementVO> getTopSpartIndexCostList(CodeReplacementVO dataVO, boolean flag, String spartCode,
                                                            Long customId, String newProdTeamCode) {
        List<CodeReplacementVO> spartIndexCostList = null;
        if (flag) {
            spartIndexCostList = iCodeReplacementDao.getBlurTopSpartIndexCostList(dataVO, spartCode, customId, newProdTeamCode);
        } else {
            newProdTeamCode = CostReductUtils.setProdTeamCode(dataVO, newProdTeamCode);
            spartIndexCostList = iCodeReplacementDao.getTopSpartIndexCostList(dataVO, spartCode, newProdTeamCode);
        }
        return spartIndexCostList;
    }

    private void setYearInfo(CodeReplacementVO dataVO) throws CommonApplicationException {
        FcstIndustryUtil.setRegionCode(dataVO);
        int versionIdsYear = Boolean.valueOf(dataVO.getIsNeedBlur().split("\\|")[0])
                ? iCodeReplacementDao.getBlurVersionIdsYear(dataVO)
                : iCodeReplacementDao.getVersionIdsYear(dataVO);
        if (Objects.isNull(versionIdsYear) || versionIdsYear == 0) {
            throw new CommonApplicationException("当前版本有误，请检查数据");
        }
        dataVO.setTargetPeriod(Arrays.asList(String.valueOf(versionIdsYear - 2), String.valueOf(versionIdsYear - 1),
                String.valueOf(versionIdsYear)));
        dataVO.setVersionId(iDmFcstVersionInfoDao.findVersionIdByDataType(IndustryConstEnum.PAGE_TYPE.MONTH.getValue()).getVersionId());
    }

    // 判断当前的选择层级
    private String determineCurrentSelectionLevel(CodeReplacementVO codeReplacementVO)
            throws CommonApplicationException {
        // SPART层级，分 新旧Spart\TopSpart。
        if (CollectionUtils.isNotEmpty(codeReplacementVO.getOldSpartCode()) && CollectionUtils.isNotEmpty(
                codeReplacementVO.getNewSpartCode())) {
            if (StringUtils.isNotBlank(codeReplacementVO.getRelationType()) || StringUtils.isNotBlank(
                    codeReplacementVO.getReplaceRelationType()) || StringUtils.isNotBlank(codeReplacementVO.getReplaceRelationName())) {
                return CommonConstant.SELECTION_LEVEL_SPART_LEVEL;
            } else {
                return CommonConstant.SELECTION_LEVEL_TOP_SPART_LEVEL;
            }
            // PBI层级，下列字段需非空
        } else if ((StringUtils.isNotBlank(codeReplacementVO.getGroupLevel()) && levelParamVerify(codeReplacementVO))
                || StringUtils.equals(codeReplacementVO.getGroupLevel(), "LV0")) {
            return CommonConstant.SELECTION_LEVEL_PBI_LEVEL;
        } else {
            throw new CommonApplicationException("传入的参数有误，请检查层级的入参");
        }
    }

    // 选择PBI层级需 Lv1-Lv4(Lv3.5) 存在非空的值。
    private boolean levelParamVerify(CodeReplacementVO codeReplacementVO) {
        return StringUtils.isNotBlank(codeReplacementVO.getLv1ProdTeamCode()) || StringUtils.isNotBlank(
                codeReplacementVO.getLv2ProdTeamCode()) || StringUtils.isNotBlank(codeReplacementVO.getLv3ProdTeamCode())
                || StringUtils.isNotBlank(codeReplacementVO.getLv4ProdTeamCode());
    }

    // 检验必输参数，设置必要参数
    private void verifyMandatoryParameters(CodeReplacementVO codeReplacementVO) throws CommonApplicationException {
        // 字段：成本类型 PBI目录树 国内/海外 BG 地区部 代表处 基期
        if (Objects.isNull(codeReplacementVO) || StringUtils.isEmpty(codeReplacementVO.getCostType())
                || StringUtils.isEmpty(codeReplacementVO.getGranularityType())) {
            throw new CommonApplicationException("传入的必输参数有误，请检查入参");
        } else if (StringUtils.isEmpty(codeReplacementVO.getOverseaFlag()) || StringUtils.isEmpty(
                codeReplacementVO.getBgCode()) || StringUtils.isEmpty(codeReplacementVO.getRegionCode())) {
            throw new CommonApplicationException("传入的必输参数有误，请检查入参");
        } else if (StringUtils.isEmpty(codeReplacementVO.getBasePeriodId()) || StringUtils.isEmpty(
                codeReplacementVO.getRepofficeCode())) {
            throw new CommonApplicationException("传入的必输参数有误，请检查入参");
        }
        codeReplacementVO.setTablePrefixCostTypeValue();
        codeReplacementVO.setTablePrefixPbiValue();
    }

    private void processList(List<CodeReplacementVO> spartIndexCostList, Map resultMap) {
        Map<String, List<CodeReplacementVO>> dataMap = spartIndexCostList.stream()
                .collect(Collectors.groupingBy(CodeReplacementVO::getCodeType));
        resultMap.put(OLD_SPART_LIST, dataMap.get(CommonConstant.CODE_TYPE_OLD));
        resultMap.put(NEW_SPART_LIST, dataMap.get(CommonConstant.CODE_TYPE_NEW));
        resultMap.put(REPL_SAMEL_IST, dataMap.get(CommonConstant.CODE_TYPE_SAME));
    }

    /**
     * 新老编码月度发货量分布查询功能
     *
     * @param dataVO 参数
     * @return 结果
     * @throws CommonApplicationException 异常
     */
    @JalorOperation(code = "distributionOfMonthlyShipments", desc = "编码替换服务-新老编码月度发货量分布查询功能")
    @Override
    public ResultDataVO distributionOfMonthlyShipments(CodeReplacementVO dataVO) throws CommonApplicationException {
        Map resultMap = new HashMap();
        // 获取用户权限
        dropDownService.setPermissionParameter(dataVO);
        Boolean marketingAnalystIndMark = marketingAnalystIndResultMap(dataVO);
        if (marketingAnalystIndMark) {
            return ResultDataVO.success(resultMap);
        }
        verifyMandatoryParameters(dataVO);
        // 选择层级（2+1） || 是否虚化（2）。查询的表不同，总共3*2=6组组合
        String isNeedBlur = dataVO.getIsNeedBlur();
        String selectionLevel = determineCurrentSelectionLevel(dataVO);
        setYearInfo(dataVO);
        if (dataVO.getPreCus()) {
            CostReductUtils.getPreCusIndexCostList(dataVO);
            List<CodeReplacementVO> spartIndexCostList = iCodeReplacementDao.getPreBlurSpartIndexQtyList(dataVO);
            processList(spartIndexCostList, resultMap);
        } else {
            distributionOfMonthlyShipList(dataVO, isNeedBlur, selectionLevel, resultMap);
        }
        return ResultDataVO.success(resultMap);
    }

    private void distributionOfMonthlyShipList(CodeReplacementVO dataVO, String isNeedBlur, String selectionLevel, Map resultMap) {
        // 选择层级可分为SPART层级与PBI维度层级，区别在于关系、替代关系与新旧SpartCode是否有值。
        switch (selectionLevel) {
            case CommonConstant.SELECTION_LEVEL_SPART_LEVEL:
                List<CodeReplacementVO> spartIndexCostList = null;
                // 是否虚化
                if (Boolean.TRUE.equals(Boolean.valueOf(isNeedBlur))) {
                    spartIndexCostList = iCodeReplacementDao.getBlurSpartIndexQtyList(dataVO,
                            CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()), CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()));
                    processList(spartIndexCostList, resultMap);
                } else {
                    spartIndexCostList = iCodeReplacementDao.getSpartIndexQtyList(dataVO,
                            CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()), CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()));
                    // 新旧spart按此格式返回给前端，用codeType区分
                    processList(spartIndexCostList, resultMap);
                }
                break;
            case CommonConstant.SELECTION_LEVEL_TOP_SPART_LEVEL:
                // topSpart 有 4情况。 此处做按单个的虚化属性做数据查询
                if (isNeedBlur.split("\\|").length == 2) {
                    resultMap.put(NEW_SPART_LIST, getShipDataInfo(dataVO, Boolean.valueOf(isNeedBlur.split("\\|")[0]),
                            CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()), dataVO.getNewCustomId(), dataVO.getNewProdTeamCode()));
                    resultMap.put(OLD_SPART_LIST, getShipDataInfo(dataVO, Boolean.valueOf(isNeedBlur.split("\\|")[1]),
                            CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()), dataVO.getOldCustomId(), dataVO.getOldProdTeamCode()));
                }
                break;
            case CommonConstant.SELECTION_LEVEL_PBI_LEVEL:
                processList(iCodeReplacementDao.getPbiIndexQtyList(dataVO), resultMap);
                break;
            default:
                break;
        }
    }

    public List<CodeReplacementVO> getShipDataInfo(CodeReplacementVO dataVO, boolean flag, String spartCode,
                                                   Long customId, String newProdTeamCode) {
        List<CodeReplacementVO> spartIndexCostList = null;
        if (flag) {
            spartIndexCostList = iCodeReplacementDao.getBlurCombShipQuery(dataVO, spartCode, customId, newProdTeamCode);
        } else {
            newProdTeamCode = CostReductUtils.setProdTeamCode(dataVO, newProdTeamCode);
            spartIndexCostList = iCodeReplacementDao.getCombShipQuery(dataVO, spartCode, newProdTeamCode);
        }
        return spartIndexCostList;
    }

    /**
     * 新老编码月度成本偏差查询功能
     *
     * @param dataVO 参数
     * @return 结果
     * @throws CommonApplicationException 异常
     */
    @JalorOperation(code = "monthlyCostVariance", desc = "编码替换服务-新老编码月度成本偏差查询功能")
    @Override
    public ResultDataVO monthlyCostVariance(CodeReplacementVO dataVO) throws CommonApplicationException {
        Map resultMap = new HashMap();
        // 获取用户权限
        dropDownService.setPermissionParameter(dataVO);
        Boolean marketingAnalystIndMark = marketingAnalystIndResultMap(dataVO);
        if (marketingAnalystIndMark) {
            return ResultDataVO.success(resultMap);
        }
        verifyMandatoryParameters(dataVO);
        // 选择层级（2+1） || 是否虚化（2）。查询的表不同，总共3*2=6组组合
        String isNeedBlur = dataVO.getIsNeedBlur();
        String selectionLevel = determineCurrentSelectionLevel(dataVO);
        setYearInfo(dataVO);
        if (dataVO.getPreCus()) {
            CostReductUtils.getPreCusIndexCostList(dataVO);
            List<CodeReplacementVO> newSpartList = iCodeReplacementDao.getPreBlurSpartCVExpList(dataVO, CommonConstant.CODE_TYPE_NEW);
            List<CodeReplacementVO> oldSpartList = iCodeReplacementDao.getPreBlurSpartCVExpList(dataVO, CommonConstant.CODE_TYPE_OLD);
            resultMap.put(DATA_LIST, CostReductUtils.processCVList(newSpartList, oldSpartList));
        } else {
            monthlyCostVarianceList(dataVO, isNeedBlur, selectionLevel, resultMap);
        }
        return ResultDataVO.success(resultMap);
    }

    private void monthlyCostVarianceList(CodeReplacementVO dataVO, String isNeedBlur, String selectionLevel, Map resultMap) {
        // 选择层级可分为SPART层级与PBI维度层级，区别在于关系、替代关系与新旧SpartCode是否有值。
        // 获取加密key
        String plainText = ConfigUtil.getInstance().get16PlainText();
        switch (selectionLevel) {
            case CommonConstant.SELECTION_LEVEL_SPART_LEVEL:
                // 是否虚化
                if (Boolean.TRUE.equals(Boolean.valueOf(isNeedBlur))) {
                    List<CodeReplacementVO> newSpartList = iCodeReplacementDao.getBlurSpartCVExpList(dataVO,
                            CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()), dataVO.getCustomId(), dataVO.getNewProdTeamCode(),
                            CommonConstant.CODE_TYPE_NEW, plainText);
                    List<CodeReplacementVO> oldSpartList = iCodeReplacementDao.getBlurSpartCVExpList(dataVO,
                            CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()), dataVO.getCustomId(), dataVO.getOldProdTeamCode(),
                            CommonConstant.CODE_TYPE_OLD, plainText);
                    resultMap.put(DATA_LIST, CostReductUtils.processCVList(newSpartList, oldSpartList));
                } else {
                    List<CodeReplacementVO> newSpartList = iCodeReplacementDao.getSpartCVList(dataVO,
                            CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()), dataVO.getNewProdTeamCode(), CommonConstant.SPART_NAME, plainText);
                    List<CodeReplacementVO> oldSpartList = iCodeReplacementDao.getSpartCVList(dataVO,
                            CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()), dataVO.getOldProdTeamCode(), CommonConstant.SPART_NAME, plainText);
                    resultMap.put(DATA_LIST, CostReductUtils.processCVList(newSpartList, oldSpartList));
                }
                break;
            case CommonConstant.SELECTION_LEVEL_TOP_SPART_LEVEL:
                // topSpart 有 4情况。 此处做按单个的虚化属性做数据查询
                if (isNeedBlur.split("\\|").length == 2) {
                    List<CodeReplacementVO> newSpartList = getCVDataInfo(dataVO,
                            Boolean.valueOf(isNeedBlur.split("\\|")[0]), CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()),
                            dataVO.getNewCustomId(), dataVO.getNewProdTeamCode(), plainText);
                    List<CodeReplacementVO> oldSpartList = getCVDataInfo(dataVO,
                            Boolean.valueOf(isNeedBlur.split("\\|")[1]), CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()),
                            dataVO.getOldCustomId(), dataVO.getOldProdTeamCode(), plainText);
                    resultMap.put(DATA_LIST, CostReductUtils.processCVList(newSpartList, oldSpartList));
                }
                break;
            case CommonConstant.SELECTION_LEVEL_PBI_LEVEL:
                List<CodeReplacementVO> spartIndexCostList = iCodeReplacementDao.getPbiIndexCVList(dataVO, plainText);
                Map<String, List<CodeReplacementVO>> collect = spartIndexCostList.stream()
                        .collect(Collectors.groupingBy(CodeReplacementVO::getCodeType));
                resultMap.put(DATA_LIST, CostReductUtils.processCVList(collect.get(CommonConstant.CODE_TYPE_NEW), collect.get(CommonConstant.CODE_TYPE_OLD)));
                break;
            default:
                break;
        }
    }

    public List<CodeReplacementVO> getCVDataInfo(CodeReplacementVO dataVO, boolean flag, String spartCode,
                                                 Long customId, String newProdTeamCode, String plainText) {
        List<CodeReplacementVO> spartIndexCostList = null;
        if (flag) {
            spartIndexCostList = iCodeReplacementDao.getBlurSpartCVList(dataVO, spartCode, customId, newProdTeamCode,
                    "TOP-SPART", plainText);
        } else {
            newProdTeamCode = CostReductUtils.setProdTeamCode(dataVO, newProdTeamCode);
            spartIndexCostList = iCodeReplacementDao.getSpartCVList(dataVO, spartCode, newProdTeamCode, "TOP-SPART",
                    plainText);
        }
        return spartIndexCostList;
    }

    /**
     * 新老编码月度累计成本指数图查询功能
     *
     * @param dataVO 参数
     * @return 结果
     * @throws CommonApplicationException 异常
     */
    @JalorOperation(code = "monthlyAccumulatedCostIndexChartQuery", desc = "编码替换服务-新老编码月度累计成本指数图查询功能")
    @Override
    public ResultDataVO monthlyAccumulatedCostIndexChartQuery(CodeReplacementVO dataVO)
            throws CommonApplicationException {
        Map resultMap = new HashMap();
        // 获取用户权限
        dropDownService.setPermissionParameter(dataVO);
        Boolean marketingAnalystIndMark = marketingAnalystIndResultMap(dataVO);
        if (marketingAnalystIndMark) {
            return ResultDataVO.success(resultMap);
        }
        verifyMandatoryParameters(dataVO);
        // 选择层级（2+1） || 是否虚化（2）。查询的表不同，总共3*2=6组组合
        String isNeedBlur = dataVO.getIsNeedBlur();
        // 选择层级可分为SPART层级与PBI维度层级，区别在于关系、替代关系与新旧SpartCode是否有值。
        String selectionLevel = determineCurrentSelectionLevel(dataVO);
        setYearInfo(dataVO);
        // 预处理的虚化
        if (dataVO.getPreCus()) {
            CostReductUtils.getPreCusIndexCostList(dataVO);
            List<CodeReplacementVO> spartIndexCostList = iCodeReplacementDao.getPreBlurSpartIndexAccCostList(dataVO);
            processList(spartIndexCostList, resultMap);
        } else {
            monthlyAccumulatedCostIndexChart(dataVO, isNeedBlur, selectionLevel, resultMap);
        }
        return ResultDataVO.success(resultMap);
    }

    private void monthlyAccumulatedCostIndexChart(CodeReplacementVO dataVO, String isNeedBlur, String selectionLevel, Map resultMap) {
        List<CodeReplacementVO> spartIndexCostList = null;
        List<CodeReplacementVO> pbiReplSameIndexCostList = null;
        switch (selectionLevel) {
            case CommonConstant.SELECTION_LEVEL_SPART_LEVEL:
                if (Boolean.FALSE.equals(Boolean.valueOf(isNeedBlur))) {
                    spartIndexCostList = iCodeReplacementDao.getSpartIndexAccCostList(dataVO,
                            CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()), CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()));
                    // 新旧spart按此格式返回给前端，用codeType区分
                    processList(spartIndexCostList, resultMap);
                    // 同基编码
                    pbiReplSameIndexCostList = iCodeReplacementDao.getReplSameSpartIndexAccCostList(dataVO, CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()));
                    resultMap.put("replSameList", pbiReplSameIndexCostList);
                } else {
                    spartIndexCostList = iCodeReplacementDao.getBlurSpartIndexAccCostList(dataVO,
                            CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()), CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()));
                    processList(spartIndexCostList, resultMap);
                }
                break;
            case CommonConstant.SELECTION_LEVEL_TOP_SPART_LEVEL:
                // topSpart 有 4情况。 此处做按单个的虚化属性做数据查询
                if (isNeedBlur.split("\\|").length == 2) {
                    resultMap.put(NEW_SPART_LIST, getTopSpartIndexAccCostInfo(dataVO, Boolean.valueOf(isNeedBlur.split("\\|")[0]),
                            CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()), dataVO.getNewCustomId(), dataVO.getNewProdTeamCode()));
                    resultMap.put(OLD_SPART_LIST, getTopSpartIndexAccCostInfo(dataVO, Boolean.valueOf(isNeedBlur.split("\\|")[1]),
                            CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()), dataVO.getOldCustomId(), dataVO.getOldProdTeamCode()));
                }
                break;
            case CommonConstant.SELECTION_LEVEL_PBI_LEVEL:
                spartIndexCostList = iCodeReplacementDao.getPbiIndexAccCostList(dataVO);
                processList(spartIndexCostList, resultMap);
                // 同基编码
                pbiReplSameIndexCostList = iCodeReplacementDao.getReplSamePbiIndexAccCostList(dataVO);
                resultMap.put(REPL_SAMEL_IST, pbiReplSameIndexCostList);
                break;
            default:
                break;
        }
    }

    // 获取月累计指数信息
    public List<CodeReplacementVO> getTopSpartIndexAccCostInfo(CodeReplacementVO dataVO, boolean flag, String spartCode,
                                                               Long customId, String newProdTeamCode) {
        List<CodeReplacementVO> spartIndexCostList = null;
        if (flag) {
            spartIndexCostList = iCodeReplacementDao.getBlurTopSpartIndexAccCostList(dataVO, spartCode, customId,
                    newProdTeamCode);
        } else {
            newProdTeamCode = CostReductUtils.setProdTeamCode(dataVO, newProdTeamCode);
            spartIndexCostList = iCodeReplacementDao.getTopSpartIndexAccCostList(dataVO, spartCode, newProdTeamCode);
        }
        return spartIndexCostList;
    }

    /**
     * 新老编码月度累计发货量分布查询功能
     *
     * @param dataVO 参数
     * @return 结果
     * @throws CommonApplicationException 异常
     */
    @JalorOperation(code = "distributionOfMonthlyAccumulatedShipments", desc = "编码替换服务-新老编码月度累计发货量分布查询功能")
    @Override
    public ResultDataVO distributionOfMonthlyAccumulatedShipments(CodeReplacementVO dataVO)
            throws CommonApplicationException {
        Map resultMap = new HashMap();
        // 获取用户权限
        dropDownService.setPermissionParameter(dataVO);
        Boolean marketingAnalystIndMark = marketingAnalystIndResultMap(dataVO);
        if (marketingAnalystIndMark) {
            return ResultDataVO.success(resultMap);
        }
        verifyMandatoryParameters(dataVO);
        // 选择层级（2+1） || 是否虚化（2）。查询的表不同，总共3*2=6组组合
        String isNeedBlur = dataVO.getIsNeedBlur();
        String selectionLevel = determineCurrentSelectionLevel(dataVO);
        setYearInfo(dataVO);
        // 预处理的虚化
        if (dataVO.getPreCus()) {
            CostReductUtils.getPreCusIndexCostList(dataVO);
            List<CodeReplacementVO> spartIndexCostList = iCodeReplacementDao.getPreBlurSpartIndexAccQtyList(dataVO);
            processList(spartIndexCostList, resultMap);
        } else {
            distributionOfMonthlyAccShipList(dataVO, isNeedBlur, selectionLevel, resultMap);
        }
        return ResultDataVO.success(resultMap);
    }

    private void distributionOfMonthlyAccShipList(CodeReplacementVO dataVO, String isNeedBlur, String selectionLevel, Map resultMap) {
        // 选择层级可分为SPART层级与PBI维度层级，区别在于关系、替代关系与新旧SpartCode是否有值。
        switch (selectionLevel) {
            case CommonConstant.SELECTION_LEVEL_SPART_LEVEL:
                List<CodeReplacementVO> spartIndexCostList = null;
                // 是否虚化
                if (Boolean.TRUE.equals(Boolean.valueOf(isNeedBlur))) {
                    spartIndexCostList = iCodeReplacementDao.getBlurSpartIndexAccQtyList(dataVO,
                            CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()), CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()));
                    processList(spartIndexCostList, resultMap);
                } else {
                    spartIndexCostList = iCodeReplacementDao.getSpartIndexAccQtyList(dataVO,
                            CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()), CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()));
                    // 新旧spart按此格式返回给前端，用codeType区分
                    processList(spartIndexCostList, resultMap);
                }
                break;
            case CommonConstant.SELECTION_LEVEL_TOP_SPART_LEVEL:
                // topSpart 有 4情况。 此处做按单个的虚化属性做数据查询
                if (isNeedBlur.split("\\|").length == 2) {
                    resultMap.put(NEW_SPART_LIST, getShipAccDataInfo(dataVO, Boolean.valueOf(isNeedBlur.split("\\|")[0]),
                            CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()), dataVO.getNewCustomId(), dataVO.getNewProdTeamCode()));
                    resultMap.put(OLD_SPART_LIST, getShipAccDataInfo(dataVO, Boolean.valueOf(isNeedBlur.split("\\|")[1]),
                            CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()), dataVO.getOldCustomId(), dataVO.getOldProdTeamCode()));
                }
                break;
            case CommonConstant.SELECTION_LEVEL_PBI_LEVEL:
                processList(iCodeReplacementDao.getPbiIndexAccQtyList(dataVO), resultMap);
                break;
            default:
                break;
        }
    }

    // 获取发货量信息
    public List<CodeReplacementVO> getShipAccDataInfo(CodeReplacementVO dataVO, boolean flag, String spartCode,
                                                      Long customId, String newProdTeamCode) {
        List<CodeReplacementVO> spartIndexCostList = null;
        if (flag) {
            spartIndexCostList = iCodeReplacementDao.getBlurAccCombShipQuery(dataVO, spartCode, customId,
                    newProdTeamCode);
        } else {
            newProdTeamCode = CostReductUtils.setProdTeamCode(dataVO, newProdTeamCode);
            spartIndexCostList = iCodeReplacementDao.getCombAccShipQuery(dataVO, spartCode, newProdTeamCode);
        }
        return spartIndexCostList;
    }

    /**
     * 新老编码月度累计成本偏差查询功能
     *
     * @param dataVO 参数
     * @return 结果
     * @throws CommonApplicationException 异常
     */
    @JalorOperation(code = "monthlyAccumulatedCostVariance", desc = "编码替换服务-新老编码月度累计成本偏差查询功能")
    @Override
    public ResultDataVO monthlyAccumulatedCostVariance(CodeReplacementVO dataVO) throws CommonApplicationException {
        // 选择层级（2+1） || 是否虚化（2）。查询的表不同，总共3*2=6组组合
        Map resultMap = new HashMap();
        // 获取用户权限
        dropDownService.setPermissionParameter(dataVO);
        Boolean marketingAnalystIndMark = marketingAnalystIndResultMap(dataVO);
        if (marketingAnalystIndMark) {
            return ResultDataVO.success(resultMap);
        }
        verifyMandatoryParameters(dataVO);
        // 选择层级（2+1） || 是否虚化（2）。查询的表不同，总共3*2=6组组合
        String isNeedBlur = dataVO.getIsNeedBlur();
        String selectionLevel = determineCurrentSelectionLevel(dataVO);
        setYearInfo(dataVO);
        if (dataVO.getPreCus()) {
            CostReductUtils.getPreCusIndexCostList(dataVO);
            List<CodeReplacementVO> newSpartList = iCodeReplacementDao.getPreBlurSpartCVAccExpList(dataVO, CommonConstant.CODE_TYPE_NEW);
            List<CodeReplacementVO> oldSpartList = iCodeReplacementDao.getPreBlurSpartCVAccExpList(dataVO, CommonConstant.CODE_TYPE_OLD);
            resultMap.put(DATA_LIST, CostReductUtils.processCVList(newSpartList, oldSpartList));
        } else {
            monthlyAccCostList(dataVO, isNeedBlur, selectionLevel, resultMap);
        }
        return ResultDataVO.success(resultMap);

    }

    private void monthlyAccCostList(CodeReplacementVO dataVO, String isNeedBlur, String selectionLevel, Map resultMap) {
        // 选择层级可分为SPART层级与PBI维度层级，区别在于关系、替代关系与新旧SpartCode是否有值。
        // 获取加密key
        String plainText = ConfigUtil.getInstance().get16PlainText();
        switch (selectionLevel) {
            case CommonConstant.SELECTION_LEVEL_SPART_LEVEL:
                // 是否虚化
                if (Boolean.TRUE.equals(Boolean.valueOf(isNeedBlur))) {
                    List<CodeReplacementVO> newSpartList = iCodeReplacementDao.getBlurSpartCVAccExpList(dataVO,
                            CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()), dataVO.getCustomId(), dataVO.getNewProdTeamCode(),
                            CommonConstant.CODE_TYPE_NEW, plainText);
                    List<CodeReplacementVO> oldSpartList = iCodeReplacementDao.getBlurSpartCVAccExpList(dataVO,
                            CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()), dataVO.getCustomId(), dataVO.getOldProdTeamCode(),
                            CommonConstant.CODE_TYPE_OLD, plainText);
                    resultMap.put(DATA_LIST, CostReductUtils.processCVList(newSpartList, oldSpartList));
                } else {
                    List<CodeReplacementVO> newSpartList = iCodeReplacementDao.getSpartCVAccList(dataVO,
                            CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()), dataVO.getNewProdTeamCode(), CommonConstant.SPART_NAME, plainText);
                    List<CodeReplacementVO> oldSpartList = iCodeReplacementDao.getSpartCVAccList(dataVO,
                            CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()), dataVO.getOldProdTeamCode(), CommonConstant.SPART_NAME, plainText);
                    resultMap.put(DATA_LIST, CostReductUtils.processCVList(newSpartList, oldSpartList));
                }
                break;
            case CommonConstant.SELECTION_LEVEL_TOP_SPART_LEVEL:
                // topSpart 有 4情况。 此处做按单个的虚化属性做数据查询
                if (isNeedBlur.split("\\|").length == 2) {
                    List<CodeReplacementVO> newSpartList = getCVAccDataInfo(dataVO,
                            Boolean.valueOf(isNeedBlur.split("\\|")[0]), CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()),
                            dataVO.getNewCustomId(), dataVO.getNewProdTeamCode(), plainText);
                    List<CodeReplacementVO> oldSpartList = getCVAccDataInfo(dataVO,
                            Boolean.valueOf(isNeedBlur.split("\\|")[1]), CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()),
                            dataVO.getOldCustomId(), dataVO.getOldProdTeamCode(), plainText);
                    resultMap.put(DATA_LIST, CostReductUtils.processCVList(newSpartList, oldSpartList));
                }
                break;
            case CommonConstant.SELECTION_LEVEL_PBI_LEVEL:
                List<CodeReplacementVO> spartIndexCostList = iCodeReplacementDao.getPbiIndexAccCVList(dataVO, plainText);
                Map<String, List<CodeReplacementVO>> collect = spartIndexCostList.stream()
                        .collect(Collectors.groupingBy(CodeReplacementVO::getCodeType));
                resultMap.put(DATA_LIST, CostReductUtils.processCVList(collect.get(CommonConstant.CODE_TYPE_NEW), collect.get(CommonConstant.CODE_TYPE_OLD)));
                break;
            default:
                break;
        }
    }

    public List<CodeReplacementVO> getCVAccDataInfo(CodeReplacementVO dataVO, boolean flag, String spartCode,
                                                    Long customId, String newProdTeamCode, String plainText) {
        List<CodeReplacementVO> spartIndexCostList = null;
        if (flag) {
            spartIndexCostList = iCodeReplacementDao.getBlurSpartCVAccList(dataVO, spartCode, customId, newProdTeamCode,
                    "TOP-SPART", plainText);
        } else {
            newProdTeamCode = CostReductUtils.setProdTeamCode(dataVO, newProdTeamCode);
            spartIndexCostList = iCodeReplacementDao.getSpartCVAccList(dataVO, spartCode, newProdTeamCode, "TOP-SPART",
                    plainText);
        }
        return spartIndexCostList;
    }

    /**
     * 数据下载
     *
     * @param dataVO 参数
     * @return 结果
     * @throws CommonApplicationException 异常
     */
    @JalorOperation(code = "dataDownload", desc = "编码替换服务-数据下载")
    @Audit(module = "codeReplacementService-dataDownload", operation = "dataDownload", message = "编码替换服务-数据下载")
    @Override
    public ResultDataVO dataDownload(HttpServletResponse response, CodeReplacementExpVO dataVO)
            throws ApplicationException {
        // 获取用户权限
        dropDownService.setPermissionParameter(dataVO);
        IExcelTemplateBeanManager templateBeanManager = null;
        verifyMandatoryParameters(dataVO);
        dataVO.setQueryLevel(determineCurrentSelectionLevel(dataVO));
        if (StringUtils.equals(dataVO.getQueryLevel(), CommonConstant.SELECTION_LEVEL_PBI_LEVEL)) {
            templateBeanManager = CodeReplaceTemplateEnum.getByCode("02", "");
        } else {
            templateBeanManager = getiExcelTemplateBeanManager(dataVO);
        }
        setYearInfo(dataVO);
        Map parmMap = new HashMap();
        parmMap.put("exportModuleName", "成本指数-ICT-编码替换分析");
        parmMap.put("exportFileName", "产业成本指数-编码替换" + System.currentTimeMillis());
        iExportProcessorService.fillEasyExcelExport(response, templateBeanManager, dataVO, parmMap);
        return ResultDataVO.success();
    }

    @NotNull
    private IExcelTemplateBeanManager getiExcelTemplateBeanManager(CodeReplacementExpVO dataVO) throws CommonApplicationException {
        IExcelTemplateBeanManager templateBeanManager;
        if (StringUtils.isNotBlank(dataVO.getIsNeedBlur()) && dataVO.getIsNeedBlur().contains("true")) {
            if (StringUtils.equals(dataVO.getQueryLevel(), CommonConstant.SELECTION_LEVEL_TOP_SPART_LEVEL)) {
                templateBeanManager = CodeReplaceTemplateEnum.getByCode("04", "");
            } else {
                templateBeanManager = CodeReplaceTemplateEnum.getByCode("02", "");
            }
        } else {
            if (StringUtils.equals(dataVO.getQueryLevel(), CommonConstant.SELECTION_LEVEL_TOP_SPART_LEVEL)) {
                templateBeanManager = CodeReplaceTemplateEnum.getByCode("03", "");
            } else {
                templateBeanManager = CodeReplaceTemplateEnum.getByCode("01", "");
            }
        }
        return templateBeanManager;
    }

    /**
     * 基期切换
     *
     * @param codeReplacementVO 参数
     * @return 结果
     * @throws CommonApplicationException 异常
     */
    @JalorOperation(code = "switchBasePeriodId", desc = "编码替换服务-基期切换")
    @Override
    public ResultDataVO switchBasePeriodId(CodeReplacementVO codeReplacementVO) throws ApplicationException {
        log.info("==>Begin codeReplacementService#switchBasePeriodId");
        // 必填字段校验
        verifyMandatoryParameters(codeReplacementVO);
        // 设置版本ID和报告期开始时间与结束时间
        setYearInfo(codeReplacementVO);
        codeReplacementVO.setQueryLevel(determineCurrentSelectionLevel(codeReplacementVO));
        // 任务状态初始化设置
        DmFocVarifyTaskVO varifyTaskVO = new DmFocVarifyTaskVO();
        varifyTaskVO.setTaskType("ict_data_codeReplacement");
        varifyTaskVO.setStatus("PROCESSING");
        JSONObject jsonObject = new JSONObject(codeReplacementVO);
        jsonObject.set("oldSpartCode", CostReductUtils.getSpartCodeStr(codeReplacementVO.getOldSpartCode()));
        jsonObject.set("newSpartCode", CostReductUtils.getSpartCodeStr(codeReplacementVO.getNewSpartCode()));
        refreshDataByFunction(jsonObject.toString(), varifyTaskVO);
        return ResultDataVO.success(varifyTaskVO);
    }


    // 刷新函数
    public void refreshDataByFunction(String jsonStr, DmFocVarifyTaskVO varifyTaskVO) {
        if (null == varifyTaskVO.getTaskId()) {
            varifyTaskVO.setPeriodId(Integer.parseInt(new JSONObject(jsonStr).get("basePeriodId", String.class)));
            varifyTaskVO.setTaskId(varifyTaskDao.getVerifyTaskId());
            varifyTaskDao.insertVerifyTask(varifyTaskVO);
        } else {
            varifyTaskDao.updateVerifyTask(varifyTaskVO);
        }
        // 异步调用函数
        asyncMonthAnalysisService.refreshDataByFunction(jsonStr, varifyTaskVO);
    }

    @Nullable
    private Boolean marketingAnalystIndResultMap(CodeReplacementVO dataVO) {
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        // 行销分析师角色拦截
        if (CommonConstant.SPECIAL_ROLES.contains(currentRole.getRoleName()) && CommonConstant.GRANULARITY_TYPE_NOT_PROD_LIST.contains(dataVO.getGranularityType())) {
            return true;
        }
        return false;
    }
}
