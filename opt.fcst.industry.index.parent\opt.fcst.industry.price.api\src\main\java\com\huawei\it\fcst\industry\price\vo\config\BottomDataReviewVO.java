/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.vo.config;

import com.huawei.it.fcst.industry.price.vo.common.CommonPriceBaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/9/6
 */
@Setter
@Getter
@EqualsAndHashCode(callSuper=true)
@NoArgsConstructor
@ApiModel(value = "底层数据审视VO")
public class BottomDataReviewVO extends CommonPriceBaseVO implements Serializable {

    private static final long serialVersionUID = 217717592083744735L;

    @ApiModelProperty("文件名称")
    private String fileName;

    @ApiModelProperty("l1编码")
    private String lv1Code;

    @ApiModelProperty("l1名称")
    private String lv1CnName;

    @ApiModelProperty("l1编码")
    private String lv2Code;

    @ApiModelProperty("l2名称")
    private String lv2CnName;

    @ApiModelProperty("l3编码")
    private String lv3Code;

    @ApiModelProperty("l3名称")
    private String lv3CnName;

    @ApiModelProperty("l4编码")
    private String lv4Code;

    @ApiModelProperty("l4名称")
    private String lv4CnName;

    @ApiModelProperty("关键字")
    private String keyword;

    @ApiModelProperty("合同号")
    private String hwContractNum;

    @ApiModelProperty("页面")
    private String pageFlag;

    @ApiModelProperty("开始时间")
    private String beginDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("操作类型")
    private String modifyType;

    @ApiModelProperty("版本号")
    private Long annualVersionId;

}
