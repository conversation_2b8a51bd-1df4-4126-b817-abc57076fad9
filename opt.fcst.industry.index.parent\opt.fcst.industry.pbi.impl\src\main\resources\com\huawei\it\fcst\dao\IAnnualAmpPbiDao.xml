<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.pbi.dao.IAnnualAmpPbiDao">
    <resultMap type="com.huawei.it.fcst.industry.pbi.vo.annual.DmFocAnnualAmpVO" id="annualResultMap">
        <result property="id" column="id"/>
        <result property="versionId" column="version_id"/>
        <result property="periodYear" column="period_year"/>
        <result property="prodRndTeamCode" column="prod_rnd_team_code"/>
        <result property="prodRndTeamCode" column="prod_rnd_team_catg_code"/>
        <result property="prodRndTeamCnName" column="prod_rnd_team_cn_name"/>
        <result property="lv0ProdRndTeamCnName" column="lv0_prod_rd_team_cn_name"/>
        <result property="lv0ProdRndTeamCode" column="lv0_prod_rnd_team_code"/>
        <result property="lv1ProdRndTeamCnName" column="lv1_prod_rd_team_cn_name"/>
        <result property="lv1ProdRndTeamCode" column="lv1_prod_rnd_team_code"/>
        <result property="lv2ProdRndTeamCnName" column="lv2_prod_rd_team_cn_name"/>
        <result property="lv2ProdRndTeamCode" column="lv2_prod_rnd_team_code"/>
        <result property="lv3ProdRndTeamCnName" column="lv3_prod_rd_team_cn_name"/>
        <result property="lv3ProdRndTeamCode" column="lv3_prod_rnd_team_code"/>
        <result property="lv4ProdRndTeamCode" column="lv4_prod_rnd_team_code"/>
        <result property="lv4ProdRndTeamCnName" column="lv4_prod_rd_team_cn_name"/>
        <result property="lv0ProdRndTeamCnName" column="lv0_industry_catg_cn_name"/>
        <result property="lv0ProdRndTeamCode" column="lv0_industry_catg_code"/>
        <result property="lv1ProdRndTeamCnName" column="lv1_industry_catg_cn_name"/>
        <result property="lv1ProdRndTeamCode" column="lv1_industry_catg_code"/>
        <result property="lv2ProdRndTeamCnName" column="lv2_industry_catg_cn_name"/>
        <result property="lv2ProdRndTeamCode" column="lv2_industry_catg_code"/>
        <result property="lv3ProdRndTeamCnName" column="lv3_industry_catg_cn_name"/>
        <result property="lv3ProdRndTeamCode" column="lv3_industry_catg_code"/>
        <result property="lv4ProdRndTeamCnName" column="lv4_industry_catg_cn_name"/>
        <result property="lv4ProdRndTeamCode" column="lv4_industry_catg_code"/>
        <result property="lv0ProdRndTeamCnName" column="lv0_prod_list_cn_name"/>
        <result property="lv0ProdRndTeamCode" column="lv0_prod_list_code"/>
        <result property="lv1ProdRndTeamCnName" column="lv1_prod_list_cn_name"/>
        <result property="lv1ProdRndTeamCode" column="lv1_prod_list_code"/>
        <result property="lv2ProdRndTeamCnName" column="lv2_prod_list_cn_name"/>
        <result property="lv2ProdRndTeamCode" column="lv2_prod_list_code"/>
        <result property="lv2ProdRndTeamCode" column="lv2_industry_catg_code"/>
        <result property="lv3ProdRndTeamCnName" column="lv3_prod_list_cn_name"/>
        <result property="lv3ProdRndTeamCode" column="lv3_prod_list_code"/>
        <result property="lv4ProdRndTeamCnName" column="lv4_prod_list_cn_name"/>
        <result property="lv4ProdRndTeamCode" column="lv4_prod_list_code"/>
        <result property="parentLevel" column="parent_level"/>
        <result property="parentCode" column="parent_code"/>
        <result property="parentCnName" column="parent_cn_name"/>
        <result property="groupCode" column="group_code"/>
        <result property="groupCnName" column="group_cn_name"/>
        <result property="groupLevel" column="group_level"/>
        <result property="annualAmp" column="annual_amp"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDdate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="appendFlag" column="append_flag"/>
        <result property="weightRate" column="weight_rate"/>
        <result property="statusCode" column="status_code"/>
        <result property="groupCodeAndName" column="groupCodeAndName"/>
        <result property="weightAnnualAmpPercent" column="weightAnnualAmpPercent"/>
        <result property="appendYear" column="append_year"/>
        <result property="dimensionCode" column="dimension_code"/>
        <result property="dimensionCnName" column="dimension_cn_name"/>
        <result property="dimensionSubCategoryCode" column="dimension_subcategory_code"/>
        <result property="dimensionSubCategoryCnName" column="dimension_subcategory_cn_name"/>
        <result property="dimensionSubDetailCode" column="dimension_sub_detail_code"/>
        <result property="dimensionSubDetailCnName" column="dimension_sub_detail_cn_name"/>
        <result property="spartCode" column="spart_code"/>
        <result property="spartCnName" column="spart_cn_name"/>
        <result property="customCnName" column="custom_cn_name"/>
        <result property="customId" column="custom_id"/>
        <result property="rmbCostAmt" column="rmb_cost_amt"/>
    </resultMap>

    <sql id="userPermisson">
        <choose>
            <when test='granularityType == "IRB"'>
                <if test='groupLevel == "LV0" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
                    <foreach collection='lv1DimensionSet' item="code" open="AND amp.prod_rnd_team_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                    <foreach collection='lv2DimensionSet' item="code" open="AND amp.prod_rnd_team_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='groupLevel == "LV2" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                    <foreach collection='lv3DimensionSet' item="code" open="AND amp.prod_rnd_team_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
            </when>
            <when test='granularityType == "INDUS"'>
                <if test='groupLevel == "LV0" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
                    <foreach collection='lv1DimensionSet' item="code" open="AND amp.industry_catg_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                    <foreach collection='lv2DimensionSet' item="code" open="AND amp.industry_catg_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='groupLevel == "LV2" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                    <foreach collection='lv3DimensionSet' item="code" open="AND amp.industry_catg_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
            </when>
            <when test='granularityType == "PROD"'>
                <if test='groupLevel == "LV0" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
                    <foreach collection='lv1DimensionSet' item="code" open="AND amp.prod_list_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                    <foreach collection='lv2DimensionSet' item="code" open="AND amp.prod_list_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='groupLevel == "LV2" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                    <foreach collection='lv3DimensionSet' item="code" open="AND amp.prod_list_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
            </when>
        </choose>
    </sql>

    <sql id="allProdTeamCode">
        <choose>
            <when test='teamLevel == "LV0"'>
                <choose>
                    <when test='granularityType == "IRB"'>
                        info.lv0_prod_rd_team_cn_name,info.lv0_prod_rnd_team_code
                    </when>
                    <when test='granularityType == "INDUS"'>
                        info.lv0_industry_catg_cn_name,info.lv0_industry_catg_code
                    </when>
                    <when test='granularityType == "PROD"'>
                        info.lv0_prod_list_cn_name,info.lv0_prod_list_code
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </when>
            <when test='teamLevel == "LV1"'>
                <choose>
                    <when test='granularityType == "IRB"'>
                        info.lv0_prod_rd_team_cn_name,info.lv0_prod_rnd_team_code,info.lv1_prod_rd_team_cn_name,info.lv1_prod_rnd_team_code
                    </when>
                    <when test='granularityType == "INDUS"'>
                        info.lv0_industry_catg_cn_name,info.lv0_industry_catg_code,info.lv1_industry_catg_cn_name,info.lv1_industry_catg_code
                    </when>
                    <when test='granularityType == "PROD"'>
                        info.lv0_prod_list_cn_name,info.lv0_prod_list_code,info.lv1_prod_list_cn_name,info.lv1_prod_list_code
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </when>
            <when test='teamLevel == "LV2"'>
                <choose>
                    <when test='granularityType == "IRB"'>
                        info.lv0_prod_rd_team_cn_name,info.lv0_prod_rnd_team_code,info.lv1_prod_rd_team_cn_name,info.lv1_prod_rnd_team_code,
                        info.lv2_prod_rd_team_cn_name,info.lv2_prod_rnd_team_code
                    </when>
                    <when test='granularityType == "INDUS"'>
                        info.lv0_industry_catg_cn_name,info.lv0_industry_catg_code,info.lv1_industry_catg_cn_name,info.lv1_industry_catg_code,
                        info.lv2_industry_catg_cn_name,info.lv2_industry_catg_code
                    </when>
                    <when test='granularityType == "PROD"'>
                        info.lv0_prod_list_cn_name,info.lv0_prod_list_code,info.lv1_prod_list_cn_name,info.lv1_prod_list_code,
                        info.lv2_prod_list_cn_name,info.lv2_prod_list_code
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </when>
            <when test='teamLevel == "LV3"'>
                <choose>
                    <when test='granularityType == "IRB"'>
                        info.lv0_prod_rd_team_cn_name,info.lv0_prod_rnd_team_code,info.lv1_prod_rd_team_cn_name,info.lv1_prod_rnd_team_code,
                        info.lv2_prod_rd_team_cn_name,info.lv2_prod_rnd_team_code,info.lv3_prod_rd_team_cn_name,info.lv3_prod_rnd_team_code
                    </when>
                    <when test='granularityType == "INDUS"'>
                        info.lv0_industry_catg_cn_name,info.lv0_industry_catg_code,info.lv1_industry_catg_cn_name,info.lv1_industry_catg_code,
                        info.lv2_industry_catg_cn_name,info.lv2_industry_catg_code,info.lv3_industry_catg_cn_name,info.lv3_industry_catg_code
                    </when>
                    <when test='granularityType == "PROD"'>
                        info.lv0_prod_list_cn_name,info.lv0_prod_list_code,info.lv1_prod_list_cn_name,info.lv1_prod_list_code,
                        info.lv2_prod_list_cn_name,info.lv2_prod_list_code,info.lv3_prod_list_cn_name,info.lv3_prod_list_code
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </when>
            <when test='teamLevel == "LV4"'>
                <choose>
                    <when test='granularityType == "IRB"'>
                        info.lv0_prod_rd_team_cn_name,info.lv0_prod_rnd_team_code,info.lv1_prod_rd_team_cn_name,info.lv1_prod_rnd_team_code,
                        info.lv2_prod_rd_team_cn_name,info.lv2_prod_rnd_team_code,info.lv3_prod_rd_team_cn_name,info.lv3_prod_rnd_team_code,
                        info.lv4_prod_rd_team_cn_name,info.lv4_prod_rnd_team_code
                    </when>
                    <when test='granularityType == "INDUS"'>
                        info.lv0_industry_catg_cn_name,info.lv0_industry_catg_code,info.lv1_industry_catg_cn_name,info.lv1_industry_catg_code,
                        info.lv2_industry_catg_cn_name,info.lv2_industry_catg_code,
                        info.lv3_industry_catg_cn_name,info.lv3_industry_catg_code,info.lv4_industry_catg_cn_name,info.lv4_industry_catg_code
                    </when>
                    <when test='granularityType == "PROD"'>
                        info.lv0_prod_list_cn_name,info.lv0_prod_list_code,info.lv1_prod_list_cn_name,info.lv1_prod_list_code,
                        info.lv2_prod_list_cn_name,info.lv2_prod_list_code,
                        info.lv3_prod_list_cn_name,info.lv3_prod_list_code, info.lv4_prod_list_cn_name,info.lv4_prod_list_code
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </when>
            <otherwise>
            </otherwise>
        </choose>
    </sql>

    <sql id="leftjoinDimInfo">
        <if test='groupLevel != "LV0" and groupLevel != "LV1" and groupLevel != "LV2" and groupLevel != "LV3" and groupLevel != "LV4"'>
            <choose>
                <when test='groupLevel == "DIMENSION"'>
                    and nvl ( amp.dimension_code, 'snull' ) = nvl ( info.dimension_code, 'snull' )
                </when>
                <when test='groupLevel == "SUBCATEGORY"'>
                    and nvl ( amp.dimension_code, 'snull' ) = nvl ( info.dimension_code, 'snull' )
                    and nvl ( amp.dimension_subcategory_code, 'snull' ) = nvl ( info.dimension_subcategory_code,
                    'snull' )
                </when>
                <when test='groupLevel == "SUB_DETAIL"'>
                    and nvl ( amp.dimension_code, 'snull' ) = nvl ( info.dimension_code, 'snull' )
                    and nvl ( amp.dimension_subcategory_code, 'snull' ) = nvl ( info.dimension_subcategory_code,
                    'snull' )
                    and nvl ( amp.dimension_sub_detail_code, 'snull' ) = nvl ( info.dimension_sub_detail_code,
                    'snull' )
                </when>
                <when test='groupLevel == "SPART"'>
                    and nvl ( amp.group_code, 'snull' ) = nvl ( info.spart_code, 'snull' )
                </when>
            </choose>
        </if>
        <if test='isNeedBlur != true'>
            <if test='teamLevel == "LV0"'>
                <choose>
                    <when test='granularityType == "IRB"'>
                        and amp.prod_rnd_team_code = info.lv0_prod_rnd_team_code
                    </when>
                    <when test='granularityType == "INDUS"'>
                        and amp.industry_catg_code = info.lv0_industry_catg_code
                    </when>
                    <when test='granularityType == "PROD"'>
                        and amp.prod_list_code = info.lv0_prod_list_code
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
            <if test='teamLevel == "LV1"'>
                <choose>
                    <when test='granularityType == "IRB"'>
                        and amp.prod_rnd_team_code = info.lv1_prod_rnd_team_code
                    </when>
                    <when test='granularityType == "INDUS"'>
                        and amp.industry_catg_code = info.lv1_industry_catg_code
                    </when>
                    <when test='granularityType == "PROD"'>
                        and amp.prod_list_code = info.lv1_prod_list_code
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
            <if test='teamLevel == "LV2"'>
                <choose>
                    <when test='granularityType == "IRB"'>
                        and amp.prod_rnd_team_code = info.lv2_prod_rnd_team_code
                    </when>
                    <when test='granularityType == "INDUS"'>
                        and amp.industry_catg_code = info.lv2_industry_catg_code
                    </when>
                    <when test='granularityType == "PROD"'>
                        and amp.prod_list_code = info.lv2_prod_list_code
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
            <if test='teamLevel == "LV3"'>
                <choose>
                    <when test='granularityType == "IRB"'>
                        and amp.prod_rnd_team_code = info.lv3_prod_rnd_team_code
                    </when>
                    <when test='granularityType == "INDUS"'>
                        and amp.industry_catg_code = info.lv3_industry_catg_code
                    </when>
                    <when test='granularityType == "PROD"'>
                        and amp.prod_list_code = info.lv3_prod_list_code
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
            <if test='teamLevel == "LV4"'>
                <choose>
                    <when test='granularityType == "IRB"'>
                        and amp.prod_rnd_team_code = info.lv4_prod_rnd_team_code
                    </when>
                    <when test='granularityType == "INDUS"'>
                        and amp.industry_catg_code = info.lv4_industry_catg_code
                    </when>
                    <when test='granularityType == "PROD"'>
                        and amp.prod_list_code = info.lv4_prod_list_code
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
        </if>
    </sql>

    <select id="allIndustryNormalCost" resultMap="annualResultMap">
        select distinct
        amp.group_cn_name,amp.period_year,amp.group_level,amp.group_code,
        CONCAT(ROUND(amp.annual_amp*100,1),'%') as annual_amp,
        status.status_code,status.append_year,
        <if test='groupLevel == "DIMENSION"'>
            info.dimension_code,info.dimension_cn_name,
        </if>
        <if test='groupLevel == "SUBCATEGORY"'>
            info.dimension_code,info.dimension_cn_name,info.dimension_subcategory_code,info.dimension_subcategory_cn_name,
        </if>
        <if test='groupLevel == "SUB_DETAIL"'>
            info.dimension_code,info.dimension_cn_name,info.dimension_subcategory_code,info.dimension_subcategory_cn_name,
            info.dimension_sub_detail_code,info.dimension_sub_detail_cn_name,
        </if>
        <if test='groupLevel == "SPART"'>
            info.spart_code,info.spart_cn_name,
        </if>
        <choose>
            <when test='granularityType == "IRB"'>
                amp.prod_rd_team_cn_name as prod_rnd_team_cn_name,
            </when>
            <when test='granularityType == "INDUS"'>
                amp.industry_catg_cn_name as prod_rnd_team_cn_name,
            </when>
            <when test='granularityType == "PROD"'>
                amp.prod_list_cn_name as prod_rnd_team_cn_name,
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='isMultipleSelect == false'>
            weight.weight_rate*100 AS weight_rate,
        </if>
        <if test='isMultipleSelect == true'>
            weight.absolute_weight*100 AS weight_rate,
        </if>
        <include refid="allProdTeamCode" />
        <if test='ytdFlag == "N"'>
            from fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_AMP_T amp
            left join fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_WEIGHT_T weight
        </if>
        <if test='ytdFlag == "Y"'>
            from fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_YTD_AMP_T amp
            left join fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_YTD_WEIGHT_T weight
        </if>
        on amp.view_flag = weight.view_flag and amp.group_code = weight.group_code
        and amp.version_id =weight.version_id
        and amp.group_level = weight.group_level
        and amp.parent_code = weight.parent_code
        and amp.period_year = weight.period_year
        and amp.oversea_flag = weight.oversea_flag
        and amp.bg_code = weight.bg_code
        and amp.region_code = weight.region_code
        and amp.repoffice_code = weight.repoffice_code
        <if test='costType == "PSP"'>
            and nvl(amp.software_mark,'snull') = nvl( weight.software_mark,'snull')
        </if>
        and nvl(amp.dimension_code,'snull') = nvl( weight.dimension_code,'snull')
        and nvl(amp.dimension_subcategory_code,'snull') = nvl( weight.dimension_subcategory_code,'snull')
        and nvl(amp.dimension_sub_detail_code,'snull') = nvl( weight.dimension_sub_detail_code,'snull')
        and nvl ( amp.main_flag, 'snull' ) = nvl ( weight.main_flag, 'snull' )
        and nvl ( amp.code_attributes, 'snull' ) = nvl ( weight.code_attributes, 'snull' )
        <choose>
            <when test='granularityType == "IRB"'>
                and amp.prod_rnd_team_code = weight.prod_rnd_team_code
            </when>
            <when test='granularityType == "INDUS"'>
                and amp.industry_catg_code = weight.industry_catg_code
            </when>
            <when test='granularityType == "PROD"'>
                and amp.prod_list_code = weight.prod_list_code
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='ytdFlag == "N"'>
            left join fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_STATUS_T status
        </if>
        <if test='ytdFlag == "Y"'>
            left join fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_YTD_STATUS_T status
        </if>
        on amp.group_code = status.group_code
        and amp.version_id = status.version_id
        and amp.group_level = status.group_level
        and amp.period_year = status.period_year
        and amp.view_flag = status.view_flag
        and amp.parent_code = status.parent_code
        and amp.oversea_flag = status.oversea_flag
        and amp.bg_code = status.bg_code
        and amp.region_code = status.region_code
        and amp.repoffice_code = status.repoffice_code
        <if test='costType == "PSP"'>
            and amp.software_mark = status.software_mark
        </if>
        and nvl ( amp.dimension_code, 'snull' ) = nvl ( status.dimension_code, 'snull' )
        and nvl ( amp.dimension_subcategory_code, 'snull' ) = nvl ( status.dimension_subcategory_code, 'snull' )
        and nvl ( amp.dimension_sub_detail_code, 'snull' ) = nvl ( status.dimension_sub_detail_code, 'snull' )
        and nvl ( amp.main_flag, 'snull' ) = nvl ( status.main_flag, 'snull' )
        and nvl ( amp.code_attributes, 'snull' ) = nvl ( status.code_attributes, 'snull' )
        <choose>
            <when test='granularityType == "IRB"'>
                and amp.prod_rnd_team_code = status.prod_rnd_team_code
            </when>
            <when test='granularityType == "INDUS"'>
                and amp.industry_catg_code = status.industry_catg_code
            </when>
            <when test='granularityType == "PROD"'>
                and amp.prod_list_code = status.prod_list_code
            </when>
            <otherwise>
            </otherwise>
        </choose>
        left join fin_dm_opt_foi.dm_fcst_ict_${tablePreFix}_dim_info_t info
        on amp.version_id = info.version_id
        and amp.view_flag = info.view_flag
        and amp.oversea_flag = info.oversea_flag and amp.bg_code = info.bg_code
        <if test='mainFlag == "N"'>
            and amp.group_level = info.group_level
        </if>
        <choose>
            <when test='regionCode == "GLOBAL" and repofficeCode != "ALL"'>
                and info.region_code is null
            </when>
            <otherwise>
                and amp.region_code = info.region_code
            </otherwise>
        </choose>
        and amp.repoffice_code = info.repoffice_code
        <if test='costType == "PSP"'>
            AND nvl(amp.software_mark, 'snull') = nvl(info.software_mark, 'snull')
        </if>
        <choose>
            <when test='groupLevel != "DIMENSION" and groupLevel != "SUBCATEGORY" and groupLevel != "SUB_DETAIL" and groupLevel != "SPART" and mainFlag =="N"'>
                and info.main_flag is null
            </when>
            <otherwise>
                and nvl ( amp.main_flag, 'snull' ) = nvl ( info.main_flag, 'snull' )
            </otherwise>
        </choose>
        <include refid="leftjoinDimInfo"/>
        <choose>
            <when test='groupLevel == "DIMENSION"'>
                and amp.group_code = info.dimension_code
            </when>
            <when test='groupLevel == "SUBCATEGORY"'>
                and amp.group_code = info.dimension_subcategory_code
            </when>
            <when test='groupLevel == "SUB_DETAIL"'>
                and amp.group_code = info.dimension_sub_detail_code
            </when>
            <when test='groupLevel == "SPART"'>
                and amp.group_code = info.spart_code
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <include refid="commonSearchWhere"></include>
            <if test='groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND amp.group_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='prodRndTeamCodeList != null and prodRndTeamCodeList.size() > 0'>
                <choose>
                    <when test='granularityType == "IRB"'>
                        <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.prod_rnd_team_code IN (" close=")"
                                 index="index"
                                 separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='granularityType == "INDUS"'>
                        <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.industry_catg_code IN (" close=")"
                                 index="index"
                                 separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='granularityType == "PROD"'>
                        <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.prod_list_code IN (" close=")"
                                 index="index"
                                 separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
        <include refid="userPermisson"></include>
        </trim>
        order by amp.period_year,weight_rate desc
    </select>

    <sql id ="commonSearchWhere">
        <if test='versionId != null'>
            and amp.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='overseaFlag != null and overseaFlag != ""'>
            and amp.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='softwareMark != null and softwareMark != ""'>
            and amp.software_mark = #{softwareMark,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and amp.bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and amp.region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and amp.repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag != null and mainFlag != ""'>
            and amp.main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes != null and codeAttributes != ""'>
            and amp.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and amp.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and amp.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='dimensionCode != null and dimensionCode != ""'>
            and amp.dimension_code = #{dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubCategoryCode != null and dimensionSubCategoryCode != ""'>
            and amp.dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubDetailCode != null and dimensionSubDetailCode != ""'>
            and amp.dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
        <if test='periodYear != null and periodYear != ""'>
            and amp.period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND amp.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        and amp.del_flag = 'N'
    </sql>

    <select id="getAnnualperiodYearList" resultType="java.lang.String">
        with temp_date as (
            SELECT SUBSTR(MAX(endtime), 0, 4 )  AS period_year
            FROM fin_dm_opt_foi.dm_fcst_ict_period_id_dim
            WHERE version_id = #{versionId} )
        select period_year from (
                                    select  to_char(period_year) as period_year from temp_date
                                    union
                                    select  to_char(period_year-1) as period_year from temp_date
                                    union
                                    select  to_char(period_year-2) as period_year  from temp_date
                                )
        order by period_year desc
    </select>

    <select id="findGroupCodeOrderByWeight" resultMap="annualResultMap">
        select group_code, group_cn_name,
        <if test='isMultipleSelect == false'>
            sum(ROUND(weight_rate*100,1)) weight_rate
        </if>
        <if test='isMultipleSelect == true'>
            sum(ROUND(absolute_weight*100,1)) weight_rate
        </if>
        <if test='ytdFlag == "N"'>
            from fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_WEIGHT_T
        </if>
        <if test='ytdFlag == "Y"'>
            from fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_YTD_WEIGHT_T
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
        <include refid="commonAmpSearchWhere"></include>
            <if test='year != null and year != ""'>
                and period_year = #{year,jdbcType=VARCHAR}
            </if>
            <if test='parentCodeList != null and parentCodeList.size() > 0'>
                <foreach collection='parentCodeList' item="code" open="AND parent_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
        GROUP BY group_code,group_cn_name
        ORDER BY weight_rate DESC
    </select>

    <sql id="commonAmpSearchWhere">
        <if test='versionId != null'>
            and version_id = #{versionId}
        </if>
        <if test='overseaFlag != null and overseaFlag != ""'>
            and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='softwareMark != null and softwareMark != ""'>
            and software_mark = #{softwareMark,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag != null and mainFlag != ""'>
            and main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes != null and codeAttributes != ""'>
            and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='dimensionCode != null and dimensionCode != ""'>
            and dimension_code = #{dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubCategoryCode != null and dimensionSubCategoryCode != ""'>
            and dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubDetailCode != null and dimensionSubDetailCode != ""'>
            and dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
        <if test='prodRndTeamCodeList != null and prodRndTeamCodeList.size() > 0'>
            <choose>
                <when test='granularityType == "IRB"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND prod_rnd_team_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='granularityType == "INDUS"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND industry_catg_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='granularityType == "PROD"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND prod_list_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        and del_flag = 'N'
    </sql>

    <select id="multiIndustryPbiCostChart" resultMap="annualResultMap">
        select amp.parent_code,amp.parent_cn_name,amp.period_year,
        CONCAT(ROUND( SUM ( amp.annual_amp*100 ), 1 ),'%') AS annual_amp,
        amp.group_code, amp.group_cn_name, amp.group_level,
        <if test='isMultipleSelect == false'>
            CONCAT(ROUND(SUM ( weight.weight_rate*100 ),1),'%') AS weight_rate,
        </if>
        <if test='isMultipleSelect == true'>
            CONCAT(ROUND(SUM ( weight.absolute_weight*100 ),1),'%') AS weight_rate,
        </if>
        CONCAT (amp.group_code, ' ', amp.group_cn_name ) AS groupCodeAndName,
        status.status_code,weight.append_flag, max(status.append_year) AS append_year,
        amp.dimension_code,amp.dimension_subcategory_code,amp.dimension_sub_detail_code,
        <choose>
            <when test='granularityType == "IRB"'>
                amp.prod_rnd_team_code as prod_rnd_team_catg_code,amp.prod_rd_team_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "INDUS"'>
                amp.industry_catg_code as prod_rnd_team_catg_code, amp.industry_catg_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "PROD"'>
                amp.prod_list_code as prod_rnd_team_catg_code, amp.prod_list_cn_name as prod_rnd_team_cn_name
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='ytdFlag == "N"'>
            from fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_AMP_T amp
            left join fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_WEIGHT_T weight
        </if>
        <if test='ytdFlag == "Y"'>
            from fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_YTD_AMP_T amp
            left join fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_YTD_WEIGHT_T weight
        </if>
        on amp.view_flag = weight.view_flag
        and amp.version_id =weight.version_id
        and amp.group_code = weight.group_code
        and amp.group_level = weight.group_level
        and amp.parent_code = weight.parent_code
        and amp.period_year = weight.period_year
        and amp.oversea_flag = weight.oversea_flag
        and amp.bg_code = weight.bg_code
        and amp.region_code = weight.region_code
        and amp.repoffice_code = weight.repoffice_code
        <if test='costType == "PSP"'>
            and nvl(amp.software_mark,'snull') = nvl( weight.software_mark,'snull')
        </if>
        and nvl(amp.dimension_code,'snull') = nvl( weight.dimension_code,'snull')
        and nvl(amp.dimension_subcategory_code,'snull') = nvl( weight.dimension_subcategory_code,'snull')
        and nvl(amp.dimension_sub_detail_code,'snull') = nvl( weight.dimension_sub_detail_code,'snull')
        and nvl ( amp.main_flag, 'snull' ) = nvl ( weight.main_flag, 'snull' )
        and nvl ( amp.code_attributes, 'snull' ) = nvl ( weight.code_attributes, 'snull' )
        <choose>
            <when test='granularityType == "IRB"'>
                and amp.prod_rnd_team_code = weight.prod_rnd_team_code
            </when>
            <when test='granularityType == "INDUS"'>
                and amp.industry_catg_code = weight.industry_catg_code
            </when>
            <when test='granularityType == "PROD"'>
                and amp.prod_list_code = weight.prod_list_code
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='ytdFlag == "N"'>
            left join fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_STATUS_T status
        </if>
        <if test='ytdFlag == "Y"'>
            left join fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_YTD_STATUS_T status
        </if>
        on amp.group_code = status.group_code
        and amp.version_id = status.version_id
        and amp.group_level = status.group_level
        and amp.period_year = status.period_year
        and amp.view_flag = status.view_flag
        and amp.parent_code = status.parent_code
        and amp.oversea_flag = status.oversea_flag
        and amp.bg_code = status.bg_code
        and amp.region_code = status.region_code
        and amp.repoffice_code = status.repoffice_code
        <if test='costType == "PSP"'>
            and amp.software_mark = status.software_mark
        </if>
        and nvl(amp.dimension_code,'snull') = nvl( status.dimension_code,'snull')
        and nvl(amp.dimension_subcategory_code,'snull') = nvl( status.dimension_subcategory_code,'snull')
        and nvl(amp.dimension_sub_detail_code,'snull') = nvl( status.dimension_sub_detail_code,'snull')
        and nvl ( amp.main_flag, 'snull' ) = nvl ( status.main_flag, 'snull' )
        and nvl ( amp.code_attributes, 'snull' ) = nvl ( status.code_attributes, 'snull' )
        <choose>
            <when test='granularityType == "IRB"'>
                and amp.prod_rnd_team_code = status.prod_rnd_team_code
            </when>
            <when test='granularityType == "INDUS"'>
                and amp.industry_catg_code = status.industry_catg_code
            </when>
            <when test='granularityType == "PROD"'>
                and amp.prod_list_code = status.prod_list_code
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
          <include refid="commonSearchWhere"></include>
            <if test='parentCodeList != null and parentCodeList.size() > 0'>
                <foreach collection='parentCodeList' item="code" open="AND amp.parent_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='prodRndTeamCodeList != null and prodRndTeamCodeList.size() > 0'>
                <choose>
                    <when test='granularityType == "IRB"'>
                        <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.prod_rnd_team_code IN (" close=")"
                                 index="index"
                                 separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='granularityType == "INDUS"'>
                        <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.industry_catg_code IN (" close=")"
                                 index="index"
                                 separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='granularityType == "PROD"'>
                        <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.prod_list_code IN (" close=")"
                                 index="index"
                                 separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
            <if test='groupCodeOrder != null and groupCodeOrder != ""'>
                <foreach collection="groupCodeOrder.split(',')" item="item" open="and amp.group_code IN (" close=")"
                         index="index" separator=",">
                    #{item}
                </foreach>
            </if>
            <include refid="userPermisson"></include>
        </trim>
        GROUP BY amp.parent_code,amp.parent_cn_name,prod_rnd_team_cn_name,prod_rnd_team_catg_code,weight.append_flag,
        amp.group_level,amp.group_code,amp.group_cn_name,groupCodeAndName,amp.period_year,status.status_code,
        amp.dimension_code,amp.dimension_subcategory_code, amp.dimension_sub_detail_code
        order by locate(amp.group_code, #{groupCodeOrder}), amp.period_year
    </select>

    <select id="multiIndustryMinLevelChart" resultMap="annualResultMap">
        select amp.parent_code,amp.parent_cn_name,amp.period_year,
        CONCAT(ROUND( SUM ( amp.annual_amp*100 ), 1 ),'%') AS annual_amp,
        amp.group_code, amp.group_cn_name, amp.group_level,
        CONCAT(ROUND(SUM ( weight.weight_rate*100 ),1),'%') AS weight_rate,
        CONCAT (amp.group_code, ' ', amp.group_cn_name ) AS groupCodeAndName,
        status.status_code, max(status.append_year) AS append_year,
        amp.dimension_code,amp.dimension_subcategory_code,amp.dimension_sub_detail_code,
        amp.lv_code as prod_rnd_team_catg_code,amp.lv_cn_name as prod_rnd_team_cn_name
        <if test='ytdFlag == "N"'>
            from fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_MID_GROUP_AMP_T amp
            left join fin_dm_opt_foi.DM_FCST_ICT_${combTablePreFix}_BASE_CUS_ANNL_WEIGHT_T weight
        </if>
        <if test='ytdFlag == "Y"'>
            from fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_MID_YTD_GROUP_AMP_T amp
            left join fin_dm_opt_foi.DM_FCST_ICT_${combTablePreFix}_BASE_CUS_ANNL_YTD_WEIGHT_T weight
        </if>
        on amp.view_flag = weight.view_flag
        and amp.version_id =weight.version_id
        and amp.group_code = weight.group_code
        and amp.group_level = weight.group_level
        and amp.parent_code = weight.parent_code
        and amp.period_year = weight.period_year
        and amp.oversea_flag = weight.oversea_flag
        and amp.bg_code = weight.bg_code
        and amp.region_code = weight.region_code
        and amp.repoffice_code = weight.repoffice_code
        <if test='costType == "PSP"'>
            and amp.software_mark = weight.software_mark
        </if>
        and nvl ( amp.main_flag, 'snull' ) = nvl ( weight.main_flag, 'snull' )
        and nvl ( amp.code_attributes, 'snull' ) = nvl ( weight.code_attributes, 'snull' )
        and amp.lv_code = weight.lv4_code
        <if test='customIdList != null and customIdList.size() > 0'>
            <foreach collection='customIdList' item="id" open="AND weight.custom_id IN (" close=")" index="index"
                     separator=",">
                #{id}
            </foreach>
        </if>
        <if test='ytdFlag == "N"'>
            left join fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_STATUS_T status
        </if>
        <if test='ytdFlag == "Y"'>
            left join fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_YTD_STATUS_T status
        </if>
        on amp.group_code = status.group_code
        and amp.version_id = status.version_id
        and amp.group_level = status.group_level
        and amp.period_year = status.period_year
        and amp.view_flag = status.view_flag
        and amp.parent_code = status.parent_code
        and amp.oversea_flag = status.oversea_flag
        and amp.bg_code = status.bg_code
        and amp.region_code = status.region_code
        and amp.repoffice_code = status.repoffice_code
        <if test='costType == "PSP"'>
            and amp.software_mark = status.software_mark
        </if>
        and nvl(amp.dimension_code,'snull') = nvl( status.dimension_code,'snull')
        and nvl(amp.dimension_subcategory_code,'snull') = nvl( status.dimension_subcategory_code,'snull')
        and nvl(amp.dimension_sub_detail_code,'snull') = nvl( status.dimension_sub_detail_code,'snull')
        and nvl ( amp.main_flag, 'snull' ) = nvl ( status.main_flag, 'snull' )
        and nvl ( amp.code_attributes, 'snull' ) = nvl ( status.code_attributes, 'snull' )
        <choose>
            <when test='granularityType == "IRB"'>
                and amp.lv_code = status.prod_rnd_team_code
            </when>
            <when test='granularityType == "INDUS"'>
                and amp.lv_code = status.industry_catg_code
            </when>
            <when test='granularityType == "PROD"'>
                and amp.lv_code = status.prod_list_code
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
         <include refid="commonSearchWhere"></include>
            <if test='viewFlag != null and viewFlag != "" and viewFlag == "PROD_SPART"'>
                and amp.group_level = 'SPART'
            </if>
            <if test='viewFlag != null and viewFlag != "" and viewFlag == "DIMENSION"'>
                and amp.group_level = 'SUB_DETAIL'
            </if>
            <if test='customGroupCodeList != null and customGroupCodeList.size() > 0'>
                <foreach collection='customGroupCodeList' item="code" open="AND amp.group_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='prodRndTeamCodeList != null and prodRndTeamCodeList.size() > 0'>
                <choose>
                    <when test='teamLevel == "LV0"'>
                        <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.lv0_code IN (" close=")"
                                 index="index"
                                 separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='teamLevel == "LV1"'>
                        <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.lv1_code IN (" close=")"
                                 index="index"
                                 separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='teamLevel == "LV2"'>
                        <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.lv2_code IN (" close=")"
                                 index="index"
                                 separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='teamLevel == "LV3"'>
                        <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.lv3_code IN (" close=")"
                                 index="index"
                                 separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='teamLevel == "LV4"'>
                        <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.lv4_code IN (" close=")"
                                 index="index"
                                 separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
            <if test='prodRndTeamCodeOrder != null and prodRndTeamCodeOrder != ""'>
                <foreach collection="prodRndTeamCodeOrder.split(',')" item="item" open="and amp.lv_code IN (" close=")"
                         index="index" separator=",">
                    #{item}
                </foreach>
            </if>
        </trim>
        GROUP BY amp.parent_code,amp.parent_cn_name,prod_rnd_team_cn_name,prod_rnd_team_catg_code,
        amp.group_level,amp.group_code,amp.group_cn_name,groupCodeAndName,amp.period_year,status.status_code,
        amp.dimension_code,amp.dimension_subcategory_code, amp.dimension_sub_detail_code
        <if test='condition != null and condition !="" and condition =="minLevel"'>
            order by locate(amp.lv_code, #{prodRndTeamCodeOrder}), amp.period_year
        </if>
        <if test='condition == null or condition ==""'>
            order by locate(amp.group_code, #{groupCodeOrder}), amp.period_year
        </if>
    </select>

    <select id="industryPbiCostList" resultMap="annualResultMap">
        select prod_rnd_team_cn_name,prod_rnd_team_code,
        dimension_code,dimension_cn_name,
        dimension_subcategory_code,dimension_subcategory_cn_name,
        dimension_sub_detail_code,dimension_sub_detail_cn_name,
        parent_code,parent_cn_name,period_year,annual_amp,group_code,group_level,group_cn_name,
        weight_rate, status_code, annual_amp * weight_rate/100 as weightAnnualAmpPercent,
        append_year,append_flag
        FROM (
        SELECT DISTINCT
        amp.group_level,
        <choose>
            <when test='isNeedBlur != true and viewFlag == "PROD_SPART" and groupLevel == "SPART"'>
                dim.dimension_code,
                dim.dimension_cn_name,
                dim.dimension_subcategory_code,
                dim.dimension_subcategory_cn_name,
            </when>
            <otherwise>
                amp.dimension_code,
                amp.dimension_cn_name,
                amp.dimension_subcategory_code,
                amp.dimension_subcategory_cn_name,
            </otherwise>
        </choose>
        amp.dimension_sub_detail_code,amp.dimension_sub_detail_cn_name,
        amp.parent_code,amp.parent_cn_name,amp.period_year,
        ROUND(amp.annual_amp*100,1) as annual_amp,amp.group_code,
        amp.group_cn_name,
        <choose>
            <when test='isMultipleSelect == true'>
                weight.absolute_weight*100 as weight_rate,
            </when>
            <otherwise>
                weight.weight_rate*100 as weight_rate,
            </otherwise>
        </choose>
        status.status_code as status_code,weight.append_flag, status.append_year,
        <choose>
            <when test='granularityType == "IRB"'>
                amp.prod_rnd_team_code,amp.prod_rd_team_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "INDUS"'>
                amp.industry_catg_code as prod_rnd_team_code, amp.industry_catg_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "PROD"'>
                amp.prod_list_code as prod_rnd_team_code, amp.prod_list_cn_name as prod_rnd_team_cn_name
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='ytdFlag == "N"'>
            from fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_AMP_T amp
            left join fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_WEIGHT_T weight
        </if>
        <if test='ytdFlag == "Y"'>
            from fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_YTD_AMP_T amp
            left join fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_YTD_WEIGHT_T weight
        </if>
        on amp.view_flag = weight.view_flag
        and amp.version_id =weight.version_id
        and amp.group_code = weight.group_code
        and amp.group_level = weight.group_level
        and amp.parent_code = weight.parent_code
        and amp.period_year = weight.period_year
        and amp.oversea_flag = weight.oversea_flag
        and amp.bg_code = weight.bg_code
        and amp.region_code = weight.region_code
        and amp.repoffice_code = weight.repoffice_code
        <if test='costType == "PSP"'>
            and nvl(amp.software_mark,'snull') = nvl( weight.software_mark,'snull')
        </if>
        and nvl(amp.dimension_code,'snull') = nvl( weight.dimension_code,'snull')
        and nvl(amp.dimension_subcategory_code,'snull') = nvl( weight.dimension_subcategory_code,'snull')
        and nvl(amp.dimension_sub_detail_code,'snull') = nvl( weight.dimension_sub_detail_code,'snull')
        and nvl ( amp.main_flag, 'snull' ) = nvl ( weight.main_flag, 'snull' )
        and nvl ( amp.code_attributes, 'snull' ) = nvl ( weight.code_attributes, 'snull' )
        <choose>
            <when test='granularityType == "IRB"'>
                and amp.prod_rnd_team_code = weight.prod_rnd_team_code
            </when>
            <when test='granularityType == "INDUS"'>
                and amp.industry_catg_code = weight.industry_catg_code
            </when>
            <when test='granularityType == "PROD"'>
                and amp.prod_list_code = weight.prod_list_code
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='ytdFlag == "N"'>
            left join fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_STATUS_T status
        </if>
        <if test='ytdFlag == "Y"'>
            left join fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_YTD_STATUS_T status
        </if>
        on amp.group_code = status.group_code
        and amp.version_id = status.version_id
        and amp.group_level = status.group_level
        and amp.period_year = status.period_year
        and amp.view_flag = status.view_flag
        and amp.parent_code = status.parent_code
        and amp.oversea_flag = status.oversea_flag
        and amp.bg_code = status.bg_code
        and amp.region_code = status.region_code
        and amp.repoffice_code = status.repoffice_code
        <if test='costType == "PSP"'>
            and amp.software_mark = status.software_mark
        </if>
        and nvl(amp.dimension_code,'snull') = nvl( status.dimension_code,'snull')
        and nvl(amp.dimension_subcategory_code,'snull') = nvl( status.dimension_subcategory_code,'snull')
        and nvl(amp.dimension_sub_detail_code,'snull') = nvl( status.dimension_sub_detail_code,'snull')
        and nvl ( amp.main_flag, 'snull' ) = nvl ( status.main_flag, 'snull' )
        and nvl ( amp.code_attributes, 'snull' ) = nvl ( status.code_attributes, 'snull' )
        <choose>
            <when test='granularityType == "IRB"'>
                and amp.prod_rnd_team_code = status.prod_rnd_team_code
            </when>
            <when test='granularityType == "INDUS"'>
                and amp.industry_catg_code = status.industry_catg_code
            </when>
            <when test='granularityType == "PROD"'>
                and amp.prod_list_code = status.prod_list_code
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='isNeedBlur != true and viewFlag == "PROD_SPART" and groupLevel == "SPART"'>
            LEFT JOIN fin_dm_opt_foi.dm_fcst_${costType}_dimension_spart_dim_t dim
            ON dim.del_flag = 'N'
            AND dim.spart_code = amp.group_code
            <choose>
                <when test='granularityType == "IRB"'>
                    AND dim.prod_rnd_team_code = amp.prod_rnd_team_code
                </when>
                <when test='granularityType == "INDUS"'>
                    AND dim.industry_catg_code = amp.industry_catg_code
                </when>
                <otherwise>
                    AND dim.prod_list_code = amp.prod_list_code
                </otherwise>
            </choose>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
          <include refid="commonSearchWhere"></include>
        <if test='year != null and year !=""'>
            and amp.period_year = #{year,jdbcType=VARCHAR}
        </if>
        <if test='parentCodeList != null and parentCodeList.size() > 0'>
            <foreach collection='parentCodeList' item="code" open="AND amp.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='prodRndTeamCodeList != null and prodRndTeamCodeList.size() > 0'>
            <choose>
                <when test='granularityType == "IRB"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.prod_rnd_team_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='granularityType == "INDUS"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.industry_catg_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='granularityType == "PROD"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.prod_list_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
            <include refid="userPermisson"></include>
        </trim>
        )
        order by period_year,weight_rate desc,group_cn_name
    </select>

    <select id="industryMinLevelCostList" resultMap="annualResultMap">
        select prod_rnd_team_cn_name,prod_rnd_team_code,
        dimension_code,dimension_cn_name,
        dimension_subcategory_code,dimension_subcategory_cn_name,
        dimension_sub_detail_code,dimension_sub_detail_cn_name,
        parent_code,parent_cn_name,period_year,annual_amp,group_code,group_level,group_cn_name,
        weight_rate, status_code, annual_amp * weight_rate/100 as weightAnnualAmpPercent, append_year
        from
        (select distinct amp.group_level,
        amp.dimension_code,amp.dimension_cn_name,
        amp.dimension_subcategory_code,amp.dimension_subcategory_cn_name,
        amp.dimension_sub_detail_code,amp.dimension_sub_detail_cn_name,
        amp.parent_code,amp.parent_cn_name,amp.period_year,
        ROUND(amp.annual_amp*100,1) as annual_amp,amp.group_code,
        amp.group_cn_name,weight.weight_rate*100 as weight_rate,
        status.status_code as status_code, status.append_year,
        amp.lv_code as prod_rnd_team_code,amp.lv_cn_name as prod_rnd_team_cn_name
        <if test='ytdFlag == "N"'>
            from fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_MID_GROUP_AMP_T amp
            left join fin_dm_opt_foi.DM_FCST_ICT_${combTablePreFix}_BASE_CUS_ANNL_WEIGHT_T weight
        </if>
        <if test='ytdFlag == "Y"'>
            from fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_MID_YTD_GROUP_AMP_T amp
            left join fin_dm_opt_foi.DM_FCST_ICT_${combTablePreFix}_BASE_CUS_ANNL_YTD_WEIGHT_T weight
        </if>
        on amp.view_flag = weight.view_flag
        and amp.version_id =weight.version_id
        and amp.group_code = weight.group_code
        and amp.group_level = weight.group_level
        and amp.parent_code = weight.parent_code
        and amp.period_year = weight.period_year
        and amp.oversea_flag = weight.oversea_flag
        and amp.bg_code = weight.bg_code
        and amp.region_code = weight.region_code
        and amp.repoffice_code = weight.repoffice_code
        <if test='costType == "PSP"'>
            and amp.software_mark = weight.software_mark
        </if>
        and nvl ( amp.main_flag, 'snull' ) = nvl ( weight.main_flag, 'snull' )
        and nvl ( amp.code_attributes, 'snull' ) = nvl ( weight.code_attributes, 'snull' )
        and amp.lv_code = weight.lv4_code
        <if test='customIdList != null and customIdList.size() > 0'>
            <foreach collection='customIdList' item="id" open="AND weight.custom_id IN (" close=")" index="index"
                     separator=",">
                #{id}
            </foreach>
        </if>
        <if test='ytdFlag == "N"'>
            left join fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_STATUS_T status
        </if>
        <if test='ytdFlag == "Y"'>
            left join fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_YTD_STATUS_T status
        </if>
        on amp.group_code = status.group_code
        and amp.version_id = status.version_id
        and amp.group_level = status.group_level
        and amp.period_year = status.period_year
        and amp.view_flag = status.view_flag
        and amp.parent_code = status.parent_code
        and amp.oversea_flag = status.oversea_flag
        and amp.bg_code = status.bg_code
        and amp.region_code = status.region_code
        and amp.repoffice_code = status.repoffice_code
        <if test='costType == "PSP"'>
            and amp.software_mark = status.software_mark
        </if>
        and nvl(amp.dimension_code,'snull') = nvl( status.dimension_code,'snull')
        and nvl(amp.dimension_subcategory_code,'snull') = nvl( status.dimension_subcategory_code,'snull')
        and nvl(amp.dimension_sub_detail_code,'snull') = nvl( status.dimension_sub_detail_code,'snull')
        and nvl ( amp.main_flag, 'snull' ) = nvl ( status.main_flag, 'snull' )
        and nvl ( amp.code_attributes, 'snull' ) = nvl ( status.code_attributes, 'snull' )
        <choose>
            <when test='granularityType == "IRB"'>
                and amp.lv_code = status.prod_rnd_team_code
            </when>
            <when test='granularityType == "INDUS"'>
                and amp.lv_code = status.industry_catg_code
            </when>
            <when test='granularityType == "PROD"'>
                and amp.lv_code = status.prod_list_code
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
          <include refid="commonSearchWhere"></include>
            <if test='year != null and year !=""'>
                and amp.period_year = #{year,jdbcType=VARCHAR}
            </if>
            <if test='viewFlag != null and viewFlag != "" and viewFlag == "PROD_SPART"'>
                and amp.group_level = 'SPART'
            </if>
            <if test='viewFlag != null and viewFlag != "" and viewFlag == "DIMENSION"'>
                and amp.group_level = 'SUB_DETAIL'
            </if>
            <if test='customGroupCodeList != null and customGroupCodeList.size() > 0'>
                <foreach collection='customGroupCodeList' item="code" open="AND amp.group_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='prodRndTeamCodeList != null and prodRndTeamCodeList.size() > 0'>
                <choose>
                    <when test='teamLevel == "LV0"'>
                        <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.lv0_code IN (" close=")"
                                 index="index"
                                 separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='teamLevel == "LV1"'>
                        <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.lv1_code IN (" close=")"
                                 index="index"
                                 separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='teamLevel == "LV2"'>
                        <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.lv2_code IN (" close=")"
                                 index="index"
                                 separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='teamLevel == "LV3"'>
                        <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.lv3_code IN (" close=")"
                                 index="index"
                                 separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='teamLevel == "LV4"'>
                        <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.lv4_code IN (" close=")"
                                 index="index"
                                 separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
        </trim>
        )
        order by period_year,weight_rate desc,group_cn_name
    </select>

    <select id="distributePbiCostChart" resultMap="annualResultMap">
        select distinct
        dimension_code,dimension_cn_name,
        dimension_subcategory_code,dimension_subcategory_cn_name,
        dimension_sub_detail_code,dimension_sub_detail_cn_name,
        group_level,group_code,group_cn_name,parent_code,parent_cn_name,
        period_year,rmb_cost_amt,
        <choose>
            <when test='granularityType == "IRB"'>
                prod_rd_team_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "INDUS"'>
                industry_catg_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "PROD"'>
                prod_list_cn_name as prod_rnd_team_cn_name
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='ytdFlag == "N"'>
            from fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_COST_T amp
        </if>
        <if test='ytdFlag == "Y"'>
            from fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_YTD_COST_T amp
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
        <include refid="commonAmpSearchWhere"></include>
            <if test='periodYear != null and periodYear != ""'>
                and period_year = #{periodYear,jdbcType=VARCHAR}
            </if>
            <if test='groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND group_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='yearList != null and yearList.size() > 0'>
                <foreach collection='yearList' item="year" open="AND period_year IN (" close=")"
                         index="index" separator=",">
                    #{year}
                </foreach>
            </if>
            <include refid="userPermisson"></include>
        </trim>
        order by period_year,rmb_cost_amt desc
    </select>

    <select id="multiNormalSpartChart" resultMap="annualResultMap">
        select
        amp.period_year,
        CONCAT(ROUND( SUM ( amp.annual_amp*100 ), 1 ),'%') AS annual_amp,
        amp.group_code, amp.group_cn_name, amp.group_level,
        <if test='isMultipleSelect == false'>
            CONCAT(ROUND(SUM ( weight.weight_rate*100 ),1),'%') AS weight_rate,
        </if>
        <if test='isMultipleSelect == true'>
            CONCAT(ROUND(SUM ( weight.absolute_weight*100 ),1),'%') AS weight_rate,
        </if>
        CONCAT (amp.group_code, ' ', amp.group_cn_name ) AS groupCodeAndName,
        status.status_code,weight.append_flag, max(status.append_year) AS append_year,
        amp.dimension_code,amp.dimension_subcategory_code,amp.dimension_sub_detail_code,
        <choose>
            <when test='granularityType == "IRB"'>
                amp.prod_rnd_team_code as prod_rnd_team_catg_code,amp.prod_rd_team_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "INDUS"'>
                amp.industry_catg_code as prod_rnd_team_catg_code, amp.industry_catg_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "PROD"'>
                amp.prod_list_code as prod_rnd_team_catg_code, amp.prod_list_cn_name as prod_rnd_team_cn_name
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='ytdFlag == "N"'>
            from fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_AMP_T amp
            left join fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_WEIGHT_T weight
        </if>
        <if test='ytdFlag == "Y"'>
            from fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_YTD_AMP_T amp
            left join fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_YTD_WEIGHT_T weight
        </if>
        on amp.view_flag = weight.view_flag
        and amp.version_id =weight.version_id
        and amp.group_code = weight.group_code
        and amp.group_level = weight.group_level
        and amp.parent_code = weight.parent_code
        and amp.period_year = weight.period_year
        and amp.oversea_flag = weight.oversea_flag
        and amp.bg_code = weight.bg_code
        and amp.region_code = weight.region_code
        and amp.repoffice_code = weight.repoffice_code
        <if test='costType == "PSP"'>
            and nvl(amp.software_mark,'snull') = nvl( weight.software_mark,'snull')
        </if>
        and nvl(amp.dimension_code,'snull') = nvl( weight.dimension_code,'snull')
        and nvl(amp.dimension_subcategory_code,'snull') = nvl( weight.dimension_subcategory_code,'snull')
        and nvl(amp.dimension_sub_detail_code,'snull') = nvl( weight.dimension_sub_detail_code,'snull')
        and nvl ( amp.main_flag, 'snull' ) = nvl ( weight.main_flag, 'snull' )
        and nvl ( amp.code_attributes, 'snull' ) = nvl ( weight.code_attributes, 'snull' )
        <choose>
            <when test='granularityType == "IRB"'>
                and amp.prod_rnd_team_code = weight.prod_rnd_team_code
            </when>
            <when test='granularityType == "INDUS"'>
                and amp.industry_catg_code = weight.industry_catg_code
            </when>
            <when test='granularityType == "PROD"'>
                and amp.prod_list_code = weight.prod_list_code
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='ytdFlag == "N"'>
            left join fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_STATUS_T status
        </if>
        <if test='ytdFlag == "Y"'>
            left join fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_YTD_STATUS_T status
        </if>
        on amp.group_code = status.group_code
        and amp.version_id = status.version_id
        and amp.group_level = status.group_level
        and amp.period_year = status.period_year
        and amp.view_flag = status.view_flag
        and amp.parent_code = status.parent_code
        and amp.oversea_flag = status.oversea_flag
        and amp.bg_code = status.bg_code
        and amp.region_code = status.region_code
        and amp.repoffice_code = status.repoffice_code
        <if test='costType == "PSP"'>
            and amp.software_mark = status.software_mark
        </if>
        and nvl(amp.dimension_code,'snull') = nvl( status.dimension_code,'snull')
        and nvl(amp.dimension_subcategory_code,'snull') = nvl( status.dimension_subcategory_code,'snull')
        and nvl(amp.dimension_sub_detail_code,'snull') = nvl( status.dimension_sub_detail_code,'snull')
        and nvl ( amp.main_flag, 'snull' ) = nvl ( status.main_flag, 'snull' )
        and nvl ( amp.code_attributes, 'snull' ) = nvl ( status.code_attributes, 'snull' )
        <choose>
            <when test='granularityType == "IRB"'>
                and amp.prod_rnd_team_code = status.prod_rnd_team_code
            </when>
            <when test='granularityType == "INDUS"'>
                and amp.industry_catg_code = status.industry_catg_code
            </when>
            <when test='granularityType == "PROD"'>
                and amp.prod_list_code = status.prod_list_code
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
         <include refid="commonSearchWhere"></include>
            <if test='year != null and year != ""'>
                and amp.period_year = #{year,jdbcType=VARCHAR}
            </if>
            <if test='subGroupCodeList != null and subGroupCodeList.size() > 0'>
                <foreach collection='subGroupCodeList' item="code" open="AND amp.group_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='prodRndTeamCodeList != null and prodRndTeamCodeList.size() > 0'>
                <choose>
                    <when test='granularityType == "IRB"'>
                        <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.prod_rnd_team_code IN (" close=")"
                                 index="index"
                                 separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='granularityType == "INDUS"'>
                        <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.industry_catg_code IN (" close=")"
                                 index="index"
                                 separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='granularityType == "PROD"'>
                        <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.prod_list_code IN (" close=")"
                                 index="index"
                                 separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
            <if test='groupCodeOrder != null and groupCodeOrder != ""'>
                <foreach collection="groupCodeOrder.split(',')" item="item" open="and amp.group_code IN (" close=")"
                         index="index" separator=",">
                    #{item}
                </foreach>
            </if>
        </trim>
        GROUP BY amp.parent_code,amp.parent_cn_name,prod_rnd_team_cn_name,prod_rnd_team_catg_code,weight.append_flag,
        amp.group_level,amp.group_code,amp.group_cn_name,groupCodeAndName,amp.period_year,status.status_code,
        amp.dimension_code,amp.dimension_subcategory_code, amp.dimension_sub_detail_code
        order by amp.period_year
    </select>

</mapper>