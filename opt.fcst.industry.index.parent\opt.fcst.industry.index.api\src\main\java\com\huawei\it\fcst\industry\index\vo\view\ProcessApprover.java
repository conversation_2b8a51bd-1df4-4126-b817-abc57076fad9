/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.view;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/22
 */
@Getter
@Setter
public class ProcessApprover {

    private String nodeLevel;

    private String roleId;

    private String roleName;

    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<DimensionInfo> dimensionInfo;


}
