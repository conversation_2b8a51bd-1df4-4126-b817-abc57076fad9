/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.config;

import cn.hutool.core.util.StrUtil;
import com.huawei.it.fcst.industry.index.constant.CommonConstant;
import com.huawei.it.fcst.industry.index.constant.Constant;
import com.huawei.it.fcst.industry.index.dao.IDmFocTopCateInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTopItemInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocVersionDao;
import com.huawei.it.fcst.industry.index.enums.IndustryConst;
import com.huawei.it.fcst.industry.index.enums.IndustryIndexEnum;
import com.huawei.it.fcst.industry.index.enums.ModuleEnum;
import com.huawei.it.fcst.industry.index.impl.annual.StatisticsExcelService;
import com.huawei.it.fcst.industry.index.impl.common.CommonService;
import com.huawei.it.fcst.industry.index.impl.month.MonthExportService;
import com.huawei.it.fcst.industry.index.utils.ExcelUtil;
import com.huawei.it.fcst.industry.index.utils.ExcelUtilPro;
import com.huawei.it.fcst.industry.index.utils.FcstIndexUtil;
import com.huawei.it.fcst.industry.index.utils.ObjectCopyUtil;
import com.huawei.it.fcst.industry.index.utils.PoiEnum;
import com.huawei.it.fcst.industry.index.vo.common.AbstractExcelTitleVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFoiImpExpRecordVO;
import com.huawei.it.fcst.industry.index.vo.common.ExcelUploadVO;
import com.huawei.it.fcst.industry.index.vo.common.ExportExcelVo;
import com.huawei.it.fcst.industry.index.vo.common.HeaderVo;
import com.huawei.it.fcst.industry.index.vo.common.LeafExcelTitleVO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocTopCateInfoDTO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocTopItemInfoDTO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoDTO;
import com.huawei.it.fcst.industry.index.vo.config.HistoryInputVO;
import com.huawei.it.fcst.util.CellStyles;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.cursor.Cursor;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Inject;
import javax.inject.Named;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/5/9
 */
@Slf4j
@Named("configExportService")
public class ConfigExportService {
    private static final Logger LOGGER = LoggerFactory.getLogger(MonthExportService.class);

    @Inject
    private IDmFocVersionDao dmFocVersionDao;

    @Inject
    private IDmFocTopCateInfoDao dmFocTopCateInfoDao;

    @Inject
    private IDmFocTopItemInfoDao dmFocTopItemInfoDao;

    @Inject
    private StatisticsExcelService statisticsExcelService;

    @Inject
    private CommonService commonService;

    @Inject
    private ExcelUtil excelUtil;

    @Inject
    private ExcelUtilPro excelUtilPro;

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void purchaseExport(HistoryInputVO historyInputVO, IRequestContext current)
            throws ApplicationException, IOException {
        RequestContextManager.setCurrent(current);
        Timestamp creationDate = new Timestamp(System.currentTimeMillis());
        List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
        // 通过id获取版本信息
        DmFocVersionInfoDTO versionDTO = dmFocVersionDao.findDmFocVersionDTOById(historyInputVO.getVersionId(),historyInputVO.getTablePreFix());
        // 获取需要导出的数据和动态表头
        List<String> yearPeriodList = FcstIndexUtil.getPeriod(versionDTO.getVersion());
        Map cateAndItemWithWight = getCateAndItemWithWight(versionDTO, historyInputVO,yearPeriodList);

        List<Map> cateItemWithWeightList = new ArrayList<>();
        if (cateAndItemWithWight.get("cateItemWithWeightList") instanceof List) {
            cateItemWithWeightList = (List<Map>) cateAndItemWithWight.get("cateItemWithWeightList");
        }
        String sheetName = "";
        if (cateAndItemWithWight.get("sheetName") instanceof String) {
            sheetName = (String) cateAndItemWithWight.get("sheetName");
        }
        String modelType = setModelType(historyInputVO);
        String bgName = historyInputVO.getLv0ProdListCnName();
        // 获取第一行表单
        List<AbstractExcelTitleVO> formsVO = getFormsVO(historyInputVO,versionDTO.getVersion(),bgName);
        // 设置第二行表头模型
        Set<String> titles = new LinkedHashSet<>();
        List<HeaderVo> headers = new ArrayList<HeaderVo>();
        if (cateAndItemWithWight.get("headers") instanceof List) {
            headers = (List<HeaderVo>) cateAndItemWithWight.get("headers");
        }
        FcstIndexUtil.setHeader(titleVoList, headers, titles);
        ExportExcelVo exportExcelVo =
                ExportExcelVo.builder()
                        .formInfoVo(formsVO)
                        .titleVoList(titleVoList)
                        .list(cateItemWithWeightList)
                        .sheetName(sheetName)
                        .fileName(historyInputVO.getFileName())
                        .yearPeriodList(yearPeriodList)
                        .historyInputVO(historyInputVO)
                        .mergeCell(false)
                        .build();
        List<ExportExcelVo> exportExcelVoList = new ArrayList<>();
        exportExcelVoList.add(exportExcelVo);
        DmFoiImpExpRecordVO expRecordVO = expSyncSelectColumnExcel(exportExcelVoList);
        // 插入导出数据
        expRecordVO.setCreationDate(creationDate);
        expRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        expRecordVO.setModuleType(modelType);
        statisticsExcelService.insertExportExcelRecord(expRecordVO);
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void madeExport(HistoryInputVO historyInputVO, IRequestContext current)
            throws ApplicationException, IOException {
        RequestContextManager.setCurrent(current);
        Timestamp creationDate = new Timestamp(System.currentTimeMillis());
        List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
        // 通过id获取版本信息
        DmFocVersionInfoDTO versionDTO = dmFocVersionDao.findDmFocVersionDTOById(historyInputVO.getVersionId(), historyInputVO.getTablePreFix());
        // 获取需要导出的数据和动态表头
        List<String> yearPeriodList = FcstIndexUtil.getPeriod(versionDTO.getVersion());
        Map tableHeaderMap = getDynamicTableHeader(versionDTO, historyInputVO,yearPeriodList);

        List<Map> tableHeaderList = new ArrayList<>();
        if (tableHeaderMap.get("tableHeaderList") instanceof List) {
            tableHeaderList = (List<Map>) tableHeaderMap.get("tableHeaderList");
        }
        String sheetName = "";
        if (tableHeaderMap.get("sheetName") instanceof String) {
            sheetName = (String) tableHeaderMap.get("sheetName");
        }
        // 设置导出到个人中心的页面模块
        String modelType = setMadeModelType(historyInputVO);
        String bgName = historyInputVO.getLv0ProdListCnName();
        // 获取第一行表单
        List<AbstractExcelTitleVO> formsVO = getFormsVO(historyInputVO, versionDTO.getVersion(), bgName);
        // 设置第二行表头模型
        Set<String> titles = new LinkedHashSet<>();
        List<HeaderVo> headers = new ArrayList<>();
        if (tableHeaderMap.get("headers") instanceof List) {
            headers = (List<HeaderVo>) tableHeaderMap.get("headers");
        }
        FcstIndexUtil.setHeader(titleVoList, headers, titles);
        ExportExcelVo exportExcelVo =
                ExportExcelVo.builder()
                        .formInfoVo(formsVO)
                        .titleVoList(titleVoList)
                        .list(tableHeaderList)
                        .sheetName(sheetName)
                        .yearPeriodList(yearPeriodList)
                        .historyInputVO(historyInputVO)
                        .fileName(historyInputVO.getFileName())
                        .mergeCell(false)
                        .build();
        List<ExportExcelVo> exportExcelVoList = new ArrayList<>();
        exportExcelVoList.add(exportExcelVo);

        DmFoiImpExpRecordVO expRecordVO = expSyncSelectColumnExcel(exportExcelVoList);
        // 插入导出数据
        expRecordVO.setModuleType(modelType);
        expRecordVO.setCreationDate(creationDate);
        expRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        statisticsExcelService.insertExportExcelRecord(expRecordVO);
    }


    @NotNull
    private String setMadeModelType(HistoryInputVO historyInputVO) {
        String modelType;
        if (IndustryConst.INDUSTRY_ORG.ICT.getValue().equals(historyInputVO.getIndustryOrg())) {
            modelType = IndustryConst.DataType.ITEM.getValue().equals(historyInputVO.getModelType()) ?
                    IndustryConst.INDUSTRY_ORG_PAGE.ICT_MODEL.getValue() + ModuleEnum.MANUFACTURE_ITEM.getCnName() : IndustryConst.INDUSTRY_ORG_PAGE.ICT_MODEL.getValue() + ModuleEnum.MANUFACTURE.getCnName();
        } else if (IndustryConst.INDUSTRY_ORG.ENERGY.getValue().equals(historyInputVO.getIndustryOrg())){
            modelType = IndustryConst.DataType.ITEM.getValue().equals(historyInputVO.getModelType()) ?
                    IndustryConst.INDUSTRY_ORG_PAGE.ENERGY_MODEL.getValue() + ModuleEnum.MANUFACTURE_ITEM.getCnName() : IndustryConst.INDUSTRY_ORG_PAGE.ENERGY_MODEL.getValue() + ModuleEnum.MANUFACTURE.getCnName();
        } else{
            modelType = IndustryConst.DataType.ITEM.getValue().equals(historyInputVO.getModelType()) ?
                    IndustryConst.INDUSTRY_ORG_PAGE.IAS_MODEL.getValue() + ModuleEnum.MANUFACTURE_ITEM.getCnName() : IndustryConst.INDUSTRY_ORG_PAGE.IAS_MODEL.getValue() + ModuleEnum.MANUFACTURE.getCnName();

        }
        return modelType;
    }

    private Map getDynamicTableHeader(DmFocVersionInfoDTO versionDTO, HistoryInputVO historyInputVO,List<String> yearPeriodList)
            throws CommonApplicationException {
        List<Map> tableHeaderList = new ArrayList<>();
        List<HeaderVo> headers;
        // 获取报告期范围
        if (StringUtils.isEmpty(versionDTO.getVersion())) {
            throw new CommonApplicationException("清单版本名称为空");
        }
        String sheetName = FcstIndexUtil.getSheetName(historyInputVO);
        setMadeDynamicHeader(historyInputVO, yearPeriodList);
        if (IndustryConst.DataType.ITEM.getValue().equals(historyInputVO.getModelType())) {
            // 设置ITEM查询对象
            headers = historyInputVO.getTopItemHeader();
        } else {
            // 设置制造对象清单的查询对象
            headers = historyInputVO.getTopCateHeader();
        }
        Map map = new HashMap();
        map.put("sheetName", sheetName);
        map.put("headers", headers);
        map.put("tableHeaderList", tableHeaderList);
        return map;
    }


    @NotNull
    public String setModelType(HistoryInputVO historyInputVO) {
        String modelType;
        if (IndustryConst.INDUSTRY_ORG.ICT.getValue().equals(historyInputVO.getIndustryOrg())) {
            modelType = IndustryConst.DataType.CATE.getValue().equals(historyInputVO.getModelType()) ?
                    IndustryConst.INDUSTRY_ORG_PAGE.ICT_MODEL.getValue() + ModuleEnum.CATEGORY.getCnName() : IndustryConst.INDUSTRY_ORG_PAGE.ICT_MODEL.getValue() + ModuleEnum.ITEM.getCnName();
        } else if (IndustryConst.INDUSTRY_ORG.ENERGY.getValue().equals(historyInputVO.getIndustryOrg())){
            modelType = IndustryConst.DataType.CATE.getValue().equals(historyInputVO.getModelType()) ?
                    IndustryConst.INDUSTRY_ORG_PAGE.ENERGY_MODEL.getValue() + ModuleEnum.CATEGORY.getCnName() : IndustryConst.INDUSTRY_ORG_PAGE.ENERGY_MODEL.getValue() + ModuleEnum.ITEM.getCnName();
        } else {
            modelType = IndustryConst.DataType.CATE.getValue().equals(historyInputVO.getModelType()) ?
                    IndustryConst.INDUSTRY_ORG_PAGE.IAS_MODEL.getValue() + ModuleEnum.CATEGORY.getCnName() : IndustryConst.INDUSTRY_ORG_PAGE.IAS_MODEL.getValue() + ModuleEnum.ITEM.getCnName();
        }
        return modelType;
    }

    private List<AbstractExcelTitleVO> getFormsVO(HistoryInputVO historyInputVO,String versionName,String bgName)
            throws ApplicationException {
        List<AbstractExcelTitleVO> formInfoVo = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setViewFlag(historyInputVO.getViewFlag());
        dmFocViewInfoVO.setGranularityType(historyInputVO.getGranularityType());
        dmFocViewInfoVO.setCostType(historyInputVO.getCostType());
        dmFocViewInfoVO.setIndustryOrg(historyInputVO.getIndustryOrg());
        commonService.setViewFlagValueWithLookUp(dmFocViewInfoVO);

        LeafExcelTitleVO costTypeVO = new LeafExcelTitleVO(Constant.StrEnum.COST_TYPE.getValue() + IndustryIndexEnum.getCostTypeFlag(historyInputVO.getCostType()).getDesc(),
                CommonConstant.VIEW_WIDTH, true, "costType", "costType", CellType.STRING, false);
        formInfoVo.add(costTypeVO);
        LeafExcelTitleVO caliberFlagVO = new LeafExcelTitleVO(Constant.StrEnum.CALIBER_FLAG.getValue() + IndustryIndexEnum.getCaliberFlag(historyInputVO.getCaliberFlag()).getDesc(),
                CommonConstant.VIEW_WIDTH, true, "caliberFlag", "caliberFlag", CellType.STRING, false);
        formInfoVo.add(caliberFlagVO);
        LeafExcelTitleVO granuleVO = new LeafExcelTitleVO(Constant.StrEnum.GRANULE.getValue() + IndustryIndexEnum.getGranularityType(historyInputVO.getGranularityType()).getDesc(),
                CommonConstant.VIEW_WIDTH, true, "granule", "granule", CellType.STRING, false);
        formInfoVo.add(granuleVO);
        LeafExcelTitleVO overseaFlagVO = new LeafExcelTitleVO(Constant.StrEnum.OVERSEA_FLAG.getValue() + IndustryConst.getOverseaFlag(historyInputVO.getOverseaFlag()).getDesc(),
                CommonConstant.VIEW_WIDTH, true, "overseaFlag", "overseaFlag", CellType.STRING, false);
        formInfoVo.add(overseaFlagVO);
        LeafExcelTitleVO bgFlagVO = new LeafExcelTitleVO(Constant.StrEnum.BG_FLAG.getValue() + bgName,
                CommonConstant.VIEW_WIDTH, true, "bgFlag", "bgFlag", CellType.STRING, false);
        formInfoVo.add(bgFlagVO);
        LeafExcelTitleVO columnCnVersion = new LeafExcelTitleVO(dmFocViewInfoVO.getViewFlagValue(), CommonConstant.VIEW_WIDTH, true, "version", "version", CellType.STRING, false);
        formInfoVo.add(columnCnVersion);
        LeafExcelTitleVO columnCnVersionName = new LeafExcelTitleVO(
                CommonConstant.VERSION_REFRESH + versionName, CommonConstant.VERSION_WIDTH, true, "versionName", "versionName", CellType.STRING, false);
        formInfoVo.add(columnCnVersionName);
        return formInfoVo;
    }

    private Map getCateAndItemWithWight(DmFocVersionInfoDTO versionDTO, HistoryInputVO historyInputVO,List<String> yearPeriodList)
            throws CommonApplicationException {
        // 获取报告期范围
        if (StrUtil.isBlank(versionDTO.getVersion())) {
            throw new CommonApplicationException("清单版本名称为空");
        }
        List<Map> cateItemWithWeightList = new ArrayList<>();
        List<HeaderVo> headers;
        String sheetName = FcstIndexUtil.getSheetName(historyInputVO);
        setDynamicHeader(historyInputVO,yearPeriodList);
        if (IndustryConst.DataType.CATE.getValue().equals(historyInputVO.getModelType())) {
            headers = historyInputVO.getTopCateHeader();
        } else {
            headers = historyInputVO.getTopItemHeader();
        }
        Map map = new HashMap();
        map.put("sheetName", sheetName);
        map.put("headers", headers);
        map.put("cateItemWithWeightList", cateItemWithWeightList);
        return map;
    }

    public DmFocTopCateInfoDTO setTopCateVO(HistoryInputVO historyInputVO, List<String> yearPeriodList) {
        DmFocTopCateInfoDTO cateInfoDTO = ObjectCopyUtil.copy(historyInputVO, DmFocTopCateInfoDTO.class);
        cateInfoDTO.setYearPeriodList(yearPeriodList);
        if (CollectionUtils.isNotEmpty(yearPeriodList)) {
            cateInfoDTO.setPeriodYear0(yearPeriodList.get(0));
            cateInfoDTO.setPeriodYear1(yearPeriodList.get(1));
            cateInfoDTO.setPeriodYear2(yearPeriodList.get(2));
            cateInfoDTO.setPeriodYear3(yearPeriodList.get(3));
            cateInfoDTO.setPeriodYear4(yearPeriodList.get(4));
        }
        cateInfoDTO.setDelFlag(CommonConstant.IS_NOT);
        return cateInfoDTO;
    }

    public DmFocTopItemInfoDTO setTopItemVO(HistoryInputVO historyInputVO, List<String> yearPeriodList) {
        DmFocTopItemInfoDTO itemInfoDTO = ObjectCopyUtil.copy(historyInputVO, DmFocTopItemInfoDTO.class);
        itemInfoDTO.setYearPeriodList(yearPeriodList);
        if (CollectionUtils.isNotEmpty(yearPeriodList)) {
            itemInfoDTO.setPeriodYear0(yearPeriodList.get(0));
            itemInfoDTO.setPeriodYear1(yearPeriodList.get(1));
            itemInfoDTO.setPeriodYear2(yearPeriodList.get(2));
            itemInfoDTO.setPeriodYear3(yearPeriodList.get(3));
            itemInfoDTO.setPeriodYear4(yearPeriodList.get(4));
        }
        itemInfoDTO.setDelFlag(CommonConstant.IS_NOT);
        return itemInfoDTO;
    }

    private void setTotalRow(HistoryInputVO historyInputVO,List<Map> cateItemWithWeightList, int allCount) {
        if (CollectionUtils.isNotEmpty(cateItemWithWeightList)) {
            Map levWeightMap = new HashMap();
            levWeightMap.put("lv0_prod_rd_team_cn_name", CommonConstant.TOTAL_CN);
            putExcelAllCount(historyInputVO,levWeightMap,allCount);
            cateItemWithWeightList.add(levWeightMap);
        }
    }

    public void putExcelAllCount(HistoryInputVO historyInputVO, Map levWeightMap, int allCount) {
        String granularityType = historyInputVO.getGranularityType();
        String viewFlag = historyInputVO.getViewFlag();
        String costType = historyInputVO.getCostType();
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(costType)) {
            if (IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(granularityType)
                    && (IndustryIndexEnum.VIEW_FLAG_U.VIEW1.getValue().equals(viewFlag) ||
                    IndustryIndexEnum.VIEW_FLAG_U.VIEW7.getValue().equals(viewFlag))) {
                levWeightMap.put("top_l3_ceg_cn_name", allCount + "");
            } else if (IndustryIndexEnum.GRANULARITY_TYPE.PROFITS.getValue().equals(granularityType)
                    && (IndustryIndexEnum.VIEW_FLAG_P.VIEW1.getValue().equals(viewFlag))) {
                levWeightMap.put("top_l3_ceg_cn_name", allCount + "");
            }else {
                levWeightMap.put("lv1_prod_rd_team_cn_name", allCount + "");
            }
        }
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(costType)) {
            if (IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(granularityType)
                    && (IndustryIndexEnum.VIEW_FLAG_U.VIEW1.getValue().equals(viewFlag) ||
                    IndustryIndexEnum.VIEW_FLAG_U.VIEW6.getValue().equals(viewFlag))) {
                levWeightMap.put("top_shipping_object_cn_name", allCount + "");
            } else if (IndustryIndexEnum.GRANULARITY_TYPE.PROFITS.getValue().equals(granularityType)
                    && (IndustryIndexEnum.VIEW_FLAG_P.VIEW1.getValue().equals(viewFlag))) {
                levWeightMap.put("top_shipping_object_cn_name", allCount + "");
            } else {
                levWeightMap.put("lv1_prod_rd_team_cn_name", allCount + "");
            }
        }
    }
    private void setDynamicHeader(HistoryInputVO historyInputVO,List<String> yearPeriodList) {
        // 返回最终组合数据
        setWeightTeam(historyInputVO);
        // 获取四年权重
        FcstIndexUtil.setDynamicYear(yearPeriodList, historyInputVO);
        // 设置是否top品和规格品
        setTopFlag(historyInputVO);
    }

    private void setMadeDynamicHeader(HistoryInputVO historyInputVO, List<String> yearPeriodList) {
        // 返回最终组合数据
        setMadeWeightTeam(historyInputVO);
        // 获取四年权重
        FcstIndexUtil.setDynamicYear(yearPeriodList, historyInputVO);
        // 设置是否top品和规格品
        setItemFlag(historyInputVO);
    }

    private void setItemFlag(HistoryInputVO historyInputVO) {
        if (IndustryConst.DataType.ITEM.getValue().equals(historyInputVO.getModelType())) {
            historyInputVO.getTopItemHeader().add(new HeaderVo("是否规格品", "is_top_flag", CellType.STRING, true, 12 * 320));
        } else {
            historyInputVO.getTopCateHeader().add(new HeaderVo("是否重点制造对象", "is_top_flag", CellType.STRING, true, 12 * 320));
        }
    }

    private void setWeightTeam(HistoryInputVO historyInputVO) {
        List<HeaderVo> commonHeader = new LinkedList<>();
        getCommonHeader(commonHeader,historyInputVO);
        if (IndustryConst.DataType.CATE.getValue().equals(historyInputVO.getModelType())) {
            FcstIndexUtil.setCommonWeightTeamCode(historyInputVO.getTopCateHeader(),historyInputVO);
            historyInputVO.getTopCateHeader().addAll(commonHeader);
            if (IndustryIndexEnum.VIEW_FLAG_U.VIEW5.getValue().equals(historyInputVO.getViewFlag())
                    || IndustryIndexEnum.VIEW_FLAG_U.VIEW6.getValue().equals(historyInputVO.getViewFlag())) {
                commonHeader.add(new HeaderVo("品类名称", "top_category_cn_name",CellType.STRING,true, 12 * 480));
                commonHeader.add(new HeaderVo("品类编码","top_category_code", CellType.STRING,true,12 * 320));
            }
        }else {
            FcstIndexUtil.setCommonWeightTeamCode(historyInputVO.getTopItemHeader(),historyInputVO);
            historyInputVO.getTopItemHeader().addAll(commonHeader);
            historyInputVO.getTopItemHeader().add(new HeaderVo("ITEM名称", "top_item_cn_name",CellType.STRING,true,12 * 480));
            historyInputVO.getTopItemHeader().add(new HeaderVo("ITEM编码", "top_item_code",CellType.STRING,true,12 * 320));
        }
    }

    private void setMadeWeightTeam(HistoryInputVO historyInputVO) {
        List<HeaderVo> commonHeader = new LinkedList<>();
        commonHeader.add(new HeaderVo("发货对象", "top_shipping_object_cn_name", CellType.STRING, true, 12 * 480));
        commonHeader.add(new HeaderVo("制造对象", "top_manufacture_object_cn_name", CellType.STRING, true, 12 * 480));
        if (IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(historyInputVO.getGranularityType())
                && IndustryIndexEnum.VIEW_FLAG_U_M.VIEW5.getValue().equals(historyInputVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_U_M.VIEW6.getValue().equals(historyInputVO.getViewFlag())) {
            commonHeader.add(new HeaderVo("重量级团队LV1", "lv1_prod_rd_team_cn_name", CellType.STRING, true, 15 * 320));
            commonHeader.add(new HeaderVo("重量级团队LV2", "lv2_prod_rd_team_cn_name", CellType.STRING, true, 15 * 320));
            commonHeader.add(new HeaderVo("重量级团队LV3", "lv3_prod_rd_team_cn_name", CellType.STRING, true, 15 * 320));
        }
        if (IndustryConst.DataType.ITEM.getValue().equals(historyInputVO.getModelType())) {
            FcstIndexUtil.setCommonWeightTeamCode(historyInputVO.getTopItemHeader(), historyInputVO);
            historyInputVO.getTopItemHeader().addAll(commonHeader);
            historyInputVO.getTopItemHeader().add(new HeaderVo("ITEM名称", "top_item_cn_name", CellType.STRING, true, 12 * 480));
            historyInputVO.getTopItemHeader().add(new HeaderVo("ITEM编码", "top_item_code", CellType.STRING, true, 12 * 320));
        } else {
            FcstIndexUtil.setCommonWeightTeamCode(historyInputVO.getTopCateHeader(), historyInputVO);
            historyInputVO.getTopCateHeader().addAll(commonHeader);
        }
    }

    private void getCommonHeader(List<HeaderVo> commonHeader, HistoryInputVO historyInputVO) {
        commonHeader.add(new HeaderVo("专项采购认证部", "top_l3_ceg_cn_name",CellType.STRING,true, 12 * 480));
        commonHeader.add(new HeaderVo("专项采购认证部简称", "top_l3_ceg_short_cn_name",CellType.STRING,true,12 * 480));
        universalHeader(commonHeader, historyInputVO);
        if (IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(historyInputVO.getGranularityType())) {
            if (IndustryIndexEnum.VIEW_FLAG_U.VIEW5.getValue().equals(historyInputVO.getViewFlag())
                    || IndustryIndexEnum.VIEW_FLAG_U.VIEW6.getValue().equals(historyInputVO.getViewFlag())
                    || IndustryIndexEnum.VIEW_FLAG_U.VIEW7.getValue().equals(historyInputVO.getViewFlag())) {
                commonHeader.add(new HeaderVo("重量级团队LV1", "lv1_prod_rd_team_cn_name", CellType.STRING, true, 15 * 320));
                commonHeader.add(new HeaderVo("重量级团队LV2", "lv2_prod_rd_team_cn_name", CellType.STRING, true, 15 * 320));
                commonHeader.add(new HeaderVo("重量级团队LV3", "lv3_prod_rd_team_cn_name", CellType.STRING, true, 15 * 320));
            }
        }
    }

    private void universalHeader(List<HeaderVo> commonHeader, HistoryInputVO historyInputVO) {
        if (!(IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(historyInputVO.getGranularityType())
                && IndustryIndexEnum.VIEW_FLAG_U.VIEW5.getValue().equals(historyInputVO.getViewFlag()))) {
            // 添加模块和品类
            if (IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(historyInputVO.getGranularityType())
                    && IndustryIndexEnum.VIEW_FLAG_U.VIEW6.getValue().equals(historyInputVO.getViewFlag())) {
                commonHeader.add(new HeaderVo("模块", "top_l4_ceg_cn_name",CellType.STRING,true, 12 * 480));
                commonHeader.add(new HeaderVo("模块简称", "top_l4_ceg_short_cn_name",CellType.STRING,true,12 * 480));
            }else {
                commonHeader.add(new HeaderVo("模块", "top_l4_ceg_cn_name",CellType.STRING,true, 12 * 480));
                commonHeader.add(new HeaderVo("模块简称", "top_l4_ceg_short_cn_name",CellType.STRING,true,12 * 480));
                commonHeader.add(new HeaderVo("品类名称", "top_category_cn_name",CellType.STRING,true, 12 * 480));
                commonHeader.add(new HeaderVo("品类编码","top_category_code", CellType.STRING,true,12 * 320));
            }
        }
    }

    private void setTopFlag(HistoryInputVO historyInputVO) {
        if (IndustryConst.DataType.CATE.getValue().equals(historyInputVO.getModelType())) {
            historyInputVO.getTopCateHeader().add(new HeaderVo("是否TOP品类", "is_top_flag", CellType.STRING, true, 12 * 320));
        } else {
            historyInputVO.getTopItemHeader().add(new HeaderVo("是否规格品", "is_top_flag", CellType.STRING, true, 12 * 320));
        }
    }

    public DmFoiImpExpRecordVO expSyncSelectColumnExcel(List<ExportExcelVo> exportExcelVoList)
            throws CommonApplicationException, IOException {
        excelUtil.setExportExcelVo(exportExcelVoList);
        return  exportAsyncExcel(exportExcelVoList);
    }

    public DmFoiImpExpRecordVO exportAsyncExcel(List<ExportExcelVo> exportExcelVoList)
            throws IOException, CommonApplicationException {
        SXSSFWorkbook fileWorkbook = new SXSSFWorkbook();

        ExcelUploadVO excelUploadVO = new ExcelUploadVO();
        // 生成一个新的excel放到服务器上
        getWorkbook(fileWorkbook, exportExcelVoList, excelUploadVO);
        String fileName = excelUploadVO.getFileName();
        Long userId = excelUploadVO.getUserId();
        // 插入数据，上传文件
        return StatisticsExcelService.uploadExportExcel(fileWorkbook, excelUploadVO.getCount(), fileName, userId);
    }
    private void getWorkbook(SXSSFWorkbook workbook, List<ExportExcelVo> exportExcelVoList, ExcelUploadVO excelUploadVO) throws IOException {
        for (ExportExcelVo exportExcelVo : exportExcelVoList) {
            List<AbstractExcelTitleVO> excelTitleVOS = exportExcelVo.getTitleVoList(); // 表头
            List<AbstractExcelTitleVO> selectedLeafExcelTitleVO = exportExcelVo.getSelectedLeafExcelTitleVO();
            int titleRowCount = exportExcelVo.getTitleRowCount();
            String sheetName = exportExcelVo.getSheetName();
            if (sheetName.contains("/") || sheetName.contains("\\")) {
                sheetName = sheetName.replace("/", "-");
            }
            boolean isLock = PoiEnum.isLockExcelSheet(selectedLeafExcelTitleVO);
            // 设置Excel单元格样式
            CellStyles cellStyles = PoiEnum.getCellStyles(workbook);
            // 插入表单信息
            List<AbstractExcelTitleVO> formsVOs = exportExcelVo.getFormInfoVo();
            HistoryInputVO historyInputVO = exportExcelVo.getHistoryInputVO();
            if (CollectionUtils.isEmpty(exportExcelVo.getList())) {
                List<String> yearPeriodList = exportExcelVo.getYearPeriodList();
                // 设置ITEM查询对象
                int allCount = 0;
                if (IndustryConst.DataType.CATE.getValue().equals(historyInputVO.getModelType())) {
                    // 设置品类查询对象
                    DmFocTopCateInfoDTO build = setTopCateVO(historyInputVO, yearPeriodList);
                    if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(historyInputVO.getCostType())) {
                        allCount = dmFocTopCateInfoDao.findAllCount(build);
                    } else {
                        allCount = dmFocTopCateInfoDao.findManufactureAllCount(build);
                    }
                } else {
                    // 设置ITEM查询对象
                    DmFocTopItemInfoDTO build = setTopItemVO(historyInputVO, yearPeriodList);
                    if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(historyInputVO.getCostType())) {
                        allCount= dmFocTopItemInfoDao.findItemAllCount(build);
                    } else {
                        allCount = dmFocTopItemInfoDao.findMfcItemAllCount(build);
                    }
                }
                excelUploadVO.setCount(allCount);
                int maxCount = 90 * Constant.IntegerEnum.PAGE_SIZE.getValue();
                Integer sheetCount = allCount % maxCount == 0 ? allCount / maxCount : allCount / maxCount + 1;
                for (int i = 1; i <= sheetCount; i++) {
                    // Integer值代表 sheet页的行，Row值代码一行的内容
                    Map<Integer, Row> titleRowMapItem = new HashMap<>();
                    SXSSFSheet sheetItem  = creatSheet(workbook, titleRowCount, sheetName+"_"+ i, titleRowMapItem);
                    PoiEnum.extracted(workbook, exportExcelVo, excelTitleVOS, selectedLeafExcelTitleVO, sheetItem, titleRowMapItem, cellStyles, formsVOs);
                    // 规格品数据需要分页查询,list数据需要分页查询，插入数据
                    setExcelUpLoadVO(excelUploadVO, exportExcelVo);
                    // 获取数据并写入sheet页
                    getDataAndWriterSheet(exportExcelVo, selectedLeafExcelTitleVO, titleRowCount, sheetItem,cellStyles,i,allCount);
                    sheetLockAndFlush(selectedLeafExcelTitleVO, titleRowCount, isLock, sheetItem);
                }
            }
        }
    }

    private void setExcelUpLoadVO(ExcelUploadVO excelUploadVO, ExportExcelVo exportExcelVo) {
        Boolean mergeCell = exportExcelVo.getMergeCell();
        Long userId = exportExcelVo.getUserId();
        excelUploadVO.setFileName(exportExcelVo.getFileName());
        excelUploadVO.setMergeCell(mergeCell);
        excelUploadVO.setUserId(userId);
    }

    private void sheetLockAndFlush(List<AbstractExcelTitleVO> selectedLeafExcelTitleVO, int titleRowCount, boolean isLock, SXSSFSheet sheetItem) throws IOException {
        if (isLock) {
            sheetItem.setAutoFilter(
                    new CellRangeAddress(sheetItem.getFirstRowNum() + titleRowCount - 1, sheetItem.getLastRowNum(), 0,
                            selectedLeafExcelTitleVO.size() - 1));
            PoiEnum.lockSheet(sheetItem);
        }
        // 避免占用太多内存
        sheetItem.flushRows();
    }

    private SXSSFSheet creatSheet(SXSSFWorkbook workbook, int titleRowCount, String sheetName, Map<Integer, Row> titleRowMap) {
        SXSSFSheet sheet = workbook.createSheet(sheetName);
        for (int i = 1; i < titleRowCount; i++) {
            Row row = sheet.createRow(i);
            if (!titleRowMap.containsKey(i)) {
                titleRowMap.put(i, row);
            }
        }
        return sheet;
    }

    private void getDataAndWriterSheet(ExportExcelVo exportExcelVo, List<AbstractExcelTitleVO> selectedLeafExcelTitleVO, int titleRowCount, SXSSFSheet sheet, CellStyles cellStyles, int sheetCount,int allCount) throws IOException {
        Integer pageSize = Constant.IntegerEnum.PAGE_SIZE.getValue();
        HistoryInputVO historyInputVO = exportExcelVo.getHistoryInputVO();
        List<String> yearPeriodList = exportExcelVo.getYearPeriodList();
        Integer totalPage = allCount % pageSize == 0 ? allCount / pageSize : allCount / pageSize + 1;
        Cursor<Map> cursor = null;
        if (IndustryConst.DataType.CATE.getValue().equals(historyInputVO.getModelType())) {
            DmFocTopCateInfoDTO cateBuild = setTopCateVO(historyInputVO, yearPeriodList);
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(historyInputVO.getCostType())) {
                cursor = dmFocTopCateInfoDao.findTopCateVOList(cateBuild);
            } else {
                cursor = dmFocTopCateInfoDao.findManufactureList(cateBuild);
            }
        } else {
            DmFocTopItemInfoDTO itemBuild = setTopItemVO(historyInputVO, yearPeriodList);
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(historyInputVO.getCostType())) {
                cursor = dmFocTopItemInfoDao.findTopItemVOList(itemBuild);
            } else {
                cursor = dmFocTopItemInfoDao.findMfcItemVOList(itemBuild);
            }
        }
        final List<Map> cateItemWithWeightList = new ArrayList<>();
        // 分页计数
        int countPage = 1;
        for (Map map : cursor) {
            cateItemWithWeightList.add(map);
            if (cateItemWithWeightList.size() % 10000 == 0) {
                setCateOrItemWithItemList(selectedLeafExcelTitleVO, titleRowCount, sheet, cellStyles, sheetCount, pageSize, historyInputVO, allCount, totalPage, cateItemWithWeightList, countPage);
                countPage = countPage +1;
            }
        }
        if (!cateItemWithWeightList.isEmpty()) {
            setCateOrItemWithItemList(selectedLeafExcelTitleVO, titleRowCount, sheet, cellStyles, sheetCount, pageSize, historyInputVO, allCount, totalPage, cateItemWithWeightList,countPage);
        }
    }

    private void setCateOrItemWithItemList(List<AbstractExcelTitleVO> selectedLeafExcelTitleVO, int titleRowCount, SXSSFSheet sheet, CellStyles cellStyles, int sheetCount, Integer pageSize, HistoryInputVO historyInputVO, int allCount, Integer totalPage, List<Map> cateItemWithWeightList, int countPage) throws IOException {
        List<Map> temp;
        int startIndex;
        temp = cateItemWithWeightList.stream().collect(Collectors.toList());
        startIndex = (countPage - 1) * pageSize - (90 * pageSize * (sheetCount - 1)) + titleRowCount;
        if (countPage == totalPage) {
            setTotalRow(historyInputVO, temp, allCount);
        }
        PoiEnum.addConfigData(startIndex, temp, selectedLeafExcelTitleVO, sheet, cellStyles);
        // 避免占用太多内存
        sheet.flushRows();
        cateItemWithWeightList.clear();
    }
}
