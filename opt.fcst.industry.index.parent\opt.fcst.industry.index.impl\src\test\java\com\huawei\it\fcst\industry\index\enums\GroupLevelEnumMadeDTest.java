/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.enums;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 * @since 2023/11/1
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class GroupLevelEnumMadeDTest {

    @Test
    public void getInstance() {
        GroupLevelEnumMadeD levelEnumMadeD = GroupLevelEnumMadeD.getInstance("");
        Assert.assertNull(levelEnumMadeD);
    }

    @Test
    public void getInstanceIct() {
        GroupLevelEnumMadeD levelEnumMadeD = GroupLevelEnumMadeD.getInstance("ICT");
        Assert.assertNotNull(levelEnumMadeD);
    }
}