/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.common.CommonViewVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;

import java.util.List;

/**
 * IDmFocViewInfoDao
 *
 * <AUTHOR>
 * @since 2023/3/14
 */
public interface IDmFocTotalViewInfoDao {

    List<DmFocViewInfoVO> totalReverseFindLv1ProdCode(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> totalViewInfoList(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> totalViewFlagInfoList(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> getTotalLv0ProdList(CommonViewVO commonViewVO);


}
