/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.replace;

import com.huawei.it.fcst.vo.BaseVO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * DmFocReplaceVO Class
 *
 * <AUTHOR>
 * @since 2024/7/10
 */
@Getter
@Setter
@NoArgsConstructor
public class DmFocReplVO extends BaseVO {

    private Long id;

    private String bgCode;

    private String bgCnName;

    private String gtsType;

    private String lv1Code;

    private String lv1CnName;

    private String lv2Code;

    private String lv2CnName;

    private String lv3Code;

    private String lv3CnName;

    private String lv4Code;

    private String lv4CnName;

    // 产品名称
    private String prodCnName;

    private String prodCode;

    private String spartCode;

    // 替换关系名称
    private String replaceRelationName;

    // 新老编码替换类型（一对一  、一对多 、多对多）
    private String replaceRelationType;

    private String oldSpartCode;

    private String oldSpartDesc;

    private String newSpartCode;

    private String newSpartDesc;

    // 关系（ 替换 、收编）
    private String relationType;

    private String newSpartGtmDate;

    private String replaceBeginDate;

    private Long versionId;
}
