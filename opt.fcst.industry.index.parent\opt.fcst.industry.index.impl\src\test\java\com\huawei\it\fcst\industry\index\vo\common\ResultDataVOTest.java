/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.common;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

/**
 * ResultDataVOTest Class
 *
 * <AUTHOR>
 * @since 2023/4/22
 */
public class ResultDataVOTest extends BaseVOCoverUtilsTest<ResultDataVO> {

    @Override
    protected Class<ResultDataVO> getTClass() {
        return ResultDataVO.class;
    }

    @Test
    public void testMethod() {
        ResultDataVO dmFocActualCostVO = new ResultDataVO();
        dmFocActualCostVO.setCode("200");
        dmFocActualCostVO.getCode();
        dmFocActualCostVO.setMessage("success");
        dmFocActualCostVO.getMessage();
        Assert.assertNotNull(dmFocActualCostVO);
    }
}