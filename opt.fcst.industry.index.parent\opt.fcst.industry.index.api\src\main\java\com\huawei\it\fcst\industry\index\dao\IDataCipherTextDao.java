/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import java.util.List;
import java.util.Map;

import com.huawei.it.fcst.industry.index.vo.ciphertext.VarifyTaskVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.cursor.Cursor;

import com.huawei.it.fcst.industry.index.vo.ciphertext.CipherTextDataVO;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023年03月22日
 */
public interface IDataCipherTextDao {
    /**
     * 流试获取数据
     * @param cipherTextDataVO 参数
     * @return 迭代器
     */
    Cursor<CipherTextDataVO> getDataListStream(@Param("cipherTextDataVO") CipherTextDataVO cipherTextDataVO);

    /**
     * 数据落库
     * @param list 明细数据
     * @param keyStr 加密颜值
     * @return upsert 条数
     */
    int saveData(@Param("list") List<CipherTextDataVO> list, @Param("keyStr") String keyStr, @Param("targetTableName") String targetTableName);

    /**
     * 删除数据
     * @param cipherTextDataVO 参数
     * @return 删除条数
     */
    int delByPeriodId(@Param("cipherTextDataVO") CipherTextDataVO cipherTextDataVO);

    /**
     * lts 任务执行cell 过程
     * @param parameter 参数
     * @return 结构
     */
    Map startFunctionTask(Map parameter);

    VarifyTaskVO searchVerifyTask(@Param("varifyTaskVO") VarifyTaskVO varifyTaskVO);

    /**
     * 查询同时执行的任务数量
     * @return 任务数量
     */
    int countVarifyTask();

    Long getVerifyTaskId(@Param("tablePreFix") String tablePreFix);

    void insertVerifyTask(VarifyTaskVO varifyTaskVO);

    void updateVerifyTask(VarifyTaskVO varifyTaskVO);

}
