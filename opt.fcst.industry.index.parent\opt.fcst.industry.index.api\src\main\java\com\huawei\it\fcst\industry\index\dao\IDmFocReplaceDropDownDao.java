/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.common.CommonDropDownVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;

import java.util.List;

/**
 * IDmFocDropDownDao
 *
 * <AUTHOR>
 * @since 2023/3/14
 */
public interface IDmFocReplaceDropDownDao {

    List<DmFocViewInfoVO> getReplaceLv2ProdRndTeamList();

    List<DmFocViewInfoVO> replaceViewInfoList(CommonDropDownVO commonViewVO);

    List<DmFocViewInfoVO> viewFlagInfoList(CommonDropDownVO commonViewVO);

    List<DmFocViewInfoVO> totalReverseFindLv1ProdCode(CommonDropDownVO commonViewVO);

}
