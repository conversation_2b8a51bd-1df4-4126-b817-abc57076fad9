/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.combination;

import com.huawei.it.fcst.industry.index.dao.IDmFocCustomCombDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocEnergyCustomCombDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocEnergyMadeCustomCombDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeCustomCombDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTotalCustomCombDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocVersionDao;
import com.huawei.it.fcst.industry.index.enums.GroupLevelAllEnum;
import com.huawei.it.fcst.industry.index.enums.IndustryConst;
import com.huawei.it.fcst.industry.index.enums.IndustryIndexEnum;
import com.huawei.it.fcst.industry.index.impl.annual.AnnualCommonService;
import com.huawei.it.fcst.industry.index.impl.common.CommonService;
import com.huawei.it.fcst.industry.index.vo.combination.CombinationVO;
import com.huawei.it.fcst.industry.index.vo.combination.DmCustomCombVO;
import com.huawei.it.fcst.industry.index.vo.common.CommonViewVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Named;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * CustomCommonService Class
 *
 * <AUTHOR>
 * @since 2023/9/11
 */

@Named("customCommonService")
@JalorResource(code = "customCommonService", desc = "汇总组合公共类")
public class CustomCommonService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CustomCommonService.class);

    private static Map<String, String> allCombPageFlag = new HashMap<>(4);

    static {
        allCombPageFlag.put("ANNUAL", "MONTH");
        allCombPageFlag.put("MONTH", "ANNUAL");
        allCombPageFlag.put("ALL_ANNUAL", "ALL_MONTH");
        allCombPageFlag.put("ALL_MONTH", "ALL_ANNUAL");
    }

    @Autowired
    private IDmFocCustomCombDao dmFocCustomCombDao;

    @Autowired
    private IDmFocEnergyCustomCombDao dmFocEnergyCustomCombDao;

    @Autowired
    private IDmFocEnergyMadeCustomCombDao dmFocEnergyMadeCustomCombDao;

    @Autowired
    private IDmFocTotalCustomCombDao dmFocTotalCustomCombDao;

    @Autowired
    private IDmFocMadeCustomCombDao dmFocMadeCustomCombDao;

    @Autowired
    private CommonService commonService;

    @Autowired
    private IDmFocVersionDao dmFocVersionDao;

    @Autowired
    private AnnualCommonService annualCommonService;

    public List<DmCustomCombVO> filterAnotherPageData(CombinationVO combinationVO, List<DmCustomCombVO> customVOList) {
        // 获取最新的top品version_id
        combinationVO.setVersionId(dmFocVersionDao.findAnnualVersion(combinationVO.getTablePreFix()).getVersionId());
        // 计算最近的三年
        List<String> threeYears = annualCommonService.getThreeYears(combinationVO.getCostType(),combinationVO.getIndustryOrg());
        // 获取当前年份
        if (CollectionUtils.isNotEmpty(threeYears)) {
            int countYear  = threeYears.size()-1;
            combinationVO.setPeriodYear(threeYears.get(countYear));
        }
        // 获取最新的规格品version_id
        combinationVO.setMonthVersionId(commonService.getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),combinationVO.getTablePreFix()));
        String granularityType = combinationVO.getGranularityType();
        // 根据来源页面，判断另一个页面是否有对应的code，只判断重量级团队，L1,L2，以及量纲，量纲子类，子类明细
        String granularityPageSymbol = granularityType + "_" + allCombPageFlag.get(combinationVO.getPageSymbol());
        combinationVO.setGranularityPageSymbol(granularityPageSymbol);
        // 设置传入的list对应的重量级团队，以及L1,L2，量纲的code
        setProdTeamAndDimesionCode(combinationVO, customVOList);
        // 查询另一个页面重量级团队，量纲所有层级，L1,L2的code
        List<DmFocViewInfoVO> anotherCustomCombList = getAnotherPageGroupCode(combinationVO);

        List<DmFocViewInfoVO> allPurchaseGroupCodeList = new ArrayList<>();
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(combinationVO.getCostType())) {
            customVOList.removeIf(custom -> "SHIPPING_OBJECT".equals(custom.getGroupLevel()) || "MANUFACTURE_OBJECT".equals(custom.getGroupLevel()));
        } else {
            customVOList.removeIf(custom -> "CEG".equals(custom.getGroupLevel()) || "MODL".equals(custom.getGroupLevel()) || "CATEGORY".equals(custom.getGroupLevel()));
        }
        // 查询另一个页面采购层级或制造层级的code
        getGroupCodeForPurchaseLevel(combinationVO, allPurchaseGroupCodeList, false);
        // 传进来的，不在另一个页面中的code
        List<DmCustomCombVO> diffAnotherList = customVOList.stream().filter(custom -> !anotherCustomCombList.stream().map(another -> {
            return another.getConnectCode();
        }).collect(Collectors.toList()).contains(custom.getConnectCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(diffAnotherList)) {
            // 差集不为空，表示有一些另一个页面没有的code，需要排除
            for (DmCustomCombVO diff : diffAnotherList) {
                customVOList.removeIf(custom -> diff.getConnectCode().equals(custom.getConnectCode()));
            }
        }
        List<DmCustomCombVO> purchaseGroupCodeList = allPurchaseGroupCodeList.stream().map(purchase -> {
            DmCustomCombVO dmCustomComb = new DmCustomCombVO();
            BeanUtils.copyProperties(purchase, dmCustomComb);
            return dmCustomComb;
        }).collect(Collectors.toList());
        customVOList.addAll(purchaseGroupCodeList);
        return customVOList;
    }

    private void setProdTeamAndDimesionCode(CombinationVO combinationVO, List<DmCustomCombVO> customVOList) {

        List<String> lv1CodeList = customVOList.stream().filter(custom->StringUtils.isNotEmpty(custom.getLv1ProdRndTeamCode())).map(DmCustomCombVO::getLv1ProdRndTeamCode).distinct().collect(Collectors.toList());
        List<String> lv2CodeList = customVOList.stream().filter(custom->StringUtils.isNotEmpty(custom.getLv2ProdRndTeamCode())).map(DmCustomCombVO::getLv2ProdRndTeamCode).distinct().collect(Collectors.toList());
        List<String> lv3CodeList = customVOList.stream().filter(custom->StringUtils.isNotEmpty(custom.getLv3ProdRndTeamCode())).map(DmCustomCombVO::getLv3ProdRndTeamCode).distinct().collect(Collectors.toList());
        List<String> lv4CodeList = customVOList.stream().filter(custom->StringUtils.isNotEmpty(custom.getLv4ProdRndTeamCode())).map(DmCustomCombVO::getLv4ProdRndTeamCode).distinct().collect(Collectors.toList());
        List<String> l1NameList = customVOList.stream().filter(custom->StringUtils.isNotEmpty(custom.getL1Name())).map(DmCustomCombVO::getL1Name).distinct().collect(Collectors.toList());
        List<String> l2NameList = customVOList.stream().filter(custom->StringUtils.isNotEmpty(custom.getL2Name())).map(DmCustomCombVO::getL2Name).distinct().collect(Collectors.toList());
        List<String> dimensionCodeList = customVOList.stream().filter(custom->StringUtils.isNotEmpty(custom.getDimensionCode())).map(DmCustomCombVO::getDimensionCode).distinct().collect(Collectors.toList());
        List<String> dimensionSubcategoryCodeList = customVOList.stream().filter(custom->StringUtils.isNotEmpty(custom.getDimensionSubCategoryCode())).map(DmCustomCombVO::getDimensionSubCategoryCode).distinct().collect(Collectors.toList());
        List<String> dimensionSubDetailCodeList = customVOList.stream().filter(custom->StringUtils.isNotEmpty(custom.getDimensionSubDetailCode())).map(DmCustomCombVO::getDimensionSubDetailCode).distinct().collect(Collectors.toList());
        List<String> spartCodeList = customVOList.stream().filter(custom->StringUtils.isNotEmpty(custom.getDimensionSubDetailCode())).map(DmCustomCombVO::getSpartCode).distinct().collect(Collectors.toList());

        combinationVO.setLv1CodeList(lv1CodeList);
        combinationVO.setLv2CodeList(lv2CodeList);
        combinationVO.setLv3CodeList(lv3CodeList);
        combinationVO.setLv4CodeList(lv4CodeList);
        combinationVO.setL1NameList(l1NameList);
        combinationVO.setL2NameList(l2NameList);
        combinationVO.setDimensionCodeList(dimensionCodeList);
        combinationVO.setDimensionSubcategoryCodeList(dimensionSubcategoryCodeList);
        combinationVO.setDimensionSubDetailCodeList(dimensionSubDetailCodeList);
        combinationVO.setSpartCodeList(spartCodeList);
        if ("D".equals(combinationVO.getGranularityType())) {
            Set<String> connectDimensionCodeList = new HashSet<>();
            customVOList.stream().forEach(custom -> {
                String lv2ProdRndTeamCode = custom.getLv2ProdRndTeamCode() == null ? "" : custom.getLv2ProdRndTeamCode();
                String lv3ProdRndTeamCode = custom.getLv3ProdRndTeamCode() == null ? "" : custom.getLv3ProdRndTeamCode();
                String lv4ProdRndTeamCode = custom.getLv4ProdRndTeamCode() == null ? "" : custom.getLv4ProdRndTeamCode();
                String dimensionCode = custom.getDimensionCode() == null ? "" : custom.getDimensionCode();
                String dimensionSubCategoryCode = custom.getDimensionSubCategoryCode() == null ? "" : custom.getDimensionSubCategoryCode();
                String dimensionSubDetailCode = custom.getDimensionSubDetailCode() == null ? "" : custom.getDimensionSubDetailCode();
                String spartCode = custom.getSpartCode() == null ? "" : custom.getSpartCode();
                String coaCode = custom.getCoaCode() == null ? "" : custom.getCoaCode();
                String connectDimensionCode;
                if (IndustryConst.INDUSTRY_ORG.ICT.getValue().equals(combinationVO.getIndustryOrg())) {
                    connectDimensionCode = custom.getLv1ProdRndTeamCode() + "#*#" + lv2ProdRndTeamCode + "#*#" + lv3ProdRndTeamCode + "#*#" + dimensionCode + "#*#" + dimensionSubCategoryCode + "#*#" + dimensionSubDetailCode +"#*#"+spartCode;
                } else if(IndustryConst.INDUSTRY_ORG.ENERGY.getValue().equals(combinationVO.getIndustryOrg())){
                    connectDimensionCode = custom.getLv1ProdRndTeamCode() + "#*#" + lv2ProdRndTeamCode + "#*#" + lv3ProdRndTeamCode + "#*#" + coaCode + "#*#" + dimensionCode + "#*#" + dimensionSubCategoryCode + "#*#" + dimensionSubDetailCode +"#*#"+spartCode;
                } else {
                    connectDimensionCode = custom.getLv1ProdRndTeamCode() + "#*#" + lv2ProdRndTeamCode + "#*#" + lv3ProdRndTeamCode + "#*#" + lv4ProdRndTeamCode + "#*#" + dimensionCode + "#*#" + dimensionSubCategoryCode + "#*#" + dimensionSubDetailCode +"#*#"+spartCode;
                }
                connectDimensionCodeList.add(connectDimensionCode);
            });
            connectDimensionCodeList.remove(null);
            combinationVO.setConnectDimensionCodeList(connectDimensionCodeList);
        }
    }

    public List<DmFocViewInfoVO> getAnotherPageGroupCode(CombinationVO combinationVO) {

        List<DmFocViewInfoVO> allGroupCodeList = new ArrayList<>();
        // 重量级团队code,l1,l2，量纲code汇总
        combinationVO.setReverseFlag(false);
        CombinationVO commonVO =new CombinationVO();
        BeanUtils.copyProperties(combinationVO, commonVO);
        commonVO.setPageFlag(allCombPageFlag.get(commonVO.getPageSymbol()));
        commonVO.setIsMultipleSelect(false);
        getIndustryGroupCode(commonVO, allGroupCodeList, false);
        combinationVO.setLv0Flag(commonVO.getLv0Flag());
        return allGroupCodeList;
    }

    public void getIndustryGroupCode(CommonViewVO commonViewVO, List<DmFocViewInfoVO> allGroupCodeList, boolean flag) {
        annualCommonService.getLv1andLv2DimensionSet(commonViewVO);
        Set<String> lv0DimensionSet = commonViewVO.getLv0DimensionSet();
        List<DmFocViewInfoVO> lv1GroupCodeList = new ArrayList<>();
        boolean condition = "N".equals(commonViewVO.getExpandFlag()) && StringUtils.isEmpty(commonViewVO.getFilterGroupLevel());
        boolean twoCondition = StringUtils.isNotEmpty(commonViewVO.getKeyWord()) || !flag;
        boolean allCondition = condition || twoCondition;

        if (lv0DimensionSet.size() == 0 || !lv0DimensionSet.contains("NO_PERMISSION")) {
            commonViewVO.setLv0Flag("Y");
            if (condition || GroupLevelAllEnum.LV0.getValue().equals(commonViewVO.getFilterGroupLevel()) || twoCondition) {
                commonViewVO.setGroupLevel(GroupLevelAllEnum.LV0.getValue());
                getDbListForAllCondition(commonViewVO, allGroupCodeList);
            }
        } else {
            commonViewVO.setLv0Flag("N");
        }
        getSubProdTeamCodeList(commonViewVO, allGroupCodeList, lv1GroupCodeList, allCondition);
        getSubIndustryGroupCode(lv1GroupCodeList, commonViewVO, allGroupCodeList, allCondition);
        prodTeamCodeWithLv3(commonViewVO, allGroupCodeList, allCondition);
        prodTeamCodeWithLv4(commonViewVO, allGroupCodeList, twoCondition);
        queryProfitAndDimensionData(commonViewVO, allGroupCodeList, twoCondition);
    }

    public void getSubProdTeamCodeList(CommonViewVO commonViewVO, List<DmFocViewInfoVO> allGroupCodeList, List<DmFocViewInfoVO> lv1GroupCodeList, boolean allCondition) {
        if (allCondition||GroupLevelAllEnum.LV1.getValue().equals(commonViewVO.getFilterGroupLevel())) {
            if (!IndustryIndexEnum.VIEW_FLAG_U.VIEW1.getValue().equals(commonViewVO.getViewFlag()) && !IndustryIndexEnum.GRANULARITY_TYPE.PROFITS.getValue().equals(commonViewVO.getGranularityType())) {
                commonViewVO.setGroupLevel(GroupLevelAllEnum.LV1.getValue());
                getDbListForAllCondition(commonViewVO, lv1GroupCodeList);
                commonViewVO.setTeamLevel(GroupLevelAllEnum.LV0.getValue());
                annualCommonService.handlePermissionTeamLevel(lv1GroupCodeList, commonViewVO);
                allGroupCodeList.addAll(lv1GroupCodeList);
            }
            if (!IndustryIndexEnum.VIEW_FLAG_P.VIEW1.getValue().equals(commonViewVO.getViewFlag())&& IndustryIndexEnum.GRANULARITY_TYPE.PROFITS.getValue().equals(commonViewVO.getGranularityType())) {
                commonViewVO.setGroupLevel(GroupLevelAllEnum.LV1.getValue());
                getDbListForAllCondition(commonViewVO, lv1GroupCodeList);
                commonViewVO.setTeamLevel(GroupLevelAllEnum.LV0.getValue());
                annualCommonService.hasPermissionTeamLevelPfi(lv1GroupCodeList, commonViewVO);
                allGroupCodeList.addAll(lv1GroupCodeList);
            }
            if (IndustryIndexEnum.VIEW_FLAG_D.VIEW1.getValue().equals(commonViewVO.getViewFlag()) && IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(commonViewVO.getGranularityType())) {
                commonViewVO.setGroupLevel(GroupLevelAllEnum.LV1.getValue());
                getDbListForAllCondition(commonViewVO, lv1GroupCodeList);
                commonViewVO.setTeamLevel(GroupLevelAllEnum.LV0.getValue());
                annualCommonService.handlePermissionTeamLevel(lv1GroupCodeList, commonViewVO);
                allGroupCodeList.addAll(lv1GroupCodeList);
            }
        }
    }

    public void getSubIndustryGroupCode(List<DmFocViewInfoVO> lv1GroupCodeList, CommonViewVO commonViewVO, List<DmFocViewInfoVO> allGroupCodeList, boolean allCondition) {
        boolean viewBoolean = !IndustryIndexEnum.VIEW_FLAG_U.VIEW1.getValue().equals(commonViewVO.getViewFlag()) && !IndustryIndexEnum.VIEW_FLAG_U.VIEW2.getValue().equals(commonViewVO.getViewFlag());
        List<DmFocViewInfoVO> lv2GroupCodeList = new ArrayList<>();
        if (allCondition||GroupLevelAllEnum.LV2.getValue().equals(commonViewVO.getFilterGroupLevel())) {
            if (viewBoolean && IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(commonViewVO.getGranularityType())) {
                commonViewVO.setGroupLevel(GroupLevelAllEnum.LV2.getValue());
                getDbListForAllCondition(commonViewVO, lv2GroupCodeList);
                commonViewVO.setTeamLevel(GroupLevelAllEnum.LV1.getValue());
                List<String> lv1TeamCodeList = lv1GroupCodeList.stream().filter(lv1->GroupLevelAllEnum.LV1.getValue().equals(lv1.getGroupLevel())).map(DmFocViewInfoVO::getGroupCode).distinct().collect(Collectors.toList());
                commonViewVO.setTeamCodeList(lv1TeamCodeList);
                annualCommonService.handlePermissionTeamLevel(lv2GroupCodeList, commonViewVO);
                allGroupCodeList.addAll(lv2GroupCodeList);
            }
            if (viewBoolean && IndustryIndexEnum.GRANULARITY_TYPE.PROFITS.getValue().equals(commonViewVO.getGranularityType())) {
                commonViewVO.setGroupLevel(GroupLevelAllEnum.LV2.getValue());
                getDbListForAllCondition(commonViewVO, lv2GroupCodeList);
                commonViewVO.setTeamLevel(GroupLevelAllEnum.LV1.getValue());
                List<String> lv1TeamCodeList = lv1GroupCodeList.stream().filter(lv1->GroupLevelAllEnum.LV1.getValue().equals(lv1.getGroupLevel())).map(DmFocViewInfoVO::getGroupCode).distinct().collect(Collectors.toList());
                commonViewVO.setTeamCodeList(lv1TeamCodeList);
                annualCommonService.hasPermissionTeamLevelPfi(lv2GroupCodeList, commonViewVO);
                allGroupCodeList.addAll(lv2GroupCodeList);
            }
            boolean viewCondition = !IndustryIndexEnum.VIEW_FLAG_D.VIEW1.getValue().equals(commonViewVO.getViewFlag()) && !IndustryIndexEnum.VIEW_FLAG_D.VIEW2.getValue().equals(commonViewVO.getViewFlag()) && !IndustryIndexEnum.VIEW_FLAG_D.VIEW3.getValue().equals(commonViewVO.getViewFlag());
            if (viewCondition && !IndustryIndexEnum.VIEW_FLAG_D.VIEW10.getValue().equals(commonViewVO.getViewFlag()) &&IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(commonViewVO.getGranularityType())) {
                commonViewVO.setGroupLevel(GroupLevelAllEnum.LV2.getValue());
                getDbListForAllCondition(commonViewVO, lv2GroupCodeList);
                commonViewVO.setTeamLevel(GroupLevelAllEnum.LV1.getValue());
                List<String> lv1TeamCodeList = lv1GroupCodeList.stream().filter(lv1->GroupLevelAllEnum.LV1.getValue().equals(lv1.getGroupLevel())).map(DmFocViewInfoVO::getGroupCode).distinct().collect(Collectors.toList());
                commonViewVO.setTeamCodeList(lv1TeamCodeList);
                annualCommonService.handlePermissionTeamLevel(lv2GroupCodeList, commonViewVO);
                allGroupCodeList.addAll(lv2GroupCodeList);
            }
        }
    }

    private void prodTeamCodeWithLv3(CommonViewVO commonViewVO, List<DmFocViewInfoVO> allGroupCodeList, boolean allCondition) {
        if (("N".equals(commonViewVO.getExpandFlag())&&StringUtils.isEmpty(commonViewVO.getFilterGroupLevel()))||GroupLevelAllEnum.LV3.getValue().equals(commonViewVO.getFilterGroupLevel())|| allCondition) {
            if ((IndustryIndexEnum.VIEW_FLAG_D.VIEW4.getValue().equals(commonViewVO.getViewFlag()) ||IndustryIndexEnum.VIEW_FLAG_U.VIEW8.getValue().equals(commonViewVO.getViewFlag())) && IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(commonViewVO.getGranularityType())) {
                commonViewVO.setGroupLevel(GroupLevelAllEnum.LV3.getValue());
                getDbListForAllCondition(commonViewVO, allGroupCodeList);
            }
            boolean viewFlag = IndustryIndexEnum.VIEW_FLAG_D.VIEW7.getValue().equals(commonViewVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_D.VIEW8.getValue().equals(commonViewVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_D.VIEW9.getValue().equals(commonViewVO.getViewFlag());
            boolean lv3ViewFlag = viewFlag || IndustryIndexEnum.VIEW_FLAG_D.VIEW12.getValue().equals(commonViewVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_D.VIEW13.getValue().equals(commonViewVO.getViewFlag());
            if (lv3ViewFlag && IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(commonViewVO.getGranularityType())) {
                commonViewVO.setGroupLevel(GroupLevelAllEnum.LV3.getValue());
                getDbListForAllCondition(commonViewVO, allGroupCodeList);
            }
        }
    }

    private void prodTeamCodeWithLv4(CommonViewVO commonViewVO, List<DmFocViewInfoVO> allGroupCodeList, boolean condition) {
        if (GroupLevelAllEnum.LV4.getValue().equals(commonViewVO.getGroupLevel()) || GroupLevelAllEnum.LV4.getValue().equals(commonViewVO.getFilterGroupLevel()) || condition) {
            if (IndustryIndexEnum.VIEW_FLAG_D.VIEW8.getValue().equals(commonViewVO.getViewFlag()) && IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(commonViewVO.getGranularityType())) {
                commonViewVO.setGroupLevel(GroupLevelAllEnum.LV4.getValue());
                getDbListForAllCondition(commonViewVO, allGroupCodeList);
            }
            if (IndustryIndexEnum.VIEW_FLAG_D.VIEW13.getValue().equals(commonViewVO.getViewFlag()) && IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(commonViewVO.getGranularityType()) && IndustryConst.INDUSTRY_ORG.IAS.getValue().equals(commonViewVO.getIndustryOrg())) {
                commonViewVO.setGroupLevel(GroupLevelAllEnum.LV4.getValue());
                getDbListForAllCondition(commonViewVO, allGroupCodeList);
            }
        }
    }

    public void getGroupCodeForPurchaseLevel(CommonViewVO commonViewVO, List<DmFocViewInfoVO> allGroupCodeList, boolean flag) {
        // 专家团，模块，品类
        boolean twoCondition = StringUtils.isNotEmpty(commonViewVO.getKeyWord()) || !flag;
        if ("Y".equals(commonViewVO.getExpandFlag()) || StringUtils.isNotEmpty(commonViewVO.getFilterGroupLevel()) || twoCondition) {
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
                if (GroupLevelAllEnum.SHIPPING_OBJECT.getValue().equals(commonViewVO.getGroupLevel()) || GroupLevelAllEnum.SHIPPING_OBJECT.getValue().equals(commonViewVO.getFilterGroupLevel()) || twoCondition) {
                    commonViewVO.setGroupLevel(GroupLevelAllEnum.SHIPPING_OBJECT.getValue());
                    getDbListForAllCondition(commonViewVO, allGroupCodeList);
                }
                if (GroupLevelAllEnum.MANUFACTURE_OBJECT.getValue().equals(commonViewVO.getGroupLevel()) || GroupLevelAllEnum.MANUFACTURE_OBJECT.getValue().equals(commonViewVO.getFilterGroupLevel()) || twoCondition) {
                    commonViewVO.setGroupLevel(GroupLevelAllEnum.MANUFACTURE_OBJECT.getValue());
                    getDbListForAllCondition(commonViewVO, allGroupCodeList);
                }
            }
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
                if (GroupLevelAllEnum.CEG.getValue().equals(commonViewVO.getGroupLevel()) || GroupLevelAllEnum.CEG.getValue().equals(commonViewVO.getFilterGroupLevel()) || twoCondition) {
                    commonViewVO.setGroupLevel(GroupLevelAllEnum.CEG.getValue());
                    getDbListForAllCondition(commonViewVO, allGroupCodeList);
                }
                if (GroupLevelAllEnum.MODL.getValue().equals(commonViewVO.getGroupLevel()) || GroupLevelAllEnum.MODL.getValue().equals(commonViewVO.getFilterGroupLevel()) || twoCondition) {
                    commonViewVO.setGroupLevel(GroupLevelAllEnum.MODL.getValue());
                    getDbListForAllCondition(commonViewVO, allGroupCodeList);
                }
                if (GroupLevelAllEnum.CATEGORY.getValue().equals(commonViewVO.getGroupLevel()) || GroupLevelAllEnum.CATEGORY.getValue().equals(commonViewVO.getFilterGroupLevel()) || twoCondition) {
                    commonViewVO.setGroupLevel(GroupLevelAllEnum.CATEGORY.getValue());
                    getDbListForAllCondition(commonViewVO, allGroupCodeList);
                }
            }
        }
    }

    private void queryProfitAndDimensionData(CommonViewVO commonViewVO, List<DmFocViewInfoVO> allGroupCodeList, boolean condition) {

        if (GroupLevelAllEnum.L1.getValue().equals(commonViewVO.getGroupLevel()) ||GroupLevelAllEnum.L1.getValue().equals(commonViewVO.getFilterGroupLevel())|| condition) {
            if (IndustryIndexEnum.GRANULARITY_TYPE.PROFITS.getValue().equals(commonViewVO.getGranularityType()) && IndustryIndexEnum.VIEW_FLAG_D.VIEW4.getValue().equals(commonViewVO.getViewFlag())) {
                commonViewVO.setGroupLevel(GroupLevelAllEnum.L1.getValue());
                getDbListForAllCondition(commonViewVO, allGroupCodeList);
            }
            if (IndustryIndexEnum.GRANULARITY_TYPE.PROFITS.getValue().equals(commonViewVO.getGranularityType()) && IndustryIndexEnum.VIEW_FLAG_D.VIEW5.getValue().equals(commonViewVO.getViewFlag())) {
                commonViewVO.setGroupLevel(GroupLevelAllEnum.L1.getValue());
                getDbListForAllCondition(commonViewVO, allGroupCodeList);
            }
        }
        if (GroupLevelAllEnum.L2.getValue().equals(commonViewVO.getGroupLevel()) || GroupLevelAllEnum.L2.getValue().equals(commonViewVO.getFilterGroupLevel())|| condition) {
            if (IndustryIndexEnum.GRANULARITY_TYPE.PROFITS.getValue().equals(commonViewVO.getGranularityType()) && IndustryIndexEnum.VIEW_FLAG_D.VIEW5.getValue().equals(commonViewVO.getViewFlag())) {
                commonViewVO.setGroupLevel(GroupLevelAllEnum.L2.getValue());
                getDbListForAllCondition(commonViewVO, allGroupCodeList);
            }
        }
        dimensionGroupLevelList(commonViewVO, allGroupCodeList, condition);
    }

    private void dimensionGroupLevelList(CommonViewVO commonViewVO, List<DmFocViewInfoVO> allGroupCodeList, boolean condition) {
        if (GroupLevelAllEnum.DIMENSION.getValue().equals(commonViewVO.getGroupLevel()) || GroupLevelAllEnum.DIMENSION.getValue().equals(commonViewVO.getFilterGroupLevel())|| condition) {
            if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(commonViewVO.getGranularityType())) {
                commonViewVO.setGroupLevel(GroupLevelAllEnum.DIMENSION.getValue());
                getDbListForAllCondition(commonViewVO, allGroupCodeList);
            }
        }
        if (GroupLevelAllEnum.SUBCATEGORY.getValue().equals(commonViewVO.getGroupLevel()) || GroupLevelAllEnum.SUBCATEGORY.getValue().equals(commonViewVO.getFilterGroupLevel())|| condition) {
            if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(commonViewVO.getGranularityType()) && !IndustryIndexEnum.VIEW_FLAG_D.VIEW1.getValue().equals(commonViewVO.getViewFlag()) && !IndustryIndexEnum.VIEW_FLAG_D.VIEW4.getValue().equals(commonViewVO.getViewFlag()) && !IndustryIndexEnum.VIEW_FLAG_D.VIEW7.getValue().equals(commonViewVO.getViewFlag())) {
                commonViewVO.setGroupLevel(GroupLevelAllEnum.SUBCATEGORY.getValue());
                getDbListForAllCondition(commonViewVO, allGroupCodeList);
            }
        }
        boolean spartFlag = IndustryIndexEnum.VIEW_FLAG_D.VIEW10.getValue().equals(commonViewVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_D.VIEW11.getValue().equals(commonViewVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_D.VIEW12.getValue().equals(commonViewVO.getViewFlag());
        if (GroupLevelAllEnum.SUB_DETAIL.getValue().equals(commonViewVO.getGroupLevel()) || GroupLevelAllEnum.SUB_DETAIL.getValue().equals(commonViewVO.getFilterGroupLevel())|| condition) {
            boolean subDetailFlag = IndustryIndexEnum.VIEW_FLAG_D.VIEW3.getValue().equals(commonViewVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_D.VIEW6.getValue().equals(commonViewVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_D.VIEW9.getValue().equals(commonViewVO.getViewFlag());
            boolean coaFlag = subDetailFlag || spartFlag || IndustryIndexEnum.VIEW_FLAG_D.VIEW13.getValue().equals(commonViewVO.getViewFlag());
            if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(commonViewVO.getGranularityType()) && coaFlag) {
                commonViewVO.setGroupLevel(GroupLevelAllEnum.SUB_DETAIL.getValue());
                getDbListForAllCondition(commonViewVO, allGroupCodeList);
            }
        }
        dimensionSpartCoaGroupLevelList(commonViewVO, allGroupCodeList, condition, spartFlag);
    }

    private void dimensionSpartCoaGroupLevelList(CommonViewVO commonViewVO, List<DmFocViewInfoVO> allGroupCodeList, boolean condition, boolean spartFlag) {
        if (GroupLevelAllEnum.SPART.getValue().equals(commonViewVO.getGroupLevel()) || GroupLevelAllEnum.SPART.getValue().equals(commonViewVO.getFilterGroupLevel())|| condition) {
            if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(commonViewVO.getGranularityType()) && (spartFlag ||IndustryIndexEnum.VIEW_FLAG_D.VIEW13.getValue().equals(commonViewVO.getViewFlag()))) {
                commonViewVO.setGroupLevel(GroupLevelAllEnum.SPART.getValue());
                getDbListForAllCondition(commonViewVO, allGroupCodeList);
            }
        }
        if (GroupLevelAllEnum.COA.getValue().equals(commonViewVO.getGroupLevel()) || GroupLevelAllEnum.COA.getValue().equals(commonViewVO.getFilterGroupLevel())|| condition) {
            if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(commonViewVO.getGranularityType()) && IndustryIndexEnum.VIEW_FLAG_D.VIEW13.getValue().equals(commonViewVO.getViewFlag()) && IndustryConst.INDUSTRY_ORG.ENERGY.getValue().equals(commonViewVO.getIndustryOrg())) {
                commonViewVO.setGroupLevel(GroupLevelAllEnum.COA.getValue());
                getDbListForAllCondition(commonViewVO, allGroupCodeList);
            }
        }
    }

    public void getDbListForAllCondition(CommonViewVO commonViewVO, List<DmFocViewInfoVO> allGroupCodeList) {
        String granularityPageSymbol = commonViewVO.getGranularityPageSymbol();
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
            getManufactureDbListForAllCondition(granularityPageSymbol, commonViewVO, allGroupCodeList);
        }
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
            getPurchaseDbListForAllCondition(granularityPageSymbol, commonViewVO, allGroupCodeList);
        }
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(commonViewVO.getCostType())) {
            getTotalDbListForAllCondition(granularityPageSymbol, commonViewVO, allGroupCodeList);
        }
    }

    private void getManufactureDbListForAllCondition(String granularityPageSymbol, CommonViewVO commonViewVO, List<DmFocViewInfoVO> allGroupCodeList) {
        List<DmFocViewInfoVO> dmGroupCodeList;
        switch (granularityPageSymbol) {
            case "U_MONTH":
                dmGroupCodeList = dmFocMadeCustomCombDao.prodTeamCodeManufactureForGeneralMonth(commonViewVO);
                allGroupCodeList.addAll(dmGroupCodeList);
                break;
            case "U_ANNUAL":
                dmGroupCodeList = dmFocMadeCustomCombDao.prodTeamCodeManufactureForGeneralAnnual(commonViewVO);
                allGroupCodeList.addAll(dmGroupCodeList);
                break;
            case "P_MONTH":
                dmGroupCodeList = dmFocMadeCustomCombDao.prodTeamCodeManufactureForProfitMonth(commonViewVO);
                allGroupCodeList.addAll(dmGroupCodeList);
                break;
            case "P_ANNUAL":
                dmGroupCodeList = dmFocMadeCustomCombDao.prodTeamCodeManufactureForProfitAnnual(commonViewVO);
                allGroupCodeList.addAll(dmGroupCodeList);
                break;
            case "D_ANNUAL":
                if ("ICT".equals(commonViewVO.getIndustryOrg())) {
                    dmGroupCodeList = dmFocMadeCustomCombDao.prodTeamCodeManufactureForDimensionAnnual(commonViewVO);
                    allGroupCodeList.addAll(dmGroupCodeList);
                } else {
                    // 数字能源或IAS
                    dmGroupCodeList = dmFocEnergyMadeCustomCombDao.prodTeamCodeEnergyManufactureForDimensionAnnual(commonViewVO);
                    allGroupCodeList.addAll(dmGroupCodeList);
                }
                break;
            case "D_MONTH":
                if ("ICT".equals(commonViewVO.getIndustryOrg())) {
                    dmGroupCodeList = dmFocMadeCustomCombDao.prodTeamCodeManufactureForDimensionMonth(commonViewVO);
                    allGroupCodeList.addAll(dmGroupCodeList);
                } else {
                    // 数字能源或IAS
                    dmGroupCodeList = dmFocEnergyMadeCustomCombDao.prodTeamCodeEnergyManufactureForDimensionMonth(commonViewVO);
                    allGroupCodeList.addAll(dmGroupCodeList);
                }
                break;
            default:
                break;
        }
    }

    private void getPurchaseDbListForAllCondition(String granularityPageSymbol, CommonViewVO commonViewVO, List<DmFocViewInfoVO> allGroupCodeList) {
        List<DmFocViewInfoVO> dmPurchaseGroupCodeList;
        switch (granularityPageSymbol) {
            case "U_MONTH":
                dmPurchaseGroupCodeList = dmFocCustomCombDao.prodTeamCodeForGeneralMonth(commonViewVO);
                allGroupCodeList.addAll(dmPurchaseGroupCodeList);
                break;
            case "U_ANNUAL":
                dmPurchaseGroupCodeList = dmFocCustomCombDao.prodTeamCodeForGeneralAnnual(commonViewVO);
                allGroupCodeList.addAll(dmPurchaseGroupCodeList);
                break;
            case "P_MONTH":
                dmPurchaseGroupCodeList = dmFocCustomCombDao.prodTeamCodeForProfitMonth(commonViewVO);
                allGroupCodeList.addAll(dmPurchaseGroupCodeList);
                break;
            case "P_ANNUAL":
                dmPurchaseGroupCodeList = dmFocCustomCombDao.prodTeamCodeForProfitAnnual(commonViewVO);
                allGroupCodeList.addAll(dmPurchaseGroupCodeList);
                break;
            case "D_ANNUAL":
                if ("ICT".equals(commonViewVO.getIndustryOrg())) {
                    dmPurchaseGroupCodeList = dmFocCustomCombDao.prodTeamCodeForDimensionAnnual(commonViewVO);
                    allGroupCodeList.addAll(dmPurchaseGroupCodeList);
                } else {
                    // 数字能源或IAS
                    dmPurchaseGroupCodeList = dmFocEnergyCustomCombDao.prodEnergyTeamCodeForDimensionAnnual(commonViewVO);
                    allGroupCodeList.addAll(dmPurchaseGroupCodeList);
                }
                break;
            case "D_MONTH":
                if ("ICT".equals(commonViewVO.getIndustryOrg())) {
                    dmPurchaseGroupCodeList = dmFocCustomCombDao.prodTeamCodeForDimensionMonth(commonViewVO);
                    allGroupCodeList.addAll(dmPurchaseGroupCodeList);
                } else {
                    // 数字能源或IAS
                    dmPurchaseGroupCodeList = dmFocEnergyCustomCombDao.prodEnergyTeamCodeForDimensionMonth(commonViewVO);
                    allGroupCodeList.addAll(dmPurchaseGroupCodeList);
                }
                break;
            default:
                break;
        }
    }

    private void getTotalDbListForAllCondition(String granularityPageSymbol, CommonViewVO commonViewVO, List<DmFocViewInfoVO> allGroupCodeList) {
        List<DmFocViewInfoVO> dmTotalGroupCodeList;
        switch (granularityPageSymbol) {
            case "U_MONTH":
            case "U_ANNUAL":
                dmTotalGroupCodeList = dmFocTotalCustomCombDao.prodTeamCodeForGeneral(commonViewVO);
                allGroupCodeList.addAll(dmTotalGroupCodeList);
                break;
            case "P_MONTH":
            case "P_ANNUAL":
                dmTotalGroupCodeList = dmFocTotalCustomCombDao.prodTeamCodeForProfit(commonViewVO);
                allGroupCodeList.addAll(dmTotalGroupCodeList);
                break;
            case "D_ANNUAL":
            case "D_MONTH":
                if ("ICT".equals(commonViewVO.getIndustryOrg())) {
                    dmTotalGroupCodeList = dmFocTotalCustomCombDao.prodTeamCodeForDimension(commonViewVO);
                    allGroupCodeList.addAll(dmTotalGroupCodeList);
                } else {
                    // 数字能源或者IAS
                    dmTotalGroupCodeList = dmFocTotalCustomCombDao.prodEnergyTeamCodeForDimension(commonViewVO);
                    allGroupCodeList.addAll(dmTotalGroupCodeList);
                }
                break;
            default:
                break;
        }
    }
}
