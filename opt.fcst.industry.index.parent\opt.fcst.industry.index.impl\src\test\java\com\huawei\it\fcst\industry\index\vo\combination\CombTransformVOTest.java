/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.combination;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;
import com.huawei.it.jalor5.core.request.impl.RequestContext;

import org.junit.Assert;
import org.junit.Test;

/**
 * CombTransformVOTest Class
 *
 * <AUTHOR>
 * @since 2023/9/19
 */
public class CombTransformVOTest extends BaseVOCoverUtilsTest<CombTransformVO> {

    @Override
    protected Class<CombTransformVO> getTClass() {
        return CombTransformVO.class;
    }

    @Test
    public void testMethod() {
        CombTransformVO dmFocActualCostVO = new CombTransformVO();
        dmFocActualCostVO.setRoleId(10);
        dmFocActualCostVO.getRoleId();
        dmFocActualCostVO.setGranularityType("U");
        dmFocActualCostVO.getGranularityType();
        dmFocActualCostVO.setPageFlag("o");
        dmFocActualCostVO.getPageFlag();
        dmFocActualCostVO.setCurrent(new RequestContext());
        dmFocActualCostVO.getCurrent();
        Assert.assertNotNull(dmFocActualCostVO);
    }
}