<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.price.dao.IDmPriceVirtualizedTaskDao">
    <select id="callAnnualFuncTask" resultType="java.lang.String">
        select fin_dm_opt_foi.f_dm_fcst_price_point_base_cus_annl (#{query.customId,jdbcType=NUMERIC})
    </select>

    <select id="callMonthWeightFuncTask" resultType="java.lang.String">
        select fin_dm_opt_foi.f_dm_fcst_price_base_cus_mon_weight_t (#{query.customId,jdbcType=NUMERIC},NULL)
    </select>

    <select id="callMonthCostIdxFuncTask" resultType="java.lang.String">
        select fin_dm_opt_foi.f_dm_fcst_price_base_cus_mon_cost_idx_t (#{query.customId,jdbcType=NUMERIC},NULL)
    </select>

    <select id="callMonthRateFuncTask" resultType="java.lang.String">
        select fin_dm_opt_foi.f_dm_fcst_price_base_cus_mon_rate_t (#{query.customId,jdbcType=NUMERIC},NULL)
    </select>
</mapper>
