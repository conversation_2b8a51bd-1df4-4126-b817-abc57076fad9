/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.dao;

import com.huawei.it.fcst.industry.price.vo.drop.DmFcstBasePriceCusUserVO;

import java.util.List;

/**
 * IDmFocViewInfoDao
 *
 * <AUTHOR>
 * @since 2023/3/14
 */
public interface IDmFcstPriceBaseCusUserDao {

    List<DmFcstBasePriceCusUserVO> getCusIdByUser(DmFcstBasePriceCusUserVO dmFcstBaseCusUserVO);

    int createDmFcstCusUserInfoDTO(DmFcstBasePriceCusUserVO dmFcstBaseCusUserVO);



}
