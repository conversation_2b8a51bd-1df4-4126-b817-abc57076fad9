/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.dao;

import com.huawei.it.fcst.industry.pbi.vo.month.IctMonthAnalysisVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * IctMonthCostIdxDao Interface
 *
 * <AUTHOR>
 * @since 2024-07-05
 */
public interface IctMonthWeightDao {
    /**
     * 产业成本指数（ICT）权重图查询
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<IctMonthAnalysisVO> findWeightVOList(@Param("monthAnalysisVO") IctMonthAnalysisVO monthAnalysisVO);

    /**
     * 产业成本指数（ICT）组合权重图查询
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<IctMonthAnalysisVO> findCombWeightList(@Param("monthAnalysisVO") IctMonthAnalysisVO monthAnalysisVO);

    /**
     * 产业成本指数（ICT）虚化权重图查询
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<IctMonthAnalysisVO> findBlurWeightVOList(@Param("monthAnalysisVO") IctMonthAnalysisVO monthAnalysisVO);

    /**
     * 查询最小层级的权重图
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<IctMonthAnalysisVO> findBlurMinLevWeightVOList(@Param("monthAnalysisVO") IctMonthAnalysisVO monthAnalysisVO);

    /**
     * 分页查询多子项指数图最小层级的下拉框
     *
     * @param monthAnalysisVO 查询参数VO
     * @param pageVO page vo
     * @return List
     */
    PagedResult<IctMonthAnalysisVO> findBlurMinLevDropdownListByPage(@Param("monthAnalysisVO") IctMonthAnalysisVO monthAnalysisVO, @Param("pageVO") PageVO pageVO);

    /**
     * 产业成本指数（ICT）虚化权重图分页查询
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    PagedResult<IctMonthAnalysisVO> findBlurWeightVOListByPage(@Param("monthAnalysisVO") IctMonthAnalysisVO monthAnalysisVO, @Param("pageVO") PageVO pageVO);
}