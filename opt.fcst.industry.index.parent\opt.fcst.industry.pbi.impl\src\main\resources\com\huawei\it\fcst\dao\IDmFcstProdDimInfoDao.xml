<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.pbi.dao.IDmFcstProdDimInfoDao">
    <resultMap type="com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstDimInfoVO" id="resultMap">
        <result property="id" column="id"/>
        <result property="bgCode" column="bg_code"/>
        <result property="bgCnName" column="bg_cn_name"/>
        <result property="dimensionCode" column="dimension_code"/>
        <result property="dimensionCnName" column="dimension_cn_name"/>
        <result property="dimensionSubCategoryCode" column="dimension_subCategory_code"/>
        <result property="dimensionSubCategoryCnName" column="dimension_subCategory_cn_name"/>
        <result property="dimensionSubDetailCode" column="dimension_sub_detail_code"/>
        <result property="dimensionSubDetailCnName" column="dimension_sub_detail_cn_name"/>
        <result property="spartCode" column="spart_code"/>
        <result property="spartCnName" column="spart_cn_name"/>
        <result property="regionCode" column="region_code"/>
        <result property="regionCnName" column="region_cn_name"/>
        <result property="repofficeCode" column="repoffice_code"/>
        <result property="repofficeCnName" column="repoffice_cn_name"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="groupCode" column="groupCode"/>
        <result property="groupLevel" column="groupLevel"/>
        <result property="groupCnName" column="groupCnName"/>
        <result property="groupLevel" column="group_level"/>
        <result property="num" column="num"/>
        <result property="lv0Code" column="lv0_code"/>
        <result property="lv1Code" column="lv1_code"/>
        <result property="lv1CnName" column="lv1_cn_name"/>
        <result property="lv2Code" column="lv2_code"/>
        <result property="prodRndTeamCode" column="prod_rnd_team_code"/>
    </resultMap>

    <select id="findCostGapSpartCodeList" resultMap="resultMap">
        SELECT  distinct group_code as groupCode,group_cn_name as groupCnName,'SPART' as groupLevel
        FROM fin_dm_opt_foi.dm_fcst_ict_prod_cost_gap_detail_t a
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='versionId != null'>
                and version_id = #{versionId,jdbcType=NUMERIC}
            </if>
            <if test='softwareMark!=null and softwareMark!=""'>
                and software_mark = #{softwareMark,jdbcType=VARCHAR}
            </if>
            <if test='costType != null and costType != ""'>
                and cost_type = #{costType,jdbcType=VARCHAR}
            </if>
            <if test='mainFlag != null and mainFlag != ""'>
                and main_flag = #{mainFlag,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes != null and codeAttributes != ""'>
                and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='bgCode != null and bgCode != ""'>
                and bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='prodRndTeamCode != null and prodRndTeamCode != ""'>
                and prod_rnd_team_code = #{prodRndTeamCode,jdbcType=VARCHAR}
            </if>
            and exists (select 1 from fin_dm_opt_foi.dm_fcst_ict_psp_prod_top_spart_info_t b where a.prod_rnd_team_code
            = b.lv4_prod_list_code
            and a.group_code = b.top_spart_code
            and b.version_id = #{versionId,jdbcType=NUMERIC}
            and b.is_top_flag ='Y'
            and b.double_flag = 'Y'
            <if test='softwareMark!=null and softwareMark!=""'>
                and b.software_mark = #{softwareMark,jdbcType=VARCHAR}
            </if>
            <if test='mainFlag != null and mainFlag != ""'>
                and b.main_flag = #{mainFlag,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes != null and codeAttributes != ""'>
                and b.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='bgCode != null and bgCode != ""'>
                and b.bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='repofficeCode != null and repofficeCode != ""'>
                and b.repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='regionCode != null and regionCode != ""'>
                and b.region_code = #{regionCode,jdbcType=VARCHAR}
            </if>
            <if test='viewFlag != null and viewFlag != ""'>
                and b.view_flag = #{viewFlag,jdbcType=VARCHAR}
            </if>
            <if test='lv0ProdRndTeamCodeList != null and lv0ProdRndTeamCodeList.size() > 0'>
                <foreach collection='lv0ProdRndTeamCodeList' item="code" open="AND b.lv0_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv1ProdRndTeamCodeList != null and lv1ProdRndTeamCodeList.size() > 0'>
                <foreach collection='lv1ProdRndTeamCodeList' item="code" open="AND b.lv1_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv2ProdRndTeamCodeList != null and lv2ProdRndTeamCodeList.size() > 0'>
                <foreach collection='lv2ProdRndTeamCodeList' item="code" open="AND b.lv2_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv3ProdRndTeamCodeList != null and lv3ProdRndTeamCodeList.size() > 0'>
                <foreach collection='lv3ProdRndTeamCodeList' item="code" open="AND b.lv3_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv4ProdRndTeamCodeList != null and lv4ProdRndTeamCodeList.size() > 0'>
                <foreach collection='lv4ProdRndTeamCodeList' item="code" open="AND b.lv4_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            )
            <if test='prodRndTeamCodeList != null and prodRndTeamCodeList.size() > 0'>
                <foreach collection='prodRndTeamCodeList' item="code" open="AND prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='repofficeCode != null and repofficeCode != ""'>
                and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='regionCode != null and regionCode != ""'>
                and region_code = #{regionCode,jdbcType=VARCHAR}
            </if>
            <if test='viewFlag != null and viewFlag != ""'>
                and view_flag = #{viewFlag,jdbcType=VARCHAR}
            </if>
            <if test='ratioPspStd != null'>
                and ratio_psp_std &gt;= #{ratioPspStd,jdbcType=NUMERIC}
            </if>
            <if test='keyword != null and keyword != ""'>
                AND group_code LIKE '%'||#{keyword} ||'%'
            </if>
            and group_level = 'SPART'
            and group_code !='SNULL'
        </trim>
    </select>

    <select id="baseProdDimInfoList"  resultMap="resultMap">
        select
        <choose>
            <when test='searchVO.nextGroupLevel == "SPART"'>
                <if test='searchVO.pageType == "ANNUAL"'>
                    DISTINCT spart_code AS groupCode, spart_cn_name AS groupCnName, 'SPART' AS groupLevel
                </if>
                <if test='searchVO.pageType != "ANNUAL"'>
                    DISTINCT top_spart_code AS groupCode, top_spart_cn_name AS groupCnName, 'SPART' AS groupLevel
                </if>
            </when>
            <when test='searchVO.nextGroupLevel == "SUB_DETAIL"'>
                DISTINCT dimension_sub_detail_code AS groupCode, dimension_sub_detail_cn_name AS groupCnName, 'SUB_DETAIL' AS groupLevel
            </when>
            <when test='searchVO.nextGroupLevel == "SUBCATEGORY"'>
                DISTINCT dimension_subcategory_code AS groupCode, dimension_subcategory_cn_name AS groupCnName, 'SUBCATEGORY' AS groupLevel
            </when>
            <when test='searchVO.nextGroupLevel == "DIMENSION"'>
                DISTINCT dimension_code AS groupCode, dimension_cn_name AS groupCnName, 'DIMENSION' AS groupLevel
            </when>
            <when test='searchVO.nextGroupLevel == "LV4"'>
                DISTINCT lv4_prod_list_code AS groupCode, lv4_prod_list_cn_name AS groupCnName, 'LV4' AS groupLevel
            </when>
            <when test='searchVO.nextGroupLevel == "LV3"'>
                DISTINCT lv3_prod_list_code AS groupCode, lv3_prod_list_cn_name AS groupCnName, 'LV3' AS groupLevel
            </when>
            <when test='searchVO.nextGroupLevel == "LV2"'>
                DISTINCT lv2_prod_list_code AS groupCode, lv2_prod_list_cn_name AS groupCnName,'LV2' AS groupLevel
            </when>
            <when test='searchVO.nextGroupLevel == "LV1"'>
                DISTINCT lv1_prod_list_code AS groupCode, lv1_prod_list_cn_name AS groupCnName,'LV1' AS groupLevel
            </when>
            <when test='searchVO.nextGroupLevel == "LV0"'>
                DISTINCT lv0_prod_list_code AS groupCode, lv0_prod_list_cn_name AS groupCnName,'LV0' AS groupLevel
            </when>
            <when test='searchVO.nextGroupLevel == "CODE"'>
                DISTINCT code_attributes AS groupCode, code_attributes AS groupCnName,'CODE' AS groupLevel
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <include refid="baseProdDimSearchWhere"></include>
        <if test='searchVO.nextGroupLevel == "SPART" and searchVO.pageType == "ANNUAL"'>
            order by spart_code asc
        </if>
        <if test='searchVO.nextGroupLevel == "SPART" and searchVO.pageType != "ANNUAL"'>
            order by top_spart_code asc
        </if>
    </select>
    <select id="baseProdSpartDimInfoList"  resultMap="resultMap">
        select
        <if test='searchVO.pageType == "ANNUAL"'>
            distinct spart_code AS groupCode, spart_cn_name AS groupCnName, 'SPART' AS groupLevel
        </if>
        <if test='searchVO.pageType != "ANNUAL"'>
            distinct top_spart_code AS groupCode, top_spart_cn_name AS groupCnName, 'SPART' AS groupLevel
        </if>
        <include refid="baseProdDimSearchWhere"></include>
        LIMIT #{pageVO.pageSize} OFFSET #{pageVO.mysqlStartIndex}
    </select>
    <select id="baseProdSpartDimInfoListCount"  resultType="int">
        select count(1) from (select
        <if test='searchVO.pageType == "ANNUAL"'>
            distinct spart_code AS groupCode, spart_cn_name AS groupCnName, 'SPART' AS groupLevel
        </if>
        <if test='searchVO.pageType != "ANNUAL"'>
            distinct top_spart_code AS groupCode, top_spart_cn_name AS groupCnName, 'SPART' AS groupLevel
        </if>
        <include refid="baseProdDimSearchWhere"></include>)
    </select>

    <sql id ="baseProdDimSearchWhere">
        <if test='searchVO.costType == "PSP"'>
            <choose>
                <when test='searchVO.pageType == "ANNUAL"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_dim_info_t
                </when>
                <when test='(searchVO.pageType == "MONTH" or searchVO.pageType == "CONFIG") and searchVO.viewFlag =="PROD_SPART"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_top_spart_info_t
                </when>
                <when test='searchVO.pageType == "MONTH" and searchVO.viewFlag =="DIMENSION"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_dim_info_t
                </when>
                <when test='searchVO.pageType == "REPLACE_DIM"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_top_spart_info_t
                </when>
            </choose>
        </if>
        <if test='searchVO.costType == "STD"'>
            <choose>
                <when test='searchVO.pageType == "ANNUAL"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_dim_info_t
                </when>
                <when test='(searchVO.pageType == "MONTH" or searchVO.pageType == "CONFIG") and searchVO.viewFlag =="PROD_SPART"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_top_spart_info_t
                </when>
                <when test='searchVO.pageType == "MONTH" and searchVO.viewFlag =="DIMENSION"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_dim_info_t
                </when>
                <when test='searchVO.pageType == "REPLACE_DIM"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_top_spart_info_t
                </when>
            </choose>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='searchVO.versionId!=null'>
                and version_id = #{searchVO.versionId,jdbcType=NUMERIC}
            </if>
            <if test='searchVO.pageType == "REPLACE_DIM" or (searchVO.pageType == "MONTH" and searchVO.viewFlag == "PROD_SPART")'>
                and is_top_flag ='Y'
                and double_flag = 'Y'
            </if>
            <if test='searchVO.pageType == "CONFIG"'>
                and double_flag = 'Y'
            </if>
            <choose>
                <when test='searchVO.pageType == "ANNUAL"'>
                    <if test='searchVO.isHasSoftWare and (searchVO.costType == "PSP" and searchVO.softwareMark!=null and searchVO.softwareMark!="")'>
                        and software_mark = #{searchVO.softwareMark}
                    </if>
                </when>
                <otherwise>
                    <if test='searchVO.costType == "PSP" and searchVO.softwareMark!=null and searchVO.softwareMark!=""'>
                        and software_mark = #{searchVO.softwareMark}
                    </if>
                </otherwise>
            </choose>
            <if test='searchVO.regionCode!=null and searchVO.regionCode!=""'>
                and region_code = #{searchVO.regionCode,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.repofficeCode!=null and searchVO.repofficeCode!=""'>
                and repoffice_code = #{searchVO.repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.bgCode!=null and searchVO.bgCode!=""'>
                and bg_code = #{searchVO.bgCode,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.overseaFlag!=null and searchVO.overseaFlag!=""'>
                and oversea_flag = #{searchVO.overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.viewFlag!=null and searchVO.viewFlag!=""'>
                and view_flag = #{searchVO.viewFlag,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.mainFlag!=null and searchVO.mainFlag!=""'>
                and main_flag = #{searchVO.mainFlag,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.codeAttributes!=null and searchVO.codeAttributes!=""'>
                and code_attributes = #{searchVO.codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.pageType == "ANNUAL" and searchVO.nextGroupLevel!=null and searchVO.nextGroupLevel!="" and searchVO.nextGroupLevel !="CODE"'>
                and group_level = #{searchVO.nextGroupLevel,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.pageType == "MONTH" and searchVO.viewFlag == "DIMENSION"  and searchVO.nextGroupLevel!=null and searchVO.nextGroupLevel!="" and searchVO.nextGroupLevel !="CODE"'>
                and group_level = #{searchVO.nextGroupLevel,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.lv0ProdRndTeamCodeList != null and searchVO.lv0ProdRndTeamCodeList.size() > 0'>
                <foreach collection='searchVO.lv0ProdRndTeamCodeList' item="code" open="AND lv0_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='searchVO.lv1ProdRndTeamCodeList != null and searchVO.lv1ProdRndTeamCodeList.size() > 0'>
                <foreach collection='searchVO.lv1ProdRndTeamCodeList' item="code" open="AND lv1_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='searchVO.lv2ProdRndTeamCodeList != null and searchVO.lv2ProdRndTeamCodeList.size() > 0'>
                <foreach collection='searchVO.lv2ProdRndTeamCodeList' item="code" open="AND lv2_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='searchVO.lv3ProdRndTeamCodeList != null and searchVO.lv3ProdRndTeamCodeList.size() > 0'>
                <foreach collection='searchVO.lv3ProdRndTeamCodeList' item="code" open="AND lv3_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='searchVO.lv4ProdRndTeamCodeList != null and searchVO.lv4ProdRndTeamCodeList.size() > 0'>
                <foreach collection='searchVO.lv4ProdRndTeamCodeList' item="code" open="AND lv4_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='searchVO.lv0ProdRndTeamCode!=null and searchVO.lv0ProdRndTeamCode!=""'>
                and lv0_prod_list_code = #{searchVO.lv0ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.lv1ProdRndTeamCode!=null and searchVO.lv1ProdRndTeamCode!=""'>
                and lv1_prod_list_code = #{searchVO.lv1ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.lv2ProdRndTeamCode!=null and searchVO.lv2ProdRndTeamCode!=""'>
                and lv2_prod_list_code = #{searchVO.lv2ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.lv3ProdRndTeamCode!=null and searchVO.lv3ProdRndTeamCode!=""'>
                and lv3_prod_list_code = #{searchVO.lv3ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.lv4ProdRndTeamCode!=null and searchVO.lv4ProdRndTeamCode!=""'>
                and lv4_prod_list_code = #{searchVO.lv4ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.dimensionCode!=null and searchVO.dimensionCode!=""'>
                and dimension_code = #{searchVO.dimensionCode,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.dimensionSubCategoryCode!=null and searchVO.dimensionSubCategoryCode!=""'>
                and dimension_subCategory_code = #{searchVO.dimensionSubCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.dimensionSubDetailCode!=null and searchVO.dimensionSubDetailCode!=""'>
                and dimension_sub_detail_code = #{searchVO.dimensionSubDetailCode,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.spartCodeList!=null and searchVO.spartCodeList.size() > 0 and searchVO.pageType == "ANNUAL"'>
                <foreach collection='searchVO.spartCodeList' item="code" open="and spart_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='searchVO.spartCodeList!=null and searchVO.spartCodeList.size() > 0 and searchVO.pageType != "ANNUAL"'>
                <foreach collection='searchVO.spartCodeList' item="code" open="and top_spart_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='searchVO.spartCode!=null and searchVO.spartCode!="" and searchVO.pageType != "ANNUAL"'>
                and top_spart_code = #{searchVO.spartCode,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.nextGroupLevel == "SPART" and searchVO.pageType == "ANNUAL"'>
                and spart_code !='SNULL'
            </if>
            <if test='searchVO.nextGroupLevel == "SPART" and searchVO.pageType != "ANNUAL"'>
                and top_spart_code !='SNULL'
            </if>
            <if test='searchVO.nextGroupLevel == "SUB_DETAIL"'>
                and dimension_sub_detail_code !='SNULL'
            </if>
            <if test='searchVO.nextGroupLevel == "LV1" and searchVO.lv2DimensionSet != null and searchVO.lv2DimensionSet.size() > 0'>
                <foreach collection='searchVO.lv2DimensionSet' item="code" open="AND lv1_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='searchVO.nextGroupLevel == "LV2" and searchVO.lv3DimensionSet != null and searchVO.lv3DimensionSet.size() > 0'>
                <foreach collection='searchVO.lv3DimensionSet' item="code" open="AND lv2_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='searchVO.pageType == "ANNUAL" and searchVO.keyword != null and searchVO.keyword != ""'>
                AND spart_code LIKE '%'||#{searchVO.keyword} ||'%'
            </if>
            <if test='searchVO.pageType != "ANNUAL" and searchVO.keyword != null and searchVO.keyword != ""'>
                AND top_spart_code LIKE '%'||#{searchVO.keyword} ||'%'
            </if>
        </trim>
    </sql>

    <sql id="topSpartInnerJoin">
        ON t2.top_spart_code = t1.spart_code
        <if test='searchVO.monVersionId != null'>
            AND t2.version_id = #{searchVO.monVersionId,jdbcType=NUMERIC}
        </if>
        AND t2.bg_code = t1.bg_code
        AND t2.code_attributes = t1.code_attributes
        AND t2.lv1_prod_list_code = t1.lv1_prod_list_code
        AND t2.lv2_prod_list_code = t1.lv2_prod_list_code
        AND t2.lv3_prod_list_code = t1.lv3_prod_list_code
        AND t2.lv4_prod_list_code = t1.lv4_prod_list_code
        AND t2.main_flag = 'Y'
        AND t2.is_top_flag = 'Y'
        AND t2.double_flag = 'Y'
        and t2.region_code = #{searchVO.regionCode}
        and t2.repoffice_code = #{searchVO.repofficeCode}
        and t2.oversea_flag = #{searchVO.overseaFlag}
        AND t2.del_flag = 'N'
    </sql>

    <select id="mainFlagDimInfoList"  resultMap="resultMap">
        select
        <choose>
            <when test='searchVO.nextGroupLevel == "SPART"'>
                DISTINCT t1.spart_code AS groupCode, t1.spart_cn_name AS groupCnName, 'SPART' AS groupLevel
            </when>
            <when test='searchVO.nextGroupLevel == "LV4"'>
                DISTINCT t1.lv4_prod_list_code AS groupCode, t1.lv4_prod_list_cn_name AS groupCnName, 'LV4' AS groupLevel
            </when>
            <when test='searchVO.nextGroupLevel == "LV3"'>
                DISTINCT t1.lv3_prod_list_code AS groupCode, t1.lv3_prod_list_cn_name AS groupCnName, 'LV3' AS groupLevel
            </when>
            <when test='searchVO.nextGroupLevel == "LV2"'>
                DISTINCT t1.lv2_prod_list_code AS groupCode, t1.lv2_prod_list_cn_name AS groupCnName,'LV2' AS groupLevel
            </when>
            <when test='searchVO.nextGroupLevel == "LV1"'>
                DISTINCT t1.lv1_prod_list_code AS groupCode, t1.lv1_prod_list_cn_name AS groupCnName,'LV1' AS groupLevel
            </when>
            <when test='searchVO.nextGroupLevel == "CODE"'>
                DISTINCT t1.code_attributes AS groupCode, t1.code_attributes AS groupCnName,'CODE' AS groupLevel
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <include refid="mainSearchWhere"></include>
        <if test ='searchVO.nextGroupLevel == "SPART"'>
            order by t1.spart_code asc
        </if>
    </select>

    <select id="mainFlagSpartDimInfoList"  resultMap="resultMap">
        select distinct t1.spart_code AS groupCode, t1.spart_cn_name AS groupCnName, 'SPART' AS groupLevel
        <include refid="mainSearchWhere"></include>
        LIMIT #{pageVO.pageSize} OFFSET #{pageVO.mysqlStartIndex}
    </select>

    <select id="mainFlagSpartDimInfoListCount"  resultType="int">
        select count(1) from (select distinct t1.spart_code AS groupCode, t1.spart_cn_name AS groupCnName, 'SPART' AS groupLevel
        <include refid="mainSearchWhere"></include>)
    </select>

    <sql id ="mainSearchWhere">
        <if test='searchVO.costType == "PSP"'>
            FROM fin_dm_opt_foi.dm_fcst_ict_psp_pbi_main_code_dim_t t1
            INNER JOIN fin_dm_opt_foi.dm_fcst_ict_psp_prod_top_spart_info_t t2
            <include refid="topSpartInnerJoin"/>
        </if>
        <if test='searchVO.costType == "STD"'>
            FROM fin_dm_opt_foi.dm_fcst_ict_std_pbi_main_code_dim_t t1
            INNER JOIN fin_dm_opt_foi.dm_fcst_ict_std_prod_top_spart_info_t t2
            <include refid="topSpartInnerJoin"/>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='searchVO.versionId!=null'>
                and t1.version_id = #{searchVO.versionId,jdbcType=NUMERIC}
            </if>
            <choose>
                <when test='searchVO.pageType == "ANNUAL"'>
                    <if test='searchVO.isHasSoftWare and (searchVO.costType == "PSP" and searchVO.softwareMark!=null and searchVO.softwareMark!="")'>
                        and t2.software_mark = #{searchVO.softwareMark,jdbcType=VARCHAR}
                    </if>
                </when>
                <otherwise>
                    <if test='searchVO.costType == "PSP" and searchVO.softwareMark!=null and searchVO.softwareMark!=""'>
                        and t2.software_mark = #{searchVO.softwareMark,jdbcType=VARCHAR}
                    </if>
                </otherwise>
            </choose>
            <if test='searchVO.bgCode!=null and searchVO.bgCode!=""'>
                and t1.bg_code = #{searchVO.bgCode,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.codeAttributes!=null and searchVO.codeAttributes!=""'>
                and t1.code_attributes = #{searchVO.codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.lv1ProdRndTeamCodeList != null and searchVO.lv1ProdRndTeamCodeList.size() > 0'>
                <foreach collection='searchVO.lv1ProdRndTeamCodeList' item="code" open="AND t1.lv1_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='searchVO.lv2ProdRndTeamCodeList != null and searchVO.lv2ProdRndTeamCodeList.size() > 0'>
                <foreach collection='searchVO.lv2ProdRndTeamCodeList' item="code" open="AND t1.lv2_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='searchVO.lv3ProdRndTeamCodeList != null and searchVO.lv3ProdRndTeamCodeList.size() > 0'>
                <foreach collection='searchVO.lv3ProdRndTeamCodeList' item="code" open="AND t1.lv3_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='searchVO.lv4ProdRndTeamCodeList != null and searchVO.lv4ProdRndTeamCodeList.size() > 0'>
                <foreach collection='searchVO.lv4ProdRndTeamCodeList' item="code" open="AND t1.lv4_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='searchVO.lv1ProdRndTeamCode!=null and searchVO.lv1ProdRndTeamCode!=""'>
                and t1.lv1_prod_list_code = #{searchVO.lv1ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.lv2ProdRndTeamCode!=null and searchVO.lv2ProdRndTeamCode!=""'>
                and t1.lv2_prod_list_code = #{searchVO.lv2ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.lv3ProdRndTeamCode!=null and searchVO.lv3ProdRndTeamCode!=""'>
                and t1.lv3_prod_list_code = #{searchVO.lv3ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.lv4ProdRndTeamCode!=null and searchVO.lv4ProdRndTeamCode!=""'>
                and t1.lv4_prod_list_code = #{searchVO.lv4ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.spartCodeList!=null and searchVO.spartCodeList.size() > 0'>
                <foreach collection='searchVO.spartCodeList' item="code" open="and t1.spart_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='searchVO.nextGroupLevel == "SPART"'>
                and t1.spart_code !='SNULL'
            </if>
            <if test='searchVO.nextGroupLevel == "LV1" and searchVO.lv2DimensionSet != null and searchVO.lv2DimensionSet.size() > 0'>
                <foreach collection='searchVO.lv2DimensionSet' item="code" open="AND t1.lv1_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='searchVO.nextGroupLevel == "LV2" and searchVO.lv3DimensionSet != null and searchVO.lv3DimensionSet.size() > 0'>
                <foreach collection='searchVO.lv3DimensionSet' item="code" open="AND t1.lv2_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='searchVO.keyword != null and searchVO.keyword != ""'>
                AND t1.spart_code LIKE '%'||#{searchVO.keyword} ||'%'
            </if>
        </trim>
    </sql>

    <select id="mainFlagAnnualDimInfoList"  resultMap="resultMap">
        select
        <choose>
            <when test='searchVO.nextGroupLevel == "SPART"'>
                DISTINCT t1.spart_code AS groupCode, t1.spart_cn_name AS groupCnName, 'SPART' AS groupLevel
            </when>
            <when test='searchVO.nextGroupLevel == "LV4"'>
                DISTINCT t1.lv4_prod_list_code AS groupCode, t1.lv4_prod_list_cn_name AS groupCnName, 'LV4' AS groupLevel
            </when>
            <when test='searchVO.nextGroupLevel == "LV3"'>
                DISTINCT t1.lv3_prod_list_code AS groupCode, t1.lv3_prod_list_cn_name AS groupCnName, 'LV3' AS groupLevel
            </when>
            <when test='searchVO.nextGroupLevel == "LV2"'>
                DISTINCT t1.lv2_prod_list_code AS groupCode, t1.lv2_prod_list_cn_name AS groupCnName,'LV2' AS groupLevel
            </when>
            <when test='searchVO.nextGroupLevel == "LV1"'>
                DISTINCT t1.lv1_prod_list_code AS groupCode, t1.lv1_prod_list_cn_name AS groupCnName,'LV1' AS groupLevel
            </when>
            <when test='searchVO.nextGroupLevel == "CODE"'>
                DISTINCT t1.code_attributes AS groupCode, t1.code_attributes AS groupCnName,'CODE' AS groupLevel
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <include refid="mainAnnualSearchWhere"></include>
        <if test ='searchVO.nextGroupLevel == "SPART"'>
            order by t1.spart_code asc
        </if>
    </select>

    <select id="mainFlagAnnualSpartDimInfoList"  resultMap="resultMap">
        select distinct t1.spart_code AS groupCode, t1.spart_cn_name AS groupCnName, 'SPART' AS groupLevel
        <include refid="mainAnnualSearchWhere"></include>
        LIMIT #{pageVO.pageSize} OFFSET #{pageVO.mysqlStartIndex}
    </select>

    <select id="mainFlagAnnualSpartDimInfoListCount"  resultType="int">
        select count(1) from (select distinct t1.spart_code AS groupCode, t1.spart_cn_name AS groupCnName, 'SPART' AS groupLevel
        <include refid="mainAnnualSearchWhere"></include>)
    </select>

    <sql id = "mainAnnualSearchWhere">
        <if test='searchVO.costType == "PSP"'>
            from fin_dm_opt_foi.dm_fcst_ict_psp_pbi_main_code_dim_t t1 INNER JOIN fin_dm_opt_foi.DM_FCST_ICT_PSP_PROD_DIM_INFO_T t2
            on t1.bg_code = t2.bg_code
            and t1.version_id = t2.version_id
            and nvl(t1.lv1_prod_list_code,'snull') = nvl(t2.lv1_prod_list_code,'snull')
            and nvl(t1.lv2_prod_list_code,'snull') = nvl(t2.lv2_prod_list_code,'snull')
            and nvl(t1.lv3_prod_list_code,'snull') = nvl(t2.lv3_prod_list_code,'snull')
            and nvl(t1.lv4_prod_list_code,'snull') = nvl(t2.lv4_prod_list_code,'snull')
            and nvl(t1.spart_code,'snull') = nvl(t2.spart_code,'snull')
            and t2.main_flag = #{searchVO.mainFlag}
            and t2.region_code = #{searchVO.regionCode}
            and t2.repoffice_code = #{searchVO.repofficeCode}
            and t2.oversea_flag = #{searchVO.overseaFlag}
            and t2.view_flag = #{searchVO.viewFlag}
            and t2.del_flag = 'N'
            <if test='searchVO.codeAttributes!=null and searchVO.codeAttributes!=""'>
                and nvl(t1.code_attributes,'snull') = nvl(t2.code_attributes,'snull')
            </if>
        </if>
        <if test='searchVO.costType == "STD"'>
            from fin_dm_opt_foi.dm_fcst_ict_std_pbi_main_code_dim_t t1 INNER JOIN fin_dm_opt_foi.DM_FCST_ICT_STD_PROD_DIM_INFO_T t2
            on t1.bg_code = t2.bg_code
            and t1.version_id = t2.version_id
            and nvl(t1.lv1_prod_list_code,'snull') = nvl(t2.lv1_prod_list_code,'snull')
            and nvl(t1.lv2_prod_list_code,'snull') = nvl(t2.lv2_prod_list_code,'snull')
            and nvl(t1.lv3_prod_list_code,'snull') = nvl(t2.lv3_prod_list_code,'snull')
            and nvl(t1.lv4_prod_list_code,'snull') = nvl(t2.lv4_prod_list_code,'snull')
            and nvl(t1.spart_code,'snull') = nvl(t2.spart_code,'snull')
            and t2.main_flag = #{searchVO.mainFlag}
            and t2.region_code = #{searchVO.regionCode}
            and t2.repoffice_code = #{searchVO.repofficeCode}
            and t2.oversea_flag = #{searchVO.overseaFlag}
            and t2.view_flag = #{searchVO.viewFlag}
            and t2.del_flag = 'N'
            <if test='searchVO.codeAttributes!=null and searchVO.codeAttributes!=""'>
                and nvl(t1.code_attributes,'snull') = nvl(t2.code_attributes,'snull')
            </if>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='searchVO.versionId!=null'>
                and t1.version_id = #{searchVO.versionId,jdbcType=NUMERIC}
            </if>
            <if test='searchVO.costType == "PSP" and searchVO.softwareMark!=null and searchVO.softwareMark!=""'>
                and t2.software_mark = #{searchVO.softwareMark,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.bgCode!=null and searchVO.bgCode!=""'>
                and t1.bg_code = #{searchVO.bgCode,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.codeAttributes!=null and searchVO.codeAttributes!=""'>
                and t1.code_attributes = #{searchVO.codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.lv1ProdRndTeamCodeList != null and searchVO.lv1ProdRndTeamCodeList.size() > 0'>
                <foreach collection='searchVO.lv1ProdRndTeamCodeList' item="code" open="AND t1.lv1_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='searchVO.lv2ProdRndTeamCodeList != null and searchVO.lv2ProdRndTeamCodeList.size() > 0'>
                <foreach collection='searchVO.lv2ProdRndTeamCodeList' item="code" open="AND t1.lv2_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='searchVO.lv3ProdRndTeamCodeList != null and searchVO.lv3ProdRndTeamCodeList.size() > 0'>
                <foreach collection='searchVO.lv3ProdRndTeamCodeList' item="code" open="AND t1.lv3_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='searchVO.lv4ProdRndTeamCodeList != null and searchVO.lv4ProdRndTeamCodeList.size() > 0'>
                <foreach collection='searchVO.lv4ProdRndTeamCodeList' item="code" open="AND t1.lv4_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='searchVO.lv1ProdRndTeamCode!=null and searchVO.lv1ProdRndTeamCode!=""'>
                and t1.lv1_prod_list_code = #{searchVO.lv1ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.lv2ProdRndTeamCode!=null and searchVO.lv2ProdRndTeamCode!=""'>
                and t1.lv2_prod_list_code = #{searchVO.lv2ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.lv3ProdRndTeamCode!=null and searchVO.lv3ProdRndTeamCode!=""'>
                and t1.lv3_prod_list_code = #{searchVO.lv3ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.lv4ProdRndTeamCode!=null and searchVO.lv4ProdRndTeamCode!=""'>
                and t1.lv4_prod_list_code = #{searchVO.lv4ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.dimensionCode!=null and searchVO.dimensionCode!=""'>
                and t1.dimension_code = #{searchVO.dimensionCode,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.dimensionSubCategoryCode!=null and searchVO.dimensionSubCategoryCode!=""'>
                and t1.dimension_subCategory_code = #{searchVO.dimensionSubCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.dimensionSubDetailCode!=null and searchVO.dimensionSubDetailCode!=""'>
                and t1.dimension_sub_detail_code = #{searchVO.dimensionSubDetailCode,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.spartCodeList!=null and searchVO.spartCodeList.size() > 0'>
                <foreach collection='searchVO.spartCodeList' item="code" open="and t1.spart_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='searchVO.nextGroupLevel == "SPART"'>
                and t1.spart_code !='SNULL'
            </if>
            <if test='searchVO.nextGroupLevel == "SUB_DETAIL"'>
                and t1.dimension_sub_detail_code !='SNULL'
            </if>
            <if test='searchVO.nextGroupLevel == "LV1" and searchVO.lv2DimensionSet != null and searchVO.lv2DimensionSet.size() > 0'>
                <foreach collection='searchVO.lv2DimensionSet' item="code" open="AND t1.lv1_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='searchVO.nextGroupLevel == "LV2" and searchVO.lv3DimensionSet != null and searchVO.lv3DimensionSet.size() > 0'>
                <foreach collection='searchVO.lv3DimensionSet' item="code" open="AND t1.lv2_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='searchVO.keyword != null and searchVO.keyword != ""'>
                AND t1.spart_code LIKE '%'||#{searchVO.keyword} ||'%'
            </if>
        </trim>
    </sql>

    <select id="getProdSpartOrDimensionNum"  resultType="java.lang.Integer">
        select
        <choose>
            <when test='groupLevel == "SPART"'>
                <if test='pageType == "ANNUAL"'>
                    count(spart_code) AS num
                </if>
                <if test='pageType != "ANNUAL"'>
                    count(top_spart_code) AS num
                </if>
            </when>
            <when test='groupLevel == "SUB_DETAIL"'>
                count(dimension_sub_detail_code) AS num
            </when>
            <when test='groupLevel == "SUBCATEGORY"'>
                count(dimension_subcategory_code) AS num
            </when>
            <when test='groupLevel == "DIMENSION"'>
                count(dimension_code) AS num
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='costType == "PSP"'>
            <choose>
                <when test='pageType == "ANNUAL"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_dim_info_t
                </when>
                <when test='pageType == "MONTH" and viewFlag =="PROD_SPART"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_top_spart_info_t
                </when>
                <when test='pageType == "MONTH" and viewFlag =="DIMENSION"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_dim_info_t
                </when>
                <when test='pageType == "REPLACE_DIM"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_top_spart_info_t
                </when>
            </choose>
        </if>
        <if test='costType == "STD"'>
            <choose>
                <when test='pageType == "ANNUAL"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_dim_info_t
                </when>
                <when test='pageType == "MONTH" and viewFlag =="PROD_SPART"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_top_spart_info_t
                </when>
                <when test='pageType == "MONTH" and viewFlag =="DIMENSION"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_dim_info_t
                </when>
                <when test='pageType == "REPLACE_DIM"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_top_spart_info_t
                </when>
            </choose>
        </if>
        <include refid="getNumSqlCondition"></include>
    </select>

    <select id="getLv4CodeWithSpartOrDimension"  resultMap="resultMap">
        select lv4_prod_list_code as prod_rnd_team_code
        <if test='costType == "PSP"'>
            <choose>
                <when test='pageType == "ANNUAL"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_dim_info_t
                </when>
                <when test='pageType == "MONTH" and viewFlag =="PROD_SPART"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_top_spart_info_t
                </when>
                <when test='pageType == "MONTH" and viewFlag =="DIMENSION"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_dim_info_t
                </when>
                <when test='pageType == "REPLACE_DIM"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_top_spart_info_t
                </when>
            </choose>
        </if>
        <if test='costType == "STD"'>
            <choose>
                <when test='pageType == "ANNUAL"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_dim_info_t
                </when>
                <when test='pageType == "MONTH" and viewFlag =="PROD_SPART"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_top_spart_info_t
                </when>
                <when test='pageType == "MONTH" and viewFlag =="DIMENSION"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_dim_info_t
                </when>
                <when test='pageType == "REPLACE_DIM"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_top_spart_info_t
                </when>
            </choose>
        </if>
        <include refid="getNumSqlCondition"></include>
    </select>

    <sql id="getNumSqlCondition">
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='versionId!=null'>
                and version_id = #{versionId,jdbcType=NUMERIC}
            </if>
            <if test='pageType == "REPLACE_DIM" or (pageType == "MONTH" and viewFlag == "PROD_SPART")'>
                and is_top_flag ='Y'
                and double_flag = 'Y'
            </if>
            <if test='costType == "PSP" and softwareMark!=null and softwareMark!=""'>
                and software_mark = #{softwareMark,jdbcType=VARCHAR}
            </if>
            <if test='regionCode!=null and regionCode!=""'>
                and region_code = #{regionCode,jdbcType=VARCHAR}
            </if>
            <if test='repofficeCode!=null and repofficeCode!=""'>
                and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='bgCode!=null and bgCode!=""'>
                and bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='overseaFlag!=null and overseaFlag!=""'>
                and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='viewFlag!=null and viewFlag!=""'>
                and view_flag = #{viewFlag,jdbcType=VARCHAR}
            </if>
            <if test='mainFlag!=null and mainFlag!=""'>
                and main_flag = #{mainFlag,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='pageType == "ANNUAL" and groupLevel!=null and groupLevel!=""'>
                and group_level = #{groupLevel,jdbcType=VARCHAR}
            </if>
            <if test='pageType == "MONTH" and viewFlag == "DIMENSION" and groupLevel!=null and groupLevel!=""'>
                and group_level = #{groupLevel,jdbcType=VARCHAR}
            </if>
            <if test='lv0ProdRndTeamCodeList != null and lv0ProdRndTeamCodeList.size() > 0'>
                <foreach collection='lv0ProdRndTeamCodeList' item="code" open="AND lv0_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv1ProdRndTeamCodeList != null and lv1ProdRndTeamCodeList.size() > 0'>
                <foreach collection='lv1ProdRndTeamCodeList' item="code" open="AND lv1_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv2ProdRndTeamCodeList != null and lv2ProdRndTeamCodeList.size() > 0'>
                <foreach collection='lv2ProdRndTeamCodeList' item="code" open="AND lv2_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv3ProdRndTeamCodeList != null and lv3ProdRndTeamCodeList.size() > 0'>
                <foreach collection='lv3ProdRndTeamCodeList' item="code" open="AND lv3_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv4ProdRndTeamCodeList != null and lv4ProdRndTeamCodeList.size() > 0'>
                <foreach collection='lv4ProdRndTeamCodeList' item="code" open="AND lv4_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv0ProdRndTeamCode!=null and lv0ProdRndTeamCode!=""'>
                and lv0_prod_list_code = #{lv0ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv1ProdRndTeamCode!=null and lv1ProdRndTeamCode!=""'>
                and lv1_prod_list_code = #{lv1ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv2ProdRndTeamCode!=null and lv2ProdRndTeamCode!=""'>
                and lv2_prod_list_code = #{lv2ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv3ProdRndTeamCode!=null and lv3ProdRndTeamCode!=""'>
                and lv3_prod_list_code = #{lv3ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv4ProdRndTeamCode!=null and lv4ProdRndTeamCode!=""'>
                and lv4_prod_list_code = #{lv4ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionCode!=null and dimensionCode!=""'>
                and dimension_code = #{dimensionCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubCategoryCode!=null and dimensionSubCategoryCode!=""'>
                and dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubDetailCode!=null and dimensionSubDetailCode!=""'>
                and dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
            </if>
            <if test='spartCodeList!=null and spartCodeList.size() > 0 and pageType == "ANNUAL"'>
                <foreach collection='spartCodeList' item="code" open="and spart_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='spartCodeList!=null and spartCodeList.size() > 0 and pageType != "ANNUAL"'>
                <foreach collection='spartCodeList' item="code" open="and top_spart_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='spartCode!=null and spartCode!="" and pageType != "ANNUAL"'>
                and top_spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='nextGroupLevel == "SPART" and pageType == "ANNUAL"'>
                and spart_code !='SNULL'
            </if>
            <if test='nextGroupLevel == "SPART" and pageType != "ANNUAL"'>
                and top_spart_code !='SNULL'
            </if>
            <if test='nextGroupLevel == "SUB_DETAIL"'>
                and dimension_sub_detail_code !='SNULL'
            </if>
            <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND lv1_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel == "LV2" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND lv2_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </sql>
    <select id="getLv1AndLv2CodeList" resultMap="resultMap">
        select * from (select DISTINCT lv0_prod_list_code as lv0_code,lv1_prod_list_code as lv1_code,lv2_prod_list_code as lv2_code FROM fin_dm_opt_foi.dm_fcst_ict_psp_prod_dim_info_t
        WHERE  DEL_FLAG = 'N' and group_level ='LV2'
        union
        select DISTINCT lv0_prod_list_code as lv0_code,lv1_prod_list_code as lv1_code,lv2_prod_list_code as lv2_code FROM fin_dm_opt_foi.dm_fcst_ict_std_prod_dim_info_t
        WHERE  DEL_FLAG = 'N' and group_level ='LV2')
        <trim  prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='versionId != null'>
                and version_id = #{versionId}
            </if>
            <if test='lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND lv1_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND lv2_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="reverseFindLv1Code" resultMap="resultMap">
        SELECT DISTINCT lv1_prod_list_code as lv1_code,lv1_prod_list_cn_name as lv1_cn_name
        <if test='costType == "PSP"'>
            FROM  fin_dm_opt_foi.dm_fcst_ict_psp_prod_dim_info_t
        </if>
        <if test='costType == "STD"'>
            FROM  fin_dm_opt_foi.dm_fcst_ict_std_prod_dim_info_t
        </if>
        <trim  prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='versionId != null'>
                and version_id = #{versionId}
            </if>
            <if test='lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND lv2_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='costType == "PSP" and softwareMark!=null and softwareMark!=""'>
                and software_mark = #{softwareMark,jdbcType=VARCHAR}
            </if>
            <if test='regionCode!=null and regionCode!=""'>
                and region_code = #{regionCode,jdbcType=VARCHAR}
            </if>
            <if test='repofficeCode!=null and repofficeCode!=""'>
                and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='bgCode!=null and bgCode!=""'>
                and bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='overseaFlag!=null and overseaFlag!=""'>
                and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='viewFlag!=null and viewFlag!=""'>
                and view_flag = #{viewFlag,jdbcType=VARCHAR}
            </if>
            <if test='mainFlag!=null and mainFlag!=""'>
                and main_flag = #{mainFlag,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            and del_flag ='N'
        </trim>
    </select>

    <select id="reverseFindLv1CodeMonth" resultMap="resultMap">
        SELECT DISTINCT lv1_prod_list_code as lv1_code,lv1_prod_list_cn_name as lv1_cn_name
        <if test='costType == "PSP"'>
            FROM  fin_dm_opt_foi.dm_fcst_ict_psp_prod_top_spart_info_t
        </if>
        <if test='costType == "STD"'>
            FROM  fin_dm_opt_foi.dm_fcst_ict_std_prod_top_spart_info_t
        </if>
        WHERE version_id = #{versionId,jdbcType=NUMERIC} and del_flag ='N' and is_top_flag ='Y'
        <if test='lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
            <foreach collection='lv3DimensionSet' item="code" open="AND lv2_prod_list_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='costType == "PSP" and softwareMark!=null and softwareMark!=""'>
            and software_mark = #{softwareMark,jdbcType=VARCHAR}
        </if>
        <if test='regionCode!=null and regionCode!=""'>
            and region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode!=null and repofficeCode!=""'>
            and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='bgCode!=null and bgCode!=""'>
            and bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag!=null and overseaFlag!=""'>
            and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag!=null and viewFlag!=""'>
            and view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag!=null and mainFlag!=""'>
            and main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes!=null and codeAttributes!=""'>
            and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
    </select>
</mapper>