/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.util;

import com.huawei.it.jalor5.core.request.IUserPrincipal;
import com.huawei.it.jalor5.core.request.RequestContextException;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.security.UserVO;

/**
 * UserInfo
 *
 * <AUTHOR>
 * @since 2024-7-4
 */
public class UserInfoUtils {


    /**
     * get role id of the current login user
     *
     * @return UserVO
     */
    public static int getRoleId() {
        return getCurrentUser().getCurrentRole().getRoleId();
    }

    /**
     * get role id of the current login user
     *
     * @return UserVO
     */
    public static String getRoleName() {
        return getCurrentUser().getCurrentRole().getRoleName();
    }

    /**
     * getUserId
     *
     * @return Long user id
     */
    public static Long getUserId() {
        IUserPrincipal user = RequestContext.getCurrent().getUser();
        if (user != null) {
            return user.getUserId();
        }
        throw new RequestContextException();
    }

    /**
     * get current login user
     *
     * @return UserVO
     */
    public static UserVO getCurrentUser() {
        return (UserVO) RequestContext.getCurrent().getUser();
    }

    public static String getUserCn() {
        IUserPrincipal user = RequestContext.getCurrent().getUser();
        if (user != null) {
            return user.getUserCN();
        }
        throw new RequestContextException();
    }

}
