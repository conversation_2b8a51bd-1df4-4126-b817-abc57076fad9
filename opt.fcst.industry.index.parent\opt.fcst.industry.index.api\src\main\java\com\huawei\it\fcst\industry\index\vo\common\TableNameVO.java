/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.common;

import com.huawei.it.fcst.industry.index.enums.IndustryConst;
import org.jetbrains.annotations.Nullable;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/4/2
 */
public class TableNameVO implements Serializable {
    private static final long serialVersionUID = -5394978246348938140L;

    /**
     * 表名称前缀
     */
    private String tablePreFix;

    /**
     * 产业组织区分
     */
    private String industryOrg;

    public String getTablePreFix() {
        if (this.industryOrg == null) {
            return null;
        }
        IndustryConst.INDUSTRY_ORG industryOrgName = IndustryConst.getIndustryOrgName(this.industryOrg);
        return getTableName(industryOrgName);
    }

    @Nullable
    private static String getTableName(IndustryConst.INDUSTRY_ORG industryOrgName) {
        String tableValue = null;
        switch (industryOrgName) {
            case ICT:
                tableValue = IndustryConst.TABLE_NAME.ICT_TABLE.getValue();
                break;
            case ICT_NEW:
                tableValue = IndustryConst.TABLE_NAME.ICT_NEW_TABLE.getValue();
                break;
            case ICT_PRICE:
                tableValue = IndustryConst.TABLE_NAME.ICT_PRICE_TABLE.getValue();
                break;
            case ENERGY:
                tableValue = IndustryConst.TABLE_NAME.ENERGY_TABLE.getValue();
                break;
            case IAS:
                tableValue = IndustryConst.TABLE_NAME.IAS_TABLE.getValue();
                break;
            default:
                break;
        }
        return tableValue;
    }

    public static String getTablePreFix(String industryOrg) {
        if (industryOrg == null) {
            return null;
        }
        IndustryConst.INDUSTRY_ORG industryOrgName = IndustryConst.getIndustryOrgName(industryOrg);
        return getTableName(industryOrgName);
    }

    public void setTablePreFix(String tablePreFix) {
        this.tablePreFix = tablePreFix;
    }

    public String getIndustryOrg() {
        return industryOrg;
    }

    public void setIndustryOrg(String industryOrg) {
        this.industryOrg = industryOrg;
    }
}

