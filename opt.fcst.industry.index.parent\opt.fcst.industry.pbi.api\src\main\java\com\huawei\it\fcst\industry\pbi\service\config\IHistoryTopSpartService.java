/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.service.config;

import com.huawei.it.fcst.industry.pbi.vo.common.DmFcstDataRefreshStatus;
import com.huawei.it.fcst.industry.pbi.vo.config.HistoryTopSpartInfoVO;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;

/**
 * <AUTHOR>
 * @since 2024/6/26
 */
@Path("/history")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface IHistoryTopSpartService {

    /**
     * 历史SPART清单分页查询
     *
     * @param historyTopSpartInfoVO 参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/topSpart/pageList")
    ResultDataVO getTopSpartPageList(HistoryTopSpartInfoVO historyTopSpartInfoVO) throws CommonApplicationException;

    /**
     * 历史SPART清单导出
     *
     * @param historyTopSpartInfoVO 参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/topSpartExport")
    ResultDataVO topSpartExport(@Context HttpServletResponse response, HistoryTopSpartInfoVO historyTopSpartInfoVO) throws ApplicationException;

    /**
     * 异步导出获取任务状态查询
     *
     * @param dataRefreshStatus 参数
     * @return 结果
     * @throws CommonApplicationException 异常
     */
    @POST
    @Path("/query/status")
    ResultDataVO queryDataRefreshStatus(DmFcstDataRefreshStatus dataRefreshStatus) throws CommonApplicationException;


}
