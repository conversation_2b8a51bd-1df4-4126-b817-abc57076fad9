/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.config;

import com.huawei.it.fcst.industry.index.vo.common.TableNameVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * apdFomManufactureDimVO Class
 *
 * <AUTHOR>
 * @since 2023/10/18
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "配置管理页面历史清单入参")
public class FomManufactureDimVO extends TableNameVO {
    /**
     * 版本id（主键）
     **/
    @ApiModelProperty(value = "版本id（主键）")
    private Long versionId;

    /**
     * 编码
     **/
    @ApiModelProperty("item_code")
    private String itemCode;

    /**
     * 名称（中文描述）
     **/
    @ApiModelProperty("cn_desc")
    private String cnDesc;

    /**
     * LV0名称
     **/
    @ApiModelProperty("lv0_org_cn")
    private String lv0OrgCn;

    /**
     * 经营对象
     **/
    @ApiModelProperty("bussiness_object")
    private String bussinessObject;

    /**
     * 发货对象
     **/
    @ApiModelProperty("shipping_object")
    private String shippingObject;

    /**
     * 制造对象
     **/
    @ApiModelProperty("manufacture_object")
    private String manufactureObject;

    /**
     * 制造BU
     **/
    @ApiModelProperty("manufacture_bu")
    private String manufactureBu;

    @ApiModelProperty(value = "维度类型（U：通用/ P盈利颗粒度/ D量纲颗粒度）")
    private String granularityType;

    @ApiModelProperty(value = "数据类型（CATEGORY：制造对象、ITEM：规格品）")
    private String dataType;

    /**
     * 发货对象编码
     **/
    @ApiModelProperty("top_shipping_object_code")
    private String topShippingObjectCode;

    /**
     * 制造对象编码
     **/
    @ApiModelProperty("top_manufacture_object_code")
    private String topManufactureObjectCode;

    /**
     * 发货对象名称
     **/
    @ApiModelProperty("top_shipping_object_cn_name")
    private String topShippingObjectCnName;

    /**
     * 制造对象名称
     **/
    @ApiModelProperty("top_manufacture_object_cn_name")
    private String topManufactureObjectCnName;

    /**
     *  页码
     */
    private Integer pageIndex;

    /**
     *  一页数量
     */
    private Integer pageSize;

    /**
     *  总条数
     */
    private Integer totalSize;

    private String tableDimPreFix;
}
