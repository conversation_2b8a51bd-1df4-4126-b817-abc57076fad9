/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.utils;

import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.AssertionsForClassTypes.assertThatNoException;

/**
 * FcstIndexUtilTest Class
 *
 * <AUTHOR>
 * @since 2023/4/26
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class FcstIndexUtilTest {
    @InjectMocks
    private FcstIndexUtil fcstIndexUtil;

    @Test
    public void getNextThreeGroupLevel() {
        String groupLevel="LV1";
        boolean flag=true;
        String level = fcstIndexUtil.getNextThreeGroupLevel(groupLevel);
        Assert.assertNotNull(level);
    }

    @Test
    public void getNextThreeGroupLevel2t() {
        String groupLevel="LV1";
        boolean flag=false;
        String level = fcstIndexUtil.getNextThreeGroupLevel(groupLevel);
        Assert.assertNotNull(level);
    }

    @Test
    public void getNextOneGroupLevel() {
        String groupLevel="CEG";
        String level = fcstIndexUtil.getNextOneGroupLevel(groupLevel);
        Assert.assertNotNull(level);
    }

    @Test
    public void getNextTwoGroupLevel() {
        String groupLevel="CEG";
        String level = fcstIndexUtil.getNextTwoGroupLevel(groupLevel);
        Assert.assertNotNull(level);
    }

    @Test
    public void getNextGroupLevelByView() {
        String viewFlag="0";
        String level="CEG";
        boolean flag=true;
        String byView = fcstIndexUtil.getNextGroupLevelByView(viewFlag, level,"U","ICT");
        Assert.assertNotNull(byView);
    }

    @Test
    public void getNextGroupLevelByView2t() {
        String viewFlag="1";
        String level="CEG";
        boolean flag=true;
        String byView = fcstIndexUtil.getNextGroupLevelByView(viewFlag, level,"U","ICT");
        Assert.assertNotNull(byView);
    }

    @Test
    public void getNextGroupLevelByView3t() {
        String viewFlag="2";
        String level="CEG";
        boolean flag=true;
        String byView = fcstIndexUtil.getNextGroupLevelByView(viewFlag, level,"U","ICT");
        Assert.assertNotNull(byView);
    }

    @Test
    public void getNextGroupLevelByView4t() {
        String viewFlag="3";
        String level="CEG";
        boolean flag=true;
        String byView = fcstIndexUtil.getNextGroupLevelByView(viewFlag, level,"U","ICT");
        Assert.assertNotNull(byView);
    }

    @Test
    public void getNextGroupLevelByView5t() {
        String viewFlag="0";
        String level="CEG";
        boolean flag=true;
        String byView = fcstIndexUtil.getNextGroupLevelByView(viewFlag, level,"D","ICT");
        Assert.assertNotNull(byView);
    }

    @Test
    public void getNextGroupLevelByView6t() {
        String viewFlag="1";
        String level="CEG";
        boolean flag=true;
        String byView = fcstIndexUtil.getNextGroupLevelByView(viewFlag, level,"D","ICT");
        Assert.assertNotNull(byView);
    }

    @Test
    public void getNextGroupLevelByView7t() {
        String viewFlag="2";
        String level="CEG";
        boolean flag=true;
        String byView = fcstIndexUtil.getNextGroupLevelByView(viewFlag, level,"D","ICT");
        Assert.assertNotNull(byView);
    }

    @Test
    public void getNextGroupLevelByView8t() {
        String viewFlag="3";
        String level="CEG";
        boolean flag=true;
        String byView = fcstIndexUtil.getNextGroupLevelByView(viewFlag, level,"D","ICT");
        Assert.assertNotNull(byView);
    }

    @Test
    public void getNextGroupLevelByView9t() {
        String viewFlag="3";
        String level="CEG";
        boolean flag=true;
        String byView = fcstIndexUtil.getNextGroupLevelByView(viewFlag, level,"P","ICT");
        Assert.assertNotNull(byView);
    }

    @Test
    public void getNextGroupLevelByView10t() {
        String viewFlag="4";
        String level="CEG";
        boolean flag=true;
        String byView = fcstIndexUtil.getNextGroupLevelByView(viewFlag, level,"P","ICT");
        Assert.assertNotNull(byView);
    }

    @Test
    public void getNextGroupLevelByView11t() {
        String viewFlag="4";
        String level="CEG";
        boolean flag=true;
        String byView = fcstIndexUtil.getNextGroupLevelByView(viewFlag, level,"D","ICT");
        Assert.assertNotNull(byView);
    }

    @Test
    public void getNextGroupLevelByView12t() {
        String viewFlag="4";
        String level="CEG";
        boolean flag=true;
        String byView = fcstIndexUtil.getNextGroupLevelByView(viewFlag, level,"U","ICT");
        Assert.assertNotNull(byView);
    }

    @Test
    public void getNextGroupLevelByView13t() {
        String viewFlag="5";
        String level="CEG";
        boolean flag=true;
        String byView = fcstIndexUtil.getNextGroupLevelByView(viewFlag, level,"P","ICT");
        Assert.assertNotNull(byView);
    }

    @Test
    public void getNextGroupLevelByView14t() {
        String viewFlag="5";
        String level="CEG";
        boolean flag=true;
        String byView = fcstIndexUtil.getNextGroupLevelByView(viewFlag, level,"D","ICT");
        Assert.assertNotNull(byView);
    }

    @Test
    public void getNextGroupLevelByView15t() {
        String viewFlag="5";
        String level="CEG";
        boolean flag=true;
        String byView = fcstIndexUtil.getNextGroupLevelByView(viewFlag, level,"U","ICT");
        Assert.assertNotNull(byView);
    }

    @Test
    public void getNextGroupLevelByView16t() {
        String viewFlag="6";
        String level="CEG";
        boolean flag=true;
        String byView = fcstIndexUtil.getNextGroupLevelByView(viewFlag, level,"U","ICT");
        Assert.assertNotNull(byView);
    }

    @Test
    public void getNextGroupLevelByView17t() {
        String viewFlag="6";
        String level="CEG";
        boolean flag=true;
        String byView = fcstIndexUtil.getNextGroupLevelByView(viewFlag, level,"D","ICT");
        Assert.assertNotNull(byView);
    }

    @Test
    public void getNextGroupLevelByView18t() {
        String viewFlag="7";
        String level="CEG";
        boolean flag=true;
        String byView = fcstIndexUtil.getNextGroupLevelByView(viewFlag, level,"D","ICT");
        Assert.assertNotNull(byView);
    }

    @Test
    public void getNextGroupLevelByView19t() {
        String viewFlag="8";
        String level="CEG";
        boolean flag=true;
        String byView = fcstIndexUtil.getNextGroupLevelByView(viewFlag, level,"D","ICT");
        Assert.assertNotNull(byView);
    }

    @Test
    public void getNextGroupLevelByView20t() {
        String viewFlag="9";
        String level="CEG";
        boolean flag=true;
        String byView = fcstIndexUtil.getNextGroupLevelByView(viewFlag, level,"D","ICT");
        Assert.assertNotNull(byView);
    }


    @Test
    public void getNextGroupLevel() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setViewFlag("0");
        Map nextGroupLevel = fcstIndexUtil.getNextGroupLevel(monthAnalysisVO);
        Assert.assertNotNull(nextGroupLevel);
    }

    @Test
    public void getNextGroupLevel2t() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setViewFlag("1");
        Map nextGroupLevel = fcstIndexUtil.getNextGroupLevel(monthAnalysisVO);
        Assert.assertNotNull(nextGroupLevel);
    }

    @Test
    public void getNextGroupLevel3t() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setViewFlag("2");
        Map nextGroupLevel = fcstIndexUtil.getNextGroupLevel(monthAnalysisVO);
        Assert.assertNotNull(nextGroupLevel);
    }

    @Test
    public void getNextGroupLevel4t() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setGranularityType("D");
        Map nextGroupLevel = fcstIndexUtil.getNextGroupLevel(monthAnalysisVO);
        Assert.assertNotNull(nextGroupLevel);
    }

    @Test
    public void getNextGroupLevel5t() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setViewFlag("1");
        monthAnalysisVO.setGranularityType("D");
        Map nextGroupLevel = fcstIndexUtil.getNextGroupLevel(monthAnalysisVO);
        Assert.assertNotNull(nextGroupLevel);
    }

    @Test
    public void getNextGroupLevel6t() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setViewFlag("2");
        monthAnalysisVO.setGranularityType("D");
        Map nextGroupLevel = fcstIndexUtil.getNextGroupLevel(monthAnalysisVO);
        Assert.assertNotNull(nextGroupLevel);
    }

    @Test
    public void getNextGroupLevel7t() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setViewFlag("5");
        monthAnalysisVO.setGranularityType("D");
        Map nextGroupLevel = fcstIndexUtil.getNextGroupLevel(monthAnalysisVO);
        Assert.assertNotNull(nextGroupLevel);
    }

    @Test
    public void getNextGroupLevel8T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setViewFlag("3");
        monthAnalysisVO.setGranularityType("D");
        Map nextGroupLevel = fcstIndexUtil.getNextGroupLevel(monthAnalysisVO);
        Assert.assertNotNull(nextGroupLevel);
    }

    @Test
    public void getNextGroupLevel9T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setViewFlag("4");
        monthAnalysisVO.setGranularityType("D");
        Map nextGroupLevel = fcstIndexUtil.getNextGroupLevel(monthAnalysisVO);
        Assert.assertNotNull(nextGroupLevel);
    }

    @Test
    public void getNextGroupLevel10T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setViewFlag("6");
        monthAnalysisVO.setGranularityType("D");
        Map nextGroupLevel = fcstIndexUtil.getNextGroupLevel(monthAnalysisVO);
        Assert.assertNotNull(nextGroupLevel);
    }

    @Test
    public void getNextGroupLevel11T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setViewFlag("7");
        monthAnalysisVO.setGranularityType("D");
        Map nextGroupLevel = fcstIndexUtil.getNextGroupLevel(monthAnalysisVO);
        Assert.assertNotNull(nextGroupLevel);
    }

    @Test
    public void getNextGroupLevel12T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setViewFlag("8");
        monthAnalysisVO.setGranularityType("D");
        Map nextGroupLevel = fcstIndexUtil.getNextGroupLevel(monthAnalysisVO);
        Assert.assertNotNull(nextGroupLevel);
    }

    @Test
    public void getNextGroupLevel13T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setViewFlag("6");
        monthAnalysisVO.setGranularityType("U");
        Map nextGroupLevel = fcstIndexUtil.getNextGroupLevel(monthAnalysisVO);
        Assert.assertNotNull(nextGroupLevel);
    }

    @Test
    public void getNextGroupLevel14T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setViewFlag("3");
        monthAnalysisVO.setGranularityType("U");
        Map nextGroupLevel = fcstIndexUtil.getNextGroupLevel(monthAnalysisVO);
        Assert.assertNotNull(nextGroupLevel);
    }

    @Test
    public void getNextGroupLevel15T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setViewFlag("3");
        monthAnalysisVO.setGranularityType("P");
        Map nextGroupLevel = fcstIndexUtil.getNextGroupLevel(monthAnalysisVO);
        Assert.assertNotNull(nextGroupLevel);
    }

    @Test
    public void getNextGroupLevel16T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setViewFlag("4");
        monthAnalysisVO.setGranularityType("P");
        Map nextGroupLevel = fcstIndexUtil.getNextGroupLevel(monthAnalysisVO);
        Assert.assertNotNull(nextGroupLevel);
    }

    @Test
    public void getNextGroupLevel17T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setViewFlag("4");
        monthAnalysisVO.setGranularityType("U");
        Map nextGroupLevel = fcstIndexUtil.getNextGroupLevel(monthAnalysisVO);
        Assert.assertNotNull(nextGroupLevel);
    }

    @Test
    public void getNextGroupLevel18T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setViewFlag("5");
        monthAnalysisVO.setGranularityType("U");
        Map nextGroupLevel = fcstIndexUtil.getNextGroupLevel(monthAnalysisVO);
        Assert.assertNotNull(nextGroupLevel);
    }


    @Test
    public void getPeriodScope() {
        List<Integer> periodScope = fcstIndexUtil.getPeriodScope("2023");
        Assert.assertNotNull(periodScope);
    }

    @Test
    public void handlePeriod() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setViewFlag("2");
        fcstIndexUtil.handlePeriod(monthAnalysisVO,"2023");
        Assertions.assertNull(null);
    }

    @Test
    public void handlePeriod2T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setPeriodStartTime(202301);
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setViewFlag("2");
        fcstIndexUtil.handlePeriod(monthAnalysisVO,"2023");
        Assertions.assertNull(null);
    }

    @Test
    public void handlePeriod3T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setPeriodEndTime(202312);
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setViewFlag("2");
        fcstIndexUtil.handlePeriod(monthAnalysisVO,"2023");
        Assertions.assertNull(null);
    }

    @Test
    public void handlePeriodForMonthYoy() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setViewFlag("2");
        fcstIndexUtil.handlePeriodForMonthYoy(monthAnalysisVO,"2023");
        Assertions.assertNull(null);
    }

    @Test
    public void handlePeriodForMonthYoy2T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setPeriodStartTime(202301);
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setViewFlag("2");
        fcstIndexUtil.handlePeriodForMonthYoy(monthAnalysisVO,"2023");
        Assertions.assertNull(null);
    }

    @Test
    public void handlePeriodForMonthYoy3T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setPeriodEndTime(202312);
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setViewFlag("2");
        fcstIndexUtil.handlePeriodForMonthYoy(monthAnalysisVO,"2023");
        assertThatNoException();
    }

    @Test
    public void getTitle() {
        String title="月度分析";
        String groupCnName="月度分析";
        String title1 = fcstIndexUtil.getTitle(title, groupCnName);
        Assert.assertNotNull(title1);
    }

    @Test
    public void getBasePeriodStr() {
        Integer basePeriod=20230422;
        String basePeriodStr = fcstIndexUtil.getBasePeriodStr(basePeriod);
        Assert.assertNotNull(basePeriodStr);
    }

    @Test
    public void getNextSevenGroupLevel() {
        String groupLevel="Lv2";
        String basePeriodStr = fcstIndexUtil.getNextSevenGroupLevel(groupLevel);
        Assert.assertNull(basePeriodStr);
    }

    @Test
    public void getNextSixGroupLevel() {
        String groupLevel="Lv2";
        String basePeriodStr = fcstIndexUtil.getNextSixGroupLevel(groupLevel);
        Assert.assertNull(basePeriodStr);
    }

    @Test
    public void getNextFiveGroupLevelUniversal() {
        String groupLevel="Lv2";
        String basePeriodStr = fcstIndexUtil.getNextFiveGroupLevelUniversal(groupLevel);
        Assert.assertNull(basePeriodStr);
    }

    @Test
    public void getNextFourGroupLevelProfit() {
        String groupLevel="Lv2";
        String basePeriodStr = fcstIndexUtil.getNextFourGroupLevelProfit(groupLevel);
        Assert.assertNull(basePeriodStr);
    }

    @Test
    public void getNextFiveGroupLevel() {
        String groupLevel="Lv2";
        String basePeriodStr = fcstIndexUtil.getNextFiveGroupLevel(groupLevel);
        Assert.assertNull(basePeriodStr);
    }

    @Test
    public void getNextOneGroupLevelDimension() {
        String groupLevel="Lv2";
        String basePeriodStr = fcstIndexUtil.getNextOneGroupLevelDimension(groupLevel);
        Assert.assertNull(basePeriodStr);
    }

    @Test
    public void getNextTwoGroupLevelDimension() {
        String groupLevel="Lv2";
        String basePeriodStr = fcstIndexUtil.getNextTwoGroupLevelDimension(groupLevel);
        Assert.assertNull(basePeriodStr);
    }

    @Test
    public void getNextThreeGroupLevelDimension() {
        String groupLevel="Lv2";
        String basePeriodStr = fcstIndexUtil.getNextThreeGroupLevelDimension(groupLevel);
        Assert.assertNull(basePeriodStr);
    }

    @Test
    public void getNextFourGroupLevelDimension() {
        String groupLevel="Lv2";
        String basePeriodStr = fcstIndexUtil.getNextFourGroupLevelDimension(groupLevel);
        Assert.assertNull(basePeriodStr);
    }

    @Test
    public void getNextFiveGroupLevelDimension() {
        String groupLevel="Lv2";
        String basePeriodStr = fcstIndexUtil.getNextFiveGroupLevelDimension(groupLevel);
        Assert.assertNull(basePeriodStr);
    }

    @Test
    public void getNextSixGroupLevelDimension() {
        String groupLevel="Lv2";
        String basePeriodStr = fcstIndexUtil.getNextSixGroupLevelDimension(groupLevel);
        Assert.assertNull(basePeriodStr);
    }

    @Test
    public void getNextSevenGroupLevelDimension() {
        String groupLevel="Lv2";
        String basePeriodStr = fcstIndexUtil.getNextSevenGroupLevelDimension(groupLevel);
        Assert.assertNull(basePeriodStr);
    }

    @Test
    public void getNextEightGroupLevelDimension() {
        String groupLevel="Lv2";
        String basePeriodStr = fcstIndexUtil.getNextEightGroupLevelDimension(groupLevel);
        Assert.assertNull(basePeriodStr);
    }

    @Test
    public void getNextNineGroupLevelDimension() {
        String groupLevel="Lv2";
        String basePeriodStr = fcstIndexUtil.getNextNineGroupLevelDimension(groupLevel);
        Assert.assertNull(basePeriodStr);
    }

}