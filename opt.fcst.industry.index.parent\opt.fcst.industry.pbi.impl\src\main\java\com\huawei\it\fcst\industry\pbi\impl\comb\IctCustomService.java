/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.comb;

import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.industry.pbi.dao.IDmCustomDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmCustomTempDao;
import com.huawei.it.fcst.industry.pbi.enums.IndustryConstEnum;
import com.huawei.it.fcst.industry.pbi.vo.comb.CombTransformVO;
import com.huawei.it.fcst.industry.pbi.vo.comb.CommonViewVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstDimInfoVO;
import com.huawei.it.fcst.util.ObjectCopyUtil;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Named;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * IctCustomService Class
 *
 * <AUTHOR>
 * @since 2024/9/13
 */
@Named("ictCustomService")
@JalorResource(code = "ictCustomService", desc = "NEW ICT-汇总组合修改操作")
public class IctCustomService {

    private static final Logger LOGGER = LoggerFactory.getLogger(IctCustomService.class);

    @Autowired
    private IDmCustomDao dmCustomDao;

    @Autowired
    private IDmCustomTempDao dmCustomTempDao;

    @Autowired
    private IctCustomCommonService ictCustomCommonService;

    private static final String ENABLE_FLAG_Y = "Y";

    private static final String ENABLE_FLAG_N = "N";

    @Autowired
    private AsyncIctCustomService asyncIctCustomService;

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void createComb(List<DmFcstDimInfoVO> customVOList, CombTransformVO combTransformVO, List<DmFcstDimInfoVO> otherCustomVOList) throws CommonApplicationException, ExecutionException, InterruptedException {
        // 插入数据
        LOGGER.info("当前ICT选择页面的长度:{},页面:{}", customVOList.size(), combTransformVO.getPageFlag());
        String costType = combTransformVO.getCostType();
        insertCustomCombRecursion(customVOList, costType, 0L, 500L);
        // 插入另一个页面的数据
        LOGGER.info("另一个ICT选择页面的长度:{},页面:{}", otherCustomVOList.size(), combTransformVO.getPageFlag());
        if (CollectionUtils.isNotEmpty(otherCustomVOList)) {
            insertCustomCombRecursion(otherCustomVOList, costType, 0L, 500L);
        }
        LOGGER.info("开始调用ICT函数刷新结果表");
        // 调用函数
        callCustomCombFunction(combTransformVO);
    }

    private void insertCustomCombRecursion(List<DmFcstDimInfoVO> customVOList, String costType, Long start, Long limit) {
        List<DmFcstDimInfoVO> customSubList =
                customVOList.stream().skip(start).limit(limit).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(customSubList)) {
            return;
        }
        dmCustomDao.createCustomList(customSubList, costType);
        insertCustomCombRecursion(customVOList, costType, start + limit, limit);
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public boolean callCustomCombFunction(CombTransformVO combTransformVO) throws CommonApplicationException, ExecutionException, InterruptedException {
        // 调用函数
        LOGGER.info("ICT函数调用开始,成本类型：{},目录树：{},组合id:{},年度版本:{},月度版本:{},页面标识:{},对比时期:{}", combTransformVO.getCostType(), combTransformVO.getGranularityType(), combTransformVO.getCustomId(), combTransformVO.getVersionId(), combTransformVO.getMonthVersionId(), combTransformVO.getPageFlag(), combTransformVO.getYtdFlag());
        String pageFlag = combTransformVO.getPageFlag();
        if (pageFlag.contains(CommonConstant.PAGE_FLAG_ALL)) {
            // 如果同步，两个函数都要调用
            Future<Boolean> customAnnualFlag = asyncIctCustomService.callCustomCombAnnual(combTransformVO);

            Future<Boolean> customMonthFlag = asyncIctCustomService.callCustomCombMonth(combTransformVO);
            while (true) {
                if (customAnnualFlag.isDone() && customMonthFlag.isDone()) {
                    break;
                }
            }
            return customAnnualFlag.get() && customMonthFlag.get();
        } else if ("ANNUAL".equals(pageFlag)) {
            Future<Boolean> customAnnualFlag = asyncIctCustomService.callCustomCombAnnual(combTransformVO);
            while (true) {
                if (customAnnualFlag.isDone()) {
                    break;
                }
            }
            return customAnnualFlag.get();
        } else {
            Future<Boolean> customMonthFlag = asyncIctCustomService.callCustomCombMonth(combTransformVO);
            while (true) {
                if (customMonthFlag.isDone()) {
                    break;
                }
            }
            return customMonthFlag.get();
        }
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updateCustomCombList(CommonViewVO commonViewVO, CombTransformVO combTransformVO) throws CommonApplicationException, ExecutionException, InterruptedException {
        Long originCustomId = commonViewVO.getCustomId();
        // 根据id获取临时表的数据
        List<DmFcstDimInfoVO> tempCustomCombList = dmCustomTempDao.getTempCustomList(commonViewVO);
        commonViewVO.setCustomVOList(tempCustomCombList);
        List<String> subEnableFlagList = tempCustomCombList.stream().map(DmFcstDimInfoVO::getSubEnableFlag).distinct().collect(Collectors.toList());
        // 原id的数据
        List<DmFcstDimInfoVO> dmCustomCombList = dmCustomDao.getCombinationCombByCustomId(commonViewVO);
        String userId = commonViewVO.getUserId();
        String userAccount = commonViewVO.getUserAccount();
        String roleId = commonViewVO.getRoleId();
        // 如果原数据就是不同步的，编辑时选择不同步，直接更新该id下的数据
        if (!commonViewVO.getPageFlag().contains(CommonConstant.PAGE_FLAG_ALL)) {
            if (!commonViewVO.getOldPageFlag().contains(CommonConstant.PAGE_FLAG_ALL)) {
                Timestamp creationDate = dmCustomCombList.get(0).getCreationDate();
                Timestamp lastUpdatedDate = dmCustomCombList.get(0).getLastUpdateDate();
                // 更新操作：直接删除，重新插入
                dmCustomDao.deleteCustomCombList(commonViewVO);
                // 判断是否全部失效
                List<DmFcstDimInfoVO> tempCurrentCustomCombList = new ArrayList<>();
                tempCustomCombList.forEach(tempComb -> {
                    DmFcstDimInfoVO comb = ObjectCopyUtil.copy(tempComb, DmFcstDimInfoVO.class);
                    comb.setCustomId(originCustomId);
                    comb.setCustomCnName(commonViewVO.getCustomCnName());
                    comb.setPageFlag(commonViewVO.getPageFlag());
                    comb.setIsSeparate(commonViewVO.getIsSeparate());
                    if ("ANNUAL".equals(commonViewVO.getPageSymbol())) {
                        comb.setYtdFlag(commonViewVO.getYtdFlag());
                    }
                    comb.setRoleId(roleId);
                    comb.setUserId(userAccount);
                    comb.setCreatedBy(userId);
                    comb.setCreationDate(creationDate);
                    comb.setLastUpdateDate(lastUpdatedDate);
                    comb.setEnableFlag(ENABLE_FLAG_Y);
                    comb.setUseFlag("PAGE");
                    if (subEnableFlagList.contains(ENABLE_FLAG_N)) {
                        comb.setEnableFlag(ENABLE_FLAG_N);
                    }
                    comb.setLastUpdatedBy(userId);
                    tempCurrentCustomCombList.add(comb);
                });
                insertCustomCombRecursion(tempCurrentCustomCombList, combTransformVO.getCostType(), 0L, 500L);
                // 插入数据组需要的lv3.5+spart数据
                insertSpartLevelCustom(commonViewVO, tempCurrentCustomCombList, originCustomId, creationDate, lastUpdatedDate, subEnableFlagList);
            } else {
                // 如果原数据就是同步的，编辑时不选择同步，那么更新两个页面的记录
                syncEditDataNoSync(commonViewVO, userId, roleId, originCustomId, subEnableFlagList, dmCustomCombList);
            }
        } else {
            // 勾选同步,但原数据不是同步的，更新原pageFlag为ALL_ANNUAL或者ALL_MONTH，新增另一个页面的ALL_数据
            // 勾选同步,原数据是同步的，更新数据即可
            editDataSync(commonViewVO, userId, roleId, originCustomId, subEnableFlagList, dmCustomCombList);
        }
        callCustomCombFunction(combTransformVO);
    }

    /**
     * 插入数据组需要的lv3.5+spart_code到数据库中
     *
     * @param commonViewVO       参数
     * @param tempCustomCombList 参数
     * @param originCustomId     参数
     * @param creationDate       参数
     * @param lastUpdatedDate    参数
     * @param subEnableFlagList  参数
     */
    private void insertSpartLevelCustom(CommonViewVO commonViewVO, List<DmFcstDimInfoVO> tempCustomCombList, Long originCustomId, Timestamp creationDate, Timestamp lastUpdatedDate, List<String> subEnableFlagList) {
        CommonViewVO commonViewParam = new CommonViewVO();
        BeanUtils.copyProperties(commonViewVO, commonViewParam);
        commonViewParam.setPageFlag(commonViewVO.getPageSymbol());
        List<DmFcstDimInfoVO> spartListForPage = ictCustomCommonService.getSpartListForPage(commonViewParam, tempCustomCombList);
        String codeAttribute;
        if ("Y".equals(commonViewVO.getMainFlag()) && StringUtils.isBlank(commonViewVO.getCodeAttributes())) {
            codeAttribute = "全选";
        } else {
            codeAttribute = commonViewVO.getCodeAttributes();
        }
        String userId = commonViewVO.getUserId();
        String userAccount = commonViewVO.getUserAccount();
        String roleId = commonViewVO.getRoleId();
        if (IndustryConstEnum.COST_TYPE.STD.getValue().equals(commonViewVO.getCostType())){
            commonViewVO.setSoftwareMark(IndustryConstEnum.SOFTWARE_MARK.HARDWARE.getValue());
        }
        List<DmFcstDimInfoVO> spartPageList = new ArrayList<>();
        spartListForPage.forEach(spartCustVO -> {
            DmFcstDimInfoVO cust = ObjectCopyUtil.copy(spartCustVO, DmFcstDimInfoVO.class);
            cust.setCustomId(originCustomId);
            cust.setCustomCnName(commonViewVO.getCustomCnName());
            cust.setBgCode(commonViewVO.getBgCode());
            cust.setBgCnName(commonViewVO.getBgCnName());
            cust.setGranularityType(commonViewVO.getGranularityType());
            if ("ANNUAL".equals(commonViewVO.getPageSymbol())) {
                cust.setYtdFlag(commonViewVO.getYtdFlag());
            }
            cust.setOverseaFlag(commonViewVO.getOverseaFlag());
            cust.setMainFlag(commonViewVO.getMainFlag());
            cust.setCodeAttributes(codeAttribute);
            cust.setRegionCode(commonViewVO.getRegionCode());
            cust.setRegionCnName(commonViewVO.getRegionCnName());
            cust.setRepofficeCode(commonViewVO.getRepofficeCode());
            cust.setRepofficeCnName(commonViewVO.getRepofficeCnName());
            cust.setSoftwareMark(commonViewVO.getSoftwareMark());
            cust.setPageFlag(commonViewVO.getPageFlag());
            cust.setIsSeparate(commonViewVO.getIsSeparate());
            cust.setUseFlag("CALC");
            cust.setRoleId(roleId);
            cust.setUserId(userAccount);
            cust.setCreatedBy(userId);
            cust.setCreationDate(creationDate);
            cust.setLastUpdateDate(lastUpdatedDate);
            cust.setEnableFlag(ENABLE_FLAG_Y);
            cust.setSubEnableFlag(ENABLE_FLAG_Y);
            if (subEnableFlagList.contains(ENABLE_FLAG_N)) {
                cust.setEnableFlag(ENABLE_FLAG_N);
            }
            cust.setLastUpdatedBy(userId);
            spartPageList.add(cust);
        });
        insertCustomCombRecursion(spartPageList, commonViewVO.getCostType(), 0L, 500L);
    }

    private void syncEditDataNoSync(CommonViewVO commonViewVO, String userId, String roleId, Long originCustomId, List<String> subEnableFlagList, List<DmFcstDimInfoVO> dmCustomCombList) {

        String otherPageFlag = CommonConstant.allCombPageFlag.get(commonViewVO.getPageFlag());
        // 如果原数据就是同步的，编辑时不选择同步，那么先更新另一个页面的page_flag即可，再更新本页面的记录，另一个页面记录和原记录id一致
        CommonViewVO commonView = new CommonViewVO();
        BeanUtils.copyProperties(commonViewVO, commonView);
        commonView.setOldPageFlag(CommonConstant.allCombPageFlag.get(commonView.getOldPageFlag()));
        commonView.setPageFlag(otherPageFlag);
        commonView.setIsSeparate("Y");
        String userAccount = commonViewVO.getUserAccount();
        dmCustomDao.updateCustomCombPageFlag(commonView);

        // 更新操作：删除，重新插入本页面的记录
        dmCustomDao.deleteCustomCombList(commonViewVO);
        List<DmFcstDimInfoVO> customList = commonViewVO.getCustomVOList();
        Timestamp creationDate;
        Timestamp lastUpdatedDate;
        if (CollectionUtils.isNotEmpty(dmCustomCombList)) {
            creationDate = dmCustomCombList.get(0).getCreationDate();
            lastUpdatedDate = dmCustomCombList.get(0).getLastUpdateDate();
        } else {
            creationDate = new Timestamp(System.currentTimeMillis());
            lastUpdatedDate = new Timestamp(System.currentTimeMillis());
        }
        List<DmFcstDimInfoVO> customVOList = new ArrayList<>();
        customList.forEach(custom -> {
            DmFcstDimInfoVO customVO = ObjectCopyUtil.copy(custom, DmFcstDimInfoVO.class);
            customVO.setCustomId(originCustomId);
            customVO.setCustomCnName(commonViewVO.getCustomCnName());
            customVO.setBgCode(commonViewVO.getBgCode());
            customVO.setBgCnName(commonViewVO.getBgCnName());
            customVO.setViewFlag(commonViewVO.getViewFlag());
            customVO.setGranularityType(commonViewVO.getGranularityType());
            customVO.setOverseaFlag(commonViewVO.getOverseaFlag());
            customVO.setUserId(userAccount);
            customVO.setRoleId(roleId);
            customVO.setCreatedBy(userId);
            customVO.setIsSeparate("Y");
            if ("ANNUAL".equals(commonViewVO.getPageSymbol())) {
                customVO.setYtdFlag(commonViewVO.getYtdFlag());
            }
            customVO.setUseFlag("PAGE");
            customVO.setPageFlag(commonViewVO.getPageFlag());
            // 是否全部失效
            customVO.setEnableFlag(ENABLE_FLAG_Y);
            if (subEnableFlagList.contains(ENABLE_FLAG_N)) {
                customVO.setEnableFlag(ENABLE_FLAG_N);
            }
            customVO.setCreationDate(creationDate);
            customVO.setLastUpdateDate(lastUpdatedDate);
            customVO.setLastUpdatedBy(userId);
            customVOList.add(customVO);
        });
        insertCustomCombRecursion(customVOList, commonViewVO.getCostType(), 0L, 500L);
        // 插入数据组需要的数据
        commonViewVO.setIsSeparate("Y");
        insertSpartLevelCustom(commonViewVO, customVOList, originCustomId, creationDate, lastUpdatedDate, subEnableFlagList);
    }

    private void editDataSync(CommonViewVO commonViewVO, String userId, String roleId, Long originCustomId, List<String> subEnableFlagList, List<DmFcstDimInfoVO> dmCustomCombList) throws CommonApplicationException {
        List<DmFcstDimInfoVO> customList = commonViewVO.getCustomVOList();
        List<DmFcstDimInfoVO> customCombList = new ArrayList<>();
        customCombList.addAll(customList);
        String otherPageFlag = CommonConstant.allCombPageFlag.get(commonViewVO.getPageFlag());
        String otherOldPageFlag = CommonConstant.allCombPageFlag.get(commonViewVO.getOldPageFlag());
        String otherPageSymbol = CommonConstant.allCombPageFlag.get(commonViewVO.getPageSymbol());
        commonViewVO.setOtherPageSymbol(otherPageSymbol);
        // 查询出来旧的年度的YTD_FLAG
        if (otherOldPageFlag.contains("ANNUAL")) {
            commonViewVO.setOtherOldPageFlag(otherOldPageFlag);
            DmFcstDimInfoVO dimInfoVO = dmCustomDao.getYtdFlagByAnnualPage(commonViewVO);
            if (null == dimInfoVO) {
                commonViewVO.setYtdFlag("N");
            } else {
                commonViewVO.setYtdFlag(dimInfoVO.getYtdFlag());
            }
        }
        // 原数据是不同步的，更新原数据的pageflag，新增另一个页面的数据
        // 更新操作：直接删除，重新插入
        dmCustomDao.deleteCustomCombList(commonViewVO);
        List<DmFcstDimInfoVO> oneCustomList = new ArrayList<>();
        pageDataInsert(commonViewVO, userId, roleId, originCustomId, subEnableFlagList, dmCustomCombList, customList, oneCustomList);
        // 筛选出与另一个页面存在的数据，进行同步
        commonViewVO.setExpandFlag("Y");
        List<DmFcstDimInfoVO> otherCustomList = ictCustomCommonService.filterAnotherPageData(commonViewVO, customCombList);
        Timestamp creationDate;
        Timestamp lastUpdatedDate;
        if (CollectionUtils.isNotEmpty(dmCustomCombList)) {
            creationDate = dmCustomCombList.get(0).getCreationDate();
            lastUpdatedDate = dmCustomCombList.get(0).getLastUpdateDate();
        } else {
            creationDate = new Timestamp(System.currentTimeMillis());
            lastUpdatedDate = new Timestamp(System.currentTimeMillis());
        }
        if (CollectionUtils.isNotEmpty(otherCustomList)) {
            // 删除另一个页面ALL_的数据，再新增；如果另一个页面本来就没有，则删除空数据
            CommonViewVO combination = new CommonViewVO();
            combination.setCostType(commonViewVO.getCostType());
            combination.setOldPageFlag(otherOldPageFlag);
            combination.setCustomId(commonViewVO.getCustomId());
            dmCustomDao.deleteCustomCombList(combination);
            List<DmFcstDimInfoVO> otherPageCustomList = new ArrayList<>();
            // 组装use_flag=PAGE的数据
            if (IndustryConstEnum.COST_TYPE.STD.getValue().equals(commonViewVO.getCostType())){
                commonViewVO.setSoftwareMark(IndustryConstEnum.SOFTWARE_MARK.HARDWARE.getValue());
            }
            otherPageCombForEdit(otherCustomList, otherPageCustomList, originCustomId, commonViewVO, otherPageFlag, creationDate, lastUpdatedDate, subEnableFlagList);
            insertCustomCombRecursion(otherPageCustomList, commonViewVO.getCostType(), 0L, 500L);
            // 插入数据需要的数据到数据库
            commonViewVO.setPageSymbol(commonViewVO.getOtherPageSymbol());
            commonViewVO.setPageFlag(otherPageFlag);
            insertSpartLevelCustom(commonViewVO, otherPageCustomList, originCustomId, creationDate, lastUpdatedDate, subEnableFlagList);
        }
    }

    private void otherPageCombForEdit(List<DmFcstDimInfoVO> otherCustomList, List<DmFcstDimInfoVO> otherPageCustomList,Long originCustomId,
                                      CommonViewVO commonViewVO, String otherPageFlag, Timestamp creationDate, Timestamp lastUpdatedDate, List<String> subEnableFlagList) {
        String userId = commonViewVO.getUserId();
        String userAccount = commonViewVO.getUserAccount();
        String roleId = commonViewVO.getRoleId();
        otherCustomList.forEach(other -> {
            DmFcstDimInfoVO comb = ObjectCopyUtil.copy(other, DmFcstDimInfoVO.class);
            comb.setCustomId(originCustomId);
            comb.setCustomCnName(commonViewVO.getCustomCnName());
            comb.setBgCode(commonViewVO.getBgCode());
            comb.setBgCnName(commonViewVO.getBgCnName());
            comb.setMainFlag(commonViewVO.getMainFlag());
            comb.setCodeAttributes(commonViewVO.getCodeAttributes());
            comb.setRegionCode(commonViewVO.getRegionCode());
            comb.setRegionCnName(commonViewVO.getRegionCnName());
            comb.setRepofficeCode(commonViewVO.getRepofficeCode());
            comb.setRepofficeCnName(commonViewVO.getRepofficeCnName());
            comb.setGranularityType(commonViewVO.getGranularityType());
            if ("ANNUAL".equals(commonViewVO.getOtherPageSymbol())) {
                comb.setYtdFlag(commonViewVO.getYtdFlag());
            } else {
                comb.setYtdFlag(null);
            }
            comb.setSoftwareMark(commonViewVO.getSoftwareMark());
            comb.setOverseaFlag(commonViewVO.getOverseaFlag());
            comb.setPageFlag(otherPageFlag);
            comb.setIsSeparate(commonViewVO.getIsSeparate());
            comb.setUseFlag("PAGE");
            comb.setUserId(userAccount);
            comb.setRoleId(roleId);
            comb.setCreatedBy(userId);
            // 判断是否全部失效或有效
            comb.setEnableFlag(ENABLE_FLAG_Y);
            if (subEnableFlagList.contains(ENABLE_FLAG_N)) {
                comb.setEnableFlag(ENABLE_FLAG_N);
            }
            comb.setCreationDate(creationDate);
            comb.setLastUpdateDate(lastUpdatedDate);
            comb.setLastUpdatedBy(userId);
            otherPageCustomList.add(comb);
        });
    }

    private void pageDataInsert(CommonViewVO commonViewVO, String userId, String roleId, Long originCustomId, List<String> subEnableFlagList,
                                List<DmFcstDimInfoVO> originCustomCombList, List<DmFcstDimInfoVO> customList, List<DmFcstDimInfoVO> oneCustomList) {
        Timestamp creationDate;
        Timestamp lastUpdatedDate;
        if (CollectionUtils.isNotEmpty(originCustomCombList)) {
            creationDate = originCustomCombList.get(0).getCreationDate();
            lastUpdatedDate = originCustomCombList.get(0).getLastUpdateDate();
        } else {
            creationDate = new Timestamp(System.currentTimeMillis());
            lastUpdatedDate = new Timestamp(System.currentTimeMillis());
        }
        customList.stream().forEach(comb -> {
            DmFcstDimInfoVO dmCustomCombVO = new DmFcstDimInfoVO();
            BeanUtils.copyProperties(comb, dmCustomCombVO);
            dmCustomCombVO.setCustomId(originCustomId);
            dmCustomCombVO.setCustomCnName(commonViewVO.getCustomCnName());
            dmCustomCombVO.setBgCode(commonViewVO.getBgCode());
            dmCustomCombVO.setBgCnName(commonViewVO.getBgCnName());
            dmCustomCombVO.setViewFlag(commonViewVO.getViewFlag());
            dmCustomCombVO.setGranularityType(commonViewVO.getGranularityType());
            dmCustomCombVO.setOverseaFlag(commonViewVO.getOverseaFlag());
            dmCustomCombVO.setPageFlag(commonViewVO.getPageFlag());
            dmCustomCombVO.setIsSeparate(commonViewVO.getIsSeparate());
            if ("ANNUAL".equals(commonViewVO.getPageSymbol())) {
                dmCustomCombVO.setYtdFlag(commonViewVO.getYtdFlag());
            }
            dmCustomCombVO.setUseFlag("PAGE");
            dmCustomCombVO.setUserId(commonViewVO.getUserAccount());
            dmCustomCombVO.setRoleId(roleId);
            dmCustomCombVO.setCreatedBy(userId);
            // 判断是否全部失效或有效
            dmCustomCombVO.setEnableFlag(ENABLE_FLAG_Y);
            if (subEnableFlagList.contains(ENABLE_FLAG_N)) {
                dmCustomCombVO.setEnableFlag(ENABLE_FLAG_N);
            }
            dmCustomCombVO.setCreationDate(creationDate);
            dmCustomCombVO.setLastUpdateDate(lastUpdatedDate);
            dmCustomCombVO.setLastUpdatedBy(userId);
            oneCustomList.add(dmCustomCombVO);
        });
        insertCustomCombRecursion(oneCustomList, commonViewVO.getCostType(), 0L, 500L);
        // 插入数据组需要的数据
        insertSpartLevelCustom(commonViewVO, oneCustomList, originCustomId, creationDate, lastUpdatedDate, subEnableFlagList);
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void asyncInitComb(List<DmFcstDimInfoVO> pspCombList, List<DmFcstDimInfoVO> stdCombList, CommonViewVO commonViewVO, CombTransformVO combTransformVO) throws InterruptedException {
        if (CollectionUtils.isNotEmpty(pspCombList)) {
            List<DmFcstDimInfoVO> annualList = pspCombList.stream().filter(custom -> custom.getPageFlag().contains("ANNUAL")).collect(Collectors.toList());
            List<DmFcstDimInfoVO> monthList = pspCombList.stream().filter(custom -> custom.getPageFlag().contains("MONTH")).collect(Collectors.toList());
            commonViewVO.setCostType(IndustryConstEnum.COST_TYPE.PSP.getValue());
            forAllPageList(annualList, monthList, commonViewVO);
        }
        if (CollectionUtils.isNotEmpty(stdCombList)) {
            List<DmFcstDimInfoVO> stdAnnualList = stdCombList.stream().filter(custom -> custom.getPageFlag().contains("ANNUAL")).collect(Collectors.toList());
            List<DmFcstDimInfoVO> stdMonthList = stdCombList.stream().filter(custom -> custom.getPageFlag().contains("MONTH")).collect(Collectors.toList());
            commonViewVO.setCostType(IndustryConstEnum.COST_TYPE.STD.getValue());
            forAllPageList(stdAnnualList, stdMonthList, commonViewVO);
        }
    }

    public void forAllPageList(List<DmFcstDimInfoVO> annualList, List<DmFcstDimInfoVO> monthList, CommonViewVO commonViewVO) throws InterruptedException {
        // 年度-通用
        Future<Boolean> annualFlag = asyncIctCustomService.initPageCondition(annualList, commonViewVO, "ANNUAL");
        // 月度-量纲
        Future<Boolean> monthFlag = asyncIctCustomService.initPageCondition(monthList, commonViewVO, "MONTH");
        while (true) {
            if (annualFlag.isDone() && monthFlag.isDone()) {
                break;
            }
        }
    }
}
