/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.enums;

/**
 * Group层级枚举类
 *
 * <AUTHOR>
 * @since 2023/3/21
 */
public enum GroupLevelEnumP {
    // Group层级（LV0：LV0、LV1：重量级团队LV1、LV2: 重量级团队LV2、CEG：专项采购认证部、MODL：模块、category：品类、item：规格品）
    LV0("LV0", "LV0"),
    LV1("LV1", "重量级团队LV1"),
    LV2("LV2", "重量级团队LV2"),
    L1("L1", "颗粒度L1"),
    L2("L2", "颗粒度L2"),
    CEG("CEG", "专项采购认证部"),
    MODL("MODL", "模块"),
    CATEGORY("CATEGORY", "品类"),
    ITEM("ITEM", "ITEM");

    private String value;
    private String name;

    GroupLevelEnumP(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据key获取对应的实例
     *
     * @param key group level
     * @return GroupLevelEnumU
     */
    public static GroupLevelEnumP getInstance(String key) {
        for (GroupLevelEnumP value : GroupLevelEnumP.values()) {
            if (value.getValue().equalsIgnoreCase(key)) {
                return value;
            }
        }
        return null;
    }
}