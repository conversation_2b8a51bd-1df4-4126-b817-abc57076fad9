/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.enums;

/**
 * Group层级枚举类
 *
 * <AUTHOR>
 * @since 2023/3/21
 */
public enum GroupLevelEnum {
    // Group层级
    LV0("LV0", "L0"),
    LV1("LV1", "重量级团队L1"),
    LV2("LV2", "重量级团队L2"),
    LV3("LV3", "重量级团队L3"),
    LV4("LV4", "重量级团队L3.5"),
    SPART("SPART", "SPART");

    private String value;
    private String name;

    GroupLevelEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public String getValue() {
        return value;
    }

    /**
     * 根据key获取对应的实例
     *
     * @param level group level
     * @return GroupLevelEnumU
     */
    public static GroupLevelEnum getInstance(String level) {
        for (GroupLevelEnum value : GroupLevelEnum.values()) {
            if (value.getValue().equalsIgnoreCase(level)) {
                return value;
            }
        }
        return null;
    }
}