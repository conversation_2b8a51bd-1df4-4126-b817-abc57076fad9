/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.service.month;

import com.huawei.it.fcst.industry.pbi.vo.common.DmFocVarifyTaskVO;
import com.huawei.it.fcst.industry.pbi.vo.month.IctMonthAnalysisVO;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.List;

/**
 * 产业成本指数（ICT）月度分析页面接口类
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Path("/ictMonthlyAnalysis")
@Produces(MediaType.APPLICATION_JSON)
public interface IIctMonthAnalysisService {

    /**
     * [查询多选下拉框列表]
     *     产业成本指数（多维度）图数据的右上角多选下拉框使用
     *
     * @param monthAnalysisVO MonthAnalysisVO
     * @return ResultDataVO
     * @throws ApplicationException
     */
    @POST
    @Path("/multi/boxList")
    ResultDataVO getMultiBoxList(IctMonthAnalysisVO monthAnalysisVO) throws ApplicationException;


    /**
     * [查询多选下拉框列表]
     *     产业成本指数（多维度）图数据的右上角多选下拉框使用
     *
     * @param monthAnalysisVO MonthAnalysisVO
     * @return ResultDataVO
     * @throws ApplicationException
     */
    @POST
    @Path("/multi/comb/boxList")
    ResultDataVO getMultiCombBoxList(IctMonthAnalysisVO monthAnalysisVO) throws ApplicationException;

    /**
     * [查询多选下拉框列表]
     *     ICT产业成本指数（多子项）图数据的右上角多选下拉框
     *
     * @param monthAnalysisVO 页面查询参数
     * @return ResultDataVO ResultDataVO
     * @throws ApplicationException
     */
    @POST
    @Path("/multi/dropdown/list")
    ResultDataVO getMultiDropdownList(IctMonthAnalysisVO monthAnalysisVO) throws ApplicationException;

    /**
     * [产业成本对比分析指数图]
     *
     * @return ResultDataVO
     * @throws InterruptedException
     */
    @POST
    @Path("/getCompareIndex/chart")
    ResultDataVO getIndustryCompareIndexChart(List<IctMonthAnalysisVO> monthAnalysisVoList) throws InterruptedException;

    /**
     * 产业成本指数（ICT）图查询
     *
     * @param monthAnalysisVO 页面查询参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/costIndex/chart")
    ResultDataVO getIndustryCostIndexChart(IctMonthAnalysisVO monthAnalysisVO) throws CommonApplicationException;

    /**
     * 产业成本指数（ICT）多子项图查询
     *
     * @param monthAnalysisVO 页面查询参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/multi/costIndex/chart")
    ResultDataVO getIndustryCostIndexMultiChart(IctMonthAnalysisVO monthAnalysisVO) throws ApplicationException;

    /**
     * 产业成本指数（ICT）同环比查询
     *
     * @param monthAnalysisVO 页面查询参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/costIndex/yoyAndpop")
    ResultDataVO getIndustryCostYoyAndPopChart(IctMonthAnalysisVO monthAnalysisVO) throws ApplicationException;

    /**
     * 成本分布图（ICT）查询
     *
     * @param monthAnalysisVO 页面查询参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/costDistribution/chart")
    ResultDataVO getCostDistributionChart(IctMonthAnalysisVO monthAnalysisVO) throws ApplicationException;

    /**
     * 产业成本指数（ICT）-权重图查询
     *
     * @param monthAnalysisVO 页面查询参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/weight/chart")
    ResultDataVO getIndustryCostWeightChart(IctMonthAnalysisVO monthAnalysisVO) throws ApplicationException;

    /**
     * 产业成本指数（ICT）-降成本目标对比查询
     *
     * @param monthAnalysisVO 页面查询参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/costTargetCompare/chart")
    ResultDataVO getCostTargetCompareChart(IctMonthAnalysisVO monthAnalysisVO) throws ApplicationException;

    /**
     * 产业成本指数（ICT）-月度分析-切换基期
     *
     * @param monthAnalysisVO 参数VO
     * @return ResultDataVO
     * @throws ApplicationException
     */
    @POST
    @Path("/index/switchBasePeriodId")
    ResultDataVO switchBasePeriodId(IctMonthAnalysisVO monthAnalysisVO) throws ApplicationException;

    /**
     * 调用函数刷新数据
     *
     * @param jsonStr json string
     * @param varifyTaskVO vo
     */
    void refreshDataByFunction(String jsonStr, DmFocVarifyTaskVO varifyTaskVO, String flag);

    /**
     * 查询涨跌根因分析
     *
     * @return ResultDataVO
     * @throws ApplicationException
     */
    @POST
    @Path("/index/amp/chart")
    ResultDataVO getCauseAmpChartList(IctMonthAnalysisVO monthAnalysisVO, @Context HttpServletRequest request) throws ApplicationException;

    /**
     * 产业成本指数（ICT）-月度分析-数据下载
     *
     * @param monthAnalysisVO 页面查询参数
     * @param response 响应
     * @return ResultDataVO ResultDataVO
     * @throws ApplicationException
     */
    @POST
    @Path("/detailData/export")
    ResultDataVO detailDataExport(IctMonthAnalysisVO monthAnalysisVO, @Context HttpServletResponse response) throws ApplicationException;

}