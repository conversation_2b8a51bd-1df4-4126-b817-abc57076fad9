/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.annual;

import com.huawei.it.fcst.industry.index.vo.common.TableNameVO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * AnnualAnalysisVO Class
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
@Getter
@Setter
@NoArgsConstructor
public class AnnualAnalysisVO  extends TableNameVO implements Serializable  {
    private static final long serialVersionUID = -1576704097209028139L;

    private List<String> parentCodeList;

    private String groupLevel;

    private String nextGroupLevel;

    private String parentLevel;

    private String multiLevel;

    private String viewFlag;

    private String teamLevel;

    private List<String> yearList;

    private int pageIndex;

    private int pageSize;

    private List<String> groupCodeList;

    private List<String> subGroupCodeList;

    private String orderColumn;

    private String orderMethod;

    private List<String> lv1ProdRdTeamCnName;

    private List<String> lv2ProdRdTeamCnName;

    private List<String> lv3ProdRdTeamCnName;

    private List<String> lv4ProdRdTeamCnName;

    private List<String> l1NameList;

    private List<String> l2NameList;

    private String lv3CegCnName;

    private String lv4CegCnName;

    private String categoryCnName;

    private String year;

    private Long versionId;

    private String fileName;

    /**
     * 重量级团队list
     */
    private List<String> teamCodeList;

    public String maxValue;

    /**
     * 颗粒度（U：通用，P：盈利，D:量纲）
     */
    private String granularityType;

    private String groupCodeOrder;

    /**
     * 业务口径（R:收入时点/C：发货成本）
     */
    private String caliberFlag;

    /**
     * ICT产业集合
     */
    private Set<String> lv0DimensionSet = new HashSet<>();

    /**
     * 重量级团队LV1集合
     */
    private Set<String> lv1DimensionSet  = new HashSet<>();

    /**
     * 重量级团队LV2集合
     */
    private Set<String> lv2DimensionSet  = new HashSet<>();

    /**
     * 会计期年份
     */
    private Long periodYear;

    /**
     * 国内/海外
     */
    private String overseaFlag;

    /**
     * BG编码
     */
    private String lv0ProdListCode;

    /**
     * 量纲维度code集合
     */
    private List<String> dmsCodeList;

    /**
     * 量纲
     */
    private  List<String> dimensionCnName;

    /**
     * 量纲子类
     */
    private List<String> dimensionSubCategoryCnName;


    /**
     * 量纲子类明细
     */
    private  List<String> dimensionSubDetailCnName;


    /**
     * spart集合
     */
    private List<String> spartCnName;

    /**
     * coa集合
     */
    private List<String> coaCnName;

    /**
     * 是否多选
     */
    private Boolean isMultipleSelect;

    private String parentCodeOrder;

    // 是否包含汇总组合
    private Boolean isContainComb;

    private String customLevel;

    // 反向视角标识
    private Boolean reverseLv1Flag;

    private Boolean reverseSymbol;

    private List<String> customIdList;

    private List<String> combinaCodeList;

    private String lv0ProdRndTeamCode;

    /**
     * 量纲code集合
     */
    private List<String> dimensionCodeList;

    /**
     * 量纲子类code集合
     */
    private List<String> dimensionSubcategoryCodeList;

    /**
     * 量纲子类明细code集合
     */
    private List<String> dimensionSubDetailCodeList;

    /**
     * spartcode集合
     */
    private List<String> spartCodeList;

    /**
     * coacode集合
     */
    private List<String> coaCodeList;



    private List<String> purCodeList;

    private String purLevel;

    /**
     * 发货对象
     */
    private String shippingObjectCode;

    private String shippingObjectCnName;

    /**
     * 制造对象
     */
    private String manufactureObjectCode;

    private String manufactureObjectCnName;

    /**
     * true  存在涨跌图、多子项涨跌图、一览表
     * false
     *
     * isShowChildContent 年度、月度  为false时不显示子项图 仅限采购制造 拥有组合和正常项一起的时候
     */
    private Boolean isShowChildContent;

    /**
     * true  存在涨跌图、多子项涨跌图、一览表
     * false  存在多子项涨跌图、一览表
     * 存在涨跌图月度isShowPriceChart  年度bisShowHistogramChart  均为false时 反向视角并且当前用户拥有最大视角是Lv3时只显示子层级图表，不显示当前层级图表
     */
    private Boolean isShowHistogramChart;

    /**
     * 只有在总成本下，且不存在子项图时，才传false，其余情况，制造成本，采购成本默认true
     */
    private Boolean isTotalChildChart;

    /**
     * 成本类型
     */
    private String costType;

    /**
     * 总成本选ALL的时候，需要选择成本类型(内部联动)
     */
    private String costSubType;

    /**
     * 汇总组合导出名称拼接
     */
    private String excelExp;

    private List<String> prodTeamCodeList;

    // 是否关联code查询
    private boolean codeFlag;

    private String l1Name;

    private String l2Name;

    // 是否包含对比分析
    private Boolean isCompareFlag;

    private String lv2ProdRndTeamCode;

    private String lv3ProdRndTeamCode;

    private String lv4ProdRndTeamCode;

    private String lv1ProdRndTeamCode;

    private List<AnnualParamVO> annualParamList;

}

