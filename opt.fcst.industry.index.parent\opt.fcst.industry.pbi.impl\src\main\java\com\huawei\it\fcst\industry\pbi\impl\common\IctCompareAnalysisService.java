/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.common;

import com.huawei.it.fcst.industry.pbi.dao.IDmFcstCustomCombDao;
import com.huawei.it.fcst.industry.pbi.enums.GroupLevelEnum;
import com.huawei.it.fcst.industry.pbi.enums.IndustryConstEnum;
import com.huawei.it.fcst.industry.pbi.impl.comb.IctCustomCombService;
import com.huawei.it.fcst.industry.pbi.service.common.IIctCompareAnalysisService;
import com.huawei.it.fcst.industry.pbi.utils.FcstIndustryUtil;
import com.huawei.it.fcst.industry.pbi.vo.comb.CommonViewVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstDimInfoVO;
import com.huawei.it.fcst.util.UserInfoUtils;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.security.RoleVO;
import com.huawei.it.jalor5.security.UserVO;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Named;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * CompareAnalysisService
 *
 * <AUTHOR>
 * @since 2023/11/21
 */
@Named("ictCompareAnalysisService")
@JalorResource(code = "ictCompareAnalysisService", desc = "NEW ICT-对比分析")
public class IctCompareAnalysisService implements IIctCompareAnalysisService {

    @Autowired
    private IDmFcstCustomCombDao dmFcstCustomCombDao;
    @Autowired
    private IctCustomCombService ictCustomCombService;

    @Override
    @JalorOperation(code = "allDimensionList", desc = "年度月度对比分析树")
    public ResultDataVO getDimensionList(CommonViewVO commonViewVO) throws CommonApplicationException {
        List<DmFcstDimInfoVO> allDimensionList = new ArrayList<>();
        // 获取组合维度 (只获取组合项，不看组合的子项,去掉失效的组合)
        List<DmFcstDimInfoVO> customCombVOList = new ArrayList<>();
        boolean needCombFlag = StringUtils.isEmpty(commonViewVO.getLv0CnName())
                || StringUtils.isNotEmpty(commonViewVO.getFilterGroupLevel())
                || StringUtils.isNotEmpty(commonViewVO.getKeyword());
        boolean isHisttoryVersion = commonViewVO.getIsHistoryVersion() != null && commonViewVO.getIsHistoryVersion();
        if (StringUtils.isBlank(commonViewVO.getGroupLevel())) {
            if (IndustryConstEnum.PAGE_TYPE.ANNUAL.getValue().equals(commonViewVO.getPageFlag())){
                // 非历史版本才需要添加组合项
                if (!isHisttoryVersion && (needCombFlag)) {
                    customCombVOList = getCombDimensionTree(commonViewVO);
                }
            } else {
                if (needCombFlag) {
                    customCombVOList = getCombDimensionTree(commonViewVO);
                }
            }
        }
        // 获取正常维度树
        ResultDataVO getDimensionTree = ictCustomCombService.getIctProdRndTeamTree(commonViewVO);
        List<DmFcstDimInfoVO> data = (List<DmFcstDimInfoVO>) getDimensionTree.getData();
        allDimensionList.addAll(data);
        allDimensionList.addAll(customCombVOList);
        return ResultDataVO.success(allDimensionList);
    }

    @NotNull
    private List<DmFcstDimInfoVO> getCombDimensionTree(CommonViewVO commonViewVO) {
        // 获取组合维度 (只获取组合项，不看组合的子项,去掉失效的组合)
        CommonViewVO combViewVO = new CommonViewVO();
        BeanUtils.copyProperties(commonViewVO, combViewVO);
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        combViewVO.setRoleId(String.valueOf(currentRole.getRoleId()));
        combViewVO.setUserAccount(currentUser.getUserAccount());
        combViewVO.setGroupLevel("LV0");
        // 另存页面查询所有组合，年度月度页面只查询有效的组合
        if (!"Y".equals(commonViewVO.getIsSavePage())) {
            combViewVO.setEnableFlag("Y");
        }
        List<DmFcstDimInfoVO> dmCustomCombVOList = new ArrayList<>();
        // 递归循环获取所有层级汇总组合
        recursionCustomCombList(combViewVO, dmCustomCombVOList);
        // 汇总组合的返回值处理
        dmCustomCombVOList = dmCustomCombVOList.stream().map(customCombVO -> {
            DmFcstDimInfoVO dmFcstDimInfoVO = new DmFcstDimInfoVO();
            dmFcstDimInfoVO.setGroupCode(customCombVO.getCombId() + "");
            dmFcstDimInfoVO.setConnectCode(customCombVO.getCombId() + "");
            dmFcstDimInfoVO.setCombId(customCombVO.getCombId());
            dmFcstDimInfoVO.setGroupCode(customCombVO.getGroupCode());
            dmFcstDimInfoVO.setGroupCnName(customCombVO.getGroupCnName());
            dmFcstDimInfoVO.setEnableFlag(customCombVO.getEnableFlag());
            dmFcstDimInfoVO.setGroupLevel(customCombVO.getGroupLevel());
            dmFcstDimInfoVO.setIsCombination(true);
            return dmFcstDimInfoVO;
        }).collect(Collectors.toList());
        return dmCustomCombVOList;
    }

    private void recursionCustomCombList(CommonViewVO combViewVO, List<DmFcstDimInfoVO> dmCustomCombVOList) {
        Map<String, String> nextGroupLevelMap = FcstIndustryUtil.getNextGroupLevel(combViewVO);
        String nextGroupLevel = nextGroupLevelMap.get("nextGroupLevel");
        combViewVO.setNextGroupLevel(nextGroupLevel);
        // 各层级的汇总组合
        dmCustomCombVOList.addAll(dmFcstCustomCombDao.getCombinationParent(combViewVO));
        combViewVO.setGroupLevel(nextGroupLevel);
        if (GroupLevelEnum.LV4.getValue().equals(nextGroupLevel) || StringUtils.isNotEmpty(combViewVO.getFilterGroupLevel()) || StringUtils.isNotEmpty(combViewVO.getKeyword())) {
            return;
        }
        recursionCustomCombList(combViewVO, dmCustomCombVOList);
    }

}
