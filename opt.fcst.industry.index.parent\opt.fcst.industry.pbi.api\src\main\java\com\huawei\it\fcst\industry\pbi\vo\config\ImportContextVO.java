/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.config;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.huawei.it.fcst.industry.pbi.vo.common.DmFcstDataRefreshStatus;
import com.huawei.it.fcst.vo.UploadInfoVO;
import com.huawei.it.jalor5.core.request.IRequestContext;
import lombok.Builder;
import lombok.Data;

/**
 * ImportContextVO Class
 *
 * <AUTHOR>
 * @since 2024-09-29
 */
@Data
@Builder
public class ImportContextVO {

    private Long versionId;

    private Long newVersionId;

    private Long userId;

    private String userCn;

    private int batchNum;

    private int totalNum;

    private String errorMsg;

    private ExcelWriter workbookWriter;

    private WriteSheet writeSheet;

    private IRequestContext context;

    private UploadInfoVO uploadInfoVO;

    private DmFcstDataRefreshStatus dataRefreshStatus;
}