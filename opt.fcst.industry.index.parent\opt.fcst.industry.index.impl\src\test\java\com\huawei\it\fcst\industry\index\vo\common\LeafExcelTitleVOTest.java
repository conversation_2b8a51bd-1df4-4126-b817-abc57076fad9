/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.common;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.apache.poi.ss.usermodel.CellType;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mock;

/**
 * LeafExcelTitleVOTest Class
 *
 * <AUTHOR>
 * @since 2023/4/22
 */
public class LeafExcelTitleVOTest extends BaseVOCoverUtilsTest<LeafExcelTitleVO> {

    @Mock
    private CellType dataType;

    @Override
    protected Class<LeafExcelTitleVO> getTClass() {
        return LeafExcelTitleVO.class;
    }

    @Test
    public void testMethod() {
        LeafExcelTitleVO dmFocActualCostVO = new LeafExcelTitleVO();
        dmFocActualCostVO.setDataFormatStr("str");
        dmFocActualCostVO.getDataFormatStr();
        dmFocActualCostVO.setColumnCode("code");
        dmFocActualCostVO.setDataKey("key");
        dmFocActualCostVO.setDataType(dataType);
        String value = "value";
        Integer width = 10;
        Boolean isSelected = true;
        String dataKey = "value";
        String columnCode = "value";
        CellType dataType = CellType.STRING;
        String dataFormatStr = "11";
        new LeafExcelTitleVO(value, width, isSelected, dataKey, columnCode,
                dataType, dataFormatStr);
        new LeafExcelTitleVO(value, width, isSelected, dataKey, columnCode,
                dataType, dataFormatStr, true);
        new LeafExcelTitleVO(value, width, isSelected, dataKey, columnCode,
                dataType);
        new LeafExcelTitleVO(value, width, dataKey, columnCode,
                dataType, dataFormatStr);
        new LeafExcelTitleVO(value, width, dataKey, columnCode,
                dataType);
        Assert.assertNotNull(dmFocActualCostVO);
    }
}