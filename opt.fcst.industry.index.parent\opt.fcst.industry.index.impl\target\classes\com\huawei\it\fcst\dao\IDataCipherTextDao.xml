<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IDataCipherTextDao">
    <resultMap type="com.huawei.it.fcst.industry.index.vo.ciphertext.CipherTextDataVO" id="resultMap">
        <result property="primaryId" column="primary_id" />
        <result property="periodId" column="period_id" />
        <result property="rmbCostAmt" column="rmb_cost_amt" />
        <result property="rmbItemUnitCostAmt" column="rmb_item_unit_cost_amt" />
        <result property="rmbFactRateGcAmt" column="rmb_fact_rate_gc_amt" />
    </resultMap>

    <resultMap type="com.huawei.it.fcst.industry.index.vo.ciphertext.VarifyTaskVO" id="resultTaskMap">
        <result property="taskId" column="task_id" />
        <result property="status" column="status" />
        <result property="periodId" column="period_id" />
        <result property="taskType" column="task_type" />
        <result property="combStatus" column="comb_status" />
    </resultMap>

    <select id="getDataListStream" resultMap="resultMap" fetchSize="10000">
        <if test='cipherTextDataVO.sourceTableName == "DWL_PROD_BOM_ITEM_SHIP_DIM_I"'>
            select primary_id,period_id,rmb_cost_amt,rmb_fact_rate_gc_amt from
            fin_dm_opt_foi.DWL_PROD_BOM_ITEM_SHIP_DIM_I
        </if>
        <if test='cipherTextDataVO.sourceTableName == "DWL_PROD_BOM_ITEM_REV_DETAIL_I"'>
            select primary_id,period_id,rmb_item_unit_cost_amt as rmb_cost_amt,rmb_fact_rate_gc_amt from
            fin_dm_opt_foi.DWL_PROD_BOM_ITEM_REV_DETAIL_I
        </if>
        <if test='cipherTextDataVO.sourceTableName == "DWL_PROD_PROD_UNIT_KMS_I"'>
            select primary_id,period_id,rmb_fact_rate_amt as rmb_cost_amt,usd_fact_rate_amt as rmb_fact_rate_gc_amt from
            fin_dm_opt_foi.DWL_PROD_PROD_UNIT_KMS_I
        </if>
        <if test='cipherTextDataVO.periodId != null and cipherTextDataVO.periodId != "" '>
            WHERE period_id = #{cipherTextDataVO.periodId}
        </if>
    </select>

    <delete id="delByPeriodId">
        delete from
        <if test='cipherTextDataVO.targetTableName == "DM_FOC_JAVA_PRIMARY_ENCRYPT_T"'>
            fin_dm_opt_foi.DM_FOC_JAVA_PRIMARY_ENCRYPT_T
        </if>
        <if test='cipherTextDataVO.targetTableName == "DM_FOC_REVENUE_JAVA_PRIMARY_ENCRYPT_T"'>
            fin_dm_opt_foi.DM_FOC_REVENUE_JAVA_PRIMARY_ENCRYPT_T
        </if>
        where period_id = #{cipherTextDataVO.periodId}
    </delete>

    <insert id="saveData">
        INSERT INTO
        <if test='targetTableName == "DM_FOC_JAVA_PRIMARY_ENCRYPT_T"'>
            fin_dm_opt_foi.DM_FOC_JAVA_PRIMARY_ENCRYPT_T
            (primary_id,period_id,rmb_cost_amt,rmb_fact_rate_gc_amt,CREATION_DATE,LAST_UPDATE_DATE)
            VALUES
            <foreach collection="list" item="item" separator=",">
                (
                #{item.primaryId,jdbcType=VARCHAR},
                #{item.periodId,jdbcType=VARCHAR},
                gs_encrypt(#{item.rmbCostAmt,jdbcType=VARCHAR},#{keyStr}, 'aes128', 'cbc', 'sha256'),
                gs_encrypt(#{item.rmbFactRateGcAmt,jdbcType=VARCHAR},#{keyStr}, 'aes128', 'cbc', 'sha256'),
                NOW(),
                NOW()
                )</foreach>
        </if>
        <if test='targetTableName == "DM_FOC_REVENUE_JAVA_PRIMARY_ENCRYPT_T"'>
            fin_dm_opt_foi.DM_FOC_REVENUE_JAVA_PRIMARY_ENCRYPT_T
            (primary_id,period_id,rmb_item_unit_cost_amt,rmb_fact_rate_gc_amt,CREATION_DATE,LAST_UPDATE_DATE)
            VALUES
            <foreach collection="list" item="item" separator=",">
                (
                #{item.primaryId,jdbcType=VARCHAR},
                #{item.periodId,jdbcType=VARCHAR},
                gs_encrypt(#{item.rmbCostAmt,jdbcType=VARCHAR},#{keyStr}, 'aes128', 'cbc', 'sha256'),
                gs_encrypt(#{item.rmbFactRateGcAmt,jdbcType=VARCHAR},#{keyStr}, 'aes128', 'cbc', 'sha256'),
                #{item.creationDate,jdbcType=TIMESTAMP},
                NOW()
                )</foreach>
        </if>
        <if test='targetTableName == "DM_FCST_JAVA_PRIMARY_ENCRYPT_T"'>
            fin_dm_opt_foi.DM_FCST_JAVA_PRIMARY_ENCRYPT_T
            (primary_id,period_id,rmb_fact_rate_amt,usd_fact_rate_amt,CREATION_DATE,LAST_UPDATE_DATE)
            VALUES
            <foreach collection="list" item="item" separator=",">
                (
                #{item.primaryId,jdbcType=VARCHAR},
                #{item.periodId,jdbcType=VARCHAR},
                gs_encrypt(#{item.rmbCostAmt,jdbcType=VARCHAR},#{keyStr}, 'aes128', 'cbc', 'sha256'),
                gs_encrypt(#{item.rmbFactRateGcAmt,jdbcType=VARCHAR},#{keyStr}, 'aes128', 'cbc', 'sha256'),
                #{item.creationDate,jdbcType=TIMESTAMP},
                NOW()
                )</foreach>
        </if>
    </insert>

    <select id="startFunctionTask" statementType="CALLABLE">
        <if test='funcName == "F_DM_FOC_DIFF_VIEW_DIM"'>
            {call fin_dm_opt_foi.F_DM_FOC_DIFF_VIEW_DIM(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_MID_MONTH_ITEM"'>
            {call fin_dm_opt_foi.F_DM_FOC_MID_MONTH_ITEM(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_period_id,mode= IN,jdbcType=NUMERIC},
            #{f_caliber,mode= IN,jdbcType=VARCHAR},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_MID_MONTH_CATE"'>
            {call fin_dm_opt_foi.F_DM_FOC_MID_MONTH_CATE(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_view_flag,mode= IN,jdbcType=VARCHAR},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_ITEM_DTL_DECODE"'>
            {call fin_dm_opt_foi.F_DM_FOC_ITEM_DTL_DECODE(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_view_flag,mode= IN,jdbcType=VARCHAR},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_TOP_CATE_INFO"'>
            {call fin_dm_opt_foi.F_DM_FOC_TOP_CATE_INFO(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_ITEM_ACTUAL_APPEND"'>
            {call fin_dm_opt_foi.F_DM_FOC_ITEM_ACTUAL_APPEND(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_TOP_ITEM_INFO"'>
            {call fin_dm_opt_foi.F_DM_FOC_TOP_ITEM_INFO(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_ITEM_FCST_APPEND"'>
            {call fin_dm_opt_foi.F_DM_FOC_ITEM_FCST_APPEND(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_ONLY_TOP_DELETE"'>
            {call fin_dm_opt_foi.F_DM_FOC_ONLY_TOP_DELETE(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_MID_MONTH_IDX"'>
            {call fin_dm_opt_foi.F_DM_FOC_MID_MONTH_IDX(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>

        <if test='funcName == "F_DM_FOC_ACTUAL_COST"'>
            {call fin_dm_opt_foi.F_DM_FOC_ACTUAL_COST(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_MONTH_WEIGHT"'>
            {call fin_dm_opt_foi.F_DM_FOC_MONTH_WEIGHT(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_MONTH_COST_IDX"'>
            {call fin_dm_opt_foi.F_DM_FOC_MONTH_COST_IDX(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_MONTH_RATE"'>
            {call fin_dm_opt_foi.F_DM_FOC_MONTH_RATE(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_VIEW_ANNL_COST_T"'>
            {call fin_dm_opt_foi.F_DM_FOC_VIEW_ANNL_COST_T(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_cost_type,mode= IN,jdbcType=VARCHAR},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_view_flag,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_VIEW_ANNL_COST_T_0613"'>
            {call fin_dm_opt_foi.F_DM_FOC_VIEW_ANNL_COST_T_0613(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_cost_type,mode= IN,jdbcType=VARCHAR},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_view_flag,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_ANNUAL_WEIGHT_T"'>
            {call fin_dm_opt_foi.F_DM_FOC_ANNUAL_WEIGHT_T(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_cost_type,mode= IN,jdbcType=VARCHAR},
            #{f_lev_num,mode= IN,jdbcType=NUMERIC},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_ANNUAL_WEIGHT_T_0613"'>
            {call fin_dm_opt_foi.F_DM_FOC_ANNUAL_WEIGHT_T_0613(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_cost_type,mode= IN,jdbcType=VARCHAR},
            #{f_lev_num,mode= IN,jdbcType=NUMERIC},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_view_flag,mode= IN,jdbcType=NUMERIC},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_ANNUAL_AMP_T"'>
            {call fin_dm_opt_foi.F_DM_FOC_ANNUAL_AMP_T(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_cost_type,mode= IN,jdbcType=VARCHAR},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_DATA_ENCRYPT"'>
            {call fin_dm_opt_foi.F_DM_FOC_DATA_ENCRYPT(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_caliber,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_period_id,mode= IN,jdbcType=NUMERIC},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_VIEW_ANNUAL_STATUS"'>
            {call fin_dm_opt_foi.F_DM_VIEW_ANNUAL_STATUS(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_cost_type,mode= IN,jdbcType=VARCHAR},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_REVENUE_ITEM_SHIP_DTL"'>
            {call fin_dm_opt_foi.F_DM_FOC_REVENUE_ITEM_SHIP_DTL(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_year,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_ITEM_DTL_INNER"'>
            {call fin_dm_opt_foi.F_DM_FOC_ITEM_DTL_INNER(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_year,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_REVERSES_TOP_CATE_INFO"'>
            {call fin_dm_opt_foi.F_DM_FOC_REVERSES_TOP_CATE_INFO(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_REVERSES_TOP_ITEM_INFO"'>
            {call fin_dm_opt_foi.F_DM_FOC_REVERSES_TOP_ITEM_INFO(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_REVERSE_ANNUAL_WEIGHT"'>
            {call fin_dm_opt_foi.f_dm_foc_reverse_annual_weight(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_version_id,mode= IN,jdbcType=NUMERIC},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_MID_MONTH_ITEM_FLAG"'>
            {call fin_dm_opt_foi.f_dm_foc_mid_month_item_flag(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_caliber,mode= IN,jdbcType=VARCHAR},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_CUS_VIEW_ANNL_COST_T"'>
            {call fin_dm_opt_foi.f_dm_foc_cus_view_annl_cost_t(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_custom_id,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_CUS_ITEM_DTL_DECODE"'>
            {call fin_dm_opt_foi.f_dm_foc_cus_item_dtl_decode(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_custom_id,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_CUS_ITEM_APPEND"'>
            {call fin_dm_opt_foi.f_dm_foc_cus_item_append(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_custom_id,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_MID_MONTH_CATE_DMS"'>
            {call fin_dm_opt_foi.F_DM_FOC_MID_MONTH_CATE_DMS(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_view_flag,mode= IN,jdbcType=NUMERIC},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_TOP_CATE_INFO_DMS"'>
            {call fin_dm_opt_foi.F_DM_FOC_TOP_CATE_INFO_DMS(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_view_flag,mode= IN,jdbcType=NUMERIC},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_TOP_ITEM_INFO_DMS"'>
            {call fin_dm_opt_foi.F_DM_FOC_TOP_ITEM_INFO_DMS(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_view_flag,mode= IN,jdbcType=NUMERIC},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_ONLY_TOP_DELETE_DMS"'>
            {call fin_dm_opt_foi.F_DM_FOC_ONLY_TOP_DELETE_DMS(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_view_flag,mode= IN,jdbcType=NUMERIC},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_DIFF_VIEW_DIM_DMS"'>
            {call fin_dm_opt_foi.F_DM_FOC_DIFF_VIEW_DIM_DMS(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_view_flag,mode= IN,jdbcType=NUMERIC},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_MONTH_WEIGHT_DMS"'>
            {call fin_dm_opt_foi.F_DM_FOC_MONTH_WEIGHT_DMS(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_view_flag,mode= IN,jdbcType=NUMERIC},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_item_version,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_ACTUAL_COST_DMS"'>
            {call fin_dm_opt_foi.F_DM_FOC_ACTUAL_COST_DMS(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_view_flag,mode= IN,jdbcType=NUMERIC},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_item_version,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_ITEM_DTL_DECODE_DMS"'>
            {call fin_dm_opt_foi.F_DM_FOC_ITEM_DTL_DECODE_DMS(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_view_flag,mode= IN,jdbcType=NUMERIC},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_ITEM_ACTUAL_APPEND_DMS"'>
            {call fin_dm_opt_foi.F_DM_FOC_ITEM_ACTUAL_APPEND_DMS(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_view_flag,mode= IN,jdbcType=NUMERIC},
            #{f_caliber,mode= IN,jdbcType=VARCHAR},
            #{f_oversea_flag,mode= IN,jdbcType=VARCHAR},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_ITEM_FCST_APPEND_DMS"'>
            {call fin_dm_opt_foi.F_DM_FOC_ITEM_FCST_APPEND_DMS(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_view_flag,mode= IN,jdbcType=NUMERIC},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_MONTH_CUSTOM_COMB"'>
            {call fin_dm_opt_foi.f_dm_foc_month_custom_comb(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_item_version,mode= IN,jdbcType=VARCHAR},
            #{f_custom_id,mode= IN,jdbcType=NUMERIC},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_MID_MONTH_IDX_DMS"'>
            {call fin_dm_opt_foi.f_dm_foc_mid_month_idx_dms(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_caliber,mode= IN,jdbcType=VARCHAR},
            #{f_view_flag,mode= IN,jdbcType=VARCHAR},
            #{f_oversea_flag,mode= IN,jdbcType=VARCHAR},
            #{f_item_version,mode= IN,jdbcType=NUMERIC},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_VIEW_ANNL_COST_D"'>
            {call fin_dm_opt_foi.f_dm_foc_view_annl_cost_d(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_ANNUAL_WEIGHT_T_D"'>
            {call fin_dm_opt_foi.f_dm_foc_annual_weight_t_d(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_lev_num,mode= IN,jdbcType=NUMERIC},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_version_id,mode= IN,jdbcType=NUMERIC},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_ANNUAL_ITEM_AMP_T"'>
            {call fin_dm_opt_foi.f_dm_foc_annual_item_amp_t(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_view_flag,mode= IN,jdbcType=VARCHAR},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_version_id,mode= IN,jdbcType=NUMERIC},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_ANNUAL_AMP_T_D"'>
            {call fin_dm_opt_foi.f_dm_foc_annual_amp_t_d(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_lev_num,mode= IN,jdbcType=NUMERIC},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_version_id,mode= IN,jdbcType=NUMERIC},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_VIEW_ANNL_COST_INSERT"'>
            {call fin_dm_opt_foi.f_dm_foc_view_annl_cost_insert(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_view_flag,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_CUSTOM_ANNUAL"'>
            {call fin_dm_opt_foi.f_dm_foc_custom_annual(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_version_id,mode= IN,jdbcType=NUMERIC},
            #{f_custom_id,mode= IN,jdbcType=NUMERIC},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_MID_MONTH_ITEM_FIX"'>
            {call fin_dm_opt_foi.F_DM_FOC_MID_MONTH_ITEM_FIX(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_period_id,mode= IN,jdbcType=NUMERIC},
            #{f_caliber,mode= IN,jdbcType=VARCHAR},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_MID_MONTH_ITEM_DMS"'>
            {call fin_dm_opt_foi.F_DM_FOC_MID_MONTH_ITEM_DMS(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_period_id,mode= IN,jdbcType=NUMERIC},
            #{f_caliber,mode= IN,jdbcType=VARCHAR},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_DMS_ITEM_APPEND_COMBINE"'>
            {call fin_dm_opt_foi.F_DM_FOC_DMS_ITEM_APPEND_COMBINE(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_caliber,mode= IN,jdbcType=VARCHAR},
            #{f_oversea_flag,mode= IN,jdbcType=VARCHAR},
            #{f_lv0_prod_list_code,mode= IN,jdbcType=VARCHAR},
            #{f_view_flag,mode= IN,jdbcType=VARCHAR},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FOC_ITEM_APPEND_COMBINE"'>
            {call fin_dm_opt_foi.F_DM_FOC_ITEM_APPEND_COMBINE(
            #{f_industry_flag,mode= IN,jdbcType=VARCHAR},
            #{f_dimension,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FCST_DATA_ENCRYPT"'>
            {call fin_dm_opt_foi.F_DM_FCST_DATA_ENCRYPT(
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_period_id,mode= IN,jdbcType=NUMERIC},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FCST_STD_PROD_UNIT_T"'>
            {call fin_dm_opt_foi.F_DM_FCST_STD_PROD_UNIT_T(
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_year,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FCST_MID_MONTH_SPART"'>
            {call fin_dm_opt_foi.F_DM_FCST_MID_MONTH_SPART(
            #{f_year,mode= IN,jdbcType=VARCHAR},
            #{f_cost_type,mode= IN,jdbcType=VARCHAR},
            #{f_granularity_type,mode= IN,jdbcType=VARCHAR},
            #{f_view_flag,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FCST_SPART_ACTUAL_APPEND"'>
            {call fin_dm_opt_foi.F_DM_FCST_SPART_ACTUAL_APPEND(
            #{f_cost_type,mode= IN,jdbcType=VARCHAR},
            #{f_granularity_type,mode= IN,jdbcType=VARCHAR},
            #{f_view_flag,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FCST_SPART_DTL_DECODE"'>
            {call fin_dm_opt_foi.F_DM_FCST_SPART_DTL_DECODE(
            #{f_cost_type,mode= IN,jdbcType=VARCHAR},
            #{f_granularity_type,mode= IN,jdbcType=VARCHAR},
            #{f_view_flag,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FCST_TOP_SPART_INFO"'>
            {call fin_dm_opt_foi.F_DM_FCST_TOP_SPART_INFO(
            #{f_cost_type,mode= IN,jdbcType=VARCHAR},
            #{f_granularity_type,mode= IN,jdbcType=VARCHAR},
            #{f_view_flag,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FCST_MON_COST_AMT"'>
            {call fin_dm_opt_foi.F_DM_FCST_MON_COST_AMT(
            #{f_cost_type,mode= IN,jdbcType=VARCHAR},
            #{f_granularity_type,mode= IN,jdbcType=VARCHAR},
            #{f_view_flag,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_customization_id,mode= IN,jdbcType=NUMERIC},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FCST_ICT_SUM_DETAIL_SPART_T"'>
            {call fin_dm_opt_foi.F_DM_FCST_ICT_SUM_DETAIL_SPART_T(
            #{f_cost_type,mode= IN,jdbcType=VARCHAR},
            #{f_granularity_type,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_version_id,mode= IN,jdbcType=NUMERIC},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FCST_ICT_MON_MID_COST_IDX_T"'>
            {call fin_dm_opt_foi.F_DM_FCST_ICT_MON_MID_COST_IDX_T(
            #{f_cost_type,mode= IN,jdbcType=VARCHAR},
            #{f_granularity_type,mode= IN,jdbcType=VARCHAR},
            #{f_page_type,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_version_id,mode= IN,jdbcType=NUMERIC},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FCST_ICT_MON_MID_COST_RED_IDX_T"'>
            {call fin_dm_opt_foi.F_DM_FCST_ICT_MON_MID_COST_RED_IDX_T(
            #{f_cost_type,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_version_id,mode= IN,jdbcType=NUMERIC},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FCST_ICT_MON_WEIGHT_T"'>
            {call fin_dm_opt_foi.F_DM_FCST_ICT_MON_WEIGHT_T(
            #{f_cost_type,mode= IN,jdbcType=VARCHAR},
            #{f_granularity_type,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_version_id,mode= IN,jdbcType=NUMERIC},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_FCST_ICT_PRE_BASE_CUS_MON_RESULT_T"'>
            {call fin_dm_opt_foi.F_FCST_ICT_PRE_BASE_CUS_MON_RESULT_T(
            #{f_cost_type,mode= IN,jdbcType=VARCHAR},
            #{f_granularity_type,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_version_id,mode= IN,jdbcType=NUMERIC},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FCST_BASE_CUS_ANNL"'>
            {call fin_dm_opt_foi.F_DM_FCST_BASE_CUS_ANNL(
            #{f_cost_type,mode= IN,jdbcType=VARCHAR},
            #{f_granularity_type,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_version_id,mode= IN,jdbcType=NUMERIC},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FCST_MID_ANNL_AVG"'>
            {call fin_dm_opt_foi.F_DM_FCST_MID_ANNL_AVG(
            #{f_cost_type,mode= IN,jdbcType=VARCHAR},
            #{f_granularity_type,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_version_id,mode= IN,jdbcType=NUMERIC},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FCST_DIM_INFO"'>
            {call fin_dm_opt_foi.F_DM_FCST_DIM_INFO(
            #{f_cost_type,mode= IN,jdbcType=VARCHAR},
            #{f_granularity_type,mode= IN,jdbcType=VARCHAR},
            #{f_version_id,mode= IN,jdbcType=NUMERIC},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FCST_ANNL_WEIGHT"'>
            {call fin_dm_opt_foi.F_DM_FCST_ANNL_WEIGHT(
            #{f_cost_type,mode= IN,jdbcType=VARCHAR},
            #{f_granularity_type,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_version_id,mode= IN,jdbcType=NUMERIC},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FCST_ANNL_AMP"'>
            {call fin_dm_opt_foi.F_DM_FCST_ANNL_AMP(
            #{f_cost_type,mode= IN,jdbcType=VARCHAR},
            #{f_granularity_type,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_version_id,mode= IN,jdbcType=NUMERIC},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FCST_ANNL_STATUS"'>
            {call fin_dm_opt_foi.F_DM_FCST_ANNL_STATUS(
            #{f_cost_type,mode= IN,jdbcType=VARCHAR},
            #{f_granularity_type,mode= IN,jdbcType=VARCHAR},
            #{f_version_id,mode= IN,jdbcType=NUMERIC},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FCST_ICT_MON_REPL_COST_INFO_T"'>
            {call fin_dm_opt_foi.F_DM_FCST_ICT_MON_REPL_COST_INFO_T(
            #{f_cost_type,mode= IN,jdbcType=VARCHAR},
            #{f_granularity_type,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_version_id,mode= IN,jdbcType=NUMERIC},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FCST_ICT_YTD_REPL_COST_INFO_T"'>
            {call fin_dm_opt_foi.F_DM_FCST_ICT_YTD_REPL_COST_INFO_T(
            #{f_cost_type,mode= IN,jdbcType=VARCHAR},
            #{f_granularity_type,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_version_id,mode= IN,jdbcType=NUMERIC},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FCST_MID_MONTH_SPART_ENTIRE"'>
            {call fin_dm_opt_foi.F_DM_FCST_MID_MONTH_SPART_ENTIRE(
            #{f_year,mode= IN,jdbcType=VARCHAR},
            #{f_cost_type,mode= IN,jdbcType=VARCHAR},
            #{f_granularity_type,mode= IN,jdbcType=VARCHAR},
            #{f_view_flag,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FCST_SPART_ACTUAL_APPEND_ENTIRE"'>
            {call fin_dm_opt_foi.F_DM_FCST_SPART_ACTUAL_APPEND_ENTIRE(
            #{f_cost_type,mode= IN,jdbcType=VARCHAR},
            #{f_granularity_type,mode= IN,jdbcType=VARCHAR},
            #{f_view_flag,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FCST_SPART_DTL_DECODE_ENTIRE"'>
            {call fin_dm_opt_foi.F_DM_FCST_SPART_DTL_DECODE_ENTIRE(
            #{f_cost_type,mode= IN,jdbcType=VARCHAR},
            #{f_granularity_type,mode= IN,jdbcType=VARCHAR},
            #{f_view_flag,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FCST_ANNL_COST"'>
            {call fin_dm_opt_foi.F_DM_FCST_ANNL_COST(
            #{f_cost_type,mode= IN,jdbcType=VARCHAR},
            #{f_granularity_type,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_version_id,mode= IN,jdbcType=NUMERIC},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
        <if test='funcName == "F_DM_FCST_ICT_MON_YTD_AVG_T"'>
            {call fin_dm_opt_foi.F_DM_FCST_ICT_MON_YTD_AVG_T(
            #{f_cost_type,mode= IN,jdbcType=VARCHAR},
            #{f_granularity_type,mode= IN,jdbcType=VARCHAR},
            #{f_keystr,mode= IN,jdbcType=VARCHAR},
            #{f_version_id,mode= IN,jdbcType=NUMERIC},
            #{X_RESULT_STATUS,mode=OUT,jdbcType=VARCHAR}
            )}
        </if>
    </select>

    <select id="countVarifyTask" resultType="int">
        select count(1) from
            fin_dm_opt_foi.dm_foc_varify_task_t
        where status = 'PROCESSING' and task_type = 'data_cipher'
    </select>


    <select id="searchVerifyTask" resultMap="resultTaskMap">
        SELECT task_id,period_id,status,del_flag,task_type,comb_status,creation_date,last_update_date
        FROM fin_dm_opt_foi.dm_foc_varify_task_t
        WHERE task_id = #{varifyTaskVO.taskId}
        AND task_type =#{varifyTaskVO.taskType}
    </select>

    <select id="getVerifyTaskId" resultType="java.lang.Long" flushCache="true" useCache="false">
        select fin_dm_opt_foi.dm_foc_varify_task_s.NEXTVAL
    </select>

    <insert id="insertVerifyTask">
        insert into fin_dm_opt_foi.dm_foc_varify_task_t(task_id,status,period_id,creation_date,del_flag,task_type,comb_status)
        values (#{taskId},#{status},#{periodId},now(),'N',#{taskType},#{combStatus})
    </insert>

    <update id="updateVerifyTask">
        update fin_dm_opt_foi.dm_foc_varify_task_t set
        <if test='status != null'>
            status =#{status},
        </if>
        <if test='combStatus != null'>
            comb_status =#{combStatus},
        </if>
        last_update_date =#{lastUpdateDate}
        where task_id = #{taskId}
    </update>

</mapper>
