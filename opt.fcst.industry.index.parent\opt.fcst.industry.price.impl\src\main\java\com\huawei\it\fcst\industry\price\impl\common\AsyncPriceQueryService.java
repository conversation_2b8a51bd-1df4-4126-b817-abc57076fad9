/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.impl.common;

import com.huawei.it.fcst.industry.price.dao.IDmRawDataExamineDao;
import com.huawei.it.fcst.industry.price.vo.config.ExamineVO;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.scheduling.annotation.EnableAsync;

import javax.inject.Named;
import java.util.concurrent.Future;

/**
 * AsyncExportService Class
 *
 * <AUTHOR>
 * @since 2023/03/15
 */
@Slf4j
@EnableAsync
@Named(value = "asyncPriceQueryService")
public class AsyncPriceQueryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncPriceQueryService.class);

    @Autowired
    private IDmRawDataExamineDao dmRawDataExamineDao;

    /**
     * 查询spart，合同号
     *
     * @param examineVO 参数VO
     */
    public void findSpartContract(ExamineVO examineVO) {
        log.info(">>>Begin asyncPriceQueryService::findSpartContract");
        Future<Long> spartCont = findSpartCont(examineVO);
        Future<Long> contractNumberCont = findContractNumberCont(examineVO);
        try {
            examineVO.setSpartCont(spartCont.get());
            examineVO.setContractNumberCont(contractNumberCont.get());
        } catch (Exception ex) {
            log.error(">>>>>An exception occurs with asyncPriceQueryService::findSpartContract");
            log.error(ex.getMessage());
        }
    }

    @Async("ictAsyncServiceExecutor")
    public Future<Long> findSpartCont(ExamineVO examineVO) {
        log.info(">>>Begin asyncPriceQueryService::findSpartCont");
        Long spartCodeCount = dmRawDataExamineDao.findSpartDataExamineCount(examineVO);
        return new AsyncResult<>(spartCodeCount);
    }

    @Async("ictAsyncServiceExecutor")
    public Future<Long> findContractNumberCont(ExamineVO examineVO) {
        log.info(">>>Begin asyncPriceQueryService::findContractNumberCont");
        Long contractCount = dmRawDataExamineDao.findContractExamineCount(examineVO);
        return new AsyncResult<>(contractCount);
    }
}