/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.impl.scheduler;

import com.huawei.it.fcst.enums.GroupTaskStatus;
import com.huawei.it.fcst.enums.GroupTaskType;
import com.huawei.it.fcst.enums.MessageName;
import com.huawei.it.fcst.enums.SubModuleName;
import com.huawei.it.fcst.industry.price.dao.IDmFcstPriceBaseCusDimDao;
import com.huawei.it.fcst.industry.price.service.scheduler.IPricePBILtsProcessService;
import com.huawei.it.fcst.industry.price.vo.drop.DmFcstBasePriceCusDimVO;
import com.huawei.it.fcst.task.ITaskProcessService;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.async.AsyncMessage;
import com.huawei.it.jalor5.async.IMessageSender;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.core.util.CollectionUtil;

import lombok.extern.slf4j.Slf4j;

import org.ehcache.impl.internal.concurrent.ConcurrentHashMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.inject.Inject;

/**
 * pbi 虚化任务lts 触发接口
 *
 * <AUTHOR>
 * @since 202407
 */
@Slf4j
@Service("pricePBILtsProcessService")
@JalorResource(code = "pricePBILtsProcessService", desc = "定价指数-lts触发虚化任务")
public class PricePBILtsProcessService implements IPricePBILtsProcessService {

    @Autowired
    private IMessageSender messageSender;

    @Inject
    private IDmFcstPriceBaseCusDimDao dmFcstBaseCusDimDao;

    private Map<String, ITaskProcessService> taskProcessService = new ConcurrentHashMap<>();

    /**
     * 构造器初始化，获取指定bean
     *
     * @param list 所有实现类
     */
    @Autowired
    public PricePBILtsProcessService(List<ITaskProcessService> list) {
        list.forEach(item -> {
            if (SubModuleName.PRICE.equals(item.getSubModuleName())) {
                GroupTaskType taskTypeEnum = (GroupTaskType) item.getTaskType();
                taskProcessService.put(taskTypeEnum.name(), item);
            }
        });
    }

    @Override
    @JalorOperation(code = "processTriggerTask", desc = "执行虚化任务")
    @Audit(module = "pricePBILtsProcessService-processTriggerTask", operation = "processTriggerTask", message = "执行虚化任务")
    public ResultDataVO processTriggerTask(Map<String, Object> param) throws ApplicationException {
        // 获取需要执行任务信息
        List<DmFcstBasePriceCusDimVO> taskInfoList = dmFcstBaseCusDimDao.getNeedTaskList();
        if (CollectionUtil.isNullOrEmpty(taskInfoList)) {
            log.info("Failed to obtain the information about the task to be executed.");
            // 处理任务调度处理异常不能结束的runing任务
            updateExceptionTaskStatus();
            log.info("processTriggerTask empty end 1 .");
            return ResultDataVO.success("1");
        }
        // 设置任务执行状态，剔除没有实现的类型
        for (DmFcstBasePriceCusDimVO vo : taskInfoList) {
            if (taskProcessService.get(vo.getPageType()) == null) {
                vo.setStatusFlag(GroupTaskStatus.FAIL.getCode());
                log.error("The implementation class of ICallFuncTaskProcessService is not found.");
            }
            vo.setStatusFlag(GroupTaskStatus.ING.getCode());
        }
        // 更新当前任务状态ing
        dmFcstBaseCusDimDao.updateStatusFlag(taskInfoList);
        List<DmFcstBasePriceCusDimVO> ingList = taskInfoList.stream()
                .filter(item -> GroupTaskStatus.ING.getCode().equalsIgnoreCase(item.getStatusFlag()))
                .collect(Collectors.toList());
        // 定义任务消息
        AsyncMessage asyncMessage = null;
        for (DmFcstBasePriceCusDimVO dmFcstBaseCusDimVO : ingList) {
            asyncMessage = new AsyncMessage(MessageName.OPT_INDUSTRY_MESSAGE_TASK.name());
            asyncMessage.setContext(ITaskProcessService.class,
                    taskProcessService.get(dmFcstBaseCusDimVO.getPageType()));
            asyncMessage.setContent(dmFcstBaseCusDimVO);
            messageSender.send(asyncMessage);
        }
        log.info("processTriggerTask end.");
        return ResultDataVO.success(ingList);
    }

    /**
     * 处理超1小时没有执行完的任务设置失败
     */
    private void updateExceptionTaskStatus() {
        List<DmFcstBasePriceCusDimVO> exceptionTaskList = dmFcstBaseCusDimDao.getExceptionTaskList();
        if (CollectionUtil.isNullOrEmpty(exceptionTaskList)) {
            return;
        }
        // 设置任务执行中
        for (DmFcstBasePriceCusDimVO vo : exceptionTaskList) {
            vo.setStatusFlag(GroupTaskStatus.FAIL.getCode());
        }
        dmFcstBaseCusDimDao.updateStatusFlag(exceptionTaskList);
    }

}
