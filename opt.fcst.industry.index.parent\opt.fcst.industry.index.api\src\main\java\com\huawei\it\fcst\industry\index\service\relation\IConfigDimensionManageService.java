/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.service.relation;

import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.relation.DimensionInputVO;
import com.huawei.it.fcst.industry.index.vo.relation.DmDimCatgModlCegIctVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.cxf.jaxrs.ext.multipart.Multipart;

import java.io.IOException;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.Consumes;
import javax.ws.rs.FormParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;

/**
 * IConfigDimensionManageService Class
 *
 * <AUTHOR>
 * @since 2023/3/15
 */
@Path("/configManage")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface IConfigDimensionManageService {
    /**
     * 新增或者编辑映射维表
     *
     * @param dimensionInputVO 参数
     * @return 结果
     * @throws CommonApplicationException 异常
     */
    @Path("/relationSave")
    @POST
    ResultDataVO relationSave(DimensionInputVO dimensionInputVO) throws CommonApplicationException;

    @POST
    @Path("/relationImport")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    ResultDataVO relationImport(@Multipart("files") Attachment attachment, @FormParam("versionId") Long versionId,@FormParam("industryOrg") String industryOrg) throws CommonApplicationException, IOException;

    @POST
    @Path("/relationExport")
    ResultDataVO exportRelationList(DmDimCatgModlCegIctVO dmDimCatgModlCegIctVO, @Context HttpServletResponse response) throws CommonApplicationException;
}
