<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IDmFocNoticeInfoDao">
    <resultMap type="com.huawei.it.fcst.industry.index.vo.config.DmFocNoticeInfoDTO" id="resultMap">
        <result property="id" column="id"/>
        <result property="noticeType" column="notice_type"/>
        <result property="noticeTheme" column="notice_theme"/>
        <result property="noticeTitle" column="notice_title"/>
        <result property="noticeContent" column="notice_content"/>
        <result property="noticeTag" column="notice_tag"/>
        <result property="fileSourceKey" column="file_source_key"/>
        <result property="fileType" column="file_type"/>
        <result property="createdBy" column="created_by"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="isHistory" column="is_history"/>
    </resultMap>

    <sql id="allFields">
        id,
        notice_type,
        notice_theme,
        notice_title,
        notice_tag,
        notice_content,
        file_source_key,
        file_type,
        del_flag,
        is_history,
        created_by,
        creation_date,
        last_updated_by,
        last_update_date
    </sql>

    <sql id="allValue">
        #{id,jdbcType=NUMERIC},
        #{noticeType,jdbcType=VARCHAR},
        #{noticeTheme,jdbcType=VARCHAR},
        #{noticeTitle,jdbcType=VARCHAR},
        #{noticeTag,jdbcType=VARCHAR},
        #{noticeContent,jdbcType=VARCHAR},
        #{fileSourceKey,jdbcType=VARCHAR},
        #{fileType,jdbcType=VARCHAR},
        #{delFlag,jdbcType=VARCHAR},
        #{isHistory,jdbcType=VARCHAR},
        #{createdBy,jdbcType=VARCHAR},
        NOW(),
        #{lastUpdatedBy,jdbcType=VARCHAR},
        NOW()
    </sql>

    <sql id="searchFields">
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
             del_flag = 'N'
            <if test='_parameter.get("0").id != null'>
                AND id = #{0.id,jdbcType=NUMERIC}
            </if>
            <if test='_parameter.get("0").noticeType != null and _parameter.get("0").noticeType !=""'>
                AND notice_type = #{0.noticeType,jdbcType=VARCHAR}
            </if>
            <if test='_parameter.get("0").noticeTheme != null and _parameter.get("0").noticeTheme !=""'>
                AND notice_theme = #{0.noticeTheme,jdbcType=VARCHAR}
            </if>
            <if test='_parameter.get("0").noticeTitle != null and _parameter.get("0").noticeTitle !=""'>
                AND notice_title = #{0.noticeTitle,jdbcType=VARCHAR}
            </if>
            <if test='_parameter.get("0").noticeTag != null and _parameter.get("0").noticeTag !=""'>
                AND notice_tag = #{0.noticeTag,jdbcType=VARCHAR}
            </if>
            <if test='_parameter.get("0").noticeContent != null and _parameter.get("0").noticeContent !=""'>
                AND notice_content = #{0.noticeContent,jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <select id="findByPage" resultMap="resultMap">
        SELECT
        <include refid="allFields"/>
        FROM fin_dm_opt_foi.dm_foc_notice_info_t
        <include refid="searchFields"/>
        order by creation_date desc
        LIMIT #{1.pageSize} OFFSET  #{1.startIndex}-1
    </select>

    <select id="findByPageCount" resultType="int">
        SELECT COUNT(1) from
        (
        SELECT
        <include refid="allFields"/>
        FROM fin_dm_opt_foi.dm_foc_notice_info_t
        <include refid="searchFields"/>
            )
    </select>

    <select id="findNoticeInfoById" resultMap="resultMap">
        SELECT
        <include refid="allFields"/>
        FROM fin_dm_opt_foi.dm_foc_notice_info_t
        where  id = #{id,jdbcType=NUMERIC}
    </select>

    <select id="findNoticeByType" resultMap="resultMap">
        with new_notice as(
            select max(creation_date)creation_date,
                   notice_type
            from fin_dm_opt_foi.dm_foc_notice_info_t
           where del_flag = 'N' and (is_history = 'N' or is_history is null)
         <if test='noticeType != null and noticeType.size() > 0'>
           <foreach collection='noticeType' item="code" open="AND notice_type IN (" close=")" index="index"
                    separator=",">
               #{code}
           </foreach>
          </if>
        group by notice_type)
        select t2.id,
               t2.notice_type,
               t2.notice_theme,
               t2.notice_title,
               t2.notice_tag,
               t2.notice_content,
               t2.file_source_key,
               t2.file_type,
               t2.del_flag,
               t2.created_by,
               t2.creation_date,
               t2.last_updated_by,
               t2.last_update_date
        from new_notice t1,fin_dm_opt_foi.dm_foc_notice_info_t t2 where t1.creation_date = t2.creation_date and
            t1.notice_type = t2.notice_type
    </select>

    <select id="getNoticeKey" resultType="java.lang.Long">
        SELECT nextval('fin_dm_opt_foi.dm_foc_notice_info_s')
    </select>

    <insert id="createDmFocNoticeInfo" parameterType="com.huawei.it.fcst.industry.index.vo.config.DmFocNoticeInfoDTO">
        INSERT INTO fin_dm_opt_foi.dm_foc_notice_info_t
        (<include refid="allFields"/>)
        VALUES
        (<include refid="allValue"/>)
    </insert>

    <update id="updateDmFocNoticeInfo" parameterType="com.huawei.it.fcst.industry.index.vo.config.DmFocNoticeInfoDTO">
        UPDATE fin_dm_opt_foi.dm_foc_notice_info_t
        set del_flag = 'Y'
        WHERE id =#{id,jdbcType=NUMERIC}
    </update>
</mapper>
