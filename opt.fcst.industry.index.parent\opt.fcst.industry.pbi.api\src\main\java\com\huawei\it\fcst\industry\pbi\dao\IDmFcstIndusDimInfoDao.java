/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.dao;

import com.huawei.it.fcst.industry.pbi.vo.common.CommonBaseVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstDimInfoVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * IDmFocViewInfoDao
 *
 * <AUTHOR>
 * @since 2023/3/14
 */
public interface IDmFcstIndusDimInfoDao {

    List<DmFcstDimInfoVO> baseIndusDimInfoList(@Param("searchVO")CommonBaseVO commonViewVO);

    PagedResult<DmFcstDimInfoVO> baseIndusSpartDimInfoList(@Param("searchVO") CommonBaseVO commonViewVO, @Param("pageVO") PageVO pageVO);

    List<DmFcstDimInfoVO> mainFlagDimInfoList(@Param("searchVO")CommonBaseVO commonViewVO);

    PagedResult<DmFcstDimInfoVO> mainFlagSpartDimInfoList(@Param("searchVO") CommonBaseVO commonViewVO, @Param("pageVO") PageVO pageVO);

    List<DmFcstDimInfoVO> mainFlagAnnualDimInfoList(@Param("searchVO")CommonBaseVO commonViewVO);

    PagedResult<DmFcstDimInfoVO> mainFlagAnnualSpartDimInfoList(@Param("searchVO") CommonBaseVO commonViewVO, @Param("pageVO") PageVO pageVO);

    Integer getIndusSpartOrDimensionNum(CommonBaseVO commonViewVO);

    List<DmFcstDimInfoVO> getLv4CodeWithSpartOrDimension(CommonBaseVO commonViewVO);

    List<DmFcstDimInfoVO> findCostGapSpartCodeList(CommonBaseVO commonBaseVO);

    List<DmFcstDimInfoVO> getLv1AndLv2CodeList(CommonBaseVO commonViewVO);

    List<DmFcstDimInfoVO> reverseFindLv1Code(CommonBaseVO commonViewVO);

    List<DmFcstDimInfoVO> reverseFindLv1CodeMonth(CommonBaseVO commonViewVO);

}
