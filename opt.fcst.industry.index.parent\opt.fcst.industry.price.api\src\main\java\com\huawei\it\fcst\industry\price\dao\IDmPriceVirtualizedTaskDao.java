/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.dao;

import com.huawei.it.fcst.industry.price.vo.drop.DmFcstBasePriceCusDimVO;
import org.apache.ibatis.annotations.Param;

/**
 * IDmFocViewInfoDao
 *
 * <AUTHOR>
 * @since 2024/7/23
 */
public interface IDmPriceVirtualizedTaskDao {

    /**
     * 执行年度虚化任务
     *
     * @param dmFcstBaseCusDimVO 执行参数
     * @return
     */
    String callAnnualFuncTask(@Param("query") DmFcstBasePriceCusDimVO dmFcstBaseCusDimVO);

    /**
     * 执行月度权重虚化任务
     *
     * @param dmFcstBaseCusDimVO 执行参数
     * @return
     */
    String callMonthWeightFuncTask(@Param("query")DmFcstBasePriceCusDimVO dmFcstBaseCusDimVO);

    /**
     * 执行月度指数虚化任务
     *
     * @param dmFcstBaseCusDimVO 执行参数
     * @return
     */
    String callMonthCostIdxFuncTask(@Param("query")DmFcstBasePriceCusDimVO dmFcstBaseCusDimVO);

    /**
     * 执行月度成本虚化任务
     *
     * @param dmFcstBaseCusDimVO 执行参数
     * @return
     */
    String callMonthRateFuncTask(@Param("query")DmFcstBasePriceCusDimVO dmFcstBaseCusDimVO);

}
