/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.annual;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * DmFocAnnualAmpVO Class
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
// 此@data注解不可以被替换成getter和setter
@Data
@NoArgsConstructor
public class DmFocAnnualAmpVO {
    private int id;

    private Long versionId;

    private String lv0ProdRndTeamCnName;

    private String lv0ProdRndTeamCode;

    private String spartCode;

    private String spartCnName;

    private String periodYear;

    private String prodRndTeamCode;

    private String prodRndTeamCnName;

    private String lv2ProdRndTeamCnName;

    private String lv2ProdRndTeamCode;

    private String lv1ProdRndTeamCnName;

    private String lv1ProdRndTeamCode;

    private String lv4ProdRndTeamCnName;

    private String lv4ProdRndTeamCode;

    private String lv3ProdRndTeamCnName;

    private String lv3ProdRndTeamCode;

    private String groupCode;

    private String groupCnName;

    private String groupLevel;

    private String parentCode;

    private String parentCnName;

    private String parentLevel;

    private String annualAmp;

    private String delFlag;

    private String viewFlag;

    private String appendFlag;

    private String weightRate;

    private String createdBy;

    private Timestamp creationDate;

    private String lastUpdatedBy;

    private Timestamp lastUpdateDdate;

    private String groupCodeAndName;

    // 提示信息
    private String hoverMsg;

    // 哪年补齐
    private String appendYear;


    // 组合 customName
    private String customCnName;

    //  组合 customCode
    private Long customId;

    private Long combId;

    private String combCnName;

    private String weightAnnualAmpPercent;

    private String weightAnnualAmpPercentOrder;

    private String statusCode;

    /**
     * 量纲颗粒度code
     **/
    private String dimensionCode;

    /**
     * 量纲颗粒度名称
     **/
    private String dimensionCnName;

    /**
     * 量纲颗粒度code
     **/
    private String dimensionSubDetailCode;

    /**
     * 量纲颗粒度名称
     **/
    private String dimensionSubDetailCnName;

    /**
     * 量纲颗粒度code
     **/
    private String dimensionSubCategoryCode;

    /**
     * 量纲颗粒度名称
     **/
    private String dimensionSubCategoryCnName;

    /**
     * 成本金额
     **/
    private String rmbCostAmt;

    /**
     * 成本类型（采购:P；制造:M）
     **/
    private String costType;

}
