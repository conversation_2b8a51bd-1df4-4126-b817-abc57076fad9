/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.common;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * ExportExcelVoTest Class
 *
 * <AUTHOR>
 * @since 2023/4/22
 */
public class ExportExcelVoTest extends BaseVOCoverUtilsTest<ExportExcelVo> {

    @Override
    protected Class<ExportExcelVo> getTClass() {
        return ExportExcelVo.class;
    }

    @Test
    public void testMethod() {
        ExportExcelVo exportExcelVo = new ExportExcelVo();
        List<AbstractExcelTitleVO> list = new ArrayList<>();
        exportExcelVo.builder().fourTitleList(list).threeTitleList(list).selectedLeafExcelTitleVO(list).titleRowCount(1).build();
        Assert.assertNotNull(exportExcelVo);
    }
}