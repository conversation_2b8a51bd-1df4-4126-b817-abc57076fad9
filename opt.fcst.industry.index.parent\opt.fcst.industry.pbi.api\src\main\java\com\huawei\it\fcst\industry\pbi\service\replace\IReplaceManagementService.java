/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.service.replace;

import com.huawei.it.fcst.industry.pbi.vo.replace.ReplaceSearchVO;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.cxf.jaxrs.ext.multipart.Multipart;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.Consumes;
import javax.ws.rs.FormParam;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;

/**
 * IReplaceService Class
 *
 * <AUTHOR>
 * @since 2024/7/10
 */
@Path("/replaceManagement")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface IReplaceManagementService {

    /**
     * 根据版本获取下拉框
     *
     * @param replaceSearchVO 参数
     * @return 结果
     */
    @Path("/dropDownList")
    @POST
    ResultDataVO replDropDownList(ReplaceSearchVO replaceSearchVO) throws CommonApplicationException;

    /**
     * 编辑时，下拉框
     *
     * @param replaceSearchVO 参数
     * @return 结果
     */
    @Path("/list")
    @POST
    ResultDataVO productDimList(ReplaceSearchVO replaceSearchVO) throws CommonApplicationException;

    /**
     * 系统时间
     *
     * @return 系统时间
     */
    @GET
    @Path("/getSystemTime")
    ResultDataVO getSystemTime();

    /**
     * 获取版本下拉框
     *
     * @return 结果
     */
    @Path("/version/list")
    @POST
    ResultDataVO findVersionList() throws CommonApplicationException;

    /**
     * 新旧编码，分页查询列表
     *
     * @param replaceSearchVO 参数
     * @return 结果
     */
    @Path("/infoList")
    @POST
    ResultDataVO findInfoPageList(ReplaceSearchVO replaceSearchVO);

    /**
     * 新旧编码，新增，或者编辑操作
     *
     * @param replaceSearchVO 参数
     * @return 结果
     */
    @Path("/info/update")
    @POST
    ResultDataVO updateReplInfoList(ReplaceSearchVO replaceSearchVO) throws CommonApplicationException;

    /**
     * 新旧编码，删除
     *
     * @param replaceSearchVO 参数
     * @return 结果
     */
    @Path("/info/delete")
    @POST
    ResultDataVO deleteReplInfoList(ReplaceSearchVO replaceSearchVO) throws CommonApplicationException;

    @Path("/info/export")
    @POST
    ResultDataVO replinfoExport(@Context HttpServletResponse response, ReplaceSearchVO replaceSearchVO) throws ApplicationException;

    /**
     * [导入新旧编码替换记录]
     *
     * @param versionId
     * @return ResultDataVO
     */
    @POST
    @Path("/info/import")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    ResultDataVO importReplaceInfoList(@Multipart("files") Attachment attachment, @FormParam("versionId") Long versionId) throws Exception;

}
