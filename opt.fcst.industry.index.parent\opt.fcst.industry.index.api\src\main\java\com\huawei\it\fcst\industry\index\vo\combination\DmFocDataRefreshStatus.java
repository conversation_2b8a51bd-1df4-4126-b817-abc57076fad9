/*
 *
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.industry.index.vo.combination;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * DmFocDataRefreshStatus Class
 *
 * <AUTHOR>
 * @since 2023/8/10
 */
@NoArgsConstructor
@Setter
@Getter
public class DmFocDataRefreshStatus implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 删除标识(未删除：N，已删除：Y)
     **/
    private String delFlag;

    /**
     * 修改人
     **/
    private Long lastUpdatedBy;

    /**
     * 创建时间
     **/
    private Date creationDate;

    /**
     * 创建人
     **/
    private Long createdBy;

    /**
     * 任务id
     **/
    private Long taskId;

    /**
     * 任务状态
     **/
    private String status;

    /**
     * 修改时间
     **/
    private Date lastUpdateDate;

    private int roleId;

    // INIT初始化的任务，COMB汇总组合的所有操作
    private String taskFlag;

    private Long userId;
}
