/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.impl.common;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.huawei.it.fcst.constant.Constant;
import com.huawei.it.fcst.dao.IFcstDataRefreshStatusDao;
import com.huawei.it.fcst.industry.price.dao.IDmFcstPriceDimInfoDao;
import com.huawei.it.fcst.industry.price.dao.IDmFcstPriceVersionInfoDao;
import com.huawei.it.fcst.industry.price.dao.IPriceMonthCostIdxDao;
import com.huawei.it.fcst.industry.price.service.common.IPriceCommonService;
import com.huawei.it.fcst.industry.price.utils.FcstIndustryUtil;
import com.huawei.it.fcst.industry.price.vo.common.CommonPriceBaseVO;
import com.huawei.it.fcst.industry.price.vo.month.PriceMonthAnalysisVO;
import com.huawei.it.fcst.industry.price.vo.version.DmFcstVersionInfoVO;
import com.huawei.it.fcst.util.UserInfoUtils;
import com.huawei.it.fcst.vo.DmFcstDataRefreshStatus;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Inject;
import javax.inject.Named;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * IctCommonService Class
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Slf4j
@Named("priceCommonService")
@JalorResource(code = "priceCommonService", desc = "定价指数-公共服务")
public class PriceCommonService implements IPriceCommonService {

    private static final Pattern PATTERN = Pattern.compile("[0-9]*");

    @Autowired
    private IDmFcstPriceVersionInfoDao versionInfoDao;

    @Autowired
    private IDmFcstPriceDimInfoDao dmFcstDimInfoDao;

    @Autowired
    private IFcstDataRefreshStatusDao dataRefreshStatusDao;

    @Inject
    private IPriceMonthCostIdxDao priceMonthCostIdxDao;

    @Override
    public Long getVersionId(String dataType) {
        log.info("Begin PriceCommonService::getVersionId and dataType={}", dataType);
        DmFcstVersionInfoVO dmFocVersionVO = versionInfoDao.findVersionIdByDataType(dataType);
        dmFocVersionVO = Optional.ofNullable(dmFocVersionVO).orElse(new DmFcstVersionInfoVO());
        return dmFocVersionVO.getVersionId();
    }

    @Override
    public Long createNewVersionInfo(String dataType) {
        DmFcstVersionInfoVO versionInfoVO = DmFcstVersionInfoVO.builder()
                .status(0L)
                .isRunning("N")
                .versionType("ADJUST")
                .dataType(dataType)
                .versionId(versionInfoDao.getVersionKey())
                .version(getVerionName(dataType))
                .build();
        versionInfoVO.setCreatedBy(UserInfoUtils.getUserId());
        versionInfoVO.setLastUpdatedBy(UserInfoUtils.getUserId());
        versionInfoDao.createDmFcstVersionInfoDTO(versionInfoVO);
        return versionInfoVO.getVersionId();
    }

    /**
     * 根据数据类型升序生成版本名称
     *
     * @param dataType 数据类型
     * @return String version name
     */
    private String getVerionName(String dataType) {
        // 查询今天的版本信息
        String todayStr = DateUtil.today().replace("-", "");
        DmFcstVersionInfoVO build = DmFcstVersionInfoVO.builder()
                .dataType(dataType).version(todayStr).build();
        List<DmFcstVersionInfoVO> versionList = versionInfoDao.findVersionListByVerName(build);
        if (CollectionUtils.isEmpty(versionList) || StringUtils.isBlank(versionList.get(0).getVersion())) {
            return todayStr.concat("-001");
        }
        return getVersionNameSub(versionList.get(0).getVersion(), todayStr);
    }

    /**
     * 根据传入版本名称升序生成新的版本名称
     *
     * @param inputVersionName 版本名称 形如 20240712-001
     * @param todayStr          当天日期字符串 yyyyMMdd
     * @return String version name
     */
    private String getVersionNameSub(String inputVersionName, String todayStr) {
        if (inputVersionName.contains("-")) {
            String versionNumber = inputVersionName.substring(inputVersionName.indexOf("-") + 1);
            if (PATTERN.matcher(versionNumber).matches()) {
                if (NumberUtil.parseInt(versionNumber) < 9) {
                    return todayStr.concat("-00") + (NumberUtil.parseInt(versionNumber) + 1);
                } else if (NumberUtil.parseInt(versionNumber) < 99) {
                    return todayStr.concat("-0") + (NumberUtil.parseInt(versionNumber) + 1);
                } else {
                    return todayStr.concat("-") + (NumberUtil.parseInt(versionNumber) + 1);
                }
            } else {
                return todayStr.concat("-001");
            }
        }
        return todayStr.concat("-001");
    }

    @Override
    public void setTitleDisplayName(CommonPriceBaseVO commonBaseVO) {
        String displayName = commonBaseVO.getBgCnName();
        // 名称拼接
        String l1CnName = getCompleteName(commonBaseVO.getLv1ProdListCnNameList(), displayName, "L1");
        String l2CnName = getCompleteName(commonBaseVO.getLv2ProdListCnNameList(), l1CnName, "L2");
        String l3CnName = getCompleteName(commonBaseVO.getLv3ProdListCnNameList(), l2CnName, "L3");
        String connectName = getCompleteName(commonBaseVO.getLv4ProdListCnNameList(), l3CnName, "L3.5");
        String name = getCompleteName(commonBaseVO.getSpartCodeList(), connectName, "SPART");
        commonBaseVO.setDisplayName(name);
    }

    private String getCompleteName(List<String> prodRdTeamCnNameList, String displayName, String groupLevel) {
        if (CollectionUtils.isNotEmpty(prodRdTeamCnNameList)) {
            if (prodRdTeamCnNameList.size() == 1) {
                displayName = displayName + "-" + prodRdTeamCnNameList.get(0);
            } else {
                String prodRdTeamCnNameStr = prodRdTeamCnNameList.stream().collect(Collectors.joining(","));
                displayName = displayName + "-多" + groupLevel + "(" + prodRdTeamCnNameStr + ")";
            }
        }
        return displayName;
    }

    @Override
    @JalorOperation(code = "findActualPeriodId", desc = "查询切换基期实际数的开始和结束时间")
    public ResultDataVO findActualPeriodId() {
        log.info("==>Begin PriceCommonService#findActualPeriodId");
        PriceMonthAnalysisVO monthAnalysisVO = new PriceMonthAnalysisVO();
        String actualMonth = priceMonthCostIdxDao.findActualMonth();
        if (StringUtils.isNotBlank(actualMonth)) {
            FcstIndustryUtil.handlePeriod(monthAnalysisVO, actualMonth);
        }
        return ResultDataVO.success(monthAnalysisVO);
    }

    @Override
    @JalorOperation(code = "findRefreshTime", desc = "查询系统刷新时间")
    public ResultDataVO findRefreshTime() {
        log.info("==>Begin PriceCommonService#findRefreshTime");
        return ResultDataVO.success(dmFcstDimInfoDao.findRefreshTime());
    }

    @Override
    public DmFcstDataRefreshStatus saveDataRefreshStatus(String taskFlag) throws ApplicationException {
        DmFcstDataRefreshStatus dataRefreshStatus = new DmFcstDataRefreshStatus();
        dataRefreshStatus.setTaskId(dataRefreshStatusDao.getDataRefrashKey());
        dataRefreshStatus.setStatus(Constant.TaskStatus.INIT.getValue());
        dataRefreshStatus.setDelFlag("N");
        dataRefreshStatus.setTaskFlag(taskFlag);
        dataRefreshStatus.setUserId(UserInfoUtils.getUserId());
        dataRefreshStatus.setCreatedBy(UserInfoUtils.getUserId());
        dataRefreshStatus.setCreationDate(new Date());
        dataRefreshStatus.setLastUpdatedBy(UserInfoUtils.getUserId());
        dataRefreshStatus.setLastUpdateDate(new Date());
        dataRefreshStatusDao.createDmFcstDataRefreshStatus(dataRefreshStatus);
        return dataRefreshStatus;
    }

}