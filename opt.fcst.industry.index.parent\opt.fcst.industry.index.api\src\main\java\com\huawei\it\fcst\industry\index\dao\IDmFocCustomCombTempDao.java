/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.combination.CombinationVO;
import com.huawei.it.fcst.industry.index.vo.combination.DmCustomCombVO;
import com.huawei.it.fcst.industry.index.vo.common.CommonViewVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * IDmFocCustomCombTempDao Class
 *
 * <AUTHOR>
 * @since 2024/4/9
 */
public interface IDmFocCustomCombTempDao {

    void createTempManufactureCustomcombList(List<DmCustomCombVO> customVOList);

    void createTempEnergyManufactureCustomcombList(List<DmCustomCombVO> customVOList);

    void createTempCustomCombList(List<DmCustomCombVO> customSubList);

    void createTempEnergyCustomCombList(List<DmCustomCombVO> customSubList);

    void createTempIasCustomCombList(List<DmCustomCombVO> customSubList);

    void createTempIasManufactureCustomcombList(List<DmCustomCombVO> customVOList);

    void createTempCustomCombByCustomId(CombinationVO combinationVO);

    void createMadeTempCustomCombByCustomId(CombinationVO combinationVO);

    void createTempEnergyCustomCombByCustomId(CombinationVO combinationVO);

    void createMadeTempEnergyCustomCombByCustomId(CombinationVO combinationVO);

    List<DmCustomCombVO> getTempCustomCombList(CommonViewVO commonViewVO);

    List<DmCustomCombVO> getTempManufactureCustomCombList(CommonViewVO commonViewVO);

    List<DmCustomCombVO> getTempTableCustomCombList(CombinationVO combinationVO);

    List<DmCustomCombVO> getTempTableManufactureCustomCombList(CombinationVO combinationVO);

    Integer getCountCustomComb(CombinationVO combinationVO);

    Integer getCountManufactureCustomComb(CombinationVO combinationVO);

    List<String> getTempParentCustomCombList(CommonViewVO commonViewVO);

    List<String> getTempParentManufactureCustomCombList(CommonViewVO commonViewVO);

    void deleteCustomTemp(CombinationVO combinationVO);

    void deleteManufacutreCustomTemp(CombinationVO combinationVO);

    void deleteCustomTempByConnectCode(@Param("combinationVO") CombinationVO combinationVO, @Param("currentCustomList") List<DmCustomCombVO> currentCustomList);

    void deleteManufacutreCustomTempByConnectCode(@Param("combinationVO") CombinationVO combinationVO, @Param("currentCustomList") List<DmCustomCombVO> currentCustomList);

    void updateChageSelectFlagList(@Param("combinationVO")CombinationVO combinationVO,@Param("list")List<DmCustomCombVO> changeCustomVOList);

    void updateMadeChageSelectFlagList(@Param("combinationVO")CombinationVO combinationVO,@Param("list")List<DmCustomCombVO> changeCustomVOList);

}
