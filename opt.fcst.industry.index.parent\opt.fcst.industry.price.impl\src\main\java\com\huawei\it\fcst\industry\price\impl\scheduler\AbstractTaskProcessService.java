/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.impl.scheduler;

import com.huawei.it.fcst.enums.GroupTaskStatus;
import com.huawei.it.fcst.enums.SubModuleName;
import com.huawei.it.fcst.industry.price.dao.IDmFcstPriceBaseCusDimDao;
import com.huawei.it.fcst.industry.price.vo.drop.DmFcstBasePriceCusDimVO;
import com.huawei.it.fcst.task.ITaskProcessService;

import lombok.extern.slf4j.Slf4j;
import javax.inject.Inject;

import java.io.Serializable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 任务执行抽象类
 *
 * <AUTHOR>
 * @since 202407
 */
@Slf4j
public abstract class AbstractTaskProcessService implements ITaskProcessService {
    protected static final String FUNC_STATUS_SUCCESS = "SUCCESS";

    protected static final String FUNC_STATUS_FAIL = "FAIL";
    protected static final ExecutorService EXECUTOR_SERVICE;

    static {
        EXECUTOR_SERVICE = new ThreadPoolExecutor(5, 10, 60, TimeUnit.MINUTES, new LinkedBlockingQueue<Runnable>(100),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Inject
    private IDmFcstPriceBaseCusDimDao dmFcstPriceBaseCusDimDao;

    @Override
    public SubModuleName getSubModuleName() {
        return SubModuleName.PRICE;
    }

    @Override
    public Serializable before(Serializable serializable) {
        log.info("start ......");
        return serializable;
    }

    @Override
    public void after(Serializable serializable, Boolean status) {
        DmFcstBasePriceCusDimVO dmFcstBaseCusDimVO = (DmFcstBasePriceCusDimVO) serializable;
        dmFcstBaseCusDimVO.setStatusFlag(status ? GroupTaskStatus.SUCCESS.getCode() : GroupTaskStatus.FAIL.getCode());
        dmFcstPriceBaseCusDimDao.updateTaskStatus(dmFcstBaseCusDimVO);
    }

}
