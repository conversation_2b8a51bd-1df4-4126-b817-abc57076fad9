<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IProdGroupsViewDao">
    <resultMap type="com.huawei.it.fcst.industry.index.vo.view.ViewInfoVO" id="resultMap">
        <result property="id" column="ID" />
        <result property="lv0ProdRndTeamCode" column="LV0_PROD_RND_TEAM_CODE" />
        <result property="lv0ProdRnTeamName" column="LV0_PROD_RD_TEAM_CN_NAME" />
        <result property="lv1ProdRndTeamCode" column="LV1_PROD_RND_TEAM_CODE" />
        <result property="lv1ProdRnTeamName" column="LV1_PROD_RD_TEAM_CN_NAME" />
        <result property="lv2ProdRndTeamCode" column="LV2_PROD_RND_TEAM_CODE" />
        <result property="lv2ProdRnTeamName" column="LV2_PROD_RD_TEAM_CN_NAME" />
        <result property="lv3ProdRndTeamCode" column="LV3_PROD_RND_TEAM_CODE" />
        <result property="lv3ProdRnTeamName" column="LV3_PROD_RD_TEAM_CN_NAME" />
        <result property="l3CegCode" column="L3_CEG_CODE" />
        <result property="l3CegName" column="L3_CEG_CN_NAME" />
        <result property="categoryCode" column="CATEGORY_CODE" />
        <result property="categoryName" column="CATEGORY_CN_NAME" />
        <result property="itemCode" column="ITEM_CODE" />
        <result property="itemName" column="ITEM_CN_NAME" />
        <result property="viewFlag" column="VIEW_FLAG" />
        <result property="groupLevel" column="GROUP_LEVEL" />
        <result property="createdBy" column="created_by" />
        <result property="creationDate" column="creation_date" />
        <result property="lastUpdatedBy" column="last_updated_by" />
        <result property="lastUpdateDate" column="last_update_date" />
        <result property="delFlag" column="del_flag" />
        <result property="selfParentDimensionValue" column="self_parent_dimension_value" />
        <result property="dimensionValue" column="dimension_value" />
        <result property="dimensionDisplayValue" column="dimension_display_value" />
    </resultMap>

    <select id="getPurchaseLv2ProdRndTeamList" resultMap="resultMap">
        select DISTINCT LV0_PROD_RND_TEAM_CODE,LV1_PROD_RND_TEAM_CODE,LV2_PROD_RND_TEAM_CODE,VIEW_FLAG FROM fin_dm_opt_foi.${tablePreFix}_view_info_d
        WHERE VIEW_FLAG = '3' AND DEL_FLAG = 'N'
    </select>

    <select id="getTotalLv2ProdRndTeamList" resultMap="resultMap">
        select DISTINCT LV0_PROD_RND_TEAM_CODE,LV1_PROD_RND_TEAM_CODE,LV2_PROD_RND_TEAM_CODE,VIEW_FLAG FROM fin_dm_opt_foi.${tablePreFix}_total_view_info_d
        WHERE VIEW_FLAG = '3' AND DEL_FLAG = 'N' AND page_flag ='ANNUAL'
    </select>

    <select id="getManuFactureLv2ProdRndTeamList" resultMap="resultMap">
        select DISTINCT LV0_PROD_RND_TEAM_CODE,LV1_PROD_RND_TEAM_CODE,LV2_PROD_RND_TEAM_CODE,VIEW_FLAG FROM fin_dm_opt_foi.${tablePreFix}_made_view_info_d
        WHERE VIEW_FLAG = '3' AND DEL_FLAG = 'N'
    </select>

    <select id ="getIctDimensionValue" resultType="java.lang.String">
        select * from (select DISTINCT  LV0_PROD_RND_TEAM_CODE||'_LV0' as dimension_value FROM fin_dm_opt_foi.dm_foc_total_view_info_d
        where VIEW_FLAG = '0' AND DEL_FLAG = 'N' AND page_flag ='ANNUAL'
        union all
        select DISTINCT LV1_PROD_RND_TEAM_CODE||'_LV1' as dimension_value FROM fin_dm_opt_foi.dm_foc_total_view_info_d
        where VIEW_FLAG = '1' AND DEL_FLAG = 'N' AND page_flag ='ANNUAL'
        union all
        select DISTINCT LV2_PROD_RND_TEAM_CODE||'_LV2' as dimension_value FROM fin_dm_opt_foi.dm_foc_total_view_info_d
        where VIEW_FLAG = '2' AND DEL_FLAG = 'N' AND page_flag ='ANNUAL')
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <choose>
                <when test="dimensionValueList != null and dimensionValueList.size()>0">
                    AND dimension_value in
                    <foreach collection="dimensionValueList" item="item" open="(" separator="," close=")">
                        #{item,jdbcType=VARCHAR}
                    </foreach>
                </when>
            </choose>
        </trim>
    </select>

    <select id ="getIctLv1DimensionValue" resultType="java.lang.String">
        select * from (select DISTINCT LV1_PROD_RND_TEAM_CODE||'_LV1' as dimension_value,LV2_PROD_RND_TEAM_CODE||'_LV2' as LV2_PROD_RND_TEAM_CODE FROM fin_dm_opt_foi.dm_foc_total_view_info_d
        where VIEW_FLAG = '3' AND DEL_FLAG = 'N' AND page_flag ='ANNUAL')
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <choose>
                <when test="dimensionValueList != null and dimensionValueList.size()>0">
                    AND LV2_PROD_RND_TEAM_CODE in
                    <foreach collection="dimensionValueList" item="item" open="(" separator="," close=")">
                        #{item,jdbcType=VARCHAR}
                    </foreach>
                </when>
            </choose>
        </trim>
    </select>

    <select id ="getEnergyDimensionValue" resultType="java.lang.String">
        select * from (select DISTINCT  LV0_PROD_RND_TEAM_CODE||'_L0_E' as dimension_value FROM fin_dm_opt_foi.dm_foc_energy_total_view_info_d
        where VIEW_FLAG = '0' AND DEL_FLAG = 'N' AND page_flag ='ANNUAL'
        union all
        select DISTINCT LV1_PROD_RND_TEAM_CODE||'_L1_E' as dimension_value FROM fin_dm_opt_foi.dm_foc_energy_total_view_info_d
        where VIEW_FLAG = '1' AND DEL_FLAG = 'N' AND page_flag ='ANNUAL'
        union all
        select DISTINCT LV2_PROD_RND_TEAM_CODE||'_L2_E' as dimension_value FROM fin_dm_opt_foi.dm_foc_energy_total_view_info_d
        where VIEW_FLAG = '2' AND DEL_FLAG = 'N' AND page_flag ='ANNUAL')
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <choose>
                <when test="dimensionValueList != null and dimensionValueList.size()>0">
                    AND dimension_value in
                    <foreach collection="dimensionValueList" item="item" open="(" separator="," close=")">
                        #{item,jdbcType=VARCHAR}
                    </foreach>
                </when>
            </choose>
        </trim>
    </select>

    <select id ="getEnergyLv1DimensionValue" resultType="java.lang.String">
        select * from (
        select DISTINCT LV1_PROD_RND_TEAM_CODE||'_L1_E' as dimension_value,LV2_PROD_RND_TEAM_CODE||'_L2_E' as LV2_PROD_RND_TEAM_CODE FROM fin_dm_opt_foi.dm_foc_energy_total_view_info_d
        where VIEW_FLAG = '3' AND DEL_FLAG = 'N' AND page_flag ='ANNUAL'
        )
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <choose>
                <when test="dimensionValueList != null and dimensionValueList.size()>0">
                    AND LV2_PROD_RND_TEAM_CODE in
                    <foreach collection="dimensionValueList" item="item" open="(" separator="," close=")">
                        #{item,jdbcType=VARCHAR}
                    </foreach>
                </when>
            </choose>
        </trim>
    </select>

    <select id ="getIasDimensionValue" resultType="java.lang.String">
        select * from (select DISTINCT  LV0_PROD_RND_TEAM_CODE||'_L0_IAS' as dimension_value FROM fin_dm_opt_foi.dm_foc_ias_total_view_info_d
        where VIEW_FLAG = '0' AND DEL_FLAG = 'N' AND page_flag ='ANNUAL'
        union all
        select DISTINCT LV1_PROD_RND_TEAM_CODE||'_L1_IAS' as dimension_value FROM fin_dm_opt_foi.dm_foc_ias_total_view_info_d
        where VIEW_FLAG = '1' AND DEL_FLAG = 'N' AND page_flag ='ANNUAL'
        union all
        select DISTINCT LV2_PROD_RND_TEAM_CODE||'_L2_IAS' as dimension_value FROM fin_dm_opt_foi.dm_foc_ias_total_view_info_d
        where VIEW_FLAG = '2' AND DEL_FLAG = 'N' AND page_flag ='ANNUAL')
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <choose>
                <when test="dimensionValueList != null and dimensionValueList.size()>0">
                    AND dimension_value in
                    <foreach collection="dimensionValueList" item="item" open="(" separator="," close=")">
                        #{item,jdbcType=VARCHAR}
                    </foreach>
                </when>
            </choose>
        </trim>
    </select>

    <select id ="getIctIrbDimensionValue" resultType="java.lang.String">
        select * from ( select DISTINCT  null as self_parent_dimension_value, '104364_L0_ICT' as dimension_value, 'ICT-经管' as dimension_display_value,'0' as view_flag,'LV0' as group_level FROM fin_dm_opt_foi.dm_fcst_ict_psp_irb_dim_info_t
        where  DEL_FLAG = 'N'
        union all
        select DISTINCT '104364_L0_ICT' as self_parent_dimension_value, 'IRB_L1_ICT' as dimension_value,'重量级团队目录' as dimension_display_value, '1' as view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_psp_irb_dim_info_t
        where  DEL_FLAG = 'N'
        union all
        select * from (select DISTINCT 'IRB_L1_ICT' as self_parent_dimension_value, LV1_PROD_RND_TEAM_CODE||'_L2_ICT' as dimension_value,LV1_PROD_RD_TEAM_CN_NAME as dimension_display_value, '2' as view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_psp_irb_dim_info_t
        where  DEL_FLAG = 'N' and group_level ='LV1'
        union
        select DISTINCT 'IRB_L1_ICT' as self_parent_dimension_value, LV1_PROD_RND_TEAM_CODE||'_L2_ICT' as dimension_value,LV1_PROD_RD_TEAM_CN_NAME as dimension_display_value, '2' as view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_std_irb_dim_info_t
        where  DEL_FLAG = 'N' and group_level ='LV1')
        union all
        select * from (select DISTINCT LV1_PROD_RND_TEAM_CODE||'_L2_ICT' as self_parent_dimension_value, LV2_PROD_RND_TEAM_CODE||'_L3_ICT' as dimension_value,LV2_PROD_RD_TEAM_CN_NAME as dimension_display_value, '3' as view_flag,'LV2' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_psp_irb_dim_info_t
        where  DEL_FLAG = 'N' and group_level ='LV2'
        union
        select DISTINCT LV1_PROD_RND_TEAM_CODE||'_L2_ICT' as self_parent_dimension_value, LV2_PROD_RND_TEAM_CODE||'_L3_ICT' as dimension_value,LV2_PROD_RD_TEAM_CN_NAME as dimension_display_value, '3' as view_flag,'LV2' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_std_irb_dim_info_t
        where  DEL_FLAG = 'N' and group_level ='LV2'))
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <choose>
                <when test="dimensionValueList != null and dimensionValueList.size()>0">
                    AND dimension_value in
                    <foreach collection="dimensionValueList" item="item" open="(" separator="," close=")">
                        #{item,jdbcType=VARCHAR}
                    </foreach>
                </when>
            </choose>
        </trim>
    </select>

    <select id ="getIctProdDimensionValue" resultType="java.lang.String">
        select * from (
        select DISTINCT '104364_L0_ICT' as self_parent_dimension_value, 'PROD_L1_ICT' as dimension_value,'销售目录' as dimension_display_value,'1'as view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_psp_prod_dim_info_t
        where  DEL_FLAG = 'N'
        union all
        select * from (select DISTINCT 'PROD_L1_ICT' as self_parent_dimension_value, LV1_PROD_LIST_CODE||'_L2_ICT' as dimension_value,LV1_PROD_LIST_cn_Name as dimension_display_value, '2' as view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_psp_prod_dim_info_t
        where  DEL_FLAG = 'N' and group_level ='LV1'
        union
        select DISTINCT 'PROD_L1_ICT' as self_parent_dimension_value, LV1_PROD_LIST_CODE||'_L2_ICT' as dimension_value,LV1_PROD_LIST_cn_Name as dimension_display_value, '2' as view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_std_prod_dim_info_t
        where  DEL_FLAG = 'N' and group_level ='LV1')
        union all
        select * from (select DISTINCT LV1_PROD_LIST_CODE||'_L2_ICT' as self_parent_dimension_value, LV2_PROD_LIST_CODE||'_L3_ICT' as dimension_value,LV2_PROD_LIST_CN_NAME as dimension_display_value, '3' as view_flag,'LV2' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_psp_prod_dim_info_t
        where  DEL_FLAG = 'N' and group_level ='LV2'
        union
        select DISTINCT LV1_PROD_LIST_CODE||'_L2_ICT' as self_parent_dimension_value, LV2_PROD_LIST_CODE||'_L3_ICT' as dimension_value,LV2_PROD_LIST_CN_NAME as dimension_display_value, '3' as view_flag,'LV2' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_std_prod_dim_info_t
        where  DEL_FLAG = 'N' and group_level ='LV2')
       )
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <choose>
                <when test="dimensionValueList != null and dimensionValueList.size()>0">
                    AND dimension_value in
                    <foreach collection="dimensionValueList" item="item" open="(" separator="," close=")">
                        #{item,jdbcType=VARCHAR}
                    </foreach>
                </when>
            </choose>
        </trim>
    </select>

    <select id ="getIctIndusDimensionValue" resultType="java.lang.String">
        select * from (
        select DISTINCT '104364_L0_ICT' as self_parent_dimension_value, 'INDUS_L1_ICT' as dimension_value,'产业目录' as dimension_display_value, '1' as view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_psp_indus_dim_info_t
        where DEL_FLAG = 'N'
        union all
        select * from (select DISTINCT 'INDUS_L1_ICT' as self_parent_dimension_value, lv1_industry_catg_code||'_L2_ICT' as dimension_value,lv1_industry_catg_cn_name as dimension_display_value, '2' as view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_psp_indus_dim_info_t
        where  DEL_FLAG = 'N' and group_level ='LV1'
        union
        select DISTINCT 'INDUS_L1_ICT' as self_parent_dimension_value, lv1_industry_catg_code||'_L2_ICT' as dimension_value,lv1_industry_catg_cn_name as dimension_display_value, '2' as view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_std_indus_dim_info_t
        where  DEL_FLAG = 'N' and group_level ='LV1')
        union all
        select * from (select DISTINCT lv1_industry_catg_code||'_L2_ICT' as self_parent_dimension_value, lv2_industry_catg_code||'_L3_ICT' as dimension_value,lv2_industry_catg_cn_name as dimension_display_value, '3' as view_flag,'LV2' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_psp_indus_dim_info_t
        where DEL_FLAG = 'N'  and group_level ='LV2'
        union
        select DISTINCT lv1_industry_catg_code||'_L2_ICT' as self_parent_dimension_value, lv2_industry_catg_code||'_L3_ICT' as dimension_value,lv2_industry_catg_cn_name as dimension_display_value, '3' as view_flag,'LV2' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_std_indus_dim_info_t
        where DEL_FLAG = 'N'  and group_level ='LV2'))
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <choose>
                <when test="dimensionValueList != null and dimensionValueList.size()>0">
                    AND dimension_value in
                    <foreach collection="dimensionValueList" item="item" open="(" separator="," close=")">
                        #{item,jdbcType=VARCHAR}
                    </foreach>
                </when>
            </choose>
        </trim>
    </select>

    <select id="getDimensionWithTree" resultMap="resultMap">
        select * from(
        select DISTINCT  null as self_parent_dimension_value, LV0_PROD_RND_TEAM_CODE||'_LV0' as dimension_value, LV0_PROD_RD_TEAM_CN_NAME as dimension_display_value,view_flag,'LV0' as group_level FROM fin_dm_opt_foi.dm_foc_total_view_info_d
        where VIEW_FLAG = '0' AND DEL_FLAG = 'N' AND page_flag ='ANNUAL'
        union all
        select DISTINCT LV0_PROD_RND_TEAM_CODE||'_LV0' as self_parent_dimension_value, LV1_PROD_RND_TEAM_CODE||'_LV1' as dimension_value,LV1_PROD_RD_TEAM_CN_NAME as dimension_display_value, view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_foc_total_view_info_d
        where VIEW_FLAG = '1' AND DEL_FLAG = 'N' AND page_flag ='ANNUAL'
        union all
        select DISTINCT LV1_PROD_RND_TEAM_CODE||'_LV1' as self_parent_dimension_value, LV2_PROD_RND_TEAM_CODE||'_LV2' as dimension_value,LV2_PROD_RD_TEAM_CN_NAME as dimension_display_value, view_flag,'LV2' as group_level  FROM fin_dm_opt_foi.dm_foc_total_view_info_d
        where VIEW_FLAG = '2' AND DEL_FLAG = 'N' AND page_flag ='ANNUAL'
        union all
        select DISTINCT  null as self_parent_dimension_value, LV0_PROD_RND_TEAM_CODE||'_L0_E' as dimension_value, LV0_PROD_RD_TEAM_CN_NAME as dimension_display_value,view_flag,'LV0' as group_level FROM fin_dm_opt_foi.dm_foc_energy_total_view_info_d
        where VIEW_FLAG = '0' AND DEL_FLAG = 'N' AND page_flag ='ANNUAL'
        union all
        select DISTINCT LV0_PROD_RND_TEAM_CODE||'_L0_E' as self_parent_dimension_value, LV1_PROD_RND_TEAM_CODE||'_L1_E' as dimension_value,LV1_PROD_RD_TEAM_CN_NAME as dimension_display_value, view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_foc_energy_total_view_info_d
        where VIEW_FLAG = '1' AND DEL_FLAG = 'N' AND page_flag ='ANNUAL'
        union all
        select DISTINCT LV1_PROD_RND_TEAM_CODE||'_L1_E' as self_parent_dimension_value, LV2_PROD_RND_TEAM_CODE||'_L2_E' as dimension_value,LV2_PROD_RD_TEAM_CN_NAME as dimension_display_value, view_flag,'LV2' as group_level  FROM fin_dm_opt_foi.dm_foc_energy_total_view_info_d
        where VIEW_FLAG = '2' AND DEL_FLAG = 'N' AND page_flag ='ANNUAL'
        union all
        select DISTINCT  null as self_parent_dimension_value, LV0_PROD_RND_TEAM_CODE||'_L0_IAS' as dimension_value, LV0_PROD_RD_TEAM_CN_NAME as dimension_display_value,view_flag,'LV0' as group_level FROM fin_dm_opt_foi.dm_foc_ias_total_view_info_d
        where VIEW_FLAG = '0' AND DEL_FLAG = 'N' AND page_flag ='ANNUAL'
        union all
        select DISTINCT LV0_PROD_RND_TEAM_CODE||'_L0_IAS' as self_parent_dimension_value, LV1_PROD_RND_TEAM_CODE||'_L1_IAS' as dimension_value,LV1_PROD_RD_TEAM_CN_NAME as dimension_display_value, view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_foc_ias_total_view_info_d
        where VIEW_FLAG = '1' AND DEL_FLAG = 'N' AND page_flag ='ANNUAL'
        union all
        select DISTINCT LV1_PROD_RND_TEAM_CODE||'_L1_IAS' as self_parent_dimension_value, LV2_PROD_RND_TEAM_CODE||'_L2_IAS' as dimension_value,LV2_PROD_RD_TEAM_CN_NAME as dimension_display_value, view_flag,'LV2' as group_level  FROM fin_dm_opt_foi.dm_foc_ias_total_view_info_d
        where VIEW_FLAG = '2' AND DEL_FLAG = 'N' AND page_flag ='ANNUAL')
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <choose>
                <when test="dimensionValueList != null and dimensionValueList.size()>0">
                    AND dimension_value in
                    <foreach collection="dimensionValueList" item="item" open="(" separator="," close=")">
                        #{item,jdbcType=VARCHAR}
                    </foreach>
                </when>
                <otherwise>
                    AND 1=2
                </otherwise>
            </choose>
        </trim>
        order by  view_flag
    </select>

    <select id="getIctWithTree" resultMap="resultMap">
        select DISTINCT  null as self_parent_dimension_value, '104364_L0_ICT' as dimension_value, 'ICT-经管' as dimension_display_value,'0' as view_flag,'LV0' as group_level FROM fin_dm_opt_foi.dm_fcst_ict_psp_irb_dim_info_t
        where  DEL_FLAG = 'N'
        union all
        select DISTINCT '104364_L0_ICT' as self_parent_dimension_value, 'IRB_L1_ICT' as dimension_value,'重量级团队目录' as dimension_display_value, '1' as view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_psp_irb_dim_info_t
        where  DEL_FLAG = 'N'
        union all
        select * from (select DISTINCT 'IRB_L1_ICT' as self_parent_dimension_value, LV1_PROD_RND_TEAM_CODE||'_L2_ICT' as dimension_value,LV1_PROD_RD_TEAM_CN_NAME as dimension_display_value, '2' as view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_psp_irb_dim_info_t
        where  DEL_FLAG = 'N' and group_level ='LV1'
        union
        select DISTINCT 'IRB_L1_ICT' as self_parent_dimension_value, LV1_PROD_RND_TEAM_CODE||'_L2_ICT' as dimension_value,LV1_PROD_RD_TEAM_CN_NAME as dimension_display_value, '2' as view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_std_irb_dim_info_t
        where  DEL_FLAG = 'N' and group_level ='LV1')
        union all
        select * from (select DISTINCT LV1_PROD_RND_TEAM_CODE||'_L2_ICT' as self_parent_dimension_value, LV2_PROD_RND_TEAM_CODE||'_L3_ICT' as dimension_value,LV2_PROD_RD_TEAM_CN_NAME as dimension_display_value, '3' as view_flag,'LV2' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_psp_irb_dim_info_t
        where  DEL_FLAG = 'N' and group_level ='LV2'
        union
        select DISTINCT LV1_PROD_RND_TEAM_CODE||'_L2_ICT' as self_parent_dimension_value, LV2_PROD_RND_TEAM_CODE||'_L3_ICT' as dimension_value,LV2_PROD_RD_TEAM_CN_NAME as dimension_display_value, '3' as view_flag,'LV2' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_std_irb_dim_info_t
        where  DEL_FLAG = 'N' and group_level ='LV2')
        union all
        select DISTINCT '104364_L0_ICT' as self_parent_dimension_value, 'PROD_L1_ICT' as dimension_value,'销售目录' as dimension_display_value,'1'as view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_psp_prod_dim_info_t
        where  DEL_FLAG = 'N'
        union all
        select * from (select DISTINCT 'PROD_L1_ICT' as self_parent_dimension_value, LV1_PROD_LIST_CODE||'_L2_ICT' as dimension_value,LV1_PROD_LIST_cn_Name as dimension_display_value, '2' as view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_psp_prod_dim_info_t
        where  DEL_FLAG = 'N' and group_level ='LV1'
        union
        select DISTINCT 'PROD_L1_ICT' as self_parent_dimension_value, LV1_PROD_LIST_CODE||'_L2_ICT' as dimension_value,LV1_PROD_LIST_cn_Name as dimension_display_value, '2' as view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_std_prod_dim_info_t
        where  DEL_FLAG = 'N' and group_level ='LV1')
        union all
        select * from (select DISTINCT LV1_PROD_LIST_CODE||'_L2_ICT' as self_parent_dimension_value, LV2_PROD_LIST_CODE||'_L3_ICT' as dimension_value,LV2_PROD_LIST_CN_NAME as dimension_display_value, '3' as view_flag,'LV2' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_psp_prod_dim_info_t
        where  DEL_FLAG = 'N' and group_level ='LV2'
        union
        select DISTINCT LV1_PROD_LIST_CODE||'_L2_ICT' as self_parent_dimension_value, LV2_PROD_LIST_CODE||'_L3_ICT' as dimension_value,LV2_PROD_LIST_CN_NAME as dimension_display_value, '3' as view_flag,'LV2' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_std_prod_dim_info_t
        where  DEL_FLAG = 'N' and group_level ='LV2')
        union all
        select DISTINCT '104364_L0_ICT' as self_parent_dimension_value, 'INDUS_L1_ICT' as dimension_value,'产业目录' as dimension_display_value, '1' as view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_psp_indus_dim_info_t
        where DEL_FLAG = 'N'
        union all
        select * from (select DISTINCT 'INDUS_L1_ICT' as self_parent_dimension_value, lv1_industry_catg_code||'_L2_ICT' as dimension_value,lv1_industry_catg_cn_name as dimension_display_value, '2' as view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_psp_indus_dim_info_t
        where  DEL_FLAG = 'N' and group_level ='LV1'
        union
        select DISTINCT 'INDUS_L1_ICT' as self_parent_dimension_value, lv1_industry_catg_code||'_L2_ICT' as dimension_value,lv1_industry_catg_cn_name as dimension_display_value, '2' as view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_std_indus_dim_info_t
        where  DEL_FLAG = 'N' and group_level ='LV1')
        union all
        select * from (select DISTINCT lv1_industry_catg_code||'_L2_ICT' as self_parent_dimension_value, lv2_industry_catg_code||'_L3_ICT' as dimension_value,lv2_industry_catg_cn_name as dimension_display_value, '3' as view_flag,'LV2' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_psp_indus_dim_info_t
        where DEL_FLAG = 'N'  and group_level ='LV2'
        union
        select DISTINCT lv1_industry_catg_code||'_L2_ICT' as self_parent_dimension_value, lv2_industry_catg_code||'_L3_ICT' as dimension_value,lv2_industry_catg_cn_name as dimension_display_value, '3' as view_flag,'LV2' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_std_indus_dim_info_t
        where DEL_FLAG = 'N'  and group_level ='LV2')
        order by  view_flag

    </select>

    <insert id="insertDimensionWithTree" parameterType="java.util.List">
        INSERT INTO fin_dm_opt_foi.DM_FOC_CATG_CEG_ICT_D
        (refresh_date,
        self_parent_dimension_value,
        dimension_value,
        dimension_display_value,
        group_level,
        view_flag,
        creation_date,
        created_by,
        last_updated_by,
        last_updated_date)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (NOW(),
            #{item.selfParentDimensionValue,jdbcType=VARCHAR},
            #{item.dimensionValue,jdbcType=VARCHAR},
            #{item.dimensionDisplayValue,jdbcType=VARCHAR},
            #{item.groupLevel,jdbcType=VARCHAR},
            #{item.viewFlag,jdbcType=VARCHAR},
            NOW(),
            #{item.createdBy,jdbcType=VARCHAR},
            #{item.lastUpdatedBy,jdbcType=VARCHAR},
            NOW()
        </foreach>
    </insert>

    <select id="getDimensionWithTreePageList" resultMap="resultMap">
        select * from
        (
        <include refid="treePart"></include>
        )
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test="viewInfoVO.dimensionDisplayValue != null and viewInfoVO.dimensionDisplayValue !=''">
                AND UPPER (dimension_display_value) LIKE CONCAT ( CONCAT ( '%', UPPER (#{viewInfoVO.dimensionDisplayValue,jdbcType=VARCHAR})) ,'%')
            </if>
            <if test="viewInfoVO.dimensionValue != null and viewInfoVO.dimensionValue !=''">
                AND UPPER (dimension_value) LIKE CONCAT ( CONCAT ( '%', UPPER (#{viewInfoVO.dimensionValue,jdbcType=VARCHAR})) ,'%')
            </if>
        </trim>
        order by  view_flag
        LIMIT #{pageVO.pageSize} OFFSET #{pageVO.mysqlStartIndex}
    </select>
    <sql id ="treePart">
        select DISTINCT  null as self_parent_dimension_value, LV0_PROD_RND_TEAM_CODE||'_LV0' as dimension_value, LV0_PROD_RD_TEAM_CN_NAME as dimension_display_value,view_flag,'LV0' as group_level  FROM fin_dm_opt_foi.dm_foc_total_view_info_d
        where VIEW_FLAG = '0' AND DEL_FLAG = 'N' AND page_flag ='ANNUAL'
        union all
        select DISTINCT LV0_PROD_RND_TEAM_CODE||'_LV0' as self_parent_dimension_value, LV1_PROD_RND_TEAM_CODE||'_LV1' as dimension_value,LV1_PROD_RD_TEAM_CN_NAME as dimension_display_value, view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.DM_FOC_TOTAL_VIEW_INFO_D
        where VIEW_FLAG = '1' AND DEL_FLAG = 'N' AND page_flag ='ANNUAL'
        union all
        select DISTINCT LV1_PROD_RND_TEAM_CODE||'_LV1' as self_parent_dimension_value, LV2_PROD_RND_TEAM_CODE||'_LV2' as dimension_value,LV2_PROD_RD_TEAM_CN_NAME as dimension_display_value, view_flag,'LV2' as group_level  FROM fin_dm_opt_foi.DM_FOC_TOTAL_VIEW_INFO_D
        where VIEW_FLAG = '2' AND DEL_FLAG = 'N' AND page_flag ='ANNUAL'
        union all
        select DISTINCT  null as self_parent_dimension_value, LV0_PROD_RND_TEAM_CODE||'_L0_E' as dimension_value, LV0_PROD_RD_TEAM_CN_NAME as dimension_display_value,view_flag,'LV0' as group_level FROM fin_dm_opt_foi.dm_foc_energy_total_view_info_d
        where VIEW_FLAG = '0' AND DEL_FLAG = 'N' AND page_flag ='ANNUAL'
        union all
        select DISTINCT LV0_PROD_RND_TEAM_CODE||'_L0_E' as self_parent_dimension_value, LV1_PROD_RND_TEAM_CODE||'_L1_E' as dimension_value,LV1_PROD_RD_TEAM_CN_NAME as dimension_display_value, view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_foc_energy_total_view_info_d
        where VIEW_FLAG = '1' AND DEL_FLAG = 'N' AND page_flag ='ANNUAL'
        union all
        select DISTINCT LV1_PROD_RND_TEAM_CODE||'_L1_E' as self_parent_dimension_value, LV2_PROD_RND_TEAM_CODE||'_L2_E' as dimension_value,LV2_PROD_RD_TEAM_CN_NAME as dimension_display_value, view_flag,'LV2' as group_level  FROM fin_dm_opt_foi.dm_foc_energy_total_view_info_d
        where VIEW_FLAG = '2' AND DEL_FLAG = 'N' AND page_flag ='ANNUAL'
        union all
        select DISTINCT  null as self_parent_dimension_value, LV0_PROD_RND_TEAM_CODE||'_L0_IAS' as dimension_value, LV0_PROD_RD_TEAM_CN_NAME as dimension_display_value,view_flag,'LV0' as group_level FROM fin_dm_opt_foi.dm_foc_ias_total_view_info_d
        where VIEW_FLAG = '0' AND DEL_FLAG = 'N' AND page_flag ='ANNUAL'
        union all
        select DISTINCT LV0_PROD_RND_TEAM_CODE||'_L0_IAS' as self_parent_dimension_value, LV1_PROD_RND_TEAM_CODE||'_L1_IAS' as dimension_value,LV1_PROD_RD_TEAM_CN_NAME as dimension_display_value, view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_foc_ias_total_view_info_d
        where VIEW_FLAG = '1' AND DEL_FLAG = 'N' AND page_flag ='ANNUAL'
        union all
        select DISTINCT LV1_PROD_RND_TEAM_CODE||'_L1_IAS' as self_parent_dimension_value, LV2_PROD_RND_TEAM_CODE||'_L2_IAS' as dimension_value,LV2_PROD_RD_TEAM_CN_NAME as dimension_display_value, view_flag,'LV2' as group_level  FROM fin_dm_opt_foi.dm_foc_ias_total_view_info_d
        where VIEW_FLAG = '2' AND DEL_FLAG = 'N' AND page_flag ='ANNUAL'
        union all
        select DISTINCT  null as self_parent_dimension_value, '104364_L0_ICT' as dimension_value, 'ICT-经管' as dimension_display_value,'0' as view_flag,'LV0' as group_level FROM fin_dm_opt_foi.dm_fcst_ict_psp_irb_dim_info_t
        where  DEL_FLAG = 'N'
        union all
        select DISTINCT '104364_L0_ICT' as self_parent_dimension_value, 'IRB_L1_ICT' as dimension_value,'重量级团队目录' as dimension_display_value, '1' as view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_psp_irb_dim_info_t
        where  DEL_FLAG = 'N'
        union all
        select * from (select DISTINCT 'IRB_L1_ICT' as self_parent_dimension_value, LV1_PROD_RND_TEAM_CODE||'_L2_ICT' as dimension_value,LV1_PROD_RD_TEAM_CN_NAME as dimension_display_value, '2' as view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_psp_irb_dim_info_t
                       where  DEL_FLAG = 'N' and group_level ='LV1'
                       union
                       select DISTINCT 'IRB_L1_ICT' as self_parent_dimension_value, LV1_PROD_RND_TEAM_CODE||'_L2_ICT' as dimension_value,LV1_PROD_RD_TEAM_CN_NAME as dimension_display_value, '2' as view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_std_irb_dim_info_t
                       where  DEL_FLAG = 'N' and group_level ='LV1')
        union all
        select * from (select DISTINCT LV1_PROD_RND_TEAM_CODE||'_L2_ICT' as self_parent_dimension_value, LV2_PROD_RND_TEAM_CODE||'_L3_ICT' as dimension_value,LV2_PROD_RD_TEAM_CN_NAME as dimension_display_value, '3' as view_flag,'LV2' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_psp_irb_dim_info_t
                       where  DEL_FLAG = 'N' and group_level ='LV2'
                       union
                       select DISTINCT LV1_PROD_RND_TEAM_CODE||'_L2_ICT' as self_parent_dimension_value, LV2_PROD_RND_TEAM_CODE||'_L3_ICT' as dimension_value,LV2_PROD_RD_TEAM_CN_NAME as dimension_display_value, '3' as view_flag,'LV2' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_std_irb_dim_info_t
                       where  DEL_FLAG = 'N' and group_level ='LV2')
        union all
        select DISTINCT '104364_L0_ICT' as self_parent_dimension_value, 'PROD_L1_ICT' as dimension_value,'销售目录' as dimension_display_value,'1'as view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_psp_prod_dim_info_t
        where  DEL_FLAG = 'N'
        union all
        select * from (select DISTINCT 'PROD_L1_ICT' as self_parent_dimension_value, LV1_PROD_LIST_CODE||'_L2_ICT' as dimension_value,LV1_PROD_LIST_cn_Name as dimension_display_value, '2' as view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_psp_prod_dim_info_t
                       where  DEL_FLAG = 'N' and group_level ='LV1'
                       union
                       select DISTINCT 'PROD_L1_ICT' as self_parent_dimension_value, LV1_PROD_LIST_CODE||'_L2_ICT' as dimension_value,LV1_PROD_LIST_cn_Name as dimension_display_value, '2' as view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_std_prod_dim_info_t
                       where  DEL_FLAG = 'N' and group_level ='LV1')
        union all
        select * from (select DISTINCT LV1_PROD_LIST_CODE||'_L2_ICT' as self_parent_dimension_value, LV2_PROD_LIST_CODE||'_L3_ICT' as dimension_value,LV2_PROD_LIST_CN_NAME as dimension_display_value, '3' as view_flag,'LV2' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_psp_prod_dim_info_t
                       where  DEL_FLAG = 'N' and group_level ='LV2'
                       union
                       select DISTINCT LV1_PROD_LIST_CODE||'_L2_ICT' as self_parent_dimension_value, LV2_PROD_LIST_CODE||'_L3_ICT' as dimension_value,LV2_PROD_LIST_CN_NAME as dimension_display_value, '3' as view_flag,'LV2' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_std_prod_dim_info_t
                       where  DEL_FLAG = 'N' and group_level ='LV2')
        union all
        select DISTINCT '104364_L0_ICT' as self_parent_dimension_value, 'INDUS_L1_ICT' as dimension_value,'产业目录' as dimension_display_value, '1' as view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_psp_indus_dim_info_t
        where DEL_FLAG = 'N'
        union all
        select * from (select DISTINCT 'INDUS_L1_ICT' as self_parent_dimension_value, lv1_industry_catg_code||'_L2_ICT' as dimension_value,lv1_industry_catg_cn_name as dimension_display_value, '2' as view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_psp_indus_dim_info_t
                       where  DEL_FLAG = 'N' and group_level ='LV1'
                       union
                       select DISTINCT 'INDUS_L1_ICT' as self_parent_dimension_value, lv1_industry_catg_code||'_L2_ICT' as dimension_value,lv1_industry_catg_cn_name as dimension_display_value, '2' as view_flag,'LV1' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_std_indus_dim_info_t
                       where  DEL_FLAG = 'N' and group_level ='LV1')
        union all
        select * from (select DISTINCT lv1_industry_catg_code||'_L2_ICT' as self_parent_dimension_value, lv2_industry_catg_code||'_L3_ICT' as dimension_value,lv2_industry_catg_cn_name as dimension_display_value, '3' as view_flag,'LV2' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_psp_indus_dim_info_t
                       where DEL_FLAG = 'N'  and group_level ='LV2'
                       union
                       select DISTINCT lv1_industry_catg_code||'_L2_ICT' as self_parent_dimension_value, lv2_industry_catg_code||'_L3_ICT' as dimension_value,lv2_industry_catg_cn_name as dimension_display_value, '3' as view_flag,'LV2' as group_level  FROM fin_dm_opt_foi.dm_fcst_ict_std_indus_dim_info_t
                       where DEL_FLAG = 'N'  and group_level ='LV2')
    </sql>

    <select id="getDimensionWithTreePageListCount" resultType="int">
        select count(*) from (
        select * from (
        <include refid="treePart"></include>
        )
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test="viewInfoVO.dimensionDisplayValue != null and viewInfoVO.dimensionDisplayValue !=''">
                AND UPPER (dimension_display_value) LIKE CONCAT ( CONCAT ( '%', UPPER (#{viewInfoVO.dimensionDisplayValue,jdbcType=VARCHAR})) ,'%')
            </if>
            <if test="viewInfoVO.dimensionValue != null and viewInfoVO.dimensionValue !=''">
                AND UPPER (dimension_value) LIKE CONCAT ( CONCAT ( '%', UPPER (#{viewInfoVO.dimensionValue,jdbcType=VARCHAR})) ,'%')
            </if>
        </trim>
        order by  view_flag
        )
    </select>
</mapper>
