/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.relation;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

/**
 * DmDimCatgModlCegIctVOTest Class
 *
 * <AUTHOR>
 * @since 2023/4/23
 */
public class DmDimCatgModlCegIctVOTest extends BaseVOCoverUtilsTest<DmDimCatgModlCegIctVO> {

    @Override
    protected Class<DmDimCatgModlCegIctVO> getTClass() {
        return DmDimCatgModlCegIctVO.class;
    }

    @Test
    public void testMethod() {
        DmDimCatgModlCegIctVO dimensionParamVO = new DmDimCatgModlCegIctVO();
        dimensionParamVO.builder().categoryCnName("adjust").build();
        Assert.assertNotNull(dimensionParamVO);
    }
}