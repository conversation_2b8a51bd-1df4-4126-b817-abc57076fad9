/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.common;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

/**
 * DmFoiImpExpRecordVOTest Class
 *
 * <AUTHOR>
 * @since 2023/4/22
 */
public class DmFoiImpExpRecordVOTest extends BaseVOCoverUtilsTest<DmFoiImpExpRecordVO> {

    @Override
    protected Class<DmFoiImpExpRecordVO> getTClass() {
        return DmFoiImpExpRecordVO.class;
    }

    @Test
    public void testMethod() {
        DmFoiImpExpRecordVO dmFocActualCostVO = new DmFoiImpExpRecordVO();
        dmFocActualCostVO.getCreationDate();
        dmFocActualCostVO.getId();
        dmFocActualCostVO.setId(10);
        dmFocActualCostVO.setDelFlag("Y");
        dmFocActualCostVO.getDelFlag();
        dmFocActualCostVO.getLastUpdatedBy();
        dmFocActualCostVO.setLastUpdatedBy("1175");
        dmFocActualCostVO.setCreatedBy("1175");
        dmFocActualCostVO.getCreatedBy();
        dmFocActualCostVO.getCreationDate();
        dmFocActualCostVO.getEndDate();
        dmFocActualCostVO.setExceptionFeedback("back");
        dmFocActualCostVO.getExceptionFeedback();
        dmFocActualCostVO.setFileName("file");
        dmFocActualCostVO.getFileName();
        dmFocActualCostVO.setFileErrorKey("error");
        dmFocActualCostVO.getFileErrorKey();
        dmFocActualCostVO.setFileSize("10");
        dmFocActualCostVO.getFileSize();
        dmFocActualCostVO.setFileSourceKey("sour");
        dmFocActualCostVO.getFileSourceKey();
        dmFocActualCostVO.getLastUpdateDate();
        dmFocActualCostVO.setOptType("opt");
        dmFocActualCostVO.getOptType();
        dmFocActualCostVO.setPageModule("page");
        dmFocActualCostVO.getPageModule();
        dmFocActualCostVO.setPeriodId("id");
        dmFocActualCostVO.getPeriodId();
        dmFocActualCostVO.setRecordNum(11);
        dmFocActualCostVO.getRecordNum();
        dmFocActualCostVO.setRecSts("sts");
        dmFocActualCostVO.getRecSts();
        dmFocActualCostVO.setUserId("1175");
        dmFocActualCostVO.getUserId();
        dmFocActualCostVO.setStatus("1");
        dmFocActualCostVO.getStatus();
        Assert.assertNotNull(dmFocActualCostVO);
    }
}