/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.combination;

import com.huawei.it.jalor5.core.request.IRequestContext;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * CombTransformVO Class
 *
 * <AUTHOR>
 * @since 2023/8/8
 */
@Getter
@Setter
@NoArgsConstructor
public class CombTransformVO {

    private Long userId;

    private int roleId;

    private String granularityType;

    private String encryptKey;

    private String pageFlag;

    private Long customId;

    private Long taskId;

    private Long versionId;

    private Long monthVersionId;

    private String costType;

    private String industryOrg;

    private String tablePreFix;

    private IRequestContext current;

    private List<DmCustomCombVO> customCombIasList;

    private List<DmCustomCombVO> customCombIasMadeList;
}
