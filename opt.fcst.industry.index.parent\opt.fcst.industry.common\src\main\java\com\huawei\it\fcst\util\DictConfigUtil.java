package com.huawei.it.fcst.util;

import com.huawei.it.jalor5.registry.RegistryVO;
import com.huawei.it.jalor5.registry.service.IRegistryQueryService;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;

@Slf4j
public class DictConfigUtil {
    private static volatile IRegistryQueryService registryQueryService;

    public static String getProperty(String key, String defaultValue) {
        IRegistryQueryService service = getRegistryQueryService();
        if (service == null) {
            log.info("dict return default value");
            return defaultValue;
        }
        String value = service.findValueByPathNoAssertInternal(key, false, defaultValue);
        if (log.isDebugEnabled()) {
            log.debug("dict getProperty[{}] = {}", key, value);
        }
        return value;
    }

    public static List<RegistryVO> getProperties(String key) {
        IRegistryQueryService service = getRegistryQueryService();
        if (service != null) {
            return service.findRegistryListByParentPathInternal(key, false);
        }
        return Collections.emptyList();
    }

    private static IRegistryQueryService getRegistryQueryService() {
        if (registryQueryService == null) {
            synchronized (IRegistryQueryService.class) {
                if (registryQueryService == null) {
                    registryQueryService = SpringContextUtil.getBean(IRegistryQueryService.class);
                }
            }
        }
        return registryQueryService;
    }
}
