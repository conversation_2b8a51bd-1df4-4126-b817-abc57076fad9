/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.service.config;

import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.config.HistoryInputVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import io.swagger.annotations.Api;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * IConfigHistoryService Class
 *
 * <AUTHOR>
 * @since 2023/3/13
 */
@Path("/configHistory")
@Api(value = "配置管理历史清单服务")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface IConfigHistoryService {
    /**
     * [全品类清单分页查询]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @POST
    @Path("/getAllCategoryList")
    ResultDataVO allCategoryList(HistoryInputVO historyInputVO) throws CommonApplicationException;

    /**
     * [规格品清单分页查询]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @POST
    @Path("/getTopItemChart")
    ResultDataVO topItemList(HistoryInputVO historyInputVO) throws CommonApplicationException;

    /**
     * [历史清单导出]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @POST
    @Path("/historyAllExport")
    ResultDataVO exportExcel(HistoryInputVO historyInputVO) throws CommonApplicationException;
}
