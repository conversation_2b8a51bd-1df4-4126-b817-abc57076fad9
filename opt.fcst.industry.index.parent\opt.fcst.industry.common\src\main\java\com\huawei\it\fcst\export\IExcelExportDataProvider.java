/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.export;

import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
 * 导出数据填充接口
 *
 * <AUTHOR>
 * @since 202407
 */
public interface IExcelExportDataProvider {
    /**
     * 明细行数据获取接口
     *
     * @param conditionObject 查询条件
     * @param pageVO          分页对象
     * @return 导出数据
     */
    List<?> getData(Serializable conditionObject, PageVO pageVO)
            throws ApplicationException, InterruptedException, ExecutionException;

    /**
     * 导出header 填充接口
     *
     * @param conditionObject 条件
     * @param parameters      自定义参数
     * @return 导出头填充信息
     * @throws ApplicationException 异常信息
     */
    Map<String, Object> getHeader(Serializable conditionObject, Map<String, Object> parameters)
            throws ApplicationException;

}
