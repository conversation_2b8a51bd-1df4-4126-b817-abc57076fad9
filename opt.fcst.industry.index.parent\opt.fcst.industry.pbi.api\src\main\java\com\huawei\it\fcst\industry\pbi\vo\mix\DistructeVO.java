/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.mix;

import com.huawei.it.fcst.annotations.ExportAttribute;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * DistructeVO Class
 *
 * <AUTHOR>
 * @since 2024/10/16
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DistructeVO {

    @ExportAttribute(sort = 0)
    private String periodId;

    @ExportAttribute(sort = 1)
    private Double pspRmbCostAmt = 0.0D;

    @ExportAttribute(sort = 2)
    private Double stdRmbCostAmt = 0.0D;

    @ExportAttribute(sort = 3)
    private String gapPspStd;

    @ExportAttribute(sort = 4)
    private String ratioPspStd;
}
