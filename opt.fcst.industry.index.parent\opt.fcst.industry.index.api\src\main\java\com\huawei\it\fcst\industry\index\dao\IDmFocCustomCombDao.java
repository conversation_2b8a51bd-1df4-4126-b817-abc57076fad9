/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.combination.CombTransformVO;
import com.huawei.it.fcst.industry.index.vo.combination.CombinationVO;
import com.huawei.it.fcst.industry.index.vo.combination.DmCustomCombVO;
import com.huawei.it.fcst.industry.index.vo.common.CommonViewVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * IDmFocCustomCombDao Class
 *
 * <AUTHOR>
 * @since 2023/8/1
 */
public interface IDmFocCustomCombDao {

    Long getCustomIdKey(CombinationVO combinationVO);

    Long getManufactureCustomIdKey(CombinationVO combinationVO);

    void createCustomcombList(List<DmCustomCombVO> customVOList);

    void createManufactureCustomcombList(List<DmCustomCombVO> customVOList);

    void createEnergyCustomcombList(List<DmCustomCombVO> customVOList);

    void createEnergyManufactureCustomcombList(List<DmCustomCombVO> customVOList);

    void createIasCustomcombList(List<DmCustomCombVO> customVOList);

    void createIasManufactureCustomcombList(List<DmCustomCombVO> customVOList);

    void renameCombination(CombinationVO combinationVO);

    void updateCombination(@Param("customId")Long customId,  @Param("list") List<CombinationVO> combinationList);

    List<DmCustomCombVO> getCustomCombListByPage(CombinationVO combinationVO);

    List<DmFocViewInfoVO> getCombinationList(CommonViewVO commonViewVO);

    void deleteCustomList(CombinationVO combinationVO);

    List<DmCustomCombVO> getCustomCombList(CombinationVO combinationVO);

    List<DmCustomCombVO> getEnergyCustomCombList(CombinationVO combinationVO);

    void updateCustomSubEnable(List<DmCustomCombVO> customCodeSubEnableList);

    void updateCustomSubEnableList(@Param("combinationVO") CombinationVO combinationVO, @Param("list")List<DmCustomCombVO> customCodeSubEnableList);

    void updateCustomEnableByCustomId(CombinationVO combinationVO);

    void updateCustomEnable(Set<Long> enableCustomIdList);

    void updateCustomNoEnable(Set<Long> noEnableCustomIdList);

    void updateCustomAllEnableFlag(List<Long> enableCustomIdList);

    List<DmCustomCombVO> getCustomCombNameList(CombinationVO combinationVO);

    List<DmCustomCombVO> getCustomCombListByName(CombinationVO combinationVO);

    List<DmFocViewInfoVO> prodTeamCodeForGeneralMonth(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> prodTeamCodeForGeneralAnnual(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> prodTeamCodeForProfitMonth(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> prodTeamCodeForProfitAnnual(CommonViewVO commonViewVO);

    List<DmCustomCombVO> getCustomCodeList(@Param("customId") Long customId);

    void updateCustomSubEnableForNextGroupLevel(List<DmCustomCombVO> customCodeSubEnableList);

    List<DmFocViewInfoVO> prodTeamCodeForDimensionMonth(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> prodTeamCodeForDimensionAnnual(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> getParentCodeListGeneralAnnual(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> getParentCodeListGeneralMonth(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> getParentCodeListProfitAnnual(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> getParentCodeListProfitMonth(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> getParentCodeListDimensionAnnual(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> getParentCodeListDimensionMonth(CommonViewVO commonViewVO);

    String cusViewAnnualCost(CombTransformVO combTransformVO);

    String cusItemDtlDecode(CombTransformVO combTransformVO);

    String cusItemAppend(CombTransformVO combTransformVO);

    String customAnnual(CombTransformVO combTransformVO);

    String customMonthComb(CombTransformVO combTransformVO);

    void updateCustomSeparateList(List<DmCustomCombVO> dmCustomCombList);

    void updateCustomSeparate(CombinationVO combinationVO);

    void updateCustomPageFlag(CombinationVO combinationVO);

    List<DmCustomCombVO> groupCodeForGeneralAnnualList(CombinationVO combinationVO);

    List<DmCustomCombVO> groupCodeForProfitAnnualList(CombinationVO combinationVO);

    List<DmCustomCombVO> groupCodeForDimensionAnnualList(CombinationVO combinationVO);

    List<DmCustomCombVO> groupCodeForGeneralMonthList(CombinationVO combinationVO);

    List<DmCustomCombVO> groupCodeForProfitMonthList(CombinationVO combinationVO);

    List<DmCustomCombVO> groupCodeForDimensionMonthList(CombinationVO combinationVO);

    List<DmCustomCombVO> getManufactureCustomCombList(CombinationVO combinationVO);

    List<DmCustomCombVO> getManufactureEnergyCustomCombList(CombinationVO combinationVO);

    void updateManufacutreCustomSubEnableList(@Param("combinationVO") CombinationVO combinationVO,@Param("list") List<DmCustomCombVO> diffGroupCodeList);

    List<Long> getAllCustomCombList(CombinationVO combinationVO);
}
