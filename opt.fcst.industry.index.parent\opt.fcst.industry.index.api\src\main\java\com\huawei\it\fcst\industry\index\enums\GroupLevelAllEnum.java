/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.enums;

/**
 * GroupLevelEnumAll Class
 *
 * <AUTHOR>
 * @since 2023/11/2
 */
public enum GroupLevelAllEnum {
    LV0("LV0", "LV0"),
    LV1("LV1", "重量级团队LV1"),
    LV2("LV2", "重量级团队LV2"),
    LV3("LV3", "重量级团队LV3"),
    LV4("LV4", "重量级团队LV3.5"),
    L1("L1", "颗粒度L1"),
    L2("L2", "颗粒度L2"),
    COA("COA", "COA"),
    DIMENSION("DIMENSION", "量纲"),
    SUBCATEGORY("SUBCATEGORY", "量纲子类"),
    SUB_DETAIL("SUB_DETAIL", "量纲子类明细"),
    SPART("SPART", "SPART"),
    CEG("CEG", "专项采购认证部"),
    MODL("MODL", "模块"),
    CATEGORY("CATEGORY", "品类"),
    SHIPPING_OBJECT("SHIPPING_OBJECT", "发货对象"),
    MANUFACTURE_OBJECT("MANUFACTURE_OBJECT", "制造对象"),
    ITEM("ITEM", "ITEM");

    private String value;
    private String name;

    GroupLevelAllEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static GroupLevelAllEnum getInstance(String key) {
        for (GroupLevelAllEnum value : GroupLevelAllEnum.values()) {
            if (value.getValue().equalsIgnoreCase(key)) {
                return value;
            }
        }
        return null;
    }
}
