<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.price.dao.IDmRawDataExamineDao">
    <resultMap type="com.huawei.it.fcst.industry.price.vo.config.DmRawDataExamineDTO" id="resultMap">
        <result property="versionId" column="version_id"/>
        <result property="version" column="version"/>
        <result property="lv1Code" column="lv1_code"/>
        <result property="lv1CnName" column="lv1_cn_name"/>
        <result property="lv2Code" column="lv2_code"/>
        <result property="lv2CnName" column="lv2_cn_name"/>
        <result property="lv3Code" column="lv3_code"/>
        <result property="lv3CnName" column="lv3_cn_name"/>
        <result property="lv4Code" column="lv4_code"/>
        <result property="lv4CnName" column="lv4_cn_name"/>
        <result property="overseaFlag" column="oversea_flag"/>
        <result property="bgCode" column="bg_code"/>
        <result property="bgCnName" column="bg_cn_name"/>
        <result property="bgCode" column="bg_code"/>
        <result property="bgCnName" column="bg_cn_name"/>
        <result property="regionCode" column="region_code"/>
        <result property="regionCnName" column="region_cn_name"/>
        <result property="repofficeCode" column="repoffice_code"/>
        <result property="repofficeCnName" column="repoffice_cn_name"/>
        <result property="spartCode" column="spart_code"/>
        <result property="hwContractNum" column="hw_contract_num"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="begin" column="begin"/>
        <result property="end" column="end"/>
        <result property="beginDate" column="begin_date"/>
        <result property="endDate" column="end_date"/>
        <result property="modifyReason" column="modify_reason"/>
        <result property="modifyType" column="modify_type"/>
        <result property="lv1ProdListCode" column="lv1_prod_list_code"/>
        <result property="lv1ProdListCnName" column="lv1_prod_list_cn_name"/>
        <result property="lv2ProdListCode" column="lv2_prod_list_code"/>
        <result property="lv2ProdListCnName" column="lv2_prod_list_cn_name"/>
        <result property="lv3ProdListCode" column="lv3_prod_list_code"/>
        <result property="lv3ProdListCnName" column="lv3_prod_list_cn_name"/>
        <result property="lv4ProdListCode" column="lv4_prod_list_code"/>
        <result property="lv4ProdListCnName" column="lv4_prod_list_cn_name"/>
        <result property="signTopCustCategoryCode" column="sign_top_cust_category_code"/>
        <result property="signTopCustCategoryCnName" column="sign_top_cust_category_cn_name"/>
        <result property="signSubsidiaryCustcatgCnName" column="sign_subsidiary_custcatg_cn_name"/>
    </resultMap>

    <sql id ="searchResultWhere">
        <if test='regionCode!=null and regionCode!=""'>
            and region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode!=null and repofficeCode!=""'>
            and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='bgCode!=null and bgCode!=""'>
            and bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag!=null and overseaFlag!=""'>
            and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='signTopCustCategoryCode != null and signTopCustCategoryCode != ""'>
            and sign_top_cust_category_code = #{signTopCustCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='signSubsidiaryCustcatgCnName != null and signSubsidiaryCustcatgCnName != ""'>
            and sign_subsidiary_custcatg_cn_name = #{signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
        </if>
        <if test='spartCode!=null and spartCode!=""'>
            and spart_code = #{spartCode,jdbcType=VARCHAR}
        </if>
        <if test='lv1Code != null and lv1Code != ""'>
            and lv1_prod_list_code = #{lv1Code,jdbcType=VARCHAR}
        </if>
        <if test='lv2Code != null and lv2Code != ""'>
            and lv2_prod_list_code = #{lv2Code,jdbcType=VARCHAR}
        </if>
        <if test='lv3Code != null and lv3Code != ""'>
            and lv3_prod_list_code = #{lv3Code,jdbcType=VARCHAR}
        </if>
        <if test='lv4Code != null and lv4Code != ""'>
            and lv4_prod_list_code = #{lv4Code,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel == "OVERSEA"'>
            and oversea_flag is not null
        </if>
        <if test='groupLevel == "REGION"'>
            and region_code is not null
        </if>
        <if test='groupLevel == "REPOFFICE"'>
            and repoffice_code is not null
        </if>
        <if test='groupLevel == "SIGN_TOP"'>
            and sign_top_cust_category_code is not null
        </if>
        <if test='groupLevel == "SIGN_SUB"'>
            and sign_subsidiary_custcatg_cn_name is not null
        </if>
    </sql>

    <select id="findRawDataExamineList" resultMap="resultMap">
        select distinct
        <if test='groupLevel != null and groupLevel != "" and groupLevel == "LV1"'>
            lv1_prod_list_code as lv1_code,
            lv1_prod_list_cn_name as lv1_cn_name
        </if>
        <if test='groupLevel != null and groupLevel != "" and groupLevel == "LV2"'>
            lv2_prod_list_code as lv2_code,
            LV2_PROD_LIST_CN_NAME as lv2_cn_name
        </if>
        <if test='groupLevel != null and groupLevel != "" and groupLevel == "LV3"'>
            lv3_prod_list_code as lv3_code,
            LV3_PROD_LIST_CN_NAME as lv3_cn_name
        </if>
        <if test='groupLevel != null and groupLevel != "" and groupLevel == "LV4"'>
            lv4_prod_list_code as lv4_code,
            LV4_PROD_LIST_CN_NAME as lv4_cn_name
        </if>
        <if test='groupLevel != null and groupLevel != "" and groupLevel == "CONTRACT_NUM"'>
            hw_contract_num
        </if>
        <if test='groupLevel != null and groupLevel != "" and groupLevel == "REGION"'>
            region_code,region_cn_name
        </if>
        <if test='groupLevel != null and groupLevel != "" and groupLevel == "REPOFFICE"'>
            repoffice_code,repoffice_cn_name
        </if>
        <if test='groupLevel != null and groupLevel != "" and groupLevel == "SIGN_TOP"'>
            sign_top_cust_category_code, sign_top_cust_category_cn_name
        </if>
        <if test='groupLevel != null and groupLevel != "" and groupLevel == "SIGN_SUB"'>
            sign_subsidiary_custcatg_cn_name
        </if>
        from fin_dm_opt_foi.dm_fcst_price_raw_data_examine_t
        where del_flag = 'N'
        <include refid="searchResultWhere"/>
    </select>

    <select id="findExamineDropDownList" resultMap="resultMap">
        select distinct
        <if test='groupLevel == "OVERSEA"'>
            oversea_flag
        </if>
        <if test='groupLevel == "BG"'>
            bg_code,bg_cn_name
        </if>
        <if test='groupLevel == "REGION"'>
            region_code,region_cn_name
        </if>
        <if test='groupLevel == "REPOFFICE"'>
            repoffice_code,repoffice_cn_name
        </if>
        <if test='groupLevel != null and groupLevel != "" and groupLevel == "CONTRACT_NUM"'>
            hw_contract_num
        </if>
        <if test='groupLevel == "SIGN_TOP"'>
            sign_top_cust_category_code, sign_top_cust_category_cn_name
        </if>
        <if test='groupLevel == "SIGN_SUB"'>
            sign_subsidiary_custcatg_cn_name
        </if>
        <if test='groupLevel != null and groupLevel != "" and groupLevel == "LV1"'>
            lv1_prod_list_code as lv1_code,
            lv1_prod_list_cn_name as lv1_cn_name
        </if>
        <if test='groupLevel != null and groupLevel != "" and groupLevel == "LV2"'>
            lv2_prod_list_code as lv2_code,
            lv2_prod_list_cn_name as lv2_cn_name
        </if>
        <if test='groupLevel != null and groupLevel != "" and groupLevel == "LV3"'>
            lv3_prod_list_code as lv3_code,
            lv3_prod_list_cn_name as lv3_cn_name
        </if>
        <if test='groupLevel != null and groupLevel != "" and groupLevel == "LV4"'>
            lv4_prod_list_code as lv4_code,
            lv4_prod_list_cn_name as lv4_cn_name
        </if>
        <if test='groupLevel != null and groupLevel != "" and groupLevel == "SPART"'>
            spart_code
        </if>
        from fin_dm_opt_foi.dm_fcst_price_examine_result_t
        where del_flag = 'N'
        <if test='versionId!=null'>
            and version_id = #{versionId}
        </if>
        <include refid="searchResultWhere"/>
    </select>

    <select id="findSpartCodeByPageList" resultMap="resultMap">
        SELECT DISTINCT spart_code
        from fin_dm_opt_foi.dm_fcst_price_raw_data_examine_t
       <include refid="spartSearchParamWhere"></include>
        order by spart_code
        LIMIT #{pageVO.pageSize} OFFSET #{pageVO.mysqlStartIndex}
    </select>

    <sql id ="spartSearchParamWhere">
        where del_flag = 'N'
        <if test='searchVO.keyword != null and searchVO.keyword != ""'>
            AND spart_code LIKE '%'||#{searchVO.keyword} ||'%'
        </if>
        <if test='searchVO.regionCode!=null and searchVO.regionCode!=""'>
            and region_code = #{searchVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.repofficeCode!=null and searchVO.repofficeCode!=""'>
            and repoffice_code = #{searchVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.bgCode!=null and searchVO.bgCode!=""'>
            and bg_code = #{searchVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.overseaFlag!=null and searchVO.overseaFlag!=""'>
            and oversea_flag = #{searchVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.signTopCustCategoryCode != null and searchVO.signTopCustCategoryCode != ""'>
            and sign_top_cust_category_code = #{searchVO.signTopCustCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.signSubsidiaryCustcatgCnName != null and searchVO.signSubsidiaryCustcatgCnName != ""'>
            and sign_subsidiary_custcatg_cn_name = #{searchVO.signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.lv1Code != null and searchVO.lv1Code != ""'>
            and lv1_prod_list_code = #{searchVO.lv1Code,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.lv2Code != null and searchVO.lv2Code != ""'>
            and lv2_prod_list_code = #{searchVO.lv2Code,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.lv3Code != null and searchVO.lv3Code != ""'>
            and lv3_prod_list_code = #{searchVO.lv3Code,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.lv4Code != null and searchVO.lv4Code != ""'>
            and lv4_prod_list_code = #{searchVO.lv4Code,jdbcType=VARCHAR}
        </if>
    </sql>
        
        
    <select id="findSpartCodeByPageListCount" resultType="int">
        SELECT count(1) from (SELECT DISTINCT spart_code 
        from fin_dm_opt_foi.dm_fcst_price_raw_data_examine_t
        <include refid="spartSearchParamWhere"></include>)
    </select>

    <sql id="allFields">
        t1.version_id,
        oversea_flag,
        hw_contract_num,
        bg_code,
        bg_cn_name,
        region_code,
        region_cn_name,
        repoffice_code,
        repoffice_cn_name,
        sign_top_cust_category_code,
	    sign_top_cust_category_cn_name,
	    sign_subsidiary_custcatg_cn_name,
        spart_code,
        begin_date,
        end_date,
        modify_reason,
        modify_type,
        del_flag,
        last_updated_by,
        last_update_date,
        created_by,
        creation_date,
        lv1_prod_list_code as lv1_code,
        lv1_prod_list_cn_name as lv1_cn_name,
        lv2_prod_list_code as lv2_code,
        lv2_prod_list_cn_name as lv2_cn_name,
        lv3_prod_list_code as lv3_code,
        lv3_prod_list_cn_name as lv3_cn_name,
        lv4_prod_list_code as lv4_code,
        lv4_prod_list_cn_name as lv4_cn_name
    </sql>

    <sql id="allInsertFields">
        version_id,
        sign_top_cust_category_code,
        sign_top_cust_category_cn_name,
        sign_subsidiary_custcatg_cn_name,
        hw_contract_num,
        oversea_flag,
        bg_code,
        bg_cn_name,
        region_code,
        region_cn_name,
        repoffice_code,
        repoffice_cn_name,
        spart_code,
        begin_date,
        end_date,
        modify_reason,
        modify_type,
        created_by,
        creation_date,
        last_updated_by,
        last_update_date,
        del_flag,
        page_flag,
        lv1_prod_list_code,
        lv1_prod_list_cn_name,
        lv2_prod_list_code,
        lv2_prod_list_cn_name,
        lv3_prod_list_code,
        lv3_prod_list_cn_name,
        lv4_prod_list_code,
        lv4_prod_list_cn_name
    </sql>

    <sql id="allProdFields">
        lv1_prod_list_code,
        lv1_prod_list_cn_name,
        lv2_prod_list_code,
        lv2_prod_list_cn_name,
        lv3_prod_list_code,
        lv3_prod_list_cn_name,
        lv4_prod_list_code,
        lv4_prod_list_cn_name
    </sql>

    <select id="findProdCodeRawDataExamineList" resultMap="resultMap">
        select
        DISTINCT bg_code,bg_cn_name,
        <include refid="allProdFields"/>
        from fin_dm_opt_foi.dm_fcst_price_raw_data_examine_t
        where del_flag = 'N'
        <if test='annualVersionId != null'>
            and version_id = #{annualVersionId}
        </if>
    </select>

    <select id="findOtherRawDataExamineList" resultMap="resultMap">
        select
        DISTINCT
        bg_code,
        bg_cn_name,
        region_code,
        region_cn_name,
        repoffice_code,
        repoffice_cn_name,
        oversea_flag,
        sign_top_cust_category_code,
        sign_top_cust_category_cn_name,
        sign_subsidiary_custcatg_cn_name
        from fin_dm_opt_foi.dm_fcst_price_raw_data_examine_t
        where del_flag = 'N'
        <if test='annualVersionId != null'>
            and version_id = #{annualVersionId}
        </if>

    </select>

    <select id="findSpartDataExamineCount" resultType="java.lang.Long">
        select count(1) from (
        select
        DISTINCT
        spart_code
        from fin_dm_opt_foi.dm_fcst_price_raw_data_examine_t
        where del_flag = 'N'
        <if test='annualVersionId != null'>
            and version_id = #{annualVersionId}
        </if>
        <if test='spartCodeSet != null and spartCodeSet.size() > 0'>
            <foreach collection='spartCodeSet' item="code" open="AND spart_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='spartCodeSet == null or spartCodeSet.size() == 0'>
            and spart_code = null
        </if>
        )
    </select>

    <select id="findContractExamineCount" resultType="java.lang.Long">
        select count(1) from (
        select
        DISTINCT
        hw_contract_num
        from fin_dm_opt_foi.dm_fcst_price_raw_data_examine_t
        where del_flag = 'N'
        <if test='annualVersionId != null'>
            and version_id = #{annualVersionId}
        </if>
        <if test='hwContractNumSet != null and hwContractNumSet.size() > 0'>
            <foreach collection='hwContractNumSet' item="contractNum" open="and hw_contract_num IN (" close=")" index="index"
                     separator=",">
                #{contractNum}
            </foreach>
        </if>
        <if test='hwContractNumSet == null or hwContractNumSet.size() == 0'>
            and hw_contract_num = null
        </if>
        )
    </select>

    <select id="findDataReviewListByPage" resultMap="resultMap">
        select t2.version,
        <include refid="allFields"/>
        from fin_dm_opt_foi.dm_fcst_price_examine_result_t t1
        left join (SELECT version_id, version
        FROM fin_dm_opt_foi.dm_fcst_price_version_info_t
        WHERE del_flag = 'N'
        AND data_type='DATA_REVIEW') t2
        ON t1.version_id = t2.version_id
        where t1.del_flag = 'N'
        <if test='reviewVO.versionId != null'>
            and t1.version_id = #{reviewVO.versionId}
        </if>
        <if test='reviewVO.modifyType != null and reviewVO.modifyType != ""'>
            and t1.modify_type = #{reviewVO.modifyType}
        </if>
        <if test='reviewVO.pageFlag != null and reviewVO.pageFlag != ""'>
            and t1.page_flag = #{reviewVO.pageFlag}
        </if>
        <if test='reviewVO.overseaFlag != null and reviewVO.overseaFlag != ""'>
            and t1.oversea_flag= #{reviewVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='reviewVO.bgCode != null and reviewVO.bgCode != ""'>
            and t1.bg_code = #{reviewVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='reviewVO.regionCode != null and reviewVO.regionCode != ""'>
            and t1.region_code = #{reviewVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='reviewVO.repofficeCode != null and reviewVO.repofficeCode != ""'>
            and t1.repoffice_code = #{reviewVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='reviewVO.hwContractNum != null and reviewVO.hwContractNum != ""'>
            and t1.hw_contract_num = #{reviewVO.hwContractNum,jdbcType=VARCHAR}
        </if>
        <if test='reviewVO.signTopCustCategoryCode != null and reviewVO.signTopCustCategoryCode != ""'>
            and t1.sign_top_cust_category_code = #{reviewVO.signTopCustCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='reviewVO.signSubsidiaryCustcatgCnName != null and reviewVO.signSubsidiaryCustcatgCnName != ""'>
            and t1.sign_subsidiary_custcatg_cn_name = #{reviewVO.signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
        </if>
        <if test='reviewVO.pageFlag!= null and reviewVO.pageFlag == "abnormal"'>
            AND t1.modify_type !='REVOKE'
        </if>
        <if test='reviewVO.lv1Code != null and reviewVO.lv1Code != ""'>
            AND t1.lv1_prod_list_code = #{reviewVO.lv1Code,jdbcType=VARCHAR}
        </if>
        <if test='reviewVO.lv2Code != null and reviewVO.lv2Code != ""'>
            and t1.lv2_prod_list_code = #{reviewVO.lv2Code,jdbcType=VARCHAR}
        </if>
        <if test='reviewVO.lv3Code != null and reviewVO.lv3Code != ""'>
            and t1.lv3_prod_list_code = #{reviewVO.lv3Code,jdbcType=VARCHAR}
        </if>
        <if test='reviewVO.lv4Code != null and reviewVO.lv4Code != ""'>
            and t1.lv4_prod_list_code = #{reviewVO.lv4Code,jdbcType=VARCHAR}
        </if>
        <if test='reviewVO.spartCode != null and reviewVO.spartCode != ""'>
            and t1.spart_code = #{reviewVO.spartCode,jdbcType=VARCHAR}
        </if>
        order by last_update_date desc
        LIMIT #{pageVO.pageSize} OFFSET #{pageVO.mysqlStartIndex}
    </select>

    <select id="findDataReviewListByPageCount" resultType="int">
        select count(1) from (
        select t2.version,
        <include refid="allFields"/>
        from fin_dm_opt_foi.dm_fcst_price_examine_result_t t1
        left join (SELECT version_id, version
        FROM fin_dm_opt_foi.dm_fcst_price_version_info_t
        WHERE del_flag = 'N'
        AND data_type='DATA_REVIEW') t2
        ON t1.version_id = t2.version_id
        where t1.del_flag = 'N'
        <if test='reviewVO.versionId != null'>
            and t1.version_id = #{reviewVO.versionId}
        </if>
        <if test='reviewVO.modifyType != null and reviewVO.modifyType != ""'>
            and t1.modify_type = #{reviewVO.modifyType}
        </if>
        <if test='reviewVO.pageFlag != null and reviewVO.pageFlag != ""'>
            and t1.page_flag = #{reviewVO.pageFlag}
        </if>
        <if test='reviewVO.overseaFlag != null and reviewVO.overseaFlag != ""'>
            and t1.oversea_flag= #{reviewVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='reviewVO.bgCode != null and reviewVO.bgCode != ""'>
            and t1.bg_code = #{reviewVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='reviewVO.regionCode != null and reviewVO.regionCode != ""'>
            and t1.region_code = #{reviewVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='reviewVO.repofficeCode != null and reviewVO.repofficeCode != ""'>
            and t1.repoffice_code = #{reviewVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='reviewVO.hwContractNum != null and reviewVO.hwContractNum != ""'>
            and t1.hw_contract_num = #{reviewVO.hwContractNum,jdbcType=VARCHAR}
        </if>
        <if test='reviewVO.signTopCustCategoryCode != null and reviewVO.signTopCustCategoryCode != ""'>
            and t1.sign_top_cust_category_code = #{reviewVO.signTopCustCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='reviewVO.signSubsidiaryCustcatgCnName != null and reviewVO.signSubsidiaryCustcatgCnName != ""'>
            and t1.sign_subsidiary_custcatg_cn_name = #{reviewVO.signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
        </if>
        <if test='reviewVO.pageFlag!= null and reviewVO.pageFlag == "abnormal"'>
            AND t1.modify_type !='REVOKE'
        </if>
        <if test='reviewVO.modifyType != null and reviewVO.modifyType != ""'>
            AND t1.modify_type = #{reviewVO.modifyType,jdbcType=VARCHAR}
        </if>
        <if test='reviewVO.lv1Code != null and reviewVO.lv1Code != ""'>
            and t1.lv1_prod_list_code = #{reviewVO.lv1Code,jdbcType=VARCHAR}
        </if>
        <if test='reviewVO.lv2Code != null and reviewVO.lv2Code != ""'>
            and t1.lv2_prod_list_code = #{reviewVO.lv2Code,jdbcType=VARCHAR}
        </if>
        <if test='reviewVO.lv3Code != null and reviewVO.lv3Code != ""'>
            and t1.lv3_prod_list_code = #{reviewVO.lv3Code,jdbcType=VARCHAR}
        </if>
        <if test='reviewVO.lv4Code != null and reviewVO.lv4Code != ""'>
            and t1.lv4_prod_list_code = #{reviewVO.lv4Code,jdbcType=VARCHAR}
        </if>
        <if test='reviewVO.spartCode != null and reviewVO.spartCode != ""'>
            and t1.spart_code = #{reviewVO.spartCode,jdbcType=VARCHAR}
        </if>
        )
    </select>

    <select id="findAllDataReviewList" resultMap="resultMap">
        select
        <include refid="allFields"/>
        from fin_dm_opt_foi.dm_fcst_price_examine_result_t t1
        where t1.del_flag = 'N'
        <if test='reviewVO.versionId != null'>
            and t1.version_id = #{reviewVO.versionId}
        </if>
        <if test='reviewVO.overseaFlag != null and reviewVO.overseaFlag != ""'>
            and t1.oversea_flag= #{reviewVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='reviewVO.bgCode != null and reviewVO.bgCode != ""'>
            and t1.bg_code = #{reviewVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='reviewVO.regionCode != null and reviewVO.regionCode != ""'>
            and t1.region_code = #{reviewVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='reviewVO.repofficeCode != null and reviewVO.repofficeCode != ""'>
            and t1.repoffice_code = #{reviewVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='reviewVO.hwContractNum != null and reviewVO.hwContractNum != ""'>
            and t1.hw_contract_num = #{reviewVO.hwContractNum,jdbcType=VARCHAR}
        </if>
        <if test='reviewVO.signTopCustCategoryCode != null and reviewVO.signTopCustCategoryCode != ""'>
            and t1.sign_top_cust_category_code = #{reviewVO.signTopCustCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='reviewVO.signSubsidiaryCustcatgCnName != null and reviewVO.signSubsidiaryCustcatgCnName != ""'>
            and t1.sign_subsidiary_custcatg_cn_name = #{reviewVO.signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
        </if>
        AND t1.page_flag ='abnormal'
        AND t1.modify_type !='REVOKE'
    </select>

    <insert id="createPriceDataReviewList">
        <foreach collection="list" item="item" separator=";">
            INSERT INTO fin_dm_opt_foi.dm_fcst_price_examine_result_t
            (<include refid="allInsertFields"/>)
            VALUES
            (
            #{item.versionId,jdbcType=NUMERIC},
            #{item.signTopCustCategoryCode,jdbcType=VARCHAR},
            #{item.signTopCustCategoryCnName,jdbcType=VARCHAR},
            #{item.signSubsidiaryCustcatgCnName,jdbcType=VARCHAR},
            #{item.hwContractNum,jdbcType=VARCHAR},
            #{item.overseaFlag,jdbcType=VARCHAR},
            #{item.bgCode,jdbcType=VARCHAR},
            #{item.bgCnName,jdbcType=VARCHAR},
            #{item.regionCode,jdbcType=VARCHAR},
            #{item.regionCnName,jdbcType=VARCHAR},
            #{item.repofficeCode,jdbcType=VARCHAR},
            #{item.repofficeCnName,jdbcType=VARCHAR},
            #{item.spartCode,jdbcType=VARCHAR},
            #{item.beginDate,jdbcType=NUMERIC},
            #{item.endDate,jdbcType=NUMERIC},
            #{item.modifyReason,jdbcType=VARCHAR},
            #{item.modifyType,jdbcType=VARCHAR},
            #{item.createdBy,jdbcType=VARCHAR},
            #{item.creationDate,jdbcType=TIMESTAMP},
            #{item.lastUpdatedBy,jdbcType=VARCHAR},
            #{item.lastUpdateDate,jdbcType=TIMESTAMP},
            #{item.delFlag,jdbcType=VARCHAR},
            #{item.pageFlag,jdbcType=VARCHAR},
            #{item.lv1Code,jdbcType=VARCHAR},
            #{item.lv1CnName,jdbcType=VARCHAR},
            #{item.lv2Code,jdbcType=VARCHAR},
            #{item.lv2CnName,jdbcType=VARCHAR},
            #{item.lv3Code,jdbcType=VARCHAR},
            #{item.lv3CnName,jdbcType=VARCHAR},
            #{item.lv4Code,jdbcType=VARCHAR},
            #{item.lv4CnName,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <select id="findPriceBeginEndDate" resultMap="resultMap">
        with temp_date as (
            SELECT SUBSTR(REPLACE(last_update_date,'-',''), 0, 4 ) as now_date
            FROM fin_dm_opt_foi.DM_FCST_PRICE_TOP_SPART_INFO_T
            WHERE del_flag = 'N'
              AND is_top_flag = 'Y'
              AND version_id = (
                SELECT version_id
                FROM fin_dm_opt_foi.dm_fcst_price_version_info_t
                WHERE del_flag = 'N'
                  AND is_running = 'N'
                  AND status = 1
                  AND data_type = 'MONTH'
                  AND version_type IN ('AUTO', 'FINAL')
                ORDER BY creation_date DESC
            LIMIT 1
            )
        ORDER BY creation_date DESC
            LIMIT 1 OFFSET 0)
        select concat(now_date-3,'01') as begin, concat(now_date,'12') as end from temp_date
    </select>
</mapper>
