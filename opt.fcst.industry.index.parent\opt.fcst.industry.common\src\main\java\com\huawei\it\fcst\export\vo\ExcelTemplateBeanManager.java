/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.export.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 导出模板信息
 *
 * @since 202407
 */
@Getter
@Setter
public class ExcelTemplateBeanManager implements Serializable {

    static final long serialVersionUID = -6430539695959161871L;

    // 模板名称
    private String templateName;

    // 模块
    private String moduleType;

    // 描述
    private String desc;

    // sheet 和 java beans关系
    private List<SheetBeanMetaVO> sheetBeans;

    /**
     * 默认构造器
     */
    public ExcelTemplateBeanManager() {

    }

    /**
     * excel 模板构建
     *
     * @param templateName 模板名称
     * @param moduleType   模块类型
     * @param desc         描述
     * @param sheetBeans   sheetBeans
     */
    public ExcelTemplateBeanManager(String templateName, String moduleType, String desc,
            List<SheetBeanMetaVO> sheetBeans) {
        this.templateName = templateName;
        this.moduleType = moduleType;
        this.desc = desc;
        this.sheetBeans = sheetBeans;
    }
}
