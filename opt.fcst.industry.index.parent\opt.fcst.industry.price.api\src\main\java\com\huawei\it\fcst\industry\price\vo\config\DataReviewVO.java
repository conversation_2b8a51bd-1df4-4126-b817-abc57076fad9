/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.vo.config;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * DataReviewVO Class
 *
 * <AUTHOR>
 * @since 2024/9/10
 */
@Data
@NoArgsConstructor
@ApiModel(value = "底层数据审视入参VO")
public class DataReviewVO implements Serializable {

    private static final long serialVersionUID = 5877161359390939921L;

    @ApiModelProperty("操作记录列表")
    private List<DmRawDataExamineDTO> reviewList;

    @ApiModelProperty("操作类型")
    private String modifyType;

    @ApiModelProperty("页面")
    private String pageType;

    @ApiModelProperty("版本id")
    private Long versionId;

    @ApiModelProperty("国内/海外")
    private Long overseaFlag;

    @ApiModelProperty("bg编码")
    private Long bgCode;

    @ApiModelProperty("地区处编码")
    private Long regionCode;

    @ApiModelProperty("代表处编码")
    private Long repofficeCode;

    @ApiModelProperty("合同号")
    private Long hwContractNum;

    private int num;

    private String signTopCustCategoryCode;

    private String signSubsidiaryCustcatgCnName;

    private Map<String, List<DmRawDataExamineDTO>> remokeMap;

}
