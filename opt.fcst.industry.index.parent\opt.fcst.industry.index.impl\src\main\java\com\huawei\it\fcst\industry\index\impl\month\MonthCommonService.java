/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.month;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.huawei.it.fcst.industry.index.config.ExecutorConfig;
import com.huawei.it.fcst.industry.index.constant.CommonConstant;
import com.huawei.it.fcst.industry.index.constant.Constant;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeMonthYoyDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeRecMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMonthYoyDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocRecMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTotalMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTotalMonthYoyDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocVersionDao;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumD;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumMadeD;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumP;
import com.huawei.it.fcst.industry.index.enums.IndustryIndexEnum;
import com.huawei.it.fcst.industry.index.enums.ResultCodeEnum;
import com.huawei.it.fcst.industry.index.impl.annual.AnnualCommonService;
import com.huawei.it.fcst.industry.index.service.common.ICommonService;
import com.huawei.it.fcst.industry.index.utils.FcstIndexMadeUtil;
import com.huawei.it.fcst.industry.index.utils.FcstIndexUtil;
import com.huawei.it.fcst.industry.index.utils.ObjectCopyUtil;
import com.huawei.it.fcst.industry.index.vo.common.DataPermissionsVO;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.month.DmFocActualCostVO;
import com.huawei.it.fcst.industry.index.vo.month.DmFocMonthCostIdxVO;
import com.huawei.it.fcst.industry.index.vo.month.DmFocMonthWeightVO;
import com.huawei.it.fcst.industry.index.vo.month.DmFocMonthYoyVO;
import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.inject.Named;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * MonthCommonService Class
 *
 * <AUTHOR>
 * @since 2023/03/13
 */
@Slf4j
@Named("monthCommonService")
public class MonthCommonService {
    private static final Logger LOGGER = LoggerFactory.getLogger(MonthAnalysisService.class);

    @Autowired
    private IDmFocMonthCostIdxDao dmFocMonthCostIdxDao;

    @Autowired
    private IDmFocMadeMonthCostIdxDao dmFocMadeMonthCostIdxDao;

    @Autowired
    private IDmFocTotalMonthCostIdxDao dmFocTotalMonthCostIdxDao;

    @Autowired
    private IDmFocMonthYoyDao dmFocMonthYoyDao;

    @Autowired
    private IDmFocMadeMonthYoyDao dmFocMadeMonthYoyDao;

    @Autowired
    private IDmFocTotalMonthYoyDao dmFocTotalMonthYoyDao;

    @Autowired
    private ICommonService commonService;

    @Autowired
    private IDmFocRecMonthCostIdxDao dmFocRecMonthCostIdxDao;

    @Autowired
    private IDmFocMadeRecMonthCostIdxDao dmFocMadeRecMonthCostIdxDao;

    @Autowired
    private AsyncQueryService asyncQueryService;

    @Autowired
    private ExecutorConfig executorConfig;

    @Autowired
    private AnnualCommonService  annualCommonService;

    @Autowired
    private IDmFocVersionDao dmFocVersionDao;

    /**
     * 查询产业成价格指数图
     *
     * @param monthAnalysisVO 参数参数VO
     * @return ResultDataVO result data
     */
    public ResultDataVO getIndustryCostIndexChart(MonthAnalysisVO monthAnalysisVO) {
        LOGGER.info(">>>Begin MonthCommonService::getIndustryCostIndexChart");
        List<DmFocMonthCostIdxVO> priceIndexVOList = new ArrayList<>();
        // 第1步： 校验必填参数
        String industryOrg = monthAnalysisVO.getIndustryOrg();
        if (ObjectUtils.isEmpty(monthAnalysisVO.getBasePeriodId()) || CollectionUtils.isEmpty(monthAnalysisVO.getGroupCodeList())
                || StringUtils.isAnyBlank(monthAnalysisVO.getViewFlag(), industryOrg)) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        // 检查所选基期及groupCode是否有数据，若没有数据，则调用函数 F_DM_FOC_POINT_INDEX 刷新 产业成本指数(dm_foc_month_cost_idx_t)
        monthAnalysisVO.setVersionId(commonService.getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(), monthAnalysisVO.getTablePreFix()));
        // 第2步：检查基期开始时间和结束时间是否传参，若没有，则默认取报告期36个月的开始时间和结束时间
        FcstIndexUtil.handlePeriod(monthAnalysisVO, dmFocMonthCostIdxDao.findActualMonthNum(monthAnalysisVO.getTablePreFix()).toString());
        // 反向视角标识设置
        Boolean reveseViewFlag = FcstIndexUtil.isReveseViewFlag(monthAnalysisVO);
        // 勾选了汇总组合
        if (monthAnalysisVO.getIsContainComb()) {
            combinePriceIndexVOList(monthAnalysisVO, priceIndexVOList);
        } else {
            // 反向视角
            if (reveseViewFlag) {
                handleReveseViewCostIdxData(monthAnalysisVO, priceIndexVOList);
            } else {
                // 处理正向视角的产业成本指数的数据
                handleCostIndexData(monthAnalysisVO, priceIndexVOList);
            }
        }
        if (CollectionUtils.isNotEmpty(priceIndexVOList)) {
            priceIndexVOList.stream().forEach(index -> {
                if (!index.getGroupCode().equals(String.valueOf(index.getCustomId())) && (CommonConstant.GROUP_LEVEL.contains(index.getGroupLevel()))) {
                    index.setGroupCnName(index.getGroupCode() + " " + index.getGroupCnName());
                }
            });
        }
        return ResultDataVO.success(Optional.ofNullable(priceIndexVOList).orElse(new ArrayList<>()));
    }

    public void getUserPermission(MonthAnalysisVO monthAnalysisVO) {
        // 获取当前用户角色对应的数据范围
        DataPermissionsVO dimensionList = commonService
                .getDimensionList(monthAnalysisVO.getCostType(), monthAnalysisVO.getTablePreFix(), monthAnalysisVO.getIndustryOrg());
        monthAnalysisVO.setLv0DimensionSet(dimensionList.getLv0DimensionSet());
        monthAnalysisVO.setLv1DimensionSet(dimensionList.getLv1DimensionSet());
        monthAnalysisVO.setLv2DimensionSet(dimensionList.getLv2DimensionSet());
    }
    /**
     * 查询产业成价格指数图
     *
     * @param monthAnalysisVoList 参数参数VO
     * @return ResultDataVO result data
     */
    public ResultDataVO getCompareIndexChart(List<MonthAnalysisVO> monthAnalysisVoList) throws InterruptedException {
        LOGGER.info(">>>Begin MonthCommonService::getCompareIndexChart");
        IRequestContext requestContext = RequestContext.getCurrent();
        // 入参处理重量级团队
        compareIndexParams(monthAnalysisVoList);
        List<DmFocMonthCostIdxVO> priceIndexVOList = new ArrayList();
        Executor pool = executorConfig.asyncServiceExecutor();
        CountDownLatch countDownLatch = new CountDownLatch(monthAnalysisVoList.size());
        for (MonthAnalysisVO monthAnalysisVO : monthAnalysisVoList) {
            if (StringUtils.isBlank(monthAnalysisVO.getIndustryOrg())) {
                return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
            }
            Runnable runnable = () -> {
                RequestContextManager.setCurrent(requestContext);
                try {
                    // 切换基期
                    MonthAnalysisVO basePeriodIdMonthVO = new MonthAnalysisVO();
                    BeanUtils.copyProperties(monthAnalysisVO, basePeriodIdMonthVO);
                    compareChangeBasePeriodId(basePeriodIdMonthVO);
                    // 查询指数图数据
                    ResultDataVO industryCostIndexChart = getIndustryCostIndexChart(monthAnalysisVO);
                    List<DmFocMonthCostIdxVO> monthCostIdxList = (List<DmFocMonthCostIdxVO>) industryCostIndexChart.getData();
                    priceIndexVOList.addAll(monthCostIdxList);
                } catch (Exception exception) {
                    LOGGER.error("error getCompareIndexChart :{} ", exception.getLocalizedMessage());
                } finally {
                    countDownLatch.countDown();
                    RequestContextManager.removeCurrent();
                    LOGGER.info("剩余线程个数：{},当前线程名称：{}", countDownLatch.getCount(), Thread.currentThread().getName());
                }
            };
            pool.execute(runnable);
        }
        countDownLatch.await();
        return ResultDataVO.success(Optional.ofNullable(priceIndexVOList).orElse(new ArrayList<>()));
    }


    private void compareIndexParams(List<MonthAnalysisVO> monthAnalysisVolist) {
        // 入参处理重量级团队
        for (MonthAnalysisVO monthAnalysisVO : monthAnalysisVolist) {
            if (!monthAnalysisVO.getIsContainComb()) {
                String prodRndTeamCode = null;
                String teamLevel = null;
                if (StringUtils.isNotBlank(monthAnalysisVO.getLv0ProdRndTeamCode())) {
                    prodRndTeamCode = monthAnalysisVO.getLv0ProdRndTeamCode();
                    teamLevel = "LV0";
                }
                if (StringUtils.isNotBlank(monthAnalysisVO.getLv1ProdRndTeamCode())) {
                    prodRndTeamCode = monthAnalysisVO.getLv1ProdRndTeamCode();
                    teamLevel = "LV1";
                }
                if (StringUtils.isNotBlank(monthAnalysisVO.getLv2ProdRndTeamCode())) {
                    prodRndTeamCode = monthAnalysisVO.getLv2ProdRndTeamCode();
                    teamLevel = "LV2";
                }
                if (StringUtils.isNotBlank(monthAnalysisVO.getLv3ProdRndTeamCode())) {
                    prodRndTeamCode = monthAnalysisVO.getLv3ProdRndTeamCode();
                    teamLevel = "LV3";
                }
                if (StringUtils.isNotBlank(monthAnalysisVO.getLv4ProdRndTeamCode())) {
                    prodRndTeamCode = monthAnalysisVO.getLv4ProdRndTeamCode();
                    teamLevel = "LV4";
                }
                List<String> prodRndTeamCodeList = new ArrayList<>();
                prodRndTeamCodeList.add(prodRndTeamCode);
                monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
                monthAnalysisVO.setTeamLevel(teamLevel);
            } else {
                monthAnalysisVO.setProdRndTeamCodeList(monthAnalysisVO.getGroupCodeList());
            }
        }
    }

    /**
     * 对比分析切换基期
     *
     * @param monthAnalysisVO
     */
    public  void compareChangeBasePeriodId(MonthAnalysisVO monthAnalysisVO) throws CommonApplicationException {
        if (monthAnalysisVO.getVersionId() == null) {
            monthAnalysisVO.setVersionId(commonService.getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),monthAnalysisVO.getTablePreFix()));
        }
        // 分视角获取下个层级的groupLevel值
        Map map = new HashMap();
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType()) || IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostSubType())) {
            map = FcstIndexMadeUtil.getNextGroupLevel(monthAnalysisVO);
        } else {
            map = FcstIndexUtil.getNextGroupLevel(monthAnalysisVO);
        }
        monthAnalysisVO.setParentLevel(monthAnalysisVO.getGroupLevel());
        monthAnalysisVO.setGroupLevel(map.get("nextGroupLevel").toString());
        if (monthAnalysisVO.getIsContainComb() && CollectionUtils.isNotEmpty(monthAnalysisVO.getCustomIdList())) {
            monthAnalysisVO.setReverseViewFlag(false);
            // 汇总组合调用切换基期函数
            MonthAnalysisVO buildComb = new MonthAnalysisVO();
            BeanUtils.copyProperties(monthAnalysisVO, buildComb);
            distinguishIfCombine(buildComb);
            indexChangeBasePeriodId(buildComb);
        } else {
            // 反向视角和不包含汇总组合的切换基期任务判断
            changePeriodIdFlag(monthAnalysisVO);
        }
    }

    @Nullable
    private void changePeriodIdFlag(MonthAnalysisVO monthAnalysisVO) throws CommonApplicationException {
        // 反向视角标识设置
        Boolean reveseViewFlag = FcstIndexUtil.isReveseViewFlag(monthAnalysisVO);
        monthAnalysisVO.setReverseViewFlag(reveseViewFlag);
        if (reveseViewFlag) {
            // 反向视角判断是否需要切换基期
            if (reverseDataIsOk(monthAnalysisVO)) {
                return;
            }
        } else {
            // 正向视角判断是否需要切换基期
            if (isDataIsOk(monthAnalysisVO)) {
                return;
            }
        }
        // 调用函数
        compareRefreshIndustryIndexData(monthAnalysisVO);
    }

    /**
     * 对比分析指数图切换基期
     * @param searchMonthVO
     */
    public void compareRefreshIndustryIndexData(MonthAnalysisVO searchMonthVO) throws CommonApplicationException {
        LOGGER.info(">>>searchMonthVO:{}", JSON.toJSONString(searchMonthVO));
        MonthAnalysisVO monthAnalysisVO = ObjectCopyUtil.copy(searchMonthVO, MonthAnalysisVO.class);
        setParams(monthAnalysisVO);
        String successFlag;
        String totalSuccessFlag;
        // 总成本切换基期时，需要同时将采购成本和制造成本的数据也刷出来
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(monthAnalysisVO.getCostType())) {
            // 总成本
            totalSuccessFlag = dmFocMonthCostIdxDao.insertTotalPriceIdxWithFun(monthAnalysisVO);
            if (!CommonConstant.SUCCESS.equals(totalSuccessFlag) && !CommonConstant.SUCCESS_STATUS.equals(totalSuccessFlag)) {
                throw new CommonApplicationException("对比分析总成本切换基期函数调用失败");
            }
            // 采购成本
            MonthAnalysisVO paramsVO = ObjectCopyUtil.copy(monthAnalysisVO, MonthAnalysisVO.class);
            paramsVO.setCostType(IndustryIndexEnum.COST_TYPE.P.getValue());
            successFlag = dmFocMonthCostIdxDao.insertPriceIndexWithFun(paramsVO);
            if (!CommonConstant.SUCCESS.equals(successFlag) && !CommonConstant.SUCCESS_STATUS.equals(successFlag)) {
                throw new CommonApplicationException("对比分析切换基期函数调用失败");
            }
            // 制造成本
            MonthAnalysisVO params = ObjectCopyUtil.copy(monthAnalysisVO, MonthAnalysisVO.class);
            params.setCostType(IndustryIndexEnum.COST_TYPE.M.getValue());
            successFlag = dmFocMonthCostIdxDao.insertPriceIndexWithFun(params);
            if (!CommonConstant.SUCCESS.equals(successFlag) && !CommonConstant.SUCCESS_STATUS.equals(successFlag)) {
                throw new CommonApplicationException("对比分析切换基期函数调用失败");
            }
        } else {
            successFlag = dmFocMonthCostIdxDao.insertPriceIndexWithFun(monthAnalysisVO);
            if (!CommonConstant.SUCCESS.equals(successFlag) && !CommonConstant.SUCCESS_STATUS.equals(successFlag)) {
                throw new CommonApplicationException("对比分析切换基期函数调用失败");
            }
        }
        LOGGER.info(">>>compareRefreshIndustryIndexData:{}", successFlag);
    }

    private void indexChangeBasePeriodId(MonthAnalysisVO buildComb) throws CommonApplicationException {
        // 混合选择 正常维度
        MonthAnalysisVO normalSingleVO = new MonthAnalysisVO();
        if (CollectionUtils.isNotEmpty(buildComb.getGroupCodeList())) {
            if (isDataIsOk(buildComb)) {
                return;
            } else {
                BeanUtils.copyProperties(buildComb, normalSingleVO);
                normalSingleVO.setCustomIdList(null);
                normalSingleVO.setCombinaCodeList(null);
                // 调用函数
                compareRefreshIndustryIndexData(normalSingleVO);
            }
        }
        // 汇总组合
        MonthAnalysisVO combAnalysisVO = new MonthAnalysisVO();
        if (CollectionUtils.isNotEmpty(buildComb.getCombinaCodeList())) {
            // 切换基期函数
            if (isCombDataIsOk(buildComb)) {
                return;
            } else {
                getCombAnalysisVO(buildComb, combAnalysisVO);
                // 调用函数
                compareRefreshIndustryIndexData(combAnalysisVO);
            }
        }
    }

    public void getCombAnalysisVO(MonthAnalysisVO buildComb, MonthAnalysisVO combAnalysisVO) {
        BeanUtils.copyProperties(buildComb, combAnalysisVO);
        combAnalysisVO.setProdRndTeamCodeList(null);
        combAnalysisVO.setL1NameList(null);
        combAnalysisVO.setL2NameList(null);
        combAnalysisVO.setDmsCodeList(null);
        combAnalysisVO.setGroupCodeList(null);
    }

    // 查询制造成本下的产业成本指数 没有预测数
    private List<DmFocMonthCostIdxVO> getMadeCostIndexDataList(MonthAnalysisVO monthAnalysisVO) {
        setStartEndTime(monthAnalysisVO);
        return monthAnalysisVO.getCompareAnalysisVO().getIsCompareFlag()
                ? dmFocMadeMonthCostIdxDao.findMadeComparePriceIndexVOList(monthAnalysisVO)
                : dmFocMadeMonthCostIdxDao.findMadePriceIndexVOList(monthAnalysisVO);
    }

    // 查询总成本下的产业成本指数 没有预测数
    private List<DmFocMonthCostIdxVO> getTotalCostIndexDataList(MonthAnalysisVO monthAnalysisVO) {
        setEndTime(monthAnalysisVO);
        monthAnalysisVO.setMonthVersionId(dmFocVersionDao.findAnnualVersion(monthAnalysisVO.getTablePreFix()).getVersionId());
        return monthAnalysisVO.getCompareAnalysisVO().getIsCompareFlag()
                ? dmFocTotalMonthCostIdxDao.findTotalComparePriceIndexVOList(monthAnalysisVO)
                : dmFocTotalMonthCostIdxDao.findTotalPriceIndexVOList(monthAnalysisVO);
    }

    // 处理正向视角的产业成本指数的数据
    private void handleCostIndexData(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexVOList) {
        // 查询采购成本的数据
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
            getCostIndexDataList(monthAnalysisVO, priceIndexVOList);
        }
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())) {
            // 查询制造成本的数据 没有预测数
            priceIndexVOList.addAll(getMadeCostIndexDataList(monthAnalysisVO));
        }
        // 查询总成本的数据
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(monthAnalysisVO.getCostType())) {
            // 总成本下多选场景 只查询单一的成本类型
            if (monthAnalysisVO.getIsMultipleSelect()) {
                addAllpriceIdxList(monthAnalysisVO, priceIndexVOList);
            }else {
                // 总成本下单选场景 需要查询3种成本类型的数据
                asyncQueryService.findTotalCostIdxDataList(priceIndexVOList, monthAnalysisVO);
            }
        }
        // 多选时采购层级名称拼接
        if (monthAnalysisVO.getIsMultipleSelect() && "exp".equals(monthAnalysisVO.getType())) {
            mutilSelectGroupCnName(monthAnalysisVO, priceIndexVOList, null, null, null, "indexCurrentLevel");
        }
    }

    private void addAllpriceIdxList(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexVOList) {
        // 如果type不是exp，即不是导出，需要传入costSubType区分是总成本下的哪一成本
        if (!"exp".equals(monthAnalysisVO.getType())) {
            queryTotalPrinceIndexBySubCostType(monthAnalysisVO, priceIndexVOList);
        } else {
            // 如果type是exp，那么是导出，需要导出总成本下的3个成本
            getCostIndexDataList(monthAnalysisVO, priceIndexVOList);
            // 查询总成本的数据 没有预测数，总成本和制造成本顺序不能换，因为重新设置了startTime和endTime
            priceIndexVOList.addAll(getTotalCostIndexDataList(monthAnalysisVO));
            // 查询制造成本的数据 没有预测数
            priceIndexVOList.addAll(getMadeCostIndexDataList(monthAnalysisVO));
        }
    }

    private void queryTotalPrinceIndexBySubCostType(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexVOList) {
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostSubType())) {
            // 查询采购成本的数据
            getCostIndexDataList(monthAnalysisVO, priceIndexVOList);
        }
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostSubType())) {
            // 查询制造成本的数据 没有预测数
            priceIndexVOList.addAll(getMadeCostIndexDataList(monthAnalysisVO));
        }
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(monthAnalysisVO.getCostSubType())) {
            // 查询总成本的数据 没有预测数
            priceIndexVOList.addAll(getTotalCostIndexDataList(monthAnalysisVO));
        }
    }

    private void getCostIndexDataList(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexVOList) {
        if (monthAnalysisVO.getCompareAnalysisVO().getIsCompareFlag()) {
            priceIndexVOList.addAll(dmFocMonthCostIdxDao.findCompareDmFocPriceIndexVOList(monthAnalysisVO));
        } else {
            priceIndexVOList.addAll(dmFocMonthCostIdxDao.findDmFocPriceIndexVOList(monthAnalysisVO));
        }
    }

    // 处理反向视角的成本指数数据
    private void handleReveseViewCostIdxData(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexVOList) {
        // 查询采购成本的数据
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
            priceIndexVOList.addAll(dmFocRecMonthCostIdxDao.findRevPriceIndexVOList(monthAnalysisVO));
        }
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())) {
            // 查询制造成本的数据 无预测数
            setStartEndTime(monthAnalysisVO);
            priceIndexVOList.addAll(dmFocMadeRecMonthCostIdxDao.findMadeRevPriceIndexVOList(monthAnalysisVO));
        }
    }

    private void setStartEndTime(MonthAnalysisVO monthAnalysisVO) {
        Map<String, Long> startEndTime = dmFocMadeMonthCostIdxDao
                .findStartEndTime(monthAnalysisVO.getGranularityType(), monthAnalysisVO.getTablePreFix());
        monthAnalysisVO.setPeriodStartTime(Integer.parseInt(startEndTime.get("start").toString()));
        monthAnalysisVO.setPeriodEndTime(Integer.parseInt(startEndTime.get("end").toString()));
    }

    private void setEndTime(MonthAnalysisVO monthAnalysisVO) {
        Long actualMonthNum = commonService.findActualMonthNum(monthAnalysisVO.getIndustryOrg());
        monthAnalysisVO.setPeriodEndTime(Integer.valueOf(actualMonthNum.toString()));
    }

    private void combinePriceIndexVOList(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexVOList) {
        distinguishIfCombine(monthAnalysisVO);
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getGroupCodeList())) {
            // 查询采购成本的数据
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
                getCostIndexDataList(monthAnalysisVO, priceIndexVOList);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())) {
                // 查询制造成本的数据 无预测数
                priceIndexVOList.addAll(getMadeCostIndexDataList(monthAnalysisVO));
            }
        }
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getCombinaCodeList())) {
            List<DmFocMonthCostIdxVO> combPriceIndexVOList = new ArrayList<>();
            // 查询采购成本下的汇总组合数据
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
                combPriceIndexVOList = dmFocMonthCostIdxDao.findDmFocCombPriceIndexVOList(monthAnalysisVO);
            } else if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())) {
                // 查询制造成本下的汇总组合数据 无预测数
                setStartEndTime(monthAnalysisVO);
                combPriceIndexVOList = dmFocMadeMonthCostIdxDao.findMadeCombPriceIndexVOList(monthAnalysisVO);
            }
            if ("exp".equals(monthAnalysisVO.getType())) {
                for (DmFocMonthCostIdxVO dmFocMonthCostIdxVO : combPriceIndexVOList) {
                    if (!dmFocMonthCostIdxVO.getGroupCnName().contains(dmFocMonthCostIdxVO.getCustomCnName())) {
                        StringBuffer groupCnName = new StringBuffer();
                        groupCnName.append(dmFocMonthCostIdxVO.getGroupCnName()).append("(").append(dmFocMonthCostIdxVO.getCustomCnName()).append(")");
                        dmFocMonthCostIdxVO.setGroupCnName(groupCnName.toString());
                    }
                }
            }
            priceIndexVOList.addAll(combPriceIndexVOList);
        }
    }

    public void heatMapMutilSelectGroupCnName(MonthAnalysisVO monthAnalysisVO, List<DmFocActualCostVO> heatMapList) {
        // 盈利层级名称拼接
        setProfitCnName(monthAnalysisVO, heatMapList);
        // 量纲层级名称拼接
        setDimensionCnName(monthAnalysisVO, heatMapList);
        // 多选时图例名称拼接
        if (CommonConstant.GROUP_LEVEL_P.contains(monthAnalysisVO.getGroupLevel()) || CommonConstant.GROUP_LEVEL_M.contains(monthAnalysisVO.getGroupLevel())) {
            setHeatMapGroupCnName(monthAnalysisVO, heatMapList);
        }
    }

    private void setHeatMapGroupCnName(MonthAnalysisVO monthAnalysisVO, List<DmFocActualCostVO> heatMapList) {
        // 通用颗粒度
        if (IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(monthAnalysisVO.getGranularityType())) {
            heatMapList.stream().forEach(ele -> {
                ele.setGroupCnName(ele.getGroupCnName() + "(" +ele.getProdRndTeamCnName() +")");
            });
        }
        // 盈利颗粒度
        setPurchaseAndMadeLevelCnName(monthAnalysisVO, heatMapList);
        // 量纲颗粒度
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(monthAnalysisVO.getGranularityType())) {
            heatMapList.stream().forEach(ele -> {
                String heatMapDmsCnName = getDmsCnName(null,null,ele,null);
                ele.setGroupCnName(ele.getGroupCnName() + "(" + heatMapDmsCnName + ")");
            });
        }
    }

    private void setDimensionCnName(MonthAnalysisVO monthAnalysisVO, List<DmFocActualCostVO> heatMapList) {
        if (GroupLevelEnumD.COA.getValue().equals(monthAnalysisVO.getGroupLevel())) {
            heatMapList.stream().forEach(ele -> {
                ele.setGroupCnName(ele.getGroupCnName()  + "(" + ele.getProdRndTeamCnName()+")");
            });
        }
        if (GroupLevelEnumD.DIMENSION.getValue().equals(monthAnalysisVO.getGroupLevel())) {
            heatMapList.stream().forEach(ele -> {
                dimensionGroupCnNameConnect(ele);
            });
        }
        if (GroupLevelEnumD.SUBCATEGORY.getValue().equals(monthAnalysisVO.getGroupLevel())) {
            heatMapList.stream().forEach(ele -> {
                if (StringUtils.isNotBlank(ele.getCoaCode()) && StringUtils.isNotBlank(ele.getCoaCnName())) {
                    ele.setGroupCnName(ele.getGroupCnName()  + "(" + ele.getDimensionCode() +" " + ele.getDimensionCnName()  + "(" + ele.getCoaCode() +" " + ele.getCoaCnName() + "(" + ele.getProdRndTeamCnName()+")))");
                } else{
                    ele.setGroupCnName(ele.getGroupCnName()  + "(" + ele.getDimensionCode() +" " + ele.getDimensionCnName() + "(" + ele.getProdRndTeamCnName()+"))");
                }
            });
        }
        if (GroupLevelEnumD.SUB_DETAIL.getValue().equals(monthAnalysisVO.getGroupLevel())) {
            heatMapList.stream().forEach(ele -> {
                if (StringUtils.isNotBlank(ele.getCoaCode()) && StringUtils.isNotBlank(ele.getCoaCnName())) {
                    ele.setGroupCnName(ele.getGroupCnName()  + "(" + ele.getDimensionSubCategoryCode() +" " +ele.getDimensionSubCategoryCnName() + "(" + ele.getCoaCode() +" " + ele.getCoaCnName() + "(" + ele.getProdRndTeamCnName() + ")))");
                } else {
                    ele.setGroupCnName(ele.getGroupCnName()  + "(" + ele.getDimensionSubCategoryCode() +" " +ele.getDimensionSubCategoryCnName() + "(" + ele.getProdRndTeamCnName() + "))");
                }
            });
        }
        if (GroupLevelEnumD.SPART.getValue().equals(monthAnalysisVO.getGroupLevel())) {
            heatMapList.stream().forEach(ele -> {
                if (StringUtils.isNotBlank(ele.getCoaCode()) && StringUtils.isNotBlank(ele.getCoaCnName())) {
                    ele.setGroupCnName(ele.getGroupCnName()  + "(" + ele.getDimensionSubDetailCode() +" " +ele.getDimensionSubDetailCnName() + "(" + ele.getCoaCode() +" " + ele.getCoaCnName() + "(" + ele.getProdRndTeamCnName() + ")))");
                } else {
                    ele.setGroupCnName(ele.getGroupCnName()  + "(" + ele.getDimensionSubDetailCode() +" " +ele.getDimensionSubDetailCnName() + "(" + ele.getProdRndTeamCnName() + "))");
                }
            });
        }
    }

    private void dimensionGroupCnNameConnect(DmFocActualCostVO ele) {
        if (StringUtils.isNotBlank(ele.getCoaCode()) && StringUtils.isNotBlank(ele.getCoaCnName())) {
            ele.setGroupCnName(ele.getGroupCnName()  + "(" + ele.getCoaCode() +" " + ele.getCoaCnName() + "(" + ele.getProdRndTeamCnName()+")");
        } else {
            ele.setGroupCnName(ele.getGroupCnName()  + "(" + ele.getProdRndTeamCnName()+")");
        }
    }

    private void setPurchaseAndMadeLevelCnName(MonthAnalysisVO monthAnalysisVO, List<DmFocActualCostVO> heatMapList) {
        if (IndustryIndexEnum.GRANULARITY_TYPE.PROFITS.getValue().equals(monthAnalysisVO.getGranularityType())) {
            heatMapList.stream().forEach(ele -> {
                String profitName = null;
                if (StringUtils.isNotBlank(ele.getL1Name())) {
                    profitName = ele.getL1Name();
                }
                if (StringUtils.isNotBlank(ele.getL2Name())) {
                    profitName = ele.getL2Name();
                }
                ele.setGroupCnName(ele.getGroupCnName() + "(" + profitName + "(" + ele.getProdRndTeamCnName() +"))");
            });
        }
    }

    private void setProfitCnName(MonthAnalysisVO monthAnalysisVO, List<DmFocActualCostVO> heatMapList) {
        if (GroupLevelEnumP.L1.getValue().equals(monthAnalysisVO.getGroupLevel())) {
            if (CollectionUtils.isNotEmpty(heatMapList)) {
                heatMapList.stream().forEach(ele -> {
                    ele.setGroupCnName(ele.getL1Name() + "(" + ele.getProdRndTeamCnName()+")");
                });
            }
        }
        // L2维度
        if (GroupLevelEnumP.L2.getValue().equals(monthAnalysisVO.getGroupLevel())) {
            if(CollectionUtils.isNotEmpty(heatMapList)) {
                heatMapList.stream().forEach(ele -> {
                    ele.setGroupCnName(ele.getL2Name() + "(" + ele.getL1Name() + "("+ele.getProdRndTeamCnName()+"))");
                });
            }
        }
    }

    public void splicingWeightCodeAndName(List<DmFocMonthWeightVO> weightList) {
        if (CollectionUtils.isNotEmpty(weightList)) {
            weightList.stream().forEach(weightVO -> {
                if (CommonConstant.GROUP_LEVEL.contains(weightVO.getGroupLevel())) {
                    weightVO.setGroupCnName(weightVO.getGroupCode() + " " + weightVO.getGroupCnName());
                }
            });
        }
    }

    public void splicingWeightParentCodeAndName(List<DmFocMonthWeightVO> weightList) {
        if (CollectionUtils.isNotEmpty(weightList)) {
            weightList.stream().forEach(weightVO -> {
                if (CommonConstant.GROUP_LEVEL_DIMENSION.contains(weightVO.getParentLevel())) {
                    weightVO.setParentCnName(weightVO.getParentCode() + " " + weightVO.getParentCnName());
                }
            });
        }
    }

    public void splicingHeatMapCodeAndName( List<DmFocActualCostVO> mutilGroupCnNameByCode) {
        if (CollectionUtils.isNotEmpty(mutilGroupCnNameByCode)) {
            mutilGroupCnNameByCode.stream().forEach(actualCostVO -> {
                if (CommonConstant.GROUP_LEVEL.contains(actualCostVO.getGroupLevel())) {
                    actualCostVO.setGroupCnName(actualCostVO.getGroupCode() + " " + actualCostVO.getGroupCnName());
                }
            });
        }
    }

    public void mutilSelectGroupCnName(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexVOList,
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList, List<DmFocMonthWeightVO> weightList,
        List<DmFocActualCostVO> heatMapList, String methodFlag) {
        // 通用层级名称拼接
        universalLevelCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,methodFlag);
        // 盈利层级名称拼接
        profitLevelCnName(monthAnalysisVO,priceIndexVOList,yoyAndPopIndexVOList,weightList,heatMapList,methodFlag);
        // 量纲层级名称拼接
        dimensionLevelCnName(monthAnalysisVO,priceIndexVOList, yoyAndPopIndexVOList,weightList,heatMapList, methodFlag);
        // 多选时图例名称拼接
        if (CommonConstant.GROUP_LEVEL_P_M.contains(monthAnalysisVO.getGroupLevel())) {
            if ("indexCurrentLevel".equals(methodFlag)) {
                indexCostGroupCnName(monthAnalysisVO, priceIndexVOList, methodFlag);
            }
            if ("mutilIndexCost".equals(methodFlag)) {
                indexCostGroupCnName(monthAnalysisVO, priceIndexVOList, methodFlag);
            }
            if ("weightList".equals(methodFlag)) {
                weightAndHeatGroupCnName(monthAnalysisVO, weightList, null,methodFlag);
            }
            if ("heatMapList".equals(methodFlag)) {
                weightAndHeatGroupCnName(monthAnalysisVO, null, heatMapList,methodFlag);
            }
            if ("yoyAndPopIndexCost".equals(methodFlag)){
                yoYCostGroupCnName(monthAnalysisVO, yoyAndPopIndexVOList);
            }
        }
    }

    private void universalLevelCnName(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexVOList,
                                      List<DmFocMonthYoyVO> yoyAndPopIndexVOList,String methodFlag) {
        if (IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(monthAnalysisVO.getGranularityType())) {
            if ("indexCurrentLevel".equals(methodFlag) || "yoyAndPopIndexCost".equals(methodFlag)) {
                setSingChartCegOrShipingObject(monthAnalysisVO, priceIndexVOList, yoyAndPopIndexVOList);
            }
        }
    }

    private void profitLevelCnName(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexVOList,
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList, List<DmFocMonthWeightVO> weightList,
        List<DmFocActualCostVO> heatMapList, String methodFlag) {
        if (IndustryIndexEnum.GRANULARITY_TYPE.PROFITS.getValue().equals(monthAnalysisVO.getGranularityType())) {
            profitSingleChartCnName(monthAnalysisVO, priceIndexVOList, yoyAndPopIndexVOList, methodFlag);
            profitMutliSubCharCnName(monthAnalysisVO, priceIndexVOList, weightList, heatMapList, methodFlag);
        }
    }

    private void profitMutliSubCharCnName(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexVOList, List<DmFocMonthWeightVO> weightList, List<DmFocActualCostVO> heatMapList, String methodFlag) {
        if ("mutilIndexCost".equals(methodFlag)|| "heatMapList".equals(methodFlag) || "weightList".equals(methodFlag)) {
            // L1维度
            setL1Name(monthAnalysisVO, priceIndexVOList, weightList, heatMapList);
            // L2维度
            setL2Name(monthAnalysisVO, priceIndexVOList, weightList, heatMapList);
        }
    }

    private void setL2Name(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexVOList, List<DmFocMonthWeightVO> weightList, List<DmFocActualCostVO> heatMapList) {
        if (GroupLevelEnumP.L2.getValue().equals(monthAnalysisVO.getParentLevel())) {
            if(CollectionUtils.isNotEmpty(priceIndexVOList)) {
                priceIndexVOList.stream().forEach(ele -> {
                    ele.setParentCnName(ele.getL2Name() + "(" + ele.getL1Name() +"("+ ele.getProdRndTeamCnName()+"))");
                });
            }
            if(CollectionUtils.isNotEmpty(weightList)) {
                weightList.stream().forEach(ele -> {
                    ele.setParentCnName(ele.getL2Name() + "(" + ele.getL1Name() +"("+ ele.getProdRndTeamCnName()+"))");
                });
            }
            if(CollectionUtils.isNotEmpty(heatMapList)) {
                heatMapList.stream().forEach(ele -> {
                    ele.setParentCnName(ele.getL2Name() + "(" + ele.getL1Name() +"("+ ele.getProdRndTeamCnName()+"))");
                });
            }
        }
    }

    private void setL1Name(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexVOList, List<DmFocMonthWeightVO> weightList, List<DmFocActualCostVO> heatMapList) {
        if (GroupLevelEnumP.L1.getValue().equals(monthAnalysisVO.getParentLevel())) {
            if (CollectionUtils.isNotEmpty(priceIndexVOList)) {
                priceIndexVOList.stream().forEach(ele -> {
                    ele.setParentCnName(ele.getL1Name() + "(" + ele.getProdRndTeamCnName() + ")");
                });
            }
            if (CollectionUtils.isNotEmpty(weightList)) {
                weightList.stream().forEach(ele -> {
                    ele.setParentCnName(ele.getL1Name() + "(" + ele.getProdRndTeamCnName() + ")");
                });
            }
            if (CollectionUtils.isNotEmpty(heatMapList)) {
                heatMapList.stream().forEach(ele -> {
                    ele.setParentCnName(ele.getL1Name() + "(" + ele.getProdRndTeamCnName() + ")");
                });
            }
        }
    }

    private void profitSingleChartCnName(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexVOList, List<DmFocMonthYoyVO> yoyAndPopIndexVOList, String methodFlag) {
        if ("indexCurrentLevel".equals(methodFlag) || "yoyAndPopIndexCost".equals(methodFlag)) {
            // L1维度
            setSingleChartL1Name(monthAnalysisVO, priceIndexVOList, yoyAndPopIndexVOList);
            setSingleChartL2Name(monthAnalysisVO, priceIndexVOList, yoyAndPopIndexVOList);
            setSingChartCegOrShipingObject(monthAnalysisVO, priceIndexVOList, yoyAndPopIndexVOList);
        }
    }

    private void setSingChartCegOrShipingObject(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexVOList, List<DmFocMonthYoyVO> yoyAndPopIndexVOList) {
        // CEG层级或者发货对象
        if (GroupLevelEnumD.CEG.getValue().equals(monthAnalysisVO.getGroupLevel()) || GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue().equals(monthAnalysisVO.getGroupLevel())) {
            Optional.ofNullable(priceIndexVOList).orElse(new ArrayList<>()).stream().forEach(ele -> {
                        String profitNameIndex = getProfitName(ele, null, null, null);
                        if (StringUtils.isNotBlank(profitNameIndex)) {
                            ele.setGroupCnName(ele.getGroupCnName() + "(" + profitNameIndex + ")");
                        } else {
                            ele.setGroupCnName(ele.getGroupCnName() + "(" + ele.getProdRndTeamCnName() + ")");
                        }
                    }
            );
            Optional.ofNullable(yoyAndPopIndexVOList).orElse(new ArrayList<>()).stream().forEach(ele -> {
                        String profitNameYoyAndPop = getProfitName(null, ele, null, null);
                        if (StringUtils.isNotBlank(profitNameYoyAndPop)) {
                            ele.setGroupCnName(ele.getGroupCnName() + "(" + profitNameYoyAndPop + ")");
                        } else {
                            ele.setGroupCnName(ele.getGroupCnName() + "(" + ele.getProdRndTeamCnName() + ")");
                        }
                    }
            );
        }
    }

    private void setSingleChartL2Name(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexVOList, List<DmFocMonthYoyVO> yoyAndPopIndexVOList) {
        if (GroupLevelEnumP.L2.getValue().equals(monthAnalysisVO.getGroupLevel())){
            if (CollectionUtils.isNotEmpty(priceIndexVOList)) {
                priceIndexVOList.stream().forEach(ele -> {
                    ele.setGroupCnName(ele.getGroupCnName() + "(" + ele.getL1Name()+"("+ ele.getProdRndTeamCnName()+"))");
                });
            }
            if (CollectionUtils.isNotEmpty(yoyAndPopIndexVOList)) {
                yoyAndPopIndexVOList.stream().forEach(ele -> {
                    ele.setGroupCnName(ele.getGroupCnName() + "(" + ele.getL1Name()+"("+ ele.getProdRndTeamCnName()+"))");
                });
            }
        }
    }

    private String getProfitName(DmFocMonthCostIdxVO costIdxVO,DmFocMonthYoyVO yoyVO,DmFocMonthWeightVO weightVO,DmFocActualCostVO heatMapVO ) {
        String profitName = null;
        if (costIdxVO != null && StringUtils.isNotBlank(costIdxVO.getL1Name())) {
            profitName = costIdxVO.getL1Name() + "(" + costIdxVO.getProdRndTeamCnName() +")";
        }
        if (costIdxVO != null && StringUtils.isNotBlank(costIdxVO.getL2Name())) {
            profitName = costIdxVO.getL2Name() + "(" + costIdxVO.getL1Name() + "(" + costIdxVO.getProdRndTeamCnName() +"))";
        }
        if (yoyVO != null && StringUtils.isNotBlank(yoyVO.getL1Name())) {
            profitName = yoyVO.getL1Name() + "(" + yoyVO.getProdRndTeamCnName() +")";
        }
        if (yoyVO != null && StringUtils.isNotBlank(yoyVO.getL2Name())) {
            profitName = yoyVO.getL2Name() + "(" + yoyVO.getL1Name() +"(" + yoyVO.getProdRndTeamCnName() +"))";
        }
        if (weightVO != null && StringUtils.isNotBlank(weightVO.getL1Name())) {
            profitName = weightVO.getL1Name() + "(" + weightVO.getProdRndTeamCnName() +")";
        }
        if (weightVO != null && StringUtils.isNotBlank(weightVO.getL2Name())) {
            profitName = weightVO.getL2Name() + "(" + weightVO.getL1Name() +"(" + weightVO.getProdRndTeamCnName() +"))";
        }
        if (heatMapVO != null && StringUtils.isNotBlank(heatMapVO.getL1Name())) {
            profitName = heatMapVO.getL1Name() + "(" + heatMapVO.getProdRndTeamCnName() +")";
        }
        if (heatMapVO != null && StringUtils.isNotBlank(heatMapVO.getL2Name())) {
            profitName = heatMapVO.getL2Name() + "(" + heatMapVO.getL1Name() +"(" + heatMapVO.getProdRndTeamCnName() +"))";
        }
        return profitName;
    }

    private void setSingleChartL1Name(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexVOList, List<DmFocMonthYoyVO> yoyAndPopIndexVOList) {
        if (GroupLevelEnumP.L1.getValue().equals(monthAnalysisVO.getGroupLevel())) {
            Optional.ofNullable(priceIndexVOList).orElse(new ArrayList<>())
                    .stream().forEach(ele -> ele.setGroupCnName(ele.getGroupCnName() + "(" + ele.getProdRndTeamCnName() + ")"));
            Optional.ofNullable(yoyAndPopIndexVOList).orElse(new ArrayList<>())
                    .stream().forEach(ele -> ele.setGroupCnName(ele.getGroupCnName() + "(" + ele.getProdRndTeamCnName() + ")"));
        }
    }

    private void dimensionLevelCnName(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexVOList,
        List<DmFocMonthYoyVO> yoyAndPopIndexVOList, List<DmFocMonthWeightVO> weightList,
        List<DmFocActualCostVO> heatMapList, String methodFlag) {
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(monthAnalysisVO.getGranularityType())) {
            setDimensionSingleChartCnName(monthAnalysisVO, priceIndexVOList, yoyAndPopIndexVOList, methodFlag);
            setDimensionMutliCharCnName(monthAnalysisVO, priceIndexVOList, heatMapList,weightList, methodFlag);
        }

    }

    private void setDimensionMutliCharCnName(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexVOList, List<DmFocActualCostVO> heatMapList,List<DmFocMonthWeightVO> weightList, String methodFlag) {
        if ("mutilIndexCost".equals(methodFlag)|| "heatMapList".equals(methodFlag) || "weightList".equals(methodFlag)) {
            // COA维度
            coaParentCnName(monthAnalysisVO, priceIndexVOList, heatMapList,weightList);
            // 量纲维度
            dimensionParentCnName(monthAnalysisVO, priceIndexVOList, heatMapList,weightList);
            // 量纲子类维度
            dimensionSubCateParnetCnName(monthAnalysisVO, priceIndexVOList, heatMapList,weightList);
            // 量纲子类明细维度
            dimensionSubDetailParentCnName(monthAnalysisVO, priceIndexVOList, heatMapList,weightList);
            // SPART维度
            dimensionSpartCnName(monthAnalysisVO, priceIndexVOList, heatMapList,weightList);
        }
    }

    private void dimensionSpartCnName(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexVOList, List<DmFocActualCostVO> heatMapList,List<DmFocMonthWeightVO> weightList) {
        if (GroupLevelEnumD.SPART.getValue().equals(monthAnalysisVO.getParentLevel())) {
            if (CollectionUtils.isNotEmpty(priceIndexVOList)) {
                priceIndexVOList.stream().forEach(ele -> {
                    if (StringUtils.isNotBlank(ele.getCoaCode()) &&  StringUtils.isNotBlank(ele.getCoaCnName())) {
                        ele.setParentCnName(ele.getParentCode() + "(" + ele.getDimensionSubDetailCode() + " " + ele.getDimensionSubDetailCnName() + "(" + ele.getCoaCode() + " " + ele.getCoaCnName() + "(" + ele.getProdRndTeamCnName()+")))");
                    } else {
                        ele.setParentCnName(ele.getParentCode() + "(" + ele.getDimensionSubDetailCode() + " " + ele.getDimensionSubDetailCnName() + "(" + ele.getProdRndTeamCnName()+"))");
                    }
                });
            }
            if (CollectionUtils.isNotEmpty(heatMapList)) {
                heatMapList.stream().forEach(ele -> {
                    if (StringUtils.isNotBlank(ele.getCoaCode()) &&  StringUtils.isNotBlank(ele.getCoaCnName())) {
                        ele.setParentCnName(ele.getParentCode() + "(" + ele.getDimensionSubDetailCode() + " " + ele.getDimensionSubDetailCnName() + "(" + ele.getCoaCode() + " " + ele.getCoaCnName() + "(" + ele.getProdRndTeamCnName()+")))");
                    } else {
                        ele.setParentCnName(ele.getParentCode() + "(" + ele.getDimensionSubDetailCode() + " " + ele.getDimensionSubDetailCnName() + "(" + ele.getProdRndTeamCnName()+"))");
                    }
                });
            }
            if (CollectionUtils.isNotEmpty(weightList)) {
                weightList.stream().forEach(ele -> {
                    if (StringUtils.isNotBlank(ele.getCoaCode()) &&  StringUtils.isNotBlank(ele.getCoaCnName())) {
                        ele.setParentCnName(ele.getParentCode() + "(" + ele.getDimensionSubDetailCode() + " " + ele.getDimensionSubDetailCnName() + "(" + ele.getCoaCode() + " " + ele.getCoaCnName() + "(" + ele.getProdRndTeamCnName()+")))");
                    } else {
                        ele.setParentCnName(ele.getParentCode() + "(" + ele.getDimensionSubDetailCode() + " " + ele.getDimensionSubDetailCnName() + "(" + ele.getProdRndTeamCnName()+"))");
                    }
                });
            }
        }
    }

    private void dimensionSubDetailParentCnName(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexVOList, List<DmFocActualCostVO> heatMapList,List<DmFocMonthWeightVO> weightList) {
        if (GroupLevelEnumD.SUB_DETAIL.getValue().equals(monthAnalysisVO.getParentLevel())) {
            if (CollectionUtils.isNotEmpty(priceIndexVOList)) {
                priceIndexVOList.stream().forEach(ele -> {
                    if (StringUtils.isNotBlank(ele.getCoaCode()) &&  StringUtils.isNotBlank(ele.getCoaCnName())) {
                        ele.setParentCnName(ele.getParentCode() + " " + ele.getParentCnName()  + "(" + ele.getDimensionSubCategoryCode() + " " + ele.getDimensionSubCategoryCnName() + "(" + ele.getCoaCode() + " " + ele.getCoaCnName() + "(" + ele.getProdRndTeamCnName()+")))");
                    } else {
                        ele.setParentCnName(ele.getParentCode() + " " + ele.getParentCnName()  + "(" + ele.getDimensionSubCategoryCode() + " " + ele.getDimensionSubCategoryCnName() + "(" + ele.getProdRndTeamCnName()+"))");
                    }
                });
            }
            if (CollectionUtils.isNotEmpty(heatMapList)) {
                heatMapList.stream().forEach(ele -> {
                    if (StringUtils.isNotBlank(ele.getCoaCode()) &&  StringUtils.isNotBlank(ele.getCoaCnName())) {
                        ele.setParentCnName(ele.getParentCode() + " " + ele.getParentCnName()  + "(" + ele.getDimensionSubCategoryCode() + " " + ele.getDimensionSubCategoryCnName() + "(" + ele.getCoaCode() + " " + ele.getCoaCnName() + "(" + ele.getProdRndTeamCnName()+")))");
                    } else {
                        ele.setParentCnName(ele.getParentCode() + " " + ele.getParentCnName()  + "(" + ele.getDimensionSubCategoryCode() + " " + ele.getDimensionSubCategoryCnName() + "(" + ele.getProdRndTeamCnName()+"))");
                    }
                });
            }
            if (CollectionUtils.isNotEmpty(weightList)) {
                weightList.stream().forEach(ele -> {
                    if (StringUtils.isNotBlank(ele.getCoaCode()) &&  StringUtils.isNotBlank(ele.getCoaCnName())) {
                        ele.setParentCnName(ele.getParentCode() + " " + ele.getParentCnName()  + "(" + ele.getDimensionSubCategoryCode() + " " + ele.getDimensionSubCategoryCnName() + "(" + ele.getCoaCode() + " " + ele.getCoaCnName() + "(" + ele.getProdRndTeamCnName()+")))");
                    } else {
                        ele.setParentCnName(ele.getParentCode() + " " + ele.getParentCnName()  + "(" + ele.getDimensionSubCategoryCode() + " " + ele.getDimensionSubCategoryCnName() + "(" + ele.getProdRndTeamCnName()+"))");
                    }
                });
            }
        }
    }

    private void dimensionSubCateParnetCnName(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexVOList, List<DmFocActualCostVO> heatMapList,List<DmFocMonthWeightVO> weightList) {
        if (GroupLevelEnumD.SUBCATEGORY.getValue().equals(monthAnalysisVO.getParentLevel())) {
            if (CollectionUtils.isNotEmpty(priceIndexVOList)) {
                priceIndexVOList.stream().forEach(ele -> {
                    if (StringUtils.isNotBlank(ele.getCoaCode()) &&  StringUtils.isNotBlank(ele.getCoaCnName())) {
                        ele.setParentCnName(ele.getParentCode() + " " + ele.getParentCnName()  + "(" + ele.getDimensionCode() + " " + ele.getDimensionCnName() + "(" + ele.getCoaCode() + " " + ele.getCoaCnName() + "(" + ele.getProdRndTeamCnName()+")))");
                    } else {
                        ele.setParentCnName(ele.getParentCode() + " " + ele.getParentCnName()  + "(" + ele.getDimensionCode() + " " + ele.getDimensionCnName() + "(" + ele.getProdRndTeamCnName()+"))");
                    }
                });
            }
            if (CollectionUtils.isNotEmpty(heatMapList)) {
                heatMapList.stream().forEach(ele -> {
                    if (StringUtils.isNotBlank(ele.getCoaCode()) &&  StringUtils.isNotBlank(ele.getCoaCnName())) {
                        ele.setParentCnName(ele.getParentCode() + " " + ele.getParentCnName()  + "(" + ele.getDimensionCode() + " " + ele.getDimensionCnName() + "(" + ele.getCoaCode() + " " + ele.getCoaCnName() + "(" + ele.getProdRndTeamCnName()+")))");
                    } else {
                        ele.setParentCnName(ele.getParentCode() + " " + ele.getParentCnName()  + "(" + ele.getDimensionCode() + " " + ele.getDimensionCnName() + "(" + ele.getProdRndTeamCnName()+"))");
                    }
                });
            }
            if (CollectionUtils.isNotEmpty(weightList)) {
                weightList.stream().forEach(ele -> {
                    if (StringUtils.isNotBlank(ele.getCoaCode()) &&  StringUtils.isNotBlank(ele.getCoaCnName())) {
                        ele.setParentCnName(ele.getParentCode() + " " + ele.getParentCnName()  + "(" + ele.getDimensionCode() + " " + ele.getDimensionCnName() + "(" + ele.getCoaCode() + " " + ele.getCoaCnName() + "(" + ele.getProdRndTeamCnName()+")))");
                    } else {
                        ele.setParentCnName(ele.getParentCode() + " " + ele.getParentCnName()  + "(" + ele.getDimensionCode() + " " + ele.getDimensionCnName() + "(" + ele.getProdRndTeamCnName()+"))");
                    }
                });
            }
        }
    }

    private void coaParentCnName(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexVOList, List<DmFocActualCostVO> heatMapList,List<DmFocMonthWeightVO> weightList) {
        if (GroupLevelEnumD.COA.getValue().equals(monthAnalysisVO.getParentLevel())) {
            if (CollectionUtils.isNotEmpty(priceIndexVOList)) {
                priceIndexVOList.stream().forEach(ele -> {
                    ele.setParentCnName(ele.getParentCode() + " " + ele.getParentCnName()  + "(" + ele.getProdRndTeamCnName()+")");
                });
            }
            if (CollectionUtils.isNotEmpty(heatMapList)) {
                heatMapList.stream().forEach(ele -> {
                    ele.setParentCnName(ele.getParentCode() + " " + ele.getParentCnName()  + "(" + ele.getProdRndTeamCnName()+")");
                });
            }
            if (CollectionUtils.isNotEmpty(weightList)) {
                weightList.stream().forEach(ele -> {
                    ele.setParentCnName(ele.getParentCode() + " " + ele.getParentCnName()  + "(" + ele.getProdRndTeamCnName()+")");
                });
            }
        }
    }

    private void dimensionParentCnName(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexVOList, List<DmFocActualCostVO> heatMapList,List<DmFocMonthWeightVO> weightList) {
        if (GroupLevelEnumD.DIMENSION.getValue().equals(monthAnalysisVO.getParentLevel())) {
            if (CollectionUtils.isNotEmpty(priceIndexVOList)) {
                priceIndexVOList.stream().forEach(ele -> {
                    if (StringUtils.isNotBlank(ele.getCoaCode()) &&  StringUtils.isNotBlank(ele.getCoaCnName())) {
                        ele.setParentCnName(ele.getParentCode() + " " + ele.getParentCnName() + "(" + ele.getCoaCode() + " " + ele.getCoaCnName() + "(" + ele.getProdRndTeamCnName()+"))");
                    } else {
                        ele.setParentCnName(ele.getParentCode() + " " + ele.getParentCnName() + "(" + ele.getProdRndTeamCnName()+")");
                    }
                });
            }
            if (CollectionUtils.isNotEmpty(heatMapList)) {
                heatMapList.stream().forEach(ele -> {
                    if (StringUtils.isNotBlank(ele.getCoaCode()) &&  StringUtils.isNotBlank(ele.getCoaCnName())) {
                        ele.setParentCnName(ele.getParentCode() + " " + ele.getParentCnName() + "(" + ele.getCoaCode() + " " + ele.getCoaCnName() + "(" + ele.getProdRndTeamCnName()+"))");
                    } else {
                        ele.setParentCnName(ele.getParentCode() + " " + ele.getParentCnName() + "(" + ele.getProdRndTeamCnName()+")");
                    }
                });
            }
            if (CollectionUtils.isNotEmpty(weightList)) {
                weightList.stream().forEach(ele -> {
                    if (StringUtils.isNotBlank(ele.getCoaCode()) &&  StringUtils.isNotBlank(ele.getCoaCnName())) {
                        ele.setParentCnName(ele.getParentCode() + " " + ele.getParentCnName() + "(" + ele.getCoaCode() + " " + ele.getCoaCnName() + "(" + ele.getProdRndTeamCnName()+"))");
                    } else {
                        ele.setParentCnName(ele.getParentCode() + " " + ele.getParentCnName() + "(" + ele.getProdRndTeamCnName()+")");
                    }
                });
            }
        }
    }

    private void setDimensionSingleChartCnName(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexVOList, List<DmFocMonthYoyVO> yoyAndPopIndexVOList, String methodFlag) {
        // 量纲层级名称拼接
        if ("indexCurrentLevel".equals(methodFlag) || "yoyAndPopIndexCost".equals(methodFlag)) {
            // coa维度
            coaGroupCnName(monthAnalysisVO, priceIndexVOList, yoyAndPopIndexVOList);
            // 量纲维度
            dimensionGroupCnName(monthAnalysisVO, priceIndexVOList, yoyAndPopIndexVOList);
            // 量纲子类维度
            subCateGroupCnName(monthAnalysisVO, priceIndexVOList, yoyAndPopIndexVOList);
            // 量纲子类明细维度
            subDetailGroupCnName(monthAnalysisVO, priceIndexVOList, yoyAndPopIndexVOList);
            // SPART维度
            spartCnName(monthAnalysisVO, priceIndexVOList, yoyAndPopIndexVOList);
            // CEG或者发货对象维度
            cegOrShippingObjectGroupCnName(monthAnalysisVO, priceIndexVOList, yoyAndPopIndexVOList);
        }
    }


    private void spartCnName(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexVOList, List<DmFocMonthYoyVO> yoyAndPopIndexVOList) {
        if (GroupLevelEnumD.SPART.getValue().equals(monthAnalysisVO.getGroupLevel())) {
            if (CollectionUtils.isNotEmpty(priceIndexVOList)) {
                priceIndexVOList.stream().forEach(ele -> {
                    if (StringUtils.isNotBlank(ele.getCoaCode()) &&  StringUtils.isNotBlank(ele.getCoaCnName())) {
                        ele.setGroupCnName(ele.getGroupCnName() + "(" + ele.getDimensionSubDetailCode() + " " + ele.getDimensionSubDetailCnName()  + "(" + ele.getCoaCode() +" " + ele.getCoaCnName() + "(" + ele.getProdRndTeamCnName()+")))");
                    } else {
                        ele.setGroupCnName(ele.getGroupCnName() + "(" + ele.getDimensionSubDetailCode() + " " + ele.getDimensionSubDetailCnName() + "(" + ele.getProdRndTeamCnName()+"))");
                    }
                });
            }
            if (CollectionUtils.isNotEmpty(yoyAndPopIndexVOList)){
                yoyAndPopIndexVOList.stream().forEach(ele -> {
                    if (StringUtils.isNotBlank(ele.getCoaCode()) &&  StringUtils.isNotBlank(ele.getCoaCnName())) {
                        ele.setGroupCnName(ele.getGroupCnName() + "(" + ele.getDimensionSubDetailCode() + " " + ele.getDimensionSubDetailCnName()  + "(" + ele.getCoaCode() +" " + ele.getCoaCnName() + "(" + ele.getProdRndTeamCnName()+")))");
                    } else {
                        ele.setGroupCnName(ele.getGroupCnName() + "(" + ele.getDimensionSubDetailCode() + " " + ele.getDimensionSubDetailCnName() + "(" + ele.getProdRndTeamCnName()+"))");
                    }
                });
            }
        }
    }
    private void subDetailGroupCnName(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexVOList, List<DmFocMonthYoyVO> yoyAndPopIndexVOList) {
        if (GroupLevelEnumD.SUB_DETAIL.getValue().equals(monthAnalysisVO.getGroupLevel())) {
            if (CollectionUtils.isNotEmpty(priceIndexVOList)) {
                priceIndexVOList.stream().forEach(ele -> {
                    if (StringUtils.isNotBlank(ele.getCoaCode()) &&  StringUtils.isNotBlank(ele.getCoaCnName())) {
                        ele.setGroupCnName(ele.getGroupCnName() + "(" + ele.getDimensionSubCategoryCode() + " " + ele.getDimensionSubCategoryCnName() + "(" + ele.getCoaCode() +" " + ele.getCoaCnName() + "(" + ele.getProdRndTeamCnName()+")))");
                    } else {
                        ele.setGroupCnName(ele.getGroupCnName() + "(" + ele.getDimensionSubCategoryCode() + " " + ele.getDimensionSubCategoryCnName() + "(" + ele.getProdRndTeamCnName()+"))");
                    }

                });
            }
            if (CollectionUtils.isNotEmpty(yoyAndPopIndexVOList)){
                yoyAndPopIndexVOList.stream().forEach(ele -> {
                    if (StringUtils.isNotBlank(ele.getCoaCode()) &&  StringUtils.isNotBlank(ele.getCoaCnName())) {
                        ele.setGroupCnName(ele.getGroupCnName() + "(" + ele.getDimensionSubCategoryCode() + " " + ele.getDimensionSubCategoryCnName() + "(" +  ele.getCoaCode() +" " + ele.getCoaCnName() + "(" + ele.getProdRndTeamCnName() + ")))");
                    } else {
                        ele.setGroupCnName(ele.getGroupCnName() + "(" + ele.getDimensionSubCategoryCode() + " " + ele.getDimensionSubCategoryCnName() + "(" + ele.getProdRndTeamCnName() + "))");
                    }
                });
            }
        }
    }

    private void subCateGroupCnName(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexVOList, List<DmFocMonthYoyVO> yoyAndPopIndexVOList) {
        if (GroupLevelEnumD.SUBCATEGORY.getValue().equals(monthAnalysisVO.getGroupLevel())) {
            if (CollectionUtils.isNotEmpty(priceIndexVOList)) {
                priceIndexVOList.stream().forEach(ele -> {
                    if (StringUtils.isNotBlank(ele.getCoaCode()) &&  StringUtils.isNotBlank(ele.getCoaCnName())) {
                        ele.setGroupCnName(ele.getGroupCnName()+ "(" +  ele.getDimensionCode() +" " + ele.getDimensionCnName() + "(" +  ele.getCoaCode() +" " + ele.getCoaCnName() + "(" + ele.getProdRndTeamCnName() + ")))");
                    } else {
                        ele.setGroupCnName(ele.getGroupCnName()+ "(" +  ele.getDimensionCode() +" " + ele.getDimensionCnName() + "(" + ele.getProdRndTeamCnName() + "))");
                    }
                });
            }
            if (CollectionUtils.isNotEmpty(yoyAndPopIndexVOList)) {
                yoyAndPopIndexVOList.stream().forEach(ele -> {
                    if (StringUtils.isNotBlank(ele.getCoaCode()) &&  StringUtils.isNotBlank(ele.getCoaCnName())) {
                        ele.setGroupCnName(ele.getGroupCnName()+ "(" +  ele.getDimensionCode() +" " + ele.getDimensionCnName() + "(" +  ele.getCoaCode() +" " + ele.getCoaCnName() + "(" + ele.getProdRndTeamCnName() + ")))");
                    } else {
                        ele.setGroupCnName(ele.getGroupCnName() + "(" +  ele.getDimensionCode() +" " + ele.getDimensionCnName() + "(" + ele.getProdRndTeamCnName() + "))");
                    }
                });
            }
        }
    }

    private void cegOrShippingObjectGroupCnName(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexVOList, List<DmFocMonthYoyVO> yoyAndPopIndexVOList) {
        if (GroupLevelEnumD.CEG.getValue().equals(monthAnalysisVO.getGroupLevel()) || GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue().equals(monthAnalysisVO.getGroupLevel()) ) {
            if (CollectionUtils.isNotEmpty(priceIndexVOList)) {
                priceIndexVOList.stream().forEach(ele -> {
                    String priceIndexDmsCnName = getDmsCnName(null,null,null,ele);
                    ele.setGroupCnName(ele.getGroupCnName() + "(" + priceIndexDmsCnName + ")");
                });
            }
            if (CollectionUtils.isNotEmpty(yoyAndPopIndexVOList)) {
                yoyAndPopIndexVOList.stream().forEach(ele -> {
                    String yoyAndPopDmsCnName = getDmsCnName(ele,null,null,null);
                    ele.setGroupCnName(ele.getGroupCnName() + "(" + yoyAndPopDmsCnName +")");
                });
            }
        }
    }

    private void coaGroupCnName(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexVOList, List<DmFocMonthYoyVO> yoyAndPopIndexVOList) {
        if (GroupLevelEnumD.COA.getValue().equals(monthAnalysisVO.getGroupLevel())) {
            if (CollectionUtils.isNotEmpty(priceIndexVOList)) {
                priceIndexVOList.stream().forEach(ele -> {
                    ele.setGroupCnName(ele.getGroupCnName() + "(" + ele.getProdRndTeamCnName()+")");
                });
            }
            if (CollectionUtils.isNotEmpty(yoyAndPopIndexVOList)) {
                yoyAndPopIndexVOList.stream().forEach(ele -> {
                    ele.setGroupCnName(ele.getGroupCnName() + "(" + ele.getProdRndTeamCnName()+")");
                });
            }
        }
    }

    private void dimensionGroupCnName(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexVOList, List<DmFocMonthYoyVO> yoyAndPopIndexVOList) {
        if (GroupLevelEnumD.DIMENSION.getValue().equals(monthAnalysisVO.getGroupLevel())) {
            if (CollectionUtils.isNotEmpty(priceIndexVOList)) {
                priceIndexVOList.stream().forEach(ele -> {
                    if (StringUtils.isNotBlank(ele.getCoaCode()) &&  StringUtils.isNotBlank(ele.getCoaCnName())) {
                        ele.setGroupCnName(ele.getGroupCnName() + "(" +  ele.getCoaCode() +" " + ele.getCoaCnName() + "(" + ele.getProdRndTeamCnName()+"))");
                    } else {
                        ele.setGroupCnName(ele.getGroupCnName() + "(" + ele.getProdRndTeamCnName()+")");
                    }
                });
            }
            if (CollectionUtils.isNotEmpty(yoyAndPopIndexVOList)) {
                yoyAndPopIndexVOList.stream().forEach(ele -> {
                    if (StringUtils.isNotBlank(ele.getCoaCode()) &&  StringUtils.isNotBlank(ele.getCoaCnName())) {
                        ele.setGroupCnName(ele.getGroupCnName() + "(" +  ele.getCoaCode() +" " + ele.getCoaCnName() + "(" + ele.getProdRndTeamCnName()+"))");
                    } else {
                        ele.setGroupCnName(ele.getGroupCnName() + "(" + ele.getProdRndTeamCnName()+")");
                    }
                });
            }
        }
    }

    private void weightAndHeatGroupCnName(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthWeightVO> weightList,List<DmFocActualCostVO> heatMapList,String methodFlag) {
        // 通用颗粒度
        if (IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(monthAnalysisVO.getGranularityType())) {
            universalWightAndHeatGroupCnNameStr(weightList,heatMapList,methodFlag);
        }
        // 盈利颗粒度
        if (IndustryIndexEnum.GRANULARITY_TYPE.PROFITS.getValue().equals(monthAnalysisVO.getGranularityType())) {
            profitWeightAndHeatGroupCnNameStr(weightList,heatMapList,methodFlag);
        }
        // 量纲颗粒度
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(monthAnalysisVO.getGranularityType())) {
            dimensionWightAndHeatGroupCnNameStr(weightList,heatMapList,methodFlag);
        }
    }

    private void indexCostGroupCnName(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexVOList, String methodFlag) {
        // 通用颗粒度
        if (IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(monthAnalysisVO.getGranularityType())) {
            universalGroupCnNameStr(priceIndexVOList, methodFlag);
        }
        // 盈利颗粒度
        if (IndustryIndexEnum.GRANULARITY_TYPE.PROFITS.getValue().equals(monthAnalysisVO.getGranularityType())) {
            profitGroupCnNameStr(priceIndexVOList, methodFlag);
        }
        // 量纲颗粒度
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(monthAnalysisVO.getGranularityType())) {
            dimensionGroupCnNameStr(priceIndexVOList, methodFlag);
        }
    }

    private void yoYCostGroupCnName(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthYoyVO> yoyAndPopIndexVOList) {
        // 通用颗粒度
        if (IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(monthAnalysisVO.getGranularityType())) {
            universalYoyGroupCnNameStr(yoyAndPopIndexVOList);
        }
        // 盈利颗粒度
        if (IndustryIndexEnum.GRANULARITY_TYPE.PROFITS.getValue().equals(monthAnalysisVO.getGranularityType())) {
            profitYoyGroupCnNameStr(yoyAndPopIndexVOList);
        }
        // 量纲颗粒度
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(monthAnalysisVO.getGranularityType())) {
            dimensionYoyGroupCnNameStr(yoyAndPopIndexVOList);
        }
    }

    private void dimensionYoyGroupCnNameStr(List<DmFocMonthYoyVO> yoyAndPopIndexVOList) {
        yoyAndPopIndexVOList.stream().forEach(ele -> {
            StringBuffer groupCnNameDms = new StringBuffer();
            String dmsCnName = getDmsCnName(ele,null,null,null);
            groupCnNameDms.append(ele.getGroupCnName()).append("(").append(dmsCnName).append(")");
            ele.setGroupCnName(groupCnNameDms.toString());
        });
    }

    private void dimensionWightAndHeatGroupCnNameStr(List<DmFocMonthWeightVO> weightList,List<DmFocActualCostVO> heatMapList,String methodFlag) {
        if ("weightList".equals(methodFlag)) {
            weightList.stream().forEach(ele -> {
                String dmsCnName = getDmsCnName(null,ele,null,null);
                ele.setParentCnName(ele.getParentCnName() + "(" + dmsCnName + ")");
            });
        }
        if ("heatMapList".equals(methodFlag)) {
            heatMapList.stream().forEach(ele -> {
                String heatMapDmsCnName = getDmsCnName(null,null,ele,null);
                ele.setParentCnName(ele.getParentCnName() + "(" + heatMapDmsCnName + ")");
            });
        }
    }
    @Nullable
    private String getDmsCnName(DmFocMonthYoyVO yoyVO,
                                DmFocMonthWeightVO weightVO,DmFocActualCostVO actualCostVO,DmFocMonthCostIdxVO costIdxVO) {
        String dmsCnName = getCoa(yoyVO, weightVO, actualCostVO, costIdxVO);
        dmsCnName = getDimension(yoyVO, weightVO, actualCostVO, costIdxVO, dmsCnName);
        dmsCnName = getSubCategory(yoyVO, weightVO, actualCostVO, costIdxVO, dmsCnName);
        dmsCnName = getSubDetail(yoyVO, weightVO, actualCostVO, costIdxVO, dmsCnName);
        dmsCnName = getSpart(yoyVO, weightVO, actualCostVO, costIdxVO, dmsCnName);
        return dmsCnName;
    }

    private String getCoa(DmFocMonthYoyVO yoyVO, DmFocMonthWeightVO weightVO, DmFocActualCostVO actualCostVO, DmFocMonthCostIdxVO costIdxVO) {
        String dmsCnName = null;
        if (null != yoyVO && checkCodeAndName(yoyVO.getCoaCode(), yoyVO.getCoaCnName())) {
            dmsCnName = yoyVO.getCoaCode() + " " + yoyVO.getCoaCnName() + "(" +  yoyVO.getProdRndTeamCnName() +")";
        }
        if (null != weightVO && checkCodeAndName(weightVO.getCoaCode(), weightVO.getCoaCnName())) {
            dmsCnName = weightVO.getCoaCode() + " " + weightVO.getDimensionCnName() + "(" +  weightVO.getProdRndTeamCnName() +")";
        }
        if (null != actualCostVO && checkCodeAndName(actualCostVO.getCoaCode(), actualCostVO.getCoaCnName())) {
            dmsCnName = actualCostVO.getCoaCode() + " " + actualCostVO.getCoaCnName() + "(" +  actualCostVO.getProdRndTeamCnName() +")";
        }
        if (null != costIdxVO && checkCodeAndName(costIdxVO.getCoaCode(), costIdxVO.getCoaCnName())) {
            dmsCnName = costIdxVO.getCoaCode() + " " + costIdxVO.getCoaCnName() + "(" +  costIdxVO.getProdRndTeamCnName() +")";
        }
        return dmsCnName;
    }

    private Boolean checkCodeAndName(String code, String name) {
        if (StringUtils.isNotBlank(code) && StringUtils.isNotBlank(name)) {
            return true;
        }
        return false;
    }

    private String getDimension(DmFocMonthYoyVO yoyVO, DmFocMonthWeightVO weightVO, DmFocActualCostVO actualCostVO, DmFocMonthCostIdxVO costIdxVO, String dmsCnName) {
        if (null != yoyVO && checkCodeAndName(yoyVO.getDimensionCode(), yoyVO.getDimensionCnName())) {
            if (checkCodeAndName(yoyVO.getCoaCode(), yoyVO.getCoaCnName())) {
                dmsCnName = yoyVO.getDimensionCode() + " " + yoyVO.getDimensionCnName() + "(" + yoyVO.getCoaCode() + " " + yoyVO.getCoaCnName() + "(" +  yoyVO.getProdRndTeamCnName() +")";
            } else {
                dmsCnName = yoyVO.getDimensionCode() + " " + yoyVO.getDimensionCnName() + "(" +  yoyVO.getProdRndTeamCnName() +")";
            }
        }
        if (null != weightVO && checkCodeAndName(weightVO.getDimensionCode(), weightVO.getDimensionCnName())) {
            if (checkCodeAndName(weightVO.getCoaCode(),weightVO.getCoaCnName())) {
                dmsCnName = weightVO.getDimensionCode() + " " + weightVO.getDimensionCnName() + "(" + weightVO.getCoaCode() + " " + weightVO.getCoaCnName() + "(" +  weightVO.getProdRndTeamCnName() +")";
            } else {
                dmsCnName = weightVO.getDimensionCode() + " " + weightVO.getDimensionCnName() + "(" +  weightVO.getProdRndTeamCnName() +")";
            }
        }
        dmsCnName = getDimensionOther(actualCostVO, costIdxVO, dmsCnName);
        return dmsCnName;
    }

    private String getDimensionOther(DmFocActualCostVO actualCostVO, DmFocMonthCostIdxVO costIdxVO, String dmsCnName) {
        if (null != actualCostVO && checkCodeAndName(actualCostVO.getDimensionCode(), actualCostVO.getDimensionCnName())) {
            if (checkCodeAndName(actualCostVO.getCoaCode(), actualCostVO.getCoaCnName())) {
                dmsCnName = actualCostVO.getDimensionCode() + " " + actualCostVO.getDimensionCnName() + "(" + actualCostVO.getCoaCode() + " " + actualCostVO.getCoaCnName() + "(" +  actualCostVO.getProdRndTeamCnName() +")";
            } else {
                dmsCnName = actualCostVO.getDimensionCode() + " " + actualCostVO.getDimensionCnName() + "(" +  actualCostVO.getProdRndTeamCnName() +")";
            }
        }
        if (null != costIdxVO && checkCodeAndName(costIdxVO.getDimensionCode(), costIdxVO.getDimensionCnName())) {
            if (checkCodeAndName(costIdxVO.getCoaCode(), costIdxVO.getCoaCnName())) {
                dmsCnName = costIdxVO.getDimensionCode() + " " + costIdxVO.getDimensionCnName() + "(" + costIdxVO.getCoaCode() + " " + costIdxVO.getCoaCnName() + "(" +  costIdxVO.getProdRndTeamCnName() +")";
            } else {
                dmsCnName = costIdxVO.getDimensionCode() + " " + costIdxVO.getDimensionCnName() + "(" +  costIdxVO.getProdRndTeamCnName() +")";
            }
        }
        return dmsCnName;
    }

    private String getSubCategory(DmFocMonthYoyVO yoyVO, DmFocMonthWeightVO weightVO, DmFocActualCostVO actualCostVO, DmFocMonthCostIdxVO costIdxVO, String dmsCnName) {
        if (null != yoyVO && checkCodeAndName(yoyVO.getDimensionSubCategoryCode(), yoyVO.getDimensionSubCategoryCnName())) {
            if (checkCodeAndName(yoyVO.getCoaCode(), yoyVO.getCoaCnName())) {
                dmsCnName = yoyVO.getDimensionSubCategoryCode() + " " + yoyVO.getDimensionSubCategoryCnName() + "(" + yoyVO.getCoaCode() + " " + yoyVO.getCoaCnName() + "(" +  yoyVO.getProdRndTeamCnName() +")";
            } else {
                dmsCnName = yoyVO.getDimensionSubCategoryCode() + " " + yoyVO.getDimensionSubCategoryCnName() + "(" +  yoyVO.getProdRndTeamCnName() +")";
            }
        }
        if (null != weightVO && checkCodeAndName(weightVO.getDimensionSubCategoryCode(), weightVO.getDimensionSubCategoryCnName())) {
            if (checkCodeAndName(weightVO.getCoaCode(), weightVO.getCoaCnName())) {
                dmsCnName = weightVO.getDimensionSubCategoryCode() + " " + weightVO.getDimensionSubCategoryCnName() + "(" + weightVO.getCoaCode() + " " + weightVO.getCoaCnName() + "(" +  weightVO.getProdRndTeamCnName() +")";
            } else {
                dmsCnName = weightVO.getDimensionSubCategoryCode() + " " + weightVO.getDimensionSubCategoryCnName() + "(" +  weightVO.getProdRndTeamCnName() +")";
            }
        }
        dmsCnName = getSubCategoryOther(actualCostVO, costIdxVO, dmsCnName);
        return dmsCnName;
    }

    private String getSubCategoryOther(DmFocActualCostVO actualCostVO, DmFocMonthCostIdxVO costIdxVO, String dmsCnName) {
        if (null != actualCostVO && checkCodeAndName(actualCostVO.getDimensionSubCategoryCode() ,actualCostVO.getDimensionSubCategoryCnName())) {
            if (checkCodeAndName(actualCostVO.getCoaCode(), actualCostVO.getCoaCnName())) {
                dmsCnName = actualCostVO.getDimensionSubCategoryCode() + " " + actualCostVO.getDimensionSubCategoryCnName() + "(" + actualCostVO.getCoaCode() + " " + actualCostVO.getCoaCnName() + "(" +  actualCostVO.getProdRndTeamCnName() +")";
            } else {
                dmsCnName = actualCostVO.getDimensionSubCategoryCode() + " " + actualCostVO.getDimensionSubCategoryCnName() + "(" +  actualCostVO.getProdRndTeamCnName() +")";
            }
        }
        if (null != costIdxVO && checkCodeAndName(costIdxVO.getDimensionSubCategoryCode(), costIdxVO.getDimensionSubCategoryCnName())) {
            if (checkCodeAndName(costIdxVO.getCoaCode(), costIdxVO.getCoaCnName())) {
                dmsCnName = costIdxVO.getDimensionSubCategoryCode() + " " + costIdxVO.getDimensionSubCategoryCnName() + "(" + costIdxVO.getCoaCode() + " " + costIdxVO.getCoaCnName() + "(" +  costIdxVO.getProdRndTeamCnName() +")";
            } else {
                dmsCnName = costIdxVO.getDimensionSubCategoryCode() + " " + costIdxVO.getDimensionSubCategoryCnName() + "(" +  costIdxVO.getProdRndTeamCnName() +")";
            }
        }
        return dmsCnName;
    }

    private String getSubDetail(DmFocMonthYoyVO yoyVO, DmFocMonthWeightVO weightVO, DmFocActualCostVO actualCostVO, DmFocMonthCostIdxVO costIdxVO, String dmsCnName) {
        if (null != yoyVO && checkCodeAndName(yoyVO.getDimensionSubDetailCode(), yoyVO.getDimensionSubDetailCnName())) {
            if (checkCodeAndName(yoyVO.getCoaCode(), yoyVO.getCoaCnName())) {
                dmsCnName = yoyVO.getDimensionSubDetailCode() + " " + yoyVO.getDimensionSubDetailCnName() + "(" + yoyVO.getCoaCode() + " " + yoyVO.getCoaCnName() + "(" +  yoyVO.getProdRndTeamCnName() +")";
            } else {
                dmsCnName = yoyVO.getDimensionSubDetailCode() + " " + yoyVO.getDimensionSubDetailCnName() + "(" +  yoyVO.getProdRndTeamCnName() +")";
            }
        }
        if (null != weightVO && checkCodeAndName(weightVO.getDimensionSubDetailCode(), weightVO.getDimensionSubDetailCnName())) {
            if (checkCodeAndName(weightVO.getCoaCode(), weightVO.getCoaCnName())) {
                dmsCnName = weightVO.getDimensionSubDetailCode() + " " + weightVO.getDimensionSubDetailCnName() + "(" + weightVO.getCoaCode() + " " + weightVO.getCoaCnName() + "(" +  weightVO.getProdRndTeamCnName() +")";
            } else {
                dmsCnName = weightVO.getDimensionSubDetailCode() + " " + weightVO.getDimensionSubDetailCnName() + "(" +  weightVO.getProdRndTeamCnName() +")";
            }
        }
        dmsCnName = getSubDetailOther(actualCostVO, costIdxVO, dmsCnName);
        return dmsCnName;
    }

    private String getSubDetailOther(DmFocActualCostVO actualCostVO, DmFocMonthCostIdxVO costIdxVO, String dmsCnName) {
        if (null != actualCostVO && checkCodeAndName(actualCostVO.getDimensionSubDetailCode(), actualCostVO.getDimensionSubDetailCnName())) {
            if (checkCodeAndName(actualCostVO.getCoaCode(), actualCostVO.getCoaCnName())) {
                dmsCnName = actualCostVO.getDimensionSubDetailCode() + " " + actualCostVO.getDimensionSubDetailCnName() + "(" + actualCostVO.getCoaCode() + " " + actualCostVO.getCoaCnName() + "(" +  actualCostVO.getProdRndTeamCnName() +")";
            } else {
                dmsCnName = actualCostVO.getDimensionSubDetailCode() + " " + actualCostVO.getDimensionSubDetailCnName() + "(" +  actualCostVO.getProdRndTeamCnName() +")";
            }
        }
        if (null != costIdxVO && checkCodeAndName(costIdxVO.getDimensionSubDetailCode(), costIdxVO.getDimensionSubDetailCnName())) {
            if (checkCodeAndName(costIdxVO.getCoaCode(), costIdxVO.getCoaCnName())) {
                dmsCnName = costIdxVO.getDimensionSubDetailCode() + " " + costIdxVO.getDimensionSubDetailCnName() + "(" + costIdxVO.getCoaCode() + " " + costIdxVO.getCoaCnName() + "(" +  costIdxVO.getProdRndTeamCnName() +")";
            } else {
                dmsCnName = costIdxVO.getDimensionSubDetailCode() + " " + costIdxVO.getDimensionSubDetailCnName() + "(" +  costIdxVO.getProdRndTeamCnName() +")";
            }
        }
        return dmsCnName;
    }

    private String getSpart(DmFocMonthYoyVO yoyVO, DmFocMonthWeightVO weightVO, DmFocActualCostVO actualCostVO, DmFocMonthCostIdxVO costIdxVO, String dmsCnName) {
        if (null != yoyVO && StringUtils.isNotBlank(yoyVO.getSpartCode())) {
            if (checkCodeAndName(yoyVO.getCoaCode(), yoyVO.getCoaCnName())) {
                dmsCnName = yoyVO.getSpartCode() + "(" + yoyVO.getCoaCode() + " " + yoyVO.getCoaCnName() + "(" +  yoyVO.getProdRndTeamCnName() +")";
            } else {
                dmsCnName = yoyVO.getSpartCode() + "(" +  yoyVO.getProdRndTeamCnName() +")";
            }
        }
        if (null != weightVO && StringUtils.isNotBlank(weightVO.getSpartCode())) {
            if (checkCodeAndName(weightVO.getCoaCode(), weightVO.getCoaCnName())) {
                dmsCnName = weightVO.getSpartCode() + "(" + weightVO.getCoaCode() + " " + weightVO.getCoaCnName() + "(" +  weightVO.getProdRndTeamCnName() +")";
            } else {
                dmsCnName = weightVO.getSpartCode() + "(" +  weightVO.getProdRndTeamCnName() +")";
            }
        }
        dmsCnName = getSpartOther(actualCostVO, costIdxVO, dmsCnName);
        return dmsCnName;
    }

    private String getSpartOther(DmFocActualCostVO actualCostVO, DmFocMonthCostIdxVO costIdxVO, String dmsCnName) {
        if (null != actualCostVO && StringUtils.isNotBlank(actualCostVO.getSpartCode())) {
            if (checkCodeAndName(actualCostVO.getCoaCode(), actualCostVO.getCoaCnName())) {
                dmsCnName = actualCostVO.getSpartCode() + "(" + actualCostVO.getCoaCode() + " " + actualCostVO.getCoaCnName() + "(" +  actualCostVO.getProdRndTeamCnName() +")";
            } else {
                dmsCnName = actualCostVO.getSpartCode() + "(" +  actualCostVO.getProdRndTeamCnName() +")";
            }
        }
        if (null != costIdxVO && StringUtils.isNotBlank(costIdxVO.getSpartCode())) {
            if (checkCodeAndName(costIdxVO.getCoaCode(), costIdxVO.getCoaCnName())) {
                dmsCnName = costIdxVO.getSpartCode() + "(" + costIdxVO.getCoaCode() + " " + costIdxVO.getCoaCnName() + "(" +  costIdxVO.getProdRndTeamCnName() +")";
            } else {
                dmsCnName = costIdxVO.getSpartCode() + "(" +  costIdxVO.getProdRndTeamCnName() +")";
            }
        }
        return dmsCnName;
    }

    private void dimensionGroupCnNameStr(List<DmFocMonthCostIdxVO> priceIndexVOList, String methodFlag) {
        priceIndexVOList.stream().forEach(ele -> {
            StringBuilder groupCnNameDms = new StringBuilder();
            String priceIndexDmsCnName = getDmsCnName(null,null,null,ele);
            groupCnNameDms.append(ele.getGroupCnName()).append("(").append(priceIndexDmsCnName).append(")");
            if ("indexCurrentLevel".equals(methodFlag)) {
                ele.setGroupCnName(groupCnNameDms.toString());
            } else {
                StringBuffer parentCnNameDms = new StringBuffer();
                parentCnNameDms.append(ele.getParentCnName()).append("(").append(priceIndexDmsCnName).append(")");
                ele.setParentCnName(parentCnNameDms.toString());
            }
        });
    }

    private void profitWeightAndHeatGroupCnNameStr(List<DmFocMonthWeightVO> weightList,List<DmFocActualCostVO> heatMapList,String methodFlag) {
        if ("weightList".equals(methodFlag)) {
            setWeightCharL1AndL2Name(weightList, heatMapList, methodFlag);
        } else {
            setHeatMapCharL1AndL2Name(weightList, heatMapList, methodFlag);
        }
    }

    private void setHeatMapCharL1AndL2Name(List<DmFocMonthWeightVO> weightList, List<DmFocActualCostVO> heatMapList, String methodFlag) {
        heatMapList.stream().forEach(ele -> {
            String profitNameHeatMap= getProfitName(null,null,null,ele);
            if (StringUtils.isNotBlank(profitNameHeatMap)) {
                ele.setParentCnName(ele.getParentCnName() + "(" + profitNameHeatMap + ")");
            } else {
                universalWightAndHeatGroupCnNameStr(weightList, heatMapList, methodFlag);
            }
        });
    }

    private void setWeightCharL1AndL2Name(List<DmFocMonthWeightVO> weightList, List<DmFocActualCostVO> heatMapList, String methodFlag) {
        weightList.stream().forEach(ele -> {
            String profitNameWeight = getProfitName(null,null,ele,null);
            if (StringUtils.isNotBlank(profitNameWeight)) {
                ele.setParentCnName(ele.getParentCnName() + "(" + profitNameWeight + ")");
            } else {
                universalWightAndHeatGroupCnNameStr(weightList, heatMapList, methodFlag);
            }
        });
    }

    private void profitGroupCnNameStr(List<DmFocMonthCostIdxVO> priceIndexVOList, String methodFlag) {
        priceIndexVOList.stream().forEach(ele -> {
            String profitNameCostId = getProfitName(ele, null, null, null);
            if (StringUtils.isNotBlank(profitNameCostId)) {
                if ("indexCurrentLevel".equals(methodFlag)) {
                    ele.setGroupCnName(ele.getGroupCnName() + "(" + profitNameCostId + ")");
                } else {
                    ele.setParentCnName(ele.getParentCnName() + "(" + profitNameCostId + ")");
                }
            } else {
                universalGroupCnNameStr(priceIndexVOList, methodFlag);
            }
        });
    }

    private void profitYoyGroupCnNameStr(List<DmFocMonthYoyVO> yoyAndPopIndexVOList) {
        yoyAndPopIndexVOList.stream().forEach(ele -> {
            String profitNameYoyAndPop = getProfitName(null, ele,null,null);
            if (StringUtils.isNotBlank(profitNameYoyAndPop)) {
                ele.setGroupCnName(ele.getGroupCnName() + "(" + profitNameYoyAndPop + ")");
            } else {
                universalYoyGroupCnNameStr(yoyAndPopIndexVOList);
            }
        });
    }

    private void universalWightAndHeatGroupCnNameStr(List<DmFocMonthWeightVO> weightList, List<DmFocActualCostVO> heatMapList, String methodFlag) {
        if ("weightList".equals(methodFlag)){
            weightList.stream().forEach(ele -> {
                ele.setParentCnName(ele.getParentCnName() + "(" +ele.getProdRndTeamCnName() +")");
            });
        }
        if ("heatMapList".equals(methodFlag)) {
            heatMapList.stream().forEach(ele -> {
                ele.setParentCnName(ele.getParentCnName() + "(" +ele.getProdRndTeamCnName() +")");
            });
        }
    }
    private void universalGroupCnNameStr(List<DmFocMonthCostIdxVO> priceIndexVOList,String methodFlag) {
        priceIndexVOList.stream().forEach(ele -> {
            StringBuffer groupCnName = new StringBuffer();
            groupCnName.append(ele.getGroupCnName()).append("(").append(ele.getProdRndTeamCnName()).append(")");
            if ("indexCurrentLevel".equals(methodFlag) ) {
                ele.setGroupCnName(groupCnName.toString());
            } else {
                StringBuffer parentCnName = new StringBuffer();
                parentCnName.append(ele.getParentCnName()).append("(").append(ele.getProdRndTeamCnName()).append(")");
                ele.setParentCnName(parentCnName.toString());
            }
        });
    }
    private void universalYoyGroupCnNameStr(List<DmFocMonthYoyVO> yoyAndPopIndexVOList) {
        yoyAndPopIndexVOList.stream().forEach(ele -> {
            StringBuffer groupCnName = new StringBuffer();
            groupCnName.append(ele.getGroupCnName()).append("(").append(ele.getProdRndTeamCnName()).append(")");
            ele.setGroupCnName(groupCnName.toString());
        });
    }

    /**
     * 查询产业成本指数同比环比图
     *
     * @param monthAnalysisVO 参数参数VO
     * @return ResultDataVO result data
     */
    public ResultDataVO getIndustryCostYoyAndPopChart(MonthAnalysisVO monthAnalysisVO) {
        LOGGER.info(">>>Begin MonthCommonService::getIndustryCostYoyAndPopChart");
        // 第1步： 校验必填参数
        String industryOrg = monthAnalysisVO.getIndustryOrg();
        if (CollectionUtils.isEmpty(monthAnalysisVO.getGroupCodeList())
                || StringUtils.isAnyBlank(monthAnalysisVO.getViewFlag(), industryOrg)) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        if (monthAnalysisVO.getVersionId() == null) {
            monthAnalysisVO.setVersionId(commonService.getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),monthAnalysisVO.getTablePreFix()));
        }
        // 第2步：检查基期开始时间和结束时间是否传参，若没有，则默认取报告期24个月的开始时间和结束时间
        FcstIndexUtil.handlePeriodForMonthYoy(monthAnalysisVO,dmFocMonthCostIdxDao.findActualMonthNum(monthAnalysisVO.getTablePreFix()).toString());
        List<DmFocMonthYoyVO> yoyList = new ArrayList<>();
        List<DmFocMonthYoyVO> popList = new ArrayList<>();
        List<DmFocMonthYoyVO> yoyAndPopList = new ArrayList<>();
        if (monthAnalysisVO.getIsContainComb() && CollectionUtils.isNotEmpty(monthAnalysisVO.getCustomIdList())) {
            addCombYoyAndPopList(monthAnalysisVO, yoyList, popList);
        } else {
            // 查询采购成本下的同比/环比
            getYoyAndPopForPurchase(monthAnalysisVO, yoyList, popList);
            // 查询制造成本下的同比/环比 无预测数据
            getYoyAndPopForManufacture(monthAnalysisVO, yoyList, popList);
            // 查询总成本下的同比/环比 无预测数据
            getYoyAndPopDataListForTotal(monthAnalysisVO, yoyList, popList);
            yoyAndPopList.addAll(yoyList);
            yoyAndPopList.addAll(popList);
        }
        HashMap map = new HashMap();
        map.put("yoyList",yoyList);
        map.put("popList",popList);
        return ResultDataVO.success(map);
    }

    private void getYoyAndPopDataListForTotal(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthYoyVO> yoyList, List<DmFocMonthYoyVO> popList) {
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(monthAnalysisVO.getCostType())) {
            // 总成本下多选场景 只查询单一的成本类型
            if (monthAnalysisVO.getIsMultipleSelect()) {
                // 查询采购成本下的同比/环比
                getYoyAndPopForPurchase(monthAnalysisVO, yoyList, popList);
                // 查询制造成本下的同比/环比 无预测数据
                getYoyAndPopForManufacture(monthAnalysisVO, yoyList, popList);
                // 查询总成本下的同比/环比 无预测数据
                getYoyAndPopForTotal(monthAnalysisVO, yoyList, popList);
            } else {
                // 总成本下单选场景 需要查询3种成本类型的数据
                asyncQueryService.findTotalYoyAndPopDataList(monthAnalysisVO, yoyList, popList);
            }
        }
    }

    // 查询总成本下的同比/环比 无预测数据
    private void getYoyAndPopForTotal(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthYoyVO> yoyList, List<DmFocMonthYoyVO> popList) {
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(monthAnalysisVO.getCostType())
                || IndustryIndexEnum.COST_TYPE.T.getValue().equals(monthAnalysisVO.getCostSubType())) {
            setEndTime(monthAnalysisVO);
            // 同比list
            if ("YOY".equals(monthAnalysisVO.getYoyOrPop())) {
                monthAnalysisVO.setYoyFlag("YOY");
                yoyList.addAll(dmFocTotalMonthYoyDao.findTotalMonthYoyVOList(monthAnalysisVO));
            }
            // 环比list
            if ("POP".equals(monthAnalysisVO.getYoyOrPop())) {
                monthAnalysisVO.setYoyFlag("POP");
                popList.addAll(dmFocTotalMonthYoyDao.findTotalMonthYoyVOList(monthAnalysisVO));
            }
            if (StringUtils.isBlank(monthAnalysisVO.getYoyOrPop())) {
                // 同比list
                monthAnalysisVO.setYoyFlag("YOY");
                yoyList.addAll(dmFocTotalMonthYoyDao.findTotalMonthYoyVOList(monthAnalysisVO));
                // 环比list
                monthAnalysisVO.setYoyFlag("POP");
                popList.addAll(dmFocTotalMonthYoyDao.findTotalMonthYoyVOList(monthAnalysisVO));
            }
        }
    }

    // 查询制造成本下的同比/环比 无预测数据
    private void getYoyAndPopForManufacture(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthYoyVO> yoyList, List<DmFocMonthYoyVO> popList) {
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())
                || IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostSubType())) {
            setStartEndTime(monthAnalysisVO);
            // 同比list
            if ("YOY".equals(monthAnalysisVO.getYoyOrPop())) {
                monthAnalysisVO.setYoyFlag("YOY");
                yoyList.addAll(dmFocMadeMonthYoyDao.findMadeMonthYoyVOList(monthAnalysisVO));
            }
            if ("POP".equals(monthAnalysisVO.getYoyOrPop())) {
                // 环比list
                monthAnalysisVO.setYoyFlag("POP");
                popList.addAll(dmFocMadeMonthYoyDao.findMadeMonthYoyVOList(monthAnalysisVO));
            }
            if (StringUtils.isBlank(monthAnalysisVO.getYoyOrPop())) {
                // 同比list
                monthAnalysisVO.setYoyFlag("YOY");
                yoyList.addAll(dmFocMadeMonthYoyDao.findMadeMonthYoyVOList(monthAnalysisVO));
                // 环比list
                monthAnalysisVO.setYoyFlag("POP");
                popList.addAll(dmFocMadeMonthYoyDao.findMadeMonthYoyVOList(monthAnalysisVO));
            }
        }
    }

    // 查询采购成本下的同比/环比
    private void getYoyAndPopForPurchase(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthYoyVO> yoyList, List<DmFocMonthYoyVO> popList) {
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())
                || IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostSubType())) {
            // 同比list
            if ("YOY".equals(monthAnalysisVO.getYoyOrPop())) {
                monthAnalysisVO.setYoyFlag("YOY");
                yoyList.addAll(dmFocMonthYoyDao.findDmFocMonthYoyVOList(monthAnalysisVO));
            }
            if ("POP".equals(monthAnalysisVO.getYoyOrPop())) {
                // 环比list
                monthAnalysisVO.setYoyFlag("POP");
                popList.addAll(dmFocMonthYoyDao.findDmFocMonthYoyVOList(monthAnalysisVO));
            }
            if (StringUtils.isBlank(monthAnalysisVO.getYoyOrPop())) {
                // 同比list
                monthAnalysisVO.setYoyFlag("YOY");
                yoyList.addAll(dmFocMonthYoyDao.findDmFocMonthYoyVOList(monthAnalysisVO));
                // 环比list
                monthAnalysisVO.setYoyFlag("POP");
                popList.addAll(dmFocMonthYoyDao.findDmFocMonthYoyVOList(monthAnalysisVO));
            }
        }
    }

    private void addCombYoyAndPopList(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthYoyVO> yoyList, List<DmFocMonthYoyVO> popList) {
        distinguishIfCombine(monthAnalysisVO);
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getGroupCodeList())) {
            // 查询采购成本下的同比/环比
            getYoyAndPopForPurchase(monthAnalysisVO, yoyList, popList);
            // 查询制造成本下的同比/环比 无预测数据
            getYoyAndPopForManufacture(monthAnalysisVO, yoyList, popList);
        }
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getCombinaCodeList())) {
            List<DmFocMonthYoyVO> combYoyVOList = new ArrayList<>();
            List<DmFocMonthYoyVO> combPopVOList = new ArrayList<>();
            // 查询采购成本下汇总组合的同比/环比
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
                if ("YOY".equals(monthAnalysisVO.getYoyOrPop())) {
                    monthAnalysisVO.setYoyFlag("YOY");
                    combYoyVOList = dmFocMonthYoyDao.findDmFocMonthCombYoyVOList(monthAnalysisVO);
                }
                if ("POP".equals(monthAnalysisVO.getYoyOrPop())) {
                    monthAnalysisVO.setYoyFlag("POP");
                    combPopVOList = dmFocMonthYoyDao.findDmFocMonthCombYoyVOList(monthAnalysisVO);
                }
                if (StringUtils.isBlank(monthAnalysisVO.getYoyOrPop())) {
                    monthAnalysisVO.setYoyFlag("YOY");
                    combYoyVOList = dmFocMonthYoyDao.findDmFocMonthCombYoyVOList(monthAnalysisVO);
                    monthAnalysisVO.setYoyFlag("POP");
                    combPopVOList = dmFocMonthYoyDao.findDmFocMonthCombYoyVOList(monthAnalysisVO);
                }
            }
            // 查询制造成本下汇总组合的同比/环比
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())) {
                Long actualMonthNum = commonService.findActualMonthNum(monthAnalysisVO.getIndustryOrg());
                monthAnalysisVO.setPeriodEndTime(Integer.valueOf(actualMonthNum.toString()));
                if ("YOY".equals(monthAnalysisVO.getYoyOrPop())) {
                    monthAnalysisVO.setYoyFlag("YOY");
                    combYoyVOList = dmFocMadeMonthYoyDao.findMadeMonthCombYoyVOList(monthAnalysisVO);
                }
                if ("POP".equals(monthAnalysisVO.getYoyOrPop())) {
                    monthAnalysisVO.setYoyFlag("POP");
                    combPopVOList = dmFocMadeMonthYoyDao.findMadeMonthCombYoyVOList(monthAnalysisVO);
                }
                if (StringUtils.isBlank(monthAnalysisVO.getYoyOrPop())) {
                    monthAnalysisVO.setYoyFlag("YOY");
                    combYoyVOList = dmFocMadeMonthYoyDao.findMadeMonthCombYoyVOList(monthAnalysisVO);
                    monthAnalysisVO.setYoyFlag("POP");
                    combPopVOList = dmFocMadeMonthYoyDao.findMadeMonthCombYoyVOList(monthAnalysisVO);
                }
            }
            yoyList.addAll(combYoyVOList);
            popList.addAll(combPopVOList);
        }
    }

    private void setYoyAndPopCnName(DmFocMonthYoyVO focMonthYoyAndPopVO) {
        if (!focMonthYoyAndPopVO.getGroupCnName().contains(focMonthYoyAndPopVO.getCustomCnName())) {
            StringBuffer groupCnName = new StringBuffer();
            groupCnName.append(focMonthYoyAndPopVO.getGroupCnName()).append("(").append(focMonthYoyAndPopVO.getCustomCnName()).append(")");
            focMonthYoyAndPopVO.setGroupCnName(groupCnName.toString());
        }
    }

    private void addCombYoyAndPopListExp(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthYoyVO> yoyAndPopList) {
        distinguishIfCombine(monthAnalysisVO);
        // 同比环比list
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getGroupCodeList())) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
                yoyAndPopList.addAll(dmFocMonthYoyDao.findDmFocMonthYoyVOList(monthAnalysisVO));
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())) {
                setMonthStartEndTime(monthAnalysisVO);
                yoyAndPopList.addAll(dmFocMadeMonthYoyDao.findMadeMonthYoyVOList(monthAnalysisVO));
            }
            if (monthAnalysisVO.getIsMultipleSelect()) {
                mutilSelectGroupCnName(monthAnalysisVO,null, yoyAndPopList,null,null,"yoyAndPopIndexCost");
            }
        }
        // 汇总组合处理
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getCombinaCodeList())) {
            List<DmFocMonthYoyVO> combYoyAndPopVOList = new ArrayList<>();
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
                combYoyAndPopVOList = dmFocMonthYoyDao.findDmFocMonthCombYoyVOList(monthAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())) {
                setMonthStartEndTime(monthAnalysisVO);
                combYoyAndPopVOList = dmFocMadeMonthYoyDao.findMadeMonthCombYoyVOList(monthAnalysisVO);
            }
            for (DmFocMonthYoyVO focMonthYoyAndPop : combYoyAndPopVOList) {
                setYoyAndPopCnName(focMonthYoyAndPop);
            }
            yoyAndPopList.addAll(combYoyAndPopVOList);
        }
    }


    public void distinguishIfCombine(MonthAnalysisVO monthAnalysisVO) {
        List<String> combinaCodeList = new ArrayList<>();
        List<String> groupCodeList = new ArrayList<>();
        List<String> allGroupCodeList = monthAnalysisVO.getGroupCodeList();
        monthAnalysisVO.setGroupCodeList(null);
        for (String groupCode : allGroupCodeList) {
            if (groupCode.contains("_##")) {
                if ("null".equals(groupCode.split("_##")[1])) {
                    // 组合的根节点
                    combinaCodeList.add(groupCode.split("_##")[0] + "_##" + groupCode.split("_##")[0]);
                }  else {
                    // 组合的子项
                    combinaCodeList.add(groupCode);
                }
            } else {
                // 正常维度的查询
                groupCodeList.add(groupCode);
            }
        }
        monthAnalysisVO.setCombinaCodeList(combinaCodeList);
        monthAnalysisVO.setGroupCodeList(groupCodeList);
    }

    /**
     * 查询导出的同比环比集合
     *
     * @param monthAnalysisVO 参数
     * @return List<Map> 结果
     */
    public List<Map> getMonthYoyListForExp(MonthAnalysisVO monthAnalysisVO) {
        // 第1步： 参数设置
        List<Map>  monthYoyExportList = new ArrayList<>();
        if (monthAnalysisVO.getVersionId() == null) {
            monthAnalysisVO.setVersionId(commonService.getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),monthAnalysisVO.getTablePreFix()));
        }
        // 第2步：检查基期开始时间和结束时间是否传参，若没有，则默认取报告期24个月的开始时间和结束时间
        Long actualMonthNum = commonService.findActualMonthNum(monthAnalysisVO.getIndustryOrg());
        FcstIndexUtil.handlePeriodForMonthYoy(monthAnalysisVO,actualMonthNum.toString());
        List<DmFocMonthYoyVO> allMonthYoyList = new ArrayList<>();
        if (monthAnalysisVO.getIsContainComb() && CollectionUtils.isNotEmpty(monthAnalysisVO.getCustomIdList())) {
            addCombYoyAndPopListExp(monthAnalysisVO,allMonthYoyList);
        } else {
            // 同比环比list
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
                allMonthYoyList = dmFocMonthYoyDao.findDmFocMonthYoyVOList(monthAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())) {
                setMonthStartEndTime(monthAnalysisVO);
                allMonthYoyList = dmFocMadeMonthYoyDao.findMadeMonthYoyVOList(monthAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(monthAnalysisVO.getCostType())) {
                allMonthYoyList.addAll(dmFocMonthYoyDao.findDmFocMonthYoyVOList(monthAnalysisVO));
                // 总成本
                monthAnalysisVO.setPeriodEndTime(Integer.valueOf(actualMonthNum.toString()));
                allMonthYoyList.addAll(dmFocTotalMonthYoyDao.findTotalMonthYoyVOList(monthAnalysisVO));
                // 制造成本
                setMonthStartEndTime(monthAnalysisVO);
                allMonthYoyList.addAll(dmFocMadeMonthYoyDao.findMadeMonthYoyVOList(monthAnalysisVO));
            }
            if (monthAnalysisVO.getIsMultipleSelect()) {
                mutilSelectGroupCnName(monthAnalysisVO,null,allMonthYoyList,null,null,"yoyAndPopIndexCost");
            }
        }
        monthYoyExportList = rowToColumn(allMonthYoyList);
        return monthYoyExportList;
    }

    private List<Map> rowToColumn(List<DmFocMonthYoyVO> allList) {
        // 存放行转列之后的数据
        List<Map> result = new ArrayList<>();
        // 存放临时的数据对象
        Map<String, Object> lmap = new HashMap<>();
        // 存放最终的数据对象
        HashMap<Object, Object> valueMap = new HashMap<>();
        for (DmFocMonthYoyVO dmFocMonthYoyVO : allList) {
            Long periodId = dmFocMonthYoyVO.getPeriodId();
            String groupCode = dmFocMonthYoyVO.getGroupCode();
            String groupCnName = dmFocMonthYoyVO.getGroupCnName();
            String yoyFlag = dmFocMonthYoyVO.getYoyFlag();
            String yoyPercent = dmFocMonthYoyVO.getYoyPercent();
            String costType = dmFocMonthYoyVO.getCostType();
            // 若临时map中不包括这个基期和编码，则创建新的valueMap来存放结果对象
            if (!lmap.containsKey(periodId + groupCode + groupCnName + costType)) {
                valueMap = new HashMap<>();
                valueMap.put("periodId",periodId);
                valueMap.put("groupCode",groupCode);
                valueMap.put("groupCnName",groupCnName);
                valueMap.put(yoyFlag,yoyPercent);
                valueMap.put("costType",costType);
                // 将新建的map存放到临时lmap对象中，用于下一次的判断
                lmap.put(periodId + groupCode + groupCnName+ costType,valueMap);
                result.add(valueMap);
            } else {
                // 若临时map中包含这一条记录，则在已有的基础上设置同步或者环比值
                valueMap = (HashMap<Object, Object>) lmap.get(periodId + groupCode + groupCnName+costType);
                valueMap.put(yoyFlag,yoyPercent);
            }
        }
        return result;
    }

    public void setMonthStartEndTime(MonthAnalysisVO monthAnalysisVO) {
        Map<String, Long> startEndTime = dmFocMadeMonthCostIdxDao
                .findStartEndTime(monthAnalysisVO.getGranularityType(), monthAnalysisVO.getTablePreFix());
        monthAnalysisVO.setPeriodStartTime(Integer.parseInt(startEndTime.get("start").toString()));
        monthAnalysisVO.setPeriodEndTime(Integer.parseInt(startEndTime.get("end").toString()));
    }

    @Transactional
    public boolean isDataIsOk(MonthAnalysisVO monthAnalysisVO) {
        // 默认基期不切换
        if (ifBasePeriodId(monthAnalysisVO)) {
            return true;
        }
        boolean flag = false;
        // 单选的切换基期数据量设置
        int monthCount = getMonthCount(monthAnalysisVO);
        // 多选的切换基期数据量设置
        if (CollectionUtils.isEmpty(monthAnalysisVO.getSubGroupCodeList())) {
            if (monthAnalysisVO.getIsMultipleSelect() && CollectionUtils.isNotEmpty(monthAnalysisVO.getGroupCodeList())) {
                monthCount = monthCount * (monthAnalysisVO.getGroupCodeList().size());
            }
        }
        MonthAnalysisVO paramsVO = ObjectCopyUtil.copy(monthAnalysisVO, MonthAnalysisVO.class);
        int indexByBasePeriodId = getIndexByBasePeriodId(monthAnalysisVO, paramsVO);
        if (CollectionUtils.isNotEmpty(paramsVO.getSubGroupCodeList())) {
            List<String> collects = paramsVO.getSubGroupCodeList().stream().filter(vo -> StringUtils.isNotEmpty(vo)).collect(Collectors.toList());
            if (collects.size() * monthCount > indexByBasePeriodId) {
                flag = false;
            } else {
                flag = true;
            }
            return flag;
        }
        if (indexByBasePeriodId <monthCount) {
            flag = false;
        }else {
            flag = true;
        }
        return flag;
    }

    private boolean ifBasePeriodId(MonthAnalysisVO monthAnalysisVO) {
        // 默认基期不切换
        List<String> threeYears  = annualCommonService.getYearList(monthAnalysisVO.getCostType(),monthAnalysisVO.getIndustryOrg());
        if (CollectionUtils.isNotEmpty(threeYears)) {
            int currentYear = NumberUtil.parseInt(threeYears.get(0));
            String basePeriodId = (currentYear - Constant.IntegerEnum.ONE.getValue()) + Constant.StrEnum.ZERO_ONE.getValue();
            if (basePeriodId.equals(monthAnalysisVO.getBasePeriodId().toString())) {
                return true;
            }
        }
        return false;
    }

    private int getMonthCount(MonthAnalysisVO monthAnalysisVO) {
        int monthCount = 0;
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())) {
            Map<String, Long> startEndTime = dmFocMadeMonthCostIdxDao
                    .findStartEndTime(monthAnalysisVO.getGranularityType(), monthAnalysisVO.getTablePreFix());
            if (null != startEndTime.get("start") && null != startEndTime.get("end")) {
                monthAnalysisVO.setPeriodStartTime(Integer.parseInt(startEndTime.get("start").toString()));
                monthAnalysisVO.setPeriodEndTime(Integer.parseInt(startEndTime.get("end").toString()));
                monthCount = dataCompare(monthAnalysisVO.getPeriodStartTime().toString(), monthAnalysisVO.getPeriodEndTime().toString());
            }
        }
        Long actualMonthNum = commonService.findActualMonthNum(monthAnalysisVO.getIndustryOrg());
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(monthAnalysisVO.getCostType())) {
            // 第1步：检查基期开始时间和结束时间是否传参，若没有，则默认取报告期36个月的开始时间和结束时间
            FcstIndexUtil.handlePeriod(monthAnalysisVO, actualMonthNum.toString());
            monthAnalysisVO.setPeriodEndTime(Integer.valueOf(actualMonthNum.toString()));
            monthCount =  dataCompare(monthAnalysisVO.getPeriodStartTime().toString(),actualMonthNum.toString());
        }
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
            // 第1步：检查基期开始时间和结束时间是否传参，若没有，则默认取报告期36个月的开始时间和结束时间
            FcstIndexUtil.handlePeriod(monthAnalysisVO, actualMonthNum.toString());
            monthCount =  dataCompare(monthAnalysisVO.getPeriodStartTime().toString(),monthAnalysisVO.getPeriodEndTime().toString());
        }
        return monthCount;
    }

    private int dataCompare(String startDate, String endDate) {
        int result = 0;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        Calendar from = Calendar.getInstance();
        Calendar to = Calendar.getInstance();
        try {
            from.setTime(sdf.parse(startDate));
            to.setTime(sdf.parse(endDate));

            int fromYear = from.get(Calendar.YEAR);
            int fromMonth = from.get(Calendar.MONTH);
            int toYear = to.get(Calendar.YEAR);
            int toMonth = to.get(Calendar.MONTH);

            result = toYear * 12 + toMonth - (fromYear * 12 + fromMonth) + 1;
        } catch (ParseException e) {
            LOGGER.error("error getMonthSpace :{} ", e.getLocalizedMessage());
        }
        return result;
    }

    private int getIndexByBasePeriodId(MonthAnalysisVO monthAnalysisVO, MonthAnalysisVO paramsVO) {
        int indexByBasePeriodId = 0;
        // 查询采购成本的数据
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType()) || IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostSubType())) {
            indexByBasePeriodId = dmFocMonthCostIdxDao.findPriceIndexByBasePeriodId(paramsVO);
        }
        // 查询制造成本的数据
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType()) || IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostSubType())) {
            indexByBasePeriodId = dmFocMadeMonthCostIdxDao.findMadePriceIdxByBasePeriodId(paramsVO);
        }
        // 查询总成本的数据
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(monthAnalysisVO.getCostType()) || IndustryIndexEnum.COST_TYPE.T.getValue().equals(monthAnalysisVO.getCostSubType())) {
            indexByBasePeriodId = dmFocTotalMonthCostIdxDao.findTotalPriceIdxByBasePeriodId(paramsVO);
        }
        return indexByBasePeriodId;
    }

    @Transactional
    public boolean hasCombNormalDataIsOk(MonthAnalysisVO monthAnalysisVO) {
        // 默认基期不切换
        if (ifBasePeriodId(monthAnalysisVO)) {
            return true;
        }
        boolean dataIsOk = false;
        int monthCount = getMonthCount(monthAnalysisVO);
        // 多选的切换基期数据量设置
        if (CollectionUtils.isEmpty(monthAnalysisVO.getSubGroupCodeList())) {
            if (monthAnalysisVO.getIsMultipleSelect() && CollectionUtils.isNotEmpty(monthAnalysisVO.getGroupCodeList())) {
                monthCount = monthCount * (monthAnalysisVO.getGroupCodeList().size());
            }
        }
        MonthAnalysisVO paramsVO = ObjectCopyUtil.copy(monthAnalysisVO, MonthAnalysisVO.class);
        List<DmFocMonthCostIdxVO> priceIndexNormalChartByMultiDim = new ArrayList<>();
        // 查询采购成本的数据
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
            priceIndexNormalChartByMultiDim = dmFocMonthCostIdxDao.findPriceIndexNormalChartByMultiDim(paramsVO);
        }
        // 查询采购成本的数据
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())) {
            priceIndexNormalChartByMultiDim = dmFocMadeMonthCostIdxDao.findMadePriceIndexNormalByMultiDim(paramsVO);
        }
        if (CollectionUtils.isNotEmpty(paramsVO.getSubGroupCodeList())) {
            List<String> collects = paramsVO.getSubGroupCodeList().stream().filter(vo -> StringUtils.isNotEmpty(vo)).collect(Collectors.toList());
            if (collects.size()*monthCount > priceIndexNormalChartByMultiDim.size()) {
                dataIsOk = false;
            } else {
                dataIsOk = true;
            }
        }
        return dataIsOk;
    }

    @Transactional
    public boolean reverseDataIsOk(MonthAnalysisVO monthAnalysisVO) {
        // 默认基期不切换
        if (ifBasePeriodId(monthAnalysisVO)) {
            return true;
        }
        boolean dataIsOk = false;
        int revMonthCount = getMonthCount(monthAnalysisVO);
        // 多选的切换基期数据量设置
        if (CollectionUtils.isEmpty(monthAnalysisVO.getSubGroupCodeList())) {
            if (monthAnalysisVO.getIsMultipleSelect() && CollectionUtils.isNotEmpty(monthAnalysisVO.getGroupCodeList())) {
                revMonthCount = revMonthCount * (monthAnalysisVO.getGroupCodeList().size());
            }
        }
        MonthAnalysisVO paramsVO = ObjectCopyUtil.copy(monthAnalysisVO, MonthAnalysisVO.class);
        int indexDataCount = 0;
        // 查询采购成本的数据
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
            indexDataCount = dmFocRecMonthCostIdxDao.findRecPriceIndexByBasePeriodId(paramsVO);
        }
        // 查询制造成本的数据
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())) {
            indexDataCount = dmFocMadeRecMonthCostIdxDao.findMadeRecPriceIdxByBasePeriodId(paramsVO);
        }
        if (CollectionUtils.isNotEmpty(paramsVO.getSubGroupCodeList())) {
            List<String> collects = paramsVO.getSubGroupCodeList()
                    .stream().filter(vo -> StringUtils.isNotEmpty(vo)).collect(Collectors.toList());
            if (collects.size() * revMonthCount > indexDataCount) {
                dataIsOk = false;
            } else {
                dataIsOk = true;
            }
            return dataIsOk;
        }
        if (indexDataCount < revMonthCount) {
            dataIsOk = false;
        }else {
            dataIsOk = true;
        }
        return dataIsOk;
    }

    @Transactional
    public boolean isCombDataIsOk(MonthAnalysisVO monthAnalysisVO) {
        // 默认基期不切换
        if (ifBasePeriodId(monthAnalysisVO)) {
            return true;
        }
        boolean dataIsOk = false;
        int combMonthCount = getMonthCount(monthAnalysisVO);
        // 多选的切换基期数据量设置
        if (CollectionUtils.isEmpty(monthAnalysisVO.getCombinaSubGroupCodeList())) {
            if (monthAnalysisVO.getIsMultipleSelect() && CollectionUtils.isNotEmpty(monthAnalysisVO.getGroupCodeList())) {
                combMonthCount = combMonthCount * (monthAnalysisVO.getGroupCodeList().size());
            }
        }
        MonthAnalysisVO paramsVO = ObjectCopyUtil.copy(monthAnalysisVO, MonthAnalysisVO.class);
        int indexByBasePeriodId = 0;
        // 查询采购成本的数据
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
            indexByBasePeriodId = dmFocMonthCostIdxDao.findCombPriceIndexByBasePeriodId(paramsVO);
        }
        // 查询制造成本的数据
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())) {
            indexByBasePeriodId = dmFocMadeMonthCostIdxDao.findMadeCombPriceIdxByBasePeriodId(paramsVO);
        }
        // 多指数图时
        if (CollectionUtils.isNotEmpty(paramsVO.getCombinaSubGroupCodeList())) {
            List<String> collects = paramsVO.getCombinaSubGroupCodeList().stream().filter(vo -> StringUtils.isNotEmpty(vo)).collect(Collectors.toList());
            if (collects.size() * combMonthCount > indexByBasePeriodId) {
                dataIsOk = false;
            } else {
                dataIsOk = true;
            }
            return dataIsOk;
        }
        if (indexByBasePeriodId < combMonthCount) {
            dataIsOk = false;
        }else {
            dataIsOk = true;
        }
        return dataIsOk;
    }


    public void setParams(MonthAnalysisVO monthAnalysisVO) {
        if (monthAnalysisVO.getReverseViewFlag()) {
            if (CollectionUtils.isNotEmpty(monthAnalysisVO.getPurCodeList())) {
                String mutliPurCode = monthAnalysisVO.getPurCodeList().stream().map(String::valueOf).collect(Collectors.joining(","));
                monthAnalysisVO.setPurCode(mutliPurCode);
            } else {
                String multiGroupCode = monthAnalysisVO.getGroupCodeList().stream().map(String::valueOf).collect(Collectors.joining(","));
                monthAnalysisVO.setPurCode(multiGroupCode);
            }
        }
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getGroupCodeList())) {
            String mutliGroupCode = monthAnalysisVO.getGroupCodeList().stream().map(String::valueOf).collect(Collectors.joining(","));
            monthAnalysisVO.setGroupCode(mutliGroupCode);
        }
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getProdRndTeamCodeList())) {
            String mutliProdRndTeamCode = monthAnalysisVO.getProdRndTeamCodeList().stream().map(String::valueOf).collect(Collectors.joining(","));
            monthAnalysisVO.setProdRndTeamCode(mutliProdRndTeamCode);
        }
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getCustomIdList())) {
            String mutliCustomIds = monthAnalysisVO.getCustomIdList().stream().map(String::valueOf).collect(Collectors.joining(","));
            monthAnalysisVO.setCustomId(mutliCustomIds);
        }
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getCombinaCodeList())) {
            List<String> combinaCodeList = new ArrayList<>();
            monthAnalysisVO.getCombinaCodeList().forEach(item -> combinaCodeList.add(item.split("_##")[1]));
            String mutliCombinCode = combinaCodeList.stream().map(String::valueOf).collect(Collectors.joining(","));
            monthAnalysisVO.setGroupCode(mutliCombinCode);
        }
        setOtherParams(monthAnalysisVO);
    }

    private void setOtherParams(MonthAnalysisVO monthAnalysisVO) {
        // 多指数图的参数设置
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getParentCodeList())) {
            String mutliParentCode = monthAnalysisVO.getParentCodeList().stream().map(String::valueOf).collect(Collectors.joining(","));
            monthAnalysisVO.setGroupCode(mutliParentCode);
        }
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getCombParentCodeList())) {
            List<String> combParentCodeList = new ArrayList<>();
            monthAnalysisVO.getCombParentCodeList().forEach(item -> combParentCodeList.add(item.split("_##")[1]));
            String mutliCombParentCode = combParentCodeList.stream().map(String::valueOf).collect(Collectors.joining(","));
            monthAnalysisVO.setGroupCode(mutliCombParentCode);
        }
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getCoaCodeList())) {
            String multiCoaCode = monthAnalysisVO.getCoaCodeList().stream().map(String::valueOf).collect(Collectors.joining(","));
            monthAnalysisVO.setCoaCode(multiCoaCode);
        }
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getDmsCodeList())) {
            String mutliDmsCode = monthAnalysisVO.getDmsCodeList().stream().map(String::valueOf).collect(Collectors.joining(","));
            monthAnalysisVO.setDmsCode(mutliDmsCode);
        }
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getSpartCodeList())) {
            String spartCode = monthAnalysisVO.getSpartCodeList().stream().map(String::valueOf).collect(Collectors.joining(","));
            monthAnalysisVO.setSpartCode(spartCode);
        }
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getL1NameList())) {
            String mutliL1Name = monthAnalysisVO.getL1NameList().stream().map(String::valueOf).collect(Collectors.joining(","));
            monthAnalysisVO.setL1Name(mutliL1Name);
        }
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getL2NameList())) {
            String mutliL2Name = monthAnalysisVO.getL2NameList().stream().map(String::valueOf).collect(Collectors.joining(","));
            monthAnalysisVO.setL2Name(mutliL2Name);
        }
    }
}