<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.pbi.dao.IDmFcstCustomCombDao">
    <resultMap type="com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstDimInfoVO" id="resultMap">
        <result property="combId" column="comb_id"/>
        <result property="groupCode" column="groupCode"/>
        <result property="groupLevel" column="groupLevel"/>
        <result property="groupCnName" column="groupCnName"/>
        <result property="groupLevel" column="group_level"/>
        <result property="enableFlag" column="enable_flag"/>
    </resultMap>

    <select id="getCombinationSubByGroupLevel" resultMap="resultMap">
        SELECT
        <choose>
            <when test='nextGroupLevel == "LV1"'>
                distinct custom_id as comb_id,lv1_prod_rnd_team_code AS groupCode ,CONCAT(lv1_prod_rd_team_cn_name,'(',custom_cn_name,')') AS groupCnName, 'LV1' AS group_level
            </when>
            <when test='nextGroupLevel == "LV2"'>
                distinct custom_id as comb_id,lv2_prod_rnd_team_code AS groupCode ,CONCAT(lv2_prod_rd_team_cn_name,'(',custom_cn_name,')') AS groupCnName, 'LV2' AS group_level
            </when>
            <when test='nextGroupLevel == "LV3"'>
                distinct custom_id as comb_id,lv3_prod_rnd_team_code AS groupCode ,CONCAT(lv3_prod_rd_team_cn_name,'(',custom_cn_name,')') AS groupCnName, 'LV3' AS group_level
            </when>
            <when test='nextGroupLevel == "LV4"'>
                distinct custom_id as comb_id,lv4_prod_rnd_team_code AS groupCode ,CONCAT(lv4_prod_rd_team_cn_name,'(',custom_cn_name,')') AS groupCnName, 'LV4' AS group_level
            </when>
            <when test='nextGroupLevel == "DIMENSION"'>
                distinct custom_id as comb_id,dimension_code AS groupCode ,CONCAT(dimension_cn_name,'(',custom_cn_name,')') AS groupCnName, 'DIMENSION' AS group_level
            </when>
            <when test='nextGroupLevel == "SUBCATEGORY"'>
                distinct custom_id as comb_id,dimension_subcategory_code AS groupCode ,CONCAT(dimension_subcategory_cn_name,'(',custom_cn_name,')') AS groupCnName, 'SUBCATEGORY' AS group_level
            </when>
            <when test='nextGroupLevel == "SUB_DETAIL"'>
                distinct custom_id as comb_id,dimension_sub_detail_code AS groupCode ,CONCAT(dimension_sub_detail_cn_name,'(',custom_cn_name,')') AS groupCnName, 'SUB_DETAIL' AS group_level
            </when>
            <when test='nextGroupLevel == "SPART"'>
                distinct custom_id as comb_id,spart_code AS groupCode ,CONCAT(spart_cn_name,'(',custom_cn_name,')') AS groupCnName, 'SPART' AS group_level
            </when>
        </choose>
        from fin_dm_opt_foi.${tablePreFix}_custom_comb_d
        where del_flag ='N'
        <if test='granularityType != null and granularityType!=""'>
            and granularity_type = #{granularityType}
        </if>
        <if test='softwareMark!=null and softwareMark!=""'>
            and software_mark = #{softwareMark,jdbcType=VARCHAR}
        </if>
        <if test='regionCode!=null and regionCode!=""'>
            and region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode!=null and repofficeCode!=""'>
            and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='bgCode!=null and bgCode!=""'>
            and bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag!=null and overseaFlag!=""'>
            and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag!=null and viewFlag!=""'>
            and view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag!=null and mainFlag!=""'>
            and main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='roleId != null and roleId!=""'>
            and role_id = #{roleId}
        </if>
        <if test='userId != null and userId!=""'>
            and user_id = #{userId}
        </if>
        <if test='pageFlag != null and pageFlag!=""'>
            and (page_flag = #{pageFlag} or page_flag = concat('ALL_',#{pageFlag}::text))
        </if>
        <if test='nextGroupLevel =="LV1"'>
            and lv1_prod_rnd_team_code is not null
        </if>
        <if test='nextGroupLevel =="LV2"'>
            and lv2_prod_rnd_team_code is not null
        </if>
        <if test='nextGroupLevel =="LV3"'>
            and lv3_prod_rnd_team_code is not null
        </if>
        <if test='nextGroupLevel =="LV4"'>
            and lv4_prod_rnd_team_code is not null
        </if>
        <if test='nextGroupLevel =="DIMENSION"'>
            and dimension_code is not null
        </if>
        <if test='nextGroupLevel =="CATEGORY"'>
            and category_code is not null
        </if>
        <if test='nextGroupLevel =="SUBCATEGORY"'>
            and dimension_subcategory_code is not null
        </if>
        <if test='nextGroupLevel =="SUB_DETAIL"'>
            and dimension_sub_detail_code is not null
        </if>
        <if test='nextGroupLevel =="SPART"'>
            and spart_code is not null
        </if>
        and group_level = #{nextGroupLevel}
        <if test='combIdList != null and combIdList.size() > 0'>
            <foreach collection='combIdList' item="code" open="AND comb_id IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='combDimensionSubDetailCodeList != null and combDimensionSubDetailCodeList.size() > 0'>
            <foreach collection='combDimensionSubDetailCodeList' item="code" open="AND dimension_sub_detail_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='combDimensionSubcategoryCodeList != null and combDimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='combDimensionSubcategoryCodeList' item="code" open="AND dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='combDimensionCodeList != null and combDimensionCodeList.size() > 0'>
            <foreach collection='combDimensionCodeList' item="code" open="AND dimension_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='combSpartCodeList != null and combSpartCodeList.size() > 0'>
            <foreach collection='combSpartCodeList' item="code" open="AND spart_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='comblv1ProdRndTeamCodeList != null and comblv1ProdRndTeamCodeList.size() > 0'>
            <foreach collection='comblv1ProdRndTeamCodeList' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='comblv2ProdRndTeamCodeList != null and comblv2ProdRndTeamCodeList.size() > 0'>
            <foreach collection='comblv2ProdRndTeamCodeList' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='comblv3ProdRndTeamCodeList != null and comblv3ProdRndTeamCodeList.size() > 0'>
            <foreach collection='comblv3ProdRndTeamCodeList' item="code" open="AND lv3_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='comblv4ProdRndTeamCodeList != null and comblv4ProdRndTeamCodeList.size() > 0'>
            <foreach collection='comblv4ProdRndTeamCodeList' item="code" open="AND lv4_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
    </select>

    <select id="getCombinationByGroupLevel" resultMap="resultMap">
        SELECT
        <choose>
            <when test='nextGroupLevel == "LV1"'>
                distinct custom_id as comb_id,lv1_prod_rnd_team_code AS groupCode ,CONCAT(lv1_prod_rd_team_cn_name,'(',custom_cn_name,')') AS groupCnName, 'LV1' AS group_level
            </when>
            <when test='nextGroupLevel == "LV2"'>
                distinct custom_id as comb_id,lv2_prod_rnd_team_code AS groupCode ,CONCAT(lv2_prod_rd_team_cn_name,'(',custom_cn_name,')') AS groupCnName, 'LV2' AS group_level
            </when>
            <when test='nextGroupLevel == "LV3"'>
                distinct custom_id as comb_id,lv3_prod_rnd_team_code AS groupCode ,CONCAT(lv3_prod_rd_team_cn_name,'(',custom_cn_name,')') AS groupCnName, 'LV3' AS group_level
            </when>
            <when test='nextGroupLevel == "LV4"'>
                distinct custom_id as comb_id,lv4_prod_rnd_team_code AS groupCode ,CONCAT(lv4_prod_rd_team_cn_name,'(',custom_cn_name,')') AS groupCnName, 'LV4' AS group_level
            </when>
            <when test='nextGroupLevel == "DIMENSION"'>
                distinct custom_id as comb_id,dimension_code AS groupCode ,CONCAT(dimension_cn_name,'(',custom_cn_name,')') AS groupCnName, 'DIMENSION' AS group_level
            </when>
            <when test='nextGroupLevel == "SUBCATEGORY"'>
                distinct custom_id as comb_id,dimension_subcategory_code AS groupCode ,CONCAT(dimension_subcategory_cn_name,'(',custom_cn_name,')') AS groupCnName, 'SUBCATEGORY' AS group_level
            </when>
            <when test='nextGroupLevel == "SUB_DETAIL"'>
                distinct custom_id as comb_id,dimension_sub_detail_code AS groupCode ,CONCAT(dimension_sub_detail_cn_name,'(',custom_cn_name,')') AS groupCnName, 'SUB_DETAIL' AS group_level
            </when>
            <when test='nextGroupLevel == "SPART"'>
                distinct custom_id as comb_id,spart_code AS groupCode ,CONCAT(spart_cn_name,'(',custom_cn_name,')') AS groupCnName, 'SPART' AS group_level
            </when>
        </choose>
        from fin_dm_opt_foi.${tablePreFix}_custom_comb_d
        where del_flag ='N'
        <if test='granularityType != null and granularityType!=""'>
            and granularity_type = #{granularityType}
        </if>
        <if test='softwareMark!=null and softwareMark!=""'>
            and software_mark = #{softwareMark,jdbcType=VARCHAR}
        </if>
        <if test='regionCode!=null and regionCode!=""'>
            and region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode!=null and repofficeCode!=""'>
            and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='bgCode!=null and bgCode!=""'>
            and bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag!=null and overseaFlag!=""'>
            and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag!=null and viewFlag!=""'>
            and view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag!=null and mainFlag!=""'>
            and main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='roleId != null and roleId!=""'>
            and role_id = #{roleId}
        </if>
        <if test='userId != null and userId!=""'>
            and user_id = #{userId}
        </if>
        <if test='pageFlag != null and pageFlag!=""'>
            and (page_flag = #{pageFlag} or page_flag = concat('ALL_',#{pageFlag}::text))
        </if>
        <if test='nextGroupLevel =="LV1"'>
            and lv1_prod_rnd_team_code is not null
        </if>
        <if test='nextGroupLevel =="LV2"'>
            and lv2_prod_rnd_team_code is not null
        </if>
        <if test='nextGroupLevel =="LV3"'>
            and lv3_prod_rnd_team_code is not null
        </if>
        <if test='nextGroupLevel =="LV4"'>
            and lv4_prod_rnd_team_code is not null
        </if>
        <if test='nextGroupLevel =="DIMENSION"'>
            and dimension_code is not null
        </if>
        <if test='nextGroupLevel =="SUBCATEGORY"'>
            and dimension_subcategory_code is not null
        </if>
        <if test='nextGroupLevel =="SUB_DETAIL"'>
            and dimension_sub_detail_code is not null
        </if>
        <if test='nextGroupLevel =="SPART"'>
            and spart_code is not null
        </if>
        and group_level = #{nextGroupLevel}
        <if test='parentCustomIdList != null and parentCustomIdList.size() > 0'>
            <foreach collection='parentCustomIdList' item="code" open="AND custom_id IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
    </select>


    <select id="getCombinationParent" resultMap="resultMap">
        with combOrder as (select custom_id,groupCnName,enable_flag,min(levelOrder)as levelOrder  from (
        SELECT distinct custom_id,custom_cn_name AS groupCnName,enable_flag,
        CASE group_level
        WHEN 'LV1' THEN 1
        WHEN 'LV2' THEN 2
        WHEN 'LV3' THEN 3
        WHEN 'LV4' THEN 4
        ELSE 5
        END AS levelOrder
        from
        <if test='costType == "STD"'>
            fin_dm_opt_foi.dm_fcst_ict_std_custom_comb_dim_t
        </if>
        <if test='costType == "PSP"'>
            fin_dm_opt_foi.dm_fcst_ict_psp_custom_comb_dim_t
        </if>
        where del_flag ='N' and use_flag ='PAGE'
        <if test='granularityType != null and granularityType!=""'>
            and granularity_type = #{granularityType}
        </if>
        <if test='softwareMark!=null and softwareMark!=""'>
            and software_mark = #{softwareMark,jdbcType=VARCHAR}
        </if>
        <if test='regionCode!=null and regionCode!=""'>
            and region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode!=null and repofficeCode!=""'>
            and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='bgCode!=null and bgCode!=""'>
            and bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag!=null and overseaFlag!=""'>
            and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag!=null and mainFlag!=""'>
            and main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='ytdFlag!=null and ytdFlag!=""'>
            and ytd_flag = #{ytdFlag,jdbcType=VARCHAR}
        </if>
        <if test='roleId != null and roleId!=""'>
            and role_id = #{roleId}
        </if>
        <if test='userAccount != null and userAccount!=""'>
            and user_id = #{userAccount}
        </if>
        <if test='pageType != null and pageType!=""'>
            and (page_flag = #{pageType} or page_flag = concat('ALL_',#{pageType}::text))
        </if>
        <if test='enableFlag != null and enableFlag!=""'>
            and enable_flag = #{enableFlag}
        </if>
        <if test='keyword != null and keyword!=""'>
            and groupCnName LIKE CONCAT(CONCAT('%', #{keyword}::text ,'%'))
        </if>
        )
        group by custom_id,groupCnName,enable_flag
        )
        select d1.* from (
        SELECT DISTINCT
        custom_id as comb_id,
        custom_cn_name AS groupCnName,enable_flag,
        group_level,
        CASE group_level
        WHEN 'LV1' THEN 1
        WHEN 'LV2' THEN 2
        WHEN 'LV3' THEN 3
        WHEN 'LV4' THEN 4
        END AS levelOrder
        FROM
        <if test='costType == "STD"'>
        fin_dm_opt_foi.dm_fcst_ict_std_custom_comb_dim_t
        </if>
        <if test='costType == "PSP"'>
        fin_dm_opt_foi.dm_fcst_ict_psp_custom_comb_dim_t
        </if>
        WHERE
        del_flag = 'N' and use_flag ='PAGE'
        <if test='granularityType != null and granularityType!=""'>
            and granularity_type = #{granularityType,jdbcType=VARCHAR}
        </if>
        <if test='softwareMark!=null and softwareMark!=""'>
            and software_mark = #{softwareMark,jdbcType=VARCHAR}
        </if>
        <if test='regionCode!=null and regionCode!=""'>
            and region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode!=null and repofficeCode!=""'>
            and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='bgCode!=null and bgCode!=""'>
            and bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag!=null and overseaFlag!=""'>
            and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag!=null and mainFlag!=""'>
            and main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='ytdFlag!=null and ytdFlag!=""'>
            and ytd_flag = #{ytdFlag,jdbcType=VARCHAR}
        </if>
        <if test='roleId != null and roleId!=""'>
            and role_id = #{roleId}
        </if>
        <if test='userAccount != null and userAccount!=""'>
            and user_id = #{userAccount}
        </if>
        <if test='pageType != null and pageType!=""'>
            and (page_flag = #{pageType} or page_flag = concat('ALL_',#{pageType}::text))
        </if>
        <if test='enableFlag != null and enableFlag!=""'>
            and enable_flag = #{enableFlag}
        </if>
        <if test='keyword != null and keyword!=""'>
            and groupCnName LIKE CONCAT(CONCAT('%', #{keyword}::text ,'%'))
        </if>
        ) d1 inner join combOrder d2  on d1.comb_id = d2.custom_id  and d1.groupCnName = d2.groupCnName and d1.levelOrder = d2.levelOrder
        <where>
            <if test='nextGroupLevel != null and nextGroupLevel!=""'>
                d1.group_level = #{nextGroupLevel}
            </if>
            <if test='filterGroupLevel != null and filterGroupLevel!=""'>
                and d1.group_level = #{filterGroupLevel}
            </if>
        </where>

    </select>

</mapper>