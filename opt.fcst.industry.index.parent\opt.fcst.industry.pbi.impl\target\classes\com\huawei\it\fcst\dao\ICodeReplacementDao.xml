<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.pbi.dao.ICodeReplacementDao">

    <sql id="queryConditionYTD">
        <!-->最大版本</-->
        AND version_id = #{VO.versionId}
        <!-->固定过去2年加今年</-->
        AND period_year IN
        <foreach collection='VO.targetPeriod' item="item" open="(" close=")" index="index"
                 separator=",">
            #{item}
        </foreach>
        AND BASE_PERIOD_ID = #{VO.basePeriodId}
        AND REGION_CODE = #{VO.regionCode}
        AND REPOFFICE_CODE = #{VO.repofficeCode}
        AND BG_CODE = #{VO.bgCode}
        AND OVERSEA_FLAG = #{VO.overseaFlag}
        <if test ='VO.relationType != null and VO.relationType != ""'>
            AND RELATION_TYPE = #{VO.relationType}
        </if>
        <if test ='VO.replaceRelationType != null and VO.replaceRelationType != ""'>
            AND REPLACE_RELATION_TYPE = #{VO.replaceRelationType}
        </if>
        <if test ='VO.replaceRelationName != null and VO.replaceRelationName != ""'>
            AND REPLACE_RELATION_NAME = #{VO.replaceRelationName}
        </if>
    </sql>

    <sql id="queryCondition">
        <!-->最大版本</-->
        AND version_id =  #{VO.versionId}
        <!-->固定过去2年加今年</-->
        AND period_year IN
        <foreach collection='VO.targetPeriod' item="item" open="(" close=")" index="index"
                 separator=",">
            #{item}
        </foreach>
        AND BASE_PERIOD_ID = #{VO.basePeriodId}
        AND REGION_CODE = #{VO.regionCode}
        AND REPOFFICE_CODE = #{VO.repofficeCode}
        AND BG_CODE = #{VO.bgCode}
        AND OVERSEA_FLAG = #{VO.overseaFlag}
        <if test ='VO.relationType != null and VO.relationType != ""'>
            AND RELATION_TYPE = #{VO.relationType}
        </if>
        <if test ='VO.replaceRelationType != null and VO.replaceRelationType != ""'>
            AND REPLACE_RELATION_TYPE = #{VO.replaceRelationType}
        </if>
        <if test ='VO.replaceRelationName != null and VO.replaceRelationName != ""'>
            AND REPLACE_RELATION_NAME = #{VO.replaceRelationName}
        </if>
    </sql>

    <sql id="queryTopCondition">
        <!-->最大版本</-->
        AND version_id =  #{VO.versionId}
        <!-->固定过去2年加今年</-->
        AND period_year IN
        <foreach collection='VO.targetPeriod' item="item" open="(" close=")" index="index"
                 separator=",">
            #{item}
        </foreach>
        AND GROUP_LEVEL = 'SPART'
        AND BASE_PERIOD_ID = #{VO.basePeriodId}
        AND REGION_CODE = #{VO.regionCode}
        AND REPOFFICE_CODE = #{VO.repofficeCode}
        AND BG_CODE = #{VO.bgCode}
        AND OVERSEA_FLAG = #{VO.overseaFlag}
        <if test ='VO.relationType != null and VO.relationType != ""'>
            AND RELATION_TYPE = #{VO.relationType}
        </if>
        <if test ='VO.replaceRelationType != null and VO.replaceRelationType != ""'>
            AND REPLACE_RELATION_TYPE = #{VO.replaceRelationType}
        </if>
        <if test ='VO.replaceRelationName != null and VO.replaceRelationName != ""'>
            AND REPLACE_RELATION_NAME = #{VO.replaceRelationName}
        </if>
    </sql>

    <sql id="queryTopAccCondition">
        <!-->最大版本</-->
        AND version_id =  #{VO.versionId}
        <!-->固定过去2年加今年</-->
        AND period_year IN
        <foreach collection='VO.targetPeriod' item="item" open="(" close=")" index="index"
                 separator=",">
            #{item}
        </foreach>
        AND GROUP_LEVEL = 'SPART'
        AND REGION_CODE = #{VO.regionCode}
        AND REPOFFICE_CODE = #{VO.repofficeCode}
        AND BG_CODE = #{VO.bgCode}
        AND OVERSEA_FLAG = #{VO.overseaFlag}
        <if test ='VO.relationType != null and VO.relationType != ""'>
            AND RELATION_TYPE = #{VO.relationType}
        </if>
        <if test ='VO.replaceRelationType != null and VO.replaceRelationType != ""'>
            AND REPLACE_RELATION_TYPE = #{VO.replaceRelationType}
        </if>
        <if test ='VO.replaceRelationName != null and VO.replaceRelationName != ""'>
            AND REPLACE_RELATION_NAME = #{VO.replaceRelationName}
        </if>
    </sql>


    <sql id="queryTopAccConditionYTD">
        <!-->最大版本</-->
        AND version_id =  #{VO.versionId}
        <!-->固定过去2年加今年</-->
        AND period_year IN
        <foreach collection='VO.targetPeriod' item="item" open="(" close=")" index="index"
                 separator=",">
            #{item}
        </foreach>
        AND GROUP_LEVEL = 'SPART'
        AND REGION_CODE = #{VO.regionCode}
        AND REPOFFICE_CODE = #{VO.repofficeCode}
        AND BG_CODE = #{VO.bgCode}
        AND OVERSEA_FLAG = #{VO.overseaFlag}
        <if test ='VO.relationType != null and VO.relationType != ""'>
            AND RELATION_TYPE = #{VO.relationType}
        </if>
        <if test ='VO.replaceRelationType != null and VO.replaceRelationType != ""'>
            AND REPLACE_RELATION_TYPE = #{VO.replaceRelationType}
        </if>
        <if test ='VO.replaceRelationName != null and VO.replaceRelationName != ""'>
            AND REPLACE_RELATION_NAME = #{VO.replaceRelationName}
        </if>
    </sql>

    <sql id="queryBlurCondition">
        <!-->最大版本</-->
        AND version_id =  #{VO.versionId}
        <!-->固定过去2年加今年</-->
        AND period_year IN
        <foreach collection='VO.targetPeriod' item="item" open="(" close=")" index="index"
                 separator=",">
            #{item}
        </foreach>
        AND GROUP_LEVEL_TYPE = 'PBI'
        AND GRANULARITY_TYPE = #{VO.granularityType}
        AND REGION_CODE = #{VO.regionCode}
        AND REPOFFICE_CODE = #{VO.repofficeCode}
        AND BG_CODE = #{VO.bgCode}
        AND OVERSEA_FLAG = #{VO.overseaFlag}
        <if test ='VO.relationType != null and VO.relationType != ""'>
            AND RELATION_TYPE = #{VO.relationType}
        </if>
        <if test ='VO.replaceRelationType != null and VO.replaceRelationType != ""'>
            AND REPLACE_RELATION_TYPE = #{VO.replaceRelationType}
        </if>
        <if test ='VO.replaceRelationName != null and VO.replaceRelationName != ""'>
            AND REPLACE_RELATION_NAME = #{VO.replaceRelationName}
        </if>
    </sql>

    <sql id="queryBlurConditionYTD">
        <!-->最大版本</-->
        AND version_id =  #{VO.versionId}
        <!-->固定过去2年加今年</-->
        AND period_year IN
        <foreach collection='VO.targetPeriod' item="item" open="(" close=")" index="index"
                 separator=",">
            #{item}
        </foreach>
        AND GROUP_LEVEL_TYPE = 'PBI'
        AND GRANULARITY_TYPE = #{VO.granularityType}
        AND REGION_CODE = #{VO.regionCode}
        AND REPOFFICE_CODE = #{VO.repofficeCode}
        AND BG_CODE = #{VO.bgCode}
        AND OVERSEA_FLAG = #{VO.overseaFlag}
        <if test ='VO.relationType != null and VO.relationType != ""'>
            AND RELATION_TYPE = #{VO.relationType}
        </if>
        <if test ='VO.replaceRelationType != null and VO.replaceRelationType != ""'>
            AND REPLACE_RELATION_TYPE = #{VO.replaceRelationType}
        </if>
        <if test ='VO.replaceRelationName != null and VO.replaceRelationName != ""'>
            AND REPLACE_RELATION_NAME = #{VO.replaceRelationName}
        </if>
    </sql>

    <sql id="queryBlurTopCondition">
        <!-->最大版本</-->
        AND version_id =  #{VO.versionId}
        <!-->固定过去2年加今年</-->
        AND period_year IN
        <foreach collection='VO.targetPeriod' item="item" open="(" close=")" index="index"
                 separator=",">
            #{item}
        </foreach>
        AND GRANULARITY_TYPE = #{VO.granularityType}
        AND REGION_CODE = #{VO.regionCode}
        AND REPOFFICE_CODE = #{VO.repofficeCode}
        AND BG_CODE = #{VO.bgCode}
        AND OVERSEA_FLAG = #{VO.overseaFlag}
    </sql>

    <sql id="queryBlurTopAccCondition">
        <!-->最大版本</-->
        AND version_id =  #{VO.versionId}
        <!-->固定过去2年加今年</-->
        AND period_year IN
        <foreach collection='VO.targetPeriod' item="item" open="(" close=")" index="index"
                 separator=",">
            #{item}
        </foreach>
        AND REGION_CODE = #{VO.regionCode}
        AND REPOFFICE_CODE = #{VO.repofficeCode}
        AND BG_CODE = #{VO.bgCode}
        AND OVERSEA_FLAG = #{VO.overseaFlag}
    </sql>

    <sql id="queryBlurTopAccConditionYTD">
        <!-->最大版本</-->
        AND version_id =  #{VO.versionId}
        <!-->固定过去2年加今年</-->
        AND period_year IN
        <foreach collection='VO.targetPeriod' item="item" open="(" close=")" index="index"
                 separator=",">
            #{item}
        </foreach>
        AND REGION_CODE = #{VO.regionCode}
        AND REPOFFICE_CODE = #{VO.repofficeCode}
        AND BG_CODE = #{VO.bgCode}
        AND OVERSEA_FLAG = #{VO.overseaFlag}
    </sql>
    <sql id="queryQtyCondition">
        <!-->最大版本</-->
        AND version_id =  #{VO.versionId}
        <!-->固定过去2年加今年</-->
        AND period_year IN
        <foreach collection='VO.targetPeriod' item="item" open="(" close=")" index="index"
                 separator=",">
            #{item}
        </foreach>
        AND REGION_CODE = #{VO.regionCode}
        AND REPOFFICE_CODE = #{VO.repofficeCode}
        AND BG_CODE = #{VO.bgCode}
        AND OVERSEA_FLAG = #{VO.overseaFlag}
        <if test ='VO.relationType != null and VO.relationType != ""'>
            AND RELATION_TYPE = #{VO.relationType}
        </if>
        <if test ='VO.replaceRelationType != null and VO.replaceRelationType != ""'>
            AND REPLACE_RELATION_TYPE = #{VO.replaceRelationType}
        </if>
        <if test ='VO.replaceRelationName != null and VO.replaceRelationName != ""'>
            AND REPLACE_RELATION_NAME = #{VO.replaceRelationName}
        </if>
    </sql>
    <sql id="queryQtyAccCondition">
        <!-->最大版本</-->
        AND version_id =  #{VO.versionId}
        <!-->固定过去2年加今年</-->
        AND period_year IN
        <foreach collection='VO.targetPeriod' item="item" open="(" close=")" index="index"
                 separator=",">
            #{item}
        </foreach>
        AND REGION_CODE = #{VO.regionCode}
        AND REPOFFICE_CODE = #{VO.repofficeCode}
        AND BG_CODE = #{VO.bgCode}
        AND OVERSEA_FLAG = #{VO.overseaFlag}
        <if test ='VO.relationType != null and VO.relationType != ""'>
            AND RELATION_TYPE = #{VO.relationType}
        </if>
        <if test ='VO.replaceRelationType != null and VO.replaceRelationType != ""'>
            AND REPLACE_RELATION_TYPE = #{VO.replaceRelationType}
        </if>
        <if test ='VO.replaceRelationName != null and VO.replaceRelationName != ""'>
            AND REPLACE_RELATION_NAME = #{VO.replaceRelationName}
        </if>
    </sql>
    <sql id="queryTopQtyCondition">
        <!-->最大版本</-->
        AND version_id =  #{VO.versionId}
        <!-->固定过去2年加今年</-->
        AND period_year IN
        <foreach collection='VO.targetPeriod' item="item" open="(" close=")" index="index"
                 separator=",">
            #{item}
        </foreach>
        AND GROUP_LEVEL = 'TOP-SPART'
        AND REGION_CODE = #{VO.regionCode}
        AND REPOFFICE_CODE = #{VO.repofficeCode}
        AND BG_CODE = #{VO.bgCode}
        AND OVERSEA_FLAG = #{VO.overseaFlag}
        <if test ='VO.relationType != null and VO.relationType != ""'>
            AND RELATION_TYPE = #{VO.relationType}
        </if>
        <if test ='VO.replaceRelationType != null and VO.replaceRelationType != ""'>
            AND REPLACE_RELATION_TYPE = #{VO.replaceRelationType}
        </if>
        <if test ='VO.replaceRelationName != null and VO.replaceRelationName != ""'>
            AND REPLACE_RELATION_NAME = #{VO.replaceRelationName}
        </if>
    </sql>

    <sql id="queryTopAccQtyCondition">
        <!-->最大版本</-->
        AND version_id =  #{VO.versionId}
        <!-->固定过去2年加今年</-->
        AND period_year IN
        <foreach collection='VO.targetPeriod' item="item" open="(" close=")" index="index"
                 separator=",">
            #{item}
        </foreach>
        AND REGION_CODE = #{VO.regionCode}
        AND REPOFFICE_CODE = #{VO.repofficeCode}
        AND BG_CODE = #{VO.bgCode}
        AND OVERSEA_FLAG = #{VO.overseaFlag}
        <if test ='VO.relationType != null and VO.relationType != ""'>
            AND RELATION_TYPE = #{VO.relationType}
        </if>
        <if test ='VO.replaceRelationType != null and VO.replaceRelationType != ""'>
            AND REPLACE_RELATION_TYPE = #{VO.replaceRelationType}
        </if>
        <if test ='VO.replaceRelationName != null and VO.replaceRelationName != ""'>
            AND REPLACE_RELATION_NAME = #{VO.replaceRelationName}
        </if>
    </sql>
    <sql id="queryBlurQtyCondition">
        <!-->最大版本</-->
        AND version_id =  #{VO.versionId}
        <!-->固定过去2年加今年</-->
        AND period_year IN
        <foreach collection='VO.targetPeriod' item="item" open="(" close=")" index="index"
                 separator=",">
            #{item}
        </foreach>
        AND GRANULARITY_TYPE = #{VO.granularityType}
        AND REGION_CODE = #{VO.regionCode}
        AND REPOFFICE_CODE = #{VO.repofficeCode}
        AND BG_CODE = #{VO.bgCode}
        AND OVERSEA_FLAG = #{VO.overseaFlag}
        <if test ='VO.relationType != null and VO.relationType != ""'>
            AND RELATION_TYPE = #{VO.relationType}
        </if>
        <if test ='VO.replaceRelationType != null and VO.replaceRelationType != ""'>
            AND REPLACE_RELATION_TYPE = #{VO.replaceRelationType}
        </if>
        <if test ='VO.replaceRelationName != null and VO.replaceRelationName != ""'>
            AND REPLACE_RELATION_NAME = #{VO.replaceRelationName}
        </if>
    </sql>

    <sql id="queryBlurQtyAccCondition">
        <!-->最大版本</-->
        AND version_id =  #{VO.versionId}
        <!-->固定过去2年加今年</-->
        AND period_year IN
        <foreach collection='VO.targetPeriod' item="item" open="(" close=")" index="index"
                 separator=",">
            #{item}
        </foreach>
        AND GRANULARITY_TYPE = #{VO.granularityType}
        AND REGION_CODE = #{VO.regionCode}
        AND REPOFFICE_CODE = #{VO.repofficeCode}
        AND BG_CODE = #{VO.bgCode}
        AND OVERSEA_FLAG = #{VO.overseaFlag}
        <if test ='VO.relationType != null and VO.relationType != ""'>
            AND RELATION_TYPE = #{VO.relationType}
        </if>
        <if test ='VO.replaceRelationType != null and VO.replaceRelationType != ""'>
            AND REPLACE_RELATION_TYPE = #{VO.replaceRelationType}
        </if>
        <if test ='VO.replaceRelationName != null and VO.replaceRelationName != ""'>
            AND REPLACE_RELATION_NAME = #{VO.replaceRelationName}
        </if>
    </sql>
    <sql id="queryCVCondition">
        <!-->最大版本</-->
        AND t1.version_id =  #{VO.versionId}
        <!-->固定过去2年加今年</-->
        AND t1.period_year IN
        <foreach collection='VO.targetPeriod' item="item" open="(" close=")" index="index"
                 separator=",">
            #{item}
        </foreach>
        AND t1.REGION_CODE = #{VO.regionCode}
        AND t1.REPOFFICE_CODE = #{VO.repofficeCode}
        AND t1.BG_CODE = #{VO.bgCode}
        AND t1.OVERSEA_FLAG = #{VO.overseaFlag}
        <if test ='VO.relationType != null and VO.relationType != ""'>
            AND t1.RELATION_TYPE = #{VO.relationType}
        </if>
        <if test ='VO.replaceRelationType != null and VO.replaceRelationType != ""'>
            AND t1.REPLACE_RELATION_TYPE = #{VO.replaceRelationType}
        </if>
        <if test ='VO.replaceRelationName != null and VO.replaceRelationName != ""'>
            AND t1.REPLACE_RELATION_NAME = #{VO.replaceRelationName}
        </if>
    </sql>
    <sql id="queryCVAccCondition">
        <!-->最大版本</-->
        AND t1.version_id =  #{VO.versionId}
        <!-->固定过去2年加今年</-->
        AND t1.period_year IN
        <foreach collection='VO.targetPeriod' item="item" open="(" close=")" index="index"
                 separator=",">
            #{item}
        </foreach>
        AND t1.REGION_CODE = #{VO.regionCode}
        AND t1.REPOFFICE_CODE = #{VO.repofficeCode}
        AND t1.BG_CODE = #{VO.bgCode}
        AND t1.OVERSEA_FLAG = #{VO.overseaFlag}
        <if test ='VO.relationType != null and VO.relationType != ""'>
            AND t1.RELATION_TYPE = #{VO.relationType}
        </if>
        <if test ='VO.replaceRelationType != null and VO.replaceRelationType != ""'>
            AND t1.REPLACE_RELATION_TYPE = #{VO.replaceRelationType}
        </if>
        <if test ='VO.replaceRelationName != null and VO.replaceRelationName != ""'>
            AND t1.REPLACE_RELATION_NAME = #{VO.replaceRelationName}
        </if>
    </sql>
    <sql id="queryBlurCVAccCondition">
        <!-->最大版本</-->
        AND t1.version_id =  #{VO.versionId}
        <!-->固定过去2年加今年</-->
        AND t1.period_year IN
        <foreach collection='VO.targetPeriod' item="item" open="(" close=")" index="index"
                 separator=",">
            #{item}
        </foreach>
        AND t1.REGION_CODE = #{VO.regionCode}
        AND t1.REPOFFICE_CODE = #{VO.repofficeCode}
        AND t1.BG_CODE = #{VO.bgCode}
        AND t1.OVERSEA_FLAG = #{VO.overseaFlag}
        AND t1.GRANULARITY_TYPE = #{VO.granularityType}
        <if test ='VO.relationType != null and VO.relationType != ""'>
            AND t1.RELATION_TYPE = #{VO.relationType}
        </if>
        <if test ='VO.replaceRelationType != null and VO.replaceRelationType != ""'>
            AND t1.REPLACE_RELATION_TYPE = #{VO.replaceRelationType}
        </if>
        <if test ='VO.replaceRelationName != null and VO.replaceRelationName != ""'>
            AND t1.REPLACE_RELATION_NAME = #{VO.replaceRelationName}
        </if>
    </sql>
    <sql id="queryBlurCVCondition">
        <!-->最大版本</-->
        AND t1.version_id =  #{VO.versionId}
        <!-->固定过去2年加今年</-->
        AND t1.period_year IN
        <foreach collection='VO.targetPeriod' item="item" open="(" close=")" index="index"
                 separator=",">
            #{item}
        </foreach>
        AND t1.REGION_CODE = #{VO.regionCode}
        AND t1.REPOFFICE_CODE = #{VO.repofficeCode}
        AND t1.BG_CODE = #{VO.bgCode}
        AND t1.OVERSEA_FLAG = #{VO.overseaFlag}
        AND t1.GRANULARITY_TYPE = #{VO.granularityType}
        <if test ='VO.relationType != null and VO.relationType != ""'>
            AND t1.RELATION_TYPE = #{VO.relationType}
        </if>
        <if test ='VO.replaceRelationType != null and VO.replaceRelationType != ""'>
            AND t1.REPLACE_RELATION_TYPE = #{VO.replaceRelationType}
        </if>
        <if test ='VO.replaceRelationName != null and VO.replaceRelationName != ""'>
            AND t1.REPLACE_RELATION_NAME = #{VO.replaceRelationName}
        </if>
    </sql>
    <sql id="queryTopCVCondition">
        <!-->最大版本</-->
        AND t1.version_id =  #{VO.versionId}
        <!-->固定过去2年加今年</-->
        AND t1.period_year IN
        <foreach collection='VO.targetPeriod' item="item" open="(" close=")" index="index"
                 separator=",">
            #{item}
        </foreach>
        AND t1.REGION_CODE = #{VO.regionCode}
        AND t1.REPOFFICE_CODE = #{VO.repofficeCode}
        AND t1.BG_CODE = #{VO.bgCode}
        AND t1.OVERSEA_FLAG = #{VO.overseaFlag}
    </sql>
    <sql id="queryBulrTopCondition">
        <!-->最大版本</-->
        AND version_id =  #{VO.versionId}
        <!-->固定过去2年加今年</-->
        AND period_year IN
        <foreach collection='VO.targetPeriod' item="item" open="(" close=")" index="index"
                 separator=",">
            #{item}
        </foreach>
        AND GRANULARITY_TYPE = #{VO.granularityType}
        AND REGION_CODE = #{VO.regionCode}
        AND REPOFFICE_CODE = #{VO.repofficeCode}
        AND BG_CODE = #{VO.bgCode}
        AND OVERSEA_FLAG = #{VO.overseaFlag}
        <if test ='VO.relationType != null and VO.relationType != ""'>
            AND RELATION_TYPE = #{VO.relationType}
        </if>
        <if test ='VO.replaceRelationType != null and VO.replaceRelationType != ""'>
            AND REPLACE_RELATION_TYPE = #{VO.replaceRelationType}
        </if>
        <if test ='VO.replaceRelationName != null and VO.replaceRelationName != ""'>
            AND REPLACE_RELATION_NAME = #{VO.replaceRelationName}
        </if>
    </sql>
    <sql id="queryBulrTopCVCondition">
        <!-->最大版本</-->
        AND t1.version_id =  #{VO.versionId}
        <!-->固定过去2年加今年</-->
        AND t1.period_year IN
        <foreach collection='VO.targetPeriod' item="item" open="(" close=")" index="index"
                 separator=",">
            #{item}
        </foreach>
        AND t1.GRANULARITY_TYPE = #{VO.granularityType}
        AND t1.REGION_CODE = #{VO.regionCode}
        AND t1.REPOFFICE_CODE = #{VO.repofficeCode}
        AND t1.BG_CODE = #{VO.bgCode}
        AND t1.OVERSEA_FLAG = #{VO.overseaFlag}
        <if test ='VO.relationType != null and VO.relationType != ""'>
            AND RELATION_TYPE = #{VO.relationType}
        </if>
        <if test ='VO.replaceRelationType != null and VO.replaceRelationType != ""'>
            AND REPLACE_RELATION_TYPE = #{VO.replaceRelationType}
        </if>
        <if test ='VO.replaceRelationName != null and VO.replaceRelationName != ""'>
            AND REPLACE_RELATION_NAME = #{VO.replaceRelationName}
        </if>
    </sql>
    <select id="getSpartIndexCostList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
            t1.period_year ,t1.period_id periodId,
        ROUND(nvl(COST_INDEX,0), 1) AS costIndex,
            CODE_TYPE codeType
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_IDX_T t1
        WHERE
            DEL_FLAG = 'N'
        <include refid="queryCondition"/>
            AND GROUP_LEVEL = 'SPART'
            AND CODE_TYPE = 'NEW'
            AND GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
        <if test='VO.newProdTeamCode !=""'>
            AND parent_code = #{VO.newProdTeamCode}
        </if>
        UNION ALL
        SELECT
            t2.period_year,t2.period_id periodId,
        ROUND(nvl(COST_INDEX,0), 1) AS costIndex,
            code_type codeType
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_IDX_T t2
        WHERE DEL_FLAG = 'N'
        <include refid="queryCondition"/>
        AND GROUP_LEVEL = 'SPART'
        AND CODE_TYPE = 'OLD'
        AND GROUP_CODE = #{oldSpartCode,jdbcType=VARCHAR}
        <if test='VO.oldProdTeamCode !=""'>
            AND parent_code = #{VO.oldProdTeamCode}
        </if>
        ORDER BY  periodId asc
    </select>
    <select id="getPbiIndexCostList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
            t1.period_year,t1.period_id periodId,
        ROUND(nvl(COST_INDEX,0), 1) AS costIndex,code_type codeType
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_IDX_T t1
        WHERE
        DEL_FLAG = 'N'
        <include refid="queryCondition"/>
        AND GROUP_LEVEL = #{VO.groupLevel,jdbcType=VARCHAR}
        <choose>
            <when test='VO.groupLevel == "LV1"'>
                AND PROD_LIST_CODE = #{VO.lv1ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV2"'>
                AND PROD_LIST_CODE = #{VO.lv2ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV3"'>
                AND PROD_LIST_CODE = #{VO.lv3ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV4"'>
                AND PROD_LIST_CODE = #{VO.lv4ProdTeamCode}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        ORDER BY  periodId asc
    </select>
    <select id="getTopSpartIndexCostList" resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
            period_year,
            period_id periodId,
            ROUND(nvl(COST_INDEX,0), 1) AS costIndex
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_COST_IDX_T
        WHERE
            DEL_FLAG = 'N' and main_Flag ='N'
            <include refid="queryTopCondition"/>
            AND GROUP_CODE = #{spartCode,jdbcType=VARCHAR}
            <if test='lv4ProdTeamCode !=""'>
            AND parent_code = #{lv4ProdTeamCode}
            </if>
        ORDER BY  periodId asc
    </select>
    <select id="getVersionIdsYear" resultType="java.lang.Integer">
        SELECT coalesce(to_char(MAX(PERIOD_YEAR)),to_char(now(),'YYYY'))
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${tablePrefixCostType}_${tablePrefixPbi}_MON_REPL_COST_IDX_T
        WHERE DEL_FLAG = 'N'
    </select>

    <select id="getBlurVersionIdsYear" resultType="java.lang.Integer">
        SELECT coalesce(to_char(MAX(PERIOD_YEAR)),to_char(now(),'YYYY'))
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${tablePrefixCostType}_BASE_CUS_MON_REPL_COST_IDX_T
        WHERE DEL_FLAG = 'N'
        AND GRANULARITY_TYPE = #{granularityType,jdbcType=VARCHAR}
    </select>

    <select id="getBlurSpartIndexCostList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
            period_year,
            period_id periodId,
            ROUND(nvl(COST_INDEX,0), 1)  AS costIndex,
            code_type codeType
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_REPL_COST_IDX_T
        WHERE  DEL_FLAG = 'N'
            <include refid="queryBlurCondition"/>
            AND custom_id =  #{VO.customId}
        ORDER BY  periodId asc
    </select>
    <select id="getSpartIndexQtyList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
            period_year,
            period_id periodId,
            SHIPMENT_QTY shipmentQty,
            CODE_TYPE codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_CV_T
        WHERE
            DEL_FLAG = 'N'
            <include refid="queryQtyCondition"/>
            AND GROUP_LEVEL = 'SPART'
            AND CODE_TYPE = 'NEW'
            AND GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
        UNION ALL
        SELECT
            period_year,
            period_id periodId,
            SHIPMENT_QTY shipmentQty,
            code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_CV_T
        WHERE
            DEL_FLAG = 'N'
            <include refid="queryQtyCondition"/>
            AND GROUP_LEVEL = 'SPART'
            AND CODE_TYPE = 'OLD'
            AND GROUP_CODE = #{oldSpartCode,jdbcType=VARCHAR}
        ORDER BY  periodId asc
    </select>

    <select id="getBlurTopSpartIndexCostList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
            period_year,
            period_id periodId,
            ROUND(cost_index, 2) AS costIndex
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_COST_IDX_T
        WHERE
        DEL_FLAG = 'N' and main_flag ='N'
        <include refid="queryBlurTopCondition"/>
        AND GROUP_CODE = #{spartCode,jdbcType=VARCHAR}
        <if test='customId != ""'>
            AND custom_id = #{customId}
        </if>
        ORDER BY  periodId asc
    </select>
    <select id="getPbiIndexQtyList" resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
            period_year,
            period_id periodId,
            round(SHIPMENT_QTY,0) shipmentQty,
            code_type codeType
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_CV_T t1
        WHERE
            DEL_FLAG = 'N'
            <include refid="queryQtyCondition"/>
            AND GROUP_LEVEL = #{VO.groupLevel,jdbcType=VARCHAR}
            <choose>
                <when test='VO.groupLevel == "LV1"'>
                    AND PROD_LIST_CODE = #{VO.lv1ProdTeamCode}
                </when>
                <when test='VO.groupLevel == "LV2"'>
                    AND PROD_LIST_CODE = #{VO.lv2ProdTeamCode}
                </when>
                <when test='VO.groupLevel == "LV3"'>
                    AND PROD_LIST_CODE = #{VO.lv3ProdTeamCode}
                </when>
                <when test='VO.groupLevel == "LV4"'>
                    AND PROD_LIST_CODE = #{VO.lv4ProdTeamCode}
                </when>
                <otherwise>
                </otherwise>
            </choose>
        ORDER BY  periodId asc
    </select>
    <select id="getBlurSpartIndexQtyList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
            period_year,
            period_id periodId,
            PART_QTY shipmentQty,
            code_type codeType
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_REPL_INFO_IDX_T
        WHERE
            DEL_FLAG = 'N'
            <include refid="queryBlurQtyCondition"/>
            AND GROUP_LEVEL_TYPE ='PBI'
            AND CUSTOM_ID = #{VO.customId,jdbcType=VARCHAR}
        ORDER BY  periodId asc
    </select>


    <select id="getPbiIndexCVList" resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
            t1.period_year,
            t1.period_id periodId,
        <if test='VO.tablePrefixCostType == "PSP"'>
            t1.RMB_COST_AMT rmbCostAmt,
        </if>
        <if test='VO.tablePrefixCostType != "PSP" and VO.groupLevel == "SPART"'>
            GS_DECRYPT(NVL(t1.RMB_COST_AMT,gs_encrypt('',#{keyStr}, 'aes128', 'cbc', 'sha256')),#{keyStr,jdbcType=VARCHAR}, 'AES128', 'CBC', 'SHA256') rmbCostAmt,
        </if>
        <if test='VO.tablePrefixCostType != "PSP" and VO.groupLevel != "SPART"'>
            t1.RMB_COST_AMT rmbCostAmt,
        </if>
            t1.CODE_TYPE CodeType
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_CV_T t1
        WHERE
            t1.DEL_FLAG = 'N'
            <include refid="queryCVCondition"/>
            AND t1.GROUP_LEVEL = #{VO.groupLevel,jdbcType=VARCHAR}
            <choose>
                <when test='VO.groupLevel == "LV1"'>
                    AND t1.PROD_LIST_CODE = #{VO.lv1ProdTeamCode}
                </when>
                <when test='VO.groupLevel == "LV2"'>
                    AND t1.PROD_LIST_CODE = #{VO.lv2ProdTeamCode}
                </when>
                <when test='VO.groupLevel == "LV3"'>
                    AND t1.PROD_LIST_CODE = #{VO.lv3ProdTeamCode}
                </when>
                <when test='VO.groupLevel == "LV4"'>
                    AND t1.PROD_LIST_CODE = #{VO.lv4ProdTeamCode}
                </when>
                <otherwise>
                </otherwise>
            </choose>
        ORDER BY  periodId asc
    </select>

    <select id="getSpartIndexAccCostList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
            period_year,period_id periodId,
            ROUND(cost_index, 2) AS costIndex,
            CODE_TYPE codeType
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_IDX_T
        WHERE
            DEL_FLAG = 'N'
            <include refid="queryConditionYTD"/>
            AND GROUP_LEVEL = 'SPART'
            AND CODE_TYPE = 'NEW'
            AND GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
        <if test='VO.newProdTeamCode !=""'>
            AND parent_code = #{VO.newProdTeamCode}
        </if>
        UNION ALL
        SELECT
            period_year,period_id periodId,
            cost_index costIndex,
            code_type codeType
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_IDX_T
        WHERE DEL_FLAG = 'N'
            <include refid="queryConditionYTD"/>
            AND GROUP_LEVEL = 'SPART'
            AND CODE_TYPE = 'OLD'
            AND GROUP_CODE = #{oldSpartCode,jdbcType=VARCHAR}
        <if test='VO.oldProdTeamCode !=""'>
            AND parent_code = #{VO.oldProdTeamCode}
        </if>
        ORDER BY  periodId asc
    </select>
    <select id="getTopSpartIndexAccCostList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
            period_year,
            period_id periodId,
            ROUND(nvl(COST_INDEX,0), 2) AS costIndex
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_COST_IDX_T
        WHERE
            DEL_FLAG = 'N' and main_flag ='N'
            <include refid="queryTopAccConditionYTD"/>
            AND GROUP_CODE = #{spartCode,jdbcType=VARCHAR}
            AND BASE_PERIOD_ID = #{VO.basePeriodId}
        <if test='lv4ProdTeamCode != ""'>
            AND parent_code = #{lv4ProdTeamCode}
        </if>
        ORDER BY  periodId asc
    </select>
    <select id="getPbiIndexAccCostList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
            period_year,
            period_id periodId,
            ROUND(cost_index, 2) AS costIndex,
            code_type codeType
        FROM
        <!-->代替换月累计表</-->
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_IDX_T
        WHERE
            DEL_FLAG = 'N'
            <include refid="queryConditionYTD"/>
            AND GROUP_LEVEL = #{VO.groupLevel,jdbcType=VARCHAR}
            <choose>
                <when test='VO.groupLevel == "LV1"'>
                    AND PROD_LIST_CODE = #{VO.lv1ProdTeamCode}
                </when>
                <when test='VO.groupLevel == "LV2"'>
                    AND PROD_LIST_CODE = #{VO.lv2ProdTeamCode}
                </when>
                <when test='VO.groupLevel == "LV3"'>
                    AND PROD_LIST_CODE = #{VO.lv3ProdTeamCode}
                </when>
                <when test='VO.groupLevel == "LV4"'>
                    AND PROD_LIST_CODE = #{VO.lv4ProdTeamCode}
                </when>
                <otherwise>
                </otherwise>
            </choose>
        ORDER BY  periodId asc
    </select>
    <select id="getBlurSpartIndexAccCostList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
            period_year,
            period_id periodId,
            ROUND(cost_index, 2) AS costIndex,
            code_type codeType
        FROM
        <!-->代替换月累计表</-->
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_COST_IDX_T
        WHERE  DEL_FLAG = 'N'
            <include refid="queryBlurConditionYTD"/>
            AND CUSTOM_ID = #{VO.customId}
        ORDER BY  periodId asc
    </select>
    <select id="getBlurTopSpartIndexAccCostList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
            period_year,
            period_id periodId,
            ROUND(nvl(cost_index,0), 2) AS costIndex
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_COST_IDX_T
        WHERE
            DEL_FLAG = 'N' and main_flag ='N'
            <include refid="queryBlurTopAccConditionYTD"/>
            AND GROUP_CODE = #{spartCode,jdbcType=VARCHAR}
            AND custom_Id = #{customId}
        ORDER BY  periodId asc
    </select>
    <select id="getBlurPbiIndexAccCostList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
            period_year,
            period_id periodId,
            cost_index costIndex
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_COST_IDX_T
        WHERE  DEL_FLAG = 'N'
            <include refid="queryBlurCondition"/>
            AND GROUP_LEVEL = #{VO.groupLevel,jdbcType=VARCHAR}
            <choose>
                <when test='VO.groupLevel == "LV1"'>
                    AND PBI_DIM_CODE = #{VO.lv1ProdTeamCode}
                </when>
                <when test='VO.groupLevel == "LV2"'>
                    AND PBI_DIM_CODE = #{VO.lv2ProdTeamCode}
                </when>
                <when test='VO.groupLevel == "LV3"'>
                    AND PBI_DIM_CODE = #{VO.lv3ProdTeamCode}
                </when>
                <when test='VO.groupLevel == "LV4"'>
                    AND PBI_DIM_CODE = #{VO.lv4ProdTeamCode}
                </when>
                <otherwise>
                </otherwise>
            </choose>
        ORDER BY  periodId asc
    </select>
    <select id="getSpartIndexAccQtyList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
            period_year,
            period_id periodId,
            SHIPMENT_QTY shipmentQty,
            CODE_TYPE codeType
        FROM
        <!-->代替换月累计表</-->
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_CV_T
        WHERE
            DEL_FLAG = 'N'
            <include refid="queryQtyAccCondition"/>
            AND GROUP_LEVEL = 'SPART'
            AND CODE_TYPE = 'NEW'
            AND GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
        UNION ALL
        SELECT
            period_year,
            period_id periodId,
            SHIPMENT_QTY shipmentQty,
            code_type codeType
        FROM
        <!-->代替换月累计表</-->
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_CV_T
        WHERE
            DEL_FLAG = 'N'
            <include refid="queryQtyAccCondition"/>
            AND GROUP_LEVEL = 'SPART'
            AND CODE_TYPE = 'OLD'
            AND GROUP_CODE = #{oldSpartCode,jdbcType=VARCHAR}
        <if test='VO.oldProdTeamCode !=""'>
            AND parent_code = #{VO.oldProdTeamCode}
        </if>
        ORDER BY  periodId asc
    </select>
    <select id="getTopSpartIndexAccQtyList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
            period_year,
            period_id periodId,
            SHIPMENT_QTY shipmentQty,
            'NEW' codeType
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_CV_T
        WHERE
            DEL_FLAG = 'N'
            <include refid="queryTopAccQtyCondition"/>
            AND GROUP_LEVEL = 'TOP-SPART'
            AND GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
        <if test='VO.newProdTeamCode !=""'>
            AND parent_code = #{VO.newProdTeamCode}
        </if>
        UNION ALL
        SELECT
            period_year,
            period_id periodId,
            SHIPMENT_QTY shipmentQty,
            'OLD' codeType
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_CV_T
        WHERE
            DEL_FLAG = 'N'
            <include refid="queryTopAccQtyCondition"/>
            AND GROUP_LEVEL = 'TOP-SPART'
            AND GROUP_CODE = #{oldSpartCode,jdbcType=VARCHAR}
        <if test='VO.oldProdTeamCode !=""'>
            AND parent_code = #{VO.oldProdTeamCode}
        </if>
        ORDER BY  periodId asc
    </select>
    <select id="getPbiIndexAccQtyList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
            period_year,period_id periodId,
            SHIPMENT_QTY shipmentQty,code_type codeType
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_CV_T
        WHERE
            DEL_FLAG = 'N'
        <include refid="queryQtyAccCondition"/>
            AND GROUP_LEVEL = #{VO.groupLevel,jdbcType=VARCHAR}
            <choose>
                <when test='VO.groupLevel == "LV1"'>
                    AND PROD_LIST_CODE = #{VO.lv1ProdTeamCode}
                </when>
                <when test='VO.groupLevel == "LV2"'>
                    AND PROD_LIST_CODE = #{VO.lv2ProdTeamCode}
                </when>
                <when test='VO.groupLevel == "LV3"'>
                    AND PROD_LIST_CODE = #{VO.lv3ProdTeamCode}
                </when>
                <when test='VO.groupLevel == "LV4"'>
                    AND PROD_LIST_CODE = #{VO.lv4ProdTeamCode}
                </when>
                <otherwise>
                </otherwise>
            </choose>
        ORDER BY  periodId asc
    </select>
    <select id="getBlurSpartIndexAccQtyList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
            t1.period_year,
            t1.period_id periodId,
            t1.PART_QTY shipmentQty,
            t1.code_type codeType
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_INFO_IDX_T t1
        WHERE
            DEL_FLAG = 'N'
            <include refid="queryBlurQtyCondition"/>
            AND GROUP_LEVEL_TYPE ='PBI'
            AND CUSTOM_ID =  #{VO.customId}
        <if test='VO.newProdTeamCode !=""'>
            AND parent_code = #{VO.newProdTeamCode}
        </if>
        ORDER BY  periodId asc
    </select>
    <select id="getBlurTopSpartIndexAccQtyList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
            period_year,period_id periodId,
            SHIPMENT_QTY shipmentQty,
            'NEW' codeType
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_INFO_IDX_T
        WHERE
            DEL_FLAG = 'N'
            <include refid="queryBlurQtyCondition"/>
            AND GROUP_LEVEL = 'SPART'
            AND GROUP_LEVEL_TYPE ='TOP-SPART'
            AND GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
        <if test='VO.newProdTeamCode !=""'>
            AND parent_code = #{VO.newProdTeamCode}
        </if>
        UNION ALL
        SELECT
            period_year,period_id periodId,
            SHIPMENT_QTY shipmentQty,
            'OLD' codeType
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_INFO_IDX_T
        WHERE
            DEL_FLAG = 'N'
            <include refid="queryBlurQtyCondition"/>
            AND GROUP_LEVEL = 'SPART'
            AND GROUP_LEVEL_TYPE ='TOP-SPART'
            AND GROUP_CODE = #{oldSpartCode,jdbcType=VARCHAR}
        <if test='VO.oldProdTeamCode !=""'>
            AND parent_code = #{VO.oldProdTeamCode}
        </if>
        ORDER BY  periodId asc
    </select>
    <select id="getBlurPbiIndexAccQtyList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
            period_year,
            period_id periodId,
            SHIPMENT_QTY shipmentQty,code_type codeType
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_INFO_IDX_T
        WHERE
            DEL_FLAG = 'N'
            <include refid="queryBlurQtyCondition"/>
            AND GROUP_LEVEL =#{groupLevel,jdbcType=VARCHAR}
            AND GROUP_LEVEL_TYPE ='PBI'
            <choose>
                <when test='VO.groupLevel == "LV1"'>
                    AND PBI_DIM_CODE = #{VO.lv1ProdTeamCode}
                </when>
                <when test='VO.groupLevel == "LV2"'>
                    AND PBI_DIM_CODE = #{VO.lv2ProdTeamCode}
                </when>
                <when test='VO.groupLevel == "LV3"'>
                    AND PBI_DIM_CODE = #{VO.lv3ProdTeamCode}
                </when>
                <when test='VO.groupLevel == "LV4"'>
                    AND PBI_DIM_CODE = #{VO.lv4ProdTeamCode}
                </when>
                <otherwise>
                </otherwise>
            </choose>
        ORDER BY  periodId asc
    </select>
    <select id="getSpartIndexAccCVList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
            period_year,period_id periodId,
            COST_CV_AMT costCvAmt,
            ROUND(COST_CV_RATE,2) costCvRatio
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_CV_T t1
        WHERE
            t1.DEL_FLAG = 'N'
            <include refid="queryCVCondition"/>
            AND t1.GROUP_LEVEL = 'SPART'
        AND GROUP_CODE = concat(concat(#{newSpartCode,jdbcType=VARCHAR}::text,',',#{oldSpartCode,jdbcType=VARCHAR}::text))
        ORDER BY  periodId asc
    </select>
    <select id="getTopSpartIndexAccCVList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
            period_year,period_id periodId,
            COST_CV_AMT costCvAmt,
            ROUND(COST_CV_RATE,2) costCvRatio
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_CV_T t1
        WHERE
            t1.DEL_FLAG = 'N'
            <include refid="queryTopCVCondition"/>
            AND t1.GROUP_LEVEL = 'TOP-SPART'
        AND GROUP_CODE = concat(concat(#{newSpartCode,jdbcType=VARCHAR}::text,',',#{oldSpartCode,jdbcType=VARCHAR}::text))
        ORDER BY  periodId asc
    </select>
    <select id="getPbiIndexAccCVList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
            period_year,period_id periodId,
        <if test='VO.tablePrefixCostType == "PSP"'>
            t1.RMB_COST_AMT rmbCostAmt,
        </if>
        <if test='VO.tablePrefixCostType != "PSP" and (VO.groupLevel == "SPART" or VO.groupLevel == "TOP-SPART")'>
            GS_DECRYPT(NVL(t1.RMB_COST_AMT,gs_encrypt('',#{keyStr}, 'aes128', 'cbc', 'sha256')),#{keyStr,jdbcType=VARCHAR}, 'AES128', 'CBC', 'SHA256') rmbCostAmt,
        </if>
        <if test='VO.tablePrefixCostType != "PSP" and VO.groupLevel != "SPART"'>
            t1.RMB_COST_AMT rmbCostAmt,
        </if>
            CODE_TYPE codeType
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_CV_T t1
        WHERE
            t1.DEL_FLAG = 'N'
            <include refid="queryCVAccCondition"/>
            AND t1.GROUP_LEVEL = #{VO.groupLevel,jdbcType=VARCHAR}
            <choose>
                <when test='VO.groupLevel == "LV1"'>
                    AND t1.PROD_LIST_CODE = #{VO.lv1ProdTeamCode}
                </when>
                <when test='VO.groupLevel == "LV2"'>
                    AND t1.PROD_LIST_CODE = #{VO.lv2ProdTeamCode}
                </when>
                <when test='VO.groupLevel == "LV3"'>
                    AND t1.PROD_LIST_CODE = #{VO.lv3ProdTeamCode}
                </when>
                <when test='VO.groupLevel == "LV4"'>
                    AND t1.PROD_LIST_CODE = #{VO.lv4ProdTeamCode}
                </when>
                <otherwise>
                </otherwise>
            </choose>
        ORDER BY  periodId asc
    </select>
    <select id="getBlurTopSpartIndexAccCVList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
            t1.period_year,t1.period_id periodId,
            (t1.RMB_COST_AMT - t2.RMB_COST_AMT) costCvAmt,
            ROUND((t1.RMB_COST_AMT / t2.RMB_COST_AMT),2) costCvRatio
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_INFO_IDX_T t1
        INNER JOIN
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_INFO_IDX_T t2
        ON
            t1.period_year = t2.period_year
            AND t1.period_id = t2.period_id
            AND t1.CODE_TYPE = t2.CODE_TYPE
            AND t1.VERSION_ID =t2.VERSION_ID
            AND t1.BASE_PERIOD_ID = t2.BASE_PERIOD_ID
            AND t1.GROUP_LEVEL = t2.GROUP_LEVEL
            AND t1.REGION_CODE=t2.REGION_CODE
            AND t1.REPOFFICE_CODE =t2.REPOFFICE_CODE
            AND t1.BG_CODE = t2.BG_CODE
            AND t1.OVERSEA_FLAG = t2.OVERSEA_FLAG
            AND t1.DEL_FLAG = t2.DEL_FLAG
            AND t1.GRANULARITY_TYPE = t2.GRANULARITY_TYPE
        WHERE
            t1.DEL_FLAG = 'N'
            <include refid="queryCVCondition"/>
            AND t1.GROUP_LEVEL = 'SPART'
            AND t1.CODE_TYPE = 'TOP-SPART'
            AND t1.GRANULARITY_TYPE = #{VO.granularityType}
            AND t1.GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
            AND t2.GROUP_CODE = #{oldSpartCode,jdbcType=VARCHAR}
        ORDER BY  periodId asc
    </select>
    <select id="getBlurPbiIndexAccCVList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
            t1.period_year,t1.period_id periodId,
            (t1.RMB_COST_AMT - t2.RMB_COST_AMT) costCvAmt,
            ROUND((t1.RMB_COST_AMT / t2.RMB_COST_AMT),2) costCvRatio
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_INFO_IDX_T t1
        INNER JOIN
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_INFO_IDX_T t2
        ON
            t1.period_year = t2.period_year
            AND t1.period_id = t2.period_id
            AND t1.CODE_TYPE = 'NEW'
            AND t2.CODE_TYPE = 'OLD'
            AND t1.VERSION_ID = t2.VERSION_ID
            AND t1.BASE_PERIOD_ID = t2.BASE_PERIOD_ID
            AND t1.PBI_DIM_CODE = t2.PBI_DIM_CODE
            AND t1.GROUP_LEVEL = t2.GROUP_LEVEL
            AND t1.GROUP_CODE = t2.GROUP_CODE
            AND t1.REGION_CODE = t2.REGION_CODE
            AND t1.REPOFFICE_CODE = t2.REPOFFICE_CODE
            AND t1.BG_CODE = t2.BG_CODE
            AND t1.OVERSEA_FLAG = t2.OVERSEA_FLAG
            AND t1.DEL_FLAG = t2.DEL_FLAG
            AND t1.GRANULARITY_TYPE = t2.GRANULARITY_TYPE
        WHERE
            t1.DEL_FLAG = 'N'
            <include refid="queryCVCondition"/>
            AND t1.CODE_TYPE = 'TOP-SPART'
            AND t1.GRANULARITY_TYPE = #{VO.granularityType}
            AND t1.GROUP_LEVEL = #{VO.groupLevel,jdbcType=VARCHAR}
            <choose>
                <when test='VO.groupLevel == "LV1"'>
                    AND t1.PBI_DIM_CODE = #{VO.lv1ProdTeamCode}
                </when>
                <when test='VO.groupLevel == "LV2"'>
                    AND t1.PBI_DIM_CODE = #{VO.lv2ProdTeamCode}
                </when>
                <when test='VO.groupLevel == "LV3"'>
                    AND t1.PBI_DIM_CODE = #{VO.lv3ProdTeamCode}
                </when>
                <when test='VO.groupLevel == "LV4"'>
                    AND t1.PBI_DIM_CODE = #{VO.lv4ProdTeamCode}
                </when>
                <otherwise>
                </otherwise>
            </choose>
        ORDER BY  periodId asc
    </select>
    <select id="getMonthMaxPeriodId" resultType="java.lang.Integer">
        SELECT MAX(PERIOD_ID)
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${tablePrefixCostType}_${tablePrefixPbi}_MON_COST_IDX_T
        WHERE DEL_FLAG = 'N'
    </select>
    <select id="getMaxPeriodId" resultType="java.lang.Integer">
        SELECT MAX(PERIOD_ID)
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${tablePrefixCostType}_${tablePrefixPbi}_MON_REPL_COST_IDX_T
        WHERE DEL_FLAG = 'N'
    </select>
    <select id="getSpartIndexCostExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year ,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN costIndex ELSE NULL END ) as newCostIndex,
        max(CASE WHEN codeType ='OLD' THEN costIndex ELSE NULL END ) as oldCostIndex
        FROM(
        SELECT
        t1.period_year ,t1.period_id periodId,
        ROUND(nvl(COST_INDEX,0), 1) AS costIndex,
        CODE_TYPE codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_IDX_T t1
        WHERE
        DEL_FLAG = 'N'
        <include refid="queryCondition"/>
        AND GROUP_LEVEL = 'SPART'
        AND CODE_TYPE = 'NEW'
        AND GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
        <if test='VO.newProdTeamCode !=""'>
            AND parent_code = #{VO.newProdTeamCode}
        </if>
        UNION ALL
        SELECT
        t2.period_year,t2.period_id periodId,
        ROUND(nvl(COST_INDEX,0),1) AS costIndex,
        code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_IDX_T t2
        WHERE DEL_FLAG = 'N'
        <include refid="queryCondition"/>
        AND GROUP_LEVEL = 'SPART'
        AND CODE_TYPE = 'OLD'
        AND GROUP_CODE = #{oldSpartCode,jdbcType=VARCHAR}
        <if test='VO.oldProdTeamCode !=""'>
            AND parent_code = #{VO.oldProdTeamCode}
        </if>
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>
    <select id="getPbiIndexCostExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year ,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN costIndex ELSE NULL END ) as newCostIndex,
        max(CASE WHEN codeType ='OLD' THEN costIndex ELSE NULL END ) as oldCostIndex
        FROM(
        SELECT
        t1.period_year,t1.period_id periodId,
        ROUND(nvl(COST_INDEX,0), 2) AS costIndex,code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_IDX_T t1
        WHERE
        DEL_FLAG = 'N'
        <include refid="queryCondition"/>
        AND GROUP_LEVEL = #{VO.groupLevel,jdbcType=VARCHAR}
        <choose>
            <when test='VO.groupLevel == "LV1"'>
                AND PROD_LIST_CODE = #{VO.lv1ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV2"'>
                AND PROD_LIST_CODE = #{VO.lv2ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV3"'>
                AND PROD_LIST_CODE = #{VO.lv3ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV4"'>
                AND PROD_LIST_CODE = #{VO.lv4ProdTeamCode}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        ORDER BY  periodId asc
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>
    <select id="getBlurSpartIndexCostExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN costIndex ELSE NULL END ) as newCostIndex,
        max(CASE WHEN codeType ='OLD' THEN costIndex ELSE NULL END ) as oldCostIndex
        FROM(
        SELECT
        period_year,
        period_id periodId,
        ROUND(nvl(COST_INDEX,0), 1) AS costIndex,
        'NEW' codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_REPL_COST_IDX_T
        WHERE  DEL_FLAG = 'N'
        <include refid="queryBlurCondition"/>
        AND code_type = 'NEW'
        AND custom_id =  #{VO.customId}
        UNION ALL
        SELECT
        period_year,
        period_id periodId,
        ROUND(nvl(COST_INDEX,0),1) AS costIndex,
        'OLD' codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_REPL_COST_IDX_T
        WHERE  DEL_FLAG = 'N'
        <include refid="queryBlurCondition"/>
        AND code_type = 'OLD'
        AND custom_id =  #{VO.customId}
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>

    <select id="getSpartIndexAccCostExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year ,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN costIndex ELSE NULL END ) as newCostIndex,
        max(CASE WHEN codeType ='OLD' THEN costIndex ELSE NULL END ) as oldCostIndex
        FROM(
        SELECT
        period_year,period_id periodId,
        ROUND(nvl(COST_INDEX,0), 1) AS costIndex,
        CODE_TYPE codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_IDX_T
        WHERE
        DEL_FLAG = 'N'
        <include refid="queryConditionYTD"/>
        AND GROUP_LEVEL = 'SPART'
        AND CODE_TYPE = 'NEW'
        AND GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
        <if test='VO.newProdTeamCode !=""'>
            AND parent_code = #{VO.newProdTeamCode}
        </if>
        UNION ALL
        SELECT
        period_year,period_id periodId,
        cost_index costIndex,
        code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_IDX_T
        WHERE DEL_FLAG = 'N'
        <include refid="queryConditionYTD"/>
        AND GROUP_LEVEL = 'SPART'
        AND CODE_TYPE = 'OLD'
        AND GROUP_CODE = #{oldSpartCode,jdbcType=VARCHAR}
        <if test='VO.oldProdTeamCode !=""'>
            AND parent_code = #{VO.oldProdTeamCode}
        </if>
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>
    <select id="getTopSpartIndexAccCostExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year ,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN costIndex ELSE NULL END ) as newCostIndex,
        max(CASE WHEN codeType ='OLD' THEN costIndex ELSE NULL END ) as oldCostIndex
        FROM(
        SELECT
        period_year,
        period_id periodId,
        ROUND(nvl(YTD_COST_INDEX,0), 1) costIndex,
        'NEW' codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_MON_COST_RED_IDX_T
        WHERE
        DEL_FLAG = 'N'
        <include refid="queryTopAccCondition"/>
        AND GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
        UNION ALL
        SELECT
        period_year,
        period_id periodId,
        ROUND(nvl(YTD_COST_INDEX,0), 1) costIndex,
        'OLD' codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_MON_COST_RED_IDX_T
        WHERE DEL_FLAG = 'N'
        <include refid="queryTopAccCondition"/>
        AND GROUP_CODE = #{oldSpartCode,jdbcType=VARCHAR}
        ORDER BY  periodId asc
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>
    <select id="getPbiIndexAccCostExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year ,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN costIndex ELSE NULL END ) as newCostIndex,
        max(CASE WHEN codeType ='OLD' THEN costIndex ELSE NULL END ) as oldCostIndex
        FROM(SELECT
        period_year,
        period_id periodId,
        ROUND(nvl(COST_INDEX,0), 1) AS costIndex,
        code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_IDX_T
        WHERE
        DEL_FLAG = 'N'
        <include refid="queryConditionYTD"/>
        AND GROUP_LEVEL = #{VO.groupLevel,jdbcType=VARCHAR}
        <choose>
            <when test='VO.groupLevel == "LV1"'>
                AND PROD_LIST_CODE = #{VO.lv1ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV2"'>
                AND PROD_LIST_CODE = #{VO.lv2ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV3"'>
                AND PROD_LIST_CODE = #{VO.lv3ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV4"'>
                AND PROD_LIST_CODE = #{VO.lv4ProdTeamCode}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>
    <select id="getBlurSpartIndexAccCostExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year ,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN costIndex ELSE NULL END ) as newCostIndex,
        max(CASE WHEN codeType ='OLD' THEN costIndex ELSE NULL END ) as oldCostIndex
        FROM(        SELECT
        period_year,
        period_id periodId,
        ROUND(nvl(COST_INDEX,0), 1) AS costIndex,
        'NEW' codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_COST_IDX_T
        WHERE  DEL_FLAG = 'N'
        <include refid="queryBlurConditionYTD"/>
        AND code_type = 'NEW'
        AND CUSTOM_ID = #{VO.customId}
        UNION ALL
        SELECT
        period_year,period_id periodId,
        ROUND(nvl(COST_INDEX,0), 1) AS costIndex,
        'OLD' codeType
        FROM
        <!-->代替换月累计表</-->
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_COST_IDX_T
        WHERE  DEL_FLAG = 'N'
        <include refid="queryBlurConditionYTD"/>
        AND code_type = 'OLD'
        AND CUSTOM_ID = #{VO.customId}
        ORDER BY  periodId asc
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>
    <select id="getBlurTopSpartIndexAccCostExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year ,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN costIndex ELSE NULL END ) as newCostIndex,
        max(CASE WHEN codeType ='OLD' THEN costIndex ELSE NULL END ) as oldCostIndex
        FROM(SELECT
        period_year,
        period_id periodId,
        ROUND(nvl(ytd_cost_index,0), 1) AS costIndex,
        'NEW' codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_COST_RED_IDX_T
        WHERE
        DEL_FLAG = 'N'
        <include refid="queryBlurTopAccCondition"/>
        AND GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
        <choose>
            <when test='VO.groupLevel == "LV4"'>
                AND parent_code = #{VO.newProdTeamCode}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        period_year,
        period_id periodId,
        ROUND(nvl(ytd_cost_index,0), 1) AS costIndex,
        'OLD' codeType
        FROM
        <!-->代替换月累计表</-->
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_COST_RED_IDX_T
        WHERE DEL_FLAG = 'N'
        <include refid="queryBlurTopAccCondition"/>
        AND GROUP_CODE = #{oldSpartCode,jdbcType=VARCHAR}
        <choose>
            <when test='VO.groupLevel == "LV4"'>
                AND parent_code = #{VO.oldProdTeamCode}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>
    <select id="getBlurPbiIndexAccCostExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year ,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN costIndex ELSE NULL END ) as newCostIndex,
        max(CASE WHEN codeType ='OLD' THEN costIndex ELSE NULL END ) as oldCostIndex
        FROM(SELECT
        period_year,
        period_id periodId,
        ROUND(nvl(ytd_cost_index,0), 1) AS costIndex
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_COST_IDX_T
        WHERE  DEL_FLAG = 'N'
        <include refid="queryBlurCondition"/>
        AND GROUP_LEVEL = #{VO.groupLevel,jdbcType=VARCHAR}
        <choose>
            <when test='VO.groupLevel == "LV1"'>
                AND PBI_DIM_CODE = #{VO.lv1ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV2"'>
                AND PBI_DIM_CODE = #{VO.lv2ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV3"'>
                AND PBI_DIM_CODE = #{VO.lv3ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV4"'>
                AND PBI_DIM_CODE = #{VO.lv4ProdTeamCode}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>
    <select id="getSpartIndexQtyExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year ,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN round(shipmentQty,0) ELSE NULL END ) as newQty,
        max(CASE WHEN codeType ='OLD' THEN round(shipmentQty,0) ELSE NULL END ) as oldQty
        FROM(        SELECT
        period_year,
        period_id periodId,
        SHIPMENT_QTY shipmentQty,
        CODE_TYPE codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_CV_T
        WHERE
        DEL_FLAG = 'N'
        <include refid="queryQtyCondition"/>
        AND CODE_TYPE = 'NEW'
        AND GROUP_LEVEL = 'SPART'
        AND GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
        UNION ALL
        SELECT
        period_year,
        period_id periodId,
        SHIPMENT_QTY shipmentQty,
        code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_CV_T
        WHERE
        DEL_FLAG = 'N'
        <include refid="queryQtyCondition"/>
        AND CODE_TYPE = 'OLD'
        AND GROUP_LEVEL = 'SPART'
        AND GROUP_CODE = #{oldSpartCode,jdbcType=VARCHAR}
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>

    <select id="getPbiIndexQtyExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year ,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN round(shipmentQty,0) ELSE NULL END ) as newQty,
        max(CASE WHEN codeType ='OLD' THEN  round(shipmentQty,0) ELSE NULL END ) as oldQty
        FROM(SELECT
        period_year,
        period_id periodId,
        SHIPMENT_QTY shipmentQty,
        code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_CV_T t1
        WHERE
        DEL_FLAG = 'N'
        AND REGION_CODE = #{VO.regionCode}
        <include refid="queryCVCondition"/>
        AND REPOFFICE_CODE = #{VO.repofficeCode}
        AND BG_CODE = #{VO.bgCode}
        AND OVERSEA_FLAG = #{VO.overseaFlag}
        AND GROUP_LEVEL = #{VO.groupLevel,jdbcType=VARCHAR}
        <choose>
            <when test='VO.groupLevel == "LV1"'>
                AND PROD_LIST_CODE = #{VO.lv1ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV2"'>
                AND PROD_LIST_CODE = #{VO.lv2ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV3"'>
                AND PROD_LIST_CODE = #{VO.lv3ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV4"'>
                AND PROD_LIST_CODE = #{VO.lv4ProdTeamCode}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>
    <select id="getBlurSpartIndexQtyExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year ,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN round(shipmentQty,0) ELSE NULL END ) as newQty,
        max(CASE WHEN codeType ='OLD' THEN round(shipmentQty,0) ELSE NULL END ) as oldQty
        FROM(        SELECT
        period_year,
        period_id periodId,
        PART_QTY shipmentQty,
        code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_REPL_INFO_IDX_T
        WHERE
        DEL_FLAG = 'N'
        <include refid="queryBlurQtyCondition"/>
        AND GROUP_LEVEL_TYPE ='PBI'
        AND code_type = 'NEW'
        AND CUSTOM_ID = #{VO.customId,jdbcType=VARCHAR}
        UNION ALL
        SELECT
        period_year,
        period_id periodId,
        PART_QTY shipmentQty,
        code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_REPL_INFO_IDX_T
        WHERE
        DEL_FLAG = 'N'
        <include refid="queryBlurQtyCondition"/>
        AND GROUP_LEVEL_TYPE ='PBI'
        AND code_type = 'OLD'
        AND CUSTOM_ID = #{VO.customId,jdbcType=VARCHAR}
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>

    <select id="getSpartIndexAccQtyExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year ,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN round(shipmentQty,0) ELSE NULL END ) as newQty,
        max(CASE WHEN codeType ='OLD' THEN round(shipmentQty,0) ELSE NULL END ) as oldQty
        FROM(        SELECT
        period_year,
        period_id periodId,
        SHIPMENT_QTY shipmentQty,
        CODE_TYPE codeType
        FROM
        <!-->代替换月累计表</-->
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_CV_T
        WHERE
        DEL_FLAG = 'N'
        <include refid="queryQtyAccCondition"/>
        AND GROUP_LEVEL = 'SPART'
        AND CODE_TYPE = 'NEW'
        AND GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
        UNION ALL
        SELECT
        period_year,
        period_id periodId,
        SHIPMENT_QTY shipmentQty,
        code_type codeType
        FROM
        <!-->代替换月累计表</-->
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_CV_T
        WHERE
        DEL_FLAG = 'N'
        <include refid="queryQtyAccCondition"/>
        AND GROUP_LEVEL = 'SPART'
        AND CODE_TYPE = 'OLD'
        AND GROUP_CODE = #{oldSpartCode,jdbcType=VARCHAR}
        <if test='VO.oldProdTeamCode !=""'>
            AND parent_code = #{VO.oldProdTeamCode}
        </if>
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>
    <select id="getTopSpartIndexAccQtyExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year ,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN shipmentQty ELSE NULL END ) as newQty,
        max(CASE WHEN codeType ='OLD' THEN shipmentQty ELSE NULL END ) as oldQty
        FROM(SELECT
        period_year,
        period_id periodId,
        SHIPMENT_QTY shipmentQty,
        'NEW' codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_CV_T
        WHERE
        DEL_FLAG = 'N'
        <include refid="queryTopQtyCondition"/>
        AND GROUP_LEVEL = 'TOP-SPART'
        AND GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
        <choose>
            <when test='VO.groupLevel == "LV4"'>
                AND parent_code = #{VO.newProdTeamCode}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        period_year,
        period_id periodId,
        SHIPMENT_QTY shipmentQty,
        'OLD' codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_CV_T
        WHERE
        DEL_FLAG = 'N'
        <include refid="queryTopQtyCondition"/>
        AND GROUP_LEVEL = 'TOP-SPART'
        AND GROUP_CODE = #{oldSpartCode,jdbcType=VARCHAR}
        <choose>
            <when test='VO.groupLevel == "LV4"'>
                AND parent_code = #{VO.oldProdTeamCode}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>
    <select id="getPbiIndexAccQtyExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year ,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN round(shipmentQty,2) ELSE NULL END ) as newQty,
        max(CASE WHEN codeType ='OLD' THEN round(shipmentQty,2) ELSE NULL END ) as oldQty
        FROM(SELECT
        period_year,period_id periodId,
        SHIPMENT_QTY shipmentQty,code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_CV_T
        WHERE
        DEL_FLAG = 'N'
        <include refid="queryQtyAccCondition"/>
        AND GROUP_LEVEL = #{VO.groupLevel,jdbcType=VARCHAR}
        <choose>
            <when test='VO.groupLevel == "LV1"'>
                AND PROD_LIST_CODE = #{VO.lv1ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV2"'>
                AND PROD_LIST_CODE = #{VO.lv2ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV3"'>
                AND PROD_LIST_CODE = #{VO.lv3ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV4"'>
                AND PROD_LIST_CODE = #{VO.lv4ProdTeamCode}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>
    <select id="getBlurSpartIndexAccQtyExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year ,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN round(shipmentQty,0) ELSE NULL END ) as newQty,
        max(CASE WHEN codeType ='OLD' THEN round(shipmentQty,0) ELSE NULL END ) as oldQty
        FROM(        SELECT
        t1.period_year,
        t1.period_id periodId,
        t1.PART_QTY shipmentQty,
        code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_INFO_IDX_T t1
        WHERE
        DEL_FLAG = 'N'
        <include refid="queryBlurQtyCondition"/>
        AND GROUP_LEVEL_TYPE ='PBI'
        AND code_type = 'NEW'
        AND CUSTOM_ID = #{VO.customId}
        <if test='VO.newProdTeamCode !=""'>
            AND parent_code = #{VO.newProdTeamCode}
        </if>
        UNION ALL
        SELECT
        t2.period_year,
        t2.period_id periodId,
        t2.PART_QTY shipmentQty,
        code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_INFO_IDX_T t2
        WHERE
        DEL_FLAG = 'N'
        <include refid="queryBlurQtyCondition"/>
        AND GROUP_LEVEL_TYPE ='PBI'
        AND code_type = 'OLD'
        AND CUSTOM_ID = #{VO.customId}
        <if test='VO.oldProdTeamCode !=""'>
            AND parent_code = #{VO.oldProdTeamCode}
        </if>
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>
    <select id="getBlurTopSpartIndexAccQtyExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year ,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN shipmentQty ELSE NULL END ) as newQty,
        max(CASE WHEN codeType ='OLD' THEN shipmentQty ELSE NULL END ) as oldQty
        FROM(SELECT
        period_year,period_id periodId,
        SHIPMENT_QTY shipmentQty,
        'NEW' codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_INFO_IDX_T
        WHERE
        DEL_FLAG = 'N'
        <include refid="queryBlurQtyCondition"/>
        AND GROUP_LEVEL = 'SPART'
        AND GROUP_LEVEL_TYPE ='TOP-SPART'
        AND GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
        <choose>
            <when test='VO.groupLevel == "LV4"'>
                AND parent_code = #{VO.newProdTeamCode}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        period_year,period_id periodId,
        SHIPMENT_QTY shipmentQty,
        'OLD' codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_INFO_IDX_T
        WHERE
        DEL_FLAG = 'N'
        <include refid="queryBlurQtyCondition"/>
        AND GROUP_LEVEL = 'SPART'
        AND GROUP_LEVEL_TYPE ='TOP-SPART'
        AND GROUP_CODE = #{oldSpartCode,jdbcType=VARCHAR}
        <choose>
            <when test='VO.groupLevel == "LV4"'>
                AND parent_code = #{VO.oldProdTeamCode}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>
    <select id="getBlurPbiIndexAccQtyExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year ,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN shipmentQty ELSE NULL END ) as newQty,
        max(CASE WHEN codeType ='OLD' THEN shipmentQty ELSE NULL END ) as oldQty
        FROM(SELECT
        period_year,
        period_id periodId,
        SHIPMENT_QTY shipmentQty,code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_INFO_IDX_T
        WHERE
        DEL_FLAG = 'N'
        <include refid="queryBlurQtyCondition"/>
        AND GROUP_LEVEL =#{groupLevel,jdbcType=VARCHAR}
        AND GROUP_LEVEL_TYPE ='PBI'
        <choose>
            <when test='VO.groupLevel == "LV1"'>
                AND PBI_DIM_CODE = #{VO.lv1ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV2"'>
                AND PBI_DIM_CODE = #{VO.lv2ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV3"'>
                AND PBI_DIM_CODE = #{VO.lv3ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV4"'>
                AND PBI_DIM_CODE = #{VO.lv4ProdTeamCode}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>

    <select id="getBlurSpartIndexCVExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year ,
        periodId,
        max(CASE WHEN typeName ='MONTH' THEN costCvAmt ELSE NULL END ) as theCVValue,
        max(CASE WHEN typeName ='MONTHACC' THEN costCvAmt ELSE NULL END ) as theAccCVValue,
        max(CASE WHEN typeName ='MONTH' THEN costCvRatio ELSE NULL END ) as theCVRatio,
        max(CASE WHEN typeName ='MONTHACC' THEN costCvRatio ELSE NULL END ) as theAccCVRatio
        FROM(SELECT
        t1.period_year,t1.period_id periodId,
        (t1.RMB_COST_AMT - t2.RMB_COST_AMT) costCvAmt,
        ROUND((t1.RMB_COST_AMT / t2.RMB_COST_AMT),2) costCvRatio,
        'MONTH' typeName
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_REPL_INFO_IDX_T t1
        INNER JOIN
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_REPL_INFO_IDX_T t2
        ON
        t1.period_year = t2.period_year
        AND t1.period_id = t2.period_id
        AND t1.CODE_TYPE = 'NEW'
        AND t2.CODE_TYPE = 'OLD'
        AND t1.VERSION_ID =t2.VERSION_ID
        AND t1.BASE_PERIOD_ID = t2.BASE_PERIOD_ID
        AND t1.PBI_DIM_CODE = t2.PBI_DIM_CODE
        AND t1.GROUP_LEVEL = t2.GROUP_LEVEL
        AND t1.REGION_CODE=t2.REGION_CODE
        AND t1.REPOFFICE_CODE =t2.REPOFFICE_CODE
        AND t1.BG_CODE = t2.BG_CODE
        AND t1.OVERSEA_FLAG = t2.OVERSEA_FLAG
        AND t1.DEL_FLAG = t2.DEL_FLAG
        AND t1.GRANULARITY_TYPE = t2.GRANULARITY_TYPE
        WHERE
        t1.DEL_FLAG = 'N'
        <include refid="queryCVCondition"/>
        AND t1.GROUP_LEVEL = 'SPART'
        AND t1.GRANULARITY_TYPE = #{VO.granularityType}
        AND t1.GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
        AND t2.GROUP_CODE = #{oldSpartCode,jdbcType=VARCHAR}
        UNION ALL
        SELECT
        t1.period_year,t1.period_id periodId,
        (t1.RMB_COST_AMT - t2.RMB_COST_AMT) costCvAmt,
        ROUND((t1.RMB_COST_AMT / t2.RMB_COST_AMT),2) costCvRatio,
        'MONTHACC' typeName
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_INFO_IDX_T t1
        INNER JOIN
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_INFO_IDX_T t2
        ON
        t1.period_year = t2.period_year
        AND t1.period_id = t2.period_id
        AND t1.CODE_TYPE = 'NEW'
        AND t2.CODE_TYPE = 'OLD'
        AND t1.VERSION_ID =t2.VERSION_ID
        AND t1.BASE_PERIOD_ID = t2.BASE_PERIOD_ID
        AND t1.PBI_DIM_CODE = t2.PBI_DIM_CODE
        AND t1.GROUP_LEVEL = t2.GROUP_LEVEL
        AND t1.REGION_CODE=t2.REGION_CODE
        AND t1.REPOFFICE_CODE =t2.REPOFFICE_CODE
        AND t1.BG_CODE = t2.BG_CODE
        AND t1.OVERSEA_FLAG = t2.OVERSEA_FLAG
        AND t1.DEL_FLAG = t2.DEL_FLAG
        AND t1.GRANULARITY_TYPE = t2.GRANULARITY_TYPE
        WHERE
        t1.DEL_FLAG = 'N'
        <include refid="queryCVCondition"/>
        AND t1.GROUP_LEVEL = 'SPART'
        AND t1.GRANULARITY_TYPE = #{VO.granularityType}
        AND t1.GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
        AND t2.GROUP_CODE = #{oldSpartCode,jdbcType=VARCHAR}
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>
    <select id="getBlurTopSpartIndexCVExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year ,
        periodId,
        max(CASE WHEN typeName ='MONTH' THEN costCvAmt ELSE NULL END ) as theCVValue,
        max(CASE WHEN typeName ='MONTHACC' THEN costCvAmt ELSE NULL END ) as theAccCVValue,
        max(CASE WHEN typeName ='MONTH' THEN costCvRatio ELSE NULL END ) as theCVRatio,
        max(CASE WHEN typeName ='MONTHACC' THEN costCvRatio ELSE NULL END ) as theAccCVRatio
        FROM(SELECT
        t1.period_year,t1.period_id periodId,
        (t1.RMB_COST_AMT - t2.RMB_COST_AMT) costCvAmt,
        ROUND((t1.RMB_COST_AMT / t2.RMB_COST_AMT),2) costCvRatio,
        'MONTH' typeName
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_COST_AMT_T t1
        INNER JOIN
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_COST_AMT_T t2
        ON
        t1.period_year = t2.period_year
        AND t1.period_id = t2.period_id
        AND t1.VERSION_ID =t2.VERSION_ID
        AND t1.BASE_PERIOD_ID = t2.BASE_PERIOD_ID
        AND t1.GROUP_LEVEL = t2.GROUP_LEVEL
        AND t1.REGION_CODE=t2.REGION_CODE
        AND t1.REPOFFICE_CODE =t2.REPOFFICE_CODE
        AND t1.BG_CODE = t2.BG_CODE
        AND t1.OVERSEA_FLAG = t2.OVERSEA_FLAG
        AND t1.DEL_FLAG = t2.DEL_FLAG
        AND t1.GRANULARITY_TYPE = t2.GRANULARITY_TYPE
        WHERE
        t1.DEL_FLAG = 'N'
        <include refid="queryBulrTopCVCondition"/>
        AND t1.GROUP_LEVEL = 'SPART'
        AND t1.GRANULARITY_TYPE = #{VO.granularityType}
        AND t1.GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
        AND t2.GROUP_CODE = #{oldSpartCode,jdbcType=VARCHAR}
        UNION　ALL
        SELECT
        t1.period_year,t1.period_id periodId,
        (t1.RMB_COST_AMT - t2.RMB_COST_AMT) costCvAmt,
        ROUND((t1.RMB_COST_AMT / t2.RMB_COST_AMT),2) costCvRatio,
        'MONTHACC' typeName
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_INFO_IDX_T t1
        INNER JOIN
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_INFO_IDX_T t2
        ON
        t1.period_year = t2.period_year
        AND t1.period_id = t2.period_id
        AND t1.CODE_TYPE = t2.CODE_TYPE
        AND t1.VERSION_ID =t2.VERSION_ID
        AND t1.BASE_PERIOD_ID = t2.BASE_PERIOD_ID
        AND t1.GROUP_LEVEL = t2.GROUP_LEVEL
        AND t1.REGION_CODE=t2.REGION_CODE
        AND t1.REPOFFICE_CODE =t2.REPOFFICE_CODE
        AND t1.BG_CODE = t2.BG_CODE
        AND t1.OVERSEA_FLAG = t2.OVERSEA_FLAG
        AND t1.DEL_FLAG = t2.DEL_FLAG
        AND t1.GRANULARITY_TYPE = t2.GRANULARITY_TYPE
        WHERE
        t1.DEL_FLAG = 'N'
        <include refid="queryCVCondition"/>
        AND t1.GROUP_LEVEL = 'SPART'
        AND t1.CODE_TYPE = 'TOP-SPART'
        AND t1.GRANULARITY_TYPE = #{VO.granularityType}
        AND t1.GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
        AND t2.GROUP_CODE = #{oldSpartCode,jdbcType=VARCHAR}
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>
    <select id="getBlurPbiIndexCVExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year ,
        periodId,
        max(CASE WHEN typeName ='MONTH' THEN costCvAmt ELSE NULL END ) as theCVValue,
        max(CASE WHEN typeName ='MONTHACC' THEN costCvAmt ELSE NULL END ) as theAccCVValue,
        max(CASE WHEN typeName ='MONTH' THEN costCvRatio ELSE NULL END ) as theCVRatio,
        max(CASE WHEN typeName ='MONTHACC' THEN costCvRatio ELSE NULL END ) as theAccCVRatio
        FROM(SELECT
        t1.period_year,t1.period_id periodId,
        (t1.RMB_COST_AMT - t2.RMB_COST_AMT) costCvAmt,
        ROUND((t1.RMB_COST_AMT / t2.RMB_COST_AMT),2) costCvRatio,
        'MONTH' typeName
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_REPL_INFO_IDX_T t1
        INNER JOIN
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_REPL_INFO_IDX_T t2
        ON
        t1.period_year = t2.period_year
        AND t1.period_id = t2.period_id
        AND t1.CODE_TYPE = 'NEW'
        AND t2.CODE_TYPE = 'OLD'
        AND t1.VERSION_ID = t2.VERSION_ID
        AND t1.BASE_PERIOD_ID = t2.BASE_PERIOD_ID
        AND t1.GROUP_LEVEL = t2.GROUP_LEVEL
        AND t1.REGION_CODE = t2.REGION_CODE
        AND t1.REPOFFICE_CODE = t2.REPOFFICE_CODE
        AND t1.BG_CODE = t2.BG_CODE
        AND t1.OVERSEA_FLAG = t2.OVERSEA_FLAG
        AND t1.DEL_FLAG = t2.DEL_FLAG
        AND t1.GRANULARITY_TYPE = t2.GRANULARITY_TYPE
        WHERE
        t1.DEL_FLAG = 'N'
        <include refid="queryCVCondition"/>
        AND t1.CODE_TYPE = 'TOP-SPART'
        AND t1.GRANULARITY_TYPE = #{VO.granularityType}
        AND GROUP_LEVEL = #{VO.groupLevel,jdbcType=VARCHAR}
        <choose>
            <when test='VO.groupLevel == "LV1"'>
                AND t1.PBI_DIM_CODE = #{VO.lv1ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV2"'>
                AND t1.PBI_DIM_CODE = #{VO.lv2ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV3"'>
                AND t1.PBI_DIM_CODE = #{VO.lv3ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV4"'>
                AND t1.PBI_DIM_CODE = #{VO.lv4ProdTeamCode}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        UNION ALL
        SELECT
        t1.period_year,t1.period_id periodId,
        (t1.RMB_COST_AMT - t2.RMB_COST_AMT) costCvAmt,
        ROUND((t1.RMB_COST_AMT / t2.RMB_COST_AMT),2) costCvRatio
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_INFO_IDX_T t1
        INNER JOIN
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_INFO_IDX_T t2
        ON
        t1.period_year = t2.period_year
        AND t1.period_id = t2.period_id
        AND t1.CODE_TYPE = 'NEW'
        AND t2.CODE_TYPE = 'OLD'
        AND t1.VERSION_ID = t2.VERSION_ID
        AND t1.BASE_PERIOD_ID = t2.BASE_PERIOD_ID
        AND t1.PBI_DIM_CODE = t2.PBI_DIM_CODE
        AND t1.GROUP_LEVEL = t2.GROUP_LEVEL
        AND t1.GROUP_CODE = t2.GROUP_CODE
        AND t1.REGION_CODE = t2.REGION_CODE
        AND t1.REPOFFICE_CODE = t2.REPOFFICE_CODE
        AND t1.BG_CODE = t2.BG_CODE
        AND t1.OVERSEA_FLAG = t2.OVERSEA_FLAG
        AND t1.DEL_FLAG = t2.DEL_FLAG
        AND t1.GRANULARITY_TYPE = t2.GRANULARITY_TYPE
        WHERE
        t1.DEL_FLAG = 'N'
        <include refid="queryCVCondition"/>
        AND t1.CODE_TYPE = 'TOP-SPART'
        AND t1.GRANULARITY_TYPE = #{VO.granularityType}
        AND GROUP_LEVEL = #{VO.groupLevel,jdbcType=VARCHAR}
        <choose>
            <when test='VO.groupLevel == "LV1"'>
                AND t1.PBI_DIM_CODE = #{VO.lv1ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV2"'>
                AND t1.PBI_DIM_CODE = #{VO.lv2ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV3"'>
                AND t1.PBI_DIM_CODE = #{VO.lv3ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV4"'>
                AND t1.PBI_DIM_CODE = #{VO.lv4ProdTeamCode}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>
    <select id="getSpartsL1Name" resultType="java.lang.String">
        SELECT distinct lv1_cn_name
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_CODE_REPL_INFO_T
        WHERE DEL_FLAG = 'N'
            AND version_id = #{VO.versionId}
            AND old_spart_code = #{oldSpartCode}
            ANd new_spart_code = #{newSpartCode}
    </select>
    <select id="countSpartBasePeriod" resultType="java.lang.Integer">
        SELECT
           COUNT(1)
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_IDX_T t1
        WHERE
            DEL_FLAG = 'N'
            <include refid="queryCondition"/>
            AND GROUP_LEVEL = 'SPART'
            AND CODE_TYPE = 'NEW'
            AND GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
    </select>
    <select id="countBlurStartBasePeriod" resultType="java.lang.Integer">
        SELECT
            COUNT(1)
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_REPL_COST_IDX_T
        WHERE  DEL_FLAG = 'N'
        <include refid="queryBlurCondition"/>
        AND GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
        AND GROUP_LEVEL = 'SPART'
    </select>
    <select id="getMaxVersionId" resultType="java.lang.Long">
        SELECT
            nvl(max(version_id),0)
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_IDX_T
        WHERE
            DEL_FLAG='N'
    </select>
    <select id="getBlurMaxVersionId" resultType="java.lang.Long">
        SELECT
            max(version_id)
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_REPL_COST_IDX_T
        WHERE
            DEL_FLAG='N'  AND GRANULARITY_TYPE = #{VO.granularityType}
    </select>
    <select id="getBlurCombShipQuery"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        period_year,
        period_id periodId,
        round(PART_QTY,0)  shipmentQty
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_REPL_INFO_IDX_T
        WHERE
        DEL_FLAG = 'N'
        <include refid="queryBlurQtyCondition"/>
        AND CUSTOM_ID =  #{customId}
        AND GROUP_CODE =  #{spartCode}
        <choose>
            <when test='lv4ProdTeamCode !=""'>
                AND parent_code = #{lv4ProdTeamCode}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        ORDER BY  periodId asc
    </select>
    <select id="getCombShipQuery"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        period_year,
        period_id periodId,
        round(SHIPMENT_QTY,0)  shipmentQty
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_CV_T
        WHERE
        DEL_FLAG = 'N'
        <include refid="queryTopQtyCondition"/>
        AND GROUP_CODE = #{spartCode}
        <if test='lv4ProdTeamCode != ""'>
            AND parent_code = #{lv4ProdTeamCode}
        </if>
    </select>
    <select id="getBlurAccCombShipQuery"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        period_year,
        period_id periodId,
        round(PART_QTY,0) shipmentQty
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_INFO_IDX_T
        WHERE
        DEL_FLAG = 'N'
        <include refid="queryBlurQtyAccCondition"/>
        AND CUSTOM_ID =  #{customId}
        AND GROUP_CODE =  #{spartCode}
        <if test='lv4ProdTeamCode != ""'>
            AND parent_code = #{lv4ProdTeamCode}
        </if>
        ORDER BY  periodId asc
    </select>
    <select id="getCombAccShipQuery"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        period_year,
        period_id periodId,
        round(SHIPMENT_QTY,0) shipmentQty
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_CV_T
        WHERE
        DEL_FLAG = 'N'
        <include refid="queryTopAccQtyCondition"/>
        AND GROUP_CODE = #{spartCode}
            <if test='lv4ProdTeamCode != ""'>
                AND parent_code = #{lv4ProdTeamCode}
            </if>
    </select>
    <select id="getBlurSpartCVList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
            t1.period_year,
            t1.period_id periodId,
            t1.RMB_COST_AMT rmbCostAmt
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_REPL_INFO_IDX_T t1
        WHERE
            t1.DEL_FLAG = 'N'
            <include refid="queryBlurCVCondition"/>
            AND t1.GRANULARITY_TYPE = #{VO.granularityType}
            AND t1.GROUP_CODE = #{spartCode}
            AND t1.custom_id = #{customId}
            <if test='lv4ProdTeamCode != ""'>
                AND t1.parent_code = #{lv4ProdTeamCode}
            </if>
            ORDER BY  periodId asc
    </select>
    <select id="getSpartCVList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
            period_year,period_id periodId,
        <if test='VO.tablePrefixCostType == "PSP"'>
            t1.RMB_COST_AMT rmbCostAmt
        </if>
        <if test='VO.tablePrefixCostType != "PSP" and (groupType == "SPART" or groupType == "TOP-SPART")'>
            GS_DECRYPT(NVL(t1.RMB_COST_AMT,gs_encrypt('',#{keyStr}, 'aes128', 'cbc', 'sha256')),#{keyStr,jdbcType=VARCHAR}, 'AES128', 'CBC', 'SHA256') rmbCostAmt
        </if>
        <if test='VO.tablePrefixCostType != "PSP" and groupType != "SPART" and groupType != "TOP-SPART"'>
            t1.RMB_COST_AMT rmbCostAmt
        </if>
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_CV_T t1
        WHERE
        t1.DEL_FLAG = 'N'
        <include refid="queryCVCondition"/>
        AND t1.GROUP_LEVEL = #{groupType}
        AND t1.GROUP_CODE = #{spartCode}
        <if test='lv4ProdTeamCode != ""'>
            AND t1.parent_code = #{lv4ProdTeamCode}
        </if>
        ORDER BY  periodId asc
    </select>
    <select id="getBlurSpartCVExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        t1.period_year,
        t1.period_id periodId,
        t1.RMB_COST_AMT rmbCostAmt
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_REPL_INFO_IDX_T t1
        WHERE
        t1.DEL_FLAG = 'N'
        <include refid="queryBlurCVCondition"/>
        AND t1.GRANULARITY_TYPE = #{VO.granularityType}
        AND t1.code_type = #{codeType}
        AND t1.custom_id = #{customId}
        <if test='lv4ProdTeamCode != ""'>
            AND t1.parent_code = #{lv4ProdTeamCode}
        </if>
        ORDER BY  periodId asc
    </select>
    <select id="getBlurSpartCVAccList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
            t1.period_year,t1.period_id periodId,
            t1.RMB_COST_AMT rmbCostAmt
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_INFO_IDX_T t1
        WHERE
            t1.DEL_FLAG = 'N'
            <include refid="queryBlurCVAccCondition"/>
            AND t1.GRANULARITY_TYPE = #{VO.granularityType}
            AND t1.CUSTOM_ID = #{customId}
            <if test='lv4ProdTeamCode != ""'>
                AND t1.parent_code = #{lv4ProdTeamCode}
            </if>
            ORDER BY  periodId asc
    </select>
    <select id="getSpartCVAccList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        period_year,period_id periodId,
        <if test='VO.tablePrefixCostType == "PSP"'>
            t1.RMB_COST_AMT rmbCostAmt
        </if>
        <if test='VO.tablePrefixCostType != "PSP" and (groupType == "SPART" or groupType == "TOP-SPART")'>
            GS_DECRYPT(NVL(t1.RMB_COST_AMT,gs_encrypt('',#{keyStr}, 'aes128', 'cbc', 'sha256')),#{keyStr,jdbcType=VARCHAR}, 'AES128', 'CBC', 'SHA256') rmbCostAmt
        </if>
        <if test='VO.tablePrefixCostType != "PSP" and groupType != "SPART" and groupType != "TOP-SPART"'>
            t1.RMB_COST_AMT rmbCostAmt
        </if>
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_CV_T t1
        WHERE
        t1.DEL_FLAG = 'N'
        <include refid="queryCVAccCondition"/>
        AND t1.GROUP_LEVEL = #{groupType}
        AND t1.GROUP_CODE = #{spartCode}
        <if test='lv4ProdTeamCode != ""'>
            AND t1.parent_code = #{lv4ProdTeamCode}
        </if>
        ORDER BY  periodId asc
    </select>
    <select id="getBlurSpartCVAccExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        t1.period_year,t1.period_id periodId,
        t1.RMB_COST_AMT rmbCostAmt
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_INFO_IDX_T t1
        WHERE
        t1.DEL_FLAG = 'N'
        <include refid="queryBlurCVAccCondition"/>
        AND t1.GRANULARITY_TYPE = #{VO.granularityType}
        AND t1.CUSTOM_ID = #{customId}
        AND t1.CODE_TYPE = #{codeType}
        <if test='lv4ProdTeamCode != ""'>
            AND t1.parent_code = #{lv4ProdTeamCode}
        </if>
        ORDER BY  periodId asc
    </select>
</mapper>
