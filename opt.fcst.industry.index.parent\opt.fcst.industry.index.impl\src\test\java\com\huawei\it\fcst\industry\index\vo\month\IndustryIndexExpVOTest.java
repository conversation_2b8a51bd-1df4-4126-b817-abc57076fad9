/*
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.month;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @since 2023/5/9
 */
public class IndustryIndexExpVOTest extends BaseVOCoverUtilsTest<IndustryIndexExpVO> {
    @Override
    protected Class<IndustryIndexExpVO> getTClass() { return IndustryIndexExpVO.class; }

    @Test
    public void testMethod() {
        IndustryIndexExpVO dmFocActualCostVO = new IndustryIndexExpVO();
        dmFocActualCostVO.setPeriodId(2021L);
        dmFocActualCostVO.getPeriodId();
        dmFocActualCostVO.setCostIndex(12.22);
        dmFocActualCostVO.getCostIndex();
        dmFocActualCostVO.toString();
        dmFocActualCostVO.getCostType();
        dmFocActualCostVO.setCostType("11");
        dmFocActualCostVO.getGroupCnName();
        dmFocActualCostVO.setGroupCnName("22");
        IndustryIndexExpVO.builder().periodId(2021L).costIndex(11D)
            .costType("11").groupCnName("44")
            .build().toString();
        Assert.assertNotNull(dmFocActualCostVO);
    }
}