/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.export.vo;

import com.alibaba.excel.ExcelWriter;

import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.util.PathUtil;

import lombok.Getter;
import lombok.Setter;

import javax.servlet.http.HttpServletResponse;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 导出上下文对象定义
 *
 * @since 202407
 */
@Getter
@Setter
public class ExcelExportContext implements Serializable {
    private static final long serialVersionUID = 1L;

    // 同步导出响应
    private HttpServletResponse response;

    private Boolean isAsync = Boolean.TRUE;

    /**
     * 分页对象
     */
    private PageVO pageVO;

    /**
     * 条件对象
     */
    private Serializable conditionObject;

    /**
     * excelWriter 对象
     */
    private volatile ExcelWriter excelWriter;

    /**
     * sheet信息
     */
    private List<SheetBeanMetaVO> beanMappings;

    /**
     * 模板名称
     */
    private String templateName;

    // 服务器产生的文件
    private String fileStore;

    // 文件大小
    private long fileSize;

    // 文件名称
    private String fileName;

    // 自定义参数
    private Map<String, Object> parameters;

    // 导出数据总条数
    private Long total;

    // s3 key值
    private String fileS3Key;

    // 导出开始时间
    private Date startTime;

    // 导出结束时间
    private Date endTime;

    private String status;

    /**
     * 构造器
     *
     * @param conditionObject 查询条件
     * @param beanManager     导出模板信息
     */
    public ExcelExportContext(Serializable conditionObject, ExcelTemplateBeanManager beanManager) {
        this.conditionObject = conditionObject;
        this.beanMappings = beanManager.getSheetBeans();
        this.templateName = beanManager.getTemplateName();
        this.fileStore = PathUtil.getTempPathByDay("def");
        this.fileName = String.valueOf(System.currentTimeMillis());
        this.startTime = new Date();
    }
}
