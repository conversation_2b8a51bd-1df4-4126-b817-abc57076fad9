/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.service.comb;

import com.huawei.it.fcst.industry.pbi.vo.comb.CommonViewVO;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * IctCustomCombService Class
 *
 * <AUTHOR>
 * @since 2023/8/1
 */
@Path("/custom")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface IIctCustomCombService {

    /**
     * 新增汇总组合
     *
     * @param commonViewVO 参数
     * @return 结果
     */
    @Path("/summary/create")
    @POST
    ResultDataVO createCustom(CommonViewVO commonViewVO) throws CommonApplicationException;

    /**
     * 汇总组合重命名
     *
     * @param commonViewVO 参数
     * @return 结果
     */
    @Path("/summary/rename")
    @POST
    ResultDataVO renameCustom(CommonViewVO commonViewVO) throws CommonApplicationException;

    /**
     * 汇总组合编辑
     *
     * @param commonViewVO 参数
     * @return 结果
     */
    @Path("/summary/update")
    @POST
    ResultDataVO updateCustom(CommonViewVO commonViewVO) throws CommonApplicationException;

    /**
     * 汇总组合删除
     *
     * @param commonViewVO 参数
     * @return 结果
     */
    @Path("/summary/delete")
    @POST
    ResultDataVO deleteCustom(CommonViewVO commonViewVO);

    /**
     * 查询汇总左侧树
     *
     * @param commonViewVO 参数
     * @return 结果
     */
    @Path("/summary/getIctProdRndTeamTree")
    @POST
    ResultDataVO getIctProdRndTeamTree(CommonViewVO commonViewVO) throws CommonApplicationException;

    /**
     * 根据汇总组合名称查询列表
     *
     * @param commonViewVO 参数
     * @return 结果
     */
    @Path("/summary/getCustomCombList")
    @POST
    ResultDataVO getCustomCombList(CommonViewVO commonViewVO);

    /**
     * 汇总组合名称下拉框
     *
     * @param commonViewVO 参数
     * @return 结果
     */
    @Path("/summary/getCustomCombNameList")
    @POST
    ResultDataVO getCustomCombNameList(CommonViewVO commonViewVO);

    /**
     * 层级筛选的下拉框
     *
     * @param commonViewVO 参数
     * @return
     */
    @Path("/summary/groupLevelList")
    @POST
    ResultDataVO getIctGroupLevelList(CommonViewVO commonViewVO);

    /**
     * 汇总组合，初始化部分code失效，整体失效标识
     *
     * @return 结果
     */
    @Path("/summary/initIctEnableFlag")
    @POST
    ResultDataVO initIctEnableFlag();

    @Path("/ictTempTable/create")
    @POST
    ResultDataVO createIctTempTable(CommonViewVO commonViewVO);

    @Path("/ictTempTable/delete")
    @POST
    ResultDataVO deleteIctTempTable(CommonViewVO commonViewVO);

    @Path("/ictTempTable/list")
    @POST
    ResultDataVO getIctTempTableList(CommonViewVO commonViewVO);

    @Path("/ictTempTable/remove")
    @POST
    ResultDataVO removeIctTempTableList(CommonViewVO commonViewVO) throws InterruptedException;


}
