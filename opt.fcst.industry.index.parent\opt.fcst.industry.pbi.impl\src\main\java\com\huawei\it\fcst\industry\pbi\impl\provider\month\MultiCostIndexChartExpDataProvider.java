/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.provider.month;

import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.industry.pbi.dao.IctMonthCostIdxDao;
import com.huawei.it.fcst.industry.pbi.enums.GroupLevelEnum;
import com.huawei.it.fcst.industry.pbi.enums.IndustryConstEnum;
import com.huawei.it.fcst.export.IExcelExportDataProvider;
import com.huawei.it.fcst.industry.pbi.service.month.IIctMonthAnalysisService;
import com.huawei.it.fcst.industry.pbi.utils.FcstIndustryUtil;
import com.huawei.it.fcst.export.vo.ExcelExportContext;
import com.huawei.it.fcst.export.vo.ExportList;
import com.huawei.it.fcst.industry.pbi.vo.month.IctMonthAnalysisVO;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import javax.inject.Inject;
import javax.inject.Named;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 产业成本指数图（多指数）导出数据提供类
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
@Slf4j
@Named("IExcelExport.MultiCostIndexChartExpDataProvider")
public class MultiCostIndexChartExpDataProvider implements IExcelExportDataProvider {

    @Inject
    private IIctMonthAnalysisService monthAnalysisService;

    @Inject
    private IctMonthCostIdxDao monthCostIdxDao;

    @Override
    public List<?> getData(Serializable conditionObject, PageVO pageVO) throws ApplicationException {
        log.info(">>>Begin MultiCostIndexChartExpDataProvider::getData");
        IctMonthAnalysisVO monthAnalysisVO = (IctMonthAnalysisVO) conditionObject;
        IctMonthAnalysisVO paramsVO = new IctMonthAnalysisVO();
        BeanUtils.copyProperties(monthAnalysisVO, paramsVO);
        paramsVO.setExportFlag(true);
        ResultDataVO resultDataVO = monthAnalysisService.getIndustryCostIndexMultiChart(paramsVO);
        List<List<IctMonthAnalysisVO>> dataList = (List<List<IctMonthAnalysisVO>>) resultDataVO.getData();
        dataList = Optional.ofNullable(dataList).orElse(new ArrayList<>());
        ExportList exportList = new ExportList();
        dataList.stream().forEach(list -> {
            list.forEach(dm->dm.setCostIndex(null == dm.getCostIndex() ? null : new BigDecimal(dm.getCostIndex()).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue()));
            if (monthAnalysisVO.getIsContainComb()) {
                list.forEach(dm -> dm.setGroupCnName(dm.getGroupCnName() + "(" + dm.getParentCnName() + "(" + dm.getCustomCnName() + ")" + ")"));
            }
            if (monthAnalysisVO.getIsNeedBlur()) {
                if (CommonConstant.MIN_GROUP_LEVEL.contains(monthAnalysisVO.getGroupLevel())) {
                    list.forEach(dm -> dm.setGroupCnName(dm.getGroupCnName() + "(" + dm.getParentCnName() + ")"));
                }
            }
            exportList.addAll(list);
        });
        exportList.setTotalRows(exportList.size());
        return exportList;
    }

    @Override
    public Map<String, Object> getHeader(Serializable conditionObject, Map<String, Object> parameters) throws ApplicationException {
        ExcelExportContext context = (ExcelExportContext) conditionObject;
        IctMonthAnalysisVO monthAnalysisVO = (IctMonthAnalysisVO) context.getConditionObject();
        // 自定义表头
        Map<String, Object> headMap = new HashMap<>();
        headMap.put("basePeriodId", FcstIndustryUtil.getActualMonth(monthAnalysisVO.getBasePeriodId().toString()));
        headMap.put("displayName", monthAnalysisVO.getDisplayName());
        if (monthAnalysisVO.getIsContainComb()) {
            headMap.put("subCnName", GroupLevelEnum.SPART.getName() + "编码");
        } else {
            headMap.put("subCnName", monthAnalysisVO.getSubCnName());
        }
        headMap.put("costType", monthAnalysisVO.getCostType());
        headMap.put("granularityTypeCnName", monthAnalysisVO.getGranularityTypeCnName());
        headMap.put("overseaFlagCnName", monthAnalysisVO.getOverseaFlagCnName());
        headMap.put("actualMonth", FcstIndustryUtil.getActualMonth(monthCostIdxDao.findActualMonth(monthAnalysisVO)));
        headMap.put("bgCnName", monthAnalysisVO.getBgCnName());
        headMap.put("regionCnName", monthAnalysisVO.getRegionCnName());
        headMap.put("repofficeCnName", monthAnalysisVO.getRepofficeCnName());
        headMap.put("mainFlagCnName", monthAnalysisVO.getMainFlagCnName());
        headMap.put("codeAttributes", monthAnalysisVO.getCodeAttributes());
        headMap.put("softwareMarkStr", "PSP".equals(monthAnalysisVO.getCostType()) ? CommonConstant.SOFTWARE_MARK + IndustryConstEnum.getSoftwareMark(monthAnalysisVO.getSoftwareMark()).getDesc() : "");
        return headMap;
    }
}