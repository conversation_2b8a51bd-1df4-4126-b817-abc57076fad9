/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.annual;

import com.huawei.it.fcst.industry.index.dao.IDmFoiImpExpRecordDao;
import com.huawei.it.fcst.industry.index.vo.common.DmFoiImpExpRecordVO;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;

import com.huawei.it.jalor5.security.UserVO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * PersonalCenterServiceTest Class
 *
 * <AUTHOR>
 * @since 2023/4/13
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class PersonalCenterServiceTest {

    private static final Logger LOGGER = LogManager.getLogger(PersonalCenterServiceTest.class);

    @InjectMocks
    private PersonalCenterService personalCenterService;

    @Mock
    private IDmFoiImpExpRecordDao iDmFoiImpExpRecordDao;

    @Before
    public void before() throws Exception {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);
    }

    @Test
    public void statisticsImportRecord() {
        DmFoiImpExpRecordVO dmFoiImpExpRecordVO= new DmFoiImpExpRecordVO();
        String userId ="test1";
        personalCenterService.statisticsImportRecord(dmFoiImpExpRecordVO, userId);
        Assertions.assertNull(null);
    }

    @Test
    public void statisticsExportRecord() {
        DmFoiImpExpRecordVO dmFoiImpExpRecordVO= new DmFoiImpExpRecordVO();
        personalCenterService.statisticsExportRecord(dmFoiImpExpRecordVO);
        Assertions.assertNull(null);
    }

    @Test
    public void statisticsMonthExportRecord() {
        DmFoiImpExpRecordVO dmFoiImpExpRecordVO= new DmFoiImpExpRecordVO();
        personalCenterService.statisticsMonthExportRecord(dmFoiImpExpRecordVO);
        Assertions.assertNull(null);
    }
}