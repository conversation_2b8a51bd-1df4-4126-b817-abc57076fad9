/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.service.combination;

import com.huawei.it.fcst.industry.index.vo.combination.CombinationVO;
import com.huawei.it.fcst.industry.index.vo.common.CommonViewVO;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * ICustomCombService Class
 *
 * <AUTHOR>
 * @since 2023/8/1
 */
@Path("/combination")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface ICustomCombService {

    /**
     * 新增汇总组合
     *
     * @param combinationVO 参数
     * @return 结果
     */
    @Path("/summary/create")
    @POST
    ResultDataVO createCombination(CombinationVO combinationVO) throws CommonApplicationException;

    /**
     * 汇总组合重命名
     *
     * @param combinationVO 参数
     * @return 结果
     */
    @Path("/summary/rename")
    @POST
    ResultDataVO renameCombination(CombinationVO combinationVO) throws CommonApplicationException;

    /**
     * 汇总组合编辑
     *
     * @param combinationVO 参数
     * @return 结果
     */
    @Path("/summary/update")
    @POST
    ResultDataVO updateCombination(CombinationVO combinationVO) throws CommonApplicationException;

    /**
     * 汇总组合删除
     *
     * @param combinationVO 参数
     * @return 结果
     */
    @Path("/summary/delete")
    @POST
    ResultDataVO deleteCombination(CombinationVO combinationVO);

    /**
     * 根据groupLevel查询汇总组合树
     *
     * @param commonViewVO 参数
     * @return 结果
     */
    @Path("/summary/getProdRndTeamTree")
    @POST
    ResultDataVO getProdRndTeamTree(CommonViewVO commonViewVO);

    /**
     * 根据汇总组合名称查询列表
     *
     * @param commonViewVO 参数
     * @return 结果
     */
    @Path("/summary/getCombinationList")
    @POST
    ResultDataVO getCombinationList(CommonViewVO commonViewVO);

    /**
     * 汇总组合名称下拉框
     *
     * @param combinationVO 参数
     * @return 结果
     */
    @Path("/summary/getCombinationNameList")
    @POST
    ResultDataVO getCombinationNameList(CombinationVO combinationVO);

    /**
     * 层级筛选的下拉框
     *
     * @param combinationVO 参数
     * @return
     */
    @Path("/summary/groupLevelList")
    @POST
    ResultDataVO getGroupLevelList(CombinationVO combinationVO);

    /**
     * 汇总组合，初始化部分code失效，整体失效标识
     *
     * @return 结果
     */
    @Path("/summary/initEnableFlag")
    @POST
    ResultDataVO initEnableFlag();

    @Path("/tempTable/create")
    @POST
    ResultDataVO createTempTable(CombinationVO combinationVO);

    @Path("/tempTable/delete")
    @POST
    ResultDataVO deleteTempTable(CombinationVO combinationVO);

    @Path("/tempTable/list")
    @POST
    ResultDataVO getTempTableList(CombinationVO combinationVO);

    @Path("/tempTable/remove")
    @POST
    ResultDataVO removeTempTableList(CombinationVO combinationVO) throws InterruptedException;

}
