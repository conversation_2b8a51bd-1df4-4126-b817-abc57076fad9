/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.codeReplacement;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * CodeReplacementExpVO
 *
 * @since 2024-07-04
 */

@Setter
@Getter
@EqualsAndHashCode
@NoArgsConstructor
public class CodeReplacementExpVO extends CodeReplacementVO implements Serializable {

    private static final long serialVersionUID = -1576704097209028139L;

    // 指数类型
    private String indexCostType;

    // 发货量类型
    private String shipQtyCostType;

    // BG编码
    private String bgName;

    // L1名称
    private String lv1ProdTeamName;

    // 代表处名称
    private String repofficeName;

    // 地区部名称
    private String regionName;

    // L0名称
    private String lv0ProdTeamName;

    // L2名称
    private String lv2ProdTeamName;

    // L3名称
    private String lv3ProdTeamName;

    // L4名称
    private String lv4ProdTeamName;

    private String oldCostIndex;

    private String newCostIndex;

    private String sameCostIndex;

    private String oldQty;

    private String newQty;

    private String theCVValue;

    private String theAccCVValue;

    private String theCVRatio;

    private String theAccCVRatio;

}
