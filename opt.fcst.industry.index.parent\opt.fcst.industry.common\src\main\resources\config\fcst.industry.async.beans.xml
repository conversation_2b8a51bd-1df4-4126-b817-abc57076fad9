<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:jalor="http://www.huawei.com/it/schema/jalor"
       xmlns:jaxws="http://cxf.apache.org/jaxws"
       xmlns:jaxrs="http://cxf.apache.org/jaxrs"
       xsi:schemaLocation="http://www.springframework.org/schema/beans	http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
	http://www.huawei.com/it/schema/jalor http://www.huawei.com/it/schemas/jalor-beans.xsd
	http://cxf.apache.org/jaxws http://cxf.apache.org/schemas/jaxws.xsd
	http://cxf.apache.org/jaxrs http://cxf.apache.org/schemas/jaxrs.xsd">
    <beans>
        <jalor:conduits messageType="OPT_PBI_EXPORT">
            <value>bigTaskConduit</value>
        </jalor:conduits>
        <jalor:processor messageType="OPT_PBI_EXPORT">
            <ref bean="industryExportMessageProcessor"/>
        </jalor:processor>
        <jalor:conduits messageType="OPT_INDUSTRY_MESSAGE_TASK">
            <value>bigTaskConduit</value>
        </jalor:conduits>
        <jalor:processor messageType="OPT_INDUSTRY_MESSAGE_TASK">
            <ref bean="taskMessageProcessor"/>
        </jalor:processor>
    </beans>
</beans>