/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.drop;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * DmFcstDimInfoVO Class
 *
 * <AUTHOR>
 * @since 2024/7/1
 */
// 此@data注解不可以被替换成getter和setter
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DmFcstDimInfoVO {

    private Long id;

    private Integer num;

    private Integer lv1Num;

    private Integer lv2Num;

    private Integer lv3Num;

    private Integer lv4Num;

    private String bgCode;

    private String bgCnName;

    private String createdBy;

    private Timestamp creationDate;

    private String lastUpdatedBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp lastUpdateDate;

    private String delFlag;

    private String viewFlag;

    private String groupCode;

    private String groupLevel;

    private String groupCnName;

    private String permissionFlag;

    private Long versionId;

    private String dimensionCode;

    private String dimensionCnName;

    private String dimensionSubCategoryCode;

    private String dimensionSubCategoryCnName;

    private String dimensionSubDetailCode;

    private String dimensionSubDetailCnName;

    private String spartCode;

    private String spartCnName;

    private String oldSpartCode;

    private String newSpartCode;

    private String regionCode;

    private String regionCnName;

    private String repofficeCode;

    private String repofficeCnName;

    private String mainFlag;

    private String codeAttributes;

    /**
     *  国内海外标识(N:国内/Y:海外/G:全球)
     */
    private String overseaFlag;

    /**
     *  国内海外标识中文名称
     */
    private String overseaFlagCnName;

    private String pageType;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 角色id
     */
    private String roleId;

    // 使用标识(PAGE页面使用，CALC计算使用)
    private String useFlag;

    private String statusFlag;

    private Boolean stdStatus;

    private String stdStatusFlag;

    private String pspStatusFlag;

    private Boolean pspStatus;

    private String relationType;

    private String replaceRelationType;

    private String replaceRelationName;

    private String lv0ProdRndTeamCode;

    private String softwareMark;

    private String costType;

    private String lv0ProdRdTeamCnName;

    private String lv1ProdRndTeamCode;

    private String lv1ProdRdTeamCnName;

    private String lv2ProdRndTeamCode;

    private String lv2ProdRdTeamCnName;

    private String lv3ProdRndTeamCode;

    private String lv3ProdRdTeamCnName;

    private String lv4ProdRndTeamCode;

    private String lv4ProdRdTeamCnName;

    private String connectParentCode;

    private String pageFlag;

    private String granularityType;

    private String selectFlag;

    // 是否折叠，Y表示折叠，N表示全部展开了
    private String foldFlag;

    private String prodRndTeamCode;

    private String ytdFlag;

    private String lv0Code;

    private String lv0CnName;

    private String lv1Code;

    private String lv1CnName;

    private String lv2Code;

    private String lv2CnName;

    private String lv3Code;

    private String lv3CnName;

    private String lv4Code;

    private String lv4CnName;

    private Long customId;

    private String customCnName;

    private String subEnableFlag;

    private Long combId;

    private String enableFlag;

    private Boolean isCombination;

    private String isSeparate;

    private Long stdCustomId;

    private Long pspCustomId;

    /**
     * 关联出的pbi层级
     */
    private String associatedFlag;

    private String connectCode;

}
