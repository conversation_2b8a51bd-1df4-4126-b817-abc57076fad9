/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.service.config;

import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoVO;
import com.huawei.it.fcst.industry.index.vo.config.FomManufactureDimVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import io.swagger.annotations.Api;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * IConfigReviewService Class
 *
 * <AUTHOR>
 * @since 2023/10/7
 */
@Path("/configReview")
@Api(value = "审视制造量纲维表服务")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface IConfigReviewService {
    /**
     * [制造成本版本信息]
     *
     * @return ResultDataVO
     */
    @Path("/version/dataType")
    @POST
    ResultDataVO findVersion(DmFocVersionInfoVO dmFocVersionInfoVO);

    /**
     * [经营对象、发货对象、制造对象下拉框]
     *
     * @return ResultDataVO
     */
    @Path("/version/manufactureDropDown")
    @POST
    ResultDataVO getManufactureByVersionId(FomManufactureDimVO fomManufactureDimVO) throws CommonApplicationException;

    /**
     * [审视制造量纲维表查询接口]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @Path("/dimensionList")
    @POST
    ResultDataVO dimensionList(FomManufactureDimVO fomManufactureDimVO) throws CommonApplicationException;
}