/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.enums;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 * @since 2023/11/6
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class IndustryConstTest {
    @Test
    public void getOverseaFlag() {
        IndustryConst.OVERSEA_FLAG overseaFlag = IndustryConst.getOverseaFlag("");
        Assert.assertNull(overseaFlag);
    }
}