/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.provider.annual;

import cn.hutool.core.map.MapUtil;
import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.industry.pbi.dao.IAnnualAmpPbiDao;
import com.huawei.it.fcst.industry.pbi.dao.IAnnualCustomDao;
import com.huawei.it.fcst.industry.pbi.impl.annual.AnnualAmpPbiService;
import com.huawei.it.fcst.export.IExcelExportDataProvider;
import com.huawei.it.fcst.industry.pbi.utils.FcstIndustryUtil;
import com.huawei.it.fcst.industry.pbi.vo.annual.AnnualAnalysisVO;
import com.huawei.it.fcst.industry.pbi.vo.annual.DmFocAnnualAmpVO;
import com.huawei.it.fcst.export.vo.ExcelExportContext;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import javax.inject.Inject;
import javax.inject.Named;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * AnnualChildExportDataProvider Class 导出 成本涨跌图多子项
 *
 * <AUTHOR>
 * @since 2024/7/5
 */
@Named("IExcelExport.AnnualChildExportDataProvider")
public class AnnualChildExportDataProvider implements IExcelExportDataProvider {

    @Inject
    private IAnnualAmpPbiDao annualAmpPbiDao;

    @Inject
    private IAnnualCustomDao annualCustomDao;

    @Inject
    private AnnualAmpPbiService annualAmpPbiService;

    @Override
    public List<?> getData(Serializable conditionObject, PageVO pageVO) throws CommonApplicationException {

        AnnualAnalysisVO annualVO = (AnnualAnalysisVO) conditionObject;
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        BeanUtils.copyProperties(annualVO, annualAnalysisVO);
        List<String> threeYearList = new ArrayList<>();
        if (!annualAnalysisVO.getIsMultipleSelect()) {
            List<String> threeYears = annualAnalysisVO.getYearList();
            threeYearList = threeYears.stream().sorted(Comparator.comparing(String::valueOf)).collect(Collectors.toList());
        }
        annualAnalysisVO.setParentCodeList(annualAnalysisVO.getGroupCodeList());
        annualAnalysisVO.setCustomParentCodeList(annualAnalysisVO.getCustomGroupCodeList());
        annualAnalysisVO.setParentLevel(annualAnalysisVO.getGroupLevel());
        String nextGroupLevel = FcstIndustryUtil.getViewNextGroupLevel(annualAnalysisVO);
        annualAnalysisVO.setGroupLevel(nextGroupLevel);
        String combTablePreFix = annualAmpPbiService.getCombTablePreFix(annualAnalysisVO);
        // 虚化的表取值
        annualAnalysisVO.setCombTablePreFix(combTablePreFix);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = new ArrayList<>();

        if (annualAnalysisVO.getIsNeedBlur()) {
            // 走虚化且是最细力度的时候，需要取正常结果表数据
            if (CommonConstant.MIN_GROUP_LEVEL.stream().anyMatch(level -> level.equals(annualAnalysisVO.getParentLevel()))) {
                dmFocAnnualAmpVOResult.addAll(multiChildNotContainsCombMinLevelExcel(annualAnalysisVO));
            } else {
                dmFocAnnualAmpVOResult.addAll(annualCustomDao.multiIndustryCustomCombChart(annualAnalysisVO));
                annualAmpPbiService.setParentCnNameDimensionLevel(dmFocAnnualAmpVOResult);
            }
        }
        if (!annualAnalysisVO.getIsContainComb()) {
            if (!CommonConstant.MIN_GROUP_LEVEL.contains(annualAnalysisVO.getParentLevel()) && CollectionUtils.isNotEmpty(annualAnalysisVO.getParentCodeList())) {
                dmFocAnnualAmpVOResult.addAll(multiChildNotContainsCombExcel(annualAnalysisVO));
            }
        } else {
            // 只选汇总组合
            List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = annualCustomDao.multiCombSpartChart(annualAnalysisVO);
            if (annualAnalysisVO.getIsMultipleSelect()) {
                dmFocAnnualAmpVOList.forEach(dm->dm.setGroupCnName(dm.getGroupCnName()+"("+dm.getParentCnName() +"("+ dm.getCustomCnName() +")"+")"));
            } else {
                dmFocAnnualAmpVOList.forEach(dm->dm.setGroupCnName(dm.getGroupCnName()+"("+dm.getParentCnName() +")"));
            }
            dmFocAnnualAmpVOResult.addAll(dmFocAnnualAmpVOList);
        }
        // 设置无效的涨跌幅提示语
        annualAmpPbiService.setNoEffectiveAmp(dmFocAnnualAmpVOResult, annualAnalysisVO, "excel");
        HashMap<String, List<DmFocAnnualAmpVO>> resultColumnMap = dmFocAnnualAmpVOResult.stream().collect(
                Collectors.groupingBy(item -> getGroupKey(item), HashMap::new, Collectors.toList()));
        Set<String> resultRowSet = resultColumnMap.keySet();
        List<Map> dataList = new ArrayList<>();
        setChildDataList(dataList, threeYearList, resultColumnMap, resultRowSet, annualAnalysisVO);

        return dataList;
    }

    @Override
    public Map<String, Object> getHeader(Serializable conditionObject, Map<String, Object> parameters) throws ApplicationException {
        ExcelExportContext context = (ExcelExportContext) conditionObject;
        AnnualAnalysisVO analysisVO = (AnnualAnalysisVO) context.getConditionObject();

        // 自定义表头
        Map<String, Object> headMap = new HashMap<>();
        headMap.put("displayName", analysisVO.getDisplayName());
        headMap.put("name", analysisVO.getNextGroupName());
        headMap.put("costTypeCnName", analysisVO.getCostTypeCnName());
        headMap.put("granularityTypeCnName", analysisVO.getGranularityTypeCnName());
        headMap.put("softwareMarkCnName", analysisVO.getSoftwareMarkCnName());
        headMap.put("overseaFlagCnName", analysisVO.getOverseaFlagCnName());
        headMap.put("bgCnName", analysisVO.getBgCnName());
        headMap.put("actualMonth", analysisVO.getActualMonth());
        headMap.put("regionCnName", analysisVO.getRegionCnName());
        headMap.put("repofficeCnName", analysisVO.getRepofficeCnName());
        headMap.put("mainFlagCnName", analysisVO.getMainFlagCnName());
        headMap.put("codeAttributesCnName", analysisVO.getCodeAttributesCnName());
        headMap.put("ytdFlagCnName", analysisVO.getYtdFlagCnName());

        List<String> yearList = analysisVO.getYearList();
        if (!analysisVO.getIsMultipleSelect()) {
            List<String> threeYearList = yearList.stream().sorted(Comparator.comparing(String::valueOf)).collect(Collectors.toList());
            for (int i = 0; i < threeYearList.size(); i++) {
                headMap.put("year" + i, threeYearList.get(i));
            }
        } else {
            String year = analysisVO.getPeriodYear() + "年";
            if (analysisVO.getPeriodYear().equals(yearList.get(0))) {
                year = year + "YTD";
            }
            headMap.put("year" + 0, year);
        }
        return headMap;
    }

    private List<DmFocAnnualAmpVO> multiChildNotContainsCombExcel(AnnualAnalysisVO annualAnalysisVO) {
        // 设置重量级团队
        annualAmpPbiService.resetProdRndTeamCode(annualAnalysisVO);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = annualAmpPbiDao.multiIndustryPbiCostChart(annualAnalysisVO);

        annualAmpPbiService.setGroupCnNameDimensionLevel(dmFocAnnualAmpVOResult);
        return dmFocAnnualAmpVOResult;
    }

    private List<DmFocAnnualAmpVO> multiChildNotContainsCombMinLevelExcel(AnnualAnalysisVO annualAnalysisVO) {
        // 设置重量级团队
        annualAmpPbiService.resetProdRndTeamCode(annualAnalysisVO);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = annualAmpPbiDao.multiIndustryMinLevelChart(annualAnalysisVO);

        annualAmpPbiService.setGroupCnNameDimensionLevel(dmFocAnnualAmpVOResult);
        return dmFocAnnualAmpVOResult;
    }

    private String getGroupKey(DmFocAnnualAmpVO dmFocAnnualAmpVO) {
        return dmFocAnnualAmpVO.getProdRndTeamCode() + "_" + dmFocAnnualAmpVO.getProdRndTeamCnName() + "_" + dmFocAnnualAmpVO.getParentCode() + "_" + dmFocAnnualAmpVO.getParentCnName() + "_" + dmFocAnnualAmpVO.getGroupCnName() + "_" + dmFocAnnualAmpVO.getGroupCode();
    }

    private void setChildDataList(List<Map> dataList, List<String> threeYears, HashMap<String, List<DmFocAnnualAmpVO>> resultColumnMap, Set<String> resultRowSet, AnnualAnalysisVO annualAnalysisVO) {
        for (String keyStr : resultRowSet) {
            List<DmFocAnnualAmpVO> resultAnnualAmpVO = MapUtil.get(resultColumnMap, keyStr, List.class);
            resultAnnualAmpVO.stream().sorted(Comparator.comparing(DmFocAnnualAmpVO::getPeriodYear));
            Map<String, Object> resultMap = new HashMap<>();
            for (int i = 0; i < resultAnnualAmpVO.size(); i++) {
                String parentCnName = resultAnnualAmpVO.get(i).getParentCnName();
                String prodRndTeamCnName = resultAnnualAmpVO.get(i).getProdRndTeamCnName();
                String groupCode = resultAnnualAmpVO.get(i).getGroupCode();
                String groupCnName = resultAnnualAmpVO.get(i).getGroupCnName();
                String annualAmpVO = resultAnnualAmpVO.get(i).getAnnualAmp();
                String periodYear = resultAnnualAmpVO.get(i).getPeriodYear();
                if (annualAnalysisVO.getIsNeedBlur() && CommonConstant.MIN_GROUP_LEVEL.stream().anyMatch(level -> level.equals(annualAnalysisVO.getParentLevel()))) {
                    resultMap.put("parentCnName", prodRndTeamCnName);
                } else {
                    resultMap.put("parentCnName", parentCnName);
                }
                resultMap.put("groupCnName", groupCnName);
                if (StringUtils.isNotBlank(groupCode)) {
                    resultMap.put("groupCode", groupCode);
                }
                if (!annualAnalysisVO.getIsMultipleSelect()) {
                    for (int j = 0; j < threeYears.size(); j++) {
                        if (threeYears.get(j).equals(periodYear)) {
                            resultMap.put("annualAmp" + j, annualAmpVO != null ? annualAmpVO : null);
                        }
                    }
                } else {
                    resultMap.put("annualAmp" + 0, annualAmpVO != null ? annualAmpVO : null);
                }
            }
            dataList.add(resultMap);
        }
    }

}
