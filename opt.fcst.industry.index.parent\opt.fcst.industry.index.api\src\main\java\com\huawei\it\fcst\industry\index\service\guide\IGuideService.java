/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.service.guide;

import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.guide.GuideParamVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * IGuideService Class
 *
 * <AUTHOR>
 * @since 2024/2/29
 */
@Path("operation")
@Consumes(MediaType.APPLICATION_JSON + ";charset=UTF-8")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface IGuideService {

    /**
     * 操作指南查询
     * @param guideParamVO guideParamVO
     * @return 操作指南查询
     */
    @Path("/guideList")
    @POST
    ResultDataVO findGuideInfoList(GuideParamVO guideParamVO);

    /**
     * 新增操作指南
     * @param guideParamVO guideParamVO
     * @return 新增操作指南
     */
    @Path("/saveGuideInfo")
    @POST
    ResultDataVO saveGuideInfo(GuideParamVO guideParamVO) throws CommonApplicationException;

    /**
     * 新增操作指南
     * @param guideParamVO guideParamVO
     * @return 新增操作指南
     */
    @Path("/deleteGuide")
    @POST
    ResultDataVO deleteGuide(GuideParamVO guideParamVO);

}
