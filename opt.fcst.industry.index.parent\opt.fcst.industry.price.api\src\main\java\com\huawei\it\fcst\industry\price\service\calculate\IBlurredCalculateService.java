/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.service.calculate;

import com.huawei.it.fcst.industry.price.vo.common.CommonPriceBaseVO;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import io.swagger.annotations.Api;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * <AUTHOR>
 * @since 2024/11/6
 */
@Path("/blurred")
@Api(value = "定价指数-虚化逻辑")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface IBlurredCalculateService {
    /**
     * 年度月度各层级下拉框(虚化计算)
     *
     * @param commonPriceBaseVO 参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/summaryCalculate")
    ResultDataVO dropDownSummaryCalculate(CommonPriceBaseVO commonPriceBaseVO) throws CommonApplicationException;
}
