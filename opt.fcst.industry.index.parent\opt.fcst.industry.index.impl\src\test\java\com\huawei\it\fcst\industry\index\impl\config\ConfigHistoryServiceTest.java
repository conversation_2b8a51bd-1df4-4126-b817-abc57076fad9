/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.config;

import com.huawei.it.fcst.industry.index.dao.IDmFocDataRefreshStatusDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTopCateInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTopItemInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocVersionDao;
import com.huawei.it.fcst.industry.index.impl.annual.StatisticsExcelService;
import com.huawei.it.fcst.industry.index.impl.combination.AsyncService;
import com.huawei.it.fcst.industry.index.impl.common.CommonService;
import com.huawei.it.fcst.industry.index.utils.ExcelUtil;
import com.huawei.it.fcst.industry.index.utils.TestUtils;
import com.huawei.it.fcst.industry.index.utils.UserInfoUtils;
import com.huawei.it.fcst.industry.index.vo.combination.DmFocDataRefreshStatus;
import com.huawei.it.fcst.industry.index.vo.common.DmFoiImpExpRecordVO;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocTopCateInfoDTO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocTopItemInfoDTO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoDTO;
import com.huawei.it.fcst.industry.index.vo.config.HistoryInputVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.security.RoleVO;
import com.huawei.it.jalor5.security.UserVO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.inject.Inject;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.powermock.api.mockito.PowerMockito.mockStatic;

/**
 * ConfigHistoryServiceTest Class
 *
 * <AUTHOR>
 * @since 2023/4/3
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest( {UserInfoUtils.class})
public class ConfigHistoryServiceTest {
    private static final Logger LOGGER = LoggerFactory.getLogger(ConfigHistoryService.class);

    @InjectMocks
    private ConfigHistoryService configHistoryService;

    @Mock
    private IDmFocVersionDao dmFocVersionDao;

    @Mock
    private IDmFocTopCateInfoDao dmFocTopCateInfoDao;

    @Mock
    private IDmFocTopItemInfoDao dmFocTopItemInfoDao;

    @Mock
    private IDmFocDataRefreshStatusDao dataRefreshStatusDao;

    @Mock
    private AsyncService asyncService;

    @Mock
    private ConfigExportService configExportService;

    @Before
    public void before() throws Exception {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);

    }

    @Test
    public void allCategoryList() throws Exception {
        // 入参
        HistoryInputVO historyInputVO = new HistoryInputVO();
        historyInputVO.setVersionId(10L);
        historyInputVO.setPageIndex(10);
        historyInputVO.setPageSize(10);
        historyInputVO.setIndustryOrg("ICT");
        PagedResult<DmFocTopCateInfoDTO> cateByPage = new PagedResult<>();
        DmFocTopCateInfoDTO cateInfoDTO = new DmFocTopCateInfoDTO();
        cateInfoDTO.setTopFlag("Y");
        cateInfoDTO.setTopCategoryCode("1132D");
        cateByPage.setResult(Arrays.asList(cateInfoDTO));
        DmFocVersionInfoDTO versionVO = new DmFocVersionInfoDTO();
        versionVO.setVersion("-");
        List<DmFocTopCateInfoDTO> dmFocTopCateInfoDTOS = new ArrayList<>();
        dmFocTopCateInfoDTOS.add(cateInfoDTO);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionVO);
        when(dmFocTopCateInfoDao.findCateByPage(any(), any())).thenReturn(cateByPage);

        // 断言
        ResultDataVO listByVersionId = configHistoryService.allCategoryList(historyInputVO);
        Assert.assertNotNull(listByVersionId);
    }

    @Test
    public void allCategoryList2Test() throws Exception {
        // 入参
        HistoryInputVO historyInputVO = new HistoryInputVO();
        historyInputVO.setVersionId(10L);
        historyInputVO.setPageIndex(10);
        historyInputVO.setPageSize(10);
        historyInputVO.setIndustryOrg("ICT");

        // 期望值
        PagedResult<DmFocTopCateInfoDTO> cateByPage = new PagedResult<>();
        DmFocTopCateInfoDTO cateInfoDTO = new DmFocTopCateInfoDTO();
        cateInfoDTO.setTopFlag("Y");
        cateInfoDTO.setTopCategoryCode("1132D");
        cateByPage.setResult(Arrays.asList(cateInfoDTO));
        DmFocVersionInfoDTO versionVO = new DmFocVersionInfoDTO();
        versionVO.setVersion("20230405");
        List<DmFocTopCateInfoDTO> dmFocTopCateInfoDTOS = new ArrayList<>();
        DmFocTopCateInfoDTO cateInfoDTO2 = new DmFocTopCateInfoDTO();
        cateInfoDTO.setTopCategoryCode("1132C");
        dmFocTopCateInfoDTOS.add(cateInfoDTO2);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionVO);
        when(dmFocTopCateInfoDao.findCateByPage(any(), any())).thenReturn(cateByPage);

        // 断言
        ResultDataVO listByVersionId = configHistoryService.allCategoryList(historyInputVO);
        Assert.assertNotNull(listByVersionId);
    }

    @Test
    public void allCategoryList3Test() throws Exception {
        // 入参
        HistoryInputVO historyInputVO = new HistoryInputVO();

        historyInputVO.setPageIndex(10);
        historyInputVO.setPageSize(10);
        ResultDataVO listByVersionId = new ResultDataVO();
        try {
            listByVersionId = configHistoryService.allCategoryList(historyInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(listByVersionId);
    }

    @Test
    public void allCategoryList4Test() throws Exception {
        // 入参
        HistoryInputVO historyInputVO = new HistoryInputVO();
        historyInputVO.setVersionId(10L);
        historyInputVO.setPageIndex(10);
        historyInputVO.setPageSize(10);
        ResultDataVO listByVersionId = new ResultDataVO();
        DmFocVersionInfoDTO versionVO = new DmFocVersionInfoDTO();
        versionVO.setVersionId(60L);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionVO);

        try {
            listByVersionId = configHistoryService.allCategoryList(historyInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(listByVersionId);
    }

    @Test
    public void topItemList() {
        HistoryInputVO historyInputVO = new HistoryInputVO();
        ResultDataVO listByVersionId = new ResultDataVO();
        try {
            listByVersionId = configHistoryService.topItemList(historyInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(listByVersionId);
    }

    @Test
    public void topItemList2Test() {
        HistoryInputVO historyInputVO = new HistoryInputVO();
        historyInputVO.setVersionId(10L);
        historyInputVO.setPageIndex(10);
        historyInputVO.setPageSize(10);
        ResultDataVO listByVersionId = new ResultDataVO();
        DmFocVersionInfoDTO versionVO = new DmFocVersionInfoDTO();
        versionVO.setVersionId(60L);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionVO);

        try {
            listByVersionId = configHistoryService.topItemList(historyInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(listByVersionId);
    }

    @Test
    public void topItemList3Test() throws CommonApplicationException {
        HistoryInputVO historyInputVO = new HistoryInputVO();
        historyInputVO.setVersionId(10L);
        historyInputVO.setPageIndex(10);
        historyInputVO.setPageSize(10);
        DmFocVersionInfoDTO versionVO = new DmFocVersionInfoDTO();
        versionVO.setVersionId(60L);
        versionVO.setVersion("20230405");
        PagedResult<DmFocTopItemInfoDTO> itemByPage = new PagedResult<>();
        DmFocTopItemInfoDTO itemInfoDTO = new DmFocTopItemInfoDTO();
        itemInfoDTO.setTopItemCode("2145A");
        itemByPage.setResult(Arrays.asList(itemInfoDTO));
        List<DmFocTopItemInfoDTO> dmFocTopItemInfoDTOS = new ArrayList<>();
        dmFocTopItemInfoDTOS.add(itemInfoDTO);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionVO);
        when(dmFocTopItemInfoDao.findItemByPage(any(), any())).thenReturn(itemByPage);
        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionVO);

        ResultDataVO listByVersionId = configHistoryService.topItemList(historyInputVO);
        Assert.assertNotNull(listByVersionId);
    }

    @Test
    public void topItemList4Test() throws CommonApplicationException {
        HistoryInputVO historyInputVO = new HistoryInputVO();
        historyInputVO.setVersionId(10L);
        historyInputVO.setPageIndex(10);
        historyInputVO.setPageSize(10);
        DmFocVersionInfoDTO versionVO = new DmFocVersionInfoDTO();
        versionVO.setVersionId(60L);
        versionVO.setVersion("20230405");
        PagedResult<DmFocTopItemInfoDTO> itemByPage = new PagedResult<>();
        DmFocTopItemInfoDTO itemInfoDTO = new DmFocTopItemInfoDTO();
        itemInfoDTO.setTopItemCode("2145A");
        itemByPage.setResult(Arrays.asList(itemInfoDTO));
        List<DmFocTopItemInfoDTO> dmFocTopItemInfoDTOS = new ArrayList<>();
        DmFocTopItemInfoDTO itemInfoDTO2 = new DmFocTopItemInfoDTO();
        itemInfoDTO2.setTopItemCode("2145B");
        dmFocTopItemInfoDTOS.add(itemInfoDTO2);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionVO);
        when(dmFocTopItemInfoDao.findItemByPage(any(), any())).thenReturn(itemByPage);
        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionVO);

        ResultDataVO listByVersionId = configHistoryService.topItemList(historyInputVO);
        Assert.assertNotNull(listByVersionId);
    }

    @Test
    public void topItemList5Test() throws CommonApplicationException {
        HistoryInputVO historyInputVO = new HistoryInputVO();
        historyInputVO.setVersionId(10L);
        historyInputVO.setPageIndex(10);
        historyInputVO.setPageSize(10);
        DmFocVersionInfoDTO versionVO = new DmFocVersionInfoDTO();
        versionVO.setVersionId(60L);
        versionVO.setVersion("-");
        PagedResult<DmFocTopItemInfoDTO> itemByPage = new PagedResult<>();
        DmFocTopItemInfoDTO itemInfoDTO = new DmFocTopItemInfoDTO();
        itemInfoDTO.setTopItemCode("2145A");
        itemByPage.setResult(Arrays.asList(itemInfoDTO));
        List<DmFocTopItemInfoDTO> dmFocTopItemInfoDTOS = new ArrayList<>();
        DmFocTopItemInfoDTO itemInfoDTO2 = new DmFocTopItemInfoDTO();
        itemInfoDTO2.setTopItemCode("2145B");
        dmFocTopItemInfoDTOS.add(itemInfoDTO2);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionVO);
        when(dmFocTopItemInfoDao.findItemByPage(any(), any())).thenReturn(itemByPage);
        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionVO);

        ResultDataVO listByVersionId = configHistoryService.topItemList(historyInputVO);
        Assert.assertNotNull(listByVersionId);
    }

    @Test
    public void exportExcel() {
        HistoryInputVO historyInputVO = new HistoryInputVO();
        HttpServletResponse response = null;
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configHistoryService.exportExcel(historyInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void exportExcel2Test() throws Exception {
        HistoryInputVO historyInputVO = new HistoryInputVO();
        historyInputVO.setVersionId(10L);
        HttpServletResponse response = null;
        DmFocVersionInfoDTO versionDTO =new DmFocVersionInfoDTO();
        versionDTO.setVersionId(11L);
        ResultDataVO resultDataVO=new ResultDataVO();

        mockStatic(UserInfoUtils.class);
        PowerMockito.when(UserInfoUtils.class, "getUserId").thenReturn(12424L);
        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionDTO);

        try {
            resultDataVO = configHistoryService.exportExcel(historyInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void exportExcel3Test() throws Exception {
        HistoryInputVO historyInputVO = new HistoryInputVO();
        historyInputVO.setVersionId(10L);
        historyInputVO.setModelType("CATEGORY");
        historyInputVO.setViewFlag("0");
        historyInputVO.setGranularityType("U");
        historyInputVO.setCaliberFlag("R");
        historyInputVO.setOverseaFlag("G");

        HttpServletResponse response = null;
        DmFocVersionInfoDTO versionDTO =new DmFocVersionInfoDTO();
        versionDTO.setVersionId(11L);
        versionDTO.setVersion("20230405");

        List<Map> cateItemWithWeightList=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("top_category_code", "1250A");
        cateItemWithWeightList.add(map);

        mockStatic(UserInfoUtils.class);
        PowerMockito.when(UserInfoUtils.class, "getUserId").thenReturn(12424L);

        List<DmFocTopCateInfoDTO> dmFocTopCateInfoDTOS=new ArrayList<>();
        DmFocTopCateInfoDTO cateInfoDTO = new DmFocTopCateInfoDTO();
        cateInfoDTO.setTopCategoryCode("1250A");
        dmFocTopCateInfoDTOS.add(cateInfoDTO);

        DmFoiImpExpRecordVO expRecordVO=new DmFoiImpExpRecordVO();

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionDTO);

        ResultDataVO resultDataVO = configHistoryService.exportExcel(historyInputVO);
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void exportExcel4Test() throws Exception {
        HistoryInputVO historyInputVO = new HistoryInputVO();
        historyInputVO.setVersionId(10L);
        historyInputVO.setModelType("CATEGORY");
        historyInputVO.setViewFlag("0");
        historyInputVO.setOverseaFlag("G");

        HttpServletResponse response = null;
        DmFocVersionInfoDTO versionDTO =new DmFocVersionInfoDTO();
        versionDTO.setVersionId(11L);
        versionDTO.setVersion("20230405");
        ResultDataVO resultDataVO = new ResultDataVO();

        mockStatic(UserInfoUtils.class);
        PowerMockito.when(UserInfoUtils.class, "getUserId").thenReturn(12424L);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionDTO);

        try {
            resultDataVO = configHistoryService.exportExcel(historyInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void exportExcel5Test() throws Exception {
        HistoryInputVO historyInputVO = new HistoryInputVO();
        historyInputVO.setVersionId(10L);
        historyInputVO.setModelType("CATEGORY");
        historyInputVO.setViewFlag("1");
        historyInputVO.setGranularityType("U");
        historyInputVO.setCaliberFlag("R");
        historyInputVO.setOverseaFlag("G");

        HttpServletResponse response = null;
        DmFocVersionInfoDTO versionDTO =new DmFocVersionInfoDTO();
        versionDTO.setVersionId(11L);
        versionDTO.setVersion("20230405");

        mockStatic(UserInfoUtils.class);
        PowerMockito.when(UserInfoUtils.class, "getUserId").thenReturn(12424L);

        List<Map> cateItemWithWeightList=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("top_category_code", "1250A");
        cateItemWithWeightList.add(map);

        List<DmFocTopCateInfoDTO> dmFocTopCateInfoDTOS=new ArrayList<>();
        DmFocTopCateInfoDTO cateInfoDTO = new DmFocTopCateInfoDTO();
        cateInfoDTO.setTopCategoryCode("1250B");
        dmFocTopCateInfoDTOS.add(cateInfoDTO);

        DmFoiImpExpRecordVO expRecordVO=new DmFoiImpExpRecordVO();

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionDTO);

        ResultDataVO resultDataVO = configHistoryService.exportExcel(historyInputVO);
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void exportExcel55Test() throws Exception {
        HistoryInputVO historyInputVO = new HistoryInputVO();
        historyInputVO.setVersionId(10L);
        historyInputVO.setModelType("CATEGORY");
        historyInputVO.setViewFlag("1");
        historyInputVO.setGranularityType("U");
        historyInputVO.setCaliberFlag("R");
        historyInputVO.setOverseaFlag("G");

        HttpServletResponse response = null;
        DmFocVersionInfoDTO versionDTO =new DmFocVersionInfoDTO();
        versionDTO.setVersionId(11L);
        versionDTO.setVersion("20230405");

        mockStatic(UserInfoUtils.class);
        PowerMockito.when(UserInfoUtils.class, "getUserId").thenReturn(12424L);

        List<Map> cateItemWithWeightList=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("is_top_flag", "Y");
        cateItemWithWeightList.add(map);

        List<DmFocTopCateInfoDTO> dmFocTopCateInfoDTOS=new ArrayList<>();
        DmFocTopCateInfoDTO cateInfoDTO = new DmFocTopCateInfoDTO();
        cateInfoDTO.setTopCategoryCode("1250B");
        dmFocTopCateInfoDTOS.add(cateInfoDTO);

        DmFoiImpExpRecordVO expRecordVO=new DmFoiImpExpRecordVO();

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionDTO);

        ResultDataVO resultDataVO = configHistoryService.exportExcel(historyInputVO);
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void exportExcel6Test() throws Exception {
        HistoryInputVO historyInputVO = new HistoryInputVO();
        historyInputVO.setVersionId(10L);
        historyInputVO.setModelType("ITEM");
        historyInputVO.setViewFlag("2");
        historyInputVO.setGranularityType("U");
        historyInputVO.setCaliberFlag("R");
        historyInputVO.setOverseaFlag("G");
        historyInputVO.setIndustryOrg("ICT");

        HttpServletResponse response = null;
        DmFocVersionInfoDTO versionDTO =new DmFocVersionInfoDTO();
        versionDTO.setVersionId(11L);
        versionDTO.setVersion("20230405");

        mockStatic(UserInfoUtils.class);
        PowerMockito.when(UserInfoUtils.class, "getUserId").thenReturn(12424L);

        List<Map> cateItemWithWeightList=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("top_item_code", "1250A");
        cateItemWithWeightList.add(map);

        List<DmFocTopItemInfoDTO> dmFocTopCateInfoDTOS=new ArrayList<>();
        DmFocTopItemInfoDTO cateInfoDTO = new DmFocTopItemInfoDTO();
        cateInfoDTO.setTopItemCode("1250B");
        dmFocTopCateInfoDTOS.add(cateInfoDTO);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionDTO);

        ResultDataVO resultDataVO = configHistoryService.exportExcel(historyInputVO);
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void exportExcel7Test() throws Exception {
        HistoryInputVO historyInputVO = new HistoryInputVO();
        historyInputVO.setVersionId(10L);
        historyInputVO.setModelType("ITEM");
        historyInputVO.setViewFlag("2");
        historyInputVO.setGranularityType("U");
        historyInputVO.setCaliberFlag("R");
        historyInputVO.setOverseaFlag("G");
        historyInputVO.setIndustryOrg("ICT");

        HttpServletResponse response = null;
        DmFocVersionInfoDTO versionDTO =new DmFocVersionInfoDTO();
        versionDTO.setVersionId(11L);
        versionDTO.setVersion("20230405");

        mockStatic(UserInfoUtils.class);
        PowerMockito.when(UserInfoUtils.class, "getUserId").thenReturn(12424L);

        List<Map> cateItemWithWeightList=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("top_item_code", "1250A");
        cateItemWithWeightList.add(map);

        List<DmFocTopItemInfoDTO> dmFocTopCateInfoDTOS=new ArrayList<>();
        DmFocTopItemInfoDTO cateInfoDTO = new DmFocTopItemInfoDTO();
        cateInfoDTO.setTopItemCode("1250A");
        dmFocTopCateInfoDTOS.add(cateInfoDTO);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionDTO);
        IRequestContext current = RequestContextManager.getCurrent();
        DmFocDataRefreshStatus dataRefreshStatus = new DmFocDataRefreshStatus();
        dataRefreshStatus.setStatus("TASK_INIT");
        dataRefreshStatus.setDelFlag("N");
        Long userId = UserInfoUtils.getUserId();
        dataRefreshStatus.setCreatedBy(userId);
        dataRefreshStatus.setLastUpdatedBy(userId);
        dataRefreshStatus.setCreationDate(new Date());
        dataRefreshStatus.setTaskFlag("CONFIG_" + historyInputVO.getIndustryOrg() + "_" + historyInputVO.getCostType() + "_EXPORT_" + historyInputVO.getCaliberFlag() + "_" + historyInputVO.getModelType());
        dataRefreshStatus.setTaskId(1L);
        int roleId =111;
        mockStatic(UserInfoUtils.class);
        PowerMockito.when(UserInfoUtils.class, "getRoleId").thenReturn(roleId);
        dataRefreshStatus.setRoleId(roleId);
        asyncService.fillPurchaseExportData(historyInputVO, dataRefreshStatus, current);

        ResultDataVO resultDataVO = configHistoryService.exportExcel(historyInputVO);
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void exportExcel8Test() throws Exception {
        HistoryInputVO historyInputVO = new HistoryInputVO();
        historyInputVO.setVersionId(10L);
        historyInputVO.setModelType("ITEM");
        historyInputVO.setViewFlag("2");
        historyInputVO.setGranularityType("U");
        historyInputVO.setCaliberFlag("R");
        historyInputVO.setOverseaFlag("G");
        historyInputVO.setIndustryOrg("ICT");

        HttpServletResponse response = null;
        DmFocVersionInfoDTO versionDTO =new DmFocVersionInfoDTO();
        versionDTO.setVersionId(11L);
        versionDTO.setVersion("20230405");

        List<Map> cateItemWithWeightList=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("top_item_code", "1250A");
        cateItemWithWeightList.add(map);

        mockStatic(UserInfoUtils.class);
        PowerMockito.when(UserInfoUtils.class, "getUserId").thenReturn(12424L);

        List<DmFocTopItemInfoDTO> dmFocTopCateInfoDTOS=new ArrayList<>();
        DmFocTopItemInfoDTO cateInfoDTO = new DmFocTopItemInfoDTO();
        cateInfoDTO.setTopItemCode("1250A");
        dmFocTopCateInfoDTOS.add(cateInfoDTO);

        IRequestContext current = RequestContextManager.getCurrent();
        DmFocDataRefreshStatus dataRefreshStatus = new DmFocDataRefreshStatus();
        dataRefreshStatus.setStatus("TASK_INIT");
        dataRefreshStatus.setDelFlag("N");
        Long userId = UserInfoUtils.getUserId();
        dataRefreshStatus.setCreatedBy(userId);
        dataRefreshStatus.setLastUpdatedBy(userId);
        dataRefreshStatus.setCreationDate(new Date());
        dataRefreshStatus.setTaskFlag("CONFIG_" + historyInputVO.getIndustryOrg() + "_" + historyInputVO.getCostType() + "_EXPORT_" + historyInputVO.getCaliberFlag() + "_" + historyInputVO.getModelType());
        dataRefreshStatus.setTaskId(1L);
        int roleId =111;
        mockStatic(UserInfoUtils.class);
        PowerMockito.when(UserInfoUtils.class, "getRoleId").thenReturn(roleId);
        dataRefreshStatus.setRoleId(roleId);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionDTO);
        asyncService.fillPurchaseExportData(historyInputVO, dataRefreshStatus, current);
        ResultDataVO resultDataVO = new ResultDataVO();

        try {
            resultDataVO = configHistoryService.exportExcel(historyInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void exportExcel9Test() throws Exception {
        HistoryInputVO historyInputVO = new HistoryInputVO();
        historyInputVO.setVersionId(10L);
        historyInputVO.setModelType("ITEM");
        historyInputVO.setViewFlag("3");
        historyInputVO.setGranularityType("P");
        historyInputVO.setCaliberFlag("R");
        historyInputVO.setOverseaFlag("G");
        historyInputVO.setIndustryOrg("ICT");

        HttpServletResponse response = null;
        DmFocVersionInfoDTO versionDTO =new DmFocVersionInfoDTO();
        versionDTO.setVersionId(11L);
        versionDTO.setVersion("20230405");

        List<Map> cateItemWithWeightList=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("top_item_code", "1250A");
        cateItemWithWeightList.add(map);

        List<DmFocTopItemInfoDTO> dmFocTopCateInfoDTOS=new ArrayList<>();
        DmFocTopItemInfoDTO cateInfoDTO = new DmFocTopItemInfoDTO();
        cateInfoDTO.setTopItemCode("1250A");
        dmFocTopCateInfoDTOS.add(cateInfoDTO);
        mockStatic(UserInfoUtils.class);
        PowerMockito.when(UserInfoUtils.class, "getUserId").thenReturn(12424L);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionDTO);

        IRequestContext current = RequestContextManager.getCurrent();
        DmFocDataRefreshStatus dataRefreshStatus = new DmFocDataRefreshStatus();
        dataRefreshStatus.setStatus("TASK_INIT");
        dataRefreshStatus.setDelFlag("N");
        Long userId = UserInfoUtils.getUserId();
        dataRefreshStatus.setCreatedBy(userId);
        dataRefreshStatus.setLastUpdatedBy(userId);
        dataRefreshStatus.setCreationDate(new Date());
        dataRefreshStatus.setTaskFlag("CONFIG_" + historyInputVO.getIndustryOrg() + "_" + historyInputVO.getCostType() + "_EXPORT_" + historyInputVO.getCaliberFlag() + "_" + historyInputVO.getModelType());
        dataRefreshStatus.setTaskId(1L);
        int roleId =111;
        mockStatic(UserInfoUtils.class);
        PowerMockito.when(UserInfoUtils.class, "getRoleId").thenReturn(roleId);
        dataRefreshStatus.setRoleId(roleId);

        asyncService.fillPurchaseExportData(historyInputVO, dataRefreshStatus, current);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configHistoryService.exportExcel(historyInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void exportExcel10Test() throws Exception {
        HistoryInputVO historyInputVO = new HistoryInputVO();
        historyInputVO.setVersionId(10L);
        historyInputVO.setModelType("ITEM");
        historyInputVO.setViewFlag("4");
        historyInputVO.setGranularityType("P");
        historyInputVO.setCaliberFlag("R");
        historyInputVO.setOverseaFlag("G");
        historyInputVO.setIndustryOrg("ICT");

        HttpServletResponse response = null;
        DmFocVersionInfoDTO versionDTO =new DmFocVersionInfoDTO();
        versionDTO.setVersionId(11L);
        versionDTO.setVersion("20230405");

        List<Map> cateItemWithWeightList=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("top_item_code", "1250A");
        cateItemWithWeightList.add(map);

        mockStatic(UserInfoUtils.class);
        PowerMockito.when(UserInfoUtils.class, "getUserId").thenReturn(12424L);

        List<DmFocTopItemInfoDTO> dmFocTopCateInfoDTOS=new ArrayList<>();
        DmFocTopItemInfoDTO cateInfoDTO = new DmFocTopItemInfoDTO();
        cateInfoDTO.setTopItemCode("1250A");
        dmFocTopCateInfoDTOS.add(cateInfoDTO);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionDTO);

        ResultDataVO resultDataVO = new ResultDataVO();
        IRequestContext current = RequestContextManager.getCurrent();
        DmFocDataRefreshStatus dataRefreshStatus = new DmFocDataRefreshStatus();
        dataRefreshStatus.setStatus("TASK_INIT");
        dataRefreshStatus.setDelFlag("N");
        Long userId = UserInfoUtils.getUserId();
        dataRefreshStatus.setCreatedBy(userId);
        dataRefreshStatus.setLastUpdatedBy(userId);
        dataRefreshStatus.setCreationDate(new Date());
        dataRefreshStatus.setTaskFlag("CONFIG_" + historyInputVO.getIndustryOrg() + "_" + historyInputVO.getCostType() + "_EXPORT_" + historyInputVO.getCaliberFlag() + "_" + historyInputVO.getModelType());
        dataRefreshStatus.setTaskId(1L);
        int roleId =111;
        mockStatic(UserInfoUtils.class);
        PowerMockito.when(UserInfoUtils.class, "getRoleId").thenReturn(roleId);
        dataRefreshStatus.setRoleId(roleId);

        asyncService.fillPurchaseExportData(historyInputVO, dataRefreshStatus, current);
        try {
            resultDataVO = configHistoryService.exportExcel(historyInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void exportExcel11Test() throws Exception {
        HistoryInputVO historyInputVO = new HistoryInputVO();
        historyInputVO.setVersionId(10L);
        historyInputVO.setModelType("CATEGORY");
        historyInputVO.setViewFlag("4");
        historyInputVO.setGranularityType("D");
        historyInputVO.setCaliberFlag("R");
        historyInputVO.setOverseaFlag("G");
        historyInputVO.setIndustryOrg("ICT");

        HttpServletResponse response = null;
        DmFocVersionInfoDTO versionDTO =new DmFocVersionInfoDTO();
        versionDTO.setVersionId(11L);
        versionDTO.setVersion("20230405");

        mockStatic(UserInfoUtils.class);
        PowerMockito.when(UserInfoUtils.class, "getUserId").thenReturn(12424L);

        List<Map> cateItemWithWeightList=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("top_item_code", "1250A");
        cateItemWithWeightList.add(map);

        List<DmFocTopItemInfoDTO> dmFocTopCateInfoDTOS=new ArrayList<>();
        DmFocTopItemInfoDTO cateInfoDTO = new DmFocTopItemInfoDTO();
        cateInfoDTO.setTopItemCode("1250A");
        dmFocTopCateInfoDTOS.add(cateInfoDTO);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionDTO);

        ResultDataVO resultDataVO = new ResultDataVO();

        IRequestContext current = RequestContextManager.getCurrent();
        DmFocDataRefreshStatus dataRefreshStatus = new DmFocDataRefreshStatus();
        dataRefreshStatus.setStatus("TASK_INIT");
        dataRefreshStatus.setDelFlag("N");
        Long userId = UserInfoUtils.getUserId();
        dataRefreshStatus.setCreatedBy(userId);
        dataRefreshStatus.setLastUpdatedBy(userId);
        dataRefreshStatus.setCreationDate(new Date());
        dataRefreshStatus.setTaskFlag("CONFIG_" + historyInputVO.getIndustryOrg() + "_" + historyInputVO.getCostType() + "_EXPORT_" + historyInputVO.getCaliberFlag() + "_" + historyInputVO.getModelType());
        dataRefreshStatus.setTaskId(1L);
        int roleId =111;
        mockStatic(UserInfoUtils.class);
        PowerMockito.when(UserInfoUtils.class, "getRoleId").thenReturn(roleId);
        dataRefreshStatus.setRoleId(roleId);

        asyncService.fillPurchaseExportData(historyInputVO, dataRefreshStatus, current);
        try {
            resultDataVO = configHistoryService.exportExcel(historyInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void exportExcel12Test() throws Exception {
        HistoryInputVO historyInputVO = new HistoryInputVO();
        historyInputVO.setVersionId(10L);
        historyInputVO.setModelType("CATEGORY");
        historyInputVO.setViewFlag("5");
        historyInputVO.setGranularityType("U");
        historyInputVO.setCaliberFlag("R");
        historyInputVO.setOverseaFlag("G");

        HttpServletResponse response = null;
        DmFocVersionInfoDTO versionDTO =new DmFocVersionInfoDTO();
        versionDTO.setVersionId(11L);
        versionDTO.setVersion("20230405");

        List<Map> cateItemWithWeightList=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("top_item_code", "1250A");
        cateItemWithWeightList.add(map);

        mockStatic(UserInfoUtils.class);
        PowerMockito.when(UserInfoUtils.class, "getUserId").thenReturn(12424L);

        List<DmFocTopItemInfoDTO> dmFocTopCateInfoDTOS=new ArrayList<>();
        DmFocTopItemInfoDTO cateInfoDTO = new DmFocTopItemInfoDTO();
        cateInfoDTO.setTopItemCode("1250A");
        dmFocTopCateInfoDTOS.add(cateInfoDTO);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionDTO);

        ResultDataVO resultDataVO = new ResultDataVO();

        try {
            resultDataVO = configHistoryService.exportExcel(historyInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void exportExcel13Test() throws Exception {
        HistoryInputVO historyInputVO = new HistoryInputVO();
        historyInputVO.setVersionId(10L);
        historyInputVO.setModelType("CATEGORY");
        historyInputVO.setViewFlag("1");
        historyInputVO.setGranularityType("D");
        historyInputVO.setCaliberFlag("R");
        historyInputVO.setOverseaFlag("G");

        HttpServletResponse response = null;
        DmFocVersionInfoDTO versionDTO =new DmFocVersionInfoDTO();
        versionDTO.setVersionId(11L);
        versionDTO.setVersion("20230405");

        List<Map> cateItemWithWeightList=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("top_item_code", "1250A");
        cateItemWithWeightList.add(map);

        List<DmFocTopItemInfoDTO> dmFocTopCateInfoDTOS=new ArrayList<>();
        DmFocTopItemInfoDTO cateInfoDTO = new DmFocTopItemInfoDTO();
        cateInfoDTO.setTopItemCode("1250A");
        dmFocTopCateInfoDTOS.add(cateInfoDTO);
        mockStatic(UserInfoUtils.class);
        PowerMockito.when(UserInfoUtils.class, "getUserId").thenReturn(12424L);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionDTO);

        ResultDataVO resultDataVO = new ResultDataVO();

        try {
            resultDataVO = configHistoryService.exportExcel(historyInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void exportExcel14Test() throws Exception {
        HistoryInputVO historyInputVO = new HistoryInputVO();
        historyInputVO.setVersionId(10L);
        historyInputVO.setModelType("CATEGORY");
        historyInputVO.setViewFlag("6");
        historyInputVO.setGranularityType("D");
        historyInputVO.setCaliberFlag("R");
        historyInputVO.setOverseaFlag("G");

        HttpServletResponse response = null;
        DmFocVersionInfoDTO versionDTO =new DmFocVersionInfoDTO();
        versionDTO.setVersionId(11L);
        versionDTO.setVersion("20230405");

        List<Map> cateItemWithWeightList=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("top_item_code", "1250A");
        cateItemWithWeightList.add(map);

        List<DmFocTopItemInfoDTO> dmFocTopCateInfoDTOS=new ArrayList<>();
        DmFocTopItemInfoDTO cateInfoDTO = new DmFocTopItemInfoDTO();
        cateInfoDTO.setTopItemCode("1250A");
        dmFocTopCateInfoDTOS.add(cateInfoDTO);

        mockStatic(UserInfoUtils.class);
        PowerMockito.when(UserInfoUtils.class, "getUserId").thenReturn(12424L);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionDTO);

        ResultDataVO resultDataVO = new ResultDataVO();

        try {
            resultDataVO = configHistoryService.exportExcel(historyInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void exportExcel15Test() throws Exception {
        HistoryInputVO historyInputVO = new HistoryInputVO();
        historyInputVO.setVersionId(10L);
        historyInputVO.setModelType("CATEGORY");
        historyInputVO.setViewFlag("7");
        historyInputVO.setGranularityType("D");
        historyInputVO.setCaliberFlag("R");
        historyInputVO.setOverseaFlag("G");

        HttpServletResponse response = null;
        DmFocVersionInfoDTO versionDTO =new DmFocVersionInfoDTO();
        versionDTO.setVersionId(11L);
        versionDTO.setVersion("20230405");

        List<Map> cateItemWithWeightList=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("top_item_code", "1250A");
        cateItemWithWeightList.add(map);

        mockStatic(UserInfoUtils.class);
        PowerMockito.when(UserInfoUtils.class, "getUserId").thenReturn(12424L);

        List<DmFocTopItemInfoDTO> dmFocTopCateInfoDTOS=new ArrayList<>();
        DmFocTopItemInfoDTO cateInfoDTO = new DmFocTopItemInfoDTO();
        cateInfoDTO.setTopItemCode("1250A");
        dmFocTopCateInfoDTOS.add(cateInfoDTO);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionDTO);

        ResultDataVO resultDataVO = new ResultDataVO();

        try {
            resultDataVO = configHistoryService.exportExcel(historyInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void exportExcel16Test() throws Exception {
        HistoryInputVO historyInputVO = new HistoryInputVO();
        historyInputVO.setVersionId(10L);
        historyInputVO.setModelType("CATEGORY");
        historyInputVO.setViewFlag("0");
        historyInputVO.setGranularityType("D");
        historyInputVO.setCaliberFlag("R");
        historyInputVO.setOverseaFlag("G");

        HttpServletResponse response = null;
        DmFocVersionInfoDTO versionDTO =new DmFocVersionInfoDTO();
        versionDTO.setVersionId(11L);
        versionDTO.setVersion("20230405");

        List<Map> cateItemWithWeightList=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("top_item_code", "1250A");
        cateItemWithWeightList.add(map);

        mockStatic(UserInfoUtils.class);
        PowerMockito.when(UserInfoUtils.class, "getUserId").thenReturn(12424L);

        List<DmFocTopItemInfoDTO> dmFocTopCateInfoDTOS=new ArrayList<>();
        DmFocTopItemInfoDTO cateInfoDTO = new DmFocTopItemInfoDTO();
        cateInfoDTO.setTopItemCode("1250A");
        dmFocTopCateInfoDTOS.add(cateInfoDTO);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionDTO);

        ResultDataVO resultDataVO = new ResultDataVO();

        try {
            resultDataVO = configHistoryService.exportExcel(historyInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void exportExcel17Test() throws Exception {
        HistoryInputVO historyInputVO = new HistoryInputVO();
        historyInputVO.setVersionId(10L);
        historyInputVO.setModelType("CATEGORY");
        historyInputVO.setViewFlag("2");
        historyInputVO.setGranularityType("D");
        historyInputVO.setCaliberFlag("R");
        historyInputVO.setOverseaFlag("G");

        HttpServletResponse response = null;
        DmFocVersionInfoDTO versionDTO =new DmFocVersionInfoDTO();
        versionDTO.setVersionId(11L);
        versionDTO.setVersion("20230405");

        List<Map> cateItemWithWeightList=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("top_item_code", "1250A");
        cateItemWithWeightList.add(map);

        List<DmFocTopItemInfoDTO> dmFocTopCateInfoDTOS=new ArrayList<>();
        DmFocTopItemInfoDTO cateInfoDTO = new DmFocTopItemInfoDTO();
        cateInfoDTO.setTopItemCode("1250A");
        dmFocTopCateInfoDTOS.add(cateInfoDTO);
        mockStatic(UserInfoUtils.class);
        PowerMockito.when(UserInfoUtils.class, "getUserId").thenReturn(12424L);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionDTO);

        ResultDataVO resultDataVO = new ResultDataVO();

        try {
            resultDataVO = configHistoryService.exportExcel(historyInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void exportExcel18Test() throws Exception {
        HistoryInputVO historyInputVO = new HistoryInputVO();
        historyInputVO.setVersionId(10L);
        historyInputVO.setModelType("CATEGORY");
        historyInputVO.setViewFlag("3");
        historyInputVO.setGranularityType("D");
        historyInputVO.setCaliberFlag("R");
        historyInputVO.setOverseaFlag("G");

        HttpServletResponse response = null;
        DmFocVersionInfoDTO versionDTO =new DmFocVersionInfoDTO();
        versionDTO.setVersionId(11L);
        versionDTO.setVersion("20230405");

        List<Map> cateItemWithWeightList=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("top_item_code", "1250A");
        cateItemWithWeightList.add(map);

        List<DmFocTopItemInfoDTO> dmFocTopCateInfoDTOS=new ArrayList<>();
        DmFocTopItemInfoDTO cateInfoDTO = new DmFocTopItemInfoDTO();
        cateInfoDTO.setTopItemCode("1250A");
        dmFocTopCateInfoDTOS.add(cateInfoDTO);
        mockStatic(UserInfoUtils.class);
        PowerMockito.when(UserInfoUtils.class, "getUserId").thenReturn(12424L);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionDTO);

        ResultDataVO resultDataVO = new ResultDataVO();

        try {
            resultDataVO = configHistoryService.exportExcel(historyInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void exportExcel19Test() throws Exception {
        HistoryInputVO historyInputVO = new HistoryInputVO();
        historyInputVO.setVersionId(10L);
        historyInputVO.setModelType("CATEGORY");
        historyInputVO.setViewFlag("5");
        historyInputVO.setGranularityType("D");
        historyInputVO.setCaliberFlag("R");
        historyInputVO.setOverseaFlag("G");

        HttpServletResponse response = null;
        DmFocVersionInfoDTO versionDTO =new DmFocVersionInfoDTO();
        versionDTO.setVersionId(11L);
        versionDTO.setVersion("20230405");

        List<Map> cateItemWithWeightList=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("top_item_code", "1250A");
        cateItemWithWeightList.add(map);

        List<DmFocTopItemInfoDTO> dmFocTopCateInfoDTOS=new ArrayList<>();
        DmFocTopItemInfoDTO cateInfoDTO = new DmFocTopItemInfoDTO();
        cateInfoDTO.setTopItemCode("1250A");
        dmFocTopCateInfoDTOS.add(cateInfoDTO);

        mockStatic(UserInfoUtils.class);
        PowerMockito.when(UserInfoUtils.class, "getUserId").thenReturn(12424L);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionDTO);

        ResultDataVO resultDataVO = new ResultDataVO();

        try {
            resultDataVO = configHistoryService.exportExcel(historyInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void exportExcel20Test() throws Exception {
        HistoryInputVO historyInputVO = new HistoryInputVO();
        historyInputVO.setVersionId(10L);
        historyInputVO.setModelType("ITEM");
        historyInputVO.setViewFlag("8");
        historyInputVO.setGranularityType("D");
        historyInputVO.setCaliberFlag("R");
        historyInputVO.setOverseaFlag("G");

        HttpServletResponse response = null;
        DmFocVersionInfoDTO versionDTO =new DmFocVersionInfoDTO();
        versionDTO.setVersionId(11L);
        versionDTO.setVersion("20230405");

        List<Map> cateItemWithWeightList=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("top_item_code", "1250A");
        cateItemWithWeightList.add(map);

        List<DmFocTopItemInfoDTO> dmFocTopCateInfoDTOS=new ArrayList<>();
        DmFocTopItemInfoDTO cateInfoDTO = new DmFocTopItemInfoDTO();
        cateInfoDTO.setTopItemCode("1250A");
        dmFocTopCateInfoDTOS.add(cateInfoDTO);
        mockStatic(UserInfoUtils.class);
        PowerMockito.when(UserInfoUtils.class, "getUserId").thenReturn(12424L);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionDTO);

        ResultDataVO resultDataVO = new ResultDataVO();

        try {
            resultDataVO = configHistoryService.exportExcel(historyInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void exportExcel21Test() throws Exception {
        HistoryInputVO historyInputVO = new HistoryInputVO();
        historyInputVO.setVersionId(10L);
        historyInputVO.setModelType("ITEM");
        historyInputVO.setViewFlag("0");
        historyInputVO.setGranularityType("P");
        historyInputVO.setCaliberFlag("R");
        historyInputVO.setOverseaFlag("G");

        HttpServletResponse response = null;
        DmFocVersionInfoDTO versionDTO =new DmFocVersionInfoDTO();
        versionDTO.setVersionId(11L);
        versionDTO.setVersion("20230405");

        List<Map> cateItemWithWeightList=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("top_item_code", "1250A");
        cateItemWithWeightList.add(map);

        List<DmFocTopItemInfoDTO> dmFocTopCateInfoDTOS=new ArrayList<>();
        DmFocTopItemInfoDTO cateInfoDTO = new DmFocTopItemInfoDTO();
        cateInfoDTO.setTopItemCode("1250A");
        dmFocTopCateInfoDTOS.add(cateInfoDTO);

        mockStatic(UserInfoUtils.class);
        PowerMockito.when(UserInfoUtils.class, "getUserId").thenReturn(12424L);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionDTO);

        ResultDataVO resultDataVO = new ResultDataVO();

        try {
            resultDataVO = configHistoryService.exportExcel(historyInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }
}