/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.common;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

/**
 * CommonViewVOTest Class
 *
 * <AUTHOR>
 * @since 2023/4/22
 */
public class CommonViewVOTest extends BaseVOCoverUtilsTest<CommonViewVO> {

    @Override
    protected Class<CommonViewVO> getTClass() {
        return CommonViewVO.class;
    }
}