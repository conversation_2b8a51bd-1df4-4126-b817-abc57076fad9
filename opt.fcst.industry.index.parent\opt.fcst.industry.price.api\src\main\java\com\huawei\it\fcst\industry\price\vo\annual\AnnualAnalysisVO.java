/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.vo.annual;

import com.huawei.it.fcst.industry.price.vo.common.CommonPriceBaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * AnnualAnalysisVO Class
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper=true)
@NoArgsConstructor
@ApiModel(value = "定价年度VO")
public class AnnualAnalysisVO extends CommonPriceBaseVO implements Serializable {
    private static final long serialVersionUID = -1576704097209028139L;

    @ApiModelProperty(value = "父层级编码列表")
    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<String> parentCodeList;

    @ApiModelProperty(value = "虚化父编码列表")
    private List<String> customParentCodeList;

    @ApiModelProperty(value = "虚化编码列表")
    private List<String> customGroupCodeList;

    @ApiModelProperty(value = "编码列表")
    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<String> groupCodeList;

    @ApiModelProperty(value = "虚化id列表")
    private List<String> customIdList;

    @ApiModelProperty(value = "子编码列表")
    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<String> subGroupCodeList;

    @ApiModelProperty(value = "虚化id")
    private Long customId;

    @ApiModelProperty(value = "重量级团队层级el")
    private String teamLevel;

    @ApiModelProperty(value = "排序字段")
    private String orderColumn;

    @ApiModelProperty(value = "排序方式")
    private String orderMethod;

    @ApiModelProperty(value = "年份")
    private String year;

    // 最新一年
    private String lastYear;

    @ApiModelProperty(value = "版本")
    private Long versionId;

    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @ApiModelProperty(value = "国内/海外名称")
    private String overseaFlagCnName;

    @ApiModelProperty(value = "地区部名称")
    private String regionCnName;

    @ApiModelProperty(value = "代表处名称")
    private String repofficeCnName;

    @ApiModelProperty(value = "实际数截止月")
    private String actualMonth;

    @ApiModelProperty(value = "最大值")
    public String maxValue;

    @ApiModelProperty(value = "编码序号")
    private String groupCodeOrder;

    private String prodRndTeamCodeOrder;

    @ApiModelProperty(value = "会计期年份")
    private String periodYear;

    @ApiModelProperty(value = "会计期")
    private Long periodId;

    @ApiModelProperty(value = "年份列表")
    private List<String> yearList;

    @ApiModelProperty(value = "是否多选")
    private Boolean isMultipleSelect;

    @ApiModelProperty(value = "名称")
    private String nextGroupName;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "是否需要虚化")
    private Boolean isNeedBlur;

    // 大T
    @ApiModelProperty(value = "大T系统部编码")
    private String signTopCustCategoryCode;

    @ApiModelProperty(value = "大T系统部名称")
    private String signTopCustCategoryCnName;

    // 子网
    @ApiModelProperty(value = "子网系统")
    private String signSubsidiaryCustcatgCnName;

    @ApiModelProperty(value = "标识")
    private String condition;

    @ApiModelProperty(value = "父层级")
    private String parentLevel;

    @ApiModelProperty(value = "bg名称")
    private String bgCnName;

}

