/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.month;

import com.huawei.it.fcst.annotations.ExportAttribute;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * The Entity of MultiIndexExportVO
 *
 * <AUTHOR>
 * @since 2023/03/14
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MultiIndexExportVO {

    @ExportAttribute(sort = 0, dataType = "Number")
    @ApiModelProperty(value = "年月")
    private Long periodId;

    @ExportAttribute(sort = 1)
    private String costType;

    /**
     * 上层级中文名称
     **/
    @ExportAttribute(sort = 2)
    @ApiModelProperty(value = "上层级中文名称")
    private String parentCnName;

    /**
     * 分层级中文名称
     **/
    @ExportAttribute(sort = 3)
    @ApiModelProperty(value = "分层级中文名称")
    private String groupCnName;

    /**
     * 价格指数
     **/
    @ExportAttribute(sort = 4, dataType = "Number")
    @ApiModelProperty(value = "价格指数")
    private Double costIndex;

}