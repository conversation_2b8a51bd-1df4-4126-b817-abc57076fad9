/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.impl.common;

import com.huawei.it.fcst.constant.Constant;
import com.huawei.it.fcst.industry.price.constant.CommonConstant;
import com.huawei.it.fcst.industry.price.dao.IDmFcstPriceDimInfoDao;
import com.huawei.it.fcst.industry.price.service.common.IPriceDataPermissionService;
import com.huawei.it.fcst.industry.price.vo.common.CommonPriceBaseVO;
import com.huawei.it.fcst.industry.price.vo.common.DmFcstPriceDimInfoVO;
import com.huawei.it.fcst.industry.price.vo.common.PriceDataPermissionsVO;
import com.huawei.it.fcst.util.UserInfoUtils;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.KeyValuePairVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.lookup.LookupItemVO;
import com.huawei.it.jalor5.lookup.service.ILookupItemQueryService;
import com.huawei.it.jalor5.security.ProgramItemVO;
import com.huawei.it.jalor5.security.ProgramVO;
import com.huawei.it.jalor5.security.RoleVO;
import com.huawei.it.jalor5.security.UserVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Named;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/8
 */
@Slf4j
@Named("priceDataPermissionService")
@JalorResource(code = "priceDataPermissionService", desc = "定价指数-用户数据范围")
public class PriceDataPermissionService implements IPriceDataPermissionService {

    private static final String ALL = "ALL";

    private static final String ALL_CONDITION = "@ALLCONDITION@";

    private static final String NO_PERMISSION = "NO_PERMISSION";

    @Autowired
    private ILookupItemQueryService lookupItemQueryService;

    @Autowired
    private IDmFcstPriceDimInfoDao dmFcstPriceDimInfoDao;

    @Autowired
    private PriceCommonService commonService;

    @Override
    @JalorOperation(code = "getPriceCurrentRoleDataPermission", desc = "查询定价指数当前登录角色数据范围权限")
    public PriceDataPermissionsVO getCurrentRoleDataPermission() {
        log.info(">>>Begin CommonService::getCurrentRoleDataPermission");
        PriceDataPermissionsVO priceDataPermissionsVO = new PriceDataPermissionsVO();
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        log.info(">>>CurrentRoleInfo:<{}-{}>", currentRole.getRoleId(), currentRole.getRoleName());
        priceDataPermissionsVO.setRoleId(currentRole.getRoleId());
        priceDataPermissionsVO.setRoleName(currentRole.getRoleName());
        List<ProgramVO> currentPrograms = currentUser.getCurrentPrograms();
        Set<String> dimensionValues = new HashSet<>();
        // 判断是否总体查看人
        List<LookupItemVO> allLookRoleLsit = null;
        try {
            allLookRoleLsit = lookupItemQueryService.findItemListByClassify("OVERALL_VIEWER_ROLE");
        } catch (ApplicationException ex) {
            log.error("get lookupItemQueryService error: {}", ex.getMessage());
        }
        List<String> roleNameList = allLookRoleLsit.stream().map(LookupItemVO::getItemName).collect(Collectors.toList());
        if (roleNameList.contains(currentRole.getRoleName())) {
            dimensionValues.add(ALL);
        }
        List<ProgramItemVO> allProgramItemVOList = new ArrayList<>();
        for (ProgramVO programVO : currentPrograms) {
            List<ProgramItemVO> programItemVOList = programVO.getItems();
            allProgramItemVOList.addAll(programItemVOList);
            Set<String> singleDimensionValues = new HashSet<>();
            for (ProgramItemVO programItemVO : programItemVOList) {
                // 根据角色来控制权限
                if (CommonConstant.REGION_ANALYST_PRICE.equals(currentRole.getRoleName())) {
                    if (Constant.StrEnum.PRICE_DIMENSION_CODE.getValue().equals(programItemVO.getDimension().getDimensionCode())) {
                        dimensionValues.addAll(programItemVO.getValues().stream().map(KeyValuePairVO::getKey).collect(Collectors.toSet()));
                        singleDimensionValues.addAll(programItemVO.getValues().stream().map(KeyValuePairVO::getKey).collect(Collectors.toSet()));
                    }
                    if (Constant.StrEnum.PRICE_LOCATION_DIMENSION_CODE.getValue().equals(programItemVO.getDimension().getDimensionCode())) {
                        setLocationDimensionPermission(priceDataPermissionsVO, programItemVO,singleDimensionValues);
                    }
                    if (Constant.StrEnum.PRICE_KEY_SUBACCOUNT.getValue().equals(programItemVO.getDimension().getDimensionCode())) {
                        setKeyAndSubAccountPermission(priceDataPermissionsVO, programItemVO);
                    }
                } else {
                    // (ICT + 数字能源 + IAS)的数据范围
                    if (Constant.StrEnum.PRICE_DIMENSION_CODE.getValue().equals(programItemVO.getDimension().getDimensionCode())) {
                        dimensionValues.addAll(programItemVO.getValues().stream().map(KeyValuePairVO::getKey).collect(Collectors.toSet()));
                        singleDimensionValues.addAll(programItemVO.getValues().stream().map(KeyValuePairVO::getKey).collect(Collectors.toSet()));
                    }
                }
            }
        }
        // 区域分析师不申请区域维度和大T维度时设置权限为NO_PERMISSION
        if (CommonConstant.REGION_ANALYST_PRICE.equals(currentRole.getRoleName())) {
            List<ProgramItemVO> locationDimension = allProgramItemVOList.stream().filter(result -> Constant.StrEnum.PRICE_LOCATION_DIMENSION_CODE.getValue().equals(result.getDimension().getDimensionCode())).collect(Collectors.toList());
            if (locationDimension.size() == 0) {
                priceDataPermissionsVO.getOverseaDimensionSet().add(NO_PERMISSION);
                priceDataPermissionsVO.getRegionCodeDimensionSet().add(NO_PERMISSION);
                priceDataPermissionsVO.getRegionCodeDimensionTrueSet().add(NO_PERMISSION);
                priceDataPermissionsVO.getRepofficeCodeDimensionSet().add(NO_PERMISSION);
            }
            List<ProgramItemVO> subAccountDimension = allProgramItemVOList.stream().filter(result -> Constant.StrEnum.PRICE_KEY_SUBACCOUNT.getValue().equals(result.getDimension().getDimensionCode())).collect(Collectors.toList());
            if (subAccountDimension.size() == 0) {
                priceDataPermissionsVO.getSignTopCustCategoryCodeSet().add(NO_PERMISSION);
                priceDataPermissionsVO.getSignSubsidiaryCustcatgCodeSet().add(NO_PERMISSION);
            }
        }
        setAllDimensionPermission(priceDataPermissionsVO, dimensionValues);
        return priceDataPermissionsVO;
    }

    private void setKeyAndSubAccountPermission(PriceDataPermissionsVO dataPermissionsVO, ProgramItemVO programItemVO) {
        Set<String> dimensionValues = new HashSet<>();
        dimensionValues.addAll(programItemVO.getValues().stream().map(KeyValuePairVO::getKey).collect(Collectors.toSet()));
        if (CollectionUtils.isNotEmpty(dimensionValues)) {
            if (!dimensionValues.contains(ALL) && !dimensionValues.contains(ALL_CONDITION)) {
                // 解析 大T系統,子网系统
                // 创建一个新vo，设置为当前变量，第二个循环进来变成new vo
                parsingKeyAndSubAccountDimensionCode(dataPermissionsVO, dimensionValues);
            }
        } else {
            dataPermissionsVO.getSignTopCustCategoryCodeSet().add(NO_PERMISSION);
            dataPermissionsVO.getSignSubsidiaryCustcatgCodeSet().add(NO_PERMISSION);
        }
    }

    private void parsingKeyAndSubAccountDimensionCode(PriceDataPermissionsVO dataPermissionsVO, Set<String> dimensionValues) {
        Set<String> signTopCustCategoryCodeValues = new HashSet<>();
        Set<String> signSubsidiaryCustcatgCodeValues = new HashSet<>();
        PriceDataPermissionsVO keyAndSubAccountPermissionsVO = new PriceDataPermissionsVO();
        dimensionValues.forEach(value -> {
            if (value.contains("_SIGN_PRICE") || value.contains("_TOP_PRICE")) {
                signTopCustCategoryCodeValues.add(value);
            }
            if (value.contains("_SUB_PRICE")) {
                signSubsidiaryCustcatgCodeValues.add(value);
            }
        });
        // 解析大T系统部权限
        signTopCustCategoryCodeSet(signTopCustCategoryCodeValues, keyAndSubAccountPermissionsVO);
        // 全选勾选后，子项默认就是ALL，大T和子网设置为空维度
        if (keyAndSubAccountPermissionsVO.getSignTopCustCategoryCodeSet().contains("ALL")) {
            dataPermissionsVO.getSignTopCustCategoryCodeSet().clear();
            dataPermissionsVO.getSignSubsidiaryCustcatgCodeSet().clear();
        } else {
            // 解析子网系统部权限
            signSubsidiaryCustcatgCodeSet(signSubsidiaryCustcatgCodeValues, keyAndSubAccountPermissionsVO);
            dataPermissionsVO.getSignTopCustCategoryCodeSet().addAll(keyAndSubAccountPermissionsVO.getSignTopCustCategoryCodeSet());
            dataPermissionsVO.getSignSubsidiaryCustcatgCodeSet().addAll(keyAndSubAccountPermissionsVO.getSignSubsidiaryCustcatgCodeSet());
        }
    }

    private void setLocationDimensionPermission(PriceDataPermissionsVO dataPermissionsVO, ProgramItemVO programItemVO,Set<String> singleDimensionValues) {
        Set<String> dimensionValues = new HashSet<>();
        dimensionValues.addAll(programItemVO.getValues().stream().map(KeyValuePairVO::getKey).collect(Collectors.toSet()));
        PriceDataPermissionsVO singlePermissionsVO = new PriceDataPermissionsVO();
        if (CollectionUtils.isNotEmpty(dimensionValues)) {
            if (!dimensionValues.contains(ALL) && !dimensionValues.contains(ALL_CONDITION)) {
                // 解析 国内海外，地区部，代表处
                // 创建一个新vo，设置为当前变量，第二个循环进来变成new vo
                parsingLocationDimensionCode(dataPermissionsVO, dimensionValues, singlePermissionsVO);
            }
        } else {
            dataPermissionsVO.getOverseaDimensionSet().add(NO_PERMISSION);
            dataPermissionsVO.getRegionCodeDimensionSet().add(NO_PERMISSION);
            dataPermissionsVO.getRepofficeCodeDimensionSet().add(NO_PERMISSION);
        }
        // 组装map
        combineLocationMap(singleDimensionValues, singlePermissionsVO, dataPermissionsVO);
    }

    private void combineLocationMap(Set<String> singleDimensionValues, PriceDataPermissionsVO singlePermissionsVO,
                                    PriceDataPermissionsVO dataPermissionsVO) {
        if (CollectionUtils.isNotEmpty(singleDimensionValues)) {
            if (!singleDimensionValues.contains(ALL) && !singleDimensionValues.contains(ALL_CONDITION)) {
                // 获取bg维度
                lv1DimensionSet(singleDimensionValues, singlePermissionsVO.getLv1DimensionSet());
                lv2DimensionSet(singleDimensionValues, singlePermissionsVO.getLv2DimensionSet());
            }
        }
        Map<String, Map<String, Set<String>>> priceLocationMap = dataPermissionsVO.getLocationMap();
        Set<String> overseaDimensionSet = singlePermissionsVO.getOverseaDimensionSet();
        overseaDimensionSet.removeIf(model -> model.equals(NO_PERMISSION));
        Set<String> bgDimensionSet = singlePermissionsVO.getLv1DimensionSet();
        if (bgDimensionSet.contains(NO_PERMISSION) || bgDimensionSet.size() == 0) {
            CommonPriceBaseVO commonPriceBaseVO = new CommonPriceBaseVO();
            commonPriceBaseVO.setLv2DimensionSet(singlePermissionsVO.getLv2DimensionSet());
            commonPriceBaseVO.setVersionId(commonService.getVersionId("ANNUAL"));
            List<DmFcstPriceDimInfoVO> bgCodePermissionList = dmFcstPriceDimInfoDao.getBgCodePermissionList(commonPriceBaseVO);
            bgDimensionSet = bgCodePermissionList.stream().map(DmFcstPriceDimInfoVO::getBgCode).collect(Collectors.toSet());
            bgDimensionSet.removeIf(model -> model.equals("GR"));
        }
        Set<String> regionCodeDimensionSet = singlePermissionsVO.getRegionCodeDimensionSet();
        regionCodeDimensionSet.removeIf(model -> model.equals(NO_PERMISSION));
        for (String overseaFlag : overseaDimensionSet) {
            if (priceLocationMap.containsKey(overseaFlag)) {
                Map<String, Set<String>> bgDimensionMap = priceLocationMap.get(overseaFlag);
                for (String bg : bgDimensionSet) {
                    if (bgDimensionMap.containsKey(bg)) {
                        Set<String> regionSet = bgDimensionMap.get(bg);
                        regionSet.addAll(regionCodeDimensionSet);
                        bgDimensionMap.put(bg, regionSet);
                    } else {
                        bgDimensionMap.put(bg, regionCodeDimensionSet);
                    }
                }
            } else {
                Map<String, Set<String>> bgMap = new HashMap<>();
                for (String bg : bgDimensionSet) {
                    bgMap.put(bg, regionCodeDimensionSet);
                }
                priceLocationMap.put(overseaFlag, bgMap);
            }
        }
        dataPermissionsVO.getLocationMap().putAll(priceLocationMap);
    }

    private void parsingLocationDimensionCode(PriceDataPermissionsVO dataPermissionsVO, Set<String> dimensionValues, PriceDataPermissionsVO singlePermissionsVO) {
        Set<String> overseaFlagValues = new HashSet<>();
        Set<String> regionValues = new HashSet<>();
        Set<String> repofficeCodeValues = new HashSet<>();
        dimensionValues.forEach(value -> {
            if (value.contains("_OVERSEA_PRICE")) {
                overseaFlagValues.add(value);
            }
            if (value.contains("_REGION_PRICE")) {
                regionValues.add(value);
            }
            if (value.contains("_REPOFFICE_PRICE")) {
                repofficeCodeValues.add(value);
            }
        });
        // 先解析地区部权限
        regionDimensionSet(regionValues, singlePermissionsVO);
        // 后解析国内/海外权限
        overseaFlagDimensionSet(overseaFlagValues, singlePermissionsVO);
        // 全选勾选后，子项默认就是ALL，大T和子网设置为空维度
        if (singlePermissionsVO.getOverseaDimensionSet().contains("G")) {
            dataPermissionsVO.getOverseaDimensionSet().clear();
            dataPermissionsVO.getRegionCodeDimensionSet().clear();
            dataPermissionsVO.getRegionCodeDimensionTrueSet().clear();
            dataPermissionsVO.getRepofficeCodeDimensionSet().clear();
        } else {
            // 解析代表处权限
            repofficeDimensionSet(repofficeCodeValues, singlePermissionsVO);
            dataPermissionsVO.getOverseaDimensionSet().addAll(singlePermissionsVO.getOverseaDimensionSet());
            dataPermissionsVO.getRegionCodeDimensionSet().addAll(singlePermissionsVO.getRegionCodeDimensionSet());
            dataPermissionsVO.getRegionCodeDimensionTrueSet().addAll(singlePermissionsVO.getRegionCodeDimensionTrueSet());
            dataPermissionsVO.getRepofficeCodeDimensionSet().addAll(singlePermissionsVO.getRepofficeCodeDimensionSet());
        }
    }

    private void signTopCustCategoryCodeSet(Set<String> dimensionValues, PriceDataPermissionsVO keyAndSubAccountPermissionsVO) {
        Set<String> signTopCustCategoryCodeSingleSet = new HashSet<>(dimensionValues.size());
        for (String signTopCustCategoryCode : dimensionValues) {
            signTopCustCategoryCodeSingleSet.add(signTopCustCategoryCode.split("_")[0]);
        }
        if (signTopCustCategoryCodeSingleSet.size() == 0) {
            signTopCustCategoryCodeSingleSet.add(NO_PERMISSION);
        }
        keyAndSubAccountPermissionsVO.getSignTopCustCategoryCodeSet().addAll(signTopCustCategoryCodeSingleSet);
    }

    private void signSubsidiaryCustcatgCodeSet(Set<String> dimensionValues, PriceDataPermissionsVO keyAndSubAccountPermissionsVO) {
        Set<String> signTopCustCategoryCodeSet = new HashSet<>(dimensionValues.size());
        Set<String> signSubsidiaryCustcatgCodeSingleSet = new HashSet<>(dimensionValues.size());
        for (String signSubsidiaryCustcatgCode : dimensionValues) {
            signSubsidiaryCustcatgCodeSingleSet.add(signSubsidiaryCustcatgCode.split("_")[0]);
            signTopCustCategoryCodeSet.add(signSubsidiaryCustcatgCode.split("_")[1]);
        }
        if (signSubsidiaryCustcatgCodeSingleSet.size() == 0) {
            signSubsidiaryCustcatgCodeSingleSet.add(NO_PERMISSION);
        }
        keyAndSubAccountPermissionsVO.getSignSubsidiaryCustcatgCodeSet().addAll(signSubsidiaryCustcatgCodeSingleSet);
        keyAndSubAccountPermissionsVO.getSignTopCustCategoryCodeSet().addAll(signTopCustCategoryCodeSet);
    }

    private void repofficeDimensionSet(Set<String> dimensionValues, PriceDataPermissionsVO singlePermissionsVO) {
        Set<String> regionCodeSet = new HashSet<>(dimensionValues.size());
        Set<String> overseaFlagSet = new HashSet<>(dimensionValues.size());
        Set<String> repofficeSingleSet = new HashSet<>(dimensionValues.size());
        for (String repoffice : dimensionValues) {
            repofficeSingleSet.add(repoffice.split("_")[0]);
            regionCodeSet.add(repoffice.split("_")[1]);
            overseaFlagSet.add(repoffice.split("_")[2]);
        }
        if (repofficeSingleSet.size() == 0) {
            repofficeSingleSet.add(NO_PERMISSION);
        }
        singlePermissionsVO.getRepofficeCodeDimensionSet().addAll(repofficeSingleSet);
        singlePermissionsVO.getRegionCodeDimensionSet().addAll(regionCodeSet);
        singlePermissionsVO.getOverseaDimensionSet().addAll(overseaFlagSet);
    }

    private void regionDimensionSet(Set<String> dimensionValues, PriceDataPermissionsVO singlePermissionsVO) {
        Set<String> overseaFlagSet = new HashSet<>(dimensionValues.size());
        Set<String> regionCodeSingleSet = new HashSet<>(dimensionValues.size());
        for (String region : dimensionValues) {
            regionCodeSingleSet.add(region.split("_")[0]);
            overseaFlagSet.add(region.split("_")[1]);
        }
        if (regionCodeSingleSet.size() == 0) {
            regionCodeSingleSet.add(NO_PERMISSION);
        }
        singlePermissionsVO.getOverseaDimensionSet().addAll(overseaFlagSet);
        singlePermissionsVO.getRegionCodeDimensionSet().addAll(regionCodeSingleSet);
        singlePermissionsVO.getRegionCodeDimensionTrueSet().addAll(regionCodeSingleSet);
    }

    private void overseaFlagDimensionSet(Set<String> dimensionValues, PriceDataPermissionsVO singlePermissionsVO) {
        Set<String> overseaFlagSet = new HashSet<>(dimensionValues.size());
        dimensionValues.stream().forEach(overseaFlag -> overseaFlagSet.add(overseaFlag.split("_")[0]));
        // 通过申请的地区维度权限和数据库的对比，包含所有的就用全球的权限
        List<DmFcstPriceDimInfoVO> regionCodeList = dmFcstPriceDimInfoDao.getPermissionRegionCodeList(null);
        CommonPriceBaseVO commonPriceBaseVO = new CommonPriceBaseVO();
        commonPriceBaseVO.setRegionCodeDimensionSet(singlePermissionsVO.getRegionCodeDimensionSet());
        List<DmFcstPriceDimInfoVO> permissionRegionCodeList = dmFcstPriceDimInfoDao.getPermissionRegionCodeList(commonPriceBaseVO);
        if (permissionRegionCodeList.containsAll(regionCodeList)) {
            overseaFlagSet.add("G");
        } else {
            if (overseaFlagSet.size() == 0) {
                overseaFlagSet.add(NO_PERMISSION);
            }
        }
        singlePermissionsVO.getOverseaDimensionSet().addAll(overseaFlagSet);
    }

    private void setAllDimensionPermission(PriceDataPermissionsVO dataPermissionsVO, Set<String> dimensionValues) {
        if (CollectionUtils.isNotEmpty(dimensionValues)) {
            if (!dimensionValues.contains(ALL) && !dimensionValues.contains(ALL_CONDITION)) {
                // 解析维度code编码
                parsingDimensionCode(dataPermissionsVO, dimensionValues);
            }
        } else {
            dataPermissionsVO.getLv0DimensionSet().add(NO_PERMISSION);
            dataPermissionsVO.getLv1DimensionSet().add(NO_PERMISSION);
            dataPermissionsVO.getLv2DimensionSet().add(NO_PERMISSION);
        }
    }

    private void parsingDimensionCode(PriceDataPermissionsVO dataPermissionsVO, Set<String> dimensionValues) {
        Set<String> lv0DimensionSet = dataPermissionsVO.getLv0DimensionSet();
        Set<String> lv1DimensionSet = dataPermissionsVO.getLv1DimensionSet();
        Set<String> lv2DimensionSet = dataPermissionsVO.getLv2DimensionSet();
        lv0DimensionSet(dimensionValues, lv0DimensionSet);
        lv1DimensionSet(dimensionValues, lv1DimensionSet);
        lv2DimensionSet(dimensionValues, lv2DimensionSet);
    }

    private void lv2DimensionSet(Set<String> dimensionValues, Set<String> lv2DimensionSet) {
        for (String lv2ProdCode : dimensionValues) {
            if (lv2ProdCode.contains("_L2")) {
                lv2DimensionSet.add(lv2ProdCode.split("_")[0]);
            }
        }
        if (lv2DimensionSet.size() == 0) {
            lv2DimensionSet.add(NO_PERMISSION);
        }
    }

    private void lv1DimensionSet(Set<String> dimensionValues, Set<String> lv1DimensionSet) {
        for (String lv1ProdCode : dimensionValues) {
            if (lv1ProdCode.contains("_L1")) {
                lv1DimensionSet.add(lv1ProdCode.split("_")[0]);
            }
        }
        if (lv1DimensionSet.size() == 0) {
            lv1DimensionSet.add(NO_PERMISSION);
        }
    }

    private void lv0DimensionSet(Set<String> dimensionValues, Set<String> lv0DimensionSet) {
        for (String lv0ProdCode : dimensionValues) {
            if (lv0ProdCode.contains("_L0")) {
                lv0DimensionSet.add(lv0ProdCode.split("_")[0]);
            }
        }
        if (lv0DimensionSet.size() == 0) {
            lv0DimensionSet.add(NO_PERMISSION);
        }
    }

    /**
     *
     * @return ResultDataVO
     */

    @Override
    @JalorOperation(code = "getlv0Permission", desc = "校验LV0层级权限")
    public ResultDataVO getlv0Permission(CommonPriceBaseVO commonBaseVO) {
        commonBaseVO.setVersionId(commonService.getVersionId("ANNUAL"));
        List<DmFcstPriceDimInfoVO> allProdDimensionList = dmFcstPriceDimInfoDao.getLv1CodeList(commonBaseVO);
        PriceDataPermissionsVO dataPermissionsVO = getCurrentRoleDataPermission();
        Set<String> lv2DimensionSet = dataPermissionsVO.getLv2DimensionSet();
        Set<String> lv1CodeList =
                allProdDimensionList.stream().filter(item -> commonBaseVO.getLv0ProdListCode().equals(item.getLv0ProdListCode())).map(DmFcstPriceDimInfoVO::getLv1ProdListCode).collect(Collectors.toSet());
        Boolean hasAllLv0Permission = false;
        if (lv2DimensionSet.containsAll(lv1CodeList) || CollectionUtils.isEmpty(lv2DimensionSet)) {
            hasAllLv0Permission = true;
        }
        return ResultDataVO.success(hasAllLv0Permission);
    }


    public void setUserPermission(CommonPriceBaseVO commonPriceBaseVO) {
        PriceDataPermissionsVO currentRoleDataPermission = getCurrentRoleDataPermission();
        commonPriceBaseVO.setLv1DimensionSet(currentRoleDataPermission.getLv1DimensionSet());
        commonPriceBaseVO.setLv2DimensionSet(currentRoleDataPermission.getLv2DimensionSet());
    }
}
