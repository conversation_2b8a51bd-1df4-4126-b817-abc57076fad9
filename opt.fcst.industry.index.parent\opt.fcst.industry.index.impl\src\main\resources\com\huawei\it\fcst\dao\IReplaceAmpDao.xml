<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IReplaceAmpDao">
    <resultMap type="com.huawei.it.fcst.industry.index.vo.annual.DmFocAnnualAmpVO" id="annualResultMap">
        <result property="id" column="id"/>
        <result property="versionId" column="version_id"/>
        <result property="periodYear" column="period_year"/>
        <result property="lv0ProdRndTeamCode" column="lv0_prod_rnd_team_code"/>
        <result property="prodRndTeamCode" column="prod_rnd_team_code"/>
        <result property="prodRndTeamCnName" column="prod_rnd_team_cn_name"/>
        <result property="parentLevel" column="parent_level"/>
        <result property="parentCode" column="parent_code"/>
        <result property="parentCnName" column="parent_cn_name"/>
        <result property="groupCode" column="group_code"/>
        <result property="groupCnName" column="group_cn_name"/>
        <result property="groupLevel" column="group_level"/>
        <result property="annualAmp" column="annual_amp"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDdate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="appendFlag" column="append_flag"/>
        <result property="weightRate" column="weight_rate"/>
        <result property="statusCode" column="status_code"/>
        <result property="groupCodeAndName" column="groupCodeAndName"/>
        <result property="weightAnnualAmpPercent" column="weightAnnualAmpPercent"/>
        <result property="appendYear" column="append_year"/>
        <result property="dimensionCode" column="dimension_code"/>
        <result property="dimensionCnName" column="dimension_cn_name"/>
        <result property="dimensionSubCategoryCode" column="dimension_subcategory_code"/>
        <result property="dimensionSubCategoryCnName" column="dimension_subcategory_cn_name"/>
        <result property="dimensionSubDetailCode" column="dimension_sub_detail_code"/>
        <result property="dimensionSubDetailCnName" column="dimension_sub_detail_cn_name"/>
        <result property="customCnName" column="custom_cn_name"/>
        <result property="customId" column="custom_id"/>
        <result property="rmbCostAmt" column="rmb_cost_amt"/>
        <result property="dataType" column="data_type"/>
    </resultMap>

    <select id="allReplaceAmpCost" resultMap="annualResultMap">
        select distinct
        amp.group_cn_name,amp.period_year,amp.group_level,amp.group_code,
        CONCAT(ROUND(amp.annual_amp*100,1),'%') as annual_amp,
        status.append_year,status.status_code,amp.parent_cn_name,amp.parent_code,
        <if test='isMultipleSelect == false'>
            weight.weight_rate*100 as weight_rate
        </if>
        <if test='isMultipleSelect == true'>
            weight.absolute_weight*100 as weight_rate
        </if>
        from fin_dm_opt_foi.dm_foc_repl_annl_amp_t amp
        left join fin_dm_opt_foi.dm_foc_repl_annl_weight_t weight
        on amp.view_flag = weight.view_flag and amp.group_code = weight.group_code
        and amp.group_level = weight.group_level
        and amp.parent_code = weight.parent_code
        and amp.version_id =weight.version_id and amp.period_year = weight.period_year
        and amp.caliber_flag = weight.caliber_flag
        left join fin_dm_opt_foi.dm_foc_repl_annl_status_t status
        on amp.group_code = status.group_code and amp.group_level = status.group_level
        and amp.period_year = status.period_year and amp.version_id = status.version_id
        and amp.view_flag = status.view_flag
        and amp.caliber_flag = status.caliber_flag
        and amp.parent_code = status.parent_code
        and amp.data_type = status.data_type
        where amp.del_flag = 'N' and amp.data_type= 'TOTAL'
        <if test='groupLevel != null and groupLevel != ""'>
            and amp.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and amp.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='groupCodeList != null and groupCodeList.size() > 0'>
            <foreach collection='groupCodeList' item="code" open="AND amp.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='prodRndTeamCodeList != null and prodRndTeamCodeList.size() > 0'>
            <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='periodYear != null and periodYear != ""'>
            and amp.period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and amp.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='caliberFlag != null and caliberFlag != ""'>
            and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND amp.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        <include refid="replaceUserPermission"></include>
        order by amp.period_year,weight_rate desc
    </select>

    <sql id ="replaceUserPermission">
        <if test='groupLevel =="LV0" and lv0DimensionSet != null and lv0DimensionSet.size() > 0'>
            <foreach collection='lv0DimensionSet' item="code" open="AND amp.group_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel =="LV1" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND amp.group_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel =="LV2" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND amp.group_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
    </sql>

    <select id="getAnnualPeriodYear" resultType="java.lang.String">
        select distinct period_year
        from fin_dm_opt_foi.dm_foc_repl_annl_amp_t where version_id = #{versionId,jdbcType=NUMERIC}
        order by period_year desc limit 3
    </select>

    <select id="industryReplaceCostList" resultMap="annualResultMap">
        select parent_code,parent_cn_name,period_year,annual_amp,group_code,group_level,group_cn_name,data_type,
        weight_rate, status_code, annual_amp * weight_rate/100 as weightAnnualAmpPercent, append_year,
        append_flag from
        (select distinct amp.group_level,
        amp.parent_code,amp.parent_cn_name,amp.period_year,
        ROUND(amp.annual_amp*100,1) as annual_amp,amp.group_code,
        amp.group_cn_name,amp.data_type,
        <choose>
            <when test='isMultipleSelect == true'>
                weight.absolute_weight*100 as weight_rate,
            </when>
            <otherwise>
                weight.weight_rate*100 as weight_rate,
            </otherwise>
        </choose>
        status.status_code as status_code,weight.append_flag, status.append_year
        from fin_dm_opt_foi.dm_foc_repl_annl_amp_t amp
        left join fin_dm_opt_foi.dm_foc_repl_annl_weight_t weight
        on amp.view_flag = weight.view_flag and amp.group_code = weight.group_code
        and amp.group_level = weight.group_level
        and amp.parent_code = weight.parent_code
        and amp.caliber_flag = weight.caliber_flag
        and amp.version_id =weight.version_id and amp.period_year = weight.period_year
        left join fin_dm_opt_foi.dm_foc_repl_annl_status_t status
        on amp.group_code = status.group_code and amp.group_level = status.group_level
        and amp.period_year = status.period_year and amp.version_id = status.version_id
        and amp.view_flag = status.view_flag
        and amp.caliber_flag = status.caliber_flag
        and amp.parent_code = status.parent_code
        and amp.data_type = status.data_type
        where amp.del_flag = 'N'
        <if test='groupLevel != null and groupLevel != ""'>
            and amp.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and amp.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='caliberFlag != null and caliberFlag != ""'>
            and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and amp.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='periodYear != null and periodYear !=""'>
            and amp.period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='dataType != null and dataType !=""'>
            and amp.data_type = #{dataType,jdbcType=VARCHAR}
        </if>
        <if test='year != null and year !=""'>
            and amp.period_year = #{year,jdbcType=VARCHAR}
        </if>
        <if test='prodRndTeamCodeList != null and prodRndTeamCodeList.size() > 0'>
            <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='parentCodeList != null and parentCodeList.size() > 0'>
            <foreach collection='parentCodeList' item="code" open="AND amp.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupCodeList != null and groupCodeList.size() > 0'>
            <foreach collection='groupCodeList' item="code" open="AND amp.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND amp.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        <include refid="replaceUserPermission"></include>
        )
        <choose>
            <when test='groupLevel == "BIND"'>
                order by period_year,weight_rate desc,group_cn_name
            </when>
            <otherwise>
                order by period_year,annual_amp desc,group_cn_name
            </otherwise>
        </choose>
    </select>

</mapper>