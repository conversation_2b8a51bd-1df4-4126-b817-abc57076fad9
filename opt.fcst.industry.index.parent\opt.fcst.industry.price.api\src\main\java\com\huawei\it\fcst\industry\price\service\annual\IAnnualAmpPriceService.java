/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.service.annual;

import com.huawei.it.fcst.industry.price.vo.annual.AnnualAnalysisVO;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import io.swagger.annotations.Api;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;

/**
 * IAnnualAmpPriceService Class
 *
 * <AUTHOR>
 * @since 2024/11/6
 */
@Path("/annual")
@Api(value = "定价指数-年度分析页面")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface IAnnualAmpPriceService {
    /**
     * 查询当前层级涨跌幅
     *
     * @param annualAnalysisVO 参数
     * @return 结果
     */
    @Path("/allIndustryPriceCost")
    @POST
    ResultDataVO allIndustryPriceCost(AnnualAnalysisVO annualAnalysisVO) throws CommonApplicationException;

    /**
     * 查询当前层级的子项涨跌幅
     *
     * @param annualAnalysisVO 参数
     * @return 结果
     */
    @Path("/multiIndustryPriceChart")
    @POST
    ResultDataVO multiIndustryPbiCostChart(AnnualAnalysisVO annualAnalysisVO) throws CommonApplicationException;

    /**
     * 查询当前层级的子项涨跌幅和权重
     *
     * @param annualAnalysisVO 参数
     * @return 结果
     */
    @Path("/industryPriceList")
    @POST
    ResultDataVO industryCostList(AnnualAnalysisVO annualAnalysisVO) throws CommonApplicationException;

    @Path("/periodYearList")
    @POST
    ResultDataVO periodYearList(AnnualAnalysisVO annualAnalysisVO) throws CommonApplicationException;

    @POST
    @Path("/detail/export")
    ResultDataVO exportDetail(@Context HttpServletResponse response, AnnualAnalysisVO annualAnalysisVO) throws ApplicationException;
}
