/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.impl.provider.annual;

import com.huawei.it.fcst.export.IExcelExportDataProvider;
import com.huawei.it.fcst.export.vo.ExcelExportContext;
import com.huawei.it.fcst.industry.price.dao.IAnnualAmpCustomDao;
import com.huawei.it.fcst.industry.price.dao.IAnnualAmpPriceDao;
import com.huawei.it.fcst.industry.price.enums.GroupLevelEnum;
import com.huawei.it.fcst.industry.price.impl.annual.AnnualAmpPriceService;
import com.huawei.it.fcst.industry.price.utils.FcstIndustryUtil;
import com.huawei.it.fcst.industry.price.vo.annual.AnnualAnalysisVO;
import com.huawei.it.fcst.industry.price.vo.annual.DmFocAnnualAmpVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;

import javax.inject.Inject;
import javax.inject.Named;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * AnnualViewExportDataProvider Class 导出 成本涨跌一览表
 *
 * <AUTHOR>
 * @since 2024/7/5
 */
@Named("IExcelExport.AnnualViewExportProvider")
public class AnnualViewExportDataProvider implements IExcelExportDataProvider {

    @Inject
    private IAnnualAmpCustomDao annualAmpCustomDao;

    @Inject
    private IAnnualAmpPriceDao annualAmpPriceDao;

    @Inject
    private AnnualAmpPriceService annualAmpPriceService;

    @Override
    public List<?> getData(Serializable conditionObject, PageVO pageVO) throws CommonApplicationException {

        AnnualAnalysisVO annualVO = (AnnualAnalysisVO) conditionObject;
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        BeanUtils.copyProperties(annualVO, annualAnalysisVO);
        annualAnalysisVO.setParentCodeList(annualAnalysisVO.getGroupCodeList());
        annualAnalysisVO.setParentLevel(annualAnalysisVO.getGroupLevel());

        String nextGroupLevel = FcstIndustryUtil.getViewNextGroupLevel(annualAnalysisVO);
        annualAnalysisVO.setGroupLevel(nextGroupLevel);
        List<String> yearList = annualAnalysisVO.getYearList();
        annualAnalysisVO.setLastYear(yearList.get(0));
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();

        List<DmFocAnnualAmpVO> dmFocAnnualAmpList = new ArrayList<>();
        if (annualAnalysisVO.getIsNeedBlur()) {
            // 走虚化且是最细力度的时候，需要取正常结果表数据
            dmFocAnnualAmpVOList.addAll(annualAmpCustomDao.industryAmpMinLevelList(annualAnalysisVO));
        }
        if (!GroupLevelEnum.SPART.getValue().equals(annualAnalysisVO.getParentLevel()) && CollectionUtils.isNotEmpty(annualAnalysisVO.getParentCodeList())) {
            dmFocAnnualAmpVOList.addAll(annualAmpPriceDao.industryNormalCostList(annualAnalysisVO));
        }

        List<String> threeYearList = annualAnalysisVO.getYearList().stream().sorted(Comparator.comparing(String::valueOf)).collect(Collectors.toList());
        for (String year : threeYearList) {
            annualAnalysisVO.setPeriodYear(year);
            // 筛选出对应年份
            List<DmFocAnnualAmpVO> annualAmpAndWeightYear = dmFocAnnualAmpVOList.stream().filter(annual -> annualAnalysisVO.getPeriodYear().equals(annual.getPeriodYear())).collect(Collectors.toList());
            // 设置权重*涨跌序号
            List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = annualAmpPriceService.dealGroupLevelAmp(annualAmpAndWeightYear, annualAnalysisVO, false, null);
            // 设置无效的涨跌幅提示语
            annualAmpPriceService.setNoEffectiveAmp(dmFocAnnualAmpVOResult, annualAnalysisVO, "excel");
            dmFocAnnualAmpList.addAll(dmFocAnnualAmpVOResult);
        }
        return dmFocAnnualAmpList;
    }

    @Override
    public Map<String, Object> getHeader(Serializable conditionObject, Map<String, Object> parameters) throws ApplicationException {
        ExcelExportContext context = (ExcelExportContext) conditionObject;
        AnnualAnalysisVO annualAnalysisVO = (AnnualAnalysisVO) context.getConditionObject();
        // 自定义表头
        Map<String, Object> viewHeadMap = new HashMap<>();
        viewHeadMap.put("displayName", annualAnalysisVO.getDisplayName());
        viewHeadMap.put("name", annualAnalysisVO.getNextGroupName());
        viewHeadMap.put("overseaFlagCnName", annualAnalysisVO.getOverseaFlagCnName());
        viewHeadMap.put("bgCnName", annualAnalysisVO.getBgCnName());
        viewHeadMap.put("actualMonth", annualAnalysisVO.getActualMonth());
        viewHeadMap.put("regionCnName", annualAnalysisVO.getRegionCnName());
        viewHeadMap.put("repofficeCnName", annualAnalysisVO.getRepofficeCnName());
        viewHeadMap.put("signTopCustCategoryCnName", annualAnalysisVO.getSignTopCustCategoryCnName());
        viewHeadMap.put("signSubsidiaryCustcatgCnName", annualAnalysisVO.getSignSubsidiaryCustcatgCnName());
        return viewHeadMap;
    }
}
