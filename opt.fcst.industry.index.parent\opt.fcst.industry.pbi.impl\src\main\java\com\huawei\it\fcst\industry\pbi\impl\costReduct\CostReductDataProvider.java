/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.costReduct;

import com.huawei.it.fcst.industry.pbi.dao.ICostReductDao;
import com.huawei.it.fcst.export.IExcelExportDataProvider;
import com.huawei.it.fcst.industry.pbi.vo.config.CostReductVO;
import com.huawei.it.fcst.export.vo.ExcelExportContext;
import com.huawei.it.fcst.export.vo.ExportList;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.inject.Inject;
import javax.inject.Named;

/**
 * 导出服务-降成本
 */
@Named("IExcelExport.CostReductDataProvider")
public class CostReductDataProvider implements IExcelExportDataProvider {
    @Inject
    private ICostReductDao iCostReductDao;

    // 普通查询
    @Override
    public List<?> getData(Serializable conditionObject, PageVO pageVO) {
        CostReductVO costReductVO = (CostReductVO) conditionObject;
        PagedResult<CostReductVO> costReductExpByPage = iCostReductDao.findCostReductExpByPage(costReductVO, pageVO);
        ExportList  list = new ExportList();
        list.addAll(costReductExpByPage.getResult());
        list.setTotalRows(costReductExpByPage.getPageVO().getTotalRows());
        return list;
    }

    @Override
    public Map<String, Object> getHeader(Serializable conditionObject, Map<String, Object> parameters) {
        ExcelExportContext context = (ExcelExportContext) conditionObject;
        CostReductVO costReductVO = (CostReductVO) context.getConditionObject();
        // 自定义表头
        Map<String, Object> headMap = new HashMap<>();
        headMap.put("version",iCostReductDao.getVersionName(costReductVO));
        return headMap;
    }
}
