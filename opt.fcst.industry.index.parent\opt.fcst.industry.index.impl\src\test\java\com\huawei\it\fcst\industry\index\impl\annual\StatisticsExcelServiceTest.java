/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.annual;

import static org.mockito.ArgumentMatchers.any;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.when;

import com.huawei.it.fcst.industry.index.utils.ExcelExportUtil;
import com.huawei.it.fcst.industry.index.utils.FileProcessUtis;
import com.huawei.it.fcst.industry.index.vo.common.DmFoiImpExpRecordVO;
import com.huawei.it.fcst.industry.index.vo.common.UploadInfoVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;


import com.huawei.it.jalor5.security.UserVO;
import org.apache.poi.ss.usermodel.Workbook;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.core.io.ClassPathResource;

import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * StatisticsExcelServiceTest Class
 *
 * <AUTHOR>
 * @since 2023/4/13
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest( {FileProcessUtis.class})
public class StatisticsExcelServiceTest {

    @InjectMocks
    private StatisticsExcelService statisticsExcelService;

    @Mock
    private PersonalCenterService personalCenterService;

    @Before
    public void before() throws Exception {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);
    }

    @Test
    public void uploadExportExcel() throws Exception {
        Workbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2.xlsx");
        int number = 5;
        String fileName = "全品类清单";
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        DmFoiImpExpRecordVO dmFoiImpExpRecordVO = statisticsExcelService.uploadExportExcel(workbook, number, fileName,0L);
        Assert.assertNotNull(dmFoiImpExpRecordVO);
    }

    @Test
    public void uploadExportExcelNoFile() throws Exception {
        Workbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2.xlsx");
        int number = 5;
        String fileName="";
        DmFoiImpExpRecordVO dmFoiImpExpRecordVO = statisticsExcelService.uploadExportExcel(workbook, number, fileName,0L);
        Assert.assertNull(dmFoiImpExpRecordVO);
    }

    @Test
    public void insertExportExcelRecord() {
        DmFoiImpExpRecordVO dmFoiImpExpRecordVO = new DmFoiImpExpRecordVO();
        dmFoiImpExpRecordVO.setModuleType("成本指数-产业-ICT-年度分析");
        statisticsExcelService.insertExportExcelRecord(dmFoiImpExpRecordVO);
        Assert.assertNull(null);
    }

    @Test
    public void insertMonthExportRecord() {
        DmFoiImpExpRecordVO dmFoiImpExpRecordVO = new DmFoiImpExpRecordVO();
        dmFoiImpExpRecordVO.setModuleType("成本指数-产业-ICT-年度分析");
        statisticsExcelService.insertMonthExportRecord(dmFoiImpExpRecordVO);
        Assert.assertNull(null);
    }

    @Test
    public void insertImportExcel() throws CommonApplicationException {
        DmFoiImpExpRecordVO dmFoiImpExpRecordVO = new DmFoiImpExpRecordVO();
        UploadInfoVO uploadInfoVO = new UploadInfoVO();
        uploadInfoVO.setUserId(11L);
        Map<String, Object> params = new HashMap<>();
        params.put("module", "成本指数-产业-ICT-映射维表");
        uploadInfoVO.setParams(params);
        boolean flag = true;
        Integer recordNum = 2;
        statisticsExcelService.insertImportExcel(dmFoiImpExpRecordVO, uploadInfoVO, flag, recordNum);
        Assert.assertNull(null);
    }

    @Test
    public void insertImportExcel2Test() throws CommonApplicationException {
        DmFoiImpExpRecordVO dmFoiImpExpRecordVO = new DmFoiImpExpRecordVO();
        UploadInfoVO uploadInfoVO = new UploadInfoVO();
        uploadInfoVO.setUserId(11L);
        Map<String, Object> params = new HashMap<>();
        params.put("module", "成本指数-产业-ICT-映射维表");
        uploadInfoVO.setParams(params);
        Integer recordNum = 3;
        statisticsExcelService.insertImportExcel(dmFoiImpExpRecordVO, uploadInfoVO, false, recordNum);
        Assert.assertNull(null);
    }

    @Test
    public void getImportUploadFileKey() throws Exception {
        DmFoiImpExpRecordVO dmFoiImpExpRecordVO = new DmFoiImpExpRecordVO();
        Long userId = 111L;
        int type = 1;
        ClassPathResource classPathResource = new ClassPathResource("excel.export.template/UTtestTemplate1.xlsx");
        InputStream inputStream = classPathResource.getInputStream();
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        statisticsExcelService.getImportUploadFileKey(dmFoiImpExpRecordVO, userId, type, inputStream);
        Assert.assertNull(null);
    }

    @Test
    public void getImportUploadFileKey2Test() throws Exception {
        DmFoiImpExpRecordVO dmFoiImpExpRecordVO = new DmFoiImpExpRecordVO();
        Long userId = 111L;
        int type = 2;
        ClassPathResource classPathResource = new ClassPathResource("excel.export.template/UTtestTemplate1.xlsx");
        InputStream inputStream = classPathResource.getInputStream();
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        statisticsExcelService.getImportUploadFileKey(dmFoiImpExpRecordVO, userId, type, inputStream);
        Assert.assertNull(null);
    }

    @Test
    public void getImportUploadFileKey3Test() throws Exception {
        DmFoiImpExpRecordVO dmFoiImpExpRecordVO = new DmFoiImpExpRecordVO();
        Long userId = 111L;
        int type = 3;
        ClassPathResource classPathResource = new ClassPathResource("excel.export.template/UTtestTemplate1.xlsx");
        InputStream inputStream = classPathResource.getInputStream();
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        statisticsExcelService.getImportUploadFileKey(dmFoiImpExpRecordVO, userId, type, inputStream);
        Assert.assertNull(null);
    }

    @Test
    public void insertFailExportExcelRecord() {
        DmFoiImpExpRecordVO dmFoiImpExpRecordVO = new DmFoiImpExpRecordVO();
        int fileSize = 10;
        int number = 2;
        String moduleType = "成本指数-产业-ICT-全品类清单";
        statisticsExcelService.insertFailExportExcelRecord(dmFoiImpExpRecordVO, fileSize, number, moduleType);
        Assert.assertNull(null);
    }

}