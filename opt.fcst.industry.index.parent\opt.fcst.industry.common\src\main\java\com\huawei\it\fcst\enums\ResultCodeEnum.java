/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.enums;

/**
 * 返回响应码实体
 *
 * <AUTHOR>
 * @since 2022-09-26
 */
public enum ResultCodeEnum {
    PARAM_ERROR("400", "参数错误！"),
    SUCCESS("200", "操作成功！"),
    BIG_DATA("202", "操作的数据过多！"),
    NO_DATA("204", "暂无数据！"),
    NOT_PERMISSION("401", "未授权，无法访问！"),
    VERSION_PARAM_ERROR("402", "参数错误！");

    private String code;
    private String message;

    ResultCodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}