/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.service.config;

import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.config.ManufactureBottomVO;
import com.huawei.it.fcst.industry.index.vo.config.ProcurementBottomVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import io.swagger.annotations.Api;

import java.util.List;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;

/**
 * IConfigBottomReview Class
 *
 * <AUTHOR>
 * @since 2023/12/7
 */
@Path("/configBottom")
@Api(value = "配置管理底层数据审视服务")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface IConfigBottomReviewService {
    /**
     * [采购成本-底层数据审视下拉框接口]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @Path("/procurement/dropDownBox")
    @POST
    ResultDataVO getProcurementDropDown(ProcurementBottomVO procurementBottomVO) throws CommonApplicationException;

    /**
     * [采购成本-异常数据录入表单下拉框接口]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @Path("/procurement/formDropDown")
    @POST
    ResultDataVO getProFormDropDown(ProcurementBottomVO procurementBottomVO) throws CommonApplicationException;

    /**
     * [采购成本-异常数据录入表单下拉框接口]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @Path("/procurement/itemImpactQty")
    @GET
    ResultDataVO getImpactQty(@QueryParam("id") Long id,@QueryParam("industryOrg") String industryOrg) throws CommonApplicationException;

    /**
     * [采购成本-底层数据审视保存接口]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @Path("/procurement/save")
    @POST
    ResultDataVO saveProModify(List<ProcurementBottomVO> procurementVOList) throws Exception;

    /**
     * [采购成本-底层数据审视撤销接口]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @Path("/procurement/revoke")
    @POST
    ResultDataVO revokeProModify(List<ProcurementBottomVO> procurementVOList) throws CommonApplicationException;

    /**
     * [采购成本-底层数据审视导出接口]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @Path("/procurement/export")
    @POST
    ResultDataVO exportProcurement(ProcurementBottomVO procurementBottomVO,@Context HttpServletResponse response) throws Exception;

    /**
     * [制造成本-底层数据审视下拉框接口]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @Path("/manufacture/dropDownBox")
    @POST
    ResultDataVO getManufactureDropDown(ManufactureBottomVO manufactureBottomVO) throws CommonApplicationException;

    /**
     * [制造成本-底层数据审视表单下拉框接口]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @Path("/manufacture/formDropDown")
    @POST
    ResultDataVO getMftFormDropDown(ManufactureBottomVO manufactureBottomVO) throws CommonApplicationException;

    /**
     * [制造成本-底层数据审视保存接口]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @Path("/manufacture/save")
    @POST
    ResultDataVO saveManufactureModify(List<ManufactureBottomVO> manufactureVOList) throws Exception;

    /**
     * [制造成本-底层数据审视撤销接口]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @Path("/manufacture/revoke")
    @POST
    ResultDataVO revokeManufactureModify(List<ManufactureBottomVO> manufactureVOList) throws CommonApplicationException;

    /**
     * [制造成本-底层数据审视导出接口]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @Path("/manufacture/export")
    @POST
    ResultDataVO exportManufacture(ManufactureBottomVO manufactureBottomVO,@Context HttpServletResponse response) throws CommonApplicationException;


    /**
     * 采购成本-ITEM异常数据录入查询接口&&历史修改记录查询
     *
     * @return ResultDataVO
     */
    @POST
    @Path("/getProModifyListByPage")
    ResultDataVO getProModifyListByPage(ProcurementBottomVO procurementBottomVO) throws CommonApplicationException;

    /**
     * 制造成本-ITEM异常数据录入查询接口&&历史修改记录查询
     *
     * @return ResultDataVO
     */
    @POST
    @Path("/getMftModifyListByPage")
    ResultDataVO getMftModifyListByPage(ManufactureBottomVO manufactureBottomVO) throws CommonApplicationException;
}
