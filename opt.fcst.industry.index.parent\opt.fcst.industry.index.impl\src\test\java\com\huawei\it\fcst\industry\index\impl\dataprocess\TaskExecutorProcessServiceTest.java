/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.dataprocess;

import com.huawei.it.fcst.industry.index.dao.IDataCipherTextDao;
import com.huawei.it.fcst.config.ConfigUtil;
import com.huawei.it.fcst.industry.index.utils.UserInfoUtils;
import com.huawei.it.fcst.industry.index.vo.ciphertext.CipherTextDataVO;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;;
import com.huawei.it.jalor5.security.UserVO;
import com.huawei.it.soa.util.SoaAppTokenClientUtil;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Future;

/**
 * TaskExecutorProcessServiceTest Class
 *
 * <AUTHOR>
 * @since 2023/6/28
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest( {UserInfoUtils.class, ConfigUtil.class, SoaAppTokenClientUtil.class})
public class TaskExecutorProcessServiceTest {
    private static final Logger LOGGER = LogManager.getLogger(TaskExecutorProcessServiceTest.class);

    @InjectMocks
    private TaskExecutorProcessService taskExecutorProcessService;

    @Mock
    private IDataCipherTextDao iDataCipherTextDao;

    @Before
    public void before() throws Exception {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);
    }

    @Test
    public void process() throws Exception {
        List<CipherTextDataVO> dataEncrypt=new ArrayList<>();
        CipherTextDataVO cipherTextDataVO = new CipherTextDataVO();
        IRequestContext context=null;
        String keyStr="key";
        Date date=new Date();
        java.util.concurrent.Semaphore semaphore=null;

        Future<Object> process = null;
            try {
                process =taskExecutorProcessService.process(dataEncrypt, context, keyStr, date, semaphore,cipherTextDataVO.getTargetTableName());
            } catch (Exception e) {
                LOGGER.error("入参清单数据版本为空");
            }
        Assert.assertNull(process);
    }
}