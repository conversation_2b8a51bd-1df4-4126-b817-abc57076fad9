/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.config;

import com.fasterxml.jackson.annotation.JsonFormat;

import com.huawei.it.fcst.industry.index.vo.common.TableNameVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * ManufactureBottomVO Class
 *
 * <AUTHOR>
 * @since 2023/12/7
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "配置管理制造成本底层审视关系入参")
public class ManufactureBottomVO extends TableNameVO {
    /**
     * 版本号ID
     **/
    private Long versionId;

    /**
     *
     * 版本号
     */
    private String version;

    /**
     *
     * 版本类型
     */
    private String dataType;

    /**
     *
     * 发货对象编码
     **/
    private String shippingObjectCode;

    /**
     * 发货对象名称
     **/
    private String shippingObjectCnName;

    /**
     * 制造对象编码
     **/
    private String manufactureObjectCode;

    /**
     * 制造对象名称
     **/
    private String manufactureObjectCnName;

    /**
     * ITEM编码
     **/
    private String itemCode;

    /**
     * ITEM中文名称
     **/
    private String itemCnName;

    /**
     * item影响数量
     **/
    private String impactQty;

    /**
     * 起始期
     **/
    private Long startPeriod;

    /**
     * 终止期
     **/
    private Long endPeriod;

    /**
     * ITEM编码(修改前)
     **/
    private String oldItemCode;

    /**
     * 起始期(修改前)
     **/
    private Long oldStartPeriod;

    /**
     * 终止期(修改前)
     **/
    private Long oldEndPeriod;

    /**
     * 修改理由
     **/
    private String modifyReason;

    /**
     * 修改类型 (新增：INSERT,修改：MODIFY，撤销：REVOKE)
     **/
    private String modifyType;

    /**
     * 成本类型（P：采购，M：制造）
     **/
    private String costType;

    /**
     * 口径标识（C：发货，R：收入）
     **/
    private String caliberFlag;

    /**
     *  区分模型 M/R ITEM异常数据录入/历史修改记录
     */
    private String modelType;

    /**
     * 文件名
     */
    private String fileName;

    /**
     *
     *  页码
     */
    private Integer pageIndex;

    /**
     *
     *  一页数量
     */
    private Integer pageSize;

    /**
     * 影响ITEM数量均值汇总
     */
    private Integer crmAvgCnt;

    /**
     * 影响ITEM数量汇总
     */
    private Integer cnt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date creationDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "修改时间")
    private Date lastUpdateDate;

    @ApiModelProperty(value = "是否删除")
    private String delFlag;

    private String createdBy;

    private String lastUpdatedBy;

    private String pageFlag;
}
