/*
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.month;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

import java.sql.Timestamp;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/5/9
 */
public class DmFocViewInfoVOTest extends BaseVOCoverUtilsTest<DmFocViewInfoVO> {
    @Override
    protected Class<DmFocViewInfoVO> getTClass() { return DmFocViewInfoVO.class; }

    @Test
    public void testMethod() {
        DmFocViewInfoVO dmFocActualCostVO = new DmFocViewInfoVO();
        dmFocActualCostVO.setDelFlag("Y");
        dmFocActualCostVO.getDelFlag();
        dmFocActualCostVO.setCreatedBy("1175L");
        dmFocActualCostVO.getCreatedBy();
        dmFocActualCostVO.getCreationDate();
        dmFocActualCostVO.setVersionId(11L);
        dmFocActualCostVO.getVersionId();
        dmFocActualCostVO.getLastUpdateDate();
        dmFocActualCostVO.setLastUpdatedBy("166L");
        dmFocActualCostVO.getLastUpdatedBy();
        dmFocActualCostVO.setGroupLevel("LV3");
        dmFocActualCostVO.getGroupLevel();
        dmFocActualCostVO.setVersionId(100L);
        dmFocActualCostVO.getVersionId();
        dmFocActualCostVO.setL3CegCode("2331L");
        dmFocActualCostVO.getL3CegCode();
        dmFocActualCostVO.setL3CegCnName("2331L");
        dmFocActualCostVO.getL3CegCnName();
        dmFocActualCostVO.setDelFlag("Y");
        dmFocActualCostVO.getDelFlag();
        dmFocActualCostVO.setCategoryCode("111");
        dmFocActualCostVO.getCategoryCode();
        dmFocActualCostVO.setCategoryCnName("test");
        dmFocActualCostVO.getCategoryCnName();
        dmFocActualCostVO.setL3CegShortCnName("short");
        dmFocActualCostVO.getL3CegShortCnName();
        dmFocActualCostVO.getLastUpdateDate();
        dmFocActualCostVO.setLv0ProdRndTeamCode("L44V");
        dmFocActualCostVO.getLv0ProdRndTeamCode();
        dmFocActualCostVO.setLv0ProdRdTeamCnName("444");
        dmFocActualCostVO.getLv0ProdRdTeamCnName();
        dmFocActualCostVO.setLv1ProdRdTeamCnName("111");
        dmFocActualCostVO.getLv1ProdRdTeamCnName();
        dmFocActualCostVO.setLv1ProdRndTeamCode("111");
        dmFocActualCostVO.getLv1ProdRndTeamCode();
        dmFocActualCostVO.setLv2ProdRdTeamCnName("111");
        dmFocActualCostVO.getLv2ProdRdTeamCnName();
        dmFocActualCostVO.setLv2ProdRndTeamCode("111");
        dmFocActualCostVO.getLv2ProdRndTeamCode();
        dmFocActualCostVO.setItemCode("item");
        dmFocActualCostVO.getItemCode();
        dmFocActualCostVO.setItemCnName("0321");
        dmFocActualCostVO.getItemCnName();
        dmFocActualCostVO.getViewFlag();
        Timestamp timestamp = new Timestamp(new Date().getTime());
        dmFocActualCostVO.setCreationDate(timestamp);
        dmFocActualCostVO.setLastUpdateDate(timestamp);
        DmFocViewInfoVO.builder().viewFlag("1").viewFlag("1")
                .categoryCnName("name1").categoryCode("code1").l3CegCode("l3").l3CegCnName("l3").l3CegShortCnName("l3")
                .createdBy("11").creationDate(timestamp).lastUpdateDate(timestamp).lastUpdatedBy("11").delFlag("N").groupLevel("lv2")
                .itemCnName("item").itemCode("code2").lv0ProdRdTeamCnName("lv0").lv0ProdRndTeamCode("lv0").lv1ProdRndTeamCode("code1")
                .lv1ProdRdTeamCnName("name").lv2ProdRndTeamCode("code2").lv2ProdRdTeamCnName("name2").categoryCode("code").categoryCnName("name")
                .build().toString();
        Assert.assertNotNull(dmFocActualCostVO);
    }

}