/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.combination.CombinationVO;

/**
 * IDmFocEnergyCustomCombTempDao Class
 *
 * <AUTHOR>
 * @since 2024/4/28
 */
public interface IDmFocEnergyCustomCombTempDao {

    void createEnergyTempCustomCombByCustomId(CombinationVO combinationVO);

    void createEnergyMadeTempCustomCombByCustomId(CombinationVO combinationVO);
}
