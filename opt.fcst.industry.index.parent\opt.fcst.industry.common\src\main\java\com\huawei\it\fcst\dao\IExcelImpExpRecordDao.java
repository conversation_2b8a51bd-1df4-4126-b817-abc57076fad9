/*
 *
 *  Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 *
 */

package com.huawei.it.fcst.dao;

import com.huawei.it.fcst.export.vo.PbiDmFoiImpExpRecordVO;

import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface IExcelImpExpRecordDao {

    int saveExportRecord(PbiDmFoiImpExpRecordVO dmFoiImpExpRecordVO);

    int insertStatisticsRecord(PbiDmFoiImpExpRecordVO dmFoiImpExpRecordVO);

    int updateDataRefreshStatus(@Param("taskId") Long taskId, @Param("status") String status);
}
