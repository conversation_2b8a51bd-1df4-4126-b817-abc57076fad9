/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.codeReplacement;

import com.huawei.it.fcst.industry.pbi.vo.common.CommonBaseVO;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * CodeReplacementVO
 *
 * @since 2024-07-04
 */

@Setter
@Getter
@EqualsAndHashCode
@NoArgsConstructor
public class CodeReplacementVO extends CommonBaseVO {

    /**
     * 是否需要虚化
     */
    private String isNeedBlur;

    /**
     * 预处理虚化
     */
    private Boolean preCus;

    /**
     * 会计期
     */
    private String periodId;

    /**
     * 新SPART
     */
    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<String> newSpartCode;

    /**
     * 旧SPART
     */
    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<String> oldSpartCode;

    /**
     * 替换关系名称
     */
    private String replaceRelationName;

    /**
     * 替换关系类型
     */
    private String replaceRelationType;

    /**
     * 关系名称
     */
    private String relationDesc;

    /**
     * 关系
     */
    private String relationType;

    /**
     * 基期
     */
    private String basePeriodId;

    /**
     * 代表处编码
     */
    private String repofficeCode;

    /**
     * L0
     */
    private String lv0ProdTeamCode;

    /**
     * L1
     */
    private String lv1ProdTeamCode;

    /**
     * L2
     */
    private String lv2ProdTeamCode;

    /**
     * L3
     */
    private String lv3ProdTeamCode;

    /**
     * L3.5
     */
    private String lv4ProdTeamCode;

    /**
     * L3.5
     */
    private String newProdTeamCode;

    /**
     * L3.5
     */
    private String oldProdTeamCode;

    /**
     * 编码类型（NEW:新编码  OLD: 旧编码   TOP:Top编码）
     */
    private String codeType;

    /**
     * 发货量
     */
    private String shipmentQty;

    /**
     * 成本偏差额
     */
    private String costCvAmt;

    /**
     * 成本偏差率
     */
    private String costCvRatio;

    /**
     * 指数值
     */
    private String costIndex;

    /**
     * 各层级编码
     */
    private String groupCode;

    /**
     * 查询的时间，以年为维度
     */
    private List<String> targetPeriod;

    // 表前缀-成本类型
    private String tablePrefixCostType;

    // 表前缀-pbi目录树
    private String tablePrefixPbi;

    // 指数类型
    private String indexCostType;

    // 发货量类型
    private String shipQtyCostType;

    // 查询层级
    private String queryLevel;

    private Long newCustomId;

    private Long oldCustomId;

    private Long customId;

    private String rmbCostAmt;

    private String pbiLevel;

    public void setTablePrefixCostTypeValue() {
        String tableValue = null;
        switch (this.getCostType()) {
            case "PSP":
            case "STD":
            case "PREDICT":
                tableValue = this.getCostType();
                break;
            default:
                break;
        }
        this.tablePrefixCostType = tableValue;
    }

    public void setTablePrefixPbiValue() {
        String tableValue = null;
        switch (this.getGranularityType()) {
            case "IRB":
            case "INDUS":
            case "PROD":
                tableValue = this.getGranularityType();
                break;
            default:
                break;
        }
        this.tablePrefixPbi = tableValue;
    }
}
