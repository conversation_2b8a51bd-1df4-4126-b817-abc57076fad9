/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.common;

import cn.hutool.core.map.MapUtil;
import com.alibaba.ttl.threadpool.TtlExecutors;
import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.industry.pbi.dao.IDmCustomDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstDataRefreshStatusDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstIctRawDataExamineDao;
import com.huawei.it.fcst.industry.pbi.dao.IMixManagementDao;
import com.huawei.it.fcst.industry.pbi.enums.GroupLevelEnum;
import com.huawei.it.fcst.industry.pbi.enums.IndustryConstEnum;
import com.huawei.it.fcst.industry.pbi.impl.comb.IctCustomCommonService;
import com.huawei.it.fcst.industry.pbi.impl.comb.IctCustomService;
import com.huawei.it.fcst.industry.pbi.impl.mix.MixCommonService;
import com.huawei.it.fcst.industry.pbi.impl.thread.CustomThread;
import com.huawei.it.fcst.industry.pbi.utils.FcstIndustryUtil;
import com.huawei.it.fcst.industry.pbi.vo.comb.CombTransformVO;
import com.huawei.it.fcst.industry.pbi.vo.comb.CommonViewVO;
import com.huawei.it.fcst.industry.pbi.vo.common.DmFcstDataRefreshStatus;
import com.huawei.it.fcst.industry.pbi.vo.config.ExamineVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstDimInfoVO;
import com.huawei.it.fcst.industry.pbi.vo.mix.CurrentPriceIndexVO;
import com.huawei.it.fcst.industry.pbi.vo.mix.DifferVO;
import com.huawei.it.fcst.industry.pbi.vo.mix.DistructeVO;
import com.huawei.it.fcst.industry.pbi.vo.mix.DmFocMixResultVO;
import com.huawei.it.fcst.industry.pbi.vo.mix.MixSearchVO;
import com.huawei.it.fcst.industry.pbi.vo.mix.MultiPriceIndexVO;
import com.huawei.it.fcst.industry.pbi.vo.mix.PriceIndexResultVO;
import com.huawei.it.fcst.util.ExcelExportUtil;
import com.huawei.it.fcst.util.ObjectCopyUtil;
import com.huawei.it.jalor5.core.annotation.NoJalorTransation;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Named;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * AsyncExportService Class
 *
 * <AUTHOR>
 * @since 2023/03/15
 */
@Slf4j
@EnableAsync
@Named(value = "asyncIctQueryService")
public class AsyncIctQueryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncIctQueryService.class);

    private static final String TASK_FAIL = "TASK_FAIL";

    private static final String TASK_SUCCESS = "TASK_SUCCESS";

    private static final String EXCEL_FLAG = "excel";

    private static final String PSP_COST_INDEX = "pspCostIndex";

    private static final String STD_COST_INDEX = "stdCostIndex";

    private static final String GROUP_CN_NAME = "groupCnName";

    @Autowired
    private IDmFcstIctRawDataExamineDao dmFcstIctRawDataExamineDao;

    @Autowired
    private IDmFcstDataRefreshStatusDao dataRefreshStatusDao;

    @Autowired
    private IMixManagementDao mixManagementDao;

    @Autowired
    private IctCustomService ictCustomService;

    @Autowired
    private IctCustomCommonService ictCustomCommonService;

    @Autowired
    private MixCommonService mixCommonService;

    @Autowired
    private IDmCustomDao dmCustomDao;

    protected final static ExecutorService EXECUTOR_SERVICE;

    static {
        ThreadPoolExecutor pool = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors() * 10, Runtime.getRuntime().availableProcessors() * 30, 60,
                TimeUnit.SECONDS, new LinkedBlockingQueue<>(Runtime.getRuntime().availableProcessors() * 200),
                new ThreadPoolExecutor.CallerRunsPolicy());
        // 如果设置为true,当任务执行完后，所有的线程在指定的空闲时间后，poolSize会为0
        // 如果不设置，或者设置为false，那么，poolSize会保留为核心线程的数量
        pool.allowCoreThreadTimeOut(true);
        EXECUTOR_SERVICE = TtlExecutors.getTtlExecutorService(pool);
    }

    /**
     * 查询spart，合同号
     *
     * @param examineVO 参数VO
     */
    public void findSpartContract(ExamineVO examineVO) {
        log.info(">>>Begin asyncIctQueryService::findSpartContract");
        Future<Long> spartCont = findSpartCont(examineVO);
        Future<Long> contractNumberCont = findContractNumberCont(examineVO);
        while (true) {
            if (spartCont.isDone() && contractNumberCont.isDone()) {
                break;
            }
        }
        try {
            examineVO.setSpartCont(spartCont.get());
            examineVO.setContractNumberCont(contractNumberCont.get());
        } catch (Exception ex) {
            log.error(">>>>>An exception occurs with asyncIctQueryService::findSpartContract");
            log.error(ex.getMessage());
        }
    }

    @Async("ictAsyncServiceExecutor")
    public Future<Long> findSpartCont(ExamineVO examineVO) {
        log.info(">>>Begin asyncIctQueryService::findSpartCont");
        Long spartCodeCount = dmFcstIctRawDataExamineDao.findSpartExamineCount(examineVO);
        return new AsyncResult<>(spartCodeCount);
    }

    @Async("ictAsyncServiceExecutor")
    public Future<Long> findContractNumberCont(ExamineVO examineVO) {
        log.info(">>>Begin asyncIctQueryService::findContractNumberCont");
        Long contractCount = dmFcstIctRawDataExamineDao.findContractCount(examineVO);
        return new AsyncResult<>(contractCount);
    }

    private DmFcstDataRefreshStatus build(long userId, Long taskId, Date date) {
        DmFcstDataRefreshStatus dataRefreshStatus = new DmFcstDataRefreshStatus();
        dataRefreshStatus.setTaskId(taskId);
        dataRefreshStatus.setLastUpdatedBy(userId);
        dataRefreshStatus.setLastUpdateDate(date);
        return dataRefreshStatus;
    }

    private void updateStates(DmFcstDataRefreshStatus dataRefreshStatus, String status) {
        dataRefreshStatus.setStatus(status);
        dataRefreshStatus.setLastUpdateDate(new Date());
    }

    private void retrySave(DmFcstDataRefreshStatus dataRefreshStatus) {
        updateStates(dataRefreshStatus, TASK_FAIL);
        dataRefreshStatusDao.updateDmFcstDataRefreshStatus(dataRefreshStatus);
    }

    public List<DmFocMixResultVO> findMultiPriceIndex(MixSearchVO mixSearchVO) {
        log.info(">>>Begin asyncIctQueryService::findMultiPriceIndex");
        boolean pspCustomFlag = CollectionUtils.isNotEmpty(mixSearchVO.getPspCustomIdList()) && IndustryConstEnum.COST_TYPE.PSP.getValue().equals(mixSearchVO.getCostType());
        boolean stdCustomFlag = CollectionUtils.isNotEmpty(mixSearchVO.getStdCustomIdList()) && IndustryConstEnum.COST_TYPE.STD.getValue().equals(mixSearchVO.getCostType());
        Future<List<DmFocMixResultVO>> multiCustomPriceIndexList;
        if (pspCustomFlag || stdCustomFlag) {
            multiCustomPriceIndexList = findMultiCustomPriceIndexList(mixSearchVO);
        } else {
            multiCustomPriceIndexList = new AsyncResult<>(new ArrayList<>());
        }
        boolean pspNormalFlag = CollectionUtils.isNotEmpty(mixSearchVO.getPspGroupCodeList()) && IndustryConstEnum.COST_TYPE.PSP.getValue().equals(mixSearchVO.getCostType());
        boolean stdNormalFlag = CollectionUtils.isNotEmpty(mixSearchVO.getStdGroupCodeList()) && IndustryConstEnum.COST_TYPE.STD.getValue().equals(mixSearchVO.getCostType());
        Future<List<DmFocMixResultVO>> multiNormalPriceIndexList;
        if (pspNormalFlag || stdNormalFlag) {
            multiNormalPriceIndexList = findMultiNormalPriceIndexList(mixSearchVO);
        } else {
            multiNormalPriceIndexList = new AsyncResult<>(new ArrayList<>());
        }
        List<DmFocMixResultVO> allResultList = new ArrayList<>();
        while (true) {
            if (multiCustomPriceIndexList.isDone() && multiNormalPriceIndexList.isDone()) {
                break;
            }
        }
        try {
            allResultList.addAll(multiCustomPriceIndexList.get());
            allResultList.addAll(multiNormalPriceIndexList.get());
        } catch (Exception ex) {
            log.error(">>>>>An exception occurs with asyncIctQueryService::findMultiPriceIndex");
            log.error(ex.getMessage());
        }
        return allResultList;
    }

    public List<DmFocMixResultVO> findPriceIndexResult(MixSearchVO mixSearchVO, String methodFlag) {
        log.info(">>>Begin asyncIctQueryService::findPriceIndexResult");
        boolean pspNormalFlag = CollectionUtils.isNotEmpty(mixSearchVO.getPspGroupCodeList()) && IndustryConstEnum.COST_TYPE.PSP.getValue().equals(mixSearchVO.getCostType());
        boolean stdNormalFlag = CollectionUtils.isNotEmpty(mixSearchVO.getStdGroupCodeList()) && IndustryConstEnum.COST_TYPE.STD.getValue().equals(mixSearchVO.getCostType());
        Future<List<DmFocMixResultVO>> notBlurPriceIndexList;
        if (pspNormalFlag || stdNormalFlag) {
            notBlurPriceIndexList = notBlurForPriceIndexChild(mixSearchVO, methodFlag);
        } else {
            notBlurPriceIndexList = new AsyncResult<>(new ArrayList<>());
        }
        boolean pspCustomFlag = CollectionUtils.isNotEmpty(mixSearchVO.getPspCustomIdList()) && IndustryConstEnum.COST_TYPE.PSP.getValue().equals(mixSearchVO.getCostType());
        boolean stdCustomFlag = CollectionUtils.isNotEmpty(mixSearchVO.getStdCustomIdList()) && IndustryConstEnum.COST_TYPE.STD.getValue().equals(mixSearchVO.getCostType());
        Future<List<DmFocMixResultVO>> blurPriceIndexList;
        if (pspCustomFlag || stdCustomFlag) {
            blurPriceIndexList = blurForPriceIndexChild(mixSearchVO, methodFlag);
        } else {
            blurPriceIndexList = new AsyncResult<>(new ArrayList<>());
        }

        List<DmFocMixResultVO> allResultList = new ArrayList<>();
        while (true) {
            if (notBlurPriceIndexList.isDone() && blurPriceIndexList.isDone()) {
                break;
            }
        }
        try {
            allResultList.addAll(notBlurPriceIndexList.get());
            allResultList.addAll(blurPriceIndexList.get());
        } catch (Exception ex) {
            log.error(">>>>>An exception occurs with asyncIctQueryService::findPriceIndexResult");
            log.error(ex.getMessage());
        }
        return allResultList;
    }

    @Async("ictAsyncServiceExecutor")
    public Future<List<DmFocMixResultVO>> findMultiCustomPriceIndexList(MixSearchVO mixSearchVO) {
        log.info(">>>Begin asyncIctQueryService::findMultiCustomPriceIndexList");
        List<DmFocMixResultVO> childMixResultVOList;
        if (CommonConstant.MIN_GROUP_LEVEL.stream().anyMatch(level -> level.equals(mixSearchVO.getGroupLevel()))) {
            childMixResultVOList = mixManagementDao.findPriceIndexMinLevelMultiType(mixSearchVO);
            childMixResultVOList.stream().forEach(child->child.setCostType(mixSearchVO.getCostType()));
        } else {
            // 查询子层级
            MixSearchVO mixSearch = new MixSearchVO();
            BeanUtils.copyProperties(mixSearchVO, mixSearch);
            if (IndustryConstEnum.VIEW_FLAG.PROD_SPART.getValue().equals(mixSearch.getViewFlag())) {
                mixSearch.setGroupLevel(GroupLevelEnum.SPART.getValue());
            } else {
                mixSearch.setGroupLevel(GroupLevelEnum.SUB_DETAIL.getValue());
            }
            mixSearch.setCustomPspParentCodeList(mixSearch.getPspCustomGroupCodeList());
            mixSearch.setCustomStdParentCodeList(mixSearch.getStdCustomGroupCodeList());
            mixSearch.setPspCustomGroupCodeList(null);
            mixSearch.setStdCustomGroupCodeList(null);
            childMixResultVOList = mixManagementDao.findCustomPriceIndexMultiType(mixSearch);
            childMixResultVOList.stream().forEach(result->result.setCostType(mixSearchVO.getCostType()));
        }
        return new AsyncResult<>(childMixResultVOList);
    }

    @Async("ictAsyncServiceExecutor")
    public Future<List<DmFocMixResultVO>> findMultiNormalPriceIndexList(MixSearchVO mixSearchVO) {
        log.info(">>>Begin asyncIctQueryService::findMultiNormalPriceIndexList");
        MixSearchVO mixSearch = new MixSearchVO();
        BeanUtils.copyProperties(mixSearchVO, mixSearch);
        List<DmFocMixResultVO> childMixResultVOList = new ArrayList<>();
        if (!GroupLevelEnum.SPART.getValue().equals(mixSearch.getGroupLevel()) && !mixSearch.isRatioRateFlag()) {
            String nextGroupLevel = FcstIndustryUtil.getViewNextGroupLevel(mixSearch);
            mixSearch.setParentLevel(mixSearch.getGroupLevel());
            mixSearch.setParentPspGroupCodeList(mixSearch.getPspGroupCodeList());
            mixSearch.setParentStdGroupCodeList(mixSearch.getStdGroupCodeList());
            mixSearch.setGroupLevel(nextGroupLevel);
            mixSearch.setPspGroupCodeList(null);
            mixSearch.setStdGroupCodeList(null);
            // 设置重量级团队
            resetMixProdRndTeamCode(mixSearch);
        }
        if (!GroupLevelEnum.SPART.getValue().equals(mixSearchVO.getGroupLevel()) || mixSearch.isRatioRateFlag()|| (GroupLevelEnum.SPART.getValue().equals(mixSearchVO.getGroupLevel()) && mixSearchVO.getSpartCodeList().size()>1)) {
            childMixResultVOList = mixManagementDao.findPriceIndexMultiType(mixSearch);
            childMixResultVOList.stream().forEach(vo->vo.setCostType(mixSearch.getCostType()));
        }
        return new AsyncResult<>(childMixResultVOList);
    }

    public void resetMixProdRndTeamCode(MixSearchVO mixSearchVO) {
        // 如果parentlevel为LV0/LV1/LV2/LV3/L3.5，也就是当parentLevel的下一层级还是重量级团队的时候，需要把prodRndTeamCodeList置为null
        boolean parentFlag = CommonConstant.PROD_GROUP_LEVEL.stream().anyMatch(level -> level.equals(mixSearchVO.getParentLevel()));
        boolean currentFlag = CommonConstant.PROD_GROUP_LEVEL.stream().anyMatch(level -> level.equals(mixSearchVO.getGroupLevel()));
        if (parentFlag && currentFlag) {
            mixSearchVO.setPspProdRndTeamCodeList(null);
            mixSearchVO.setStdProdRndTeamCodeList(null);
        }
    }

    @Async("ictAsyncServiceExecutor")
    public Future<List<DmFocMixResultVO>> blurForPriceIndexChild(MixSearchVO searchVO, String methodFlag) {
        MixSearchVO mixSearchVO = new MixSearchVO();
        BeanUtils.copyProperties(searchVO, mixSearchVO);
        List<DmFocMixResultVO> dmFocMixResultVOList;
        if (null != searchVO.getCustomId()) {
            dmFocMixResultVOList = mixManagementDao.getSummaryCombCurrentPriceIndex(mixSearchVO);
        } else {
            dmFocMixResultVOList = mixManagementDao.findCustomPriceIndexMultiType(mixSearchVO);
        }
        if (CollectionUtils.isNotEmpty(dmFocMixResultVOList)) {
            dmFocMixResultVOList.stream().forEach(mixResultVO -> {
                mixResultVO.setRelationflag("parent");
                if (CommonConstant.GROUP_LEVEL_DIMENSION.contains(mixResultVO.getGroupLevel())) {
                    mixResultVO.setGroupCnName(mixResultVO.getGroupCode() + " " + mixResultVO.getGroupCnName());
                }
            });
        }
        // 子项取正常维度的表
        if (CommonConstant.MIN_GROUP_LEVEL.stream().anyMatch(level -> level.equals(mixSearchVO.getGroupLevel()))) {
            // 查询子层级
            List<DmFocMixResultVO> childMixResultVOList = mixManagementDao.findPriceIndexMinLevelMultiType(mixSearchVO);
            if (EXCEL_FLAG.equals(methodFlag)) {
                childMixResultVOList.stream().forEach(child -> {
                    child.setCostIndex(StringUtils.isBlank(child.getCostIndex()) ? "": new BigDecimal(child.getCostIndex()).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                    // 量纲子类明细
                    if (GroupLevelEnum.SUB_DETAIL.getValue().equals(searchVO.getGroupLevel())) {
                        child.setChildGroupCnName(child.getGroupCode() + " " + child.getGroupCnName() +"("+ child.getProdRndTeamCnName() +")");
                        // 父的名称
                        child.setGroupCnName(child.getGroupCode() +" "+child.getGroupCnName());
                    } else {
                        child.setChildGroupCnName(child.getGroupCode() +"("+ child.getProdRndTeamCnName() +")");
                        child.setGroupCnName(child.getGroupCode());
                    }
                });
            } else {
                childMixResultVOList.stream().forEach(mixResultVO->{
                    mixResultVO.setRelationflag("children");
                    if (CommonConstant.GROUP_LEVEL_DIMENSION.contains(mixResultVO.getGroupLevel())) {
                        mixResultVO.setGroupCnName(mixResultVO.getGroupCode() + " " + mixResultVO.getGroupCnName());
                    }
                });
            }
            dmFocMixResultVOList.addAll(childMixResultVOList);
        } else {
            // 非最细粒度，子项
            notMinLevelChildPriceIndex(mixSearchVO, methodFlag, dmFocMixResultVOList);
        }
        return new AsyncResult<>(dmFocMixResultVOList);
    }

    private void notMinLevelChildPriceIndex(MixSearchVO mixSearchVO, String methodFlag, List<DmFocMixResultVO> dmFocMixResultVOList) {
        if (IndustryConstEnum.VIEW_FLAG.PROD_SPART.getValue().equals(mixSearchVO.getViewFlag())) {
            mixSearchVO.setGroupLevel(GroupLevelEnum.SPART.getValue());
        } else {
            mixSearchVO.setGroupLevel(GroupLevelEnum.SUB_DETAIL.getValue());
        }
        // 查询子层级
        mixSearchVO.setCustomPspParentCodeList(mixSearchVO.getPspGroupCodeList());
        mixSearchVO.setCustomStdParentCodeList(mixSearchVO.getStdGroupCodeList());
        mixSearchVO.setPspCustomGroupCodeList(null);
        mixSearchVO.setStdCustomGroupCodeList(null);
        List<DmFocMixResultVO> childMixResultList = mixManagementDao.findCustomPriceIndexMultiType(mixSearchVO);

        if (EXCEL_FLAG.equals(methodFlag)) {
            childMixResultList.stream().forEach(child -> {
                if (CommonConstant.GROUP_LEVEL_DIMENSION.contains(mixSearchVO.getGroupLevel())) {
                    child.setChildGroupCnName(child.getGroupCode() + " " + child.getGroupCnName());
                } else {
                    child.setChildGroupCnName(child.getGroupCnName());
                }
                if (StringUtils.isNotBlank(child.getParentCnName())) {
                    child.setGroupCnName(child.getParentCode() + " "+ child.getParentCnName());
                } else {
                    child.setGroupCnName(child.getParentCode());
                }
            });
        } else {
            childMixResultList.stream().forEach(dm->{
                dm.setRelationflag("children");
                if (CommonConstant.GROUP_LEVEL_DIMENSION.contains(dm.getGroupLevel())) {
                    dm.setGroupCnName(dm.getGroupCode() + " " + dm.getGroupCnName());
                }
            });
        }
        dmFocMixResultVOList.addAll(childMixResultList);
    }

    @Async("ictAsyncServiceExecutor")
    public Future<List<DmFocMixResultVO>> notBlurForPriceIndexChild(MixSearchVO searchVO, String methodFlag) {
        MixSearchVO mixSearchVO = new MixSearchVO();
        BeanUtils.copyProperties(searchVO, mixSearchVO);
        List<DmFocMixResultVO> parentMixResultList;
        if (null != mixSearchVO.getCustomId()) {
            parentMixResultList = mixManagementDao.getSummaryCombCurrentPriceIndex(mixSearchVO);
        } else {
            parentMixResultList = mixManagementDao.findPriceIndexMultiType(mixSearchVO);
        }
        // 导出需要保留两位小数
        if (EXCEL_FLAG.equals(methodFlag)) {
            parentMixResultList.stream().forEach(vo-> vo.setCostIndex(StringUtils.isBlank(vo.getCostIndex()) ? "" : new BigDecimal(vo.getCostIndex()).setScale(2, BigDecimal.ROUND_HALF_UP).toString()));
        }
        if (CollectionUtils.isNotEmpty(parentMixResultList)) {
            parentMixResultList.stream().forEach(mixResultVO -> {
                mixResultVO.setRelationflag("parent");
                if (CommonConstant.GROUP_LEVEL_DIMENSION.contains(mixResultVO.getGroupLevel())) {
                    mixResultVO.setGroupCnName(mixResultVO.getGroupCode() + " " + mixResultVO.getGroupCnName());
                }
            });
        }
        if (!CommonConstant.MIN_GROUP_LEVEL.stream().anyMatch(level -> level.equals(mixSearchVO.getGroupLevel())) && !mixSearchVO.isRatioRateFlag()) {
            String nextGroupLevel = FcstIndustryUtil.getViewNextGroupLevel(mixSearchVO);
            // 设置重量级团队
            mixSearchVO.setParentLevel(mixSearchVO.getGroupLevel());
            mixSearchVO.setGroupLevel(nextGroupLevel);
            resetMixProdRndTeamCode(mixSearchVO);
            mixSearchVO.setParentPspGroupCodeList(mixSearchVO.getPspGroupCodeList());
            mixSearchVO.setParentStdGroupCodeList(mixSearchVO.getStdGroupCodeList());
            mixSearchVO.setPspGroupCodeList(null);
            mixSearchVO.setStdGroupCodeList(null);
        }
        if (!GroupLevelEnum.SPART.getValue().equals(searchVO.getGroupLevel()) || searchVO.isRatioRateFlag() || (GroupLevelEnum.SPART.getValue().equals(searchVO.getGroupLevel()) && mixSearchVO.getSpartCodeList().size() > 1)) {
            // 查询子层级
            List<DmFocMixResultVO> childMixList = mixManagementDao.findPriceIndexMultiType(mixSearchVO);
            if (EXCEL_FLAG.equals(methodFlag)) {
                childMixList.stream().forEach(child -> {
                    if (CommonConstant.GROUP_LEVEL_DIMENSION.contains(mixSearchVO.getGroupLevel())) {
                        child.setChildGroupCnName(child.getGroupCode() + " " + child.getGroupCnName());
                    } else {
                        child.setChildGroupCnName(child.getGroupCnName());
                    }
                    child.setGroupCnName(child.getParentCode() + " " + child.getParentCnName());
                    child.setCostIndex(StringUtils.isBlank(child.getCostIndex()) ? "" : new BigDecimal(child.getCostIndex()).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
                });
            }
            childMixList.stream().forEach(child -> {
                child.setRelationflag("children");
                if (CommonConstant.GROUP_LEVEL_DIMENSION.contains(child.getGroupLevel())) {
                    child.setGroupCnName(child.getGroupCode() + " " + child.getGroupCnName());
                }
            });
            parentMixResultList.addAll(childMixList);
        }
        return new AsyncResult<>(parentMixResultList);
    }

    public List<String> findInterlockCombFunction(CombTransformVO combVO) {
        log.info(">>>Begin asyncIctQueryService::findInterlockCombFunction");
        combVO.setYtdMonthFlag(CommonConstant.MONTH_N);
        Future<String> customMonFlag = getMonYtdFlagComb(combVO);

        combVO.setYtdMonthFlag(CommonConstant.MONTH_ACC);
        Future<String> customYtdFlag = getMonYtdFlagComb(combVO);

        List<String> allCombFlagList = new ArrayList<>();
        while (true) {
            if (customMonFlag.isDone() && customYtdFlag.isDone()) {
                break;
            }
        }
        try {
            allCombFlagList.add(customMonFlag.get());
            allCombFlagList.add(customYtdFlag.get());
        } catch (Exception ex) {
            log.error(">>>>>An exception occurs with asyncIctQueryService::findInterlockCombFunction");
            log.error(ex.getMessage());
        }
        return allCombFlagList;
    }

    @Async("ictAsyncServiceExecutor")
    public Future<String> getMonYtdFlagComb(CombTransformVO combVO) {
        String customFlag = dmCustomDao.getCombMonth(combVO);
        return new AsyncResult<>(customFlag);
    }

    public List<DmFocMixResultVO> findDiffResult(MixSearchVO mixSearchVO, String isNeedBlur) {
        log.info(">>>Begin asyncIctQueryService::findDiffResult");
        // 分别查询月度和月度累计
        Future<List<DmFocMixResultVO>> distributeList = getDistributeFromDbList(mixSearchVO, isNeedBlur, CommonConstant.MONTH_N);
        Future<List<DmFocMixResultVO>> accDistributeList = getDistributeFromDbList(mixSearchVO, isNeedBlur, CommonConstant.MONTH_ACC);
        List<DmFocMixResultVO> allDistributeList = new ArrayList<>();
        while (true) {
            if (distributeList.isDone() && accDistributeList.isDone()) {
                break;
            }
        }
        try {
            allDistributeList.addAll(distributeList.get());
            allDistributeList.addAll(accDistributeList.get());
        } catch (Exception ex) {
            log.error(">>>>>An exception occurs with asyncIctQueryService::findDiffResult");
            log.error(ex.getMessage());
        }
        return allDistributeList;
    }

    public List<DmFocMixResultVO> findDistributeResult(MixSearchVO mixSearchVO, String isNeedBlur) {
        log.info(">>>Begin asyncIctQueryService::findDistributeResult");
        Future<List<DmFocMixResultVO>> distributeList = getDistributeFromDbList(mixSearchVO, isNeedBlur, mixSearchVO.getYtdFlag());
        List<DmFocMixResultVO> allDistributeList = new ArrayList<>();
        while (true) {
            if (distributeList.isDone()) {
                break;
            }
        }
        try {
            allDistributeList.addAll(distributeList.get());
        } catch (Exception ex) {
            log.error(">>>>>An exception occurs with asyncIctQueryService::findDistributeResult");
            log.error(ex.getMessage());
        }
        return allDistributeList;
    }

    @Async("ictAsyncServiceExecutor")
    public Future<Integer> getDistructeExcelData(MixSearchVO searchVO, XSSFWorkbook workbook, Integer sheetIdx, IRequestContext current, String ytdFlag) throws Exception {
        RequestContextManager.setCurrent(current);
        MixSearchVO mixSearchVO = ObjectCopyUtil.copy(searchVO, MixSearchVO.class);
        mixSearchVO.setYtdFlag(ytdFlag);
        List<DmFocMixResultVO> allDistributeList = new ArrayList<>();
        List<String> costTypeList = mixSearchVO.getCostTypeList();
        for (int i = 0; i < costTypeList.size(); i++) {
            String costType = costTypeList.get(i);
            mixSearchVO.setCostType(costType);
            // 查询数据库
            List<DmFocMixResultVO> distributeResult = findDistributeResult(mixSearchVO, mixSearchVO.getIsNeedBlur());
            allDistributeList.addAll(distributeResult);
        }
        // 金额运算
        List<DmFocMixResultVO> distributeCostList = calGapRmb(allDistributeList, mixSearchVO);
        if (null != mixSearchVO.getPspRate() && !mixSearchVO.getPspRate().equals(0.0D)) {
            distributeCostList = distributeCostList.stream().filter(dm -> dm.getRatioPspStd().compareTo(mixSearchVO.getPspRate().toString()) > 0).collect(Collectors.toList());
        }
        setGroupCnNameDimensionLevel(distributeCostList);
        concatMixPercent(distributeCostList);

        Sheet sheet = workbook.getSheetAt(sheetIdx);
        String title = "月度成本分布图";
        if (CommonConstant.MONTH_ACC.equals(mixSearchVO.getYtdFlag())) {
            title = "月度累计成本分布图";
        }
        sheet.getRow(0).getCell(0).setCellValue(title + " "+ mixSearchVO.getDisplayName());
        setExcelColumn(mixSearchVO, sheet);
        List<DistructeVO> distructeVOList = new ArrayList();
        // 设置字段
        setDistructExcelColumn(distributeCostList, distructeVOList, mixSearchVO);
        // 填充采购价格指数图Sheet数据
        new ExcelExportUtil<DistructeVO>(2, 2, DistructeVO.class).fillSheetData(sheet, distructeVOList);

        // 设置其他筛选字段单元格
        setCommonColumn(mixSearchVO, sheet);
        log.info(">>>End AsyncIctQueryService::getDistructeExcelData");
        return new AsyncResult<>(distructeVOList.size());
    }

    @Async("ictAsyncServiceExecutor")
    public Future<Integer> getDiffExcelData(MixSearchVO searchVO, XSSFWorkbook workbook, Integer sheetIdx, IRequestContext current) throws Exception {
        RequestContextManager.setCurrent(current);
        MixSearchVO mixSearchVO = ObjectCopyUtil.copy(searchVO, MixSearchVO.class);

        List<DmFocMixResultVO> allDifferCostList = new ArrayList<>();
        List<String> costTypeList = mixSearchVO.getCostTypeList();
        for (int i = 0; i < costTypeList.size(); i++) {
            String costType = costTypeList.get(i);
            mixSearchVO.setCostType(costType);
            // 查询数据库
            List<DmFocMixResultVO> differResult = findDiffResult(mixSearchVO, mixSearchVO.getIsNeedBlur());
            allDifferCostList.addAll(differResult);
        }
        // 金额运算
        List<DmFocMixResultVO> differCostList = calGapRmb(allDifferCostList, mixSearchVO);
        if (null != mixSearchVO.getPspRate() && !mixSearchVO.getPspRate().equals(0.0D)) {
            differCostList = differCostList.stream().filter(dm -> dm.getRatioPspStd().compareTo(mixSearchVO.getPspRate().toString()) > 0).collect(Collectors.toList());
        }
        setGroupCnNameDimensionLevel(differCostList);
        concatMixPercent(differCostList);

        Sheet sheet = workbook.getSheetAt(sheetIdx);
        sheet.getRow(0).getCell(0).setCellValue("差异明细 " + mixSearchVO.getDisplayName());
        setExcelColumn(mixSearchVO, sheet);
        List<DifferVO> differVOList = new ArrayList();
        // 设置字段
        setDiffExcelColumn(differCostList, differVOList);
        // 填充采购价格指数图Sheet数据
        new ExcelExportUtil<DifferVO>(2, 2, DifferVO.class).fillSheetData(sheet, differVOList);

        // 设置其他筛选字段单元格
        setCommonColumn(mixSearchVO, sheet);
        log.info(">>>End AsyncIctQueryService::getDiffExcelData");
        return new AsyncResult<>(differVOList.size());
    }

    @Async("ictAsyncServiceExecutor")
    public Future<Integer> getCurrentPriceIndexData(MixSearchVO searchVO, XSSFWorkbook workbook, Integer sheetIdx, IRequestContext current, String ytdFlag) throws Exception {
        RequestContextManager.setCurrent(current);
        MixSearchVO mixSearchVO = ObjectCopyUtil.copy(searchVO, MixSearchVO.class);

        List<DmFocMixResultVO> allPriceIndexList = new ArrayList<>();
        // 查询数据库
        mixSearchVO.setYtdFlag(ytdFlag);
        mixCommonService.getDbforCurrentPriceIndex(mixSearchVO, allPriceIndexList);
        HashMap<String, List<DmFocMixResultVO>> resultColumnMap = allPriceIndexList.stream().collect(
                Collectors.groupingBy(item -> item.getPeriodId(), LinkedHashMap::new, Collectors.toList()));
        Set<String> resultRowSet = resultColumnMap.keySet();
        List<Map> dataList = new ArrayList<>();
        mixCommonService.setCurrentDataList(dataList, resultColumnMap, resultRowSet);

        Sheet sheet = workbook.getSheetAt(sheetIdx);
        String title = "月度产业成本指数图";
        if (CommonConstant.MONTH_ACC.equals(mixSearchVO.getYtdFlag())) {
            title = "月度累计产业成本指数图";
        }
        sheet.getRow(0).getCell(0).setCellValue(title + " "+ mixSearchVO.getDisplayName());
        List<String> costTypeList = mixSearchVO.getCostTypeList();
        String costTypeStr = costTypeList.stream().collect(Collectors.joining("、"));
        sheet.getRow(0).getCell(3).setCellValue(CommonConstant.COST_TYPE + costTypeStr);
        sheet.getRow(1).getCell(3).setCellValue(CommonConstant.GRANULARITY_TYPE_NAME + mixSearchVO.getGranularityTypeCnName());

        sheet.getRow(0).getCell(4).setCellValue(CommonConstant.REGION_NAME + mixSearchVO.getRegionCnName());
        sheet.getRow(1).getCell(4).setCellValue(CommonConstant.REPOFFICE_NAME + mixSearchVO.getRepofficeCnName());
        List<CurrentPriceIndexVO> currentPriceIndexList = new ArrayList();
        // 设置字段
        setCurrentPriceExcelColumn(dataList, currentPriceIndexList);
        // 填充采购价格指数图Sheet数据
        new ExcelExportUtil<CurrentPriceIndexVO>(2, 2, CurrentPriceIndexVO.class).fillSheetData(sheet, currentPriceIndexList);
        // 设置其他筛选字段单元格
        sheet.getRow(2).getCell(3).setCellValue(CommonConstant.SOFTWARE_MARK_NAME + mixSearchVO.getSoftwareMarkCnName());
        sheet.getRow(3).getCell(3).setCellValue(CommonConstant.OVERSEA_FLAG_NAME + mixSearchVO.getOverseaFlagCnName());
        sheet.getRow(4).getCell(3).setCellValue(CommonConstant.BG_NAME + mixSearchVO.getBgCnName());
        sheet.getRow(5).getCell(3).setCellValue(CommonConstant.ACTUAL_MONTH + mixSearchVO.getActualMonth());
        sheet.getRow(2).getCell(4).setCellValue(CommonConstant.BASE_PERIODID + mixSearchVO.getBasePeriodId());
        sheet.getRow(3).getCell(4).setCellValue(CommonConstant.MAINFLAG_NAME + mixSearchVO.getMainFlagCnName());
        if (StringUtils.isNotBlank(mixSearchVO.getCodeAttributesCnName())) {
            sheet.getRow(4).getCell(4).setCellValue(mixSearchVO.getCodeAttributesCnName());
        }
        log.info(">>>End AsyncIctQueryService::getCurrentPriceIndexData");
        return new AsyncResult<>(currentPriceIndexList.size());
    }

    @Async("ictAsyncServiceExecutor")
    public Future<Integer> getMultiPriceIndexData(MixSearchVO searchVO, XSSFWorkbook workbook, Integer sheetIdx, IRequestContext current, String ytdFlag) throws Exception {
        RequestContextManager.setCurrent(current);
        MixSearchVO mixSearchVO = ObjectCopyUtil.copy(searchVO, MixSearchVO.class);
        mixSearchVO.setYtdFlag(ytdFlag);
        List<DmFocMixResultVO> allMultPriceIndexList = new ArrayList<>();
        List<String> costTypeList = mixSearchVO.getCostTypeList();
        String isNeedBlur = mixSearchVO.getIsNeedBlur();
        for (int i = 0; i < costTypeList.size(); i++) {
            String costType = costTypeList.get(i);
            getMultiIndexFromDb(mixSearchVO, costType, isNeedBlur, allMultPriceIndexList);
        }
        List<Map> dataList = new ArrayList<>();
        setGroupCnNameDimensionLevel(allMultPriceIndexList);
        HashMap<String, List<DmFocMixResultVO>> resultColumnMap = allMultPriceIndexList.stream().collect(
                Collectors.groupingBy(item -> item.getPeriodId() + "_" + item.getProdRndTeamCnName() + "_" + item.getParentCode() + "_" + item.getGroupCnName() + "_" + item.getGroupCode(), LinkedHashMap::new, Collectors.toList()));
        Set<String> resultRowSet = resultColumnMap.keySet();
        setMixChildDataList(dataList, resultColumnMap, resultRowSet, mixSearchVO);

        Sheet sheet = workbook.getSheetAt(sheetIdx);
        String title = "月度产业成本指数图";
        if (CommonConstant.MONTH_ACC.equals(mixSearchVO.getYtdFlag())) {
            title = "月度累计产业成本指数图";
        }
        sheet.getRow(0).getCell(0).setCellValue(title + " " + mixSearchVO.getDisplayName());
        String costTypeStr = costTypeList.stream().collect(Collectors.joining("、"));
        sheet.getRow(0).getCell(4).setCellValue(CommonConstant.COST_TYPE + costTypeStr);
        sheet.getRow(1).getCell(4).setCellValue(CommonConstant.GRANULARITY_TYPE_NAME + mixSearchVO.getGranularityTypeCnName());

        sheet.getRow(0).getCell(5).setCellValue(CommonConstant.REGION_NAME + mixSearchVO.getRegionCnName());
        sheet.getRow(1).getCell(5).setCellValue(CommonConstant.REPOFFICE_NAME + mixSearchVO.getRepofficeCnName());
        List<MultiPriceIndexVO> multiPriceIndexList = new ArrayList();
        // 设置字段
        setMultiPriceExcelColumn(dataList, multiPriceIndexList);
        // 填充采购价格指数图Sheet数据
        new ExcelExportUtil<MultiPriceIndexVO>(2, 2, MultiPriceIndexVO.class).fillSheetData(sheet, multiPriceIndexList);
        // 设置其他筛选字段单元格
        sheet.getRow(2).getCell(4).setCellValue(CommonConstant.SOFTWARE_MARK_NAME + mixSearchVO.getSoftwareMarkCnName());
        sheet.getRow(3).getCell(4).setCellValue(CommonConstant.OVERSEA_FLAG_NAME + mixSearchVO.getOverseaFlagCnName());
        sheet.getRow(4).getCell(4).setCellValue(CommonConstant.BG_NAME + mixSearchVO.getBgCnName());
        sheet.getRow(5).getCell(4).setCellValue(CommonConstant.ACTUAL_MONTH + mixSearchVO.getActualMonth());
        sheet.getRow(2).getCell(5).setCellValue(CommonConstant.BASE_PERIODID + mixSearchVO.getBasePeriodId());
        sheet.getRow(3).getCell(5).setCellValue(CommonConstant.MAINFLAG_NAME + mixSearchVO.getMainFlagCnName());
        if (StringUtils.isNotBlank(mixSearchVO.getCodeAttributesCnName())) {
            sheet.getRow(4).getCell(5).setCellValue(mixSearchVO.getCodeAttributesCnName());
        }
        log.info(">>>End AsyncIctQueryService::getMultiPriceIndexData");
        return new AsyncResult<>(multiPriceIndexList.size());
    }

    private void getMultiIndexFromDb(MixSearchVO mixSearchVO, String costType, String isNeedBlur, List<DmFocMixResultVO> allMultPriceIndexList) throws InterruptedException, ExecutionException {
        mixSearchVO.setCostType(costType);
        if (CommonConstant.BLUR_TRUE.equals(isNeedBlur)) {
            // 虚化
            Future<List<DmFocMixResultVO>> multiCustomPriceIndexList = findMultiCustomPriceIndexList(mixSearchVO);
            while (true) {
                if (multiCustomPriceIndexList.isDone()) {
                    break;
                }
            }
            List<DmFocMixResultVO> multiPriceIndexList = multiCustomPriceIndexList.get();
            multiPriceIndexList.stream().forEach(dm -> dm.setCostType(costType));
            allMultPriceIndexList.addAll(multiPriceIndexList);
        } else if (CommonConstant.BLUR_TWO_CONDITION.equals(isNeedBlur)) {
            // 虚化+正常项
            List<DmFocMixResultVO> mixPriceIndexList = findMultiPriceIndex(mixSearchVO);
            allMultPriceIndexList.addAll(mixPriceIndexList);
        } else {
            Future<List<DmFocMixResultVO>> multiNormalPriceIndexList = findMultiNormalPriceIndexList(mixSearchVO);
            List<DmFocMixResultVO> multiPriceIndexList = multiNormalPriceIndexList.get();
            multiPriceIndexList.stream().forEach(dm -> dm.setCostType(costType));
            allMultPriceIndexList.addAll(multiPriceIndexList);
        }
    }

    @Async("ictAsyncServiceExecutor")
    public Future<Integer> getResultPriceIndexData(MixSearchVO searchVO, XSSFWorkbook workbook, Integer sheetIdx, IRequestContext current, String ytdFlag) throws Exception {
        RequestContextManager.setCurrent(current);
        MixSearchVO mixSearchVO = ObjectCopyUtil.copy(searchVO, MixSearchVO.class);
        mixSearchVO.setYtdFlag(ytdFlag);
        List<String> costTypeList = mixSearchVO.getCostTypeList();
        // 查询数据库
        List<DmFocMixResultVO> allMixResultList = getPriceIndexResultExcelData(mixSearchVO);

        Sheet sheet = workbook.getSheetAt(sheetIdx);
        String title = "月度指数结果表";
        if (CommonConstant.MONTH_ACC.equals(mixSearchVO.getYtdFlag())) {
            title = "月度累计指数结果表";
        }
        sheet.getRow(0).getCell(0).setCellValue(title + " " + mixSearchVO.getDisplayName());
        String costTypeStr = costTypeList.stream().collect(Collectors.joining("、"));
        sheet.getRow(0).getCell(5).setCellValue(CommonConstant.COST_TYPE + costTypeStr);
        sheet.getRow(1).getCell(5).setCellValue(CommonConstant.GRANULARITY_TYPE_NAME + mixSearchVO.getGranularityTypeCnName());

        sheet.getRow(0).getCell(6).setCellValue(CommonConstant.REGION_NAME + mixSearchVO.getRegionCnName());
        sheet.getRow(1).getCell(6).setCellValue(CommonConstant.REPOFFICE_NAME + mixSearchVO.getRepofficeCnName());
        List<PriceIndexResultVO> priceIndexResultVOList = new ArrayList();
        // 设置字段
        setResultPriceExcelColumn(allMixResultList, priceIndexResultVOList);
        // 填充采购价格指数图Sheet数据
        new ExcelExportUtil<PriceIndexResultVO>(2, 2, PriceIndexResultVO.class).fillSheetData(sheet, priceIndexResultVOList);
        // 设置其他筛选字段单元格
        sheet.getRow(2).getCell(5).setCellValue(CommonConstant.SOFTWARE_MARK_NAME + mixSearchVO.getSoftwareMarkCnName());
        sheet.getRow(3).getCell(5).setCellValue(CommonConstant.OVERSEA_FLAG_NAME + mixSearchVO.getOverseaFlagCnName());
        sheet.getRow(4).getCell(5).setCellValue(CommonConstant.BG_NAME + mixSearchVO.getBgCnName());
        sheet.getRow(5).getCell(5).setCellValue(CommonConstant.ACTUAL_MONTH + mixSearchVO.getActualMonth());
        sheet.getRow(2).getCell(6).setCellValue(CommonConstant.BASE_PERIODID + mixSearchVO.getBasePeriodId());
        sheet.getRow(3).getCell(6).setCellValue(CommonConstant.MAINFLAG_NAME + mixSearchVO.getMainFlagCnName());
        if (StringUtils.isNotBlank(mixSearchVO.getCodeAttributesCnName())) {
            sheet.getRow(4).getCell(6).setCellValue(mixSearchVO.getCodeAttributesCnName());
        }
        log.info(">>>End AsyncIctQueryService::getResultPriceIndexData");
        return new AsyncResult<>(priceIndexResultVOList.size());
    }

    public List<DmFocMixResultVO> getPriceIndexResultExcelData(MixSearchVO mixSearchVO) throws ExecutionException, InterruptedException {
        List<DmFocMixResultVO> allMixResultVOList = new ArrayList<>();
        List<String> costTypeList = mixSearchVO.getCostTypeList();
        String isNeedBlur = mixSearchVO.getIsNeedBlur();
        for (int i = 0; i < costTypeList.size(); i++) {
            String costType = costTypeList.get(i);
            mixSearchVO.setCostType(costType);
            if (CommonConstant.BLUR_TRUE.equals(isNeedBlur)) {
                // 虚化
                Future<List<DmFocMixResultVO>> dmFocMixResultList = blurForPriceIndexChild(mixSearchVO, EXCEL_FLAG);
                List<DmFocMixResultVO> dmFocMixResultVOList = dmFocMixResultList.get();
                String costTypeDesc = IndustryConstEnum.getCostType(mixSearchVO.getCostType()).getDesc();
                dmFocMixResultVOList.stream().forEach(dm -> dm.setCostType(costTypeDesc));
                allMixResultVOList.addAll(dmFocMixResultVOList);
            } else if (CommonConstant.BLUR_TWO_CONDITION.equals(isNeedBlur)) {
                List<DmFocMixResultVO> twoMixResultList = findPriceIndexResult(mixSearchVO, EXCEL_FLAG);
                String costTypeDesc = IndustryConstEnum.getCostType(mixSearchVO.getCostType()).getDesc();
                twoMixResultList.stream().forEach(dm -> dm.setCostType(costTypeDesc));
                allMixResultVOList.addAll(twoMixResultList);
            } else {
                Future<List<DmFocMixResultVO>> mixResultVOList = notBlurForPriceIndexChild(mixSearchVO, EXCEL_FLAG);
                List<DmFocMixResultVO> mixResultList = mixResultVOList.get();
                String costTypeDesc = IndustryConstEnum.getCostType(mixSearchVO.getCostType()).getDesc();
                mixResultList.stream().forEach(dm -> dm.setCostType(costTypeDesc));
                allMixResultVOList.addAll(mixResultList);
            }
        }
        return allMixResultVOList;
    }

    public void setMixChildDataList(List<Map> dataList, HashMap<String, List<DmFocMixResultVO>> resultColumnMap, Set<String> resultRowSet, MixSearchVO mixSearchVO) {
        String nextGroupLevel = FcstIndustryUtil.getViewNextGroupLevel(mixSearchVO);
        mixSearchVO.setNextGroupLevel(nextGroupLevel);
        for (String keyStr : resultRowSet) {
            List<DmFocMixResultVO> resultList = MapUtil.get(resultColumnMap, keyStr, List.class);
            Map<String, Object> resultMap = new HashMap<>();
            for (int i = 0; i < resultList.size(); i++) {
                String periodId = resultList.get(i).getPeriodId();
                String groupCnName = resultList.get(i).getGroupCnName();
                String costType = resultList.get(i).getCostType();
                String costIndex = resultList.get(i).getCostIndex();
                String prodRndTeamCnName = resultList.get(i).getProdRndTeamCnName();
                String parentCnName = resultList.get(i).getParentCnName();
                String parentCode = resultList.get(i).getParentCode();
                resultMap.put("periodId", periodId);
                if (mixSearchVO.getIsNeedBlur().contains(CommonConstant.BLUR_TRUE)) {
                    putBlurGroupCnName(mixSearchVO, prodRndTeamCnName, parentCnName, parentCode, groupCnName, resultMap);
                } else {
                    resultMap.put(GROUP_CN_NAME, groupCnName);
                }
                if (IndustryConstEnum.COST_TYPE.PSP.getValue().equals(costType)) {
                    resultMap.put(PSP_COST_INDEX, costIndex);
                } else {
                    resultMap.put(STD_COST_INDEX, costIndex);
                }
                // 兼容某些code只有一种成本类型的数据
                if (resultList.size() == 1) {
                    Object pspCostIndex = resultMap.get(PSP_COST_INDEX);
                    Object stdCostIndex = resultMap.get(STD_COST_INDEX);
                    if (null == pspCostIndex) {
                        resultMap.put(PSP_COST_INDEX, "");
                    }
                    if (null == stdCostIndex) {
                        resultMap.put(STD_COST_INDEX, "");
                    }
                }
            }
            dataList.add(resultMap);
        }
    }

    private void putBlurGroupCnName(MixSearchVO mixSearchVO, String prodRndTeamCnName, String parentCnName, String parentCode, String groupCnName, Map<String, Object> resultMap) {
        if (CommonConstant.MIN_GROUP_LEVEL.stream().anyMatch(level -> level.equals(mixSearchVO.getGroupLevel()))) {
            // 拼接prodTeamCnName
            if (StringUtils.isNotBlank(prodRndTeamCnName)) {
                resultMap.put(GROUP_CN_NAME, groupCnName + "(" + prodRndTeamCnName + ")");
            } else {
                resultMap.put(GROUP_CN_NAME, groupCnName);
            }
        } else {
            if (StringUtils.isNotBlank(parentCnName)) {
                resultMap.put(GROUP_CN_NAME, groupCnName + "(" + parentCode + " " + parentCnName + ")");
            } else {
                resultMap.put(GROUP_CN_NAME, groupCnName + "(" + parentCode + ")");
            }
        }
    }

    private void setExcelColumn(MixSearchVO mixSearchVO, Sheet sheet) {
        List<String> costTypeList = mixSearchVO.getCostTypeList();
        String costTypeStr = costTypeList.stream().collect(Collectors.joining("、"));
        sheet.getRow(0).getCell(5).setCellValue(CommonConstant.COST_TYPE + costTypeStr);
        sheet.getRow(1).getCell(5).setCellValue(CommonConstant.GRANULARITY_TYPE_NAME + mixSearchVO.getGranularityTypeCnName());

        sheet.getRow(0).getCell(6).setCellValue(CommonConstant.REGION_NAME + mixSearchVO.getRegionCnName());
        sheet.getRow(1).getCell(6).setCellValue(CommonConstant.REPOFFICE_NAME + mixSearchVO.getRepofficeCnName());
    }

    private void setCommonColumn(MixSearchVO mixSearchVO, Sheet sheet) {
        sheet.getRow(2).getCell(5).setCellValue(CommonConstant.SOFTWARE_MARK_NAME + mixSearchVO.getSoftwareMarkCnName());
        sheet.getRow(3).getCell(5).setCellValue(CommonConstant.OVERSEA_FLAG_NAME + mixSearchVO.getOverseaFlagCnName());
        sheet.getRow(4).getCell(5).setCellValue(CommonConstant.BG_NAME + mixSearchVO.getBgCnName());
        sheet.getRow(5).getCell(5).setCellValue(CommonConstant.ACTUAL_MONTH + mixSearchVO.getActualMonth());
        sheet.getRow(6).getCell(5).setCellValue(CommonConstant.UNIT);
        sheet.getRow(2).getCell(6).setCellValue(CommonConstant.BASE_PERIODID + mixSearchVO.getBasePeriodId());
        sheet.getRow(3).getCell(6).setCellValue(CommonConstant.MAINFLAG_NAME + mixSearchVO.getMainFlagCnName());
        if (StringUtils.isNotBlank(mixSearchVO.getCodeAttributesCnName())) {
            sheet.getRow(4).getCell(6).setCellValue(mixSearchVO.getCodeAttributesCnName());
        }
    }

    private void setDistructExcelColumn(List<DmFocMixResultVO> distributeCostList, List<DistructeVO> distructeVOList, MixSearchVO mixSearchVO) {
        if (CommonConstant.MONTH_N.equals(mixSearchVO.getYtdFlag())) {
            Optional.ofNullable(distributeCostList).orElse(new ArrayList<>()).forEach(item -> {
                DistructeVO distructeVO = DistructeVO.builder()
                        .periodId(item.getPeriodId())
                        .pspRmbCostAmt(item.getPspRmbCostAmt())
                        .stdRmbCostAmt(item.getStdRmbCostAmt())
                        .gapPspStd(item.getGapPspStd())
                        .ratioPspStd(item.getRatioPspStd())
                        .build();
                distructeVOList.add(distructeVO);
            });
        } else {
            Optional.ofNullable(distributeCostList).orElse(new ArrayList<>()).forEach(item -> {
                DistructeVO distructeVO = DistructeVO.builder()
                        .periodId(item.getPeriodId())
                        .pspRmbCostAmt(item.getPspRmbCostAmtAcc())
                        .stdRmbCostAmt(item.getStdRmbCostAmtAcc())
                        .gapPspStd(item.getGapPspStdAcc())
                        .ratioPspStd(item.getRatioPspStdAcc())
                        .build();
                distructeVOList.add(distructeVO);
            });
        }
    }

    private void setCurrentPriceExcelColumn(List<Map> dataList, List<CurrentPriceIndexVO> currentPriceIndexList) {
        Optional.ofNullable(dataList).orElse(new ArrayList<>()).forEach(item -> {
            CurrentPriceIndexVO priceIndexVO = new CurrentPriceIndexVO();
            priceIndexVO.setPeriodId(String.valueOf(item.get("periodId")));
            Object pspCostIndex = item.get(PSP_COST_INDEX);
            String pspCostIndexStr = "";
            String stdCostIndexStr = "";
            if (ObjectUtils.isNotEmpty(pspCostIndex)) {
                pspCostIndexStr = pspCostIndex.toString();
            }
            Object stdCostIndex = item.get(STD_COST_INDEX);
            if (ObjectUtils.isNotEmpty(stdCostIndex)) {
                stdCostIndexStr = stdCostIndex.toString();
            }
            priceIndexVO.setPspCostIndex(StringUtils.isBlank(pspCostIndexStr)? "" : new BigDecimal(pspCostIndexStr).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            priceIndexVO.setStdCostIndex(StringUtils.isBlank(stdCostIndexStr)? "" : new BigDecimal(stdCostIndexStr).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            currentPriceIndexList.add(priceIndexVO);
        });
    }

    private void setMultiPriceExcelColumn(List<Map> dataList, List<MultiPriceIndexVO> multiPriceIndexList) {
        Optional.ofNullable(dataList).orElse(new ArrayList<>()).forEach(item -> {
            MultiPriceIndexVO multiPriceIndexVO = new MultiPriceIndexVO();
            multiPriceIndexVO.setPeriodId(String.valueOf(item.get("periodId")));
            Object pspCostIndex = item.get(PSP_COST_INDEX);
            Object groupCnName = item.get(GROUP_CN_NAME);
            String pspPriceIndexStr = "";
            String stdPriceIndexStr = "";
            String groupCnNameStr = "";
            if (ObjectUtils.isNotEmpty(groupCnName)) {
                groupCnNameStr = groupCnName.toString();
            }
            if (ObjectUtils.isNotEmpty(pspCostIndex)) {
                pspPriceIndexStr = pspCostIndex.toString();
            }
            Object stdCostIndex = item.get(STD_COST_INDEX);
            if (ObjectUtils.isNotEmpty(stdCostIndex)) {
                stdPriceIndexStr = stdCostIndex.toString();
            }
            multiPriceIndexVO.setGroupCnName(groupCnNameStr);
            multiPriceIndexVO.setPspCostIndex(StringUtils.isBlank(pspPriceIndexStr) ? "" : new BigDecimal(pspPriceIndexStr).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            multiPriceIndexVO.setStdCostIndex(StringUtils.isBlank(stdPriceIndexStr) ? "" : new BigDecimal(stdPriceIndexStr).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
            multiPriceIndexList.add(multiPriceIndexVO);
        });
    }

    private void setResultPriceExcelColumn(List<DmFocMixResultVO> allMixResultList, List<PriceIndexResultVO> priceIndexResultVOList) {
        Optional.ofNullable(allMixResultList).orElse(new ArrayList<>()).forEach(item -> {
            PriceIndexResultVO priceIndexResultVO = PriceIndexResultVO.builder()
                    .periodId(item.getPeriodId())
                    .groupCnName(item.getGroupCnName())
                    .childGroupCnName(item.getChildGroupCnName())
                    .costType(item.getCostType())
                    .costIndex(StringUtils.isBlank(item.getCostIndex()) ? item.getCostIndex() : new BigDecimal(item.getCostIndex()).setScale(2, BigDecimal.ROUND_HALF_UP).toString())
                    .build();
            priceIndexResultVOList.add(priceIndexResultVO);
        });
    }

    private void setDiffExcelColumn(List<DmFocMixResultVO> diffList, List<DifferVO> differVOList) {
        Optional.ofNullable(diffList).orElse(new ArrayList<>()).forEach(item -> {
            DifferVO diffVO = DifferVO.builder()
                    .periodId(item.getPeriodId())
                    .gapPspStd(item.getGapPspStd())
                    .ratioPspStd(item.getRatioPspStd())
                    .gapPspStdAcc(item.getGapPspStdAcc())
                    .ratioPspStdAcc(item.getRatioPspStdAcc())
                    .build();
            differVOList.add(diffVO);
        });
    }

    public void setGroupCnNameDimensionLevel(List<DmFocMixResultVO> dmFocMixResultVOList) {
        if (CollectionUtils.isNotEmpty(dmFocMixResultVOList)) {
            dmFocMixResultVOList.stream().forEach(mixResultVO -> {
                if (CommonConstant.GROUP_LEVEL_DIMENSION.contains(mixResultVO.getGroupLevel())) {
                    mixResultVO.setGroupCnName(mixResultVO.getGroupCode() + " " + mixResultVO.getGroupCnName());
                }
            });
        }
    }

    public void concatMixPercent(List<DmFocMixResultVO> dmFocMixVOList) {
        dmFocMixVOList.stream().forEach(dm -> {
            String ratioPspStd = dm.getRatioPspStd();
            if (StringUtils.isNotBlank(ratioPspStd)) {
                dm.setRatioPspStd(ratioPspStd + "%");
            }
            String ratioPspStdAcc = dm.getRatioPspStdAcc();
            if (StringUtils.isNotBlank(ratioPspStdAcc)) {
                dm.setRatioPspStdAcc(ratioPspStdAcc + "%");
            }
        });
    }

    public List<DmFocMixResultVO> calGapRmb(List<DmFocMixResultVO> allDistributeList, MixSearchVO mixSearchVO) {
        HashMap<String, List<DmFocMixResultVO>> resultColumnMap = allDistributeList.stream().collect(
                Collectors.groupingBy(item -> item.getPeriodId(), LinkedHashMap::new, Collectors.toList()));
        List<DmFocMixResultVO> dmFocMixResultVOList = new ArrayList<>();
        Set<String> resultRowSet = resultColumnMap.keySet();
        // 计算差异额和差异率
        for (String keyStr : resultRowSet) {
            List<DmFocMixResultVO> resultList = MapUtil.get(resultColumnMap, keyStr, List.class);
            DmFocMixResultVO dmFocMixResultVO = new DmFocMixResultVO();
            for (int i = 0; i < resultList.size(); i++) {
                String periodId = resultList.get(i).getPeriodId();
                String costType = resultList.get(i).getCostType();
                String ytdFlag = resultList.get(i).getYtdFlag();
                Double rmbCostAmt = resultList.get(i).getRmbCostAmt();
                Double rmbCostAmtAcc = resultList.get(i).getRmbCostAmtAcc();
                dmFocMixResultVO.setPeriodId(periodId);
                if (IndustryConstEnum.COST_TYPE.PSP.getValue().equals(costType)) {
                    if (CommonConstant.MONTH_N.equals(ytdFlag)) {
                        dmFocMixResultVO.setPspRmbCostAmt(rmbCostAmt);
                    } else {
                        dmFocMixResultVO.setPspRmbCostAmtAcc(rmbCostAmtAcc);
                    }
                } else {
                    if (CommonConstant.MONTH_N.equals(ytdFlag)) {
                        dmFocMixResultVO.setStdRmbCostAmt(rmbCostAmt);
                    } else {
                        dmFocMixResultVO.setStdRmbCostAmtAcc(rmbCostAmtAcc);
                    }
                }
            }
            if (CommonConstant.MONTH_N.equals(mixSearchVO.getYtdFlag())) {
                setResultAmt(dmFocMixResultVO.getPspRmbCostAmt(), dmFocMixResultVO.getStdRmbCostAmt(), dmFocMixResultVO, "MONTH");
            } else if (CommonConstant.MONTH_ACC.equals(mixSearchVO.getYtdFlag())) {
                setResultAmt(dmFocMixResultVO.getPspRmbCostAmtAcc(), dmFocMixResultVO.getStdRmbCostAmtAcc(), dmFocMixResultVO,"ACC");
            } else {
                setResultAmt(dmFocMixResultVO.getPspRmbCostAmt(), dmFocMixResultVO.getStdRmbCostAmt(), dmFocMixResultVO, "MONTH");
                setResultAmt(dmFocMixResultVO.getPspRmbCostAmtAcc(), dmFocMixResultVO.getStdRmbCostAmtAcc(), dmFocMixResultVO,"ACC");
            }
            dmFocMixResultVOList.add(dmFocMixResultVO);
        }
        return dmFocMixResultVOList;
    }

    private void setResultAmt(Double pspRmbCostAmt, Double stdRmbCostAmt, DmFocMixResultVO dmFocMixResultVO, String pageType) {
        Double gapPspStd = pspRmbCostAmt - stdRmbCostAmt;
        BigDecimal gapPspStdDecimal = new BigDecimal(String.valueOf(gapPspStd));
        // 差异额
        String gapPspStdStr = gapPspStdDecimal.setScale(2, RoundingMode.HALF_UP).toString();

        String ratioPspStd = "0";
        if (stdRmbCostAmt.compareTo(0.0D)>0) {
            Double ratioPspStdDouble = pspRmbCostAmt / stdRmbCostAmt;
            BigDecimal ratioStdDecimal = new BigDecimal(ratioPspStdDouble.toString());
            BigDecimal multiply = ratioStdDecimal.multiply(new BigDecimal(100));
            ratioPspStd = multiply.setScale(2, RoundingMode.HALF_UP).toString();
        }
        // 设置金额保留两位小数
        BigDecimal pspDecimal = new BigDecimal(String.valueOf(pspRmbCostAmt));
        Double pspDouble = pspDecimal.setScale(2, RoundingMode.HALF_UP).doubleValue();
        BigDecimal stdDecimal = new BigDecimal(String.valueOf(stdRmbCostAmt));
        Double stdDouble = stdDecimal.setScale(2, RoundingMode.HALF_UP).doubleValue();
        if (IndustryConstEnum.PAGE_TYPE.MONTH.getValue().equals(pageType)) {
            dmFocMixResultVO.setRatioPspStd(ratioPspStd);
            dmFocMixResultVO.setGapPspStd(gapPspStdStr);
            dmFocMixResultVO.setPspRmbCostAmt(pspDouble);
            dmFocMixResultVO.setStdRmbCostAmt(stdDouble);
        } else {
            dmFocMixResultVO.setRatioPspStdAcc(ratioPspStd);
            dmFocMixResultVO.setGapPspStdAcc(gapPspStdStr);
            dmFocMixResultVO.setPspRmbCostAmtAcc(pspDouble);
            dmFocMixResultVO.setStdRmbCostAmtAcc(stdDouble);
        }
    }

    @Async("ictAsyncServiceExecutor")
    public Future<List<DmFocMixResultVO>> getDistributeFromDbList(MixSearchVO mixSearch, String isNeedBlur, String ytdFlag) {
        List<DmFocMixResultVO> allDifferList = new ArrayList<>();
        MixSearchVO mixSearchVO = new MixSearchVO();
        BeanUtils.copyProperties(mixSearch, mixSearchVO);
        mixSearchVO.setYtdFlag(ytdFlag);
        if (CommonConstant.BLUR_TRUE.equals(isNeedBlur)) {
            // 虚化
            List<DmFocMixResultVO> distributeCostList = mixManagementDao.distributeCustomChartMultiType(mixSearchVO);
            // 如果是差异明细，需要重新设置月度累计返回值
            setDiffResult(mixSearchVO, distributeCostList);
            allDifferList.addAll(distributeCostList);
        } else if (CommonConstant.BLUR_TWO_CONDITION.equals(isNeedBlur)) {
            boolean pspCustomFlag = CollectionUtils.isNotEmpty(mixSearchVO.getPspCustomIdList()) && IndustryConstEnum.COST_TYPE.PSP.getValue().equals(mixSearchVO.getCostType());
            boolean stdCustomFlag = CollectionUtils.isNotEmpty(mixSearchVO.getStdCustomIdList()) && IndustryConstEnum.COST_TYPE.STD.getValue().equals(mixSearchVO.getCostType());
            List<DmFocMixResultVO> distributeCustomList = new ArrayList<>();
            if (pspCustomFlag || stdCustomFlag) {
                distributeCustomList = mixManagementDao.distributeCustomChartMultiType(mixSearchVO);
            }
            setDiffResult(mixSearchVO, distributeCustomList);
            allDifferList.addAll(distributeCustomList);
            mixSearchVO.setTablePreFix(mixSearchVO.getCostType() + "_" + mixSearchVO.getGranularityType());
            boolean pspNormalFlag = CollectionUtils.isNotEmpty(mixSearchVO.getPspGroupCodeList()) && IndustryConstEnum.COST_TYPE.PSP.getValue().equals(mixSearchVO.getCostType());
            boolean stdNormalFlag = CollectionUtils.isNotEmpty(mixSearchVO.getStdGroupCodeList()) && IndustryConstEnum.COST_TYPE.STD.getValue().equals(mixSearchVO.getCostType());
            List<DmFocMixResultVO> distributeCostList = new ArrayList<>();
            if (pspNormalFlag || stdNormalFlag) {
                distributeCostList = mixManagementDao.distributeChartMultiType(mixSearchVO);
            }
            setDiffResult(mixSearchVO, distributeCostList);
            allDifferList.addAll(distributeCostList);
        } else {
            mixSearchVO.setTablePreFix(mixSearchVO.getCostType() + "_" + mixSearchVO.getGranularityType());
            List<DmFocMixResultVO> distributeCostList = mixManagementDao.distributeChartMultiType(mixSearchVO);
            setDiffResult(mixSearchVO, distributeCostList);
            allDifferList.addAll(distributeCostList);
        }
        return new AsyncResult<>(allDifferList);
    }

    private void setDiffResult(MixSearchVO mixSearchVO, List<DmFocMixResultVO> distributeCostList) {
        distributeCostList.forEach(dist -> {
            dist.setCostType(mixSearchVO.getCostType());
            dist.setYtdFlag(mixSearchVO.getYtdFlag());
            if (CommonConstant.MONTH_ACC.equals(mixSearchVO.getYtdFlag())) {
                dist.setRmbCostAmtAcc(dist.getRmbCostAmt());
                dist.setRmbCostAmt(0.0D);
            }
        });
    }

    @Async("ictAsyncServiceExecutor")
    @NoJalorTransation
    public void asyncCreateComb(List<DmFcstDimInfoVO> customVOList, CombTransformVO combTransformVO, List<DmFcstDimInfoVO> otherCustomVOList) {
        final long millis = System.currentTimeMillis();
        final Date now = new Date(millis);
        Long taskId = combTransformVO.getTaskId();
        Long userId = combTransformVO.getUserId();
        DmFcstDataRefreshStatus refreshStatus = build(userId, taskId, now);

        boolean result = false;
        try {
            ictCustomService.createComb(customVOList, combTransformVO, otherCustomVOList);
            updateStates(refreshStatus, TASK_SUCCESS);
            dataRefreshStatusDao.updateDmFcstDataRefreshStatus(refreshStatus);
            result = true;
        } catch (Exception exception) {
            LOGGER.error("create ict custom error: {},{}", taskId, exception.getMessage());
        }
        if (!result) {
            try {
                Thread.sleep(20);
            } catch (InterruptedException exception) {
                Thread.currentThread().interrupt();
            }
            try {
                retrySave(refreshStatus);
            } catch (Exception exception) {
                LOGGER.error("create ict custom error: {}", taskId, exception.getMessage());
            }
        }
    }

    @Async("ictAsyncServiceExecutor")
    @NoJalorTransation
    public void asyncCustomCombRename(CombTransformVO combTransformVO) {
        final long millis = System.currentTimeMillis();
        final Date now = new Date(millis);
        Long taskId = combTransformVO.getTaskId();
        Long userId = combTransformVO.getUserId();
        DmFcstDataRefreshStatus refreshStatus = build(userId, taskId, now);

        boolean result = false;
        try {
            ictCustomService.callCustomCombFunction(combTransformVO);
            updateStates(refreshStatus, TASK_SUCCESS);
            dataRefreshStatusDao.updateDmFcstDataRefreshStatus(refreshStatus);
            result = true;
        } catch (Exception exception) {
            LOGGER.error("rename ict custom error: {}", taskId, exception.getMessage());
        }
        if (!result) {
            try {
                Thread.sleep(20);
            } catch (InterruptedException exception) {
                Thread.currentThread().interrupt();
            }
            try {
                retrySave(refreshStatus);
            } catch (Exception exception) {
                LOGGER.error("rename ict custom error: {}", taskId, exception.getMessage());
            }
        }
    }

    @Async("ictAsyncServiceExecutor")
    @NoJalorTransation
    public void asyncCustomCombUpdate(CommonViewVO commonViewVO, CombTransformVO combTransformVO) {
        final long millis = System.currentTimeMillis();
        final Date now = new Date(millis);
        Long taskId = combTransformVO.getTaskId();
        Long userId = combTransformVO.getUserId();
        DmFcstDataRefreshStatus refreshStatus = build(userId, taskId, now);

        boolean result = false;
        try {
            ictCustomService.updateCustomCombList(commonViewVO, combTransformVO);
            updateStates(refreshStatus, TASK_SUCCESS);
            dataRefreshStatusDao.updateDmFcstDataRefreshStatus(refreshStatus);
            result = true;
        } catch (Exception exception) {
            LOGGER.error("update ict custom error: {},{}", taskId, exception.getMessage());
        }
        if (!result) {
            try {
                Thread.sleep(20);
            } catch (InterruptedException exception) {
                Thread.currentThread().interrupt();
            }
            try {
                retrySave(refreshStatus);
            } catch (Exception exception) {
                LOGGER.error("update ict custom error: {}", taskId, exception.getMessage());
            }
        }
    }

    @Async("ictAsyncServiceExecutor")
    @NoJalorTransation
    public void asyncInitCombData(List<DmFcstDimInfoVO> pspCombList, List<DmFcstDimInfoVO> stdCombList, CommonViewVO commonViewVO, CombTransformVO combTransformVO) {
        final long millis = System.currentTimeMillis();
        final Date now = new Date(millis);
        Long taskId = combTransformVO.getTaskId();
        Long userId = combTransformVO.getUserId();
        DmFcstDataRefreshStatus refreshStatus = build(userId, taskId, now);

        boolean result = false;
        try {
            ictCustomService.asyncInitComb(pspCombList, stdCombList, commonViewVO, combTransformVO);
            updateStates(refreshStatus, TASK_SUCCESS);
            dataRefreshStatusDao.updateDmFcstDataRefreshStatus(refreshStatus);
            result = true;
        } catch (Exception exception) {
            LOGGER.error("init ict custom error: {}", taskId, exception.getMessage());
        }
        if (!result) {
            try {
                Thread.sleep(20);
            } catch (InterruptedException exception) {
                Thread.currentThread().interrupt();
            }
            try {
                retrySave(refreshStatus);
            } catch (Exception exception) {
                LOGGER.error("init ict custom error: {}", taskId, exception.getMessage());
            }
        }
    }

    @Async("ictAsyncServiceExecutor")
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Future<Boolean> getIctFoldGroupCodeList(CommonViewVO commonViewVO, List<DmFcstDimInfoVO> customVOList, List<DmFcstDimInfoVO> filterCustomCombVOList, IRequestContext requestContext) throws InterruptedException {
        List<DmFcstDimInfoVO> allGroupCodeList = new ArrayList<>();

        CountDownLatch countDownLatch = new CountDownLatch(customVOList.size());
        for (DmFcstDimInfoVO dmCustomCombVO : customVOList) {
            CommonViewVO commonView = ObjectCopyUtil.copy(commonViewVO, CommonViewVO.class);
            commonView.setLv0Flag("Y");
            commonView.setLv0Code(dmCustomCombVO.getLv0Code());
            commonView.setLv1Code(dmCustomCombVO.getLv1Code());
            commonView.setLv2Code(dmCustomCombVO.getLv2Code());
            commonView.setLv3Code(dmCustomCombVO.getLv3Code());
            commonView.setLv4Code(dmCustomCombVO.getLv4Code());
            commonView.setGroupLevel(dmCustomCombVO.getGroupLevel());
            commonView.setPageFlag(commonViewVO.getPageSymbol());
            commonView.setViewFlag(IndustryConstEnum.VIEW_FLAG.PROD_SPART.getValue());
            FcstIndustryUtil.setSpecailCode(commonView);
            if (IndustryConstEnum.MAIN_FLAG.Y.getValue().equals(commonView.getMainFlag()) && CommonConstant.ALL_CH.equals(commonView.getCodeAttributes())) {
                commonView.setCodeAttributes("");
            }
            if (IndustryConstEnum.PAGE_TYPE.ANNUAL.getValue().equals(commonView.getPageSymbol())) {
                if (IndustryConstEnum.MAIN_FLAG.N.getValue().equals(commonView.getMainFlag())) {
                    commonView.setMainFlag(null);
                }
            }
            EXECUTOR_SERVICE.execute(new CustomThread(countDownLatch, commonView, allGroupCodeList, requestContext, ictCustomCommonService));
        }
        countDownLatch.await();
        List<DmFcstDimInfoVO> allCombVOList = ObjectCopyUtil.copyList(allGroupCodeList, DmFcstDimInfoVO.class);
        filterCustomCombVOList.addAll(allCombVOList);
        return new AsyncResult<>(Boolean.TRUE);
    }
}