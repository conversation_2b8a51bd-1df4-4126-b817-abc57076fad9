/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.provider.month;

import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.industry.pbi.dao.IctMonthCostIdxDao;
import com.huawei.it.fcst.industry.pbi.enums.IndustryConstEnum;
import com.huawei.it.fcst.export.IExcelExportDataProvider;
import com.huawei.it.fcst.industry.pbi.service.month.IIctMonthAnalysisService;
import com.huawei.it.fcst.industry.pbi.utils.FcstIndustryUtil;
import com.huawei.it.fcst.export.vo.ExcelExportContext;
import com.huawei.it.fcst.export.vo.ExportList;
import com.huawei.it.fcst.industry.pbi.vo.month.IctMonthAnalysisVO;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import javax.inject.Inject;
import javax.inject.Named;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 降成本目标对比图导出数据提供类
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
@Slf4j
@Named("IExcelExport.CostRedTargetChartExpDataProvider")
public class CostRedTargetChartExpDataProvider implements IExcelExportDataProvider {

    @Inject
    private IIctMonthAnalysisService monthAnalysisService;

    @Inject
    private IctMonthCostIdxDao monthCostIdxDao;

    @Override
    public List<?> getData(Serializable conditionObject, PageVO pageVO) throws ApplicationException {
        log.info(">>>Begin CostRedTargetChartExpDataProvider::getData");
        IctMonthAnalysisVO monthAnalysisVO = (IctMonthAnalysisVO)conditionObject;
        IctMonthAnalysisVO paramsVO = new IctMonthAnalysisVO();
        monthAnalysisVO.setPageIndex(1);
        monthAnalysisVO.setPageSize(3000);
        BeanUtils.copyProperties(monthAnalysisVO, paramsVO);
        ResultDataVO resultDataVO = monthAnalysisService.getCostTargetCompareChart(paramsVO);
        Map<String, List<IctMonthAnalysisVO>> mapData = (Map<String, List<IctMonthAnalysisVO>> ) resultDataVO.getData();
        ExportList exportList = new ExportList();
        mapData.entrySet().stream().forEach(entry ->{
            List<IctMonthAnalysisVO> costRedTargetList = entry.getValue();
            costRedTargetList.forEach(ele->ele.setCostIndex(null == ele.getCostIndex() ? null : new BigDecimal(ele.getCostIndex()).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue()));
            costRedTargetList.forEach(ele->ele.setYtdCostIndex(null == ele.getYtdCostIndex() ? null : new BigDecimal(ele.getYtdCostIndex()).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue()));
            costRedTargetList.forEach(ele->ele.setCostReductionRate(null == ele.getCostReductionRate() ? null:new BigDecimal(ele.getCostReductionRate()).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue()));
            exportList.addAll(entry.getValue());
        });
        exportList.setTotalRows(exportList.size());
        return exportList;
    }

    @Override
    public Map<String, Object> getHeader(Serializable conditionObject, Map<String, Object> parameters) throws ApplicationException {
        ExcelExportContext context = (ExcelExportContext) conditionObject;
        IctMonthAnalysisVO monthAnalysisVO = (IctMonthAnalysisVO) context.getConditionObject();
        // 自定义表头
        Map<String, Object> headMap = new HashMap<>();
        headMap.put("displayName", monthAnalysisVO.getDisplayName());
        headMap.put("groupCnName", monthAnalysisVO.getGroupCnName());
        headMap.put("basePeriodId", FcstIndustryUtil.getActualMonth(monthAnalysisVO.getBasePeriodId().toString()));
        headMap.put("granularityTypeCnName", monthAnalysisVO.getGranularityTypeCnName());
        headMap.put("costType", monthAnalysisVO.getCostType());
        headMap.put("overseaFlagCnName", monthAnalysisVO.getOverseaFlagCnName());
        headMap.put("bgCnName", monthAnalysisVO.getBgCnName());
        headMap.put("actualMonth", FcstIndustryUtil.getActualMonth(monthCostIdxDao.findActualMonth(monthAnalysisVO)));
        headMap.put("regionCnName", monthAnalysisVO.getRegionCnName());
        headMap.put("repofficeCnName", monthAnalysisVO.getRepofficeCnName());
        headMap.put("mainFlagCnName", monthAnalysisVO.getMainFlagCnName());
        headMap.put("codeAttributes", monthAnalysisVO.getCodeAttributes());
        headMap.put("softwareMarkStr", "PSP".equals(monthAnalysisVO.getCostType()) ? CommonConstant.SOFTWARE_MARK + IndustryConstEnum.getSoftwareMark(monthAnalysisVO.getSoftwareMark()).getDesc() : "");
        return headMap;
    }
}