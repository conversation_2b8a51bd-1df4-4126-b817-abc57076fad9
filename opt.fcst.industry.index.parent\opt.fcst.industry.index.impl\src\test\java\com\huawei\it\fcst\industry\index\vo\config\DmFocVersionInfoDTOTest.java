/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.config;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

/**
 * DmFocVersionInfoDTOTest Class
 *
 * <AUTHOR>
 * @since 2023/4/23
 */
public class DmFocVersionInfoDTOTest extends BaseVOCoverUtilsTest<DmFocVersionInfoDTO> {

    @Override
    protected Class<DmFocVersionInfoDTO> getTClass() {
        return DmFocVersionInfoDTO.class;
    }

    @Test
    public void testMethod() {
        DmFocVersionInfoDTO dimensionParamVO = new DmFocVersionInfoDTO();
        dimensionParamVO.setVersion("2222");
        dimensionParamVO.getVersion();
        dimensionParamVO.setVersionId(1L);
        dimensionParamVO.getVersionId();
        dimensionParamVO.setParentVersionId(10L);
        dimensionParamVO.getParentVersionId();
        dimensionParamVO.setDataType("data");
        dimensionParamVO.getDataType();
        dimensionParamVO.setStatus(100L);
        dimensionParamVO.getStatus();
        dimensionParamVO.setStep(1);
        dimensionParamVO.getStep();
        dimensionParamVO.setUserId(11L);
        dimensionParamVO.getUserId();
        dimensionParamVO.setLastUpdateStr("str");
        dimensionParamVO.getLastUpdateStr();
        dimensionParamVO.setStartDate("2023");
        dimensionParamVO.getStartDate();
        dimensionParamVO.setEndDate("2304");
        dimensionParamVO.getEndDate();
        dimensionParamVO.setVersionType("data");
        dimensionParamVO.getVersionType();
        dimensionParamVO.builder()
            .versionType("adjust")
            .dataType("data")
            .endDate("end")
            .version("20230301")
            .parentVersionId(15L)
            .lastUpdateStr("16.33")
            .startDate("15")
            .versionId(15L)
            .status(11L)
            .step(1)
            .userId(1175L)
            .build();
        Assert.assertNotNull(dimensionParamVO);
    }
}