/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.config;

import com.huawei.it.fcst.vo.BaseVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 主力编码维表VO实体类
 *
 * <AUTHOR>
 * @since 2024-07-10
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IctProdMainCodeDimVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 1153525718803322696L;

    /**
     * 主键ID
     */
    private Long primaryId;

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 版本名称
     */
    private String versionName;

    /**
     * 下拉框层级：LV0/LV1/LV2/LV3/LV4/SPART
     *      LV0-BG下拉框
     *      LV1-L1下拉框
     *      LV2-L2下拉框
     *      LV3-L3下拉框
     *      LV4-L3.5下拉框
     *      SPART-SPART下拉框
     */
    private String groupLevel;

    /**
     * BG编码
     */
    private String bgCode;

    /**
     * BG中文名称
     */
    private String bgCnName;

    /**
     * LV1销售目录编码
     */
    private String lv1ProdListCode;

    /**
     * LV1销售目录中文名称
     */
    private String lv1ProdListCnName;

    /**
     * LV2销售目录编码
     */
    private String lv2ProdListCode;

    /**
     * LV2销售目录中文名称
     */
    private String lv2ProdListCnName;

    /**
     * LV3销售目录编码
     */
    private String lv3ProdListCode;

    /**
     * LV3销售目录中文名称
     */
    private String lv3ProdListCnName;

    /**
     * LV4销售目录编码
     */
    private String lv4ProdListCode;

    /**
     * LV4销售目录中文名称
     */
    private String lv4ProdListCnName;

    /**
     * 产品
     */
    private String productCode;

    /**
     * SPART编码
     */
    private String spartCode;

    /**
     * SPART中文名称
     */
    private String spartCnName;

    /**
     * 编码属性
     */
    private String codeAttributes;

    /**
     * 当前页
     */
    private int curPage;

    /**
     * 每页显示条数
     */
    private int pageSize;

    /**
     * bg编码集合
     */
    private List<String> bgCodeList;

    @Override
    public String toString() {
        return StringUtils.defaultIfBlank(this.getBgCnName(), "").concat("#")
                .concat(StringUtils.defaultIfBlank(this.getLv1ProdListCnName(), "")).concat("#")
                .concat(StringUtils.defaultIfBlank(this.getLv2ProdListCnName(), "")).concat("#")
                .concat(StringUtils.defaultIfBlank(this.getLv3ProdListCnName(), "")).concat("#")
                .concat(StringUtils.defaultIfBlank(this.getLv4ProdListCnName(), "")).concat("#")
                .concat(StringUtils.defaultIfBlank(this.getSpartCode(), ""));
    }

}