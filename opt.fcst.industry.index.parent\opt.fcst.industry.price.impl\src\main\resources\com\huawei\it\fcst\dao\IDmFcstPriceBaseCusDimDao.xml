<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.price.dao.IDmFcstPriceBaseCusDimDao">
    <resultMap type="com.huawei.it.fcst.industry.price.vo.drop.DmFcstBasePriceCusDimVO" id="resultMap">
        <result property="bgCode" column="bg_code"/>
        <result property="bgCnName" column="bg_cn_name"/>
        <result property="spartCode" column="spart_code"/>
        <result property="spartCnName" column="spart_cn_name"/>
        <result property="regionCode" column="region_code"/>
        <result property="regionCnName" column="region_cn_name"/>
        <result property="repofficeCode" column="repoffice_code"/>
        <result property="repofficeCnName" column="repoffice_cn_name"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdatedate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="statusFlag" column="status_flag"/>
        <result property="customId" column="custom_id"/>
        <result property="groupCode" column="groupCode"/>
        <result property="groupLevel" column="groupLevel"/>
        <result property="groupCnName" column="groupCnName"/>
        <result property="lvCnName" column="lv_cn_name"/>
        <result property="lvCode" column="lv_code"/>
        <result property="parentLevel" column="parent_level"/>
        <result property="pageType" column="page_flag"/>
        <result property="signTopCustCategoryCode" column="sign_top_cust_category_code"/>
        <result property="signTopCustCategoryCnName" column="sign_top_cust_category_cn_name"/>
        <result property="signSubsidiaryCustcatgCnName" column="sign_subsidiary_custcatg_cn_name"/>
    </resultMap>

    <select id="baseCusDimStatus" resultMap="resultMap">
        select
        <choose>
            <when test='nextGroupLevel == "SPART"'>
                DISTINCT t1.custom_id,t1.spart_code AS groupCode, t1.spart_cn_name AS groupCnName, 'SPART' AS groupLevel,t1.status_flag,t1.lv_code,t1.parent_level
            </when>
            <otherwise>
            </otherwise>
        </choose>
        from fin_dm_opt_foi.dm_fcst_price_base_cus_dim_t t1 left join fin_dm_opt_foi.dm_fcst_price_cus_user_info_t t2
        on t1.custom_id = t2.custom_id
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            and t1.del_flag = 'N'
            <if test='pageType!=null and pageType!=""'>
                and t1.page_flag = #{pageType,jdbcType=VARCHAR}
            </if>
            <if test='regionCode!=null and regionCode!=""'>
                and t1.region_code = #{regionCode,jdbcType=VARCHAR}
            </if>
            <if test='repofficeCode!=null and repofficeCode!=""'>
                and t1.repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='bgCode!=null and bgCode!=""'>
                and t1.bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='overseaFlag!=null and overseaFlag!=""'>
                and t1.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='viewFlag!=null and viewFlag!=""'>
                and t1.view_flag = #{viewFlag,jdbcType=VARCHAR}
            </if>
            <if test='signTopCustCategoryCode != null and signTopCustCategoryCode != ""'>
                and t1.sign_top_cust_category_code = #{signTopCustCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test='signSubsidiaryCustcatgCnName != null and signSubsidiaryCustcatgCnName != ""'>
                and t1.sign_subsidiary_custcatg_cn_name = #{signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!=""'>
                and t1.spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='userId!=null and userId!=""'>
                and t2.user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test='roleId!=null and roleId!=""'>
                and t2.role_id = #{roleId,jdbcType=VARCHAR}
            </if>
            <if test='nextGroupLevel!=null and nextGroupLevel!=""'>
                and t1.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </if>
            <if test='nextGroupLevel == "SPART"'>
                and t1.spart_code is not null
            </if>
        </trim>
    </select>

    <select id="getBaseCusDimInfoList"  resultMap="resultMap">
        select
        <choose>
            <when test='groupLevel == "SPART"'>
                custom_id,lv_code,lv_cn_name,spart_code,spart_cn_name,page_flag,region_code,region_cn_name,
                repoffice_code,repoffice_cn_name,bg_code,bg_cn_name,
                sign_top_cust_category_code,sign_top_cust_category_cn_name,sign_subsidiary_custcatg_cn_name,
                oversea_flag,view_flag,status_flag,del_flag,group_level,parent_level
            </when>
            <otherwise>
            </otherwise>
        </choose>
        from fin_dm_opt_foi.dm_fcst_price_base_cus_dim_t
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            and del_flag = 'N'
            <if test='pageType!=null and pageType!=""'>
                and page_flag = #{pageType,jdbcType=VARCHAR}
            </if>
            <if test='regionCode!=null and regionCode!=""'>
                and region_code = #{regionCode,jdbcType=VARCHAR}
            </if>
            <if test='repofficeCode!=null and repofficeCode!=""'>
                and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='bgCode!=null and bgCode!=""'>
                and bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='overseaFlag!=null and overseaFlag!=""'>
                and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='viewFlag!=null and viewFlag!=""'>
                and view_flag = #{viewFlag,jdbcType=VARCHAR}
            </if>
            <if test='signTopCustCategoryCode != null and signTopCustCategoryCode != ""'>
                and sign_top_cust_category_code = #{signTopCustCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test='signSubsidiaryCustcatgCnName != null and signSubsidiaryCustcatgCnName != ""'>
                and sign_subsidiary_custcatg_cn_name = #{signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!=""'>
                and spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='groupLevel!=null and groupLevel!=""'>
                and group_level = #{groupLevel,jdbcType=VARCHAR}
            </if>
        </trim>
    </select>

    <insert id="createDmFcstCusDimDTO" parameterType="com.huawei.it.fcst.industry.price.vo.drop.DmFcstBasePriceCusDimVO">
        INSERT INTO fin_dm_opt_foi.dm_fcst_price_base_cus_dim_t
        (custom_id,
         custom_cn_name,
         lv_code,
         lv_cn_name,
         parent_level,
         spart_code,
         spart_cn_name,
         group_level,
         oversea_flag,
         region_code,
         region_cn_name,
         repoffice_code,
         repoffice_cn_name,
         sign_top_cust_category_code,
         sign_top_cust_category_cn_name,
         sign_subsidiary_custcatg_cn_name,
         view_flag,
         page_flag,
         status_flag,
         created_by,
         creation_date,
         last_updated_by,
         last_update_date,
         del_flag,
         bg_code,
         bg_cn_name)
        VALUES
            (#{customId,jdbcType=NUMERIC},
             #{customCnName,jdbcType=VARCHAR},
             #{lvCode,jdbcType=VARCHAR},
             #{lvCnName,jdbcType=VARCHAR},
             #{parentLevel,jdbcType=VARCHAR},
             #{spartCode,jdbcType=VARCHAR},
             #{spartCnName,jdbcType=VARCHAR},
             #{groupLevel,jdbcType=VARCHAR},
             #{overseaFlag,jdbcType=VARCHAR},
             #{regionCode,jdbcType=VARCHAR},
             #{regionCnName,jdbcType=VARCHAR},
             #{repofficeCode,jdbcType=VARCHAR},
             #{repofficeCnName,jdbcType=VARCHAR},
             #{signTopCustCategoryCode,jdbcType=VARCHAR},
             #{signTopCustCategoryCnName,jdbcType=VARCHAR},
             #{signSubsidiaryCustcatgCnName,jdbcType=VARCHAR},
             #{viewFlag,jdbcType=VARCHAR},
             #{pageType,jdbcType=VARCHAR},
             #{statusFlag,jdbcType=VARCHAR},
             #{createdBy,jdbcType=NUMERIC},
             NOW(),
             #{lastUpdatedBy,jdbcType=NUMERIC},
             NOW(),
             'N',
             #{bgCode,jdbcType=VARCHAR},
             #{bgCnName,jdbcType=VARCHAR}
            )
    </insert>

    <select id="getBaseCusDimKey" resultType="java.lang.Long">
        SELECT nextval('fin_dm_opt_foi.dm_fcst_price_base_cus_dim_s')
    </select>

    <select id="getNeedTaskList" resultMap="resultMap">
        select custom_id,
               page_flag,
               view_flag,
               lv_code
        from (select custom_id,page_flag,view_flag,lv_code,row_number() over(partition by page_flag order by last_update_date asc) as rn
              from fin_dm_opt_foi.dm_fcst_price_base_cus_dim_t t1
              where not EXISTS(select 1
                               from dm_fcst_price_base_cus_dim_t t2
                               where t1.page_flag = t2.page_flag
                                 and status_flag in('ING'))
                and del_flag = 'N' and status_flag in ('D','N')
        <![CDATA[
                and ifnull(err_count,0) < 5
	    ]]>
        )
        where rn = 1
    </select>

    <update id="updateTaskStatus">
        update fin_dm_opt_foi.dm_fcst_price_base_cus_dim_t
        set  status_flag = #{statusFlag,jdbcType=VARCHAR}
        ,last_update_date = now()
        <if test='statusFlag == "N"' >
            ,err_count = ifnull(err_count,0) + 1
        </if>
        <if test='statusFlag == "Y"' >
            ,err_count = 0
        </if>
        where custom_id = #{customId,jdbcType=NUMERIC}
    </update>

    <update id="updateStatusFlag">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update fin_dm_opt_foi.dm_fcst_price_base_cus_dim_t set status_flag =#{item.statusFlag,jdbcType=VARCHAR} ,last_update_date = now() where custom_id =#{item.customId,jdbcType=NUMERIC}
        </foreach>
    </update>

    <select id="getExceptionTaskList" resultMap="resultMap">
        select custom_id,
               page_flag,
               view_flag,
               lv_code
        from fin_dm_opt_foi.dm_fcst_price_base_cus_dim_t
        where status_flag='ING' and del_flag = 'N'
              <![CDATA[
          and abs(timestampdiff(HOUR,last_update_date,now())) > 1
        ]]>
    </select>
</mapper>