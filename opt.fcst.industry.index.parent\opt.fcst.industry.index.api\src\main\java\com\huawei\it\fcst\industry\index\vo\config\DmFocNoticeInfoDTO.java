/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.config;

import com.huawei.it.fcst.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * DmFocNoticeInfoDTO Class
 *
 * <AUTHOR>
 * @since 2024/2/18
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "公告映射表")
public class DmFocNoticeInfoDTO extends BaseVO {

    /**
     * id
     **/
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 公共类型
     **/
    @ApiModelProperty(value = "公共类型")
    private String noticeType;

    /**
     * 公共主题
     **/
    @ApiModelProperty(value = "公共主题")
    private String noticeTheme;

    /**
     * 品类名称
     **/
    @ApiModelProperty(value = "公告标题")
    private String noticeTitle;

    /**
     * 公告内容
     **/
    @ApiModelProperty(value = "公告内容")
    private String noticeContent;

    /**
     * 公告标签
     **/
    @ApiModelProperty(value = "公告标签")
    private String noticeTag;

    /**
     * 源文件
     **/
    @ApiModelProperty(value = "源文件")
    private String fileSourceKey;

    /**
     * 录入文件类型
     **/
    @ApiModelProperty(value = "录入文件类型")
    private String fileType;

    /**
     * 历史公告
     **/
    @ApiModelProperty(value = "历史公告")
    private String isHistory;


}
