/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.replace;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstVersionInfoDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmfcstIctCodeReplInfoDao;
import com.huawei.it.fcst.industry.pbi.impl.config.AsyncIctConfigService;
import com.huawei.it.fcst.industry.pbi.impl.template.ReplaceTemplateEnum;
import com.huawei.it.fcst.industry.pbi.service.common.IIctCommonService;
import com.huawei.it.fcst.export.IExcelTemplateBeanManager;
import com.huawei.it.fcst.export.IExportProcessorService;
import com.huawei.it.fcst.industry.pbi.service.replace.IReplaceManagementService;
import com.huawei.it.fcst.industry.pbi.utils.FcstIndustryUtil;
import com.huawei.it.fcst.industry.pbi.vo.common.DmFcstDataRefreshStatus;
import com.huawei.it.fcst.industry.pbi.vo.config.ImportContextVO;
import com.huawei.it.fcst.industry.pbi.vo.replace.DmFocReplVO;
import com.huawei.it.fcst.industry.pbi.vo.replace.ReplaceSearchVO;
import com.huawei.it.fcst.industry.pbi.vo.version.DmFcstVersionInfoDTO;
import com.huawei.it.fcst.util.ExcelUtils;
import com.huawei.it.fcst.util.UserInfoUtils;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.fcst.vo.UploadInfoVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Inject;
import javax.inject.Named;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * ReplaceManagementService Class
 *
 * <AUTHOR>
 * @since 2024/7/10
 */
@Slf4j
@Named("replaceManagementService")
@JalorResource(code = "replaceManagementService", desc = "new ICT-配置管理-新旧编码替换")
public class ReplaceManagementService implements IReplaceManagementService {

    @Autowired
    private IDmfcstIctCodeReplInfoDao dmfcstIctCodeReplInfoDao;

    @Autowired
    private IDmFcstVersionInfoDao dmFcstVersionInfoDao;

    @Autowired
    private IExportProcessorService iExportProcessorService;

    @Inject
    private ExcelUtils excelUtils;

    @Inject
    private IIctCommonService ictCommonService;

    @Inject
    private AsyncIctConfigService asyncIctConfigService;

    private static final Pattern PATTERN = Pattern.compile("[0-9]*");

    @JalorOperation(code = "replDropDownList", desc = "根据版本获取下拉框")
    @Override
    public ResultDataVO replDropDownList(ReplaceSearchVO replaceSearchVO) throws CommonApplicationException {

        return ResultDataVO.success(dmfcstIctCodeReplInfoDao.findReplInfoDropDownList(replaceSearchVO));
    }

    @JalorOperation(code = "productDimList", desc = "编辑时获取下拉框")
    @Override
    public ResultDataVO productDimList(ReplaceSearchVO replaceSearchVO) throws CommonApplicationException {
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(replaceSearchVO.getPageSize());
        pageVO.setCurPage(replaceSearchVO.getPageIndex());
        List<DmFocReplVO> productDimList = new ArrayList<>();
        if ("SPART".equals(replaceSearchVO.getGroupLevel())) {
            PagedResult<DmFocReplVO> spartCodePageList = dmfcstIctCodeReplInfoDao.findSpartCodePageList(replaceSearchVO, pageVO);
            List<DmFocReplVO> result = spartCodePageList.getResult();
            pageVO = spartCodePageList.getPageVO();
            productDimList = result;
        } else {
            productDimList = dmfcstIctCodeReplInfoDao.findProductDimList(replaceSearchVO);
        }
        Map result = new LinkedHashMap();
        result.put("result", productDimList);
        result.put("pageVo", pageVO);
        return ResultDataVO.success(result);
    }

    @Override
    @JalorOperation(code = "getSystemTime", desc = "获取系统时间")
    public ResultDataVO getSystemTime() {
        return ResultDataVO.success(new Date().getTime());
    }

    @JalorOperation(code = "findVersionList", desc = "获取版本下拉框")
    @Override
    public ResultDataVO findVersionList() throws CommonApplicationException {
        DmFcstVersionInfoDTO versionDto = new DmFcstVersionInfoDTO();
        versionDto.setDataType("REPLACE_DIM");
        return ResultDataVO.success(dmFcstVersionInfoDao.findPlanVersionList(versionDto));
    }

    @JalorOperation(code = "findInfoPageList", desc = "查询新旧编码列表")
    @Override
    public ResultDataVO findInfoPageList(ReplaceSearchVO replaceSearchVO) {
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(replaceSearchVO.getPageSize());
        pageVO.setCurPage(replaceSearchVO.getPageIndex());
        return ResultDataVO.success(dmfcstIctCodeReplInfoDao.findInfoPageList(replaceSearchVO, pageVO));
    }

    @JalorOperation(code = "updateReplInfoList", desc = "新增或编辑新旧编码")
    @Audit(module = "replaceManagementService-updateReplInfoList", operation = "updateReplInfoList", message = "新增或编辑新旧编码")
    @Override
    public ResultDataVO updateReplInfoList(ReplaceSearchVO replaceSearchVO) throws CommonApplicationException {
        if (null == replaceSearchVO.getVersionId()) {
            throw new CommonApplicationException("入参维度版本为空");
        }
        List<DmFocReplVO> dmFocReplVOList = replaceSearchVO.getReplaceList();
        // 输入的字符去空格
        trimReplParamSpace(dmFocReplVOList);
        // 校验入参数据
        checkReplData(dmFocReplVOList);
        // 查询当前版本对应维度数据
        List<DmFocReplVO> replInfoListWithVersion = dmfcstIctCodeReplInfoDao.findReplInfoList(replaceSearchVO);
        updateReplData(replaceSearchVO, replInfoListWithVersion);

        return ResultDataVO.success();
    }

    @JalorOperation(code = "deleteReplInfoList", desc = "删除新旧编码")
    @Audit(module = "replaceManagementService-deleteReplInfoList", operation = "deleteReplInfoList", message = "删除新旧编码")
    @Override
    public ResultDataVO deleteReplInfoList(ReplaceSearchVO replaceSearchVO) throws CommonApplicationException {

        List<DmFocReplVO> replaceList = replaceSearchVO.getReplaceList();
        Set<String> replaceRelationName = replaceList.stream().map(DmFocReplVO::getReplaceRelationName).collect(Collectors.toSet());
        replaceSearchVO.setReplaceRelationNameList(replaceRelationName);
        // 排除掉传入的替换关系名称，即是保留下来未删除的替换关系名称
        List<DmFocReplVO> replInfoList = dmfcstIctCodeReplInfoDao.findReplInfoList(replaceSearchVO);
        Long userId = UserInfoUtils.getUserId();
        DmFcstVersionInfoDTO versionInfoDTO = createReplaceDimVersion(userId);
        setReplColumn(replInfoList, versionInfoDTO.getVersionId(), userId);
        if (CollectionUtils.isNotEmpty(replInfoList)) {
            dmfcstIctCodeReplInfoDao.createDmCodeReplInfoList(replInfoList);
        }
        return ResultDataVO.success();
    }

    @JalorOperation(code = "replinfoExport", desc = "导出新旧编码")
    @Audit(module = "replaceManagementService-replinfoExport", operation = "replinfoExport", message = "导出新旧编码")
    @Override
    public ResultDataVO replinfoExport(HttpServletResponse response, ReplaceSearchVO replaceSearchVO) throws ApplicationException {
        IExcelTemplateBeanManager templateBeanManager = ReplaceTemplateEnum.getByCode("01", "");
        Map<String, Object> parameters = new HashMap<>();
        String module = "成本指数-ICT-新旧编码替换";
        parameters.put("exportModuleName", module);
        parameters.put("exportFileName", replaceSearchVO.getFileName());
        iExportProcessorService.fillEasyExcelExport(response, templateBeanManager, replaceSearchVO, parameters);
        return ResultDataVO.success();

    }

    @Override
    @JalorOperation(code = "importReplaceInfoList", desc = "导入新旧编码")
    @Audit(module = "replaceManagementService-importReplaceInfoList", operation = "importReplaceInfoList", message = "导入新旧编码")
    public ResultDataVO importReplaceInfoList(Attachment attachment, Long versionId) throws Exception {
        log.info(">>>Begin ReplaceManagementService::importReplaceInfoList");
        if (ObjectUtils.isEmpty(versionId)) {
            throw new CommonApplicationException("版本ID不能为空！");
        }
        // 校验导入的Excel文件格式和文件大小
        excelUtils.verifyExcelFile(attachment, CommonConstant.REPLACE_DIM_TYPE);
        // 保存请求上下文信息
        IRequestContext context = RequestContextManager.getCurrent();
        // 设置任务刷新状态
        DmFcstDataRefreshStatus dataRefreshStatus = ictCommonService.saveDataRefreshStatus("REPLACE_DIM_IMPORT");
        // 获取模块类型和导入数量限制
        Map<String, Object> params = ictCommonService.getHeaderMap(CommonConstant.REPLACE_DIM_TYPE);
        // 导入信息记录对象VO
        UploadInfoVO uploadInfoVO = FcstIndustryUtil.getUploadInfoVO(attachment, versionId, params, CommonConstant.REPLACE_DIM_TYPE);
        ImportContextVO importContextVO = ImportContextVO.builder().versionId(versionId).context(context)
                .userId(UserInfoUtils.getUserId()).userCn(UserInfoUtils.getUserCn())
                .dataRefreshStatus(dataRefreshStatus)
                .uploadInfoVO(uploadInfoVO).build();
        asyncIctConfigService.importReplaceInfoList(attachment, importContextVO);
        return ResultDataVO.success(dataRefreshStatus);
    }

    private void updateReplData(ReplaceSearchVO replaceSearchVO,
                                List<DmFocReplVO> replInfoListWithVersion) throws CommonApplicationException {

        List<DmFocReplVO> replaceList = replaceSearchVO.getReplaceList();
        Long userId = UserInfoUtils.getUserId();
        DmFcstVersionInfoDTO versionInfoDTO = createReplaceDimVersion(userId);

        List<Long> idList = replaceList.stream().map(DmFocReplVO::getId).collect(Collectors.toList());
        idList.remove(null);
        // 原版本需要剔除正在编辑的记录
        List<DmFocReplVO> originList  = new ArrayList<>();
        if (!idList.isEmpty()) {
            originList = replInfoListWithVersion.stream().filter(info -> !idList.contains(info.getId())).collect(Collectors.toList());
        } else {
            originList.addAll(replInfoListWithVersion);
        }
        replaceList.addAll(originList);
        // 把所有的list校验是否重复
        checkRepeatReplData(replaceList, replaceSearchVO);

        setReplColumn(replaceList, versionInfoDTO.getVersionId(), userId);
        dmfcstIctCodeReplInfoDao.createDmCodeReplInfoList(replaceList);
    }

    private void trimReplParamSpace(List<DmFocReplVO> dmFocReplVOList) {
        dmFocReplVOList.stream().forEach(replVO -> {

            if (StringUtils.isNotBlank(replVO.getProdCnName())) {
                replVO.setProdCnName(replVO.getProdCnName().trim());
            }
            if (StringUtils.isNotBlank(replVO.getReplaceRelationName())) {
                replVO.setReplaceRelationName(replVO.getReplaceRelationName().trim());
            }
            if (StringUtils.isNotBlank(replVO.getOldSpartDesc())) {
                replVO.setOldSpartDesc(replVO.getOldSpartDesc().trim());
            }
            if (StringUtils.isNotBlank(replVO.getNewSpartDesc())) {
                replVO.setNewSpartDesc(replVO.getNewSpartDesc().trim());
            }
        });
    }

    private void checkReplData(List<DmFocReplVO> dmFocReplVOList)
            throws CommonApplicationException {
        StringBuilder builder = new StringBuilder();
        validSpecialStr(dmFocReplVOList, builder);
        if (ObjectUtils.isNotEmpty(builder)) {
            throw new CommonApplicationException("检测到" + builder);
        }
        // 校验输入的产品名称是否在产品维表存在
        StringBuilder productBuilder = new StringBuilder();
        validProdName(dmFocReplVOList, productBuilder);
        if (ObjectUtils.isNotEmpty(productBuilder)) {
            throw new CommonApplicationException("检测到" + productBuilder);
        }
    }

    private void checkRepeatReplData(List<DmFocReplVO> dmFocReplVOList, ReplaceSearchVO replaceSearchVO)
            throws CommonApplicationException {
        // 重复替换关系名称校验
        StringBuilder stBuilder = new StringBuilder();
        validRepeatName(dmFocReplVOList, stBuilder, replaceSearchVO);
        if (ObjectUtils.isNotEmpty(stBuilder)) {
            throw new CommonApplicationException("检测到" + stBuilder);
        }
    }

    private DmFcstVersionInfoDTO createReplaceDimVersion(Long userId) {
        DmFcstVersionInfoDTO versionInfoDTO = new DmFcstVersionInfoDTO();
        versionInfoDTO.setVersionId(dmFcstVersionInfoDao.getVersionKey());
        versionInfoDTO.setVersion(gengerateVerionName());
        versionInfoDTO.setVersionType("ADJUST");
        versionInfoDTO.setStatus(0L);
        versionInfoDTO.setDataType("REPLACE_DIM");

        versionInfoDTO.setCreatedBy(userId);
        versionInfoDTO.setCreationDate(new Date());
        versionInfoDTO.setLastUpdatedBy(userId);
        versionInfoDTO.setLastUpdateDate(new Date());
        versionInfoDTO.setDelFlag("N");
        versionInfoDTO.setIsRunning("N");
        dmFcstVersionInfoDao.createDmFcstVersionInfoDTO(versionInfoDTO);
        return versionInfoDTO;
    }

    private void setReplColumn(List<DmFocReplVO> replInfoListWithVersion, Long versionId, Long userId) {
        // 循环集合设置版本信息
        replInfoListWithVersion.stream().forEach(dto -> {
            dto.setId(dmfcstIctCodeReplInfoDao.getReplAutoKey());
            dto.setVersionId(versionId);
            dto.setGtsType("PROD");
            dto.setCreatedBy(userId);
            dto.setCreationDate(new Date());
            dto.setLastUpdatedBy(userId);
            dto.setLastUpdateDate(new Date());
            dto.setDelFlag("N");
        });
    }

    private String gengerateVerionName() {
        String versionName = null;
        DmFcstVersionInfoDTO versionDto = new DmFcstVersionInfoDTO();
        versionDto.setLastUpdateStr(DateUtil.today());
        versionDto.setDataType("REPLACE_DIM");
        List<DmFcstVersionInfoDTO> planVersionMap = dmFcstVersionInfoDao.findPlanVersionList(versionDto);
        String dateStr = DateUtil.today().replace("-", "");
        if (CollectionUtils.isEmpty(planVersionMap)) {
            versionName = dateStr + "-001";
        } else {
            if (StringUtils.isNotEmpty(planVersionMap.get(0).getVersion())) {
                versionName = getVersionNameSub(planVersionMap.get(0).getVersion(), dateStr);
            } else {
                versionName = dateStr + "-001";
            }
        }
        return versionName;
    }

    private String getVersionNameSub(String version, String dateStr) {
        String versionNameStr;
        if (version.contains("-")) {
            String versionNum = version.substring(version.indexOf("-") + 1);
            if (PATTERN.matcher(versionNum).matches()) {
                if (NumberUtil.parseInt(versionNum) >= 9) {
                    versionNameStr = dateStr + "-0" + (NumberUtil.parseInt(versionNum) + 1);
                } else {
                    versionNameStr = dateStr + "-00" + (NumberUtil.parseInt(versionNum) + 1);
                }
            } else {
                versionNameStr = dateStr + "-001";
            }
        } else {
            versionNameStr = dateStr + "-001";
        }
        return versionNameStr;
    }

    private void validSpecialStr(List<DmFocReplVO> dmFocReplVOList, StringBuilder stringBuilder) {
        dmFocReplVOList.stream().forEach(dm -> {
            if (FcstIndustryUtil.verifySpecialCharacter(dm.getProdCnName())) {
                stringBuilder.append("产品名称含有特殊字符，请检查;");
            }
            if (FcstIndustryUtil.verifySpecialCharacter(dm.getReplaceRelationName())) {
                stringBuilder.append("替换关系名称含有特殊字符，请检查;");
            }
        });
    }

    private void validRepeatName(List<DmFocReplVO> dmFocReplVOList, StringBuilder stringBuilder, ReplaceSearchVO replaceSearchVO) {
        Map<String, List<DmFocReplVO>> repeatNameList = dmFocReplVOList.stream()
                .collect(Collectors.groupingBy(
                        repl -> repl.getReplaceRelationName()));
        List<String> count = repeatNameList.keySet()
                .stream()
                .filter(key -> repeatNameList.get(key).size() > 1)
                .distinct()
                .collect(Collectors.toList());
        if (count.size() > 0) {
            stringBuilder.append("保存的替换关系名称有重复，请检查");
        }
    }

    private void validProdName(List<DmFocReplVO> dmFocReplVOList, StringBuilder stringBuilder) {
        // 校验入参的产品名称是否存在
        Set<String> prodCnNameList = dmFocReplVOList.stream().filter(dm->StringUtils.isNotBlank(dm.getProdCnName())).map(DmFocReplVO::getProdCnName).collect(Collectors.toSet());
        prodCnNameList.remove(null);
        if (CollectionUtils.isNotEmpty(prodCnNameList)) {
            List<DmFocReplVO> productProdList = dmfcstIctCodeReplInfoDao.findProductProdCode(prodCnNameList);
            Set<String> productNameList = productProdList.stream().map(DmFocReplVO::getProdCnName).collect(Collectors.toSet());

            dmFocReplVOList.stream().forEach(dm -> {
                if (!productNameList.contains(dm.getProdCnName())) {
                    stringBuilder.append("产品名称:" + dm.getProdCnName() + "不存在;");
                }
                // 设置产品编码
                productProdList.forEach(product -> {
                    if (dm.getProdCnName().equals(product.getProdCnName())) {
                        dm.setProdCode(product.getProdCode());
                    }
                });
            });
        }
    }
}
