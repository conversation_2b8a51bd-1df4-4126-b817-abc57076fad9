/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.relation;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

/**
 * DmDimFoiItemCatgModlCegVOTest Class
 *
 * <AUTHOR>
 * @since 2023/4/23
 */
public class DmDimFoiItemCatgModlCegVOTest extends BaseVOCoverUtilsTest<DmDimFoiItemCatgModlCegVO> {

    @Override
    protected Class<DmDimFoiItemCatgModlCegVO> getTClass() {
        return DmDimFoiItemCatgModlCegVO.class;
    }

    @Test
    public void testMethod() {
        DmDimFoiItemCatgModlCegVO dimensionParamVO = new DmDimFoiItemCatgModlCegVO();
        dimensionParamVO.setVersionId(100L);
        dimensionParamVO.getVersionId();
        dimensionParamVO.setL2CegCode("2331L");
        dimensionParamVO.setL3CegCode("2331L");
        dimensionParamVO.setL4CegCode("2331L");
        dimensionParamVO.getL2CegCode();
        dimensionParamVO.getL3CegCode();
        dimensionParamVO.getL4CegCode();
        dimensionParamVO.getL3CegShortCnName();
        dimensionParamVO.setL2CegCnName("2331L");
        dimensionParamVO.setL3CegCnName("2331L");
        dimensionParamVO.setL4CegCnName("2331L");
        dimensionParamVO.setL4CegShortCnName("444");
        dimensionParamVO.getL4CegShortCnName();
        dimensionParamVO.getL2CegCnName();
        dimensionParamVO.getL3CegCnName();
        dimensionParamVO.getL4CegCnName();
        dimensionParamVO.setCategoryCode("111");
        dimensionParamVO.getCategoryCode();
        dimensionParamVO.setCategoryName("test");
        dimensionParamVO.getCategoryName();
        dimensionParamVO.setItemCode("item");
        dimensionParamVO.getItemCode();
        dimensionParamVO.setItemName("IName");
        dimensionParamVO.getItemName();
        dimensionParamVO.setGroupLevel("LV3");
        dimensionParamVO.getGroupLevel();
        dimensionParamVO.setL3CegShortCnName("short");
        Assert.assertNotNull(dimensionParamVO);
    }
}