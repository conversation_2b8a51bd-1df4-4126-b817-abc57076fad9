<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding">
    <file url="file://$PROJECT_DIR$/opt.fcst.industry.index.parent/opt.fcst.industry.common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/opt.fcst.industry.index.parent/opt.fcst.industry.common/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/opt.fcst.industry.index.parent/opt.fcst.industry.index.api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/opt.fcst.industry.index.parent/opt.fcst.industry.index.api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/opt.fcst.industry.index.parent/opt.fcst.industry.index.impl/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/opt.fcst.industry.index.parent/opt.fcst.industry.index.impl/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/opt.fcst.industry.index.parent/opt.fcst.industry.index.start/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/opt.fcst.industry.index.parent/opt.fcst.industry.index.start/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/opt.fcst.industry.index.parent/opt.fcst.industry.pbi.api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/opt.fcst.industry.index.parent/opt.fcst.industry.pbi.api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/opt.fcst.industry.index.parent/opt.fcst.industry.pbi.impl/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/opt.fcst.industry.index.parent/opt.fcst.industry.pbi.impl/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/opt.fcst.industry.index.parent/opt.fcst.industry.price.api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/opt.fcst.industry.index.parent/opt.fcst.industry.price.api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/opt.fcst.industry.index.parent/opt.fcst.industry.price.impl/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/opt.fcst.industry.index.parent/opt.fcst.industry.price.impl/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/opt.fcst.industry.index.parent/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/opt.fcst.industry.index.parent/src/main/resources" charset="UTF-8" />
  </component>
</project>