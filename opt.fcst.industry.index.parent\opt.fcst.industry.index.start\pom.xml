<?xml version="1.0" encoding="UTF-8"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<artifactId>opt.fcst.industry.index.start</artifactId>
	<parent>
		<groupId>com.huawei.it.fcst</groupId>
		<artifactId>opt.fcst.industry.index.parent</artifactId>
		<version>1.0-SNAPSHOT</version>
	</parent>

	<properties>
		<subAppName>fcst_industry_index_service</subAppName>
		<maven.build.timestamp.format>yyyyMMddhhmm</maven.build.timestamp.format>
	</properties>

	<dependencies>
		<!-- fcst index Api -->
		<dependency>
			<groupId>com.huawei.it.fcst</groupId>
			<artifactId>opt.fcst.industry.index.api</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
		<!-- fcst index Api Impl -->
		<dependency>
			<groupId>com.huawei.it.fcst</groupId>
			<artifactId>opt.fcst.industry.index.impl</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.huawei.it.fcst</groupId>
			<artifactId>opt.fcst.industry.pbi.api</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.huawei.it.fcst</groupId>
			<artifactId>opt.fcst.industry.pbi.impl</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.huawei.it.fcst</groupId>
			<artifactId>opt.fcst.industry.price.api</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.huawei.it.fcst</groupId>
			<artifactId>opt.fcst.industry.price.impl</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
	</dependencies>

	<profiles>
		<!-- 动态环境配置 -->
		<profile>
			<id>online</id>
			<properties>
				<spring_profiles_id_name>online</spring_profiles_id_name>
				<application_subAppId>${subAppName}</application_subAppId>
				<fox_registry_eureka_serviceurl_default>http://vega-kwe.huawei.com/msa/register/v2/</fox_registry_eureka_serviceurl_default>
			</properties>
			<!-- 默认启动配置 -->
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
		</profile>
		<!-- 生产配置 -->
		<profile>
			<id>production</id>
			<properties>
				<spring_profiles_id_name>production</spring_profiles_id_name>
				<application_subAppId>${subAppName}</application_subAppId>
				<fox_registry_eureka_serviceurl_default>http://vega.huawei.com/msa/register/v2/</fox_registry_eureka_serviceurl_default>
			</properties>
		</profile>
	</profiles>

	<build>
		<finalName>${subAppName}_${spring_profiles_id_name}_${maven.build.timestamp}</finalName>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>true</filtering>
				<includes>
					<include>**/*.properties</include>
					<include>**/*.xml</include>
					<include>**/*.conf</include>
				</includes>
			</resource>
		</resources>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<mainClass>com.huawei.it.fcst.industry.index.IndustryIndexApplication</mainClass>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-assembly-plugin</artifactId>
				<version>2.4</version>
				<configuration>
					<appendAssemblyId>false</appendAssemblyId>
					<descriptors>
						<descriptor>src/main/assembly/descriptor.xml</descriptor>
					</descriptors>
				</configuration>
				<executions>
					<execution>
						<id>make-assembly</id>
						<phase>package</phase>
						<goals>
							<goal>single</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>