/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2018-2019. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.utils;

import cn.hutool.core.util.NumberUtil;
import com.huawei.it.fcst.industry.index.constant.CommonConstant;
import com.huawei.it.fcst.industry.index.constant.Constant;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumD;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumP;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumU;
import com.huawei.it.fcst.industry.index.enums.IndustryConst;
import com.huawei.it.fcst.industry.index.enums.IndustryIndexEnum;
import com.huawei.it.fcst.industry.index.vo.common.AbstractExcelTitleVO;
import com.huawei.it.fcst.industry.index.vo.common.HeaderVo;
import com.huawei.it.fcst.industry.index.vo.common.LeafExcelTitleVO;
import com.huawei.it.fcst.industry.index.vo.config.HistoryInputVO;
import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;
import com.huawei.it.fcst.industry.index.vo.standard.StandardAnalysisVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.stream.Stream;

/**
 * ParseData.
 *
 * <AUTHOR>
 * @since 2019年12月16日
 */
public class FcstIndexUtil {
    /** LOG 日志. */
    private static final Logger LOGGER = LoggerFactory.getLogger(FcstIndexUtil.class);

    // group层级下一层级映射，视角5
    private static Map<String, String> nextFiveGroupLevelMapProfit = new HashMap<>(8);

    static {
        nextFiveGroupLevelMapProfit.put(GroupLevelEnumP.LV0.getValue(), GroupLevelEnumP.LV1.getValue());
        nextFiveGroupLevelMapProfit.put(GroupLevelEnumP.LV1.getValue(), GroupLevelEnumP.LV2.getValue());
        nextFiveGroupLevelMapProfit.put(GroupLevelEnumP.LV2.getValue(), GroupLevelEnumP.L1.getValue());
        nextFiveGroupLevelMapProfit.put(GroupLevelEnumP.L1.getValue(), GroupLevelEnumP.L2.getValue());
        nextFiveGroupLevelMapProfit.put(GroupLevelEnumP.L2.getValue(), GroupLevelEnumP.CEG.getValue());
        nextFiveGroupLevelMapProfit.put(GroupLevelEnumP.CEG.getValue(), GroupLevelEnumP.MODL.getValue());
        nextFiveGroupLevelMapProfit.put(GroupLevelEnumP.MODL.getValue(), GroupLevelEnumP.CATEGORY.getValue());
        nextFiveGroupLevelMapProfit.put(GroupLevelEnumP.CATEGORY.getValue(), GroupLevelEnumP.ITEM.getValue());
    }

    // group层级下一层级映射，视角4
    private static Map<String, String> nextFourGroupLevelMapProfit = new HashMap<>(7);

    static {
        nextFourGroupLevelMapProfit.put(GroupLevelEnumP.LV0.getValue(), GroupLevelEnumP.LV1.getValue());
        nextFourGroupLevelMapProfit.put(GroupLevelEnumP.LV1.getValue(), GroupLevelEnumP.LV2.getValue());
        nextFourGroupLevelMapProfit.put(GroupLevelEnumP.LV2.getValue(), GroupLevelEnumP.L1.getValue());
        nextFourGroupLevelMapProfit.put(GroupLevelEnumP.L1.getValue(), GroupLevelEnumP.CEG.getValue());
        nextFourGroupLevelMapProfit.put(GroupLevelEnumP.CEG.getValue(), GroupLevelEnumP.MODL.getValue());
        nextFourGroupLevelMapProfit.put(GroupLevelEnumP.MODL.getValue(), GroupLevelEnumP.CATEGORY.getValue());
        nextFourGroupLevelMapProfit.put(GroupLevelEnumP.CATEGORY.getValue(), GroupLevelEnumP.ITEM.getValue());
    }

    // group层级下一层级映射，反向视角7
    private static Map<String, String> nextSevenGroupLevelMap = new HashMap<>(7);

    static {
        nextSevenGroupLevelMap.put(GroupLevelEnumU.LV0.getValue(), GroupLevelEnumU.CEG.getValue());
        nextSevenGroupLevelMap.put(GroupLevelEnumU.CEG.getValue(), GroupLevelEnumU.MODL.getValue());
        nextSevenGroupLevelMap.put(GroupLevelEnumU.MODL.getValue(), GroupLevelEnumU.CATEGORY.getValue());
        nextSevenGroupLevelMap.put(GroupLevelEnumU.CATEGORY.getValue(), GroupLevelEnumU.LV1.getValue());
        nextSevenGroupLevelMap.put(GroupLevelEnumU.LV1.getValue(), GroupLevelEnumU.LV2.getValue());
        nextSevenGroupLevelMap.put(GroupLevelEnumU.LV2.getValue(), GroupLevelEnumU.LV3.getValue());
        nextSevenGroupLevelMap.put(GroupLevelEnumU.LV3.getValue(), GroupLevelEnumU.LV4.getValue());
    }


    // group层级下一层级映射，反向视角6
    private static Map<String, String> nextSixGroupLevelMap = new HashMap<>(6);

    static {
        nextSixGroupLevelMap.put(GroupLevelEnumU.LV0.getValue(), GroupLevelEnumU.CEG.getValue());
        nextSixGroupLevelMap.put(GroupLevelEnumU.CEG.getValue(), GroupLevelEnumU.MODL.getValue());
        nextSixGroupLevelMap.put(GroupLevelEnumU.MODL.getValue(), GroupLevelEnumU.LV1.getValue());
        nextSixGroupLevelMap.put(GroupLevelEnumU.LV1.getValue(), GroupLevelEnumU.LV2.getValue());
        nextSixGroupLevelMap.put(GroupLevelEnumU.LV2.getValue(), GroupLevelEnumU.LV3.getValue());
        nextSixGroupLevelMap.put(GroupLevelEnumU.LV3.getValue(), GroupLevelEnumU.LV4.getValue());
    }

    // group层级下一层级映射，反向视角5
    private static Map<String, String> nextFiveGroupLevelMap = new HashMap<>(5);

    static {
        nextFiveGroupLevelMap.put(GroupLevelEnumU.LV0.getValue(), GroupLevelEnumU.CEG.getValue());
        nextFiveGroupLevelMap.put(GroupLevelEnumU.CEG.getValue(), GroupLevelEnumU.LV1.getValue());
        nextFiveGroupLevelMap.put(GroupLevelEnumU.LV1.getValue(), GroupLevelEnumU.LV2.getValue());
        nextFiveGroupLevelMap.put(GroupLevelEnumU.LV2.getValue(), GroupLevelEnumU.LV3.getValue());
        nextFiveGroupLevelMap.put(GroupLevelEnumU.LV3.getValue(), GroupLevelEnumU.LV4.getValue());
    }

    // group层级下一层级映射，视角5(IAS)
    private static Map<String, String> nextFiveGroupLevelMapIas = new HashMap<>(8);

    static {
        nextFiveGroupLevelMapIas.put(GroupLevelEnumU.LV0.getValue(), GroupLevelEnumU.LV1.getValue());
        nextFiveGroupLevelMapIas.put(GroupLevelEnumU.LV1.getValue(), GroupLevelEnumU.LV2.getValue());
        nextFiveGroupLevelMapIas.put(GroupLevelEnumU.LV2.getValue(), GroupLevelEnumU.LV3.getValue());
        nextFiveGroupLevelMapIas.put(GroupLevelEnumU.LV3.getValue(), GroupLevelEnumU.LV4.getValue());
        nextFiveGroupLevelMapIas.put(GroupLevelEnumU.LV4.getValue(), GroupLevelEnumU.CEG.getValue());
        nextFiveGroupLevelMapIas.put(GroupLevelEnumU.CEG.getValue(), GroupLevelEnumU.MODL.getValue());
        nextFiveGroupLevelMapIas.put(GroupLevelEnumU.MODL.getValue(), GroupLevelEnumU.CATEGORY.getValue());
        nextFiveGroupLevelMapIas.put(GroupLevelEnumU.CATEGORY.getValue(), GroupLevelEnumU.ITEM.getValue());
    }

    // group层级下一层级映射，视角4
    private static Map<String, String> nextFourGroupLevelMap = new HashMap<>(7);

    static {
        nextFourGroupLevelMap.put(GroupLevelEnumU.LV0.getValue(), GroupLevelEnumU.LV1.getValue());
        nextFourGroupLevelMap.put(GroupLevelEnumU.LV1.getValue(), GroupLevelEnumU.LV2.getValue());
        nextFourGroupLevelMap.put(GroupLevelEnumU.LV2.getValue(), GroupLevelEnumU.LV3.getValue());
        nextFourGroupLevelMap.put(GroupLevelEnumU.LV3.getValue(), GroupLevelEnumU.CEG.getValue());
        nextFourGroupLevelMap.put(GroupLevelEnumU.CEG.getValue(), GroupLevelEnumU.MODL.getValue());
        nextFourGroupLevelMap.put(GroupLevelEnumU.MODL.getValue(), GroupLevelEnumU.CATEGORY.getValue());
        nextFourGroupLevelMap.put(GroupLevelEnumU.CATEGORY.getValue(), GroupLevelEnumU.ITEM.getValue());
    }

    // group层级下一层级映射，视角3
    private static Map<String, String> nextThreeGroupLevelMap1 = new HashMap<>(6);

    static {
        nextThreeGroupLevelMap1.put(GroupLevelEnumU.LV0.getValue(), GroupLevelEnumU.LV1.getValue());
        nextThreeGroupLevelMap1.put(GroupLevelEnumU.LV1.getValue(), GroupLevelEnumU.LV2.getValue());
        nextThreeGroupLevelMap1.put(GroupLevelEnumU.LV2.getValue(), GroupLevelEnumU.CEG.getValue());
        nextThreeGroupLevelMap1.put(GroupLevelEnumU.CEG.getValue(), GroupLevelEnumU.MODL.getValue());
        nextThreeGroupLevelMap1.put(GroupLevelEnumU.MODL.getValue(), GroupLevelEnumU.CATEGORY.getValue());
        nextThreeGroupLevelMap1.put(GroupLevelEnumU.CATEGORY.getValue(), GroupLevelEnumU.ITEM.getValue());
    }

    // group层级下一层级映射，视角1
    private static Map<String, String> nextOneGroupLvlMap = new HashMap<>(4);

    static {
        nextOneGroupLvlMap.put(GroupLevelEnumU.LV0.getValue(), GroupLevelEnumU.CEG.getValue());
        nextOneGroupLvlMap.put(GroupLevelEnumU.CEG.getValue(), GroupLevelEnumU.MODL.getValue());
        nextOneGroupLvlMap.put(GroupLevelEnumU.MODL.getValue(), GroupLevelEnumU.CATEGORY.getValue());
        nextOneGroupLvlMap.put(GroupLevelEnumU.CATEGORY.getValue(), GroupLevelEnumU.ITEM.getValue());
    }

    // group层级下一层级映射，视角2
    private static Map<String, String> nextTwoGroupLvlMap = new HashMap<>(5);

    static {
        nextTwoGroupLvlMap.put(GroupLevelEnumU.LV0.getValue(), GroupLevelEnumU.LV1.getValue());
        nextTwoGroupLvlMap.put(GroupLevelEnumU.LV1.getValue(), GroupLevelEnumU.CEG.getValue());
        nextTwoGroupLvlMap.put(GroupLevelEnumU.CEG.getValue(), GroupLevelEnumU.MODL.getValue());
        nextTwoGroupLvlMap.put(GroupLevelEnumU.MODL.getValue(), GroupLevelEnumU.CATEGORY.getValue());
        nextTwoGroupLvlMap.put(GroupLevelEnumU.CATEGORY.getValue(), GroupLevelEnumU.ITEM.getValue());
    }

    // 量纲颗粒度group层级下一层级映射，视角1
    private static Map<String, String> nextDimensionOneGroupLvlMap = new HashMap<>(6);
    static {
        nextDimensionOneGroupLvlMap.put(GroupLevelEnumD.LV0.getValue(), GroupLevelEnumD.LV1.getValue());
        nextDimensionOneGroupLvlMap.put(GroupLevelEnumD.LV1.getValue(), GroupLevelEnumD.DIMENSION.getValue());
        nextDimensionOneGroupLvlMap.put(GroupLevelEnumD.DIMENSION.getValue(), GroupLevelEnumD.CEG.getValue());
        nextDimensionOneGroupLvlMap.put(GroupLevelEnumD.CEG.getValue(), GroupLevelEnumD.MODL.getValue());
        nextDimensionOneGroupLvlMap.put(GroupLevelEnumD.MODL.getValue(), GroupLevelEnumD.CATEGORY.getValue());
        nextDimensionOneGroupLvlMap.put(GroupLevelEnumD.CATEGORY.getValue(), GroupLevelEnumD.ITEM.getValue());
    }

    // 量纲颗粒度group层级下一层级映射，视角2
    private static Map<String, String> nextDimensionTwoGroupLvlMap = new HashMap<>(7);
    static {
        nextDimensionTwoGroupLvlMap.put(GroupLevelEnumD.LV0.getValue(), GroupLevelEnumD.LV1.getValue());
        nextDimensionTwoGroupLvlMap.put(GroupLevelEnumD.LV1.getValue(), GroupLevelEnumD.DIMENSION.getValue());
        nextDimensionTwoGroupLvlMap.put(GroupLevelEnumD.DIMENSION.getValue(), GroupLevelEnumD.SUBCATEGORY.getValue());
        nextDimensionTwoGroupLvlMap.put(GroupLevelEnumD.SUBCATEGORY.getValue(), GroupLevelEnumD.CEG.getValue());
        nextDimensionTwoGroupLvlMap.put(GroupLevelEnumD.CEG.getValue(), GroupLevelEnumD.MODL.getValue());
        nextDimensionTwoGroupLvlMap.put(GroupLevelEnumD.MODL.getValue(), GroupLevelEnumD.CATEGORY.getValue());
        nextDimensionTwoGroupLvlMap.put(GroupLevelEnumD.CATEGORY.getValue(), GroupLevelEnumD.ITEM.getValue());
    }

    // 量纲颗粒度group层级下一层级映射，视角3
    private static Map<String, String> nextDimensionThreeGroupLvlMap = new HashMap<>(8);
    static {
        nextDimensionThreeGroupLvlMap.put(GroupLevelEnumD.LV0.getValue(), GroupLevelEnumD.LV1.getValue());
        nextDimensionThreeGroupLvlMap.put(GroupLevelEnumD.LV1.getValue(), GroupLevelEnumD.DIMENSION.getValue());
        nextDimensionThreeGroupLvlMap.put(GroupLevelEnumD.DIMENSION.getValue(), GroupLevelEnumD.SUBCATEGORY.getValue());
        nextDimensionThreeGroupLvlMap.put(GroupLevelEnumD.SUBCATEGORY.getValue(), GroupLevelEnumD.SUB_DETAIL.getValue());
        nextDimensionThreeGroupLvlMap.put(GroupLevelEnumD.SUB_DETAIL.getValue(), GroupLevelEnumD.CEG.getValue());
        nextDimensionThreeGroupLvlMap.put(GroupLevelEnumD.CEG.getValue(), GroupLevelEnumD.MODL.getValue());
        nextDimensionThreeGroupLvlMap.put(GroupLevelEnumD.MODL.getValue(), GroupLevelEnumD.CATEGORY.getValue());
        nextDimensionThreeGroupLvlMap.put(GroupLevelEnumD.CATEGORY.getValue(), GroupLevelEnumD.ITEM.getValue());
    }

    // 量纲颗粒度group层级下一层级映射，视角10
    private static Map<String, String> nextDimensionTenLvlMap = new HashMap<>(9);
    static {
        nextDimensionTenLvlMap.put(GroupLevelEnumD.LV0.getValue(), GroupLevelEnumD.LV1.getValue());
        nextDimensionTenLvlMap.put(GroupLevelEnumD.LV1.getValue(), GroupLevelEnumD.DIMENSION.getValue());
        nextDimensionTenLvlMap.put(GroupLevelEnumD.DIMENSION.getValue(), GroupLevelEnumD.SUBCATEGORY.getValue());
        nextDimensionTenLvlMap.put(GroupLevelEnumD.SUBCATEGORY.getValue(), GroupLevelEnumD.SUB_DETAIL.getValue());
        nextDimensionTenLvlMap.put(GroupLevelEnumD.SUB_DETAIL.getValue(), GroupLevelEnumD.SPART.getValue());
        nextDimensionTenLvlMap.put(GroupLevelEnumD.SPART.getValue(), GroupLevelEnumD.CEG.getValue());
        nextDimensionTenLvlMap.put(GroupLevelEnumD.CEG.getValue(), GroupLevelEnumD.MODL.getValue());
        nextDimensionTenLvlMap.put(GroupLevelEnumD.MODL.getValue(), GroupLevelEnumD.CATEGORY.getValue());
        nextDimensionTenLvlMap.put(GroupLevelEnumD.CATEGORY.getValue(), GroupLevelEnumD.ITEM.getValue());
    }

    // 量纲颗粒度group层级下一层级映射，视角4
    private static Map<String, String> nextDimensionFourGroupLvlMap = new HashMap<>(7);
    static {
        nextDimensionFourGroupLvlMap.put(GroupLevelEnumD.LV0.getValue(), GroupLevelEnumD.LV1.getValue());
        nextDimensionFourGroupLvlMap.put(GroupLevelEnumD.LV1.getValue(), GroupLevelEnumD.LV2.getValue());
        nextDimensionFourGroupLvlMap.put(GroupLevelEnumD.LV2.getValue(), GroupLevelEnumD.DIMENSION.getValue());
        nextDimensionFourGroupLvlMap.put(GroupLevelEnumD.DIMENSION.getValue(), GroupLevelEnumD.CEG.getValue());
        nextDimensionFourGroupLvlMap.put(GroupLevelEnumD.CEG.getValue(), GroupLevelEnumD.MODL.getValue());
        nextDimensionFourGroupLvlMap.put(GroupLevelEnumD.MODL.getValue(), GroupLevelEnumD.CATEGORY.getValue());
        nextDimensionFourGroupLvlMap.put(GroupLevelEnumD.CATEGORY.getValue(), GroupLevelEnumD.ITEM.getValue());
    }

    // 量纲颗粒度group层级下一层级映射，视角5
    private static Map<String, String> nextDimensionFiveGroupLvlMap = new HashMap<>(8);
    static {
        nextDimensionFiveGroupLvlMap.put(GroupLevelEnumD.LV0.getValue(), GroupLevelEnumD.LV1.getValue());
        nextDimensionFiveGroupLvlMap.put(GroupLevelEnumD.LV1.getValue(), GroupLevelEnumD.LV2.getValue());
        nextDimensionFiveGroupLvlMap.put(GroupLevelEnumD.LV2.getValue(), GroupLevelEnumD.DIMENSION.getValue());
        nextDimensionFiveGroupLvlMap.put(GroupLevelEnumD.DIMENSION.getValue(), GroupLevelEnumD.SUBCATEGORY.getValue());
        nextDimensionFiveGroupLvlMap.put(GroupLevelEnumD.SUBCATEGORY.getValue(), GroupLevelEnumD.CEG.getValue());
        nextDimensionFiveGroupLvlMap.put(GroupLevelEnumD.CEG.getValue(), GroupLevelEnumD.MODL.getValue());
        nextDimensionFiveGroupLvlMap.put(GroupLevelEnumD.MODL.getValue(), GroupLevelEnumD.CATEGORY.getValue());
        nextDimensionFiveGroupLvlMap.put(GroupLevelEnumD.CATEGORY.getValue(), GroupLevelEnumD.ITEM.getValue());
    }

    // 量纲颗粒度group层级下一层级映射，视角6
    private static Map<String, String> nextDimensionSixGroupLvlMap = new HashMap<>(9);
    static {
        nextDimensionSixGroupLvlMap.put(GroupLevelEnumD.LV0.getValue(), GroupLevelEnumD.LV1.getValue());
        nextDimensionSixGroupLvlMap.put(GroupLevelEnumD.LV1.getValue(), GroupLevelEnumD.LV2.getValue());
        nextDimensionSixGroupLvlMap.put(GroupLevelEnumD.LV2.getValue(), GroupLevelEnumD.DIMENSION.getValue());
        nextDimensionSixGroupLvlMap.put(GroupLevelEnumD.DIMENSION.getValue(), GroupLevelEnumD.SUBCATEGORY.getValue());
        nextDimensionSixGroupLvlMap.put(GroupLevelEnumD.SUBCATEGORY.getValue(), GroupLevelEnumD.SUB_DETAIL.getValue());
        nextDimensionSixGroupLvlMap.put(GroupLevelEnumD.SUB_DETAIL.getValue(), GroupLevelEnumD.CEG.getValue());
        nextDimensionSixGroupLvlMap.put(GroupLevelEnumD.CEG.getValue(), GroupLevelEnumD.MODL.getValue());
        nextDimensionSixGroupLvlMap.put(GroupLevelEnumD.MODL.getValue(), GroupLevelEnumD.CATEGORY.getValue());
        nextDimensionSixGroupLvlMap.put(GroupLevelEnumD.CATEGORY.getValue(), GroupLevelEnumD.ITEM.getValue());
    }

    // 量纲颗粒度group层级下一层级映射，视角11
    private static Map<String, String> nextDimensionElevenLvlMap = new HashMap<>(10);
    static {
        nextDimensionElevenLvlMap.put(GroupLevelEnumD.LV0.getValue(), GroupLevelEnumD.LV1.getValue());
        nextDimensionElevenLvlMap.put(GroupLevelEnumD.LV1.getValue(), GroupLevelEnumD.LV2.getValue());
        nextDimensionElevenLvlMap.put(GroupLevelEnumD.LV2.getValue(), GroupLevelEnumD.DIMENSION.getValue());
        nextDimensionElevenLvlMap.put(GroupLevelEnumD.DIMENSION.getValue(), GroupLevelEnumD.SUBCATEGORY.getValue());
        nextDimensionElevenLvlMap.put(GroupLevelEnumD.SUBCATEGORY.getValue(), GroupLevelEnumD.SUB_DETAIL.getValue());
        nextDimensionElevenLvlMap.put(GroupLevelEnumD.SUB_DETAIL.getValue(), GroupLevelEnumD.SPART.getValue());
        nextDimensionElevenLvlMap.put(GroupLevelEnumD.SPART.getValue(), GroupLevelEnumD.CEG.getValue());
        nextDimensionElevenLvlMap.put(GroupLevelEnumD.CEG.getValue(), GroupLevelEnumD.MODL.getValue());
        nextDimensionElevenLvlMap.put(GroupLevelEnumD.MODL.getValue(), GroupLevelEnumD.CATEGORY.getValue());
        nextDimensionElevenLvlMap.put(GroupLevelEnumD.CATEGORY.getValue(), GroupLevelEnumD.ITEM.getValue());
    }

    // 量纲颗粒度group层级下一层级映射，视角7
    private static Map<String, String> nextDimensionSevenGroupLvlMap = new HashMap<>(8);
    static {
        nextDimensionSevenGroupLvlMap.put(GroupLevelEnumD.LV0.getValue(), GroupLevelEnumD.LV1.getValue());
        nextDimensionSevenGroupLvlMap.put(GroupLevelEnumD.LV1.getValue(), GroupLevelEnumD.LV2.getValue());
        nextDimensionSevenGroupLvlMap.put(GroupLevelEnumD.LV2.getValue(), GroupLevelEnumD.LV3.getValue());
        nextDimensionSevenGroupLvlMap.put(GroupLevelEnumD.LV3.getValue(), GroupLevelEnumD.DIMENSION.getValue());
        nextDimensionSevenGroupLvlMap.put(GroupLevelEnumD.DIMENSION.getValue(), GroupLevelEnumD.CEG.getValue());
        nextDimensionSevenGroupLvlMap.put(GroupLevelEnumD.CEG.getValue(), GroupLevelEnumD.MODL.getValue());
        nextDimensionSevenGroupLvlMap.put(GroupLevelEnumD.MODL.getValue(), GroupLevelEnumD.CATEGORY.getValue());
        nextDimensionSevenGroupLvlMap.put(GroupLevelEnumD.CATEGORY.getValue(), GroupLevelEnumD.ITEM.getValue());
    }

    // 量纲颗粒度group层级下一层级映射，视角8
    private static Map<String, String> nextDimensionEightGroupLvlMap = new HashMap<>(9);
    static {
        nextDimensionEightGroupLvlMap.put(GroupLevelEnumD.LV0.getValue(), GroupLevelEnumD.LV1.getValue());
        nextDimensionEightGroupLvlMap.put(GroupLevelEnumD.LV1.getValue(), GroupLevelEnumD.LV2.getValue());
        nextDimensionEightGroupLvlMap.put(GroupLevelEnumD.LV2.getValue(), GroupLevelEnumD.LV3.getValue());
        nextDimensionEightGroupLvlMap.put(GroupLevelEnumD.LV3.getValue(), GroupLevelEnumD.DIMENSION.getValue());
        nextDimensionEightGroupLvlMap.put(GroupLevelEnumD.DIMENSION.getValue(), GroupLevelEnumD.SUBCATEGORY.getValue());
        nextDimensionEightGroupLvlMap.put(GroupLevelEnumD.SUBCATEGORY.getValue(), GroupLevelEnumD.CEG.getValue());
        nextDimensionEightGroupLvlMap.put(GroupLevelEnumD.CEG.getValue(), GroupLevelEnumD.MODL.getValue());
        nextDimensionEightGroupLvlMap.put(GroupLevelEnumD.MODL.getValue(), GroupLevelEnumD.CATEGORY.getValue());
        nextDimensionEightGroupLvlMap.put(GroupLevelEnumD.CATEGORY.getValue(), GroupLevelEnumD.ITEM.getValue());
    }

    // 量纲颗粒度group层级下一层级映射，视角9
    private static Map<String, String> nextDimensionNineGroupLvlMap = new HashMap<>(10);
    static {
        nextDimensionNineGroupLvlMap.put(GroupLevelEnumD.LV0.getValue(), GroupLevelEnumD.LV1.getValue());
        nextDimensionNineGroupLvlMap.put(GroupLevelEnumD.LV1.getValue(), GroupLevelEnumD.LV2.getValue());
        nextDimensionNineGroupLvlMap.put(GroupLevelEnumD.LV2.getValue(), GroupLevelEnumD.LV3.getValue());
        nextDimensionNineGroupLvlMap.put(GroupLevelEnumD.LV3.getValue(), GroupLevelEnumD.DIMENSION.getValue());
        nextDimensionNineGroupLvlMap.put(GroupLevelEnumD.DIMENSION.getValue(), GroupLevelEnumD.SUBCATEGORY.getValue());
        nextDimensionNineGroupLvlMap.put(GroupLevelEnumD.SUBCATEGORY.getValue(), GroupLevelEnumD.SUB_DETAIL.getValue());
        nextDimensionNineGroupLvlMap.put(GroupLevelEnumD.SUB_DETAIL.getValue(), GroupLevelEnumD.CEG.getValue());
        nextDimensionNineGroupLvlMap.put(GroupLevelEnumD.CEG.getValue(), GroupLevelEnumD.MODL.getValue());
        nextDimensionNineGroupLvlMap.put(GroupLevelEnumD.MODL.getValue(), GroupLevelEnumD.CATEGORY.getValue());
        nextDimensionNineGroupLvlMap.put(GroupLevelEnumD.CATEGORY.getValue(), GroupLevelEnumD.ITEM.getValue());
    }

    // 量纲颗粒度group层级下一层级映射，视角12
    private static Map<String, String> nextDimensionTwelveGroupLvlMap = new HashMap<>(11);
    static {
        nextDimensionTwelveGroupLvlMap.put(GroupLevelEnumD.LV0.getValue(), GroupLevelEnumD.LV1.getValue());
        nextDimensionTwelveGroupLvlMap.put(GroupLevelEnumD.LV1.getValue(), GroupLevelEnumD.LV2.getValue());
        nextDimensionTwelveGroupLvlMap.put(GroupLevelEnumD.LV2.getValue(), GroupLevelEnumD.LV3.getValue());
        nextDimensionTwelveGroupLvlMap.put(GroupLevelEnumD.LV3.getValue(), GroupLevelEnumD.DIMENSION.getValue());
        nextDimensionTwelveGroupLvlMap.put(GroupLevelEnumD.DIMENSION.getValue(), GroupLevelEnumD.SUBCATEGORY.getValue());
        nextDimensionTwelveGroupLvlMap.put(GroupLevelEnumD.SUBCATEGORY.getValue(), GroupLevelEnumD.SUB_DETAIL.getValue());
        nextDimensionTwelveGroupLvlMap.put(GroupLevelEnumD.SUB_DETAIL.getValue(), GroupLevelEnumD.SPART.getValue());
        nextDimensionTwelveGroupLvlMap.put(GroupLevelEnumD.SPART.getValue(), GroupLevelEnumD.CEG.getValue());
        nextDimensionTwelveGroupLvlMap.put(GroupLevelEnumD.CEG.getValue(), GroupLevelEnumD.MODL.getValue());
        nextDimensionTwelveGroupLvlMap.put(GroupLevelEnumD.MODL.getValue(), GroupLevelEnumD.CATEGORY.getValue());
        nextDimensionTwelveGroupLvlMap.put(GroupLevelEnumD.CATEGORY.getValue(), GroupLevelEnumD.ITEM.getValue());
    }

    // 量纲颗粒度group层级下一层级映射，视角13(数字能源)
    private static Map<String, String> nextDimensionThirteenGroupLvlMap = new HashMap<>(12);
    static {
        nextDimensionThirteenGroupLvlMap.put(GroupLevelEnumD.LV0.getValue(), GroupLevelEnumD.LV1.getValue());
        nextDimensionThirteenGroupLvlMap.put(GroupLevelEnumD.LV1.getValue(), GroupLevelEnumD.LV2.getValue());
        nextDimensionThirteenGroupLvlMap.put(GroupLevelEnumD.LV2.getValue(), GroupLevelEnumD.LV3.getValue());
        nextDimensionThirteenGroupLvlMap.put(GroupLevelEnumD.LV3.getValue(), GroupLevelEnumD.COA.getValue());
        nextDimensionThirteenGroupLvlMap.put(GroupLevelEnumD.COA.getValue(), GroupLevelEnumD.DIMENSION.getValue());
        nextDimensionThirteenGroupLvlMap.put(GroupLevelEnumD.DIMENSION.getValue(), GroupLevelEnumD.SUBCATEGORY.getValue());
        nextDimensionThirteenGroupLvlMap.put(GroupLevelEnumD.SUBCATEGORY.getValue(), GroupLevelEnumD.SUB_DETAIL.getValue());
        nextDimensionThirteenGroupLvlMap.put(GroupLevelEnumD.SUB_DETAIL.getValue(), GroupLevelEnumD.SPART.getValue());
        nextDimensionThirteenGroupLvlMap.put(GroupLevelEnumD.SPART.getValue(), GroupLevelEnumD.CEG.getValue());
        nextDimensionThirteenGroupLvlMap.put(GroupLevelEnumD.CEG.getValue(), GroupLevelEnumD.MODL.getValue());
        nextDimensionThirteenGroupLvlMap.put(GroupLevelEnumD.MODL.getValue(), GroupLevelEnumD.CATEGORY.getValue());
        nextDimensionThirteenGroupLvlMap.put(GroupLevelEnumD.CATEGORY.getValue(), GroupLevelEnumD.ITEM.getValue());
    }

    // 量纲颗粒度group层级下一层级映射，视角13(IAS)
    private static Map<String, String> nextDimensionThirteenGroupLvlMapIas = new HashMap<>(12);
    static {
        nextDimensionThirteenGroupLvlMapIas.put(GroupLevelEnumD.LV0.getValue(), GroupLevelEnumD.LV1.getValue());
        nextDimensionThirteenGroupLvlMapIas.put(GroupLevelEnumD.LV1.getValue(), GroupLevelEnumD.LV2.getValue());
        nextDimensionThirteenGroupLvlMapIas.put(GroupLevelEnumD.LV2.getValue(), GroupLevelEnumD.LV3.getValue());
        nextDimensionThirteenGroupLvlMapIas.put(GroupLevelEnumD.LV3.getValue(), GroupLevelEnumD.LV4.getValue());
        nextDimensionThirteenGroupLvlMapIas.put(GroupLevelEnumD.LV4.getValue(), GroupLevelEnumD.DIMENSION.getValue());
        nextDimensionThirteenGroupLvlMapIas.put(GroupLevelEnumD.DIMENSION.getValue(), GroupLevelEnumD.SUBCATEGORY.getValue());
        nextDimensionThirteenGroupLvlMapIas.put(GroupLevelEnumD.SUBCATEGORY.getValue(), GroupLevelEnumD.SUB_DETAIL.getValue());
        nextDimensionThirteenGroupLvlMapIas.put(GroupLevelEnumD.SUB_DETAIL.getValue(), GroupLevelEnumD.SPART.getValue());
        nextDimensionThirteenGroupLvlMapIas.put(GroupLevelEnumD.SPART.getValue(), GroupLevelEnumD.CEG.getValue());
        nextDimensionThirteenGroupLvlMapIas.put(GroupLevelEnumD.CEG.getValue(), GroupLevelEnumD.MODL.getValue());
        nextDimensionThirteenGroupLvlMapIas.put(GroupLevelEnumD.MODL.getValue(), GroupLevelEnumD.CATEGORY.getValue());
        nextDimensionThirteenGroupLvlMapIas.put(GroupLevelEnumD.CATEGORY.getValue(), GroupLevelEnumD.ITEM.getValue());
    }

    /**
     * 私有构造器
     * 解决sonarQube问题： Add a private constructor to hide the implicit public one.
     */
    private FcstIndexUtil() {
        super();
    }

    /**
     * 获取下一层级的Group层级
     * LV0：ICT、LV1：重量级团队LV1、LV2: 重量级团队LV2、CEG：专项采购认证部、category：品类、item：规格品
     *
     * @param groupLevel 当前Group层级
     * @return String 下一层级的Group层级
     */
    public static String getNextThreeGroupLevel(String groupLevel) {
        return nextThreeGroupLevelMap1.get(groupLevel);
    }

    public static String getNextOneGroupLevel(String groupLevel) {
        return nextOneGroupLvlMap.get(groupLevel);
    }

    public static String getNextSevenGroupLevel(String groupLevel) {
        return nextSevenGroupLevelMap.get(groupLevel);
    }

    public static String getNextSixGroupLevel(String groupLevel) {
        return nextSixGroupLevelMap.get(groupLevel);
    }

    public static String getNextFiveGroupLevelUniversal(String groupLevel) {
        return nextFiveGroupLevelMap.get(groupLevel);
    }

    public static String getNextFourGroupLevel(String groupLevel) {
        return nextFourGroupLevelMap.get(groupLevel);
    }

    public static String getNextFiveGroupLevelIas(String groupLevel) {
        return nextFiveGroupLevelMapIas.get(groupLevel);
    }

    public static String getNextFourGroupLevelProfit(String groupLevel) {
        return nextFourGroupLevelMapProfit.get(groupLevel);
    }

    public static String getNextFiveGroupLevel(String groupLevel) {
        return nextFiveGroupLevelMapProfit.get(groupLevel);
    }

    public static String getNextOneGroupLevelDimension(String groupLevel) {
        return nextDimensionOneGroupLvlMap.get(groupLevel);
    }

    public static String getNextTwoGroupLevelDimension(String groupLevel) {
        return nextDimensionTwoGroupLvlMap.get(groupLevel);
    }

    public static String getNextThreeGroupLevelDimension(String groupLevel) {
        return nextDimensionThreeGroupLvlMap.get(groupLevel);
    }

    public static String getNextFourGroupLevelDimension(String groupLevel) {
        return nextDimensionFourGroupLvlMap.get(groupLevel);
    }

    public static String getNextFiveGroupLevelDimension(String groupLevel) {
        return nextDimensionFiveGroupLvlMap.get(groupLevel);
    }

    public static String getNextSixGroupLevelDimension(String groupLevel) {
        return nextDimensionSixGroupLvlMap.get(groupLevel);
    }
    public static String getNextSevenGroupLevelDimension(String groupLevel) {
        return nextDimensionSevenGroupLvlMap.get(groupLevel);
    }

    public static String getNextEightGroupLevelDimension(String groupLevel) {
        return nextDimensionEightGroupLvlMap.get(groupLevel);
    }

    public static String getNextNineGroupLevelDimension(String groupLevel) {
        return nextDimensionNineGroupLvlMap.get(groupLevel);
    }

    public static String getNextTenGroupLevelDimension(String groupLevel) {
        return nextDimensionTenLvlMap.get(groupLevel);
    }

    public static String getNextElevenGroupLevelDimension(String groupLevel) {
        return nextDimensionElevenLvlMap.get(groupLevel);
    }

    public static String getNextTwelveGroupLevelDimension(String groupLevel) {
        return nextDimensionTwelveGroupLvlMap.get(groupLevel);
    }

    public static String getNextThirteenGroupLevelDimension(String groupLevel) {
        return nextDimensionThirteenGroupLvlMap.get(groupLevel);
    }

    public static String getNextThirteenGroupLevelDimensionIas(String groupLevel) {
        return nextDimensionThirteenGroupLvlMapIas.get(groupLevel);
    }

    public static String getNextTwoGroupLevel(String groupLevel) {
        return nextTwoGroupLvlMap.get(groupLevel);
    }

    public static String getNextGroupLevelByView(String viewFlag, String level,String granularityType,String industryOrg) {
        String nextGroupLevel = null;
        switch (viewFlag) {
            case "0":
                nextGroupLevel = nextGroupLevelOne(granularityType, level);
                break;
            case "1":
                nextGroupLevel = nextGroupLevelTwo(granularityType, IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION, getNextTwoGroupLevelDimension(level), getNextTwoGroupLevel(level));
                break;
            case "2":
                nextGroupLevel = nextGroupLevelTwo(granularityType, IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION, getNextThreeGroupLevelDimension(level), getNextThreeGroupLevel(level));
                break;
            default:
                break;
        }
        String nextGroupLevelByViewsub = getNextGroupLevelByViewsub(viewFlag, level, granularityType,industryOrg);
        if (StringUtils.isNotBlank(nextGroupLevelByViewsub)) {
            nextGroupLevel = nextGroupLevelByViewsub;
        }
        return nextGroupLevel;
    }

    private static String nextGroupLevelTwo(String granularityType, IndustryIndexEnum.GRANULARITY_TYPE dimension, String nextTwoGroupLevelDimension, String nextTwoGroupLevel) {
        String nextGroupLevel;
        if (dimension.getValue().equals(granularityType)) {
            nextGroupLevel = nextTwoGroupLevelDimension;
        } else {
            nextGroupLevel = nextTwoGroupLevel;
        }
        return nextGroupLevel;
    }

    private static String getNextGroupLevelByViewsub(String viewFlag, String level,String granularityType,String industryOrg) {
        String nextGroupLevel = null;
        switch (viewFlag) {
            case "3":
                nextGroupLevel = nextGroupLevelWithThreeView(granularityType, getNextFourGroupLevelDimension(level), getNextFourGroupLevel(level), getNextFourGroupLevelProfit(level));
                break;
            case "4":
                nextGroupLevel = nextGroupLevelWithThreeView(granularityType, getNextFiveGroupLevelDimension(level), getNextFiveGroupLevelUniversal(level), getNextFiveGroupLevel(level));
                break;
            case "5":
                nextGroupLevel = nextGroupLevelTwo(granularityType, IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION, getNextSixGroupLevelDimension(level), getNextSixGroupLevel(level));
                break;
            default:
                break;
        }
        String nextGroupLevelByViewSub1 = getNextGroupLevelByViewSub1(viewFlag, level, granularityType,industryOrg);
        if (StringUtils.isNotBlank(nextGroupLevelByViewSub1)) {
            nextGroupLevel = nextGroupLevelByViewSub1;
        }
        return nextGroupLevel;
    }

    private static String nextGroupLevelWithThreeView(String granularityType, String nextFourGroupLevelDimension, String nextFourGroupLevel, String nextFourGroupLevelProfit) {
        String nextGroupLevel;
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(granularityType)) {
            nextGroupLevel = nextFourGroupLevelDimension;
        } else {
            nextGroupLevel = nextGroupLevelTwo(granularityType, IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL, nextFourGroupLevel, nextFourGroupLevelProfit);
        }
        return nextGroupLevel;
    }

    private static String getNextGroupLevelByViewSub1(String viewFlag, String level,String granularityType,String industryOrg) {
        String nextGroupLevel = null;
        switch (viewFlag) {
            case "6":
                nextGroupLevel = nextGroupLevelTwo(granularityType, IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION, getNextSevenGroupLevelDimension(level), getNextSevenGroupLevel(level));
                break;
            case "7":
                nextGroupLevel = nextGroupLevelTwo(granularityType, IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION, getNextEightGroupLevelDimension(level), getNextFiveGroupLevelIas(level));
                break;
            case "8":
                nextGroupLevel = getNextNineGroupLevelDimension(level);
                break;
            case "9":
                nextGroupLevel = getNextTenGroupLevelDimension(level);
                break;
            default:
                break;
        }
        String nextGroupLevelByViewSub2 = getNextGroupLevelByViewSub2(viewFlag, level,industryOrg);
        if (StringUtils.isNotBlank(nextGroupLevelByViewSub2)) {
            nextGroupLevel = nextGroupLevelByViewSub2;
        }
        return nextGroupLevel;
    }

    private static String getNextGroupLevelByViewSub2(String viewFlag, String groupLevel,String industryOrg) {
        String nextGroupLevel = null;
        switch (viewFlag) {
            case "10":
                nextGroupLevel = getNextElevenGroupLevelDimension(groupLevel);
                break;
            case "11":
                nextGroupLevel = getNextTwelveGroupLevelDimension(groupLevel);
                break;
            case "12":
                if (IndustryConst.INDUSTRY_ORG.IAS.getValue().equals(industryOrg)) {
                    nextGroupLevel = getNextThirteenGroupLevelDimensionIas(groupLevel);
                } else {
                    nextGroupLevel = getNextThirteenGroupLevelDimension(groupLevel);
                }
                break;
            default:
                break;
        }
        return nextGroupLevel;
    }

    public static Map getNextGroupLevel(MonthAnalysisVO monthAnalysisVO) {
        String nextGroupLevel;
        String nextGroupName = "";
        String granularityType = monthAnalysisVO.getGranularityType();
        HashMap map = new HashMap();
        IndustryIndexEnum.VIEW_FLAG_D viewFlag = IndustryIndexEnum.getViewFlagDimension(monthAnalysisVO.getViewFlag());
        // 分视角获取下个层级的groupLevel值
        switch (viewFlag) {
            case VIEW1:
                nextGroupLevel = nextGroupLevelOne(granularityType, monthAnalysisVO.getGroupLevel());
                break;
            case VIEW2:
                nextGroupLevel = nextGroupLevelTwo(granularityType, IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION, getNextTwoGroupLevelDimension(monthAnalysisVO.getGroupLevel()), getNextTwoGroupLevel(monthAnalysisVO.getGroupLevel()));
                break;
            case VIEW3:
                nextGroupLevel = nextGroupLevelTwo(granularityType, IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION, getNextThreeGroupLevelDimension(monthAnalysisVO.getGroupLevel()), getNextThreeGroupLevel(monthAnalysisVO.getGroupLevel()));
                break;
            case VIEW4:
                nextGroupLevel = getViewFourGroupLevelName(granularityType, monthAnalysisVO);
                break;
            case VIEW5:
                nextGroupLevel = getViewFiveGroupLevelName(granularityType, monthAnalysisVO);
                break;
            case VIEW6:
                nextGroupLevel = getViewSixGroupLevelName(granularityType, monthAnalysisVO);
                break;
            default:
                nextGroupLevel = "";
                break;
        }
        String nextGroupLevelSub = getNextGroupLevelSub(monthAnalysisVO);
        if (StringUtils.isNotBlank(nextGroupLevelSub)) {
            nextGroupLevel = nextGroupLevelSub;
        }
        if (IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(granularityType)) {
            nextGroupName = GroupLevelEnumU.getInstance(nextGroupLevel).getName();
        }
        if (IndustryIndexEnum.GRANULARITY_TYPE.PROFITS.getValue().equals(granularityType)) {
            nextGroupName = GroupLevelEnumP.getInstance(nextGroupLevel).getName();
        }
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(granularityType)) {
            nextGroupName = GroupLevelEnumD.getInstance(nextGroupLevel).getName();
        }
        map.put("nextGroupLevel",nextGroupLevel);
        map.put("nextGroupName",nextGroupName);
        return map;
    }

    private static String nextGroupLevelOne(String granularityType, String groupLevel) {
        return nextGroupLevelTwo(granularityType, IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION, getNextOneGroupLevelDimension(groupLevel), getNextOneGroupLevel(groupLevel));
    }

    public static String getNextGroupLevelSub(MonthAnalysisVO monthAnalysisVO) {
        String nextGroupLevel;
        String granularityType = monthAnalysisVO.getGranularityType();
        IndustryIndexEnum.VIEW_FLAG_D viewFlag = IndustryIndexEnum.getViewFlagDimension(monthAnalysisVO.getViewFlag());
        // 分视角获取下个层级的groupLevel值
        switch (viewFlag) {
            case VIEW7:
                nextGroupLevel = nextGroupLevelTwo(granularityType, IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION, getNextSevenGroupLevelDimension(monthAnalysisVO.getGroupLevel()), getNextSevenGroupLevel(monthAnalysisVO.getGroupLevel()));
                break;
            case VIEW8:
                nextGroupLevel = nextGroupLevelTwo(granularityType, IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION, getNextEightGroupLevelDimension(monthAnalysisVO.getGroupLevel()), getNextFiveGroupLevelIas(monthAnalysisVO.getGroupLevel()));
                break;
            case VIEW9:
                nextGroupLevel = getNextNineGroupLevelDimension(monthAnalysisVO.getGroupLevel());
                break;
            case VIEW10:
                nextGroupLevel = getNextTenGroupLevelDimension(monthAnalysisVO.getGroupLevel());
                break;
            case VIEW11:
                nextGroupLevel = getNextElevenGroupLevelDimension(monthAnalysisVO.getGroupLevel());
                break;
            case VIEW12:
                nextGroupLevel = getNextTwelveGroupLevelDimension(monthAnalysisVO.getGroupLevel());
                break;
            case VIEW13:
                if (IndustryConst.INDUSTRY_ORG.IAS.getValue().equals(monthAnalysisVO.getIndustryOrg())) {
                    nextGroupLevel = getNextThirteenGroupLevelDimensionIas(monthAnalysisVO.getGroupLevel());
                } else {
                    nextGroupLevel = getNextThirteenGroupLevelDimension(monthAnalysisVO.getGroupLevel());
                }
                break;
            default:
                nextGroupLevel = "";
                break;
        }
        return nextGroupLevel;
    }

    private static String getViewFourGroupLevelName(String granularityType, MonthAnalysisVO monthAnalysisVO) {
        String nextGroupLevel;
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(granularityType)) {
            nextGroupLevel = getNextFourGroupLevelDimension(monthAnalysisVO.getGroupLevel());
        } else {
            nextGroupLevel = nextGroupLevelTwo(granularityType, IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL, getNextFourGroupLevel(monthAnalysisVO.getGroupLevel()), getNextFourGroupLevelProfit(monthAnalysisVO.getGroupLevel()));
        }
        return nextGroupLevel;
    }

    private static String getViewFiveGroupLevelName(String granularityType, MonthAnalysisVO monthAnalysisVO) {
        String nextGroupLevel;
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(granularityType)) {
            nextGroupLevel = getNextFiveGroupLevelDimension(monthAnalysisVO.getGroupLevel());
        } else {
            nextGroupLevel = nextGroupLevelTwo(granularityType, IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL, getNextFiveGroupLevelUniversal(monthAnalysisVO.getGroupLevel()), getNextFiveGroupLevel(monthAnalysisVO.getGroupLevel()));
        }
        return nextGroupLevel;
    }

    private static String getViewSixGroupLevelName(String granularityType, MonthAnalysisVO monthAnalysisVO) {
        return nextGroupLevelTwo(granularityType, IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION, getNextSixGroupLevelDimension(monthAnalysisVO.getGroupLevel()), getNextSixGroupLevel(monthAnalysisVO.getGroupLevel()));
    }

    /**
     * 获取报告期36个月的开始和结束时间
     *      默认基期开始时间为（默认基期为（预测年份-3）年的1月份）
     *      默认基期结束时间为（预测年份的12月份或者预测年份的06月）
     *
     * @return List
     */
    public static List<Integer> getPeriodScope(String actualMonthMum) {
        String periodEndTime = null;
        String periodStartTime = null;
        if (StringUtils.isNotBlank(actualMonthMum) && !"0".equals(actualMonthMum)) {
            String yearStr = actualMonthMum.substring(0, 4);
            String monthStr = actualMonthMum.substring(4, 6);
            int month = Integer.valueOf(monthStr);
            int year = Integer.valueOf(yearStr);
            periodStartTime = (year - Constant.IntegerEnum.TWO.getValue()) + Constant.StrEnum.ZERO_ONE.getValue();
            if (month <= 6) {
                periodEndTime = (year + Constant.IntegerEnum.ONE.getValue()) + Constant.StrEnum.SIX.getValue();
            }
            if (month > 6) {
                periodEndTime = (year + Constant.IntegerEnum.ONE.getValue()) + Constant.StrEnum.TWELVE.getValue();
            }
            List<Integer> periodTimeList = new ArrayList<>();
            if (null != periodStartTime && null != periodEndTime) {
                periodTimeList = Arrays.asList(Integer.valueOf(periodStartTime), Integer.valueOf(periodEndTime));
            }
            return periodTimeList;
        }
        return null;
    }

    /**
     * 处理报告期36个月的开始和结束时间
     * 得到基期的开始时间和结束时间
     *
     * @param searchParamsVO 参数
     */
    public static void handlePeriod(MonthAnalysisVO searchParamsVO,String actualMonthMum) {
        if (ObjectUtils.isEmpty(searchParamsVO.getPeriodStartTime()) && ObjectUtils.isEmpty(
                searchParamsVO.getPeriodEndTime())) {
            if (StringUtils.isNotBlank(actualMonthMum)) {
                List<Integer> periodScope = FcstIndexUtil.getPeriodScope(actualMonthMum);
                if (CollectionUtils.isNotEmpty(periodScope)) {
                    searchParamsVO.setPeriodStartTime(periodScope.get(0));
                    searchParamsVO.setPeriodEndTime(periodScope.get(1));
                }
            }
        }
    }

    /**
     * 处理报告期24个月的开始和结束时间
     * 得到基期的开始时间和结束时间
     *
     * @param searchParamsVO 参数
     */
    public static void handlePeriodForMonthYoy(MonthAnalysisVO searchParamsVO,String actualMonthMum) {
        String periodEndTime = null;
        String periodStartTime = null;
        if (StringUtils.isNotBlank(actualMonthMum) && !"0".equals(actualMonthMum)) {
            String yearStr= actualMonthMum.substring(0, 4);
            String monthStr = actualMonthMum.substring(4, 6);
            int month = Integer.valueOf(monthStr);
            int year = Integer.valueOf(yearStr);
            periodStartTime = (year - Constant.IntegerEnum.ONE.getValue()) + Constant.StrEnum.ZERO_ONE.getValue();
            if (month <= 6) {
                periodEndTime = (year + Constant.IntegerEnum.ONE.getValue()) + Constant.StrEnum.SIX.getValue();
            }
            if (month > 6) {
                periodEndTime = (year + Constant.IntegerEnum.ONE.getValue()) + Constant.StrEnum.TWELVE.getValue();
            }
            searchParamsVO.setPeriodStartTime(Integer.valueOf(periodStartTime));
            if (null != periodEndTime) {
                searchParamsVO.setPeriodEndTime(Integer.valueOf(periodEndTime));
            }
        }
    }

    public static String getTitle(String title, String groupCnName) {
        return String.format(Locale.ROOT, title, groupCnName);
    }

    /**
     * 获取下载模板中的显性提示基期
     *
     * @param basePeriod 基期
     * @return String
     */
    public static String getBasePeriodStr(Integer basePeriod) {
        return Constant.StrEnum.BASE_PERIOD.getValue().concat(String.valueOf(basePeriod));
    }

    public static List<String> getPeriod(String version) {
        String[] splitVersion = version.split("-");
        List<String> yearPeriodList = new ArrayList<>();
        if (splitVersion.length != 0) {
            long count = 3L;
            String yearStr = splitVersion[0].substring(0, 4);
            long endYear = NumberUtil.parseInt(yearStr);
            String periodStr = endYear - 1 + "+" + endYear + "YTD";
            for (long i = count; i > 0; i--) {
                long startYear = endYear - i;
                yearPeriodList.add(String.valueOf(startYear));
            }
            yearPeriodList.add(endYear + "YTD");
            yearPeriodList.add(periodStr);
        }
        return yearPeriodList;
    }

    /**
     * 获取当前年份的1月到实际月份
     * 得到基期的开始时间和结束时间
     *
     * @param standardAnalysisVO 参数
     */
    public static void handleNowPeriod(StandardAnalysisVO standardAnalysisVO, String periodEndTime) {
        if (ObjectUtils.isEmpty(standardAnalysisVO.getPeriodStartTime()) && ObjectUtils.isEmpty(
                standardAnalysisVO.getPeriodEndTime())) {
            List<Integer> periodTimeList = new ArrayList<>();
            if (StringUtils.isNotBlank(periodEndTime)) {
                String periodStartTime = null;
                String yearStr = periodEndTime.substring(0, 4);
                int year = Integer.valueOf(yearStr);
                periodStartTime = year + Constant.StrEnum.ZERO_ONE.getValue();
                if (null != periodStartTime && null != periodEndTime) {
                    periodTimeList = Arrays.asList(Integer.valueOf(periodStartTime), Integer.valueOf(periodEndTime));
                }
            }
            if (CollectionUtils.isNotEmpty(periodTimeList)) {
                standardAnalysisVO.setPeriodStartTime(periodTimeList.get(0));
                standardAnalysisVO.setPeriodEndTime(periodTimeList.get(1));
            }
        }
    }

    public static String getSheetName(HistoryInputVO historyInputVO) {
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(historyInputVO.getGranularityType())) {
            return getDimensionSheetName(historyInputVO);
        } else if (IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(historyInputVO.getGranularityType())) {
            return getUniversalSheetName(historyInputVO);
        } else {
            return getProfitSheetName(historyInputVO);
        }
    }

    public static String getDimensionSheetName(HistoryInputVO historyInputVO) {
        switch (historyInputVO.getViewFlag()) {
            case "1":
                return IndustryIndexEnum.VIEW_FLAG_SHEET.VIEW2.getName();
            case "2":
                return IndustryIndexEnum.VIEW_FLAG_SHEET.VIEW3.getName();
            case "9":
                return IndustryIndexEnum.VIEW_FLAG_SHEET.VIEW4.getName();
            case "3":
                return IndustryIndexEnum.VIEW_FLAG_SHEET.VIEW5.getName();
            case "4":
                return IndustryIndexEnum.VIEW_FLAG_SHEET.VIEW6.getName();
            case "5":
                return IndustryIndexEnum.VIEW_FLAG_SHEET.VIEW7.getName();
            case "10":
                return IndustryIndexEnum.VIEW_FLAG_SHEET.VIEW8.getName();
            case "6":
                return IndustryIndexEnum.VIEW_FLAG_SHEET.VIEW9.getName();
            case "7":
                return IndustryIndexEnum.VIEW_FLAG_SHEET.VIEW10.getName();
            case "8":
                return IndustryIndexEnum.VIEW_FLAG_SHEET.VIEW11.getName();
            case "11":
                return IndustryIndexEnum.VIEW_FLAG_SHEET.VIEW12.getName();
            case "12":
                return IndustryIndexEnum.VIEW_FLAG_SHEET.VIEW13.getName();
            default:
                return IndustryIndexEnum.VIEW_FLAG_SHEET.VIEW1.getName();
        }
    }

    public static String getUniversalSheetName(HistoryInputVO historyInputVO) {
        switch (historyInputVO.getViewFlag()) {
            case "1":
                return IndustryIndexEnum.VIEW_FLAG_SHEET.VIEW2.getName();
            case "2":
                return IndustryIndexEnum.VIEW_FLAG_SHEET.VIEW3.getName();
            case "3":
                return IndustryIndexEnum.VIEW_FLAG_SHEET.VIEW4.getName();
            case "4":
                return IndustryIndexEnum.VIEW_FLAG_SHEET.VIEW5.getName();
            case "5":
                return IndustryIndexEnum.VIEW_FLAG_SHEET.VIEW6.getName();
            case "6":
                return IndustryIndexEnum.VIEW_FLAG_SHEET.VIEW7.getName();
            default:
                return IndustryIndexEnum.VIEW_FLAG_SHEET.VIEW1.getName();
        }
    }

    public static String getProfitSheetName(HistoryInputVO historyInputVO) {
        switch (historyInputVO.getViewFlag()) {
            case "3":
                return IndustryIndexEnum.VIEW_FLAG_SHEET.VIEW1.getName();
            case "4":
                return IndustryIndexEnum.VIEW_FLAG_SHEET.VIEW2.getName();
            default:
                return null;
        }
    }

    public static void setCommonWeightTeamCode(List<HeaderVo> topHeader, HistoryInputVO historyInputVO) {
        int viewInt = Integer.parseInt(historyInputVO.getViewFlag());
        String granularityType = historyInputVO.getGranularityType();
        if (IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(granularityType)) {
            getUniversalHeader(viewInt,topHeader,historyInputVO);
        }
        if (IndustryIndexEnum.GRANULARITY_TYPE.PROFITS.getValue().equals(granularityType)) {
            getProfitsHeader(viewInt,topHeader,historyInputVO);
        }
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(granularityType)) {
            getDimensionHeader(viewInt, topHeader, historyInputVO);
        }
    }

    private static void getUniversalHeader(int viewInt, List<HeaderVo> topHeader,HistoryInputVO historyInputVO) {
        if (viewInt < 7 && viewInt > 3) {
            topHeader.add(new HeaderVo("重量级团队LV0", "lv0_prod_rd_team_cn_name", CellType.STRING, true, 15 * 320));
        }else {
            if (viewInt == 7) {
                viewInt = 3;
            }
            for (int i = 0; i <= viewInt; i++) {
                topHeader.add(new HeaderVo("重量级团队LV" + i, "lv" + i + "_prod_rd_team_cn_name", CellType.STRING, true, 15 * 320));
            }
            if (IndustryIndexEnum.VIEW_FLAG_U.VIEW8.getValue().equals(historyInputVO.getViewFlag())) {
                topHeader.add(new HeaderVo("重量级团队LV3.5", "lv4_prod_rd_team_cn_name", CellType.STRING, true, 15 * 320));
            }
        }
    }

    private static void getProfitsHeader(int viewInt, List<HeaderVo> topHeader, HistoryInputVO historyInputVO) {
        if (viewInt > 2) {
            viewInt = 2;
        }
        for (int i = 0; i <= viewInt ; i++) {
            topHeader.add(new HeaderVo("重量级团队LV" + i, "lv" + i + "_prod_rd_team_cn_name", CellType.STRING, true, 15 * 320));
        }
        if (IndustryIndexEnum.VIEW_FLAG_P.VIEW4.getValue().equals(historyInputVO.getViewFlag())) {
            topHeader.add(new HeaderVo("盈利颗粒度L1", "l1_name", CellType.STRING, true, 15 * 320));
        }
        if (IndustryIndexEnum.VIEW_FLAG_P.VIEW5.getValue().equals(historyInputVO.getViewFlag())) {
            topHeader.add(new HeaderVo("盈利颗粒度L1", "l1_name", CellType.STRING, true, 15 * 320));
            topHeader.add(new HeaderVo("盈利颗粒度L2", "l2_name", CellType.STRING, true, 15 * 320));
        }
    }

    private static void getDimensionHeader(int viewInt, List<HeaderVo> topHeader, HistoryInputVO historyInputVO) {
        if (viewInt < 3 || viewInt == 9) {
            viewInt = 1;
            for (int i = 0; i <= viewInt ; i++) {
                topHeader.add(new HeaderVo("重量级团队LV" + i, "lv" + i + "_prod_rd_team_cn_name", CellType.STRING, true, 15 * 320));
            }
        }
        if ((2 < viewInt && viewInt < 6) || viewInt == 10) {
            viewInt = 2;
            for (int i = 0; i <= viewInt ; i++) {
                topHeader.add(new HeaderVo("重量级团队LV" + i, "lv" + i + "_prod_rd_team_cn_name", CellType.STRING, true, 15 * 320));
            }
        } else if ((5 < viewInt && viewInt < 9) || viewInt == 11) {
            viewInt = 3;
            for (int i = 0; i <= viewInt ; i++) {
                topHeader.add(new HeaderVo("重量级团队LV" + i, "lv" + i + "_prod_rd_team_cn_name", CellType.STRING, true, 15 * 320));
            }
        } else if (viewInt == 12){
            viewInt = 3;
            for (int i = 0; i <= viewInt ; i++) {
                topHeader.add(new HeaderVo("重量级团队LV" + i, "lv" + i + "_prod_rd_team_cn_name", CellType.STRING, true, 15 * 320));
            }
            if (IndustryConst.INDUSTRY_ORG.IAS.getValue().equals(historyInputVO.getIndustryOrg())) {
                topHeader.add(new HeaderVo("重量级团队LV3.5", "lv4_prod_rd_team_cn_name", CellType.STRING, true, 15 * 320));
            }
        }
        setDimensionHeader(topHeader, historyInputVO);
    }

    private static void setDimensionHeader(List<HeaderVo> topHeader, HistoryInputVO historyInputVO) {
        if (IndustryIndexEnum.VIEW_FLAG_D.VIEW1.getValue().equals(historyInputVO.getViewFlag())
            || IndustryIndexEnum.VIEW_FLAG_D.VIEW4.getValue().equals(historyInputVO.getViewFlag())
            || IndustryIndexEnum.VIEW_FLAG_D.VIEW7.getValue().equals(historyInputVO.getViewFlag())) {
            topHeader.add(new HeaderVo("量纲", "dimension_cn_name", CellType.STRING, true, 15 * 320));
        } else if (IndustryIndexEnum.VIEW_FLAG_D.VIEW2.getValue().equals(historyInputVO.getViewFlag())
            || IndustryIndexEnum.VIEW_FLAG_D.VIEW5.getValue().equals(historyInputVO.getViewFlag())
            || IndustryIndexEnum.VIEW_FLAG_D.VIEW8.getValue().equals(historyInputVO.getViewFlag())) {
            topHeader.add(new HeaderVo("量纲", "dimension_cn_name", CellType.STRING, true, 15 * 320));
            topHeader.add(new HeaderVo("量纲子类", "dimension_subcategory_cn_name", CellType.STRING, true, 15 * 320));
        } else if (IndustryIndexEnum.VIEW_FLAG_D.VIEW3.getValue().equals(historyInputVO.getViewFlag())
            || IndustryIndexEnum.VIEW_FLAG_D.VIEW6.getValue().equals(historyInputVO.getViewFlag())
            || IndustryIndexEnum.VIEW_FLAG_D.VIEW9.getValue().equals(historyInputVO.getViewFlag())) {
            topHeader.add(new HeaderVo("量纲", "dimension_cn_name", CellType.STRING, true, 15 * 320));
            topHeader.add(new HeaderVo("量纲子类", "dimension_subcategory_cn_name", CellType.STRING, true, 15 * 320));
            topHeader.add(new HeaderVo("量纲子类明细", "dimension_sub_detail_cn_name", CellType.STRING, true, 15 * 320));
        } else if (IndustryIndexEnum.VIEW_FLAG_D.VIEW10.getValue().equals(historyInputVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D.VIEW11.getValue().equals(historyInputVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D.VIEW12.getValue().equals(historyInputVO.getViewFlag())) {
            topHeader.add(new HeaderVo("量纲", "dimension_cn_name", CellType.STRING, true, 15 * 310));
            topHeader.add(new HeaderVo("量纲子类", "dimension_subcategory_cn_name", CellType.STRING, true, 15 * 310));
            topHeader.add(new HeaderVo("量纲子类明细", "dimension_sub_detail_cn_name", CellType.STRING, true, 15 * 310));
            topHeader.add(new HeaderVo("SPART", "spart_cn_name", CellType.STRING, true, 15 * 310));
        } else {
            if (IndustryConst.INDUSTRY_ORG.ENERGY.getValue().equals(historyInputVO.getIndustryOrg())) {
                topHeader.add(new HeaderVo("COA", "coa_cn_name", CellType.STRING, true, 15 * 310));
            }
            topHeader.add(new HeaderVo("量纲", "dimension_cn_name", CellType.STRING, true, 15 * 310));
            topHeader.add(new HeaderVo("量纲子类", "dimension_subcategory_cn_name", CellType.STRING, true, 15 * 310));
            topHeader.add(new HeaderVo("量纲子类明细", "dimension_sub_detail_cn_name", CellType.STRING, true, 15 * 310));
            topHeader.add(new HeaderVo("SPART", "spart_cn_name", CellType.STRING, true, 15 * 310));
        }
    }

    public static void setDynamicYear(List<String> yearPeriodList, HistoryInputVO historyInputVO) {
        // 动态表头设置
        for (int i = 0; i < yearPeriodList.size(); i++) {
            String periodStr = yearPeriodList.get(i) + CommonConstant.WEIGHT_CN_NAME;
            String periodWeight = CommonConstant.WEIGHT_EN_NAME + i;
            if (IndustryConst.DataType.ITEM.getValue().equals(historyInputVO.getModelType())) {
                historyInputVO.getTopItemHeader().add(
                    new HeaderVo(periodStr, periodWeight, CellType.NUMERIC, true, 15 * 350));
            } else {
                historyInputVO.getTopCateHeader().add(
                    new HeaderVo(periodStr, periodWeight, CellType.NUMERIC, true, 15 * 350));
            }
        }
    }

    public static void setHeader(List<AbstractExcelTitleVO> titleVoList, List<HeaderVo> headers, Set<String> titles) {
        LeafExcelTitleVO column;
        for (HeaderVo header : headers) {
            column =
                new LeafExcelTitleVO(
                    header.getTitle(),
                    header.getWidth(),
                    true,
                    header.getField(),
                    header.getField(),
                    header.getDataType(),
                    header.getIsEditable());
            titleVoList.add(column);
            titles.add(header.getField());
        }
    }

    public static void setTopDTO(List<Map> cateItemWithWeightList) {
        for (Map topMap : cateItemWithWeightList) {
            if(IndustryConst.TopFlag.IS_YES_EN.getValue().equals(topMap.get("is_top_flag"))) {
                topMap.put("is_top_flag", IndustryConst.TopFlag.IS_YES_CN.getValue());
            } else {
                topMap.put("is_top_flag", IndustryConst.TopFlag.IS_NOT_CN.getValue());
            }
        }
    }

    public static List<Double> getSumWeight(List<Map> foiGroupLevWeightVO) {
        List<Double> allWeightSums = new ArrayList<>();
        Stream<Map> weight0 = foiGroupLevWeightVO.stream().filter(obj -> null != obj.get("weight0"));
        Double weightSum1 = weight0.mapToDouble(num -> Double.parseDouble(num.get("weight0").toString())).sum();
        allWeightSums.add(weightSum1);
        Stream<Map> weight1 = foiGroupLevWeightVO.stream().filter(obj -> null != obj.get("weight1"));
        Double weightSum2 = weight1.mapToDouble(num -> Double.parseDouble(num.get("weight1").toString())).sum();
        allWeightSums.add(weightSum2);
        Stream<Map> weight2 = foiGroupLevWeightVO.stream().filter(obj -> null != obj.get("weight2"));
        Double weightSu3 = weight2.mapToDouble(num -> Double.parseDouble(num.get("weight2").toString())).sum();
        allWeightSums.add(weightSu3);
        Stream<Map> weight3 = foiGroupLevWeightVO.stream().filter(obj -> null != obj.get("weight3"));
        Double weightSum4 = weight3.mapToDouble(num -> Double.parseDouble(num.get("weight3").toString())).sum();
        allWeightSums.add(weightSum4);
        return allWeightSums;
    }

    public static boolean isReveseViewFlag(MonthAnalysisVO monthAnalysisVO) {
        return IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(monthAnalysisVO.getGranularityType())
                && (IndustryIndexEnum.VIEW_FLAG_U.VIEW5.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_U.VIEW6.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_U.VIEW7.getValue().equals(monthAnalysisVO.getViewFlag()));
    }
}
