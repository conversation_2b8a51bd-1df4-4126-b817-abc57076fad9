/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.impl.provider.config;

import com.huawei.it.fcst.export.IExcelExportDataProvider;
import com.huawei.it.fcst.export.vo.ExcelExportContext;
import com.huawei.it.fcst.export.vo.ExportList;
import com.huawei.it.fcst.industry.price.dao.IPriceTopSpartInfoDao;
import com.huawei.it.fcst.industry.price.utils.FcstIndustryUtil;
import com.huawei.it.fcst.industry.price.vo.config.PriceHistorySpartListVO;
import com.huawei.it.fcst.industry.price.vo.config.PriceHistorySpartSearchVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import javax.inject.Inject;
import javax.inject.Named;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 导出 历史spart清单
 *
 * <AUTHOR>
 * @since 2024/7/11
 */
@Named("IExcelExport.HistorySpartExportDataProvider")
public class HistorySpartExportDataProvider implements IExcelExportDataProvider {

    @Inject
    private IPriceTopSpartInfoDao priceTopSpartInfoDao;

    @Override
    public List<?> getData(Serializable conditionObject, PageVO pageVO) throws ApplicationException {
        PriceHistorySpartSearchVO historyTopSpartInfoVO = (PriceHistorySpartSearchVO) conditionObject;
        historyTopSpartInfoVO.setPageIndex(pageVO.getCurPage());
        historyTopSpartInfoVO.setPageSize(pageVO.getPageSize());
        PagedResult<PriceHistorySpartListVO> pagedResult =  priceTopSpartInfoDao.findTopSpartByPage(historyTopSpartInfoVO, pageVO);
        List<PriceHistorySpartListVO> dataList = pagedResult.getResult();
        for (PriceHistorySpartListVO historySpartListVO : dataList) {
            // 设置国内/海外标识
            if (!StringUtils.isAnyBlank(historyTopSpartInfoVO.getSignTopCustCategoryCode(),
                    historyTopSpartInfoVO.getSignSubsidiaryCustcatgCnName())) {
                historySpartListVO.setOverseaFlagCnName("");
            }
            // 设置百分比的权重值
            if (StringUtils.isNotBlank(historySpartListVO.getWeight0())) {
                historySpartListVO.setWeight0(getWeightStr(historySpartListVO.getWeight0()));
            }
            if (StringUtils.isNotBlank(historySpartListVO.getWeight1())) {
                historySpartListVO.setWeight1(getWeightStr(historySpartListVO.getWeight1()));
            }
            if (StringUtils.isNotBlank(historySpartListVO.getWeight2())) {
                historySpartListVO.setWeight2(getWeightStr(historySpartListVO.getWeight2()));
            }
            if (StringUtils.isNotBlank(historySpartListVO.getWeight3())) {
                historySpartListVO.setWeight3(getWeightStr(historySpartListVO.getWeight3()));
            }
        }
        ExportList list = new ExportList();
        list.addAll(dataList);
        list.setTotalRows(pagedResult.getPageVO().getTotalRows());
        return list;
    }

    @NotNull
    private String getWeightStr(String weight) {
        if ("-".equals(weight)) {
            return weight;
        }
        BigDecimal bigDecimal = new BigDecimal(weight);
        bigDecimal = bigDecimal.multiply(BigDecimal.valueOf(100));
        return bigDecimal.setScale(2, RoundingMode.HALF_UP) + "%";
    }

    @Override
    public Map<String, Object> getHeader(Serializable conditionObject, Map<String, Object> parameters) throws ApplicationException {
        ExcelExportContext context = (ExcelExportContext) conditionObject;
        PriceHistorySpartSearchVO historyTopSpartInfoVO = (PriceHistorySpartSearchVO) context.getConditionObject();
        // 自定义表头
        Map<String, Object> headMap = new HashMap<>();
        // 获取需要导出的数据和动态表头
        headMap.put("versionName", historyTopSpartInfoVO.getVersionName());
        List<String> yearPeriodList = FcstIndustryUtil.getPeriod(historyTopSpartInfoVO.getVersionName());
        headMap.put("year0", yearPeriodList.get(0));
        headMap.put("year1", yearPeriodList.get(1));
        headMap.put("year2", yearPeriodList.get(2));
        headMap.put("year3", yearPeriodList.get(3));
        return headMap;
    }

}
