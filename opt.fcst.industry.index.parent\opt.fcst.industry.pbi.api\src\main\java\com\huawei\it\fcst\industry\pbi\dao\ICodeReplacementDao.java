/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.dao;

import com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO;
import com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ICodeReplacementDao Class
 *
 * @since 2024-07-04
 */
public interface ICodeReplacementDao {

    /**
     * @param codeReplacementVO
     * @return
     */
    int getVersionIdsYear(CodeReplacementVO codeReplacementVO);

    /**
     * @param codeReplacementVO
     * @return
     */
    int getBlurVersionIdsYear(CodeReplacementVO codeReplacementVO);

    /**
     * @param codeReplacementVO
     * @return
     */
    int getMonthMaxPeriodId(CodeReplacementVO codeReplacementVO);

    /**
     * @param codeReplacementVO
     * @return
     */
    int getMaxPeriodId(CodeReplacementVO codeReplacementVO);

    /**
     * SPART层级，走关系维表
     *
     * @param codeReplacementVO
     * @param oldSpartCode
     * @param newSpartCode
     * @return
     */
    List<CodeReplacementVO> getSpartIndexCostList(@Param("VO") CodeReplacementVO codeReplacementVO,
        @Param("oldSpartCode") String oldSpartCode, @Param("newSpartCode") String newSpartCode);

    /**
     *
     * get Repl Same Spart Index Cost List
     *
     * @param codeReplacementVO
     * @param newSpartCode
     * @return
     */
    List<CodeReplacementVO> getReplSameSpartIndexCostList(@Param("VO") CodeReplacementVO codeReplacementVO, @Param("newSpartCode") String newSpartCode);

    /**
     * SPART层级，走关系维表
     *
     * @param codeReplacementVO
     * @param oldSpartCode
     * @param newSpartCode
     * @return
     */
    List<CodeReplacementExpVO> getSpartIndexCostExpList(@Param("VO") CodeReplacementExpVO codeReplacementVO,
        @Param("oldSpartCode") String oldSpartCode, @Param("newSpartCode") String newSpartCode);

    /**
     * SPART层级，不走关系维表
     *
     * @param codeReplacementVO
     * @param spartCodeStr
     * @param prodTeamCode
     * @return
     */
    List<CodeReplacementVO> getTopSpartIndexCostList(@Param("VO") CodeReplacementVO codeReplacementVO,
        @Param("spartCode") String spartCodeStr, @Param("lv4ProdTeamCode") String prodTeamCode);

    /**
     * @param codeReplacementVO
     * @return
     */
    List<CodeReplacementVO> getPbiReplSameIndexCostList(@Param("VO") CodeReplacementVO codeReplacementVO);

    /**
     * @param codeReplacementVO
     * @return
     */
    List<CodeReplacementVO> getPbiIndexCostList(@Param("VO") CodeReplacementVO codeReplacementVO);

    /**
     * @param codeReplacementVO
     * @return
     */
    List<CodeReplacementExpVO> getPbiIndexCostExpList(@Param("VO") CodeReplacementExpVO codeReplacementVO);

    /**
     * @param codeReplacementVO
     * @param newSpartCode
     * @param oldSpartCode
     * @return
     */
    List<CodeReplacementVO> getBlurSpartIndexCostList(@Param("VO") CodeReplacementVO codeReplacementVO,
        @Param("oldSpartCode") String oldSpartCode, @Param("newSpartCode") String newSpartCode);

    /**
     * @param codeReplacementVO
     * @return
     */
    List<CodeReplacementVO> getPreBlurSpartIndexCostList(@Param("VO") CodeReplacementVO codeReplacementVO);

    /**
     * @param codeReplacementVO
     * @param newSpartCode
     * @param oldSpartCode
     * @return
     */
    List<CodeReplacementExpVO> getBlurSpartIndexCostExpList(@Param("VO") CodeReplacementExpVO codeReplacementVO,
        @Param("oldSpartCode") String oldSpartCode, @Param("newSpartCode") String newSpartCode);


    /**
     * @param codeReplacementVO
     * @return
     */
    List<CodeReplacementExpVO> getPreBlurSpartIndexCostExpList(@Param("VO") CodeReplacementExpVO codeReplacementVO);

    /**
     * SPART层级，不走关系维表
     *
     * @param codeReplacementVO
     * @param spartCode
     * @param lv4ProdTeamCode
     * @return
     */
    List<CodeReplacementVO> getBlurTopSpartIndexCostList(@Param("VO") CodeReplacementVO codeReplacementVO,
        @Param("spartCode") String spartCode,  @Param("customId") Long customId,@Param("lv4ProdTeamCode") String lv4ProdTeamCode);

    /**
     * @param codeReplacementVO
     * @param oldSpartCode
     * @param newSpartCode
     * @return
     */
    List<CodeReplacementVO> getSpartIndexQtyList(@Param("VO") CodeReplacementVO codeReplacementVO,
        @Param("oldSpartCode") String oldSpartCode, @Param("newSpartCode") String newSpartCode);

    /**
     * @param codeReplacementVO
     * @param oldSpartCode
     * @param newSpartCode
     * @return
     */
    List<CodeReplacementExpVO> getSpartIndexQtyExpList(@Param("VO") CodeReplacementExpVO codeReplacementVO,
        @Param("oldSpartCode") String oldSpartCode, @Param("newSpartCode") String newSpartCode);

    /**
     * @param codeReplacementVO
     * @return
     */
    List<CodeReplacementVO> getPbiIndexQtyList(@Param("VO") CodeReplacementVO codeReplacementVO);

    /**
     * @param codeReplacementVO
     * @return
     */
    List<CodeReplacementExpVO> getPbiIndexQtyExpList(@Param("VO") CodeReplacementExpVO codeReplacementVO);

    /**
     * @param codeReplacementVO
     * @param oldSpartCode
     * @param newSpartCode
     * @return
     */
    List<CodeReplacementVO> getBlurSpartIndexQtyList(@Param("VO") CodeReplacementVO codeReplacementVO,
        @Param("oldSpartCode") String oldSpartCode, @Param("newSpartCode") String newSpartCode);

    /**
     * @param codeReplacementVO
     * @return
     */
    List<CodeReplacementVO> getPreBlurSpartIndexQtyList(@Param("VO") CodeReplacementVO codeReplacementVO);

    /**
     * @param codeReplacementVO
     * @param oldSpartCode
     * @param newSpartCode
     * @return
     */
    List<CodeReplacementExpVO> getBlurSpartIndexQtyExpList(@Param("VO") CodeReplacementExpVO codeReplacementVO,
        @Param("oldSpartCode") String oldSpartCode, @Param("newSpartCode") String newSpartCode);


    List<CodeReplacementExpVO> getPreBlurSpartIndexQtyExpList(@Param("VO") CodeReplacementExpVO codeReplacementVO);

    /**
     * @param codeReplacementVO
     * @param plainText
     * @return
     */
    List<CodeReplacementVO> getPbiIndexCVList(@Param("VO") CodeReplacementVO codeReplacementVO, @Param("keyStr")String plainText);

    /**
     * SPART层级，走关系维表
     *
     * @param codeReplacementVO
     * @param oldSpartCode
     * @param newSpartCode
     * @return
     */
    List<CodeReplacementVO> getSpartIndexAccCostList(@Param("VO") CodeReplacementVO codeReplacementVO,
        @Param("oldSpartCode") String oldSpartCode, @Param("newSpartCode") String newSpartCode);

    /**
     * SPART层级，走关系维表
     *
     * @param codeReplacementVO
     * @param newSpartCode
     * @return
     */
    List<CodeReplacementVO> getReplSameSpartIndexAccCostList(@Param("VO") CodeReplacementVO codeReplacementVO, @Param("newSpartCode") String newSpartCode);

    /**
     * SPART层级，走关系维表
     *
     * @param codeReplacementVO
     * @param oldSpartCode
     * @param newSpartCode
     * @return
     */
    List<CodeReplacementExpVO> getSpartIndexAccCostExpList(@Param("VO") CodeReplacementExpVO codeReplacementVO,
        @Param("oldSpartCode") String oldSpartCode, @Param("newSpartCode") String newSpartCode);

    /**
     * SPART层级，不走关系维表
     *
     * @param codeReplacementVO
     * @param spartCodeStr
     * @param lv4ProdTeamCode
     * @return
     */
    List<CodeReplacementVO> getTopSpartIndexAccCostList(@Param("VO") CodeReplacementVO codeReplacementVO,
        @Param("spartCode") String spartCodeStr, @Param("lv4ProdTeamCode") String lv4ProdTeamCode);

    /**
     * @param codeReplacementVO
     * @return
     */
    List<CodeReplacementVO> getPbiIndexAccCostList(@Param("VO") CodeReplacementVO codeReplacementVO);

    /**
     * @param codeReplacementVO
     * @return
     */
    List<CodeReplacementVO> getReplSamePbiIndexAccCostList(@Param("VO") CodeReplacementVO codeReplacementVO);

    /**
     * @param codeReplacementVO
     * @return
     */
    List<CodeReplacementExpVO> getPbiIndexAccCostExpList(@Param("VO") CodeReplacementExpVO codeReplacementVO);

    /**
     * @param codeReplacementVO
     * @param newSpartCode
     * @param oldSpartCode
     * @return
     */
    List<CodeReplacementVO> getBlurSpartIndexAccCostList(@Param("VO") CodeReplacementVO codeReplacementVO,
        @Param("oldSpartCode") String oldSpartCode, @Param("newSpartCode") String newSpartCode);

    /**
     * @param codeReplacementVO
     * @return
     */
    List<CodeReplacementVO> getPreBlurSpartIndexAccCostList(@Param("VO") CodeReplacementVO codeReplacementVO);

    /**
     * @param codeReplacementVO
     * @param newSpartCode
     * @param oldSpartCode
     * @return
     */
    List<CodeReplacementExpVO> getBlurSpartIndexAccCostExpList(@Param("VO") CodeReplacementExpVO codeReplacementVO,
        @Param("oldSpartCode") String oldSpartCode, @Param("newSpartCode") String newSpartCode);

    List<CodeReplacementExpVO> getPreBlurSpartIndexAccCostExpList(@Param("VO") CodeReplacementExpVO codeReplacementVO);

    /**
     * SPART层级，不走关系维表
     *
     * @param codeReplacementVO
     * @param spartCodeStr
     * @param lv4ProdTeamCode
     * @return
     */
    List<CodeReplacementVO> getBlurTopSpartIndexAccCostList(@Param("VO") CodeReplacementVO codeReplacementVO,
        @Param("spartCode") String spartCodeStr, @Param("customId") Long customId, @Param("lv4ProdTeamCode") String lv4ProdTeamCode);

    /**
     * @param codeReplacementVO
     * @param oldSpartCode
     * @param newSpartCode
     * @return
     */
    List<CodeReplacementVO> getSpartIndexAccQtyList(@Param("VO") CodeReplacementVO codeReplacementVO,
        @Param("oldSpartCode") String oldSpartCode, @Param("newSpartCode") String newSpartCode);

    /**
     * @param codeReplacementVO
     * @param oldSpartCode
     * @param newSpartCode
     * @return
     */
    List<CodeReplacementExpVO> getSpartIndexAccQtyExpList(@Param("VO") CodeReplacementExpVO codeReplacementVO,
        @Param("oldSpartCode") String oldSpartCode, @Param("newSpartCode") String newSpartCode);

    /**
     * @param codeReplacementVO
     * @return
     */
    List<CodeReplacementVO> getPbiIndexAccQtyList(@Param("VO") CodeReplacementVO codeReplacementVO);

    /**
     * @param codeReplacementVO
     * @return
     */
    List<CodeReplacementExpVO> getPbiIndexAccQtyExpList(@Param("VO") CodeReplacementExpVO codeReplacementVO);

    /**
     * @param codeReplacementVO
     * @param oldSpartCode
     * @param newSpartCode
     * @return
     */
    List<CodeReplacementVO> getBlurSpartIndexAccQtyList(@Param("VO") CodeReplacementVO codeReplacementVO,
        @Param("oldSpartCode") String oldSpartCode, @Param("newSpartCode") String newSpartCode);

    List<CodeReplacementVO> getPreBlurSpartIndexAccQtyList(@Param("VO") CodeReplacementVO codeReplacementVO);

    /**
     * @param codeReplacementVO
     * @param oldSpartCode
     * @param newSpartCode
     * @return
     */
    List<CodeReplacementExpVO> getBlurSpartIndexAccQtyExpList(@Param("VO") CodeReplacementExpVO codeReplacementVO,
        @Param("oldSpartCode") String oldSpartCode, @Param("newSpartCode") String newSpartCode);

    List<CodeReplacementExpVO> getPreBlurSpartIndexAccQtyExpList(@Param("VO") CodeReplacementExpVO codeReplacementVO);

    /**
     * @param codeReplacementVO
     * @param plainText
     * @return
     */
    List<CodeReplacementVO> getPbiIndexAccCVList(@Param("VO") CodeReplacementVO codeReplacementVO, @Param("keyStr")String plainText);


    String getSpartsL1Name(@Param("VO") CodeReplacementExpVO dataVO,@Param("oldSpartCode") String spartCodeStr, @Param("newSpartCode") String spartCodeStr1);


    List<CodeReplacementVO> getBlurCombShipQuery(@Param("VO")CodeReplacementVO dataVO, @Param("spartCode")String spartCodeStr,
        @Param("customId") Long customId,@Param("lv4ProdTeamCode") String lv4ProdTeamCode) ;

    List<CodeReplacementVO> getCombShipQuery(@Param("VO")CodeReplacementVO dataVO, @Param("spartCode")String spartCodeStr,
        @Param("lv4ProdTeamCode")String newProdTeamCode);

    List<CodeReplacementVO> getBlurAccCombShipQuery(@Param("VO") CodeReplacementVO dataVO,
        @Param("spartCode") String spartCodeStr, @Param("customId") Long customId,
        @Param("lv4ProdTeamCode") String lv4ProdTeamCode);

    List<CodeReplacementVO> getCombAccShipQuery(@Param("VO") CodeReplacementVO dataVO,
        @Param("spartCode") String spartCodeStr, @Param("lv4ProdTeamCode") String lv4ProdTeamCode);

    List<CodeReplacementVO> getBlurSpartCVList(@Param("VO") CodeReplacementVO codeReplacementVO,
        @Param("spartCode") String oldSpartCode, @Param("customId") Long customId, @Param("lv4ProdTeamCode")String groupType,
        @Param("groupType") String spart, @Param("keyStr")String plainText);

    List<CodeReplacementVO> getBlurSpartCVExpList(@Param("VO") CodeReplacementVO codeReplacementVO,
        @Param("spartCode") String oldSpartCode, @Param("customId") Long customId, @Param("lv4ProdTeamCode")String groupType,
        @Param("codeType") String spart, @Param("keyStr")String plainText);

    List<CodeReplacementVO> getPreBlurSpartCVExpList(@Param("VO") CodeReplacementVO codeReplacementVO,@Param("codeType") String codeType);

    List<CodeReplacementVO> getSpartCVList(@Param("VO") CodeReplacementVO codeReplacementVO,
        @Param("spartCode") String spartCodeStr,@Param("lv4ProdTeamCode")String lv4ProdTeamCode, @Param("groupType") String groupType,
        @Param("keyStr")String plainText);

    List<CodeReplacementVO> getBlurSpartCVAccList(@Param("VO") CodeReplacementVO dataVO,
        @Param("spartCode") String spartCodeStr, @Param("customId") Long customId,
        @Param("lv4ProdTeamCode") String lv4ProdTeamCode, @Param("groupType") String groupType, @Param("keyStr")String plainText);

    List<CodeReplacementVO> getBlurSpartCVAccExpList(@Param("VO") CodeReplacementVO dataVO,
        @Param("spartCode") String spartCodeStr, @Param("customId") Long customId,
        @Param("lv4ProdTeamCode") String lv4ProdTeamCode, @Param("codeType") String codeType, @Param("keyStr")String plainText);

    List<CodeReplacementVO> getPreBlurSpartCVAccExpList(@Param("VO") CodeReplacementVO dataVO,@Param("codeType") String codeType);

    List<CodeReplacementVO> getSpartCVAccList(@Param("VO") CodeReplacementVO dataVO,
        @Param("spartCode") String spartCodeStr, @Param("lv4ProdTeamCode") String lv4ProdTeamCode, @Param("groupType") String groupType,
        @Param("keyStr")String plainText);
}
