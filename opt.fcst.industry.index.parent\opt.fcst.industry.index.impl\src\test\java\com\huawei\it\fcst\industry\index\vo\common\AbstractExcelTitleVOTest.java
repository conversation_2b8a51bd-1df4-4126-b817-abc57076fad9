/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.common;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

/**
 * AbstractExcelTitleVOTest Class
 *
 * <AUTHOR>
 * @since 2023/4/22
 */
public class AbstractExcelTitleVOTest extends BaseVOCoverUtilsTest<AbstractExcelTitleVO> {

    @Override
    protected Class<AbstractExcelTitleVO> getTClass() {
        return AbstractExcelTitleVO.class;
    }
}