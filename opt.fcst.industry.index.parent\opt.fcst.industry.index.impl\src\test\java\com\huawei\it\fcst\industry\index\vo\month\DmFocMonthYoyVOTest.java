/*
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.month;


import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

import java.sql.Timestamp;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/5/9
 */
public class DmFocMonthYoyVOTest extends BaseVOCoverUtilsTest<DmFocMonthYoyVO> {
    @Override
    protected Class<DmFocMonthYoyVO> getTClass() { return DmFocMonthYoyVO.class; }

    @Test
    public void testMethod() {
        DmFocMonthYoyVO dmFocActualCostVO = new DmFocMonthYoyVO();
        dmFocActualCostVO.setDelFlag("Y");
        dmFocActualCostVO.getDelFlag();
        dmFocActualCostVO.setCreatedBy("1175");
        dmFocActualCostVO.getCreatedBy();
        dmFocActualCostVO.getCreationDate();
        dmFocActualCostVO.setGroupCode("1163A");
        dmFocActualCostVO.getGroupCode();
        dmFocActualCostVO.setVersionId(11L);
        dmFocActualCostVO.getVersionId();
        dmFocActualCostVO.setGroupCnName("元器");
        dmFocActualCostVO.getGroupCnName();
        dmFocActualCostVO.setGroupLevel("LV3");
        dmFocActualCostVO.getGroupLevel();
        dmFocActualCostVO.setId(12L);
        dmFocActualCostVO.getId();
        dmFocActualCostVO.setViewFlag("0");
        dmFocActualCostVO.getViewFlag();
        dmFocActualCostVO.setParentCode("54211");
        dmFocActualCostVO.getParentCode();
        dmFocActualCostVO.setPeriodYear(2023L);
        dmFocActualCostVO.getPeriodYear();
        dmFocActualCostVO.getLastUpdateDate();
        dmFocActualCostVO.setLastUpdatedBy("166");
        dmFocActualCostVO.getLastUpdatedBy();
        dmFocActualCostVO.setProdRndTeamCode("code");
        dmFocActualCostVO.getProdRndTeamCode();
        dmFocActualCostVO.setProdRndTeamCnName("test");
        dmFocActualCostVO.getProdRndTeamCnName();
        dmFocActualCostVO.setYoyFlag("Y");
        dmFocActualCostVO.getYoyFlag();
        dmFocActualCostVO.setYoyRate(15D);
        dmFocActualCostVO.getYoyRate();
        dmFocActualCostVO.setYoyPercent("0.223");
        dmFocActualCostVO.getYoyPercent();
        dmFocActualCostVO.getBasePeriodId();
        dmFocActualCostVO.setPeriodId(202305L);
        dmFocActualCostVO.setCostType("P");
        dmFocActualCostVO.getCostType();
        dmFocActualCostVO.setCustomCnName("ddddd");
        dmFocActualCostVO.getCustomCnName();
        dmFocActualCostVO.setDmsCnName("1314");
        dmFocActualCostVO.getDmsCnName();
        dmFocActualCostVO.setDmsCode("232425");
        dmFocActualCostVO.getDmsCode();
        dmFocActualCostVO.setDimensionCode("D000022");
        dmFocActualCostVO.setDimensionCnName("PCS(射频)");
        dmFocActualCostVO.setDimensionSubCategoryCode("S000338");
        dmFocActualCostVO.setDimensionSubCategoryCnName("TDD AAU");
        dmFocActualCostVO.setDimensionSubDetailCnName("M000767");
        dmFocActualCostVO.setDimensionSubDetailCode("32T32R");
        dmFocActualCostVO.getDimensionCode();
        dmFocActualCostVO.getDimensionCnName();
        dmFocActualCostVO.getDimensionSubCategoryCode();
        dmFocActualCostVO.getDimensionSubCategoryCnName();
        dmFocActualCostVO.getDimensionSubDetailCnName();
        dmFocActualCostVO.getDimensionSubDetailCode();
        dmFocActualCostVO.setParentCode("1244");
        dmFocActualCostVO.getParentCode();
        dmFocActualCostVO.setParentCnName("光");
        dmFocActualCostVO.getParentCnName();
        Timestamp timestamp = new Timestamp(new Date().getTime());
        dmFocActualCostVO.setCreationDate(timestamp);
        dmFocActualCostVO.setLastUpdateDate(timestamp);
        DmFocMonthYoyVO.builder().id(11L).delFlag("N").basePeriodId(202010L).createdBy("test1").lastUpdatedBy("test1")
                .creationDate(timestamp).lastUpdateDate(timestamp).groupCnName("name1").groupCode("code1").periodId(202010L)
                .groupLevel("LV2").periodYear(202010L).parentCode("code1").viewFlag("1").yoyFlag("1").prodRndTeamCnName("lv1").prodRndTeamCode("code1").yoyPercent("1")
                .yoyRate(12D).build();
        Assert.assertNotNull(dmFocActualCostVO);
    }
}