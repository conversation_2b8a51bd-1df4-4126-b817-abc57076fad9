/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.dao;

import com.huawei.it.fcst.industry.pbi.vo.month.IctMonthAnalysisVO;
import com.huawei.it.fcst.industry.pbi.vo.month.PeriodIdDimVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * IctMonthCostIdxDao Interface
 *
 * <AUTHOR>
 * @since 2024-07-05
 */
public interface IctMonthCostIdxDao {

    /**
     * 查询产业成本指数（ICT）
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<IctMonthAnalysisVO> findCostIndexVOList(@Param("monthAnalysisVO") IctMonthAnalysisVO monthAnalysisVO);

    List<IctMonthAnalysisVO> findMainFlagCostIndexVOList(@Param("monthAnalysisVO") IctMonthAnalysisVO monthAnalysisVO);

    /**
     * 查询产业成本指数（ICT）
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<IctMonthAnalysisVO> findDmFcstCombCostIndexVOList(@Param("monthAnalysisVO") IctMonthAnalysisVO monthAnalysisVO);

    /**
     * 查询产业成本指数（ICT）虚化
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list IctMonthAnalysisVOs
     */
    List<IctMonthAnalysisVO> findBlurCostIndexVOList(@Param("monthAnalysisVO") IctMonthAnalysisVO monthAnalysisVO);

    /**
     * 查询ICT产业成本指数（多子项）
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<IctMonthAnalysisVO> findMultiCostIndexVOList(@Param("monthAnalysisVO") IctMonthAnalysisVO monthAnalysisVO);


    /**
     * 查询ICT产业成本指数（多子项）组合
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<IctMonthAnalysisVO> findPriceIndexCombChartByMultiDim(@Param("monthAnalysisVO") IctMonthAnalysisVO monthAnalysisVO);


    /**
     * 查询ICT产业成本指数（多子项）虚化
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<IctMonthAnalysisVO> findBlurMultiCostIndexVOList(@Param("monthAnalysisVO") IctMonthAnalysisVO monthAnalysisVO);

    /**
     * 查询ICT产业成本指数（多子项）虚化最小层级
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<IctMonthAnalysisVO> findBlurMinMultiCostIndexList(@Param("monthAnalysisVO") IctMonthAnalysisVO monthAnalysisVO);

    String findActualMonth(@Param("monthAnalysisVO") IctMonthAnalysisVO monthAnalysisVO);

    List<PeriodIdDimVO> findStartEndTime(@Param("versionId") Long versionId);

    /**
     * 查询降成本目标对比
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<IctMonthAnalysisVO> findReduceCostTargetList(@Param("monthAnalysisVO") IctMonthAnalysisVO monthAnalysisVO);

    /**
     * 查询降成本目标对比 虚化
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<IctMonthAnalysisVO> findBlurReduceCostTargetList(@Param("monthAnalysisVO") IctMonthAnalysisVO monthAnalysisVO);

    /**
     * 查询降成本目标对比 组合
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<IctMonthAnalysisVO> findCombReduceCostTargetList(@Param("monthAnalysisVO") IctMonthAnalysisVO monthAnalysisVO);

    int findCostIndexCount(@Param("monthAnalysisVO") IctMonthAnalysisVO monthAnalysisVO);

    int findCombCostIndexCount(@Param("monthAnalysisVO") IctMonthAnalysisVO monthAnalysisVO);

    int findMultiCostIndexCount(@Param("monthAnalysisVO") IctMonthAnalysisVO monthAnalysisVO);

    int findMixCombCostIndexCount(@Param("monthAnalysisVO") IctMonthAnalysisVO monthAnalysisVO);


    int findMixCostIndexCount(@Param("monthAnalysisVO") IctMonthAnalysisVO monthAnalysisVO);

    /**
     * 查询默认基期
     *
     * @param monthAnalysisVO
     * @return basePeriodId
     */
    String findBasePeriodId(@Param("monthAnalysisVO") IctMonthAnalysisVO monthAnalysisVO);

    String findMixBasePeriodId(@Param("monthAnalysisVO") IctMonthAnalysisVO monthAnalysisVO);

    String callFuncRefreshData(@Param("jsonStr") String jsonStr);

}