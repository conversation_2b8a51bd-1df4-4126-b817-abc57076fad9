/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.exeption;

import com.huawei.it.jalor5.core.exception.ApplicationException;

/**
 * 导出异常类定义
 */
public class TemplateNotFundException extends ApplicationException {
    private static final long serialVersionUID = 1L;

    public TemplateNotFundException(String errorCode) {
        super(errorCode);
    }

    public TemplateNotFundException(String errorCode, Exception exception) {
        super(errorCode, exception);
    }

    public TemplateNotFundException(String errorCode, Object... args) {
        super(errorCode, args);
    }

    public TemplateNotFundException(String errorCode, Throwable innerException, Object... args) {
        super(errorCode, innerException, args);
    }
}
