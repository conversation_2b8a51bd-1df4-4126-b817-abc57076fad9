/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.utils;

import com.huawei.it.auth.exception.AuthException;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.log.ILogger;
import com.huawei.it.jalor5.core.log.JalorLoggerFactory;
import com.huawei.it.jalor5.core.request.IRequestContextHolder;
import com.huawei.it.jalor5.core.request.impl.Application;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.soa.common.SoaException;
import com.huawei.it.soa.util.SoaAppTokenClientUtil;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;

import javax.inject.Inject;

/**
 * AccessTokenClient
 *
 * <AUTHOR>
 * @since 2020-7-15
 */
@Component
@Slf4j
public class AccessTokenClient {
    private static final ILogger LOGGER = JalorLoggerFactory.getLogger(AccessTokenClient.class);

    private static final String SOAACCESS_TOKEN = "SOAAccessToken";

    @Value("${application.appId}")
    private String APPID;

    @Value("${httpClient.sgov.environment}")
    String environment;

    @Value("${hic.cfgcenter.url}")
    private String hic_cfgcenter_url;

    @Value("${hic.region}")
    private String hic_region;

    @Value("${application.appId}")
    private String appId;

    @Value("${httpClient.sgov.credential}")
    private String credential;

    @Inject
    private IRequestContextHolder requestContextHolder;

    /**
     * accessToken
     *
     * @return String
     */
    @Cacheable(cacheNames = SOAACCESS_TOKEN, unless = "#result == null")
    public String accessToken() {
        try {
            System.setProperty("hic_env", environment);
            System.setProperty("hic_cfgcenter_url", hic_cfgcenter_url);
            System.setProperty("hic_region", hic_region);
            SoaAppTokenClientUtil.initial(appId, credential);
            return SoaAppTokenClientUtil.getBasicTokenByAppCredential();
        } catch (AuthException | SoaException e) {
            LOGGER.error(e.getMessage());
        }
        return "";
    }

    /**
     * evictToken
     */
    @CacheEvict(cacheNames = SOAACCESS_TOKEN, allEntries = true)
    @Scheduled(initialDelay = 5, fixedRate = 1000 * 60)
    public void evictToken() {
        initRequestContext();
        log.info("evictToken");
    }

    private void initRequestContext() {
        log.info(">>>Begin AccessTokenClient::initRequestContext");
        RequestContext requestContext = (RequestContext) RequestContext.getCurrent(true);
        if (requestContext == null) {
            requestContext = new RequestContext();
            requestContext.setApplication(Application.getCurrent());
            RequestContextManager.setCurrent(requestContext);
        }
        try {
            requestContextHolder.begin(APPID);
            log.info(">>>End AccessTokenClient::initRequestContext");
        } catch (ApplicationException ex) {
            log.error(">>>ApplicationException:{}", ex.getMessage());
        }
    }

    /**
     * headers
     *
     * @return Map
     */
    public Map<String, String> headers() {
        Map<String, String> headers = new HashMap<>(4);
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", accessToken());
        return headers;
    }
}
