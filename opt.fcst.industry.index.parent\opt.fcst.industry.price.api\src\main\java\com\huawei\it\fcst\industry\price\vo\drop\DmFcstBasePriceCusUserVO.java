/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.vo.drop;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * DmFcstDimInfoVO Class
 *
 * <AUTHOR>
 * @since 2024/7/1
 */
// 此@data注解不可以被替换成getter和setter
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DmFcstBasePriceCusUserVO {

    private Long createdBy;

    private Timestamp creationDate;

    private Long lastUpdatedBy;

    private Timestamp lastUpdatedate;

    private String delFlag;

    /**
     * 组合id
     */
    private Long customId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 角色id
     */
    private String roleId;

}
