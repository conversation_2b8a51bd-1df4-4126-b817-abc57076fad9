/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.utils;

import com.huawei.it.fcst.industry.index.vo.common.AbstractExcelTitleVO;
import com.huawei.it.fcst.industry.index.vo.common.BranchExcelTitleVO;
import com.huawei.it.fcst.industry.index.vo.common.Constants;
import com.huawei.it.fcst.industry.index.vo.common.ExcelVO;
import com.huawei.it.fcst.industry.index.vo.common.LeafExcelTitleVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.util.CollectionUtil;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * The class ExcelUtilPro.java
 *
 * <AUTHOR>
 * @since 2022/2/22
 */
@Component
public class ExcelUtilPro {
    @Value("${spring.profiles.active}")
    private String env;

    /**
     * [服务名称]implReadHead
     *
     * <AUTHOR>
     * @param heads 入参
     * @return List<ExcelVO>
     * @throws CommonApplicationException
     */
    public List<ExcelVO> implReadHead(String[] heads) {
        List<ExcelVO> list = new ArrayList<>();
        Arrays.asList(heads).stream().forEach(head -> {
            ExcelVO vo = new ExcelVO();
            if (head.contains(Constants.XHX.getValue())) {
                String[] titleHead = head.split(Constants.XHX.getValue());
                vo.setHeadName(titleHead[0]);
                vo.setHeadType(titleHead[1]);
            } else {
                vo.setHeadName(Constants.SNULL.getValue());
                vo.setHeadType(Constants.SNULL.getValue());
            }
            list.add(vo);
        });
        return list;
    }


    /**
     * [服务名称]adjustTitleVoList
     *
     * @param excelTitleVOS            入参
     * @param selectedLeafExcelTitleVO 入参
     * @return int
     * @throws CommonApplicationException
     * <AUTHOR>
     */
    public int adjustTitleVoList(List<AbstractExcelTitleVO> excelTitleVOS,
        List<AbstractExcelTitleVO> selectedLeafExcelTitleVO) {
        ExcelUtilPro excelUtilPro = new ExcelUtilPro();
        excelUtilPro.initTitleVoList(excelTitleVOS, selectedLeafExcelTitleVO, 1, 0);
        // 叶子结点的高度调整，
        int maxX2 = 0;
        for (AbstractExcelTitleVO leafExcelTitleVO : selectedLeafExcelTitleVO) {
            if (leafExcelTitleVO.getX2() > maxX2) {
                maxX2 = leafExcelTitleVO.getX2();
            }
        }
        for (AbstractExcelTitleVO leafExcelTitleVO : selectedLeafExcelTitleVO) {
            if (leafExcelTitleVO.getX2() < maxX2) {
                leafExcelTitleVO.setX2(maxX2);
            }
        }
        return maxX2 + 1;
    }

    private String getDateString(String cellvalue, Cell cell) {
        String value;
        if (DateUtil.isCellDateFormatted(cell)) {
            value = cn.hutool.core.date.DateUtil.format(cell.getDateCellValue(), Constants.DATEFORMAT_SS.getValue());
        } else {
            value = String.valueOf(cell.getNumericCellValue());
        }
        return value;
    }

    private String getRichNumber(String cellvalue, Cell cell) {
        String value;
        try {
            value = String.valueOf(cell.getNumericCellValue());
        } catch (IllegalStateException e) {
            value = String.valueOf(cell.getRichStringCellValue());
        }
        return value;
    }

    private void adjustChildExcelTitleVoWidth(List<AbstractExcelTitleVO> childTitleVoList, int gap) {
        if (CollectionUtil.isNullOrEmpty(childTitleVoList)) {
            return;
        }
        int index = childTitleVoList.size() - 1;
        while (index >= 0) {
            if (childTitleVoList.get(index).getSelected() != null && childTitleVoList.get(index).getSelected()) {
                break;
            } else {
                index--;
            }
        }
        if (index < 0) {
            return;
        }
        AbstractExcelTitleVO tailExcelTitleVo = childTitleVoList.get(index);
        int adjustWidth = tailExcelTitleVo.getWidth() + gap;
        tailExcelTitleVo.setWidth(adjustWidth);
        if (tailExcelTitleVo instanceof BranchExcelTitleVO) {
            adjustChildExcelTitleVoWidth(((BranchExcelTitleVO) tailExcelTitleVo).getChildExcelTitleList(), gap);
        }
    }

    /**
     * 结果
     *
     * @param titleVoList 参数
     * @param selectedLeafExcelTitleVO 参数
     * @param xx 参数
     * @param yy 参数
     * @return 结果
     */
    public Map initTitleVoList(List<AbstractExcelTitleVO> titleVoList,
                                List<AbstractExcelTitleVO> selectedLeafExcelTitleVO, int xx, int yy) {
        Map initMap = new HashMap();
        int allLeafCount = 0;
        boolean isHasChildSelected = false;
        int allChildWidthSum = 0;
        int tempYy = yy;
        int tempXx = xx;
        for (AbstractExcelTitleVO titleVO : titleVoList) {
            if ((titleVO instanceof LeafExcelTitleVO) && (titleVO.getSelected()
            )) {
                titleVO.setX1(tempXx);
                titleVO.setY1(tempYy);
                titleVO.setX2(tempXx);
                titleVO.setY2(tempYy);
                tempYy++;
                allLeafCount++;
                titleVO.setSelected(true);
                isHasChildSelected = true;
                allChildWidthSum += titleVO.getWidth();
                selectedLeafExcelTitleVO.add(titleVO);
            } else if (titleVO instanceof BranchExcelTitleVO) {
                Map childMap = initTitleVoList(((BranchExcelTitleVO) titleVO).getChildExcelTitleList(),
                        selectedLeafExcelTitleVO, tempXx + 1, tempYy);
                boolean isCurrentHasChildSelected = Boolean.valueOf(childMap.get("isHasChildSelected").toString());
                if (isCurrentHasChildSelected) {
                    int currentLeafCount = getCurrentLeafCount(tempYy, tempXx, titleVO, childMap);
                    allLeafCount += currentLeafCount;
                    tempYy += currentLeafCount;
                    int currentChildWidthSum = Integer.valueOf(childMap.get("allChildWidthSum").toString());
                    allChildWidthSum = getAllChildWidthSum(allChildWidthSum, titleVO, currentChildWidthSum);
                }
                isHasChildSelected = isHasChildSelected || isCurrentHasChildSelected;
            } else {
                continue;
            }
        }
        initMap.put("allLeafCount", allLeafCount);
        initMap.put("isHasChildSelected", isHasChildSelected);
        initMap.put("allChildWidthSum", allChildWidthSum);
        return initMap;
    }

    private int getAllChildWidthSum(int allChildWidthSum, AbstractExcelTitleVO titleVO, int currentChildWidthSum) {
        if (currentChildWidthSum > titleVO.getWidth()) {
            titleVO.setWidth(currentChildWidthSum);
            allChildWidthSum += currentChildWidthSum;
        }
        if (currentChildWidthSum < titleVO.getWidth()) {
            int gap = titleVO.getWidth() - currentChildWidthSum;
            adjustChildExcelTitleVoWidth(((BranchExcelTitleVO) titleVO).getChildExcelTitleList(), gap);
            allChildWidthSum += titleVO.getWidth();
        }
        return allChildWidthSum;
    }

    private int getCurrentLeafCount(int tempYy, int tempXx, AbstractExcelTitleVO titleVO, Map childMap) {
        titleVO.setX1(tempXx);
        titleVO.setY1(tempYy);
        titleVO.setX2(tempXx);
        titleVO.setSelected(true);
        int currentLeafCount = Integer.valueOf(childMap.get("allLeafCount").toString());
        titleVO.setY2(tempYy + currentLeafCount - 1);
        return currentLeafCount;
    }

    /**
     * [服务名称]getStringCellValue
     *
     * @param cell     入参
     * @param headType 入参
     * @return Object
     * @throws CommonApplicationException
     * <AUTHOR>
     */
    public String getStringCellValue(Cell cell, String headType)
            throws CommonApplicationException {
        Optional<String> ele = Optional.ofNullable(null);
        if (headType.equals("VARCHAR") && cell == null) {
            return Constants.DEFAULT.getValue();
        } else if (headType.equals("NUMERIC") && cell == null) {
            return ele.orElse(null);
        } else if (headType.equals("NUMERIC") && cell != null) {
            try {
                String value = getStringCellValue(cell);
                return StrUtil.isBlank(value) ? ele.orElse(null) : NumberUtil.parseNumber(value).toString();
            } catch (Exception e1) {
                throw new CommonApplicationException(
                        "第" + (cell.getRowIndex() + 2) + "行,第" + (cell.getColumnIndex() + 1) + "列不是数字类型");
            }
        } else if ( ("VARCHAR").equals(headType) && cell != null) {
            return getStringCellValue(cell);
        } else {
            return ele.orElse(null);
        }
    }

    private String getStringCellValue(Cell cell) {
        if (cell == null) {
            return Constants.DEFAULT.getValue();
        }
        String cellvalue = Constants.DEFAULT.getValue();
        switch (cell.getCellType()) {
            case STRING:
                cellvalue = cell.getStringCellValue();
                break;
            case NUMERIC:
                cellvalue = getDateString(cellvalue, cell);
                break;
            case BLANK:
                cellvalue = Constants.DEFAULT.getValue();
                break;
            case BOOLEAN:
                cellvalue = String.valueOf(cell.getBooleanCellValue());
                break;
            case FORMULA:
                cellvalue = getRichNumber(cellvalue, cell);
                break;
            case ERROR:
                cellvalue = Constants.ERROR.getValue();
                break;
            default:
                cellvalue = Constants.DEFAULT.getValue();
                break;
        }
        return cellvalue.endsWith(".0") ? cellvalue.replace(".0", "") : cellvalue;
    }
}
