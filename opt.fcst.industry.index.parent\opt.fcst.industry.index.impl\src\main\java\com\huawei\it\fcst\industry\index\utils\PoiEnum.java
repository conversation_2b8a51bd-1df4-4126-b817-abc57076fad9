/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.utils;

import com.huawei.it.fcst.industry.index.constant.Constant;
import com.huawei.it.fcst.industry.index.impl.annual.StatisticsExcelService;
import com.huawei.it.fcst.industry.index.vo.common.AbstractExcelTitleVO;
import com.huawei.it.fcst.industry.index.vo.common.BranchExcelTitleVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFoiImpExpRecordVO;
import com.huawei.it.fcst.industry.index.vo.common.ExcelUploadVO;
import com.huawei.it.fcst.industry.index.vo.common.ExportExcelVo;
import com.huawei.it.fcst.industry.index.vo.common.LeafExcelTitleVO;
import com.huawei.it.fcst.util.CellStyles;
import com.huawei.it.fcst.util.FileNameUtil;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.util.CollectionUtil;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.DefaultIndexedColorMap;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.awt.Color;
import java.io.IOException;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * The class PoiEnum.java
 *
 * <AUTHOR>
 * @since 2022年1月12日
 */
public enum PoiEnum {
    INSTANCE;

    private static final Logger LOGGER = LoggerFactory.getLogger(PoiEnum.class);

    private static final String MICROSOFT_YAHEI = "微软雅黑";

    private static final String SONGSTYLE = "宋体";

    private static final String TITLECOLORFLAG = "TITLECOLORFLAG";

    private static final String CONFIGHEADERCOLORFLAG = "CONFIGHEADERCOLORFLAG";

    private static final String HEADERCOLORFLAG = "HEADERCOLORFLAG";

    private static final String BASEPERIODFLAG = "BASEPERIODFLAG";

    /**
     * 导出excel
     *
     * @param os OutputStream
     * @param exportExcelVoList List<ExportExcelVo>
     * @throws IOException
     */
    public static DmFoiImpExpRecordVO exportExcel(OutputStream os, List<ExportExcelVo> exportExcelVoList)
        throws IOException, CommonApplicationException {
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        SXSSFWorkbook fileWorkbook = new SXSSFWorkbook();

        ExcelUploadVO excelUploadVO = new ExcelUploadVO();
        // 导出excel
        if (null != os) {
            getWorkbook(workbook, exportExcelVoList, excelUploadVO);
            workbook.write(os);
        }
        // 生成一个新的excel放到服务器上
        getWorkbook(fileWorkbook, exportExcelVoList, excelUploadVO);
        String fileName = FileNameUtil.dealFileName(excelUploadVO.getFileName());
        Long userId = excelUploadVO.getUserId();

        // 插入数据，上传文件
        return StatisticsExcelService.uploadExportExcel(fileWorkbook, excelUploadVO.getCount(), fileName, userId);
    }

    private static void getWorkbook(SXSSFWorkbook workbook, List<ExportExcelVo> exportExcelVoList,
        ExcelUploadVO excelUploadVO) throws IOException {
        int count = 0;
        for (ExportExcelVo exportExcelVo : exportExcelVoList) {
            List<AbstractExcelTitleVO> excelTitleVOS = exportExcelVo.getTitleVoList(); // 表头
            List<AbstractExcelTitleVO> selectedLeafExcelTitleVO = exportExcelVo.getSelectedLeafExcelTitleVO();
            int titleRowCount = exportExcelVo.getTitleRowCount();
            String sheetName = exportExcelVo.getSheetName();
            if (sheetName.contains("/") || sheetName.contains("\\")) {
                sheetName = sheetName.replace("/", "-");
            }
            SXSSFSheet sheet = workbook.createSheet(sheetName);
            Map<Integer, Row> titleRowMap = new HashMap<>();
            for (int i = 1; i < titleRowCount; i++) {
                Row row = sheet.createRow(i);
                if (!titleRowMap.containsKey(i)) {
                    titleRowMap.put(i, row);
                }
            }
            boolean isLock = isLockExcelSheet(selectedLeafExcelTitleVO);
            // 设置Excel单元格样式
            CellStyles cellStyles = getCellStyles(workbook);
            // 插入表单信息
            List<AbstractExcelTitleVO> formsVOs = exportExcelVo.getFormInfoVo();
            extracted(workbook, exportExcelVo, excelTitleVOS, selectedLeafExcelTitleVO, sheet, titleRowMap, cellStyles, formsVOs);
            // 插入数据
            List<Map> list = exportExcelVo.getList();
            Boolean mergeCell = exportExcelVo.getMergeCell();
            Long userId = exportExcelVo.getUserId();
            count = count + list.size();
            excelUploadVO.setCount(count);
            excelUploadVO.setFileName(FileNameUtil.dealFileName(exportExcelVo.getFileName()));
            excelUploadVO.setMergeCell(mergeCell);
            excelUploadVO.setUserId(userId);
            addData(titleRowCount, list, selectedLeafExcelTitleVO, sheet, cellStyles,exportExcelVo);
            if (isLock) {
                sheet.setAutoFilter(
                    new CellRangeAddress(sheet.getFirstRowNum() + titleRowCount - 1, sheet.getLastRowNum(), 0,
                        selectedLeafExcelTitleVO.size() - 1));
                lockSheet(sheet);
            }
            // 避免占用太多内存
            sheet.flushRows();
        }
    }

    public static void extracted(SXSSFWorkbook workbook, ExportExcelVo exportExcelVo, List<AbstractExcelTitleVO> excelTitleVOS, List<AbstractExcelTitleVO> selectedLeafExcelTitleVO, SXSSFSheet sheet, Map<Integer, Row> titleRowMap, CellStyles cellStyles, List<AbstractExcelTitleVO> formsVOs) {
        if (exportExcelVo.getMergeCell()) {
            // 合并单元格
            CellRangeAddress rangeAddress = new CellRangeAddress(0, 0, 0, selectedLeafExcelTitleVO.size() - 1);
            sheet.addMergedRegion(rangeAddress);
            addTitleInfoVo(sheet, formsVOs, selectedLeafExcelTitleVO, cellStyles);
            // 插入表头信息
            if (!CollectionUtil.isNullOrEmpty(excelTitleVOS) && !CollectionUtil.isNullOrEmpty(
                    selectedLeafExcelTitleVO)) {
                creatHead(workbook, excelTitleVOS, titleRowMap, sheet);
            }
        } else {
            addFormInfoVo(sheet, formsVOs, cellStyles,selectedLeafExcelTitleVO);
            // 插入表头信息
            if (!CollectionUtil.isNullOrEmpty(excelTitleVOS) && !CollectionUtil.isNullOrEmpty(
                    selectedLeafExcelTitleVO)) {
                creatConfigHead(workbook, excelTitleVOS, selectedLeafExcelTitleVO, titleRowMap, sheet);
            }
        }
    }

    public static boolean isLockExcelSheet(List<AbstractExcelTitleVO> selectedLeafExcelTitleVO) {
        boolean isLock = false;
        if (!CollectionUtil.isNullOrEmpty(selectedLeafExcelTitleVO)) {
            for (AbstractExcelTitleVO selectedTitleVO : selectedLeafExcelTitleVO) {
                if (selectedTitleVO.getEditable() != null && !selectedTitleVO.getEditable()) {
                    isLock = true;
                    break;
                }
            }
        }
        return isLock;
    }

    public static CellStyles getCellStyles(SXSSFWorkbook workbook) {
        CellStyle bodyRateStyle = createRateValueHeaderCellStyle(workbook);
        CellStyle bodyStyles = createLeftCellStyle(workbook);
        CellStyle bodyEditStyles = createLeftEditCellStyle(workbook);
        CellStyle bodyTitleStyles1 = createTitleCellStyle1(workbook);
        CellStyle bodyTitleStyles2 = createTitleCellStyle2(workbook);
        CellStyle bodyTitleStyles3 = createTitleCellStyle3(workbook);
        return CellStyles.builder()
            .bodyRateStyle(bodyRateStyle)
            .bodyStyles(bodyStyles)
            .bodyEditStyles(bodyEditStyles)
            .bodyTitleStyles1(bodyTitleStyles1)
            .bodyTitleStyles2(bodyTitleStyles2)
            .bodyTitleStyles3(bodyTitleStyles3)
            .build();
    }

    private static CellStyle createTitleCellStyle3(SXSSFWorkbook workbook) {
        CellStyle baseCellStyle = createBaseCellStyle(workbook);
        baseCellStyle.setDataFormat(workbook.createDataFormat().getFormat(Constant.StrEnum.EXCEL_TEXT.getValue()));
        baseCellStyle.setLocked(true);
        Font ft = setFont(workbook, baseCellStyle);
        baseCellStyle.setFont(ft);
        return baseCellStyle;
    }

    private static CellStyle createRateValueHeaderCellStyle(SXSSFWorkbook workbook) {
        CellStyle style = createBaseCellStyle(workbook);
        DataFormat dataFormat = workbook.createDataFormat();
        style.setDataFormat(dataFormat.getFormat("0.00%"));
        style.setLocked(true);
        return style;
    }

    private static CellStyle createLeftCellStyle(SXSSFWorkbook workbook) {
        CellStyle baseCellStyle = createBaseCellStyle(workbook);
        baseCellStyle.setDataFormat(workbook.createDataFormat().getFormat(Constant.StrEnum.EXCEL_TEXT.getValue()));
        baseCellStyle.setLocked(true);
        return baseCellStyle;
    }

    private static CellStyle createLeftEditCellStyle(SXSSFWorkbook workbook) {
        CellStyle baseEditCellStyle = createBaseCellStyle(workbook);
        baseEditCellStyle.setDataFormat(workbook.createDataFormat().getFormat(Constant.StrEnum.EXCEL_TEXT.getValue()));
        baseEditCellStyle.setLocked(false);
        return baseEditCellStyle;
    }

    private static CellStyle createTitleCellStyle1(SXSSFWorkbook workbook) {
        CellStyle baseEditCellStyle = createCellStyle(workbook, TITLECOLORFLAG, false);
        baseEditCellStyle.setDataFormat(workbook.createDataFormat().getFormat(Constant.StrEnum.EXCEL_TEXT.getValue()));
        Font ft = setFont(workbook, baseEditCellStyle);
        ft.setColor((short) 1);
        baseEditCellStyle.setFont(ft);
        baseEditCellStyle.setLocked(true);
        return baseEditCellStyle;
    }

    private static CellStyle createTitleCellStyle2(SXSSFWorkbook workbook) {
        CellStyle baseEditCellStyle = createCellStyle(workbook, "", true);
        baseEditCellStyle.setDataFormat(workbook.createDataFormat().getFormat(Constant.StrEnum.EXCEL_TEXT.getValue()));
        Font ft = setFont(workbook, baseEditCellStyle);
        ft.setBold(false);
        baseEditCellStyle.setAlignment(HorizontalAlignment.LEFT);
        baseEditCellStyle.setFont(ft);
        baseEditCellStyle.setLocked(true);
        return baseEditCellStyle;
    }

    private static void addFormInfoVo(SXSSFSheet sheet, List<AbstractExcelTitleVO> formsVOs, CellStyles cellStyles,List<AbstractExcelTitleVO> selectedLeafExcelTitleVO) {
        Row row = sheet.createRow(0);
        int columnIndex = 0;
        for (AbstractExcelTitleVO excelTitleVO : formsVOs) {
            if (excelTitleVO instanceof LeafExcelTitleVO) {
                if ("costType".equals(((LeafExcelTitleVO) excelTitleVO).getColumnCode())) {
                    columnIndex = 0;
                } else if ("caliberFlag".equals(((LeafExcelTitleVO) excelTitleVO).getColumnCode())) {
                    columnIndex = 1;
                } else if ("granule".equals(((LeafExcelTitleVO) excelTitleVO).getColumnCode())) {
                    columnIndex = 2;
                } else if ("overseaFlag".equals(((LeafExcelTitleVO) excelTitleVO).getColumnCode())) {
                    columnIndex = 3;
                } else if ("bgFlag".equals(((LeafExcelTitleVO) excelTitleVO).getColumnCode())) {
                    columnIndex = 4;
                } else if ("version".equals(((LeafExcelTitleVO) excelTitleVO).getColumnCode())) {
                    CellRangeAddress rangeAddress = new CellRangeAddress(0, 0, selectedLeafExcelTitleVO.size()-4, selectedLeafExcelTitleVO.size()-3);
                    sheet.addMergedRegion(rangeAddress);
                    columnIndex = selectedLeafExcelTitleVO.size()-4;
                } else if("versionName".equals(((LeafExcelTitleVO) excelTitleVO).getColumnCode())) {
                    CellRangeAddress rangeAddress = new CellRangeAddress(0, 0, selectedLeafExcelTitleVO.size()-2, selectedLeafExcelTitleVO.size()-1);
                    sheet.addMergedRegion(rangeAddress);
                    columnIndex = selectedLeafExcelTitleVO.size()-2;
                } else {
                    columnIndex = selectedLeafExcelTitleVO.size()-1;
                }
                String cellValue = getInfoCellValue(excelTitleVO);
                Cell headCel11 = getHeadCell3(cellStyles, row, columnIndex);
                setInfoCellValue(headCel11, columnIndex, cellValue, excelTitleVO.getWidth(),
                    ((LeafExcelTitleVO) excelTitleVO).getDataType());
                columnIndex++;
            }
        }
    }

    private static void addTitleInfoVo(SXSSFSheet sheet, List<AbstractExcelTitleVO> formsVOs,
        List<AbstractExcelTitleVO> selectedLeafExcelTitleVO, CellStyles cellStyles) {
        Row row = sheet.createRow(0);
        int columnIndex = 0;
        for (AbstractExcelTitleVO excelTitleVO : formsVOs) {
            Cell headCel11 = null;
            if (excelTitleVO instanceof LeafExcelTitleVO && ("title").equals(((LeafExcelTitleVO) excelTitleVO).getColumnCode())) {
                headCel11 = getTitleCell(cellStyles, row, columnIndex);
            } else {
                headCel11 = getActualMonthCell(cellStyles, row, columnIndex);
            }
            String cellValue = getInfoCellValue(excelTitleVO);
            setInfoCellValue(headCel11, columnIndex, cellValue, excelTitleVO.getWidth(),
                ((LeafExcelTitleVO) excelTitleVO).getDataType());
            columnIndex = columnIndex + selectedLeafExcelTitleVO.size();
        }
    }

    private static void setInfoCellValue(Cell headCel11, int columnIndex, String cellValue, Integer width,
        CellType dataType) {
        if (StringUtils.isEmpty(cellValue)) {
            headCel11.setCellType(CellType.BLANK);
        } else {
            headCel11.setCellValue(cellValue);
            headCel11.setCellType(dataType);
        }
        headCel11.getSheet().setColumnWidth(columnIndex, width);
    }

    private static String getInfoCellValue(AbstractExcelTitleVO excelTitleVO) {
        Object cellValue = excelTitleVO.getValue();
        if (ObjectUtils.isEmpty(cellValue)) {
            cellValue = "";
        }
        return cellValue.toString();
    }

    public static void lockSheet(SXSSFSheet sh) {
        sh.lockSelectLockedCells(false);
        sh.lockSelectUnlockedCells(false);
        sh.lockFormatCells(false);
        sh.lockFormatColumns(false);
        sh.lockFormatRows(false);
        sh.lockDeleteRows(false);
        sh.lockAutoFilter(false);
        sh.enableLocking();
    }

    public static void addData(int titleRowCount, List<Map> list, List<AbstractExcelTitleVO> selectedLeafExcelTitleVO,
                               Sheet sheet, CellStyles cellStyles, ExportExcelVo exportExcelVo) {
        setCellValueAndAddData(titleRowCount, list, selectedLeafExcelTitleVO, sheet, cellStyles, exportExcelVo);
    }

    private static void setCellValueAndAddData(int titleRowCount, List<Map> list, List<AbstractExcelTitleVO> selectedLeafExcelTitleVO, Sheet sheet, CellStyles cellStyles, ExportExcelVo exportExcelVo) {
        int rowNum = titleRowCount;
        int thirdLineNum = titleRowCount + 1;
        int forthLineNum = titleRowCount + 2;
        int fifthLineNum = titleRowCount + 3;
        int sixthLineNum = titleRowCount + 4;
        int sevenLineNum = titleRowCount + 5;
        for (Map rowMap : list) {
            Row row = sheet.createRow(rowNum++);
            int columnIndex = 0;
            addDataSub(selectedLeafExcelTitleVO, cellStyles, rowMap, row, columnIndex);
            if (rowNum == thirdLineNum) {
                setThreeForms(exportExcelVo, selectedLeafExcelTitleVO, cellStyles, row);
            }
            if (rowNum == forthLineNum) {
                setFourForms(exportExcelVo, selectedLeafExcelTitleVO, cellStyles, row);
            }
            if (rowNum == fifthLineNum) {
                setFiveForms(exportExcelVo, selectedLeafExcelTitleVO, cellStyles, row);
            }
            if (rowNum == sixthLineNum) {
                setSixForms(exportExcelVo, selectedLeafExcelTitleVO, cellStyles, row);
            }
            if (rowNum == sevenLineNum) {
                setSevenForms(exportExcelVo, selectedLeafExcelTitleVO, cellStyles, row);
            }
        }
        setCellValue(list, selectedLeafExcelTitleVO, sheet, cellStyles, exportExcelVo, rowNum, titleRowCount);
    }

    private static void setCellValue(List<Map> list, List<AbstractExcelTitleVO> selectedLeafExcelTitleVO, Sheet sheet, CellStyles cellStyles, ExportExcelVo exportExcelVo, int rowNum, int titleRowCount) {
        int thirdNum = titleRowCount + 1;
        int forthNum = titleRowCount + 2;
        int fifthNum = titleRowCount + 3;
        int sixthNum = titleRowCount + 4;
        int sevenNum = titleRowCount + 5;
        if (list.size() <= 4) {
            for (int i = list.size() + 1; i <= sevenNum; i++) {
                Row row = sheet.createRow(rowNum++);
                if (rowNum == thirdNum) {
                    setThreeForms(exportExcelVo, selectedLeafExcelTitleVO, cellStyles, row);
                }
                if (rowNum == forthNum) {
                    setFourForms(exportExcelVo, selectedLeafExcelTitleVO, cellStyles, row);
                }
                if (rowNum == fifthNum) {
                    setFiveForms(exportExcelVo, selectedLeafExcelTitleVO, cellStyles, row);
                }
                if (rowNum == sixthNum) {
                    setSixForms(exportExcelVo, selectedLeafExcelTitleVO, cellStyles, row);
                }
                if (rowNum == sevenNum) {
                    setSevenForms(exportExcelVo, selectedLeafExcelTitleVO, cellStyles, row);
                }
            }
        }
    }

    public static void addConfigData(int titleRowCount, List<Map> list, List<AbstractExcelTitleVO> selectedLeafExcelTitleVO, Sheet sheet, CellStyles cellStyles) {
        int rowNum = titleRowCount;
        for (Map rowMap : list) {
            Row row = sheet.createRow(rowNum++);
            int columnIndex = 0;
            addDataSub(selectedLeafExcelTitleVO, cellStyles, rowMap, row, columnIndex);
        }
    }

    private static void setThreeForms(ExportExcelVo exportExcelVo,List<AbstractExcelTitleVO> selectedLeafExcelTitleVO,
                                      CellStyles cellStyles,Row row) {
        List<AbstractExcelTitleVO> threeTitleList = exportExcelVo.getThreeTitleList();
        if (CollectionUtils.isNotEmpty(threeTitleList)) {
            for (AbstractExcelTitleVO threeTitleVO : threeTitleList) {
                if (threeTitleVO instanceof LeafExcelTitleVO) {
                    Cell headCell2 = getActualMonthCell(cellStyles, row, selectedLeafExcelTitleVO.size());
                    String cellValue = getInfoCellValue(threeTitleVO);
                    setInfoCellValue(headCell2, selectedLeafExcelTitleVO.size(), cellValue, threeTitleVO.getWidth(),
                            ((LeafExcelTitleVO) threeTitleVO).getDataType());
                }
            }
        }
    }
    private static void setFourForms(ExportExcelVo exportExcelVo,List<AbstractExcelTitleVO> selectedLeafExcelTitleVO,
                                      CellStyles cellStyles,Row row) {
        List<AbstractExcelTitleVO> fourTitleList = exportExcelVo.getFourTitleList();
        if (CollectionUtils.isNotEmpty(fourTitleList)) {
            for (AbstractExcelTitleVO fourTitleVO : fourTitleList) {
                if (fourTitleVO instanceof LeafExcelTitleVO && ("overSeaFlagValue").equals(((LeafExcelTitleVO) fourTitleVO).getColumnCode())) {
                    Cell headCell2 = getActualMonthCell(cellStyles, row, selectedLeafExcelTitleVO.size());
                    String cellValue = getInfoCellValue(fourTitleVO);
                    setInfoCellValue(headCell2, selectedLeafExcelTitleVO.size(), cellValue, fourTitleVO.getWidth(),
                            ((LeafExcelTitleVO) fourTitleVO).getDataType());
                }
            }
        }
    }
    private static void setFiveForms(ExportExcelVo exportExcelVo,List<AbstractExcelTitleVO> selectedLeafExcelTitleVO,
                                      CellStyles cellStyles,Row row) {
        List<AbstractExcelTitleVO> fiveTitleList = exportExcelVo.getFiveTitleList();
        if (CollectionUtils.isNotEmpty(fiveTitleList)) {
            for (AbstractExcelTitleVO fiveTitleVO : fiveTitleList) {
                if (fiveTitleVO instanceof LeafExcelTitleVO && ("bgFlagValue").equals(((LeafExcelTitleVO) fiveTitleVO).getColumnCode())) {
                    Cell headCell2 = getActualMonthCell(cellStyles, row, selectedLeafExcelTitleVO.size());
                    String cellValue = getInfoCellValue(fiveTitleVO);
                    setInfoCellValue(headCell2, selectedLeafExcelTitleVO.size(), cellValue, fiveTitleVO.getWidth(),
                            ((LeafExcelTitleVO) fiveTitleVO).getDataType());
                }
            }
        }
    }
    private static void setSixForms(ExportExcelVo exportExcelVo,List<AbstractExcelTitleVO> selectedLeafExcelTitleVO,
                                     CellStyles cellStyles,Row row) {
        List<AbstractExcelTitleVO> sixTitleList = exportExcelVo.getSixTitleList();
        if (CollectionUtils.isNotEmpty(sixTitleList)) {
            for (AbstractExcelTitleVO sixTitleVO : sixTitleList) {
                if (sixTitleVO instanceof LeafExcelTitleVO && ("viewFlagValue").equals(((LeafExcelTitleVO) sixTitleVO).getColumnCode())) {
                    Cell headCell3 = getActualMonthCell(cellStyles, row, selectedLeafExcelTitleVO.size());
                    String cellValue = getInfoCellValue(sixTitleVO);
                    setInfoCellValue(headCell3, selectedLeafExcelTitleVO.size(), cellValue, sixTitleVO.getWidth(),
                            ((LeafExcelTitleVO) sixTitleVO).getDataType());
                }
            }
        }
    }

    private static void setSevenForms(ExportExcelVo exportExcelVo,List<AbstractExcelTitleVO> selectedLeafExcelTitleVO,
                                    CellStyles cellStyles,Row row) {
        List<AbstractExcelTitleVO> sevenTitleList = exportExcelVo.getSevenTitleList();
        if (CollectionUtils.isNotEmpty(sevenTitleList)) {
            for (AbstractExcelTitleVO sevenTitleVO : sevenTitleList) {
                if (sevenTitleVO instanceof LeafExcelTitleVO && ("actualMonth").equals(((LeafExcelTitleVO) sevenTitleVO).getColumnCode())) {
                    Cell headCell3 = getActualMonthCell(cellStyles, row, selectedLeafExcelTitleVO.size());
                    String cellValue = getInfoCellValue(sevenTitleVO);
                    setInfoCellValue(headCell3, selectedLeafExcelTitleVO.size(), cellValue, sevenTitleVO.getWidth(),
                            ((LeafExcelTitleVO) sevenTitleVO).getDataType());
                }
            }
        }
    }


    private static void addDataSub(List<AbstractExcelTitleVO> selectedLeafExcelTitleVO, CellStyles cellStyles, Map rowMap, Row row, int columnIndex) {
        for (AbstractExcelTitleVO excelTitleVO : selectedLeafExcelTitleVO) {
            if (excelTitleVO instanceof LeafExcelTitleVO) {
                String cellValue = getCellValue(rowMap, (LeafExcelTitleVO) excelTitleVO);
                if (((LeafExcelTitleVO) excelTitleVO).getDataType().equals(CellType.NUMERIC)) {
                    Cell headCel11 = getHeadCel12(cellStyles, row, columnIndex);
                    setCellValueAndStyle(headCel11, columnIndex, cellValue, excelTitleVO.getWidth(),
                        CellType.NUMERIC);
                } else if (excelTitleVO.getEditable()) {
                    Cell headCel11 = getHeadCel13(cellStyles, row, columnIndex);
                    setCellValueAndStyle(headCel11, columnIndex, cellValue, excelTitleVO.getWidth(),
                        ((LeafExcelTitleVO) excelTitleVO).getDataType());
                } else {
                    Cell headCel11 = getHeadCel11(cellStyles, row, columnIndex);
                    setCellValueAndStyle(headCel11, columnIndex, cellValue, excelTitleVO.getWidth(),
                        ((LeafExcelTitleVO) excelTitleVO).getDataType());
                }
                columnIndex++;
            }
        }
    }

    private static Cell getHeadCel11(CellStyles cellStyles, Row row, int columnIndex) {
        Cell headCel11 = row.createCell(columnIndex);
        if (cellStyles != null) {
            headCel11.setCellStyle(cellStyles.getBodyStyles());
        }
        return headCel11;
    }

    private static Cell getHeadCell3(CellStyles cellStyles, Row row, int columnIndex) {
        Cell headCel11 = row.createCell(columnIndex);
        if (cellStyles != null) {
            headCel11.setCellStyle(cellStyles.getBodyTitleStyles3());
        }
        return headCel11;
    }

    private static Cell getTitleCell(CellStyles cellStyles, Row row, int columnIndex) {
        Cell headCel11 = row.createCell(columnIndex);
        if (cellStyles != null) {
            headCel11.setCellStyle(cellStyles.getBodyTitleStyles1());
        }
        return headCel11;
    }

    private static Cell getActualMonthCell(CellStyles cellStyles, Row row, int columnIndex) {
        Cell headCel11 = row.createCell(columnIndex);
        if (cellStyles != null) {
            headCel11.setCellStyle(cellStyles.getBodyTitleStyles2());
        }
        return headCel11;
    }

    private static Cell getHeadCel12(CellStyles cellStyle, Row row, int columnIndex) {
        Cell headCel11 = row.createCell(columnIndex);
        if (cellStyle != null) {
            headCel11.setCellStyle(cellStyle.getBodyRateStyle());
        }
        return headCel11;
    }

    private static Cell getHeadCel13(CellStyles cellStyle, Row row, int columnIndex) {
        Cell headCel11 = row.createCell(columnIndex);
        if (cellStyle != null) {
            headCel11.setCellStyle(cellStyle.getBodyEditStyles());
        }
        return headCel11;
    }

    private static String getCellValue(Map rowMap, LeafExcelTitleVO excelTitleVO) {
        Object cellValue = rowMap.get(excelTitleVO.getDataKey());
        if (ObjectUtils.isEmpty(cellValue)) {
            cellValue = "";
        }
        return cellValue.toString();
    }

    private static void creatHead(SXSSFWorkbook workbook, List<AbstractExcelTitleVO> excelTitleVOS,
                                        Map<Integer, Row> titleRowMap, Sheet sheet) {
        for (AbstractExcelTitleVO excelTitleVO : excelTitleVOS) {
            if (excelTitleVO.getSelected() == null || !excelTitleVO.getSelected()) {
                continue;
            }
            creatHeadSub(workbook, excelTitleVOS, titleRowMap, sheet, excelTitleVO);
        }
    }

    private static void creatHeadSub(SXSSFWorkbook workbook, List<AbstractExcelTitleVO> excelTitleVOS, Map<Integer, Row> titleRowMap, Sheet sheet, AbstractExcelTitleVO excelTitleVO) {
        if (excelTitleVO instanceof LeafExcelTitleVO && (("viewFlagCnName").equals(((LeafExcelTitleVO) excelTitleVO).getColumnCode()) || ("costType").equals(((LeafExcelTitleVO) excelTitleVO).getColumnCode()))) {
            CellStyle headStyle1 = createCellStyle(workbook, BASEPERIODFLAG, true);
            Font ft = setFont2(workbook, headStyle1);
            headStyle1.setFont(ft);
            setCellValueAndStyle2(excelTitleVOS, titleRowMap, headStyle1, excelTitleVO);
        } else {
            CellStyle headStyle1 = createCellStyle(workbook, HEADERCOLORFLAG, true);
            Font ft = setFont(workbook, headStyle1);
            headStyle1.setFont(ft);
            setCellValueAndStyle2(excelTitleVOS, titleRowMap, headStyle1, excelTitleVO);
        }
        if (excelTitleVO.getX1() < excelTitleVO.getX2() || excelTitleVO.getY1() < excelTitleVO.getY2()) {
            sheet.addMergedRegion(
                    new CellRangeAddress(excelTitleVO.getX1(), excelTitleVO.getX2(), excelTitleVO.getY1(),
                            excelTitleVO.getY2()));
        }
        if (excelTitleVO instanceof BranchExcelTitleVO) {
            if (!CollectionUtil.isNullOrEmpty(((BranchExcelTitleVO) excelTitleVO).getChildExcelTitleList())) {
                creatHead(workbook, ((BranchExcelTitleVO) excelTitleVO).getChildExcelTitleList(),
                        titleRowMap, sheet);
            }
        }
    }


    private static void creatConfigHead(SXSSFWorkbook workbook, List<AbstractExcelTitleVO> excelTitleVOS,
        List<AbstractExcelTitleVO> selectedLeafExcelTitleVO, Map<Integer, Row> titleRowMap, Sheet sheet) {
        for (AbstractExcelTitleVO excelTitleVO : excelTitleVOS) {
            if (excelTitleVO.getSelected() == null || !excelTitleVO.getSelected()) {
                continue;
            }
            configHeadSub(workbook, selectedLeafExcelTitleVO, titleRowMap, sheet, excelTitleVO);
        }
    }

    private static void configHeadSub(SXSSFWorkbook workbook, List<AbstractExcelTitleVO> selectedLeafExcelTitleVO, Map<Integer, Row> titleRowMap, Sheet sheet, AbstractExcelTitleVO excelTitleVO) {
        if (!excelTitleVO.getEditable() && ((LeafExcelTitleVO) excelTitleVO).getDataType()
            .equals(CellType.STRING)) {
            CellStyle headStyle1 = createCellStyle(workbook, CONFIGHEADERCOLORFLAG, true);
            Font ft1 = setFont(workbook, headStyle1);
            headStyle1.setFont(ft1);
            setCellValueAndStyle(selectedLeafExcelTitleVO, titleRowMap, headStyle1, excelTitleVO);
        }
        if (excelTitleVO.getEditable() && ((LeafExcelTitleVO) excelTitleVO).getDataType().equals(CellType.STRING)) {
            CellStyle headStyle2 = createCellStyle(workbook, CONFIGHEADERCOLORFLAG, true);
            Font ft2 = setFont(workbook, headStyle2);
            headStyle2.setFont(ft2);
            setCellValueAndStyle(selectedLeafExcelTitleVO, titleRowMap, headStyle2, excelTitleVO);
        }
        if (((LeafExcelTitleVO) excelTitleVO).getDataType().equals(CellType.NUMERIC)) {
            CellStyle headStyle3 = createCellStyle(workbook, CONFIGHEADERCOLORFLAG, true);
            Font ft3 = setFont(workbook, headStyle3);
            ft3.setColor((short) 2);
            headStyle3.setFont(ft3);
            setCellValueAndStyle(selectedLeafExcelTitleVO, titleRowMap, headStyle3, excelTitleVO);
        }
        if (excelTitleVO.getX1() < excelTitleVO.getX2() || excelTitleVO.getY1() < excelTitleVO.getY2()) {
            sheet.addMergedRegion(
                new CellRangeAddress(excelTitleVO.getX1(), excelTitleVO.getX2(), excelTitleVO.getY1(),
                    excelTitleVO.getY2()));
        }
        if (excelTitleVO instanceof BranchExcelTitleVO) {
            if (!CollectionUtil.isNullOrEmpty(((BranchExcelTitleVO) excelTitleVO).getChildExcelTitleList())) {
                creatConfigHead(workbook, ((BranchExcelTitleVO) excelTitleVO).getChildExcelTitleList(),
                        selectedLeafExcelTitleVO, titleRowMap, sheet);
            }
        }
    }

    private static void setCellValueAndStyle(List<AbstractExcelTitleVO> selectedLeafExcelTitleVO,
        Map<Integer, Row> titleRowMap, CellStyle headStyle, AbstractExcelTitleVO excelTitleVO) {
        for (int rowIndex = excelTitleVO.getX1(); rowIndex <= excelTitleVO.getX2(); rowIndex++) {
            Row row = titleRowMap.get(rowIndex);
            for (int columnIndex = excelTitleVO.getY1(); columnIndex <= excelTitleVO.getY2(); columnIndex++) {
                String cellValue = "";
                if (rowIndex == excelTitleVO.getX1() && columnIndex == excelTitleVO.getY1()) {
                    cellValue = excelTitleVO.getValue();
                }
                Cell headCel11 = row.createCell(columnIndex);
                if (headStyle != null) {
                    headCel11.setCellStyle(headStyle);
                }
                setCellValueAndStyle(headCel11, columnIndex, cellValue, selectedLeafExcelTitleVO.get(columnIndex).getWidth(), CellType.STRING);
            }
        }
    }

    private static void setCellValueAndStyle2(List<AbstractExcelTitleVO> excelTitleVOS,
        Map<Integer, Row> titleRowMap, CellStyle headStyle, AbstractExcelTitleVO abstractExcelTitleVO) {
        for (int rowIndex = abstractExcelTitleVO.getX1(); rowIndex <= abstractExcelTitleVO.getX2(); rowIndex++) {
            Row row = titleRowMap.get(rowIndex);
            for (int columnIndex = abstractExcelTitleVO.getY1(); columnIndex <= abstractExcelTitleVO.getY2(); columnIndex++) {
                String cellValue = "";
                if (rowIndex == abstractExcelTitleVO.getX1() && columnIndex == abstractExcelTitleVO.getY1()) {
                    cellValue = abstractExcelTitleVO.getValue();
                }
                Cell headCel11 = row.createCell(columnIndex);
                if (headStyle != null) {
                    headCel11.setCellStyle(headStyle);
                }
                setCellValueAndStyle(headCel11, columnIndex, cellValue, excelTitleVOS.get(columnIndex).getWidth(), CellType.STRING);
            }
        }
    }

    /**
     * 创建表头样式.
     *
     * @param workbook 参数
     * @param titleColor excel表格部分颜色
     * @param isAlignment 是否左对齐
     * @return [CellStyle]
     * <AUTHOR>
     * @since 2020年12月16日
     */
    private static CellStyle createCellStyle(SXSSFWorkbook workbook, String titleColor, Boolean isAlignment) {
        CellStyle style = workbook.createCellStyle();
        if ((("").equals(titleColor) || BASEPERIODFLAG.equals(titleColor)) ? false : true) {
            style.setBorderBottom(BorderStyle.THIN);
            style.setBorderLeft(BorderStyle.THIN);
            style.setBorderTop(BorderStyle.THIN);
            style.setBorderRight(BorderStyle.THIN);
        }
        // 水平居左
        if (isAlignment) {
            style.setAlignment(HorizontalAlignment.LEFT);
        } else {
            style.setAlignment(HorizontalAlignment.CENTER);
        }
        style.setAlignment(HorizontalAlignment.CENTER);
        // 垂直居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置背景色 用于标题
        if (CONFIGHEADERCOLORFLAG.equals(titleColor)) {
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            ((XSSFCellStyle) style).setFillForegroundColor(new XSSFColor(new Color(100, 198, 255),new DefaultIndexedColorMap()));
        }
        if (HEADERCOLORFLAG.equals(titleColor)) {
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            ((XSSFCellStyle) style).setFillForegroundColor(new XSSFColor(new Color(197, 217, 241), new DefaultIndexedColorMap()));
        }
        if (BASEPERIODFLAG.equals(titleColor)) {
            style.setAlignment(HorizontalAlignment.LEFT);
        }
        if (TITLECOLORFLAG.equals(titleColor)) {
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            ((XSSFCellStyle) style).setFillForegroundColor(new XSSFColor(new Color(31, 73, 125),new DefaultIndexedColorMap()));
        }
        return style;
    }

    /**
     * 创建单元格的格式.
     *
     * @param workbook 参数
     * @return [CellStyle]
     * <AUTHOR>
     * @since 2020年12月16日
     */
    public static CellStyle createBaseCellStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setWrapText(true);
        // 水平居中
        style.setAlignment(HorizontalAlignment.CENTER);
        // 垂直居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        // 创建字体
        Font ft = workbook.createFont();
        // 加粗字体
        ft.setBold(false);
        ft.setFontName(SONGSTYLE);
        ft.setFontHeightInPoints((short) 11);
        // 加载字体
        style.setFont(ft);
        return style;
    }

    private static Font setFont(SXSSFWorkbook workbook, CellStyle style) {
        // 创建字体
        Font ft = workbook.createFont();
        // 加粗字体
        ft.setBold(true);
        ft.setFontName(MICROSOFT_YAHEI);
        ft.setFontHeightInPoints((short) 11);
        style.setFont(ft);
        return ft;
    }

    private static Font setFont2(SXSSFWorkbook workbook, CellStyle style) {
        // 创建字体
        Font ft = workbook.createFont();
        ft.setFontName(MICROSOFT_YAHEI);
        ft.setFontHeightInPoints((short) 11);
        style.setFont(ft);
        return ft;
    }

    /**
     * setCellValue设置单元格内容及样式
     *
     * @param headCel11 单元格
     * @param cell 列
     * @param value 值
     * @param columnWidth 列宽
     * @param dataType 单元格数据类型
     */
    public static void setCellValueAndStyle(Cell headCel11, int cell, String value, int columnWidth,
        CellType dataType) {
        if (StringUtils.isEmpty(value)) {
            headCel11.setCellType(CellType.BLANK);
        } else {
            if ("NUMERIC".equals(dataType.name())) {
                headCel11.setCellValue(Double.parseDouble(value));
            } else {
                headCel11.setCellValue(value);
            }
            headCel11.setCellType(dataType);
        }
        headCel11.getSheet().setColumnWidth(cell, columnWidth);
    }
}
