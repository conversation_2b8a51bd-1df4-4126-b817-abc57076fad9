/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.enums;

/**
 * 返回响应码实体
 *
 * <AUTHOR>
 * @since 2022-09-26
 */
public enum ResultCodeEnum {
    PARAM_ERROR("400", "参数错误！"),
    NOT_PERMISSION("401", "未授权，无法访问！"),
    UNAUTHORIZED_OPERATION("407", "无权操作他人的页面！"),
    SUCCESS("200", "操作成功！"),
    VERSION_PARAM_ERROR("402", "参数错误！"),
    FAILURE("201", "当前维度版本已刷新过系统，无需重复刷新！"),
    SERVER_ERROR("500", "服务器异常，请联系管理员！");

    private String message;
    private String code;

    ResultCodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public String getCode() {
        return code;
    }


}