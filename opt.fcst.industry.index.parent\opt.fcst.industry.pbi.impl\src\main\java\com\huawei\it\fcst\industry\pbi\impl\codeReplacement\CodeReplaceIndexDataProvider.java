/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.codeReplacement;

import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.industry.pbi.dao.ICodeReplacementDao;
import com.huawei.it.fcst.industry.pbi.enums.IndustryConstEnum;
import com.huawei.it.fcst.export.IExcelExportDataProvider;
import com.huawei.it.fcst.industry.pbi.utils.CostReductUtils;
import com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO;
import com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO;
import com.huawei.it.fcst.export.vo.ExcelExportContext;
import com.huawei.it.jalor5.core.base.PageVO;
import org.apache.commons.lang3.StringUtils;

import javax.inject.Inject;
import javax.inject.Named;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 导出服务-月度指数图
 */
@Named("IExcelExport.CodeReplaceIndexDataProvider")
public class CodeReplaceIndexDataProvider implements IExcelExportDataProvider {
    @Inject
    private ICodeReplacementDao iCodeDao;

    @Inject
    private CodeReplacementService codeReplacementService;

    // 普通查询
    @Override
    public List<?> getData(Serializable conditionObject, PageVO pageVO) {
        CodeReplacementExpVO dataVO = (CodeReplacementExpVO) conditionObject;
        // 关系层级
        List<CodeReplacementExpVO> spartIndexCostList = null;
        if (CostReductUtils.getSpecailRoleMark(dataVO)) {
            return spartIndexCostList;
        }
        // 预处理的虚化
        if (dataVO.getPreCus()) {
            CostReductUtils.getPreCusIndexCostList(dataVO);
            spartIndexCostList = iCodeDao.getPreBlurSpartIndexCostExpList(dataVO);
        } else {
            spartIndexCostList = codeReplaceIndexData(dataVO, spartIndexCostList);
        }
        return spartIndexCostList;
    }

    private List<CodeReplacementExpVO> codeReplaceIndexData(CodeReplacementExpVO dataVO, List<CodeReplacementExpVO> spartIndexCostList) {
        String selectionLevel = dataVO.getQueryLevel();
        String isNeedBlur = dataVO.getIsNeedBlur();
        if (CommonConstant.SELECTION_LEVEL_SPART_LEVEL.equals(selectionLevel)) {
            if (Boolean.TRUE.equals(Boolean.valueOf(isNeedBlur))) {
                spartIndexCostList = iCodeDao.getBlurSpartIndexCostExpList(dataVO,
                        CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()), CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()));
            } else {
                spartIndexCostList = iCodeDao.getSpartIndexCostExpList(dataVO,
                        CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()), CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()));
            }
        } else if (CommonConstant.SELECTION_LEVEL_TOP_SPART_LEVEL.equals(selectionLevel)) {
            // topSpart 有 4情况。 此处做按单个的虚化属性做数据查询
            if (isNeedBlur.split("\\|").length == 2) {
                List<CodeReplacementVO> newSpartIndexCostList = codeReplacementService.getTopSpartIndexCostList(dataVO,
                        Boolean.valueOf(isNeedBlur.split("\\|")[0]), CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()),
                        dataVO.getNewCustomId(), dataVO.getNewProdTeamCode());
                List<CodeReplacementVO> oldSpartIndexCostList = codeReplacementService.getTopSpartIndexCostList(dataVO,
                        Boolean.valueOf(isNeedBlur.split("\\|")[1]), CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()),
                        dataVO.getOldCustomId(), dataVO.getOldProdTeamCode());
                spartIndexCostList = CostReductUtils.processIndexList(newSpartIndexCostList, oldSpartIndexCostList);
            }
        } else if (CommonConstant.SELECTION_LEVEL_PBI_LEVEL.equals(selectionLevel)) {
            spartIndexCostList = iCodeDao.getPbiIndexCostExpList(dataVO);
        }
        return spartIndexCostList;
    }

    @Override
    public Map<String, Object> getHeader(Serializable conditionObject, Map<String, Object> parameters) {
        ExcelExportContext context = (ExcelExportContext) conditionObject;
        CodeReplacementExpVO codeReplacementExpVO = (CodeReplacementExpVO) context.getConditionObject();
        // 自定义表头
        Map<String, Object> headMap = new HashMap<>();
        CostReductUtils.setCommonHeaderInfo(codeReplacementExpVO, headMap);
        String selectionLevel = codeReplacementExpVO.getQueryLevel();
        if (CommonConstant.SELECTION_LEVEL_SPART_LEVEL.equals(selectionLevel)) {
            headMap.put("maxPeriodId", iCodeDao.getMaxPeriodId(codeReplacementExpVO));
            headMap.put("title", "ICT产业-" + StringUtils.defaultString(codeReplacementExpVO.getLv1ProdTeamName()) + "-"
                    + codeReplacementExpVO.getRelationDesc());
        } else if (CommonConstant.SELECTION_LEVEL_TOP_SPART_LEVEL.equals(selectionLevel)) {
            headMap.put("maxPeriodId", iCodeDao.getMonthMaxPeriodId(codeReplacementExpVO));
            headMap.put("title", "ICT产业-自定义新/老编码");
            headMap.put("oldSpartName", "(" + CostReductUtils.getSpartCodeStr(codeReplacementExpVO.getOldSpartCode()) + ")");
            headMap.put("newSpartName", "(" + CostReductUtils.getSpartCodeStr(codeReplacementExpVO.getNewSpartCode()) + ")");
        } else if (CommonConstant.SELECTION_LEVEL_PBI_LEVEL.equals(selectionLevel)) {
            String title = CostReductUtils.setTilteName(codeReplacementExpVO);
            if (codeReplacementExpVO.getPreCus()) {
                headMap.put("title", title + "-" + CostReductUtils.setRelationDesc(codeReplacementExpVO));
            } else {
                headMap.put("title", title);
            }
            if (IndustryConstEnum.GRANULARITY_TYPE.PROD.getValue().equals(codeReplacementExpVO.getGranularityType())) {
                headMap.put("maxPeriodId", iCodeDao.getMaxPeriodId(codeReplacementExpVO));
            }
        }
        return headMap;
    }
}
