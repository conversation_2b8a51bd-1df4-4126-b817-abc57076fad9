/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2022. All rights reserved.
 */

package com.huawei.it.fcst.vo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.poi.ss.usermodel.CellType;

/**
 * HeaderVo Class
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class HeaderVo {
    /**
     * 标题
     */
    private String title;

    /**
     * 字段名
     */
    private String field;

    private CellType dataType;

    private Boolean isEditable;

    private Integer width;

}
