/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.dao;

import com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstBaseCusUserVO;

import java.util.List;

/**
 * IDmFocViewInfoDao
 *
 * <AUTHOR>
 * @since 2023/3/14
 */
public interface IDmFcstBaseCusUserDao {

    List<DmFcstBaseCusUserVO> getCusIdByUser(DmFcstBaseCusUserVO dmFcstBaseCusUserVO);

    int createDmFcstCusUserInfoDTO(DmFcstBaseCusUserVO dmFcstBaseCusUserVO);



}
