/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.month;

import com.huawei.it.fcst.annotations.ExportAttribute;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * The Entity of MonthYoyExpVO
 *
 * <AUTHOR>
 * @since 2023/03/15
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MonthYoyExpVO implements Serializable {
    private static final long serialVersionUID = -3527986743977395982L;

    /**
     * 年 月
     **/
    @ExportAttribute(sort = 0)
    @ApiModelProperty(value = "会计期年月")
    private Long periodId;

    /**
     * 成本类型
     **/
    @ExportAttribute(sort = 1)
    private String costType;

    /**
     * 名称
     **/
    @ExportAttribute(sort = 2)
    @ApiModelProperty(value = "名称")
    private String groupCnName;

    /**
     * 环比占比
     **/
    @ExportAttribute(sort = 3)
    @ApiModelProperty(value = "同比")
    private String yoyPercent;

    /**
     * 环比占比
     **/
    @ExportAttribute(sort = 4)
    @ApiModelProperty(value = "环比")
    private String popPercent;
}