/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.vo.annual;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * CommonAnnualVO Class
 *
 * <AUTHOR>
 * @since 2023/4/7
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "年度公共VO")
public class CommonAnnualVO {

    @ApiModelProperty(value = "数字1")
    private Double num1;

    @ApiModelProperty(value = "数字2")
    private Double num2;
}
