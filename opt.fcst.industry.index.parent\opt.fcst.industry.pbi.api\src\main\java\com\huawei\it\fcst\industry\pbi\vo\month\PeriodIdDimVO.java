/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.month;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * PeriodIdDimVO Class
 *
 * <AUTHOR>
 * @since 2024-07-28
 */
@Getter
@Setter
public class PeriodIdDimVO implements Serializable {
    private static final long serialVersionUID = -8794510668860550216L;

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * PBI目录树
     */
    private String granularityType;

    /**
     * 开始时间
     */
    private Integer startTime;

    /**
     * 结束时间
     */
    private Integer endTime;
}