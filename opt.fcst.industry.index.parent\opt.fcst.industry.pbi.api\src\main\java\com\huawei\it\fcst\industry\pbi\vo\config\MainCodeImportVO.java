/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.config;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.huawei.it.fcst.industry.pbi.annotations.ExcelValid;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * 主力编码维表导入VO实体类
 *
 * <AUTHOR>
 * @since 2024-09-26
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MainCodeImportVO implements Serializable {

    private static final long serialVersionUID = 7787100180232581463L;

    /**
     * 主键ID
     */
    @ExcelIgnore
    private Long primaryId;

    /**
     * 版本ID
     */
    @ExcelIgnore
    private Long versionId;

    /**
     * 版本名称
     */
    @ExcelIgnore
    private String versionName;

    @ExcelProperty(value = "BG编码")
    private String bgCode;

    @ExcelProperty(value = "*BG")
    @ExcelValid(message = "BG名称不能为空！")
    private String bgCnName;

    @ExcelProperty(value = "*L1")
    @ExcelValid(message = "L1名称不能为空！")
    private String lv1ProdListCnName;

    @ExcelProperty(value = "L1编码")
    private String lv1ProdListCode;

    @ExcelProperty(value = "*L2")
    @ExcelValid(message = "L2名称不能为空！")
    private String lv2ProdListCnName;

    @ExcelProperty(value = "L2编码")
    private String lv2ProdListCode;

    @ExcelProperty(value = "*L3")
    @ExcelValid(message = "L3名称不能为空！")
    private String lv3ProdListCnName;

    @ExcelProperty(value = "L3编码")
    private String lv3ProdListCode;

    @ExcelProperty(value = "*L3.5")
    @ExcelValid(message = "L3.5名称不能为空！")
    private String lv4ProdListCnName;

    @ExcelProperty(value = "L3.5编码")
    private String lv4ProdListCode;

    @ExcelProperty(value = "产品")
    private String productCode;

    @ExcelProperty(value = "*SPART编码")
    @ExcelValid(message = "SPART编码不能为空！")
    private String spartCode;

    @ExcelProperty(value = "SPART描述")
    private String spartCnName;

    @ExcelProperty(value = "*编码属性")
    @ExcelValid(message = "编码属性不能为空！")
    private String codeAttributes;

    @ExcelIgnore
    private Long createdBy;

    @ExcelIgnore
    private Long lastUpdatedBy;

    @ExcelProperty(value = "错误信息")
    private String errorMessage;

}