/*
 *
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * ExportExcelVo Class
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExportExcelVo {

    private List<AbstractExcelTitleVO> titleVoList;

    private List<AbstractExcelTitleVO> selectedLeafExcelTitleVO;

    private List<AbstractExcelTitleVO> formInfoVo;

    private List<Map> list;

    private String fileName;

    private Long userId;

    private int titleRowCount;

    private String  sheetName;

    private Boolean mergeCell;
}
