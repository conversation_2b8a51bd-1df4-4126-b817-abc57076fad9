/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.utils;

import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * ObjectCopyUtilTest Class
 *
 * <AUTHOR>
 * @since 2023/4/22
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class ObjectCopyUtilTest {

    @InjectMocks
    private ObjectCopyUtil objectCopyUtil;

    @Test
    public void copyList() {
        List<String> a =new ArrayList<>();
        a.add("111");
        a.add("222");
        List<String> list = objectCopyUtil.copyList(a, String.class);
        Assertions.assertNotNull(list);
    }

    @Test
    public void copyList2T() {
        List<String> a =new ArrayList<>();
        List<String> list = objectCopyUtil.copyList(a, String.class);
        Assertions.assertNotNull(list);
    }

    @Test
    public void copy() {
        String a ="111";
        String copy = objectCopyUtil.copy(a, String.class);
        Assertions.assertNotNull(copy);
    }

    @Test
    public void copy2T() {
        String a =null;
        String copy = objectCopyUtil.copy(a, String.class);
        Assertions.assertNull(null);
    }

}