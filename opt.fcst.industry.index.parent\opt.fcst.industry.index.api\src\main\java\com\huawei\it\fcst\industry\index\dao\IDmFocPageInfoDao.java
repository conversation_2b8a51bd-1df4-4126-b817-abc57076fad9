/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.month.DmFocPageInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * The DAO to access DmFoiPageInfoVO entity
 *
 * <AUTHOR>
 * @since 2022-10-21 10:33:21
 */
public interface IDmFocPageInfoDao {
    /**
     * Find DmFoiPageInfoVO List records.
     *
     * @return list
     */
    List<DmFocPageInfoVO> findPageInfoVOList(@Param("pageInfoVO") DmFocPageInfoVO pageInfoVO);

    /**
     * Find DmFoiPageInfoVO by id.
     *
     * @param pageId is id
     * @return DmFoiPageInfoVO
     */
    DmFocPageInfoVO findDmFocPageInfoVOById(@Param("tablePreFix") String tablePreFix,@Param("pageId") Long pageId);

    /**
     * Insert a new DmFoiPageInfoVO record.
     *
     * @param DmFoiPageInfoVO is
     * @return int
     */
    int createDmFocPageInfoVO(DmFocPageInfoVO DmFoiPageInfoVO);

    /**
     * Create batch DmFoiPageInfoVO record.
     *
     * @param items items
     * @return int
     */
    int createDmFocPageInfoVOList(List<DmFocPageInfoVO> items);

    /**
     * Update an existed  DmFoiPageInfoVO record.
     *
     * @param DmFoiPageInfoVO is
     * @return int
     */
    int updateDmFocPageInfoVO(DmFocPageInfoVO DmFoiPageInfoVO);

    /**
     * Update batch   DmFoiPageInfoVO record.
     *
     * @param items items
     * @return int
     */
    int updateDmFocPageInfoVOList(List<DmFocPageInfoVO> items);

    /**
     * Delete DmFoiPageInfoVO by id.
     *
     * @param id id
     * @return int
     */
    int deleteDmFocPageInfoVO(Long id);

    /**
     * Delete batchDmFoiPageInfoVO by id.
     *
     * @param items items
     * @return int
     */
    int deleteDmFocPageInfoVOList(List<DmFocPageInfoVO> items);
}
