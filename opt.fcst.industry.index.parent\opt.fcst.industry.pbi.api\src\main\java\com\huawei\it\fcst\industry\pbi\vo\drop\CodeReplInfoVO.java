/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.drop;

import com.huawei.it.fcst.industry.pbi.vo.common.CommonBaseVO;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/26
 */
@Setter
@Getter
@EqualsAndHashCode
@NoArgsConstructor
public class CodeReplInfoVO extends CommonBaseVO {

    /**
     * 用户id
     */
    private String userId;

    /**
     * 角色id
     */
    private String roleId;

    private String oldSpartCode;

    private List<String> oldSpartCodeList;

    private String newSpartCode;

    private List<String> newSpartCodeList;

    private String relationType;

    private String replaceRelationType;

    private String replaceRelationName;

    /**
     * 成本类型
     */
    @Size(max=100,message = "安全考虑，限制数量不能超过100")
    private List<String> costTypeList;

}
