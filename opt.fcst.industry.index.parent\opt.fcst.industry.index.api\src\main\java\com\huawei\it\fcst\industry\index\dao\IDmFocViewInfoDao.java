/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.annual.AnnualAnalysisVO;
import com.huawei.it.fcst.industry.index.vo.common.CommonViewVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;

import java.util.List;

/**
 * IDmFocViewInfoDao
 *
 * <AUTHOR>
 * @since 2023/3/14
 */
public interface IDmFocViewInfoDao {

    List<DmFocViewInfoVO> reverseFindLv1ProdCode(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> reverseFindLv1ProdCodeMonth(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> viewInfoList(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> viewInfoListForMonth(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> viewInfoListForTopCate(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> revViewInfoList(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> revViewInfoListForMonth(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> revViewInfoListForTopCate(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> viewFlagInfoList(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> viewInfoKeyWordList(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> viewInfoKeyWordForMonth(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> viewInfoKeyWordForTopCate(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> getLv0ProdList(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> getCombinationSubByGroupLevel(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> getCombinationByGroupLevel(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> getCombinationParent(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> getAllCombItemCode(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocViewInfoVO> getAllNormalItemCode(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocViewInfoVO> getAllItemCode(AnnualAnalysisVO annualAnalysisVO);

}
