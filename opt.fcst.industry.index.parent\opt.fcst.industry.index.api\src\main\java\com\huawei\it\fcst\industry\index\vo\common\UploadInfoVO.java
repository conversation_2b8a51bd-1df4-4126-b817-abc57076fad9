/*
 *
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.industry.index.vo.common;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Map;

/**
 * UploadInfoVO Class
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
@Getter
@Setter
@NoArgsConstructor
public class UploadInfoVO implements Serializable {
    private static final long serialVersionUID = -5394978246348938140L;

    private String fileName;

    private String sheetName;

    private long fileSize;

    private Map<String, Object> params;

    private int rowNumber;

    private String version;

    private Long userId;

    private Timestamp creationDate;

    private DmFoiImpExpRecordVO dmFoiImpExpRecord;

}
