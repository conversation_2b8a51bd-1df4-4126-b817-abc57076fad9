/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.config;

import com.google.common.collect.Lists;
import com.huawei.it.fcst.enums.ResultCodeEnum;
import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstVersionInfoDao;
import com.huawei.it.fcst.industry.pbi.dao.IctProdMainCodeDimDao;
import com.huawei.it.fcst.industry.pbi.impl.template.MainCodeTemplateEnum;
import com.huawei.it.fcst.industry.pbi.service.common.IIctCommonService;
import com.huawei.it.fcst.industry.pbi.service.config.IIctConfigManagementService;
import com.huawei.it.fcst.export.IExportProcessorService;
import com.huawei.it.fcst.industry.pbi.utils.FcstIndustryUtil;
import com.huawei.it.fcst.industry.pbi.vo.common.DmFcstDataRefreshStatus;
import com.huawei.it.fcst.industry.pbi.vo.config.DmFcstIctProdMainCodeDimVO;
import com.huawei.it.fcst.industry.pbi.vo.config.IctProdMainCodeDimVO;
import com.huawei.it.fcst.industry.pbi.vo.config.ImportContextVO;
import com.huawei.it.fcst.industry.pbi.vo.version.DmFcstVersionInfoVO;
import com.huawei.it.fcst.util.ExcelUtils;
import com.huawei.it.fcst.util.UserInfoUtils;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.fcst.vo.UploadInfoVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;

import javax.inject.Inject;
import javax.inject.Named;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * IctConfigManagementService Class
 *
 * <AUTHOR>
 * @since 2024-07-10
 */
@Slf4j
@Named("ictConfigManagementService")
@JalorResource(code = "IctConfigManagementService", desc = "产业成本指数（ICT）配置管理服务")
public class IctConfigManagementService implements IIctConfigManagementService {

    @Inject
    private IctProdMainCodeDimDao prodMainCodeDimDao;

    @Inject
    private IDmFcstVersionInfoDao versionInfoDao;

    @Inject
    private IIctCommonService ictCommonService;

    @Inject
    private IExportProcessorService exportProcessorService;

    @Inject
    private AsyncIctConfigService asyncIctConfigService;

    @Inject
    private ExcelUtils excelUtils;

    @Override
    @JalorOperation(code = "getVersionList", desc = "查询版本列表信息")
    public ResultDataVO getVersionList(DmFcstVersionInfoVO versionInfoVO) throws ApplicationException {
        log.info("==>Begin IctConfigManagementService#getVersionList");
        return ResultDataVO.success(versionInfoDao.findVersionList(versionInfoVO));
    }

    @Override
    @JalorOperation(code = "getMainCodeDropboxList", desc = "查询主力编码-各层级下拉框列表")
    public ResultDataVO getMainCodeDropboxList(IctProdMainCodeDimVO prodMainCodeDimVO) throws ApplicationException {
        log.info("==>Begin IctConfigManagementService#getMainCodeDropboxList");
        if (ObjectUtils.isEmpty(prodMainCodeDimVO.getVersionId())) {
            throw new CommonApplicationException("版本ID不能为空！");
        }
        return ResultDataVO.success(prodMainCodeDimDao.findMainCodeDropboxList(prodMainCodeDimVO));
    }

    @Override
    @JalorOperation(code = "getMainCodeEditDropboxList", desc = "查询主力编码-新增/编辑时的各层级下拉框列表")
    public ResultDataVO getMainCodeEditDropboxList(IctProdMainCodeDimVO prodMainCodeDimVO) throws ApplicationException {
        log.info("==>Begin IctConfigManagementService#getMainCodeEditDropboxList");
        if (StringUtils.isBlank(prodMainCodeDimVO.getGroupLevel())) {
            throw new CommonApplicationException("groupLevel字段不能为空！");
        }
        return ResultDataVO.success(prodMainCodeDimDao.findMainCodeEditDropboxList(prodMainCodeDimVO));
    }

    @Override
    @JalorOperation(code = "getMainCodeSpartDropboxList", desc = "分页查询主力编码-新增/编辑时SPART层级下拉框列表")
    public ResultDataVO getMainCodeSpartDropboxList(IctProdMainCodeDimVO prodMainCodeDimVO) throws ApplicationException {
        log.info("==>Begin IctConfigManagementService#getMainCodeSpartDropboxList");
        PageVO pageVO = new PageVO();
        pageVO.setCurPage(prodMainCodeDimVO.getCurPage());
        pageVO.setPageSize(prodMainCodeDimVO.getPageSize());
        return ResultDataVO.success(prodMainCodeDimDao.findMainCodeSpartDropboxList(prodMainCodeDimVO, pageVO));
    }

    @Override
    @JalorOperation(code = "getProdMainCodeDimVOList", desc = "分页查询主力产品主力编码列表")
    public ResultDataVO getProdMainCodeDimVOList(IctProdMainCodeDimVO prodMainCodeDimVO) throws ApplicationException {
        log.info("==>Begin IctConfigManagementService#getProdMainCodeDimVOList");
        if (ObjectUtils.isEmpty(prodMainCodeDimVO.getVersionId())) {
            throw new CommonApplicationException("版本ID不能为空！");
        }
        PageVO pageVO = new PageVO();
        pageVO.setCurPage(prodMainCodeDimVO.getCurPage());
        pageVO.setPageSize(prodMainCodeDimVO.getPageSize());
        return ResultDataVO.success(prodMainCodeDimDao.findMainCodeDimListByPage(prodMainCodeDimVO, pageVO));
    }

    private List<IctProdMainCodeDimVO> insertProdMainCodeDimVOList(DmFcstIctProdMainCodeDimVO ictProdMainCodeDimVO,
            List<IctProdMainCodeDimVO> currentVersionDataList) throws ApplicationException {
        log.info("==>Begin IctConfigManagementService#insertProdMainCodeDimVOList");
        // 1、校验必填项 新增主力编码时系统管理员必填信息包括：BG名称、L1名称、L2名称、L3名称、L3.5名称、SPART编码
        List<IctProdMainCodeDimVO> insertMainCodeDimVOList = ictProdMainCodeDimVO.getInsertMainCodeDimVOList();
        checkNullValue(insertMainCodeDimVOList);
        // 2、校验重复数据
        Map<String, List<IctProdMainCodeDimVO>> listMap = checkDuplicateData(insertMainCodeDimVOList, currentVersionDataList);
        // 将新增的主力编码添加到当前版本集合中去
        listMap.values().stream().forEach(value -> currentVersionDataList.addAll(value));
        return currentVersionDataList;
    }

    private void batchInsertMainCodeVOs(List<IctProdMainCodeDimVO> currentVersionDataList) throws ApplicationException {
        // 创建版本信息
        Long newVersionId = ictCommonService.createNewVersionInfo("MAIN_DIM");
        if (CollectionUtils.isEmpty(currentVersionDataList)) {
            return;
        }
        // 关联上最新的版本ID
        currentVersionDataList.stream().forEach(item -> {
            item.setVersionId(newVersionId);
            item.setCreatedBy(UserInfoUtils.getUserId());
            item.setLastUpdatedBy(UserInfoUtils.getUserId());
        });
        // 数据量小于1000条直接插入，否则分批插入
        if (currentVersionDataList.size() <= 1000) {
            prodMainCodeDimDao.batchInsertMainCodeDimVOs(currentVersionDataList);
        } else {
            Lists.partition(currentVersionDataList, 1000).stream()
                    .forEach(voList -> prodMainCodeDimDao.batchInsertMainCodeDimVOs(voList));
        }
    }

    private void callFuncMainCodeDimMapping() throws CommonApplicationException {
        callFuncMainCodeDimMapping("PSP");
        callFuncMainCodeDimMapping("STD");
    }

    private void callFuncMainCodeDimMapping(String costType) throws CommonApplicationException {
        String result = prodMainCodeDimDao.callFuncMainCodeDimMapping(costType);
        log.info(">>>Invoke Function：f_dm_fcst_main_code_dim_mapping, the {} result: {}", costType, result);
        if (!"SUCCESS".equals(result) && !"1".equals(result)) {
            throw new CommonApplicationException("调用[f_dm_fcst_main_code_dim_mapping]函数失败");
        }
    }
    
    private String getGroupKey(IctProdMainCodeDimVO prodMainCodeDimVO) {
        return StringUtils.defaultIfBlank(prodMainCodeDimVO.getBgCnName(), "").concat("#")
                .concat(StringUtils.defaultIfBlank(prodMainCodeDimVO.getLv1ProdListCnName(), "")).concat("#")
                .concat(StringUtils.defaultIfBlank(prodMainCodeDimVO.getLv2ProdListCnName(), "")).concat("#")
                .concat(StringUtils.defaultIfBlank(prodMainCodeDimVO.getLv3ProdListCnName(), "")).concat("#")
                .concat(StringUtils.defaultIfBlank(prodMainCodeDimVO.getLv4ProdListCnName(), "")).concat("#")
                .concat(StringUtils.defaultIfBlank(prodMainCodeDimVO.getSpartCode(), ""));
    }

    /**
     * 重复数据校验
     *
     * @param mainCodeDimVOList 入参集合
     * @param currentVersionDataList 历史版本数据
     * @return List Map 返回去重后的待保存数据
     * @throws CommonApplicationException
     */
    @NotNull
    private Map<String, List<IctProdMainCodeDimVO>> checkDuplicateData(List<IctProdMainCodeDimVO> mainCodeDimVOList,
            List<IctProdMainCodeDimVO> currentVersionDataList) throws CommonApplicationException {
        // 1.校验提交的数据本身是否有重复数据
        Map<String, List<IctProdMainCodeDimVO>> saveListMap = mainCodeDimVOList.stream()
                .collect(Collectors.groupingBy(item -> getGroupKey(item)));
        List<String> duplicateDataList = saveListMap.keySet().stream()
                .filter(key -> saveListMap.get(key).size() > 1)
                .distinct()
                .collect(Collectors.toList());
        if (duplicateDataList.size() > 0) {
            throw new CommonApplicationException("保存的数据有重复，请检查:".concat(duplicateDataList.toString()));
        }
        // 2.校验保存的数据与历史版本数据是否重复,并删除重复的提交数据
        StringBuilder duplicateDataBuilder = new StringBuilder();
        Map<String, List<IctProdMainCodeDimVO>> historyListMap = currentVersionDataList.stream()
                .collect(Collectors.groupingBy(item -> getGroupKey(item)));
        historyListMap.keySet().forEach(key -> {
            if (saveListMap.containsKey(key)) {
                // 删除重复数据
                saveListMap.remove(key);
                duplicateDataBuilder.append(key).append(";");
            }
        });
        if (duplicateDataBuilder.length() > 0) {
            throw new CommonApplicationException("保存的数据与历史版本数据有重复，请检查:".concat(duplicateDataBuilder.toString()));
        }
        return saveListMap;
    }

    private List<IctProdMainCodeDimVO> getMainCodeDimVOList(Long versionId) {
        // 查询当前版本数据
        return prodMainCodeDimDao.findMainCodeDimVOList(IctProdMainCodeDimVO.builder().versionId(versionId).build());
    }

    private void checkNullValue(List<IctProdMainCodeDimVO> mainCodeDimVOList) throws CommonApplicationException {
        StringBuilder nullValueBuilder = new StringBuilder();
        mainCodeDimVOList.stream().forEach(item -> {
            if (StringUtils.isBlank(item.getBgCnName())) {
                nullValueBuilder.append("BG名称不能为空！");
            }
            if (StringUtils.isBlank(item.getLv1ProdListCnName())) {
                nullValueBuilder.append("L1名称不能为空！");
            }
            if (StringUtils.isBlank(item.getLv2ProdListCnName())) {
                nullValueBuilder.append("L2名称不能为空！");
            }
            if (StringUtils.isBlank(item.getLv3ProdListCnName())) {
                nullValueBuilder.append("L3名称不能为空！");
            }
            if (StringUtils.isBlank(item.getLv4ProdListCnName())) {
                nullValueBuilder.append("L3.5名称不能为空！");
            }
            if (StringUtils.isBlank(item.getSpartCode())) {
                nullValueBuilder.append("SPART编码名称不能为空！");
            }
            if (StringUtils.isBlank(item.getCodeAttributes())) {
                nullValueBuilder.append("编码属性不能为空！");
            }
        });
        if (nullValueBuilder.length() > 0) {
            throw new CommonApplicationException(nullValueBuilder.toString());
        }
    }

    @Override
    @JalorOperation(code = "updateProdMainCodeDimVOList", desc = "批量新增/编辑主力产品主力编码记录")
    @Audit(module = "ictConfigManagementService-updateProdMainCodeDimVOList", operation = "updateProdMainCodeDimVOList", message = "批量新增/编辑主力产品主力编码记录")
    public ResultDataVO updateProdMainCodeDimVOList(DmFcstIctProdMainCodeDimVO ictProdMainCodeDimVO) throws ApplicationException {
        log.info("==>Begin IctConfigManagementService#updateProdMainCodeDimVOList");
        if (ObjectUtils.isEmpty(ictProdMainCodeDimVO.getVersionId())) {
            throw new CommonApplicationException("版本ID不能为空！");
        }
        List<IctProdMainCodeDimVO> insertMainCodeDimVOList = ictProdMainCodeDimVO.getInsertMainCodeDimVOList();
        List<IctProdMainCodeDimVO> editMainCodeDimVOList = ictProdMainCodeDimVO.getEditMainCodeDimVOList();
        if (CollectionUtils.isEmpty(insertMainCodeDimVOList) && CollectionUtils.isEmpty(editMainCodeDimVOList)) {
            throw new CommonApplicationException("insertMainCodeDimVOList和editMainCodeDimVOList集合不能同时为空！");
        }
        // 入参数据量控制
        if (insertMainCodeDimVOList.size() > 100 || editMainCodeDimVOList.size() > 100) {
            return ResultDataVO.failure(ResultCodeEnum.BIG_DATA);
        }
        // 查询当前版本的数据
        List<IctProdMainCodeDimVO> newMainCodeList = new ArrayList<>();
        List<IctProdMainCodeDimVO> currentVersionDataList = getMainCodeDimVOList(ictProdMainCodeDimVO.getVersionId());
        if (CollectionUtils.isNotEmpty(insertMainCodeDimVOList)) {
            newMainCodeList = insertProdMainCodeDimVOList(ictProdMainCodeDimVO, currentVersionDataList);
        }
        if (CollectionUtils.isNotEmpty(editMainCodeDimVOList)) {
            newMainCodeList = updateMainCodeDimVOS(editMainCodeDimVOList, currentVersionDataList);
        }
        // 批量保存入库
        batchInsertMainCodeVOs(newMainCodeList);
        return ResultDataVO.success();
    }

    private List<IctProdMainCodeDimVO> updateMainCodeDimVOS(List<IctProdMainCodeDimVO> editMainCodeDimVOList,
            List<IctProdMainCodeDimVO> currentVersionDataList) throws CommonApplicationException {
        // 1、校验必填项
        checkNullValue(editMainCodeDimVOList);
        // 2、校验重复数据
        Map<String, List<IctProdMainCodeDimVO>> saveListMap = checkDuplicateData(editMainCodeDimVOList, currentVersionDataList);
        // 待保存的主力编码记录ID集合
        Set<Long> primaryIdList = new HashSet<>();
        saveListMap.entrySet().forEach(entry -> {
            List<Long> primaryIds = entry.getValue().stream()
                    .map(IctProdMainCodeDimVO::getPrimaryId).collect(Collectors.toList());
            primaryIdList.addAll(primaryIds);
        });
        // 剔除被编辑了的主力编码记录
        currentVersionDataList = currentVersionDataList.stream()
                .filter(item -> (item.getPrimaryId() != null && !primaryIdList.contains(item.getPrimaryId())))
                .collect(Collectors.toList());
        // 将编辑后的主力编码添加到当前版本集合中去
        for (List<IctProdMainCodeDimVO> mainCodeDimVOList : saveListMap.values()) {
            currentVersionDataList.addAll(mainCodeDimVOList);
        }
        return currentVersionDataList;
    }

    @Override
    @JalorOperation(code = "deleteProdMainCodeDimVOList", desc = "批量删除主力产品主力编码记录")
    @Audit(module = "ictConfigManagementService-deleteProdMainCodeDimVOList", operation = "deleteProdMainCodeDimVOList", message = "批量删除主力产品主力编码记录")
    public ResultDataVO deleteProdMainCodeDimVOList(DmFcstIctProdMainCodeDimVO ictProdMainCodeDimVO) throws ApplicationException {
        log.info("==>Begin IctConfigManagementService#updateProdMainCodeDimVOList");
        // 入参数据量控制
        List<Long> primaryIdList = ictProdMainCodeDimVO.getPrimaryIdList();
        if (primaryIdList.size() > 100) {
            return ResultDataVO.failure(ResultCodeEnum.BIG_DATA);
        }
        // 查询当前版本的数据
        List<IctProdMainCodeDimVO> currentVerDataList = getMainCodeDimVOList(ictProdMainCodeDimVO.getVersionId());
        // 从当前版本中剔除被删除了的主力编码记录
        currentVerDataList = currentVerDataList.stream()
                .filter(item -> !primaryIdList.contains(item.getPrimaryId()))
                .collect(Collectors.toList());
        // 批量保存入库
        batchInsertMainCodeVOs(currentVerDataList);
        return ResultDataVO.success();
    }

    @Override
    @JalorOperation(code = "exportProdMainCodeDimVOList", desc = "导出主力产品主力编码记录")
    @Audit(module = "ictConfigManagementService-exportProdMainCodeDimVOList", operation = "exportProdMainCodeDimVOList", message = "导出主力产品主力编码记录")
    public ResultDataVO exportProdMainCodeDimVOList(IctProdMainCodeDimVO prodMainCodeDimVO, HttpServletResponse response) throws ApplicationException {
        log.info("==>Begin IctConfigManagementService#exportProdMainCodeDimVOList");
        // 设置版本名称
        prodMainCodeDimVO.setVersionName(versionInfoDao.findDmFocVersionDTOById(prodMainCodeDimVO.getVersionId()).getVersion());
        // 获取导出模板
        MainCodeTemplateEnum mainCodeTemplateEnum = MainCodeTemplateEnum.MAIN_CODE_01;
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("exportFileName", "配置管理-主力产品主力编码_" + System.currentTimeMillis());
        paramMap.put("exportModuleName", "成本指数-ICT-主力产品主力编码");
        exportProcessorService.fillEasyExcelExport(response, mainCodeTemplateEnum, prodMainCodeDimVO, paramMap);
        return ResultDataVO.success();
    }

    @Override
    @JalorOperation(code = "importProdMainCodeDimVOList", desc = "导入主力产品主力编码记录")
    @Audit(module = "ictConfigManagementService-importProdMainCodeDimVOList", operation = "importProdMainCodeDimVOList", message = "导入主力产品主力编码记录")
    public ResultDataVO importProdMainCodeDimVOList(Attachment attachment, Long versionId) throws Exception {
        log.info(">>>Begin ctConfigManagementService::importProdMainCodeDimVOList");
        if (ObjectUtils.isEmpty(versionId)) {
            throw new CommonApplicationException("版本ID不能为空！");
        }
        // 校验导入的Excel文件格式和文件大小
        excelUtils.verifyExcelFile(attachment, CommonConstant.MAIN_CODE_TYPE);
        // 保存请求上下文信息
        IRequestContext context = RequestContextManager.getCurrent();
        // 设置任务刷新状态
        DmFcstDataRefreshStatus dataRefreshStatus = ictCommonService.saveDataRefreshStatus("MAIN_DIM_IMPORT");
        // 获取模块类型和导入数量限制
        Map<String, Object> params = ictCommonService.getHeaderMap(CommonConstant.MAIN_CODE_TYPE);
        // 导入信息记录对象VO
        UploadInfoVO uploadInfoVO = FcstIndustryUtil.getUploadInfoVO(attachment, versionId, params, CommonConstant.MAIN_CODE_TYPE);
        ImportContextVO importContextVO = ImportContextVO.builder().versionId(versionId).context(context)
                .userId(UserInfoUtils.getUserId()).userCn(UserInfoUtils.getUserCn())
                .dataRefreshStatus(dataRefreshStatus)
                .uploadInfoVO(uploadInfoVO).build();
        asyncIctConfigService.importMainCodeDimVOList(attachment, importContextVO);
        return ResultDataVO.success(dataRefreshStatus);
    }

}