/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2018-2019. All rights reserved.
 */

package com.huawei.it.fcst.constant;

import lombok.Getter;

/**
 * 常量类.
 *
 * <AUTHOR>
 * @since 2023/03/10
 */
public class Constant {

    /**
     * 字符串常量
     */
    public enum StrEnum {
        MAIN_FLAG("Y"),
        NUMBER("Number"),
        QUERY_EXPLAIN_CHART_URL("App.Explain.queryShapChartUrl"),
        COMPARE_FLAG("产业-多选主体"),
        DIMENSION_CODE("getDimensionWithTree"),
        PROD_DIMENSION_CODE("getProdDimensionWithTree"),
        PRICE_DIMENSION_CODE("getPriceDimensionWithTree"),
        LOCATION_DIMENSION_CODE("getLocationWithTree"),
        PRICE_LOCATION_DIMENSION_CODE("getPriceLocationWithTree"),
        PRICE_KEY_SUBACCOUNT("getKeyAndSubAccountWithTree"),
        COST_TYPE_CODE("getCostType"),
        PSP_COST_TYPE_CODE("getPspCostType");
        private String value;

        StrEnum(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    public enum IntegerEnum {
        PAGE_SIZE(10000),
        MAX_SIZE(5000),
        ONE_HUNDRED(100);
        private int value;

        IntegerEnum(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    // 任务状态
    public enum TaskStatus {
        INIT("初始化", "TASK_INIT"),
        TASK_SUCCESS("任务成功", "TASK_SUCCESS"),
        TASK_FAIL("任务失败", "TASK_FAIL");

        @Getter
        private String desc;

        @Getter
        private String value;

        TaskStatus(String desc, String value) {
            this.desc = desc;
            this.value = value;
        }
    }

}