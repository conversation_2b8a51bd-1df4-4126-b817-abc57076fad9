/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2022/6/7
 */
public interface IndustryConstEnum {
    enum COST_TYPE {
        PSP("PSP", "PSP成本"),
        STD("STD", "标准成本"),
        PREDICT("PREDICT", "预估成本");
        @Getter
        @Setter
        private String value;
        @Getter
        @Setter
        private String desc;
        COST_TYPE(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    enum DATA_TYPE {
        MAIN_DIM("MAIN_DIM", "MAIN_DIM"),
        RED_DIM("RED_DIM", "RED_DIM"),
        REPLACE_DIM("REPLACE_DIM", "REPLACE_DIM"),
        DATA_REVIEW("DATA_REVIEW", "DATA_REVIEW");
        @Getter
        @Setter
        private String value;
        @Getter
        @Setter
        private String desc;
        DATA_TYPE(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    enum GRANULARITY_TYPE {
        IRB("IRB", "重量级团队目录"),
        INDUS("INDUS", "产业目录"),
        PROD("PROD", "销售目录");
        @Getter
        @Setter
        private String value;
        @Getter
        @Setter
        private String desc;
        GRANULARITY_TYPE(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    enum PAGE_TYPE {
        ANNUAL("ANNUAL", "年度分析页面"),
        MONTH("MONTH", "月度分析页面"),
        REPLACE_DIM("REPLACE_DIM", "编码替换页面"),
        CONFIG("CONFIG", "配置管理页面"),
        MAIN_DIM("MAIN_DIM", "主力编码");
        @Getter
        @Setter
        private String value;
        @Getter
        @Setter
        private String desc;
        PAGE_TYPE(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    enum VIEW_FLAG {
        PROD_SPART("PROD_SPART", "路径一"),
        DIMENSION("DIMENSION", "路径二");
        @Getter
        @Setter
        private String value;
        @Getter
        @Setter
        private String desc;

        VIEW_FLAG(String value, String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    enum SOFTWARE_MARK {
        HARDWARE("HARDWARE", "硬件"),
        SOFTWARE("SOFTWARE", "软件"),
        OTHERS("OTHERS", "其他"),
        ALL("ALL", "全选");
        @Getter
        @Setter
        private String value;
        @Getter
        @Setter
        private String desc;
        SOFTWARE_MARK(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    enum MAIN_FLAG {
        Y("Y", "是"),
        N("N", "否");
        @Getter
        @Setter
        private String value;
        @Getter
        @Setter
        private String desc;
        MAIN_FLAG(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    enum YTD_FLAG {
        Y("Y", "年度YTD"),
        N("N", "年度");
        @Getter
        @Setter
        private String value;
        @Getter
        @Setter
        private String desc;
        YTD_FLAG(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    static YTD_FLAG getYtdFlag(String key){
        for (YTD_FLAG ytdFlag : YTD_FLAG.values()) {
            if (ytdFlag.getValue().equalsIgnoreCase(key)) {
                return ytdFlag;
            }
        }
        return null;
    }

    static GRANULARITY_TYPE getGranularityType(String key){
        for (GRANULARITY_TYPE granularityType : GRANULARITY_TYPE.values()) {
            if (granularityType.getValue().equalsIgnoreCase(key)) {
                return granularityType;
            }
        }
        return null;
    }

    static COST_TYPE getCostType(String type){
        for (COST_TYPE costType : COST_TYPE.values()) {
            if (costType.getValue().equalsIgnoreCase(type)) {
                return costType;
            }
        }
        return null;
    }


    static String getCostTypeValue(String key){
        for (COST_TYPE costType : COST_TYPE.values()) {
            if (costType.getDesc().equalsIgnoreCase(key)) {
                return costType.getValue();
            }
        }
        return null;
    }

    static String getGranularityTypeValue(String key){
        for (GRANULARITY_TYPE granule : GRANULARITY_TYPE.values()) {
            if (granule.getDesc().equalsIgnoreCase(key)) {
                return granule.getValue();
            }
        }
        return null;
    }

    static SOFTWARE_MARK getSoftwareMark(String key){
        for (SOFTWARE_MARK softwareMark : SOFTWARE_MARK.values()) {
            if (softwareMark.getValue().equalsIgnoreCase(key)) {
                return softwareMark;
            }
        }
        return null;
    }

    static MAIN_FLAG getMainFlag(String key){
        for (MAIN_FLAG mainFlag : MAIN_FLAG.values()) {
            if (mainFlag.getValue().equalsIgnoreCase(key)) {
                return mainFlag;
            }
        }
        return null;
    }
}
