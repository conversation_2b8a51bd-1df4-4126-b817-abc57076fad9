/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.config;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.collect.Lists;
import com.huawei.it.fcst.industry.pbi.dao.IDmfcstIctCodeReplInfoDao;
import com.huawei.it.fcst.industry.pbi.dao.IctProdMainCodeDimDao;
import com.huawei.it.fcst.industry.pbi.vo.config.IctProdMainCodeDimVO;
import com.huawei.it.fcst.industry.pbi.vo.config.ImportContextVO;
import com.huawei.it.fcst.industry.pbi.vo.config.MainCodeImportVO;
import com.huawei.it.fcst.industry.pbi.vo.replace.DmFocReplVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 主力编码维表导入监听器
 *
 * <AUTHOR>
 * @since 2024-09-26
 */
@Slf4j
public class MainCodeImportListener implements ReadListener<MainCodeImportVO> {

    private IctProdMainCodeDimDao prodMainCodeDimDao;

    private IDmfcstIctCodeReplInfoDao dmfcstIctCodeReplInfoDao;

    private ImportContextVO importContextVO;

    private Set<String> checkDuplicateData = new HashSet<>(20000);

    // L1层级映射
    private Map<String, List<IctProdMainCodeDimVO>> lv1Map = new HashMap<>();

    // L2层级映射
    private Map<String, List<IctProdMainCodeDimVO>>  lv2Map = new HashMap<>();

    // L3层级映射
    private Map<String, List<IctProdMainCodeDimVO>>  lv3Map = new HashMap<>();

    // L4层级映射
    private Map<String, List<IctProdMainCodeDimVO>>  lv4Map = new HashMap<>();

    // 设置数据集合
    private ThreadLocal<List<MainCodeImportVO>> currentDataList = ThreadLocal.withInitial(ArrayList::new);

    private ThreadLocal<StringBuilder> errorTips = ThreadLocal.withInitial(StringBuilder::new);

    public MainCodeImportListener(ImportContextVO importContextVO, IctProdMainCodeDimDao prodMainCodeDimDao,IDmfcstIctCodeReplInfoDao dmfcstIctCodeReplInfoDao) {
        this.importContextVO = importContextVO;
        this.prodMainCodeDimDao = prodMainCodeDimDao;
        this.dmfcstIctCodeReplInfoDao = dmfcstIctCodeReplInfoDao;
    }

    /**
     * 导入写入方法
     *
     * @param dataVO
     * @param analysisContext
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void invoke(MainCodeImportVO dataVO, AnalysisContext analysisContext) {
        currentDataList.get().add(dataVO);
        if (currentDataList.get().size() >= 10000) {
            dataProcess();
        }
    }

    // 数据集合处理
    private void dataProcess() {
        List<MainCodeImportVO> dataList = currentDataList.get();
        // 校验导入的数据
        String errorMsg = checkImportData(dataList);
        // 不存在问题数据，直接保存入库
        if (StringUtils.isBlank(errorMsg)) {
            saveBatchData(dataList);
        } else {
            // 存在有问题的数据
            errorTips.get().append(errorMsg);
            String errorMessage = importContextVO.getErrorMsg();
            if (StringUtils.isBlank(errorMessage)) {
                importContextVO.setErrorMsg(errorTips.get().toString());
            } else {
                importContextVO.setErrorMsg(errorMessage.concat(errorTips.get().toString()));
            }
        }
        // 所有数据必须写入文件，并在个人中心里体现
        ExcelWriter excelWriter = importContextVO.getWorkbookWriter();
        WriteSheet writeSheet = importContextVO.getWriteSheet();
        excelWriter.write(dataList, writeSheet);
        int batchNum = importContextVO.getBatchNum();
        importContextVO.setBatchNum(batchNum++);
        importContextVO.setTotalNum(importContextVO.getTotalNum() + dataList.size());
        dataList.clear();
        currentDataList.remove();
        errorTips.remove();
    }



    //  批量插入数据
    private void saveBatchData(List<MainCodeImportVO> importDataList) {
        // 转换成需要保存的VO
        List<IctProdMainCodeDimVO> saveDataList = new ArrayList<>();
        importDataList.stream().forEach(mainCodeImportVO -> {
            mainCodeImportVO.setVersionId(importContextVO.getNewVersionId());
            mainCodeImportVO.setCreatedBy(importContextVO.getUserId());
            mainCodeImportVO.setLastUpdatedBy(importContextVO.getUserId());
            IctProdMainCodeDimVO mainCodeDimVO = IctProdMainCodeDimVO.builder().build();
            BeanUtils.copyProperties(mainCodeImportVO, mainCodeDimVO);
            saveDataList.add(mainCodeDimVO);
        });
        // 数据量小于1000条直接插入，否则分批插入
        if (saveDataList.size() <= 1000) {
            prodMainCodeDimDao.batchInsertMainCodeDimVOs(saveDataList);
            return;
        }
        Lists.partition(saveDataList, 1000).stream()
                .forEach(voList -> prodMainCodeDimDao.batchInsertMainCodeDimVOs(voList));
    }

    // 处理完后
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        // 处理尾部数据，不足1w的数据
        if (!currentDataList.get().isEmpty()) {
            dataProcess();
        }
        checkDuplicateData.clear();
        lv1Map.clear();
        lv2Map.clear();
        lv3Map.clear();
        lv4Map.clear();
        log.info(">>>>>MainCodeImportListener Import Completed!<<<<<");
    }

    private String checkImportData(List<MainCodeImportVO> mainCodeDataList) {
        // 记录行数
        AtomicInteger lineCount = new AtomicInteger(1);
        StringBuilder builder = new StringBuilder();
        mainCodeDataList.stream().forEach(data -> {
            lineCount.addAndGet(1);
            checkOneData(data);
            if (StringUtils.isNotEmpty(data.getErrorMessage())) {
                builder.append("第" + (importContextVO.getBatchNum() * 10000 + lineCount.get()) + "行存在"
                        + StringUtils.defaultString(data.getErrorMessage()));
            }
        });
        return builder.toString();
    }

    private void checkOneData(MainCodeImportVO mainCodeImportVO) {
        StringBuilder builder = new StringBuilder();
        // 校验为空字段
        validateEmptyField(mainCodeImportVO, builder);
        // 填充BG/L1-L3.5编码信息
        setBgCode(mainCodeImportVO, builder);
        // 校验重复数据
        validateDuplicateData(mainCodeImportVO, builder);
        if (builder.length() > 0) {
            mainCodeImportVO.setErrorMessage(builder.toString());
        }
    }

    private void setBgCode(MainCodeImportVO mainCodeImportVO, StringBuilder builder) {
        List<DmFocReplVO> bgCodeFromProductDim = dmfcstIctCodeReplInfoDao.findBgCodeFromProductDim();
        List<String> bgCodeCollect = bgCodeFromProductDim.stream().map(item -> item.getBgCode()).collect(Collectors.toList());
        List<String> bgCnNameCollect = bgCodeFromProductDim.stream().map(item -> item.getBgCnName()).collect(Collectors.toList());
        if (!bgCnNameCollect.contains(mainCodeImportVO.getBgCnName())) {
            builder.append("BG名称不正确;");
        } else {
            DmFocReplVO dmFocReplVO = bgCodeFromProductDim.stream().filter(item -> mainCodeImportVO.getBgCnName().equals(item.getBgCnName())).findFirst().orElse(new DmFocReplVO());
            mainCodeImportVO.setBgCode(dmFocReplVO.getBgCode());
            IctProdMainCodeDimVO mainCodeDimVO = IctProdMainCodeDimVO.builder().bgCodeList(bgCodeCollect).build();
            if (lv1Map.isEmpty()) {
                mainCodeDimVO.setGroupLevel("LV1");
                List<IctProdMainCodeDimVO> lv1DataList = prodMainCodeDimDao.findMainCodeEditDropboxList(mainCodeDimVO);
                lv1Map = lv1DataList.stream().collect(
                        Collectors.groupingBy(item -> item.getBgCode(), HashMap::new, Collectors.toList()));
            }
            if (lv2Map.isEmpty()) {
                mainCodeDimVO.setGroupLevel("LV2");
                List<IctProdMainCodeDimVO> lv2DataList = prodMainCodeDimDao.findMainCodeEditDropboxList(mainCodeDimVO);
                lv2Map = lv2DataList.stream().collect(
                        Collectors.groupingBy(item -> item.getBgCode(), HashMap::new, Collectors.toList()));
            }
            if (lv3Map.isEmpty()) {
                mainCodeDimVO.setGroupLevel("LV3");
                List<IctProdMainCodeDimVO> lv3DataList = prodMainCodeDimDao.findMainCodeEditDropboxList(mainCodeDimVO);
                lv3Map = lv3DataList.stream().collect(
                        Collectors.groupingBy(item -> item.getBgCode(), HashMap::new, Collectors.toList()));
            }
            if (lv4Map.isEmpty()) {
                mainCodeDimVO.setGroupLevel("LV4");
                List<IctProdMainCodeDimVO> lv4DataList = prodMainCodeDimDao.findMainCodeEditDropboxList(mainCodeDimVO);
                lv4Map = lv4DataList.stream().collect(
                        Collectors.groupingBy(item -> item.getBgCode(), HashMap::new, Collectors.toList()));
            }
            List<IctProdMainCodeDimVO> lv1MapByBgCode = lv1Map.get(mainCodeImportVO.getBgCode());
            Map<String, String> lv1LevelMap = lv1MapByBgCode.stream().collect(Collectors.toMap(IctProdMainCodeDimVO::getLv1ProdListCnName, IctProdMainCodeDimVO::getLv1ProdListCode, (v1, v2) -> v1));
            lv1LevelMap.put("ALL", "ALL");
            List<IctProdMainCodeDimVO> lv2MapByBgCode = lv2Map.get(mainCodeImportVO.getBgCode());
            Map<String, String> lv2LevelMap = lv2MapByBgCode.stream().collect(Collectors.toMap(IctProdMainCodeDimVO::getLv2ProdListCnName, IctProdMainCodeDimVO::getLv2ProdListCode, (v1, v2) -> v1));
            lv2LevelMap.put("ALL", "ALL");
            List<IctProdMainCodeDimVO> lv3MapByBgCode = lv3Map.get(mainCodeImportVO.getBgCode());
            Map<String, String> lv3LevelMap = lv3MapByBgCode.stream().collect(Collectors.toMap(IctProdMainCodeDimVO::getLv3ProdListCnName, IctProdMainCodeDimVO::getLv3ProdListCode, (v1, v2) -> v1));
            lv3LevelMap.put("ALL", "ALL");
            List<IctProdMainCodeDimVO> lv4MapByBgCode = lv4Map.get(mainCodeImportVO.getBgCode());
            Map<String, String> lv4LevelMap = lv4MapByBgCode.stream().collect(Collectors.toMap(IctProdMainCodeDimVO::getLv4ProdListCnName, IctProdMainCodeDimVO::getLv4ProdListCode, (v1, v2) -> v1));
            lv4LevelMap.put("ALL", "ALL");
            mainCodeImportVO.setLv1ProdListCode(lv1LevelMap.get(mainCodeImportVO.getLv1ProdListCnName()));
            mainCodeImportVO.setLv2ProdListCode(lv2LevelMap.get(mainCodeImportVO.getLv2ProdListCnName()));
            mainCodeImportVO.setLv3ProdListCode(lv3LevelMap.get(mainCodeImportVO.getLv3ProdListCnName()));
            mainCodeImportVO.setLv4ProdListCode(lv4LevelMap.get(mainCodeImportVO.getLv4ProdListCnName()));
        }
    }

    private void validateDuplicateData(MainCodeImportVO mainCodeImportVO, StringBuilder builder) {
        // 校验导入的数据是否有重复
        String content = getContent(mainCodeImportVO);
        if (checkDuplicateData.contains(content)) {
            builder.append("重复数据：").append(content).append(";");
        } else {
            checkDuplicateData.add(content);
        }
    }

    private String getContent(MainCodeImportVO mainCodeImportVO) {
        return StringUtils.defaultIfBlank(mainCodeImportVO.getBgCnName(), "").concat("#")
                .concat(StringUtils.defaultIfBlank(mainCodeImportVO.getLv1ProdListCnName(), "")).concat("#")
                .concat(StringUtils.defaultIfBlank(mainCodeImportVO.getLv2ProdListCnName(), "")).concat("#")
                .concat(StringUtils.defaultIfBlank(mainCodeImportVO.getLv3ProdListCnName(), "")).concat("#")
                .concat(StringUtils.defaultIfBlank(mainCodeImportVO.getLv4ProdListCnName(), "")).concat("#")
                .concat(StringUtils.defaultIfBlank(mainCodeImportVO.getSpartCode(), ""));
    }

    private void validateEmptyField(MainCodeImportVO mainCodeImportVO, StringBuilder builder) {
        ExcelImportValid.valid(mainCodeImportVO);
        builder.append(mainCodeImportVO.getErrorMessage());
    }

}
