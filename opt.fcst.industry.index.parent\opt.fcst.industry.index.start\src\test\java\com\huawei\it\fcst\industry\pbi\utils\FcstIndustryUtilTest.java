package com.huawei.it.fcst.industry.pbi.utils;

import com.huawei.it.fcst.industry.pbi.vo.common.CommonBaseVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 * @since 2025/7/2
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class FcstIndustryUtilTest {

    @InjectMocks
    private FcstIndustryUtil fcstIndustryUtil;

    @Test
    public void checkTablePreFixParam() throws CommonApplicationException {
        CommonBaseVO commonBaseVO = new CommonBaseVO();
        commonBaseVO.setCostType("");
        commonBaseVO.setGranularityType("PROD");
        fcstIndustryUtil.checkTablePreFixParam(commonBaseVO);
    }

}