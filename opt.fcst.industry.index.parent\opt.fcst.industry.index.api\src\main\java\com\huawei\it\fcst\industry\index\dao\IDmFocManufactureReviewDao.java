/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.config.ManufactureBottomVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * IDmFocManufactureReviewDao Class
 *
 * <AUTHOR>
 * @since 2023/12/7
 */
public interface IDmFocManufactureReviewDao {

    /**
     * 制造成本-ITEM异常数据录入分页查询
     *
     * @param manufactureBottomVO manufactureBottomVO
     * @param pageVO pageVO
     * @return PagedResult
     */
    PagedResult<ManufactureBottomVO> findByPage(@Param("manufactureBottomVO") ManufactureBottomVO manufactureBottomVO, @Param("pageVO")PageVO pageVO);

    List<ManufactureBottomVO> findBaseDropDown(ManufactureBottomVO manufactureBottomVO);

    List<Map> findBaseReviewList(ManufactureBottomVO manufactureBottomVO);

    List<ManufactureBottomVO> findMadeReviewVOList(ManufactureBottomVO manufactureBottomVO);

    int createMadeReviewList(@Param("list") List<ManufactureBottomVO> madeReviewVOList,@Param("tablePreFix") String tablePreFix);

    List<ManufactureBottomVO> getMadeItemDropDown(ManufactureBottomVO manufactureBottomVO);

    ManufactureBottomVO getMadeImpactQty(ManufactureBottomVO manufactureBottomVO);
}
