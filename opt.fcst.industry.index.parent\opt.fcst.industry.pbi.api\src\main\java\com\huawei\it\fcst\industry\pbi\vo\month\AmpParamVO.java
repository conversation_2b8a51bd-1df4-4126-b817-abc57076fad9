/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.month;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * AmpParamVO Class
 *
 * <AUTHOR>
 * @since 2024/1/29
 */
@Getter
@Setter
@NoArgsConstructor
public class AmpParamVO {

    private Integer algorithmType = 1;

    private List<Factors> factors;

    private String rules;

    private String scenarioType = "1";
}
