<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IDmFocMonthAccCostAmpDao">
    <resultMap type="com.huawei.it.fcst.industry.index.vo.month.DmFocMonthCostIdxVO" id="resultMap">
        <result property="id" column="id"/>
        <result property="versionId" column="version_id"/>
        <result property="periodYear" column="period_year"/>
        <result property="periodId" column="period_id"/>
        <result property="basePeriodId" column="base_period_id"/>
        <result property="prodRndTeamCode" column="prod_rnd_team_code"/>
        <result property="prodRndTeamCnName" column="prod_rnd_team_cn_name"/>
        <result property="groupCode" column="group_code"/>
        <result property="groupCnName" column="group_cn_name"/>
        <result property="groupLevel" column="group_level"/>
        <result property="costIndex" column="cost_index"/>
        <result property="parentCode" column="parent_code"/>
        <result property="parentCnName" column="parent_cn_name"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="appendFlag" column="append_flag"/>
        <result property="weightRate" column="weight_rate"/>
        <result property="weightPercent" column="weight_percent"/>
        <result property="scenarioFlag" column="scenario_flag"/>
        <result property="costType" column="cost_type"/>

    </resultMap>

    <sql id="allField">
        distinct
        prod_rnd_team_code,
        prod_rnd_team_cn_name,
        group_cn_name,
        group_level,
        group_code,
        period_year,
        period_id,
        ROUND(cost_index, 2) AS cost_index,
        version_id,
        <if test='searchParamsVO.costType == "STD"'>
        'STD' AS cost_type,
        </if>
        <if test='searchParamsVO.costType == "REPLACE"'>
        'REPLACE' AS cost_type,
        </if>
        <if test='searchParamsVO.costType == "SAME"'>
        'SAME' AS cost_type,
        </if>
        last_update_date
    </sql>

    <select id="findActualMonthNum" resultType="java.lang.Long">
        SELECT IFNULL(max(period_id),0) period_id
        FROM fin_dm_opt_foi.dm_foc_repl_same_total_mtd_index_t t
        WHERE t.del_flag = 'N'
          AND t.scenario_flag = 'S'
          AND t.version_id =(
            SELECT version_id
            FROM fin_dm_opt_foi.dm_foc_version_info_t
        WHERE del_flag = 'N'
          AND is_running = 'N'
          AND status = 1
          AND data_type = 'ITEM'
          AND version_type IN ('AUTO', 'FINAL')
        ORDER BY creation_date DESC
            LIMIT 1
            )
    </select>
</mapper>
