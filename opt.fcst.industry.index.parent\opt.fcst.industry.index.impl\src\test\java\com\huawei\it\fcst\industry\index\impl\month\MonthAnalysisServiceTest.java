/*
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.month;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.huawei.it.fcst.industry.index.dao.IDataCipherTextDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocActualCostDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocDataRefreshStatusDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeActualCostDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeMonthWeightDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeRecMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMonthWeightDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocRecMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTotalActualCostDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTotalMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTotalMonthWeightDao;
import com.huawei.it.fcst.industry.index.enums.ResultCodeEnum;
import com.huawei.it.fcst.industry.index.impl.annual.AnnualCommonService;
import com.huawei.it.fcst.industry.index.impl.annual.StatisticsExcelService;
import com.huawei.it.fcst.industry.index.impl.combination.AsyncService;
import com.huawei.it.fcst.industry.index.service.common.ICommonService;
import com.huawei.it.fcst.industry.index.utils.AesGcmUtil;
import com.huawei.it.fcst.config.ConfigUtil;
import com.huawei.it.fcst.industry.index.utils.FcstIndexMadeUtil;
import com.huawei.it.fcst.industry.index.utils.FcstIndexUtil;
import com.huawei.it.fcst.industry.index.utils.FileProcessUtis;
import com.huawei.it.fcst.industry.index.utils.TestUtils;
import com.huawei.it.fcst.industry.index.utils.UserInfoUtils;
import com.huawei.it.fcst.industry.index.vo.ciphertext.VarifyTaskVO;
import com.huawei.it.fcst.industry.index.vo.common.DataPermissionsVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFoiImpExpRecordVO;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.month.DmFocActualCostVO;
import com.huawei.it.fcst.industry.index.vo.month.DmFocMonthCostIdxVO;
import com.huawei.it.fcst.industry.index.vo.month.DmFocMonthWeightVO;
import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.security.RoleVO;
import com.huawei.it.jalor5.security.UserVO;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.web.multipart.support.DefaultMultipartHttpServletRequest;

import javax.servlet.ServletOutputStream;
import javax.servlet.WriteListener;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Future;

import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 * @since 2023/4/4
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest( {FcstIndexUtil.class,FcstIndexMadeUtil.class, ConfigUtil.class, FileProcessUtis.class, StatisticsExcelService.class,
        AesGcmUtil.class, UserInfoUtils.class})
public class MonthAnalysisServiceTest {

    @InjectMocks
    private MonthAnalysisService monthAnalysisService;

    @Mock
    private MonthCommonService monthCommonService;

    @Mock
    private IDmFocMonthCostIdxDao dmFocMonthCostIdxDao;

    @Mock
    private IDmFocMonthWeightDao dmFocMonthWeightDao;

    @Mock
    private IDmFocActualCostDao dmFocActualCostDao;

    @Mock
    private IDmFocMadeActualCostDao dmFocMadeActualCostDao;

    @Mock
    private ICommonService commonService;

    @Mock
    private AsyncService asyncService;

    @Mock
    private AsyncExportService asyncExportService;

    @Mock
    private IDataCipherTextDao iDataCipherTextDao;

    @Mock
    private AnnualCommonService annualCommonService;

    @Mock
    private IDmFocTotalMonthCostIdxDao dmFocTotalMonthCostIdxDao;

    @Mock
    private IDmFocRecMonthCostIdxDao dmFocRecMonthCostIdxDao;

    @Mock
    private IDmFocMadeRecMonthCostIdxDao dmFocMadeRecMonthCostIdxDao;

    @Mock
    private IDmFocDataRefreshStatusDao dataRefreshStatusDao;

    @Mock
    private IDmFocMadeMonthWeightDao dmFocMadeMonthWeightDao;

    @Mock
    private IDmFocTotalActualCostDao dmFocTotalActualCostDao;

    @Mock
    private IDmFocTotalMonthWeightDao dmFocTotalMonthWeightDao;

    @Mock
    private IDmFocMadeMonthCostIdxDao dmFocMadeMonthCostIdxDao;

    @Mock
    DefaultMultipartHttpServletRequest httpServletRequest;

    @Mock
    private ConfigUtil configUtil;

    private String plainText = "12adadadsadssaasddsaasdsdaasdfsd1223aaaa";

    private JSONObject json;

    @Before
    public void before() throws Exception {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);
        json = TestUtils.getTestArg(
                "/com/huawei/it/fcst/industry/index/impl/month/MonthAnalysisService/monthAnalysisJson.json");
    }

    HttpServletResponse response = new HttpServletResponse() {
        @Override
        public void addCookie(Cookie cookie) {

        }

        @Override
        public boolean containsHeader(String s) {
            return false;
        }

        @Override
        public String encodeURL(String s) {
            return null;
        }

        @Override
        public String encodeRedirectURL(String s) {
            return null;
        }

        @Override
        public String encodeUrl(String s) {
            return null;
        }

        @Override
        public String encodeRedirectUrl(String s) {
            return null;
        }

        @Override
        public void sendError(int i, String s) throws IOException {

        }

        @Override
        public void sendError(int i) throws IOException {

        }

        @Override
        public void sendRedirect(String s) throws IOException {

        }

        @Override
        public void setDateHeader(String s, long l) {

        }

        @Override
        public void addDateHeader(String s, long l) {

        }

        @Override
        public void setHeader(String s, String s1) {

        }

        @Override
        public void addHeader(String s, String s1) {

        }

        @Override
        public void setIntHeader(String s, int i) {

        }

        @Override
        public void addIntHeader(String s, int i) {

        }

        @Override
        public void setStatus(int i) {

        }

        @Override
        public void setStatus(int i, String s) {

        }

        @Override
        public int getStatus() {
            return 0;
        }

        @Override
        public String getHeader(String s) {
            return null;
        }

        @Override
        public Collection<String> getHeaders(String s) {
            return null;
        }

        @Override
        public Collection<String> getHeaderNames() {
            return null;
        }

        @Override
        public String getCharacterEncoding() {
            return null;
        }

        @Override
        public String getContentType() {
            return null;
        }

        @Override
        public ServletOutputStream getOutputStream() throws IOException {
            return new ServletOutputStream() {
                @Override
                public boolean isReady() {
                    return false;
                }

                @Override
                public void setWriteListener(WriteListener writeListener) {

                }

                @Override
                public void write(int b) throws IOException {

                }
            };
        }

        @Override
        public PrintWriter getWriter() throws IOException {
            return null;
        }

        @Override
        public void setCharacterEncoding(String s) {

        }

        @Override
        public void setContentLength(int i) {

        }

        @Override
        public void setContentLengthLong(long l) {

        }

        @Override
        public void setContentType(String s) {

        }

        @Override
        public void setBufferSize(int i) {

        }

        @Override
        public int getBufferSize() {
            return 0;
        }

        @Override
        public void flushBuffer() throws IOException {

        }

        @Override
        public void resetBuffer() {

        }

        @Override
        public boolean isCommitted() {
            return false;
        }

        @Override
        public void reset() {

        }

        @Override
        public void setLocale(Locale locale) {

        }

        @Override
        public Locale getLocale() {
            return null;
        }
    };

    @Test
    public void isDataIsOkTest() {
        boolean result = monthAnalysisService.isDataIsOk(null);
        Assertions.assertFalse(result);
    }

    @Test
    public void getMultiBoxList() throws Exception {
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        String groupLevel = "ICT";
        String viewFlag = "0";
        List<String> ProdRndTeamCodeList = new ArrayList<>();
        ProdRndTeamCodeList.add("104364");
        String granule  ="U";
        List<DmFocMonthWeightVO> weghtLists = JSONObject.parseArray(json.getString("List<DmFocMonthWeightVO>"),DmFocMonthWeightVO.class);
        MonthAnalysisVO commonViewVO = new MonthAnalysisVO();
        commonViewVO.setViewFlag(viewFlag);
        commonViewVO.setGroupLevel(groupLevel);
        commonViewVO.setTeamCodeList(ProdRndTeamCodeList);
        commonViewVO.setVersionId(91L);
        commonViewVO.setGroupCodeList(groupCodeList);
        commonViewVO.setGranularityType(granule);
        commonViewVO.setIsMultipleSelect(false);
        commonViewVO.setCostType("P");
        commonViewVO.setIndustryOrg("IAS");
        mockNextGroupLevel();
        DataPermissionsVO dimensionList = new DataPermissionsVO();
        Set<String> lv1DimensionSet = new HashSet<>();
        lv1DimensionSet.add("code1");
        dimensionList.setLv1DimensionSet(lv1DimensionSet);
        PowerMockito.doReturn(dimensionList).when(commonService).getDimensionList(anyString(),anyString(),anyString());
        PowerMockito.when(dmFocMonthWeightDao.findWeightList(Mockito.any(MonthAnalysisVO.class))).thenReturn(weghtLists);
        Assertions.assertNotNull(monthAnalysisService.getMultiBoxList(commonViewVO));

        commonViewVO.setCostType("M");
        commonViewVO.setGroupLevel(groupLevel);
        mockMadeNextGroupLevel();
        PowerMockito.when(dmFocMadeMonthWeightDao.findMadeWeightList(Mockito.any(MonthAnalysisVO.class))).thenReturn(weghtLists);
        Assertions.assertNotNull(monthAnalysisService.getMultiBoxList(commonViewVO));

        commonViewVO.setCostType("T");
        commonViewVO.setGroupLevel(groupLevel);
        mockNextGroupLevel();
        PowerMockito.when(dmFocTotalMonthWeightDao.findTotalWeightList(Mockito.any(MonthAnalysisVO.class))).thenReturn(weghtLists);
        Assertions.assertNotNull(monthAnalysisService.getMultiBoxList(commonViewVO));
    }

    private void mockNextGroupLevel() throws Exception {
        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","LV1");
        map.put("nextGroupName","LV1重量级团队");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);
    }

    private void mockMadeNextGroupLevel() throws Exception {
        mockStatic(FcstIndexMadeUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","LV1");
        map.put("nextGroupName","LV1重量级团队");
        when(FcstIndexMadeUtil.class, "getNextGroupLevel", any()).thenReturn(map);
    }

    @Test
    public void getMultiBoxListVaild() throws Exception {
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        String groupLevel = "ICT";
        String viewFlag = "0";
        String granule  ="1";
        List<String> ProdRndTeamCodeList = new ArrayList<>();
        ProdRndTeamCodeList.add("104364");
        MonthAnalysisVO commonViewVO = new MonthAnalysisVO();
        commonViewVO.setViewFlag(viewFlag);
        commonViewVO.setGroupLevel(groupLevel);
        commonViewVO.setTeamCodeList(ProdRndTeamCodeList);
        commonViewVO.setVersionId(91L);
        commonViewVO.setGroupCodeList(groupCodeList);
        commonViewVO.setGranularityType(granule);
        commonViewVO.setIsMultipleSelect(false);
        commonViewVO.setCostType("P");
        commonViewVO.setIndustryOrg("IAS");
        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","LV1");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);
        DataPermissionsVO dimensionList = new DataPermissionsVO();
        Set<String> lv1DimensionSet = new HashSet<>();
        lv1DimensionSet.add("code1");
        dimensionList.setLv1DimensionSet(lv1DimensionSet);
        PowerMockito.doReturn(dimensionList).when(commonService).getDimensionList(anyString(),anyString(),anyString());

        Assertions.assertNotNull(monthAnalysisService.getMultiBoxList(commonViewVO));
    }

    @Test
    public void getMultiBoxListVaild1test() throws Exception {
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        String groupLevel = "ICT";
        String viewFlag = "1";
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        String granule  ="1";
        mockStatic(FcstIndexUtil.class);
        MonthAnalysisVO commonViewVO = new MonthAnalysisVO();
        commonViewVO.setViewFlag(viewFlag);
        commonViewVO.setGroupLevel(groupLevel);
        commonViewVO.setTeamCodeList(prodRndTeamCodeList);
        commonViewVO.setVersionId(91L);
        commonViewVO.setGroupCodeList(groupCodeList);
        commonViewVO.setGranularityType(granule);
        commonViewVO.setIsMultipleSelect(false);
        commonViewVO.setCostType("P");
        commonViewVO.setIndustryOrg("IAS");
        Map map = new HashMap();
        map.put("nextGroupLevel","LV1");
        map.put("nextGroupName","LV1重量级团队");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);
        List<DmFocMonthWeightVO> weghtLists = new ArrayList<>();
        DmFocMonthWeightVO  focMonthWeightVO = new DmFocMonthWeightVO();
        focMonthWeightVO.setGroupCode("100001");
        weghtLists.add(focMonthWeightVO);
        PowerMockito.doReturn(weghtLists).when(dmFocMonthWeightDao).findWeightList(any());

        DataPermissionsVO dimensionList = new DataPermissionsVO();
        PowerMockito.doReturn(dimensionList).when(commonService).getDimensionList(anyString(),anyString(),anyString());

        Assertions.assertNotNull(monthAnalysisService.getMultiBoxList(commonViewVO));
    }

    @Test
    public void getMultiBoxListVaild2test() throws Exception {
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        String groupLevel = "ICT";
        String viewFlag = "1";
        String granule  ="1";
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        mockStatic(FcstIndexUtil.class);
        MonthAnalysisVO commonViewVO = new MonthAnalysisVO();
        commonViewVO.setViewFlag(viewFlag);
        commonViewVO.setGroupLevel(groupLevel);
        commonViewVO.setTeamCodeList(prodRndTeamCodeList);
        commonViewVO.setVersionId(91L);
        commonViewVO.setGroupCodeList(groupCodeList);
        commonViewVO.setGranularityType(granule);
        commonViewVO.setIsMultipleSelect(false);
        commonViewVO.setCostType("P");
        commonViewVO.setIndustryOrg("IAS");
        Map map = new HashMap();
        map.put("nextGroupLevel","ICT");
        map.put("nextGroupName","ICT");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);
        List<DmFocMonthWeightVO> weghtLists = new ArrayList<>();
        DmFocMonthWeightVO focMonthWeightVO = new DmFocMonthWeightVO();
        focMonthWeightVO.setGroupCode("100001");
        weghtLists.add(focMonthWeightVO);
        PowerMockito.doReturn(weghtLists).when(dmFocMonthWeightDao).findWeightList(any());

        DataPermissionsVO dimensionList = new DataPermissionsVO();
        PowerMockito.doReturn(dimensionList).when(commonService).getDimensionList(anyString(),anyString(),anyString());

        Assertions.assertNotNull(monthAnalysisService.getMultiBoxList(commonViewVO));
    }

    @Test
    public void getMultiBoxListVaild3test() throws Exception {
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        String groupLevel = "ICT";
        String viewFlag = "0";
        String granule  ="1";
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        mockStatic(FcstIndexUtil.class);
        MonthAnalysisVO commonViewVO = new MonthAnalysisVO();
        commonViewVO.setViewFlag(viewFlag);
        commonViewVO.setGroupLevel(groupLevel);
        commonViewVO.setTeamCodeList(prodRndTeamCodeList);
        commonViewVO.setVersionId(91L);
        commonViewVO.setGroupCodeList(groupCodeList);
        commonViewVO.setGranularityType(granule);
        commonViewVO.setIsMultipleSelect(false);
        commonViewVO.setCostType("P");
        commonViewVO.setIndustryOrg("IAS");
        Map map = new HashMap();
        map.put("nextGroupLevel","ICT");
        map.put("nextGroupName","ICT");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);
        List<DmFocMonthWeightVO> weghtLists = new ArrayList<>();
        DmFocMonthWeightVO  focMonthWeightVO = new DmFocMonthWeightVO();
        focMonthWeightVO.setGroupCode("100001");
        weghtLists.add(focMonthWeightVO);
        PowerMockito.doReturn(weghtLists).when(dmFocMonthWeightDao).findWeightList(any());
        DataPermissionsVO dimensionList = new DataPermissionsVO();
        PowerMockito.doReturn(dimensionList).when(commonService).getDimensionList(anyString(),anyString(),anyString());
        Assertions.assertNotNull(monthAnalysisService.getMultiBoxList(commonViewVO));
    }

    @Test
    public void getMultiBoxListVaild4test() throws Exception {
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        String groupLevel = "ICT";
        String viewFlag = "1";
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        String granule  ="1";
        mockStatic(FcstIndexUtil.class);
        MonthAnalysisVO commonViewVO = new MonthAnalysisVO();
        commonViewVO.setViewFlag(viewFlag);
        commonViewVO.setGroupLevel(groupLevel);
        commonViewVO.setTeamCodeList(prodRndTeamCodeList);
        commonViewVO.setVersionId(91L);
        commonViewVO.setGroupCodeList(groupCodeList);
        commonViewVO.setGranularityType(granule);
        commonViewVO.setIsMultipleSelect(false);
        commonViewVO.setCostType("P");
        commonViewVO.setIndustryOrg("IAS");
        Map map = new HashMap();
        map.put("nextGroupLevel","ICT");
        map.put("nextGroupName","ICT");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);
        List<DmFocMonthWeightVO> weghtLists = new ArrayList<>();
        DmFocMonthWeightVO focMonthWeightVO = new DmFocMonthWeightVO();
        focMonthWeightVO.setGroupCode("100002");
        weghtLists.add(focMonthWeightVO);

        DmFocMonthWeightVO focMonthWeightVO2 = new DmFocMonthWeightVO();
        focMonthWeightVO2.setGroupCode("100001");
        weghtLists.add(focMonthWeightVO2);
        PowerMockito.doReturn(weghtLists).when(dmFocMonthWeightDao).findWeightList(any());

        DataPermissionsVO dimensionList = new DataPermissionsVO();
        PowerMockito.doReturn(dimensionList).when(commonService).getDimensionList(anyString(),anyString(),anyString());

        Assertions.assertNotNull(monthAnalysisService.getMultiBoxList(commonViewVO));
    }

    @Test
    public void getMultiBoxListVaild5test() throws Exception {
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        String groupLevel = "ICT";
        String viewFlag = "1";
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        String granule  ="U";
        mockStatic(FcstIndexUtil.class);
        MonthAnalysisVO commonViewVO = new MonthAnalysisVO();
        commonViewVO.setViewFlag(viewFlag);
        commonViewVO.setGroupLevel(groupLevel);
        commonViewVO.setTeamCodeList(prodRndTeamCodeList);
        commonViewVO.setVersionId(91L);
        commonViewVO.setGroupCodeList(groupCodeList);
        commonViewVO.setGranularityType(granule);
        commonViewVO.setIsMultipleSelect(false);
        commonViewVO.setCostType("P");
        commonViewVO.setIndustryOrg("IAS");
        Map map = new HashMap();
        map.put("nextGroupLevel","L2");
        map.put("nextGroupName","L2");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);
        List<DmFocMonthWeightVO> weghtLists = new ArrayList<>();
        DmFocMonthWeightVO focMonthWeightVO = new DmFocMonthWeightVO();
        focMonthWeightVO.setGroupCode("100001");
        weghtLists.add(focMonthWeightVO);
        PowerMockito.doReturn(weghtLists).when(dmFocMonthWeightDao).findWeightList(any());

        DataPermissionsVO dimensionList = new DataPermissionsVO();
        PowerMockito.doReturn(dimensionList).when(commonService).getDimensionList(anyString(),anyString(),anyString());

        List<DmFocViewInfoVO> dmFocViewInfoVOList0 = JSONObject.parseArray(json.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        when(annualCommonService.sortLv1ByCode(any(List.class))).thenReturn(dmFocViewInfoVOList0);
        Assertions.assertNotNull(monthAnalysisService.getMultiBoxList(commonViewVO));
        commonViewVO.getGroupCodeList().clear();
        Assertions.assertNotNull(monthAnalysisService.getMultiBoxList(commonViewVO));
        commonViewVO.setCustomIdList(Arrays.asList(1L));
        Assertions.assertNotNull(monthAnalysisService.getMultiBoxList(commonViewVO));
    }

    @Test
    public void getIndustryCostIndexChart() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setViewFlag("0");
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setBasePeriodId(202201);
        ResultDataVO industryCostIndexChart = new ResultDataVO();
        PowerMockito.doReturn(industryCostIndexChart).when(monthCommonService).getIndustryCostIndexChart(monthAnalysisVO);

        DataPermissionsVO dimensionList = new DataPermissionsVO();
        PowerMockito.doReturn(dimensionList).when(commonService).getDimensionList(anyString(),anyString(),anyString());
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostIndexChart(monthAnalysisVO));
    }

    @Test
    public void getIndustryCostYoyAndPopChart() {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setViewFlag("0");
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setBasePeriodId(202201);
        ResultDataVO industryCostIndexChart = new ResultDataVO();
        PowerMockito.doReturn(industryCostIndexChart).when(monthCommonService).getIndustryCostYoyAndPopChart(monthAnalysisVO);

        Assertions.assertNotNull(monthAnalysisService.getIndustryCostYoyAndPopChart(monthAnalysisVO));
    }


    @Test
    public void getIndustryCostMultiDimensionVaild() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setViewFlag("0");
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setIndustryOrg("IAS");
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostMultiDimensionChart(monthAnalysisVO));
    }

    @Test
    public void getIndustryCostMultiDimensionVaild1test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("1504B");
        groupCodeList.add("1501C");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setIndustryOrg("IAS");
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostMultiDimensionChart(monthAnalysisVO));
    }
    @Test
    public void getIndustryCostMultiDimensionVaild2test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setViewFlag("0");
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setVersionId(91L);
        List<String> subGroupCodeList = new ArrayList<>();
        subGroupCodeList.add("1504B");
        subGroupCodeList.add("1501C");
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setGroupCodeList(subGroupCodeList);

        doReturn(true).when(monthCommonService).isDataIsOk(any());
        List<DmFocMonthCostIdxVO> priceIndexChartList = new ArrayList<>();
        DmFocMonthCostIdxVO focMonthCostIdxVO = new DmFocMonthCostIdxVO();
        focMonthCostIdxVO.setCostIndex(1.33);
        focMonthCostIdxVO.setGroupCode("1324");
        priceIndexChartList.add(focMonthCostIdxVO);
        PowerMockito.doReturn(priceIndexChartList).when(dmFocMonthCostIdxDao).findPriceIndexChartByMultiDim(monthAnalysisVO);
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostMultiDimensionChart(monthAnalysisVO));
    }

    @Test
    public void getIndustryCostMultiDimensionVaild3test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIsContainComb(true);
        monthAnalysisVO.setIsMultipleSelect(false);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setViewFlag("6");
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setIndustryOrg("IAS");
        List<String> subGroupCodeList = new ArrayList<>();
        subGroupCodeList.add("1504B");
        subGroupCodeList.add("1501C");
        monthAnalysisVO.setSubGroupCodeList(subGroupCodeList);
        ResultDataVO periodYearList = new ResultDataVO();
        List<String> yearList = new ArrayList<>();
        yearList.add("2013");
        periodYearList.setData(yearList);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList);

        doReturn(true).when(monthCommonService).isDataIsOk(any());
        List<DmFocMonthCostIdxVO> priceIndexChartList = new ArrayList<>();
        DmFocMonthCostIdxVO focMonthCostIdxVO = new DmFocMonthCostIdxVO();
        focMonthCostIdxVO.setCostIndex(1.33);
        focMonthCostIdxVO.setGroupCode("1324");
        priceIndexChartList.add(focMonthCostIdxVO);
        mockNextGroupLevel();
        PowerMockito.doReturn(priceIndexChartList).when(dmFocMonthCostIdxDao).findPriceIndexChartByMultiDim(monthAnalysisVO);
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostMultiDimensionChart(monthAnalysisVO));
    }

    @Test
    public void getIndustryCostMultiDimensionVaild4test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIsContainComb(true);
        monthAnalysisVO.setIsMultipleSelect(false);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        monthAnalysisVO.setGroupLevel("LV1");
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setViewFlag("5");
        monthAnalysisVO.setIndustryOrg("IAS");
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setVersionId(91L);
        List<String> subGroupCodeList = new ArrayList<>();
        subGroupCodeList.add("1504B");
        subGroupCodeList.add("1501C");
        monthAnalysisVO.setSubGroupCodeList(subGroupCodeList);
        monthAnalysisVO.setCustomIdList(Arrays.asList(1L));
        ResultDataVO periodYearList = new ResultDataVO();
        List<String> yearList = new ArrayList<>();
        yearList.add("2013");
        periodYearList.setData(yearList);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList);

        doReturn(true).when(monthCommonService).isDataIsOk(any());
        List<DmFocMonthCostIdxVO> priceIndexChartList = new ArrayList<>();
        DmFocMonthCostIdxVO focMonthCostIdxVO = new DmFocMonthCostIdxVO();
        focMonthCostIdxVO.setCostIndex(1.33);
        focMonthCostIdxVO.setGroupCode("1324");
        priceIndexChartList.add(focMonthCostIdxVO);
        mockNextGroupLevel();
        PowerMockito.doReturn(priceIndexChartList).when(dmFocMonthCostIdxDao).findPriceIndexChartByMultiDim(monthAnalysisVO);
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostMultiDimensionChart(monthAnalysisVO));
    }

    @Test
    public void getIndustryCostMultiDimensionVaild5test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        monthAnalysisVO.setGroupLevel("LV2");
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setViewFlag("2");
        monthAnalysisVO.setIndustryOrg("IAS");
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setVersionId(91L);
        List<String> subgGroupCodeList = new ArrayList<>();
        subgGroupCodeList.add("1504B");
        subgGroupCodeList.add("1501C");
        monthAnalysisVO.setSubGroupCodeList(subgGroupCodeList);
        monthAnalysisVO.setIsContainComb(true);
        monthAnalysisVO.setIsMultipleSelect(false);
        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","LV1");
        map.put("nextGroupName","重量级团队LV1");
        when(FcstIndexUtil.class, "getNextGroupLevel", monthAnalysisVO).thenReturn(map);
        ResultDataVO periodYearList = new ResultDataVO();
        List<String> yearList = new ArrayList<>();
        yearList.add("2013");
        periodYearList.setData(yearList);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList);
        doReturn(true).when(monthCommonService).isDataIsOk(any());
        List<DmFocMonthCostIdxVO> priceIndexChartList = new ArrayList<>();
        DmFocMonthCostIdxVO focMonthCostIdxVO = new DmFocMonthCostIdxVO();
        focMonthCostIdxVO.setCostIndex(1.33);
        focMonthCostIdxVO.setGroupCode("1324");
        priceIndexChartList.add(focMonthCostIdxVO);
        PowerMockito.doReturn(priceIndexChartList).when(dmFocMonthCostIdxDao).findPriceIndexChartByMultiDim(monthAnalysisVO);
        monthAnalysisVO.setIsMultipleSelect(true);
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostMultiDimensionChart(monthAnalysisVO));

        monthAnalysisVO.setCustomIdList(Arrays.asList(1L));
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostMultiDimensionChart(monthAnalysisVO));
    }


    @Test
    public void getIndustryCostMultiDimensionVaild6test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setViewFlag("4");
        monthAnalysisVO.setIndustryOrg("IAS");
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setVersionId(91L);
        List<String> subGroupCodeList = new ArrayList<>();
        subGroupCodeList.add("1504B");
        subGroupCodeList.add("1501C");
        monthAnalysisVO.setSubGroupCodeList(subGroupCodeList);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setReverseViewFlag(true);

        ResultDataVO periodYearList = new ResultDataVO();
        List<String> yearList = new ArrayList<>();
        yearList.add("2013");
        periodYearList.setData(yearList);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList);
        mockNextGroupLevel();
        doReturn(false).when(monthCommonService).isDataIsOk(any());
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostMultiDimensionChart(monthAnalysisVO));
    }

    @Test
    public void getIndustryCostMultiDimensionVaild8test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setViewFlag("1");
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setVersionId(91L);
        List<String> subGroupCodeList = new ArrayList<>();
        subGroupCodeList.add("1504B");
        subGroupCodeList.add("1501C");
        monthAnalysisVO.setSubGroupCodeList(subGroupCodeList);
        monthAnalysisVO.setIsContainComb(true);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setIndustryOrg("IAS");
        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","LV1");
        when(FcstIndexUtil.class, "getNextGroupLevel", monthAnalysisVO).thenReturn(map);
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostMultiDimensionChart(monthAnalysisVO));
        monthAnalysisVO.setSubGroupCodeList(null);
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostMultiDimensionChart(monthAnalysisVO));
    }

    @Test
    public void getIndustryCostMultiDimensionVaild9test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setViewFlag("1");
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setVersionId(91L);
        List<String> subGroupCodeList = new ArrayList<>();
        subGroupCodeList.add("1504B");
        subGroupCodeList.add("1501C");
        monthAnalysisVO.setSubGroupCodeList(subGroupCodeList);
        monthAnalysisVO.setIsContainComb(true);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setIndustryOrg("IAS");
        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupName","重量级团队LV1");
        when(FcstIndexUtil.class, "getNextGroupLevel", monthAnalysisVO).thenReturn(map);
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostMultiDimensionChart(monthAnalysisVO));
        monthAnalysisVO.setViewFlag(null);
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostMultiDimensionChart(monthAnalysisVO));
    }

    @Test
    public void getIndustryCostMultiDimensionChart10test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        monthAnalysisVO.setGroupLevel("L1");
        monthAnalysisVO.setViewFlag("4");
        monthAnalysisVO.setCostType("P");
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setIndustryOrg("IAS");
        List<String> subGroupCodeList = new ArrayList<>();
        subGroupCodeList.add("1504B");
        subGroupCodeList.add("1501C");
        monthAnalysisVO.setSubGroupCodeList(subGroupCodeList);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setIsMultipleSelect(false);
        ResultDataVO periodYearList = new ResultDataVO();
        List<String> yearList = new ArrayList<>();
        yearList.add("2013");
        periodYearList.setData(yearList);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList);
        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupName","name");
        map.put("nextGroupLevel","L2");
        when(FcstIndexUtil.class, "getNextGroupLevel", monthAnalysisVO).thenReturn(map);
        doReturn(true).when(monthCommonService).isDataIsOk(any());
        Mockito.when(dmFocMonthCostIdxDao.findPriceIndexChartByMultiDim(any())).thenReturn(new ArrayList<>());
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostMultiDimensionChart(monthAnalysisVO));

        monthAnalysisVO.setCostType("M");
        mockStatic(FcstIndexMadeUtil.class);
        when(FcstIndexMadeUtil.class, "getNextGroupLevel", any()).thenReturn(map);

        ResultDataVO periodYearList1 = new ResultDataVO();
        periodYearList.setData(yearList);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList1);
        Mockito.when(dmFocMadeMonthCostIdxDao.findStartEndTime(any(),any())).thenReturn(getStringLongMap());
        Mockito.when(dmFocMadeMonthCostIdxDao.findMadePriceIndexByMultiDim(any())).thenReturn(new ArrayList<>());
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostMultiDimensionChart(monthAnalysisVO));

        monthAnalysisVO.setCostType("T");
        ResultDataVO periodYearList2 = new ResultDataVO();
        periodYearList.setData(yearList);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList2);
        Mockito.when(commonService.findActualMonthNum(any())).thenReturn(1L);
        Mockito.when(dmFocTotalMonthCostIdxDao.findTotalPriceIndexByMultiDim(any())).thenReturn(new ArrayList<>());
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostMultiDimensionChart(monthAnalysisVO));
    }

    @Test
    public void getRevPriceIndexListTest() throws Exception {
        MonthAnalysisVO paramsVO = new MonthAnalysisVO();
        paramsVO.setCostType("P");
        paramsVO.setGranularityType("U");
        List<DmFocMonthCostIdxVO> priceIndexChartList = new ArrayList<>();
        Mockito.when(dmFocRecMonthCostIdxDao.findRevPriceIndexChartByMultiDim(any())).thenReturn(priceIndexChartList);
        Mockito.when(dmFocMadeRecMonthCostIdxDao.findMadeRevPriceIndexByMultiDim(any())).thenReturn(priceIndexChartList);
        Mockito.when(dmFocMadeMonthCostIdxDao.findStartEndTime(any(),any())).thenReturn(getStringLongMap());
        Whitebox.invokeMethod(monthAnalysisService, "getRevPriceIndexList", paramsVO, priceIndexChartList);
        Assert.assertNotNull(priceIndexChartList);

        paramsVO.setCostType("M");
        Whitebox.invokeMethod(monthAnalysisService, "getRevPriceIndexList", paramsVO, priceIndexChartList);
        Assert.assertNotNull(priceIndexChartList);
    }

    @NotNull
    private Map<String, Long> getStringLongMap() {
        Map<String, Long> startEndTime = new HashMap<>();
        startEndTime.put("start", 2022301L);
        startEndTime.put("end", 2022303L);
        return startEndTime;
    }

    @Test
    public void getIndustryCostWeightChart() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("IAS");
        List<DmFocMonthWeightVO> weightList = new ArrayList<>();
        DmFocMonthWeightVO  focMonthWeightVO = new DmFocMonthWeightVO();
        focMonthWeightVO.setGroupCode("28040023");
        focMonthWeightVO.setGroupCnName("28040023");
        focMonthWeightVO.setGroupLevel("ITEM");
        focMonthWeightVO.setWeightRate(0.001);
        weightList.add(focMonthWeightVO);
        doReturn(weightList).when(dmFocMonthWeightDao).findWeightList(monthAnalysisVO);
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();

        DataPermissionsVO dimensionList = new DataPermissionsVO();
        Set<String> lv1DimensionSet = new HashSet<>();
        lv1DimensionSet.add("code1");
        dimensionList.setLv1DimensionSet(lv1DimensionSet);
        doReturn(dimensionList).when(commonService).getDimensionList(anyString(),anyString(),anyString());
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostWeightChart(monthAnalysisVO));
    }

    @Test
    public void getIndustryCostWeightChart4test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setIsContainComb(true);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("IAS");
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        List<DmFocMonthWeightVO> weightList = new ArrayList<>();
        DmFocMonthWeightVO  focMonthWeightVO = new DmFocMonthWeightVO();
        focMonthWeightVO.setGroupCode("28040023");
        focMonthWeightVO.setGroupCnName("28040023");
        focMonthWeightVO.setGroupLevel("CATEGORY");
        focMonthWeightVO.setWeightRate(0.001);
        weightList.add(focMonthWeightVO);
        doReturn(weightList).when(dmFocMonthWeightDao).findWeightList(monthAnalysisVO);
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();
        DataPermissionsVO dimensionList = new DataPermissionsVO();
        doReturn(dimensionList).when(commonService).getDimensionList(anyString(),anyString(),anyString());
        monthAnalysisVO.setCustomIdList(Arrays.asList(1L));
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostWeightChart(monthAnalysisVO));
    }

    @Test
    public void getIndustryCostWeightChart1test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIsContainComb(true);
        monthAnalysisVO.setIsMultipleSelect(false);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("IAS");
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        List<DmFocMonthWeightVO> weightList = new ArrayList<>();
        DmFocMonthWeightVO  focMonthWeightVO1 = new DmFocMonthWeightVO();
        focMonthWeightVO1.setGroupCode("28040023");
        focMonthWeightVO1.setGroupCnName("28040023");
        focMonthWeightVO1.setGroupLevel("ITEM");
        focMonthWeightVO1.setWeightRate(0.001);
        weightList.add(focMonthWeightVO1);
        DmFocMonthWeightVO  focMonthWeightVO2 = new DmFocMonthWeightVO();
        focMonthWeightVO2.setGroupCode("28040024");
        focMonthWeightVO2.setGroupCnName("28040024");
        focMonthWeightVO2.setGroupLevel("ITEM");
        focMonthWeightVO2.setWeightRate(0.002);
        weightList.add(focMonthWeightVO2);
        DmFocMonthWeightVO  focMonthWeightVO3 = new DmFocMonthWeightVO();
        focMonthWeightVO3.setGroupCode("28040025");
        focMonthWeightVO3.setGroupCnName("28040025");
        focMonthWeightVO3.setGroupLevel("ITEM");
        focMonthWeightVO3.setWeightRate(0.003);
        weightList.add(focMonthWeightVO3);
        DmFocMonthWeightVO  focMonthWeightVO4 = new DmFocMonthWeightVO();
        focMonthWeightVO4.setGroupCode("28040026");
        focMonthWeightVO4.setGroupCnName("28040026");
        focMonthWeightVO4.setGroupLevel("ITEM");
        focMonthWeightVO4.setWeightRate(0.004);
        weightList.add(focMonthWeightVO4);
        DmFocMonthWeightVO  focMonthWeightVO5 = new DmFocMonthWeightVO();
        focMonthWeightVO5.setGroupCode("28040026");
        focMonthWeightVO5.setGroupCnName("28040026");
        focMonthWeightVO5.setGroupLevel("ITEM");
        focMonthWeightVO5.setWeightRate(0.004);
        weightList.add(focMonthWeightVO5);
        DmFocMonthWeightVO  focMonthWeightVO6 = new DmFocMonthWeightVO();
        focMonthWeightVO6.setGroupCode("28040026");
        focMonthWeightVO6.setGroupCnName("28040026");
        focMonthWeightVO6.setGroupLevel("ITEM");
        focMonthWeightVO6.setWeightRate(0.004);
        weightList.add(focMonthWeightVO6);
        DmFocMonthWeightVO  focMonthWeightVO7 = new DmFocMonthWeightVO();
        focMonthWeightVO7.setGroupCode("28040026");
        focMonthWeightVO7.setGroupCnName("28040026");
        focMonthWeightVO7.setGroupLevel("ITEM");
        focMonthWeightVO7.setWeightRate(0.004);
        weightList.add(focMonthWeightVO7);
        DmFocMonthWeightVO  focMonthWeightVO8 = new DmFocMonthWeightVO();
        focMonthWeightVO8.setGroupCode("28040026");
        focMonthWeightVO8.setGroupCnName("28040026");
        focMonthWeightVO8.setGroupLevel("ITEM");
        focMonthWeightVO8.setWeightRate(0.004);
        weightList.add(focMonthWeightVO8);
        DmFocMonthWeightVO  focMonthWeightVO9 = new DmFocMonthWeightVO();
        focMonthWeightVO9.setGroupCode("28040026");
        focMonthWeightVO9.setGroupCnName("28040026");
        focMonthWeightVO9.setGroupLevel("ITEM");
        focMonthWeightVO9.setWeightRate(0.004);
        weightList.add(focMonthWeightVO9);
        DmFocMonthWeightVO  focMonthWeightV10 = new DmFocMonthWeightVO();
        focMonthWeightV10.setGroupCode("28040026");
        focMonthWeightV10.setGroupCnName("28040026");
        focMonthWeightV10.setGroupLevel("ITEM");
        focMonthWeightV10.setWeightRate(0.004);
        weightList.add(focMonthWeightV10);
        DmFocMonthWeightVO  focMonthWeightV11 = new DmFocMonthWeightVO();
        focMonthWeightV11.setGroupCode("28040026");
        focMonthWeightV11.setGroupCnName("28040026");
        focMonthWeightV11.setGroupLevel("ITEM");
        focMonthWeightV11.setWeightRate(0.004);
        weightList.add(focMonthWeightV11);
        doReturn(weightList).when(dmFocMonthWeightDao).findWeightList(monthAnalysisVO);
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();

        DataPermissionsVO dimensionList = new DataPermissionsVO();
        doReturn(dimensionList).when(commonService).getDimensionList(anyString(),anyString(),anyString());
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostWeightChart(monthAnalysisVO));
    }

    @Test
    public void getIndustryCostWeightChart2test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setIsMultipleSelect(false);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("1");
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("IAS");
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        mockNextGroupLevel();
        DataPermissionsVO dimensionList = new DataPermissionsVO();
        doReturn(dimensionList).when(commonService).getDimensionList(anyString(),anyString(),anyString());
        List<DmFocMonthWeightVO> weightVOList = new ArrayList<>();
        DmFocMonthWeightVO weightVO = DmFocMonthWeightVO.builder().weightPercent("0.1").weightRate(0.1d).weightRateStr("0.1").build();
        weightVOList.add(weightVO);
        when(dmFocMonthWeightDao.findWeightList(any())).thenReturn(weightVOList);
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostWeightChart(monthAnalysisVO));

        monthAnalysisVO.setCostType("M");
        mockMadeNextGroupLevel();
        when(dmFocMadeMonthWeightDao.findMadeWeightList(any())).thenReturn(weightVOList);
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostWeightChart(monthAnalysisVO));

        monthAnalysisVO.setCostType("T");
        when(dmFocTotalMonthWeightDao.findTotalWeightList(any())).thenReturn(weightVOList);
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostWeightChart(monthAnalysisVO));
    }

    @Test
    public void getIndustryCostWeightChart3test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIsContainComb(true);
        monthAnalysisVO.setCustomIdList(Arrays.asList(1L));
        monthAnalysisVO.setIsMultipleSelect(false);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("1");
        monthAnalysisVO.setCostType("P");
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        mockNextGroupLevel();
        DataPermissionsVO dimensionList = new DataPermissionsVO();
        doReturn(dimensionList).when(commonService).getDimensionList(anyString(),anyString(),anyString());
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostWeightChart(monthAnalysisVO));
    }

    @Test
    public void getIndustryCostHeatmapChart() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("IAS");
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        monthAnalysisVO.setCurPage(1);
        monthAnalysisVO.setPageSize(10);
        List<String> subGroupCodeList = new ArrayList<>();
        subGroupCodeList.add("16349");
        subGroupCodeList.add("12274");
        subGroupCodeList.add("13409");
        monthAnalysisVO.setSubGroupCodeList(subGroupCodeList);
        monthAnalysisVO.setVersionId(91L);
        List<DmFocActualCostVO> dmFocActualCostVOList = new ArrayList<>();
        dmFocActualCostVOList.add(new DmFocActualCostVO());
        PowerMockito.doReturn(dmFocActualCostVOList).when(dmFocActualCostDao).findGroupCnNameByCode(monthAnalysisVO);
        String groupCodeOrder = "16349,12274";
        PowerMockito.doReturn(groupCodeOrder).when(dmFocActualCostDao).findGroupCodeOrder(monthAnalysisVO);
        List<DmFocActualCostVO> itemAndAmt = new ArrayList<>();
        DmFocActualCostVO focActualCostVO = new DmFocActualCostVO();
        focActualCostVO.setGroupCnName("元器件");
        focActualCostVO.setActualCostAmt(1215112d);
        itemAndAmt.add(focActualCostVO);
        PowerMockito.doReturn(itemAndAmt).when(dmFocActualCostDao).findActualCostAmtList(monthAnalysisVO);
        DataPermissionsVO dimensionList = new DataPermissionsVO();
        mockNextGroupLevel();
        ResultDataVO periodYearList = new ResultDataVO();
        List<String> yearList = new ArrayList<>();
        yearList.add("2013");
        periodYearList.setData(yearList);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList);
        doReturn(dimensionList).when(commonService).getDimensionList(anyString(),anyString(),anyString());
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostHeatmapChart(monthAnalysisVO));

        monthAnalysisVO.setCostType("M");
        ResultDataVO periodYearList1 = new ResultDataVO();
        periodYearList1.setData(yearList);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList1);
        mockMadeNextGroupLevel();
        when(dmFocMadeActualCostDao.findMadeGroupCnNameByCode(any())).thenReturn(new ArrayList<>());
        when(dmFocMadeActualCostDao.findMadeGroupCodeOrder(any())).thenReturn("string");
        when(commonService.findActualMonthNum(any())).thenReturn(1L);
        when(dmFocMadeActualCostDao.findMadeActualCostAmtList(any())).thenReturn(new ArrayList<>());
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostHeatmapChart(monthAnalysisVO));

        monthAnalysisVO.setCostType("T");
        ResultDataVO periodYearLis2 = new ResultDataVO();
        periodYearLis2.setData(yearList);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearLis2);
        when(dmFocTotalActualCostDao.findTotalGroupCnNameByCode(any())).thenReturn(new ArrayList<>());
        when(dmFocTotalActualCostDao.findTotalGroupCodeOrder(any())).thenReturn("string");
        when(commonService.findActualMonthNum(any())).thenReturn(1L);
        when(dmFocTotalActualCostDao.findTotalActualCostAmtList(any())).thenReturn(new ArrayList<>());
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostHeatmapChart(monthAnalysisVO));
    }

    @Test
    public void getIndustryCostHeatmapChartVaild1test(){
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setGroupLevel("ICT");
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        monthAnalysisVO.setCurPage(1);
        monthAnalysisVO.setPageSize(10);
        monthAnalysisVO.setVersionId(91L);
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostHeatmapChart(monthAnalysisVO));
    }

    @Test
    public void getIndustryCostHeatmapChartVaild2test(){
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setViewFlag("1");
        monthAnalysisVO.setGroupLevel("ICT");
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        monthAnalysisVO.setCurPage(1);
        monthAnalysisVO.setPageSize(10);
        List<String> subGroupCodeList = new ArrayList<>();
        subGroupCodeList.add("16349");
        subGroupCodeList.add("12274");
        subGroupCodeList.add("13409");
        monthAnalysisVO.setSubGroupCodeList(subGroupCodeList);
        monthAnalysisVO.setVersionId(91L);
        ResultDataVO periodYearList = new ResultDataVO();
        List<String> yearList = new ArrayList<>();
        yearList.add("2013");
        periodYearList.setData(yearList);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList);
        List<String> groupCodeAndName = new ArrayList<>();
        groupCodeAndName.add("16349");
        groupCodeAndName.add("12274");
        PowerMockito.doReturn(groupCodeAndName).when(dmFocActualCostDao).findGroupCnNameByCode(monthAnalysisVO);
        String groupCodeOrder = "16349,12274";
        PowerMockito.doReturn(groupCodeOrder).when(dmFocActualCostDao).findGroupCodeOrder(monthAnalysisVO);
        List<DmFocActualCostVO> itemAndAmt = new ArrayList<>();
        DmFocActualCostVO focActualCostVO = new DmFocActualCostVO();
        focActualCostVO.setGroupCnName("元器件");
        focActualCostVO.setActualCostAmt(1215112d);
        itemAndAmt.add(focActualCostVO);
        PowerMockito.doReturn(itemAndAmt).when(dmFocActualCostDao).findActualCostAmtList(monthAnalysisVO);
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostHeatmapChart(monthAnalysisVO));
    }
    @Test
    public void getIndustryCostHeatmapChartVaild3test(){
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setViewFlag("2");
        monthAnalysisVO.setGroupLevel("LV2");
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        monthAnalysisVO.setCurPage(1);
        monthAnalysisVO.setPageSize(10);
        List<String> subGroupCodeList = new ArrayList<>();
        subGroupCodeList.add("16349");
        subGroupCodeList.add("12274");
        subGroupCodeList.add("13409");
        monthAnalysisVO.setGroupCodeList(subGroupCodeList);
        monthAnalysisVO.setVersionId(91L);
        List<String> groupCodeAndName = new ArrayList<>();
        groupCodeAndName.add("16349");
        groupCodeAndName.add("12274");
        PowerMockito.doReturn(groupCodeAndName).when(dmFocActualCostDao).findGroupCnNameByCode(monthAnalysisVO);
        String groupCodeOrder = "16349,12274";
        PowerMockito.doReturn(groupCodeOrder).when(dmFocActualCostDao).findGroupCodeOrder(monthAnalysisVO);
        List<DmFocActualCostVO> itemAndAmt = new ArrayList<>();
        DmFocActualCostVO focActualCostVO = new DmFocActualCostVO();
        focActualCostVO.setGroupCnName("元器件");
        focActualCostVO.setActualCostAmt(1215112d);
        itemAndAmt.add(focActualCostVO);
        PowerMockito.doReturn(itemAndAmt).when(dmFocActualCostDao).findActualCostAmtList(monthAnalysisVO);
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostHeatmapChart(monthAnalysisVO));
    }

    @Test
    public void getIndustryCostHeatmapChartVaild4test(){
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setViewFlag("1");
        monthAnalysisVO.setGroupLevel("LV1");
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        monthAnalysisVO.setCurPage(1);
        monthAnalysisVO.setPageSize(10);
        List<String> subGroupCodeList = new ArrayList<>();
        subGroupCodeList.add("16349");
        subGroupCodeList.add("12274");
        subGroupCodeList.add("13409");
        monthAnalysisVO.setSubGroupCodeList(subGroupCodeList);
        monthAnalysisVO.setVersionId(91L);
        ResultDataVO periodYearList = new ResultDataVO();
        List<String> yearList = new ArrayList<>();
        yearList.add("2013");
        periodYearList.setData(yearList);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList);
        List<String> groupCodeAndName = new ArrayList<>();
        groupCodeAndName.add("16349");
        groupCodeAndName.add("12274");
        PowerMockito.doReturn(groupCodeAndName).when(dmFocActualCostDao).findGroupCnNameByCode(monthAnalysisVO);
        String groupCodeOrder = "16349,12274";
        PowerMockito.doReturn(groupCodeOrder).when(dmFocActualCostDao).findGroupCodeOrder(monthAnalysisVO);
        List<DmFocActualCostVO> itemAndAmt = new ArrayList<>();
        DmFocActualCostVO focActualCostVO = new DmFocActualCostVO();
        focActualCostVO.setGroupCnName("元器件");
        focActualCostVO.setActualCostAmt(1215112d);
        itemAndAmt.add(focActualCostVO);
        PowerMockito.doReturn(itemAndAmt).when(dmFocActualCostDao).findActualCostAmtList(monthAnalysisVO);
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostHeatmapChart(monthAnalysisVO));
    }

    @Test
    public void getIndustryCostHeatmapChartVaild5test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setViewFlag("1");
        monthAnalysisVO.setGroupLevel("LV1");
        ResultDataVO periodYearList = new ResultDataVO();
        List<String> yearList = new ArrayList<>();
        yearList.add("2013");
        periodYearList.setData(yearList);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        monthAnalysisVO.setCurPage(1);
        monthAnalysisVO.setPageSize(10);
        List<String> subGroupCodeList = new ArrayList<>();
        subGroupCodeList.add("16349");
        subGroupCodeList.add("12274");
        subGroupCodeList.add("13409");
        monthAnalysisVO.setSubGroupCodeList(subGroupCodeList);
        monthAnalysisVO.setVersionId(91L);
        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupName","LV1重量级团队");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostHeatmapChart(monthAnalysisVO));
        monthAnalysisVO.setIsMultipleSelect(true);
        map.put("nextGroupLevel","LV1");
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostHeatmapChart(monthAnalysisVO));
        when(dmFocActualCostDao.findMutilGroupCnNameByCode(any(MonthAnalysisVO.class))).thenReturn(Arrays.asList(new DmFocActualCostVO(), new DmFocActualCostVO()));
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostHeatmapChart(monthAnalysisVO));
        monthAnalysisVO.setCurPage(0);
        monthAnalysisVO.setPageSize(1);
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostHeatmapChart(monthAnalysisVO));
    }

    @Test
    public void detailDataExport() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsShowPriceChart(false);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setBasePeriodId(202201);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setCaliberFlag("C");
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("IAS");
        DataPermissionsVO dimensionList = getDimensionList();
        when(commonService.getDimensionList(anyString(),anyString(),anyString())).thenReturn(dimensionList);
        String groupCnName = "重量级团队LV1";
        when(commonService.getGroupCnName(any())).thenReturn(groupCnName);
        getCurrentUser();
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        Future<Integer> priceIdxTotal = new AsyncResult<>(2);
        when(asyncExportService.fillPriceIdxSheet(any(),anyInt(),anyString(),any(),any())).thenReturn(priceIdxTotal);

        Future<Integer> monthYoyTotal = new AsyncResult<>(2);
        when(asyncExportService.fillMonthYoySheet(any(),anyInt(),anyString(),any(),any())).thenReturn(monthYoyTotal);

        Future<Integer> multiIdxTotal = new AsyncResult<>(2);
        when(asyncExportService.fillMultiIndexTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(multiIdxTotal);

        Future<Integer> weightTotal = new AsyncResult<>(2);
        when(asyncExportService.fillWeightTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(weightTotal);

        Future<Integer> heapTotal = new AsyncResult<>(2);
        when(asyncExportService.fillHeapTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(heapTotal);

        Assertions.assertNotNull(monthAnalysisService.detailDataExport(monthAnalysisVO, response));
    }
    @Test
    public void detailDataExport1test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsShowPriceChart(false);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setGroupLevel("ICT");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setBasePeriodId(202201);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setCaliberFlag("C");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("IAS");
        when(commonService.getDimensionList(anyString(),anyString(),anyString())).thenReturn(getDimensionList());
        String groupCnName = "重量级团队LV1";
        when(commonService.getGroupCnName(any())).thenReturn(groupCnName);
        getCurrentUser();
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        Future<Integer> priceIdxTotal = new AsyncResult<>(2);
        when(asyncExportService.fillPriceIdxSheet(any(),anyInt(),anyString(),any(),any())).thenReturn(priceIdxTotal);

        Future<Integer> monthYoyTotal = new AsyncResult<>(2);
        when(asyncExportService.fillMonthYoySheet(any(),anyInt(),anyString(),any(),any())).thenReturn(monthYoyTotal);

        Future<Integer> multiIdxTotal = new AsyncResult<>(2);
        when(asyncExportService.fillMultiIndexTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(multiIdxTotal);

        Future<Integer> weightTotal = new AsyncResult<>(2);
        when(asyncExportService.fillWeightTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(weightTotal);

        Future<Integer> heapTotal = new AsyncResult<>(2);
        when(asyncExportService.fillHeapTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(heapTotal);

        Assertions.assertNotNull(monthAnalysisService.detailDataExport(monthAnalysisVO, response));
    }

    @Test
    public void detailDataExport2test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsShowPriceChart(false);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setFileName("月度分析-明细数据-2023-04-12 16_06_05");
        monthAnalysisVO.setGroupLevel("LV1");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("1");
        monthAnalysisVO.setBasePeriodId(202201);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setCaliberFlag("C");
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("IAS");
        DataPermissionsVO dimensionList = getDimensionList();
        when(commonService.getDimensionList(anyString(),anyString(),anyString())).thenReturn(dimensionList);
        String groupCnName = "重量级团队LV2";
        when(commonService.getGroupCnName(any())).thenReturn(groupCnName);
        getCurrentUser();
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        Future<Integer> priceIdxTotal = new AsyncResult<>(2);
        when(asyncExportService.fillPriceIdxSheet(any(),anyInt(),anyString(),any(),any())).thenReturn(priceIdxTotal);

        Future<Integer> monthYoyTotal = new AsyncResult<>(2);
        when(asyncExportService.fillMonthYoySheet(any(),anyInt(),anyString(),any(),any())).thenReturn(monthYoyTotal);

        Future<Integer> multiIdxTotal = new AsyncResult<>(2);
        when(asyncExportService.fillMultiIndexTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(multiIdxTotal);

        Future<Integer> weightTotal = new AsyncResult<>(2);
        when(asyncExportService.fillWeightTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(weightTotal);

        Future<Integer> heapTotal = new AsyncResult<>(2);
        when(asyncExportService.fillHeapTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(heapTotal);

        Assertions.assertNotNull(monthAnalysisService.detailDataExport(monthAnalysisVO, response));
    }

    @Test
    public void detailDataExport3test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsShowPriceChart(false);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setFileName("月度分析-明细数据-2023-04-12 16_06_05");
        monthAnalysisVO.setGroupLevel("LV1");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("1");
        monthAnalysisVO.setBasePeriodId(202201);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setCaliberFlag("C");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("IAS");
        DataPermissionsVO dimensionList = getDimensionList();
        when(commonService.getDimensionList(anyString(),anyString(),anyString())).thenReturn(dimensionList);
        String groupCnName = "重量级团队LV2";
        when(commonService.getGroupCnName(any())).thenReturn(groupCnName);
        getCurrentUser();
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        Future<Integer> priceIdxTotal = new AsyncResult<>(2);
        when(asyncExportService.fillPriceIdxSheet(any(),anyInt(),anyString(),any(),any())).thenReturn(priceIdxTotal);

        Future<Integer> monthYoyTotal = new AsyncResult<>(2);
        when(asyncExportService.fillMonthYoySheet(any(),anyInt(),anyString(),any(),any())).thenReturn(monthYoyTotal);

        Future<Integer> multiIdxTotal = new AsyncResult<>(2);
        when(asyncExportService.fillMultiIndexTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(multiIdxTotal);

        Future<Integer> weightTotal = new AsyncResult<>(2);
        when(asyncExportService.fillWeightTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(weightTotal);

        Future<Integer> heapTotal = new AsyncResult<>(2);
        when(asyncExportService.fillHeapTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(heapTotal);

        Assertions.assertNotNull(monthAnalysisService.detailDataExport(monthAnalysisVO, response));
    }

    @Test
    public void detailDataExport4test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsShowPriceChart(false);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setFileName("月度分析-明细数据-2023-04-12 16_06_05");
        monthAnalysisVO.setGroupLevel("LV2");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("2");
        monthAnalysisVO.setBasePeriodId(202201);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setCaliberFlag("C");
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("IAS");
        DataPermissionsVO dimensionList = getDimensionList();
        when(commonService.getDimensionList(anyString(),anyString(),anyString())).thenReturn(dimensionList);
        String groupCnName = "重量级团队LV3";
        when(commonService.getGroupCnName(any())).thenReturn(groupCnName);
        getCurrentUser();
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        Future<Integer> priceIdxTotal = new AsyncResult<>(2);
        when(asyncExportService.fillPriceIdxSheet(any(),anyInt(),anyString(),any(),any())).thenReturn(priceIdxTotal);

        Future<Integer> monthYoyTotal = new AsyncResult<>(2);
        when(asyncExportService.fillMonthYoySheet(any(),anyInt(),anyString(),any(),any())).thenReturn(monthYoyTotal);

        Future<Integer> multiIdxTotal = new AsyncResult<>(2);
        when(asyncExportService.fillMultiIndexTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(multiIdxTotal);

        Future<Integer> weightTotal = new AsyncResult<>(2);
        when(asyncExportService.fillWeightTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(weightTotal);

        Future<Integer> heapTotal = new AsyncResult<>(2);
        when(asyncExportService.fillHeapTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(heapTotal);

        Assertions.assertNotNull(monthAnalysisService.detailDataExport(monthAnalysisVO, response));
    }

    @Test
    public void detailDataExport5test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsShowPriceChart(false);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setFileName("月度分析-明细数据-2023-04-12 16_06_05");
        monthAnalysisVO.setGroupLevel("LV2");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("4");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setIndustryOrg("IAS");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setCaliberFlag("C");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("IAS");
        DataPermissionsVO dimensionList = getDimensionList();
        when(commonService.getDimensionList(anyString(),anyString(),anyString())).thenReturn(dimensionList);
        String groupCnName = "盈利颗粒度L1";
        when(commonService.getGroupCnName(any())).thenReturn(groupCnName);
        getCurrentUser();
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        Future<Integer> priceIdxTotal = new AsyncResult<>(2);
        when(asyncExportService.fillPriceIdxSheet(any(),anyInt(),anyString(),any(),any())).thenReturn(priceIdxTotal);

        Future<Integer> monthYoyTotal = new AsyncResult<>(2);
        when(asyncExportService.fillMonthYoySheet(any(),anyInt(),anyString(),any(),any())).thenReturn(monthYoyTotal);

        Future<Integer> multiIdxTotal = new AsyncResult<>(2);
        when(asyncExportService.fillMultiIndexTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(multiIdxTotal);

        Future<Integer> weightTotal = new AsyncResult<>(2);
        when(asyncExportService.fillWeightTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(weightTotal);

        Future<Integer> heapTotal = new AsyncResult<>(2);
        when(asyncExportService.fillHeapTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(heapTotal);

        Assertions.assertNotNull(monthAnalysisService.detailDataExport(monthAnalysisVO, response));
    }

    @Test
    public void detailDataExport51test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsShowPriceChart(false);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setFileName("月度分析-明细数据-2023-04-12 16_06_05");
        monthAnalysisVO.setGroupLevel("ITEM");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("4");
        monthAnalysisVO.setBasePeriodId(202201);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("135229");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setCaliberFlag("C");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("IAS");
        DataPermissionsVO dimensionList = getDimensionList();
        when(commonService.getDimensionList(anyString(),anyString(),anyString())).thenReturn(dimensionList);
        String groupCnName = "盈利颗粒度L1";
        when(commonService.getGroupCnName(any())).thenReturn(groupCnName);
        getCurrentUser();
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        Future<Integer> priceIdxTotal = new AsyncResult<>(2);
        when(asyncExportService.fillPriceIdxSheet(any(),anyInt(),anyString(),any(),any())).thenReturn(priceIdxTotal);

        Future<Integer> monthYoyTotal = new AsyncResult<>(2);
        when(asyncExportService.fillMonthYoySheet(any(),anyInt(),anyString(),any(),any())).thenReturn(monthYoyTotal);

        Future<Integer> multiIdxTotal = new AsyncResult<>(2);
        when(asyncExportService.fillMultiIndexTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(multiIdxTotal);

        Future<Integer> weightTotal = new AsyncResult<>(2);
        when(asyncExportService.fillWeightTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(weightTotal);

        Future<Integer> heapTotal = new AsyncResult<>(2);
        when(asyncExportService.fillHeapTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(heapTotal);

        Assertions.assertNotNull(monthAnalysisService.detailDataExport(monthAnalysisVO, response));
    }

    @Test
    public void detailDataExport10test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsShowPriceChart(false);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setFileName("月度分析-明细数据-2023-04-12 16_06_05");
        monthAnalysisVO.setGroupLevel("L1");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("4");
        monthAnalysisVO.setBasePeriodId(202201);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("1007");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setCaliberFlag("C");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("IAS");
        DataPermissionsVO dimensionList = getDimensionList();
        when(commonService.getDimensionList(anyString(),anyString(),anyString())).thenReturn(dimensionList);
        String groupCnName = "盈利颗粒度L2";
        when(commonService.getGroupCnName(any())).thenReturn(groupCnName);
        getCurrentUser();
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        Future<Integer> priceIdxTotal = new AsyncResult<>(2);
        when(asyncExportService.fillPriceIdxSheet(any(),anyInt(),anyString(),any(),any())).thenReturn(priceIdxTotal);

        Future<Integer> monthYoyTotal = new AsyncResult<>(2);
        when(asyncExportService.fillMonthYoySheet(any(),anyInt(),anyString(),any(),any())).thenReturn(monthYoyTotal);

        Future<Integer> multiIdxTotal = new AsyncResult<>(2);
        when(asyncExportService.fillMultiIndexTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(multiIdxTotal);

        Future<Integer> weightTotal = new AsyncResult<>(2);
        when(asyncExportService.fillWeightTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(weightTotal);

        Future<Integer> heapTotal = new AsyncResult<>(2);
        when(asyncExportService.fillHeapTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(heapTotal);

        Assertions.assertNotNull(monthAnalysisService.detailDataExport(monthAnalysisVO, response));
    }

    @Test
    public void detailDataExport6test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsShowPriceChart(false);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setFileName("月度分析-明细数据-2023-04-12 16_06_05");
        monthAnalysisVO.setGroupLevel("LV3");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("3");
        monthAnalysisVO.setBasePeriodId(202201);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("135229");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setCaliberFlag("C");
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("IAS");
        DataPermissionsVO dimensionList = getDimensionList();
        when(commonService.getDimensionList(anyString(),anyString(),anyString())).thenReturn(dimensionList);
        String groupCnName = "专项采购认证部";
        when(commonService.getGroupCnName(any())).thenReturn(groupCnName);
        getCurrentUser();
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        Future<Integer> priceIdxTotal = new AsyncResult<>(2);
        when(asyncExportService.fillPriceIdxSheet(any(),anyInt(),anyString(),any(),any())).thenReturn(priceIdxTotal);

        Future<Integer> monthYoyTotal = new AsyncResult<>(2);
        when(asyncExportService.fillMonthYoySheet(any(),anyInt(),anyString(),any(),any())).thenReturn(monthYoyTotal);

        Future<Integer> multiIdxTotal = new AsyncResult<>(2);
        when(asyncExportService.fillMultiIndexTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(multiIdxTotal);

        Future<Integer> weightTotal = new AsyncResult<>(2);
        when(asyncExportService.fillWeightTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(weightTotal);

        Future<Integer> heapTotal = new AsyncResult<>(2);
        when(asyncExportService.fillHeapTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(heapTotal);

        Assertions.assertNotNull(monthAnalysisService.detailDataExport(monthAnalysisVO, response));
    }

    @Test
    public void detailDataExport7test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsShowPriceChart(false);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setFileName("月度分析-明细数据-2023-04-12 16_06_05");
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("2");
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setIndustryOrg("IAS");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("19382");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setCaliberFlag("C");
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("IAS");
        DataPermissionsVO dimensionList = getDimensionList();
        when(commonService.getDimensionList(anyString(),anyString(),anyString())).thenReturn(dimensionList);
        String groupCnName = "品类";
        when(commonService.getGroupCnName(any())).thenReturn(groupCnName);
        getCurrentUser();
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        Future<Integer> priceIdxTotal = new AsyncResult<>(2);
        when(asyncExportService.fillPriceIdxSheet(any(),anyInt(),anyString(),any(),any())).thenReturn(priceIdxTotal);

        Future<Integer> monthYoyTotal = new AsyncResult<>(2);
        when(asyncExportService.fillMonthYoySheet(any(),anyInt(),anyString(),any(),any())).thenReturn(monthYoyTotal);

        Future<Integer> multiIdxTotal = new AsyncResult<>(2);
        when(asyncExportService.fillMultiIndexTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(multiIdxTotal);

        Future<Integer> weightTotal = new AsyncResult<>(2);
        when(asyncExportService.fillWeightTemplate2Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(weightTotal);

        Future<Integer> heapTotal = new AsyncResult<>(2);
        when(asyncExportService.fillHeapTemplate2Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(heapTotal);

        Assertions.assertNotNull(monthAnalysisService.detailDataExport(monthAnalysisVO, response));
    }
    @Test
    public void detailDataExport11test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsShowPriceChart(false);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setFileName("月度分析-明细数据-2023-04-12 16_06_05");
        monthAnalysisVO.setGroupLevel("L2");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("4");
        monthAnalysisVO.setBasePeriodId(202201);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("1007");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setCaliberFlag("C");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("IAS");
        DataPermissionsVO dimensionList = getDimensionList();
        when(commonService.getDimensionList(anyString(),anyString(),anyString())).thenReturn(dimensionList);
        String groupCnName = "专项采购认证部";
        when(commonService.getGroupCnName(any())).thenReturn(groupCnName);
        when(dataRefreshStatusDao.getDataRefrashKey()).thenReturn(1L);
        getCurrentUser();
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        Future<Integer> priceIdxTotal = new AsyncResult<>(2);
        when(asyncExportService.fillPriceIdxSheet(any(),anyInt(),anyString(),any(),any())).thenReturn(priceIdxTotal);

        Future<Integer> monthYoyTotal = new AsyncResult<>(2);
        when(asyncExportService.fillMonthYoySheet(any(),anyInt(),anyString(),any(),any())).thenReturn(monthYoyTotal);

        Future<Integer> multiIdxTotal = new AsyncResult<>(2);
        when(asyncExportService.fillMultiIndexTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(multiIdxTotal);

        Future<Integer> weightTotal = new AsyncResult<>(2);
        when(asyncExportService.fillWeightTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(weightTotal);

        Future<Integer> heapTotal = new AsyncResult<>(2);
        when(asyncExportService.fillHeapTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(heapTotal);
        Assertions.assertNotNull(monthAnalysisService.detailDataExport(monthAnalysisVO, response));

    }

    @NotNull
    private DataPermissionsVO getDimensionList() {
        DataPermissionsVO dimensionList = new DataPermissionsVO();
        Set<String> lv1DimensionSet = new HashSet<>();
        lv1DimensionSet.add("123243");
        dimensionList.setLv1DimensionSet(lv1DimensionSet);
        Set<String> lv2DimensionSet = new HashSet<>();
        lv2DimensionSet.add("1232243");
        dimensionList.setLv2DimensionSet(lv2DimensionSet);
        return dimensionList;
    }

    private void getCurrentUser() throws Exception {
        mockStatic(UserInfoUtils.class);
        String w3Accouont ="test1";
        when(UserInfoUtils.class, "getUserAccount").thenReturn(w3Accouont);
        UserVO currentUser = new UserVO();
        RoleVO currentRole = new RoleVO();
        currentRole.setRoleId(1213);
        currentRole.setRoleName("ICT_Analysis");
        currentUser.setCurrentRole(currentRole);
        when(UserInfoUtils.class, "getCurrentUser").thenReturn(currentUser);
    }

    @Test
    public void detailDataExport8test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsShowPriceChart(false);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setFileName("月度分析-明细数据-2023-04-12 16_06_05");
        monthAnalysisVO.setGroupLevel("CEG");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("4");
        monthAnalysisVO.setBasePeriodId(202201);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("135229");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setCaliberFlag("C");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("IAS");
        DataPermissionsVO dimensionList = getDimensionList();
        when(commonService.getDimensionList(anyString(),anyString(),anyString())).thenReturn(dimensionList);
        String groupCnName = "品类";
        when(commonService.getGroupCnName(any())).thenReturn(groupCnName);
        getCurrentUser();
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        Future<Integer> priceIdxTotal = new AsyncResult<>(2);
        when(asyncExportService.fillPriceIdxSheet(any(),anyInt(),anyString(),any(),any())).thenReturn(priceIdxTotal);

        Future<Integer> monthYoyTotal = new AsyncResult<>(2);
        when(asyncExportService.fillMonthYoySheet(any(),anyInt(),anyString(),any(),any())).thenReturn(monthYoyTotal);

        Future<Integer> multiIdxTotal = new AsyncResult<>(2);
        when(asyncExportService.fillMultiIndexTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(multiIdxTotal);

        Future<Integer> weightTotal = new AsyncResult<>(2);
        when(asyncExportService.fillWeightTemplate2Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(weightTotal);

        Future<Integer> heapTotal = new AsyncResult<>(2);
        when(asyncExportService.fillHeapTemplate2Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(heapTotal);

        Assertions.assertNotNull(monthAnalysisService.detailDataExport(monthAnalysisVO, response));
    }
    @Test
    public void detailDataExport9test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsShowPriceChart(false);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setFileName("月度分析-明细数据-2023-04-12 16_06_05");
        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("2");
        monthAnalysisVO.setBasePeriodId(202201);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("1007");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setCaliberFlag("C");
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("IAS");
        DataPermissionsVO dimensionList = getDimensionList();
        when(commonService.getDimensionList(anyString(),anyString(),anyString())).thenReturn(dimensionList);
        String groupCnName = "ITEM";
        when(commonService.getGroupCnName(any())).thenReturn(groupCnName);
        getCurrentUser();
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        Future<Integer> priceIdxTotal = new AsyncResult<>(2);
        when(asyncExportService.fillPriceIdxSheet(any(),anyInt(),anyString(),any(),any())).thenReturn(priceIdxTotal);

        Future<Integer> monthYoyTotal = new AsyncResult<>(2);
        when(asyncExportService.fillMonthYoySheet(any(),anyInt(),anyString(),any(),any())).thenReturn(monthYoyTotal);

        Future<Integer> multiIdxTotal = new AsyncResult<>(2);
        when(asyncExportService.fillMultiIndexTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(multiIdxTotal);

        Future<Integer> weightTotal = new AsyncResult<>(2);
        when(asyncExportService.fillWeightTemplate2Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(weightTotal);

        Assertions.assertNotNull(monthAnalysisService.detailDataExport(monthAnalysisVO, response));
    }

    @Test
    public void detailDataExport12test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsShowPriceChart(false);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setFileName("月度分析-明细数据-2023-04-12 16_06_05");
        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("4");
        monthAnalysisVO.setBasePeriodId(202201);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("1007");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setCaliberFlag("C");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("IAS");
        DataPermissionsVO dimensionList = getDimensionList();
        when(commonService.getDimensionList(anyString(),anyString(),anyString())).thenReturn(dimensionList);
        String groupCnName = "ITEM";
        when(commonService.getGroupCnName(any())).thenReturn(groupCnName);
        getCurrentUser();
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        Future<Integer> priceIdxTotal = new AsyncResult<>(2);
        when(asyncExportService.fillPriceIdxSheet(any(),anyInt(),anyString(),any(),any())).thenReturn(priceIdxTotal);

        Future<Integer> monthYoyTotal = new AsyncResult<>(2);
        when(asyncExportService.fillMonthYoySheet(any(),anyInt(),anyString(),any(),any())).thenReturn(monthYoyTotal);

        Future<Integer> multiIdxTotal = new AsyncResult<>(2);
        when(asyncExportService.fillMultiIndexTemplate1Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(multiIdxTotal);

        Future<Integer> weightTotal = new AsyncResult<>(2);
        when(asyncExportService.fillWeightTemplate2Sheet(any(),any(),anyInt(),anyString(),any())).thenReturn(weightTotal);

        Assertions.assertNotNull(monthAnalysisService.detailDataExport(monthAnalysisVO, response));
    }

    @Test

    public void detailDataExportTest0test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsShowPriceChart(false);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setIsMultipleSelect(false);
        monthAnalysisVO.setGroupLevel("ITEM");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setBasePeriodId(202201);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setCaliberFlag("C");
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("IAS");
        getCurrentUser();
        DataPermissionsVO dimensionList = getDimensionList();
        when(commonService.getDimensionList(anyString(),anyString(),anyString())).thenReturn(dimensionList);
        Future<Integer> multiIdxTotal = new AsyncResult<>(2);
        when(asyncExportService.fillMultiIndexTemplate1Sheet(any(),any(),anyInt(),any(),any())).thenReturn(multiIdxTotal);
        mockStatic(StatisticsExcelService.class);
        when(StatisticsExcelService.class, "uploadExportExcel", any(), anyInt(), any(),any()).thenReturn(new DmFoiImpExpRecordVO());
        Assertions.assertNotNull(monthAnalysisService.detailDataExport(monthAnalysisVO, response));
    }

    @Test
    public void detailDataExportTest1test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("ITEM");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        monthAnalysisVO.setBasePeriodId(202201);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setCaliberFlag("C");
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("IAS");
        Assertions.assertNotNull(monthAnalysisService.detailDataExport(monthAnalysisVO, response));
    }

    @Test
    public void detailDataExportTest2test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("ITEM");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setBasePeriodId(202201);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setCaliberFlag("C");
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("IAS");
        Assertions.assertNotNull(monthAnalysisService.detailDataExport(monthAnalysisVO, response));
    }

    @Test
    public void detailDataExportTest3test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("ITEM");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setViewFlag("0");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setCaliberFlag("C");
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("IAS");
        Assertions.assertNotNull(monthAnalysisService.detailDataExport(monthAnalysisVO, response));
    }

    @Test
    public void detailDataExportTest4test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("ITEM");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setViewFlag("0");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("IAS");
        DataPermissionsVO dimensionList = getDimensionList();
        when(commonService.getDimensionList(anyString(),anyString(),anyString())).thenReturn(dimensionList);
        Assertions.assertNotNull(monthAnalysisService.detailDataExport(monthAnalysisVO, response));
    }

    @Test
    public void detailDataExportTest5T() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("ITEM");
        monthAnalysisVO.setVersionId(91L);
        monthAnalysisVO.setBasePeriodId(202201);
        monthAnalysisVO.setViewFlag("0");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("104364");
        monthAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        monthAnalysisVO.setCaliberFlag("C");
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("IAS");
        DataPermissionsVO dimensionList = getDimensionList();
        when(commonService.getDimensionList(anyString(),anyString(),anyString())).thenReturn(dimensionList);
        Assertions.assertNotNull(monthAnalysisService.detailDataExport(monthAnalysisVO, response));
    }


    @Test
    public void detailDataExportTest6T() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setGroupLevel("ITEM");
        monthAnalysisVO.setVersionId(91L);
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.add("104364");
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setProdRndTeamCodeList(prodRndTeamCodeList);
        Assertions.assertNotNull(monthAnalysisService.detailDataExport(monthAnalysisVO, response));
    }

    @Test
    public void getMultiItemBoxListTest() {
        Long longArg = json.getLong("Long");
        MonthAnalysisVO monthAnalysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"),MonthAnalysisVO.class);
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("IAS");
        PagedResult<DmFocMonthCostIdxVO> pagedResult = JSONObject.parseObject(json.getString("PagedResult<DmFocMonthCostIdxVO>"),new TypeReference<PagedResult<DmFocMonthCostIdxVO>>(){});
        PagedResult<DmFocMonthCostIdxVO> pagedResult0 = JSONObject.parseObject(json.getString("PagedResult<DmFocMonthCostIdxVO>"),new TypeReference<PagedResult<DmFocMonthCostIdxVO>>(){});
        when(dmFocMonthCostIdxDao.findAllItemCodeForAll(any(MonthAnalysisVO.class),any(PageVO.class))).thenReturn(pagedResult);
        when(dmFocMonthCostIdxDao.findAllItemCodeForComb(any(MonthAnalysisVO.class),any(PageVO.class))).thenReturn(pagedResult0);
        when(commonService.getVersionId(any(String.class),any())).thenReturn(longArg);
        ResultDataVO result = monthAnalysisService.getMultiItemBoxList(monthAnalysisVO);
        Assertions.assertNotNull(result);
        pagedResult.getResult().forEach(obj -> obj.setCustomId(0L));
        result = monthAnalysisService.getMultiItemBoxList(monthAnalysisVO);
        Assertions.assertNotNull(result);
        monthAnalysisVO.getCombinaCodeList().clear();
        result = monthAnalysisService.getMultiItemBoxList(monthAnalysisVO);
        Assertions.assertNotNull(result);
        monthAnalysisVO.getGroupCodeList().clear();
        monthAnalysisVO.getCombinaCodeList().add("ddd");
        result = monthAnalysisService.getMultiItemBoxList(monthAnalysisVO);
        Assertions.assertNotNull(result);

        monthAnalysisVO.setCostType("M");
        monthAnalysisVO.setGroupCodeList(Arrays.asList("123"));
        monthAnalysisVO.setParentCodeList(Arrays.asList("123"));
        when(dmFocMadeMonthCostIdxDao.findAllItemCodeForAll(any(MonthAnalysisVO.class),any(PageVO.class))).thenReturn(pagedResult0);
        Assertions.assertNotNull(monthAnalysisService.getMultiItemBoxList(monthAnalysisVO));

    }

    @Test
    public void queryDataRefreshStatusTest() throws CommonApplicationException {
        VarifyTaskVO varifyTaskVOParam = JSONObject.parseObject(json.getString("VarifyTaskVO"),VarifyTaskVO.class);
        varifyTaskVOParam.setIndustryOrg("IAs");
        VarifyTaskVO varifyTaskVO = JSONObject.parseObject(json.getString("VarifyTaskVO"),VarifyTaskVO.class);
        when(iDataCipherTextDao.searchVerifyTask(any(VarifyTaskVO.class))).thenReturn(varifyTaskVO);
        ResultDataVO result = monthAnalysisService.queryDataRefreshStatus(varifyTaskVOParam);
        Assertions.assertNotNull(result);

        try {
            varifyTaskVO.setStatus("FAILED");
            monthAnalysisService.queryDataRefreshStatus(varifyTaskVOParam);
        } catch (ApplicationException e) {
            assertThatExceptionOfType(ApplicationException.class);
        }

        try {
            varifyTaskVOParam.setTaskId(-1L);
            monthAnalysisService.queryDataRefreshStatus(varifyTaskVOParam);
        } catch (ApplicationException e) {
            assertThatExceptionOfType(ApplicationException.class);
        }

        try {
            varifyTaskVOParam.setTaskId(null);
            monthAnalysisService.queryDataRefreshStatus(varifyTaskVOParam);
        } catch (ApplicationException e) {
            assertThatExceptionOfType(ApplicationException.class);
        }
    }

    @Test
    public void getIndustryCostIndexTaskStatusTest() {
        MonthAnalysisVO monthAnalysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"),MonthAnalysisVO.class);
        monthAnalysisVO.setIndustryOrg("IAS");
        when(monthCommonService.isDataIsOk(any(MonthAnalysisVO.class))).thenReturn(true);
        when(monthCommonService.isCombDataIsOk(any(MonthAnalysisVO.class))).thenReturn(true);
        when(monthCommonService.reverseDataIsOk(any(MonthAnalysisVO.class))).thenReturn(true);
        when(commonService.getVersionId(any(String.class),any())).thenReturn(1L);
        when(iDataCipherTextDao.getVerifyTaskId(any())).thenReturn(1L);
        ResultDataVO periodYearList = new ResultDataVO();
        List<String> yearList = new ArrayList<>();
        yearList.add("2013");
        periodYearList.setData(yearList);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList);
        monthAnalysisVO.setGranularityType("U");
        ResultDataVO result = monthAnalysisService.getIndustryCostIndexTaskStatus(monthAnalysisVO);
        Assertions.assertNotNull(result);

        monthAnalysisVO.setGroupLevel("LV1");
        monthAnalysisVO.setVersionId(null);
        monthAnalysisVO.getCustomIdList().clear();
        result = monthAnalysisService.getIndustryCostIndexTaskStatus(monthAnalysisVO);
        Assertions.assertNotNull(result);

        monthAnalysisVO.setGroupLevel("LV1");
        when(monthCommonService.isDataIsOk(any(MonthAnalysisVO.class))).thenReturn(false);
        result = monthAnalysisService.getIndustryCostIndexTaskStatus(monthAnalysisVO);
        Assertions.assertNotNull(result);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setGroupLevel("LV1");
        monthAnalysisVO.setViewFlag("6");
        result = monthAnalysisService.getIndustryCostIndexTaskStatus(monthAnalysisVO);
        Assertions.assertNotNull(result);
        monthAnalysisVO.setGroupLevel("LV1");
        monthAnalysisVO.setViewFlag("5");
        result = monthAnalysisService.getIndustryCostIndexTaskStatus(monthAnalysisVO);
        Assertions.assertNotNull(result);
        when(monthCommonService.reverseDataIsOk(any(MonthAnalysisVO.class))).thenReturn(false);
        monthAnalysisVO.setGroupLevel("LV1");
        monthAnalysisVO.setViewFlag("4");
        result = monthAnalysisService.getIndustryCostIndexTaskStatus(monthAnalysisVO);
        Assertions.assertNotNull(result);
        monthAnalysisVO.setGroupLevel("LV1");
        monthAnalysisVO.setGranularityType("D");
        result = monthAnalysisService.getIndustryCostIndexTaskStatus(monthAnalysisVO);
        Assertions.assertNotNull(result);
    }

    @Test
    public void getIndustryCostIndexTaskStatus1Test() {
        MonthAnalysisVO monthAnalysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"), MonthAnalysisVO.class);
        mockStatic(FcstIndexUtil.class);
        Map<String, Object> map = new HashMap<>();
        ResultDataVO periodYearList = new ResultDataVO();
        List<String> yearList = new ArrayList<>();
        yearList.add("2013");
        periodYearList.setData(yearList);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList);
        PowerMockito.when(FcstIndexUtil.getNextGroupLevel(Mockito.any(MonthAnalysisVO.class))).thenReturn(map);
        ResultDataVO result = monthAnalysisService.getIndustryCostIndexTaskStatus(monthAnalysisVO);
        Assertions.assertNotNull(result);
        map.put("nextGroupLevel", "test");
        result = monthAnalysisService.getIndustryCostIndexTaskStatus(monthAnalysisVO);
        Assertions.assertNotNull(result);
        map.put("nextGroupLevel", null);
        map.put("nextGroupName", "test");
        result = monthAnalysisService.getIndustryCostIndexTaskStatus(monthAnalysisVO);
        Assertions.assertNotNull(result);
        monthAnalysisVO.setViewFlag(null);
        result = monthAnalysisService.getIndustryCostIndexTaskStatus(monthAnalysisVO);
        Assertions.assertNotNull(result);
        monthAnalysisVO.setGroupCodeList(null);
        result = monthAnalysisService.getIndustryCostIndexTaskStatus(monthAnalysisVO);
        Assertions.assertNotNull(result);
        monthAnalysisVO.setBasePeriodId(null);
        result = monthAnalysisService.getIndustryCostIndexTaskStatus(monthAnalysisVO);
        Assertions.assertNotNull(result);
    }

    @Test
    public void getResultDataVOTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"), MonthAnalysisVO.class);
        monthAnalysisVO.setIndustryOrg("IAS");
        VarifyTaskVO varifyTaskVOParam = JSONObject.parseObject(json.getString("VarifyTaskVO"),VarifyTaskVO.class);
        varifyTaskVOParam.setIndustryOrg("IAS");
        ResultDataVO result = Whitebox.invokeMethod(monthAnalysisService, "getResultDataVO",
                monthAnalysisVO, varifyTaskVOParam, "test");
        Assertions.assertNull(result);

        when(monthCommonService.isDataIsOk(any(MonthAnalysisVO.class))).thenReturn(true);
        when(monthCommonService.isCombDataIsOk(any(MonthAnalysisVO.class))).thenReturn(true);
        result = Whitebox.invokeMethod(monthAnalysisService, "getResultDataVO",
                monthAnalysisVO, varifyTaskVOParam, "test");
        Assertions.assertNotNull(result);
        when(monthCommonService.isCombDataIsOk(any(MonthAnalysisVO.class))).thenReturn(false);
        result = Whitebox.invokeMethod(monthAnalysisService, "getResultDataVO",
                monthAnalysisVO, varifyTaskVOParam, "test");
        Assertions.assertNull(result);

        monthAnalysisVO.getCombinaCodeList().clear();
        result = Whitebox.invokeMethod(monthAnalysisService, "getResultDataVO",
                monthAnalysisVO, varifyTaskVOParam, "test");
        Assertions.assertNotNull(result);
        when(monthCommonService.isDataIsOk(any(MonthAnalysisVO.class))).thenReturn(false);
        result = Whitebox.invokeMethod(monthAnalysisService, "getResultDataVO",
                monthAnalysisVO, varifyTaskVOParam, "test");
        Assertions.assertNull(result);

        monthAnalysisVO.getGroupCodeList().clear();
        result = Whitebox.invokeMethod(monthAnalysisService, "getResultDataVO",
                monthAnalysisVO, varifyTaskVOParam, "test");
        Assertions.assertNull(result);

        monthAnalysisVO.getCombinaCodeList().add("test");
        result = Whitebox.invokeMethod(monthAnalysisService, "getResultDataVO",
                monthAnalysisVO, varifyTaskVOParam, "test");
        Assertions.assertNull(result);

        when(monthCommonService.isCombDataIsOk(any(MonthAnalysisVO.class))).thenReturn(true);
        result = Whitebox.invokeMethod(monthAnalysisService, "getResultDataVO",
                monthAnalysisVO, varifyTaskVOParam, "test");
        Assertions.assertNotNull(result);
    }

    @Test
    public void getIndustryCostMultiIndexTaskStatusTest() {
        MonthAnalysisVO monthAnalysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"),MonthAnalysisVO.class);
        monthAnalysisVO.setIndustryOrg("IAS");
        when(monthCommonService.isDataIsOk(any(MonthAnalysisVO.class))).thenReturn(true);
        when(monthCommonService.reverseDataIsOk(any(MonthAnalysisVO.class))).thenReturn(true);
        when(monthCommonService.hasCombNormalDataIsOk(any(MonthAnalysisVO.class))).thenReturn(true);
        when(monthCommonService.isCombDataIsOk(any(MonthAnalysisVO.class))).thenReturn(true);
        when(commonService.getVersionId(any(String.class),any())).thenReturn(1L);
        when(iDataCipherTextDao.getVerifyTaskId(any())).thenReturn(1L);
        ResultDataVO result = monthAnalysisService.getIndustryCostMultiIndexTaskStatus(monthAnalysisVO);
        Assertions.assertNotNull(result);
        monthAnalysisVO.setGroupLevel("LV1");
        monthAnalysisVO.getCombinaSubGroupCodeList().clear();
        monthAnalysisVO.getParentCodeList().clear();
        result = monthAnalysisService.getIndustryCostMultiIndexTaskStatus(monthAnalysisVO);
        Assertions.assertNotNull(result);
        monthAnalysisVO.setGroupLevel("LV1");
        monthAnalysisVO.getCustomIdList().clear();
        result = monthAnalysisService.getIndustryCostMultiIndexTaskStatus(monthAnalysisVO);
        Assertions.assertNotNull(result);
        ResultDataVO periodYearList = new ResultDataVO();
        List<String> yearList = new ArrayList<>();
        yearList.add("2013");
        periodYearList.setData(yearList);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList);

        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setGroupLevel("LV1");
        when(monthCommonService.isDataIsOk(any(MonthAnalysisVO.class))).thenReturn(false);
        result = monthAnalysisService.getIndustryCostMultiIndexTaskStatus(monthAnalysisVO);
        Assertions.assertNotNull(result);

        mockStatic(FcstIndexUtil.class);
        Map<String, Object> map = new HashMap<>();
        map.put("nextGroupLevel","LV1");
        map.put("nextGroupName","LV1重量级团队");
        PowerMockito.when(FcstIndexUtil.getNextGroupLevel(Mockito.any(MonthAnalysisVO.class))).thenReturn(map);
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostMultiIndexTaskStatus(monthAnalysisVO));
        monthAnalysisVO.setViewFlag("1");
        monthAnalysisVO.setIsContainComb(true);
        monthAnalysisVO.setCustomIdList(Arrays.asList(1L));
        Assertions.assertNotNull(monthAnalysisService.getIndustryCostMultiIndexTaskStatus(monthAnalysisVO));
        monthAnalysisVO.getSubGroupCodeList().clear();
        result = monthAnalysisService.getIndustryCostMultiIndexTaskStatus(monthAnalysisVO);
        Assertions.assertNotNull(result);
    }

    @Test
    public void getMutliChangePeriodIdTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"),MonthAnalysisVO.class);
        VarifyTaskVO varifyTaskVO = new VarifyTaskVO();
        monthAnalysisVO.setReverseViewFlag(true);
        ResultDataVO result = Whitebox.invokeMethod(monthAnalysisService, "getMutliChangePeriodId", monthAnalysisVO,
                varifyTaskVO, "test");
        Assertions.assertNull(result);

        when(monthCommonService.reverseDataIsOk(any(MonthAnalysisVO.class))).thenReturn(true);
        result = Whitebox.invokeMethod(monthAnalysisService, "getMutliChangePeriodId", monthAnalysisVO,
                varifyTaskVO, "test");
        Assertions.assertNotNull(result);
    }

    @Test
    public void vaildStatusAndCombStatusTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"),MonthAnalysisVO.class);
        VarifyTaskVO varifyTaskVO = JSONObject.parseObject(json.getString("VarifyTaskVO"),VarifyTaskVO.class);

        ResultDataVO result = Whitebox.invokeMethod(monthAnalysisService, "vaildStatusAndCombStatus", varifyTaskVO, "test", monthAnalysisVO);
        Assertions.assertNotNull(result);
        result = Whitebox.invokeMethod(monthAnalysisService, "vaildStatusAndCombStatus", varifyTaskVO, "test1", monthAnalysisVO);
        Assertions.assertNull(result);

        monthAnalysisVO.getSubGroupCodeList().clear();
        result = Whitebox.invokeMethod(monthAnalysisService, "vaildStatusAndCombStatus", varifyTaskVO, "test", monthAnalysisVO);
        Assertions.assertNull(result);
        result = Whitebox.invokeMethod(monthAnalysisService, "vaildStatusAndCombStatus", varifyTaskVO, "test1", monthAnalysisVO);
        Assertions.assertNull(result);

        monthAnalysisVO.getParentCodeList().clear();
        result = Whitebox.invokeMethod(monthAnalysisService, "vaildStatusAndCombStatus", varifyTaskVO, "test", monthAnalysisVO);
        Assertions.assertNull(result);
        result = Whitebox.invokeMethod(monthAnalysisService, "vaildStatusAndCombStatus", varifyTaskVO, "test1", monthAnalysisVO);
        Assertions.assertNull(result);

        monthAnalysisVO.getSubGroupCodeList().add("test");
        monthAnalysisVO.getParentCodeList().add("test");
        monthAnalysisVO.getCombinaSubGroupCodeList().clear();
        result = Whitebox.invokeMethod(monthAnalysisService, "vaildStatusAndCombStatus", varifyTaskVO, "test", monthAnalysisVO);
        Assertions.assertNull(result);
        result = Whitebox.invokeMethod(monthAnalysisService, "vaildStatusAndCombStatus", varifyTaskVO, "test1", monthAnalysisVO);
        Assertions.assertNull(result);

        monthAnalysisVO.getCustomIdList().clear();
        result = Whitebox.invokeMethod(monthAnalysisService, "vaildStatusAndCombStatus", varifyTaskVO, "test", monthAnalysisVO);
        Assertions.assertNotNull(result);
        result = Whitebox.invokeMethod(monthAnalysisService, "vaildStatusAndCombStatus", varifyTaskVO, "test1", monthAnalysisVO);
        Assertions.assertNull(result);

        monthAnalysisVO.getCombinaSubGroupCodeList().add("test");
        result = Whitebox.invokeMethod(monthAnalysisService, "vaildStatusAndCombStatus", varifyTaskVO, "test", monthAnalysisVO);
        Assertions.assertNotNull(result);
        result = Whitebox.invokeMethod(monthAnalysisService, "vaildStatusAndCombStatus", varifyTaskVO, "test1", monthAnalysisVO);
        Assertions.assertNull(result);
        varifyTaskVO.setCombStatus("dddd");
        result = Whitebox.invokeMethod(monthAnalysisService, "vaildStatusAndCombStatus", varifyTaskVO, "test", monthAnalysisVO);
        Assertions.assertNotNull(result);
        varifyTaskVO.setStatus("dddd");
        result = Whitebox.invokeMethod(monthAnalysisService, "vaildStatusAndCombStatus", varifyTaskVO, "test", monthAnalysisVO);
        Assertions.assertNull(result);
        monthAnalysisVO.getCombParentCodeList().clear();
        result = Whitebox.invokeMethod(monthAnalysisService, "vaildStatusAndCombStatus", varifyTaskVO, "test", monthAnalysisVO);
        Assertions.assertNull(result);
        monthAnalysisVO.getParentCodeList().clear();
        result = Whitebox.invokeMethod(monthAnalysisService, "vaildStatusAndCombStatus", varifyTaskVO, "test", monthAnalysisVO);
        Assertions.assertNull(result);
    }

    @Test
    public void vaildStatusAndCombStatusSubTest() throws Exception {
        VarifyTaskVO varifyTaskVO = JSONObject.parseObject(json.getString("VarifyTaskVO"),VarifyTaskVO.class);
        MonthAnalysisVO monthAnalysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"),MonthAnalysisVO.class);

        ResultDataVO result = Whitebox.invokeMethod(monthAnalysisService, "vaildStatusAndCombStatus", varifyTaskVO, "test1", monthAnalysisVO);
        Assertions.assertNull(result);

        monthAnalysisVO.getSubGroupCodeList().clear();
        monthAnalysisVO.getCombinaSubGroupCodeList().clear();
        result = Whitebox.invokeMethod(monthAnalysisService, "vaildStatusAndCombStatus", varifyTaskVO, "test1", monthAnalysisVO);
        Assertions.assertNull(result);
        monthAnalysisVO.getCombParentCodeList().clear();
        result = Whitebox.invokeMethod(monthAnalysisService, "vaildStatusAndCombStatus", varifyTaskVO, "test1", monthAnalysisVO);
        Assertions.assertNull(result);
        monthAnalysisVO.getParentCodeList().clear();
        monthAnalysisVO.getCombParentCodeList().add("test");
        result = Whitebox.invokeMethod(monthAnalysisService, "vaildStatusAndCombStatus", varifyTaskVO, "test1", monthAnalysisVO);
        Assertions.assertNull(result);
        monthAnalysisVO.getCombParentCodeList().clear();
        result = Whitebox.invokeMethod(monthAnalysisService, "vaildStatusAndCombStatus", varifyTaskVO, "test1", monthAnalysisVO);
        Assertions.assertNull(result);
        monthAnalysisVO.getParentCodeList().clear();
        result = Whitebox.invokeMethod(monthAnalysisService, "vaildStatusAndCombStatus", varifyTaskVO, "test1", monthAnalysisVO);
        Assertions.assertNull(result);
    }

    @Test
    public void changeBasePeriodIdTest() throws Exception {
        VarifyTaskVO varifyTaskVO = JSONObject.parseObject(json.getString("VarifyTaskVO"),VarifyTaskVO.class);
        MonthAnalysisVO monthAnalysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"),MonthAnalysisVO.class);
        monthAnalysisVO.setIndustryOrg("IAS");
        Whitebox.invokeMethod(monthAnalysisService, "changeBasePeriodId", varifyTaskVO, "test1", monthAnalysisVO);
        assertThatNoException();
        monthAnalysisVO.getCombinaSubGroupCodeList().clear();
        monthAnalysisVO.getSubGroupCodeList().clear();
        Whitebox.invokeMethod(monthAnalysisService, "changeBasePeriodId", varifyTaskVO, "test1", monthAnalysisVO);
        assertThatNoException();
    }

    @Test
    public void resultDataGroupByTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"),MonthAnalysisVO.class);
        List<DmFocMonthCostIdxVO> list = new ArrayList<>();
        DmFocMonthCostIdxVO cost = new DmFocMonthCostIdxVO();
        cost.setGroupCode("test");
        list.add(cost);
        monthAnalysisVO.setIsMultipleSelect(false);
        List<List<DmFocMonthCostIdxVO>> result = Whitebox.invokeMethod(monthAnalysisService,
                "resultDataGroupBy", monthAnalysisVO, list);
        Assertions.assertNotNull(result);
    }

    @Test
    public void addCombMutilPriceIndexChartListTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"),MonthAnalysisVO.class);
        List<DmFocMonthCostIdxVO> list = JSONObject.parseArray(json.getString("List<DmFocMonthCostIdxVO>"),DmFocMonthCostIdxVO.class);
        when(dmFocMonthCostIdxDao.findPriceIndexCombChartByMultiDim(any(MonthAnalysisVO.class))).thenReturn(list);
        List<DmFocMonthCostIdxVO> list1 = new ArrayList<>();
        monthAnalysisVO.getSubGroupCodeList().add("ddd_##dfd");
        monthAnalysisVO.getParentCodeList().add("ddd_##dfd");
        monthAnalysisVO.getParentCodeList().add("ddd_##null");
        Whitebox.invokeMethod(monthAnalysisService, "addCombMutilPriceIndexChartList", monthAnalysisVO, list1);
        assertThatNoException();
        monthAnalysisVO.getSubGroupCodeList().clear();
        Whitebox.invokeMethod(monthAnalysisService, "addCombMutilPriceIndexChartList", monthAnalysisVO, list1);
        assertThatNoException();
        list.forEach(obj -> obj.setParentCnName("ddd"));
        Whitebox.invokeMethod(monthAnalysisService, "addCombMutilPriceIndexChartList", monthAnalysisVO, list1);
        assertThatNoException();
        list.forEach(obj -> obj.setParentCnName(null));
        Whitebox.invokeMethod(monthAnalysisService, "addCombMutilPriceIndexChartList", monthAnalysisVO, list1);
        assertThatNoException();
    }

    @Test
    public void addCombMutilPriceIndexChartList1Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        List<String> subGroupCodeList = new ArrayList<>();
        subGroupCodeList.add("test");
        monthAnalysisVO.setSubGroupCodeList(subGroupCodeList);
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setCombinaSubGroupCodeList(subGroupCodeList);
        monthAnalysisVO.setParentCodeList(subGroupCodeList);
        List<DmFocMonthCostIdxVO> priceIndexChartList = new ArrayList<>();

        Mockito.when(dmFocMadeMonthCostIdxDao.findStartEndTime(any(),any())).thenReturn(getStringLongMap());
        Mockito.when(dmFocMonthCostIdxDao.findPriceIndexNormalChartByMultiDim(any())).thenReturn(priceIndexChartList);
        Mockito.when(dmFocMonthCostIdxDao.findPriceIndexCombChartByMultiDim(any())).thenReturn(priceIndexChartList);
        Whitebox.invokeMethod(monthAnalysisService, "addCombMutilPriceIndexChartList", monthAnalysisVO,
                priceIndexChartList);
        Assert.assertNotNull(priceIndexChartList);

        monthAnalysisVO.setCostType("M");
        Mockito.when(dmFocMadeMonthCostIdxDao.findMadePriceIndexNormalByMultiDim(any())).thenReturn(priceIndexChartList);
        Mockito.when(dmFocMadeMonthCostIdxDao.findMadePriceIndexCombByMultiDim(any())).thenReturn(priceIndexChartList);
        Whitebox.invokeMethod(monthAnalysisService, "addCombMutilPriceIndexChartList", monthAnalysisVO,
                priceIndexChartList);
        Assert.assertNotNull(priceIndexChartList);
    }

    @Test
    public void getGroupKeyTest() throws Exception {
        DmFocMonthCostIdxVO dmFocMonthCostIdxVO = new DmFocMonthCostIdxVO();
        String result = Whitebox.invokeMethod(monthAnalysisService, "getGroupKey", dmFocMonthCostIdxVO);
        Assert.assertNotNull(result);
    }

    @Test
    public void combWeightVOListTest() throws Exception {
        mockStatic(AesGcmUtil.class);
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();
        MonthAnalysisVO monthAnalysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"), MonthAnalysisVO.class);
        HashMap<String, Object> dataMap = new HashMap<>();
        List<DmFocMonthWeightVO> weightList = new ArrayList<>();
        List<DmFocMonthWeightVO> list = JSONObject.parseArray(json.getString("List<DmFocMonthWeightVO>"),DmFocMonthWeightVO.class);
        when(dmFocMonthWeightDao.findCombWeightList(any(MonthAnalysisVO.class))).thenReturn(list);
        Whitebox.invokeMethod(monthAnalysisService, "combWeightVOList", monthAnalysisVO, dataMap, weightList);
        assertThatNoException();
    }

    @Test
    public void combWeightVOList1Test() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        List<String> subGroupCodeList = new ArrayList<>();
        subGroupCodeList.add("test");
        monthAnalysisVO.setSubGroupCodeList(subGroupCodeList);
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setCombinaSubGroupCodeList(subGroupCodeList);
        monthAnalysisVO.setParentCodeList(subGroupCodeList);
        monthAnalysisVO.setCombinaCodeList(subGroupCodeList);
        HashMap<String, Object> dataMap = new HashMap<>();
        List<DmFocMonthWeightVO> weightList = new ArrayList<>();
        DmFocMonthWeightVO dmFocMonthWeightVO = new DmFocMonthWeightVO();
        dmFocMonthWeightVO.setGroupCode("code");
        dmFocMonthWeightVO.setGroupCnName("codename");
        dmFocMonthWeightVO.setGroupLevel("ITEM");
        dmFocMonthWeightVO.setWeightRate(0.1);
        weightList.add(dmFocMonthWeightVO);

        mockStatic(AesGcmUtil.class);
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();
        Whitebox.invokeMethod(monthAnalysisService, "combWeightVOList", monthAnalysisVO, dataMap,
                weightList);
        Assert.assertNotNull(weightList);

        monthAnalysisVO.setCostType("M");
        List<DmFocMonthWeightVO> weightList1 = new ArrayList<>();
        DmFocMonthWeightVO dmFocMonthWeightVO1 = new DmFocMonthWeightVO();
        dmFocMonthWeightVO1.setGroupCode("code");
        dmFocMonthWeightVO1.setGroupCnName("codename");
        dmFocMonthWeightVO1.setGroupLevel("ITEM");
        dmFocMonthWeightVO1.setWeightRate(0.1);
        weightList1.add(dmFocMonthWeightVO1);
        Whitebox.invokeMethod(monthAnalysisService, "combWeightVOList", monthAnalysisVO, dataMap,
                weightList1);
        Assert.assertNotNull(weightList1);
    }

    @Test
    public void mutilSelectHeartmapChartTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setCurPage(1);
        monthAnalysisVO.setPageSize(2);
        Map<String, Object> heatmapResult = new HashMap<>();
        List<DmFocActualCostVO> mutilGroupCnNameByCode = new ArrayList<>();
        DmFocActualCostVO dmFocActualCostVO = new DmFocActualCostVO();
        dmFocActualCostVO.setVersionId(1L);
        mutilGroupCnNameByCode.add(dmFocActualCostVO);

        Mockito.when(dmFocActualCostDao.findActualCostAmtList(any())).thenReturn(mutilGroupCnNameByCode);
        Mockito.when(dmFocActualCostDao.findMutilGroupCnNameByCode(any())).thenReturn(mutilGroupCnNameByCode);
        Whitebox.invokeMethod(monthAnalysisService, "mutilSelectHeartmapChart", monthAnalysisVO,
                heatmapResult);
        Assert.assertNotNull(heatmapResult);

        monthAnalysisVO.setCostType("M");
        Mockito.when(commonService.findActualMonthNum(any())).thenReturn(02L);
        Mockito.when(dmFocMadeActualCostDao.findMadeActualCostAmtList(any())).thenReturn(mutilGroupCnNameByCode);
        Mockito.when(dmFocMadeActualCostDao.findMadeMutilGroupCnNameByCode(any())).thenReturn(mutilGroupCnNameByCode);
        Whitebox.invokeMethod(monthAnalysisService, "mutilSelectHeartmapChart", monthAnalysisVO,
                heatmapResult);
        Assert.assertNotNull(heatmapResult);

        monthAnalysisVO.setCostType("T");
        Mockito.when(dmFocTotalActualCostDao.findTotalActualCostAmtList(any())).thenReturn(mutilGroupCnNameByCode);
        Mockito.when(dmFocTotalActualCostDao.findTotalMutilGroupCnNameByCode(any())).thenReturn(mutilGroupCnNameByCode);
        Whitebox.invokeMethod(monthAnalysisService, "mutilSelectHeartmapChart", monthAnalysisVO,
                heatmapResult);
        Assert.assertNotNull(heatmapResult);
    }

    @Test
    public void weightVOListTest() throws Exception {
        mockStatic(AesGcmUtil.class);
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();
        MonthAnalysisVO monthAnalysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"), MonthAnalysisVO.class);
        List<DmFocMonthWeightVO> list = JSONObject.parseArray(json.getString("List<DmFocMonthWeightVO>"),DmFocMonthWeightVO.class);
        HashMap<String, Object> dataMap = new HashMap<>();
        monthAnalysisVO.setIsMultipleSelect(true);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setParentLevel("CATEGORY");
        monthAnalysisVO.setGroupLevel("ITEM");
        Whitebox.invokeMethod(monthAnalysisService, "weightVOList", monthAnalysisVO, dataMap, list);
        assertThatNoException();
        list.removeIf(obj -> !obj.getCustomId().equals(10L));
        list.forEach(obj -> obj.setWeightRate(1.1));
        monthAnalysisVO.setGroupLevel("CATEGORY");
        monthAnalysisVO.setIsContainComb(false);
        Whitebox.invokeMethod(monthAnalysisService, "weightVOList", monthAnalysisVO, dataMap, list);
        assertThatNoException();
        monthAnalysisVO.setParentLevel("ITEM");
        Whitebox.invokeMethod(monthAnalysisService, "weightVOList", monthAnalysisVO, dataMap, list);
        assertThatNoException();
    }

    @Test
    public void encryptWeightRateTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        HashMap<String, Object> dataMap = new HashMap<>();
        Whitebox.invokeMethod(monthAnalysisService, "encryptWeightRate", monthAnalysisVO, null, dataMap);
        assertThatNoException();
    }

    @Test
    public void setOtherWeightValueTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        Whitebox.invokeMethod(monthAnalysisService, "setOtherWeightValue", monthAnalysisVO, new ArrayList<>(), 1.1);
        assertThatNoException();
    }

    @Test
    public void detailDataExportTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"), MonthAnalysisVO.class);
        monthAnalysisVO.setGroupLevel("ITEM");
        monthAnalysisVO.setIsShowPriceChart(true);
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setIsTotalChildChart(true);
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setViewFlag("6");
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("IAS");
        getCurrentUser();
        when(commonService.getDimensionList(anyString(),anyString(),anyString())).thenReturn(getDimensionList());
        when(asyncExportService.fillPriceIdxSheet(any(), anyInt(), any(), any(), any())).thenReturn(new AsyncResult<>(2));
        when(asyncExportService.fillMultiIndexTemplate1Sheet(any(), any(), anyInt(), any(), any())).thenReturn(new AsyncResult<>(2));
        mockStatic(StatisticsExcelService.class);
        when(StatisticsExcelService.class, "uploadExportExcel", any(), anyInt(), any(),any()).thenReturn(new DmFoiImpExpRecordVO());

        Assertions.assertNotNull(monthAnalysisService.detailDataExport(monthAnalysisVO, response));
        monthAnalysisVO.setViewFlag("5");
        Assertions.assertNotNull(monthAnalysisService.detailDataExport(monthAnalysisVO, response));
        monthAnalysisVO.setViewFlag("4");
        Assertions.assertNotNull(monthAnalysisService.detailDataExport(monthAnalysisVO, response));
        monthAnalysisVO.setViewFlag("3");
        Assertions.assertNotNull(monthAnalysisService.detailDataExport(monthAnalysisVO, response));

        monthAnalysisVO.setGranularityType("D");
        Assertions.assertNotNull(monthAnalysisService.detailDataExport(monthAnalysisVO, response));
    }

    @Test
    public void refreshIndustryIndexFunctionTest() {
        VarifyTaskVO varifyTaskVO = JSONObject.parseObject(json.getString("VarifyTaskVO"),VarifyTaskVO.class);
        MonthAnalysisVO monthAnalysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"),MonthAnalysisVO.class);
        when(iDataCipherTextDao.getVerifyTaskId(any())).thenReturn(1L);
        monthAnalysisService.refreshIndustryIndexFunction(monthAnalysisVO,varifyTaskVO);
        Mockito.verify(asyncExportService, Mockito.atLeastOnce()).refreshIndustryIndexData(any(MonthAnalysisVO.class),
                any(VarifyTaskVO.class));
    }

    @Test
    public void detailDataExportVaildTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"), MonthAnalysisVO.class);
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setIsContainComb(true);
        monthAnalysisVO.setCustomIdList(Arrays.asList(1L));
        mockNextGroupLevel();
        ResultDataVO result = monthAnalysisService.detailDataExportVaild(monthAnalysisVO);
        Assertions.assertNotNull(result);

        monthAnalysisVO.getParentCodeList().clear();
        monthAnalysisVO.getGroupCodeList().clear();
        monthAnalysisVO.getCombinaCodeList().clear();
        result = monthAnalysisService.detailDataExportVaild(monthAnalysisVO);
        Assertions.assertNotNull(result);
        monthAnalysisVO.setCustomIdList(null);
        result = monthAnalysisService.detailDataExportVaild(monthAnalysisVO);
        Assertions.assertNotNull(result);
        monthAnalysisVO.setIsContainComb(false);
        result = monthAnalysisService.detailDataExportVaild(monthAnalysisVO);
        Assertions.assertNotNull(result);
        mockStatic(FcstIndexUtil.class);
        when(FcstIndexUtil.getNextGroupLevel(any(MonthAnalysisVO.class))).thenReturn(new HashMap());
        result = monthAnalysisService.detailDataExportVaild(monthAnalysisVO);
        Assertions.assertNotNull(result);

    }

    @Test
    public void detailDataExportVaild1Test() throws CommonApplicationException {
        Map map = new HashMap();
        map.put("nextGroupLevel","LV1");
        map.put("nextGroupName","LV2");
        mockStatic(FcstIndexUtil.class);
        when(FcstIndexUtil.getNextGroupLevel(any(MonthAnalysisVO.class))).thenReturn(map);
        mockStatic(FcstIndexMadeUtil.class);
        when(FcstIndexMadeUtil.getNextGroupLevel(any(MonthAnalysisVO.class))).thenReturn(map);
        List<String> subGroupCodeList = new ArrayList<>();
        subGroupCodeList.add("test");
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setIsContainComb(true);
        monthAnalysisVO.setCustomIdList(Arrays.asList(1L));
        monthAnalysisVO.setParentCodeList(subGroupCodeList);
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setCombinaCodeList(subGroupCodeList);
        monthAnalysisVO.setGroupCodeList(subGroupCodeList);

        ResultDataVO periodYearList = new ResultDataVO();
        when(annualCommonService.getAnnualPeriodYear(anyString(),any())).thenReturn(periodYearList);
        ResultDataVO result = monthAnalysisService.detailDataExportVaild(monthAnalysisVO);
        Assertions.assertNotNull(result);

        monthAnalysisVO.setCostType("M");
        ResultDataVO result1 = monthAnalysisService.detailDataExportVaild(monthAnalysisVO);
        Assertions.assertNotNull(result1);
    }

    @Test
    public void getExportTemplateTest() throws Exception {
        MonthAnalysisVO monthAnalysisVO = JSONObject.parseObject(json.getString("MonthAnalysisVO"), MonthAnalysisVO.class);
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIsTotalChildChart(false);
        String result = Whitebox.invokeMethod(monthAnalysisService, "getExportTemplate", monthAnalysisVO);
        Assertions.assertNotNull(result);

        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setGranularityType("P");
        result = Whitebox.invokeMethod(monthAnalysisService, "getExportTemplate", monthAnalysisVO);
        Assertions.assertNotNull(result);

        monthAnalysisVO.setIsShowChildContent(false);
        result = Whitebox.invokeMethod(monthAnalysisService, "getExportTemplate", monthAnalysisVO);
        Assertions.assertNotNull(result);
    }

    @Test
    public void getExpProfitTemplateByLevelTest() throws Exception {
        String result = Whitebox.invokeMethod(monthAnalysisService, "getExpProfitTemplateByLevel", "MODL");
        Assertions.assertNotNull(result);
        result = Whitebox.invokeMethod(monthAnalysisService, "getExpProfitTemplateByLevel", "CATEGORY");
        Assertions.assertNotNull(result);
        result = Whitebox.invokeMethod(monthAnalysisService, "getExpProfitTemplateByLevel", "ITEM");
        Assertions.assertNull(result);
    }

    @Test
    public void getExpUniversalTemplateByLevelTest() throws Exception {
        String result = Whitebox.invokeMethod(monthAnalysisService, "getExpUniversalTemplateByLevel", "MODL", "1");
        Assertions.assertNotNull(result);
        result = Whitebox.invokeMethod(monthAnalysisService, "getExpUniversalTemplateByLevel", "CATEGORY", "1");
        Assertions.assertNotNull(result);
        result = Whitebox.invokeMethod(monthAnalysisService, "getExpUniversalTemplateByLevel", "LV3", "1");
        Assertions.assertNotNull(result);
        result = Whitebox.invokeMethod(monthAnalysisService, "getExpUniversalTemplateByLevel", "ITEM", "1");
        Assertions.assertNull(result);
    }

    @Test
    public void getExpDimensionTemplateByLevelTest() throws Exception {
        String result = Whitebox.invokeMethod(monthAnalysisService, "getExpDimensionTemplateByLevel", "MODL");
        Assertions.assertNotNull(result);
        result = Whitebox.invokeMethod(monthAnalysisService, "getExpDimensionTemplateByLevel", "CATEGORY");
        Assertions.assertNotNull(result);
        result = Whitebox.invokeMethod(monthAnalysisService, "getExpDimensionTemplateByLevel", "SUB_DETAIL");
        Assertions.assertNotNull(result);
    }

    @Test
    public void getCostDistributionChart() throws CommonApplicationException {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        ResultDataVO resultDataVO = monthAnalysisService.getCostDistributionChart(monthAnalysisVO);
        Assertions.assertTrue(ResultCodeEnum.PARAM_ERROR.getCode().equals(resultDataVO.getCode()));

        monthAnalysisVO.setGroupCodeList(Arrays.asList("123"));
        ResultDataVO resultDataVO1 = monthAnalysisService.getCostDistributionChart(monthAnalysisVO);
        Assertions.assertTrue(ResultCodeEnum.PARAM_ERROR.getCode().equals(resultDataVO1.getCode()));

        when(commonService.getVersionId(any(String.class),any())).thenReturn(1L);
        when(dmFocActualCostDao.findCostDistributionData(any())).thenReturn(new ArrayList<>());
        monthAnalysisVO.setViewFlag("1");
        monthAnalysisVO.setIndustryOrg("IAS");
        ResultDataVO periodYearList = new ResultDataVO();
        List<String> yearList = new ArrayList<>();
        yearList.add("2013");
        periodYearList.setData(yearList);
        when(dmFocMonthCostIdxDao.findActualMonthNum(anyString())).thenReturn(202406L);
        when(annualCommonService.getAnnualPeriodYear(anyString(),anyString())).thenReturn(periodYearList);
        Assertions.assertNotNull(monthAnalysisService.getCostDistributionChart(monthAnalysisVO));
    }

    @Test
    public void getCompareIndexChart() throws InterruptedException {
        when(monthCommonService.getCompareIndexChart(any())).thenReturn(new ResultDataVO());
        Assertions.assertNotNull(monthAnalysisService.getCompareIndexChart(new ArrayList<>()));
    }

    @Test
    public void getAmpChartList() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setCostType("P");
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setReverseViewFlag(true);
        mockNextGroupLevel();
        when(dmFocMonthCostIdxDao.findActualMonthNum(anyString())).thenReturn(202406L);
        List<DmFocMonthCostIdxVO> priceIndexChartList = new ArrayList<>();
        DmFocMonthCostIdxVO dmFocMonthCostIdxVO = new DmFocMonthCostIdxVO();
        dmFocMonthCostIdxVO.setGroupCode("123");
        priceIndexChartList.add(dmFocMonthCostIdxVO);
        when(dmFocMonthCostIdxDao.findAmpPurchasePriceIndexChart(any())).thenReturn(priceIndexChartList);
        Assertions.assertNotNull(monthAnalysisService.getAmpChartList(monthAnalysisVO, httpServletRequest));

        monthAnalysisVO.setCostType("M");
        mockMadeNextGroupLevel();
        Map<String, Long> startEndTime = new HashMap<>();
        startEndTime.put("start", 1L);
        startEndTime.put("end", 2L);
        when(dmFocMadeMonthCostIdxDao.findStartEndTime(anyString(), anyString())).thenReturn(startEndTime);
        when(dmFocMadeRecMonthCostIdxDao.findRevMadeAmpPurchasePriceIndexChart(any())).thenReturn(priceIndexChartList);
        Assertions.assertNotNull(monthAnalysisService.getAmpChartList(monthAnalysisVO, httpServletRequest));
    }

}