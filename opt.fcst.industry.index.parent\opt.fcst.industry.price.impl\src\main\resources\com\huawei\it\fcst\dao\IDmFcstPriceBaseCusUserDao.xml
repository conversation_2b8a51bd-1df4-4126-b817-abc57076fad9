<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.price.dao.IDmFcstPriceBaseCusUserDao">
    <resultMap type="com.huawei.it.fcst.industry.price.vo.drop.DmFcstBasePriceCusUserVO" id="resultMap">
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdatedate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="userId" column="user_id"/>
        <result property="roleId" column="role_id"/>
        <result property="customId" column="custom_id"/>
    </resultMap>

    <select id="getCusIdByUser" resultMap="resultMap">
        select custom_id
        from fin_dm_opt_foi.dm_fcst_price_cus_user_info_t
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='customId!=null and customId!=""'>
                and custom_id = #{customId,jdbcType=VARCHAR}
            </if>
            <if test='userId!=null and userId!=""'>
                and user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test='roleId!=null and roleId!=""'>
                and role_id = #{roleId,jdbcType=VARCHAR}
            </if>
        </trim>
    </select>

    <insert id="createDmFcstCusUserInfoDTO" parameterType="com.huawei.it.fcst.industry.price.vo.drop.DmFcstBasePriceCusUserVO">
        INSERT INTO fin_dm_opt_foi.dm_fcst_price_cus_user_info_t
        (custom_id,
         created_by,
         creation_date,
         last_updated_by,
         last_update_date,
         del_flag,
         user_id,
         role_id)
        VALUES
            (#{customId,jdbcType=NUMERIC},
             #{createdBy,jdbcType=NUMERIC},
             NOW(),
             #{lastUpdatedBy,jdbcType=NUMERIC},
             NOW(),
             #{delFlag,jdbcType=VARCHAR},
             #{userId,jdbcType=VARCHAR},
             #{roleId,jdbcType=VARCHAR}
            )
    </insert>
</mapper>