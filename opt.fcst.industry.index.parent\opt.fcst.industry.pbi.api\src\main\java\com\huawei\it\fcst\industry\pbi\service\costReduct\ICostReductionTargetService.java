/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.service.costReduct;

import com.huawei.it.fcst.industry.pbi.vo.config.CostReductVO;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.cxf.jaxrs.ext.multipart.Multipart;

import java.util.List;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.Consumes;
import javax.ws.rs.FormParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;

/**
 * ICostReductionTargetService Class
 */
@Path("/costReductionTarget")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface ICostReductionTargetService {

    /**
     * [配置管理-降成本目标-下拉框接口]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @Path("/dropDownBox")
    @POST
    ResultDataVO getCostReductDropDown(CostReductVO costReductVO) throws CommonApplicationException;



    /**
     * [配置管理-降成本目标-层级信息下拉框]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @Path("/getGroupLevelInfo")
    @POST
    ResultDataVO getGroupLevelInfo(CostReductVO costReductVO) throws CommonApplicationException;

    /**
     * 配置管理-降成本目标-查询接口
     *
     * @param pageVO pageVO params
     * @return ResultDataVO
     */
    @POST
    @Path("/page/{pageSize}/{curPage}")
    ResultDataVO getCostReductListByPage(CostReductVO costReductVO, @PathParam("") PageVO pageVO);

    /**
     * 配置管理-降成本目标-保存接口
     *
     * @param costReductVOList CostReductVOList
     * @return ResultDataVO
     */
    @POST
    @Path("/save")
    ResultDataVO saveCostReductData(List<CostReductVO> costReductVOList) throws ApplicationException;

    /**
     * 配置管理-降成本目标-删除接口
     *
     * @param costReductVOList CostReductVOList
     * @return ResultDataVO
     */
    @POST
    @Path("/delete")
    ResultDataVO deleteCostReductData(List<CostReductVO> costReductVOList) throws ApplicationException;

    /**
     * 配置管理-降成本目标-导出接口
     *
     * @param costReductVO
     * @return ResultDataVO
     */
    @POST
    @Path("/export")
    ResultDataVO exportCostReductData(CostReductVO costReductVO, @Context HttpServletResponse response)
        throws Exception;

    /**
     * 配置管理-降成本目标-导入接口
     *
     * @param versionId
     * @return ResultDataVO
     */
    @POST
    @Path("/import")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    ResultDataVO importCostReductData(@Multipart("files") Attachment attachment, @FormParam("versionId") Long versionId) throws Exception;

    /**
     * 配置管理-降成本目标-数据重复检查
     *
     * @param costReductVOList CostReductVOList
     * @return ResultDataVO
     */
    @POST
    @Path("/check")
    ResultDataVO checkCostReductData(List<CostReductVO> costReductVOList) throws ApplicationException;
}
