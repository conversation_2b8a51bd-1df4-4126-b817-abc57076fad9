/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.view;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

/**
 * ViewInfoVOTest Class
 *
 * <AUTHOR>
 * @since 2023/5/9
 */
public class ViewInfoVOTest extends BaseVOCoverUtilsTest<ViewInfoVO> {

    @Override
    protected Class<ViewInfoVO> getTClass() {
        return ViewInfoVO.class;
    }

    @Test
    public void testMethod() {
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setId(1);
        viewInfoVO.getId();
        viewInfoVO.setLv0ProdRndTeamCode("lv0");
        viewInfoVO.getLv0ProdRndTeamCode();
        viewInfoVO.setLv0ProdRnTeamName("lv0");
        viewInfoVO.getLv0ProdRnTeamName();
        viewInfoVO.setLv1ProdRndTeamCode("lv1");
        viewInfoVO.getLv1ProdRndTeamCode();
        viewInfoVO.setLv1ProdRnTeamName("lv1");
        viewInfoVO.getLv1ProdRnTeamName();
        viewInfoVO.setLv2ProdRndTeamCode("lv2");
        viewInfoVO.getLv2ProdRndTeamCode();
        viewInfoVO.setLv2ProdRnTeamName("lv2");
        viewInfoVO.getLv2ProdRnTeamName();
        viewInfoVO.setL3CegCode("l3");
        viewInfoVO.getL3CegCode();
        viewInfoVO.setL3CegName("l3");
        viewInfoVO.getL3CegName();
        viewInfoVO.setCategoryCode("code");
        viewInfoVO.getCategoryCode();
        viewInfoVO.setCategoryName("name");
        viewInfoVO.getCategoryName();
        viewInfoVO.setItemCode("item");
        viewInfoVO.getItemCode();
        viewInfoVO.setItemName("name");
        viewInfoVO.getItemName();
        viewInfoVO.setDelFlag("N");
        viewInfoVO.getDelFlag();
        viewInfoVO.setViewFlag("1");
        viewInfoVO.getViewFlag();
        viewInfoVO.getDimensionDisplayValue();
        viewInfoVO.getDimensionValue();
        viewInfoVO.getSelfParentDimensionValue();
        viewInfoVO.getLv3ProdRnTeamName();
        viewInfoVO.setGroupLevel("LV1");
        viewInfoVO.getGroupLevel();
        Assert.assertNotNull(viewInfoVO);
    }
}