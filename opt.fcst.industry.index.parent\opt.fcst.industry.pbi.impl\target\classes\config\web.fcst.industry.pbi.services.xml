<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jaxws="http://cxf.apache.org/jaxws"
	xmlns:jaxrs="http://cxf.apache.org/jaxrs"
	xsi:schemaLocation="
	 http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
     http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
     http://cxf.apache.org/jaxws http://cxf.apache.org/schemas/jaxws.xsd
     http://cxf.apache.org/jaxrs http://cxf.apache.org/schemas/jaxrs.xsd">
	<jaxrs:server id="fcstIctApiManageRest" address="/pbi">
		<jaxrs:serviceBeans>
			<ref bean="annualAmpPbiPbiService"/>
			<ref bean="replaceManagementService"/>
			<ref bean="mixManagementService"/>
			<ref bean="ictConfigManagementService"/>
			<ref bean="ictCommonService"/>
			<ref bean="codeReplacementService"/>
			<ref bean="dataPermissionService"/>
			<ref bean="summaryCalculateService"/>
			<ref bean="dropDownService"/>
			<ref bean="historyTopSpartService"/>
			<ref bean="ictMonthAnalysisService"/>
			<ref bean="pbiLtsProcessService"/>
			<ref bean="costReductionTargetService"/>
		</jaxrs:serviceBeans>
		<jaxrs:providers>
			<ref bean="jsonProvider" />
			<ref bean="errorHandlerProvider" />
		</jaxrs:providers>
	</jaxrs:server>
</beans>