<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IDmFocReplaceDropDownDao">
    <resultMap type="com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO" id="resultMap">
        <result property="id" column="id"/>
        <result property="lv0ProdRndTeamCode" column="lv0_prod_rnd_team_code"/>
        <result property="lv0ProdRdTeamCnName" column="lv0_prod_rd_team_cn_name"/>
        <result property="lv1ProdRndTeamCode" column="lv1_prod_rnd_team_code"/>
        <result property="lv1ProdRdTeamCnName" column="lv1_prod_rd_team_cn_name"/>
        <result property="lv2ProdRndTeamCode" column="lv2_prod_rnd_team_code"/>
        <result property="lv2ProdRdTeamCnName" column="lv2_prod_rd_team_cn_name"/>
        <result property="lv3ProdRndTeamCode" column="lv3_prod_rnd_team_code"/>
        <result property="lv3ProdRdTeamCnName" column="lv3_prod_rd_team_cn_name"/>
        <result property="lv0ProdListCode" column="lv0_prod_list_code"/>
        <result property="lv0ProdListCnName" column="lv0_prod_list_cn_name"/>
        <result property="prodRndTeamCode" column="prod_rnd_team_code"/>
        <result property="itemCode" column="item_code"/>
        <result property="itemCnName" column="item_cn_name"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDdate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="groupCode" column="groupCode"/>
        <result property="groupLevel" column="groupLevel"/>
        <result property="groupCnName" column="groupCnName"/>
        <result property="weightRate" column="weight_rate"/>
    </resultMap>

    <select id="totalReverseFindLv1ProdCode" resultMap="resultMap">
        SELECT DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name
        FROM  fin_dm_opt_foi.dm_foc_total_view_info_d
        WHERE view_flag = #{viewFlag,jdbcType=VARCHAR} and del_flag ='N' and caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        and oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        <if test='versionId!=null'>
            and version_id = #{versionId}
        </if>
        <if test='lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
    </select>

    <sql id="weight_rate">
        <if test='viewFlag != null and viewFlag == "1"'>
            <choose>
                <when test='nextGroupLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "2"'>
            <choose>
                <when test='nextGroupLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                    groupLevel,weight_rate
                </when>
                <when test='nextGroupLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "3"'>
            <choose>
                <when test='nextGroupLevel == "BIND"'>
                    DISTINCT replacement_group_id AS groupCode, replacement_group_id ||' '|| replacement_group_cn_name AS groupCnName, 'BIND' AS groupLevel
                </when>
                <when test='nextGroupLevel == "LV3"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName,
                    'LV3' AS groupLevel,weight_rate
                </when>
                <when test='nextGroupLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,weight_rate
                </when>
                <when test='nextGroupLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
    </sql>
    <sql id="absolute_weight">
        <if test='viewFlag != null and viewFlag == "1"'>
            <choose>
                <when test='nextGroupLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "2"'>
            <choose>
                <when test='nextGroupLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                    groupLevel,absolute_weight
                </when>
                <when test='nextGroupLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "3"'>
            <choose>
                <when test='nextGroupLevel == "BIND"'>
                    DISTINCT replacement_group_id AS groupCode, replacement_group_id ||' '|| replacement_group_cn_name AS groupCnName, 'BIND' AS groupLevel
                </when>
                <when test='nextGroupLevel == "LV3"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName,
                    'LV3' AS groupLevel,absolute_weight
                </when>
                <when test='nextGroupLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,absolute_weight
                </when>
                <when test='nextGroupLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
    </sql>

    <select id="replaceViewInfoList" resultMap="resultMap">
        SELECT
        <if test='isMultipleSelect == false'>
            <include refid="weight_rate"></include>
        </if>
        <if test='isMultipleSelect == true'>
            <include refid="absolute_weight"></include>
        </if>
        <choose>
            <when test='nextGroupLevel == "BIND" or nextGroupLevel == "LV1"'>
                from fin_dm_opt_foi.dm_foc_repl_view_info_d amp
            </when>
            <when test='nextGroupLevel == "LV3" and viewFlag !="2"'>
                from fin_dm_opt_foi.dm_foc_repl_view_info_d amp left join fin_dm_opt_foi.dm_foc_repl_annl_weight_t weight
                on amp.view_flag = weight.view_flag and amp.lv3_prod_rnd_team_code = weight.group_code
                and amp.lv2_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
            <when test='nextGroupLevel == "LV2" and viewFlag !="1"'>
                from fin_dm_opt_foi.dm_foc_repl_view_info_d amp left join fin_dm_opt_foi.dm_foc_repl_annl_weight_t weight
                on amp.view_flag = weight.view_flag and amp.lv2_prod_rnd_team_code = weight.group_code
                and amp.lv1_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
        </choose>
        WHERE amp.view_flag = #{viewFlag,jdbcType=VARCHAR} and amp.del_flag ='N' and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        <if test='lv0ProdRndTeamCodeList != null and lv0ProdRndTeamCodeList.size() > 0'>
            <foreach collection='lv0ProdRndTeamCodeList' item="code" open="AND amp.lv0_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv1ProdRndTeamCodeList != null and lv1ProdRndTeamCodeList.size() > 0'>
            <foreach collection='lv1ProdRndTeamCodeList' item="code" open="AND amp.lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv2ProdRndTeamCodeList != null and lv2ProdRndTeamCodeList.size() > 0'>
            <foreach collection='lv2ProdRndTeamCodeList' item="code" open="AND amp.lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv3ProdRndTeamCodeList != null and lv3ProdRndTeamCodeList.size() > 0'>
            <foreach collection='lv3ProdRndTeamCodeList' item="code" open="AND amp.lv3_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='versionId!=null'>
            AND amp.version_id = #{versionId}
        </if>
        <if test='nextGroupLevel!=null and nextGroupLevel!=""'>
            AND amp.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
        </if>
        <if test='dataFlag!=null and dataFlag!=""'>
            AND amp.data_flag = #{dataFlag,jdbcType=VARCHAR}
        </if>
        <if test='nextGroupLevel == "LV1" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND amp.lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='(nextGroupLevel == "LV2" or nextGroupLevel == "LV3") and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND amp.lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='isMultipleSelect == false'>
            <include refid="orderby_weight"></include>
        </if>
        <if test='isMultipleSelect == true'>
            <include refid="orderby_absweight"></include>
        </if>
    </select>
    <sql id = "orderby_weight">
        <if test='nextGroupLevel != "LV1" and nextGroupLevel != "BIND"'>
            order by weight_rate desc
        </if>
    </sql>
    <sql id = "orderby_absweight">
        <if test='nextGroupLevel != "LV1" and nextGroupLevel != "BIND"'>
            order by absolute_weight desc
        </if>
    </sql>

    <select id="viewFlagInfoList" resultMap="resultMap">
        select distinct view_flag, lv0_prod_rnd_team_code, lv0_prod_rd_team_cn_name
        from fin_dm_opt_foi.dm_foc_repl_view_info_d
        where version_id = #{versionId}
        order by view_flag
    </select>

    <select id="getReplaceLv2ProdRndTeamList" resultMap="resultMap">
        select distinct lv0_prod_rnd_team_code,lv1_prod_rnd_team_code,lv2_prod_rnd_team_code,view_flag from fin_dm_opt_foi.dm_foc_repl_view_info_d
        where VIEW_FLAG = '2' AND DEL_FLAG = 'N' and group_level ='LV2'
        <if test='versionId!=null'>
            and version_id = #{versionId}
        </if>
    </select>

</mapper>