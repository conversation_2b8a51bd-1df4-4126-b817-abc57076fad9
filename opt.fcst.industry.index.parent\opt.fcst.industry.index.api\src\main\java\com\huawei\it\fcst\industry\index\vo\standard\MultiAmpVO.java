/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.standard;

import com.huawei.it.fcst.annotations.ExportAttribute;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * MultiAmpVO Class
 *
 * <AUTHOR>
 * @since 2024/11/1
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MultiAmpVO {

    @ExportAttribute(sort = 0)
    private String costType;

    @ExportAttribute(sort = 1)
    private String groupCnName;

    @ExportAttribute(sort = 2)
    private String annualAmp0;

    @ExportAttribute(sort = 3)
    private String annualAmp1;

    @ExportAttribute(sort = 4)
    private String annualAmp2;

}
