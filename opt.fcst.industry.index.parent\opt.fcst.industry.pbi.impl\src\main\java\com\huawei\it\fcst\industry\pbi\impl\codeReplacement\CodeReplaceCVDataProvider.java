/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.codeReplacement;

import com.huawei.it.fcst.config.ConfigUtil;
import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.industry.pbi.dao.ICodeReplacementDao;
import com.huawei.it.fcst.industry.pbi.enums.IndustryConstEnum;
import com.huawei.it.fcst.export.IExcelExportDataProvider;
import com.huawei.it.fcst.industry.pbi.utils.CostReductUtils;
import com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO;
import com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO;
import com.huawei.it.fcst.export.vo.ExcelExportContext;
import com.huawei.it.jalor5.core.base.PageVO;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.inject.Inject;
import javax.inject.Named;

/**
 * 导出服务-月度偏差图
 */
@Named("IExcelExport.CodeReplaceCVDataProvider")
public class CodeReplaceCVDataProvider implements IExcelExportDataProvider {

    private static final Logger log = LoggerFactory.getLogger(CodeReplaceCVDataProvider.class);

    @Inject
    private ICodeReplacementDao iCodeReplacementDao;

    @Inject
    private CodeReplacementService codeReplacementService;

    // 普通查询
    @Override
    public List<?> getData(Serializable conditionObject, PageVO pageVO) {
        // 获取加密key
        String plainText = ConfigUtil.getInstance().get16PlainText();
        CodeReplacementExpVO dataVO = (CodeReplacementExpVO) conditionObject;
        List<CodeReplacementExpVO> spartIndexCostList = null;
        if (CostReductUtils.getSpecailRoleMark(dataVO)) {
            return spartIndexCostList;
        }
        // 选择层级可分为SPART层级与PBI维度层级，区别在于关系、替代关系与新旧SpartCode是否有值。
        String selectionLevel = dataVO.getQueryLevel();
        String isNeedBlur = dataVO.getIsNeedBlur();
        if (dataVO.getPreCus()) {
            CostReductUtils.getPreCusIndexCostList(dataVO);
            List<CodeReplacementVO> newSpartList = iCodeReplacementDao.getPreBlurSpartCVExpList(dataVO, CommonConstant.CODE_TYPE_NEW);
            List<CodeReplacementVO> oldSpartList = iCodeReplacementDao.getPreBlurSpartCVExpList(dataVO, CommonConstant.CODE_TYPE_OLD);
            List<CodeReplacementVO> newSpartAccList = iCodeReplacementDao.getPreBlurSpartCVAccExpList(dataVO,  CommonConstant.CODE_TYPE_NEW);
            List<CodeReplacementVO> oldSpartAccList = iCodeReplacementDao.getPreBlurSpartCVAccExpList(dataVO, CommonConstant.CODE_TYPE_OLD);
            spartIndexCostList = CostReductUtils.TranMapToList(
                    CostReductUtils.processCVList(newSpartList, oldSpartList),
                    CostReductUtils.processCVList(newSpartAccList, oldSpartAccList));
        } else {
            // 选择层级可分为SPART层级与PBI维度层级，区别在于关系、替代关系与新旧SpartCode是否有值。
            if (CommonConstant.SELECTION_LEVEL_SPART_LEVEL.equals(selectionLevel)) {
                // 是否虚化
                if (Boolean.TRUE.equals(Boolean.valueOf(isNeedBlur))) {
                    spartIndexCostList = getBlurSpartLevelList(dataVO, plainText);
                } else {
                    spartIndexCostList = getSpartLevelList(dataVO, plainText);
                }
            } else if (CommonConstant.SELECTION_LEVEL_TOP_SPART_LEVEL.equals(selectionLevel)) {
                // topSpart 有 4情况。 此处做按单个的虚化属性做数据查询
                if (isNeedBlur.split("\\|").length == 2) {
                    spartIndexCostList = getTopSpartLevelList(dataVO, isNeedBlur, plainText);
                }
            } else if (CommonConstant.SELECTION_LEVEL_PBI_LEVEL.equals(selectionLevel)) {
                List<CodeReplacementVO> infoList = iCodeReplacementDao.getPbiIndexCVList(dataVO, plainText);
                Map<String, List<CodeReplacementVO>> newCollect = infoList.stream()
                        .collect(Collectors.groupingBy(CodeReplacementVO::getCodeType));
                List<CodeReplacementVO> infoAccList = iCodeReplacementDao.getPbiIndexAccCVList(dataVO, plainText);
                Map<String, List<CodeReplacementVO>> oldCollect = infoAccList.stream()
                        .collect(Collectors.groupingBy(CodeReplacementVO::getCodeType));
                spartIndexCostList = CostReductUtils.TranMapToList(
                        CostReductUtils.processCVList(newCollect.get(CommonConstant.CODE_TYPE_NEW), newCollect.get(CommonConstant.CODE_TYPE_OLD)),
                        CostReductUtils.processCVList(oldCollect.get(CommonConstant.CODE_TYPE_NEW), oldCollect.get(CommonConstant.CODE_TYPE_OLD)));
            }
        }
        return spartIndexCostList;
    }

    private List<CodeReplacementExpVO> getTopSpartLevelList(CodeReplacementExpVO dataVO, String isNeedBlur, String plainText) {
        List<CodeReplacementExpVO> spartIndexCostList;
        List<CodeReplacementVO> oldSpartList = codeReplacementService.getCVDataInfo(dataVO,
                Boolean.valueOf(isNeedBlur.split("\\|")[1]), CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()),
                dataVO.getOldCustomId(), dataVO.getOldProdTeamCode(), plainText);
        List<CodeReplacementVO> newSpartList = codeReplacementService.getCVDataInfo(dataVO,
                Boolean.valueOf(isNeedBlur.split("\\|")[0]), CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()),
                dataVO.getNewCustomId(), dataVO.getNewProdTeamCode(), plainText);

        List<CodeReplacementVO> oldSpartAccList = codeReplacementService.getCVAccDataInfo(dataVO,
                Boolean.valueOf(isNeedBlur.split("\\|")[1]), CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()),
                dataVO.getOldCustomId(), dataVO.getOldProdTeamCode(), plainText);
        List<CodeReplacementVO> newSpartAccList = codeReplacementService.getCVAccDataInfo(dataVO,
                Boolean.valueOf(isNeedBlur.split("\\|")[0]), CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()),
                dataVO.getNewCustomId(), dataVO.getNewProdTeamCode(), plainText);
        spartIndexCostList = CostReductUtils.TranMapToList(
                CostReductUtils.processCVList(newSpartList, oldSpartList),
                CostReductUtils.processCVList(newSpartAccList, oldSpartAccList));
        return spartIndexCostList;
    }

    private List<CodeReplacementExpVO> getSpartLevelList(CodeReplacementExpVO dataVO, String plainText) {
        List<CodeReplacementExpVO> spartIndexCostList;
        List<CodeReplacementVO> newSpartList = iCodeReplacementDao.getSpartCVList(dataVO,
                CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()), dataVO.getNewProdTeamCode(), CommonConstant.SPART_NAME, plainText);
        List<CodeReplacementVO> oldSpartList = iCodeReplacementDao.getSpartCVList(dataVO,
                CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()), dataVO.getOldProdTeamCode(), CommonConstant.SPART_NAME, plainText);
        List<CodeReplacementVO> newSpartAccList = iCodeReplacementDao.getSpartCVAccList(dataVO,
                CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()), dataVO.getNewProdTeamCode(), CommonConstant.SPART_NAME, plainText);
        List<CodeReplacementVO> oldSpartAccList = iCodeReplacementDao.getSpartCVAccList(dataVO,
                CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()), dataVO.getOldProdTeamCode(), CommonConstant.SPART_NAME, plainText);
        spartIndexCostList = CostReductUtils.TranMapToList(
                CostReductUtils.processCVList(newSpartList, oldSpartList),
                CostReductUtils.processCVList(newSpartAccList, oldSpartAccList));
        return spartIndexCostList;
    }

    private List<CodeReplacementExpVO> getBlurSpartLevelList(CodeReplacementExpVO dataVO, String plainText) {
        List<CodeReplacementExpVO> spartIndexCostList;
        List<CodeReplacementVO> newSpartList = iCodeReplacementDao.getBlurSpartCVExpList(dataVO,
                CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()), dataVO.getCustomId(), dataVO.getNewProdTeamCode(),
                CommonConstant.CODE_TYPE_NEW, plainText);
        List<CodeReplacementVO> oldSpartList = iCodeReplacementDao.getBlurSpartCVExpList(dataVO,
                CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()), dataVO.getCustomId(), dataVO.getOldProdTeamCode(),
                CommonConstant.CODE_TYPE_OLD, plainText);
        List<CodeReplacementVO> newSpartAccList = iCodeReplacementDao.getBlurSpartCVAccExpList(dataVO,
                CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()), dataVO.getCustomId(), dataVO.getNewProdTeamCode(),
                CommonConstant.CODE_TYPE_NEW, plainText);
        List<CodeReplacementVO> oldSpartAccList = iCodeReplacementDao.getBlurSpartCVAccExpList(dataVO,
                CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()), dataVO.getCustomId(), dataVO.getOldProdTeamCode(),
                CommonConstant.CODE_TYPE_OLD, plainText);
        spartIndexCostList = CostReductUtils.TranMapToList(
                CostReductUtils.processCVList(newSpartList, oldSpartList),
                CostReductUtils.processCVList(newSpartAccList, oldSpartAccList));
        return spartIndexCostList;
    }

    @Override
    public Map<String, Object> getHeader(Serializable conditionObject, Map<String, Object> parameters) {
        ExcelExportContext context = (ExcelExportContext) conditionObject;
        CodeReplacementExpVO codeReplacementExpVO = (CodeReplacementExpVO) context.getConditionObject();
        // 自定义表头
        Map<String, Object> headMap = new HashMap<>();
        String selectionLevel = codeReplacementExpVO.getQueryLevel();
        CostReductUtils.setCommonHeaderInfo(codeReplacementExpVO, headMap);
        if (CommonConstant.SELECTION_LEVEL_SPART_LEVEL.equals(selectionLevel)) {
            String str = iCodeReplacementDao.getSpartsL1Name(codeReplacementExpVO, CostReductUtils.getSpartCodeStr(codeReplacementExpVO.getOldSpartCode()),
                    CostReductUtils.getSpartCodeStr(codeReplacementExpVO.getNewSpartCode()));
            headMap.put("title", "ICT产业-" + StringUtils.defaultString(str) + "-" + codeReplacementExpVO.getRelationDesc());
            headMap.put("maxPeriodId", iCodeReplacementDao.getMaxPeriodId(codeReplacementExpVO));
        } else if (CommonConstant.SELECTION_LEVEL_TOP_SPART_LEVEL.equals(selectionLevel)) {
            headMap.put("maxPeriodId", iCodeReplacementDao.getMonthMaxPeriodId(codeReplacementExpVO));
            headMap.put("oldSpartName", "(" + CostReductUtils.getSpartCodeStr(codeReplacementExpVO.getOldSpartCode()) + ")");
            headMap.put("newSpartName", "(" + CostReductUtils.getSpartCodeStr(codeReplacementExpVO.getNewSpartCode()) + ")");
            headMap.put("title", "ICT产业-自定义新/老编码");
        } else if (CommonConstant.SELECTION_LEVEL_PBI_LEVEL.equals(selectionLevel)) {
            if (IndustryConstEnum.GRANULARITY_TYPE.PROD.getValue().equals(codeReplacementExpVO.getGranularityType())) {
                headMap.put("maxPeriodId", iCodeReplacementDao.getMaxPeriodId(codeReplacementExpVO));
            }
            String title = CostReductUtils.setTilteName(codeReplacementExpVO);
            if (codeReplacementExpVO.getPreCus()) {
                headMap.put("title", title + "-" + CostReductUtils.setRelationDesc(codeReplacementExpVO));
            } else {
                headMap.put("title", title);
            }
        }
        return headMap;
    }

}
