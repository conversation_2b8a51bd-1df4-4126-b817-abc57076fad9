/*
 *
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.industry.index.impl.annual;

import com.huawei.it.fcst.industry.index.dao.IDmFoiImpExpRecordDao;
import com.huawei.it.fcst.industry.index.utils.UserInfoUtils;
import com.huawei.it.fcst.industry.index.vo.common.DmFoiImpExpRecordVO;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Named;

/**
 * PersonalCenterService Class
 *
 * <AUTHOR>
 * @since 2023/3/14
 */
@Named("personalCenterService")
@JalorResource(code = "personalCenterService", desc = "personal Center Service")
public class PersonalCenterService {
    @Autowired
    private IDmFoiImpExpRecordDao iDmFoiImpExpRecordDao;

    public void statisticsImportRecord(DmFoiImpExpRecordVO dmFoiImpExpRecordVO, String userId) {
        dmFoiImpExpRecordVO.setCreatedBy(userId);
        dmFoiImpExpRecordVO.setLastUpdatedBy(userId);
        dmFoiImpExpRecordVO.setOptType("IMP");
        dmFoiImpExpRecordVO.setStatus("Save");
        dmFoiImpExpRecordVO.setFileType("xlsx");
        iDmFoiImpExpRecordDao.insertStatisticsImportRecord(dmFoiImpExpRecordVO);
    }

    public void statisticsExportRecord(DmFoiImpExpRecordVO dmFoiImpExpRecordVO) {
        Long userId = UserInfoUtils.getUserId();
        dmFoiImpExpRecordVO.setCreatedBy(String.valueOf(userId));
        dmFoiImpExpRecordVO.setLastUpdatedBy(String.valueOf(userId));
        dmFoiImpExpRecordVO.setOptType("EXP");
        dmFoiImpExpRecordVO.setStatus("Save");
        dmFoiImpExpRecordVO.setFileType("xlsx");
        iDmFoiImpExpRecordDao.insertStatisticsExportRecord(dmFoiImpExpRecordVO);
    }

    public void statisticsMonthExportRecord(DmFoiImpExpRecordVO dmFoiImpExpRecordVO) {
        String userId = dmFoiImpExpRecordVO.getUserId();
        dmFoiImpExpRecordVO.setCreatedBy(userId);
        dmFoiImpExpRecordVO.setLastUpdatedBy(String.valueOf(userId));
        dmFoiImpExpRecordVO.setOptType("EXP");
        dmFoiImpExpRecordVO.setStatus("Save");
        dmFoiImpExpRecordVO.setFileType("xlsx");
        iDmFoiImpExpRecordDao.insertStatisticsExportRecord(dmFoiImpExpRecordVO);
    }

}
