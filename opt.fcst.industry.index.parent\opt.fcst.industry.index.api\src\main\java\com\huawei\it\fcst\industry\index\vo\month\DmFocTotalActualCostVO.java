/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.month;

import com.huawei.it.fcst.annotations.ExportAttribute;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * DmFocTotalActualCost Class
 *
 * <AUTHOR>
 * @since 2023/10/25
 */
@Getter
@Setter
@NoArgsConstructor
public class DmFocTotalActualCostVO implements Serializable {
    
    private static final long serialVersionUID = -6792516599981138913L;

    // 会计期
    @ExportAttribute(sort = 0, dataType = "Number")
    private Long periodId;

    // 父级中文名称
    private String parentCnName;

    // 分层级中文名称
    @ExportAttribute(sort = 1)
    private String groupCnName;

    // 采购占比
    @ExportAttribute(sort = 2)
    private String purWeight;

    // 制造占比
    @ExportAttribute(sort = 3)
    private String madeWeight;
    
    // 采购金额
    @ExportAttribute(sort = 4)
    private Double purAmt;

    // 制造金额
    @ExportAttribute(sort = 5)
    private Double madeAmt;

    // 总成本金额
    @ExportAttribute(sort = 6)
    private Double totalAmt;

    private String groupCode;
}
