<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.pbi.dao.IDmfcstIctCodeReplInfoDao">
    <resultMap type="com.huawei.it.fcst.industry.pbi.vo.replace.DmFocReplVO" id="repResultMap">
        <result property="id" column="id"/>
        <result property="versionId" column="version_id"/>
        <result property="gtsType" column="gts_type"/>
        <result property="bgCode" column="bg_code"/>
        <result property="bgCnName" column="bg_cn_name"/>
        <result property="lv1Code" column="lv1_code"/>
        <result property="lv1CnName" column="lv1_cn_name"/>
        <result property="lv2Code" column="lv2_code"/>
        <result property="lv2CnName" column="lv2_cn_name"/>
        <result property="lv3Code" column="lv3_code"/>
        <result property="lv3CnName" column="lv3_cn_name"/>
        <result property="lv4Code" column="lv4_code"/>
        <result property="lv4CnName" column="lv4_cn_name"/>
        <result property="prodCnName" column="prod_cn_name"/>
        <result property="prodCode" column="prod_code"/>
        <result property="spartCode" column="spart_code"/>
        <result property="replaceRelationName" column="replace_relation_name"/>
        <result property="replaceRelationType" column="replace_relation_type"/>
        <result property="oldSpartCode" column="old_spart_code"/>
        <result property="oldSpartDesc" column="old_spart_desc"/>
        <result property="newSpartCode" column="new_spart_code"/>
        <result property="newSpartDesc" column="new_spart_desc"/>
        <result property="relationType" column="relation_type"/>
        <result property="newSpartGtmDate" column="new_spart_gtm_date"/>
        <result property="replaceBeginDate" column="replace_begin_date"/>
        <result property="creationDate" column="creation_date"/>
        <result property="createdBy" column="created_by"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>

    </resultMap>

    <sql id="allFields">
        id,
	  version_id,
	  gts_type,
	  bg_code,
	  bg_cn_name,
	  lv1_code,
	  lv1_cn_name,
	  lv2_code,
	  lv2_cn_name,
	  lv3_code,
	  lv3_cn_name,
	  lv4_code,
	  lv4_cn_name,
      prod_code,
	  prod_cn_name,
	  replace_relation_name,
	  replace_relation_type,
	  old_spart_code,
	  old_spart_desc,
	  new_spart_code,
	  new_spart_desc,
	  relation_type,
      to_char(new_spart_gtm_date,'yyyy-mm-dd') as new_spart_gtm_date,
      to_char(replace_begin_date,'yyyy-mm-dd') as replace_begin_date,
	  created_by,
	  creation_date,
	  last_updated_by,
	  last_update_date,
	  del_flag
    </sql>

    <sql id="allOriginFields">
        id,
	  version_id,
	  gts_type,
	  bg_code,
	  bg_cn_name,
	  lv1_code,
	  lv1_cn_name,
	  lv2_code,
	  lv2_cn_name,
	  lv3_code,
	  lv3_cn_name,
	  lv4_code,
	  lv4_cn_name,
      prod_code,
	  prod_cn_name,
	  replace_relation_name,
	  replace_relation_type,
	  old_spart_code,
	  old_spart_desc,
	  new_spart_code,
	  new_spart_desc,
	  relation_type,
      new_spart_gtm_date,
      replace_begin_date,
	  created_by,
	  creation_date,
	  last_updated_by,
	  last_update_date,
	  del_flag
    </sql>

    <select id="findReplInfoDropDownList" resultMap="repResultMap">
        select
        <if test='column == "BG"'>
            DISTINCT bg_code,bg_cn_name
        </if>
        <if test='column == "LV1"'>
            DISTINCT lv1_code,lv1_cn_name
        </if>
        <if test='column == "LV2"'>
            DISTINCT lv2_code,lv2_cn_name
        </if>
        <if test='column == "LV3"'>
            DISTINCT lv3_code,lv3_cn_name
        </if>
        <if test='column == "LV4"'>
            DISTINCT lv4_code,lv4_cn_name
        </if>
        <if test='column == "replaceRelationType"'>
            DISTINCT replace_relation_type
        </if>
        <if test='column == "replaceRelationName"'>
            DISTINCT replace_relation_name
        </if>
        <if test='column == "relationType"'>
            DISTINCT relation_type
        </if>
        from fin_dm_opt_foi.DM_FCST_ICT_CODE_REPL_INFO_T
        where del_flag = 'N'
        <if test='versionId != null'>
            and version_id = #{versionId}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='lv1Code != null and lv1Code != ""'>
            and lv1_code = #{lv1Code,jdbcType=VARCHAR}
        </if>
        <if test='lv2Code != null and lv2Code != ""'>
            and lv2_code = #{lv2Code,jdbcType=VARCHAR}
        </if>
        <if test='lv3Code != null and lv3Code != ""'>
            and lv3_code = #{lv3Code,jdbcType=VARCHAR}
        </if>
        <if test='lv4Code != null and lv4Code != ""'>
            and lv4_code = #{lv4Code,jdbcType=VARCHAR}
        </if>
        <if test='oldSpartCode != null and oldSpartCode != ""'>
            and old_spart_code = #{oldSpartCode,jdbcType=VARCHAR}
        </if>
        <if test='newSpartCode != null and newSpartCode != ""'>
            and new_spart_code = #{newSpartCode,jdbcType=VARCHAR}
        </if>
        <if test='relationType != null and relationType != ""'>
            and relation_type = #{relationType,jdbcType=VARCHAR}
        </if>
        <if test='column != null and column != "" and column =="LV2"'>
            and lv2_code is not null
        </if>
        <if test='column != null and column != "" and column =="LV3"'>
            and lv3_code is not null
        </if>
        <if test='column != null and column != "" and column =="LV4"'>
            and lv4_code is not null
        </if>
    </select>

    <select id="findAllRelationName" resultType="java.lang.String">
        select DISTINCT replace_relation_name
        from fin_dm_opt_foi.DM_FCST_ICT_CODE_REPL_INFO_T
        where del_flag = 'N'
        <if test='versionId != null'>
            and version_id = #{versionId}
        </if>
        <if test='replaceRelationNameList != null and replaceRelationNameList.size() > 0'>
            <foreach collection='replaceRelationNameList' item="name" open="AND replace_relation_name not IN (" close=")" index="index"
                     separator=",">
                #{name}
            </foreach>
        </if>
    </select>

    <select id="findProductProdCode" resultMap="repResultMap">
        select distinct
        prod_cn_name,prod_code
        from dmdim.dm_dim_product_d
        where del_flag = 'N'
        <if test='prodCnNameList != null and prodCnNameList.size() > 0'>
            <foreach collection='prodCnNameList' item="name" open="and prod_cn_name IN (" close=")" index="index" separator=",">
                #{name}
            </foreach>
        </if>
    </select>

    <select id="findReplInfoList" resultMap="repResultMap">
        select <include refid="allFields"/>
        from fin_dm_opt_foi.DM_FCST_ICT_CODE_REPL_INFO_T
        where del_flag = 'N'
        <if test='versionId != null'>
            and version_id = #{versionId}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='lv1Code != null and lv1Code != ""'>
            and lv1_code = #{lv1Code,jdbcType=VARCHAR}
        </if>
        <if test='lv2Code != null and lv2Code != ""'>
            and lv2_code = #{lv2Code,jdbcType=VARCHAR}
        </if>
        <if test='lv3Code != null and lv3Code != ""'>
            and lv3_code = #{lv3Code,jdbcType=VARCHAR}
        </if>
        <if test='lv4Code != null and lv4Code != ""'>
            and lv4_code = #{lv4Code,jdbcType=VARCHAR}
        </if>
        <if test='oldSpartCode != null and oldSpartCode != ""'>
            and old_spart_code = #{oldSpartCode,jdbcType=VARCHAR}
        </if>
        <if test='newSpartCode != null and newSpartCode != ""'>
            and new_spart_code = #{newSpartCode,jdbcType=VARCHAR}
        </if>
        <if test='relationType != null and relationType != ""'>
            and relation_type = #{relationType,jdbcType=VARCHAR}
        </if>
        <if test='replaceRelationType != null and replaceRelationType != ""'>
            and replace_relation_type = #{replaceRelationType,jdbcType=VARCHAR}
        </if>
        <if test='replaceRelationName != null and replaceRelationName != ""'>
            and replace_relation_name = #{replaceRelationName,jdbcType=VARCHAR}
        </if>
        <if test='replaceRelationNameList != null and replaceRelationNameList.size() > 0'>
            <foreach collection='replaceRelationNameList' item="name" open="AND replace_relation_name not in (" close=")" index="index"
                     separator=",">
                #{name}
            </foreach>
        </if>
    </select>

    <select id="findInfoPageList" resultMap="repResultMap">
        select
        <include refid="allFields"/>
        from fin_dm_opt_foi.DM_FCST_ICT_CODE_REPL_INFO_T
        where del_flag = 'N'
        <if test='searchVO.versionId != null'>
            and version_id = #{searchVO.versionId}
        </if>
        <if test='searchVO.bgCode != null and searchVO.bgCode != ""'>
            and bg_code = #{searchVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.lv1Code != null and searchVO.lv1Code != ""'>
            and lv1_code = #{searchVO.lv1Code,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.lv2Code != null and searchVO.lv2Code != ""'>
            and lv2_code = #{searchVO.lv2Code,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.lv3Code != null and searchVO.lv3Code != ""'>
            and lv3_code = #{searchVO.lv3Code,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.lv4Code != null and searchVO.lv4Code != ""'>
            and lv4_code = #{searchVO.lv4Code,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.relationType != null and searchVO.relationType != ""'>
            and relation_type = #{searchVO.relationType,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.replaceRelationType != null and searchVO.replaceRelationType != ""'>
            and replace_relation_type = #{searchVO.replaceRelationType,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.replaceRelationName != null and searchVO.replaceRelationName != ""'>
            and replace_relation_name = #{searchVO.replaceRelationName,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.oldSpartCode != null and searchVO.oldSpartCode != ""'>
            and old_spart_code like CONCAT(CONCAT( '%', UPPER (#{searchVO.oldSpartCode,jdbcType=VARCHAR})) ,'%')
        </if>
        <if test='searchVO.newSpartCode != null and searchVO.newSpartCode != ""'>
            and new_spart_code like CONCAT(CONCAT( '%', UPPER (#{searchVO.newSpartCode,jdbcType=VARCHAR})) ,'%')
        </if>
        order by bg_cn_name
        LIMIT #{pageVO.pageSize} OFFSET #{pageVO.mysqlStartIndex}
    </select>

    <select id="findInfoPageListCount" resultType="int">
        select count(1)
        from fin_dm_opt_foi.DM_FCST_ICT_CODE_REPL_INFO_T
        where del_flag = 'N'
        <if test='searchVO.versionId != null'>
            and version_id = #{searchVO.versionId}
        </if>
        <if test='searchVO.bgCode != null and searchVO.bgCode != ""'>
            and bg_code = #{searchVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.lv1Code != null and searchVO.lv1Code != ""'>
            and lv1_code = #{searchVO.lv1Code,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.lv2Code != null and searchVO.lv2Code != ""'>
            and lv2_code = #{searchVO.lv2Code,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.lv3Code != null and searchVO.lv3Code != ""'>
            and lv3_code = #{searchVO.lv3Code,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.lv4Code != null and searchVO.lv4Code != ""'>
            and lv4_code = #{searchVO.lv4Code,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.relationType != null and searchVO.relationType != ""'>
            and relation_type = #{searchVO.relationType,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.replaceRelationType != null and searchVO.replaceRelationType != ""'>
            and replace_relation_type = #{searchVO.replaceRelationType,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.replaceRelationName != null and searchVO.replaceRelationName != ""'>
            and replace_relation_name = #{searchVO.replaceRelationName,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.oldSpartCode != null and searchVO.oldSpartCode != ""'>
            and old_spart_code like CONCAT(CONCAT( '%', UPPER (#{searchVO.oldSpartCode,jdbcType=VARCHAR})) ,'%')
        </if>
        <if test='searchVO.newSpartCode != null and searchVO.newSpartCode != ""'>
            and new_spart_code like CONCAT(CONCAT( '%', UPPER (#{searchVO.newSpartCode,jdbcType=VARCHAR})) ,'%')
        </if>
    </select>

    <select id="findSpartCodePageList" resultMap="repResultMap">
        SELECT DISTINCT spart_code from fin_dm_opt_foi.dwl_prod_prod_unit_i
        where 1=1
        <if test='searchVO.keyword != null and searchVO.keyword != ""'>
            AND spart_code LIKE '%'||#{searchVO.keyword} ||'%'
        </if>
        order by spart_code
        LIMIT #{pageVO.pageSize} OFFSET #{pageVO.mysqlStartIndex}
    </select>

    <select id="findSpartCodePageListCount" resultType="java.lang.Integer">
        SELECT count(1) from ( select DISTINCT spart_code from fin_dm_opt_foi.dwl_prod_prod_unit_i
        where 1=1
        <if test='searchVO.keyword != null and searchVO.keyword != ""'>
            AND spart_code LIKE '%'||#{searchVO.keyword} ||'%'
        </if>
        )
    </select>

    <select id="getReplAutoKey" resultType="java.lang.Long" flushCache="true" useCache="false">
        select nextval('dm_fcst_ict_code_repl_info_s')
    </select>

    <insert id="createDmCodeReplInfoList">
        INSERT INTO fin_dm_opt_foi.DM_FCST_ICT_CODE_REPL_INFO_T
        (<include refid="allOriginFields"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id},
            #{item.versionId},
            #{item.gtsType,jdbcType=VARCHAR},
            #{item.bgCode,jdbcType=VARCHAR},
            #{item.bgCnName,jdbcType=VARCHAR},
            #{item.lv1Code,jdbcType=VARCHAR},
            #{item.lv1CnName,jdbcType=VARCHAR},
            #{item.lv2Code,jdbcType=VARCHAR},
            #{item.lv2CnName,jdbcType=VARCHAR},
            #{item.lv3Code,jdbcType=VARCHAR},
            #{item.lv3CnName,jdbcType=VARCHAR},
            #{item.lv4Code,jdbcType=VARCHAR},
            #{item.lv4CnName,jdbcType=VARCHAR},
            #{item.prodCode,jdbcType=VARCHAR},
            #{item.prodCnName,jdbcType=VARCHAR},
            #{item.replaceRelationName,jdbcType=VARCHAR},
            #{item.replaceRelationType,jdbcType=VARCHAR},
            #{item.oldSpartCode,jdbcType=VARCHAR},
            #{item.oldSpartDesc,jdbcType=VARCHAR},
            #{item.newSpartCode,jdbcType=VARCHAR},
            #{item.newSpartDesc,jdbcType=VARCHAR},
            #{item.relationType,jdbcType=VARCHAR},
            #{item.newSpartGtmDate,jdbcType=TIMESTAMP},
            #{item.replaceBeginDate,jdbcType=TIMESTAMP},
            #{item.createdBy,jdbcType=VARCHAR},
            #{item.creationDate,jdbcType=TIMESTAMP},
            #{item.lastUpdatedBy,jdbcType=VARCHAR},
            #{item.lastUpdateDate,jdbcType=TIMESTAMP},
            #{item.delFlag,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="findProductDimList" resultMap="repResultMap">
        select distinct
        <if test='groupLevel != null and groupLevel != "" and groupLevel == "LV1"'>
            lv1_prod_list_code as lv1_code,
            lv1_prod_list_cn_name as lv1_cn_name
        </if>
        <if test='groupLevel != null and groupLevel != "" and groupLevel == "LV2"'>
            lv2_prod_list_code as lv2_code,
            lv2_prod_list_cn_name as lv2_cn_name
        </if>
        <if test='groupLevel != null and groupLevel != "" and groupLevel == "LV3"'>
            lv3_prod_list_code as lv3_code,
            lv3_prod_list_cn_name as lv3_cn_name
        </if>
        <if test='groupLevel != null and groupLevel != "" and groupLevel == "LV4"'>
            lv4_prod_list_code as lv4_code,
            lv4_prod_list_cn_name as lv4_cn_name
        </if>
        from dmdim.dm_dim_product_d
        where del_flag = 'N'
        <if test='bgCode != null and bgCode != ""'>
            and lv0_prod_list_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='lv1Code != null and lv1Code != ""'>
            and lv1_prod_list_code = #{lv1Code,jdbcType=VARCHAR}
        </if>
        <if test='lv2Code != null and lv2Code != ""'>
            and lv2_prod_list_code = #{lv2Code,jdbcType=VARCHAR}
        </if>
        <if test='lv3Code != null and lv3Code != ""'>
            and lv3_prod_list_code = #{lv3Code,jdbcType=VARCHAR}
        </if>
        <if test='lv4Code != null and lv4Code != ""'>
            and lv4_prod_list_code = #{lv4Code,jdbcType=VARCHAR}
        </if>
    </select>



</mapper>