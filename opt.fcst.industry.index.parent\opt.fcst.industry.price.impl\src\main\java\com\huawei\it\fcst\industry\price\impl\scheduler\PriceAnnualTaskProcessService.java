/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.impl.scheduler;

import com.huawei.it.fcst.enums.GroupTaskType;
import com.huawei.it.fcst.industry.price.dao.IDmPriceVirtualizedTaskDao;
import com.huawei.it.fcst.industry.price.vo.drop.DmFcstBasePriceCusDimVO;
import com.huawei.it.jalor5.core.annotation.NoJalorTransation;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.Serializable;

import javax.inject.Inject;

/**
 * 年度func执行
 *
 * <AUTHOR>
 * @since 202407
 */
@Slf4j
@Service("task.priceAnnualTaskProcessService")
public class PriceAnnualTaskProcessService extends AbstractTaskProcessService {
    @Inject
    private IDmPriceVirtualizedTaskDao virtualizedTaskDao;
    @Override
    public GroupTaskType getTaskType() {
        return GroupTaskType.ANNUAL;
    }

    @Override
    @NoJalorTransation
    public Boolean process(Serializable serializable,Serializable beforeResult) {
        DmFcstBasePriceCusDimVO dmFcstBaseCusDimVO = (DmFcstBasePriceCusDimVO)serializable;
        log.info("AnnualTaskProcessService:start->{},虚化id:{}",dmFcstBaseCusDimVO.getPageType(),dmFcstBaseCusDimVO.getCustomId());
        return FUNC_STATUS_SUCCESS.equalsIgnoreCase(virtualizedTaskDao.callAnnualFuncTask(dmFcstBaseCusDimVO));
    }
}
