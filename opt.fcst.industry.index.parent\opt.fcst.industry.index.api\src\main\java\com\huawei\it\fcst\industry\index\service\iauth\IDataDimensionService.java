/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.service.iauth;

import com.huawei.it.fcst.industry.index.vo.view.ProcessApprover;
import com.huawei.it.fcst.industry.index.vo.view.ViewInfoVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import java.util.List;
import java.util.Map;

/**
 * 功能描述iauth权限对接接口
 *
 * <AUTHOR>
 * @since 2023年03月10日
 */
@Path("dimension")
@Consumes(MediaType.APPLICATION_JSON + ";charset=UTF-8")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface IDataDimensionService {

    /**
     * 维度树型上报
     *
     * @return
     */
    @Path("getDimensionWithTree")
    @POST
    List<ViewInfoVO> getDimensionWithTree(List<String> parameter)throws CommonApplicationException;

    /**
     * ICT经管维度树型上报
     *
     * @return
     */
    @Path("getProdDimensionWithTree")
    @POST
    List<ViewInfoVO> getProdDimensionWithTree(List<String> parameter)throws CommonApplicationException;

    /**
     * 定价维度树型上报
     *
     * @return
     */
    @Path("getPriceDimensionWithTree")
    @POST
    List<ViewInfoVO> getPriceDimensionWithTree(List<String> parameter)throws CommonApplicationException;

    /**
     * 获取成本类型
     *
     * @return
     */
    @Path("getCostType")
    @POST
    List<ViewInfoVO> getCostType(List<String> parameter)throws CommonApplicationException;

    /**
     * 获取PSP成本类型
     *
     * @return
     */
    @Path("getPspCostType")
    @POST
    List<ViewInfoVO> getPspCostType(List<String> parameter)throws CommonApplicationException;

    /**
     * 获取维度树型上报查询
     * @param viewInfoVO 查询条件
     * @param pageVO 分页对象
     * @return 数据分页对象
     * @throws CommonApplicationException 运行时异常
     */
    @Path("getDimensionWithTree/page/{pageSize}/{curPage}")
    @GET
    PagedResult<ViewInfoVO> getDimensionWithTree(@QueryParam("") ViewInfoVO viewInfoVO, @PathParam("") PageVO pageVO) throws CommonApplicationException;

    /**
     * 获取销售目录维度树型上报查询
     * @param viewInfoVO 查询条件
     * @param pageVO 分页对象
     * @return 数据分页对象
     * @throws CommonApplicationException 运行时异常
     */
    @Path("getProdDimensionWithTree/page/{pageSize}/{curPage}")
    @GET
    PagedResult<ViewInfoVO> getProdDimensionWithTree(@QueryParam("") ViewInfoVO viewInfoVO, @PathParam("") PageVO pageVO) throws CommonApplicationException;

    /**
     * 获取定价维度树型上报查询
     * @param viewInfoVO 查询条件
     * @param pageVO 分页对象
     * @return 数据分页对象
     * @throws CommonApplicationException 运行时异常
     */
    @Path("getPriceDimensionWithTree/page/{pageSize}/{curPage}")
    @GET
    PagedResult<ViewInfoVO> getPriceDimensionWithTree(@QueryParam("") ViewInfoVO viewInfoVO, @PathParam("") PageVO pageVO) throws CommonApplicationException;

    /**
     * 获取销售目录维度树型上报查询
     * @param viewInfoVO 查询条件
     * @param pageVO 分页对象
     * @return 数据分页对象
     * @throws ApplicationException 运行时异常
     */
    @Path("getCostType/page/{pageSize}/{curPage}")
    @GET
    PagedResult<ViewInfoVO> getCostType(@QueryParam("")ViewInfoVO viewInfoVO , @PathParam("") PageVO pageVO) throws CommonApplicationException;

    /**
     * 获取PSP成本类型分页查询
     * @param viewInfoVO 查询条件
     * @param pageVO 分页对象
     * @return 数据分页对象
     * @throws ApplicationException 运行时异常
     */
    @Path("getPspCostType/page/{pageSize}/{curPage}")
    @GET
    PagedResult<ViewInfoVO> getPspCostType(@QueryParam("")ViewInfoVO viewInfoVO , @PathParam("") PageVO pageVO) throws CommonApplicationException;

    /**
     * 国内/海外，地区部代表处-树结构
     *
     * @return
     */
    @Path("/getLocationWithTree")
    @POST
    List<ViewInfoVO> getLocationWithTree(List<String> parameter)throws ApplicationException;

    /**
     * 国内/海外，地区部代表处-分页查询
     *
     * @return
     */
    @Path("/getLocationWithTree/page/{pageSize}/{curPage}")
    @GET
    PagedResult<ViewInfoVO> getLocationWithTree(@QueryParam("")ViewInfoVO viewInfoVO , @PathParam("") PageVO pageVO)throws CommonApplicationException;

    /**
     * 定价-国内/海外，地区部代表处-树结构
     *
     * @param parameter 请求参数
     * @return List 国内/海外、地区部和代表处的列表
     * @throws CommonApplicationException Application Exception
     */
    @Path("/getPriceLocationWithTree")
    @POST
    List<ViewInfoVO> getPriceLocationWithTree(List<String> parameter)throws CommonApplicationException;

    /**
     * 定价-国内/海外，地区部代表处-分页查询
     *
     * @param viewInfoVO 请求参数
     * @param pageVO 分页参数
     * @return PagedResult 分页结果集
     * @throws CommonApplicationException Application Exception
     */
    @Path("/getPriceLocationWithTree/page/{pageSize}/{curPage}")
    @GET
    PagedResult<ViewInfoVO> getPriceLocationWithTree(@QueryParam("")ViewInfoVO viewInfoVO , @PathParam("") PageVO pageVO)throws CommonApplicationException;

    /**
     * 定价-大T系统部、子网系统部-树结构
     *
     * @param parameter 请求参数
     * @return List 大T系统部、子网系统部列表
     * @throws CommonApplicationException Application Exception
     */
    @Path("/getKeyAndSubAccountWithTree")
    @POST
    List<ViewInfoVO> getKeyAndSubAccountWithTree(List<String> parameter)throws CommonApplicationException;

    /**
     * 定价-大T系统部、子网系统部-分页查询
     *
     * @param viewInfoVO 请求参数
     * @param pageVO 分页参数
     * @return PagedResult 分页结果集
     * @throws CommonApplicationException Application Exception
     */
    @Path("/getKeyAndSubAccountWithTree/page/{pageSize}/{curPage}")
    @GET
    PagedResult<ViewInfoVO> getKeyAndSubAccountWithTree(@QueryParam("")ViewInfoVO viewInfoVO , @PathParam("") PageVO pageVO)throws CommonApplicationException;

    List<ViewInfoVO> getCurrentLv2ProdRndTeamList(String costType, String tablePreFix);


    /**
     * 依据数据范围设置流程审批人
     * @param processApprover ProcessApprover
     * @return Map
     * @throws CommonApplicationException 运行时异常
     */
    @Path("getApproverList")
    @POST
    Map approverBasedOnDatascope(ProcessApprover processApprover);

    /**
     * 依据数据范围设置流程审批人
     * @param processApprover ProcessApprover
     * @return Map
     * @throws ApplicationException 运行时异常
     */
    @Path("getThirdApproverList")
    @POST
    Map approverThirdBasedOnDatascope(ProcessApprover processApprover);
}
