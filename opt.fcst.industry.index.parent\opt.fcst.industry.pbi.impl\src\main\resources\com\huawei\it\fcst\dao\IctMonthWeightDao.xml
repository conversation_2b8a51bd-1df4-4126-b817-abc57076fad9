<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.pbi.dao.IctMonthWeightDao">
    <resultMap type="com.huawei.it.fcst.industry.pbi.vo.month.IctMonthAnalysisVO" id="resultMap">
        <result property="basePeriodId" column="base_period_id"/>
        <result property="versionId" column="version_id"/>
        <result property="customId" column="custom_id"/>
        <result property="combId" column="comb_id"/>
        <result property="customCnName" column="custom_cn_name"/>
        <result property="granularityType" column="granularity_type"/>
        <result property="periodYear" column="period_year"/>
        <result property="periodId" column="period_id"/>
        <result property="prodRndTeamCode" column="prod_rnd_team_code"/>
        <result property="prodRndTeamCnName" column="prod_rd_team_cn_name"/>
        <result property="prodListCode" column="prod_list_code"/>
        <result property="prodListCnName" column="prod_list_cn_name"/>
        <result property="industryCatgCode" column="industry_catg_code"/>
        <result property="industryCatgCnName" column="industry_catg_cn_name"/>
        <result property="dimensionCode" column="dimension_code"/>
        <result property="dimensionCnName" column="dimension_cn_name"/>
        <result property="dimensionSubCategoryCode" column="dimension_subcategory_code"/>
        <result property="dimensionSubCategoryCnName" column="dimension_subcategory_cn_name"/>
        <result property="dimensionSubDetailCode" column="dimension_sub_detail_code"/>
        <result property="dimensionSubDetailCnName" column="dimension_sub_detail_cn_name"/>
        <result property="groupCode" column="group_code"/>
        <result property="groupCnName" column="group_cn_name"/>
        <result property="groupLevel" column="group_level"/>
        <result property="weightRate" column="weight_rate"/>
        <result property="weightPercent" column="weight_percent"/>
        <result property="parentCode" column="parent_code"/>
        <result property="parentCnName" column="parent_cn_name"/>
        <result property="parentLevel" column="parent_level"/>
        <result property="mainFlag" column="main_flag"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="appendFlag" column="append_flag"/>
        <result property="regionCode" column="region_code"/>
        <result property="regionCnName" column="region_cn_name"/>
        <result property="repofficeCode" column="repoffice_code"/>
        <result property="repofficeCnName" column="repoffice_cn_name"/>
        <result property="bgCode" column="bg_code"/>
        <result property="bgCnName" column="bg_cn_name"/>
        <result property="overseaFlag" column="oversea_flag"/>
        <result property="codeAttributes" column="code_attributes"/>
    </resultMap>

    <sql id="weightChartFields">
        DISTINCT
        group_code,
        group_cn_name,
        group_level,
        parent_code,
        parent_cn_name,
        weight_rate,
        ROUND(weight_rate * 100, 2) || '%' AS weight_percent
    </sql>

    <select id="findWeightVOList" resultMap="resultMap">
        SELECT
        <include refid="weightChartFields"/>
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.tablePreFix}_mon_weight_t
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <include refid="searchWhere"/>
            and del_flag = 'N'
        </trim>
        ORDER BY weight_rate DESC
    </select>
    
    <sql id="searchWhere">
        <if test='monthAnalysisVO.versionId != null'>
            AND version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='monthAnalysisVO.softwareMark != null and monthAnalysisVO.softwareMark != ""'>
            AND software_mark = #{monthAnalysisVO.softwareMark,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.intervalYear != null and monthAnalysisVO.intervalYear != ""'>
            AND period_year = #{monthAnalysisVO.intervalYear,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.viewFlag != null and monthAnalysisVO.viewFlag != ""'>
            AND view_flag = #{monthAnalysisVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            AND oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
            AND bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
            AND region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
            AND repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.mainFlag != null and monthAnalysisVO.mainFlag != ""'>
            AND main_flag = #{monthAnalysisVO.mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.codeAttributes != null and monthAnalysisVO.codeAttributes != ""'>
            AND code_attributes = #{monthAnalysisVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupLevel != null and monthAnalysisVO.groupLevel != ""'>
            and group_level = #{monthAnalysisVO.groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.parentCodeList != null and monthAnalysisVO.parentCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code" open="AND parent_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.subGroupCodeList != null and monthAnalysisVO.subGroupCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.subGroupCodeList' item="code" open="AND group_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.granularityType=="IRB" and monthAnalysisVO.prodRndTeamCodeList != null and monthAnalysisVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND prod_rnd_team_code IN ("
                     close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.granularityType=="PROD" and monthAnalysisVO.prodRndTeamCodeList != null and monthAnalysisVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND prod_list_code IN ("
                     close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.granularityType=="INDUS" and monthAnalysisVO.prodRndTeamCodeList != null and monthAnalysisVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND industry_catg_code IN ("
                     close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.dimensionCode != null and monthAnalysisVO.dimensionCode != ""'>
            AND dimension_code = #{monthAnalysisVO.dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.dimensionSubCategoryCode != null and monthAnalysisVO.dimensionSubCategoryCode != ""'>
            AND dimension_subcategory_code = #{monthAnalysisVO.dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.dimensionSubDetailCode != null and monthAnalysisVO.dimensionSubDetailCode != ""'>
            AND dimension_sub_detail_code = #{monthAnalysisVO.dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
    </sql>

    <sql id="blurSearchWhere">
        <if test='monthAnalysisVO.versionId != null'>
            AND version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='monthAnalysisVO.softwareMark != null and monthAnalysisVO.softwareMark != ""'>
            AND software_mark = #{monthAnalysisVO.softwareMark,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.intervalYear != null and monthAnalysisVO.intervalYear != ""'>
            AND period_year = #{monthAnalysisVO.intervalYear,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.customIdList != null and monthAnalysisVO.customIdList.size() > 0'>
            <foreach collection='monthAnalysisVO.customIdList' item="code" open="AND custom_id IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.customCnName != null and monthAnalysisVO.customCnName != ""'>
            AND custom_cn_name = #{monthAnalysisVO.customCnName,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.granularityType != null and monthAnalysisVO.granularityType != ""'>
            AND granularity_type = #{monthAnalysisVO.granularityType,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.viewFlag != null and monthAnalysisVO.viewFlag != ""'>
            AND view_flag = #{monthAnalysisVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            AND oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
            AND bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
            AND region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
            AND repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.mainFlag != null and monthAnalysisVO.mainFlag != ""'>
            AND main_flag = #{monthAnalysisVO.mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.codeAttributes != null and monthAnalysisVO.codeAttributes != ""'>
            AND code_attributes = #{monthAnalysisVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.parentLevel != null and monthAnalysisVO.parentLevel != ""'>
            AND parent_level = #{monthAnalysisVO.parentLevel,jdbcType=VARCHAR}
        </if>
        <choose>
            <when test='monthAnalysisVO.viewFlag == "PROD_SPART"'>
                AND group_level = 'SPART'
            </when>
            <when test='monthAnalysisVO.viewFlag == "DIMENSION"'>
                AND group_level = 'SUB_DETAIL'
            </when>
        </choose>
        <if test='monthAnalysisVO.customGroupCodeList != null and monthAnalysisVO.customGroupCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.customGroupCodeList' item="code" open="AND parent_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.subGroupCodeList != null and monthAnalysisVO.subGroupCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.subGroupCodeList' item="code" open="AND group_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
    </sql>

    <select id="findBlurWeightVOList" resultMap="resultMap">
        SELECT
        <include refid="weightChartFields"/>
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.blurTablePreFix}_base_cus_mon_weight_t
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <include refid="blurSearchWhere"/>
            and del_flag = 'N'
        </trim>
        ORDER BY weight_rate DESC
    </select>

    <select id="findBlurWeightVOListByPage" resultMap="resultMap">
        SELECT
        <include refid="weightChartFields"/>
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.blurTablePreFix}_base_cus_mon_weight_t
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <include refid="blurSearchWhere"/>
            and del_flag = 'N'
        </trim>
        ORDER BY weight_rate DESC
        LIMIT #{pageVO.pageSize} OFFSET #{pageVO.mysqlStartIndex}
    </select>

    <select id="findBlurWeightVOListByPageCount" resultType="int">
        SELECT COUNT(1) FROM (
        SELECT <include refid="weightChartFields"/>
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.blurTablePreFix}_base_cus_mon_weight_t
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <include refid="blurSearchWhere"/>
            and del_flag = 'N'
        </trim>
        )
    </select>

    <sql id="blurMinLevDropdownListQuery">
        SELECT DISTINCT
        t1.custom_id,
        t1.group_code,
        t1.group_cn_name,
        t1.group_level,
        t2.parent_code,
        t2.parent_cn_name,
        t1.weight_rate,
        ROUND(t1.weight_rate * 100, 2 ) || '%' AS weight_percent,
        t1.lv4_code AS prod_rnd_team_code,
        t2.lv4_cn_name AS prod_rd_team_cn_name
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.blurTablePreFix}_base_cus_mon_weight_t t1
        LEFT JOIN fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.tablePreFix}_mon_mid_cost_idx_t t2
        on t2.version_id = t1.version_id
        AND t2.view_flag = t1.view_flag
        AND t2.oversea_flag = t1.oversea_flag
        AND t2.bg_code = t1.bg_code
        AND t2.region_code = t1.region_code
        AND t2.repoffice_code = t1.repoffice_code
        AND t2.main_flag = t1.main_flag
        AND t2.group_level = t1.group_level
        AND t2.group_code = t1.group_code
        AND t2.lv4_code = t1.lv4_code
        AND t2.del_flag = 'N'
        <if test='monthAnalysisVO.costType == "PSP"'>
        AND t2.software_mark = t1.software_mark
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='monthAnalysisVO.versionId != null'>
                AND t1.version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
            </if>
            <if test='monthAnalysisVO.softwareMark != null and monthAnalysisVO.softwareMark != ""'>
                AND t1.software_mark = #{monthAnalysisVO.softwareMark,jdbcType=VARCHAR}
            </if>
            <if test='monthAnalysisVO.intervalYear != null and monthAnalysisVO.intervalYear != ""'>
                AND t1.period_year = #{monthAnalysisVO.intervalYear,jdbcType=VARCHAR}
            </if>
            <if test='monthAnalysisVO.customIdList != null and monthAnalysisVO.customIdList.size() > 0'>
                <foreach collection='monthAnalysisVO.customIdList' item="code" open="AND t1.custom_id IN (" close=")" index="index" separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='monthAnalysisVO.granularityType != null and monthAnalysisVO.granularityType != ""'>
                AND t1.granularity_type = #{monthAnalysisVO.granularityType,jdbcType=VARCHAR}
            </if>
            <if test='monthAnalysisVO.viewFlag != null and monthAnalysisVO.viewFlag != ""'>
                AND t1.view_flag = #{monthAnalysisVO.viewFlag,jdbcType=VARCHAR}
            </if>
            <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
                AND t1.oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
                AND t1.bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
            </if>
            <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
                AND t1.region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
            </if>
            <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
                AND t1.repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='monthAnalysisVO.mainFlag != null and monthAnalysisVO.mainFlag != ""'>
                AND t1.main_flag = #{monthAnalysisVO.mainFlag,jdbcType=VARCHAR}
            </if>
            <if test='monthAnalysisVO.codeAttributes != null and monthAnalysisVO.codeAttributes != ""'>
                AND t1.code_attributes = #{monthAnalysisVO.codeAttributes,jdbcType=VARCHAR}
            </if>
            <choose>
                <when test='monthAnalysisVO.viewFlag == "PROD_SPART"'>
                    AND t1.group_level = 'SPART'
                </when>
                <when test='monthAnalysisVO.viewFlag == "DIMENSION"'>
                    AND t1.group_level = 'SUB_DETAIL'
                </when>
            </choose>
            <if test='monthAnalysisVO.customGroupCodeList != null and monthAnalysisVO.customGroupCodeList.size() > 0'>
                <foreach collection='monthAnalysisVO.customGroupCodeList' item="code" open="AND t1.group_code IN (" close=")" index="index" separator=",">
                    #{code}
                </foreach>
            </if>
            and t1.del_flag = 'N'
        </trim>


    </sql>

    <select id="findBlurMinLevWeightVOList" resultMap="resultMap">
        <include refid="blurMinLevDropdownListQuery"/>
        ORDER BY t1.weight_rate DESC
    </select>

    <select id="findBlurMinLevDropdownListByPage" resultMap="resultMap">
        <include refid="blurMinLevDropdownListQuery"/>
        ORDER BY t1.weight_rate DESC
        LIMIT #{pageVO.pageSize} OFFSET #{pageVO.mysqlStartIndex}
    </select>

    <select id="findBlurMinLevDropdownListByPageCount" resultType="int">
       SELECT COUNT(1) FROM (<include refid="blurMinLevDropdownListQuery"/>)
    </select>

    <select id="findCombWeightList" resultMap="resultMap">
        SELECT distinct
        t1.custom_id as comb_id,
        t1.custom_cn_name,
        t1.parent_cn_name,
        t1.parent_code,
        t1.weight_rate,
        ROUND(t1.weight_rate * 100, 2) || '%' AS weight_percent,
        t1.group_cn_name,
        t1.group_code
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.blurTablePreFix}_cus_mon_weight_t t1
        WHERE t1.version_id = #{monthAnalysisVO.versionId} AND t1.del_flag = 'N'
        <if test='monthAnalysisVO.softwareMark != null and monthAnalysisVO.softwareMark != ""'>
            AND t1.software_mark = #{monthAnalysisVO.softwareMark,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.intervalYear != null and monthAnalysisVO.intervalYear != ""'>
            AND t1.period_year = #{monthAnalysisVO.intervalYear,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.granularityType != null and monthAnalysisVO.granularityType != ""'>
            AND t1.granularity_type = #{monthAnalysisVO.granularityType,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            AND t1.oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
            AND t1.bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
            AND t1.region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
            AND t1.repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.mainFlag != null and monthAnalysisVO.mainFlag != ""'>
            AND t1.main_flag = #{monthAnalysisVO.mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.codeAttributes != null and monthAnalysisVO.codeAttributes != ""'>
            AND t1.code_attributes = #{monthAnalysisVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.combIdList != null and monthAnalysisVO.combIdList.size() > 0'>
            <foreach collection='monthAnalysisVO.combIdList' item="code" open="AND t1.custom_id IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        ORDER BY t1.weight_rate DESC
    </select>
</mapper>
