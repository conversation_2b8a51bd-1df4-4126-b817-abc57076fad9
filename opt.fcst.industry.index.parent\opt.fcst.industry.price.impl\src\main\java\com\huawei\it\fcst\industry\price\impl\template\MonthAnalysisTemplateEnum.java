/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.impl.template;

import com.huawei.it.fcst.export.IExcelTemplateBeanManager;
import com.huawei.it.fcst.export.vo.ExcelTemplateBeanManager;
import com.huawei.it.fcst.export.vo.SheetBeanMetaVO;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 定价指数-综合指数分析页面导出模板
 *
 * <AUTHOR>
 * @since 2024年7月12日
 */
@Getter
public enum MonthAnalysisTemplateEnum implements IExcelTemplateBeanManager {
    MONTH_CODE_01("01", "PriceMonthAnalysisExportTemplate1", "价格指数-产业-ICT-综合指数分析", "价格指数-产业-ICT-综合指数分析") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            List<SheetBeanMetaVO> sheetBeans = new ArrayList<>();
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_01.templateName, 0, "PriceIndexChartExpDataProvider", "定价指数图", Boolean.FALSE));
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_01.templateName, 1, "PriceYoyAndPopChartExpDataProvider", "定价指数图-同比环比图", Boolean.FALSE));
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_01.templateName, 2, "MultiPriceIndexChartExpDataProvider", "定价指数图（多子项）", Boolean.FALSE));
            return new ExcelTemplateBeanManager(MONTH_CODE_01.templateName,MONTH_CODE_01.moduleType,MONTH_CODE_01.desc,sheetBeans);
        }
    },
    MONTH_CODE_02("02", "PriceMonthMultiSelectExpTemplate1", "价格指数-产业-ICT-综合指数分析", "价格指数-产业-ICT-综合指数分析") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            List<SheetBeanMetaVO> sheetBeans = new ArrayList<>();
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_02.templateName, 0, "PriceIndexChartExpDataProvider", "定价指数图", Boolean.FALSE));
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_02.templateName, 1, "PriceYoyAndPopChartExpDataProvider", "定价指数图-同比环比图", Boolean.FALSE));
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_02.templateName, 2, "MultiPriceIndexChartExpDataProvider", "定价指数图（多子项）", Boolean.FALSE));
            return new ExcelTemplateBeanManager(MONTH_CODE_02.templateName,MONTH_CODE_02.moduleType,MONTH_CODE_02.desc,sheetBeans);
        }
    },
    MONTH_CODE_03("03", "PriceMonthAnalysisExportTemplate2", "价格指数-产业-ICT-综合指数分析", "价格指数-产业-ICT-综合指数分析") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            List<SheetBeanMetaVO> sheetBeans = new ArrayList<>();
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_03.templateName, 0, "PriceIndexChartExpDataProvider", "定价指数图", Boolean.FALSE));
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_03.templateName, 1, "PriceYoyAndPopChartExpDataProvider", "定价指数图-同比环比图", Boolean.FALSE));
            return new ExcelTemplateBeanManager(MONTH_CODE_03.templateName,MONTH_CODE_03.moduleType,MONTH_CODE_03.desc,sheetBeans);
        }
    },
    MONTH_CODE_04("04", "PriceMonthMultiSelectExpTemplate2", "价格指数-产业-ICT-综合指数分析", "价格指数-产业-ICT-综合指数分析") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            List<SheetBeanMetaVO> sheetBeans = new ArrayList<>();
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_04.templateName, 0, "PriceIndexChartExpDataProvider", "定价指数图", Boolean.FALSE));
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_04.templateName, 1, "PriceYoyAndPopChartExpDataProvider", "定价指数图-同比环比图", Boolean.FALSE));
            return new ExcelTemplateBeanManager(MONTH_CODE_04.templateName,MONTH_CODE_04.moduleType,MONTH_CODE_04.desc,sheetBeans);
        }
    };

    private String code;
    private String templateName;
    private String moduleType;
    private String desc;

    MonthAnalysisTemplateEnum(String code, String templateName, String moduleType, String description) {
        this.code = code;
        this.templateName = templateName;
        this.moduleType = moduleType;
        this.desc = description;
    }

}
