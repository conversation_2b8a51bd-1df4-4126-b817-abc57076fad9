/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.utils;

import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * FcstIndexMadeUtilTest Class
 *
 * <AUTHOR>
 * @since 2023/11/6
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class FcstIndexMadeUtilTest {
    private static final Logger LOGGER = LoggerFactory.getLogger(FcstIndexMadeUtil.class);
    @InjectMocks
    private FcstIndexMadeUtil fcstIndexMadeUtil;


    @Test
    public void getNextThreeGroupLevel() {
        Assert.assertNotNull(fcstIndexMadeUtil.getNextThreeGroupLevel("ICT"));
    }

    @Test
    public void getNextOneGroupLevel() {
        Assert.assertNotNull(fcstIndexMadeUtil.getNextOneGroupLevel("ICT"));
    }

    @Test
    public void getNextSixGroupLevel() {
        Assert.assertNotNull(fcstIndexMadeUtil.getNextSixGroupLevel("ICT"));
    }

    @Test
    public void getNextFiveGroupLevelUniversal() {
        Assert.assertNotNull(fcstIndexMadeUtil.getNextFiveGroupLevelUniversal("ICT"));
    }

    @Test
    public void getMadeNextFourGroupLevel() {
        Assert.assertNotNull(fcstIndexMadeUtil.getMadeNextFourGroupLevel("ICT"));
    }

    @Test
    public void getMadeNextFourGroupLevelProfit() {
        Assert.assertNotNull(fcstIndexMadeUtil.getMadeNextFourGroupLevelProfit("ICT"));
    }

    @Test
    public void getMadeNextFiveGroupLevel() {
        Assert.assertNotNull(fcstIndexMadeUtil.getMadeNextFiveGroupLevel("ICT"));
    }

    @Test
    public void getMadeNextOneGroupLevelDimension() {
        Assert.assertNotNull(fcstIndexMadeUtil.getMadeNextOneGroupLevelDimension("ICT"));
    }

    @Test
    public void getMadeNextTwoGroupLevelDimension() {
        Assert.assertNotNull(fcstIndexMadeUtil.getMadeNextTwoGroupLevelDimension("ICT"));
    }

    @Test
    public void getMadeNextThreeGroupLevelDimension() {
        Assert.assertNotNull(fcstIndexMadeUtil.getMadeNextThreeGroupLevelDimension("ICT"));
    }

    @Test
    public void getMadeNextFourGroupLevelDimension() {
        Assert.assertNotNull(fcstIndexMadeUtil.getMadeNextFourGroupLevelDimension("ICT"));
    }

    @Test
    public void getMadeNextFiveGroupLevelDimension() {
        Assert.assertNotNull(fcstIndexMadeUtil.getMadeNextFiveGroupLevelDimension("ICT"));
    }

    @Test
    public void getMadeNextSixGroupLevelDimension() {
        Assert.assertNotNull(fcstIndexMadeUtil.getMadeNextSixGroupLevelDimension("ICT"));
    }

    @Test
    public void getMadeNextSevenGroupLevelDimension() {
        Assert.assertNotNull(fcstIndexMadeUtil.getMadeNextSevenGroupLevelDimension("ICT"));
    }

    @Test
    public void getMadeNextEightGroupLevelDimension() {
        Assert.assertNotNull(fcstIndexMadeUtil.getMadeNextEightGroupLevelDimension("ICT"));
    }

    @Test
    public void getMadeNextNineGroupLevelDimension() {
        Assert.assertNotNull(fcstIndexMadeUtil.getMadeNextNineGroupLevelDimension("ICT"));
    }

    @Test
    public void getMadeNextTwoGroupLevel() {
        Assert.assertNotNull(fcstIndexMadeUtil.getMadeNextTwoGroupLevel("ICT"));
    }

    @Test
    public void getNextGroupLevelByView() {
        Assert.assertNotNull(fcstIndexMadeUtil.getNextGroupLevelByView("0","ICT","U","ICT"));
    }

    @Test
    public void getNextGroupLevelByView2T() {
        Assert.assertNotNull(fcstIndexMadeUtil.getNextGroupLevelByView("1","ICT","U","ICT"));
    }

    @Test
    public void getNextGroupLevelByView3T() {
        Assert.assertNotNull(fcstIndexMadeUtil.getNextGroupLevelByView("2","ICT","U","ICT"));
    }

    @Test
    public void getNextGroupLevelByView4T() {
        Assert.assertNotNull(fcstIndexMadeUtil.getNextGroupLevelByView("3","ICT","D","ICT"));
    }

    @Test
    public void getNextGroupLevelByView5T() {
        Assert.assertNotNull(fcstIndexMadeUtil.getNextGroupLevelByView("4","ICT","U","ICT"));
    }

    @Test
    public void getNextGroupLevelByView6T() {
        Assert.assertNotNull(fcstIndexMadeUtil.getNextGroupLevelByView("5","ICT","U","ICT"));
    }

    @Test
    public void getNextGroupLevelByView7T() {
        Assert.assertNotNull(fcstIndexMadeUtil.getNextGroupLevelByView("6","ICT","U","ICT"));
    }

    @Test
    public void getNextGroupLevelByView8T() {
        Assert.assertNotNull(fcstIndexMadeUtil.getNextGroupLevelByView("7","ICT","U","ICT"));
    }

    @Test
    public void getNextGroupLevelByView9T() {
        Assert.assertNotNull(fcstIndexMadeUtil.getNextGroupLevelByView("8","ICT","U","ICT"));
    }

    @Test
    public void getNextGroupLevelByView10T() {
        Assert.assertNotNull(fcstIndexMadeUtil.getNextGroupLevelByView("9","ICT","U","ICT"));
    }

    @Test
    public void getNextGroupLevel() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setViewFlag("0");
        Assert.assertNotNull(fcstIndexMadeUtil.getNextGroupLevel(monthAnalysisVO));
    }

    @Test
    public void getNextGroupLevel2T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setViewFlag("1");
        Assert.assertNotNull(fcstIndexMadeUtil.getNextGroupLevel(monthAnalysisVO));
    }

    @Test
    public void getNextGroupLevel3T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setViewFlag("2");
        Assert.assertNotNull(fcstIndexMadeUtil.getNextGroupLevel(monthAnalysisVO));
    }

    @Test
    public void getNextGroupLevel4T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setViewFlag("3");
        monthAnalysisVO.setGroupLevel("LV2");
        monthAnalysisVO.setGranularityType("D");
        Assert.assertNotNull(fcstIndexMadeUtil.getNextGroupLevel(monthAnalysisVO));
    }

    @Test
    public void getNextGroupLevel44T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setViewFlag("3");
        monthAnalysisVO.setGroupLevel("LV2");
        monthAnalysisVO.setGranularityType("U");
        Assert.assertNotNull(fcstIndexMadeUtil.getNextGroupLevel(monthAnalysisVO));
    }

    @Test
    public void getNextGroupLevel5T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setViewFlag("4");
        monthAnalysisVO.setGroupLevel("LV2");
        monthAnalysisVO.setGranularityType("U");
        Assert.assertNotNull(fcstIndexMadeUtil.getNextGroupLevel(monthAnalysisVO));
    }

    @Test
    public void getNextGroupLevel6T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setViewFlag("5");
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setGroupLevel("LV2");
        Assert.assertNotNull(fcstIndexMadeUtil.getNextGroupLevel(monthAnalysisVO));
    }

    @Test
    public void getNextGroupLevel7T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setViewFlag("6");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setGroupLevel("LV1");
        Assert.assertNotNull(fcstIndexMadeUtil.getNextGroupLevel(monthAnalysisVO));
    }

    @Test
    public void getNextGroupLevel8T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setViewFlag("7");
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setGroupLevel("LV2");
        Assert.assertNotNull(fcstIndexMadeUtil.getNextGroupLevel(monthAnalysisVO));
    }

    @Test
    public void getNextGroupLevel9T() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setViewFlag("8");
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setGroupLevel("LV2");
        Assert.assertNotNull(fcstIndexMadeUtil.getNextGroupLevel(monthAnalysisVO));
    }

}