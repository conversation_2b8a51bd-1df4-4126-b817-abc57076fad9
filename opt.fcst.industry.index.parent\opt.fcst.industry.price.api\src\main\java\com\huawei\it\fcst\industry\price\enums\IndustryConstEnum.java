/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2022/6/7
 */
public interface IndustryConstEnum {
    enum PAGE_TYPE {
        ANNUAL("ANNUAL", "年度分析页面"),
        MONTH("MONTH", "月度分析页面");
        @Getter
        @Setter
        private String value;
        @Getter
        @Setter
        private String desc;
        PAGE_TYPE(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }
    }
}
