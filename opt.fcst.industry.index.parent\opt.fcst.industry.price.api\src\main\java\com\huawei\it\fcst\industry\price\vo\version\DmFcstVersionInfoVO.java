/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.vo.version;

import com.huawei.it.fcst.vo.BaseVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * DmFocVersionInfoVO Class
 *
 * <AUTHOR>
 * @since 2024/7/10
 */
@NoArgsConstructor
@Getter
@Setter
@Builder
@AllArgsConstructor
public class DmFcstVersionInfoVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -967582932244188145L;

    private String dataType;

    private String parentVersionId;

    private String version;

    private Long versionId;


    private String versionType;

    private String isRunning;

    /**
     * 状态（1：已刷新、0：未刷新）
     **/
    private Long status;

    private String step;

}
