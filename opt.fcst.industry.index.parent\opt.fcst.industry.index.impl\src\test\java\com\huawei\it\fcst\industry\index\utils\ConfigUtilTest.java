/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.utils;

import com.huawei.it.fcst.config.ConfigUtil;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * ConfigUtilTest Class
 *
 * <AUTHOR>
 * @since 2023/4/26
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class ConfigUtilTest {
    @InjectMocks
    private ConfigUtil configUtil;


    @Test
    public void getPlainTextValue() {
        String plainTextValue = configUtil.getPlainTextValue();
        Assert.assertNotNull(plainTextValue);
    }

    @Test
    public void get32PlainText() {
        String plainText = configUtil.get32PlainText();
        Assert.assertNotNull(plainText);
    }

    @Test
    public void get16PlainText() {
        String plainText = configUtil.get16PlainText();
        Assert.assertNotNull(plainText);
    }

    @Test
    public void getKeyId() {
        String keyId = configUtil.getKeyId();
        Assert.assertNull(keyId);
    }

    @Test
    public void getCiphertext() {
        String ciphertext = configUtil.getCiphertext();
        Assert.assertNull(ciphertext);
    }

    @Test
    public void getKmsEnv() {
        String kmsEnv = configUtil.getKmsEnv();
        Assert.assertNull(kmsEnv);
    }

    @Test
    public void getGetDataKeyDecodeByKeyId() {
        String keyDecodeByKeyId = configUtil.getGetDataKeyDecodeByKeyId();
        Assert.assertNull(keyDecodeByKeyId);
    }

    @Test
    public void getInstance() {
        ConfigUtil instance = null;
        try {
            instance = configUtil.getInstance();
        } catch (Exception e) {

        }
        Assert.assertNull(instance);
    }
}