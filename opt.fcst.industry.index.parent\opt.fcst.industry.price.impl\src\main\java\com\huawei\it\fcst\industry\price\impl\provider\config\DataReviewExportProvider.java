/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.impl.provider.config;

import com.huawei.it.fcst.enums.CommonConstEnum;
import com.huawei.it.fcst.export.IExcelExportDataProvider;
import com.huawei.it.fcst.export.vo.ExcelExportContext;
import com.huawei.it.fcst.export.vo.ExportList;
import com.huawei.it.fcst.industry.price.dao.IDmFcstPriceVersionInfoDao;
import com.huawei.it.fcst.industry.price.dao.IDmRawDataExamineDao;
import com.huawei.it.fcst.industry.price.vo.config.BottomDataReviewVO;
import com.huawei.it.fcst.industry.price.vo.config.DmRawDataExamineDTO;
import com.huawei.it.fcst.industry.price.vo.version.DmFcstVersionInfoVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Inject;
import javax.inject.Named;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 导出 底层数据审视
 * DataReviewExportProvider Class
 *
 * <AUTHOR>
 * @since 2024/11/7
 */
@Named("IExcelExport.DataReviewExportProvider")
public class DataReviewExportProvider implements IExcelExportDataProvider {

    @Inject
    private IDmFcstPriceVersionInfoDao dmFcstVersionInfoDao;

    @Autowired
    private IDmRawDataExamineDao dmRawDataExamineDao;

    @Override
    public List<?> getData(Serializable conditionObject, PageVO pageVO) {
        BottomDataReviewVO bottomDataReviewVO = (BottomDataReviewVO) conditionObject;
        PagedResult<DmRawDataExamineDTO> dmRawDataExaminePagedResult = dmRawDataExamineDao.findDataReviewListByPage(bottomDataReviewVO, pageVO);
        List<DmRawDataExamineDTO> dmRawDataExamineList = dmRawDataExaminePagedResult.getResult();
        for (DmRawDataExamineDTO rawDataExamineDTO : dmRawDataExamineList) {
            // 设置国内海外
            if (StringUtils.isNotEmpty(rawDataExamineDTO.getOverseaFlag())) {
                rawDataExamineDTO.setOverseaFlag(CommonConstEnum.getOverSeaFlag(rawDataExamineDTO.getOverseaFlag()).getDesc());
            }
            rawDataExamineDTO.setModifyType(CommonConstEnum.getModifyType(rawDataExamineDTO.getModifyType()).getDesc());
            if ("REVOKE".equals(rawDataExamineDTO.getModifyType())) {
                rawDataExamineDTO.setModifyReasonR(rawDataExamineDTO.getModifyReason());
            } else {
                rawDataExamineDTO.setModifyReasonM(rawDataExamineDTO.getModifyReason());
            }
        }
        ExportList list = new ExportList();
        list.addAll(dmRawDataExamineList);
        list.setTotalRows(dmRawDataExaminePagedResult.getPageVO().getTotalRows());
        return list;
    }

    @Override
    public Map<String, Object> getHeader(Serializable conditionObject, Map<String, Object> parameters) throws ApplicationException {
        ExcelExportContext context = (ExcelExportContext) conditionObject;
        BottomDataReviewVO bottomDataReviewVO = (BottomDataReviewVO) context.getConditionObject();
        // 自定义表头
        Map<String, Object> headMap = new HashMap<>();
        // 通过id获取版本信息
        DmFcstVersionInfoVO versionInfoVO = dmFcstVersionInfoDao.findDmFocVersionById(bottomDataReviewVO.getVersionId());
        headMap.put("version", versionInfoVO.getVersion());
        return headMap;
    }
}
