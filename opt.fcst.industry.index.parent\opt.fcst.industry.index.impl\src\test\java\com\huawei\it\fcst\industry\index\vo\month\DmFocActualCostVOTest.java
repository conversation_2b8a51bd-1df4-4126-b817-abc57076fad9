/*
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.month;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @since 2023/5/9
 */
public class DmFocActualCostVOTest extends BaseVOCoverUtilsTest<DmFocActualCostVO> {
    @Override
    protected Class<DmFocActualCostVO> getTClass() {
        return DmFocActualCostVO.class;
    }

    @Test
    public void testMethod() {
        DmFocActualCostVO dmFocActualCostVO = new DmFocActualCostVO();
        dmFocActualCostVO.setActualCostAmt(10D);
        dmFocActualCostVO.getActualCostAmt();
        dmFocActualCostVO.setDelFlag("Y");
        dmFocActualCostVO.getDelFlag();
        dmFocActualCostVO.setCreatedBy("1175");
        dmFocActualCostVO.getCreatedBy();
        dmFocActualCostVO.getCreationDate();
        dmFocActualCostVO.setGroupCode("1163A");
        dmFocActualCostVO.getGroupCode();
        dmFocActualCostVO.setVersionId(11L);
        dmFocActualCostVO.getVersionId();
        dmFocActualCostVO.setGroupCnName("元器");
        dmFocActualCostVO.getGroupCnName();
        dmFocActualCostVO.setGroupLevel("LV3");
        dmFocActualCostVO.getGroupLevel();
        dmFocActualCostVO.setId(12L);
        dmFocActualCostVO.getId();
        dmFocActualCostVO.setViewFlag("0");
        dmFocActualCostVO.getViewFlag();
        dmFocActualCostVO.setPeriodId(13L);
        dmFocActualCostVO.getPeriodId();
        dmFocActualCostVO.setParentCode("54211");
        dmFocActualCostVO.getParentCode();
        dmFocActualCostVO.setPeriodYear(2023L);
        dmFocActualCostVO.getPeriodYear();
        dmFocActualCostVO.getLastUpdateDate();
        dmFocActualCostVO.setLastUpdatedBy("166");
        dmFocActualCostVO.getLastUpdatedBy();
        dmFocActualCostVO.setProdRndTeamCode("code");
        dmFocActualCostVO.getProdRndTeamCode();
        dmFocActualCostVO.setProdRndTeamCnName("ttt");
        dmFocActualCostVO.getProdRndTeamCnName();
        dmFocActualCostVO.getCostType();
        dmFocActualCostVO.setCostType("11");
        dmFocActualCostVO.getDimensionCnName();
        dmFocActualCostVO.setDimensionCnName("22");
        dmFocActualCostVO.getDimensionSubCategoryCnName();
        dmFocActualCostVO.setDimensionSubCategoryCnName("33");
        dmFocActualCostVO.getDimensionSubDetailCnName();
        dmFocActualCostVO.setDimensionSubDetailCnName("44");
        dmFocActualCostVO.getDmsCode();
        dmFocActualCostVO.setDmsCode("55");
        dmFocActualCostVO.getDmsCnName();
        dmFocActualCostVO.setDmsCnName("66");
        dmFocActualCostVO.getMadeAmt();
        dmFocActualCostVO.setMadeAmt(66.1);
        dmFocActualCostVO.getMadeWeight();
        dmFocActualCostVO.setMadeWeight(0.3);
        dmFocActualCostVO.getPurAmt();
        dmFocActualCostVO.setPurAmt(41.1);
        dmFocActualCostVO.getPurWeight();
        dmFocActualCostVO.setPurWeight(0.6);
        dmFocActualCostVO.getTotalAmt();
        dmFocActualCostVO.setTotalAmt(99.0);
        Assert.assertNotNull(dmFocActualCostVO);
    }

}