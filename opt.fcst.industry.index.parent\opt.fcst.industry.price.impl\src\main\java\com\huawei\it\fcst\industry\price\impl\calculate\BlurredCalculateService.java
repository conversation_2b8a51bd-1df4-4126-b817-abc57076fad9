/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.impl.calculate;

import com.huawei.it.fcst.enums.CommonConstEnum;
import com.huawei.it.fcst.industry.price.dao.IDmFcstPriceBaseCusDimDao;
import com.huawei.it.fcst.industry.price.dao.IDmFcstPriceBaseCusUserDao;
import com.huawei.it.fcst.industry.price.dao.IDmFcstPriceDimInfoDao;
import com.huawei.it.fcst.industry.price.impl.common.PriceCommonService;
import com.huawei.it.fcst.industry.price.service.calculate.IBlurredCalculateService;
import com.huawei.it.fcst.industry.price.utils.FcstIndustryUtil;
import com.huawei.it.fcst.industry.price.vo.common.CommonPriceBaseVO;
import com.huawei.it.fcst.industry.price.vo.common.DmFcstPriceDimInfoVO;
import com.huawei.it.fcst.industry.price.vo.drop.BasePriceCusDimVO;
import com.huawei.it.fcst.industry.price.vo.drop.DmFcstBasePriceCusDimVO;
import com.huawei.it.fcst.industry.price.vo.drop.DmFcstBasePriceCusUserVO;
import com.huawei.it.fcst.util.ObjectCopyUtil;
import com.huawei.it.fcst.util.UserInfoUtils;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.security.RoleVO;
import com.huawei.it.jalor5.security.UserVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Named;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/11/6
 */
@Named("blurredCalculateService")
@JalorResource(code = "blurredCalculateService", desc = "定价指数-虚化汇总计算")
public class BlurredCalculateService  implements IBlurredCalculateService {

    @Autowired
    private IDmFcstPriceBaseCusDimDao dmFcstPriceBaseCusDao;

    @Autowired
    private IDmFcstPriceBaseCusUserDao dmFcstBasePriceCusUserDao;

    @Autowired
    private IDmFcstPriceDimInfoDao dmFcstPriceDimInfoDao;

    @Autowired
    private PriceCommonService commonService;

    @JalorOperation(code = "dropDownSummaryCalculate", desc = "虚化计算状态")
    @Audit(module = "blurredCalculateService-dropDownSummaryCalculate", operation = "dropDownSummaryCalculate", message = "虚化计算状态")
    @Override
    public ResultDataVO dropDownSummaryCalculate(CommonPriceBaseVO commonPriceBaseVO) throws CommonApplicationException {
        // 入参校验
        if ("SPART".equals(commonPriceBaseVO.getGroupLevel()) && CollectionUtils.isEmpty(commonPriceBaseVO.getSpartCodeList())) {
            throw new CommonApplicationException("参数错误");
        }
        commonPriceBaseVO.setVersionId(commonService.getVersionId("ANNUAL"));
        List<Map<String, Object>> allCalculateMap = new ArrayList<>();
        List<String> spartCodeList = commonPriceBaseVO.getSpartCodeList();
        for (String groupCode : spartCodeList) {
            Map<String, Object> calculateSpartMap = new HashMap<>();
            commonPriceBaseVO.setSpartCode(groupCode);
            commonPriceBaseVO.setGroupCode(groupCode);
            List<String> setSpartCodeList = new ArrayList<>();
            setSpartCodeList.add(groupCode);
            commonPriceBaseVO.setSpartCodeList(setSpartCodeList);
            setCalculateMap(commonPriceBaseVO, calculateSpartMap);
            allCalculateMap.add(calculateSpartMap);
        }
        return ResultDataVO.success(allCalculateMap);
    }

    private void setCalculateMap(CommonPriceBaseVO commonPriceBaseVO, Map<String, Object> calculateMap) throws CommonApplicationException {
        int num = dmFcstPriceDimInfoDao.getSpartNum(commonPriceBaseVO);
        // spart维度存在不为空的话，有可能存在虚化
        if (StringUtils.isNotBlank(commonPriceBaseVO.getSpartCode())) {
            // 校验lv3.5有没有选择
            List<String> lv4ProdListCodeList = commonPriceBaseVO.getLv4ProdListCodeList();
            if (CollectionUtils.isNotEmpty(lv4ProdListCodeList) && lv4ProdListCodeList.size() == 1) {
                // 不用虚化
                String prodCodeList = lv4ProdListCodeList.get(0);
                calculateMap.put("prodListCodeList", prodCodeList);
                calculateMap.put("groupCode", commonPriceBaseVO.getGroupCode());
                calculateMap.put("num", num);
                calculateMap.put("status", false);
            } else {
                verifyAndCreatComb(commonPriceBaseVO, calculateMap, num);
            }
        } else {
            // 不用虚化
            calculateMap.put("groupCode", commonPriceBaseVO.getGroupCode());
            calculateMap.put("status", false);
            calculateMap.put("num", num);
        }
    }

    private void verifyAndCreatComb(CommonPriceBaseVO commonBaseVO, Map<String, Object> calculateMap, int num) throws CommonApplicationException {
        // 需要虚化,需要校验一下对应条件下Spart维度或者量纲维度数据量，是否大于1
        if (num > 1) {
            calculateMap.put("status", true);
            calculateMap.put("num", num);
            // 计算状态校验 第一步：查询组合表能不能查到历史计算过的记录
            // 第二步：如果记录能查到,再和用户账号做匹配，匹配的上就直接返回已计算,匹配不上的，先新增加一条组合信息绑定到个人，计算状态默认已计算
            // 第三步：如果记录查不到,先新增组合记录，再调用计算，回写计算状态
            BasePriceCusDimVO baseCusDimVO = ObjectCopyUtil.copy(commonBaseVO, BasePriceCusDimVO.class);
            FcstIndustryUtil.setLvCode(baseCusDimVO);
            List<DmFcstBasePriceCusDimVO> combInfoList = dmFcstPriceBaseCusDao.getBaseCusDimInfoList(baseCusDimVO);
            // 由于lv0Code是拼接的，需要将拼接的字符串转换为数组后，匹配
            List<String> paramLv0CodeList = Arrays.asList(baseCusDimVO.getLvCode().replace("'", "").split(","));
            // 获取已有组合
            DmFcstBasePriceCusDimVO dmFcstCombDimInfo = new DmFcstBasePriceCusDimVO();
            for (DmFcstBasePriceCusDimVO dmFcstBaseCusDimVO : combInfoList) {
                String lv0CodeStr = dmFcstBaseCusDimVO.getLvCode().replace("'", "");
                List<String> lv0CodeList = Arrays.asList(lv0CodeStr.split(","));
                if (paramLv0CodeList.containsAll(lv0CodeList) && paramLv0CodeList.size() == lv0CodeList.size()) {
                    // 已存在组合
                    dmFcstCombDimInfo = dmFcstBaseCusDimVO;
                    break;
                }
            }
            if (null != dmFcstCombDimInfo.getCustomId()) {
                // 获取计算状态
                String statusFlag = dmFcstCombDimInfo.getStatusFlag();
                // 组合id
                Long customId = dmFcstCombDimInfo.getCustomId();
                // 设置个人信息
                DmFcstBasePriceCusUserVO dmFcstBaseCusUserVO = getDmFcstBasePriceCusUserVO(customId);
                // 查询个人信息表
                List<DmFcstBasePriceCusUserVO> cusIdByUser = dmFcstBasePriceCusUserDao.getCusIdByUser(dmFcstBaseCusUserVO);
                if (CollectionUtils.isEmpty(cusIdByUser)) {
                    // 先新增加一条个人信息绑定到组合id
                    dmFcstBasePriceCusUserDao.createDmFcstCusUserInfoDTO(dmFcstBaseCusUserVO);
                }
                calculateMap.put("calculateStatus", statusFlag);
                calculateMap.put("customId", customId);
            } else {
                // 查不到记录，新增个人组合记录，计算状态先设置为计算中
                FcstIndustryUtil.setLvCode(baseCusDimVO);
                DmFcstBasePriceCusDimVO dmFcstBaseCusDimVO = ObjectCopyUtil.copy(baseCusDimVO, DmFcstBasePriceCusDimVO.class);
                dmFcstBaseCusDimVO.setStatusFlag(CommonConstEnum.STATUS_FLAG.D.getValue());
                // 新增组合
                Long customId = insertBaseCombDimVO(dmFcstBaseCusDimVO);
                // 设置个人信息
                DmFcstBasePriceCusUserVO dmFcstBaseCusUserVO = getDmFcstBasePriceCusUserVO(customId);
                // 新增个人信息
                dmFcstBasePriceCusUserDao.createDmFcstCusUserInfoDTO(dmFcstBaseCusUserVO);
                calculateMap.put("calculateStatus", CommonConstEnum.STATUS_FLAG.D.getValue());
            }
            calculateMap.put("groupCode", commonBaseVO.getGroupCode());
        } else {
            List<DmFcstPriceDimInfoVO> lv4CodeList = dmFcstPriceDimInfoDao.getLv4CodeWithSpart(commonBaseVO);
            List<String> lv4Code = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(lv4CodeList)) {
                lv4Code = lv4CodeList.stream().map(ele -> ele.getProdListCode()).collect(Collectors.toList());
            }
            calculateMap.put("prodListCodeList", lv4Code);
            calculateMap.put("status", false);
            calculateMap.put("num", num);
            calculateMap.put("groupCode", commonBaseVO.getGroupCode());
        }
    }

    @NotNull
    private DmFcstBasePriceCusUserVO getDmFcstBasePriceCusUserVO(Long customId) {
        DmFcstBasePriceCusUserVO dmFcstBaseCusUserVO = new DmFcstBasePriceCusUserVO();
        dmFcstBaseCusUserVO.setCustomId(customId);
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        Long userId = UserInfoUtils.getUserId();
        dmFcstBaseCusUserVO.setRoleId(String.valueOf(currentRole.getRoleId()));
        dmFcstBaseCusUserVO.setUserId(String.valueOf(userId));
        dmFcstBaseCusUserVO.setCreatedBy(userId);
        dmFcstBaseCusUserVO.setLastUpdatedBy(userId);
        dmFcstBaseCusUserVO.setDelFlag("N");
        return dmFcstBaseCusUserVO;
    }

    private Long insertBaseCombDimVO(DmFcstBasePriceCusDimVO dmFcstCombDimInfo) {
        // 设置组合名称字段
        Long baseCusDimKey = dmFcstPriceBaseCusDao.getBaseCusDimKey();
        dmFcstCombDimInfo.setCustomId(baseCusDimKey);
        dmFcstCombDimInfo.setCustomCnName(baseCusDimKey + "");
        Long userId = UserInfoUtils.getUserId();
        dmFcstCombDimInfo.setCreatedBy(userId);
        dmFcstCombDimInfo.setLastUpdatedBy(userId);
        dmFcstPriceBaseCusDao.createDmFcstCusDimDTO(dmFcstCombDimInfo);
        return baseCusDimKey;
    }
}
