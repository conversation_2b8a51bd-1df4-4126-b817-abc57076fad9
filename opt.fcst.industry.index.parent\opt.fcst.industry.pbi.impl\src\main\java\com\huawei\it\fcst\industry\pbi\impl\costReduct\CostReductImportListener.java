/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.costReduct;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.collect.Lists;
import com.huawei.it.fcst.industry.pbi.dao.ICostReductDao;
import com.huawei.it.fcst.industry.pbi.utils.CostReductUtils;
import com.huawei.it.fcst.industry.pbi.vo.config.CostReductImportVO;
import com.huawei.it.fcst.industry.pbi.vo.config.CostReductVO;
import com.huawei.it.fcst.industry.pbi.vo.config.ImportContextVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import java.time.Year;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 降成本目标维表导入监听器
 *
 * <AUTHOR>
 * @since 2024-09-27
 */
@Slf4j
public class CostReductImportListener implements ReadListener<CostReductImportVO> {

    private ICostReductDao costReductDao;

    private ImportContextVO importContextVO;

    // L1层级映射
    private Map<String, String> lv1Map = new HashMap<>();

    // L2层级映射
    private Map<String, String> lv2Map = new HashMap<>();

    private Set<String> checkDuplicateData = new HashSet<>(20000);

    // 设置数据集合
    private ThreadLocal<List<CostReductImportVO>> currentDataList = ThreadLocal.withInitial(ArrayList::new);

    private ThreadLocal<StringBuilder> errorTips = ThreadLocal.withInitial(StringBuilder::new);

    public CostReductImportListener(ImportContextVO importContextVO, ICostReductDao costReductDao) {
        this.importContextVO = importContextVO;
        this.costReductDao = costReductDao;
    }

    /**
     * 导入写入方法
     *
     * @param dataVO
     * @param analysisContext
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void invoke(CostReductImportVO dataVO, AnalysisContext analysisContext) {
        currentDataList.get().add(dataVO);
        if (currentDataList.get().size() >= 10000) {
            dataProcess();
        }
    }

    // 数据集合处理
    private void dataProcess() {
        List<CostReductImportVO> importVOList = currentDataList.get();
        // 校验导入的数据
        String errorMsg = checkImportData(importVOList);
        // 不存在问题数据，直接保存入库
        if (StringUtils.isBlank(errorMsg)) {
            saveBatchData(importVOList);
        } else {
            // 存在有问题的数据
            errorTips.get().append(errorMsg);
            String errorMessage = importContextVO.getErrorMsg();
            if (StringUtils.isBlank(errorMessage)) {
                importContextVO.setErrorMsg(errorTips.get().toString());
            } else {
                importContextVO.setErrorMsg(errorMessage.concat(errorTips.get().toString()));
            }
        }
        // 所有数据必须写入文件，并在个人中心里体现
        ExcelWriter workbookWriter = importContextVO.getWorkbookWriter();
        WriteSheet writeSheet = importContextVO.getWriteSheet();
        workbookWriter.write(importVOList, writeSheet);
        int batchNum = importContextVO.getBatchNum();
        importContextVO.setBatchNum(batchNum++);
        importContextVO.setTotalNum(importContextVO.getTotalNum() + importVOList.size());
        importVOList.clear();
        currentDataList.remove();
        errorTips.remove();
    }



    //  批量插入数据
    private void saveBatchData(List<CostReductImportVO> importDataList) {
        // 转换成需要保存的VO
        List<CostReductVO> saveDataList = new ArrayList<>();
        importDataList.stream().forEach(importVO -> {
            importVO.setVersionId(String.valueOf(importContextVO.getNewVersionId()));
            CostReductVO costReductVO = new CostReductVO();
            BeanUtils.copyProperties(importVO, costReductVO);
            saveDataList.add(costReductVO);
        });
        // 数据量小于1000条直接插入，否则分批插入
        if (saveDataList.size() <= 1000) {
            costReductDao.batchInsertCostReductVOs(saveDataList, importContextVO.getUserId());
            return;
        }
        Lists.partition(saveDataList, 1000).stream()
                .forEach(voList -> costReductDao.batchInsertCostReductVOs(voList, importContextVO.getUserId()));
    }

    // 处理完后
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        // 处理尾部数据，不足1w的数据
        if (!currentDataList.get().isEmpty()) {
            dataProcess();
        }
        lv1Map.clear();
        lv2Map.clear();
        checkDuplicateData.clear();
        log.info(">>>>>CostReductImportListener Import Completed!<<<<<");
    }

    private String checkImportData(List<CostReductImportVO> costReductDataList) {
        // 记录行数
        AtomicInteger lineCount = new AtomicInteger(1);
        StringBuilder builder = new StringBuilder();
        costReductDataList.stream().forEach(data -> {
            lineCount.addAndGet(1);
            checkOneData(data);
            if (StringUtils.isNotEmpty(data.getErrorMessage())) {
                builder.append("第" + (importContextVO.getBatchNum() * 10000 + lineCount.get()) + "行存在"
                        + StringUtils.defaultString(data.getErrorMessage()));
            }
        });
        return builder.toString();
    }

    private void checkOneData(CostReductImportVO importVO) {
        StringBuilder builder = new StringBuilder();
        // 填充L1和L2编码的信息
        CostReductVO costReductVO = setLv1AndLv2Code(importVO);
        BeanUtils.copyProperties(importVO, costReductVO);
        // 校验为空字段
        if (checkCostReductIfNullValue(costReductVO)) {
            builder.append("必输项为空的数据，不可保存;");
        }
        // 校验会计期的正确性(只能是当年+往前推两年)
        checkPeriodId(importVO, builder);
        // 校验字段长度
        if (CostReductUtils.checkCostReductDataLength(costReductVO)) {
            builder.append("字段过长，不可保存;");
        }
        // 校验降成本目标
        if (StringUtils.isNotBlank(costReductVO.getObjective()) && CostReductUtils.checkRatioValue(costReductVO)) {
            builder.append("降成本目标数据格式不正确，请检查数据;");
        }
        // 重新设置降成本目标的值，去除 "%"
        importVO.setObjective(costReductVO.getObjective());
        // 重复数据校验
        if (checkDuplicateData.contains(importVO.toString())) {
            builder.append("重复数据：").append(importVO.toString()).append(";");
        } else {
            checkDuplicateData.add(importVO.toString());
        }
        if (builder.length() > 0) {
            importVO.setErrorMessage(builder.toString());
        }
    }

    private void checkPeriodId(CostReductImportVO importVO, StringBuilder builder) {
        if (StringUtils.isNotBlank(importVO.getPeriodId())) {
            Year currentYear = Year.now();
            Year lastYear = currentYear.minusYears(1);
            Year twoYearsAgo = currentYear.minusYears(2);
            List<String> periodIdList = Arrays.asList(String.valueOf(currentYear.getValue()),
                    String.valueOf(lastYear.getValue()), String.valueOf(twoYearsAgo.getValue()));
            if (!periodIdList.stream().anyMatch(item -> item.equals(importVO.getPeriodId()))) {
                builder.append("会计期的值不正确，请检查;");
            }
        }
    }

    @NotNull
    private CostReductVO setLv1AndLv2Code(CostReductImportVO importVO) {
        CostReductVO costReductVO = new CostReductVO();
        if (lv1Map.isEmpty()) {
            costReductVO.setGroupLevel("LV1");
            List<CostReductVO> lv1DataList = costReductDao.getGroupLevelInfo(costReductVO);
            lv1Map = lv1DataList.stream().collect(Collectors.toMap(
                    CostReductVO::getLv1ProdRdTeamCnName, CostReductVO::getLv1ProdRndTeamCode, (v1, v2) -> v1));
            lv1Map.put("ALL", "ALL");
        }
        if (lv2Map.isEmpty()) {
            costReductVO.setGroupLevel("LV2");
            List<CostReductVO> lv2DataList = costReductDao.getGroupLevelInfo(costReductVO);
            lv2Map = lv2DataList.stream().collect(Collectors.toMap(
                    CostReductVO::getLv2ProdRdTeamCnName, CostReductVO::getLv2ProdRndTeamCode, (v1, v2) -> v1));
            lv2Map.put("ALL", "ALL");
        }
        if (StringUtils.isNotBlank(importVO.getLv1ProdRdTeamCnName())) {
            importVO.setLv1ProdRndTeamCode(lv1Map.get(importVO.getLv1ProdRdTeamCnName()));
        }
        if (StringUtils.isNotBlank(importVO.getLv2ProdRdTeamCnName())) {
            importVO.setLv2ProdRndTeamCode(lv2Map.get(importVO.getLv2ProdRdTeamCnName()));
        }
        return costReductVO;
    }

    public static boolean checkCostReductIfNullValue(CostReductVO vo) {
        // 字段不能为空
        return StringUtils.isAnyBlank(vo.getPeriodId(), vo.getLv1ProdRdTeamCnName(), vo.getObjective());
    }

}
