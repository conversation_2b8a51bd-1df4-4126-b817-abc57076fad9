/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.config;

import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.concurrent.Executor;

/**
 * ExecutorConfigTest Class
 *
 * <AUTHOR>
 * @since 2023/5/9
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class ExecutorConfigTest {

    @InjectMocks
    private ExecutorConfig executorConfig;

    @Test
    public void asyncServiceExecutor() {
        Executor executor = executorConfig.asyncServiceExecutor();
        Assertions.assertNotNull(executor);
    }

}