/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.impl.scheduler;

import com.huawei.it.fcst.enums.GroupTaskType;
import com.huawei.it.fcst.industry.price.dao.IDmPriceVirtualizedTaskDao;
import com.huawei.it.fcst.industry.price.vo.drop.DmFcstBasePriceCusDimVO;
import com.huawei.it.jalor5.core.annotation.NoJalorTransation;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.inject.Inject;

import java.io.Serializable;

/**
 * 月度func执行
 *
 * <AUTHOR>
 * @since 202407
 */
@Service("task.priceMonthTaskProcessService")
@Slf4j
public class PriceMonthTaskProcessService extends AbstractTaskProcessService {

    @Inject
    private IDmPriceVirtualizedTaskDao virtualizedTaskDao;
    @Override
    public GroupTaskType getTaskType() {
        return GroupTaskType.MONTH;
    }

    @Override
    @NoJalorTransation
    public Boolean process(Serializable serializable,Serializable beforeResult) {
        DmFcstBasePriceCusDimVO dmFcstBaseCusDimVO = (DmFcstBasePriceCusDimVO)serializable;
        log.info("MonthTaskProcessService:start->{},虚化id:{}", dmFcstBaseCusDimVO.getPageType(), dmFcstBaseCusDimVO.getCustomId());
        return FUNC_STATUS_SUCCESS.equalsIgnoreCase(serialCallMonthFunc(dmFcstBaseCusDimVO));
    }

    /**
     * 月度的串行执行 月度和月累计函数
     *
     * @param dmFcstBaseCusDimVO
     * @return
     */
    private String serialCallMonthFunc(DmFcstBasePriceCusDimVO dmFcstBaseCusDimVO) {
        if (!FUNC_STATUS_SUCCESS.equalsIgnoreCase(callMonthWeightFuncTask(dmFcstBaseCusDimVO))) {
            return FUNC_STATUS_FAIL;
        }
        if (!FUNC_STATUS_SUCCESS.equalsIgnoreCase(callMonthCostIdxFuncTask(dmFcstBaseCusDimVO))) {
            return FUNC_STATUS_FAIL;
        }
        return callMonthRateFuncTask(dmFcstBaseCusDimVO);
    }


    /**
     * 月度虚化权重函数
     *
     * @param dmFcstBaseCusDimVO 参数
     */
    private String callMonthWeightFuncTask(DmFcstBasePriceCusDimVO dmFcstBaseCusDimVO) {
        String result = virtualizedTaskDao.callMonthWeightFuncTask(dmFcstBaseCusDimVO);
        log.info("f_dm_fcst_price_base_cus_mon_weight_t:{},{},虚化id:{}", this.getTaskType().name(), result, dmFcstBaseCusDimVO.getCustomId());
        return result;
    }

    /**
     * 月度虚化指数函数
     *
     * @param dmFcstBaseCusDimVO 参数
     */
    private String callMonthCostIdxFuncTask(DmFcstBasePriceCusDimVO dmFcstBaseCusDimVO) {
        String result = virtualizedTaskDao.callMonthCostIdxFuncTask(dmFcstBaseCusDimVO);
        log.info("f_dm_fcst_price_base_cus_mon_cost_idx_t:{},{},虚化id:{}", this.getTaskType().name(), result, dmFcstBaseCusDimVO.getCustomId());
        return result;
    }


    /**
     * 月度虚化成本占比函数
     *
     * @param dmFcstBaseCusDimVO 参数
     */
    private String callMonthRateFuncTask(DmFcstBasePriceCusDimVO dmFcstBaseCusDimVO) {
        String result = virtualizedTaskDao.callMonthRateFuncTask(dmFcstBaseCusDimVO);
        log.info("f_dm_fcst_price_base_cus_mon_rate_t:{},{},虚化id:{}", this.getTaskType().name(), result, dmFcstBaseCusDimVO.getCustomId());
        return result;
    }
}
