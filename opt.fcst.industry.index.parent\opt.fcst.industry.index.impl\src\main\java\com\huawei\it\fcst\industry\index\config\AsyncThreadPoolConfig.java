/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.config;

import cn.hutool.core.map.MapUtil;
import com.huawei.it.fcst.industry.index.constant.Constant;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.map.HashedMap;
import org.slf4j.MDC;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Slf4j
@EnableAsync
@Configuration
public class AsyncThreadPoolConfig {
    private static final String THREAD_NAME = "dataAsyncThreadPool:";

    /**
     * 任务执行线程器 - 优化配置以提高性能和减少内存占用
     *
     * @return Executor
     */
    @Bean("taskExecutor")
    public Executor asyncThreadPoolConfig() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 根据CPU核心数动态调整线程数
        int availableProcessors = Runtime.getRuntime().availableProcessors();

        // 核心线程数：CPU核心数 * 2，适合IO密集型任务
        executor.setCorePoolSize(availableProcessors * 2);

        // 最大线程数：CPU核心数 * 4，确保有足够的并发处理能力
        executor.setMaxPoolSize(Math.max(availableProcessors * 4, 50));
        executor.setQueueCapacity(Constant.PoolNumEnum.QUEUE_CAPACITY.getValue());
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        // 增加线程保活时间，减少线程创建销毁开销
        executor.setKeepAliveSeconds(300); // 5分钟

        // 增加等待终止时间
        executor.setAwaitTerminationSeconds(120); // 2分钟

        // 允许核心线程超时，在空闲时释放资源
        executor.setAllowCoreThreadTimeOut(true);

        executor.setTaskDecorator((runnable) -> {
            Map<String, String> contextMap = new HashedMap<>();
            // 获取当前线程的MDC上下文
            Map<String, String> copyOfContextMap = MDC.getCopyOfContextMap();
            if (MapUtil.isNotEmpty(copyOfContextMap)) {
                contextMap.putAll(copyOfContextMap);
            }
            return () -> {
                try {
                    RequestContextManager.buildBasicContext();
                    // 将MDC上下文设置到子线程
                    MDC.setContextMap(contextMap);
                    // 执行实际任务
                    runnable.run();
                } finally {
                    MDC.clear();
                    RequestContextManager.removeCurrent();
                }
            };
        });

        executor.initialize();

        log.info("线程池配置 - 核心线程数: {}, 最大线程数: {}, 队列容量: {}", executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
        return executor;
    }
}