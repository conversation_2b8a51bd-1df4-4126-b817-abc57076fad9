/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.config;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import com.huawei.it.fcst.industry.index.constant.Constant;

@EnableAsync
@Configuration
public class AsyncThreadPoolConfig {
    private static final String THREAD_NAME = "dataAsyncThreadPool:";

    /**
     * 任务执行线程器
     *
     * @return Executor
     */
    @Bean("taskExecutor")
    public Executor asyncThreadPoolConfig() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(30);
        executor.setMaxPoolSize(50);
        executor.setQueueCapacity(Constant.PoolNumEnum.QUEUE_CAPACITY.getValue());
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setThreadNamePrefix(THREAD_NAME);
        executor.setKeepAliveSeconds(60);
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        return executor;
    }
}