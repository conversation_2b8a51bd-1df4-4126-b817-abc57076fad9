/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.mix;

import cn.hutool.json.JSONObject;
import com.huawei.it.fcst.enums.ResultCodeEnum;
import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstDataRefreshStatusDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstVersionInfoDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFocVarifyTaskDao;
import com.huawei.it.fcst.industry.pbi.dao.IctMonthCostIdxDao;
import com.huawei.it.fcst.industry.pbi.enums.GroupLevelEnum;
import com.huawei.it.fcst.industry.pbi.enums.IndustryConstEnum;
import com.huawei.it.fcst.industry.pbi.impl.annual.AnnualAmpPbiService;
import com.huawei.it.fcst.industry.pbi.impl.common.AsyncIctQueryService;
import com.huawei.it.fcst.industry.pbi.impl.common.DropDownService;
import com.huawei.it.fcst.industry.pbi.impl.common.IctCommonService;
import com.huawei.it.fcst.industry.pbi.impl.month.AsyncMonthAnalysisService;
import com.huawei.it.fcst.industry.pbi.service.common.IIctCommonService;
import com.huawei.it.fcst.industry.pbi.service.mix.IMixManagementService;
import com.huawei.it.fcst.industry.pbi.utils.FcstIndustryUtil;
import com.huawei.it.fcst.industry.pbi.vo.comb.CombTransformVO;
import com.huawei.it.fcst.industry.pbi.vo.common.DmFcstDataRefreshStatus;
import com.huawei.it.fcst.industry.pbi.vo.common.DmFocVarifyTaskVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstDimInfoVO;
import com.huawei.it.fcst.industry.pbi.vo.mix.DmFocMixResultVO;
import com.huawei.it.fcst.industry.pbi.vo.mix.MixSearchVO;
import com.huawei.it.fcst.industry.pbi.vo.month.IctMonthAnalysisVO;
import com.huawei.it.fcst.util.ExcelExportUtil;
import com.huawei.it.fcst.util.ObjectCopyUtil;
import com.huawei.it.fcst.util.UserInfoUtils;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.security.RoleVO;
import com.huawei.it.jalor5.security.UserVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Named;
import javax.servlet.http.HttpServletResponse;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * MixManagementService Class
 *
 * <AUTHOR>
 * @since 2024/7/5
 */
@Named("mixManagementService")
@JalorResource(code = "mixManagementService", desc = "new ICT-勾稽管理页面")
public class MixManagementService implements IMixManagementService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MixManagementService.class);

    @Autowired
    private MixCommonService mixCommonService;

    @Autowired
    private AnnualAmpPbiService annualAmpPbiService;

    @Autowired
    private IDmFcstVersionInfoDao dmFcstVersionInfoDao;

    @Autowired
    private IctCommonService ictCommonService;

    @Autowired
    private IDmFocVarifyTaskDao varifyTaskDao;

    @Autowired
    private IIctCommonService commonService;

    @Autowired
    private IctMonthCostIdxDao ictMonthCostIdxDao;

    @Autowired
    private AsyncMonthAnalysisService asyncMonthAnalysisService;

    @Autowired
    private IDmFcstDataRefreshStatusDao dmFcstDataRefreshStatusDao;

    @Autowired
    private AsyncIctQueryService asyncIctQueryService;

    @Autowired
    private DropDownService dropDownService;

    public static final String BLUR_TRUE = "true";

    @JalorOperation(code = "distributeChart", desc = "成本分布图")
    @Override
    public ResultDataVO distributeChart(MixSearchVO mixSearchVO) throws CommonApplicationException, ExecutionException, InterruptedException {
        dropDownService.setPermissionParameter(mixSearchVO);
        checkTablePreFixParam(mixSearchVO);
        mixSearchVO.setVersionId(dmFcstVersionInfoDao.findVersionIdByDataType("MONTH").getVersionId());
        FcstIndustryUtil.setRegionCode(mixSearchVO);
        // 需要去查询到该阈值下的所有满足条件的spart
        getRatioSpartList(mixSearchVO);
        getProdTeamCodeListByType(mixSearchVO);
        List<DmFocMixResultVO> allDistributeList = new ArrayList<>();
        List<String> costTypeList = mixSearchVO.getCostTypeList();
        for (int i = 0; i < costTypeList.size(); i++) {
            String costType = costTypeList.get(i);
            mixSearchVO.setCostType(costType);
            // 查询数据库
            List<DmFocMixResultVO> distributeResult = asyncIctQueryService.findDistributeResult(mixSearchVO, mixSearchVO.getIsNeedBlur());
            allDistributeList.addAll(distributeResult);
        }
        // 金额运算
        List<DmFocMixResultVO> dmFocMixResultVOList = asyncIctQueryService.calGapRmb(allDistributeList, mixSearchVO);
        setGroupCnNameDimensionLevel(dmFocMixResultVOList);
        concatMixPercent(dmFocMixResultVOList);
        return ResultDataVO.success(dmFocMixResultVOList);
    }

    @JalorOperation(code = "differenceChart", desc = "差异明细")
    @Override
    public ResultDataVO differenceChart(MixSearchVO mixSearchVO) throws CommonApplicationException, ExecutionException, InterruptedException {
        dropDownService.setPermissionParameter(mixSearchVO);
        checkTablePreFixParam(mixSearchVO);
        mixSearchVO.setVersionId(dmFcstVersionInfoDao.findVersionIdByDataType("MONTH").getVersionId());
        FcstIndustryUtil.setRegionCode(mixSearchVO);
        // 需要去查询到该阈值下的所有满足条件的spart
        getRatioSpartList(mixSearchVO);
        getProdTeamCodeListByType(mixSearchVO);
        List<DmFocMixResultVO> allDifferList = new ArrayList<>();
        List<String> costTypeList = mixSearchVO.getCostTypeList();
        for (int i = 0; i < costTypeList.size(); i++) {
            String costType = costTypeList.get(i);
            mixSearchVO.setCostType(costType);
            List<DmFocMixResultVO> differResult = asyncIctQueryService.findDistributeResult(mixSearchVO, mixSearchVO.getIsNeedBlur());
            allDifferList.addAll(differResult);
        }
        // 金额运算
        List<DmFocMixResultVO> dmFocMixResultList = asyncIctQueryService.calGapRmb(allDifferList, mixSearchVO);
        setGroupCnNameDimensionLevel(dmFocMixResultList);
        concatMixPercent(dmFocMixResultList);
        return ResultDataVO.success(dmFocMixResultList);
    }

    @JalorOperation(code = "currentPriceIndexChart", desc = "单指数图")
    @Override
    public ResultDataVO currentPriceIndexChart(MixSearchVO mixSearchVO) throws CommonApplicationException {
        dropDownService.setPermissionParameter(mixSearchVO);
        checkTablePreFixParam(mixSearchVO);
        mixSearchVO.setVersionId(dmFcstVersionInfoDao.findVersionIdByDataType("MONTH").getVersionId());
        FcstIndustryUtil.setRegionCode(mixSearchVO);

        List<DmFocMixResultVO> allPriceIndexList = new ArrayList<>();
        // 查询数据库
        mixCommonService.getDbforCurrentPriceIndex(mixSearchVO, allPriceIndexList);

        setGroupCnNameDimensionLevel(allPriceIndexList);
        return ResultDataVO.success(allPriceIndexList);
    }

    @JalorOperation(code = "multiPriceIndexChart", desc = "多指数图")
    @Override
    public ResultDataVO multiPriceIndexChart(MixSearchVO mixSearchVO) throws CommonApplicationException, ExecutionException, InterruptedException {
        dropDownService.setPermissionParameter(mixSearchVO);
        checkTablePreFixParam(mixSearchVO);
        PageVO pageVO = new PageVO();
        // 每页6个code
        pageVO.setPageSize(mixSearchVO.getPageSize());
        pageVO.setCurPage(mixSearchVO.getPageIndex());
        // 查询区间年
        List<String> yearList = annualAmpPbiService.getYearList(mixSearchVO.getGranularityType());
        if (CollectionUtils.isNotEmpty(yearList) && yearList.size() >= 2) {
            mixSearchVO.setPeriodYear(yearList.get(1) + "-" + yearList.get(0));
        }
        mixSearchVO.setVersionId(dmFcstVersionInfoDao.findVersionIdByDataType("MONTH").getVersionId());
        FcstIndustryUtil.setRegionCode(mixSearchVO);
        // 查询满足条件的所有spart
        getRatioSpartList(mixSearchVO);
        getProdTeamCodeListByType(mixSearchVO);
        List<String> costTypeList = mixSearchVO.getCostTypeList();
        List<DmFocMixResultVO> allPriceIndexList = new ArrayList<>();
        String isNeedBlur = mixSearchVO.getIsNeedBlur();
        for (int i = 0; i < costTypeList.size(); i++) {
            String costType = costTypeList.get(i);
            mixSearchVO.setCostType(costType);
            if (CommonConstant.BLUR_TRUE.equals(isNeedBlur)) {
                // 虚化的
                Future<List<DmFocMixResultVO>> customPriceIndexList = asyncIctQueryService.findMultiCustomPriceIndexList(mixSearchVO);
                while (true) {
                    if (customPriceIndexList.isDone()) {
                        break;
                    }
                }
                allPriceIndexList.addAll(customPriceIndexList.get());
            } else if (CommonConstant.BLUR_TWO_CONDITION.equals(isNeedBlur)) {
                // 虚化+正常项
                List<DmFocMixResultVO> mixPriceIndexList = asyncIctQueryService.findMultiPriceIndex(mixSearchVO);
                allPriceIndexList.addAll(mixPriceIndexList);
            } else {
                // 只有正常项时
                Future<List<DmFocMixResultVO>> multiNormalPriceIndexList = asyncIctQueryService.findMultiNormalPriceIndexList(mixSearchVO);
                while (true) {
                    if (multiNormalPriceIndexList.isDone()) {
                        break;
                    }
                }
                allPriceIndexList.addAll(multiNormalPriceIndexList.get());
            }
        }
        // 分页
        List<DmFocMixResultVO> dmFocMixResultPageList = limitMixGroupCodePage(allPriceIndexList, pageVO);

        Map<String, Object> result = new HashMap<>();
        setGroupCnNameDimensionLevel(dmFocMixResultPageList);
        result.put("result", dmFocMixResultPageList);
        result.put("pageVo", pageVO);
        return ResultDataVO.success(result);
    }

    @JalorOperation(code = "priceIndexChildList", desc = "指数结果表")
    @Override
    public ResultDataVO priceIndexChildList(MixSearchVO mixSearchVO) throws CommonApplicationException, ExecutionException, InterruptedException {
        dropDownService.setPermissionParameter(mixSearchVO);
        checkTablePreFixParam(mixSearchVO);
        // 获取最新的version_id
        mixSearchVO.setVersionId(dmFcstVersionInfoDao.findVersionIdByDataType("MONTH").getVersionId());
        FcstIndustryUtil.setRegionCode(mixSearchVO);
        // 查询满足条件的所有spart
        getRatioSpartList(mixSearchVO);
        getProdTeamCodeListByType(mixSearchVO);
        List<DmFocMixResultVO> allMixResultList = new ArrayList<>();
        List<String> costTypeList = mixSearchVO.getCostTypeList();
        String isNeedBlur = mixSearchVO.getIsNeedBlur();
        for (int i = 0; i < costTypeList.size(); i++) {
            String costType = costTypeList.get(i);
            mixSearchVO.setCostType(costType);
            if (CommonConstant.BLUR_TRUE.equals(isNeedBlur)) {
                // 虚化
                Future<List<DmFocMixResultVO>> dmFocMixResultList = asyncIctQueryService.blurForPriceIndexChild(mixSearchVO, "data");
                while (true) {
                    if (dmFocMixResultList.isDone()) {
                        break;
                    }
                }
                List<DmFocMixResultVO> dmFocMixResultVOList = dmFocMixResultList.get();
                dmFocMixResultVOList.stream().forEach(dm -> dm.setCostType(mixSearchVO.getCostType()));
                allMixResultList.addAll(dmFocMixResultVOList);
            } else if (CommonConstant.BLUR_TWO_CONDITION.equals(isNeedBlur)) {
                List<DmFocMixResultVO> twoMixResultList = asyncIctQueryService.findPriceIndexResult(mixSearchVO, "data");
                twoMixResultList.stream().forEach(dm -> dm.setCostType(mixSearchVO.getCostType()));
                allMixResultList.addAll(twoMixResultList);
            } else {
                Future<List<DmFocMixResultVO>> normalPriceIndex = asyncIctQueryService.notBlurForPriceIndexChild(mixSearchVO, "data");
                List<DmFocMixResultVO> dmFocMixList = normalPriceIndex.get();
                dmFocMixList.stream().forEach(dm -> dm.setCostType(mixSearchVO.getCostType()));
                allMixResultList.addAll(dmFocMixList);
            }
        }
        PageVO pageVO = new PageVO();
        // 每页6个code
        pageVO.setPageSize(mixSearchVO.getPageSize());
        pageVO.setCurPage(mixSearchVO.getPageIndex());
        // 分页
        List<DmFocMixResultVO> dmFocMixResultPageList = limitMixGroupCodePage(allMixResultList, pageVO);

        Map result = new LinkedHashMap();
        result.put("result", dmFocMixResultPageList);
        result.put("pageVo", pageVO);
        return ResultDataVO.success(result);
    }

    @JalorOperation(code = "switchIndexBasePeriodId", desc = "切换基期")
    @Override
    public ResultDataVO switchIndexBasePeriodId(MixSearchVO mixSearchVO) throws ApplicationException {
        // 必填字段校验
        if (verifyMixParameters(mixSearchVO)) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        checkTablePreFixParam(mixSearchVO);
        // 设置版本ID和报告期开始时间与结束时间
        setRefreshParams(mixSearchVO);
        // 任务状态初始化设置
        DmFocVarifyTaskVO varifyTaskVO = new DmFocVarifyTaskVO();
        varifyTaskVO.setTaskType("ict_data_index");
        // 判断是否需要切换基期
        if (isPriceDataIsOk(mixSearchVO)) {
            varifyTaskVO.setStatus("SUCCESS");
        } else {
            varifyTaskVO.setStatus("PROCESSING");
            refreshMixDataByFunction(new JSONObject(mixSearchVO).toString(), varifyTaskVO, "normal");
        }
        // 汇总组合判断是否需要切换基期
        if (null != mixSearchVO.getCustomId()) {
            MixSearchVO searchVO = ObjectCopyUtil.copy(mixSearchVO, MixSearchVO.class);
            List<Long> customIdList = new ArrayList<>();
            customIdList.add(mixSearchVO.getCustomId());
            searchVO.setCombIdList(customIdList);
            List<String> customStrList = new ArrayList<>();
            customStrList.add(String.valueOf(mixSearchVO.getCustomId()));
            searchVO.setGroupCodeList(customStrList);
            varifyTaskVO.setCombStatus("PROCESSING");
            refreshMixDataByFunction(new JSONObject(searchVO).toString(), varifyTaskVO,"comb");
        }
        return ResultDataVO.success(varifyTaskVO);
    }

    @JalorOperation(code = "multiSpartCalculate", desc = "多选spart计算当前层级指数图")
    @Audit(module = "mixManagementService-multiSpartCalculate", operation = "multiSpartCalculate", message = "多选spart计算当前层级指数图")
    @Override
    public ResultDataVO multiSpartCalculate(MixSearchVO mixSearchVO) throws ApplicationException, InterruptedException {
        // 必填字段校验
        if (verifyMixParameters(mixSearchVO)) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        checkTablePreFixParam(mixSearchVO);

        mixSearchVO.setVersionId(commonService.getVersionId("MONTH"));
        FcstIndustryUtil.setSpecailCode(mixSearchVO);
        // 任务状态初始化设置
        DmFocVarifyTaskVO varifyTaskVO = new DmFocVarifyTaskVO();
        varifyTaskVO.setTaskType("ict_calculate_current_index");
        varifyTaskVO.setStatus("PROCESSING");
        // 只输入阈值
        if (null != mixSearchVO.getRatioPspStd() && !GroupLevelEnum.SPART.getValue().equals(mixSearchVO.getGroupLevel())) {
            String costType = mixSearchVO.getCostTypeList().stream().collect(Collectors.joining("_"));
            mixSearchVO.setCostType(costType);
            List<DmFcstDimInfoVO> gapSpartCodeList = dropDownService.getCostGapSpartCodeList(mixSearchVO);
            List<String> spartCodeList = gapSpartCodeList.stream().map(DmFcstDimInfoVO::getGroupCode).collect(Collectors.toList());
            mixSearchVO.setSpartCodeList(spartCodeList);
            mixSearchVO.setRatioRateFlag(true);
        }
        getCurrentPriceDataByFunction(mixSearchVO, varifyTaskVO);
        return ResultDataVO.success(varifyTaskVO);
    }

    public void getCurrentPriceDataByFunction(MixSearchVO mixSearchVO, DmFocVarifyTaskVO varifyTaskVO) throws InterruptedException, ApplicationException {

        // 生成一个新的taskId作为customId
        if (null == varifyTaskVO.getTaskId()) {
            Timestamp time = new Timestamp(System.currentTimeMillis());
            Long customId = Long.parseLong(new SimpleDateFormat("yyyyMMddHHmmsss").format(time));
            mixSearchVO.setCustomId(customId);
            varifyTaskVO.setTaskId(customId);
            varifyTaskVO.setPeriodId(mixSearchVO.getBasePeriodId());
            varifyTaskDao.insertVerifyTask(varifyTaskVO);
        } else {
            varifyTaskDao.updateVerifyTask(varifyTaskVO);
        }
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        Long userId = currentUser.getUserId();
        String userAccount = currentUser.getUserAccount();
        RoleVO currentRole = currentUser.getCurrentRole();
        String roleId = String.valueOf(currentRole.getRoleId());
        mixSearchVO.setRoleId(roleId);
        mixSearchVO.setUserId(String.valueOf(userId));
        mixSearchVO.setUserAccount(userAccount);
        // 异步调用计算指数的函数
        asyncMonthAnalysisService.getCurrentIndexDataByFunction(mixSearchVO, varifyTaskVO);
    }

    private void getRatioSpartList(MixSearchVO mixSearchVO) {
        if (null != mixSearchVO.getRatioPspStd() && !GroupLevelEnum.SPART.getValue().equals(mixSearchVO.getGroupLevel())) {
            MixSearchVO mixSearch = ObjectCopyUtil.copy(mixSearchVO, MixSearchVO.class);
            String costType = mixSearch.getCostTypeList().stream().collect(Collectors.joining("_"));
            mixSearch.setCostType(costType);
            List<DmFcstDimInfoVO> gapSpartCodeList = dropDownService.getCostGapSpartCodeList(mixSearch);
            List<String> spartCodeList = gapSpartCodeList.stream().map(DmFcstDimInfoVO::getGroupCode).collect(Collectors.toList());
            mixSearchVO.setIsNeedBlur(CommonConstant.BLUR_FALSE);
            mixSearchVO.setPspGroupCodeList(spartCodeList);
            mixSearchVO.setStdGroupCodeList(spartCodeList);
            mixSearchVO.setSpartCodeList(spartCodeList);
            mixSearchVO.setGroupCodeList(spartCodeList);
            mixSearchVO.setGroupLevel(GroupLevelEnum.SPART.getValue());
            mixSearchVO.setRatioRateFlag(true);
        }
    }

    private void getProdTeamCodeListByType(MixSearchVO mixSearchVO) {
        List<String> spartCodeList = mixSearchVO.getSpartCodeList();
        // 多选时spart 或者只输入阈值才需要设置
        if ((GroupLevelEnum.SPART.getValue().equals(mixSearchVO.getGroupLevel()) && spartCodeList.size()>1) || mixSearchVO.isRatioRateFlag()) {
            List<String> lv4ProdRndTeamCodeList = mixSearchVO.getLv4ProdRndTeamCodeList();
            if (CollectionUtils.isNotEmpty(lv4ProdRndTeamCodeList)) {
                mixSearchVO.setPspProdRndTeamCodeList(lv4ProdRndTeamCodeList);
                mixSearchVO.setStdProdRndTeamCodeList(lv4ProdRndTeamCodeList);
            } else {
                // 如果没有选LV3.5，多选spart，需要找到lv3.5，然后设置 PspProdRndTeamCodeList和StdProdRndTeamCodeList
                asyncMonthAnalysisService.getLv4ProdTeamCode(mixSearchVO);
            }
        }

    }

    private List<DmFocMixResultVO> limitMixGroupCodePage(List<DmFocMixResultVO> allMixResultVOList, PageVO pageVO) {

        Map<String, List<DmFocMixResultVO>> resultMixMap = allMixResultVOList.stream().collect(
                Collectors.groupingBy(item -> item.getProdRndTeamCnName() + "_" + item.getParentCode() + "_" + item.getGroupCode(), LinkedHashMap::new, Collectors.toList()));

        int count = resultMixMap.size();
        pageVO.setTotalRows(count);
        int curPage = pageVO.getCurPage() - 1;
        int pageSize = pageVO.getPageSize();
        Set<String> resultSet = resultMixMap.keySet();
        List<String> resultList = new ArrayList<>();
        resultList.addAll(resultSet);

        List<String> resultCodePageResult = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(resultList) && count > 0) {
            int fromIndex = curPage * pageSize;
            int totalIndex = (curPage + 1) * pageSize;
            if (totalIndex > count) {
                totalIndex = count;
            }
            resultCodePageResult = resultList.subList(fromIndex, totalIndex);
        }
        List<DmFocMixResultVO> dmFocMixResultVOList = new ArrayList<>();
        resultCodePageResult.stream().forEach(code -> dmFocMixResultVOList.addAll(resultMixMap.get(code)));
        return dmFocMixResultVOList;
    }

    public void refreshMixDataByFunction(String jsonStr, DmFocVarifyTaskVO varifyTaskVO, String flag) {
        if (null == varifyTaskVO.getTaskId()) {
            varifyTaskVO.setTaskId(varifyTaskDao.getVerifyTaskId());
            varifyTaskVO.setPeriodId(new JSONObject(jsonStr).getInt("basePeriodId"));
            varifyTaskDao.insertVerifyTask(varifyTaskVO);
        } else {
            varifyTaskDao.updateVerifyTask(varifyTaskVO);
        }
        // 异步调用函数
        if ("normal".equals(flag)) {
            asyncMonthAnalysisService.refreshDataByFunction(jsonStr, varifyTaskVO);
        } else {
            asyncMonthAnalysisService.refreshCombDataByFunction(jsonStr, varifyTaskVO);
        }
    }

    /**
     * 判断数据是否准备好
     *
     * @param mixSearchVO 查询参数
     * @return boolean true or false
     */
    public boolean isPriceDataIsOk(MixSearchVO mixSearchVO) {
        // 默认基期不切换
        IctMonthAnalysisVO monthAnalysisVO = new IctMonthAnalysisVO();
        BeanUtils.copyProperties(mixSearchVO, monthAnalysisVO);
        // 如果是输入阈值且没有选spart时，查到所有符合的spart
        getRatioSpartList(mixSearchVO);
        monthAnalysisVO.setGroupCodeList(mixSearchVO.getGroupCodeList());
        String basePeriodId = ictMonthCostIdxDao.findMixBasePeriodId(monthAnalysisVO);
        if (ifBasePeriodId(monthAnalysisVO, basePeriodId)) {
            return true;
        }
        // 切换基期数据量设置
        IctMonthAnalysisVO monthAnalysis = ObjectCopyUtil.copy(monthAnalysisVO, IctMonthAnalysisVO.class);
        monthAnalysis.setBasePeriodId(Integer.valueOf(basePeriodId));
        int defaultPeriodCount = ictMonthCostIdxDao.findMixCostIndexCount(monthAnalysis);
        int costIndexCount = ictMonthCostIdxDao.findMixCostIndexCount(monthAnalysisVO);
        return costIndexCount >= defaultPeriodCount;
    }

    private int getPriceIndexCount(IctMonthAnalysisVO monthAnalysisVO) {
        int result = 0;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        Calendar from = Calendar.getInstance();
        Calendar to = Calendar.getInstance();
        try {
            from.setTime(sdf.parse(String.valueOf(monthAnalysisVO.getPeriodStartTime())));
            to.setTime(sdf.parse(String.valueOf(monthAnalysisVO.getPeriodEndTime())));
            int fromYear = from.get(Calendar.YEAR);
            int toYear = to.get(Calendar.YEAR);
            int fromMonth = from.get(Calendar.MONTH);
            int toMonth = to.get(Calendar.MONTH);
            result = (toYear - fromYear) * 12 + toMonth - fromMonth + 1;
        } catch (ParseException ex) {
            LOGGER.error("类型转换错误");
        }
        return result;
    }

    private boolean verifyMixParameters(MixSearchVO mixSearchVO) {
        return ObjectUtils.isEmpty(mixSearchVO.getBasePeriodId()) || CollectionUtils.isEmpty(mixSearchVO.getCostTypeList())
                || StringUtils.isAnyBlank(mixSearchVO.getGranularityType(),
                mixSearchVO.getOverseaFlag(), mixSearchVO.getBgCode(), mixSearchVO.getRegionCode(),
                mixSearchVO.getRepofficeCode());
    }

    private void setRefreshParams(MixSearchVO mixSearchVO) {
        mixSearchVO.setVersionId(commonService.getVersionId("MONTH"));
        // 基期开始时间和结束时间
        String mixActualMonth = dmFcstVersionInfoDao.findMixActualMonth(mixSearchVO);
        FcstIndustryUtil.handlePeriod(mixSearchVO, mixActualMonth);
        FcstIndustryUtil.setRegionCode(mixSearchVO);
        String costType = mixSearchVO.getCostTypeList().stream().collect(Collectors.joining(","));
        mixSearchVO.setCostType(costType);
        mixSearchVO.setPageType("MIX");
        List<String> prodRndTeamCodeList = new ArrayList<>();
        prodRndTeamCodeList.addAll(mixSearchVO.getPspProdRndTeamCodeList());
        prodRndTeamCodeList.addAll(mixSearchVO.getStdProdRndTeamCodeList());
        List<String> prodRndTeamCode = prodRndTeamCodeList.stream().distinct().collect(Collectors.toList());
        mixSearchVO.setProdRndTeamCodeList(prodRndTeamCode);
    }

    private boolean ifBasePeriodId(IctMonthAnalysisVO monthAnalysisVO, String basePeriodId) {

        return basePeriodId.equals(String.valueOf(monthAnalysisVO.getBasePeriodId()));
    }

    @JalorOperation(code = "mixDetail", desc = "勾稽管理-数据下载")
    @Transactional(rollbackFor = Exception.class)
    @Audit(module = "mixManagementService-mixDetail", operation = "mixDetail", message = "勾稽管理-数据下载")
    @Override
    public ResultDataVO mixDetail(HttpServletResponse response, MixSearchVO mixSearchVO) throws Exception {
        dropDownService.setPermissionParameter(mixSearchVO);
        long start = System.currentTimeMillis();
        Long userId = UserInfoUtils.getUserId();
        DmFcstDataRefreshStatus dataRefreshStatus = new DmFcstDataRefreshStatus();
        setDmFcstDataRefreshStatus(dataRefreshStatus, userId, mixSearchVO);

        // 创建异步任务记录，进行新增操作
        dmFcstDataRefreshStatusDao.createDmFcstDataRefreshStatus(dataRefreshStatus);

        checkTablePreFixParam(mixSearchVO);
        // 设置标题
        ictCommonService.setExcelDisplayName(mixSearchVO);
        // 设置参数
        mixSearchVO.setVersionId(dmFcstVersionInfoDao.findVersionIdByDataType("MONTH").getVersionId());
        FcstIndustryUtil.setRegionCode(mixSearchVO);
        // 查询权重区间年
        List<String> yearList = annualAmpPbiService.getYearList(mixSearchVO.getGranularityType());
        if (CollectionUtils.isNotEmpty(yearList) && yearList.size() >= 2) {
            mixSearchVO.setPeriodYear(yearList.get(1) + "-" + yearList.get(0));
        }
        // 查询满足条件的所有spart
        getRatioSpartList(mixSearchVO);
        getProdTeamCodeListByType(mixSearchVO);
        if ("Y".equals(mixSearchVO.getMainFlag())) {
            // 编码属性
            mixSearchVO.setCodeAttributesCnName("编码属性：" + mixSearchVO.getCodeAttributes());
        } else {
            // 不选spart范围，或spart范围选择N
            mixSearchVO.setCodeAttributesCnName("");
        }
        String softwareMarkCnName = IndustryConstEnum.getSoftwareMark(mixSearchVO.getSoftwareMark()).getDesc();
        mixSearchVO.setSoftwareMarkCnName(softwareMarkCnName);
        String mixActualMonth = dmFcstVersionInfoDao.findMixActualMonth(mixSearchVO);
        mixSearchVO.setActualMonth(mixActualMonth);
        // 获取模板
        String exportTemplate = getMixTemplate(mixSearchVO);
        // 导出模块为空表示无权限下载，直接返回
        if (StringUtils.isEmpty(exportTemplate)) {
            return ResultDataVO.failure(ResultCodeEnum.NOT_PERMISSION.getCode(),
                    CommonConstant.NO_PERMISSION_TO_DOWNLOAD);
        }
        mixSearchVO.setExportTemplate(exportTemplate);
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate(exportTemplate);
        CombTransformVO combTransformVO = new CombTransformVO();
        setComTransform(combTransformVO, dataRefreshStatus, userId);

        mixSearchVO.setCreationDate(new Timestamp(start));
        asyncMonthAnalysisService.asyncExportMixData(mixSearchVO, workbook, combTransformVO);
        return ResultDataVO.success(dataRefreshStatus);
    }

    private void setDmFcstDataRefreshStatus(DmFcstDataRefreshStatus dataRefreshStatus, long userId, MixSearchVO mixSearchVO) {
        dataRefreshStatus.setStatus("TASK_INIT");
        dataRefreshStatus.setDelFlag("N");

        dataRefreshStatus.setCreatedBy(userId);
        dataRefreshStatus.setLastUpdatedBy(userId);
        dataRefreshStatus.setCreationDate(new Date());
        List<String> costTypeList = mixSearchVO.getCostTypeList();

        String costTypeStr = costTypeList.stream().collect(Collectors.joining("_"));
        dataRefreshStatus.setTaskFlag("MIX_" + costTypeStr + "_EXPORT_" + mixSearchVO.getGranularityType());
        dataRefreshStatus.setTaskId(dmFcstDataRefreshStatusDao.getDataRefrashKey());
        dataRefreshStatus.setRoleId(UserInfoUtils.getRoleId());
    }

    private void setComTransform(CombTransformVO combTransformVO, DmFcstDataRefreshStatus dataRefreshStatus, long userId) {
        combTransformVO.setTaskId(dataRefreshStatus.getTaskId());
        combTransformVO.setUserId(userId);
        IRequestContext current = RequestContextManager.getCurrent();
        combTransformVO.setCurrent(current);
    }

    private String getMixTemplate(MixSearchVO mixSearchVO) throws CommonApplicationException {
        String exportTemplate;
        // 行销分析师
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        if (CommonConstant.SPECIAL_ROLES.contains(currentRole.getRoleName())) {
            if (null != mixSearchVO.getRatioPspStd()) {
                exportTemplate = CommonConstant.INDUSTRY_MIX_TEMPLATE4_PATH;
            } else if (CollectionUtils.isNotEmpty(mixSearchVO.getLv4ProdRndTeamCodeList()) && GroupLevelEnum.LV4.getValue().equals(mixSearchVO.getGroupLevel())) {
                exportTemplate = CommonConstant.INDUSTRY_MIX_TEMPLATE3_PATH;
            } else {
                exportTemplate = getMixSubTemplate(mixSearchVO);
            }
        } else {
            exportTemplate = getMixSubTemplate(mixSearchVO);
        }
        return exportTemplate;
    }

    @NotNull
    private String getMixSubTemplate(MixSearchVO mixSearchVO) {
        String exportTemplate;
        if (mixSearchVO.getIsNeedBlur().contains(BLUR_TRUE)) {
            if (mixSearchVO.isRatioRateFlag() || GroupLevelEnum.SPART.getValue().equals(mixSearchVO.getGroupLevel())) {
                exportTemplate = CommonConstant.INDUSTRY_MIX_TEMPLATE5_PATH;
            } else {
                exportTemplate = CommonConstant.INDUSTRY_MIX_TEMPLATE1_PATH;
            }
        } else {
            // 不虚化时,不选LV3.5,选量纲维度/spart,只能看当前层级指数和指数结果表
            boolean flag = StringUtils.isNotEmpty(mixSearchVO.getDimensionCode()) || StringUtils.isNotEmpty(mixSearchVO.getDimensionSubCategoryCode()) || StringUtils.isNotEmpty(mixSearchVO.getDimensionSubDetailCode());
            boolean conditon = CollectionUtils.isEmpty(mixSearchVO.getLv4ProdRdTeamCnNameList()) && (CollectionUtils.isNotEmpty(mixSearchVO.getSpartCodeList()) || flag);
            if (mixSearchVO.isRatioRateFlag()) {
                exportTemplate = CommonConstant.INDUSTRY_MIX_TEMPLATE5_PATH;
            } else if (conditon || CommonConstant.MIN_GROUP_LEVEL.stream().anyMatch(level -> level.equals(mixSearchVO.getGroupLevel()))) {
                if (GroupLevelEnum.SPART.getValue().equals(mixSearchVO.getGroupLevel()) && mixSearchVO.getSpartCodeList().size() > 1) {
                    exportTemplate = CommonConstant.INDUSTRY_MIX_TEMPLATE5_PATH;
                } else {
                    exportTemplate = CommonConstant.INDUSTRY_MIX_TEMPLATE2_PATH;
                }
            } else {
                exportTemplate = CommonConstant.INDUSTRY_MIX_TEMPLATE1_PATH;
            }
        }
        return exportTemplate;
    }

    private void checkTablePreFixParam(MixSearchVO mixSearchVO) throws CommonApplicationException {
        if (!CommonConstant.COST_TYPE_LIST.containsAll(mixSearchVO.getCostTypeList())) {
            throw new CommonApplicationException("成本类型错误");
        }
        if (!CommonConstant.GRANULARITY_TYPE_LIST.stream().anyMatch(cost -> cost.equals(mixSearchVO.getGranularityType()))) {
            throw new CommonApplicationException("目录树错误");
        }
    }

    public void concatMixPercent(List<DmFocMixResultVO> dmFocMixResultVOList) {
        dmFocMixResultVOList.stream().forEach(dm -> {
            String ratioPspStd = dm.getRatioPspStd();
            if (StringUtils.isNotBlank(ratioPspStd)) {
                dm.setRatioPspStd(ratioPspStd + "%");
            }
            String ratioPspStdAcc = dm.getRatioPspStdAcc();
            if (StringUtils.isNotBlank(ratioPspStdAcc)) {
                dm.setRatioPspStdAcc(ratioPspStdAcc + "%");
            }
        });
    }

    public void setGroupCnNameDimensionLevel(List<DmFocMixResultVO> dmFocMixResultVOList) {
        if (CollectionUtils.isNotEmpty(dmFocMixResultVOList)) {
            dmFocMixResultVOList.stream().forEach(mixResultVO -> {
                if (CommonConstant.GROUP_LEVEL_DIMENSION.contains(mixResultVO.getGroupLevel())) {
                    mixResultVO.setGroupCnName(mixResultVO.getGroupCode() + " " + mixResultVO.getGroupCnName());
                }
            });
        }
    }

}
