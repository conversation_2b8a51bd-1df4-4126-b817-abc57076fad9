/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.service.codeReplacement;

import com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO;
import com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;

/**
 * ICodeReplacementService Class
 */
@Path("/codeReplacement")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface ICodeReplacementService {

    /**
     * 新老编码月度成本指数图查询功能
     *
     * @param codeReplacementVO 参数
     * @return 结果
     * @throws CommonApplicationException 异常
     */
    @Path("/monthlyCostIndexChartQuery")
    @POST
    ResultDataVO monthlyCostIndexChartQuery(CodeReplacementVO codeReplacementVO) throws CommonApplicationException;

    /**
     * 新老编码月度发货量分布查询功能
     *
     * @param codeReplacementVO 参数
     * @return 结果
     * @throws CommonApplicationException 异常
     */
    @Path("/distributionOfMonthlyShipments")
    @POST
    ResultDataVO distributionOfMonthlyShipments(CodeReplacementVO codeReplacementVO) throws CommonApplicationException;

    /**
     * 新老编码月度成本偏差查询功能
     *
     * @param codeReplacementVO 参数
     * @return 结果
     * @throws CommonApplicationException 异常
     */
    @Path("/monthlyCostVariance")
    @POST
    ResultDataVO monthlyCostVariance(CodeReplacementVO codeReplacementVO) throws CommonApplicationException;

    /**
     * 新老编码月度累计成本指数图查询功能
     *
     * @param codeReplacementVO 参数
     * @return 结果
     * @throws CommonApplicationException 异常
     */
    @Path("/monthlyAccumulatedCostIndexChartQuery")
    @POST
    ResultDataVO monthlyAccumulatedCostIndexChartQuery(CodeReplacementVO codeReplacementVO)
        throws CommonApplicationException;

    /**
     * 新老编码月度累计发货量分布查询功能
     *
     * @param codeReplacementVO 参数
     * @return 结果
     * @throws CommonApplicationException 异常
     */
    @Path("/distributionOfMonthlyAccumulatedShipments")
    @POST
    ResultDataVO distributionOfMonthlyAccumulatedShipments(CodeReplacementVO codeReplacementVO)
        throws CommonApplicationException;

    /**
     * 新老编码月度累计成本偏差查询功能
     *
     * @param codeReplacementVO 参数
     * @return 结果
     * @throws CommonApplicationException 异常
     */
    @Path("/monthlyAccumulatedCostVariance")
    @POST
    ResultDataVO monthlyAccumulatedCostVariance(CodeReplacementVO codeReplacementVO) throws CommonApplicationException;

    /**
     * 数据下载
     *
     * @param codeReplacementVO 参数
     * @return 结果
     * @throws CommonApplicationException 异常
     */
    @Path("/dataDownload")
    @POST
    ResultDataVO dataDownload(@Context HttpServletResponse response, CodeReplacementExpVO codeReplacementVO)
        throws ApplicationException;

    /**
     * 基期切换
     *
     * @param codeReplacementVO 参数
     * @return 结果
     * @throws CommonApplicationException 异常
     */
    @POST
    @Path("/switchBasePeriodId")
    ResultDataVO switchBasePeriodId(CodeReplacementVO codeReplacementVO) throws ApplicationException;
}
