/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.vo.config;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 历史Spart清单实体类VO
 *
 * <AUTHOR>
 * @since 2024-11-6
 */
@Data
@NoArgsConstructor
@ApiModel(value = "历史Spart清单实体类VO")
public class PriceHistorySpartListVO extends PriceHistorySpartSearchVO implements Serializable {

    private static final long serialVersionUID = -3156129886663429537L;

    @ApiModelProperty("会计年")
    private Integer periodYear;

    @ApiModelProperty("权重")
    private Double weightRate;

    @ApiModelProperty("是否TOP标识（Y：TOP、N：非TOP）")
    private String isTopFlag;

    @ApiModelProperty("是否今年top标识（y：是、n：不是）")
    private String ytdTopFlag;


    @ApiModelProperty("是否2年数据计算标识（Y：是、N：不是）")
    private String doubleFlag;
}
