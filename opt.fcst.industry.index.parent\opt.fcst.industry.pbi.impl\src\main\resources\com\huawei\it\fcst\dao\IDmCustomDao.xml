<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.pbi.dao.IDmCustomDao">
    <resultMap type="com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstDimInfoVO" id="resultMap">
        <result property="id" column="id"/>
        <result property="bgCode" column="bg_code"/>
        <result property="bgCnName" column="bg_cn_name"/>
        <result property="customCnName" column="custom_cn_name"/>
        <result property="customId" column="custom_id"/>
        <result property="dimensionCode" column="dimension_code"/>
        <result property="dimensionCnName" column="dimension_cn_name"/>
        <result property="dimensionSubCategoryCode" column="dimension_subCategory_code"/>
        <result property="dimensionSubCategoryCnName" column="dimension_subCategory_cn_name"/>
        <result property="dimensionSubDetailCode" column="dimension_sub_detail_code"/>
        <result property="dimensionSubDetailCnName" column="dimension_sub_detail_cn_name"/>
        <result property="enableFlag" column="enable_flag"/>
        <result property="subEnableFlag" column="sub_enable_flag"/>
        <result property="granularityType" column="granularity_type"/>
        <result property="softwareMark" column="software_mark"/>
        <result property="overseaFlag" column="oversea_flag"/>
        <result property="pageFlag" column="page_flag"/>
        <result property="isSeparate" column="is_separate"/>
        <result property="ytdFlag" column="ytd_flag"/>
        <result property="mainFlag" column="main_flag"/>
        <result property="codeAttributes" column="code_attributes"/>
        <result property="spartCode" column="spart_code"/>
        <result property="spartCnName" column="spart_cn_name"/>
        <result property="regionCode" column="region_code"/>
        <result property="regionCnName" column="region_cn_name"/>
        <result property="repofficeCode" column="repoffice_code"/>
        <result property="repofficeCnName" column="repoffice_cn_name"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="groupCode" column="groupCode"/>
        <result property="groupCnName" column="groupCnName"/>
        <result property="groupLevel" column="group_level"/>
        <result property="groupCnName" column="group_cn_name"/>
        <result property="groupCode" column="group_code"/>
        <result property="lv0Code" column="lv0_code"/>
        <result property="lv1Code" column="lv1_code"/>
        <result property="lv1CnName" column="lv1_cn_name"/>
        <result property="lv2Code" column="lv2_code"/>
        <result property="codeAttributes" column="code_attributes"/>
        <result property="mainFlag" column="main_flag"/>
        <result property="lv0Code" column="LV0_CODE" />
        <result property="lv0CnName" column="LV0_CN_NAME" />
        <result property="lv1Code" column="LV1_CODE" />
        <result property="lv1CnName" column="LV1_CN_NAME" />
        <result property="lv2Code" column="LV2_CODE" />
        <result property="lv2CnName" column="LV2_CN_NAME" />
        <result property="lv3Code" column="LV3_CODE" />
        <result property="lv3CnName" column="LV3_CN_NAME" />
        <result property="lv4Code" column="LV4_CODE" />
        <result property="lv4CnName" column="LV4_CN_NAME" />
        <result property="useFlag" column="use_flag" />
        <result property="connectCode" column="connectCode" />
        <result property="connectParentCode" column="connectParentCode" />
    </resultMap>

    <select id="getCombinationCombList" resultMap="resultMap">
        select custom_cn_name,custom_id,page_flag,LV0_CODE, LV0_CN_NAME,
        LV1_CODE, LV1_CN_NAME,
        LV2_CODE,LV2_CN_NAME,LV3_CODE,LV3_CN_NAME,
        LV4_CODE,LV4_CN_NAME,group_level,group_code,group_cn_name,user_id,role_id,
        granularity_type,oversea_flag,main_flag,code_attributes,enable_flag,sub_enable_flag,
        bg_code,bg_cn_name,is_separate,creation_date,last_update_date,created_by,last_updated_by,
        concat(DECODE(LV0_CODE,'','',LV0_CODE),
        DECODE(LV1_CODE,'','','#*#' || LV1_CODE),
        DECODE(LV2_CODE,'','','#*#' || LV2_CODE),
        DECODE(LV3_CODE,'','','#*#' || LV3_CODE),
        DECODE(LV4_CODE,'','','#*#' || LV4_CODE)
        ) as connectCode
        <choose>
            <when test='costType == "PSP"'>
                from fin_dm_opt_foi.dm_fcst_ict_psp_custom_comb_dim_t
            </when>
            <when test='costType == "STD"'>
                from fin_dm_opt_foi.dm_fcst_ict_std_custom_comb_dim_t
            </when>
        </choose>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
                 and use_flag ='PAGE'
            <if test='customId!=null'>
                and custom_id = #{customId}
            </if>
            <if test='regionCode!=null and regionCode!=""'>
                and region_code = #{regionCode,jdbcType=VARCHAR}
            </if>
            <if test='repofficeCode!=null and repofficeCode!=""'>
                and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='bgCode!=null and bgCode!=""'>
                and bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='overseaFlag!=null and overseaFlag!=""'>
                and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='mainFlag!=null and mainFlag!=""'>
                and main_flag = #{mainFlag,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='lv0Code!=null and lv0Code!=""'>
                and LV0_CODE = #{lv0Code,jdbcType=VARCHAR}
            </if>
            <if test='lv1Code!=null and lv1Code!=""'>
                and LV1_CODE = #{lv1Code,jdbcType=VARCHAR}
            </if>
            <if test='lv2Code!=null and lv2Code!=""'>
                and LV2_CODE = #{lv2Code,jdbcType=VARCHAR}
            </if>
            <if test='lv3Code!=null and lv3Code!=""'>
                and LV3_CODE = #{lv3Code,jdbcType=VARCHAR}
            </if>
            <if test='lv4Code!=null and lv4Code!=""'>
                and LV4_CODE = #{lv4Code,jdbcType=VARCHAR}
            </if>
            <if test='softwareMark!=null and softwareMark!=""'>
                and software_mark = #{softwareMark,jdbcType=VARCHAR}
            </if>
            <if test='pageFlag!=null and pageFlag!=""'>
                and page_flag = #{pageFlag,jdbcType=VARCHAR}
            </if>
            <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND LV1_CODE IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel == "LV2" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND LV2_CODE IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getCombinationCombByCustomId" resultMap="resultMap">
        select custom_cn_name,custom_id,page_flag,LV0_CODE, LV0_CN_NAME,
        LV1_CODE, LV1_CN_NAME,
        LV2_CODE,LV2_CN_NAME,LV3_CODE,LV3_CN_NAME,
        LV4_CODE,LV4_CN_NAME,group_level,group_code,group_cn_name,user_id,role_id,granularity_type,oversea_flag,main_flag,
        code_attributes,enable_flag,sub_enable_flag,bg_code,bg_cn_name,is_separate,creation_date,last_update_date,created_by,last_updated_by
        <if test='costType =="PSP"'>
            from fin_dm_opt_foi.dm_fcst_ict_psp_custom_comb_dim_t
        </if>
        <if test='costType =="STD"'>
            from fin_dm_opt_foi.dm_fcst_ict_std_custom_comb_dim_t
        </if>
        where del_flag ='N' and use_flag ='PAGE' and custom_id = #{customId} and page_flag = #{oldPageFlag}
    </select>

    <select id="getYtdFlagByAnnualPage" resultMap="resultMap">
        select distinct ytd_flag
        <if test='costType =="PSP"'>
            from fin_dm_opt_foi.dm_fcst_ict_psp_custom_comb_dim_t
        </if>
        <if test='costType =="STD"'>
            from fin_dm_opt_foi.dm_fcst_ict_std_custom_comb_dim_t
        </if>
        where del_flag ='N' and use_flag ='PAGE' and custom_id = #{customId} and page_flag = #{otherOldPageFlag}
        limit 1
    </select>

    <select id="getCustomCombCountByName" resultType="java.lang.Long">
        select count(1) from
        <if test='costType =="PSP"'>
            fin_dm_opt_foi.DM_FCST_ICT_PSP_CUSTOM_COMB_DIM_T
        </if>
        <if test='costType =="STD"'>
            fin_dm_opt_foi.DM_FCST_ICT_STD_CUSTOM_COMB_DIM_T
        </if>
        where del_flag ='N'
        <if test='roleId != null and roleId!=""'>
            and role_id = #{roleId}
        </if>
        <if test='userAccount != null and userAccount!=""'>
            and user_id = #{userAccount}
        </if>
        <if test='customCnName != null and customCnName!=""'>
            and custom_cn_name = #{customCnName}
        </if>
    </select>

    <insert id="createCustomList">
        insert into
        <if test='costType =="PSP"'>
            fin_dm_opt_foi.dm_fcst_ict_psp_custom_comb_dim_t
        </if>
        <if test='costType =="STD"'>
            fin_dm_opt_foi.dm_fcst_ict_std_custom_comb_dim_t
        </if>
        (custom_id,custom_cn_name, group_level,LV0_CODE, LV0_CN_NAME, LV1_CODE, LV1_CN_NAME,
        LV2_CODE,LV2_CN_NAME,LV3_CODE,LV3_CN_NAME,LV4_CODE,LV4_CN_NAME,spart_code,spart_cn_name,bg_code,bg_cn_name,granularity_type,oversea_flag,enable_flag,sub_enable_flag,page_flag,
        region_code,region_cn_name,repoffice_code,repoffice_cn_name,main_flag,code_attributes,software_mark,
        group_cn_name,group_code,user_id,role_id,use_flag,ytd_flag,created_by,creation_date,
        last_updated_by, last_update_date,del_flag,is_separate)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.customId},#{item.customCnName}, #{item.groupLevel},#{item.lv0Code}, #{item.lv0CnName}, #{item.lv1Code}, #{item.lv1CnName},
            #{item.lv2Code},#{item.lv2CnName},#{item.lv3Code},#{item.lv3CnName},#{item.lv4Code},#{item.lv4CnName},
            #{item.spartCode},#{item.spartCnName},#{item.bgCode},#{item.bgCnName},#{item.granularityType},#{item.overseaFlag},#{item.enableFlag},#{item.subEnableFlag},#{item.pageFlag},
            #{item.regionCode}, #{item.regionCnName},#{item.repofficeCode},#{item.repofficeCnName},#{item.mainFlag},#{item.codeAttributes},#{item.softwareMark},
            #{item.groupCnName},#{item.groupCode},#{item.userId},#{item.roleId},#{item.useFlag},#{item.ytdFlag},#{item.createdBy},
            #{item.creationDate},#{item.lastUpdatedBy}, #{item.lastUpdateDate},'N',#{item.isSeparate})
        </foreach>
    </insert>

    <update id="renameCustomCombination">
        update
        <if test='costType =="PSP"'>
            fin_dm_opt_foi.dm_fcst_ict_psp_custom_comb_dim_t
        </if>
        <if test='costType =="STD"'>
            fin_dm_opt_foi.dm_fcst_ict_std_custom_comb_dim_t
        </if>
        set custom_cn_name = #{customCnName} where custom_id = #{customId} and page_flag =#{pageFlag}
    </update>

    <delete id="deleteCustomCombList">
        <if test='costType=="PSP"'>
            delete from fin_dm_opt_foi.dm_fcst_ict_psp_custom_comb_dim_t where custom_id = #{customId} and page_flag = #{oldPageFlag}
        </if>
        <if test='costType=="STD"'>
            delete from fin_dm_opt_foi.dm_fcst_ict_std_custom_comb_dim_t where custom_id = #{customId} and page_flag = #{oldPageFlag}
        </if>
    </delete>

    <update id="updateCustomCombPageFlag">
        <if test='costType =="PSP"'>
            update fin_dm_opt_foi.dm_fcst_ict_psp_custom_comb_dim_t
        </if>
        <if test='costType =="STD"'>
            update fin_dm_opt_foi.dm_fcst_ict_std_custom_comb_dim_t
        </if>
        set
        page_flag = #{pageFlag}, is_separate = #{isSeparate}
        where custom_id = #{customId} and page_flag = #{oldPageFlag}
    </update>

    <update id="updateCustomCombSeparate">
        <if test='costType =="PSP"'>
            update fin_dm_opt_foi.dm_fcst_ict_psp_custom_comb_dim_t
        </if>
        <if test='costType =="STD"'>
            update fin_dm_opt_foi.dm_fcst_ict_std_custom_comb_dim_t
        </if>
        set
        is_separate = #{isSeparate}
        where custom_id = #{customId} and page_flag = #{pageFlag}
    </update>

    <select id="getCustomCombNameList" resultMap="resultMap">
        select custom_id,custom_cn_name,enable_flag,granularity_type,bg_code,bg_cn_name,oversea_flag,software_mark,page_flag,is_separate,
        region_code,region_cn_name, repoffice_code,repoffice_cn_name,main_flag,code_attributes,ytd_flag,#{costType} as costType,lv0_code,
        min(creation_date) as creation_date
        from
        <if test='costType =="PSP"'>
            fin_dm_opt_foi.dm_fcst_ict_psp_custom_comb_dim_t
        </if>
        <if test='costType =="STD"'>
            fin_dm_opt_foi.dm_fcst_ict_std_custom_comb_dim_t
        </if>
        where del_flag ='N' and use_flag = 'PAGE'
        <if test='pageSymbol != null and pageSymbol!=""'>
            and (page_flag = #{pageSymbol} or page_flag = concat('ALL_',#{pageSymbol}::text))
        </if>
        <if test='roleId != null and roleId!=""'>
            and role_id = #{roleId}
        </if>
        <if test='userId != null and userId!=""'>
            and user_id = #{userId}
        </if>
        group by custom_id,custom_cn_name,enable_flag,granularity_type,bg_code,bg_cn_name,oversea_flag,software_mark,page_flag,is_separate,
        region_code,region_cn_name,repoffice_code,repoffice_cn_name,main_flag,code_attributes,ytd_flag,lv0_code
        order by creation_date desc
    </select>

    <update id="updateCustomEnableById">
        update
        <if test='costType =="PSP"'>
            fin_dm_opt_foi.dm_fcst_ict_psp_custom_comb_dim_t
        </if>
        <if test='costType =="STD"'>
            fin_dm_opt_foi.dm_fcst_ict_std_custom_comb_dim_t
        </if>
        set enable_flag = 'N' where custom_id = #{customId} and page_flag = #{pageFlag}
    </update>

    <update id="updateCombSubEnableList">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update
            <if test='commonViewVO.costType =="PSP"'>
                fin_dm_opt_foi.dm_fcst_ict_psp_custom_comb_dim_t
            </if>
            <if test='commonViewVO.costType =="STD"'>
                fin_dm_opt_foi.dm_fcst_ict_std_custom_comb_dim_t
            </if>
            set sub_enable_flag = 'N' where custom_id = #{item.customId} and group_code =#{item.groupCode} and group_level=#{item.groupLevel} and page_flag =#{item.pageFlag}
            <if test='item.lv1Code !=null and item.lv1Code !=""'>
                and LV1_CODE =#{item.lv1Code}
            </if>
            <if test='item.lv2Code !=null and item.lv2Code !=""'>
                and LV2_CODE =#{item.lv2Code}
            </if>
            <if test='item.lv3Code !=null and item.lv3Code !=""'>
                and LV3_CODE =#{item.lv3Code}
            </if>
            <if test='item.lv4Code !=null and item.lv4Code !=""'>
                and LV4_CODE =#{item.lv4Code}
            </if>
        </foreach>
    </update>

    <select id="getCombAnnual" resultType="java.lang.String">
        select fin_dm_opt_foi.F_DM_FCST_CUS_ANNL(#{costType},#{granularityType},#{ytdFlag},#{customId}, #{encryptKey},#{versionId})
    </select>

    <select id="getCombMonth" resultType="java.lang.String">
        select fin_dm_opt_foi.f_fcst_ict_point_cus_mon_result_t(#{costType},#{granularityType},#{pageSymbol},#{ytdMonthFlag},#{customId},#{encryptKey},#{monthVersionId})
    </select>
</mapper>