/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.ciphertext;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

/**
 * CipherTextDataVOTest Class
 *
 * <AUTHOR>
 * @since 2023/5/9
 */
public class CipherTextDataVOTest extends BaseVOCoverUtilsTest<CipherTextDataVO> {

    @Override
    protected Class<CipherTextDataVO> getTClass() {
        return CipherTextDataVO.class;
    }

    @Test
    public void testMethod() {
        CipherTextDataVO dmFocActualCostVO = new CipherTextDataVO();
        dmFocActualCostVO.setPeriodId(2023L);
        dmFocActualCostVO.getPeriodId();
        dmFocActualCostVO.getCreationDate();
        dmFocActualCostVO.getRmbCostAmt();
        dmFocActualCostVO.getRmbFactRateGcAmt();
        dmFocActualCostVO.getPrimaryId();
        Assert.assertNotNull(dmFocActualCostVO);
    }
}