/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.impl.template;

import com.huawei.it.fcst.export.IExcelTemplateBeanManager;
import com.huawei.it.fcst.export.vo.ExcelTemplateBeanManager;
import com.huawei.it.fcst.export.vo.SheetBeanMetaVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import lombok.Getter;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * 底层数据审视导出模板
 *
 * <AUTHOR>
 * @since 2024/06
 */
@Getter
public enum DataReviewTemplateEnum implements IExcelTemplateBeanManager {
    DATA_REVIEW_01("01", "DataReviewPriceExportTemplate", "配置管理-底层数据审视-异常数据", "配置管理-底层数据审视") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            ExcelTemplateBeanManager excelTemplateBeanManager = new ExcelTemplateBeanManager();
            List<SheetBeanMetaVO> reviewList = new ArrayList<>();
            reviewList.add(new SheetBeanMetaVO(DATA_REVIEW_01.templateName, 0, "DataReviewExportProvider", "异常数据", Boolean.FALSE));
            excelTemplateBeanManager.setSheetBeans(reviewList);
            excelTemplateBeanManager.setTemplateName(DATA_REVIEW_01.templateName);
            excelTemplateBeanManager.setModuleType(DATA_REVIEW_01.moduleType);
            excelTemplateBeanManager.setDesc(DATA_REVIEW_01.desc);
            return excelTemplateBeanManager;
        }
    },
    DATA_REVIEW_02("02", "DataReviewPriceHistoryExportTemplate", "配置管理-底层数据审视-操作记录", "配置管理-底层数据审视") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            ExcelTemplateBeanManager dataReviewTemplateBeanManager = new ExcelTemplateBeanManager();
            List<SheetBeanMetaVO> reviewList = new ArrayList<>();
            reviewList.add(new SheetBeanMetaVO(DATA_REVIEW_02.templateName, 0, "DataReviewExportProvider", "操作记录", Boolean.FALSE));
            dataReviewTemplateBeanManager.setSheetBeans(reviewList);
            dataReviewTemplateBeanManager.setTemplateName(DATA_REVIEW_02.templateName);
            dataReviewTemplateBeanManager.setModuleType(DATA_REVIEW_02.moduleType);
            dataReviewTemplateBeanManager.setDesc(DATA_REVIEW_02.desc);
            return dataReviewTemplateBeanManager;
        }
    };
    private String code;
    private String templateName;
    private String moduleType;
    private String desc;

    DataReviewTemplateEnum(String code, String templateName, String moduleType, String desc) {
        this.code = code;
        this.templateName = templateName;
        this.moduleType = moduleType;
        this.desc = desc;
    }
    static final String DATA_REVIEW = "DATA_REVIEW";
    static final String SUB_LEVEL_CODE = "{0}_{1}";

    /**
     * 获取模板信息
     *
     * @param levelCode 层级code
     * @param roleName 角色名称
     * @return 枚举信息
     * @throws CommonApplicationException
     */
    public static DataReviewTemplateEnum getByCode(String levelCode, String roleName) throws CommonApplicationException {
        String key = MessageFormat.format(SUB_LEVEL_CODE, DATA_REVIEW, levelCode);
        for (DataReviewTemplateEnum value : DataReviewTemplateEnum.values()) {
            if (value.name().equalsIgnoreCase(key)) {
                return value;
            }
        }
        throw new CommonApplicationException("Check the template definition relationship.");
    }
}
