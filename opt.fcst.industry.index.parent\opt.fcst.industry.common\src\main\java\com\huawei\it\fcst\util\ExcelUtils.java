/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.util;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.huawei.it.fcst.common.StatisticsExcelService;
import com.huawei.it.fcst.constant.Constants;
import com.huawei.it.fcst.export.vo.PbiDmFoiImpExpRecordVO;
import com.huawei.it.fcst.vo.AbstractExcelTitleVO;
import com.huawei.it.fcst.vo.ExcelVO;
import com.huawei.it.fcst.vo.ExportExcelVo;
import com.huawei.it.fcst.vo.UploadInfoVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.util.PathUtil;
import com.huawei.it.jalor5.registry.service.IRegistryQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

import static java.io.File.separator;

/**
 * ExcelUtil Class
 *
 * <AUTHOR>
 * @since 2024/9/10
 */
@Slf4j
@Component
public class ExcelUtils {

    private static final Logger logger = LoggerFactory.getLogger(ExcelUtils.class);

    private static final String FILE = "file";

    private static final String METHOD = "method";

    @Value("${spring.profiles.active}")
    private String env;

    @Autowired
    private ExcelUtilPro excelUtilPro;

    @Autowired
    private StatisticsExcelService statisticsExcelService;

    @Autowired
    private IRegistryQueryService registryQueryService;

    /**
     * 导入excel，读取数据
     *
     * @param heads 表头
     * @return List<Map>
     * @throws CommonApplicationException
     * @throws IOException
     */
    public List<LinkedHashMap<String, Object>> importExcel(Attachment attachment, List<ExcelVO> heads,
                                                           UploadInfoVO infoVO, ByteArrayOutputStream byteArrayOutputStream) throws CommonApplicationException, IOException {
        InputStream inputStream = null;
        String msg = null;
        try {
            if (!(attachment.getDataHandler().getName()).endsWith(Constants.XLSX.getValue()) &&
                    !(attachment.getDataHandler().getName()).endsWith(Constants.XLS.getValue())) {
                // 统计导入错误信息
                PbiDmFoiImpExpRecordVO dmFoiRecordVO = new PbiDmFoiImpExpRecordVO();
                msg = "请导入Excel xlsx xls格式下的文件";
                dmFoiRecordVO.setExceptionFeedback(msg);
                inputStream = getInputStream(byteArrayOutputStream);
                dmFoiRecordVO.setCreationDate(infoVO.getCreationDate());
                dmFoiRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                statisticsExcelService.getImportUploadFileKey(dmFoiRecordVO, infoVO.getUserId(), 1, inputStream);
                statisticsExcelService.insertImportExcel(dmFoiRecordVO, infoVO, false, 0);
                throw new CommonApplicationException(msg);
            }
        } catch (Exception e) {
            msg = msg == null ? e.getMessage() : msg;
            throw new CommonApplicationException(msg);
        } finally {
            closeIO(inputStream);
        }
        return this.readExcel(attachment, heads, infoVO, byteArrayOutputStream);
    }

    private static void closeIO(InputStream inputStream) throws IOException {
        if (inputStream != null) {
            try {
                inputStream.close();
            } catch (IOException e) {
                logger.error("close IOException.{}", e);
            } finally {
                inputStream.close();
            }
        }
    }

    public List<LinkedHashMap<String, Object>> readExcel(Attachment attachment, List<ExcelVO> heads,
                                                         UploadInfoVO infoVO, ByteArrayOutputStream byteArrayOutputStream) throws CommonApplicationException, IOException {
        ZipSecureFile.setMinInflateRatio(-1.0d);
        XSSFWorkbook workbook = this.createWorkbook(attachment, infoVO, byteArrayOutputStream);
        return getMapList(heads, infoVO, workbook, byteArrayOutputStream);
    }

    private List<LinkedHashMap<String, Object>> getMapList(List<ExcelVO> heads, UploadInfoVO uploadVO, XSSFWorkbook workbook, ByteArrayOutputStream byteArrayOutputStream)
            throws CommonApplicationException, IOException {
        List<LinkedHashMap<String, Object>> list = null;
        InputStream inputStream = null;
        String message = null;
        try {
            int lastRowNum = MapUtil.getInt(uploadVO.getParams(), "maxRowNum");
            XSSFSheet sheet = workbook.getSheetAt(0);
            uploadVO.setSheetName(sheet.getSheetName());
            inputStream = getInputStream(byteArrayOutputStream);
            if (sheet.getLastRowNum() <= lastRowNum && sheet.getLastRowNum() > 0) {
                list = this.readDataByExcel(heads, sheet, uploadVO, inputStream);
            } else if (sheet.getLastRowNum() <= 0) {
                // 统计导入错误信息
                PbiDmFoiImpExpRecordVO dmFoiImpExpRecordVO = new PbiDmFoiImpExpRecordVO();
                dmFoiImpExpRecordVO.setCreationDate(uploadVO.getCreationDate());
                dmFoiImpExpRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                statisticsExcelService.getImportUploadFileKey(dmFoiImpExpRecordVO, uploadVO.getUserId(), 1, inputStream);
                message = "导入的是空文件";
                dmFoiImpExpRecordVO.setExceptionFeedback(message);
                statisticsExcelService.insertImportExcel(dmFoiImpExpRecordVO, uploadVO, false, 0);
                throw new CommonApplicationException(message);
            } else {
                // 统计导入错误信息
                PbiDmFoiImpExpRecordVO dmFoiRecordVO = new PbiDmFoiImpExpRecordVO();
                dmFoiRecordVO.setExceptionFeedback("导入的文件超过允许文件的最大行数:" + lastRowNum);
                statisticsExcelService.getImportUploadFileKey(dmFoiRecordVO, uploadVO.getUserId(), 1, inputStream);
                statisticsExcelService.insertImportExcel(dmFoiRecordVO, uploadVO, false, sheet.getLastRowNum());
                message = "导入的文件验证不通过,请检查当前的最大文件行数是:".concat(String.valueOf(sheet.getLastRowNum()))
                        .concat(Constants.DH.getValue())
                        .concat("允许文件的最大行数是:" + lastRowNum)
                        .concat(Constants.DH.getValue())
                        .concat("文件只支持一个sheet页导入,读取的文件sheet是:" + workbook.getNumberOfSheets());
                throw new CommonApplicationException(message);
            }
        } catch (Exception e) {
            message = message == null ? e.getMessage() : message;
            throw new CommonApplicationException(message);
        } finally {
            // 关闭流
            closeIO(inputStream);
        }
        return list;
    }

    private List<LinkedHashMap<String, Object>> readDataByExcel(List<ExcelVO> heads, XSSFSheet xssfSheet, UploadInfoVO uploadVO, InputStream inputStream)
            throws CommonApplicationException, IOException {
        List<LinkedHashMap<String, Object>> dataList = new ArrayList<>();
        int lastRowNum = xssfSheet.getLastRowNum();
        for (int rowNum = 1; rowNum <= lastRowNum; rowNum++) {
            XSSFRow firstRow = xssfSheet.getRow(0);
            XSSFRow xssfRow = xssfSheet.getRow(rowNum);
            if (xssfRow == null) {
                break;
            }
            LinkedHashMap<String, Object> data = new LinkedHashMap<String, Object>();
            // 只判断第一行标题是否跟需要的字段size相等
            short lastCellNum = firstRow.getLastCellNum();
            if (heads.size() == lastCellNum) {
                getReadExcelList(heads, xssfRow, data);
                dataList.add(data);
            } else {
                PbiDmFoiImpExpRecordVO expRecordVO = new PbiDmFoiImpExpRecordVO();
                expRecordVO.setCreationDate(uploadVO.getCreationDate());
                expRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                expRecordVO.setExceptionFeedback("列名出错");
                statisticsExcelService.getImportUploadFileKey(expRecordVO, uploadVO.getUserId(), 1, inputStream);
                statisticsExcelService.insertImportExcel(expRecordVO, uploadVO, false, xssfSheet.getLastRowNum() - 1);
                throw new CommonApplicationException("列名出错");
            }
        }
        return dataList;
    }

    private void getReadExcelList(List<ExcelVO> heads, XSSFRow xssfRow, LinkedHashMap<String, Object> data) throws CommonApplicationException {
        for (int cellNum = 0; cellNum < heads.size(); cellNum++) {
            XSSFCell xssfCell = xssfRow.getCell(cellNum);
            String title = heads.get(cellNum).getHeadName();
            if (!StrUtil.equals(Constants.SNULL.getValue(), title)) {
                data.put(heads.get(cellNum).getHeadName(),
                        excelUtilPro.getStringCellValue(xssfCell, heads.get(cellNum).getHeadType()));
            }
        }
    }

    private XSSFWorkbook createWorkbook(Attachment attachment, UploadInfoVO uploadInfoVO, ByteArrayOutputStream byteArrayOutputStream)
            throws CommonApplicationException, IOException {
        XSSFWorkbook wobook = null;
        String fileName = attachment.getDataHandler().getName();
        Long userId = uploadInfoVO.getUserId();
        InputStream inputStream = null;
        try {
            inputStream = getInputStream(byteArrayOutputStream);
            if (fileName.endsWith(Constants.XLSX.getValue()) || fileName.endsWith(Constants.XLS.getValue())) {
                if (inputStream.available() >= 1024000) {
                    // 统计导入错误信息
                    PbiDmFoiImpExpRecordVO dmFoiImpExpRecordVO = new PbiDmFoiImpExpRecordVO();
                    dmFoiImpExpRecordVO.setCreationDate(uploadInfoVO.getCreationDate());
                    dmFoiImpExpRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                    dmFoiImpExpRecordVO.setExceptionFeedback("文件允许的大小是：1兆");
                    statisticsExcelService.getImportUploadFileKey(dmFoiImpExpRecordVO, userId, 1, inputStream);
                    statisticsExcelService.insertImportExcel(dmFoiImpExpRecordVO, uploadInfoVO, false, 0);
                    throw new CommonApplicationException("文件允许的大小是：1兆.");
                } else {
                    wobook = new XSSFWorkbook(inputStream);
                }
            } else {
                PbiDmFoiImpExpRecordVO dmFoiRecordVO = new PbiDmFoiImpExpRecordVO();
                dmFoiRecordVO.setCreationDate(uploadInfoVO.getCreationDate());
                dmFoiRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                dmFoiRecordVO.setExceptionFeedback("只能使用XLSX OR XLS格式EXCEL的文件");
                statisticsExcelService.getImportUploadFileKey(dmFoiRecordVO, userId, 1, inputStream);
                statisticsExcelService.insertImportExcel(dmFoiRecordVO, uploadInfoVO, false, 0);
                throw new CommonApplicationException("只能使用XLSX OR XLS格式EXCEL的文件.");
            }
            return wobook;
        } catch (IOException ex) {
            logger.info("Failed to read the file：{}", ex.getMessage());
            throw new CommonApplicationException("读取文件异常");
        } finally {
            // 关闭流
            if (null != inputStream) {
                inputStream.close();
            }
        }
    }

    /**
     * 存储输入流，以便后面使用
     *
     * @param inputStream 参数
     * @return ByteArrayOutputStream
     */
    public ByteArrayOutputStream putInputStreamCacher(InputStream inputStream) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int len;
        try {
            while ((len = inputStream.read(buffer)) > -1) {
                outputStream.write(buffer, 0, len);
            }
            outputStream.flush();
        } catch (IOException exception) {
            logger.error("存储输入流异常：{}", exception.getMessage());
        }
        return outputStream;
    }

    public InputStream getInputStream(ByteArrayOutputStream byteArrayOutputStream) {
        return new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
    }


    /**
     * [服务名称]expByS3
     *
     * @param servletResponse          入参
     * @param exportExcelVoList 入参
     * @throws CommonApplicationException void
     * <AUTHOR>
     */
    public PbiDmFoiImpExpRecordVO expSelTitle(HttpServletResponse servletResponse, List<ExportExcelVo> exportExcelVoList)
            throws CommonApplicationException, IOException {
            return PoiEnum.exportExcel(null, exportExcelVoList);
    }

    public PbiDmFoiImpExpRecordVO expSelectColumnExcel(List<ExportExcelVo> exportExcelVoList, HttpServletResponse response)
            throws CommonApplicationException, IOException {
        for (ExportExcelVo exportExcelVO : exportExcelVoList) {
            List<AbstractExcelTitleVO> leafExcelTitleVO = new ArrayList<>();
            int titleRowCount = excelUtilPro.adjustTitleVoList(exportExcelVO.getTitleVoList(), leafExcelTitleVO);
            List<AbstractExcelTitleVO> selectedLeafExcelTitleVO = leafExcelTitleVO;
            if (exportExcelVO.getMergeCell()) {
                selectedLeafExcelTitleVO = leafExcelTitleVO.stream()
                        .limit(leafExcelTitleVO.size() - 1)
                        .collect(Collectors.toList());
            }
            exportExcelVO.setSelectedLeafExcelTitleVO(selectedLeafExcelTitleVO);
            exportExcelVO.setTitleRowCount(titleRowCount);
        }
        return this.expSelTitle(response, exportExcelVoList);
    }

    /**
     * 校验导入的Excel文件格式和文件大小
     *
     * @param attachment attachment
     */
    public void verifyExcelFile(Attachment attachment, String pageModule) throws CommonApplicationException {
        if (verifyFileFormat(attachment)) {
            checkFile(attachment, pageModule, "请导入xlsx、xls格式的Excel文件!");
            throw new CommonApplicationException("请导入xlsx、xls格式的Excel文件!");
        }
        if (verifyFileSize(attachment)) {
            String errorMsg = String.format(Locale.ROOT, "文件允许的大小是：%dM!", getExcelImportFileSize());
            checkFile(attachment, pageModule, errorMsg);
            throw new CommonApplicationException(errorMsg);
        }
    }

    /**
     * 校验导入的Excel文件格式，目前只支持【.XLSX和.XLS】的文件格式
     *
     * @param attachment attachment
     * @return true or false
     */
    private boolean verifyFileFormat(Attachment attachment) {
        String fileName;
        try {
            fileName = new String(attachment.getDataHandler().getName().getBytes("ISO8859-1"), "UTF-8");
        } catch (UnsupportedEncodingException ex) {
            log.error(">>>Find verifyFileFormat a error:", ex);
            fileName = "";
        }
        return !(fileName).endsWith(Constants.XLSX.getValue()) && !(fileName).endsWith(Constants.XLS.getValue());
    }

    /**
     * 校验导入的Excel文件大小，目前只支持20M
     *
     * @param attachment attachment
     * @return true or false
     */
    private boolean verifyFileSize(Attachment attachment) {
        try {
            return attachment.getDataHandler().getInputStream().available() >= getExcelImportFileSize() * 1024000;
        } catch (IOException ex) {
            log.error(">>>Find verifyFileSize a error:", ex);
            return false;
        }
    }

    // 获取Excel导入的最大条数
    private int getExcelImportFileSize() {
        try {
            String fileSize = registryQueryService.findValueByPath("Jalor.Excel.ExcelImportFileSize", true);
            return Integer.valueOf(fileSize);
        } catch (ApplicationException ex) {
            log.error(">>>Find getExcelImportFileSize a error:", ex);
            return 1;
        }
    }

    public static String getFilename(Attachment attachment) throws UnsupportedEncodingException {
        String fileName = new String(attachment.getDataHandler().getName().getBytes("ISO8859-1"), "UTF-8");
        int index = fileName.indexOf(".");
        return fileName.substring(0, index);
    }
    private static String getTempName() {
        return DateFormatUtils.format(new Date(), "yyyyMMddHHmmssSSS");
    }

    public void checkFile(Attachment attachment, String pageModule, String errorMsg) {
        FileOutputStream out = null;
        InputStream inputStream = null;
        File file = null;
        try {
            // 个人中心导入所需文件
            String tempName = new String(attachment.getDataHandler().getName().getBytes("ISO8859-1"), "UTF-8") ;
            String fileName = tempName.substring(0, tempName.indexOf(".")) + "_" + getTempName();
            file = new File(PathUtil.getTempPathByDay(Constants.DEF.getValue()) + separator + attachment.getDataHandler().getName());
            out = new FileOutputStream(file);
            // 刷新流
            out.flush();
            // 上传文件至S3服务器
            Long userId = UserInfoUtils.getUserId();
            // 设置个人中心上传信息
            PbiDmFoiImpExpRecordVO recordVO = new PbiDmFoiImpExpRecordVO();
            inputStream = attachment.getDataHandler().getInputStream();

            recordVO.setRecordNum(0L);
            recordVO.setExceptionFeedback(errorMsg);
            recordVO.setPageModule(pageModule);
            UploadInfoVO uploadInfoVO = new UploadInfoVO();
            uploadInfoVO.setUserId(userId);
            uploadInfoVO.setFileName(fileName);
            uploadInfoVO.setFileSize(inputStream.available()/ 1024);
            statisticsExcelService.getImportUploadFileKey(recordVO, userId, 1, inputStream);
            statisticsExcelService.insertImportExcel(recordVO, uploadInfoVO, false, 0);
        } catch (Exception e) {
            log.error("verifyExcelFile 上传文件失败");
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    log.error("verifyExcelFile Stream 关闭失败");
                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("verifyExcelFile inputStream 关闭失败");
                }
            }
            if (file != null && file.exists()) {
                if (!file.delete()) {
                    log.info("del file failed");
                }
            }
        }
    }
}
