/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.notice;

import com.huawei.it.fcst.constant.Constants;
import com.huawei.it.fcst.industry.index.cache.IndustryGlobalParameterUtil;
import com.huawei.it.fcst.industry.index.dao.IDmFocNoticeInfoDao;
import com.huawei.it.fcst.industry.index.enums.ResultCodeEnum;
import com.huawei.it.fcst.industry.index.service.notice.INoticeInfoService;
import com.huawei.it.fcst.industry.index.utils.FileProcessUtis;
import com.huawei.it.fcst.industry.index.utils.UserInfoUtils;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocNoticeInfoDTO;
import com.huawei.it.fcst.industry.index.vo.notice.NoticeInfoVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.core.util.PathUtil;
import com.huawei.it.jalor5.lookup.LookupItemVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.pdfbox.io.RandomAccessFile;
import org.apache.pdfbox.pdfparser.PDFParser;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.springframework.util.ObjectUtils;

import javax.inject.Inject;
import javax.inject.Named;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/2/19
 */
@Slf4j
@Named("noticeInfoService")
@JalorResource(code = "noticeInfoService", desc = "公告管理服务")
public class NoticeInfoService implements INoticeInfoService {
    @Inject
    private IDmFocNoticeInfoDao dmFocNoticeInfoDao;

    private static final Pattern PATTERN_URL = Pattern.compile("^https?://[a-z\\d-.]+\\.huawei\\.com([/?].*)?$");

    private static final String SAVE_NOTICE_FAILED = "FAILED";

    private static final String SAVE_NOTICE_SUCCESS = "SUCCESS";

    private static final String HOME_PAGE_BULLETIN_TYPE = "HOME_PAGE_BULLETIN_TYPE";

    private static final String[] ALL_OWEXTENSIONS = {".jpg", ".png", ".pdf"};

    private static final String NOTICE_TAG_REGEX = "^[a-zA-Z0-9\\u4e00-\\u9fa5]{0,5}$";

    private static final int FILE_SIZE = 20480000;

    @JalorOperation(code = "findNoticeInfoListByPage", desc = "分页查询公告")
    @Override
    public ResultDataVO findNoticeInfoListByPage(NoticeInfoVO noticeInfoVO) {
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(noticeInfoVO.getPageSize());
        pageVO.setCurPage(noticeInfoVO.getPageIndex());
        PagedResult<DmFocNoticeInfoDTO> noticeInfoByPage = dmFocNoticeInfoDao.findByPage(noticeInfoVO, pageVO);
        Map result = new LinkedHashMap();
        result.put("result", noticeInfoByPage.getResult());
        result.put("pageVO", noticeInfoByPage.getPageVO());
        return ResultDataVO.success(result);
    }

    @JalorOperation(code = "findNoticeInfoDetail", desc = "公告详情查看")
    @Override
    public ResultDataVO findNoticeInfoDetail(Long id) {
        return ResultDataVO.success(dmFocNoticeInfoDao.findNoticeInfoById(id));
    }

    @JalorOperation(code = "fingNoticeByType", desc = "依据公告类型查公告")
    @Override
    public ResultDataVO fingNoticeByType(NoticeInfoVO noticeInfoVO) {
        return ResultDataVO.success(dmFocNoticeInfoDao.findNoticeByType(noticeInfoVO.getNoticeTypeList()));
    }

    @JalorOperation(code = "saveNoticeInfo", desc = "公告带附件保存")
    @Audit(module = "noticeInfoService-saveNoticeInfo", operation = "saveNoticeInfo", message = "公告带附件保存")
    @Override
    public ResultDataVO saveNoticeInfo(Attachment attachment, String noticeType, String noticeTitle, String noticeTag, String fileType, String isHistory) throws CommonApplicationException {
        String returnFlag = SAVE_NOTICE_FAILED;
        File file = null;
        try {
            vaildNoticeInfo(noticeType, noticeTag);
            String name = attachment.getDataHandler().getName();
            String fileName = name.toLowerCase(Locale.ROOT);
            List<String> list = Arrays.asList(ALL_OWEXTENSIONS);
            if (!list.stream().anyMatch(extension -> fileName.endsWith(extension))) {
                throw new CommonApplicationException("文件类型不正确");
            }
            if (attachment.getDataHandler().getInputStream().available() > FILE_SIZE) {
                throw new CommonApplicationException("文件大小不能超过20M");
            }
            file = new File(PathUtil.getTempPathByDay(Constants.DEF.getValue()) + name);
            FileUtils.copyInputStreamToFile(attachment.getDataHandler().getInputStream(), file);
            // 校验pdf文件是否包含js脚本
            if(fileName.endsWith("pdf") && pdfValidate(file)){
                return ResultDataVO.success(returnFlag);
            }
            DmFocNoticeInfoDTO dmFocNoticeInfoDTO = new DmFocNoticeInfoDTO();
            dmFocNoticeInfoDTO.setFileSourceKey(uploadFileToS3(file));
            dmFocNoticeInfoDTO.setId(dmFocNoticeInfoDao.getNoticeKey());
            dmFocNoticeInfoDTO.setNoticeType(noticeType);
            dmFocNoticeInfoDTO.setNoticeTitle(noticeTitle);
            dmFocNoticeInfoDTO.setNoticeTag(noticeTag);
            dmFocNoticeInfoDTO.setFileType(fileType);
            dmFocNoticeInfoDTO.setIsHistory(isHistory);
            dmFocNoticeInfoDTO.setDelFlag("N");
            Long userId = UserInfoUtils.getUserId();
            dmFocNoticeInfoDTO.setCreatedBy(userId);
            dmFocNoticeInfoDTO.setLastUpdatedBy(userId);
            int count = dmFocNoticeInfoDao.createDmFocNoticeInfo(dmFocNoticeInfoDTO);
            if (count == 1) {
                returnFlag = SAVE_NOTICE_SUCCESS;
            }
        } catch (IOException exception) {
            log.error("saveNoticeInfo import file error {}",exception);
        } finally {
            if (file != null && file.exists()) {
                if (!file.delete()) {
                    log.info("del file failed");
                }
            }
        }
        return ResultDataVO.success(returnFlag);
    }

    private void vaildNoticeInfo(String noticeType, String noticeTag) throws CommonApplicationException {
        // 获取公告类型
        List<LookupItemVO> lookUpItemList = IndustryGlobalParameterUtil.findViewListByItemCode(HOME_PAGE_BULLETIN_TYPE);
        List<String> noticeTypeList = lookUpItemList.stream().map(item -> item.getItemName()).collect(Collectors.toList());
        if (!noticeTypeList.stream().anyMatch(item -> noticeType.equalsIgnoreCase(item))) {
            throw new CommonApplicationException("公告类型不正确");
        }
        if (!noticeTag.matches(NOTICE_TAG_REGEX)) {
            throw new CommonApplicationException("标签输入值不合法");
        }
    }

    /**
     * 校验pdf文件是否包含js脚本
     * return  true: 校验不通过  false: 校验通过
     **/
    public static boolean pdfValidate(File file) throws IOException{
        RandomAccessFile is = null;
        try {
            is = new RandomAccessFile(file, "r");
            PDFParser parser = new PDFParser(is);
            parser.parse();
            PDDocument doc = parser.getPDDocument();
            String CosName = doc.getDocument().getTrailer().toString();
            // 校验是否包含javascript代码
            if (CosName.contains("COSName{JavaScript}") || CosName.contains("COSName{JS}")) {
                return true;
            }
            String[] cosNames = CosName.split("COSName");
            for (String name : cosNames) {
                if (name.startsWith("{URI}:COSString") || name.startsWith("{F}:COSString")) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("PDF效验异常：" + e);
            return true;
        } finally {
            if (is != null) {
                is.close();
            }
        }
        return false;
    }

    private static String uploadFileToS3(File file) throws CommonApplicationException {
        String userIdStr = String.valueOf(UserInfoUtils.getUserId());
        // 上传
        return  FileProcessUtis.uploadToS3(file, userIdStr);
    }

    @JalorOperation(code = "saveNoticeContentInfo", desc = "公告不带附件保存")
    @Audit(module = "noticeInfoService-saveNoticeContentInfo", operation = "saveNoticeContentInfo", message = "公告不带附件保存")
    @Override
    public ResultDataVO saveNoticeContentInfo(DmFocNoticeInfoDTO dmFocNoticeInfoDTO) throws CommonApplicationException {
        if (("link").equals(dmFocNoticeInfoDTO.getFileType()) && !PATTERN_URL.matcher(dmFocNoticeInfoDTO.getNoticeContent()).matches()) {
            throw new CommonApplicationException("URL链接必须是华为内部链接地址");
        }
         // 送检需要将入参fileSourceKey置空，存在越权风险
        dmFocNoticeInfoDTO.setFileSourceKey(null);
        vaildNoticeInfo(dmFocNoticeInfoDTO.getNoticeType(), dmFocNoticeInfoDTO.getNoticeTag());
        dmFocNoticeInfoDTO.setId(dmFocNoticeInfoDao.getNoticeKey());
        dmFocNoticeInfoDTO.setDelFlag("N");
        Long userId = UserInfoUtils.getUserId();
        dmFocNoticeInfoDTO.setCreatedBy(userId);
        dmFocNoticeInfoDTO.setLastUpdatedBy(userId);
        int count = dmFocNoticeInfoDao.createDmFocNoticeInfo(dmFocNoticeInfoDTO);
        String returnFlag = "FAILED";
        if (count == 1) {
            returnFlag = "SUCCESS";
        }
        return ResultDataVO.success(returnFlag);
    }

    @JalorOperation(code = "deleteNoticeInfo", desc = "公告删除")
    @Audit(module = "noticeInfoService-deleteNoticeInfo", operation = "deleteNoticeInfo", message = "公告删除")
    @Override
    public ResultDataVO deleteNoticeInfo(Long id) {
        if (ObjectUtils.isEmpty(id)) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR.getCode(), ResultCodeEnum.PARAM_ERROR.getMessage().concat("id必填！"));
        }
        return ResultDataVO.success(dmFocNoticeInfoDao.updateDmFocNoticeInfo(id));
    }


}
