/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.replace;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.collect.Lists;
import com.huawei.it.fcst.industry.pbi.dao.IDmfcstIctCodeReplInfoDao;
import com.huawei.it.fcst.industry.pbi.impl.config.ExcelImportValid;
import com.huawei.it.fcst.industry.pbi.utils.FcstIndustryUtil;
import com.huawei.it.fcst.industry.pbi.vo.config.ImportContextVO;
import com.huawei.it.fcst.industry.pbi.vo.replace.DmFocReplVO;
import com.huawei.it.fcst.industry.pbi.vo.replace.ReplaceImportVO;
import com.huawei.it.fcst.industry.pbi.vo.replace.ReplaceSearchVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 新旧编码替换维表导入监听器
 *
 * <AUTHOR>
 * @since 2024-09-27
 */
@Slf4j
public class CodeReplaceImportListener implements ReadListener<ReplaceImportVO> {

    private IDmfcstIctCodeReplInfoDao codeReplInfoDao;

    private ImportContextVO importContextVO;

    // L1层级映射
    private Map<String, List<DmFocReplVO>> lv1Map = new HashMap<>();

    // L2层级映射
    private Map<String, List<DmFocReplVO>>  lv2Map = new HashMap<>();

    // L3层级映射
    private Map<String, List<DmFocReplVO>>  lv3Map = new HashMap<>();

    // L4层级映射
    private Map<String, List<DmFocReplVO>>  lv4Map = new HashMap<>();

    private DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyy/MM/dd");

    private Pattern pattern = Pattern.compile("^\\d{4}[-/]\\d{2}[-/]\\d{2}$");

    // 设置数据集合
    private ThreadLocal<List<ReplaceImportVO>> currentDataList = ThreadLocal.withInitial(ArrayList::new);

    private ThreadLocal<StringBuilder> errorTips = ThreadLocal.withInitial(StringBuilder::new);

    public CodeReplaceImportListener(ImportContextVO importContextVO, IDmfcstIctCodeReplInfoDao codeReplInfoDao) {
        this.importContextVO = importContextVO;
        this.codeReplInfoDao = codeReplInfoDao;
    }

    /**
     * 导入写入方法
     *
     * @param dataVO
     * @param analysisContext
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void invoke(ReplaceImportVO dataVO, AnalysisContext analysisContext) {
        currentDataList.get().add(dataVO);
        if (currentDataList.get().size() >= 10000) {
            dataProcess();
        }
    }

    // 数据集合处理
    private void dataProcess() {
        List<ReplaceImportVO> dataList = currentDataList.get();
        // 校验导入的数据
        String errorMsg = checkImportData(dataList);
        // 不存在问题数据，直接保存入库
        if (StringUtils.isBlank(errorMsg)) {
            saveBatchData(dataList);
        } else {
            // 存在有问题的数据
            errorTips.get().append(errorMsg);
            String errorMessage = importContextVO.getErrorMsg();
            if (StringUtils.isNotBlank(errorMessage)) {
                importContextVO.setErrorMsg(errorMessage.concat(errorTips.get().toString()));
            } else {
                importContextVO.setErrorMsg(errorTips.get().toString());
            }
        }
        // 所有数据必须写入文件，并在个人中心里体现
        ExcelWriter workbookWriter = importContextVO.getWorkbookWriter();
        WriteSheet writeSheet = importContextVO.getWriteSheet();
        workbookWriter.write(dataList, writeSheet);
        int batchNum = importContextVO.getBatchNum();
        importContextVO.setBatchNum(batchNum++);
        importContextVO.setTotalNum(importContextVO.getTotalNum() + dataList.size());
        dataList.clear();
        currentDataList.remove();
        errorTips.remove();
    }


    //  批量插入数据
    private void saveBatchData(List<ReplaceImportVO> importDataList) {
        // 转换成需要保存的VO
        List<DmFocReplVO> saveDataList = new ArrayList<>();
        importDataList.stream().forEach(importVO -> {
            importVO.setId(codeReplInfoDao.getReplAutoKey());
            importVO.setGtsType("PROD");
            importVO.setDelFlag("N");
            importVO.setVersionId(importContextVO.getNewVersionId());
            importVO.setCreatedBy(importContextVO.getUserId());
            importVO.setCreationDate(new Date());
            importVO.setLastUpdatedBy(importContextVO.getUserId());
            importVO.setLastUpdateDate(new Date());
            DmFocReplVO dmFocReplVO = new DmFocReplVO();
            BeanUtils.copyProperties(importVO, dmFocReplVO);
            saveDataList.add(dmFocReplVO);
        });
        // 数据量小于1000条直接插入，否则分批插入
        if (saveDataList.size() <= 1000) {
            codeReplInfoDao.createDmCodeReplInfoList(saveDataList);
            return;
        }
        Lists.partition(saveDataList, 1000).stream()
                .forEach(voList -> codeReplInfoDao.createDmCodeReplInfoList(voList));
    }

    // 处理完后
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        // 处理尾部数据，不足1w的数据
        if (!currentDataList.get().isEmpty()) {
            dataProcess();
        }
        lv1Map.clear();
        lv2Map.clear();
        lv3Map.clear();
        lv4Map.clear();
        log.info(">>>>>CodeReplaceImportListener Import Completed!<<<<<");
    }

    private String checkImportData(List<ReplaceImportVO> dataList) {
        // 记录行数
        AtomicInteger lineCount = new AtomicInteger(1);
        StringBuilder builder = new StringBuilder();
        dataList.stream().forEach(data -> {
            lineCount.addAndGet(1);
            checkOneData(data);
            if (StringUtils.isNotEmpty(data.getErrorMessage())) {
                builder.append("第" + (importContextVO.getBatchNum() * 10000 + lineCount.get()) + "行存在"
                        + StringUtils.defaultString(data.getErrorMessage()));
            }
        });
        return builder.toString();
    }

    private void checkOneData(ReplaceImportVO importVO) {
        StringBuilder builder = new StringBuilder();
        // 校验为空字段
        validateVOField(importVO, builder);
        if (builder.length() > 0) {
            importVO.setErrorMessage(builder.toString());
        }
    }

    private void trimReplParamSpace(ReplaceImportVO importVO) {
        if (StringUtils.isNotBlank(importVO.getProdCnName())) {
            importVO.setProdCnName(importVO.getProdCnName().trim());
        }
        if (StringUtils.isNotBlank(importVO.getReplaceRelationName())) {
            importVO.setReplaceRelationName(importVO.getReplaceRelationName().trim());
        }
        if (StringUtils.isNotBlank(importVO.getOldSpartDesc())) {
            importVO.setOldSpartDesc(importVO.getOldSpartDesc().trim());
        }
        if (StringUtils.isNotBlank(importVO.getNewSpartDesc())) {
            importVO.setNewSpartDesc(importVO.getNewSpartDesc().trim());
        }
    }

    private void validSpecialStr(ReplaceImportVO importVO, StringBuilder stringBuilder) {
        if (FcstIndustryUtil.verifySpecialCharacter(importVO.getProdCnName())) {
            stringBuilder.append("产品名称含有特殊字符，请检查;");
        }
        if (FcstIndustryUtil.verifySpecialCharacter(importVO.getReplaceRelationName())) {
            stringBuilder.append("替换关系名称含有特殊字符，请检查;");
        }
        // 校验新老编码替换类型
        if (StringUtils.isNotBlank(importVO.getReplaceRelationType())
                && !Arrays.asList("一对一", "一对多", "多对一", "多对多").contains(importVO.getReplaceRelationType())) {
            stringBuilder.append("新老编码替换类型不正确，请检查;");
        }
        // 根据新老编码替换类型校验新老编码数量是否一致
        validateSpartCodeNum(importVO, stringBuilder);
        // 校验关系
        if (StringUtils.isNotBlank(importVO.getRelationType()) && !Arrays.asList("收编", "替换").contains(importVO.getRelationType())) {
            stringBuilder.append("关系不正确，请检查;");
        }
    }

    private void validateSpartCodeNum(ReplaceImportVO importVO, StringBuilder stringBuilder) {
        // 1个老编码对应多个新编码
        if ("一对多".equals(importVO.getReplaceRelationType()) && (StringUtils.isNotBlank(importVO.getNewSpartCode())
                && importVO.getNewSpartCode().split(",").length < 2)) {
            stringBuilder.append("新老编码替换类型与编码数量不一致，请检查;");
        }
        // 多个老编码对应1个新编码
        if ("多对一".equals(importVO.getReplaceRelationType()) && (StringUtils.isNotBlank(importVO.getOldSpartCode())
                && importVO.getOldSpartCode().split(",").length < 2)) {
            stringBuilder.append("新老编码替换类型与编码数量不一致，请检查;");
        }
        // 多个老编码对应多个新编码
        if ("多对多".equals(importVO.getReplaceRelationType())
                && ((StringUtils.isNotBlank(importVO.getOldSpartCode()) && importVO.getOldSpartCode().split(",").length < 2)
                || (StringUtils.isNotBlank(importVO.getNewSpartCode()) && importVO.getNewSpartCode().split(",").length < 2))) {
            stringBuilder.append("新老编码替换类型与编码数量不一致，请检查;");
        }
    }

    private void validateVOField(ReplaceImportVO importVO, StringBuilder builder) {
        // 去除空格
        trimReplParamSpace(importVO);
        // 校验必填项
        ExcelImportValid.valid(importVO);
        builder.append(importVO.getErrorMessage());
        // 填充BG/L1-L3.5编码信息
        setLv1ToLv4Code(importVO, builder);
        // 校验特殊字符
        validSpecialStr(importVO, builder);
        // 校验新编码上市时间和切换开始时间
        validateDates(importVO, builder);
    }

    private void validateDates(ReplaceImportVO importVO, StringBuilder builder) {
        // 新编码上市时间和切换开始时间填写格式为YYYY-MM-DD、YYYY/MM/DD
        if (StringUtils.isNotBlank(importVO.getNewSpartGtmDate()) && !matchDatePattern(importVO.getNewSpartGtmDate())) {
            builder.append("新编码上市时间格式不正确，请检查;");
        }
        if (StringUtils.isNotBlank(importVO.getReplaceBeginDate()) && !matchDatePattern(importVO.getReplaceBeginDate())) {
            builder.append("切换开始时间格式不正确，请检查;");
        }
        // 切换开始时间 >= 新编码上市时间
        if (StringUtils.isNotBlank(importVO.getNewSpartGtmDate()) && StringUtils.isNotBlank(importVO.getReplaceBeginDate())
                && (matchDatePattern(importVO.getNewSpartGtmDate()) && matchDatePattern(importVO.getReplaceBeginDate()))) {
            try {
                LocalDate newDate = getLocalDate(importVO.getNewSpartGtmDate());
                LocalDate beginDate = getLocalDate(importVO.getReplaceBeginDate());
                if (beginDate.isBefore(newDate)) {
                    builder.append("切换开始时间不能小于新编码上市时间;");
                }
            } catch (DateTimeParseException ex) {
                log.error(">>>Invalid DateTime Format ==>{}", ex.getMessage());
            }
        }
    }

    private LocalDate getLocalDate(String newSpartGtmDate) {
        return newSpartGtmDate.contains("-")
                ? LocalDate.parse(newSpartGtmDate, formatter)
                : LocalDate.parse(newSpartGtmDate, formatter1);
    }

    private boolean matchDatePattern(String newSpartGtmDate) {
        return pattern.matcher(newSpartGtmDate).matches();
    }

    private void setLv1ToLv4Code(ReplaceImportVO importVO, StringBuilder builder) {
        List<DmFocReplVO> bgCodeFromProductDim = codeReplInfoDao.findBgCodeFromProductDim();
        List<String> bgCodeCollect = bgCodeFromProductDim.stream().map(item -> item.getBgCode()).collect(Collectors.toList());
        List<String> bgCnNameCollect = bgCodeFromProductDim.stream().map(item -> item.getBgCnName()).collect(Collectors.toList());
        if (!bgCnNameCollect.contains(importVO.getBgCnName())) {
            builder.append("BG名称不正确").append(";");
        } else {
            DmFocReplVO dmFocReplVO = bgCodeFromProductDim.stream().filter(item -> importVO.getBgCnName().equals(item.getBgCnName())).findFirst().orElse(new DmFocReplVO());
            importVO.setBgCode(dmFocReplVO.getBgCode());
            ReplaceSearchVO searchVO = new ReplaceSearchVO();
            searchVO.setBgCodeList(bgCodeCollect);
            if (lv1Map.isEmpty() ) {
                searchVO.setGroupLevel("LV1");
                List<DmFocReplVO> lv1DataList = codeReplInfoDao.findProductDimList(searchVO);
                lv1Map = lv1DataList.stream().collect(
                        Collectors.groupingBy(item -> item.getBgCode(), HashMap::new, Collectors.toList()));
            }
            if (lv2Map.isEmpty()) {
                searchVO.setGroupLevel("LV2");
                List<DmFocReplVO> lv2DataList = codeReplInfoDao.findProductDimList(searchVO);
                lv2Map = lv2DataList.stream().collect(
                        Collectors.groupingBy(item -> item.getBgCode(), HashMap::new, Collectors.toList()));
            }
            if (lv3Map.isEmpty()) {
                searchVO.setGroupLevel("LV3");
                List<DmFocReplVO> lv3DataList = codeReplInfoDao.findProductDimList(searchVO);
                lv3Map = lv3DataList.stream().collect(
                        Collectors.groupingBy(item -> item.getBgCode(), HashMap::new, Collectors.toList()));
            }
            if (lv4Map.isEmpty()) {
                searchVO.setGroupLevel("LV4");
                List<DmFocReplVO> lv4DataList = codeReplInfoDao.findProductDimList(searchVO);
                lv4Map = lv4DataList.stream().collect(
                        Collectors.groupingBy(item -> item.getBgCode(), HashMap::new, Collectors.toList()));
            }
            List<DmFocReplVO> lv1MapByBgCode = lv1Map.get(importVO.getBgCode());
            Map<String, String> lv1LevelMap = lv1MapByBgCode.stream().collect(Collectors.toMap(DmFocReplVO::getLv1CnName, DmFocReplVO::getLv1Code, (v1, v2) -> v1));
            lv1LevelMap.put("ALL", "ALL");
            List<DmFocReplVO> lv2MapByBgCode = lv2Map.get(importVO.getBgCode());
            Map<String, String> lv2LevelMap = lv2MapByBgCode.stream().collect(Collectors.toMap(DmFocReplVO::getLv2CnName, DmFocReplVO::getLv2Code, (v1, v2) -> v1));
            lv2LevelMap.put("ALL", "ALL");
            List<DmFocReplVO> lv3MapByBgCode = lv3Map.get(importVO.getBgCode());
            Map<String, String> lv3LevelMap = lv3MapByBgCode.stream().collect(Collectors.toMap(DmFocReplVO::getLv3CnName, DmFocReplVO::getLv3Code, (v1, v2) -> v1));
            lv3LevelMap.put("ALL", "ALL");
            List<DmFocReplVO> lv4MapByBgCode = lv4Map.get(importVO.getBgCode());
            Map<String, String> lv4LevelMap = lv4MapByBgCode.stream().collect(Collectors.toMap(DmFocReplVO::getLv4CnName, DmFocReplVO::getLv4Code, (v1, v2) -> v1));
            lv4LevelMap.put("ALL", "ALL");
            importVO.setLv1Code(lv1LevelMap.get(importVO.getLv1CnName()));
            importVO.setLv2Code(lv2LevelMap.get(importVO.getLv2CnName()));
            importVO.setLv3Code(lv3LevelMap.get(importVO.getLv3CnName()));
            importVO.setLv4Code(lv4LevelMap.get(importVO.getLv4CnName()));
        }
    }

}
