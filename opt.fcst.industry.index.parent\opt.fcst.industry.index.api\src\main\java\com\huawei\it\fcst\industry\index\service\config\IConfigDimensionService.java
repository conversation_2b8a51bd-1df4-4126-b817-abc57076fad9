/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.service.config;

import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.config.DimensionParamVO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocCatgCegIctDTO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoVO;
import com.huawei.it.fcst.industry.index.vo.relation.DimensionInputVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import io.swagger.annotations.Api;

import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import java.util.List;

/**
 * IConfigDimensionService Class
 *
 * <AUTHOR>
 * @since 2023/3/13
 */
@Path("/configDimension")
@Api(value = "配置管理维护维表服务")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface IConfigDimensionService {
    /**
     * [查询版本信息]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @Path("/version/dataType")
    @POST
    ResultDataVO findVersion(DmFocVersionInfoVO dmFocVersionInfoVo) throws CommonApplicationException;

    /**
     * [映射关系维表查询专项采购认证部下拉框]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @Path("/version/list")
    @GET
    ResultDataVO getListByVersionId(@QueryParam("industryOrg") String industryOrg,@QueryParam("versionId") Long versionId,@QueryParam("l3CegCode") String l3CegCode) throws CommonApplicationException;

    /**
     * [映射关系维表新增编辑下拉框获取专家团编码和简称]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @Path("/ceg")
    @GET
    ResultDataVO getShortCnNameAndCode(@QueryParam("industryOrg") String industryOrg,@QueryParam("keyWord") String keyword,@QueryParam("l3CegCode") String l3CegCode) throws CommonApplicationException;

    /**
     * [映射关系维表，新增编辑下获取品类名称和编码]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @Path("/cate")
    @GET
    ResultDataVO getCategoryCodeByName(@QueryParam("keyWord") String keyword,@QueryParam("industryOrg") String industryOrg) throws CommonApplicationException;
    
    /**
     * [映射关系维表查询接口]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @Path("/relationList")
    @POST
    ResultDataVO relationList(DimensionParamVO dimensionParamVO) throws CommonApplicationException;

    /**
     * [映射关系维表维度刷新]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @POST
    @Path("/dimensionUpdate")
    ResultDataVO dimensionUpdate(DimensionInputVO dimensionInputVO) throws CommonApplicationException;

    /**
     * [映射关系维表获取系统时间接口]
     *
     * @return ResultDataVO
     */
    @GET
    @Path("/getSystemTime")
    ResultDataVO getSystemTime();

    /**
     * [映射关系维表维度删除接口]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @POST
    @Path("/relationDelete")
    ResultDataVO relationDelete(List<DmFocCatgCegIctDTO> dmFocCatgCegIctDTOList) throws CommonApplicationException;

    /**
     * [采购价格指数配置页面的映射维表数据，同步至产业成本指数映射维表]
     *
     * @return ResultDataVO
     */
    @GET
    @Path("/getSyncDimPurchar")
    ResultDataVO getSyncDimPurchar(@QueryParam("industryOrg") String industryOrg) throws CommonApplicationException;


    /**
     * [采购价格指数配置页面的映射维表数据，1-8号0点同步至产业成本指数映射维表]
     *
     */
    @GET
    @Path("/syncDimPurchar")
    void syncDimPurchar();
}
