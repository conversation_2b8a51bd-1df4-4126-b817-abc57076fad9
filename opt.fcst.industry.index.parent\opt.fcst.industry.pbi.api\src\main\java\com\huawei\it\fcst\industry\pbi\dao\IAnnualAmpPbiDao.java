/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.dao;

import com.huawei.it.fcst.industry.pbi.vo.annual.AnnualAnalysisVO;
import com.huawei.it.fcst.industry.pbi.vo.annual.DmFocAnnualAmpVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * IAnnualAmpDao
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
public interface IAnnualAmpPbiDao {

    List<DmFocAnnualAmpVO> allIndustryNormalCost(AnnualAnalysisVO annualAnalysisVO);

    List<String> getAnnualperiodYearList(@Param("granularityType") String granularityType,@Param("versionId") Long versionId);

    List<DmFocAnnualAmpVO> findGroupCodeOrderByWeight(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> multiIndustryPbiCostChart(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> multiIndustryMinLevelChart(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> industryPbiCostList(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> industryMinLevelCostList(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> distributePbiCostChart(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> multiNormalSpartChart(AnnualAnalysisVO annualAnalysisVO);
}
