/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.template;

import com.huawei.it.fcst.export.IExcelTemplateBeanManager;
import com.huawei.it.fcst.export.vo.ExcelTemplateBeanManager;
import com.huawei.it.fcst.export.vo.SheetBeanMetaVO;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 月度分析页面单选导出模板
 *
 * <AUTHOR>
 * @since 2024年7月12日
 */
@Getter
public enum MonthAnalysisTemplateEnum implements IExcelTemplateBeanManager {
    MONTH_CODE_01("01", "MonthAnalysisExportTemplate1", "成本指数-ICT-月度分析", "成本指数-ICT-月度分析") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            List<SheetBeanMetaVO> sheetBeans = new ArrayList<>();
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_01.templateName, 0, "CostIndexChartExpDataProvider", "产业成本指数图", Boolean.FALSE));
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_01.templateName, 1, "YoyAndPopChartExpDataProvider", "产业成本指数图-同比环比图", Boolean.FALSE));
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_01.templateName, 2, "CostRedTargetChartExpDataProvider", "降成本目标对比图", Boolean.FALSE));
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_01.templateName, 3, "MultiCostIndexChartExpDataProvider", "产业成本指数图（多指数）", Boolean.FALSE));
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_01.templateName, 4, "CostDistributionChartExpDataProvider", "成本分布图", Boolean.FALSE));
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_01.templateName, 5, "WeightChartExpDataProvider", "权重图", Boolean.FALSE));
            return new ExcelTemplateBeanManager(MONTH_CODE_01.templateName,MONTH_CODE_01.moduleType,MONTH_CODE_01.desc,sheetBeans);
        }
    },
    MONTH_CODE_02("02", "MonthAnalysisExportTemplate2", "成本指数-ICT-月度分析", "成本指数-ICT-月度分析") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            List<SheetBeanMetaVO> sheetBeans = new ArrayList<>();
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_02.templateName, 0, "CostIndexChartExpDataProvider", "产业成本指数图", Boolean.FALSE));
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_02.templateName, 1, "YoyAndPopChartExpDataProvider", "产业成本指数图-同比环比图", Boolean.FALSE));
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_02.templateName, 2, "MultiCostIndexChartExpDataProvider", "产业成本指数图（多指数）", Boolean.FALSE));
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_02.templateName, 3, "CostDistributionChartExpDataProvider", "成本分布图", Boolean.FALSE));
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_02.templateName, 4, "WeightChartExpDataProvider", "权重图", Boolean.FALSE));
            return new ExcelTemplateBeanManager(MONTH_CODE_02.templateName,MONTH_CODE_02.moduleType,MONTH_CODE_02.desc,sheetBeans);
        }
    },
    MONTH_CODE_03("03", "MonthAnalysisExportTemplate3", "成本指数-ICT-月度分析", "成本指数-ICT-月度分析") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            List<SheetBeanMetaVO> sheetBeans = new ArrayList<>();
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_03.templateName, 0, "CostIndexChartExpDataProvider", "产业成本指数图", Boolean.FALSE));
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_03.templateName, 1, "YoyAndPopChartExpDataProvider", "产业成本指数图-同比环比图", Boolean.FALSE));
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_03.templateName, 2, "CostRedTargetChartExpDataProvider", "降成本目标对比图", Boolean.FALSE));
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_03.templateName, 3, "MultiCostIndexChartExpDataProvider", "产业成本指数图（多指数）", Boolean.FALSE));
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_03.templateName, 4, "WeightChartExpDataProvider", "权重图", Boolean.FALSE));
            return new ExcelTemplateBeanManager(MONTH_CODE_03.templateName,MONTH_CODE_03.moduleType,MONTH_CODE_03.desc,sheetBeans);
        }
    },
    MONTH_CODE_04("04", "MonthAnalysisExportTemplate4", "成本指数-ICT-月度分析", "成本指数-ICT-月度分析") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            List<SheetBeanMetaVO> sheetBeans = new ArrayList<>();
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_04.templateName, 0, "CostIndexChartExpDataProvider", "产业成本指数图", Boolean.FALSE));
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_04.templateName, 1, "YoyAndPopChartExpDataProvider", "产业成本指数图-同比环比图", Boolean.FALSE));
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_04.templateName, 2, "CostRedTargetChartExpDataProvider", "降成本目标对比图", Boolean.FALSE));
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_04.templateName, 3, "CostDistributionChartExpDataProvider", "成本分布图", Boolean.FALSE));
            return new ExcelTemplateBeanManager(MONTH_CODE_04.templateName,MONTH_CODE_04.moduleType,MONTH_CODE_04.desc,sheetBeans);
        }
    },
    MONTH_CODE_05("05", "MonthAnalysisExportTemplate5", "成本指数-ICT-月度分析", "成本指数-ICT-月度分析") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            List<SheetBeanMetaVO> sheetBeans = new ArrayList<>();
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_05.templateName, 0, "CostIndexChartExpDataProvider", "产业成本指数图", Boolean.FALSE));
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_05.templateName, 1, "YoyAndPopChartExpDataProvider", "产业成本指数图-同比环比图", Boolean.FALSE));
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_05.templateName, 2, "MultiCostIndexChartExpDataProvider", "产业成本指数图（多指数）", Boolean.FALSE));
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_05.templateName, 3, "WeightChartExpDataProvider", "权重图", Boolean.FALSE));
            return new ExcelTemplateBeanManager(MONTH_CODE_05.templateName,MONTH_CODE_05.moduleType,MONTH_CODE_05.desc,sheetBeans);
        }
    },
    MONTH_CODE_06("06", "MonthAnalysisExportTemplate6", "成本指数-ICT-月度分析", "成本指数-ICT-月度分析") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            List<SheetBeanMetaVO> sheetBeans = new ArrayList<>();
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_06.templateName, 0, "CostIndexChartExpDataProvider", "产业成本指数图", Boolean.FALSE));
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_06.templateName, 1, "YoyAndPopChartExpDataProvider", "产业成本指数图-同比环比图", Boolean.FALSE));
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_06.templateName, 2, "CostDistributionChartExpDataProvider", "成本分布图", Boolean.FALSE));
            return new ExcelTemplateBeanManager(MONTH_CODE_06.templateName,MONTH_CODE_06.moduleType,MONTH_CODE_06.desc,sheetBeans);
        }
    },
    MONTH_CODE_07("07", "MonthAnalysisExportTemplate7", "成本指数-ICT-月度分析", "成本指数-ICT-月度分析") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            List<SheetBeanMetaVO> sheetBeans = new ArrayList<>();
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_07.templateName, 0, "CostIndexChartExpDataProvider", "产业成本指数图", Boolean.FALSE));
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_07.templateName, 1, "YoyAndPopChartExpDataProvider", "产业成本指数图-同比环比图", Boolean.FALSE));
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_07.templateName, 2, "CostRedTargetChartExpDataProvider", "降成本目标对比图", Boolean.FALSE));
            return new ExcelTemplateBeanManager(MONTH_CODE_07.templateName,MONTH_CODE_07.moduleType,MONTH_CODE_07.desc,sheetBeans);
        }
    },
    MONTH_CODE_08("08", "MonthAnalysisExportTemplate8", "成本指数-ICT-月度分析", "成本指数-ICT-月度分析") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            List<SheetBeanMetaVO> sheetBeans = new ArrayList<>();
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_08.templateName, 0, "CostIndexChartExpDataProvider", "产业成本指数图", Boolean.FALSE));
            sheetBeans.add(new SheetBeanMetaVO(MONTH_CODE_08.templateName, 1, "YoyAndPopChartExpDataProvider", "产业成本指数图-同比环比图", Boolean.FALSE));
            return new ExcelTemplateBeanManager(MONTH_CODE_08.templateName,MONTH_CODE_08.moduleType,MONTH_CODE_08.desc,sheetBeans);
        }
    };

    private String code;
    private String templateName;
    private String moduleType;
    private String desc;

    MonthAnalysisTemplateEnum(String templateCode, String templateName, String moduleType, String desc) {
        this.code = templateCode;
        this.templateName = templateName;
        this.moduleType = moduleType;
        this.desc = desc;
    }

}
