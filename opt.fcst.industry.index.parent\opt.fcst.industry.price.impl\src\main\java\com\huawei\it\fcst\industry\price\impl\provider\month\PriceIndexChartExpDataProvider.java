/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.impl.provider.month;

import com.huawei.it.fcst.export.IExcelExportDataProvider;
import com.huawei.it.fcst.export.vo.ExcelExportContext;
import com.huawei.it.fcst.export.vo.ExportList;
import com.huawei.it.fcst.industry.price.service.month.IPriceMonthAnalysisService;
import com.huawei.it.fcst.industry.price.vo.month.PriceMonthAnalysisVO;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import javax.inject.Inject;
import javax.inject.Named;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 定价指数图导出数据提供类
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
@Slf4j
@Named("IExcelExport.PriceIndexChartExpDataProvider")
public class PriceIndexChartExpDataProvider implements IExcelExportDataProvider {

    @Inject
    private IPriceMonthAnalysisService monthAnalysisService;

    @Override
    public List<?> getData(Serializable conditionObject, PageVO pageVO) throws ApplicationException, InterruptedException {
        log.info(">>>Begin PriceIndexChartExpDataProvider::getData");
        PriceMonthAnalysisVO monthAnalysisVO = (PriceMonthAnalysisVO)conditionObject;
        PriceMonthAnalysisVO paramsVO = new PriceMonthAnalysisVO();
        BeanUtils.copyProperties(monthAnalysisVO, paramsVO);
        ResultDataVO resultDataVO = monthAnalysisService.getPriceIndexChart(paramsVO);
        List<PriceMonthAnalysisVO> dataList = (List<PriceMonthAnalysisVO>) resultDataVO.getData();
        dataList = Optional.ofNullable(dataList).orElse(new ArrayList<>());
        if (monthAnalysisVO.getIsMultipleSelect()) {
            // 设置SPART层级时数据展示的格式
            dataList.stream().forEach(item -> {
                if ("SPART".equals(monthAnalysisVO.getGroupLevel())) {
                    item.setGroupCnName(item.getGroupCode());
                }
            });
        }
        ExportList exportList = new ExportList();
        exportList.addAll(dataList);
        exportList.setTotalRows(dataList.size());
        return exportList;
    }

    @Override
    public Map<String, Object> getHeader(Serializable conditionObject, Map<String, Object> parameters) throws ApplicationException {
        ExcelExportContext context = (ExcelExportContext)conditionObject;
        PriceMonthAnalysisVO priceMonthAnalysisVO = (PriceMonthAnalysisVO)context.getConditionObject();
        return monthAnalysisService.getHeaderInfo(priceMonthAnalysisVO);
    }
}