/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.dao;

import com.huawei.it.fcst.industry.pbi.vo.month.IctMonthAnalysisVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * IctMonthCostIdxDao Interface
 *
 * <AUTHOR>
 * @since 2024-07-05
 */
public interface IctMonthCostAmtDao {
    /**
     * 查询产业成本分布图（ICT）
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<IctMonthAnalysisVO> findCostAmtVOList(@Param("monthAnalysisVO") IctMonthAnalysisVO monthAnalysisVO);


    /**
     * 查询产业成本分布图（ICT）汇总组合
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<IctMonthAnalysisVO> findCostCombAmtVOList(@Param("monthAnalysisVO") IctMonthAnalysisVO monthAnalysisVO);

    /**
     * 查询产业成本分布图（ICT）虚化
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<IctMonthAnalysisVO> findBlurCostAmtVOList(@Param("monthAnalysisVO") IctMonthAnalysisVO monthAnalysisVO);

}