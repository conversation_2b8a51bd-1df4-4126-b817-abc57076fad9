/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.month;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

/**
 * DmFocTotalActualCostVOTest Class
 *
 * <AUTHOR>
 * @since 2023/11/3
 */
public class DmFocTotalActualCostVOTest extends BaseVOCoverUtilsTest<DmFocTotalActualCostVO> {

    @Override
    protected Class<DmFocTotalActualCostVO> getTClass() {
        return DmFocTotalActualCostVO.class;
    }

    @Test
    public void testMethod() {
        DmFocTotalActualCostVO dmFocActualCostVO = new DmFocTotalActualCostVO();
        dmFocActualCostVO.setGroupCode("1163A");
        dmFocActualCostVO.getGroupCode();
        dmFocActualCostVO.setGroupCnName("元器");
        dmFocActualCostVO.getGroupCnName();
        dmFocActualCostVO.setPeriodId(13L);
        dmFocActualCostVO.getPeriodId();
        dmFocActualCostVO.setParentCnName("11");
        dmFocActualCostVO.getParentCnName();
        dmFocActualCostVO.setPurAmt(11.2);
        dmFocActualCostVO.getPurAmt();
        dmFocActualCostVO.setMadeWeight("0.11");
        dmFocActualCostVO.getMadeWeight();
        dmFocActualCostVO.setMadeAmt(0.6);
        dmFocActualCostVO.getMadeAmt();
        dmFocActualCostVO.setTotalAmt(55.22);
        dmFocActualCostVO.getTotalAmt();
        dmFocActualCostVO.setPurWeight("11%");
        dmFocActualCostVO.getPurWeight();
        Assert.assertNotNull(dmFocActualCostVO);
    }
}