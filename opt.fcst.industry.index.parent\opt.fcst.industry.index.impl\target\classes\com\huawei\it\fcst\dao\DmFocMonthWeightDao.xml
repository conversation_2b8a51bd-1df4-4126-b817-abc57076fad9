<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IDmFocMonthWeightDao">
    <resultMap type="com.huawei.it.fcst.industry.index.vo.month.DmFocMonthWeightVO" id="resultMap">
        <result property="id" column="id"/>
        <result property="versionId" column="version_id"/>
        <result property="periodYear" column="period_year"/>
        <result property="periodYearType" column="period_year_type"/>
        <result property="prodRndTeamCode" column="prod_rnd_team_code"/>
        <result property="prodRndTeamCnName" column="prod_rnd_team_cn_name"/>
        <result property="groupCode" column="group_code"/>
        <result property="groupCnName" column="group_cn_name"/>
        <result property="groupLevel" column="group_level"/>
        <result property="parentLevel" column="parent_level"/>
        <result property="weightRate" column="weight_rate"/>
        <result property="weightPercent" column="weight_percent"/>
        <result property="parentCode" column="parent_code"/>
        <result property="parentCnName" column="parent_cn_name"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="l1Name" column="l1_name"/>
        <result property="l2Name" column="l2_name"/>
        <result property="dmsCode" column="dms_code"/>
        <result property="dmsCnName" column="dms_cn_name"/>
        <result property="coaCode" column="coa_code"/>
        <result property="coaCnName" column="coa_cn_name"/>
        <result property="dimensionCode" column="dimension_code"/>
        <result property="dimensionCnName" column="dimension_cn_name"/>
        <result property="dimensionSubCategoryCode" column="dimension_subcategory_code"/>
        <result property="dimensionSubCategoryCnName" column="dimension_subcategory_cn_name"/>
        <result property="dimensionSubDetailCode" column="dimension_sub_detail_code"/>
        <result property="dimensionSubDetailCnName" column="dimension_sub_detail_cn_name"/>
        <result property="spartCode" column="spart_code"/>
        <result property="spartCnName" column="spart_cn_name"/>
        <result property="customId" column="custom_id"/>
        <result property="customCnName" column="custom_cn_name"/>
        <result property="costType" column="cost_type"/>
    </resultMap>

    <select id="findWeightList" resultMap="resultMap">
        <if test='searchParamsVO.granularityType == "U"'>
            SELECT distinct t1.prod_rnd_team_code,t1.prod_rnd_team_cn_name,
                t1.parent_code, t1.parent_cn_name,
                t1.group_level, t1.weight_rate,
                ROUND(t1.weight_rate * 100, 2) || '%' AS weight_percent,
                t1.group_code,
                'P' AS cost_type,
                t1.group_cn_name
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_month_weight_t t1
        </if>
        <if test='searchParamsVO.granularityType == "P"'>
            SELECT distinct t1.prod_rnd_team_code,t1.prod_rnd_team_cn_name,t1.l1_name,t1.l2_name,
                t1.parent_code, t1.parent_cn_name,
                t1.group_level, t1.weight_rate,
                ROUND(t1.weight_rate * 100, 2) || '%' AS weight_percent,
                t1.group_code,
                'P' AS cost_type,
                t1.group_cn_name
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_pft_month_weight_t t1
        </if>
        <if test='searchParamsVO.granularityType == "D"'>
            SELECT distinct t1.prod_rnd_team_code,t1.prod_rnd_team_cn_name,
                t1.dms_code,t1.dms_cn_name,
                <if test='searchParamsVO.industryOrg == "ENERGY"'>
                    t1.coa_code,t1.coa_cn_name,
                </if>
                t1.dimension_code,t1.dimension_cn_name,
                t1.dimension_subcategory_code,t1.dimension_subcategory_cn_name,
                t1.dimension_sub_detail_code,t1.dimension_sub_detail_cn_name,
                t1.spart_code,t1.spart_cn_name,
                t1.parent_code, t1.parent_cn_name,
                t1.group_level, t1.weight_rate,
                ROUND(t1.weight_rate * 100, 2) || '%' AS weight_percent,
                t1.group_code,
                'P' AS cost_type,
                t1.group_cn_name
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_dms_month_weight_t t1
        </if>
        WHERE t1.del_flag = 'N'
        AND t1.period_year_type = 'S'
        AND t1.version_id = #{searchParamsVO.versionId}
        <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
            and t1.lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode}
        </if>
        <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
            AND t1.caliber_Flag = #{searchParamsVO.caliberFlag}
        </if>
        <if test='searchParamsVO.parentCodeList != null and searchParamsVO.parentCodeList.size() > 0'>
            <foreach collection='searchParamsVO.parentCodeList' item="code" open="AND t1.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.subGroupCodeList != null and searchParamsVO.subGroupCodeList.size() > 0'>
            <foreach collection='searchParamsVO.subGroupCodeList' item="code" open="AND t1.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.coaCodeList != null and searchParamsVO.coaCodeList.size() > 0'>
            <foreach collection='searchParamsVO.coaCodeList' item="code" open="AND t1.coa_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionCodeList != null and searchParamsVO.dimensionCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionCodeList' item="code" open="AND t1.dimension_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubcategoryCodeList != null and searchParamsVO.dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubcategoryCodeList' item="code" open="AND t1.dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubDetailCodeList != null and searchParamsVO.dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubDetailCodeList' item="code" open="AND t1.dimension_sub_detail_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.spartCodeList != null and searchParamsVO.spartCodeList.size() > 0'>
            <foreach collection='searchParamsVO.spartCodeList' item="code" open="AND t1.spart_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.prodRndTeamCodeList != null and searchParamsVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='searchParamsVO.prodRndTeamCodeList' item="code" open="AND t1.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l1NameList != null and searchParamsVO.l1NameList.size() > 0'>
            <foreach collection='searchParamsVO.l1NameList' item="code" open="AND t1.l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l2NameList != null and searchParamsVO.l2NameList.size() > 0'>
            <foreach collection='searchParamsVO.l2NameList' item="code" open="AND t1.l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupLevel =="LV1" and searchParamsVO.lv1DimensionSet != null and searchParamsVO.lv1DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv1DimensionSet' item="code" open="AND t1.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupLevel =="LV2" and searchParamsVO.lv2DimensionSet != null and searchParamsVO.lv2DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv2DimensionSet' item="code" open="AND t1.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
            and t1.oversea_flag = #{searchParamsVO.overseaFlag}
        </if>
        AND t1.view_flag = #{searchParamsVO.viewFlag}
        AND t1.group_level = #{searchParamsVO.groupLevel}
        ORDER BY t1.weight_rate DESC, t1.parent_cn_name DESC
    </select>

    <select id="findNormalWeightList" resultMap="resultMap">
        <if test='searchParamsVO.granularityType == "U"'>
            SELECT DISTINCT
                t1.prod_rnd_team_code,t1.prod_rnd_team_cn_name,
                t1.parent_code, t1.parent_cn_name,
                t1.group_code,
                t1.group_level,
                t1.weight_rate,
                ROUND( t1.weight_rate * 100, 2 ) || '%' AS weight_percent,
                'P' AS cost_type,
                t1.group_cn_name
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_month_weight_t t1
        </if>
        <if test='searchParamsVO.granularityType == "P"'>
            SELECT DISTINCT
                t1.prod_rnd_team_code,t1.prod_rnd_team_cn_name,t1.l1_name,t1.l2_name,
                t1.parent_code, t1.parent_cn_name,
                t1.group_code,
                t1.group_level,
                t1.weight_rate,
                ROUND( t1.weight_rate * 100, 2 ) || '%' AS weight_percent,
                'P' AS cost_type,
                t1.group_cn_name
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_pft_month_weight_t t1
        </if>
        <if test='searchParamsVO.granularityType == "D"'>
            SELECT DISTINCT
                t1.prod_rnd_team_code,t1.prod_rnd_team_cn_name,
                t1.dms_code,t1.dms_cn_name,
            <if test='searchParamsVO.industryOrg == "ENERGY"'>
                t1.coa_code,t1.coa_cn_name,
            </if>
                t1.dimension_code,t1.dimension_cn_name,
                t1.dimension_subcategory_code,t1.dimension_subcategory_cn_name,
                t1.dimension_sub_detail_code,t1.dimension_sub_detail_cn_name,
                t1.spart_code,t1.spart_cn_name,
                t1.parent_code, t1.parent_cn_name,
                t1.group_code,
                t1.group_level,
                t1.weight_rate,
                ROUND( t1.weight_rate * 100, 2 ) || '%' AS weight_percent,
                'P' AS cost_type,
                t1.group_cn_name
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_dms_month_weight_t t1
        </if>
        WHERE t1.del_flag = 'N'
        AND t1.period_year_type = 'S'
        AND t1.version_id = #{searchParamsVO.versionId}
        AND t1.caliber_Flag = #{searchParamsVO.caliberFlag}
        <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
            and t1.lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode}
        </if>
        <if test='searchParamsVO.l1NameList!= null and searchParamsVO.l1NameList.size() > 0'>
            <foreach collection='searchParamsVO.l1NameList' item="code" open="AND t1.l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l2NameList!= null and searchParamsVO.l2NameList.size() > 0'>
            <foreach collection='searchParamsVO.l2NameList' item="code" open="AND t1.l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.coaCodeList != null and searchParamsVO.coaCodeList.size() > 0'>
            <foreach collection='searchParamsVO.coaCodeList' item="code" open="AND t1.coa_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionCodeList != null and searchParamsVO.dimensionCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionCodeList' item="code" open="AND t1.dimension_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubcategoryCodeList != null and searchParamsVO.dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubcategoryCodeList' item="code" open="AND t1.dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubDetailCodeList != null and searchParamsVO.dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubDetailCodeList' item="code" open="AND t1.dimension_sub_detail_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.spartCodeList != null and searchParamsVO.spartCodeList.size() > 0'>
            <foreach collection='searchParamsVO.spartCodeList' item="code" open="AND t1.spart_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.prodRndTeamCodeList != null and searchParamsVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='searchParamsVO.prodRndTeamCodeList' item="code" open="AND t1.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.parentCodeList != null and searchParamsVO.parentCodeList.size() > 0'>
            <foreach collection='searchParamsVO.parentCodeList' item="code" open="AND t1.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.teamLevel == "LV1" and searchParamsVO.lv1DimensionSet != null and searchParamsVO.lv1DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv1DimensionSet' item="code" open="AND t1.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.teamLevel == "LV2" and searchParamsVO.lv2DimensionSet != null and searchParamsVO.lv2DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv2DimensionSet' item="code" open="AND t1.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
            and t1.oversea_flag = #{searchParamsVO.overseaFlag}
        </if>
        AND t1.view_flag = #{searchParamsVO.viewFlag}
        AND t1.group_level = #{searchParamsVO.combGroupLevel}
        ORDER BY
        t1.weight_rate DESC
        <if test='searchParamsVO.pageSize != 0'>
            LIMIT #{searchParamsVO.curPage}, #{searchParamsVO.pageSize}
        </if>
    </select>

    <select id="findCombWeightList" resultMap="resultMap">
        SELECT distinct
            t1.custom_id,
            t1.custom_cn_name,
            t1.parent_level,
            t1.parent_cn_name,
            t1.parent_code,
            t1.weight_rate,
            ROUND(t1.weight_rate * 100, 2) || '%' AS weight_percent,
            'P' AS cost_type,
            t1.group_cn_name,
            t1.group_code
        FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_custom_month_weight_t t1
        WHERE t1.del_flag = 'N'
        AND t1.version_id = #{searchParamsVO.versionId}
        AND t1.parent_level = #{searchParamsVO.parentLevel}
        <if test='searchParamsVO.granularityType != null and searchParamsVO.granularityType != ""'>
            and t1.granularity_type = #{searchParamsVO.granularityType}
        </if>
        <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
            and t1.lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode}
        </if>
        <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
            AND t1.caliber_Flag = #{searchParamsVO.caliberFlag}
        </if>
        <if test='searchParamsVO.combinaCodeList != null and searchParamsVO.combinaCodeList.size() > 0'>
            <foreach collection='searchParamsVO.combinaCodeList' item="code" open="AND t1.custom_id || '_##' || t1.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.customIdList != null and searchParamsVO.customIdList.size() > 0'>
            <foreach collection='searchParamsVO.customIdList' item="code" open="AND t1.custom_id IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
            and t1.oversea_flag = #{searchParamsVO.overseaFlag}
        </if>
        AND t1.view_flag = #{searchParamsVO.viewFlag}
        ORDER BY t1.weight_rate DESC
        <if test='searchParamsVO.pageSize != 0'>
            LIMIT #{searchParamsVO.curPage}, #{searchParamsVO.pageSize}
        </if>
    </select>
</mapper>
