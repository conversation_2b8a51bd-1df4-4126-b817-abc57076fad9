/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.annual;

import cn.hutool.core.util.StrUtil;
import com.huawei.it.fcst.industry.index.cache.IndustryCacheHandler;
import com.huawei.it.fcst.industry.index.cache.IndustryGlobalParameterUtil;
import com.huawei.it.fcst.industry.index.constant.CommonConstant;
import com.huawei.it.fcst.industry.index.constant.Constant;
import com.huawei.it.fcst.industry.index.dao.IAnnualAmpDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocDataRefreshStatusDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocDmsViewInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeDmsViewInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadePftViewInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeViewInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTotalDmsViewInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTotalPftViewInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTotalViewInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocVersionDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocViewInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocPftViewInfoDao;
import com.huawei.it.fcst.industry.index.dao.IMadeAnnualAmpDao;
import com.huawei.it.fcst.industry.index.dao.ITotalAnnualAmpDao;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumD;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumMadeU;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumU;
import com.huawei.it.fcst.industry.index.enums.IndustryConst;
import com.huawei.it.fcst.industry.index.enums.IndustryIndexEnum;
import com.huawei.it.fcst.industry.index.enums.ModuleEnum;
import com.huawei.it.fcst.industry.index.impl.common.CommonService;
import com.huawei.it.fcst.industry.index.impl.iauth.DataDimensionService;
import com.huawei.it.fcst.industry.index.service.annual.IAnnualCommonService;
import com.huawei.it.fcst.industry.index.utils.FcstIndexMadeUtil;
import com.huawei.it.fcst.industry.index.utils.FcstIndexUtil;
import com.huawei.it.fcst.industry.index.utils.UserInfoUtils;
import com.huawei.it.fcst.industry.index.vo.combination.DmFocDataRefreshStatus;
import com.huawei.it.fcst.industry.index.vo.common.CommonViewVO;
import com.huawei.it.fcst.industry.index.vo.common.DataPermissionsVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.common.TableNameVO;
import com.huawei.it.fcst.industry.index.vo.view.ViewInfoVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.lookup.LookupItemVO;
import com.huawei.it.jalor5.lookup.service.ILookupItemQueryService;

import com.huawei.it.jalor5.security.RoleVO;
import com.huawei.it.jalor5.security.UserVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.inject.Named;

/**
 * AnnualCommonService Class
 *
 * <AUTHOR>
 * @since 2023/3/14
 */
@Named("annualCommonService")
@JalorResource(code = "annualCommonService", desc = "层级下拉框")
public class AnnualCommonService implements IAnnualCommonService {

    @Autowired
    private IDmFocViewInfoDao dmFocViewInfoDao;

    @Autowired
    private IDmFocMadeViewInfoDao dmFocMadeViewInfoDao;

    @Autowired
    private IDmFocTotalViewInfoDao dmFocTotalViewInfoDao;

    @Autowired
    private ILookupItemQueryService lookupItemQueryService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private IDmFocVersionDao dmFocVersionDao;

    @Autowired
    private IDmFocPftViewInfoDao dmFocPftViewInfoDao;

    @Autowired
    private IDmFocMadePftViewInfoDao dmFocMadePftViewInfoDao;

    @Autowired
    private IDmFocTotalPftViewInfoDao dmFocTotalPftViewInfoDao;

    @Autowired
    private IDmFocDmsViewInfoDao dmFocDmsViewInfoDao;

    @Autowired
    private IDmFocMadeDmsViewInfoDao dmFocMadeDmsViewInfoDao;

    @Autowired
    private IDmFocTotalDmsViewInfoDao dmFocTotalDmsViewInfoDao;

    @Autowired
    private DataDimensionService dataDimensionService;

    @Autowired
    private IAnnualAmpDao annualAmpDao;

    @Autowired
    private IMadeAnnualAmpDao madeAnnualAmpDao;

    @Autowired
    private ITotalAnnualAmpDao totalAnnualAmpDao;

    @Autowired
    private IDmFocDataRefreshStatusDao dataRefreshStatusDao;

    @Autowired
    private IndustryCacheHandler industryCacheHandler;

    @Override
    @JalorOperation(code = "getAnnualPeriodYear", desc = "会计期年下拉框")
    public ResultDataVO getAnnualPeriodYear(String costType,String industryOrg) {
        List<String> annualPeriodYear = getYearList(costType,industryOrg);
        return ResultDataVO.success(annualPeriodYear);
    }

    public List<String> getYearList(String costType,String industryOrg) {
        List<String> annualPeriodYear = new ArrayList<>();
        String tablePreFix = TableNameVO.getTablePreFix(industryOrg);
        Long versionId = commonService.getVersionId(IndustryIndexEnum.DataType.CATE.getValue(),TableNameVO.getTablePreFix(industryOrg));
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(costType)) {
            annualPeriodYear = annualAmpDao.getAnnualPeriodYear(tablePreFix,versionId);
        }
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(costType)) {
            annualPeriodYear = madeAnnualAmpDao.getMadeAnnualPeriodYear(tablePreFix,versionId);
        }
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(costType)) {
            annualPeriodYear = totalAnnualAmpDao.getTotalAnnualPeriodYear(tablePreFix,versionId);
        }
        return annualPeriodYear;
    }

    @Override
    @JalorOperation(code = "getBgInfoList", desc = "BG下拉框")
    public ResultDataVO getBgInfoList(CommonViewVO commonViewVO) {
        List<DmFocViewInfoVO> lv0ProdList = dmFocViewInfoDao.getLv0ProdList(commonViewVO);
        lv0ProdList = Optional.ofNullable(lv0ProdList).orElse(new ArrayList<>());
        // IAS组织下需要去掉"其他"
        if (IndustryConst.INDUSTRY_ORG.IAS.getValue().equals(commonViewVO.getIndustryOrg())) {
            lv0ProdList.removeIf(model -> model.getLv0ProdListCnName().equals("其他"));
        }
        return ResultDataVO.success(lv0ProdList);
    }

    @Override
    @JalorOperation(code = "viewInfoList", desc = "查询不同层级下拉框")
    public ResultDataVO viewInfoList(CommonViewVO commonViewVO) throws ApplicationException {
        List<DmFocViewInfoVO> dmFocCustomCombVOList = new ArrayList<>();
        setInitSearchViewVO(commonViewVO);
        IndustryIndexEnum.GRANULARITY_TYPE granule = IndustryIndexEnum.getGranularityType(commonViewVO.getGranularityType());
        // 区分入参里面传入的都是组合，还是非组合，还是组合和非组合混合多选
        Boolean combSelectFlag = isContainComb(commonViewVO);
        // 根据groupLevel计算子项level
        String nextGroupLevel;
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
            nextGroupLevel = FcstIndexMadeUtil.getNextGroupLevelByView(commonViewVO.getViewFlag(), commonViewVO.getGroupLevel(), commonViewVO.getGranularityType(),commonViewVO.getIndustryOrg());
        } else {
            nextGroupLevel = FcstIndexUtil.getNextGroupLevelByView(commonViewVO.getViewFlag(), commonViewVO.getGroupLevel(), commonViewVO.getGranularityType(),commonViewVO.getIndustryOrg());
        }
        commonViewVO.setNextGroupLevel(nextGroupLevel);
        // 加入相应层级汇总的组合名称 (配置管理页面不用添加汇总组合,总成本也不用添加汇总组合)
        boolean hasCombFlag = StringUtils.isBlank(commonViewVO.getKeyWord()) && !IndustryConst.PAGE_FLAG.CONFIG.getValue().equals(commonViewVO.getPageFlag()) && !IndustryIndexEnum.COST_TYPE.T.getValue().equals(commonViewVO.getCostType());
        if (hasCombFlag) {
            CommonViewVO combViewVO = new CommonViewVO();
            BeanUtils.copyProperties(commonViewVO, combViewVO);
            UserVO currentUser = UserInfoUtils.getCurrentUser();
            RoleVO currentRole = currentUser.getCurrentRole();
            combViewVO.setRoleId(String.valueOf(currentRole.getRoleId()));
            combViewVO.setUserId(String.valueOf(UserInfoUtils.getUserId()));
            // 各层级的汇总组合
            addCombinationVoList(combViewVO, dmFocCustomCombVOList);
            if (CollectionUtils.isNotEmpty(combViewVO.getCustomIdList())) {
                // 加入各层级组合的子项 依据传入的groupLevel和customId
                addCombinationSubVoList(combViewVO,dmFocCustomCombVOList);
            }
        }
        // 正常维度下拉框
        List<DmFocViewInfoVO> dmViewInfoVOList = getDmViewInfoVOList(commonViewVO, granule, combSelectFlag);
        // 按照特定顺序排列lv1
        List<DmFocViewInfoVO> newAnnualAmpVOList = sortLv1ByCode(dmViewInfoVOList);
        // 需要将汇总组合放在最上面
        List<DmFocViewInfoVO> allAnnualAmpVOList = new ArrayList<>();

        allAnnualAmpVOList.addAll(dmFocCustomCombVOList);

        if (CollectionUtils.isNotEmpty(newAnnualAmpVOList)) {
            allAnnualAmpVOList.addAll(newAnnualAmpVOList);
        } else {
            allAnnualAmpVOList.addAll(dmViewInfoVOList);
        }
        return ResultDataVO.success(allAnnualAmpVOList);
    }

    public List<DmFocViewInfoVO> getDmViewInfoVOList(CommonViewVO commonViewVO, IndustryIndexEnum.GRANULARITY_TYPE granule, Boolean combSelectFlag) {
        List<DmFocViewInfoVO> dmFocAnnualAmpVOList = new ArrayList<>();
        if (combSelectFlag) {
            switch (granule){
                case UNIVERSAL:
                    univesalViewInfoList(commonViewVO, dmFocAnnualAmpVOList);
                    break;
                case PROFITS:
                    profitViewInfoList(commonViewVO, dmFocAnnualAmpVOList);
                    break;
                case DIMENSION:
                    dimensionViewInfoList(commonViewVO, dmFocAnnualAmpVOList);
                    break;
                default:
                    break;
            }
        }
        return dmFocAnnualAmpVOList;
    }

    private void dimensionViewInfoList(CommonViewVO commonViewVO, List<DmFocViewInfoVO> dmFocAnnualAmpVOList) {
        if (!IndustryConst.PAGE_FLAG.ANNUAL.getValue().equals(commonViewVO.getPageFlag())) {
            dmFocAnnualAmpVOList.addAll(getDmsMonthPage(commonViewVO));
        } else {
            dmFocAnnualAmpVOList.addAll(getDmsAnnualAndRelationPage(commonViewVO));
        }
    }

    private void profitViewInfoList(CommonViewVO commonViewVO, List<DmFocViewInfoVO> dmFocAnnualAmpVOList) {
        if (!IndustryConst.PAGE_FLAG.ANNUAL.getValue().equals(commonViewVO.getPageFlag())) {
            dmFocAnnualAmpVOList.addAll(getProfitMonthPage(commonViewVO));
        } else {
            dmFocAnnualAmpVOList.addAll(getProfitAnnualAndRelationPage(commonViewVO));
        }
    }

    private void univesalViewInfoList(CommonViewVO commonViewVO, List<DmFocViewInfoVO> dmFocAnnualAmpVOList) {
        if (!IndustryConst.PAGE_FLAG.ANNUAL.getValue().equals(commonViewVO.getPageFlag())) {
            dmFocAnnualAmpVOList.addAll(getMonthPage(commonViewVO));
        } else {
            dmFocAnnualAmpVOList.addAll(getAnnualAndRelationPage(commonViewVO));
        }
    }

    @NotNull
    private Boolean isContainComb(CommonViewVO commonViewVO) {
        // 区分最大视角权限标签和是否是只查询汇总组合，不查询正常维度表单
        Boolean combSelectFlag = false;
        if (commonViewVO.getPermissionTag() || CollectionUtils.isNotEmpty(commonViewVO.getGroupCodeList())
                || StringUtils.isNotBlank(commonViewVO.getKeyWord())) {
            combSelectFlag = true;
        }
        return combSelectFlag;
    }

    private void addCombinationVoList(CommonViewVO commonViewVO, List<DmFocViewInfoVO> dmFocCustomCombVOList) {
        List<DmFocViewInfoVO> dmCustomCombVOList = new ArrayList<>();
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
            dmCustomCombVOList =  dmFocViewInfoDao.getCombinationParent(commonViewVO);
        }
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
            dmCustomCombVOList =  dmFocMadeViewInfoDao.getMadeCombinationParent(commonViewVO);
        }
        List<DmFocViewInfoVO> customCombVOList = dmCustomCombVOList.stream().map(customCombVO -> {
            DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
            dmFocViewInfoVO.setCustomId(customCombVO.getCustomId());
            dmFocViewInfoVO.setGroupCode(customCombVO.getGroupCode());
            dmFocViewInfoVO.setGroupCnName(customCombVO.getGroupCnName());
            dmFocViewInfoVO.setEnableFlag(customCombVO.getEnableFlag());
            dmFocViewInfoVO.setIsCombination(true);
            return dmFocViewInfoVO;
        }).collect(Collectors.toList());
        dmFocCustomCombVOList.addAll(customCombVOList);
    }

    private void addCombinationSubVoList(CommonViewVO commonViewVO, List<DmFocViewInfoVO> dmFocCustomCombVOList) {
        // 根据groupLevel计算子项level
        String nextGroupLevel;
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
            nextGroupLevel = FcstIndexMadeUtil.getNextGroupLevelByView(commonViewVO.getViewFlag(), commonViewVO.getGroupLevel(), commonViewVO.getGranularityType(),commonViewVO.getIndustryOrg());
        } else {
            nextGroupLevel = FcstIndexUtil.getNextGroupLevelByView(commonViewVO.getViewFlag(), commonViewVO.getGroupLevel(), commonViewVO.getGranularityType(),commonViewVO.getIndustryOrg());
        }
        commonViewVO.setNextGroupLevel(nextGroupLevel);
        List<DmFocViewInfoVO> newCombinationByGroupLevel = new ArrayList<>();
        // 查询组合的子项 (非顶层)
        List<DmFocViewInfoVO> combinationSubByGroupLevel = new ArrayList<>();
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
            combinationSubByGroupLevel =  dmFocViewInfoDao.getCombinationSubByGroupLevel(commonViewVO);
        }
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
            combinationSubByGroupLevel =  dmFocMadeViewInfoDao.getMadeCombinationSubByGroupLevel(commonViewVO);
        }
        List<Long> customSubCollect = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(combinationSubByGroupLevel)) {
            customSubCollect = combinationSubByGroupLevel.stream().map(DmFocViewInfoVO::getCustomId).distinct().collect(Collectors.toList());
        }
        if (commonViewVO.getCustomIdList().size() > customSubCollect.size()) {
            // 查询组合的子项 (次顶层)
            List<Long> parentCustomIdList = commonViewVO.getCustomIdList();
            parentCustomIdList.removeAll(customSubCollect);
            commonViewVO.setParentCustomIdList(parentCustomIdList);
            List<DmFocViewInfoVO> combinationByGroupLevel = new ArrayList<>();
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
                combinationByGroupLevel = dmFocViewInfoDao.getCombinationByGroupLevel(commonViewVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
                combinationByGroupLevel = dmFocMadeViewInfoDao.getMadeCombinationByGroupLevel(commonViewVO);
            }
            newCombinationByGroupLevel.addAll(combinationByGroupLevel);
        }
        newCombinationByGroupLevel.addAll(combinationSubByGroupLevel);
        dmFocCustomCombVOList.addAll(newCombinationByGroupLevel);
    }

    public void setInitSearchViewVO(CommonViewVO commonViewVO) {
        // 计算最近的三年
        List<String> threeYears = getThreeYears(commonViewVO.getCostType(),commonViewVO.getIndustryOrg());
        // 获取当前年份
        if (CollectionUtils.isNotEmpty(threeYears)) {
            int countYear  = threeYears.size()-1;
            commonViewVO.setPeriodYear(threeYears.get(countYear));
        }
        // 获取最新的top品version_id
        commonViewVO.setVersionId(dmFocVersionDao.findAnnualVersion(commonViewVO.getTablePreFix()).getVersionId());
        if (!IndustryConst.PAGE_FLAG.CONFIG.getValue().equals(commonViewVO.getPageFlag())) {
            // 获取最新的规格品version_id
            commonViewVO.setMonthVersionId(commonService.getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix()));
        }
        // 获取数据权限
        DataPermissionsVO currentRoleDataPermission = commonService.getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        commonViewVO.setLv0DimensionSet(currentRoleDataPermission.getLv0DimensionSet());
        commonViewVO.setLv1DimensionSet(currentRoleDataPermission.getLv1DimensionSet());
        commonViewVO.setLv2DimensionSet(currentRoleDataPermission.getLv2DimensionSet());
        Boolean reverseFlag = IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(commonViewVO.getGranularityType())
                && (IndustryIndexEnum.VIEW_FLAG_U.VIEW5.getValue().equals(commonViewVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_U.VIEW6.getValue().equals(commonViewVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_U.VIEW7.getValue().equals(commonViewVO.getViewFlag()));
        commonViewVO.setReverseFlag(reverseFlag);
    }

    @Override
    @JalorOperation(code = "reverseViewInfoList", desc = "查询反向视角不同层级下拉框")
    public ResultDataVO reverseViewInfoList(CommonViewVO commonViewVO) throws ApplicationException {
        List<DmFocViewInfoVO> dmFocViewInfoList = new ArrayList<>();
        setInitSearchViewVO(commonViewVO);
        // 根据groupLevel计算子项level
        String nextGroupLevel;
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
            nextGroupLevel = FcstIndexMadeUtil.getNextGroupLevelByView(commonViewVO.getViewFlag(),
                    commonViewVO.getGroupLevel(), commonViewVO.getGranularityType(), commonViewVO.getIndustryOrg());
        } else {
            nextGroupLevel = FcstIndexUtil.getNextGroupLevelByView(commonViewVO.getViewFlag(),
                    commonViewVO.getGroupLevel(), commonViewVO.getGranularityType(), commonViewVO.getIndustryOrg());
        }
        commonViewVO.setNextGroupLevel(nextGroupLevel);
        List<ViewInfoVO> lv2ProdDimensionList = dataDimensionService.getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        if (IndustryConst.PAGE_FLAG.ANNUAL.getValue().equals(commonViewVO.getPageFlag())) {
            dmFocViewInfoList = getReverseAnnualAndRelationPage(commonViewVO,lv2ProdDimensionList);
        } else {
            dmFocViewInfoList = getReverseUniversalMonthPage(commonViewVO,lv2ProdDimensionList);
        }
        // 按照特定顺序排列lv1
        List<DmFocViewInfoVO> newAnnualAmpVOList = sortLv1ByCode(dmFocViewInfoList);
        if (CollectionUtils.isNotEmpty(newAnnualAmpVOList)) {
            return ResultDataVO.success(newAnnualAmpVOList);
        }
        return ResultDataVO.success(dmFocViewInfoList);
    }

    @Override
    @JalorOperation(code = "viewFlagInfoList", desc = "查询视角下拉框")
    public ResultDataVO viewFlagInfoList(CommonViewVO commonViewVO) throws ApplicationException {
        List<ViewInfoVO> allProdDimensionList = dataDimensionService.getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        commonService.getCommonViewVO(allProdDimensionList,commonViewVO);
        Long versionId = commonService.getVersionId(IndustryIndexEnum.DataType.CATE.getValue(),commonViewVO.getTablePreFix());
        commonViewVO.setVersionId(versionId);
        List<DmFocViewInfoVO> viewFlagList = new ArrayList<>();
        IndustryIndexEnum.GRANULARITY_TYPE granuleEnum = IndustryIndexEnum.getGranularityType(commonViewVO.getGranularityType());
        switch (granuleEnum){
            case UNIVERSAL:
                viewFlagList = getDmFocViewInfoList(commonViewVO);
                // 判断是否有视角0的权限
                if (CollectionUtils.isEmpty(commonViewVO.getLv0DimensionSet()) && CollectionUtils.isEmpty(commonViewVO.getLv1DimensionSet())
                        && CollectionUtils.isEmpty(commonViewVO.getLv2DimensionSet())) {
                    viewFlagValueWihtLookup(commonViewVO, viewFlagList);
                    return ResultDataVO.success(viewFlagList);
                } else {
                    viewFlagList.removeIf(model -> model.getViewFlag().equals("0"));
                }
                setViewFlagListWithOne(commonViewVO, allProdDimensionList, viewFlagList,commonViewVO.getGranularityType());
                break;
            case PROFITS:
                viewFlagList = dmFocViewInfoDao.viewFlagInfoList(commonViewVO);
                // 判断是否有视角0的权限
                if (CollectionUtils.isEmpty(commonViewVO.getLv0DimensionSet()) && CollectionUtils.isEmpty(commonViewVO.getLv1DimensionSet())
                        && CollectionUtils.isEmpty(commonViewVO.getLv2DimensionSet())) {
                    viewFlagValueWihtLookup(commonViewVO, viewFlagList);
                    return ResultDataVO.success(viewFlagList);
                } else{
                    viewFlagList.removeIf(model -> model.getViewFlag().equals("0"));
                }
                setViewFlagListWithOne(commonViewVO, allProdDimensionList, viewFlagList,commonViewVO.getGranularityType());
                break;
            case DIMENSION:
                viewFlagList = dmFocViewInfoDao.viewFlagInfoList(commonViewVO);
                setViewFlagListWithOne(commonViewVO, allProdDimensionList, viewFlagList,commonViewVO.getGranularityType());
                break;
            default:
                break;
        }
        viewFlagValueWihtLookup(commonViewVO, viewFlagList);
        return ResultDataVO.success(viewFlagList);
    }

    @Override
    @JalorOperation(code = "allViewInfo", desc = "所有视角列表")
    public ResultDataVO allViewInfo(String industryOrg) throws ApplicationException {
        List<LookupItemVO> allViewInfoLsit = new ArrayList<>();
        industryCacheHandler.initLookupValue();
        // 获取成本类型
        List<LookupItemVO> costTypeLsit = lookupItemQueryService.findItemListByClassify(Constant.StrEnum.LOOK_COST_TYPE.getValue());
        if (CollectionUtils.isEmpty(costTypeLsit)) {
            return ResultDataVO.success(allViewInfoLsit);
        }
        for (LookupItemVO lookupItemVO : costTypeLsit) {
            if (lookupItemVO.getItemCode().equals(IndustryIndexEnum.COST_TYPE.T.getValue())) {
                List<LookupItemVO>  totalViewInfoList = IndustryGlobalParameterUtil.findViewListByItemCode(Constant.StrEnum.ALL_LOOKUP_UNIVERSAL_VIEW.getValue());
                allViewInfoLsit.addAll(totalViewInfoList);
                List<LookupItemVO>  totalProfitViewInfoList = IndustryGlobalParameterUtil.findViewListByItemCode(Constant.StrEnum.ALL_LOOKUP_PROFITS_VIEW.getValue());
                allViewInfoLsit.addAll(totalProfitViewInfoList);
                List<LookupItemVO>  totalDimensionViewInfoList = IndustryGlobalParameterUtil.findViewListByItemCode(Constant.StrEnum.ALL_LOOKUP_DIMENSION_VIEW.getValue());
                allViewInfoLsit.addAll(totalDimensionViewInfoList);
            }
            if (lookupItemVO.getItemCode().equals(IndustryIndexEnum.COST_TYPE.P.getValue())) {
                List<LookupItemVO>  pViewInfoList = IndustryGlobalParameterUtil.findViewListByItemCode(Constant.StrEnum.PURCHASE_LOOKUP_UNIVERSAL_VIEW.getValue());
                allViewInfoLsit.addAll(pViewInfoList);
                List<LookupItemVO>  pProfitViewInfoList = IndustryGlobalParameterUtil.findViewListByItemCode(Constant.StrEnum.PURCHASE_LOOKUP_PROFITS_VIEW.getValue());
                allViewInfoLsit.addAll(pProfitViewInfoList);
                List<LookupItemVO>  pDimensionViewInfoList = IndustryGlobalParameterUtil.findViewListByItemCode(Constant.StrEnum.PURCHASE_LOOKUP_DIMENSION_VIEW.getValue());
                allViewInfoLsit.addAll(pDimensionViewInfoList);
            }
            if (lookupItemVO.getItemCode().equals(IndustryIndexEnum.COST_TYPE.M.getValue())) {
                List<LookupItemVO>  mViewInfoList = IndustryGlobalParameterUtil.findViewListByItemCode(Constant.StrEnum.MANUFACTURE_LOOKUP_UNIVERSAL_VIEW.getValue());
                allViewInfoLsit.addAll(mViewInfoList);
                List<LookupItemVO>  mProfitViewInfoList = IndustryGlobalParameterUtil.findViewListByItemCode(Constant.StrEnum.MANUFACTURE_LOOKUP_PROFITS_VIEW.getValue());
                allViewInfoLsit.addAll(mProfitViewInfoList);
                List<LookupItemVO>  mDimensionViewInfoList = IndustryGlobalParameterUtil.findViewListByItemCode(Constant.StrEnum.MANUFACTURE_LOOKUP_DIMENSION_VIEW.getValue());
                allViewInfoLsit.addAll(mDimensionViewInfoList);
            }
        }
        setIasViewFlag(industryOrg, allViewInfoLsit);
        return ResultDataVO.success(allViewInfoLsit);
    }

    private void setIasViewFlag(String industryOrg, List<LookupItemVO> allViewInfoLsit) {
        if (IndustryConst.INDUSTRY_ORG.IAS.getValue().equals(industryOrg)) {
            for (LookupItemVO lookupItemVO : allViewInfoLsit) {
                if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(lookupItemVO.getItemAttr1()) && IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(lookupItemVO.getItemAttr2())) {
                    if (IndustryIndexEnum.VIEW_FLAG_U.VIEW5.getValue().equals(lookupItemVO.getItemCode())) {
                        replaceViewFlagName(lookupItemVO, "⑤", "⑥");
                    }
                    if (IndustryIndexEnum.VIEW_FLAG_U.VIEW6.getValue().equals(lookupItemVO.getItemCode())) {
                        replaceViewFlagName(lookupItemVO, "⑥", "⑦");
                    }
                    if (IndustryIndexEnum.VIEW_FLAG_U.VIEW7.getValue().equals(lookupItemVO.getItemCode())) {
                        replaceViewFlagName(lookupItemVO, "⑦", "⑧");
                    }
                }
                if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(lookupItemVO.getItemAttr1()) && IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(lookupItemVO.getItemAttr2())) {
                    if (IndustryIndexEnum.VIEW_FLAG_U.VIEW6.getValue().equals(lookupItemVO.getItemCode())) {
                        replaceViewFlagName(lookupItemVO, "⑥", "⑦");
                    }
                }
                if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(lookupItemVO.getItemAttr2())) {
                    if (IndustryIndexEnum.VIEW_FLAG_D.VIEW13.getValue().equals(lookupItemVO.getItemCode())) {
                        lookupItemVO.setItemName(lookupItemVO.getItemName().replace("COA","LV3.5").replace("$",ModuleEnum.IAS.getCnName()));
                        lookupItemVO.setItemDesc(lookupItemVO.getItemDesc().replace("coa","lv4"));
                    }
                }
            }
        }
    }

    private void replaceViewFlagName(LookupItemVO lookupItemVO, String s, String s2) {
        if (!lookupItemVO.getItemName().contains("LV3")) {
            lookupItemVO.setItemName(StrUtil.replace(lookupItemVO.getItemName() + "-LV3", s, s2).replace("$", ModuleEnum.IAS.getCnName()));
            lookupItemVO.setItemDesc(lookupItemVO.getItemDesc() + ",lv3");
        } else {
            lookupItemVO.setItemName(StrUtil.replace(lookupItemVO.getItemName(), s, s2).replace("$", ModuleEnum.IAS.getCnName()));
        }
    }

    private List<DmFocViewInfoVO> getDmFocViewInfoList(CommonViewVO commonViewVO) {
        List<DmFocViewInfoVO> viewFlagList = dmFocViewInfoDao.viewFlagInfoList(commonViewVO);
        // 采购成本配置管理需要移除视角4和5
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
            if ("CONFIG".equals(commonViewVO.getPageFlag())) {
                viewFlagList.removeIf(model -> model.getViewFlag().equals("4"));
                viewFlagList.removeIf(model -> model.getViewFlag().equals("5"));
            }
        }
        return viewFlagList;
    }

    private void viewFlagValueWihtLookup(CommonViewVO commonViewVO, List<DmFocViewInfoVO> viewFlagList) throws ApplicationException {
        industryCacheHandler.initLookupValue();
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
            setPurchaseViewFlagValueWithLookUp(viewFlagList,commonViewVO);
        }
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
            setManufactureViewFlagValueWithLookUp(viewFlagList,commonViewVO);
        }
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(commonViewVO.getCostType())) {
            setAllViewFlagValueWithLookUp(viewFlagList,commonViewVO);
        }
    }

    @Override
    @JalorOperation(code = "queryDataRefreshStatus", desc = "轮询任务状态")
    public ResultDataVO queryDataRefreshStatus(DmFocDataRefreshStatus dataRefreshStatus) throws CommonApplicationException {
        if (null == dataRefreshStatus.getTaskId() || dataRefreshStatus.getTaskId() <= 0L) {
            throw new CommonApplicationException("该任务ID有误!");
        }
        DmFocDataRefreshStatus dmFocDataRefreshStatus =
                dataRefreshStatusDao.findDmFocDataRefreshStatusById(dataRefreshStatus);
        if ("TASK_FAIL".equals(dataRefreshStatus.getStatus())) {
            throw new CommonApplicationException("任务失败");
        }
        return ResultDataVO.success(dmFocDataRefreshStatus);
    }

    @Override
    @JalorOperation(code = "currentDataRefreshStatus", desc = "当前用户当前角色的taskId查询")
    public ResultDataVO currentDataRefreshStatus(DmFocDataRefreshStatus dataRefreshStatus) {
        Long userId = UserInfoUtils.getUserId();
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        Integer roleId = currentRole.getRoleId();
        dataRefreshStatus.setUserId(userId);
        dataRefreshStatus.setRoleId(roleId);
        DmFocDataRefreshStatus dmFocDataRefreshStatus =
                dataRefreshStatusDao.findDmFocDataRefreshStatus(dataRefreshStatus);
        return ResultDataVO.success(dmFocDataRefreshStatus);
    }

    private void setViewFlagListWithOne(CommonViewVO commonViewVO, List<ViewInfoVO> allProdDimensionList, List<DmFocViewInfoVO> viewFlagList,String granularityType) {
        // 判断是否有视角1的权限
        Boolean hasLv1Flag = false;
        if (CollectionUtils.isNotEmpty(commonViewVO.getLv1DimensionSet())) {
            hasLv1Flag = setLv1Flag(commonViewVO, allProdDimensionList, hasLv1Flag);
            if (!hasLv1Flag && (IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(granularityType) || IndustryIndexEnum.GRANULARITY_TYPE.PROFITS.getValue().equals(granularityType))) {
                viewFlagList.removeIf(model -> model.getViewFlag().equals("1"));
            }
            if (!hasLv1Flag && IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(granularityType)) {
                viewFlagList.removeIf(model -> model.getViewFlag().equals("0"));
                viewFlagList.removeIf(model -> model.getViewFlag().equals("1"));
                viewFlagList.removeIf(model -> model.getViewFlag().equals("2"));
                viewFlagList.removeIf(model -> model.getViewFlag().equals("9"));
            }
        }

    }

    private Boolean setLv1Flag(CommonViewVO commonViewVO, List<ViewInfoVO> allProdDimensionList, Boolean hasLv1Flag) {
        for (String lv1code : commonViewVO.getLv1DimensionSet()) {
            Set<String> lv2ProdCodeSet = allProdDimensionList.stream()
                    .filter(item -> lv1code.equals(item.getLv1ProdRndTeamCode()))
                    .map(ViewInfoVO::getLv2ProdRndTeamCode).collect(Collectors.toSet());
            if (commonViewVO.getLv1DimensionSet().contains("NO_PERMISSION")) {
                hasLv1Flag = false;
            } else {
                // lv2DimensionSet为空集合，代表拥有所有LV1权限
                if (commonViewVO.getLv2DimensionSet().size() == 0) {
                    hasLv1Flag = true;
                    break;
                }
                if (lv2ProdCodeSet.size() !=0) {
                    if (commonViewVO.getLv2DimensionSet().containsAll(lv2ProdCodeSet)) {
                        hasLv1Flag = true;
                        break;
                    } else {
                        hasLv1Flag = false;
                    }
                }
            }
        }
        return hasLv1Flag;
    }

    private void setNextGroupLevel(CommonViewVO commonViewVO) {
        String nextGroupLevel;
        if (StringUtils.isNotBlank(commonViewVO.getGroupLevel())) {
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
                nextGroupLevel = FcstIndexMadeUtil.getNextGroupLevelByView(commonViewVO.getViewFlag(), commonViewVO.getGroupLevel(), commonViewVO.getGranularityType(),commonViewVO.getIndustryOrg());
            } else {
                nextGroupLevel = FcstIndexUtil.getNextGroupLevelByView(commonViewVO.getViewFlag(), commonViewVO.getGroupLevel(), commonViewVO.getGranularityType(),commonViewVO.getIndustryOrg());
            }
            commonViewVO.setNextGroupLevel(nextGroupLevel);
            return;
        }
    }

    /**
     *
     * @param viewFlagList
     * @throws ApplicationException
     */
    public void setPurchaseViewFlagValueWithLookUp(List<DmFocViewInfoVO> viewFlagList,CommonViewVO commonViewVO) throws ApplicationException {
        // 查询Lookup中配置的通用视角
        setUniversalLookUp(viewFlagList,commonViewVO.getIndustryOrg(), IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL, Constant.StrEnum.PURCHASE_LOOKUP_UNIVERSAL_VIEW);
        // 查询Lookup中配置的盈利视角
        setUniversalLookUp(viewFlagList,commonViewVO.getIndustryOrg(),IndustryIndexEnum.GRANULARITY_TYPE.PROFITS, Constant.StrEnum.PURCHASE_LOOKUP_PROFITS_VIEW);
        // 查询Lookup中配置的量纲视角
        setUniversalLookUp(viewFlagList,commonViewVO.getIndustryOrg(),IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION, Constant.StrEnum.PURCHASE_LOOKUP_DIMENSION_VIEW);
    }

    /**
     *
     * @param viewFlagList
     * @throws ApplicationException
     */
    public void setManufactureViewFlagValueWithLookUp(List<DmFocViewInfoVO> viewFlagList,CommonViewVO commonViewVO) throws ApplicationException {
        // 查询Lookup中配置的通用视角
        setUniversalLookUp(viewFlagList, commonViewVO.getIndustryOrg(),IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL, Constant.StrEnum.MANUFACTURE_LOOKUP_UNIVERSAL_VIEW);
        // 查询Lookup中配置的盈利视角
        setUniversalLookUp(viewFlagList, commonViewVO.getIndustryOrg(), IndustryIndexEnum.GRANULARITY_TYPE.PROFITS, Constant.StrEnum.MANUFACTURE_LOOKUP_PROFITS_VIEW);
        // 查询Lookup中配置的量纲视角
        setUniversalLookUp(viewFlagList, commonViewVO.getIndustryOrg(),IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION, Constant.StrEnum.MANUFACTURE_LOOKUP_DIMENSION_VIEW);
    }

    /**
     *
     * @param viewFlagList
     * @throws ApplicationException
     */
    public void setAllViewFlagValueWithLookUp(List<DmFocViewInfoVO> viewFlagList,CommonViewVO commonViewVO) throws ApplicationException {
        // 查询Lookup中配置的通用视角
        setUniversalLookUp(viewFlagList,commonViewVO.getIndustryOrg(),IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL, Constant.StrEnum.ALL_LOOKUP_UNIVERSAL_VIEW);
        // 查询Lookup中配置的盈利视角
        setUniversalLookUp(viewFlagList,commonViewVO.getIndustryOrg(), IndustryIndexEnum.GRANULARITY_TYPE.PROFITS, Constant.StrEnum.ALL_LOOKUP_PROFITS_VIEW);
        // 查询Lookup中配置的量纲视角
        setUniversalLookUp(viewFlagList,commonViewVO.getIndustryOrg(), IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION, Constant.StrEnum.ALL_LOOKUP_DIMENSION_VIEW);
    }

    private void setUniversalLookUp(List<DmFocViewInfoVO> viewFlagList,String industryOrg,IndustryIndexEnum.GRANULARITY_TYPE granularityType, Constant.StrEnum lookupUniversalView) throws ApplicationException {
        if (CollectionUtils.isNotEmpty(viewFlagList)) {
            if (granularityType.getValue().equals(viewFlagList.get(0).getGranularityType())) {
                List<LookupItemVO> lookUpItemList = IndustryGlobalParameterUtil.findViewListByItemCode(lookupUniversalView.getValue());
                for (DmFocViewInfoVO dmFocViewInfoVO : viewFlagList) {
                    for (LookupItemVO lookupItemVO : lookUpItemList) {
                        if (dmFocViewInfoVO.getViewFlag().equals(lookupItemVO.getItemCode())) {
                            // 视角名称区分ICT和数字能源和IAS
                            IndustryConst.INDUSTRY_ORG industryOrgName = IndustryConst.getIndustryOrgName(industryOrg);
                            switch (industryOrgName) {
                                case ICT:
                                    dmFocViewInfoVO.setViewFlagValue(lookupItemVO.getItemName().replace("$", ModuleEnum.ICT.getCnName()));
                                    break;
                                case ENERGY:
                                    dmFocViewInfoVO.setViewFlagValue(lookupItemVO.getItemName().replace("$",ModuleEnum.ENERGY.getCnName()));
                                    break;
                                case IAS:
                                    dmFocViewInfoVO.setViewFlagValue(lookupItemVO.getItemName().replace("$",ModuleEnum.IAS.getCnName()));
                                    commonService.iasSpecialViewFlagValueSet(dmFocViewInfoVO, lookupItemVO);
                                    break;
                                default:
                                    break;
                            }
                            dmFocViewInfoVO.setViewFlagOrder(lookupItemVO.getItemIndex());
                        }
                    }
                }
            }
        }
    }

    /**
     * 最近三年
     *
     * @return 最近三年list
     */
    public List<String> getThreeYears(String costType,String industryOrg) {
        ResultDataVO periodYearList = getAnnualPeriodYear(costType,industryOrg);
        List<String> threeYears  = (List<String>) periodYearList.getData();
        if (CollectionUtils.isNotEmpty(threeYears)) {
            Collections.sort(threeYears);
        }
        return threeYears;
    }

    /**
     * 获取通用颗粒度月度分析层级下拉框
     *
     * @param commonViewVO 参数
     * @return 结果
     */
    private List<DmFocViewInfoVO> getMonthPage(CommonViewVO commonViewVO) {
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        if (StringUtils.isNotBlank(commonViewVO.getKeyWord())) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
                if ("CATE_TYPE".equals(commonViewVO.getConfigDataType())) {
                    dmFocMonthAmpVOList = dmFocViewInfoDao.viewInfoKeyWordForTopCate(commonViewVO);
                } else {
                    dmFocMonthAmpVOList = dmFocViewInfoDao.viewInfoKeyWordForMonth(commonViewVO);
                }
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
                if ("MADE_TYPE".equals(commonViewVO.getConfigDataType())) {
                    dmFocMonthAmpVOList = dmFocMadeViewInfoDao.madeObjectViewInfoKeyWord(commonViewVO);
                } else {
                    dmFocMonthAmpVOList = dmFocMadeViewInfoDao.madeViewInfoKeyWordForMonth(commonViewVO);
                }
            }
        } else {
            dmFocMonthAmpVOList = getUniversalDmFocViewInfoVOList(commonViewVO, dmFocMonthAmpVOList);
        }
        return dmFocMonthAmpVOList;
    }

    private List<DmFocViewInfoVO> getUniversalDmFocViewInfoVOList(CommonViewVO commonViewVO, List<DmFocViewInfoVO> dmFocMonthAmpVOList) {
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
            if ("CATE_TYPE".equals(commonViewVO.getConfigDataType())) {
                dmFocMonthAmpVOList = dmFocViewInfoDao.viewInfoListForTopCate(commonViewVO);
            } else {
                dmFocMonthAmpVOList = dmFocViewInfoDao.viewInfoListForMonth(commonViewVO);
            }
        }
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
            if ("MADE_TYPE".equals(commonViewVO.getConfigDataType())) {
                dmFocMonthAmpVOList = dmFocMadeViewInfoDao.madeObjectViewInfoList(commonViewVO);
            } else {
                dmFocMonthAmpVOList = dmFocMadeViewInfoDao.madeViewInfoListForMonth(commonViewVO);
            }
        }
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(commonViewVO.getCostType())) {
            dmFocMonthAmpVOList = dmFocTotalViewInfoDao.totalViewInfoList(commonViewVO);
        }
        // 重量级团队权限控制
        if (!IndustryConst.PAGE_FLAG.CONFIG.getValue().equals(commonViewVO.getPageFlag())) {
            handlePermissionTeamLevel(dmFocMonthAmpVOList, commonViewVO);
        }
        return dmFocMonthAmpVOList;
    }

    /**
     * 获取盈利颗粒度月度分析层级下拉框
     *
     * @param commonViewVO 参数
     * @return 结果
     */
    private List<DmFocViewInfoVO> getProfitMonthPage(CommonViewVO commonViewVO) {
        List<DmFocViewInfoVO> dmFocProfitMonthVOList = new ArrayList<>();
        if (StringUtils.isNotBlank(commonViewVO.getKeyWord())) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
                if ("CATE_TYPE".equals(commonViewVO.getConfigDataType())) {
                    dmFocProfitMonthVOList = dmFocPftViewInfoDao.viewInfoKeyWordForTopCate(commonViewVO);
                } else {
                    dmFocProfitMonthVOList = dmFocPftViewInfoDao.viewInfoKeyWordForMonth(commonViewVO);
                }
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
                if ("MADE_TYPE".equals(commonViewVO.getConfigDataType())) {
                    dmFocProfitMonthVOList = dmFocMadePftViewInfoDao.madeObjectViewInfoKeyWord(commonViewVO);
                } else {
                    dmFocProfitMonthVOList = dmFocMadePftViewInfoDao.madeViewInfoKeyWordForMonth(commonViewVO);
                }
            }
        } else {
            dmFocProfitMonthVOList = getPftDmFocViewInfoList(commonViewVO, dmFocProfitMonthVOList);
        }
        return dmFocProfitMonthVOList;
    }

    private List<DmFocViewInfoVO> getPftDmFocViewInfoList(CommonViewVO commonViewVO, List<DmFocViewInfoVO> dmFocProfitMonthVOList) {
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
            if ("CATE_TYPE".equals(commonViewVO.getConfigDataType())) {
                dmFocProfitMonthVOList = dmFocPftViewInfoDao.viewInfoListForTopCate(commonViewVO);
            } else {
                dmFocProfitMonthVOList = dmFocPftViewInfoDao.viewInfoListForMonth(commonViewVO);
            }
        }
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
            if ("MADE_TYPE".equals(commonViewVO.getConfigDataType())) {
                dmFocProfitMonthVOList = dmFocMadePftViewInfoDao.madeObjectViewInfoList(commonViewVO);
            } else {
                dmFocProfitMonthVOList = dmFocMadePftViewInfoDao.madeViewInfoListForMonth(commonViewVO);
            }
        }
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(commonViewVO.getCostType())) {
            dmFocProfitMonthVOList = dmFocTotalPftViewInfoDao.totalViewInfoList(commonViewVO);
        }
        // 重量级团队权限控制
        if (!IndustryConst.PAGE_FLAG.CONFIG.getValue().equals(commonViewVO.getPageFlag())) {
            hasPermissionTeamLevelPfi(dmFocProfitMonthVOList, commonViewVO);
        }
        return dmFocProfitMonthVOList;
    }


    /**
     * 获取量纲颗粒度月度分析层级下拉框
     *
     * @param commonViewVO 参数
     * @return 结果
     */
    private List<DmFocViewInfoVO> getDmsMonthPage(CommonViewVO commonViewVO) {
        List<DmFocViewInfoVO> dmFocDmsMonthVOList = new ArrayList<>();
        if (StringUtils.isNotBlank(commonViewVO.getKeyWord())) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
                if ("CATE_TYPE".equals(commonViewVO.getConfigDataType())) {
                    dmFocDmsMonthVOList = dmFocDmsViewInfoDao.viewInfoKeyWordForTopCate(commonViewVO);
                } else {
                    dmFocDmsMonthVOList = dmFocDmsViewInfoDao.viewInfoKeyWordForMonth(commonViewVO);
                }
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
                if ("MADE_TYPE".equals(commonViewVO.getConfigDataType())) {
                    dmFocDmsMonthVOList = dmFocMadeDmsViewInfoDao.madeObjectViewInfoKeyWord(commonViewVO);
                } else {
                    dmFocDmsMonthVOList = dmFocMadeDmsViewInfoDao.madeViewInfoKeyWordForMonth(commonViewVO);
                }
            }
        } else {
            dmFocDmsMonthVOList = getDmsDmFocViewInfoVOList(commonViewVO, dmFocDmsMonthVOList);
        }
        return dmFocDmsMonthVOList;
    }

    private List<DmFocViewInfoVO> getDmsDmFocViewInfoVOList(CommonViewVO commonViewVO, List<DmFocViewInfoVO> dmFocDmsMonthVOList) {
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
            if ("CATE_TYPE".equals(commonViewVO.getConfigDataType())) {
                dmFocDmsMonthVOList = dmFocDmsViewInfoDao.viewInfoListForTopCate(commonViewVO);
            } else {
                dmFocDmsMonthVOList = dmFocDmsViewInfoDao.viewInfoListForMonth(commonViewVO);
            }
        }
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
            if ("MADE_TYPE".equals(commonViewVO.getConfigDataType())) {
                dmFocDmsMonthVOList = dmFocMadeDmsViewInfoDao.madeObjectViewInfoList(commonViewVO);
            } else {
                dmFocDmsMonthVOList = dmFocMadeDmsViewInfoDao.madeViewInfoListForMonth(commonViewVO);
            }
        }
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(commonViewVO.getCostType())) {
            dmFocDmsMonthVOList = dmFocTotalDmsViewInfoDao.totalViewInfoList(commonViewVO);
        }
        // 重量级团队权限控制
        if (!IndustryConst.PAGE_FLAG.CONFIG.getValue().equals(commonViewVO.getPageFlag())) {
            handlePermissionTeamLevel(dmFocDmsMonthVOList, commonViewVO);
        }
        return dmFocDmsMonthVOList;
    }

    /**
     * 获取通用颗粒度年度分析或配置管理 层级下拉框
     *
     * @param commonViewVO 参数
     * @return 结果
     */
    private List<DmFocViewInfoVO> getAnnualAndRelationPage(CommonViewVO commonViewVO) {
        List<DmFocViewInfoVO> dmFocAnnualAmpVOList = new ArrayList<>();
        if (StringUtils.isNotBlank(commonViewVO.getKeyWord())) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
                dmFocAnnualAmpVOList = dmFocViewInfoDao.viewInfoKeyWordList(commonViewVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
                dmFocAnnualAmpVOList = dmFocMadeViewInfoDao.madeViewInfoKeyWordList(commonViewVO);
            }
        } else {
            setNextGroupLevel(commonViewVO);
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
                dmFocAnnualAmpVOList = dmFocViewInfoDao.viewInfoList(commonViewVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
                dmFocAnnualAmpVOList = dmFocMadeViewInfoDao.madeViewInfoList(commonViewVO);
            }
            if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(commonViewVO.getCostType())) {
                dmFocAnnualAmpVOList = dmFocTotalViewInfoDao.totalViewInfoList(commonViewVO);
            }
            // 重量级团队权限控制
            if (!IndustryConst.PAGE_FLAG.CONFIG.getValue().equals(commonViewVO.getPageFlag())) {
                handlePermissionTeamLevel(dmFocAnnualAmpVOList,commonViewVO);
            }
        }
        return dmFocAnnualAmpVOList;
    }

    /**
     * 获取通用颗粒度反向视角年度分析或配置管理 层级下拉框
     *
     * @param commonViewVO 参数
     * @return 结果
     */
    private List<DmFocViewInfoVO> getReverseAnnualAndRelationPage(CommonViewVO commonViewVO,List<ViewInfoVO> lv2ProdDimensionList) {
        getLv1andLv2DimensionSet(commonViewVO);
        List<DmFocViewInfoVO> dmFocAnnualAmpVOList = new ArrayList<>();
        setAnnualRevViewFlag(commonViewVO);
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
            dmFocAnnualAmpVOList = dmFocViewInfoDao.revViewInfoList(commonViewVO);
        }
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
            dmFocAnnualAmpVOList =  dmFocMadeViewInfoDao.madeRevViewInfoList(commonViewVO);
        }
        // 反向视角重量级团队权限控制
        if (!IndustryConst.PAGE_FLAG.CONFIG.getValue().equals(commonViewVO.getPageFlag())) {
            reverseViewPermission(dmFocAnnualAmpVOList, commonViewVO, lv2ProdDimensionList);
        }
        return dmFocAnnualAmpVOList;
    }

    private void setAnnualRevViewFlag(CommonViewVO commonViewVO) {
        // 依据组织结构不同设置，revViewFlag值，ICT和数字能源设置为3，IAS设置为7
        if (IndustryConst.INDUSTRY_ORG.IAS.getValue().equals(commonViewVO.getIndustryOrg())) {
            commonViewVO.setRevViewFlag(IndustryIndexEnum.VIEW_FLAG_U.VIEW8.getValue());
        } else {
            commonViewVO.setRevViewFlag(IndustryIndexEnum.VIEW_FLAG_U.VIEW4.getValue());
        }
    }


    /**
     * 获取通用粒度反向视角月度分析层级下拉框
     *
     * @param commonViewVO 参数
     * @return 结果
     */
    private List<DmFocViewInfoVO> getReverseUniversalMonthPage(CommonViewVO commonViewVO,List<ViewInfoVO> lv3ProdDimensionList) {
        getLv1andLv2DimensionSet(commonViewVO);
        List<DmFocViewInfoVO> dmFocUniversalMonthVOList = new ArrayList<>();
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
            if ("CATE_TYPE".equals(commonViewVO.getConfigDataType())) {
                dmFocUniversalMonthVOList =  dmFocViewInfoDao.revViewInfoListForTopCate(commonViewVO);
            } else {
                dmFocUniversalMonthVOList =  dmFocViewInfoDao.revViewInfoListForMonth(commonViewVO);
            }
        }
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
            if ("MADE_TYPE".equals(commonViewVO.getConfigDataType())) {
                dmFocUniversalMonthVOList =  dmFocMadeViewInfoDao.madeObjectRevViewInfoList(commonViewVO);
            } else {
                dmFocUniversalMonthVOList =  dmFocMadeViewInfoDao.madeRevViewInfoListForMonth(commonViewVO);
            }

        }
        // 反向视角重量级团队权限控制
        if (!IndustryConst.PAGE_FLAG.CONFIG.getValue().equals(commonViewVO.getPageFlag())) {
            reverseViewPermission(dmFocUniversalMonthVOList, commonViewVO, lv3ProdDimensionList);
        }
        return dmFocUniversalMonthVOList;
    }

    public void getLv1andLv2DimensionSet(CommonViewVO commonViewVO) {
        // 反向找对应的LV1
        if ("2".equals(commonViewVO.getMaxViewFlag()) && commonViewVO.getLv1DimensionSet().contains("NO_PERMISSION")) {
            CommonViewVO lv1ViewVO = new CommonViewVO();
            lv1ViewVO.setGroupLevel(GroupLevelEnumD.LV1.getValue());
            lv1ViewVO.setTeamLevel(GroupLevelEnumD.LV1.getValue());
            lv1ViewVO.setCostType(commonViewVO.getCostType());
            lv1ViewVO.setViewFlag("2");
            lv1ViewVO.setOverseaFlag(commonViewVO.getOverseaFlag());
            lv1ViewVO.setLv0ProdListCode(commonViewVO.getLv0ProdListCode());
            lv1ViewVO.setCaliberFlag(commonViewVO.getCaliberFlag());
            lv1ViewVO.setPageFlag(commonViewVO.getPageFlag());
            lv1ViewVO.setIsMultipleSelect(commonViewVO.getIsMultipleSelect());
            lv1ViewVO.setMonthVersionId(commonViewVO.getMonthVersionId());
            lv1ViewVO.setPeriodYear(commonViewVO.getPeriodYear());
            lv1ViewVO.setVersionId(commonViewVO.getVersionId());
            lv1ViewVO.setLv0DimensionSet(commonViewVO.getLv0DimensionSet());
            lv1ViewVO.setLv1DimensionSet(commonViewVO.getLv1DimensionSet());
            lv1ViewVO.setLv2DimensionSet(commonViewVO.getLv2DimensionSet());
            lv1ViewVO.setGranularityType(commonViewVO.getGranularityType());
            lv1ViewVO.setIndustryOrg(commonViewVO.getIndustryOrg());
            List<DmFocViewInfoVO> lv1ProdVOList = new ArrayList<>();
            if (IndustryConst.PAGE_FLAG.MONTH.getValue().equals(lv1ViewVO.getPageFlag())) {
                lv1ProdVOList = getMonthPage(lv1ViewVO);
            } else {
                lv1ProdVOList = getAnnualAndRelationPage(lv1ViewVO);
            }
            if (CollectionUtils.isNotEmpty(lv1ProdVOList)) {
                Set<String> lv1ProdRndTeamCode = lv1ProdVOList.stream().map(DmFocViewInfoVO::getLv1ProdRndTeamCode).collect(Collectors.toSet());
                commonViewVO.setLv1DimensionSet(lv1ProdRndTeamCode);
            }
        }
    }

    /**
     * 获取盈利颗粒度年度分析或配置管理 层级下拉框
     *
     * @param commonViewVO 参数
     * @return 结果
     */
    private List<DmFocViewInfoVO> getProfitAnnualAndRelationPage(CommonViewVO commonViewVO) {
        List<DmFocViewInfoVO> dmFocProfitAnnualAmpVOList = new ArrayList<>() ;
        if (StringUtils.isNotBlank(commonViewVO.getKeyWord())) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
                dmFocProfitAnnualAmpVOList = dmFocPftViewInfoDao.viewInfoKeyWordList(commonViewVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
                dmFocProfitAnnualAmpVOList = dmFocMadePftViewInfoDao.madeViewInfoKeyWordList(commonViewVO);
            }
        } else {
            setNextGroupLevel(commonViewVO);
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
                dmFocProfitAnnualAmpVOList = dmFocPftViewInfoDao.viewInfoList(commonViewVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
                dmFocProfitAnnualAmpVOList = dmFocMadePftViewInfoDao.madeViewInfoList(commonViewVO);
            }
            if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(commonViewVO.getCostType())) {
                dmFocProfitAnnualAmpVOList = dmFocTotalPftViewInfoDao.totalViewInfoList(commonViewVO);
            }
            // 重量级团队权限控制
            if (!IndustryConst.PAGE_FLAG.CONFIG.getValue().equals(commonViewVO.getPageFlag())) {
                hasPermissionTeamLevelPfi(dmFocProfitAnnualAmpVOList, commonViewVO);
            }
        }
        return dmFocProfitAnnualAmpVOList;
    }


    /**
     * 获取量纲颗粒度年度分析或配置管理 层级下拉框
     *
     * @param commonViewVO 参数
     * @return 结果
     */
    private List<DmFocViewInfoVO> getDmsAnnualAndRelationPage(CommonViewVO commonViewVO) {
        List<DmFocViewInfoVO> dmFocDmsAnnualAmpVOList = new ArrayList<>();
        if (StringUtils.isNotBlank(commonViewVO.getKeyWord())) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
                dmFocDmsAnnualAmpVOList = dmFocDmsViewInfoDao.viewInfoKeyWordList(commonViewVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
                dmFocDmsAnnualAmpVOList = dmFocMadeDmsViewInfoDao.madeViewInfoKeyWordList(commonViewVO);
            }
        } else {
            setNextGroupLevel(commonViewVO);
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
                dmFocDmsAnnualAmpVOList = dmFocDmsViewInfoDao.viewInfoList(commonViewVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
                dmFocDmsAnnualAmpVOList = dmFocMadeDmsViewInfoDao.madeViewInfoList(commonViewVO);
            }
            if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(commonViewVO.getCostType())) {
                dmFocDmsAnnualAmpVOList = dmFocTotalDmsViewInfoDao.totalViewInfoList(commonViewVO);
            }
            // 重量级团队权限控制
            if (!IndustryConst.PAGE_FLAG.CONFIG.getValue().equals(commonViewVO.getPageFlag())) {
                handlePermissionTeamLevel(dmFocDmsAnnualAmpVOList, commonViewVO);
            }
        }
        return dmFocDmsAnnualAmpVOList;
    }

    /**
     * 视角1时，处理重量级团队lv1的权限
     * 视角2时，处理重量级团队lv2的权限
     * 视角3时，处理重量级团队Lv1和lv2的权限
     * @param dmFocAnnualAmpVOList
     * @param commonViewVO
     */
    public void handlePermissionTeamLevel(List<DmFocViewInfoVO>dmFocAnnualAmpVOList,CommonViewVO commonViewVO) {
        // 当有最大权限时，就不用循环校验各子项是否有权限了
        if (commonViewVO.getLv0DimensionSet().size() == 0 && commonViewVO.getLv1DimensionSet().size() == 0 && commonViewVO.getLv2DimensionSet().size() == 0) {
            return;
        }
        List<ViewInfoVO> lv2ProdDimensionList = dataDimensionService.getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        // 有视角1权限时处理重量级团队Lv1
        handleViewFlagOne(dmFocAnnualAmpVOList, commonViewVO, lv2ProdDimensionList);
        // 有视角2权限时处理重量级团队Lv1和Lv2
        handleViewFlagTwo(dmFocAnnualAmpVOList, commonViewVO, lv2ProdDimensionList);
        // 有视角3权限时处理重量级团队Lv1和Lv2
        handleViewFlagThree(dmFocAnnualAmpVOList, commonViewVO, lv2ProdDimensionList);
    }

    private void handleViewFlagThree(List<DmFocViewInfoVO> dmFocAnnualAmpVOList, CommonViewVO commonViewVO, List<ViewInfoVO> lv2ProdDimensionList) {
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(commonViewVO.getGranularityType())) {
            Boolean hasLv3View = getHasLv3View(commonViewVO);
            if (hasLv3View) {
                lv1ProdTeamCodePermission(dmFocAnnualAmpVOList, commonViewVO, lv2ProdDimensionList);
            }
        } else {
            if (IndustryIndexEnum.VIEW_FLAG_U.VIEW4.getValue().equals(commonViewVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_U.VIEW8.getValue().equals(commonViewVO.getViewFlag())) {
                lv1ProdTeamCodePermission(dmFocAnnualAmpVOList, commonViewVO, lv2ProdDimensionList);
            }
        }
    }

    private boolean handleLv1ProdTeamCodeWithThree(CommonViewVO commonViewVO, List<ViewInfoVO> lv3ProdDimensionList, DmFocViewInfoVO dmFocViewInfoVO) {
        Set<String> lv2DimensionSet = commonViewVO.getLv2DimensionSet();
        Set<String> lv1DimensionSet = commonViewVO.getLv1DimensionSet();
        if (GroupLevelEnumU.LV1.getValue().equals(dmFocViewInfoVO.getGroupLevel())) {
            Set<String> lv2ProdCodeSet = lv3ProdDimensionList.stream()
                    .filter(item ->  dmFocViewInfoVO.getGroupCode().equals(item.getLv1ProdRndTeamCode()))
                    .map(ViewInfoVO::getLv2ProdRndTeamCode).collect(Collectors.toSet());
            // lv1DimensionSet不包涵，代表没有LV1的权限
            if (lv1DimensionSet.size()!=0 && !lv1DimensionSet.contains(dmFocViewInfoVO.getGroupCode())) {
                dmFocViewInfoVO.setPermissionFlag("no");
                return true;
            }
            // lv2DimensionSet为空集合，代表拥有LV1的权限
            if (lv2DimensionSet.size() == 0) {
                dmFocViewInfoVO.setPermissionFlag("has");
                return true;
            }
            if (lv2DimensionSet.containsAll(lv2ProdCodeSet)) {
                dmFocViewInfoVO.setPermissionFlag("has");
                return true;
            } else {
                dmFocViewInfoVO.setPermissionFlag("no");
            }
        }
        return false;
    }

    private void handleViewFlagTwo(List<DmFocViewInfoVO> dmFocAnnualAmpVOList, CommonViewVO commonViewVO, List<ViewInfoVO> lv2ProdDimensionList) {
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(commonViewVO.getGranularityType())) {
            Boolean hasLv2View = IndustryIndexEnum.VIEW_FLAG_D.VIEW4.getValue().equals(commonViewVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_D.VIEW5.getValue().equals(commonViewVO.getViewFlag())
                    || IndustryIndexEnum.VIEW_FLAG_D.VIEW6.getValue().equals(commonViewVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_D.VIEW11.getValue().equals(commonViewVO.getViewFlag());
            if (hasLv2View) {
                lv1ProdTeamCodePermission(dmFocAnnualAmpVOList, commonViewVO, lv2ProdDimensionList);
            }
        } else {
            if (IndustryIndexEnum.VIEW_FLAG_U.VIEW3.getValue().equals(commonViewVO.getViewFlag())) {
                lv1ProdTeamCodePermission(dmFocAnnualAmpVOList, commonViewVO, lv2ProdDimensionList);
            }
        }
    }

    public void reverseViewPermission(List<DmFocViewInfoVO> dmFocAnnualAmpVOList, CommonViewVO commonViewVO, List<ViewInfoVO> lv3ProdDimensionList) {
        // 当有最大权限时，就不用循环校验各子项是否有权限了
        if (commonViewVO.getLv0DimensionSet().size() == 0 && commonViewVO.getLv1DimensionSet().size() == 0 && commonViewVO.getLv2DimensionSet().size() == 0) {
            return;
        }
        // 补齐LV1，permissionFlag设置上no
        // 根据视角不同处理重量级团队Lv1权限
        setRevLv1ProdCodeList(dmFocAnnualAmpVOList, commonViewVO);
        // 1:校验重量级团队lv1层级的code是否有所有重量级团队LV2和lv3的权限,2:重量级团队lv2层级的code是否有所有重量级团队LV3的权限
        if (CollectionUtils.isNotEmpty(dmFocAnnualAmpVOList)) {
            for (DmFocViewInfoVO dmFocViewInfoVO : dmFocAnnualAmpVOList) {
                // 处理重量级团队Lv1权限
                handleLv1ProdTeamCodeWithThree(commonViewVO, lv3ProdDimensionList, dmFocViewInfoVO);
            }
        }

    }

    private void setRevLv1ProdCodeList(List<DmFocViewInfoVO> dmFocAnnualAmpVOList, CommonViewVO commonViewVO) {
        if(IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
            if (IndustryIndexEnum.VIEW_FLAG_U.VIEW5.getValue().equals(commonViewVO.getViewFlag())
                    && GroupLevelEnumU.CEG.getValue().equals(commonViewVO.getPurLevel())
                    && GroupLevelEnumU.LV0.getValue().equals(commonViewVO.getTeamLevel())) {
                setLv1ProCodeListForView3(commonViewVO, dmFocAnnualAmpVOList);
            }
            if (IndustryIndexEnum.VIEW_FLAG_U.VIEW6.getValue().equals(commonViewVO.getViewFlag())
                    && GroupLevelEnumU.MODL.getValue().equals(commonViewVO.getPurLevel())
                    && GroupLevelEnumU.LV0.getValue().equals(commonViewVO.getTeamLevel())) {
                setLv1ProCodeListForView3(commonViewVO, dmFocAnnualAmpVOList);
            }
            if (IndustryIndexEnum.VIEW_FLAG_U.VIEW7.getValue().equals(commonViewVO.getViewFlag())
                    && GroupLevelEnumU.CATEGORY.getValue().equals(commonViewVO.getPurLevel())
                    && GroupLevelEnumU.LV0.getValue().equals(commonViewVO.getTeamLevel())) {
                setLv1ProCodeListForView3(commonViewVO, dmFocAnnualAmpVOList);
            }
        } else {
            if (IndustryIndexEnum.VIEW_FLAG_U_M.VIEW6.getValue().equals(commonViewVO.getViewFlag())
                    && GroupLevelEnumMadeU.MANUFACTURE_OBJECT.getValue().equals(commonViewVO.getPurLevel())
                    && GroupLevelEnumU.LV0.getValue().equals(commonViewVO.getTeamLevel())) {
                setLv1ProCodeListForView3(commonViewVO, dmFocAnnualAmpVOList);
            }
        }
    }

    private void lv1ProdTeamCodePermission(List<DmFocViewInfoVO> dmFocAnnualAmpVOList, CommonViewVO commonViewVO, List<ViewInfoVO> lv2ProdDimensionList) {
        // 补齐LV1，permissionFlag设置上no
        lv1Permission(commonViewVO, dmFocAnnualAmpVOList);
        // 1:校验重量级团队lv1层级的code是否有所有重量级团队LV2的权限
        for (DmFocViewInfoVO dmFocViewInfoVO : dmFocAnnualAmpVOList) {
            // 处理重量级团队Lv1权限
            handleLv1ProdTeamCodeWithThree(commonViewVO, lv2ProdDimensionList, dmFocViewInfoVO);
        }
    }

    private void handleViewFlagOne(List<DmFocViewInfoVO> dmFocAnnualAmpVOList, CommonViewVO commonViewVO, List<ViewInfoVO> lv2ProdDimensionList) {
        Set<String> lv2DimensionSet = commonViewVO.getLv2DimensionSet();
        Set<String> lv1DimensionSet = commonViewVO.getLv1DimensionSet();
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(commonViewVO.getGranularityType())) {
            Boolean hasLv1View = IndustryIndexEnum.VIEW_FLAG_D.VIEW1.getValue().equals(commonViewVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_D.VIEW2.getValue().equals(commonViewVO.getViewFlag())
                    || IndustryIndexEnum.VIEW_FLAG_D.VIEW3.getValue().equals(commonViewVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_D.VIEW10.getValue().equals(commonViewVO.getViewFlag());
            if (hasLv1View) {
                lv1ProdTeamCodePermission(dmFocAnnualAmpVOList, lv2ProdDimensionList, lv2DimensionSet, lv1DimensionSet);
                dmFocAnnualAmpVOList.removeIf(model -> "no".equals(model.getPermissionFlag()));
            }
        } else {
            if (IndustryIndexEnum.VIEW_FLAG_U.VIEW2.getValue().equals(commonViewVO.getViewFlag())) {
                lv1ProdTeamCodePermission(dmFocAnnualAmpVOList, lv2ProdDimensionList, lv2DimensionSet, lv1DimensionSet);
                dmFocAnnualAmpVOList.removeIf(model -> "no".equals(model.getPermissionFlag()));
            }
        }
    }

    private void lv1ProdTeamCodePermission(List<DmFocViewInfoVO> dmFocAnnualAmpVOList, List<ViewInfoVO> lv3ProdDimensionList, Set<String> lv2DimensionSet, Set<String> lv1DimensionSet) {
        // 1:校验重量级团队lv1层级的code是否有所有重量级团队LV2编码的权限
        for (DmFocViewInfoVO dmFocViewInfoVO : dmFocAnnualAmpVOList) {
            if (GroupLevelEnumU.LV1.getValue().equals(dmFocViewInfoVO.getGroupLevel())) {
                Set<String> lv2ProdCodeSet = lv3ProdDimensionList.stream()
                        .filter(item -> dmFocViewInfoVO.getGroupCode().equals(item.getLv1ProdRndTeamCode()))
                        .map(ViewInfoVO::getLv2ProdRndTeamCode).collect(Collectors.toSet());
                // lv1DimensionSet不为空集合，并且不包含在里面代表没有LV1的权限
                if (lv1DimensionSet.size() != 0 && !lv1DimensionSet.contains(dmFocViewInfoVO.getGroupCode())) {
                    dmFocViewInfoVO.setPermissionFlag("no");
                    continue;
                }
                // lv2DimensionSet 为空集合，代表拥有LV1的权限
                if (lv2DimensionSet.size() == 0) {
                    dmFocViewInfoVO.setPermissionFlag("has");
                    continue;
                }
                if (lv2DimensionSet.containsAll(lv2ProdCodeSet)) {
                    dmFocViewInfoVO.setPermissionFlag("has");
                } else {
                    dmFocViewInfoVO.setPermissionFlag("no");
                }
            }
        }
    }

    public void hasPermissionTeamLevelPfi(List<DmFocViewInfoVO>dmFocProfitMonthVOList,CommonViewVO commonViewVO) {
        // 当有最大权限时，就不用循环校验各子项是否有权限了
        if (commonViewVO.getLv0DimensionSet().size() == 0 && commonViewVO.getLv1DimensionSet().size() == 0 && commonViewVO.getLv2DimensionSet().size() == 0 ) {
            return;
        }
        List<ViewInfoVO> lv2ProdDimensionList = dataDimensionService.getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        // 有视角1权限时处理重量级团队Lv1
        handleViewFlagOne(dmFocProfitMonthVOList, commonViewVO, lv2ProdDimensionList);
        // 有视角2,3,4权限时处理重量级团队Lv1
        if (IndustryIndexEnum.VIEW_FLAG_P.VIEW3.getValue().equals(commonViewVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_P.VIEW4.getValue().equals(commonViewVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_P.VIEW5.getValue().equals(commonViewVO.getViewFlag())) {
            lv1ProdTeamCodePermission(dmFocProfitMonthVOList, commonViewVO, lv2ProdDimensionList);
        }
    }

    private void lv1Permission(CommonViewVO commonViewVO, List<DmFocViewInfoVO> dmFocAnnualAmpVOList) {
        commonViewVO.setReverseFlag(false);
        if (GroupLevelEnumU.LV0.getValue().equals(commonViewVO.getTeamLevel())) {
            setLv1ProCodeListForView3(commonViewVO, dmFocAnnualAmpVOList);
        }
    }

    @NotNull
    private Boolean getHasLv3View(CommonViewVO commonViewVO) {
        return IndustryIndexEnum.VIEW_FLAG_D.VIEW7.getValue().equals(commonViewVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D.VIEW8.getValue().equals(commonViewVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D.VIEW9.getValue().equals(commonViewVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D.VIEW12.getValue().equals(commonViewVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D.VIEW13.getValue().equals(commonViewVO.getViewFlag());
    }

    private List<DmFocViewInfoVO> getLv2ProdList(CommonViewVO commonViewVO, CommonViewVO lv2ViewVO, List<DmFocViewInfoVO> Lv2ProdVOList) {
        if (IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(lv2ViewVO.getGranularityType())) {
            Lv2ProdVOList = universalLv2ProdVOList(commonViewVO, lv2ViewVO, Lv2ProdVOList);
        }
        if (IndustryIndexEnum.GRANULARITY_TYPE.PROFITS.getValue().equals(lv2ViewVO.getGranularityType())) {
            Lv2ProdVOList = getPftLv2ProdList(commonViewVO, lv2ViewVO, Lv2ProdVOList);
        }
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(lv2ViewVO.getGranularityType())) {
            Lv2ProdVOList = getDmsLv2ProdList(commonViewVO, lv2ViewVO, Lv2ProdVOList);
        }
        return Lv2ProdVOList;
    }

    private List<DmFocViewInfoVO> getDmsLv2ProdList(CommonViewVO commonViewVO, CommonViewVO lv2ViewVO, List<DmFocViewInfoVO> Lv2ProdVOList) {
        if (IndustryConst.PAGE_FLAG.ANNUAL.getValue().equals(lv2ViewVO.getPageFlag())) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
                Lv2ProdVOList = dmFocDmsViewInfoDao.viewInfoList(lv2ViewVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
                Lv2ProdVOList = dmFocMadeDmsViewInfoDao.madeViewInfoList(lv2ViewVO);
            }
        } else {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
                Lv2ProdVOList = dmFocDmsViewInfoDao.viewInfoListForMonth(lv2ViewVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
                Lv2ProdVOList = dmFocMadeDmsViewInfoDao.madeViewInfoListForMonth(lv2ViewVO);
            }
        }
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(commonViewVO.getCostType())) {
            Lv2ProdVOList = dmFocTotalDmsViewInfoDao.totalViewInfoList(lv2ViewVO);
        }
        return Lv2ProdVOList;
    }

    private List<DmFocViewInfoVO> getPftLv2ProdList(CommonViewVO commonViewVO, CommonViewVO lv2ViewVO, List<DmFocViewInfoVO> Lv2ProdVOList) {
        if (IndustryConst.PAGE_FLAG.ANNUAL.getValue().equals(lv2ViewVO.getPageFlag())) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
                Lv2ProdVOList = dmFocPftViewInfoDao.viewInfoList(lv2ViewVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
                Lv2ProdVOList = dmFocMadePftViewInfoDao.madeViewInfoList(lv2ViewVO);
            }
        } else {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
                Lv2ProdVOList = dmFocPftViewInfoDao.viewInfoListForMonth(lv2ViewVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
                Lv2ProdVOList = dmFocMadePftViewInfoDao.madeViewInfoListForMonth(lv2ViewVO);
            }
        }
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(commonViewVO.getCostType())) {
            Lv2ProdVOList = dmFocTotalPftViewInfoDao.totalViewInfoList(lv2ViewVO);
        }
        return Lv2ProdVOList;
    }

    private List<DmFocViewInfoVO> universalLv2ProdVOList(CommonViewVO commonViewVO, CommonViewVO lv2ViewVO, List<DmFocViewInfoVO> lv2ProdVOList) {
        if (IndustryConst.PAGE_FLAG.ANNUAL.getValue().equals(lv2ViewVO.getPageFlag())) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
                lv2ProdVOList = dmFocViewInfoDao.viewInfoList(lv2ViewVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
                lv2ProdVOList = dmFocMadeViewInfoDao.madeViewInfoList(lv2ViewVO);
            }
        } else {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
                lv2ProdVOList = dmFocViewInfoDao.viewInfoListForMonth(lv2ViewVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonViewVO.getCostType())) {
                lv2ProdVOList = dmFocMadeViewInfoDao.madeViewInfoListForMonth(lv2ViewVO);
            }
        }
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(commonViewVO.getCostType())) {
            lv2ProdVOList = dmFocTotalViewInfoDao.totalViewInfoList(lv2ViewVO);
        }
        return lv2ProdVOList;
    }

    private void setLv1AnnualAmpVOList(CommonViewVO commonViewVO, List<DmFocViewInfoVO> dmFocAnnualAmpVOList, List<DmFocViewInfoVO> lv2ProdVOList,String groupLevel) {
        List<String> lv1ProdCodeList = dmFocAnnualAmpVOList.stream().map(lv1ProdCode -> lv1ProdCode.getGroupCode()).collect(Collectors.toList());
        // 无权限的LV1code集合
        HashSet<DmFocViewInfoVO> Lv1ProdVOSet = new HashSet<>();
        lv2ProdVOList.stream().forEach(lv2ProdEle ->{
            if (!lv1ProdCodeList.contains(lv2ProdEle.getLv1ProdRndTeamCode())) {
                DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
                dmFocViewInfoVO.setGroupCode(lv2ProdEle.getLv1ProdRndTeamCode());
                dmFocViewInfoVO.setGroupCnName(lv2ProdEle.getLv1ProdRdTeamCnName());
                dmFocViewInfoVO.setGroupLevel(groupLevel);
                if (Constant.StrEnum.ICT_ORG.getValue().equals(commonViewVO.getIndustryOrg())) {
                    dmFocViewInfoVO.setConnectCode(Constant.StrEnum.ICT.getValue() + Constant.StrEnum.UNDERLINE.getValue() +lv2ProdEle.getLv1ProdRndTeamCode());
                    dmFocViewInfoVO.setConnectParentCode(Constant.StrEnum.ICT.getValue());
                    dmFocViewInfoVO.setLv0ProdRndTeamCode(Constant.StrEnum.ICT.getValue());
                } else if (Constant.StrEnum.ENERGY_ORG.getValue().equals(commonViewVO.getIndustryOrg())){
                    dmFocViewInfoVO.setConnectCode(Constant.StrEnum.ENERGY.getValue() + Constant.StrEnum.UNDERLINE.getValue() + lv2ProdEle.getLv1ProdRndTeamCode());
                    dmFocViewInfoVO.setConnectParentCode(Constant.StrEnum.ENERGY.getValue());
                    dmFocViewInfoVO.setLv0ProdRndTeamCode(Constant.StrEnum.ENERGY.getValue());
                } else {
                    dmFocViewInfoVO.setConnectCode(Constant.StrEnum.IAS.getValue() + Constant.StrEnum.UNDERLINE.getValue() + lv2ProdEle.getLv1ProdRndTeamCode());
                    dmFocViewInfoVO.setConnectParentCode(Constant.StrEnum.IAS.getValue());
                    dmFocViewInfoVO.setLv0ProdRndTeamCode(Constant.StrEnum.IAS.getValue());
                }
                dmFocViewInfoVO.setLv1ProdRndTeamCode(lv2ProdEle.getLv1ProdRndTeamCode());
                dmFocViewInfoVO.setPermissionFlag("no");
                Lv1ProdVOSet.add(dmFocViewInfoVO);
            }
        });
        // 无权限的lv1code添加到集合里
        dmFocAnnualAmpVOList.addAll(Lv1ProdVOSet);
    }

    private  void setLv1ProCodeListForView3(CommonViewVO commonViewVO, List<DmFocViewInfoVO> dmFocAnnualAmpVOList) {
        CommonViewVO Lv1ViewVO = new CommonViewVO();
        Lv1ViewVO.setViewFlag(commonViewVO.getViewFlag());
        Lv1ViewVO.setMonthVersionId(commonViewVO.getMonthVersionId());
        Lv1ViewVO.setLv0ProdListCode(commonViewVO.getLv0ProdListCode());
        Lv1ViewVO.setOverseaFlag(commonViewVO.getOverseaFlag());
        Lv1ViewVO.setCaliberFlag(commonViewVO.getCaliberFlag());
        Lv1ViewVO.setPageFlag(commonViewVO.getPageFlag());
        Lv1ViewVO.setLv2DimensionSet(commonViewVO.getLv2DimensionSet());
        Lv1ViewVO.setReverseFlag(commonViewVO.getReverseFlag());
        Lv1ViewVO.setPurCodeList(commonViewVO.getPurCodeList());
        Lv1ViewVO.setPurLevel(commonViewVO.getPurLevel());
        Lv1ViewVO.setPurLevel(commonViewVO.getPurLevel());
        Lv1ViewVO.setManufactureObjectCode(commonViewVO.getManufactureObjectCode());
        Lv1ViewVO.setShippingObjectCode(commonViewVO.getShippingObjectCode());
        Lv1ViewVO.setGranularityType(commonViewVO.getGranularityType());
        Lv1ViewVO.setCostType(commonViewVO.getCostType());
        Lv1ViewVO.setIndustryOrg(commonViewVO.getIndustryOrg());
        setRevViewFlag(commonViewVO, Lv1ViewVO);
        List<DmFocViewInfoVO> Lv1ProdVOList = new ArrayList<>();
        Lv1ProdVOList = getLv1ProdList(commonViewVO, Lv1ViewVO, Lv1ProdVOList);
        setLv1AnnualAmpVOList(Lv1ViewVO,dmFocAnnualAmpVOList, Lv1ProdVOList,GroupLevelEnumU.LV1.getValue());
    }

    private void setRevViewFlag(CommonViewVO commonViewVO, CommonViewVO Lv1ViewVO) {
        if (Lv1ViewVO.getReverseFlag() && IndustryConst.PAGE_FLAG.ANNUAL.getValue().equals(Lv1ViewVO.getPageFlag())) {
            if (IndustryConst.INDUSTRY_ORG.IAS.getValue().equals(Lv1ViewVO.getIndustryOrg())) {
                Lv1ViewVO.setViewFlag("3");
            } else {
                Lv1ViewVO.setViewFlag("7");
            }
        }
        if (Lv1ViewVO.getReverseFlag() && IndustryConst.PAGE_FLAG.MONTH.getValue().equals(Lv1ViewVO.getPageFlag())) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(commonViewVO.getCostType())) {
                Lv1ViewVO.setViewFlag("6");
            } else {
                Lv1ViewVO.setViewFlag("5");
            }
        }
    }

    private List<DmFocViewInfoVO> getLv1ProdList(CommonViewVO commonViewVO, CommonViewVO lv1ViewVO, List<DmFocViewInfoVO> lv1ProdVOList) {
        if (IndustryConst.PAGE_FLAG.ANNUAL.getValue().equals(commonViewVO.getPageFlag())) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(lv1ViewVO.getCostType())) {
                lv1ProdVOList = dmFocViewInfoDao.reverseFindLv1ProdCode(lv1ViewVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(lv1ViewVO.getCostType())) {
                lv1ProdVOList = dmFocMadeViewInfoDao.madeReverseFindLv1ProdCode(lv1ViewVO);
            }
        } else {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(lv1ViewVO.getCostType())) {
                lv1ProdVOList = dmFocViewInfoDao.reverseFindLv1ProdCodeMonth(lv1ViewVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(lv1ViewVO.getCostType())) {
                lv1ProdVOList = dmFocMadeViewInfoDao.madeReverseFindLv1ProdCodeMonth(lv1ViewVO);
            }
        }
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(lv1ViewVO.getCostType())) {
            lv1ProdVOList = dmFocTotalViewInfoDao.totalReverseFindLv1ProdCode(lv1ViewVO);
        }
        return lv1ProdVOList;
    }

    public List<DmFocViewInfoVO> sortLv1ByCode(List<DmFocViewInfoVO> dmFocAnnualAmpVOList) {
        List<DmFocViewInfoVO> newAnnualAmpVOList = new ArrayList<>();
        // 重量级团队Lv1排序
        if (CollectionUtils.isNotEmpty(dmFocAnnualAmpVOList)) {
            if (GroupLevelEnumU.LV1.getValue().equals(dmFocAnnualAmpVOList.get(0).getGroupLevel())) {
                for (String code : CommonConstant.LV1_CODE) {
                    for (DmFocViewInfoVO dmFocViewInfoVO : dmFocAnnualAmpVOList) {
                        if (code.equals(dmFocViewInfoVO.getGroupCode())) {
                            newAnnualAmpVOList.add(dmFocViewInfoVO);
                        }
                    }
                }
                List<DmFocViewInfoVO> otherAmpVOList = dmFocAnnualAmpVOList.stream().filter(amp -> !newAnnualAmpVOList.contains(amp)).collect(Collectors.toList());
                newAnnualAmpVOList.addAll(otherAmpVOList);
            }
            distinctAnnualAmpList(dmFocAnnualAmpVOList, newAnnualAmpVOList);
        }
        return  newAnnualAmpVOList;
    }

    private void distinctAnnualAmpList(List<DmFocViewInfoVO> dmFocAnnualAmpVOList, List<DmFocViewInfoVO> newAnnualAmpVOList) {
        // 采购层级code编辑唯一处理下
        if (CommonConstant.GROUP_LEVEL_P.contains(dmFocAnnualAmpVOList.get(0).getGroupLevel())) {
            newAnnualAmpVOList.addAll(dmFocAnnualAmpVOList.stream().distinct().collect(Collectors.toList()));
        }
        // 制造层级code编辑唯一处理下
        if (CommonConstant.GROUP_LEVEL_M.contains(dmFocAnnualAmpVOList.get(0).getGroupLevel())) {
            newAnnualAmpVOList.addAll(dmFocAnnualAmpVOList.stream().distinct().collect(Collectors.toList()));
        }
        // 量纲层级code编辑唯一处理下
        if (CommonConstant.GROUP_LEVEL_ALL_DIMENSION.contains(dmFocAnnualAmpVOList.get(0).getGroupLevel())) {
            newAnnualAmpVOList.addAll(dmFocAnnualAmpVOList.stream().distinct().collect(Collectors.toList()));
        }
        // 盈利颗粒度层级code编辑唯一处理下
        if (CommonConstant.GROUP_LEVEL_PROFITS.contains(dmFocAnnualAmpVOList.get(0).getGroupLevel())) {
            newAnnualAmpVOList.addAll(dmFocAnnualAmpVOList.stream().distinct().collect(Collectors.toList()));
        }
    }
}
