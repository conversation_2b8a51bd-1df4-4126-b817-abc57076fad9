/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.dao;

import com.huawei.it.fcst.industry.pbi.vo.common.CommonBaseVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstDimInfoVO;

import java.util.List;

/**
 * IDmFocViewInfoDao
 *
 * <AUTHOR>
 * @since 2023/3/14
 */
public interface IDmFcstCustomCombDao {

    List<DmFcstDimInfoVO> getCombinationSubByGroupLevel(CommonBaseVO commonBaseVO);

    List<DmFcstDimInfoVO> getCombinationByGroupLevel(CommonBaseVO commonBaseVO);

    List<DmFcstDimInfoVO> getCombinationParent(CommonBaseVO commonBaseVO);

}
