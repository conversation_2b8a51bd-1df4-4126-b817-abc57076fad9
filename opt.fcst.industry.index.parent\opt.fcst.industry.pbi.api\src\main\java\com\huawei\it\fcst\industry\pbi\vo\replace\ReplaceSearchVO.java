/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.replace;

import com.huawei.it.fcst.industry.pbi.vo.common.CommonBaseVO;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * ReplaceSearchVO Class
 *
 * <AUTHOR>
 * @since 2024/7/10
 */

@Getter
@Setter
@EqualsAndHashCode
@NoArgsConstructor
public class ReplaceSearchVO extends CommonBaseVO implements Serializable {

    private Long versionId;

    private String version;

    private String column;

    private String fileName;

    private String lv1Code;

    private String lv1CnName;

    private String lv2Code;

    private String lv2CnName;

    private String lv3Code;

    private String lv3CnName;

    private String lv4Code;

    private String lv4CnName;

    // 多选spart_code ：111,222,333
    private String oldSpartCode;

    private String newSpartCode;

    private String relationType;

    private String replaceRelationType;

    private String replaceRelationName;

    private String keyword;

    private List<Long> idList;

    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<DmFocReplVO> replaceList;

    private Set<String> replaceRelationNameList;

    private List<String> bgCodeList;

}
