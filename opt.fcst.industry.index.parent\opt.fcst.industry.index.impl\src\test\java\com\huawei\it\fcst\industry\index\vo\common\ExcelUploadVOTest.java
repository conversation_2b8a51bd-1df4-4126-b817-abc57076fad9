/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.common;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

/**
 * ExcelUploadVOTest Class
 *
 * <AUTHOR>
 * @since 2023/4/22
 */
public class ExcelUploadVOTest extends BaseVOCoverUtilsTest<ExcelUploadVO> {

    @Override
    protected Class<ExcelUploadVO> getTClass() {
        return ExcelUploadVO.class;
    }

    @Test
    public void testMethod() {
        ExcelUploadVO dmFocActualCostVO = new ExcelUploadVO();
        dmFocActualCostVO.setFileName("file");
        dmFocActualCostVO.getFileName();
        dmFocActualCostVO.setMergeCell(true);
        dmFocActualCostVO.getMergeCell();
        Assert.assertNotNull(dmFocActualCostVO);
    }
}