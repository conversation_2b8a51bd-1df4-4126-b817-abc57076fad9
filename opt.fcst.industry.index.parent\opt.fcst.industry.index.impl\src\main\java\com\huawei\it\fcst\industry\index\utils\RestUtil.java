/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.utils;

import com.huawei.his.jalor.https.CertificateVerifier;
import com.huawei.his.jalor.https.CertificateVerifierFactory;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

/**
 * RestUtil Class
 *
 * <AUTHOR>
 * @since 2024/4/11
 */
public class RestUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(RestUtil.class);

    private static final String UTF8 = "UTF-8";

    private static final String AUTHORIZATION = "Authorization";

    private static final String HTTP = "http";
    private static final String HTTPS = "https";
    private static SSLConnectionSocketFactory sslConnectionSocketFactory;

    // 连接池管理类
    private static PoolingHttpClientConnectionManager poolingHttpClientConnectionManager;

    // 管理HTTPS的上下文
    private static SSLContextBuilder sslContextBuilder;

    private static final String[] SUPPORTED_CIPHER_SUITES = new String[] {
            "TLS_DHE_RSA_WITH_AES_128_GCM_SHA256", "TLS_DHE_RSA_WITH_AES_256_GCM_SHA384",
            "TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256", "TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384",
            "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256", "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"
    };

    static {
        try {
            sslContextBuilder = new SSLContextBuilder().setSecureRandom(SecureRandom.getInstanceStrong()).loadTrustMaterial(null,
                    (x509Certificates, authType) -> {
                        LOGGER.debug("- begin check x509Certificates .. ");
                        // 证书校验：校验对端证书是否由受信根CA签发，是否已被吊销，是否在有效期内
                        CertificateVerifier verifier = CertificateVerifierFactory.getInstance();
                        verifier.checkTrustIssuedRootCA(x509Certificates, authType);
                        verifier.checkCRLRevoked(x509Certificates);
                        verifier.checkCertValidity(x509Certificates);
                        LOGGER.debug("- end check x509Certificates .. ");
                        return true;
                    }
            );
            sslConnectionSocketFactory = new SSLConnectionSocketFactory(sslContextBuilder.build(),
                    new String[]{"TLSv1.2", "TLSv1.3"}, SUPPORTED_CIPHER_SUITES, NoopHostnameVerifier.INSTANCE);
            Registry<ConnectionSocketFactory> registryBuilder = RegistryBuilder.<ConnectionSocketFactory>create()
                    .register(HTTP, new PlainConnectionSocketFactory())
                    .register(HTTPS, sslConnectionSocketFactory)
                    .build();
            poolingHttpClientConnectionManager = new PoolingHttpClientConnectionManager(registryBuilder);
            poolingHttpClientConnectionManager.setMaxTotal(200);
        } catch (NoSuchAlgorithmException noSuchAlgorithmException) {
            noSuchAlgorithmException.printStackTrace();
        } catch (KeyStoreException keyStoreException) {
            keyStoreException.printStackTrace();
        } catch (KeyManagementException keyManagementException) {
            keyManagementException.printStackTrace();
        }
    }

    /**
     * Get HttpClient
     *
     * @return CloseableHttpClient HttpClient
     */
    public static CloseableHttpClient getHttpClient() {
        return HttpClients.custom()
                .setSSLSocketFactory(sslConnectionSocketFactory)
                .setConnectionManager(poolingHttpClientConnectionManager)
                .setConnectionManagerShared(true)
                .build();
    }

    /**
     *  post请求调用
     *
     *  @param url 请求链接
     *  @param basicToken 动态token
     *  @param json json格式的参数
     *  @param request 请求头
     *  @return String
     */
    public static String doPostByAuthorization(String url, String basicToken, String json, HttpServletRequest request) {
        String result = "";
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse response = null;
        try {
            httpClient = url.contains(HTTPS) ? getHttpClient() : HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(url);
            StringEntity stringEntity = new StringEntity(json, "utf-8");
            stringEntity.setContentType("text/json");
            httpPost.addHeader(AUTHORIZATION, basicToken);
            httpPost.addHeader("Referer", request.getHeader("Referer"));
            httpPost.setHeader("Content-Type", "application/json;charset=UTF-8");
            httpPost.setEntity(stringEntity);
            response = httpClient.execute(httpPost);
            HttpEntity httpEntity = response.getEntity();
            result = EntityUtils.toString(httpEntity, "UTF-8");
        } catch (IOException exception) {
            LOGGER.error(exception.getMessage(), exception);
        } catch (Exception ex) {
            LOGGER.error(ex.getMessage(), ex);
        } finally {
            closeResponse(httpClient, response); // 关闭连接,释放资源
        }
        return result;
    }

    /**
     * 关闭响应和连接 .
     *
     * @param httpClient .
     * @param response .
     */
    private static void closeResponse(CloseableHttpClient httpClient, CloseableHttpResponse response) {
        if (null != response) {
            try {
                response.close();
            } catch (IOException e) {
                LOGGER.error(e.getMessage(), e);
            }
        }
        if (null != httpClient) {
            try {
                httpClient.close();
            } catch (IOException e) {
                LOGGER.error(e.getMessage(), e);
            }
        }
    }

}
