/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.service.annual;

import com.huawei.it.fcst.industry.index.vo.annual.AnnualAnalysisVO;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.util.List;

/**
 * IAnnualAmpService Class
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
@Path("/annual")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface IAnnualAmpService {
    /**
     * 查询当前层级涨跌幅
     *
     * @param annualAnalysisVO 参数
     * @return 结果
     */
    @Path("/allIndustryCost")
    @POST
    ResultDataVO allIndustryCost(AnnualAnalysisVO annualAnalysisVO);

    /**
     * 查询当前层级的子项涨跌幅
     *
     * @param annualAnalysisVO 参数
     * @return 结果
     */
    @Path("/multiIndustryCostChart")
    @POST
    ResultDataVO multiIndustryCostChart(AnnualAnalysisVO annualAnalysisVO);

    /**
     * 查询当前层级的子项涨跌幅和权重
     *
     * @param annualAnalysisVO 参数
     * @return 结果
     */
    @Path("/industryCostList")
    @POST
    ResultDataVO industryCostList(AnnualAnalysisVO annualAnalysisVO);

    /**
     * 查询当前层级的成本分布图
     *
     * @param annualAnalysisVO 参数
     * @return 结果
     */
    @Path("/distributeCostChart")
    @POST
    ResultDataVO distributeCostChart(AnnualAnalysisVO annualAnalysisVO);

    /**
     * 查询对比分析涨跌图
     *
     * @param annualAnalysisList 参数
     * @return 结果
     */
    @Path("/getCompareAmpCost")
    @POST
    ResultDataVO getCompareAmpCost(List<AnnualAnalysisVO> annualAnalysisList) throws InterruptedException;


    /**
     * 数据下载
     *
     * @param annualAnalysisVO 参数
     * @param response 响应
     * @return 结果
     */
    @Path("/exportDetail")
    @POST
    ResultDataVO exportDetail(AnnualAnalysisVO annualAnalysisVO, @Context HttpServletResponse response) throws ApplicationException, IOException, InterruptedException;

}
