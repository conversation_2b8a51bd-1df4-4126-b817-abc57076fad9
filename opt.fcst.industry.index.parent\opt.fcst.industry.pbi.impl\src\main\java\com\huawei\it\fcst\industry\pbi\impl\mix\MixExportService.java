/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.mix;

import com.huawei.it.fcst.common.StatisticsExcelService;
import com.huawei.it.fcst.export.vo.PbiDmFoiImpExpRecordVO;
import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.industry.pbi.impl.common.AsyncIctQueryService;
import com.huawei.it.fcst.industry.pbi.vo.mix.MixSearchVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.request.IRequestContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Named;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.concurrent.Future;

/**
 * MixExportService Class
 *
 * <AUTHOR>
 * @since 2024/10/16
 */
@Slf4j
@Named("mixExportService")
public class MixExportService {

    @Autowired
    private AsyncIctQueryService asyncIctQueryService;

    @Autowired
    private StatisticsExcelService statisticsExcelService;

    public void exportDetailData(MixSearchVO mixSearchVO, XSSFWorkbook workbook, IRequestContext current, Long userId) throws Exception {
        String exportTemplate = mixSearchVO.getExportTemplate();
        int totalRows;
        if (CommonConstant.INDUSTRY_MIX_TEMPLATE1_PATH.equals(exportTemplate)) {
            totalRows = exportTemplateOne(mixSearchVO, workbook, current);
        } else if (CommonConstant.INDUSTRY_MIX_TEMPLATE2_PATH.equals(exportTemplate)) {
            totalRows = exportTemplateTwo(mixSearchVO, workbook, current);
        } else if (CommonConstant.INDUSTRY_MIX_TEMPLATE3_PATH.equals(exportTemplate)) {
            totalRows = exportTemplateThree(mixSearchVO, workbook, current);
        } else if (CommonConstant.INDUSTRY_MIX_TEMPLATE4_PATH.equals(exportTemplate)) {
            totalRows = exportTemplateFour(mixSearchVO, workbook, current);
        } else {
            totalRows = exportTemplateFive(mixSearchVO, workbook, current);
        }
        insertExportRecord(totalRows, workbook, mixSearchVO, userId);
    }

    private void insertExportRecord(int totalRows, Workbook workbook, MixSearchVO mixSearchVO, Long userId) throws IOException, CommonApplicationException {
        // 插入数据，上传文件
        String fileName = mixSearchVO.getFileName();
        PbiDmFoiImpExpRecordVO dmFoiImpExpRecordVO = StatisticsExcelService.uploadExportExcel(workbook, totalRows, fileName, userId);
        String module = "成本指数-ICT-成本勾稽分析";
        dmFoiImpExpRecordVO.setModuleType(module);
        dmFoiImpExpRecordVO.setUserId(String.valueOf(userId));
        // 设置创建时间和结束时间
        dmFoiImpExpRecordVO.setCreationDate(mixSearchVO.getCreationDate());
        dmFoiImpExpRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        dmFoiImpExpRecordVO.setEndDate(new Timestamp(System.currentTimeMillis()));
        // 插入数据
        statisticsExcelService.insertExportExcelRecord(dmFoiImpExpRecordVO);
    }

    private int exportTemplateOne(MixSearchVO mixSearchVO, XSSFWorkbook workbook, IRequestContext current) throws Exception {

        Future<Integer> distructeMonTotal = asyncIctQueryService.getDistructeExcelData(mixSearchVO, workbook, 0, current, CommonConstant.MONTH_N);

        Future<Integer> distructeYtdTotal = asyncIctQueryService.getDistructeExcelData(mixSearchVO, workbook, 1, current, CommonConstant.MONTH_ACC);

        Future<Integer> diffTotal = asyncIctQueryService.getDiffExcelData(mixSearchVO, workbook, 2, current);

        Future<Integer> currentMonPriceIndexTotal = asyncIctQueryService.getCurrentPriceIndexData(mixSearchVO, workbook, 3, current, CommonConstant.MONTH_N);
        Future<Integer> currentAccPriceIndexTotal = asyncIctQueryService.getCurrentPriceIndexData(mixSearchVO, workbook, 4, current, CommonConstant.MONTH_ACC);

        Future<Integer> multiMonPriceIndexTotal = asyncIctQueryService.getMultiPriceIndexData(mixSearchVO, workbook, 5, current, CommonConstant.MONTH_N);
        Future<Integer> multiAccPriceIndexTotal = asyncIctQueryService.getMultiPriceIndexData(mixSearchVO, workbook, 6, current, CommonConstant.MONTH_ACC);
        Future<Integer> monResultPriceIndexTotal = asyncIctQueryService.getResultPriceIndexData(mixSearchVO, workbook, 7, current, CommonConstant.MONTH_N);
        Future<Integer> accResultPriceIndexTotal = asyncIctQueryService.getResultPriceIndexData(mixSearchVO, workbook, 8, current, CommonConstant.MONTH_ACC);

        while (true) {
            boolean condition = distructeMonTotal.isDone() && distructeYtdTotal.isDone() && diffTotal.isDone();
            boolean condition2 = currentMonPriceIndexTotal.isDone() && currentAccPriceIndexTotal.isDone() && multiMonPriceIndexTotal.isDone();
            boolean condition3 = multiAccPriceIndexTotal.isDone() && monResultPriceIndexTotal.isDone() && accResultPriceIndexTotal.isDone();
            if (condition && condition2 && condition3) {
                break;
            }
        }
        return distructeMonTotal.get() + distructeYtdTotal.get() + diffTotal.get() + currentMonPriceIndexTotal.get()
                + currentAccPriceIndexTotal.get() + multiMonPriceIndexTotal.get() + multiAccPriceIndexTotal.get()
                + monResultPriceIndexTotal.get() + accResultPriceIndexTotal.get();
    }

    private int exportTemplateTwo(MixSearchVO mixSearchVO, XSSFWorkbook workbook, IRequestContext current) throws Exception {

        Future<Integer> currentMonPriceIndexTotal = asyncIctQueryService.getCurrentPriceIndexData(mixSearchVO, workbook, 0, current, CommonConstant.MONTH_N);
        Future<Integer> currentAccPriceIndexTotal = asyncIctQueryService.getCurrentPriceIndexData(mixSearchVO, workbook, 1, current, CommonConstant.MONTH_ACC);
        Future<Integer> monResultPriceIndexTotal = asyncIctQueryService.getResultPriceIndexData(mixSearchVO, workbook, 2, current, CommonConstant.MONTH_N);
        Future<Integer> accResultPriceIndexTotal = asyncIctQueryService.getResultPriceIndexData(mixSearchVO, workbook, 3, current, CommonConstant.MONTH_ACC);

        while (true) {
            boolean condition = currentMonPriceIndexTotal.isDone() && currentAccPriceIndexTotal.isDone() && monResultPriceIndexTotal.isDone();
            if (condition && accResultPriceIndexTotal.isDone()) {
                break;
            }
        }
        return currentMonPriceIndexTotal.get() + currentAccPriceIndexTotal.get() + monResultPriceIndexTotal.get() + accResultPriceIndexTotal.get();
    }

    private int exportTemplateThree(MixSearchVO mixSearchVO, XSSFWorkbook workbook, IRequestContext current) throws Exception {

        Future<Integer> distructeMonTotal = asyncIctQueryService.getDistructeExcelData(mixSearchVO, workbook, 0, current, CommonConstant.MONTH_N);

        Future<Integer> distructeYtdTotal = asyncIctQueryService.getDistructeExcelData(mixSearchVO, workbook, 1, current, CommonConstant.MONTH_ACC);

        Future<Integer> diffTotal = asyncIctQueryService.getDiffExcelData(mixSearchVO, workbook, 2, current);

        Future<Integer> currentMonPriceIndexTotal = asyncIctQueryService.getCurrentPriceIndexData(mixSearchVO, workbook, 3, current, CommonConstant.MONTH_N);
        Future<Integer> currentAccPriceIndexTotal = asyncIctQueryService.getCurrentPriceIndexData(mixSearchVO, workbook, 4, current, CommonConstant.MONTH_ACC);

        while (true) {
            boolean condition = distructeMonTotal.isDone() && distructeYtdTotal.isDone() && currentMonPriceIndexTotal.isDone();
            boolean condition2 = currentAccPriceIndexTotal.isDone() && diffTotal.isDone();
            if (condition && condition2) {
                break;
            }
        }
        return distructeMonTotal.get() + distructeYtdTotal.get() + diffTotal.get() + currentMonPriceIndexTotal.get()
                + currentAccPriceIndexTotal.get();
    }

    private int exportTemplateFour(MixSearchVO mixSearchVO, XSSFWorkbook workbook, IRequestContext current) throws Exception {

        Future<Integer> diffTotal = asyncIctQueryService.getDiffExcelData(mixSearchVO, workbook, 0, current);

        Future<Integer> currentMonPriceIndexTotal = asyncIctQueryService.getCurrentPriceIndexData(mixSearchVO, workbook, 1, current, CommonConstant.MONTH_N);
        Future<Integer> currentAccPriceIndexTotal = asyncIctQueryService.getCurrentPriceIndexData(mixSearchVO, workbook, 2, current, CommonConstant.MONTH_ACC);

        while (true) {
            boolean condition = currentMonPriceIndexTotal.isDone() && currentAccPriceIndexTotal.isDone() && diffTotal.isDone();
            if (condition) {
                break;
            }
        }
        return diffTotal.get() + currentMonPriceIndexTotal.get() + currentAccPriceIndexTotal.get();
    }

    private int exportTemplateFive(MixSearchVO mixSearchVO, XSSFWorkbook workbook, IRequestContext current) throws Exception {

        Future<Integer> diffTotal = asyncIctQueryService.getDiffExcelData(mixSearchVO, workbook, 0, current);

        Future<Integer> currentMonPriceIndexTotal = asyncIctQueryService.getCurrentPriceIndexData(mixSearchVO, workbook, 1, current, CommonConstant.MONTH_N);
        Future<Integer> currentAccPriceIndexTotal = asyncIctQueryService.getCurrentPriceIndexData(mixSearchVO, workbook, 2, current, CommonConstant.MONTH_ACC);
        Future<Integer> multiMonPriceIndexTotal = asyncIctQueryService.getMultiPriceIndexData(mixSearchVO, workbook, 3, current, CommonConstant.MONTH_N);
        Future<Integer> multiAccPriceIndexTotal = asyncIctQueryService.getMultiPriceIndexData(mixSearchVO, workbook, 4, current, CommonConstant.MONTH_ACC);
        Future<Integer> monResultPriceIndexTotal = asyncIctQueryService.getResultPriceIndexData(mixSearchVO, workbook, 5, current, CommonConstant.MONTH_N);
        Future<Integer> accResultPriceIndexTotal = asyncIctQueryService.getResultPriceIndexData(mixSearchVO, workbook, 6, current, CommonConstant.MONTH_ACC);

        while (true) {
            boolean condition = diffTotal.isDone() && currentMonPriceIndexTotal.isDone() && currentAccPriceIndexTotal.isDone();
            boolean condition2 = multiMonPriceIndexTotal.isDone() && multiAccPriceIndexTotal.isDone() && monResultPriceIndexTotal.isDone();
            if (condition && condition2 && accResultPriceIndexTotal.isDone()) {
                break;
            }
        }
        return diffTotal.get() + currentMonPriceIndexTotal.get()
                + currentAccPriceIndexTotal.get() + multiMonPriceIndexTotal.get() +multiAccPriceIndexTotal.get() +monResultPriceIndexTotal.get() + accResultPriceIndexTotal.get();
    }
}
