/*
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.relation;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.huawei.it.fcst.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * DmDimMaterialCodeVO Class
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "指数预测指数预测物理维度表实体类")
public class DmDimMaterialCodeVO extends BaseVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 品类名称
     **/
    @JsonProperty("item_subtype_cn_name")
    private String itemSubtypeCnName;

    /**
     * 品类code
     **/
    @JsonProperty("item_subtype_code")
    private String itemSubtypeCode;

    /**
     *
     *  关键字模糊查询
     */
    private String keyword;
}
