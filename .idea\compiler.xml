<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="opt.fcst.industry.index.api" />
        <module name="opt.fcst.industry.index.impl" />
        <module name="opt.fcst.industry.pbi.impl" />
        <module name="opt.fcst.industry.index.start" />
        <module name="opt.fcst.industry.price.api" />
        <module name="opt.fcst.industry.price.impl" />
        <module name="opt.fcst.industry.common" />
        <module name="opt.fcst.industry.pbi.api" />
      </profile>
    </annotationProcessing>
  </component>
</project>