/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.config;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;
import com.huawei.it.fcst.industry.index.vo.common.HeaderVo;

import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * HistoryInputVOTest Class
 *
 * <AUTHOR>
 * @since 2023/4/23
 */
public class HistoryInputVOTest extends BaseVOCoverUtilsTest<HistoryInputVO> {

    @Override
    protected Class<HistoryInputVO> getTClass() {
        return HistoryInputVO.class;
    }

    @Test
    public void testMethod() {
        HistoryInputVO dimensionParamVO = new HistoryInputVO();
        dimensionParamVO.setTotalSize(10);
        dimensionParamVO.getTotalSize();
        dimensionParamVO.setFileName("file");
        dimensionParamVO.setLv0ProdRndTeamCode("000");
        dimensionParamVO.setLv1ProdRndTeamCode("000");
        dimensionParamVO.setLv2ProdRndTeamCode("000");
        dimensionParamVO.setModelType("mode");
        dimensionParamVO.setTopL3CegCode("333");
        dimensionParamVO.setTopCategoryCode("code");
        dimensionParamVO.setLv3ProdRndTeamCode("l3");
        dimensionParamVO.getLv3ProdRndTeamCode();
        dimensionParamVO.setViewFlag("0");
        dimensionParamVO.getViewFlag();
        dimensionParamVO.setCaliberFlag("U");
        dimensionParamVO.getCaliberFlag();
        dimensionParamVO.setGranularityType("R");
        dimensionParamVO.getGranularityType();
        dimensionParamVO.setFileName("file");
        dimensionParamVO.getFileName();
        List<HeaderVo> list = new ArrayList<>();
        HeaderVo headerVo = new HeaderVo("title", "field", null, true, 100 * 5);
        list.add(headerVo);
        dimensionParamVO.setTopCateHeader(list);
        dimensionParamVO.getTopCateHeader();
        dimensionParamVO.setTopItemHeader(list);
        dimensionParamVO.getTopItemHeader();
        Assert.assertNotNull(dimensionParamVO);
    }
}