/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.enums;

/**
 * Group层级枚举类
 *
 * <AUTHOR>
 * @since 2023/3/21
 */
public enum GroupLevelEnumMadeD {
    // Group层级（LV0：LV0、LV1：重量级团队LV1、LV2: 重量级团队LV2、LV3:重量级团队LV3、LV4:重量级团队LV4、SHIPPING_OBJECT：发货对象、MANUFACTURE_OBJECT：制造对象、item：规格品）
    LV0("LV0", "LV0"),
    LV1("LV1", "重量级团队LV1"),
    LV2("LV2", "重量级团队LV2"),
    LV3("LV3", "重量级团队LV3"),
    LV4("LV4", "重量级团队LV3.5"),
    COA("COA", "COA"),
    DIMENSION("DIMENSION", "量纲"),
    SUBCATEGORY("SUBCATEGORY", "量纲子类"),
    SUB_DETAIL("SUB_DETAIL", "量纲子类明细"),
    SPART("SPART", "SPART"),
    SHIPPING_OBJECT("SHIPPING_OBJECT", "发货对象"),
    MANUFACTURE_OBJECT("MANUFACTURE_OBJECT", "制造对象"),
    ITEM("ITEM", "ITEM");

    private String value;
    private String name;

    GroupLevelEnumMadeD(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据key获取对应的实例
     *
     * @param key group level
     * @return GroupLevelEnumU
     */
    public static GroupLevelEnumMadeD getInstance(String key) {
        for (GroupLevelEnumMadeD value : GroupLevelEnumMadeD.values()) {
            if (value.getValue().equalsIgnoreCase(key)) {
                return value;
            }
        }
        return null;
    }
}