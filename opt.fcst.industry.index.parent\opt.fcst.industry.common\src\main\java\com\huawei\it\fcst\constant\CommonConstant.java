/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.constant;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * CommonConstant Class
 *
 * <AUTHOR>
 * @since 2024/7/2
 */
public class CommonConstant {

    public final static Map<String, Integer> BG_CODE_RULE = new HashMap<>();

    static {
        BG_CODE_RULE.put("GR", 1);
        BG_CODE_RULE.put("PDCG901160", 2);
        BG_CODE_RULE.put("PDCG901159", 3);
    }

    public final static Map<String, Integer> ALL_CODE_RULE = new HashMap<>();

    static {
        ALL_CODE_RULE.put("ALL",1);
    }

    public final static Map<String, Integer> ALL_CN_RULE = new HashMap<>();

    static {
        ALL_CN_RULE.put("全选",1);
    }

    public final static Set<String> OVERSEA_FLAG = new HashSet<>();

    static {
        OVERSEA_FLAG.add("N");
        OVERSEA_FLAG.add("Y");
    }
}