/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.constant;

import com.huawei.it.fcst.vo.HeaderVo;
import org.apache.poi.ss.usermodel.CellType;

import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * CommonConstant Class
 *
 * <AUTHOR>
 * @since 2024/7/2
 */
public class CommonConstant {

    public static final Map<String, String> overseaMap = new HashMap<String, String>();

    static {
        overseaMap.put("N", "国内");
        overseaMap.put("Y", "海外");
        overseaMap.put("G", "全球");
    }

    public static final String VARCHAR = "VARCHAR";

    public static final String IS_NOT = "N";

    public static final String HISTORY_PAGE = "history";

    public static final String ABNORMAL_PAGE = "abnormal";

    // 定价指数区域分析师
    public static final String REGION_ANALYST_PRICE = "Region_Analyst_Pri";

    public static final String DATA_REVIEW_MODULE_TYPE = "价格指数-产业-底层数据审视";

    public static final String DATA_REVIEW_OPERATION = "价格指数-产业-底层数据审视-操作记录";

    public static final String DATA_REVIEW_ABNORMAL = "价格指数-产业-底层数据审视-异常数据";

    public static final Pattern PATTERN_COLUMN = Pattern.compile("(_([a-z]))");

    public static final String DATA_REVIEW_TEMPLATE_PATH ="excel/export/template/dataReviewPriceTemplate.xlsx";

    public static final Map<String, String> overseaFlagMap = new HashMap<String, String>();

    static {
        overseaFlagMap.put("全球", "G");
        overseaFlagMap.put("国内", "N");
        overseaFlagMap.put("海外", "Y");
    }

    public final static Set<String> MODIFY_TYPE = new HashSet<String>();

    static {
        MODIFY_TYPE.add("INSERT");
        MODIFY_TYPE.add("MODIFY");
    }

    public static final List<HeaderVo> DATA_REVIEW_COLUMN_HEADER = new LinkedList<>();
    static {
        DATA_REVIEW_COLUMN_HEADER.add(new HeaderVo("操作类型", "modifyType", CellType.STRING,true, 12 * 480));
        DATA_REVIEW_COLUMN_HEADER.add(new HeaderVo("BG", "bgCnName",CellType.STRING,true, 12 * 480));
        DATA_REVIEW_COLUMN_HEADER.add(new HeaderVo("国内/海外", "overseaFlag",CellType.STRING,true, 12 * 480));
        DATA_REVIEW_COLUMN_HEADER.add(new HeaderVo("地区部", "regionCnName",CellType.STRING,true,12 * 480));
        DATA_REVIEW_COLUMN_HEADER.add(new HeaderVo("代表处", "repofficeCnName",CellType.STRING,true, 12 * 480));
        DATA_REVIEW_COLUMN_HEADER.add(new HeaderVo("大T系统部", "signTopCustCategoryCnName",CellType.STRING,true, 12 * 480));
        DATA_REVIEW_COLUMN_HEADER.add(new HeaderVo("子网系统", "signSubsidiaryCustcatgCnName",CellType.STRING,true, 12 * 480));
        DATA_REVIEW_COLUMN_HEADER.add(new HeaderVo("L1名称","lv1CnName", CellType.STRING,true,12 * 480));
        DATA_REVIEW_COLUMN_HEADER.add(new HeaderVo("L1编码", "lv1Code",CellType.STRING,true, 15 * 480));
        DATA_REVIEW_COLUMN_HEADER.add(new HeaderVo("L2名称","lv2CnName", CellType.STRING,true,12 * 480));
        DATA_REVIEW_COLUMN_HEADER.add(new HeaderVo("L2编码", "lv2Code",CellType.STRING,true, 15 * 480));
        DATA_REVIEW_COLUMN_HEADER.add(new HeaderVo("L3名称","lv3CnName", CellType.STRING,true,12 * 480));
        DATA_REVIEW_COLUMN_HEADER.add(new HeaderVo("L3编码", "lv3Code",CellType.STRING,true, 15 * 480));
        DATA_REVIEW_COLUMN_HEADER.add(new HeaderVo("L3.5名称","lv4CnName", CellType.STRING,true,12 * 480));
        DATA_REVIEW_COLUMN_HEADER.add(new HeaderVo("L3.5编码", "lv4Code",CellType.STRING,true, 15 * 480));
        DATA_REVIEW_COLUMN_HEADER.add(new HeaderVo("SPART编码", "spartCode",CellType.STRING,true, 15 * 480));
        DATA_REVIEW_COLUMN_HEADER.add(new HeaderVo("合同号", "hwContractNum",CellType.STRING,true, 15 * 480));
        DATA_REVIEW_COLUMN_HEADER.add(new HeaderVo("起始期", "beginDate",CellType.STRING,true, 15 * 480));
        DATA_REVIEW_COLUMN_HEADER.add(new HeaderVo("终止期", "endDate",CellType.STRING,true, 15 * 480));
        DATA_REVIEW_COLUMN_HEADER.add(new HeaderVo("修改理由", "modifyReasonM",CellType.STRING,true, 15 * 480));
        DATA_REVIEW_COLUMN_HEADER.add(new HeaderVo("撤销理由", "modifyReasonR",CellType.STRING,true, 15 * 480));
    }
}