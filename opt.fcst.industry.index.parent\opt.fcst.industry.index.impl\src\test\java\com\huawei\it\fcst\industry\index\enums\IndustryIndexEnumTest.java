package com.huawei.it.fcst.industry.index.enums;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 * @since 2023/5/9
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class IndustryIndexEnumTest {

    @Test
    public void getViewFlag() {
        Assert.assertNotNull(IndustryIndexEnum.getViewFlagProfits("3"));
    }

    @Test
    public void testMethod() {
        IndustryIndexEnum.VIEW_FLAG_U.VIEW1.setValue("value");
        IndustryIndexEnum.VIEW_FLAG_U.VIEW1.setDesc("desc");
        IndustryIndexEnum.DataType.CATE.setValue("value");
        IndustryIndexEnum.DataType.CATE.getValue();
        IndustryIndexEnum.DataType.CATE.getDesc();
        IndustryIndexEnum.getCaliberFlag("R").setDesc("desc");
        IndustryIndexEnum.getCaliberFlag("R").setValue("value");
        IndustryIndexEnum.getGranularityType("U").setDesc("desc");
        IndustryIndexEnum.getGranularityType("U").setValue("value");
        IndustryIndexEnum.getViewFlagProfits("a");
        IndustryIndexEnum.getCaliberFlag("a");
        IndustryIndexEnum.getGranularityType("a");
        IndustryIndexEnum.VIEW_FLAG_U.VIEW1.getDesc();
        IndustryIndexEnum.VIEW_FLAG_P.VIEW1.setValue("value");
        IndustryIndexEnum.VIEW_FLAG_P.VIEW1.setDesc("desc");
        IndustryIndexEnum.VIEW_FLAG_P.VIEW1.getDesc();
        IndustryIndexEnum.DataType.CATE.setDesc("desc");

        Assert.assertNull(null);
    }
}