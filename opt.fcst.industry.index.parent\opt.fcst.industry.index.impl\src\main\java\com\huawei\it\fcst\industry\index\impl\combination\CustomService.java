/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.combination;

import com.huawei.it.fcst.industry.index.constant.Constant;
import com.huawei.it.fcst.industry.index.dao.IDmFocCustomCombDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocCustomCombTempDao;
import com.huawei.it.fcst.industry.index.enums.IndustryConst;
import com.huawei.it.fcst.industry.index.enums.IndustryIndexEnum;
import com.huawei.it.fcst.industry.index.vo.combination.CombTransformVO;
import com.huawei.it.fcst.industry.index.vo.combination.CombinationVO;
import com.huawei.it.fcst.industry.index.vo.combination.DmCustomCombVO;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Named;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * CustomService Class
 *
 * <AUTHOR>
 * @since 2023/8/8
 */
@Named("customService")
@JalorResource(code = "customService", desc = "汇总组合页面操作")
public class CustomService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CustomService.class);

    private static final String SUCCESS = "SUCCESS";

    private static final String SUCCESS_STATUS = "1";

    private static final String PAGE_FLAG_ALL = "ALL";

    private static final String ENABLE_FLAG_Y = "Y";

    private static final String ENABLE_FLAG_N = "N";

    private static Map<String, String> allCustomPageFlag = new HashMap<>(4);

    static {
        allCustomPageFlag.put("ANNUAL", "MONTH");
        allCustomPageFlag.put("MONTH", "ANNUAL");
        allCustomPageFlag.put("ALL_ANNUAL", "ALL_MONTH");
        allCustomPageFlag.put("ALL_MONTH", "ALL_ANNUAL");
    }

    @Autowired
    private IDmFocCustomCombDao dmFocCustomCombDao;

    @Autowired
    private IDmFocCustomCombTempDao dmFocCustomCombTempDao;

    @Autowired
    private CustomCommonService customCommonService;

    @Autowired
    private AsyncCustomService asyncCustomService;

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void createCustom(List<DmCustomCombVO> customVOList, CombTransformVO combTransformVO, List<DmCustomCombVO> otherCustomVOList) throws CommonApplicationException, ExecutionException, InterruptedException {
        // 插入数据
        LOGGER.info("当前页面的长度:{}", customVOList.size());
        String costType = combTransformVO.getCostType();
        String industryOrg = combTransformVO.getIndustryOrg();
        insertCustomRecursion(customVOList, costType, industryOrg,0L, 500L);
        // 插入另一个页面的数据
        LOGGER.info("另一个页面的长度:{}", otherCustomVOList.size());
        if (CollectionUtils.isNotEmpty(otherCustomVOList)) {
            insertCustomRecursion(otherCustomVOList, costType, industryOrg, 0L, 500L);
        }
        LOGGER.info("开始调用函数刷新结果表");
        // 调用函数
        callCombFunction(combTransformVO);
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public boolean callCombFunction(CombTransformVO combTransformVO) throws CommonApplicationException, ExecutionException, InterruptedException {

        if (Constant.StrEnum.ICT_ORG.getValue().equals(combTransformVO.getIndustryOrg())) {
            combTransformVO.setIndustryOrg("I");
        } else if (Constant.StrEnum.ENERGY_ORG.getValue().equals(combTransformVO.getIndustryOrg())) {
            combTransformVO.setIndustryOrg("E");
        } else {
            combTransformVO.setIndustryOrg("IAS");
        }
        // 调用函数
        LOGGER.info("函数开始,组织:{},成本类型：{},颗粒度：{},组合id:{},版本:{},月度版本:{},页面标识:{}", combTransformVO.getIndustryOrg(),combTransformVO.getCostType(),combTransformVO.getGranularityType(),combTransformVO.getCustomId(),combTransformVO.getVersionId(),combTransformVO.getMonthVersionId(),combTransformVO.getPageFlag());
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(combTransformVO.getCostType())) {
            // 采购成本
            return purchaseCostTypeFunction(combTransformVO);
        } else {
            // 制造成本
            return manufactureCostTypeFunction(combTransformVO);
        }
    }

    public boolean purchaseCostTypeFunction(CombTransformVO combTransformVO) throws CommonApplicationException, ExecutionException, InterruptedException {
        String pageFlag = combTransformVO.getPageFlag();
        if (pageFlag.contains(PAGE_FLAG_ALL)) {
            // 如果同步，两个函数都要调用
            Future<Boolean> customAnnualFlag = asyncCustomService.callCustomAnnual(combTransformVO);

            Future<Boolean> customMonthFlag = asyncCustomService.callCustomMonth(combTransformVO);
            while (true) {
                if (customAnnualFlag.isDone() && customMonthFlag.isDone()) {
                    break;
                }
            }
            return customAnnualFlag.get() && customMonthFlag.get();
        } else if ("ANNUAL".equals(pageFlag)) {
            Future<Boolean> customAnnualFlag = asyncCustomService.callCustomAnnual(combTransformVO);
            while (true) {
                if (customAnnualFlag.isDone()) {
                    break;
                }
            }
            return customAnnualFlag.get();
        } else {
            Future<Boolean> customMonthFlag = asyncCustomService.callCustomMonth(combTransformVO);
            while (true) {
                if (customMonthFlag.isDone()) {
                    break;
                }
            }
            return customMonthFlag.get();
        }
    }

    public boolean manufactureCostTypeFunction(CombTransformVO combTransformVO) throws CommonApplicationException, ExecutionException, InterruptedException {
        String pageFlag = combTransformVO.getPageFlag();
        if (pageFlag.contains(PAGE_FLAG_ALL)) {
            // 如果同步，两个函数都要调用
            Future<Boolean> customAnnualFlag = asyncCustomService.callMadeCustomAnnual(combTransformVO);
            Future<Boolean> customMonthFlag = asyncCustomService.callMadeCustomMonth(combTransformVO);
            while (true) {
                if (customAnnualFlag.isDone() && customMonthFlag.isDone()) {
                    break;
                }
            }
            return customAnnualFlag.get() && customMonthFlag.get();
        } else if ("ANNUAL".equals(pageFlag)) {
            Future<Boolean> customAnnualFlag = asyncCustomService.callMadeCustomAnnual(combTransformVO);
            while (true) {
                if (customAnnualFlag.isDone()) {
                    break;
                }
            }
            return customAnnualFlag.get();
        } else {
            Future<Boolean> customMonthFlag = asyncCustomService.callMadeCustomMonth(combTransformVO);
            while (true) {
                if (customMonthFlag.isDone()) {
                    break;
                }
            }
            return customMonthFlag.get();
        }
    }

    /**
     * 递归插入数据
     *
     * @param customVOList 组合
     * @param start        当前页
     * @param limit        限制
     */
    private void insertCustomRecursion(List<DmCustomCombVO> customVOList, String costType, String industryOrg, Long start, Long limit) {
        List<DmCustomCombVO> customSubList =
                customVOList.stream().skip(start).limit(limit).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(customSubList)) {
            return;
        }
        // 插入数据
        if (Constant.StrEnum.ICT_ORG.getValue().equals(industryOrg)) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(costType)) {
                dmFocCustomCombDao.createCustomcombList(customSubList);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(costType)) {
                dmFocCustomCombDao.createManufactureCustomcombList(customSubList);
            }
        }
        if (Constant.StrEnum.ENERGY_ORG.getValue().equals(industryOrg)) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(costType)) {
                dmFocCustomCombDao.createEnergyCustomcombList(customSubList);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(costType)) {
                dmFocCustomCombDao.createEnergyManufactureCustomcombList(customSubList);
            }
        }
        if (Constant.StrEnum.IAS_ORG.getValue().equals(industryOrg)) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(costType)) {
                dmFocCustomCombDao.createIasCustomcombList(customSubList);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(costType)) {
                dmFocCustomCombDao.createIasManufactureCustomcombList(customSubList);
            }
        }
        insertCustomRecursion(customVOList, costType, industryOrg, start + limit, limit);
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updateCombList(CombinationVO combinationVO, CombTransformVO combTransformVO) throws CommonApplicationException, ExecutionException, InterruptedException {
        Long originCustomId = combinationVO.getCustomId();
        // 根据id获取临时表的数据
        List<DmCustomCombVO> tempCustomCombList = new ArrayList<>();
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(combinationVO.getCostType())) {
            tempCustomCombList = dmFocCustomCombTempDao.getTempCustomCombList(combinationVO);
        } else {
            tempCustomCombList = dmFocCustomCombTempDao.getTempManufactureCustomCombList(combinationVO);
        }
        combinationVO.setCustomVOList(tempCustomCombList);
        List<String> subEnableFlagList = tempCustomCombList.stream().map(DmCustomCombVO::getSubEnableFlag).distinct().collect(Collectors.toList());
        // 原id的数据
        List<DmCustomCombVO> dmCustomCombList = dmFocCustomCombDao.getCustomCombListByPage(combinationVO);
        String userId = combinationVO.getUserId();
        String roleId = combinationVO.getRoleId();
        // 如果原数据就是不同步的，编辑时选择不同步，直接更新该id下的数据
        if (!combinationVO.getPageFlag().contains(PAGE_FLAG_ALL)) {
            if (!combinationVO.getOldPageFlag().contains(PAGE_FLAG_ALL)) {
                Timestamp creationDate = dmCustomCombList.get(0).getCreationDate();
                Timestamp lastUpdatedDate = dmCustomCombList.get(0).getLastUpdateDate();
                // 更新操作：直接删除，重新插入
                dmFocCustomCombDao.deleteCustomList(combinationVO);
                // 判断是否全部失效
                tempCustomCombList.forEach(comb -> {
                    comb.setCustomId(originCustomId);
                    comb.setCustomCnName(combinationVO.getCustomCnName());
                    comb.setLv0ProdListCode(combinationVO.getLv0ProdListCode());
                    comb.setLv0ProdListCnName(combinationVO.getLv0ProdListCnName());
                    comb.setViewFlag(combinationVO.getViewFlag());
                    comb.setGranularityType(combinationVO.getGranularityType());
                    comb.setCaliberFlag(combinationVO.getCaliberFlag());
                    comb.setOverseaFlag(combinationVO.getOverseaFlag());
                    comb.setPageFlag(combinationVO.getPageFlag());
                    comb.setIsSeparate(combinationVO.getIsSeparate());
                    comb.setRoleId(roleId);
                    comb.setUserId(userId);
                    comb.setCreatedBy(userId);
                    comb.setCreationDate(creationDate);
                    comb.setLastUpdateDate(lastUpdatedDate);
                    comb.setEnableFlag(ENABLE_FLAG_Y);
                    if (subEnableFlagList.contains(ENABLE_FLAG_N)) {
                        comb.setEnableFlag(ENABLE_FLAG_N);
                    }
                    comb.setLastUpdatedBy(userId);
                });
                insertCustomRecursion(tempCustomCombList, combinationVO.getCostType(), combinationVO.getIndustryOrg(), 0L, 500L);
            } else {
                // 如果原数据就是同步的，编辑时不选择同步，那么更新两个页面的记录
                dataSyncEditNoSync(combinationVO, userId, roleId, originCustomId, subEnableFlagList, dmCustomCombList);
            }
        } else {
            // 勾选同步,但原数据不是同步的，更新原pageFlag为ALL_ANNUAL或者ALL_MONTH，新增另一个页面的ALL_数据
            // 勾选同步,原数据是同步的，更新数据即可
            editSync(combinationVO, userId, roleId, originCustomId, subEnableFlagList, dmCustomCombList);
        }
        callCombFunction(combTransformVO);
    }

    private void dataSyncEditNoSync(CombinationVO combinationVO, String userId, String roleId, Long originCustomId, List<String> subEnableFlagList, List<DmCustomCombVO> dmCustomCombList) {

        String otherPageFlag = allCustomPageFlag.get(combinationVO.getPageFlag());
        // 如果原数据就是同步的，编辑时不选择同步，那么先更新另一个页面的page_flag即可，再更新本页面的记录，另一个页面记录和原记录id一致
        CombinationVO combination = new CombinationVO();
        BeanUtils.copyProperties(combinationVO, combination);
        combination.setOldPageFlag(allCustomPageFlag.get(combinationVO.getOldPageFlag()));
        combination.setPageFlag(otherPageFlag);
        combination.setIsSeparate("Y");
        dmFocCustomCombDao.updateCustomPageFlag(combination);

        // 更新操作：删除，重新插入本页面的记录
        dmFocCustomCombDao.deleteCustomList(combinationVO);
        List<DmCustomCombVO> customList = combinationVO.getCustomVOList();
        customList.forEach(customVO -> {
            customVO.setCustomId(originCustomId);
            customVO.setCustomCnName(combinationVO.getCustomCnName());
            customVO.setLv0ProdListCode(combinationVO.getLv0ProdListCode());
            customVO.setLv0ProdListCnName(combinationVO.getLv0ProdListCnName());
            customVO.setViewFlag(combinationVO.getViewFlag());
            customVO.setGranularityType(combinationVO.getGranularityType());
            customVO.setCaliberFlag(combinationVO.getCaliberFlag());
            customVO.setOverseaFlag(combinationVO.getOverseaFlag());
            customVO.setUserId(userId);
            customVO.setRoleId(roleId);
            customVO.setCreatedBy(userId);
            customVO.setIsSeparate("Y");
            customVO.setPageFlag(combinationVO.getPageFlag());
            // 是否全部失效
            customVO.setEnableFlag(ENABLE_FLAG_Y);
            if (subEnableFlagList.contains(ENABLE_FLAG_N)) {
                customVO.setEnableFlag(ENABLE_FLAG_N);
            }
            if (CollectionUtils.isNotEmpty(dmCustomCombList)) {
                Timestamp creationDate = dmCustomCombList.get(0).getCreationDate();
                Timestamp lastUpdatedDate = dmCustomCombList.get(0).getLastUpdateDate();
                customVO.setCreationDate(creationDate);
                customVO.setLastUpdateDate(lastUpdatedDate);
            } else {
                customVO.setCreationDate(new Timestamp(System.currentTimeMillis()));
                customVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
            }
            customVO.setLastUpdatedBy(userId);
        });
        insertCustomRecursion(customList, combinationVO.getCostType(), combinationVO.getIndustryOrg(), 0L, 500L);
    }

    private void editSync(CombinationVO combinationVO, String userId, String roleId, Long originCustomId, List<String> subEnableFlagList, List<DmCustomCombVO> dmCustomCombList) throws CommonApplicationException {
        List<DmCustomCombVO> customList = combinationVO.getCustomVOList();
        List<DmCustomCombVO> customCombList = new ArrayList<>();
        customCombList.addAll(customList);
        String otherPageFlag = allCustomPageFlag.get(combinationVO.getPageFlag());
        String otherOldPageFlag = allCustomPageFlag.get(combinationVO.getOldPageFlag());
        // 原数据是不同步的，更新原数据的pageflag，新增另一个页面的数据
        // 更新操作：直接删除，重新插入
        dmFocCustomCombDao.deleteCustomList(combinationVO);
        originDataInsert(combinationVO, userId, roleId, originCustomId, subEnableFlagList, dmCustomCombList, customList);
        // 筛选出与另一个页面存在的数据，进行同步
        combinationVO.setExpandFlag("Y");
        List<DmCustomCombVO> otherCustomList = customCommonService.filterAnotherPageData(combinationVO, customCombList);

        if (CollectionUtils.isNotEmpty(otherCustomList)) {
            // 删除另一个页面ALL_的数据，再新增；如果另一个页面本来就没有，则删除空数据
            CombinationVO combination = new CombinationVO();
            combination.setOldPageFlag(otherOldPageFlag);
            combination.setIndustryOrg(combinationVO.getIndustryOrg());
            combination.setCustomId(combinationVO.getCustomId());
            combination.setCostType(combinationVO.getCostType());
            dmFocCustomCombDao.deleteCustomList(combination);
            otherCustomList.forEach(comb -> {
                comb.setCustomId(originCustomId);
                comb.setCustomCnName(combinationVO.getCustomCnName());
                comb.setLv0ProdListCode(combinationVO.getLv0ProdListCode());
                comb.setLv0ProdListCnName(combinationVO.getLv0ProdListCnName());
                comb.setViewFlag(combinationVO.getViewFlag());
                comb.setGranularityType(combinationVO.getGranularityType());
                comb.setCaliberFlag(combinationVO.getCaliberFlag());
                comb.setOverseaFlag(combinationVO.getOverseaFlag());
                comb.setPageFlag(otherPageFlag);
                comb.setIsSeparate(combinationVO.getIsSeparate());
                comb.setUserId(userId);
                comb.setRoleId(roleId);
                comb.setCreatedBy(userId);
                // 判断是否全部失效或有效
                comb.setEnableFlag(ENABLE_FLAG_Y);
                if (subEnableFlagList.contains(ENABLE_FLAG_N)) {
                    comb.setEnableFlag(ENABLE_FLAG_N);
                }
                if (CollectionUtils.isNotEmpty(dmCustomCombList)) {
                    Timestamp creationDate = dmCustomCombList.get(0).getCreationDate();
                    Timestamp lastUpdatedDate = dmCustomCombList.get(0).getLastUpdateDate();
                    comb.setCreationDate(creationDate);
                    comb.setLastUpdateDate(lastUpdatedDate);
                } else {
                    comb.setCreationDate(new Timestamp(System.currentTimeMillis()));
                    comb.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                }
                comb.setLastUpdatedBy(userId);
            });
            insertCustomRecursion(otherCustomList, combinationVO.getCostType(), combinationVO.getIndustryOrg(), 0L, 500L);
        }
    }

    private void originDataInsert(CombinationVO combinationVO, String userId, String roleId, Long originCustomId, List<String> subEnableFlagList, List<DmCustomCombVO> dmCustomCombList, List<DmCustomCombVO> customList) {
        List<DmCustomCombVO> oneCustomList = customList.stream().map(comb -> {
            DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
            BeanUtils.copyProperties(comb, dmCustomCombVO);
            dmCustomCombVO.setCustomId(originCustomId);
            dmCustomCombVO.setCustomCnName(combinationVO.getCustomCnName());
            dmCustomCombVO.setLv0ProdListCode(combinationVO.getLv0ProdListCode());
            dmCustomCombVO.setLv0ProdListCnName(combinationVO.getLv0ProdListCnName());
            dmCustomCombVO.setViewFlag(combinationVO.getViewFlag());
            dmCustomCombVO.setGranularityType(combinationVO.getGranularityType());
            dmCustomCombVO.setCaliberFlag(combinationVO.getCaliberFlag());
            dmCustomCombVO.setOverseaFlag(combinationVO.getOverseaFlag());
            dmCustomCombVO.setPageFlag(combinationVO.getPageFlag());
            dmCustomCombVO.setIsSeparate(combinationVO.getIsSeparate());
            dmCustomCombVO.setUserId(userId);
            dmCustomCombVO.setRoleId(roleId);
            dmCustomCombVO.setCreatedBy(userId);
            // 判断是否全部失效或有效
            dmCustomCombVO.setEnableFlag(ENABLE_FLAG_Y);
            if (subEnableFlagList.contains(ENABLE_FLAG_N)) {
                dmCustomCombVO.setEnableFlag(ENABLE_FLAG_N);
            }
            if (CollectionUtils.isNotEmpty(dmCustomCombList)) {
                Timestamp creationDate = dmCustomCombList.get(0).getCreationDate();
                Timestamp lastUpdatedDate = dmCustomCombList.get(0).getLastUpdateDate();
                dmCustomCombVO.setCreationDate(creationDate);
                dmCustomCombVO.setLastUpdateDate(lastUpdatedDate);
            } else {
                dmCustomCombVO.setCreationDate(new Timestamp(System.currentTimeMillis()));
                dmCustomCombVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
            }
            dmCustomCombVO.setLastUpdatedBy(userId);
            return dmCustomCombVO;
        }).collect(Collectors.toList());
        insertCustomRecursion(oneCustomList, combinationVO.getCostType(), combinationVO.getIndustryOrg(), 0L, 500L);
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void asyncInitFlag(List<DmCustomCombVO> customCombList, List<DmCustomCombVO> manufactureCustomCombList, List<DmCustomCombVO> customCombEnergyList, List<DmCustomCombVO> manufactureCustomCombEnergyList,CombinationVO combinationVO, CombTransformVO combTransformVO) throws InterruptedException {
        if (CollectionUtils.isNotEmpty(customCombList)) {
            List<DmCustomCombVO> annualList = customCombList.stream().filter(custom->custom.getPageFlag().contains("ANNUAL")).collect(Collectors.toList());
            List<DmCustomCombVO> monthList = customCombList.stream().filter(custom->custom.getPageFlag().contains("MONTH")).collect(Collectors.toList());
            combinationVO.setCostType(IndustryIndexEnum.COST_TYPE.P.getValue());
            combinationVO.setTablePreFix(IndustryConst.TABLE_NAME.ICT_TABLE.getValue());
            combinationVO.setIndustryOrg("ICT");
            forAllCostTypeList(annualList, monthList, combinationVO);
        }
        if (CollectionUtils.isNotEmpty(manufactureCustomCombList)) {
            List<DmCustomCombVO> annualList = manufactureCustomCombList.stream().filter(custom->custom.getPageFlag().contains("ANNUAL")).collect(Collectors.toList());
            List<DmCustomCombVO> monthList = manufactureCustomCombList.stream().filter(custom->custom.getPageFlag().contains("MONTH")).collect(Collectors.toList());
            combinationVO.setCostType(IndustryIndexEnum.COST_TYPE.M.getValue());
            combinationVO.setTablePreFix(IndustryConst.TABLE_NAME.ICT_TABLE.getValue());
            combinationVO.setIndustryOrg("ICT");
            forAllCostTypeList(annualList, monthList, combinationVO);
        }
        if (CollectionUtils.isNotEmpty(customCombEnergyList)) {
            List<DmCustomCombVO> annualList = customCombEnergyList.stream().filter(custom->custom.getPageFlag().contains("ANNUAL")).collect(Collectors.toList());
            List<DmCustomCombVO> monthList = customCombEnergyList.stream().filter(custom->custom.getPageFlag().contains("MONTH")).collect(Collectors.toList());
            combinationVO.setCostType(IndustryIndexEnum.COST_TYPE.P.getValue());
            combinationVO.setTablePreFix(IndustryConst.TABLE_NAME.ENERGY_TABLE.getValue());
            combinationVO.setIndustryOrg("ENERGY");
            forAllCostTypeList(annualList, monthList, combinationVO);
        }
        if (CollectionUtils.isNotEmpty(manufactureCustomCombEnergyList)) {
            List<DmCustomCombVO> annualList = manufactureCustomCombEnergyList.stream().filter(custom->custom.getPageFlag().contains("ANNUAL")).collect(Collectors.toList());
            List<DmCustomCombVO> monthList = manufactureCustomCombEnergyList.stream().filter(custom->custom.getPageFlag().contains("MONTH")).collect(Collectors.toList());
            combinationVO.setCostType(IndustryIndexEnum.COST_TYPE.M.getValue());
            combinationVO.setTablePreFix(IndustryConst.TABLE_NAME.ENERGY_TABLE.getValue());
            combinationVO.setIndustryOrg("ENERGY");
            forAllCostTypeList(annualList, monthList, combinationVO);
        }
        List<DmCustomCombVO> customCombIasList = combTransformVO.getCustomCombIasList();
        List<DmCustomCombVO> customCombIasMadeList = combTransformVO.getCustomCombIasMadeList();
        if (CollectionUtils.isNotEmpty(customCombIasList)) {
            List<DmCustomCombVO> annualList = customCombIasList.stream().filter(custom->custom.getPageFlag().contains("ANNUAL")).collect(Collectors.toList());
            List<DmCustomCombVO> monthList = customCombIasList.stream().filter(custom->custom.getPageFlag().contains("MONTH")).collect(Collectors.toList());
            combinationVO.setCostType(IndustryIndexEnum.COST_TYPE.P.getValue());
            combinationVO.setTablePreFix(IndustryConst.TABLE_NAME.IAS_TABLE.getValue());
            combinationVO.setIndustryOrg("IAS");
            forAllCostTypeList(annualList, monthList, combinationVO);
        }
        if (CollectionUtils.isNotEmpty(customCombIasMadeList)) {
            List<DmCustomCombVO> annualList = customCombIasMadeList.stream().filter(custom->custom.getPageFlag().contains("ANNUAL")).collect(Collectors.toList());
            List<DmCustomCombVO> monthList = customCombIasMadeList.stream().filter(custom->custom.getPageFlag().contains("MONTH")).collect(Collectors.toList());
            combinationVO.setCostType(IndustryIndexEnum.COST_TYPE.M.getValue());
            combinationVO.setTablePreFix(IndustryConst.TABLE_NAME.IAS_TABLE.getValue());
            combinationVO.setIndustryOrg("IAS");
            forAllCostTypeList(annualList, monthList, combinationVO);
        }
    }

    public void forAllCostTypeList(List<DmCustomCombVO> annualList, List<DmCustomCombVO> monthList, CombinationVO combinationVO) throws InterruptedException {
        List<DmCustomCombVO> generalAnnualList = annualList.stream().filter(custom -> custom.getGranularityType().equals("U")).collect(Collectors.toList());
        List<DmCustomCombVO> profitAnnualList = annualList.stream().filter(custom -> custom.getGranularityType().equals("P")).collect(Collectors.toList());
        List<DmCustomCombVO> dimensionAnnualList = annualList.stream().filter(custom -> custom.getGranularityType().equals("D")).collect(Collectors.toList());

        List<DmCustomCombVO> generalMonthList = monthList.stream().filter(custom -> custom.getGranularityType().equals("U")).collect(Collectors.toList());
        List<DmCustomCombVO> profitMonthList = monthList.stream().filter(custom -> custom.getGranularityType().equals("P")).collect(Collectors.toList());
        List<DmCustomCombVO> dimensionMonthList = monthList.stream().filter(custom -> custom.getGranularityType().equals("D")).collect(Collectors.toList());

        // 年度-通用
        Future<Boolean> univalAnnualFlag = asyncCustomService.granularityTypePageCondition(generalAnnualList, combinationVO, "U_ANNUAL");
        // 年度-盈利
        Future<Boolean> profitAnnualFlag = asyncCustomService.granularityTypePageCondition(profitAnnualList, combinationVO, "P_ANNUAL");
        // 年度-量纲
        Future<Boolean> dimesionAnnualFlag = asyncCustomService.granularityTypePageCondition(dimensionAnnualList, combinationVO, "D_ANNUAL");
        // 月度-通用
        Future<Boolean> univalMonthFlag = asyncCustomService.granularityTypePageCondition(generalMonthList, combinationVO, "U_MONTH");
        // 月度-盈利
        Future<Boolean> profitMonthFlag = asyncCustomService.granularityTypePageCondition(profitMonthList, combinationVO, "P_MONTH");
        // 月度-量纲
        Future<Boolean> dimensionMonthFlag = asyncCustomService.granularityTypePageCondition(dimensionMonthList, combinationVO, "D_MONTH");
        while (true) {
            boolean annualFlag = univalAnnualFlag.isDone() && profitAnnualFlag.isDone() && dimesionAnnualFlag.isDone();
            boolean monthFlag = univalMonthFlag.isDone() && profitMonthFlag.isDone() && dimensionMonthFlag.isDone();
            if (annualFlag && monthFlag) {
                break;
            }
        }
    }
}
