/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;

/**
 * DmFocVarifyTaskVO Class
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DmFocVarifyTaskVO {

    private Long id;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 任务状态，PROCESSING表示执行中，SUCCESS表示执行成功，FAIL表示执行失败
     */
    private String status;

    /**
     * 会计期（YYYYMM）
     */
    private Integer periodId;

    /**
     * 创建时间
     */
    private Timestamp creationDate;

    /**
     * 更新时间
     */
    private Timestamp lastUpdateDate;

    /**
     * 组合状态
     */
    private String combStatus;

    /**
     * 业务标识(指数切换基期）
     */
    private String taskType;

    private String minutes;
}