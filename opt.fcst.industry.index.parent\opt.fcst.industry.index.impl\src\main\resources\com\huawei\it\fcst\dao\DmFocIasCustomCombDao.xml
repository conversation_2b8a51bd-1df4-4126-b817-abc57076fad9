<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IDmFocIasCustomCombDao">
    <resultMap type="com.huawei.it.fcst.industry.index.vo.combination.DmCustomCombVO" id="customResultMap">
        <result property="pageFlag" column="page_flag"/>
        <result property="lv0ProdRndTeamCode" column="LV0_PROD_RND_TEAM_CODE" />
        <result property="lv0ProdRdTeamCnName" column="LV0_PROD_RD_TEAM_CN_NAME" />
        <result property="lv1ProdRndTeamCode" column="LV1_PROD_RND_TEAM_CODE" />
        <result property="lv1ProdRdTeamCnName" column="LV1_PROD_RD_TEAM_CN_NAME" />
        <result property="lv2ProdRndTeamCode" column="LV2_PROD_RND_TEAM_CODE" />
        <result property="lv2ProdRdTeamCnName" column="LV2_PROD_RD_TEAM_CN_NAME" />
        <result property="lv3ProdRndTeamCode" column="LV3_PROD_RND_TEAM_CODE" />
        <result property="lv3ProdRdTeamCnName" column="LV3_PROD_RD_TEAM_CN_NAME" />
        <result property="lv4ProdRndTeamCode" column="LV4_PROD_RND_TEAM_CODE" />
        <result property="lv4ProdRdTeamCnName" column="LV4_PROD_RD_TEAM_CN_NAME" />
        <result property="l1Name" column="l1_name"/>
        <result property="l2Name" column="l2_name"/>
        <result property="coaCode" column="coa_code"/>
        <result property="coaCnName" column="coa_cn_name"/>
        <result property="dimensionCode" column="dimension_code"/>
        <result property="dimensionCnName" column="dimension_cn_name"/>
        <result property="dimensionSubCategoryCode" column="dimension_subcategory_code"/>
        <result property="dimensionSubCategoryCnName" column="dimension_subcategory_cn_name"/>
        <result property="dimensionSubDetailCode" column="dimension_sub_detail_code"/>
        <result property="dimensionSubDetailCnName" column="dimension_sub_detail_cn_name"/>
        <result property="groupLevel" column="group_level"/>
        <result property="groupCnName" column="group_cn_name"/>
        <result property="groupCode" column="group_code"/>
        <result property="userId" column="user_id"/>
        <result property="roleId" column="role_id"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="caliberFlag" column="caliber_flag"/>
        <result property="granularityType" column="granularity_type"/>
        <result property="overseaFlag" column="oversea_flag"/>
        <result property="enableFlag" column="enable_flag"/>
        <result property="subEnableFlag" column="sub_enable_flag"/>
        <result property="lv0ProdListCode" column="lv0_prod_list_code"/>
        <result property="lv0ProdListCnName" column="lv0_prod_list_cn_name"/>
        <result property="l3CegCnName" column="TOP_L3_CEG_SHORT_CN_NAME"/>
        <result property="l3CegCode" column="top_l3_ceg_code"/>
        <result property="l3CegCnName" column="top_l3_ceg_cn_name"/>
        <result property="l3CegCnName" column="L3_CEG_SHORT_CN_NAME"/>
        <result property="l3CegCode" column="L3_CEG_CODE"/>
        <result property="l3CegCnName" column="l3_ceg_cn_name"/>
        <result property="l4CegCnName" column="TOP_L4_CEG_SHORT_CN_NAME"/>
        <result property="l4CegCode" column="TOP_L4_CEG_CODE"/>
        <result property="l4CegCnName" column="top_l4_ceg_cn_name"/>
        <result property="l4CegCnName" column="L4_CEG_SHORT_CN_NAME"/>
        <result property="l4CegCode" column="L4_CEG_CODE"/>
        <result property="l4CegCnName" column="l4_ceg_cn_name"/>
        <result property="categoryCode" column="category_code"/>
        <result property="categoryCnName" column="category_cn_name"/>
        <result property="customId" column="custom_id"/>
        <result property="customCnName" column="custom_cn_name"/>
        <result property="parentCode" column="parent_code"/>
        <result property="isSeparate" column="is_separate"/>
        <result property="creationDate" column="creation_date"/>
        <result property="createdBy" column="created_by"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="connectCode" column="connectCode"/>
        <result property="connectParentCode" column="connectParentCode"/>
        <result property="shippingObjectCode" column="shipping_object_code"/>
        <result property="shippingObjectCnName" column="shipping_object_cn_name"/>
        <result property="manufactureObjectCode" column="manufacture_object_code"/>
        <result property="manufactureObjectCnName" column="manufacture_object_cn_name"/>
    </resultMap>

    <select id="getIasCustomCombList" resultMap="customResultMap">
        select custom_cn_name,custom_id,page_flag,LV0_PROD_RND_TEAM_CODE, LV0_PROD_RD_TEAM_CN_NAME,
        LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME,
        LV2_PROD_RND_TEAM_CODE,LV2_PROD_RD_TEAM_CN_NAME,LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME,LV4_PROD_RND_TEAM_CODE,LV4_PROD_RD_TEAM_CN_NAME,l1_name,l2_name,
        dimension_code, dimension_cn_name, dimension_subcategory_code, dimension_subcategory_cn_name,
        dimension_sub_detail_code,spart_code,spart_cn_name,
        dimension_sub_detail_cn_name,l3_ceg_code,l3_ceg_short_cn_name,l4_ceg_code,l4_ceg_short_cn_name,category_code,category_cn_name,
        group_level,group_code,group_cn_name,user_id,role_id,view_flag,caliber_flag,granularity_type,oversea_flag,
        enable_flag,sub_enable_flag,lv0_prod_list_code,lv0_prod_list_cn_name,parent_code,is_separate,creation_date,last_update_date,created_by,last_updated_by,
        concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
        DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
        DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' ||LV3_PROD_RND_TEAM_CODE ),
        DECODE(LV4_PROD_RND_TEAM_CODE,'','','#*#' ||LV4_PROD_RND_TEAM_CODE ),
        DECODE(l1_name,'','','#*#' ||l1_name ),
        DECODE(l2_name,'','','#*#' ||l2_name ),
        DECODE(dimension_code,'','','#*#' ||dimension_code ),
        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_subcategory_code ),
        DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_sub_detail_code ),
        DECODE(spart_code,'','','#*#' ||spart_code ),
        DECODE(l3_ceg_code,'','','#*#' ||l3_ceg_code ),
        DECODE(l4_ceg_code,'','','#*#' ||l4_ceg_code ),
        DECODE(category_code,'','','#*#' ||category_code)
        ) AS connectCode
        from fin_dm_opt_foi.dm_foc_ias_custom_comb_d where del_flag ='N' and enable_flag='Y'
        and user_id = #{userId} and role_id = #{roleId}
        <if test='customId != null and customId!=""'>
            and custom_id = #{customId}
        </if>
    </select>

    <select id="getManufactureIasCustomCombList" resultMap="customResultMap">
        select custom_cn_name,custom_id,page_flag,LV0_PROD_RND_TEAM_CODE, LV0_PROD_RD_TEAM_CN_NAME,
        LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME,
        LV2_PROD_RND_TEAM_CODE,LV2_PROD_RD_TEAM_CN_NAME,LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME,LV4_PROD_RND_TEAM_CODE,LV4_PROD_RD_TEAM_CN_NAME,
        l1_name,l2_name,dimension_code, dimension_cn_name, dimension_subcategory_code, dimension_subcategory_cn_name,
        dimension_sub_detail_code,spart_code,spart_cn_name,
        dimension_sub_detail_cn_name,shipping_object_code,shipping_object_cn_name,manufacture_object_code,manufacture_object_cn_name,
        group_level,group_code,group_cn_name,user_id,role_id,view_flag,caliber_flag,granularity_type,oversea_flag,
        enable_flag,sub_enable_flag,lv0_prod_list_code,lv0_prod_list_cn_name,parent_code,is_separate,creation_date,last_update_date,created_by,last_updated_by,
        concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
        DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
        DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' ||LV3_PROD_RND_TEAM_CODE ),
        DECODE(LV4_PROD_RND_TEAM_CODE,'','','#*#' ||LV4_PROD_RND_TEAM_CODE ),
        DECODE(l1_name,'','','#*#' ||l1_name ),
        DECODE(l2_name,'','','#*#' ||l2_name ),
        DECODE(dimension_code,'','','#*#' ||dimension_code ),
        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_subcategory_code ),
        DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_sub_detail_code ),
        DECODE(spart_code,'','','#*#' ||spart_code ),
        DECODE(shipping_object_code,'','','#*#' ||shipping_object_code ),
        DECODE(manufacture_object_code,'','','#*#' ||manufacture_object_code )
        ) AS connectCode
        from fin_dm_opt_foi.dm_foc_ias_made_custom_comb_d where del_flag ='N' and enable_flag='Y'
        and user_id = #{userId} and role_id = #{roleId}
        <if test='customId != null and customId!=""'>
            and custom_id = #{customId}
        </if>
    </select>

</mapper>