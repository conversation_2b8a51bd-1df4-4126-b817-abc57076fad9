/*
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.relation.DmDimCatgModlCegIctVO;
import com.huawei.it.fcst.industry.index.vo.relation.DmDimMaterialCodeVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * IDmDimCatgModlCegIctDao
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
public interface IDmDimCatgModlCegIctDao {

    /**
     * Find all DmDimCatgModlCegIctT records.
     *
     * @return list
     */
    List<DmDimCatgModlCegIctVO> findDmDimCatgModlCegIctList(DmDimCatgModlCegIctVO vo);

    List<Map> findExportCatgModlCegIctList(DmDimCatgModlCegIctVO vo);

    List<DmDimMaterialCodeVO> findDimMaterialCodeD (DmDimMaterialCodeVO vo);

    /**
     * Create batch DmDimCatgModlCegIctT record.
     *
     * @param items items
     * @return int
     */
    int createDmDimCatgModlCegIctTList(@Param("list") List<DmDimCatgModlCegIctVO> items, @Param("tablePreFix") String tablePreFix);

    /**
     * Delete DmDimCatgModlCegIctT by versionId.
     *
     * @param versionId Long
     * @return int
     */
    int deleteDmDimCatgModlCegIctT(@Param("versionId")Long versionId,@Param("tablePreFix") String tablePreFix);

    List<DmDimCatgModlCegIctVO> findDimCegCodeD();

    List<Map> findCatgCegIctList(String tablePreFix);

    int updateL3ShortName(DmDimCatgModlCegIctVO cegIctVO);

    List<DmDimCatgModlCegIctVO> findCatgModlCegIctListByShortName(DmDimCatgModlCegIctVO dimCatgModlCegIctVO);

    int updateCateByVo(DmDimCatgModlCegIctVO cegIctVO);

    int updateL3AndL4(DmDimCatgModlCegIctVO cegIctVO);

    int updateL4ShortName(DmDimCatgModlCegIctVO cegIctVO);
}
