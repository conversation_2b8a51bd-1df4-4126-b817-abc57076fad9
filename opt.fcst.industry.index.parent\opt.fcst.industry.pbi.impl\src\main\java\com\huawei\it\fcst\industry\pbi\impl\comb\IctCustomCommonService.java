/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.comb;

import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstDimInfoDao;
import com.huawei.it.fcst.industry.pbi.enums.GroupLevelEnum;
import com.huawei.it.fcst.industry.pbi.enums.IndustryConstEnum;
import com.huawei.it.fcst.industry.pbi.impl.common.DropDownService;
import com.huawei.it.fcst.industry.pbi.impl.common.IctCommonService;
import com.huawei.it.fcst.industry.pbi.utils.FcstIndustryUtil;
import com.huawei.it.fcst.industry.pbi.vo.comb.CommonViewVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstDimInfoVO;
import com.huawei.it.fcst.util.ObjectCopyUtil;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Named;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * IctCustomCommonService Class
 *
 * <AUTHOR>
 * @since 2024/9/13
 */
@Named("ictCustomCommonService")
@JalorResource(code = "ictCustomCommonService", desc = "NEW ICT-汇总组合公共类")
public class IctCustomCommonService {

    private static final Logger LOGGER = LoggerFactory.getLogger(IctCustomCommonService.class);

    @Autowired
    private IDmFcstDimInfoDao dmFcstDimInfoDao;

    @Autowired
    private IctCommonService commonService;

    @Autowired
    private DropDownService dropDownService;

    public List<DmFcstDimInfoVO> filterAnotherPageData(CommonViewVO commonVO, List<DmFcstDimInfoVO> customVOList) {

        CommonViewVO commonViewVO = ObjectCopyUtil.copy(commonVO, CommonViewVO.class);
        if ("Y".equals(commonViewVO.getMainFlag()) && "全选".equals(commonViewVO.getCodeAttributes())) {
            commonViewVO.setCodeAttributes("");
        }
        commonViewVO.setVersionId(commonService.getVersionId(IndustryConstEnum.PAGE_TYPE.ANNUAL.getValue()));
        // 获取月度version_id
        commonViewVO.setMonVersionId(commonService.getVersionId(IndustryConstEnum.PAGE_TYPE.MONTH.getValue()));
        // 根据来源页面，判断另一个页面是否有对应的code，只判断重量级团队
        // 设置传入的list对应的重量级团队的code
        setProdTeamDimesionCode(commonViewVO, customVOList);
        // 查询另一个页面重量级团队
        List<DmFcstDimInfoVO> anotherCustomCombList = getAnotherPageCode(commonViewVO);

        // 传进来的，不在另一个页面中的code
        List<DmFcstDimInfoVO> diffList = customVOList.stream().filter(custom -> !anotherCustomCombList.stream().map(another -> {
            return another.getConnectCode();
        }).collect(Collectors.toList()).contains(custom.getConnectCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(diffList)) {
            // 差集不为空，表示有一些另一个页面没有的code，需要排除
            for (DmFcstDimInfoVO diff : diffList) {
                customVOList.removeIf(custom -> diff.getConnectCode().equals(custom.getConnectCode()));
            }
        }
        return customVOList;
    }

    private void setProdTeamDimesionCode(CommonViewVO commonViewVO, List<DmFcstDimInfoVO> customVOList) {

        List<String> lv1CodeList = customVOList.stream().map(DmFcstDimInfoVO::getLv1Code).filter(lv1ProdRndTeamCode -> StringUtils.isNotEmpty(lv1ProdRndTeamCode)).distinct().collect(Collectors.toList());
        List<String> lv2CodeList = customVOList.stream().map(DmFcstDimInfoVO::getLv2Code).filter(lv2ProdRndTeamCode -> StringUtils.isNotEmpty(lv2ProdRndTeamCode)).distinct().collect(Collectors.toList());
        List<String> lv3CodeList = customVOList.stream().map(DmFcstDimInfoVO::getLv3Code).filter(lv3ProdRndTeamCode -> StringUtils.isNotEmpty(lv3ProdRndTeamCode)).distinct().collect(Collectors.toList());
        List<String> lv4CodeList = customVOList.stream().map(DmFcstDimInfoVO::getLv4Code).filter(lv4ProdRndTeamCode -> StringUtils.isNotEmpty(lv4ProdRndTeamCode)).distinct().collect(Collectors.toList());
        commonViewVO.setLv1CodeList(lv1CodeList);
        commonViewVO.setLv2CodeList(lv2CodeList);
        commonViewVO.setLv3CodeList(lv3CodeList);
        commonViewVO.setLv4CodeList(lv4CodeList);
    }

    public List<DmFcstDimInfoVO> getAnotherPageCode(CommonViewVO commonViewVO) {

        List<DmFcstDimInfoVO> allGroupCodeList = new ArrayList<>();

        CommonViewVO commonVO =new CommonViewVO();
        BeanUtils.copyProperties(commonViewVO, commonVO);
        FcstIndustryUtil.setSpecailCode(commonVO);
        String otherFlag = CommonConstant.allCombPageFlag.get(commonVO.getPageSymbol());
        if (IndustryConstEnum.PAGE_TYPE.ANNUAL.getValue().equals(otherFlag)) {
            if ("N".equals(commonVO.getMainFlag())) {
                commonVO.setMainFlag(null);
            }
        }
        commonVO.setPageFlag(otherFlag);
        getIndustryGroupCode(commonVO, allGroupCodeList, false);
        commonViewVO.setLv0Flag(commonVO.getLv0Flag());
        return allGroupCodeList;
    }

    public void getIndustryGroupCode(CommonViewVO commonViewVO, List<DmFcstDimInfoVO> allGroupCodeList, boolean flag) {
        // lv1Dimensionsionset表示LV0权限,lv0Dimensionsionset表示目录树权限
        Set<String> lv1DimensionSet = commonViewVO.getLv1DimensionSet();
        boolean condition = "N".equals(commonViewVO.getExpandFlag()) && StringUtils.isEmpty(commonViewVO.getFilterGroupLevel());
        boolean twoCondition = StringUtils.isNotEmpty(commonViewVO.getKeyword()) || !flag;
        boolean allCondition = condition || twoCondition;

        if (lv1DimensionSet.size() == 0 || !lv1DimensionSet.contains("NO_PERMISSION")) {
            commonViewVO.setLv0Flag("Y");
            if (condition || GroupLevelEnum.LV0.getValue().equals(commonViewVO.getFilterGroupLevel()) || twoCondition) {
                commonViewVO.setGroupLevel(GroupLevelEnum.LV0.getValue());
                getDbListForAllPage(commonViewVO, allGroupCodeList);
            }
        } else {
            commonViewVO.setLv0Flag("N");
        }
        getSubProdTeamList(commonViewVO, allGroupCodeList, allCondition);
        prodTeamWithLv3(commonViewVO, allGroupCodeList, allCondition);
        queryOtherData(commonViewVO, allGroupCodeList, twoCondition);
    }

    public void getSubProdTeamList(CommonViewVO commonViewVO, List<DmFcstDimInfoVO> allGroupCodeList, boolean allCondition) {
        if (allCondition || GroupLevelEnum.LV1.getValue().equals(commonViewVO.getFilterGroupLevel())) {
            commonViewVO.setGroupLevel(GroupLevelEnum.LV1.getValue());
            getDbListForAllPage(commonViewVO, allGroupCodeList);
            commonViewVO.setNextGroupLevel(GroupLevelEnum.LV1.getValue());
            commonViewVO.setPageType(commonViewVO.getPageSymbol());
            CommonViewVO commonVO = ObjectCopyUtil.copy(commonViewVO, CommonViewVO.class);
            if ("MONTH".equals(commonViewVO.getPageType())) {
                commonVO.setVersionId(commonVO.getMonVersionId());
            }
            dropDownService.handlePermission(allGroupCodeList, commonVO);
        }
        if (allCondition || GroupLevelEnum.LV2.getValue().equals(commonViewVO.getFilterGroupLevel())) {
            commonViewVO.setGroupLevel(GroupLevelEnum.LV2.getValue());
            getDbListForAllPage(commonViewVO, allGroupCodeList);
        }
    }

    private void queryOtherData(CommonViewVO commonViewVO, List<DmFcstDimInfoVO> allGroupCodeList, boolean condition) {
        if (GroupLevelEnum.LV4.getValue().equals(commonViewVO.getGroupLevel()) || GroupLevelEnum.LV4.getValue().equals(commonViewVO.getFilterGroupLevel()) || condition) {
            commonViewVO.setGroupLevel(GroupLevelEnum.LV4.getValue());
            getDbListForAllPage(commonViewVO, allGroupCodeList);
        }
        boolean threeCondition = condition && commonViewVO.getIsCompareFlag();

        if (GroupLevelEnum.DIMENSION.getValue().equals(commonViewVO.getGroupLevel()) || GroupLevelEnum.DIMENSION.getValue().equals(commonViewVO.getFilterGroupLevel()) || threeCondition) {
            commonViewVO.setGroupLevel(GroupLevelEnum.DIMENSION.getValue());
            getDbListForAllPage(commonViewVO, allGroupCodeList);
        }
        if (GroupLevelEnum.SUBCATEGORY.getValue().equals(commonViewVO.getGroupLevel()) || GroupLevelEnum.SUBCATEGORY.getValue().equals(commonViewVO.getFilterGroupLevel()) || threeCondition) {
            commonViewVO.setGroupLevel(GroupLevelEnum.SUBCATEGORY.getValue());
            getDbListForAllPage(commonViewVO, allGroupCodeList);
        }
        if (GroupLevelEnum.SUB_DETAIL.getValue().equals(commonViewVO.getGroupLevel()) || GroupLevelEnum.SUB_DETAIL.getValue().equals(commonViewVO.getFilterGroupLevel()) || threeCondition) {
            commonViewVO.setGroupLevel(GroupLevelEnum.SUB_DETAIL.getValue());
            getDbListForAllPage(commonViewVO, allGroupCodeList);
        }
        if (GroupLevelEnum.SPART.getValue().equals(commonViewVO.getGroupLevel()) || GroupLevelEnum.SPART.getValue().equals(commonViewVO.getFilterGroupLevel()) || threeCondition) {
            commonViewVO.setGroupLevel(GroupLevelEnum.SPART.getValue());
            getDbListForAllPage(commonViewVO, allGroupCodeList);
        }
    }

    private void prodTeamWithLv3(CommonViewVO commonViewVO, List<DmFcstDimInfoVO> allGroupCodeList, boolean allCondition) {
        if (("N".equals(commonViewVO.getExpandFlag()) && StringUtils.isEmpty(commonViewVO.getFilterGroupLevel())) || GroupLevelEnum.LV3.getValue().equals(commonViewVO.getFilterGroupLevel()) || allCondition) {
            commonViewVO.setGroupLevel(GroupLevelEnum.LV3.getValue());
            getDbListForAllPage(commonViewVO, allGroupCodeList);
        }
    }

    public void getDbListForAllPage(CommonViewVO commonViewVO, List<DmFcstDimInfoVO> allGroupCodeList) {
        List<DmFcstDimInfoVO> dmFcstDimInfoList;
        String granularityType = commonViewVO.getGranularityType();
        if (CommonConstant.PROD_GROUP_LEVEL.contains(commonViewVO.getGroupLevel()) || GroupLevelEnum.SPART.getValue().equals(commonViewVO.getGroupLevel())) {
            commonViewVO.setViewFlag(IndustryConstEnum.VIEW_FLAG.PROD_SPART.getValue());
        } else {
            commonViewVO.setViewFlag(IndustryConstEnum.VIEW_FLAG.DIMENSION.getValue());
        }
        if ("Y".equals(commonViewVO.getMainFlag())) {
            if (IndustryConstEnum.PAGE_TYPE.MONTH.getValue().equals(commonViewVO.getPageSymbol())) {
                switch (granularityType) {
                    case "IRB":
                        dmFcstDimInfoList = dmFcstDimInfoDao.getMainIrbDimInfoList(commonViewVO);
                        allGroupCodeList.addAll(dmFcstDimInfoList);
                        break;
                    case "INDUS":
                        dmFcstDimInfoList = dmFcstDimInfoDao.getMainIndusDimInfoList(commonViewVO);
                        allGroupCodeList.addAll(dmFcstDimInfoList);
                        break;
                    case "PROD":
                        dmFcstDimInfoList = dmFcstDimInfoDao.getMainProdDimInfoList(commonViewVO);
                        allGroupCodeList.addAll(dmFcstDimInfoList);
                        break;
                    default:
                        break;
                }
            } else {
                switch (granularityType) {
                    case "IRB":
                        dmFcstDimInfoList = dmFcstDimInfoDao.getMainAnnualIrbDimInfoList(commonViewVO);
                        allGroupCodeList.addAll(dmFcstDimInfoList);
                        break;
                    case "INDUS":
                        dmFcstDimInfoList = dmFcstDimInfoDao.getMainAnnualIndusDimInfoList(commonViewVO);
                        allGroupCodeList.addAll(dmFcstDimInfoList);
                        break;
                    case "PROD":
                        dmFcstDimInfoList = dmFcstDimInfoDao.getMainAnnualProdDimInfoList(commonViewVO);
                        allGroupCodeList.addAll(dmFcstDimInfoList);
                        break;
                    default:
                        break;
                }
            }
        } else {
            getDbListNotMainFlag(granularityType, commonViewVO, allGroupCodeList);
        }
    }

    private void getDbListNotMainFlag(String granularityType, CommonViewVO commonViewVO, List<DmFcstDimInfoVO> allGroupCodeList) {
        List<DmFcstDimInfoVO> dmFcstDimInfoList = new ArrayList<>();
        switch (granularityType) {
            case "IRB":
                dmFcstDimInfoList = dmFcstDimInfoDao.getIrbDimInfoList(commonViewVO);
                allGroupCodeList.addAll(dmFcstDimInfoList);
                break;
            case "INDUS":
                dmFcstDimInfoList = dmFcstDimInfoDao.getIndusDimInfoList(commonViewVO);
                allGroupCodeList.addAll(dmFcstDimInfoList);
                break;
            case "PROD":
                dmFcstDimInfoList = dmFcstDimInfoDao.getProdDimInfoList(commonViewVO);
                allGroupCodeList.addAll(dmFcstDimInfoList);
                break;
            default:
                break;
        }
    }

    public List<DmFcstDimInfoVO> getSpartListForPage(CommonViewVO commonViewVO, List<DmFcstDimInfoVO> customVOList) {
        if ("Y".equals(commonViewVO.getMainFlag()) && "全选".equals(commonViewVO.getCodeAttributes())) {
            commonViewVO.setCodeAttributes("");
        }
        FcstIndustryUtil.setSpecailCode(commonViewVO);
        // 获取最大层级
        Set<String> groupLevelSet = customVOList.stream().map(DmFcstDimInfoVO::getGroupLevel).collect(Collectors.toSet());
        List<String> groupLevelList = new ArrayList<>();
        // 获取最大的层级
        for (String groupLevel : CommonConstant.PROD_GROUP_LEVEL) {
            for (String customLevel : groupLevelSet) {
                if (groupLevel.equals(customLevel)) {
                    groupLevelList.add(customLevel);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(groupLevelList)) {
            String maxGroupLevel = groupLevelList.stream().findFirst().orElse("");
            commonViewVO.setMaxLevel(maxGroupLevel);
        }

        List<String> lv1List = customVOList.stream().map(DmFcstDimInfoVO::getLv1Code).distinct().collect(Collectors.toList());
        List<String> lv2List = customVOList.stream().map(DmFcstDimInfoVO::getLv2Code).distinct().collect(Collectors.toList());
        List<String> lv3List = customVOList.stream().map(DmFcstDimInfoVO::getLv3Code).distinct().collect(Collectors.toList());
        List<String> lv4List = customVOList.stream().map(DmFcstDimInfoVO::getLv4Code).distinct().collect(Collectors.toList());
        lv1List.remove(null);
        lv2List.remove(null);
        lv3List.remove(null);
        lv4List.remove(null);
        commonViewVO.setLv1CodeList(lv1List);
        commonViewVO.setLv2CodeList(lv2List);
        commonViewVO.setLv3CodeList(lv3List);
        commonViewVO.setLv4CodeList(lv4List);
        // 查询数据库
        commonViewVO.setVersionId(commonService.getVersionId(IndustryConstEnum.PAGE_TYPE.ANNUAL.getValue()));
        // 获取月度version_id
        commonViewVO.setMonVersionId(commonService.getVersionId(IndustryConstEnum.PAGE_TYPE.MONTH.getValue()));
        return findSpartFromDb(commonViewVO);
    }

    private List<DmFcstDimInfoVO> findSpartFromDb(CommonViewVO commonViewVO) {
        List<DmFcstDimInfoVO> dmFcstDimInfoList = new ArrayList<>();
        String granularityType = commonViewVO.getGranularityType();
        if ("Y".equals(commonViewVO.getMainFlag())) {
            if (IndustryConstEnum.PAGE_TYPE.MONTH.getValue().equals(commonViewVO.getPageFlag())) {
                switch (granularityType) {
                    case "IRB":
                        dmFcstDimInfoList =  dmFcstDimInfoDao.getMainIrbSpartList(commonViewVO);
                        break;
                    case "INDUS":
                        dmFcstDimInfoList = dmFcstDimInfoDao.getMainIndusSpartList(commonViewVO);
                        break;
                    case "PROD":
                        dmFcstDimInfoList = dmFcstDimInfoDao.getMainProdSpartList(commonViewVO);
                        break;
                    default:
                        break;
                }
            } else {
                switch (granularityType) {
                    case "IRB":
                        dmFcstDimInfoList = dmFcstDimInfoDao.getMainAnnualIrbSpartList(commonViewVO);
                        break;
                    case "INDUS":
                        dmFcstDimInfoList = dmFcstDimInfoDao.getMainAnnualIndusSpartList(commonViewVO);
                        break;
                    case "PROD":
                        dmFcstDimInfoList = dmFcstDimInfoDao.getMainAnnualProdSpartList(commonViewVO);
                        break;
                    default:
                        break;
                }
            }
        } else {
            switch (granularityType) {
                case "IRB":
                    dmFcstDimInfoList = dmFcstDimInfoDao.getIrbSpartList(commonViewVO);
                    break;
                case "INDUS":
                    dmFcstDimInfoList = dmFcstDimInfoDao.getIndusSpartList(commonViewVO);
                    break;
                case "PROD":
                    dmFcstDimInfoList = dmFcstDimInfoDao.getProdSpartList(commonViewVO);
                    break;
                default:
                    break;
            }
        }
        return dmFcstDimInfoList;
    }
}
