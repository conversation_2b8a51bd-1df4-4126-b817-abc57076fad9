/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.vo.drop;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * DmFcstDimInfoVO Class
 *
 * <AUTHOR>
 * @since 2024/7/1
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DmFcstBasePriceCusDimVO implements Serializable {
    private static final long serialVersionUID = -3527116743977395982L;

    private Long createdBy;

    private Timestamp creationDate;

    private Long lastUpdatedBy;

    private Timestamp lastUpdatedate;

    private String bgCode;

    private String bgCnName;

    private String delFlag;

    private String viewFlag;

    private Long versionId;

    private String spartCode;

    private String spartCnName;

    private String groupCode;

    private String groupLevel;

    private String parentLevel;

    private String groupCnName;

    /**
     * 组合id
     */
    private Long customId;

    private String customCnName;

    private String regionCode;

    private String regionCnName;

    private String repofficeCode;

    private String repofficeCnName;

    private String lvCode;

    private String lvCnName;

    private String granularityType;

    /**
     *  国内海外标识(N:国内/Y:海外/G:全球)
     */
    private String overseaFlag;

    private String pageType;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 角色id
     */
    private String roleId;

    private String statusFlag;

    private String signTopCustCategoryCode;

    @ApiModelProperty("签约客户_大T系统部名称")
    private String signTopCustCategoryCnName;

    @ApiModelProperty("签约客户_子网系统部名称")
    private String signSubsidiaryCustcatgCnName;

}
