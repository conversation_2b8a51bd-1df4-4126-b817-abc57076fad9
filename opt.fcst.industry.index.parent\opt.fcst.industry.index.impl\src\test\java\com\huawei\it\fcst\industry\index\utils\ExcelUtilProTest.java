/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.utils;

import com.huawei.it.fcst.industry.index.constant.CommonConstant;
import com.huawei.it.fcst.industry.index.vo.common.AbstractExcelTitleVO;
import com.huawei.it.fcst.industry.index.vo.common.BranchExcelTitleVO;
import com.huawei.it.fcst.industry.index.vo.common.ExcelVO;
import com.huawei.it.fcst.industry.index.vo.common.LeafExcelTitleVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import org.apache.poi.ss.formula.FormulaParseException;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Comment;
import org.apache.poi.ss.usermodel.Hyperlink;
import org.apache.poi.ss.usermodel.RichTextString;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellAddress;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * ExcelUtilProTest Class
 *
 * <AUTHOR>
 * @since 2023/5/11
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class ExcelUtilProTest {
    private static final Logger LOGGER = LoggerFactory.getLogger(ExcelUtilPro.class);
    @InjectMocks
    private ExcelUtilPro excelUtilPro;

    @Mock
    private Cell cell;

    @Test
    public void implReadHead() {
        String[] heads={"8","10","11_10"};
        List<ExcelVO> excelVOS = excelUtilPro.implReadHead(heads);
        Assert.assertNotNull(excelVOS);
    }

    @Test
    public void adjustTitleVoList() {
        List<AbstractExcelTitleVO> formInfoVo = new ArrayList<>();
        LeafExcelTitleVO columnCnVersion =
            new LeafExcelTitleVO(
                CommonConstant.VERSION_TITLE,
                CommonConstant.WIDTH,
                true,
                "version",
                "version",
                CellType.STRING,
                false);
        formInfoVo.add(columnCnVersion);
        LeafExcelTitleVO columnCnVersionName =
            new LeafExcelTitleVO(
                "202301-TOP品类-Auto", CommonConstant.WIDTH, true, "versionName", "versionName", CellType.STRING, false);
        formInfoVo.add(columnCnVersionName);
        List<AbstractExcelTitleVO> selectedLeafExcelTitleVO=new ArrayList<>();
        int voList = excelUtilPro.adjustTitleVoList(formInfoVo, selectedLeafExcelTitleVO);
        Assert.assertNotNull(voList);
    }

    @Test
    public void adjustTitleVoList2Test() {
        List<AbstractExcelTitleVO> formInfoVo = new ArrayList<>();
        List<AbstractExcelTitleVO> childExcelTitleList=new ArrayList<>();
        LeafExcelTitleVO columnCnVersionName =
            new LeafExcelTitleVO(
                "202301-TOP品类-Auto", CommonConstant.WIDTH, true, "versionName", "versionName", CellType.STRING, false);
        childExcelTitleList.add(columnCnVersionName);
        BranchExcelTitleVO branchExcelTitleVO = new BranchExcelTitleVO("test", 380 * 10);
        branchExcelTitleVO.setChildExcelTitleList(childExcelTitleList);
        formInfoVo.add(branchExcelTitleVO);
        List<AbstractExcelTitleVO> selectedLeafExcelTitleVO=new ArrayList<>();
        int voList = excelUtilPro.adjustTitleVoList(formInfoVo, selectedLeafExcelTitleVO);
        Assert.assertNotNull(voList);
    }

    @Test
    public void getStringCellValue() throws CommonApplicationException {
        String headType="VARCHAR";
        cell=null;
        String stringCellValue = excelUtilPro.getStringCellValue(cell, headType);
        Assert.assertNotNull(stringCellValue);
    }

    @Test
    public void getStringCellValue2Test() throws CommonApplicationException {
        String headType="NUMERIC";
        cell=null;
        String stringCellValue = excelUtilPro.getStringCellValue(cell, headType);
        Assert.assertNull(stringCellValue);
    }

    @Test
    public void getStringCellValue3Test() throws CommonApplicationException {
        String headType="NUMERICS";
        String stringCellValue = excelUtilPro.getStringCellValue(cell, headType);
        Assert.assertNull(stringCellValue);
    }

    @Test
    public void getStringCellValue4Test() throws Exception {
        String headType = "NUMERIC";
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/UTtestTemplate2.xlsx");
        Sheet heapSheet = workbook.getSheetAt(0);
        Cell cell = heapSheet.getRow(1).getCell(0);
        String stringCellValue = excelUtilPro.getStringCellValue(cell, headType);
        Assert.assertNotNull(stringCellValue);
    }

    @Test
    public void getStringCellValue5Test() throws Exception {
        String headType = "NUMERIC";
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate1.xlsx");
        Sheet heapSheet = workbook.getSheetAt(0);
        Cell cell = heapSheet.getRow(2).getCell(0);
        String stringCellValue = excelUtilPro.getStringCellValue(cell, headType);
        Assert.assertNull(stringCellValue);
    }

    @Test
    public void getStringCellValue6Test() throws Exception {
        String headType = "VARCHAR";
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/UTtestTemplate2.xlsx");
        Sheet heapSheet = workbook.getSheetAt(0);
        Cell cell = heapSheet.getRow(1).getCell(0);
        String stringCellValue = excelUtilPro.getStringCellValue(cell, headType);
        Assert.assertNotNull(stringCellValue);
    }

    @Test
    public void getStringCellValue7Test() throws Exception {
        String headType = "NUMERIC";
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/UTtestTemplate2.xlsx");
        Sheet heapSheet = workbook.getSheetAt(0);
        Cell cell = heapSheet.getRow(0).getCell(0);
        String stringCellValue=null;
        try {
            stringCellValue = excelUtilPro.getStringCellValue(cell, headType);
        } catch (Exception e1) {
            LOGGER.error("入参清单数据版本为空");
            }
        Assert.assertNull(stringCellValue);
    }

    @Test
    public void getStringCellValue8Test() throws Exception {
        String headType = "NUMERIC";
        Cell cell = getCell();
        String stringCellValue=null;
        try {
            stringCellValue = excelUtilPro.getStringCellValue(cell, headType);
        } catch (Exception e1) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNull(stringCellValue);
    }

    @NotNull
    private static Cell getCell() {
        Cell cell = new Cell() {
            @Override
            public int getColumnIndex() {
                return 0;
            }

            @Override
            public int getRowIndex() {
                return 0;
            }

            @Override
            public Sheet getSheet() {
                return null;
            }

            @Override
            public Row getRow() {
                return null;
            }

            @Override
            public void setCellType(CellType cellType) {

            }

            @Override
            public void setBlank() {

            }

            @Override
            public CellType getCellType() {
                return CellType.BOOLEAN;
            }

            @Override
            public CellType getCachedFormulaResultType() {
                return null;
            }

            @Override
            public void setCellValue(double v) {

            }

            @Override
            public void setCellValue(Date date) {

            }

            @Override
            public void setCellValue(LocalDateTime localDateTime) {

            }

            @Override
            public void setCellValue(Calendar calendar) {

            }

            @Override
            public void setCellValue(RichTextString richTextString) {

            }

            @Override
            public void setCellValue(String s) {

            }

            @Override
            public void setCellFormula(String s) throws FormulaParseException, IllegalStateException {

            }

            @Override
            public void removeFormula() throws IllegalStateException {

            }

            @Override
            public String getCellFormula() {
                return null;
            }

            @Override
            public double getNumericCellValue() {
                return 0;
            }

            @Override
            public Date getDateCellValue() {
                return null;
            }

            @Override
            public LocalDateTime getLocalDateTimeCellValue() {
                return null;
            }

            @Override
            public RichTextString getRichStringCellValue() {
                return null;
            }

            @Override
            public String getStringCellValue() {
                return null;
            }

            @Override
            public void setCellValue(boolean b) {

            }

            @Override
            public void setCellErrorValue(byte b) {

            }

            @Override
            public boolean getBooleanCellValue() {
                return false;
            }

            @Override
            public byte getErrorCellValue() {
                return 0;
            }

            @Override
            public void setCellStyle(CellStyle cellStyle) {

            }

            @Override
            public CellStyle getCellStyle() {
                return null;
            }

            @Override
            public void setAsActiveCell() {

            }

            @Override
            public CellAddress getAddress() {
                return null;
            }

            @Override
            public void setCellComment(Comment comment) {

            }

            @Override
            public Comment getCellComment() {
                return null;
            }

            @Override
            public void removeCellComment() {

            }

            @Override
            public Hyperlink getHyperlink() {
                return null;
            }

            @Override
            public void setHyperlink(Hyperlink hyperlink) {

            }

            @Override
            public void removeHyperlink() {

            }

            @Override
            public CellRangeAddress getArrayFormulaRange() {
                return null;
            }

            @Override
            public boolean isPartOfArrayFormulaGroup() {
                return false;
            }
        };
        return cell;
    }

    @Test
    public void getStringCellValue9Test() throws Exception {
        String headType = "NUMERIC";
        Cell cell = getCellValue();
        String stringCellValue=null;
        try {
            stringCellValue = excelUtilPro.getStringCellValue(cell, headType);
        } catch (Exception e1) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(stringCellValue);
    }

    @NotNull
    private static Cell getCellValue() {
        Cell cell = new Cell() {
            @Override
            public int getColumnIndex() {
                return 0;
            }

            @Override
            public int getRowIndex() {
                return 0;
            }

            @Override
            public Sheet getSheet() {
                return null;
            }

            @Override
            public Row getRow() {
                return null;
            }

            @Override
            public void setCellType(CellType cellType) {

            }

            @Override
            public void setBlank() {

            }

            @Override
            public CellType getCellType() {
                return CellType.FORMULA;
            }

            @Override
            public CellType getCachedFormulaResultType() {
                return null;
            }

            @Override
            public void setCellValue(double v) {

            }

            @Override
            public void setCellValue(Date date) {

            }

            @Override
            public void setCellValue(LocalDateTime localDateTime) {

            }

            @Override
            public void setCellValue(Calendar calendar) {

            }

            @Override
            public void setCellValue(RichTextString richTextString) {

            }

            @Override
            public void setCellValue(String s) {

            }

            @Override
            public void setCellFormula(String s) throws FormulaParseException, IllegalStateException {

            }

            @Override
            public void removeFormula() throws IllegalStateException {

            }

            @Override
            public String getCellFormula() {
                return null;
            }

            @Override
            public double getNumericCellValue() {
                return 0;
            }

            @Override
            public Date getDateCellValue() {
                return null;
            }

            @Override
            public LocalDateTime getLocalDateTimeCellValue() {
                return null;
            }

            @Override
            public RichTextString getRichStringCellValue() {
                return null;
            }

            @Override
            public String getStringCellValue() {
                return null;
            }

            @Override
            public void setCellValue(boolean b) {

            }

            @Override
            public void setCellErrorValue(byte b) {

            }

            @Override
            public boolean getBooleanCellValue() {
                return false;
            }

            @Override
            public byte getErrorCellValue() {
                return 0;
            }

            @Override
            public void setCellStyle(CellStyle cellStyle) {

            }

            @Override
            public CellStyle getCellStyle() {
                return null;
            }

            @Override
            public void setAsActiveCell() {

            }

            @Override
            public CellAddress getAddress() {
                return null;
            }

            @Override
            public void setCellComment(Comment comment) {

            }

            @Override
            public Comment getCellComment() {
                return null;
            }

            @Override
            public void removeCellComment() {

            }

            @Override
            public Hyperlink getHyperlink() {
                return null;
            }

            @Override
            public void setHyperlink(Hyperlink hyperlink) {

            }

            @Override
            public void removeHyperlink() {

            }

            @Override
            public CellRangeAddress getArrayFormulaRange() {
                return null;
            }

            @Override
            public boolean isPartOfArrayFormulaGroup() {
                return false;
            }
        };
        return cell;
    }

    private void setCell(){
        cell = new Cell() {
            @Override
            public int getColumnIndex() {
                return 0;
            }

            @Override
            public int getRowIndex() {
                return 0;
            }

            @Override
            public Sheet getSheet() {
                return null;
            }

            @Override
            public Row getRow() {
                return null;
            }

            @Override
            public void setCellType(CellType cellType) {

            }

            @Override
            public void setBlank() {

            }

            @Override
            public CellType getCellType() {
                return CellType.ERROR;
            }

            @Override
            public CellType getCachedFormulaResultType() {
                return null;
            }

            @Override
            public void setCellValue(double v) {

            }

            @Override
            public void setCellValue(Date date) {

            }

            @Override
            public void setCellValue(LocalDateTime localDateTime) {

            }

            @Override
            public void setCellValue(Calendar calendar) {

            }

            @Override
            public void setCellValue(RichTextString richTextString) {

            }

            @Override
            public void setCellValue(String s) {

            }

            @Override
            public void setCellFormula(String s) throws FormulaParseException, IllegalStateException {

            }

            @Override
            public void removeFormula() throws IllegalStateException {

            }

            @Override
            public String getCellFormula() {
                return null;
            }

            @Override
            public double getNumericCellValue() {
                return 0;
            }

            @Override
            public Date getDateCellValue() {
                return null;
            }

            @Override
            public LocalDateTime getLocalDateTimeCellValue() {
                return null;
            }

            @Override
            public RichTextString getRichStringCellValue() {
                return null;
            }

            @Override
            public String getStringCellValue() {
                return null;
            }

            @Override
            public void setCellValue(boolean b) {

            }

            @Override
            public void setCellErrorValue(byte b) {

            }

            @Override
            public boolean getBooleanCellValue() {
                return false;
            }

            @Override
            public byte getErrorCellValue() {
                return 0;
            }

            @Override
            public void setCellStyle(CellStyle cellStyle) {

            }

            @Override
            public CellStyle getCellStyle() {
                return null;
            }

            @Override
            public void setAsActiveCell() {

            }

            @Override
            public CellAddress getAddress() {
                return null;
            }

            @Override
            public void setCellComment(Comment comment) {

            }

            @Override
            public Comment getCellComment() {
                return null;
            }

            @Override
            public void removeCellComment() {

            }

            @Override
            public Hyperlink getHyperlink() {
                return null;
            }

            @Override
            public void setHyperlink(Hyperlink hyperlink) {

            }

            @Override
            public void removeHyperlink() {

            }

            @Override
            public CellRangeAddress getArrayFormulaRange() {
                return null;
            }

            @Override
            public boolean isPartOfArrayFormulaGroup() {
                return false;
            }
        };
    }

    @Test
    public void getStringCellValue10Test() throws Exception {
        String headType = "NUMERIC";
        setCell();
        String stringCellValue=null;
        try {
            stringCellValue = excelUtilPro.getStringCellValue(cell, headType);
        } catch (Exception e1) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNull(stringCellValue);
    }

    @Test
    public void getStringCellValue11Test() throws Exception {
        String headType = "NUMERIC";
        Cell cell = getCellsValue();
        String stringCellValue=null;
        try {
            stringCellValue = excelUtilPro.getStringCellValue(cell, headType);
        } catch (Exception e1) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNull(stringCellValue);
    }

    @NotNull
    private static Cell getCellsValue() {
        Cell cell = new Cell() {
            @Override
            public int getColumnIndex() {
                return 0;
            }

            @Override
            public int getRowIndex() {
                return 0;
            }

            @Override
            public Sheet getSheet() {
                return null;
            }

            @Override
            public Row getRow() {
                return null;
            }

            @Override
            public void setCellType(CellType cellType) {

            }

            @Override
            public void setBlank() {

            }

            @Override
            public CellType getCellType() {
                return CellType._NONE;
            }

            @Override
            public CellType getCachedFormulaResultType() {
                return null;
            }

            @Override
            public void setCellValue(double v) {

            }

            @Override
            public void setCellValue(Date date) {

            }

            @Override
            public void setCellValue(LocalDateTime localDateTime) {

            }

            @Override
            public void setCellValue(Calendar calendar) {

            }

            @Override
            public void setCellValue(RichTextString richTextString) {

            }

            @Override
            public void setCellValue(String s) {

            }

            @Override
            public void setCellFormula(String s) throws FormulaParseException, IllegalStateException {

            }

            @Override
            public void removeFormula() throws IllegalStateException {

            }

            @Override
            public String getCellFormula() {
                return null;
            }

            @Override
            public double getNumericCellValue() {
                return 0;
            }

            @Override
            public Date getDateCellValue() {
                return null;
            }

            @Override
            public LocalDateTime getLocalDateTimeCellValue() {
                return null;
            }

            @Override
            public RichTextString getRichStringCellValue() {
                return null;
            }

            @Override
            public String getStringCellValue() {
                return null;
            }

            @Override
            public void setCellValue(boolean b) {

            }

            @Override
            public void setCellErrorValue(byte b) {

            }

            @Override
            public boolean getBooleanCellValue() {
                return false;
            }

            @Override
            public byte getErrorCellValue() {
                return 0;
            }

            @Override
            public void setCellStyle(CellStyle cellStyle) {

            }

            @Override
            public CellStyle getCellStyle() {
                return null;
            }

            @Override
            public void setAsActiveCell() {

            }

            @Override
            public CellAddress getAddress() {
                return null;
            }

            @Override
            public void setCellComment(Comment comment) {

            }

            @Override
            public Comment getCellComment() {
                return null;
            }

            @Override
            public void removeCellComment() {

            }

            @Override
            public Hyperlink getHyperlink() {
                return null;
            }

            @Override
            public void setHyperlink(Hyperlink hyperlink) {

            }

            @Override
            public void removeHyperlink() {

            }

            @Override
            public CellRangeAddress getArrayFormulaRange() {
                return null;
            }

            @Override
            public boolean isPartOfArrayFormulaGroup() {
                return false;
            }
        };
        return cell;
    }
}