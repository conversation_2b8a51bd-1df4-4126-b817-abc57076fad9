/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.codeReplacement;

import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.industry.pbi.dao.ICodeReplacementDao;
import com.huawei.it.fcst.industry.pbi.utils.CostReductUtils;
import com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO;
import com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO;
import com.huawei.it.jalor5.core.base.PageVO;

import javax.inject.Inject;
import javax.inject.Named;
import java.io.Serializable;
import java.util.List;

/**
 * 导出服务-月度指数图
 */
@Named("IExcelExport.CodeReplaceIndexAccDataProvider")
public class CodeReplaceIndexAccDataProvider extends CodeReplaceIndexDataProvider {
    @Inject
    private ICodeReplacementDao iCodeDao;

    @Inject
    private CodeReplacementService codeReplacementService;

    // 普通查询
    @Override
    public List<?> getData(Serializable conditionObject, PageVO pageVO) {
        CodeReplacementExpVO dataVO = (CodeReplacementExpVO) conditionObject;
        // 关系层级
        List<CodeReplacementExpVO> spartIndexCostList = null;
        if (CostReductUtils.getSpecailRoleMark(dataVO)) {
            return spartIndexCostList;
        }
        // 预处理的虚化
        if (dataVO.getPreCus()) {
            CostReductUtils.getPreCusIndexCostList(dataVO);
            spartIndexCostList = iCodeDao.getPreBlurSpartIndexAccCostExpList(dataVO);
        } else {
            spartIndexCostList = codeReplaceIndexAccData(dataVO, spartIndexCostList);
        }
        return spartIndexCostList;
    }

    private List<CodeReplacementExpVO> codeReplaceIndexAccData(CodeReplacementExpVO dataVO, List<CodeReplacementExpVO> spartIndexCostList) {
        String selectionLevel = dataVO.getQueryLevel();
        String isNeedBlur = dataVO.getIsNeedBlur();
        if (CommonConstant.SELECTION_LEVEL_SPART_LEVEL.equals(selectionLevel)) {
            if (Boolean.TRUE.equals(Boolean.valueOf(isNeedBlur))) {
                spartIndexCostList = iCodeDao.getBlurSpartIndexAccCostExpList(dataVO,
                        CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()), CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()));
            } else {
                spartIndexCostList = iCodeDao.getSpartIndexAccCostExpList(dataVO,
                        CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()), CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()));
            }
        } else if (CommonConstant.SELECTION_LEVEL_TOP_SPART_LEVEL.equals(selectionLevel)) {
            // topSpart 有 4情况。 此处做按单个的虚化属性做数据查询
            if (isNeedBlur.split("\\|").length == 2) {
                List<CodeReplacementVO> newSpartIndexCostList = codeReplacementService.getTopSpartIndexAccCostInfo(dataVO,
                        Boolean.valueOf(isNeedBlur.split("\\|")[0]), CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()),
                        dataVO.getNewCustomId(), dataVO.getNewProdTeamCode());
                List<CodeReplacementVO> oldSpartIndexCostList = codeReplacementService.getTopSpartIndexAccCostInfo(dataVO,
                        Boolean.valueOf(isNeedBlur.split("\\|")[1]), CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()),
                        dataVO.getOldCustomId(), dataVO.getOldProdTeamCode());
                spartIndexCostList = CostReductUtils.processIndexList(newSpartIndexCostList, oldSpartIndexCostList);
            }
        } else if (CommonConstant.SELECTION_LEVEL_PBI_LEVEL.equals(selectionLevel)) {
            spartIndexCostList = iCodeDao.getPbiIndexAccCostExpList(dataVO);
        }
        return spartIndexCostList;
    }

}
