<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.huawei.it.fcst</groupId>
	<artifactId>opt.fcst.industry.index.parent</artifactId>
	<version>1.0-SNAPSHOT</version>
	<packaging>pom</packaging>

	<modules>
		<module>opt.fcst.industry.index.api</module>
		<module>opt.fcst.industry.index.impl</module>
		<module>opt.fcst.industry.pbi.api</module>
		<module>opt.fcst.industry.pbi.impl</module>
		<module>opt.fcst.industry.price.api</module>
		<module>opt.fcst.industry.price.impl</module>
		<module>opt.fcst.industry.index.start</module>
		<module>opt.fcst.industry.common</module>
	</modules>

	<parent>
		<groupId>com.huawei.his.framework</groupId>
		<artifactId>jalor-dependencies</artifactId>
		<version>6.6.8.0.RELEASE</version>
	</parent>

	<!-- 版本级单元测试配置 -->
	<properties>
		<hutool.version>5.8.25</hutool.version>
		<fastjson.version>1.2.83</fastjson.version>
		<spring-boot.version>2.7.18</spring-boot.version>
		<spring.version>5.3.39-h2</spring.version>
		<netty.version>4.1.118.Final</netty.version>
		<jalor.version>6.6.8.0.RELEASE</jalor.version>
		<tomcat.embed.version>9.0.107</tomcat.embed.version>
		<cxf.version>3.5.11</cxf.version>
		<poi.version>5.2.2</poi.version>
		<commons-beanutils.version>1.11.0</commons-beanutils.version>
		<pdfbox.version>2.0.33</pdfbox.version>
		<commons.fileupload.version>1.6.0</commons.fileupload.version>
	</properties>
	<!-- 版本级单元测试配置 -->

	<dependencies>
		<dependency>
			<groupId>com.huawei.his.framework</groupId>
			<artifactId>jalor-starter-biz</artifactId>
			<version>${jalor.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.huawei.his.framework</groupId>
					<artifactId>jalor-configcenter-generate9dynamictoken</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>gson</artifactId>
					<groupId>com.google.code.gson</groupId>
				</exclusion>
				<exclusion>
					<artifactId>spring-web</artifactId>
					<groupId>org.springframework</groupId>
				</exclusion>
				<exclusion>
					<artifactId>bcprov-jdk15on</artifactId>
					<groupId>org.bouncycastle</groupId>
				</exclusion>
				<exclusion>
					<artifactId>jalor-inspection</artifactId>
					<groupId>com.huawei.his.framework</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.huawei.his.framework</groupId>
			<artifactId>jalor-starter-excel-biz</artifactId>
			<version>${jalor.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>mysql-connector-j</artifactId>
					<groupId>com.mysql</groupId>
				</exclusion>
				<exclusion>
					<artifactId>fileupload</artifactId>
					<groupId>com.huawei.wisecloud.secure</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.huawei.his.framework</groupId>
			<artifactId>huawei-security-conformable</artifactId>
			<version>2.6.20-RELEASE</version>
			<exclusions>
				<exclusion>
					<artifactId>bcprov-jdk18on</artifactId>
					<groupId>org.bouncycastle</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- 工具jar -->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.26</version><!--$NO-MVN-MAN-VER$-->
		</dependency>
		<!-- 拼音相关jar -->
		<dependency>
			<groupId>com.hankcs</groupId>
			<artifactId>hanlp</artifactId>
			<version>portable-1.8.4</version>
		</dependency>
		<!-- 工具jar -->
		<!-- 引入gauss驱动 Jar -->
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
			<version>42.7.5</version>
		</dependency>
		<!-- 引入gauss驱动 Jar -->
		<!-- jalor-store-s3配置 -->
		<dependency>
			<groupId>com.huawei.his.framework</groupId>
			<artifactId>jalor-store-s3</artifactId>
			<version>${jalor.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>easyexcel</artifactId>
					<groupId>com.alibaba</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.huawei.his.framework</groupId>
			<artifactId>jalor-dispersed-cacheclean-biz</artifactId>
			<version>${jalor.version}</version>
		</dependency>
		<!-- 引入gauss驱动 Jar -->
		<!-- 版本级单元测试配置 -->
		<!-- 单元测试 Jar -->
		<!-- 版本级单元测试配置 -->
		<dependency>
			<groupId>com.huawei.dt</groupId>
			<artifactId>dt4j-starter-boot</artifactId>
			<version>2.0.3</version>
			<scope>test</scope>
		</dependency>
		<!-- 单元测试 Jar -->
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-api-mockito2</artifactId>
			<version>2.0.7</version>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<artifactId>objenesis</artifactId>
					<groupId>org.objenesis</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-module-junit4-rule-agent</artifactId>
			<version>2.0.7</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-core</artifactId>
			<version>2.0.7</version>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<artifactId>objenesis</artifactId>
					<groupId>org.objenesis</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-module-junit4</artifactId>
			<version>2.0.7</version>
			<scope>test</scope>
		</dependency>
		<!-- 版本级单元测试配置 -->
		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-rt-rs-service-description-swagger</artifactId>
			<version>${cxf.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>cxf-rt-rs-service-description-swagger-ui</artifactId>
					<groupId>org.apache.cxf</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>pdfbox</artifactId>
		</dependency>

		<dependency>
			<groupId>com.huawei.it.jalor5</groupId>
			<artifactId>jalor5.xauth.api</artifactId>
			<version>1.2.12-SP10.RELEASE</version>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- xauth jar -->
		<!-- 多维度处理补丁 -->
		<dependency>
			<groupId>com.huawei.it.jalor5</groupId>
			<artifactId>jalor5.reverseauth</artifactId>
			<version>1.0.1-RELEASE</version>
			<exclusions>
				<exclusion>
					<groupId>*</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- 多维度处理补丁 -->

		<!-- API管控 Jar -->
		<dependency>
			<groupId>com.huawei.his.framework</groupId>
			<artifactId>jalor-webservice-support</artifactId>
			<version>${jalor.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>bcprov-jdk15on</artifactId>
					<groupId>org.bouncycastle</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- API管控 Jar -->
		<!-- 加解密 -->
		<dependency>
			<groupId>com.huawei.kmssdk</groupId>
			<artifactId>kmssdk</artifactId>
			<version>3.3.0.2</version>
			<exclusions>
				<exclusion>
					<artifactId>bcutil-jdk15to18</artifactId>
					<groupId>org.bouncycastle</groupId>
				</exclusion>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- end加解密 -->
		<!-- mqs -->
		<dependency>
			<groupId>com.huawei.his.mqs</groupId>
			<artifactId>mqs-sdk</artifactId>
			<version>3.5.6.0</version>
			<exclusions>
				<exclusion>
					<artifactId>logback-core</artifactId>
					<groupId>ch.qos.logback</groupId>
				</exclusion>
				<exclusion>
					<artifactId>logback-classic</artifactId>
					<groupId>ch.qos.logback</groupId>
				</exclusion>
				<exclusion>
					<artifactId>bcprov-jdk18on</artifactId>
					<groupId>org.bouncycastle</groupId>
				</exclusion>
				<exclusion>
					<artifactId>netty-codec-redis</artifactId>
					<groupId>io.netty</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>transmittable-thread-local</artifactId>
			<version>2.13.2</version>
		</dependency>
		<!-- mqs -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>easyexcel</artifactId>
			<version>3.3.4</version>
			<exclusions>
				<exclusion>
					<artifactId>poi-ooxml-schemas</artifactId>
					<groupId>org.apache.poi</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.8.25</version>
		</dependency>
		<dependency>
			<groupId>com.huawei.his.framework</groupId>
			<artifactId>jalor-hw-mqs</artifactId>
			<version>${jalor.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>mqs-sdk</artifactId>
					<groupId>com.huawei.his.mqs</groupId>
				</exclusion>
			</exclusions>
		</dependency>
	</dependencies>

	<!-- 漏洞修复 -->
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.apache.pdfbox</groupId>
				<artifactId>pdfbox</artifactId>
				<version>${pdfbox.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-codec</groupId>
				<artifactId>commons-codec</artifactId>
				<version>1.17.1</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.it.jalor5</groupId>
				<artifactId>jalor5.useronline.api</artifactId>
				<version>4.1.5.12</version>
				<exclusions>
					<exclusion>
						<groupId>*</groupId>
						<artifactId>*</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.huawei.it.jalor5</groupId>
				<artifactId>jalor5.useronline.impl</artifactId>
				<version>4.1.5.8</version>
				<exclusions>
					<exclusion>
						<groupId>*</groupId>
						<artifactId>*</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<!--  递归Jalor日志类属性存在栈溢出问题  -->
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-core</artifactId>
				<version>6.6.8.0-SP11.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-api</artifactId>
				<version>6.6.8.0-SP6.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-usf-client</artifactId>
				<version>6.6.8.0-SP4.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-jwt</artifactId>
				<version>6.6.8.0-SP1.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-audit-aop</artifactId>
				<version>6.6.8.0-SP1.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-aegis</artifactId>
				<version>6.6.8.0-SP1.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-rpc-vega</artifactId>
				<version>6.6.8.0-SP2.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-loadbalancer</artifactId>
				<version>6.6.8.0-SP6.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-security-api</artifactId>
				<version>6.6.8.0-SP1.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-helper</artifactId>
				<version>6.6.8.0-SP3.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-download</artifactId>
				<version>6.6.8.0.SP1.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-login</artifactId>
				<version>6.6.8.0-SP1.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-web-support</artifactId>
				<version>6.6.8.0-SP3.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-boot</artifactId>
				<version>6.6.8.0-SP1.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-sgov-huawei</artifactId>
				<version>6.6.8.0-SP1.RELEASE</version>
			</dependency>
		    <dependency>
				<groupId>com.huawei.his.framework</groupId>
				<artifactId>jalor-excel-impl</artifactId>
				<version>6.6.8.0-SP2.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.thoughtworks.xstream</groupId>
				<artifactId>xstream</artifactId>
				<version>1.4.21</version>
			</dependency>
			<dependency>
				<groupId>org.mockito</groupId>
				<artifactId>mockito-core</artifactId>
				<version>3.5.13</version>
				<scope>compile</scope>
			</dependency>
			<dependency>
				<groupId>org.codehaus.jettison</groupId>
				<artifactId>jettison</artifactId>
				<version>1.5.4</version>
				<exclusions>
					<exclusion>
						<artifactId>*</artifactId>
						<groupId>org.glassfish.jersey</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.fasterxml.woodstox</groupId>
				<artifactId>woodstox-core</artifactId>
				<version>6.4.0</version>
			</dependency>
			<dependency>
				<groupId>net.lingala.zip4j</groupId>
				<artifactId>zip4j</artifactId>
				<version>2.11.4</version>
			</dependency>
			<dependency>
				<groupId>org.dom4j</groupId>
				<artifactId>dom4j</artifactId>
				<version>2.1.4</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.it.jalor5.hw</groupId>
				<artifactId>huawei.httpdownload</artifactId>
				<version>5.4.15</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.it.jalor5</groupId>
				<artifactId>jalor5.log.desensitization</artifactId>
				<version>1.9.8</version>
				<exclusions>
					<exclusion>
						<groupId>*</groupId>
						<artifactId>*</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>org.apache.httpcomponents.client5</groupId>
				<artifactId>httpclient5</artifactId>
				<version>5.2.3</version>
			</dependency>
			<dependency>
				<groupId>ognl</groupId>
				<artifactId>ognl</artifactId>
				<version>3.3.4</version>
			</dependency>
			<dependency>
				<groupId>com.squareup.okio</groupId>
				<artifactId>okio</artifactId>
				<version>3.6.0</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.his.idaas</groupId>
				<artifactId>sso-sdk</artifactId>
				<version>2.2.7.3</version>
			</dependency>
			<!--cxf 升级-->
			<dependency>
				<groupId>org.freemarker</groupId>
				<artifactId>freemarker</artifactId>
				<version>2.3.32</version>
			</dependency>
			<dependency>
				<groupId>org.yaml</groupId>
				<artifactId>snakeyaml</artifactId>
				<version>2.2</version>
			</dependency>
			<dependency>
				<groupId>org.apache.tomcat.embed</groupId>
				<artifactId>tomcat-embed-websocket</artifactId>
				<version>${tomcat.embed.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.tomcat.embed</groupId>
				<artifactId>tomcat-embed-el</artifactId>
				<version>${tomcat.embed.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.tomcat.embed</groupId>
				<artifactId>tomcat-embed-core</artifactId>
				<version>${tomcat.embed.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.tomcat</groupId>
				<artifactId>tomcat-servlet-api</artifactId>
				<version>${tomcat.embed.version}</version>
			</dependency>

			<dependency>
				<groupId>org.apache.tomcat</groupId>
				<artifactId>tomcat-coyote</artifactId>
				<version>${tomcat.embed.version}</version>
			</dependency>
			<dependency>
				<groupId>com.squareup.okhttp3</groupId>
				<artifactId>okhttp</artifactId>
				<version>4.12.0</version>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-compress</artifactId>
				<version>1.26.1</version>
			</dependency>
			<dependency>
				<groupId>commons-io</groupId>
				<artifactId>commons-io</artifactId>
				<version>2.16.1</version>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-collections4</artifactId>
				<version>4.5.0-M2</version>
			</dependency>
			<dependency>
				<groupId>org.apache.cxf</groupId>
				<artifactId>cxf-spring-boot-starter-jaxws</artifactId>
				<version>${cxf.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.cxf</groupId>
				<artifactId>cxf-spring-boot-starter-jaxrs</artifactId>
				<version>${cxf.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.cxf</groupId>
				<artifactId>cxf-rt-frontend-jaxrs</artifactId>
				<version>${cxf.version}</version>
			</dependency>
			<dependency>
				<groupId>com.huawei.it.edm</groupId>
				<artifactId>edm3-client-sdk</artifactId>
				<version>3.2.3.6</version>
				<exclusions>
					<exclusion>
						<artifactId>bcprov-jdk15on</artifactId>
						<groupId>org.bouncycastle</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.google.guava</groupId>
				<artifactId>guava</artifactId>
				<version>32.1.1-jre</version>
			</dependency>
			<dependency>
				<groupId>org.bouncycastle</groupId>
				<artifactId>bcprov-jdk18on</artifactId>
				<version>1.78.1</version>
			</dependency>
			<dependency>
				<groupId>com.github.luben</groupId>
				<artifactId>zstd-jni</artifactId>
				<version>1.5.4-1</version>
			</dependency>
			<dependency>
				<groupId>com.huaweicloud</groupId>
				<artifactId>esdk-obs-java-bundle</artifactId>
				<version>3.23.9</version>
			</dependency>
			<dependency>
				<artifactId>gson</artifactId>
				<groupId>com.google.code.gson</groupId>
				<version>2.10.1</version>
			</dependency>
	    	<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi</artifactId>
				<version>${poi.version}</version>
			</dependency>

			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi-excelant</artifactId>
				<version>${poi.version}</version>
			</dependency>

			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi-ooxml</artifactId>
				<version>${poi.version}</version>
			</dependency>

			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi-ooxml-lite</artifactId>
				<version>${poi.version}</version>
			</dependency>

			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi-scratchpad</artifactId>
				<version>${poi.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-beanutils</groupId>
				<artifactId>commons-beanutils</artifactId>
				<version>${commons-beanutils.version}</version>
			</dependency>
			<dependency>
				<groupId>io.netty</groupId>
				<artifactId>netty-handler-ssl-ocsp</artifactId>
				<version>${netty.version}</version>
			</dependency>
			<dependency>
				<groupId>io.netty</groupId>
				<artifactId>netty-codec-mqtt</artifactId>
				<version>${netty.version}</version>
			</dependency>
			<dependency>
				<groupId>io.netty</groupId>
				<artifactId>netty-codec-smtp</artifactId>
				<version>${netty.version}</version>
			</dependency>
			<dependency>
				<groupId>io.netty</groupId>
				<artifactId>netty-codec-memcache</artifactId>
				<version>${netty.version}</version>
			</dependency>
			<dependency>
				<groupId>io.netty</groupId>
				<artifactId>netty-codec-stomp</artifactId>
				<version>${netty.version}</version>
			</dependency>
			<dependency>
				<groupId>io.netty</groupId>
				<artifactId>netty-codec-xml</artifactId>
				<version>${netty.version}</version>
			</dependency>
			<dependency>
				<groupId>io.netty</groupId>
				<artifactId>netty-transport-classes-kqueue</artifactId>
				<version>${netty.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-fileupload</groupId>
				<artifactId>commons-fileupload</artifactId>
				<version>${commons.fileupload.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<!-- 版本级单元测试配置 -->
		<plugins><!-- 不要放在pluginManagement下 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<skipTests>true</skipTests>
				</configuration>
			</plugin>
			<plugin>
				<groupId>com.huawei.dt</groupId>
				<artifactId>dt4j-coverage-maven-plugin</artifactId>
				<version>2.0.0</version>
				<configuration>
					<reportName>TestReport</reportName>
					<reportVersion>1.0.0</reportVersion>
					<reportUser>TestUser</reportUser>
					<checkoutRelativeDirectory>opt.fcst.industry.index.parent</checkoutRelativeDirectory>
					<includeStats>
					</includeStats>
					<!-- 覆盖率排除的包或类路径 -->
					<excludeStats>
						<item>com/huawei/it/fcst/industry/index/IndustryIndexApplication</item>
						<item>com/huawei/it/fcst/industry/index/vo/annual/DmFocAnnualAmpVO</item>
						<item>com/huawei/it/fcst/industry/index/vo/common/CommonViewVO</item>
						<item>com/huawei/it/fcst/industry/index/vo/common/ExportExcelVo</item>
						<item>com/huawei/it/fcst/industry/index/vo/relation/DmDimCatgModlCegIctVO</item>
						<item>com/huawei/it/fcst/industry/index/vo/relation/DmDimMaterialCodeVO</item>
						<item>com/huawei/it/fcst/industry/index/vo/relation/RelationVO</item>
						<item>com/huawei/it/fcst/industry/index/vo/common/DmFocViewInfoVO</item>
						<item>com/huawei/it/fcst/industry/index/vo/month/DmFocMonthWeightVO</item>
						<item>com/huawei/it/fcst/industry/index/utils/CellStyles</item>
						<item>com/huawei/it/fcst/industry/index/ApplicationStartEvent</item>
						<item>com/huawei/it/fcst/industry/index/ApplicationStopEvent</item>
						<item>com/huawei/it/fcst/industry/index/vo/combination/CombinationVO</item>
						<item>com/huawei/it/fcst/industry/index/enums/IndustryConst</item>
					</excludeStats>
				</configuration>
				<executions>
					<!-- 必须添加instrument任务，否则无法采集到class的指令执行数据 -->
					<execution>
						<id>instrument</id>
						<goals>
							<goal>instrument</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<!-- G.MAVEN.08 发布构建阶段构建前使用dependency-lock-maven-plugin插件锁定版本号，并将dependencies-lock.json归档到源代码仓库。 -->
			<plugin>
				<groupId>se.vandmo</groupId>
				<artifactId>dependency-lock-maven-plugin</artifactId>
				<version>0.13</version>
				<configuration>
					<filename>dependencies-lock.json</filename>
				</configuration>
			</plugin>
		</plugins>
		<!-- 版本级单元测试配置 -->
	</build>
</project>