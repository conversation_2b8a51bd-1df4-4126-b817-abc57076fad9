/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO;
import com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO;
import com.huawei.it.fcst.industry.pbi.vo.config.CostReductVO;
import com.huawei.it.fcst.util.UserInfoUtils;
import com.huawei.it.jalor5.core.util.CollectionUtil;
import com.huawei.it.jalor5.security.RoleVO;
import com.huawei.it.jalor5.security.UserVO;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * CostReductUtils
 */
public class CostReductUtils {
    // 非空校验
    public static boolean checkCostReductIfNullValue(CostReductVO vo) {
        // 字段不能为空
        if (Objects.isNull(vo) || StringUtils.isBlank(vo.getLv1ProdRndTeamCode()) || StringUtils.isBlank(
                vo.getObjective())) {
            return true;
        } else if (StringUtils.isBlank(vo.getPeriodId()) || StringUtils.isBlank(vo.getVersionId())
                || StringUtils.isBlank(vo.getLv1ProdRdTeamCnName())) {
            return true;
        } else {
            return false;
        }
    }

    // 字段长度校验
    public static boolean checkCostReductDataLength(CostReductVO vo) {
        // 字段不能为空
        if (Objects.isNull(vo) || (StringUtils.isNotBlank(vo.getLv1ProdRndTeamCode()) && vo.getLv1ProdRndTeamCode().length() > 200)) {
            return true;
        } else if (StringUtils.isNotBlank(vo.getLv2ProdRndTeamCode()) && vo.getLv2ProdRndTeamCode().length() > 200) {
            return true;
        } else if (Objects.isNull(vo) || (StringUtils.isNotBlank(vo.getLv1ProdRdTeamCnName()) && vo.getLv1ProdRdTeamCnName().length() > 200)) {
            return true;
        } else if (StringUtils.isNotBlank(vo.getLv2ProdRdTeamCnName()) && vo.getLv2ProdRdTeamCnName().length() > 200) {
            return true;
        } else if (StringUtils.isNotBlank(vo.getObjective()) && vo.getObjective().length() > 200) {
            return true;
        } else {
            return false;
        }
    }

    // 数值校验，可以没有，但是有值必须是数字
    public static boolean checkRatioValue(CostReductVO vo) {
        if (Objects.isNull(vo)) {
            return true;
        }
        String targetValue = vo.getObjective();
        if (StringUtils.isBlank(targetValue)) {
            return true;
        }
        // 入传入百分数，则做转换
        if (targetValue.contains("%")) {
            targetValue = targetValue.replace("%", "");
            if (NumberUtil.isNumber(targetValue)) {
                BigDecimal initRatio1 = new BigDecimal(targetValue);
                targetValue = initRatio1.divide(new BigDecimal(100)).toString();
            } else {
                return true;
            }
        }
        if (StringUtils.isNotBlank(targetValue) && NumberUtil.isNumber(targetValue) && (Double.valueOf(targetValue) <= 1D
                && Double.valueOf(targetValue) >= -1D)) {
            vo.setObjective(targetValue);
            return false;
        } else {
            return true;
        }
    }

    @NotNull
    public static String setTilteName(CodeReplacementExpVO codeReplacementExpVO) {
        String title = "";
        switch (codeReplacementExpVO.getGroupLevel()) {
            case "LV0":
                title = "ICT产业";
                break;
            case "LV1":
                title = "ICT产业-" + codeReplacementExpVO.getLv1ProdTeamName();
                break;
            case "LV2":
                title = "ICT产业-" + codeReplacementExpVO.getLv1ProdTeamName() + "-"
                        + codeReplacementExpVO.getLv2ProdTeamName();
                break;
            case "LV3":
                title = "ICT产业-" + codeReplacementExpVO.getLv1ProdTeamName() + "-"
                        + codeReplacementExpVO.getLv2ProdTeamName() + "-" + codeReplacementExpVO.getLv3ProdTeamName();
                break;
            case "LV4":
                title = "ICT产业-" + codeReplacementExpVO.getLv1ProdTeamName() + "-"
                        + codeReplacementExpVO.getLv2ProdTeamName() + "-" + codeReplacementExpVO.getLv3ProdTeamName() + "-"
                        + codeReplacementExpVO.getLv4ProdTeamName();
                break;
            default:
                title = "ICT产业-";
                break;
        }
        return title;
    }

    public static void setCommonHeaderInfo(CodeReplacementExpVO codeReplacementExpVO, Map<String, Object> headMap) {
        headMap.put("costTypeName", CommonConstant.costTypeMap.get(codeReplacementExpVO.getCostType()));
        headMap.put("regionName", codeReplacementExpVO.getRegionName());
        headMap.put("pbiName", CommonConstant.pbiMap.get(codeReplacementExpVO.getGranularityType()));
        headMap.put("overSeaName", CommonConstant.overseaMap.get(codeReplacementExpVO.getOverseaFlag()));
        headMap.put("repofficeName", codeReplacementExpVO.getRepofficeName());
        headMap.put("bgName", codeReplacementExpVO.getBgName());
        headMap.put("basePeriodId", codeReplacementExpVO.getBasePeriodId());
    }

    public static List<CodeReplacementExpVO> processShipList(List<CodeReplacementVO> shipNewDataInfo,
                                                             List<CodeReplacementVO> shipOldDataInfo) {
        List<CodeReplacementExpVO> infoList = new ArrayList<>();
        List<String> peroidList = new ArrayList<>();
        Map<String, List<CodeReplacementVO>> newInfo = shipNewDataInfo.stream()
                .collect(Collectors.groupingBy(CodeReplacementVO::getPeriodId));

        Map<String, List<CodeReplacementVO>> oldInfo = shipOldDataInfo.stream()
                .collect(Collectors.groupingBy(CodeReplacementVO::getPeriodId));

        if (!CollectionUtil.isNullOrEmpty(newInfo)) {
            for (Map.Entry<String, List<CodeReplacementVO>> entry : newInfo.entrySet()) {
                String key = entry.getKey();
                List<CodeReplacementVO> value = entry.getValue();
                if (CollectionUtil.isNullOrEmpty(value)) {
                    continue;
                }
                CodeReplacementVO newData = value.get(0);
                peroidList.add(key);
                CodeReplacementExpVO data = BeanUtil.fillBeanWithMap(BeanUtil.beanToMap(newData),
                        new CodeReplacementExpVO(), false, true);
                data.setShipmentQty(null);
                data.setNewQty(
                        (new BigDecimal(newData.getShipmentQty()).setScale(0, BigDecimal.ROUND_HALF_UP)).toString());
                if (!CollectionUtil.isNullOrEmpty(oldInfo.get(key))) {
                    data.setOldQty((new BigDecimal(oldInfo.get(key).get(0).getShipmentQty()).setScale(0,
                            BigDecimal.ROUND_HALF_UP)).toString());
                }
                infoList.add(data);
            }
        }
        if (!CollectionUtil.isNullOrEmpty(oldInfo)) {
            for (Map.Entry<String, List<CodeReplacementVO>> entry : oldInfo.entrySet()) {
                String key = entry.getKey();
                List<CodeReplacementVO> value = entry.getValue();
                if (CollectionUtil.isNullOrEmpty(value) || peroidList.contains(key)) {
                    continue;
                }
                CodeReplacementVO newData = value.get(0);
                peroidList.add(key);
                CodeReplacementExpVO data = BeanUtil.fillBeanWithMap(BeanUtil.beanToMap(newData),
                        new CodeReplacementExpVO(), false, true);
                data.setShipmentQty(null);
                data.setOldQty(newData.getShipmentQty());
                infoList.add(data);
            }
        }
        return infoList.stream()
                .sorted(Comparator.comparing(CodeReplacementExpVO::getPeriodId))
                .collect(Collectors.toList());
    }

    public static String setProdTeamCode(CodeReplacementVO dataVO, String newProdTeamCode) {
        if (StringUtils.isBlank(newProdTeamCode)) {
            if (StringUtils.isNotBlank(dataVO.getLv0ProdTeamCode())) {
                dataVO.setPbiLevel("LV0");
                newProdTeamCode = dataVO.getLv0ProdTeamCode();
            }
            if (StringUtils.isNotBlank(dataVO.getLv1ProdTeamCode())) {
                dataVO.setPbiLevel("LV1");
                newProdTeamCode = dataVO.getLv1ProdTeamCode();
            }
            if (StringUtils.isNotBlank(dataVO.getLv2ProdTeamCode())) {
                dataVO.setPbiLevel("LV2");
                newProdTeamCode = dataVO.getLv2ProdTeamCode();
            }
            if (StringUtils.isNotBlank(dataVO.getLv3ProdTeamCode())) {
                dataVO.setPbiLevel("LV3");
                newProdTeamCode = dataVO.getLv3ProdTeamCode();
            }
            if (StringUtils.isNotBlank(dataVO.getLv4ProdTeamCode())) {
                dataVO.setPbiLevel("LV4");
                newProdTeamCode = dataVO.getLv4ProdTeamCode();
            }
        }
        return newProdTeamCode;
    }

    public static String setRelationDesc(CodeReplacementExpVO dataVO) {
        String relationDesc = "";
        if (StringUtils.isNotBlank(dataVO.getReplaceRelationName())) {
            relationDesc = dataVO.getReplaceRelationName();
        }
        if (StringUtils.isNotBlank(dataVO.getReplaceRelationType())) {
            if (StringUtils.isBlank(relationDesc)) {
                relationDesc = dataVO.getReplaceRelationType();
            } else {
                relationDesc = relationDesc + "-" + dataVO.getReplaceRelationType();
            }
        }
        if (StringUtils.isNotBlank(dataVO.getRelationType())) {
            if (StringUtils.isBlank(relationDesc)) {
                relationDesc = dataVO.getRelationType();
            } else {
                relationDesc = relationDesc + "-" + dataVO.getRelationType();
            }
        }
        return relationDesc;
    }

    public static Map<String, Map<String, String>> processCVList(List<CodeReplacementVO> newSpartList,
                                                                 List<CodeReplacementVO> oldSpartList) {
        Map<String, Map<String, String>> dataMap = new LinkedHashMap<>();
        BigDecimal totalNumber = BigDecimal.ZERO;
        // 按月份处理
        if (CollectionUtil.isNullOrEmpty(newSpartList) || CollectionUtil.isNullOrEmpty(oldSpartList)) {
            return new HashMap<>();
        }
        Map<String, List<CodeReplacementVO>> newCollect = newSpartList.stream()
                .collect(Collectors.groupingBy(CodeReplacementVO::getPeriodId));
        Map<String, List<CodeReplacementVO>> oldCollect = oldSpartList.stream()
                .collect(Collectors.groupingBy(CodeReplacementVO::getPeriodId));
        for (Map.Entry<String, List<CodeReplacementVO>> entry : newCollect.entrySet()) {
            if (CollectionUtil.isNullOrEmpty(newCollect.get(entry.getKey())) && CollectionUtil.isNullOrEmpty(
                    oldCollect.get(entry.getKey()))) {
                continue;
            } else if( StringUtils.isEmpty(
                    newCollect.get(entry.getKey()).get(0).getRmbCostAmt()) || StringUtils.isEmpty(
                    oldCollect.get(entry.getKey()).get(0).getRmbCostAmt())){
                continue;
            }
            BigDecimal newSpartValue = new BigDecimal(newCollect.get(entry.getKey()).get(0).getRmbCostAmt());
            BigDecimal oldSpartValue = new BigDecimal(oldCollect.get(entry.getKey()).get(0).getRmbCostAmt());
            Map<String, String> infoMap = new HashMap<>();
            BigDecimal subtract = newSpartValue.subtract(oldSpartValue).divide(new BigDecimal(10000));
            totalNumber = totalNumber.add(subtract);
            infoMap.put("costCvAmt", subtract.setScale(8, BigDecimal.ROUND_HALF_UP).toPlainString());
            if (oldSpartValue.compareTo(BigDecimal.ZERO) == 0) {
                infoMap.put("costCvRatio", "");
            } else {
                infoMap.put("costCvRatio", (newSpartValue.divide(oldSpartValue, 8, BigDecimal.ROUND_HALF_UP)).multiply(new BigDecimal(100)).toPlainString()
                        + "%");
            }
            dataMap.put(entry.getKey(), infoMap);
        }
        Map<String, String> totalInfo = new HashMap<>();
        totalInfo.put("costCvAmt", totalNumber.setScale(8, BigDecimal.ROUND_HALF_UP).toPlainString());
        dataMap.put("总计", totalInfo);
        return dataMap;
    }

    public static List<CodeReplacementExpVO> TranMapToList(Map<String, Map<String, String>> newInfoMap,
                                                           Map<String, Map<String, String>> oldInfoMap) {
        List<CodeReplacementExpVO> infoList = new ArrayList<>();
        List<String> peroidList = new ArrayList<>();

        if (!CollectionUtil.isNullOrEmpty(newInfoMap)) {
            for (Map.Entry<String, Map<String, String>> entry : newInfoMap.entrySet()) {
                String key = entry.getKey();
                Map<String, String> value = entry.getValue();
                if (CollectionUtil.isNullOrEmpty(value)) {
                    continue;
                }
                peroidList.add(key);
                CodeReplacementExpVO data = new CodeReplacementExpVO();
                data.setPeriodId(key);
                data.setCostCvAmt(null);
                data.setTheCVRatio(value.get("costCvRatio"));
                data.setTheCVValue(value.get("costCvAmt"));
                if (!CollectionUtil.isNullOrEmpty(oldInfoMap.get(key))) {
                    data.setTheAccCVRatio(oldInfoMap.get(key).get("costCvRatio"));
                    data.setTheAccCVValue(oldInfoMap.get(key).get("costCvAmt"));
                }
                infoList.add(data);
            }
        }
        if (!CollectionUtil.isNullOrEmpty(oldInfoMap)) {
            for (Map.Entry<String, Map<String, String>> entry : oldInfoMap.entrySet()) {
                String key = entry.getKey();
                Map<String, String> value = entry.getValue();
                if (CollectionUtil.isNullOrEmpty(value) || peroidList.contains(key)) {
                    continue;
                }
                peroidList.add(key);
                CodeReplacementExpVO data = new CodeReplacementExpVO();
                data.setCostCvAmt(null);
                data.setPeriodId(key);
                data.setTheAccCVRatio(value.get("costCvRatio"));
                data.setTheAccCVValue(value.get("costCvAmt"));
                infoList.add(data);
            }
        }
        return infoList.stream()
                .sorted(Comparator.comparing(CodeReplacementExpVO::getPeriodId))
                .collect(Collectors.toList());
    }

    public static void getPreCusIndexCostList(CodeReplacementVO dataVO) {
        String lvCodeStr = CostReductUtils.setProdTeamCode(dataVO, "");
        dataVO.setProdRndTeamCode(lvCodeStr);
        if (StringUtils.isNotBlank(dataVO.getRelationType()) && StringUtils.isNotBlank(dataVO.getReplaceRelationType())) {
            dataVO.setGroupLevel(dataVO.getPbiLevel() + "_" + "REPLACE_RELATION");
            dataVO.setGroupCode(dataVO.getReplaceRelationType() + dataVO.getRelationType());
        } else if (StringUtils.isNotBlank(dataVO.getRelationType())) {
            dataVO.setGroupLevel(dataVO.getPbiLevel() + "_" + "RELATION");
            dataVO.setGroupCode(dataVO.getRelationType());
        } else {
            dataVO.setGroupLevel(dataVO.getPbiLevel() + "_" + "REPLACE");
            dataVO.setGroupCode(dataVO.getReplaceRelationType());
        }
    }

    public static boolean getSpecailRoleMark(CodeReplacementExpVO dataVO) {
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        // 行销分析师角色拦截
        if (CommonConstant.SPECIAL_ROLES.contains(currentRole.getRoleName()) && CommonConstant.GRANULARITY_TYPE_NOT_PROD_LIST.contains(dataVO.getGranularityType())) {
            return true;
        }
        return false;
    }

    public static List<CodeReplacementExpVO> processIndexList(List<CodeReplacementVO> aNew, List<CodeReplacementVO> old) {
        List<CodeReplacementExpVO> infoList = new ArrayList<>();
        List<String> peroidList = new ArrayList<>();
        Map<String, List<CodeReplacementVO>> oldInfo = old.stream()
                .collect(Collectors.groupingBy(CodeReplacementVO::getPeriodId));
        Map<String, List<CodeReplacementVO>> newInfo = aNew.stream()
                .collect(Collectors.groupingBy(CodeReplacementVO::getPeriodId));
        newInfoList(infoList, peroidList, oldInfo, newInfo);
        oldInfoList(infoList, peroidList, oldInfo);

        return infoList.stream()
                .sorted(Comparator.comparing(CodeReplacementExpVO::getPeriodId))
                .collect(Collectors.toList());
    }

    private static void newInfoList(List<CodeReplacementExpVO> infoList, List<String> peroidList, Map<String, List<CodeReplacementVO>> oldInfo, Map<String, List<CodeReplacementVO>> newInfo) {
        if (!CollectionUtil.isNullOrEmpty(newInfo)) {
            for (Map.Entry<String, List<CodeReplacementVO>> entry : newInfo.entrySet()) {
                String key = entry.getKey();
                List<CodeReplacementVO> value = entry.getValue();
                if (CollectionUtil.isNullOrEmpty(value)) {
                    continue;
                }
                peroidList.add(key);
                CodeReplacementVO newData = value.get(0);
                CodeReplacementExpVO data = BeanUtil.fillBeanWithMap(BeanUtil.beanToMap(newData),
                        new CodeReplacementExpVO(), false, true);
                data.setCostIndex(null);
                data.setNewCostIndex(
                        (new BigDecimal(newData.getCostIndex()).setScale(1, BigDecimal.ROUND_HALF_UP)).toString());
                if (!CollectionUtil.isNullOrEmpty(oldInfo.get(key))) {
                    data.setOldCostIndex((new BigDecimal(oldInfo.get(key).get(0).getCostIndex()).setScale(1,
                            BigDecimal.ROUND_HALF_UP)).toString());
                }
                infoList.add(data);
            }
        }
    }

    private static void oldInfoList(List<CodeReplacementExpVO> infoList, List<String> peroidList, Map<String, List<CodeReplacementVO>> oldInfo) {
        if (!CollectionUtil.isNullOrEmpty(oldInfo)) {
            for (Map.Entry<String, List<CodeReplacementVO>> entry : oldInfo.entrySet()) {
                List<CodeReplacementVO> value = entry.getValue();
                String key = entry.getKey();
                if (CollectionUtil.isNullOrEmpty(value) || peroidList.contains(key)) {
                    continue;
                }
                peroidList.add(key);
                CodeReplacementVO oldData = value.get(0);
                CodeReplacementExpVO data = BeanUtil.fillBeanWithMap(BeanUtil.beanToMap(oldData),
                        new CodeReplacementExpVO(), false, true);
                data.setCostIndex(null);
                data.setOldCostIndex((new BigDecimal(oldInfo.get(key).get(0).getCostIndex()).setScale(1,
                        BigDecimal.ROUND_HALF_UP)).toString());
                infoList.add(data);
            }
        }
    }

    // 数据库是多个SPARTCODE 转成一行取数
    public static String getSpartCodeStr(List<String> oldSpartCode) {
        if (CollectionUtil.isNullOrEmpty(oldSpartCode)) {
            return "";
        } else {
            return oldSpartCode.stream().collect(Collectors.joining(","));
        }
    }
}
