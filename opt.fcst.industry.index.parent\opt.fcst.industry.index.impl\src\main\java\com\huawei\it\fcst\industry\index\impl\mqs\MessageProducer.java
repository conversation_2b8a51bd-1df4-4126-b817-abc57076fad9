/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.mqs;

import com.huawei.his.mqs.client.producer.Producer;
import com.huawei.his.mqs.client.producer.SendResult;
import com.huawei.his.mqs.common.exception.UmpException;
import com.huawei.his.mqs.common.message.Message;
import com.huawei.it.fcst.config.ConfigUtil;
import com.huawei.it.fcst.industry.index.utils.UmpClient;
import com.huawei.it.soa.util.SoaAppTokenClientUtil;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.UUID;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023年07月03日
 */
@Component
@Slf4j
public class MessageProducer {
    /**
     * 线程启动类
     *
     * @throws Exception 异常
     */
    public void startProducer() throws Exception {
        final Producer instanceProducer = UmpClient.INSTANCE.getProducer();
        if(instanceProducer.isStarted()){
            return;
        }
        instanceProducer.setUmpNamesrvUrls(ConfigUtil.getInstance().getTopicUrl());
        // 设置客户端账号
        instanceProducer.setAppId(SoaAppTokenClientUtil.getSoaAppId());
        // 设置客户端密钥
        instanceProducer.setAppSecret(SoaAppTokenClientUtil.getSoaCredential());
        // 设置Topic Name
        instanceProducer.setTopic(ConfigUtil.getInstance().getTopName());
        // 设置订阅消息的标签，可以指定消费某一类型的消息，默认*表示消费所有类型的消息
        instanceProducer.setTags("*");

        instanceProducer.setInstanceName(UUID.randomUUID().toString());
        // 设置是否需要加密传输
        instanceProducer.setEncryptTransport(false);
        // 启动消息生产者，建议在应用程序启动的时候调用
        instanceProducer.start();
        log.debug("start Message Producer");
    }


    /**
     * 获取UMP生产者实例
     *
     * @return 生产者实例
     */
    private Producer getProducer() {
        final Producer producer = UmpClient.INSTANCE.getProducer();
        try {
            if (!producer.isStarted()) {
                startProducer();
            }
        } catch (Exception e) {
            log.error("Get Producer ERROR.", e);
        }
        return producer;
    }

    /**
     * 发送UMP消息
     *
     * @param message 消息体
     */
    public boolean sendMessage(Message message) {
        try {
            SendResult sendResult = getProducer().send(message);
            return sendResult.isSuccess();
        } catch (UmpException e) {
            log.error("Send Msg error .", e);
            return false;
        }
    }

    /**
     * 应用停止时停止生产者
     *
     * @throws UmpException 异常
     */
    public void stopProducer() throws UmpException {
        final Producer instanceProducer = UmpClient.INSTANCE.getProducer();
        if(instanceProducer.isStarted()){
            instanceProducer.shutdown();
        }
    }
}
