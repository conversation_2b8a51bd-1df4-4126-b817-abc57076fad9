/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.month;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.huawei.it.fcst.industry.index.constant.CommonConstant;
import com.huawei.it.fcst.industry.index.constant.Constant;
import com.huawei.it.fcst.industry.index.dao.IDataCipherTextDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocActualCostDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeActualCostDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeMonthWeightDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeRecMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMonthWeightDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocRecMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTotalActualCostDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTotalMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTotalMonthWeightDao;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumD;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumP;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumU;
import com.huawei.it.fcst.industry.index.enums.IndustryConst;
import com.huawei.it.fcst.industry.index.enums.IndustryIndexEnum;
import com.huawei.it.fcst.industry.index.impl.annual.AnnualCommonService;
import com.huawei.it.fcst.industry.index.service.common.ICommonService;
import com.huawei.it.fcst.industry.index.utils.FcstIndexMadeUtil;
import com.huawei.it.fcst.industry.index.utils.FcstIndexUtil;
import com.huawei.it.fcst.industry.index.utils.ObjectCopyUtil;
import com.huawei.it.fcst.industry.index.vo.ciphertext.VarifyTaskVO;
import com.huawei.it.fcst.industry.index.vo.common.CommonViewVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.month.DmFocActualCostVO;
import com.huawei.it.fcst.industry.index.vo.month.DmFocMonthCostIdxVO;
import com.huawei.it.fcst.industry.index.vo.month.DmFocMonthWeightVO;
import com.huawei.it.fcst.industry.index.vo.month.DmFocTotalActualCostVO;
import com.huawei.it.fcst.industry.index.vo.month.ExportFocActualCostVO;
import com.huawei.it.fcst.industry.index.vo.month.ExportMonthWeightVO;
import com.huawei.it.fcst.industry.index.vo.month.IndustryIndexExpVO;
import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;
import com.huawei.it.fcst.industry.index.vo.month.MonthYoyExpVO;
import com.huawei.it.fcst.industry.index.vo.month.MultiIndexExportVO;
import com.huawei.it.fcst.util.ExcelExportUtil;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.jetbrains.annotations.NotNull;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Inject;
import javax.inject.Named;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * AsyncExportService Class
 *
 * <AUTHOR>
 * @since 2023/03/15
 */
@Slf4j
@EnableAsync
@Named(value = "asyncExportService")
public class AsyncExportService {
    @Inject
    private IDmFocMonthCostIdxDao dmFocMonthCostIdxDao;

    @Inject
    private IDmFocMonthWeightDao dmFocMonthWeightDao;

    @Inject
    private IDmFocActualCostDao dmFocActualCostDao;

    @Inject
    private IDmFocMadeActualCostDao dmFocMadeActualCostDao;

    @Inject
    private IDmFocTotalActualCostDao dmFocTotalActualCostDao;

    @Inject
    private ICommonService commonService;

    @Inject
    private MonthCommonService monthCommonService;

    @Inject
    private AnnualCommonService annualCommonService;

    @Inject
    private IDataCipherTextDao iDataCipherTextDao;

    @Inject
    private IDmFocRecMonthCostIdxDao dmFocRecMonthCostIdxDao;

    @Inject
    private IDmFocMadeRecMonthCostIdxDao dmFocMadeRecMonthCostIdxDao;

    @Inject
    private IDmFocMadeMonthCostIdxDao dmFocMadeMonthCostIdxDao;

    @Inject
    private IDmFocTotalMonthCostIdxDao dmFocTotalMonthCostIdxDao;

    @Inject
    private IDmFocMadeMonthWeightDao dmFocMadeMonthWeightDao;

    @Inject
    private IDmFocTotalMonthWeightDao dmFocTotalMonthWeightDao;

    private List<DmFocViewInfoVO> getBgList(MonthAnalysisVO monthAnalysisVO) {
        CommonViewVO commonViewVO = new CommonViewVO();
        commonViewVO.setGranularityType(monthAnalysisVO.getGranularityType());
        commonViewVO.setViewFlag(monthAnalysisVO.getViewFlag());
        commonViewVO.setCaliberFlag(monthAnalysisVO.getCaliberFlag());
        commonViewVO.setLv0ProdListCode(monthAnalysisVO.getLv0ProdListCode());
        commonViewVO.setCostType(monthAnalysisVO.getCostType());
        commonViewVO.setIndustryOrg(monthAnalysisVO.getIndustryOrg());
        ResultDataVO bgInfoList = annualCommonService.getBgInfoList(commonViewVO);
        return (List<DmFocViewInfoVO>) bgInfoList.getData();
    }

    /**
     * 填充热力图sheet页数据
     *
     * @param searchParamsVO 参数参数VO
     * @param workbook 工作簿
     * @param sheetIdx sheet页下标
     * @param groupCnName groupCnName
     * @param current 当前请求上下文
     * @return Future<Integer> total rows
     * @throws Exception
     */
    @Async("asyncServiceExecutor")
    public Future<Integer> fillHeapTemplate1Sheet(MonthAnalysisVO searchParamsVO, Workbook workbook,
            int sheetIdx, String groupCnName, IRequestContext current) throws Exception {
        log.info(">>>Begin AsyncExportService::fillHeapTemplate1Sheet");
        // 4、热力图导出数据
        RequestContextManager.setCurrent(current);
        MonthAnalysisVO paramsVO = ObjectCopyUtil.copy(searchParamsVO, MonthAnalysisVO.class);
        paramsVO.setParentCodeList(paramsVO.getGroupCodeList());
        paramsVO.setParentLevel(searchParamsVO.getGroupLevel());
        // 查询下一层级的数据
        // 分视角获取下个层级的groupLevel值和对应中文名称
        Map map = getGroupCodeLevelMap(paramsVO);
        paramsVO.setGroupLevel(map.get("nextGroupLevel").toString());
        commonService.setProdRndTeamCode(paramsVO);
        if (GroupLevelEnumP.L2.getValue().equals(paramsVO.getGroupLevel())) {
            paramsVO.setProfitsName(null);
        }
        // 处理36个月的会计期
        FcstIndexUtil.handlePeriod(paramsVO, dmFocMonthCostIdxDao.findActualMonthNum(paramsVO.getTablePreFix()).toString());
        // 名称需要拼接
        String title = groupCnName+"-多"+ map.get("nextGroupName").toString();
        String heapTitle = FcstIndexUtil.getTitle(Constant.StrEnum.HEAP_TITLE.getValue(), title);
        // 设置第1行标题
        Sheet heapSheet = workbook.getSheetAt(sheetIdx);
        Row heapSheetRow = heapSheet.getRow(0);
        heapSheetRow.getCell(0).setCellValue(heapTitle);
        heapSheetRow.getCell(5).setCellValue(Constant.StrEnum.CALIBER_FLAG.getValue() +  IndustryIndexEnum.getCaliberFlag(paramsVO.getCaliberFlag()).getDesc());
        DmFocViewInfoVO dmFocViewInfoVO = getDmFocViewInfoVO(searchParamsVO);
        // 设置第2行第4列标题名称
        String name = map.get("nextGroupName").toString();
        String heapName = FcstIndexUtil.getTitle(Constant.StrEnum.HEAP_TITLE2.getValue(), name);
        heapSheet.getRow(1).getCell(3).setCellValue(heapName);
        heapSheet.getRow(1).getCell(5).setCellValue(Constant.StrEnum.COST_TYPE.getValue() + IndustryIndexEnum.getCostType(paramsVO.getCostType()).getDesc());
        List<DmFocActualCostVO> heapMapExpData = getHeapMapExpDataList(paramsVO);
        if (paramsVO.getIsMultipleSelect()) {
            // 名称拼接
            monthCommonService.mutilSelectGroupCnName(paramsVO, null,
                    null, null, heapMapExpData, "heatMapList");
        }
        new ExcelExportUtil<DmFocActualCostVO>(2, 2, DmFocActualCostVO.class)
            .fillSheetData(heapSheet, heapMapExpData);
        heapSheet.getRow(2).getCell(5).setCellValue(Constant.StrEnum.GRANULE.getValue() + IndustryIndexEnum.getGranularityType(paramsVO.getGranularityType()).getDesc());
        heapSheet.getRow(3).getCell(5).setCellValue(Constant.StrEnum.OVERSEA_FLAG.getValue() + IndustryConst.getOverseaFlag(paramsVO.getOverseaFlag()).getDesc());
        heapSheet.getRow(4).getCell(5).setCellValue(Constant.StrEnum.BG_FLAG.getValue() + getBgList(paramsVO).get(0).getLv0ProdListCnName());
        heapSheet.getRow(5).getCell(5).setCellValue(Constant.StrEnum.INDUSTRY_VIEW.getValue()+ dmFocViewInfoVO.getViewFlagValue());
        heapSheet.getRow(6).getCell(5).setCellValue(dmFocMonthCostIdxDao.findActualMonth(paramsVO.getTablePreFix()));
        Cell cell = heapSheet.getRow(7).getCell(5);
        if (cell == null) {
            cell = heapSheet.getRow(7).createCell(5);
        }
        cell.setCellValue(FcstIndexUtil.getBasePeriodStr(searchParamsVO.getBasePeriodId()));
        log.info(">>>End AsyncExportService::fillHeapTemplate1Sheet");
        return new AsyncResult<>(heapMapExpData.size());
    }

    @NotNull
    private DmFocViewInfoVO getDmFocViewInfoVO(MonthAnalysisVO searchParamsVO) throws ApplicationException {
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setViewFlag(searchParamsVO.getViewFlag());
        dmFocViewInfoVO.setGranularityType(searchParamsVO.getGranularityType());
        dmFocViewInfoVO.setCostType(searchParamsVO.getCostType());
        dmFocViewInfoVO.setIndustryOrg(searchParamsVO.getIndustryOrg());
        commonService.setViewFlagValueWithLookUp(dmFocViewInfoVO);
        return dmFocViewInfoVO;
    }

    /**
     * 填充热力图sheet页数据
     *
     * @param searchParamsVO 参数参数VO
     * @param workbook 工作簿
     * @param sheetIdx sheet页下标
     * @param groupCnName groupCnName
     * @param current 当前请求上下文
     * @return Future<Integer> total rows
     * @throws Exception
     */
    @Async("asyncServiceExecutor")
    public Future<Integer> fillHeapTemplate2Sheet(MonthAnalysisVO searchParamsVO, Workbook workbook,
        int sheetIdx, String groupCnName, IRequestContext current) throws Exception {
        log.info(">>>Begin AsyncExportService::fillHeapTemplate2Sheet");
        // 4、热力图导出数据
        RequestContextManager.setCurrent(current);
        MonthAnalysisVO paramsVO = ObjectCopyUtil.copy(searchParamsVO, MonthAnalysisVO.class);
        paramsVO.setParentCodeList(paramsVO.getGroupCodeList());
        paramsVO.setParentLevel(searchParamsVO.getGroupLevel());
        // 查询下一层级的数据
        // 分视角获取下个层级的groupLevel值和对应中文名称
        Map map = getGroupCodeLevelMap(paramsVO);
        paramsVO.setGroupLevel(map.get("nextGroupLevel").toString());
        // 除过视角1,ICT和LV1层级不需要传入重量级团队code值
        commonService.setProdRndTeamCode(paramsVO);
        if (GroupLevelEnumP.L2.getValue().equals(paramsVO.getGroupLevel())) {
            paramsVO.setProfitsName(null);
        }
        // 处理36个月的会计期
        FcstIndexUtil.handlePeriod(paramsVO, dmFocMonthCostIdxDao.findActualMonthNum(paramsVO.getTablePreFix()).toString());
        List<DmFocActualCostVO> heapMapExpData = getHeapMapExpDataList(paramsVO);
        if (paramsVO.getIsMultipleSelect()) {
            // 名称拼接
            monthCommonService.mutilSelectGroupCnName(paramsVO, null,
                    null, null, heapMapExpData, "heatMapList");
        }
        Sheet heapSheet = workbook.getSheetAt(sheetIdx);
        // 名称需要拼接
        String title = groupCnName+"-多"+ map.get("nextGroupName").toString();
        String heapTitle = FcstIndexUtil.getTitle(Constant.StrEnum.HEAP_TITLE.getValue(), title);
        Row heapSheetRow = heapSheet.getRow(0);
        // 设置第1行标题
        heapSheetRow.getCell(0).setCellValue(heapTitle);
        DmFocViewInfoVO dmFocViewInfoVO = getDmFocViewInfoVO(searchParamsVO);
        heapSheet.getRow(0).getCell(6).setCellValue(Constant.StrEnum.CALIBER_FLAG.getValue() + IndustryIndexEnum.getCaliberFlag(paramsVO.getCaliberFlag()).getDesc());
        // 设置第2行第2列标题名称
        String name = map.get("nextGroupName").toString();
        String titleName = FcstIndexUtil.getTitle(Constant.StrEnum.HEAP_TITLE2.getValue(), name);
        String titleCode = FcstIndexUtil.getTitle(Constant.StrEnum.HEAP_TITLE3.getValue(), name);
        heapSheet.getRow(1).getCell(3).setCellValue(titleName);
        heapSheet.getRow(1).getCell(4).setCellValue(titleCode);
        heapSheet.getRow(1).getCell(6).setCellValue(Constant.StrEnum.COST_TYPE.getValue() + IndustryIndexEnum.getCostType(paramsVO.getCostType()).getDesc());
        List<ExportFocActualCostVO> exportFocActualCostVOList = getExportFocActualCostList(heapMapExpData);
        new ExcelExportUtil<ExportFocActualCostVO>(2, 2, ExportFocActualCostVO.class)
                .fillSheetData(heapSheet, exportFocActualCostVOList);
        heapSheet.getRow(2).getCell(6).setCellValue(Constant.StrEnum.GRANULE.getValue() + IndustryIndexEnum.getGranularityType(paramsVO.getGranularityType()).getDesc());
        heapSheet.getRow(3).getCell(6).setCellValue(Constant.StrEnum.OVERSEA_FLAG.getValue() + IndustryConst.getOverseaFlag(paramsVO.getOverseaFlag()).getDesc());
        heapSheet.getRow(4).getCell(6).setCellValue(Constant.StrEnum.BG_FLAG.getValue() + getBgList(paramsVO).get(0).getLv0ProdListCnName());
        heapSheet.getRow(5).getCell(6).setCellValue(Constant.StrEnum.INDUSTRY_VIEW.getValue()+ dmFocViewInfoVO.getViewFlagValue());
        heapSheet.getRow(6).getCell(6).setCellValue(dmFocMonthCostIdxDao.findActualMonth(paramsVO.getTablePreFix()));
        Cell cell = heapSheet.getRow(7).getCell(6);
        if (cell == null) {
            cell = heapSheet.getRow(7).createCell(6);
        }
        cell.setCellValue(FcstIndexUtil.getBasePeriodStr(searchParamsVO.getBasePeriodId()));
        log.info(">>>End AsyncExportService::fillHeapTemplate2Sheet");
        return new AsyncResult<>(heapMapExpData.size());
    }

    private List<DmFocActualCostVO> getHeapMapExpDataList(MonthAnalysisVO paramsVO) {
        List<DmFocActualCostVO> heapMapExpData = new ArrayList<>();
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(paramsVO.getCostType())) {
            heapMapExpData = dmFocActualCostDao.findHeapMapExpData(paramsVO);
        }
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(paramsVO.getCostType())) {
            heapMapExpData = dmFocMadeActualCostDao.findHeapMapExpData(paramsVO);
        }
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(paramsVO.getCostType())) {
            heapMapExpData.addAll(dmFocActualCostDao.findHeapMapExpData(paramsVO));
            heapMapExpData.addAll(dmFocMadeActualCostDao.findHeapMapExpData(paramsVO));
            heapMapExpData.addAll(dmFocTotalActualCostDao.findHeapMapExpData(paramsVO));
        }
        heapMapExpData.stream().forEach(heap->heap.setCostType(IndustryIndexEnum.getCostType(heap.getCostType()).getDesc()));
        monthCommonService.splicingHeatMapCodeAndName(heapMapExpData);
        return heapMapExpData;
    }

    @Async("asyncServiceExecutor")
    @Transactional(propagation=Propagation.NOT_SUPPORTED)
    public Future<Integer> fillDistributeCostSheet(MonthAnalysisVO searchParamsVO, Workbook workbook, int sheetIdx,
                                                   String groupCnName, IRequestContext current) throws Exception {
        log.info(">>>Begin AsyncExportService::fillDistributeCostSheet");
        // 成本分布图
        RequestContextManager.setCurrent(current);
        MonthAnalysisVO paramsVO = ObjectCopyUtil.copy(searchParamsVO, MonthAnalysisVO.class);
        paramsVO.setParentLevel(paramsVO.getParentLevel());
        paramsVO.setParentCodeList(paramsVO.getGroupCodeList());
        Long actualMonthNum = dmFocMonthCostIdxDao.findActualMonthNum(paramsVO.getTablePreFix());
        FcstIndexUtil.handlePeriod(paramsVO, actualMonthNum.toString());
        paramsVO.setPeriodEndTime(Integer.valueOf(actualMonthNum.toString()));
        List<DmFocActualCostVO> costDistributionList = dmFocActualCostDao.findCostDistributionList(paramsVO);
        if (paramsVO.getIsMultipleSelect()) {
            // 采购层级名称拼接
            monthCommonService.heatMapMutilSelectGroupCnName(paramsVO, costDistributionList);
        }
        Sheet distributionSheet = workbook.getSheetAt(sheetIdx);
        String distributionTitle = FcstIndexUtil.getTitle(Constant.StrEnum.DISTRIBUTE_TITLE.getValue(), groupCnName);
        distributionSheet.getRow(0).getCell(0).setCellValue(distributionTitle);
        distributionSheet.getRow(0).getCell(7).setCellValue(Constant.StrEnum.CALIBER_FLAG.getValue() + IndustryIndexEnum.getCaliberFlag(paramsVO.getCaliberFlag()).getDesc());
        DmFocViewInfoVO dmFocViewInfoVO = getDmFocViewInfoVO(searchParamsVO);
        List<DmFocTotalActualCostVO> dmFocTotalActualCostVOList = new ArrayList<>();
        costDistributionList.stream().forEach(cost->{
            DmFocTotalActualCostVO dmFocTotalActualCostVO = ObjectCopyUtil.copy(cost, DmFocTotalActualCostVO.class);
            dmFocTotalActualCostVO.setPurWeight(cost.getPurWeight() +"%");
            dmFocTotalActualCostVO.setMadeWeight(cost.getMadeWeight() +"%");
            if (GroupLevelEnumD.DIMENSION.getValue().equals(paramsVO.getGroupLevel()) || GroupLevelEnumD.SUBCATEGORY.getValue().equals(paramsVO.getGroupLevel()) || GroupLevelEnumD.SUB_DETAIL.getValue().equals(paramsVO.getGroupLevel()) ) {
                dmFocTotalActualCostVO.setGroupCnName(dmFocTotalActualCostVO.getGroupCode() + " " + dmFocTotalActualCostVO.getGroupCnName());
            }
            dmFocTotalActualCostVOList.add(dmFocTotalActualCostVO);
        });

        distributionSheet.getRow(1).getCell(7).setCellValue(Constant.StrEnum.COST_TYPE.getValue() + IndustryIndexEnum.getCostType(paramsVO.getCostType()).getDesc());
        new ExcelExportUtil<DmFocTotalActualCostVO>(2, 2,
                DmFocTotalActualCostVO.class).fillSheetData(distributionSheet, dmFocTotalActualCostVOList);
        distributionSheet.getRow(2).getCell(7).setCellValue(Constant.StrEnum.GRANULE.getValue() + IndustryIndexEnum.getGranularityType(paramsVO.getGranularityType()).getDesc());
        distributionSheet.getRow(3).getCell(7).setCellValue(Constant.StrEnum.OVERSEA_FLAG.getValue() + IndustryConst.getOverseaFlag(paramsVO.getOverseaFlag()).getDesc());
        distributionSheet.getRow(4).getCell(7).setCellValue(Constant.StrEnum.BG_FLAG.getValue() + getBgList(paramsVO).get(0).getLv0ProdListCnName());
        distributionSheet.getRow(5).getCell(7).setCellValue(Constant.StrEnum.INDUSTRY_VIEW.getValue() + dmFocViewInfoVO.getViewFlagValue());
        distributionSheet.getRow(6).getCell(7).setCellValue(dmFocMonthCostIdxDao.findActualMonth(paramsVO.getTablePreFix()));
        Cell cell = distributionSheet.getRow(7).getCell(7);
        cell.setCellValue(FcstIndexUtil.getBasePeriodStr(paramsVO.getBasePeriodId()));
        log.info(">>>End AsyncExportService::fillDistributeCostSheet");
        return new AsyncResult<>(costDistributionList.size());
    }

    @NotNull
    private List<ExportFocActualCostVO> getExportFocActualCostList(List<DmFocActualCostVO> heapMapExpData) {
        List<ExportFocActualCostVO> exportFocActualCostVOList = new ArrayList<>();
        Optional.ofNullable(heapMapExpData).orElse(new ArrayList<>()).stream().forEach(costItem ->{
            ExportFocActualCostVO focActualCostVO = new ExportFocActualCostVO();
            focActualCostVO.setPeriodId(costItem.getPeriodId());
            focActualCostVO.setCostType(costItem.getCostType());
            focActualCostVO.setParentCnName(costItem.getParentCnName());
            focActualCostVO.setGroupCode(costItem.getGroupCode());
            focActualCostVO.setGroupCnName(costItem.getGroupCnName());
            focActualCostVO.setActualCostAmt(costItem.getActualCostAmt());
            exportFocActualCostVOList.add(focActualCostVO);
        });
        return exportFocActualCostVOList;
    }

    /**
     * 填充产业成本指数图sheet页数据
     *
     * @param searchParamsVO 参数参数VO
     * @param workbook 工作簿
     * @param sheetIdx sheet页下标
     * @param groupCnName groupCnName
     * @param current 当前请求上下文
     * @return Future<Integer> total rows
     * @throws Exception
     */
    @Async("asyncServiceExecutor")
    public Future<Integer> fillPriceIdxSheet(Workbook workbook, int sheetIdx, String groupCnName,
                                             MonthAnalysisVO searchParamsVO, IRequestContext current) throws Exception {
        log.info(">>>Begin AsyncExportService::fillPriceIdxSheet");
        // 1、查询产业成本指数图导出数据
        RequestContextManager.setCurrent(current);
        MonthAnalysisVO indexParamsVO = ObjectCopyUtil.copy(searchParamsVO, MonthAnalysisVO.class);
        indexParamsVO.setType("exp");
        // 对比分析参数处理
        ResultDataVO industryCostIndexChartVO = new ResultDataVO();
        if (indexParamsVO.getCompareAnalysisVO().getIsCompareFlag()) {
            List<String> compareCnNameList = new ArrayList<>();
            List<MonthAnalysisVO> compareMonthVO = indexParamsVO.getCompareAnalysisVO().getCompareMonthVO();
            if (CollectionUtils.isNotEmpty(compareMonthVO)) {
                indexParamsVO.setBasePeriodId(compareMonthVO.get(0).getBasePeriodId());
                for (MonthAnalysisVO compareMonthAnalysisVO : compareMonthVO) {
                    compareMonthAnalysisVO.setType("exp");
                    compareCnNameList.addAll(compareMonthAnalysisVO.getGroupCnNameList());
                }
                industryCostIndexChartVO = monthCommonService.getCompareIndexChart(compareMonthVO);
            }
            groupCnName = Constant.StrEnum.COMPARE_FLAG.getValue() + "(" + compareCnNameList.stream().map(String::valueOf).collect(Collectors.joining(",")) + ")";
        } else {
            industryCostIndexChartVO = monthCommonService.getIndustryCostIndexChart(indexParamsVO);
        }
        List<DmFocMonthCostIdxVO> priceIndexVOList = (List<DmFocMonthCostIdxVO>) industryCostIndexChartVO.getData();
        // 获取标题
        String indexTitle = getTitleByCostType(indexParamsVO, groupCnName);
        Sheet indexSheet = workbook.getSheetAt(sheetIdx);
        indexSheet.getRow(0).getCell(0).setCellValue(indexTitle);
        indexSheet.getRow(0).getCell(4).setCellValue(Constant.StrEnum.CALIBER_FLAG.getValue() + IndustryIndexEnum.getCaliberFlag(indexParamsVO.getCaliberFlag()).getDesc());
        indexSheet.getRow(1).getCell(4).setCellValue(Constant.StrEnum.COST_TYPE.getValue() + IndustryIndexEnum.getCostType(indexParamsVO.getCostType()).getDesc());
        DmFocViewInfoVO dmFocViewInfoVO = getDmFocViewInfoVO(searchParamsVO);

        List<IndustryIndexExpVO> priceIndexExpVOList = new ArrayList<>();
        // 设置字段
        setPriceIndexExcelColumn(priceIndexExpVOList, priceIndexVOList);
        // 填充采购价格指数图Sheet数据
        new ExcelExportUtil<IndustryIndexExpVO>(2, 2, IndustryIndexExpVO.class).fillSheetData(indexSheet, priceIndexExpVOList);
        indexSheet.getRow(2).getCell(4).setCellValue(Constant.StrEnum.GRANULE.getValue() + IndustryIndexEnum.getGranularityType(indexParamsVO.getGranularityType()).getDesc());
        indexSheet.getRow(3).getCell(4).setCellValue(Constant.StrEnum.OVERSEA_FLAG.getValue() + IndustryConst.getOverseaFlag(indexParamsVO.getOverseaFlag()).getDesc());
        indexSheet.getRow(4).getCell(4).setCellValue(Constant.StrEnum.BG_FLAG.getValue() + getBgList(indexParamsVO).get(0).getLv0ProdListCnName());
        indexSheet.getRow(5).getCell(4).setCellValue(Constant.StrEnum.INDUSTRY_VIEW.getValue() + dmFocViewInfoVO.getViewFlagValue());
        indexSheet.getRow(6).getCell(4).setCellValue(dmFocMonthCostIdxDao.findActualMonth(searchParamsVO.getTablePreFix()));
        Cell cell = indexSheet.getRow(7).getCell(4);
        if (cell == null) {
            cell = indexSheet.getRow(7).createCell(4);
        }
        cell.setCellValue(FcstIndexUtil.getBasePeriodStr(indexParamsVO.getBasePeriodId()));
        log.info(">>>End AsyncExportService::fillPriceIdxSheet");
        return new AsyncResult<>(priceIndexExpVOList.size());
    }

    private String getTitleByCostType(MonthAnalysisVO indexParamsVO, String groupCnName) {
        String indexTitle;
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(indexParamsVO.getCostType())) {
            indexTitle = FcstIndexUtil.getTitle(Constant.StrEnum.PURCHASE_INDEX_TITLE.getValue(), groupCnName);
        } else if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(indexParamsVO.getCostType())) {
            indexTitle = FcstIndexUtil.getTitle(Constant.StrEnum.MANUFACTURE_INDEX_TITLE.getValue(), groupCnName);
        } else {
            indexTitle = FcstIndexUtil.getTitle(Constant.StrEnum.TOTAL_INDEX_TITLE.getValue(), groupCnName);
        }
        return indexTitle;
    }

    private void setPriceIndexExcelColumn(List<IndustryIndexExpVO> priceIndexExpVOList, List<DmFocMonthCostIdxVO> priceIndexVOList) {
        Optional.ofNullable(priceIndexVOList).orElse(new ArrayList<>()).stream().forEach(item -> {
            IndustryIndexExpVO industryIndexExpVO = IndustryIndexExpVO.builder()
                    .periodId(item.getPeriodId())
                    .costType(IndustryIndexEnum.getCostType(item.getCostType()).getDesc())
                    .groupCnName(item.getGroupCnName())
                    .costIndex(null == item.getCostIndex() ? null : new BigDecimal(item.getCostIndex()).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue())
                    .build();
            priceIndexExpVOList.add(industryIndexExpVO);
        });
    }

    /**
     * 填充产业成本同比环比图sheet页数据
     *
     * @param searchParamsVO 参数参数VO
     * @param workbook 工作簿
     * @param sheetIdx sheet页下标
     * @param groupCnName groupCnName
     * @param current 当前请求上下文
     * @return Future<Integer> total rows
     * @throws Exception
     */
    @Async("asyncServiceExecutor")
    public Future<Integer> fillMonthYoySheet(Workbook workbook, int sheetIdx, String groupCnName,
        MonthAnalysisVO searchParamsVO, IRequestContext current) throws Exception {
        log.info(">>>Begin AsyncExportService::fillMonthYoySheet");
        // 2、查询产业成本指数同比环比图导出数据
        RequestContextManager.setCurrent(current);
        MonthAnalysisVO yoyParamsVO = ObjectCopyUtil.copy(searchParamsVO, MonthAnalysisVO.class);
        List<Map> monthYoyListForExp = monthCommonService.getMonthYoyListForExp(yoyParamsVO);
        String indexTitle;
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(yoyParamsVO.getCostType())) {
            indexTitle = FcstIndexUtil.getTitle(Constant.StrEnum.PURCHASE_INDEX_YEAR_TITLE.getValue(), groupCnName);
        } else if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(yoyParamsVO.getCostType())) {
            indexTitle = FcstIndexUtil.getTitle(Constant.StrEnum.MANUFACTURE_INDEX_YEAR_TITLE.getValue(), groupCnName);
        } else {
            indexTitle = FcstIndexUtil.getTitle(Constant.StrEnum.TOTAL_INDEX_YEAR_TITLE.getValue(), groupCnName);
        }
        Sheet indexSheet = workbook.getSheetAt(sheetIdx);
        indexSheet.getRow(0).getCell(0).setCellValue(indexTitle);
        indexSheet.getRow(0).getCell(5).setCellValue(Constant.StrEnum.CALIBER_FLAG.getValue() + IndustryIndexEnum.getCaliberFlag(yoyParamsVO.getCaliberFlag()).getDesc());
        indexSheet.getRow(1).getCell(5).setCellValue(Constant.StrEnum.COST_TYPE.getValue()+ IndustryIndexEnum.getCostType(yoyParamsVO.getCostType()).getDesc());
        DmFocViewInfoVO dmFocViewInfoVO = getDmFocViewInfoVO(searchParamsVO);
        List<MonthYoyExpVO> priceIndexExpVOList = new ArrayList<>();
        Boolean levelFlag = getLevelFlag(yoyParamsVO);
        Optional.ofNullable(monthYoyListForExp).orElse(new ArrayList<>()).stream().forEach(yoyItem -> {
            MonthYoyExpVO monthYoyExpVO = MonthYoyExpVO.builder()
                    .periodId(NumberUtil.parseLong(yoyItem.get("periodId").toString()))
                    .costType(IndustryIndexEnum.getCostType(yoyItem.get("costType").toString()).getDesc())
                    .groupCnName(levelFlag ? yoyItem.get("groupCode").toString()  + " " + yoyItem.get("groupCnName").toString() :yoyItem.get("groupCnName").toString())
                    .yoyPercent(yoyItem.get("YOY")==null?"":String.valueOf(yoyItem.get("YOY")))
                    .popPercent(yoyItem.get("POP")==null?"":String.valueOf(yoyItem.get("POP")))
                    .build();
            priceIndexExpVOList.add(monthYoyExpVO);
        });
        // 填充采购价格指数图Sheet数据
        new ExcelExportUtil<MonthYoyExpVO>(2, 2, MonthYoyExpVO.class).fillSheetData(indexSheet,
                priceIndexExpVOList);
        indexSheet.getRow(2).getCell(5).setCellValue(Constant.StrEnum.GRANULE.getValue() + IndustryIndexEnum.getGranularityType(yoyParamsVO.getGranularityType()).getDesc());
        indexSheet.getRow(3).getCell(5).setCellValue(Constant.StrEnum.OVERSEA_FLAG.getValue() + IndustryConst.getOverseaFlag(yoyParamsVO.getOverseaFlag()).getDesc());
        indexSheet.getRow(4).getCell(5).setCellValue(Constant.StrEnum.BG_FLAG.getValue() + getBgList(yoyParamsVO).get(0).getLv0ProdListCnName());
        indexSheet.getRow(5).getCell(5).setCellValue(Constant.StrEnum.INDUSTRY_VIEW.getValue() + dmFocViewInfoVO.getViewFlagValue());
        indexSheet.getRow(6).getCell(5).setCellValue(dmFocMonthCostIdxDao.findActualMonth(searchParamsVO.getTablePreFix()));
        Cell cell = indexSheet.getRow(7).getCell(5);
        if (cell == null) {
            cell = indexSheet.getRow(7).createCell(5);
        }
        cell.setCellValue(FcstIndexUtil.getBasePeriodStr(yoyParamsVO.getBasePeriodId()));
        log.info(">>>End AsyncExportService::fillMonthYoySheet");
        return new AsyncResult<>(priceIndexExpVOList.size());
    }

    @NotNull
    private Boolean getLevelFlag(MonthAnalysisVO yoyParamsVO) {
        return GroupLevelEnumD.DIMENSION.getValue().equals(yoyParamsVO.getGroupLevel())
                || GroupLevelEnumD.SUBCATEGORY.getValue().equals(yoyParamsVO.getGroupLevel())
                || GroupLevelEnumD.SUB_DETAIL.getValue().equals(yoyParamsVO.getGroupLevel())
                || GroupLevelEnumD.CATEGORY.getValue().equals(yoyParamsVO.getGroupLevel());
    }


    /**
     * 填充产业成本指数图（多指数）sheet页数据
     *
     * @param searchParamsVO 参数参数VO
     * @param workbook 工作簿
     * @param sheetIdx sheet页下标
     * @param current 当前请求上下文
     * @return Future<Integer> total rows
     * @throws Exception
     */
    @Async("asyncServiceExecutor")
    public Future<Integer> fillMultiIndexTemplate1Sheet(MonthAnalysisVO searchParamsVO, Workbook workbook,
        int sheetIdx, String groupCnName,IRequestContext current) throws Exception {
        log.info(">>>Begin AsyncExportService::fillMultiIndexTemplate1Sheet");
        // 3、产业成本指数图—多专家团指数导出数据
        RequestContextManager.setCurrent(current);
        MonthAnalysisVO mutilIndexVO = ObjectCopyUtil.copy(searchParamsVO, MonthAnalysisVO.class);
        mutilIndexVO.setReverseViewFlag(false);
        mutilIndexVO.setParentCodeList(mutilIndexVO.getGroupCodeList());
        // 分视角获取下个层级的groupLevel值和对应中文名称
        Map map = getGroupCodeLevelMap(mutilIndexVO);
        mutilIndexVO.setGroupLevel(map.get("nextGroupLevel").toString());
        // 反向视角标识设置
        Boolean reveseViewFlag = getReveseViewFlag(mutilIndexVO);
        List<DmFocMonthCostIdxVO> priceIndexExpData = new ArrayList<>();
        if (reveseViewFlag) {
            priceIndexExpData = getRevPriceIndexExpData(mutilIndexVO, priceIndexExpData);
        } else {
            // 除过视角1,ICT和LV1层级不需要传入重量级团队code值
            commonService.setProdRndTeamCode(mutilIndexVO);
            if (GroupLevelEnumP.L2.getValue().equals(mutilIndexVO.getGroupLevel())) {
                mutilIndexVO.setProfitsName(null);
            }
            priceIndexExpData = getPriceIndexExpDataList(mutilIndexVO, priceIndexExpData);
            // 第4步：循环groupCodeList，然后再根据groupCode查询对应的数据
            if (mutilIndexVO.getIsMultipleSelect()) {
                // 名称拼接
                monthCommonService.mutilSelectGroupCnName(mutilIndexVO, priceIndexExpData,
                        null, null, null, "mutilIndexCost");
            }
        }
        // 名字需要重新拼接
        String name;
        if (mutilIndexVO.getIsContainComb() && CollectionUtils.isNotEmpty(mutilIndexVO.getCustomIdList())) {
            name = groupCnName + "-多ITEM";
        } else {
            name = groupCnName + "-多" + map.get("nextGroupName").toString();
        }
        String multiIdxTitle = getMultiIdxTitle(mutilIndexVO, name);
        Sheet multiIdxSheet = workbook.getSheetAt(sheetIdx);
        multiIdxSheet.getRow(0).getCell(0).setCellValue(multiIdxTitle);
        multiIdxSheet.getRow(0).getCell(5).setCellValue(Constant.StrEnum.CALIBER_FLAG.getValue() + IndustryIndexEnum.getCaliberFlag(mutilIndexVO.getCaliberFlag()).getDesc());
        DmFocViewInfoVO dmFocViewInfoVO = getDmFocViewInfoVO(searchParamsVO);
        // 设置第2行第4列标题名称
        String titleName = FcstIndexUtil.getTitle(Constant.StrEnum.HEAP_TITLE2.getValue(), map.get("nextGroupName").toString());
        multiIdxSheet.getRow(1).getCell(3).setCellValue(titleName);
        multiIdxSheet.getRow(1).getCell(5).setCellValue(Constant.StrEnum.COST_TYPE.getValue()+ IndustryIndexEnum.getCostType(mutilIndexVO.getCostType()).getDesc());
        List<MultiIndexExportVO> multiIndexExportVOList = getMultiIndexExportVOList(priceIndexExpData);
        new ExcelExportUtil<MultiIndexExportVO>(2, 2, MultiIndexExportVO.class).fillSheetData(multiIdxSheet,
                multiIndexExportVOList);
        multiIdxSheet.getRow(2).getCell(5).setCellValue(Constant.StrEnum.GRANULE.getValue() + IndustryIndexEnum.getGranularityType(mutilIndexVO.getGranularityType()).getDesc());
        multiIdxSheet.getRow(3).getCell(5).setCellValue(Constant.StrEnum.OVERSEA_FLAG.getValue() + IndustryConst.getOverseaFlag(mutilIndexVO.getOverseaFlag()).getDesc());
        multiIdxSheet.getRow(4).getCell(5).setCellValue(Constant.StrEnum.BG_FLAG.getValue() + getBgList(mutilIndexVO).get(0).getLv0ProdListCnName());
        multiIdxSheet.getRow(5).getCell(5).setCellValue(Constant.StrEnum.INDUSTRY_VIEW.getValue() + dmFocViewInfoVO.getViewFlagValue());
        multiIdxSheet.getRow(6).getCell(5).setCellValue(dmFocMonthCostIdxDao.findActualMonth(searchParamsVO.getTablePreFix()));
        Cell cell = multiIdxSheet.getRow(7).getCell(5);
        if (cell == null) {
            cell = multiIdxSheet.getRow(7).createCell(5);
        }
        cell.setCellValue(FcstIndexUtil.getBasePeriodStr(searchParamsVO.getBasePeriodId()));
        log.info(">>>End AsyncExportService::fillMultiIndexTemplate1Sheet");
        return new AsyncResult<>(priceIndexExpData.size());
    }

    private void connectGroupCodeAndCnName(List<DmFocMonthCostIdxVO> priceIndexExpData) {
        if (CollectionUtils.isNotEmpty(priceIndexExpData)) {
            priceIndexExpData.stream().forEach(index -> {
                if (CommonConstant.GROUP_LEVEL_DIMENSION.contains(index.getGroupLevel())) {
                    index.setGroupCnName(index.getGroupCode() + " " + index.getGroupCnName());
                }
            });
        }
    }

    @NotNull
    private Boolean getReveseViewFlag(MonthAnalysisVO mutilIndexVO) {
        return IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(mutilIndexVO.getGranularityType()) &&
                (IndustryIndexEnum.VIEW_FLAG_U.VIEW5.getValue().equals(mutilIndexVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_U.VIEW6.getValue().equals(mutilIndexVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_U.VIEW7.getValue().equals(mutilIndexVO.getViewFlag()));
    }

    @NotNull
    private Map getGroupCodeLevelMap(MonthAnalysisVO mutilIndexVO) {
        Map map;
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(mutilIndexVO.getCostType())) {
            map = FcstIndexMadeUtil.getNextGroupLevel(mutilIndexVO);
        } else {
            map = FcstIndexUtil.getNextGroupLevel(mutilIndexVO);
        }
        return map;
    }

    private String getMultiIdxTitle(MonthAnalysisVO mutilIndexVO, String name) {
        String multiIdxTitle;
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(mutilIndexVO.getCostType())) {
            multiIdxTitle = FcstIndexUtil.getTitle(Constant.StrEnum.PURCHASE_INDEX_TITLE.getValue(), name);
        } else if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(mutilIndexVO.getCostType())) {
            multiIdxTitle = FcstIndexUtil.getTitle(Constant.StrEnum.MANUFACTURE_INDEX_TITLE.getValue(), name);
        } else {
            multiIdxTitle = FcstIndexUtil.getTitle(Constant.StrEnum.TOTAL_INDEX_TITLE.getValue(), name);
        }
        return multiIdxTitle;
    }

    private List<DmFocMonthCostIdxVO> getRevPriceIndexExpData(MonthAnalysisVO mutilIndexVO, List<DmFocMonthCostIdxVO> priceIndexExpData) {
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(mutilIndexVO.getCostType())) {
            priceIndexExpData = dmFocRecMonthCostIdxDao.findRevPriceIndexExpData(mutilIndexVO);
        }
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(mutilIndexVO.getCostType())) {
            monthCommonService.setMonthStartEndTime(mutilIndexVO);
            priceIndexExpData = dmFocMadeRecMonthCostIdxDao.findRevPriceIndexExpData(mutilIndexVO);
        }
        return priceIndexExpData;
    }

    private List<DmFocMonthCostIdxVO> getPriceIndexExpDataList(MonthAnalysisVO mutilIndexVO, List<DmFocMonthCostIdxVO> priceIndexExpData) {
        // 查询采购成本下反转视角的数据
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(mutilIndexVO.getCostType())) {
            priceIndexExpData = dmFocMonthCostIdxDao.findPriceIndexExpData(mutilIndexVO);
        }
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(mutilIndexVO.getCostType())) {
            monthCommonService.setMonthStartEndTime(mutilIndexVO);
            priceIndexExpData = dmFocMadeMonthCostIdxDao.findPriceIndexExpData(mutilIndexVO);
        }
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(mutilIndexVO.getCostType())) {
            priceIndexExpData.addAll(dmFocMonthCostIdxDao.findPriceIndexExpData(mutilIndexVO));
            Long actualMonthNum = dmFocMonthCostIdxDao.findActualMonthNum(mutilIndexVO.getTablePreFix());
            mutilIndexVO.setPeriodEndTime(Integer.valueOf(actualMonthNum.toString()));
            priceIndexExpData.addAll(dmFocTotalMonthCostIdxDao.findPriceIndexExpData(mutilIndexVO));

            monthCommonService.setMonthStartEndTime(mutilIndexVO);
            priceIndexExpData.addAll(dmFocMadeMonthCostIdxDao.findPriceIndexExpData(mutilIndexVO));
        }
        connectGroupCodeAndCnName(priceIndexExpData);
        return priceIndexExpData;
    }

    @NotNull
    private List<MultiIndexExportVO> getMultiIndexExportVOList(List<DmFocMonthCostIdxVO> priceIndexExpData) {
        List<MultiIndexExportVO> multiIndexExportVOList = new ArrayList<>();
        priceIndexExpData.stream().forEach(indexEle ->{
            MultiIndexExportVO multiIndexExportVO = MultiIndexExportVO.builder()
                    .periodId(indexEle.getPeriodId())
                    .costType(IndustryIndexEnum.getCostType(indexEle.getCostType()).getDesc())
                    .parentCnName(indexEle.getParentCnName())
                    .groupCnName(indexEle.getGroupCnName())
                    .costIndex(null == indexEle.getCostIndex() ? null : new BigDecimal(indexEle.getCostIndex()).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue())
                    .build();
            multiIndexExportVOList.add(multiIndexExportVO);
        });
        return multiIndexExportVOList;
    }

    /**
     * 填充产业成本指数图（多指数）sheet页数据
     *
     * @param searchParamsVO 参数参数VO
     * @param workbook 工作簿
     * @param sheetIdx sheet页下标
     * @param current 当前请求上下文
     * @return Future<Integer> total rows
     * @throws Exception
     */
    @Async("asyncServiceExecutor")
    public Future<Integer> fillMultiIndexTemplate2Sheet(MonthAnalysisVO searchParamsVO, Workbook workbook,
        int sheetIdx, String groupCnName,IRequestContext current) throws Exception {
        log.info(">>>Begin AsyncExportService::fillMultiIndexTemplate2Sheet");
        // 3、产业成本指数图—多专家团指数导出数据
        RequestContextManager.setCurrent(current);
        MonthAnalysisVO paramsVO = ObjectCopyUtil.copy(searchParamsVO, MonthAnalysisVO.class);
        paramsVO.setParentCodeList(paramsVO.getGroupCodeList());
        // 分视角获取下个层级的groupLevel值和对应中文名称
        Map map = getGroupCodeLevelMap(paramsVO);
        paramsVO.setGroupLevel(map.get("nextGroupLevel").toString());
        // 除过视角1,ICT和LV1层级不需要传入重量级团队code值
        commonService.setProdRndTeamCode(paramsVO);
        // 名字需要重新拼接
        String name;
        if (paramsVO.getIsContainComb() && CollectionUtils.isNotEmpty(paramsVO.getCustomIdList())) {
            name = groupCnName + "-多ITEM";
        } else {
            name = groupCnName + "-多" + map.get("nextGroupName").toString();
        }
        String multiIdxTitle = getMultiIdxTitle(paramsVO, name);
        Sheet multiIdxSheet = workbook.getSheetAt(sheetIdx);
        String titleName = FcstIndexUtil.getTitle(Constant.StrEnum.HEAP_TITLE2.getValue(), map.get("nextGroupName").toString());
        String titleCode = FcstIndexUtil.getTitle(Constant.StrEnum.HEAP_TITLE3.getValue(), map.get("nextGroupName").toString());
        if (GroupLevelEnumP.L2.getValue().equals(paramsVO.getGroupLevel())) {
            paramsVO.setProfitsName(null);
        }
        List<DmFocMonthCostIdxVO> priceIndexExpData = new ArrayList<>();
        if (paramsVO.getIsContainComb() && CollectionUtils.isNotEmpty(paramsVO.getCustomIdList())) {
            addCombMutilPriceIndexChartList(paramsVO, priceIndexExpData);
            titleName ="ITEM名称";
            titleCode ="ITEM编码";
        } else {
            getPriceIndexExpData(paramsVO, priceIndexExpData);
            // 第4步：循环groupCodeList，然后再根据groupCode查询对应的数据
            if (paramsVO.getIsMultipleSelect()) {
                // 名称拼接
                monthCommonService.mutilSelectGroupCnName(paramsVO, priceIndexExpData, null, null, null, "mutilIndexCost");
            }
        }
        multiIdxSheet.getRow(0).getCell(0).setCellValue(multiIdxTitle);
        multiIdxSheet.getRow(0).getCell(6).setCellValue(Constant.StrEnum.CALIBER_FLAG.getValue() + IndustryIndexEnum.getCaliberFlag(paramsVO.getCaliberFlag()).getDesc());
        multiIdxSheet.getRow(1).getCell(6).setCellValue(Constant.StrEnum.COST_TYPE.getValue() + IndustryIndexEnum.getCostType(paramsVO.getCostType()).getDesc());
        DmFocViewInfoVO dmFocViewInfoVO = getDmFocViewInfoVO(searchParamsVO);
        // 设置第2行第4列标题名称
        multiIdxSheet.getRow(1).getCell(3).setCellValue(titleName);
        multiIdxSheet.getRow(1).getCell(4).setCellValue(titleCode);
        // ceg专项采购认证部层级 和category品类层级的下个层级的导出数据需要编码列
        new ExcelExportUtil<DmFocMonthCostIdxVO>(2, 2, DmFocMonthCostIdxVO.class).fillSheetData(multiIdxSheet,
                priceIndexExpData);
        multiIdxSheet.getRow(2).getCell(6).setCellValue(Constant.StrEnum.GRANULE.getValue() + IndustryIndexEnum.getGranularityType(paramsVO.getGranularityType()).getDesc());
        multiIdxSheet.getRow(3).getCell(6).setCellValue(Constant.StrEnum.OVERSEA_FLAG.getValue() + IndustryConst.getOverseaFlag(paramsVO.getOverseaFlag()).getDesc());
        multiIdxSheet.getRow(4).getCell(6).setCellValue(Constant.StrEnum.BG_FLAG.getValue() + getBgList(paramsVO).get(0).getLv0ProdListCnName());
        multiIdxSheet.getRow(5).getCell(6).setCellValue(Constant.StrEnum.INDUSTRY_VIEW.getValue() + dmFocViewInfoVO.getViewFlagValue());
        multiIdxSheet.getRow(6).getCell(6).setCellValue(dmFocMonthCostIdxDao.findActualMonth(paramsVO.getTablePreFix()));
        Cell cell = multiIdxSheet.getRow(7).getCell(6);
        if (cell == null) {
            cell = multiIdxSheet.getRow(7).createCell(6);
        }
        cell.setCellValue(FcstIndexUtil.getBasePeriodStr(searchParamsVO.getBasePeriodId()));
        log.info(">>>End AsyncExportService::fillMultiIndexTemplate2Sheet");
        return new AsyncResult<>(priceIndexExpData.size());
    }

    private void getPriceIndexExpData(MonthAnalysisVO paramsVO, List<DmFocMonthCostIdxVO> priceIndexExpData) {
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(paramsVO.getCostType())) {
            priceIndexExpData.addAll(dmFocMonthCostIdxDao.findPriceIndexExpData(paramsVO));
        }
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(paramsVO.getCostType())) {
            monthCommonService.setMonthStartEndTime(paramsVO);
            priceIndexExpData.addAll(dmFocMadeMonthCostIdxDao.findPriceIndexExpData(paramsVO));
        }
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(paramsVO.getCostType())) {
            priceIndexExpData.addAll(dmFocMonthCostIdxDao.findPriceIndexExpData(paramsVO));
            Long actualMonthNum = dmFocMonthCostIdxDao.findActualMonthNum(paramsVO.getTablePreFix());
            paramsVO.setPeriodEndTime(Integer.valueOf(actualMonthNum.toString()));
            priceIndexExpData.addAll(dmFocTotalMonthCostIdxDao.findPriceIndexExpData(paramsVO));

            monthCommonService.setMonthStartEndTime(paramsVO);
            priceIndexExpData.addAll(dmFocMadeMonthCostIdxDao.findPriceIndexExpData(paramsVO));
        }
        connectGroupCodeAndCnName(priceIndexExpData);
        priceIndexExpData.stream().forEach(price->{
            price.setCostType(IndustryIndexEnum.getCostType(price.getCostType()).getDesc());
            price.setCostIndex(null == price.getCostIndex() ? null : new BigDecimal(price.getCostIndex()).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
        });
    }

    private void addCombMutilPriceIndexChartList(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexChartList) {
        distinguishIfCombine(monthAnalysisVO);
        // 非组合的item数据
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getParentCodeList())) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
                priceIndexChartList.addAll(dmFocMonthCostIdxDao.findPriceIndexNormalChartByMultiDim(monthAnalysisVO));
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())) {
                monthCommonService.setMonthStartEndTime(monthAnalysisVO);
                priceIndexChartList.addAll(dmFocMadeMonthCostIdxDao.findMadePriceIndexNormalByMultiDim(monthAnalysisVO));
            }
            // 非组合的采购层级名称拼接
            monthCommonService.mutilSelectGroupCnName(monthAnalysisVO, priceIndexChartList,
                    null, null, null, "mutilIndexCost");
        }
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getCombinaCodeList())) {
            List<DmFocMonthCostIdxVO> priceIndexCombChartByMultiDim = new ArrayList<>();
            // 汇总组合的查询
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
                priceIndexCombChartByMultiDim = dmFocMonthCostIdxDao.findPriceIndexCombChartByMultiDim(monthAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())) {
                monthCommonService.setMonthStartEndTime(monthAnalysisVO);
                priceIndexCombChartByMultiDim = dmFocMadeMonthCostIdxDao.findMadePriceIndexCombByMultiDim(monthAnalysisVO);
            }
            for (DmFocMonthCostIdxVO combMonthCostIdxVO : priceIndexCombChartByMultiDim) {
                if (null == combMonthCostIdxVO.getParentCnName()) {
                    combMonthCostIdxVO.setParentCnName(combMonthCostIdxVO.getCustomCnName());
                    continue;
                }
                if (!combMonthCostIdxVO.getParentCnName().equals(combMonthCostIdxVO.getCustomCnName())) {
                    combMonthCostIdxVO.setParentCnName(combMonthCostIdxVO.getParentCnName() + "(" + combMonthCostIdxVO.getCustomCnName() + ")");
                }
            }
            priceIndexChartList.addAll(priceIndexCombChartByMultiDim);
        }
        priceIndexChartList.stream().forEach(price->{
            price.setCostType(IndustryIndexEnum.getCostType(price.getCostType()).getDesc());
            price.setCostIndex(null == price.getCostIndex() ? null : new BigDecimal(price.getCostIndex()).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
        });
    }

    private void distinguishIfCombine(MonthAnalysisVO monthAnalysisVO) {
        List<String> combinaCodeList = new ArrayList<>();
        List<String> parentCodeList = new ArrayList<>();
        List<String> allGroupCodeList = monthAnalysisVO.getParentCodeList();
        monthAnalysisVO.setParentCodeList(null);
        for (String parentCode : allGroupCodeList) {
            if (parentCode.contains("_##")) {
                if ("null".equals(parentCode.split("_##")[1])) {
                    combinaCodeList.add(parentCode.split("_##")[0] + "_##" + parentCode.split("_##")[0]);
                }  else {
                    combinaCodeList.add(parentCode);
                }
                monthAnalysisVO.setCombinaCodeList(combinaCodeList);
            } else {
                // 正常维度的查询
                parentCodeList.add(parentCode);
                monthAnalysisVO.setParentCodeList(parentCodeList);
            }
        }
    }

    /**
     * 填充权重图sheet页数据
     *
     * @param searchParamsVO 参数参数VO
     * @param workbook 工作簿
     * @param sheetIdx sheet页下标
     * @param groupCnName groupCnName
     * @param current 当前请求上下文
     * @return Future<Integer> total rows
     * @throws Exception
     */
    @Async("asyncServiceExecutor")
    public Future<Integer> fillWeightTemplate1Sheet(MonthAnalysisVO searchParamsVO, Workbook workbook, int sheetIdx,
        String groupCnName, IRequestContext current) throws Exception {
        log.info(">>>Begin AsyncExportService::fillWeightTemplate1Sheet");
        // 5、权重图导出数据
        RequestContextManager.setCurrent(current);
        MonthAnalysisVO paramsVO = ObjectCopyUtil.copy(searchParamsVO, MonthAnalysisVO.class);
        paramsVO.setParentCodeList(paramsVO.getGroupCodeList());
        // 除过视角1,ICT和LV1层级不需要传入重量级团队code值
        commonService.setProdRndTeamCode(paramsVO);
        // 分视角获取下个层级的groupLevel值和对应中文名称
        Map map = getGroupCodeLevelMap(paramsVO);
        paramsVO.setGroupLevel(map.get("nextGroupLevel").toString());
        if (GroupLevelEnumP.L2.getValue().equals(paramsVO.getGroupLevel())) {
            paramsVO.setProfitsName(null);
        }
        List<DmFocMonthWeightVO> weightList = getWeightList(paramsVO);
        if (paramsVO.getIsMultipleSelect()) {
            monthCommonService.mutilSelectGroupCnName(paramsVO,null,null, weightList,null,"weightList");
        }
        Sheet weightSheet = workbook.getSheetAt(sheetIdx);
        String title;
        if (paramsVO.getIsContainComb() && CollectionUtils.isNotEmpty(paramsVO.getCustomIdList())) {
            title = groupCnName + "-多ITEM";
        } else {
            title = groupCnName + "-多" + map.get("nextGroupName").toString();
        }
        String weightTitle = FcstIndexUtil.getTitle(Constant.StrEnum.WEIGHT_TITLE.getValue(), title);
        weightSheet.getRow(0).getCell(0).setCellValue(weightTitle);
        weightSheet.getRow(0).getCell(4).setCellValue(Constant.StrEnum.CALIBER_FLAG.getValue() + IndustryIndexEnum.getCaliberFlag(paramsVO.getCaliberFlag()).getDesc());
        DmFocViewInfoVO dmFocViewInfoVO = getDmFocViewInfoVO(searchParamsVO);
        // 设置第2行第3列标题名称
        String name = map.get("nextGroupName").toString();
        String titleName = FcstIndexUtil.getTitle(Constant.StrEnum.HEAP_TITLE2.getValue(), name);
        weightSheet.getRow(1).getCell(2).setCellValue(titleName);
        weightSheet.getRow(1).getCell(4).setCellValue(Constant.StrEnum.COST_TYPE.getValue() + IndustryIndexEnum.getCostType(paramsVO.getCostType()).getDesc());
        List<ExportMonthWeightVO> exportWeightList = new ArrayList<>();
        Optional.ofNullable(weightList).orElse(new ArrayList<>()).stream().forEach(weightEle -> {
            ExportMonthWeightVO build = ExportMonthWeightVO.builder()
                    .costType(IndustryIndexEnum.getCostType(weightEle.getCostType()).getDesc()).parentCnName(weightEle.getParentCnName())
                    .groupCnName(weightEle.getGroupCnName())
                    .weightPercent(weightEle.getWeightPercent()).build();
            exportWeightList.add(build);
        });
        new ExcelExportUtil<ExportMonthWeightVO>(2, 2, ExportMonthWeightVO.class).fillSheetData(weightSheet, exportWeightList);
        weightSheet.getRow(2).getCell(4).setCellValue(Constant.StrEnum.GRANULE.getValue() + IndustryIndexEnum.getGranularityType(paramsVO.getGranularityType()).getDesc());
        weightSheet.getRow(3).getCell(4).setCellValue(Constant.StrEnum.OVERSEA_FLAG.getValue() + IndustryConst.getOverseaFlag(paramsVO.getOverseaFlag()).getDesc());
        weightSheet.getRow(4).getCell(4).setCellValue(Constant.StrEnum.BG_FLAG.getValue() + getBgList(paramsVO).get(0).getLv0ProdListCnName());
        weightSheet.getRow(5).getCell(4).setCellValue(Constant.StrEnum.INDUSTRY_VIEW.getValue() + dmFocViewInfoVO.getViewFlagValue());
        weightSheet.getRow(6).getCell(4).setCellValue(dmFocMonthCostIdxDao.findActualMonth(paramsVO.getTablePreFix()));
        Cell cell = weightSheet.getRow(7).getCell(4);
        if (cell == null) {
            cell = weightSheet.getRow(7).createCell(4);
        }
        cell.setCellValue(FcstIndexUtil.getBasePeriodStr(searchParamsVO.getBasePeriodId()));
        log.info(">>>End AsyncExportService::fillWeightTemplate1Sheet");
        return new AsyncResult<>(weightList.size());
    }

    private List<DmFocMonthWeightVO> getWeightList(MonthAnalysisVO paramsVO) {
        List<DmFocMonthWeightVO> weightList = new ArrayList<>();
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(paramsVO.getCostType())) {
            weightList.addAll(dmFocMonthWeightDao.findWeightList(paramsVO));
        }
        // 查询制造成本下的权重图数据
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(paramsVO.getCostType())) {
            weightList.addAll(dmFocMadeMonthWeightDao.findMadeWeightList(paramsVO));
        }
        // 查询总成本下的权重图数据
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(paramsVO.getCostType())) {
            weightList.addAll(dmFocMonthWeightDao.findWeightList(paramsVO));
            weightList.addAll(dmFocMadeMonthWeightDao.findMadeWeightList(paramsVO));
            weightList.addAll(dmFocTotalMonthWeightDao.findTotalWeightList(paramsVO));
        }
        monthCommonService.splicingWeightCodeAndName(weightList);
        return weightList;
    }

    /**
     * 填充权重图sheet页数据
     *
     * @param searchParamsVO 参数参数VO
     * @param workbook 工作簿
     * @param sheetIdx sheet页下标
     * @param groupCnName groupCnName
     * @param current 当前请求上下文
     * @return Future<Integer> total rows
     * @throws Exception
     */
    @Async("asyncServiceExecutor")
    public Future<Integer> fillWeightTemplate2Sheet(MonthAnalysisVO searchParamsVO, Workbook workbook, int sheetIdx,
        String groupCnName, IRequestContext current) throws Exception {
        log.info(">>>Begin AsyncExportService::fillWeightTemplate2Sheet");
        // 5、权重图导出数据
        RequestContextManager.setCurrent(current);
        MonthAnalysisVO paramsVO = ObjectCopyUtil.copy(searchParamsVO, MonthAnalysisVO.class);
        paramsVO.setParentLevel(paramsVO.getParentLevel());
        paramsVO.setParentCodeList(paramsVO.getGroupCodeList());
        // 除过视角1,ICT和LV1层级不需要传入重量级团队code值
        commonService.setProdRndTeamCode(paramsVO);
        // 分视角获取下个层级的groupLevel值和对应中文名称
        Map map = getGroupCodeLevelMap(paramsVO);
        paramsVO.setGroupLevel(map.get("nextGroupLevel").toString());
        if (GroupLevelEnumP.L2.getValue().equals(paramsVO.getGroupLevel())) {
            paramsVO.setProfitsName(null);
        }
        List<DmFocMonthWeightVO> weightList = new ArrayList<>();
        weightList = getDmFocMonthWeightList(paramsVO, weightList);
        Sheet weightSheet = workbook.getSheetAt(sheetIdx);
        String title;
        if (paramsVO.getIsContainComb() && CollectionUtils.isNotEmpty(paramsVO.getCustomIdList())) {
            title = groupCnName + "-多ITEM";
        } else {
            title = groupCnName + "-多" + map.get("nextGroupName").toString();
        }
        String weightTitle = FcstIndexUtil.getTitle(Constant.StrEnum.WEIGHT_TITLE.getValue(), title);
        weightSheet.getRow(0).getCell(0).setCellValue(weightTitle);
        weightSheet.getRow(0).getCell(5).setCellValue(Constant.StrEnum.CALIBER_FLAG.getValue() + IndustryIndexEnum.getCaliberFlag(paramsVO.getCaliberFlag()).getDesc());
        DmFocViewInfoVO dmFocViewInfoVO = getDmFocViewInfoVO(searchParamsVO);
        // 设置第2行第3列标题名称
        String name = map.get("nextGroupName").toString();
        String titleName = FcstIndexUtil.getTitle(Constant.StrEnum.HEAP_TITLE2.getValue(), name);
        String titleCode = FcstIndexUtil.getTitle(Constant.StrEnum.HEAP_TITLE3.getValue(), name);
        if (paramsVO.getIsContainComb()) {
            titleName = "ITEM名称";
            titleCode = "ITEM编码";
        }
        weightSheet.getRow(1).getCell(2).setCellValue(titleName);
        weightSheet.getRow(1).getCell(3).setCellValue(titleCode);
        weightSheet.getRow(1).getCell(5).setCellValue(Constant.StrEnum.COST_TYPE.getValue() +IndustryIndexEnum.getCostType(paramsVO.getCostType()).getDesc());
        new ExcelExportUtil<DmFocMonthWeightVO>(2, 2,
                DmFocMonthWeightVO.class).fillSheetData(weightSheet, weightList);
        weightSheet.getRow(2).getCell(5).setCellValue(Constant.StrEnum.GRANULE.getValue() + IndustryIndexEnum.getGranularityType(paramsVO.getGranularityType()).getDesc());
        weightSheet.getRow(3).getCell(5).setCellValue(Constant.StrEnum.OVERSEA_FLAG.getValue() + IndustryConst.getOverseaFlag(paramsVO.getOverseaFlag()).getDesc());
        weightSheet.getRow(4).getCell(5).setCellValue(Constant.StrEnum.BG_FLAG.getValue() + getBgList(paramsVO).get(0).getLv0ProdListCnName());
        weightSheet.getRow(5).getCell(5).setCellValue(Constant.StrEnum.INDUSTRY_VIEW.getValue() + dmFocViewInfoVO.getViewFlagValue());
        weightSheet.getRow(6).getCell(5).setCellValue(dmFocMonthCostIdxDao.findActualMonth(paramsVO.getTablePreFix()));
        Cell cell = weightSheet.getRow(7).getCell(5);
        if (cell == null) {
            cell = weightSheet.getRow(7).createCell(5);
        }
        cell.setCellValue(FcstIndexUtil.getBasePeriodStr(searchParamsVO.getBasePeriodId()));
        log.info(">>>End AsyncExportService::fillWeightTemplate2Sheet");
        return new AsyncResult<>(weightList.size());
    }

    private List<DmFocMonthWeightVO> getDmFocMonthWeightList(MonthAnalysisVO paramsVO, List<DmFocMonthWeightVO> weightList) {
        if (paramsVO.getIsContainComb()) {
            // 区分汇总与非汇总组合
            distinguishIfCombine(paramsVO);
            // 查询汇总组合item数据
            if (CollectionUtils.isNotEmpty(paramsVO.getCombinaCodeList())) {
                purchaseAndManfactureCombList(paramsVO, weightList);
                for (DmFocMonthWeightVO weightVO : weightList) {
                    if (null == weightVO.getParentCnName()) {
                        weightVO.setParentCnName(weightVO.getCustomCnName());
                        continue;
                    }
                    if (!weightVO.getParentCnName().equals(weightVO.getCustomCnName())) {
                        weightVO.setParentCnName(weightVO.getParentCnName() + "(" + weightVO.getCustomCnName() + ")");
                    }
                }
            }
            // 查询非汇总组合item数据
            containCombWithItem(paramsVO, weightList);
            paramsVO.setGroupLevel(GroupLevelEnumU.ITEM.getValue());
            List<DmFocMonthWeightVO> newWeightList = weightList.stream().sorted(Comparator.comparing(DmFocMonthWeightVO::getWeightRate).reversed()).collect(Collectors.toList());
            weightList = newWeightList;
        } else {
            weightList = getMonthWeightNotContainComb(paramsVO, weightList);
        }
        List<Integer> numList = Stream.iterate(1, item -> item + 1).limit(weightList.size()).collect(Collectors.toList());
        // item层级需要转换成权重排序(按序列排序)
        if (GroupLevelEnumU.ITEM.getValue().equals(paramsVO.getGroupLevel())){
            for (int i = 0; i < weightList.size(); i++) {
                weightList.get(i).setWeightPercent(numList.get(i).toString());
            }
        }
        weightList.stream().forEach(weight->weight.setCostType(IndustryIndexEnum.getCostType(weight.getCostType()).getDesc()));
        return weightList;
    }

    private void purchaseAndManfactureCombList(MonthAnalysisVO paramsVO, List<DmFocMonthWeightVO> weightList) {
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(paramsVO.getCostType())) {
            weightList.addAll(dmFocMonthWeightDao.findCombWeightList(paramsVO));
        }
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(paramsVO.getCostType())) {
            weightList.addAll(dmFocMadeMonthWeightDao.findMadeCombWeightList(paramsVO));
        }
        // 父名称拼接 parentCode+parentCnName
        monthCommonService.splicingWeightParentCodeAndName(weightList);
    }

    private void containCombWithItem(MonthAnalysisVO paramsVO, List<DmFocMonthWeightVO> weightList) {
        if (CollectionUtils.isNotEmpty(paramsVO.getParentCodeList())) {
            paramsVO.setCombGroupLevel(paramsVO.getParentLevel() + "_ITEM");
            List<DmFocMonthWeightVO> normalWeightList = new ArrayList<>();
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(paramsVO.getCostType())) {
                normalWeightList = dmFocMonthWeightDao.findNormalWeightList(paramsVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(paramsVO.getCostType())) {
                normalWeightList = dmFocMadeMonthWeightDao.findMadeNormalWeightList(paramsVO);
            }
            monthCommonService.mutilSelectGroupCnName(paramsVO,null,null, normalWeightList,null,"weightList");
            weightList.addAll(normalWeightList);
        }
    }

    private List<DmFocMonthWeightVO> getMonthWeightNotContainComb(MonthAnalysisVO paramsVO, List<DmFocMonthWeightVO> weightList) {
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(paramsVO.getCostType())) {
            weightList = dmFocMonthWeightDao.findWeightList(paramsVO);
        }
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(paramsVO.getCostType())) {
            weightList = dmFocMadeMonthWeightDao.findMadeWeightList(paramsVO);
        }
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(paramsVO.getCostType())) {
            weightList.addAll(dmFocMonthWeightDao.findWeightList(paramsVO));
            weightList.addAll(dmFocMadeMonthWeightDao.findMadeWeightList(paramsVO));
            weightList.addAll(dmFocTotalMonthWeightDao.findTotalWeightList(paramsVO));
        }
        monthCommonService.splicingWeightCodeAndName(weightList);
        if (paramsVO.getIsMultipleSelect()) {
            monthCommonService.mutilSelectGroupCnName(paramsVO,null,null, weightList,null,"weightList");
        }
        List<Integer> numList = Stream.iterate(1, item -> item + 1).limit(weightList.size()).collect(Collectors.toList());
        // item层级需要转换成权重排序(按序列排序)
        if (GroupLevelEnumU.ITEM.getValue().equals(paramsVO.getGroupLevel())){
            for (int i = 0; i < weightList.size(); i++) {
                weightList.get(i).setWeightPercent(numList.get(i).toString());
            }
        }
        return weightList;
    }

    @Async("asyncServiceExecutor")
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void refreshIndustryIndexData(MonthAnalysisVO monthAnalysisVO, VarifyTaskVO varifyTaskVO) {
        log.info(">>>searchParamsVO:{}", JSON.toJSONString(monthAnalysisVO));
        monthCommonService.setParams(monthAnalysisVO);
        String successFlag;
        // 总成本切换基期时，需要同时将采购成本和制造成本的数据也刷出来
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(monthAnalysisVO.getCostType())) {
            // 总成本
            successFlag = dmFocMonthCostIdxDao.insertTotalPriceIdxWithFun(monthAnalysisVO);
            // 采购成本
            MonthAnalysisVO paramsVO = ObjectCopyUtil.copy(monthAnalysisVO, MonthAnalysisVO.class);
            paramsVO.setCostType(IndustryIndexEnum.COST_TYPE.P.getValue());
            dmFocMonthCostIdxDao.insertPriceIndexWithFun(paramsVO);
            // 制造成本成本
            MonthAnalysisVO params = ObjectCopyUtil.copy(monthAnalysisVO, MonthAnalysisVO.class);
            params.setCostType(IndustryIndexEnum.COST_TYPE.M.getValue());
            params.setGroupLevel(monthAnalysisVO.getParentLevel());
            Map map = FcstIndexMadeUtil.getNextGroupLevel(params);
            params.setGroupLevel(map.get("nextGroupLevel").toString());
            dmFocMonthCostIdxDao.insertPriceIndexWithFun(params);
        } else {
            successFlag = dmFocMonthCostIdxDao.insertPriceIndexWithFun(monthAnalysisVO);
        }
        log.info(">>>insertPriceIndexWithFun:{}", successFlag);
        if ("PROCESSING".equals(varifyTaskVO.getStatus())) {
            if ("SUCCESS".equals(successFlag)) {
                varifyTaskVO.setStatus("SUCCESS");
            } else {
                varifyTaskVO.setStatus("FAILED");
            }
        }
        if ("PROCESSING".equals(varifyTaskVO.getCombStatus())) {
            if ("SUCCESS".equals(successFlag)) {
                varifyTaskVO.setCombStatus("SUCCESS");
            } else {
                varifyTaskVO.setCombStatus("FAILED");
            }
        }
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        varifyTaskVO.setLastUpdateDate(timestamp);
        iDataCipherTextDao.updateVerifyTask(varifyTaskVO);
    }

}