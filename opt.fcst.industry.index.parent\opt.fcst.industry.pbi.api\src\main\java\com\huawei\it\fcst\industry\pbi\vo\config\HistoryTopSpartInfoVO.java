/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.config;

import com.huawei.it.fcst.industry.pbi.vo.common.CommonBaseVO;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/6/26
 */
@Setter
@Getter
@EqualsAndHashCode
@NoArgsConstructor
public class HistoryTopSpartInfoVO  extends CommonBaseVO implements Serializable {

    /**
     * 文件名称
     */
    private String fileName;


    /**
     *
     *  topSpartCode
     */
    private String topSpartCode;



}
