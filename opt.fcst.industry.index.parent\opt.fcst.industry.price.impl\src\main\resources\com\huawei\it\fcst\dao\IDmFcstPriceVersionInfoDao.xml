<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.price.dao.IDmFcstPriceVersionInfoDao">
    <resultMap type="com.huawei.it.fcst.industry.price.vo.version.DmFcstVersionInfoVO" id="resultMap">
        <result property="delFlag" column="del_flag"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="versionType" column="version_type"/>
        <result property="parentVersionId" column="parent_version_id"/>
        <result property="dataType" column="data_type"/>
        <result property="version" column="version"/>
        <result property="versionId" column="version_id"/>
        <result property="creationDate" column="creation_date"/>
        <result property="version" column="version"/>
        <result property="createdBy" column="created_by"/>
        <result property="status" column="status"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="step" column="step"/>
        <result property="isRunning" column="is_running"/>
    </resultMap>

    <sql id="allFields">
        del_flag,
        last_updated_by,
        version_type,
        parent_version_id,
        data_type,
        version_id,
        creation_date,
        version,
        created_by,
        status,
        last_update_date,
        step,
        is_running
    </sql>

    <sql id="allValues">
        'N',
        #{lastUpdatedBy,jdbcType=VARCHAR},
        #{versionType,jdbcType=VARCHAR},
        #{parentVersionId,jdbcType=NUMERIC},
        #{dataType,jdbcType=VARCHAR},
        #{versionId,jdbcType=NUMERIC},
        NOW(),
        #{version,jdbcType=VARCHAR},
        #{createdBy,jdbcType=VARCHAR},
        #{status,jdbcType=NUMERIC},
        NOW(),
        #{step,jdbcType=INTEGER},
        #{isRunning,jdbcType=VARCHAR}
    </sql>

    <sql id="uniqueKeyField">
        version_id=#{versionId,jdbcType=NUMERIC}
    </sql>

    <sql id="setValues">
        <if test='delFlag != null'>
            del_flag = #{delFlag,jdbcType=VARCHAR},
        </if>
        <if test='lastUpdatedBy != null'>
            last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
        </if>
        <if test='versionType != null'>
            version_type = #{versionType,jdbcType=VARCHAR},
        </if>
        <if test='parentVersionId != null'>
            parent_version_id = #{parentVersionId,jdbcType=NUMERIC},
        </if>
        <if test='dataType != null and dataType != ""'>
            data_type = #{dataType,jdbcType=VARCHAR},
        </if>
        <if test='versionId != null'>
            version_id = #{versionId,jdbcType=NUMERIC},
        </if>
        <if test='creationDate != null'>
            creation_date = #{creationDate,jdbcType=TIMESTAMP},
        </if>
        <if test='version != null and version != ""'>
            version = #{version,jdbcType=VARCHAR},
        </if>
        <if test='createdBy != null'>
            created_by = #{createdBy,jdbcType=VARCHAR},
        </if>
        <if test='status != null'>
            status = #{status,jdbcType=NUMERIC},
        </if>
        <if test='lastUpdateDate != null'>
            last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
        </if>
        <if test='step != null'>
            step = #{step,jdbcType=INTEGER},
        </if>
        <if test='isRunning != null'>
            is_running = #{isRunning,jdbcType=VARCHAR},
        </if>
    </sql>

    <select id="findVersionIdByDataType" resultMap="resultMap">
        SELECT version_id,
               last_updated_by,
               version_type,
               parent_version_id,
               data_type,
               creation_date,
               version,
               created_by,
               status,
               last_update_date
        FROM fin_dm_opt_foi.dm_fcst_price_version_info_t
        WHERE del_flag = 'N'
          AND is_running = 'N'
          AND status = 1
          AND data_type = #{dataType}
          AND version_type IN ('AUTO', 'FINAL')
        ORDER BY creation_date DESC
            LIMIT 1
    </select>

    <select id="getVersionKey" resultType="java.lang.Long">
        SELECT fin_dm_opt_foi.dm_fcst_price_version_info_s.nextval
    </select>

    <select id="findVersionList" resultMap="resultMap">
        SELECT <include refid="allFields"/>
        FROM fin_dm_opt_foi.dm_fcst_price_version_info_t
        WHERE del_flag = 'N'
        <if test='versionType != null and versionType != ""'>
            AND version_type = #{versionType,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            AND version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='parentVersionId != null'>
            AND parent_version_id =#{parentVersionId,jdbcType=NUMERIC}
        </if>
        <if test='dataType != null and dataType != ""'>
            AND data_type= #{dataType,jdbcType=VARCHAR}
        </if>
        <if test='version != null and version != ""'>
            AND version LIKE CONCAT(CONCAT('%', #{version,jdbcType=VARCHAR}) ,'%')
        </if>
        <if test='status != null'>
            AND status = #{status,jdbcType=NUMERIC}
        </if>
        <if test='step != null'>
            AND step = #{step,jdbcType=INTEGER}
        </if>
        ORDER BY last_update_date DESC
    </select>

    <delete id="deleteDmFcstVersionInfoById" parameterType="java.lang.Long">
        DELETE FROM fin_dm_opt_foi.dm_fcst_price_version_info_t
        WHERE version_id = #{versionId,jdbcType=NUMERIC}
    </delete>

    <insert id="createDmFcstVersionInfoDTO" parameterType="com.huawei.it.fcst.industry.price.vo.version.DmFcstVersionInfoVO">
        INSERT INTO fin_dm_opt_foi.dm_fcst_price_version_info_t
        (<include refid="allFields"/>)
        VALUES
        (<include refid="allValues"/>)
    </insert>

    <select id="findVersionListByVerName" resultMap="resultMap">
        SELECT <include refid="allFields"/>
        FROM fin_dm_opt_foi.dm_fcst_price_version_info_t
        WHERE del_flag = 'N'
        <if test='versionType != null and versionType != ""'>
            AND version_type = #{versionType,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            AND version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='parentVersionId != null'>
            AND parent_version_id =#{parentVersionId,jdbcType=NUMERIC}
        </if>
        <if test='dataType != null and dataType != ""'>
            AND data_type= #{dataType,jdbcType=VARCHAR}
        </if>
        <if test='status != null'>
            AND status = #{status,jdbcType=NUMERIC}
        </if>
        <if test='version != null and version != ""'>
            AND SUBSTR(version, 0, 8) = #{version,jdbcType=VARCHAR}
        </if>
        ORDER BY version DESC
    </select>

    <select id="findRefreshTime" resultMap="resultMap">
        SELECT last_update_date
        FROM fin_dm_opt_foi.dm_fcst_price_annl_amp_t
        WHERE del_flag = 'N'
          AND version_id = (
            SELECT v.version_id
            FROM fin_dm_opt_foi.dm_fcst_price_version_info_t v
            WHERE v.del_flag = 'N'
            AND v.status = 1
            AND v.data_type = 'ANNUAL'
            AND v.is_running = 'N'
            ORDER BY v.last_update_date DESC
            LIMIT 1 OFFSET 0
            )
        ORDER BY last_update_date DESC
            LIMIT 1 OFFSET 0
    </select>

    <select id="findMaxDataReviewVersion" resultMap="resultMap">
        SELECT
        <include refid="allFields"/>
        FROM fin_dm_opt_foi.dm_fcst_price_version_info_t
        where del_flag = 'N'
        AND is_running = 'N'
        AND status = 1
        AND data_type = 'DATA_REVIEW'
        order by last_update_date desc
        LIMIT 1 OFFSET 0
    </select>

    <select id="findDmFocVersionById" parameterType="java.lang.Long" resultMap="resultMap">
        SELECT
        <include refid="allFields"/>
        FROM fin_dm_opt_foi.dm_fcst_price_version_info_t
        WHERE del_flag = 'N'
        AND version_id=#{versionId,jdbcType=NUMERIC}
    </select>

    <select id="findAnnualActualMonth" resultType="java.lang.String">
        SELECT IFNULL(max(period_id),0)
        FROM fin_dm_opt_foi.dm_fcst_price_mon_cost_idx_t
        WHERE del_flag = 'N'
          AND version_id =(
            SELECT version_id
            FROM fin_dm_opt_foi.dm_fcst_price_version_info_t
            WHERE del_flag = 'N'
              AND is_running = 'N'
              AND status = 1
              AND data_type = 'MONTH'
              AND version_type IN ('AUTO', 'FINAL')
            ORDER BY creation_date DESC
            LIMIT 1
            )
    </select>

    <update id="updateRunningStatusFlag">
        update fin_dm_opt_foi.dm_fcst_price_version_info_t set is_running = 'N' where is_running ='Y'
    </update>

    <update id="updateStatusFlag">
        update fin_dm_opt_foi.dm_fcst_price_version_info_t set is_running = 'Y',last_update_date = now() where version_id =#{versionId,jdbcType=NUMERIC}
    </update>

</mapper>
