<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.price.dao.IAnnualAmpCustomDao">
    <resultMap type="com.huawei.it.fcst.industry.price.vo.annual.DmFocAnnualAmpVO" id="annualResultMap">
        <result property="id" column="id"/>
        <result property="versionId" column="version_id"/>
        <result property="periodYear" column="period_year"/>
        <result property="prodRndTeamCode" column="prod_rnd_team_code"/>
        <result property="prodRndTeamCnName" column="prod_rnd_team_cn_name"/>
        <result property="prodRndTeamCode" column="prod_rnd_team_catg_code"/>
        <result property="parentLevel" column="parent_level"/>
        <result property="parentCode" column="parent_code"/>
        <result property="parentCnName" column="parent_cn_name"/>
        <result property="groupCode" column="group_code"/>
        <result property="groupCnName" column="group_cn_name"/>
        <result property="groupLevel" column="group_level"/>
        <result property="spartCode" column="spart_code"/>
        <result property="annualAmp" column="annual_amp"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDdate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="appendFlag" column="append_flag"/>
        <result property="weightRate" column="weight_rate"/>
        <result property="statusCode" column="status_code"/>
        <result property="weightAnnualAmpPercent" column="weightAnnualAmpPercent"/>
        <result property="appendYear" column="append_year"/>
        <result property="customCnName" column="custom_cn_name"/>
        <result property="customId" column="custom_id"/>
    </resultMap>

    <select id="allAmpCustomCost" resultMap="annualResultMap">
        select distinct amp.custom_id,amp.custom_cn_name,
        amp.group_level,amp.group_cn_name,amp.group_code,
        amp.period_year,
        CONCAT(ROUND(amp.annual_amp*100,1),'%') as annual_amp,
        status.status_code
        from fin_dm_opt_foi.DM_FCST_PRICE_BASE_CUS_ANNL_AMP_T amp
        left join fin_dm_opt_foi.DM_FCST_PRICE_BASE_CUS_ANNL_WEIGHT_T weight
        on amp.view_flag = weight.view_flag
        and amp.group_code = weight.group_code
        and amp.group_level = weight.group_level
        and amp.parent_code = weight.parent_code
        and amp.version_id =weight.version_id
        and amp.period_year = weight.period_year
        and amp.bg_code = weight.bg_code
        and nvl(amp.parent_code,'snull') = nvl(weight.parent_code,'snull')
        and nvl(amp.oversea_flag,'snull') = nvl(weight.oversea_flag,'snull')
        and nvl(amp.region_code,'snull') = nvl(weight.region_code,'snull')
        and nvl(amp.repoffice_code,'snull') = nvl(weight.repoffice_code,'snull')
        and nvl(amp.sign_top_cust_category_code,'snull') = nvl( weight.sign_top_cust_category_code,'snull')
        and nvl(amp.sign_subsidiary_custcatg_cn_name,'snull') = nvl( weight.sign_subsidiary_custcatg_cn_name,'snull')
        left join fin_dm_opt_foi.DM_FCST_PRICE_BASE_CUS_ANNL_STATUS_T status
        on amp.group_code = status.group_code and amp.group_level = status.group_level
        and amp.custom_id = status.custom_id
        and nvl(amp.parent_code,'snull') = nvl(status.parent_code,'snull')
        and nvl(amp.sign_top_cust_category_code,'snull') = nvl( status.sign_top_cust_category_code,'snull')
        and nvl(amp.sign_subsidiary_custcatg_cn_name,'snull') = nvl( status.sign_subsidiary_custcatg_cn_name,'snull')
        and nvl(amp.oversea_flag,'snull') = nvl( status.oversea_flag,'snull')
        and nvl(amp.region_code,'snull') = nvl( status.region_code,'snull')
        and nvl(amp.repoffice_code,'snull') = nvl( status.repoffice_code,'snull')
        and amp.period_year = status.period_year
        and amp.version_id = status.version_id and amp.view_flag = status.view_flag
        and amp.bg_code = status.bg_code
        where amp.del_flag = 'N'
        <if test='groupLevel != null and groupLevel != ""'>
            and amp.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag != null and overseaFlag != ""'>
            and amp.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and amp.bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and amp.region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and amp.repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='signTopCustCategoryCode != null and signTopCustCategoryCode != ""'>
            and amp.sign_top_cust_category_code = #{signTopCustCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='signSubsidiaryCustcatgCnName != null and signSubsidiaryCustcatgCnName != ""'>
            and amp.sign_subsidiary_custcatg_cn_name = #{signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
        </if>
        <if test='parentLevel != null and parentLevel != ""'>
            and amp.parent_level = #{parentLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and amp.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='periodYear != null and periodYear != ""'>
            and amp.period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and amp.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='customIdList != null and customIdList.size() > 0'>
            <foreach collection='customIdList' item="customId" open="AND amp.custom_id IN (" close=")"
                     index="index" separator=",">
                #{customId}
            </foreach>
        </if>
        <if test='customGroupCodeList != null and customGroupCodeList.size() > 0'>
            <foreach collection='customGroupCodeList' item="code" open="AND amp.group_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND amp.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        order by amp.period_year
    </select>

    <select id="findCustomCodeOrderByWeight" resultMap="annualResultMap">
        select distinct group_code, group_cn_name,ROUND( sum(weight_rate) * 100, 1 ) weight_rate
        from fin_dm_opt_foi.DM_FCST_PRICE_BASE_CUS_ANNL_WEIGHT_T
        where del_flag = 'N'
        and group_level = 'SPART'
        <if test='overseaFlag != null and overseaFlag != ""'>
            and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='signTopCustCategoryCode != null and signTopCustCategoryCode != ""'>
            and sign_top_cust_category_code = #{signTopCustCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='signSubsidiaryCustcatgCnName != null and signSubsidiaryCustcatgCnName != ""'>
            and sign_subsidiary_custcatg_cn_name = #{signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
        </if>
        <if test='parentLevel != null and parentLevel != ""'>
            and parent_level = #{parentLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='periodYear != null and periodYear !=""'>
            and period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='year != null and year != ""'>
            and period_year = #{year,jdbcType=VARCHAR}
        </if>
        <if test='customIdList != null and customIdList.size() > 0'>
            <foreach collection='customIdList' item="customId" open="AND custom_id IN (" close=")"
                     index="index" separator=",">
                #{customId}
            </foreach>
        </if>
        <if test='customParentCodeList != null and customParentCodeList.size() > 0'>
            <foreach collection='customParentCodeList' item="code" open="AND parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        group by group_code, group_cn_name
        ORDER BY weight_rate DESC,group_cn_name
    </select>

    <select id="findCustomCodeOrderMinLevel" resultMap="annualResultMap">
        select amp.LV4_PROD_LIST_CODE as prod_rnd_team_code,amp.LV4_PROD_LIST_CN_NAME as prod_rnd_team_cn_name,
        amp.group_code, amp.group_cn_name, ROUND(SUM(weight.weight_rate*100 ),1) as weight_rate
        from fin_dm_opt_foi.DM_FCST_PRICE_ANNL_MID_GROUP_AMP_T amp
        left join fin_dm_opt_foi.DM_FCST_PRICE_BASE_CUS_ANNL_WEIGHT_T weight
        on amp.view_flag = weight.view_flag
        and amp.group_code = weight.group_code
        and amp.group_level = weight.group_level
        and amp.parent_code = weight.parent_code
        and amp.version_id =weight.version_id
        and amp.period_year = weight.period_year
        and amp.bg_code = weight.bg_code
        and nvl(amp.oversea_flag,'snull') = nvl(weight.oversea_flag,'snull')
        and nvl(amp.region_code,'snull') = nvl(weight.region_code,'snull')
        and nvl(amp.repoffice_code,'snull') = nvl(weight.repoffice_code,'snull')
        and nvl(amp.sign_top_cust_category_code,'snull') = nvl( weight.sign_top_cust_category_code,'snull')
        and nvl(amp.sign_subsidiary_custcatg_cn_name,'snull') = nvl( weight.sign_subsidiary_custcatg_cn_name,'snull')
        where amp.del_flag = 'N'
        and weight.group_level = 'SPART'
        and weight.logic_num = 1
        <if test='customIdList != null and customIdList.size() > 0'>
            <foreach collection='customIdList' item="id" open="AND weight.custom_id IN (" close=")" index="index"
                     separator=",">
                #{id}
            </foreach>
        </if>
        <if test='overseaFlag != null and overseaFlag != ""'>
            and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and weight.bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and weight.region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and weight.repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='signTopCustCategoryCode != null and signTopCustCategoryCode != ""'>
            and weight.sign_top_cust_category_code = #{signTopCustCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='signSubsidiaryCustcatgCnName != null and signSubsidiaryCustcatgCnName != ""'>
            and weight.sign_subsidiary_custcatg_cn_name = #{signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and weight.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and weight.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='periodYear != null and periodYear !=""'>
            and weight.period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='year != null and year != ""'>
            and weight.period_year = #{year,jdbcType=VARCHAR}
        </if>
        <if test='customGroupCodeList != null and customGroupCodeList.size() > 0'>
            <foreach collection='customGroupCodeList' item="code" open="AND weight.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='prodListCodeList != null and prodListCodeList.size() > 0'>
            <choose>
                <when test='teamLevel == "LV0"'>
                    <foreach collection='prodListCodeList' item="code" open="AND amp.LV0_PROD_LIST_CODE IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='teamLevel == "LV1"'>
                    <foreach collection='prodListCodeList' item="code" open="AND amp.LV1_PROD_LIST_CODE IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='teamLevel == "LV2"'>
                    <foreach collection='prodListCodeList' item="code" open="AND amp.LV2_PROD_LIST_CODE IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='teamLevel == "LV3"'>
                    <foreach collection='prodListCodeList' item="code" open="AND amp.LV3_PROD_LIST_CODE IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='teamLevel == "LV4"'>
                    <foreach collection='prodListCodeList' item="code" open="AND amp.LV4_PROD_LIST_CODE IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        group by prod_rnd_team_code,prod_rnd_team_cn_name,amp.group_code, amp.group_cn_name
        ORDER BY weight_rate DESC,group_cn_name
    </select>

    <select id="multiAmpMinLevelChart" resultMap="annualResultMap">
        select amp.parent_code,amp.parent_cn_name,
        amp.period_year,
        CONCAT(ROUND( SUM ( amp.annual_amp*100 ), 1 ),'%') AS annual_amp,
        amp.group_code, amp.group_cn_name, amp.group_level,
        CONCAT(ROUND(SUM ( weight.weight_rate*100 ),1),'%') AS weight_rate,
        status.status_code, max(status.append_year) AS append_year,
        amp.LV4_PROD_LIST_CODE as prod_rnd_team_catg_code,amp.LV4_PROD_LIST_CN_NAME as prod_rnd_team_cn_name
        from fin_dm_opt_foi.DM_FCST_PRICE_ANNL_MID_GROUP_AMP_T amp
        left join fin_dm_opt_foi.DM_FCST_PRICE_BASE_CUS_ANNL_WEIGHT_T weight
        on amp.view_flag = weight.view_flag
        and amp.group_code = weight.group_code
        and amp.group_level = weight.group_level
        and amp.parent_code = weight.parent_code
        and amp.version_id =weight.version_id and amp.period_year = weight.period_year
        and amp.bg_code = weight.bg_code
        and nvl ( amp.oversea_flag, 'snull' ) = nvl ( weight.oversea_flag, 'snull' )
        and nvl ( amp.region_code, 'snull' ) = nvl ( weight.region_code, 'snull' )
        and nvl ( amp.repoffice_code, 'snull' ) = nvl ( weight.repoffice_code, 'snull' )
        and nvl(amp.sign_top_cust_category_code,'snull') = nvl( weight.sign_top_cust_category_code,'snull')
        and nvl(amp.sign_subsidiary_custcatg_cn_name,'snull') = nvl( weight.sign_subsidiary_custcatg_cn_name,'snull')
        <if test='customIdList != null and customIdList.size() > 0'>
            <foreach collection='customIdList' item="id" open="AND weight.custom_id IN (" close=")" index="index"
                     separator=",">
                #{id}
            </foreach>
        </if>
        left join fin_dm_opt_foi.DM_FCST_PRICE_ANNL_STATUS_T status
        on amp.group_code = status.group_code and amp.group_level = status.group_level
        and amp.period_year = status.period_year and amp.version_id = status.version_id
        and amp.view_flag = status.view_flag
        and amp.parent_code = status.parent_code
        and amp.bg_code = status.bg_code
        and nvl ( amp.oversea_flag, 'snull' ) = nvl ( status.oversea_flag, 'snull' )
        and nvl ( amp.region_code, 'snull' ) = nvl ( status.region_code, 'snull' )
        and nvl ( amp.repoffice_code, 'snull' ) = nvl ( status.repoffice_code, 'snull' )
        and nvl(amp.sign_top_cust_category_code,'snull') = nvl( status.sign_top_cust_category_code,'snull')
        and nvl(amp.sign_subsidiary_custcatg_cn_name,'snull') = nvl( status.sign_subsidiary_custcatg_cn_name,'snull')
        where amp.del_flag = 'N'
        <if test='overseaFlag != null and overseaFlag != ""'>
            and amp.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and amp.bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and amp.region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and amp.repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='signTopCustCategoryCode != null and signTopCustCategoryCode != ""'>
            and weight.sign_top_cust_category_code = #{signTopCustCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='signSubsidiaryCustcatgCnName != null and signSubsidiaryCustcatgCnName != ""'>
            and weight.sign_subsidiary_custcatg_cn_name = #{signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and amp.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and amp.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='periodYear != null and periodYear != ""'>
            and amp.period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and amp.version_id = #{versionId}
        </if>
        and amp.group_level = 'SPART'
        <if test='customGroupCodeList != null and customGroupCodeList.size() > 0'>
            <foreach collection='customGroupCodeList' item="code" open="AND amp.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='prodListCodeList != null and prodListCodeList.size() > 0'>
            <choose>
                <when test='teamLevel == "LV0"'>
                    <foreach collection='prodListCodeList' item="code" open="AND amp.LV0_PROD_LIST_CODE IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='teamLevel == "LV1"'>
                    <foreach collection='prodListCodeList' item="code" open="AND amp.LV1_PROD_LIST_CODE IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='teamLevel == "LV2"'>
                    <foreach collection='prodListCodeList' item="code" open="AND amp.LV2_PROD_LIST_CODE IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='teamLevel == "LV3"'>
                    <foreach collection='prodListCodeList' item="code" open="AND amp.LV3_PROD_LIST_CODE IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='teamLevel == "LV4"'>
                    <foreach collection='prodListCodeList' item="code" open="AND amp.LV4_PROD_LIST_CODE IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND amp.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        <if test='prodRndTeamCodeOrder != null and prodRndTeamCodeOrder != ""'>
            <foreach collection="prodRndTeamCodeOrder.split(',')" item="item" open="and amp.LV4_PROD_LIST_CODE IN (" close=")"
                     index="index" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY amp.parent_code,amp.parent_cn_name,prod_rnd_team_cn_name,prod_rnd_team_catg_code,
        amp.group_level,amp.group_code,amp.group_cn_name,amp.period_year,status.status_code
        <if test='condition != null and condition !="" and condition =="minLevel"'>
            order by locate(amp.LV4_PROD_LIST_CODE, #{prodRndTeamCodeOrder}), amp.period_year
        </if>
        <if test='condition == null or condition ==""'>
            order by locate(amp.group_code, #{groupCodeOrder}), amp.period_year
        </if>
    </select>

    <select id="multiAmpCustomChart" resultMap="annualResultMap">
        select
        amp.custom_cn_name,
        amp.custom_id,
        amp.parent_code,
        amp.parent_level,
        amp.parent_cn_name,
        amp.period_year,
        amp.group_level,
        CONCAT(ROUND( SUM ( amp.annual_amp*100 ), 1 ),'%') AS annual_amp,
        amp.group_code AS group_code,
        amp.group_cn_name AS group_cn_name,
        CONCAT(ROUND(SUM ( weight.weight_rate*100 ),1),'%') AS weight_rate,
        status.status_code
        from fin_dm_opt_foi.DM_FCST_PRICE_BASE_CUS_ANNL_AMP_T amp
        left join fin_dm_opt_foi.DM_FCST_PRICE_BASE_CUS_ANNL_WEIGHT_T weight
        on amp.view_flag = weight.view_flag and amp.group_code = weight.group_code
        and nvl(amp.parent_code,'snull') = nvl(weight.parent_code,'snull')
        and nvl(amp.parent_level,'snull') = nvl(weight.parent_level,'snull')
        and amp.custom_id = weight.custom_id
        and amp.version_id =weight.version_id and amp.period_year = weight.period_year
        and amp.bg_code = weight.bg_code
        and nvl(amp.oversea_flag,'snull') = nvl(weight.oversea_flag,'snull')
        and nvl(amp.region_code,'snull') = nvl(weight.region_code,'snull')
        and nvl(amp.repoffice_code,'snull') = nvl(weight.repoffice_code,'snull')
        and nvl(amp.sign_top_cust_category_code,'snull') = nvl( weight.sign_top_cust_category_code,'snull')
        and nvl(amp.sign_subsidiary_custcatg_cn_name,'snull') = nvl( weight.sign_subsidiary_custcatg_cn_name,'snull')
        left join fin_dm_opt_foi.DM_FCST_PRICE_BASE_CUS_ANNL_STATUS_T status
        on amp.group_code = status.group_code and amp.group_level = status.group_level
        and nvl(amp.parent_code,'snull') = nvl(status.parent_code,'snull')
        and nvl(amp.parent_level,'snull') = nvl(status.parent_level,'snull')
        and amp.custom_id = status.custom_id
        and amp.period_year = status.period_year
        and amp.version_id = status.version_id
        and amp.view_flag = status.view_flag
        and amp.bg_code = status.bg_code
        and nvl(amp.oversea_flag,'snull') = nvl(status.oversea_flag,'snull')
        and nvl(amp.region_code,'snull') = nvl(status.region_code,'snull')
        and nvl(amp.repoffice_code,'snull') = nvl(status.repoffice_code,'snull')
        and nvl(amp.sign_top_cust_category_code,'snull') = nvl( status.sign_top_cust_category_code,'snull')
        and nvl(amp.sign_subsidiary_custcatg_cn_name,'snull') = nvl( status.sign_subsidiary_custcatg_cn_name,'snull')
        where amp.del_flag = 'N'
        and amp.group_level = 'SPART'
        <if test='overseaFlag != null and overseaFlag != ""'>
            and amp.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and amp.bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and amp.region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and amp.repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='signTopCustCategoryCode != null and signTopCustCategoryCode != ""'>
            and amp.sign_top_cust_category_code = #{signTopCustCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='signSubsidiaryCustcatgCnName != null and signSubsidiaryCustcatgCnName != ""'>
            and amp.sign_subsidiary_custcatg_cn_name = #{signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
        </if>
        <if test='parentLevel != null and parentLevel != ""'>
            and amp.parent_level = #{parentLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and amp.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and amp.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='periodYear != null and periodYear !=""'>
            and amp.period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='customIdList != null and customIdList.size() > 0'>
            <foreach collection='customIdList' item="customId" open="AND amp.custom_id IN (" close=")"
                     index="index" separator=",">
                #{customId}
            </foreach>
        </if>
        <if test='customParentCodeList != null and customParentCodeList.size() > 0'>
            <foreach collection='customParentCodeList' item="code" open="AND amp.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupCodeOrder != null and groupCodeOrder != ""'>
            <foreach collection="groupCodeOrder.split(',')" item="item" open="and amp.group_code IN (" close=")"
                     index="index" separator=",">
                #{item}
            </foreach>
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND amp.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        GROUP BY amp.parent_level,amp.custom_cn_name,amp.custom_id,amp.parent_code,amp.parent_cn_name,
        amp.group_code,amp.group_cn_name,amp.group_level,amp.period_year,status.status_code
        order by locate(amp.group_code, #{groupCodeOrder}), weight_rate desc
    </select>

    <select id="industryAmpMinLevelList" resultMap="annualResultMap">
        select prod_rnd_team_cn_name,prod_rnd_team_code,
        parent_code,parent_cn_name,period_year,annual_amp,group_code,group_level,group_cn_name,
        weight_rate, status_code, annual_amp * weight_rate/100 as weightAnnualAmpPercent, append_year
        from
        (select distinct amp.group_level,
        amp.parent_code,amp.parent_cn_name,amp.period_year,
        ROUND(amp.annual_amp*100,1) as annual_amp,amp.group_code,
        amp.group_cn_name,weight.weight_rate*100 as weight_rate,
        status.status_code as status_code, status.append_year,
        amp.LV4_PROD_LIST_CODE as prod_rnd_team_code,amp.LV4_PROD_LIST_CN_NAME as prod_rnd_team_cn_name
        from fin_dm_opt_foi.DM_FCST_PRICE_ANNL_MID_GROUP_AMP_T amp
        left join fin_dm_opt_foi.DM_FCST_PRICE_BASE_CUS_ANNL_WEIGHT_T weight
        on amp.view_flag = weight.view_flag and amp.group_code = weight.group_code
        and amp.group_level = weight.group_level
        and amp.parent_code = weight.parent_code
        and amp.version_id =weight.version_id and amp.period_year = weight.period_year
        and amp.bg_code = weight.bg_code
        and nvl ( amp.oversea_flag, 'snull' ) = nvl ( weight.oversea_flag, 'snull' )
        and nvl ( amp.region_code, 'snull' ) = nvl ( weight.region_code, 'snull' )
        and nvl ( amp.repoffice_code, 'snull' ) = nvl ( weight.repoffice_code, 'snull' )
        and nvl(amp.sign_top_cust_category_code,'snull') = nvl( weight.sign_top_cust_category_code,'snull')
        and nvl(amp.sign_subsidiary_custcatg_cn_name,'snull') = nvl( weight.sign_subsidiary_custcatg_cn_name,'snull')
        <if test='customIdList != null and customIdList.size() > 0'>
            <foreach collection='customIdList' item="id" open="AND weight.custom_id IN (" close=")" index="index"
                     separator=",">
                #{id}
            </foreach>
        </if>
        left join fin_dm_opt_foi.DM_FCST_PRICE_ANNL_STATUS_T status
        on amp.group_code = status.group_code and amp.group_level = status.group_level
        and amp.period_year = status.period_year and amp.version_id = status.version_id
        and amp.view_flag = status.view_flag
        and amp.parent_code = status.parent_code
        and amp.oversea_flag = status.oversea_flag
        and amp.bg_code = status.bg_code
        and nvl ( amp.oversea_flag, 'snull' ) = nvl ( status.oversea_flag, 'snull' )
        and nvl ( amp.region_code, 'snull' ) = nvl ( status.region_code, 'snull' )
        and nvl ( amp.repoffice_code, 'snull' ) = nvl ( status.repoffice_code, 'snull' )
        and nvl(amp.sign_top_cust_category_code,'snull') = nvl( status.sign_top_cust_category_code,'snull')
        and nvl(amp.sign_subsidiary_custcatg_cn_name,'snull') = nvl( status.sign_subsidiary_custcatg_cn_name,'snull')
        where amp.del_flag = 'N'
        <if test='overseaFlag != null and overseaFlag != ""'>
            and amp.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and amp.bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and amp.region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and amp.repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='signTopCustCategoryCode != null and signTopCustCategoryCode != ""'>
            and amp.sign_top_cust_category_code = #{signTopCustCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='signSubsidiaryCustcatgCnName != null and signSubsidiaryCustcatgCnName != ""'>
            and amp.sign_subsidiary_custcatg_cn_name = #{signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and amp.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and amp.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and amp.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='periodYear != null and periodYear !=""'>
            and amp.period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='year != null and year !=""'>
            and amp.period_year = #{year,jdbcType=VARCHAR}
        </if>
        and amp.group_level = 'SPART'
        <if test='customGroupCodeList != null and customGroupCodeList.size() > 0'>
            <foreach collection='customGroupCodeList' item="code" open="AND amp.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='prodListCodeList != null and prodListCodeList.size() > 0'>
            <choose>
                <when test='teamLevel == "LV0"'>
                    <foreach collection='prodListCodeList' item="code" open="AND amp.LV0_PROD_LIST_CODE IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='teamLevel == "LV1"'>
                    <foreach collection='prodListCodeList' item="code" open="AND amp.LV1_PROD_LIST_CODE IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='teamLevel == "LV2"'>
                    <foreach collection='prodListCodeList' item="code" open="AND amp.LV2_PROD_LIST_CODE IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='teamLevel == "LV3"'>
                    <foreach collection='prodListCodeList' item="code" open="AND amp.LV3_PROD_LIST_CODE IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='teamLevel == "LV4"'>
                    <foreach collection='prodListCodeList' item="code" open="AND amp.LV4_PROD_LIST_CODE IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND amp.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        )
        order by period_year,weight_rate desc,group_cn_name
    </select>

    <select id="industryCustomList" resultMap="annualResultMap">
        select
        custom_cn_name,custom_id,parent_level,parent_code,parent_cn_name,period_year,annual_amp,group_code,group_cn_name,group_level,
        weight_rate, status_code, annual_amp * weight_rate/100 as weightAnnualAmpPercent
        from
        (select amp.custom_cn_name,amp.custom_id,amp.parent_code,amp.parent_level,amp.parent_cn_name,amp.period_year,
        ROUND(sum(amp.annual_amp*100),1) as annual_amp,amp.group_code,amp.group_cn_name,amp.group_level, status.status_code,
        <choose>
            <when test='annualAnalysisVO.isMultipleSelect == true'>
                sum(weight.absolute_weight*100) as weight_rate
            </when>
            <otherwise>
                sum(weight.weight_rate*100) as weight_rate
            </otherwise>
        </choose>
        from fin_dm_opt_foi.DM_FCST_PRICE_BASE_CUS_ANNL_AMP_T amp
        left join fin_dm_opt_foi.DM_FCST_PRICE_BASE_CUS_ANNL_WEIGHT_T weight
        on amp.view_flag = weight.view_flag and amp.group_code = weight.group_code
        and nvl(amp.parent_code,'snull') = nvl(weight.parent_code,'snull')
        and nvl(amp.parent_level,'snull') = nvl(weight.parent_level,'snull')
        and amp.custom_id = weight.custom_id
        and amp.version_id =weight.version_id
        and amp.period_year = weight.period_year
        and amp.bg_code = weight.bg_code
        and nvl(amp.oversea_flag,'snull') = nvl(weight.oversea_flag,'snull')
        and nvl(amp.region_code,'snull') = nvl(weight.region_code,'snull')
        and nvl(amp.repoffice_code,'snull') = nvl(weight.repoffice_code,'snull')
        and nvl(amp.sign_top_cust_category_code,'snull') = nvl( weight.sign_top_cust_category_code,'snull')
        and nvl(amp.sign_subsidiary_custcatg_cn_name,'snull') = nvl( weight.sign_subsidiary_custcatg_cn_name,'snull')
        left join DM_FCST_PRICE_BASE_CUS_ANNL_STATUS_T status
        on amp.group_code = status.group_code and amp.group_level = status.group_level
        and nvl(amp.parent_code,'snull') = nvl(status.parent_code,'snull')
        and nvl(amp.parent_level,'snull') = nvl(status.parent_level,'snull')
        and amp.custom_id = status.custom_id
        and amp.period_year = status.period_year
        and amp.version_id = status.version_id
        and amp.view_flag = status.view_flag
        and amp.bg_code = status.bg_code
        and nvl(amp.oversea_flag,'snull') = nvl(status.oversea_flag,'snull')
        and nvl(amp.region_code,'snull') = nvl(status.region_code,'snull')
        and nvl(amp.repoffice_code,'snull') = nvl(status.repoffice_code,'snull')
        and nvl(amp.sign_top_cust_category_code,'snull') = nvl( status.sign_top_cust_category_code,'snull')
        and nvl(amp.sign_subsidiary_custcatg_cn_name,'snull') = nvl( status.sign_subsidiary_custcatg_cn_name,'snull')
        where amp.del_flag = 'N'
        <if test='annualAnalysisVO.viewFlag != null and annualAnalysisVO.viewFlag != "" and annualAnalysisVO.viewFlag == "PROD_SPART"'>
            and amp.group_level = 'SPART'
        </if>
        <if test='annualAnalysisVO.viewFlag != null and annualAnalysisVO.viewFlag != "" and annualAnalysisVO.viewFlag == "DIMENSION"'>
            and amp.group_level = 'SUB_DETAIL'
        </if>
        <if test='annualAnalysisVO.overseaFlag != null and annualAnalysisVO.overseaFlag != ""'>
            and amp.oversea_flag = #{annualAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.bgCode != null and annualAnalysisVO.bgCode != ""'>
            and amp.bg_code = #{annualAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.regionCode != null and annualAnalysisVO.regionCode != ""'>
            and amp.region_code = #{annualAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.repofficeCode != null and annualAnalysisVO.repofficeCode != ""'>
            and amp.repoffice_code = #{annualAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.signTopCustCategoryCode != null and annualAnalysisVO.signTopCustCategoryCode != ""'>
            and amp.sign_top_cust_category_code = #{annualAnalysisVO.signTopCustCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.signSubsidiaryCustcatgCnName != null and annualAnalysisVO.signSubsidiaryCustcatgCnName != ""'>
            and amp.sign_subsidiary_custcatg_cn_name = #{annualAnalysisVO.signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.parentLevel != null and annualAnalysisVO.parentLevel != ""'>
            and amp.parent_level = #{annualAnalysisVO.parentLevel,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.viewFlag != null and annualAnalysisVO.viewFlag != ""'>
            and amp.view_flag = #{annualAnalysisVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.versionId != null'>
            and amp.version_id = #{annualAnalysisVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='annualAnalysisVO.periodYear != null and annualAnalysisVO.periodYear !=""'>
            and amp.period_year = #{annualAnalysisVO.periodYear,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.customIdList != null and annualAnalysisVO.customIdList.size() > 0'>
            <foreach collection='annualAnalysisVO.customIdList' item="customId" open="AND amp.custom_id IN (" close=")" index="index"
                     separator=",">
                #{customId}
            </foreach>
        </if>
        <if test='annualAnalysisVO.customParentCodeList != null and annualAnalysisVO.customParentCodeList.size() > 0'>
            <foreach collection='annualAnalysisVO.customParentCodeList' item="code" open="AND amp.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='annualAnalysisVO.year != null and annualAnalysisVO.year !=""'>
            and amp.period_year = #{annualAnalysisVO.year,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.yearList != null and annualAnalysisVO.yearList.size() > 0'>
            <foreach collection='annualAnalysisVO.yearList' item="year" open="AND amp.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        group by
        amp.custom_cn_name,amp.custom_id,amp.parent_code,amp.parent_level,amp.parent_cn_name,amp.period_year,
        amp.group_code,amp.group_cn_name,amp.group_level, status.status_code
        )
        order by period_year,weight_rate desc,group_cn_name
    </select>

</mapper>