/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.mix;

import cn.hutool.core.map.MapUtil;
import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.industry.pbi.dao.IMixManagementDao;
import com.huawei.it.fcst.industry.pbi.enums.IndustryConstEnum;
import com.huawei.it.fcst.industry.pbi.vo.mix.DmFocMixResultVO;
import com.huawei.it.fcst.industry.pbi.vo.mix.MixSearchVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Named;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * MixCommonService Class
 *
 * <AUTHOR>
 * @since 2024/10/16
 */
@Slf4j
@Named("mixCommonService")
public class MixCommonService {

    @Autowired
    private IMixManagementDao mixManagementDao;
    public void getDbforCurrentPriceIndex(MixSearchVO mixSearchVO, List<DmFocMixResultVO> allPriceIndexList) {
        String isNeedBlur = mixSearchVO.getIsNeedBlur();
        List<String> costTypeList = mixSearchVO.getCostTypeList();
        if (null != mixSearchVO.getCustomId()) {
            // 查汇总组合后的维表,月度或月度累计
            for (int i = 0; i < costTypeList.size(); i++) {
                String costType = costTypeList.get(i);
                mixSearchVO.setCostType(costType);
                List<DmFocMixResultVO> dmFocPriceIndexList = mixManagementDao.getSummaryCombCurrentPriceIndex(mixSearchVO);
                dmFocPriceIndexList.forEach(dm -> dm.setCostType(mixSearchVO.getCostType()));
                allPriceIndexList.addAll(dmFocPriceIndexList);
            }
        } else {
            for (int i = 0; i < costTypeList.size(); i++) {
                String costType = costTypeList.get(i);
                mixSearchVO.setCostType(costType);
                if (CommonConstant.BLUR_TRUE.equals(isNeedBlur)) {
                    // 虚化
                    List<DmFocMixResultVO> dmFocPriceIndexList = mixManagementDao.findCustomPriceIndexMultiType(mixSearchVO);
                    dmFocPriceIndexList.stream().forEach(dm -> dm.setCostType(costType));
                    allPriceIndexList.addAll(dmFocPriceIndexList);
                } else if (CommonConstant.BLUR_TWO_CONDITION.equals(isNeedBlur)) {
                    boolean pspCustomFlag = CollectionUtils.isNotEmpty(mixSearchVO.getPspCustomIdList()) && IndustryConstEnum.COST_TYPE.PSP.getValue().equals(mixSearchVO.getCostType());
                    boolean stdCustomFlag = CollectionUtils.isNotEmpty(mixSearchVO.getStdCustomIdList()) && IndustryConstEnum.COST_TYPE.STD.getValue().equals(mixSearchVO.getCostType());
                    if (pspCustomFlag || stdCustomFlag) {
                        List<DmFocMixResultVO> customPriceIndexList = mixManagementDao.findCustomPriceIndexMultiType(mixSearchVO);
                        customPriceIndexList.stream().forEach(dm -> dm.setCostType(costType));
                        allPriceIndexList.addAll(customPriceIndexList);
                    }
                    boolean pspNormalFlag = CollectionUtils.isNotEmpty(mixSearchVO.getPspGroupCodeList()) && IndustryConstEnum.COST_TYPE.PSP.getValue().equals(mixSearchVO.getCostType());
                    boolean stdNormalFlag = CollectionUtils.isNotEmpty(mixSearchVO.getStdGroupCodeList()) && IndustryConstEnum.COST_TYPE.STD.getValue().equals(mixSearchVO.getCostType());
                    if (pspNormalFlag || stdNormalFlag) {
                        List<DmFocMixResultVO> normalPriceIndexList = mixManagementDao.findPriceIndexMultiType(mixSearchVO);
                        normalPriceIndexList.stream().forEach(dm -> dm.setCostType(costType));
                        allPriceIndexList.addAll(normalPriceIndexList);
                    }
                }else {
                    List<DmFocMixResultVO> dmFocPriceIndexList = mixManagementDao.findPriceIndexMultiType(mixSearchVO);
                    dmFocPriceIndexList.stream().forEach(dm -> dm.setCostType(costType));
                    allPriceIndexList.addAll(dmFocPriceIndexList);
                }
            }
        }
    }

    public void setCurrentDataList(List<Map> dataList, HashMap<String, List<DmFocMixResultVO>> resultColumnMap, Set<String> resultRowSet) {
        for (String keyStr : resultRowSet) {
            List<DmFocMixResultVO> resultDataList = MapUtil.get(resultColumnMap, keyStr, List.class);
            Map<String, Object> currentDataMap = new HashMap<>();
            for (int i = 0; i < resultDataList.size(); i++) {
                String periodId = resultDataList.get(i).getPeriodId();
                String costType = resultDataList.get(i).getCostType();
                String costIndex = resultDataList.get(i).getCostIndex();
                currentDataMap.put("periodId", periodId);
                if ("PSP".equals(costType)) {
                    currentDataMap.put("pspCostIndex", costIndex);
                } else {
                    currentDataMap.put("stdCostIndex", costIndex);
                }
            }
            dataList.add(currentDataMap);
        }
    }


}
