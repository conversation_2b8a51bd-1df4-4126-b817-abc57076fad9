/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.annual;

import com.huawei.it.fcst.annotations.ExportAttribute;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * DmFocAnnualAmpVO Class
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
// 此@data注解不可以被替换成getter和setter
@Data
@NoArgsConstructor
public class DmFocAnnualAmpVO {
    private int id;

    private Long periodId;

    private Long versionId;

    @ExportAttribute(sort = 0)
    private String periodYear;

    private String prodRndTeamCode;

    private String prodRndTeamCnName;

    private String lv0ProdRndTeamCnName;

    private String lv0ProdRndTeamCode;

    private String lv1ProdRndTeamCnName;

    private String lv1ProdRndTeamCode;

    private String lv2ProdRndTeamCnName;

    private String lv2ProdRndTeamCode;

    private String lv3ProdRndTeamCnName;

    private String lv3ProdRndTeamCode;

    private String lv4ProdRndTeamCnName;

    private String lv4ProdRndTeamCode;

    private String spartCnName;

    private String spartCode;

    private String coaCnName;

    private String coaCode;

    private String l3CegCnName;

    private String l3CegCode;

    private String l4CegCnName;

    private String l4CegCode;

    private String categoryCnName;

    private String categoryCode;

    private String groupCode;

    private String groupCnName;

    private String groupLevel;

    @ExportAttribute(sort = 1)
    private String annualAmp;

    private String sameAnnualAmp;

    private String replAnnualAmp;

    private String parentCode;

    private String parentCnName;

    private String parentLevel;

    private String createdBy;

    private Timestamp creationDate;

    private String lastUpdatedBy;

    private Timestamp lastUpdateDdate;

    private Timestamp lastUpdateDate;

    private String delFlag;

    private String viewFlag;

    private String appendFlag;

    private String weightRate;

    private String sameWeightRate;

    private String replWeightRate;

    private String groupCodeAndName;

    private String weightAnnualAmpPercent;

    private String replWeightAnnualAmpPercent;

    private String sameWeightAnnualAmpPercent;

    private String weightAnnualAmpPercentOrder;

    private String statusCode;

    private String rmbCostPer;

    // 提示信息
    private String hoverMsg;

    // 加解密使用
    private String key;

    private String iv;

    // 哪年补齐
    private String appendYear;

    // 盈利颗粒度l1name
    private String l1Name;

    // 盈利颗粒度l2name
    private String l2Name;

    // 量纲颗粒度 dmsCnName
    private String dmsCnName;

    // 量纲颗粒度 dmsCnCode
    private String dmsCode;

    // 组合 customName
    private String customCnName;

    //  组合 customCode
    private Long customId;

    private String isHasComb;

    /**
     * 量纲颗粒度code
     **/
    private String dimensionCode;

    /**
     * 量纲颗粒度名称
     **/
    private String dimensionCnName;

    /**
     * 量纲颗粒度code
     **/
    private String dimensionSubCategoryCode;

    /**
     * 量纲颗粒度名称
     **/
    private String dimensionSubCategoryCnName;

    /**
     * 量纲颗粒度code
     **/
    private String dimensionSubDetailCode;

    /**
     * 量纲颗粒度名称
     **/
    private String dimensionSubDetailCnName;

    /**
     * 发货对象code
     **/
    private String shippingObjectCode;

    /**
     * 发货对象名称
     **/
    private String shippingObjectCnName;

    /**
     * 制造对象code
     **/
    private String manufactureObjectCode;

    /**
     * 制造对象名称
     **/
    private String manufactureObjectCnName;

    /**
     * 反向视角，采购code
     **/
    private String purCode;

    /**
     * 反向视角，采购名称
     **/
    private String purCnName;

    /**
     * 成本金额
     **/
    private String rmbCostAmt;

    private Double rmbCostAmtDouble;

    // 采购金额
    private Double purchaseRmbCostAmt;

    // 制造金额
    private Double manufactureRmbCostAmt;

    // 总成本金额
    private Double totalRmbCostAmt;

    // 采购占比
    private Double purchasePercentage;

    // 制造占比
    private Double manfacturePercentage;

    /**
     * 成本类型（采购:P；制造:M）
     **/
    private String costType;

    private String costTypeValue;

    private String costSubType;

    /**
     * 百分百
     **/
    private String percentage;

    private Double percentageDouble;

    private String isComb;

    private String dataType;

    private Double costIndex;

}
