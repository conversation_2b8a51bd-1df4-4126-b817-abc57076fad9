/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.annual.AnnualAnalysisVO;
import com.huawei.it.fcst.industry.index.vo.annual.DmFocAnnualAmpVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * IAnnualAmpDao
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
public interface IAnnualAmpDao {
    List<DmFocAnnualAmpVO> allIndustryCost(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> allIndustryRevCost(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> allIndustryCombCost(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> multiIndustryCostChart(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> multiIndustryCostChartMultiSelect(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> multiIndustryCostNormalChart(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> findGroupCodeOrderByWeight(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> findCombItemCodeOrderByWeight(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> findProdteamCodeOrderByWeight(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> findRevGroupCodeOrderByWeight(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> findGranularityTypeOrderByWeight(AnnualAnalysisVO annualAnalysisVO);

    List<String> getAnnualPeriodYear(@Param("tablePreFix") String tablePreFix,@Param("versionId") Long versionId);

    List<DmFocAnnualAmpVO> multiIndustryCombChart(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> industryCostList(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> industryRevCostList(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> industryCostAllList(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> industryCostNormalList(AnnualAnalysisVO annualAnalysisVO);

    int industryCostAllItemCount(AnnualAnalysisVO annualAnalysisVO);

    int industryCostCombItemCount(AnnualAnalysisVO annualAnalysisVO);

    int industryCostNormalItemCount(AnnualAnalysisVO annualAnalysisVO);

    PagedResult<DmFocAnnualAmpVO> industryCombCharPage(@Param("annualAnalysisVO") AnnualAnalysisVO annualAnalysisVO, @Param("pageVO") PageVO pageVO);

    List<DmFocAnnualAmpVO> industryCombCharlist(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> multiIndustryChartRevMultiSelect(AnnualAnalysisVO annualAnalysisVO);

}
