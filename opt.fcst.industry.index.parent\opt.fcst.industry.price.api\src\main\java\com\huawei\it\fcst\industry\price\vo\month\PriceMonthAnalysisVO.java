/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.vo.month;

import com.huawei.it.fcst.industry.price.vo.common.CommonPriceBaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 综合指数分析VO实体类
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Builder
@Getter
@Setter
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "综合指数分析VO实体类")
public class PriceMonthAnalysisVO extends CommonPriceBaseVO implements Serializable {

    private static final long serialVersionUID = -5453606724142940760L;

    @ApiModelProperty("虚化组合ID")
    private Long customId;

    @ApiModelProperty("组合名称")
    private String customCnName;

    @ApiModelProperty("虚化组合ID集合")
    private List<Long> customIdList;

    @ApiModelProperty("虚化组合ID集合")
    private String customIds;

    @ApiModelProperty("会计年")
    private Integer periodYear;

    @ApiModelProperty("年区间")
    private String intervalYear;

    @ApiModelProperty("会计月")
    private Integer periodId;

    @ApiModelProperty("基期月份")
    private Integer basePeriodId;

    @ApiModelProperty("BG编码")
    private String bgCode;

    @ApiModelProperty("BG名称")
    private String bgCnName;

    @ApiModelProperty("PBI目录树")
    private String granularityType;

    @ApiModelProperty("PBI目录树名称")
    private String granularityTypeCnName;

    @ApiModelProperty("国内/海外中文名称")
    private String overseaFlagCnName;

    @ApiModelProperty("各层级正常项编码集合")
    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<String> groupCodeList;

    @ApiModelProperty("父层级项编码集合")
    private List<String> parentCodeList;

    @ApiModelProperty("正常子项编码集合")
    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<String> subGroupCodeList;

    @ApiModelProperty("下一层级")
    private String nextGroupLevel;

    @ApiModelProperty("父级层级")
    private String parentLevel;

    @ApiModelProperty("重量级团队层级")
    private String teamLevel;

    @ApiModelProperty("各层级虚化项编码集合")
    private List<String> customGroupCodeList;

    @ApiModelProperty("层级中文名称")
    private String groupCnName;

    @ApiModelProperty("父层级编码")
    private String parentCode;

    @ApiModelProperty("父层级中文名称")
    private String parentCnName;

    @ApiModelProperty("子层级中文名称")
    private String subCnName;

    @ApiModelProperty("产品LV4编码")
    private String lv4ProdListCode;

    @ApiModelProperty("产品LV4名称")
    private String lv4ProdListCnName;

    @ApiModelProperty("价格指数值")
    private Double costIndex;

    @ApiModelProperty("权重值")
    private Double weightRate;

    @ApiModelProperty("百分比权重值")
    private String weightPercent;

    @ApiModelProperty("同环比值")
    private Double rate;

    @ApiModelProperty("同比")
    private String yoyRate;

    @ApiModelProperty("环比")
    private String popRate;

    @ApiModelProperty("同环比百分比值")
    private String ratePercent;

    @ApiModelProperty("同环比标识（YOY：同比，POP：环比）")
    private String rateFlag;

    @ApiModelProperty("补齐标识（Y：补齐数据、N：真实数据）")
    private String appendFlag;

    // 用于数据下载时表格展示
    @ApiModelProperty("地区部或大T系统部名称")
    private String regOrDtCnName;

    // 用于数据下载时表格展示
    @ApiModelProperty("代表处或希望系统名称")
    private String repOrSnCnName;

    @ApiModelProperty("是否需要虚化")
    private Boolean isNeedBlur;

    @ApiModelProperty("是否多选")
    private Boolean isMultipleSelect;

    @ApiModelProperty("导出标识")
    private boolean exportFlag;

}