/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.utils;

import com.huawei.it.fcst.industry.index.impl.annual.StatisticsExcelService;
import com.huawei.it.fcst.industry.index.vo.common.AbstractExcelTitleVO;
import com.huawei.it.fcst.industry.index.vo.common.Constants;
import com.huawei.it.fcst.industry.index.vo.common.DmFoiImpExpRecordVO;
import com.huawei.it.fcst.industry.index.vo.common.ExcelVO;
import com.huawei.it.fcst.industry.index.vo.common.ExportExcelVo;
import com.huawei.it.fcst.industry.index.vo.common.UploadInfoVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;

import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

/**
 * This class ExcelFactory.java
 *
 * <AUTHOR>
 * @since 2021年2月9日
 */
@Component
public class ExcelUtil {
    private static final Logger logger = LoggerFactory.getLogger(ExcelUtil.class);

    private static final String FILE = "file";

    private static final String METHOD = "method";

    @Value("${spring.profiles.active}")
    private String env;

    @Autowired
    private ExcelUtilPro excelUtilPro;

    @Autowired
    private StatisticsExcelService statisticsExcelService;

    /**
     * expSelectColumnExcel导出可选择列的excel
     *
     * @param exportExcelVoList 数据
     * @param response          返回
     * @throws CommonApplicationException 异常处理
     */
    public DmFoiImpExpRecordVO expSelectColumnExcel(List<ExportExcelVo> exportExcelVoList, HttpServletResponse response)
            throws CommonApplicationException, IOException {
        setExportExcelVo(exportExcelVoList);
        return this.expSelTitle(response, exportExcelVoList);
    }

    public void setExportExcelVo(List<ExportExcelVo> exportExcelVoList) {
        for (ExportExcelVo exportExcelVo : exportExcelVoList) {
            List<AbstractExcelTitleVO> leafExcelTitleVO = new ArrayList<>();
            int titleRowCount = excelUtilPro.adjustTitleVoList(exportExcelVo.getTitleVoList(), leafExcelTitleVO);
            List<AbstractExcelTitleVO> selectedLeafExcelTitleVO = leafExcelTitleVO;
            if (exportExcelVo.getMergeCell()) {
                selectedLeafExcelTitleVO = leafExcelTitleVO.stream()
                        .limit(leafExcelTitleVO.size() - 1)
                        .collect(Collectors.toList());
            }
            exportExcelVo.setSelectedLeafExcelTitleVO(selectedLeafExcelTitleVO);
            exportExcelVo.setTitleRowCount(titleRowCount);
        }
    }

    /**
     * 导入excel，读取数据
     *
     * @param heads 表头
     * @return List<Map>
     * @throws CommonApplicationException
     * @throws IOException
     */
    public List<LinkedHashMap<String, Object>> importExcel(Attachment attachment, List<ExcelVO> heads,
                                                           UploadInfoVO uploadInfoVO, ByteArrayOutputStream byteArrayOutputStream) throws CommonApplicationException, IOException {
        if (!(attachment.getDataHandler().getName()).endsWith(Constants.XLSX.getValue()) &&
                !(attachment.getDataHandler().getName()).endsWith(Constants.XLS.getValue())) {
            String msg = "请导入Excel xlsx xls格式下的文件";
            try (InputStream inputStream = getInputStream(byteArrayOutputStream)) {
                // 统计导入错误信息
                // 统计导入错误信息
                DmFoiImpExpRecordVO dmFoiImpExpRecordVO = new DmFoiImpExpRecordVO();
                dmFoiImpExpRecordVO.setExceptionFeedback(msg);
                dmFoiImpExpRecordVO.setCreationDate(uploadInfoVO.getCreationDate());
                dmFoiImpExpRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                statisticsExcelService.getImportUploadFileKey(dmFoiImpExpRecordVO, uploadInfoVO.getUserId(), 1, inputStream);
                statisticsExcelService.insertImportExcel(dmFoiImpExpRecordVO, uploadInfoVO, false, null);
                throw new CommonApplicationException(msg);
            } catch (Exception exception) {
                msg = msg == null ? exception.getMessage() : msg;
                throw new CommonApplicationException(msg);
            }
        }
        return this.readExcel(attachment, heads, uploadInfoVO, byteArrayOutputStream);
    }

    private static void closeIO(InputStream in) throws IOException {
        if (in != null) {
            try {
                in.close();
            } catch (IOException e) {
                logger.error("close IOException.{}", e);
            } finally {
                in.close();
            }
        }
    }

    public List<LinkedHashMap<String, Object>> readExcel(Attachment attachment, List<ExcelVO> heads,
                                                         UploadInfoVO uploadInfoVO, ByteArrayOutputStream byteArrayOutputStream) throws CommonApplicationException, IOException {
        ZipSecureFile.setMinInflateRatio(-1.0d);
        XSSFWorkbook workbook = this.createWorkbook(attachment, uploadInfoVO, byteArrayOutputStream);
        return getMapList(heads, uploadInfoVO, workbook, byteArrayOutputStream);
    }

    private List<LinkedHashMap<String, Object>> getMapList(List<ExcelVO> heads, UploadInfoVO uploadInfoVO, XSSFWorkbook workbook, ByteArrayOutputStream byteArrayOutputStream)
            throws CommonApplicationException, IOException {
        List<LinkedHashMap<String, Object>> list = null;
        InputStream inputStream = null;
        String msg = null;
        try {
            int lastRowNum = MapUtil.getInt(uploadInfoVO.getParams(), "maxRowNum");
            XSSFSheet sheet = workbook.getSheetAt(0);
            uploadInfoVO.setSheetName(sheet.getSheetName());
            inputStream = getInputStream(byteArrayOutputStream);
            if (sheet.getLastRowNum() <= lastRowNum && sheet.getLastRowNum() > 0) {
                list = this.readDataByExcel(heads, sheet, uploadInfoVO, inputStream);
            } else if (sheet.getLastRowNum() <= 0) {
                // 统计导入错误信息
                DmFoiImpExpRecordVO dmFoiImpExpRecordVO = new DmFoiImpExpRecordVO();
                dmFoiImpExpRecordVO.setCreationDate(uploadInfoVO.getCreationDate());
                dmFoiImpExpRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                statisticsExcelService.getImportUploadFileKey(dmFoiImpExpRecordVO, uploadInfoVO.getUserId(), 1, inputStream);
                msg = "导入的是空文件";
                dmFoiImpExpRecordVO.setExceptionFeedback(msg);
                statisticsExcelService.insertImportExcel(dmFoiImpExpRecordVO, uploadInfoVO, false, 0);
                throw new CommonApplicationException(msg);
            } else {
                // 统计导入错误信息
                DmFoiImpExpRecordVO dmFoiImpExpRecordVO = new DmFoiImpExpRecordVO();
                dmFoiImpExpRecordVO.setExceptionFeedback("导入的文件超过允许文件的最大行数:" + lastRowNum);
                statisticsExcelService.getImportUploadFileKey(dmFoiImpExpRecordVO, uploadInfoVO.getUserId(), 1, inputStream);
                statisticsExcelService.insertImportExcel(dmFoiImpExpRecordVO, uploadInfoVO, false, sheet.getLastRowNum());
                msg = "导入的文件验证不通过,请检查当前的最大文件行数是:".concat(String.valueOf(sheet.getLastRowNum()))
                        .concat(Constants.DH.getValue())
                        .concat("允许文件的最大行数是:" + lastRowNum)
                        .concat(Constants.DH.getValue())
                        .concat("文件只支持一个sheet页导入,读取的文件sheet是:" + workbook.getNumberOfSheets());
                throw new CommonApplicationException(msg);
            }
        } catch (Exception e) {
            msg = msg == null ? e.getMessage() : msg;
            throw new CommonApplicationException(msg);
        } finally {
            // 关闭流
            closeIO(inputStream);
        }
        return list;
    }

    private List<LinkedHashMap<String, Object>> readDataByExcel(List<ExcelVO> heads, XSSFSheet sheet, UploadInfoVO uploadInfoVO, InputStream inputStream)
            throws CommonApplicationException, IOException {
        List<LinkedHashMap<String, Object>> list = new ArrayList<>();
        int lastRowNum = sheet.getLastRowNum();
        for (int rowNum = 1; rowNum <= lastRowNum; rowNum++) {
            XSSFRow row = sheet.getRow(rowNum);
            if (row == null) {
                break;
            }
            LinkedHashMap<String, Object> data = new LinkedHashMap<String, Object>();
            short lastCellNum = row.getLastCellNum();
            if (heads.size() == lastCellNum) {
                getReadExcelList(heads, row, data);
                list.add(data);
            } else {
                DmFoiImpExpRecordVO dmFoiImpExpRecordVO = new DmFoiImpExpRecordVO();
                dmFoiImpExpRecordVO.setCreationDate(uploadInfoVO.getCreationDate());
                dmFoiImpExpRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                dmFoiImpExpRecordVO.setExceptionFeedback("列名出错");
                statisticsExcelService.getImportUploadFileKey(dmFoiImpExpRecordVO, uploadInfoVO.getUserId(), 1, inputStream);
                statisticsExcelService.insertImportExcel(dmFoiImpExpRecordVO, uploadInfoVO, false, sheet.getLastRowNum() - 1);
                throw new CommonApplicationException("列名出错");
            }
        }
        return list;
    }

    private void getReadExcelList(List<ExcelVO> heads, XSSFRow row, LinkedHashMap<String, Object> data) throws CommonApplicationException {
        for (int cellNum = 0; cellNum < heads.size(); cellNum++) {
            XSSFCell cell = row.getCell(cellNum);
            String title = heads.get(cellNum).getHeadName();
            if (!StrUtil.equals(Constants.SNULL.getValue(), title)) {
                data.put(heads.get(cellNum).getHeadName(),
                        excelUtilPro.getStringCellValue(cell, heads.get(cellNum).getHeadType()));
            }
        }
    }

    private XSSFWorkbook createWorkbook(Attachment attachment, UploadInfoVO uploadInfoVO, ByteArrayOutputStream byteArrayOutputStream)
            throws CommonApplicationException,IOException{
        XSSFWorkbook wobook = null;
        String fileName = attachment.getDataHandler().getName();
        Long userId = uploadInfoVO.getUserId();
        InputStream inputStream = null;
        try {
            inputStream = getInputStream(byteArrayOutputStream);
            if (fileName.endsWith(Constants.XLSX.getValue()) || fileName.endsWith(Constants.XLS.getValue())) {
                if (inputStream.available() >= 2048000) {
                    // 统计导入错误信息
                    DmFoiImpExpRecordVO dmFoiImpExpRecordVO = new DmFoiImpExpRecordVO();
                    dmFoiImpExpRecordVO.setCreationDate(uploadInfoVO.getCreationDate());
                    dmFoiImpExpRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                    dmFoiImpExpRecordVO.setExceptionFeedback("文件允许的大小是：2兆");
                    statisticsExcelService.getImportUploadFileKey(dmFoiImpExpRecordVO, userId, 1, inputStream);
                    statisticsExcelService.insertImportExcel(dmFoiImpExpRecordVO, uploadInfoVO, false, null);
                    throw new CommonApplicationException("文件允许的大小是：2兆.");
                } else {
                    wobook = new XSSFWorkbook(inputStream);
                }
            } else {
                DmFoiImpExpRecordVO dmFoiImpExpRecordVO = new DmFoiImpExpRecordVO();
                dmFoiImpExpRecordVO.setCreationDate(uploadInfoVO.getCreationDate());
                dmFoiImpExpRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                dmFoiImpExpRecordVO.setExceptionFeedback("只能使用XLSX OR XLS格式EXCEL的文件");
                statisticsExcelService.getImportUploadFileKey(dmFoiImpExpRecordVO, userId, 1, inputStream);
                statisticsExcelService.insertImportExcel(dmFoiImpExpRecordVO, uploadInfoVO, false, null);
                throw new CommonApplicationException("只能使用XLSX OR XLS格式EXCEL的文件.");
            }
            return wobook;
        } catch (IOException exception) {
            logger.info("Failed to read the file：{}", exception.getMessage());
            throw new CommonApplicationException("读取文件异常");
        } finally {
            // 关闭流
            if (null != inputStream) {
                inputStream.close();
            }
        }
    }

    /**
     * 存储输入流，以便后面使用
     *
     * @param inputStream 参数
     * @return ByteArrayOutputStream
     */
    public ByteArrayOutputStream putInputStreamCacher(InputStream inputStream) {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int len;
        try {
            while ((len = inputStream.read(buffer)) > -1) {
                byteArrayOutputStream.write(buffer, 0, len);
            }
            byteArrayOutputStream.flush();
        } catch (IOException exception) {
            logger.error("存储输入流异常：{}", exception.getMessage());
        }
        return byteArrayOutputStream;
    }

    public InputStream getInputStream(ByteArrayOutputStream byteArrayOutputStream) {
        return new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
    }


    /**
     * [服务名称]expByS3
     *
     * @param response          入参
     * @param exportExcelVoList 入参
     * @throws CommonApplicationException void
     * <AUTHOR>
     */
    public DmFoiImpExpRecordVO expSelTitle(HttpServletResponse response, List<ExportExcelVo> exportExcelVoList)
            throws CommonApplicationException, IOException {
        if (null != response) {
            OutputStream os = null;
            try {
                os = response.getOutputStream();
                response.reset();
                response.setHeader("Content-disposition",
                        "attachment; filename="
                                + new String(("template" + ".xlsx").getBytes("gbk"), "ISO8859-1"));
                response.setContentType("application/vnd.ms-excel;charset=utf-8");
                return PoiEnum.exportExcel(os, exportExcelVoList);
            } catch (IOException e) {
                throw new CommonApplicationException("出现异常" + e.getMessage());
            } finally {
                os.close();
            }
        } else {
            return PoiEnum.exportExcel(null, exportExcelVoList);
        }
    }
}
