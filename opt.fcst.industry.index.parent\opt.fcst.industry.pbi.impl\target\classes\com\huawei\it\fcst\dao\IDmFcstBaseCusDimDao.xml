<?xml version="1.0" encoding="UTF-8" ?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.pbi.dao.IDmFcstBaseCusDimDao">
<resultMap type="com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstBaseCusDimVO" id="resultMap">
    <result property="bgCode" column="bg_code"/>
    <result property="bgCnName" column="bg_cn_name"/>
    <result property="dimensionCode" column="dimension_code"/>
    <result property="dimensionCnName" column="dimension_cn_name"/>
    <result property="dimensionSubCategoryCode" column="dimension_subcategory_code"/>
    <result property="dimensionSubCategoryCnName" column="dimension_subcategory_cn_name"/>
    <result property="dimensionSubDetailCode" column="dimension_sub_detail_code"/>
    <result property="dimensionSubDetailCnName" column="dimension_sub_detail_cn_name"/>
    <result property="spartCode" column="spart_code"/>
    <result property="spartCnName" column="spart_cn_name"/>
    <result property="regionCode" column="region_code"/>
    <result property="regionCnName" column="region_cn_name"/>
    <result property="repofficeCode" column="repoffice_code"/>
    <result property="repofficeCnName" column="repoffice_cn_name"/>
    <result property="createdBy" column="created_by"/>
    <result property="creationDate" column="creation_date"/>
    <result property="lastUpdatedBy" column="last_updated_by"/>
    <result property="lastUpdatedate" column="last_update_date"/>
    <result property="delFlag" column="del_flag"/>
    <result property="viewFlag" column="view_flag"/>
    <result property="statusFlag" column="status_flag"/>
    <result property="codeType" column="code_type"/>
    <result property="relationType" column="relation_type"/>
    <result property="replaceRelationName" column="replace_relation_name"/>
    <result property="replaceRelationName" column="replace_relation_name"/>
    <result property="customId" column="custom_id"/>
    <result property="groupCode" column="groupCode"/>
    <result property="groupLevel" column="groupLevel"/>
    <result property="groupCnName" column="groupCnName"/>
    <result property="lvCnName" column="lv_cn_name"/>
    <result property="lvCode" column="lv_code"/>
    <result property="mainFlag" column="main_flag"/>
    <result property="tableName" column="table_name"/>
    <result property="pageType" column="page_type"/>
    <result property="costType" column="cost_type"/>
    <result property="granularityType" column="granularity_type"/>
</resultMap>

<select id="baseCusDimStatus" resultMap="resultMap">
    select
    <choose>
        <when test='nextGroupLevel == "REPLACE_NAME"'>
            DISTINCT t1.custom_id,t1.replace_relation_name AS groupCode, t1.replace_relation_name AS groupCnName,'REPLACE_NAME' AS groupLevel,t1.status_flag,t1.code_type
        </when>
        <when test='nextGroupLevel == "SPART"'>
            DISTINCT t1.custom_id,t1.spart_code AS groupCode, t1.spart_cn_name AS groupCnName, 'SPART' AS groupLevel,t1.status_flag,t1.code_type
        </when>
        <when test='nextGroupLevel == "SUB_DETAIL"'>
            DISTINCT t1.custom_id,t1.dimension_sub_detail_code AS groupCode, t1.dimension_sub_detail_cn_name AS groupCnName, 'SUB_DETAIL' AS groupLevel,t1.status_flag,t1.code_type
        </when>
        <when test='nextGroupLevel == "SUBCATEGORY"'>
            DISTINCT t1.custom_id,t1.dimension_subcategory_code AS groupCode, t1.dimension_subcategory_cn_name AS groupCnName, 'SUBCATEGORY' AS groupLevel,t1.status_flag,t1.code_type
        </when>
        <when test='nextGroupLevel == "DIMENSION"'>
            DISTINCT t1.custom_id,t1.dimension_code AS groupCode, t1.dimension_cn_name AS groupCnName, 'DIMENSION' AS groupLevel,t1.status_flag,t1.code_type
        </when>
        <otherwise>
        </otherwise>
    </choose>
    <if test='costType == "PSP"'>
        from fin_dm_opt_foi.dm_fcst_ict_psp_base_cus_dim_t t1 left join fin_dm_opt_foi.dm_fcst_ict_cus_user_info_t t2
        on t1.custom_id = t2.custom_id and t2.cost_type ='PSP'
    </if>
    <if test='costType == "STD"'>
        from fin_dm_opt_foi.dm_fcst_ict_std_base_cus_dim_t t1 left join fin_dm_opt_foi.dm_fcst_ict_cus_user_info_t t2
        on t1.custom_id = t2.custom_id and t2.cost_type ='STD'
    </if>
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
        <if test='pageType!=null and pageType!=""'>
            and t1.page_type = #{pageType,jdbcType=VARCHAR}
        </if>
        <if test='regionCode!=null and regionCode!=""'>
            and t1.region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode!=null and repofficeCode!=""'>
            and t1.repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='bgCode!=null and bgCode!=""'>
            and t1.bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag!=null and overseaFlag!=""'>
            and t1.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag!=null and viewFlag!=""'>
            and t1.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='granularityType!=null and granularityType!=""'>
            and t1.granularity_type = #{granularityType,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag!=null and mainFlag!=""'>
            and t1.main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes!=null and codeAttributes!=""'>
            and t1.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='codeType!=null and codeType!=""'>
            and t1.code_type = #{codeType,jdbcType=VARCHAR}
        </if>
        <if test='codeTypeList != null and codeTypeList.size() > 0'>
            <foreach collection='codeTypeList' item="code" open="and t1.code_type IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='replaceRelationName!=null and replaceRelationName!=""'>
            and t1.replace_relation_name = #{replaceRelationName,jdbcType=VARCHAR}
        </if>
        <if test='replaceRelationType!=null and replaceRelationType!=""'>
            and t1.replace_relation_type = #{replaceRelationType,jdbcType=VARCHAR}
        </if>
        <if test='relationType != null and relationType!=""'>
            and t1.relation_type = #{relationType,jdbcType=VARCHAR}
        </if>
        <if test='lvCode!=null and lvCode!=""'>
            and t1.lv_code = #{lvCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionCode!=null and dimensionCode!=""'>
            and t1.dimension_code = #{dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubCategoryCode!=null and dimensionSubCategoryCode!=""'>
            and t1.dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubDetailCode!=null and dimensionSubDetailCode!=""'>
            and t1.dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
        <if test='spartCode!=null and spartCode!=""'>
            and t1.spart_code = #{spartCode,jdbcType=VARCHAR}
        </if>
        <if test='userId!=null and userId!=""'>
            and t2.user_id = #{userId,jdbcType=VARCHAR}
        </if>
        <if test='roleId!=null and roleId!=""'>
            and t2.role_id = #{roleId,jdbcType=VARCHAR}
        </if>
        <if test='nextGroupLevel!=null and nextGroupLevel!=""'>
            and t1.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
        </if>
        <if test='nextGroupLevel == "SPART"'>
            and t1.spart_code is not null
        </if>
        <if test='nextGroupLevel == "DIMENSION"'>
            and t1.dimension_code is not null
        </if>
        <if test='nextGroupLevel == "SUBCATEGORY"'>
            and t1.dimension_subcategory_code is not null
        </if>
        <if test='nextGroupLevel == "SUB_DETAIL"'>
            and t1.dimension_sub_detail_code is not null
        </if>
    </trim>
</select>

<select id="getBaseCusDimInfoList"  resultMap="resultMap">
    select
    <choose>
        <when test='groupLevel == "REPLACE_NAME"'>
            custom_id,lv_code,lv_cn_name,spart_code, spart_cn_name,page_type,granularity_type,region_code,region_cn_name,repoffice_code,repoffice_cn_name,bg_code,bg_cn_name,oversea_flag,view_flag,status_flag,del_flag,main_flag,code_attributes,code_type,replace_relation_name,replace_relation_type,relation_type,group_level,group_code
        </when>
        <when test='groupLevel == "SPART"'>
            custom_id,lv_code,lv_cn_name,spart_code, spart_cn_name,page_type,granularity_type,region_code,region_cn_name,repoffice_code,repoffice_cn_name,bg_code,bg_cn_name,oversea_flag,view_flag,status_flag,del_flag,main_flag,code_attributes,code_type,group_level,group_code
        </when>
        <when test='groupLevel == "SUB_DETAIL"'>
            custom_id,lv_code,lv_cn_name,dimension_subcategory_code, dimension_subcategory_cn_name,dimension_sub_detail_code, dimension_sub_detail_cn_name,page_type,granularity_type,region_code,region_cn_name,repoffice_code,repoffice_cn_name,bg_code,bg_cn_name,oversea_flag,view_flag,status_flag,del_flag,main_flag,code_attributes,code_type
        </when>
        <when test='groupLevel == "SUBCATEGORY"'>
            custom_id,lv_code,lv_cn_name,dimension_code, dimension_cn_name,dimension_sub_detail_code, dimension_sub_detail_cn_name,page_type,granularity_type,region_code,region_cn_name,repoffice_code,repoffice_cn_name,bg_code,bg_cn_name,oversea_flag,view_flag,status_flag,del_flag,main_flag,code_attributes,code_type,group_level,group_code
        </when>
        <when test='groupLevel == "DIMENSION"'>
            custom_id,lv_code,lv_cn_name,dimension_code, dimension_cn_name,page_type,granularity_type,region_code,region_cn_name,repoffice_code,repoffice_cn_name,bg_code,bg_cn_name,oversea_flag,view_flag,status_flag,del_flag,main_flag,code_attributes,code_type,group_level,group_code
        </when>
        <otherwise>
        </otherwise>
    </choose>
    <if test='costType == "PSP"'>
        from fin_dm_opt_foi.dm_fcst_ict_psp_base_cus_dim_t
    </if>
    <if test='costType == "STD"'>
        from fin_dm_opt_foi.dm_fcst_ict_std_base_cus_dim_t
    </if>
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
        <if test='pageType!=null and pageType!=""'>
            and page_type = #{pageType,jdbcType=VARCHAR}
        </if>
        <if test='regionCode!=null and regionCode!=""'>
            and region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode!=null and repofficeCode!=""'>
            and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='bgCode!=null and bgCode!=""'>
            and bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag!=null and overseaFlag!=""'>
            and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag!=null and viewFlag!=""'>
            and view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='granularityType!=null and granularityType!=""'>
            and granularity_type = #{granularityType,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag!=null and mainFlag!=""'>
            and main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes!=null and codeAttributes!=""'>
            and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='replaceRelationName!=null and replaceRelationName!=""'>
            and replace_relation_name = #{replaceRelationName,jdbcType=VARCHAR}
        </if>
        <if test='replaceRelationType!=null and replaceRelationType!=""'>
            and replace_relation_type = #{replaceRelationType,jdbcType=VARCHAR}
        </if>
        <if test='relationType != null and relationType!=""'>
            and relation_type = #{relationType,jdbcType=VARCHAR}
        </if>
        <if test='codeType!=null and codeType!=""'>
            and code_type = #{codeType,jdbcType=VARCHAR}
        </if>
        <if test='codeTypeList != null and codeTypeList.size() > 0'>
            <foreach collection='codeTypeList' item="code" open="and code_type IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lvCode!=null and lvCode!=""'>
            and lv_code = #{lvCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionCode!=null and dimensionCode!=""'>
            and dimension_code = #{dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubCategoryCode!=null and dimensionSubCategoryCode!=""'>
            and dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubDetailCode!=null and dimensionSubDetailCode!=""'>
            and dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
        <if test='spartCode!=null and spartCode!=""'>
            and spart_code = #{spartCode,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel!=null and groupLevel!=""'>
            and group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='groupCode!=null and groupCode!=""'>
            and group_code = #{groupCode,jdbcType=VARCHAR}
        </if>
    </trim>
</select>

    <select id="getBaseCusDimKey" resultType="java.lang.Long">
        <if test='costType == "PSP"'>
            SELECT nextval('fin_dm_opt_foi.dm_fcst_ict_psp_base_cus_dim_s')
        </if>
        <if test='costType == "STD"'>
            SELECT nextval('fin_dm_opt_foi.dm_fcst_ict_std_base_cus_dim_s')
        </if>
    </select>

<insert id="createDmFcstCusDimDTO" parameterType="com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstBaseCusDimVO">
    <if test='costType == "PSP"'>
        INSERT INTO fin_dm_opt_foi.dm_fcst_ict_psp_base_cus_dim_t
    </if>
    <if test='costType == "STD"'>
        INSERT INTO fin_dm_opt_foi.dm_fcst_ict_std_base_cus_dim_t
    </if>
    (custom_id,
    custom_cn_name,
    lv_code,
    lv_cn_name,
    dimension_code,
    dimension_cn_name,
    dimension_subcategory_code,
    dimension_subcategory_cn_name,
    dimension_sub_detail_code,
    dimension_sub_detail_cn_name,
    spart_code,
    spart_cn_name,
    page_type,
    granularity_type,
    region_code,
    region_cn_name,
    repoffice_code,
    repoffice_cn_name,
    bg_code,
    bg_cn_name,
    oversea_flag,
    view_flag,
    status_flag,
    main_flag,
    code_attributes,
    created_by,
    creation_date,
    last_updated_by,
    last_update_date,
    del_flag,
    replace_relation_name,
    replace_relation_type,
    relation_type,
    code_type,
    parent_level,
    group_level,
    group_code)
    VALUES
    (#{customId,jdbcType=NUMERIC},
    #{customCnName,jdbcType=VARCHAR},
    #{lvCode,jdbcType=VARCHAR},
    #{lvCnName,jdbcType=VARCHAR},
    #{dimensionCode,jdbcType=VARCHAR},
    #{dimensionCnName,jdbcType=VARCHAR},
    #{dimensionSubCategoryCode,jdbcType=VARCHAR},
    #{dimensionSubCategoryCnName,jdbcType=VARCHAR},
    #{dimensionSubDetailCode,jdbcType=VARCHAR},
    #{dimensionSubDetailCnName,jdbcType=VARCHAR},
    #{spartCode,jdbcType=VARCHAR},
    #{spartCnName,jdbcType=VARCHAR},
    #{pageType,jdbcType=VARCHAR},
    #{granularityType,jdbcType=VARCHAR},
    #{regionCode,jdbcType=VARCHAR},
    #{regionCnName,jdbcType=VARCHAR},
    #{repofficeCode,jdbcType=VARCHAR},
    #{repofficeCnName,jdbcType=VARCHAR},
    #{bgCode,jdbcType=VARCHAR},
    #{bgCnName,jdbcType=VARCHAR},
    #{overseaFlag,jdbcType=VARCHAR},
    #{viewFlag,jdbcType=VARCHAR},
    #{statusFlag,jdbcType=VARCHAR},
    #{mainFlag,jdbcType=VARCHAR},
    #{codeAttributes,jdbcType=VARCHAR},
    #{createdBy,jdbcType=NUMERIC},
    NOW(),
    #{lastUpdatedBy,jdbcType=NUMERIC},
    NOW(),
    'N',
    #{replaceRelationName,jdbcType=VARCHAR},
    #{replaceRelationType,jdbcType=VARCHAR},
    #{relationType,jdbcType=VARCHAR},
    #{codeType,jdbcType=VARCHAR},
    #{parentLevel,jdbcType=VARCHAR},
    #{groupLevel,jdbcType=VARCHAR},
    #{groupCode,jdbcType=VARCHAR}
    )
</insert>

    <select id="getNeedTaskList" resultMap="resultMap">
        select custom_id,
        granularity_type,
        page_type,
        view_flag,
        lv_code,
        main_flag,
        'fin_dm_opt_foi.dm_fcst_ict_psp_base_cus_dim_t' as table_name,
        'PSP' as cost_type
        from (select custom_id ,granularity_type, page_type,view_flag,lv_code,main_flag,row_number() over(partition by page_type order by last_update_date asc) as rn
        from fin_dm_opt_foi.dm_fcst_ict_psp_base_cus_dim_t t1
        where not EXISTS(select 1
        from dm_fcst_ict_psp_base_cus_dim_t t2
        where t1.page_type = t2.page_type
        and status_flag in('ING'))
        and del_flag = 'N' and status_flag in ('D','N')
        <![CDATA[
          and ifnull(error_count,0) < 5
	    ]]>
        )
        where rn = 1
    union ALL
     select
        custom_id,
        granularity_type,
        page_type,
        view_flag,
        lv_code,
        main_flag,
        'fin_dm_opt_foi.dm_fcst_ict_std_base_cus_dim_t' as table_name,
        'STD' as cost_type
    from (select custom_id ,granularity_type, page_type,view_flag,lv_code,main_flag,row_number() over(partition by page_type order by last_update_date asc) as rn
          from fin_dm_opt_foi.dm_fcst_ict_std_base_cus_dim_t t1
          where not EXISTS(select 1
                           from dm_fcst_ict_std_base_cus_dim_t t2
                           where t1.page_type = t2.page_type
                             and status_flag in('ING'))
            and del_flag = 'N' and status_flag in ('D','N')
                    <![CDATA[
            and ifnull(error_count,0) < 5
        ]]>
            )
    where rn = 1
    </select>

    <update id="updateTaskStatus">
        update ${tableName}
        set  status_flag = #{statusFlag,jdbcType=VARCHAR}
            ,last_update_date = now()
        <if test='statusFlag == "N"' >
            ,error_count = ifnull(error_count,0) + 1
        </if>
        <if test='statusFlag == "Y"' >
            ,error_count = 0
        </if>
        where custom_id = #{customId,jdbcType=NUMERIC}
    </update>
    <update id="updateStatusFlag">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update ${item.tableName} set status_flag =#{item.statusFlag,jdbcType=VARCHAR} ,last_update_date = now() where custom_id =#{item.customId,jdbcType=NUMERIC}
        </foreach>
    </update>

    <select id="getExceptionTaskList" resultMap="resultMap">
        select custom_id,
               granularity_type,
               page_type,
               view_flag,
               lv_code,
               main_flag,
               'fin_dm_opt_foi.dm_fcst_ict_psp_base_cus_dim_t' as table_name,
               'PSP' as cost_type
              from fin_dm_opt_foi.dm_fcst_ict_psp_base_cus_dim_t t1
        where status_flag='ING'
              <![CDATA[
          and abs(timestampdiff(HOUR,last_update_date,now())) > 1
                ]]>
        union ALL
        select
            custom_id,
            granularity_type,
            page_type ,
            view_flag,
            lv_code,
            main_flag,
            'fin_dm_opt_foi.dm_fcst_ict_std_base_cus_dim_t' as table_name,
            'STD' as cost_type
              from fin_dm_opt_foi.dm_fcst_ict_std_base_cus_dim_t t1
        where status_flag='ING'
              <![CDATA[
          and abs(timestampdiff(HOUR,last_update_date,now())) > 1
                ]]>
    </select>

</mapper>