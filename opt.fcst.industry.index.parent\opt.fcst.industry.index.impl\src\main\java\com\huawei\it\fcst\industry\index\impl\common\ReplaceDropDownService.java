/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.common;

import com.huawei.it.fcst.industry.index.cache.IndustryCacheHandler;
import com.huawei.it.fcst.industry.index.cache.IndustryGlobalParameterUtil;
import com.huawei.it.fcst.industry.index.constant.CommonConstant;
import com.huawei.it.fcst.industry.index.constant.Constant;
import com.huawei.it.fcst.industry.index.dao.IDmFocReplaceDropDownDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocVersionDao;
import com.huawei.it.fcst.industry.index.dao.IReplaceAmpDao;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumU;
import com.huawei.it.fcst.industry.index.enums.IndustryIndexEnum;
import com.huawei.it.fcst.industry.index.enums.ModuleEnum;
import com.huawei.it.fcst.industry.index.service.common.ICommonService;
import com.huawei.it.fcst.industry.index.service.common.IReplaceDropDownService;
import com.huawei.it.fcst.industry.index.vo.common.CommonDropDownVO;
import com.huawei.it.fcst.industry.index.vo.common.DataPermissionsVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.lookup.LookupItemVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Named;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * ReplaceDropDownService Class
 *
 * <AUTHOR> @since 2024/9/2
 */
@Named("replaceDropDownService")
@JalorResource(code = "replaceDropDownService", desc = "替代指数层级下拉框")
public class ReplaceDropDownService implements IReplaceDropDownService {
    @Autowired
    private IDmFocVersionDao dmFocVersionDao;

    @Autowired
    private IReplaceAmpDao replaceAmpDao;

    @Autowired
    private IDmFocReplaceDropDownDao dmFocDropDownDao;

    @Autowired
    private ICommonService commonService;

    @Autowired
    private IndustryCacheHandler industryCacheHandler;


    @Override
    @JalorOperation(code = "getAnnualPeriodYear", desc = "会计期年份下拉框")
    public ResultDataVO getAnnualPeriodYear() {
        Long versionId = dmFocVersionDao.findAnnualVersion("dm_foc").getVersionId();
        List<String> annualPeriodYear = replaceAmpDao.getAnnualPeriodYear(versionId);
        return ResultDataVO.success(annualPeriodYear);
    }

    @Override
    @JalorOperation(code = "dropDownList", desc = "获取不同层级的编码下拉框")
    public ResultDataVO dropDownList(CommonDropDownVO commonViewVO) throws ApplicationException {
        setInitSearchViewVO(commonViewVO);
        // 正常维度下拉框
        List<DmFocViewInfoVO> dmViewInfoVOList = getDmViewInfoVOList(commonViewVO);
        // 按照特定顺序排列lv1
        List<DmFocViewInfoVO> newAnnualAmpVOList = sortLv1ByCode(dmViewInfoVOList);

        List<DmFocViewInfoVO> allAnnualAmpVOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(newAnnualAmpVOList)) {
            allAnnualAmpVOList.addAll(newAnnualAmpVOList);
        } else {
            allAnnualAmpVOList.addAll(dmViewInfoVOList);
        }
        return ResultDataVO.success(allAnnualAmpVOList);
    }

    public void setInitSearchViewVO(CommonDropDownVO commonViewVO) throws ApplicationException {
        // 计算最近的三年
        List<String> threeYears = getThreeYears();
        // 获取当前年份
        if (CollectionUtils.isNotEmpty(threeYears)) {
            commonViewVO.setPeriodYear(threeYears.get(0));
        }
        // 获取最新的version_id
        commonViewVO.setVersionId(dmFocVersionDao.findAnnualVersion("dm_foc").getVersionId());
        // 获取数据权限
        DataPermissionsVO currentRoleDataPermission = commonService.getCurrentRoleDataPermission("ICT");
        commonViewVO.setLv0DimensionSet(currentRoleDataPermission.getLv0DimensionSet());
        commonViewVO.setLv1DimensionSet(currentRoleDataPermission.getLv1DimensionSet());
        commonViewVO.setLv2DimensionSet(currentRoleDataPermission.getLv2DimensionSet());
    }

    /**
     * 获取通用颗粒度年度分析或配置管理 层级下拉框
     *
     * @param commonViewVO 参数
     * @return 结果
     */
    public List<DmFocViewInfoVO> getDmViewInfoVOList(CommonDropDownVO commonViewVO) {
        List<DmFocViewInfoVO> dmFocAnnualAmpVOList = dmFocDropDownDao.replaceViewInfoList(commonViewVO);
        // 重量级团队权限控制
        handlePermissionTeamLevel(dmFocAnnualAmpVOList,commonViewVO);
        return dmFocAnnualAmpVOList;
    }

    /**
     * 视角1时，处理重量级团队lv1的权限
     * 视角2时，处理重量级团队lv2的权限
     * 视角3时，处理重量级团队Lv1和lv2的权限
     * @param dmFocAnnualAmpVOList
     * @param commonViewVO
     */
    public void handlePermissionTeamLevel(List<DmFocViewInfoVO>dmFocAnnualAmpVOList, CommonDropDownVO commonViewVO) {
        // 当有最大权限时，就不用循环校验各子项是否有权限了
        if (commonViewVO.getLv0DimensionSet().size() == 0 && commonViewVO.getLv1DimensionSet().size() == 0 && commonViewVO.getLv2DimensionSet().size() == 0) {
            return;
        }
        List<DmFocViewInfoVO> lv2ProdDimensionList = dmFocDropDownDao.getReplaceLv2ProdRndTeamList();
        // 有视角1权限时处理重量级团队Lv1
        handleViewFlagOne(dmFocAnnualAmpVOList, commonViewVO, lv2ProdDimensionList);
        // 有视角2权限时处理重量级团队Lv1和Lv2
        handleViewFlagTwo(dmFocAnnualAmpVOList, commonViewVO, lv2ProdDimensionList);
        // 有视角3权限时处理重量级团队Lv1和Lv2
        handleViewFlagThree(dmFocAnnualAmpVOList, commonViewVO, lv2ProdDimensionList);
    }

    private void handleViewFlagOne(List<DmFocViewInfoVO> dmFocAnnualAmpVOList, CommonDropDownVO commonViewVO, List<DmFocViewInfoVO> lv2ProdDimensionList) {
        Set<String> lv2DimensionSet = commonViewVO.getLv2DimensionSet();
        Set<String> lv1DimensionSet = commonViewVO.getLv1DimensionSet();
        if (IndustryIndexEnum.VIEW_FLAG_U.VIEW2.getValue().equals(commonViewVO.getViewFlag())) {
            lv1ProdTeamCodePermission(dmFocAnnualAmpVOList, lv2ProdDimensionList, lv2DimensionSet, lv1DimensionSet);
            dmFocAnnualAmpVOList.removeIf(model -> "no".equals(model.getPermissionFlag()));
        }
    }

    private void handleViewFlagTwo(List<DmFocViewInfoVO> dmFocAnnualAmpVOList, CommonDropDownVO commonViewVO, List<DmFocViewInfoVO> lv2ProdDimensionList) {
        if (IndustryIndexEnum.VIEW_FLAG_U.VIEW3.getValue().equals(commonViewVO.getViewFlag())) {
            prodTeamCodePermission(dmFocAnnualAmpVOList, commonViewVO, lv2ProdDimensionList);
        }
    }

    private void handleViewFlagThree(List<DmFocViewInfoVO> dmFocAnnualAmpVOList, CommonDropDownVO commonViewVO, List<DmFocViewInfoVO> lv2ProdDimensionList) {
        if (IndustryIndexEnum.VIEW_FLAG_U.VIEW4.getValue().equals(commonViewVO.getViewFlag())) {
            prodTeamCodePermission(dmFocAnnualAmpVOList, commonViewVO, lv2ProdDimensionList);
        }
    }

    private void prodTeamCodePermission(List<DmFocViewInfoVO> dmFocAnnualAmpVOList, CommonDropDownVO commonViewVO, List<DmFocViewInfoVO> lv2ProdDimensionList) {
        // 补齐LV1，permissionFlag设置上no
        if (GroupLevelEnumU.LV0.getValue().equals(commonViewVO.getGroupLevel())) {
            setLv1ProCodeListForView3(commonViewVO, dmFocAnnualAmpVOList);
        }
        // 1:校验重量级团队lv1层级的code是否有所有重量级团队LV2的权限
        for (DmFocViewInfoVO dmFocViewInfoVO : dmFocAnnualAmpVOList) {
            // 处理重量级团队Lv1权限
            handleLv1ProdTeamCodeWithThree(commonViewVO, lv2ProdDimensionList, dmFocViewInfoVO);
        }
    }

    private boolean handleLv1ProdTeamCodeWithThree(CommonDropDownVO commonViewVO, List<DmFocViewInfoVO> lv3ProdDimensionList, DmFocViewInfoVO dmFocViewInfo) {
        Set<String> lv2DimensionSet = commonViewVO.getLv2DimensionSet();
        Set<String> lv1DimensionSet = commonViewVO.getLv1DimensionSet();
        if (GroupLevelEnumU.LV1.getValue().equals(dmFocViewInfo.getGroupLevel())) {
            Set<String> lv2ProdCodeSet = lv3ProdDimensionList.stream()
                    .filter(item ->  dmFocViewInfo.getGroupCode().equals(item.getLv1ProdRndTeamCode()))
                    .map(DmFocViewInfoVO::getLv2ProdRndTeamCode).collect(Collectors.toSet());
            // lv1DimensionSet不包涵，代表没有LV1的权限
            if (lv1DimensionSet.size()!=0 && !lv1DimensionSet.contains(dmFocViewInfo.getGroupCode())) {
                dmFocViewInfo.setPermissionFlag("no");
                return true;
            }
            // lv2DimensionSet为空集合，代表拥有LV1的权限
            if (lv2DimensionSet.size() == 0) {
                dmFocViewInfo.setPermissionFlag("has");
                return true;
            }
            if (lv2DimensionSet.containsAll(lv2ProdCodeSet)) {
                dmFocViewInfo.setPermissionFlag("has");
                return true;
            } else {
                dmFocViewInfo.setPermissionFlag("no");
            }
        }
        return false;
    }

    private  void setLv1ProCodeListForView3(CommonDropDownVO commonViewVO, List<DmFocViewInfoVO> dmFocAnnualAmpVOList) {
        CommonDropDownVO lv1ViewVO = new CommonDropDownVO();
        lv1ViewVO.setViewFlag(commonViewVO.getViewFlag());
        lv1ViewVO.setLv0ProdListCode(commonViewVO.getLv0ProdListCode());
        lv1ViewVO.setOverseaFlag(commonViewVO.getOverseaFlag());
        lv1ViewVO.setCaliberFlag(commonViewVO.getCaliberFlag());
        lv1ViewVO.setLv2DimensionSet(commonViewVO.getLv2DimensionSet());
        List<DmFocViewInfoVO> Lv1ProdVOList = dmFocDropDownDao.totalReverseFindLv1ProdCode(lv1ViewVO);
        setLv1AnnualAmpVOList(dmFocAnnualAmpVOList, Lv1ProdVOList,GroupLevelEnumU.LV1.getValue());
    }


    private void setLv1AnnualAmpVOList(List<DmFocViewInfoVO> dmFocAnnualAmpVOList, List<DmFocViewInfoVO> lv2ProdVOList,String groupLevel) {
        List<String> lv1ProdCodeList = dmFocAnnualAmpVOList.stream().map(lv1ProdCode -> lv1ProdCode.getGroupCode()).collect(Collectors.toList());
        // 无权限的LV1code集合
        HashSet<DmFocViewInfoVO> lv1ProdVOSet = new HashSet<>();
        lv2ProdVOList.stream().forEach(lv2ProdEle ->{
            if (!lv1ProdCodeList.contains(lv2ProdEle.getLv1ProdRndTeamCode())) {
                DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
                dmFocViewInfoVO.setGroupCode(lv2ProdEle.getLv1ProdRndTeamCode());
                dmFocViewInfoVO.setGroupCnName(lv2ProdEle.getLv1ProdRdTeamCnName());
                dmFocViewInfoVO.setGroupLevel(groupLevel);
                dmFocViewInfoVO.setLv1ProdRndTeamCode(lv2ProdEle.getLv1ProdRndTeamCode());
                dmFocViewInfoVO.setPermissionFlag("no");
                lv1ProdVOSet.add(dmFocViewInfoVO);
            }
        });
        // 无权限的lv1code添加到集合里
        dmFocAnnualAmpVOList.addAll(lv1ProdVOSet);
    }

    private void lv1ProdTeamCodePermission(List<DmFocViewInfoVO> dmFocAnnualAmpVOList, List<DmFocViewInfoVO> lv2ProdDimensionList, Set<String> lv2DimensionSet, Set<String> lv1DimensionSet) {
        // 1:校验重量级团队lv1层级的code是否有所有重量级团队LV2编码的权限
        for (DmFocViewInfoVO dmViewInfoVO : dmFocAnnualAmpVOList) {
            if (GroupLevelEnumU.LV1.getValue().equals(dmViewInfoVO.getGroupLevel())) {
                Set<String> lv2ProdCodeSet = lv2ProdDimensionList.stream()
                        .filter(item -> dmViewInfoVO.getGroupCode().equals(item.getLv1ProdRndTeamCode()))
                        .map(DmFocViewInfoVO::getLv2ProdRndTeamCode).collect(Collectors.toSet());
                // lv1DimensionSet不为空集合，并且不包含在里面代表没有LV1的权限
                if (lv1DimensionSet.size() != 0 && !lv1DimensionSet.contains(dmViewInfoVO.getGroupCode())) {
                    dmViewInfoVO.setPermissionFlag("no");
                    continue;
                }
                // lv2DimensionSet 为空集合，代表拥有LV1的权限
                if (lv2DimensionSet.size() == 0) {
                    dmViewInfoVO.setPermissionFlag("has");
                    continue;
                }
                if (lv2DimensionSet.containsAll(lv2ProdCodeSet)) {
                    dmViewInfoVO.setPermissionFlag("has");
                } else {
                    dmViewInfoVO.setPermissionFlag("no");
                }
            }
        }
    }

    public List<DmFocViewInfoVO> sortLv1ByCode(List<DmFocViewInfoVO> dmAnnualAmpVOList) {
        List<DmFocViewInfoVO> newAnnualAmpVOList = new ArrayList<>();
        // 重量级团队Lv1排序
        if (CollectionUtils.isNotEmpty(dmAnnualAmpVOList)) {
            if (GroupLevelEnumU.LV1.getValue().equals(dmAnnualAmpVOList.get(0).getGroupLevel())) {
                for (String code : CommonConstant.LV1_CODE) {
                    for (DmFocViewInfoVO dmFocViewInfoVO : dmAnnualAmpVOList) {
                        if (code.equals(dmFocViewInfoVO.getGroupCode())) {
                            newAnnualAmpVOList.add(dmFocViewInfoVO);
                        }
                    }
                }
                List<DmFocViewInfoVO> otherAmpVOList = dmAnnualAmpVOList.stream().filter(amp -> !newAnnualAmpVOList.contains(amp)).collect(Collectors.toList());
                newAnnualAmpVOList.addAll(otherAmpVOList);
            }
        }
        return  newAnnualAmpVOList;
    }

    /**
     * 最近三年
     *
     * @return 最近三年list
     */
    public List<String> getThreeYears() {
        ResultDataVO periodYearList = getAnnualPeriodYear();
        return (List<String>) periodYearList.getData();
    }

    @Override
    @JalorOperation(code = "viewFlagInfoList", desc = "视角下拉框")
    public ResultDataVO viewFlagInfoList(CommonDropDownVO commonViewVO) throws ApplicationException {
        List<DmFocViewInfoVO> allProdDimensionList = dmFocDropDownDao.getReplaceLv2ProdRndTeamList();
        getCommonDropDownVO(allProdDimensionList,commonViewVO);
        // 获取最新的version_id
        commonViewVO.setVersionId(dmFocVersionDao.findAnnualVersion("dm_foc").getVersionId());
        List<DmFocViewInfoVO> viewFlagList = dmFocDropDownDao.viewFlagInfoList(commonViewVO);
        // 判断是否有视角0的权限
        if (CollectionUtils.isEmpty(commonViewVO.getLv0DimensionSet()) && CollectionUtils.isEmpty(commonViewVO.getLv1DimensionSet())
                && CollectionUtils.isEmpty(commonViewVO.getLv2DimensionSet())) {
            viewFlagValueWihtLookup(viewFlagList);
            return ResultDataVO.success(viewFlagList);
        } else {
            viewFlagList.removeIf(model -> model.getViewFlag().equals("0"));
        }
        setViewFlagListWithOne(commonViewVO, allProdDimensionList, viewFlagList);
        viewFlagValueWihtLookup(viewFlagList);
        return ResultDataVO.success(viewFlagList);
    }

    /**
     * 设置权限返回值
     *
     * @param allProdDimensionList
     * @return CommonViewVO
     */
    public void getCommonDropDownVO(List<DmFocViewInfoVO> allProdDimensionList, CommonDropDownVO commonViewVO) throws ApplicationException {
        DataPermissionsVO currentRoleDataPermission = commonService.getCurrentRoleDataPermission("ICT");
        Set<String> lv0DimensionSet = currentRoleDataPermission.getLv0DimensionSet();
        Set<String> lv1DimensionSet = currentRoleDataPermission.getLv1DimensionSet();
        Set<String> lv2DimensionSet = currentRoleDataPermission.getLv2DimensionSet();

        List<String> lv0ProdDimensionList = allProdDimensionList.stream().map(DmFocViewInfoVO::getLv0ProdRndTeamCode).distinct().collect(Collectors.toList());
        List<String> lv1ProdDimensionList = allProdDimensionList.stream().map(DmFocViewInfoVO::getLv1ProdRndTeamCode).distinct().collect(Collectors.toList());
        List<String> lv2ProdDimensionList = allProdDimensionList.stream().map(DmFocViewInfoVO::getLv2ProdRndTeamCode).distinct().collect(Collectors.toList());
        if (lv0DimensionSet.containsAll(lv0ProdDimensionList) && lv1DimensionSet.containsAll(lv1ProdDimensionList)
                && lv2DimensionSet.containsAll(lv2ProdDimensionList)) {
            lv0DimensionSet.clear();
            lv1DimensionSet.clear();
            lv2DimensionSet.clear();
        }
        commonViewVO.setLv0DimensionSet(lv0DimensionSet);
        commonViewVO.setLv1DimensionSet(lv1DimensionSet);
        commonViewVO.setLv2DimensionSet(lv2DimensionSet);
    }

    private void viewFlagValueWihtLookup(List<DmFocViewInfoVO> viewFlagList) throws ApplicationException {
        industryCacheHandler.initLookupValue();
        // 查询Lookup中配置的通用视角
        setUniversalLookUp(viewFlagList, Constant.StrEnum.ALL_LOOKUP_UNIVERSAL_VIEW);
    }


    private void setUniversalLookUp(List<DmFocViewInfoVO> viewFlagList, Constant.StrEnum lookupUniversalView) throws ApplicationException {
        if (CollectionUtils.isNotEmpty(viewFlagList)) {
            List<LookupItemVO> lookUpItemList = IndustryGlobalParameterUtil.findViewListByItemCode(lookupUniversalView.getValue());
            for (DmFocViewInfoVO dmFocViewInfoVO : viewFlagList) {
                for (LookupItemVO lookupItemVO : lookUpItemList) {
                    if (dmFocViewInfoVO.getViewFlag().equals(lookupItemVO.getItemCode())) {
                        dmFocViewInfoVO.setViewFlagValue(lookupItemVO.getItemName().replace("$", ModuleEnum.ICT.getCnName()));
                        dmFocViewInfoVO.setViewFlagOrder(lookupItemVO.getItemIndex());
                    }
                }
            }}
    }

    private void setViewFlagListWithOne(CommonDropDownVO commonViewVO, List<DmFocViewInfoVO> allProdDimensionList, List<DmFocViewInfoVO> viewFlagList) {
        // 判断是否有视角1的权限
        Boolean hasLv1Flag = false;
        if (CollectionUtils.isNotEmpty(commonViewVO.getLv1DimensionSet())) {
            hasLv1Flag = setLv1Flag(commonViewVO, allProdDimensionList, hasLv1Flag);
            if (!hasLv1Flag) {
                viewFlagList.removeIf(model -> model.getViewFlag().equals("1"));
            }
        }
    }
    private Boolean setLv1Flag(CommonDropDownVO commonViewVO, List<DmFocViewInfoVO> allProdDimensionList, Boolean hasLv1PermissionFlag) {
        for (String lv1code : commonViewVO.getLv1DimensionSet()) {
            Set<String> lv2ProdCodeSet = allProdDimensionList.stream()
                    .filter(item -> lv1code.equals(item.getLv1ProdRndTeamCode()))
                    .map(DmFocViewInfoVO::getLv2ProdRndTeamCode).collect(Collectors.toSet());
            if (commonViewVO.getLv1DimensionSet().contains("NO_PERMISSION")) {
                hasLv1PermissionFlag = false;
            } else {
                // lv2DimensionSet为空集合，代表拥有所有LV1权限
                if (commonViewVO.getLv2DimensionSet().size() == 0) {
                    hasLv1PermissionFlag = true;
                    break;
                }
                if (lv2ProdCodeSet.size() !=0) {
                    if (commonViewVO.getLv2DimensionSet().containsAll(lv2ProdCodeSet)) {
                        hasLv1PermissionFlag = true;
                        break;
                    } else {
                        hasLv1PermissionFlag = false;
                    }
                }
            }
        }
        return hasLv1PermissionFlag;
    }

}
