/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.config.DmFocTopCateInfoDTO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.cursor.Cursor;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/3/15
 */
public interface IDmFocTopCateInfoDao {
    /**
     * 全品类清单分页查询
     *
     * @param vo 参数
     * @param paramPageVO 参数
     * @return PagedResult<DmFocTopCateInfoDTO>
     */
    PagedResult<DmFocTopCateInfoDTO> findCateByPage(@Param("dmFocTopCateInfoDTO")DmFocTopCateInfoDTO vo,@Param("pageVO")PageVO paramPageVO);

    Cursor<Map> findTopCateVOList(DmFocTopCateInfoDTO build);

    int findAllCount(DmFocTopCateInfoDTO build);

    int findManufactureAllCount(DmFocTopCateInfoDTO build);

    PagedResult<DmFocTopCateInfoDTO> findManufactureByPage(@Param("dmFocTopCateInfoDTO")DmFocTopCateInfoDTO build,@Param("pageVO")PageVO pageVO);

    Cursor<Map> findManufactureList(DmFocTopCateInfoDTO build);
}
