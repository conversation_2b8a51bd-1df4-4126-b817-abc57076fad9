/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2019. All rights reserved.
 *
 */

package com.huawei.it.fcst.industry.index;

import com.huawei.it.fcst.industry.index.impl.mqs.MessageConsumer;
import com.huawei.it.fcst.industry.index.impl.mqs.MessageProducer;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.ioc.Jalor;
import com.huawei.it.jalor5.core.server.IAfterStartupHandler;
import com.huawei.it.jalor5.core.server.StartupEventArgs;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 20230703
 */
@Component
@Slf4j
public class ApplicationStartEvent implements IAfterStartupHandler {

    private MessageConsumer consumer;
    private MessageProducer producer;

    @Override
    public int getOrder() {
        return 100 * 20;
    }

    @Override
    public void execute(Object sender, StartupEventArgs args) throws ApplicationException {
        log.info(" application auto start ump . ");
        try {
            if (producer == null) {
                producer = Jalor.getContext().getBean("messageProducer",MessageProducer.class);
            }
            producer.startProducer();
        } catch (Exception exception) {
            log.error("start ump producer error:{}",exception);
        }
        try {
            if (consumer == null) {
                consumer = Jalor.getContext().getBean("messageConsumer",MessageConsumer.class);
            }
            consumer.run();
        } catch (Exception exception) {
            log.error("start ump consumer error :{}",exception.getMessage());
        }
    }

}
