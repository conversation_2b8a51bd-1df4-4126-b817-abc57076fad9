/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.provider;

import com.huawei.it.fcst.industry.pbi.dao.IDmFcstVersionInfoDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmfcstIctCodeReplInfoDao;
import com.huawei.it.fcst.export.IExcelExportDataProvider;
import com.huawei.it.fcst.export.vo.ExcelExportContext;
import com.huawei.it.fcst.industry.pbi.vo.replace.ReplaceSearchVO;
import com.huawei.it.fcst.industry.pbi.vo.version.DmFcstVersionInfoDTO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Named;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ReplaceExportDataProvider Class
 *
 * <AUTHOR>
 * @since 2024/7/11
 */
@Named("IExcelExport.ReplaceExportDataProvider")
public class ReplaceExportDataProvider implements IExcelExportDataProvider {

    @Autowired
    private IDmfcstIctCodeReplInfoDao dmfcstIctCodeReplInfoDao;

    @Autowired
    private IDmFcstVersionInfoDao dmFcstVersionInfoDao;

    @Override
    public List<?> getData(Serializable conditionObject, PageVO pageVO) {
        ReplaceSearchVO replaceSearchVO = (ReplaceSearchVO) conditionObject;
        return dmfcstIctCodeReplInfoDao.findReplInfoList(replaceSearchVO);
    }

    @Override
    public Map<String, Object> getHeader(Serializable conditionObject, Map<String, Object> parameters) throws ApplicationException {
        ExcelExportContext context = (ExcelExportContext) conditionObject;
        ReplaceSearchVO replaceSearchVO = (ReplaceSearchVO) context.getConditionObject();
        DmFcstVersionInfoDTO versionDto = new DmFcstVersionInfoDTO();
        versionDto.setVersionId(replaceSearchVO.getVersionId());
        List<DmFcstVersionInfoDTO> planVersionList = dmFcstVersionInfoDao.findPlanVersionList(versionDto);
        String version = null;

        if (CollectionUtils.isNotEmpty(planVersionList)) {
            version = planVersionList.get(0).getVersion();
        }
        // 自定义表头
        Map<String, Object> headMap = new HashMap<>();
        headMap.put("version", version);
        return headMap;
    }
}
