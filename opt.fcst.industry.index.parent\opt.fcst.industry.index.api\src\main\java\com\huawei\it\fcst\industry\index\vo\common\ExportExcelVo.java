/*
 *
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.industry.index.vo.common;

import com.huawei.it.fcst.industry.index.vo.config.HistoryInputVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * ExportExcelVo Class
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExportExcelVo {
    private List<AbstractExcelTitleVO> titleVoList;
    private List<AbstractExcelTitleVO> formInfoVo;
    private List<Map> list;

    private List<AbstractExcelTitleVO> selectedLeafExcelTitleVO;

    private int titleRowCount;

    private String  sheetName;

    private Boolean mergeCell;

    private String fileName;

    private Long userId;

    private List<AbstractExcelTitleVO> threeTitleList;

    private List<AbstractExcelTitleVO> fourTitleList;

    private List<AbstractExcelTitleVO> fiveTitleList;

    private List<AbstractExcelTitleVO> sixTitleList;

    private List<AbstractExcelTitleVO> sevenTitleList;

    private HistoryInputVO historyInputVO;

    private List<String> yearPeriodList;
}
