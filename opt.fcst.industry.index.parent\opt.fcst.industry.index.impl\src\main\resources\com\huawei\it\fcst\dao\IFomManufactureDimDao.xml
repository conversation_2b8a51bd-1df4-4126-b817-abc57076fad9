<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IFomManufactureDimDao">
    <resultMap type="com.huawei.it.fcst.industry.index.vo.config.FomManufactureDimVO" id="resultMap">
        <result property="versionId" column="version_id"/>
        <result property="itemCode" column="item_code"/>
        <result property="cnDesc" column="cn_desc"/>
        <result property="lv0OrgCn" column="lv0_org_cn"/>
        <result property="bussinessObject" column="bussiness_object"/>
        <result property="shippingObject" column="shipping_object"/>
        <result property="manufactureObject" column="manufacture_object"/>
        <result property="manufactureBu" column="manufacture_bu"/>
        <result property="topShippingObjectCode" column="top_shipping_object_code"/>
        <result property="topManufactureObjectCode" column="top_manufacture_object_code"/>
        <result property="topShippingObjectCnName" column="top_shipping_object_cn_name"/>
        <result property="topManufactureObjectCnName" column="top_manufacture_object_cn_name"/>
    </resultMap>

    <sql id="allFields">
        version_id,
        apd_manufacture_prod_lv0 as lv0_org_cn,
        apd_manufacture_prod_lv1 as manufacture_bu,
        apd_operate_object as bussiness_object,
        apd_shipment_object as shipping_object,
        apd_manufacture_object as manufacture_object,
        item_code
    </sql>

    <sql id="uniqueKeyField">
        version_id = #{versionId,jdbcType=NUMERIC}
    </sql>

    <sql id="searchFields">
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='_parameter.get("fomManufactureDimVO").versionId != null'>
                AND version_id=#{fomManufactureDimVO.versionId,jdbcType=NUMERIC}
            </if>
            <if test='_parameter.get("fomManufactureDimVO").industryOrg != null and _parameter.get("fomManufactureDimVO").industryOrg =="ENERGY"'>
                AND apd_manufacture_prod_lv0 in ('数字能源', '数字能源TMT')
            </if>
            <if test='_parameter.get("fomManufactureDimVO").industryOrg != null and _parameter.get("fomManufactureDimVO").industryOrg =="IAS"'>
                AND apd_manufacture_prod_lv0 ='IAS' AND apd_manufacture_prod_lv1 = 'IAS'
            </if>
            <if test='_parameter.get("fomManufactureDimVO").bussinessObject != null and _parameter.get("fomManufactureDimVO").bussinessObject!=""'>
                AND apd_operate_object = #{fomManufactureDimVO.bussinessObject,jdbcType=VARCHAR}
            </if>
            <if test='_parameter.get("fomManufactureDimVO").bussinessObject == ""'>
                AND apd_operate_object is NULL
            </if>
            <if test='_parameter.get("fomManufactureDimVO").shippingObject == "" '>
                AND apd_shipment_object is NULL
            </if>
            <if test='_parameter.get("fomManufactureDimVO").shippingObject != null and _parameter.get("fomManufactureDimVO").shippingObject!=""'>
                AND apd_shipment_object = #{fomManufactureDimVO.shippingObject,jdbcType=VARCHAR}
            </if>
            <if test='_parameter.get("fomManufactureDimVO").manufactureObject != null and _parameter.get("fomManufactureDimVO").manufactureObject!=""'>
                AND apd_manufacture_object = #{fomManufactureDimVO.manufactureObject,jdbcType=VARCHAR}
            </if>
            <if test='_parameter.get("fomManufactureDimVO").manufactureObject == ""'>
                AND apd_manufacture_object is NULL
            </if>
        </trim>
    </sql>

    <select id="findManufactureByPage" resultMap="resultMap">
        SELECT
        <include refid="allFields"/>
        FROM fin_dm_opt_foi.dm_foc_inv_item_manufacture_t
        <include refid="searchFields"/>
        LIMIT #{pageVO.pageSize} OFFSET  #{pageVO.startIndex}-1
    </select>

    <select id="findManufactureByPageCount" resultType="int">
        SELECT COUNT(1) from
        (
        SELECT
        <include refid="allFields"/>
        FROM fin_dm_opt_foi.dm_foc_inv_item_manufacture_t
        <include refid="searchFields"/>
        )apd_foc_manufacture_dim_t
    </select>

    <select id="findDropDownList" resultMap="resultMap">
        SELECT DISTINCT
        <if test='versionId != null and versionId!=""'>
            apd_operate_object as  bussiness_object,
        </if>
        <if test='bussinessObject != null and bussinessObject !=""'>
            apd_shipment_object as shipping_object,
        </if>
        <if test='shippingObject != null and shippingObject !=""'>
            apd_manufacture_object as manufacture_object,
        </if>
        version_id
        FROM
        fin_dm_opt_foi.dm_foc_inv_item_manufacture_t
        WHERE
        <if test='versionId != null and versionId!=""'>
            version_id=#{versionId,jdbcType=NUMERIC}
        </if>
        <if test='industryOrg != null and industryOrg =="ENERGY"'>
            AND apd_manufacture_prod_lv0  in ('数字能源', '数字能源TMT')
        </if>
        <if test='industryOrg != null and industryOrg =="IAS"'>
            AND apd_manufacture_prod_lv0  = 'IAS' AND apd_manufacture_prod_lv1  = 'IAS'
        </if>
        <if test='bussinessObject != null and bussinessObject !=""'>
            AND apd_operate_object=#{bussinessObject,jdbcType=VARCHAR}
        </if>
        <if test='shippingObject != null and shippingObject !=""'>
            AND apd_shipment_object=#{shippingObject,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="findItemDropDownList" resultMap="resultMap">
        SELECT DISTINCT
        <if test='versionId != null and versionId!=""'>
            top_shipping_object_code,top_shipping_object_cn_name,
        </if>
        version_id
        FROM
        <if test='granularityType == "U"' >
            fin_dm_opt_foi.${tablePreFix}_made_top_item_info_t
        </if>
        <if test='granularityType == "P"' >
            fin_dm_opt_foi.${tablePreFix}_made_pft_top_item_info_t
        </if>
        <if test='granularityType == "D"'>
            fin_dm_opt_foi.${tablePreFix}_made_dms_top_item_info_t
        </if>
        WHERE
        <if test='versionId != null and versionId!=""'>
            version_id=#{versionId,jdbcType=NUMERIC}
        </if>
        <if test='topShippingObjectCode != null and topShippingObjectCode !=""'>
            AND top_shipping_object_code=#{topShippingObjectCode,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="findMfcDropDownList" resultMap="resultMap">
        SELECT DISTINCT
        <if test='versionId != null and versionId!=""'>
            top_shipping_object_code,top_shipping_object_cn_name,
        </if>
        <if test='topShippingObjectCode != null and topShippingObjectCode !=""'>
            top_manufacture_object_code,top_manufacture_object_cn_name,
        </if>
        version_id
        FROM
        <if test='granularityType == "U"'>
            fin_dm_opt_foi.${tablePreFix}_top_made_info_t
        </if>
        <if test='granularityType == "P"'>
            fin_dm_opt_foi.${tablePreFix}_pft_top_made_info_t
        </if>
        <if test='granularityType == "D"'>
            fin_dm_opt_foi.${tablePreFix}_dms_top_made_info_t
        </if>
        WHERE
        <if test='versionId != null and versionId!=""'>
            version_id=#{versionId,jdbcType=NUMERIC}
        </if>
    </select>

    <select id="syncDimMadeFunction" resultType="java.lang.String">
        SELECT FIN_DM_OPT_FOI.f_dm_fom_sync_dim_made(#{industryOrg})
    </select>
</mapper>
