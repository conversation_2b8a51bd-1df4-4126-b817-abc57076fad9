/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.relation;

import static com.huawei.it.fcst.industry.index.constant.CommonConstant.PATTERN;

import com.huawei.it.fcst.industry.index.constant.CommonConstant;
import com.huawei.it.fcst.industry.index.dao.IDmDimCatgModlCegIctDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocVersionDao;
import com.huawei.it.fcst.industry.index.enums.IndustryConst;
import com.huawei.it.fcst.industry.index.enums.ModuleEnum;
import com.huawei.it.fcst.industry.index.enums.ResultCodeEnum;
import com.huawei.it.fcst.industry.index.impl.annual.StatisticsExcelService;
import com.huawei.it.fcst.industry.index.service.relation.IConfigDimensionManageService;
import com.huawei.it.fcst.industry.index.utils.ExcelUtil;
import com.huawei.it.fcst.industry.index.utils.UserInfoUtils;
import com.huawei.it.fcst.industry.index.vo.common.AbstractExcelTitleVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFoiImpExpRecordVO;
import com.huawei.it.fcst.industry.index.vo.common.ExcelVO;
import com.huawei.it.fcst.industry.index.vo.common.ExportExcelVo;
import com.huawei.it.fcst.industry.index.vo.common.HeaderVo;
import com.huawei.it.fcst.industry.index.vo.common.LeafExcelTitleVO;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.common.TableNameVO;
import com.huawei.it.fcst.industry.index.vo.common.UploadInfoVO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoDTO;
import com.huawei.it.fcst.industry.index.vo.relation.BackDimensionVO;
import com.huawei.it.fcst.industry.index.vo.relation.DimensionInputVO;
import com.huawei.it.fcst.industry.index.vo.relation.DmDimCatgModlCegIctVO;
import com.huawei.it.fcst.industry.index.vo.relation.DmDimMaterialCodeVO;
import com.huawei.it.fcst.industry.index.vo.relation.RelationVO;
import com.huawei.it.fcst.util.ExcelUtils;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.core.util.StreamUtil;
import com.huawei.it.jalor5.registry.service.IRegistryQueryService;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.poi.ss.usermodel.CellType;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.inject.Named;
import javax.servlet.http.HttpServletResponse;

/**
 * ConfigDimensionManageService Class
 *
 * <AUTHOR>
 * @since 2023/3/15
 */
@Named("configDimensionManageService")
@JalorResource(code = "configDimensionManageService", desc = "配置管理映射关系维表")
public class ConfigDimensionManageService implements IConfigDimensionManageService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ConfigDimensionManageService.class);

    @Autowired
    private IDmDimCatgModlCegIctDao iDmDimCatgModlCegIctDao;

    @Autowired
    private IDmFocVersionDao dmFocVersionDao;

    @Autowired
    private IRegistryQueryService registryQueryService;

    @Autowired
    private StatisticsExcelService statisticsExcelService;

    @Autowired
    private ExcelUtil excelUtil;

    @Override
    @JalorOperation(code = "relationSave", desc = "维度关系新增编辑保存功能")
    @Audit(module = "configDimensionManageService-relationSave", operation = "relationSave",
        message = "维度关系新增编辑保存功能")
    public ResultDataVO relationSave(DimensionInputVO dimensionInputVO) throws CommonApplicationException {
        if (null == dimensionInputVO.getVersionId()) {
            throw new CommonApplicationException("入参维度版本为空");
        }
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList = dimensionInputVO.getDimCatgModlCegIctList();
        // 名称编码去空格
        trimParamSpace(dimCatgModlCegIctList);
        // 查询当前版本对应维度数据
        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion = iDmDimCatgModlCegIctDao.findDmDimCatgModlCegIctList(
            DmDimCatgModlCegIctVO.builder().versionId(dimensionInputVO.getVersionId()).tablePreFix(TableNameVO.getTablePreFix(dimensionInputVO.getIndustryOrg())).build());
        // 校验入参数据
        checkDimensionData(dimCatgModlCegIctList,dmDimCatgModlListWithVersion);
        // 校验当前入参与当前版本数据
        List<DmDimCatgModlCegIctVO> errorList = new ArrayList<>();
        // 校验维度数据
        int errorCount = validDimensionData(dimensionInputVO.getTablePreFix(),dimCatgModlCegIctList, errorList,dmDimCatgModlListWithVersion);
        BackDimensionVO backDimensionVO = new BackDimensionVO();
        if (0 == errorCount) {
            saveRelationDimensionData(dimensionInputVO, dmDimCatgModlListWithVersion, backDimensionVO);
        } else {
            backDimensionVO.setErrorList(errorList);
            return ResultDataVO.failure(ResultCodeEnum.VERSION_PARAM_ERROR, backDimensionVO);
        }
        return ResultDataVO.success(backDimensionVO);
    }

    private void checkDimensionData(List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList, List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion)
        throws CommonApplicationException {
        // 入参重复数据检验
        StringBuilder strbuilder = new StringBuilder();
        validDupliceData(dimCatgModlCegIctList,strbuilder);
        if (ObjectUtils.isNotEmpty(strbuilder)) {
            throw new CommonApplicationException("检测到" + strbuilder);
        }
        // 入参重复品类校验
        StringBuilder cateBuilder = new StringBuilder();
        validDupliceCate(dimCatgModlCegIctList,cateBuilder);
        if (ObjectUtils.isNotEmpty(cateBuilder)) {
            throw new CommonApplicationException("检测到" + cateBuilder);
        }
        // 校验入参里同一个专家团/模块名称是否存在多个简称
        StringBuilder stringBuilder = new StringBuilder();
        checkMoreShortCegName(dimCatgModlCegIctList, stringBuilder);
        if (ObjectUtils.isNotEmpty(stringBuilder)) {
            throw new CommonApplicationException("检测到" + stringBuilder + "存在多个简称，系统仅允许唯一简称，请修改后保存！");
        }
        // 校验入参里同一个简称是否包含多个专家团/模块
        StringBuilder oneShortBuilder = new StringBuilder();
        checkShortCegName(dimCatgModlCegIctList, oneShortBuilder);
        if (ObjectUtils.isNotEmpty(oneShortBuilder)) {
            throw new CommonApplicationException("检测到" + oneShortBuilder + "同一简称被多处引用，请修改后保存！");
        }
        // 校验入参里同一个简称是否被不同的专家团/模块引用
        StringBuilder otherShortBuilder = new StringBuilder();
        checkOtherShortCegName(dimCatgModlCegIctList, otherShortBuilder);
        if (ObjectUtils.isNotEmpty(otherShortBuilder)) {
            throw new CommonApplicationException("检测到" + otherShortBuilder + "同一简称被多处引用，请修改后保存！");
        }
        // 校验入参简称和系统其他专家团简称是否重复
        StringBuilder stringBuilderDuplice = new StringBuilder();
        checkDupliceShortCnName(dimCatgModlCegIctList, stringBuilderDuplice,dmDimCatgModlListWithVersion);
        if (ObjectUtils.isNotEmpty(stringBuilderDuplice)) {
            throw new CommonApplicationException("检测到" + stringBuilderDuplice + "和系统已有简称重复，系统仅允许唯一简称，请修改后保存！");
        }
        // 校验入参品类和系统已有品类是否重复
        StringBuilder stringBuilderCate = new StringBuilder();
        checkDupliceCate(dimCatgModlCegIctList, stringBuilderCate,dmDimCatgModlListWithVersion);
        if (ObjectUtils.isNotEmpty(stringBuilderCate)) {
            throw new CommonApplicationException("检测到品类编码" + stringBuilderCate + "在系统中已存在，请修改后保存！");
        }
    }

    private void checkOtherShortCegName(List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList, StringBuilder otherShortBuilder) {
        List<String> l3ShortNameParam = dimCatgModlCegIctList.stream()
            .map(cegIctVO -> cegIctVO.getL3CegShortCnName())
            .collect(Collectors.toList());
        List<String> resultList = new ArrayList<>();
        for (DmDimCatgModlCegIctVO modlCegIctVO : dimCatgModlCegIctList) {
            if (l3ShortNameParam.contains(modlCegIctVO.getL4CegShortCnName())) {
                resultList.add(modlCegIctVO.getL4CegShortCnName());
            }
        }
        if (CollectionUtils.isNotEmpty(resultList)) {
            List<String> otherList = resultList.stream().distinct().collect(Collectors.toList());
            for (String errorParam : otherList) {
                otherShortBuilder.append(errorParam + " ");
            }
        }
    }

    private void checkDupliceCate(List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList, StringBuilder stringBuilderCate, List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion) {
        // 新增操作时，校验重复品类
        for (DmDimCatgModlCegIctVO catgModlCegIctVO : dmDimCatgModlListWithVersion) {
            for (DmDimCatgModlCegIctVO modlCegIctVO : dimCatgModlCegIctList) {
                if (catgModlCegIctVO.getCategoryCode().equals(modlCegIctVO.getCategoryCode()) && StringUtils.isEmpty(modlCegIctVO.getOldCategoryCode())) {
                    stringBuilderCate.append(modlCegIctVO.getCategoryCode() + " ");
                }
            }
        }
        // 编辑操作时，校验重复品类
        for (DmDimCatgModlCegIctVO catgModlCegIctVO : dmDimCatgModlListWithVersion) {
            for (DmDimCatgModlCegIctVO modlCegIctVO : dimCatgModlCegIctList) {
                if (StringUtils.isNotEmpty(modlCegIctVO.getOldCategoryCode())) {
                    if (catgModlCegIctVO.getCategoryCode().equals(modlCegIctVO.getCategoryCode()) && !modlCegIctVO.getOldCategoryCode().equals(modlCegIctVO.getCategoryCode())) {
                        stringBuilderCate.append(modlCegIctVO.getCategoryCode() + " ");
                    }
                }
            }
        }
    }

    private void validDupliceCate(List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList, StringBuilder cateBuilder) {
        Map<String, List<DmDimCatgModlCegIctVO>> repeatModlCegList = dimCatgModlCegIctList.stream()
            .collect(Collectors.groupingBy(
                x -> x.getCategoryCode() + "#" + x.getCategoryCnName()));
        List<String> count = repeatModlCegList.keySet()
            .stream()
            .filter(key -> repeatModlCegList.get(key).size() > 1)
            .distinct()
            .collect(Collectors.toList());
        if (count.size() > 0) {
            cateBuilder.append("保存的品类有重复，请检查" + " ");
        }
    }

    private void checkShortCegName(List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList, StringBuilder oneShortBuilder) {
        // 校验专家团简称
        checkShortL3CegName(dimCatgModlCegIctList,oneShortBuilder);
        // 校验模块简称
        checkShortL4CegName(dimCatgModlCegIctList,oneShortBuilder);
    }

    private void checkShortL4CegName(List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList, StringBuilder oneShortBuilder) {
        Map<String, List<DmDimCatgModlCegIctVO>> l4CegRepeatList = dimCatgModlCegIctList.stream().map(l4Ceg -> {
            DmDimCatgModlCegIctVO dmDimCatgModlCegIctVO = new DmDimCatgModlCegIctVO();
            dmDimCatgModlCegIctVO.setL4CegCnName(l4Ceg.getL4CegCnName());
            dmDimCatgModlCegIctVO.setL4CegShortCnName(l4Ceg.getL4CegShortCnName());
            return dmDimCatgModlCegIctVO;
        }).distinct().collect(Collectors.groupingBy(DmDimCatgModlCegIctVO::getL4CegShortCnName));
        List<String> l4CegShortName = l4CegRepeatList.keySet().stream().filter(key -> l4CegRepeatList.get(key).size() > 1).distinct().collect(Collectors.toList());

        if (l4CegShortName.size() > 0) {
            for (Map.Entry<String, List<DmDimCatgModlCegIctVO>> l4Entry : l4CegRepeatList.entrySet()) {
                String entryKey = l4Entry.getKey();
                l4CegShortName.stream().forEach(l4Ceg -> {
                    if (entryKey.equals(l4Ceg)) {
                        List<DmDimCatgModlCegIctVO> l4EntryValue = l4Entry.getValue();
                        for (DmDimCatgModlCegIctVO catgModlCegIctVO : l4EntryValue) {
                            if (!oneShortBuilder.toString().contains(catgModlCegIctVO.getL4CegShortCnName())) {
                                oneShortBuilder.append(catgModlCegIctVO.getL4CegShortCnName() + " ");
                            }
                        }
                    }
                });
            }
        }
    }

    private void checkShortL3CegName(List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList, StringBuilder oneShortBuilder) {
        Map<String, List<DmDimCatgModlCegIctVO>> l3CegRepeatList = dimCatgModlCegIctList.stream().map(l3Ceg -> {
            DmDimCatgModlCegIctVO dmDimCatgModlCegIctVO = new DmDimCatgModlCegIctVO();
            dmDimCatgModlCegIctVO.setL3CegCnName(l3Ceg.getL3CegCnName());
            dmDimCatgModlCegIctVO.setL3CegShortCnName(l3Ceg.getL3CegShortCnName());
            return dmDimCatgModlCegIctVO;
        }).distinct().collect(Collectors.groupingBy(DmDimCatgModlCegIctVO::getL3CegShortCnName));
        List<String> l3CegShortName = l3CegRepeatList.keySet().stream().filter(key -> l3CegRepeatList.get(key).size() > 1).distinct().collect(Collectors.toList());
        if (l3CegShortName.size() > 0) {
            for (Map.Entry<String, List<DmDimCatgModlCegIctVO>> l3Entry : l3CegRepeatList.entrySet()) {
                String entryKey = l3Entry.getKey();
                l3CegShortName.stream().forEach(l3Ceg -> {
                    if (entryKey.equals(l3Ceg)) {
                        List<DmDimCatgModlCegIctVO> l3EntryValue = l3Entry.getValue();
                        for (DmDimCatgModlCegIctVO catgModlCegIctVO : l3EntryValue) {
                            if (!oneShortBuilder.toString().contains(catgModlCegIctVO.getL3CegShortCnName())) {
                                oneShortBuilder.append(catgModlCegIctVO.getL3CegShortCnName() + " ");
                            }
                        }
                    }
                });
            }
        }
    }

    private Boolean checkL3ParamAndService(List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion,
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList) {
        // 只有专家团+简称
        List<String> lv3Service = dmDimCatgModlListWithVersion.stream()
            .map(cegIctVO -> cegIctVO.getL3CegCnName() + cegIctVO.getL3CegCode() + cegIctVO.getL3CegShortCnName())
            .collect(Collectors.toList());
        List<String> lv3Param = dimCatgModlCegIctList.stream()
            .map(cegIctVO -> cegIctVO.getL3CegCnName() + cegIctVO.getL3CegCode() + cegIctVO.getL3CegShortCnName())
            .collect(Collectors.toList());

        if (!lv3Service.containsAll(lv3Param)) {
            return true;
        }
        return false;
    }

    @Override
    @JalorOperation(code = "relationImport", desc = "维度关系导入功能")
    @Audit(module = "configDimensionManageService-relationImport", operation = "relationImport",
        message = "维度关系导入功能")
    public ResultDataVO relationImport(Attachment attachment, Long versionId,String industryOrg)
        throws CommonApplicationException, IOException {
        ByteArrayOutputStream byteArrayOutputStream = null;
        try {
            if (null == versionId) {
                throw new CommonApplicationException("入参清单刷新版本为空");
            }
            Timestamp creationDate = new Timestamp(System.currentTimeMillis());
            String tablePreFix = TableNameVO.getTablePreFix(industryOrg);
            // 查询版本
            DmFocVersionInfoDTO dmFocPlanVersionVO = dmFocVersionDao.findDmFocPlanVersionVOById(versionId,tablePreFix);
            List<ExcelVO> heads = new ArrayList();
            List<HeaderVo> model = new LinkedList<>();
            // 获取excel表头模型和行数
            Map<String, Object> params = getHeaderModuleMap(heads, model,industryOrg);
            // 导入信息记录对象VO
            Long userId = UserInfoUtils.getUserId();
            UploadInfoVO uploadInfoVO = getUploadInfoVO(attachment, dmFocPlanVersionVO, params, userId);
            uploadInfoVO.setCreationDate(creationDate);
            byteArrayOutputStream = excelUtil.putInputStreamCacher(attachment.getDataHandler().getInputStream());
            List<LinkedHashMap<String, Object>> maps = validImpModel(attachment, heads, model, uploadInfoVO,
                byteArrayOutputStream);
            // 将map数据转为json数组
            JSONArray jsonArray = mapToObject(model, maps);
            List<DmDimCatgModlCegIctVO> dmDimCatgModlCegIctList = jsonArray.toJavaList(DmDimCatgModlCegIctVO.class);
            List<DmDimCatgModlCegIctVO> errorList = new ArrayList<>();
            List<DmDimCatgModlCegIctVO> errorGeneralList = new ArrayList<>();
            StringBuilder errMsgBuilder = new StringBuilder();
            if (CollectionUtils.isEmpty(dmDimCatgModlCegIctList)) {
                throw new CommonApplicationException("导入的模板为空，没数据");
            }
            // 名称编码去空格
            trimParamSpace(dmDimCatgModlCegIctList);
            // 数据的匹配校验
            int errorCount = validDimensionData(tablePreFix,dmDimCatgModlCegIctList, errorList, null);
            // 数据的常规校验
            StringBuilder errorBuilder = checkImportData(dmDimCatgModlCegIctList,errMsgBuilder,errorList,errorGeneralList);

            List<Map> normalList = getCateNormalList(dmDimCatgModlCegIctList);
            List<Map> dimensionDataList = getDataList(uploadInfoVO, dmDimCatgModlCegIctList, errorList);
            DmFocVersionInfoDTO focPlanVersionVO;
            if (0 == errorCount && ObjectUtils.isEmpty(errorBuilder)) {
                focPlanVersionVO = saveOrUpdateDimension(tablePreFix, model, uploadInfoVO, dmDimCatgModlCegIctList, normalList, dimensionDataList);
            } else {
                // 上传文件
                uploadImpExpRecord(industryOrg, model, uploadInfoVO, dmDimCatgModlCegIctList, errorList, errorBuilder, normalList, dimensionDataList);
                throw new CommonApplicationException("输入值不合法");
            }
            return ResultDataVO.success(Optional.ofNullable(focPlanVersionVO).orElse(new DmFocVersionInfoDTO()));
        } catch (IOException e) {
            LOGGER.error("import error: {}", e.getMessage());
            return ResultDataVO.failure(ResultCodeEnum.SERVER_ERROR, e.getMessage());
        } finally {
            if (null != byteArrayOutputStream) {
                byteArrayOutputStream.close();
            }
        }
    }

    private void uploadImpExpRecord(String industryOrg, List<HeaderVo> model, UploadInfoVO uploadInfoVO, List<DmDimCatgModlCegIctVO> dmDimCatgModlCegIctList, List<DmDimCatgModlCegIctVO> errorList, StringBuilder errorBuilder, List<Map> normalList, List<Map> dimensionDataList) throws CommonApplicationException, IOException {
        DmFoiImpExpRecordVO recordVO = uploadImportExcel(uploadInfoVO, dimensionDataList, model, normalList, false);
        // 截取异常反馈信息到2000
        checkFeedBackStr(errorList, errorBuilder, recordVO);
        recordVO.setCreationDate(uploadInfoVO.getCreationDate());
        recordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        recordVO.setExceptionFeedback(getErrorModelFeedback(errorList, errorBuilder).toString());
        recordVO.setModuleType(setModelType(industryOrg));
        statisticsExcelService.insertImportExcel(recordVO, uploadInfoVO, false, dmDimCatgModlCegIctList.size());
    }

    private void checkFeedBackStr(List<DmDimCatgModlCegIctVO> errorList, StringBuilder errorBuilder, DmFoiImpExpRecordVO recordVO)
        throws UnsupportedEncodingException {
        String errorFeedback = getErrorModelFeedback(errorList, errorBuilder).toString();
        int length = errorFeedback.getBytes("UTF-8").length;
        if (length >= 2000) {
            String substring = errorFeedback.substring(0,666);
            recordVO.setExceptionFeedback(substring);
        } else {
            recordVO.setExceptionFeedback(getErrorModelFeedback(errorList, errorBuilder).toString());
        }
    }

    private StringBuilder checkImportData(List<DmDimCatgModlCegIctVO> dmDimCatgModlCegIctList,StringBuilder errMsgBuilder,
        List<DmDimCatgModlCegIctVO> errorList,List<DmDimCatgModlCegIctVO> errorGeneralList) throws CommonApplicationException {
        // 构建检验导入数据的前置条件
        Map<String, List<DmDimCatgModlCegIctVO>> duplicateMap = dmDimCatgModlCegIctList.stream().collect(Collectors.groupingBy(
            x -> x.getCategoryCode() + "#" + x.getCategoryCnName() + "#"
                + x.getL3CegCnName() + "#" + x.getL3CegCode() + "#" + x.getL3CegShortCnName() + "#"
                + x.getL4CegCnName() + "#" + x.getL4CegCode() + "#" + x.getL4CegShortCnName()));

        Map<String, List<DmDimCatgModlCegIctVO>> l3MoreShortNameMap = dmDimCatgModlCegIctList.stream().map(l3Ceg -> {
            DmDimCatgModlCegIctVO dmDimCatgModlCegIctVO = new DmDimCatgModlCegIctVO();
            dmDimCatgModlCegIctVO.setL3CegCnName(l3Ceg.getL3CegCnName());
            dmDimCatgModlCegIctVO.setL3CegShortCnName(l3Ceg.getL3CegShortCnName());
            return dmDimCatgModlCegIctVO;
        }).distinct().collect(Collectors.groupingBy(DmDimCatgModlCegIctVO::getL3CegCnName));
        Map<String, List<DmDimCatgModlCegIctVO>> l4MoreShortNameMap = dmDimCatgModlCegIctList.stream().map(l4Ceg -> {
            DmDimCatgModlCegIctVO dmDimCatgModlCegIctVO = new DmDimCatgModlCegIctVO();
            dmDimCatgModlCegIctVO.setL4CegCnName(l4Ceg.getL4CegCnName());
            dmDimCatgModlCegIctVO.setL4CegShortCnName(l4Ceg.getL4CegShortCnName());
            return dmDimCatgModlCegIctVO;
        }).distinct().collect(Collectors.groupingBy(DmDimCatgModlCegIctVO::getL4CegCnName));

        Map<String, List<DmDimCatgModlCegIctVO>> l3OnlyShortNameMap = dmDimCatgModlCegIctList.stream().map(l3Ceg -> {
            DmDimCatgModlCegIctVO dmDimCatgModlCegIctVO = new DmDimCatgModlCegIctVO();
            dmDimCatgModlCegIctVO.setL3CegCnName(l3Ceg.getL3CegCnName());
            dmDimCatgModlCegIctVO.setL3CegShortCnName(l3Ceg.getL3CegShortCnName());
            return dmDimCatgModlCegIctVO;
        }).distinct().collect(Collectors.groupingBy(DmDimCatgModlCegIctVO::getL3CegShortCnName));
        Map<String, List<DmDimCatgModlCegIctVO>> l4OnlyShortNameMap = dmDimCatgModlCegIctList.stream().map(l4Ceg -> {
            DmDimCatgModlCegIctVO dmDimCatgModlCegIctVO = new DmDimCatgModlCegIctVO();
            dmDimCatgModlCegIctVO.setL4CegCnName(l4Ceg.getL4CegCnName());
            dmDimCatgModlCegIctVO.setL4CegShortCnName(l4Ceg.getL4CegShortCnName());
            return dmDimCatgModlCegIctVO;
        }).distinct().collect(Collectors.groupingBy(DmDimCatgModlCegIctVO::getL4CegShortCnName));

        List<String> l3AllShortName = dmDimCatgModlCegIctList.stream().map(cegIctVO -> cegIctVO.getL3CegShortCnName())
            .collect(Collectors.toList());

        Map<String, List<DmDimCatgModlCegIctVO>> l3AndL4Map = getL3AndL4Map(dmDimCatgModlCegIctList);
        Map<String, List<DmDimCatgModlCegIctVO>> l3CategoryMap = getL3CategoryMap(dmDimCatgModlCegIctList);
        Map<String, List<DmDimCatgModlCegIctVO>> l4CategoryMap = getL4CategoryMap(dmDimCatgModlCegIctList);

        RelationVO relationVO = RelationVO.builder()
            .duplicateMap(duplicateMap)
            .l3MoreShortNameMap(l3MoreShortNameMap)
            .l4MoreShortNameMap(l4MoreShortNameMap)
            .l3OnlyShortNameMap(l3OnlyShortNameMap)
            .l4OnlyShortNameMap(l4OnlyShortNameMap)
            .l3AllShortName(l3AllShortName)
            .l3AndL4Map(l3AndL4Map)
            .l3CategoryMap(l3CategoryMap)
            .l4CategoryMap(l4CategoryMap)
            .build();
        dmDimCatgModlCegIctList.stream().forEach(
            dmDimCatgModlCegIctVO -> setDimensionErrorMsg(dmDimCatgModlCegIctVO,relationVO,errorGeneralList,errMsgBuilder,errorList));
        return errMsgBuilder;
    }

    private Map<String, List<DmDimCatgModlCegIctVO>> getL3AndL4Map(List<DmDimCatgModlCegIctVO> dmDimCatgModlCegIctList) {
        return dmDimCatgModlCegIctList.stream().map(catg -> {
            DmDimCatgModlCegIctVO dmDimCatgModlCegIctVO = new DmDimCatgModlCegIctVO();
            dmDimCatgModlCegIctVO.setL3CegCnName(catg.getL3CegCnName());
            dmDimCatgModlCegIctVO.setL4CegCnName(catg.getL4CegCnName());
            return dmDimCatgModlCegIctVO;
        }).distinct().collect(Collectors.groupingBy(DmDimCatgModlCegIctVO::getL4CegCnName));
    }

    private Map<String, List<DmDimCatgModlCegIctVO>> getL3CategoryMap(List<DmDimCatgModlCegIctVO> dmDimCatgModlCegIctList) {
        return dmDimCatgModlCegIctList.stream().map(catg -> {
            DmDimCatgModlCegIctVO dmDimCatgModlCegIctVO = new DmDimCatgModlCegIctVO();
            dmDimCatgModlCegIctVO.setL3CegCnName(catg.getL3CegCnName());
            dmDimCatgModlCegIctVO.setCategoryCnName(catg.getCategoryCnName());
            dmDimCatgModlCegIctVO.setCategoryCode(catg.getCategoryCode());
            return dmDimCatgModlCegIctVO;
        }).distinct().collect(Collectors.groupingBy(DmDimCatgModlCegIctVO::getCategoryCnName));
    }

    private Map<String, List<DmDimCatgModlCegIctVO>> getL4CategoryMap(List<DmDimCatgModlCegIctVO> dmDimCatgModlCegIctList) {
        return dmDimCatgModlCegIctList.stream().map(catg -> {
            DmDimCatgModlCegIctVO dmDimCatgModlCegIctVO = new DmDimCatgModlCegIctVO();
            dmDimCatgModlCegIctVO.setL4CegCnName(catg.getL4CegCnName());
            dmDimCatgModlCegIctVO.setCategoryCnName(catg.getCategoryCnName());
            dmDimCatgModlCegIctVO.setCategoryCode(catg.getCategoryCode());
            return dmDimCatgModlCegIctVO;
        }).distinct().collect(Collectors.groupingBy(DmDimCatgModlCegIctVO::getCategoryCnName));
    }

    private void setDimensionErrorMsg(DmDimCatgModlCegIctVO dmDimCatgModlCegIctVO, RelationVO relationVO,
        List<DmDimCatgModlCegIctVO> errorGeneralList, StringBuilder errMsgBuilder, List<DmDimCatgModlCegIctVO> errorList) {
        StringBuilder errBuilder = new StringBuilder();
        // 校验导入的错误数据
        checkDimensionErrorMsg(errBuilder,dmDimCatgModlCegIctVO,relationVO);

        if (errBuilder.length() > 0) {
            for (DmDimCatgModlCegIctVO cegIctVO : errorList) {
                if (cegIctVO.getL3CegCode().equals(dmDimCatgModlCegIctVO.getL3CegCode()) &&
                        cegIctVO.getL4CegCode().equals(dmDimCatgModlCegIctVO.getL4CegCode()) &&
                        cegIctVO.getCategoryCode().equals(dmDimCatgModlCegIctVO.getCategoryCode())) {
                    setErrorMsg(dmDimCatgModlCegIctVO, errBuilder, cegIctVO);
                }

            }
            handleError(errMsgBuilder, errBuilder);
        }
        errorGeneralList.add(dmDimCatgModlCegIctVO);
    }

    private void setErrorMsg(DmDimCatgModlCegIctVO dmDimCatgModlCegIctVO, StringBuilder errBuilder, DmDimCatgModlCegIctVO cegIctVO) {
        String errorOne = "";
        if (StringUtils.isNotEmpty(cegIctVO.getErrorMessage())) {
            if (!cegIctVO.getErrorMessage().contains(errBuilder.toString())) {
                errorOne = dmDimCatgModlCegIctVO.getErrorMessage();
            }
        }
        dmDimCatgModlCegIctVO.setErrorMessage(errorOne + errBuilder.toString());
    }
    private void handleError(StringBuilder errMsgBuilder, StringBuilder errBuilder) {
        if (!errMsgBuilder.toString().contains(errBuilder.toString())) {
            String[] errors = errBuilder.toString().split(";");
            for (String error : errors) {
                if (!errMsgBuilder.toString().contains(error)) {
                    errMsgBuilder.append(error + ";");
                }
            }
        }
    }

    private void checkDimensionErrorMsg(StringBuilder errBuilder, DmDimCatgModlCegIctVO dmDimCatgModlCegIctVO, RelationVO relationVO) {
        // 重复数据检验
        StringBuilder duplicateBuilder = new StringBuilder();
        validImportData(dmDimCatgModlCegIctVO,relationVO,duplicateBuilder);
        if (ObjectUtils.isNotEmpty(duplicateBuilder)) {
            errBuilder.append("检测到品类" + duplicateBuilder + "存在多行重复数据;");
        }
        // 校验同一个专家团/模块名称是否存在多个简称
        StringBuilder sameCegBuilder = new StringBuilder();
        checkImportMoreShortName(dmDimCatgModlCegIctVO,relationVO,sameCegBuilder);
        if (ObjectUtils.isNotEmpty(sameCegBuilder)) {
            errBuilder.append("检测到" + sameCegBuilder + "存在多个简称，系统仅允许唯一简称;");
        }
        // 校验同一个简称是否包含多个专家团/模块
        StringBuilder oneShortBuilder = new StringBuilder();
        checkImportOnlyShortName(dmDimCatgModlCegIctVO,relationVO,oneShortBuilder);
        if (ObjectUtils.isNotEmpty(oneShortBuilder)) {
            errBuilder.append("检测到" + oneShortBuilder + "同一简称被多处引用，系统仅允许唯一简称;");
        }
        // 校验入参里同一个简称是否被多个专家团/模块引用
        StringBuilder otherShortBuilder = new StringBuilder();
        checkImportOtherShortName(dmDimCatgModlCegIctVO,relationVO,otherShortBuilder);
        if (ObjectUtils.isNotEmpty(otherShortBuilder)) {
            errBuilder.append("检测到" + otherShortBuilder + "同一简称被多处引用，系统仅允许唯一简称;");
        }
        // 检验同一品类存在多个专项采购认证部/模块
        StringBuilder sameCategoryBuilder = new StringBuilder();
        checkImportMoreCate(dmDimCatgModlCegIctVO,relationVO,sameCategoryBuilder);
        if (ObjectUtils.isNotEmpty(sameCategoryBuilder)) {
            errBuilder.append("检测到同一" + sameCategoryBuilder + "被多处引用，需要符合系统唯一性;");
        }
    }

    private void checkImportMoreCate(DmDimCatgModlCegIctVO dmDimCatgModlCegIctVO, RelationVO relationVO, StringBuilder sameCategoryBuilder) {
        Map<String, List<DmDimCatgModlCegIctVO>> l3AndL4Map = relationVO.getL3AndL4Map();
        Map<String, List<DmDimCatgModlCegIctVO>> l3CategoryMap = relationVO.getL3CategoryMap();
        Map<String, List<DmDimCatgModlCegIctVO>> l4CategoryMap = relationVO.getL4CategoryMap();
        List<String> l3AndL4List = l3AndL4Map.keySet().stream().filter(key -> l3AndL4Map.get(key).size() > 1).distinct().collect(Collectors.toList());
        List<String> l3CategoryList = l3CategoryMap.keySet().stream().filter(key -> l3CategoryMap.get(key).size() > 1).distinct().collect(Collectors.toList());
        List<String> l4CategoryList = l4CategoryMap.keySet().stream().filter(key -> l4CategoryMap.get(key).size() > 1).distinct().collect(Collectors.toList());
        if (l3AndL4List.size() > 0) {
            if (l3AndL4List.contains(dmDimCatgModlCegIctVO.getL4CegCnName())) {
                sameCategoryBuilder.append(dmDimCatgModlCegIctVO.getL4CegCnName() + " ");
            }
        }
        if (l3CategoryList.size() > 0) {
            if (l3CategoryList.contains(dmDimCatgModlCegIctVO.getCategoryCnName())) {
                Set<String> setList = new HashSet<>();
                for (List<DmDimCatgModlCegIctVO> l3CategoryLists : l3CategoryMap.values()) {
                    for (DmDimCatgModlCegIctVO modlCegIctVO : l3CategoryLists) {
                        setList.add(modlCegIctVO.getCategoryCode());
                    }
                }
                if (setList.size() == 1) {
                    sameCategoryBuilder.append(dmDimCatgModlCegIctVO.getCategoryCode() + " ");
                }
            }
        }
        if (l4CategoryList.size() > 0) {
            if (l4CategoryList.contains(dmDimCatgModlCegIctVO.getCategoryCnName()) &&
                !sameCategoryBuilder.toString().contains(dmDimCatgModlCegIctVO.getCategoryCode())) {
                Set<String> setList = new HashSet<>();
                for (List<DmDimCatgModlCegIctVO> l4CategoryLists : l4CategoryMap.values()) {
                    for (DmDimCatgModlCegIctVO modlCegIctVO : l4CategoryLists) {
                        setList.add(modlCegIctVO.getCategoryCode());
                    }
                }
                if (setList.size() == 1) {
                    sameCategoryBuilder.append(dmDimCatgModlCegIctVO.getCategoryCode() + " ");
                }
            }
        }
    }

    private void checkImportOtherShortName(DmDimCatgModlCegIctVO dmDimCatgModlCegIctVO, RelationVO relationVO, StringBuilder otherShortBuilder) {
        List<String> l3AllShortName = relationVO.getL3AllShortName();
        if (l3AllShortName.contains(dmDimCatgModlCegIctVO.getL4CegShortCnName())) {
            otherShortBuilder.append(dmDimCatgModlCegIctVO.getL4CegShortCnName() + " ");
        }
    }

    private void checkImportOnlyShortName(DmDimCatgModlCegIctVO dmDimCatgModlCegIctVO, RelationVO relationVO, StringBuilder oneShortBuilder) {
        Map<String, List<DmDimCatgModlCegIctVO>> l3OnlyShortNameMap = relationVO.getL3OnlyShortNameMap();
        Map<String, List<DmDimCatgModlCegIctVO>> l4OnlyShortNameMap = relationVO.getL4OnlyShortNameMap();
        List<String> l3OnlyName = l3OnlyShortNameMap.keySet().stream().filter(key -> l3OnlyShortNameMap.get(key).size() > 1).distinct().collect(Collectors.toList());
        List<String> l4OnlyName = l4OnlyShortNameMap.keySet().stream().filter(key -> l4OnlyShortNameMap.get(key).size() > 1).distinct().collect(Collectors.toList());
        if (l3OnlyName.size() > 0) {
            if (l3OnlyName.contains(dmDimCatgModlCegIctVO.getL3CegShortCnName())) {
                oneShortBuilder.append(dmDimCatgModlCegIctVO.getL3CegShortCnName() + " ");
            }
        }
        if (l4OnlyName.size() > 0) {
            if (l4OnlyName.contains(dmDimCatgModlCegIctVO.getL4CegShortCnName())) {
                oneShortBuilder.append(dmDimCatgModlCegIctVO.getL4CegShortCnName() + " ");
            }
        }
    }

    private void checkImportMoreShortName(DmDimCatgModlCegIctVO dmDimCatgModlCegIctVO, RelationVO relationVO, StringBuilder sameCegBuilder) {
        Map<String, List<DmDimCatgModlCegIctVO>> l3MoreShortNameMap = relationVO.getL3MoreShortNameMap();
        Map<String, List<DmDimCatgModlCegIctVO>> l4MoreShortNameMap = relationVO.getL4MoreShortNameMap();
        List<String> l3CegName = l3MoreShortNameMap.keySet().stream().filter(key -> l3MoreShortNameMap.get(key).size() > 1).distinct().collect(Collectors.toList());
        List<String> l4CegName = l4MoreShortNameMap.keySet().stream().filter(key -> l4MoreShortNameMap.get(key).size() > 1).distinct().collect(Collectors.toList());
        if (l3CegName.size() > 0) {
            if (l3CegName.contains(dmDimCatgModlCegIctVO.getL3CegCnName())) {
                sameCegBuilder.append(dmDimCatgModlCegIctVO.getL3CegCnName() + " ");
            }
        }
        if (l4CegName.size() > 0) {
            if (l4CegName.contains(dmDimCatgModlCegIctVO.getL4CegCnName())) {
                sameCegBuilder.append(dmDimCatgModlCegIctVO.getL4CegCnName() + " ");
            }
        }
    }

    private void validImportData(DmDimCatgModlCegIctVO dmDimCatgModlCegIctVO, RelationVO relationVO, StringBuilder duplicateBuilder) {
        Map<String, List<DmDimCatgModlCegIctVO>> repeatDimensionList = relationVO.getDuplicateMap();
        String paramVo = dmDimCatgModlCegIctVO.getCategoryCode() + "#" + dmDimCatgModlCegIctVO.getCategoryCnName() + "#"
            + dmDimCatgModlCegIctVO.getL3CegCnName() + "#" + dmDimCatgModlCegIctVO.getL3CegCode() + "#" + dmDimCatgModlCegIctVO.getL3CegShortCnName() + "#"
            + dmDimCatgModlCegIctVO.getL4CegCnName() + "#" + dmDimCatgModlCegIctVO.getL4CegCode() + "#" + dmDimCatgModlCegIctVO.getL4CegShortCnName();
        List<DmDimCatgModlCegIctVO> dmFoqRawMaterialsCategoryVOS = repeatDimensionList.get(paramVo);
        if (dmFoqRawMaterialsCategoryVOS.size() > 1) {
            duplicateBuilder.append(dmDimCatgModlCegIctVO.getCategoryCnName() + " ");
        }
    }

    @Override
    @JalorOperation(code = "exportRelationList", desc = "导出映射关系维表")
    @Audit(module = "configDimensionManageService-exportRelationList", operation = "exportRelationList",
        message = "导出映射关系维表")
    public ResultDataVO exportRelationList(DmDimCatgModlCegIctVO dmDimCatgModlCegIctVO, HttpServletResponse response)
        throws CommonApplicationException {
        if (null == dmDimCatgModlCegIctVO.getVersionId()) {
            throw new CommonApplicationException("入参维表版本为空");
        }
        try {
            String industryOrg = dmDimCatgModlCegIctVO.getIndustryOrg();
            dmDimCatgModlCegIctVO.setTablePreFix(TableNameVO.getTablePreFix(industryOrg));
            exportRelation(dmDimCatgModlCegIctVO, response);
        } catch (IOException e) {
            LOGGER.error("exportRelationList error: {}", e.getMessage());
        }
        return ResultDataVO.success(MapUtil.of(new Object[][] {{"expFlag", true}}));
    }

    private void exportRelation(DmDimCatgModlCegIctVO dmDimCatgModlCegIctVO, HttpServletResponse response)
        throws CommonApplicationException, IOException {
        Timestamp creationDate = new Timestamp(System.currentTimeMillis());
        // 获取需要导出的数据和动态表头
        DmFocVersionInfoDTO dmFocPlanVersionVO = dmFocVersionDao.findDmFocPlanVersionVOById(dmDimCatgModlCegIctVO.getVersionId(),dmDimCatgModlCegIctVO.getTablePreFix());
        // 查询列表
        Map<String, Object> dimensionMap = getDimensionList(dmDimCatgModlCegIctVO);
        List<Map> dimensionList = new ArrayList<>();
        if (dimensionMap.get("mappingDimensionList") instanceof List) {
            dimensionList = (List<Map>) dimensionMap.get("mappingDimensionList");
        }
        String sheetName = "";
        if (dimensionMap.get("sheetName") instanceof String) {
            sheetName = (String) dimensionMap.get("sheetName");
        }
        if (CollectionUtils.isNotEmpty(dimensionList)) {
            // 获取第一行表单
            List<AbstractExcelTitleVO> formsVO = getRelationFormsVO(dmFocPlanVersionVO.getVersion());
            // 设置第二行表头模型
            Set<String> titles = new LinkedHashSet<>();
            List<HeaderVo> headers = new ArrayList<>();
            if (dimensionMap.get("headers") instanceof List) {
                headers = (List<HeaderVo>) dimensionMap.get("headers");
            }
            List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
            setHeader(titleVoList, headers, titles);
            // 文件名
            String filename = CommonConstant.MAPPING_RELATIONAL_DIMENSION.concat(
                new SimpleDateFormat("_yyyy-MM-dd HH_mm_ss").format(new Date()));
            ExportExcelVo exportExcelVo = ExportExcelVo.builder()
                .formInfoVo(formsVO)
                .titleVoList(titleVoList)
                .list(dimensionList)
                .sheetName(sheetName)
                .fileName(filename)
                .mergeCell(false)
                .build();
            List<ExportExcelVo> exportExcelVoList = new ArrayList<>();
            exportExcelVoList.add(exportExcelVo);
            DmFoiImpExpRecordVO expRecordVO = excelUtil.expSelectColumnExcel(exportExcelVoList, response);
            // 插入导出数据
            expRecordVO.setModuleType(setModelType(dmDimCatgModlCegIctVO.getIndustryOrg()));
            expRecordVO.setCreationDate(creationDate);
            expRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
            statisticsExcelService.insertExportExcelRecord(expRecordVO);
        } else {
            DmFoiImpExpRecordVO dmFoiImpExpRecordVO = new DmFoiImpExpRecordVO();
            dmFoiImpExpRecordVO.setExceptionFeedback("后台没查到维表数据,无法导出");
            String modelType = setModelType(dmDimCatgModlCegIctVO.getIndustryOrg());
            dmFoiImpExpRecordVO.setCreationDate(creationDate);
            dmFoiImpExpRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
            statisticsExcelService.insertFailExportExcelRecord(dmFoiImpExpRecordVO, 0, 0,modelType);
            throw new CommonApplicationException("后台没查到维表数据,无法导出");
        }
    }

    private Map<String, Object> getDimensionList(DmDimCatgModlCegIctVO dmDimCatgModlCegIctVO) {
        String sheetName = CommonConstant.MAPPING_RELATIONAL_DIMENSION;
        List<HeaderVo> headers = CommonConstant.MAPPING_RELATIONAL_DIMENSION_HEADER;
        // 查询维表清单
        List<Map> dimensionList = iDmDimCatgModlCegIctDao.findExportCatgModlCegIctList(dmDimCatgModlCegIctVO);

        Map<String, Object> map = new HashMap();
        map.put("sheetName", sheetName);
        map.put("headers", headers);
        map.put("mappingDimensionList", dimensionList);
        return map;
    }

    /**
     * [服务名称]getFormsVOs 获取维表版本
     *
     * @param versionName 版本名称
     * @return List<AbstractExcelTitleVO>
     * <AUTHOR>
     */
    private List<AbstractExcelTitleVO> getRelationFormsVO(String versionName) {
        List<AbstractExcelTitleVO> formInfoVo = new ArrayList<>();
        LeafExcelTitleVO columnCnVersion = new LeafExcelTitleVO(CommonConstant.DIMENSION_VERSION + versionName,
            CommonConstant.WIDTH, true, "dimensionVersion", "dimensionVersion", CellType.STRING, false);
        formInfoVo.add(columnCnVersion);
        return formInfoVo;
    }

    /**
     *  [服务名称]mapToObject
     *
     * @param model 参数
     * @param maps 参数
     * @return JSONArray
     */
    public JSONArray mapToObject(List<HeaderVo> model, List<LinkedHashMap<String, Object>> maps) {
        JSONArray jsonArray = new JSONArray();
        for (int i = 1; i < maps.size(); i++) {
            Map<String, Object> map = maps.get(i);
            JSONObject jsonObject = new JSONObject();
            Set<String> strings = map.keySet();
            mapToObjectSub(model, map, jsonObject, strings);
            jsonArray.add(jsonObject);
        }
        return jsonArray;
    }

    private void mapToObjectSub(List<HeaderVo> model, Map<String, Object> map, JSONObject jsonObject,
        Set<String> strings) {
        for (String string : strings) {
            for (HeaderVo headerVo : model) {
                if (string.equals(headerVo.getTitle())) {
                    String field = formatField(headerVo.getField(), CommonConstant.PATTERN_COLUMN);
                    jsonObject.put(field, map.get(headerVo.getTitle()));
                }
            }
        }
    }

    private String formatField(String string, Pattern pattern) {
        Matcher matcher = pattern.matcher(string);
        if (matcher.find()) {
            String group = matcher.group(1);
            String replace = string.replace(group, matcher.group(2).toUpperCase(Locale.ROOT));
            string = formatField(replace, pattern);
        }
        return string;
    }

    private StringBuilder getErrorModelFeedback(List<DmDimCatgModlCegIctVO> errorList, StringBuilder stringBuilder) {
        for (DmDimCatgModlCegIctVO dimCatgModlCegIctVO : errorList) {
            if (StringUtils.isNotEmpty(dimCatgModlCegIctVO.getErrorMessage())
                && !stringBuilder.toString().contains(dimCatgModlCegIctVO.getErrorMessage())) {
                String[] errorMsgs = dimCatgModlCegIctVO.getErrorMessage().split(";");
                for (String errorMsg : errorMsgs) {
                    if (!stringBuilder.toString().contains(errorMsg)) {
                        stringBuilder.append(errorMsg + ";");
                    }
                }
            }
        }
        return stringBuilder;
    }


    /**
     *
     * @param attachment 参数
     * @param heads 参数
     * @param model 参数
     * @param uploadInfoVO 参数
     * @param byteArrayOutputStream 参数
     * @return List 结果
     * @throws CommonApplicationException
     * @throws IOException
     */
    public List<LinkedHashMap<String, Object>> validImpModel(Attachment attachment, List<ExcelVO> heads,
        List<HeaderVo> model, UploadInfoVO uploadInfoVO, ByteArrayOutputStream byteArrayOutputStream)
        throws CommonApplicationException, IOException {
        List<LinkedHashMap<String, Object>> maps = new ArrayList<LinkedHashMap<String, Object>>();
        try {
            maps = excelUtil.importExcel(attachment, heads, uploadInfoVO, byteArrayOutputStream);
        } catch (IOException e) {
            LOGGER.info("excelUtil importExcel failed {}", e.getMessage());
        }
        InputStream inputStream = null;
        InputStream inputStreamModel = null;
        try {
            if (CollectionUtils.isEmpty(maps)) {
                DmFoiImpExpRecordVO dmFoiImpExpRecordVO = new DmFoiImpExpRecordVO();
                dmFoiImpExpRecordVO.setExceptionFeedback("列名出错");
                inputStream = excelUtil.getInputStream(byteArrayOutputStream);
                statisticsExcelService.getImportUploadFileKey(dmFoiImpExpRecordVO, uploadInfoVO.getUserId(), 1,
                    inputStream);
                dmFoiImpExpRecordVO.setCreationDate(uploadInfoVO.getCreationDate());
                dmFoiImpExpRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                statisticsExcelService.insertImportExcel(dmFoiImpExpRecordVO, uploadInfoVO, false, null);
                throw new CommonApplicationException("列名出错");
            }
            // 模板校验
            if (model.size() != maps.get(0).size()) {
                DmFoiImpExpRecordVO dmFoiImpExpRecordVO = new DmFoiImpExpRecordVO();
                dmFoiImpExpRecordVO.setExceptionFeedback("列名出错");
                inputStream = excelUtil.getInputStream(byteArrayOutputStream);
                dmFoiImpExpRecordVO.setCreationDate(uploadInfoVO.getCreationDate());
                dmFoiImpExpRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                statisticsExcelService.getImportUploadFileKey(dmFoiImpExpRecordVO, uploadInfoVO.getUserId(), 1, inputStream);
                statisticsExcelService.insertImportExcel(dmFoiImpExpRecordVO, uploadInfoVO, false, null);
                throw new CommonApplicationException("列名出错");
            } else {
                for (HeaderVo headerVo : model) {
                    String title = headerVo.getTitle();
                    Map<String, Object> objectMap = maps.get(0);
                    if (!objectMap.containsValue(title)) {
                        DmFoiImpExpRecordVO dmFoiImpExpRecordVO = new DmFoiImpExpRecordVO();
                        dmFoiImpExpRecordVO.setExceptionFeedback("列名出错");
                        inputStreamModel = excelUtil.getInputStream(byteArrayOutputStream);
                        dmFoiImpExpRecordVO.setCreationDate(uploadInfoVO.getCreationDate());
                        dmFoiImpExpRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                        statisticsExcelService.getImportUploadFileKey(dmFoiImpExpRecordVO, uploadInfoVO.getUserId(), 1, inputStreamModel);
                        statisticsExcelService.insertImportExcel(dmFoiImpExpRecordVO, uploadInfoVO, false, null);
                        throw new CommonApplicationException("列名出错");
                    }
                }
            }
        } catch (IOException ex) {
            LOGGER.info("读取列名出错：{}", ex.getMessage());
        } finally {
            StreamUtil.closeStreams(inputStream);
            StreamUtil.closeStreams(inputStreamModel);
        }
        return maps;
    }

    private void validDupliceData(List<DmDimCatgModlCegIctVO> dmDimCatgModlCegIctList,StringBuilder stringBuilder) {
        Map<String, List<DmDimCatgModlCegIctVO>> repeatModlCegList = dmDimCatgModlCegIctList.stream()
            .collect(Collectors.groupingBy(
                x -> x.getCategoryCode() + "#" + x.getCategoryCnName() + "#"
                    + x.getL3CegCnName() + "#" + x.getL3CegCode() + "#" + x.getL3CegShortCnName() + "#"
                    + x.getL4CegCnName() + "#" + x.getL4CegCode() + "#" + x.getL4CegShortCnName()
            ));
        List<String> count = repeatModlCegList.keySet()
            .stream()
            .filter(key -> repeatModlCegList.get(key).size() > 1)
            .distinct()
            .collect(Collectors.toList());
        if (count.size() > 0) {
            stringBuilder.append("保存的数据有重复，请检查" + " ");
        }
    }

    private void trimParamSpace(List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList) {
        dimCatgModlCegIctList.stream().forEach(catg -> {

            if (StringUtils.isNotBlank(catg.getL3CegCnName())) {
                catg.setL3CegCnName(catg.getL3CegCnName().trim());
            }
            if (StringUtils.isNotBlank(catg.getL3CegShortCnName())) {
                catg.setL3CegShortCnName(catg.getL3CegShortCnName().trim());
            }
            if (StringUtils.isNotBlank(catg.getL3CegCode())) {
                catg.setL3CegCode(catg.getL3CegCode().trim());
            }
            if (StringUtils.isNotBlank(catg.getL4CegCnName())) {
                catg.setL4CegCnName(catg.getL4CegCnName().trim());
            }
            if (StringUtils.isNotBlank(catg.getL4CegShortCnName())) {
                catg.setL4CegShortCnName(catg.getL4CegShortCnName().trim());
            }
            if (StringUtils.isNotBlank(catg.getL4CegCode())) {
                catg.setL4CegCode(catg.getL4CegCode().trim());
            }
            if (StringUtils.isNotBlank(catg.getCategoryCnName())) {
                catg.setCategoryCnName(catg.getCategoryCnName().trim());
            }
            if (StringUtils.isNotBlank(catg.getCategoryCode())) {
                catg.setCategoryCode(catg.getCategoryCode().trim());
            }
        });
    }

    @NotNull
    private UploadInfoVO getUploadInfoVO(Attachment attachment, DmFocVersionInfoDTO dmFocPlanVersionVO,
        Map<String, Object> params, Long userId) throws IOException {
        UploadInfoVO uploadInfoVO = new UploadInfoVO();
        uploadInfoVO.setFileName(ExcelUtils.getFilename(attachment).concat(new SimpleDateFormat("_yyyy-MM-dd HH_mm_ss").format(new Date())));
        uploadInfoVO.setFileSize(attachment.getDataHandler().getInputStream().available() / 1024);
        uploadInfoVO.setParams(params);
        uploadInfoVO.setUserId(userId);
        uploadInfoVO.setVersion(dmFocPlanVersionVO.getVersion());
        return uploadInfoVO;
    }

    private List<Map> getCateNormalList(List<DmDimCatgModlCegIctVO> modelFoiTopCates) {
        List<Map> normalList = new ArrayList<>();
        for (DmDimCatgModlCegIctVO cateInfoVO : modelFoiTopCates) {
            Map cateMap = new LinkedHashMap();
            putCateMap(cateInfoVO, cateMap);
            normalList.add(cateMap);
        }
        return normalList;
    }

    /**
     * 封装成map
     *
     * @param cateInfoVO 参数
     * @param cateMap map
     */
    private void putCateMap(DmDimCatgModlCegIctVO cateInfoVO, Map cateMap) {
        cateMap.put("l3_ceg_code", cateInfoVO.getL3CegCode());
        cateMap.put("l3_ceg_cn_name", cateInfoVO.getL3CegCnName());
        cateMap.put("l3_ceg_short_cn_name", cateInfoVO.getL3CegShortCnName());
        cateMap.put("l4_ceg_code", cateInfoVO.getL4CegCode());
        cateMap.put("l4_ceg_cn_name", cateInfoVO.getL4CegCnName());
        cateMap.put("l4_ceg_short_cn_name", cateInfoVO.getL4CegShortCnName());
        cateMap.put("category_code", cateInfoVO.getCategoryCode());
        cateMap.put("category_cn_name", cateInfoVO.getCategoryCnName());
    }

    @NotNull
    private List<Map> getDataList(UploadInfoVO uploadInfoVO, List<DmDimCatgModlCegIctVO> dmDimCatgModlCegIctList,
        List<DmDimCatgModlCegIctVO> errorList) {
        List<Map> dataList = new ArrayList<>();
        for (DmDimCatgModlCegIctVO mdelInfoVO : errorList) {
            Map modelMap = new LinkedHashMap();
            putCateMap(mdelInfoVO, modelMap);
            modelMap.put("errorMsg", mdelInfoVO.getErrorMessage());
            dataList.add(modelMap);
        }
        uploadInfoVO.setRowNumber(dmDimCatgModlCegIctList.size());
        return dataList;
    }

    private DmFocVersionInfoDTO saveOrUpdateDimension(String tablePreFix, List<HeaderVo> model,
        UploadInfoVO uploadInfoVO, List<DmDimCatgModlCegIctVO> dmDimCatgModlCegIctList, List<Map> normalList,
        List<Map> dataList) throws CommonApplicationException, IOException {
        // 校验版本
        DmFocVersionInfoDTO build = DmFocVersionInfoDTO.builder()
            .dataType(IndustryConst.DataType.DIM.getValue())
            .status(IndustryConst.STATUS.NOT_STATUS.getValue())
            .tablePreFix(tablePreFix)
            .build();
        // 版本返回值对象
        DmFocVersionInfoDTO backVersionVO = null;
        List<DmFocVersionInfoDTO> planVersionVOList = dmFocVersionDao.findDmFocPlanVersionVOList(build);
        if (CollectionUtils.isNotEmpty(planVersionVOList)) {
            // 存在未刷新版本
            backVersionVO = saveDimensionData(tablePreFix,planVersionVOList.get(0), model, uploadInfoVO, dmDimCatgModlCegIctList, normalList, dataList);
        } else {
            // 没有未刷新的版本，新增版本后保存数据
            DmFocVersionInfoDTO versionVO = createVersionVO(tablePreFix);
            setDimCatgModlVO(dmDimCatgModlCegIctList, versionVO.getVersionId());
            iDmDimCatgModlCegIctDao.createDmDimCatgModlCegIctTList(dmDimCatgModlCegIctList,tablePreFix);
            // 记录上传成功excel到个人中心
            uploadImportSuccessFile(model, uploadInfoVO, normalList, dataList);
            backVersionVO = versionVO;
        }
        return backVersionVO;
    }

    @NotNull
    private DmFocVersionInfoDTO saveDimensionData(String tablePreFix,DmFocVersionInfoDTO dmFocPlanVersionVO, List<HeaderVo> model,
        UploadInfoVO uploadInfoVO, List<DmDimCatgModlCegIctVO> dmDimCatgModlCegIctList, List<Map> normalList,
        List<Map> dataList) throws CommonApplicationException, IOException {
        iDmDimCatgModlCegIctDao.deleteDmDimCatgModlCegIctT(dmFocPlanVersionVO.getVersionId(),tablePreFix);
        setDimCatgModlVO(dmDimCatgModlCegIctList, dmFocPlanVersionVO.getVersionId());
        iDmDimCatgModlCegIctDao.createDmDimCatgModlCegIctTList(dmDimCatgModlCegIctList,tablePreFix);
        // 设置版本名称
        setNewVersionName(dmFocPlanVersionVO, tablePreFix);
        // 记录上传成功excel到个人中心
        uploadImportSuccessFile(model, uploadInfoVO, normalList, dataList);
        return dmFocPlanVersionVO;
    }

    private void uploadImportSuccessFile(List<HeaderVo> model, UploadInfoVO uploadInfoVO, List<Map> normalList,
        List<Map> dataList) throws CommonApplicationException, IOException {
        // 上传文件
        DmFoiImpExpRecordVO recordVO = uploadImportExcel(uploadInfoVO, dataList, model, normalList, true);
        // 导入成功信息记录
        recordVO.setCreationDate(uploadInfoVO.getCreationDate());
        recordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        statisticsExcelService.insertImportExcel(recordVO, uploadInfoVO, true, uploadInfoVO.getRowNumber());
    }


    private Map<String, Object> getHeaderModuleMap(List<ExcelVO> heads, List<HeaderVo> model, String industryOrg) {
        Map<String, Object> params = new HashMap<>();
        model.addAll(CommonConstant.MAPPING_RELATIONAL_DIMENSION_HEADER);
        setValues(heads, model);
        String modelType = setModelType(industryOrg);
        params.put("module", modelType);
        try {
            Long maxRowNum = NumberUtil.parseLong(
                registryQueryService.findValueByPath("Jalor.Excel.ExcelImportMaxCount", true));
            params.put("maxRowNum", maxRowNum);
        } catch (ApplicationException e) {
            LOGGER.info("find maxRowNum error {}", e.getMessage());
        }
        return params;
    }

    @NotNull
    private String setModelType(String industryOrg) {
        String modelType;
        if (IndustryConst.INDUSTRY_ORG.ICT.getValue().equals(industryOrg)) {
            modelType = IndustryConst.INDUSTRY_ORG_PAGE.ICT_MODEL.getValue() + ModuleEnum.RELATION.getCnName();
        } else if (IndustryConst.INDUSTRY_ORG.ENERGY.getValue().equals(industryOrg)) {
            modelType = IndustryConst.INDUSTRY_ORG_PAGE.ENERGY_MODEL.getValue() + ModuleEnum.RELATION.getCnName();
        } else {
            modelType = IndustryConst.INDUSTRY_ORG_PAGE.IAS_MODEL.getValue() + ModuleEnum.RELATION.getCnName();
        }
        return modelType;
    }

    private void setValues(List<ExcelVO> list, List<HeaderVo> headerVoList) {
        for (HeaderVo header : headerVoList) {
            ExcelVO vo = new ExcelVO();
            vo.setHeadName(header.getTitle());
            vo.setHeadType(CommonConstant.VARCHAR);
            list.add(vo);
        }
    }

    private int validDimensionData(String tablePreFix,List<DmDimCatgModlCegIctVO> modelFoiTopCates, List<DmDimCatgModlCegIctVO> errorList,
        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion) {
        AtomicInteger atomicInteger = new AtomicInteger();
        // 拼接数据检验，当前对象是否与系统数据重复
        List<String> combineModlCegList = checkRepeatData(dmDimCatgModlListWithVersion);
        // 查询维度物料表校验品类和品类编码
        List<DmDimMaterialCodeVO> dimMaterialCodeD = iDmDimCatgModlCegIctDao.findDimMaterialCodeD(null);
        List<Map> dmFocCatgCegIctDTOS = iDmDimCatgModlCegIctDao.findCatgCegIctList(tablePreFix);

        dimMaterialCodeD.stream().forEach(dmDimMaterialCodeVO -> {
            for (Map map : dmFocCatgCegIctDTOS) {
                if (map.get("category_code").equals(dmDimMaterialCodeVO.getItemSubtypeCode())) {
                    String cnName = (String) map.get("category_cn_name");
                    dmDimMaterialCodeVO.setItemSubtypeCnName(cnName);
                }
            }
        });

        List<String> materialCodeList = dimMaterialCodeD.stream()
            .map(ele -> ele.getItemSubtypeCode())
            .collect(Collectors.toList());
        List<String> materialNameList = dimMaterialCodeD.stream()
            .map(ele -> ele.getItemSubtypeCnName())
            .collect(Collectors.toList());
        // 查询专家团名称和编码是否存在
        List<DmDimCatgModlCegIctVO> dimCegCodeD = iDmDimCatgModlCegIctDao.findDimCegCodeD();
        DmDimCatgModlCegIctVO otherBuild = DmDimCatgModlCegIctVO.builder()
            .l3CegCode(IndustryConst.OtherCeg.CODE.getValue())
            .l3CegCnName(IndustryConst.OtherCeg.NAME.getValue())
            .l3CegShortCnName(IndustryConst.OtherCeg.NAME.getValue())
            .l4CegCode(IndustryConst.OtherCeg.CODE.getValue())
            .l4CegCnName(IndustryConst.OtherCeg.NAME.getValue())
            .l4CegShortCnName(IndustryConst.OtherCeg.NAME.getValue())
            .build();
        dimCegCodeD.add(otherBuild);

        List<String> l3CodeList = dimCegCodeD.stream().map(ele -> ele.getL3CegCode()).collect(Collectors.toList());
        List<String> l3NameList = dimCegCodeD.stream().map(ele -> ele.getL3CegCnName()).collect(Collectors.toList());
        List<String> l4CodeList = dimCegCodeD.stream().map(ele -> ele.getL4CegCode()).collect(Collectors.toList());
        List<String> l4NameList = dimCegCodeD.stream().map(ele -> ele.getL4CegCnName()).collect(Collectors.toList());

        RelationVO relationVO = RelationVO.builder()
            .dimMaterialCodeD(dimMaterialCodeD)
            .materialCodeList(materialCodeList)
            .materialNameList(materialNameList)
            .dimCegCodeD(dimCegCodeD)
            .l3CodeList(l3CodeList)
            .l3NameList(l3NameList)
            .l4CodeList(l4CodeList)
            .l4NameList(l4NameList)
            .build();
        modelFoiTopCates.stream()
            .forEach(cate -> atomicInteger.addAndGet(setDimensionErrorCount(combineModlCegList, cate, relationVO, errorList)));
        return atomicInteger.get();
    }

    @NotNull
    private List<String> checkRepeatData(List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion) {
        List<String> combineModlCegList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dmDimCatgModlListWithVersion)) {
            for (DmDimCatgModlCegIctVO cegIctVO : dmDimCatgModlListWithVersion) {
                String combineModlCegStr = cegIctVO.getL3CegCnName() + cegIctVO.getL3CegCode() + cegIctVO.getL3CegShortCnName()
                    + cegIctVO.getL4CegCnName() + cegIctVO.getL4CegCode() + cegIctVO.getL4CegShortCnName()
                    + cegIctVO.getCategoryCode() + cegIctVO.getCategoryCnName();
                combineModlCegList.add(combineModlCegStr);
            }
        }
        return combineModlCegList;
    }

    private void saveRelationDimensionData(DimensionInputVO dimensionInputVO,
        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion,BackDimensionVO backDimensionVO) {
        DmFocVersionInfoDTO build = DmFocVersionInfoDTO.builder()
            .dataType(IndustryConst.DataType.DIM.getValue())
            .status(IndustryConst.STATUS.NOT_STATUS.getValue())
            .tablePreFix(dimensionInputVO.getTablePreFix())
            .build();
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList = dimensionInputVO.getDimCatgModlCegIctList();
        // 系统 专家团+模块+品类
        List<String> cegAndCateService = dmDimCatgModlListWithVersion.stream()
            .map(cegIctVO -> cegIctVO.getL3CegCnName() + cegIctVO.getL3CegCode()
                + cegIctVO.getL4CegCnName() + cegIctVO.getL4CegCode()
                + cegIctVO.getCategoryCode() + cegIctVO.getCategoryCnName())
            .collect(Collectors.toList());
        // 系统 品类
        List<String> cateService = dmDimCatgModlListWithVersion.stream()
            .map(cegIctVO -> cegIctVO.getCategoryCode() + cegIctVO.getCategoryCnName())
            .collect(Collectors.toList());
        List<DmFocVersionInfoDTO> versionVOList = dmFocVersionDao.findDmFocPlanVersionVOList(build);

        if (CollectionUtils.isNotEmpty(versionVOList)) {
            // 存在未刷新的版本
            dealDimensionDataWithVersion(dimensionInputVO,dmDimCatgModlListWithVersion, backDimensionVO, cegAndCateService, cateService, versionVOList);
        } else {
            // 不存在未刷新的版本
            dealDimensionDataNewVersion(dimensionInputVO,dimCatgModlCegIctList, dmDimCatgModlListWithVersion, backDimensionVO, cegAndCateService, cateService);
        }
    }

    private void dealDimensionDataNewVersion(DimensionInputVO dimensionInputVO,List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList, List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion,
        BackDimensionVO backDimensionVO, List<String> cegAndCateService, List<String> cateService) {
        // 不存在未刷新的版本，新增维度版本后保存数据
        DmFocVersionInfoDTO focPlanVersionVO = createVersionVO(dimensionInputVO.getTablePreFix());
        // 更新专家团/模块简称
        updateL3AndL4ShortNameNewVersion(dimCatgModlCegIctList, dmDimCatgModlListWithVersion, focPlanVersionVO);
        // 更新专家团/模块
        updateL3AndL4NewVersion(dimCatgModlCegIctList, dmDimCatgModlListWithVersion,cegAndCateService, cateService, focPlanVersionVO);
        // 更新品类
        updateCateNewVersion(cateService,dimCatgModlCegIctList,dmDimCatgModlListWithVersion,focPlanVersionVO);
        // 新增操作
        isInsertNewVersion(cegAndCateService,cateService,dimCatgModlCegIctList,dmDimCatgModlListWithVersion,focPlanVersionVO.getVersionId());
        setDimCatgModlVO(dmDimCatgModlListWithVersion,focPlanVersionVO.getVersionId());
        iDmDimCatgModlCegIctDao.createDmDimCatgModlCegIctTList(dmDimCatgModlListWithVersion,dimensionInputVO.getTablePreFix());
        backDimensionVO.setDmFocPlanVersionVO(focPlanVersionVO);
    }

    private void isInsertNewVersion(List<String> cegAndCateService, List<String> cateService,
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList, List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion, Long versionId) {
        List<DmDimCatgModlCegIctVO> dimIctList = new ArrayList<>();
        for (DmDimCatgModlCegIctVO modlCegIctVO : dimCatgModlCegIctList) {
            // 系统中专家团/模块（不含简称）不包含入参
            // 系统品类不包含入参品类,入参属于新增
            String cateParam = modlCegIctVO.getCategoryCode() + modlCegIctVO.getCategoryCnName();
            String cegAndCateParam = modlCegIctVO.getL3CegCnName() + modlCegIctVO.getL3CegCode()
                + modlCegIctVO.getL4CegCnName() + modlCegIctVO.getL4CegCode()
                + modlCegIctVO.getCategoryCode() + modlCegIctVO.getCategoryCnName();
            if (!cegAndCateService.contains(cegAndCateParam) && !cateService.contains(cateParam) && StringUtils.isEmpty(modlCegIctVO.getOldCategoryCode())) {
                modlCegIctVO.setVersionId(versionId);
                modlCegIctVO.setCreatedBy(UserInfoUtils.getUserId());
                modlCegIctVO.setCreationDate(new Date());
                modlCegIctVO.setLastUpdatedDate(new Date());
                modlCegIctVO.setLastUpdatedBy(UserInfoUtils.getUserId());
                modlCegIctVO.setDelFlag(CommonConstant.IS_NOT);
                modlCegIctVO.setSaveMethod(CommonConstant.SAVE_METHOD);
                dimIctList.add(modlCegIctVO);
            }
        }
        if (dimIctList.size() > 0) {
            // 进行添加操作
            dmDimCatgModlListWithVersion.addAll(dimIctList);
        }
    }

    private void updateCateNewVersion(List<String> cateService, List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList,
        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion,
        DmFocVersionInfoDTO focPlanVersionVO) {
        for (DmDimCatgModlCegIctVO modlCegIctVO : dimCatgModlCegIctList) {
            String cateParam = modlCegIctVO.getCategoryCode() + modlCegIctVO.getCategoryCnName();
            if (!cateService.contains(cateParam) && StringUtils.isNotEmpty(modlCegIctVO.getOldCategoryCode())) {
                dmDimCatgModlListWithVersion.stream().forEach(cegIctVO -> {
                    if (cegIctVO.getCategoryCode().equals(modlCegIctVO.getOldCategoryCode())) {
                        cegIctVO.setLastUpdateDate(new Date());
                        cegIctVO.setLastUpdatedBy(UserInfoUtils.getUserId());
                        cegIctVO.setVersionId(focPlanVersionVO.getVersionId());
                        cegIctVO.setL3CegShortCnName(modlCegIctVO.getL3CegShortCnName());
                        cegIctVO.setL3CegCnName(modlCegIctVO.getL3CegCnName());
                        cegIctVO.setL3CegCode(modlCegIctVO.getL3CegCode());
                        cegIctVO.setL4CegShortCnName(modlCegIctVO.getL4CegShortCnName());
                        cegIctVO.setL4CegCnName(modlCegIctVO.getL4CegCnName());
                        cegIctVO.setL4CegCode(modlCegIctVO.getL4CegCode());
                        cegIctVO.setCategoryCnName(modlCegIctVO.getCategoryCnName());
                        cegIctVO.setCategoryCode(modlCegIctVO.getCategoryCode());
                        cegIctVO.setOldCategoryCode(modlCegIctVO.getOldCategoryCode());
                    }
                });
            }
        }
    }

    private void dealDimensionDataWithVersion(DimensionInputVO dimensionInputVO,List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion,
        BackDimensionVO backDimensionVO, List<String> cegAndCateService, List<String> cateService, List<DmFocVersionInfoDTO> versionVOList) {
        // 校验为刷新的版本与当前入参版本是否一致
        Long dimVersionId = versionVOList.get(0).getVersionId();
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList = dimensionInputVO.getDimCatgModlCegIctList();
        Long paramVersionId = dimensionInputVO.getVersionId();
        // 版本一致，不需要组合数据
        if (dimVersionId.equals(paramVersionId)) {
            // 进行更新操作
            isUpdate(dimCatgModlCegIctList,dmDimCatgModlListWithVersion,cegAndCateService,cateService,dimVersionId,dimensionInputVO.getTablePreFix());
            // 进行添加操作
            isInsert(cegAndCateService,cateService,dimCatgModlCegIctList,dimVersionId,dimensionInputVO.getTablePreFix());
        } else {
            // 校验简称是否更改
            Boolean L3ShortFlag = checkL3ParamAndService(dmDimCatgModlListWithVersion, dimCatgModlCegIctList);
            Boolean L4ShortFlag = checkL4ParamAndService(dmDimCatgModlListWithVersion, dimCatgModlCegIctList);
            List<String> paramCateCodeList = dmDimCatgModlListWithVersion.stream().map(ele -> ele.getCategoryCode()).collect(Collectors.toList());
            // 未刷新版本的数据内容
            List<DmDimCatgModlCegIctVO> notRefreshListWithVersion = iDmDimCatgModlCegIctDao.findDmDimCatgModlCegIctList(
                DmDimCatgModlCegIctVO.builder().versionId(dimVersionId).tablePreFix(dimensionInputVO.getTablePreFix()).build());
            // 版本不一致，组装数据后保存数据
            combineDataForSave(dmDimCatgModlListWithVersion,notRefreshListWithVersion);
            // 进行更新操作
            isUpdate(dimCatgModlCegIctList,dmDimCatgModlListWithVersion,cegAndCateService,cateService,dimVersionId,dimensionInputVO.getTablePreFix());
            // 进行添加操作，组装数据
            combineDimensionData(dimCatgModlCegIctList,dmDimCatgModlListWithVersion,paramCateCodeList,L3ShortFlag,L4ShortFlag,dimVersionId,dimensionInputVO.getTablePreFix());
        }
        setNewVersionName(versionVOList.get(0), dimensionInputVO.getTablePreFix());
        backDimensionVO.setDmFocPlanVersionVO(versionVOList.get(0));
    }

    private Boolean checkL4ParamAndService(List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion, List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList) {
        // 只有专家团+简称
        List<String> lv4Service = dmDimCatgModlListWithVersion.stream()
            .map(cegIctVO -> cegIctVO.getL4CegCnName() + cegIctVO.getL4CegCode() + cegIctVO.getL4CegShortCnName())
            .collect(Collectors.toList());
        List<String> lv4Param = dimCatgModlCegIctList.stream()
            .map(cegIctVO -> cegIctVO.getL4CegCnName() + cegIctVO.getL4CegCode() + cegIctVO.getL4CegShortCnName())
            .collect(Collectors.toList());

        if (!lv4Service.containsAll(lv4Param)) {
            return true;
        }
        return false;
    }

    private void isUpdate(List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList, List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion,
        List<String> cegAndCateService, List<String> cateService, Long dimVersionId,String tablePreFix) {
        // 更新专家团/模块简称
        updateL3AndL4ShortNameWithVersion(dimCatgModlCegIctList,dmDimCatgModlListWithVersion,dimVersionId,tablePreFix);
        // 更新专家团/模块
        updateL3AndL4WithVersion(dimCatgModlCegIctList, cegAndCateService,cateService,dimVersionId,tablePreFix);
        // 更新品类
        updateCateWithVersion(cateService,dimCatgModlCegIctList,dimVersionId,tablePreFix);
    }

    private void updateCateWithVersion(List<String> cateService,List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList, Long dimVersionId,String tablePreFix) {
        for (DmDimCatgModlCegIctVO modlCegIctVO : dimCatgModlCegIctList) {
            modlCegIctVO.setTablePreFix(tablePreFix);
            String cateParam = modlCegIctVO.getCategoryCode() + modlCegIctVO.getCategoryCnName();
            if (!cateService.contains(cateParam) && StringUtils.isNotEmpty(modlCegIctVO.getOldCategoryCode())) {
                modlCegIctVO.setLastUpdateDate(new Date());
                modlCegIctVO.setLastUpdatedBy(UserInfoUtils.getUserId());
                modlCegIctVO.setVersionId(dimVersionId);
                iDmDimCatgModlCegIctDao.updateCateByVo(modlCegIctVO);
            }
        }
    }

    private void combineDimensionData(List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList, List<DmDimCatgModlCegIctVO> paramIctList,
        List<String> paramCateCodeList,Boolean L3ShortFlag, Boolean L4ShortFlag, Long dimVersionId,String tablePreFix) {
        for (DmDimCatgModlCegIctVO modlCegIctVO2 : dimCatgModlCegIctList) {
            if (StrUtil.isNotEmpty(modlCegIctVO2.getOldCategoryCode()) && paramCateCodeList.contains(modlCegIctVO2.getOldCategoryCode())) {
                // 编辑需要移除旧数据
                paramIctList.removeIf(model -> model.getCategoryCode().equals(modlCegIctVO2.getOldCategoryCode()));
            }
        }
        if (CollectionUtils.isNotEmpty(dimCatgModlCegIctList)) {
            // 组装专家团/模块简称
            combineShortCnName(paramIctList,dimCatgModlCegIctList,L3ShortFlag,L4ShortFlag);
            dimCatgModlCegIctList.addAll(paramIctList);
        }
        setDimCatgModlVO(dimCatgModlCegIctList, dimVersionId);
        iDmDimCatgModlCegIctDao.deleteDmDimCatgModlCegIctT(dimVersionId,tablePreFix);
        iDmDimCatgModlCegIctDao.createDmDimCatgModlCegIctTList(dimCatgModlCegIctList,tablePreFix);
    }

    private void combineShortCnName(List<DmDimCatgModlCegIctVO> paramIctList, List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList, Boolean L3ShortFlag, Boolean L4ShortFlag) {
        if (L3ShortFlag) {
            paramIctList.stream().forEach(cegIctVO -> {
                for (DmDimCatgModlCegIctVO ictVO : dimCatgModlCegIctList) {
                    if (cegIctVO.getL3CegCode().equals(ictVO.getL3CegCode())) {
                        cegIctVO.setL3CegShortCnName(ictVO.getL3CegShortCnName());
                    }
                }
            });
        }
        if (L4ShortFlag) {
            paramIctList.stream().forEach(cegIctVO -> {
                for (DmDimCatgModlCegIctVO ictVO : dimCatgModlCegIctList) {
                    if (cegIctVO.getL4CegCode().equals(ictVO.getL4CegCode())) {
                        cegIctVO.setL4CegShortCnName(ictVO.getL4CegShortCnName());
                    }
                }
            });
        }
    }

    private void combineDataForSave(List<DmDimCatgModlCegIctVO> paramListWithVersion, List<DmDimCatgModlCegIctVO> notRefreshListWithVersion) {
        List<DmDimCatgModlCegIctVO> combineDataList = new ArrayList<>();
        // 未刷新版本数据统一两版本不一致的简称
        notRefreshListWithVersion.stream().forEach(dmDimCatgModlCegIctVO -> {
            for (DmDimCatgModlCegIctVO paramVO : paramListWithVersion) {
                if (paramVO.getL3CegCode().equals(dmDimCatgModlCegIctVO.getL3CegCode()) && !paramVO.getL3CegShortCnName().equals(dmDimCatgModlCegIctVO.getL3CegShortCnName())) {
                    dmDimCatgModlCegIctVO.setL3CegShortCnName(paramVO.getL3CegShortCnName());
                }
                if (paramVO.getL4CegCode().equals(dmDimCatgModlCegIctVO.getL4CegCode()) && !paramVO.getL4CegShortCnName().equals(dmDimCatgModlCegIctVO.getL4CegShortCnName())) {
                    dmDimCatgModlCegIctVO.setL4CegShortCnName(paramVO.getL4CegShortCnName());
                }
            }
        });
        // 筛选出两版本相同品类的VO，并从未刷新版本中剔除掉，剩下不同品类的VO，存放进入参版本数据中
        for (DmDimCatgModlCegIctVO dmDimCatgModlCegIctVO : paramListWithVersion) {
            for (DmDimCatgModlCegIctVO cegIctVO : notRefreshListWithVersion) {
                if (dmDimCatgModlCegIctVO.getCategoryCode().equals(cegIctVO.getCategoryCode())) {
                    combineDataList.add(cegIctVO);
                }
            }
        }
        List<DmDimCatgModlCegIctVO> collect = combineDataList.stream().distinct().collect(Collectors.toList());
        notRefreshListWithVersion.removeAll(collect);
        // 组合两个版本数据到入参版本
        paramListWithVersion.addAll(notRefreshListWithVersion);
    }

    private void updateL3AndL4NewVersion(List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList,
        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion,List<String> cegAndCateService,
        List<String> cateService, DmFocVersionInfoDTO focPlanVersionVO) {
        dmDimCatgModlListWithVersion.stream().forEach(cegIctVO -> {
            for (DmDimCatgModlCegIctVO modlCegIctVO : dimCatgModlCegIctList) {
                String cateParam = modlCegIctVO.getCategoryCode() + modlCegIctVO.getCategoryCnName();
                String cegAndCateParam = modlCegIctVO.getL3CegCnName() + modlCegIctVO.getL3CegCode()
                    + modlCegIctVO.getL4CegCnName() + modlCegIctVO.getL4CegCode()
                    + modlCegIctVO.getCategoryCode() + modlCegIctVO.getCategoryCnName();
                // 更改的系统组合不包括入参，入参的品类在在系统里
                if (!cegAndCateService.contains(cegAndCateParam) && cateService.contains(cateParam)
                    && cegIctVO.getCategoryCode().equals(modlCegIctVO.getOldCategoryCode())) {
                    cegIctVO.setLastUpdateDate(new Date());
                    cegIctVO.setLastUpdatedBy(UserInfoUtils.getUserId());
                    cegIctVO.setVersionId(focPlanVersionVO.getVersionId());
                    cegIctVO.setL3CegShortCnName(modlCegIctVO.getL3CegShortCnName());
                    cegIctVO.setL3CegCnName(modlCegIctVO.getL3CegCnName());
                    cegIctVO.setL3CegCode(modlCegIctVO.getL3CegCode());
                }
            }
        });
    }

    private void updateL3AndL4ShortNameNewVersion(List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList, List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion, DmFocVersionInfoDTO focPlanVersionVO) {
        List<String> l3CegService = dmDimCatgModlListWithVersion.stream()
            .map(cegIctVO -> cegIctVO.getL3CegCnName() + cegIctVO.getL3CegCode() + cegIctVO.getL3CegShortCnName())
            .distinct().collect(Collectors.toList());
        List<String> l4CegService = dmDimCatgModlListWithVersion.stream()
            .map(cegIctVO -> cegIctVO.getL4CegCnName() + cegIctVO.getL4CegCode() + cegIctVO.getL4CegShortCnName())
            .distinct().collect(Collectors.toList());

        for (DmDimCatgModlCegIctVO modlCegIctVO : dimCatgModlCegIctList) {
            String l3CegParam = modlCegIctVO.getL3CegCnName() + modlCegIctVO.getL3CegCode() + modlCegIctVO.getL3CegShortCnName();
            String l4CegParam = modlCegIctVO.getL4CegCnName() + modlCegIctVO.getL4CegCode() + modlCegIctVO.getL4CegShortCnName();
            if (!l3CegService.contains(l3CegParam) && StringUtils.isNotEmpty(modlCegIctVO.getOldCategoryCode())) {
                for (DmDimCatgModlCegIctVO dmDimCatgModlCegIctVO : dmDimCatgModlListWithVersion) {
                    if (dmDimCatgModlCegIctVO.getL3CegCnName().equals(modlCegIctVO.getL3CegCnName())) {
                        dmDimCatgModlCegIctVO.setLastUpdateDate(new Date());
                        dmDimCatgModlCegIctVO.setLastUpdatedBy(UserInfoUtils.getUserId());
                        dmDimCatgModlCegIctVO.setVersionId(focPlanVersionVO.getVersionId());
                        dmDimCatgModlCegIctVO.setL3CegShortCnName(modlCegIctVO.getL3CegShortCnName());
                    }
                }
            }
            if (!l4CegService.contains(l4CegParam) && StringUtils.isNotEmpty(modlCegIctVO.getOldCategoryCode())) {
                for (DmDimCatgModlCegIctVO dmDimCatgModlCegIctVO : dmDimCatgModlListWithVersion) {
                    if (dmDimCatgModlCegIctVO.getL4CegCnName().equals(modlCegIctVO.getL4CegCnName())) {
                        dmDimCatgModlCegIctVO.setLastUpdateDate(new Date());
                        dmDimCatgModlCegIctVO.setLastUpdatedBy(UserInfoUtils.getUserId());
                        dmDimCatgModlCegIctVO.setVersionId(focPlanVersionVO.getVersionId());
                        dmDimCatgModlCegIctVO.setL4CegShortCnName(modlCegIctVO.getL4CegShortCnName());
                    }
                }
            }
        }
    }

    private void updateL3AndL4WithVersion(List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList,
        List<String> cegAndCateService, List<String> cateService, Long dimVersionId,String tablePreFix) {
        for (DmDimCatgModlCegIctVO modlCegIctVO : dimCatgModlCegIctList) {
            modlCegIctVO.setTablePreFix(tablePreFix);
            String cateParam = modlCegIctVO.getCategoryCode() + modlCegIctVO.getCategoryCnName();
            String cegAndCateParam = modlCegIctVO.getL3CegCnName() + modlCegIctVO.getL3CegCode()
                + modlCegIctVO.getL4CegCnName() + modlCegIctVO.getL4CegCode()
                + modlCegIctVO.getCategoryCode() + modlCegIctVO.getCategoryCnName();
            if (!cegAndCateService.contains(cegAndCateParam) && cateService.contains(cateParam) && StringUtils.isNotEmpty(modlCegIctVO.getOldCategoryCode())) {
                modlCegIctVO.setLastUpdateDate(new Date());
                modlCegIctVO.setLastUpdatedBy(UserInfoUtils.getUserId());
                modlCegIctVO.setVersionId(dimVersionId);
                iDmDimCatgModlCegIctDao.updateL3AndL4(modlCegIctVO);
            }
        }
    }

    private void updateL3AndL4ShortNameWithVersion(List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList,
        List<DmDimCatgModlCegIctVO> paramListWithVersion,Long dimVersionId,String tablePreFix) {
        List<String> l3CegService = paramListWithVersion.stream()
            .map(cegIctVO -> cegIctVO.getL3CegCnName() + "#" + cegIctVO.getL3CegCode() + "#" + cegIctVO.getL3CegShortCnName())
            .distinct().collect(Collectors.toList());
        List<String> l4CegService = paramListWithVersion.stream()
            .map(cegIctVO -> cegIctVO.getL4CegCnName() + "#" + cegIctVO.getL4CegCode() + "#" + cegIctVO.getL4CegShortCnName())
            .distinct().collect(Collectors.toList());

        for (DmDimCatgModlCegIctVO cegIctVO : dimCatgModlCegIctList) {
            cegIctVO.setTablePreFix(tablePreFix);
            String l3CegParam = cegIctVO.getL3CegCnName() + "#" + cegIctVO.getL3CegCode() + "#" + cegIctVO.getL3CegShortCnName();
            String l4CegParam = cegIctVO.getL4CegCnName() + "#" + cegIctVO.getL4CegCode() + "#" + cegIctVO.getL4CegShortCnName();
            if (!l3CegService.contains(l3CegParam) && StringUtils.isNotEmpty(cegIctVO.getOldCategoryCode())) {
                cegIctVO.setLastUpdateDate(new Date());
                cegIctVO.setLastUpdatedBy(UserInfoUtils.getUserId());
                cegIctVO.setVersionId(dimVersionId);
                // 更新相同专家团以及其他记录的简称
                iDmDimCatgModlCegIctDao.updateL3ShortName(cegIctVO);
            }
            if (!l4CegService.contains(l4CegParam) && StringUtils.isNotEmpty(cegIctVO.getOldCategoryCode())) {
                cegIctVO.setLastUpdateDate(new Date());
                cegIctVO.setLastUpdatedBy(UserInfoUtils.getUserId());
                cegIctVO.setVersionId(dimVersionId);
                // 更新相同模块以及其他记录的简称
                iDmDimCatgModlCegIctDao.updateL4ShortName(cegIctVO);
            }
        }
    }

    private void isInsert(List<String> cegAndCateService, List<String> cateService,
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList,Long dimVersionId,String tablePreFix) {
        List<DmDimCatgModlCegIctVO> dimIctList = new ArrayList<>();
        for (DmDimCatgModlCegIctVO modlCegIctVO : dimCatgModlCegIctList) {
            modlCegIctVO.setTablePreFix(tablePreFix);
            String cateParam = modlCegIctVO.getCategoryCode() + modlCegIctVO.getCategoryCnName();
            String cegAndCateParam = modlCegIctVO.getL3CegCnName() + modlCegIctVO.getL3CegCode()
                + modlCegIctVO.getL4CegCnName() + modlCegIctVO.getL4CegCode()
                + modlCegIctVO.getCategoryCode() + modlCegIctVO.getCategoryCnName();
            if (!cegAndCateService.contains(cegAndCateParam) && !cateService.contains(cateParam) && StringUtils.isEmpty(modlCegIctVO.getOldCategoryCode())) {
                dimIctList.add(modlCegIctVO);
            }
        }
        if (dimIctList.size() > 0) {
            List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion = iDmDimCatgModlCegIctDao.findDmDimCatgModlCegIctList(
                DmDimCatgModlCegIctVO.builder().versionId(dimVersionId).tablePreFix(tablePreFix).build());
            // 进行添加操作
            combineDimensionForSave(dimIctList, dmDimCatgModlListWithVersion, dimVersionId,tablePreFix);
            dimIctList = dimIctList.stream().distinct().collect(Collectors.toList());
            iDmDimCatgModlCegIctDao.deleteDmDimCatgModlCegIctT(dimVersionId,tablePreFix);
            iDmDimCatgModlCegIctDao.createDmDimCatgModlCegIctTList(dimIctList,tablePreFix);
        }
    }

    private int setDimensionErrorCount(List<String> combineModlCegList, DmDimCatgModlCegIctVO dmodlCegIctVO,
        RelationVO relationVO, List<DmDimCatgModlCegIctVO> errorList) {
        StringBuilder builder = new StringBuilder();
        int errorCount = 0;
        String voStr = dmodlCegIctVO.getL3CegCnName() + dmodlCegIctVO.getL3CegCode() + dmodlCegIctVO.getL3CegShortCnName()
            + dmodlCegIctVO.getL4CegCnName() + dmodlCegIctVO.getL4CegCode() + dmodlCegIctVO.getL4CegShortCnName()
            + dmodlCegIctVO.getCategoryCode() + dmodlCegIctVO.getCategoryCnName();
        // 校验专家团编码和名称
        validLv3CegData(combineModlCegList, dmodlCegIctVO, builder, voStr, relationVO);
        // 校验模块编码和名称
        validLv4CegData(dmodlCegIctVO, builder, relationVO);
        // 校验品类编码和名称
        validCategoryCodeWithName(dmodlCegIctVO, builder, relationVO);
        if (builder.length() > 0) {
            errorCount = errorCount + 1;
            dmodlCegIctVO.setErrorMessage(builder.toString());
        }
        errorList.add(dmodlCegIctVO);
        return errorCount;
    }

    private void validLv4CegData(DmDimCatgModlCegIctVO dmodlCegIctVO, StringBuilder builder, RelationVO relationVO) {
        if (StrUtil.isBlank(dmodlCegIctVO.getL4CegCnName())) {
            builder.append("模块为空;");
        }
        if (StrUtil.isBlank(dmodlCegIctVO.getL4CegCode())) {
            builder.append("模块编码为空;");
        }
        if (StringUtils.isBlank(dmodlCegIctVO.getL4CegShortCnName())) {
            builder.append("模块简称为空;");
        }
        // 校验模块编码和名称是否匹配
        validL4CodeAndName(dmodlCegIctVO, builder, relationVO);
    }

    private void validL4CodeAndName(DmDimCatgModlCegIctVO dmodlCegIctVO, StringBuilder builder, RelationVO relationVO) {
        List<DmDimCatgModlCegIctVO> dimCegCodeD = relationVO.getDimCegCodeD();
        List<String> codeCollect = relationVO.getL4CodeList();
        List<String> nameCollect = relationVO.getL4NameList();

        if (StrUtil.isNotBlank(dmodlCegIctVO.getL4CegCode())) {
            if (!codeCollect.contains(dmodlCegIctVO.getL4CegCode())) {
                String cegCodeStr = "模块编码(" + dmodlCegIctVO.getL4CegCode() + ")不存在;";
                builder.append(cegCodeStr);
            } else {
                List<DmDimCatgModlCegIctVO> cegCollect = dimCegCodeD.stream()
                    .filter(dto -> dto.getL4CegCode().equals(dmodlCegIctVO.getL4CegCode()))
                    .collect(Collectors.toList());
                if (!cegCollect.get(0).getL4CegCnName().equals(dmodlCegIctVO.getL4CegCnName())) {
                    String cegCodeNameStr = "模块编码(" + dmodlCegIctVO.getL4CegCode() + ")和模块名称(" + dmodlCegIctVO
                        .getL4CegCnName() + ")不匹配;";
                    builder.append(cegCodeNameStr);
                }
            }
        }
        if (StrUtil.isNotBlank(dmodlCegIctVO.getL4CegCnName())) {
            if (!nameCollect.contains(dmodlCegIctVO.getL4CegCnName())) {
                String cegNameStr = "模块名称(" + dmodlCegIctVO.getL4CegCnName() + ")不存在;";
                builder.append(cegNameStr);
            }
        }
    }

    private void validLv3CegData(List<String> combineModlCegList, DmDimCatgModlCegIctVO dmodlCegIctVO,
        StringBuilder builder, String voStr, RelationVO relationVO) {
        if (combineModlCegList.contains(voStr)) {
            builder.append("品类名称为(" + dmodlCegIctVO.getCategoryCnName() + ")的数据在系统已存在;");
        }
        if (StrUtil.isBlank(dmodlCegIctVO.getL3CegCnName())) {
            builder.append("专项采购认证部为空;");
        }
        if (StrUtil.isBlank(dmodlCegIctVO.getL3CegCode())) {
            builder.append("专项采购认证部编码为空;");
        }
        if (StringUtils.isBlank(dmodlCegIctVO.getL3CegShortCnName())) {
            builder.append("专项采购认证部简称为空;");
        }
        // 校验专家团编码和名称是否匹配
        validL3CodeAndName(dmodlCegIctVO, builder, relationVO);
    }

    private void validCategoryCodeWithName(DmDimCatgModlCegIctVO dmodlCegIctVO, StringBuilder builder,
        RelationVO relationVO) {
        if (StrUtil.isBlank(dmodlCegIctVO.getCategoryCode())) {
            builder.append("品类编码为空;");
        }
        if (StrUtil.isBlank(dmodlCegIctVO.getCategoryCnName())) {
            builder.append("品类名称为空;");
        }
        // 校验品类是否存在
        validCategoryCodeWithNameSub(dmodlCegIctVO, builder, relationVO);
    }

    private void validCategoryCodeWithNameSub(DmDimCatgModlCegIctVO dmodlCegIctVO, StringBuilder builder,
        RelationVO relationVO) {
        List<DmDimMaterialCodeVO> dimMaterialCodeD = relationVO.getDimMaterialCodeD();
        List<String> codeCollect = relationVO.getMaterialCodeList();
        List<String> nameCollect = relationVO.getMaterialNameList();

        if (StrUtil.isNotBlank(dmodlCegIctVO.getCategoryCode())) {
            if (!codeCollect.contains(dmodlCegIctVO.getCategoryCode())) {
                String categoryCodeStr = "品类编码(" + dmodlCegIctVO.getCategoryCode() + ")不存在;";
                builder.append(categoryCodeStr);
            } else {
                List<DmDimMaterialCodeVO> materialCollect = dimMaterialCodeD.stream()
                    .filter(dto -> dto.getItemSubtypeCode().equals(dmodlCegIctVO.getCategoryCode()))
                    .collect(Collectors.toList());
                if (!materialCollect.get(0).getItemSubtypeCnName().equals(dmodlCegIctVO.getCategoryCnName())) {
                    String categoryCodeNameStr = "品类编码(" + dmodlCegIctVO.getCategoryCode() + ")和品类名称("
                        + dmodlCegIctVO.getCategoryCnName() + ")不匹配;";
                    builder.append(categoryCodeNameStr);
                }
            }
        }
        if (StrUtil.isNotBlank(dmodlCegIctVO.getCategoryCnName())) {
            if (!nameCollect.contains(dmodlCegIctVO.getCategoryCnName())) {
                String categoryNameStr = "品类名称(" + dmodlCegIctVO.getCategoryCnName() + ")不存在";
                builder.append(categoryNameStr);
            }
        }
    }

    private void validL3CodeAndName(DmDimCatgModlCegIctVO dmodlCegIctVO, StringBuilder builder,
        RelationVO relationVO) {
        List<DmDimCatgModlCegIctVO> dimCegCodeD = relationVO.getDimCegCodeD();
        List<String> codeCollect = relationVO.getL3CodeList();
        List<String> nameCollect = relationVO.getL3NameList();

        if (StrUtil.isNotBlank(dmodlCegIctVO.getL3CegCode())) {
            if (!codeCollect.contains(dmodlCegIctVO.getL3CegCode())) {
                String cegCodeStr = "专项采购认证部编码(" + dmodlCegIctVO.getL3CegCode() + ")不存在;";
                builder.append(cegCodeStr);
            } else {
                List<DmDimCatgModlCegIctVO> cegCollect = dimCegCodeD.stream()
                    .filter(dto -> dto.getL3CegCode().equals(dmodlCegIctVO.getL3CegCode()))
                    .collect(Collectors.toList());
                if (!cegCollect.get(0).getL3CegCnName().equals(dmodlCegIctVO.getL3CegCnName())) {
                    String cegCodeNameStr = "专项采购认证部编码(" + dmodlCegIctVO.getL3CegCode() + ")和专项采购认证部名称(" + dmodlCegIctVO
                        .getL3CegCnName() + ")不匹配;";
                    builder.append(cegCodeNameStr);
                }
            }
        }
        if (StrUtil.isNotBlank(dmodlCegIctVO.getL3CegCnName())) {
            if (!nameCollect.contains(dmodlCegIctVO.getL3CegCnName())) {
                String cegNameStr = "专项采购认证部名称(" + dmodlCegIctVO.getL3CegCnName() + ")不存在;";
                builder.append(cegNameStr);
            }
        }
    }

    private void setNewVersionName(DmFocVersionInfoDTO focPlanVersionVO, String tablePreFix) {
        // 更新版本名称
        focPlanVersionVO.setTablePreFix(tablePreFix);
        String newVersionName = getNewVersionName(focPlanVersionVO);
        focPlanVersionVO.setVersion(newVersionName);
        focPlanVersionVO.setLastUpdatedBy(UserInfoUtils.getUserId());
        focPlanVersionVO.setLastUpdateDate(new Date());
        focPlanVersionVO.setIsRunning(CommonConstant.IS_NOT);
        focPlanVersionVO.setTablePreFix(tablePreFix);
        dmFocVersionDao.updateDmFocVersionDTO(focPlanVersionVO);
    }

    @NotNull
    private String getNewVersionName(DmFocVersionInfoDTO focPlanVersionVO) {
        String oldVersionName = focPlanVersionVO.getVersion();
        String newVersionName = "";
        String dateString = DateUtil.today().replace("-", "");
        if (StrUtil.isNotEmpty(oldVersionName)) {
            String[] split = oldVersionName.split("-");
            if (StrUtil.isNotEmpty(split[0]) && split[0].equals(dateString)) {
                newVersionName = dateString + "-" + split[1];
            } else {
                DmFocVersionInfoDTO versionVo = new DmFocVersionInfoDTO();
                versionVo.setLastUpdateStr(DateUtil.today());
                versionVo.setDataType(IndustryConst.DataType.DIM.getValue());
                versionVo.setTablePreFix(focPlanVersionVO.getTablePreFix());
                List<DmFocVersionInfoDTO> versionVoList = dmFocVersionDao.findDmFocPlanVersionVOList(versionVo);
                if (CollectionUtils.isNotEmpty(versionVoList)) {
                    newVersionName = getVersionNameSub(versionVoList.get(0).getVersion(), dateString);
                } else {
                    newVersionName = dateString + "-001";
                }
            }
        } else {
            newVersionName = dateString + "-001";
        }
        return newVersionName;
    }

    private void combineDimensionForSave(List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList,
        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion, Long versionId,String tablePreFix) {
        List<String> categoryCodeList = dmDimCatgModlListWithVersion.stream()
            .map(ele -> ele.getCategoryCode())
            .collect(Collectors.toList());
        for (DmDimCatgModlCegIctVO modlCegIctVO2 : dimCatgModlCegIctList) {
            if (StrUtil.isNotEmpty(modlCegIctVO2.getOldCategoryCode()) && categoryCodeList.contains(
                modlCegIctVO2.getOldCategoryCode())) {
                // 编辑需要移除旧数据
                dmDimCatgModlListWithVersion.removeIf(model -> model.getCategoryCode().equals(modlCegIctVO2.getCategoryCode()));
            }
        }
        // 查询哪几条数据更新了简称
        List<DmDimCatgModlCegIctVO> allDimCatgModlCegIctList = new ArrayList<>();
        for (DmDimCatgModlCegIctVO dimCatgModlCegIctVO : dimCatgModlCegIctList) {
            // 一个查询可能存在多条数据
            dimCatgModlCegIctVO.setVersionId(versionId);
            dimCatgModlCegIctVO.setTablePreFix(tablePreFix);
            List<DmDimCatgModlCegIctVO> oneDimCatgModlCegIctList = iDmDimCatgModlCegIctDao.findCatgModlCegIctListByShortName(dimCatgModlCegIctVO);
            allDimCatgModlCegIctList.addAll(oneDimCatgModlCegIctList);
        }
        // 去重
        allDimCatgModlCegIctList = allDimCatgModlCegIctList.stream().distinct().collect(Collectors.toList());
        List<DmDimCatgModlCegIctVO> otherList = getDmDimCatgModlCegIctList(dimCatgModlCegIctList, allDimCatgModlCegIctList);
        List<DmDimCatgModlCegIctVO> dmDimListWithVersion = getDmDimListWithVersion(otherList, dmDimCatgModlListWithVersion);
        dimCatgModlCegIctList.addAll(otherList);
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctLists = new ArrayList<>();
        dimCatgModlCegIctList.stream().forEach(cegIctVO -> {
            for (DmDimCatgModlCegIctVO modlCegIctVO : dmDimListWithVersion) {
                if (!modlCegIctVO.getCategoryCode().equals(cegIctVO.getCategoryCode())) {
                    dimCatgModlCegIctLists.add(modlCegIctVO);
                }
            }
        });
        dimCatgModlCegIctList.addAll(dimCatgModlCegIctLists);
        setDimCatgModlVO(dimCatgModlCegIctList, versionId);
    }

    private List<DmDimCatgModlCegIctVO> getDmDimListWithVersion(List<DmDimCatgModlCegIctVO> otherList,
        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion) {
        List<DmDimCatgModlCegIctVO> collect = new ArrayList<>();
        collect = dmDimCatgModlListWithVersion.stream().filter(cegIctVO -> {
            for (DmDimCatgModlCegIctVO ictVO : otherList) {
                if (cegIctVO.getCategoryCode().equals(ictVO.getCategoryCode())) {
                    return false;
                }
            }
            return true;
        }).collect(Collectors.toList());
        return collect;
    }

    @NotNull
    private List<DmDimCatgModlCegIctVO> getDmDimCatgModlCegIctList(List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList, List<DmDimCatgModlCegIctVO> allDimCatgModlCegIctList) {
        List<DmDimCatgModlCegIctVO> otherList = new ArrayList<>();
        // 找到除入参外的list
        for (DmDimCatgModlCegIctVO modlCegIctVO2 : dimCatgModlCegIctList) {
            if (StringUtils.isNotBlank(modlCegIctVO2.getOldCategoryCode())) {
                for (DmDimCatgModlCegIctVO all : allDimCatgModlCegIctList) {
                    if (all.getL3CegCode().equals(modlCegIctVO2.getL3CegCode()) && !all.getCategoryCode().equals(modlCegIctVO2.getOldCategoryCode())) {
                        otherList.add(all);
                    }
                    if (all.getL4CegCode().equals(modlCegIctVO2.getL4CegCode()) && !all.getCategoryCode().equals(modlCegIctVO2.getOldCategoryCode())) {
                        otherList.add(all);
                    }
                }
            }
        }
        return otherList;
    }

    private void checkMoreShortCegName(List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList, StringBuilder stringBuilder) {
        // 专家团名称是否存在多个简称
        checkMoreShortL3CegName(dimCatgModlCegIctList,stringBuilder);
        // 模块名称是否存在多个简称
        checkMoreShortL4CegName(dimCatgModlCegIctList,stringBuilder);
    }

    private void checkMoreShortL3CegName(List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList, StringBuilder stringBuilder) {
        Map<String, List<DmDimCatgModlCegIctVO>> l3CegRepeatList = dimCatgModlCegIctList.stream().map(l3Ceg -> {
            DmDimCatgModlCegIctVO dmDimCatgModlCegIctVO = new DmDimCatgModlCegIctVO();
            dmDimCatgModlCegIctVO.setL3CegCnName(l3Ceg.getL3CegCnName());
            dmDimCatgModlCegIctVO.setL3CegShortCnName(l3Ceg.getL3CegShortCnName());
            return dmDimCatgModlCegIctVO;
        }).distinct().collect(Collectors.groupingBy(DmDimCatgModlCegIctVO::getL3CegCnName));
        List<String> l3CegName = l3CegRepeatList.keySet().stream().filter(key -> l3CegRepeatList.get(key).size() > 1).distinct().collect(Collectors.toList());

        if (l3CegName.size() > 0) {
            for (Map.Entry<String, List<DmDimCatgModlCegIctVO>> l3Entry : l3CegRepeatList.entrySet()) {
                String entryKey = l3Entry.getKey();
                l3CegName.stream().forEach(l3Ceg -> {
                    if (entryKey.equals(l3Ceg)) {
                        List<DmDimCatgModlCegIctVO> l3EntryValue = l3Entry.getValue();
                        for (DmDimCatgModlCegIctVO catgModlCegIctVO : l3EntryValue) {
                            if (!stringBuilder.toString().contains(catgModlCegIctVO.getL3CegCnName())) {
                                stringBuilder.append(catgModlCegIctVO.getL3CegCnName() + " ");
                            }
                        }
                    }
                });
            }
        }
    }

    private void checkMoreShortL4CegName(List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList, StringBuilder stringBuilder) {
        Map<String, List<DmDimCatgModlCegIctVO>> l4CegRepeatList = dimCatgModlCegIctList.stream().map(l4Ceg -> {
            DmDimCatgModlCegIctVO dmDimCatgModlCegIctVO = new DmDimCatgModlCegIctVO();
            dmDimCatgModlCegIctVO.setL4CegCnName(l4Ceg.getL4CegCnName());
            dmDimCatgModlCegIctVO.setL4CegShortCnName(l4Ceg.getL4CegShortCnName());
            return dmDimCatgModlCegIctVO;
        }).distinct().collect(Collectors.groupingBy(DmDimCatgModlCegIctVO::getL4CegCnName));
        List<String> l4CegName = l4CegRepeatList.keySet().stream().filter(key -> l4CegRepeatList.get(key).size() > 1).distinct().collect(Collectors.toList());

        if (l4CegName.size() > 0) {
            for (Map.Entry<String, List<DmDimCatgModlCegIctVO>> l4Entry : l4CegRepeatList.entrySet()) {
                String entryKey = l4Entry.getKey();
                l4CegName.stream().forEach(l4Ceg -> {
                    if (entryKey.equals(l4Ceg)) {
                        List<DmDimCatgModlCegIctVO> l4EntryValue = l4Entry.getValue();
                        for (DmDimCatgModlCegIctVO catgModlCegIctVO : l4EntryValue) {
                            if (!stringBuilder.toString().contains(catgModlCegIctVO.getL4CegCnName())) {
                                stringBuilder.append(catgModlCegIctVO.getL4CegCnName() + " ");
                            }
                        }
                    }
                });
            }
        }
    }

    private void checkDupliceShortCnName(List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList, StringBuilder stringBuilder,List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion) {
        if (CollectionUtils.isNotEmpty(dmDimCatgModlListWithVersion)) {
            // 通过专家团/模块的名称和简称去重
            List<DmDimCatgModlCegIctVO> distinctLv3List = dmDimCatgModlListWithVersion.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                new TreeSet<>(Comparator.comparing(o -> o.getL3CegCnName() + ";" + o.getL3CegShortCnName()))), ArrayList::new));
            List<DmDimCatgModlCegIctVO> distinctLv4List = dmDimCatgModlListWithVersion.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                new TreeSet<>(Comparator.comparing(o -> o.getL4CegCnName() + ";" + o.getL4CegShortCnName()))), ArrayList::new));

            for (DmDimCatgModlCegIctVO  catgModlCegIctParam: dimCatgModlCegIctList) {
                for (DmDimCatgModlCegIctVO catgModlCegIctVO : distinctLv3List) {
                    if (catgModlCegIctParam.getL3CegShortCnName().equals(catgModlCegIctVO.getL3CegShortCnName())) {
                        if (!catgModlCegIctParam.getL3CegCnName().equals(catgModlCegIctVO.getL3CegCnName())) {
                            stringBuilder.append(catgModlCegIctParam.getL3CegCnName() + " ");
                        }
                    }
                }
            }
            for (DmDimCatgModlCegIctVO  catgModlCegIctParam: dimCatgModlCegIctList) {
                for (DmDimCatgModlCegIctVO catgModlCegIctVO : distinctLv4List) {
                    if (catgModlCegIctParam.getL4CegShortCnName().equals(catgModlCegIctVO.getL4CegShortCnName())) {
                        if (!catgModlCegIctParam.getL4CegCnName().equals(catgModlCegIctVO.getL4CegCnName())) {
                            stringBuilder.append(catgModlCegIctParam.getL4CegCnName() + " ");
                        }
                    }
                }
            }
        }
    }

    @NotNull
    private DmFocVersionInfoDTO createVersionVO(String tablePreFix) {
        DmFocVersionInfoDTO focPlanVersionVO = new DmFocVersionInfoDTO();
        focPlanVersionVO.setVersionId(dmFocVersionDao.getVersionKey(tablePreFix));
        focPlanVersionVO.setVersion(gengerateVerionName(tablePreFix));
        focPlanVersionVO.setVersionType(IndustryConst.VersionType.ADJUST.getValue());
        focPlanVersionVO.setStatus(IndustryConst.STATUS.NOT_STATUS.getValue());
        focPlanVersionVO.setDataType(IndustryConst.DataType.DIM.getValue());
        focPlanVersionVO.setCreatedBy(UserInfoUtils.getUserId());
        focPlanVersionVO.setCreationDate(new Date());
        focPlanVersionVO.setLastUpdatedBy(UserInfoUtils.getUserId());
        focPlanVersionVO.setLastUpdateDate(new Date());
        focPlanVersionVO.setDelFlag(CommonConstant.IS_NOT);
        focPlanVersionVO.setIsRunning(CommonConstant.IS_NOT);
        focPlanVersionVO.setTablePreFix(tablePreFix);
        dmFocVersionDao.createDmFocVersionDTO(focPlanVersionVO);
        return focPlanVersionVO;
    }

    private void setDimCatgModlVO(List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion, Long versionId) {
        // 循环集合设置版本信息
        dmDimCatgModlListWithVersion.stream().forEach(dto -> {
            dto.setVersionId(versionId);
            dto.setCreatedBy(UserInfoUtils.getUserId());
            dto.setCreationDate(new Date());
            dto.setLastUpdatedDate(new Date());
            dto.setLastUpdatedBy(UserInfoUtils.getUserId());
            dto.setDelFlag(CommonConstant.IS_NOT);
            dto.setSaveMethod(CommonConstant.SAVE_METHOD);
        });
    }

    private String gengerateVerionName(String tablePreFix) {
        DmFocVersionInfoDTO versionDto = new DmFocVersionInfoDTO();
        versionDto.setLastUpdateStr(DateUtil.today());
        versionDto.setDataType(IndustryConst.DataType.DIM.getValue());
        versionDto.setTablePreFix(tablePreFix);
        List<DmFocVersionInfoDTO> versionMap = dmFocVersionDao.findDmFocPlanVersionVOList(versionDto);
        return getVersionName(versionMap);
    }

    @NotNull
    public String getVersionName(List<DmFocVersionInfoDTO> versionMap) {
        String versionName;
        String dateStr = DateUtil.today().replace("-", "");
        // 查看当前日期有无版本信息
        if (CollectionUtils.isEmpty(versionMap)) {
            versionName = dateStr + "-001";
        } else {
            // 在当前日期上进行递增
            if (StringUtils.isNotEmpty(versionMap.get(0).getVersion())) {
                versionName = getVersionNameSub(versionMap.get(0).getVersion(), dateStr);
            } else {
                versionName = dateStr + "-001";
            }
        }
        return versionName;
    }

    @NotNull
    public String getVersionNameSub(String inputVersionName, String dateStr) {
        String versionName;
        if (inputVersionName.contains("-")) {
            String versionNumber = inputVersionName.substring(inputVersionName.indexOf("-") + 1);
            if (PATTERN.matcher(versionNumber).matches()) {
                if (NumberUtil.parseInt(versionNumber) > 10 || NumberUtil.parseInt(versionNumber) == 9) {
                    versionName = dateStr + "-0" + (NumberUtil.parseInt(versionNumber) + 1);
                } else {
                    versionName = dateStr + "-00" + (NumberUtil.parseInt(versionNumber) + 1);
                }
            } else {
                versionName = dateStr + "-001";
            }
        } else {
            versionName = dateStr + "-001";
        }
        return versionName;
    }

    public DmFoiImpExpRecordVO uploadImportExcel(UploadInfoVO uploadInfoVO, List<Map> dataList, List<HeaderVo> model,
        List<Map> normalList, boolean flag) throws CommonApplicationException, IOException {
        // 上传源文件
        DmFoiImpExpRecordVO record = exportRelationExcel(normalList, uploadInfoVO, model);
        String fileSourceKey = record.getFileSourceKey();

        DmFoiImpExpRecordVO recordVO = new DmFoiImpExpRecordVO();
        if (!flag) {
            model.add(new HeaderVo("错误信息", "errorMsg", CellType.STRING, false, 12 * 480));
            // 上传异常文件
            recordVO = exportRelationExcel(dataList, uploadInfoVO, model);
            String fileErrorKey = recordVO.getFileSourceKey();
            recordVO.setFileErrorKey(fileErrorKey);
        }
        recordVO.setFileSourceKey(fileSourceKey);
        return recordVO;
    }

    private DmFoiImpExpRecordVO exportRelationExcel(List<Map> dataList, UploadInfoVO uploadInfoVO,
        List<HeaderVo> headers) throws CommonApplicationException, IOException {
        String sheetName = uploadInfoVO.getSheetName();
        String version = uploadInfoVO.getVersion();
        List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
        // 获取第一行表单
        List<AbstractExcelTitleVO> formsVO = getFormsVO(version);
        // 设置第二行表头模型
        Set<String> titles = new LinkedHashSet<>();
        setHeader(titleVoList, headers, titles);
        ExportExcelVo exportExcelVo = ExportExcelVo.builder()
            .formInfoVo(formsVO)
            .titleVoList(titleVoList)
            .list(dataList)
            .sheetName(sheetName)
            .mergeCell(false)
            .build();
        List<ExportExcelVo> exportExcelVoList = new ArrayList<>();
        exportExcelVoList.add(exportExcelVo);
        return excelUtil.expSelectColumnExcel(exportExcelVoList, null);
    }

    private void setHeader(List<AbstractExcelTitleVO> titleVoList, List<HeaderVo> headers, Set<String> titles) {
        LeafExcelTitleVO column;
        for (HeaderVo header : headers) {
            column = new LeafExcelTitleVO(header.getTitle(), header.getWidth(), true, header.getField(),
                header.getField(), header.getDataType(), header.getIsEditable());
            titleVoList.add(column);
            titles.add(header.getField());
        }
    }

    /**
     * [服务名称]getFormsVOs 获取清单版本
     *
     * @param versionName 版本名称
     * @return List<AbstractExcelTitleVO>
     * <AUTHOR>
     */
    public List<AbstractExcelTitleVO> getFormsVO(String versionName) {
        List<AbstractExcelTitleVO> formInfoVo = new ArrayList<>();
        LeafExcelTitleVO columnCnVersion = new LeafExcelTitleVO(CommonConstant.DIMENSION_VERSION + versionName,
            CommonConstant.WIDTH, true, "dimensionVersion", "dimensionVersion", CellType.STRING, false);
        formInfoVo.add(columnCnVersion);
        return formInfoVo;
    }

}
