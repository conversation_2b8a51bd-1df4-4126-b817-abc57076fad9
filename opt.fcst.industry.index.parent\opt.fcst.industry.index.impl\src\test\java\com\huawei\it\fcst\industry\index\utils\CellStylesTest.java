/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.utils;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;
import com.huawei.it.fcst.util.CellStyles;

/**
 * CellStylesTest Class
 *
 * <AUTHOR>
 * @since 2023/4/26
 */
public class CellStylesTest extends BaseVOCoverUtilsTest<CellStyles> {

    @Override
    protected Class<CellStyles> getTClass() {
        return CellStyles.class;
    }
}