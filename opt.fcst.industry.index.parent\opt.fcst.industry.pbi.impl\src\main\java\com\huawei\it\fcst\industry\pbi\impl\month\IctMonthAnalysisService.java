/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.month;

import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.huawei.it.fcst.enums.ResultCodeEnum;
import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.constant.Constant;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstIrbDimInfoDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFocVarifyTaskDao;
import com.huawei.it.fcst.industry.pbi.dao.IctMonthCostAmtDao;
import com.huawei.it.fcst.industry.pbi.dao.IctMonthCostIdxDao;
import com.huawei.it.fcst.industry.pbi.dao.IctMonthCostRateDao;
import com.huawei.it.fcst.industry.pbi.dao.IctMonthWeightDao;
import com.huawei.it.fcst.industry.pbi.enums.GroupLevelEnum;
import com.huawei.it.fcst.industry.pbi.enums.IndustryConstEnum;
import com.huawei.it.fcst.industry.pbi.impl.annual.AnnualAmpPbiService;
import com.huawei.it.fcst.industry.pbi.impl.common.IctCommonService;
import com.huawei.it.fcst.industry.pbi.impl.common.IctExecutorConfig;
import com.huawei.it.fcst.industry.pbi.impl.template.MonthAnalysisTemplateEnum;
import com.huawei.it.fcst.industry.pbi.impl.template.MonthMultiSelectTemplateEnum;
import com.huawei.it.fcst.industry.pbi.service.common.IIctCommonService;
import com.huawei.it.fcst.export.IExcelTemplateBeanManager;
import com.huawei.it.fcst.export.IExportProcessorService;
import com.huawei.it.fcst.industry.pbi.service.month.IIctMonthAnalysisService;
import com.huawei.it.fcst.industry.pbi.utils.FcstIndustryUtil;
import com.huawei.it.fcst.industry.pbi.utils.IctAccessTokenClient;
import com.huawei.it.fcst.industry.pbi.utils.RestUtil;
import com.huawei.it.fcst.industry.pbi.vo.common.DmFocVarifyTaskVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstDimInfoVO;
import com.huawei.it.fcst.industry.pbi.vo.month.AmpParamVO;
import com.huawei.it.fcst.industry.pbi.vo.month.Factors;
import com.huawei.it.fcst.industry.pbi.vo.month.IctMonthAnalysisVO;
import com.huawei.it.fcst.industry.pbi.vo.month.PeriodIdDimVO;
import com.huawei.it.fcst.util.ObjectCopyUtil;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.core.util.CollectionUtil;
import com.huawei.it.jalor5.registry.service.IRegistryQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Inject;
import javax.inject.Named;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * IctMonthAnalysisService Class
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Slf4j
@Named("ictMonthAnalysisService")
@JalorResource(code = "ictMonthAnalysisService", desc = "产业成本指数（ICT）月度分析服务")
public class IctMonthAnalysisService implements IIctMonthAnalysisService {

    private static final Logger LOGGER = LoggerFactory.getLogger(IctMonthAnalysisService.class);

    private static final String NEXT_GROUP_LEVEL = "nextGroupLevel";

    private static final String NEXT_GROUP_NAME = "nextGroupName";

    private static final String SUCCESS_FLAG = "SUCCESS";

    private static final String PROCESSING_FLAG = "PROCESSING";

    @Inject
    private IctMonthCostIdxDao ictMonthCostIdxDao;

    @Inject
    private IctMonthCostRateDao ictMonthCostRateDao;

    @Inject
    private IctMonthCostAmtDao ictMonthCostAmtDao;

    @Inject
    private IctMonthWeightDao ictMonthWeightDao;

    @Inject
    private IDmFocVarifyTaskDao varifyTaskDao;

    @Inject
    private IDmFcstIrbDimInfoDao dmFcstIrbDimInfoDao;

    @Inject
    private IIctCommonService commonService;

    @Autowired
    private IctCommonService ictCommonService;

    @Inject
    private AnnualAmpPbiService annualAmpPbiService;

    @Inject
    private AsyncMonthAnalysisService asyncMonthAnalysisService;

    @Inject
    private IExportProcessorService exportProcessorService;

    @Autowired
    private IRegistryQueryService registryQueryService;

    @Autowired
    private IctAccessTokenClient ictAccessTokenClient;

    @Autowired
    private IctExecutorConfig executorConfig;

    private static final String TOP_WEIGHT_LIST = "top10WeightList";


    private Map<String, MonthAnalysisTemplateEnum> templateEnumMap = new HashMap<String, MonthAnalysisTemplateEnum>() {{
        // {{"模板1，显示降成本，显示成本分布，显示子项图"}}
        put("true-true-true", MonthAnalysisTemplateEnum.MONTH_CODE_01);
        // {{"模板2，不显示降成本，显示成本分布，显示子项图"}}
        put("false-true-true", MonthAnalysisTemplateEnum.MONTH_CODE_02);
        // {{"模板3，显示降成本，不显示成本分布，显示子项图"}}
        put("true-false-true", MonthAnalysisTemplateEnum.MONTH_CODE_03);
        // {{"模板4，显示降成本，显示成本分布，不显示子项图"}}
        put("true-true-false", MonthAnalysisTemplateEnum.MONTH_CODE_04);
        // {{"模板5，不显示降成本，不显示成本分布，显示子项图"}}
        put("false-false-true", MonthAnalysisTemplateEnum.MONTH_CODE_05);
        // {{"模板6，不显示降成本，显示成本分布，不显示子项图"}}
        put("false-true-false", MonthAnalysisTemplateEnum.MONTH_CODE_06);
        // {{"模板7，显示降成本，不显示成本分布，不显示子项图"}}
        put("true-false-false", MonthAnalysisTemplateEnum.MONTH_CODE_07);
        // {{"模板8，不显示降成本，不显示成本分布，不显示子项图"}}
        put("false-false-false", MonthAnalysisTemplateEnum.MONTH_CODE_08);
    }};

    private Map<String, MonthMultiSelectTemplateEnum> multiExpTemplateMap = new HashMap<String, MonthMultiSelectTemplateEnum>() {{
        // {{"模板1，显示降成本，显示成本分布，显示子项图"}}
        put("true-true-true", MonthMultiSelectTemplateEnum.MONTH_MULTI_01);
        // {{"模板2，不显示降成本，显示成本分布，显示子项图"}}
        put("false-true-true", MonthMultiSelectTemplateEnum.MONTH_MULTI_02);
        // {{"模板3，显示降成本，不显示成本分布，显示子项图"}}
        put("true-false-true", MonthMultiSelectTemplateEnum.MONTH_MULTI_03);
        // {{"模板4，显示降成本，显示成本分布，不显示子项图"}}
        put("true-true-false", MonthMultiSelectTemplateEnum.MONTH_MULTI_04);
        // {{"模板5，不显示降成本，不显示成本分布，显示子项图"}}
        put("false-false-true", MonthMultiSelectTemplateEnum.MONTH_MULTI_05);
        // {{"模板6，不显示降成本，显示成本分布，不显示子项图"}}
        put("false-true-false", MonthMultiSelectTemplateEnum.MONTH_MULTI_06);
        // {{"模板7，显示降成本，不显示成本分布，不显示子项图"}}
        put("true-false-false", MonthMultiSelectTemplateEnum.MONTH_MULTI_07);
        // {{"模板8，不显示降成本，不显示成本分布，不显示子项图"}}
        put("false-false-false", MonthMultiSelectTemplateEnum.MONTH_MULTI_08);
    }};

    @Override
    @JalorOperation(code = "getMultiBoxList", desc = "ICT产业成本指数（多子项）图正常维度多选下拉框")
    public ResultDataVO getMultiBoxList(IctMonthAnalysisVO searchParamsVO) throws ApplicationException {
        FcstIndustryUtil.checkTablePreFixParam(searchParamsVO);
        // 必填字段校验
        if (verifyParameters(searchParamsVO)) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        // 查询区间年
        setIntervalYear(searchParamsVO);
        searchParamsVO.setVersionId(commonService.getVersionId(IndustryConstEnum.PAGE_TYPE.MONTH.getValue()));
        FcstIndustryUtil.setRegionCode(searchParamsVO);
        // groupLevel是量纲、量纲子类、量纲子类明细和SPART时才需要传 prodRndTeamCodeList
        setProdRndTeamCodeList(searchParamsVO);
        // 正常维度设置值
        searchParamsVO.setParentLevel(searchParamsVO.getGroupLevel());
        searchParamsVO.setParentCodeList(searchParamsVO.getGroupCodeList());
        Map<String, String> groupLevelMap = getGroupLevelMap(searchParamsVO);
        // 正常维度查询
        searchParamsVO.setGroupLevel(groupLevelMap.get(NEXT_GROUP_LEVEL));
        List<IctMonthAnalysisVO> weightVOList = ictMonthWeightDao.findWeightVOList(searchParamsVO);
        return ResultDataVO.success(getVoPagedResult(searchParamsVO, weightVOList));
    }

    @Override
    @JalorOperation(code = "getMultiCombBoxList", desc = "ICT产业成本指数（多子项）图汇总组合多选下拉框")
    public ResultDataVO getMultiCombBoxList(IctMonthAnalysisVO monthAnalysisVO) throws ApplicationException {
        FcstIndustryUtil.checkTablePreFixParam(monthAnalysisVO);
        // 必填字段校验
        if (verifyParameters(monthAnalysisVO)) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        // 查询区间年
        setIntervalYear(monthAnalysisVO);
        monthAnalysisVO.setVersionId(commonService.getVersionId(IndustryConstEnum.PAGE_TYPE.MONTH.getValue()));
        FcstIndustryUtil.setRegionCode(monthAnalysisVO);
        monthAnalysisVO.setParentLevel(monthAnalysisVO.getGroupLevel());
        List<IctMonthAnalysisVO> weightVOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getCombIdList())) {
            combWeightVOList(monthAnalysisVO,weightVOList);
        }
        return ResultDataVO.success(getVoPagedResult(monthAnalysisVO, weightVOList));
    }

    @NotNull
    private PagedResult<IctMonthAnalysisVO> getVoPagedResult(IctMonthAnalysisVO monthAnalysisVO, List<IctMonthAnalysisVO> weightVOList) {
        PageVO pageVO = new PageVO();
        pageVO.setCurPage(monthAnalysisVO.getPageIndex());
        pageVO.setPageSize(monthAnalysisVO.getPageSize());
        int count = weightVOList.size();
        pageVO.setTotalRows(count);
        // 分页
        List<IctMonthAnalysisVO> pageWeightVOList = dropDownBoxPageList(weightVOList, pageVO);
        PagedResult<IctMonthAnalysisVO> combDropDownPageList = new PagedResult<>();
        combDropDownPageList.setResult(pageWeightVOList);
        combDropDownPageList.setPageVO(pageVO);
        return combDropDownPageList;
    }

    @NotNull
    private List<IctMonthAnalysisVO> dropDownBoxPageList(List<IctMonthAnalysisVO> weightVOList, PageVO pageVO) {
        int count = weightVOList.size();
        int curPage = pageVO.getCurPage() - 1;
        int pageSize = pageVO.getPageSize();
        List<IctMonthAnalysisVO> dropDownBoxPageList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(weightVOList) && count > 0) {
            int fromIndex = curPage * pageSize;
            int totalIndex = (curPage + 1) * pageSize;
            if (totalIndex > count) {
                totalIndex = count;
            }
            dropDownBoxPageList = weightVOList.subList(fromIndex, totalIndex);
        }
        return dropDownBoxPageList;
    }

    private void combWeightVOList(IctMonthAnalysisVO monthAnalysisVO, List<IctMonthAnalysisVO> weightList) {
        monthAnalysisVO.setGroupLevel(GroupLevelEnum.SPART.getValue());
        // 第1步：取出当前层级下所有子项的权重并按权重从高到低排列
        weightList.addAll(ictMonthWeightDao.findCombWeightList(monthAnalysisVO));

    }

    @Override
    @JalorOperation(code = "getMultiDropdownList", desc = "ICT产业成本指数（多子项）图多选下拉框")
    public ResultDataVO getMultiDropdownList(IctMonthAnalysisVO monthAnalysisVO) throws ApplicationException {
        log.info("==>Begin IctMonthAnalysisService#getMultiDropdownList");
        FcstIndustryUtil.checkTablePreFixParam(monthAnalysisVO);
        // 必填字段校验 不走虚化逻辑也无需调用此方法
        if (verifyParameters(monthAnalysisVO) || !monthAnalysisVO.getIsNeedBlur()) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        FcstIndustryUtil.checkTablePreFixParam(monthAnalysisVO);
        // 查询区间年
        setIntervalYear(monthAnalysisVO);
        monthAnalysisVO.setVersionId(commonService.getVersionId(IndustryConstEnum.PAGE_TYPE.MONTH.getValue()));
        FcstIndustryUtil.setRegionCode(monthAnalysisVO);
        PageVO pageVO = new PageVO();
        pageVO.setCurPage(monthAnalysisVO.getPageIndex());
        pageVO.setPageSize(monthAnalysisVO.getPageSize());
        if (CommonConstant.MIN_GROUP_LEVEL.contains(monthAnalysisVO.getGroupLevel())) {
            // 走虚化，如果选量纲子类明细或者SPART层级，子层级需要取虚化权重结果表本身层级的数据
            monthAnalysisVO.setSubGroupCodeList(monthAnalysisVO.getGroupCodeList());
            return ResultDataVO.success(ictMonthWeightDao.findBlurMinLevDropdownListByPage(monthAnalysisVO, pageVO));
        }
        // 走虚化，选了量纲和量纲子类，子层级是量纲子类明细，取权重虚化表结果表数据
        monthAnalysisVO.setParentLevel(monthAnalysisVO.getGroupLevel());
        monthAnalysisVO.setParentCodeList(monthAnalysisVO.getGroupCodeList());
        return ResultDataVO.success(ictMonthWeightDao.findBlurWeightVOListByPage(monthAnalysisVO, pageVO));
    }

    private boolean verifyParameters(IctMonthAnalysisVO monthAnalysisVO) {
        return ObjectUtils.isEmpty(monthAnalysisVO.getBasePeriodId())
                || StringUtils.isAnyBlank(monthAnalysisVO.getCostType(), monthAnalysisVO.getGranularityType(),
                monthAnalysisVO.getOverseaFlag(), monthAnalysisVO.getBgCode(), monthAnalysisVO.getRegionCode(),
                monthAnalysisVO.getRepofficeCode());
    }

    /**
     * 查询产业成本价格指数图
     *
     * @param monthAnalysisVoList 参数参数VO
     * @return ResultDataVO result data
     */
    @Override
    @JalorOperation(code = "getIndustryCompareIndexChart", desc = "ICT产业成本对比分析指数图")
    public ResultDataVO getIndustryCompareIndexChart(List<IctMonthAnalysisVO> monthAnalysisVoList) throws InterruptedException {
        LOGGER.info(">>>Begin MonthCommonService::getCompareIndexChart");
        IRequestContext requestContext = RequestContext.getCurrent();
        if (!CollectionUtil.isNullOrEmpty(monthAnalysisVoList) && monthAnalysisVoList.size() > Constant.IntegerEnum.MAX_SIZE.getValue()) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR, "操作的数据过多！");
        }
        // 入参处理重量级团队
        compareIndexParams(monthAnalysisVoList);
        List<IctMonthAnalysisVO> priceIndexVOList = new ArrayList();
        Executor pool = executorConfig.ictAsyncServiceExecutor();
        CountDownLatch countDownLatch = new CountDownLatch(monthAnalysisVoList.size());
        for (IctMonthAnalysisVO monthAnalysisVO : monthAnalysisVoList) {
            Runnable runnable = () -> {
                RequestContextManager.setCurrent(requestContext);
                try {
                    FcstIndustryUtil.checkTablePreFixParam(monthAnalysisVO);
                    if(CommonConstant.PROD_GROUP_LEVEL.contains(monthAnalysisVO.getGroupLevel())) {
                        monthAnalysisVO.setViewFlag(IndustryConstEnum.VIEW_FLAG.PROD_SPART.getValue());
                    }
                    // 切换基期
                    IctMonthAnalysisVO basePeriodIdMonthVO = new IctMonthAnalysisVO();
                    BeanUtils.copyProperties(monthAnalysisVO, basePeriodIdMonthVO);
                    compareChangeBasePeriodId(basePeriodIdMonthVO);
                    // 查询指数图数据
                    ResultDataVO industryCostIndexChart = getIndustryCostIndexChart(monthAnalysisVO);
                    List<IctMonthAnalysisVO> monthCostIdxList = (List<IctMonthAnalysisVO>) industryCostIndexChart.getData();
                    priceIndexVOList.addAll(monthCostIdxList);
                } catch (Exception exception) {
                    LOGGER.error("error getIndustryCompareIndexChart :{} ", exception.getLocalizedMessage());
                } finally {
                    countDownLatch.countDown();
                    RequestContextManager.removeCurrent();
                    LOGGER.info("剩余线程个数：{},当前线程名称：{}", countDownLatch.getCount(), Thread.currentThread().getName());
                }
            };
            pool.execute(runnable);
        }
        countDownLatch.await();
        return ResultDataVO.success(Optional.ofNullable(priceIndexVOList).orElse(new ArrayList<>()));
    }


    private void compareIndexParams(List<IctMonthAnalysisVO> monthAnalysisVolist) {
        // 入参处理重量级团队
        for (IctMonthAnalysisVO monthAnalysisVO : monthAnalysisVolist) {
            if (monthAnalysisVO.getIsContainComb()) {
                monthAnalysisVO.setProdRndTeamCodeList(monthAnalysisVO.getGroupCodeList());
            }
        }
    }

    @Override
    @JalorOperation(code = "getIndustryCostIndexChart", desc = "ICT产业成本指数图查询")
    public ResultDataVO getIndustryCostIndexChart(IctMonthAnalysisVO monthAnalysisVO) throws CommonApplicationException {
        log.info("==>Begin IctMonthAnalysisService#getIndustryCostIndexChart");
        FcstIndustryUtil.checkTablePreFixParam(monthAnalysisVO);
        // 必填字段校验
        if (verifyParameters(monthAnalysisVO)) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        // 查询区间年
        List<String> yearList = annualAmpPbiService.getYearList(monthAnalysisVO.getGranularityType());
        if (CollectionUtils.isNotEmpty(yearList) && yearList.size() >= 2) {
            monthAnalysisVO.setIntervalYear(yearList.get(1) + "+" + yearList.get(0) + "YTD");
        }
        // 设置版本ID和报告期开始时间与结束时间
        setQueryParams(monthAnalysisVO);
        List<IctMonthAnalysisVO> costIndexVOList = new ArrayList<>();
        // 组合
        if (monthAnalysisVO.getIsContainComb()) {
            costIndexVOList = ictMonthCostIdxDao.findDmFcstCombCostIndexVOList(monthAnalysisVO);
        }
        // 正常项
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getGroupCodeList())) {
            normalMonthCostIdxList(monthAnalysisVO, costIndexVOList);
        }
        // 虚化
        if (monthAnalysisVO.getIsNeedBlur()) {
            monthAnalysisVO.setGroupCodeList(null);
            costIndexVOList.addAll(ictMonthCostIdxDao.findBlurCostIndexVOList(monthAnalysisVO));
        }
        return ResultDataVO.success(costIndexVOList);
    }

    private void normalMonthCostIdxList(IctMonthAnalysisVO monthAnalysisVO, List<IctMonthAnalysisVO> costIndexVOList) {
        if (IndustryConstEnum.MAIN_FLAG.Y.getValue().equals(monthAnalysisVO.getMainFlag())) {
            monthAnalysisVO.setAnnualVersionId(commonService.getVersionId(IndustryConstEnum.PAGE_TYPE.ANNUAL.getValue()));
            costIndexVOList.addAll(ictMonthCostIdxDao.findMainFlagCostIndexVOList(monthAnalysisVO));
            List<DmFcstDimInfoVO> lv0Code = dmFcstIrbDimInfoDao.getLv0Code(monthAnalysisVO);
            if (CollectionUtils.isNotEmpty(lv0Code)) {
                costIndexVOList.stream().forEach(costIndexVO -> costIndexVO.setLv0ProdRdTeamCnName(lv0Code.get(0).getLv0ProdRdTeamCnName()));
                costIndexVOList.stream().forEach(costIndexVO -> costIndexVO.setLv0ProdRndTeamCode(lv0Code.get(0).getLv0ProdRndTeamCode()));
            }
        } else {
            costIndexVOList.addAll(ictMonthCostIdxDao.findCostIndexVOList(monthAnalysisVO));
        }
    }

    private void setQueryParams(IctMonthAnalysisVO monthAnalysisVO) {
        monthAnalysisVO.setVersionId(commonService.getVersionId(IndustryConstEnum.PAGE_TYPE.MONTH.getValue()));
        // 检查基期开始时间和结束时间是否传参，若没有，则默认取报告期36个月的开始时间和结束时间
        List<PeriodIdDimVO> startEndTime = ictMonthCostIdxDao.findStartEndTime(monthAnalysisVO.getVersionId());
        if (CollectionUtils.isNotEmpty(startEndTime)) {
            PeriodIdDimVO periodIdDimVO = startEndTime.stream().filter(ele -> ele.getGranularityType().equals(monthAnalysisVO.getGranularityType())).findFirst().orElse(new PeriodIdDimVO());
            Integer endTime = periodIdDimVO.getEndTime();
            FcstIndustryUtil.handlePeriod(monthAnalysisVO, endTime.toString());
        }
        FcstIndustryUtil.setRegionCode(monthAnalysisVO);
    }

    @Override
    @JalorOperation(code = "getIndustryCostIndexMultiChart", desc = "ICT产业成本指数多子项图查询")
    public ResultDataVO getIndustryCostIndexMultiChart(IctMonthAnalysisVO monthAnalysisVO) throws ApplicationException {
        log.info("==>Begin IctMonthAnalysisService#getIndustryCostIndexMultiChart");
        FcstIndustryUtil.checkTablePreFixParam(monthAnalysisVO);
        // 必填字段校验
        if (verifyParameters(monthAnalysisVO)) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        // 设置版本ID和报告期开始时间与结束时间
        setQueryParams(monthAnalysisVO);
        // 设置区间年
        setIntervalYear(monthAnalysisVO);
        // groupLevel是量纲、量纲子类、量纲子类明细和SPART时才需要传 prodRndTeamCodeList
        setProdRndTeamCodeList(monthAnalysisVO);
        monthAnalysisVO.setParentCodeList(monthAnalysisVO.getGroupCodeList());
        monthAnalysisVO.setParentLevel(monthAnalysisVO.getGroupLevel());
        List<IctMonthAnalysisVO> multiCostIndexVOList = new ArrayList<>();
        // 汇总组合
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getCombIdList())) {
            multiCostIndexVOList = ictMonthCostIdxDao.findPriceIndexCombChartByMultiDim(monthAnalysisVO);
        }
        // 虚化
        if (monthAnalysisVO.getIsNeedBlur()) {
            if (CommonConstant.MIN_GROUP_LEVEL.contains(monthAnalysisVO.getGroupLevel())) {
                // 走虚化，如果选量纲子类明细或者SPART层级，子层级查询的是 指数实际结果表 的数据（中间表）
                multiCostIndexVOList.addAll(ictMonthCostIdxDao.findBlurMinMultiCostIndexList(monthAnalysisVO));
            } else {
                // 非最小层级时才需要设置下一层级
                if (!CommonConstant.MIN_GROUP_LEVEL.contains(monthAnalysisVO.getGroupLevel())) {
                    Map<String, String> groupLevelMap = FcstIndustryUtil.getNextGroupLevel(monthAnalysisVO);
                    monthAnalysisVO.setGroupLevel(groupLevelMap.get(NEXT_GROUP_LEVEL));
                }
                // 量纲/量纲子类层级时，子层级查询 虚化指数结果表的数据
                multiCostIndexVOList.addAll(ictMonthCostIdxDao.findBlurMultiCostIndexVOList(monthAnalysisVO));
            }
        }
        // 正常维度
        boolean exportFlag = CollectionUtils.isNotEmpty(monthAnalysisVO.getGroupCodeList()) && monthAnalysisVO.isExportFlag();
        if ((CollectionUtils.isNotEmpty(monthAnalysisVO.getGroupCodeList()) && CollectionUtils.isNotEmpty(monthAnalysisVO.getSubGroupCodeList()))||exportFlag) {
            // 非最小层级时才需要设置下一层级
            if (!CommonConstant.MIN_GROUP_LEVEL.contains(monthAnalysisVO.getGroupLevel())) {
                Map<String, String> groupLevelMap = FcstIndustryUtil.getNextGroupLevel(monthAnalysisVO);
                monthAnalysisVO.setGroupLevel(groupLevelMap.get(NEXT_GROUP_LEVEL));
            }
            // 查询正常指数结果表数据
            multiCostIndexVOList.addAll(ictMonthCostIdxDao.findMultiCostIndexVOList(monthAnalysisVO));
        }
        multiCostIndexVOList = Optional.ofNullable(multiCostIndexVOList).orElse(new ArrayList<>());
        // 加密权重数据
        encryptWeightData(monthAnalysisVO, multiCostIndexVOList);
        List<List<IctMonthAnalysisVO>> dataList = new ArrayList<>();
        multiCostIndexVOList.stream().collect(Collectors.groupingBy(IctMonthAnalysisVO::getGroupCode))
                .entrySet().forEach(entry -> dataList.add(entry.getValue()));
        return ResultDataVO.success(dataList);
    }

    private void setProdRndTeamCodeList(IctMonthAnalysisVO monthAnalysisVO) {
        // groupLevel是量纲、量纲子类、量纲子类明细和SPART时才需要传 prodRndTeamCodeList
        if (!CommonConstant.ENCRYPT_GROUP_LEVEL.contains(monthAnalysisVO.getGroupLevel())) {
            monthAnalysisVO.setProdRndTeamCodeList(null);
        }
    }

    @Override
    @JalorOperation(code = "getIndustryCostYoyAndPopChart", desc = "ICT产业成本指数同环比查询")
    public ResultDataVO getIndustryCostYoyAndPopChart(IctMonthAnalysisVO monthAnalysisVO) throws ApplicationException {
        log.info("==>Begin IctMonthAnalysisService#getIndustryCostYoyAndPopChart");
        FcstIndustryUtil.checkTablePreFixParam(monthAnalysisVO);
        // 必填字段校验
        if (verifyParameters(monthAnalysisVO)) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        // 设置版本ID和报告期开始时间与结束时间
        monthAnalysisVO.setVersionId(commonService.getVersionId(IndustryConstEnum.PAGE_TYPE.MONTH.getValue()));
        // 检查基期开始时间和结束时间是否传参，若没有，则默认取报告期24个月的开始时间和结束时间
        List<PeriodIdDimVO> startEndTime = ictMonthCostIdxDao.findStartEndTime(monthAnalysisVO.getVersionId());
        if (CollectionUtils.isNotEmpty(startEndTime)) {
            PeriodIdDimVO periodIdDimVO = startEndTime.stream().filter(ele -> ele.getGranularityType().equals(monthAnalysisVO.getGranularityType())).findFirst().orElse(new PeriodIdDimVO());
            Integer endTime = periodIdDimVO.getEndTime();
            FcstIndustryUtil.handlePeriodForMonthYoy(monthAnalysisVO, endTime.toString());
        }
        FcstIndustryUtil.setRegionCode(monthAnalysisVO);
        List<IctMonthAnalysisVO> costRateVOList = new ArrayList<>();
        // 组合
        if (monthAnalysisVO.getIsContainComb()) {
            costRateVOList = ictMonthCostRateDao.findDmFcstMonthCombYoyVOList(monthAnalysisVO);
        }
        // 正常
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getGroupCodeList())) {
            costRateVOList.addAll(ictMonthCostRateDao.findCostRateVOList(monthAnalysisVO));
        }
        // 虚化
        if (monthAnalysisVO.getIsNeedBlur()) {
            costRateVOList.addAll(ictMonthCostRateDao.findBlurCostRateVOList(monthAnalysisVO));
        }
        return ResultDataVO.success(costRateVOList);
    }

    @Override
    @JalorOperation(code = "getCostDistributionChart", desc = "ICT成本分布图查询")
    public ResultDataVO getCostDistributionChart(IctMonthAnalysisVO monthAnalysisVO) throws ApplicationException {
        log.info("==>Begin IctMonthAnalysisService#getCostDistributionChart");
        FcstIndustryUtil.checkTablePreFixParam(monthAnalysisVO);
        // 必填字段校验
        if (verifyParameters(monthAnalysisVO)) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        setQueryParams(monthAnalysisVO);
        List<IctMonthAnalysisVO> costAmtVOList = new ArrayList<>();
        // 组合
        if (monthAnalysisVO.getIsContainComb()) {
            costAmtVOList = ictMonthCostAmtDao.findCostCombAmtVOList(monthAnalysisVO);
        }
        // 正常
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getGroupCodeList())) {
            costAmtVOList.addAll(ictMonthCostAmtDao.findCostAmtVOList(monthAnalysisVO));
        }
        // 虚化
        if (monthAnalysisVO.getIsNeedBlur()) {
            costAmtVOList.addAll(ictMonthCostAmtDao.findBlurCostAmtVOList(monthAnalysisVO));
        }
        return ResultDataVO.success(costAmtVOList);
    }

    @JalorOperation(code = "getIndustryCostWeightChart", desc = "ICT产业成本指数权重图查询")
    @Override
    public ResultDataVO getIndustryCostWeightChart(IctMonthAnalysisVO monthAnalysisVO) throws ApplicationException {
        log.info("==>Begin IctMonthAnalysisService#getIndustryCostWeightChart");
        FcstIndustryUtil.checkTablePreFixParam(monthAnalysisVO);
        // 必填字段校验
        if (verifyParameters(monthAnalysisVO)) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        // 查询区间年
        setIntervalYear(monthAnalysisVO);
        monthAnalysisVO.setVersionId(commonService.getVersionId(IndustryConstEnum.PAGE_TYPE.MONTH.getValue()));
        FcstIndustryUtil.setRegionCode(monthAnalysisVO);
        // groupLevel是量纲、量纲子类、量纲子类明细和SPART时才需要传 prodRndTeamCodeList
        setProdRndTeamCodeList(monthAnalysisVO);
        // 正常维度设置值
        monthAnalysisVO.setParentLevel(monthAnalysisVO.getGroupLevel());
        monthAnalysisVO.setParentCodeList(monthAnalysisVO.getGroupCodeList());
        Map<String, String> groupLevelMap = getGroupLevelMap(monthAnalysisVO);
        monthAnalysisVO.setSubCnName(groupLevelMap.get(NEXT_GROUP_NAME));
        List<IctMonthAnalysisVO> weightVOList = new ArrayList<>();
        // 虚化查询
        if (monthAnalysisVO.getIsNeedBlur()) {
            if (CommonConstant.MIN_GROUP_LEVEL.contains(monthAnalysisVO.getGroupLevel())) {
                // 走虚化，如果选量纲子类明细或者SPART层级，子层级需要取虚化权重结果表本身层级的数据
                weightVOList = ictMonthWeightDao.findBlurMinLevWeightVOList(monthAnalysisVO);
            } else {
                // 走虚化，选了量纲和量纲子类，子层级是量纲子类明细，取权重虚化表结果表数据
                monthAnalysisVO.setGroupLevel(groupLevelMap.get(NEXT_GROUP_LEVEL));
                weightVOList = ictMonthWeightDao.findBlurWeightVOList(monthAnalysisVO);
            }
        }
        // 汇总组合查询
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getCombIdList())) {
            combWeightVOList(monthAnalysisVO,weightVOList);
        }
        // 正常维度查询
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getGroupCodeList())) {
            monthAnalysisVO.setGroupLevel(groupLevelMap.get(NEXT_GROUP_LEVEL));
            weightVOList.addAll(ictMonthWeightDao.findWeightVOList(monthAnalysisVO));
        }
        Map<String, Object> dataMap = new HashMap<>();
        // 处理权重图结果
        handleWeightList(monthAnalysisVO, weightVOList, dataMap);
        return ResultDataVO.success(dataMap);
    }

    // 子项按权重从高到低排列，最多显示10个，第11个起合并为其它，“其它”项排在所有子项之后
    private void handleWeightList(IctMonthAnalysisVO monthAnalysisVO, List<IctMonthAnalysisVO> weightVOList, Map<String, Object> dataMap) {
        // 多选
        if (monthAnalysisVO.getIsMultipleSelect()) {
            handleMultiSelectWeightList(monthAnalysisVO, weightVOList, dataMap);
        } else {
            // 单选
            handleSingleSelectWeightList(monthAnalysisVO, weightVOList, dataMap);
        }
    }

    private void handleSingleSelectWeightList(IctMonthAnalysisVO monthAnalysisVO, List<IctMonthAnalysisVO> weightVOList, Map<String, Object> dataMap) {
        // 导出时下载全部数据，不需要合并为其他
        if (monthAnalysisVO.isExportFlag()) {
            // 量纲、量纲子类已经量纲子类明细层级或者SPART层级权重值需要加密处理
            encryptWeightData(monthAnalysisVO, weightVOList);
            dataMap.put(TOP_WEIGHT_LIST, weightVOList);
        } else {
            // 如果权重图个数小于10个 ，则不需要计算其他权重
            if (weightVOList.size() <= 10) {
                // 量纲、量纲子类已经量纲子类明细层级或者SPART层级权重值需要加密处理
                encryptWeightData(monthAnalysisVO, weightVOList);
                dataMap.put(TOP_WEIGHT_LIST, weightVOList);
            } else {
                // 截取TOP10的数据并计算出TOP10权重之和sumWeight
                List<IctMonthAnalysisVO> top10WeightList = weightVOList.stream().limit(10).collect(Collectors.toList());
                double sumWeight = top10WeightList.stream()
                        .collect(Collectors.summarizingDouble(IctMonthAnalysisVO::getWeightRate))
                        .getSum();
                // 第3步：用 1 - sumWeight得到otherWeight其他权重之和 权重用百分号展示
                setOtherWeightValue(top10WeightList, sumWeight);
                // 量纲、量纲子类已经量纲子类明细层级或者SPART层级权重值需要加密处理
                encryptWeightData(monthAnalysisVO, top10WeightList);
                dataMap.put(TOP_WEIGHT_LIST, top10WeightList);
            }
        }
    }

    private void handleMultiSelectWeightList(IctMonthAnalysisVO monthAnalysisVO, List<IctMonthAnalysisVO> weightVOList, Map<String, Object> dataMap) {
        Map<String, List<IctMonthAnalysisVO>> multiTop10WeightList = new HashMap<>();
        if (monthAnalysisVO.getIsNeedBlur()) {
            multiTop10WeightList = weightVOList.stream()
                    .collect(Collectors.groupingBy(IctMonthAnalysisVO::getGroupCnName));
        } else {
            multiTop10WeightList = weightVOList.stream()
                    .collect(Collectors.groupingBy(IctMonthAnalysisVO::getParentCnName));
        }
        for (Map.Entry<String, List<IctMonthAnalysisVO>> multiWeightVOList : multiTop10WeightList.entrySet()) {
            List<IctMonthAnalysisVO> weightList = multiWeightVOList.getValue();
            // 导出时下载全部数据，不需要合并为其他
            if (monthAnalysisVO.isExportFlag()) {
                multiEncryptWeightRate(monthAnalysisVO, weightList);
                dataMap.put(multiWeightVOList.getKey(), weightList);
            } else {
                // 截取TOP10的数据并计算出TOP10权重之和sumWeight
                List<IctMonthAnalysisVO> top10WeightList = weightList.stream().limit(10).collect(Collectors.toList());
                if (weightList.size() <= 10) {
                    multiEncryptWeightRate(monthAnalysisVO, top10WeightList);
                    dataMap.put(multiWeightVOList.getKey(), top10WeightList);
                } else {
                    // 用 1 - sumWeight得到otherWeight其他权重之和 权重用百分号展示
                    double sumWeightMulti = top10WeightList.stream()
                            .collect(Collectors.summarizingDouble(IctMonthAnalysisVO::getWeightRate))
                            .getSum();
                    setOtherWeightValue(top10WeightList, sumWeightMulti);
                    multiEncryptWeightRate(monthAnalysisVO, top10WeightList);
                    dataMap.put(multiWeightVOList.getKey(), top10WeightList);
                }
            }
        }
    }

    private void multiEncryptWeightRate(IctMonthAnalysisVO monthAnalysisVO, List<IctMonthAnalysisVO> top10WeightList) {
        // 虚化场景都需要对权重加密
        if (monthAnalysisVO.getIsNeedBlur()) {
            multiEncryptWeight(top10WeightList);
            setWeightOrder(monthAnalysisVO, top10WeightList);
        } else {
            // 非虚化场景下，子层级的groupLevel是量纲、量纲子类、量纲子类明细以及SPART层级时，数据需要加密
            if (CommonConstant.ENCRYPT_GROUP_LEVEL.contains(monthAnalysisVO.getGroupLevel())) {
                multiEncryptWeight(top10WeightList);
                setWeightOrder(monthAnalysisVO, top10WeightList);
            }
        }
    }

    private void setWeightOrder(IctMonthAnalysisVO monthAnalysisVO, List<IctMonthAnalysisVO> top10WeightList) {
        // 导出时对于虚化的场景，权重图都不展示百分比，按照百分比降序排列展示
        for (int idx = 0; idx < top10WeightList.size(); idx++) {
            IctMonthAnalysisVO ictMonthAnalysisVO = top10WeightList.get(idx);
            if (CommonConstant.ENCRYPT_GROUP_LEVEL.contains(monthAnalysisVO.getGroupLevel())
                    && !GroupLevelEnum.SPART.getValue().equals(monthAnalysisVO.getGroupLevel())) {
                if (monthAnalysisVO.isExportFlag()) {
                    ictMonthAnalysisVO.setGroupCnName(ictMonthAnalysisVO.getGroupCode() + " " + ictMonthAnalysisVO.getGroupCnName());
                }
            }
            ictMonthAnalysisVO.setWeightOrder(String.valueOf(idx + 1));
            ictMonthAnalysisVO.setWeightPercent(String.valueOf(idx + 1));
        }
    }

    private void multiEncryptWeight(List<IctMonthAnalysisVO> top10WeightList) {
        for (IctMonthAnalysisVO dmFocMonthWeightVO : top10WeightList) {
            double sinWeightRate = Math.sin((dmFocMonthWeightVO.getWeightRate()) / 180 * Math.PI);
            dmFocMonthWeightVO.setWeightRateStr(Double.toString(sinWeightRate));
            dmFocMonthWeightVO.setWeightRate(null);
            dmFocMonthWeightVO.setWeightPercent(null);
        }
    }

    private void setOtherWeightValue(List<IctMonthAnalysisVO> top10WeightList, double sumWeight) {
        // 用 1 - sumWeight得到otherWeight其他权重之和 权重用百分号展示
        double otherWeight = (1 - sumWeight) * 100;
        BigDecimal decimal = new BigDecimal(String.valueOf(otherWeight));
        otherWeight = decimal.setScale(1, RoundingMode.HALF_UP).doubleValue();
        IctMonthAnalysisVO otherWeightVO = IctMonthAnalysisVO.builder()
                .groupCode("其他")
                .groupCnName("其他")
                .weightRate(1 - sumWeight)
                .weightPercent(otherWeight + "%")
                .build();
        otherWeightVO.setParentCode(top10WeightList.get(0).getParentCode());
        top10WeightList.add(otherWeightVO);
    }

    private void encryptWeightData(IctMonthAnalysisVO monthAnalysisVO, List<IctMonthAnalysisVO> top10WeightList) {
        // 虚化场景都需要对权重加密
        if (monthAnalysisVO.getIsNeedBlur()) {
            encryptWeight(top10WeightList);
            setWeightOrder(monthAnalysisVO, top10WeightList);
        } else {
            // 非虚化场景下，子层级的groupLevel是量纲、量纲子类、量纲子类明细以及SPART层级时，数据需要加密
            if (CommonConstant.ENCRYPT_GROUP_LEVEL.contains(monthAnalysisVO.getGroupLevel())) {
                encryptWeight(top10WeightList);
                setWeightOrder(monthAnalysisVO, top10WeightList);
            }
        }
    }

    private void encryptWeight(List<IctMonthAnalysisVO> top10WeightList) {
        for (IctMonthAnalysisVO ictMonthAnalysisVO : top10WeightList) {
            if (null != ictMonthAnalysisVO.getWeightRate()) {
                double sinWeightRate = Math.sin((ictMonthAnalysisVO.getWeightRate()) / 180 * Math.PI);
                ictMonthAnalysisVO.setWeightRateStr(Double.toString(sinWeightRate));
            }
            ictMonthAnalysisVO.setWeightRate(null);
            ictMonthAnalysisVO.setWeightPercent(null);
        }
    }

    @Override
    @JalorOperation(code = "getCostTargetCompareChart", desc = "ICT产业成本指数降成本目标对比查询")
    public ResultDataVO getCostTargetCompareChart(IctMonthAnalysisVO monthAnalysisVO) throws ApplicationException {
        log.info("==>Begin IctMonthAnalysisService#getCostTargetCompareChart");
        FcstIndustryUtil.checkTablePreFixParam(monthAnalysisVO);
        // 注意：降成本目标结果仅在标准成本下（重量级团队目录）展示
        if (!IndustryConstEnum.GRANULARITY_TYPE.IRB.getValue().equals(monthAnalysisVO.getGranularityType())) {
            return ResultDataVO.success();
        }
        // 必填字段校验
        if (verifyParameters(monthAnalysisVO)) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        // 设置版本ID和报告期开始时间与结束时间
        setQueryParams(monthAnalysisVO);
        Map<String, List<IctMonthAnalysisVO>> resultData = new HashMap<>();
        // 1、查询出所有的多选主体 PSP成本没有降成本目标，只有分月指数和YTD指数
        List<IctMonthAnalysisVO> reduceCostTargetList = IndustryConstEnum.COST_TYPE.PSP.getValue().equals(monthAnalysisVO.getCostType())
                ? findReduceCostTargetList(monthAnalysisVO)
                : getReduceCostTargetList(monthAnalysisVO);
        // 2、先根据降成本目标主体来分组，然后再根据选择的主体groupCode来分组
        Map<String, Map<String, List<IctMonthAnalysisVO>>> collect = Optional.ofNullable(reduceCostTargetList).orElse(new ArrayList<>())
                .stream().collect(Collectors.groupingBy(vo -> StringUtils.defaultIfBlank(vo.getCostReductionCnName(),"noTarget"),
                        Collectors.groupingBy(IctMonthAnalysisVO::getGroupCode)));
        // 处理结果数据，将同一目标主体的放在一块
        for (Map.Entry<String, Map<String, List<IctMonthAnalysisVO>>> entry : collect.entrySet()) {
            List<IctMonthAnalysisVO> reduceCostList = entry.getValue().values().stream()
                    .flatMap(Collection::stream).collect(Collectors.toList());
            putReduceCostData(resultData, reduceCostList, entry.getKey(), monthAnalysisVO);
        }
        return ResultDataVO.success(resultData);
    }

    @Nullable
    private List<IctMonthAnalysisVO> getReduceCostTargetList(IctMonthAnalysisVO paramsVO) {
        // 用户选择重量级团队主体颗粒度为LV1时, 降成本目标为LV1, LV0层级时没有降成本目标
        if (Arrays.asList(GroupLevelEnum.LV0.getValue(), GroupLevelEnum.LV1.getValue()).contains(paramsVO.getGroupLevel())) {
            return findReduceCostTargetList(paramsVO);
        }
        List<IctMonthAnalysisVO> reduceCostTargetList = new ArrayList<>();
        // 用户选择重量级团队主体颗粒度为LV2/LV3/LV3.5/量纲维度/Spart时, 降成本目标优先对应LV2，若无则取上层级LV1
        List<IctMonthAnalysisVO> redCostTargetList = findReduceCostTargetList(paramsVO);
        redCostTargetList = Optional.ofNullable(redCostTargetList).orElse(new ArrayList<>());
        // 先找LV2的降成本目标
        List<IctMonthAnalysisVO> lv2Collect = redCostTargetList.stream()
                .filter(item -> GroupLevelEnum.LV2.getValue().equals(item.getCostReductionLevel()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(lv2Collect)) {
            reduceCostTargetList.addAll(lv2Collect);
        }
        Set<String> lv2CodeSet = Optional.ofNullable(lv2Collect).orElse(new ArrayList<>())
                .stream().map(IctMonthAnalysisVO::getGroupCode).collect(Collectors.toSet());
        // 排除LV2的编码后再找LV1的降成本目标
        List<IctMonthAnalysisVO> lv1Collect = redCostTargetList.stream()
                .filter(item -> !lv2CodeSet.contains(item.getGroupCode())
                        && GroupLevelEnum.LV1.getValue().equals(item.getCostReductionLevel()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(lv1Collect)) {
            reduceCostTargetList.addAll(lv1Collect);
        }
        Set<String> lv1CodeSet = Optional.ofNullable(lv1Collect).orElse(new ArrayList<>())
                .stream().map(IctMonthAnalysisVO::getGroupCode).collect(Collectors.toSet());
        // 再找出没有降成本目标的数据
        List<IctMonthAnalysisVO> collect = redCostTargetList.stream()
                .filter(item -> !lv2CodeSet.contains(item.getGroupCode()) && !lv1CodeSet.contains(item.getGroupCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            reduceCostTargetList.addAll(collect);
        }
        return reduceCostTargetList;
    }

    @Nullable
    private List<IctMonthAnalysisVO> findReduceCostTargetList(IctMonthAnalysisVO paramsVO) {
        List<IctMonthAnalysisVO> reduceCostTargetList = new ArrayList<>();
        // 正常维度
        if (CollectionUtils.isNotEmpty(paramsVO.getGroupCodeList())) {
            reduceCostTargetList.addAll(ictMonthCostIdxDao.findReduceCostTargetList(paramsVO));
        }
        // 虚化
        if (CollectionUtils.isNotEmpty(paramsVO.getCustomIdList())) {
            reduceCostTargetList.addAll(ictMonthCostIdxDao.findBlurReduceCostTargetList(paramsVO));
        }
        // 组合
        if (CollectionUtils.isNotEmpty(paramsVO.getCombIdList())) {
            reduceCostTargetList.addAll(ictMonthCostIdxDao.findCombReduceCostTargetList(paramsVO));
        }
        return reduceCostTargetList;
    }

    private void putReduceCostData(Map<String, List<IctMonthAnalysisVO>> resultData,
                                   List<IctMonthAnalysisVO> reduceCostTargetList, String costReductionCnName, IctMonthAnalysisVO monthAnalysisVO) {
        if (CollectionUtils.isEmpty(reduceCostTargetList)) {
            return;
        }
        String costType = monthAnalysisVO.getCostType();
        if ((IndustryConstEnum.COST_TYPE.PSP.getValue().equals(costType) && "noTarget".equals(costReductionCnName))||IndustryConstEnum.COST_TYPE.STD.getValue().equals(costType)) {
            if (resultData.containsKey(costReductionCnName)) {
                // 同一个降成本目标主体放一块
                resultData.get(costReductionCnName).addAll(reduceCostTargetList);
            } else {
                resultData.put(costReductionCnName, reduceCostTargetList);
            }
        }
    }

    @Override
    @JalorOperation(code = "switchBasePeriodId", desc = "ICT产业成本指数月度分析切换基期")
    public ResultDataVO switchBasePeriodId(IctMonthAnalysisVO monthAnalysisVO) throws ApplicationException {
        log.info("==>Begin IctMonthAnalysisService#switchBasePeriodId");
        FcstIndustryUtil.checkTablePreFixParam(monthAnalysisVO);
        // 必填字段校验
        if (verifyParameters(monthAnalysisVO)) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        // 切换基期STD的表默认都是硬件
        if (IndustryConstEnum.COST_TYPE.STD.getValue().equals(monthAnalysisVO.getCostType())) {
            monthAnalysisVO.setSoftwareMark(IndustryConstEnum.SOFTWARE_MARK.HARDWARE.getValue());
        }
        // 设置版本ID和报告期开始时间与结束时间
        setQueryParams(monthAnalysisVO);
        // 任务状态初始化设置
        DmFocVarifyTaskVO varifyTaskVO = new DmFocVarifyTaskVO();
        varifyTaskVO.setTaskType("ict_data_index");
        // 正常维度判断是否需要切换基期
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getGroupCodeList())) {
            if (isDataIsOk(monthAnalysisVO)) {
                varifyTaskVO.setStatus(SUCCESS_FLAG);
            } else {
                IctMonthAnalysisVO noramlAnalysisVO = new IctMonthAnalysisVO();
                BeanUtils.copyProperties(monthAnalysisVO, noramlAnalysisVO);
                List<Long> list = new ArrayList<>();
                noramlAnalysisVO.setCombIdList(list);
                varifyTaskVO.setStatus(PROCESSING_FLAG);
                refreshDataByFunction(new JSONObject(noramlAnalysisVO).toString(), varifyTaskVO, "normal");
            }
        }
        // 汇总组合判断是否需要切换基期
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getCombIdList())) {
            if (isCombDataIsOk(monthAnalysisVO)) {
                varifyTaskVO.setCombStatus(SUCCESS_FLAG);
            } else {
                IctMonthAnalysisVO combAnalysisVO = new IctMonthAnalysisVO();
                BeanUtils.copyProperties(monthAnalysisVO, combAnalysisVO);
                List<String> list = new ArrayList<>();
                combAnalysisVO.setProdRndTeamCodeList(list);
                combAnalysisVO.setGroupCodeList(list);
                combAnalysisVO.setParentCodeList(list);
                varifyTaskVO.setCombStatus(PROCESSING_FLAG);
                refreshDataByFunction(new JSONObject(combAnalysisVO).toString(), varifyTaskVO, "comb");
            }
        }
        return ResultDataVO.success(varifyTaskVO);
    }

    /**
     * 判断汇总组合数据是否准备好
     *
     * @param monthAnalysisVO 查询参数
     * @return boolean true or false
     */
    public boolean isCombDataIsOk(IctMonthAnalysisVO monthAnalysisVO) {
        // 默认基期不切换
        if (ifBasePeriodId(monthAnalysisVO)) {
            return true;
        }
        // 单选的切换基期数据量设置
        int monthCount = getMonthCount(monthAnalysisVO);
        // 多选的切换基期数据量设置
        if (monthAnalysisVO.getIsMultipleSelect() && CollectionUtils.isNotEmpty(monthAnalysisVO.getCombIdList())) {
            monthCount = monthCount * (monthAnalysisVO.getCombIdList().size());
        }
        int costIndexCount = ictMonthCostIdxDao.findCombCostIndexCount(monthAnalysisVO);
        return costIndexCount >= monthCount;
    }

    /**
     * 判断数据是否准备好
     *
     * @param monthAnalysisVO 查询参数
     * @return boolean true or false
     */
    public boolean isDataIsOk(IctMonthAnalysisVO monthAnalysisVO) {
        // 默认基期不切换
        if (ifBasePeriodId(monthAnalysisVO)) {
            return true;
        }
        // 单选的切换基期数据量设置
        int monthCount = getMonthCount(monthAnalysisVO);
        // 多选的切换基期数据量设置
        if (CollectionUtils.isEmpty(monthAnalysisVO.getSubGroupCodeList())) {
            if (monthAnalysisVO.getIsMultipleSelect() && CollectionUtils.isNotEmpty(monthAnalysisVO.getGroupCodeList())) {
                monthCount = monthCount * (monthAnalysisVO.getGroupCodeList().size());
            }
        }
        int costIndexCount = ictMonthCostIdxDao.findCostIndexCount(monthAnalysisVO);
        // 多子项目的数据量检查
        int multiCostIndexCount = getMultiCostIndexCount(monthAnalysisVO);
        if (costIndexCount == 0 || multiCostIndexCount == 0) {
            return false;
        }
        return costIndexCount >= monthCount;
    }

    private int getMultiCostIndexCount (IctMonthAnalysisVO monthAnalysisVO) {
        IctMonthAnalysisVO paramsVO = ObjectCopyUtil.copy(monthAnalysisVO, IctMonthAnalysisVO.class);
        // groupLevel是量纲、量纲子类、量纲子类明细和SPART时才需要传 prodRndTeamCodeList
        setProdRndTeamCodeList(paramsVO);
        paramsVO.setParentCodeList(paramsVO.getGroupCodeList());
        paramsVO.setParentLevel(paramsVO.getGroupLevel());
        // 非最小层级时才需要设置下一层级
        if (!CommonConstant.MIN_GROUP_LEVEL.contains(paramsVO.getGroupLevel())) {
            Map<String, String> groupLevelMap = FcstIndustryUtil.getNextGroupLevel(paramsVO);
            paramsVO.setGroupLevel(groupLevelMap.get(NEXT_GROUP_LEVEL));
        }
        // 查询正常指数结果表数据
        return ictMonthCostIdxDao.findMultiCostIndexCount(paramsVO);
    }

    private int getMonthCount(IctMonthAnalysisVO monthAnalysisVO) {
        int result = 0;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        Calendar from = Calendar.getInstance();
        Calendar to = Calendar.getInstance();
        try {
            from.setTime(sdf.parse(String.valueOf(monthAnalysisVO.getPeriodStartTime())));
            to.setTime(sdf.parse(String.valueOf(monthAnalysisVO.getPeriodEndTime())));
            int fromYear = from.get(Calendar.YEAR);
            int toYear = to.get(Calendar.YEAR);
            int fromMonth = from.get(Calendar.MONTH);
            int toMonth = to.get(Calendar.MONTH);
            result = (toYear - fromYear) * 12 + toMonth - fromMonth + 1 ;
        } catch (ParseException ex) {
            log.error("error getMonthSpace :{} ", ex.getLocalizedMessage());
        }
        return result;
    }

    private boolean ifBasePeriodId(IctMonthAnalysisVO monthAnalysisVO) {
        String basePeriodId = ictMonthCostIdxDao.findBasePeriodId(monthAnalysisVO);
        return basePeriodId.equals(String.valueOf(monthAnalysisVO.getBasePeriodId()));
    }

    /**
     * 调用切换基期函数刷新数据
     *
     * @param jsonStr json string
     * @param dmFocVarifyTaskVO
     */
    @Override
    public void refreshDataByFunction(String jsonStr, DmFocVarifyTaskVO dmFocVarifyTaskVO, String flag) {
        if (null == dmFocVarifyTaskVO.getTaskId()) {
            dmFocVarifyTaskVO.setTaskId(varifyTaskDao.getVerifyTaskId());
            dmFocVarifyTaskVO.setPeriodId(new JSONObject(jsonStr).getInt("basePeriodId"));
            varifyTaskDao.insertVerifyTask(dmFocVarifyTaskVO);
        } else {
            varifyTaskDao.updateVerifyTask(dmFocVarifyTaskVO);
        }
        // 异步调用函数
        if ("normal".equals(flag)) {
            asyncMonthAnalysisService.refreshDataByFunction(jsonStr, dmFocVarifyTaskVO);
        } else {
            asyncMonthAnalysisService.refreshCombDataByFunction(jsonStr, dmFocVarifyTaskVO);
        }
    }

    /**
     * 对比分析切换基期
     *
     * @param monthAnalysisVO
     */
    public  void compareChangeBasePeriodId(IctMonthAnalysisVO monthAnalysisVO) throws CommonApplicationException {
        // 设置版本ID和报告期开始时间与结束时间
        setQueryParams(monthAnalysisVO);
        if (monthAnalysisVO.getIsContainComb() && CollectionUtils.isNotEmpty(monthAnalysisVO.getCombIdList())) {
            // 汇总组合调用切换基期函数
            IctMonthAnalysisVO buildComb = new IctMonthAnalysisVO();
            BeanUtils.copyProperties(monthAnalysisVO, buildComb);
            changeCombPeriodIdFlag(buildComb);
        } else {
            // 不包含汇总组合的切换基期任务判断
            changePeriodIdFlag(monthAnalysisVO);
        }
    }

    @Nullable
    private void changePeriodIdFlag(IctMonthAnalysisVO monthAnalysisVO) throws CommonApplicationException {
        // 判断是否需要切换基期
        if (isDataIsOk(monthAnalysisVO)) {
            return;
        }
        // 调用函数
        compareRefreshIndustryIndexData(monthAnalysisVO);
    }

    @Nullable
    private void changeCombPeriodIdFlag(IctMonthAnalysisVO monthAnalysisVO) throws CommonApplicationException {
        // 判断是否需要切换基期
        if (isCombDataIsOk(monthAnalysisVO)) {
            return;
        }
        IctMonthAnalysisVO combAnalysisVO = new IctMonthAnalysisVO();
        BeanUtils.copyProperties(monthAnalysisVO, combAnalysisVO);
        List<String> list = new ArrayList<>();
        combAnalysisVO.setProdRndTeamCodeList(list);
        combAnalysisVO.setGroupCodeList(list);
        combAnalysisVO.setParentCodeList(list);
        // 调用函数
        compareRefreshIndustryIndexData(combAnalysisVO);
    }

    /**
     * 对比分析指数图切换基期
     * @param searchMonthVO
     */
    public void compareRefreshIndustryIndexData(IctMonthAnalysisVO searchMonthVO) throws CommonApplicationException {
        LOGGER.info(">>>searchMonthVO:{}", JSON.toJSONString(searchMonthVO));
        IctMonthAnalysisVO monthAnalysisVO = ObjectCopyUtil.copy(searchMonthVO, IctMonthAnalysisVO.class);
        // 调用切换基期函数刷新数据
        monthAnalysisVO.setPageType(IndustryConstEnum.PAGE_TYPE.MONTH.getValue());
        String successFlag = ictMonthCostIdxDao.callFuncRefreshData(new JSONObject(monthAnalysisVO).toString());
        if (!SUCCESS_FLAG.equals(successFlag)) {
            throw new CommonApplicationException("对比分析切换基期函数调用失败");
        }
        LOGGER.info(">>>compareRefreshIndustryIndexData:{}", successFlag);
    }

    @Override
    @JalorOperation(code = "getCauseAmpChartList", desc = "ICT产业成本指数月度分析查询涨跌根因分析")
    public ResultDataVO getCauseAmpChartList(IctMonthAnalysisVO monthAnalysisVO, HttpServletRequest request) throws ApplicationException {
        FcstIndustryUtil.checkTablePreFixParam(monthAnalysisVO);
        // 必填字段校验
        if (verifyParameters(monthAnalysisVO)) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        // 设置版本ID和报告期开始时间与结束时间
        setQueryParams(monthAnalysisVO);
        // 依据条件查询数据
        Iterator<Map.Entry<String, List<IctMonthAnalysisVO>>> iterator = getMultiCostIndexIterator(monthAnalysisVO);
        String rules = "";
        AmpParamVO paramVO = new AmpParamVO();
        List<Factors> factorsList = new ArrayList<>();
        Integer length = 0;
        while (iterator.hasNext()) {
            Map.Entry<String, List<IctMonthAnalysisVO>> entryNext = iterator.next();
            List<IctMonthAnalysisVO> monthCostIdxList = entryNext.getValue();

            List<Double> weightRateSet = monthCostIdxList.stream().map(IctMonthAnalysisVO::getWeightRate).collect(Collectors.toList());
            Factors factors = new Factors();
            factors.setCode(monthCostIdxList.get(0).getGroupCode());
            Object[] costIdxArray = monthCostIdxList.stream().map(IctMonthAnalysisVO::getCostIndex).collect(Collectors.toList()).toArray();
            length = length + costIdxArray.length;
            Long time = System.nanoTime();
            factors.setId("__" + time + "__");
            if (GroupLevelEnum.SPART.getValue().equals(monthAnalysisVO.getGroupLevel())) {
                factors.setName(monthCostIdxList.get(0).getGroupCode());
            } else {
                factors.setName(monthCostIdxList.get(0).getGroupCnName());
            }
            factors.setValue(costIdxArray);
            factorsList.add(factors);
            if (!iterator.hasNext()) {
                rules = rules + "__" + time + "__*" + weightRateSet.get(0);
            } else {
                rules = rules + "__" + time + "__*" + weightRateSet.get(0) + "+";
            }
        }
        rules = "=" + rules;
        paramVO.setFactors(factorsList);
        paramVO.setRules(rules);
        String jsonParams = JSON.toJSONString(paramVO);
        LOGGER.info("产业请求涨跌根因分析参数:{}",jsonParams);
        if (factorsList.size() *2 != length) {
            LOGGER.info("指数数据不匹配,无法根因分析");
            return ResultDataVO.success();
        }
        LOGGER.info("产业开始调用python请求涨跌根因分析");
        String requestUrl = registryQueryService.findValueByPath(Constant.StrEnum.QUERY_EXPLAIN_CHART_URL.getValue(), true);
        String result = RestUtil.doPostByAuthorization(requestUrl, ictAccessTokenClient.accessToken(), jsonParams, request);
        LOGGER.info("产业根因分析结束调用");
        return com.alibaba.fastjson.JSONObject.parseObject(result, ResultDataVO.class);
    }

    @NotNull
    private Iterator<Map.Entry<String, List<IctMonthAnalysisVO>>> getMultiCostIndexIterator(IctMonthAnalysisVO monthAnalysisVO) {
        setIntervalYear(monthAnalysisVO);
        // groupLevel是量纲、量纲子类、量纲子类明细和SPART时才需要传 prodRndTeamCodeList
        setProdRndTeamCodeList(monthAnalysisVO);
        monthAnalysisVO.setParentCodeList(monthAnalysisVO.getGroupCodeList());
        monthAnalysisVO.setParentLevel(monthAnalysisVO.getGroupLevel());
        // 查询指数和权重
        List<IctMonthAnalysisVO> multiCostIndexVOList;
        // 非最小层级时才需要设置下一层级
        if (!CommonConstant.MIN_GROUP_LEVEL.contains(monthAnalysisVO.getGroupLevel())) {
            Map<String, String> groupLevelMap = FcstIndustryUtil.getNextGroupLevel(monthAnalysisVO);
            monthAnalysisVO.setGroupLevel(groupLevelMap.get(NEXT_GROUP_LEVEL));
        }
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getCustomIdList())) {
            // 量纲/量纲子类层级时，子层级查询 虚化指数结果表的数据
            multiCostIndexVOList = ictMonthCostIdxDao.findBlurMultiCostIndexVOList(monthAnalysisVO);
        } else {
            // 查询正常指数结果表数据
            multiCostIndexVOList = ictMonthCostIdxDao.findMultiCostIndexVOList(monthAnalysisVO);
        }
        Map<String, List<IctMonthAnalysisVO>> priceIndexChartMap = multiCostIndexVOList.stream()
                .collect(Collectors.groupingBy(item ->  item.getProdRndTeamCode() + "_" + item.getGroupCode(), HashMap::new, Collectors.toList()));
        return priceIndexChartMap.entrySet().iterator();
    }

    private void setIntervalYear(IctMonthAnalysisVO monthAnalysisVO) {
        // 设置区间年
        List<String> yearList = annualAmpPbiService.getYearList(monthAnalysisVO.getGranularityType());
        if (CollectionUtils.isNotEmpty(yearList) && yearList.size() >= 2) {
            monthAnalysisVO.setIntervalYear(yearList.get(1) + "-" + yearList.get(0));
        }
    }

    @Override
    @JalorOperation(code = "detailDataExport", desc = "ICT产业成本指数月度分析数据下载")
    @Audit(module = "ictMonthAnalysisService-detailDataExport", operation = "detailDataExport", message = "ICT产业成本指数月度分析数据下载")
    public ResultDataVO detailDataExport(IctMonthAnalysisVO monthAnalysisVO, HttpServletResponse response)
            throws ApplicationException {
        log.info("==>Begin IctMonthAnalysisService#detailDataExport");
        FcstIndustryUtil.checkTablePreFixParam(monthAnalysisVO);
        // 设置导出标识
        monthAnalysisVO.setExportFlag(true);
        // 获取导出模板
        IExcelTemplateBeanManager monthAnalysisTemplate = getMonthAnalysisTemplateEnum(monthAnalysisVO);
        // 设置标题
        ictCommonService.setExcelDisplayName(monthAnalysisVO);
        Map<String, String> groupLevelMap = getGroupLevelMap(monthAnalysisVO);
        monthAnalysisVO.setGroupCnName(GroupLevelEnum.getInstance(monthAnalysisVO.getGroupLevel()).getName());
        monthAnalysisVO.setNextGroupLevel(groupLevelMap.get(NEXT_GROUP_LEVEL));
        if (GroupLevelEnum.SPART.getValue().equals(monthAnalysisVO.getNextGroupLevel())) {
            monthAnalysisVO.setSubCnName(groupLevelMap.get(NEXT_GROUP_NAME) + CommonConstant.EXP_CODE);
        } else {
            monthAnalysisVO.setSubCnName(groupLevelMap.get(NEXT_GROUP_NAME) + CommonConstant.EXP_NAME);
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("exportFileName", "月度分析-明细数据_" + System.currentTimeMillis());
        paramMap.put("exportModuleName", "成本指数-ICT-月度分析");
        exportProcessorService.fillEasyExcelExport(response, monthAnalysisTemplate, monthAnalysisVO, paramMap);
        return ResultDataVO.success();
    }

    /**
     * 获取层级映射
     *
     * @param monthAnalysisVO param VO
     * @return map
     */
    @NotNull
    private Map<String, String> getGroupLevelMap(IctMonthAnalysisVO monthAnalysisVO) {
        Map<String, String> groupLevelMap = new HashMap<>();
        // 设置虚化时的子层级名称
        if (monthAnalysisVO.getIsNeedBlur()) {
            groupLevelMap = FcstIndustryUtil.getBlurNextGroupLevel(monthAnalysisVO);
        } else {
            // 非最小层级时才有下一层级的名称
            if (!CommonConstant.MIN_GROUP_LEVEL.contains(monthAnalysisVO.getGroupLevel())) {
                groupLevelMap = FcstIndustryUtil.getNextGroupLevel(monthAnalysisVO);
            }
        }
        return groupLevelMap;
    }

    @NotNull
    private IExcelTemplateBeanManager getMonthAnalysisTemplateEnum(IctMonthAnalysisVO monthAnalysisVO) {
        // 降成本目标对比图 只有在PBI目录树为重量级团队才展示
        boolean isIrbFlag = IndustryConstEnum.GRANULARITY_TYPE.IRB.getValue().equals(monthAnalysisVO.getGranularityType());
        // 成本分布图 只有在 isShowCostDistribution 为 true 时才展示
        boolean showCostDistribution = monthAnalysisVO.getIsShowCostDistribution() == null ? false
                : monthAnalysisVO.getIsShowCostDistribution();
        // 权重图、多子项指数图 只有在 isShowChildrenChart 为 true 时才展示
        boolean showChildrenChart = monthAnalysisVO.getIsShowChildrenChart() == null ? false
                : monthAnalysisVO.getIsShowChildrenChart();
        String key = String.valueOf(isIrbFlag).concat("-").concat(String.valueOf(showCostDistribution))
                .concat("-").concat(String.valueOf(showChildrenChart));
        log.info(">>> Map Key is : {}", key);
        return monthAnalysisVO.getIsMultipleSelect() ? multiExpTemplateMap.get(key) : templateEnumMap.get(key);
    }

}