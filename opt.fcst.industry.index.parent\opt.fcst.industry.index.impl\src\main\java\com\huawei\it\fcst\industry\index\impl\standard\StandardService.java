/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.standard;

import com.alibaba.fastjson.JSON;
import com.huawei.it.fcst.industry.index.constant.CommonConstant;
import com.huawei.it.fcst.industry.index.constant.Constant;
import com.huawei.it.fcst.industry.index.dao.IDmFocVersionDao;
import com.huawei.it.fcst.industry.index.dao.IStandardDao;
import com.huawei.it.fcst.industry.index.enums.GroupLevelAllEnum;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumU;
import com.huawei.it.fcst.industry.index.enums.IndustryConst;
import com.huawei.it.fcst.industry.index.enums.IndustryIndexEnum;
import com.huawei.it.fcst.industry.index.enums.ResultCodeEnum;
import com.huawei.it.fcst.industry.index.impl.annual.StatisticsExcelService;
import com.huawei.it.fcst.industry.index.impl.common.CommonService;
import com.huawei.it.fcst.industry.index.impl.common.ReplaceDropDownService;
import com.huawei.it.fcst.industry.index.impl.month.AsyncQueryService;
import com.huawei.it.fcst.industry.index.impl.replace.AsyncReplaceExportService;
import com.huawei.it.fcst.industry.index.impl.replace.CommonAmpService;
import com.huawei.it.fcst.industry.index.service.standard.IStandardService;
import com.huawei.it.fcst.industry.index.utils.ExcelExportUtil;
import com.huawei.it.fcst.industry.index.utils.FcstIndexUtil;
import com.huawei.it.fcst.industry.index.utils.ObjectCopyUtil;
import com.huawei.it.fcst.industry.index.utils.UserInfoUtils;
import com.huawei.it.fcst.industry.index.vo.annual.CommonAnnualVO;
import com.huawei.it.fcst.industry.index.vo.annual.DmFocAnnualAmpVO;
import com.huawei.it.fcst.industry.index.vo.common.CommonDropDownVO;
import com.huawei.it.fcst.industry.index.vo.common.DataPermissionsVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFoiImpExpRecordVO;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoDTO;
import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;
import com.huawei.it.fcst.industry.index.vo.replace.ResultAnnualVO;
import com.huawei.it.fcst.industry.index.vo.standard.StandardAnalysisVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Named;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * StandardService Class
 *
 * <AUTHOR>
 * @since 2024/9/2
 */
@Named("standardService")
@JalorResource(code = "standardService", desc = "标准成本分析")
public class StandardService implements IStandardService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StandardService.class);

    private static final String STD_TYPE = "STD";

    private static final String SAME_TYPE = "SAME";

    @Autowired
    private IDmFocVersionDao dmFocVersionDao;

    @Autowired
    private IStandardDao iStandardDao;

    @Autowired
    private CommonService commonService;

    @Autowired
    private StatisticsExcelService statisticsExcelService;

    @Autowired
    private AsyncQueryService asyncQueryService;

    @Autowired
    private CommonAmpService commonAmpService;

    @Autowired
    private AsyncReplaceExportService asyncReplaceExportService;

    @Autowired
    private ReplaceDropDownService replaceDropDownService;

    @JalorOperation(code = "currentAmpCost", desc = "当前层级涨跌图")
    @Override
    public ResultDataVO currentAmpCost(StandardAnalysisVO standardAnalysisVO) {
        // 数据权限范围
        commonService.setUserPermission(standardAnalysisVO);
        // 获取最新的version_id
        standardAnalysisVO.setVersionId(dmFocVersionDao.findAnnualVersion(IndustryConst.TABLE_NAME.ICT_TABLE.getValue()).getVersionId());
        List<DmFocAnnualAmpVO> annualAmpVOList = new ArrayList<>();
        asyncQueryService.findCurrentStandDataList(annualAmpVOList, standardAnalysisVO);
        commonAmpService.setNoEffectiveAmp(annualAmpVOList, "data");
        return ResultDataVO.success(annualAmpVOList);
    }

    @JalorOperation(code = "multiAmpCostChart", desc = "多子项涨跌图")
    @Override
    public ResultDataVO multiAmpCostChart(StandardAnalysisVO standardAnalysisVO) {
        // 数据权限范围
        commonService.setUserPermission(standardAnalysisVO);
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(standardAnalysisVO.getPageSize());
        pageVO.setCurPage(standardAnalysisVO.getPageIndex());
        // 获取最新的version_id
        standardAnalysisVO.setVersionId(dmFocVersionDao.findAnnualVersion(IndustryConst.TABLE_NAME.ICT_TABLE.getValue()).getVersionId());
        // 计算最近的三年
        List<String> threeYears = iStandardDao.getStandAnnualPeriodYear(standardAnalysisVO);

        if (!standardAnalysisVO.getIsMultipleSelect()) {
            standardAnalysisVO.setYearList(threeYears);
        } else {
            // 获取当前年份YTD
            if (CollectionUtils.isNotEmpty(threeYears)) {
                standardAnalysisVO.setYear(threeYears.get(0));
            }
        }
        standardAnalysisVO.setParentLevel(standardAnalysisVO.getGroupLevel());
        // 根据groupLevel计算子项level
        String groupLevel = FcstIndexUtil.getNextGroupLevelByView(standardAnalysisVO.getViewFlag(),
                standardAnalysisVO.getParentLevel(), "U", "ICT");
        // 设置重量级团队code为null
        resetProdRndTeamCodeList(standardAnalysisVO);

        standardAnalysisVO.setParentCodeList(standardAnalysisVO.getGroupCodeList());
        standardAnalysisVO.setGroupLevel(groupLevel);

        List<DmFocAnnualAmpVO> dmFocAnnualAmpResultList = new ArrayList<>();
        if (standardAnalysisVO.getIsMultipleSelect()) {
            dmFocAnnualAmpResultList = multiSelectCodeResult(standardAnalysisVO, pageVO);
        } else {
            List<DmFocAnnualAmpVO> groupCodeOrderList = iStandardDao.findStandardGroupCodeOrderByWeight(standardAnalysisVO);
            String groupCodeOrder = limitGroupCodePage(groupCodeOrderList, pageVO);
            standardAnalysisVO.setGroupCodeOrder(groupCodeOrder);
            if (STD_TYPE.equals(standardAnalysisVO.getCostType())) {
                dmFocAnnualAmpResultList.addAll(iStandardDao.multiStdAmpCostChart(standardAnalysisVO));
            } else if (SAME_TYPE.equals(standardAnalysisVO.getCostType())) {
                dmFocAnnualAmpResultList.addAll(iStandardDao.multiSameAmpCostChart(standardAnalysisVO));
            } else {
                dmFocAnnualAmpResultList.addAll(iStandardDao.multiReplaceAmpCostChart(standardAnalysisVO));
            }
        }
        // 设置无效的涨跌幅提示语
        commonAmpService.setNoEffectiveAmp(dmFocAnnualAmpResultList, "data");
        ResultAnnualVO resultAnnualVO = new ResultAnnualVO();
        resultAnnualVO.setResult(dmFocAnnualAmpResultList);
        resultAnnualVO.setPageVo(pageVO);
        return ResultDataVO.success(resultAnnualVO);
    }

    @JalorOperation(code = "industryAmpCostList", desc = "涨跌一览表")
    @Override
    public ResultDataVO industryAmpCostList(StandardAnalysisVO standardAnalysisVO) {
        // 数据权限范围
        commonService.setUserPermission(standardAnalysisVO);
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(standardAnalysisVO.getPageSize());
        pageVO.setCurPage(standardAnalysisVO.getPageIndex());
        setIndustrySearchParams(standardAnalysisVO);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpWeightList = new ArrayList<>();
        if (STD_TYPE.equals(standardAnalysisVO.getCostType())) {
            asyncQueryService.findOverviewDataList(dmFocAnnualAmpWeightList, standardAnalysisVO);
        } else if (SAME_TYPE.equals(standardAnalysisVO.getCostType())) {
            dmFocAnnualAmpWeightList = iStandardDao.industrySameAmpCostList(standardAnalysisVO);
        } else {
            dmFocAnnualAmpWeightList = iStandardDao.industryReplaceAmpCostList(standardAnalysisVO);
        }
        // item层级按照权重*涨跌大小排序；各层级权重排序;获取权重*涨跌最大值
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = commonAmpService.dealGroupLevelWeightAndAmp(dmFocAnnualAmpWeightList, standardAnalysisVO, true);
        List<DmFocAnnualAmpVO> annualAmpAndWeightList = new ArrayList<>();
        if (IndustryIndexEnum.REPLACE_COST_TYPE.STD.getValue().equals(standardAnalysisVO.getCostType())) {
            commonAmpService.setNoEffectiveAmp(dmFocAnnualAmpVOResult, "data");
            // 标准成本下三个成本类型数据行转列
            List<DmFocAnnualAmpVO> ananualAmpVOList = new ArrayList<>();
            Map<String, List<DmFocAnnualAmpVO>> mapDmFocAnnualAmpList = dmFocAnnualAmpVOResult.stream().collect(
                    Collectors.groupingBy(item -> item.getGroupCode() +"_"+ item.getGroupCnName() + "_" +item.getPeriodYear() , HashMap::new, Collectors.toList()));
            // 三个成本的数据汇总在一起
            commonAmpService.setAnnaulAmpVOList(ananualAmpVOList, mapDmFocAnnualAmpList);
            // 排序和分页
            annualAmpAndWeightList = orderColumnLimitPage(ananualAmpVOList, standardAnalysisVO, pageVO);
        } else {
            // 排序和分页
            annualAmpAndWeightList = orderColumnLimitPage(dmFocAnnualAmpVOResult, standardAnalysisVO, pageVO);
            commonAmpService.setNoEffectiveAmp(annualAmpAndWeightList, "data");
        }
        ResultAnnualVO resultAnnualVO = new ResultAnnualVO();
        resultAnnualVO.setMaxValue(standardAnalysisVO.getMaxValue() + "%");
        resultAnnualVO.setResult(annualAmpAndWeightList);
        resultAnnualVO.setPageVo(pageVO);
        return ResultDataVO.success(resultAnnualVO);
    }


    @JalorOperation(code = "distributeAmpCostChart", desc = "成本分布图")
    @Override
    public ResultDataVO distributeAmpCostChart(StandardAnalysisVO standardAnalysisVO) {
        // 数据权限范围
        commonService.setUserPermission(standardAnalysisVO);
        standardAnalysisVO.setVersionId(dmFocVersionDao.findAnnualVersion(IndustryConst.TABLE_NAME.ICT_TABLE.getValue()).getVersionId());
        if (!standardAnalysisVO.getIsMultipleSelect()) {
            // 计算最近的三年
            List<String> threeYears = iStandardDao.getStandAnnualPeriodYear(standardAnalysisVO);
            standardAnalysisVO.setYearList(threeYears);
        }
        return ResultDataVO.success(iStandardDao.distributeAmpCostChart(standardAnalysisVO));
    }

    @Override
    @JalorOperation(code = "monthAccCostAmpList", desc = "月度累计成本涨跌指数图")
    public ResultDataVO monthAccCostAmpList(StandardAnalysisVO standardAnalysisVO) {
        // 数据权限范围
        commonService.setUserPermission(standardAnalysisVO);
        List<DmFocAnnualAmpVO> priceIndexVOList = new ArrayList<>();
        standardAnalysisVO.setVersionId(dmFocVersionDao.findAnnualVersion(IndustryConst.TABLE_NAME.ICT_TABLE.getValue()).getVersionId());
        standardAnalysisVO.setMonthVersionId(iStandardDao.findMonAccVersion());
        // 检查基期开始时间和结束时间是否传参，若没有，则默认取报告期36个月的开始时间和结束时间
        // 最大基期时间为空的话，不用继续查询，返回空数组
        Long actualMonthNum = iStandardDao.findActualMonthNum();
        if ("0".equals(actualMonthNum.toString())) {
            return ResultDataVO.success(priceIndexVOList);
        }
        FcstIndexUtil.handleNowPeriod(standardAnalysisVO, actualMonthNum.toString());
        // 多选时查询入参成本类型对应的数据
        if (standardAnalysisVO.getIsMultipleSelect()) {
            // 多选场景,需要传costType
            priceIndexVOList = iStandardDao.findDmFocMonthAccCostAmpList(standardAnalysisVO);
        } else {
            asyncQueryService.findMonthAccAllDataList(priceIndexVOList, standardAnalysisVO);
        }
        priceIndexVOList.forEach(vo -> vo.setCostTypeValue(IndustryIndexEnum.getStandCostType(vo.getCostType()).getDesc()));
        return ResultDataVO.success(priceIndexVOList);
    }

    @JalorOperation(code = "exportDetail", desc = "明细数据下载")
    @Audit(module = "standardService-exportDetail", operation = "exportDetail", message = "明细数据下载")
    @Override
    public ResultDataVO exportDetail(StandardAnalysisVO standardAnalysisVO, HttpServletResponse response) throws Exception {
        Timestamp creationDate = new Timestamp(System.currentTimeMillis());
        // 数据权限范围
        commonService.setUserPermission(standardAnalysisVO);
        standardAnalysisVO.setParentLevel(standardAnalysisVO.getGroupLevel());
        long start = System.currentTimeMillis();
        LOGGER.info("Begin StandardService::exportDetail:{}", JSON.toJSONString(standardAnalysisVO));
        // 获取导出模板
        Boolean viewFlagCondition = IndustryIndexEnum.VIEW_FLAG_U.VIEW1.getValue().equals(standardAnalysisVO.getViewFlag()) ||
                (IndustryIndexEnum.VIEW_FLAG_U.VIEW2.getValue().equals(standardAnalysisVO.getViewFlag()) && GroupLevelEnumU.LV1.getValue().equals(standardAnalysisVO.getGroupLevel())) ||
                (IndustryIndexEnum.VIEW_FLAG_U.VIEW3.getValue().equals(standardAnalysisVO.getViewFlag()) && GroupLevelEnumU.LV2.getValue().equals(standardAnalysisVO.getGroupLevel()));
        Boolean condition = viewFlagCondition ||
                (IndustryIndexEnum.VIEW_FLAG_U.VIEW4.getValue().equals(standardAnalysisVO.getViewFlag()) && GroupLevelEnumU.LV3.getValue().equals(standardAnalysisVO.getGroupLevel()));
        if (!standardAnalysisVO.getGroupLevel().equals(GroupLevelEnumU.LV0.getValue())) {
            setSearchViewVO(standardAnalysisVO);
        }
        boolean isNotAllDataFlag = standardAnalysisVO.getGroupLevel().equals(GroupLevelEnumU.LV0.getValue()) || standardAnalysisVO.getAllGroupCodeList().size() <= 6;
        String exportTemplate = getStandExportTempalte(standardAnalysisVO, condition, isNotAllDataFlag);
        // 导出模块为空表示无权限下载，直接返回
        if (StringUtils.isEmpty(exportTemplate)) {
            return ResultDataVO.failure(ResultCodeEnum.NOT_PERMISSION.getCode(),
                    Constant.StrEnum.NO_PERMISSION_TO_DOWNLOAD.getValue());
        }
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate(exportTemplate);
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        BeanUtils.copyProperties(standardAnalysisVO, monthAnalysisVO);
        monthAnalysisVO.setGranularityType(IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue());
        monthAnalysisVO.setIndustryOrg(IndustryConst.INDUSTRY_ORG.ICT.getValue());
        monthAnalysisVO.setLv1ProdRdTeamCnName(standardAnalysisVO.getLv1ProdRdTeamCnName());
        monthAnalysisVO.setLv2ProdRdTeamCnName(standardAnalysisVO.getLv2ProdRdTeamCnName());
        monthAnalysisVO.setLv3ProdRdTeamCnName(standardAnalysisVO.getLv3ProdRdTeamCnName());
        String groupCnName = commonService.getReplaceGroupCnName(monthAnalysisVO);
        DmFocVersionInfoDTO annualVersion = dmFocVersionDao.findAnnualVersion(IndustryConst.TABLE_NAME.ICT_TABLE.getValue());
        standardAnalysisVO.setVersionId(annualVersion.getVersionId());
        Long monAccVersionId = iStandardDao.findMonAccVersion();
        standardAnalysisVO.setMonthVersionId(monAccVersionId);
        List<String> threeYears = iStandardDao.getStandAnnualPeriodYear(standardAnalysisVO);
        standardAnalysisVO.setYearList(threeYears);
        // 导出模板对应的数据
        int totalRows = getExportData(isNotAllDataFlag, condition, standardAnalysisVO, workbook, groupCnName);
        String fileName = standardAnalysisVO.getFileName().concat(".xlsx");
        // Excel文件下载到浏览器
        ExcelExportUtil.downloadExcel(workbook, fileName, response);
        // 插入导出记录信息，并上传导出文件到S3服务器
        insertExportRecord(totalRows, workbook, standardAnalysisVO, UserInfoUtils.getUserId(), creationDate);
        long end = System.currentTimeMillis();
        LOGGER.info("End StandardService::exportDetail", (end - start));
        return ResultDataVO.success();
    }

    private int getExportData(boolean isNotAllDataFlag, boolean condition, StandardAnalysisVO standardAnalysisVO, XSSFWorkbook workbook, String groupCnName) throws Exception {
        int totalRows = 0;
        if (isNotAllDataFlag) {
            if (condition) {
                totalRows = exportPageDataCurrentLevelTemplate(standardAnalysisVO, workbook, groupCnName);
            } else {
                totalRows = exportPageDataByTemplate(standardAnalysisVO, workbook, groupCnName);
            }
        } else {
            if (condition) {
                totalRows = exportDataCurrentLevelTemplate(standardAnalysisVO, workbook, groupCnName);
            } else {
                totalRows = exportDataByTemplate(standardAnalysisVO, workbook, groupCnName);
            }
        }
        return totalRows;
    }

    private void insertExportRecord(int totalRows, Workbook workbook, StandardAnalysisVO standardAnalysisVO, Long userId, Timestamp creationDate) throws IOException, CommonApplicationException {
        // 插入数据，上传文件
        String fileName = standardAnalysisVO.getFileName();
        DmFoiImpExpRecordVO recordVO = StatisticsExcelService.uploadExportExcel(workbook, totalRows, fileName, userId);
        recordVO.setModuleType(CommonConstant.INDUSTRT_STANDARD);
        recordVO.setUserId(String.valueOf(userId));
        // 设置创建时间和结束时间
        recordVO.setCreationDate(creationDate);
        recordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        recordVO.setEndDate(new Timestamp(System.currentTimeMillis()));
        // 插入数据
        statisticsExcelService.insertExportExcelRecord(recordVO);
    }

    private String getStandExportTempalte(StandardAnalysisVO standardAnalysisVO, Boolean condition, boolean isNotAllDataFlag) throws ApplicationException {
        String exportTemplate;
        if (isNotAllDataFlag) {
            if (standardAnalysisVO.getIsMultipleSelect()) {
                if (condition) {
                    exportTemplate = Constant.StrEnum.STAND_AMP_TEMPLATE5_PATH.getValue();
                } else {
                    exportTemplate = Constant.StrEnum.STAND_AMP_TEMPLATE6_PATH.getValue();
                }
            } else {
                if (condition) {
                    exportTemplate = Constant.StrEnum.STAND_AMP_TEMPLATE5_PATH.getValue();
                } else {
                    exportTemplate = Constant.StrEnum.STAND_AMP_TEMPLATE4_PATH.getValue();
                }
            }
        } else {
            if (standardAnalysisVO.getIsMultipleSelect()) {
                if (condition) {
                    exportTemplate = Constant.StrEnum.STAND_AMP_TEMPLATE2_PATH.getValue();
                } else {
                    exportTemplate = Constant.StrEnum.STAND_AMP_TEMPLATE3_PATH.getValue();
                }
            } else {
                if (condition) {
                    exportTemplate = Constant.StrEnum.STAND_AMP_TEMPLATE2_PATH.getValue();
                } else {
                    exportTemplate = Constant.StrEnum.STAND_AMP_TEMPLATE1_PATH.getValue();
                }
            }
        }
        return exportTemplate;
    }

    private int exportDataByTemplate(StandardAnalysisVO standardAnalysisVO, XSSFWorkbook workbook, String groupCnName) throws Exception {

        IRequestContext current = RequestContextManager.getCurrent();

        Future<Integer> distributeTotal = asyncReplaceExportService.fillStandDistributeSheet(workbook, 0, groupCnName, standardAnalysisVO, current);
        Future<Integer> currentAmpTotal = asyncReplaceExportService.fillStandCurrentAmpSheet(workbook, 1, groupCnName, standardAnalysisVO, current);
        Future<Integer> currentPriceIndexTotal = asyncReplaceExportService.fillCurrentPriceIndexSheet(workbook, 2, groupCnName, standardAnalysisVO, current);
        Future<Integer> multiAmpTotal;
        if (standardAnalysisVO.getIsMultipleSelect()) {
            multiAmpTotal = asyncReplaceExportService.fillMultiSelectAmpSheet(workbook, 3, groupCnName, standardAnalysisVO, current);
        } else {
            multiAmpTotal = asyncReplaceExportService.fillStandMultiAmpSheet(workbook, 3, groupCnName, standardAnalysisVO, current);
        }
        Future<Integer> overviewAmpTotal = asyncReplaceExportService.fillOverviewAmpSheet(workbook, 4, groupCnName, standardAnalysisVO, current);

        StandardAnalysisVO standardVO = ObjectCopyUtil.copy(standardAnalysisVO, StandardAnalysisVO.class);
        standardVO.setGroupCodeList(standardVO.getAllGroupCodeList());

        Future<Integer> allDistribute = asyncReplaceExportService.fillStandDistributeSheet(workbook, 5, groupCnName, standardVO, current);
        Future<Integer> allCurrentAmp = asyncReplaceExportService.fillStandCurrentAmpSheet(workbook, 6, groupCnName, standardVO, current);
        Future<Integer> allCurrentPriceIndex = asyncReplaceExportService.fillCurrentPriceIndexSheet(workbook, 7, groupCnName, standardVO, current);
        Future<Integer> allMultiAmp;
        if (standardAnalysisVO.getIsMultipleSelect()) {
            allMultiAmp = asyncReplaceExportService.fillMultiSelectAmpSheet(workbook, 8, groupCnName, standardVO, current);
        } else {
            allMultiAmp = asyncReplaceExportService.fillStandMultiAmpSheet(workbook, 8, groupCnName, standardVO, current);
        }
        Future<Integer> allOverviewAmp = asyncReplaceExportService.fillOverviewAmpSheet(workbook, 9, groupCnName, standardVO, current);

        while (true) {
            boolean conditionOne = distributeTotal.isDone() && currentAmpTotal.isDone() && currentPriceIndexTotal.isDone();
            boolean conditionTwo = multiAmpTotal.isDone() && overviewAmpTotal.isDone() && allDistribute.isDone();
            boolean conditionThree = allCurrentAmp.isDone() && allCurrentPriceIndex.isDone() && allMultiAmp.isDone();
            boolean condition =conditionOne&& conditionTwo &&conditionThree;
            if (condition&& allOverviewAmp.isDone() ) {
                break;
            }
        }
        return distributeTotal.get() + currentAmpTotal.get()+ currentPriceIndexTotal.get()+ multiAmpTotal.get()
                + overviewAmpTotal.get()+ allDistribute.get()+ allCurrentAmp.get()+ allCurrentPriceIndex.get()+ allMultiAmp.get()+ allOverviewAmp.get() ;
    }

    private int exportDataCurrentLevelTemplate(StandardAnalysisVO standardAnalysisVO, XSSFWorkbook workbook, String groupCnName) throws Exception {

        IRequestContext current = RequestContextManager.getCurrent();
        Future<Integer> distributeTotal = asyncReplaceExportService.fillStandDistributeSheet(workbook, 0, groupCnName, standardAnalysisVO, current);
        Future<Integer> currentAmpTotal = asyncReplaceExportService.fillStandCurrentAmpSheet(workbook, 1, groupCnName, standardAnalysisVO, current);
        Future<Integer> currentPriceIndexTotal = asyncReplaceExportService.fillCurrentPriceIndexSheet(workbook, 2, groupCnName, standardAnalysisVO, current);

        StandardAnalysisVO standardVO = ObjectCopyUtil.copy(standardAnalysisVO, StandardAnalysisVO.class);
        standardVO.setGroupCodeList(standardVO.getAllGroupCodeList());

        Future<Integer> allDistribute = asyncReplaceExportService.fillStandDistributeSheet(workbook, 3, groupCnName, standardVO, current);
        Future<Integer> allCurrentAmp = asyncReplaceExportService.fillStandCurrentAmpSheet(workbook, 4, groupCnName, standardVO, current);
        Future<Integer> allCurrentPriceIndex = asyncReplaceExportService.fillCurrentPriceIndexSheet(workbook, 5, groupCnName, standardVO, current);

        while (true) {
            boolean conditionOne = distributeTotal.isDone() && currentAmpTotal.isDone() && currentPriceIndexTotal.isDone();
            boolean conditionTwo = allDistribute.isDone() && allCurrentAmp.isDone() && allCurrentPriceIndex.isDone();
            if (conditionOne && conditionTwo) {
                break;
            }
        }
        return distributeTotal.get() + currentAmpTotal.get() + currentPriceIndexTotal.get() + allDistribute.get()
                + allCurrentAmp.get() + allCurrentPriceIndex.get();
    }

    private int exportPageDataCurrentLevelTemplate(StandardAnalysisVO standardAnalysisVO, XSSFWorkbook workbook, String groupCnName) throws Exception {

        IRequestContext current = RequestContextManager.getCurrent();
        Future<Integer> distributeTotal = asyncReplaceExportService.fillStandDistributeSheet(workbook, 0, groupCnName, standardAnalysisVO, current);
        Future<Integer> currentAmpTotal = asyncReplaceExportService.fillStandCurrentAmpSheet(workbook, 1, groupCnName, standardAnalysisVO, current);
        Future<Integer> currentPriceIndexTotal = asyncReplaceExportService.fillCurrentPriceIndexSheet(workbook, 2, groupCnName, standardAnalysisVO, current);

        while (true) {
            boolean conditionOne = distributeTotal.isDone() && currentAmpTotal.isDone() && currentPriceIndexTotal.isDone();
            if (conditionOne) {
                break;
            }
        }
        return distributeTotal.get() + currentAmpTotal.get() + currentPriceIndexTotal.get();
    }

    private int exportPageDataByTemplate(StandardAnalysisVO standardAnalysisVO, XSSFWorkbook workbook, String groupCnName) throws Exception {

        IRequestContext current = RequestContextManager.getCurrent();

        Future<Integer> distributeTotal = asyncReplaceExportService.fillStandDistributeSheet(workbook, 0, groupCnName, standardAnalysisVO, current);
        Future<Integer> currentAmpTotal = asyncReplaceExportService.fillStandCurrentAmpSheet(workbook, 1, groupCnName, standardAnalysisVO, current);
        Future<Integer> currentPriceIndexTotal = asyncReplaceExportService.fillCurrentPriceIndexSheet(workbook, 2, groupCnName, standardAnalysisVO, current);
        Future<Integer> multiAmpTotal;
        if (standardAnalysisVO.getIsMultipleSelect()) {
            multiAmpTotal = asyncReplaceExportService.fillMultiSelectAmpSheet(workbook, 3, groupCnName, standardAnalysisVO, current);
        } else {
            multiAmpTotal = asyncReplaceExportService.fillStandMultiAmpSheet(workbook, 3, groupCnName, standardAnalysisVO, current);
        }
        Future<Integer> overviewAmpTotal = asyncReplaceExportService.fillOverviewAmpSheet(workbook, 4, groupCnName, standardAnalysisVO, current);

        while (true) {
            boolean conditionOne = distributeTotal.isDone() && currentAmpTotal.isDone() && currentPriceIndexTotal.isDone();
            boolean conditionTwo = multiAmpTotal.isDone() && overviewAmpTotal.isDone();
            if (conditionOne && conditionTwo) {
                break;
            }
        }
        return distributeTotal.get() + currentAmpTotal.get()+ currentPriceIndexTotal.get()+ multiAmpTotal.get() + overviewAmpTotal.get();
    }

    private void setSearchViewVO(StandardAnalysisVO standardVO) throws ApplicationException {
        CommonDropDownVO commonViewVO = ObjectCopyUtil.copy(standardVO, CommonDropDownVO.class);
        // 计算最近的三年
        List<String> yearList = standardVO.getYearList();
        // 获取当前年份
        if (CollectionUtils.isNotEmpty(yearList)) {
            commonViewVO.setPeriodYear(yearList.get(0));
        }
        commonViewVO.setNextGroupLevel(standardVO.getGroupLevel());
        commonViewVO.setDataFlag("STANDARD");
        if ("LV3".equals(commonViewVO.getNextGroupLevel())) {
            commonViewVO.setLv3ProdRndTeamCodeList(null);
        }
        if ("LV2".equals(commonViewVO.getNextGroupLevel())) {
            commonViewVO.setLv2ProdRndTeamCodeList(null);
        }
        if ("LV1".equals(commonViewVO.getNextGroupLevel())) {
            commonViewVO.setLv1ProdRndTeamCodeList(null);
        }
        if (CollectionUtils.isNotEmpty(commonViewVO.getLv2ProdRndTeamCodeList())) {
            commonViewVO.setGroupLevel("LV2");
        } else if (CollectionUtils.isNotEmpty(commonViewVO.getLv1ProdRndTeamCodeList())) {
            commonViewVO.setGroupLevel("LV1");
        } else {
            commonViewVO.setGroupLevel("LV0");
        }
        commonViewVO.setGroupCodeList(null);
        // 获取最新的version_id
        commonViewVO.setVersionId(dmFocVersionDao.findAnnualVersion("dm_foc").getVersionId());
        // 获取数据权限
        DataPermissionsVO currentRoleDataPermission = commonService.getCurrentRoleDataPermission("ICT");
        commonViewVO.setLv0DimensionSet(currentRoleDataPermission.getLv0DimensionSet());
        commonViewVO.setLv1DimensionSet(currentRoleDataPermission.getLv1DimensionSet());
        commonViewVO.setLv2DimensionSet(currentRoleDataPermission.getLv2DimensionSet());
        List<DmFocViewInfoVO> dmViewInfoVOList = replaceDropDownService.getDmViewInfoVOList(commonViewVO);
        List<String> allGroupCodeList = dmViewInfoVOList.stream().map(DmFocViewInfoVO::getGroupCode).collect(Collectors.toList());
        standardVO.setAllGroupCodeList(allGroupCodeList);
    }

    private void setIndustrySearchParams(StandardAnalysisVO standardAnalysisVO) {
        standardAnalysisVO.setParentCodeList(standardAnalysisVO.getGroupCodeList());
        standardAnalysisVO.setParentLevel(standardAnalysisVO.getGroupLevel());

        standardAnalysisVO.setParentLevel(standardAnalysisVO.getGroupLevel());
        if (!GroupLevelAllEnum.LV3.getValue().equals(standardAnalysisVO.getGroupLevel())) {
            String groupLevel = FcstIndexUtil.getNextGroupLevelByView(standardAnalysisVO.getViewFlag(),
                    standardAnalysisVO.getParentLevel(), "U", "ICT");
            standardAnalysisVO.setGroupLevel(groupLevel);
        }
        // 设置重量级团队code为null
        resetProdRndTeamCodeList(standardAnalysisVO);
        if (!standardAnalysisVO.getIsMultipleSelect()) {
            // 计算最近的三年
            List<String> threeYears = iStandardDao.getStandAnnualPeriodYear(standardAnalysisVO);
            standardAnalysisVO.setYearList(threeYears);
        }
        standardAnalysisVO.setVersionId(dmFocVersionDao.findAnnualVersion(IndustryConst.TABLE_NAME.ICT_TABLE.getValue()).getVersionId());
    }

    private List<DmFocAnnualAmpVO> multiSelectCodeResult(StandardAnalysisVO standardAnalysisVO, PageVO pageVO) {
        List<DmFocAnnualAmpVO> dmFocAnnualAmpResultList = new ArrayList<>();
        asyncQueryService.findMultiStandDataList(dmFocAnnualAmpResultList, standardAnalysisVO);
        HashMap<String, List<DmFocAnnualAmpVO>> divideGroupCodeMap = dmFocAnnualAmpResultList.stream().collect(
                Collectors.groupingBy(result -> result.getParentCode() + "_" + result.getParentCnName() + "_" + result.getGroupCode() + "_"
                        + result.getGroupCnName(), LinkedHashMap::new, Collectors.toList()));
        pageVO.setTotalRows(divideGroupCodeMap.size());
        List<DmFocAnnualAmpVO> annualAmpWeightList = new ArrayList<>();
        int curPage = pageVO.getCurPage() - 1;
        if (!divideGroupCodeMap.isEmpty()) {
            int pageSize = pageVO.getPageSize();
            int fromIndex = curPage * pageSize;
            int totalIndex = (curPage + 1) * pageSize;
            if (totalIndex > divideGroupCodeMap.size()) {
                totalIndex = divideGroupCodeMap.size();
            }
            List<String> groupCodeList = new ArrayList<>();
            groupCodeList.addAll(divideGroupCodeMap.keySet());
            for (int i = fromIndex; i < totalIndex; i++) {
                String groupCode = groupCodeList.get(i);
                List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = divideGroupCodeMap.get(groupCode);
                annualAmpWeightList.addAll(dmFocAnnualAmpVOList);
            }
        }
        return annualAmpWeightList;
    }

    private List<DmFocAnnualAmpVO> orderColumnLimitPage(List<DmFocAnnualAmpVO> dmFocAnnualAmpPageResult, StandardAnalysisVO standardAnalysisVO, PageVO pageVO) {
        String orderColumn = standardAnalysisVO.getOrderColumn();
        if (StringUtils.isNotBlank(orderColumn)) {
            switch (orderColumn) {
                case "weightRate":
                    dmFocAnnualAmpPageResult = sortByWeightRateBar(dmFocAnnualAmpPageResult, standardAnalysisVO);
                    break;
                case "groupCnName":
                    groupCnNameOrder(dmFocAnnualAmpPageResult, standardAnalysisVO);
                    break;
                case "groupCode":
                    groupCodeOrder(dmFocAnnualAmpPageResult, standardAnalysisVO);
                    break;
                case "annualAmp":
                    dmFocAnnualAmpPageResult = getAnnualAmpOrderResult(dmFocAnnualAmpPageResult, standardAnalysisVO);
                    break;
                case "replAnnualAmp":
                    dmFocAnnualAmpPageResult = getReplAnnualAmpOrderResult(dmFocAnnualAmpPageResult, standardAnalysisVO);
                    break;
                case "sameAnnualAmp":
                    dmFocAnnualAmpPageResult = getSameAnnualAmpOrderResult(dmFocAnnualAmpPageResult, standardAnalysisVO);
                    break;
                case "weightAnnualAmpPercent":
                case "weightAnnualAmpPercentOrder":
                    if ("asc".equals(standardAnalysisVO.getOrderMethod())) {
                        dmFocAnnualAmpPageResult = sortByWeightAnnualBar(dmFocAnnualAmpPageResult, true);
                    } else {
                        dmFocAnnualAmpPageResult = sortByWeightAnnualBar(dmFocAnnualAmpPageResult, false);
                    }
                    break;
                default:
                    break;
            }
        } else {
            // 根据状态码区分-和有数据的记录后，默认权重大小排序
            dmFocAnnualAmpPageResult = sortWeightBarAndRecordList(dmFocAnnualAmpPageResult, standardAnalysisVO);
        }
        // 分页
        int count = dmFocAnnualAmpPageResult.size();
        dmFocAnnualAmpPageResult = getResultLimitPage(dmFocAnnualAmpPageResult, pageVO);
        pageVO.setTotalRows(count);
        return dmFocAnnualAmpPageResult;
    }

    private List<DmFocAnnualAmpVO> getAnnualAmpOrderResult(List<DmFocAnnualAmpVO> dmFocAnnualAmpPageResult, StandardAnalysisVO standardAnalysisVO) {
        List<DmFocAnnualAmpVO> barAnnualAmpList = dmFocAnnualAmpPageResult.stream().filter(result -> "0*".equals(result.getAnnualAmp())).collect(Collectors.toList());
        List<DmFocAnnualAmpVO> otherAnnualAmpList = dmFocAnnualAmpPageResult.stream().filter(vo -> !barAnnualAmpList.contains(vo)).collect(Collectors.toList());

        annualAmpOrder(otherAnnualAmpList, standardAnalysisVO);

        List<DmFocAnnualAmpVO> ampPagedResult = new ArrayList<>();
        ampPagedResult.addAll(otherAnnualAmpList);
        ampPagedResult.addAll(barAnnualAmpList);
        return ampPagedResult;
    }

    private List<DmFocAnnualAmpVO> getReplAnnualAmpOrderResult(List<DmFocAnnualAmpVO> dmFocAnnualAmpPageResult, StandardAnalysisVO standardAnalysisVO) {
        List<DmFocAnnualAmpVO> barReplAnnualAmpList = dmFocAnnualAmpPageResult.stream().filter(result -> "0*".equals(result.getReplAnnualAmp())).collect(Collectors.toList());
        List<DmFocAnnualAmpVO> otherReplAnnualAmpList = dmFocAnnualAmpPageResult.stream().filter(vo -> !barReplAnnualAmpList.contains(vo)).collect(Collectors.toList());

        replAnnualAmpOrder(otherReplAnnualAmpList, standardAnalysisVO);

        List<DmFocAnnualAmpVO> ampPagedResult = new ArrayList<>();
        ampPagedResult.addAll(otherReplAnnualAmpList);
        ampPagedResult.addAll(barReplAnnualAmpList);
        return ampPagedResult;
    }

    private List<DmFocAnnualAmpVO> getSameAnnualAmpOrderResult(List<DmFocAnnualAmpVO> dmFocAnnualAmpPageResult, StandardAnalysisVO standardAnalysisVO) {
        List<DmFocAnnualAmpVO> barSameAnnualAmpList = dmFocAnnualAmpPageResult.stream().filter(result -> "0*".equals(result.getSameAnnualAmp())).collect(Collectors.toList());
        List<DmFocAnnualAmpVO> otherSameAnnualAmpList = dmFocAnnualAmpPageResult.stream().filter(vo -> !barSameAnnualAmpList.contains(vo)).collect(Collectors.toList());

        sameAnnualAmpOrder(otherSameAnnualAmpList, standardAnalysisVO);

        List<DmFocAnnualAmpVO> ampPagedResult = new ArrayList<>();
        ampPagedResult.addAll(otherSameAnnualAmpList);
        ampPagedResult.addAll(barSameAnnualAmpList);
        return ampPagedResult;
    }

    private List<DmFocAnnualAmpVO> sortWeightBarAndRecordList(List<DmFocAnnualAmpVO> dmFocAnnualAmpPagedResult, StandardAnalysisVO standardAnalysisVO) {
        List<DmFocAnnualAmpVO> barAnnualAmpVOList = dmFocAnnualAmpPagedResult.stream().filter(result -> "0*".equals(result.getWeightRate())).collect(Collectors.toList());
        List<DmFocAnnualAmpVO> otherList = dmFocAnnualAmpPagedResult.stream().filter(vo -> !barAnnualAmpVOList.contains(vo)).collect(Collectors.toList());
        commonAmpService.sortByWeight(otherList);
        List<DmFocAnnualAmpVO> newDmFocAnnualAmpPagedResult = new ArrayList<>();
        newDmFocAnnualAmpPagedResult.addAll(otherList);
        newDmFocAnnualAmpPagedResult.addAll(barAnnualAmpVOList);
        return newDmFocAnnualAmpPagedResult;
    }

    private List<DmFocAnnualAmpVO> sortByWeightAnnualBar(List<DmFocAnnualAmpVO> dmFocAnnualAmpPagedResult, boolean flag) {

        List<DmFocAnnualAmpVO> barAnnualList = dmFocAnnualAmpPagedResult.stream().filter(result -> "0*".equals(result.getWeightAnnualAmpPercent())).collect(Collectors.toList());
        List<DmFocAnnualAmpVO> otherWeightList = dmFocAnnualAmpPagedResult.stream().filter(vo -> !barAnnualList.contains(vo)).collect(Collectors.toList());

        orderWeightAnnualAmpPercent(otherWeightList, flag);
        List<DmFocAnnualAmpVO> annualAmpVOPagedResult = new ArrayList<>();
        annualAmpVOPagedResult.addAll(otherWeightList);
        annualAmpVOPagedResult.addAll(barAnnualList);
        return annualAmpVOPagedResult;

    }

    private List<DmFocAnnualAmpVO> getResultLimitPage(List<DmFocAnnualAmpVO> dmFocAnnualResult, PageVO pageVO) {
        int count = dmFocAnnualResult.size();
        int curPage = pageVO.getCurPage() - 1;
        int pageSize = pageVO.getPageSize();
        List<DmFocAnnualAmpVO> dmAnnualAmpList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dmFocAnnualResult) && count > 0) {
            int fromIndex = curPage * pageSize;
            int totalIndex = (curPage + 1) * pageSize;
            if (totalIndex > count) {
                totalIndex = count;
            }
            dmAnnualAmpList = dmFocAnnualResult.subList(fromIndex, totalIndex);
        }
        return dmAnnualAmpList;
    }

    private CommonAnnualVO transformStr(String value1, String value2) {
        CommonAnnualVO annualVO = new CommonAnnualVO();
        Double number1 = null;
        Double number2 = null;
        if (StringUtils.isNotBlank(value1) && !value1.equals("0*")) {
            value1 = subPercentStr(value1);
            number1 = Double.parseDouble(value1);
        }
        if (StringUtils.isNotBlank(value2) && !value2.equals("0*")) {
            value2 = subPercentStr(value2);
            number2 = Double.parseDouble(value2);
        }
        annualVO.setNum1(number1);
        annualVO.setNum2(number2);
        return annualVO;
    }

    private void weightRateOrder(List<DmFocAnnualAmpVO> dmFocAnnualAmpPagedResult, StandardAnalysisVO standardAnalysisVO) {
        if ("asc".equals(standardAnalysisVO.getOrderMethod())) {
            Collections.sort(dmFocAnnualAmpPagedResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnualOne, DmFocAnnualAmpVO dmFocAnnualOther) {
                    String weightRateOne = dmFocAnnualOne.getWeightRate();
                    String weightRateOther = dmFocAnnualOther.getWeightRate();
                    return commonAmpService.compareWeightColumn(weightRateOne, weightRateOther);
                }
            });
        } else {
            Collections.sort(dmFocAnnualAmpPagedResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnualOne, DmFocAnnualAmpVO dmFocAnnualOther) {
                    String weightRate1 = dmFocAnnualOne.getWeightRate();
                    String weightRate2 = dmFocAnnualOther.getWeightRate();
                    return commonAmpService.compareWeightColumn(weightRate2, weightRate1);
                }
            });
        }
    }

    public void resetProdRndTeamCodeList(StandardAnalysisVO standardAnalysisVO) {
        // 如果parentlevel为LV0/LV1/LV2/LV3，也就是当parentLevel的下一层级还是重量级团队的时候，需要把prodRndTeamCodeList置为null
        boolean parentFlag = CommonConstant.PROD_RND_TEAM_GROUP_LEVEL.stream().anyMatch(level -> level.equals(standardAnalysisVO.getParentLevel()));
        boolean currentFlag = CommonConstant.PROD_RND_TEAM_GROUP_LEVEL.stream().anyMatch(level -> level.equals(standardAnalysisVO.getGroupLevel()));
        if (parentFlag && currentFlag) {
            standardAnalysisVO.setProdRndTeamCodeList(null);
        }
    }

    private String limitGroupCodePage(List<DmFocAnnualAmpVO> dmFocAnnualPagedResult, PageVO pageVO) {
        int count = dmFocAnnualPagedResult.size();
        pageVO.setTotalRows(count);
        int curPage = pageVO.getCurPage() - 1;
        int pageSize = pageVO.getPageSize();
        List<DmFocAnnualAmpVO> annualAmpAndWeightList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dmFocAnnualPagedResult) && count > 0) {
            int fromIndex = curPage * pageSize;
            int totalIndex = (curPage + 1) * pageSize;
            if (totalIndex > count) {
                totalIndex = count;
            }
            annualAmpAndWeightList = dmFocAnnualPagedResult.subList(fromIndex, totalIndex);
        }
        return annualAmpAndWeightList.stream().map(DmFocAnnualAmpVO::getGroupCode).collect(Collectors.joining(","));
    }

    private List<DmFocAnnualAmpVO> sortByWeightRateBar(List<DmFocAnnualAmpVO> dmFocAnnualAmpPageResult, StandardAnalysisVO standardAnalysisVO) {

        List<DmFocAnnualAmpVO> barAnnualAmpVOList = dmFocAnnualAmpPageResult.stream().filter(result -> "0*".equals(result.getWeightRate())).collect(Collectors.toList());
        List<DmFocAnnualAmpVO> otherList = dmFocAnnualAmpPageResult.stream().filter(vo -> !barAnnualAmpVOList.contains(vo)).collect(Collectors.toList());
        weightRateOrder(otherList, standardAnalysisVO);
        List<DmFocAnnualAmpVO> newAnnualAmpVOPagedResult = new ArrayList<>();
        newAnnualAmpVOPagedResult.addAll(otherList);
        newAnnualAmpVOPagedResult.addAll(barAnnualAmpVOList);
        return newAnnualAmpVOPagedResult;
    }

    private void groupCnNameOrder(List<DmFocAnnualAmpVO> dmFocAnnualAmpPageResult, StandardAnalysisVO standardAnalysisVO) {
        if ("asc".equals(standardAnalysisVO.getOrderMethod())) {
            Collections.sort(dmFocAnnualAmpPageResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnual1, DmFocAnnualAmpVO dmFocAnnual2) {
                    return commonAmpService.sortByGroupCnName(dmFocAnnual1, dmFocAnnual2);
                }
            });
        } else {
            Collections.sort(dmFocAnnualAmpPageResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnual1, DmFocAnnualAmpVO dmFocAnnual2) {
                    return commonAmpService.sortByGroupCnName(dmFocAnnual2, dmFocAnnual1);
                }
            });
        }
    }

    private void groupCodeOrder(List<DmFocAnnualAmpVO> dmFocAnnualAmpPageResult, StandardAnalysisVO standardAnalysisVO) {
        if ("asc".equals(standardAnalysisVO.getOrderMethod())) {
            Collections.sort(dmFocAnnualAmpPageResult, Comparator.comparing(DmFocAnnualAmpVO::getGroupCode));
        } else {
            Collections.sort(dmFocAnnualAmpPageResult, Comparator.comparing(DmFocAnnualAmpVO::getGroupCode).reversed());
        }
    }

    private void sameAnnualAmpOrder(List<DmFocAnnualAmpVO> dmFocAnnualPagedResult, StandardAnalysisVO standardAnalysisVO) {
        if ("asc".equals(standardAnalysisVO.getOrderMethod())) {
            Collections.sort(dmFocAnnualPagedResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnual1, DmFocAnnualAmpVO dmFocAnnual2) {
                    String annualAmpOne = dmFocAnnual1.getSameAnnualAmp();
                    String annualAmpTwo = dmFocAnnual2.getSameAnnualAmp();
                    return commonAmpService.compareWeightColumn(annualAmpOne, annualAmpTwo);
                }
            });
        } else {
            Collections.sort(dmFocAnnualPagedResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnual1, DmFocAnnualAmpVO dmFocAnnual2) {
                    String annualAmpOne = dmFocAnnual1.getSameAnnualAmp();
                    String annualAmpTwo = dmFocAnnual2.getSameAnnualAmp();
                    return commonAmpService.compareWeightColumn(annualAmpTwo, annualAmpOne);
                }
            });
        }
    }

    private void replAnnualAmpOrder(List<DmFocAnnualAmpVO> dmFocAnnualPagedResult, StandardAnalysisVO standardAnalysisVO) {
        if ("asc".equals(standardAnalysisVO.getOrderMethod())) {
            Collections.sort(dmFocAnnualPagedResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnual1, DmFocAnnualAmpVO dmFocAnnual2) {
                    String annualAmpOne = dmFocAnnual1.getReplAnnualAmp();
                    String annualAmpTwo = dmFocAnnual2.getReplAnnualAmp();
                    return commonAmpService.compareWeightColumn(annualAmpOne, annualAmpTwo);
                }
            });
        } else {
            Collections.sort(dmFocAnnualPagedResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnual1, DmFocAnnualAmpVO dmFocAnnual2) {
                    String annualAmpOne = dmFocAnnual1.getReplAnnualAmp();
                    String annualAmpTwo = dmFocAnnual2.getReplAnnualAmp();
                    return commonAmpService.compareWeightColumn(annualAmpTwo, annualAmpOne);
                }
            });
        }
    }

    private void annualAmpOrder(List<DmFocAnnualAmpVO> dmFocAnnualPagedResult, StandardAnalysisVO standardAnalysisVO) {
        if ("asc".equals(standardAnalysisVO.getOrderMethod())) {
            Collections.sort(dmFocAnnualPagedResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnual1, DmFocAnnualAmpVO dmFocAnnual2) {
                    String annualAmpOne = dmFocAnnual1.getAnnualAmp();
                    String annualAmpTwo = dmFocAnnual2.getAnnualAmp();
                    return commonAmpService.compareWeightColumn(annualAmpOne, annualAmpTwo);
                }
            });
        } else {
            Collections.sort(dmFocAnnualPagedResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnual1, DmFocAnnualAmpVO dmFocAnnual2) {
                    String annualAmpOne = dmFocAnnual1.getAnnualAmp();
                    String annualAmpTwo = dmFocAnnual2.getAnnualAmp();
                    return commonAmpService.compareWeightColumn(annualAmpTwo, annualAmpOne);
                }
            });
        }
    }

    private String subPercentStr(String str) {
        if (str.contains("%")) {
            int index = str.indexOf("%");
            return str.substring(0, index);
        }
        return str;
    }

    private void orderWeightAnnualAmpPercent(List<DmFocAnnualAmpVO> dmFocAnnualAmpResult, boolean flag) {
        // 权重*涨跌 排序
        Collections.sort(dmFocAnnualAmpResult, new Comparator<DmFocAnnualAmpVO>() {
            @Override
            public int compare(DmFocAnnualAmpVO dmFocAnnualAmp1, DmFocAnnualAmpVO dmFocAnnualAmp2) {
                String weightAmpPercent1 = dmFocAnnualAmp1.getWeightAnnualAmpPercent();
                String weightAmpPercent2 = dmFocAnnualAmp2.getWeightAnnualAmpPercent();
                if (flag) {
                    return commonAmpService.compareWeightColumn(weightAmpPercent1, weightAmpPercent2);
                }
                return commonAmpService.compareWeightColumn(weightAmpPercent2, weightAmpPercent1);
            }
        });
    }
}
