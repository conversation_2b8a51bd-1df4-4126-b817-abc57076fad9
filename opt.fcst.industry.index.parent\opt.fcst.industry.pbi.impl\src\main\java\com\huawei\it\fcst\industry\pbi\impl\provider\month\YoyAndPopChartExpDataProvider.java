/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.provider.month;

import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.industry.pbi.dao.IctMonthCostIdxDao;
import com.huawei.it.fcst.industry.pbi.enums.IndustryConstEnum;
import com.huawei.it.fcst.export.IExcelExportDataProvider;
import com.huawei.it.fcst.industry.pbi.service.month.IIctMonthAnalysisService;
import com.huawei.it.fcst.industry.pbi.utils.FcstIndustryUtil;
import com.huawei.it.fcst.export.vo.ExcelExportContext;
import com.huawei.it.fcst.export.vo.ExportList;
import com.huawei.it.fcst.industry.pbi.vo.month.IctMonthAnalysisVO;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import javax.inject.Inject;
import javax.inject.Named;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 产业成本指数-同比环比图导出数据提供类
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
@Slf4j
@Named("IExcelExport.YoyAndPopChartExpDataProvider")
public class YoyAndPopChartExpDataProvider implements IExcelExportDataProvider {

    @Inject
    private IIctMonthAnalysisService monthAnalysisService;

    @Inject
    private IctMonthCostIdxDao monthCostIdxDao;

    @Override
    public List<?> getData(Serializable conditionObject, PageVO pageVO) throws ApplicationException {
        log.info(">>>Begin YoyAndPopChartExpDataProvider::getData");
        IctMonthAnalysisVO monthAnalysisVO = (IctMonthAnalysisVO)conditionObject;
        IctMonthAnalysisVO paramsVO = new IctMonthAnalysisVO();
        BeanUtils.copyProperties(monthAnalysisVO, paramsVO);
        ResultDataVO resultDataVO = monthAnalysisService.getIndustryCostYoyAndPopChart(paramsVO);
        List<IctMonthAnalysisVO> dataList = (List<IctMonthAnalysisVO>) resultDataVO.getData();
        dataList = Optional.ofNullable(dataList).orElse(new ArrayList<>());
        ExportList exportList = new ExportList();
        List<IctMonthAnalysisVO> yoyDataList = dataList.stream()
                .filter(item -> "YOY".equals(item.getRateFlag()))
                .collect(Collectors.toList());
        yoyDataList.stream().forEach(item -> item.setYoyRate(item.getRatePercent()));
        List<IctMonthAnalysisVO> popDataList = dataList.stream()
                .filter(item -> "POP".equals(item.getRateFlag()))
                .collect(Collectors.toList());
        popDataList.stream().forEach(item -> item.setPopRate(item.getRatePercent()));
        yoyDataList.stream().forEach(yoyData -> {
            for (IctMonthAnalysisVO popData : popDataList) {
                if (yoyData.getPeriodId().compareTo(popData.getPeriodId()) == 0) {
                    yoyData.setPopRate(popData.getPopRate());
                    break;
                }
            }
        });
        exportList.addAll(yoyDataList);
        exportList.setTotalRows(exportList.size());
        return exportList;
    }

    @Override
    public Map<String, Object> getHeader(Serializable conditionObject, Map<String, Object> parameters) throws ApplicationException {
        ExcelExportContext context = (ExcelExportContext) conditionObject;
        IctMonthAnalysisVO monthAnalysisVO = (IctMonthAnalysisVO) context.getConditionObject();
        // 自定义表头
        Map<String, Object> headMap = new HashMap<>();
        headMap.put("basePeriodId", FcstIndustryUtil.getActualMonth(monthAnalysisVO.getBasePeriodId().toString()));
        headMap.put("displayName", monthAnalysisVO.getDisplayName());
        headMap.put("groupCnName", monthAnalysisVO.getGroupCnName());
        headMap.put("costType", monthAnalysisVO.getCostType());
        headMap.put("granularityTypeCnName", monthAnalysisVO.getGranularityTypeCnName());
        headMap.put("overseaFlagCnName", monthAnalysisVO.getOverseaFlagCnName());
        headMap.put("bgCnName", monthAnalysisVO.getBgCnName());
        headMap.put("regionCnName", monthAnalysisVO.getRegionCnName());
        headMap.put("repofficeCnName", monthAnalysisVO.getRepofficeCnName());
        headMap.put("mainFlagCnName", monthAnalysisVO.getMainFlagCnName());
        headMap.put("codeAttributes", monthAnalysisVO.getCodeAttributes());
        headMap.put("actualMonth", FcstIndustryUtil.getActualMonth(monthCostIdxDao.findActualMonth(monthAnalysisVO)));
        headMap.put("softwareMarkStr", "PSP".equals(monthAnalysisVO.getCostType()) ? CommonConstant.SOFTWARE_MARK + IndustryConstEnum.getSoftwareMark(monthAnalysisVO.getSoftwareMark()).getDesc() : "");
        return headMap;
    }
}