/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.relation;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.huawei.it.fcst.industry.index.dao.IDmDimCatgModlCegIctDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocVersionDao;
import com.huawei.it.fcst.industry.index.enums.IndustryConst;
import com.huawei.it.fcst.industry.index.impl.annual.StatisticsExcelService;
import com.huawei.it.fcst.industry.index.impl.config.ConfigDimensionService;
import com.huawei.it.fcst.industry.index.utils.ExcelUtil;
import com.huawei.it.fcst.industry.index.vo.common.DmFoiImpExpRecordVO;
import com.huawei.it.fcst.industry.index.vo.common.ExcelVO;
import com.huawei.it.fcst.industry.index.vo.common.HeaderVo;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.common.UploadInfoVO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoDTO;
import com.huawei.it.fcst.industry.index.vo.relation.DimensionInputVO;
import com.huawei.it.fcst.industry.index.vo.relation.DmDimCatgModlCegIctVO;
import com.huawei.it.fcst.industry.index.vo.relation.DmDimMaterialCodeVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.registry.service.IRegistryQueryService;
import com.huawei.it.jalor5.security.UserVO;
import com.sun.istack.NotNull;
import com.sun.istack.Nullable;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.core.MultivaluedMap;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * ConfigDimensionManageServiceTest Class
 *
 * <AUTHOR>
 * @since 2023/4/12
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class ConfigDimensionManageServiceTest {
    private static final Logger LOGGER = LoggerFactory.getLogger(ConfigDimensionService.class);

    @InjectMocks
    private ConfigDimensionManageService configDimensionManageService;

    @Mock
    private IDmDimCatgModlCegIctDao iDmDimCatgModlCegIctDao;

    @Mock
    private IDmFocVersionDao dmFocVersionDao;

    @Mock
    private IRegistryQueryService registryQueryService;

    @Mock
    private StatisticsExcelService statisticsExcelService;

    @Mock
    private ExcelUtil excelUtil;

    @Mock
    private Attachment attachment;

    @Before
    public void before() throws Exception {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);
    }

    @Test
    public void relationSave() {
        DimensionInputVO inputVO = new DimensionInputVO();
        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationSave(inputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationSave1Test() {
        DimensionInputVO inputVO = new DimensionInputVO();
        inputVO.setVersionId(11L);
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setCategoryCode("1005A");
        cegIctVO.setCategoryCnName("天线");
        cegIctVO.setL3CegCode("04081");
        cegIctVO.setL3CegCnName("总成配件");
        cegIctVO.setL3CegShortCnName("总成");
        cegIctVO.setL4CegCnName("总成配件-单天线");
        cegIctVO.setL4CegCode("14088");
        cegIctVO.setL4CegShortCnName("单天线");

        DmDimCatgModlCegIctVO cegIctVO2 = new DmDimCatgModlCegIctVO();
        cegIctVO2.setCategoryCode("1005B");
        cegIctVO2.setCategoryCnName("天线");
        cegIctVO2.setL3CegCode("04081");
        cegIctVO2.setL3CegCnName("总成配件");
        cegIctVO2.setL3CegShortCnName("总成");
        cegIctVO2.setL4CegCnName("总成配件-单天线");
        cegIctVO2.setL4CegCode("14088");
        cegIctVO2.setL4CegShortCnName("单天线");

        dimCatgModlCegIctList.add(cegIctVO);
        dimCatgModlCegIctList.add(cegIctVO2);

        inputVO.setDimCatgModlCegIctList(dimCatgModlCegIctList);

        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationSave(inputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationSave2Test() {
        DimensionInputVO inputVO = new DimensionInputVO();
        inputVO.setVersionId(11L);
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setCategoryCode("1005A");
        cegIctVO.setCategoryCnName("天线");
        cegIctVO.setL3CegCode("04081");
        cegIctVO.setL3CegCnName("总成配件");
        cegIctVO.setL3CegShortCnName("总成");
        cegIctVO.setL4CegCnName("总成配件-单天线");
        cegIctVO.setL4CegCode("14088");
        cegIctVO.setL4CegShortCnName("单天线");
        dimCatgModlCegIctList.add(cegIctVO);
        inputVO.setDimCatgModlCegIctList(dimCatgModlCegIctList);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO materialCodeVO = new DmDimMaterialCodeVO();
        materialCodeVO.setItemSubtypeCode("1155");
        materialCodeVO.setItemSubtypeCnName("元器");
        dimMaterialCodeD.add(materialCodeVO);

        List<Map> dmFocCatgCegIctDTOS=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("category_code", "1155");
        map.put("category_cn_name", "元器");
        dmFocCatgCegIctDTOS.add(map);
        when(iDmDimCatgModlCegIctDao.findDmDimCatgModlCegIctList(any())).thenReturn(dimCatgModlCegIctList);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        when(iDmDimCatgModlCegIctDao.findCatgCegIctList(any())).thenReturn(dmFocCatgCegIctDTOS);

        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationSave(inputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationSave3Test() {
        DimensionInputVO inputVO = new DimensionInputVO();
        inputVO.setVersionId(11L);
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setCategoryCode("1005A");
        cegIctVO.setCategoryCnName("天线");
        cegIctVO.setL3CegCode("04081");
        cegIctVO.setL3CegCnName("总成配件");
        cegIctVO.setL3CegShortCnName("总成");
        cegIctVO.setL4CegCnName("总成配件-单天线");
        cegIctVO.setL4CegCode("14088");
        cegIctVO.setL4CegShortCnName("单天线");
        DmDimCatgModlCegIctVO cegIctVO2 = new DmDimCatgModlCegIctVO();
        cegIctVO2.setCategoryCode("1005B");
        cegIctVO2.setCategoryCnName("天线");
        cegIctVO2.setL3CegCode("04081");
        cegIctVO2.setL3CegCnName("总成配件");
        cegIctVO2.setL3CegShortCnName("总成1");
        cegIctVO2.setL4CegCnName("总成配件-单天线");
        cegIctVO2.setL4CegCode("14088");
        cegIctVO2.setL4CegShortCnName("单天线1");

        dimCatgModlCegIctList.add(cegIctVO);
        dimCatgModlCegIctList.add(cegIctVO2);

        inputVO.setDimCatgModlCegIctList(dimCatgModlCegIctList);

        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationSave(inputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("检测到存在多个简称，系统仅允许唯一简称，请修改后保存！");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationSave4Test() {
        DimensionInputVO inputVO = new DimensionInputVO();
        inputVO.setVersionId(11L);
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setCategoryCode("1005A");
        cegIctVO.setCategoryCnName("天线");
        cegIctVO.setL3CegCode("04081");
        cegIctVO.setL3CegCnName("总成配件");
        cegIctVO.setL3CegShortCnName("总成");
        cegIctVO.setL4CegCnName("总成配件-单天线");
        cegIctVO.setL4CegCode("14088");
        cegIctVO.setL4CegShortCnName("单天线");
        DmDimCatgModlCegIctVO cegIctVO22 = new DmDimCatgModlCegIctVO();
        cegIctVO22.setCategoryCode("1005C");
        cegIctVO22.setCategoryCnName("天线");
        cegIctVO22.setL3CegCode("04081");
        cegIctVO22.setL3CegCnName("总成配件");
        cegIctVO22.setL3CegShortCnName("总成");
        cegIctVO22.setL4CegCnName("总成配件-单天线");
        cegIctVO22.setL4CegCode("14088");
        cegIctVO22.setL4CegShortCnName("单天线");
        cegIctVO22.setOldCategoryCode("1005D");
        dimCatgModlCegIctList.add(cegIctVO);
        dimCatgModlCegIctList.add(cegIctVO22);
        inputVO.setDimCatgModlCegIctList(dimCatgModlCegIctList);

        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO2 = new DmDimCatgModlCegIctVO();
        cegIctVO2.setCategoryCode("1005A");
        cegIctVO2.setCategoryCnName("天线");
        cegIctVO2.setL3CegCode("04082");
        cegIctVO2.setL3CegCnName("总成配件2");
        cegIctVO2.setL3CegShortCnName("总成2");
        cegIctVO2.setL4CegCnName("总成配件-单天线");
        cegIctVO2.setL4CegCode("14088");
        cegIctVO2.setL4CegShortCnName("单天线");
        DmDimCatgModlCegIctVO cegIctVO222 = new DmDimCatgModlCegIctVO();
        cegIctVO222.setCategoryCode("1005C");
        cegIctVO222.setCategoryCnName("天线");
        cegIctVO222.setL3CegCode("04082");
        cegIctVO222.setL3CegCnName("总成配件2");
        cegIctVO222.setL3CegShortCnName("总成2");
        cegIctVO222.setL4CegCnName("总成配件-单天线");
        cegIctVO222.setL4CegCode("14088");
        cegIctVO222.setL4CegShortCnName("单天线");
        dmDimCatgModlListWithVersion.add(cegIctVO2);
        dmDimCatgModlListWithVersion.add(cegIctVO222);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO materialCodeVO = new DmDimMaterialCodeVO();
        materialCodeVO.setItemSubtypeCode("1155");
        materialCodeVO.setItemSubtypeCnName("元器");
        dimMaterialCodeD.add(materialCodeVO);

        List<Map> dmFocCatgCegIctDTOS=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("category_code", "1155");
        map.put("category_cn_name", "元器");
        dmFocCatgCegIctDTOS.add(map);

        when(iDmDimCatgModlCegIctDao.findDmDimCatgModlCegIctList(any())).thenReturn(dmDimCatgModlListWithVersion);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        when(iDmDimCatgModlCegIctDao.findCatgCegIctList(any())).thenReturn(dmFocCatgCegIctDTOS);


        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationSave(inputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("检测到品类编码在系统中已存在，请修改后保存！");
        }
        Assert.assertNotNull(resultDataVO);
    }

    // 校验入参里同一个简称是否被不同的专家团/模块引用
    @Test
    public void relationSave41Test() {
        DimensionInputVO inputVO = new DimensionInputVO();
        inputVO.setVersionId(11L);
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setCategoryCode("1005A");
        cegIctVO.setCategoryCnName("天线");
        cegIctVO.setL3CegCode("04081");
        cegIctVO.setL3CegCnName("总成配件");
        cegIctVO.setL3CegShortCnName("总成");
        cegIctVO.setL4CegCode("04023");
        cegIctVO.setL4CegCnName("总成配件4");
        cegIctVO.setL4CegShortCnName("总成");
        dimCatgModlCegIctList.add(cegIctVO);

        DmDimCatgModlCegIctVO cegIctVO2 = new DmDimCatgModlCegIctVO();
        cegIctVO2.setCategoryCode("1005C");
        cegIctVO2.setCategoryCnName("天线");
        cegIctVO2.setL3CegCode("04083");
        cegIctVO2.setL3CegCnName("总成配件2");
        cegIctVO2.setL3CegShortCnName("总成2");
        cegIctVO2.setL4CegCode("040232");
        cegIctVO2.setL4CegCnName("总成配件42");
        cegIctVO2.setL4CegShortCnName("总成3");
        dimCatgModlCegIctList.add(cegIctVO2);
        inputVO.setDimCatgModlCegIctList(dimCatgModlCegIctList);

        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO22 = new DmDimCatgModlCegIctVO();
        cegIctVO22.setCategoryCode("1005A");
        cegIctVO22.setCategoryCnName("天线");
        cegIctVO22.setL3CegCode("04082");
        cegIctVO22.setL3CegCnName("总成配件2");
        cegIctVO22.setL3CegShortCnName("总成2");
        dmDimCatgModlListWithVersion.add(cegIctVO22);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO materialCodeVO = new DmDimMaterialCodeVO();
        materialCodeVO.setItemSubtypeCode("1155");
        materialCodeVO.setItemSubtypeCnName("元器");
        dimMaterialCodeD.add(materialCodeVO);

        List<Map> dmFocCatgCegIctDTOS=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("category_code", "1155");
        map.put("category_cn_name", "元器");
        dmFocCatgCegIctDTOS.add(map);

        when(iDmDimCatgModlCegIctDao.findDmDimCatgModlCegIctList(any())).thenReturn(dmDimCatgModlListWithVersion);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        when(iDmDimCatgModlCegIctDao.findCatgCegIctList(any())).thenReturn(dmFocCatgCegIctDTOS);


        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationSave(inputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("保存的品类有重复，请检查");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationSave42Test() {
        DimensionInputVO inputVO = new DimensionInputVO();
        inputVO.setVersionId(11L);
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setCategoryCode("1005A");
        cegIctVO.setCategoryCnName("天线");
        cegIctVO.setL3CegCode("04081");
        cegIctVO.setL3CegCnName("总成配件");
        cegIctVO.setL3CegShortCnName("总成");
        dimCatgModlCegIctList.add(cegIctVO);

        DmDimCatgModlCegIctVO cegIctVO2 = new DmDimCatgModlCegIctVO();
        cegIctVO2.setCategoryCode("1005A");
        cegIctVO2.setCategoryCnName("天线");
        cegIctVO2.setL3CegCode("04083");
        cegIctVO2.setL3CegCnName("总成配件2");
        cegIctVO2.setL3CegShortCnName("总成2");
        dimCatgModlCegIctList.add(cegIctVO2);
        inputVO.setDimCatgModlCegIctList(dimCatgModlCegIctList);

        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO22 = new DmDimCatgModlCegIctVO();
        cegIctVO22.setCategoryCode("1005A");
        cegIctVO22.setCategoryCnName("天线");
        cegIctVO22.setL3CegCode("04082");
        cegIctVO22.setL3CegCnName("总成配件2");
        cegIctVO22.setL3CegShortCnName("总成2");
        dmDimCatgModlListWithVersion.add(cegIctVO22);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO materialCodeVO = new DmDimMaterialCodeVO();
        materialCodeVO.setItemSubtypeCode("1155");
        materialCodeVO.setItemSubtypeCnName("元器");
        dimMaterialCodeD.add(materialCodeVO);

        List<Map> dmFocCatgCegIctDTOS=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("category_code", "1155");
        map.put("category_cn_name", "元器");
        dmFocCatgCegIctDTOS.add(map);

        when(iDmDimCatgModlCegIctDao.findDmDimCatgModlCegIctList(any())).thenReturn(dmDimCatgModlListWithVersion);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        when(iDmDimCatgModlCegIctDao.findCatgCegIctList(any())).thenReturn(dmFocCatgCegIctDTOS);


        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationSave(inputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("保存的品类有重复，请检查");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationSave44Test() {
        DimensionInputVO inputVO = new DimensionInputVO();
        inputVO.setVersionId(11L);
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setCategoryCode("1005C");
        cegIctVO.setCategoryCnName("天线3");
        cegIctVO.setL3CegCode("04081");
        cegIctVO.setL3CegCnName("总成配件");
        cegIctVO.setL3CegShortCnName("总成");
        cegIctVO.setL4CegCnName("总成配件-单天线");
        cegIctVO.setL4CegCode("14088");
        cegIctVO.setL4CegShortCnName("单天线");
        dimCatgModlCegIctList.add(cegIctVO);

        DmDimCatgModlCegIctVO cegIctVO22 = new DmDimCatgModlCegIctVO();
        cegIctVO22.setCategoryCode("1005B");
        cegIctVO22.setCategoryCnName("天线2");
        cegIctVO22.setL3CegCode("04082");
        cegIctVO22.setL3CegCnName("总成配件2");
        cegIctVO22.setL3CegShortCnName("总成");
        cegIctVO22.setL4CegCnName("总成配件-单天线2");
        cegIctVO22.setL4CegCode("14088");
        cegIctVO22.setL4CegShortCnName("单天线");
        dimCatgModlCegIctList.add(cegIctVO22);

        inputVO.setDimCatgModlCegIctList(dimCatgModlCegIctList);

        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO2 = new DmDimCatgModlCegIctVO();
        cegIctVO2.setCategoryCode("1005A");
        cegIctVO2.setCategoryCnName("天线");
        cegIctVO2.setL3CegCode("04082");
        cegIctVO2.setL3CegCnName("总成配件2");
        cegIctVO2.setL3CegShortCnName("总成2");
        cegIctVO2.setL4CegCnName("总成配件-单天线");
        cegIctVO2.setL4CegCode("14088");
        cegIctVO2.setL4CegShortCnName("单天线");
        dmDimCatgModlListWithVersion.add(cegIctVO2);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO materialCodeVO = new DmDimMaterialCodeVO();
        materialCodeVO.setItemSubtypeCode("1155");
        materialCodeVO.setItemSubtypeCnName("元器");
        dimMaterialCodeD.add(materialCodeVO);

        List<Map> dmFocCatgCegIctDTOS=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("category_code", "1155");
        map.put("category_cn_name", "元器");
        dmFocCatgCegIctDTOS.add(map);

        when(iDmDimCatgModlCegIctDao.findDmDimCatgModlCegIctList(any())).thenReturn(dmDimCatgModlListWithVersion);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        when(iDmDimCatgModlCegIctDao.findCatgCegIctList(any())).thenReturn(dmFocCatgCegIctDTOS);


        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationSave(inputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("检测到同一简称被多处引用，请修改后保存！");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationSave444Test() {
        DimensionInputVO inputVO = new DimensionInputVO();
        inputVO.setVersionId(11L);
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setCategoryCode("1005C");
        cegIctVO.setCategoryCnName("天线3");

        cegIctVO.setL3CegCode("04082");
        cegIctVO.setL3CegCnName("总成配件1");
        cegIctVO.setL3CegShortCnName("总成");
        cegIctVO.setL4CegCnName("总成配件单天线");
        cegIctVO.setL4CegCode("14088");
        cegIctVO.setL4CegShortCnName("单天线");
        dimCatgModlCegIctList.add(cegIctVO);

        inputVO.setDimCatgModlCegIctList(dimCatgModlCegIctList);

        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO2 = new DmDimCatgModlCegIctVO();
        cegIctVO2.setCategoryCode("1005A");
        cegIctVO2.setCategoryCnName("天线");
        cegIctVO2.setL3CegCode("04082");
        cegIctVO2.setL3CegCnName("总成配件2");
        cegIctVO2.setL3CegShortCnName("总成");
        cegIctVO2.setL4CegCnName("总成配件-单天线");
        cegIctVO2.setL4CegCode("14088");
        cegIctVO2.setL4CegShortCnName("单天线");
        dmDimCatgModlListWithVersion.add(cegIctVO2);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO materialCodeVO = new DmDimMaterialCodeVO();
        materialCodeVO.setItemSubtypeCode("1155");
        materialCodeVO.setItemSubtypeCnName("元器");
        dimMaterialCodeD.add(materialCodeVO);

        List<Map> dmFocCatgCegIctDTOS=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("category_code", "1155");
        map.put("category_cn_name", "元器");
        dmFocCatgCegIctDTOS.add(map);

        when(iDmDimCatgModlCegIctDao.findDmDimCatgModlCegIctList(any())).thenReturn(dmDimCatgModlListWithVersion);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        when(iDmDimCatgModlCegIctDao.findCatgCegIctList(any())).thenReturn(dmFocCatgCegIctDTOS);


        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationSave(inputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("检测到和系统已有简称重复，系统仅允许唯一简称，请修改后保存！");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationSave5Test() {
        DimensionInputVO inputVO = new DimensionInputVO();
        inputVO.setVersionId(11L);
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setCategoryCode("1005A");
        cegIctVO.setCategoryCnName("天线");
        cegIctVO.setL3CegCode("04081");
        cegIctVO.setL3CegCnName("总成配件");
        cegIctVO.setL3CegShortCnName("总成");
        cegIctVO.setL4CegCnName("总成配件-单天线");
        cegIctVO.setL4CegCode("14088");
        cegIctVO.setL4CegShortCnName("单天线");
        dimCatgModlCegIctList.add(cegIctVO);
        inputVO.setDimCatgModlCegIctList(dimCatgModlCegIctList);

        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO2 = new DmDimCatgModlCegIctVO();
        cegIctVO2.setCategoryCode("1005B");
        cegIctVO2.setCategoryCnName("天线配件");
        cegIctVO2.setL3CegCode("04081");
        cegIctVO2.setL3CegCnName("总成配件");
        cegIctVO2.setL3CegShortCnName("总成");
        dmDimCatgModlListWithVersion.add(cegIctVO2);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO materialCodeVO = new DmDimMaterialCodeVO();
        materialCodeVO.setItemSubtypeCode("1155");
        materialCodeVO.setItemSubtypeCnName("元器");
        dimMaterialCodeD.add(materialCodeVO);

        List<Map> dmFocCatgCegIctDTOS=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("category_code", "1155");
        map.put("category_cn_name", "元器");
        dmFocCatgCegIctDTOS.add(map);

        when(iDmDimCatgModlCegIctDao.findDmDimCatgModlCegIctList(any())).thenReturn(dmDimCatgModlListWithVersion);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        when(iDmDimCatgModlCegIctDao.findCatgCegIctList(any())).thenReturn(dmFocCatgCegIctDTOS);


        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationSave(inputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationSave6Test() {
        DimensionInputVO inputVO = new DimensionInputVO();
        inputVO.setVersionId(11L);
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setCategoryCode("1005A");
        cegIctVO.setCategoryCnName("天线");
        cegIctVO.setL3CegCode("04081");
        cegIctVO.setL3CegCnName("总成配件");
        cegIctVO.setL3CegShortCnName("总成");
        dimCatgModlCegIctList.add(cegIctVO);

        DmDimCatgModlCegIctVO cegIctVO3 = new DmDimCatgModlCegIctVO();
        cegIctVO3.setCategoryCode("1005A");
        cegIctVO3.setCategoryCnName("天线");
        cegIctVO3.setL3CegCode("04081");
        cegIctVO3.setL3CegCnName("总成配件");
        cegIctVO3.setL3CegShortCnName("总成");
        dimCatgModlCegIctList.add(cegIctVO3);

        inputVO.setDimCatgModlCegIctList(dimCatgModlCegIctList);

        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO2 = new DmDimCatgModlCegIctVO();
        cegIctVO2.setCategoryCode("1005B");
        cegIctVO2.setCategoryCnName("天线配件");
        cegIctVO2.setL3CegCode("04081");
        cegIctVO2.setL3CegCnName("总成配件");
        cegIctVO2.setL3CegShortCnName("总成");
        dmDimCatgModlListWithVersion.add(cegIctVO2);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO materialCodeVO = new DmDimMaterialCodeVO();
        materialCodeVO.setItemSubtypeCode("1155");
        materialCodeVO.setItemSubtypeCnName("元器");
        dimMaterialCodeD.add(materialCodeVO);

        List<Map> dmFocCatgCegIctDTOS=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("category_code", "1155");
        map.put("category_cn_name", "元器");
        dmFocCatgCegIctDTOS.add(map);

        when(iDmDimCatgModlCegIctDao.findDmDimCatgModlCegIctList(any())).thenReturn(dmDimCatgModlListWithVersion);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        when(iDmDimCatgModlCegIctDao.findCatgCegIctList(any())).thenReturn(dmFocCatgCegIctDTOS);


        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationSave(inputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("保存的数据有重复，请检查");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationSave7Test() throws Exception {
        DimensionInputVO inputVO = new DimensionInputVO();
        inputVO.setVersionId(11L);
        inputVO.setIndustryOrg("ICT");
        inputVO.setTablePreFix("dm_foc");
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setCategoryCode("1005B");
        cegIctVO.setCategoryCnName("天线配件");

        cegIctVO.setL3CegCode("04081");
        cegIctVO.setL3CegCnName("总成配件2");
        cegIctVO.setL3CegShortCnName("总成");
        cegIctVO.setL4CegCnName("总成配件-单天线");
        cegIctVO.setL4CegCode("14088");
        cegIctVO.setL4CegShortCnName("单天线2");
        cegIctVO.setOldCategoryCode("1005B");
        dimCatgModlCegIctList.add(cegIctVO);
        inputVO.setDimCatgModlCegIctList(dimCatgModlCegIctList);

        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO2 = new DmDimCatgModlCegIctVO();
        cegIctVO2.setCategoryCode("1005B");
        cegIctVO2.setCategoryCnName("天线配件");
        cegIctVO2.setL3CegCode("04081");
        cegIctVO2.setL3CegCnName("总成配件");
        cegIctVO2.setL3CegShortCnName("总成1");
        cegIctVO2.setL4CegCnName("总成配件-单天线");
        cegIctVO2.setL4CegCode("14088");
        cegIctVO2.setL4CegShortCnName("单天线");
        dmDimCatgModlListWithVersion.add(cegIctVO2);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO materialCodeVO = new DmDimMaterialCodeVO();
        materialCodeVO.setItemSubtypeCode("1005B");
        materialCodeVO.setItemSubtypeCnName("天线配件");
        dimMaterialCodeD.add(materialCodeVO);
        DmDimMaterialCodeVO materialCodeVO2 = new DmDimMaterialCodeVO();
        materialCodeVO2.setItemSubtypeCode("1155");
        materialCodeVO2.setItemSubtypeCnName("元器");
        dimMaterialCodeD.add(materialCodeVO2);

        List<Map> dmFocCatgCegIctDTOS=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("category_code", "1155");
        map.put("category_cn_name", "元器");
        dmFocCatgCegIctDTOS.add(map);

        List<DmDimCatgModlCegIctVO> dimCegCodeD=new ArrayList<>();
        dimCegCodeD.add(cegIctVO);

        DmFocVersionInfoDTO build = DmFocVersionInfoDTO.builder()
                .dataType(IndustryConst.DataType.DIM.getValue())
                .status(IndustryConst.STATUS.NOT_STATUS.getValue())
                .tablePreFix(inputVO.getTablePreFix())
                .build();
        List<DmFocVersionInfoDTO> planVersionVOList  = new ArrayList<>();
        planVersionVOList.add(build);

        when(iDmDimCatgModlCegIctDao.findDmDimCatgModlCegIctList(any())).thenReturn(dmDimCatgModlListWithVersion);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        when(iDmDimCatgModlCegIctDao.findDimCegCodeD()).thenReturn(dimCegCodeD);
        when(iDmDimCatgModlCegIctDao.findCatgCegIctList(any())).thenReturn(dmFocCatgCegIctDTOS);

        when(dmFocVersionDao.findDmFocPlanVersionVOList(build)).thenReturn(planVersionVOList);

        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationSave(inputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    // 校验维度数据
    @Test
    public void relationSave8Test() {
        DimensionInputVO inputVO = new DimensionInputVO();
        inputVO.setVersionId(11L);
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setCategoryCode("1005B");
        cegIctVO.setCategoryCnName("天线配件");
        cegIctVO.setL3CegCode("04081");
        cegIctVO.setL3CegCnName("总成配件");
        cegIctVO.setL3CegShortCnName("总成");
        cegIctVO.setL4CegCnName("总成配件-单天线");
        cegIctVO.setL4CegCode("14088");
        cegIctVO.setL4CegShortCnName("单天线");
        cegIctVO.setOldCategoryCode("1005B");
        dimCatgModlCegIctList.add(cegIctVO);
        inputVO.setDimCatgModlCegIctList(dimCatgModlCegIctList);

        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO2 = new DmDimCatgModlCegIctVO();
        cegIctVO2.setCategoryCode("1005B");
        cegIctVO2.setCategoryCnName("天线配件");
        cegIctVO2.setL3CegCode("04081");
        cegIctVO2.setL3CegCnName("总成配件");
        cegIctVO2.setL3CegShortCnName("总成1");
        dmDimCatgModlListWithVersion.add(cegIctVO2);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO materialCodeVO = new DmDimMaterialCodeVO();
        materialCodeVO.setItemSubtypeCode("1005D");
        materialCodeVO.setItemSubtypeCnName("天线配件");
        dimMaterialCodeD.add(materialCodeVO);
        DmDimMaterialCodeVO materialCodeVO2 = new DmDimMaterialCodeVO();
        materialCodeVO2.setItemSubtypeCode("1155");
        materialCodeVO2.setItemSubtypeCnName("元器");
        dimMaterialCodeD.add(materialCodeVO2);

        List<Map> dmFocCatgCegIctDTOS=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("category_code", "1155");
        map.put("category_cn_name", "元器");
        dmFocCatgCegIctDTOS.add(map);

        List<DmDimCatgModlCegIctVO> dimCegCodeD=new ArrayList<>();
        dimCegCodeD.add(cegIctVO);

        when(iDmDimCatgModlCegIctDao.findDmDimCatgModlCegIctList(any())).thenReturn(dmDimCatgModlListWithVersion);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        when(iDmDimCatgModlCegIctDao.findDimCegCodeD()).thenReturn(dimCegCodeD);
        when(iDmDimCatgModlCegIctDao.findCatgCegIctList(any())).thenReturn(dmFocCatgCegIctDTOS);


        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationSave(inputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationSave81Test() {
        DimensionInputVO inputVO = new DimensionInputVO();
        inputVO.setVersionId(11L);
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setL3CegCode("04081");
        cegIctVO.setL3CegCnName("总成配件");
        cegIctVO.setL3CegShortCnName("总成");
        cegIctVO.setL4CegCnName("总成配件-单天线");
        cegIctVO.setL4CegCode("14088");
        cegIctVO.setL4CegShortCnName("单天线");
        cegIctVO.setOldCategoryCode("1005B");
        dimCatgModlCegIctList.add(cegIctVO);


        inputVO.setDimCatgModlCegIctList(dimCatgModlCegIctList);

        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO2 = new DmDimCatgModlCegIctVO();
        cegIctVO2.setCategoryCode("1005B");
        cegIctVO2.setCategoryCnName("天线配件");
        cegIctVO2.setL3CegCode("04081");
        cegIctVO2.setL3CegCnName("总成配件");
        cegIctVO2.setL3CegShortCnName("总成");
        dmDimCatgModlListWithVersion.add(cegIctVO2);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO materialCodeVO = new DmDimMaterialCodeVO();
        materialCodeVO.setItemSubtypeCode("1005D");
        materialCodeVO.setItemSubtypeCnName("天线配件");
        dimMaterialCodeD.add(materialCodeVO);
        DmDimMaterialCodeVO materialCodeVO2 = new DmDimMaterialCodeVO();
        materialCodeVO2.setItemSubtypeCode("1155");
        materialCodeVO2.setItemSubtypeCnName("元器");
        dimMaterialCodeD.add(materialCodeVO2);

        List<Map> dmFocCatgCegIctDTOS=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("category_code", "1155");
        map.put("category_cn_name", "元器");
        dmFocCatgCegIctDTOS.add(map);

        List<DmDimCatgModlCegIctVO> dimCegCodeD=new ArrayList<>();
        dimCegCodeD.add(cegIctVO);

        when(iDmDimCatgModlCegIctDao.findDmDimCatgModlCegIctList(any())).thenReturn(dmDimCatgModlListWithVersion);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        when(iDmDimCatgModlCegIctDao.findDimCegCodeD()).thenReturn(dimCegCodeD);
        when(iDmDimCatgModlCegIctDao.findCatgCegIctList(any())).thenReturn(dmFocCatgCegIctDTOS);


        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationSave(inputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationSave82Test() {
        DimensionInputVO inputVO = new DimensionInputVO();
        inputVO.setVersionId(11L);
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setCategoryCode("1005B");
        cegIctVO.setCategoryCnName("天线配件A");
        cegIctVO.setL3CegCode("04081");
        cegIctVO.setL3CegCnName("总成配件8");
        cegIctVO.setL3CegShortCnName("总成");
        cegIctVO.setL4CegCnName("总成配件-单天线");
        cegIctVO.setL4CegCode("14088");
        cegIctVO.setL4CegShortCnName("单天线");
        cegIctVO.setOldCategoryCode("1005B");
        dimCatgModlCegIctList.add(cegIctVO);
        inputVO.setDimCatgModlCegIctList(dimCatgModlCegIctList);

        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO2 = new DmDimCatgModlCegIctVO();
        cegIctVO2.setCategoryCode("1005B");
        cegIctVO2.setCategoryCnName("天线配件");
        cegIctVO2.setL3CegCode("04081");
        cegIctVO2.setL3CegCnName("总成配件");
        cegIctVO2.setL3CegShortCnName("总成1");
        dmDimCatgModlListWithVersion.add(cegIctVO2);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO materialCodeVO = new DmDimMaterialCodeVO();
        materialCodeVO.setItemSubtypeCode("1005B");
        materialCodeVO.setItemSubtypeCnName("天线配件");
        dimMaterialCodeD.add(materialCodeVO);
        DmDimMaterialCodeVO materialCodeVO2 = new DmDimMaterialCodeVO();
        materialCodeVO2.setItemSubtypeCode("1155");
        materialCodeVO2.setItemSubtypeCnName("元器");
        dimMaterialCodeD.add(materialCodeVO2);

        List<Map> dmFocCatgCegIctDTOS=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("category_code", "1155");
        map.put("category_cn_name", "元器");
        dmFocCatgCegIctDTOS.add(map);

        List<DmDimCatgModlCegIctVO> dimCegCodeD=new ArrayList<>();
        dimCegCodeD.add(cegIctVO2);

        when(iDmDimCatgModlCegIctDao.findDmDimCatgModlCegIctList(any())).thenReturn(dmDimCatgModlListWithVersion);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        when(iDmDimCatgModlCegIctDao.findDimCegCodeD()).thenReturn(dimCegCodeD);
        when(iDmDimCatgModlCegIctDao.findCatgCegIctList(any())).thenReturn(dmFocCatgCegIctDTOS);


        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationSave(inputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationSave83Test() {
        DimensionInputVO inputVO = new DimensionInputVO();
        inputVO.setVersionId(11L);
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setCategoryCode("1005B");
        cegIctVO.setCategoryCnName("天线配件A");
        cegIctVO.setL3CegCode("04081");
        cegIctVO.setL3CegCnName("总成配件8");
        cegIctVO.setL3CegShortCnName("总成");
        cegIctVO.setL4CegCnName("总成配件-单天线");
        cegIctVO.setL4CegCode("14088");
        cegIctVO.setL4CegShortCnName("单天线");
        cegIctVO.setOldCategoryCode("1005B");
        dimCatgModlCegIctList.add(cegIctVO);
        inputVO.setDimCatgModlCegIctList(dimCatgModlCegIctList);

        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO2 = new DmDimCatgModlCegIctVO();
        cegIctVO2.setCategoryCode("1005B");
        cegIctVO2.setCategoryCnName("天线配件");
        cegIctVO2.setL3CegCode("04081");
        cegIctVO2.setL3CegCnName("总成配件");
        cegIctVO2.setL3CegShortCnName("总成1");

        DmDimCatgModlCegIctVO cegIctVO3 = new DmDimCatgModlCegIctVO();
        cegIctVO3.setCategoryCode("1005B");
        cegIctVO3.setCategoryCnName("天线配件A");
        cegIctVO3.setL3CegCode("04081");
        cegIctVO3.setL3CegCnName("总成配件8");
        cegIctVO3.setL3CegShortCnName("总成");

        dmDimCatgModlListWithVersion.add(cegIctVO2);
        dmDimCatgModlListWithVersion.add(cegIctVO3);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO materialCodeVO = new DmDimMaterialCodeVO();
        materialCodeVO.setItemSubtypeCode("1005B");
        materialCodeVO.setItemSubtypeCnName("天线配件");
        dimMaterialCodeD.add(materialCodeVO);
        DmDimMaterialCodeVO materialCodeVO2 = new DmDimMaterialCodeVO();
        materialCodeVO2.setItemSubtypeCode("1155");
        materialCodeVO2.setItemSubtypeCnName("元器");
        dimMaterialCodeD.add(materialCodeVO2);

        List<Map> dmFocCatgCegIctDTOS=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("category_code", "1155");
        map.put("category_cn_name", "元器");
        dmFocCatgCegIctDTOS.add(map);

        List<DmDimCatgModlCegIctVO> dimCegCodeD=new ArrayList<>();
        dimCegCodeD.add(cegIctVO2);

        when(iDmDimCatgModlCegIctDao.findDmDimCatgModlCegIctList(any())).thenReturn(dmDimCatgModlListWithVersion);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        when(iDmDimCatgModlCegIctDao.findDimCegCodeD()).thenReturn(dimCegCodeD);
        when(iDmDimCatgModlCegIctDao.findCatgCegIctList(any())).thenReturn(dmFocCatgCegIctDTOS);


        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationSave(inputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationSave84Test() {
        DimensionInputVO inputVO = new DimensionInputVO();
        inputVO.setVersionId(11L);
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setL3CegCnName("总成配件8");
        cegIctVO.setL3CegShortCnName("总成");
        cegIctVO.setOldCategoryCode("1005B");
        cegIctVO.setL4CegCnName("总成配件-单天线");
        cegIctVO.setL4CegCode("14088");
        cegIctVO.setL4CegShortCnName("单天线");
        dimCatgModlCegIctList.add(cegIctVO);
        inputVO.setDimCatgModlCegIctList(dimCatgModlCegIctList);

        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO2 = new DmDimCatgModlCegIctVO();
        cegIctVO2.setCategoryCode("1005B");
        cegIctVO2.setCategoryCnName("天线配件");
        cegIctVO2.setL3CegCode("04081");
        cegIctVO2.setL3CegCnName("总成配件");
        cegIctVO2.setL3CegShortCnName("总成1");

        DmDimCatgModlCegIctVO cegIctVO3 = new DmDimCatgModlCegIctVO();
        cegIctVO3.setCategoryCode("1005B");
        cegIctVO3.setCategoryCnName("天线配件A");
        cegIctVO3.setL3CegCode("04081");
        cegIctVO3.setL3CegCnName("总成配件8");
        cegIctVO3.setL3CegShortCnName("总成");

        dmDimCatgModlListWithVersion.add(cegIctVO2);
        dmDimCatgModlListWithVersion.add(cegIctVO3);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO materialCodeVO = new DmDimMaterialCodeVO();
        materialCodeVO.setItemSubtypeCode("1005B");
        materialCodeVO.setItemSubtypeCnName("天线配件");
        dimMaterialCodeD.add(materialCodeVO);
        DmDimMaterialCodeVO materialCodeVO2 = new DmDimMaterialCodeVO();
        materialCodeVO2.setItemSubtypeCode("1155");
        materialCodeVO2.setItemSubtypeCnName("元器");
        dimMaterialCodeD.add(materialCodeVO2);

        List<Map> dmFocCatgCegIctDTOS=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("category_code", "1155");
        map.put("category_cn_name", "元器");
        dmFocCatgCegIctDTOS.add(map);

        List<DmDimCatgModlCegIctVO> dimCegCodeD=new ArrayList<>();
        dimCegCodeD.add(cegIctVO2);

        when(iDmDimCatgModlCegIctDao.findDmDimCatgModlCegIctList(any())).thenReturn(dmDimCatgModlListWithVersion);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        when(iDmDimCatgModlCegIctDao.findDimCegCodeD()).thenReturn(dimCegCodeD);
        when(iDmDimCatgModlCegIctDao.findCatgCegIctList(any())).thenReturn(dmFocCatgCegIctDTOS);


        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationSave(inputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationSave85Test() {
        DimensionInputVO inputVO = new DimensionInputVO();
        inputVO.setVersionId(11L);
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setOldCategoryCode("1005B");
        dimCatgModlCegIctList.add(cegIctVO);
        inputVO.setDimCatgModlCegIctList(dimCatgModlCegIctList);

        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationSave(inputVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    // 操作未刷新
    @Test
    public void relationSave9Test() {
        DimensionInputVO inputVO = new DimensionInputVO();
        inputVO.setVersionId(11L);
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setCategoryCode("1005B");
        cegIctVO.setCategoryCnName("天线配件");
        cegIctVO.setL3CegCode("04081");
        cegIctVO.setL3CegCnName("总成配件");
        cegIctVO.setL3CegShortCnName("总成");
        cegIctVO.setL4CegCnName("总成配件-单天线");
        cegIctVO.setL4CegCode("14088");
        cegIctVO.setL4CegShortCnName("单天线");
        cegIctVO.setOldCategoryCode("1005B");
        dimCatgModlCegIctList.add(cegIctVO);
        inputVO.setDimCatgModlCegIctList(dimCatgModlCegIctList);

        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO2 = new DmDimCatgModlCegIctVO();
        cegIctVO2.setCategoryCode("1005B");
        cegIctVO2.setCategoryCnName("天线配件");
        cegIctVO2.setL3CegCode("04082");
        cegIctVO2.setL3CegCnName("总成配件2");
        cegIctVO2.setL3CegShortCnName("总成1");
        dmDimCatgModlListWithVersion.add(cegIctVO2);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO materialCodeVO = new DmDimMaterialCodeVO();
        materialCodeVO.setItemSubtypeCode("1005B");
        materialCodeVO.setItemSubtypeCnName("天线配件");
        dimMaterialCodeD.add(materialCodeVO);
        DmDimMaterialCodeVO materialCodeVO2 = new DmDimMaterialCodeVO();
        materialCodeVO2.setItemSubtypeCode("1155");
        materialCodeVO2.setItemSubtypeCnName("元器");
        dimMaterialCodeD.add(materialCodeVO2);

        List<Map> dmFocCatgCegIctDTOS=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("category_code", "1155");
        map.put("category_cn_name", "元器");
        dmFocCatgCegIctDTOS.add(map);

        List<DmDimCatgModlCegIctVO> dimCegCodeD=new ArrayList<>();
        dimCegCodeD.add(cegIctVO);

        List<DmFocVersionInfoDTO> versionVOList=new ArrayList<>();
        DmFocVersionInfoDTO versionVO = new DmFocVersionInfoDTO();
        versionVO.setVersion("20230501");
        versionVO.setVersionId(11L);
        versionVOList.add(versionVO);

        when(iDmDimCatgModlCegIctDao.findDmDimCatgModlCegIctList(any())).thenReturn(dmDimCatgModlListWithVersion);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        when(iDmDimCatgModlCegIctDao.findDimCegCodeD()).thenReturn(dimCegCodeD);
        when(iDmDimCatgModlCegIctDao.findCatgCegIctList(any())).thenReturn(dmFocCatgCegIctDTOS);
        when(dmFocVersionDao.findDmFocPlanVersionVOList(any())).thenReturn(versionVOList);


        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationSave(inputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationSave91Test() {
        DimensionInputVO inputVO = new DimensionInputVO();
        inputVO.setVersionId(11L);
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setCategoryCode("1005B");
        cegIctVO.setCategoryCnName("天线配件");
        cegIctVO.setL3CegCode("04081");
        cegIctVO.setL3CegCnName("总成配件");
        cegIctVO.setL3CegShortCnName("总成");
        cegIctVO.setL4CegCnName("总成配件-单天线");
        cegIctVO.setL4CegCode("14088");
        cegIctVO.setL4CegShortCnName("单天线");
        cegIctVO.setOldCategoryCode("1005B");
        dimCatgModlCegIctList.add(cegIctVO);
        inputVO.setDimCatgModlCegIctList(dimCatgModlCegIctList);

        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO2 = new DmDimCatgModlCegIctVO();
        cegIctVO2.setCategoryCode("1005A");
        cegIctVO2.setCategoryCnName("天线配件2");
        cegIctVO2.setL3CegCode("04082");
        cegIctVO2.setL3CegCnName("总成配件2");
        cegIctVO2.setL3CegShortCnName("总成1");
        dmDimCatgModlListWithVersion.add(cegIctVO2);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO materialCodeVO = new DmDimMaterialCodeVO();
        materialCodeVO.setItemSubtypeCode("1005B");
        materialCodeVO.setItemSubtypeCnName("天线配件");
        dimMaterialCodeD.add(materialCodeVO);
        DmDimMaterialCodeVO materialCodeVO2 = new DmDimMaterialCodeVO();
        materialCodeVO2.setItemSubtypeCode("1155");
        materialCodeVO2.setItemSubtypeCnName("元器");
        dimMaterialCodeD.add(materialCodeVO2);

        List<Map> dmFocCatgCegIctDTOS=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("category_code", "1155");
        map.put("category_cn_name", "元器");
        dmFocCatgCegIctDTOS.add(map);

        List<DmDimCatgModlCegIctVO> dimCegCodeD=new ArrayList<>();
        dimCegCodeD.add(cegIctVO);

        List<DmFocVersionInfoDTO> versionVOList=new ArrayList<>();
        DmFocVersionInfoDTO versionVO = new DmFocVersionInfoDTO();
        versionVO.setVersion("20230501");
        versionVO.setVersionId(11L);
        versionVOList.add(versionVO);

        when(iDmDimCatgModlCegIctDao.findDmDimCatgModlCegIctList(any())).thenReturn(dmDimCatgModlListWithVersion);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        when(iDmDimCatgModlCegIctDao.findDimCegCodeD()).thenReturn(dimCegCodeD);
        when(iDmDimCatgModlCegIctDao.findCatgCegIctList(any())).thenReturn(dmFocCatgCegIctDTOS);
        when(dmFocVersionDao.findDmFocPlanVersionVOList(any())).thenReturn(versionVOList);


        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationSave(inputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationSave92Test() {
        DimensionInputVO inputVO = new DimensionInputVO();
        inputVO.setVersionId(11L);
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setCategoryCode("1005B");
        cegIctVO.setCategoryCnName("天线配件");
        cegIctVO.setL3CegCode("04081");
        cegIctVO.setL3CegCnName("总成配件");
        cegIctVO.setL3CegShortCnName("总成");
        cegIctVO.setL4CegCnName("总成配件-单天线");
        cegIctVO.setL4CegCode("14088");
        cegIctVO.setL4CegShortCnName("单天线");
        dimCatgModlCegIctList.add(cegIctVO);


        inputVO.setDimCatgModlCegIctList(dimCatgModlCegIctList);

        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO2 = new DmDimCatgModlCegIctVO();
        cegIctVO2.setCategoryCode("1005A");
        cegIctVO2.setCategoryCnName("天线配件2");
        cegIctVO2.setL3CegCode("04082");
        cegIctVO2.setL3CegCnName("总成配件2");
        cegIctVO2.setL3CegShortCnName("总成1");
        dmDimCatgModlListWithVersion.add(cegIctVO2);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO materialCodeVO = new DmDimMaterialCodeVO();
        materialCodeVO.setItemSubtypeCode("1005B");
        materialCodeVO.setItemSubtypeCnName("天线配件");
        dimMaterialCodeD.add(materialCodeVO);
        DmDimMaterialCodeVO materialCodeVO2 = new DmDimMaterialCodeVO();
        materialCodeVO2.setItemSubtypeCode("1155");
        materialCodeVO2.setItemSubtypeCnName("元器");
        dimMaterialCodeD.add(materialCodeVO2);

        List<Map> dmFocCatgCegIctDTOS=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("category_code", "1155");
        map.put("category_cn_name", "元器");
        dmFocCatgCegIctDTOS.add(map);

        List<DmDimCatgModlCegIctVO> dimCegCodeD=new ArrayList<>();
        dimCegCodeD.add(cegIctVO);

        List<DmFocVersionInfoDTO> versionVOList=new ArrayList<>();
        DmFocVersionInfoDTO versionVO = new DmFocVersionInfoDTO();
        versionVO.setVersion("20230501");
        versionVO.setVersionId(11L);
        versionVOList.add(versionVO);

        when(iDmDimCatgModlCegIctDao.findDmDimCatgModlCegIctList(any())).thenReturn(dmDimCatgModlListWithVersion);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        when(iDmDimCatgModlCegIctDao.findDimCegCodeD()).thenReturn(dimCegCodeD);
        when(iDmDimCatgModlCegIctDao.findCatgCegIctList(any())).thenReturn(dmFocCatgCegIctDTOS);
        when(dmFocVersionDao.findDmFocPlanVersionVOList(any())).thenReturn(versionVOList);


        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationSave(inputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    // 操作已刷新（无未刷新）
    @Test
    public void relationSave93Test() {
        DimensionInputVO inputVO = new DimensionInputVO();
        inputVO.setVersionId(11L);
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setCategoryCode("1005B");
        cegIctVO.setCategoryCnName("天线配件");
        cegIctVO.setL3CegCode("04081");
        cegIctVO.setL3CegCnName("总成配件");
        cegIctVO.setL3CegShortCnName("总成");
        cegIctVO.setL4CegCnName("总成配件-单天线");
        cegIctVO.setL4CegCode("14088");
        cegIctVO.setL4CegShortCnName("单天线");
        dimCatgModlCegIctList.add(cegIctVO);
        inputVO.setDimCatgModlCegIctList(dimCatgModlCegIctList);

        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO2 = new DmDimCatgModlCegIctVO();
        cegIctVO2.setCategoryCode("1005A");
        cegIctVO2.setCategoryCnName("天线配件2");
        cegIctVO2.setL3CegCode("04082");
        cegIctVO2.setL3CegCnName("总成配件2");
        cegIctVO2.setL3CegShortCnName("总成1");
        dmDimCatgModlListWithVersion.add(cegIctVO2);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO materialCodeVO = new DmDimMaterialCodeVO();
        materialCodeVO.setItemSubtypeCode("1005B");
        materialCodeVO.setItemSubtypeCnName("天线配件");
        dimMaterialCodeD.add(materialCodeVO);
        DmDimMaterialCodeVO materialCodeVO2 = new DmDimMaterialCodeVO();
        materialCodeVO2.setItemSubtypeCode("1155");
        materialCodeVO2.setItemSubtypeCnName("元器");
        dimMaterialCodeD.add(materialCodeVO2);

        List<Map> dmFocCatgCegIctDTOS=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("category_code", "1155");
        map.put("category_cn_name", "元器");
        dmFocCatgCegIctDTOS.add(map);

        List<DmDimCatgModlCegIctVO> dimCegCodeD=new ArrayList<>();
        dimCegCodeD.add(cegIctVO);

        List<DmFocVersionInfoDTO> versionVOList=new ArrayList<>();
        DmFocVersionInfoDTO versionVO = new DmFocVersionInfoDTO();
        versionVO.setVersion("20230501");
        versionVO.setVersionId(11L);
        versionVOList.add(versionVO);

        when(iDmDimCatgModlCegIctDao.findDmDimCatgModlCegIctList(any())).thenReturn(dmDimCatgModlListWithVersion);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        when(iDmDimCatgModlCegIctDao.findDimCegCodeD()).thenReturn(dimCegCodeD);
        when(iDmDimCatgModlCegIctDao.findCatgCegIctList(any())).thenReturn(dmFocCatgCegIctDTOS);
       // when(iDmFoiPlanVersionDao.findDmFoiPlanVersionVOList(any())).thenReturn(versionVOList);


        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationSave(inputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    // 操作已刷新（无未刷新）
    @Test
    public void relationSave94Test() {
        DimensionInputVO inputVO = new DimensionInputVO();
        inputVO.setVersionId(11L);
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setCategoryCode("1005B");
        cegIctVO.setCategoryCnName("天线配件");
        cegIctVO.setL3CegCode("04081");
        cegIctVO.setL3CegCnName("总成配件");
        cegIctVO.setL3CegShortCnName("总成");
        cegIctVO.setL4CegCnName("总成配件-单天线");
        cegIctVO.setL4CegCode("14088");
        cegIctVO.setL4CegShortCnName("单天线");
        cegIctVO.setOldCategoryCode("1005A");
        dimCatgModlCegIctList.add(cegIctVO);


        inputVO.setDimCatgModlCegIctList(dimCatgModlCegIctList);

        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO2 = new DmDimCatgModlCegIctVO();
        cegIctVO2.setCategoryCode("1005A");
        cegIctVO2.setCategoryCnName("天线配件2");
        cegIctVO2.setL3CegCode("04082");
        cegIctVO2.setL3CegCnName("总成配件");
        cegIctVO2.setL3CegShortCnName("总成1");
        cegIctVO2.setL4CegCnName("总成配件-单天线");
        cegIctVO2.setL4CegCode("14088");
        cegIctVO2.setL4CegShortCnName("单天线");
        dmDimCatgModlListWithVersion.add(cegIctVO2);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO materialCodeVO = new DmDimMaterialCodeVO();
        materialCodeVO.setItemSubtypeCode("1005B");
        materialCodeVO.setItemSubtypeCnName("天线配件");
        dimMaterialCodeD.add(materialCodeVO);
        DmDimMaterialCodeVO materialCodeVO2 = new DmDimMaterialCodeVO();
        materialCodeVO2.setItemSubtypeCode("1155");
        materialCodeVO2.setItemSubtypeCnName("元器");
        dimMaterialCodeD.add(materialCodeVO2);

        List<Map> dmFocCatgCegIctDTOS=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("category_code", "1155");
        map.put("category_cn_name", "元器");
        dmFocCatgCegIctDTOS.add(map);

        List<DmDimCatgModlCegIctVO> dimCegCodeD=new ArrayList<>();
        dimCegCodeD.add(cegIctVO);

        List<DmFocVersionInfoDTO> versionVOList=new ArrayList<>();
        DmFocVersionInfoDTO versionVO = new DmFocVersionInfoDTO();
        versionVO.setVersion("20230501");
        versionVO.setVersionId(11L);
        versionVOList.add(versionVO);

        when(iDmDimCatgModlCegIctDao.findDmDimCatgModlCegIctList(any())).thenReturn(dmDimCatgModlListWithVersion);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        when(iDmDimCatgModlCegIctDao.findDimCegCodeD()).thenReturn(dimCegCodeD);
        when(iDmDimCatgModlCegIctDao.findCatgCegIctList(any())).thenReturn(dmFocCatgCegIctDTOS);
        // when(iDmFoiPlanVersionDao.findDmFoiPlanVersionVOList(any())).thenReturn(versionVOList);


        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationSave(inputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    // 操作未刷新
    @Test
    public void relationSave10Test() {
        DimensionInputVO inputVO = new DimensionInputVO();
        inputVO.setVersionId(11L);
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setCategoryCode("1005B");
        cegIctVO.setCategoryCnName("天线配件");

        cegIctVO.setL3CegCode("04082");
        cegIctVO.setL3CegCnName("总成配件2");
        cegIctVO.setL3CegShortCnName("总成");
        cegIctVO.setL4CegCnName("总成配件-单天线");
        cegIctVO.setL4CegCode("14088");
        cegIctVO.setL4CegShortCnName("单天线");
        cegIctVO.setOldCategoryCode("1005B");
        dimCatgModlCegIctList.add(cegIctVO);
        inputVO.setDimCatgModlCegIctList(dimCatgModlCegIctList);

        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO2 = new DmDimCatgModlCegIctVO();
        cegIctVO2.setCategoryCode("1005B");
        cegIctVO2.setCategoryCnName("天线配件");
        cegIctVO2.setL3CegCode("04081");
        cegIctVO2.setL3CegCnName("总成配件");
        cegIctVO2.setL3CegShortCnName("总成1");
        dmDimCatgModlListWithVersion.add(cegIctVO2);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO materialCodeVO = new DmDimMaterialCodeVO();
        materialCodeVO.setItemSubtypeCode("1005B");
        materialCodeVO.setItemSubtypeCnName("天线配件");
        dimMaterialCodeD.add(materialCodeVO);
        DmDimMaterialCodeVO materialCodeVO2 = new DmDimMaterialCodeVO();
        materialCodeVO2.setItemSubtypeCode("1155");
        materialCodeVO2.setItemSubtypeCnName("元器");
        dimMaterialCodeD.add(materialCodeVO2);

        List<Map> dmFocCatgCegIctDTOS=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("category_code", "1155");
        map.put("category_cn_name", "元器");
        dmFocCatgCegIctDTOS.add(map);

        List<DmDimCatgModlCegIctVO> dimCegCodeD=new ArrayList<>();
        dimCegCodeD.add(cegIctVO);

        List<DmFocVersionInfoDTO> versionVOList=new ArrayList<>();
        DmFocVersionInfoDTO versionVO = new DmFocVersionInfoDTO();
        String dateString = DateUtil.today().replace("-", "");
        dateString=dateString+"-"+"01";
        versionVO.setVersion(dateString);
        versionVO.setVersionId(11L);
        versionVOList.add(versionVO);

        when(iDmDimCatgModlCegIctDao.findDmDimCatgModlCegIctList(any())).thenReturn(dmDimCatgModlListWithVersion);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        when(iDmDimCatgModlCegIctDao.findDimCegCodeD()).thenReturn(dimCegCodeD);
        when(iDmDimCatgModlCegIctDao.findCatgCegIctList(any())).thenReturn(dmFocCatgCegIctDTOS);
        when(dmFocVersionDao.findDmFocPlanVersionVOList(any())).thenReturn(versionVOList);


        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationSave(inputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    // 操作未刷新
    @Test
    public void relationSave101Test() {
        DimensionInputVO inputVO = new DimensionInputVO();
        inputVO.setVersionId(11L);
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setCategoryCode("1005B");
        cegIctVO.setCategoryCnName("天线配件");

        cegIctVO.setL3CegCode("04082");
        cegIctVO.setL3CegCnName("总成配件2");
        cegIctVO.setL3CegShortCnName("总成");
        cegIctVO.setL4CegCnName("总成配件-单天线");
        cegIctVO.setL4CegCode("14088");
        cegIctVO.setL4CegShortCnName("单天线");
        cegIctVO.setOldCategoryCode("1005B");
        dimCatgModlCegIctList.add(cegIctVO);
        inputVO.setDimCatgModlCegIctList(dimCatgModlCegIctList);

        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO2 = new DmDimCatgModlCegIctVO();
        cegIctVO2.setCategoryCode("1005B");
        cegIctVO2.setCategoryCnName("天线配件");
        cegIctVO2.setL3CegCode("04081");
        cegIctVO2.setL3CegCnName("总成配件");
        cegIctVO2.setL3CegShortCnName("总成1");
        dmDimCatgModlListWithVersion.add(cegIctVO2);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO materialCodeVO = new DmDimMaterialCodeVO();
        materialCodeVO.setItemSubtypeCode("1005B");
        materialCodeVO.setItemSubtypeCnName("天线配件");
        dimMaterialCodeD.add(materialCodeVO);
        DmDimMaterialCodeVO materialCodeVO2 = new DmDimMaterialCodeVO();
        materialCodeVO2.setItemSubtypeCode("1155");
        materialCodeVO2.setItemSubtypeCnName("元器");
        dimMaterialCodeD.add(materialCodeVO2);

        List<Map> dmFocCatgCegIctDTOS=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("category_code", "1155");
        map.put("category_cn_name", "元器");
        dmFocCatgCegIctDTOS.add(map);

        List<DmDimCatgModlCegIctVO> dimCegCodeD=new ArrayList<>();
        dimCegCodeD.add(cegIctVO);

        List<DmFocVersionInfoDTO> versionVOList=new ArrayList<>();
        DmFocVersionInfoDTO versionVO = new DmFocVersionInfoDTO();
        versionVO.setVersionId(11L);
        versionVOList.add(versionVO);

        when(iDmDimCatgModlCegIctDao.findDmDimCatgModlCegIctList(any())).thenReturn(dmDimCatgModlListWithVersion);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        when(iDmDimCatgModlCegIctDao.findDimCegCodeD()).thenReturn(dimCegCodeD);
        when(iDmDimCatgModlCegIctDao.findCatgCegIctList(any())).thenReturn(dmFocCatgCegIctDTOS);
        when(dmFocVersionDao.findDmFocPlanVersionVOList(any())).thenReturn(versionVOList);


        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationSave(inputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    // 操作未刷新
    @Test
    public void relationSave11Test() {
        DimensionInputVO inputVO = new DimensionInputVO();
        inputVO.setVersionId(11L);
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setCategoryCode("1005B");
        cegIctVO.setCategoryCnName("天线配件");
        cegIctVO.setL3CegCode("04081");
        cegIctVO.setL3CegCnName("总成配件");
        cegIctVO.setL3CegShortCnName("总成");
        cegIctVO.setL4CegCnName("总成配件-单天线");
        cegIctVO.setL4CegCode("14088");
        cegIctVO.setL4CegShortCnName("单天线");
        cegIctVO.setOldCategoryCode("1005B");
        dimCatgModlCegIctList.add(cegIctVO);
        inputVO.setDimCatgModlCegIctList(dimCatgModlCegIctList);

        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO2 = new DmDimCatgModlCegIctVO();
        cegIctVO2.setCategoryCode("1005B");
        cegIctVO2.setCategoryCnName("天线配件");
        cegIctVO2.setL3CegCode("04082");
        cegIctVO2.setL3CegCnName("总成配件2");
        cegIctVO2.setL3CegShortCnName("总成1");
        dmDimCatgModlListWithVersion.add(cegIctVO2);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO materialCodeVO = new DmDimMaterialCodeVO();
        materialCodeVO.setItemSubtypeCode("1005B");
        materialCodeVO.setItemSubtypeCnName("天线配件");
        dimMaterialCodeD.add(materialCodeVO);
        DmDimMaterialCodeVO materialCodeVO2 = new DmDimMaterialCodeVO();
        materialCodeVO2.setItemSubtypeCode("1155");
        materialCodeVO2.setItemSubtypeCnName("元器");
        dimMaterialCodeD.add(materialCodeVO2);

        List<Map> dmFocCatgCegIctDTOS=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("category_code", "1155");
        map.put("category_cn_name", "元器");
        dmFocCatgCegIctDTOS.add(map);

        List<DmDimCatgModlCegIctVO> dimCegCodeD=new ArrayList<>();
        dimCegCodeD.add(cegIctVO);

        List<DmFocVersionInfoDTO> versionVOList=new ArrayList<>();
        DmFocVersionInfoDTO versionVO = new DmFocVersionInfoDTO();
        versionVO.setVersion("20230301-01");
        versionVO.setVersionId(11L);
        versionVOList.add(versionVO);

        when(iDmDimCatgModlCegIctDao.findDmDimCatgModlCegIctList(any())).thenReturn(dmDimCatgModlListWithVersion);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        when(iDmDimCatgModlCegIctDao.findDimCegCodeD()).thenReturn(dimCegCodeD);
        when(iDmDimCatgModlCegIctDao.findCatgCegIctList(any())).thenReturn(dmFocCatgCegIctDTOS);
        when(dmFocVersionDao.findDmFocPlanVersionVOList(any())).thenReturn(versionVOList);


        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationSave(inputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    // 操作未刷新
    @Test
    public void relationSave12Test() {
        DimensionInputVO inputVO = new DimensionInputVO();
        inputVO.setVersionId(11L);
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setCategoryCode("1005B");
        cegIctVO.setCategoryCnName("天线配件");
        cegIctVO.setL3CegCode("04081");
        cegIctVO.setL3CegCnName("总成配件");
        cegIctVO.setL3CegShortCnName("总成");
        cegIctVO.setL4CegCnName("总成配件-单天线");
        cegIctVO.setL4CegCode("14088");
        cegIctVO.setL4CegShortCnName("单天线");
        cegIctVO.setOldCategoryCode("1005B");
        dimCatgModlCegIctList.add(cegIctVO);


        inputVO.setDimCatgModlCegIctList(dimCatgModlCegIctList);

        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO2 = new DmDimCatgModlCegIctVO();
        cegIctVO2.setCategoryCode("1005B");
        cegIctVO2.setCategoryCnName("天线配件");
        cegIctVO2.setL3CegCode("04082");
        cegIctVO2.setL3CegCnName("总成配件2");
        cegIctVO2.setL3CegShortCnName("总成1");
        dmDimCatgModlListWithVersion.add(cegIctVO2);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO materialCodeVO = new DmDimMaterialCodeVO();
        materialCodeVO.setItemSubtypeCode("1005B");
        materialCodeVO.setItemSubtypeCnName("天线配件");
        dimMaterialCodeD.add(materialCodeVO);
        DmDimMaterialCodeVO materialCodeVO2 = new DmDimMaterialCodeVO();
        materialCodeVO2.setItemSubtypeCode("1155");
        materialCodeVO2.setItemSubtypeCnName("元器");
        dimMaterialCodeD.add(materialCodeVO2);

        List<Map> dmFocCatgCegIctDTOS=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("category_code", "1155");
        map.put("category_cn_name", "元器");
        dmFocCatgCegIctDTOS.add(map);

        List<DmDimCatgModlCegIctVO> dimCegCodeD=new ArrayList<>();
        dimCegCodeD.add(cegIctVO);

        List<DmFocVersionInfoDTO> versionVOList=new ArrayList<>();
        DmFocVersionInfoDTO versionVO = new DmFocVersionInfoDTO();
        versionVO.setVersion("20230301-011");
        versionVO.setVersionId(11L);
        versionVOList.add(versionVO);

        when(iDmDimCatgModlCegIctDao.findDmDimCatgModlCegIctList(any())).thenReturn(dmDimCatgModlListWithVersion);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        when(iDmDimCatgModlCegIctDao.findDimCegCodeD()).thenReturn(dimCegCodeD);
        when(iDmDimCatgModlCegIctDao.findCatgCegIctList(any())).thenReturn(dmFocCatgCegIctDTOS);
        when(dmFocVersionDao.findDmFocPlanVersionVOList(any())).thenReturn(versionVOList);


        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationSave(inputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    // 操作未刷新
    @Test
    public void relationSave13Test() {
        DimensionInputVO inputVO = new DimensionInputVO();
        inputVO.setVersionId(11L);
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setCategoryCode("1005B");
        cegIctVO.setCategoryCnName("天线配件");
        cegIctVO.setL3CegCode("04081");
        cegIctVO.setL3CegCnName("总成配件");
        cegIctVO.setL3CegShortCnName("总成");
        cegIctVO.setOldCategoryCode("1005B");
        dimCatgModlCegIctList.add(cegIctVO);


        inputVO.setDimCatgModlCegIctList(dimCatgModlCegIctList);

        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO2 = new DmDimCatgModlCegIctVO();
        cegIctVO2.setCategoryCode("1005B");
        cegIctVO2.setCategoryCnName("天线配件");
        cegIctVO2.setL3CegCode("04082");
        cegIctVO2.setL3CegCnName("总成配件2");
        cegIctVO2.setL3CegShortCnName("总成1");
        cegIctVO.setL4CegCnName("总成配件-单天线");
        cegIctVO.setL4CegCode("14088");
        cegIctVO.setL4CegShortCnName("单天线");
        dmDimCatgModlListWithVersion.add(cegIctVO2);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO materialCodeVO = new DmDimMaterialCodeVO();
        materialCodeVO.setItemSubtypeCode("1005B");
        materialCodeVO.setItemSubtypeCnName("天线配件");
        dimMaterialCodeD.add(materialCodeVO);
        DmDimMaterialCodeVO materialCodeVO2 = new DmDimMaterialCodeVO();
        materialCodeVO2.setItemSubtypeCode("1155");
        materialCodeVO2.setItemSubtypeCnName("元器");
        dimMaterialCodeD.add(materialCodeVO2);

        List<Map> dmFocCatgCegIctDTOS=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("category_code", "1155");
        map.put("category_cn_name", "元器");
        dmFocCatgCegIctDTOS.add(map);

        List<DmDimCatgModlCegIctVO> dimCegCodeD=new ArrayList<>();
        dimCegCodeD.add(cegIctVO);

        List<DmFocVersionInfoDTO> versionVOList=new ArrayList<>();
        DmFocVersionInfoDTO versionVO = new DmFocVersionInfoDTO();
        versionVO.setVersion("20230301-AUTO");
        versionVO.setVersionId(11L);
        versionVOList.add(versionVO);

        when(iDmDimCatgModlCegIctDao.findDmDimCatgModlCegIctList(any())).thenReturn(dmDimCatgModlListWithVersion);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        when(iDmDimCatgModlCegIctDao.findDimCegCodeD()).thenReturn(dimCegCodeD);
        when(iDmDimCatgModlCegIctDao.findCatgCegIctList(any())).thenReturn(dmFocCatgCegIctDTOS);
        when(dmFocVersionDao.findDmFocPlanVersionVOList(any())).thenReturn(versionVOList);
        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationSave(inputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    // 操作已刷新（存在未刷新）
    @Test
    public void relationSave14Test() {
        DimensionInputVO inputVO = new DimensionInputVO();
        inputVO.setVersionId(11L);
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setCategoryCode("1005B");
        cegIctVO.setCategoryCnName("天线配件");
        cegIctVO.setL3CegCode("04081");
        cegIctVO.setL3CegCnName("总成配件");
        cegIctVO.setL3CegShortCnName("总成");
        cegIctVO.setL4CegCnName("总成配件-单天线");
        cegIctVO.setL4CegCode("14088");
        cegIctVO.setL4CegShortCnName("单天线2");
        cegIctVO.setOldCategoryCode("1005B");
        dimCatgModlCegIctList.add(cegIctVO);
        inputVO.setDimCatgModlCegIctList(dimCatgModlCegIctList);

        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO2 = new DmDimCatgModlCegIctVO();
        cegIctVO2.setCategoryCode("1005B");
        cegIctVO2.setCategoryCnName("天线配件");
        cegIctVO2.setL3CegCode("04082");
        cegIctVO2.setL3CegCnName("总成配件2");
        cegIctVO2.setL3CegShortCnName("总成1");
        cegIctVO2.setL4CegCnName("总成配件-单天线");
        cegIctVO2.setL4CegCode("14088");
        cegIctVO2.setL4CegShortCnName("单天线");

        DmDimCatgModlCegIctVO cegIctVO3 = new DmDimCatgModlCegIctVO();
        cegIctVO3.setCategoryCode("1005C");
        cegIctVO3.setCategoryCnName("天线配件");
        cegIctVO3.setL3CegCode("04083");
        cegIctVO3.setL3CegCnName("总成配件3");
        cegIctVO3.setL3CegShortCnName("总成2");
        cegIctVO3.setL4CegCnName("总成配件-单天线");
        cegIctVO3.setL4CegCode("14088");
        cegIctVO3.setL4CegShortCnName("单天线");
        dmDimCatgModlListWithVersion.add(cegIctVO2);
        dmDimCatgModlListWithVersion.add(cegIctVO3);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO materialCodeVO = new DmDimMaterialCodeVO();
        materialCodeVO.setItemSubtypeCode("1005B");
        materialCodeVO.setItemSubtypeCnName("天线配件");
        dimMaterialCodeD.add(materialCodeVO);
        DmDimMaterialCodeVO materialCodeVO2 = new DmDimMaterialCodeVO();
        materialCodeVO2.setItemSubtypeCode("1155");
        materialCodeVO2.setItemSubtypeCnName("元器");
        dimMaterialCodeD.add(materialCodeVO2);

        List<Map> dmFocCatgCegIctDTOS=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("category_code", "1155");
        map.put("category_cn_name", "元器");
        dmFocCatgCegIctDTOS.add(map);

        List<DmDimCatgModlCegIctVO> dimCegCodeD=new ArrayList<>();
        dimCegCodeD.add(cegIctVO);

        List<DmFocVersionInfoDTO> versionVOList=new ArrayList<>();
        DmFocVersionInfoDTO versionVO = new DmFocVersionInfoDTO();
        versionVO.setVersion("20230301-AUTO");
        versionVO.setVersionId(12L);
        versionVOList.add(versionVO);

        when(iDmDimCatgModlCegIctDao.findDmDimCatgModlCegIctList(any())).thenReturn(dmDimCatgModlListWithVersion);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        when(iDmDimCatgModlCegIctDao.findDimCegCodeD()).thenReturn(dimCegCodeD);
        when(iDmDimCatgModlCegIctDao.findCatgCegIctList(any())).thenReturn(dmFocCatgCegIctDTOS);
        when(dmFocVersionDao.findDmFocPlanVersionVOList(any())).thenReturn(versionVOList);
        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationSave(inputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    // 操作已刷新（存在未刷新）
    @Test
    public void relationSave15Test() {
        DimensionInputVO inputVO = new DimensionInputVO();
        inputVO.setVersionId(11L);
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setCategoryCode("1005B");
        cegIctVO.setCategoryCnName("天线配件");
        cegIctVO.setL3CegCode("04081");
        cegIctVO.setL3CegCnName("总成配件");
        cegIctVO.setL3CegShortCnName("总成");
        cegIctVO.setL4CegCnName("总成配件-单天线");
        cegIctVO.setL4CegCode("14088");
        cegIctVO.setL4CegShortCnName("单天线");
        cegIctVO.setOldCategoryCode("1005B");
        dimCatgModlCegIctList.add(cegIctVO);
        inputVO.setDimCatgModlCegIctList(dimCatgModlCegIctList);

        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO2 = new DmDimCatgModlCegIctVO();
        cegIctVO2.setCategoryCode("1005B");
        cegIctVO2.setCategoryCnName("天线配件");
        cegIctVO2.setL3CegCode("04082");
        cegIctVO2.setL3CegCnName("总成配件2");
        cegIctVO2.setL3CegShortCnName("总成1");
        cegIctVO2.setL4CegCnName("总成配件-单天线");
        cegIctVO2.setL4CegCode("14088");
        cegIctVO2.setL4CegShortCnName("单天线");

        DmDimCatgModlCegIctVO cegIctVO3 = new DmDimCatgModlCegIctVO();
        cegIctVO3.setCategoryCode("1005C");
        cegIctVO3.setCategoryCnName("天线配件");
        cegIctVO3.setL3CegCode("04081");
        cegIctVO3.setL3CegCnName("总成配件");
        cegIctVO3.setL3CegShortCnName("总成");
        cegIctVO3.setL4CegCnName("总成配件-单天线");
        cegIctVO3.setL4CegCode("14088");
        cegIctVO3.setL4CegShortCnName("单天线");
        dmDimCatgModlListWithVersion.add(cegIctVO2);
        dmDimCatgModlListWithVersion.add(cegIctVO3);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO materialCodeVO = new DmDimMaterialCodeVO();
        materialCodeVO.setItemSubtypeCode("1005B");
        materialCodeVO.setItemSubtypeCnName("天线配件");
        dimMaterialCodeD.add(materialCodeVO);
        DmDimMaterialCodeVO materialCodeVO2 = new DmDimMaterialCodeVO();
        materialCodeVO2.setItemSubtypeCode("1155");
        materialCodeVO2.setItemSubtypeCnName("元器");
        dimMaterialCodeD.add(materialCodeVO2);

        List<Map> dmFocCatgCegIctDTOS=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("category_code", "1155");
        map.put("category_cn_name", "元器");
        dmFocCatgCegIctDTOS.add(map);

        List<DmDimCatgModlCegIctVO> dimCegCodeD=new ArrayList<>();
        dimCegCodeD.add(cegIctVO);

        List<DmFocVersionInfoDTO> versionVOList=new ArrayList<>();
        DmFocVersionInfoDTO versionVO = new DmFocVersionInfoDTO();
        versionVO.setVersion("20230301-AUTO");
        versionVO.setVersionId(12L);
        versionVOList.add(versionVO);

        when(iDmDimCatgModlCegIctDao.findDmDimCatgModlCegIctList(any())).thenReturn(dmDimCatgModlListWithVersion);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        when(iDmDimCatgModlCegIctDao.findDimCegCodeD()).thenReturn(dimCegCodeD);
        when(iDmDimCatgModlCegIctDao.findCatgCegIctList(any())).thenReturn(dmFocCatgCegIctDTOS);
        when(dmFocVersionDao.findDmFocPlanVersionVOList(any())).thenReturn(versionVOList);
        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationSave(inputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    // 操作已刷新（存在未刷新）
    @Test
    public void relationSave16Test() {
        DimensionInputVO inputVO = new DimensionInputVO();
        inputVO.setVersionId(11L);
        List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setCategoryCode("1005B");
        cegIctVO.setCategoryCnName("天线配件");
        cegIctVO.setL3CegCode("04081");
        cegIctVO.setL3CegCnName("总成配件");
        cegIctVO.setL3CegShortCnName("总成");
        cegIctVO.setL4CegCnName("总成配件-单天线");
        cegIctVO.setL4CegCode("14088");
        cegIctVO.setL4CegShortCnName("单天线2");
        cegIctVO.setOldCategoryCode("1005B");
        dimCatgModlCegIctList.add(cegIctVO);
        inputVO.setDimCatgModlCegIctList(dimCatgModlCegIctList);

        List<DmDimCatgModlCegIctVO> dmDimCatgModlListWithVersion=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO2 = new DmDimCatgModlCegIctVO();
        cegIctVO2.setCategoryCode("1005B");
        cegIctVO2.setCategoryCnName("天线配件");
        cegIctVO2.setL3CegCode("04081");
        cegIctVO2.setL3CegCnName("总成配件2");
        cegIctVO2.setL3CegShortCnName("总成1");
        cegIctVO2.setL4CegCnName("总成配件-单天线");
        cegIctVO2.setL4CegCode("14088");
        cegIctVO2.setL4CegShortCnName("单天线");

        DmDimCatgModlCegIctVO cegIctVO3 = new DmDimCatgModlCegIctVO();
        cegIctVO3.setCategoryCode("1005C");
        cegIctVO3.setCategoryCnName("天线配件");
        cegIctVO3.setL3CegCode("04083");
        cegIctVO3.setL3CegCnName("总成配件3");
        cegIctVO3.setL3CegShortCnName("总成2");
        cegIctVO3.setL4CegCnName("总成配件-单天线");
        cegIctVO3.setL4CegCode("14088");
        cegIctVO3.setL4CegShortCnName("单天线");
        dmDimCatgModlListWithVersion.add(cegIctVO2);
        dmDimCatgModlListWithVersion.add(cegIctVO3);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO materialCodeVO = new DmDimMaterialCodeVO();
        materialCodeVO.setItemSubtypeCode("1005B");
        materialCodeVO.setItemSubtypeCnName("天线配件");
        dimMaterialCodeD.add(materialCodeVO);
        DmDimMaterialCodeVO materialCodeVO2 = new DmDimMaterialCodeVO();
        materialCodeVO2.setItemSubtypeCode("1155");
        materialCodeVO2.setItemSubtypeCnName("元器");
        dimMaterialCodeD.add(materialCodeVO2);

        List<Map> dmFocCatgCegIctDTOS=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("category_code", "1155");
        map.put("category_cn_name", "元器");
        dmFocCatgCegIctDTOS.add(map);

        List<DmDimCatgModlCegIctVO> dimCegCodeD=new ArrayList<>();
        dimCegCodeD.add(cegIctVO);

        List<DmFocVersionInfoDTO> versionVOList=new ArrayList<>();
        DmFocVersionInfoDTO versionVO = new DmFocVersionInfoDTO();
        versionVO.setVersion("20230301-AUTO");
        versionVO.setVersionId(12L);
        versionVOList.add(versionVO);

        when(iDmDimCatgModlCegIctDao.findDmDimCatgModlCegIctList(any())).thenReturn(dmDimCatgModlListWithVersion);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        when(iDmDimCatgModlCegIctDao.findDimCegCodeD()).thenReturn(dimCegCodeD);
        when(iDmDimCatgModlCegIctDao.findCatgCegIctList(any())).thenReturn(dmFocCatgCegIctDTOS);
        when(dmFocVersionDao.findDmFocPlanVersionVOList(any())).thenReturn(versionVOList);
        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationSave(inputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationImport() {
        Attachment attachment=null;
        Long versionId=null;
        ResultDataVO resultDataVO=new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.relationImport(attachment,versionId,"");
        } catch (CommonApplicationException | IOException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationImport1Test() throws CommonApplicationException, IOException {
        Long versionId=10L;
        ResultDataVO resultDataVO=new ResultDataVO();
        DmFocVersionInfoDTO dmFoiPlanVersionVO=new DmFocVersionInfoDTO();
        dmFoiPlanVersionVO.setVersion("202303");
        dmFoiPlanVersionVO.setVersionId(15L);
        setAttachment();
        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String,Object> map = new LinkedHashMap<>();
        map.put("k1", "100");
        resultList.add(map);
        when(dmFocVersionDao.findDmFocPlanVersionVOById(any(),any())).thenReturn(dmFoiPlanVersionVO);
        PowerMockito.doReturn(resultList).when(excelUtil).importExcel(any(), any(), any(), any());
        try {
            resultDataVO = configDimensionManageService.relationImport(attachment,versionId,"ICT");
        } catch (CommonApplicationException | IOException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    private void  setAttachment(){
        attachment = new Attachment("", new DataHandler(new DataSource() {
            @Override
            public InputStream getInputStream() throws IOException {
                return new InputStream() {
                    @Override
                    public int read() throws IOException {
                        return 0;
                    }
                };
            }

            @Override
            public OutputStream getOutputStream() throws IOException {
                return null;
            }

            @Override
            public String getContentType() {
                return null;
            }

            @Override
            public String getName() {
                return "test.xlsx";
            }
        }),
            new MultivaluedMap() {
                @Override
                public int size() {
                    return 0;
                }

                @Override
                public boolean isEmpty() {
                    return false;
                }

                @Override
                public boolean containsKey(Object key) {
                    return false;
                }

                @Override
                public boolean containsValue(Object value) {
                    return false;
                }

                @Override
                public Object get(Object key) {
                    return null;
                }

                @Nullable
                @Override
                public Object put(Object key, Object value) {
                    return null;
                }

                @Override
                public Object remove(Object key) {
                    return null;
                }

                @Override
                public void putAll(@NotNull Map m) {

                }

                @Override
                public void clear() {

                }

                @NotNull
                @Override
                public Set keySet() {
                    return null;
                }

                @NotNull
                @Override
                public Collection values() {
                    return null;
                }

                @NotNull
                @Override
                public Set<Entry> entrySet() {
                    return new Set<Entry>() {
                        @Override
                        public int size() {
                            return 0;
                        }

                        @Override
                        public boolean isEmpty() {
                            return false;
                        }

                        @Override
                        public boolean contains(Object o) {
                            return false;
                        }

                        @NotNull
                        @Override
                        public Iterator<Entry> iterator() {
                            return new Iterator<Entry>() {
                                @Override
                                public boolean hasNext() {
                                    return false;
                                }

                                @Override
                                public Entry next() {
                                    return null;
                                }
                            };
                        }

                        @NotNull
                        @Override
                        public Object[] toArray() {
                            return new Object[0];
                        }

                        @NotNull
                        @Override
                        public <T> T[] toArray(@NotNull T[] a) {
                            return null;
                        }

                        @Override
                        public boolean add(Entry entry) {
                            return false;
                        }

                        @Override
                        public boolean remove(Object o) {
                            return false;
                        }

                        @Override
                        public boolean containsAll(@NotNull Collection<?> c) {
                            return false;
                        }

                        @Override
                        public boolean addAll(@NotNull Collection<? extends Entry> c) {
                            return false;
                        }

                        @Override
                        public boolean retainAll(@NotNull Collection<?> c) {
                            return false;
                        }

                        @Override
                        public boolean removeAll(@NotNull Collection<?> c) {
                            return false;
                        }

                        @Override
                        public void clear() {

                        }
                    };
                }

                @Override
                public boolean equals(Object o) {
                    return false;
                }

                @Override
                public int hashCode() {
                    return 0;
                }

                @Override
                public void putSingle(Object o, Object o2) {

                }

                @Override
                public void add(Object o, Object o2) {

                }

                @Override
                public Object getFirst(Object o) {
                    return null;
                }

                @Override
                public void addAll(Object o, Object[] objects) {

                }

                @Override
                public void addAll(Object o, List list) {

                }

                @Override
                public void addFirst(Object o, Object o2) {

                }

                @Override
                public boolean equalsIgnoreValueOrder(MultivaluedMap multivaluedMap) {
                    return false;
                }
            });
    }

    @Test
    public void relationImport2Test() throws CommonApplicationException, IOException {
        Long versionId=10L;
        ResultDataVO resultDataVO=new ResultDataVO();
        DmFocVersionInfoDTO dmFoiPlanVersionVO=new DmFocVersionInfoDTO();
        dmFoiPlanVersionVO.setVersion("202303");
        dmFoiPlanVersionVO.setVersionId(15L);
        setAttachment();

        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String,Object> map = new LinkedHashMap<>();
        map.put("k1", "100");
        map.put("k2", "200");
        map.put("k3", "300");
        map.put("k4", "400");
        map.put("k5", "500");
        map.put("k6", "600");
        map.put("k7", "700");
        map.put("k8", "800");
        resultList.add(map);
        when(dmFocVersionDao.findDmFocPlanVersionVOById(any(),any())).thenReturn(dmFoiPlanVersionVO);
        PowerMockito.doReturn(resultList).when(excelUtil).importExcel(any(), any(), any(), any());

        try {
            resultDataVO = configDimensionManageService.relationImport(attachment,versionId,"ICT");
        } catch (CommonApplicationException | IOException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationImport3Test() throws CommonApplicationException, IOException {
        Long versionId=10L;
        ResultDataVO resultDataVO=new ResultDataVO();
        DmFocVersionInfoDTO dmFoiPlanVersionVO=new DmFocVersionInfoDTO();
        dmFoiPlanVersionVO.setVersion("202303");
        dmFoiPlanVersionVO.setVersionId(15L);
        setAttachment();

        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String,Object> map = new LinkedHashMap<>();
        map.put("k1", "专项采购认证部");
        map.put("k2", "专项采购认证部简称");
        map.put("k3", "专项采购认证部编码");
        map.put("k4", "模块");
        map.put("k5", "模块简称");
        map.put("k6", "模块编码");
        map.put("k7", "品类编码");
        map.put("k8", "品类名称");
        resultList.add(map);
        when(dmFocVersionDao.findDmFocPlanVersionVOById(any(),any())).thenReturn(dmFoiPlanVersionVO);
        PowerMockito.doReturn(resultList).when(excelUtil).importExcel(any(), any(), any(), any());

        try {
            resultDataVO = configDimensionManageService.relationImport(attachment,versionId,"ICT");
        } catch (CommonApplicationException | IOException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationImport4Test() throws CommonApplicationException, IOException {
        Long versionId=10L;
        ResultDataVO resultDataVO=new ResultDataVO();
        DmFocVersionInfoDTO dmFoiPlanVersionVO=new DmFocVersionInfoDTO();
        dmFoiPlanVersionVO.setVersion("202303");
        dmFoiPlanVersionVO.setVersionId(15L);
        setAttachment();

        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String,Object> map = new LinkedHashMap<>();
        map.put("k1", "专项采购认证部");
        map.put("k2", "专项采购认证部简称");
        map.put("k3", "专项采购认证部编码");
        map.put("k4", "模块");
        map.put("k5", "模块简称");
        map.put("k6", "模块编码");
        map.put("k7", "品类编码");
        map.put("k8", "品类名称");
        resultList.add(map);
        LinkedHashMap<String,Object> map2 = new LinkedHashMap<>();
        map2.put("专项采购认证部","贵州代表处");
        map2.put("专项采购认证部简称","贵州");
        map2.put("专项采购认证部编码","11205");
        map2.put("模块", "贵州代表处风林火山");
        map2.put("模块简称", "贵州火山");
        map2.put("模块编码", "2210D");
        map2.put("品类编码","1140A");
        map2.put("品类名称","电缆");
        resultList.add(map2);

        DmFoiImpExpRecordVO expRecordVO=new DmFoiImpExpRecordVO();
        expRecordVO.setRecordNum(25000);
        expRecordVO.setFileName("维表版本");

        when(dmFocVersionDao.findDmFocPlanVersionVOById(any(),any())).thenReturn(dmFoiPlanVersionVO);
        PowerMockito.doReturn(resultList).when(excelUtil).importExcel(any(), any(), any(), any());
        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);

        try {
            resultDataVO = configDimensionManageService.relationImport(attachment,versionId,"ICT");
        } catch (CommonApplicationException | IOException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationImport5Test() throws CommonApplicationException, IOException {
        Long versionId=10L;
        ResultDataVO resultDataVO=new ResultDataVO();
        DmFocVersionInfoDTO dmFoiPlanVersionVO=new DmFocVersionInfoDTO();
        dmFoiPlanVersionVO.setVersion("202303");
        dmFoiPlanVersionVO.setVersionId(15L);
        setAttachment();

        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String,Object> map = new LinkedHashMap<>();
        map.put("k1", "专项采购认证部");
        map.put("k2", "专项采购认证部简称");
        map.put("k3", "专项采购认证部编码");
        map.put("k4", "模块");
        map.put("k5", "模块简称");
        map.put("k6", "模块编码");
        map.put("k7", "品类编码");
        map.put("k8", "品类名称");
        resultList.add(map);
        LinkedHashMap<String,Object> map2 = new LinkedHashMap<>();
        map2.put("专项采购认证部","贵州代表处");
        map2.put("专项采购认证部简称","贵州");
        map2.put("专项采购认证部编码","11205");
        map2.put("模块", "贵州代表处风林火山");
        map2.put("模块简称", "贵州火山");
        map2.put("模块编码", "2210D");
        map2.put("品类编码","1140A");
        map2.put("品类名称","电缆");
        resultList.add(map2);

        DmFoiImpExpRecordVO expRecordVO=new DmFoiImpExpRecordVO();
        expRecordVO.setRecordNum(25000);
        expRecordVO.setFileName("维表版本");

        List<DmDimCatgModlCegIctVO> dimCegCodeD=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setL3CegCode("11205");
        cegIctVO.setL3CegCnName("贵州代表处");
        cegIctVO.setL3CegShortCnName("贵州");
        dimCegCodeD.add(cegIctVO);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO dimMaterialCodeVO = new DmDimMaterialCodeVO();
        dimMaterialCodeVO.setItemSubtypeCnName("电缆");
        dimMaterialCodeVO.setItemSubtypeCode("1140A");
        dimMaterialCodeD.add(dimMaterialCodeVO);

        when(dmFocVersionDao.findDmFocPlanVersionVOById(any(),any())).thenReturn(dmFoiPlanVersionVO);
        PowerMockito.doReturn(resultList).when(excelUtil).importExcel(any(), any(), any(), any());
        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);
        when(iDmDimCatgModlCegIctDao.findDimCegCodeD()).thenReturn(dimCegCodeD);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);

        try {
            resultDataVO = configDimensionManageService.relationImport(attachment,versionId,"ICT");
        } catch (CommonApplicationException | IOException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    // 错误校验
    @Test
    public void relationImport6Test() throws CommonApplicationException, IOException {
        Long versionId=10L;
        ResultDataVO resultDataVO=new ResultDataVO();
        DmFocVersionInfoDTO dmFoiPlanVersionVO=new DmFocVersionInfoDTO();
        dmFoiPlanVersionVO.setVersion("202303");
        dmFoiPlanVersionVO.setVersionId(15L);
        setAttachment();

        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String,Object> map = new LinkedHashMap<>();
        map.put("k1", "专项采购认证部");
        map.put("k2", "专项采购认证部简称");
        map.put("k3", "专项采购认证部编码");
        map.put("k4", "模块");
        map.put("k5", "模块简称");
        map.put("k6", "模块编码");
        map.put("k7", "品类编码");
        map.put("k8", "品类名称");
        resultList.add(map);
        LinkedHashMap<String,Object> map2 = new LinkedHashMap<>();
        map2.put("专项采购认证部","贵州代表处");
        map2.put("专项采购认证部简称","贵州");
        map2.put("专项采购认证部编码","11205");
        map2.put("模块", "贵州代表处风林火山");
        map2.put("模块简称", "贵州火山");
        map2.put("模块编码", "2210D");
        map2.put("品类编码","1140A");
        map2.put("品类名称","电缆");
        resultList.add(map2);

        // 一个专家团存在多个简称
        LinkedHashMap<String,Object> map3 = new LinkedHashMap<>();
        map3.put("专项采购认证部","贵州代表处");
        map3.put("专项采购认证部简称","贵州2");
        map3.put("专项采购认证部编码","11205");
        map3.put("模块", "贵州代表处风林火山");
        map3.put("模块简称", "贵州火山");
        map3.put("模块编码", "2210D");
        map3.put("品类编码","1140A");
        map3.put("品类名称","电缆");
        resultList.add(map3);

        // 一个简称包含多个专家团
        LinkedHashMap<String,Object> map4 = new LinkedHashMap<>();
        map4.put("专项采购认证部","贵州代表处PP");
        map4.put("专项采购认证部简称","贵州2");
        map4.put("专项采购认证部编码","11206");
        map4.put("模块", "贵州代表处风林火山");
        map4.put("模块简称", "贵州火山");
        map4.put("模块编码", "2210D");
        map4.put("品类编码","1140A");
        map4.put("品类名称","电缆");
        resultList.add(map4);

        DmFoiImpExpRecordVO expRecordVO=new DmFoiImpExpRecordVO();
        expRecordVO.setRecordNum(25000);
        expRecordVO.setFileName("维表版本");

        List<DmDimCatgModlCegIctVO> dimCegCodeD=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setL3CegCode("11205");
        cegIctVO.setL3CegCnName("贵州代表处");
        cegIctVO.setL3CegShortCnName("贵州");
        dimCegCodeD.add(cegIctVO);

        DmDimCatgModlCegIctVO cegIctVO2 = new DmDimCatgModlCegIctVO();
        cegIctVO2.setL3CegCode("11206");
        cegIctVO2.setL3CegCnName("贵州代表处PP");
        cegIctVO2.setL3CegShortCnName("贵州PP");
        dimCegCodeD.add(cegIctVO2);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO dimMaterialCodeVO = new DmDimMaterialCodeVO();
        dimMaterialCodeVO.setItemSubtypeCnName("电缆");
        dimMaterialCodeVO.setItemSubtypeCode("1140A");
        dimMaterialCodeD.add(dimMaterialCodeVO);

        List<DmFocVersionInfoDTO> versionVOList=new ArrayList<>();
        DmFocVersionInfoDTO versionVO = new DmFocVersionInfoDTO();
        versionVO.setVersion("20230301-AUTO");
        versionVO.setVersionId(11L);
        versionVOList.add(versionVO);

        when(dmFocVersionDao.findDmFocPlanVersionVOById(any(),any())).thenReturn(dmFoiPlanVersionVO);
        PowerMockito.doReturn(resultList).when(excelUtil).importExcel(any(), any(), any(), any());
        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);
        when(iDmDimCatgModlCegIctDao.findDimCegCodeD()).thenReturn(dimCegCodeD);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        when(dmFocVersionDao.findDmFocPlanVersionVOList(any())).thenReturn(versionVOList);

        try {
            resultDataVO = configDimensionManageService.relationImport(attachment,versionId,"ICT");
        } catch (CommonApplicationException | IOException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    // 存在未刷新
    @Test
    public void relationImport7Test() throws CommonApplicationException, IOException {
        Long versionId=11L;
        ResultDataVO resultDataVO=new ResultDataVO();
        DmFocVersionInfoDTO dmFoiPlanVersionVO=new DmFocVersionInfoDTO();
        dmFoiPlanVersionVO.setVersion("202303");
        dmFoiPlanVersionVO.setVersionId(11L);
        setAttachment();

        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String,Object> map = new LinkedHashMap<>();
        map.put("k1", "专项采购认证部");
        map.put("k2", "专项采购认证部简称");
        map.put("k3", "专项采购认证部编码");
        map.put("k4", "模块");
        map.put("k5", "模块简称");
        map.put("k6", "模块编码");
        map.put("k7", "品类编码");
        map.put("k8", "品类名称");
        resultList.add(map);
        LinkedHashMap<String,Object> map2 = new LinkedHashMap<>();
        map2.put("专项采购认证部","贵州代表处");
        map2.put("专项采购认证部简称","贵州");
        map2.put("专项采购认证部编码","11205");
        map2.put("模块", "贵州代表处风林火山");
        map2.put("模块简称", "贵州火山");
        map2.put("模块编码", "2210D");
        map2.put("品类编码","1140A");
        map2.put("品类名称","电缆");
        resultList.add(map2);

        DmFoiImpExpRecordVO expRecordVO=new DmFoiImpExpRecordVO();
        expRecordVO.setRecordNum(25000);
        expRecordVO.setFileName("维表版本");

        List<DmDimCatgModlCegIctVO> dimCegCodeD=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setL3CegCode("11205");
        cegIctVO.setL3CegCnName("贵州代表处");
        cegIctVO.setL3CegShortCnName("贵州");
        cegIctVO.setL4CegCode("2210D");
        cegIctVO.setL4CegCnName("贵州代表处风林火山");
        cegIctVO.setL4CegShortCnName("贵州火山");
        dimCegCodeD.add(cegIctVO);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO dimMaterialCodeVO = new DmDimMaterialCodeVO();
        dimMaterialCodeVO.setItemSubtypeCnName("电缆");
        dimMaterialCodeVO.setItemSubtypeCode("1140A");
        dimMaterialCodeD.add(dimMaterialCodeVO);

        List<DmFocVersionInfoDTO> versionVOList=new ArrayList<>();
        DmFocVersionInfoDTO versionVO = new DmFocVersionInfoDTO();
        versionVO.setVersion("20230301-AUTO");
        versionVO.setVersionId(11L);
        versionVOList.add(versionVO);

        when(dmFocVersionDao.findDmFocPlanVersionVOById(any(),any())).thenReturn(dmFoiPlanVersionVO);
        PowerMockito.doReturn(resultList).when(excelUtil).importExcel(any(), any(), any(), any());
        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);
        when(iDmDimCatgModlCegIctDao.findDimCegCodeD()).thenReturn(dimCegCodeD);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        when(dmFocVersionDao.findDmFocPlanVersionVOList(any())).thenReturn(versionVOList);

        try {
            resultDataVO = configDimensionManageService.relationImport(attachment,versionId,"ICT");
        } catch (CommonApplicationException | IOException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    // 存在未刷新
    @Test
    public void relationImport8Test() throws CommonApplicationException, IOException {
        Long versionId=11L;
        ResultDataVO resultDataVO=new ResultDataVO();
        DmFocVersionInfoDTO dmFoiPlanVersionVO=new DmFocVersionInfoDTO();
        dmFoiPlanVersionVO.setVersion("202303");
        dmFoiPlanVersionVO.setVersionId(12L);
        setAttachment();

        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String,Object> map = new LinkedHashMap<>();
        map.put("k1", "专项采购认证部");
        map.put("k2", "专项采购认证部简称");
        map.put("k3", "专项采购认证部编码");
        map.put("k4", "模块");
        map.put("k5", "模块简称");
        map.put("k6", "模块编码");
        map.put("k7", "品类编码");
        map.put("k8", "品类名称");
        resultList.add(map);
        LinkedHashMap<String,Object> map2 = new LinkedHashMap<>();
        map2.put("专项采购认证部","贵州代表处");
        map2.put("专项采购认证部简称","贵州");
        map2.put("专项采购认证部编码","11205");
        map2.put("模块", "贵州代表处风林火山");
        map2.put("模块简称", "贵州火山");
        map2.put("模块编码", "2210D");
        map2.put("品类编码","1140A");
        map2.put("品类名称","电缆");
        resultList.add(map2);

        DmFoiImpExpRecordVO expRecordVO=new DmFoiImpExpRecordVO();
        expRecordVO.setRecordNum(25000);
        expRecordVO.setFileName("维表版本");

        List<DmDimCatgModlCegIctVO> dimCegCodeD=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setL3CegCode("11205");
        cegIctVO.setL3CegCnName("贵州代表处");
        cegIctVO.setL3CegShortCnName("贵州");
        cegIctVO.setL4CegCode("2210D");
        cegIctVO.setL4CegCnName("贵州代表处风林火山");
        cegIctVO.setL4CegShortCnName("贵州火山");
        dimCegCodeD.add(cegIctVO);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO dimMaterialCodeVO = new DmDimMaterialCodeVO();
        dimMaterialCodeVO.setItemSubtypeCnName("电缆");
        dimMaterialCodeVO.setItemSubtypeCode("1140A");
        dimMaterialCodeD.add(dimMaterialCodeVO);

        List<DmFocVersionInfoDTO> versionVOList=new ArrayList<>();
        DmFocVersionInfoDTO versionVO = new DmFocVersionInfoDTO();
        versionVO.setVersion("20230301-AUTO");
        versionVO.setVersionId(11L);
        versionVOList.add(versionVO);

        when(dmFocVersionDao.findDmFocPlanVersionVOById(any(),any())).thenReturn(dmFoiPlanVersionVO);
        PowerMockito.doReturn(resultList).when(excelUtil).importExcel(any(), any(), any(), any());
        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);
        when(iDmDimCatgModlCegIctDao.findDimCegCodeD()).thenReturn(dimCegCodeD);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        when(dmFocVersionDao.findDmFocPlanVersionVOList(any())).thenReturn(versionVOList);

        try {
            resultDataVO = configDimensionManageService.relationImport(attachment,versionId,"ICT");
        } catch (CommonApplicationException | IOException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    // 不存在未刷新
    @Test
    public void relationImport9Test() throws CommonApplicationException, IOException {
        Long versionId=11L;
        ResultDataVO resultDataVO=new ResultDataVO();
        DmFocVersionInfoDTO dmFoiPlanVersionVO=new DmFocVersionInfoDTO();
        dmFoiPlanVersionVO.setVersion("202303");
        dmFoiPlanVersionVO.setVersionId(12L);
        setAttachment();

        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String,Object> map = new LinkedHashMap<>();
        map.put("k1", "专项采购认证部");
        map.put("k2", "专项采购认证部简称");
        map.put("k3", "专项采购认证部编码");
        map.put("k4", "模块");
        map.put("k5", "模块简称");
        map.put("k6", "模块编码");
        map.put("k7", "品类编码");
        map.put("k8", "品类名称");
        resultList.add(map);
        LinkedHashMap<String,Object> map2 = new LinkedHashMap<>();
        map2.put("专项采购认证部","贵州代表处");
        map2.put("专项采购认证部简称","贵州");
        map2.put("专项采购认证部编码","11205");
        map2.put("模块", "贵州代表处风林火山");
        map2.put("模块简称", "贵州火山");
        map2.put("模块编码", "2210D");
        map2.put("品类编码","1140A");
        map2.put("品类名称","电缆");
        resultList.add(map2);

        DmFoiImpExpRecordVO expRecordVO=new DmFoiImpExpRecordVO();
        expRecordVO.setRecordNum(25000);
        expRecordVO.setFileName("维表版本");

        List<DmDimCatgModlCegIctVO> dimCegCodeD=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setL3CegCode("11205");
        cegIctVO.setL3CegCnName("贵州代表处");
        cegIctVO.setL3CegShortCnName("贵州");
        cegIctVO.setL4CegCode("2210D");
        cegIctVO.setL4CegCnName("贵州代表处风林火山");
        cegIctVO.setL4CegShortCnName("贵州火山");
        dimCegCodeD.add(cegIctVO);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO dimMaterialCodeVO = new DmDimMaterialCodeVO();
        dimMaterialCodeVO.setItemSubtypeCnName("电缆");
        dimMaterialCodeVO.setItemSubtypeCode("1140A");
        dimMaterialCodeD.add(dimMaterialCodeVO);

        List<DmFocVersionInfoDTO> versionVOList=new ArrayList<>();
        DmFocVersionInfoDTO versionVO = new DmFocVersionInfoDTO();
        versionVO.setVersion("20230301-AUTO");
        versionVO.setVersionId(11L);
        versionVOList.add(versionVO);

        when(dmFocVersionDao.findDmFocPlanVersionVOById(any(),any())).thenReturn(dmFoiPlanVersionVO);
        PowerMockito.doReturn(resultList).when(excelUtil).importExcel(any(), any(), any(), any());
        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);
        when(iDmDimCatgModlCegIctDao.findDimCegCodeD()).thenReturn(dimCegCodeD);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        //when(iDmFoiPlanVersionDao.findDmFoiPlanVersionVOList(any())).thenReturn(versionVOList);

        try {
            resultDataVO = configDimensionManageService.relationImport(attachment,versionId,"ICT");
        } catch (CommonApplicationException | IOException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    //错误校验--重复行数+专家团/模块空值
    @Test
    public void relationImport10Test() throws CommonApplicationException, IOException {
        Long versionId=11L;
        ResultDataVO resultDataVO=new ResultDataVO();
        DmFocVersionInfoDTO dmFoiPlanVersionVO=new DmFocVersionInfoDTO();
        dmFoiPlanVersionVO.setVersion("202303");
        dmFoiPlanVersionVO.setVersionId(12L);
        setAttachment();

        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String,Object> map = new LinkedHashMap<>();
        map.put("k1", "专项采购认证部");
        map.put("k2", "专项采购认证部简称");
        map.put("k3", "专项采购认证部编码");
        map.put("k4", "模块");
        map.put("k5", "模块简称");
        map.put("k6", "模块编码");
        map.put("k7", "品类编码");
        map.put("k8", "品类名称");
        resultList.add(map);
        LinkedHashMap<String,Object> map2 = new LinkedHashMap<>();
        map2.put("专项采购认证部","贵州代表处");
        map2.put("专项采购认证部简称","贵州");
        map2.put("专项采购认证部编码","11205");
        map2.put("模块", "贵州代表处风林火山");
        map2.put("模块简称", "贵州火山");
        map2.put("模块编码", "2210D");
        map2.put("品类编码","1140A");
        map2.put("品类名称","电缆");
        resultList.add(map2);
        LinkedHashMap<String,Object> map3 = new LinkedHashMap<>();
        map3.put("专项采购认证部","贵州代表处");
        map3.put("专项采购认证部简称","贵州");
        map3.put("专项采购认证部编码","11205");
        map3.put("模块", "贵州代表处风林火山");
        map3.put("模块简称", "贵州火山");
        map3.put("模块编码", "2210D");
        map3.put("品类编码","1140A");
        map3.put("品类名称","电缆");
        resultList.add(map3);
        LinkedHashMap<String,Object> map4 = new LinkedHashMap<>();
        map4.put("专项采购认证部","");
        map4.put("专项采购认证部简称","");
        map4.put("专项采购认证部编码","11205");
        map4.put("模块", "");
        map4.put("模块简称", "");
        map4.put("模块编码", "");
        map4.put("品类编码","1140A");
        map4.put("品类名称","电缆");
        resultList.add(map4);


        DmFoiImpExpRecordVO expRecordVO=new DmFoiImpExpRecordVO();
        expRecordVO.setRecordNum(25000);
        expRecordVO.setFileName("维表版本");

        List<DmDimCatgModlCegIctVO> dimCegCodeD=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setL3CegCode("11205");
        cegIctVO.setL3CegCnName("贵州代表处");
        cegIctVO.setL3CegShortCnName("贵州");
        cegIctVO.setL4CegCode("2210D");
        cegIctVO.setL4CegCnName("贵州代表处风林火山");
        cegIctVO.setL4CegShortCnName("贵州火山");
        dimCegCodeD.add(cegIctVO);

        List<DmDimMaterialCodeVO> dimMaterialCodeD=new ArrayList<>();
        DmDimMaterialCodeVO dimMaterialCodeVO = new DmDimMaterialCodeVO();
        dimMaterialCodeVO.setItemSubtypeCnName("电缆");
        dimMaterialCodeVO.setItemSubtypeCode("1140A");
        dimMaterialCodeD.add(dimMaterialCodeVO);

        List<DmFocVersionInfoDTO> versionVOList=new ArrayList<>();
        DmFocVersionInfoDTO versionVO = new DmFocVersionInfoDTO();
        versionVO.setVersion("20230301-AUTO");
        versionVO.setVersionId(11L);
        versionVOList.add(versionVO);

        when(dmFocVersionDao.findDmFocPlanVersionVOById(any(),any())).thenReturn(dmFoiPlanVersionVO);
        PowerMockito.doReturn(resultList).when(excelUtil).importExcel(any(), any(), any(), any());
        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);
        when(iDmDimCatgModlCegIctDao.findDimCegCodeD()).thenReturn(dimCegCodeD);
        when(iDmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(dimMaterialCodeD);
        when(dmFocVersionDao.findDmFocPlanVersionVOList(any())).thenReturn(versionVOList);

        try {
            resultDataVO = configDimensionManageService.relationImport(attachment,versionId,"ICT");
        } catch (CommonApplicationException | IOException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void exportRelationList() throws CommonApplicationException {
        DmDimCatgModlCegIctVO dmDimCatgModlCegIctVO=new DmDimCatgModlCegIctVO();
        HttpServletResponse response=null;
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.exportRelationList(dmDimCatgModlCegIctVO,response);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void exportRelationList1Test() throws CommonApplicationException {
        DmDimCatgModlCegIctVO dmDimCatgModlCegIctVO=new DmDimCatgModlCegIctVO();
        HttpServletResponse response=null;
        dmDimCatgModlCegIctVO.setVersionId(11L);

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.exportRelationList(dmDimCatgModlCegIctVO,response);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void exportRelationList2Test() throws CommonApplicationException, IOException {
        DmDimCatgModlCegIctVO dmDimCatgModlCegIctVO=new DmDimCatgModlCegIctVO();
        HttpServletResponse response=null;
        dmDimCatgModlCegIctVO.setVersionId(11L);

        List<Map> dimensionList = new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("l3Code", "12602");
        map.put("cateCode", "14589");
        dimensionList.add(map);

        DmFocVersionInfoDTO dmFoiPlanVersionVO=new DmFocVersionInfoDTO();
        dmFoiPlanVersionVO.setVersionId(11L);
        dmFoiPlanVersionVO.setVersion("20230409");

        DmFoiImpExpRecordVO expRecordVO=new DmFoiImpExpRecordVO();
        expRecordVO.setRecordNum(25000);
        expRecordVO.setFileName("维表版本");

        when(dmFocVersionDao.findDmFocPlanVersionVOById(any(),any())).thenReturn(dmFoiPlanVersionVO);
        when(iDmDimCatgModlCegIctDao.findExportCatgModlCegIctList(any())).thenReturn(dimensionList);
        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);


        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configDimensionManageService.exportRelationList(dmDimCatgModlCegIctVO,response);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void mapToObject() {
        List<HeaderVo> model = new ArrayList<>();
        List<LinkedHashMap<String, Object>> maps = new ArrayList<>();
        HeaderVo headerVo = new HeaderVo();
        headerVo.setField("维表");
        headerVo.setWidth(18*380);
        headerVo.setTitle("k3");
        model.add(headerVo);
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        map.put("k1", "15");
        map.put("k2", "20");
        LinkedHashMap<String, Object> map2 = new LinkedHashMap<>();
        map2.put("k3", "16");
        map2.put("k4", "22");
        maps.add(map);
        maps.add(map2);
        JSONArray jsonArray = configDimensionManageService.mapToObject(model, maps);
        Assert.assertNotNull(jsonArray);
    }

    @Test
    public void validImpModel() throws UnsupportedEncodingException {
        Attachment attachment=null;
        List<ExcelVO > heads=new ArrayList<>();
        List<HeaderVo> model=new ArrayList<>();
        UploadInfoVO uploadInfoVO=new UploadInfoVO();
        uploadInfoVO.setUserId(1175L);
        ByteArrayOutputStream byteArrayOutputStream=new ByteArrayOutputStream();
        ResultDataVO resultDataVO = new ResultDataVO();
        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        try {
            resultList = configDimensionManageService.validImpModel(attachment,
                heads, model, uploadInfoVO, byteArrayOutputStream);
        } catch (CommonApplicationException | IOException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultList);
    }

    @Test
    public void uploadImportExcel() throws CommonApplicationException, IOException {
        UploadInfoVO uploadInfoVO=new UploadInfoVO();
        List<Map> dataList=new ArrayList<>();
        List<HeaderVo> model=new ArrayList<>();
        List<Map> normalList=new ArrayList<>();
        boolean flag=false;

        uploadInfoVO.setFileName("映射维表");
        uploadInfoVO.setSheetName("维表2023");
        uploadInfoVO.setVersion("20230411-01");
        HeaderVo headerVo = new HeaderVo();
        headerVo.setTitle("title");
        headerVo.setField("维表");
        headerVo.setWidth(10*200);
        headerVo.setIsEditable(false);

        DmFoiImpExpRecordVO expRecordVO=new DmFoiImpExpRecordVO();
        expRecordVO.setRecordNum(25000);
        expRecordVO.setFileName("维表版本");
        expRecordVO.setFileSourceKey("fileSource");

        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);

        DmFoiImpExpRecordVO expRecordVO2 = configDimensionManageService.uploadImportExcel(uploadInfoVO, dataList,
            model, normalList, flag);
        Assert.assertNotNull(expRecordVO2);
    }
}