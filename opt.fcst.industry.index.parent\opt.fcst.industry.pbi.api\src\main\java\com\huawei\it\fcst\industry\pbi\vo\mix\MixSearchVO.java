/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.mix;

import com.huawei.it.fcst.industry.pbi.vo.common.CommonBaseVO;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

/**
 * MixSearchVO Class
 *
 * <AUTHOR>
 * @since 2024/7/5
 */
@Getter
@Setter
@EqualsAndHashCode
@NoArgsConstructor
public class MixSearchVO extends CommonBaseVO implements Serializable {

    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<String> costTypeList;

    private List<String> customGroupCodeList;

    private List<String> pspCustomGroupCodeList;

    private List<String> stdCustomGroupCodeList;

    private List<String> pspGroupCodeList;

    private List<String> stdGroupCodeList;

    private List<String> pspProdRndTeamCodeList;

    private List<String> stdProdRndTeamCodeList;

    // PSP成本/设备成本差异率阈值，大于这个值的数据需要展示出来
    private Double pspRate;

    private boolean ratioRateFlag;

    private String subGroupLevel;

    private String name;

    private String combTablePreFix;

    private String nextGroupLevel;

    private List<String> stdCodeList;

    private List<String> pspCodeList;

    private String distributeUnit;

    private String diffUnit;

    private String fileName;

    private String periodId;

    private String periodYear;

    // 重量级团队level
    private String teamLevel;

    private Integer basePeriodId;

    private String actualMonth;

    private String granularityTypeCnName;

    private List<String> customParentCodeList;

    private List<String> customPspParentCodeList;

    private List<String> customStdParentCodeList;

    private List<String> parentCodeList = new ArrayList<>();

    private List<String> parentPspGroupCodeList;

    private List<String> parentStdGroupCodeList;

    private String overseaFlagCnName;

    private Long customId;

    private List<Long> pspCustomIdList;

    private List<Long> stdCustomIdList;

    private List<Long> combIdList = new ArrayList<>();

    // 是否需要虚化 true,false
    private String isNeedBlur;

    private String userAccount;

    private Timestamp creationDate;

    private String exportTemplate;


}
