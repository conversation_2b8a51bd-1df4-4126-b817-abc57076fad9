/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.common;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;

/**
 * DmFoiImpExpRecordVO Class
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
@Getter
@Setter
@NoArgsConstructor
public class DmFoiImpExpRecordVO {
    private Integer id;

    // 文件名
    private String fileName;

    // 文件大小
    private String fileSize;

    // 页面模块
    private String pageModule;

    // 状态: 保存 Save，或者修改
    private String status;

    // 异常反馈
    private String exceptionFeedback;

    // 记录条数
    private Integer recordNum;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp creationDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp endDate;

    private String createdBy;

    private String userId;

    private String lastUpdatedBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp lastUpdateDate;

    private String delFlag;

    private String periodId;

    private String fileSourceKey;

    private String fileErrorKey;

    private String optType;

    private String moduleType;

    // 导入导出状态（OK：成功、FAIL：失败）
    private String recSts;

    // 文件类型，例如:xlsx
    private String fileType;

}
