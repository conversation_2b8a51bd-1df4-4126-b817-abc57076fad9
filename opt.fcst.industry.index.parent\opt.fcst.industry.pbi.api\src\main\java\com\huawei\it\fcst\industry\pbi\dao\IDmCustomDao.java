/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.dao;

import com.huawei.it.fcst.industry.pbi.vo.comb.CombTransformVO;
import com.huawei.it.fcst.industry.pbi.vo.comb.CommonViewVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstDimInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * IDmCustomDao Class
 *
 * <AUTHOR>
 * @since 2024/9/13
 */
public interface IDmCustomDao {

    List<DmFcstDimInfoVO> getCombinationCombList(CommonViewVO commonViewVO);

    List<DmFcstDimInfoVO> getCombinationCombByCustomId(CommonViewVO commonViewVO);

    DmFcstDimInfoVO getYtdFlagByAnnualPage(CommonViewVO commonViewVO);

    Long getCustomCombCountByName(CommonViewVO commonViewVO);

    void createCustomList(@Param("list") List<DmFcstDimInfoVO> customSubList, @Param("costType") String costType);

    void renameCustomCombination(CommonViewVO commonViewVO);

    void deleteCustomCombList(CommonViewVO commonViewVO);

    void updateCustomCombPageFlag(CommonViewVO commonViewVO);

    void updateCustomCombSeparate(CommonViewVO commonViewVO);

    List<DmFcstDimInfoVO> getCustomCombNameList(CommonViewVO commonViewVO);

    void updateCustomEnableById(CommonViewVO commonViewVO);

    void updateCombSubEnableList(@Param("commonViewVO") CommonViewVO commonViewVO, @Param("list") List<DmFcstDimInfoVO> diffGroupCodeList);

    String getCombAnnual(CombTransformVO combTransformVO);

    String getCombMonth(CombTransformVO combTransformVO);
}
