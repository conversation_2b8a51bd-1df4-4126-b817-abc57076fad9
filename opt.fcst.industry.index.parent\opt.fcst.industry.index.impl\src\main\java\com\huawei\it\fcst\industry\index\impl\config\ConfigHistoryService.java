/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.config;

import com.huawei.it.fcst.industry.index.dao.IDmFocDataRefreshStatusDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTopCateInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTopItemInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocVersionDao;
import com.huawei.it.fcst.industry.index.impl.combination.AsyncService;
import com.huawei.it.fcst.industry.index.service.config.IConfigHistoryService;
import com.huawei.it.fcst.industry.index.utils.FcstIndexUtil;
import com.huawei.it.fcst.industry.index.utils.UserInfoUtils;
import com.huawei.it.fcst.industry.index.vo.combination.DmFocDataRefreshStatus;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocTopCateInfoDTO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocTopItemInfoDTO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoDTO;
import com.huawei.it.fcst.industry.index.vo.config.HistoryInputVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.hutool.core.util.StrUtil;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;


import javax.inject.Inject;
import javax.inject.Named;

/**
 * ConfigHistoryService Class
 *
 * <AUTHOR>
 * @since 2023/3/14
 */
@Named("configHistoryService")
@JalorResource(code = "configHistoryService", desc = "配置管理历史清单服务")
public class ConfigHistoryService implements IConfigHistoryService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ConfigHistoryService.class);
    @Inject
    private IDmFocVersionDao dmFocVersionDao;

    @Inject
    private IDmFocTopCateInfoDao dmFocTopCateInfoDao;

    @Inject
    private IDmFocTopItemInfoDao dmFocTopItemInfoDao;

    @Inject
    private IDmFocDataRefreshStatusDao dataRefreshStatusDao;

    @Inject
    private AsyncService asyncService;

    @Inject
    private ConfigExportService configExportService;

    @Override
    @JalorOperation(code = "allCategoryList", desc = "全品类清单分页查询")
    public ResultDataVO allCategoryList(HistoryInputVO historyInputVO) throws CommonApplicationException {
        LOGGER.info("Begin ConfigHistoryService::allCategoryList");
        if (null == historyInputVO.getVersionId()) {
            throw new CommonApplicationException("入参清单数据版本为空");
        }
        // 设置报告期范围
        DmFocVersionInfoDTO versionVO = dmFocVersionDao.findDmFocVersionDTOById(historyInputVO.getVersionId(), historyInputVO.getTablePreFix());
        if (StrUtil.isBlank(versionVO.getVersion())) {
            throw new CommonApplicationException("入参清单数据版本名称为空");
        }
        List<String> yearPeriodList = FcstIndexUtil.getPeriod(versionVO.getVersion());
        // 设置品类查询对象
        DmFocTopCateInfoDTO build = configExportService.setTopCateVO(historyInputVO, yearPeriodList);
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(historyInputVO.getPageSize());
        pageVO.setCurPage(historyInputVO.getPageIndex());
        PagedResult<DmFocTopCateInfoDTO> cateByPage = dmFocTopCateInfoDao.findCateByPage(build, pageVO);
        Map result = new LinkedHashMap();
        result.put("result", cateByPage.getResult());
        result.put("pageVO", cateByPage.getPageVO());
        result.put("version", versionVO.getVersion());
        return ResultDataVO.success(result);
    }

    @Override
    @JalorOperation(code = "topItemList", desc = "规格品清单分页查询")
    public ResultDataVO topItemList(HistoryInputVO historyInputVO) throws CommonApplicationException {
        LOGGER.info("Begin ConfigHistoryService::topItemList");
        if (null == historyInputVO.getVersionId()) {
            throw new CommonApplicationException("入参清单数据版本为空");
        }
        // 设置报告期范围
        DmFocVersionInfoDTO versionVO = dmFocVersionDao.findDmFocVersionDTOById(historyInputVO.getVersionId(), historyInputVO.getTablePreFix());
        if (StrUtil.isBlank(versionVO.getVersion())) {
            throw new CommonApplicationException("入参清单数据版本名称为空");
        }
        List<String> yearPeriodList = FcstIndexUtil.getPeriod(versionVO.getVersion());
        // 设置品类查询对象
        DmFocTopItemInfoDTO build = configExportService.setTopItemVO(historyInputVO, yearPeriodList);
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(historyInputVO.getPageSize());
        pageVO.setCurPage(historyInputVO.getPageIndex());

        PagedResult<DmFocTopItemInfoDTO> itemByPage = dmFocTopItemInfoDao.findItemByPage(build, pageVO);
        Map result = new LinkedHashMap();
        result.put("result", itemByPage.getResult());
        result.put("pageVO", itemByPage.getPageVO());
        result.put("version", versionVO.getVersion());
        return ResultDataVO.success(result);
    }


    @Override
    @JalorOperation(code = "exportExcel", desc = "历史清单导出")
    @Audit(module = "configHistoryService-exportExcel", operation = "exportExcel", message = "历史清单导出")
    public ResultDataVO exportExcel(HistoryInputVO historyInputVO)
            throws CommonApplicationException {
        LOGGER.info("Begin ConfigHistoryService::exportExcel");
        if (null == historyInputVO.getVersionId()) {
            throw new CommonApplicationException("入参清单数据版本为空");
        }
        DmFocDataRefreshStatus dmFocDataRefreshStatus = new DmFocDataRefreshStatus();
        dmFocDataRefreshStatus.setStatus("TASK_INIT");
        dmFocDataRefreshStatus.setDelFlag("N");
        Long userId = UserInfoUtils.getUserId();
        dmFocDataRefreshStatus.setCreatedBy(userId);
        dmFocDataRefreshStatus.setLastUpdatedBy(userId);
        dmFocDataRefreshStatus.setCreationDate(new Date());
        dmFocDataRefreshStatus.setTaskFlag("CONFIG_" + historyInputVO.getIndustryOrg() + "_" + historyInputVO.getCostType() + "_EXPORT_" + historyInputVO.getCaliberFlag() + "_" + historyInputVO.getModelType());
        dmFocDataRefreshStatus.setTaskId(dataRefreshStatusDao.getDataRefrashKey());
        dmFocDataRefreshStatus.setRoleId(UserInfoUtils.getRoleId());
        // 创建异步任务记录，进行新增操作
        dataRefreshStatusDao.createDmFocDataRefreshStatus(dmFocDataRefreshStatus);
        IRequestContext current = RequestContextManager.getCurrent();
        // 异步导出数据
        asyncService.fillPurchaseExportData(historyInputVO, dmFocDataRefreshStatus, current);
        return ResultDataVO.success(dmFocDataRefreshStatus);
    }
}
