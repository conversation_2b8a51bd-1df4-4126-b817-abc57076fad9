/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.annual;

import com.huawei.it.fcst.industry.index.vo.common.TableNameVO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * AnnualParamVO Class
 *
 * <AUTHOR>
 * @since 2023/12/21
 */
@Getter
@Setter
@NoArgsConstructor
public class AnnualParamVO  extends TableNameVO {

    private List<String> parentCodeList;

    private List<String> groupCnNameList;

    private String groupLevel;

    private String nextGroupLevel;

    private String parentLevel;

    private String multiLevel;

    private String viewFlag;

    private String teamLevel;

    private String granularityType;

    private String caliberFlag;

    private List<String> yearList;

    private List<String> groupCodeList;

    private List<String> subGroupCodeList;


    private List<String> lv1ProdRdTeamCnName;

    private List<String> lv2ProdRdTeamCnName;

    private List<String> lv3ProdRdTeamCnName;

    private List<String> l1NameList;

    private List<String> l2NameList;

    private String lv1ProdRndTeamCode;

    private String lv2ProdRndTeamCode;

    private String lv3ProdRndTeamCode;

    private String lv4ProdRndTeamCode;

    private String l1Name;

    private String l2Name;

    private String lv3CegCnName;

    private String lv4CegCnName;

    private String categoryCnName;

    private String year;

    private Long periodYear;

    private Long versionId;

    /**
     * 重量级团队list
     */
    private List<String> teamCodeList;

    /**
     * LV0产业集合
     */
    private Set<String> lv0DimensionSet = new HashSet<>();

    /**
     * 重量级团队LV1集合
     */
    private Set<String> lv1DimensionSet  = new HashSet<>();

    /**
     * 重量级团队LV2集合
     */
    private Set<String> lv2DimensionSet  = new HashSet<>();

    /**
     * 重量级团队LV3集合
     */
    private Set<String> lv3DimensionSet  = new HashSet<>();

    /**
     * 国内/海外
     */
    private String overseaFlag;

    /**
     * BG编码
     */
    private String lv0ProdListCode;

    /**
     * 量纲维度code集合
     */
    private List<String> dmsCodeList;

    /**
     * 量纲
     */
    private  List<String> dimensionCnName;

    /**
     * 量纲子类
     */
    private List<String> dimensionSubCategoryCnName;


    /**
     * 量纲子类明细
     */
    private  List<String> dimensionSubDetailCnName;

    /**
     * 是否多选
     */
    private Boolean isMultipleSelect;

    private String parentCodeOrder;

    // 是否包含汇总组合
    private Boolean isContainComb;

    private String customLevel;

    // 反向视角标识
    private Boolean reverseLv1Flag;

    private Boolean reverseSymbol;

    private List<String> customIdList;

    private List<String> combinaCodeList;

    /**
     * 量纲code集合
     */
    private List<String> dimensionCodeList;

    /**
     * 量纲子类code集合
     */
    private List<String> dimensionSubcategoryCodeList;

    /**
     * 量纲子类明细code集合
     */
    private List<String> dimensionSubDetailCodeList;

    private List<String> spartCodeList;

    private List<String> spartCnName;

    private List<String> coaCodeList;

    private List<String> coaCnName;

    private List<String> purCodeList;

    private String purLevel;

    private String lv0ProdRndTeamCode;

    /**
     * 发货对象
     */
    private String shippingObjectCode;

    private String shippingObjectCnName;

    /**
     * 制造对象
     */
    private String manufactureObjectCode;

    private String manufactureObjectCnName;

    /**
     * 成本类型
     */
    private String costType;

    /**
     * 汇总组合导出名称拼接
     */
    private String excelExp;

    private List<String> prodTeamCodeList;

    // 是否关联code查询
    private boolean codeFlag;

}
