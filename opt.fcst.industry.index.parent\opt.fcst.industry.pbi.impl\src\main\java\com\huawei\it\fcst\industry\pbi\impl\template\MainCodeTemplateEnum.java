/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.template;

import com.huawei.it.fcst.export.IExcelTemplateBeanManager;
import com.huawei.it.fcst.export.vo.ExcelTemplateBeanManager;
import com.huawei.it.fcst.export.vo.SheetBeanMetaVO;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 定义配置管理-主力产品主力编码导出模板
 *
 * <AUTHOR>
 * @since 2024年7月12日
 */
@Getter
public enum MainCodeTemplateEnum implements IExcelTemplateBeanManager {
    MAIN_CODE_01("01", "ProdMainCodeExportTemplate", "配置管理-主力产品主力编码", "配置管理-主力产品主力编码") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            ExcelTemplateBeanManager excelTemplateBeanManager = new ExcelTemplateBeanManager();
            List<SheetBeanMetaVO> sheetBeans = new ArrayList<>();
            sheetBeans.add(new SheetBeanMetaVO(MAIN_CODE_01.templateName, 0, "MainCodeExportDataProvider", "配置管理-主力产品主力编码", Boolean.FALSE));
            excelTemplateBeanManager.setSheetBeans(sheetBeans);
            excelTemplateBeanManager.setTemplateName(MAIN_CODE_01.templateName);
            excelTemplateBeanManager.setModuleType(MAIN_CODE_01.moduleType);
            excelTemplateBeanManager.setDesc(MAIN_CODE_01.desc);
            return excelTemplateBeanManager;
        }
    };
    private String code;
    private String templateName;
    private String moduleType;
    private String desc;

    MainCodeTemplateEnum(String code, String templateName, String moduleType, String desc) {
        this.code = code;
        this.templateName = templateName;
        this.moduleType = moduleType;
        this.desc = desc;
    }
}
