/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.annual;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

/**
 * CommonAnnualVOTest Class
 *
 * <AUTHOR>
 * @since 2023/4/13
 */
public class CommonAnnualVOTest extends BaseVOCoverUtilsTest<CommonAnnualVO> {

    @Override
    protected Class<CommonAnnualVO> getTClass() {
        return CommonAnnualVO.class;
    }
}