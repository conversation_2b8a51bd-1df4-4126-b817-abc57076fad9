<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.pbi.dao.ICostReductDao">

    <sql id="tableName">
        fin_dm_opt_foi.DM_FCST_ICT_COST_RED_OBJ_T
    </sql>

    <insert id="batchInsertCostReductVOs">
        INSERT INTO
        fin_dm_opt_foi.DM_FCST_ICT_COST_RED_OBJ_T
        (version_id,lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,
            objective,created_by,creation_date,last_updated_by,last_update_date,del_flag,period_id)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.versionId,jdbcType=VARCHAR},
            #{item.lv1ProdRndTeamCode,jdbcType=VARCHAR},
            #{item.lv1ProdRdTeamCnName,jdbcType=VARCHAR},
            #{item.lv2ProdRndTeamCode,jdbcType=VARCHAR},
            #{item.lv2ProdRdTeamCnName,jdbcType=VARCHAR},
            #{item.objective,jdbcType=VARCHAR},
            #{userId,jdbcType=BIGINT},
            now(),
            #{userId,jdbcType=BIGINT},
            now(),
            'N',
            #{item.periodId,jdbcType=VARCHAR}
             )
        </foreach>
    </insert>
    <insert id="copyHisData">
        INSERT INTO
            fin_dm_opt_foi.DM_FCST_ICT_COST_RED_OBJ_T
        (version_id,lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,
         objective,created_by,creation_date,last_updated_by,last_update_date,del_flag,period_id)
        SELECT #{newVersionId},lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,
               objective,#{userId,jdbcType=BIGINT},NOW(),#{userId,jdbcType=BIGINT},NOW(),del_flag,period_id
        FROM  fin_dm_opt_foi.DM_FCST_ICT_COST_RED_OBJ_T
        WHERE   del_flag='N'
          AND  VERSION_ID =  #{oldVersionId}
    </insert>

    <update id="updateCostReductDataList">
        <foreach collection="list" item="item" separator=";">
            UPDATE
            <include refid="tableName"/>
                SET
            <if test='item.periodId != null and item.periodId!=""'>
                period_id=#{item.periodId,jdbcType=VARCHAR},
            </if>
            <if test='item.lv1ProdRndTeamCode != null and item.lv1ProdRndTeamCode!=""'>
                lv1_prod_rnd_team_code=#{item.lv1ProdRndTeamCode,jdbcType=VARCHAR},
            </if>
            <if test='item.lv1ProdRdTeamCnName != null and item.lv1ProdRdTeamCnName!=""'>
                lv1_prod_rd_team_cn_name=#{item.lv1ProdRdTeamCnName,jdbcType=VARCHAR},
            </if>
            <if test='item.lv2ProdRndTeamCode != null and item.lv2ProdRndTeamCode!=""'>
                lv2_prod_rnd_team_code= #{item.lv2ProdRndTeamCode,jdbcType=VARCHAR},
            </if>
            <if test='item.lv2ProdRdTeamCnName != null and item.lv2ProdRdTeamCnName!=""'>
                lv2_prod_rd_team_cn_name =#{item.lv2ProdRdTeamCnName,jdbcType=VARCHAR},
            </if>
            <if test='item.objective != null and item.objective!=""'>
                objective =#{item.objective,jdbcType=VARCHAR},
            </if>
            last_update_date = now(),
            last_updated_by = #{userId,jdbcType=BIGINT}
            WHERE DEL_FLAG = 'N'
            <if test='item.versionId != null and item.versionId!=""'>
                AND version_id=#{item.versionId,jdbcType=VARCHAR}
            </if>
            <if test='item.oldData.periodId != null and item.oldData.periodId!=""'>
                AND period_id=#{item.oldData.periodId,jdbcType=VARCHAR}
            </if>
            <if test='item.oldData.lv1ProdRndTeamCode != null and item.oldData.lv1ProdRndTeamCode!=""'>
                AND lv1_prod_rnd_team_code=#{item.oldData.lv1ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='item.oldData.lv1ProdRdTeamCnName != null and item.oldData.lv1ProdRdTeamCnName!=""'>
                AND lv1_prod_rd_team_cn_name=#{item.oldData.lv1ProdRdTeamCnName,jdbcType=VARCHAR}
            </if>
            <if test='item.oldData.lv2ProdRndTeamCode != null and item.oldData.lv2ProdRndTeamCode!=""'>
                AND lv2_prod_rnd_team_code= #{item.oldData.lv2ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='item.oldData.lv2ProdRdTeamCnName != null and item.oldData.lv2ProdRdTeamCnName!=""'>
                AND lv2_prod_rd_team_cn_name =#{item.oldData.lv2ProdRdTeamCnName,jdbcType=VARCHAR}
            </if>
        </foreach>
    </update>

    <delete id="deleteCostReductDataList">
        <foreach collection="list" item="item" separator=";">
            DELETE FROM
            <include refid="tableName"/>
            WHERE DEL_FLAG = 'N'
            <if test='item.versionId != null and item.versionId!=""'>
                AND version_id=#{item.versionId,jdbcType=VARCHAR}
            </if>
            <if test='item.periodId != null and item.periodId!=""'>
                AND period_id=#{item.periodId,jdbcType=VARCHAR}
            </if>
            <if test='item.lv1ProdRndTeamCode != null and item.lv1ProdRndTeamCode!=""'>
                AND  lv1_prod_rnd_team_code=#{item.lv1ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='item.lv1ProdRdTeamCnName != null and item.lv1ProdRdTeamCnName!=""'>
                AND lv1_prod_rd_team_cn_name=#{item.lv1ProdRdTeamCnName,jdbcType=VARCHAR}
            </if>
            <if test='item.lv2ProdRndTeamCode != null and item.lv2ProdRndTeamCode!=""'>
                AND lv2_prod_rnd_team_code= #{item.lv2ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='item.lv2ProdRdTeamCnName != null and item.lv2ProdRdTeamCnName!=""'>
                AND lv2_prod_rd_team_cn_name =#{item.lv2ProdRdTeamCnName,jdbcType=VARCHAR}
            </if>
        </foreach>
    </delete>

    <select id="findCostReductDropDown" resultType="com.huawei.it.fcst.industry.pbi.vo.config.CostReductVO">
        SELECT DISTINCT
        period_id periodId
        , lv1_prod_rd_team_cn_name lv1ProdRdTeamCnName
        <if test='lv1ProdRdTeamCnName != null and lv1ProdRdTeamCnName !=""'>
            , lv2_prod_rd_team_cn_name lv2ProdRdTeamCnName
        </if>
        FROM
            <include refid="tableName"/>
        WHERE del_flag = 'N'
            AND version_id = #{versionId,jdbcType=VARCHAR}
        <if test='periodId != null and periodId!=""'>
            AND period_id LIKE CONCAT(CONCAT('%', #{periodId,jdbcType=VARCHAR}::text ,'%'))
        </if>
        <if test='lv1ProdRdTeamCnName != null and lv1ProdRdTeamCnName!=""'>
            AND  lv1_prod_rd_team_cn_name LIKE CONCAT(CONCAT('%', #{lv1ProdRdTeamCnName,jdbcType=VARCHAR}::text ,'%'))
        </if>
        <if test='lv2ProdRdTeamCnName != null and lv2ProdRdTeamCnName!=""'>
            AND  lv2_prod_rd_team_cn_name LIKE CONCAT(CONCAT('%', #{lv2ProdRdTeamCnName,jdbcType=VARCHAR}::text ,'%'))
        </if>
        <if test='lv2ProdRdTeamCnName ==""'>
            AND  lv2_prod_rd_team_cn_name is null
        </if>
    </select>

    <select id="findCostReductByPage" resultType="com.huawei.it.fcst.industry.pbi.vo.config.CostReductVO">
        SELECT
            lv1_prod_rnd_team_code lv1ProdRndTeamCode,
            lv2_prod_rnd_team_code lv2ProdRndTeamCode,
            lv1_prod_rd_team_cn_name lv1ProdRdTeamCnName,
            lv2_prod_rd_team_cn_name lv2ProdRdTeamCnName,
            objective objective,
            version_id versionId,
            period_id periodId,
            created_by createdBy,
            creation_date creationDate,
            last_updated_by lastUpdatedBy,
            last_update_date lastUpdateDate
        FROM
            <include refid="tableName"/>
        WHERE del_flag = 'N'
        <if test='_parameter.get("0").lv1ProdRdTeamCnName != null and _parameter.get("0").lv1ProdRdTeamCnName!=""'>
            AND lv1_prod_rd_team_cn_name=#{0.lv1ProdRdTeamCnName,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").lv1ProdRndTeamCode != null and _parameter.get("0").lv1ProdRndTeamCode!=""'>
            AND lv1_prod_rnd_team_code=#{0.lv1ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").lv2ProdRdTeamCnName != null and _parameter.get("0").lv2ProdRdTeamCnName!=""'>
            AND lv2_prod_rd_team_cn_name=#{0.lv2ProdRdTeamCnName,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").lv2ProdRndTeamCode != null and _parameter.get("0").lv2ProdRndTeamCode!=""'>
            AND lv2_prod_rnd_team_code=#{0.lv2ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").versionId != null and _parameter.get("0").versionId!=""'>
            AND version_id=#{0.versionId,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").periodId != null and _parameter.get("0").periodId!=""'>
            AND period_id=#{0.periodId,jdbcType=VARCHAR}
        </if>
        ORDER BY lv1ProdRndTeamCode DESC
        LIMIT #{1.pageSize} OFFSET #{1.startIndex}-1
    </select>
    <select id="findCostReductByPageCount" resultType="java.lang.Integer">
        SELECT COUNT(1) from (
        SELECT
        lv1_prod_rnd_team_code lv1ProdRndTeamCode,
        lv2_prod_rnd_team_code lv2ProdRndTeamCode,
        lv1_prod_rd_team_cn_name lv1ProdRdTeamCnName,
        lv2_prod_rd_team_cn_name lv2ProdRdTeamCnName,
        objective objective,
        version_id versionId,
        period_id periodId,
        created_by createdBy,
        creation_date creationDate,
        last_updated_by lastUpdatedBy,
        last_update_date lastUpdateDate
        FROM
        <include refid="tableName"/>
        WHERE del_flag = 'N'
        <if test='_parameter.get("0").lv1ProdRdTeamCnName != null and _parameter.get("0").lv1ProdRdTeamCnName!=""'>
            AND lv1_prod_rd_team_cn_name=#{0.lv1ProdRdTeamCnName,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").lv1ProdRndTeamCode != null and _parameter.get("0").lv1ProdRndTeamCode!=""'>
            AND lv1_prod_rnd_team_code=#{0.lv1ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").lv2ProdRdTeamCnName != null and _parameter.get("0").lv2ProdRdTeamCnName!=""'>
            AND lv2_prod_rd_team_cn_name=#{0.lv2ProdRdTeamCnName,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").lv2ProdRndTeamCode != null and _parameter.get("0").lv2ProdRndTeamCode!=""'>
            AND lv2_prod_rnd_team_code=#{0.lv2ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").versionId != null and _parameter.get("0").versionId!=""'>
            AND version_id=#{0.versionId,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").periodId != null and _parameter.get("0").periodId!=""'>
            AND period_id=#{0.periodId,jdbcType=VARCHAR}
        </if>
        ORDER BY lv1ProdRndTeamCode DESC
        )
    </select>
    <select id="findCostReductExpByPage" resultType="com.huawei.it.fcst.industry.pbi.vo.config.CostReductVO">
        SELECT
        lv1_prod_rnd_team_code lv1ProdRndTeamCode,
        lv2_prod_rnd_team_code lv2ProdRndTeamCode,
        lv1_prod_rd_team_cn_name lv1ProdRdTeamCnName,
        lv2_prod_rd_team_cn_name lv2ProdRdTeamCnName,
        concat(ROUND(objective * 100,2),'%') objective,
        version_id versionId,
        period_id periodId,
        created_by createdBy,
        creation_date creationDate,
        last_updated_by lastUpdatedBy,
        last_update_date lastUpdateDate
        FROM
        <include refid="tableName"/>
        WHERE del_flag = 'N'
        <if test='_parameter.get("0").lv1ProdRdTeamCnName != null and _parameter.get("0").lv1ProdRdTeamCnName!=""'>
            AND lv1_prod_rd_team_cn_name=#{0.lv1ProdRdTeamCnName,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").lv1ProdRndTeamCode != null and _parameter.get("0").lv1ProdRndTeamCode!=""'>
            AND lv1_prod_rnd_team_code=#{0.lv1ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").lv2ProdRdTeamCnName != null and _parameter.get("0").lv2ProdRdTeamCnName!=""'>
            AND lv2_prod_rd_team_cn_name=#{0.lv2ProdRdTeamCnName,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").lv2ProdRndTeamCode != null and _parameter.get("0").lv2ProdRndTeamCode!=""'>
            AND lv2_prod_rnd_team_code=#{0.lv2ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").versionId != null and _parameter.get("0").versionId!=""'>
            AND version_id=#{0.versionId,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").periodId != null and _parameter.get("0").periodId!=""'>
            AND period_id=#{0.periodId,jdbcType=VARCHAR}
        </if>
        ORDER BY lv1ProdRndTeamCode DESC
        LIMIT #{1.pageSize} OFFSET #{1.startIndex}-1
    </select>
    <select id="findCostReductExpByPageCount" resultType="java.lang.Integer">
        SELECT COUNT(1) from (
        SELECT
        lv1_prod_rnd_team_code lv1ProdRndTeamCode,
        lv2_prod_rnd_team_code lv2ProdRndTeamCode,
        lv1_prod_rd_team_cn_name lv1ProdRdTeamCnName,
        lv2_prod_rd_team_cn_name lv2ProdRdTeamCnName,
        concat(ROUND(objective * 100,2),'%') objective,
        version_id versionId,
        period_id periodId,
        created_by createdBy,
        creation_date creationDate,
        last_updated_by lastUpdatedBy,
        last_update_date lastUpdateDate
        FROM
        <include refid="tableName"/>
        WHERE del_flag = 'N'
        <if test='_parameter.get("0").lv1ProdRdTeamCnName != null and _parameter.get("0").lv1ProdRdTeamCnName!=""'>
            AND lv1_prod_rd_team_cn_name=#{0.lv1ProdRdTeamCnName,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").lv1ProdRndTeamCode != null and _parameter.get("0").lv1ProdRndTeamCode!=""'>
            AND lv1_prod_rnd_team_code=#{0.lv1ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").lv2ProdRdTeamCnName != null and _parameter.get("0").lv2ProdRdTeamCnName!=""'>
            AND lv2_prod_rd_team_cn_name=#{0.lv2ProdRdTeamCnName,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").lv2ProdRndTeamCode != null and _parameter.get("0").lv2ProdRndTeamCode!=""'>
            AND lv2_prod_rnd_team_code=#{0.lv2ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").versionId != null and _parameter.get("0").versionId!=""'>
            AND version_id=#{0.versionId,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").periodId != null and _parameter.get("0").periodId!=""'>
            AND period_id=#{0.periodId,jdbcType=VARCHAR}
        </if>
        ORDER BY lv1ProdRndTeamCode DESC
        )
    </select>

    <select id="getRecordSize" resultType="java.lang.Integer">
        SELECT COUNT(1) from (
        SELECT
        version_id versionId,
        period_id periodId,
        lv1_prod_rnd_team_code lv1ProdRndTeamCode,
        lv2_prod_rnd_team_code lv2ProdRndTeamCode,
        lv1_prod_rd_team_cn_name lv1ProdRdTeamCnName,
        lv2_prod_rd_team_cn_name lv2ProdRdTeamCnName,
        concat(ROUND(objective * 100,2),'%') objective,
        created_by createdBy,
        creation_date creationDate,
        last_updated_by lastUpdatedBy,
        last_update_date lastUpdateDate
        FROM
        <include refid="tableName"/>
        WHERE del_flag = 'N'
        <if test='_parameter.get("0").lv2ProdRdTeamCnName != null and _parameter.get("0").lv2ProdRdTeamCnName!=""'>
            AND lv2_prod_rd_team_cn_name=#{0.lv2ProdRdTeamCnName,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").lv2ProdRndTeamCode != null and _parameter.get("0").lv2ProdRndTeamCode!=""'>
            AND lv2_prod_rnd_team_code=#{0.lv2ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").versionId != null and _parameter.get("0").versionId!=""'>
            AND version_id=#{0.versionId,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").periodId != null and _parameter.get("0").periodId!=""'>
            AND period_id=#{0.periodId,jdbcType=VARCHAR}
        </if>
        ORDER BY lv1ProdRndTeamCode DESC
        )
    </select>

    <select id="getSpecialInfoList" resultType="java.lang.String">
        SELECT concat(lv1_prod_rnd_team_code,lv2_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rd_team_cn_name,
                      period_id)
            info
        FROM
            <include refid="tableName"/>
        WHERE DEL_FLAG ='N'
        AND version_id = #{versionId,jdbcType=VARCHAR}
    </select>
    <select id="getVersionName" resultType="java.lang.String">
        SELECT VERSION
            FROm fin_dm_opt_foi.DM_FCST_ICT_VERSION_INFO_T
            WHERE DATA_TYPE = 'RED_DIM'
                AND version_id = #{versionId,jdbcType=VARCHAR}
    </select>
    <select id="getGroupLevelInfo" resultType="com.huawei.it.fcst.industry.pbi.vo.config.CostReductVO">
        SELECT distinct
            <if test="groupLevel == 'LV1'">
            lv1_prod_rnd_team_code  lv1ProdRndTeamCode , lv1_prod_rd_team_cn_name lv1ProdRdTeamCnName
            </if>
            <if test="groupLevel == 'LV2'">
                lv2_prod_rnd_team_code  lv2ProdRndTeamCode , lv2_prod_rd_team_cn_name lv2ProdRdTeamCnName
            </if>
        FROM
            fin_dm_opt_foi.dm_fcst_ict_psp_irb_mid_mon_spart_t
        WHERE DEL_FLAG ='N'
          AND version_id = (SELECT max(version_id) from fin_dm_opt_foi.dm_fcst_ict_psp_irb_mid_mon_spart_t
        WHERE DEL_FLAG ='N')
        <if test='lv1ProdRdTeamCnName != null and lv1ProdRdTeamCnName!=""'>
            AND  lv1_prod_rd_team_cn_name LIKE CONCAT(CONCAT('%', #{lv1ProdRdTeamCnName,jdbcType=VARCHAR}::text ,'%'))
        </if>
        <if test='lv2ProdRdTeamCnName != null and lv2ProdRdTeamCnName!=""'>
            AND  lv2_prod_rd_team_cn_name LIKE CONCAT(CONCAT('%', #{lv2ProdRdTeamCnName,jdbcType=VARCHAR}::text ,'%'))
        </if>
    </select>
</mapper>
