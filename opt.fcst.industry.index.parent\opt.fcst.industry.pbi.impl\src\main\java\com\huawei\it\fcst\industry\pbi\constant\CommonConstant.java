/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.constant;

import com.huawei.it.fcst.industry.pbi.enums.GroupLevelEnum;
import com.huawei.it.fcst.vo.HeaderVo;
import org.apache.poi.ss.usermodel.CellType;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * CommonConstant Class
 *
 * <AUTHOR>
 * @since 2024/7/2
 */
public class CommonConstant {

    // 请勿改成set
    public final static List<String> PROD_GROUP_LEVEL = new ArrayList<>();

    static {
        PROD_GROUP_LEVEL.add("LV0");
        PROD_GROUP_LEVEL.add("LV1");
        PROD_GROUP_LEVEL.add("LV2");
        PROD_GROUP_LEVEL.add("LV3");
        PROD_GROUP_LEVEL.add("LV4");
    }

    public final static Set<String> ENCRYPT_GROUP_LEVEL = new HashSet<String>();

    static {
        ENCRYPT_GROUP_LEVEL.add("DIMENSION");
        ENCRYPT_GROUP_LEVEL.add("SUBCATEGORY");
        ENCRYPT_GROUP_LEVEL.add("SUB_DETAIL");
        ENCRYPT_GROUP_LEVEL.add("SPART");
    }

    public final static Set<String> MIN_GROUP_LEVEL = new HashSet<String>();

    static {
        MIN_GROUP_LEVEL.add("SUB_DETAIL");
        MIN_GROUP_LEVEL.add("SPART");
    }

    public final static Set<String> COST_TYPE_LIST = new HashSet<String>();

    static {
        COST_TYPE_LIST.add("PSP");
        COST_TYPE_LIST.add("STD");
    }

    public final static Set<String> GRANULARITY_TYPE_LIST = new HashSet<String>();

    static {
        GRANULARITY_TYPE_LIST.add("IRB");
        GRANULARITY_TYPE_LIST.add("INDUS");
        GRANULARITY_TYPE_LIST.add("PROD");
    }

    public final static Set<String> GRANULARITY_TYPE_NOT_PROD_LIST = new HashSet<String>();

    static {
        GRANULARITY_TYPE_NOT_PROD_LIST.add("IRB");
        GRANULARITY_TYPE_NOT_PROD_LIST.add("INDUS");
    }

    public final static Set<String> SPECIAL_ROLES = new HashSet<String>();

    static {
        SPECIAL_ROLES.add("Marketing_Analyst_Ind");
        SPECIAL_ROLES.add("Region_Analyst_Ind");
    }

    public final static Set<String> PAGE_FLAG_LIST = new HashSet<String>();

    static {
        PAGE_FLAG_LIST.add("CONFIG");
    }

    /**
     * 关联计算状态的层级
     */
    public final static Set<String> ANNUAL_NO_MAIN_FLAG = new HashSet<>();
    static {
        // 编码属性层级
        ANNUAL_NO_MAIN_FLAG.add("CODE");
        ANNUAL_NO_MAIN_FLAG.add("SPART");
        ANNUAL_NO_MAIN_FLAG.add("SUB_DETAIL");
        ANNUAL_NO_MAIN_FLAG.add("SUBCATEGORY");
        ANNUAL_NO_MAIN_FLAG.add("DIMENSION");
    }

    /**
     * 关联计算状态的层级
     */
    public final static Set<String> GROUP_LEVEL_STATUS = new HashSet<>();
    static {
        // 编码替换名称
        GROUP_LEVEL_STATUS.add("REPLACE_NAME");
        GROUP_LEVEL_STATUS.add("SPART");
        GROUP_LEVEL_STATUS.add("SUB_DETAIL");
        GROUP_LEVEL_STATUS.add("SUBCATEGORY");
        GROUP_LEVEL_STATUS.add("DIMENSION");
    }

    /**
     * 替换关系层级
     */
    public final static Set<String> GROUP_LEVEL_RELATION = new HashSet<>();
    static {
        GROUP_LEVEL_RELATION.add("REPLACE_TYPE");
        GROUP_LEVEL_RELATION.add("REPLACE_NAME");
        GROUP_LEVEL_RELATION.add("RELATION_TYPE");
    }

    /**
     * pbi层级
     */
    public final static Set<String> PBI_LEVEL_RELATION = new HashSet<>();
    static {
        PBI_LEVEL_RELATION.add("LV1");
        PBI_LEVEL_RELATION.add("LV2");
        PBI_LEVEL_RELATION.add("LV3");
        PBI_LEVEL_RELATION.add("LV4");
    }

    public final static Map<String, Integer> BG_CODE_RULE = new HashMap<>();

    static {
        BG_CODE_RULE.put("GR", 1);
        BG_CODE_RULE.put("PDCG901160", 2);
        BG_CODE_RULE.put("PDCG901159", 3);
    }

    public final static Map<String, Integer> REGION_CODE_RULE = new HashMap<>();

    static {
        REGION_CODE_RULE.put("GLOBAL",1);
    }

    public final static Map<String, Integer> REPOFFICE_CODE_RULE = new HashMap<>();

    static {
        REPOFFICE_CODE_RULE.put("ALL",1);
    }

    public final static Map<String, Integer> OVERSEA_FLAG_CODE_RULE = new HashMap<>();

    static {
        OVERSEA_FLAG_CODE_RULE.put("G",1);
        OVERSEA_FLAG_CODE_RULE.put("N",2);
        OVERSEA_FLAG_CODE_RULE.put("Y",3);
    }

    public final static Map<String, Integer> MAIN_FLAG_RULE = new HashMap<>();

    static {
        MAIN_FLAG_RULE.put("N",1);
        MAIN_FLAG_RULE.put("Y",2);
    }

    /**
     * 不包含spart层级
     */
    public final static Set<String> GROUP_LEVEL_DIMENSION = new HashSet<String>();

    static {
        GROUP_LEVEL_DIMENSION.add(GroupLevelEnum.DIMENSION.getValue());
        GROUP_LEVEL_DIMENSION.add(GroupLevelEnum.SUBCATEGORY.getValue());
        GROUP_LEVEL_DIMENSION.add(GroupLevelEnum.SUB_DETAIL.getValue());
    }

    public static final String REGION_ANALYST_IND = "Region_Analyst_Ind";

    public static final String MARKETING_ANALYST_IND = "Marketing_Analyst_Ind";

    public static final String SELECTION_LEVEL_SPART_LEVEL = "SPART";

    public static final String SELECTION_LEVEL_TOP_SPART_LEVEL = "SPART_TOP";

    public static final String SELECTION_LEVEL_PBI_LEVEL = "PBI";

    public static final String CODE_TYPE_NEW = "NEW";

    public static final String CODE_TYPE_OLD = "OLD";

    public static final String CODE_TYPE_SAME = "SAME";

    // 月度
    public static final String MONTH_N = "MON";

    // 月度累计
    public static final String MONTH_ACC = "YTD";

    public static final String ENABLE_FLAG_Y = "Y";

    public static final String BLUR_TRUE = "true,true";

    public static final String BLUR_TWO_CONDITION = "true,false";

    public static final String BLUR_FALSE = "false,false";

    public static final String PAGE_FLAG_ALL = "ALL";

    public static final String SPART_NAME = "SPART";

    public static final String GR = "GR";


    public static final Map<String, String> pbiMap = new HashMap<String, String>();

    public static final Map<String, String> overseaMap = new HashMap<String, String>();

    public static final Map<String, String> costTypeMap = new HashMap<String, String>();

    static {
        pbiMap.put("IRB", "重量级团队目录");
        pbiMap.put("INDUS", "产业目录");
        pbiMap.put("PROD", "销售目录");
        overseaMap.put("N", "国内");
        overseaMap.put("Y", "海外");
        overseaMap.put("G", "全球");
        costTypeMap.put("PSP", "PSP");
        costTypeMap.put("STD", "标准成本");
        costTypeMap.put("PREDICT", "预估成本");
    }

    public static final Map<String, String> groupLevelMap = new HashMap<String, String>();
    static {
        groupLevelMap.put("LV0", "L0");
        groupLevelMap.put("LV1", "L1");
        groupLevelMap.put("LV2", "L2");
        groupLevelMap.put("LV3", "L3");
        groupLevelMap.put("LV4", "L3.5");
        groupLevelMap.put("SPART", "SPART");
    }

    public static final List<HeaderVo> DATA_REVIEW_HEADER = new LinkedList<>();
    static {
        DATA_REVIEW_HEADER.add(new HeaderVo("操作类型", "modifyType", CellType.STRING,true, 12 * 480));
        DATA_REVIEW_HEADER.add(new HeaderVo("成本类型", "costType", CellType.STRING,true, 12 * 480));
        DATA_REVIEW_HEADER.add(new HeaderVo("PBI目录", "granularityType",CellType.STRING,true,12 * 480));
        DATA_REVIEW_HEADER.add(new HeaderVo("国内/海外", "overseaFlag",CellType.STRING,true, 12 * 480));
        DATA_REVIEW_HEADER.add(new HeaderVo("BG", "bgCnName",CellType.STRING,true, 12 * 480));
        DATA_REVIEW_HEADER.add(new HeaderVo("地区部", "regionCnName",CellType.STRING,true,12 * 480));
        DATA_REVIEW_HEADER.add(new HeaderVo("代表处", "repofficeCnName",CellType.STRING,true, 12 * 480));
        DATA_REVIEW_HEADER.add(new HeaderVo("L1名称","lv1CnName", CellType.STRING,true,12 * 480));
        DATA_REVIEW_HEADER.add(new HeaderVo("L1编码", "lv1Code",CellType.STRING,true, 15 * 480));
        DATA_REVIEW_HEADER.add(new HeaderVo("L2名称","lv2CnName", CellType.STRING,true,12 * 480));
        DATA_REVIEW_HEADER.add(new HeaderVo("L2编码", "lv2Code",CellType.STRING,true, 15 * 480));
        DATA_REVIEW_HEADER.add(new HeaderVo("L3名称","lv3CnName", CellType.STRING,true,12 * 480));
        DATA_REVIEW_HEADER.add(new HeaderVo("L3编码", "lv3Code",CellType.STRING,true, 15 * 480));
        DATA_REVIEW_HEADER.add(new HeaderVo("L3.5名称","lv4CnName", CellType.STRING,true,12 * 480));
        DATA_REVIEW_HEADER.add(new HeaderVo("L3.5编码", "lv4Code",CellType.STRING,true, 15 * 480));
        DATA_REVIEW_HEADER.add(new HeaderVo("SPART编码", "spartCode",CellType.STRING,true, 15 * 480));
        DATA_REVIEW_HEADER.add(new HeaderVo("合同号", "hwContractNum",CellType.STRING,true, 15 * 480));
        DATA_REVIEW_HEADER.add(new HeaderVo("起始期", "beginDate",CellType.STRING,true, 15 * 480));
        DATA_REVIEW_HEADER.add(new HeaderVo("终止期", "endDate",CellType.STRING,true, 15 * 480));
        DATA_REVIEW_HEADER.add(new HeaderVo("修改理由", "modifyReasonM",CellType.STRING,true, 15 * 480));
        DATA_REVIEW_HEADER.add(new HeaderVo("撤销理由", "modifyReasonR",CellType.STRING,true, 15 * 480));
    }

    public static final String VARCHAR = "VARCHAR";

    public static final String IS_NOT = "N";

    public static final String HISTORY_PAGE = "history";

    public static final String ABNORMAL_PAGE = "abnormal";

    public static final String DATA_REVIEW_TYPE = "成本指数-ICT-底层数据审视";

    public static final String MAIN_CODE_TYPE = "成本指数-ICT-主力产品主力编码";

    public static final String RED_TARGET_TYPE = "成本指数-ICT-降成本目标";

    public static final String REPLACE_DIM_TYPE = "成本指数-ICT-新旧编码替换";

    public static final String NO_PERMISSION_TO_DOWNLOAD = "No permission to download.";

    public static final String INDUSTRY_MIX_TEMPLATE3_PATH = "excel/export/template/MixExportTemplate3.xlsx";

    public static final String INDUSTRY_MIX_TEMPLATE2_PATH = "excel/export/template/MixExportTemplate2.xlsx";

    public static final String INDUSTRY_MIX_TEMPLATE1_PATH = "excel/export/template/MixExportTemplate.xlsx";

    public static final String INDUSTRY_MIX_TEMPLATE4_PATH = "excel/export/template/MixExportTemplate4.xlsx";

    public static final String INDUSTRY_MIX_TEMPLATE5_PATH = "excel/export/template/MixExportTemplate5.xlsx";

    public static final String MAIN_CODE_TEMPLATE_PATH ="excel/export/template/MainCodeImportTemplate.xlsx";

    public static final String RED_DIM_TEMPLATE_PATH ="excel/export/template/RedDimImportTemplate.xlsx";

    public static final String REPLACE_TEMPLATE_PATH ="excel/export/template/ReplaceImportTemplate.xlsx";

    public static final String DATA_REVIEW_TEMPLATE_PATH ="excel/export/template/dataReviewTemplate.xlsx";

    public static final Pattern PATTERN_COLUMN = Pattern.compile("(_([a-z]))");

    public static final String COST_TYPE = "成本类型:";

    public static final String SOFTWARE_MARK = "软硬件标识:";

    public static final String SOFTWARE_MARK_NAME = "PSP软硬件标识:";

    public static final String GRANULARITY_TYPE_NAME = "PBI目录树:";

    public static final String OVERSEA_FLAG_NAME = "国内/海外:";

    public static final String BG_NAME = "BG:";

    public static final String ACTUAL_MONTH = "本期实际数截止月:";

    public static final String UNIT = "单位:CNY";

    public static final String REGION_NAME = "地区部:";

    public static final String REPOFFICE_NAME = "代表处:";

    public static final String BASE_PERIODID = "基期:";

    public static final String MAINFLAG_NAME = "SPART范围:";

    public static final String EXP_NAME = "名称";

    public static final String EXP_CODE = "编码";

    public static final String ALL_CH = "全选";

    static {
        pbiMap.put("IRB", "重量级团队目录");
        pbiMap.put("INDUS", "产业目录");
        pbiMap.put("PROD", "销售目录");
        overseaMap.put("N", "国内");
        overseaMap.put("Y", "海外");
        overseaMap.put("G", "全球");
        costTypeMap.put("PSP", "PSP");
        costTypeMap.put("STD", "标准成本");
        costTypeMap.put("PREDICT", "预估成本");
    }
    public static final Map<String, String> overseaFlagMap = new HashMap<String, String>();

    static {
        overseaFlagMap.put("全球", "G");
        overseaFlagMap.put("国内", "N");
        overseaFlagMap.put("海外", "Y");
    }

    public final static Set<String> MODIFY_TYPE = new HashSet<String>();

    static {
        MODIFY_TYPE.add("INSERT");
        MODIFY_TYPE.add("MODIFY");
    }

    public final static Map<String, String> allCombPageFlag = new HashMap<>(4);

    static {
        allCombPageFlag.put("ANNUAL", "MONTH");
        allCombPageFlag.put("MONTH", "ANNUAL");
        allCombPageFlag.put("ALL_ANNUAL", "ALL_MONTH");
        allCombPageFlag.put("ALL_MONTH", "ALL_ANNUAL");
    }
}