/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.replace;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * AnnualAnalysisVO Class
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
@Getter
@Setter
@NoArgsConstructor
public class ReplaceAnalysisVO implements Serializable  {
    private static final long serialVersionUID = -1576704097209028139L;

    private List<String> parentCodeList;

    private String groupLevel;

    private String nextGroupLevel;

    private String parentLevel;

    private String viewFlag;

    private List<String> yearList;

    private int pageIndex;

    private int pageSize;

    private List<String> groupCodeList;

    private List<String> groupCnNameList;

    private List<String> subGroupCodeList;

    private String orderColumn;

    private String orderMethod;

    private String dataType;

    private String year;

    private Long versionId;

    private String fileName;

    public String maxValue;

    private String groupCodeOrder;

    /**
     * 业务口径（R:收入时点/C：发货成本）
     */
    private String caliberFlag;

    /**
     * L3多选
     */
    private List<String> prodRndTeamCodeList;

    /**
     * ICT产业集合
     */
    private Set<String> lv0DimensionSet = new HashSet<>();

    /**
     * 重量级团队LV1集合
     */
    private Set<String> lv1DimensionSet  = new HashSet<>();

    /**
     * 重量级团队LV2集合
     */
    private Set<String> lv2DimensionSet  = new HashSet<>();

    /**
     * 重量级团队LV1CODE
     */
    private List<String> lv1ProdRdTeamCnName;

    /**
     * 重量级团队LV2CODE
     */
    private List<String> lv2ProdRdTeamCnName;

    /**
     * 重量级团队LV3CODE
     */
    private List<String> lv3ProdRdTeamCnName;

    private List<String> lv4ProdRdTeamCnName;

    /**
     * 会计期年份
     */
    private Long periodYear;

    /**
     * 国内/海外
     */
    private String overseaFlag;

    /**
     * BG编码
     */
    private String lv0ProdListCode;

    private String parentCodeOrder;

    /**
     * 成本类型
     */
    private String costType;

    /**
     * 基期开始时间
     */
    private Integer periodStartTime;

    /**
     * 基期结束时间
     */
    private Integer periodEndTime;

    private Boolean isMultipleSelect;

}

