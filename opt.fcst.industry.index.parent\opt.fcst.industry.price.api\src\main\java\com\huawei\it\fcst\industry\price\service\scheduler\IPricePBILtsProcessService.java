/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.service.scheduler;

import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import java.util.Map;

/**
 * pbi 虚化任务lts 触发接口
 *
 * <AUTHOR>
 * @since 202407
 *
 */
@Path("/lts")
@Consumes(MediaType.APPLICATION_JSON + ";charset=UTF-8")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface IPricePBILtsProcessService {

    /**
     * lts 触发接口
     *
     * @param param 参数
     * @return 成功，失败
     * @throws ApplicationException 异常信息
     */
    @POST
    @Path("/processTriggerTask")
    ResultDataVO processTriggerTask (Map<String,Object> param) throws ApplicationException;

}
