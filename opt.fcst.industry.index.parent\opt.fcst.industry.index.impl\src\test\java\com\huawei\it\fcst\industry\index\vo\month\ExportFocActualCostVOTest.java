/*
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.month;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @since 2023/5/9
 */
public class ExportFocActualCostVOTest extends BaseVOCoverUtilsTest<ExportFocActualCostVO> {
    @Override
    protected Class<ExportFocActualCostVO> getTClass() { return ExportFocActualCostVO.class; }

    @Test
    public void testMethod() {
        ExportFocActualCostVO dmFocActualCostVO = new ExportFocActualCostVO();
        dmFocActualCostVO.setVersionId(100L);
        dmFocActualCostVO.getVersionId();
        dmFocActualCostVO.setGroupCode("code");
        dmFocActualCostVO.getGroupCode();
        dmFocActualCostVO.setGroupCnName("cnName");
        dmFocActualCostVO.getGroupCnName();
        dmFocActualCostVO.setActualCostAmt(45.12);
        dmFocActualCostVO.getActualCostAmt();
        dmFocActualCostVO.setId(4L);
        dmFocActualCostVO.getId();
        dmFocActualCostVO.setPeriodId(2021L);
        dmFocActualCostVO.getPeriodId();
        dmFocActualCostVO.getCostType();
        dmFocActualCostVO.setCostType("11");
        dmFocActualCostVO.getParentCnName();
        dmFocActualCostVO.setParentCnName("666");
        Assert.assertNotNull(dmFocActualCostVO);
    }
}