/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2018-2019. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.utils;

import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumMadeD;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumMadeP;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumMadeU;
import com.huawei.it.fcst.industry.index.enums.IndustryConst;
import com.huawei.it.fcst.industry.index.enums.IndustryIndexEnum;
import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * ParseData.
 *
 * <AUTHOR>
 * @since 2019年12月16日
 */
public class FcstIndexMadeUtil {
    /** LOG 日志. */
    private static final Logger LOGGER = LoggerFactory.getLogger(FcstIndexMadeUtil.class);

    // group层级下一层级映射，视角5
    private static Map<String, String> madeNextFiveGroupLevelMapProfit = new HashMap<>(7);

    static {
        madeNextFiveGroupLevelMapProfit.put(GroupLevelEnumMadeP.LV0.getValue(), GroupLevelEnumMadeP.LV1.getValue());
        madeNextFiveGroupLevelMapProfit.put(GroupLevelEnumMadeP.LV1.getValue(), GroupLevelEnumMadeP.LV2.getValue());
        madeNextFiveGroupLevelMapProfit.put(GroupLevelEnumMadeP.LV2.getValue(), GroupLevelEnumMadeP.L1.getValue());
        madeNextFiveGroupLevelMapProfit.put(GroupLevelEnumMadeP.L1.getValue(), GroupLevelEnumMadeP.L2.getValue());
        madeNextFiveGroupLevelMapProfit.put(GroupLevelEnumMadeP.L2.getValue(), GroupLevelEnumMadeP.SHIPPING_OBJECT.getValue());
        madeNextFiveGroupLevelMapProfit.put(GroupLevelEnumMadeP.SHIPPING_OBJECT.getValue(), GroupLevelEnumMadeP.MANUFACTURE_OBJECT.getValue());
        madeNextFiveGroupLevelMapProfit.put(GroupLevelEnumMadeP.MANUFACTURE_OBJECT.getValue(), GroupLevelEnumMadeP.ITEM.getValue());
    }

    // group层级下一层级映射，视角4
    private static Map<String, String> madeNextFourGroupLevelMapProfit = new HashMap<>(6);

    static {
        madeNextFourGroupLevelMapProfit.put(GroupLevelEnumMadeP.LV0.getValue(), GroupLevelEnumMadeP.LV1.getValue());
        madeNextFourGroupLevelMapProfit.put(GroupLevelEnumMadeP.LV1.getValue(), GroupLevelEnumMadeP.LV2.getValue());
        madeNextFourGroupLevelMapProfit.put(GroupLevelEnumMadeP.LV2.getValue(), GroupLevelEnumMadeP.L1.getValue());
        madeNextFourGroupLevelMapProfit.put(GroupLevelEnumMadeP.L1.getValue(), GroupLevelEnumMadeP.SHIPPING_OBJECT.getValue());
        madeNextFourGroupLevelMapProfit.put(GroupLevelEnumMadeP.SHIPPING_OBJECT.getValue(), GroupLevelEnumMadeP.MANUFACTURE_OBJECT.getValue());
        madeNextFourGroupLevelMapProfit.put(GroupLevelEnumMadeP.MANUFACTURE_OBJECT.getValue(), GroupLevelEnumMadeP.ITEM.getValue());
    }


    // group层级下一层级映射，反向视角6
    private static Map<String, String> madeNextSixGroupLevelMap = new HashMap<>(6);

    static {
        madeNextSixGroupLevelMap.put(GroupLevelEnumMadeU.LV0.getValue(), GroupLevelEnumMadeU.SHIPPING_OBJECT.getValue());
        madeNextSixGroupLevelMap.put(GroupLevelEnumMadeU.SHIPPING_OBJECT.getValue(), GroupLevelEnumMadeU.MANUFACTURE_OBJECT.getValue());
        madeNextSixGroupLevelMap.put(GroupLevelEnumMadeU.MANUFACTURE_OBJECT.getValue(), GroupLevelEnumMadeU.LV1.getValue());
        madeNextSixGroupLevelMap.put(GroupLevelEnumMadeU.LV1.getValue(), GroupLevelEnumMadeU.LV2.getValue());
        madeNextSixGroupLevelMap.put(GroupLevelEnumMadeU.LV2.getValue(), GroupLevelEnumMadeU.LV3.getValue());
        madeNextSixGroupLevelMap.put(GroupLevelEnumMadeU.LV3.getValue(), GroupLevelEnumMadeU.LV4.getValue());
    }

    // group层级下一层级映射，反向视角5
    private static Map<String, String> madeNextFiveGroupLevelMap = new HashMap<>(5);

    static {
        madeNextFiveGroupLevelMap.put(GroupLevelEnumMadeU.LV0.getValue(), GroupLevelEnumMadeU.SHIPPING_OBJECT.getValue());
        madeNextFiveGroupLevelMap.put(GroupLevelEnumMadeU.SHIPPING_OBJECT.getValue(), GroupLevelEnumMadeU.LV1.getValue());
        madeNextFiveGroupLevelMap.put(GroupLevelEnumMadeU.LV1.getValue(), GroupLevelEnumMadeU.LV2.getValue());
        madeNextFiveGroupLevelMap.put(GroupLevelEnumMadeU.LV2.getValue(), GroupLevelEnumMadeU.LV3.getValue());
        madeNextFiveGroupLevelMap.put(GroupLevelEnumMadeU.LV3.getValue(), GroupLevelEnumMadeU.LV4.getValue());
    }

    // group层级下一层级映射，视角5(IAS)
    private static Map<String, String> madeNextFiveGroupLevelMapIas = new HashMap<>(7);

    static {
        madeNextFiveGroupLevelMapIas.put(GroupLevelEnumMadeU.LV0.getValue(), GroupLevelEnumMadeU.LV1.getValue());
        madeNextFiveGroupLevelMapIas.put(GroupLevelEnumMadeU.LV1.getValue(), GroupLevelEnumMadeU.LV2.getValue());
        madeNextFiveGroupLevelMapIas.put(GroupLevelEnumMadeU.LV2.getValue(), GroupLevelEnumMadeU.LV3.getValue());
        madeNextFiveGroupLevelMapIas.put(GroupLevelEnumMadeU.LV3.getValue(), GroupLevelEnumMadeU.LV4.getValue());
        madeNextFiveGroupLevelMapIas.put(GroupLevelEnumMadeU.LV4.getValue(), GroupLevelEnumMadeU.SHIPPING_OBJECT.getValue());
        madeNextFiveGroupLevelMapIas.put(GroupLevelEnumMadeU.SHIPPING_OBJECT.getValue(), GroupLevelEnumMadeU.MANUFACTURE_OBJECT.getValue());
        madeNextFiveGroupLevelMapIas.put(GroupLevelEnumMadeU.MANUFACTURE_OBJECT.getValue(), GroupLevelEnumMadeU.ITEM.getValue());
    }

    // group层级下一层级映射，视角4
    private static Map<String, String> madeNextFourGroupLevelMap = new HashMap<>(6);

    static {
        madeNextFourGroupLevelMap.put(GroupLevelEnumMadeU.LV0.getValue(), GroupLevelEnumMadeU.LV1.getValue());
        madeNextFourGroupLevelMap.put(GroupLevelEnumMadeU.LV1.getValue(), GroupLevelEnumMadeU.LV2.getValue());
        madeNextFourGroupLevelMap.put(GroupLevelEnumMadeU.LV2.getValue(), GroupLevelEnumMadeU.LV3.getValue());
        madeNextFourGroupLevelMap.put(GroupLevelEnumMadeU.LV3.getValue(), GroupLevelEnumMadeU.SHIPPING_OBJECT.getValue());
        madeNextFourGroupLevelMap.put(GroupLevelEnumMadeU.SHIPPING_OBJECT.getValue(), GroupLevelEnumMadeU.MANUFACTURE_OBJECT.getValue());
        madeNextFourGroupLevelMap.put(GroupLevelEnumMadeU.MANUFACTURE_OBJECT.getValue(), GroupLevelEnumMadeU.ITEM.getValue());
    }

    // group层级下一层级映射，视角3
    private static Map<String, String> madeNextThreeGroupLevelMap1 = new HashMap<>(5);

    static {
        madeNextThreeGroupLevelMap1.put(GroupLevelEnumMadeU.LV0.getValue(), GroupLevelEnumMadeU.LV1.getValue());
        madeNextThreeGroupLevelMap1.put(GroupLevelEnumMadeU.LV1.getValue(), GroupLevelEnumMadeU.LV2.getValue());
        madeNextThreeGroupLevelMap1.put(GroupLevelEnumMadeU.LV2.getValue(), GroupLevelEnumMadeU.SHIPPING_OBJECT.getValue());
        madeNextThreeGroupLevelMap1.put(GroupLevelEnumMadeU.SHIPPING_OBJECT.getValue(), GroupLevelEnumMadeU.MANUFACTURE_OBJECT.getValue());
        madeNextThreeGroupLevelMap1.put(GroupLevelEnumMadeU.MANUFACTURE_OBJECT.getValue(), GroupLevelEnumMadeU.ITEM.getValue());
    }

    // group层级下一层级映射，视角1
    private static Map<String, String> madeNextOneGroupLvlMap = new HashMap<>(3);

    static {
        madeNextOneGroupLvlMap.put(GroupLevelEnumMadeU.LV0.getValue(), GroupLevelEnumMadeU.SHIPPING_OBJECT.getValue());
        madeNextOneGroupLvlMap.put(GroupLevelEnumMadeU.SHIPPING_OBJECT.getValue(), GroupLevelEnumMadeU.MANUFACTURE_OBJECT.getValue());
        madeNextOneGroupLvlMap.put(GroupLevelEnumMadeU.MANUFACTURE_OBJECT.getValue(), GroupLevelEnumMadeU.ITEM.getValue());
    }

    // group层级下一层级映射，视角2
    private static Map<String, String> madeNextTwoGroupLvlMap = new HashMap<>(4);

    static {
        madeNextTwoGroupLvlMap.put(GroupLevelEnumMadeU.LV0.getValue(), GroupLevelEnumMadeU.LV1.getValue());
        madeNextTwoGroupLvlMap.put(GroupLevelEnumMadeU.LV1.getValue(), GroupLevelEnumMadeU.SHIPPING_OBJECT.getValue());
        madeNextTwoGroupLvlMap.put(GroupLevelEnumMadeU.SHIPPING_OBJECT.getValue(), GroupLevelEnumMadeU.MANUFACTURE_OBJECT.getValue());
        madeNextTwoGroupLvlMap.put(GroupLevelEnumMadeU.MANUFACTURE_OBJECT.getValue(), GroupLevelEnumMadeU.ITEM.getValue());
    }

    // 量纲颗粒度group层级下一层级映射，视角1
    private static Map<String, String> madeNextDimensionOneGroupLvlMap = new HashMap<>(5);
    static {
        madeNextDimensionOneGroupLvlMap.put(GroupLevelEnumMadeD.LV0.getValue(), GroupLevelEnumMadeD.LV1.getValue());
        madeNextDimensionOneGroupLvlMap.put(GroupLevelEnumMadeD.LV1.getValue(), GroupLevelEnumMadeD.DIMENSION.getValue());
        madeNextDimensionOneGroupLvlMap.put(GroupLevelEnumMadeD.DIMENSION.getValue(), GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue());
        madeNextDimensionOneGroupLvlMap.put(GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue(), GroupLevelEnumMadeD.MANUFACTURE_OBJECT.getValue());
        madeNextDimensionOneGroupLvlMap.put(GroupLevelEnumMadeD.MANUFACTURE_OBJECT.getValue(), GroupLevelEnumMadeD.ITEM.getValue());
    }

    // 量纲颗粒度group层级下一层级映射，视角2
    private static Map<String, String> madeNextDimensionTwoGroupLvlMap = new HashMap<>(6);
    static {
        madeNextDimensionTwoGroupLvlMap.put(GroupLevelEnumMadeD.LV0.getValue(), GroupLevelEnumMadeD.LV1.getValue());
        madeNextDimensionTwoGroupLvlMap.put(GroupLevelEnumMadeD.LV1.getValue(), GroupLevelEnumMadeD.DIMENSION.getValue());
        madeNextDimensionTwoGroupLvlMap.put(GroupLevelEnumMadeD.DIMENSION.getValue(), GroupLevelEnumMadeD.SUBCATEGORY.getValue());
        madeNextDimensionTwoGroupLvlMap.put(GroupLevelEnumMadeD.SUBCATEGORY.getValue(), GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue());
        madeNextDimensionTwoGroupLvlMap.put(GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue(), GroupLevelEnumMadeD.MANUFACTURE_OBJECT.getValue());
        madeNextDimensionTwoGroupLvlMap.put(GroupLevelEnumMadeD.MANUFACTURE_OBJECT.getValue(), GroupLevelEnumMadeD.ITEM.getValue());
    }

    // 量纲颗粒度group层级下一层级映射，视角3
    private static Map<String, String> madeNextDimensionThreeGroupLvlMap = new HashMap<>(7);
    static {
        madeNextDimensionThreeGroupLvlMap.put(GroupLevelEnumMadeD.LV0.getValue(), GroupLevelEnumMadeD.LV1.getValue());
        madeNextDimensionThreeGroupLvlMap.put(GroupLevelEnumMadeD.LV1.getValue(), GroupLevelEnumMadeD.DIMENSION.getValue());
        madeNextDimensionThreeGroupLvlMap.put(GroupLevelEnumMadeD.DIMENSION.getValue(), GroupLevelEnumMadeD.SUBCATEGORY.getValue());
        madeNextDimensionThreeGroupLvlMap.put(GroupLevelEnumMadeD.SUBCATEGORY.getValue(), GroupLevelEnumMadeD.SUB_DETAIL.getValue());
        madeNextDimensionThreeGroupLvlMap.put(GroupLevelEnumMadeD.SUB_DETAIL.getValue(), GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue());
        madeNextDimensionThreeGroupLvlMap.put(GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue(), GroupLevelEnumMadeD.MANUFACTURE_OBJECT.getValue());
        madeNextDimensionThreeGroupLvlMap.put(GroupLevelEnumMadeD.MANUFACTURE_OBJECT.getValue(), GroupLevelEnumMadeD.ITEM.getValue());
    }

    // 量纲颗粒度group层级下一层级映射，视角10
    private static Map<String, String> madeNextDimensionTenGroupLvlMap = new HashMap<>(8);
    static {
        madeNextDimensionTenGroupLvlMap.put(GroupLevelEnumMadeD.LV0.getValue(), GroupLevelEnumMadeD.LV1.getValue());
        madeNextDimensionTenGroupLvlMap.put(GroupLevelEnumMadeD.LV1.getValue(), GroupLevelEnumMadeD.DIMENSION.getValue());
        madeNextDimensionTenGroupLvlMap.put(GroupLevelEnumMadeD.DIMENSION.getValue(), GroupLevelEnumMadeD.SUBCATEGORY.getValue());
        madeNextDimensionTenGroupLvlMap.put(GroupLevelEnumMadeD.SUBCATEGORY.getValue(), GroupLevelEnumMadeD.SUB_DETAIL.getValue());
        madeNextDimensionTenGroupLvlMap.put(GroupLevelEnumMadeD.SUB_DETAIL.getValue(), GroupLevelEnumMadeD.SPART.getValue());
        madeNextDimensionTenGroupLvlMap.put(GroupLevelEnumMadeD.SPART.getValue(), GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue());
        madeNextDimensionTenGroupLvlMap.put(GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue(), GroupLevelEnumMadeD.MANUFACTURE_OBJECT.getValue());
        madeNextDimensionTenGroupLvlMap.put(GroupLevelEnumMadeD.MANUFACTURE_OBJECT.getValue(), GroupLevelEnumMadeD.ITEM.getValue());
    }

    // 量纲颗粒度group层级下一层级映射，视角4
    private static Map<String, String> madeNextDimensionFourGroupLvlMap = new HashMap<>(6);
    static {
        madeNextDimensionFourGroupLvlMap.put(GroupLevelEnumMadeD.LV0.getValue(), GroupLevelEnumMadeD.LV1.getValue());
        madeNextDimensionFourGroupLvlMap.put(GroupLevelEnumMadeD.LV1.getValue(), GroupLevelEnumMadeD.LV2.getValue());
        madeNextDimensionFourGroupLvlMap.put(GroupLevelEnumMadeD.LV2.getValue(), GroupLevelEnumMadeD.DIMENSION.getValue());
        madeNextDimensionFourGroupLvlMap.put(GroupLevelEnumMadeD.DIMENSION.getValue(), GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue());
        madeNextDimensionFourGroupLvlMap.put(GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue(), GroupLevelEnumMadeD.MANUFACTURE_OBJECT.getValue());
        madeNextDimensionFourGroupLvlMap.put(GroupLevelEnumMadeD.MANUFACTURE_OBJECT.getValue(), GroupLevelEnumMadeD.ITEM.getValue());
    }

    // 量纲颗粒度group层级下一层级映射，视角5
    private static Map<String, String> madeNextDimensionFiveGroupLvlMap = new HashMap<>(7);
    static {
        madeNextDimensionFiveGroupLvlMap.put(GroupLevelEnumMadeD.LV0.getValue(), GroupLevelEnumMadeD.LV1.getValue());
        madeNextDimensionFiveGroupLvlMap.put(GroupLevelEnumMadeD.LV1.getValue(), GroupLevelEnumMadeD.LV2.getValue());
        madeNextDimensionFiveGroupLvlMap.put(GroupLevelEnumMadeD.LV2.getValue(), GroupLevelEnumMadeD.DIMENSION.getValue());
        madeNextDimensionFiveGroupLvlMap.put(GroupLevelEnumMadeD.DIMENSION.getValue(), GroupLevelEnumMadeD.SUBCATEGORY.getValue());
        madeNextDimensionFiveGroupLvlMap.put(GroupLevelEnumMadeD.SUBCATEGORY.getValue(), GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue());
        madeNextDimensionFiveGroupLvlMap.put(GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue(), GroupLevelEnumMadeD.MANUFACTURE_OBJECT.getValue());
        madeNextDimensionFiveGroupLvlMap.put(GroupLevelEnumMadeD.MANUFACTURE_OBJECT.getValue(), GroupLevelEnumMadeD.ITEM.getValue());
    }

    // 量纲颗粒度group层级下一层级映射，视角6
    private static Map<String, String> madeNextDimensionSixGroupLvlMap = new HashMap<>(8);
    static {
        madeNextDimensionSixGroupLvlMap.put(GroupLevelEnumMadeD.LV0.getValue(), GroupLevelEnumMadeD.LV1.getValue());
        madeNextDimensionSixGroupLvlMap.put(GroupLevelEnumMadeD.LV1.getValue(), GroupLevelEnumMadeD.LV2.getValue());
        madeNextDimensionSixGroupLvlMap.put(GroupLevelEnumMadeD.LV2.getValue(), GroupLevelEnumMadeD.DIMENSION.getValue());
        madeNextDimensionSixGroupLvlMap.put(GroupLevelEnumMadeD.DIMENSION.getValue(), GroupLevelEnumMadeD.SUBCATEGORY.getValue());
        madeNextDimensionSixGroupLvlMap.put(GroupLevelEnumMadeD.SUBCATEGORY.getValue(), GroupLevelEnumMadeD.SUB_DETAIL.getValue());
        madeNextDimensionSixGroupLvlMap.put(GroupLevelEnumMadeD.SUB_DETAIL.getValue(), GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue());
        madeNextDimensionSixGroupLvlMap.put(GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue(), GroupLevelEnumMadeD.MANUFACTURE_OBJECT.getValue());
        madeNextDimensionSixGroupLvlMap.put(GroupLevelEnumMadeD.MANUFACTURE_OBJECT.getValue(), GroupLevelEnumMadeD.ITEM.getValue());
    }

    // 量纲颗粒度group层级下一层级映射，视角11
    private static Map<String, String> madeNextDimensionElevenGroupLvlMap = new HashMap<>(9);
    static {
        madeNextDimensionElevenGroupLvlMap.put(GroupLevelEnumMadeD.LV0.getValue(), GroupLevelEnumMadeD.LV1.getValue());
        madeNextDimensionElevenGroupLvlMap.put(GroupLevelEnumMadeD.LV1.getValue(), GroupLevelEnumMadeD.LV2.getValue());
        madeNextDimensionElevenGroupLvlMap.put(GroupLevelEnumMadeD.LV2.getValue(), GroupLevelEnumMadeD.DIMENSION.getValue());
        madeNextDimensionElevenGroupLvlMap.put(GroupLevelEnumMadeD.DIMENSION.getValue(), GroupLevelEnumMadeD.SUBCATEGORY.getValue());
        madeNextDimensionElevenGroupLvlMap.put(GroupLevelEnumMadeD.SUBCATEGORY.getValue(), GroupLevelEnumMadeD.SUB_DETAIL.getValue());
        madeNextDimensionElevenGroupLvlMap.put(GroupLevelEnumMadeD.SUB_DETAIL.getValue(), GroupLevelEnumMadeD.SPART.getValue());
        madeNextDimensionElevenGroupLvlMap.put(GroupLevelEnumMadeD.SPART.getValue(), GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue());
        madeNextDimensionElevenGroupLvlMap.put(GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue(), GroupLevelEnumMadeD.MANUFACTURE_OBJECT.getValue());
        madeNextDimensionElevenGroupLvlMap.put(GroupLevelEnumMadeD.MANUFACTURE_OBJECT.getValue(), GroupLevelEnumMadeD.ITEM.getValue());
    }

    // 量纲颗粒度group层级下一层级映射，视角7
    private static Map<String, String> madeNextDimensionSevenGroupLvlMap = new HashMap<>(7);
    static {
        madeNextDimensionSevenGroupLvlMap.put(GroupLevelEnumMadeD.LV0.getValue(), GroupLevelEnumMadeD.LV1.getValue());
        madeNextDimensionSevenGroupLvlMap.put(GroupLevelEnumMadeD.LV1.getValue(), GroupLevelEnumMadeD.LV2.getValue());
        madeNextDimensionSevenGroupLvlMap.put(GroupLevelEnumMadeD.LV2.getValue(), GroupLevelEnumMadeD.LV3.getValue());
        madeNextDimensionSevenGroupLvlMap.put(GroupLevelEnumMadeD.LV3.getValue(), GroupLevelEnumMadeD.DIMENSION.getValue());
        madeNextDimensionSevenGroupLvlMap.put(GroupLevelEnumMadeD.DIMENSION.getValue(), GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue());
        madeNextDimensionSevenGroupLvlMap.put(GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue(), GroupLevelEnumMadeD.MANUFACTURE_OBJECT.getValue());
        madeNextDimensionSevenGroupLvlMap.put(GroupLevelEnumMadeD.MANUFACTURE_OBJECT.getValue(), GroupLevelEnumMadeD.ITEM.getValue());
    }

    // 量纲颗粒度group层级下一层级映射，视角8
    private static Map<String, String> madeNextDimensionEightGroupLvlMap = new HashMap<>(8);
    static {
        madeNextDimensionEightGroupLvlMap.put(GroupLevelEnumMadeD.LV0.getValue(), GroupLevelEnumMadeD.LV1.getValue());
        madeNextDimensionEightGroupLvlMap.put(GroupLevelEnumMadeD.LV1.getValue(), GroupLevelEnumMadeD.LV2.getValue());
        madeNextDimensionEightGroupLvlMap.put(GroupLevelEnumMadeD.LV2.getValue(), GroupLevelEnumMadeD.LV3.getValue());
        madeNextDimensionEightGroupLvlMap.put(GroupLevelEnumMadeD.LV3.getValue(), GroupLevelEnumMadeD.DIMENSION.getValue());
        madeNextDimensionEightGroupLvlMap.put(GroupLevelEnumMadeD.DIMENSION.getValue(), GroupLevelEnumMadeD.SUBCATEGORY.getValue());
        madeNextDimensionEightGroupLvlMap.put(GroupLevelEnumMadeD.SUBCATEGORY.getValue(), GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue());
        madeNextDimensionEightGroupLvlMap.put(GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue(), GroupLevelEnumMadeD.MANUFACTURE_OBJECT.getValue());
        madeNextDimensionEightGroupLvlMap.put(GroupLevelEnumMadeD.MANUFACTURE_OBJECT.getValue(), GroupLevelEnumMadeD.ITEM.getValue());
    }

    // 量纲颗粒度group层级下一层级映射，视角9
    private static Map<String, String> madeNextDimensionNineGroupLvlMap = new HashMap<>(9);
    static {
        madeNextDimensionNineGroupLvlMap.put(GroupLevelEnumMadeD.LV0.getValue(), GroupLevelEnumMadeD.LV1.getValue());
        madeNextDimensionNineGroupLvlMap.put(GroupLevelEnumMadeD.LV1.getValue(), GroupLevelEnumMadeD.LV2.getValue());
        madeNextDimensionNineGroupLvlMap.put(GroupLevelEnumMadeD.LV2.getValue(), GroupLevelEnumMadeD.LV3.getValue());
        madeNextDimensionNineGroupLvlMap.put(GroupLevelEnumMadeD.LV3.getValue(), GroupLevelEnumMadeD.DIMENSION.getValue());
        madeNextDimensionNineGroupLvlMap.put(GroupLevelEnumMadeD.DIMENSION.getValue(), GroupLevelEnumMadeD.SUBCATEGORY.getValue());
        madeNextDimensionNineGroupLvlMap.put(GroupLevelEnumMadeD.SUBCATEGORY.getValue(), GroupLevelEnumMadeD.SUB_DETAIL.getValue());
        madeNextDimensionNineGroupLvlMap.put(GroupLevelEnumMadeD.SUB_DETAIL.getValue(), GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue());
        madeNextDimensionNineGroupLvlMap.put(GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue(), GroupLevelEnumMadeD.MANUFACTURE_OBJECT.getValue());
        madeNextDimensionNineGroupLvlMap.put(GroupLevelEnumMadeD.MANUFACTURE_OBJECT.getValue(), GroupLevelEnumMadeD.ITEM.getValue());
    }

    // 量纲颗粒度group层级下一层级映射，视角12
    private static Map<String, String> madeNextDimensionTwelveGroupLvlMap = new HashMap<>(10);
    static {
        madeNextDimensionTwelveGroupLvlMap.put(GroupLevelEnumMadeD.LV0.getValue(), GroupLevelEnumMadeD.LV1.getValue());
        madeNextDimensionTwelveGroupLvlMap.put(GroupLevelEnumMadeD.LV1.getValue(), GroupLevelEnumMadeD.LV2.getValue());
        madeNextDimensionTwelveGroupLvlMap.put(GroupLevelEnumMadeD.LV2.getValue(), GroupLevelEnumMadeD.LV3.getValue());
        madeNextDimensionTwelveGroupLvlMap.put(GroupLevelEnumMadeD.LV3.getValue(), GroupLevelEnumMadeD.DIMENSION.getValue());
        madeNextDimensionTwelveGroupLvlMap.put(GroupLevelEnumMadeD.DIMENSION.getValue(), GroupLevelEnumMadeD.SUBCATEGORY.getValue());
        madeNextDimensionTwelveGroupLvlMap.put(GroupLevelEnumMadeD.SUBCATEGORY.getValue(), GroupLevelEnumMadeD.SUB_DETAIL.getValue());
        madeNextDimensionTwelveGroupLvlMap.put(GroupLevelEnumMadeD.SUB_DETAIL.getValue(), GroupLevelEnumMadeD.SPART.getValue());
        madeNextDimensionTwelveGroupLvlMap.put(GroupLevelEnumMadeD.SPART.getValue(), GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue());
        madeNextDimensionTwelveGroupLvlMap.put(GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue(), GroupLevelEnumMadeD.MANUFACTURE_OBJECT.getValue());
        madeNextDimensionTwelveGroupLvlMap.put(GroupLevelEnumMadeD.MANUFACTURE_OBJECT.getValue(), GroupLevelEnumMadeD.ITEM.getValue());
    }

    // 量纲颗粒度group层级下一层级映射，视角13
    private static Map<String, String> madeNextDimensionThirteenGroupLvlMap = new HashMap<>(11);
    static {
        madeNextDimensionThirteenGroupLvlMap.put(GroupLevelEnumMadeD.LV0.getValue(), GroupLevelEnumMadeD.LV1.getValue());
        madeNextDimensionThirteenGroupLvlMap.put(GroupLevelEnumMadeD.LV1.getValue(), GroupLevelEnumMadeD.LV2.getValue());
        madeNextDimensionThirteenGroupLvlMap.put(GroupLevelEnumMadeD.LV2.getValue(), GroupLevelEnumMadeD.LV3.getValue());
        madeNextDimensionThirteenGroupLvlMap.put(GroupLevelEnumMadeD.LV3.getValue(), GroupLevelEnumMadeD.COA.getValue());
        madeNextDimensionThirteenGroupLvlMap.put(GroupLevelEnumMadeD.COA.getValue(), GroupLevelEnumMadeD.DIMENSION.getValue());
        madeNextDimensionThirteenGroupLvlMap.put(GroupLevelEnumMadeD.DIMENSION.getValue(), GroupLevelEnumMadeD.SUBCATEGORY.getValue());
        madeNextDimensionThirteenGroupLvlMap.put(GroupLevelEnumMadeD.SUBCATEGORY.getValue(), GroupLevelEnumMadeD.SUB_DETAIL.getValue());
        madeNextDimensionThirteenGroupLvlMap.put(GroupLevelEnumMadeD.SUB_DETAIL.getValue(), GroupLevelEnumMadeD.SPART.getValue());
        madeNextDimensionThirteenGroupLvlMap.put(GroupLevelEnumMadeD.SPART.getValue(), GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue());
        madeNextDimensionThirteenGroupLvlMap.put(GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue(), GroupLevelEnumMadeD.MANUFACTURE_OBJECT.getValue());
        madeNextDimensionThirteenGroupLvlMap.put(GroupLevelEnumMadeD.MANUFACTURE_OBJECT.getValue(), GroupLevelEnumMadeD.ITEM.getValue());
    }

    // 量纲颗粒度group层级下一层级映射，视角13
    private static Map<String, String> madeNextDimensionThirteenGroupLvlMapIas = new HashMap<>(11);
    static {
        madeNextDimensionThirteenGroupLvlMapIas.put(GroupLevelEnumMadeD.LV0.getValue(), GroupLevelEnumMadeD.LV1.getValue());
        madeNextDimensionThirteenGroupLvlMapIas.put(GroupLevelEnumMadeD.LV1.getValue(), GroupLevelEnumMadeD.LV2.getValue());
        madeNextDimensionThirteenGroupLvlMapIas.put(GroupLevelEnumMadeD.LV2.getValue(), GroupLevelEnumMadeD.LV3.getValue());
        madeNextDimensionThirteenGroupLvlMapIas.put(GroupLevelEnumMadeD.LV3.getValue(), GroupLevelEnumMadeD.LV4.getValue());
        madeNextDimensionThirteenGroupLvlMapIas.put(GroupLevelEnumMadeD.LV4.getValue(), GroupLevelEnumMadeD.DIMENSION.getValue());
        madeNextDimensionThirteenGroupLvlMapIas.put(GroupLevelEnumMadeD.DIMENSION.getValue(), GroupLevelEnumMadeD.SUBCATEGORY.getValue());
        madeNextDimensionThirteenGroupLvlMapIas.put(GroupLevelEnumMadeD.SUBCATEGORY.getValue(), GroupLevelEnumMadeD.SUB_DETAIL.getValue());
        madeNextDimensionThirteenGroupLvlMapIas.put(GroupLevelEnumMadeD.SUB_DETAIL.getValue(), GroupLevelEnumMadeD.SPART.getValue());
        madeNextDimensionThirteenGroupLvlMapIas.put(GroupLevelEnumMadeD.SPART.getValue(), GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue());
        madeNextDimensionThirteenGroupLvlMapIas.put(GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue(), GroupLevelEnumMadeD.MANUFACTURE_OBJECT.getValue());
        madeNextDimensionThirteenGroupLvlMapIas.put(GroupLevelEnumMadeD.MANUFACTURE_OBJECT.getValue(), GroupLevelEnumMadeD.ITEM.getValue());
    }

    /**
     * 私有构造器
     * 解决sonarQube问题： Add a private constructor to hide the implicit public one.
     */
    private FcstIndexMadeUtil() {
        super();
    }

    /**
     * 获取下一层级的Group层级
     * LV0：ICT、LV1：重量级团队LV1、LV2: 重量级团队LV2、CEG：专项采购认证部、category：品类、item：规格品
     *
     * @param groupLevel 当前Group层级
     * @return String 下一层级的Group层级
     */
    public static String getNextThreeGroupLevel(String groupLevel) {
        return madeNextThreeGroupLevelMap1.get(groupLevel);
    }

    public static String getNextOneGroupLevel(String groupLevel) {
        return madeNextOneGroupLvlMap.get(groupLevel);
    }

    public static String getNextSixGroupLevel(String groupLevel) {
        return madeNextSixGroupLevelMap.get(groupLevel);
    }

    public static String getNextFiveGroupLevelUniversal(String groupLevel) {
        return madeNextFiveGroupLevelMap.get(groupLevel);
    }

    public static String getMadeNextFourGroupLevel(String groupLevel) {
        return madeNextFourGroupLevelMap.get(groupLevel);
    }

    public static String getMadeNextFiveGroupLevelIas(String groupLevel) {
        return madeNextFiveGroupLevelMapIas.get(groupLevel);
    }

    public static String getMadeNextFourGroupLevelProfit(String groupLevel) {
        return madeNextFourGroupLevelMapProfit.get(groupLevel);
    }

    public static String getMadeNextFiveGroupLevel(String groupLevel) {
        return madeNextFiveGroupLevelMapProfit.get(groupLevel);
    }

    public static String getMadeNextOneGroupLevelDimension(String groupLevel) {
        return madeNextDimensionOneGroupLvlMap.get(groupLevel);
    }

    public static String getMadeNextTwoGroupLevelDimension(String groupLevel) {
        return madeNextDimensionTwoGroupLvlMap.get(groupLevel);
    }

    public static String getMadeNextThreeGroupLevelDimension(String groupLevel) {
        return madeNextDimensionThreeGroupLvlMap.get(groupLevel);
    }

    public static String getMadeNextFourGroupLevelDimension(String groupLevel) {
        return madeNextDimensionFourGroupLvlMap.get(groupLevel);
    }

    public static String getMadeNextFiveGroupLevelDimension(String groupLevel) {
        return madeNextDimensionFiveGroupLvlMap.get(groupLevel);
    }

    public static String getMadeNextSixGroupLevelDimension(String groupLevel) {
        return madeNextDimensionSixGroupLvlMap.get(groupLevel);
    }
    public static String getMadeNextSevenGroupLevelDimension(String groupLevel) {
        return madeNextDimensionSevenGroupLvlMap.get(groupLevel);
    }

    public static String getMadeNextEightGroupLevelDimension(String groupLevel) {
        return madeNextDimensionEightGroupLvlMap.get(groupLevel);
    }

    public static String getMadeNextNineGroupLevelDimension(String groupLevel) {
        return madeNextDimensionNineGroupLvlMap.get(groupLevel);
    }

    public static String getMadeNextTenGroupLevelDimension(String groupLevel) {
        return madeNextDimensionTenGroupLvlMap.get(groupLevel);
    }

    public static String getMadeNextElevenGroupLevelDimension(String groupLevel) {
        return madeNextDimensionElevenGroupLvlMap.get(groupLevel);
    }


    public static String getMadeNextTwelveGroupLevelDimension(String groupLevel) {
        return madeNextDimensionTwelveGroupLvlMap.get(groupLevel);
    }

    public static String getMadeNextThirteenGroupLevelDimension(String groupLevel) {
        return madeNextDimensionThirteenGroupLvlMap.get(groupLevel);
    }

    public static String getMadeNextThirteenGroupLevelDimensionIas(String groupLevel) {
        return madeNextDimensionThirteenGroupLvlMapIas.get(groupLevel);
    }

    public static String getMadeNextTwoGroupLevel(String groupLevel) {
        return madeNextTwoGroupLvlMap.get(groupLevel);
    }

    public static String getNextGroupLevelByView(String viewFlag, String level, String granularityType, String industryOrg) {
        String nextGroupLevel = null;
        switch (viewFlag) {
            case "0":
                nextGroupLevel = nextMadeGroupLevelOne(granularityType, level);
                break;
            case "1":
                nextGroupLevel = newMadeGroupLevelTwo(granularityType, level);
                break;
            case "2":
                nextGroupLevel = nextMadeGroupLevelThree(granularityType, IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION, getMadeNextThreeGroupLevelDimension(level), getNextThreeGroupLevel(level));
                break;
            default:
                break;
        }
        String nextGroupLevelByViewsub = getNextGroupLevelByViewsub(viewFlag, level, granularityType, industryOrg);
        if (StringUtils.isNotBlank(nextGroupLevelByViewsub)) {
            nextGroupLevel = nextGroupLevelByViewsub;
        }
        return nextGroupLevel;
    }

    private static String nextMadeGroupLevelThree(String granularityType, IndustryIndexEnum.GRANULARITY_TYPE dimension, String nextThreeGroupLevelDimension, String nextThreeGroupLevel) {
        String nextGroupLevel;
        if (dimension.getValue().equals(granularityType)) {
            nextGroupLevel = nextThreeGroupLevelDimension;
        } else {
            nextGroupLevel = nextThreeGroupLevel;
        }
        return nextGroupLevel;
    }


    private static String getNextGroupLevelByViewsub(String viewFlag, String level,String granularityType,String industryOrg) {
        String nextGroupLevel = null;
        switch (viewFlag) {
            case "3":
                nextGroupLevel = nextGroupLevelWithThreeView(granularityType, getMadeNextFourGroupLevelDimension(level), getMadeNextFourGroupLevel(level), getMadeNextFourGroupLevelProfit(level));
                break;
            case "4":
                nextGroupLevel = nextGroupLevelWithThreeView(granularityType, getMadeNextFiveGroupLevelDimension(level), getNextFiveGroupLevelUniversal(level), getMadeNextFiveGroupLevel(level));
                break;
            case "5":
                nextGroupLevel = nextMadeGroupLevelThree(granularityType, IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION, getMadeNextSixGroupLevelDimension(level), getNextSixGroupLevel(level));
                break;
            default:
                break;
        }
        String nextGroupLevelByViewSub1 = getNextGroupLevelByViewSub1(viewFlag, level,granularityType,industryOrg);
        if (StringUtils.isNotBlank(nextGroupLevelByViewSub1)) {
            nextGroupLevel = nextGroupLevelByViewSub1;
        }
        return nextGroupLevel;
    }

    private static String nextGroupLevelWithThreeView(String granularityType, String nextFourGroupLevelDimension, String nextFourGroupLevel, String nextFourGroupLevelProfit) {
        String nextGroupLevel;
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(granularityType)) {
            nextGroupLevel = nextFourGroupLevelDimension;
        } else {
            nextGroupLevel = nextMadeGroupLevelThree(granularityType, IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL, nextFourGroupLevel, nextFourGroupLevelProfit);
        }
        return nextGroupLevel;
    }

    private static String getNextGroupLevelByViewSub1(String viewFlag, String level,String granularityType,String industryOrg) {
        String nextGroupLevel = null;
        switch (viewFlag) {
            case "6":
                nextGroupLevel = getMadeNextSevenGroupLevelDimension(level);
                break;
            case "7":
                nextGroupLevel = nextMadeGroupLevelThree(granularityType, IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION, getMadeNextEightGroupLevelDimension(level), getMadeNextFiveGroupLevelIas(level));
                break;
            case "8":
                nextGroupLevel = getMadeNextNineGroupLevelDimension(level);
                break;
            case "9":
                nextGroupLevel = getMadeNextTenGroupLevelDimension(level);
                break;
            default:
                break;
        }
        String nextGroupLevelByViewSub2 = getNextGroupLevelByViewSub2(viewFlag, level,industryOrg);
        if (StringUtils.isNotBlank(nextGroupLevelByViewSub2)) {
            nextGroupLevel = nextGroupLevelByViewSub2;
        }
        return nextGroupLevel;
    }

    private static String getNextGroupLevelByViewSub2(String viewFlag, String level,String industryOrg) {
        String nextGroupLevel = null;
        switch (viewFlag) {
            case "10":
                nextGroupLevel = getMadeNextElevenGroupLevelDimension(level);
                break;
            case "11":
                nextGroupLevel = getMadeNextTwelveGroupLevelDimension(level);
                break;
            case "12":
                if (IndustryConst.INDUSTRY_ORG.IAS.getValue().equals(industryOrg)) {
                    nextGroupLevel = getMadeNextThirteenGroupLevelDimensionIas(level);
                } else {
                    nextGroupLevel = getMadeNextThirteenGroupLevelDimension(level);
                }
                break;
            default:
                break;
        }
        return nextGroupLevel;
    }

    public static Map getNextGroupLevel(MonthAnalysisVO paramVO) {
        String nextGroupLevel;
        String nextGroupName = "";
        String granularityType = paramVO.getGranularityType();
        HashMap map = new HashMap();
        IndustryIndexEnum.VIEW_FLAG_D viewFlag = IndustryIndexEnum.getViewFlagDimension(paramVO.getViewFlag());
        // 分视角获取下个层级的groupLevel值
        switch (viewFlag) {
            case VIEW1:
                nextGroupLevel = nextMadeGroupLevelOne(granularityType, paramVO.getGroupLevel());
                break;
            case VIEW2:
                nextGroupLevel = newMadeGroupLevelTwo(granularityType, paramVO.getGroupLevel());
                break;
            case VIEW3:
                nextGroupLevel = nextMadeGroupLevelThree(granularityType, IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION, getMadeNextThreeGroupLevelDimension(paramVO.getGroupLevel()), getNextThreeGroupLevel(paramVO.getGroupLevel()));
                break;
            case VIEW4:
                nextGroupLevel = getMadeViewFourGroupLevelName(granularityType, paramVO);
                break;
            case VIEW5:
                nextGroupLevel = getMadeViewFiveGroupLevelName(granularityType, paramVO);
                break;
            case VIEW6:
                nextGroupLevel = getMadeViewSixGroupLevelName(granularityType, paramVO);
                break;
            default:
                nextGroupLevel = "";
                break;
        }
        String nextGroupLevelSub = getNextGroupLevelSub(paramVO);
        if (StringUtils.isNotBlank(nextGroupLevelSub)) {
            nextGroupLevel = nextGroupLevelSub;
        }
        if (IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(granularityType)) {
            nextGroupName = GroupLevelEnumMadeU.getInstance(nextGroupLevel).getName();
        }
        if (IndustryIndexEnum.GRANULARITY_TYPE.PROFITS.getValue().equals(granularityType)) {
            nextGroupName = GroupLevelEnumMadeP.getInstance(nextGroupLevel).getName();
        }
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(granularityType)) {
            nextGroupName = GroupLevelEnumMadeD.getInstance(nextGroupLevel).getName();
        }
        map.put("nextGroupLevel",nextGroupLevel);
        map.put("nextGroupName",nextGroupName);
        return map;
    }

    private static String newMadeGroupLevelTwo(String granularityType, String groupLevel) {
        return nextMadeGroupLevelThree(granularityType, IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION, getMadeNextTwoGroupLevelDimension(groupLevel), getMadeNextTwoGroupLevel(groupLevel));
    }

    private static String nextMadeGroupLevelOne(String granularityType, String groupLevel) {
        return nextMadeGroupLevelThree(granularityType, IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION, getMadeNextOneGroupLevelDimension(groupLevel), getNextOneGroupLevel(groupLevel));
    }


    public static String getNextGroupLevelSub(MonthAnalysisVO monthAnalysisVO) {
        String nextGroupLevel;
        IndustryIndexEnum.VIEW_FLAG_D viewFlag = IndustryIndexEnum.getViewFlagDimension(monthAnalysisVO.getViewFlag());
        // 分视角获取下个层级的groupLevel值
        switch (viewFlag) {
            case VIEW7:
                nextGroupLevel = getMadeNextSevenGroupLevelDimension(monthAnalysisVO.getGroupLevel());
                break;
            case VIEW8:
                nextGroupLevel = nextMadeGroupLevelThree(monthAnalysisVO.getGranularityType(), IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION, getMadeNextEightGroupLevelDimension(monthAnalysisVO.getGroupLevel()), getMadeNextFiveGroupLevelIas(monthAnalysisVO.getGroupLevel()));
                break;
            case VIEW9:
                nextGroupLevel = getMadeNextNineGroupLevelDimension(monthAnalysisVO.getGroupLevel());
                break;
            case VIEW10:
                nextGroupLevel = getMadeNextTenGroupLevelDimension(monthAnalysisVO.getGroupLevel());
                break;
            case VIEW11:
                nextGroupLevel = getMadeNextElevenGroupLevelDimension(monthAnalysisVO.getGroupLevel());
                break;
            case VIEW12:
                nextGroupLevel = getMadeNextTwelveGroupLevelDimension(monthAnalysisVO.getGroupLevel());
                break;
            case VIEW13:
                if (IndustryConst.INDUSTRY_ORG.IAS.getValue().equals(monthAnalysisVO.getIndustryOrg())) {
                    nextGroupLevel = getMadeNextThirteenGroupLevelDimensionIas(monthAnalysisVO.getGroupLevel());
                } else {
                    nextGroupLevel = getMadeNextThirteenGroupLevelDimension(monthAnalysisVO.getGroupLevel());
                }
                break;
            default:
                nextGroupLevel = "";
                break;
        }
        return nextGroupLevel;
    }

    private static String getMadeViewFourGroupLevelName(String granularityType, MonthAnalysisVO monthAnalysisVO) {
        String nextGroupLevel;
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(granularityType)) {
            nextGroupLevel = getMadeNextFourGroupLevelDimension(monthAnalysisVO.getGroupLevel());
        } else {
            nextGroupLevel = nextMadeGroupLevelThree(granularityType, IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL, getMadeNextFourGroupLevel(monthAnalysisVO.getGroupLevel()), getMadeNextFourGroupLevelProfit(monthAnalysisVO.getGroupLevel()));
        }
        return nextGroupLevel;
    }

    private static String getMadeViewFiveGroupLevelName(String granularityType, MonthAnalysisVO monthAnalysisVO) {
        String nextGroupLevel;
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(granularityType)) {
            nextGroupLevel = getMadeNextFiveGroupLevelDimension(monthAnalysisVO.getGroupLevel());
        } else {
            nextGroupLevel = nextMadeGroupLevelThree(granularityType, IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL, getNextFiveGroupLevelUniversal(monthAnalysisVO.getGroupLevel()), getMadeNextFiveGroupLevel(monthAnalysisVO.getGroupLevel()));
        }
        return nextGroupLevel;
    }

    private static String getMadeViewSixGroupLevelName(String granularityType, MonthAnalysisVO monthAnalysisVO) {
        return nextMadeGroupLevelThree(granularityType, IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION, getMadeNextSixGroupLevelDimension(monthAnalysisVO.getGroupLevel()), getNextSixGroupLevel(monthAnalysisVO.getGroupLevel()));
    }
}
