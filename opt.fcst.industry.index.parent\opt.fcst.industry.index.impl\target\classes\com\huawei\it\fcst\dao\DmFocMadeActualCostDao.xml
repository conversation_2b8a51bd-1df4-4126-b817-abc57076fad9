<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IDmFocMadeActualCostDao">
    <resultMap type="com.huawei.it.fcst.industry.index.vo.month.DmFocActualCostVO" id="resultMap">
        <result property="id" column="id"/>
        <result property="versionId" column="version_id"/>
        <result property="periodYear" column="period_year"/>
        <result property="periodId" column="period_id"/>
        <result property="prodRndTeamCode" column="prod_rnd_team_code"/>
        <result property="prodRndTeamCnName" column="prod_rnd_team_cn_name"/>
        <result property="parentCode" column="parent_code"/>
        <result property="parentCnName" column="parent_cn_name"/>
        <result property="groupCode" column="group_code"/>
        <result property="groupCnName" column="group_cn_name"/>
        <result property="groupLevel" column="group_level"/>
        <result property="actualCostAmt" column="actual_cost_amt"/>
        <result property="parentCode" column="parent_code"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="l1Name" column="l1_name"/>
        <result property="l2Name" column="l2_name"/>
        <result property="dmsCode" column="dms_code"/>
        <result property="dmsCnName" column="dms_cn_name"/>
        <result property="coaCode" column="coa_code"/>
        <result property="coaCnName" column="coa_cn_name"/>
        <result property="dimensionCode" column="dimension_code"/>
        <result property="dimensionCnName" column="dimension_cn_name"/>
        <result property="dimensionSubCategoryCode" column="dimension_subcategory_code"/>
        <result property="dimensionSubCategoryCnName" column="dimension_subcategory_cn_name"/>
        <result property="dimensionSubDetailCode" column="dimension_sub_detail_code"/>
        <result property="dimensionSubDetailCnName" column="dimension_sub_detail_cn_name"/>
        <result property="spartCode" column="spart_code"/>
        <result property="spartCnName" column="spart_cn_name"/>
        <result property="purAmt" column="pur_amt"/>
        <result property="purWeight" column="pur_weight"/>
        <result property="madeAmt" column="made_amt"/>
        <result property="madeWeight" column="made_weight"/>
        <result property="totalAmt" column="total_amt"/>
        <result property="costType" column="cost_type"/>
    </resultMap>

    <select id="findMadeMutilGroupCnNameByCode" resultMap="resultMap">
        <if test='searchParamsVO.granularityType == "U"'>
            SELECT
            prod_rnd_team_code,
            prod_rnd_team_cn_name,
            group_level,group_code,
            group_cn_name,parent_cn_name,
            'M' AS cost_type
            from (
            SELECT
            t1.prod_rnd_team_code,
            t1.prod_rnd_team_cn_name,
            t1.parent_cn_name,
            t1.group_cn_name,
            t1.group_code,
            t1.group_level,
            SUM ( t1.actual_cost_amt ) ss
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_actual_cost_t t1
            <where>
                <include refid="whereBy_default2"/>
            </where>
            group by
            t1.prod_rnd_team_code,
            t1.prod_rnd_team_cn_name,
            t1.group_cn_name,
            t1.parent_cn_name,
            t1.group_code,
            t1.group_level
            order by sum (t1.actual_cost_amt) desc
            ) t
        </if>
        <if test='searchParamsVO.granularityType == "P"'>
            SELECT
            prod_rnd_team_code,
            prod_rnd_team_cn_name,
            l1_name,
            l2_name,
            group_level,group_code,
            group_cn_name,parent_cn_name,
            'M' AS cost_type
            from (
            select t1.prod_rnd_team_code,
            t1.prod_rnd_team_cn_name,
            t1.l1_name,
            t1.l2_name,
            t1.parent_cn_name,
            t1.group_cn_name,
            t1.group_code,
            t1.group_level,
            sum(t1.actual_cost_amt) ss
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_pft_actual_cost_t t1
            <where>
                <include refid="whereBy_default2"/>
            </where>
            group by
            t1.prod_rnd_team_code,
            t1.prod_rnd_team_cn_name,
            t1.l1_name,
            t1.l2_name,t1.parent_cn_name,
            t1.group_cn_name,t1.group_code,t1.group_level
            order by sum (t1.actual_cost_amt) desc
            ) t
        </if>
        <if test='searchParamsVO.granularityType == "D"'>
            SELECT prod_rnd_team_code,prod_rnd_team_cn_name,dms_code,dms_cn_name,
            <if test='searchParamsVO.industryOrg == "ENERGY"'>
                coa_code, coa_cn_name,
            </if>
            dimension_code,dimension_cn_name,dimension_subcategory_code,dimension_subcategory_cn_name,
            dimension_sub_detail_code,dimension_sub_detail_cn_name,spart_code,spart_cn_name,
            group_level,group_code,group_cn_name,parent_cn_name,
            'M' AS cost_type
            from (
            select t1.prod_rnd_team_code,t1.prod_rnd_team_cn_name,t1.dms_code,t1.dms_cn_name,
            <if test='searchParamsVO.industryOrg == "ENERGY"'>
                t1.coa_code, t1.coa_cn_name,
            </if>
            t1.dimension_code,t1.dimension_cn_name,t1.dimension_subcategory_code,t1.dimension_subcategory_cn_name,
            t1.dimension_sub_detail_code,t1.dimension_sub_detail_cn_name,t1.spart_code,t1.spart_cn_name,
            t1.parent_cn_name,t1.group_cn_name,t1.group_code,t1.group_level,
            sum(t1.actual_cost_amt) ss
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_dms_actual_cost_t t1
            <where>
                <include refid="whereBy_default2"/>
            </where>
            group by t1.prod_rnd_team_code,t1.prod_rnd_team_cn_name, t1.dms_code,t1.dms_cn_name,
            <if test='searchParamsVO.industryOrg == "ENERGY"'>
                t1.coa_code, t1.coa_cn_name,
            </if>
            t1.dimension_code,t1.dimension_cn_name,
            t1.dimension_subcategory_code,t1.dimension_subcategory_cn_name,
            t1.dimension_sub_detail_code,t1.dimension_sub_detail_cn_name,
            t1.spart_code,t1.spart_cn_name,
            t1.parent_cn_name,t1.group_cn_name,t1.group_code,t1.group_level
            order by sum (t1.actual_cost_amt) desc
            ) t
        </if>
    </select>

    <select id="findMadeGroupCnNameByCode" resultMap="resultMap">
        SELECT group_level,group_code,group_cn_name
        from (
        select group_cn_name,group_code,group_level, sum(actual_cost_amt) ss
        <if test='searchParamsVO.granularityType == "U"'>
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_actual_cost_t
        </if>
        <if test='searchParamsVO.granularityType == "P"'>
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_pft_actual_cost_t
        </if>
        <if test='searchParamsVO.granularityType == "D"'>
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_dms_actual_cost_t
        </if>
        <where>
            <include refid="whereBy_default"/>
        </where>
        group by group_cn_name,group_code,group_level
        order by sum (actual_cost_amt) desc
        <if test='searchParamsVO.pageSize != 0'>
            LIMIT #{searchParamsVO.curPage}, #{searchParamsVO.pageSize}
        </if>
        ) t
    </select>

    <select id="findMadeGroupCodeOrder" resultType="java.lang.String">
        select group_concat(group_code ORDER BY actual_cost_amt desc) groupCodeOrder
        from (
        select group_code, sum(actual_cost_amt) actual_cost_amt
        <if test='searchParamsVO.granularityType == "U"'>
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_actual_cost_t
        </if>
        <if test='searchParamsVO.granularityType == "P"'>
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_pft_actual_cost_t
        </if>
        <if test='searchParamsVO.granularityType == "D"'>
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_dms_actual_cost_t
        </if>
        <where>
            <include refid="whereBy_default"/>
        </where>
        group by group_code
        order by sum (actual_cost_amt) desc
        <if test='searchParamsVO.pageSize != 0'>
            LIMIT #{searchParamsVO.curPage}, #{searchParamsVO.pageSize}
        </if>
        ) t
    </select>

    <select id="findMadeActualCostAmtList" resultMap="resultMap">
        <if test='searchParamsVO.granularityType == "U"'>
            select t1.prod_rnd_team_code,t1.prod_rnd_team_cn_name,t1.period_id,t1.parent_code,
            t1.parent_cn_name,t1.group_level,t1.group_cn_name,
            sum (t1.actual_cost_amt) / 10000 as actual_cost_amt,
            'M' AS cost_type,
            t1.group_code
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_actual_cost_t t1
        </if>
        <if test='searchParamsVO.granularityType == "P"'>
            select t1.prod_rnd_team_code,t1.prod_rnd_team_cn_name,t1.l1_name,t1.l2_name,
            t1.period_id,t1.parent_code, t1.parent_cn_name,
            t1.group_level,t1.group_cn_name,
            sum (t1.actual_cost_amt) / 10000 as actual_cost_amt,
            'M' AS cost_type,
            t1.group_code
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_pft_actual_cost_t t1
        </if>
        <if test='searchParamsVO.granularityType == "D"'>
            select t1.prod_rnd_team_code,t1.prod_rnd_team_cn_name,t1.dms_code,t1.dms_cn_name,
            <if test='searchParamsVO.industryOrg == "ENERGY"'>
                t1.coa_code, t1.coa_cn_name,
            </if>
           t1.dimension_code,t1.dimension_cn_name,
            t1.dimension_subcategory_code,t1.dimension_subcategory_cn_name,
            t1.dimension_sub_detail_code,t1.dimension_sub_detail_cn_name,t1.spart_code,t1.spart_cn_name,
            t1.period_id,t1.parent_code, t1.parent_cn_name,
            t1.group_level,t1.group_cn_name,
            sum (t1.actual_cost_amt) / 10000 as actual_cost_amt,
            'M' AS cost_type,t1.group_code
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_dms_actual_cost_t t1
        </if>
        <where>
            <include refid="searchFields"/>
        </where>
        <if test='searchParamsVO.granularityType == "U"'>
            group by t1.prod_rnd_team_code,t1.prod_rnd_team_cn_name,t1.period_id, t1.group_level,
            t1.parent_code,t1.parent_cn_name,t1.group_code,t1.group_cn_name
        </if>
        <if test='searchParamsVO.granularityType == "P"'>
            group by t1.prod_rnd_team_code,t1.prod_rnd_team_cn_name,t1.l1_name,t1.l2_name,t1.period_id,
            t1.group_level,t1.parent_code,t1.parent_cn_name,t1.group_code,t1.group_cn_name
        </if>
        <if test='searchParamsVO.granularityType == "D"'>
            group by t1.prod_rnd_team_code,t1.prod_rnd_team_cn_name,t1.dms_code,t1.dms_cn_name,
            <if test='searchParamsVO.industryOrg == "ENERGY"'>
                t1.coa_code, t1.coa_cn_name,
            </if>
            t1.dimension_code,t1.dimension_cn_name,
            t1.dimension_subcategory_code,t1.dimension_subcategory_cn_name,
            t1.dimension_sub_detail_code,t1.dimension_sub_detail_cn_name, t1.spart_code,t1.spart_cn_name,
            t1.period_id, t1.group_level,t1.parent_code,t1.parent_cn_name,t1.group_code,t1.group_cn_name
        </if>
        ORDER BY t1.period_id,t1.parent_cn_name,
        locate(t1.group_code, #{searchParamsVO.groupCodeOrder})
    </select>

    <select id="findHeapMapExpData" resultMap="resultMap">
        <if test='searchParamsVO.granularityType == "U"'>
            SELECT distinct
            t1.prod_rnd_team_code,t1.prod_rnd_team_cn_name,
            t1.parent_code,t1.parent_cn_name,
            t1.period_id,t1.group_code,t1.group_cn_name,
            ROUND(t1.actual_cost_amt / 10000, 2) AS actual_cost_amt,
            'M' AS cost_type
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_actual_cost_t t1
        </if>
        <if test='searchParamsVO.granularityType == "P"'>
            SELECT distinct
            t1.prod_rnd_team_code,t1.prod_rnd_team_cn_name,t1.l1_name,t1.l2_name,
            t1.parent_code, t1.parent_cn_name,
            t1.period_id,t1.group_code,t1.group_cn_name,
            ROUND(t1.actual_cost_amt / 10000, 2) AS actual_cost_amt,
            'M' AS cost_type
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_pft_actual_cost_t t1
        </if>
        <if test='searchParamsVO.granularityType == "D"'>
            SELECT distinct
            t1.prod_rnd_team_code,t1.prod_rnd_team_cn_name, t1.dms_code,t1.dms_cn_name,
            <if test='searchParamsVO.industryOrg == "ENERGY"'>
                t1.coa_code, t1.coa_cn_name,
            </if>
            t1.dimension_code,t1.dimension_cn_name,t1.dimension_subcategory_code,t1.dimension_subcategory_cn_name,
            t1.dimension_sub_detail_code,t1.dimension_sub_detail_cn_name,
            t1.spart_code,t1.spart_cn_name,
            t1.parent_code, t1.parent_cn_name,
            t1.period_id,t1.group_code,
            CASE t1.group_level
            WHEN 'DIMENSION' THEN t1.group_code || ' ' || t1.group_cn_name
            WHEN 'SUBCATEGORY' THEN t1.group_code || ' ' || t1.group_cn_name
            WHEN 'SUB_DETAIL' THEN t1.group_code || ' ' || t1.group_cn_name
            ELSE t1.group_cn_name END AS group_cn_name,
            ROUND(t1.actual_cost_amt / 10000, 2) AS actual_cost_amt,
            'M' AS cost_type
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_dms_actual_cost_t t1
        </if>
        WHERE t1.del_flag = 'N'
        <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
            and t1.lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode}
        </if>
        <if test='searchParamsVO.shippingObjectCode != null and searchParamsVO.shippingObjectCode != ""'>
            and t1.shipping_object_code = #{searchParamsVO.shippingObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.manufactureObjectCode != null and searchParamsVO.manufactureObjectCode != ""'>
            and t1.manufacture_object_code = #{searchParamsVO.manufactureObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.coaCodeList != null and searchParamsVO.coaCodeList.size() > 0'>
            <foreach collection='searchParamsVO.coaCodeList' item="code" open="AND t1.coa_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionCodeList != null and searchParamsVO.dimensionCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionCodeList' item="code" open="AND t1.dimension_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubcategoryCodeList != null and searchParamsVO.dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubcategoryCodeList' item="code" open="AND t1.dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubDetailCodeList != null and searchParamsVO.dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubDetailCodeList' item="code" open="AND t1.dimension_sub_detail_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.spartCodeList != null and searchParamsVO.spartCodeList.size() > 0'>
            <foreach collection='searchParamsVO.spartCodeList' item="code" open="AND t1.spart_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.prodRndTeamCodeList != null and searchParamsVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='searchParamsVO.prodRndTeamCodeList' item="code" open="AND t1.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l1NameList != null and searchParamsVO.l1NameList.size() > 0'>
            <foreach collection='searchParamsVO.l1NameList' item="code" open="AND t1.l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l2NameList != null and searchParamsVO.l2NameList.size() > 0'>
            <foreach collection='searchParamsVO.l2NameList' item="code" open="AND t1.l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.parentCodeList != null and searchParamsVO.parentCodeList.size() > 0'>
            <foreach collection='searchParamsVO.parentCodeList' item="code" open="AND t1.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
            AND t1.caliber_Flag = #{searchParamsVO.caliberFlag}
        </if>
        <if test='searchParamsVO.versionId != null'>
            AND t1.version_id = #{searchParamsVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='searchParamsVO.periodStartTime != null and searchParamsVO.periodStartTime != ""'>
            AND t1.period_id <![CDATA[ >= ]]> #{searchParamsVO.periodStartTime}
        </if>
        <if test='searchParamsVO.periodEndTime != null and searchParamsVO.periodEndTime != ""'>
            AND t1.period_id <![CDATA[ <= ]]> #{searchParamsVO.periodEndTime}
        </if>
        <if test='searchParamsVO.subGroupCodeList != null and searchParamsVO.subGroupCodeList.size() > 0'>
            <foreach collection='searchParamsVO.subGroupCodeList' item="code" open="AND t1.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupLevel =="LV1" and searchParamsVO.lv1DimensionSet != null and searchParamsVO.lv1DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv1DimensionSet' item="code" open="AND t1.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupLevel =="LV2" and searchParamsVO.lv2DimensionSet != null and searchParamsVO.lv2DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv2DimensionSet' item="code" open="AND t1.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
            and t1.oversea_flag = #{searchParamsVO.overseaFlag}
        </if>
        AND t1.view_flag = #{searchParamsVO.viewFlag}
        <if test='searchParamsVO.groupLevel != null and searchParamsVO.groupLevel != ""'>
            AND t1.group_level = #{searchParamsVO.groupLevel}
        </if>
        ORDER BY t1.period_id
    </select>

    <sql id="whereBy_default2">
        t1.del_flag='N'
        <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
            and t1.lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode}
        </if>
        <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
            AND t1.caliber_Flag = #{searchParamsVO.caliberFlag}
        </if>
        <if test='searchParamsVO.versionId != null'>
            AND t1.version_id = #{searchParamsVO.versionId}
        </if>
        <if test='searchParamsVO.coaCodeList != null and searchParamsVO.coaCodeList.size() > 0'>
            <foreach collection='searchParamsVO.coaCodeList' item="code" open="AND t1.coa_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionCodeList != null and searchParamsVO.dimensionCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionCodeList' item="code" open="AND t1.dimension_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubcategoryCodeList != null and searchParamsVO.dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubcategoryCodeList' item="code" open="AND t1.dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubDetailCodeList != null and searchParamsVO.dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubDetailCodeList' item="code" open="AND t1.dimension_sub_detail_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.spartCodeList != null and searchParamsVO.spartCodeList.size() > 0'>
            <foreach collection='searchParamsVO.spartCodeList' item="code" open="AND t1.spart_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.prodRndTeamCodeList != null and searchParamsVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='searchParamsVO.prodRndTeamCodeList' item="code" open="AND t1.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l1NameList != null and searchParamsVO.l1NameList.size() > 0'>
            <foreach collection='searchParamsVO.l1NameList' item="code" open="AND t1.l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l2NameList != null and searchParamsVO.l2NameList.size() > 0'>
            <foreach collection='searchParamsVO.l2NameList' item="code" open="AND t1.l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.parentCodeList != null and searchParamsVO.parentCodeList.size() > 0'>
            <foreach collection='searchParamsVO.parentCodeList' item="code" open="AND t1.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.periodStartTime != null and searchParamsVO.periodStartTime != ""'>
            AND t1.period_id <![CDATA[ >= ]]> #{searchParamsVO.periodStartTime}
        </if>
        <if test='searchParamsVO.periodEndTime != null and searchParamsVO.periodEndTime != ""'>
            AND t1.period_id <![CDATA[ <= ]]> #{searchParamsVO.periodEndTime}
        </if>
        <if test='searchParamsVO.groupCodeOrder != null and searchParamsVO.groupCodeOrder != ""'>
            <foreach collection="searchParamsVO.groupCodeOrder.split(',')" item ="item" open="AND t1.group_code IN (" close=")"  index="index" separator=",">
                #{item}
            </foreach>
        </if>
        <if test='searchParamsVO.subGroupCodeList != null and searchParamsVO.subGroupCodeList.size() > 0'>
            <foreach collection='searchParamsVO.subGroupCodeList' item="code" open="AND t1.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupLevel =="LV1" and searchParamsVO.lv1DimensionSet != null and searchParamsVO.lv1DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv1DimensionSet' item="code" open="AND t1.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupLevel =="LV2" and searchParamsVO.lv2DimensionSet != null and searchParamsVO.lv2DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv2DimensionSet' item="code" open="AND t1.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
            and t1.oversea_flag = #{searchParamsVO.overseaFlag}
        </if>
        AND t1.view_flag = #{searchParamsVO.viewFlag}
        <if test='searchParamsVO.groupLevel != null'>
            AND t1.group_level = #{searchParamsVO.groupLevel}
        </if>
    </sql>

    <sql id="whereBy_default">
        del_flag='N'
        <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
            and lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode}
        </if>
        <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
            AND caliber_Flag = #{searchParamsVO.caliberFlag}
        </if>
        <if test='searchParamsVO.versionId != null'>
            AND version_id = #{searchParamsVO.versionId}
        </if>
        <if test='searchParamsVO.shippingObjectCode != null and searchParamsVO.shippingObjectCode != ""'>
            and shipping_object_code = #{searchParamsVO.shippingObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.manufactureObjectCode != null and searchParamsVO.manufactureObjectCode != ""'>
            and manufacture_object_code = #{searchParamsVO.manufactureObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.coaCodeList != null and searchParamsVO.coaCodeList.size() > 0'>
            <foreach collection='searchParamsVO.coaCodeList' item="code" open="AND coa_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionCodeList != null and searchParamsVO.dimensionCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionCodeList' item="code" open="AND dimension_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubcategoryCodeList != null and searchParamsVO.dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubcategoryCodeList' item="code" open="AND dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubDetailCodeList != null and searchParamsVO.dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubDetailCodeList' item="code" open="AND dimension_sub_detail_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.spartCodeList != null and searchParamsVO.spartCodeList.size() > 0'>
            <foreach collection='searchParamsVO.spartCodeList' item="code" open="AND spart_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.prodRndTeamCodeList != null and searchParamsVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='searchParamsVO.prodRndTeamCodeList' item="code" open="AND prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l1NameList != null and searchParamsVO.l1NameList.size() > 0'>
            <foreach collection='searchParamsVO.l1NameList' item="code" open="AND l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l2NameList != null and searchParamsVO.l2NameList.size() > 0'>
            <foreach collection='searchParamsVO.l2NameList' item="code" open="AND l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.parentCodeList != null and searchParamsVO.parentCodeList.size() > 0'>
            <foreach collection='searchParamsVO.parentCodeList' item="code" open="AND parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.periodStartTime != null and searchParamsVO.periodStartTime != ""'>
            AND period_id <![CDATA[ >= ]]> #{searchParamsVO.periodStartTime}
        </if>
        <if test='searchParamsVO.periodEndTime != null and searchParamsVO.periodEndTime != ""'>
            AND period_id <![CDATA[ <= ]]> #{searchParamsVO.periodEndTime}
        </if>
        <if test='searchParamsVO.groupCodeOrder != null and searchParamsVO.groupCodeOrder != ""'>
            <foreach collection="searchParamsVO.groupCodeOrder.split(',')" item ="item" open="AND group_code IN (" close=")"  index="index" separator=",">
                #{item}
            </foreach>
        </if>
        <if test='searchParamsVO.subGroupCodeList != null and searchParamsVO.subGroupCodeList.size() > 0'>
            <foreach collection='searchParamsVO.subGroupCodeList' item="code" open="AND group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupLevel =="LV1" and searchParamsVO.lv1DimensionSet != null and searchParamsVO.lv1DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv1DimensionSet' item="code" open="AND prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupLevel =="LV2" and searchParamsVO.lv2DimensionSet != null and searchParamsVO.lv2DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv2DimensionSet' item="code" open="AND prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
            and oversea_flag = #{searchParamsVO.overseaFlag}
        </if>
        AND view_flag = #{searchParamsVO.viewFlag}
        <if test='searchParamsVO.groupLevel != null'>
            AND group_level = #{searchParamsVO.groupLevel}
        </if>
    </sql>

    <sql id="searchFields">
        t1.del_flag='N'
        <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
            and t1.lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode}
        </if>
        <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
            AND t1.caliber_Flag = #{searchParamsVO.caliberFlag}
        </if>
        <if test='searchParamsVO.versionId != null'>
            AND t1.version_id = #{searchParamsVO.versionId}
        </if>
        <if test='searchParamsVO.shippingObjectCode != null and searchParamsVO.shippingObjectCode != ""'>
            and t1.shipping_object_code = #{searchParamsVO.shippingObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.manufactureObjectCode != null and searchParamsVO.manufactureObjectCode != ""'>
            and t1.manufacture_object_code = #{searchParamsVO.manufactureObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.coaCodeList != null and searchParamsVO.coaCodeList.size() > 0'>
            <foreach collection='searchParamsVO.coaCodeList' item="code" open="AND t1.coa_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionCodeList != null and searchParamsVO.dimensionCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionCodeList' item="code" open="AND t1.dimension_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubcategoryCodeList != null and searchParamsVO.dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubcategoryCodeList' item="code" open="AND t1.dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubDetailCodeList != null and searchParamsVO.dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubDetailCodeList' item="code" open="AND t1.dimension_sub_detail_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.spartCodeList != null and searchParamsVO.spartCodeList.size() > 0'>
            <foreach collection='searchParamsVO.spartCodeList' item="code" open="AND t1.spart_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.prodRndTeamCodeList != null and searchParamsVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='searchParamsVO.prodRndTeamCodeList' item="code" open="AND t1.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l1NameList != null and searchParamsVO.l1NameList.size() > 0'>
            <foreach collection='searchParamsVO.l1NameList' item="code" open="AND t1.l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l2NameList != null and searchParamsVO.l2NameList.size() > 0'>
            <foreach collection='searchParamsVO.l2NameList' item="code" open="AND t1.l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.parentCodeList != null and searchParamsVO.parentCodeList.size() > 0'>
            <foreach collection='searchParamsVO.parentCodeList' item="code" open="AND t1.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.periodStartTime != null and searchParamsVO.periodStartTime != ""'>
            AND t1.period_id <![CDATA[ >= ]]> #{searchParamsVO.periodStartTime}
        </if>
        <if test='searchParamsVO.periodEndTime != null and searchParamsVO.periodEndTime != ""'>
            AND t1.period_id <![CDATA[ <= ]]> #{searchParamsVO.periodEndTime}
        </if>
        <if test='searchParamsVO.groupCodeOrder != null and searchParamsVO.groupCodeOrder != ""'>
            <foreach collection="searchParamsVO.groupCodeOrder.split(',')" item ="item" open="AND t1.group_code IN (" close=")"  index="index" separator=",">
                #{item}
            </foreach>
        </if>
        <if test='searchParamsVO.subGroupCodeList != null and searchParamsVO.subGroupCodeList.size() > 0'>
            <foreach collection='searchParamsVO.subGroupCodeList' item="code" open="AND t1.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupLevel =="LV1" and searchParamsVO.lv1DimensionSet != null and searchParamsVO.lv1DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv1DimensionSet' item="code" open="AND t1.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupLevel =="LV2" and searchParamsVO.lv2DimensionSet != null and searchParamsVO.lv2DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv2DimensionSet' item="code" open="AND t1.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
            and t1.oversea_flag = #{searchParamsVO.overseaFlag}
        </if>
        AND t1.view_flag = #{searchParamsVO.viewFlag}
        <if test='searchParamsVO.groupLevel != null'>
            AND t1.group_level = #{searchParamsVO.groupLevel}
        </if>
    </sql>

</mapper>
