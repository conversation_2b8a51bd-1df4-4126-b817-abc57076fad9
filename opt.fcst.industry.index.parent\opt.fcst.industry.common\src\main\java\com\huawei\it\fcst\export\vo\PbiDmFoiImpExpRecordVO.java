/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.export.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * DmFoiImpExpRecordVO Class
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PbiDmFoiImpExpRecordVO implements Serializable {
    private Long id;

    // 文件名
    private String fileName;

    // 文件大小
    private String fileSize;

    // 页面模块
    private String pageModule;

    // 状态: 保存 Save，或者修改
    private String status;

    // 异常反馈
    private String exceptionFeedback;

    // 记录条数
    private Long recordNum;

    private Date creationDate;

    private Date endDate;

    private String createdBy;

    private String userId;

    private String lastUpdatedBy;

    private Date lastUpdateDate;

    private String delFlag;

    private String periodId;

    private String fileSourceKey;

    private String fileErrorKey;

    private String optType;

    private String moduleType;

    // 导入导出状态（OK：成功、FAIL：失败）
    private String recSts;

    // 文件类型，例如:xlsx
    private String fileType;

}
