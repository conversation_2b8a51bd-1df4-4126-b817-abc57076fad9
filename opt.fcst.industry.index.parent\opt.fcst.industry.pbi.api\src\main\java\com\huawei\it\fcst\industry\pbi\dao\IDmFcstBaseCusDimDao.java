/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.dao;

import com.huawei.it.fcst.industry.pbi.vo.drop.BaseCusDimVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstBaseCusDimVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * IDmFocViewInfoDao
 *
 * <AUTHOR>
 * @since 2023/3/14
 */
public interface IDmFcstBaseCusDimDao {

    List<DmFcstBaseCusDimVO> baseCusDimStatus(BaseCusDimVO baseCusDimVO);

    List<DmFcstBaseCusDimVO> getBaseCusDimInfoList(BaseCusDimVO baseCusDimVO);

    int createDmFcstCusDimDTO(DmFcstBaseCusDimVO dmFcstBaseCusDimVO);

    Long getBaseCusDimKey(@Param("costType") String costType);

    /**
     * 获取需要执行的任务清单
     *
     * @return 待执行任务列表
     */
    List<DmFcstBaseCusDimVO> getNeedTaskList();

    /**
     * 更新任务状态
     *
     * @param dmFcstBaseCusDimVO 组合对象
     * @return update 结果
     */
    int updateTaskStatus(DmFcstBaseCusDimVO dmFcstBaseCusDimVO);

    /**
     * 任务触发批量更新任务状态
     *
     * @param list 任务列表
     * @return 更新条数
     */
    int updateStatusFlag(List<DmFcstBaseCusDimVO> list);

    /**
     * 任务触发批量更新任务状态
     *
     * @return 更新条数
     */
    List<DmFcstBaseCusDimVO> getExceptionTaskList();
}
