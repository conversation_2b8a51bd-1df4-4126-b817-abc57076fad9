/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.service.standard;

import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.standard.StandardAnalysisVO;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;

/**
 * StandardService Class
 *
 * <AUTHOR>
 * @since 2024/9/2
 */
@Path("/standard")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface IStandardService {

    @Path("/currentAmpCost")
    @POST
    ResultDataVO currentAmpCost(StandardAnalysisVO standardAnalysisVO);

    @Path("/multiAmpCostChart")
    @POST
    ResultDataVO multiAmpCostChart(StandardAnalysisVO standardAnalysisVO);

    @Path("/industryAmpCostList")
    @POST
    ResultDataVO industryAmpCostList(StandardAnalysisVO standardAnalysisVO);

    @Path("/distributeAmpCostChart")
    @POST
    ResultDataVO distributeAmpCostChart(StandardAnalysisVO standardAnalysisVO);

    /**
     * 查询月度累计成本涨跌图
     *
     * @param standardAnalysisVO 参数
     * @return 结果
     */
    @Path("/monthlyAccChart")
    @POST
    ResultDataVO monthAccCostAmpList(StandardAnalysisVO standardAnalysisVO);

    @Path("/exportDetail")
    @POST
    ResultDataVO exportDetail(StandardAnalysisVO standardAnalysisVO, @Context HttpServletResponse response) throws Exception;

}
