/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.impl.template;

import com.huawei.it.fcst.export.IExcelTemplateBeanManager;
import com.huawei.it.fcst.export.vo.ExcelTemplateBeanManager;
import com.huawei.it.fcst.export.vo.SheetBeanMetaVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import lombok.Getter;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * 年度导出模板
 *
 * <AUTHOR>
 * @since 2024/06
 */
@Getter
public enum AnnualTemplateEnum implements IExcelTemplateBeanManager {
    PRICE_ANNUAL_01("01", "AnnualPriceExportTemplate1", "年度分析", "年度分析-明细数据") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            ExcelTemplateBeanManager excelTemplateBeanManager = new ExcelTemplateBeanManager();
            List<SheetBeanMetaVO> annualList = new ArrayList<>();
            annualList.add(new SheetBeanMetaVO(PRICE_ANNUAL_01.templateName, 0, "AnnualCurrentExportProvider", "成本涨跌图", Boolean.FALSE));
            annualList.add(new SheetBeanMetaVO(PRICE_ANNUAL_01.templateName, 1, "AnnualChildExportProvider", "成本涨跌图多子项", Boolean.FALSE));
            annualList.add(new SheetBeanMetaVO(PRICE_ANNUAL_01.templateName, 2, "AnnualViewExportProvider", "成本涨跌幅一览表", Boolean.FALSE));
            excelTemplateBeanManager.setSheetBeans(annualList);
            excelTemplateBeanManager.setTemplateName(PRICE_ANNUAL_01.templateName);
            excelTemplateBeanManager.setModuleType(PRICE_ANNUAL_01.moduleType);
            excelTemplateBeanManager.setDesc(PRICE_ANNUAL_01.desc);
            return excelTemplateBeanManager;
        }
    },

    PRICE_ANNUAL_02("02", "AnnualPriceExportTemplate2", "年度分析", "年度分析-明细数据") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            ExcelTemplateBeanManager excelTemplateBeanManager = new ExcelTemplateBeanManager();
            List<SheetBeanMetaVO> annualList = new ArrayList<>();
            annualList.add(new SheetBeanMetaVO(PRICE_ANNUAL_02.templateName, 0, "AnnualCurrentExportProvider", "成本涨跌图", Boolean.FALSE));
            excelTemplateBeanManager.setSheetBeans(annualList);
            excelTemplateBeanManager.setTemplateName(PRICE_ANNUAL_02.templateName);
            excelTemplateBeanManager.setModuleType(PRICE_ANNUAL_02.moduleType);
            excelTemplateBeanManager.setDesc(PRICE_ANNUAL_02.desc);
            return excelTemplateBeanManager;
        }
    },
    PRICE_ANNUAL_03("03", "AnnualPriceExportTemplate3", "年度分析", "年度分析-明细数据") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            ExcelTemplateBeanManager excelTemplateBeanManager = new ExcelTemplateBeanManager();
            List<SheetBeanMetaVO> annualList = new ArrayList<>();
            annualList.add(new SheetBeanMetaVO(PRICE_ANNUAL_03.templateName, 0, "AnnualCurrentExportProvider", "成本涨跌图", Boolean.FALSE));
            annualList.add(new SheetBeanMetaVO(PRICE_ANNUAL_03.templateName, 1, "AnnualChildExportProvider", "成本涨跌图多子项", Boolean.FALSE));
            annualList.add(new SheetBeanMetaVO(PRICE_ANNUAL_03.templateName, 2, "AnnualViewExportProvider", "成本涨跌幅一览表", Boolean.FALSE));
            excelTemplateBeanManager.setSheetBeans(annualList);
            excelTemplateBeanManager.setTemplateName(PRICE_ANNUAL_03.templateName);
            excelTemplateBeanManager.setModuleType(PRICE_ANNUAL_03.moduleType);
            excelTemplateBeanManager.setDesc(PRICE_ANNUAL_03.desc);
            return excelTemplateBeanManager;
        }
    },
    PRICE_ANNUAL_04("04", "AnnualPriceExportTemplate4", "年度分析", "年度分析-明细数据") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            ExcelTemplateBeanManager excelTemplateBeanManager = new ExcelTemplateBeanManager();
            List<SheetBeanMetaVO> annualList = new ArrayList<>();
            annualList.add(new SheetBeanMetaVO(PRICE_ANNUAL_04.templateName, 0, "AnnualCurrentExportProvider", "成本涨跌图", Boolean.FALSE));
            annualList.add(new SheetBeanMetaVO(PRICE_ANNUAL_04.templateName, 1, "AnnualChildExportProvider", "成本涨跌图多子项", Boolean.FALSE));
            annualList.add(new SheetBeanMetaVO(PRICE_ANNUAL_04.templateName, 2, "AnnualViewExportProvider", "成本涨跌幅一览表", Boolean.FALSE));
            excelTemplateBeanManager.setSheetBeans(annualList);
            excelTemplateBeanManager.setTemplateName(PRICE_ANNUAL_04.templateName);
            excelTemplateBeanManager.setModuleType(PRICE_ANNUAL_04.moduleType);
            excelTemplateBeanManager.setDesc(PRICE_ANNUAL_04.desc);
            return excelTemplateBeanManager;
        }
    },
    PRICE_ANNUAL_05("05", "AnnualPriceExportTemplate5", "年度分析", "年度分析-明细数据") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            ExcelTemplateBeanManager excelTemplateBeanManager = new ExcelTemplateBeanManager();
            List<SheetBeanMetaVO> annualList = new ArrayList<>();
            annualList.add(new SheetBeanMetaVO(PRICE_ANNUAL_05.templateName, 0, "AnnualCurrentExportProvider", "成本涨跌图", Boolean.FALSE));
            annualList.add(new SheetBeanMetaVO(PRICE_ANNUAL_05.templateName, 1, "AnnualChildExportProvider", "成本涨跌图多子项", Boolean.FALSE));
            annualList.add(new SheetBeanMetaVO(PRICE_ANNUAL_05.templateName, 2, "AnnualViewExportProvider", "成本涨跌幅一览表", Boolean.FALSE));
            excelTemplateBeanManager.setSheetBeans(annualList);
            excelTemplateBeanManager.setTemplateName(PRICE_ANNUAL_05.templateName);
            excelTemplateBeanManager.setModuleType(PRICE_ANNUAL_05.moduleType);
            excelTemplateBeanManager.setDesc(PRICE_ANNUAL_05.desc);
            return excelTemplateBeanManager;
        }
    };
    private String code;
    private String templateName;
    private String moduleType;
    private String desc;

    AnnualTemplateEnum(String code, String templateName, String moduleType, String desc) {
        this.code = code;
        this.templateName = templateName;
        this.moduleType = moduleType;
        this.desc = desc;
    }
    static final String ANNUAL = "PRICE_ANNUAL";
    static final String SUB_LEVEL_CODE = "{0}_{1}";

    /**
     * 获取模板信息
     *
     * @param levelCode 层级code
     * @param roleName 角色名称
     * @return 枚举信息
     * @throws CommonApplicationException
     */
    public static AnnualTemplateEnum getByCode(String levelCode, String roleName) throws CommonApplicationException {
        String key = MessageFormat.format(SUB_LEVEL_CODE, ANNUAL, levelCode);
        for (AnnualTemplateEnum value : AnnualTemplateEnum.values()) {
            if (value.name().equalsIgnoreCase(key)) {
                return value;
            }
        }
        throw new CommonApplicationException("Check the annual template definition relationship.");
    }
}
