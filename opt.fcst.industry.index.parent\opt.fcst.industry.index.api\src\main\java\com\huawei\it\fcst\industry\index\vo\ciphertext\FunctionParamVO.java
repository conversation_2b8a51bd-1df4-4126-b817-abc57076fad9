/*
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.ciphertext;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2023/4/23
 */
@Setter
@Getter
public class FunctionParamVO extends LtsParmaVO {

    String funcName;

    String periodId;

    String year;

    String caliberFlag;

    String dimensionType;

    String customId;

    String customizationId;

    String itemVersion;

    String overseaFlag;

    Long versionId;

    String viewFlag;

    Long levNum;

    String pageType;

    String costType;

    String granularityType;

    String lv0ProdListCode;

    String industryFlag;

    String ytdFlag;
}
