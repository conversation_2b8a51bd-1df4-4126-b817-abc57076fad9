/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.impl.annual;

import com.huawei.it.fcst.export.IExcelTemplateBeanManager;
import com.huawei.it.fcst.export.IExportProcessorService;
import com.huawei.it.fcst.industry.price.dao.IAnnualAmpCustomDao;
import com.huawei.it.fcst.industry.price.dao.IAnnualAmpPriceDao;
import com.huawei.it.fcst.industry.price.dao.IDmFcstPriceVersionInfoDao;
import com.huawei.it.fcst.industry.price.enums.GroupLevelEnum;
import com.huawei.it.fcst.industry.price.impl.common.PriceCommonService;
import com.huawei.it.fcst.industry.price.impl.common.PriceDataPermissionService;
import com.huawei.it.fcst.industry.price.impl.template.AnnualTemplateEnum;
import com.huawei.it.fcst.industry.price.service.annual.IAnnualAmpPriceService;
import com.huawei.it.fcst.industry.price.utils.FcstIndustryUtil;
import com.huawei.it.fcst.industry.price.vo.annual.AnnualAnalysisVO;
import com.huawei.it.fcst.industry.price.vo.annual.CommonAnnualVO;
import com.huawei.it.fcst.industry.price.vo.annual.DmFocAnnualAmpVO;
import com.huawei.it.fcst.industry.price.vo.version.DmFcstVersionInfoVO;
import com.huawei.it.fcst.util.PinyinUtil;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Named;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.Collator;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * AnnualAmpPriceService Class
 *
 * <AUTHOR>
 * @since 2024/11/6
 */
@Named("annualAmpPriceService")
@JalorResource(code = "annualAmpPriceService", desc = "定价指数-年度分析页面")
public class AnnualAmpPriceService implements IAnnualAmpPriceService {

    @Autowired
    private IDmFcstPriceVersionInfoDao dmFcstVersionInfoDao;

    @Autowired
    private IAnnualAmpPriceDao annualAmpPriceDao;

    @Autowired
    private IAnnualAmpCustomDao annualAmpCustomDao;

    @Autowired
    private PriceCommonService priceCommonService;

    @Autowired
    private IExportProcessorService iExportProcessorService;

    @Autowired
    private PriceDataPermissionService priceDataPermissionService;

    @JalorOperation(code = "allIndustryPriceCost", desc = "当前层级涨跌图")
    @Override
    public ResultDataVO allIndustryPriceCost(AnnualAnalysisVO annualAnalysisVO) throws CommonApplicationException {
        // 设置用户权限
        priceDataPermissionService.setUserPermission(annualAnalysisVO);
        if (!annualAnalysisVO.getIsMultipleSelect()) {
            // 计算最近的三年
            List<String> threeYears = getYearList();
            annualAnalysisVO.setYearList(threeYears);
        }
        FcstIndustryUtil.setSpecailCode(annualAnalysisVO);
        // 获取最新的version_id
        annualAnalysisVO.setVersionId(dmFcstVersionInfoDao.findVersionIdByDataType("ANNUAL").getVersionId());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();

        getCurrentAmpList(annualAnalysisVO, dmFocAnnualAmpVOList);
        // 设置无效的涨跌幅提示语
        setNoEffectiveAmp(dmFocAnnualAmpVOList, annualAnalysisVO, "data");
        return ResultDataVO.success(dmFocAnnualAmpVOList);
    }

    @JalorOperation(code = "multiIndustryPbiCostChart", desc = "多子项涨跌图")
    @Override
    public ResultDataVO multiIndustryPbiCostChart(AnnualAnalysisVO annualAnalysisVO) throws CommonApplicationException {
        // 设置用户权限
        priceDataPermissionService.setUserPermission(annualAnalysisVO);
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(annualAnalysisVO.getPageSize());
        pageVO.setCurPage(annualAnalysisVO.getPageIndex());
        // 设置参数
        setMultiParams(annualAnalysisVO);

        List<DmFocAnnualAmpVO> dmFocAnnualAmpResult = new ArrayList<>();

        if (annualAnalysisVO.getIsNeedBlur()) {
            // 走虚化且是最细力度的时候，需要取正常结果表数据
            dmFocAnnualAmpResult.addAll(multiChildMinLevel(annualAnalysisVO, pageVO));
        }
        if (CollectionUtils.isNotEmpty(annualAnalysisVO.getGroupCodeList())) {
            dmFocAnnualAmpResult.addAll(multiChildNotContainsCustom(annualAnalysisVO, pageVO));
        }

        // 设置无效的涨跌幅提示语
        setNoEffectiveAmp(dmFocAnnualAmpResult, annualAnalysisVO, "data");
        Map<Object, Object> resultMap = new LinkedHashMap<>();
        resultMap.put("result", dmFocAnnualAmpResult);
        resultMap.put("pageVo", pageVO);
        return ResultDataVO.success(resultMap);
    }

    @JalorOperation(code = "industryCostList", desc = "涨跌一览表")
    @Override
    public ResultDataVO industryCostList(AnnualAnalysisVO annualAnalysisVO) throws CommonApplicationException {
        // 设置用户权限
        priceDataPermissionService.setUserPermission(annualAnalysisVO);
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(annualAnalysisVO.getPageSize());
        pageVO.setCurPage(annualAnalysisVO.getPageIndex());
        setOverviewSearchParam(annualAnalysisVO);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpList = new ArrayList<>();
        if (annualAnalysisVO.getIsNeedBlur()) {
            // 走虚化且是最细力度的时候，需要取正常结果表数据
            dmFocAnnualAmpList.addAll(annualAmpCustomDao.industryAmpMinLevelList(annualAnalysisVO));
        }
        if (CollectionUtils.isNotEmpty(annualAnalysisVO.getGroupCodeList())) {
            dmFocAnnualAmpList.addAll(annualAmpPriceDao.industryNormalCostList(annualAnalysisVO));
        }
        // 按照权重*涨跌大小排序；各层级权重排序;获取权重*涨跌最大值
        List<DmFocAnnualAmpVO> dmFocAnnualAmpResult = dealGroupLevelAmp(dmFocAnnualAmpList, annualAnalysisVO, true, pageVO);
        // 设置无效的涨跌幅提示语
        setNoEffectiveAmp(dmFocAnnualAmpResult, annualAnalysisVO, "data");
        // 排序和分页
        List<DmFocAnnualAmpVO> annualAmpAndWeightList = orderColumnAndLimitPage(dmFocAnnualAmpResult, annualAnalysisVO, pageVO);
        Map<Object, Object> resultMap = new LinkedHashMap<>();
        resultMap.put("maxValue", annualAnalysisVO.getMaxValue() + "%");
        resultMap.put("result", annualAmpAndWeightList);
        resultMap.put("pageVo", pageVO);
        return ResultDataVO.success(resultMap);
    }

    @JalorOperation(code = "periodYearList", desc = "会计期年")
    @Override
    public ResultDataVO periodYearList(AnnualAnalysisVO annualAnalysisVO) {
        return ResultDataVO.success(getYearList());
    }

    @JalorOperation(code = "exportDetail", desc = "年度分析-数据下载")
    @Audit(module = "annualAmpPriceService-exportDetail", operation = "exportDetail", message = "年度分析-数据下载")
    @Override
    public ResultDataVO exportDetail(HttpServletResponse response, AnnualAnalysisVO annualAnalysisVO) throws ApplicationException {
        // 设置用户权限
        priceDataPermissionService.setUserPermission(annualAnalysisVO);
        // 计算最近的三年
        List<String> threeYears = getYearList();
        annualAnalysisVO.setYearList(threeYears);
        // 获取最新的version_id
        annualAnalysisVO.setVersionId(dmFcstVersionInfoDao.findVersionIdByDataType("ANNUAL").getVersionId());
        // 设置标题
        priceCommonService.setTitleDisplayName(annualAnalysisVO);
        FcstIndustryUtil.setSpecailCode(annualAnalysisVO);
        // 实际数截止月
        String mixActualMonth = dmFcstVersionInfoDao.findAnnualActualMonth(annualAnalysisVO);
        annualAnalysisVO.setActualMonth(mixActualMonth);
        if (!GroupLevelEnum.SPART.getValue().equals(annualAnalysisVO.getGroupLevel())) {
            Map map = FcstIndustryUtil.getNextGroupLevel(annualAnalysisVO);
            String nextGroupLevel = String.valueOf(map.get("nextGroupLevel"));
            String nextGroupName = String.valueOf(map.get("nextGroupName"));
            annualAnalysisVO.setNextGroupName(nextGroupName);
            annualAnalysisVO.setNextGroupLevel(nextGroupLevel);
        }
        String name = GroupLevelEnum.getInstance(annualAnalysisVO.getGroupLevel()).getName();
        annualAnalysisVO.setName(name);
        // 获取模板
        IExcelTemplateBeanManager templateBeanManager = getExportTemplate(annualAnalysisVO);

        Map<String, Object> parameters = new HashMap<>();
        String module = "价格指数-产业-ICT年度分析";
        parameters.put("exportModuleName", module);
        parameters.put("exportFileName", annualAnalysisVO.getFileName());
        iExportProcessorService.fillEasyExcelExport(response, templateBeanManager, annualAnalysisVO, parameters);
        return ResultDataVO.success();
    }

    private IExcelTemplateBeanManager getExportTemplate(AnnualAnalysisVO annualAnalysisVO) throws CommonApplicationException {
        IExcelTemplateBeanManager templateBeanManager;
        if (!annualAnalysisVO.getIsNeedBlur()) {
            if (GroupLevelEnum.SPART.getValue().equals(annualAnalysisVO.getGroupLevel())) {
                templateBeanManager = AnnualTemplateEnum.getByCode("02", "");
            } else if (GroupLevelEnum.SPART.getValue().equals(annualAnalysisVO.getNextGroupLevel())) {
                templateBeanManager = getNextSpartTemplate(annualAnalysisVO);
            } else {
                templateBeanManager = getTemplate(annualAnalysisVO);
            }
        } else {
            templateBeanManager = getTemplate(annualAnalysisVO);
        }
        return templateBeanManager;
    }

    private IExcelTemplateBeanManager getNextSpartTemplate(AnnualAnalysisVO annualAnalysisVO) throws CommonApplicationException {
        if (annualAnalysisVO.getIsMultipleSelect()) {
            return AnnualTemplateEnum.getByCode("04", "");
        } else {
            return AnnualTemplateEnum.getByCode("05", "");
        }
    }

    private IExcelTemplateBeanManager getTemplate(AnnualAnalysisVO annualAnalysisVO) throws CommonApplicationException {
        if (annualAnalysisVO.getIsMultipleSelect()) {
            return AnnualTemplateEnum.getByCode("03", "");
        } else {
            return AnnualTemplateEnum.getByCode("01", "");
        }
    }

    public List<DmFocAnnualAmpVO> dealGroupLevelAmp(List<DmFocAnnualAmpVO> annualAmpAndWeightList, AnnualAnalysisVO annualAnalysisVO, boolean flag, PageVO pageVO) {
        getMaxWeightRate(annualAmpAndWeightList, annualAnalysisVO);
        // 拼接%，保留一位小数
        concatPercent(annualAmpAndWeightList, annualAnalysisVO, flag);
        return annualAmpAndWeightList;
    }

    private List<DmFocAnnualAmpVO> orderColumnAndLimitPage(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult, AnnualAnalysisVO annualVO, PageVO pageVO) {
        String orderColumn = annualVO.getOrderColumn();
        if (StringUtils.isNotBlank(orderColumn)) {
            switch (orderColumn) {
                case "weightRate":
                    dmFocAnnualAmpVOPagedResult = sortAmpByWeightRateBar(dmFocAnnualAmpVOPagedResult, annualVO);
                    break;
                case "groupCnName":
                    groupCnNameSort(dmFocAnnualAmpVOPagedResult, annualVO);
                    break;
                case "groupCode":
                    groupCodeSort(dmFocAnnualAmpVOPagedResult, annualVO);
                    break;
                case "annualAmp":
                    dmFocAnnualAmpVOPagedResult = getAnnualAmpPageResult(dmFocAnnualAmpVOPagedResult, annualVO);
                    break;
                case "weightAnnualAmpPercent":
                    if ("asc".equals(annualVO.getOrderMethod())) {
                        dmFocAnnualAmpVOPagedResult = sortByWeightAnnualAmpPercentBar(dmFocAnnualAmpVOPagedResult, annualVO, true);
                    } else {
                        dmFocAnnualAmpVOPagedResult = sortByWeightAnnualAmpPercentBar(dmFocAnnualAmpVOPagedResult, annualVO, false);
                    }
                    break;
                default:
                    break;
            }
        } else {
            // 根据状态码区分-和有数据的记录后，默认权重大小排序
            dmFocAnnualAmpVOPagedResult = sortWeightBarAndRecordList(dmFocAnnualAmpVOPagedResult, annualVO);
        }

        // 分页
        int count = dmFocAnnualAmpVOPagedResult.size();
        List<DmFocAnnualAmpVO> dmFocAnnualAmpPagedResult = limitOverviewPage(dmFocAnnualAmpVOPagedResult, pageVO);
        pageVO.setTotalRows(count);
        return dmFocAnnualAmpPagedResult;
    }

    private List<DmFocAnnualAmpVO> limitOverviewPage(List<DmFocAnnualAmpVO> dmFocAnnualPageResult, PageVO pageVO) {
        int count = dmFocAnnualPageResult.size();
        pageVO.setTotalRows(count);
        int curPage = pageVO.getCurPage() - 1;
        int pageSize = pageVO.getPageSize();
        List<DmFocAnnualAmpVO> annualAmpList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dmFocAnnualPageResult) && count > 0) {
            int fromIndex = curPage * pageSize;
            int totalIndex = (curPage + 1) * pageSize;
            if (totalIndex > count) {
                totalIndex = count;
            }
            annualAmpList = dmFocAnnualPageResult.subList(fromIndex, totalIndex);
        }
        return annualAmpList;
    }

    private List<DmFocAnnualAmpVO> sortWeightBarAndRecordList(List<DmFocAnnualAmpVO> dmFocAnnualAmpPagedResult, AnnualAnalysisVO analysisVO) {
        List<DmFocAnnualAmpVO> barAnnualAmpList = dmFocAnnualAmpPagedResult.stream().filter(result -> "0*".equals(result.getWeightRate())).collect(Collectors.toList());
        List<DmFocAnnualAmpVO> otherAmpList = dmFocAnnualAmpPagedResult.stream().filter(vo -> !barAnnualAmpList.contains(vo)).collect(Collectors.toList());
        sortByWeight(otherAmpList);
        List<DmFocAnnualAmpVO> newDmFocAnnualAmpPagedResult = new ArrayList<>();
        newDmFocAnnualAmpPagedResult.addAll(otherAmpList);
        newDmFocAnnualAmpPagedResult.addAll(barAnnualAmpList);
        return newDmFocAnnualAmpPagedResult;
    }

    private void sortByWeight(List<DmFocAnnualAmpVO> ampWeightList) {
        Collections.sort(ampWeightList, new Comparator<DmFocAnnualAmpVO>() {
            @Override
            public int compare(DmFocAnnualAmpVO dmFocAnnualAmpOne, DmFocAnnualAmpVO dmFocAnnualAmpTwo) {
                String weightRate1 = dmFocAnnualAmpOne.getWeightRate();
                String weightRate2 = dmFocAnnualAmpTwo.getWeightRate();
                Double weightNumOne = null;
                Double weightNumTwo = null;
                if (StringUtils.isNotBlank(weightRate1) && !weightRate1.equals("%")) {
                    weightRate1 = subPercentStr(weightRate1);
                    weightNumOne = Double.parseDouble(weightRate1);
                }
                if (StringUtils.isNotBlank(weightRate2) && !weightRate2.equals("%")) {
                    weightRate2 = subPercentStr(weightRate2);
                    weightNumTwo = Double.parseDouble(weightRate2);
                }
                if (weightNumOne == null && weightNumTwo == null) {
                    return sortByGroupCnName(dmFocAnnualAmpOne, dmFocAnnualAmpTwo);
                }
                if (weightNumOne == null) {
                    return 1;
                }
                if (weightNumTwo == null) {
                    return -1;
                }
                if (weightNumOne != null && weightNumTwo != null && ObjectUtils.notEqual(weightNumOne, weightNumTwo)) {
                    return weightNumTwo.compareTo(weightNumOne);
                }
                if (weightNumOne != null && weightNumTwo != null && Double.toString(weightNumOne).equals(Double.toString(weightNumTwo))) {
                    return sortByGroupCnName(dmFocAnnualAmpOne, dmFocAnnualAmpTwo);
                }
                return 0;
            }
        });
    }

    private String subPercentStr(String str) {
        if (str.contains("%")) {
            int index = str.indexOf("%");
            return str.substring(0, index);
        }
        return str;
    }

    private List<DmFocAnnualAmpVO> getAnnualAmpPageResult(List<DmFocAnnualAmpVO> annualAmpPagedResult, AnnualAnalysisVO annualAnalysisVO) {
        List<DmFocAnnualAmpVO> barAnnualAmpList = annualAmpPagedResult.stream().filter(result -> "0*".equals(result.getAnnualAmp())).collect(Collectors.toList());
        List<DmFocAnnualAmpVO> otherAmpList = annualAmpPagedResult.stream().filter(vo -> !barAnnualAmpList.contains(vo)).collect(Collectors.toList());

        annualAmpSort(otherAmpList, annualAnalysisVO);

        List<DmFocAnnualAmpVO> ampPagedResult = new ArrayList<>();
        ampPagedResult.addAll(otherAmpList);
        ampPagedResult.addAll(barAnnualAmpList);
        return ampPagedResult;
    }

    private List<DmFocAnnualAmpVO> sortAmpByWeightRateBar(List<DmFocAnnualAmpVO> dmFocAnnualAmpPagedResult, AnnualAnalysisVO analysisVO) {

        List<DmFocAnnualAmpVO> barAnnualAmpVOList = dmFocAnnualAmpPagedResult.stream().filter(result -> "0*".equals(result.getWeightRate())).collect(Collectors.toList());
        List<DmFocAnnualAmpVO> otherList = dmFocAnnualAmpPagedResult.stream().filter(vo -> !barAnnualAmpVOList.contains(vo)).collect(Collectors.toList());
        weightRateOrder(otherList, analysisVO);
        List<DmFocAnnualAmpVO> newAnnualAmpVOPagedResult = new ArrayList<>();
        newAnnualAmpVOPagedResult.addAll(otherList);
        newAnnualAmpVOPagedResult.addAll(barAnnualAmpVOList);
        return newAnnualAmpVOPagedResult;
    }

    private List<DmFocAnnualAmpVO> sortByWeightAnnualAmpPercentBar(List<DmFocAnnualAmpVO> dmFocAnnualAmpPagedResult, AnnualAnalysisVO analysisVO, boolean flag) {

        List<DmFocAnnualAmpVO> barAnnualAmpVOList = dmFocAnnualAmpPagedResult.stream().filter(result -> "0*".equals(result.getWeightAnnualAmpPercent())).collect(Collectors.toList());
        List<DmFocAnnualAmpVO> otherWeightList = dmFocAnnualAmpPagedResult.stream().filter(vo -> !barAnnualAmpVOList.contains(vo)).collect(Collectors.toList());
        orderWeightAnnualAmpPercent(otherWeightList, analysisVO, flag);
        List<DmFocAnnualAmpVO> newAnnualAmpPagedResult = new ArrayList<>();
        newAnnualAmpPagedResult.addAll(otherWeightList);
        newAnnualAmpPagedResult.addAll(barAnnualAmpVOList);
        return newAnnualAmpPagedResult;
    }

    private void orderWeightAnnualAmpPercent(List<DmFocAnnualAmpVO> dmFocAnnualAmpPagedResult, AnnualAnalysisVO annualAnalysisVO, boolean flag) {
        // 权重*涨跌 排序
        Collections.sort(dmFocAnnualAmpPagedResult, new Comparator<DmFocAnnualAmpVO>() {
            @Override
            public int compare(DmFocAnnualAmpVO dmFocAnnualAmp1, DmFocAnnualAmpVO dmFocAnnualAmp2) {
                String weightAmpPercent1 = dmFocAnnualAmp1.getWeightAnnualAmpPercent();
                String weightAmpPercent2 = dmFocAnnualAmp2.getWeightAnnualAmpPercent();
                if (flag) {
                    return compareWeightColumn(weightAmpPercent1, weightAmpPercent2);
                }
                return compareWeightColumn(weightAmpPercent2, weightAmpPercent1);
            }
        });
    }

    private void annualAmpSort(List<DmFocAnnualAmpVO> dmFocAnnualResult, AnnualAnalysisVO analysisVO) {
        if ("asc".equals(analysisVO.getOrderMethod())) {
            Collections.sort(dmFocAnnualResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnual1, DmFocAnnualAmpVO dmFocAnnual2) {
                    String annualAmp1 = dmFocAnnual1.getAnnualAmp();
                    String annualAmp2 = dmFocAnnual2.getAnnualAmp();
                    return compareWeightColumn(annualAmp1, annualAmp2);
                }
            });
        } else {
            Collections.sort(dmFocAnnualResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnual1, DmFocAnnualAmpVO dmFocAnnual2) {
                    String annualAmp1 = dmFocAnnual1.getAnnualAmp();
                    String annualAmp2 = dmFocAnnual2.getAnnualAmp();
                    return compareWeightColumn(annualAmp2, annualAmp1);
                }
            });
        }
    }

    private void groupCnNameSort(List<DmFocAnnualAmpVO> dmFocAnnualPageResult, AnnualAnalysisVO annualVO) {
        if ("asc".equals(annualVO.getOrderMethod())) {
            Collections.sort(dmFocAnnualPageResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnual1, DmFocAnnualAmpVO dmFocAnnual2) {
                    return sortByGroupCnName(dmFocAnnual1, dmFocAnnual2);
                }
            });
        } else {
            Collections.sort(dmFocAnnualPageResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnual1, DmFocAnnualAmpVO dmFocAnnual2) {
                    return sortByGroupCnName(dmFocAnnual2, dmFocAnnual1);
                }
            });
        }
    }

    private int sortByGroupCnName(DmFocAnnualAmpVO dmFocAnnualAmpOne, DmFocAnnualAmpVO dmFocAnnualAmpTwo) {
        String groupCnName1 = StringUtils.isEmpty(dmFocAnnualAmpOne.getGroupCnName()) ? dmFocAnnualAmpOne.getGroupCode() : dmFocAnnualAmpOne.getGroupCnName();
        String groupCnName2 = StringUtils.isEmpty(dmFocAnnualAmpTwo.getGroupCnName()) ? dmFocAnnualAmpTwo.getGroupCode() : dmFocAnnualAmpTwo.getGroupCnName();

        String alphabet1 = groupCnName1.substring(0, 1);
        // 判断首字符是否为中文，如果是中文便将首字符拼音的首字母和&符号加在字符串前面
        if (alphabet1.matches("[\\u4e00-\\u9fa5]+")) {
            groupCnName1 = PinyinUtil.chineseToPingyin(alphabet1) + "&" + groupCnName1;
        }
        String alphabet2 = groupCnName2.substring(0, 1);
        // 判断首字符是否为中文，如果是中文便将首字符拼音的首字母和&符号加在字符串前面
        if (alphabet2.matches("[\\u4e00-\\u9fa5]+")) {
            groupCnName2 = PinyinUtil.chineseToPingyin(alphabet2) + "&" + groupCnName2;
        }
        return Collator.getInstance(Locale.CHINA).compare(groupCnName1, groupCnName2);
    }

    private void groupCodeSort(List<DmFocAnnualAmpVO> dmFocAnnualPagResult, AnnualAnalysisVO annualAnalysisVO) {
        if ("asc".equals(annualAnalysisVO.getOrderMethod())) {
            Collections.sort(dmFocAnnualPagResult, Comparator.comparing(DmFocAnnualAmpVO::getGroupCode));
        } else {
            Collections.sort(dmFocAnnualPagResult, Comparator.comparing(DmFocAnnualAmpVO::getGroupCode).reversed());
        }
    }

    private void weightRateOrder(List<DmFocAnnualAmpVO> annualAmpPageResult, AnnualAnalysisVO annualAnalysisVO) {
        if ("asc".equals(annualAnalysisVO.getOrderMethod())) {
            Collections.sort(annualAmpPageResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnualOne, DmFocAnnualAmpVO dmFocAnnualTwo) {
                    String weightRate1 = dmFocAnnualOne.getWeightRate();
                    String weightRate2 = dmFocAnnualTwo.getWeightRate();
                    return compareWeightColumn(weightRate1, weightRate2);
                }
            });
        } else {
            Collections.sort(annualAmpPageResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnualOne, DmFocAnnualAmpVO dmFocAnnualTwo) {
                    String weightRate1 = dmFocAnnualOne.getWeightRate();
                    String weightRate2 = dmFocAnnualTwo.getWeightRate();
                    return compareWeightColumn(weightRate2, weightRate1);
                }
            });
        }
    }

    private int compareWeightColumn(String weightRateOne, String weightRateTwo) {
        CommonAnnualVO commonVO = transformStr(weightRateOne, weightRateTwo);
        Double num1 = commonVO.getNum1();
        Double num2 = commonVO.getNum2();
        if (num1 != null && num2 != null && ObjectUtils.notEqual(num1, num2)) {
            return num1.compareTo(num2);
        }
        if (num1 == null && num2 == null) {
            return 0;
        }
        if (num1 == null) {
            return -1;
        }
        if (num2 == null) {
            return 1;
        }
        return 0;
    }

    private CommonAnnualVO transformStr(String str1, String str2) {
        CommonAnnualVO commonAnnualVO = new CommonAnnualVO();
        Double numberOne = null;
        Double numberTwo = null;
        if (StringUtils.isNotBlank(str1) && !str1.equals("0*")) {
            str1 = subPercentStr(str1);
            numberOne = Double.parseDouble(str1);
        }
        if (StringUtils.isNotBlank(str2) && !str2.equals("0*")) {
            str2 = subPercentStr(str2);
            numberTwo = Double.parseDouble(str2);
        }
        commonAnnualVO.setNum1(numberOne);
        commonAnnualVO.setNum2(numberTwo);
        return commonAnnualVO;
    }


    private void getMaxWeightRate(List<DmFocAnnualAmpVO> annualAmpAndWeightList, AnnualAnalysisVO annualAnalysisVO) {
        List<String> annualPercentList = new ArrayList<>();
        List<String> weightAnnualAmpPercentList = annualAmpAndWeightList.stream().map(DmFocAnnualAmpVO::getWeightAnnualAmpPercent).collect(Collectors.toList());
        Double weightAmpPercent = 0.0D;
        for (String weightAnnualAmpPercent : weightAnnualAmpPercentList) {
            if (StringUtils.isNotBlank(weightAnnualAmpPercent)) {
                weightAmpPercent = Math.abs(Double.parseDouble(weightAnnualAmpPercent));
            }
            annualPercentList.add(new BigDecimal(String.valueOf(weightAmpPercent)).setScale(1, BigDecimal.ROUND_HALF_UP).toString());
        }
        if (CollectionUtils.isNotEmpty(annualPercentList)) {
            String maxWeightAnnual = Collections.max(annualPercentList);
            annualAnalysisVO.setMaxValue(maxWeightAnnual);
        }
    }

    private void concatPercent(List<DmFocAnnualAmpVO> annualAmpList, AnnualAnalysisVO annualVO, boolean flag) {
        annualAmpList.forEach(annual -> {
            String annualAmp = annual.getAnnualAmp();
            String weightRate = annual.getWeightRate();
            String weightAnnualAmpPercent = annual.getWeightAnnualAmpPercent();
            if (StringUtils.isNotBlank(annualAmp) && !annualAmp.equals("0*")) {
                annual.setAnnualAmp(annualAmp + "%");
            }
            if (StringUtils.isNotBlank(weightRate) && !weightRate.equals("0*")) {
                weightRate = new BigDecimal(weightRate).setScale(1, BigDecimal.ROUND_HALF_UP).toString();
                annual.setWeightRate(weightRate + "%");
            }
            if (StringUtils.isNotBlank(weightAnnualAmpPercent) && !weightAnnualAmpPercent.equals("0*")) {
                String newWeightAnnualAmpPercent = new BigDecimal(weightAnnualAmpPercent).setScale(1, BigDecimal.ROUND_HALF_UP).toString();
                annual.setWeightAnnualAmpPercent(newWeightAnnualAmpPercent + "%");
            }
        });
    }

    public void getCurrentAmpList(AnnualAnalysisVO annualAnalysisVO, List<DmFocAnnualAmpVO> dmFocAnnualAmpList) throws CommonApplicationException {
        // 需要虚化
        if (annualAnalysisVO.getIsNeedBlur()) {
            dmFocAnnualAmpList.addAll(annualAmpCustomDao.allAmpCustomCost(annualAnalysisVO));
        }
        if (CollectionUtils.isNotEmpty(annualAnalysisVO.getGroupCodeList())) {
            if (GroupLevelEnum.SPART.getValue().equals(annualAnalysisVO.getGroupLevel())) {
                annualAnalysisVO.setParentCodeList(annualAnalysisVO.getProdListCodeList());
            }
            dmFocAnnualAmpList.addAll(annualAmpPriceDao.allAmpNormalCost(annualAnalysisVO));
        }
    }

    public List<String> getYearList() {
        DmFcstVersionInfoVO versionInfoVO = dmFcstVersionInfoDao.findVersionIdByDataType("ANNUAL");
        return annualAmpPriceDao.getPeriodYearList(versionInfoVO.getVersionId());
    }

    private void setOverviewSearchParam(AnnualAnalysisVO annualAnalysisVO) throws CommonApplicationException {
        annualAnalysisVO.setParentCodeList(annualAnalysisVO.getGroupCodeList());
        annualAnalysisVO.setParentLevel(annualAnalysisVO.getGroupLevel());

        annualAnalysisVO.setParentLevel(annualAnalysisVO.getGroupLevel());
        if (!annualAnalysisVO.getIsNeedBlur() && !GroupLevelEnum.SPART.getValue().equals(annualAnalysisVO.getGroupLevel())) {
            String nextGroupLevel = FcstIndustryUtil.getViewNextGroupLevel(annualAnalysisVO);
            annualAnalysisVO.setGroupLevel(nextGroupLevel);
        }
        if (!annualAnalysisVO.getIsMultipleSelect()) {
            // 计算最近的三年
            annualAnalysisVO.setYearList(getYearList());
        }
        annualAnalysisVO.setLastYear(getYearList().get(0));

        FcstIndustryUtil.setSpecailCode(annualAnalysisVO);
        annualAnalysisVO.setVersionId(dmFcstVersionInfoDao.findVersionIdByDataType("ANNUAL").getVersionId());
    }

    private List<DmFocAnnualAmpVO> multiChildMinLevel(AnnualAnalysisVO annualAnalysisVO, PageVO pageVO) {

        List<DmFocAnnualAmpVO> groupCodeOrderList = annualAmpCustomDao.findCustomCodeOrderMinLevel(annualAnalysisVO);

        String groupCodeOrder = limitGroupCodeByPage(groupCodeOrderList, pageVO, "minLevel");
        annualAnalysisVO.setProdRndTeamCodeOrder(groupCodeOrder);
        annualAnalysisVO.setCondition("minLevel");
        return annualAmpCustomDao.multiAmpMinLevelChart(annualAnalysisVO);
    }

    private List<DmFocAnnualAmpVO> multiChildNotContainsCustom(AnnualAnalysisVO annualAnalysisVO, PageVO pageVO) {

        List<DmFocAnnualAmpVO> groupCodeOrderList = annualAmpPriceDao.findAmpGroupCodeOrderByWeight(annualAnalysisVO);

        String groupCodeOrder = limitGroupCodeByPage(groupCodeOrderList, pageVO, "normal");
        annualAnalysisVO.setGroupCodeOrder(groupCodeOrder);
        return annualAmpPriceDao.multiAmpNormalChart(annualAnalysisVO);
    }

    private String limitGroupCodeByPage(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult, PageVO pageVO, String condition) {
        int count = dmFocAnnualAmpVOPagedResult.size();
        pageVO.setTotalRows(count);
        int curPage = pageVO.getCurPage() - 1;
        int pageSize = pageVO.getPageSize();
        List<DmFocAnnualAmpVO> annualAmpList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dmFocAnnualAmpVOPagedResult) && count > 0) {
            int fromIndex = curPage * pageSize;
            int totalIndex = (curPage + 1) * pageSize;
            if (totalIndex > count) {
                totalIndex = count;
            }
            annualAmpList = dmFocAnnualAmpVOPagedResult.subList(fromIndex, totalIndex);
        }
        if ("minLevel".equals(condition)) {
            return annualAmpList.stream().map(DmFocAnnualAmpVO::getProdRndTeamCode).collect(Collectors.joining(","));
        }
        return annualAmpList.stream().map(DmFocAnnualAmpVO::getGroupCode).collect(Collectors.joining(","));
    }

    public void setNoEffectiveAmp(List<DmFocAnnualAmpVO> dmFocAnnualAmpList, AnnualAnalysisVO annualAnalysisVO, String type) {
        String groupLevel = annualAnalysisVO.getGroupLevel();
        boolean specialFlag = GroupLevelEnum.SPART.getValue().equals(groupLevel) || annualAnalysisVO.getIsNeedBlur();
        dmFocAnnualAmpList.stream().forEach(vo -> {
            String statusCode = vo.getStatusCode();
            if (specialFlag) {
                if (StringUtils.isNotBlank(statusCode)) {
                    getHoverMsgForMinCondition(statusCode, vo);
                }
            } else {
                if (StringUtils.isNotBlank(statusCode)) {
                    setGroupCodeHoverMsg(statusCode, vo);
                }
            }
        });
        setPromptMessage(dmFocAnnualAmpList, type, specialFlag);
    }

    private void getHoverMsgForMinCondition(String statusCode, DmFocAnnualAmpVO annualVO) {
        switch (statusCode) {
            case "2":
                annualVO.setHoverMsg(subtractNum(annualVO.getPeriodYear(), 1) + "、" + annualVO.getPeriodYear() + "年无有效数据，无法计算涨跌幅");
                break;
            case "4":
                annualVO.setHoverMsg(annualVO.getAnnualAmp() + ";" + subtractNum(annualVO.getPeriodYear(), 1) + "无有效数据，由"+ annualVO.getAppendYear() + "年均本补齐");
                break;
            case "5":
                annualVO.setHoverMsg(annualVO.getAnnualAmp() + ";" + annualVO.getPeriodYear() + "年无有效数据，由" + annualVO.getAppendYear() + "年均本补齐");
                break;
            default:
                break;
        }
    }

    private void setGroupCodeHoverMsg(String statusCode, DmFocAnnualAmpVO annualVO) {
        if ("2".equals(statusCode)) {
            annualVO.setHoverMsg(subtractNum(annualVO.getPeriodYear(), 1)+"、" + annualVO.getPeriodYear() + "年无有效数据，无法计算涨跌幅");
        }
    }

    private void setPromptMessage(List<DmFocAnnualAmpVO> dmFocAnnualAmpList, String type, boolean specialFlag) {
        // 当涨跌幅为0且有提示语时，涨跌幅设置为0*
        dmFocAnnualAmpList.stream().forEach(annual -> {
            if (StringUtils.isNotBlank(annual.getHoverMsg()) && !"5".equals(annual.getStatusCode())
                    && ("0.0%".equals(annual.getAnnualAmp()) || "0.0".equals(annual.getAnnualAmp()))) {
                if ("data".equals(type)) {
                    annual.setAnnualAmp("0*");
                    annual.setWeightAnnualAmpPercent("0*");
                } else {
                    annual.setAnnualAmp(null);
                    if (!specialFlag) {
                        annual.setWeightAnnualAmpPercent(null);
                    }
                }
            }
            if (!specialFlag && StringUtils.isNotBlank(annual.getAppendFlag())
                    && "Y".equals(annual.getAppendFlag())) {
                if ("data".equals(type)) {
                    annual.setWeightRate("0*");
                } else {
                    annual.setWeightRate(null);
                }
            }
        });
    }

    private void setMultiParams(AnnualAnalysisVO annualAnalysisVO) throws CommonApplicationException {
        // 计算最近的三年
        List<String> threeYears = getYearList();
        if (CollectionUtils.isNotEmpty(threeYears)) {
            annualAnalysisVO.setYear(threeYears.get(0));
        }
        // 获取当前年份YTD
        if (!annualAnalysisVO.getIsMultipleSelect()) {
            annualAnalysisVO.setYearList(threeYears);
        }
        annualAnalysisVO.setParentCodeList(annualAnalysisVO.getGroupCodeList());

        annualAnalysisVO.setParentLevel(annualAnalysisVO.getGroupLevel());
        String nextGroupLevel = FcstIndustryUtil.getViewNextGroupLevel(annualAnalysisVO);
        annualAnalysisVO.setGroupLevel(nextGroupLevel);

        FcstIndustryUtil.setSpecailCode(annualAnalysisVO);
        annualAnalysisVO.setVersionId(dmFcstVersionInfoDao.findVersionIdByDataType("ANNUAL").getVersionId());
    }

    private String subtractNum(String periodYear, int number) {
        BigDecimal yearDecimal = new BigDecimal(periodYear);
        BigDecimal year = yearDecimal.subtract(new BigDecimal(number));
        return year.toString();
    }
}
