/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.task;

import com.huawei.it.fcst.enums.SubModuleName;

import java.io.Serializable;

/**
 * pbi 虚化任务接口
 *
 * <AUTHOR>
 * @since 202407
 */
public interface ITaskProcessService extends Serializable {

    /**
     * 设置模块类型
     *
     * @return
     */
    SubModuleName getSubModuleName();

    /**
     * 任务类型
     *
     * @return 分组类型
     */
    IGroupTaskType getTaskType();

    /**
     * 任务执行之前
     *
     * @param serializable
     */
    Serializable before(Serializable serializable);

    /**
     * 函数执行
     *
     * @param parma
     * @param beforeResult
     * @return 成功失败
     */
    Boolean process(Serializable parma, Serializable beforeResult);

    /**
     * 更新函数执行状态
     *
     * @param serializable
     * @param status
     */
    void after(Serializable serializable, Boolean status);
}
