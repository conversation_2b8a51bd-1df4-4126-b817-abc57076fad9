<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IDmFocTopCateInfoDao">
    <resultMap type="com.huawei.it.fcst.industry.index.vo.config.DmFocTopCateInfoDTO" id="resultMap">
        <result property="delFlag" column="del_flag"/>
        <result property="lv0ProdRdTeamCnName" column="lv0_prod_rd_team_cn_name"/>
        <result property="lv1ProdRdTeamCnName" column="lv1_prod_rd_team_cn_name"/>
        <result property="lv2ProdRdTeamCnName" column="lv2_prod_rd_team_cn_name"/>
        <result property="lv3ProdRdTeamCnName" column="lv3_prod_rd_team_cn_name"/>
        <result property="lv4ProdRdTeamCnName" column="lv4_prod_rd_team_cn_name"/>
        <result property="lv0ProdRndTeamCode" column="lv0_prod_rnd_team_code"/>
        <result property="lv1ProdRndTeamCode" column="lv1_prod_rnd_team_code"/>
        <result property="lv2ProdRndTeamCode" column="lv2_prod_rnd_team_code"/>
        <result property="lv3ProdRndTeamCode" column="lv3_prod_rnd_team_code"/>
        <result property="lv4ProdRndTeamCode" column="lv4_prod_rnd_team_code"/>
        <result property="l1Name" column="l1_name"/>
        <result property="l2Name" column="l2_name"/>
        <result property="topL3CegCode" column="top_l3_ceg_code"/>
        <result property="topL3CegCnName" column="top_l3_ceg_cn_name"/>
        <result property="topL3CegShortCnName" column="top_l3_ceg_short_cn_name"/>
        <result property="topL4CegCode" column="top_l4_ceg_code"/>
        <result property="topL4CegCnName" column="top_l4_ceg_cn_name"/>
        <result property="topL4CegShortCnName" column="top_l4_ceg_short_cn_name"/>
        <result property="overseaFlag" column="oversea_flag"/>
        <result property="lv0ProdListCode" column="lv0_prod_list_code"/>
        <result property="dimensionCode" column="dimension_code"/>
        <result property="dimensionCnName" column="dimension_cn_name"/>
        <result property="dimensionSubCategoryCode" column="dimension_subCategory_code"/>
        <result property="dimensionSubCategoryCnName" column="dimension_subCategory_cn_name"/>
        <result property="dimensionSubDetailCode" column="dimension_sub_detail_code"/>
        <result property="dimensionSubDetailCnName" column="dimension_sub_detail_cn_name"/>
        <result property="spartCode" column="spart_code"/>
        <result property="spartCnName" column="spart_cn_name"/>
        <result property="coaCode" column="coa_code"/>
        <result property="coaCnName" column="coa_cn_name"/>
        <result property="topCategoryCnName" column="top_category_cn_name"/>
        <result property="topCategoryCode" column="top_category_code"/>
        <result property="topShippingObjectCode" column="top_shipping_object_code"/>
        <result property="topManufactureObjectCode" column="top_manufacture_object_code"/>
        <result property="topShippingObjectCnName" column="top_shipping_object_cn_name"/>
        <result property="topManufactureObjectCnName" column="top_manufacture_object_cn_name"/>
        <result property="viewSortId" column="view_sort_id"/>
        <result property="parentCode" column="parent_code"/>
        <result property="topFlag" column="is_top_flag"/>
        <result property="weight0" column="weight0"/>
        <result property="weight1" column="weight1"/>
        <result property="weight2" column="weight2"/>
        <result property="weight3" column="weight3"/>
        <result property="weight4" column="weight4"/>
        <result property="weight4" column="weight4"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="versionId" column="version_id"/>
        <result property="creationDate" column="creation_date"/>
        <result property="createdBy" column="created_by"/>
        <result property="lastUpdateDate" column="last_update_date"/>
    </resultMap>

    <sql id="setValues">
        <if test='lv0ProdRndTeamCode != null and lv0ProdRndTeamCode!=""'>
            AND lv0_prod_rnd_team_code = #{lv0ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='lv1ProdRndTeamCode != null and lv1ProdRndTeamCode!=""'>
            AND lv1_prod_rnd_team_code = #{lv1ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='lv2ProdRndTeamCode != null and lv2ProdRndTeamCode!=""'>
            AND lv2_prod_rnd_team_code = #{lv2ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='lv3ProdRndTeamCode != null and lv3ProdRndTeamCode!=""'>
            AND lv3_prod_rnd_team_code = #{lv3ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='lv4ProdRndTeamCode != null and lv4ProdRndTeamCode!=""'>
            AND lv4_prod_rnd_team_code = #{lv4ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='l1Name != null and l1Name!=""'>
            AND l1_name = #{l1Name,jdbcType=VARCHAR}
        </if>
        <if test='l2Name != null and l2Name!=""'>
            AND l2_name = #{l2Name,jdbcType=VARCHAR}
        </if>
        <if test='caliberFlag != null and caliberFlag!=""'>
            AND caliber_Flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='topL3CegCode != null and topL3CegCode!=""'>
            AND top_l3_ceg_code = #{topL3CegCode,jdbcType=VARCHAR}
        </if>
        <if test='topL4CegCode != null and topL4CegCode!=""'>
            AND top_l4_ceg_code = #{topL4CegCode,jdbcType=VARCHAR}
        </if>
        <if test='topCategoryCode != null and topCategoryCode!=""'>
            AND top_category_code = #{topCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='topShippingObjectCode != null and topShippingObjectCode!=""'>
            AND top_shipping_object_code = #{topShippingObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='topManufactureObjectCode != null and  topManufactureObjectCode!=""'>
            AND top_manufacture_object_code = #{topManufactureObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null and versionId!=""'>
            AND version_id=#{versionId,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag != null and overseaFlag!=""'>
            AND oversea_flag=#{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='lv0ProdListCode != null and lv0ProdListCode!=""'>
            AND lv0_prod_list_code=#{lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionCode != null and dimensionCode!=""'>
            AND dimension_code=#{dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubCategoryCode != null and dimensionSubCategoryCode!=""'>
            AND dimension_subCategory_code=#{dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubDetailCode != null and dimensionSubDetailCode!=""'>
            AND dimension_sub_detail_code=#{dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
        <if test='spartCode != null and spartCode!=""'>
            AND spart_code=#{spartCode,jdbcType=VARCHAR}
        </if>
        <if test='coaCode != null and coaCode!=""'>
            AND coa_code=#{coaCode,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag!=""'>
            AND view_flag=#{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='lastUpdatedBy != null'>
            last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
        </if>
        <if test='creationDate != null'>
            AND creation_date=#{creationDate,jdbcType=TIMESTAMP}
        </if>

        <if test='createdBy != null'>
            created_by = #{createdBy,jdbcType=VARCHAR},
        </if>
        <if test='lastUpdateDate != null'>
            AND last_update_date=#{lastUpdateDate,jdbcType=TIMESTAMP}
        </if>
    </sql>

    <sql id="searchFields">
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='dmFocTopCateInfoDTO.delFlag != null'>
                AND del_flag = #{dmFocTopCateInfoDTO.delFlag,jdbcType=VARCHAR}
            </if>
            <if test='dmFocTopCateInfoDTO.lv0ProdRndTeamCode != null and dmFocTopCateInfoDTO.lv0ProdRndTeamCode!=""'>
                AND lv0_prod_rnd_team_code = #{dmFocTopCateInfoDTO.lv0ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='dmFocTopCateInfoDTO.lv1ProdRndTeamCode != null and dmFocTopCateInfoDTO.lv1ProdRndTeamCode!=""'>
                AND lv1_prod_rnd_team_code = #{dmFocTopCateInfoDTO.lv1ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='dmFocTopCateInfoDTO.lv2ProdRndTeamCode != null and dmFocTopCateInfoDTO.lv2ProdRndTeamCode!=""'>
                AND lv2_prod_rnd_team_code = #{dmFocTopCateInfoDTO.lv2ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='dmFocTopCateInfoDTO.lv3ProdRndTeamCode != null and dmFocTopCateInfoDTO.lv3ProdRndTeamCode!=""'>
                AND lv3_prod_rnd_team_code = #{dmFocTopCateInfoDTO.lv3ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='dmFocTopCateInfoDTO.lv4ProdRndTeamCode != null and dmFocTopCateInfoDTO.lv4ProdRndTeamCode!=""'>
                AND lv4_prod_rnd_team_code = #{dmFocTopCateInfoDTO.lv4ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='dmFocTopCateInfoDTO.l1Name != null and dmFocTopCateInfoDTO.l1Name!=""'>
                AND l1_name = #{dmFocTopCateInfoDTO.l1Name,jdbcType=VARCHAR}
            </if>
            <if test='dmFocTopCateInfoDTO.l2Name != null and dmFocTopCateInfoDTO.l2Name!=""'>
                AND l2_name = #{dmFocTopCateInfoDTO.l2Name,jdbcType=VARCHAR}
            </if>
            <if test='dmFocTopCateInfoDTO.caliberFlag != null and dmFocTopCateInfoDTO.caliberFlag!=""'>
                AND caliber_Flag = #{dmFocTopCateInfoDTO.caliberFlag,jdbcType=VARCHAR}
            </if>
            <if test='dmFocTopCateInfoDTO.topL3CegCode != null and dmFocTopCateInfoDTO.topL3CegCode!=""'>
                AND top_l3_ceg_code = #{dmFocTopCateInfoDTO.topL3CegCode,jdbcType=VARCHAR}
            </if>
            <if test='dmFocTopCateInfoDTO.topL4CegCode != null and dmFocTopCateInfoDTO.topL4CegCode!=""'>
                AND top_l4_ceg_code = #{dmFocTopCateInfoDTO.topL4CegCode,jdbcType=VARCHAR}
            </if>
            <if test='dmFocTopCateInfoDTO.topCategoryCode != null and dmFocTopCateInfoDTO.topCategoryCode!=""'>
                AND top_category_code = #{dmFocTopCateInfoDTO.topCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test='dmFocTopCateInfoDTO.topShippingObjectCode != null and dmFocTopCateInfoDTO.topShippingObjectCode!=""'>
                AND top_shipping_object_code = #{dmFocTopCateInfoDTO.topShippingObjectCode,jdbcType=VARCHAR}
            </if>
            <if test='dmFocTopCateInfoDTO.topManufactureObjectCode != null and dmFocTopCateInfoDTO.topManufactureObjectCode!=""'>
                AND top_manufacture_object_code = #{dmFocTopCateInfoDTO.topManufactureObjectCode,jdbcType=VARCHAR}
            </if>
            <if test='dmFocTopCateInfoDTO.versionId != null and dmFocTopCateInfoDTO.versionId!=""'>
                AND version_id=#{dmFocTopCateInfoDTO.versionId,jdbcType=VARCHAR}
            </if>
            <if test='dmFocTopCateInfoDTO.overseaFlag != null and dmFocTopCateInfoDTO.overseaFlag!=""'>
                AND oversea_flag=#{dmFocTopCateInfoDTO.overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='dmFocTopCateInfoDTO.lv0ProdListCode != null and dmFocTopCateInfoDTO.lv0ProdListCode!=""'>
                AND lv0_prod_list_code=#{dmFocTopCateInfoDTO.lv0ProdListCode,jdbcType=VARCHAR}
            </if>
            <if test='dmFocTopCateInfoDTO.dimensionCode != null and dmFocTopCateInfoDTO.dimensionCode!=""'>
                AND dimension_code=#{dmFocTopCateInfoDTO.dimensionCode,jdbcType=VARCHAR}
            </if>
            <if test='dmFocTopCateInfoDTO.dimensionSubCategoryCode != null and dmFocTopCateInfoDTO.dimensionSubCategoryCode!=""'>
                AND dimension_subCategory_code=#{dmFocTopCateInfoDTO.dimensionSubCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test='dmFocTopCateInfoDTO.dimensionSubDetailCode != null and dmFocTopCateInfoDTO.dimensionSubDetailCode!=""'>
                AND dimension_sub_detail_code=#{dmFocTopCateInfoDTO.dimensionSubDetailCode,jdbcType=VARCHAR}
            </if>
            <if test='dmFocTopCateInfoDTO.spartCode != null and dmFocTopCateInfoDTO.spartCode!=""'>
                AND spart_code=#{dmFocTopCateInfoDTO.spartCode,jdbcType=VARCHAR}
            </if>
            <if test='dmFocTopCateInfoDTO.coaCode != null and dmFocTopCateInfoDTO.coaCode!=""'>
                AND coa_code=#{dmFocTopCateInfoDTO.coaCode,jdbcType=VARCHAR}
            </if>
            <if test='dmFocTopCateInfoDTO.viewFlag != null and dmFocTopCateInfoDTO.viewFlag!=""'>
                AND view_flag=#{dmFocTopCateInfoDTO.viewFlag,jdbcType=VARCHAR}
            </if>
            <if test='dmFocTopCateInfoDTO.lastUpdatedBy != null'>
                AND last_updated_by LIKE CONCAT(CONCAT('%', #{dmFocTopCateInfoDTO.lastUpdatedBy,jdbcType=VARCHAR}) ,'%')
            </if>
            <if test='dmFocTopCateInfoDTO.creationDate != null'>
                AND creation_date=#{dmFocTopCateInfoDTO.creationDate,jdbcType=TIMESTAMP}
            </if>
            <if test='dmFocTopCateInfoDTO.createdBy != null'>
                AND created_by LIKE CONCAT(CONCAT('%', #{dmFocTopCateInfoDTO.createdBy,jdbcType=VARCHAR}) ,'%')
            </if>
            <if test='dmFocTopCateInfoDTO.lastUpdateDate != null'>
                AND last_update_date=#{dmFocTopCateInfoDTO.lastUpdateDate,jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <select id="findCateByPage" resultMap="resultMap">
        SELECT d1.*,d2.is_top_flag FROM (
        SELECT
        lv0_prod_rd_team_cn_name,
        lv0_prod_rnd_team_code,
        lv1_prod_rd_team_cn_name,
        lv1_prod_rnd_team_code,
        lv2_prod_rd_team_cn_name,
        lv2_prod_rnd_team_code,
        <if test='dmFocTopCateInfoDTO.granularityType != "P"' >
            lv3_prod_rd_team_cn_name,
            lv3_prod_rnd_team_code,
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType != "P" and dmFocTopCateInfoDTO.industryOrg == "IAS"' >
            lv4_prod_rd_team_cn_name,
            lv4_prod_rnd_team_code,
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType == "P"' >
            l1_name,
            l2_name,
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType == "D"' >
            dimension_cn_name, dimension_code,
            dimension_subCategory_cn_name, dimension_subCategory_code,
            dimension_sub_detail_cn_name, dimension_sub_detail_code,
            spart_code,spart_cn_name,
            <if test='dmFocTopCateInfoDTO.industryOrg == "ENERGY"' >
                coa_code,coa_cn_name,
            </if>
        </if>
        top_l3_ceg_cn_name,top_l3_ceg_short_cn_name,
        top_l4_ceg_cn_name,top_l4_ceg_short_cn_name,
        top_category_cn_name,top_category_code,
        SUM ( CASE WHEN period_year = #{dmFocTopCateInfoDTO.periodYear0} THEN  weight_rate END ) AS weight0,
        SUM ( CASE WHEN period_year = #{dmFocTopCateInfoDTO.periodYear1} THEN  weight_rate END ) AS weight1,
        SUM ( CASE WHEN period_year = #{dmFocTopCateInfoDTO.periodYear2} THEN  weight_rate END ) AS weight2,
        SUM ( CASE WHEN period_year = #{dmFocTopCateInfoDTO.periodYear3} THEN  weight_rate END ) AS weight3,
        SUM ( CASE WHEN period_year = #{dmFocTopCateInfoDTO.periodYear4} THEN  weight_rate END ) AS weight4
        <if test='dmFocTopCateInfoDTO.granularityType == "U"'>
            FROM fin_dm_opt_foi.${dmFocTopCateInfoDTO.tablePreFix}_top_cate_info_t
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType == "P"'>
            FROM fin_dm_opt_foi.${dmFocTopCateInfoDTO.tablePreFix}_pft_top_cate_info_t
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType == "D"'>
            FROM fin_dm_opt_foi.${dmFocTopCateInfoDTO.tablePreFix}_dms_top_cate_info_t
        </if>
        <include refid="searchFields"/>
        GROUP BY
        lv0_prod_rd_team_cn_name,
        lv0_prod_rnd_team_code,
        lv1_prod_rd_team_cn_name,
        lv1_prod_rnd_team_code,
        lv2_prod_rd_team_cn_name,
        lv2_prod_rnd_team_code,
        <if test='dmFocTopCateInfoDTO.granularityType != "P"'>
            lv3_prod_rd_team_cn_name,
            lv3_prod_rnd_team_code,
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType != "P" and dmFocTopCateInfoDTO.industryOrg == "IAS"' >
            lv4_prod_rd_team_cn_name,
            lv4_prod_rnd_team_code,
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType == "P"'>
            l1_name,
            l2_name,
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType == "D"'>
            dimension_cn_name, dimension_code,
            dimension_subCategory_cn_name, dimension_subCategory_code,
            dimension_sub_detail_cn_name, dimension_sub_detail_code,
            spart_code,spart_cn_name,
            <if test='dmFocTopCateInfoDTO.industryOrg == "ENERGY"' >
                coa_code,coa_cn_name,
            </if>
        </if>
        top_l3_ceg_cn_name,
        top_l3_ceg_short_cn_name,
        top_l4_ceg_cn_name,
        top_l4_ceg_short_cn_name,
        top_category_cn_name,
        top_category_code) d1
        LEFT JOIN (
        SELECT DISTINCT
        <if test='dmFocTopCateInfoDTO.viewFlag == "0"' >
            lv0_prod_rnd_team_code,
        </if>
        <if test='dmFocTopCateInfoDTO.viewFlag == "1" and dmFocTopCateInfoDTO.granularityType != "D"' >
            lv1_prod_rnd_team_code,
        </if>
        <if test='dmFocTopCateInfoDTO.viewFlag == "2" and dmFocTopCateInfoDTO.granularityType != "D"' >
            lv2_prod_rnd_team_code,
        </if>
        <choose>
            <when test='dmFocTopCateInfoDTO.granularityType == "U"'>
                <if test='dmFocTopCateInfoDTO.viewFlag == "3"' >
                    lv3_prod_rnd_team_code,
                </if>
                <if test='dmFocTopCateInfoDTO.viewFlag == "4" or dmFocTopCateInfoDTO.viewFlag == "5" or dmFocTopCateInfoDTO.viewFlag == "6"' >
                    lv2_prod_rnd_team_code,lv3_prod_rnd_team_code,top_l4_ceg_cn_name,
                    <if test='dmFocTopCateInfoDTO.industryOrg == "IAS"'>
                        lv4_prod_rnd_team_code,
                    </if>
                </if>
                <if test='dmFocTopCateInfoDTO.viewFlag == "7"'>
                    lv3_prod_rnd_team_code,lv4_prod_rnd_team_code,top_l4_ceg_cn_name,
                </if>
            </when>
            <when test='dmFocTopCateInfoDTO.granularityType == "P"'>
                <if test='dmFocTopCateInfoDTO.viewFlag == "3"' >
                    l1_name, lv2_prod_rnd_team_code,
                </if>
                <if test='dmFocTopCateInfoDTO.viewFlag == "4"' >
                    l2_name,
                    l1_name, lv2_prod_rnd_team_code,
                </if>
            </when>
            <otherwise>
                lv1_prod_rnd_team_code,
                lv2_prod_rnd_team_code,
                lv3_prod_rnd_team_code,
                <if test='dmFocTopCateInfoDTO.industryOrg == "IAS"' >
                    lv4_prod_rnd_team_code,
                </if>
                dimension_code,
                dimension_subCategory_code,
                dimension_sub_detail_code,
                spart_code,
                <if test='dmFocTopCateInfoDTO.industryOrg == "ENERGY"' >
                    coa_code,
                </if>
            </otherwise>
        </choose>
        top_category_code,
        is_top_flag
        <if test='dmFocTopCateInfoDTO.granularityType == "U"'>
            FROM fin_dm_opt_foi.${dmFocTopCateInfoDTO.tablePreFix}_top_cate_info_t
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType == "P"'>
            FROM fin_dm_opt_foi.${dmFocTopCateInfoDTO.tablePreFix}_pft_top_cate_info_t
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType == "D"'>
            FROM fin_dm_opt_foi.${dmFocTopCateInfoDTO.tablePreFix}_dms_top_cate_info_t
        </if>
        WHERE del_flag = 'N'
        AND double_flag = 'Y'
        AND is_top_flag = 'Y'
        <if test='dmFocTopCateInfoDTO.lv0ProdRndTeamCode != null and dmFocTopCateInfoDTO.lv0ProdRndTeamCode!=""'>
            AND lv0_prod_rnd_team_code = #{dmFocTopCateInfoDTO.lv0ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.lv1ProdRndTeamCode != null and dmFocTopCateInfoDTO.lv1ProdRndTeamCode!=""'>
            AND lv1_prod_rnd_team_code = #{dmFocTopCateInfoDTO.lv1ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.lv2ProdRndTeamCode != null and dmFocTopCateInfoDTO.lv2ProdRndTeamCode!=""'>
            AND lv2_prod_rnd_team_code = #{dmFocTopCateInfoDTO.lv2ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.lv3ProdRndTeamCode != null and dmFocTopCateInfoDTO.lv3ProdRndTeamCode!=""'>
            AND lv3_prod_rnd_team_code = #{dmFocTopCateInfoDTO.lv3ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.lv4ProdRndTeamCode != null and dmFocTopCateInfoDTO.lv4ProdRndTeamCode!=""'>
            AND lv4_prod_rnd_team_code = #{dmFocTopCateInfoDTO.lv4ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.l1Name != null and dmFocTopCateInfoDTO.l1Name!=""'>
            AND l1_name = #{dmFocTopCateInfoDTO.l1Name,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.l2Name != null and dmFocTopCateInfoDTO.l2Name!=""'>
            AND l2_name = #{dmFocTopCateInfoDTO.l2Name,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.caliberFlag != null and dmFocTopCateInfoDTO.caliberFlag!=""'>
            AND caliber_Flag = #{dmFocTopCateInfoDTO.caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.topL3CegCode != null and dmFocTopCateInfoDTO.topL3CegCode!=""'>
            AND top_l3_ceg_code = #{dmFocTopCateInfoDTO.topL3CegCode,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.topL4CegCode != null and dmFocTopCateInfoDTO.topL4CegCode!=""'>
            AND top_l4_ceg_code = #{dmFocTopCateInfoDTO.topL4CegCode,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.topCategoryCode != null and dmFocTopCateInfoDTO.topCategoryCode!=""'>
            AND top_category_code = #{dmFocTopCateInfoDTO.topCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.versionId != null and dmFocTopCateInfoDTO.versionId!=""'>
            AND version_id = #{dmFocTopCateInfoDTO.versionId,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.overseaFlag != null and dmFocTopCateInfoDTO.overseaFlag!=""'>
            AND oversea_flag=#{dmFocTopCateInfoDTO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.lv0ProdListCode != null and dmFocTopCateInfoDTO.lv0ProdListCode!=""'>
            AND lv0_prod_list_code=#{dmFocTopCateInfoDTO.lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.dimensionCode != null and dmFocTopCateInfoDTO.dimensionCode!=""'>
            AND dimension_code=#{dmFocTopCateInfoDTO.dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.dimensionSubCategoryCode != null and dmFocTopCateInfoDTO.dimensionSubCategoryCode!=""'>
            AND dimension_subCategory_code=#{dmFocTopCateInfoDTO.dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.dimensionSubDetailCode != null and dmFocTopCateInfoDTO.dimensionSubDetailCode!=""'>
            AND dimension_sub_detail_code=#{dmFocTopCateInfoDTO.dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.spartCode != null and dmFocTopCateInfoDTO.spartCode!=""'>
            AND spart_code=#{dmFocTopCateInfoDTO.spartCode,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.coaCode != null and dmFocTopCateInfoDTO.coaCode!=""'>
            AND coa_code=#{dmFocTopCateInfoDTO.coaCode,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.viewFlag != null and dmFocTopCateInfoDTO.viewFlag!=""'>
            AND view_flag = #{dmFocTopCateInfoDTO.viewFlag,jdbcType=VARCHAR}
        </if>
        ) d2 on
        d1.top_category_code = d2.top_category_code
        <if test='dmFocTopCateInfoDTO.viewFlag == "0"' >
            and d1.lv0_prod_rnd_team_code = d2.lv0_prod_rnd_team_code
        </if>
        <if test='dmFocTopCateInfoDTO.viewFlag == "1" and dmFocTopCateInfoDTO.granularityType != "D"' >
            and d1.lv1_prod_rnd_team_code = d2.lv1_prod_rnd_team_code
        </if>
        <if test='dmFocTopCateInfoDTO.viewFlag == "2" and dmFocTopCateInfoDTO.granularityType != "D"' >
            and d1.lv2_prod_rnd_team_code = d2.lv2_prod_rnd_team_code
        </if>
        <choose>
            <when test='dmFocTopCateInfoDTO.granularityType == "U"'>
                <if test='dmFocTopCateInfoDTO.viewFlag == "3"' >
                    and d1.lv3_prod_rnd_team_code = d2.lv3_prod_rnd_team_code
                </if>
                <if test='dmFocTopCateInfoDTO.viewFlag == "4" or dmFocTopCateInfoDTO.viewFlag == "5" or dmFocTopCateInfoDTO.viewFlag == "6"' >
                    and d1.lv3_prod_rnd_team_code = d2.lv3_prod_rnd_team_code and d1.top_l4_ceg_cn_name = d2.top_l4_ceg_cn_name
                    <if test='dmFocTopCateInfoDTO.industryOrg == "IAS"'>
                        and d1.lv4_prod_rnd_team_code = d2.lv4_prod_rnd_team_code
                    </if>
                </if>
                <if test='dmFocTopCateInfoDTO.viewFlag == "7"'>
                    and d1.lv3_prod_rnd_team_code = d2.lv3_prod_rnd_team_code
                    and d1.lv4_prod_rnd_team_code = d2.lv4_prod_rnd_team_code
                    and d1.top_l4_ceg_cn_name = d2.top_l4_ceg_cn_name
                </if>
            </when>
            <when test='dmFocTopCateInfoDTO.granularityType == "P"'>
                <if test='dmFocTopCateInfoDTO.viewFlag == "3"' >
                    and d1.l1_name = d2.l1_name and d1.lv2_prod_rnd_team_code = d2.lv2_prod_rnd_team_code
                </if>
                <if test='dmFocTopCateInfoDTO.viewFlag == "4"' >
                    and d1.l2_name = d2.l2_name
                    and d1.l1_name = d2.l1_name and d1.lv2_prod_rnd_team_code = d2.lv2_prod_rnd_team_code
                </if>
            </when>
            <otherwise>
                and nvl(d1.lv1_prod_rnd_team_code,'snull') = nvl(d2.lv1_prod_rnd_team_code,'snull')
                AND nvl(d1.lv2_prod_rnd_team_code,'snull') = nvl(d2.lv2_prod_rnd_team_code,'snull')
                AND nvl(d1.lv3_prod_rnd_team_code,'snull') = nvl(d2.lv3_prod_rnd_team_code,'snull')
                <if test='dmFocTopCateInfoDTO.industryOrg == "IAS"'>
                AND nvl(d1.lv4_prod_rnd_team_code,'snull') = nvl(d2.lv4_prod_rnd_team_code,'snull')
                </if>
                <if test='dmFocTopCateInfoDTO.viewFlag == "0" or dmFocTopCateInfoDTO.viewFlag == "3" or dmFocTopCateInfoDTO.viewFlag == "6"' >
                    and d1.dimension_code = d2.dimension_code
                </if>
                <if test='dmFocTopCateInfoDTO.viewFlag == "1" or dmFocTopCateInfoDTO.viewFlag == "4" or dmFocTopCateInfoDTO.viewFlag == "7"' >
                    and d1.dimension_code = d2.dimension_code and d1.dimension_subCategory_code = d2.dimension_subCategory_code
                </if>
                <if test='dmFocTopCateInfoDTO.viewFlag == "2" or dmFocTopCateInfoDTO.viewFlag == "5" or dmFocTopCateInfoDTO.viewFlag == "8"' >
                    and d1.dimension_code = d2.dimension_code
                    and d1.dimension_subCategory_code = d2.dimension_subCategory_code
                    and d1.dimension_sub_detail_code = d2.dimension_sub_detail_code
                </if>
                <if test='dmFocTopCateInfoDTO.viewFlag == "9" or dmFocTopCateInfoDTO.viewFlag == "10" or dmFocTopCateInfoDTO.viewFlag == "11"' >
                    and d1.dimension_code = d2.dimension_code
                    and d1.dimension_subCategory_code = d2.dimension_subCategory_code
                    and d1.dimension_sub_detail_code = d2.dimension_sub_detail_code
                    and d1.spart_code = d2.spart_code
                </if>
                <if test='dmFocTopCateInfoDTO.viewFlag == "12"' >
                    and d1.dimension_code = d2.dimension_code
                    and d1.dimension_subCategory_code = d2.dimension_subCategory_code
                    and d1.dimension_sub_detail_code = d2.dimension_sub_detail_code
                    and d1.spart_code = d2.spart_code
                    <if test='dmFocTopCateInfoDTO.industryOrg == "ENERGY"' >
                        and d1.coa_code = d2.coa_code
                    </if>
                </if>
            </otherwise>
        </choose>
        ORDER BY if(isnull(d1.weight4),1,0),d1.weight4 DESC
        LIMIT #{pageVO.pageSize} OFFSET #{pageVO.startIndex} - 1
    </select>

    <select id="findCateByPageCount" resultType="int">
        SELECT COUNT(1) from
        (
        SELECT
        lv0_prod_rd_team_cn_name,
        lv1_prod_rd_team_cn_name,
        lv2_prod_rd_team_cn_name,
        <if test='dmFocTopCateInfoDTO.granularityType != "P" and dmFocTopCateInfoDTO.industryOrg == "IAS"' >
            lv4_prod_rd_team_cn_name,
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType != "P"'>
            lv3_prod_rd_team_cn_name,
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType == "P"'>
            l1_name, l2_name,
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType == "D"'>
            dimension_cn_name, dimension_code,
            dimension_subCategory_cn_name, dimension_subCategory_code,
            dimension_sub_detail_cn_name, dimension_sub_detail_code,
            spart_code,spart_cn_name,
            <if test='dmFocTopCateInfoDTO.industryOrg == "ENERGY"' >
                coa_code,coa_cn_name,
            </if>
        </if>
        top_l3_ceg_cn_name,
        top_l3_ceg_short_cn_name,
        top_l4_ceg_cn_name,
        top_l4_ceg_short_cn_name,
        top_category_cn_name,
        top_category_code,
        SUM ( CASE WHEN period_year = #{dmFocTopCateInfoDTO.periodYear0} THEN  weight_rate END ) AS weight0,
        SUM ( CASE WHEN period_year = #{dmFocTopCateInfoDTO.periodYear1} THEN  weight_rate END ) AS weight1,
        SUM ( CASE WHEN period_year = #{dmFocTopCateInfoDTO.periodYear2} THEN  weight_rate END ) AS weight2,
        SUM ( CASE WHEN period_year = #{dmFocTopCateInfoDTO.periodYear3} THEN  weight_rate END ) AS weight3,
        SUM ( CASE WHEN period_year = #{dmFocTopCateInfoDTO.periodYear4} THEN  weight_rate END ) AS weight4
        <if test='dmFocTopCateInfoDTO.granularityType == "U"'>
            FROM fin_dm_opt_foi.${dmFocTopCateInfoDTO.tablePreFix}_top_cate_info_t
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType == "P"'>
            FROM fin_dm_opt_foi.${dmFocTopCateInfoDTO.tablePreFix}_pft_top_cate_info_t
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType == "D"'>
            FROM fin_dm_opt_foi.${dmFocTopCateInfoDTO.tablePreFix}_dms_top_cate_info_t
        </if>
        <include refid="searchFields"/>
        GROUP BY
        lv0_prod_rd_team_cn_name,
        lv1_prod_rd_team_cn_name,
        lv2_prod_rd_team_cn_name,
        <if test='dmFocTopCateInfoDTO.granularityType != "P"'>
            lv3_prod_rd_team_cn_name,
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType != "P" and dmFocTopCateInfoDTO.industryOrg == "IAS"'>
            lv4_prod_rd_team_cn_name,
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType == "P"'>
            l1_name, l2_name,
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType == "D"'>
            dimension_cn_name, dimension_code,
            dimension_subCategory_cn_name, dimension_subCategory_code,
            dimension_sub_detail_cn_name, dimension_sub_detail_code,
            spart_code,spart_cn_name,
            <if test='dmFocTopCateInfoDTO.industryOrg == "ENERGY"' >
                coa_code,coa_cn_name,
            </if>
        </if>
        top_l3_ceg_cn_name,
        top_l3_ceg_short_cn_name,
        top_l4_ceg_cn_name,
        top_l4_ceg_short_cn_name,
        top_category_cn_name,
        top_category_code
        )${dmFocTopCateInfoDTO.tablePreFix}_top_cate_info_t
    </select>

    <select id="findManufactureByPage" resultMap="resultMap">
        SELECT d1.*,d2.is_top_flag FROM (
        SELECT
        lv0_prod_rd_team_cn_name,
        lv0_prod_rnd_team_code,
        lv1_prod_rd_team_cn_name,
        lv1_prod_rnd_team_code,
        lv2_prod_rd_team_cn_name,
        lv2_prod_rnd_team_code,
        <if test='dmFocTopCateInfoDTO.granularityType != "P"' >
            lv3_prod_rd_team_cn_name,
            lv3_prod_rnd_team_code,
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType != "P" and dmFocTopCateInfoDTO.industryOrg == "IAS"' >
            lv4_prod_rd_team_cn_name,
            lv4_prod_rnd_team_code,
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType == "P"' >
            l1_name,
            l2_name,
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType == "D"' >
            dimension_cn_name, dimension_code,
            dimension_subCategory_cn_name, dimension_subCategory_code,
            dimension_sub_detail_cn_name, dimension_sub_detail_code,
            spart_code,spart_cn_name,
            <if test='dmFocTopCateInfoDTO.industryOrg == "ENERGY"' >
                coa_code,coa_cn_name,
            </if>
        </if>
        top_shipping_object_code,
        top_manufacture_object_code,
        top_shipping_object_cn_name,
        top_manufacture_object_cn_name,
        SUM ( CASE WHEN period_year = #{dmFocTopCateInfoDTO.periodYear0} THEN  weight_rate END ) AS weight0,
        SUM ( CASE WHEN period_year = #{dmFocTopCateInfoDTO.periodYear1} THEN  weight_rate END ) AS weight1,
        SUM ( CASE WHEN period_year = #{dmFocTopCateInfoDTO.periodYear2} THEN  weight_rate END ) AS weight2,
        SUM ( CASE WHEN period_year = #{dmFocTopCateInfoDTO.periodYear3} THEN  weight_rate END ) AS weight3,
        SUM ( CASE WHEN period_year = #{dmFocTopCateInfoDTO.periodYear4} THEN  weight_rate END ) AS weight4
        <if test='dmFocTopCateInfoDTO.granularityType == "U"'>
            FROM fin_dm_opt_foi.${dmFocTopCateInfoDTO.tablePreFix}_top_made_info_t
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType == "P"'>
            FROM fin_dm_opt_foi.${dmFocTopCateInfoDTO.tablePreFix}_pft_top_made_info_t
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType == "D"'>
            FROM fin_dm_opt_foi.${dmFocTopCateInfoDTO.tablePreFix}_dms_top_made_info_t
        </if>
        <include refid="searchFields"/>
        GROUP BY
        lv0_prod_rd_team_cn_name,
        lv0_prod_rnd_team_code,
        lv1_prod_rd_team_cn_name,
        lv1_prod_rnd_team_code,
        lv2_prod_rd_team_cn_name,
        lv2_prod_rnd_team_code,
        <if test='dmFocTopCateInfoDTO.granularityType != "P"'>
            lv3_prod_rd_team_cn_name,
            lv3_prod_rnd_team_code,
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType != "P" and dmFocTopCateInfoDTO.industryOrg == "IAS"' >
            lv4_prod_rd_team_cn_name,
            lv4_prod_rnd_team_code,
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType == "P"'>
            l1_name,
            l2_name,
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType == "D"'>
            dimension_cn_name, dimension_code,
            dimension_subCategory_cn_name, dimension_subCategory_code,
            dimension_sub_detail_cn_name, dimension_sub_detail_code,
            spart_code,spart_cn_name,
            <if test='dmFocTopCateInfoDTO.industryOrg == "ENERGY"' >
                coa_code,coa_cn_name,
            </if>
        </if>
        top_shipping_object_code,
        top_manufacture_object_code,
        top_shipping_object_cn_name,
        top_manufacture_object_cn_name) d1
        LEFT JOIN (
        SELECT DISTINCT
        <if test='dmFocTopCateInfoDTO.viewFlag == "0"' >
            lv0_prod_rnd_team_code,
        </if>
        <if test='dmFocTopCateInfoDTO.viewFlag == "1" and dmFocTopCateInfoDTO.granularityType != "D"' >
            lv1_prod_rnd_team_code,
        </if>
        <if test='dmFocTopCateInfoDTO.viewFlag == "2" and dmFocTopCateInfoDTO.granularityType != "D"' >
            lv2_prod_rnd_team_code,
        </if>
        <choose>
            <when test='dmFocTopCateInfoDTO.granularityType == "U"'>
                <if test='dmFocTopCateInfoDTO.viewFlag == "3"' >
                    lv3_prod_rnd_team_code,
                </if>
                <if test='dmFocTopCateInfoDTO.viewFlag == "4" or dmFocTopCateInfoDTO.viewFlag == "5" or dmFocTopCateInfoDTO.viewFlag == "6"' >
                    lv2_prod_rnd_team_code,lv3_prod_rnd_team_code,
                    <if test='dmFocTopCateInfoDTO.industryOrg == "IAS"'>
                        lv4_prod_rnd_team_code,
                    </if>
                </if>
                <if test='dmFocTopCateInfoDTO.viewFlag == "7"' >
                    lv3_prod_rnd_team_code,lv4_prod_rnd_team_code,
                </if>
            </when>
            <when test='dmFocTopCateInfoDTO.granularityType == "P"'>
                <if test='dmFocTopCateInfoDTO.viewFlag == "3"' >
                    l1_name, lv2_prod_rnd_team_code,
                </if>
                <if test='dmFocTopCateInfoDTO.viewFlag == "4"' >
                    l2_name,
                    l1_name, lv2_prod_rnd_team_code,
                </if>
            </when>
            <otherwise>
                lv1_prod_rnd_team_code,
                lv2_prod_rnd_team_code,
                lv3_prod_rnd_team_code,
                <if test='dmFocTopCateInfoDTO.industryOrg == "IAS"' >
                    lv4_prod_rnd_team_code,
                </if>
                dimension_code,
                dimension_subCategory_code,
                dimension_sub_detail_code,
                spart_code,
                <if test='dmFocTopCateInfoDTO.industryOrg == "ENERGY"' >
                    coa_code,
                </if>
            </otherwise>
        </choose>
        top_shipping_object_code,
        top_manufacture_object_code,
        is_top_flag
        <if test='dmFocTopCateInfoDTO.granularityType == "U"'>
            FROM fin_dm_opt_foi.${dmFocTopCateInfoDTO.tablePreFix}_top_made_info_t
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType == "P"'>
            FROM fin_dm_opt_foi.${dmFocTopCateInfoDTO.tablePreFix}_pft_top_made_info_t
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType == "D"'>
            FROM fin_dm_opt_foi.${dmFocTopCateInfoDTO.tablePreFix}_dms_top_made_info_t
        </if>
        WHERE del_flag = 'N'
        AND double_flag = 'Y'
        AND is_top_flag = 'Y'
        <if test='dmFocTopCateInfoDTO.lv0ProdRndTeamCode != null and dmFocTopCateInfoDTO.lv0ProdRndTeamCode!=""'>
            AND lv0_prod_rnd_team_code = #{dmFocTopCateInfoDTO.lv0ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.lv1ProdRndTeamCode != null and dmFocTopCateInfoDTO.lv1ProdRndTeamCode!=""'>
            AND lv1_prod_rnd_team_code = #{dmFocTopCateInfoDTO.lv1ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.lv2ProdRndTeamCode != null and dmFocTopCateInfoDTO.lv2ProdRndTeamCode!=""'>
            AND lv2_prod_rnd_team_code = #{dmFocTopCateInfoDTO.lv2ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.lv3ProdRndTeamCode != null and dmFocTopCateInfoDTO.lv3ProdRndTeamCode!=""'>
            AND lv3_prod_rnd_team_code = #{dmFocTopCateInfoDTO.lv3ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.lv4ProdRndTeamCode != null and dmFocTopCateInfoDTO.lv4ProdRndTeamCode!=""'>
            AND lv4_prod_rnd_team_code = #{dmFocTopCateInfoDTO.lv4ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.l1Name != null and dmFocTopCateInfoDTO.l1Name!=""'>
            AND l1_name = #{dmFocTopCateInfoDTO.l1Name,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.l2Name != null and dmFocTopCateInfoDTO.l2Name!=""'>
            AND l2_name = #{dmFocTopCateInfoDTO.l2Name,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.caliberFlag != null and dmFocTopCateInfoDTO.caliberFlag!=""'>
            AND caliber_Flag = #{dmFocTopCateInfoDTO.caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.topShippingObjectCode != null and dmFocTopCateInfoDTO.topShippingObjectCode!=""'>
            AND top_shipping_object_code = #{dmFocTopCateInfoDTO.topShippingObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.topManufactureObjectCode != null and dmFocTopCateInfoDTO.topManufactureObjectCode!=""'>
            AND top_manufacture_object_code = #{dmFocTopCateInfoDTO.topManufactureObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.versionId != null and dmFocTopCateInfoDTO.versionId!=""'>
            AND version_id = #{dmFocTopCateInfoDTO.versionId,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.overseaFlag != null and dmFocTopCateInfoDTO.overseaFlag!=""'>
            AND oversea_flag=#{dmFocTopCateInfoDTO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.lv0ProdListCode != null and dmFocTopCateInfoDTO.lv0ProdListCode!=""'>
            AND lv0_prod_list_code=#{dmFocTopCateInfoDTO.lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.dimensionCode != null and dmFocTopCateInfoDTO.dimensionCode!=""'>
            AND dimension_code=#{dmFocTopCateInfoDTO.dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.dimensionSubCategoryCode != null and dmFocTopCateInfoDTO.dimensionSubCategoryCode!=""'>
            AND dimension_subCategory_code=#{dmFocTopCateInfoDTO.dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.dimensionSubDetailCode != null and dmFocTopCateInfoDTO.dimensionSubDetailCode!=""'>
            AND dimension_sub_detail_code=#{dmFocTopCateInfoDTO.dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.spartCode != null and dmFocTopCateInfoDTO.spartCode!=""'>
            AND spart_code=#{dmFocTopCateInfoDTO.spartCode,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.coaCode != null and dmFocTopCateInfoDTO.coaCode!=""'>
            AND coa_code=#{dmFocTopCateInfoDTO.coaCode,jdbcType=VARCHAR}
        </if>
        <if test='dmFocTopCateInfoDTO.viewFlag != null and dmFocTopCateInfoDTO.viewFlag!=""'>
            AND view_flag = #{dmFocTopCateInfoDTO.viewFlag,jdbcType=VARCHAR}
        </if>
        ) d2 on
        d1.top_shipping_object_code = d2.top_shipping_object_code
        and d1.top_manufacture_object_code = d2.top_manufacture_object_code
        <if test='dmFocTopCateInfoDTO.viewFlag == "0"' >
            and d1.lv0_prod_rnd_team_code = d2.lv0_prod_rnd_team_code
        </if>
        <if test='dmFocTopCateInfoDTO.viewFlag == "1" and dmFocTopCateInfoDTO.granularityType != "D"' >
            and d1.lv1_prod_rnd_team_code = d2.lv1_prod_rnd_team_code
        </if>
        <if test='dmFocTopCateInfoDTO.viewFlag == "2" and dmFocTopCateInfoDTO.granularityType != "D"' >
            and d1.lv2_prod_rnd_team_code = d2.lv2_prod_rnd_team_code
        </if>
        <choose>
            <when test='dmFocTopCateInfoDTO.granularityType == "U"'>
                <if test='dmFocTopCateInfoDTO.viewFlag == "3"' >
                    and d1.lv3_prod_rnd_team_code = d2.lv3_prod_rnd_team_code
                </if>
                <if test='dmFocTopCateInfoDTO.viewFlag == "4" or dmFocTopCateInfoDTO.viewFlag == "5" or dmFocTopCateInfoDTO.viewFlag == "6"' >
                    and d1.lv3_prod_rnd_team_code = d2.lv3_prod_rnd_team_code
                    <if test='dmFocTopCateInfoDTO.industryOrg == "IAS"'>
                        and d1.lv4_prod_rnd_team_code = d2.lv4_prod_rnd_team_code
                    </if>
                </if>
                <if test='dmFocTopCateInfoDTO.viewFlag == "7"' >
                    and d1.lv3_prod_rnd_team_code = d2.lv3_prod_rnd_team_code
                    and d1.lv4_prod_rnd_team_code = d2.lv4_prod_rnd_team_code
                </if>
            </when>
            <when test='dmFocTopCateInfoDTO.granularityType == "P"'>
                <if test='dmFocTopCateInfoDTO.viewFlag == "3"' >
                    and d1.l1_name = d2.l1_name and d1.lv2_prod_rnd_team_code = d2.lv2_prod_rnd_team_code
                </if>
                <if test='dmFocTopCateInfoDTO.viewFlag == "4"' >
                    and d1.l2_name = d2.l2_name
                    and d1.l1_name = d2.l1_name and d1.lv2_prod_rnd_team_code = d2.lv2_prod_rnd_team_code
                </if>
            </when>
            <otherwise>
                <if test='dmFocTopCateInfoDTO.viewFlag == "0" or dmFocTopCateInfoDTO.viewFlag == "1" or dmFocTopCateInfoDTO.viewFlag == "2"' >
                    and d1.lv1_prod_rnd_team_code = d2.lv1_prod_rnd_team_code
                </if>
                <if test='dmFocTopCateInfoDTO.viewFlag == "3" or dmFocTopCateInfoDTO.viewFlag == "4" or dmFocTopCateInfoDTO.viewFlag == "5"' >
                    and d1.lv2_prod_rnd_team_code = d2.lv2_prod_rnd_team_code
                </if>
                <if test='dmFocTopCateInfoDTO.viewFlag == "6" or dmFocTopCateInfoDTO.viewFlag == "7" or dmFocTopCateInfoDTO.viewFlag == "8" or dmFocTopCateInfoDTO.viewFlag == "11"' >
                    and d1.lv3_prod_rnd_team_code = d2.lv3_prod_rnd_team_code
                </if>
                <if test='dmFocTopCateInfoDTO.viewFlag == "12"'>
                    and d1.lv3_prod_rnd_team_code = d2.lv3_prod_rnd_team_code
                    <if test='dmFocTopCateInfoDTO.industryOrg == "IAS"'>
                        and d1.lv4_prod_rnd_team_code = d2.lv4_prod_rnd_team_code
                    </if>
                </if>
                <if test='dmFocTopCateInfoDTO.viewFlag == "0" or dmFocTopCateInfoDTO.viewFlag == "3" or dmFocTopCateInfoDTO.viewFlag == "6"' >
                    and d1.dimension_code = d2.dimension_code
                </if>
                <if test='dmFocTopCateInfoDTO.viewFlag == "1" or dmFocTopCateInfoDTO.viewFlag == "4" or dmFocTopCateInfoDTO.viewFlag == "7"' >
                    and d1.dimension_code = d2.dimension_code and d1.dimension_subCategory_code = d2.dimension_subCategory_code
                </if>
                <if test='dmFocTopCateInfoDTO.viewFlag == "2" or dmFocTopCateInfoDTO.viewFlag == "5" or dmFocTopCateInfoDTO.viewFlag == "8"' >
                    and d1.dimension_code = d2.dimension_code
                    and d1.dimension_subCategory_code = d2.dimension_subCategory_code
                    and d1.dimension_sub_detail_code = d2.dimension_sub_detail_code
                </if>
                <if test='dmFocTopCateInfoDTO.viewFlag == "9" or dmFocTopCateInfoDTO.viewFlag == "10" or dmFocTopCateInfoDTO.viewFlag == "11"' >
                    and d1.dimension_code = d2.dimension_code
                    and d1.dimension_subCategory_code = d2.dimension_subCategory_code
                    and d1.dimension_sub_detail_code = d2.dimension_sub_detail_code
                    and d1.spart_code = d2.spart_code
                </if>
                <if test='dmFocTopCateInfoDTO.viewFlag == "12"' >
                    and d1.dimension_code = d2.dimension_code
                    and d1.dimension_subCategory_code = d2.dimension_subCategory_code
                    and d1.dimension_sub_detail_code = d2.dimension_sub_detail_code
                    and d1.spart_code = d2.spart_code
                <if test='dmFocTopCateInfoDTO.industryOrg == "ENERGY"' >
                    and d1.coa_code = d2.coa_code
                </if>
                </if>
            </otherwise>
        </choose>
        ORDER BY if(isnull(d1.weight4),1,0),d1.weight4 DESC
        LIMIT #{pageVO.pageSize} OFFSET #{pageVO.startIndex} - 1
    </select>

    <select id="findManufactureByPageCount" resultType="int">
        SELECT COUNT(1) from
        (
        SELECT
        lv0_prod_rd_team_cn_name,
        lv1_prod_rd_team_cn_name,
        lv2_prod_rd_team_cn_name,
        <if test='dmFocTopCateInfoDTO.granularityType != "P"'>
            lv3_prod_rd_team_cn_name,
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType != "P" and dmFocTopCateInfoDTO.industryOrg == "IAS"'>
            lv4_prod_rd_team_cn_name,
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType == "P"'>
            l1_name, l2_name,
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType == "D"'>
            dimension_cn_name, dimension_code,
            dimension_subCategory_cn_name, dimension_subCategory_code,
            dimension_sub_detail_cn_name, dimension_sub_detail_code,
            spart_code,spart_cn_name,
            <if test='dmFocTopCateInfoDTO.industryOrg == "ENERGY"' >
                coa_code,coa_cn_name,
            </if>
        </if>
        top_shipping_object_code,
        top_manufacture_object_code,
        top_shipping_object_cn_name,
        top_manufacture_object_cn_name,
        SUM ( CASE WHEN period_year = #{dmFocTopCateInfoDTO.periodYear0} THEN  weight_rate END ) AS weight0,
        SUM ( CASE WHEN period_year = #{dmFocTopCateInfoDTO.periodYear1} THEN  weight_rate END ) AS weight1,
        SUM ( CASE WHEN period_year = #{dmFocTopCateInfoDTO.periodYear2} THEN  weight_rate END ) AS weight2,
        SUM ( CASE WHEN period_year = #{dmFocTopCateInfoDTO.periodYear3} THEN  weight_rate END ) AS weight3,
        SUM ( CASE WHEN period_year = #{dmFocTopCateInfoDTO.periodYear4} THEN  weight_rate END ) AS weight4
        <if test='dmFocTopCateInfoDTO.granularityType == "U"'>
            FROM fin_dm_opt_foi.${dmFocTopCateInfoDTO.tablePreFix}_top_made_info_t
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType == "P"'>
            FROM fin_dm_opt_foi.${dmFocTopCateInfoDTO.tablePreFix}_pft_top_made_info_t
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType == "D"'>
            FROM fin_dm_opt_foi.${dmFocTopCateInfoDTO.tablePreFix}_dms_top_made_info_t
        </if>
        <include refid="searchFields"/>
        GROUP BY
        lv0_prod_rd_team_cn_name,
        lv1_prod_rd_team_cn_name,
        lv2_prod_rd_team_cn_name,
        <if test='dmFocTopCateInfoDTO.granularityType != "P"'>
            lv3_prod_rd_team_cn_name,
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType != "P" and dmFocTopCateInfoDTO.industryOrg == "IAS"' >
            lv4_prod_rd_team_cn_name,
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType == "P"'>
            l1_name, l2_name,
        </if>
        <if test='dmFocTopCateInfoDTO.granularityType == "D"'>
            dimension_cn_name, dimension_code,
            dimension_subCategory_cn_name, dimension_subCategory_code,
            dimension_sub_detail_cn_name, dimension_sub_detail_code,
            spart_code,spart_cn_name,
            <if test='dmFocTopCateInfoDTO.industryOrg == "ENERGY"' >
                coa_code,coa_cn_name,
            </if>
        </if>
        top_shipping_object_code,
        top_manufacture_object_code,
        top_shipping_object_cn_name,
        top_manufacture_object_cn_name
        )dm
    </select>

    <select id="findTopCateVOList" resultType="java.util.Map" fetchSize="10000">
        SELECT d1.*,CASE d2.is_top_flag
        WHEN 'Y' THEN '是' ELSE '否' END is_top_flag FROM (
        SELECT
        lv0_prod_rd_team_cn_name,
        lv0_prod_rnd_team_code,
        lv1_prod_rd_team_cn_name,
        lv1_prod_rnd_team_code,
        lv2_prod_rd_team_cn_name,
        lv2_prod_rnd_team_code,
        <if test='granularityType != "P"' >
            lv3_prod_rd_team_cn_name,
            lv3_prod_rnd_team_code,
        </if>
        <if test='granularityType != "P" and industryOrg == "IAS"' >
            lv4_prod_rd_team_cn_name,
            lv4_prod_rnd_team_code,
        </if>
        <if test='granularityType == "P"' >
            l1_name,
            l2_name,
        </if>
        <if test='granularityType == "D"' >
            dimension_cn_name, dimension_code,
            dimension_subCategory_cn_name, dimension_subCategory_code,
            dimension_sub_detail_cn_name, dimension_sub_detail_code,
            spart_code,spart_cn_name,
            <if test='industryOrg == "ENERGY"' >
                coa_code,coa_cn_name,
            </if>
        </if>
        top_l3_ceg_cn_name,top_l3_ceg_short_cn_name,
        top_l4_ceg_cn_name,top_l4_ceg_short_cn_name,
        top_category_cn_name,top_category_code,lv0_prod_list_cn_name,
        SUM ( CASE WHEN period_year = #{periodYear0} THEN  weight_rate END ) AS weight0,
        SUM ( CASE WHEN period_year = #{periodYear1} THEN  weight_rate END ) AS weight1,
        SUM ( CASE WHEN period_year = #{periodYear2} THEN  weight_rate END ) AS weight2,
        SUM ( CASE WHEN period_year = #{periodYear3} THEN  weight_rate END ) AS weight3,
        SUM ( CASE WHEN period_year = #{periodYear4} THEN  weight_rate END ) AS weight4
        <if test='granularityType == "U"'>
            FROM fin_dm_opt_foi.${tablePreFix}_top_cate_info_t
        </if>
        <if test='granularityType == "P"'>
            FROM fin_dm_opt_foi.${tablePreFix}_pft_top_cate_info_t
        </if>
        <if test='granularityType == "D"'>
            FROM fin_dm_opt_foi.${tablePreFix}_dms_top_cate_info_t
        </if>
        WHERE
        del_flag = 'N'
        <include refid="setValues"/>
        GROUP BY
        lv0_prod_rd_team_cn_name,
        lv0_prod_rnd_team_code,
        lv1_prod_rd_team_cn_name,
        lv1_prod_rnd_team_code,
        lv2_prod_rd_team_cn_name,
        lv2_prod_rnd_team_code,
        <if test='granularityType != "P"'>
            lv3_prod_rd_team_cn_name,
            lv3_prod_rnd_team_code,
        </if>
        <if test='granularityType != "P" and industryOrg == "IAS"' >
            lv4_prod_rd_team_cn_name,
            lv4_prod_rnd_team_code,
        </if>
        <if test='granularityType == "P"'>
            l1_name,
            l2_name,
        </if>
        <if test='granularityType == "D"'>
            dimension_cn_name, dimension_code,
            dimension_subCategory_cn_name, dimension_subCategory_code,
            dimension_sub_detail_cn_name, dimension_sub_detail_code,
            spart_code,spart_cn_name,
            <if test='industryOrg == "ENERGY"' >
                coa_code,coa_cn_name,
            </if>
        </if>
        top_l3_ceg_cn_name,
        top_l3_ceg_short_cn_name,
        top_l4_ceg_cn_name,
        top_l4_ceg_short_cn_name,
        top_category_cn_name,
        top_category_code,
        lv0_prod_list_cn_name) d1
        LEFT JOIN (
        SELECT DISTINCT
        <if test='viewFlag == "0"' >
            lv0_prod_rnd_team_code,
        </if>
        <if test='viewFlag == "1" and granularityType != "D"' >
            lv1_prod_rnd_team_code,
        </if>
        <if test='viewFlag == "2" and granularityType != "D"' >
            lv2_prod_rnd_team_code,
        </if>
        <choose>
            <when test='granularityType == "U"'>
                <if test='viewFlag == "3"' >
                    lv3_prod_rnd_team_code,
                </if>
                <if test='viewFlag == "4" or viewFlag == "5" or viewFlag == "6"' >
                    lv2_prod_rnd_team_code,lv3_prod_rnd_team_code,top_l4_ceg_cn_name,
                    <if test='industryOrg == "IAS"'>
                     lv4_prod_rnd_team_code,
                    </if>
                </if>
                <if test='viewFlag == "7"' >
                    lv3_prod_rnd_team_code,lv4_prod_rnd_team_code,top_l4_ceg_cn_name,
                </if>
            </when>
            <when test='granularityType == "P"'>
                <if test='viewFlag == "3"' >
                    l1_name, lv2_prod_rnd_team_code,
                </if>
                <if test='viewFlag == "4"' >
                    l2_name,
                    l1_name, lv2_prod_rnd_team_code,
                </if>
            </when>
            <otherwise>
                lv1_prod_rnd_team_code,
                lv2_prod_rnd_team_code,
                lv3_prod_rnd_team_code,
                <if test='industryOrg == "IAS"' >
                    lv4_prod_rnd_team_code,
                </if>
                dimension_code,
                dimension_subCategory_code,
                dimension_sub_detail_code,
                spart_code,
                <if test='industryOrg == "ENERGY"' >
                    coa_code,
                </if>
            </otherwise>
        </choose>
        top_category_code,
        is_top_flag
        <if test='granularityType == "U"'>
            FROM fin_dm_opt_foi.${tablePreFix}_top_cate_info_t
        </if>
        <if test='granularityType == "P"'>
            FROM fin_dm_opt_foi.${tablePreFix}_pft_top_cate_info_t
        </if>
        <if test='granularityType == "D"'>
            FROM fin_dm_opt_foi.${tablePreFix}_dms_top_cate_info_t
        </if>
        WHERE del_flag = 'N'
        AND double_flag = 'Y'
        AND is_top_flag = 'Y'
        <if test='lv0ProdRndTeamCode != null and lv0ProdRndTeamCode!=""'>
            AND lv0_prod_rnd_team_code = #{lv0ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='lv1ProdRndTeamCode != null and lv1ProdRndTeamCode!=""'>
            AND lv1_prod_rnd_team_code = #{lv1ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='lv2ProdRndTeamCode != null and lv2ProdRndTeamCode!=""'>
            AND lv2_prod_rnd_team_code = #{lv2ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='lv3ProdRndTeamCode != null and lv3ProdRndTeamCode!=""'>
            AND lv3_prod_rnd_team_code = #{lv3ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='lv4ProdRndTeamCode != null and lv4ProdRndTeamCode!=""'>
            AND lv4_prod_rnd_team_code = #{lv4ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='l1Name != null and l1Name!=""'>
            AND l1_name = #{l1Name,jdbcType=VARCHAR}
        </if>
        <if test='l2Name != null and l2Name!=""'>
            AND l2_name = #{l2Name,jdbcType=VARCHAR}
        </if>
        <if test='caliberFlag != null and caliberFlag!=""'>
            AND caliber_Flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='topL3CegCode != null and topL3CegCode!=""'>
            AND top_l3_ceg_code = #{topL3CegCode,jdbcType=VARCHAR}
        </if>
        <if test='topL4CegCode != null and topL4CegCode!=""'>
            AND top_l4_ceg_code = #{topL4CegCode,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null and versionId!=""'>
            AND version_id = #{versionId,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag != null and overseaFlag!=""'>
            AND oversea_flag=#{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='lv0ProdListCode != null and lv0ProdListCode!=""'>
            AND lv0_prod_list_code=#{lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionCode != null and dimensionCode!=""'>
            AND dimension_code=#{dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubCategoryCode != null and dimensionSubCategoryCode!=""'>
            AND dimension_subCategory_code=#{dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubDetailCode != null and dimensionSubDetailCode!=""'>
            AND dimension_sub_detail_code=#{dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
        <if test='spartCode != null and spartCode!=""'>
            AND spart_code=#{spartCode,jdbcType=VARCHAR}
        </if>
        <if test='coaCode != null and coaCode!=""'>
            AND coa_code=#{coaCode,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag!=""'>
            AND view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        ) d2 on
        d1.top_category_code = d2.top_category_code
        <if test='viewFlag == "0"' >
            and d1.lv0_prod_rnd_team_code = d2.lv0_prod_rnd_team_code
        </if>
        <if test='viewFlag == "1" and granularityType != "D"' >
            and d1.lv1_prod_rnd_team_code = d2.lv1_prod_rnd_team_code
        </if>
        <if test='viewFlag == "2" and granularityType != "D"' >
            and d1.lv2_prod_rnd_team_code = d2.lv2_prod_rnd_team_code
        </if>
        <choose>
            <when test='granularityType == "U"'>
                <if test='viewFlag == "3"' >
                    and d1.lv3_prod_rnd_team_code = d2.lv3_prod_rnd_team_code
                </if>
                <if test='viewFlag == "4" or viewFlag == "5" or viewFlag == "6"' >
                    and d1.lv3_prod_rnd_team_code = d2.lv3_prod_rnd_team_code and d1.top_l4_ceg_cn_name = d2.top_l4_ceg_cn_name
                    <if test='industryOrg == "IAS"'>
                        and d1.lv4_prod_rnd_team_code = d2.lv4_prod_rnd_team_code
                    </if>
                </if>
                <if test='viewFlag == "7"' >
                    and d1.lv3_prod_rnd_team_code = d2.lv3_prod_rnd_team_code
                    and d1.lv4_prod_rnd_team_code = d2.lv4_prod_rnd_team_code
                    and d1.top_l4_ceg_cn_name = d2.top_l4_ceg_cn_name
                </if>
            </when>
            <when test='granularityType == "P"'>
                <if test='viewFlag == "3"' >
                    and d1.l1_name = d2.l1_name and d1.lv2_prod_rnd_team_code = d2.lv2_prod_rnd_team_code
                </if>
                <if test='viewFlag == "4"' >
                    and d1.l2_name = d2.l2_name
                    and d1.l1_name = d2.l1_name and d1.lv2_prod_rnd_team_code = d2.lv2_prod_rnd_team_code
                </if>
            </when>
            <otherwise>
                and nvl(d1.lv1_prod_rnd_team_code,'snull') = nvl(d2.lv1_prod_rnd_team_code,'snull')
                AND nvl(d1.lv2_prod_rnd_team_code,'snull') = nvl(d2.lv2_prod_rnd_team_code,'snull')
                AND nvl(d1.lv3_prod_rnd_team_code,'snull') = nvl(d2.lv3_prod_rnd_team_code,'snull')
                <if test='industryOrg == "IAS"' >
                    AND nvl(d1.lv4_prod_rnd_team_code,'snull') = nvl(d2.lv4_prod_rnd_team_code,'snull')
                </if>
                <if test='viewFlag == "0" or viewFlag == "3" or viewFlag == "6"' >
                    and d1.dimension_code = d2.dimension_code
                </if>
                <if test='viewFlag == "1" or viewFlag == "4" or viewFlag == "7"' >
                    and d1.dimension_code = d2.dimension_code and d1.dimension_subCategory_code = d2.dimension_subCategory_code
                </if>
                <if test='viewFlag == "2" or viewFlag == "5" or viewFlag == "8"' >
                    and d1.dimension_code = d2.dimension_code
                    and d1.dimension_subCategory_code = d2.dimension_subCategory_code
                    and d1.dimension_sub_detail_code = d2.dimension_sub_detail_code
                </if>
                <if test='viewFlag == "9" or viewFlag == "10" or viewFlag == "11"' >
                    and d1.dimension_code = d2.dimension_code
                    and d1.dimension_subCategory_code = d2.dimension_subCategory_code
                    and d1.dimension_sub_detail_code = d2.dimension_sub_detail_code
                    and d1.spart_code = d2.spart_code
                </if>
                <if test='viewFlag == "12"' >
                    and d1.dimension_code = d2.dimension_code
                    and d1.dimension_subCategory_code = d2.dimension_subCategory_code
                    and d1.dimension_sub_detail_code = d2.dimension_sub_detail_code
                    and d1.spart_code = d2.spart_code
                    <if test='industryOrg == "ENERGY"' >
                        and d1.coa_code = d2.coa_code
                    </if>
                </if>
            </otherwise>
        </choose>
        ORDER BY if(isnull(d1.weight4),1,0),d1.weight4 DESC
    </select>

    <select id="findManufactureList" resultType="java.util.Map" fetchSize="10000">
        SELECT d1.*,CASE d2.is_top_flag
        WHEN 'Y' THEN '是' ELSE '否' END is_top_flag FROM (
        SELECT
        lv0_prod_rd_team_cn_name,
        lv0_prod_rnd_team_code,
        lv1_prod_rd_team_cn_name,
        lv1_prod_rnd_team_code,
        lv2_prod_rd_team_cn_name,
        lv2_prod_rnd_team_code,
        <if test='granularityType != "P"' >
            lv3_prod_rd_team_cn_name,
            lv3_prod_rnd_team_code,
        </if>
        <if test='granularityType != "P" and industryOrg == "IAS"' >
            lv4_prod_rd_team_cn_name,
            lv4_prod_rnd_team_code,
        </if>
        <if test='granularityType == "P"' >
            l1_name,
            l2_name,
        </if>
        <if test='granularityType == "D"' >
            dimension_cn_name, dimension_code,
            dimension_subCategory_cn_name, dimension_subCategory_code,
            dimension_sub_detail_cn_name, dimension_sub_detail_code,
            spart_code,spart_cn_name,
            <if test='industryOrg == "ENERGY"' >
                coa_code,coa_cn_name,
            </if>
        </if>
        top_shipping_object_code,
        top_manufacture_object_code,
        top_shipping_object_cn_name,
        top_manufacture_object_cn_name,lv0_prod_list_cn_name,
        SUM ( CASE WHEN period_year = #{periodYear0} THEN  weight_rate END ) AS weight0,
        SUM ( CASE WHEN period_year = #{periodYear1} THEN  weight_rate END ) AS weight1,
        SUM ( CASE WHEN period_year = #{periodYear2} THEN  weight_rate END ) AS weight2,
        SUM ( CASE WHEN period_year = #{periodYear3} THEN  weight_rate END ) AS weight3,
        SUM ( CASE WHEN period_year = #{periodYear4} THEN  weight_rate END ) AS weight4
        <if test='granularityType == "U"'>
            FROM fin_dm_opt_foi.${tablePreFix}_top_made_info_t
        </if>
        <if test='granularityType == "P"'>
            FROM fin_dm_opt_foi.${tablePreFix}_pft_top_made_info_t
        </if>
        <if test='granularityType == "D"'>
            FROM fin_dm_opt_foi.${tablePreFix}_dms_top_made_info_t
        </if>
        WHERE
        del_flag = 'N'
        <include refid="setValues"/>
        GROUP BY
        lv0_prod_rd_team_cn_name,
        lv0_prod_rnd_team_code,
        lv1_prod_rd_team_cn_name,
        lv1_prod_rnd_team_code,
        lv2_prod_rd_team_cn_name,
        lv2_prod_rnd_team_code,
        <if test='granularityType != "P"'>
            lv3_prod_rd_team_cn_name,
            lv3_prod_rnd_team_code,
        </if>
        <if test='granularityType != "P" and industryOrg == "IAS"' >
            lv4_prod_rd_team_cn_name,
            lv4_prod_rnd_team_code,
        </if>
        <if test='granularityType == "P"'>
            l1_name,
            l2_name,
        </if>
        <if test='granularityType == "D"'>
            dimension_cn_name, dimension_code,
            dimension_subCategory_cn_name, dimension_subCategory_code,
            dimension_sub_detail_cn_name, dimension_sub_detail_code,
            spart_code,spart_cn_name,
            <if test='industryOrg == "ENERGY"' >
                coa_code,coa_cn_name,
            </if>
        </if>
        top_shipping_object_code,
        top_manufacture_object_code,
        top_shipping_object_cn_name,
        top_manufacture_object_cn_name,
        lv0_prod_list_cn_name) d1
        LEFT JOIN (
        SELECT DISTINCT
        <if test='viewFlag == "0"' >
            lv0_prod_rnd_team_code,
        </if>
        <if test='viewFlag == "1" and granularityType != "D"' >
            lv1_prod_rnd_team_code,
        </if>
        <if test='viewFlag == "2" and granularityType != "D"' >
            lv2_prod_rnd_team_code,
        </if>
        <choose>
            <when test='granularityType == "U"'>
                <if test='viewFlag == "3"' >
                    lv3_prod_rnd_team_code,
                </if>
                <if test='viewFlag == "4" or viewFlag == "5" or viewFlag == "6"' >
                    lv2_prod_rnd_team_code,lv3_prod_rnd_team_code,
                    <if test='industryOrg == "IAS"'>
                        lv4_prod_rnd_team_code,
                    </if>
                </if>
                <if test='viewFlag == "7"' >
                    lv3_prod_rnd_team_code,lv4_prod_rnd_team_code,
                </if>
            </when>
            <when test='granularityType == "P"'>
                <if test='viewFlag == "3"' >
                    l1_name, lv2_prod_rnd_team_code,
                </if>
                <if test='viewFlag == "4"' >
                    l2_name,
                    l1_name, lv2_prod_rnd_team_code,
                </if>
            </when>
            <otherwise>
                lv1_prod_rnd_team_code,
                lv2_prod_rnd_team_code,
                lv3_prod_rnd_team_code,
                <if test='industryOrg == "IAS"' >
                    lv4_prod_rnd_team_code,
                </if>
                dimension_code,
                dimension_subCategory_code,
                dimension_sub_detail_code,
                spart_code,
                <if test='industryOrg == "ENERGY"' >
                    coa_code,
                </if>
            </otherwise>
        </choose>
        top_shipping_object_code,
        top_manufacture_object_code,
        is_top_flag
        <if test='granularityType == "U"'>
            FROM fin_dm_opt_foi.${tablePreFix}_top_made_info_t
        </if>
        <if test='granularityType == "P"'>
            FROM fin_dm_opt_foi.${tablePreFix}_pft_top_made_info_t
        </if>
        <if test='granularityType == "D"'>
            FROM fin_dm_opt_foi.${tablePreFix}_dms_top_made_info_t
        </if>
        WHERE del_flag = 'N'
        AND double_flag = 'Y'
        AND is_top_flag = 'Y'
        <if test='lv0ProdRndTeamCode != null and lv0ProdRndTeamCode!=""'>
            AND lv0_prod_rnd_team_code = #{lv0ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='lv1ProdRndTeamCode != null and lv1ProdRndTeamCode!=""'>
            AND lv1_prod_rnd_team_code = #{lv1ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='lv2ProdRndTeamCode != null and lv2ProdRndTeamCode!=""'>
            AND lv2_prod_rnd_team_code = #{lv2ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='lv3ProdRndTeamCode != null and lv3ProdRndTeamCode!=""'>
            AND lv3_prod_rnd_team_code = #{lv3ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='lv4ProdRndTeamCode != null and lv4ProdRndTeamCode!=""'>
            AND lv4_prod_rnd_team_code = #{lv4ProdRndTeamCode,jdbcType=VARCHAR}
        </if>
        <if test='l1Name != null and l1Name!=""'>
            AND l1_name = #{l1Name,jdbcType=VARCHAR}
        </if>
        <if test='l2Name != null and l2Name!=""'>
            AND l2_name = #{l2Name,jdbcType=VARCHAR}
        </if>
        <if test='caliberFlag != null and caliberFlag!=""'>
            AND caliber_Flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='topShippingObjectCode != null and topShippingObjectCode!=""'>
            AND top_shipping_object_code = #{topShippingObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='topManufactureObjectCode != null and topManufactureObjectCode!=""'>
            AND top_manufacture_object_code = #{topManufactureObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null and versionId!=""'>
            AND version_id = #{versionId,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag != null and overseaFlag!=""'>
            AND oversea_flag=#{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='lv0ProdListCode != null and lv0ProdListCode!=""'>
            AND lv0_prod_list_code=#{lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionCode != null and dimensionCode!=""'>
            AND dimension_code=#{dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubCategoryCode != null and dimensionSubCategoryCode!=""'>
            AND dimension_subCategory_code=#{dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubDetailCode != null and dimensionSubDetailCode!=""'>
            AND dimension_sub_detail_code=#{dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
        <if test='spartCode != null and spartCode!=""'>
            AND spart_code=#{spartCode,jdbcType=VARCHAR}
        </if>
        <if test='coaCode != null and coaCode!=""'>
            AND coa_code=#{coaCode,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag!=""'>
            AND view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        ) d2 on
        d1.top_shipping_object_code = d2.top_shipping_object_code
        and d1.top_manufacture_object_code = d2.top_manufacture_object_code
        <if test='viewFlag == "0"' >
            and d1.lv0_prod_rnd_team_code = d2.lv0_prod_rnd_team_code
        </if>
        <if test='viewFlag == "1" and granularityType != "D"' >
            and d1.lv1_prod_rnd_team_code = d2.lv1_prod_rnd_team_code
        </if>
        <if test='viewFlag == "2" and granularityType != "D"' >
            and d1.lv2_prod_rnd_team_code = d2.lv2_prod_rnd_team_code
        </if>
        <choose>
            <when test='granularityType == "U"'>
                <if test='viewFlag == "3"' >
                    and d1.lv3_prod_rnd_team_code = d2.lv3_prod_rnd_team_code
                </if>
                <if test='viewFlag == "4" or viewFlag == "5" or viewFlag == "6"' >
                    and d1.lv3_prod_rnd_team_code = d2.lv3_prod_rnd_team_code
                  <if test='industryOrg == "IAS"'>
                      and d1.lv4_prod_rnd_team_code = d2.lv4_prod_rnd_team_code
                  </if>
                </if>
                <if test='viewFlag == "7"'>
                    and d1.lv3_prod_rnd_team_code = d2.lv3_prod_rnd_team_code
                    and d1.lv4_prod_rnd_team_code = d2.lv4_prod_rnd_team_code
                </if>
            </when>
            <when test='granularityType == "P"'>
                <if test='viewFlag == "3"' >
                    and d1.l1_name = d2.l1_name and d1.lv2_prod_rnd_team_code = d2.lv2_prod_rnd_team_code
                </if>
                <if test='viewFlag == "4"' >
                    and d1.l2_name = d2.l2_name
                    and d1.l1_name = d2.l1_name and d1.lv2_prod_rnd_team_code = d2.lv2_prod_rnd_team_code
                </if>
            </when>
            <otherwise>
                <if test='viewFlag == "0" or viewFlag == "1" or viewFlag == "2"' >
                    and d1.lv1_prod_rnd_team_code = d2.lv1_prod_rnd_team_code
                </if>
                <if test='viewFlag == "3" or viewFlag == "4" or viewFlag == "5"' >
                    and d1.lv2_prod_rnd_team_code = d2.lv2_prod_rnd_team_code
                </if>
                <if test='viewFlag == "6" or viewFlag == "7" or viewFlag == "8" or viewFlag == "11"' >
                    and d1.lv3_prod_rnd_team_code = d2.lv3_prod_rnd_team_code
                </if>
                <if test='viewFlag == "12"' >
                    and d1.lv3_prod_rnd_team_code = d2.lv3_prod_rnd_team_code
                    <if test='industryOrg == "IAS"' >
                        and d1.lv4_prod_rnd_team_code = d2.lv4_prod_rnd_team_code
                    </if>
                </if>
                <if test='viewFlag == "0" or viewFlag == "3" or viewFlag == "6"' >
                    and d1.dimension_code = d2.dimension_code
                </if>
                <if test='viewFlag == "1" or viewFlag == "4" or viewFlag == "7"' >
                    and d1.dimension_code = d2.dimension_code and d1.dimension_subCategory_code = d2.dimension_subCategory_code
                </if>
                <if test='viewFlag == "2" or viewFlag == "5" or viewFlag == "8"' >
                    and d1.dimension_code = d2.dimension_code
                    and d1.dimension_subCategory_code = d2.dimension_subCategory_code
                    and d1.dimension_sub_detail_code = d2.dimension_sub_detail_code
                </if>
                <if test='viewFlag == "9" or viewFlag == "10" or viewFlag == "11"' >
                    and d1.dimension_code = d2.dimension_code
                    and d1.dimension_subCategory_code = d2.dimension_subCategory_code
                    and d1.dimension_sub_detail_code = d2.dimension_sub_detail_code
                    and d1.spart_code = d2.spart_code
                </if>
                <if test='viewFlag == "12"' >
                    and d1.dimension_code = d2.dimension_code
                    and d1.dimension_subCategory_code = d2.dimension_subCategory_code
                    and d1.dimension_sub_detail_code = d2.dimension_sub_detail_code
                    and d1.spart_code = d2.spart_code
                <if test='industryOrg == "ENERGY"' >
                    and d1.coa_code = d2.coa_code
                </if>
                </if>
            </otherwise>
        </choose>
        ORDER BY if(isnull(d1.weight4),1,0),d1.weight4 DESC
    </select>

    <select id="findAllCount" resultType="int">
        SELECT COUNT(1) from
        ( SELECT
        lv0_prod_rd_team_cn_name,
        lv1_prod_rd_team_cn_name,
        lv2_prod_rd_team_cn_name,
        <if test='granularityType != "P"'>
            lv3_prod_rd_team_cn_name,
        </if>
        <if test='granularityType != "P" and industryOrg == "IAS"'>
            lv4_prod_rd_team_cn_name,
        </if>
        <if test='granularityType == "P"'>
            l1_name, l2_name,
        </if>
        <if test='granularityType == "D"'>
            dimension_cn_name, dimension_code,
            dimension_subCategory_cn_name, dimension_subCategory_code,
            dimension_sub_detail_cn_name, dimension_sub_detail_code,
            spart_code,spart_cn_name,
            <if test='industryOrg == "ENERGY"' >
                coa_code,coa_cn_name,
            </if>
        </if>
        top_l3_ceg_cn_name,
        top_l3_ceg_short_cn_name,
        top_l4_ceg_cn_name,
        top_l4_ceg_short_cn_name,
        top_category_cn_name,
        top_category_code,
        SUM ( CASE WHEN period_year = #{periodYear0} THEN  weight_rate END ) AS weight0,
        SUM ( CASE WHEN period_year = #{periodYear1} THEN  weight_rate END ) AS weight1,
        SUM ( CASE WHEN period_year = #{periodYear2} THEN  weight_rate END ) AS weight2,
        SUM ( CASE WHEN period_year = #{periodYear3} THEN  weight_rate END ) AS weight3,
        SUM ( CASE WHEN period_year = #{periodYear4} THEN  weight_rate END ) AS weight4
        <if test='granularityType == "U"'>
            FROM fin_dm_opt_foi.${tablePreFix}_top_cate_info_t
        </if>
        <if test='granularityType == "P"'>
            FROM fin_dm_opt_foi.${tablePreFix}_pft_top_cate_info_t
        </if>
        <if test='granularityType == "D"'>
            FROM fin_dm_opt_foi.${tablePreFix}_dms_top_cate_info_t
        </if>
        WHERE
        del_flag = 'N'
        <include refid="setValues"/>
        GROUP BY
        lv0_prod_rd_team_cn_name,
        lv1_prod_rd_team_cn_name,
        lv2_prod_rd_team_cn_name,
        <if test='granularityType != "P"'>
            lv3_prod_rd_team_cn_name,
        </if>
        <if test='granularityType != "P" and industryOrg == "IAS"'>
            lv4_prod_rd_team_cn_name,
        </if>
        <if test='granularityType == "P"'>
            l1_name, l2_name,
        </if>
        <if test='granularityType == "D"'>
            dimension_cn_name, dimension_code,
            dimension_subCategory_cn_name, dimension_subCategory_code,
            dimension_sub_detail_cn_name, dimension_sub_detail_code,
            spart_code,spart_cn_name,
            <if test='industryOrg == "ENERGY"' >
                coa_code,coa_cn_name,
            </if>
        </if>
        top_l3_ceg_cn_name,
        top_l3_ceg_short_cn_name,
        top_l4_ceg_cn_name,
        top_l4_ceg_short_cn_name,
        top_category_cn_name,
        top_category_code)${tablePreFix}_top_cate_info_t
    </select>

    <select id="findManufactureAllCount" resultType="int">
        SELECT COUNT(1) from
        ( SELECT
        lv0_prod_rd_team_cn_name,
        lv1_prod_rd_team_cn_name,
        lv2_prod_rd_team_cn_name,
        <if test='granularityType != "P"'>
            lv3_prod_rd_team_cn_name,
        </if>
        <if test='granularityType != "P" and industryOrg == "IAS"'>
            lv4_prod_rd_team_cn_name,
        </if>
        <if test='granularityType == "P"'>
            l1_name, l2_name,
        </if>
        <if test='granularityType == "D"'>
            dimension_cn_name, dimension_code,
            dimension_subCategory_cn_name, dimension_subCategory_code,
            dimension_sub_detail_cn_name, dimension_sub_detail_code,
            spart_code,spart_cn_name,
            <if test='industryOrg == "ENERGY"' >
                coa_code,coa_cn_name,
            </if>
        </if>
        top_shipping_object_code,
        top_manufacture_object_code,
        top_shipping_object_cn_name,
        top_manufacture_object_cn_name,
        SUM ( CASE WHEN period_year = #{periodYear0} THEN  weight_rate END ) AS weight0,
        SUM ( CASE WHEN period_year = #{periodYear1} THEN  weight_rate END ) AS weight1,
        SUM ( CASE WHEN period_year = #{periodYear2} THEN  weight_rate END ) AS weight2,
        SUM ( CASE WHEN period_year = #{periodYear3} THEN  weight_rate END ) AS weight3,
        SUM ( CASE WHEN period_year = #{periodYear4} THEN  weight_rate END ) AS weight4
        <if test='granularityType == "U"'>
            FROM fin_dm_opt_foi.${tablePreFix}_top_made_info_t
        </if>
        <if test='granularityType == "P"'>
            FROM fin_dm_opt_foi.${tablePreFix}_pft_top_made_info_t
        </if>
        <if test='granularityType == "D"'>
            FROM fin_dm_opt_foi.${tablePreFix}_dms_top_made_info_t
        </if>
        WHERE
        del_flag = 'N'
        <include refid="setValues"/>
        GROUP BY
        lv0_prod_rd_team_cn_name,
        lv1_prod_rd_team_cn_name,
        lv2_prod_rd_team_cn_name,
        <if test='granularityType != "P"'>
            lv3_prod_rd_team_cn_name,
        </if>
        <if test='granularityType != "P" and industryOrg == "IAS"'>
            lv4_prod_rd_team_cn_name,
        </if>
        <if test='granularityType == "P"'>
            l1_name, l2_name,
        </if>
        <if test='granularityType == "D"'>
            dimension_cn_name, dimension_code,
            dimension_subCategory_cn_name, dimension_subCategory_code,
            dimension_sub_detail_cn_name, dimension_sub_detail_code,
            spart_code,spart_cn_name,
            <if test='industryOrg == "ENERGY"' >
                coa_code,coa_cn_name,
            </if>
        </if>
        top_shipping_object_code,
        top_manufacture_object_code,
        top_shipping_object_cn_name,
        top_manufacture_object_cn_name)dm
    </select>
</mapper>
