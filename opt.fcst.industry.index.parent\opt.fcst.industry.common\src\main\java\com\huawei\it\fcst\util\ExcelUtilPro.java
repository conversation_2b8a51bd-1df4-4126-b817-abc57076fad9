/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.util;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.huawei.it.fcst.constant.Constants;
import com.huawei.it.fcst.vo.AbstractExcelTitleVO;
import com.huawei.it.fcst.vo.BranchExcelTitleVO;
import com.huawei.it.fcst.vo.LeafExcelTitleVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.util.CollectionUtil;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * The class ExcelUtilPro.java
 *
 * <AUTHOR>
 * @since 2022/2/22
 */
@Component("ictExcelUtilPro")
public class ExcelUtilPro {

    /**
     * [服务名称]adjustTitleVoList
     *
     * @param abstractExcelTitleVOS            入参
     * @param selectLeafExcelTitleVO 入参
     * @return int
     * @throws CommonApplicationException
     * <AUTHOR>
     */
    public int adjustTitleVoList(List<AbstractExcelTitleVO> abstractExcelTitleVOS,
        List<AbstractExcelTitleVO> selectLeafExcelTitleVO) {
        ExcelUtilPro excelUtilPro = new ExcelUtilPro();
        excelUtilPro.initTitleVoList(abstractExcelTitleVOS, selectLeafExcelTitleVO, 1, 0);
        // 叶子结点的高度调整，
        int maxX2 = 0;
        for (AbstractExcelTitleVO leafExcelTitle : selectLeafExcelTitleVO) {
            if (leafExcelTitle.getX2() > maxX2) {
                maxX2 = leafExcelTitle.getX2();
            }
        }
        for (AbstractExcelTitleVO leafExcelTitle : selectLeafExcelTitleVO) {
            if (leafExcelTitle.getX2() < maxX2) {
                leafExcelTitle.setX2(maxX2);
            }
        }
        return maxX2 + 1;
    }

    private String getDateString(String cellvalue, Cell cell) {
        String valueStr;
        if (DateUtil.isCellDateFormatted(cell)) {
            valueStr = cn.hutool.core.date.DateUtil.format(cell.getDateCellValue(), Constants.DATEFORMAT_SS.getValue());
        } else {
            valueStr = String.valueOf(cell.getNumericCellValue());
        }
        return valueStr;
    }

    private String getRichNumber(String cellvalue, Cell cell) {
        String valueNum;
        try {
            valueNum = String.valueOf(cell.getNumericCellValue());
        } catch (IllegalStateException e) {
            valueNum = String.valueOf(cell.getRichStringCellValue());
        }
        return valueNum;
    }

    private void adjustChildExcelTitleVoWidth(List<AbstractExcelTitleVO> childTitleList, int gap) {
        if (CollectionUtil.isNullOrEmpty(childTitleList)) {
            return;
        }
        int index = childTitleList.size() - 1;
        while (index >= 0) {
            if (childTitleList.get(index).getSelected() != null && childTitleList.get(index).getSelected()) {
                break;
            } else {
                index--;
            }
        }
        if (index < 0) {
            return;
        }
        AbstractExcelTitleVO tilteExcelTitleVo = childTitleList.get(index);
        int adjustWidth = tilteExcelTitleVo.getWidth() + gap;
        tilteExcelTitleVo.setWidth(adjustWidth);
        if (tilteExcelTitleVo instanceof BranchExcelTitleVO) {
            adjustChildExcelTitleVoWidth(((BranchExcelTitleVO) tilteExcelTitleVo).getChildExcelTitleList(), gap);
        }
    }

    /**
     * 结果
     *
     * @param excelTitleVoList 参数
     * @param selectedLeafExcelTitleVO 参数
     * @param xx 参数
     * @param yy 参数
     * @return 结果
     */
    public Map initTitleVoList(List<AbstractExcelTitleVO> excelTitleVoList,
                                List<AbstractExcelTitleVO> selectedLeafExcelTitleVO, int xx, int yy) {
        Map map = new HashMap();
        int allLeafCount = 0;
        boolean isHasChildSelected = false;
        int allChildWidthSum = 0;
        int tempYy = yy;
        int tempXx = xx;
        for (AbstractExcelTitleVO excelTitleVO : excelTitleVoList) {
            if ((excelTitleVO instanceof LeafExcelTitleVO) && (excelTitleVO.getSelected()
            )) {
                excelTitleVO.setX1(tempXx);
                excelTitleVO.setY1(tempYy);
                excelTitleVO.setX2(tempXx);
                excelTitleVO.setY2(tempYy);
                tempYy++;
                allLeafCount++;
                excelTitleVO.setSelected(true);
                isHasChildSelected = true;
                allChildWidthSum += excelTitleVO.getWidth();
                selectedLeafExcelTitleVO.add(excelTitleVO);
            } else if (excelTitleVO instanceof BranchExcelTitleVO) {
                Map childMap = initTitleVoList(((BranchExcelTitleVO) excelTitleVO).getChildExcelTitleList(),
                        selectedLeafExcelTitleVO, tempXx + 1, tempYy);
                boolean isCurrentHasChildSelected = Boolean.valueOf(childMap.get("isHasChildSelected").toString());
                if (isCurrentHasChildSelected) {
                    int currentLeafCount = getCurrentLeafCount(tempYy, tempXx, excelTitleVO, childMap);
                    allLeafCount += currentLeafCount;
                    tempYy += currentLeafCount;
                    int currentChildWidthSum = Integer.valueOf(childMap.get("allChildWidthSum").toString());
                    allChildWidthSum = getAllChildWidthSum(allChildWidthSum, excelTitleVO, currentChildWidthSum);
                }
                isHasChildSelected = isHasChildSelected || isCurrentHasChildSelected;
            } else {
                continue;
            }
        }
        map.put("allLeafCount", allLeafCount);
        map.put("isHasChildSelected", isHasChildSelected);
        map.put("allChildWidthSum", allChildWidthSum);
        return map;
    }

    private int getAllChildWidthSum(int allChildWidthSum, AbstractExcelTitleVO excelTitleVO, int currentChildWidthSum) {
        if (currentChildWidthSum > excelTitleVO.getWidth()) {
            excelTitleVO.setWidth(currentChildWidthSum);
            allChildWidthSum += currentChildWidthSum;
        }
        if (currentChildWidthSum < excelTitleVO.getWidth()) {
            int gap = excelTitleVO.getWidth() - currentChildWidthSum;
            adjustChildExcelTitleVoWidth(((BranchExcelTitleVO) excelTitleVO).getChildExcelTitleList(), gap);
            allChildWidthSum += excelTitleVO.getWidth();
        }
        return allChildWidthSum;
    }

    private int getCurrentLeafCount(int tempYy, int tempXx, AbstractExcelTitleVO excelTitleVO, Map childMap) {
        excelTitleVO.setX1(tempXx);
        excelTitleVO.setY1(tempYy);
        excelTitleVO.setX2(tempXx);
        excelTitleVO.setSelected(true);
        int currentLeafCount = Integer.valueOf(childMap.get("allLeafCount").toString());
        excelTitleVO.setY2(tempYy + currentLeafCount - 1);
        return currentLeafCount;
    }

    /**
     * [服务名称]getStringCellValue
     *
     * @param cellValue     入参
     * @param headType 入参
     * @return Object
     * @throws CommonApplicationException
     * <AUTHOR>
     */
    public String getStringCellValue(Cell cellValue, String headType)
            throws CommonApplicationException {
        Optional<String> str = Optional.ofNullable(null);
        if (headType.equals("VARCHAR") && cellValue == null) {
            return Constants.DEFAULT.getValue();
        } else if (headType.equals("NUMERIC") && cellValue == null) {
            return str.orElse(null);
        } else if (headType.equals("NUMERIC") && cellValue != null) {
            try {
                String value = getStringCellValue(cellValue);
                return StrUtil.isBlank(value) ? str.orElse(null) : NumberUtil.parseNumber(value).toString();
            } catch (Exception e1) {
                throw new CommonApplicationException(
                        "第" + (cellValue.getRowIndex() + 2) + "行,第" + (cellValue.getColumnIndex() + 1) + "列不是数字类型");
            }
        } else if ( ("VARCHAR").equals(headType) && cellValue != null) {
            return getStringCellValue(cellValue);
        } else {
            return str.orElse(null);
        }
    }

    private String getStringCellValue(Cell column) {
        if (column == null) {
            return Constants.DEFAULT.getValue();
        }
        String cellvalueStr = Constants.DEFAULT.getValue();
        switch (column.getCellType()) {
            case STRING:
                cellvalueStr = column.getStringCellValue();
                break;
            case NUMERIC:
                cellvalueStr = getDateString(cellvalueStr, column);
                break;
            case BLANK:
                cellvalueStr = Constants.DEFAULT.getValue();
                break;
            case BOOLEAN:
                cellvalueStr = String.valueOf(column.getBooleanCellValue());
                break;
            case FORMULA:
                cellvalueStr = getRichNumber(cellvalueStr, column);
                break;
            case ERROR:
                cellvalueStr = Constants.ERROR.getValue();
                break;
            default:
                cellvalueStr = Constants.DEFAULT.getValue();
                break;
        }
        return cellvalueStr.endsWith(".0") ? cellvalueStr.replace(".0", "") : cellvalueStr;
    }
}
