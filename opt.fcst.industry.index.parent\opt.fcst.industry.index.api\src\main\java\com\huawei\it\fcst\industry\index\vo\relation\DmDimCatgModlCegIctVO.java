/*
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.relation;

import com.huawei.it.fcst.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * DmDimCatgModlCegIctVO Class
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "指数预测专家团-品类映射实体类")
public class DmDimCatgModlCegIctVO extends BaseVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 专家团（GROUP LV3全称）
     **/
    @ApiModelProperty("l3_ceg_cn_name")
    private String l3CegCnName;

    @ApiModelProperty("l3_ceg_code")
    private String l3CegCode;

    /**
     * 专家团（GROUP LV3简称）
     **/
    @ApiModelProperty("l3_ceg_short_cn_name")
    private String l3CegShortCnName;

    /**
     * 模块（GROUP LV4简称）
     **/
    @ApiModelProperty("l4_ceg_short_cn_name")
    private String l4CegShortCnName;

    /**
     * 模块（GROUP LV4全称）
     **/
    @ApiModelProperty("l4_ceg_cn_name")
    private String l4CegCnName;

    @ApiModelProperty("l4_ceg_code")
    private String l4CegCode;

    /**
     * 品类编码
     **/
    @ApiModelProperty("category_code")
    private String categoryCode;

    /**
     * 品类名称
     **/
    @ApiModelProperty("category_cn_name")
    private String categoryCnName;

    /**
     * 被修改的品类编码
     */
    private String oldCategoryCode;

    /**
     * TOP类别
     **/
    @ApiModelProperty("top_type")
    private String topType;

    /**
     * 品类类别
     **/
    @ApiModelProperty("category_type")
    private String categoryType;

    /**
     * 品类特征
     **/
    @ApiModelProperty("category_feature")
    private String categoryFeature;

    /**
     * 版本id
     **/
    @ApiModelProperty("version_id")
    private Long versionId;

    /**
     * 保存方式（P:页面保存、A:后台补录）
     **/
    @ApiModelProperty("save_method")
    private String saveMethod;

    /**
     * 连续性影响类别(专家团：含连续性影响/不含连续性影响)
     **/
    @ApiModelProperty("continuity_type")
    private String continuityType;

    @ApiModelProperty(value = "修改时间")
    private Date lastUpdatedDate;

    @ApiModelProperty(value = "错误信息记录")
    private String errorMessage;

    @ApiModelProperty(value = "文件名")
    private String fileName;

    @ApiModelProperty(value = "表名称前缀")
    private String tablePreFix;

    @ApiModelProperty(value = "组织机构")
    private String industryOrg;
}
