/*
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.month;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

import java.sql.Timestamp;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/5/9
 */
public class DmFocPageInfoVOTest extends BaseVOCoverUtilsTest<DmFocPageInfoVO> {
    @Override
    protected Class<DmFocPageInfoVO> getTClass() { return DmFocPageInfoVO.class; }

    @Test
    public void testMethod() {
        DmFocPageInfoVO dmFocActualCostVO = new DmFocPageInfoVO();
        dmFocActualCostVO.setDelFlag("Y");
        dmFocActualCostVO.getDelFlag();
        dmFocActualCostVO.setCreatedBy(1175L);
        dmFocActualCostVO.getCreatedBy();
        dmFocActualCostVO.getCreationDate();
        dmFocActualCostVO.setVersionId(11L);
        dmFocActualCostVO.getVersionId();
        dmFocActualCostVO.getLastUpdateDate();
        dmFocActualCostVO.setLastUpdatedBy(166L);
        dmFocActualCostVO.getLastUpdatedBy();
        dmFocActualCostVO.getPageId();
        dmFocActualCostVO.setUserId("1175");
        dmFocActualCostVO.getUserId();
        dmFocActualCostVO.setPageName("month");
        dmFocActualCostVO.getPageName();
        dmFocActualCostVO.setPageFlag("N");
        dmFocActualCostVO.getPageFlag();
        dmFocActualCostVO.setSaveThreshold("save");
        dmFocActualCostVO.getSaveThreshold();
        dmFocActualCostVO.setRoleId("role");
        dmFocActualCostVO.getRoleId();
        dmFocActualCostVO.getPageId();
        dmFocActualCostVO.setDataRange("data");
        dmFocActualCostVO.getDataRange();
        dmFocActualCostVO.setDefaultFlag("Y");
        dmFocActualCostVO.getDefaultFlag();
        dmFocActualCostVO.setGranule("U");
        dmFocActualCostVO.getGranule();
        Timestamp timestamp = new Timestamp(new Date().getTime());
        dmFocActualCostVO.setCreationDate(timestamp);
        dmFocActualCostVO.setLastUpdateDate(timestamp);
        dmFocActualCostVO.setLastUpdatedBy(111L);
        DmFocPageInfoVO.builder().pageId(11L).createdBy(11L).creationDate(timestamp).granule("U").pageFlag("Y")
                .caliberFlag("R").delFlag("N").defaultFlag("N").dataRange("1").pageName("name1").roleId("1")
                .saveThreshold("").versionId(21L).userId("test1").lastUpdatedBy(11L).lastUpdateDate(timestamp)
                .build().toString();
        Assert.assertNotNull(dmFocActualCostVO);
    }
}