/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.mix;

import com.huawei.it.fcst.annotations.ExportAttribute;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * CurrentPriceIndexVO Class
 *
 * <AUTHOR>
 * @since 2024/10/16
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CurrentPriceIndexVO {

    @ExportAttribute(sort = 0)
    private String periodId;

    @ExportAttribute(sort = 1)
    private String pspCostIndex;

    @ExportAttribute(sort = 2)
    private String stdCostIndex;
}
