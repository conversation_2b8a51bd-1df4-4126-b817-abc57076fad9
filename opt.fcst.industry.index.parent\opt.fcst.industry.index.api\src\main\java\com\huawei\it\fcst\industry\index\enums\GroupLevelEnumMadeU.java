/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.enums;

/**
 * Group层级枚举类
 *
 * <AUTHOR>
 * @since 2023/3/21
 */
public enum GroupLevelEnumMadeU {
    // Group层级（LV0：LV0、LV1：重量级团队LV1、LV2: 重量级团队LV2、LV3:重量级团队LV3、LV4:重量级团队LV4、SHIPPING_OBJECT：发货对象、MANUFACTURE_OBJECT：制造对象、item：规格品）
    LV0("LV0", "LV0"),
    LV1("LV1", "重量级团队LV1"),
    LV2("LV2", "重量级团队LV2"),
    LV3("LV3", "重量级团队LV3"),
    LV4("LV4", "重量级团队LV3.5"),
    SHIPPING_OBJECT("SHIPPING_OBJECT", "发货对象"),
    MANUFACTURE_OBJECT("MANUFACTURE_OBJECT", "制造对象"),
    ITEM("ITEM", "ITEM");

    private String value;
    private String name;

    GroupLevelEnumMadeU(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据key获取对应的实例
     *
     * @param key group level
     * @return GroupLevelEnumU
     */
    public static GroupLevelEnumMadeU getInstance(String key) {
        for (GroupLevelEnumMadeU value : GroupLevelEnumMadeU.values()) {
            if (value.getValue().equalsIgnoreCase(key)) {
                return value;
            }
        }
        return null;
    }
}