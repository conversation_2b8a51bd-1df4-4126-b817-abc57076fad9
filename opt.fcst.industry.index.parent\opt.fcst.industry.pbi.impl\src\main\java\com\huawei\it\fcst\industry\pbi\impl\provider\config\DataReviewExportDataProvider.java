/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.provider.config;

import com.huawei.it.fcst.enums.CommonConstEnum;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstIctRawDataExamineDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstVersionInfoDao;
import com.huawei.it.fcst.industry.pbi.enums.IndustryConstEnum;
import com.huawei.it.fcst.export.IExcelExportDataProvider;
import com.huawei.it.fcst.industry.pbi.vo.config.BottomLevelDataReviewVO;
import com.huawei.it.fcst.industry.pbi.vo.config.DmFcstIctRawDataExamineDTO;
import com.huawei.it.fcst.export.vo.ExcelExportContext;
import com.huawei.it.fcst.export.vo.ExportList;
import com.huawei.it.fcst.industry.pbi.vo.version.DmFcstVersionInfoDTO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Inject;
import javax.inject.Named;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 导出 底层数据审视
 * DataReviewExportDataProvider Class
 *
 * <AUTHOR>
 * @since 2024/9/11
 */
@Named("IExcelExport.DataReviewExportDataProvider")
public class DataReviewExportDataProvider implements IExcelExportDataProvider {

    @Inject
    private IDmFcstVersionInfoDao dmFcstVersionInfoDao;

    @Autowired
    private IDmFcstIctRawDataExamineDao dmFcstIctRawDataExamineDao;

    @Override
    public List<?> getData(Serializable conditionObject, PageVO pageVO) {
        BottomLevelDataReviewVO bottomLevelDataReviewVO = (BottomLevelDataReviewVO) conditionObject;
        PagedResult<DmFcstIctRawDataExamineDTO> pagedResult = dmFcstIctRawDataExamineDao.findDataReviewByPage(bottomLevelDataReviewVO, pageVO);
        List<DmFcstIctRawDataExamineDTO> result = pagedResult.getResult();
        for (DmFcstIctRawDataExamineDTO rawDataExamineDTO : result) {
            // 设置国内海外
            rawDataExamineDTO.setOverseaFlag(CommonConstEnum.getOverSeaFlag(rawDataExamineDTO.getOverseaFlag()).getDesc());
            rawDataExamineDTO.setGranularityType(IndustryConstEnum.getGranularityType(rawDataExamineDTO.getGranularityType()).getDesc());
            rawDataExamineDTO.setCostType(IndustryConstEnum.getCostType(rawDataExamineDTO.getCostType()).getDesc());
            rawDataExamineDTO.setModifyType(CommonConstEnum.getModifyType(rawDataExamineDTO.getModifyType()).getDesc());
            if ("REVOKE".equals(rawDataExamineDTO.getModifyType())) {
                rawDataExamineDTO.setModifyReasonR(rawDataExamineDTO.getModifyReason());
            } else {
                rawDataExamineDTO.setModifyReasonM(rawDataExamineDTO.getModifyReason());
            }
        }
        ExportList list = new ExportList();
        list.addAll(result);
        list.setTotalRows(pagedResult.getPageVO().getTotalRows());
        return list;
    }

    @Override
    public Map<String, Object> getHeader(Serializable conditionObject, Map<String, Object> parameters) throws ApplicationException {
        ExcelExportContext context = (ExcelExportContext) conditionObject;
        BottomLevelDataReviewVO bottomLevelDataReviewVO = (BottomLevelDataReviewVO) context.getConditionObject();
        // 自定义表头
        Map<String, Object> headMap = new HashMap<>();
        // 通过id获取版本信息
        DmFcstVersionInfoDTO versionDTO = dmFcstVersionInfoDao.findDmFocVersionDTOById(bottomLevelDataReviewVO.getVersionId());
        headMap.put("version", versionDTO.getVersion());
        return headMap;
    }
}
