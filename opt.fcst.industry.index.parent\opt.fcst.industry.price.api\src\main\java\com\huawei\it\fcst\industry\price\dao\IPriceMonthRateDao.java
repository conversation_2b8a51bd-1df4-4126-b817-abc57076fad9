/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.dao;

import com.huawei.it.fcst.industry.price.vo.month.PriceMonthAnalysisVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * IPriceMonthRateDao Interface
 *
 * <AUTHOR>
 * @since 2024-07-05
 */
public interface IPriceMonthRateDao {
    /**
     * 查询定价指数（ICT）同比/环比
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<PriceMonthAnalysisVO> findPriceRateVOList(@Param("monthAnalysisVO") PriceMonthAnalysisVO monthAnalysisVO);

    /**
     * 查询定价指数（ICT）虚化同比/环比
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<PriceMonthAnalysisVO> findBlurPriceRateVOList(@Param("monthAnalysisVO") PriceMonthAnalysisVO monthAnalysisVO);

}