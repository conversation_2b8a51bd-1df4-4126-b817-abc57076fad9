/*
 * 	Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.combination;

import com.huawei.it.fcst.industry.index.dao.IDmFocMadeViewInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocViewInfoDao;
import com.huawei.it.fcst.industry.index.enums.IndustryIndexEnum;
import com.huawei.it.fcst.config.ConfigUtil;
import com.huawei.it.fcst.industry.index.vo.combination.CombinationVO;
import com.huawei.it.fcst.industry.index.vo.common.CommonViewVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.ioc.Jalor;
import com.huawei.it.jalor5.core.ioc.delegate.JalorApplicationContext;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.security.RoleVO;
import com.huawei.it.jalor5.security.UserVO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * CompareAnalysisServiceTest Class
 *
 * <AUTHOR>
 * @since 2024/1/11
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class CompareAnalysisServiceTest {
    @InjectMocks
    private CompareAnalysisService compareAnalysisService;

    @Mock
    private CustomCombService customCombService;

    @Mock
    private IDmFocViewInfoDao dmFocViewInfoDao;

    @Mock
    private IDmFocMadeViewInfoDao dmFocMadeViewInfoDao;

    @Mock
    private JalorApplicationContext applicationContext;

    @Mock
    private IRequestContext requestContext;

    @Mock
    private ConfigUtil configUtil;

    @Before
    public void initClass() throws ApplicationException {
        Jalor.setContext(applicationContext);
        RequestContextManager.setCurrent(requestContext);
        UserVO user = new UserVO();
        user.setUserId(-1);
        RoleVO role = new RoleVO();
        role.setRoleId(-1);
        user.setCurrentRole(role);
        Mockito.when(RequestContext.getCurrent().getUser()).thenReturn(user);
        Mockito.when(Jalor.getContext().getBean("configUtil", ConfigUtil.class))
            .thenReturn(configUtil);
    }

    @Test
    public void TestGetDimensionList1Method() {
        CommonViewVO commonViewVO=new CombinationVO();
        commonViewVO.setViewFlag("6");
        commonViewVO.setGranularityType("M");
        List<DmFocViewInfoVO> data = new ArrayList<>();
        ResultDataVO getDimensionTree = new ResultDataVO();
        getDimensionTree.setData(data);
        Mockito.when(customCombService.getProdRndTeamTree(Mockito.any())).thenReturn(getDimensionTree);
        ResultDataVO result = compareAnalysisService.getDimensionList(commonViewVO);
        Assert.assertNotNull(result);
    }

    @Test
    public void TestGetDimensionList2Method() {
        CommonViewVO commonViewVO=new CombinationVO();
        commonViewVO.setViewFlag("5");
        commonViewVO.setGranularityType("U");
        List<DmFocViewInfoVO> data = new ArrayList<>();
        ResultDataVO getDimensionTree = new ResultDataVO();
        getDimensionTree.setData(data);
        Mockito.when(customCombService.getProdRndTeamTree(Mockito.any())).thenReturn(getDimensionTree);
        ResultDataVO result = compareAnalysisService.getDimensionList(commonViewVO);
        Assert.assertNotNull(result);
    }

    @Test
    public void TestGetDimensionList3Method() {
        CommonViewVO commonViewVO=new CombinationVO();
        commonViewVO.setViewFlag("4");
        commonViewVO.setGranularityType("ITEM");
        commonViewVO.setCostType(IndustryIndexEnum.COST_TYPE.P.getValue());
        commonViewVO.setGroupLevel("ITEM");
        List<DmFocViewInfoVO> dmFocViewInfoVOS = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setConnectCode("1111");
        dmFocViewInfoVO.setGroupCode("code");
        dmFocViewInfoVO.setViewFlag("2");
        dmFocViewInfoVO.setGroupLevel("LV1");
        dmFocViewInfoVOS.add(dmFocViewInfoVO);
        ResultDataVO getDimensionTree = new ResultDataVO();
        getDimensionTree.setData(dmFocViewInfoVOS);
        Mockito.when(customCombService.getProdRndTeamTree(Mockito.any())).thenReturn(getDimensionTree);
        Mockito.when(dmFocViewInfoDao.getCombinationParent(Mockito.any())).thenReturn(dmFocViewInfoVOS);
        ResultDataVO result = compareAnalysisService.getDimensionList(commonViewVO);
        Assert.assertNotNull(result);
    }

    @Test
    public void TestGetDimensionList4Method() {
        CommonViewVO commonViewVO=new CombinationVO();
        commonViewVO.setViewFlag("4");
        commonViewVO.setGranularityType("ITEM");
        commonViewVO.setCostType(IndustryIndexEnum.COST_TYPE.M.getValue());
        commonViewVO.setGroupLevel("ITEM");
        List<DmFocViewInfoVO> dmFocViewInfoVOS = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setConnectCode("1111");
        dmFocViewInfoVO.setGroupCode("code");
        dmFocViewInfoVO.setViewFlag("2");
        dmFocViewInfoVO.setGroupLevel("LV1");
        dmFocViewInfoVOS.add(dmFocViewInfoVO);
        ResultDataVO getDimensionTree = new ResultDataVO();
        getDimensionTree.setData(dmFocViewInfoVOS);
        Mockito.when(customCombService.getProdRndTeamTree(Mockito.any())).thenReturn(getDimensionTree);
        Mockito.when(dmFocMadeViewInfoDao.getMadeCombinationParent(Mockito.any())).thenReturn(dmFocViewInfoVOS);
        ResultDataVO result = compareAnalysisService.getDimensionList(commonViewVO);
        Assert.assertNotNull(result);
    }

    @Test
    public void TestGetDimensionList5Method() {
        CommonViewVO commonViewVO=new CombinationVO();
        commonViewVO.setViewFlag("4");
        commonViewVO.setGranularityType("ITEM");
        commonViewVO.setCostType(IndustryIndexEnum.COST_TYPE.M.getValue());
        commonViewVO.setGroupLevel("ITEM");
        commonViewVO.setFilterGroupLevel("ITEM");
        commonViewVO.setKeyWord("ITEM");
        commonViewVO.setIsSavePage("Y");
        List<DmFocViewInfoVO> dmFocViewInfoVOS = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setConnectCode("1111");
        dmFocViewInfoVO.setGroupCode("code");
        dmFocViewInfoVO.setViewFlag("2");
        dmFocViewInfoVO.setGroupLevel("LV1");
        dmFocViewInfoVOS.add(dmFocViewInfoVO);
        ResultDataVO getDimensionTree = new ResultDataVO();
        getDimensionTree.setData(dmFocViewInfoVOS);
        Mockito.when(customCombService.getProdRndTeamTree(Mockito.any())).thenReturn(getDimensionTree);
        Mockito.when(dmFocMadeViewInfoDao.getMadeCombinationParent(Mockito.any())).thenReturn(dmFocViewInfoVOS);
        ResultDataVO result = compareAnalysisService.getDimensionList(commonViewVO);
        Assert.assertNotNull(result);
    }

    @Test
    public void TestGetDimensionList6Method() {
        CommonViewVO commonViewVO=new CombinationVO();
        commonViewVO.setViewFlag("4");
        commonViewVO.setGranularityType("ITEM");
        commonViewVO.setCostType(IndustryIndexEnum.COST_TYPE.M.getValue());
        commonViewVO.setGroupLevel("ITEM");
        commonViewVO.setKeyWord("ITEM");
        List<DmFocViewInfoVO> dmFocViewInfoVOS = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setConnectCode("1111");
        dmFocViewInfoVO.setGroupCode("code");
        dmFocViewInfoVO.setViewFlag("2");
        dmFocViewInfoVO.setGroupLevel("LV1");
        dmFocViewInfoVOS.add(dmFocViewInfoVO);
        ResultDataVO getDimensionTree = new ResultDataVO();
        getDimensionTree.setData(dmFocViewInfoVOS);
        Mockito.when(customCombService.getProdRndTeamTree(Mockito.any())).thenReturn(getDimensionTree);
        Mockito.when(dmFocMadeViewInfoDao.getMadeCombinationParent(Mockito.any())).thenReturn(dmFocViewInfoVOS);
        ResultDataVO result = compareAnalysisService.getDimensionList(commonViewVO);
        Assert.assertNotNull(result);
    }

    @Test
    public void TestGetDimensionList7Method() {
        CommonViewVO commonViewVO=new CombinationVO();
        commonViewVO.setViewFlag("4");
        commonViewVO.setGranularityType("ITEM");
        commonViewVO.setCostType(IndustryIndexEnum.COST_TYPE.M.getValue());
        commonViewVO.setGroupLevel("ITEM");
        commonViewVO.setLv0ProdRndTeamCode("104364");
        List<DmFocViewInfoVO> dmFocViewInfoVOS = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setConnectCode("1111");
        dmFocViewInfoVO.setGroupCode("code");
        dmFocViewInfoVO.setViewFlag("2");
        dmFocViewInfoVO.setGroupLevel("LV1");
        dmFocViewInfoVOS.add(dmFocViewInfoVO);
        ResultDataVO getDimensionTree = new ResultDataVO();
        getDimensionTree.setData(dmFocViewInfoVOS);
        Mockito.when(customCombService.getProdRndTeamTree(Mockito.any())).thenReturn(getDimensionTree);
        Mockito.when(dmFocMadeViewInfoDao.getMadeCombinationParent(Mockito.any())).thenReturn(dmFocViewInfoVOS);
        ResultDataVO result = compareAnalysisService.getDimensionList(commonViewVO);
        Assert.assertNotNull(result);
    }
}
