/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.export.impl;

import com.alibaba.excel.support.ExcelTypeEnum;

import com.huawei.his.jalor.helper.StoreHelper;

import com.huawei.it.fcst.dao.IExcelImpExpRecordDao;
import com.huawei.it.fcst.export.vo.ExcelExportContext;
import com.huawei.it.fcst.export.vo.PbiDmFoiImpExpRecordVO;
import com.huawei.it.jalor5.async.AsyncMessage;
import com.huawei.it.jalor5.async.IMessageProcessor;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.util.CollectionUtil;
import com.huawei.it.jalor5.core.util.StringUtil;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.inject.Inject;
import javax.servlet.http.HttpServletResponse;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.Arrays;
import java.util.Map;

@Component("industryExportMessageProcessor")
@Slf4j
public class IndustryExportMessageProcessor implements IMessageProcessor {
    private static final String PATH = "{0}/{1}.xlsx";

    private static final String EXPORT_MODULE_NAME = "exportModuleName";

    private static final String EXPORT_FILE_NAME = "exportFileName";

    private static final String TASK_FAIL = "TASK_FAIL";

    private static final String TASK_SUCCESS = "TASK_SUCCESS";

    private static final String TASK_ID = "taskId";

    private static final String EXPORT_FAIL = "FAIL";

    private static final String EXPORT_SUCCESS = "OK";

    @Inject
    private IExcelImpExpRecordDao taskDao;

    @Inject
    private PlatformTransactionManager transactionManager;

    @Override
    public void process(AsyncMessage message) {
        ExcelExportContext context = (ExcelExportContext) message.getContent();
        IndustryExcelStreamWriter writer = new IndustryExcelStreamWriter(context);
        String filePath = MessageFormat.format(PATH, context.getFileStore(), context.getFileName());
        File file = null;
        try {
            writer.fillEasyExcelExport();
            file = FileUtils.getFile(filePath);
            context.setFileSize(file.length() / 1024);
            if (!context.getIsAsync()) {
                response(context, file);
            }
            String s3Key = Arrays.stream(StoreHelper.store(filePath).split(StoreHelper.getStoreService().getScheme()))
                    .findFirst()
                    .get();
            context.setFileS3Key(s3Key);
            context.setStatus(EXPORT_SUCCESS);
        } catch (Exception exception) {
            log.error("导出异常：{}", exception.getMessage());
            // 导出失败回写
            file = FileUtils.getFile(filePath);
            context.setStatus(EXPORT_FAIL);
        } finally {
            if (file != null && file.exists()) {
                if (!file.delete()) {
                    log.info("del file ...");
                }
            }
        }
        saveLogInfo(context);
    }

    /**
     * 导出信息保存
     *
     * @param context
     */
    public void saveLogInfo(ExcelExportContext context) {
        // 事务定义
        DefaultTransactionDefinition transDef = new DefaultTransactionDefinition();
        transDef.setName("ExcelExportLog");
        // 设置事务传播级别
        transDef.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus transStatus = transactionManager.getTransaction(transDef);
        Long userId = RequestContext.getCurrent().getUser().getUserId();
        Map<String, Object> parameter = context.getParameters();
        // 导出个人中心参数构建
        PbiDmFoiImpExpRecordVO expRecordVO = PbiDmFoiImpExpRecordVO.builder()
                .creationDate(context.getStartTime())
                .moduleType(getExportModuleName(parameter))
                .fileSize(String.valueOf(context.getFileSize()))
                .fileSourceKey(context.getFileS3Key())
                .recordNum(context.getTotal())
                .pageModule(getExportModuleName(parameter))
                .recSts(context.getStatus())
                .creationDate(context.getStartTime())
                .fileName(getExportFileName(context.getParameters()))
                .userId(String.valueOf(userId))
                .createdBy(String.valueOf(userId))
                .lastUpdatedBy(String.valueOf(userId))
                .optType("EXP")
                .status("Save")
                .fileType("xlsx")
                .build();
        taskDao.saveExportRecord(expRecordVO);
        if (context.getIsAsync()) {
            taskDao.updateDataRefreshStatus(getTaskId(parameter),
                    EXPORT_FAIL.equalsIgnoreCase(context.getStatus()) ? TASK_FAIL : TASK_SUCCESS);
        }
        transactionManager.commit(transStatus);
    }

    /**
     * 导出响应
     *
     * @param context 上下问
     * @param file    文件对象
     */
    private void response(ExcelExportContext context, File file) {
        HttpServletResponse response = context.getResponse();
        // 拼接导出名称，如果没有设置，给默认值
        String exportFileName = StringUtil.isNullOrEmpty(getExportFileName(context.getParameters()))
                ? context.getFileName()
                : getExportFileName(context.getParameters());
        try (FileInputStream inputStream = new FileInputStream(file);
                ByteArrayOutputStream byteArrayOutputStream = putInputStreamCacher(inputStream);) {
            String name = URLEncoder.encode((exportFileName + ExcelTypeEnum.XLSX.getValue()),
                    StandardCharsets.UTF_8.name());
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + name);
            byteArrayOutputStream.writeTo(response.getOutputStream());
            response.flushBuffer();
        } catch (IOException ex) {
            log.error("response error ...");
        }
    }

    /**
     * 存储输入流，以便后面使用
     *
     * @param inputStream
     * @return
     */
    private ByteArrayOutputStream putInputStreamCacher(InputStream inputStream) {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int len;
        try {
            while ((len = inputStream.read(buffer)) > -1) {
                byteArrayOutputStream.write(buffer, 0, len);
            }
            byteArrayOutputStream.flush();
        } catch (IOException exception) {
            log.error("存储输入流异常：{}", exception.getMessage());
        }
        return byteArrayOutputStream;
    }

    /**
     * 获取导出模块信息
     *
     * @param parameter 参数
     * @return 获取导出模块信息
     */
    private String getExportModuleName(Map<String, Object> parameter) {
        if (CollectionUtil.isNullOrEmpty(parameter)) {
            return "";
        }
        return (String) parameter.get(EXPORT_MODULE_NAME);
    }

    /**
     * 获取导出文件名称信息
     *
     * @param parameter 参数
     * @return 获取导出文件名称信息
     */
    private String getExportFileName(Map<String, Object> parameter) {
        if (CollectionUtil.isNullOrEmpty(parameter)) {
            return "";
        }
        return (String) parameter.get(EXPORT_FILE_NAME);
    }

    /**
     * 获取导出文件名称信息
     *
     * @param parameter 参数
     * @return 获取导出文件名称信息
     */
    private Long getTaskId(Map<String, Object> parameter) {
        if (CollectionUtil.isNullOrEmpty(parameter)) {
            return 0L;
        }
        return (Long) parameter.get(TASK_ID);
    }
}
