/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.month;

import com.alibaba.fastjson.JSONObject;
import com.huawei.it.fcst.industry.index.constant.CommonConstant;
import com.huawei.it.fcst.industry.index.constant.Constant;
import com.huawei.it.fcst.industry.index.dao.IDataCipherTextDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocActualCostDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocDataRefreshStatusDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeActualCostDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeMonthWeightDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeRecMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMonthWeightDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocRecMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTotalActualCostDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTotalMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTotalMonthWeightDao;
import com.huawei.it.fcst.industry.index.enums.GroupLevelAllEnum;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumD;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumMadeD;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumMadeU;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumU;
import com.huawei.it.fcst.industry.index.enums.IndustryConst;
import com.huawei.it.fcst.industry.index.enums.IndustryIndexEnum;
import com.huawei.it.fcst.industry.index.enums.ResultCodeEnum;
import com.huawei.it.fcst.industry.index.impl.combination.AsyncService;
import com.huawei.it.fcst.industry.index.service.common.ICommonService;
import com.huawei.it.fcst.industry.index.service.month.IMonthAnalysisService;
import com.huawei.it.fcst.industry.index.utils.AccessTokenClient;
import com.huawei.it.fcst.industry.index.utils.ExcelExportUtil;
import com.huawei.it.fcst.industry.index.utils.FcstIndexMadeUtil;
import com.huawei.it.fcst.industry.index.utils.FcstIndexUtil;
import com.huawei.it.fcst.industry.index.utils.RestUtil;
import com.huawei.it.fcst.industry.index.utils.UserInfoUtils;
import com.huawei.it.fcst.industry.index.vo.ciphertext.VarifyTaskVO;
import com.huawei.it.fcst.industry.index.vo.combination.CombTransformVO;
import com.huawei.it.fcst.industry.index.vo.combination.DmFocDataRefreshStatus;
import com.huawei.it.fcst.industry.index.vo.common.DataPermissionsVO;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.month.AmpParamVO;
import com.huawei.it.fcst.industry.index.vo.month.DmFocActualCostVO;
import com.huawei.it.fcst.industry.index.vo.month.DmFocMonthCostIdxVO;
import com.huawei.it.fcst.industry.index.vo.month.DmFocMonthWeightVO;
import com.huawei.it.fcst.industry.index.vo.month.Factors;
import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;

import com.alibaba.fastjson.JSON;

import cn.hutool.core.util.NumberUtil;

import com.huawei.it.jalor5.registry.service.IRegistryQueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.inject.Named;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * MonthAnalysisService Class
 *
 * <AUTHOR>
 * @since 2023/03/15
 */
@Named("monthAnalysisService")
@JalorResource(code = "monthAnalysisService", desc = "月度分析页面")
public class MonthAnalysisService implements IMonthAnalysisService {
    private static final Logger LOGGER = LoggerFactory.getLogger(MonthAnalysisService.class);
    private static final String NEXT_GROUP_LEVEL = "nextGroupLevel";
    private static final String NEXT_GROUP_NAME = "nextGroupName";
    @Autowired
    private MonthCommonService monthCommonService;

    @Autowired
    private IDmFocMonthCostIdxDao dmFocMonthCostIdxDao;

    @Autowired
    private IDmFocTotalMonthCostIdxDao dmFocTotalMonthCostIdxDao;

    @Autowired
    private IDmFocMadeMonthCostIdxDao dmFocMadeMonthCostIdxDao;

    @Autowired
    private IDmFocMonthWeightDao dmFocMonthWeightDao;

    @Autowired
    private IDmFocMadeMonthWeightDao dmFocMadeMonthWeightDao;

    @Autowired
    private IDmFocTotalMonthWeightDao dmFocTotalMonthWeightDao;

    @Autowired
    private IDmFocActualCostDao dmFocActualCostDao;

    @Autowired
    private IDmFocMadeActualCostDao dmFocMadeActualCostDao;

    @Autowired
    private IDmFocTotalActualCostDao dmFocTotalActualCostDao;

    @Autowired
    private ICommonService commonService;

    @Autowired
    private AsyncExportService asyncExportService;

    @Autowired
    private IDataCipherTextDao iDataCipherTextDao;

    @Autowired
    private IDmFocRecMonthCostIdxDao dmFocRecMonthCostIdxDao;

    @Autowired
    private IDmFocMadeRecMonthCostIdxDao dmFocMadeRecMonthCostIdxDao;

    @Autowired
    private AsyncService asyncService;

    @Autowired
    private IDmFocDataRefreshStatusDao dataRefreshStatusDao;

    @Autowired
    private IRegistryQueryService registryQueryService;

    @Autowired
    private AccessTokenClient accessTokenClient;


    /**
     * 判断数据是否准备好
     *
     * @param searchParamsVO 查询参数
     * @return boolean true or false
     * @throws CommonApplicationException
     */
    public boolean isDataIsOk(MonthAnalysisVO searchParamsVO) {
        return monthCommonService.isDataIsOk(searchParamsVO);
    }

    @Override
    @JalorOperation(code = "getMultiBoxList", desc = "指数（多维度）图数据的右上角多选下拉框")
    public ResultDataVO getMultiBoxList(MonthAnalysisVO monthAnalysisVO) throws ApplicationException {
        LOGGER.info("Begin MonthAnalysisService::getMultiBoxList");
        // 获取当前角色的数据范围
        DataPermissionsVO dimensionList = commonService.getDimensionList(monthAnalysisVO.getCostType(),monthAnalysisVO.getTablePreFix(),monthAnalysisVO.getIndustryOrg());
        monthAnalysisVO.setParentCodeList(monthAnalysisVO.getGroupCodeList());
        monthAnalysisVO.setParentLevel(monthAnalysisVO.getGroupLevel());
        monthAnalysisVO.setProdRndTeamCodeList(monthAnalysisVO.getTeamCodeList());
        monthAnalysisVO.setLv1DimensionSet(dimensionList.getLv1DimensionSet());
        monthAnalysisVO.setLv2DimensionSet(dimensionList.getLv2DimensionSet());
        monthAnalysisVO.setVersionId(commonService.getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),monthAnalysisVO.getTablePreFix()));
        // 分视角获取下个层级的groupLevel值
        if (setSearchInitParams(monthAnalysisVO)){
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        // 查询权重图数据
        List<DmFocMonthWeightVO> weightList = getWeightVOList(monthAnalysisVO);
        List<DmFocMonthWeightVO> newWeightList = weightList.stream().map(weightVO -> {
            DmFocMonthWeightVO monthWeightVO = new DmFocMonthWeightVO();
            monthWeightVO.setGroupLevel(weightVO.getGroupLevel());
            monthWeightVO.setGroupCode(weightVO.getGroupCode());
            if (GroupLevelEnumU.CATEGORY.getValue().equals(weightVO.getGroupLevel())) {
                monthWeightVO.setGroupCnName(weightVO.getGroupCode() + " " + weightVO.getGroupCnName());
            } else {
                monthWeightVO.setGroupCnName(weightVO.getGroupCnName());
            }
            return monthWeightVO;
        }).collect(Collectors.toList());
        List<DmFocMonthWeightVO> distinctWeightList = newWeightList.stream().distinct().collect(Collectors.toList());
        List<DmFocMonthWeightVO> newMonthWeightVOList = sortLv1ByCode(monthAnalysisVO,distinctWeightList);
        if (CollectionUtils.isNotEmpty(newMonthWeightVOList)) {
            return ResultDataVO.success(newMonthWeightVOList);
        }
        return ResultDataVO.success(distinctWeightList);
    }

    private List<DmFocMonthWeightVO> getWeightVOList(MonthAnalysisVO searchParamsVO) {
        List<DmFocMonthWeightVO> weightList = new ArrayList<>();
        // 查询采购成本下的权重图数据
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(searchParamsVO.getCostType())) {
            weightList = dmFocMonthWeightDao.findWeightList(searchParamsVO);
        }
        // 查询制造成本下的权重图数据
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(searchParamsVO.getCostType())) {
            weightList = dmFocMadeMonthWeightDao.findMadeWeightList(searchParamsVO);
        }
        // 查询总成本下的权重图数据
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(searchParamsVO.getCostType())) {
            weightList = dmFocTotalMonthWeightDao.findTotalWeightList(searchParamsVO);
        }
        return weightList;
    }

    private List<DmFocMonthWeightVO> sortLv1ByCode(MonthAnalysisVO searchParamsVO, List<DmFocMonthWeightVO> newWeightList) {
        List<DmFocMonthWeightVO> newMonthWeightVOList = new ArrayList<>();
        if (GroupLevelEnumU.LV0.getValue().equals(searchParamsVO.getGroupLevel()) && !IndustryIndexEnum.VIEW_FLAG_P.VIEW1.getValue().equals(searchParamsVO.getViewFlag())) {
            for (String code : CommonConstant.LV1_CODE) {
                ForNewWeightList(newWeightList, newMonthWeightVOList, code);
            }
            List<DmFocMonthWeightVO> otherWeightList = newWeightList.stream().filter(amp -> !newMonthWeightVOList.contains(amp)).collect(Collectors.toList());
            newMonthWeightVOList.addAll(otherWeightList);
        }
        return newMonthWeightVOList;
    }

    private void ForNewWeightList(List<DmFocMonthWeightVO> newWeightList, List<DmFocMonthWeightVO> newMonthWeightVOList, String code) {
        for (DmFocMonthWeightVO dmFocMonthWeightVO : newWeightList) {
            if (code.equals(dmFocMonthWeightVO.getGroupCode())) {
                newMonthWeightVOList.add(dmFocMonthWeightVO);
            }
        }
    }

    /**
     * 产业成本指数图接口
     *
     * @param monthAnalysisVO 查询参数VO
     * @return ResultDataVO
     */
    @Override
    @JalorOperation(code = "getMultiItemBoxList", desc = "汇总组合item下拉框查询")
    public ResultDataVO getMultiItemBoxList(MonthAnalysisVO monthAnalysisVO) {
        // 校验入参
        if (StringUtils.isBlank(monthAnalysisVO.getIndustryOrg())) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        monthAnalysisVO.setParentCodeList(monthAnalysisVO.getGroupCodeList());
        monthAnalysisVO.setParentLevel(monthAnalysisVO.getGroupLevel());
        // 组合
        monthAnalysisVO.setGroupLevel(GroupLevelEnumU.ITEM.getValue());
        distinguishIfCombine(monthAnalysisVO);
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(monthAnalysisVO.getPageSize());
        pageVO.setCurPage(monthAnalysisVO.getCurPage());
        monthAnalysisVO.setVersionId(commonService.getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),monthAnalysisVO.getTablePreFix()));
        // 只选择了组合
        PagedResult<DmFocMonthCostIdxVO> allItemCode = new PagedResult<>();
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getCombinaCodeList()) && CollectionUtils.isEmpty(monthAnalysisVO.getParentCodeList())) {
            // 查询采购成本的数据
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
                allItemCode = dmFocMonthCostIdxDao.findAllItemCodeForComb(monthAnalysisVO, pageVO);
            }
            // 查询制造成本的数据
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())) {
                allItemCode = dmFocMadeMonthCostIdxDao.findAllItemCodeForComb(monthAnalysisVO, pageVO);
            }
            for (DmFocMonthCostIdxVO dmFocMonthCostIdxVO : allItemCode.getResult()) {
                dmFocMonthCostIdxVO.setIsContainComb(true);
                dmFocMonthCostIdxVO.setGroupCode(dmFocMonthCostIdxVO.getCustomId() + "_##" + dmFocMonthCostIdxVO.getGroupCode());
            }
        }
        allItemCode = getDmFocMonthCostIdxResult(monthAnalysisVO, pageVO, allItemCode);
        Map result = new LinkedHashMap();
        result.put("result", allItemCode.getResult());
        result.put("pageVO", allItemCode.getPageVO());
        return ResultDataVO.success(result);
    }

    private PagedResult<DmFocMonthCostIdxVO> getDmFocMonthCostIdxResult(MonthAnalysisVO monthAnalysisVO, PageVO pageVO, PagedResult<DmFocMonthCostIdxVO> allItemCode) {
        // 都选择了
        if (isAllNotEmpty(monthAnalysisVO.getParentCodeList(), monthAnalysisVO.getCombinaCodeList())) {
            // 查询采购成本的数据
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
                allItemCode = dmFocMonthCostIdxDao.findAllItemCodeForAll(monthAnalysisVO, pageVO);
            }
            // 查询制造成本的数据
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())) {
                allItemCode = dmFocMadeMonthCostIdxDao.findAllItemCodeForAll(monthAnalysisVO, pageVO);
            }
            for (DmFocMonthCostIdxVO dmFocMonthCostIdxVO : allItemCode.getResult()) {
                if (0 == dmFocMonthCostIdxVO.getCustomId()) {
                    dmFocMonthCostIdxVO.setIsContainComb(false);
                } else {
                    dmFocMonthCostIdxVO.setIsContainComb(true);
                    dmFocMonthCostIdxVO.setGroupCode(dmFocMonthCostIdxVO.getCustomId() + "_##" + dmFocMonthCostIdxVO.getGroupCode());
                }
            }
        }
        return allItemCode;
    }

    /**
     * [服务名称]dataRefreshRequest 导入状态查询
     *
     * @param varifyTaskVOParam 入参
     * @return CommonResult
     * @throws ApplicationException
     * <AUTHOR>
     */
    @Override
    @JalorOperation(code = "queryDataRefreshStatus", desc = "指数图切换基期查询")
    public ResultDataVO queryDataRefreshStatus(VarifyTaskVO varifyTaskVOParam)
            throws CommonApplicationException {
        if (null == varifyTaskVOParam.getTaskId() || varifyTaskVOParam.getTaskId() <= 0L
                || StringUtils.isBlank(varifyTaskVOParam.getIndustryOrg())) {
            throw new CommonApplicationException(ResultCodeEnum.PARAM_ERROR.getMessage());
        }
        VarifyTaskVO taskVO = iDataCipherTextDao.searchVerifyTask(varifyTaskVOParam);
        if (IndustryConst.TaskStatus.TASK_FAIL.getValue().equals(taskVO.getStatus())) {
            throw new CommonApplicationException("调用函数失败");
        }
        return ResultDataVO.success(taskVO);
    }

    /**
     * 产业成本指数图接口
     *
     * @param monthAnalysisVO 查询参数VO
     * @return ResultDataVO
     */
    @Override
    @JalorOperation(code = "getIndustryCostIndexChart", desc = "产业成本指数图")
    public ResultDataVO getIndustryCostIndexChart(MonthAnalysisVO monthAnalysisVO) {
        LOGGER.info("Begin MonthAnalysisService::getIndustryCostIndexChart");
        // 获取用户权限
        monthCommonService.getUserPermission(monthAnalysisVO);
        return monthCommonService.getIndustryCostIndexChart(monthAnalysisVO);
    }


    /**
     * 产业成本对比分析指数图
     *
     * @param monthAnalysisVolist 查询参数VO
     * @return ResultDataVO
     */
    @Override
    @JalorOperation(code = "getCompareIndexChart", desc = "产业成本对比分析指数图")
    public ResultDataVO getCompareIndexChart(List<MonthAnalysisVO> monthAnalysisVolist) throws InterruptedException {
        // 获取用户权限
        for (MonthAnalysisVO monthAnalysisVO : monthAnalysisVolist) {
            monthCommonService.getUserPermission(monthAnalysisVO);
        }
        LOGGER.info("Begin MonthAnalysisService::getCompareIndexChart");
        return monthCommonService.getCompareIndexChart(monthAnalysisVolist);
    }

    /**
     * 产业成本指数 同步环比图
     *
     * @param monthAnalysisVO 查询参数VO
     * @return ResultDataVO
     */
    @Override
    @JalorOperation(code = "getIndustryCostYoyAndPopChart", desc = "产业成本指数同比环比图")
    public ResultDataVO getIndustryCostYoyAndPopChart(MonthAnalysisVO monthAnalysisVO) {
        LOGGER.info("Begin MonthAnalysisService::getIndustryCostYoyAndPopChart");
        // 获取当前角色的数据范围
        monthCommonService.getUserPermission(monthAnalysisVO);
        return monthCommonService.getIndustryCostYoyAndPopChart(monthAnalysisVO);
    }


    /**
     * 查询产业成价格指数图
     *
     * @param monthAnalysisVO 参数参数VO
     * @return ResultDataVO result data
     */
    @Override
    @JalorOperation(code = "getIndustryCostIndexTaskStatus", desc = "产业成本指数图切换基期状态")
    public ResultDataVO getIndustryCostIndexTaskStatus(MonthAnalysisVO monthAnalysisVO) {
        LOGGER.info(">>>Begin MonthCommonService::getIndustryCostIndexChart");
        // 第1步： 校验必填参数
        // 检查所选基期及groupCode是否有数据，若没有数据，则调用函数 F_DM_FOC_POINT_INDEX 刷新 产业成本指数(dm_foc_month_cost_idx_t)
        String industryOrg = monthAnalysisVO.getIndustryOrg();
        if (ObjectUtils.isEmpty(monthAnalysisVO.getBasePeriodId()) || CollectionUtils.isEmpty(monthAnalysisVO.getGroupCodeList())
                || StringUtils.isAnyBlank(monthAnalysisVO.getViewFlag(), industryOrg)) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        if (monthAnalysisVO.getVersionId() == null) {
            monthAnalysisVO.setVersionId(commonService.getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),monthAnalysisVO.getTablePreFix()));
        }
        // 第2步：检查基期开始时间和结束时间是否传参，若没有，则默认取报告期36个月的开始时间和结束时间
        FcstIndexUtil.handlePeriod(monthAnalysisVO, dmFocMonthCostIdxDao.findActualMonthNum(monthAnalysisVO.getTablePreFix()).toString());
        // 分视角获取下个层级的groupLevel值
        Map map = new HashMap();
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())
                || IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostSubType())  ) {
            map = FcstIndexMadeUtil.getNextGroupLevel(monthAnalysisVO);
        } else {
            map = FcstIndexUtil.getNextGroupLevel(monthAnalysisVO);
        }
        if (ObjectUtils.isEmpty(map.get(NEXT_GROUP_LEVEL)) || ObjectUtils.isEmpty(map.get(NEXT_GROUP_NAME))) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        monthAnalysisVO.setParentLevel(monthAnalysisVO.getGroupLevel());
        monthAnalysisVO.setGroupLevel(map.get(NEXT_GROUP_LEVEL).toString());
        // 任务状态初始化设置
        VarifyTaskVO varifyTaskVO = new VarifyTaskVO();
        varifyTaskVO.setTaskType("data_index");
        varifyTaskVO.setIndustryOrg(monthAnalysisVO.getIndustryOrg());
        String success = IndustryConst.TaskStatus.TASK_SUCCESS.getValue();
        if (monthAnalysisVO.getIsContainComb() && CollectionUtils.isNotEmpty(monthAnalysisVO.getCustomIdList())) {
            monthAnalysisVO.setReverseViewFlag(false);
            ResultDataVO varifyTaskContainComb = getResultDataVO(monthAnalysisVO, varifyTaskVO, success);
            if (varifyTaskContainComb != null) {
                return varifyTaskContainComb;
            }
        } else {
            // 反向视角和不包含汇总组合的切换基期任务判断
            ResultDataVO varifyTaskChangePeriod = changePeriodIdFlag(monthAnalysisVO, varifyTaskVO, success);
            if (varifyTaskChangePeriod != null) {
                return varifyTaskChangePeriod;
            }
        }
        return ResultDataVO.success(varifyTaskVO);
    }

    @Nullable
    private ResultDataVO changePeriodIdFlag(MonthAnalysisVO monthAnalysisVO, VarifyTaskVO varifyTaskVO, String success) {
        // 反向视角标识设置
        Boolean reveseViewFlag = FcstIndexUtil.isReveseViewFlag(monthAnalysisVO);
        monthAnalysisVO.setReverseViewFlag(reveseViewFlag);
        if (reveseViewFlag) {
            // 反向视角判断是否需要切换基期
            if (monthCommonService.reverseDataIsOk(monthAnalysisVO)) {
                varifyTaskVO.setStatus(success);
                return ResultDataVO.success(varifyTaskVO);
            }
        } else {
            // 正向视角判断是否需要切换基期
            if (isDataIsOk(monthAnalysisVO)) {
                varifyTaskVO.setStatus(success);
                return ResultDataVO.success(varifyTaskVO);
            }
        }
        varifyTaskVO.setStatus(IndustryConst.TaskStatus.TASK_PROCESSING.getValue());
        refreshIndustryIndexFunction(monthAnalysisVO, varifyTaskVO);
        return null;
    }

    @Nullable
    private ResultDataVO getResultDataVO(MonthAnalysisVO monthAnalysisVO, VarifyTaskVO varifyTaskVO, String success) {
        // 汇总组合调用切换基期函数
        MonthAnalysisVO buildComb = new MonthAnalysisVO();
        BeanUtils.copyProperties(monthAnalysisVO, buildComb);
        monthCommonService.distinguishIfCombine(buildComb);
        indexChangeBasePeriodId(varifyTaskVO, success, buildComb);
        if (isAllNotEmpty(buildComb.getGroupCodeList(), buildComb.getCombinaCodeList())) {
            if (success.equals(varifyTaskVO.getStatus()) && success.equals(varifyTaskVO.getCombStatus())) {
                return ResultDataVO.success(varifyTaskVO);
            }
        } else if (CollectionUtils.isNotEmpty(buildComb.getGroupCodeList()) && CollectionUtils.isEmpty(buildComb.getCombinaCodeList())) {
            if (success.equals(varifyTaskVO.getStatus())) {
                return ResultDataVO.success(varifyTaskVO);
            }
        } else if (CollectionUtils.isNotEmpty(buildComb.getCombinaCodeList()) && CollectionUtils.isEmpty(buildComb.getGroupCodeList())) {
            if (success.equals(varifyTaskVO.getCombStatus())) {
                return ResultDataVO.success(varifyTaskVO);
            }
        }
        return null;
    }

    private void indexChangeBasePeriodId(VarifyTaskVO varifyTaskVO, String success, MonthAnalysisVO buildComb) {
        // 混合选择 正常维度
        MonthAnalysisVO normalSingleVO = new MonthAnalysisVO();
        if (CollectionUtils.isNotEmpty(buildComb.getGroupCodeList())) {
            if (isDataIsOk(buildComb)) {
                varifyTaskVO.setStatus(success);
            } else {
                BeanUtils.copyProperties(buildComb, normalSingleVO);
                normalSingleVO.setCustomIdList(null);
                normalSingleVO.setCombinaCodeList(null);
                varifyTaskVO.setStatus(IndustryConst.TaskStatus.TASK_PROCESSING.getValue());
            }
        }
        // 汇总组合
        MonthAnalysisVO combAnalysisVO = new MonthAnalysisVO();
        if (CollectionUtils.isNotEmpty(buildComb.getCombinaCodeList())) {
            // 切换基期函数
            if (monthCommonService.isCombDataIsOk(buildComb)) {
                varifyTaskVO.setCombStatus(success);
            } else {
                monthCommonService.getCombAnalysisVO(buildComb, combAnalysisVO);
                varifyTaskVO.setCombStatus(IndustryConst.TaskStatus.TASK_PROCESSING.getValue());
            }
        }
        asyncSetTaskSattus(varifyTaskVO, normalSingleVO, combAnalysisVO);
    }

    /**
     * 产业成本指数图 (多指数)
     *
     * @param monthAnalysisVO 查询参数VO
     * @return ResultDataVO
     */
    @Override
    @JalorOperation(code = "getIndustryCostMultiIndexTaskStatus", desc = "产业成本指数图 (多指数)切换基期状态")
    public ResultDataVO getIndustryCostMultiIndexTaskStatus(MonthAnalysisVO monthAnalysisVO) {
        LOGGER.info("Begin MonthAnalysisService::getIndustryCostMultiIndexTaskStatus");
        // 第1步：校验 groupCodeList，视角，重量级图团队 必填字段
        if (CollectionUtils.isEmpty(monthAnalysisVO.getSubGroupCodeList())
                || StringUtils.isAnyBlank(monthAnalysisVO.getViewFlag(), monthAnalysisVO.getIndustryOrg())) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        ResultDataVO PARAM_ERROR = setMultiDiemsionCharSearchVO(monthAnalysisVO);
        if (PARAM_ERROR != null) {
            return PARAM_ERROR;
        }
        // 任务状态初始化设置
        VarifyTaskVO varifyTaskVO = new VarifyTaskVO();
        String success = IndustryConst.TaskStatus.TASK_SUCCESS.getValue();
        varifyTaskVO.setTaskType("data_mutli_index");
        varifyTaskVO.setIndustryOrg(monthAnalysisVO.getIndustryOrg());
        // 包含汇总组合
        if (monthAnalysisVO.getIsContainComb() && CollectionUtils.isNotEmpty(monthAnalysisVO.getCustomIdList())) {
            ResultDataVO varifyTaskCombVO = getHasCombResultDataVO(monthAnalysisVO, varifyTaskVO, success);
            if (varifyTaskCombVO != null) {
                return varifyTaskCombVO;
            }
        } else {
            ResultDataVO varifyTaskMutliChange = getMutliChangePeriodId(monthAnalysisVO, varifyTaskVO, success);
            if (varifyTaskMutliChange != null) {
                return varifyTaskMutliChange;
            }
        }
        return ResultDataVO.success(varifyTaskVO);
    }

    @Nullable
    private ResultDataVO getMutliChangePeriodId(MonthAnalysisVO monthAnalysisVO, VarifyTaskVO varifyTaskVO, String success) {
        // 反向视角
        if (monthAnalysisVO.getReverseViewFlag()) {
            MonthAnalysisVO revBuild = getRevMonthAnalysisVO(monthAnalysisVO);
            revBuild.setIndustryOrg(monthAnalysisVO.getIndustryOrg());
            if (monthCommonService.reverseDataIsOk(revBuild)) {
                varifyTaskVO.setStatus(success);
                return ResultDataVO.success(varifyTaskVO);
            }
        } else {
            // 正向视角
            MonthAnalysisVO build = getMonthAnalysisVO(monthAnalysisVO);
            build.setIndustryOrg(monthAnalysisVO.getIndustryOrg());
            if (isDataIsOk(build)) {
                varifyTaskVO.setStatus(success);
                return ResultDataVO.success(varifyTaskVO);
            }
        }
        varifyTaskVO.setStatus(IndustryConst.TaskStatus.TASK_PROCESSING.getValue());
        refreshIndustryIndexFunction(monthAnalysisVO, varifyTaskVO);
        return null;
    }

    @Nullable
    private ResultDataVO getHasCombResultDataVO(MonthAnalysisVO monthAnalysisVO, VarifyTaskVO varifyTaskVO, String success) {
        MonthAnalysisVO buildComb = new MonthAnalysisVO();
        BeanUtils.copyProperties(monthAnalysisVO, buildComb);
        monthAnalysisVO.setGroupLevel(GroupLevelEnumU.ITEM.getValue());
        distinguishIfCombine(buildComb);
        buildComb.setGroupCodeList(null);
        // 切换基期
        changeBasePeriodId(varifyTaskVO, success, buildComb);
        ResultDataVO varifyStatusTask = vaildStatusAndCombStatus(varifyTaskVO, success, buildComb);
        if (varifyStatusTask != null) {
            return varifyStatusTask;
        }
        return null;
    }

    private boolean isAllNotEmpty(List<String>... lists) {
        for (List<String> list : lists) {
            if (CollectionUtils.isEmpty(list)) {
                return false;
            }
        }
        return true;
    }

    @Nullable
    private ResultDataVO vaildStatusAndCombStatus(VarifyTaskVO varifyTaskVO, String success, MonthAnalysisVO buildComb) {
        if (isAllNotEmpty(buildComb.getParentCodeList(), buildComb.getCombParentCodeList(), buildComb.getSubGroupCodeList(), buildComb.getCombinaSubGroupCodeList())) {
            if (success.equals(varifyTaskVO.getStatus()) && success.equals(varifyTaskVO.getCombStatus())) {
                return ResultDataVO.success(varifyTaskVO);
            }
        }

        if (isAllNotEmpty(buildComb.getParentCodeList(), buildComb.getSubGroupCodeList()) && CollectionUtils.isEmpty(buildComb.getCustomIdList())) {
            if (success.equals(varifyTaskVO.getStatus())) {
                return ResultDataVO.success(varifyTaskVO);
            }
        }

        ResultDataVO varifyTaskVOSub = vaildStatusAndCombStatusSub(varifyTaskVO, success, buildComb);
        if (varifyTaskVOSub != null) {
            return varifyTaskVOSub;
        }
        return null;
    }

    private ResultDataVO getResultDataVO(VarifyTaskVO varifyTaskVO, String success, String status) {
        if (success.equals(status)) {
            return ResultDataVO.success(varifyTaskVO);
        }
        return null;
    }
    @Nullable
    private ResultDataVO vaildStatusAndCombStatusSub(VarifyTaskVO varifyTaskVO, String success, MonthAnalysisVO buildComb) {
        if (isAllNotEmpty(buildComb.getParentCodeList(), buildComb.getCombParentCodeList(), buildComb.getSubGroupCodeList())
                && CollectionUtils.isEmpty(buildComb.getCombinaSubGroupCodeList())) {
            getResultDataVO(varifyTaskVO, success, varifyTaskVO.getStatus());
        }
        if (CollectionUtils.isEmpty(buildComb.getParentCodeList())
                && isAllNotEmpty(buildComb.getCombParentCodeList(), buildComb.getCombinaSubGroupCodeList())) {
            getResultDataVO(varifyTaskVO, success, varifyTaskVO.getCombStatus());
        }
        if (isAllNotEmpty(buildComb.getParentCodeList(), buildComb.getCombParentCodeList(), buildComb.getCombinaSubGroupCodeList())
                && CollectionUtils.isEmpty(buildComb.getSubGroupCodeList())) {
            getResultDataVO(varifyTaskVO, success, varifyTaskVO.getCombStatus());
        }
        return null;
    }

    private void changeBasePeriodId(VarifyTaskVO varifyTaskVO, String success, MonthAnalysisVO buildComb) {
        // 多选有汇总组合和非汇总组合两种情况
        MonthAnalysisVO normalAnalysisVO = new MonthAnalysisVO();
        if (isAllNotEmpty(buildComb.getParentCodeList(), buildComb.getSubGroupCodeList())) {
            // 非汇总组合切换基期
            if (monthCommonService.hasCombNormalDataIsOk(buildComb)) {
                varifyTaskVO.setStatus(success);
            } else {
                BeanUtils.copyProperties(buildComb, normalAnalysisVO);
                normalAnalysisVO.setParentLevel("ITEM");
                normalAnalysisVO.setGroupLevel(null);
                normalAnalysisVO.setCustomIdList(null);
                normalAnalysisVO.setCombParentCodeList(null);
                normalAnalysisVO.setParentCodeList(buildComb.getSubGroupCodeList());
                varifyTaskVO.setStatus(IndustryConst.TaskStatus.TASK_PROCESSING.getValue());
            }
        }
        MonthAnalysisVO combAnalysisVO = new MonthAnalysisVO();
        if (isAllNotEmpty(buildComb.getCombinaCodeList(), buildComb.getCombinaSubGroupCodeList())) {
            buildComb.setCombParentCodeList(buildComb.getCombinaCodeList());
            buildComb.setCombinaCodeList(null);
            // 汇总组合切换基期
            if (monthCommonService.isCombDataIsOk(buildComb)) {
                varifyTaskVO.setCombStatus(success);
            } else {
                BeanUtils.copyProperties(buildComb, combAnalysisVO);
                combAnalysisVO.setProdRndTeamCodeList(null);
                combAnalysisVO.setL1NameList(null);
                combAnalysisVO.setL2NameList(null);
                combAnalysisVO.setDmsCodeList(null);
                combAnalysisVO.setParentCodeList(null);
                varifyTaskVO.setCombStatus(IndustryConst.TaskStatus.TASK_PROCESSING.getValue());
            }
        }
        asyncSetTaskSattus(varifyTaskVO, normalAnalysisVO, combAnalysisVO);
    }

    private void asyncSetTaskSattus(VarifyTaskVO varifyTaskVO, MonthAnalysisVO buildComb, MonthAnalysisVO combAnalysisVO) {
        if (IndustryConst.TaskStatus.TASK_PROCESSING.getValue().equals(varifyTaskVO.getStatus())) {
            refreshIndustryIndexFunction(buildComb, varifyTaskVO);
        }
        if (IndustryConst.TaskStatus.TASK_PROCESSING.getValue().equals(varifyTaskVO.getCombStatus())) {
            refreshIndustryIndexFunction(combAnalysisVO, varifyTaskVO);
        }
    }

    private MonthAnalysisVO getRevMonthAnalysisVO(MonthAnalysisVO monthAnalysisVO) {
        // 检查所选基期及groupCode是否有数据，若没有数据，则调用函数 p_dm_foc_price_index_t 刷新 价格指数总览表(DM_FOC_MONTH_COST_IDX_T)
        return MonthAnalysisVO.builder()
                .viewFlag(monthAnalysisVO.getViewFlag())
                .granularityType(monthAnalysisVO.getGranularityType())
                .caliberFlag(monthAnalysisVO.getCaliberFlag())
                .purCodeList(monthAnalysisVO.getPurCodeList())
                .basePeriodId(monthAnalysisVO.getBasePeriodId())
                .versionId(monthAnalysisVO.getVersionId())
                .parentCodeList(monthAnalysisVO.getParentCodeList())
                .subGroupCodeList(monthAnalysisVO.getSubGroupCodeList())
                .groupLevel(monthAnalysisVO.getGroupLevel())
                .overseaFlag(monthAnalysisVO.getOverseaFlag())
                .costType(monthAnalysisVO.getCostType())
                .manufactureObjectCode(monthAnalysisVO.getManufactureObjectCode())
                .shippingObjectCode(monthAnalysisVO.getShippingObjectCode())
                .lv0ProdListCode(monthAnalysisVO.getLv0ProdListCode())
                .build();
    }

    private MonthAnalysisVO getMonthAnalysisVO(MonthAnalysisVO monthAnalysisVO) {
        // 检查所选基期及groupCode是否有数据，若没有数据，则调用函数 p_dm_foc_price_index_t 刷新 价格指数总览表(DM_FOC_MONTH_COST_IDX_T)
        return MonthAnalysisVO.builder()
                .viewFlag(monthAnalysisVO.getViewFlag())
                .l1NameList(monthAnalysisVO.getL1NameList())
                .l2NameList(monthAnalysisVO.getL2NameList())
                .granularityType(monthAnalysisVO.getGranularityType())
                .caliberFlag(monthAnalysisVO.getCaliberFlag())
                .prodRndTeamCodeList(monthAnalysisVO.getProdRndTeamCodeList())
                .basePeriodId(monthAnalysisVO.getBasePeriodId())
                .versionId(monthAnalysisVO.getVersionId())
                .parentCodeList(monthAnalysisVO.getParentCodeList())
                .subGroupCodeList(monthAnalysisVO.getSubGroupCodeList())
                .groupLevel(monthAnalysisVO.getGroupLevel())
                .dmsCodeList(monthAnalysisVO.getDmsCodeList())
                .overseaFlag(monthAnalysisVO.getOverseaFlag())
                .costType(monthAnalysisVO.getCostType())
                .manufactureObjectCode(monthAnalysisVO.getManufactureObjectCode())
                .shippingObjectCode(monthAnalysisVO.getShippingObjectCode())
                .lv0ProdListCode(monthAnalysisVO.getLv0ProdListCode())
                .build();
    }

    public void refreshIndustryIndexFunction(MonthAnalysisVO monthAnalysisVO, VarifyTaskVO varifyTaskVO) {
        if (null == varifyTaskVO.getTaskId()) {
            Long taskId = iDataCipherTextDao.getVerifyTaskId(monthAnalysisVO.getTablePreFix());
            varifyTaskVO.setTaskId(taskId);
            varifyTaskVO.setPeriodId(NumberUtil.parseLong(monthAnalysisVO.getBasePeriodId().toString()));
            iDataCipherTextDao.insertVerifyTask(varifyTaskVO);
        } else {
            iDataCipherTextDao.updateVerifyTask(varifyTaskVO);
        }
        // 异步调用函数
        asyncExportService.refreshIndustryIndexData(monthAnalysisVO, varifyTaskVO);
    }

    private void setStartEndTime(MonthAnalysisVO monthAnalysisVO) {
        Map<String, Long> startEndTime = dmFocMadeMonthCostIdxDao
                .findStartEndTime(monthAnalysisVO.getGranularityType(), monthAnalysisVO.getTablePreFix());
        monthAnalysisVO.setPeriodStartTime(Integer.parseInt(startEndTime.get("start").toString()));
        monthAnalysisVO.setPeriodEndTime(Integer.parseInt(startEndTime.get("end").toString()));
    }

    /**
     * 产业成本指数图 (多指数)
     *
     * @param monthAnalysisVO 查询参数VO
     * @return ResultDataVO
     */
    @Override
    @JalorOperation(code = "getIndustryCostMultiDimensionChart", desc = "产业成本指数图 (多指数)")
    public ResultDataVO getIndustryCostMultiDimensionChart(MonthAnalysisVO monthAnalysisVO) {
        LOGGER.info("Begin MonthAnalysisService::getIndustryCostMultiDimensionChart");
        monthCommonService.getUserPermission(monthAnalysisVO);
        // 第1步：校验 groupCodeList，视角，重量级图团队 必填字段
        if (CollectionUtils.isEmpty(monthAnalysisVO.getSubGroupCodeList())
                || StringUtils.isAnyBlank(monthAnalysisVO.getViewFlag(), monthAnalysisVO.getIndustryOrg())) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        ResultDataVO PARAM_ERROR = setMultiDiemsionCharSearchVO(monthAnalysisVO);
        if (PARAM_ERROR != null) {
            return PARAM_ERROR;
        }
        List<DmFocMonthCostIdxVO> priceIndexChartList = new ArrayList<>();
        if (monthAnalysisVO.getReverseViewFlag()) {
            priceIndexChartList = getRevPriceIndexList(monthAnalysisVO, priceIndexChartList);
        } else {
            getPriceIndexChartList(monthAnalysisVO, priceIndexChartList);
        }
        // 返回值处理
        List<List<DmFocMonthCostIdxVO>> dataList = resultDataGroupBy(monthAnalysisVO, priceIndexChartList);
        return ResultDataVO.success(dataList);
    }

    private List<DmFocMonthCostIdxVO> getRevPriceIndexList(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexChartList) {
        // 查询采购成本下反转视角的数据
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
            priceIndexChartList = dmFocRecMonthCostIdxDao.findRevPriceIndexChartByMultiDim(monthAnalysisVO);
        }
        // 查询制造成本下反转视角的数据
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())) {
            setStartEndTime(monthAnalysisVO);
            priceIndexChartList = dmFocMadeRecMonthCostIdxDao.findMadeRevPriceIndexByMultiDim(monthAnalysisVO);
        }
        return priceIndexChartList;
    }

    private void getPriceIndexChartList(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexChartList) {
        if (monthAnalysisVO.getIsContainComb() && CollectionUtils.isNotEmpty(monthAnalysisVO.getCustomIdList())) {
            addCombMutilPriceIndexChartList(monthAnalysisVO, priceIndexChartList);
        } else {
            // 查询采购成本下的数据
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
                priceIndexChartList.addAll(dmFocMonthCostIdxDao.findPriceIndexChartByMultiDim(monthAnalysisVO));
            }
            // 查询制造成本下的数据
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())) {
                setStartEndTime(monthAnalysisVO);
                priceIndexChartList.addAll(dmFocMadeMonthCostIdxDao.findMadePriceIndexByMultiDim(monthAnalysisVO));
            }
            // 查询总成本下的数据
            if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(monthAnalysisVO.getCostType())) {
                Long actualMonthNum = dmFocMonthCostIdxDao.findActualMonthNum(monthAnalysisVO.getTablePreFix());
                monthAnalysisVO.setPeriodEndTime(Integer.valueOf(actualMonthNum.toString()));
                priceIndexChartList.addAll(dmFocTotalMonthCostIdxDao.findTotalPriceIndexByMultiDim(monthAnalysisVO));
            }
            // 非组合量纲层级groupCode和groupCnName做拼接
            splicingCodeAndName(priceIndexChartList);
        }
    }

    private List<List<DmFocMonthCostIdxVO>> resultDataGroupBy(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexChartList) {
        List<List<DmFocMonthCostIdxVO>> dataList = new ArrayList<>();
        priceIndexChartList = Optional.ofNullable(priceIndexChartList).orElse(new ArrayList<>());
        if (monthAnalysisVO.getIsMultipleSelect()) {
            Map<String, List<DmFocMonthCostIdxVO>> collect = priceIndexChartList.stream().collect(
                    Collectors.groupingBy(item -> getGroupKey(item), HashMap::new, Collectors.toList()));
            for (Map.Entry<String, List<DmFocMonthCostIdxVO>> entry : collect.entrySet()) {
                dataList.add(entry.getValue());
            }
        } else {
            Map<String, List<DmFocMonthCostIdxVO>> collect = priceIndexChartList.stream()
                    .collect(Collectors.groupingBy(DmFocMonthCostIdxVO::getGroupCode));
            for (Map.Entry<String, List<DmFocMonthCostIdxVO>> entry : collect.entrySet()) {
                dataList.add(entry.getValue());
            }
        }
        return dataList;
    }

    @Nullable
    private ResultDataVO setMultiDiemsionCharSearchVO(MonthAnalysisVO monthAnalysisVO) {
        if (StringUtils.isBlank(monthAnalysisVO.getIndustryOrg())) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        // 反向视角标识设置
        Boolean reveseViewFlag =FcstIndexUtil.isReveseViewFlag(monthAnalysisVO);
        monthAnalysisVO.setReverseViewFlag(reveseViewFlag);
        monthAnalysisVO.setParentCodeList(monthAnalysisVO.getGroupCodeList());
        monthAnalysisVO.setParentLevel(monthAnalysisVO.getGroupLevel());
        monthAnalysisVO.setVersionId(commonService.getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),monthAnalysisVO.getTablePreFix()));
        // 分视角获取下个层级的groupLevel值
        Map map = IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())
                || IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostSubType())
                ? FcstIndexMadeUtil.getNextGroupLevel(monthAnalysisVO)
                : FcstIndexUtil.getNextGroupLevel(monthAnalysisVO);;
        if (ObjectUtils.isEmpty(map.get(NEXT_GROUP_LEVEL)) || ObjectUtils.isEmpty(map.get(NEXT_GROUP_NAME))) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        monthAnalysisVO.setGroupLevel(map.get(NEXT_GROUP_LEVEL).toString());
        // 分视角设置下个层级名称cnName
        monthAnalysisVO.setCnName(map.get(NEXT_GROUP_NAME).toString());
        if (!monthAnalysisVO.getReverseViewFlag()) {
            commonService.setProdRndTeamCode(monthAnalysisVO);
        }
        // 第3步：检查基期开始时间和结束时间是否传参，若没有，则默认取报告期36个月的开始时间和结束时间
        FcstIndexUtil.handlePeriod(monthAnalysisVO, dmFocMonthCostIdxDao
                .findActualMonthNum(monthAnalysisVO.getTablePreFix()).toString());
        return null;
    }

    private void addCombMutilPriceIndexChartList(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexChartList) {
        distinguishIfCombine(monthAnalysisVO);
        // 非汇总组合的item数据
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getSubGroupCodeList())) {
            // 查询采购成本的数据
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
                priceIndexChartList.addAll(dmFocMonthCostIdxDao.findPriceIndexNormalChartByMultiDim(monthAnalysisVO));
            }
            // 查询采购成本的数据
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())) {
                setStartEndTime(monthAnalysisVO);
                priceIndexChartList.addAll(dmFocMadeMonthCostIdxDao.findMadePriceIndexNormalByMultiDim(monthAnalysisVO));
            }
            // 非组合量纲层级groupCode和groupCnName做拼接
            splicingCodeAndName(priceIndexChartList);
        }
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getCombinaSubGroupCodeList())) {
            // 汇总组合的查询
            List<DmFocMonthCostIdxVO> priceIndexCombChartByMultiDim = new ArrayList<>();
            // 查询采购成本的数据
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
                priceIndexCombChartByMultiDim = dmFocMonthCostIdxDao.findPriceIndexCombChartByMultiDim(monthAnalysisVO);
            }
            // 查询制造成本的数据
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())) {
                setStartEndTime(monthAnalysisVO);
                priceIndexCombChartByMultiDim = dmFocMadeMonthCostIdxDao.findMadePriceIndexCombByMultiDim(monthAnalysisVO);
            }
            priceIndexChartList.addAll(priceIndexCombChartByMultiDim);
        }
    }

    private void splicingCodeAndName(List<DmFocMonthCostIdxVO> priceIndexExpData) {
        if (CollectionUtils.isNotEmpty(priceIndexExpData)) {
            priceIndexExpData.stream().forEach(index -> {
                if (CommonConstant.GROUP_LEVEL.contains(index.getGroupLevel())) {
                    index.setGroupCnName(index.getGroupCode() + " " + index.getGroupCnName());
                }
            });
        }
    }

    private void distinguishIfCombine(MonthAnalysisVO monthAnalysisVO) {
        List<String> combinaCodeList = new ArrayList<>();
        List<String> parentCodeList = new ArrayList<>();
        List<String> allParentCodeList = monthAnalysisVO.getParentCodeList();
        monthAnalysisVO.setParentCodeList(null);
        setParentGroupCodeList(monthAnalysisVO, combinaCodeList, parentCodeList, allParentCodeList);
        setSubGroupCodeList(monthAnalysisVO);
    }

    private void setSubGroupCodeList(MonthAnalysisVO monthAnalysisVO) {
        if (CollectionUtils.isEmpty(monthAnalysisVO.getSubGroupCodeList())) {
            return;
        }
        List<String> subGroupCodeList = new ArrayList<>();
        List<String> subCombGroupCodeList = new ArrayList<>();
        List<String> allSubGroupCodeList = monthAnalysisVO.getSubGroupCodeList();
        monthAnalysisVO.setSubGroupCodeList(null);
        for (String subGroupCode : allSubGroupCodeList) {
            if (subGroupCode.contains("_##")) {
                subCombGroupCodeList.add(subGroupCode);
                monthAnalysisVO.setCombinaSubGroupCodeList(subCombGroupCodeList);
            } else {
                subGroupCodeList.add(subGroupCode);
                monthAnalysisVO.setSubGroupCodeList(subGroupCodeList);
            }
        }
    }

    private void setParentGroupCodeList(MonthAnalysisVO monthAnalysisVO, List<String> combinaCodeList, List<String> parentCodeList, List<String> allParentCodeList) {
        for (String groupCode : allParentCodeList) {
            if (groupCode.contains("_##")) {
                if ("null".equals(groupCode.split("_##")[1])) {
                    combinaCodeList.add(groupCode.split("_##")[0] + "_##" + groupCode.split("_##")[0]);
                }  else {
                    combinaCodeList.add(groupCode);
                }
                monthAnalysisVO.setCombinaCodeList(combinaCodeList);
            } else {
                parentCodeList.add(groupCode);
                monthAnalysisVO.setParentCodeList(parentCodeList);
            }
        }
    }

    private String getGroupKey(DmFocMonthCostIdxVO dmFocMonthCostIdxVO) {
        String dmsCode = dmFocMonthCostIdxVO.getDimensionCode() + "-" + dmFocMonthCostIdxVO.getDimensionSubCategoryCode() + "-" + dmFocMonthCostIdxVO.getDimensionSubDetailCode() + "-" + dmFocMonthCostIdxVO.getSpartCode() + "-";
        String profitCode = dmFocMonthCostIdxVO.getL1Name() + "-" + dmFocMonthCostIdxVO.getL2Name() + "-";
        return dmsCode + profitCode + dmFocMonthCostIdxVO.getProdRndTeamCode() + "-" + dmFocMonthCostIdxVO.getParentCode() + "-" + dmFocMonthCostIdxVO.getGroupCode() + "-" + dmFocMonthCostIdxVO.getCustomId();
    }

    private boolean setSearchInitParams(MonthAnalysisVO monthAnalysisVO) {
        // 必填参数校验
        if (StringUtils.isBlank(monthAnalysisVO.getIndustryOrg())) {
            return false;
        }
        // 分视角获取下个层级的groupLevel值
        Map map = new HashMap();
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType()) || IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostSubType())) {
            map = FcstIndexMadeUtil.getNextGroupLevel(monthAnalysisVO);
        } else {
            map = FcstIndexUtil.getNextGroupLevel(monthAnalysisVO);
        }
        if (ObjectUtils.isEmpty(map.get(NEXT_GROUP_LEVEL)) || ObjectUtils.isEmpty(map.get(NEXT_GROUP_NAME))) {
            return true;
        }
        monthAnalysisVO.setGroupLevel(map.get(NEXT_GROUP_LEVEL).toString());
        // 分视角设置下个层级名称cnName
        monthAnalysisVO.setCnName(map.get(NEXT_GROUP_NAME).toString());
        commonService.setProdRndTeamCode(monthAnalysisVO);
        return false;
    }

    @Override
    @JalorOperation(code = "getCostDistributionChart", desc = "自制制造成本分布图")
    public ResultDataVO getCostDistributionChart(MonthAnalysisVO monthAnalysisVO) throws CommonApplicationException {
        LOGGER.info("Begin MonthAnalysisService::getCostDistributionChart");
        monthCommonService.getUserPermission(monthAnalysisVO);
        // 成本类型只能是总成本类型
        if ( CollectionUtils.isEmpty(monthAnalysisVO.getGroupCodeList())
                || StringUtils.isAnyBlank(monthAnalysisVO.getViewFlag(), monthAnalysisVO.getIndustryOrg())) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        monthAnalysisVO.setVersionId(commonService.getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),monthAnalysisVO.getTablePreFix()));
        monthAnalysisVO.setSubGroupCodeList(monthAnalysisVO.getGroupCodeList());
        // 处理36个月的会计期
        Long actualMonthNum = dmFocMonthCostIdxDao.findActualMonthNum(monthAnalysisVO.getTablePreFix());
        FcstIndexUtil.handlePeriod(monthAnalysisVO, actualMonthNum.toString());
        monthAnalysisVO.setPeriodEndTime(Integer.valueOf(actualMonthNum.toString())); // 查询实际月份的数据
        return ResultDataVO.success(dmFocActualCostDao.findCostDistributionData(monthAnalysisVO));
    }

    /**
     * 产业成本权重图
     *
     * @param monthAnalysisVO 查询参数VO
     * @return ResultDataVO
     */
    @Override
    @JalorOperation(code = "getIndustryCostWeightChart", desc = "产业成本权重图")
    public ResultDataVO getIndustryCostWeightChart(MonthAnalysisVO monthAnalysisVO) {
        LOGGER.info("Begin MonthAnalysisService::getIndustryCostWeightChart");
        // 获取当前角色的数据范围
        monthCommonService.getUserPermission(monthAnalysisVO);
        monthAnalysisVO.setParentCodeList(monthAnalysisVO.getGroupCodeList());
        monthAnalysisVO.setParentLevel(monthAnalysisVO.getGroupLevel());
        monthAnalysisVO.setVersionId(commonService.getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(), monthAnalysisVO.getTablePreFix()));
        // 分视角获取下个层级的参数
        if (setSearchInitParams(monthAnalysisVO)) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        HashMap<String, Object> dataMap = new HashMap<>();
        List<DmFocMonthWeightVO> weightList = new ArrayList<>();
        if (monthAnalysisVO.getIsContainComb() && CollectionUtils.isNotEmpty(monthAnalysisVO.getCustomIdList())) {
            combWeightVOList(monthAnalysisVO, dataMap, weightList);
        } else {
            // 查询权重图数据
            getWeightList(monthAnalysisVO, weightList);
            weightVOList(monthAnalysisVO, dataMap, weightList);
        }
        return ResultDataVO.success(dataMap);
    }

    private void getWeightList(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthWeightVO> weightList) {
        // 查询采购成本下的权重图数据
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
            weightList.addAll(dmFocMonthWeightDao.findWeightList(monthAnalysisVO));
        }
        // 查询制造成本下的权重图数据
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())) {
            weightList.addAll(dmFocMadeMonthWeightDao.findMadeWeightList(monthAnalysisVO));
        }
        // 查询总成本下的权重图数据
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(monthAnalysisVO.getCostType())) {
            weightList.addAll(dmFocTotalMonthWeightDao.findTotalWeightList(monthAnalysisVO));
        }
        monthCommonService.splicingWeightCodeAndName(weightList);
    }

    private void combWeightVOList(MonthAnalysisVO monthAnalysisVO, HashMap<String, Object> dataMap, List<DmFocMonthWeightVO> weightList) {
        // 区分汇总组合
        distinguishIfCombine(monthAnalysisVO);
        // 设置分页查询
        monthAnalysisVO.setCurPage(0);
        monthAnalysisVO.setPageSize(11);
        monthAnalysisVO.setGroupLevel(GroupLevelEnumU.ITEM.getValue());
        // 第1步：取出当前层级下所有子项的权重并按权重从高到低排列
        if (CollectionUtils.isNotEmpty(monthAnalysisVO.getCombinaCodeList())) {
            // 汇总组合默认只查询一个
            // 查询采购成本下的数据
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
                weightList.addAll(dmFocMonthWeightDao.findCombWeightList(monthAnalysisVO));
            }
            // 查询制造成本下的数据
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())) {
                weightList.addAll(dmFocMadeMonthWeightDao.findMadeCombWeightList(monthAnalysisVO));
            }
            monthCommonService.splicingWeightParentCodeAndName(weightList);
        } else {
            // 非汇总组合默认只查询一个
            monthAnalysisVO.setCombGroupLevel(monthAnalysisVO.getParentLevel() + "_ITEM");
            // 查询采购成本下的数据
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
                weightList.addAll(dmFocMonthWeightDao.findNormalWeightList(monthAnalysisVO));
            }
            // 查询制造成本下的数据
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())) {
                weightList.addAll(dmFocMadeMonthWeightDao.findMadeNormalWeightList(monthAnalysisVO));
            }
        }
        weightList = Optional.ofNullable(weightList).orElse(new ArrayList<>());
        // 第2步：截取TOP10的数据并计算出TOP10权重之和sumWeight
        List<DmFocMonthWeightVO> top10WeightList = weightList.stream().limit(10).collect(Collectors.toList());
        double sumWeight = top10WeightList.stream()
                .collect(Collectors.summarizingDouble(DmFocMonthWeightVO::getWeightRate))
                .getSum();
        // 如果权重图个数小于10个 ，则不需要计算其他权重
        if (weightList.size() <= 10) {
            // item层级权重值需要加密处理
            monthAnalysisVO.setParentLevel(GroupLevelEnumU.CATEGORY.getValue());
            encryptWeightRate(monthAnalysisVO, top10WeightList, dataMap);
        } else {
            // 第3步：用 1 - sumWeight得到otherWeight其他权重之和 权重用百分号展示
            setOtherWeightValue(monthAnalysisVO, top10WeightList, sumWeight);
            // item层级权重值需要加密处理
            monthAnalysisVO.setParentLevel(GroupLevelEnumU.CATEGORY.getValue());
            encryptWeightRate(monthAnalysisVO, top10WeightList, dataMap);
        }
        List<Integer> numList = Stream.iterate(1, item -> item + 1).limit(weightList.size()).collect(Collectors.toList());
        // item层级需要转换成权重排序(按序列排序)
        for (int i = 0; i < weightList.size(); i++) {
            weightList.get(i).setWeightOrder(numList.get(i).toString());
        }
    }

    private void weightVOList(MonthAnalysisVO monthAnalysisVO, HashMap<String, Object> dataMap, List<DmFocMonthWeightVO> weightList) {
        if (monthAnalysisVO.getIsMultipleSelect()) {
            // 多选时父名称拼接
            monthCommonService.mutilSelectGroupCnName(monthAnalysisVO,null,null,weightList,null,"weightList");
            Map<String, List<DmFocMonthWeightVO>> mutiltop10WeightList = weightList.stream().collect(Collectors.groupingBy(x -> x.getParentCnName()));
            // 如果权重图个数小于10个 ，则不需要计算其他权重
            for (Map.Entry<String, List<DmFocMonthWeightVO>> mutilWeightVOLists : mutiltop10WeightList.entrySet()) {
                List<DmFocMonthWeightVO> weightVOList = mutilWeightVOLists.getValue();
                // 第2步：截取TOP10的数据并计算出TOP10权重之和sumWeight
                List<DmFocMonthWeightVO> top10WeightList = weightVOList.stream().limit(10).collect(Collectors.toList());
                if (weightVOList.size() <= 10) {
                    // item层级权重值需要加密处理
                    mutilEncryptWeightRate(monthAnalysisVO, top10WeightList);
                    dataMap.put(mutilWeightVOLists.getKey(), top10WeightList);
                } else {
                    // 第3步：用 1 - sumWeight得到otherWeight其他权重之和 权重用百分号展示
                    double sumWeightMutil = top10WeightList.stream()
                            .collect(Collectors.summarizingDouble(DmFocMonthWeightVO::getWeightRate))
                            .getSum();
                    setOtherWeightValue(monthAnalysisVO, top10WeightList, sumWeightMutil);
                    // item层级权重值需要加密处理
                    mutilEncryptWeightRate(monthAnalysisVO, top10WeightList);
                    dataMap.put(mutilWeightVOLists.getKey(), top10WeightList);
                }
            }
        } else {
            weightList = Optional.ofNullable(weightList).orElse(new ArrayList<>());
            // 第2步：截取TOP10的数据并计算出TOP10权重之和sumWeight
            List<DmFocMonthWeightVO> top10WeightList = weightList.stream().limit(10).collect(Collectors.toList());
            double sumWeight = top10WeightList.stream()
                    .collect(Collectors.summarizingDouble(DmFocMonthWeightVO::getWeightRate))
                    .getSum();
            // 如果权重图个数小于10个 ，则不需要计算其他权重
            if (weightList.size() <= 10) {
                // item层级权重值需要加密处理
                encryptWeightRate(monthAnalysisVO, top10WeightList, dataMap);
            } else {
                // 第3步：用 1 - sumWeight得到otherWeight其他权重之和 权重用百分号展示
                setOtherWeightValue(monthAnalysisVO, top10WeightList, sumWeight);
                // item层级权重值需要加密处理
                encryptWeightRate(monthAnalysisVO, top10WeightList, dataMap);
            }
        }
    }

    private void mutilEncryptWeightRate(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthWeightVO> top10WeightList) {
        // 校验是否为item层级，需要加密处理权重值
        Boolean viewFlag = IndustryIndexEnum.VIEW_FLAG_D.VIEW10.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D.VIEW11.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D.VIEW12.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D.VIEW13.getValue().equals(monthAnalysisVO.getViewFlag());
        if (GroupLevelEnumU.CATEGORY.getValue().equals(monthAnalysisVO.getParentLevel())
                || GroupLevelEnumMadeU.MANUFACTURE_OBJECT.getValue().equals(monthAnalysisVO.getParentLevel())
                || (viewFlag && GroupLevelEnumMadeD.SUB_DETAIL.getValue().equals(monthAnalysisVO.getParentLevel()))) {
            for (DmFocMonthWeightVO dmFocMonthWeightVO : top10WeightList) {
                double sinWeightRate = Math.sin((dmFocMonthWeightVO.getWeightRate()) / 180 * Math.PI);
                dmFocMonthWeightVO.setWeightRateStr(Double.toString(sinWeightRate));
                dmFocMonthWeightVO.setWeightRate(null);
                dmFocMonthWeightVO.setWeightPercent(null);
            }
            List<Integer> numList = Stream.iterate(1, item -> item + 1).limit(top10WeightList.size()).collect(Collectors.toList());
            // item层级需要转换成权重排序(按序列排序)
            for (int i = 0; i < top10WeightList.size(); i++) {
                top10WeightList.get(i).setWeightOrder(numList.get(i).toString());
            }
        }
    }

    private void encryptWeightRate(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthWeightVO> top10WeightList, HashMap<String, Object> dataMap) {
        Boolean viewFlag = IndustryIndexEnum.VIEW_FLAG_D.VIEW10.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D.VIEW11.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D.VIEW12.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D.VIEW13.getValue().equals(monthAnalysisVO.getViewFlag());
        // 校验是否为item层级，需要加密处理权重值
        if (GroupLevelEnumU.CATEGORY.getValue().equals(monthAnalysisVO.getParentLevel())
                || GroupLevelEnumMadeU.MANUFACTURE_OBJECT.getValue().equals(monthAnalysisVO.getParentLevel())
                || (viewFlag && GroupLevelEnumMadeD.SUB_DETAIL.getValue().equals(monthAnalysisVO.getParentLevel()))) {
            for (DmFocMonthWeightVO dmFocMonthWeightVO : top10WeightList) {
                if (null != dmFocMonthWeightVO.getWeightRate()) {
                    double sinWeightRate = Math.sin((dmFocMonthWeightVO.getWeightRate()) / 180 * Math.PI);
                    dmFocMonthWeightVO.setWeightRateStr(Double.toString(sinWeightRate));
                }
                dmFocMonthWeightVO.setWeightRate(null);
                dmFocMonthWeightVO.setWeightPercent(null);
            }
        }
        dataMap.put("top10WeightList", top10WeightList);
    }


    private void setOtherWeightValue(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthWeightVO> top10WeightList, double sumWeight) {
        if (top10WeightList.size() > 0) {
            // 用 1 - sumWeight得到otherWeight其他权重之和 权重用百分号展示
            double otherWeight = (1 - sumWeight) * 100;
            BigDecimal decimal = new BigDecimal(String.valueOf(otherWeight));
            otherWeight = decimal.setScale(1, RoundingMode.HALF_UP).doubleValue();
            DmFocMonthWeightVO otherWeightVO = DmFocMonthWeightVO.builder()
                    .parentCnName(top10WeightList.get(0).getParentCnName())
                    .parentCode(top10WeightList.get(0).getParentCode())
                    .groupCode(Constant.StrEnum.OTHER_WEIGHT.getValue().concat(monthAnalysisVO.getCnName()))
                    .groupCnName(Constant.StrEnum.OTHER_WEIGHT.getValue().concat(monthAnalysisVO.getCnName()))
                    .weightRate(1 - sumWeight)
                    .weightPercent(otherWeight + "%")
                    .build();
            if (monthAnalysisVO.getIsContainComb()) {
                otherWeightVO.setGroupCode(Constant.StrEnum.OTHER_WEIGHT.getValue().concat("ITEM"));
                otherWeightVO.setGroupCnName(Constant.StrEnum.OTHER_WEIGHT.getValue().concat("ITEM"));
            }
            top10WeightList.add(otherWeightVO);
        }
    }

    /**
     * 产业成本热力图
     *
     * @param monthAnalysisVO 查询参数VO
     * @return ResultDataVO
     */
    @Override
    @JalorOperation(code = "getIndustryCostHeatmapChart", desc = "产业成本热力图")
    public ResultDataVO getIndustryCostHeatmapChart(MonthAnalysisVO monthAnalysisVO) {
        LOGGER.info("Begin MonthAnalysisService::getIndustryCostHeatmapChart");
        // 第1步：校验必填参数 和设置查询条件参数
        monthCommonService.getUserPermission(monthAnalysisVO);
        if (CollectionUtils.isEmpty(monthAnalysisVO.getSubGroupCodeList()) || StringUtils.isBlank(monthAnalysisVO.getIndustryOrg())) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        monthAnalysisVO.setVersionId(commonService.getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),monthAnalysisVO.getTablePreFix()));
        monthAnalysisVO.setParentLevel(monthAnalysisVO.getGroupLevel());
        monthAnalysisVO.setParentCodeList(monthAnalysisVO.getGroupCodeList());
        // 第2步：检查基期开始时间和结束时间是否传参，若没有，则默认取报告期36个月的开始时间和结束时间
        FcstIndexUtil.handlePeriod(monthAnalysisVO, dmFocMonthCostIdxDao.findActualMonthNum(monthAnalysisVO.getTablePreFix()).toString());
        // 分视角获取下个层级的参数
        if (setSearchInitParams(monthAnalysisVO)) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        Map<String, Object> heatmapResult = new HashMap<>(5);
        try {
            if (monthAnalysisVO.getIsMultipleSelect()) {
                mutilSelectHeartmapChart(monthAnalysisVO, heatmapResult);
            } else {
                singleSelectHeartmapChart(monthAnalysisVO, heatmapResult);
            }
        } catch (Exception e) {
            LOGGER.error("MonthAnalysisService getIndustryCostHeatmapChart Exception: {}", e.getMessage());
            return ResultDataVO.failure(ResultCodeEnum.SERVER_ERROR);
        }
        LOGGER.info("End MonthAnalysisService::getIndustryCostHeatmapChart");
        return ResultDataVO.success(heatmapResult);
    }

    private void singleSelectHeartmapChart(MonthAnalysisVO monthAnalysisVO, Map<String, Object> heatmapResult) {
        // 获取总条数
        int curPage = monthAnalysisVO.getCurPage();
        int pageSize = monthAnalysisVO.getPageSize();
        monthAnalysisVO.setCurPage((curPage - 1) * pageSize);
        heatmapResult.put("total", monthAnalysisVO.getSubGroupCodeList().size());
        // 查询采购成本下的热力图数据
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
            // 根据层级不同查询层级code和name，并由高到低排序
            List<DmFocActualCostVO> dmFocActualCostVOList  = dmFocActualCostDao.findGroupCnNameByCode(monthAnalysisVO);
            monthCommonService.splicingHeatMapCodeAndName(dmFocActualCostVOList);
            List<String> groupCodeAndName = dmFocActualCostVOList.stream().map(ele->ele.getGroupCnName()).collect(Collectors.toList());
            heatmapResult.put("sorted", groupCodeAndName);
            // groupCode由高到低排序
            String groupCodeOrder = dmFocActualCostDao.findGroupCodeOrder(monthAnalysisVO);
            monthAnalysisVO.setGroupCodeOrder(groupCodeOrder);
            // 热力图结果
            List<DmFocActualCostVO> itemAndAmt = dmFocActualCostDao.findActualCostAmtList(monthAnalysisVO);
            monthCommonService.splicingHeatMapCodeAndName(itemAndAmt);
            setActualCostAmt(monthAnalysisVO, itemAndAmt);
            heatmapResult.put("data", itemAndAmt);
        }
        // 查询制造成本下的热力图数据 无预测数
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())) {
            Long actualMonthNum = dmFocMonthCostIdxDao.findActualMonthNum(monthAnalysisVO.getTablePreFix());
            monthAnalysisVO.setPeriodEndTime(Integer.valueOf(actualMonthNum.toString()));
            // 根据层级不同查询层级code和name，并由高到低排序
            List<DmFocActualCostVO> dmFocActualCostVOList  = dmFocMadeActualCostDao.findMadeGroupCnNameByCode(monthAnalysisVO);
            monthCommonService.splicingHeatMapCodeAndName(dmFocActualCostVOList);
            List<String> groupCodeAndName = dmFocActualCostVOList.stream().map(ele->ele.getGroupCnName()).collect(Collectors.toList());
            heatmapResult.put("sorted", groupCodeAndName);
            // groupCode由高到低排序
            String groupCodeOrder = dmFocMadeActualCostDao.findMadeGroupCodeOrder(monthAnalysisVO);
            monthAnalysisVO.setGroupCodeOrder(groupCodeOrder);
            // 热力图结果
            List<DmFocActualCostVO> itemAndAmt = dmFocMadeActualCostDao.findMadeActualCostAmtList(monthAnalysisVO);
            monthCommonService.splicingHeatMapCodeAndName(itemAndAmt);
            setActualCostAmt(monthAnalysisVO, itemAndAmt);
            heatmapResult.put("data", itemAndAmt);
        }
        // 查询总成本下的热力图数据 无预测数
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(monthAnalysisVO.getCostType())) {
            // 热力图结果
            Long actualMonthNum = dmFocMonthCostIdxDao.findActualMonthNum(monthAnalysisVO.getTablePreFix());
            monthAnalysisVO.setPeriodEndTime(Integer.valueOf(actualMonthNum.toString()));
            // 根据层级不同查询层级code和name，并由高到低排序
            List<DmFocActualCostVO> dmFocActualCostVOList  =dmFocTotalActualCostDao.findTotalGroupCnNameByCode(monthAnalysisVO);
            monthCommonService.splicingHeatMapCodeAndName(dmFocActualCostVOList);
            List<String> groupCodeAndName = dmFocActualCostVOList.stream().map(ele->ele.getGroupCnName()).collect(Collectors.toList());
            heatmapResult.put("sorted", groupCodeAndName);
            // groupCode由高到低排序
            String groupCodeOrder = dmFocTotalActualCostDao.findTotalGroupCodeOrder(monthAnalysisVO);
            monthAnalysisVO.setGroupCodeOrder(groupCodeOrder);
            List<DmFocActualCostVO> itemAndAmt = dmFocTotalActualCostDao.findTotalActualCostAmtList(monthAnalysisVO);
            monthCommonService.splicingHeatMapCodeAndName(itemAndAmt);
            setActualCostAmt(monthAnalysisVO, itemAndAmt);
            heatmapResult.put("data", itemAndAmt);
        }
    }

    private void mutilSelectHeartmapChart(MonthAnalysisVO monthAnalysisVO, Map<String, Object> heatmapResult) {
        List<DmFocActualCostVO> mutilGroupCnNameByCode = getMutilGroupCnNameByCode(monthAnalysisVO);
        // 采购层级名称拼接
        monthCommonService.heatMapMutilSelectGroupCnName(monthAnalysisVO, mutilGroupCnNameByCode);
        // 获取总条数
        heatmapResult.put("total", mutilGroupCnNameByCode.size());
        int curPage = monthAnalysisVO.getCurPage() - 1;
        int pageSize = monthAnalysisVO.getPageSize();
        // 分页处理
        if (mutilGroupCnNameByCode.size() > 0) {
            int fromIndex = curPage * pageSize;
            int totalIndex = (curPage + 1) * pageSize;
            if (totalIndex > mutilGroupCnNameByCode.size()) {
                totalIndex = mutilGroupCnNameByCode.size();
            }
            mutilGroupCnNameByCode = mutilGroupCnNameByCode.subList(fromIndex, totalIndex);
        }
        List<String> groupCnName = mutilGroupCnNameByCode.stream().map(DmFocActualCostVO::getGroupCnName).collect(Collectors.toList());
        heatmapResult.put("sorted", groupCnName);
        // groupCode由高到低排序
        String groupCodeOrder = mutilGroupCnNameByCode.stream().map(DmFocActualCostVO::getGroupCode).collect(Collectors.joining(","));
        monthAnalysisVO.setGroupCodeOrder(groupCodeOrder);
        // 热力图结果
        List<DmFocActualCostVO> itemAndAmt = new ArrayList<>();
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
            itemAndAmt = dmFocActualCostDao.findActualCostAmtList(monthAnalysisVO);
        }
        // 制造成本下的热力图数据 无预测数
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())) {
            Long actualMonthNum = dmFocMonthCostIdxDao.findActualMonthNum(monthAnalysisVO.getTablePreFix());
            monthAnalysisVO.setPeriodEndTime(Integer.valueOf(actualMonthNum.toString()));
            itemAndAmt = dmFocMadeActualCostDao.findMadeActualCostAmtList(monthAnalysisVO);
        }
        // 总成本下的热力图数据 无预测数
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(monthAnalysisVO.getCostType())) {
            Long actualMonthNum = dmFocMonthCostIdxDao.findActualMonthNum(monthAnalysisVO.getTablePreFix());
            monthAnalysisVO.setPeriodEndTime(Integer.valueOf(actualMonthNum.toString()));
            itemAndAmt = dmFocTotalActualCostDao.findTotalActualCostAmtList(monthAnalysisVO);
        }
        monthCommonService.splicingHeatMapCodeAndName(itemAndAmt);
        // 采购层级名称拼接
        monthCommonService.heatMapMutilSelectGroupCnName(monthAnalysisVO,  itemAndAmt);
        setActualCostAmt(monthAnalysisVO, itemAndAmt);
        heatmapResult.put("data", itemAndAmt);
    }

    private void setActualCostAmt(MonthAnalysisVO monthAnalysisVO, List<DmFocActualCostVO> itemAndAmt) {
        // 金额字段做公式转换
        Boolean viewFlag = IndustryIndexEnum.VIEW_FLAG_D.VIEW10.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D.VIEW11.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D.VIEW12.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D.VIEW13.getValue().equals(monthAnalysisVO.getViewFlag());
        Boolean spartPurchaseLevel1 = GroupLevelEnumD.SPART.getValue().equals(monthAnalysisVO.getGroupLevel())
                || GroupLevelEnumD.CEG.getValue().equals(monthAnalysisVO.getGroupLevel());
        Boolean spartPurchaseLevel = spartPurchaseLevel1 || GroupLevelEnumD.MODL.getValue().equals(monthAnalysisVO.getGroupLevel())
                || GroupLevelEnumD.CATEGORY.getValue().equals(monthAnalysisVO.getGroupLevel())
                || GroupLevelEnumD.ITEM.getValue().equals(monthAnalysisVO.getGroupLevel());
        Boolean spartPurchaseFlag =  viewFlag && spartPurchaseLevel;
        Boolean spartMadeLevel = GroupLevelEnumMadeD.SPART.getValue().equals(monthAnalysisVO.getGroupLevel())
                || GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue().equals(monthAnalysisVO.getGroupLevel())
                || GroupLevelEnumMadeD.MANUFACTURE_OBJECT.getValue().equals(monthAnalysisVO.getGroupLevel())
                || GroupLevelEnumMadeD.ITEM.getValue().equals(monthAnalysisVO.getGroupLevel());
        Boolean spartMadeFlag =  viewFlag && spartMadeLevel;
        // 校验是否为spart层级及后面层级，需要加密处理金额值
        if (spartMadeFlag || spartPurchaseFlag) {
            itemAndAmt.stream().forEach(ele->{
                if (null != ele.getActualCostAmt()) {
                    double sinWeightRate = ele.getActualCostAmt() * 1.5 / 0.8 - 10.3;
                    ele.setActualCostAmt(sinWeightRate);
                }
            });
        }
    }

    private List<DmFocActualCostVO> getMutilGroupCnNameByCode(MonthAnalysisVO monthAnalysisVO) {
        List<DmFocActualCostVO> mutilGroupCnNameByCode = new ArrayList<>();
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
            // 根据层级不同查询层级code和name，并由高到低排序
            mutilGroupCnNameByCode = dmFocActualCostDao.findMutilGroupCnNameByCode(monthAnalysisVO);
        }
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())) {
            Long actualMonthNum = dmFocMonthCostIdxDao.findActualMonthNum(monthAnalysisVO.getTablePreFix());
            monthAnalysisVO.setPeriodEndTime(Integer.valueOf(actualMonthNum.toString()));
            // 根据层级不同查询层级code和name，并由高到低排序
            mutilGroupCnNameByCode = dmFocMadeActualCostDao.findMadeMutilGroupCnNameByCode(monthAnalysisVO);
        }
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(monthAnalysisVO.getCostType())) {
            Long actualMonthNum = dmFocMonthCostIdxDao.findActualMonthNum(monthAnalysisVO.getTablePreFix());
            monthAnalysisVO.setPeriodEndTime(Integer.valueOf(actualMonthNum.toString()));
            // 根据层级不同查询层级code和name，并由高到低排序
            mutilGroupCnNameByCode = dmFocTotalActualCostDao.findTotalMutilGroupCnNameByCode(monthAnalysisVO);
        }
        // 非组合量纲层级groupCode和groupCnName做拼接
        monthCommonService.splicingHeatMapCodeAndName(mutilGroupCnNameByCode);
        return mutilGroupCnNameByCode;
    }

    @Override
    @JalorOperation(code = "getAmpChartList", desc = "查询涨跌根因分析")
    public ResultDataVO getAmpChartList(MonthAnalysisVO monthAnalysisVO, HttpServletRequest request) throws ApplicationException {
        ResultDataVO PARAM_ERROR = setMultiDiemsionCharSearchVO(monthAnalysisVO);
        if (PARAM_ERROR != null) {
            return PARAM_ERROR;
        }
        monthCommonService.getUserPermission(monthAnalysisVO);
        List<DmFocMonthCostIdxVO> priceIndexChartList = new ArrayList<>();
        // 查询指数和权重
        getAmpPriceIndexWeightList(monthAnalysisVO, priceIndexChartList);
        Map<String, List<DmFocMonthCostIdxVO>> priceIndexChartMap = priceIndexChartList.stream()
                .collect(Collectors.groupingBy(DmFocMonthCostIdxVO::getGroupCode));

        Iterator<Map.Entry<String, List<DmFocMonthCostIdxVO>>> iterator = priceIndexChartMap.entrySet().iterator();
        String rules = "";
        AmpParamVO ampParamVO = new AmpParamVO();
        List<Factors> factorsList = new ArrayList<>();
        Integer length = 0;
        while (iterator.hasNext()) {
            Map.Entry<String, List<DmFocMonthCostIdxVO>> entryNext = iterator.next();
            List<DmFocMonthCostIdxVO> dmFomMonthCostIdxList = entryNext.getValue();

            List<Double> weightRateSet = dmFomMonthCostIdxList.stream().map(DmFocMonthCostIdxVO::getWeightRate).collect(Collectors.toList());
            Factors factors = new Factors();
            factors.setCode(dmFomMonthCostIdxList.get(0).getGroupCode());
            Object[] costIdxArray = dmFomMonthCostIdxList.stream().map(DmFocMonthCostIdxVO::getCostIndex).collect(Collectors.toList()).toArray();
            length = length + costIdxArray.length;
            Long time = System.nanoTime();
            factors.setId("__" + time + "__");
            if (GroupLevelAllEnum.ITEM.getValue().equals(monthAnalysisVO.getGroupLevel()) && IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())) {
                factors.setName(dmFomMonthCostIdxList.get(0).getGroupCode());
            } else {
                factors.setName(dmFomMonthCostIdxList.get(0).getGroupCnName());
            }
            factors.setValue(costIdxArray);
            factorsList.add(factors);
            if (!iterator.hasNext()) {
                rules = rules + "__" + time + "__*" + weightRateSet.get(0);
            } else {
                rules = rules + "__" + time + "__*" + weightRateSet.get(0) + "+";
            }
        }
        rules = "=" + rules;
        ampParamVO.setFactors(factorsList);
        ampParamVO.setRules(rules);

        String jsonParams = JSON.toJSONString(ampParamVO);
        LOGGER.info("产业请求涨跌根因分析参数:{}",jsonParams);
        if (factorsList.size() *2 != length) {
            LOGGER.info("指数数据不匹配,无法根因分析");
            return ResultDataVO.success();
        }
        LOGGER.info("产业开始调用python请求涨跌根因分析");
        String requestUrl = registryQueryService.findValueByPath(Constant.StrEnum.QUERY_EXPLAIN_CHART_URL.getValue(), true);

        String result = RestUtil.doPostByAuthorization(requestUrl, accessTokenClient.accessToken(), jsonParams, request);
        LOGGER.info("产业根因分析结束调用");
        return JSONObject.parseObject(result, ResultDataVO.class);
    }

    private void getAmpPriceIndexWeightList(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthCostIdxVO> priceIndexChartList) {
        if (monthAnalysisVO.getReverseViewFlag()) {
            priceIndexChartList.addAll(getRevPriceIndexWeightList(monthAnalysisVO));
        } else {
            // 查询采购成本下的数据
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
                priceIndexChartList.addAll(dmFocMonthCostIdxDao.findAmpPurchasePriceIndexChart(monthAnalysisVO));
            }
            // 查询制造成本下的数据
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())) {
                setStartEndTime(monthAnalysisVO);
                priceIndexChartList.addAll(dmFocMadeMonthCostIdxDao.findAmpMadePriceIndexChart(monthAnalysisVO));
            }
            // 查询总成本下的数据
            if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(monthAnalysisVO.getCostType())) {
                Long actualMonthNum = commonService.findActualMonthNum(monthAnalysisVO.getIndustryOrg());
                monthAnalysisVO.setPeriodEndTime(Integer.valueOf(actualMonthNum.toString()));
                priceIndexChartList.addAll(dmFocTotalMonthCostIdxDao.findTotalAmpMadePriceIndexChart(monthAnalysisVO));
            }
        }
    }

    private List<DmFocMonthCostIdxVO> getRevPriceIndexWeightList(MonthAnalysisVO monthAnalysisVO) {
        // 查询采购成本下反向视角，指数和权重
        List<DmFocMonthCostIdxVO> priceIndexChartList = new ArrayList<>();
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
            priceIndexChartList = dmFocRecMonthCostIdxDao.findRevAmpPurchasePriceIndexChart(monthAnalysisVO);
        }
        // 查询制造成本下反转视角的数据
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(monthAnalysisVO.getCostType())) {
            setStartEndTime(monthAnalysisVO);
            priceIndexChartList = dmFocMadeRecMonthCostIdxDao.findRevMadeAmpPurchasePriceIndexChart(monthAnalysisVO);
        }
        return priceIndexChartList;
    }

    /**
     * 明细数据下载
     *
     * @param monthAnalysisVO 查询参数VO
     * @param response        响应
     * @return ResultDataVO
     * @throws Exception
     */
    @Audit(module = "monthAnalysisService-detailDataExport", operation = "detailDataExport", message = "明细数据下载")
    @Transactional(rollbackFor = Exception.class)
    @Override
    @JalorOperation(code = "detailDataExport", desc = "明细数据下载")
    public ResultDataVO detailDataExport(MonthAnalysisVO monthAnalysisVO, HttpServletResponse response) throws Exception {
        long start = System.currentTimeMillis();
        LOGGER.info("Begin MonthAnalysisService::detailDataExport and monthAnalysisVO:{}", JSON.toJSONString(monthAnalysisVO));
        // 入参校验
        if (ObjectUtils.isEmpty(monthAnalysisVO.getBasePeriodId()) || CollectionUtils.isEmpty(monthAnalysisVO.getGroupCodeList())
                || StringUtils.isAnyBlank(monthAnalysisVO.getViewFlag(), monthAnalysisVO.getIndustryOrg())) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        if (StringUtils.isEmpty(monthAnalysisVO.getCaliberFlag()) || StringUtils.isEmpty(monthAnalysisVO.getGranularityType())) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        monthCommonService.getUserPermission(monthAnalysisVO);
        // 反向视角标识设置
        Boolean reveseViewFlag = FcstIndexUtil.isReveseViewFlag(monthAnalysisVO);
        monthAnalysisVO.setReverseViewFlag(reveseViewFlag);
        String exportTemplate = getExportTemplate(monthAnalysisVO);
        // 导出模块为空表示无权限下载，直接返回
        if (StringUtils.isEmpty(exportTemplate)) {
            return ResultDataVO.failure(ResultCodeEnum.NOT_PERMISSION.getCode(),
                    Constant.StrEnum.NO_PERMISSION_TO_DOWNLOAD.getValue());
        }
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate(exportTemplate);
        String groupCnName;
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
            groupCnName = commonService.getGroupCnName(monthAnalysisVO);
        } else {
            groupCnName = commonService.getManufactureGroupCnName(monthAnalysisVO);
        }
        monthAnalysisVO.setVersionId(commonService.getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),monthAnalysisVO.getTablePreFix()));
        monthAnalysisVO.setParentLevel(monthAnalysisVO.getGroupLevel());
        // 导出模板对应的数据
        CombTransformVO combTransformVO = new CombTransformVO();
        DmFocDataRefreshStatus dataRefreshStatus = new DmFocDataRefreshStatus();
        dataRefreshStatus.setStatus("TASK_INIT");
        dataRefreshStatus.setDelFlag("N");
        Long userId = UserInfoUtils.getUserId();
        dataRefreshStatus.setCreatedBy(userId);
        dataRefreshStatus.setLastUpdatedBy(userId);
        dataRefreshStatus.setCreationDate(new Date());
        dataRefreshStatus.setTaskFlag("MONTH_EXPORT_" + monthAnalysisVO.getIndustryOrg() + "_"
                + monthAnalysisVO.getCaliberFlag() + "_"+ monthAnalysisVO.getPageId());
        dataRefreshStatus.setTaskId(dataRefreshStatusDao.getDataRefrashKey());
        dataRefreshStatus.setRoleId(UserInfoUtils.getRoleId());
        // 创建异步任务记录，进行新增操作
        dataRefreshStatusDao.createDmFocDataRefreshStatus(dataRefreshStatus);
        combTransformVO.setTaskId(dataRefreshStatus.getTaskId());
        combTransformVO.setUserId(userId);
        IRequestContext current = RequestContextManager.getCurrent();
        combTransformVO.setCurrent(current);
        // 设置创建时间
        monthAnalysisVO.setCreationDate(new Timestamp(start));
        asyncService.exportMonthData(monthAnalysisVO, workbook, groupCnName, combTransformVO);
        LOGGER.info("End MonthAnalysisService::detailDataExport and total time:{}", (System.currentTimeMillis() - start));
        return ResultDataVO.success(dataRefreshStatus);
    }

    @Override
    @Audit(module = "monthAnalysisService-detailDataExportVaild", operation = "detailDataExportVaild", message = "明细数据多指数图数据校验")
    @JalorOperation(code = "detailDataExportVaild", desc = "明细数据多指数图数据校验")
    public ResultDataVO detailDataExportVaild(MonthAnalysisVO monthAnalysisVO) throws CommonApplicationException {
        ResultDataVO PARAM_ERROR = setMultiDiemsionCharSearchVO(monthAnalysisVO);
        if (PARAM_ERROR != null) {
            return PARAM_ERROR;
        }
        monthCommonService.getUserPermission(monthAnalysisVO);
        List<DmFocMonthCostIdxVO> priceIndexChartList = new ArrayList<>();
        if (monthAnalysisVO.getIsContainComb() && CollectionUtils.isNotEmpty(monthAnalysisVO.getCustomIdList())) {
            distinguishIfCombine(monthAnalysisVO);
            // 非组合的item数据
            if (CollectionUtils.isNotEmpty(monthAnalysisVO.getParentCodeList())) {
                if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
                    priceIndexChartList.addAll(dmFocMonthCostIdxDao.findPriceIndexNormalChartByMultiDim(monthAnalysisVO));
                } else {
                    priceIndexChartList.addAll(dmFocMadeMonthCostIdxDao.findMadePriceIndexNormalByMultiDim(monthAnalysisVO));
                }
            }
            if (CollectionUtils.isNotEmpty(monthAnalysisVO.getCombinaCodeList())) {
                // 汇总组合的查询
                if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
                    List<DmFocMonthCostIdxVO> purchasePriceIndexCombChartByMultiDim = dmFocMonthCostIdxDao.findPriceIndexCombChartByMultiDim(monthAnalysisVO);
                    priceIndexChartList.addAll(purchasePriceIndexCombChartByMultiDim);
                } else {
                    List<DmFocMonthCostIdxVO> madePriceIndexCombChartByMultiDim = dmFocMadeMonthCostIdxDao.findMadePriceIndexCombByMultiDim(monthAnalysisVO);
                    priceIndexChartList.addAll(madePriceIndexCombChartByMultiDim);
                }
            }
        }
        return ResultDataVO.success(priceIndexChartList.size());
    }

    private String getExportTemplate(MonthAnalysisVO monthAnalysisVO) {
        String exportTemplate;
        if (!monthAnalysisVO.getIsShowPriceChart()) {
            // 不下载指数图
            exportTemplate = Constant.StrEnum.INDUSTRY_COST_INDEX_TEMPLATE6_PATH.getValue();
        } else if (!monthAnalysisVO.getIsShowChildContent()) {
            // 组合多选时，视角路径不同，只看最后一个重量级团队对应的ITEM
            exportTemplate = Constant.StrEnum.INDUSTRY_COST_INDEX_TEMPLATE5_PATH.getValue();
        } else if (!monthAnalysisVO.getIsTotalChildChart()) {
            exportTemplate = Constant.StrEnum.INDUSTRY_COST_INDEX_TEMPLATE7_PATH.getValue();
        } else if (monthAnalysisVO.getIsContainComb()) {
            exportTemplate = getTemplate3Path();
        } else {
            exportTemplate = getNormalTemplate(monthAnalysisVO);
        }
        return exportTemplate;
    }

    private String getNormalTemplate(MonthAnalysisVO monthAnalysisVO) {
        String exportTemplate;
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(monthAnalysisVO.getGranularityType())) {
            exportTemplate = getExpDimensionTemplateByLevel(monthAnalysisVO.getGroupLevel());
        } else if (IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(monthAnalysisVO.getGranularityType())) {
            exportTemplate = getExpUniversalTemplateByLevel(monthAnalysisVO.getGroupLevel(), monthAnalysisVO.getViewFlag());
        } else {
            exportTemplate = getExpProfitTemplateByLevel(monthAnalysisVO.getGroupLevel());
        }
        return exportTemplate;
    }

    private String getExpProfitTemplateByLevel(String groupLevel) {
        String expTemplatePath;
        GroupLevelAllEnum levelEnums = GroupLevelAllEnum.getInstance(groupLevel);
        switch (levelEnums) {
            /**
             * LV0 LV1 LV2 L1 L2层级
             * 产业成本价格指数图 产业成本价格指数图(多指数) 产业成本价格指数图(同步环比) 权重图  热力图  template1
             */
            case LV0:
            case LV1:
            case LV2:
            case L1:
            case L2:
                expTemplatePath = Constant.StrEnum.INDUSTRY_COST_INDEX_TEMPLATE1_PATH.getValue();
                break;
            case CEG:
            case MODL:
            case SHIPPING_OBJECT:
                expTemplatePath = getTemplate2Path();
                break;
            case CATEGORY:
            case MANUFACTURE_OBJECT:
                expTemplatePath = getTemplate3Path();
                break;
            default:
                expTemplatePath = null;
                break;
        }
        return expTemplatePath;
    }

    private String getTemplate3Path() {
        String expTemplatePath; /**
         * 品类层级
         * 产业成本价格指数图 产业成本价格指数图(多ITEM指数) 产业成本价格指数图(同步环比) 权重图(ITEM) template3
         */
        expTemplatePath = Constant.StrEnum.INDUSTRY_COST_INDEX_TEMPLATE3_PATH.getValue();
        return expTemplatePath;
    }

    private String getTemplate2Path() {
        /**
         * 模块 专项采购认证部层级
         * 产业成本价格指数图 产业成本价格指数图(多品类指数) 产业成本价格指数图(同步环比) 权重图(品类层级) 热力图(品类层级)  template2
         */
        return Constant.StrEnum.INDUSTRY_COST_INDEX_TEMPLATE2_PATH.getValue();
    }

    private String getExpUniversalTemplateByLevel(String groupLevel, String viewFlag) {
        String expTemplatePath;
        if (IndustryIndexEnum.VIEW_FLAG_U.VIEW5.getValue().equals(viewFlag) || IndustryIndexEnum.VIEW_FLAG_U.VIEW6.getValue().equals(viewFlag) || IndustryIndexEnum.VIEW_FLAG_U.VIEW7.getValue().equals(viewFlag)) {
            return Constant.StrEnum.INDUSTRY_COST_INDEX_TEMPLATE4_PATH.getValue();
        }
        GroupLevelAllEnum levelEnums = GroupLevelAllEnum.getInstance(groupLevel);
        switch (levelEnums) {
            /**
             * LV0 LV1 LV2 LV3,DIMENSION,SUBCATEGORY,SUB_DETAIL层级
             * 产业成本价格指数图 产业成本价格指数图(多指数) 产业成本价格指数图(同步环比) 权重图  热力图  template1
             */
            case LV0:
            case LV1:
            case LV2:
            case LV3:
            case LV4:
                expTemplatePath = Constant.StrEnum.INDUSTRY_COST_INDEX_TEMPLATE1_PATH.getValue();
                break;
            case MODL:
            case CEG:
            case SHIPPING_OBJECT:
                /**
                 * 模块 专项采购认证部层级
                 * 产业成本价格指数图 产业成本价格指数图(多品类指数) 产业成本价格指数图(同步环比) 权重图(品类层级) 热力图(品类层级)  template2
                 */
                expTemplatePath = getTemplate2Path();
                break;
            case CATEGORY:
            case MANUFACTURE_OBJECT:
                /**
                 * 品类层级
                 * 产业成本价格指数图 产业成本价格指数图(多ITEM指数) 产业成本价格指数图(同步环比) 权重图(ITEM) template3
                 */
                expTemplatePath = getTemplate3Path();
                break;
            default:
                expTemplatePath = null;
                break;
        }
        return expTemplatePath;
    }

    private String getExpDimensionTemplateByLevel(String groupLevel) {
        String expTemplatePath;
        GroupLevelAllEnum levelEnums = GroupLevelAllEnum.getInstance(groupLevel);
        switch (levelEnums) {
            /**
             * LV0 LV1 LV2 LV3层级
             * 产业成本价格指数图 产业成本价格指数图(多指数) 产业成本价格指数图(同步环比) 权重图  热力图  template1
             */
            case LV0:
            case LV1:
            case LV2:
            case LV3:
            case LV4:
            case DIMENSION:
            case SUBCATEGORY:
            case SUB_DETAIL:
            case SPART:
                expTemplatePath = Constant.StrEnum.INDUSTRY_COST_INDEX_TEMPLATE1_PATH.getValue();
                break;
            case CATEGORY:
            case MANUFACTURE_OBJECT:
                /**
                 * 品类层级
                 * 产业成本价格指数图 产业成本价格指数图(多ITEM指数) 产业成本价格指数图(同步环比) 权重图(ITEM) template3
                 */
                expTemplatePath = getTemplate3Path();
                break;
            case CEG:
            case MODL:
            case SHIPPING_OBJECT:
                /**
                 * 专项采购认证部层级
                 * 产业成本价格指数图 产业成本价格指数图(多品类指数) 产业成本价格指数图(同步环比) 权重图(品类层级) 热力图(品类层级)  template2
                 */
                expTemplatePath = getTemplate2Path();
                break;
            default:
                expTemplatePath = null;
                break;
        }
        return expTemplatePath;
    }
}

