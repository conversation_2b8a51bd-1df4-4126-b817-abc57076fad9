/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.month;

import com.fasterxml.jackson.annotation.JsonFormat;

import com.huawei.it.fcst.annotations.ExportAttribute;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * The Entity of DmFocActualCostT
 *
 * <AUTHOR>
 * @since 2023/03/10
 */
@Getter
@Setter
@NoArgsConstructor
public class DmFocActualCostVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("版本ID")
    private Long versionId;

    @ApiModelProperty("会计年")
    private Long periodYear;

    @ApiModelProperty("会计期")
    @ExportAttribute(sort = 0, dataType = "Number")
    private Long periodId;

    @ApiModelProperty("重量级团队CODE")
    private String prodRndTeamCode;

    @ApiModelProperty("量级团队中文名称")
    private String prodRndTeamCnName;

    @ApiModelProperty("分层级CODE")
    private String groupCode;

    @ApiModelProperty("分层级中文名称")
    @ExportAttribute(sort = 3)
    private String groupCnName;

    @ApiModelProperty("GROUP层级(ICT/LV1/LV2/CEG/CATEGORY/ITEM)")
    private String groupLevel;

    @ApiModelProperty("实际发货额")
    @ExportAttribute(sort = 4)
    private Double actualCostAmt;

    @ApiModelProperty("父级CODE")
    private String parentCode;

    @ApiModelProperty("父级CnName")
    @ExportAttribute(sort = 2)
    private String parentCnName;

    @ApiModelProperty("创建人")
    private String createdBy;

    @ApiModelProperty("创建日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private Timestamp creationDate;

    @ApiModelProperty("最后更新人")
    private String lastUpdatedBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    @ApiModelProperty("最后更新日期")
    private Timestamp lastUpdateDate;

    @ApiModelProperty("删除标识(未删除：N，已删除：Y)")
    private String delFlag;

    @ApiModelProperty("视角标识，用于区分不同视角下的品类数据(0: ICT层级, 1:ICT-LV1层级, 2:ICT-LV1-LV2层级)")
    private String viewFlag;

    @ApiModelProperty("l1Name")
    private String l1Name;

    @ApiModelProperty("l2Name")
    private String l2Name;

    @ApiModelProperty("量纲颗粒度code")
    private String dmsCode;

    @ApiModelProperty("量纲颗粒度名称")
    private String dmsCnName;

    // COA编码
    private String coaCode;

    // COA名称
    private String coaCnName;

    @ApiModelProperty("量纲颗粒度code")
    private String dimensionSubCategoryCode;

    @ApiModelProperty("量纲颗粒度名称")
    private String dimensionSubCategoryCnName;

    @ApiModelProperty("量纲颗粒度code")
    private String dimensionSubDetailCode;

    @ApiModelProperty("量纲颗粒度名称")
    private String dimensionSubDetailCnName;

    @ApiModelProperty("spart code")
    private String spartCode;

    @ApiModelProperty("spart名称")
    private String spartCnName;

    @ApiModelProperty("量纲颗粒度code")
    private String dimensionCode;

    @ApiModelProperty("量纲颗粒度名称")
    private String dimensionCnName;

    @ApiModelProperty("采购金额")
    private Double purAmt;

    @ApiModelProperty("采购占比")
    private Double purWeight;

    @ApiModelProperty("制造金额")
    private Double madeAmt;

    @ApiModelProperty("制造占比")
    private Double madeWeight;

    @ApiModelProperty("总成本金额")
    private Double totalAmt;

    /**
     * 顶部的成本类型下拉框: 制造成本：MANUFACTURE，采购成本：PURCHASE，总成本：TOTAL
     *
     */
    @ExportAttribute(sort = 1)
    private String costType;

}
