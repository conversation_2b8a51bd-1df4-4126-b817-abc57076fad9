/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.mqs;

import com.huawei.his.mqs.common.message.Message;
import com.huawei.it.fcst.industry.index.impl.dataprocess.TaskExecutorProcessService;

import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * MessageConsumerTest Class
 *
 * <AUTHOR>
 * @since 2023/7/17
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class MessageConsumerTest {
    private static final Logger LOGGER = LoggerFactory.getLogger(MessageConsumer.class);

    @InjectMocks
    private MessageConsumer messageConsumer;

    @Mock
    private TaskExecutorProcessService taskExecutorProcessService;

    @Test
    public void consumeMessage() {
        Message message=new Message();
        message.setTags("T");
        try {
            messageConsumer.consumeMessage(message);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNull(null);
    }

    @Test
    public void run() {
        try {
            messageConsumer.run();
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNull(null);
    }

    @Test
    public void stopConsumer() {
        try {
            messageConsumer.stopConsumer();
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNull(null);
    }
}