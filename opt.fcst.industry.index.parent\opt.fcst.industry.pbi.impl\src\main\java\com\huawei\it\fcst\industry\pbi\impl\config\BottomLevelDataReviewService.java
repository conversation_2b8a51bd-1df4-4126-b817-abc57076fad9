/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.config;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.huawei.it.fcst.common.StatisticsExcelService;
import com.huawei.it.fcst.enums.CommonConstEnum;
import com.huawei.it.fcst.enums.ResultCodeEnum;
import com.huawei.it.fcst.export.vo.PbiDmFoiImpExpRecordVO;
import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstIctRawDataExamineDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstVersionInfoDao;
import com.huawei.it.fcst.industry.pbi.enums.GroupLevelEnum;
import com.huawei.it.fcst.industry.pbi.enums.IndustryConstEnum;
import com.huawei.it.fcst.industry.pbi.impl.common.AsyncIctQueryService;
import com.huawei.it.fcst.industry.pbi.impl.template.DataReviewTemplateEnum;
import com.huawei.it.fcst.industry.pbi.service.common.IIctCommonService;
import com.huawei.it.fcst.industry.pbi.service.config.IBottomLevelDataReviewService;
import com.huawei.it.fcst.export.IExcelTemplateBeanManager;
import com.huawei.it.fcst.export.IExportProcessorService;
import com.huawei.it.fcst.industry.pbi.vo.config.BottomLevelDataReviewVO;
import com.huawei.it.fcst.industry.pbi.vo.config.DataReviewVO;
import com.huawei.it.fcst.industry.pbi.vo.config.DmFcstIctRawDataExamineDTO;
import com.huawei.it.fcst.industry.pbi.vo.config.ExamineVO;
import com.huawei.it.fcst.industry.pbi.vo.version.DmFcstVersionInfoDTO;
import com.huawei.it.fcst.industry.pbi.vo.version.DmFcstVersionInfoVO;
import com.huawei.it.fcst.util.ExcelExportUtil;
import com.huawei.it.fcst.util.ExcelUtils;
import com.huawei.it.fcst.util.UserInfoUtils;
import com.huawei.it.fcst.vo.AbstractExcelTitleVO;
import com.huawei.it.fcst.vo.ExcelVO;
import com.huawei.it.fcst.vo.ExportExcelVo;
import com.huawei.it.fcst.vo.HeaderVo;
import com.huawei.it.fcst.vo.LeafExcelTitleVO;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.fcst.vo.UploadInfoVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.core.util.StreamUtil;
import com.huawei.it.jalor5.registry.service.IRegistryQueryService;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Named;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/9/6
 */
@Slf4j
@Named("bottomLevelDataReviewService")
@JalorResource(code = "bottomLevelDataReviewService", desc = "NEW ICT-底层数据审视")
public class BottomLevelDataReviewService implements IBottomLevelDataReviewService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BottomLevelDataReviewService.class);

    @Autowired
    private IDmFcstIctRawDataExamineDao dmFcstIctRawDataExamineDao;

    @Autowired
    private IExportProcessorService exportProcessorService;

    @Autowired
    private IDmFcstVersionInfoDao dmFcstVersionInfoDao;

    @Autowired
    private IIctCommonService ictCommonService;

    @Autowired
    private ExcelUtils excelUtils;

    @Autowired
    private IRegistryQueryService registryQueryService;

    @Autowired
    private StatisticsExcelService statisticsExcelService;

    @Autowired
    private AsyncIctQueryService asyncIctQueryService;

    private static final Pattern DATE_PATTERN = Pattern.compile("\\d{4}(0[1-9]|1[0-2])");

    private static final Pattern SCIENT_NUMBER_PATTERN = Pattern.compile("^[+-]?\\d+\\.\\d+([eE][+-]?\\d+)?");

    private static final String ICT_OPERATION_PERIOD = "App.Config.Time.IctOperationPeriod";

    @JalorOperation(code = "findVersionList", desc = "获取版本下拉框")
    @Override
    public ResultDataVO findVersionList() throws CommonApplicationException {
        DmFcstVersionInfoVO versionDto = new DmFcstVersionInfoVO();
        versionDto.setDataType("DATA_REVIEW");
        return ResultDataVO.success(dmFcstVersionInfoDao.findVersionList(versionDto));
    }

    /**
     * 上面表单各层级下拉框查询(依据版本动态查询)
     * @param bottomLevelDataReviewVO
     * @return
     * @throws ApplicationException
     */
    @JalorOperation(code = "getDataReviewDropboxList", desc = "各层级下拉框查询")
    @Override
    public ResultDataVO getDataReviewDropboxList(BottomLevelDataReviewVO bottomLevelDataReviewVO) throws CommonApplicationException {
        return  ResultDataVO.success(dmFcstIctRawDataExamineDao.findExamineResultDropDownList(bottomLevelDataReviewVO)) ;
    }

    /**
     * 新增数据时各层级下拉框查询
     * @param bottomLevelDataReviewVO
     * @return
     * @throws ApplicationException
     */
    @JalorOperation(code = "getDataReviewEditDropboxList", desc = "新增数据时各层级下拉框查询")
    @Audit(module = "bottomLevelDataReviewService-getDataReviewEditDropboxList", operation = "getDataReviewEditDropboxList", message = "新增数据时各层级下拉框查询")
    @Override
    public ResultDataVO getDataReviewEditDropboxList(BottomLevelDataReviewVO bottomLevelDataReviewVO) throws CommonApplicationException {
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(bottomLevelDataReviewVO.getPageSize());
        pageVO.setCurPage(bottomLevelDataReviewVO.getPageIndex());
        List<DmFcstIctRawDataExamineDTO> rawDataExamineDimList = new ArrayList<>();
        if (GroupLevelEnum.SPART.getValue().equals(bottomLevelDataReviewVO.getGroupLevel())) {
            PagedResult<DmFcstIctRawDataExamineDTO> spartCodePageList = dmFcstIctRawDataExamineDao.findSpartCodePageList(bottomLevelDataReviewVO, pageVO);
            List<DmFcstIctRawDataExamineDTO> result = spartCodePageList.getResult();
            pageVO = spartCodePageList.getPageVO();
            rawDataExamineDimList = result;
        } else {
            rawDataExamineDimList = dmFcstIctRawDataExamineDao.findRawDataExamineDimList(bottomLevelDataReviewVO);
        }
        Map result = new LinkedHashMap();
        result.put("result", rawDataExamineDimList);
        result.put("pageVo", pageVO);
        return ResultDataVO.success(result);
    }

    private final static List<String> templateTypeList = Arrays.asList(IndustryConstEnum.DATA_TYPE.MAIN_DIM.getValue(),
            IndustryConstEnum.DATA_TYPE.RED_DIM.getValue(), IndustryConstEnum.DATA_TYPE.REPLACE_DIM.getValue(),
            IndustryConstEnum.DATA_TYPE.DATA_REVIEW.getValue());

    @JalorOperation(code = "findDataReviewByPage", desc = "分页查询")
    @Override
    public ResultDataVO findDataReviewByPage(BottomLevelDataReviewVO bottomLevelDataReviewVO) throws CommonApplicationException {
        if (ObjectUtils.isEmpty(bottomLevelDataReviewVO.getPageIndex()) || ObjectUtils.isEmpty(
                bottomLevelDataReviewVO.getPageSize())) {
            throw new CommonApplicationException("分页信息参数不正确");
        }
        PageVO pageVO = new PageVO();
        pageVO.setCurPage(bottomLevelDataReviewVO.getPageIndex());
        pageVO.setPageSize(bottomLevelDataReviewVO.getPageSize());
        // 数据库查询分页数据
        PagedResult<DmFcstIctRawDataExamineDTO> reviewDataPageResult = dmFcstIctRawDataExamineDao.findDataReviewByPage(bottomLevelDataReviewVO, pageVO);

        Map<String, Object> resultMap = new LinkedHashMap<>();
        resultMap.put("result", reviewDataPageResult.getResult());
        resultMap.put("pageVO", reviewDataPageResult.getPageVO());
        return ResultDataVO.success(resultMap);
    }

    @JalorOperation(code = "saveDataReview", desc = "保存记录")
    @Audit(module = "bottomLevelDataReviewService-saveDataReview", operation = "saveDataReview", message = "保存记录")
    @Override
    public ResultDataVO saveDataReview(DataReviewVO dataReviewVO) throws Exception {

        List<DmFcstIctRawDataExamineDTO> reviewList = dataReviewVO.getReviewList();
        // 查询当前版本对应维度数据
        List<DmFcstIctRawDataExamineDTO> examineResultList = dmFcstIctRawDataExamineDao.findDataReviewList(dataReviewVO);
        // 非空校验
        StringBuilder stringBuilder = new StringBuilder();
        checkReviewParam(reviewList, stringBuilder, false);
        // 剔除编辑操作后的数据
        List<DmFcstIctRawDataExamineDTO> allResultList = getDistinctList(reviewList, examineResultList);
        if (CollectionUtils.isNotEmpty(allResultList)) {
            // 校验与系统数据的准确性，时间是否重叠
            checkReviewSystem(reviewList, allResultList);
        }
        Long newVersionId = ictCommonService.createNewVersionInfo("DATA_REVIEW");
        // 保存数据
        saveReviewData(reviewList, allResultList, newVersionId, "data");
        return ResultDataVO.success(newVersionId);
    }

    @JalorOperation(code = "importDataReview", desc = "导入记录")
    @Audit(module = "bottomLevelDataReviewService-importDataReview", operation = "importDataReview", message = "导入记录")
    @Override
    public ResultDataVO importDataReview(Attachment attachment, Long versionId) throws CommonApplicationException, IOException {
        ByteArrayOutputStream byteArrayOutputStream = null;
        try {
            if (null == versionId) {
                throw new CommonApplicationException("入参清单刷新版本为空");
            }
            Timestamp creationDate = new Timestamp(System.currentTimeMillis());

            List<ExcelVO> heads = new ArrayList<>();
            List<HeaderVo> model = new LinkedList<>();
            // 获取excel表头模型和行数
            Map<String, Object> params = getHeaderModuleMap(heads, model);
            // 导入信息记录对象
            Long userId = UserInfoUtils.getUserId();
            UploadInfoVO uploadInfoVO = getUploadInfoVO(attachment, params, userId);
            uploadInfoVO.setCreationDate(creationDate);
            byteArrayOutputStream = excelUtils.putInputStreamCacher(attachment.getDataHandler().getInputStream());
            List<LinkedHashMap<String, Object>> maps = validImpModel(attachment, heads, model, uploadInfoVO,
                    byteArrayOutputStream);
            // 将map数据转为json数组
            JSONArray jsonArray = mapToObject(model, maps);
            List<DmFcstIctRawDataExamineDTO> dataReviewVOList = jsonArray.toJavaList(DmFcstIctRawDataExamineDTO.class);
            List<DmFcstIctRawDataExamineDTO> errorList = new ArrayList<>();
            StringBuilder errMsgBuilder = new StringBuilder();
            if (CollectionUtils.isEmpty(dataReviewVOList)) {
                throw new CommonApplicationException("导入的模板为空，没数据");
            }
            // 查询当前版本对应维度数据
            DataReviewVO dataReviewVO = new DataReviewVO();
            dataReviewVO.setVersionId(versionId);
            List<DmFcstIctRawDataExamineDTO> examineResultList = dmFcstIctRawDataExamineDao.findDataReviewList(dataReviewVO);
            // 非空校验，以及其他参数校验
            StringBuilder stringBuilder = new StringBuilder();
            checkReviewParam(dataReviewVOList, stringBuilder, true);
            String msg = "";
            if (ObjectUtils.isNotEmpty(stringBuilder)) {
                uploadErrorFile(uploadInfoVO, dataReviewVOList, byteArrayOutputStream, stringBuilder);
                throw new CommonApplicationException("成本类型,操作类型或PBI目录不合法");
            }
            // 数据的匹配校验
            Long annualVersionId = dmFcstVersionInfoDao.findVersionIdByDataType("ANNUAL").getVersionId();

            int errorCount = validDimensionData(dataReviewVOList, errorList, errMsgBuilder, annualVersionId, examineResultList);
            // 剔除修改或撤销操作类型的数据
            List<DmFcstIctRawDataExamineDTO> allResultList = getDistinctList(dataReviewVOList, examineResultList);
            // 整体性的校验
            checkImportExamineData(dataReviewVOList, allResultList, errMsgBuilder, annualVersionId);

            List<Map> normalList = getExamineNormalList(dataReviewVOList);
            List<Map> allDataList = getUploadDataList(uploadInfoVO, dataReviewVOList, errorList);
            if (0 == errorCount && ObjectUtils.isEmpty(errMsgBuilder)) {
                msg = "本次共导入" + dataReviewVOList.size() + "条，成功导入" + dataReviewVOList.size() + "条，失败录入0条";
                saveReviewExamineData(model, uploadInfoVO, dataReviewVOList, allResultList, normalList, allDataList);
            } else {
                // 上传文件
                uploadImpExpRecord(model, uploadInfoVO, dataReviewVOList, errMsgBuilder, normalList, allDataList);
                throw new CommonApplicationException("输入值不合法");
            }
            return ResultDataVO.success(msg);
        } catch (ApplicationException e) {
            throw new RuntimeException(e);
        } finally {
            if (null != byteArrayOutputStream) {
                byteArrayOutputStream.close();
            }
        }
    }

    @JalorOperation(code = "downloadTemplate", desc = "导出模板下载")
    @Audit(module = "bottomLevelDataReviewService-downloadTemplate", operation = "downloadTemplate", message = "导出模板下载")
    @Override
    public ResultDataVO downloadTemplate(DataReviewVO dataReviewVO, HttpServletResponse response) throws Exception {
        String pageType = dataReviewVO.getPageType();
        if (!templateTypeList.contains(pageType)) {
            return ResultDataVO.failure(ResultCodeEnum.VERSION_PARAM_ERROR);
        }
        String exportTemplate;
        String moduleType;
        switch (pageType) {
            case "MAIN_DIM":
                exportTemplate = CommonConstant.MAIN_CODE_TEMPLATE_PATH;
                moduleType = CommonConstant.MAIN_CODE_TYPE;
                break;
            case "RED_DIM":
                exportTemplate = CommonConstant.RED_DIM_TEMPLATE_PATH;
                moduleType = CommonConstant.RED_TARGET_TYPE;
                break;
            case "REPLACE_DIM":
                exportTemplate = CommonConstant.REPLACE_TEMPLATE_PATH;
                moduleType = CommonConstant.REPLACE_DIM_TYPE;
                break;
            default:
                exportTemplate = CommonConstant.DATA_REVIEW_TEMPLATE_PATH;
                moduleType = CommonConstant.DATA_REVIEW_TYPE;
                break;
        }
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate(exportTemplate);
        String fileName = "配置管理".concat(moduleType.substring(moduleType.indexOf("-"))).concat("导入模板");
        // Excel文件下载到浏览器
        ExcelExportUtil.downloadExcel(workbook, fileName, response);
        // 插入记录，并上传导出文件到S3服务器
        insertExportRecord(0, workbook, fileName, moduleType);
        return ResultDataVO.success();
    }

    public void insertExportRecord(int totalRows, Workbook workbook, String fileName, String moduleType) throws IOException, CommonApplicationException {
        // 插入数据，上传文件
        Long userId = UserInfoUtils.getUserId();
        PbiDmFoiImpExpRecordVO dmFoiImpExpRecordVO = StatisticsExcelService.uploadExportExcel(workbook, totalRows, fileName, userId);
        // 插入数据
        dmFoiImpExpRecordVO.setModuleType(moduleType);
        dmFoiImpExpRecordVO.setCreationDate(new Timestamp(System.currentTimeMillis()));
        dmFoiImpExpRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        statisticsExcelService.insertExportExcelRecord(dmFoiImpExpRecordVO);
    }

    private void saveReviewExamineData(List<HeaderVo> model,
                                       UploadInfoVO uploadInfoVO, List<DmFcstIctRawDataExamineDTO> dataReviewVOList, List<DmFcstIctRawDataExamineDTO> allResultList, List<Map> normalList,
                                       List<Map> dataList) throws ApplicationException, IOException {
        Long versionId = ictCommonService.createNewVersionInfo("DATA_REVIEW");

        // 先汇总，然后异常录入页面需要去除type=撤销的
        allResultList.addAll(dataReviewVOList);
        List<DmFcstIctRawDataExamineDTO> operationRecordList = allResultList.stream().filter(viewVO -> !CommonConstEnum.ModifyType.REVOKE.getValue().equals(viewVO.getModifyType())).collect(Collectors.toList());

        saveReviewData(dataReviewVOList, operationRecordList, versionId, "excel");

        // 记录上传成功excel到个人中心
        uploadImportSuccessFile(model, uploadInfoVO, normalList, dataList);
    }

    private void uploadImpExpRecord(List<HeaderVo> model, UploadInfoVO uploadInfoVO, List<DmFcstIctRawDataExamineDTO> dataReviewVOList, StringBuilder errorBuilder, List<Map> normalList, List<Map> dimensionDataList) throws CommonApplicationException, IOException {
        PbiDmFoiImpExpRecordVO recordVO = uploadImportExcel(uploadInfoVO, dimensionDataList, model, normalList, false);
        // 截取异常反馈信息到2000
        checkFeedBackStr(errorBuilder, recordVO);
        recordVO.setCreationDate(uploadInfoVO.getCreationDate());
        recordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        recordVO.setExceptionFeedback(errorBuilder.toString());
        recordVO.setModuleType(CommonConstant.DATA_REVIEW_TYPE);
        statisticsExcelService.insertImportExcel(recordVO, uploadInfoVO, false, dataReviewVOList.size());
    }

    private void uploadImportSuccessFile(List<HeaderVo> model, UploadInfoVO uploadInfoVO, List<Map> normalList,
                                         List<Map> dataList) throws CommonApplicationException, IOException {
        // 上传文件
        PbiDmFoiImpExpRecordVO recordVO = uploadImportExcel(uploadInfoVO, dataList, model, normalList, true);
        // 导入成功信息记录
        recordVO.setCreationDate(uploadInfoVO.getCreationDate());
        recordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        statisticsExcelService.insertImportExcel(recordVO, uploadInfoVO, true, uploadInfoVO.getRowNumber());
    }

    private List<Map> getExamineNormalList(List<DmFcstIctRawDataExamineDTO> dataExamineList) {
        List<Map> normalList = new ArrayList<>();
        for (DmFcstIctRawDataExamineDTO examineDTO : dataExamineList) {
            Map rawMap = new LinkedHashMap();
            putRawDataMap(examineDTO, rawMap);
            normalList.add(rawMap);
        }
        return normalList;
    }

    private List<Map> getUploadDataList(UploadInfoVO uploadInfoVO, List<DmFcstIctRawDataExamineDTO> dataReviewVOList,
                                        List<DmFcstIctRawDataExamineDTO> errorList) {
        List<Map> dataList = new ArrayList<>();
        for (DmFcstIctRawDataExamineDTO examineDTO : errorList) {
            Map modelMap = new LinkedHashMap();
            putRawDataMap(examineDTO, modelMap);
            modelMap.put("errorMsg", examineDTO.getErrorMessage());
            dataList.add(modelMap);
        }
        uploadInfoVO.setRowNumber(dataReviewVOList.size());
        return dataList;
    }

    /**
     * 封装成map
     *
     * @param examineDTO 参数
     * @param rawMap     map
     */
    private void putRawDataMap(DmFcstIctRawDataExamineDTO examineDTO, Map rawMap) {
        CommonConstEnum.ModifyType modifyType = CommonConstEnum.getModifyType(examineDTO.getModifyType());
        if (null != modifyType) {
            rawMap.put("modifyType", modifyType.getDesc());
        } else {
            rawMap.put("modifyType", modifyType);
        }
        IndustryConstEnum.COST_TYPE costType = IndustryConstEnum.getCostType(examineDTO.getCostType());
        if (null != costType) {
            rawMap.put("costType", costType.getDesc());
        } else {
            rawMap.put("costType", costType);
        }
        IndustryConstEnum.GRANULARITY_TYPE granularityType = IndustryConstEnum.getGranularityType(examineDTO.getGranularityType());
        if (null != granularityType) {
            rawMap.put("granularityType", granularityType.getDesc());
        } else {
            rawMap.put("granularityType", granularityType);
        }
        CommonConstEnum.OVERSEA_FLAG overSeaFlag = CommonConstEnum.getOverSeaFlag(examineDTO.getOverseaFlag());
        if (null != overSeaFlag) {
            rawMap.put("overseaFlag", overSeaFlag.getDesc());
        } else {
            rawMap.put("overseaFlag", examineDTO.getOverseaFlag());
        }
        rawMap.put("bgCnName", examineDTO.getBgCnName());
        rawMap.put("regionCnName", examineDTO.getRegionCnName());
        rawMap.put("repofficeCnName", examineDTO.getRepofficeCnName());
        rawMap.put("lv1CnName", examineDTO.getLv1CnName());
        rawMap.put("lv1ode", examineDTO.getLv1Code());
        rawMap.put("lv2CnName", examineDTO.getLv2CnName());
        rawMap.put("lv2ode", examineDTO.getLv2Code());
        rawMap.put("lv3CnName", examineDTO.getLv3CnName());
        rawMap.put("lv3ode", examineDTO.getLv3Code());
        rawMap.put("lv4CnName", examineDTO.getLv4CnName());
        rawMap.put("lv4ode", examineDTO.getLv4Code());
        rawMap.put("spartCode", examineDTO.getSpartCode());
        rawMap.put("hwContractNum", examineDTO.getHwContractNum());
        rawMap.put("beginDate", examineDTO.getBeginDate());
        rawMap.put("endDate", examineDTO.getEndDate());
        if (CommonConstant.MODIFY_TYPE.contains(examineDTO.getModifyType())) {
            rawMap.put("modifyReasonM", examineDTO.getModifyReason());
            rawMap.put("modifyReasonR", "");
        } else {
            rawMap.put("modifyReasonM", "");
            rawMap.put("modifyReasonR", examineDTO.getModifyReasonR());
        }
    }

    private void checkImportExamineData(List<DmFcstIctRawDataExamineDTO> dataReviewVOList, List<DmFcstIctRawDataExamineDTO> allResultList, StringBuilder errMsgBuilder, Long annualVersionId) throws CommonApplicationException {
        // 当前对象是否与系统数据重复(主要校验新增的数据跟系统是否重复，因修改和撤销的数据已经被去重了)，
        StringBuilder repeatBuilder = new StringBuilder();
        checkRepeatData(dataReviewVOList, allResultList, repeatBuilder);
        if (ObjectUtils.isNotEmpty(repeatBuilder)) {
            errMsgBuilder.append("在该时期范围内某些编码修改操作已存在;");
        }
        // 校验 spart和合同号是否存在
        Set<String> pspSpartCodeSet = new HashSet<>();
        Set<String> stdSpartCodeSet = new HashSet<>();
        Set<String> pspHwContractNumSet = new HashSet<>();
        Set<String> stdHwContractNumSet = new HashSet<>();
        dataReviewVOList.forEach(review -> {
            if (IndustryConstEnum.COST_TYPE.PSP.getValue().equals(review.getCostType())) {
                pspSpartCodeSet.add(review.getSpartCode());
                pspHwContractNumSet.add(review.getHwContractNum());
            }
            if (IndustryConstEnum.COST_TYPE.STD.getValue().equals(review.getCostType())) {
                stdSpartCodeSet.add(review.getSpartCode());
                stdHwContractNumSet.add(review.getHwContractNum());
            }
        });
        ExamineVO examineVO = ExamineVO.builder().pspSpartCodeSet(pspSpartCodeSet).stdSpartCodeSet(stdSpartCodeSet)
                .pspHwContractNumSet(pspHwContractNumSet).stdHwContractNumSet(stdHwContractNumSet).build();

        // 查询数据库
        asyncIctQueryService.findSpartContract(examineVO);
        if (pspSpartCodeSet.size() + stdSpartCodeSet.size() != examineVO.getSpartCont()) {
            errMsgBuilder.append("某些SPART编码不存在;");
        }
        if (pspHwContractNumSet.size() + stdHwContractNumSet.size() != examineVO.getContractNumberCont()) {
            errMsgBuilder.append("某些合同号不存在;");
        }
    }

    private boolean isScientifiNotation(String str) {
        if (SCIENT_NUMBER_PATTERN.matcher(str).matches()) {
            return true;
        }
        return false;
    }

    private void uploadErrorFile(UploadInfoVO uploadInfoVO, List<DmFcstIctRawDataExamineDTO> dataReviewVOList, ByteArrayOutputStream byteArrayOutputStream, StringBuilder stringBuilder) throws CommonApplicationException, IOException {
        InputStream inputStream = null;
        try {
            PbiDmFoiImpExpRecordVO dmFoiImpExpRecordVO = new PbiDmFoiImpExpRecordVO();
            dmFoiImpExpRecordVO.setExceptionFeedback(stringBuilder.toString());
            inputStream = excelUtils.getInputStream(byteArrayOutputStream);
            statisticsExcelService.getImportUploadFileKey(dmFoiImpExpRecordVO, uploadInfoVO.getUserId(), 1,
                    inputStream);
            dmFoiImpExpRecordVO.setCreationDate(uploadInfoVO.getCreationDate());
            dmFoiImpExpRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
            dmFoiImpExpRecordVO.setModuleType(CommonConstant.DATA_REVIEW_TYPE);
            statisticsExcelService.insertImportExcel(dmFoiImpExpRecordVO, uploadInfoVO, false, dataReviewVOList.size());
        } catch (IOException exception) {
            LOGGER.info("上传s3错误：{}", exception.getMessage());
        } finally {
            StreamUtil.closeStreams(inputStream);
            StreamUtil.closeStreams(byteArrayOutputStream);
        }
    }

    public PbiDmFoiImpExpRecordVO uploadImportExcel(UploadInfoVO uploadInfoVO, List<Map> dataList, List<HeaderVo> model,
                                                    List<Map> normalList, boolean flag) throws CommonApplicationException, IOException {
        // 上传源文件
        PbiDmFoiImpExpRecordVO dmFoiImpExpRecordVO = exportRelationExcel(normalList, uploadInfoVO, model);
        String fileSourceKey = dmFoiImpExpRecordVO.getFileSourceKey();

        PbiDmFoiImpExpRecordVO dmFoiRecordVO = new PbiDmFoiImpExpRecordVO();
        if (!flag) {
            model.add(new HeaderVo("错误信息", "errorMsg", CellType.STRING, false, 12 * 480));
            // 上传异常文件
            dmFoiRecordVO = exportRelationExcel(dataList, uploadInfoVO, model);
            String fileErrorKey = dmFoiRecordVO.getFileSourceKey();
            dmFoiRecordVO.setFileErrorKey(fileErrorKey);
        }
        dmFoiRecordVO.setFileSourceKey(fileSourceKey);
        return dmFoiRecordVO;
    }

    private PbiDmFoiImpExpRecordVO exportRelationExcel(List<Map> dataList, UploadInfoVO uploadInfoVO,
                                                       List<HeaderVo> headers) throws CommonApplicationException, IOException {
        String sheetName = uploadInfoVO.getSheetName();
        List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();

        // 设置第二行表头模型
        Set<String> titles = new LinkedHashSet<>();
        setHeader(titleVoList, headers, titles);
        ExportExcelVo exportExcelVo = ExportExcelVo.builder()
                .titleVoList(titleVoList)
                .list(dataList)
                .sheetName(sheetName)
                .mergeCell(false)
                .build();
        List<ExportExcelVo> exportExcelVoList = new ArrayList<>();
        exportExcelVoList.add(exportExcelVo);
        return excelUtils.expSelectColumnExcel(exportExcelVoList, null);
    }

    private void checkFeedBackStr(StringBuilder errorBuilder, PbiDmFoiImpExpRecordVO recordVO)
            throws UnsupportedEncodingException {
        String errorFeedback = errorBuilder.toString();
        int length = errorFeedback.getBytes("UTF-8").length;
        if (length >= 2000) {
            String subErrorFeedback = errorFeedback.substring(0, 666);
            recordVO.setExceptionFeedback(subErrorFeedback);
        } else {
            recordVO.setExceptionFeedback(errorFeedback);
        }
    }

    private void setHeader(List<AbstractExcelTitleVO> titleVoList, List<HeaderVo> headers, Set<String> titles) {
        LeafExcelTitleVO column;
        for (HeaderVo header : headers) {
            column = new LeafExcelTitleVO(header.getTitle(), header.getWidth(), true, header.getField(),
                    header.getField(), header.getDataType(), header.getIsEditable());
            titleVoList.add(column);
            titles.add(header.getField());
        }
    }

    private int validDimensionData(List<DmFcstIctRawDataExamineDTO> dataReviewVOList, List<DmFcstIctRawDataExamineDTO> errorList, StringBuilder errMsgBuilder, Long versionId, List<DmFcstIctRawDataExamineDTO> examineResultList) {
        AtomicInteger atomicInteger = new AtomicInteger();
        // 查询底表数据
        Set<String> costTypeList = dataReviewVOList.stream().map(DmFcstIctRawDataExamineDTO::getCostType).collect(Collectors.toSet());

        BottomLevelDataReviewVO bottomLevelDataReviewVO = new BottomLevelDataReviewVO();
        bottomLevelDataReviewVO.setCostTypeList(costTypeList);

        // 所有重量级团队
        List<DmFcstIctRawDataExamineDTO> prodTeamCodeRawDataList = dmFcstIctRawDataExamineDao.findProdTeamCodeRawDataExamineList(bottomLevelDataReviewVO);
        // bg，国内海外等
        List<DmFcstIctRawDataExamineDTO> otherRawDataList = dmFcstIctRawDataExamineDao.findOtherRawDataList(bottomLevelDataReviewVO);
        // 起始终止时间
        DmFcstIctRawDataExamineDTO rawDataDTO = dmFcstIctRawDataExamineDao.findBeginEndDate();

        // 设置重量级团队
        ExamineVO examineVO = setLvMap(prodTeamCodeRawDataList);
        examineVO.setBegin(rawDataDTO.getBegin());
        examineVO.setEnd(rawDataDTO.getEnd());
        Map<String, String> bgMap = new HashMap<>();
        Map<String, String> regionMap = new HashMap<>();
        Map<String, String> repofficeMap = new HashMap<>();
        otherRawDataList.forEach(other -> {
            bgMap.put(other.getBgCnName(), other.getBgCode());
            regionMap.put(other.getRegionCnName(), other.getRegionCode());
            repofficeMap.put(other.getRepofficeCnName(), other.getRepofficeCode());
        });
        examineVO.setBgMap(bgMap);
        examineVO.setRegionMap(regionMap);
        examineVO.setRepofficeMap(repofficeMap);
        examineVO.setOverseaFlagMap(CommonConstant.overseaFlagMap);
        dataReviewVOList.forEach(reviewVO -> {
            try {
                atomicInteger.addAndGet(setExamineErrorCount(reviewVO, examineVO, errorList, errMsgBuilder, examineResultList));
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
        });
        return atomicInteger.get();
    }

    private ExamineVO setLvMap(List<DmFcstIctRawDataExamineDTO> prodTeamCodeRawDataList) {
        Map<String, String> lv1ProdRndTeamMap = new HashMap<>();
        Map<String, String> lv2ProdRndTeamMap = new HashMap<>();
        Map<String, String> lv3ProdRndTeamMap = new HashMap<>();
        Map<String, String> lv4ProdRndTeamMap = new HashMap<>();
        Map<String, String> lv1IndustryCatgMap = new HashMap<>();
        Map<String, String> lv2IndustryCatgMap = new HashMap<>();
        Map<String, String> lv3IndustryCatgMap = new HashMap<>();
        Map<String, String> lv4IndustryCatgMap = new HashMap<>();
        Map<String, String> lv1ProdListMap = new HashMap<>();
        Map<String, String> lv2ProdListMap = new HashMap<>();
        Map<String, String> lv3ProdListMap = new HashMap<>();
        Map<String, String> lv4ProdListMap = new HashMap<>();
        prodTeamCodeRawDataList.forEach(vo->{
            lv1ProdRndTeamMap.put(vo.getLv1ProdRdTeamCnName(), vo.getLv1ProdRndTeamCode());
            lv2ProdRndTeamMap.put(vo.getLv2ProdRdTeamCnName(), vo.getLv2ProdRndTeamCode());
            lv3ProdRndTeamMap.put(vo.getLv3ProdRdTeamCnName(), vo.getLv3ProdRndTeamCode());
            lv4ProdRndTeamMap.put(vo.getLv4ProdRdTeamCnName(), vo.getLv4ProdRndTeamCode());
            lv1IndustryCatgMap.put(vo.getLv1IndustryCatgCnName(), vo.getLv1IndustryCatgCode());
            lv2IndustryCatgMap.put(vo.getLv2IndustryCatgCnName(), vo.getLv2IndustryCatgCode());
            lv3IndustryCatgMap.put(vo.getLv3IndustryCatgCnName(), vo.getLv3IndustryCatgCode());
            lv4IndustryCatgMap.put(vo.getLv4IndustryCatgCnName(), vo.getLv4IndustryCatgCode());
            lv1ProdListMap.put(vo.getBgCnName() + "_" + vo.getLv1ProdListCnName(), vo.getLv1ProdListCode());
            lv2ProdListMap.put(vo.getBgCnName() + "_" + vo.getLv2ProdListCnName(), vo.getLv2ProdListCode());
            lv3ProdListMap.put(vo.getBgCnName() + "_" + vo.getLv3ProdListCnName(), vo.getLv3ProdListCode());
            lv4ProdListMap.put(vo.getBgCnName() + "_" + vo.getLv4ProdListCnName(), vo.getLv4ProdListCode());
        });
        return ExamineVO.builder()
                .lv1ProdRndTeamMap(lv1ProdRndTeamMap)
                .lv2ProdRndTeamMap(lv2ProdRndTeamMap)
                .lv3ProdRndTeamMap(lv3ProdRndTeamMap)
                .lv4ProdRndTeamMap(lv4ProdRndTeamMap)
                .lv1IndustryCatgMap(lv1IndustryCatgMap)
                .lv2IndustryCatgMap(lv2IndustryCatgMap)
                .lv3IndustryCatgMap(lv3IndustryCatgMap)
                .lv4IndustryCatgMap(lv4IndustryCatgMap)
                .lv1ProdListMap(lv1ProdListMap)
                .lv2ProdListMap(lv2ProdListMap)
                .lv3ProdListMap(lv3ProdListMap)
                .lv4ProdListMap(lv4ProdListMap)
                .build();
    }

    private int setExamineErrorCount(DmFcstIctRawDataExamineDTO rawDataExamineDTO,
                                     ExamineVO examineVO, List<DmFcstIctRawDataExamineDTO> errorList, StringBuilder errMsgBuilder, List<DmFcstIctRawDataExamineDTO> examineResultList) throws UnsupportedEncodingException {
        StringBuilder builder = new StringBuilder();
        int errorCount = 0;
        // 非空校验
        validEmptyData(rawDataExamineDTO, builder);
        // 校验编码和名称，spart如果出现科学计数法需要重新设置值
        validProdTeamData(rawDataExamineDTO, builder, examineVO);
        // 校验bg，国内，海外等
        validOtherData(rawDataExamineDTO, builder, examineVO);
        // 校验起始期终止期是否在范围内
        validDateData(rawDataExamineDTO, builder, examineVO);

        String reviewConnectStr = getReviewExamineConnectName(rawDataExamineDTO);
        StringBuilder overBuilder = new StringBuilder();
        // 新增需要校验
        if (CommonConstEnum.ModifyType.INSERT.getValue().equals(rawDataExamineDTO.getModifyType())) {
            List<DmFcstIctRawDataExamineDTO> examineResult = examineResultList.stream().filter(oneVO -> reviewConnectStr.equals(getReviewExamineConnectName(oneVO))).collect(Collectors.toList());
            examineResult.forEach(oneVO -> {
                // 比较起始终止日期
                compareStartAndEndDate(overBuilder, rawDataExamineDTO, oneVO, reviewConnectStr);
            });
            // 表示有时间重叠
            if (overBuilder.length() > 0) {
                builder.append(overBuilder + "和已存在数据起始终止时间重叠;");
            }
        }
        if (builder.length() > 0) {
            errorCount = errorCount + 1;
            rawDataExamineDTO.setErrorMessage(builder.toString());
            errMsgBuilder.append(builder);
        }
        errorList.add(rawDataExamineDTO);
        return errorCount;
    }

    private void validOtherData(DmFcstIctRawDataExamineDTO rawDataExamineDTO, StringBuilder builder, ExamineVO examineVO) {
        // 校验编码和名称是否匹配
        Map<String, String> bgMap = examineVO.getBgMap();
        Map<String, String> regionMap = examineVO.getRegionMap();

        Map<String, String> repofficeMap = examineVO.getRepofficeMap();
        Map<String, String> overseaFlagMap = examineVO.getOverseaFlagMap();

        Set<String> bgCnNameSet = bgMap.keySet();
        Set<String> regionSet = regionMap.keySet();
        Set<String> repofficeSet = repofficeMap.keySet();
        Set<String> overseaFlagSet = overseaFlagMap.keySet();

        if (!bgCnNameSet.contains(rawDataExamineDTO.getBgCnName())) {
            String bgStr = "bg名称(" + rawDataExamineDTO.getBgCnName() + ")不存在;";
            builder.append(bgStr);
        } else {
            // 获取到bg名称对应的编码
            rawDataExamineDTO.setBgCode(bgMap.get(rawDataExamineDTO.getBgCnName()));
        }
        if (!regionSet.contains(rawDataExamineDTO.getRegionCnName())) {
            String regionStr = "地区部名称(" + rawDataExamineDTO.getRegionCnName() + ")不存在;";
            builder.append(regionStr);
        } else {
            rawDataExamineDTO.setRegionCode(regionMap.get(rawDataExamineDTO.getRegionCnName()));
        }

        if (!repofficeSet.contains(rawDataExamineDTO.getRepofficeCnName())) {
            String repofficeCnNameStr = "代表处名称(" + rawDataExamineDTO.getRepofficeCnName() + ")不存在;";
            builder.append(repofficeCnNameStr);
        } else {
            rawDataExamineDTO.setRepofficeCode(repofficeMap.get(rawDataExamineDTO.getRepofficeCnName()));
        }
        if (!overseaFlagSet.contains(rawDataExamineDTO.getOverseaFlag())) {
            String overseaFlagStr = "国内/海外名称(" + rawDataExamineDTO.getOverseaFlag() + ")不存在;";
            builder.append(overseaFlagStr);
        } else {
            rawDataExamineDTO.setOverseaFlag(overseaFlagMap.get(rawDataExamineDTO.getOverseaFlag()));
        }
    }

    private void validDateData(DmFcstIctRawDataExamineDTO rawDataExamineDTO, StringBuilder builder, ExamineVO examineVO) {
        if (null == rawDataExamineDTO.getBeginDate() || !DATE_PATTERN.matcher(String.valueOf(rawDataExamineDTO.getBeginDate())).matches() || examineVO.getBegin() > rawDataExamineDTO.getBeginDate()) {
            builder.append("起始期值不合法;");
        }
        if (null == rawDataExamineDTO.getEndDate() || !DATE_PATTERN.matcher(String.valueOf(rawDataExamineDTO.getEndDate())).matches() || examineVO.getEnd() < rawDataExamineDTO.getEndDate()) {
            builder.append("终止期值不合法;");
        }
    }

    private void validEmptyData(DmFcstIctRawDataExamineDTO rawDataExamineDTO, StringBuilder builder) throws UnsupportedEncodingException {
        if (StrUtil.isBlank(rawDataExamineDTO.getModifyType())) {
            builder.append("操作类型为空;");
        } else {
            String modifyTypeValue = CommonConstEnum.getModifyTypeValue(rawDataExamineDTO.getModifyType());
            rawDataExamineDTO.setModifyType(modifyTypeValue);
        }
        if (StrUtil.isBlank(rawDataExamineDTO.getGranularityType())) {
            builder.append("PBI目录为空;");
        } else {
            String granularityTypeValue = IndustryConstEnum.getGranularityTypeValue(rawDataExamineDTO.getGranularityType());
            rawDataExamineDTO.setGranularityType(granularityTypeValue);
        }
        if (StrUtil.isBlank(rawDataExamineDTO.getCostType())) {
            builder.append("成本类型为空;");
        } else {
            String costTypeValue = IndustryConstEnum.getCostTypeValue(rawDataExamineDTO.getCostType());
            rawDataExamineDTO.setCostType(costTypeValue);
        }
        // 判断字段是否为空
        validSubEmptyData(rawDataExamineDTO, builder);
        if (CommonConstant.MODIFY_TYPE.contains(rawDataExamineDTO.getModifyType())) {
            if (StringUtils.isBlank(rawDataExamineDTO.getModifyReasonM())) {
                builder.append("修改理由为空;");
            } else {
                // 裁剪字符
                rawDataExamineDTO.setModifyReason(rawDataExamineDTO.getModifyReasonM());
                subModifyReasonLength(rawDataExamineDTO);
            }
        } else {
            if (StringUtils.isBlank(rawDataExamineDTO.getModifyReasonR())) {
                builder.append("撤销理由为空;");
            } else {
                // 裁剪字符
                rawDataExamineDTO.setModifyReason(rawDataExamineDTO.getModifyReasonR());
                subModifyReasonLength(rawDataExamineDTO);
            }
        }
    }

    private void validSubEmptyData(DmFcstIctRawDataExamineDTO rawDataExamineDTO, StringBuilder builder) throws UnsupportedEncodingException {
        if (StrUtil.isBlank(rawDataExamineDTO.getGranularityType())) {
            builder.append("PBI目录为空;");
        }
        if (StrUtil.isBlank(rawDataExamineDTO.getOverseaFlag())) {
            builder.append("国内/海外为空;");
        }
        if (StrUtil.isBlank(rawDataExamineDTO.getBgCnName())) {
            builder.append("bg为空;");
        }
        if (StrUtil.isBlank(rawDataExamineDTO.getRegionCnName())) {
            builder.append("地区部为空;");
        }
        if (StrUtil.isBlank(rawDataExamineDTO.getRepofficeCnName())) {
            builder.append("代表处为空;");
        }
        if (StrUtil.isBlank(rawDataExamineDTO.getLv1CnName())) {
            builder.append("L1为空;");
        }
        if (StrUtil.isBlank(rawDataExamineDTO.getLv2CnName())) {
            builder.append("L2为空;");
        }
        if (StrUtil.isBlank(rawDataExamineDTO.getLv3CnName())) {
            builder.append("L3为空;");
        }
        if (StrUtil.isBlank(rawDataExamineDTO.getLv4CnName())) {
            builder.append("L3.5为空;");
        }
        if (null == rawDataExamineDTO.getHwContractNum()) {
            builder.append("合同号为空;");
        } else {
            // 去空格和换行符
            String hwContractNum = rawDataExamineDTO.getHwContractNum();
            rawDataExamineDTO.setHwContractNum(trimColumn(hwContractNum));
        }
        if (null == rawDataExamineDTO.getSpartCode()) {
            builder.append("SPART编码为空;");
        } else {
            // 去空格和换行符
            String spartCode = rawDataExamineDTO.getSpartCode();
            rawDataExamineDTO.setSpartCode(trimColumn(spartCode));
        }
        if (null == rawDataExamineDTO.getBeginDate()) {
            builder.append("起始期为空;");
        }
        if (null == rawDataExamineDTO.getEndDate()) {
            builder.append("终止期为空;");
        }
    }

    private String trimColumn(String str) {
        return str.trim().replace("\n", "").replace("\r", "");
    }

    private void validProdTeamData(DmFcstIctRawDataExamineDTO rawDataExamineDTO,
                                   StringBuilder builder, ExamineVO examineVO) {
        // 校验编码和名称是否匹配
        if ("IRB".equals(rawDataExamineDTO.getGranularityType())) {
            validIrbProdTeam(rawDataExamineDTO, examineVO, builder);
        } else if ("INDUS".equals(rawDataExamineDTO.getGranularityType())) {
            validIndusProdTeam(rawDataExamineDTO, examineVO, builder);
        } else {
            validProdProdTeam(rawDataExamineDTO, examineVO, builder);
        }
        // 防止科学计数法
        if (isScientifiNotation(rawDataExamineDTO.getSpartCode())) {
            rawDataExamineDTO.setSpartCode(new BigDecimal(rawDataExamineDTO.getSpartCode()).toPlainString());
        }
    }

    private void validIrbProdTeam(DmFcstIctRawDataExamineDTO rawDataExamineDTO, ExamineVO examineVO, StringBuilder builder) {
        Map<String, String> lv1ProdRndTeamMap = examineVO.getLv1ProdRndTeamMap();
        Map<String, String> lv2ProdRndTeamMap = examineVO.getLv2ProdRndTeamMap();
        Map<String, String> lv3ProdRndTeamMap = examineVO.getLv3ProdRndTeamMap();
        Map<String, String> lv4ProdRndTeamMap = examineVO.getLv4ProdRndTeamMap();

        Set<String> lv1ProdRdTeamCnName = lv1ProdRndTeamMap.keySet();
        Set<String> lv2ProdRdTeamCnName = lv2ProdRndTeamMap.keySet();
        Set<String> lv3ProdRdTeamCnName = lv3ProdRndTeamMap.keySet();
        Set<String> lv4ProdRdTeamCnName = lv4ProdRndTeamMap.keySet();
        if (!lv1ProdRdTeamCnName.contains(rawDataExamineDTO.getLv1CnName())) {
            String lv1ProdRdTeamStr = "L1名称(" + rawDataExamineDTO.getLv1CnName() + ")不存在;";
            builder.append(lv1ProdRdTeamStr);
        } else {
            // 获取到名称对应的编码
            rawDataExamineDTO.setLv1Code(lv1ProdRndTeamMap.get(rawDataExamineDTO.getLv1CnName()));
        }
        if (!lv2ProdRdTeamCnName.contains(rawDataExamineDTO.getLv2CnName())) {
            String lv2ProdRdTeamStr = "L2名称(" + rawDataExamineDTO.getLv2CnName() + ")不存在;";
            builder.append(lv2ProdRdTeamStr);
        } else {
            rawDataExamineDTO.setLv2Code(lv2ProdRndTeamMap.get(rawDataExamineDTO.getLv2CnName()));
        }
        if (!lv3ProdRdTeamCnName.contains(rawDataExamineDTO.getLv3CnName())) {
            String lv3ProdRdTeamStr = "L3名称(" + rawDataExamineDTO.getLv3CnName() + ")不存在;";
            builder.append(lv3ProdRdTeamStr);
        } else {
            // 获取到名称对应的编码
            rawDataExamineDTO.setLv3Code(lv3ProdRndTeamMap.get(rawDataExamineDTO.getLv3CnName()));
        }
        if (!lv4ProdRdTeamCnName.contains(rawDataExamineDTO.getLv4CnName())) {
            String lv4ProdRdTeamStr = "L3.5名称(" + rawDataExamineDTO.getLv4CnName() + ")不存在;";
            builder.append(lv4ProdRdTeamStr);
        } else {
            rawDataExamineDTO.setLv4Code(lv4ProdRndTeamMap.get(rawDataExamineDTO.getLv4CnName()));
        }
    }

    private void validIndusProdTeam(DmFcstIctRawDataExamineDTO rawDataExamineDTO, ExamineVO examineVO, StringBuilder builder) {
        Map<String, String> lv1IndustryCatgMap = examineVO.getLv1IndustryCatgMap();
        Map<String, String> lv2IndustryCatgMap = examineVO.getLv2IndustryCatgMap();
        Map<String, String> lv3IndustryCatgMap = examineVO.getLv3IndustryCatgMap();
        Map<String, String> lv4IndustryCatgMap = examineVO.getLv4IndustryCatgMap();

        Set<String> lv1IndustryCatgCnName = lv1IndustryCatgMap.keySet();
        Set<String> lv2IndustryCatgCnName = lv2IndustryCatgMap.keySet();
        Set<String> lv3IndustryCatgCnName = lv3IndustryCatgMap.keySet();
        Set<String> lv4IndustryCatgCnName = lv4IndustryCatgMap.keySet();

        if (!lv1IndustryCatgCnName.contains(rawDataExamineDTO.getLv1CnName())) {
            String lv1IndustryCatgCnNameStr = "L1名称(" + rawDataExamineDTO.getLv1CnName() + ")不存在;";
            builder.append(lv1IndustryCatgCnNameStr);
        } else {
            rawDataExamineDTO.setLv1Code(lv1IndustryCatgMap.get(rawDataExamineDTO.getLv1CnName()));
        }
        if (!lv2IndustryCatgCnName.contains(rawDataExamineDTO.getLv2CnName())) {
            String lv2IndustryCatgCnNameStr = "L2名称(" + rawDataExamineDTO.getLv2CnName() + ")不存在;";
            builder.append(lv2IndustryCatgCnNameStr);
        } else {
            rawDataExamineDTO.setLv2Code(lv2IndustryCatgMap.get(rawDataExamineDTO.getLv2CnName()));
        }
        if (!lv3IndustryCatgCnName.contains(rawDataExamineDTO.getLv3CnName())) {
            String lv3IndustryCatgCnNameStr = "L3名称(" + rawDataExamineDTO.getLv3CnName() + ")不存在;";
            builder.append(lv3IndustryCatgCnNameStr);
        } else {
            rawDataExamineDTO.setLv3Code(lv3IndustryCatgMap.get(rawDataExamineDTO.getLv3CnName()));
        }
        if (!lv4IndustryCatgCnName.contains(rawDataExamineDTO.getLv4CnName())) {
            String lv4IndustryCatgCnNameStr = "L3.5名称(" + rawDataExamineDTO.getLv4CnName() + ")不存在;";
            builder.append(lv4IndustryCatgCnNameStr);
        } else {
            rawDataExamineDTO.setLv4Code(lv4IndustryCatgMap.get(rawDataExamineDTO.getLv4CnName()));
        }
    }

    private void validProdProdTeam(DmFcstIctRawDataExamineDTO rawDataExamineDTO, ExamineVO examineVO, StringBuilder builder) {

        Map<String, String> lv1ProdListMap = examineVO.getLv1ProdListMap();
        Map<String, String> lv2ProdListMap = examineVO.getLv2ProdListMap();
        Map<String, String> lv3ProdListMap = examineVO.getLv3ProdListMap();
        Map<String, String> lv4ProdListMap = examineVO.getLv4ProdListMap();

        Set<String> lv1ProdListCnName = lv1ProdListMap.keySet();
        Set<String> lv2ProdListCnName = lv2ProdListMap.keySet();
        Set<String> lv3ProdListCnName = lv3ProdListMap.keySet();
        Set<String> lv4ProdListCnName = lv4ProdListMap.keySet();

        String lv1WithBg = rawDataExamineDTO.getBgCnName() + "_" + rawDataExamineDTO.getLv1CnName();
        String lv2WithBg = rawDataExamineDTO.getBgCnName() + "_" + rawDataExamineDTO.getLv2CnName();
        String lv3WithBg = rawDataExamineDTO.getBgCnName() + "_" + rawDataExamineDTO.getLv3CnName();
        String lv4WithBg = rawDataExamineDTO.getBgCnName() + "_" + rawDataExamineDTO.getLv4CnName();

        if (!lv1ProdListCnName.contains(lv1WithBg)) {
            String lv1ProdListStr = "L1名称(" + rawDataExamineDTO.getLv1CnName() + ")不存在;";
            builder.append(lv1ProdListStr);
        } else {
            rawDataExamineDTO.setLv1Code(lv1ProdListMap.get(lv1WithBg));
        }
        if (!lv2ProdListCnName.contains(lv2WithBg)) {
            String lv2ProdListStr = "L2名称(" + rawDataExamineDTO.getLv2CnName() + ")不存在;";
            builder.append(lv2ProdListStr);
        } else {
            rawDataExamineDTO.setLv2Code(lv2ProdListMap.get(lv2WithBg));
        }
        if (!lv3ProdListCnName.contains(lv3WithBg)) {
            String lv3ProdListStr = "L3名称(" + rawDataExamineDTO.getLv3CnName() + ")不存在;";
            builder.append(lv3ProdListStr);
        } else {
            rawDataExamineDTO.setLv3Code(lv3ProdListMap.get(lv3WithBg));
        }
        if (!lv4ProdListCnName.contains(lv4WithBg)) {
            String lv4ProdListStr = "L3.5名称(" + rawDataExamineDTO.getLv4CnName() + ")不存在;";
            builder.append(lv4ProdListStr);
        } else {
            rawDataExamineDTO.setLv4Code(lv4ProdListMap.get(lv4WithBg));
        }
    }

    /**
     * @param attachment            参数
     * @param heads                 参数
     * @param model                 参数
     * @param uploadInfoVO          参数
     * @param byteArrayOutputStream 参数
     * @return List 结果
     * @throws CommonApplicationException
     * @throws IOException
     */
    public List<LinkedHashMap<String, Object>> validImpModel(Attachment attachment, List<ExcelVO> heads,
                                                             List<HeaderVo> model, UploadInfoVO uploadInfoVO, ByteArrayOutputStream byteArrayOutputStream)
            throws CommonApplicationException, IOException {
        List<LinkedHashMap<String, Object>> maps = new ArrayList<LinkedHashMap<String, Object>>();
        try {
            maps = excelUtils.importExcel(attachment, heads, uploadInfoVO, byteArrayOutputStream);
        } catch (IOException e) {
            LOGGER.info("dataReview importExcel failed {}", e.getMessage());
        }
        InputStream inputStream = null;
        InputStream inputStreamModel = null;
        try {
            if (CollectionUtils.isEmpty(maps)) {
                PbiDmFoiImpExpRecordVO dmFoiImpExpRecordVO = new PbiDmFoiImpExpRecordVO();
                dmFoiImpExpRecordVO.setExceptionFeedback("列名出错");
                inputStream = excelUtils.getInputStream(byteArrayOutputStream);
                statisticsExcelService.getImportUploadFileKey(dmFoiImpExpRecordVO, uploadInfoVO.getUserId(), 1,
                        inputStream);
                dmFoiImpExpRecordVO.setCreationDate(uploadInfoVO.getCreationDate());
                dmFoiImpExpRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                statisticsExcelService.insertImportExcel(dmFoiImpExpRecordVO, uploadInfoVO, false, 0);
                throw new CommonApplicationException("列名出错");
            }
            // 模板校验
            List<String> firstModelList = model.stream().map(HeaderVo::getTitle).collect(Collectors.toList());
            List<Object> firstValueList = maps.get(0).values().stream().collect(Collectors.toList());
            if (model.size() != maps.get(0).size()|| firstValueList.containsAll(firstModelList)) {
                PbiDmFoiImpExpRecordVO dmFoiImpExpRecordVO = new PbiDmFoiImpExpRecordVO();
                dmFoiImpExpRecordVO.setExceptionFeedback("列名出错");
                inputStream = excelUtils.getInputStream(byteArrayOutputStream);
                dmFoiImpExpRecordVO.setCreationDate(uploadInfoVO.getCreationDate());
                dmFoiImpExpRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                statisticsExcelService.getImportUploadFileKey(dmFoiImpExpRecordVO, uploadInfoVO.getUserId(), 1, inputStream);
                statisticsExcelService.insertImportExcel(dmFoiImpExpRecordVO, uploadInfoVO, false, 0);
                throw new CommonApplicationException("列名出错");
            } else {
                for (HeaderVo headerVo : model) {
                    String title = headerVo.getTitle();
                    Map<String, Object> objectMap = maps.get(0);
                    if (!objectMap.containsKey(title)) {
                        PbiDmFoiImpExpRecordVO dmFoiImpExpRecordVO = new PbiDmFoiImpExpRecordVO();
                        dmFoiImpExpRecordVO.setExceptionFeedback("列名出错");
                        inputStreamModel = excelUtils.getInputStream(byteArrayOutputStream);
                        dmFoiImpExpRecordVO.setCreationDate(uploadInfoVO.getCreationDate());
                        dmFoiImpExpRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                        statisticsExcelService.getImportUploadFileKey(dmFoiImpExpRecordVO, uploadInfoVO.getUserId(), 1, inputStreamModel);
                        statisticsExcelService.insertImportExcel(dmFoiImpExpRecordVO, uploadInfoVO, false, 0);
                        throw new CommonApplicationException("列名出错");
                    }
                }
            }
        } catch (IOException exception) {
            LOGGER.info("读取列名出错：{}", exception.getMessage());
        } finally {
            StreamUtil.closeStreams(inputStream);
            StreamUtil.closeStreams(inputStreamModel);
            StreamUtil.closeStreams(byteArrayOutputStream);
        }
        return maps;
    }

    /**
     * [服务名称]mapToObject
     *
     * @param model 参数
     * @param dataMaps  参数
     * @return JSONArray
     */
    public JSONArray mapToObject(List<HeaderVo> model, List<LinkedHashMap<String, Object>> dataMaps) {
        JSONArray jsonArray = new JSONArray();
        for (int i = 0; i < dataMaps.size(); i++) {
            Map<String, Object> map = dataMaps.get(i);
            JSONObject jsonObject = new JSONObject();
            Set<String> values = map.keySet();
            mapToObjectSub(model, map, jsonObject, values);
            jsonArray.add(jsonObject);
        }
        return jsonArray;
    }

    private void mapToObjectSub(List<HeaderVo> model, Map<String, Object> map, JSONObject jsonObject,
                                Set<String> values) {
        for (String value : values) {
            for (HeaderVo headerVo : model) {
                if (value.equals(headerVo.getTitle())) {
                    String field = formatField(headerVo.getField(), CommonConstant.PATTERN_COLUMN);
                    jsonObject.put(field, map.get(headerVo.getTitle()));
                }
            }
        }
    }

    private String formatField(String field, Pattern pattern) {
        Matcher matcher = pattern.matcher(field);
        if (matcher.find()) {
            String group = matcher.group(1);
            String replace = field.replace(group, matcher.group(2).toUpperCase(Locale.ROOT));
            field = formatField(replace, pattern);
        }
        return field;
    }

    private Map<String, Object> getHeaderModuleMap(List<ExcelVO> heads, List<HeaderVo> model) {
        Map<String, Object> params = new HashMap<>();
        model.addAll(CommonConstant.DATA_REVIEW_HEADER);
        setHeaderValues(heads, model);
        params.put("module", CommonConstant.DATA_REVIEW_TYPE);
        try {
            Long maxRowNum = NumberUtil.parseLong(
                    registryQueryService.findValueByPath("Jalor.Excel.ExcelImportMaxCount", true));
            params.put("maxRowNum", maxRowNum);
        } catch (ApplicationException e) {
            LOGGER.info("find maxRowNum error {}", e.getMessage());
        }
        return params;
    }

    private void setHeaderValues(List<ExcelVO> list, List<HeaderVo> headerVoList) {
        for (HeaderVo header : headerVoList) {
            ExcelVO vo = new ExcelVO();
            vo.setHeadName(header.getTitle());
            vo.setHeadType(CommonConstant.VARCHAR);
            list.add(vo);
        }
    }

    private UploadInfoVO getUploadInfoVO(Attachment attachment,
                                         Map<String, Object> params, Long userId) throws IOException {
        UploadInfoVO uploadInfoVO = new UploadInfoVO();
        uploadInfoVO.setFileName(ExcelUtils.getFilename(attachment).concat(new SimpleDateFormat("_yyyy-MM-dd HH_mm_ss").format(new Date())));
        uploadInfoVO.setFileSize(attachment.getDataHandler().getInputStream().available() / 1024);
        uploadInfoVO.setParams(params);
        uploadInfoVO.setUserId(userId);
        return uploadInfoVO;
    }

    private String getReviewConnectCode(DmFcstIctRawDataExamineDTO paramVO) {
        return paramVO.getSpartCode() + "#" + paramVO.getCostType() + "#" + paramVO.getGranularityType() + "#" + paramVO.getOverseaFlag()
                + "#" + paramVO.getBgCnName() + "#" + paramVO.getRegionCnName()
                + "#" + paramVO.getRepofficeCnName() + "#" + paramVO.getLv1CnName() + "#" + paramVO.getLv2CnName()
                + "#" + paramVO.getLv3CnName() + "#" + paramVO.getLv4CnName() + "#" + paramVO.getHwContractNum()
                + "#" + paramVO.getBeginDate() + "#" + paramVO.getEndDate();
    }

    private String getReviewExamineConnectName(DmFcstIctRawDataExamineDTO paramVO) {
        return paramVO.getSpartCode() + "#" + paramVO.getCostType() + "#" + paramVO.getGranularityType() + "#" + paramVO.getOverseaFlag()
                + "#" + paramVO.getBgCnName() + "#" + paramVO.getRegionCnName()
                + "#" + paramVO.getRepofficeCnName() + "#" + paramVO.getLv1CnName() + "#" + paramVO.getLv2CnName()
                + "#" + paramVO.getLv3CnName() + "#" + paramVO.getLv4CnName() + "#" + paramVO.getHwContractNum();
    }

    private void saveReviewData(List<DmFcstIctRawDataExamineDTO> reviewList, List<DmFcstIctRawDataExamineDTO> allResultList, Long versionId, String type) {
        reviewList.forEach(examineDTO -> {
            examineDTO.setCreatedBy(UserInfoUtils.getUserCn());
            examineDTO.setCreationDate(new Date());
            examineDTO.setDelFlag(CommonConstant.IS_NOT);
            examineDTO.setVersionId(versionId);
            examineDTO.setLastUpdateDate(new Date());
            examineDTO.setLastUpdatedBy(UserInfoUtils.getUserCn());
            examineDTO.setDelFlag(CommonConstant.IS_NOT);
            examineDTO.setPageFlag(CommonConstant.HISTORY_PAGE);
        });
        dmFcstIctRawDataExamineDao.createDataReviewList(reviewList);
        if ("data".equals(type)) {
            allResultList.addAll(reviewList);
        }
        allResultList.stream().forEach(reviewVO -> {
            reviewVO.setVersionId(versionId);
            reviewVO.setPageFlag(CommonConstant.ABNORMAL_PAGE);
        });
        dmFcstIctRawDataExamineDao.createDataReviewList(allResultList);
    }

    private void checkReviewParam(List<DmFcstIctRawDataExamineDTO> reviewList, StringBuilder stringBuilder, boolean flag)
            throws CommonApplicationException, UnsupportedEncodingException {
        // 非空校验，以及截取修改理由长度
        if (flag) {
            Set<String> costTypeSet = reviewList.stream().map(DmFcstIctRawDataExamineDTO::getCostType).collect(Collectors.toSet());
            List<String> costTypeList = EnumSet.allOf(IndustryConstEnum.COST_TYPE.class).stream().map(IndustryConstEnum.COST_TYPE::getDesc).collect(Collectors.toList());
            Set<String> modifyTypeSet = reviewList.stream().map(DmFcstIctRawDataExamineDTO::getModifyType).collect(Collectors.toSet());
            List<String> modifyTypeList = EnumSet.allOf(CommonConstEnum.ModifyType.class).stream().map(CommonConstEnum.ModifyType::getDesc).collect(Collectors.toList());
            Set<String> granularityTypeSet = reviewList.stream().map(DmFcstIctRawDataExamineDTO::getGranularityType).collect(Collectors.toSet());
            List<String> granularityTypeList = EnumSet.allOf(IndustryConstEnum.GRANULARITY_TYPE.class).stream().map(IndustryConstEnum.GRANULARITY_TYPE::getDesc).collect(Collectors.toList());
            if (!costTypeSet.stream().anyMatch(costType -> costTypeList.contains(costType))) {
                stringBuilder.append("成本类型全部错误;");
            }
            if (!modifyTypeSet.stream().anyMatch(modifyType -> modifyTypeList.contains(modifyType))) {
                stringBuilder.append("操作类型全部错误;");
            }
            if (!granularityTypeSet.stream().anyMatch(granularityType -> granularityTypeList.contains(granularityType))) {
                stringBuilder.append("PBI目录树全部错误;");
            }
        } else {
            StringBuilder builder = checkDataEmpty(reviewList, stringBuilder);
            if (ObjectUtils.isNotEmpty(builder)) {
                throw new CommonApplicationException("检测到:" + builder);
            }
        }
    }

    private void checkExamineData(StringBuilder paramBuilder, List<DmFcstIctRawDataExamineDTO> paramEntryValue, List<DmFcstIctRawDataExamineDTO> allIctResultList) {
        if (CollectionUtils.isEmpty(allIctResultList)) {
            allIctResultList.addAll(paramEntryValue);
        }
        paramEntryValue.forEach(examineDTO -> {
            // 拼接后对比是否相等
            String reviewConnectStr = getReviewExamineConnectName(examineDTO);
            allIctResultList.forEach(oneVO -> {
                String allReviewConnectStr = getReviewExamineConnectName(oneVO);
                if (reviewConnectStr.equals(allReviewConnectStr)) {
                    // 比较起始终止日期
                    compareStartAndEndDate(paramBuilder, examineDTO, oneVO, reviewConnectStr);
                }
            });
        });
    }

    private void compareStartAndEndDate(StringBuilder paramBuilder, DmFcstIctRawDataExamineDTO examineDTO, DmFcstIctRawDataExamineDTO oneVO, String reviewConnectStr) {
        if (oneVO.getBeginDate() > examineDTO.getBeginDate() && oneVO.getEndDate() < examineDTO.getEndDate()) {
            if (!paramBuilder.toString().contains(reviewConnectStr)) {
                paramBuilder.append(reviewConnectStr + " ");
                return;
            }
        }
        if (oneVO.getBeginDate() < examineDTO.getBeginDate() && oneVO.getEndDate() < examineDTO.getEndDate()
                && oneVO.getEndDate() > examineDTO.getBeginDate()) {
            if (!paramBuilder.toString().contains(reviewConnectStr)) {
                paramBuilder.append(reviewConnectStr + " ");
                return;
            }
        }
        if (oneVO.getBeginDate() > examineDTO.getBeginDate() && oneVO.getEndDate() > examineDTO.getEndDate()
                && oneVO.getBeginDate() < examineDTO.getEndDate()) {
            if (!paramBuilder.toString().contains(reviewConnectStr)) {
                paramBuilder.append(reviewConnectStr + " ");
                return;
            }
        }
        if (oneVO.getBeginDate().equals(examineDTO.getBeginDate()) || oneVO.getEndDate().equals(examineDTO.getEndDate())
                || oneVO.getBeginDate().equals(examineDTO.getEndDate()) || oneVO.getEndDate().equals(examineDTO.getBeginDate())) {
            if (!paramBuilder.toString().contains(reviewConnectStr)) {
                paramBuilder.append(reviewConnectStr + " ");
            }
        }
    }

    private void checkReviewSystem(List<DmFcstIctRawDataExamineDTO> reviewList, List<DmFcstIctRawDataExamineDTO> allResultList)
            throws CommonApplicationException {
        // 校验入参数据和系统数据是否重复
        StringBuilder doubleStringBuilder = new StringBuilder();
        checkRepeatData(reviewList, allResultList, doubleStringBuilder);
        if (ObjectUtils.isNotEmpty(doubleStringBuilder)) {
            throw new CommonApplicationException("检测到:" + doubleStringBuilder + "在该时期范围修改操作已存在,请勿重复提交!");
        }
        // 校验同一条件下，系统已存在的数据和新增或者修改的数据的，起始终止时间是否重叠
        StringBuilder crossoverBuilder = new StringBuilder();
        checkExamineData(crossoverBuilder, reviewList, allResultList);
        if (ObjectUtils.isNotEmpty(crossoverBuilder)) {
            throw new CommonApplicationException("检测到SPART:" + crossoverBuilder + "和已存在数据起始终止时间重叠,请修改后保存!");
        }
    }

    private void checkRepeatData(List<DmFcstIctRawDataExamineDTO> reviewList, List<DmFcstIctRawDataExamineDTO> tempList, StringBuilder doubleBuilder) {
        List<DmFcstIctRawDataExamineDTO> combineList = new ArrayList<>();
        combineList.addAll(reviewList);
        combineList.addAll(tempList);
        // 新插入的数据 + 当前verisonId的历史数据，需要总体 校验是否重复
        Map<String, List<DmFcstIctRawDataExamineDTO>> repeatExamineList = combineList.stream()
                .collect(Collectors.groupingBy(this::getReviewConnectCode));
        List<String> count = repeatExamineList.keySet().stream()
                .filter(key -> repeatExamineList.get(key).size() > 1).distinct().collect(Collectors.toList());
        if (count.size() > 0) {
            for (String combine : count) {
                String combineCode = combine.split("#")[0];
                if (!doubleBuilder.toString().contains(combineCode)) {
                    doubleBuilder.append(combineCode + ",");
                }
            }
        }
    }

    private StringBuilder checkDataEmpty(List<DmFcstIctRawDataExamineDTO> reviewList, StringBuilder emptyBuilder)
            throws UnsupportedEncodingException {
        // 校验入参是否空值
        for (DmFcstIctRawDataExamineDTO reviewVO : reviewList) {
            if (null == reviewVO.getBeginDate()) {
                return emptyBuilder.append("起始期为空;");
            }
            if (null == reviewVO.getEndDate()) {
                return emptyBuilder.append("终止期为空;");
            }
            if (StrUtil.isBlank(reviewVO.getModifyReason())) {
                return emptyBuilder.append("修改理由为空;");
            } else {
                // 裁剪字符
                subModifyReasonLength(reviewVO);
            }
        }
        return emptyBuilder;
    }

    private void subModifyReasonLength(DmFcstIctRawDataExamineDTO reviewVO) throws UnsupportedEncodingException {
        String modifyReason = reviewVO.getModifyReason().trim();
        int length = modifyReason.getBytes("UTF-8").length;
        if (length >= 2000) {
            String substring = modifyReason.substring(0, 666);
            reviewVO.setModifyReason(substring);
        } else {
            reviewVO.setModifyReason(modifyReason);
        }
    }

    private List<DmFcstIctRawDataExamineDTO> getDistinctList(List<DmFcstIctRawDataExamineDTO> reviewList,
                                                             List<DmFcstIctRawDataExamineDTO> examineResultList) {
        // 历史数据，type=新增的不需要去重，修改和撤销才去重
        Map<String, List<DmFcstIctRawDataExamineDTO>> repeatList = reviewList.stream().filter(review -> CommonConstEnum.ModifyType.MODIFY.getValue().equals(review.getModifyType()) || CommonConstEnum.ModifyType.REVOKE.getValue().equals(review.getModifyType()))
                .collect(Collectors.groupingBy(
                        this::getReviewExamineConnectName));
        // 历史数据去除正在修改和撤销的记录
        List<String> keySet = repeatList.keySet().stream().distinct().collect(Collectors.toList());

        return examineResultList.stream().filter(vo -> !keySet.contains(getReviewExamineConnectName(vo))).collect(Collectors.toList());
    }

    @JalorOperation(code = "dataReviewExport", desc = "异常数据导出")
    @Audit(module = "bottomLevelDataReviewService-dataReviewExport", operation = "dataReviewExport", message = "异常数据导出")
    @Override
    public ResultDataVO dataReviewExport(HttpServletResponse response, BottomLevelDataReviewVO bottomLevelDataReviewVO) throws ApplicationException {
        IExcelTemplateBeanManager templateBeanManager = DataReviewTemplateEnum.getByCode("01", "");
        Map<String, Object> parameters = new HashMap<>();
        String module = "成本指数-ICT-底层数据审视-异常数据";
        parameters.put("exportModuleName", module);
        parameters.put("exportFileName", bottomLevelDataReviewVO.getFileName());
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(10000);
        bottomLevelDataReviewVO.setPageFlag("abnormal");
        exportProcessorService.fillEasyExcelExport(response,templateBeanManager, bottomLevelDataReviewVO, parameters,pageVO);
        return ResultDataVO.success();
    }

    @JalorOperation(code = "dataReviewHistroyExport", desc = "操作记录导出")
    @Audit(module = "bottomLevelDataReviewService-dataReviewHistroyExport", operation = "dataReviewHistroyExport", message = "操作记录导出")
    @Override
    public ResultDataVO dataReviewHistroyExport(HttpServletResponse response, BottomLevelDataReviewVO bottomLevelDataReviewVO) throws ApplicationException {
        IExcelTemplateBeanManager templateBeanManager = DataReviewTemplateEnum.getByCode("02", "");
        Map<String, Object> parameters = new HashMap<>();
        String module = "成本指数-ICT-底层数据审视-操作记录";
        parameters.put("exportModuleName", module);
        parameters.put("exportFileName", bottomLevelDataReviewVO.getFileName());
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(10000);
        bottomLevelDataReviewVO.setPageFlag("history");
        exportProcessorService.fillEasyExcelExport(response,templateBeanManager, bottomLevelDataReviewVO, parameters,pageVO);
        return ResultDataVO.success();
    }

    @JalorOperation(code = "refreshSystem", desc = "系统刷新")
    @Override
    public ResultDataVO refreshSystem(Long versionId) throws ApplicationException {
        String ictOperationPeriod = registryQueryService.findValueByPath(ICT_OPERATION_PERIOD, true);
        if (StringUtils.isNotBlank(ictOperationPeriod)) {
            String[] dateRange = ictOperationPeriod.split("-");
            int monthValue = LocalDate.now().getDayOfMonth();
            if (StringUtils.isNotBlank(dateRange[0]) && StringUtils.isNotBlank(dateRange[1])) {
                int beginDate = Integer.parseInt(dateRange[0]);
                int endDate = Integer.parseInt(dateRange[1]);
                if (monthValue < beginDate || monthValue > endDate) {
                    throw  new CommonApplicationException("日期不在规定操作时间范围内");
                }
            }
        }
        // 先更新系统中所有的版本为is_runing=N
        dmFcstVersionInfoDao.updateRunningStatusFlag();
        // 再依据版本号设置is_runing=Y
        dmFcstVersionInfoDao.updateStatusFlag(versionId);
        return ResultDataVO.success(versionId);
    }

    @JalorOperation(code = "findRefreshTime", desc = "获取系统刷新时间")
    @Override
    public ResultDataVO findRefreshTime() {
        HashMap reMap = new HashMap();
        List<DmFcstVersionInfoDTO> maxDataReviewVersion = dmFcstVersionInfoDao.findMaxDataReviewVersion();
        if (CollectionUtils.isNotEmpty(maxDataReviewVersion)) {
            reMap.put("version",maxDataReviewVersion.get(0));
            reMap.put("refreshTime",dmFcstVersionInfoDao.findRefreshTime());
        } else {
            reMap.put("version",null);
            reMap.put("refreshTime",null);
        }
        return ResultDataVO.success(reMap);
    }


}
