/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

/**
 * DmFocViewInfoVO Class
 *
 * <AUTHOR>
 * @since 2023/3/15
 */
// 此@data注解不可以被替换成getter和setter
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DmFocViewInfoVO {
    private Long id;

    private String lv0ProdRndTeamCode;

    private String lv0ProdRdTeamCnName;

    private String lv1ProdRndTeamCode;

    private String lv1ProdRdTeamCnName;

    private String lv2ProdRndTeamCode;

    private String lv2ProdRdTeamCnName;

    private String lv3ProdRndTeamCode;

    private String lv3ProdRdTeamCnName;

    private String lv4ProdRndTeamCode;

    private String lv4ProdRdTeamCnName;

    private String lv0ProdListCode;

    private String lv0ProdListCnName;

    private String prodRndTeamCode;

    private String l3CegCode;

    private String l3CegCnName;

    private String categoryCode;

    private String categoryCnName;

    private String itemCode;

    private String itemCnName;

    private String createdBy;

    private Timestamp creationDate;

    private String lastUpdatedBy;

    private Timestamp lastUpdateDdate;

    private String delFlag;

    private String viewFlag;

    private int viewFlagOrder;

    private String viewFlagValue;

    private String groupCode;
    
    private String groupLevel;

    private String groupCnName;

    private Long versionId;

    /**
     * 权限标签
     */
    private String permissionFlag;

    private String removeFlag;

    private Double weightRate;

    /**
     * 颗粒度（U：通用，2：盈利）
     */
    private String granularityType;

    /**
     * 某个code是否失效,Y有效，N无效
     */
    private String subEnableFlag;

    /**
     * 是否组合
     */
    private Boolean isCombination;

    /**
     * 组合id
     */
    private Long customId;

    private String parentCode;

    private String parentCnName;

    private String pageFlag;

    private String l1Name;

    private String l2Name;

    private String coaCode;

    private String coaCnName;

    private String dimensionCode;

    private String dimensionCnName;

    private String dimensionSubCategoryCode;

    private String dimensionSubCategoryCnName;

    private String dimensionSubDetailCode;

    private String dimensionSubDetailCnName;

    private String spartCode;

    private String spartCnName;

    private String userId;

    private String roleId;

    // 组合整体失效
    private String enableFlag;

    // 业务口径（R:收入时点/C：发货成本）
    private String caliberFlag;

    // 国内海外标识(I:国内/O:海外/G:全球)
    private String overseaFlag;

    private String l3CegShortCnName;

    private String l4CegShortCnName;

    private String l4CegCnName;

    private String l4CegCode;

    private String customCnName;

    private String isSeparate;

    // 拼接的groupcode
    private String connectCode;

    // 拼接的parentcode
    private String connectParentCode;

    private List<DmFocViewInfoVO> children;

    private String topItemCode;

    private int itemNum;

    // 成本类型
    private String costType;

    private String shippingObjectCode;

    private String shippingObjectCnName;

    private String manufactureObjectCode;

    private String manufactureObjectCnName;

    // 角色：parent表示父层级,或者current当前已选层级
    private String selectFlag;

    private String industryOrg;
}
