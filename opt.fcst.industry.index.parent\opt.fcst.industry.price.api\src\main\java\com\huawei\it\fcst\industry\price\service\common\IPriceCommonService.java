/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.service.common;

import com.huawei.it.fcst.industry.price.vo.common.CommonPriceBaseVO;
import com.huawei.it.fcst.vo.DmFcstDataRefreshStatus;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;

import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * IPriceCommonService Interface
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Path("/common")
@Produces(MediaType.APPLICATION_JSON)
public interface IPriceCommonService {

    /**
     * [根据数据类型查询版本ID]
     *
     * @param dataType 数据类型
     * @return Long
     */
    Long getVersionId(String dataType);

    /**
     * 查询切换基期实际数的开始和结束时间
     *
     */
    @POST
    @Path("/actual/periodId")
    ResultDataVO findActualPeriodId();

    @GET
    @Path("/refreshTime")
    ResultDataVO findRefreshTime();

    /**
     * 根据数据类型创建版本信息
     *
     * @param dataType
     * @return version id
     * @throws ApplicationException
     */
    Long createNewVersionInfo(String dataType);

    /**
     * 保存任务刷新状态
     *
     * @param taskFlag
     * @return DmFcstDataRefreshStatus
     * @throws ApplicationException
     */
    DmFcstDataRefreshStatus saveDataRefreshStatus(String taskFlag) throws ApplicationException;

    /**
     * 设置导出显示标题
     *
     * @param commonBaseVO 参数VO
     */
    void setTitleDisplayName(CommonPriceBaseVO commonBaseVO);

}