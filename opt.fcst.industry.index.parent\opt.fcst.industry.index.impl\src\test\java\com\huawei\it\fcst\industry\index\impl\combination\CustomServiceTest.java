/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.combination;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

import com.huawei.it.fcst.industry.index.dao.IDmFocCustomCombDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocCustomCombTempDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeCustomCombDao;
import com.huawei.it.fcst.industry.index.vo.combination.CombTransformVO;
import com.huawei.it.fcst.industry.index.vo.combination.CombinationVO;
import com.huawei.it.fcst.industry.index.vo.combination.DmCustomCombVO;
import com.huawei.it.fcst.industry.index.vo.combination.DmFocDataRefreshStatus;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoDTO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.AsyncResult;

import java.lang.reflect.Method;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import java.util.concurrent.Future;

/**
 * CustomServiceTest Class
 *
 * <AUTHOR>
 * @since 2023/9/14
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class CustomServiceTest {
    private static final Logger LOGGER = LoggerFactory.getLogger(CustomService.class);

    @InjectMocks
    private CustomService customService;

    @Mock
    private IDmFocCustomCombDao dmFocCustomCombDao;

    @Mock
    private CustomCommonService customCommonService;

    @Mock
    private IDmFocMadeCustomCombDao dmFocMadeCustomCombDao;

    @Mock
    private AsyncCustomService asyncCustomService;

    @Mock
    private IDmFocCustomCombTempDao dmFocCustomCombTempDao;

    @Test
    public void createCustom() throws Exception {
        List<DmCustomCombVO> customVOList = new ArrayList<>();
        CombTransformVO combTransformVO = new CombTransformVO();
        List<DmCustomCombVO> otherCustomVOList = new ArrayList<>();
        combTransformVO.setUserId(1111L);
        combTransformVO.setTaskId(22L);
        combTransformVO.setPageFlag("ALL");
        String success ="SUCCESS";
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusViewAnnualCost(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusItemDtlDecode(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusItemAppend(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).customAnnual(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).customMonthComb(any());
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callMadeCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callMadeCustomMonth(any());
        try {
            customService.createCustom(customVOList,combTransformVO,otherCustomVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNull(null);
    }

    @Test
    public void createCustom2Test() throws Exception {
        List<DmCustomCombVO> customVOList = new ArrayList<>();
        CombTransformVO combTransformVO = new CombTransformVO();
        List<DmCustomCombVO> otherCustomVOList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setCustomId(16L);
        otherCustomVOList.add(dmCustomCombVO);

        combTransformVO.setUserId(1111L);
        combTransformVO.setTaskId(22L);
        combTransformVO.setPageFlag("ALL");
        combTransformVO.setCostType("P");
        String success ="SUCCESS";
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusViewAnnualCost(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusItemDtlDecode(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusItemAppend(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).customAnnual(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).customMonthComb(any());

        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callCustomMonth(any());
        customService.createCustom(customVOList,combTransformVO,otherCustomVOList);
        Assert.assertNull(null);
    }

    @Test
    public void createCustom3Test() throws Exception {
        List<DmCustomCombVO> customVOList = new ArrayList<>();
        CombTransformVO combTransformVO = new CombTransformVO();
        List<DmCustomCombVO> otherCustomVOList = new ArrayList<>();
        combTransformVO.setUserId(1111L);
        combTransformVO.setTaskId(22L);
        combTransformVO.setPageFlag("ALL");
        String success ="SUCCESS";
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusViewAnnualCost(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusItemDtlDecode(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusItemAppend(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).customAnnual(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).customMonthComb(any());
        PowerMockito.doReturn(success).when(dmFocMadeCustomCombDao).cusMadeItemDtlDecode(any());
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callMadeCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callMadeCustomMonth(any());
        try {
            customService.createCustom(customVOList,combTransformVO,otherCustomVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNull(null);
    }

    @Test
    public void createCustom4Test() throws Exception {
        List<DmCustomCombVO> customVOList = new ArrayList<>();
        CombTransformVO combTransformVO = new CombTransformVO();
        List<DmCustomCombVO> otherCustomVOList = new ArrayList<>();
        combTransformVO.setUserId(1111L);
        combTransformVO.setTaskId(22L);
        combTransformVO.setPageFlag("ALL");
        String success ="SUCCESS";
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusViewAnnualCost(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusItemDtlDecode(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusItemAppend(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).customAnnual(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).customMonthComb(any());
        PowerMockito.doReturn(success).when(dmFocMadeCustomCombDao).cusMadeItemDtlDecode(any());
        PowerMockito.doReturn(success).when(dmFocMadeCustomCombDao).cusMadeItemAppend(any());
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callMadeCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callMadeCustomMonth(any());
        try {
            customService.createCustom(customVOList,combTransformVO,otherCustomVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNull(null);
    }

    @Test
    public void createCustom5Test() throws Exception {
        List<DmCustomCombVO> customVOList = new ArrayList<>();
        CombTransformVO combTransformVO = new CombTransformVO();
        List<DmCustomCombVO> otherCustomVOList = new ArrayList<>();
        combTransformVO.setUserId(1111L);
        combTransformVO.setTaskId(22L);
        combTransformVO.setPageFlag("ALL");
        String success ="SUCCESS";
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusViewAnnualCost(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusItemDtlDecode(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusItemAppend(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).customAnnual(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).customMonthComb(any());
        PowerMockito.doReturn(success).when(dmFocMadeCustomCombDao).cusMadeItemDtlDecode(any());
        PowerMockito.doReturn(success).when(dmFocMadeCustomCombDao).cusMadeItemAppend(any());
        PowerMockito.doReturn(success).when(dmFocMadeCustomCombDao).cusMadeViewAnnualCost(any());
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callMadeCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callMadeCustomMonth(any());
        try {
            customService.createCustom(customVOList,combTransformVO,otherCustomVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNull(null);
    }

    @Test
    public void createCustom6Test() throws Exception {
        List<DmCustomCombVO> customVOList = new ArrayList<>();
        CombTransformVO combTransformVO = new CombTransformVO();
        List<DmCustomCombVO> otherCustomVOList = new ArrayList<>();
        combTransformVO.setUserId(1111L);
        combTransformVO.setTaskId(22L);
        combTransformVO.setPageFlag("ALL");
        String success ="SUCCESS";
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusViewAnnualCost(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusItemDtlDecode(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusItemAppend(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).customAnnual(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).customMonthComb(any());
        PowerMockito.doReturn(success).when(dmFocMadeCustomCombDao).cusMadeItemDtlDecode(any());
        PowerMockito.doReturn(success).when(dmFocMadeCustomCombDao).cusMadeItemAppend(any());
        PowerMockito.doReturn(success).when(dmFocMadeCustomCombDao).cusMadeViewAnnualCost(any());
        PowerMockito.doReturn(success).when(dmFocMadeCustomCombDao).customMadeAnnual(any());
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callMadeCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callMadeCustomMonth(any());
        try {
            customService.createCustom(customVOList,combTransformVO,otherCustomVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNull(null);
    }

    @Test
    public void createCustom7Test() throws Exception {
        List<DmCustomCombVO> customVOList = new ArrayList<>();
        CombTransformVO combTransformVO = new CombTransformVO();
        List<DmCustomCombVO> otherCustomVOList = new ArrayList<>();
        combTransformVO.setUserId(1111L);
        combTransformVO.setTaskId(22L);
        combTransformVO.setPageFlag("ALL");
        String success ="SUCCESS";
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusViewAnnualCost(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusItemDtlDecode(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusItemAppend(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).customAnnual(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).customMonthComb(any());
        PowerMockito.doReturn(success).when(dmFocMadeCustomCombDao).cusMadeItemDtlDecode(any());
        PowerMockito.doReturn(success).when(dmFocMadeCustomCombDao).cusMadeItemAppend(any());
        PowerMockito.doReturn(success).when(dmFocMadeCustomCombDao).cusMadeViewAnnualCost(any());
        PowerMockito.doReturn(success).when(dmFocMadeCustomCombDao).customMadeAnnual(any());
        PowerMockito.doReturn(success).when(dmFocMadeCustomCombDao).customMadeMonthComb(any());
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callMadeCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callMadeCustomMonth(any());
        try {
            customService.createCustom(customVOList,combTransformVO,otherCustomVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNull(null);
    }

    @Test
    public void createCustom8Test() throws Exception {
        List<DmCustomCombVO> customVOList = new ArrayList<>();
        CombTransformVO combTransformVO = new CombTransformVO();
        List<DmCustomCombVO> otherCustomVOList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setCustomId(16L);
        otherCustomVOList.add(dmCustomCombVO);

        combTransformVO.setUserId(1111L);
        combTransformVO.setTaskId(22L);
        combTransformVO.setPageFlag("ALL");
        combTransformVO.setCostType("P");
        String success ="SUCCESS";
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callCustomMonth(any());

        try {
            customService.createCustom(customVOList,combTransformVO,otherCustomVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNull(null);
    }

    @Test
    public void createCustom9Test() throws Exception {
        List<DmCustomCombVO> customVOList = new ArrayList<>();
        CombTransformVO combTransformVO = new CombTransformVO();
        List<DmCustomCombVO> otherCustomVOList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setCustomId(16L);
        otherCustomVOList.add(dmCustomCombVO);

        combTransformVO.setUserId(1111L);
        combTransformVO.setTaskId(22L);
        combTransformVO.setPageFlag("ALL");
        combTransformVO.setCostType("P");
        String success ="SUCCESS";
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusViewAnnualCost(any());
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callCustomMonth(any());
        try {
            customService.createCustom(customVOList,combTransformVO,otherCustomVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNull(null);
    }

    @Test
    public void createCustom10Test() throws Exception {
        List<DmCustomCombVO> customVOList = new ArrayList<>();
        CombTransformVO combTransformVO = new CombTransformVO();
        List<DmCustomCombVO> otherCustomVOList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setCustomId(16L);
        otherCustomVOList.add(dmCustomCombVO);

        combTransformVO.setUserId(1111L);
        combTransformVO.setTaskId(22L);
        combTransformVO.setPageFlag("ALL");
        combTransformVO.setCostType("P");
        String success ="SUCCESS";
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusViewAnnualCost(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusItemDtlDecode(any());
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callCustomMonth(any());
        try {
            customService.createCustom(customVOList,combTransformVO,otherCustomVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNull(null);
    }

    @Test
    public void createCustom11Test() throws Exception {
        List<DmCustomCombVO> customVOList = new ArrayList<>();
        CombTransformVO combTransformVO = new CombTransformVO();
        List<DmCustomCombVO> otherCustomVOList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setCustomId(16L);
        otherCustomVOList.add(dmCustomCombVO);

        combTransformVO.setUserId(1111L);
        combTransformVO.setTaskId(22L);
        combTransformVO.setPageFlag("ALL");
        combTransformVO.setCostType("P");
        String success ="SUCCESS";
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusViewAnnualCost(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusItemDtlDecode(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusItemAppend(any());
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callCustomMonth(any());
        try {
            customService.createCustom(customVOList,combTransformVO,otherCustomVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNull(null);
    }

    @Test
    public void createCustom12Test() throws Exception {
        List<DmCustomCombVO> customVOList = new ArrayList<>();
        CombTransformVO combTransformVO = new CombTransformVO();
        List<DmCustomCombVO> otherCustomVOList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setCustomId(16L);
        otherCustomVOList.add(dmCustomCombVO);

        combTransformVO.setUserId(1111L);
        combTransformVO.setTaskId(22L);
        combTransformVO.setPageFlag("ALL");
        combTransformVO.setCostType("P");
        String success ="SUCCESS";
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusViewAnnualCost(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusItemDtlDecode(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusItemAppend(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).customAnnual(any());
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callCustomMonth(any());
        try {
            customService.createCustom(customVOList,combTransformVO,otherCustomVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNull(null);
    }

    @Test
    public void createCustom13Test() throws Exception {
        List<DmCustomCombVO> customVOList = new ArrayList<>();
        CombTransformVO combTransformVO = new CombTransformVO();
        List<DmCustomCombVO> otherCustomVOList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setCustomId(16L);
        otherCustomVOList.add(dmCustomCombVO);

        combTransformVO.setUserId(1111L);
        combTransformVO.setTaskId(22L);
        combTransformVO.setPageFlag("ANNUAL");
        combTransformVO.setCostType("P");
        String success ="1";
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusViewAnnualCost(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusItemDtlDecode(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusItemAppend(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).customAnnual(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).customMonthComb(any());
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callCustomMonth(any());
        customService.createCustom(customVOList,combTransformVO,otherCustomVOList);
        Assert.assertNull(null);
    }

    @Test
    public void createCustom14Test() throws Exception {
        List<DmCustomCombVO> customVOList = new ArrayList<>();
        CombTransformVO combTransformVO = new CombTransformVO();
        List<DmCustomCombVO> otherCustomVOList = new ArrayList<>();
        combTransformVO.setUserId(1111L);
        combTransformVO.setTaskId(22L);
        combTransformVO.setPageFlag("BUFF");
        String success ="1";
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusViewAnnualCost(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusItemDtlDecode(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).cusItemAppend(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).customAnnual(any());
        PowerMockito.doReturn(success).when(dmFocCustomCombDao).customMonthComb(any());
        PowerMockito.doReturn(success).when(dmFocMadeCustomCombDao).cusMadeItemDtlDecode(any());
        PowerMockito.doReturn(success).when(dmFocMadeCustomCombDao).cusMadeItemAppend(any());
        PowerMockito.doReturn(success).when(dmFocMadeCustomCombDao).cusMadeViewAnnualCost(any());
        PowerMockito.doReturn(success).when(dmFocMadeCustomCombDao).customMadeAnnual(any());
        PowerMockito.doReturn(success).when(dmFocMadeCustomCombDao).customMadeMonthComb(any());
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callMadeCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callMadeCustomMonth(any());
        try {
            customService.createCustom(customVOList,combTransformVO,otherCustomVOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNull(null);
    }

    @Test
    public void createCustom15Test() throws Exception {
        List<DmCustomCombVO> customVOList = new ArrayList<>();
        CombTransformVO combTransformVO = new CombTransformVO();
        List<DmCustomCombVO> otherCustomVOList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setCustomId(16L);
        otherCustomVOList.add(dmCustomCombVO);

        combTransformVO.setUserId(1111L);
        combTransformVO.setTaskId(22L);
        combTransformVO.setPageFlag("ALL_ANNUAL");
        combTransformVO.setCostType("M");
        String success ="1";
        PowerMockito.doReturn(success).when(dmFocMadeCustomCombDao).cusMadeViewAnnualCost(any());
        PowerMockito.doReturn(success).when(dmFocMadeCustomCombDao).cusMadeItemDtlDecode(any());
        PowerMockito.doReturn(success).when(dmFocMadeCustomCombDao).cusMadeItemAppend(any());
        PowerMockito.doReturn(success).when(dmFocMadeCustomCombDao).customMadeAnnual(any());
        PowerMockito.doReturn(success).when(dmFocMadeCustomCombDao).customMadeMonthComb(any());
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callMadeCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callMadeCustomMonth(any());
        customService.createCustom(customVOList,combTransformVO,otherCustomVOList);
        Assert.assertNull(null);
    }

    @Test
    public void callCombFunction() throws Exception {

        CombTransformVO combTransformVO=new CombTransformVO();
        combTransformVO.setCustomId(19L);
        combTransformVO.setCostType("P");
        combTransformVO.setPageFlag("Y");
        when(dmFocCustomCombDao.cusViewAnnualCost(any())).thenReturn("false");
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callCustomMonth(any());
        try {
            customService.callCombFunction(combTransformVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNull(null);
    }

    @Test
    public void callCombFunction2Test() throws Exception {
        CombTransformVO combTransformVO=new CombTransformVO();
        combTransformVO.setCustomId(19L);
        combTransformVO.setCostType("P");
        combTransformVO.setPageFlag("Y");
        when(dmFocCustomCombDao.cusViewAnnualCost(any())).thenReturn("SUCCESS");
        when(dmFocCustomCombDao.cusItemDtlDecode(any())).thenReturn("FAIL");
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callCustomMonth(any());
        try {
            customService.callCombFunction(combTransformVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNull(null);
    }

    @Test
    public void callCombFunction3Test() throws Exception {
        CombTransformVO combTransformVO=new CombTransformVO();
        combTransformVO.setCustomId(19L);
        combTransformVO.setCostType("P");
        combTransformVO.setPageFlag("ANNUAL");
        when(dmFocCustomCombDao.cusViewAnnualCost(any())).thenReturn("SUCCESS");
        when(dmFocCustomCombDao.cusItemDtlDecode(any())).thenReturn("SUCCESS");
        when(dmFocCustomCombDao.cusItemAppend(any())).thenReturn("FAIL");
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callCustomMonth(any());
        try {
            customService.callCombFunction(combTransformVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNull(null);
    }

    @Test
    public void callCombFunction4Test() throws Exception {
        CombTransformVO combTransformVO=new CombTransformVO();
        combTransformVO.setCustomId(19L);
        combTransformVO.setCostType("P");
        combTransformVO.setPageFlag("ANNUAL");
        when(dmFocCustomCombDao.cusViewAnnualCost(any())).thenReturn("SUCCESS");
        when(dmFocCustomCombDao.cusItemDtlDecode(any())).thenReturn("SUCCESS");
        when(dmFocCustomCombDao.cusItemAppend(any())).thenReturn("SUCCESS");
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callCustomMonth(any());
        try {
            customService.callCombFunction(combTransformVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNull(null);
    }

    @Test
    public void callCombFunction5Test() throws Exception {
        CombTransformVO combTransformVO=new CombTransformVO();
        combTransformVO.setCustomId(19L);
        combTransformVO.setCostType("P");
        combTransformVO.setPageFlag("ANNUALS");
        when(dmFocCustomCombDao.cusViewAnnualCost(any())).thenReturn("SUCCESS");
        when(dmFocCustomCombDao.cusItemDtlDecode(any())).thenReturn("SUCCESS");
        when(dmFocCustomCombDao.cusItemAppend(any())).thenReturn("SUCCESS");
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callCustomMonth(any());
        try {
            customService.callCombFunction(combTransformVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNull(null);
    }

    @Test
    public void callCombFunction6Test() throws Exception {
        CombTransformVO combTransformVO=new CombTransformVO();
        combTransformVO.setCustomId(19L);
        combTransformVO.setCostType("P");
        combTransformVO.setPageFlag("all");
        when(dmFocCustomCombDao.cusViewAnnualCost(any())).thenReturn("1");
        when(dmFocCustomCombDao.cusItemDtlDecode(any())).thenReturn("1");
        when(dmFocCustomCombDao.cusItemAppend(any())).thenReturn("1");
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callCustomMonth(any());
        try {
            customService.callCombFunction(combTransformVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNull(null);
    }

    @Test
    public void callCombFunction7Test() throws Exception {
        CombTransformVO combTransformVO=new CombTransformVO();
        combTransformVO.setCustomId(19L);
        combTransformVO.setCostType("P");
        combTransformVO.setPageFlag("MONTH");
        when(dmFocCustomCombDao.cusViewAnnualCost(any())).thenReturn("1");
        when(dmFocCustomCombDao.cusItemDtlDecode(any())).thenReturn("1");
        when(dmFocCustomCombDao.cusItemAppend(any())).thenReturn("1");
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callCustomMonth(any());
        try {
            customService.callCombFunction(combTransformVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNull(null);
    }

    @Test
    public void callCombFunction8Test() throws Exception {
        CombTransformVO combTransformVO=new CombTransformVO();
        combTransformVO.setCustomId(19L);
        combTransformVO.setCostType("M");
        combTransformVO.setPageFlag("ANNUAL");
        when(dmFocCustomCombDao.cusViewAnnualCost(any())).thenReturn("1");
        when(dmFocCustomCombDao.cusItemDtlDecode(any())).thenReturn("1");
        when(dmFocCustomCombDao.cusItemAppend(any())).thenReturn("1");
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callMadeCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callMadeCustomMonth(any());
        try {
            customService.callCombFunction(combTransformVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNull(null);
    }

    @Test
    public void updateCombList() throws Exception {
        CombinationVO combinationVO=new CombinationVO();
        CombTransformVO combTransformVO=new CombTransformVO();
        combinationVO.setCustomId(15L);
        List<DmCustomCombVO> customList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setCustomId(66L);
        dmCustomCombVO.setCreationDate(new Timestamp(300));
        dmCustomCombVO.setLastUpdateDate(new Timestamp(500));
        customList.add(dmCustomCombVO);
        combinationVO.setCustomVOList(customList);
        combinationVO.setRoleId("role");
        combinationVO.setUserId("user");
        combinationVO.setPageFlag("all");
        combinationVO.setOldPageFlag("all");
        combinationVO.setCustomCnName("元器");
        List<DmCustomCombVO> dmCustomCombList = new ArrayList<>();
        dmCustomCombList.add(dmCustomCombVO);

        combTransformVO.setPageFlag("all");
        combTransformVO.setCostType("P");
        when(dmFocCustomCombDao.getCustomCombListByPage(any())).thenReturn(dmCustomCombList);
        when(dmFocCustomCombDao.cusViewAnnualCost(any())).thenReturn("SUCCESS");
        when(dmFocCustomCombDao.cusItemDtlDecode(any())).thenReturn("SUCCESS");
        when(dmFocCustomCombDao.cusItemAppend(any())).thenReturn("SUCCESS");

        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callCustomMonth(any());

        try {
            customService.updateCombList(combinationVO,combTransformVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNull(null);
    }

    @Test
    public void updateCombList2Test() throws Exception {
        CombinationVO combinationVO=new CombinationVO();
        CombTransformVO combTransformVO=new CombTransformVO();
        combinationVO.setCustomId(15L);
        List<DmCustomCombVO> customList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setCustomId(66L);
        dmCustomCombVO.setCreationDate(new Timestamp(300));
        dmCustomCombVO.setLastUpdateDate(new Timestamp(500));
        customList.add(dmCustomCombVO);
        combinationVO.setCustomVOList(customList);
        combinationVO.setRoleId("role");
        combinationVO.setUserId("user");
        combinationVO.setPageFlag("all");
        combinationVO.setOldPageFlag("ALL");
        combinationVO.setCustomCnName("元器");
        combTransformVO.setCostType("P");
        List<DmCustomCombVO> dmCustomCombList = new ArrayList<>();
        dmCustomCombList.add(dmCustomCombVO);

        combTransformVO.setPageFlag("all");

        when(dmFocCustomCombDao.getCustomCombListByPage(any())).thenReturn(dmCustomCombList);
        when(dmFocCustomCombDao.cusViewAnnualCost(any())).thenReturn("SUCCESS");
        when(dmFocCustomCombDao.cusItemDtlDecode(any())).thenReturn("SUCCESS");
        when(dmFocCustomCombDao.cusItemAppend(any())).thenReturn("SUCCESS");
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callCustomMonth(any());
        try {
            customService.updateCombList(combinationVO,combTransformVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNull(null);
    }

    @Test
    public void updateCombList3Test() throws Exception {
        CombinationVO combinationVO=new CombinationVO();
        CombTransformVO combTransformVO=new CombTransformVO();
        combinationVO.setCustomId(15L);
        List<DmCustomCombVO> customList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setCustomId(66L);
        dmCustomCombVO.setCreationDate(new Timestamp(300));
        dmCustomCombVO.setLastUpdateDate(new Timestamp(500));
        customList.add(dmCustomCombVO);
        combinationVO.setCustomVOList(customList);
        combinationVO.setRoleId("role");
        combinationVO.setUserId("user");
        combinationVO.setPageFlag("ALL");
        combinationVO.setOldPageFlag("ALL");
        combinationVO.setCustomCnName("元器");
        combTransformVO.setCostType("P");
        List<DmCustomCombVO> dmCustomCombList = new ArrayList<>();
        dmCustomCombList.add(dmCustomCombVO);

        combTransformVO.setPageFlag("all");

        when(dmFocCustomCombDao.getCustomCombListByPage(any())).thenReturn(dmCustomCombList);
        when(dmFocCustomCombDao.cusViewAnnualCost(any())).thenReturn("SUCCESS");
        when(dmFocCustomCombDao.cusItemDtlDecode(any())).thenReturn("SUCCESS");
        when(dmFocCustomCombDao.cusItemAppend(any())).thenReturn("SUCCESS");
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callCustomMonth(any());

        try {
            customService.updateCombList(combinationVO,combTransformVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNull(null);
    }

    @Test
    public void updateCombList5Test() throws Exception {
        CombinationVO combinationVO=new CombinationVO();
        CombTransformVO combTransformVO=new CombTransformVO();
        combinationVO.setCustomId(15L);
        List<DmCustomCombVO> customList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setCustomId(66L);
        dmCustomCombVO.setSubEnableFlag("N");
        dmCustomCombVO.setCreationDate(new Timestamp(300));
        dmCustomCombVO.setLastUpdateDate(new Timestamp(500));
        customList.add(dmCustomCombVO);
        combinationVO.setCustomVOList(customList);
        combinationVO.setRoleId("role");
        combinationVO.setUserId("user");
        combinationVO.setPageFlag("all");
        combinationVO.setOldPageFlag("ALL");
        combinationVO.setCustomCnName("元器");
        combTransformVO.setCostType("P");
        List<DmCustomCombVO> dmCustomCombList = new ArrayList<>();
        dmCustomCombList.add(dmCustomCombVO);

        combTransformVO.setPageFlag("all");

        when(dmFocCustomCombDao.getCustomCombListByPage(any())).thenReturn(dmCustomCombList);
        when(dmFocCustomCombDao.cusViewAnnualCost(any())).thenReturn("SUCCESS");
        when(dmFocCustomCombDao.cusItemDtlDecode(any())).thenReturn("SUCCESS");
        when(dmFocCustomCombDao.cusItemAppend(any())).thenReturn("SUCCESS");
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callCustomMonth(any());

        try {
            customService.updateCombList(combinationVO,combTransformVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNull(null);
    }

    @Test
    public void updateCombList4Test() throws Exception {
        CombinationVO combinationVO=new CombinationVO();
        CombTransformVO combTransformVO=new CombTransformVO();
        combinationVO.setCustomId(15L);
        List<DmCustomCombVO> customList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setCustomId(66L);
        dmCustomCombVO.setSubEnableFlag("N");
        dmCustomCombVO.setCreationDate(new Timestamp(300));
        dmCustomCombVO.setLastUpdateDate(new Timestamp(500));
        customList.add(dmCustomCombVO);
        combinationVO.setCustomVOList(customList);
        combinationVO.setRoleId("role");
        combinationVO.setUserId("user");
        combinationVO.setPageFlag("all");
        combinationVO.setOldPageFlag("all");
        combinationVO.setCustomCnName("元器");
        combTransformVO.setCostType("P");
        List<DmCustomCombVO> dmCustomCombList = new ArrayList<>();
        dmCustomCombList.add(dmCustomCombVO);

        combTransformVO.setPageFlag("all");

        when(dmFocCustomCombDao.getCustomCombListByPage(any())).thenReturn(dmCustomCombList);
        when(dmFocCustomCombDao.cusViewAnnualCost(any())).thenReturn("SUCCESS");
        when(dmFocCustomCombDao.cusItemDtlDecode(any())).thenReturn("SUCCESS");
        when(dmFocCustomCombDao.cusItemAppend(any())).thenReturn("SUCCESS");
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callCustomMonth(any());
        try {
            customService.updateCombList(combinationVO,combTransformVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNull(null);
    }

    @Test
    public void updateCombList6Test() throws Exception {
        CombinationVO combinationVO=new CombinationVO();
        CombTransformVO combTransformVO=new CombTransformVO();
        combinationVO.setCustomId(15L);
        List<DmCustomCombVO> customList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setSubEnableFlag("N");
        dmCustomCombVO.setCustomId(66L);
        dmCustomCombVO.setCreationDate(new Timestamp(300));
        dmCustomCombVO.setLastUpdateDate(new Timestamp(500));
        customList.add(dmCustomCombVO);
        combinationVO.setCustomVOList(customList);
        combinationVO.setRoleId("role");
        combinationVO.setUserId("user");
        combinationVO.setPageFlag("ALL");
        combinationVO.setOldPageFlag("ALL");
        combinationVO.setCustomCnName("元器");
        combTransformVO.setCostType("P");

        List<DmCustomCombVO> dmCustomCombList = new ArrayList<>();
        dmCustomCombList.add(dmCustomCombVO);

        combTransformVO.setPageFlag("all");

        when(dmFocCustomCombDao.getCustomCombListByPage(any())).thenReturn(dmCustomCombList);
        when(dmFocCustomCombDao.cusViewAnnualCost(any())).thenReturn("SUCCESS");
        when(dmFocCustomCombDao.cusItemDtlDecode(any())).thenReturn("SUCCESS");
        when(dmFocCustomCombDao.cusItemAppend(any())).thenReturn("SUCCESS");
        when(customCommonService.filterAnotherPageData(any(),any())).thenReturn(customList);
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callCustomMonth(any());

        try {
            customService.updateCombList(combinationVO,combTransformVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNull(null);
    }

    @Test
    public void updateCombList7Test() throws Exception {
        CombinationVO combinationVO=new CombinationVO();
        CombTransformVO combTransformVO=new CombTransformVO();
        combinationVO.setCustomId(15L);
        List<DmCustomCombVO> customList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setSubEnableFlag("N");
        dmCustomCombVO.setCustomId(66L);
        dmCustomCombVO.setCreationDate(new Timestamp(300));
        dmCustomCombVO.setLastUpdateDate(new Timestamp(500));
        customList.add(dmCustomCombVO);
        combinationVO.setCustomVOList(customList);
        combinationVO.setRoleId("role");
        combinationVO.setUserId("user");
        combinationVO.setPageFlag("all");
        combinationVO.setOldPageFlag("ALL");
        combinationVO.setCustomCnName("元器");
        combTransformVO.setCostType("P");
        List<DmCustomCombVO> dmCustomCombList = new ArrayList<>();

        combTransformVO.setPageFlag("all");

        when(dmFocCustomCombDao.getCustomCombListByPage(any())).thenReturn(dmCustomCombList);
        when(dmFocCustomCombDao.cusViewAnnualCost(any())).thenReturn("SUCCESS");
        when(dmFocCustomCombDao.cusItemDtlDecode(any())).thenReturn("SUCCESS");
        when(dmFocCustomCombDao.cusItemAppend(any())).thenReturn("SUCCESS");
        when(customCommonService.filterAnotherPageData(any(),any())).thenReturn(customList);
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callCustomMonth(any());

        try {
            customService.updateCombList(combinationVO,combTransformVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNull(null);
    }

    @Test
    public void updateCombList8Test() throws Exception {
        CombinationVO combinationVO=new CombinationVO();
        CombTransformVO combTransformVO=new CombTransformVO();
        combinationVO.setCustomId(15L);
        List<DmCustomCombVO> customList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setSubEnableFlag("N");
        dmCustomCombVO.setCustomId(66L);
        dmCustomCombVO.setCreationDate(new Timestamp(300));
        dmCustomCombVO.setLastUpdateDate(new Timestamp(500));
        customList.add(dmCustomCombVO);
        combinationVO.setCustomVOList(customList);
        combinationVO.setRoleId("role");
        combinationVO.setUserId("user");
        combinationVO.setPageFlag("ALL");
        combinationVO.setOldPageFlag("ALL");
        combinationVO.setCustomCnName("元器");
        combTransformVO.setCostType("P");
        List<DmCustomCombVO> dmCustomCombList = new ArrayList<>();

        combTransformVO.setPageFlag("all");

        when(dmFocCustomCombDao.getCustomCombListByPage(any())).thenReturn(dmCustomCombList);
        when(dmFocCustomCombDao.cusViewAnnualCost(any())).thenReturn("SUCCESS");
        when(dmFocCustomCombDao.cusItemDtlDecode(any())).thenReturn("SUCCESS");
        when(dmFocCustomCombDao.cusItemAppend(any())).thenReturn("SUCCESS");
        when(customCommonService.filterAnotherPageData(any(),any())).thenReturn(customList);
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callCustomMonth(any());

        try {
            customService.updateCombList(combinationVO,combTransformVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNull(null);
    }

    @Test
    public void asyncInitFlag() throws Exception {
        List<DmCustomCombVO> customCombList = new ArrayList<>();
        List<DmCustomCombVO> annualList = new ArrayList<>();
        List<DmCustomCombVO> monthList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        DmCustomCombVO dmCustomCombVO2 = new DmCustomCombVO();
        dmCustomCombVO.setPageFlag("ANNUAL");
        dmCustomCombVO.setGranularityType("U");
        dmCustomCombVO.setCustomId(16L);
        dmCustomCombVO.setGroupLevel("LV2");

        dmCustomCombVO2.setPageFlag("MONTH");
        dmCustomCombVO2.setGranularityType("U");
        dmCustomCombVO2.setCustomId(18L);
        dmCustomCombVO2.setGroupLevel("LV3");
        customCombList.add(dmCustomCombVO);
        annualList.add(dmCustomCombVO);
        customCombList.add(dmCustomCombVO2);
        monthList.add(dmCustomCombVO2);

        List<DmCustomCombVO> manufactureCustomCombList = new ArrayList<>();
        List<DmCustomCombVO> energyCustomCombList = new ArrayList<>();
        List<DmCustomCombVO> manufactureEnergyCustomCombList = new ArrayList<>();
        List<DmCustomCombVO> generalAnnualList = annualList.stream().filter(custom -> custom.getGranularityType().equals("U")).collect(Collectors.toList());
        List<DmCustomCombVO> profitAnnualList = annualList.stream().filter(custom -> custom.getGranularityType().equals("P")).collect(Collectors.toList());
        List<DmCustomCombVO> dimensionAnnualList = annualList.stream().filter(custom -> custom.getGranularityType().equals("D")).collect(Collectors.toList());

        List<DmCustomCombVO> generalMonthList = monthList.stream().filter(custom -> custom.getGranularityType().equals("U")).collect(Collectors.toList());
        List<DmCustomCombVO> profitMonthList = monthList.stream().filter(custom -> custom.getGranularityType().equals("P")).collect(Collectors.toList());
        List<DmCustomCombVO> dimensionMonthList = monthList.stream().filter(custom -> custom.getGranularityType().equals("D")).collect(Collectors.toList());
        Future<Boolean> univalAnnualFlag = new AsyncResult<>(Boolean.TRUE);
        Future<Boolean> profitAnnualFlag = new AsyncResult<>(Boolean.TRUE);
        Future<Boolean> dimesionAnnualFlag = new AsyncResult<>(Boolean.TRUE);
        Future<Boolean> univalMonthFlag = new AsyncResult<>(Boolean.TRUE);
        Future<Boolean> profitMonthFlag = new AsyncResult<>(Boolean.TRUE);
        Future<Boolean> dimensionMonthFlag = new AsyncResult<>(Boolean.TRUE);
        CombinationVO combinationVO = new CombinationVO();
        when(asyncCustomService.granularityTypePageCondition(generalAnnualList, combinationVO,"U_ANNUAL")).thenReturn(univalAnnualFlag);
        when(asyncCustomService.granularityTypePageCondition(profitAnnualList,combinationVO,"P_ANNUAL")).thenReturn(profitAnnualFlag);
        when(asyncCustomService.granularityTypePageCondition(dimensionAnnualList,combinationVO,"D_ANNUAL")).thenReturn(dimesionAnnualFlag);
        when(asyncCustomService.granularityTypePageCondition(generalMonthList,combinationVO,"U_MONTH")).thenReturn(univalMonthFlag);
        when(asyncCustomService.granularityTypePageCondition(profitMonthList,combinationVO,"P_MONTH")).thenReturn(profitMonthFlag);
        when(asyncCustomService.granularityTypePageCondition(dimensionMonthList,combinationVO,"D_MONTH")).thenReturn(dimensionMonthFlag);

        CombTransformVO combTransformVO = new CombTransformVO();
        customService.asyncInitFlag(customCombList,manufactureCustomCombList,energyCustomCombList,manufactureEnergyCustomCombList,combinationVO, combTransformVO);
        Assert.assertNull(null);
    }

    @Test
    public void asyncInitFlag2T() throws Exception {
        List<DmCustomCombVO> customCombList = new ArrayList<>();
        List<DmCustomCombVO> annualList = new ArrayList<>();
        List<DmCustomCombVO> monthList = new ArrayList<>();
        List<DmCustomCombVO> manufactureCustomCombList = new ArrayList<>();
        List<DmCustomCombVO> energyCustomCombList = new ArrayList<>();
        List<DmCustomCombVO> manufactureEnergyCustomCombList = new ArrayList<>();

        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        DmCustomCombVO dmCustomCombVO2 = new DmCustomCombVO();
        dmCustomCombVO.setPageFlag("ANNUAL");
        dmCustomCombVO.setGranularityType("P");
        dmCustomCombVO.setCustomId(16L);
        dmCustomCombVO.setGroupLevel("LV2");

        dmCustomCombVO2.setPageFlag("MONTH");
        dmCustomCombVO2.setGranularityType("P");
        dmCustomCombVO2.setCustomId(18L);
        dmCustomCombVO2.setGroupLevel("LV3");
        manufactureCustomCombList.add(dmCustomCombVO);
        annualList.add(dmCustomCombVO);
        manufactureCustomCombList.add(dmCustomCombVO2);
        monthList.add(dmCustomCombVO2);
        List<DmCustomCombVO> generalAnnualList = annualList.stream().filter(custom -> custom.getGranularityType().equals("U")).collect(Collectors.toList());
        List<DmCustomCombVO> profitAnnualList = annualList.stream().filter(custom -> custom.getGranularityType().equals("P")).collect(Collectors.toList());
        List<DmCustomCombVO> dimensionAnnualList = annualList.stream().filter(custom -> custom.getGranularityType().equals("D")).collect(Collectors.toList());

        List<DmCustomCombVO> generalMonthList = monthList.stream().filter(custom -> custom.getGranularityType().equals("U")).collect(Collectors.toList());
        List<DmCustomCombVO> profitMonthList = monthList.stream().filter(custom -> custom.getGranularityType().equals("P")).collect(Collectors.toList());
        List<DmCustomCombVO> dimensionMonthList = monthList.stream().filter(custom -> custom.getGranularityType().equals("D")).collect(Collectors.toList());
        Future<Boolean> univalAnnualFlag = new AsyncResult<>(Boolean.TRUE);
        Future<Boolean> profitAnnualFlag = new AsyncResult<>(Boolean.TRUE);
        Future<Boolean> dimesionAnnualFlag = new AsyncResult<>(Boolean.TRUE);
        Future<Boolean> univalMonthFlag = new AsyncResult<>(Boolean.TRUE);
        Future<Boolean> profitMonthFlag = new AsyncResult<>(Boolean.TRUE);
        Future<Boolean> dimensionMonthFlag = new AsyncResult<>(Boolean.TRUE);
        CombinationVO combinationVO = new CombinationVO();
        when(asyncCustomService.granularityTypePageCondition(generalAnnualList, combinationVO,"U_ANNUAL")).thenReturn(univalAnnualFlag);
        when(asyncCustomService.granularityTypePageCondition(profitAnnualList,combinationVO,"P_ANNUAL")).thenReturn(profitAnnualFlag);
        when(asyncCustomService.granularityTypePageCondition(dimensionAnnualList,combinationVO,"D_ANNUAL")).thenReturn(dimesionAnnualFlag);
        when(asyncCustomService.granularityTypePageCondition(generalMonthList,combinationVO,"U_MONTH")).thenReturn(univalMonthFlag);
        when(asyncCustomService.granularityTypePageCondition(profitMonthList,combinationVO,"P_MONTH")).thenReturn(profitMonthFlag);
        when(asyncCustomService.granularityTypePageCondition(dimensionMonthList,combinationVO,"D_MONTH")).thenReturn(dimensionMonthFlag);

        CombTransformVO combTransformVO = new CombTransformVO();
        customService.asyncInitFlag(customCombList,manufactureCustomCombList,energyCustomCombList,manufactureEnergyCustomCombList,combinationVO, combTransformVO);
        Assert.assertNull(null);
    }

    @Test
    public void callFunctionRefreshDataTest() throws Exception {
        when(dmFocCustomCombDao.cusItemDtlDecode(any())).thenReturn("SUCCESS");
        when(dmFocCustomCombDao.cusItemAppend(any())).thenReturn("SUCCESS");
        when(dmFocCustomCombDao.customMonthComb(any())).thenReturn("SUCCESS");
        CombTransformVO combTransformVO = new CombTransformVO();
        combTransformVO.setPageFlag("MONTH");
        Method method = PowerMockito.method(CustomService.class, "purchaseCostTypeFunction", CombTransformVO.class);
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customAnnualFlag).when(asyncCustomService).callCustomAnnual(any());

        Future<Boolean> customMonthFlag = new AsyncResult<>(true);
        PowerMockito.doReturn(customMonthFlag).when(asyncCustomService).callCustomMonth(any());
        method.invoke(customService, combTransformVO);
        Assert.assertTrue(true);
    }

    @Test
    public void callFunctionRefreshData2Test() throws Exception {
        when(dmFocCustomCombDao.cusItemDtlDecode(any())).thenReturn("SUCCESS");
        when(dmFocCustomCombDao.cusItemAppend(any())).thenReturn("FAIL");
        CombTransformVO combTransformVO = new CombTransformVO();
        combTransformVO.setPageFlag("MONTH");
        Method method = PowerMockito.method(CustomService.class, "purchaseCostTypeFunction", CombTransformVO.class);
        try {
            method.invoke(customService, combTransformVO);
        } catch (Exception e) {
            LOGGER.error("函数错误");
        }
        Assert.assertTrue(true);
    }

    @Test
    public void callFunctionRefreshData3Test() throws Exception {
        when(dmFocCustomCombDao.cusItemDtlDecode(any())).thenReturn("SUCCESS");
        when(dmFocCustomCombDao.cusItemAppend(any())).thenReturn("SUCCESS");
        when(dmFocCustomCombDao.customMonthComb(any())).thenReturn("1");
        CombTransformVO combTransformVO = new CombTransformVO();
        combTransformVO.setPageFlag("MONTH");
        Method method = PowerMockito.method(CustomService.class, "purchaseCostTypeFunction", CombTransformVO.class);
        try {
            method.invoke(customService, combTransformVO);
        } catch (Exception e) {
            LOGGER.error("函数错误");
        }
        Assert.assertTrue(true);
    }

    @Test
    public void callFunctionRefreshData33Test() throws Exception {
        when(dmFocMadeCustomCombDao.cusMadeViewAnnualCost(any())).thenReturn("SUCCESS");
        when(dmFocMadeCustomCombDao.customMadeAnnual(any())).thenReturn("SUCCESS");
        CombTransformVO combTransformVO = new CombTransformVO();
        combTransformVO.setPageFlag("ANNUAL");
        Method method = PowerMockito.method(CustomService.class, "manufactureCostTypeFunction", CombTransformVO.class);
        try {
            method.invoke(customService, combTransformVO);
        } catch (Exception e) {
            LOGGER.error("函数错误");
        }
        Assert.assertTrue(true);
    }

    @Test
    public void getComb2MethodTest() throws Exception {
        CombTransformVO combTransformVO = new CombTransformVO();
        combTransformVO.setIndustryOrg("ICT");
        combTransformVO.setPageFlag("ANNUAL");
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        Mockito.when(asyncCustomService.callMadeCustomAnnual(combTransformVO)).thenReturn(customAnnualFlag);
        Whitebox.invokeMethod(customService, "callCombFunction", combTransformVO);
        combTransformVO.setIndustryOrg("ENERGY");
        Whitebox.invokeMethod(customService, "callCombFunction", combTransformVO);

        List<DmCustomCombVO> customVOList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setGroupCode("222");
        customVOList.add(dmCustomCombVO);
        String costType = "P";
        String industryOrg = "ICT";
        Long start = 0L;
        Long limit = 1L;
        Whitebox.invokeMethod(customService, "insertCustomRecursion", customVOList,costType,industryOrg,start,limit);

        Whitebox.invokeMethod(customService, "insertCustomRecursion", customVOList,"M",industryOrg,start,limit);
        String industryOrg2 = "ENERGY";
        Whitebox.invokeMethod(customService, "insertCustomRecursion", customVOList,costType,industryOrg2,start,limit);

        Whitebox.invokeMethod(customService, "insertCustomRecursion", customVOList,"M",industryOrg2,start,limit);

        String industryOrg3 = "IAS";
        Whitebox.invokeMethod(customService, "insertCustomRecursion", customVOList,costType,industryOrg3,start,limit);
        Whitebox.invokeMethod(customService, "insertCustomRecursion", customVOList,"M",industryOrg3,start,limit);

        CombinationVO combinationVO = new CombinationVO();
        combinationVO.setCustomId(11L);
        combinationVO.setCostType("P");
        combinationVO.setPageFlag("ANNUAL");
        combinationVO.setIndustryOrg("ICT");
        combinationVO.setOldPageFlag("ANNUAL");
        List<DmCustomCombVO> tempCustomCombList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO1= new DmCustomCombVO();
        dmCustomCombVO1.setGroupCode("1122");
        tempCustomCombList.add(dmCustomCombVO1);
        Mockito.when(dmFocCustomCombTempDao.getTempCustomCombList(combinationVO)).thenReturn(tempCustomCombList);

        Mockito.when(dmFocCustomCombDao.getCustomCombListByPage(combinationVO)).thenReturn(tempCustomCombList);
        Whitebox.invokeMethod(customService, "updateCombList", combinationVO, combTransformVO);

        List<DmCustomCombVO> tempCustomComb2List = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO2= new DmCustomCombVO();
        dmCustomCombVO2.setGroupCode("1122");
        dmCustomCombVO2.setSubEnableFlag("N");
        tempCustomComb2List.add(dmCustomCombVO1);
        Mockito.when(dmFocCustomCombTempDao.getTempCustomCombList(combinationVO)).thenReturn(tempCustomComb2List);
        Whitebox.invokeMethod(customService, "updateCombList", combinationVO, combTransformVO);
        Assert.assertTrue(true);
    }

    @Test
    public void getComb3MethodTest() throws Exception {

        CombTransformVO combTransformVO = new CombTransformVO();
        combTransformVO.setIndustryOrg("ICT");
        combTransformVO.setPageFlag("ANNUAL");

        combTransformVO.setIndustryOrg("ENERGY");
        CombinationVO combinationVO = new CombinationVO();
        combinationVO.setCustomId(11L);
        combinationVO.setCostType("P");
        combinationVO.setPageFlag("ANNUAL");
        combinationVO.setIndustryOrg("ICT");
        combinationVO.setOldPageFlag("ANNUAL");
        List<DmCustomCombVO> tempCustomComb2List = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO2= new DmCustomCombVO();
        dmCustomCombVO2.setGroupCode("1122");
        dmCustomCombVO2.setSubEnableFlag("N");
        tempCustomComb2List.add(dmCustomCombVO2);
        Future<Boolean> customAnnualFlag = new AsyncResult<>(true);
        Mockito.when(asyncCustomService.callMadeCustomAnnual(combTransformVO)).thenReturn(customAnnualFlag);
        Mockito.when(dmFocCustomCombDao.getCustomCombListByPage(combinationVO)).thenReturn(tempCustomComb2List);

        Mockito.when(dmFocCustomCombTempDao.getTempCustomCombList(combinationVO)).thenReturn(tempCustomComb2List);
        Whitebox.invokeMethod(customService, "updateCombList", combinationVO, combTransformVO);
        String userId = "";
        String roleId ="";
        Long originCustomId = 1L;
        List<String> subEnableFlagList = new ArrayList<>();
        List<DmCustomCombVO> dmCustomCombList = new ArrayList<>();
        List<DmCustomCombVO> customVOList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setGroupCode("221");
        dmCustomCombVO.setCustomId(11L);
        dmCustomCombVO.setCreationDate(new Timestamp(System.currentTimeMillis()));
        dmCustomCombVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        customVOList.add(dmCustomCombVO);
        combinationVO.setCustomVOList(customVOList);
        Whitebox.invokeMethod(customService, "dataSyncEditNoSync", combinationVO, userId, roleId,originCustomId, subEnableFlagList,dmCustomCombList);
        subEnableFlagList.add("N");
        dmCustomCombList.addAll(customVOList);
        Whitebox.invokeMethod(customService, "dataSyncEditNoSync", combinationVO, userId, roleId,originCustomId, subEnableFlagList,dmCustomCombList);

        Whitebox.invokeMethod(customService, "editSync", combinationVO, userId, roleId,originCustomId, subEnableFlagList,dmCustomCombList);
        List<DmCustomCombVO> dmCustomCombVOList = new ArrayList<>();
        Mockito.when(customCommonService.filterAnotherPageData(combinationVO,customVOList)).thenReturn(tempCustomComb2List);
        Whitebox.invokeMethod(customService, "editSync", combinationVO, userId, roleId,originCustomId, subEnableFlagList,dmCustomCombVOList);

        List<DmCustomCombVO> customCombList = new ArrayList<>();
        List<DmCustomCombVO> manufactureCustomCombList = new ArrayList<>();
        List<DmCustomCombVO> customCombEnergyList = new ArrayList<>();
        List<DmCustomCombVO> manufactureCustomCombEnergyList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO1 = new DmCustomCombVO();
        dmCustomCombVO1.setGroupCode("1111");
        dmCustomCombVO1.setPageFlag("aaa");
        dmCustomCombVO1.setGranularityType("U");
        customCombEnergyList.add(dmCustomCombVO1);

        DmCustomCombVO dmCustomCombVO22 = new DmCustomCombVO();
        dmCustomCombVO22.setGroupCode("1111");
        dmCustomCombVO22.setPageFlag("aaa");
        dmCustomCombVO22.setGranularityType("U");
        manufactureCustomCombEnergyList.add(dmCustomCombVO22);
        combTransformVO.setCustomCombIasList(manufactureCustomCombEnergyList);
        combTransformVO.setCustomCombIasMadeList(manufactureCustomCombEnergyList);
        customCombEnergyList.addAll(manufactureCustomCombEnergyList);
        manufactureCustomCombList.addAll(manufactureCustomCombEnergyList);
        List<DmCustomCombVO> generalAnnualList = new ArrayList<>();
        Future<Boolean> univalAnnualFlag = new AsyncResult<>(true);
        Mockito.when(asyncCustomService.granularityTypePageCondition(generalAnnualList, combinationVO,"U_ANNUAL")).thenReturn(univalAnnualFlag);
        Mockito.when(asyncCustomService.granularityTypePageCondition(generalAnnualList, combinationVO,"P_ANNUAL")).thenReturn(univalAnnualFlag);
        Mockito.when(asyncCustomService.granularityTypePageCondition(generalAnnualList, combinationVO,"D_ANNUAL")).thenReturn(univalAnnualFlag);
        Mockito.when(asyncCustomService.granularityTypePageCondition(generalAnnualList, combinationVO,"U_MONTH")).thenReturn(univalAnnualFlag);
        Mockito.when(asyncCustomService.granularityTypePageCondition(generalAnnualList, combinationVO,"P_MONTH")).thenReturn(univalAnnualFlag);
        Mockito.when(asyncCustomService.granularityTypePageCondition(generalAnnualList, combinationVO,"D_MONTH")).thenReturn(univalAnnualFlag);
        Whitebox.invokeMethod(customService, "asyncInitFlag", customCombList, manufactureCustomCombList, customCombEnergyList,manufactureCustomCombEnergyList, combinationVO,combTransformVO);
        Assert.assertTrue(true);
    }
}