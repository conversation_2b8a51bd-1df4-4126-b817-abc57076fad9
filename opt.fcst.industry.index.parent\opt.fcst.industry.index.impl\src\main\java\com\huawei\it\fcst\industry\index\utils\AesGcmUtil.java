/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.utils;

import com.huawei.it.jalor5.core.util.StringUtil;

import cn.hutool.core.codec.Base64;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Hex;
import org.bouncycastle.crypto.BlockCipher;
import org.bouncycastle.crypto.engines.AESEngine;
import org.bouncycastle.crypto.prng.SP800SecureRandomBuilder;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * <AUTHOR>
 * @since 2023/3/20
 */
@Slf4j
public class AesGcmUtil {
    private static final String KEY_ALGORITHM = "AES";

    private static final String AES_GCM_CIPHER_ALGORITHM = "AES/GCM/NoPadding";

    private static final int IV_SIZE = 12;

    private static final int GCM_TAG_LENGTH = 16;

    /**
     * 使用业务密钥加密数据
     *
     * @param plainData 明文
     * @param hexPlainKey 业务密钥
     * @return 密文
     * @throws DecoderException 解码失败时抛DecoderException
     */
    public static String encryptGcm(String hexPlainKey, String plainData) {
        try {
            if (StringUtil.isNullOrEmpty(hexPlainKey)) {
                return "workKey is not null";
            }
            byte[] secretKey = Hex.decodeHex(hexPlainKey);
            SecretKeySpec skeySpec = new SecretKeySpec(secretKey, KEY_ALGORITHM);
            Cipher cipher = Cipher.getInstance(AES_GCM_CIPHER_ALGORITHM);
            byte[] ivBytes = generateSecureRandomByte(IV_SIZE);

            cipher.init(Cipher.ENCRYPT_MODE, skeySpec, new GCMParameterSpec(GCM_TAG_LENGTH * 8, ivBytes));
            byte[] encryptData = cipher.doFinal(plainData.getBytes(StandardCharsets.UTF_8));
            return Hex.encodeHexString(ivBytes) + Hex.encodeHexString(encryptData);
        } catch (GeneralSecurityException | DecoderException e) {
            return "encrypt error:" + e.getMessage();
        }
    }

    /**
     * 生成指定字节数的随机字节
     *
     * @param byteSize 字节数
     * @return 随机字节
     * @throws NoSuchAlgorithmException NoSuchAlgorithmException
     */
    public static byte[] generateSecureRandomByte(int byteSize) throws NoSuchAlgorithmException {
        SecureRandom blockingRandom = SecureRandom.getInstanceStrong();
        // 是否是真熵源。物理机的/dev/random算是真熵源，虚拟机的不算。但bouncycastle的实现中没有使用这个参数
        boolean predictionResistant = false;
        // NID_aes_256_ctr
        BlockCipher cipher = new AESEngine();
        int cipherLen = 256;
        // 规范要求种子至少为384bit
        int entropyBitsRequired = 384;
        // 用于和熵源共同生成seed，可以为null
        byte[] nonce = null;
        // 是否每次取完随机数都重新刷新熵源，否则根据算法不同定期刷新
        boolean forceReseed = false;
        // CTR-DBRG
        SecureRandom secureRandom = new SP800SecureRandomBuilder(blockingRandom, predictionResistant)
                .setEntropyBitsRequired(entropyBitsRequired)
                .buildCTR(cipher, cipherLen, nonce, forceReseed);
        byte[] random = new byte[byteSize];
        secureRandom.nextBytes(random);
        return random;
    }

    /**
     * 加密key
     *
     * @param hexPlainKey 明文key
     * @return 转换后的key
     */
    public static String getBase64Encode(String hexPlainKey) {
        String encodeKey = "";
        if (StringUtil.isNullOrEmpty(hexPlainKey)) {
            return "workKey is not null";
        }
        try{
            byte[] bytes = hexPlainKey.getBytes("UTF-8");
            encodeKey= Base64.encode(bytes);
        } catch (UnsupportedEncodingException exception) {
            log.info("base64 Conversion failed:{}",exception.getMessage());
        }
        return encodeKey;
    }
}
