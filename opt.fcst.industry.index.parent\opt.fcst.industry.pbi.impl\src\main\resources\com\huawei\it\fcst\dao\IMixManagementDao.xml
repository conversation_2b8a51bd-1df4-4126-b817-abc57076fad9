<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.pbi.dao.IMixManagementDao">
    <resultMap type="com.huawei.it.fcst.industry.pbi.vo.mix.DmFocMixResultVO" id="resultMap">
        <result property="periodYear" column="period_year"/>
        <result property="periodId" column="period_id"/>
        <result property="prodRndTeamCode" column="prod_rnd_team_code"/>
        <result property="prodRndTeamCnName" column="prod_rnd_team_cn_name"/>
        <result property="parentLevel" column="parent_level"/>
        <result property="parentCode" column="parent_code"/>
        <result property="parentCnName" column="parent_cn_name"/>
        <result property="groupCode" column="group_code"/>
        <result property="groupCnName" column="group_cn_name"/>
        <result property="groupLevel" column="group_level"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDdate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="dimensionCode" column="dimension_code"/>
        <result property="dimensionCnName" column="dimension_cn_name"/>
        <result property="dimensionSubCategoryCode" column="dimension_subcategory_code"/>
        <result property="dimensionSubCategoryCnName" column="dimension_subcategory_cn_name"/>
        <result property="dimensionSubDetailCode" column="dimension_sub_detail_code"/>
        <result property="dimensionSubDetailCnName" column="dimension_sub_detail_cn_name"/>
        <result property="rmbCostAmt" column="rmb_cost_amt"/>
        <result property="costIndex" column="cost_index"/>
        <result property="costType" column="costType"/>
        <result property="weightRate" column="weight_rate"/>
        <result property="rmbCostAmt" column="rmb_cost_amt"/>
        <result property="pspRmbCostAmt" column="psp_rmb_cost_amt"/>
        <result property="stdRmbCostAmt" column="std_rmb_cost_amt"/>
        <result property="gapPspStd" column="gap_psp_std"/>
        <result property="ratioPspStd" column="ratio_psp_std"/>
        <result property="customId" column="custom_id"/>
    </resultMap>

    <sql id="inProdTeamGroupCodeByCostType">
        <choose>
            <when test='granularityType == "IRB"'>
                <if test='costType =="PSP" and pspProdRndTeamCodeList != null and pspProdRndTeamCodeList.size() > 0'>
                    <foreach collection='pspProdRndTeamCodeList' item="code" open="AND prod_rnd_team_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='costType =="STD" and stdProdRndTeamCodeList != null and stdProdRndTeamCodeList.size() > 0'>
                    <foreach collection='stdProdRndTeamCodeList' item="code" open="AND prod_rnd_team_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
            </when>
            <when test='granularityType == "INDUS"'>
                <if test='costType =="PSP" and pspProdRndTeamCodeList != null and pspProdRndTeamCodeList.size() > 0'>
                    <foreach collection='pspProdRndTeamCodeList' item="code" open="AND industry_catg_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='costType =="STD" and stdProdRndTeamCodeList != null and stdProdRndTeamCodeList.size() > 0'>
                    <foreach collection='stdProdRndTeamCodeList' item="code" open="AND industry_catg_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
            </when>
            <when test='granularityType == "PROD"'>
                <if test='costType =="PSP" and pspProdRndTeamCodeList != null and pspProdRndTeamCodeList.size() > 0'>
                    <foreach collection='pspProdRndTeamCodeList' item="code" open="AND prod_list_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='costType =="STD" and stdProdRndTeamCodeList != null and stdProdRndTeamCodeList.size() > 0'>
                    <foreach collection='stdProdRndTeamCodeList' item="code" open="AND prod_list_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='costType =="PSP" and parentPspGroupCodeList != null and parentPspGroupCodeList.size() > 0'>
            <foreach collection='parentPspGroupCodeList' item="code" open="AND parent_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='costType =="STD" and parentStdGroupCodeList != null and parentStdGroupCodeList.size() > 0'>
            <foreach collection='parentStdGroupCodeList' item="code" open="AND parent_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='costType =="PSP" and pspGroupCodeList != null and pspGroupCodeList.size() > 0'>
            <foreach collection='pspGroupCodeList' item="code" open="AND group_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='costType =="STD" and stdGroupCodeList != null and stdGroupCodeList.size() > 0'>
            <foreach collection='stdGroupCodeList' item="code" open="AND group_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
    </sql>

    <sql id="inRatioFlagByCostType">
        <if test='costType =="PSP" and parentPspGroupCodeList != null and parentPspGroupCodeList.size() > 0'>
            <foreach collection='parentPspGroupCodeList' item="code" open="AND parent_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='costType =="STD" and parentStdGroupCodeList != null and parentStdGroupCodeList.size() > 0'>
            <foreach collection='parentStdGroupCodeList' item="code" open="AND parent_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='costType =="PSP" and pspGroupCodeList != null and pspGroupCodeList.size() > 0'>
            <foreach collection='pspGroupCodeList' item="code" open="AND group_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='costType =="STD" and stdGroupCodeList != null and stdGroupCodeList.size() > 0'>
            <foreach collection='stdGroupCodeList' item="code" open="AND group_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
    </sql>

    <sql id="inCustomByCostType">
        <if test='costType=="PSP" and pspCustomIdList != null and pspCustomIdList.size() > 0'>
            <foreach collection='pspCustomIdList' item="id" open="AND custom_id IN (" close=")" index="index" separator=",">
                #{id}
            </foreach>
        </if>
        <if test='costType=="STD" and stdCustomIdList != null and stdCustomIdList.size() > 0'>
            <foreach collection='stdCustomIdList' item="id" open="AND custom_id IN (" close=")" index="index" separator=",">
                #{id}
            </foreach>
        </if>
        <if test='costType=="PSP" and customPspParentCodeList != null and customPspParentCodeList.size() > 0'>
            <foreach collection='customPspParentCodeList' item="code" open="AND parent_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='costType=="STD" and customStdParentCodeList != null and customStdParentCodeList.size() > 0'>
            <foreach collection='customStdParentCodeList' item="code" open="AND parent_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='costType=="PSP" and pspCustomGroupCodeList != null and pspCustomGroupCodeList.size() > 0'>
            <foreach collection='pspCustomGroupCodeList' item="code" open="AND group_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='costType=="STD" and stdCustomGroupCodeList != null and stdCustomGroupCodeList.size() > 0'>
            <foreach collection='stdCustomGroupCodeList' item="code" open="AND group_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
    </sql>

    <select id="distributeChartMultiType" resultMap="resultMap">
        select
        period_id,sum(rmb_cost_amt) as rmb_cost_amt
        <if test='ytdFlag == "MON"'>
            from fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_MON_COST_AMT_T
        </if>
        <if test='ytdFlag == "YTD"'>
            from fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_MON_YTD_AMT_T
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='versionId != null'>
                and version_id = #{versionId,jdbcType=NUMERIC}
            </if>
            <if test='overseaFlag != null and overseaFlag != ""'>
                and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='bgCode != null and bgCode != ""'>
                and bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='regionCode != null and regionCode != ""'>
                and region_code = #{regionCode,jdbcType=VARCHAR}
            </if>
            <if test='repofficeCode != null and repofficeCode != ""'>
                and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='mainFlag != null and mainFlag != ""'>
                and main_flag = #{mainFlag,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes != null and codeAttributes != ""'>
                and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='groupLevel != null and groupLevel != ""'>
                and group_level = #{groupLevel,jdbcType=VARCHAR}
            </if>
            <if test='viewFlag != null and viewFlag != ""'>
                and view_flag = #{viewFlag,jdbcType=VARCHAR}
            </if>
            <if test='costType=="PSP" and softwareMark != null and softwareMark!=""'>
                and software_mark = #{softwareMark,jdbcType=VARCHAR}
            </if>
            <if test='dimensionCode != null and dimensionCode != ""'>
                and dimension_code = #{dimensionCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubCategoryCode != null and dimensionSubCategoryCode != ""'>
                and dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubDetailCode != null and dimensionSubDetailCode != ""'>
                and dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
            </if>
            <include refid="inProdTeamGroupCodeByCostType"/>
            and del_flag = 'N'
        <include refid="userPermisson"></include>
        </trim>
        group by period_id
        order by period_id
    </select>

    <sql id="userPermisson">
        <choose>
            <when test='granularityType == "IRB"'>
                <if test='groupLevel == "LV0" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
                    <foreach collection='lv1DimensionSet' item="code" open="AND prod_rnd_team_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                    <foreach collection='lv2DimensionSet' item="code" open="AND prod_rnd_team_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='groupLevel == "LV2" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                    <foreach collection='lv3DimensionSet' item="code" open="AND prod_rnd_team_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
            </when>
            <when test='granularityType == "INDUS"'>
                <if test='groupLevel == "LV0" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
                    <foreach collection='lv1DimensionSet' item="code" open="AND industry_catg_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                    <foreach collection='lv2DimensionSet' item="code" open="AND industry_catg_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='groupLevel == "LV2" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                    <foreach collection='lv3DimensionSet' item="code" open="AND prod_list_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
            </when>
            <when test='granularityType == "PROD"'>
                <if test='groupLevel == "LV0" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
                    <foreach collection='lv1DimensionSet' item="code" open="AND prod_list_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                    <foreach collection='lv2DimensionSet' item="code" open="AND prod_list_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='groupLevel == "LV2" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                    <foreach collection='lv3DimensionSet' item="code" open="AND lv2_prod_list_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
            </when>
        </choose>
    </sql>

    <select id="distributeCustomChartMultiType" resultMap="resultMap">
        select
        period_id,sum(rmb_cost_amt) as rmb_cost_amt
        <if test='ytdFlag == "MON"'>
            from fin_dm_opt_foi.DM_FCST_ICT_${costType}_BASE_CUS_MON_COST_AMT_T
        </if>
        <if test='ytdFlag == "YTD"'>
            from fin_dm_opt_foi.DM_FCST_ICT_${costType}_BASE_CUS_MON_YTD_COST_AMT_T
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='versionId != null'>
                and version_id = #{versionId,jdbcType=NUMERIC}
            </if>
            <if test='granularityType != null and granularityType != ""'>
                AND GRANULARITY_TYPE = #{granularityType}
            </if>
            <if test='overseaFlag != null and overseaFlag != ""'>
                and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='bgCode != null and bgCode != ""'>
                and bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='regionCode != null and regionCode != ""'>
                and region_code = #{regionCode,jdbcType=VARCHAR}
            </if>
            <if test='repofficeCode != null and repofficeCode != ""'>
                and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='groupLevel != null and groupLevel != ""'>
                and group_level = #{groupLevel,jdbcType=VARCHAR}
            </if>
            <if test='groupCode != null and groupCode != ""'>
                and group_code = #{groupCode,jdbcType=VARCHAR}
            </if>
            <if test='mainFlag != null and mainFlag != ""'>
                and main_flag = #{mainFlag,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes != null and codeAttributes != ""'>
                and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='costType=="PSP" and softwareMark != null and softwareMark!=""'>
                and software_mark = #{softwareMark,jdbcType=VARCHAR}
            </if>
            <if test='viewFlag != null and viewFlag != ""'>
                and view_flag = #{viewFlag,jdbcType=VARCHAR}
            </if>
            <include refid="inCustomByCostType"/>
            and  del_flag = 'N'
        </trim>
        group by period_id
        order by period_id
    </select>

    <select id="findCustomPriceIndexMultiType" resultMap="resultMap">
        select
        group_level,group_code,group_cn_name,parent_cn_name,parent_code,
        period_id,cost_index
        <if test='ytdFlag == "MON"'>
            from fin_dm_opt_foi.DM_FCST_ICT_${costType}_BASE_CUS_MON_COST_IDX_T
        </if>
        <if test='ytdFlag == "YTD"'>
            from fin_dm_opt_foi.DM_FCST_ICT_${costType}_BASE_CUS_YTD_COST_IDX_T
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='versionId != null'>
                and version_id = #{versionId,jdbcType=NUMERIC}
            </if>
            <if test='ytdFlag != null and ytdFlag != ""'>
                and ytd_flag = #{ytdFlag,jdbcType=VARCHAR}
            </if>
            <if test='granularityType != null and granularityType != ""'>
                and granularity_type = #{granularityType,jdbcType=VARCHAR}
            </if>
            <if test='overseaFlag != null and overseaFlag != ""'>
                and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='bgCode != null and bgCode != ""'>
                and bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='regionCode != null and regionCode != ""'>
                and region_code = #{regionCode,jdbcType=VARCHAR}
            </if>
            <if test='repofficeCode != null and repofficeCode != ""'>
                and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='mainFlag != null and mainFlag != ""'>
                and main_flag = #{mainFlag,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes != null and codeAttributes != ""'>
                and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='costType=="PSP" and softwareMark != null and softwareMark!=""'>
                and software_mark = #{softwareMark,jdbcType=VARCHAR}
            </if>
            <if test='groupCode != null and groupCode != ""'>
                and group_code = #{groupCode,jdbcType=VARCHAR}
            </if>
            <if test='groupLevel != null and groupLevel != ""'>
                and group_level = #{groupLevel,jdbcType=VARCHAR}
            </if>
            <if test='parentCode != null and parentCode != ""'>
                and parent_code = #{parentCode,jdbcType=VARCHAR}
            </if>
            <if test='viewFlag != null and viewFlag != ""'>
                and view_flag = #{viewFlag,jdbcType=VARCHAR}
            </if>
            <include refid="inCustomByCostType"/>
            and  del_flag = 'N'
        </trim>
        order by group_cn_name,parent_code,period_id
    </select>

    <select id="findPriceIndexMultiType" resultMap="resultMap">
        select
        dimension_code,dimension_cn_name,
        dimension_subcategory_code,dimension_subcategory_cn_name,
        dimension_sub_detail_code,dimension_sub_detail_cn_name,
        group_level,group_code,group_cn_name,parent_cn_name,parent_code,
        period_id, cost_index,
        <choose>
            <when test='granularityType == "IRB"'>
                prod_rd_team_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "INDUS"'>
                industry_catg_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "PROD"'>
                prod_list_cn_name as prod_rnd_team_cn_name
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='ytdFlag == "MON"'>
            from fin_dm_opt_foi.DM_FCST_ICT_${costType}_${granularityType}_MON_COST_IDX_T
        </if>
        <if test='ytdFlag == "YTD"'>
            from fin_dm_opt_foi.DM_FCST_ICT_${costType}_${granularityType}_YTD_COST_IDX_T
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='versionId != null'>
                and version_id = #{versionId,jdbcType=NUMERIC}
            </if>
            <if test='basePeriodId != null'>
                and base_period_id = #{basePeriodId}
            </if>
            <if test='overseaFlag != null and overseaFlag != ""'>
                and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='bgCode != null and bgCode != ""'>
                and bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='regionCode != null and regionCode != ""'>
                and region_code = #{regionCode,jdbcType=VARCHAR}
            </if>
            <if test='repofficeCode != null and repofficeCode != ""'>
                and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='costType=="PSP" and softwareMark != null and softwareMark!=""'>
                and software_mark = #{softwareMark,jdbcType=VARCHAR}
            </if>
            <if test='mainFlag != null and mainFlag != ""'>
                and main_flag = #{mainFlag,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes != null and codeAttributes != ""'>
                and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='groupLevel != null and groupLevel != ""'>
                and group_level = #{groupLevel,jdbcType=VARCHAR}
            </if>
            <if test='viewFlag != null and viewFlag != ""'>
                and view_flag = #{viewFlag,jdbcType=VARCHAR}
            </if>
            <if test='dimensionCode != null and dimensionCode != ""'>
                and dimension_code = #{dimensionCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubCategoryCode != null and dimensionSubCategoryCode != ""'>
                and dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubDetailCode != null and dimensionSubDetailCode != ""'>
                and dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
            </if>
            <include refid="inProdTeamGroupCodeByCostType"/>
            and  del_flag = 'N'
        <include refid="userPermisson"></include>
        </trim>
        order by group_cn_name,period_id
    </select>

    <select id="findPriceIndexMinLevelMultiType" resultMap="resultMap">
        select
        dimension_code,dimension_cn_name,
        dimension_subcategory_code,dimension_subcategory_cn_name,
        dimension_sub_detail_code,dimension_sub_detail_cn_name,
        group_level,group_code,group_cn_name,parent_cn_name,parent_code,
        period_id, cost_index,
        <choose>
            <when test='granularityType == "IRB"'>
                prod_rd_team_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "INDUS"'>
                industry_catg_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "PROD"'>
                prod_list_cn_name as prod_rnd_team_cn_name
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='ytdFlag == "MON"'>
            from fin_dm_opt_foi.dm_fcst_ict_${costType}_${granularityType}_mon_mid_cost_idx_t
        </if>
        <if test='ytdFlag == "YTD"'>
            from fin_dm_opt_foi.dm_fcst_ict_${costType}_${granularityType}_ytd_mid_cost_idx_t
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='versionId != null'>
                and version_id = #{versionId,jdbcType=NUMERIC}
            </if>
            <if test='basePeriodId != null'>
                and base_period_id = #{basePeriodId}
            </if>
            <if test='overseaFlag != null and overseaFlag != ""'>
                and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='bgCode != null and bgCode != ""'>
                and bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='regionCode != null and regionCode != ""'>
                and region_code = #{regionCode,jdbcType=VARCHAR}
            </if>
            <if test='repofficeCode != null and repofficeCode != ""'>
                and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='costType=="PSP" and softwareMark != null and softwareMark!=""'>
                and software_mark = #{softwareMark,jdbcType=VARCHAR}
            </if>
            <if test='mainFlag != null and mainFlag != ""'>
                and main_flag = #{mainFlag,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes != null and codeAttributes != ""'>
                and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='groupLevel != null and groupLevel != ""'>
                and group_level = #{groupLevel,jdbcType=VARCHAR}
            </if>
            <if test='viewFlag != null and viewFlag != ""'>
                and view_flag = #{viewFlag,jdbcType=VARCHAR}
            </if>
            <if test='dimensionCode != null and dimensionCode != ""'>
                and dimension_code = #{dimensionCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubCategoryCode != null and dimensionSubCategoryCode != ""'>
                and dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubDetailCode != null and dimensionSubDetailCode != ""'>
                and dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
            </if>
            <if test='costType=="PSP" and pspCustomGroupCodeList != null and pspCustomGroupCodeList.size() > 0'>
                <foreach collection='pspCustomGroupCodeList' item="code" open="AND group_code IN (" close=")" index="index" separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='costType=="STD" and stdCustomGroupCodeList != null and stdCustomGroupCodeList.size() > 0'>
                <foreach collection='stdCustomGroupCodeList' item="code" open="AND group_code IN (" close=")" index="index" separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='costType=="PSP" and pspProdRndTeamCodeList != null and pspProdRndTeamCodeList.size() > 0'>
                <choose>
                    <when test='teamLevel == "LV0"'>
                        <foreach collection='pspProdRndTeamCodeList' item="code" open="AND lv0_code IN (" close=")"
                                 index="index"
                                 separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='teamLevel == "LV1"'>
                        <foreach collection='pspProdRndTeamCodeList' item="code" open="AND lv1_code IN (" close=")"
                                 index="index"
                                 separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='teamLevel == "LV2"'>
                        <foreach collection='pspProdRndTeamCodeList' item="code" open="AND lv2_code IN (" close=")"
                                 index="index"
                                 separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='teamLevel == "LV3"'>
                        <foreach collection='pspProdRndTeamCodeList' item="code" open="AND lv3_code IN (" close=")"
                                 index="index"
                                 separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='teamLevel == "LV4"'>
                        <foreach collection='pspProdRndTeamCodeList' item="code" open="AND lv4_code IN (" close=")"
                                 index="index"
                                 separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
            <if test='costType=="STD" and stdProdRndTeamCodeList != null and stdProdRndTeamCodeList.size() > 0'>
                <choose>
                    <when test='teamLevel == "LV0"'>
                        <foreach collection='stdProdRndTeamCodeList' item="code" open="AND lv0_code IN (" close=")"
                                 index="index"
                                 separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='teamLevel == "LV1"'>
                        <foreach collection='stdProdRndTeamCodeList' item="code" open="AND lv1_code IN (" close=")"
                                 index="index"
                                 separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='teamLevel == "LV2"'>
                        <foreach collection='stdProdRndTeamCodeList' item="code" open="AND lv2_code IN (" close=")"
                                 index="index"
                                 separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='teamLevel == "LV3"'>
                        <foreach collection='stdProdRndTeamCodeList' item="code" open="AND lv3_code IN (" close=")"
                                 index="index"
                                 separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='teamLevel == "LV4"'>
                        <foreach collection='stdProdRndTeamCodeList' item="code" open="AND lv4_code IN (" close=")"
                                 index="index"
                                 separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
            and del_flag = 'N'
        <include refid="userPermisson"></include>
        </trim>
        order by group_cn_name,prod_rnd_team_cn_name,period_id
    </select>

    <select id="getSummaryCombCurrentPriceIndex" resultMap="resultMap">
        select
        group_level,group_code,group_cn_name,
        period_id,cost_index
        <if test='ytdFlag == "MON"'>
            from fin_dm_opt_foi.DM_FCST_ICT_${costType}_CUS_MON_COST_IDX_T
        </if>
        <if test='ytdFlag == "YTD"'>
            from fin_dm_opt_foi.DM_FCST_ICT_${costType}_CUS_YTD_COST_IDX_T
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='versionId != null'>
                and version_id = #{versionId,jdbcType=NUMERIC}
            </if>
            <if test='customId != null'>
                AND custom_id = #{customId}
            </if>
            <if test='basePeriodId != null'>
                AND base_period_id = #{basePeriodId}
            </if>
            <if test='granularityType != null and granularityType != ""'>
                and granularity_type = #{granularityType,jdbcType=VARCHAR}
            </if>
            <if test='overseaFlag != null and overseaFlag != ""'>
                and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='bgCode != null and bgCode != ""'>
                and bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='regionCode != null and regionCode != ""'>
                and region_code = #{regionCode,jdbcType=VARCHAR}
            </if>
            <if test='repofficeCode != null and repofficeCode != ""'>
                and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='costType=="PSP" and softwareMark != null and softwareMark!=""'>
                and software_mark = #{softwareMark,jdbcType=VARCHAR}
            </if>
            <if test='mainFlag != null and mainFlag != ""'>
                and main_flag = #{mainFlag,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes != null and codeAttributes != ""'>
                and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            and del_flag = 'N'
        </trim>
        order by group_cn_name,period_id
    </select>

</mapper>