/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.drop;

import com.huawei.it.fcst.industry.pbi.vo.common.CommonBaseVO;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/26
 */
@Setter
@Getter
@EqualsAndHashCode
@NoArgsConstructor
public class BaseCusDimVO extends CommonBaseVO {

    /**
     * 用户id
     */
    private String userId;

    /**
     * 角色id
     */
    private String roleId;

    /**
     * 虚化组合id
     */
    private Long customId;

    /**
     * 虚化组合状态
     */
    private String statusFlag;

    /**
     * 编码类型（NEW:新编码  OLD: 旧编码   TOP:Top编码）
     */
    private String codeType;

    private String relationType;

    private String replaceRelationType;

    private String replaceRelationName;

    private List<String> codeTypeList;
}
