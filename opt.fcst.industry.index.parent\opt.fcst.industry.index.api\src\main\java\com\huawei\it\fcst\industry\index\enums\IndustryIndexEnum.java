/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2022/6/7
 */
public interface IndustryIndexEnum {
    enum COST_TYPE {
        M("M", "制造成本"),
        P("P", "采购成本"),
        T("T", "总成本");
        @Getter
        @Setter
        private String value;
        @Getter
        @Setter
        private String desc;
        COST_TYPE(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    enum GRANULARITY_TYPE {
        UNIVERSAL("U", "通用"),
        PROFITS("P", "盈利"),
        DIMENSION("D", "量纲");
        @Getter
        @Setter
        private String value;
        @Getter
        @Setter
        private String desc;
        GRANULARITY_TYPE(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    enum VIEW_FLAG_U {
        VIEW1("0", "LV0-专项采购认证部-模块-品类"),
        VIEW2("1", "LV0-LV1-专项采购认证部-模块-品类"),
        VIEW3("2", "LV0-LV1-LV2-专项采购认证部-模块-品类"),
        VIEW4("3", "LV0-LV1-LV2-LV3-专项采购认证部-模块-品类"),
        VIEW5("4", "LV0-专项采购认证部-LV1-LV2"),
        VIEW6("5", "LV0-专项采购认证部-模块-LV1-LV2"),
        VIEW7("6", "LV0-专项采购认证部-模块-品类-LV1-LV2"),
        VIEW8("7", "LV0-LV1-LV2-LV3-LV3.5-专项采购认证部-模块-品类");
        @Getter
        @Setter
        private String value;
        @Getter
        @Setter
        private String desc;
        VIEW_FLAG_U(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    enum VIEW_FLAG_U_M {
        VIEW1("0", "LV0-发货对象-制造对象"),
        VIEW2("1", "LV0-LV1-发货对象-制造对象"),
        VIEW3("2", "LV0-LV1-LV2-发货对象-制造对象"),
        VIEW4("3", "LV0-LV1-LV2-LV3-发货对象-制造对象"),
        VIEW5("4", "LV0-发货对象-LV1-LV2"),
        VIEW6("5", "LV0-发货对象-制造对象-LV1-LV2"),
        VIEW8("7", "LV0-LV1-LV2-LV3-LV3.5-发货对象-制造对象");
        @Getter
        @Setter
        private String value;
        @Getter
        @Setter
        private String desc;
        VIEW_FLAG_U_M(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    enum VIEW_FLAG_P {
        VIEW1("0", "LV0-专项采购认证部-模块-品类"),
        VIEW2("1", "LV0-LV1-专项采购认证部-模块-品类"),
        VIEW3("2", "LV0-LV1-LV2-专项采购认证部-模块-品类"),
        VIEW4("3", "LV0-LV1-LV2-L1-专项采购认证部-模块-品类"),
        VIEW5("4", "LV0-LV1-LV2-L1-L2-专项采购认证部-模块-品类");
        @Getter
        @Setter
        private String value;
        @Getter
        @Setter
        private String desc;
        VIEW_FLAG_P(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    enum VIEW_FLAG_P_M {
        VIEW1("0", "LV0-发货对象-制造对象"),
        VIEW2("1", "LV0-LV1-发货对象-制造对象"),
        VIEW3("2", "LV0-LV1-LV2-发货对象-制造对象"),
        VIEW4("3", "LV0-LV1-LV2-L1-发货对象-制造对象"),
        VIEW5("4", "LV0-LV1-LV2-L1-L2-发货对象-制造对象");
        @Getter
        @Setter
        private String value;
        @Getter
        @Setter
        private String desc;
        VIEW_FLAG_P_M(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    enum VIEW_FLAG_D {
        VIEW1("0", "LV0-LV1-量纲-专项采购认证部-模块-品类"),
        VIEW2("1", "LV0-LV1-量纲-量纲子类-专项采购认证部-模块-品类"),
        VIEW3("2", "LV0-LV1-量纲-量纲子类-子类明细-专项采购认证部-模块-品类"),
        VIEW4("3", "LV0-LV1-LV2-量纲-专项采购认证部-模块-品类"),
        VIEW5("4", "LV0-LV1-LV2-量纲-量纲子类-专项采购认证部-模块-品类"),
        VIEW6("5", "LV0-LV1-LV2-量纲-量纲子类-子类明细-专项采购认证部-模块-品类"),
        VIEW7("6", "LV0-LV1-LV2-LV3-量纲-专项采购认证部-模块-品类"),
        VIEW8("7", "LV0-LV1-LV2-LV3-量纲-量纲子类-专项采购认证部-模块-品类"),
        VIEW9("8", "LV0-LV1-LV2-LV3-量纲-量纲子类-子类明细-专项采购认证部-模块-品类"),
        VIEW10("9", "LV0-LV1-量纲-量纲子类-子类明细-SPART-专项采购认证部-模块-品类"),
        VIEW11("10", "LV0-LV1-LV2-量纲-量纲子类-子类明细-SPART-专项采购认证部-模块-品类"),
        VIEW12("11", "LV0-LV1-LV2-LV3-量纲-量纲子类-子类明细-SPART-专项采购认证部-模块-品类"),
        VIEW13("12", "LV0-LV1-LV2-LV3-COA-量纲-量纲子类-子类明细-SPART-专项采购认证部-模块-品类");
        @Getter
        @Setter
        private String value;
        @Getter
        @Setter
        private String desc;
        VIEW_FLAG_D(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }
    }



    enum VIEW_FLAG_D_M {
        VIEW1("0", "LV0-LV1-量纲-发货对象-制造对象"),
        VIEW2("1", "LV0-LV1-量纲-量纲子类-发货对象-制造对象"),
        VIEW3("2", "LV0-LV1-量纲-量纲子类-子类明细-发货对象-制造对象"),
        VIEW4("3", "LV0-LV1-LV2-量纲-发货对象-制造对象"),
        VIEW5("4", "LV0-LV1-LV2-量纲-量纲子类-发货对象-制造对象"),
        VIEW6("5", "LV0-LV1-LV2-量纲-量纲子类-子类明细-发货对象-制造对象"),
        VIEW7("6", "LV0-LV1-LV2-LV3-量纲-发货对象-制造对象"),
        VIEW8("7", "LV0-LV1-LV2-LV3-量纲-量纲子类-发货对象-制造对象"),
        VIEW9("8", "LV0-LV1-LV2-LV3-量纲-量纲子类-子类明细-发货对象-制造对象"),
        VIEW10("9", "LV0-LV1-量纲-量纲子类-子类明细-SPART-发货对象-制造对象"),
        VIEW11("10", "LV0-LV1-LV2-量纲-量纲子类-子类明细-SPART-发货对象-制造对象"),
        VIEW12("11", "LV0-LV1-LV2-LV3-量纲-量纲子类-子类明细-SPART-发货对象-制造对象"),
        VIEW13("12", "LV0-LV1-LV2-LV3-COA-量纲-量纲子类-子类明细-SPART-发货对象-制造对象");
        @Getter
        @Setter
        private String value;
        @Getter
        @Setter
        private String desc;
        VIEW_FLAG_D_M(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }
    }


    enum VIEW_FLAG_SHEET {
        VIEW1("0", "视角1"),
        VIEW2("1", "视角2"),
        VIEW3("2", "视角3"),
        VIEW4("3", "视角4"),
        VIEW5("4", "视角5"),
        VIEW6("5", "视角6"),
        VIEW7("6", "视角7"),
        VIEW8("7", "视角8"),
        VIEW9("8", "视角9"),
        VIEW10("9", "视角10"),
        VIEW11("10", "视角11"),
        VIEW12("11", "视角12"),
        VIEW13("12", "视角13");
        @Getter
        @Setter
        private String value;
        @Getter
        @Setter
        private String name;
        VIEW_FLAG_SHEET(String value,String name) {
            this.value = value;
            this.name = name;
        }
    }

    enum DataType {
        CATE("CATEGORY","品类"),
        ITEM("ITEM","item");
        @Getter
        @Setter
        private String value;
        @Getter
        @Setter
        private String desc;
        DataType(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    enum CALIBER_FLAG {
        U("R","收入"),
        C("C","发货");
        @Getter
        @Setter
        private String value;
        @Getter
        @Setter
        private String desc;
        CALIBER_FLAG(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    enum REPLACE_COST_TYPE {
        STD("STD", "标准成本"),
        SAME("SAME", "同编码"),
        REPLACE("REPLACE", "研发替代");
        @Getter
        @Setter
        private String value;
        @Getter
        @Setter
        private String desc;
        REPLACE_COST_TYPE(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    static VIEW_FLAG_P getViewFlagProfits(String key){
        for (VIEW_FLAG_P viewFlag : VIEW_FLAG_P.values()) {
            if (viewFlag.getValue().equalsIgnoreCase(key)) {
                return viewFlag;
            }
        }
        return null;
    }

    static VIEW_FLAG_D getViewFlagDimension(String key){
        for (VIEW_FLAG_D viewFlag : VIEW_FLAG_D.values()) {
            if (viewFlag.getValue().equalsIgnoreCase(key)) {
                return viewFlag;
            }
        }
        return null;
    }

    static CALIBER_FLAG getCaliberFlag(String key){
        for (CALIBER_FLAG caliberFlag : CALIBER_FLAG.values()) {
            if (caliberFlag.getValue().equalsIgnoreCase(key)) {
                return caliberFlag;
            }
        }
        return null;
    }

    static COST_TYPE getCostTypeFlag(String key){
        for (COST_TYPE costTypeFlag : COST_TYPE.values()) {
            if (costTypeFlag.getValue().equalsIgnoreCase(key)) {
                return costTypeFlag;
            }
        }
        return null;
    }

    static GRANULARITY_TYPE getGranularityType(String key){
        for (GRANULARITY_TYPE granule : GRANULARITY_TYPE.values()) {
            if (granule.getValue().equalsIgnoreCase(key)) {
                return granule;
            }
        }
        return null;
    }

    static COST_TYPE getCostType(String key){
        for (COST_TYPE costType : COST_TYPE.values()) {
            if (costType.getValue().equalsIgnoreCase(key)) {
                return costType;
            }
        }
        return null;
    }

    static REPLACE_COST_TYPE getStandCostType(String key){
        for (REPLACE_COST_TYPE costType : REPLACE_COST_TYPE.values()) {
            if (costType.getValue().equalsIgnoreCase(key)) {
                return costType;
            }
        }
        return null;
    }
}
