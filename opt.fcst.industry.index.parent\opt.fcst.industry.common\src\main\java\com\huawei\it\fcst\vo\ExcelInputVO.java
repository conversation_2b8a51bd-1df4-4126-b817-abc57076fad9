/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.vo;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.poi.xssf.streaming.SXSSFSheet;

import java.util.List;

/**
 * ExcelVO Class
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
@Getter
@Setter
@NoArgsConstructor
public class ExcelInputVO {

    private SXSSFSheet sheet;

    private ExportExcelVo exportExcelVo;

    private List<AbstractExcelTitleVO> excelTitleVOS;

    private List<AbstractExcelTitleVO> selectedLeafExcelTitleVO;

    private List<AbstractExcelTitleVO> formsVOs;

}
