/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.utils;

import com.huawei.it.jalor5.core.request.IUserPrincipal;
import com.huawei.it.jalor5.core.request.RequestContextException;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.security.RoleVO;
import com.huawei.it.jalor5.security.UserVO;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * UserInfo
 *
 * <AUTHOR>
 * @since 2021 -04-20
 */
public class UserInfoUtils {
    /**
     * getCurrentEmployeeNo
     *
     * @return String current employee no
     */
    public static String getCurrentEmployeeNo() {
        IUserPrincipal user = RequestContext.getCurrent().getUser();
        if (user != null) {
            return user.getEmployeeNumber();
        }
        throw new RequestContextException();
    }

    /**
     * get role id of the current login user
     *
     * @return UserVO
     */
    public static int getRoleId() {
        return getCurrentUser().getCurrentRole().getRoleId();
    }

    /**
     * getUserId
     *
     * @return Long user id
     */
    public static Long getUserId() {
        IUserPrincipal user = RequestContext.getCurrent().getUser();
        if (user != null) {
            return user.getUserId();
        }
        throw new RequestContextException();
    }

    /**
     * getUserCN
     *
     * @return String user cn
     */
    public static String getUserCn() {
        IUserPrincipal user = RequestContext.getCurrent().getUser();
        if (user != null) {
            return user.getUserCN();
        }
        throw new RequestContextException();
    }

    /**
     * get User Account
     *
     * @return String user account
     */
    public static String getUserAccount() {
        IUserPrincipal user = RequestContext.getCurrent().getUser();
        if (user != null) {
            return user.getUserAccount();
        }
        throw new RequestContextException();
    }

    /**
     * get current login user
     *
     * @return UserVO
     */
    public static UserVO getCurrentUser() {
        return (UserVO) RequestContext.getCurrent().getUser();
    }

    /**
     * get user valid roleIds
     *
     * @return String user account
     */
    public static Set<String> getUserValidRoleIds() {
        UserVO user = (UserVO) RequestContext.getCurrent().getUser();
        if (user != null) {
            List<RoleVO> validRoles = user.getValidRoles();
            return validRoles.stream()
                    .map(item -> String.valueOf(item.getRoleId()))
                    .collect(Collectors.toSet());
        }
        throw new RequestContextException();
    }

    /**
     * get user valid roleNames
     *
     * @return String user account
     */
    public static Set<String> getUserValidRoleNames() {
        UserVO user = (UserVO) RequestContext.getCurrent().getUser();
        if (user != null) {
            List<RoleVO> validRoles = user.getValidRoles();
            return validRoles.stream()
                    .map(item -> String.valueOf(item.getRoleName()))
                    .collect(Collectors.toSet());
        }
        throw new RequestContextException();
    }

    /**
     * get user valid roleIds
     *
     * @return String user account
     */
    public static Map<Integer, String> getUserRoleInfo() {
        UserVO user = (UserVO) RequestContext.getCurrent().getUser();
        if (user != null) {
            List<RoleVO> validRoles = user.getValidRoles();
            return validRoles.stream()
                    .collect(Collectors.toMap(RoleVO::getRoleId, RoleVO::getRoleName, (v1, v2) -> v1));
        }
        throw new RequestContextException();
    }

    /**
     * Gets user email.
     *
     * @return the user email
     */
    public static String getUserEmail() {
        IUserPrincipal user = RequestContext.getCurrent().getUser();
        if (user != null) {
            return user.getEmail();
        }
        throw new RequestContextException();
    }
}
