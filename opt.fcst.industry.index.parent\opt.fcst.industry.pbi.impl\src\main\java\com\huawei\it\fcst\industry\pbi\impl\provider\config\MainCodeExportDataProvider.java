/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.provider.config;

import com.huawei.it.fcst.industry.pbi.dao.IctProdMainCodeDimDao;
import com.huawei.it.fcst.export.IExcelExportDataProvider;
import com.huawei.it.fcst.industry.pbi.vo.config.IctProdMainCodeDimVO;
import com.huawei.it.fcst.export.vo.ExcelExportContext;
import com.huawei.it.fcst.export.vo.ExportList;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.ApplicationException;

import javax.inject.Inject;
import javax.inject.Named;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MainCodeExportDataProvider Class
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
@Named("IExcelExport.MainCodeExportDataProvider")
public class MainCodeExportDataProvider implements IExcelExportDataProvider {

    @Inject
    private IctProdMainCodeDimDao prodMainCodeDimDao;

    @Override
    public List<?> getData(Serializable conditionObject, PageVO pageVO) throws ApplicationException {
        IctProdMainCodeDimVO prodMainCodeDimVO = (IctProdMainCodeDimVO)conditionObject;
        PagedResult<IctProdMainCodeDimVO> dataList = prodMainCodeDimDao.findMainCodeDimListByPage(prodMainCodeDimVO, pageVO);
        ExportList exportList = new ExportList();
        exportList.addAll(dataList.getResult());
        exportList.setTotalRows(dataList.getPageVO().getTotalRows());
        return exportList;
    }

    @Override
    public Map<String, Object> getHeader(Serializable conditionObject, Map<String, Object> parameters) throws ApplicationException {
        ExcelExportContext context = (ExcelExportContext) conditionObject;
        IctProdMainCodeDimVO prodMainCodeDimVO = (IctProdMainCodeDimVO) context.getConditionObject();
        // 自定义表头
        Map<String, Object> headMap = new HashMap<>();
        headMap.put("versionName", prodMainCodeDimVO.getVersionName());
        return headMap;
    }
}