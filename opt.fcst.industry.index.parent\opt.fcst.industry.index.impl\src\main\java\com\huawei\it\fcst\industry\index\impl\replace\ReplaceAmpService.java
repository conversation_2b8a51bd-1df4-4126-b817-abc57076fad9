/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.replace;

import com.alibaba.fastjson.JSON;
import com.huawei.it.fcst.industry.index.constant.Constant;
import com.huawei.it.fcst.industry.index.dao.IDmFocVersionDao;
import com.huawei.it.fcst.industry.index.dao.IReplaceAmpDao;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumU;
import com.huawei.it.fcst.industry.index.enums.IndustryConst;
import com.huawei.it.fcst.industry.index.enums.IndustryIndexEnum;
import com.huawei.it.fcst.industry.index.enums.ModuleEnum;
import com.huawei.it.fcst.industry.index.enums.ResultCodeEnum;
import com.huawei.it.fcst.industry.index.impl.annual.StatisticsExcelService;
import com.huawei.it.fcst.industry.index.impl.common.CommonService;
import com.huawei.it.fcst.industry.index.impl.common.ReplaceDropDownService;
import com.huawei.it.fcst.industry.index.service.replace.IReplaceAmpService;
import com.huawei.it.fcst.industry.index.utils.ExcelExportUtil;
import com.huawei.it.fcst.industry.index.utils.FcstIndexUtil;
import com.huawei.it.fcst.industry.index.utils.UserInfoUtils;
import com.huawei.it.fcst.industry.index.vo.annual.DmFocAnnualAmpVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFoiImpExpRecordVO;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;
import com.huawei.it.fcst.industry.index.vo.replace.ReplaceAnalysisVO;
import com.huawei.it.fcst.industry.index.vo.replace.ResultAnnualVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Named;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/8/29
 */
@Named("replaceAmpService")
@JalorResource(code = "replaceAmpService", desc = "替代分析页面")
public class ReplaceAmpService implements IReplaceAmpService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReplaceAmpService.class);

    @Autowired
    private IDmFocVersionDao dmFocVersionDao;

    @Autowired
    private IReplaceAmpDao replaceAmpDao;

    @Autowired
    private ReplaceDropDownService replaceDropDownService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private CommonAmpService commonAmpService;

    @Autowired
    private StatisticsExcelService statisticsExcelService;

    @Autowired
    private AsyncReplaceExportService asyncReplaceExportService;

    @Override
    @JalorOperation(code = "costAmpChart", desc = "当前层级涨跌幅")
    public ResultDataVO costAmpChart(ReplaceAnalysisVO replaceAnalysisVO) {
        // 获取数据权限
        commonService.setUserPermission(replaceAnalysisVO);
        if (null == replaceAnalysisVO.getPeriodYear()) {
            // 计算最近的三年
            List<String> threeYears = replaceDropDownService.getThreeYears();
            replaceAnalysisVO.setYearList(threeYears);
        }
        // 获取最新的version_id
        replaceAnalysisVO.setVersionId(dmFocVersionDao.findAnnualVersion(IndustryConst.TABLE_NAME.ICT_TABLE.getValue()).getVersionId());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = replaceAmpDao.allReplaceAmpCost(replaceAnalysisVO);
        // 设置无效的涨跌幅提示语
        commonAmpService.setNoEffectiveAmp(dmFocAnnualAmpVOList, "data");
        List<DmFocAnnualAmpVO> annualAmpAndWeightList = new ArrayList<>();
        PageVO pageVO = new PageVO();
        if (replaceAnalysisVO.getIsMultipleSelect()) {
            // 分页处理
            pageVO.setPageSize(replaceAnalysisVO.getPageSize());
            pageVO.setCurPage(replaceAnalysisVO.getPageIndex());
            int count = dmFocAnnualAmpVOList.size();
            pageVO.setTotalRows(count);
            int curPage = pageVO.getCurPage() - 1;
            int pageSize = pageVO.getPageSize();

            if (CollectionUtils.isNotEmpty(dmFocAnnualAmpVOList) && count > 0) {
                int fromIndex = curPage * pageSize;
                int totalIndex = (curPage + 1) * pageSize;
                if (totalIndex > count) {
                    totalIndex = count;
                }
                annualAmpAndWeightList = dmFocAnnualAmpVOList.subList(fromIndex, totalIndex);
            }
        } else {
            annualAmpAndWeightList = dmFocAnnualAmpVOList;
        }
        ResultAnnualVO resultAnnualVO = new ResultAnnualVO();
        resultAnnualVO.setResult(annualAmpAndWeightList);
        resultAnnualVO.setPageVo(pageVO);
        return ResultDataVO.success(resultAnnualVO);
    }

    @Override
    @JalorOperation(code = "industryCostList", desc = "成本涨跌一览表")
    public ResultDataVO industryCostList(ReplaceAnalysisVO replaceAnalysisVO) {
        // 获取数据权限
        commonService.setUserPermission(replaceAnalysisVO);
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(replaceAnalysisVO.getPageSize());
        pageVO.setCurPage(replaceAnalysisVO.getPageIndex());
        setIndustrySearchParamVO(replaceAnalysisVO);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpOrderByWeight = replaceAmpDao.industryReplaceCostList(replaceAnalysisVO);
        // item层级按照权重*涨跌大小排序；各层级权重排序;获取权重*涨跌最大值
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = commonAmpService.dealGroupLevelWeightAndAmp(dmFocAnnualAmpOrderByWeight, replaceAnalysisVO, true);
        // 设置无效的涨跌幅提示语
        commonAmpService.setNoEffectiveAmp(dmFocAnnualAmpVOResult, "data");
        // 排序和分页
        List<DmFocAnnualAmpVO> annualAmpAndWeightList = orderColumnAndLimitPage(dmFocAnnualAmpVOResult, replaceAnalysisVO, pageVO);
        ResultAnnualVO resultAnnualVO = new ResultAnnualVO();
        resultAnnualVO.setResult(annualAmpAndWeightList);
        resultAnnualVO.setPageVo(pageVO);
        resultAnnualVO.setMaxValue(replaceAnalysisVO.getMaxValue() + "%");
        return ResultDataVO.success(resultAnnualVO);
    }

    @Override
    @Audit(module = "replaceAmpService-exportDetail", operation = "exportDetail", message = "替代成本分析数据下载")
    @JalorOperation(code = "exportDetail", desc = "替代成本分析数据下载")
    public ResultDataVO exportDetail(ReplaceAnalysisVO replaceAnalysisVO, HttpServletResponse response) throws Exception {
        Timestamp creationDate = new Timestamp(System.currentTimeMillis());
        commonService.setUserPermission(replaceAnalysisVO);
        replaceAnalysisVO.setParentLevel(replaceAnalysisVO.getGroupLevel());
        long start = System.currentTimeMillis();
        LOGGER.info("Begin ReplaceAmpService::exportDetail:{}", JSON.toJSONString(replaceAnalysisVO));
        // 获取导出模板
        Boolean viewFlag = IndustryIndexEnum.VIEW_FLAG_U.VIEW1.getValue().equals(replaceAnalysisVO.getViewFlag()) ||
                (IndustryIndexEnum.VIEW_FLAG_U.VIEW2.getValue().equals(replaceAnalysisVO.getViewFlag()) && GroupLevelEnumU.LV1.getValue().equals(replaceAnalysisVO.getGroupLevel())) ||
                (IndustryIndexEnum.VIEW_FLAG_U.VIEW3.getValue().equals(replaceAnalysisVO.getViewFlag()) && GroupLevelEnumU.LV2.getValue().equals(replaceAnalysisVO.getGroupLevel()));
        String exportTemplate = getExportTempalte(replaceAnalysisVO, viewFlag);
        // 导出模块为空表示无权限下载，直接返回
        if (StringUtils.isEmpty(exportTemplate)) {
            return ResultDataVO.failure(ResultCodeEnum.NOT_PERMISSION.getCode(),
                    Constant.StrEnum.NO_PERMISSION_TO_DOWNLOAD.getValue());
        }
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate(exportTemplate);
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        BeanUtils.copyProperties(replaceAnalysisVO, monthAnalysisVO);
        monthAnalysisVO.setGranularityType(IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue());
        monthAnalysisVO.setIndustryOrg(IndustryConst.INDUSTRY_ORG.ICT.getValue());
        String  groupCnName = commonService.getReplaceGroupCnName(monthAnalysisVO);
        replaceAnalysisVO.setVersionId(dmFocVersionDao.findAnnualVersion(IndustryConst.TABLE_NAME.ICT_TABLE.getValue()).getVersionId());
        // 导出模板对应的数据
        int totalRows = 0;
        if (viewFlag) {
            totalRows = exporAmpTemplate(replaceAnalysisVO, workbook, groupCnName);
        } else{
            totalRows = exportTemplate(replaceAnalysisVO, workbook, groupCnName);
        }
        String fileName = replaceAnalysisVO.getFileName().concat(".xlsx");
        // Excel文件下载到浏览器
        ExcelExportUtil.downloadExcel(workbook, fileName, response);
        // 插入导出记录信息，并上传导出文件到S3服务器
        insertExportRecord(totalRows, workbook, replaceAnalysisVO, UserInfoUtils.getUserId(),creationDate);
        long end = System.currentTimeMillis();
        LOGGER.info("End ReplaceAmpService::exportDetail", (end - start));
        return ResultDataVO.success();
    }

    private String getExportTempalte(ReplaceAnalysisVO replaceAnalysisVO, Boolean viewFlag) {
        String exportTemplate;
        if (replaceAnalysisVO.getIsMultipleSelect()) {
            if (viewFlag) {
                exportTemplate = Constant.StrEnum.REPLACE_AMP_TEMPLATE4_PATH.getValue();
            } else {
                if (GroupLevelEnumU.LV3.getValue().equals(replaceAnalysisVO.getGroupLevel())  || Constant.StrEnum.BIND.getValue().equals(replaceAnalysisVO.getGroupLevel())) {
                    exportTemplate = Constant.StrEnum.REPLACE_AMP_TEMPLATE6_PATH.getValue();
                } else {
                    exportTemplate = Constant.StrEnum.REPLACE_AMP_TEMPLATE3_PATH.getValue();
                }
            }
        } else {
            if (viewFlag) {
                exportTemplate = Constant.StrEnum.REPLACE_AMP_TEMPLATE2_PATH.getValue();
            } else {
                if (GroupLevelEnumU.LV3.getValue().equals(replaceAnalysisVO.getGroupLevel())  || Constant.StrEnum.BIND.getValue().equals(replaceAnalysisVO.getGroupLevel())) {
                    exportTemplate = Constant.StrEnum.REPLACE_AMP_TEMPLATE5_PATH.getValue();
                } else {
                    exportTemplate = Constant.StrEnum.REPLACE_AMP_TEMPLATE1_PATH.getValue();
                }

            }
        }
        return exportTemplate;
    }

    private int exportTemplate(ReplaceAnalysisVO replaceAnalysisVO, XSSFWorkbook workbook, String groupCnName) throws Exception {
        // 导出模板1，对应的sheet页为：制造成本涨跌图 制造成本涨跌图多子项图  制造成本一览表
        IRequestContext current = RequestContextManager.getCurrent();
        // 填充制造成本涨跌图Sheet页数据
        Future<Integer> ampTotal;
        if (replaceAnalysisVO.getIsMultipleSelect()) {
            ampTotal = asyncReplaceExportService.fillMultiAmpSheet(workbook, 0, groupCnName, replaceAnalysisVO, current);
        } else {
            ampTotal = asyncReplaceExportService.fillAmpSheet(workbook, 0, groupCnName, replaceAnalysisVO, current);
        }
        Future<Integer> allListTotal;
        // 填充制造成本一览表的Sheet页数据
        if (GroupLevelEnumU.LV3.getValue().equals(replaceAnalysisVO.getGroupLevel())  || Constant.StrEnum.BIND.getValue().equals(replaceAnalysisVO.getGroupLevel())) {
            allListTotal =  asyncReplaceExportService.fillAllBindAmpSheet(workbook, 1, groupCnName, replaceAnalysisVO, current);
        } else {
            allListTotal =  asyncReplaceExportService.fillAllAmpSheet(workbook, 1, groupCnName, replaceAnalysisVO, current);
        }

        while (true) {
            boolean allDoneFlag = ampTotal.isDone() && allListTotal.isDone();
            if (allDoneFlag) {
                break;
            }
        }
        return ampTotal.get() + allListTotal.get();
    }

    private int exporAmpTemplate(ReplaceAnalysisVO replaceAnalysisVO, XSSFWorkbook workbook, String groupCnName) throws Exception {
        // 导出模板2，对应的sheet页为：制造成本涨跌图 制造成本涨跌图多子项图  制造成本一览表
        IRequestContext current = RequestContextManager.getCurrent();
        // 填充制造成本涨跌图Sheet页数据
        Future<Integer> ampTotal;
        if (replaceAnalysisVO.getIsMultipleSelect()) {
            ampTotal = asyncReplaceExportService.fillMultiAmpSheet(workbook, 0, groupCnName, replaceAnalysisVO, current);
        } else {
            ampTotal = asyncReplaceExportService.fillAmpSheet(workbook, 0, groupCnName, replaceAnalysisVO, current);
        }
        while (true) {
            boolean allDoneFlag = ampTotal.isDone();
            if (allDoneFlag) {
                break;
            }
        }
        return ampTotal.get();
    }

    private void insertExportRecord(int totalRows, Workbook workbook, ReplaceAnalysisVO replaceAnalysisVO, Long userId,Timestamp creationDate) throws IOException, CommonApplicationException {
        // 插入数据，上传文件
        String fileName = replaceAnalysisVO.getFileName();
        DmFoiImpExpRecordVO dmFoiImpExpRecordVO = StatisticsExcelService.uploadExportExcel(workbook, totalRows, fileName, userId);
        dmFoiImpExpRecordVO.setModuleType(IndustryConst.INDUSTRY_ORG_PAGE.getValue( IndustryConst.INDUSTRY_ORG.ICT.getValue())
                + ModuleEnum.REPLACE.getCnName());
        dmFoiImpExpRecordVO.setUserId(String.valueOf(userId));
        // 设置创建时间和结束时间
        dmFoiImpExpRecordVO.setCreationDate(creationDate);
        dmFoiImpExpRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        dmFoiImpExpRecordVO.setEndDate(new Timestamp(System.currentTimeMillis()));
        // 插入数据
        statisticsExcelService.insertMonthExportRecord(dmFoiImpExpRecordVO);
    }

    private List<DmFocAnnualAmpVO> orderColumnAndLimitPage(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult, ReplaceAnalysisVO replaceAnalysisVO, PageVO pageVO) {
        String orderColumn = replaceAnalysisVO.getOrderColumn();
        if (StringUtils.isNotBlank(orderColumn)) {
            switch (orderColumn) {
                case "weightRate":
                    dmFocAnnualAmpVOPagedResult = sortByWeightRateBar(dmFocAnnualAmpVOPagedResult, replaceAnalysisVO);
                    break;
                case "groupCnName":
                    groupCnNameOrder(dmFocAnnualAmpVOPagedResult, replaceAnalysisVO);
                    break;
                case "groupCode":
                    groupCodeOrder(dmFocAnnualAmpVOPagedResult, replaceAnalysisVO);
                    break;
                case "annualAmp":
                    dmFocAnnualAmpVOPagedResult = getAnnualAmpPagedResult(dmFocAnnualAmpVOPagedResult, replaceAnalysisVO);
                    break;
                case "weightAnnualAmpPercent":
                case "weightAnnualAmpPercentOrder":
                    if ("asc".equals(replaceAnalysisVO.getOrderMethod())) {
                        dmFocAnnualAmpVOPagedResult = sortByReplaceWeightAnnualBar(dmFocAnnualAmpVOPagedResult, true);
                    } else {
                        dmFocAnnualAmpVOPagedResult = sortByReplaceWeightAnnualBar(dmFocAnnualAmpVOPagedResult, false);
                    }
                    break;
                default:
                    break;
            }
        } else {
            // 根据状态码区分-和有数据的记录后，默认权重或涨跌大小排序
            if ("BIND".equals(replaceAnalysisVO.getGroupLevel())) {
                dmFocAnnualAmpVOPagedResult = getAnnualAmpPagedResult(dmFocAnnualAmpVOPagedResult, replaceAnalysisVO);
            } else {
                dmFocAnnualAmpVOPagedResult = sortWeightBarAndRecordList(dmFocAnnualAmpVOPagedResult);
            }
        }
        // 分页
        int count = dmFocAnnualAmpVOPagedResult.size();
        dmFocAnnualAmpVOPagedResult = getAnnualAmpLimitPage(dmFocAnnualAmpVOPagedResult, pageVO);
        pageVO.setTotalRows(count);
        return dmFocAnnualAmpVOPagedResult;
    }

    @NotNull
    private List<DmFocAnnualAmpVO> getAnnualAmpLimitPage(List<DmFocAnnualAmpVO> dmFocAnnualPagedResult, PageVO pageVO) {
        int count = dmFocAnnualPagedResult.size();
        int curPage = pageVO.getCurPage() - 1;
        int pageSize = pageVO.getPageSize();
        List<DmFocAnnualAmpVO> annualAmpList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dmFocAnnualPagedResult) && count > 0) {
            int fromIndex = curPage * pageSize;
            int totalIndex = (curPage + 1) * pageSize;
            if (totalIndex > count) {
                totalIndex = count;
            }
            annualAmpList = dmFocAnnualPagedResult.subList(fromIndex, totalIndex);
        }
        return annualAmpList;
    }

    private List<DmFocAnnualAmpVO> sortWeightBarAndRecordList(List<DmFocAnnualAmpVO> dmFocAnnualAmpPagedResult) {
        List<DmFocAnnualAmpVO> barAnnualAmpVOList = dmFocAnnualAmpPagedResult.stream().filter(result -> "0*".equals(result.getWeightRate())).collect(Collectors.toList());
        List<DmFocAnnualAmpVO> otherList = dmFocAnnualAmpPagedResult.stream().filter(vo -> !barAnnualAmpVOList.contains(vo)).collect(Collectors.toList());
        commonAmpService.sortByWeight(otherList);
        List<DmFocAnnualAmpVO> newDmFocAnnualAmpPagedResult = new ArrayList<>();
        newDmFocAnnualAmpPagedResult.addAll(otherList);
        newDmFocAnnualAmpPagedResult.addAll(barAnnualAmpVOList);
        return newDmFocAnnualAmpPagedResult;
    }

    private List<DmFocAnnualAmpVO> sortByReplaceWeightAnnualBar(List<DmFocAnnualAmpVO> dmFocAnnualAmpPagedResult, boolean flag) {
        List<DmFocAnnualAmpVO> otherWeightList = dmFocAnnualAmpPagedResult;
        List<DmFocAnnualAmpVO> newBarWeightVOList = new ArrayList<>();
        List<DmFocAnnualAmpVO> barAnnualAmpVOList = dmFocAnnualAmpPagedResult.stream().filter(result -> "0*".equals(result.getWeightAnnualAmpPercent())).collect(Collectors.toList());
        otherWeightList = dmFocAnnualAmpPagedResult.stream().filter(vo -> !barAnnualAmpVOList.contains(vo)).collect(Collectors.toList());
        newBarWeightVOList.addAll(barAnnualAmpVOList);
        orderWeightAnnualAmpPercent(otherWeightList, flag);
        List<DmFocAnnualAmpVO> newAnnualAmpVOPagedResult = new ArrayList<>();
        newAnnualAmpVOPagedResult.addAll(otherWeightList);
        newAnnualAmpVOPagedResult.addAll(newBarWeightVOList);
        return newAnnualAmpVOPagedResult;
    }

    private void orderWeightAnnualAmpPercent(List<DmFocAnnualAmpVO> dmFocAnnualAmpPagedResult, boolean flag) {
        // 权重*涨跌 排序
        Collections.sort(dmFocAnnualAmpPagedResult, new Comparator<DmFocAnnualAmpVO>() {
            @Override
            public int compare(DmFocAnnualAmpVO dmFocAnnualAmp1, DmFocAnnualAmpVO dmFocAnnualAmp2) {
                String weightAmpPercent1 = dmFocAnnualAmp1.getWeightAnnualAmpPercent();
                String weightAmpPercent2 = dmFocAnnualAmp2.getWeightAnnualAmpPercent();
                if (flag) {
                    return commonAmpService.compareWeightColumn(weightAmpPercent1, weightAmpPercent2);
                }
                return commonAmpService.compareWeightColumn(weightAmpPercent2, weightAmpPercent1);
            }
        });
    }

    @NotNull
    private List<DmFocAnnualAmpVO> getAnnualAmpPagedResult(List<DmFocAnnualAmpVO> dmFocAnnualAmpPagedResult, ReplaceAnalysisVO replaceAnalysisVO) {
        List<DmFocAnnualAmpVO> barAnnualAmpList = dmFocAnnualAmpPagedResult.stream().filter(result -> "0*".equals(result.getAnnualAmp())).collect(Collectors.toList());
        List<DmFocAnnualAmpVO> otherAmpList = dmFocAnnualAmpPagedResult.stream().filter(vo -> !barAnnualAmpList.contains(vo)).collect(Collectors.toList());
        annualAmpOrder(otherAmpList, replaceAnalysisVO);
        List<DmFocAnnualAmpVO> newAmpPagedResult = new ArrayList<>();
        newAmpPagedResult.addAll(otherAmpList);
        newAmpPagedResult.addAll(barAnnualAmpList);
        return newAmpPagedResult;
    }

    private void annualAmpOrder(List<DmFocAnnualAmpVO> dmFocAnnualPagedResult, ReplaceAnalysisVO replaceAnalysisVO) {
        if ("asc".equals(replaceAnalysisVO.getOrderMethod())) {
            Collections.sort(dmFocAnnualPagedResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnual1, DmFocAnnualAmpVO dmFocAnnual2) {
                    String annualAmp1 = dmFocAnnual1.getAnnualAmp();
                    String annualAmp2 = dmFocAnnual2.getAnnualAmp();
                    return commonAmpService.compareWeightColumn(annualAmp1, annualAmp2);
                }
            });
        } else {
            Collections.sort(dmFocAnnualPagedResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnual1, DmFocAnnualAmpVO dmFocAnnual2) {
                    String annualAmp1 = dmFocAnnual1.getAnnualAmp();
                    String annualAmp2 = dmFocAnnual2.getAnnualAmp();
                    return commonAmpService.compareWeightColumn(annualAmp2, annualAmp1);
                }
            });
        }
    }

    private void groupCnNameOrder(List<DmFocAnnualAmpVO> dmFocAnnualPagedResult, ReplaceAnalysisVO replaceAnalysisVO) {
        if ("asc".equals(replaceAnalysisVO.getOrderMethod())) {
            Collections.sort(dmFocAnnualPagedResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnual1, DmFocAnnualAmpVO dmFocAnnual2) {
                    return commonAmpService.sortByGroupCnName(dmFocAnnual1, dmFocAnnual2);
                }
            });
        } else {
            Collections.sort(dmFocAnnualPagedResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnual1, DmFocAnnualAmpVO dmFocAnnual2) {
                    return commonAmpService.sortByGroupCnName(dmFocAnnual2, dmFocAnnual1);
                }
            });
        }
    }

    private void groupCodeOrder(List<DmFocAnnualAmpVO> dmFocAnnualPagResult, ReplaceAnalysisVO replaceAnalysisVO) {
        if ("asc".equals(replaceAnalysisVO.getOrderMethod())) {
            Collections.sort(dmFocAnnualPagResult, Comparator.comparing(DmFocAnnualAmpVO::getGroupCode));
        } else {
            Collections.sort(dmFocAnnualPagResult, Comparator.comparing(DmFocAnnualAmpVO::getGroupCode).reversed());
        }
    }

    private List<DmFocAnnualAmpVO> sortByWeightRateBar(List<DmFocAnnualAmpVO> dmFocAnnualAmpPagedResult, ReplaceAnalysisVO replaceAnalysisVO) {
        List<DmFocAnnualAmpVO> otherList = dmFocAnnualAmpPagedResult;
        List<DmFocAnnualAmpVO> newBarAnnualAmpVOList = new ArrayList<>();
        List<DmFocAnnualAmpVO> barAnnualAmpVOList = dmFocAnnualAmpPagedResult.stream().filter(result -> "0*".equals(result.getWeightRate())).collect(Collectors.toList());
        otherList = dmFocAnnualAmpPagedResult.stream().filter(vo -> !barAnnualAmpVOList.contains(vo)).collect(Collectors.toList());
        newBarAnnualAmpVOList.addAll(barAnnualAmpVOList);
        weightRateOrder(otherList, replaceAnalysisVO);
        List<DmFocAnnualAmpVO> newAnnualAmpVOPagedResult = new ArrayList<>();
        newAnnualAmpVOPagedResult.addAll(otherList);
        newAnnualAmpVOPagedResult.addAll(newBarAnnualAmpVOList);
        return newAnnualAmpVOPagedResult;
    }


    private void weightRateOrder(List<DmFocAnnualAmpVO> dmFocAnnualAmpPagedResult, ReplaceAnalysisVO replaceAnalysisVO) {
        if ("asc".equals(replaceAnalysisVO.getOrderMethod())) {
            Collections.sort(dmFocAnnualAmpPagedResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnual1, DmFocAnnualAmpVO dmFocAnnual2) {
                    String weightRate1 = dmFocAnnual1.getWeightRate();
                    String weightRate2 = dmFocAnnual2.getWeightRate();
                    return commonAmpService.compareWeightColumn(weightRate1, weightRate2);
                }
            });
        } else {
            Collections.sort(dmFocAnnualAmpPagedResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnual1, DmFocAnnualAmpVO dmFocAnnual2) {
                    String weightRate1 = dmFocAnnual1.getWeightRate();
                    String weightRate2 = dmFocAnnual2.getWeightRate();
                    return commonAmpService.compareWeightColumn(weightRate2, weightRate1);
                }
            });
        }
    }

    private void setIndustrySearchParamVO(ReplaceAnalysisVO replaceAnalysisVO) {
        replaceAnalysisVO.setParentLevel(replaceAnalysisVO.getGroupLevel());
        String nextGroupLevel;
        if ("BIND".equals(replaceAnalysisVO.getParentLevel())) {
            nextGroupLevel = "BIND";
        } else {
            if ("LV3".equals(replaceAnalysisVO.getParentLevel())) {
                nextGroupLevel = "BIND";
            } else {
                nextGroupLevel = FcstIndexUtil.getNextGroupLevelByView(replaceAnalysisVO.getViewFlag(), replaceAnalysisVO.getParentLevel(), "U", "ICT");
            }
            replaceAnalysisVO.setParentCodeList(replaceAnalysisVO.getGroupCodeList());
            replaceAnalysisVO.setGroupCodeList(null);
        }
        replaceAnalysisVO.setGroupLevel(nextGroupLevel);
        if (!replaceAnalysisVO.getIsMultipleSelect()) {
            // 计算最近的三年
            List<String> threeYears = replaceDropDownService.getThreeYears();
            replaceAnalysisVO.setYearList(threeYears);
        }
        replaceAnalysisVO.setVersionId(dmFocVersionDao.findAnnualVersion(IndustryConst.TABLE_NAME.ICT_TABLE.getValue()).getVersionId());
    }

}
