<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.price.dao.IPriceMonthCostIdxDao">
    <resultMap type="com.huawei.it.fcst.industry.price.vo.month.PriceMonthAnalysisVO" id="resultMap">
        <result property="basePeriodId" column="base_period_id"/>
        <result property="versionId" column="version_id"/>
        <result property="customId" column="custom_id"/>
        <result property="customCnName" column="custom_cn_name"/>
        <result property="periodYear" column="period_year"/>
        <result property="periodId" column="period_id"/>
        <result property="basePeriodId" column="base_period_id"/>
        <result property="groupCode" column="group_code"/>
        <result property="groupCnName" column="group_cn_name"/>
        <result property="groupLevel" column="group_level"/>
        <result property="costIndex" column="cost_index"/>
        <result property="weightRate" column="weight_rate"/>
        <result property="weightPercent" column="weight_percent"/>
        <result property="parentCode" column="parent_code"/>
        <result property="parentCnName" column="parent_cn_name"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="regionCode" column="region_code"/>
        <result property="regionCnName" column="region_cn_name"/>
        <result property="repofficeCode" column="repoffice_code"/>
        <result property="repofficeCnName" column="repoffice_cn_name"/>
        <result property="bgCode" column="bg_code"/>
        <result property="bgCnName" column="bg_cn_name"/>
        <result property="overseaFlag" column="oversea_flag"/>
        <result property="signTopCustCategoryCode" column="sign_top_cust_category_code"/>
        <result property="signTopCustCategoryCnName" column="sign_top_cust_category_cn_name"/>
        <result property="signSubsidiaryCustcatgCnName" column="sign_subsidiary_custcatg_cn_name"/>
        <result property="lv4ProdListCode" column="lv4_prod_list_code"/>
        <result property="lv4ProdListCnName" column="lv4_prod_list_cn_name"/>
        <result property="appendFlag" column="append_flag"/>
    </resultMap>

    <sql id="costIdxChartFields">
        DISTINCT
        version_id,
        period_id,
        group_code,
        group_cn_name,
        group_level,
        ROUND(cost_index, 2) AS cost_index
    </sql>

    <sql id="searchWhere">
        <if test='!monthAnalysisVO.isNeedBlur and monthAnalysisVO.basePeriodId != null'>
            AND t1.base_period_id = #{monthAnalysisVO.basePeriodId}
        </if>
        <if test='monthAnalysisVO.periodStartTime != null'>
            AND t1.period_id <![CDATA[ >= ]]> #{monthAnalysisVO.periodStartTime}
        </if>
        <if test='monthAnalysisVO.periodEndTime != null'>
            AND t1.period_id <![CDATA[ <= ]]> #{monthAnalysisVO.periodEndTime}
        </if>
        <if test='monthAnalysisVO.versionId != null'>
            AND t1.version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='monthAnalysisVO.viewFlag != null and monthAnalysisVO.viewFlag != ""'>
            AND t1.view_flag = #{monthAnalysisVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            AND t1.oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
            AND t1.bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
            AND t1.region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
            AND t1.repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.signTopCustCategoryCode != null and monthAnalysisVO.signTopCustCategoryCode != ""'>
            AND sign_top_cust_category_code = #{monthAnalysisVO.signTopCustCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.signSubsidiaryCustcatgCnName != null and monthAnalysisVO.signSubsidiaryCustcatgCnName != ""'>
            AND sign_subsidiary_custcatg_cn_name = #{monthAnalysisVO.signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupLevel != null and monthAnalysisVO.groupLevel != ""'>
            and t1.group_level = #{monthAnalysisVO.groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupCodeList != null and monthAnalysisVO.groupCodeList != ""'>
            <foreach collection='monthAnalysisVO.groupCodeList' item="code" open="AND t1.group_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.groupLevel != "LV0" and monthAnalysisVO.parentCodeList != null and monthAnalysisVO.parentCodeList != ""'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code" open="AND t1.parent_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.groupLevel=="LV0" and monthAnalysisVO.lv1DimensionSet != null and monthAnalysisVO.lv1DimensionSet.size() > 0'>
            <foreach collection='monthAnalysisVO.lv1DimensionSet' item="code" open="and t1.group_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.groupLevel=="LV1" and monthAnalysisVO.lv2DimensionSet != null and monthAnalysisVO.lv2DimensionSet.size() > 0'>
            <foreach collection='monthAnalysisVO.lv2DimensionSet' item="code" open="and t1.group_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
    </sql>

    <sql id="blurSearchWhere">
        <if test='monthAnalysisVO.periodStartTime != null'>
            AND period_id <![CDATA[ >= ]]> #{monthAnalysisVO.periodStartTime}
        </if>
        <if test='monthAnalysisVO.periodEndTime != null'>
            AND period_id <![CDATA[ <= ]]> #{monthAnalysisVO.periodEndTime}
        </if>
        <if test='monthAnalysisVO.versionId != null'>
            AND version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='monthAnalysisVO.customId != null'>
            AND custom_id = #{monthAnalysisVO.customId,jdbcType=NUMERIC}
        </if>
        <if test='monthAnalysisVO.customIdList != null and monthAnalysisVO.customIdList != ""'>
            <foreach collection='monthAnalysisVO.customIdList' item="code" open="AND custom_id IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.viewFlag != null and monthAnalysisVO.viewFlag != ""'>
            AND view_flag = #{monthAnalysisVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            AND oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
            AND bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
            AND region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
            AND repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.signTopCustCategoryCode != null and monthAnalysisVO.signTopCustCategoryCode != ""'>
            AND sign_top_cust_category_code = #{monthAnalysisVO.signTopCustCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.signSubsidiaryCustcatgCnName != null and monthAnalysisVO.signSubsidiaryCustcatgCnName != ""'>
            AND sign_subsidiary_custcatg_cn_name = #{monthAnalysisVO.signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupLevel != null and monthAnalysisVO.groupLevel != ""'>
            and group_level = #{monthAnalysisVO.groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.customGroupCodeList != null and monthAnalysisVO.customGroupCodeList != ""'>
            <foreach collection='monthAnalysisVO.customGroupCodeList' item="code" open="AND group_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.groupLevel != "LV0" and monthAnalysisVO.parentCodeList != null and monthAnalysisVO.parentCodeList != ""'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code" open="AND parent_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
    </sql>

    <select id="findPriceIndexVOList" resultMap="resultMap">
        SELECT DISTINCT
        t1.version_id,
        t1.period_id,
        t1.group_code,
        t1.group_cn_name,
        t1.group_level,
        t1.append_flag,
        <if test='monthAnalysisVO.groupLevel == "SPART"'>
            t1.group_code as spart_code,
            t1.group_cn_name as spart_cn_name,
        </if>
        ROUND(t1.cost_index, 2) AS cost_index
        FROM fin_dm_opt_foi.dm_fcst_price_mon_cost_idx_t t1
        WHERE t1.del_flag = 'N'
        <include refid="searchWhere"/>
        ORDER BY t1.period_id
    </select>

    <sql id="combSerchWhere">
        <if test='monthAnalysisVO.granularityType != null'>
            AND granularity_type = #{monthAnalysisVO.granularityType}
        </if>
        <if test='monthAnalysisVO.combIdList != null and monthAnalysisVO.combIdList.size() > 0'>
            <foreach collection='monthAnalysisVO.combIdList' item="code" open="AND custom_id IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.basePeriodId != null'>
            AND base_period_id = #{monthAnalysisVO.basePeriodId}
        </if>
        <if test='monthAnalysisVO.versionId != null'>
            AND version_id = #{monthAnalysisVO.versionId}
        </if>
        <if test='monthAnalysisVO.periodstartTime != null and monthAnalysisVO.periodstartTime != ""'>
            AND period_id <![CDATA[ >= ]]> #{monthAnalysisVO.periodStartTime}
        </if>
        <if test='monthAnalysisVO.periodEndTime != null and monthAnalysisVO.periodEndTime != ""'>
            AND period_id <![CDATA[ <= ]]> #{monthAnalysisVO.periodEndTime}
        </if>
        <if test='monthAnalysisVO.softwareMark != null and monthAnalysisVO.softwareMark != ""'>
            AND software_mark = #{monthAnalysisVO.softwareMark,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            AND oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
            AND bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
            AND region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
            AND repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.mainFlag != null and monthAnalysisVO.mainFlag != ""'>
            AND main_flag = #{monthAnalysisVO.mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.codeAttributes != null and monthAnalysisVO.codeAttributes != ""'>
            AND code_attributes = #{monthAnalysisVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupLevel != null and monthAnalysisVO.groupLevel != ""'>
            and group_level = #{monthAnalysisVO.groupLevel,jdbcType=VARCHAR}
        </if>
    </sql>

    <select id="findBlurPriceIndexVOList" resultMap="resultMap">
        SELECT DISTINCT
        version_id,
        custom_id,
        period_id,
        group_code,
        group_cn_name,
        group_level,
        ROUND(cost_index, 2) AS cost_index
        FROM fin_dm_opt_foi.dm_fcst_price_base_cus_mon_cost_idx_t
        WHERE del_flag = 'N'
        <include refid="blurSearchWhere"/>
        ORDER BY period_id
    </select>

    <sql id="multiCostIdxChartFields">
        DISTINCT
        t1.parent_code,
        t1.parent_cn_name,
        t1.period_id,
        t1.group_level,
        t1.group_code,
        t1.group_cn_name,
        ROUND(t1.cost_index, 2) AS cost_index,
        t2.weight_rate,
        ROUND(t2.weight_rate * 100, 2) || '%' AS weight_percent
    </sql>

    <sql id="multiIdxSearchWhere">
        <if test='monthAnalysisVO.basePeriodId != null'>
            AND t1.base_period_id = #{monthAnalysisVO.basePeriodId}
        </if>
        <if test='monthAnalysisVO.periodStartTime != null'>
            AND t1.period_id <![CDATA[ >= ]]> #{monthAnalysisVO.periodStartTime}
        </if>
        <if test='monthAnalysisVO.periodEndTime != null'>
            AND t1.period_id <![CDATA[ <= ]]> #{monthAnalysisVO.periodEndTime}
        </if>
        <if test='monthAnalysisVO.versionId != null'>
            AND t1.version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='monthAnalysisVO.viewFlag != null and monthAnalysisVO.viewFlag != ""'>
            AND t1.view_flag = #{monthAnalysisVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            AND t1.oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
            AND t1.bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
            AND t1.region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
            AND t1.repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.signTopCustCategoryCode != null and monthAnalysisVO.signTopCustCategoryCode != ""'>
            AND t1.sign_top_cust_category_code = #{monthAnalysisVO.signTopCustCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.signSubsidiaryCustcatgCnName != null and monthAnalysisVO.signSubsidiaryCustcatgCnName != ""'>
            AND t1.sign_subsidiary_custcatg_cn_name = #{monthAnalysisVO.signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupLevel != null and monthAnalysisVO.groupLevel != ""'>
            and t1.group_level = #{monthAnalysisVO.groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.parentCodeList != null and monthAnalysisVO.parentCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code" open="AND t1.parent_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.subGroupCodeList != null and monthAnalysisVO.subGroupCodeList != ""'>
            <foreach collection='monthAnalysisVO.subGroupCodeList' item="code" open="AND t1.group_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.groupLevel=="LV0" and monthAnalysisVO.lv1DimensionSet != null and monthAnalysisVO.lv1DimensionSet.size() > 0'>
            <foreach collection='monthAnalysisVO.lv1DimensionSet' item="code" open="and t1.group_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.groupLevel=="LV1" and monthAnalysisVO.lv2DimensionSet != null and monthAnalysisVO.lv2DimensionSet.size() > 0'>
            <foreach collection='monthAnalysisVO.lv2DimensionSet' item="code" open="and t1.group_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
    </sql>

    <sql id="blurMultiIdxSearchWhere">
        <if test='monthAnalysisVO.basePeriodId != null'>
            AND t1.base_period_id = #{monthAnalysisVO.basePeriodId}
        </if>
        <if test='monthAnalysisVO.periodStartTime != null'>
            AND t1.period_id <![CDATA[ >= ]]> #{monthAnalysisVO.periodStartTime}
        </if>
        <if test='monthAnalysisVO.periodEndTime != null'>
            AND t1.period_id <![CDATA[ <= ]]> #{monthAnalysisVO.periodEndTime}
        </if>
        <if test='monthAnalysisVO.versionId != null'>
            AND t1.version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='monthAnalysisVO.customId != null'>
            AND t1.custom_id = #{monthAnalysisVO.customId,jdbcType=NUMERIC}
        </if>
        <if test='monthAnalysisVO.customIdList != null and monthAnalysisVO.customIdList != ""'>
            <foreach collection='monthAnalysisVO.customIdList' item="code" open="AND t1.custom_id IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.viewFlag != null and monthAnalysisVO.viewFlag != ""'>
            AND t1.view_flag = #{monthAnalysisVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            AND t1.oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
            AND t1.bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
            AND t1.region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
            AND t1.repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.signTopCustCategoryCode != null and monthAnalysisVO.signTopCustCategoryCode != ""'>
            AND t1.sign_top_cust_category_code = #{monthAnalysisVO.signTopCustCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.signSubsidiaryCustcatgCnName != null and monthAnalysisVO.signSubsidiaryCustcatgCnName != ""'>
            AND t1.sign_subsidiary_custcatg_cn_name = #{monthAnalysisVO.signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupLevel != null and monthAnalysisVO.groupLevel != ""'>
            AND t1.group_level = #{monthAnalysisVO.groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.parentCodeList != null and monthAnalysisVO.parentCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code" open="AND t1.parent_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.customGroupCodeList != null and monthAnalysisVO.customGroupCodeList != ""'>
            <foreach collection='monthAnalysisVO.customGroupCodeList' item="code" open="AND t1.group_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
    </sql>

    <select id="callFuncRefreshData" resultType="java.lang.String">
        SELECT fin_dm_opt_foi.f_dm_fcst_price_change_period_info (
            #{versionId},
            #{basePeriodId},
            #{customIds},
            #{bgCode},
            #{overseaFlag},
            #{regionCode},
            #{repofficeCode},
            #{signTopCustCategoryCode},
            #{signSubsidiaryCustcatgCnName},
            #{groupLevel},
            #{groupCode},
            #{parentCode},
            #{viewFlag}
        )
    </select>

    <select id="findMultiBoxList" resultMap="resultMap">
        SELECT DISTINCT
        group_code,
        group_cn_name,
        group_level,
        parent_code,
        parent_cn_name,
        weight_rate,
        ROUND(weight_rate * 100, 2) || '%' AS weight_percent
        FROM fin_dm_opt_foi.dm_fcst_price_mon_weight_t
        WHERE del_flag = 'N'
        <if test='monthAnalysisVO.versionId != null'>
            AND version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='monthAnalysisVO.intervalYear != null and monthAnalysisVO.intervalYear != ""'>
            AND period_year = #{monthAnalysisVO.intervalYear,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.viewFlag != null and monthAnalysisVO.viewFlag != ""'>
            AND view_flag = #{monthAnalysisVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            AND oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
            AND bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
            AND region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
            AND repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.signTopCustCategoryCode != null and monthAnalysisVO.signTopCustCategoryCode != ""'>
            AND sign_top_cust_category_code = #{monthAnalysisVO.signTopCustCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.signSubsidiaryCustcatgCnName != null and monthAnalysisVO.signSubsidiaryCustcatgCnName != ""'>
            AND sign_subsidiary_custcatg_cn_name = #{monthAnalysisVO.signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupLevel != null and monthAnalysisVO.groupLevel != ""'>
            and group_level = #{monthAnalysisVO.groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.parentCodeList != null and monthAnalysisVO.parentCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code" open="AND parent_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.subGroupCodeList != null and monthAnalysisVO.subGroupCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.subGroupCodeList' item="code" open="AND group_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        ORDER BY weight_rate DESC
    </select>

    <select id="findBlurMultiBoxList" resultMap="resultMap">
        SELECT DISTINCT
        custom_id,
        group_code,
        group_cn_name,
        group_level,
        parent_code,
        parent_cn_name,
        lv4_prod_list_code,
        lv4_prod_list_cn_name,
        weight_rate,
        ROUND(weight_rate * 100, 2) || '%' AS weight_percent
        FROM fin_dm_opt_foi.dm_fcst_price_base_cus_mon_weight_t
        WHERE del_flag = 'N'
        AND group_level = 'SPART'
        <include refid="blurMultiBoxListWhere"/>
        ORDER BY weight_rate DESC
        LIMIT #{pageVO.pageSize} OFFSET #{pageVO.mysqlStartIndex}
    </select>

    <select id="findBlurMultiBoxListCount" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM fin_dm_opt_foi.dm_fcst_price_base_cus_mon_weight_t
        WHERE del_flag = 'N'
        AND group_level = 'SPART'
        <include refid="blurMultiBoxListWhere"/>
    </select>

    <sql id="blurMultiBoxListWhere">
        <if test='monthAnalysisVO.versionId != null'>
            AND version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='monthAnalysisVO.intervalYear != null and monthAnalysisVO.intervalYear != ""'>
            AND period_year = #{monthAnalysisVO.intervalYear,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.customIdList != null and monthAnalysisVO.customIdList.size() > 0'>
            <foreach collection='monthAnalysisVO.customIdList' item="code" open="AND custom_id IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.viewFlag != null and monthAnalysisVO.viewFlag != ""'>
            AND view_flag = #{monthAnalysisVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            AND oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
            AND bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
            AND region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
            AND repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.signTopCustCategoryCode != null and monthAnalysisVO.signTopCustCategoryCode != ""'>
            AND sign_top_cust_category_code = #{monthAnalysisVO.signTopCustCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.signSubsidiaryCustcatgCnName != null and monthAnalysisVO.signSubsidiaryCustcatgCnName != ""'>
            AND sign_subsidiary_custcatg_cn_name = #{monthAnalysisVO.signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupLevel != null and monthAnalysisVO.groupLevel != ""'>
            and group_level = #{monthAnalysisVO.groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.parentCodeList != null and monthAnalysisVO.parentCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code" open="AND parent_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.customGroupCodeList != null and monthAnalysisVO.customGroupCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.customGroupCodeList' item="code" open="AND group_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.subGroupCodeList != null and monthAnalysisVO.subGroupCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.subGroupCodeList' item="code" open="AND group_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
    </sql>

    <select id="findActualMonth" resultType="java.lang.String">
        SELECT IFNULL(max(period_id), 0)
        FROM fin_dm_opt_foi.dm_fcst_price_mon_cost_idx_t
        WHERE del_flag = 'N'
          AND group_level = 'LV0'
          AND bg_code = 'PDCG901160'
          AND oversea_flag = 'G'
          AND region_code = 'ALL'
          AND version_id = (
            SELECT version_id
            FROM fin_dm_opt_foi.dm_fcst_price_version_info_t
            WHERE del_flag = 'N'
              AND is_running = 'N'
              AND status = 1
              AND data_type = 'MONTH'
              AND version_type IN ('AUTO', 'FINAL')
            ORDER BY creation_date DESC
            LIMIT 1
            )
    </select>

    <select id="findMultiPriceIndexVOList" resultMap="resultMap">
        SELECT
        <include refid="multiCostIdxChartFields"/>
        FROM fin_dm_opt_foi.dm_fcst_price_mon_cost_idx_t t1
        LEFT JOIN fin_dm_opt_foi.dm_fcst_price_mon_weight_t t2
        ON t2.del_flag = 'N'
        AND t1.group_level = t2.group_level
        AND t1.group_code = t2.group_code
        AND t1.parent_code = t2.parent_code
        AND t1.version_id = t2.version_id
        AND t1.view_flag = t2.view_flag
        AND t1.bg_code = t2.bg_code
        AND NVL(t1.oversea_flag, 'SNULL') = NVL(t2.oversea_flag, 'SNULL')
        AND NVL(t1.region_code, 'SNULL') = NVL(t2.region_code, 'SNULL')
        AND NVL(t1.repoffice_code, 'SNULL') = NVL(t2.repoffice_code, 'SNULL')
        AND NVL(t1.sign_top_cust_category_code, 'SNULL') = NVL(t2.sign_top_cust_category_code, 'SNULL')
        AND NVL(t1.sign_subsidiary_custcatg_cn_name, 'SNULL') = NVL(t2.sign_subsidiary_custcatg_cn_name, 'SNULL')
        <if test='monthAnalysisVO.intervalYear != null and monthAnalysisVO.intervalYear != ""'>
            AND t2.period_year = #{monthAnalysisVO.intervalYear,jdbcType=VARCHAR}
        </if>
        WHERE t1.del_flag = 'N'
        <include refid="multiIdxSearchWhere"/>
        ORDER BY t2.weight_rate,t1.group_code, t1.period_id
    </select>

    <select id="findBlurMultiPriceIndexVOList" resultMap="resultMap">
        SELECT DISTINCT
        t1.parent_code,
        t1.parent_cn_name,
        t1.period_id,
        t1.group_level,
        t1.group_code,
        t1.group_cn_name,
        ROUND(t1.cost_index, 2) AS cost_index,
        t2.weight_rate,
        ROUND(t2.weight_rate * 100, 2) || '%' AS weight_percent
        FROM fin_dm_opt_foi.dm_fcst_price_mon_cost_idx_t t1
        LEFT JOIN fin_dm_opt_foi.dm_fcst_price_base_cus_mon_weight_t t2
        ON t2.del_flag = 'N'
        AND t1.group_level = t2.group_level
        AND t1.group_code = t2.group_code
        AND t1.parent_code = t2.lv4_prod_list_code
        AND t1.version_id = t2.version_id
        AND t1.view_flag = t2.view_flag
        AND t1.bg_code = t2.bg_code
        AND NVL(t1.oversea_flag, 'SNULL') = NVL(t2.oversea_flag, 'SNULL')
        AND NVL(t1.region_code, 'SNULL') = NVL(t2.region_code, 'SNULL')
        AND NVL(t1.repoffice_code, 'SNULL') = NVL(t2.repoffice_code, 'SNULL')
        AND NVL(t1.sign_top_cust_category_code, 'SNULL') = NVL(t2.sign_top_cust_category_code, 'SNULL')
        AND NVL(t1.sign_subsidiary_custcatg_cn_name, 'SNULL') = NVL(t2.sign_subsidiary_custcatg_cn_name, 'SNULL')
        <if test='monthAnalysisVO.intervalYear != null and monthAnalysisVO.intervalYear != ""'>
            AND t2.period_year = #{monthAnalysisVO.intervalYear,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.customId != null'>
            AND t2.custom_id = #{monthAnalysisVO.customId,jdbcType=NUMERIC}
        </if>
        <if test='monthAnalysisVO.customIdList != null and monthAnalysisVO.customIdList != ""'>
            <foreach collection='monthAnalysisVO.customIdList' item="code" open="AND t2.custom_id IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        WHERE t1.del_flag = 'N'
        <if test='monthAnalysisVO.basePeriodId != null'>
            AND t1.base_period_id = #{monthAnalysisVO.basePeriodId}
        </if>
        <if test='monthAnalysisVO.periodStartTime != null'>
            AND t1.period_id <![CDATA[ >= ]]> #{monthAnalysisVO.periodStartTime}
        </if>
        <if test='monthAnalysisVO.periodEndTime != null'>
            AND t1.period_id <![CDATA[ <= ]]> #{monthAnalysisVO.periodEndTime}
        </if>
        <if test='monthAnalysisVO.versionId != null'>
            AND t1.version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='monthAnalysisVO.viewFlag != null and monthAnalysisVO.viewFlag != ""'>
            AND t1.view_flag = #{monthAnalysisVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            AND t1.oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
            AND t1.bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
            AND t1.region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
            AND t1.repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.signTopCustCategoryCode != null and monthAnalysisVO.signTopCustCategoryCode != ""'>
            AND t1.sign_top_cust_category_code = #{monthAnalysisVO.signTopCustCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.signSubsidiaryCustcatgCnName != null and monthAnalysisVO.signSubsidiaryCustcatgCnName != ""'>
            AND t1.sign_subsidiary_custcatg_cn_name = #{monthAnalysisVO.signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupLevel != null and monthAnalysisVO.groupLevel != ""'>
            AND t1.group_level = #{monthAnalysisVO.groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.parentCodeList != null and monthAnalysisVO.parentCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code" open="AND t1.parent_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.groupCodeList != null and monthAnalysisVO.groupCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.groupCodeList' item="code" open="AND t1.group_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.customGroupCodeList != null and monthAnalysisVO.customGroupCodeList != ""'>
            <foreach collection='monthAnalysisVO.customGroupCodeList' item="code" open="AND t1.group_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        ORDER BY t2.weight_rate, t1.group_code, t1.period_id
    </select>

    <select id="findBasePeriodId" resultType="java.lang.String">
        SELECT MAX(period_year)-1 || '12'
        <choose>
            <when test='isNeedBlur == true'>
                FROM fin_dm_opt_foi.dm_fcst_price_base_cus_mon_cost_idx_t
            </when>
            <otherwise>
                FROM fin_dm_opt_foi.dm_fcst_price_mon_cost_idx_t
            </otherwise>
        </choose>
        WHERE del_flag = 'N'
        AND version_id = #{versionId,jdbcType=NUMERIC}
    </select>

    <select id="findCostIndexCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM fin_dm_opt_foi.dm_fcst_price_mon_cost_idx_t t1
        WHERE del_flag = 'N'
        <include refid="searchWhere"/>
    </select>

    <select id="findBlurIndexCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM fin_dm_opt_foi.dm_fcst_price_base_cus_mon_cost_idx_t
        WHERE del_flag = 'N'
        <include refid="blurSearchWhere"/>
    </select>

    <select id="findMultiCostIndexCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM fin_dm_opt_foi.dm_fcst_price_mon_cost_idx_t t1
        WHERE t1.del_flag = 'N'
        <include refid="multiIdxSearchWhere"/>
    </select>

    <select id="findBlurMultiIndexCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM fin_dm_opt_foi.dm_fcst_price_base_cus_mon_cost_idx_t t1
        WHERE t1.del_flag = 'N'
        <include refid="blurMultiIdxSearchWhere"/>
    </select>

</mapper>
