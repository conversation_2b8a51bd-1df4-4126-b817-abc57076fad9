#codeCheck
---
version: 2.0
params:
#pom文件在这个路径下,空为当前目录
  - name: buildPath
    value: 
# 编译目录    
  - name: buildCmd  
    value: "mvn clean package -Dmaven.test.skip=true -gs opt.fcst.industry.index.parent/settings.xml -s opt.fcst.industry.index.parent/settings.xml -f opt.fcst.industry.index.parent/pom.xml"
tool_params:
  secsolar:
    compile_root_dir: ${buildPath}
    compile_script: ${buildCmd}
    
steps:
  #在代码仓下载后，代码检查启动前
  pre_codecheck:
    - checkout ### 检出当前源码库 单仓
    - sh:
         command: |
           cd $WORKSPACE/code/${buildPath}   ###在代码仓下载后，代码检查启动前
           ${buildCmd}
         effect_tool: spotbugs         
#codeCBS
#代码扫描自研引擎，支持自定义domain参数	
#可不配置，默认检查huawei.com
#codeCBS:
#  domain: huawei.com   ### 如果要检查多个域名，可以用英文分号；隔开
codeCBS:
  domain: huawei.com
  dbType: PostgreSQL