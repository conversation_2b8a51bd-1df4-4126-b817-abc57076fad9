/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.combination.CombinationVO;
import com.huawei.it.fcst.industry.index.vo.combination.DmCustomCombVO;
import com.huawei.it.fcst.industry.index.vo.common.CommonViewVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;

import java.util.List;

/**
 * IDmFocEnergyCustomCombDao Class
 *
 * <AUTHOR>
 * @since 2024/4/26
 */
public interface IDmFocEnergyCustomCombDao {

    List<DmFocViewInfoVO> prodEnergyTeamCodeForDimensionMonth(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> prodEnergyTeamCodeForDimensionAnnual(CommonViewVO commonViewVO);

    List<DmCustomCombVO> groupCodeForEnergyDimensionAnnualList(CombinationVO combinationVO);

    List<DmCustomCombVO> groupCodeForEnergyDimensionMonthList(CombinationVO combinationVO);
}
