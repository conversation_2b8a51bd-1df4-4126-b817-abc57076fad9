/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.config;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;
import com.huawei.it.fcst.industry.index.vo.relation.DmDimCatgModlCegIctVO;

import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * BackDimensionDTOTest Class
 *
 * <AUTHOR>
 * @since 2023/4/23
 */
public class BackDimensionDTOTest extends BaseVOCoverUtilsTest<BackDimensionDTO> {

    @Override
    protected Class<BackDimensionDTO> getTClass() {
        return BackDimensionDTO.class;
    }

    @Test
    public void testMethod() {
        BackDimensionDTO dimensionParamVO = new BackDimensionDTO();
        DmFocVersionInfoDTO dmFocVersionInfoDTO=new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersion("20230501");
        dimensionParamVO.setDmFocVersionInfoDTO(dmFocVersionInfoDTO);
        dimensionParamVO.getDmFocVersionInfoDTO();

        List<DmDimCatgModlCegIctVO> errorList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setL3CegCnName("5502");
        dimensionParamVO.setErrorList(errorList);
        dimensionParamVO.getErrorList();

        DimensionParamVO.builder().keyword("元器").build();
        Assert.assertNotNull(dimensionParamVO);
    }
}