/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.scheduler;

import com.huawei.it.fcst.config.ConfigUtil;
import com.huawei.it.fcst.constant.Constant;
import com.huawei.it.fcst.industry.pbi.dao.IDmVirtualizedTaskDao;
import com.huawei.it.fcst.enums.GroupTaskType;
import com.huawei.it.fcst.industry.pbi.enums.IndustryConstEnum;
import com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstBaseCusDimVO;
import com.huawei.it.fcst.util.ObjectCopyUtil;
import com.huawei.it.jalor5.core.annotation.NoJalorTransation;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.inject.Inject;

import java.io.Serializable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 月度func执行
 *
 * <AUTHOR>
 * @since 202407
 */
@Service("task.monthTaskProcessService")
@Slf4j
public class MonthTaskProcessService extends AbstractTaskProcessService {

    @Inject
    private IDmVirtualizedTaskDao virtualizedTaskDao;
    @Override
    public GroupTaskType getTaskType() {
        return GroupTaskType.MONTH;
    }

    @Override
    @NoJalorTransation
    public Boolean process(Serializable serializable,Serializable beforeResult) {
        DmFcstBaseCusDimVO dmFcstBaseCusDimVO = (DmFcstBaseCusDimVO)serializable;
        log.info("MonthTaskProcessService:start->{},虚化id:{}", dmFcstBaseCusDimVO.getPageType(), dmFcstBaseCusDimVO.getCustomId());
        try {
            // 设置任务执行类型
            dmFcstBaseCusDimVO.setPageType(this.getTaskType().name());
            CompletableFuture<String> future1 = CompletableFuture.supplyAsync(() -> this.serialCallMonthFunc(dmFcstBaseCusDimVO), EXECUTOR_SERVICE);
            CompletableFuture<String> future2 = CompletableFuture.supplyAsync(() -> this.serialCallMonthTopSpartCostAmtFunc(dmFcstBaseCusDimVO), EXECUTOR_SERVICE);
            CompletableFuture<String> future3 = CompletableFuture.supplyAsync(() -> this.callMontCostAmtFunc(dmFcstBaseCusDimVO), EXECUTOR_SERVICE);
            CompletableFuture<String> future4 = CompletableFuture.supplyAsync(() -> this.callMontCostYtdAmtFunc(dmFcstBaseCusDimVO), EXECUTOR_SERVICE);
            return getFuncStatusResult(future1, future2, future3,future4);
        } catch (Exception exception) {
            log.error(exception.getMessage());
            return Boolean.FALSE;
        }
    }

    /**
     * 执行获取结果
     *
     * @param futures futures
     * @return true，false
     */
    Boolean getFuncStatusResult(CompletableFuture<String>... futures) {
        for (CompletableFuture<String> completableFuture : futures) {
            try {
                if (!FUNC_STATUS_SUCCESS.equalsIgnoreCase(completableFuture.get(30, TimeUnit.MINUTES))) {
                    return Boolean.FALSE;
                }
            } catch (Exception ex) {
                log.error(ex.getMessage());
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 月度的串行执行 月度和月累计函数
     *
     * @param dmFcstBaseCusDimVO
     * @return
     */
    private String serialCallMonthFunc(DmFcstBaseCusDimVO dmFcstBaseCusDimVO) {
        if (!FUNC_STATUS_SUCCESS.equalsIgnoreCase(callMonthFuncTask(dmFcstBaseCusDimVO))) {
            return FUNC_STATUS_FAIL;
        }
        if (!FUNC_STATUS_SUCCESS.equalsIgnoreCase(callMonthAccumulationFuncTask(dmFcstBaseCusDimVO))) {
            return FUNC_STATUS_FAIL;
        }
        return callInterLockMonAndYtdFuncTask(dmFcstBaseCusDimVO);
    }

    /**
     * 月度函数
     *
     * @param dmFcstBaseCusDimVO 参数
     */
    private String callMonthFuncTask(DmFcstBaseCusDimVO dmFcstBaseCusDimVO) {
        String result = virtualizedTaskDao.callMonthFuncTask(dmFcstBaseCusDimVO, ConfigUtil.getInstance().get16PlainText());
        log.info("f_fcst_ict_point_base_cus_mon_result_t:{},{},虚化id:{}", this.getTaskType().name(), result, dmFcstBaseCusDimVO.getCustomId());
        return result;
    }

    /**
     * 月度累计函数
     *
     * @param dmFcstBaseCusDimVO 参数
     */
    private String callMonthAccumulationFuncTask(DmFcstBaseCusDimVO dmFcstBaseCusDimVO) {
        DmFcstBaseCusDimVO monthAccumulation = new DmFcstBaseCusDimVO();
        BeanUtils.copyProperties(dmFcstBaseCusDimVO, monthAccumulation);
        monthAccumulation.setPageType(GroupTaskType.REPLACE_DIM.name());
        String result = virtualizedTaskDao.callMonthFuncTask(monthAccumulation, ConfigUtil.getInstance().get16PlainText());
        log.info("f_fcst_ict_point_base_cus_mon_result_t:{},{},虚化id:{}", GroupTaskType.REPLACE_DIM.name(), result, dmFcstBaseCusDimVO.getCustomId());
        return result;
    }

    /**
     * 成本分部图
     *
     * @param dmFcstBaseCusDimVO 参数
     */
    private String callMontCostAmtFunc(DmFcstBaseCusDimVO dmFcstBaseCusDimVO) {
        String result = virtualizedTaskDao.callMontCostAmtFunc(dmFcstBaseCusDimVO, ConfigUtil.getInstance().get16PlainText());
        log.info("f_dm_fcst_mon_cost_amt:{},虚化id:{}", result, dmFcstBaseCusDimVO.getCustomId());
        return result;
    }


    /**
     * 成本分部图累积
     *
     * @param dmFcstBaseCusDimVO 参数
     */
    private String callMontCostYtdAmtFunc(DmFcstBaseCusDimVO dmFcstBaseCusDimVO) {
        String result = virtualizedTaskDao.callMontCostYtdAmtFunc(dmFcstBaseCusDimVO, ConfigUtil.getInstance().get16PlainText());
        log.info("f_dm_fcst_ytd_mon_cost_amt:{},虚化id:{}", result, dmFcstBaseCusDimVO.getCustomId());
        return result;
    }

    /**
     * 勾稽管理月度累计
     *
     * @param dmFcstBaseCusDimVO 参数
     */
    private String callInterLockMonAndYtdFuncTask(DmFcstBaseCusDimVO dmFcstBaseCusDimVO) {
        DmFcstBaseCusDimVO dmFcstBaseCusDimInterMon = ObjectCopyUtil.copy(dmFcstBaseCusDimVO, DmFcstBaseCusDimVO.class);
        dmFcstBaseCusDimInterMon.setPageType("INTERLOCK");
        dmFcstBaseCusDimInterMon.setYtdFlag("MON");
        String result = virtualizedTaskDao.callMonthFuncTask(dmFcstBaseCusDimInterMon, ConfigUtil.getInstance().get16PlainText());
        log.info("f_fcst_ict_point_base_cus_mon_result_t:{},{},虚化id:{}", result, "INTERLOCK_MON", dmFcstBaseCusDimVO.getCustomId());
        if (!FUNC_STATUS_SUCCESS.equalsIgnoreCase(result)) {
            return FUNC_STATUS_FAIL;
        }
        return callInterLockYtdFuncTask(dmFcstBaseCusDimVO);
    }

    /**
     * 勾稽管理月度累计
     *
     * @param dmFcstBaseCusDimVO 参数
     */
    private String callInterLockYtdFuncTask(DmFcstBaseCusDimVO dmFcstBaseCusDimVO) {
        DmFcstBaseCusDimVO dmFcstBaseCusDimInterYtd = ObjectCopyUtil.copy(dmFcstBaseCusDimVO, DmFcstBaseCusDimVO.class);
        dmFcstBaseCusDimInterYtd.setPageType("INTERLOCK");
        dmFcstBaseCusDimInterYtd.setYtdFlag("YTD");
        String result = virtualizedTaskDao.callMonthFuncTask(dmFcstBaseCusDimInterYtd, ConfigUtil.getInstance().get16PlainText());
        log.info("f_fcst_ict_point_base_cus_mon_result_t:{},{},虚化id:{}", result, "INTERLOCK_YTD", dmFcstBaseCusDimVO.getCustomId());
        return result;
    }

    /**
     * 月度的串行执行 月度和月累计函数
     *
     * @param dmFcstBaseCusDimVO
     * @return
     */
    private String serialCallMonthTopSpartCostAmtFunc(DmFcstBaseCusDimVO dmFcstBaseCusDimVO) {
        if (IndustryConstEnum.VIEW_FLAG.DIMENSION.getValue().equals(dmFcstBaseCusDimVO.getViewFlag())) {
            return FUNC_STATUS_SUCCESS;
        }
        if (IndustryConstEnum.VIEW_FLAG.PROD_SPART.getValue().equals(dmFcstBaseCusDimVO.getViewFlag()) && dmFcstBaseCusDimVO.getLvCode().contains(",")) {
            return FUNC_STATUS_SUCCESS;
        }
        if (IndustryConstEnum.VIEW_FLAG.PROD_SPART.getValue().equals(dmFcstBaseCusDimVO.getViewFlag()) && Constant.StrEnum.MAIN_FLAG.getValue().equals(dmFcstBaseCusDimVO.getMainFlag())) {
            return FUNC_STATUS_SUCCESS;
        }
        if (!FUNC_STATUS_SUCCESS.equalsIgnoreCase(callMonthTopSpartCostAmtFunc(dmFcstBaseCusDimVO))) {
            return FUNC_STATUS_FAIL;
        }
        return callMonthTopSpartYtdCostAmtFunc(dmFcstBaseCusDimVO);
    }

    /**
     * Top-Spart的发货量、成本金额的实时月度虚化函数：f_dm_fcst_ict_top_base_cus_mon_repl_cost_info_t(f_cost_type character varying ,  f_granularity_type character varying , f_keystr text DEFAULT NULL::character varying, f_custom_id integer,f_version_id integer DEFAULT NULL::integer, OUT x_result_status text)
     *
     * @param dmFcstBaseCusDimVO 参数
     */
    private String callMonthTopSpartCostAmtFunc(DmFcstBaseCusDimVO dmFcstBaseCusDimVO) {
        log.info("start ————>f_dm_fcst_ict_top_base_cus_mon_repl_cost_info_t,虚化id:{}", dmFcstBaseCusDimVO.getCustomId());
        String result = virtualizedTaskDao.callMonthTopSpartCostAmtFunc(dmFcstBaseCusDimVO, ConfigUtil.getInstance().get16PlainText());
        log.info("end ————>f_dm_fcst_ict_top_base_cus_mon_repl_cost_info_t:{},虚化id:{}", result, dmFcstBaseCusDimVO.getCustomId());
        return result;
    }

    /**
     * Top-Spart的发货量、成本金额的实时月累计虚化函数：f_dm_fcst_ict_top_base_cus_ytd_repl_cost_info_t(f_cost_type character varying ,  f_granularity_type character varying , f_keystr text DEFAULT NULL::character varying, f_custom_id integer,f_version_id integer DEFAULT NULL::integer, OUT x_result_status text)
     *
     * @param dmFcstBaseCusDimVO 参数
     */
    private String callMonthTopSpartYtdCostAmtFunc(DmFcstBaseCusDimVO dmFcstBaseCusDimVO) {
        log.info("start ————>f_dm_fcst_ict_top_base_cus_ytd_repl_cost_info_t,虚化id:{}", dmFcstBaseCusDimVO.getCustomId());
        String result = virtualizedTaskDao.callMonthTopSpartYtdCostAmtFunc(dmFcstBaseCusDimVO, ConfigUtil.getInstance().get16PlainText());
        log.info("end ————>f_dm_fcst_ict_top_base_cus_ytd_repl_cost_info_t:{},虚化id:{}", result, dmFcstBaseCusDimVO.getCustomId());
        return result;
    }
}
