/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.annual.DmFocAnnualAmpVO;
import com.huawei.it.fcst.industry.index.vo.replace.ReplaceAnalysisVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * IAnnualAmpDao
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
public interface IReplaceAmpDao {

    List<DmFocAnnualAmpVO> allReplaceAmpCost(ReplaceAnalysisVO replaceAnalysisVO);

    List<DmFocAnnualAmpVO> industryReplaceCostList(ReplaceAnalysisVO replaceAnalysisVO);

    List<String> getAnnualPeriodYear(@Param("versionId") Long versionId);

}
