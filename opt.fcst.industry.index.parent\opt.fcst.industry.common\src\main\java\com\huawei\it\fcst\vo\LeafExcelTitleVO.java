/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.vo;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.poi.ss.usermodel.CellType;

/**
 * LeafExcelTitleVO Class
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
@Getter
@Setter
@NoArgsConstructor
public class LeafExcelTitleVO extends AbstractExcelTitleVO {

    private CellType dataType ;

    /**
     * 列编号，用于选择列时，区分不同的列，每个列编号 都不一样，前台选择那些列，通过这个字段来识别
     */
    private String columnCode;

    /**
     * 如果dataType是CellType.NUMERIC ,这个字段用主于定义数字的格式
     */
    private String dataFormatStr ;

    private String dataKey;

    public LeafExcelTitleVO(String value, Integer width, Boolean isSelected, String dataKey, String columnCode,
                            CellType dataType, Boolean isEditable) {
        this.dataType = dataType;
        this.setValue(value);
        this.setWidth(width);
        this.setSelected(isSelected);
        this.dataKey = dataKey;
        this.columnCode = columnCode;
        this.setEditable(isEditable);
    }
}
