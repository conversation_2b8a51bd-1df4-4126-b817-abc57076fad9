/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.dao;

import com.huawei.it.fcst.industry.pbi.vo.drop.CodeReplInfoVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstDimInfoVO;

import java.util.List;

/**
 * IDmFocViewInfoDao
 *
 * <AUTHOR>
 * @since 2023/3/14
 */
public interface IDmFcstCodeReplInfoDao {

    List<DmFcstDimInfoVO> getCodeReplInfoList(CodeReplInfoVO codeReplInfoVO);

    Integer getCoceReplLv4Num(CodeReplInfoVO codeReplInfoVO);

    List<DmFcstDimInfoVO> getCoceReplLv4Code(CodeReplInfoVO codeReplInfoVO);

    List<DmFcstDimInfoVO> getReplaceNameNum(CodeReplInfoVO codeReplInfoVO);

}
