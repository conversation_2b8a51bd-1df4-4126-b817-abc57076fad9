/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.enums;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * GroupLevelEnumPTest Class
 *
 * <AUTHOR>
 * @since 2023/6/28
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class GroupLevelEnumPTest {

    @Test
    public void getInstance() {
        GroupLevelEnumP levelEnum = GroupLevelEnumP.getInstance("");
        Assert.assertNull(levelEnum);
    }

}