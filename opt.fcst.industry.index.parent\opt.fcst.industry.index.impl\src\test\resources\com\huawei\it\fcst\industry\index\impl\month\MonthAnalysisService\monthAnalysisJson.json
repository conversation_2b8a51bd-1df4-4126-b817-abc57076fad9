{"CommonViewVO": {"groupLevel": "test", "viewFlag": "test", "groupCodeList": ["test"], "combGroupCodeList": ["test"], "teamCodeList": ["test"], "combTeamCodeList": ["test"], "teamLevel": "test", "keyWord": "test", "versionId": 1, "monthVersionId": 1, "pageFlag": "test", "nextGroupLevel": "test", "dmsCodeList": ["test"], "lv1CodeList": ["test"], "lv2CodeList": ["test"], "lv3CodeList": ["test"], "combDmsCodeList": ["test"], "granularityType": "test", "caliberFlag": "test", "periodYear": "test", "l1NameList": ["test"], "l2NameList": ["test"], "combL1NameList": ["test"], "combL2NameList": ["test"], "overseaFlag": "test", "lv0ProdListCode": "test", "lv0DimensionSet": ["test"], "lv1DimensionSet": ["test"], "lv2DimensionSet": ["test"], "lv3DimensionSet": ["test"], "lv1ProdRndTeamCodeSet": ["test"], "lv2ProdRndTeamCodeSet": ["test"], "lv3ProdRndTeamCodeSet": ["test"], "isCombination": true, "pageSymbol": "test", "granularityPageSymbol": "test", "filterGroupLevel": "test", "customId": 1, "customIdList": [1], "parentCustomIdList": [1], "userId": "test", "roleId": "test", "lv0Flag": "test", "isMultipleSelect": true, "permissionTag": true, "dataType": "test", "dimensionCodeList": ["test"], "dimensionSubcategoryCodeList": ["test"], "dimensionSubDetailCodeList": ["test"], "combDimensionCodeList": ["test"], "combSpartCodeList": ["test"], "combDimensionSubcategoryCodeList": ["test"], "combDimensionSubDetailCodeList": ["test"], "maxViewFlag": "test", "lv1NoPermissList": ["test"], "lv2NoPermissList": ["test"], "purCodeList": ["test"], "purLevel": "test", "reverseFlag": true}, "List<DmFocViewInfoVO>": [{"id": 1, "lv0ProdRndTeamCode": "test", "lv0ProdRdTeamCnName": "test", "lv1ProdRndTeamCode": "test", "lv1ProdRdTeamCnName": "test", "lv2ProdRndTeamCode": "test", "lv2ProdRdTeamCnName": "test", "lv3ProdRndTeamCode": "test", "lv3ProdRdTeamCnName": "test", "lv0ProdListCode": "test", "lv0ProdListCnName": "test", "prodRndTeamCode": "test", "l3CegCode": "test", "l3CegCnName": "test", "categoryCode": "test", "categoryCnName": "test", "itemCode": "test", "itemCnName": "test", "createdBy": "test", "creationDate": 1695120562993, "lastUpdatedBy": "test", "lastUpdateDdate": 1695120562993, "delFlag": "test", "viewFlag": "test", "viewFlagValue": "test", "groupCode": "test", "groupLevel": "test", "groupCnName": "test", "versionId": 1, "permissionFlag": "test", "removeFlag": "test", "weightRate": 1.0, "granularityType": "test", "subEnableFlag": "test", "isCombination": true, "customId": 1, "parentCode": "test", "parentCnName": "test", "pageFlag": "test", "l1Name": "test", "l2Name": "test", "dimensionCode": "test", "dimensionCnName": "test", "dimensionSubCategoryCode": "test", "dimensionSubCategoryCnName": "test", "dimensionSubDetailCode": "test", "dimensionSubDetailCnName": "test", "userId": "test", "roleId": "test", "enableFlag": "test", "caliberFlag": "test", "overseaFlag": "test", "l3CegShortCnName": "test", "l4CegShortCnName": "test", "l4CegCnName": "test", "l4CegCode": "test", "customCnName": "test", "isSeparate": "test", "connectCode": "test", "connectParentCode": "test", "children": [null], "topItemCode": "test", "itemNum": 1}], "List<DmFocMonthCostIdxVO>": [{"dimensionSubcategoryCode": "test", "l2Name": "test", "weightPercent": "test", "lastUpdateDate": 1695122249475, "dimensionCode": "test", "delFlag": "test", "customId": 1, "customCnName": "test", "dmsCnName": "test", "parentCnName": "test", "l1Name": "test", "serialVersionUID": 1, "appendFlag": "test", "parentCode": "test", "weightRate": 1.0, "groupLevel": "test", "id": 1, "purCode": "test", "groupCode": "test", "groupCnNameExp": "test", "dimensionSubDetailCode": "test", "lastUpdatedBy": "test", "periodId": 1, "dimensionCnName": "test", "dimensionSubDetailCnName": "test", "dimensionSubcategoryCnName": "test", "purCnName": "test", "creationDate": 1695122249475, "prodRndTeamCnName": "test", "isContainComb": true, "viewFlag": "test", "versionId": 1, "dmsCode": "test", "basePeriodId": 1, "periodYear": 1, "createdBy": "test", "costIndex": 1.0, "groupCnName": "test", "prodRndTeamCode": "test", "scenarioFlag": "test"}], "PagedResult<DmFocMonthCostIdxVO>": {"pageVO": {"totalRows": 1, "curPage": 1, "pageSize": 1, "resultMode": 1, "startIndex": 1, "endIndex": 1, "orderBy": "test", "filterStr": "test", "filters": [{"fn": "test", "ft": "test", "fv": {}, "fr": "test"}]}, "result": [{"dimensionSubcategoryCode": "test", "l2Name": "test", "weightPercent": "test", "lastUpdateDate": 1695122249475, "dimensionCode": "test", "delFlag": "test", "customId": 1, "customCnName": "test", "dmsCnName": "test", "parentCnName": "test", "l1Name": "test", "serialVersionUID": 1, "appendFlag": "test", "parentCode": "test", "weightRate": 1.0, "groupLevel": "test", "id": 1, "purCode": "test", "groupCode": "test", "groupCnNameExp": "test", "dimensionSubDetailCode": "test", "lastUpdatedBy": "test", "periodId": 1, "dimensionCnName": "test", "dimensionSubDetailCnName": "test", "dimensionSubcategoryCnName": "test", "purCnName": "test", "creationDate": 1695122249475, "prodRndTeamCnName": "test", "isContainComb": true, "viewFlag": "test", "versionId": 1, "dmsCode": "test", "basePeriodId": 1, "periodYear": 1, "createdBy": "test", "costIndex": 1.0, "groupCnName": "test", "prodRndTeamCode": "test", "scenarioFlag": "test"}]}, "List<DmFocMonthWeightVO>": [{"dimensionSubcategoryCode": "test", "l2Name": "test", "weightPercent": "test", "lastUpdateDate": 1695264855941, "dimensionCode": "test", "weightOrder": "test", "delFlag": "test", "customCnName": "test", "customId": 10, "dmsCnName": "test", "parentCnName": "test", "l1Name": "test", "serialVersionUID": 1, "parentCode": "test", "weightRate": 1.0, "groupLevel": "SUB_DETAIL", "id": 1, "weightRateStr": "test", "groupCode": "test", "groupCnNameExp": "test", "dimensionSubDetailCode": "test", "lastUpdatedBy": "test", "dimensionCnName": "test", "dimensionSubDetailCnName": "test", "dimensionSubcategoryCnName": "test", "creationDate": 1695264855941, "prodRndTeamCnName": "test", "periodYearType": "test", "viewFlag": "test", "versionId": 1, "dmsCode": "test", "periodYear": "test", "createdBy": "test", "groupCnName": "test", "prodRndTeamCode": "test"}, {"dimensionSubcategoryCode": "test", "l2Name": "test", "weightPercent": "test", "lastUpdateDate": 1695264855941, "dimensionCode": "test", "weightOrder": "test", "delFlag": "test", "customCnName": "test", "customId": 1, "dmsCnName": "test", "parentCnName": "test", "l1Name": "test", "serialVersionUID": 1, "parentCode": "test", "weightRate": 1.0, "groupLevel": "SUBCATEGORY", "id": 1, "weightRateStr": "test", "groupCode": "test", "groupCnNameExp": "test", "dimensionSubDetailCode": "test", "lastUpdatedBy": "test", "dimensionCnName": "test", "dimensionSubDetailCnName": "test", "dimensionSubcategoryCnName": "test", "creationDate": 1695264855941, "prodRndTeamCnName": "test", "periodYearType": "test", "viewFlag": "test", "versionId": 1, "dmsCode": "test", "periodYear": "test", "createdBy": "test", "groupCnName": "test", "prodRndTeamCode": "test"}, {"dimensionSubcategoryCode": "test", "l2Name": "test", "weightPercent": "test", "lastUpdateDate": 1695264855941, "dimensionCode": "test", "weightOrder": "test", "delFlag": "test", "customCnName": "test", "customId": 1, "dmsCnName": "test", "parentCnName": "test", "l1Name": "test", "serialVersionUID": 1, "parentCode": "test", "weightRate": 1.0, "groupLevel": "DIMENSION", "id": 1, "weightRateStr": "test", "groupCode": "test", "groupCnNameExp": "test", "dimensionSubDetailCode": "test", "lastUpdatedBy": "test", "dimensionCnName": "test", "dimensionSubDetailCnName": "test", "dimensionSubcategoryCnName": "test", "creationDate": 1695264855941, "prodRndTeamCnName": "test", "periodYearType": "test", "viewFlag": "test", "versionId": 1, "dmsCode": "test", "periodYear": "test", "createdBy": "test", "groupCnName": "test", "prodRndTeamCode": "test"}, {"dimensionSubcategoryCode": "test", "l2Name": "test", "weightPercent": "test", "lastUpdateDate": 1695264855941, "dimensionCode": "test", "weightOrder": "test", "delFlag": "test", "customCnName": "test", "customId": 1, "dmsCnName": "test", "parentCnName": "test", "l1Name": "test", "serialVersionUID": 1, "parentCode": "test", "weightRate": 1.0, "groupLevel": "test", "id": 1, "weightRateStr": "test", "groupCode": "test", "groupCnNameExp": "test", "dimensionSubDetailCode": "test", "lastUpdatedBy": "test", "dimensionCnName": "test", "dimensionSubDetailCnName": "test", "dimensionSubcategoryCnName": "test", "creationDate": 1695264855941, "prodRndTeamCnName": "test", "periodYearType": "test", "viewFlag": "test", "versionId": 1, "dmsCode": "test", "periodYear": "test", "createdBy": "test", "groupCnName": "test", "prodRndTeamCode": "test"}, {"dimensionSubcategoryCode": "test", "l2Name": "test", "weightPercent": "test", "lastUpdateDate": 1695264855941, "dimensionCode": "test", "weightOrder": "test", "delFlag": "test", "customCnName": "test", "customId": 1, "dmsCnName": "test", "parentCnName": "test", "l1Name": "test", "serialVersionUID": 1, "parentCode": "test", "weightRate": 1.0, "groupLevel": "test", "id": 1, "weightRateStr": "test", "groupCode": "test", "groupCnNameExp": "test", "dimensionSubDetailCode": "test", "lastUpdatedBy": "test", "dimensionCnName": "test", "dimensionSubDetailCnName": "test", "dimensionSubcategoryCnName": "test", "creationDate": 1695264855941, "prodRndTeamCnName": "test", "periodYearType": "test", "viewFlag": "test", "versionId": 1, "dmsCode": "test", "periodYear": "test", "createdBy": "test", "groupCnName": "test", "prodRndTeamCode": "test"}, {"dimensionSubcategoryCode": "test", "l2Name": "test", "weightPercent": "test", "lastUpdateDate": 1695264855941, "dimensionCode": "test", "weightOrder": "test", "delFlag": "test", "customCnName": "test", "customId": 1, "dmsCnName": "test", "parentCnName": "test", "l1Name": "test", "serialVersionUID": 1, "parentCode": "test", "weightRate": 1.0, "groupLevel": "test", "id": 1, "weightRateStr": "test", "groupCode": "test", "groupCnNameExp": "test", "dimensionSubDetailCode": "test", "lastUpdatedBy": "test", "dimensionCnName": "test", "dimensionSubDetailCnName": "test", "dimensionSubcategoryCnName": "test", "creationDate": 1695264855941, "prodRndTeamCnName": "test", "periodYearType": "test", "viewFlag": "test", "versionId": 1, "dmsCode": "test", "periodYear": "test", "createdBy": "test", "groupCnName": "test", "prodRndTeamCode": "test"}, {"dimensionSubcategoryCode": "test", "l2Name": "test", "weightPercent": "test", "lastUpdateDate": 1695264855941, "dimensionCode": "test", "weightOrder": "test", "delFlag": "test", "customCnName": "test", "customId": 1, "dmsCnName": "test", "parentCnName": "test", "l1Name": "test", "serialVersionUID": 1, "parentCode": "test", "weightRate": 1.0, "groupLevel": "test", "id": 1, "weightRateStr": "test", "groupCode": "test", "groupCnNameExp": "test", "dimensionSubDetailCode": "test", "lastUpdatedBy": "test", "dimensionCnName": "test", "dimensionSubDetailCnName": "test", "dimensionSubcategoryCnName": "test", "creationDate": 1695264855941, "prodRndTeamCnName": "test", "periodYearType": "test", "viewFlag": "test", "versionId": 1, "dmsCode": "test", "periodYear": "test", "createdBy": "test", "groupCnName": "test", "prodRndTeamCode": "test"}, {"dimensionSubcategoryCode": "test", "l2Name": "test", "weightPercent": "test", "lastUpdateDate": 1695264855941, "dimensionCode": "test", "weightOrder": "test", "delFlag": "test", "customCnName": "test", "customId": 1, "dmsCnName": "test", "parentCnName": "test", "l1Name": "test", "serialVersionUID": 1, "parentCode": "test", "weightRate": 1.0, "groupLevel": "test", "id": 1, "weightRateStr": "test", "groupCode": "test", "groupCnNameExp": "test", "dimensionSubDetailCode": "test", "lastUpdatedBy": "test", "dimensionCnName": "test", "dimensionSubDetailCnName": "test", "dimensionSubcategoryCnName": "test", "creationDate": 1695264855941, "prodRndTeamCnName": "test", "periodYearType": "test", "viewFlag": "test", "versionId": 1, "dmsCode": "test", "periodYear": "test", "createdBy": "test", "groupCnName": "test", "prodRndTeamCode": "test"}, {"dimensionSubcategoryCode": "test", "l2Name": "test", "weightPercent": "test", "lastUpdateDate": 1695264855941, "dimensionCode": "test", "weightOrder": "test", "delFlag": "test", "customCnName": "test", "customId": 1, "dmsCnName": "test", "parentCnName": "test", "l1Name": "test", "serialVersionUID": 1, "parentCode": "test", "weightRate": 1.0, "groupLevel": "test", "id": 1, "weightRateStr": "test", "groupCode": "test", "groupCnNameExp": "test", "dimensionSubDetailCode": "test", "lastUpdatedBy": "test", "dimensionCnName": "test", "dimensionSubDetailCnName": "test", "dimensionSubcategoryCnName": "test", "creationDate": 1695264855941, "prodRndTeamCnName": "test", "periodYearType": "test", "viewFlag": "test", "versionId": 1, "dmsCode": "test", "periodYear": "test", "createdBy": "test", "groupCnName": "test", "prodRndTeamCode": "test"}, {"dimensionSubcategoryCode": "test", "l2Name": "test", "weightPercent": "test", "lastUpdateDate": 1695264855941, "dimensionCode": "test", "weightOrder": "test", "delFlag": "test", "customCnName": "test", "customId": 1, "dmsCnName": "test", "parentCnName": "test", "l1Name": "test", "serialVersionUID": 1, "parentCode": "test", "weightRate": 1.0, "groupLevel": "test", "id": 1, "weightRateStr": "test", "groupCode": "test", "groupCnNameExp": "test", "dimensionSubDetailCode": "test", "lastUpdatedBy": "test", "dimensionCnName": "test", "dimensionSubDetailCnName": "test", "dimensionSubcategoryCnName": "test", "creationDate": 1695264855941, "prodRndTeamCnName": "test", "periodYearType": "test", "viewFlag": "test", "versionId": 1, "dmsCode": "test", "periodYear": "test", "createdBy": "test", "groupCnName": "test", "prodRndTeamCode": "test"}, {"dimensionSubcategoryCode": "test", "l2Name": "test", "weightPercent": "test", "lastUpdateDate": 1695264855941, "dimensionCode": "test", "weightOrder": "test", "delFlag": "test", "customCnName": "test", "customId": 1, "dmsCnName": "test", "parentCnName": "test", "l1Name": "test", "serialVersionUID": 1, "parentCode": "test", "weightRate": 1.0, "groupLevel": "test", "id": 1, "weightRateStr": "test", "groupCode": "test", "groupCnNameExp": "test", "dimensionSubDetailCode": "test", "lastUpdatedBy": "test", "dimensionCnName": "test", "dimensionSubDetailCnName": "test", "dimensionSubcategoryCnName": "test", "creationDate": 1695264855941, "prodRndTeamCnName": "test", "periodYearType": "test", "viewFlag": "test", "versionId": 1, "dmsCode": "test", "periodYear": "test", "createdBy": "test", "groupCnName": "test", "prodRndTeamCode": "test"}], "MonthAnalysisVO": {"viewFlag": "1", "prodRndTeamCodeList": ["test"], "prodRndTeamCode": "test", "lv1ProdRdTeamCnName": ["test"], "lv2ProdRdTeamCnName": ["test"], "lv3ProdRdTeamCnName": ["test"], "lv3CegCnName": "test", "lv4CegCnName": "test", "categoryCnName": "test", "basePeriodId": 1, "subGroupCodeList": ["test"], "groupCodeList": ["test"], "groupCode": "test", "cnName": "test", "parentCodeList": ["test"], "combParentCodeList": ["test"], "parentLevel": "test", "multiLevel": "test", "groupLevel": "LV1", "versionId": 1, "topCateVersionId": 1, "periodStartTime": 1, "periodEndTime": 1, "curPage": 1, "pageSize": 1, "groupCodeOrder": "test", "yoyFlag": "test", "fileName": "test", "granularityType": "test", "caliberFlag": "test", "l1NameList": ["test"], "l1Name": "test", "l2NameList": ["test"], "l2Name": "test", "profitsName": "test", "lv0DimensionSet": ["test"], "lv1DimensionSet": ["test"], "lv2DimensionSet": ["test"], "lv3DimensionSet": ["test"], "overseaFlag": "test", "lv0ProdListCode": "test", "dmsCodeList": ["test"], "dmsCode": "test", "dimensionCnName": ["test"], "dimensionSubCategoryCnName": ["test"], "dimensionSubDetailCnName": ["test"], "isMultipleSelect": true, "isContainComb": true, "customIdList": [1], "customId": "test", "combinaCodeList": ["test"], "combinaSubGroupCodeList": ["test"], "reverseViewFlag": true, "dimensionCodeList": ["test"], "dimensionSubcategoryCodeList": ["test"], "dimensionSubDetailCodeList": ["test"], "teamLevel": "test", "nextMonthGroupLevel": "test", "nextMonthGroupName": "test", "isShowChildContent": true, "isShowPriceChart": true, "purCodeList": ["test"], "purCode": "test", "purLevel": "test", "teamCodeList": ["test"]}, "VarifyTaskVO": {"taskId": 1, "status": "test", "periodId": 1, "lastUpdateDate": 1695171774111, "taskType": "test", "combStatus": "test"}}