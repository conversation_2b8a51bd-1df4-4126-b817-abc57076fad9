/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.combination.CombinationVO;
import com.huawei.it.fcst.industry.index.vo.combination.DmCustomCombVO;

import java.util.List;

/**
 * IDmFocIasCustomCombDao Class
 *
 * <AUTHOR>
 * @since 2024/6/25
 */
public interface IDmFocIasCustomCombDao {

    List<DmCustomCombVO> getIasCustomCombList(CombinationVO combinationVO);

    List<DmCustomCombVO> getManufactureIasCustomCombList(CombinationVO combinationVO);
}
