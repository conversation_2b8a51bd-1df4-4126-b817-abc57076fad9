/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.fcst.util;

import org.apache.commons.lang3.StringUtils;

/**
 * 文件名处理工具，针对用户上传的文件的名称进行特殊字符处理，如果存在正反斜杠、英文句号，
 * 进行替换，替换为下划线，避免此类字符造成文件跨路径风险。
 *
 * <AUTHOR>
 * @since 2025-06-23 18:12
 */
public class FileNameUtil {
    private static final String PATTER_LEGAL_FILE_NAME = "[\\\\/.]";

    private static final String LEGAL_FILE_NAME_SEPARATOR = "_";

    public static String dealFileName(String fileName) {
        if(!StringUtils.isBlank(fileName)) {
            return fileName.replaceAll(PATTER_LEGAL_FILE_NAME, LEGAL_FILE_NAME_SEPARATOR);
        }
        return RandomStringGenerator.generateFileName();
    }
}
