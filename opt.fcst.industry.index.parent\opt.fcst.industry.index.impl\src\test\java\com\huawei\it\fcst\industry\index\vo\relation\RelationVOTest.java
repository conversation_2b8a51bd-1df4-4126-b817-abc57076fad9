/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.relation;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * RelationVOTest Class
 *
 * <AUTHOR>
 * @since 2023/4/23
 */
public class RelationVOTest extends BaseVOCoverUtilsTest<RelationVO> {
    @Override
    protected Class<RelationVO> getTClass() {
        return RelationVO.class;
    }

    @Test
    public void testMethod() {
        RelationVO dimensionParamVO = new RelationVO();
        List<String> cegCodeList = new ArrayList<>();
        cegCodeList.add("1102A");
        dimensionParamVO.builder().l3CodeList(cegCodeList).build();
        Assert.assertNotNull(dimensionParamVO);
    }
}