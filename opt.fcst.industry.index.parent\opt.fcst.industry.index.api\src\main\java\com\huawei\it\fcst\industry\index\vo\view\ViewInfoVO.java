/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.view;

import com.huawei.it.jalor5.core.base.BaseResourceVO;
import lombok.Getter;
import lombok.Setter;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023年03月10日
 */
@Getter
@Setter
public class ViewInfoVO extends BaseResourceVO {
    private Integer id;
    private String lv0ProdRndTeamCode;
    private String lv0ProdRnTeamName;
    private String lv1ProdRndTeamCode;
    private String lv1ProdRnTeamName;
    private String lv2ProdRndTeamCode;
    private String lv2ProdRnTeamName;
    private String lv3ProdRndTeamCode;
    private String lv3ProdRnTeamName;
    private String l3CegCode;
    private String l3CegName;
    private String categoryCode;
    private String categoryName;
    private String itemCode;
    private String itemName;
    private String delFlag;
    private String viewFlag;
    private String selfParentDimensionValue;
    private String dimensionValue;
    private String dimensionDisplayValue;
    private String groupLevel;
    private String extParentDimensionValue;
}
