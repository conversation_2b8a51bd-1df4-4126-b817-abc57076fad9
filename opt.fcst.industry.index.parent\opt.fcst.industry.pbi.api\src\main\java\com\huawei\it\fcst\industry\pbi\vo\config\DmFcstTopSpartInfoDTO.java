/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.config;

import com.huawei.it.fcst.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * DmFocTopItemInfoDTO Class
 *
 * <AUTHOR>
 * @since 2023/3/14
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "TOPSPART实体类映射")
public class DmFcstTopSpartInfoDTO extends BaseVO {

    /**
     * 重量级团队LV2编码
     **/
    @ApiModelProperty("lv2_prod_rnd_team_code")
    private String lv2ProdRndTeamCode;

    /**
     * 重量级团队LV2中文名称
     **/
    @ApiModelProperty("lv2_prod_rd_team_cn_name")
    private String lv2ProdRdTeamCnName;


    /**
     * 重量级团队LV3编码
     **/
    @ApiModelProperty("lv3_prod_rnd_team_code")
    private String lv3ProdRndTeamCode;

    /**
     * 重量级团队LV3中文名称
     **/
    @ApiModelProperty("lv3_prod_rd_team_cn_name")
    private String lv3ProdRdTeamCnName;

    /**
     * 重量级团队LV3.5编码
     **/
    @ApiModelProperty("lv4_prod_rnd_team_code")
    private String lv4ProdRndTeamCode;

    /**
     * 重量级团队LV3.5中文名称
     **/
    @ApiModelProperty("lv4_prod_rd_team_cn_name")
    private String lv4ProdRdTeamCnName;

    /**
     * 会计年(区间值, 例如:2022-2023, 2021，2022，2023)
     **/
    @ApiModelProperty("period_year")
    private String periodYear;

    /**
     * 重量级团队LV0编码
     **/
    @ApiModelProperty("lv0_prod_rnd_team_code")
    private String lv0ProdRndTeamCode;

    /**
     * 重量级团队LV0中文名称
     **/
    @ApiModelProperty("lv0_prod_rd_team_cn_name")
    private String lv0ProdRdTeamCnName;

    /**
     * 重量级团队LV1编码
     **/
    @ApiModelProperty("lv1_prod_rnd_team_code")
    private String lv1ProdRndTeamCode;

    /**
     * 重量级团队LV1中文名称
     **/
    @ApiModelProperty("lv1_prod_rd_team_cn_name")
    private String lv1ProdRdTeamCnName;

    /**
     * 代表处编码
     **/
    @ApiModelProperty("repoffice_code")
    private String repofficeCode;

    /**
     * 代表处中文名称
     **/
    @ApiModelProperty("repoffice_cn_name")
    private String repofficeCnName;

    /**
     * 地区部编码
     **/
    @ApiModelProperty("region_code")
    private String regionCode;

    /**
     * 地区部中文名称
     **/
    @ApiModelProperty("region_cn_name")
    private String regionCnName;

    /**
     * SPART编码
     **/
    @ApiModelProperty("top_spart_code")
    private String topSpartCode;

    /**
     * SPART名称
     **/
    @ApiModelProperty("top_spart_cn_name")
    private String topSpartCnName;

    /**
     * 国内海外标识
     **/
    @ApiModelProperty("oversea_flag")
    private String overseaFlag;

    /**
     * 路径
     **/
    @ApiModelProperty("view_flag")
    private String viewFlag;

    /**
     * 权重值
     **/
    @ApiModelProperty("weight_rate")
    private String weightRate;

    /**
     * 权重值
     **/
    @ApiModelProperty("main_flag")
    private String mainFlag;

    /**
     * BG编码
     **/
    @ApiModelProperty("bg_code")
    private String bgCode;

    /**
     * BG中文名称
     **/
    @ApiModelProperty("bg_cn_name")
    private String bgCnName;

    /**
     * BG编码
     **/
    @ApiModelProperty("lv0_prod_list_code")
    private String lv0ProdListCode;

    /**
     * 版本id
     **/
    @ApiModelProperty("version_id")
    private Long versionId;

    /**
     * 版本名称
     **/
    @ApiModelProperty("version_name")
    private String versionName;

    private String periodYear0;

    private String periodYear1;

    private String periodYear2;

    private String periodYear3;

    private String periodYear4;

    /**
     *
     *  报告期集合
     */
    List<String> yearPeriodList;

    /**
     * 父级CODE
     **/
    @ApiModelProperty("parent_code")
    private String parentCode;

    /**
     * 父级CODE权重值
     **/
    @ApiModelProperty("parent_weight_rate")
    private String parentWeightRate;

    /**
     * top数量
     **/
    @ApiModelProperty(value = "top数量")
    private Long topNums;

    /**
     * 是否为TOP类, Y:表示是, N:表示否
     **/
    @ApiModelProperty("is_top_flag")
    private String topFlag;


    @ApiModelProperty(value = "目录树")
    private String granularityType;

    @ApiModelProperty(value = "成本类型")
    private String costType;

    private String topType;

    private String errorMessage;

    @ApiModelProperty(value = "PSP软硬件标识")
    private String softwareMark;

    private String weight0;

    private String weight1;

    private String weight2;

    private String weight3;

    private String weight4;
    private Integer curPage;

    private Integer pageSize;

}
