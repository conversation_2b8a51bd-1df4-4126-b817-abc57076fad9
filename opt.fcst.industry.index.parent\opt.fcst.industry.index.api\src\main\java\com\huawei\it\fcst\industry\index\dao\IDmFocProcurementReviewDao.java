/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.config.ProcurementBottomVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * IDmFocProcurementReviewDao Class
 *
 * <AUTHOR>
 * @since 2023/12/7
 */
public interface IDmFocProcurementReviewDao {

    List<Map> findBottomReviewList(ProcurementBottomVO procurementBottomVO);

    List<ProcurementBottomVO> findBottomReviewVOList(ProcurementBottomVO procurementBottomVO);

    List<ProcurementBottomVO> findBaseDropDown(ProcurementBottomVO procurementBottomVO);

    int createProcurementList(@Param("list") List<ProcurementBottomVO> baseReviewVOList,@Param("tablePreFix") String tablePreFix);

    /**
     * 采购成本-ITEM异常数据录入分页查询
     *
     * @param procurementBottomVO procurementBottomVO
     * @param pageVO pageVO
     * @return PagedResult
     */
    PagedResult<ProcurementBottomVO> findByPage(@Param("procurementBottomVO") ProcurementBottomVO procurementBottomVO, @Param("pageVO") PageVO pageVO);

    List<ProcurementBottomVO> getConfigItemDropDown(ProcurementBottomVO procurementBottomVO);

    List<ProcurementBottomVO> checkStartPeriod(ProcurementBottomVO procurementBottomVO);

    ProcurementBottomVO getImpactQty(ProcurementBottomVO procurementBottomVO);

    ProcurementBottomVO getNewImpactQty(ProcurementBottomVO procurementBottomVO);

    void insertItemImpactQty(ProcurementBottomVO newImpactQty);

    ProcurementBottomVO getItemImpactQty(@Param("id")Long id,@Param("tablePreFix") String tablePreFix);
}
