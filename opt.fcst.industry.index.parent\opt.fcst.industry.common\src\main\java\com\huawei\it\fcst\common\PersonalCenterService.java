/*
 *
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.common;

import com.huawei.it.fcst.dao.IExcelImpExpRecordDao;
import com.huawei.it.fcst.export.vo.PbiDmFoiImpExpRecordVO;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Named;

/**
 * PersonalCenterService Class
 *
 * <AUTHOR>
 * @since 2023/3/14
 */
@Named("personCenterService")
@JalorResource(code = "personCenterService", desc = "person Center Service")
public class PersonalCenterService {

    @Autowired
    private IExcelImpExpRecordDao excelImpExpRecordDao;

    public void statisticsImportRecord(PbiDmFoiImpExpRecordVO dmFoiImpExpRecordVO, String userId) {
        dmFoiImpExpRecordVO.setCreatedBy(userId);
        dmFoiImpExpRecordVO.setLastUpdatedBy(userId);
        dmFoiImpExpRecordVO.setOptType("IMP");
        dmFoiImpExpRecordVO.setStatus("Save");
        dmFoiImpExpRecordVO.setFileType("xlsx");
        excelImpExpRecordDao.insertStatisticsRecord(dmFoiImpExpRecordVO);
    }

    public void statisticsExportRecord(PbiDmFoiImpExpRecordVO dmFoiImpExpRecordVO) {
        String userId = dmFoiImpExpRecordVO.getUserId();
        dmFoiImpExpRecordVO.setCreatedBy(userId);
        dmFoiImpExpRecordVO.setLastUpdatedBy(String.valueOf(userId));
        dmFoiImpExpRecordVO.setOptType("EXP");
        dmFoiImpExpRecordVO.setStatus("Save");
        dmFoiImpExpRecordVO.setFileType("xlsx");
        excelImpExpRecordDao.insertStatisticsRecord(dmFoiImpExpRecordVO);
    }
}
