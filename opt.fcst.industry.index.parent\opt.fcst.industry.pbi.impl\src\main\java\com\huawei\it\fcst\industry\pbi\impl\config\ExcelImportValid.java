/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.config;

import com.huawei.it.fcst.industry.pbi.annotations.ExcelValid;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.security.AccessController;
import java.security.PrivilegedAction;
import java.util.Objects;

/**
 * Excel导入字段校验工具类
 *
 * <AUTHOR>
 * @since 2024-09-29
 */
@Slf4j
public class ExcelImportValid {
    /**
     * Excel导入字段校验
     *
     * @param object 校验的JavaBean 其属性须有自定义注解
     */
    public static void valid(Object object) {
        StringBuilder builder = new StringBuilder();
        Field[] fields = object.getClass().getDeclaredFields();
        for (Field field : fields) {
            // 设置可访问
            AccessController.doPrivileged((PrivilegedAction) () -> {
                field.setAccessible(true);
                return null; // nothing to return
            });
            // 属性的值
            Object fieldValue = null;
            try {
                fieldValue = field.get(object);
            } catch (IllegalAccessException ex) {
                log.error(">>>IllegalAccessException when get field ==> field is: {}, {}", field.getName(), ex.getMessage());
            }
            // 是否包含必填校验注解
            boolean isExcelValid = field.isAnnotationPresent(ExcelValid.class);
            if (isExcelValid && Objects.isNull(fieldValue)) {
                builder.append(field.getAnnotation(ExcelValid.class).message()).append(";");
            }
        }
        // 设置错误信息
        if (builder.length() > 0) {
            setErrorMsg(object, builder.toString());
        } else {
            setErrorMsg(object, "");
        }
    }

    // 设置错误信息
    private static void setErrorMsg(Object object, String value) {
        try {
            // 获取要设置的字段
            Field field = object.getClass().getDeclaredField("errorMessage");
            // 设置可访问
            AccessController.doPrivileged((PrivilegedAction) () -> {
                field.setAccessible(true);
                return null; // nothing to return
            });
            // 设置字段值
            field.set(object, value);
        } catch (Exception ex) {
            log.error(">>>An exception occurred when set errorMessage field ==> {}", ex.getMessage());
        }
    }
}