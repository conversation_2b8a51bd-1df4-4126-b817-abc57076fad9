/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.config;

import com.huawei.it.fcst.industry.index.dao.IDmFocDataRefreshStatusDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTopCateInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTopItemInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocVersionDao;
import com.huawei.it.fcst.industry.index.impl.combination.AsyncService;
import com.huawei.it.fcst.industry.index.service.config.IConfigManufactureService;
import com.huawei.it.fcst.industry.index.utils.FcstIndexUtil;
import com.huawei.it.fcst.industry.index.utils.UserInfoUtils;
import com.huawei.it.fcst.industry.index.vo.combination.DmFocDataRefreshStatus;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocTopCateInfoDTO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocTopItemInfoDTO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoDTO;
import com.huawei.it.fcst.industry.index.vo.config.HistoryInputVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.log.Audit;

import cn.hutool.core.util.StrUtil;

import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.inject.Inject;
import javax.inject.Named;

/**
 * ConfigManufactureService Class
 *
 * <AUTHOR>
 * @since 2023/10/7
 */
@Named("configManufactureService")
@JalorResource(code = "configManufactureService", desc = "制造对象清单服务")
public class ConfigManufactureService implements IConfigManufactureService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ConfigManufactureService.class);
    @Inject
    private IDmFocVersionDao dmFocVersionDao;

    @Inject
    private IDmFocTopCateInfoDao dmFocTopCateInfoDao;

    @Inject
    private IDmFocDataRefreshStatusDao dataRefreshStatusDao;

    @Inject
    private IDmFocTopItemInfoDao dmFocTopItemInfoDao;

    @Inject
    private ConfigExportService configExportService;

    @Inject
    private AsyncService asyncService;

    @Override
    @JalorOperation(code = "getManufactureList", desc = "制造对象清单查询接口")
    public ResultDataVO getManufactureList(HistoryInputVO historyInputVO) throws CommonApplicationException {
        LOGGER.info("Begin ConfigManufactureService::getManufactureList");
        if (null == historyInputVO.getVersionId()) {
            throw new CommonApplicationException("入参清单数据版本为空");
        }
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(historyInputVO.getPageSize());
        pageVO.setCurPage(historyInputVO.getPageIndex());
        // 设置报告期范围
        DmFocVersionInfoDTO focVersionVO = dmFocVersionDao.findDmFocVersionDTOById(historyInputVO.getVersionId(), historyInputVO.getTablePreFix());
        if (StrUtil.isBlank(focVersionVO.getVersion())) {
            throw new CommonApplicationException("入参清单数据版本名称为空");
        }
        List<String> yearPeriodList = FcstIndexUtil.getPeriod(focVersionVO.getVersion());
        // 设置查询实体对象
        DmFocTopCateInfoDTO build = configExportService.setTopCateVO(historyInputVO, yearPeriodList);
        PagedResult<DmFocTopCateInfoDTO> cateByPage = dmFocTopCateInfoDao.findManufactureByPage(build, pageVO);

        Map result = new LinkedHashMap();
        result.put("result", cateByPage.getResult());
        result.put("pageVO", cateByPage.getPageVO());
        result.put("version", focVersionVO.getVersion());
        return ResultDataVO.success(result);
    }

    @Override
    @JalorOperation(code = "getItemList", desc = "历史规格品对象清单查询接口")
    public ResultDataVO getItemList(HistoryInputVO historyInputVO) throws CommonApplicationException {
        LOGGER.info("Begin ConfigManufactureService::getItemList");
        if (null == historyInputVO.getVersionId()) {
            throw new CommonApplicationException("入参清单数据版本为空");
        }
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(historyInputVO.getPageSize());
        pageVO.setCurPage(historyInputVO.getPageIndex());
        // 设置报告期范围
        DmFocVersionInfoDTO versionVO = dmFocVersionDao.findDmFocVersionDTOById(historyInputVO.getVersionId(), historyInputVO.getTablePreFix());
        if (StrUtil.isBlank(versionVO.getVersion())) {
            throw new CommonApplicationException("入参清单数据版本名称为空");
        }
        List<String> yearPeriodList = FcstIndexUtil.getPeriod(versionVO.getVersion());
        // 设置查询实体对象
        DmFocTopItemInfoDTO topItemInfoDTO = configExportService.setTopItemVO(historyInputVO, yearPeriodList);
        PagedResult<DmFocTopItemInfoDTO> cateByPage = dmFocTopItemInfoDao.findMfcItemByPage(topItemInfoDTO, pageVO);

        Map result = new LinkedHashMap();
        result.put("result", cateByPage.getResult());
        result.put("pageVO", cateByPage.getPageVO());
        result.put("version", versionVO.getVersion());
        return ResultDataVO.success(result);
    }

    @Override
    @JalorOperation(code = "exportManufacture", desc = "制造成本清单导出接口")
    @Audit(module = "configManufactureService-exportManufacture", operation = "exportManufacture", message = "制造成本清单导出接口")
    public ResultDataVO exportManufacture(HistoryInputVO historyInputVO)
            throws CommonApplicationException {
        LOGGER.info("Begin ConfigManufactureService::exportManufacture");
        if (null == historyInputVO.getVersionId()) {
            throw new CommonApplicationException("入参清单数据版本为空");
        }
        DmFocDataRefreshStatus dataRefreshStatus = new DmFocDataRefreshStatus();
        dataRefreshStatus.setStatus("TASK_INIT");
        dataRefreshStatus.setDelFlag("N");
        Long userId = UserInfoUtils.getUserId();
        dataRefreshStatus.setCreatedBy(userId);
        dataRefreshStatus.setLastUpdatedBy(userId);
        dataRefreshStatus.setCreationDate(new Date());
        dataRefreshStatus.setTaskFlag("CONFIG_" + historyInputVO.getIndustryOrg() + "_" + historyInputVO.getCostType() + "_EXPORT_" + historyInputVO.getCaliberFlag() + "_" + historyInputVO.getModelType());
        dataRefreshStatus.setTaskId(dataRefreshStatusDao.getDataRefrashKey());
        dataRefreshStatus.setRoleId(UserInfoUtils.getRoleId());
        // 创建异步任务记录，进行新增操作
        dataRefreshStatusDao.createDmFocDataRefreshStatus(dataRefreshStatus);
        IRequestContext current = RequestContextManager.getCurrent();
        // 异步导出数据
        asyncService.fillMadeExportData(historyInputVO, dataRefreshStatus, current);
        return ResultDataVO.success(dataRefreshStatus);
    }
}
