/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.provider.annual;

import com.huawei.it.fcst.industry.pbi.dao.IAnnualAmpPbiDao;
import com.huawei.it.fcst.industry.pbi.dao.IAnnualCustomDao;
import com.huawei.it.fcst.industry.pbi.impl.annual.AnnualAmpPbiService;
import com.huawei.it.fcst.export.IExcelExportDataProvider;
import com.huawei.it.fcst.industry.pbi.vo.annual.AnnualAnalysisVO;
import com.huawei.it.fcst.industry.pbi.vo.annual.DmFocAnnualAmpVO;
import com.huawei.it.fcst.export.vo.ExcelExportContext;
import com.huawei.it.jalor5.core.base.PageVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Inject;
import javax.inject.Named;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AnnualDistributeExportDataProvider Class
 *
 * <AUTHOR>
 * @since 2024/7/8
 */

@Named("IExcelExport.AnnualDistributeExportDataProvider")
public class AnnualDistributeExportDataProvider implements IExcelExportDataProvider {

    @Inject
    private IAnnualCustomDao annualCustomDao;

    @Inject
    private IAnnualAmpPbiDao annualAmpPbiDao;

    @Autowired
    private AnnualAmpPbiService annualAmpPbiService;

    @Override
    public List<?> getData(Serializable conditionObject, PageVO pageVO) {
        AnnualAnalysisVO annualVO = (AnnualAnalysisVO) conditionObject;
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        BeanUtils.copyProperties(annualVO, annualAnalysisVO);

        List<DmFocAnnualAmpVO> dmFocDistributeCostList = new ArrayList<>();
        if (annualAnalysisVO.getIsNeedBlur()) {
            // 虚化
            dmFocDistributeCostList = annualCustomDao.distributeCustomChart(annualAnalysisVO);
        }
        if (CollectionUtils.isNotEmpty(annualAnalysisVO.getGroupCodeList())) {
            dmFocDistributeCostList.addAll(annualAmpPbiDao.distributePbiCostChart(annualAnalysisVO));
            annualAmpPbiService.setGroupCnNameDimensionLevel(dmFocDistributeCostList);
        }
        if (annualAnalysisVO.getIsContainComb()) {
            dmFocDistributeCostList.addAll(annualCustomDao.distributeSummaryCustomChart(annualAnalysisVO));
        }
        return dmFocDistributeCostList;
    }

    @Override
    public Map<String, Object> getHeader(Serializable conditionObject, Map<String, Object> parameters) {
        ExcelExportContext context = (ExcelExportContext) conditionObject;
        AnnualAnalysisVO annualParamVO = (AnnualAnalysisVO) context.getConditionObject();

        // 自定义表头
        Map<String, Object> headMap = new HashMap<>();
        headMap.put("displayName", annualParamVO.getDisplayName());
        headMap.put("name", annualParamVO.getName());
        headMap.put("costTypeCnName", annualParamVO.getCostTypeCnName());
        headMap.put("granularityTypeCnName", annualParamVO.getGranularityTypeCnName());
        headMap.put("softwareMarkCnName", annualParamVO.getSoftwareMarkCnName());
        headMap.put("overseaFlagCnName", annualParamVO.getOverseaFlagCnName());
        headMap.put("bgCnName", annualParamVO.getBgCnName());
        headMap.put("actualMonth", annualParamVO.getActualMonth());
        headMap.put("regionCnName", annualParamVO.getRegionCnName());
        headMap.put("repofficeCnName", annualParamVO.getRepofficeCnName());
        headMap.put("mainFlagCnName", annualParamVO.getMainFlagCnName());
        headMap.put("codeAttributesCnName", annualParamVO.getCodeAttributesCnName());
        headMap.put("ytdFlagCnName", annualParamVO.getYtdFlagCnName());
        return headMap;
    }
}
