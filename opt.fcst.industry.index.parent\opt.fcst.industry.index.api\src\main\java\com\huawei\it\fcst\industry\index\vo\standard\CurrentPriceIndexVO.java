/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.standard;

import com.huawei.it.fcst.annotations.ExportAttribute;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * CurrentPriceIndexVO Class
 *
 * <AUTHOR>
 * @since 2024/11/1
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CurrentPriceIndexVO {

    @ExportAttribute(sort = 0)
    private Long periodId;

    @ExportAttribute(sort = 1)
    private String costType;

    @ExportAttribute(sort = 2)
    private String groupCnName;

    @ExportAttribute(sort = 3)
    private String costIndex;
}
