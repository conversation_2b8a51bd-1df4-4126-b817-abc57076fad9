/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.drop;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * DmFcstDimInfoVO Class
 *
 * <AUTHOR>
 * @since 2024/7/1
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DmFcstBaseCusDimVO implements Serializable {
    private static final long serialVersionUID = -3527116743977395982L;

    private Long createdBy;

    private Timestamp creationDate;

    private Long lastUpdatedBy;

    private Timestamp lastUpdatedate;

    private String bgCode;

    private String bgCnName;

    private String delFlag;

    private String viewFlag;

    private Long versionId;

    private String dimensionCode;

    private String dimensionCnName;

    private String dimensionSubCategoryCode;

    private String dimensionSubCategoryCnName;

    private String dimensionSubDetailCode;

    private String dimensionSubDetailCnName;

    private String spartCode;

    private String spartCnName;

    private String newSpartCode;

    private String oldSpartCode;

    private String groupCode;

    private String groupLevel;

    private String parentLevel;

    private String groupCnName;

    /**
     * 组合id
     */
    private Long customId;

    private String customCnName;

    private String regionCode;

    private String regionCnName;

    private String repofficeCode;

    private String repofficeCnName;

    private String mainFlag;

    private String codeAttributes;

    private String lvCode;

    private String lvCnName;

    private String granularityType;

    /**
     *  成本类型
     */
    private String costType;

    /**
     *  国内海外标识(N:国内/Y:海外/G:全球)
     */
    private String overseaFlag;

    private String pageType;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 角色id
     */
    private String roleId;

    private String statusFlag;

    private String codeType;

    private String relationType;

    private String replaceRelationType;

    private String replaceRelationName;

    private String tableName;

    private String softwareMark;

    private String ytdFlag;

}
