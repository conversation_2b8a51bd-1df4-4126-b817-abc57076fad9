/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.provider.month;

import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.industry.pbi.dao.IctMonthCostIdxDao;
import com.huawei.it.fcst.industry.pbi.enums.IndustryConstEnum;
import com.huawei.it.fcst.export.IExcelExportDataProvider;
import com.huawei.it.fcst.industry.pbi.service.month.IIctMonthAnalysisService;
import com.huawei.it.fcst.industry.pbi.utils.FcstIndustryUtil;
import com.huawei.it.fcst.export.vo.ExcelExportContext;
import com.huawei.it.fcst.export.vo.ExportList;
import com.huawei.it.fcst.industry.pbi.vo.month.IctMonthAnalysisVO;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import javax.inject.Inject;
import javax.inject.Named;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 成本分部图导出数据提供类
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
@Slf4j
@Named("IExcelExport.CostDistributionChartExpDataProvider")
public class CostDistributionChartExpDataProvider implements IExcelExportDataProvider {

    @Inject
    private IIctMonthAnalysisService monthAnalysisService;

    @Inject
    private IctMonthCostIdxDao monthCostIdxDao;

    @Override
    public List<?> getData(Serializable conditionObject, PageVO pageVO) throws ApplicationException {
        log.info(">>>Begin CostDistributionChartExpDataProvider::getData");
        IctMonthAnalysisVO monthAnalysisVO = (IctMonthAnalysisVO)conditionObject;
        IctMonthAnalysisVO paramsVO = new IctMonthAnalysisVO();
        BeanUtils.copyProperties(monthAnalysisVO, paramsVO);
        ResultDataVO resultDataVO = monthAnalysisService.getCostDistributionChart(paramsVO);
        List<IctMonthAnalysisVO> dataList = (List<IctMonthAnalysisVO>) resultDataVO.getData();
        dataList = Optional.ofNullable(dataList).orElse(new ArrayList<>());
        ExportList exportList = new ExportList();
        exportList.addAll(dataList);
        exportList.setTotalRows(dataList.size());
        return exportList;
    }

    @Override
    public Map<String, Object> getHeader(Serializable conditionObject, Map<String, Object> parameters) throws ApplicationException {
        ExcelExportContext context = (ExcelExportContext) conditionObject;
        IctMonthAnalysisVO monthAnalysisVO = (IctMonthAnalysisVO) context.getConditionObject();
        // 自定义表头
        Map<String, Object> headMap = new HashMap<>();
        headMap.put("basePeriodId", FcstIndustryUtil.getActualMonth(monthAnalysisVO.getBasePeriodId().toString()));
        headMap.put("displayName", monthAnalysisVO.getDisplayName());
        headMap.put("groupCnName", monthAnalysisVO.getGroupCnName());
        headMap.put("costType", monthAnalysisVO.getCostType());
        headMap.put("actualMonth", FcstIndustryUtil.getActualMonth(monthCostIdxDao.findActualMonth(monthAnalysisVO)));
        headMap.put("granularityTypeCnName", monthAnalysisVO.getGranularityTypeCnName());
        headMap.put("overseaFlagCnName", monthAnalysisVO.getOverseaFlagCnName());
        headMap.put("bgCnName", monthAnalysisVO.getBgCnName());
        headMap.put("regionCnName", monthAnalysisVO.getRegionCnName());
        headMap.put("repofficeCnName", monthAnalysisVO.getRepofficeCnName());
        headMap.put("mainFlagCnName", monthAnalysisVO.getMainFlagCnName());
        headMap.put("codeAttributes", monthAnalysisVO.getCodeAttributes());
        headMap.put("softwareMarkStr", "PSP".equals(monthAnalysisVO.getCostType()) ? CommonConstant.SOFTWARE_MARK + IndustryConstEnum.getSoftwareMark(monthAnalysisVO.getSoftwareMark()).getDesc() : "");
        return headMap;
    }
}