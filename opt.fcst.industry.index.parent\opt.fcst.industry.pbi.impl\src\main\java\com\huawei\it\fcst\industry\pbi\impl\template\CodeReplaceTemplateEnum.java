/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.template;

import com.huawei.it.fcst.export.IExcelTemplateBeanManager;
import com.huawei.it.fcst.export.vo.ExcelTemplateBeanManager;
import com.huawei.it.fcst.export.vo.SheetBeanMetaVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import lombok.Getter;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * 定义月度导出模板
 *
 * <AUTHOR>
 * @since 2024/06
 */
@Getter
public enum CodeReplaceTemplateEnum implements IExcelTemplateBeanManager {
    REPLACE_01("01", "codeReplacementTemplate", "产业成本指数-编码替代", "编码替代-数据导出") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            ExcelTemplateBeanManager excelTemplateBeanManager = new ExcelTemplateBeanManager();
            List<SheetBeanMetaVO> replaceL1 = new ArrayList<>();
            replaceL1.add(
                new SheetBeanMetaVO(REPLACE_01.templateName, 0, "CodeReplaceIndexDataProvider", "1.新|老编码成本指数图（月度）",
                    Boolean.FALSE));
            replaceL1.add(
                new SheetBeanMetaVO(REPLACE_01.templateName, 1, "CodeReplaceIndexAccDataProvider", "2.新|老编码发货量分布图（月度累计）",
                    Boolean.FALSE));
            replaceL1.add(new SheetBeanMetaVO(REPLACE_01.templateName, 2, "CodeReplaceQtyDataProvider", "3.新|老编码成本偏差（月度）",
                Boolean.FALSE));
            replaceL1.add(new SheetBeanMetaVO(REPLACE_01.templateName, 3, "CodeReplaceAccQtyDataProvider", "4.新|老编码成本偏差（月度累计）",
                Boolean.FALSE));
            replaceL1.add(new SheetBeanMetaVO(REPLACE_01.templateName, 4, "CodeReplaceCVDataProvider", "5.新|老编码成本偏差",
                Boolean.FALSE));
            excelTemplateBeanManager.setSheetBeans(replaceL1);
            excelTemplateBeanManager.setTemplateName(REPLACE_01.templateName);
            excelTemplateBeanManager.setModuleType(REPLACE_01.moduleType);
            excelTemplateBeanManager.setDesc(REPLACE_01.desc);
            return excelTemplateBeanManager;
        }
    },
    REPLACE_02("02", "codeReplacementTemplate2", "产业成本指数-编码替代", "编码替代-数据导出"){
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            ExcelTemplateBeanManager excelTemplateBeanManager = new ExcelTemplateBeanManager();
            excelTemplateBeanManager.setTemplateName(REPLACE_02.templateName);
            excelTemplateBeanManager.setModuleType(REPLACE_02.moduleType);
            List<SheetBeanMetaVO> replaceL1 = new ArrayList<>();
            replaceL1.add(new SheetBeanMetaVO(REPLACE_02.templateName, 0, "CodeReplaceIndexDataProvider", "1.新|老编码成本指数图（月度）",Boolean.FALSE));
            replaceL1.add(new SheetBeanMetaVO(REPLACE_02.templateName, 1, "CodeReplaceIndexAccDataProvider", "2.新|老编码成本指数图（月度累计）",Boolean.FALSE));
            excelTemplateBeanManager.setDesc(REPLACE_02.desc);
            replaceL1.add(new SheetBeanMetaVO(REPLACE_02.templateName, 2, "CodeReplaceQtyDataProvider", "3.新|老编码发货量分布图（月度）",Boolean.FALSE));
            replaceL1.add(new SheetBeanMetaVO(REPLACE_02.templateName, 3, "CodeReplaceAccQtyDataProvider", "4.新|老编码发货量分布图（月度累计）",Boolean.FALSE));
            replaceL1.add(new SheetBeanMetaVO(REPLACE_02.templateName, 4, "CodeReplaceCVDataProvider", "5.新|老编码成本偏差", Boolean.FALSE));
            excelTemplateBeanManager.setSheetBeans(replaceL1);
            return excelTemplateBeanManager;
        }
    },

    REPLACE_03("03", "codeReplacementTemplate3", "产业成本指数-编码替代", "编码替代-数据导出") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            ExcelTemplateBeanManager excelTemplateBeanManager = new ExcelTemplateBeanManager();
            List<SheetBeanMetaVO> replaceL1 = new ArrayList<>();
            replaceL1.add(
                    new SheetBeanMetaVO(REPLACE_03.templateName, 0, "CodeReplaceIndexDataProvider", "1.新|老编码成本指数图（月度）",
                            Boolean.FALSE));
            replaceL1.add(
                    new SheetBeanMetaVO(REPLACE_03.templateName, 1, "CodeReplaceIndexAccDataProvider", "2.新|老编码发货量分布图（月度累计）",
                            Boolean.FALSE));
            replaceL1.add(new SheetBeanMetaVO(REPLACE_03.templateName, 2, "CodeReplaceQtyDataProvider", "3.新|老编码成本偏差（月度）",
                    Boolean.FALSE));
            replaceL1.add(new SheetBeanMetaVO(REPLACE_03.templateName, 3, "CodeReplaceAccQtyDataProvider", "4.新|老编码成本偏差（月度累计）",
                    Boolean.FALSE));
            replaceL1.add(new SheetBeanMetaVO(REPLACE_03.templateName, 4, "CodeReplaceCVDataProvider", "5.新|老编码成本偏差",
                    Boolean.FALSE));
            excelTemplateBeanManager.setSheetBeans(replaceL1);
            excelTemplateBeanManager.setTemplateName(REPLACE_03.templateName);
            excelTemplateBeanManager.setModuleType(REPLACE_03.moduleType);
            excelTemplateBeanManager.setDesc(REPLACE_03.desc);
            return excelTemplateBeanManager;
        }
    },
    REPLACE_04("04", "codeReplacementTemplate4", "产业成本指数-编码替代", "编码替代-数据导出"){
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            ExcelTemplateBeanManager excelTemplateBeanManager = new ExcelTemplateBeanManager();
            excelTemplateBeanManager.setTemplateName(REPLACE_04.templateName);
            excelTemplateBeanManager.setModuleType(REPLACE_04.moduleType);
            List<SheetBeanMetaVO> replaceL1 = new ArrayList<>();
            replaceL1.add(new SheetBeanMetaVO(REPLACE_04.templateName, 0, "CodeReplaceIndexDataProvider", "1.新|老编码成本指数图（月度）",Boolean.FALSE));
            replaceL1.add(new SheetBeanMetaVO(REPLACE_04.templateName, 1, "CodeReplaceIndexAccDataProvider", "2.新|老编码成本指数图（月度累计）",Boolean.FALSE));
            excelTemplateBeanManager.setDesc(REPLACE_04.desc);
            replaceL1.add(new SheetBeanMetaVO(REPLACE_04.templateName, 2, "CodeReplaceQtyDataProvider", "3.新|老编码发货量分布图（月度）",Boolean.FALSE));
            replaceL1.add(new SheetBeanMetaVO(REPLACE_04.templateName, 3, "CodeReplaceAccQtyDataProvider", "4.新|老编码发货量分布图（月度累计）",Boolean.FALSE));
            replaceL1.add(new SheetBeanMetaVO(REPLACE_04.templateName, 4, "CodeReplaceCVDataProvider", "5.新|老编码成本偏差", Boolean.FALSE));
            excelTemplateBeanManager.setSheetBeans(replaceL1);
            return excelTemplateBeanManager;
        }
    };

    private String code;

    private String templateName;

    private String moduleType;

    private String desc;

    CodeReplaceTemplateEnum(String code, String templateName, String moduleType, String desc) {
        this.code = code;
        this.templateName = templateName;
        this.moduleType = moduleType;
        this.desc = desc;
    }

    static final String NAME = "REPLACE";

    static final String SUB_LEVEL_CODE = "{0}_{1}";

    /**
     * 获取模板信息
     *
     * @param levelCode 层级code
     * @param roleName 角色名称
     * @return 枚举信息
     * @throws CommonApplicationException
     */
    public static CodeReplaceTemplateEnum getByCode(String levelCode, String roleName)
        throws CommonApplicationException {
        String key = MessageFormat.format(SUB_LEVEL_CODE, NAME, levelCode);
        for (CodeReplaceTemplateEnum value : CodeReplaceTemplateEnum.values()) {
            if (value.name().equalsIgnoreCase(key)) {
                return value;
            }
        }
        throw new CommonApplicationException("Check the template definition relationship.");
    }
}
