/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.ciphertext;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huawei.it.fcst.industry.index.vo.common.TableNameVO;
import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;

/**
 * VarifyTaskVO Class
 *
 * <AUTHOR>
 * @since 2023/7/10
 */
@Getter
@Setter
public class VarifyTaskVO extends TableNameVO {

    private Long taskId;

    private String status;

    private Long periodId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS",timezone = "GMT+8")
    private Timestamp lastUpdateDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS",timezone = "GMT+8")
    private Timestamp creationDate;

    private String taskType;

    private String combStatus;

}
