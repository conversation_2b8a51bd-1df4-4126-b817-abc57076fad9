/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.service.common;

import com.huawei.it.fcst.industry.pbi.vo.comb.CommonViewVO;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * <AUTHOR>
 * @since 2023/11/21
 */
@Path("/compareAnalysis")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface IIctCompareAnalysisService {
    /**
     * 根据groupLevel查询对比分析树
     *
     * @param commonViewVO 参数
     * @return 结果
     */
    @Path("/summary/getDimensionTree")
    @POST
    ResultDataVO getDimensionList(CommonViewVO commonViewVO) throws CommonApplicationException;


}
