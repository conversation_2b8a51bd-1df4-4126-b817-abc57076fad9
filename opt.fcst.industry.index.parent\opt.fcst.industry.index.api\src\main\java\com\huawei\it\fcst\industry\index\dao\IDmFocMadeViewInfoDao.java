/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.annual.AnnualAnalysisVO;
import com.huawei.it.fcst.industry.index.vo.common.CommonViewVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;

import java.util.List;

/**
 * IDmFocViewInfoDao
 *
 * <AUTHOR>
 * @since 2023/3/14
 */
public interface IDmFocMadeViewInfoDao {

    List<DmFocViewInfoVO> madeReverseFindLv1ProdCode(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> madeReverseFindLv1ProdCodeMonth(CommonViewVO commonViewVO);
    
    List<DmFocViewInfoVO> madeViewInfoList(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> madeViewInfoListForMonth(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> madeObjectViewInfoList(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> madeViewFlagInfoList(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> madeViewInfoKeyWordList(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> madeViewInfoKeyWordForMonth(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> madeObjectViewInfoKeyWord(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> getMadeLv0ProdList(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> getMadeCombinationSubByGroupLevel(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> getMadeCombinationByGroupLevel(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> getMadeCombinationParent(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> getMadeAllCombItemCode(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocViewInfoVO> getMadeAllNormalItemCode(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocViewInfoVO> getMadeAllItemCode(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocViewInfoVO> madeRevViewInfoList(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> madeRevViewInfoListForMonth(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> madeObjectRevViewInfoList(CommonViewVO commonViewVO);
}
