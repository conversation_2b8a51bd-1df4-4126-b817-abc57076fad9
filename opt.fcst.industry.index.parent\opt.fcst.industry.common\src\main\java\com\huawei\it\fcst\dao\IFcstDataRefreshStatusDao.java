/*
 *
 *  Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 *
 */

package com.huawei.it.fcst.dao;

import com.huawei.it.fcst.vo.DmFcstDataRefreshStatus;

import java.util.List;

/**
 * The DAO to access DmHqPlanDataRefreshStatus entity
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2022-07-30 11:04:57
 */
public interface IFcstDataRefreshStatusDao {

    Long getDataRefrashKey();

    /**
     * Find DmHqPlanDataRefreshStatus by id.
     *
     * @param dataRefreshStatus is id
     * @return DmHqPlanDataRefreshStatus
     */
    DmFcstDataRefreshStatus findDmFcstDataRefreshStatusById(DmFcstDataRefreshStatus dataRefreshStatus);

    /**
     * Find DmHqPlanVersionReleaseResult records.
     *
     * @return list
     */
    DmFcstDataRefreshStatus findDmFcstDataRefreshStatus(DmFcstDataRefreshStatus dmHqPlanDataRefreshStatus);

    List<DmFcstDataRefreshStatus> findMonthDataRefreshStatusList(DmFcstDataRefreshStatus dmHqPlanDataRefreshStatus);


    /**
     * Insert a new DmHqPlanDataRefreshStatus record.
     *
     * @param dmHqPlanDataRefreshStatus is
     * @return int
     */
    int createDmFcstDataRefreshStatus(DmFcstDataRefreshStatus dmHqPlanDataRefreshStatus);

    /**
     * Update an existed  DmHqPlanDataRefreshStatus record.
     *
     * @param dmHqPlanDataRefreshStatus is
     * @return int
     */
    int updateDmFcstDataRefreshStatus(DmFcstDataRefreshStatus dmHqPlanDataRefreshStatus);

}
