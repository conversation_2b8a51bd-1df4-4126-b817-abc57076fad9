/*
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.month;


import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @since 2023/5/9
 */
public class DmFocMonthCostIdxVOTest extends BaseVOCoverUtilsTest<DmFocMonthCostIdxVO> {
    @Override
    protected Class<DmFocMonthCostIdxVO> getTClass() { return DmFocMonthCostIdxVO.class; }

    @Test
    public void testMethod() {
        DmFocMonthCostIdxVO dmFocActualCostVO = new DmFocMonthCostIdxVO();
        dmFocActualCostVO.setDelFlag("Y");
        dmFocActualCostVO.getDelFlag();
        dmFocActualCostVO.setCreatedBy("1175");
        dmFocActualCostVO.getCreatedBy();
        dmFocActualCostVO.getCreationDate();
        dmFocActualCostVO.setGroupCode("1163A");
        dmFocActualCostVO.getGroupCode();
        dmFocActualCostVO.setVersionId(11L);
        dmFocActualCostVO.getVersionId();
        dmFocActualCostVO.setGroupCnName("元器");
        dmFocActualCostVO.getGroupCnName();
        dmFocActualCostVO.setGroupLevel("LV3");
        dmFocActualCostVO.getGroupLevel();
        dmFocActualCostVO.setId(12L);
        dmFocActualCostVO.getId();
        dmFocActualCostVO.setViewFlag("0");
        dmFocActualCostVO.getViewFlag();
        dmFocActualCostVO.setPeriodId(13L);
        dmFocActualCostVO.getPeriodId();
        dmFocActualCostVO.setParentCode("54211");
        dmFocActualCostVO.getParentCode();
        dmFocActualCostVO.setPeriodYear(2023L);
        dmFocActualCostVO.getPeriodYear();
        dmFocActualCostVO.getLastUpdateDate();
        dmFocActualCostVO.setLastUpdatedBy("166");
        dmFocActualCostVO.getLastUpdatedBy();
        dmFocActualCostVO.setProdRndTeamCode("code");
        dmFocActualCostVO.getProdRndTeamCode();
        dmFocActualCostVO.setBasePeriodId(15L);
        dmFocActualCostVO.getBasePeriodId();
        dmFocActualCostVO.setProdRndTeamCnName("test");
        dmFocActualCostVO.getProdRndTeamCnName();
        dmFocActualCostVO.setCostIndex(45D);
        dmFocActualCostVO.getCostIndex();
        dmFocActualCostVO.setAppendFlag("X");
        dmFocActualCostVO.getAppendFlag();
        dmFocActualCostVO.setWeightPercent("11");
        dmFocActualCostVO.getWeightPercent();
        dmFocActualCostVO.setWeightRate(11.2D);
        dmFocActualCostVO.getWeightRate();
        dmFocActualCostVO.setScenarioFlag("Y");
        dmFocActualCostVO.getScenarioFlag();
        dmFocActualCostVO.getCostType();
        dmFocActualCostVO.setCostType("11");
        dmFocActualCostVO.getCustomCnName();
        dmFocActualCostVO.getCustomId();
        dmFocActualCostVO.getDmsCode();
        dmFocActualCostVO.getDmsCnName();
        dmFocActualCostVO.getIsContainComb();
        dmFocActualCostVO.getPurCode();
        dmFocActualCostVO.getPurCnName();
        dmFocActualCostVO.setDimensionSubDetailCnName("2445");
        dmFocActualCostVO.getDimensionSubDetailCnName();
        Assert.assertNotNull(dmFocActualCostVO);
    }
}