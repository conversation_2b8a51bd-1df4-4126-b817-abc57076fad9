/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.comb;

import com.huawei.it.fcst.config.ConfigUtil;
import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.industry.pbi.dao.IDmCustomDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmCustomTempDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstDataRefreshStatusDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstDimInfoDao;
import com.huawei.it.fcst.industry.pbi.enums.GroupLevelEnum;
import com.huawei.it.fcst.industry.pbi.enums.IndustryConstEnum;
import com.huawei.it.fcst.industry.pbi.impl.common.AsyncIctQueryService;
import com.huawei.it.fcst.industry.pbi.impl.common.DataPermissionService;
import com.huawei.it.fcst.industry.pbi.impl.common.IctCommonService;
import com.huawei.it.fcst.industry.pbi.service.comb.IIctCustomCombService;
import com.huawei.it.fcst.industry.pbi.utils.FcstIndustryUtil;
import com.huawei.it.fcst.industry.pbi.vo.comb.CombTransformVO;
import com.huawei.it.fcst.industry.pbi.vo.comb.CommonViewVO;
import com.huawei.it.fcst.industry.pbi.vo.common.DataPermissionsVO;
import com.huawei.it.fcst.industry.pbi.vo.common.DmFcstDataRefreshStatus;
import com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstDimInfoVO;
import com.huawei.it.fcst.util.ObjectCopyUtil;
import com.huawei.it.fcst.util.UserInfoUtils;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.annotation.NoJalorTransation;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.security.RoleVO;
import com.huawei.it.jalor5.security.UserVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Named;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * IctCustomCombService Class
 *
 * <AUTHOR>
 * @since 2024/9/13
 */
@Named("ictCustomCombService")
@JalorResource(code = "ictCustomCombService", desc = "new ICT-汇总组合页面")
public class IctCustomCombService implements IIctCustomCombService {

    @Autowired
    private DataPermissionService dataPermissionService;

    @Autowired
    private IctCommonService commonService;

    @Autowired
    private IctCustomCommonService ictCustomCommonService;

    @Autowired
    private IDmCustomDao dmCustomDao;

    @Autowired
    private IDmFcstDataRefreshStatusDao dataRefreshStatusDao;

    @Autowired
    private AsyncIctQueryService asyncIctQueryService;

    @Autowired
    private IDmCustomTempDao dmCustomTempDao;

    @Autowired
    private IDmFcstDimInfoDao dmFcstDimInfoDao;

    private static final String ENABLE_FLAG_Y = "Y";

    private static final String INIT = "TASK_INIT";

    private static final String IS_NOT = "N";

    private static final String ICT_COMB = "ICT_COMB";

    private static final String ICT_INIT_COMB = "ICT_INIT_COMB";

    @Override
    @Transactional(rollbackFor = Exception.class)
    @JalorOperation(code = "createCustom", desc = "新增汇总组合")
    @Audit(module = "ictCustomCombService-createCustom", operation = "createCustom",
            message = "新增汇总组合")
    public ResultDataVO createCustom(CommonViewVO commonViewVO) throws CommonApplicationException {
        FcstIndustryUtil.checkTablePreFixParam(commonViewVO);
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        long userId = currentUser.getUserId();
        // 首字母小写WX大写
        String userAccount = currentUser.getUserAccount();
        RoleVO currentRole = currentUser.getCurrentRole();
        String roleId = String.valueOf(currentRole.getRoleId());
        commonViewVO.setRoleId(roleId);
        commonViewVO.setUserAccount(userAccount);
        commonViewVO.setUserId(String.valueOf(userId));

        // 查询汇总组合名称是否重复
        Long countByName = dmCustomDao.getCustomCombCountByName(commonViewVO);
        if (countByName > 0) {
            throw new CommonApplicationException("组合名称已存在");
        }
        // 根据临时表id获取列表
        List<DmFcstDimInfoVO> customList = dmCustomTempDao.getTempCustomList(commonViewVO);

        List<DmFcstDimInfoVO> otherCustomVOList = new ArrayList<>();

        if (commonViewVO.getPageFlag().contains(CommonConstant.PAGE_FLAG_ALL)) {
            List<DmFcstDimInfoVO> customOneList = new ArrayList<>();
            customOneList.addAll(customList);
            // 如果同步，需要查询出另一个页面的code，两个页面code不一致
            commonViewVO.setExpandFlag("Y");
            otherCustomVOList = ictCustomCommonService.filterAnotherPageData(commonViewVO, customOneList);
        }
        // 获取序列
        Timestamp time = new Timestamp(System.currentTimeMillis());
        Long customId = Long.parseLong(new SimpleDateFormat("yyyyMMddHHmmsss").format(time));

        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        // 组装当前传入的数据，需要把数据组需要的数据都组装起来

        List<DmFcstDimInfoVO> oneCustomList = getCurrentPageCombList(commonViewVO, userAccount, roleId, customList, customId, timestamp);
        // 组装另一个页面code
        List<DmFcstDimInfoVO> otherDimInfoVOList = combineOtherPageCode(commonViewVO, userAccount, roleId, otherCustomVOList, customId, timestamp);

        DmFcstDataRefreshStatus dataRefreshStatus = new DmFcstDataRefreshStatus();
        dataRefreshStatus.setStatus(INIT);
        dataRefreshStatus.setDelFlag(IS_NOT);
        dataRefreshStatus.setCreatedBy(userId);
        dataRefreshStatus.setTaskFlag(ICT_COMB + "_" + commonViewVO.getPageFlag());
        dataRefreshStatus.setLastUpdatedBy(userId);
        dataRefreshStatus.setCreationDate(new Date());
        dataRefreshStatus.setTaskId(dataRefreshStatusDao.getDataRefrashKey());
        dataRefreshStatus.setRoleId(currentRole.getRoleId());
        // 创建异步任务记录，进行新增操作
        dataRefreshStatusDao.createDmFcstDataRefreshStatus(dataRefreshStatus);
        CombTransformVO combTransformVO = getCombTransformParam(commonViewVO, userId, customId, dataRefreshStatus);
        asyncIctQueryService.asyncCreateComb(oneCustomList, combTransformVO, otherDimInfoVOList);
        // 进行新增操作
        return ResultDataVO.success(dataRefreshStatus);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @JalorOperation(code = "renameCustom", desc = "汇总组合重命名")
    @Audit(module = "ictCustomCombService-renameCustom", operation = "renameCustom",
            message = "汇总组合重命名")
    public ResultDataVO renameCustom(CommonViewVO commonViewVO) throws CommonApplicationException {
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        String userAccount = currentUser.getUserAccount();
        int roleId = currentRole.getRoleId();
        Long userId = UserInfoUtils.getUserId();
        commonViewVO.setRoleId(String.valueOf(roleId));
        commonViewVO.setUserAccount(userAccount);
        // 查询所有汇总组合名称
        Long countByName = dmCustomDao.getCustomCombCountByName(commonViewVO);
        if (countByName > 0) {
            throw new CommonApplicationException("组合名称已存在");
        }
        // 如果数据不是同步的
        if (!commonViewVO.getPageFlag().contains(CommonConstant.PAGE_FLAG_ALL)) {
            // 更新原pageFlag和名称
            dmCustomDao.renameCustomCombination(commonViewVO);
        } else {
            // 如果数据是同步,更新原pageFlag和名称
            dmCustomDao.renameCustomCombination(commonViewVO);
            // 原数据也是同步的,更新另一个页面的pageFlag和名称
            commonViewVO.setPageFlag("ALL_" + CommonConstant.allCombPageFlag.get(commonViewVO.getPageSymbol()));
            dmCustomDao.renameCustomCombination(commonViewVO);
        }
        DmFcstDataRefreshStatus dataRefreshStatus = new DmFcstDataRefreshStatus();
        dataRefreshStatus.setStatus(INIT);
        dataRefreshStatus.setDelFlag(IS_NOT);
        dataRefreshStatus.setCreatedBy(userId);
        dataRefreshStatus.setLastUpdatedBy(userId);
        dataRefreshStatus.setCreationDate(new Date());
        dataRefreshStatus.setTaskFlag(ICT_COMB + "_" + commonViewVO.getPageFlag());
        dataRefreshStatus.setTaskId(dataRefreshStatusDao.getDataRefrashKey());
        dataRefreshStatus.setRoleId(currentRole.getRoleId());
        // 创建异步任务记录，进行新增操作
        dataRefreshStatusDao.createDmFcstDataRefreshStatus(dataRefreshStatus);
        // 调用函数刷新数据
        CombTransformVO combTransformVO = new CombTransformVO();
        callFunctionRefreshData(commonViewVO, userId, dataRefreshStatus, combTransformVO);
        asyncIctQueryService.asyncCustomCombRename(combTransformVO);
        return ResultDataVO.success(dataRefreshStatus);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @JalorOperation(code = "updateCustom", desc = "编辑汇总组合")
    @Audit(module = "ictCustomCombService-updateCustom", operation = "updateCustom",
            message = "编辑汇总组合")
    public ResultDataVO updateCustom(CommonViewVO commonViewVO) throws CommonApplicationException {
        FcstIndustryUtil.checkTablePreFixParam(commonViewVO);
        Long userLongId = UserInfoUtils.getUserId();
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        String userAccount = currentUser.getUserAccount();

        String roleId = String.valueOf(currentRole.getRoleId());
        commonViewVO.setUserId(String.valueOf(userLongId));
        commonViewVO.setUserAccount(userAccount);
        commonViewVO.setRoleId(roleId);

        DmFcstDataRefreshStatus dataRefreshStatus = new DmFcstDataRefreshStatus();
        dataRefreshStatus.setStatus(INIT);
        dataRefreshStatus.setDelFlag(IS_NOT);
        dataRefreshStatus.setCreatedBy(userLongId);
        dataRefreshStatus.setLastUpdatedBy(userLongId);
        dataRefreshStatus.setCreationDate(new Date());
        dataRefreshStatus.setTaskFlag(ICT_COMB + "_" + commonViewVO.getPageFlag());
        dataRefreshStatus.setTaskId(dataRefreshStatusDao.getDataRefrashKey());
        dataRefreshStatus.setRoleId(currentRole.getRoleId());
        // 创建异步任务记录，进行新增操作
        dataRefreshStatusDao.createDmFcstDataRefreshStatus(dataRefreshStatus);
        // 数据范围
        getPermissionList(commonViewVO);
        // 调用函数刷新数据
        CombTransformVO combTransformVO = new CombTransformVO();
        callFunctionRefreshData(commonViewVO, userLongId, dataRefreshStatus, combTransformVO);
        asyncIctQueryService.asyncCustomCombUpdate(commonViewVO, combTransformVO);
        return ResultDataVO.success(dataRefreshStatus);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @JalorOperation(code = "deleteCustom", desc = "删除汇总组合")
    @Audit(module = "ictCustomCombService-deleteCustom", operation = "deleteCustom",
            message = "删除汇总组合")
    public ResultDataVO deleteCustom(CommonViewVO commonViewVO) {
        // 如果选择不同步删除，那么只删除此页面的数据
        if (!commonViewVO.getPageFlag().contains(CommonConstant.PAGE_FLAG_ALL)) {
            // 未勾选同步
            dmCustomDao.deleteCustomCombList(commonViewVO);
            // 原数据不是同步的,直接删除原数据，并查看同个customId下是否另一个页面有数据，如果有，则需要更新另一个页面的isSeparate为N
            String otherPageFlag = CommonConstant.allCombPageFlag.get(commonViewVO.getPageFlag());
            commonViewVO.setPageFlag(otherPageFlag);
            commonViewVO.setIsSeparate("N");
            dmCustomDao.updateCustomCombSeparate(commonViewVO);
        } else {
            // 原数据是同步的，并勾选同步，那么两个页面数据都要删除
            dmCustomDao.deleteCustomCombList(commonViewVO);
            String otherPageFlag = CommonConstant.allCombPageFlag.get(commonViewVO.getPageFlag());
            commonViewVO.setOldPageFlag(otherPageFlag);
            dmCustomDao.deleteCustomCombList(commonViewVO);
        }
        return ResultDataVO.success();
    }

    @Override
    @JalorOperation(code = "getIctProdRndTeamTree", desc = "汇总组合左侧树查询")
    public ResultDataVO getIctProdRndTeamTree(CommonViewVO commonViewVO) throws CommonApplicationException {
        // 设置数据权限
        getPermissionList(commonViewVO);
        FcstIndustryUtil.checkTablePreFixParam(commonViewVO);
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        commonViewVO.setRoleId(String.valueOf(currentRole.getRoleId()));
        commonViewVO.setUserId(currentUser.getUserAccount());
        if (commonViewVO.getVersionId() == null) {
            // 获取年度version_id
            commonViewVO.setVersionId(commonService.getVersionId(IndustryConstEnum.PAGE_TYPE.ANNUAL.getValue()));
        }
        // 获取月度version_id
        commonViewVO.setMonVersionId(commonService.getVersionId(IndustryConstEnum.PAGE_TYPE.MONTH.getValue()));
        FcstIndustryUtil.setSpecailCode(commonViewVO);
        if (IndustryConstEnum.PAGE_TYPE.ANNUAL.getValue().equals(commonViewVO.getPageSymbol()) &&
                !CommonConstant.ANNUAL_NO_MAIN_FLAG.contains(commonViewVO.getGroupLevel()))  {
            if ("N".equals(commonViewVO.getMainFlag())) {
                commonViewVO.setMainFlag(null);
            }
        }
        List<DmFcstDimInfoVO> dmCustomCombList = getIctProdRndTeamCodeTree(commonViewVO);
        // 无权限的重量级团队设置标识
        List<DmFcstDimInfoVO> allGroupLevelConditionList = new ArrayList<>();
        setIctPermissionFlag(commonViewVO, dmCustomCombList, allGroupLevelConditionList);
        return ResultDataVO.success(dmCustomCombList);
    }

    @Override
    @JalorOperation(code = "getCustomCombList", desc = "根据汇总组合名称查询列表")
    public ResultDataVO getCustomCombList(CommonViewVO commonViewVO) {

        List<DmFcstDimInfoVO> allGroupCodeList = new ArrayList<>();
        // 没有传颗粒度，说明是空列表
        if (StringUtils.isEmpty(commonViewVO.getGranularityType())) {
            return ResultDataVO.success();
        }
        // 如果是展开查询，需要获取下一层级groupLevel
        findNextGroupLevel(commonViewVO);
        List<DmFcstDimInfoVO> dmCustomCombList = dmCustomDao.getCombinationCombList(commonViewVO);
        // 如果是初次查询，只查询重量级团队
        firstQueryList(commonViewVO, dmCustomCombList);
        // 获取最新的规格品version_id
        commonViewVO.setMonVersionId(commonService.getVersionId(IndustryConstEnum.PAGE_TYPE.MONTH.getValue()));
        // 获取组合的父层级
        List<DmFcstDimInfoVO> customCombList = new ArrayList<>();
        customCombList.addAll(dmCustomCombList);
        Map<String, List<DmFcstDimInfoVO>> groupLevelMap = customCombList.stream().collect(Collectors.groupingBy(DmFcstDimInfoVO::getGroupLevel));
        // 获取当前custom_id的父列表
        if (StringUtils.isBlank(commonViewVO.getLv0CnName())) {
            // 当非展开层级的时候，才需要去获取父层级
            List<DmFcstDimInfoVO> parentCodeList = getParentCodeList(commonViewVO, groupLevelMap, customCombList, true);
            Set<String> combList = dmCustomCombList.stream().map(DmFcstDimInfoVO::getConnectCode).collect(Collectors.toSet());
            Set<String> parentList = parentCodeList.stream().map(DmFcstDimInfoVO::getConnectCode).collect(Collectors.toSet());
            // 交集
            Set<String> intersectionList = combList.stream().filter(comb -> parentList.contains(comb)).collect(Collectors.toSet());
            allGroupCodeList.addAll(parentCodeList);
            if (CollectionUtils.isNotEmpty(intersectionList)) {
                allGroupCodeList.removeIf(all -> intersectionList.contains(all.getConnectCode()) && null == all.getCustomId());
            }
        }
        allGroupCodeList.addAll(dmCustomCombList);

        // 把父和子汇总后的list通过group_level层级，分层级展示
        List<DmFcstDimInfoVO> allGroupLevelConditionList = new ArrayList<>();
        Map<String, List<DmFcstDimInfoVO>> viewInfoMap = allGroupCodeList.stream().collect(Collectors.groupingBy(DmFcstDimInfoVO::getGroupLevel));

        allLevelCondition(viewInfoMap, allGroupLevelConditionList, commonViewVO);

        return ResultDataVO.success(allGroupLevelConditionList);
    }

    @Override
    @JalorOperation(code = "getCustomCombNameList", desc = "汇总组合名称下拉列表")
    public ResultDataVO getCustomCombNameList(CommonViewVO commonViewVO) {
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        commonViewVO.setRoleId(String.valueOf(currentRole.getRoleId()));
        commonViewVO.setUserId(UserInfoUtils.getCurrentUser().getUserAccount());
        // 查询下拉框汇总组合名称
        List<DmFcstDimInfoVO> customCombNameList = dmCustomDao.getCustomCombNameList(commonViewVO);
        return ResultDataVO.success(customCombNameList);
    }

    @Override
    @JalorOperation(code = "getIctGroupLevelList", desc = "获取groupLevel列表")
    public ResultDataVO getIctGroupLevelList(CommonViewVO commonViewVO) {
        // 获取数据权限
        getPermissionList(commonViewVO);
        List<String> groupLevelList = new ArrayList<>();
        getIctProdTeamLevel(groupLevelList, commonViewVO);
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        if (commonViewVO.getIsCompareFlag()) {
            if ("PROD_SPART".equals(commonViewVO.getViewFlag())) {
                if (!CommonConstant.SPECIAL_ROLES.contains(currentRole.getRoleName())) {
                    groupLevelList.add(GroupLevelEnum.SPART.getValue());
                }
            } else {
                groupLevelList.add(GroupLevelEnum.DIMENSION.getValue());
                groupLevelList.add(GroupLevelEnum.SUBCATEGORY.getValue());
                groupLevelList.add(GroupLevelEnum.SUB_DETAIL.getValue());
            }
        }
        return ResultDataVO.success(groupLevelList);
    }

    @Override
    @JalorOperation(code = "initIctEnableFlag", desc = "初始化部分失效和整体失效标识")
    @Audit(module = "ictCustomCombService-initIctEnableFlag", operation = "initIctEnableFlag",
            message = "初始化部分失效和整体失效标识")
    @NoJalorTransation
    public ResultDataVO initIctEnableFlag() {
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        int roleId = currentRole.getRoleId();
        String userIdStr = UserInfoUtils.getCurrentUser().getUserAccount();
        Long userId = UserInfoUtils.getUserId();

        DmFcstDataRefreshStatus refreshStatus = new DmFcstDataRefreshStatus();
        refreshStatus.setTaskFlag(ICT_INIT_COMB);
        refreshStatus.setUserId(userId);
        refreshStatus.setRoleId(roleId);
        // 判断是否已经有轮询任务，如果有直接返回
        DmFcstDataRefreshStatus dmFocDataRefreshStatus = dataRefreshStatusDao.findDmFcstDataRefreshStatus(refreshStatus);
        // 判断当天是否已经执行过任务了，如果执行过不再二次执行
        DmFcstDataRefreshStatus dmFocDataRefreshStatusByDay = dataRefreshStatusDao.findDataRefreshStatusByDay(refreshStatus);
        if (null != dmFocDataRefreshStatus || null != dmFocDataRefreshStatusByDay) {
            return ResultDataVO.success();
        }
        // 同个用户，同个角色，切换不同数据范围时，查询需要先判断是否整体失效，还是部分失效，更新标识到数据库中
        CommonViewVO commonViewVO = new CommonViewVO();

        commonViewVO.setRoleId(String.valueOf(roleId));
        commonViewVO.setUserId(userIdStr);
        commonViewVO.setCostType(IndustryConstEnum.COST_TYPE.PSP.getValue());
        List<DmFcstDimInfoVO> pspCombList = dmCustomDao.getCombinationCombList(commonViewVO);

        commonViewVO.setCostType(IndustryConstEnum.COST_TYPE.STD.getValue());
        List<DmFcstDimInfoVO> stdCombList = dmCustomDao.getCombinationCombList(commonViewVO);
        if (CollectionUtils.isEmpty(pspCombList) && CollectionUtils.isEmpty(stdCombList)) {
            return ResultDataVO.success();
        }
        commonViewVO.setVersionId(commonService.getVersionId(IndustryConstEnum.PAGE_TYPE.ANNUAL.getValue()));
        commonViewVO.setMonVersionId(commonService.getVersionId(IndustryConstEnum.PAGE_TYPE.MONTH.getValue()));

        DmFcstDataRefreshStatus dataRefreshStatus = new DmFcstDataRefreshStatus();
        dataRefreshStatus.setStatus(INIT);
        dataRefreshStatus.setDelFlag(IS_NOT);
        dataRefreshStatus.setCreatedBy(userId);
        dataRefreshStatus.setLastUpdatedBy(userId);
        dataRefreshStatus.setCreationDate(new Date());
        dataRefreshStatus.setTaskFlag(ICT_INIT_COMB);
        dataRefreshStatus.setTaskId(dataRefreshStatusDao.getDataRefrashKey());
        dataRefreshStatus.setRoleId(roleId);

        CombTransformVO combTransformVO = new CombTransformVO();
        combTransformVO.setUserId(userId);
        combTransformVO.setTaskId(dataRefreshStatus.getTaskId());

        // 创建异步任务记录，进行新增操作
        dataRefreshStatusDao.createDmFcstDataRefreshStatus(dataRefreshStatus);
        asyncIctQueryService.asyncInitCombData(pspCombList, stdCombList,commonViewVO, combTransformVO);
        return ResultDataVO.success(dataRefreshStatus);
    }

    @Override
    @Audit(module = "ictCustomCombService-createIctTempTable", operation = "createIctTempTable",
            message = "汇总组合-创建临时表")
    @JalorOperation(code = "createIctTempTable", desc = "汇总组合-创建临时表")
    public ResultDataVO createIctTempTable(CommonViewVO commonViewVO) {
        getPermissionList(commonViewVO);
        String userAccount = UserInfoUtils.getCurrentUser().getUserAccount();
        Long userId = UserInfoUtils.getUserId();
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        String roleId = String.valueOf(currentRole.getRoleId());

        commonViewVO.setRoleId(roleId);
        commonViewVO.setUserId(String.valueOf(userId));
        commonViewVO.setUserAccount(userAccount);

        // 父层级的list设置为parent
        List<DmFcstDimInfoVO> parentCustomVOList = commonViewVO.getParentCustomVOList();

        parentCustomVOList.stream().forEach(parent -> parent.setSelectFlag("parent"));

        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        Long id = Long.parseLong(new SimpleDateFormat("yyyyMMddHHmmsss").format(timestamp));

        // 父类
        List<DmFcstDimInfoVO> oneCustomList = getCurrentTempCombList(commonViewVO, parentCustomVOList, id, timestamp);

        insertTempCombRecursion(oneCustomList, commonViewVO.getCostType(), 0L, 500L);
        // 把custom_id的数据插入到临时表
        commonViewVO.setId(String.valueOf(id));
        dmCustomTempDao.createTempCombByCustomId(commonViewVO);
        return ResultDataVO.success();
    }

    @Override
    @Audit(module = "ictCustomCombService-deleteIctTempTable", operation = "deleteIctTempTable", message = "汇总组合-删除临时表")
    @JalorOperation(code = "deleteIctTempTable", desc = "汇总组合-删除临时表")
    public ResultDataVO deleteIctTempTable(CommonViewVO commonViewVO) {
        String userAccount = UserInfoUtils.getCurrentUser().getUserAccount();
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        String roleId = String.valueOf(currentRole.getRoleId());
        commonViewVO.setUserAccount(userAccount);
        commonViewVO.setRoleId(roleId);
        dmCustomTempDao.deleteCustomCombTemp(commonViewVO);
        return ResultDataVO.success();
    }

    @Override
    @JalorOperation(code = "getIctTempTableList", desc = "汇总组合-获取临时表列表")
    @Audit(module = "ictCustomCombService-getIctTempTableList", operation = "getIctTempTableList", message = "汇总组合-获取临时表列表")
    public ResultDataVO getIctTempTableList(CommonViewVO commonViewVO) {
        setUserParam(commonViewVO);
        // 没有传目录树，说明是空列表
        if (StringUtils.isEmpty(commonViewVO.getGranularityType())) {
            return ResultDataVO.success();
        }
        List<DmFcstDimInfoVO> allDmCustomCombList = new ArrayList<>();
        if (StringUtils.isNotEmpty(commonViewVO.getLv0CnName())) {
            String groupLevel = FcstIndustryUtil.getViewNextGroupLevel(commonViewVO);
            commonViewVO.setGroupLevel(groupLevel);
        }
        List<DmFcstDimInfoVO> dmCustomCombList = dmCustomTempDao.getTempTableCustomCombList(commonViewVO);
        allDmCustomCombList.addAll(dmCustomCombList);
        Map customCombMap = new HashMap();
        Map<String, List<DmFcstDimInfoVO>> viewInfoMap = dmCustomCombList.stream().collect(Collectors.groupingBy(DmFcstDimInfoVO::getGroupLevel));
        List<DmFcstDimInfoVO> allGroupLevelConditionList = new ArrayList<>();
        allTempLevelCondition(commonViewVO, viewInfoMap, allGroupLevelConditionList);
        customCombMap.put("list", allGroupLevelConditionList);
        return ResultDataVO.success(customCombMap);
    }

    @Override
    @JalorOperation(code = "removeIctTempTableList", desc = "汇总组合-左右移动列表")
    @Audit(module = "ictCustomCombService-removeIctTempTableList", operation = "removeIctTempTableList", message = "汇总组合-左右移动列表")
    public ResultDataVO removeIctTempTableList(CommonViewVO commonViewVO) throws InterruptedException {
        // 设置参数
        setIctMoveParam(commonViewVO);
        List<DmFcstDimInfoVO> customVOList = commonViewVO.getCustomVOList();
        // 获取折叠的list
        List<DmFcstDimInfoVO> foldCustomList = new ArrayList<>();
        // 未折叠的list
        List<DmFcstDimInfoVO> notFoldCustomList = new ArrayList<>();
        getFoldOrNotList(customVOList, foldCustomList, notFoldCustomList);

        List<DmFcstDimInfoVO> customCombList = new ArrayList<>();
        IRequestContext requestContext = RequestContext.getCurrent();
        // 关键字筛选后的list或者层级筛选后的list
        List<DmFcstDimInfoVO> filterCustomVOList = commonViewVO.getFilterCustomVOList();
        if (CollectionUtils.isNotEmpty(filterCustomVOList) && "right".equals(commonViewVO.getRemoveFlag())) {
            CommonViewVO commonParamVO = new CommonViewVO();
            BeanUtils.copyProperties(commonViewVO, commonParamVO);
            // 如果筛选出来LV0下只有一个LV1，则不能移动，直接返回前端给出提示语
            Map<String, List<DmFcstDimInfoVO>> dmCustomMap = filterCustomVOList.stream().collect(Collectors.groupingBy(DmFcstDimInfoVO::getGroupLevel));
            // 如果选中了LV0下的某个grouplevel的所有code，则表示选中了LV0
            String selectAllLevelData = getSelectCodeByGroupLevel(commonParamVO, dmCustomMap);
            if (StringUtils.isNotEmpty(selectAllLevelData)) {
                ResultDataVO.success(selectAllLevelData);
            }
        }
        // 折叠后的list获取到的全部子项
        Future<Boolean> groupCodeFlag = asyncIctQueryService.getIctFoldGroupCodeList(commonViewVO, foldCustomList, customCombList, requestContext);

        List<DmFcstDimInfoVO> findParentList = new ArrayList<>();
        List<DmFcstDimInfoVO> remainList = new ArrayList<>();
        Future<Boolean> filterGroupCodeFlag = new AsyncResult<>(Boolean.TRUE);
        if (CollectionUtils.isNotEmpty(filterCustomVOList)) {
            // 判断filterCustomVOList各个code之间，是否是父子关系
            findParentChildRelationList(filterCustomVOList, findParentList, remainList, customCombList);
            // 获取折叠后的子层级
            filterGroupCodeFlag = asyncIctQueryService.getIctFoldGroupCodeList(commonViewVO, remainList, customCombList, requestContext);
        }
        while (true) {
            if (groupCodeFlag.isDone() && filterGroupCodeFlag.isDone()) {
                break;
            }
        }
        customCombList.stream().forEach(cust -> cust.setSelectFlag("current"));
        if (CollectionUtils.isNotEmpty(findParentList)) {
            // 获取筛选后list的父层级，设置SelectFlag为parent
            findParentCodeByFilterList(findParentList, commonViewVO, customCombList);
        }

        customCombList.addAll(foldCustomList);
        customCombList.addAll(notFoldCustomList);
        // 如果有某些父级的code改变了状态
        List<String> tempParentCustomCombList = new ArrayList<>();
        if (StringUtils.isNotEmpty(commonViewVO.getId())) {
            tempParentCustomCombList = dmCustomTempDao.getTempParentCombList(commonViewVO);
        }
        updateChangeListSelectFlag(commonViewVO, customVOList, tempParentCustomCombList);
        if ("right".equals(commonViewVO.getRemoveFlag())) {
            // id不为空，表示临时表已经有数据了，需要过滤掉已在临时表中的connectCode
            removeByConnectCode(commonViewVO, customCombList, tempParentCustomCombList);
        }
        // 左移或者右移
        leftOrRightMoveCondition(commonViewVO, customCombList, filterCustomVOList);
        return ResultDataVO.success();
    }

    private void getFoldOrNotList(List<DmFcstDimInfoVO> customVOList, List<DmFcstDimInfoVO> foldCustomList, List<DmFcstDimInfoVO> notFoldCustomList) {
        customVOList.forEach(custom -> {
            if ("Y".equals(custom.getFoldFlag())) {
                foldCustomList.add(custom);
            } else {
                notFoldCustomList.add(custom);
            }
        });
    }

    private void removeByConnectCode(CommonViewVO commonViewVO, List<DmFcstDimInfoVO> customCombList, List<String> tempParentCustomCombList) {
        if (StringUtils.isNotEmpty(commonViewVO.getId())) {
            customCombList.removeIf(cust -> tempParentCustomCombList.contains(cust.getConnectCode()));
        }
    }

    private void leftOrRightMoveCondition(CommonViewVO commonViewVO, List<DmFcstDimInfoVO> customCombList, List<DmFcstDimInfoVO> filterCustomVOList) {
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        Long id;
        if ("Y".equals(commonViewVO.getFirstFlag())) {
            id = Long.parseLong(new SimpleDateFormat("yyyyMMddHHmmsss").format(timestamp));
        } else {
            id = Long.valueOf(commonViewVO.getId());
        }
        if ("right".equals(commonViewVO.getRemoveFlag())) {
            // 组装当前传入的数据
            List<DmFcstDimInfoVO> oneCustomList = getCurrentTempCombList(commonViewVO, customCombList, id, timestamp);
            insertTempCombRecursion(oneCustomList, commonViewVO.getCostType(), 0L, 500L);
        } else {
            // 向左移动，需要删除传入的list
            leftRemoveCondition(customCombList, commonViewVO, filterCustomVOList);
        }
    }

    private void leftRemoveCondition(List<DmFcstDimInfoVO> customCombList, CommonViewVO commonViewVO, List<DmFcstDimInfoVO> filterCustomVOList) {
        List<DmFcstDimInfoVO> currentCustomList = customCombList.stream().filter(comb -> "current".equals(comb.getSelectFlag())).collect(Collectors.toList());

        dmCustomTempDao.deleteCombTempByConnectCode(commonViewVO, currentCustomList);
        // 如果子节点已经没有数据，父节点也需要被移除
        if (CollectionUtils.isNotEmpty(filterCustomVOList)) {
            List<DmFcstDimInfoVO> parentSelectFlagList = customCombList.stream().filter(comb -> "parent".equals(comb.getSelectFlag())).collect(Collectors.toList());

            parentSelectFlagList.stream().forEach(parent -> {
                commonViewVO.setConnectCode(parent.getConnectCode());
                Integer count = dmCustomTempDao.getCountCombTemp(commonViewVO);
                if (count == 0) {
                    dmCustomTempDao.deleteCustomCombTemp(commonViewVO);
                }
            });
        }
    }

    private void updateChangeListSelectFlag(CommonViewVO commonViewVO, List<DmFcstDimInfoVO> inputCustomList, List<String> tempParentCustomCombList) {
        // 原id的临时表数据
        if (StringUtils.isNotEmpty(commonViewVO.getId())) {
            List<DmFcstDimInfoVO> changeSelectFlagList = inputCustomList.stream().filter(custom -> tempParentCustomCombList.contains(custom.getConnectCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(changeSelectFlagList)) {
                dmCustomTempDao.updateCombChageSelectFlagList(commonViewVO, changeSelectFlagList);
            }
        }
    }

    private void setIctMoveParam(CommonViewVO commonViewVO) {
        String userAccount = UserInfoUtils.getCurrentUser().getUserAccount();
        Long userId = UserInfoUtils.getUserId();
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        String roleId = String.valueOf(currentRole.getRoleId());
        commonViewVO.setUserAccount(userAccount);
        commonViewVO.setUserId(String.valueOf(userId));
        commonViewVO.setRoleId(roleId);
        // 获取version_id
        commonViewVO.setVersionId(commonService.getVersionId(IndustryConstEnum.PAGE_TYPE.ANNUAL.getValue()));
        commonViewVO.setMonVersionId(commonService.getVersionId(IndustryConstEnum.PAGE_TYPE.MONTH.getValue()));
        commonViewVO.setPageFlag(commonViewVO.getPageSymbol());
    }

    private String getSelectCodeByGroupLevel(CommonViewVO commonViewVO, Map<String, List<DmFcstDimInfoVO>> dmCustomMap) {
        // 设置数据权限
        getPermissionList(commonViewVO);
        commonViewVO.setLv0Flag("N");
        if (commonViewVO.getLv0DimensionSet().size() == 0 || !commonViewVO.getLv0DimensionSet().contains("NO_PERMISSION")) {
            commonViewVO.setLv0Flag("Y");
        }
        for (Map.Entry<String, List<DmFcstDimInfoVO>> dmCustomEntry : dmCustomMap.entrySet()) {
            commonViewVO.setGroupLevel(dmCustomEntry.getKey());
            List<DmFcstDimInfoVO> allGroupCodeList = new ArrayList<>();
            // LV1,LV2需要权限控制
            if (GroupLevelEnum.LV1.getValue().equals(dmCustomEntry.getKey())) {
                ictCustomCommonService.getDbListForAllPage(commonViewVO, allGroupCodeList);
            }
            if (GroupLevelEnum.LV2.getValue().equals(dmCustomEntry.getKey())) {
                ictCustomCommonService.getDbListForAllPage(commonViewVO, allGroupCodeList);
            }
            if (!GroupLevelEnum.LV1.getValue().equals(dmCustomEntry.getKey()) && !GroupLevelEnum.LV2.getValue().equals(dmCustomEntry.getKey())) {
                ictCustomCommonService.getDbListForAllPage(commonViewVO, allGroupCodeList);
            }
            // 如果相等，说明选择了LV0下所有的当前这个grouplevel的code，则代表选中了lv0，直接返回
            if (allGroupCodeList.size() == dmCustomEntry.getValue().size()) {
                return "exist";
            }
        }
        return null;
    }

    private void findParentChildRelationList(List<DmFcstDimInfoVO> filterCustomList, List<DmFcstDimInfoVO> findParentList, List<DmFcstDimInfoVO> remainList, List<DmFcstDimInfoVO> customCombList) {
        Set<String> combCodeList = filterCustomList.stream().map(DmFcstDimInfoVO::getConnectCode).collect(Collectors.toSet());
        Set<String> parentCodeList = filterCustomList.stream().map(DmFcstDimInfoVO::getConnectParentCode).collect(Collectors.toSet());
        // 交集，父的connectcode不需要再去求子集
        Set<String> intersectionList = combCodeList.stream().filter(comb -> parentCodeList.contains(comb)).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(intersectionList)) {
            List<DmFcstDimInfoVO> filterParentList = filterCustomList.stream().filter(cust -> intersectionList.contains(cust.getConnectCode())).collect(Collectors.toList());
            findParentList.addAll(filterParentList);
            // 还需要去查找子集的list
            List<DmFcstDimInfoVO> remainChildList = filterCustomList.stream().filter(cust -> !intersectionList.contains(cust.getConnectCode())).collect(Collectors.toList());
            remainList.addAll(remainChildList);
            customCombList.addAll(remainList);
        } else {
            findParentList.addAll(filterCustomList);
            remainList.addAll(filterCustomList);
        }
    }

    private void findParentCodeByFilterList(List<DmFcstDimInfoVO> findParentList, CommonViewVO commonViewVO, List<DmFcstDimInfoVO> customCombList) {
        List<DmFcstDimInfoVO> parentCombList = new ArrayList<>();
        Map<String, List<DmFcstDimInfoVO>> groupLevelMap = findParentList.stream().collect(Collectors.groupingBy(DmFcstDimInfoVO::getGroupLevel));
        parentCombList = getParentCodeList(commonViewVO, groupLevelMap, findParentList, false);
        parentCombList.stream().forEach(cust -> cust.setSelectFlag("parent"));
        Set<String> combCodeList = customCombList.stream().map(DmFcstDimInfoVO::getConnectCode).collect(Collectors.toSet());
        Set<String> parentCodeList = parentCombList.stream().map(DmFcstDimInfoVO::getConnectCode).collect(Collectors.toSet());
        // 交集
        Set<String> intersectionCodeList = combCodeList.stream().filter(comb -> parentCodeList.contains(comb)).collect(Collectors.toSet());

        if (CollectionUtils.isNotEmpty(intersectionCodeList)) {
            parentCombList.removeIf(all -> intersectionCodeList.contains(all.getConnectCode()));
        }
        List<DmFcstDimInfoVO> parentCustomList = new ArrayList<>();
        parentCombList.stream().forEach(parent -> {
            DmFcstDimInfoVO dmCustomCombVO = ObjectCopyUtil.copy(parent, DmFcstDimInfoVO.class);
            parentCustomList.add(dmCustomCombVO);
        });
        customCombList.addAll(parentCustomList);
        customCombList.addAll(findParentList);
    }

    private void allTempLevelCondition(CommonViewVO commonViewVO, Map<String, List<DmFcstDimInfoVO>> viewInfoMap, List<DmFcstDimInfoVO> alllevelList) {
        List<String> allGroupLevelList = new ArrayList<>();
        setIctLevel(allGroupLevelList, commonViewVO);
        for (String groupLevel : allGroupLevelList) {
            for (Map.Entry<String, List<DmFcstDimInfoVO>> groupLevelEntry : viewInfoMap.entrySet()) {
                String groupLevelKey = groupLevelEntry.getKey();
                if (groupLevel.equals(groupLevelKey)) {
                    alllevelList.addAll(groupLevelEntry.getValue());
                }
            }
        }
    }

    private void setUserParam(CommonViewVO commonViewVO) {
        String userAccount = UserInfoUtils.getCurrentUser().getUserAccount();

        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        String roleId = String.valueOf(currentRole.getRoleId());
        commonViewVO.setUserAccount(userAccount);
        commonViewVO.setRoleId(roleId);
    }

    private void insertTempCombRecursion(List<DmFcstDimInfoVO> customList, String costType, Long start, Long limit) {
        List<DmFcstDimInfoVO> customSubList =
                customList.stream().skip(start).limit(limit).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(customSubList)) {
            return;
        }
        // 插入数据
        dmCustomTempDao.createTempCombList(customSubList, costType);

        insertTempCombRecursion(customList, costType,  start + limit, limit);
    }

    private List<DmFcstDimInfoVO> getCurrentTempCombList(CommonViewVO commonViewVO, List<DmFcstDimInfoVO> customVOList, Long id, Timestamp timestamp) {
        String userId = commonViewVO.getUserId();
        String userAccount = commonViewVO.getUserAccount();
        String roleId = commonViewVO.getRoleId();
        return customVOList.stream().map(customVO -> {
            DmFcstDimInfoVO dmFcstDimInfoVO = new DmFcstDimInfoVO();
            BeanUtils.copyProperties(customVO, dmFcstDimInfoVO);
            dmFcstDimInfoVO.setId(id);
            dmFcstDimInfoVO.setCustomCnName(String.valueOf(id));
            dmFcstDimInfoVO.setCreatedBy(userId);
            dmFcstDimInfoVO.setCreationDate(timestamp);
            dmFcstDimInfoVO.setLastUpdatedBy(userId);
            dmFcstDimInfoVO.setLastUpdateDate(timestamp);
            dmFcstDimInfoVO.setRoleId(roleId);
            dmFcstDimInfoVO.setUserId(userAccount);
            dmFcstDimInfoVO.setEnableFlag(ENABLE_FLAG_Y);
            dmFcstDimInfoVO.setSubEnableFlag(ENABLE_FLAG_Y);
            dmFcstDimInfoVO.setIsSeparate("N");
            dmFcstDimInfoVO.setViewFlag(commonViewVO.getViewFlag());
            dmFcstDimInfoVO.setPageFlag(commonViewVO.getPageSymbol());
            dmFcstDimInfoVO.setOverseaFlag(commonViewVO.getOverseaFlag());
            dmFcstDimInfoVO.setMainFlag(commonViewVO.getMainFlag());
            dmFcstDimInfoVO.setCodeAttributes(commonViewVO.getCodeAttributes());
            dmFcstDimInfoVO.setSoftwareMark(commonViewVO.getSoftwareMark());
            dmFcstDimInfoVO.setGranularityType(commonViewVO.getGranularityType());
            dmFcstDimInfoVO.setRegionCode(commonViewVO.getRegionCode());
            dmFcstDimInfoVO.setRegionCnName(commonViewVO.getRegionCnName());
            dmFcstDimInfoVO.setRepofficeCode(commonViewVO.getRepofficeCode());
            dmFcstDimInfoVO.setRepofficeCnName(commonViewVO.getRepofficeCnName());
            dmFcstDimInfoVO.setBgCode(commonViewVO.getBgCode());
            dmFcstDimInfoVO.setBgCnName(commonViewVO.getBgCnName());

            return dmFcstDimInfoVO;
        }).collect(Collectors.toList());
    }

    private void getIctProdTeamLevel(List<String> groupLevelList, CommonViewVO commonViewVO) {

        Set<String> lv0DimensionSet = commonViewVO.getLv0DimensionSet();
        Set<String> lv2DimensionSet = commonViewVO.getLv2DimensionSet();
        Set<String> lv3DimensionSet = commonViewVO.getLv3DimensionSet();
        boolean lv0PermissionFlag = lv0DimensionSet.size() == 0 || !lv0DimensionSet.contains("NO_PERMISSION");
        boolean lv2PermissionFlag = lv2DimensionSet.size() == 0 || !lv2DimensionSet.contains("NO_PERMISSION");
        boolean lv3PermissionFlag = lv3DimensionSet.size() == 0 || !lv3DimensionSet.contains("NO_PERMISSION");

        if (lv0PermissionFlag) {
            groupLevelList.add(GroupLevelEnum.LV0.getValue());
        }
        if (lv2PermissionFlag) {
            groupLevelList.add(GroupLevelEnum.LV1.getValue());
        }
        if (lv3PermissionFlag) {
            groupLevelList.add(GroupLevelEnum.LV2.getValue());
        }
        groupLevelList.add(GroupLevelEnum.LV3.getValue());
        groupLevelList.add(GroupLevelEnum.LV4.getValue());
    }

    private void findNextGroupLevel(CommonViewVO commonViewVO) {
        if (StringUtils.isNotEmpty(commonViewVO.getLv0CnName())) {
            // 获取下一层级
            String groupLevel = FcstIndustryUtil.getViewNextGroupLevel(commonViewVO);
            commonViewVO.setGroupLevel(groupLevel);
        }
    }

    private void firstQueryList(CommonViewVO commonViewVO, List<DmFcstDimInfoVO> dmCustomCombList) {
        if (StringUtils.isEmpty(commonViewVO.getLv0CnName())) {
            Set<String> customCombGroupLevelSet = dmCustomCombList.stream().map(DmFcstDimInfoVO::getGroupLevel).collect(Collectors.toSet());
            Set<String> prodTeamGroupLevelSet = new HashSet<>();
            prodTeamGroupLevelSet.add(GroupLevelEnum.LV0.getValue());
            prodTeamGroupLevelSet.add(GroupLevelEnum.LV1.getValue());
            prodTeamGroupLevelSet.add(GroupLevelEnum.LV2.getValue());
            prodTeamGroupLevelSet.add(GroupLevelEnum.LV3.getValue());
            // 判断原始数据是否有 重量级团队，如果有就只展示重量级团队的树，如果没有重量级团队，则全部展示
            Set<String> containsList = prodTeamGroupLevelSet.stream().filter(customCombGroupLevelSet::contains).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(containsList)) {
                dmCustomCombList.removeIf(dm -> !prodTeamGroupLevelSet.contains(dm.getGroupLevel()));
            }
        }
    }

    private List<DmFcstDimInfoVO> getParentCodeList(CommonViewVO commonVO, Map<String, List<DmFcstDimInfoVO>> groupLevelMap, List<? extends DmFcstDimInfoVO> dmCustomCombList, boolean flag) {
        // 设置参数
        setCommonViewParam(commonVO, dmCustomCombList, flag);
        CommonViewVO commonViewVO = ObjectCopyUtil.copy(commonVO, CommonViewVO.class);
        if ("Y".equals(commonViewVO.getMainFlag()) && "全选".equals(commonViewVO.getCodeAttributes())) {
            commonViewVO.setCodeAttributes("");
        }
        List<DmFcstDimInfoVO> allParentList = new ArrayList<>();

        for (Map.Entry<String, List<DmFcstDimInfoVO>> groupLevelEntry : groupLevelMap.entrySet()) {
            String groupLevel = groupLevelEntry.getKey();
            List<String> lv1ProdRndTeamCodeSet = groupLevelEntry.getValue().stream().map(DmFcstDimInfoVO::getLv1Code).distinct().collect(Collectors.toList());
            List<String> lv2ProdRndTeamCodeSet = groupLevelEntry.getValue().stream().map(DmFcstDimInfoVO::getLv2Code).distinct().collect(Collectors.toList());
            List<String> lv3ProdRndTeamCodeSet = groupLevelEntry.getValue().stream().map(DmFcstDimInfoVO::getLv3Code).distinct().collect(Collectors.toList());
            List<String> lv4ProdRndTeamCodeSet = groupLevelEntry.getValue().stream().map(DmFcstDimInfoVO::getLv4Code).distinct().collect(Collectors.toList());
            lv1ProdRndTeamCodeSet.remove(null);
            lv2ProdRndTeamCodeSet.remove(null);
            lv3ProdRndTeamCodeSet.remove(null);
            lv4ProdRndTeamCodeSet.remove(null);
            commonViewVO.setLv1CodeList(lv1ProdRndTeamCodeSet);
            commonViewVO.setLv2CodeList(lv2ProdRndTeamCodeSet);
            commonViewVO.setLv3CodeList(lv3ProdRndTeamCodeSet);
            commonViewVO.setLv4CodeList(lv4ProdRndTeamCodeSet);
            // 根据当前group_level，获取上一层级grouplevel
            List<String> groupLevelList = new ArrayList<>();
            getAllCodeParentList(commonViewVO, groupLevel, groupLevelList, allParentList);
        }
        // 整体去重
        allParentList = allParentList.stream().distinct().collect(Collectors.toList());
        return allParentList;
    }

    private void setCommonViewParam(CommonViewVO commonViewVO, List<? extends DmFcstDimInfoVO> dmCustomCombList, boolean flag) {
        // 设置数据权限
        getPermissionList(commonViewVO);
        commonViewVO.setLv0Flag("N");
        if (commonViewVO.getLv0DimensionSet().size() == 0 || !commonViewVO.getLv0DimensionSet().contains("NO_PERMISSION")) {
            commonViewVO.setLv0Flag("Y");
        }
        FcstIndustryUtil.setSpecailCode(commonViewVO);
        if (IndustryConstEnum.PAGE_TYPE.ANNUAL.getValue().equals(commonViewVO.getPageSymbol())) {
            if ("N".equals(commonViewVO.getMainFlag())) {
                commonViewVO.setMainFlag(null);
            }
        }
        // 如果是带ALL的
        if (commonViewVO.getPageFlag().contains(CommonConstant.PAGE_FLAG_ALL)) {
            commonViewVO.setPageFlag(commonViewVO.getPageSymbol());
        }
    }

    private void getAllCodeParentList(CommonViewVO commonViewVO, String groupLevel, List<String> groupLevelList, List<DmFcstDimInfoVO> allParentList) {
        getBeforeIctGroupLevel(groupLevel, groupLevelList);
        // 查询数据库
        queryParentCodeFromDB(commonViewVO, allParentList, groupLevelList);
    }

    private void getBeforeIctGroupLevel(String beforeGroupLevel, List<String> groupLevelList) {
        // 本身的groupLevel，且LV0不需要加入
        groupLevelList.add(beforeGroupLevel);

        if (GroupLevelEnum.LV4.getValue().equals(beforeGroupLevel)) {
            groupLevelList.add(GroupLevelEnum.LV3.getValue());
            groupLevelList.add(GroupLevelEnum.LV2.getValue());
            groupLevelList.add(GroupLevelEnum.LV1.getValue());
        }
        if (GroupLevelEnum.LV3.getValue().equals(beforeGroupLevel)) {
            groupLevelList.add(GroupLevelEnum.LV2.getValue());
            groupLevelList.add(GroupLevelEnum.LV1.getValue());
        }
        if (GroupLevelEnum.LV2.getValue().equals(beforeGroupLevel)) {
            groupLevelList.add(GroupLevelEnum.LV1.getValue());
        }
    }

    private void queryParentCodeFromDB(CommonViewVO commonViewVO, List<DmFcstDimInfoVO> allParentList, List<String> groupLevelList) {
        List<String> subGroupCodeList = new ArrayList<>();
        String mainFlag = commonViewVO.getMainFlag();
        String granularityType = commonViewVO.getGranularityType();
        for (String beforeGroupLevel : groupLevelList) {
            if (!"LV0".equals(beforeGroupLevel)) {
                List<DmFcstDimInfoVO> parentList = new ArrayList<>();
                List<DmFcstDimInfoVO> parentInfoList = new ArrayList<>();
                commonViewVO.setGroupLevel(beforeGroupLevel);
                if ("Y".equals(mainFlag)) {
                    if (IndustryConstEnum.PAGE_TYPE.MONTH.getValue().equals(commonViewVO.getPageSymbol())) {
                        switch (granularityType) {
                            case "IRB":
                                parentList = dmFcstDimInfoDao.getParentMainIrbDimInfoList(commonViewVO);
                                parentInfoList = parentList.stream().distinct().collect(Collectors.toList());
                                subGroupCodeList = parentInfoList.stream().map(DmFcstDimInfoVO::getGroupCode).collect(Collectors.toList());
                                commonViewVO.setGroupCodeList(subGroupCodeList);
                                allParentList.addAll(parentInfoList);
                                break;
                            case "INDUS":
                                parentList = dmFcstDimInfoDao.getParentMainIndusDimInfoList(commonViewVO);
                                parentInfoList = parentList.stream().distinct().collect(Collectors.toList());
                                subGroupCodeList = parentInfoList.stream().map(DmFcstDimInfoVO::getGroupCode).collect(Collectors.toList());
                                commonViewVO.setGroupCodeList(subGroupCodeList);
                                allParentList.addAll(parentInfoList);
                                break;
                            case "PROD":
                                parentList = dmFcstDimInfoDao.getParentMainProdDimInfoList(commonViewVO);
                                parentInfoList = parentList.stream().distinct().collect(Collectors.toList());
                                subGroupCodeList = parentInfoList.stream().map(DmFcstDimInfoVO::getGroupCode).collect(Collectors.toList());
                                commonViewVO.setGroupCodeList(subGroupCodeList);
                                allParentList.addAll(parentInfoList);
                                break;
                            default:
                                break;
                        }
                    } else {
                        mainFlagAnnualQueryParent(granularityType, commonViewVO, allParentList);
                    }
                } else {
                    notMainFlagQueryParent(granularityType, commonViewVO, allParentList);
                }
            }
        }
    }

    private void mainFlagAnnualQueryParent(String granularityType, CommonViewVO commonViewVO, List<DmFcstDimInfoVO> allParentList) {
        List<String> subGroupCodeList = new ArrayList<>();
        List<DmFcstDimInfoVO> parentList = new ArrayList<>();
        List<DmFcstDimInfoVO> parentInfoList = new ArrayList<>();
        switch (granularityType) {
            case "IRB":
                parentList = dmFcstDimInfoDao.getParentMainAnnualIrbDimInfoList(commonViewVO);
                parentInfoList = parentList.stream().distinct().collect(Collectors.toList());
                subGroupCodeList = parentInfoList.stream().map(DmFcstDimInfoVO::getGroupCode).collect(Collectors.toList());
                commonViewVO.setGroupCodeList(subGroupCodeList);
                allParentList.addAll(parentInfoList);
                break;
            case "INDUS":
                parentList = dmFcstDimInfoDao.getParentMainAnnualIndusDimInfoList(commonViewVO);
                parentInfoList = parentList.stream().distinct().collect(Collectors.toList());
                subGroupCodeList = parentInfoList.stream().map(DmFcstDimInfoVO::getGroupCode).collect(Collectors.toList());
                commonViewVO.setGroupCodeList(subGroupCodeList);
                allParentList.addAll(parentInfoList);
                break;
            case "PROD":
                parentList = dmFcstDimInfoDao.getParentMainAnnualProdDimInfoList(commonViewVO);
                parentInfoList = parentList.stream().distinct().collect(Collectors.toList());
                subGroupCodeList = parentInfoList.stream().map(DmFcstDimInfoVO::getGroupCode).collect(Collectors.toList());
                commonViewVO.setGroupCodeList(subGroupCodeList);
                allParentList.addAll(parentInfoList);
                break;
            default:
                break;
        }
    }

    private void notMainFlagQueryParent(String granularityType, CommonViewVO commonViewVO, List<DmFcstDimInfoVO> allParentList) {
        List<String> subGroupCodeList = new ArrayList<>();
        List<DmFcstDimInfoVO> parentList = new ArrayList<>();
        List<DmFcstDimInfoVO> parentInfoList = new ArrayList<>();
        switch (granularityType) {
            case "IRB":
                parentList = dmFcstDimInfoDao.getParentIrbDimInfoList(commonViewVO);
                parentInfoList = parentList.stream().distinct().collect(Collectors.toList());
                subGroupCodeList = parentInfoList.stream().map(DmFcstDimInfoVO::getGroupCode).collect(Collectors.toList());
                commonViewVO.setGroupCodeList(subGroupCodeList);
                allParentList.addAll(parentInfoList);
                break;
            case "INDUS":
                parentList = dmFcstDimInfoDao.getParentIndusDimInfoList(commonViewVO);
                parentInfoList = parentList.stream().distinct().collect(Collectors.toList());
                subGroupCodeList = parentInfoList.stream().map(DmFcstDimInfoVO::getGroupCode).collect(Collectors.toList());
                commonViewVO.setGroupCodeList(subGroupCodeList);
                allParentList.addAll(parentInfoList);
                break;
            case "PROD":
                parentList = dmFcstDimInfoDao.getParentProdDimInfoList(commonViewVO);
                parentInfoList = parentList.stream().distinct().collect(Collectors.toList());
                subGroupCodeList = parentInfoList.stream().map(DmFcstDimInfoVO::getGroupCode).collect(Collectors.toList());
                commonViewVO.setGroupCodeList(subGroupCodeList);
                allParentList.addAll(parentInfoList);
                break;
            default:
                break;
        }
    }

    private void callFunctionRefreshData(CommonViewVO commonViewVO, Long userId, DmFcstDataRefreshStatus dataRefreshStatus, CombTransformVO combTransformVO) {
        combTransformVO.setUserId(userId);
        combTransformVO.setTaskId(dataRefreshStatus.getTaskId());
        combTransformVO.setCustomId(commonViewVO.getCustomId());
        combTransformVO.setGranularityType(commonViewVO.getGranularityType());
        combTransformVO.setPageFlag(commonViewVO.getPageFlag());
        // 获取加密key
        String plainText = ConfigUtil.getInstance().get16PlainText();
        combTransformVO.setEncryptKey(plainText);
        // 获取最新版本号
        combTransformVO.setVersionId(commonService.getVersionId(IndustryConstEnum.PAGE_TYPE.ANNUAL.getValue()));
        combTransformVO.setMonthVersionId(commonService.getVersionId(IndustryConstEnum.PAGE_TYPE.MONTH.getValue()));
        combTransformVO.setCostType(commonViewVO.getCostType());
        combTransformVO.setPageSymbol(commonViewVO.getPageSymbol());
        combTransformVO.setYtdFlag(commonViewVO.getYtdFlag());
    }

    private void getPermissionList(CommonViewVO commonViewVO) {
        DataPermissionsVO currentRoleDataPermission = dataPermissionService.getCurrentRoleDataPermission();
        commonViewVO.setLv0DimensionSet(currentRoleDataPermission.getLv0DimensionSet());
        commonViewVO.setLv1DimensionSet(currentRoleDataPermission.getLv1DimensionSet());
        commonViewVO.setLv2DimensionSet(currentRoleDataPermission.getLv2DimensionSet());
        commonViewVO.setLv3DimensionSet(currentRoleDataPermission.getLv3DimensionSet());
        // 通过LV1,LV2反向带出LV0的权限，如果某个用户拥有所有LV1，即拥有LV0权限
        List<DmFcstDimInfoVO> allProdDimensionList = dataPermissionService.getLv1AndLv2VOList(commonViewVO);
        Set<String> lv2DimensionSet = currentRoleDataPermission.getLv2DimensionSet();
        Set<String> lv1CodeList = allProdDimensionList.stream().filter(item -> commonViewVO.getLv0ProdRndTeamCode().equals(item.getLv0Code())).map(DmFcstDimInfoVO::getLv1Code).collect(Collectors.toSet());
        if (lv2DimensionSet.containsAll(lv1CodeList) || CollectionUtils.isEmpty(lv2DimensionSet)) {
            commonViewVO.setLv1DimensionSet(new HashSet<>());
        }
    }

    private void setIctPermissionFlag(CommonViewVO commonViewVO, List<DmFcstDimInfoVO> dmCustomCombList, List<DmFcstDimInfoVO> allGroupLevelConditionList) {

        Map<String, List<DmFcstDimInfoVO>> viewInfoMap = dmCustomCombList.stream().collect(Collectors.groupingBy(DmFcstDimInfoVO::getGroupLevel));
        allLevelCondition(viewInfoMap, allGroupLevelConditionList, commonViewVO);
    }

    private void allLevelCondition(Map<String, List<DmFcstDimInfoVO>> viewInfoMap, List<DmFcstDimInfoVO> allGroupLevelConditionList, CommonViewVO commonViewVO) {
        List<String> groupLevelList = new ArrayList<>();
        setIctLevel(groupLevelList, commonViewVO);
        for (String groupLevel : groupLevelList) {
            for (Map.Entry<String, List<DmFcstDimInfoVO>> groupLevelEntry : viewInfoMap.entrySet()) {
                String groupLevelKey = groupLevelEntry.getKey();
                if (groupLevel.equals(groupLevelKey)) {
                    allGroupLevelConditionList.addAll(groupLevelEntry.getValue());
                }
            }
        }
    }

    private void setIctLevel(List<String> allGroupLevelList, CommonViewVO commonViewVO) {
        allGroupLevelList.add(GroupLevelEnum.LV0.getValue());
        allGroupLevelList.add(GroupLevelEnum.LV1.getValue());
        allGroupLevelList.add(GroupLevelEnum.LV2.getValue());
        allGroupLevelList.add(GroupLevelEnum.LV3.getValue());
        allGroupLevelList.add(GroupLevelEnum.LV4.getValue());
    }

    private List<DmFcstDimInfoVO> getIctProdRndTeamCodeTree(CommonViewVO commonViewVO) {

        List<DmFcstDimInfoVO> allGroupCodeList = new ArrayList<>();
        CommonViewVO commonVO = new CommonViewVO();
        BeanUtils.copyProperties(commonViewVO, commonVO);
        commonVO.setPageFlag(commonViewVO.getPageSymbol());
        if (StringUtils.isNotBlank(commonVO.getLv0CnName())) {
            commonVO.setExpandFlag("Y");
            // 获取下一层级
            String groupLevel = FcstIndustryUtil.getViewNextGroupLevel(commonVO);
            commonVO.setGroupLevel(groupLevel);
        } else {
            commonVO.setExpandFlag("N");
        }
        // 重量级团队code,l1,l2，量纲code汇总
        ictCustomCommonService.getIndustryGroupCode(commonVO, allGroupCodeList, true);
        commonViewVO.setLv0Flag(commonVO.getLv0Flag());
        commonViewVO.setExpandFlag(commonVO.getExpandFlag());
        commonViewVO.setGroupLevel(commonVO.getGroupLevel());
        // 移除汇总组合中已有的记录
        removeListByCombId(commonViewVO, allGroupCodeList);
        // 如果同时筛选groupLevel和关键字，需要在这里增加层级筛选
        if (StringUtils.isNotEmpty(commonViewVO.getKeyword()) && StringUtils.isNotEmpty(commonViewVO.getFilterGroupLevel())) {
            allGroupCodeList.removeIf(all -> !commonViewVO.getFilterGroupLevel().equals(all.getGroupLevel()));
        }
        return allGroupCodeList;
    }

    private void removeListByCombId(CommonViewVO commonViewVO, List<DmFcstDimInfoVO> allProdTeamCodeList) {
        if (null != commonViewVO.getCustomId()) {
            List<DmFcstDimInfoVO> customCodeList = dmCustomDao.getCombinationCombList(commonViewVO);
            for (DmFcstDimInfoVO dmFcstDimInfoVO : customCodeList) {
                allProdTeamCodeList.removeIf(all -> all.getGroupLevel().equals(dmFcstDimInfoVO.getGroupLevel()) && all.getConnectCode().equals(dmFcstDimInfoVO.getConnectCode()));
            }
        }
        // 移除右侧临时表已有的code
        if (StringUtils.isNotEmpty(commonViewVO.getId())) {
            List<DmFcstDimInfoVO> customCodeList = dmCustomTempDao.getTempCustomList(commonViewVO);
            Set<String> connectCodeSet = customCodeList.stream().map(DmFcstDimInfoVO::getConnectCode).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(connectCodeSet)) {
                allProdTeamCodeList.removeIf(all -> connectCodeSet.contains(all.getConnectCode()));
            }
        }
    }

    private List<DmFcstDimInfoVO> getCurrentPageCombList(CommonViewVO commonViewVO, String userAccount, String roleId, List<DmFcstDimInfoVO> customList, Long customId, Timestamp timestamp) {

        // 月度同步年度时，月度需要设置默认的ytdFlag
        if ("MONTH".equals(commonViewVO.getPageSymbol())) {
            commonViewVO.setYtdFlag("N");
        }
        String userId = commonViewVO.getUserId();
        // 获取数据组需要的数据,lv3.5+spart
        CommonViewVO commonViewParam = ObjectCopyUtil.copy(commonViewVO, CommonViewVO.class);
        commonViewParam.setPageFlag(commonViewVO.getPageSymbol());
        List<DmFcstDimInfoVO> customSpartList = new ArrayList<>();
        customSpartList.addAll(customList);
        List<DmFcstDimInfoVO> spartPageList = ictCustomCommonService.getSpartListForPage(commonViewParam, customSpartList);
        String codeAttribute;
        if ("Y".equals(commonViewVO.getMainFlag()) && StringUtils.isBlank(commonViewVO.getCodeAttributes())) {
            codeAttribute = "全选";
        } else {
            codeAttribute = commonViewVO.getCodeAttributes();
        }
        if (IndustryConstEnum.COST_TYPE.STD.getValue().equals(commonViewVO.getCostType())) {
            commonViewVO.setSoftwareMark(IndustryConstEnum.SOFTWARE_MARK.HARDWARE.getValue());
        }
        List<DmFcstDimInfoVO> currentSpartVOList = getDmFcstDimInfoList(commonViewVO, userAccount, roleId, customId, timestamp, spartPageList, userId, codeAttribute);
        // use_flag=PAGE的list
        List<DmFcstDimInfoVO> pageOneList = new ArrayList<>();
        currentPageCombine(customList, commonViewVO, customId, timestamp, pageOneList);

        pageOneList.addAll(currentSpartVOList);
        return pageOneList;
    }

    @NotNull
    private List<DmFcstDimInfoVO> getDmFcstDimInfoList(CommonViewVO commonViewVO, String userAccount, String roleId, Long customId, Timestamp timestamp, List<DmFcstDimInfoVO> spartPageList, String userId, String codeAttribute) {
        List<DmFcstDimInfoVO> currentSpartVOList = new ArrayList<>();
        spartPageList.forEach(customVO->{
            DmFcstDimInfoVO vo = ObjectCopyUtil.copy(customVO, DmFcstDimInfoVO.class);
            vo.setCustomId(customId);
            vo.setCustomCnName(commonViewVO.getCustomCnName());
            vo.setGranularityType(commonViewVO.getGranularityType());
            vo.setCreatedBy(userId);
            vo.setCreationDate(timestamp);
            vo.setLastUpdatedBy(userId);
            vo.setLastUpdateDate(timestamp);
            vo.setRoleId(roleId);
            vo.setUserId(userAccount);
            vo.setEnableFlag(ENABLE_FLAG_Y);
            vo.setSubEnableFlag(ENABLE_FLAG_Y);
            vo.setIsSeparate("N");
            vo.setUseFlag("CALC");
            vo.setViewFlag(commonViewVO.getViewFlag());
            vo.setOverseaFlag(commonViewVO.getOverseaFlag());
            vo.setSoftwareMark(commonViewVO.getSoftwareMark());
            vo.setMainFlag(commonViewVO.getMainFlag());
            vo.setCodeAttributes(codeAttribute);
            if ("ANNUAL".equals(commonViewVO.getPageSymbol())) {
                vo.setYtdFlag(commonViewVO.getYtdFlag());
            }
            if (CommonConstant.PAGE_FLAG_ALL.equals(commonViewVO.getPageFlag())) {
                vo.setPageFlag(commonViewVO.getPageFlag() + "_" + commonViewVO.getPageSymbol());
            } else {
                vo.setPageFlag(commonViewVO.getPageFlag());
            }
            vo.setRegionCode(commonViewVO.getRegionCode());
            vo.setRegionCnName(commonViewVO.getRegionCnName());
            vo.setRepofficeCode(commonViewVO.getRepofficeCode());
            vo.setRepofficeCnName(commonViewVO.getRepofficeCnName());
            vo.setBgCode(commonViewVO.getBgCode());
            vo.setBgCnName(commonViewVO.getBgCnName());
            currentSpartVOList.add(vo);
        });
        return currentSpartVOList;
    }

    private void currentPageCombine(List<DmFcstDimInfoVO> customList, CommonViewVO commonViewVO, Long customId, Timestamp timestamp, List<DmFcstDimInfoVO> pageOneList) {
        String userAccount = commonViewVO.getUserAccount();
        String roleId = commonViewVO.getRoleId();
        String userId = commonViewVO.getUserId();
        customList.forEach(dmCustomCombVO -> {
            DmFcstDimInfoVO dmFcstDimInfoVO = ObjectCopyUtil.copy(dmCustomCombVO, DmFcstDimInfoVO.class);
            dmFcstDimInfoVO.setCustomId(customId);
            dmFcstDimInfoVO.setCustomCnName(commonViewVO.getCustomCnName());
            dmFcstDimInfoVO.setCreatedBy(userId);
            dmFcstDimInfoVO.setCreationDate(timestamp);
            dmFcstDimInfoVO.setLastUpdatedBy(userId);
            dmFcstDimInfoVO.setLastUpdateDate(timestamp);
            dmFcstDimInfoVO.setRoleId(roleId);
            dmFcstDimInfoVO.setUserId(userAccount);
            dmFcstDimInfoVO.setEnableFlag(ENABLE_FLAG_Y);
            dmFcstDimInfoVO.setSubEnableFlag(ENABLE_FLAG_Y);
            dmFcstDimInfoVO.setIsSeparate("N");
            dmFcstDimInfoVO.setUseFlag("PAGE");
            if ("ANNUAL".equals(commonViewVO.getPageSymbol())) {
                dmFcstDimInfoVO.setYtdFlag(commonViewVO.getYtdFlag());
            }
            dmFcstDimInfoVO.setViewFlag(commonViewVO.getViewFlag());
            dmFcstDimInfoVO.setOverseaFlag(commonViewVO.getOverseaFlag());
            dmFcstDimInfoVO.setSoftwareMark(commonViewVO.getSoftwareMark());
            dmFcstDimInfoVO.setMainFlag(commonViewVO.getMainFlag());
            dmFcstDimInfoVO.setCodeAttributes(commonViewVO.getCodeAttributes());
            dmFcstDimInfoVO.setYtdFlag(commonViewVO.getYtdFlag());
            if (CommonConstant.PAGE_FLAG_ALL.equals(commonViewVO.getPageFlag())) {
                dmFcstDimInfoVO.setPageFlag(commonViewVO.getPageFlag() + "_" + commonViewVO.getPageSymbol());
            } else {
                dmFcstDimInfoVO.setPageFlag(commonViewVO.getPageFlag());
            }
            dmFcstDimInfoVO.setGranularityType(commonViewVO.getGranularityType());
            dmFcstDimInfoVO.setRegionCode(commonViewVO.getRegionCode());
            dmFcstDimInfoVO.setRegionCnName(commonViewVO.getRegionCnName());
            dmFcstDimInfoVO.setRepofficeCode(commonViewVO.getRepofficeCode());
            dmFcstDimInfoVO.setRepofficeCnName(commonViewVO.getRepofficeCnName());
            dmFcstDimInfoVO.setBgCode(commonViewVO.getBgCode());
            dmFcstDimInfoVO.setBgCnName(commonViewVO.getBgCnName());
            pageOneList.add(dmFcstDimInfoVO);
        });
    }

    private List<DmFcstDimInfoVO> combineOtherPageCode(CommonViewVO commonViewVO, String userAccont, String roleId, List<DmFcstDimInfoVO> otherCustomVOList, Long customId,
                                                       Timestamp timestamp) {
        List<DmFcstDimInfoVO> otherPageDataList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(otherCustomVOList)) {
            String userId = commonViewVO.getUserId();
            String otherPageFlag = CommonConstant.allCombPageFlag.get(commonViewVO.getPageSymbol());
            // 获取另一个页面的数据组需要的数据
            CommonViewVO commonViewParam = new CommonViewVO();
            BeanUtils.copyProperties(commonViewVO, commonViewParam);
            commonViewParam.setPageFlag(CommonConstant.allCombPageFlag.get(commonViewVO.getPageSymbol()));
            List<DmFcstDimInfoVO> otherCustomSpartList = new ArrayList<>();
            otherCustomSpartList.addAll(otherCustomVOList);
            List<DmFcstDimInfoVO> otherSpartListForPage = ictCustomCommonService.getSpartListForPage(commonViewParam, otherCustomSpartList);
            String codeAttribute;
            if ("Y".equals(commonViewVO.getMainFlag()) && StringUtils.isBlank(commonViewVO.getCodeAttributes())) {
                codeAttribute = "全选";
            } else {
                codeAttribute = commonViewVO.getCodeAttributes();
            }
            List<DmFcstDimInfoVO> otherSpartList = getOtherDmFcstDimInfoList(commonViewVO, userAccont, roleId, customId, timestamp, otherSpartListForPage, userId, otherPageFlag, codeAttribute);
            // use_flag=PAGE的list
            combineOtherCustom(otherCustomVOList, customId, commonViewVO, timestamp, otherPageFlag, otherPageDataList);

            otherPageDataList.addAll(otherSpartList);
            return otherPageDataList;
        }
        return otherPageDataList;
    }

    @NotNull
    private List<DmFcstDimInfoVO> getOtherDmFcstDimInfoList(CommonViewVO commonViewVO, String userAccont, String roleId, Long customId, Timestamp timestamp, List<DmFcstDimInfoVO> otherSpartListForPage, String userId, String otherPageFlag, String codeAttribute) {
        List<DmFcstDimInfoVO> otherSpartList = new ArrayList<>();
        otherSpartListForPage.forEach(otherSpart -> {
            DmFcstDimInfoVO custom = ObjectCopyUtil.copy(otherSpart, DmFcstDimInfoVO.class);
            custom.setCustomId(customId);
            custom.setCustomCnName(commonViewVO.getCustomCnName());
            custom.setCreatedBy(userId);
            custom.setCreationDate(timestamp);
            custom.setLastUpdatedBy(userId);
            custom.setLastUpdateDate(timestamp);
            custom.setRoleId(roleId);
            custom.setUserId(userAccont);
            custom.setEnableFlag(ENABLE_FLAG_Y);
            custom.setSubEnableFlag(ENABLE_FLAG_Y);
            custom.setIsSeparate("N");
            custom.setUseFlag("CALC");
            if ("ANNUAL".equals(otherPageFlag)) {
                custom.setYtdFlag(commonViewVO.getYtdFlag());
            }
            custom.setViewFlag(commonViewVO.getViewFlag());
            custom.setOverseaFlag(commonViewVO.getOverseaFlag());
            custom.setPageFlag(commonViewVO.getPageFlag() + "_" + otherPageFlag);
            custom.setBgCnName(commonViewVO.getBgCnName());
            custom.setBgCode(commonViewVO.getBgCode());
            custom.setGranularityType(commonViewVO.getGranularityType());
            custom.setSoftwareMark(commonViewVO.getSoftwareMark());
            custom.setMainFlag(commonViewVO.getMainFlag());
            custom.setCodeAttributes(codeAttribute);
            custom.setRegionCode(commonViewVO.getRegionCode());
            custom.setRegionCnName(commonViewVO.getRegionCnName());
            custom.setRepofficeCode(commonViewVO.getRepofficeCode());
            custom.setRepofficeCnName(commonViewVO.getRepofficeCnName());
            otherSpartList.add(custom);
        });
        return otherSpartList;
    }

    private void combineOtherCustom(List<DmFcstDimInfoVO> otherCustomVOList,Long customId,CommonViewVO commonViewVO,Timestamp timestamp, String otherPageFlag,List<DmFcstDimInfoVO> otherPageDataList) {
        String userId = commonViewVO.getUserId();
        String roleId = commonViewVO.getRoleId();
        String userAccount = commonViewVO.getUserAccount();
        otherCustomVOList.forEach(customVO -> {
            DmFcstDimInfoVO custVO = ObjectCopyUtil.copy(customVO, DmFcstDimInfoVO.class);
            custVO.setCustomId(customId);
            custVO.setCustomCnName(commonViewVO.getCustomCnName());
            custVO.setCreatedBy(userId);
            custVO.setCreationDate(timestamp);
            custVO.setLastUpdatedBy(userId);
            custVO.setLastUpdateDate(timestamp);
            custVO.setRoleId(roleId);
            custVO.setUserId(userAccount);
            custVO.setEnableFlag(ENABLE_FLAG_Y);
            custVO.setSubEnableFlag(ENABLE_FLAG_Y);
            custVO.setIsSeparate("N");
            custVO.setUseFlag("PAGE");
            if ("ANNUAL".equals(otherPageFlag)) {
                custVO.setYtdFlag(commonViewVO.getYtdFlag());
            } else {
                custVO.setYtdFlag(null);
            }
            custVO.setViewFlag(commonViewVO.getViewFlag());
            custVO.setOverseaFlag(commonViewVO.getOverseaFlag());
            custVO.setPageFlag(commonViewVO.getPageFlag() + "_" + otherPageFlag);
            custVO.setBgCnName(commonViewVO.getBgCnName());
            custVO.setBgCode(commonViewVO.getBgCode());
            custVO.setSoftwareMark(commonViewVO.getSoftwareMark());
            custVO.setMainFlag(commonViewVO.getMainFlag());
            custVO.setCodeAttributes(commonViewVO.getCodeAttributes());
            custVO.setGranularityType(commonViewVO.getGranularityType());
            custVO.setRegionCode(commonViewVO.getRegionCode());
            custVO.setRegionCnName(commonViewVO.getRegionCnName());
            custVO.setRepofficeCode(commonViewVO.getRepofficeCode());
            custVO.setRepofficeCnName(commonViewVO.getRepofficeCnName());
            otherPageDataList.add(custVO);
        });
    }

    private CombTransformVO getCombTransformParam(CommonViewVO commonViewVO, Long userId, Long customId, DmFcstDataRefreshStatus dataRefreshStatus) {
        CombTransformVO combTransformVO = new CombTransformVO();
        combTransformVO.setUserId(userId);
        combTransformVO.setTaskId(dataRefreshStatus.getTaskId());
        combTransformVO.setCustomId(customId);
        combTransformVO.setGranularityType(commonViewVO.getGranularityType());
        // 获取加密key
        String plainText = ConfigUtil.getInstance().get16PlainText();
        combTransformVO.setEncryptKey(plainText);
        combTransformVO.setPageFlag(commonViewVO.getPageFlag());
        combTransformVO.setPageSymbol(commonViewVO.getPageSymbol());
        // 获取最新版本号
        combTransformVO.setVersionId(commonService.getVersionId(IndustryConstEnum.PAGE_TYPE.ANNUAL.getValue()));
        combTransformVO.setMonthVersionId(commonService.getVersionId(IndustryConstEnum.PAGE_TYPE.MONTH.getValue()));
        combTransformVO.setCostType(commonViewVO.getCostType());
        combTransformVO.setYtdFlag(commonViewVO.getYtdFlag());
        return combTransformVO;
    }
}
