/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.month;

import org.apache.commons.lang3.StringUtils;
import com.huawei.it.fcst.industry.pbi.vo.common.CommonBaseVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;
import java.util.Locale;

/**
 * IctMonthAnalysisVO Class
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Builder
@Getter
@Setter
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class IctMonthAnalysisVO extends CommonBaseVO implements Serializable {

    private static final long serialVersionUID = 5095330500637396975L;

    /**
     * 版本ID
     */
    private Long versionId;

    /**
     * 虚化ID
     */
    private Long customId;

    private List<Long> customIdList;

    /**
     * 组合ID
     */
    private Long combId;

    /**
     * 组合ID
     */
    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<Long> combIdList;

    /**
     * 组合名称
     */
    private String customCnName;

    /**
     * 基期
     */
    private Integer basePeriodId;

    /**
     * 会计年
     */
    private Integer periodYear;

    /**
     * 年区间
     */
    private String intervalYear;

    /**
     * 会计月
     */
    private Integer periodId;

    /**
     * 成本指数值
     */
    private Double costIndex;

    /**
     * YTD指数
     */
    private Double ytdCostIndex;

    /**
     * 降成本目标值
     */
    private Double costReductionRate;

    /**
     * 降成本目标名称
     */
    private String costReductionCnName;

    /**
     * 降成本目标层级
     */
    private String costReductionLevel;

    /**
     * 同环比值
     */
    private Double rate;

    /**
     * 同比
     */
    private String yoyRate;

    /**
     * 环比
     */
    private String popRate;

    /**
     * 同环比百分比值
     */
    private String ratePercent;

    /**
     * 同环比标识（YOY：同比，POP：环比）
     */
    private String rateFlag;

    /**
     * 成本金额
     */
    private Double rmbCostAmt;

    /**
     * 成本金额
     */
    private Double rmbCostAmtExp;

    /**
     * 数量
     */
    private Integer actualQty;

    /**
     * 权重值
     */
    private Double weightRate;

    /**
     * 百分比权重值
     */
    private String weightPercent;

    /**
     * 权重加密处理值
     */
    private String weightRateStr;

    /**
     * 权重排序
     */
    private String weightOrder;;

    /**
     * 重量级团队编码
     */
    private String prodRndTeamCode;

    /**
     * 重量级团队中文名称
     */
    private String prodRndTeamCnName;

    /**
     * L1-L4编码集合
     */
    private List<String> prodRndTeamCodeList;

    /**
     * 销售目录编码
     */
    private String prodListCode;

    /**
     * 销售目录中文名称
     */
    private String prodListCnName;

    /**
     * 产业目录编码
     */
    private String industryCatgCode;

    /**
     * 产业目录中文名称
     */
    private String industryCatgCnName;

    /**
     * 量纲中文名称
     */
    private String dimensionCnName;

    /**
     * 量纲子类编码
     */
    private String dimensionSubCategoryCode;

    /**
     * 量纲子类中文名称
     */
    private String dimensionSubCategoryCnName;

    /**
     * 各层级编码
     */
    private String groupCode;

    private String combGroupLevel;

    // 是否包含汇总组合
    private Boolean isContainComb;

    /**
     * 各层级编码集合
     */
    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<String> groupCodeList;

    private List<String> parentCodeList;

    /**
     * 虚化编码集合
     */
    private List<String> customGroupCodeList;

    /**
     * 正常子项编码集合
     */
    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<String> subGroupCodeList;

    /**
     * 组合子项编码集合
     */
    private List<String> combSubGroupCodeList;

    /**
     * 虚化子项编码集合
     */
    private List<String> customSubGroupCodeList;

    /**
     * 各层级中文名称
     */
    private String groupCnName;

    private String groupCnNameIndex;

    /**
     * 父级中文名称
     */
    private String parentCnName;

    /**
     * 子层级中文名称
     */
    private String subCnName;

    /**
     * 父级层级
     */
    private String parentLevel;

    /**
     * 重量级团队层级
     */
    private String teamLevel;

    /**
     * 路径FLAG：PROD_SPART(路径一)；DIMENSION(路径二)
     */
    private String viewFlag;

    /**
     * 是否补齐字段
     */
    private String appendFlag;

    /**
     * 地区部中文名称
     */
    private String regionCnName;

    /**
     * 代表处中文名称
     */
    private String repofficeCnName;

    /**
     * BG中文名称
     */
    private String bgCnName;

    /**
     * 国内/海外中文名称
     */
    private String overseaFlagCnName;

    /**
     * SPART范围中文名称
     */
    private String mainFlagCnName;

    /**
     * PBI目录树中文名称
     */
    private String granularityTypeCnName;

    /**
     * 是否需要虚化
     */
    private Boolean isNeedBlur;

    /**
     * 是否多选
     */
    private Boolean isMultipleSelect;

    /**
     * 是否显示成本分布图
     */
    private Boolean isShowCostDistribution;

    /**
     * 是否显示权重图、多子项指数图
     */
    private Boolean isShowChildrenChart;

    /**
     * 导出标识
     */
    private boolean exportFlag;

    /**
     * 动态表名称前缀
     */
    private String tablePreFix;

    /**
     * 分层级编码对应中文名称
     */
    private List<String> groupCnNameList;

    /**
     * L0
     */
    private String lv0ProdRdTeamCnName;

    /**
     * L1
     */
    private String lv1ProdRdTeamCnName;

    /**
     * L2
     */
    private String lv2ProdRdTeamCnName;

    /**
     * L3
     */
    private String lv3ProdRdTeamCnName;

    /**
     * L3.5
     */
    private String lv4ProdRdTeamCnName;

    private String begin;

    private String end;

    /**
     * 对比分析入参
     */
    CompareAnalysisVO compareAnalysisVO;

    public String getTablePreFix() {
        if (StringUtils.isBlank(this.getCostType())) {
            return null;
        }
        return this.getCostType().toLowerCase(Locale.ROOT).concat("_")
                .concat(this.getGranularityType().toLowerCase(Locale.ROOT));
    }

    /**
     * 虚化表名称前缀
     */
    private String blurTablePreFix;

    public String getBlurTablePreFix() {
        if (StringUtils.isBlank(this.getCostType())) {
            return null;
        }
        return this.getCostType().toLowerCase(Locale.ROOT);
    }

}