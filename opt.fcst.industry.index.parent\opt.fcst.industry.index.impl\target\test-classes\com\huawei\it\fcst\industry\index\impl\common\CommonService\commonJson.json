{"DmFocViewInfoVO": {"id": 1, "industryOrg": "ICT", "lv0ProdRndTeamCode": "test", "lv0ProdRdTeamCnName": "test", "lv1ProdRndTeamCode": "test", "lv1ProdRdTeamCnName": "test", "lv2ProdRndTeamCode": "test", "lv2ProdRdTeamCnName": "test", "lv3ProdRndTeamCode": "test", "lv3ProdRdTeamCnName": "test", "lv0ProdListCode": "test", "lv0ProdListCnName": "test", "prodRndTeamCode": "test", "l3CegCode": "test", "l3CegCnName": "test", "categoryCode": "test", "categoryCnName": "test", "itemCode": "test", "itemCnName": "test", "createdBy": "test", "creationDate": 1695025370648, "lastUpdatedBy": "test", "lastUpdateDdate": 1695025370648, "delFlag": "test", "viewFlag": "test", "viewFlagValue": "test", "groupCode": "test", "groupLevel": "test", "groupCnName": "test", "versionId": 1, "permissionFlag": "test", "removeFlag": "test", "weightRate": 1.0, "granularityType": "test", "subEnableFlag": "test", "isCombination": true, "customId": 1, "parentCode": "test", "parentCnName": "test", "pageFlag": "test", "l1Name": "test", "l2Name": "test", "dimensionCode": "test", "dimensionCnName": "test", "dimensionSubCategoryCode": "test", "dimensionSubCategoryCnName": "test", "dimensionSubDetailCode": "test", "dimensionSubDetailCnName": "test", "userId": "test", "roleId": "test", "enableFlag": "test", "caliberFlag": "test", "overseaFlag": "test", "l3CegShortCnName": "test", "l4CegShortCnName": "test", "l4CegCnName": "test", "l4CegCode": "test", "customCnName": "test", "isSeparate": "test", "connectCode": "test", "connectParentCode": "test", "children": [{}], "topItemCode": "test", "itemNum": 1}, "List<LookupItemVO>": [{"lastUpdateDate": 1695000401057, "selfItemCode": "test", "itemCode": "test", "language": "test", "itemName": "test", "serialVersionUID": 1, "lastUpdateUserCN": "test", "scope": "test", "rowIdx": 1, "itemIndex": 1, "lastUpdatedBy": 1, "creationUserCN": "test", "appName": "test", "itemAttr5": "test", "itemAttr4": "test", "itemAttr6": "test", "creationDate": 1695000401057, "itemDesc": "test", "itemId": 1, "itemAttr1": "test", "createdBy": 1, "itemAttr3": "test", "itemAttr2": "test", "status": 1}], "MonthAnalysisVO": {"industryOrg": "ICT", "overseaFlag": "test", "nextMonthGroupName": "test", "fileName": "test", "caliberFlag": "test", "lv2ProdRdTeamCnName": ["test"], "groupCodeOrder": "test", "subGroupCodeList": ["test"], "pageSize": 1, "parentLevel": "test", "l1NameList": ["test"], "reverseViewFlag": true, "dimensionSubDetailCodeList": ["test"], "periodStartTime": 1, "combinaSubGroupCodeList": ["test"], "groupLevel": "test", "groupCode": "test", "isMultipleSelect": true, "multiLevel": "test", "topCateVersionId": 1, "dmsCodeList": ["test"], "dimensionSubDetailCnName": ["test"], "yoyFlag": "test", "periodEndTime": 1, "isContainComb": true, "combinaCodeList": ["test"], "versionId": 1, "customIdList": [1], "cnName": "test", "lv3CegCnName": "test", "prodRndTeamCode": "test", "l2Name": "test", "lv4CegCnName": "test", "dimensionSubCategoryCnName": ["test"], "granularityType": "test", "categoryCnName": "test", "l2NameList": ["test"], "isShowChildContent": true, "customId": "test", "l1Name": "test", "groupCodeList": ["test"], "teamLevel": "test", "purLevel": "test", "profitsName": "test", "parentCodeList": ["test"], "dimensionSubcategoryCodeList": ["test"], "purCode": "test", "lv1ProdRdTeamCnName": ["test"], "dimensionCnName": ["test"], "prodRndTeamCodeList": ["test"], "purCodeList": ["test"], "lv2DimensionSet": ["test"], "lv0DimensionSet": ["test"], "dimensionCodeList": ["test"], "lv1DimensionSet": ["test"], "viewFlag": "test", "dmsCode": "test", "curPage": 1, "basePeriodId": 1, "lv3ProdRdTeamCnName": ["test"], "lv3DimensionSet": ["test"], "combParentCodeList": ["test"], "nextMonthGroupLevel": "test", "lv0ProdListCode": "test"}, "List<ViewInfoVO>": [{"id": 1, "costType": "P", "tablePreFix": "dm_foc", "lv0ProdRndTeamCode": "test", "lv0ProdRnTeamName": "test", "lv1ProdRndTeamCode": "test", "lv1ProdRnTeamName": "test", "lv2ProdRndTeamCode": "test", "lv2ProdRnTeamName": "test", "lv3ProdRndTeamCode": "test", "lv3ProdRnTeamName": "test", "l3CegCode": "test", "l3CegName": "test", "categoryCode": "test", "categoryName": "test", "itemCode": "test", "itemName": "test", "delFlag": "test", "viewFlag": "test", "selfParentDimensionValue": "test", "dimensionValue": "test", "dimensionDisplayValue": "test", "createdBy": 1, "creationDate": 1695028389583, "creationUserCN": "test", "lastUpdatedBy": 1, "lastUpdateDate": 1695028389583, "lastUpdateUserCN": "test", "rowIdx": 1, "appName": "test", "scope": "test"}], "UserVO": {"appNames": ["test"], "costType": "P", "endDate": *************, "lastUpdateDate": 1695030731897, "cacheAppNames": {"words": [1], "wordsInUse": 1, "sizeIsSticky": true}, "globalUserId": 1, "consumerSubappId": "test", "uuid": "test", "employeeNumber": "test", "employeeNameEng": "test", "defaultOrg": "test", "currentProgramIds": [1], "serialVersionUID": 1, "lastUpdateUserCN": "test", "apartmentCode": "test", "scope": "test", "rowIdx": 1, "currentRole": {"roleSort": 1, "roleId": 1, "roleName": "test", "roleNameCn": "test", "roleNameEn": "test", "roleDesc": "test", "roleENDesc": "test", "defaultUrl": "test", "status": 1, "applyStatus": 1, "apartmentCode": "test", "owner": {"userId": 1, "userAccount": "test", "employeeNumber": "test", "userCN": "test"}, "userAdminRole": 1, "grantedOperations": [{"operationId": 1, "operationCode": "test", "operationDesc": "test", "operationUrl": ["test"], "metadata": "test", "metadataMap": {"test": "test"}, "resource": {"resourceCode": "test", "resourceDesc": "test", "resourceType": "test", "resourcePrefix": "test", "appNames": ["test"], "operations": [null], "createdBy": 1, "creationDate": *************, "creationUserCN": "test", "lastUpdatedBy": 1, "lastUpdateDate": *************, "lastUpdateUserCN": "test", "rowIdx": 1, "appName": "test", "scope": "test"}, "appendKey": "test", "multiAppendKey": "test", "createdBy": 1, "creationDate": *************, "creationUserCN": "test", "lastUpdatedBy": 1, "lastUpdateDate": *************, "lastUpdateUserCN": "test", "rowIdx": 1, "appName": "test", "scope": "test"}], "personalPermissions": ["test"], "cachePersonalPermissions": {"words": [1], "wordsInUse": 1, "sizeIsSticky": true}, "noPermissionUrl": ["test"], "permissionUrl": ["test"], "approvedByOld": "test", "approveMode": "test", "roleDimension": [{"dimension": {"id": 1, "dimensionCode": "test", "dimensionName": "test", "dimensionField": "test", "dimensionType": 1, "dimensionUrl": "test", "applyStatus": 1, "priority": 1, "createdBy": 1, "creationDate": 1695030731887, "creationUserCN": "test", "lastUpdatedBy": 1, "lastUpdateDate": 1695030731887, "lastUpdateUserCN": "test", "rowIdx": 1, "appName": "test", "scope": "test"}, "priority": 1, "requisite": 1, "createdBy": 1, "creationDate": 1695030731887, "creationUserCN": "test", "lastUpdatedBy": 1, "lastUpdateDate": 1695030731887, "lastUpdateUserCN": "test", "rowIdx": 1, "appName": "test", "scope": "test"}], "roleChk": "test", "roleList": ["test"], "currentScope": "test", "appNames": ["test"], "cacheAppNames": {"words": [1], "wordsInUse": 1, "sizeIsSticky": true}, "createdBy": 1, "creationDate": 1695030731887, "creationUserCN": "test", "lastUpdatedBy": 1, "lastUpdateDate": 1695030731887, "lastUpdateUserCN": "test", "rowIdx": 1, "appName": "test", "scope": "test"}, "lang": "test", "userCN": "test", "email": "test", "lastUpdatedBy": 1, "creationUserCN": "test", "fname": "test", "defaultRole": "test", "appName": "test", "dept": "test", "programValidity": "test", "creationDate": 1695030731897, "userId": 1, "currentPrograms": [{"programId": 1, "name": "test", "description": "test", "descriptionEn": "test", "status": 1, "applyStatus": 1, "items": [{"dimension": {"id": 1, "dimensionCode": "getDimensionWithTree", "dimensionName": "test", "dimensionField": "test", "dimensionType": 1, "dimensionUrl": "test", "applyStatus": 1, "priority": 1, "createdBy": 1, "creationDate": 1695030731893, "creationUserCN": "test", "lastUpdatedBy": 1, "lastUpdateDate": 1695030731893, "lastUpdateUserCN": "test", "rowIdx": 1, "appName": "test", "scope": "test"}, "dimensionInfo": "test", "values": [{"key": "ALL", "value": "test", "valueEN": "test"}, {"key": "test_LV0", "value": "test", "valueEN": "test"}, {"key": "test_LV1", "value": "test", "valueEN": "test"}, {"key": "test_LV2", "value": "test", "valueEN": "test"}, {"key": "test_LV3", "value": "test", "valueEN": "test"}], "programId": 1, "programItemId": 1, "type": "test", "value": "test", "displayValue": "test", "displayValueEN": "test", "createdBy": 1, "creationDate": *************, "creationUserCN": "test", "lastUpdatedBy": 1, "lastUpdateDate": *************, "lastUpdateUserCN": "test", "rowIdx": 1, "appName": "test", "scope": "test"}], "org": "test", "approvedByOld": "test", "adminProgram": "test", "programChk": "test", "programList": ["test"], "currentScope": "test", "createdBy": 1, "creationDate": *************, "creationUserCN": "test", "lastUpdatedBy": 1, "lastUpdateDate": *************, "lastUpdateUserCN": "test", "rowIdx": 1, "appName": "test", "scope": "test"}], "validRoles": [{"roleSort": 1, "roleId": 1, "roleName": "test", "roleNameCn": "test", "roleNameEn": "test", "roleDesc": "test", "roleENDesc": "test", "defaultUrl": "test", "status": 1, "applyStatus": 1, "apartmentCode": "test", "owner": {"userId": 1, "userAccount": "test", "employeeNumber": "test", "userCN": "test"}, "userAdminRole": 1, "grantedOperations": [{"operationId": 1, "operationCode": "test", "operationDesc": "test", "operationUrl": ["test"], "metadata": "test", "metadataMap": {"test": "test"}, "resource": {"resourceCode": "test", "resourceDesc": "test", "resourceType": "test", "resourcePrefix": "test", "appNames": ["test"], "operations": [null], "createdBy": 1, "creationDate": *************, "creationUserCN": "test", "lastUpdatedBy": 1, "lastUpdateDate": *************, "lastUpdateUserCN": "test", "rowIdx": 1, "appName": "test", "scope": "test"}, "appendKey": "test", "multiAppendKey": "test", "createdBy": 1, "creationDate": *************, "creationUserCN": "test", "lastUpdatedBy": 1, "lastUpdateDate": *************, "lastUpdateUserCN": "test", "rowIdx": 1, "appName": "test", "scope": "test"}], "personalPermissions": ["test"], "cachePersonalPermissions": {"words": [1], "wordsInUse": 1, "sizeIsSticky": true}, "noPermissionUrl": ["test"], "permissionUrl": ["test"], "approvedByOld": "test", "approveMode": "test", "roleDimension": [{"dimension": {"id": 1, "dimensionCode": "test", "dimensionName": "test", "dimensionField": "test", "dimensionType": 1, "dimensionUrl": "test", "applyStatus": 1, "priority": 1, "createdBy": 1, "creationDate": *************, "creationUserCN": "test", "lastUpdatedBy": 1, "lastUpdateDate": *************, "lastUpdateUserCN": "test", "rowIdx": 1, "appName": "test", "scope": "test"}, "priority": 1, "requisite": 1, "createdBy": 1, "creationDate": *************, "creationUserCN": "test", "lastUpdatedBy": 1, "lastUpdateDate": *************, "lastUpdateUserCN": "test", "rowIdx": 1, "appName": "test", "scope": "test"}], "roleChk": "test", "roleList": ["test"], "currentScope": "test", "appNames": ["test"], "cacheAppNames": {"words": [1], "wordsInUse": 1, "sizeIsSticky": true}, "createdBy": 1, "creationDate": *************, "creationUserCN": "test", "lastUpdatedBy": 1, "lastUpdateDate": *************, "lastUpdateUserCN": "test", "rowIdx": 1, "appName": "test", "scope": "test"}], "areaCode": "test", "createdBy": 1, "userAccount": "test", "displayNameCn": "test", "tenantId": "test", "displayNameEn": "test", "msaAppId": "test", "scopes": ["test"], "userType": "test", "currentOrg": "test", "coalitionPermission": true}, "DmFocPageInfoVO": {"costType": "P", "lastUpdatedBy": 1, "granule": "test", "caliberFlag": "test", "defaultFlag": "test", "dataRange": "test", "roleId": "test", "lastUpdateDate": *************, "pageFlag": "test", "delFlag": "test", "creationDate": *************, "pageId": 1, "saveThreshold": "{\"costType\":\"P\",\"userView\":\"0\",\"userViewName\":\"ICT-专项采购认证部-品类\",\"lv1\":[\"211414\"],\"lv1Name\":\"\",,\"lv1Flag\":[\"\"],\"lv2\":\"42425\",\"lv2Name\":\"\",\"lv3\":\"1111\",\"lv3Name\":\"\",\"l1\":\"\",\"l1Name\":\"\",\"l2\":\"\",\"l2Name\":\"\",\"specDept\":\"\",\"specDeptName\":\"\",\"category\":\"\",\"categoryName\":\"\"}", "pageName": "test", "userId": "test", "versionId": 1, "serialVersionUID": 1, "createdBy": 1}}