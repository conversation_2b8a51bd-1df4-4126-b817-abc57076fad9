/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.month;

import static org.mockito.ArgumentMatchers.any;

import com.huawei.it.fcst.industry.index.dao.IAnnualAmpDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeMonthYoyDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMonthYoyDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTotalMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTotalMonthYoyDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocVersionDao;
import com.huawei.it.fcst.industry.index.dao.IMadeAnnualAmpDao;
import com.huawei.it.fcst.industry.index.dao.ITotalAnnualAmpDao;
import com.huawei.it.fcst.industry.index.service.common.ICommonService;
import com.huawei.it.fcst.industry.index.utils.FileProcessUtis;
import com.huawei.it.fcst.industry.index.vo.annual.AnnualAnalysisVO;
import com.huawei.it.fcst.industry.index.vo.annual.DmFocAnnualAmpVO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoDTO;
import com.huawei.it.fcst.industry.index.vo.month.CompareAnalysisVO;
import com.huawei.it.fcst.industry.index.vo.month.DmFocMonthCostIdxVO;
import com.huawei.it.fcst.industry.index.vo.month.DmFocMonthYoyVO;
import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;

import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AsyncQueryServiceTest Class
 *
 * <AUTHOR>
 * @since 2023/11/9
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest( {FileProcessUtis.class})
public class AsyncQueryServiceTest {
    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncQueryService.class);
    @InjectMocks
    private AsyncQueryService asyncQueryService;

    @Mock
    private IDmFocMonthCostIdxDao dmFocMonthCostIdxDao;

    @Mock
    private IDmFocMadeMonthCostIdxDao dmFocMadeMonthCostIdxDao;

    @Mock
    private IDmFocTotalMonthCostIdxDao dmFocTotalMonthCostIdxDao;

    @Mock
    private IAnnualAmpDao annualAmpDao;

    @Mock
    private IMadeAnnualAmpDao madeAnnualAmpDao;

    @Mock
    private ITotalAnnualAmpDao totalAnnualAmpDao;

    @Mock
    private ICommonService commonService;

    @Mock
    private IDmFocMonthYoyDao dmFocMonthYoyDao;

    @Mock
    private IDmFocMadeMonthYoyDao dmFocMadeMonthYoyDao;

    @Mock
    private IDmFocTotalMonthYoyDao dmFocTotalMonthYoyDao;

    @Mock
    private IDmFocVersionDao dmFocVersionDao;

    @Test
    public void findPurchaseCostIdxList() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setCompareAnalysisVO(CompareAnalysisVO.builder().isCompareFlag(false).build());
        List<DmFocMonthCostIdxVO> dmFocPriceIndexVOList = new ArrayList<>();
        Mockito.when(dmFocMonthCostIdxDao.findDmFocPriceIndexVOList(monthAnalysisVO)).thenReturn(dmFocPriceIndexVOList);
        Assert.assertNotNull(asyncQueryService.findPurchaseCostIdxList(monthAnalysisVO));

        monthAnalysisVO.setCompareAnalysisVO(CompareAnalysisVO.builder().isCompareFlag(true).build());
        Assert.assertNotNull(asyncQueryService.findPurchaseCostIdxList(monthAnalysisVO));
    }

    @Test
    public void findMadeCostIdxList() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setCompareAnalysisVO(CompareAnalysisVO.builder().isCompareFlag(false).build());
        Map<String, Long> startEndTime = new HashMap<>();
        startEndTime.put("start", 10L);
        startEndTime.put("end", 10L);
        PowerMockito.doReturn(startEndTime).when(dmFocMadeMonthCostIdxDao).findStartEndTime(any(),any());
        Assert.assertNotNull(asyncQueryService.findMadeCostIdxList(monthAnalysisVO));

        monthAnalysisVO.setCompareAnalysisVO(CompareAnalysisVO.builder().isCompareFlag(true).build());
        Assert.assertNotNull(asyncQueryService.findMadeCostIdxList(monthAnalysisVO));
    }

    @Test
    public void findTotalCostIdxList() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setCompareAnalysisVO(CompareAnalysisVO.builder().isCompareFlag(true).build());
        Map<String, Long> startEndTime = new HashMap<>();
        startEndTime.put("start", 10L);
        startEndTime.put("end", 10L);
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(1L);
        PowerMockito.doReturn(15L).when(commonService).findActualMonthNum(any());
        List<DmFocMonthCostIdxVO> dmFocPriceIndexVOList = new ArrayList<>();
        Mockito.when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(dmFocVersionInfoDTO);
        PowerMockito.doReturn(dmFocPriceIndexVOList).when(dmFocTotalMonthCostIdxDao).findTotalPriceIndexVOList(any());
        Assert.assertNotNull(asyncQueryService.findTotalCostIdxList(monthAnalysisVO));

        monthAnalysisVO.setCompareAnalysisVO(CompareAnalysisVO.builder().isCompareFlag(false).build());
        Assert.assertNotNull(asyncQueryService.findTotalCostIdxList(monthAnalysisVO));
    }

    @Test
    public void multiIndustryCostChartMultiSelect() {
        AnnualAnalysisVO annualAnalysisVO=new AnnualAnalysisVO();
        Assert.assertNotNull(asyncQueryService.multiIndustryCostChartMultiSelect(annualAnalysisVO));
    }

    @Test
    public void madeMultiIndustryCostChartMultiSelect() {
        AnnualAnalysisVO annualAnalysisVO=new AnnualAnalysisVO();
        Assert.assertNotNull(asyncQueryService.madeMultiIndustryCostChartMultiSelect(annualAnalysisVO));
    }

    @Test
    public void totalMultiIndustryCostChartMultiSelect() {
        AnnualAnalysisVO annualAnalysisVO=new AnnualAnalysisVO();
        Assert.assertNotNull(asyncQueryService.totalMultiIndustryCostChartMultiSelect(annualAnalysisVO));
    }

    @Test
    public void allIndustryCost() {
        AnnualAnalysisVO annualAnalysisVO=new AnnualAnalysisVO();
        Assert.assertNotNull(asyncQueryService.allIndustryCost(annualAnalysisVO));
    }

    @Test
    public void madeAllIndustryCost() {
        AnnualAnalysisVO annualAnalysisVO=new AnnualAnalysisVO();
        Assert.assertNotNull(asyncQueryService.madeAllIndustryCost(annualAnalysisVO));
    }

    @Test
    public void totalAllIndustryCost() {
        AnnualAnalysisVO annualAnalysisVO=new AnnualAnalysisVO();
        Assert.assertNotNull(asyncQueryService.totalAllIndustryCost(annualAnalysisVO));
    }

    @Test
    public void findTotalCostIdxDataList() {
        List<DmFocMonthCostIdxVO> priceIndexVOList = new ArrayList<>();
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setCompareAnalysisVO(CompareAnalysisVO.builder().isCompareFlag(true).build());
        Map<String, Long> startEndTime = new HashMap<>();
        startEndTime.put("start", 10L);
        startEndTime.put("end", 10L);
        PowerMockito.doReturn(startEndTime).when(dmFocMadeMonthCostIdxDao).findStartEndTime(any(),any());
        List<DmFocMonthCostIdxVO> dmFocPriceIndexVOList = new ArrayList<>();
        PowerMockito.doReturn(dmFocPriceIndexVOList).when(dmFocMonthCostIdxDao).findDmFocPriceIndexVOList(any());
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(1L);
        Mockito.when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(dmFocVersionInfoDTO);
        asyncQueryService.findTotalCostIdxDataList(priceIndexVOList, monthAnalysisVO);
        Assertions.assertNotNull(dmFocPriceIndexVOList);

        monthAnalysisVO.setCompareAnalysisVO(CompareAnalysisVO.builder().isCompareFlag(false).build());
        asyncQueryService.findTotalCostIdxDataList(priceIndexVOList, monthAnalysisVO);
        Assertions.assertNotNull(dmFocPriceIndexVOList);
    }

    @Test
    public void findMonthPurchaseYoyList() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        Assert.assertNotNull(asyncQueryService.findMonthPurchaseYoyList(monthAnalysisVO));
    }

    @Test
    public void findMonthPurchasePopList() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        Assert.assertNotNull(asyncQueryService.findMonthPurchasePopList(monthAnalysisVO));
    }

    @Test
    public void findMonthManufactureYoyList() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        Map<String, Long> startEndTime = new HashMap<>();
        startEndTime.put("start", 10L);
        startEndTime.put("end", 10L);
        PowerMockito.doReturn(startEndTime).when(dmFocMadeMonthCostIdxDao).findStartEndTime(any(),any());
        Assert.assertNotNull(asyncQueryService.findMonthManufactureYoyList(monthAnalysisVO));
    }

    @Test
    public void findMonthManufacturePopList() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        Map<String, Long> startEndTime = new HashMap<>();
        startEndTime.put("start", 10L);
        startEndTime.put("end", 10L);
        PowerMockito.doReturn(startEndTime).when(dmFocMadeMonthCostIdxDao).findStartEndTime(any(),any());
        Assert.assertNotNull(asyncQueryService.findMonthManufacturePopList(monthAnalysisVO));
    }

    @Test
    public void findMonthTotalYoyList() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        Map<String, Long> startEndTime = new HashMap<>();
        startEndTime.put("start", 10L);
        startEndTime.put("end", 10L);
        PowerMockito.doReturn(startEndTime).when(dmFocMadeMonthCostIdxDao).findStartEndTime(any(),any());
        Assert.assertNotNull(asyncQueryService.findMonthTotalYoyList(monthAnalysisVO));
    }

    @Test
    public void findMonthTotalPopList() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        Map<String, Long> startEndTime = new HashMap<>();
        startEndTime.put("start", 10L);
        startEndTime.put("end", 10L);
        PowerMockito.doReturn(startEndTime).when(dmFocMadeMonthCostIdxDao).findStartEndTime(any(),any());
        Assert.assertNotNull(asyncQueryService.findMonthTotalPopList(monthAnalysisVO));
    }

    @Test
    public void findTotalYoyAndPopDataList() {
        MonthAnalysisVO monthAnalysisVO=new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setYoyOrPop("YOY");
        List<DmFocMonthYoyVO> yoyList = new ArrayList<>();
        List<DmFocMonthYoyVO> popList = new ArrayList<>();
        Map<String, Long> startEndTime = new HashMap<>();
        startEndTime.put("start", 10L);
        startEndTime.put("end", 10L);
        PowerMockito.doReturn(startEndTime).when(dmFocMadeMonthCostIdxDao).findStartEndTime(any(),any());
        asyncQueryService.findTotalYoyAndPopDataList(monthAnalysisVO,yoyList,popList);
        Assertions.assertNull(null);

        monthAnalysisVO.setYoyOrPop("POP");
        asyncQueryService.findTotalYoyAndPopDataList(monthAnalysisVO,yoyList,popList);
        Assertions.assertNull(null);
    }

    @Test
    public void findMultiTotalAnnualAmpDataList() {
        List<DmFocAnnualAmpVO> allAnnualAmpDataList = new ArrayList<>();
        List<DmFocAnnualAmpVO> allAnnualAmpDataList2 = new ArrayList<>();
        AnnualAnalysisVO annualAnalysisVO=new AnnualAnalysisVO();
        Map<String, Long> startEndTime = new HashMap<>();
        startEndTime.put("start", 10L);
        startEndTime.put("end", 10L);
        PowerMockito.doReturn(startEndTime).when(dmFocMadeMonthCostIdxDao).findStartEndTime(any(),any());
        asyncQueryService.findMultiTotalAnnualAmpDataList(allAnnualAmpDataList,allAnnualAmpDataList2,annualAnalysisVO);
        Assertions.assertNull(null);
    }

    @Test
    public void findTotalAllIndustryCost() {
        List<DmFocAnnualAmpVO> allAnnualIndustryCost = new ArrayList<>();
        AnnualAnalysisVO annualAnalysisVO=new AnnualAnalysisVO();
        Map<String, Long> startEndTime = new HashMap<>();
        startEndTime.put("start", 10L);
        startEndTime.put("end", 10L);
        PowerMockito.doReturn(startEndTime).when(dmFocMadeMonthCostIdxDao).findStartEndTime(any(),any());
        asyncQueryService.findTotalAllIndustryCost(allAnnualIndustryCost,annualAnalysisVO);
        Assertions.assertNull(null);
    }
}