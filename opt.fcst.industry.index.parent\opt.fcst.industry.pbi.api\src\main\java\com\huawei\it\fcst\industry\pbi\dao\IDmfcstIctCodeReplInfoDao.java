/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.dao;

import com.huawei.it.fcst.industry.pbi.vo.replace.DmFocReplVO;
import com.huawei.it.fcst.industry.pbi.vo.replace.ReplaceSearchVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * IReplaceManagementDao Class
 *
 * <AUTHOR>
 * @since 2024/7/10
 */
public interface IDmfcstIctCodeReplInfoDao {

    PagedResult<DmFocReplVO> findInfoPageList(@Param("searchVO") ReplaceSearchVO replaceSearchVO, @Param("pageVO")PageVO pageVO);

    List<DmFocReplVO> findReplInfoList(ReplaceSearchVO replaceSearchVO);

    List<DmFocReplVO> findReplInfoDropDownList(ReplaceSearchVO replaceSearchVO);

    List<String> findAllRelationName(ReplaceSearchVO replaceSearchVO);

    List<DmFocReplVO> findProductProdCode(@Param("prodCnNameList") Set<String> prodCnNameList);

    void createDmCodeReplInfoList(@Param("list") List<DmFocReplVO> replInfoListWithVersion);

    int delCodeReplInfoListByVersionId(Long versionId);

    Long getReplAutoKey();

    List<DmFocReplVO> findProductDimList(ReplaceSearchVO replaceSearchVO);

    List<DmFocReplVO> findBgCodeFromProductDim();

    PagedResult<DmFocReplVO> findSpartCodePageList(@Param("searchVO") ReplaceSearchVO replaceSearchVO,@Param("pageVO")PageVO pageVO);

    List<String> findAllSpartCodeList();
}
