/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.util;

import com.huawei.it.fcst.annotations.ExportAttribute;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.util.ReflectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 导入导出工具类
 *
 * <AUTHOR>
 * @since 2022-10-27
 */
@Slf4j
public class ExcelExportUtil<T> {

    // 写入数据的起始行
    private int rowIndex;

    // 写入样式的起始行
    private int styleIndex;

    // 对象字节码
    private Class clazz;

    // 对象中的所有属性
    private Field[] fields;

    public int getStyleIndex() {
        return styleIndex;
    }

    public void setStyleIndex(int styleIndex) {
        this.styleIndex = styleIndex;
    }

    public int getRowIndex() {
        return rowIndex;
    }

    public void setRowIndex(int rowIndex) {
        this.rowIndex = rowIndex;
    }

    public Field[] getFields() {
        return fields;
    }

    public void setFields(Field[] fields) {
        this.fields = fields;
    }

    public Class getClazz() {
        return clazz;
    }

    public void setClazz(Class clazz) {
        this.clazz = clazz;
    }

    public ExcelExportUtil() {
    }

    public ExcelExportUtil(int rowIndex, int styleIndex, Class clazz) {
        this.rowIndex = rowIndex;
        this.styleIndex = styleIndex;
        this.clazz = clazz;
        this.fields = clazz.getDeclaredFields();
    }
    
    /**
     * 下载Excel到浏览器
     *
     * @param workbook 工作簿
     * @param fileName 导出文件名
     * @param response 响应
     * @throws Exception
     */
    public static void downloadExcel(Workbook workbook, String fileName, HttpServletResponse response)
            throws Exception {
        fileName = URLEncoder.encode(fileName, "UTF-8");
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        response.setHeader("content-disposition", "attachment;filename=" + new String(fileName.getBytes("ISO8859-1"), "UTF-8"));
        response.setHeader("filename", fileName);
        workbook.write(response.getOutputStream());
    }

    /**
     * 填充Sheet页数据
     *
     * @param sheet 要导出的工作工作表
     * @param dataList 数据集
     * @throws Exception
     */
    public void fillSheetData(Sheet sheet, List<T> dataList) throws Exception {
        // 2、提取工作表中的公共的样式
        setCellStyleAndValues(dataList, sheet);
    }

    private void setCellStyleAndValues(List<T> dataList, Sheet sheet) throws IllegalAccessException {
        // 3、提取公共的样式
        CellStyle[] cellStyles = getTemplateStyles(sheet.getRow(styleIndex));

        // 4、创建每一行每一列的数据
        AtomicInteger atomicInteger = new AtomicInteger(rowIndex);
        for (T data : dataList) {
            // 5、创建要写入的行数据
            Row row = sheet.createRow(atomicInteger.getAndIncrement());
            for (int idx = 0; idx < cellStyles.length; idx++) {
                // 设置单元格样式
                Cell cell = row.createCell(idx);
                cell.setCellStyle(cellStyles[idx]);
                // 设置单元格值
                setCellValue(data, idx, cell);
            }
        }
    }

    private void setCellValue(T data, int index, Cell cell) throws IllegalAccessException {
        for (Field field : fields) {
            if (!field.isAnnotationPresent(ExportAttribute.class)) {
                continue;
            }
            ReflectionUtils.makeAccessible(field);
            setCellValueByDataType(data, index, cell, field);
        }
    }

    private void setCellValueByDataType(T data, int index, Cell cell, Field field) throws IllegalAccessException {
        ExportAttribute exportAttribute = field.getAnnotation(ExportAttribute.class);
        if (index == exportAttribute.sort()) {
            Object dataValue = field.get(data);
            if (dataValue != null) {
                if ("Number".equals(exportAttribute.dataType())) {
                    cell.setCellValue(Double.valueOf(dataValue.toString()));
                }else {
                    cell.setCellValue(dataValue.toString());
                }
            }
        }
    }

    /**
     * 获取模板样式
     *
     * @param dataRow 行数据
     * @return CellStyle[] 单元格饲养数组
     */
    public static CellStyle[] getTemplateStyles(Row dataRow) {
        short lastCellNum = dataRow.getLastCellNum();
        CellStyle[] cellStyle = new CellStyle[lastCellNum];
        for (int i = 0; i < lastCellNum; i++) {
            cellStyle[i] = dataRow.getCell(i).getCellStyle();
        }
        return cellStyle;
    }

    /**
     * 根据Excel导出模板创建工作簿
     *
     * @param templatePath 导出模板路径
     * @return Workbook 工作簿
     * @throws Exception
     */
    public static XSSFWorkbook getWorkbookByTemplate(String templatePath) throws Exception {
        // 1、加载导出模板
        InputStream inputStream = null;
        XSSFWorkbook workbook = null;
        try {
            Resource resource = new ClassPathResource(templatePath);
            inputStream = resource.getInputStream();
            workbook = new XSSFWorkbook(inputStream);
        } catch (IOException e) {
            throw new RuntimeException("创建workbook失败");
        } finally {
            closeIO(inputStream);
        }
        // 2、根据模板创建工作簿
        return workbook;
    }

    private static void closeIO(InputStream inputStream) throws IOException {
        if (inputStream != null) {
            try {
                inputStream.close();
            } catch (IOException e) {
                log.error("close IOException.{}", e);
            } finally {
                inputStream.close();
            }
        }
    }
}