/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.config;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * DmFocTopCateInfoDTOTest Class
 *
 * <AUTHOR>
 * @since 2023/4/23
 */
public class DmFocTopCateInfoDTOTest extends BaseVOCoverUtilsTest<DmFocTopCateInfoDTO> {

    @Override
    protected Class<DmFocTopCateInfoDTO> getTClass() {
        return DmFocTopCateInfoDTO.class;
    }

    @Test
    public void testMethod() {
        DmFocTopCateInfoDTO dimensionParamVO = new DmFocTopCateInfoDTO();
        dimensionParamVO.setVersionId(100L);
        dimensionParamVO.getVersionId();
        dimensionParamVO.setDelFlag("Y");
        dimensionParamVO.getDelFlag();
        dimensionParamVO.setPeriodYear("2023");
        dimensionParamVO.getPeriodYear();
        getSetProdRdTeam(dimensionParamVO);
        dimensionParamVO.setTopFlag("Y");
        dimensionParamVO.getTopFlag();
        dimensionParamVO.setTopCategoryCode("cate");
        dimensionParamVO.getTopCategoryCode();
        dimensionParamVO.setTopCategoryCnName("cate");
        dimensionParamVO.getTopCategoryCnName();
        dimensionParamVO.setTopL3CegCode("333");
        dimensionParamVO.getTopL3CegCode();
        dimensionParamVO.setTopL3CegCnName("3333");
        dimensionParamVO.getTopL3CegCnName();
        getSetWeight(dimensionParamVO);
        dimensionParamVO.getId();
        dimensionParamVO.setId(10L);
        dimensionParamVO.setViewSortId("id");
        dimensionParamVO.getViewSortId();
        dimensionParamVO.setErrorMessage("error");
        dimensionParamVO.getErrorMessage();
        dimensionParamVO.setLastUpdateDate(new Date());
        dimensionParamVO.getLastUpdateDate();
        dimensionParamVO.setParentCode("parent");
        dimensionParamVO.getParentCode();
        dimensionParamVO.setTopL3CegShortCnName("l3");
        dimensionParamVO.getTopL3CegShortCnName();
        dimensionParamVO.setTopNums(15L);
        dimensionParamVO.getTopNums();
        dimensionParamVO.setVersionName("version");
        dimensionParamVO.getVersionName();
        List<String> list = new ArrayList<>();
        list.add("2023");
        dimensionParamVO.setYearPeriodList(list);
        dimensionParamVO.getYearPeriodList();
        dimensionParamVO.setLv3ProdRndTeamCode("l3");
        dimensionParamVO.setLv3ProdRdTeamCnName("l3Name");
        dimensionParamVO.getLv3ProdRndTeamCode();
        dimensionParamVO.getLv3ProdRdTeamCnName();
        dimensionParamVO.setViewFlag("0");
        dimensionParamVO.getViewFlag();
        dimensionParamVO.setL1Name("l1");
        dimensionParamVO.getL1Name();
        dimensionParamVO.setL2Name("l2");
        dimensionParamVO.getL2Name();
        dimensionParamVO.setCaliberFlag("U");
        dimensionParamVO.getCaliberFlag();
        dimensionParamVO.setGranularityType("R");
        dimensionParamVO.getGranularityType();
        dimensionParamVO.getDimensionCode();
        dimensionParamVO.setDimensionCode("11");
        dimensionParamVO.getDimensionCnName();
        dimensionParamVO.setDimensionCnName("22");
        dimensionParamVO.getDimensionSubCategoryCode();
        dimensionParamVO.getDimensionSubCategoryCnName();
        dimensionParamVO.setDimensionSubDetailCode("33");
        dimensionParamVO.setDimensionSubDetailCnName("44");
        dimensionParamVO.setDimensionSubDetailCode("66");
        dimensionParamVO.setDimensionSubDetailCnName("55");
        dimensionParamVO.getDimensionSubDetailCode();
        dimensionParamVO.getDimensionSubDetailCnName();
        dimensionParamVO.getLv0ProdListCode();
        dimensionParamVO.getOverseaFlag();
        dimensionParamVO.getPeriodYear0();
        dimensionParamVO.getPeriodYear1();
        dimensionParamVO.getPeriodYear2();
        dimensionParamVO.getPeriodYear3();
        dimensionParamVO.getPeriodYear4();
        dimensionParamVO.getTopL4CegCode();
        dimensionParamVO.getTopL4CegCnName();
        dimensionParamVO.setTopL4CegCnName("11");
        dimensionParamVO.getTopL4CegShortCnName();
        dimensionParamVO.setTopL4CegShortCnName("122");
        dimensionParamVO.getTopManufactureObjectCnName();
        dimensionParamVO.setTopManufactureObjectCnName("33");
        dimensionParamVO.getTopManufactureObjectCode();
        dimensionParamVO.setTopManufactureObjectCode("44");
        dimensionParamVO.getTopShippingObjectCnName();
        dimensionParamVO.setTopShippingObjectCnName("55");
        dimensionParamVO.getTopShippingObjectCode();
        Assert.assertNotNull(dimensionParamVO);
    }

    private void getSetWeight(DmFocTopCateInfoDTO dimensionParamVO) {
        dimensionParamVO.setWeight0("0");
        dimensionParamVO.setWeight1("1");
        dimensionParamVO.setWeight2("2");
        dimensionParamVO.setWeight3("3");
        dimensionParamVO.setWeight4("4");
        dimensionParamVO.getWeight0();
        dimensionParamVO.getWeight1();
        dimensionParamVO.getWeight2();
        dimensionParamVO.getWeight3();
        dimensionParamVO.getWeight4();
    }

    private void getSetProdRdTeam(DmFocTopCateInfoDTO dimensionParamVO) {
        dimensionParamVO.setLv0ProdRndTeamCode("L44V");
        dimensionParamVO.getLv0ProdRndTeamCode();
        dimensionParamVO.setLv0ProdRdTeamCnName("444");
        dimensionParamVO.getLv0ProdRdTeamCnName();
        dimensionParamVO.setLv1ProdRdTeamCnName("111");
        dimensionParamVO.getLv1ProdRdTeamCnName();
        dimensionParamVO.setLv1ProdRndTeamCode("111");
        dimensionParamVO.getLv1ProdRndTeamCode();
        dimensionParamVO.setLv2ProdRdTeamCnName("111");
        dimensionParamVO.getLv2ProdRdTeamCnName();
        dimensionParamVO.setLv2ProdRndTeamCode("111");
        dimensionParamVO.getLv2ProdRndTeamCode();
    }
}