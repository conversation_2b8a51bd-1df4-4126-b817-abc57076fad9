/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.config;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * 降成本目标维表导入VO实体类
 *
 * <AUTHOR>
 * @since 2024-09-27
 */
@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CostReductImportVO implements Serializable {

    private static final long serialVersionUID = -8627081528210431186L;

    @ExcelProperty(value = "*会计期")
    private String periodId;

    @ExcelIgnore
    private String versionId;

    @ExcelProperty(value = "*L1")
    private String lv1ProdRdTeamCnName;

    @ExcelProperty(value = "L1编码")
    private String lv1ProdRndTeamCode;

    @ExcelProperty(value = "L2")
    private String lv2ProdRdTeamCnName;

    @ExcelProperty(value = "L2编码")
    private String lv2ProdRndTeamCode;

    @ExcelProperty(value = "*降成本目标")
    private String objective;

    @ExcelProperty(value = "错误信息")
    private String errorMessage;

    @Override
    public String toString() {
        return lv1ProdRndTeamCode + lv2ProdRndTeamCode + lv1ProdRdTeamCnName + lv2ProdRdTeamCnName + periodId;
    }

}
