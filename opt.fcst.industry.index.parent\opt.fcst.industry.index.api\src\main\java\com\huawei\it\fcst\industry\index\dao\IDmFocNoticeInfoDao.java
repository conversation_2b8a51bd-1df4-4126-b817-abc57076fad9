/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.config.DmFocNoticeInfoDTO;
import com.huawei.it.fcst.industry.index.vo.notice.NoticeInfoVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/3/13
 */
public interface IDmFocNoticeInfoDao {

    PagedResult<DmFocNoticeInfoDTO> findByPage(NoticeInfoVO noticeInfoVO, PageVO pageVO);

    Long getNoticeKey();

    List<DmFocNoticeInfoDTO> findNoticeInfoById (Long id);

    List<DmFocNoticeInfoDTO> findNoticeByType (@Param("noticeType")List<String> noticeType);

    int createDmFocNoticeInfo(DmFocNoticeInfoDTO dmFocNoticeInfoDTO);

    /**
     * Update an existed DmFocNoticeInfo record.
     *
     * @param id Long
     * @return int
     */
    int updateDmFocNoticeInfo(Long id);



}
