/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.standard;

import com.huawei.it.fcst.industry.index.vo.replace.ReplaceAnalysisVO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * StandardAnalysisVO Class
 *
 * <AUTHOR>
 * @since 2024/9/2
 */

@Getter
@Setter
@NoArgsConstructor
public class StandardAnalysisVO extends ReplaceAnalysisVO implements Serializable {

    private static final long serialVersionUID = -6810910873052415832L;

    private List<String> lv0ProdRndTeamCodeList;

    private List<String> lv1ProdRndTeamCodeList;

    private List<String> lv2ProdRndTeamCodeList;

    private List<String> lv3ProdRndTeamCodeList;

    private String dataFlag;

    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<String> allGroupCodeList = new ArrayList<>();

    private Long monthVersionId;

}
