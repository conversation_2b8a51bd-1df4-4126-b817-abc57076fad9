/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.relation;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * DimensionInputVOTest Class
 *
 * <AUTHOR>
 * @since 2023/4/23
 */
public class DimensionInputVOTest extends BaseVOCoverUtilsTest<DimensionInputVO> {

    @Override
    protected Class<DimensionInputVO> getTClass() {
        return DimensionInputVO.class;
    }

    @Test
    public void testMethod() {
        DimensionInputVO dimensionParamVO = new DimensionInputVO();
        dimensionParamVO.setPageIndex(1);
        dimensionParamVO.getPageIndex();
        dimensionParamVO.setPageSize(10);
        dimensionParamVO.getPageSize();
        dimensionParamVO.setVersionId(100L);
        dimensionParamVO.getVersionId();
        dimensionParamVO.setL3CegCode("2331L");
        dimensionParamVO.setL4CegCode("2331L");
        dimensionParamVO.getL3CegCode();
        dimensionParamVO.getL4CegCode();
        dimensionParamVO.setL3CegCnName("2331L");
        dimensionParamVO.setL4CegCnName("2331L");
        dimensionParamVO.getL3CegCnName();
        dimensionParamVO.getL4CegCnName();
        dimensionParamVO.setTotalSize(15);
        dimensionParamVO.getTotalSize();
        dimensionParamVO.setCategoryCode("111");
        dimensionParamVO.getCategoryCode();
        dimensionParamVO.setCategoryName("test");
        dimensionParamVO.getCategoryName();
        List<DmDimCatgModlCegIctVO> dmFocCatgCegIctDTOList=new ArrayList<>();
        DmDimCatgModlCegIctVO ictDTO = new DmDimCatgModlCegIctVO();
        ictDTO.setVersionId(10L);
        dimensionParamVO.setDimCatgModlCegIctList(dmFocCatgCegIctDTOList);
        dimensionParamVO.getDimCatgModlCegIctList();
        Assert.assertNotNull(dimensionParamVO);
    }
}