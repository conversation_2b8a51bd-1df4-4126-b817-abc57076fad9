/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.config.DmFocTopItemInfoDTO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.cursor.Cursor;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/3/15
 */
public interface IDmFocTopItemInfoDao {
    /**
     * 查询TOP品类/规格品清单刷新时间
     *
     * @return DmFocTopItemInfoDTO
     */
    DmFocTopItemInfoDTO findRefreshTime(@Param("tablePreFix") String tablePreFix);

    /**
     * 全品类清单分页查询
     *
     * @param vo 参数
     * @param paramPageVO 参数
     * @return PagedResult<DmFocTopItemInfoDTO>
     */
    PagedResult<DmFocTopItemInfoDTO> findItemByPage(@Param("dmFocTopItemInfoDTO") DmFocTopItemInfoDTO vo, @Param("pageVO") PageVO paramPageVO);

    Cursor<Map> findTopItemVOList(DmFocTopItemInfoDTO build);

    Cursor<Map> findMfcItemVOList(DmFocTopItemInfoDTO build);

    int findItemAllCount(DmFocTopItemInfoDTO build);

    int findMfcItemAllCount(DmFocTopItemInfoDTO build);

    PagedResult<DmFocTopItemInfoDTO> findMfcItemByPage(@Param("dmFocTopItemInfoDTO") DmFocTopItemInfoDTO build, @Param("pageVO") PageVO pageVO);
}
