/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.annual.DmFocAnnualAmpVO;
import com.huawei.it.fcst.industry.index.vo.standard.StandardAnalysisVO;

import java.util.List;

/**
 * IStandardDao Class
 *
 * <AUTHOR>
 * @since 2024/9/9
 */
public interface IStandardDao {

    List<DmFocAnnualAmpVO> findCurrentSameAmpCost(StandardAnalysisVO standardAnalysisVO);

    List<DmFocAnnualAmpVO> findStandardGroupCodeOrderByWeight(StandardAnalysisVO standardAnalysisVO);

    List<DmFocAnnualAmpVO> multiStdAmpCostChart(StandardAnalysisVO standardAnalysisVO);

    List<DmFocAnnualAmpVO> multiSameAmpCostChart(StandardAnalysisVO standardAnalysisVO);

    List<DmFocAnnualAmpVO> multiReplaceAmpCostChart(StandardAnalysisVO standardAnalysisVO);

    List<DmFocAnnualAmpVO> industryStdAmpCostList(StandardAnalysisVO standardAnalysisVO);

    List<DmFocAnnualAmpVO> industrySameAmpCostList(StandardAnalysisVO standardAnalysisVO);

    List<DmFocAnnualAmpVO> industryReplaceAmpCostList(StandardAnalysisVO standardAnalysisVO);

    List<DmFocAnnualAmpVO> distributeAmpCostChart(StandardAnalysisVO standardAnalysisVO);

    List<DmFocAnnualAmpVO> findCurrentStdAmpCost(StandardAnalysisVO standardAnalysisVO);

    List<DmFocAnnualAmpVO> findCurrentReplaceAmpCost(StandardAnalysisVO standardAnalysisVO);

    List<String> getStandAnnualPeriodYear(StandardAnalysisVO standardAnalysisVO);

    Long findActualMonthNum();

    Long findMonAccVersion();

    List<DmFocAnnualAmpVO> findDmFocMonthAccCostAmpList(StandardAnalysisVO standardAnalysisVO);
}
