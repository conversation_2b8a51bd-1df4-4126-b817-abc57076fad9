/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.service.combination;

import com.huawei.it.fcst.industry.index.vo.common.CommonViewVO;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * <AUTHOR>
 * @since 2023/11/21
 */
@Path("/compareAnalysis")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface ICompareAnalysisService {
    /**
     * 根据groupLevel查询对比分析树
     *
     * @param commonViewVO 参数
     * @return 结果
     */
    @Path("/summary/getDimensionTree")
    @POST
    ResultDataVO getDimensionList(CommonViewVO commonViewVO);


}
