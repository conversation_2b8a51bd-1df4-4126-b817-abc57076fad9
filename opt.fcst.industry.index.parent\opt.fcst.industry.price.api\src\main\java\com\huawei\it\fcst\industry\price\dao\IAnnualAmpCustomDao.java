/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.dao;

import com.huawei.it.fcst.industry.price.vo.annual.AnnualAnalysisVO;
import com.huawei.it.fcst.industry.price.vo.annual.DmFocAnnualAmpVO;

import java.util.List;

/**
 * IAnnualAmpCustomDao Class
 *
 * <AUTHOR>
 * @since 2024/11/6
 */
public interface IAnnualAmpCustomDao {

    List<DmFocAnnualAmpVO> allAmpCustomCost(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> findCustomCodeOrderMinLevel(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> multiAmpMinLevelChart(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> findCustomCodeOrderByWeight(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> multiAmpCustomChart(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> industryAmpMinLevelList(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> industryCustomList(AnnualAnalysisVO annualAnalysisVO);
}
