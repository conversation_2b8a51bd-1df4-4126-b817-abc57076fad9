/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.service.common;

import com.huawei.it.fcst.industry.pbi.vo.common.CommonBaseVO;
import com.huawei.it.fcst.industry.pbi.vo.common.DataPermissionsVO;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;

import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * <AUTHOR>
 * @since 2024/6/26
 */
@Path("/dimension")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface IDataPermissionService {

    /**
     * 获取登录用户当前角色的数据范围权限
     *
     * @return List
     * @throws ApplicationException
     */
    @GET
    @Path("/user/role/permissions")
    DataPermissionsVO getCurrentRoleDataPermission() throws ApplicationException;

    /**
     * 获取登录用户当前角色的数据范围权限
     *
     * @return List
     * @throws ApplicationException
     */
    @GET
    @Path("/user/granularityType")
    ResultDataVO getGranularityType();


    /**
     * 获取登录用户当前角色的数据范围权限
     *
     * @return List
     * @throws ApplicationException
     */
    @GET
    @Path("/user/getCostType")
    ResultDataVO getCostType();

    /**
     * 判断LV0权限全不全
     *
     * @return List
     * @throws ApplicationException
     */
    @POST
    @Path("/user/getlv0Permission")
    ResultDataVO getlv0Permission(CommonBaseVO commonBaseVO);

    DataPermissionsVO getDimensionList(CommonBaseVO commonBaseVO);



}
