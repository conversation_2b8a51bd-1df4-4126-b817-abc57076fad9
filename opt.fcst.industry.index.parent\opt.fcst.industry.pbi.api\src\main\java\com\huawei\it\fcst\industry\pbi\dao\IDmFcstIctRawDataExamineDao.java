/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.dao;

import com.huawei.it.fcst.industry.pbi.vo.config.BottomLevelDataReviewVO;
import com.huawei.it.fcst.industry.pbi.vo.config.DataReviewVO;
import com.huawei.it.fcst.industry.pbi.vo.config.DmFcstIctRawDataExamineDTO;
import com.huawei.it.fcst.industry.pbi.vo.config.ExamineVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * IDmFcstIctRawDataExamineDao Class
 *
 * <AUTHOR>
 * @since 2024/9/6
 */
public interface IDmFcstIctRawDataExamineDao {

    List<DmFcstIctRawDataExamineDTO> findRawDataExamineDimList(BottomLevelDataReviewVO bottomLevelDataReviewVO);

    PagedResult<DmFcstIctRawDataExamineDTO> findSpartCodePageList(@Param("searchVO") BottomLevelDataReviewVO bottomLevelDataReviewVO, @Param("pageVO") PageVO pageVO);

    List<DmFcstIctRawDataExamineDTO> findExamineResultDropDownList(BottomLevelDataReviewVO bottomLevelDataReviewVO);

    PagedResult<DmFcstIctRawDataExamineDTO> findDataReviewByPage(@Param("reviewVO") BottomLevelDataReviewVO bottomLevelDataReviewVO, @Param("pageVO") PageVO pageVO);

    List<DmFcstIctRawDataExamineDTO> findDataReviewList(@Param("reviewVO") DataReviewVO dataReviewVO);

    void createDataReviewList(@Param("list") List<DmFcstIctRawDataExamineDTO> allResultList);

    List<DmFcstIctRawDataExamineDTO> findProdTeamCodeRawDataExamineList(BottomLevelDataReviewVO bottomLevelDataReviewVO);

    List<DmFcstIctRawDataExamineDTO> findOtherRawDataList(BottomLevelDataReviewVO bottomLevelDataReviewVO);

    Long findSpartExamineCount(ExamineVO examineVO);

    Long findContractCount(ExamineVO examineVO);

    DmFcstIctRawDataExamineDTO findBeginEndDate();
}
