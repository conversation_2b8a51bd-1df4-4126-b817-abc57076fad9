/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.config;

import com.huawei.it.fcst.industry.index.dao.IDmFocVersionDao;
import com.huawei.it.fcst.industry.index.dao.IFomManufactureDimDao;
import com.huawei.it.fcst.industry.index.enums.IndustryConst;
import com.huawei.it.fcst.industry.index.service.config.IConfigReviewService;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.common.TableNameVO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoVO;
import com.huawei.it.fcst.industry.index.vo.config.FomManufactureDimVO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoDTO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.inject.Inject;
import javax.inject.Named;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * ConfigReviewService Class
 *
 * <AUTHOR>
 * @since 2023/10/7
 */
@Slf4j
@Named("configReviewService")
@JalorResource(code = "configReviewService", desc = "审视制造量纲维表服务")
public class ConfigReviewService implements IConfigReviewService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ConfigReviewService.class);
    @Inject
    private IDmFocVersionDao dmFocVersionDao;

    @Inject
    private IFomManufactureDimDao fomManufactureDimDao;

    @Override
    @JalorOperation(code = "findVersion", desc = "制造成本版本信息")
    public ResultDataVO findVersion(DmFocVersionInfoVO dmFocVersionInfoVO) {
        LOGGER.info("Begin ConfigReviewService::findVersion");
        List<DmFocVersionInfoDTO> mftVersionList = new ArrayList<>();
        dmFocVersionInfoVO.setTablePreFix(TableNameVO.getTablePreFix(dmFocVersionInfoVO.getIndustryOrg()));
        if (!IndustryConst.DataType.DIM_MADE.getValue().equals(dmFocVersionInfoVO.getDataType())) {
            dmFocVersionInfoVO.setStatus(IndustryConst.STATUS.IS_STATUS.getValue());
            mftVersionList = dmFocVersionDao.findMftVersionList(dmFocVersionInfoVO);
        } else {
            mftVersionList = dmFocVersionDao.findMadeVersionList(dmFocVersionInfoVO);
        }
        return ResultDataVO.success(mftVersionList);
    }

    @Override
    @JalorOperation(code = "getManufactureByVersionId", desc = "经营对象、发货对象、制造对象下拉框")
    public ResultDataVO getManufactureByVersionId(FomManufactureDimVO fomManufactureDimVO) throws CommonApplicationException {
        LOGGER.info("Begin ConfigReviewService::getManufactureByVersionId");
        if (null == fomManufactureDimVO.getVersionId()) {
            throw new CommonApplicationException("入参维度版本为空");
        }
        List<FomManufactureDimVO> dropDownList = new ArrayList<>();
        if (IndustryConst.DataType.DIM_MADE.getValue().equals(fomManufactureDimVO.getDataType())) {
            setTableDimPreFix(fomManufactureDimVO);
            dropDownList = fomManufactureDimDao.findDropDownList(fomManufactureDimVO);
        } else if (IndustryConst.DataType.ITEM.getValue().equals(fomManufactureDimVO.getDataType())) {
            dropDownList = fomManufactureDimDao.findItemDropDownList(fomManufactureDimVO);
        } else {
            dropDownList = fomManufactureDimDao.findMfcDropDownList(fomManufactureDimVO);
        }
        return ResultDataVO.success(dropDownList);
    }

    private void setTableDimPreFix(FomManufactureDimVO fomManufactureDimVO) {
        if (IndustryConst.INDUSTRY_ORG.ICT.getValue().equals(fomManufactureDimVO.getIndustryOrg())) {
            fomManufactureDimVO.setTableDimPreFix("apd_foc");
        } else if(IndustryConst.INDUSTRY_ORG.ENERGY.getValue().equals(fomManufactureDimVO.getIndustryOrg())){
            fomManufactureDimVO.setTableDimPreFix("apd_foc_energy");
        } else {
            fomManufactureDimVO.setTableDimPreFix("apd_foc_ias");
        }
    }

    @Override
    @JalorOperation(code = "dimensionList", desc = "审视制造量纲维表查询接口")
    public ResultDataVO dimensionList(FomManufactureDimVO fomManufactureDimVO) throws CommonApplicationException {
        LOGGER.info("Begin ConfigReviewService::dimensionList");
        if (null == fomManufactureDimVO.getVersionId()) {
            throw new CommonApplicationException("入参维度版本为空");
        }
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(fomManufactureDimVO.getPageSize());
        pageVO.setCurPage(fomManufactureDimVO.getPageIndex());
        setTableDimPreFix(fomManufactureDimVO);
        // 分页查询
        PagedResult<FomManufactureDimVO> fomManufacturePageResult = fomManufactureDimDao.findManufactureByPage(fomManufactureDimVO, pageVO);
        Map fomManufactureMap = new LinkedHashMap();
        fomManufactureMap.put("result", fomManufacturePageResult.getResult());
        fomManufactureMap.put("pageVO", fomManufacturePageResult.getPageVO());
        return ResultDataVO.success(fomManufactureMap);
    }
}
