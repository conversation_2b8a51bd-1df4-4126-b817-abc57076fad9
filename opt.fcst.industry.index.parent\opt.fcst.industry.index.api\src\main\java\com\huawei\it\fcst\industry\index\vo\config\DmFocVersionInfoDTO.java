/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.config;

import com.huawei.it.fcst.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * DmFocVersionInfoDTO Class
 *
 * <AUTHOR>
 * @since 2023/3/13
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "版本信息表实体类映射")
public class DmFocVersionInfoDTO extends BaseVO implements Serializable {
    private static final long serialVersionUID = 7928632739085588273L;

    /**
     * 版本id（主键）
     **/
    @ApiModelProperty(value = "版本id（主键）")
    private Long versionId;

    /**
     * 版本名称
     **/
    @ApiModelProperty(value = "版本名称")
    private String version;

    /**
     * 父版本id（只有adjust、final才有父版本）
     **/
    @ApiModelProperty(value = "父版本id（只有adjust、final才有父版本）")
    private Long parentVersionId;

    /**
     * 版本类型（auto/adjust/final）
     **/
    @ApiModelProperty(value = "版本类型（auto/adjust/final）")
    private String versionType;

    /**
     * 数据类型（category：TOP品类、item：规格品）
     **/
    @ApiModelProperty(value = "数据类型（category：TOP品类、item：规格品）")
    private String dataType;

    @ApiModelProperty(value = "开始时间")
    private String startDate;

    @ApiModelProperty(value = "结束时间")
    private String endDate;

    private String lastUpdateStr;

    @ApiModelProperty(value = "区分配置页面步骤")
    private int step;

    /**
     * 状态（1：已刷新、0：未刷新）
     **/
    @ApiModelProperty(value = "状态（1：已刷新、0：未刷新）")
    private Long status;

    @ApiModelProperty(value = "用户账号")
    private Long userId;

    @ApiModelProperty(value = "是否在跑数")
    private String isRunning;

    @ApiModelProperty(value = "组织机构")
    private String industryOrg;

    @ApiModelProperty(value = "表名前缀")
    private String tablePreFix;

}
