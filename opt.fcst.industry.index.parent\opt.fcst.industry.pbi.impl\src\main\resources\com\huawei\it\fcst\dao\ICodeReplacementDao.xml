<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.pbi.dao.ICodeReplacementDao">

    <sql id="queryCondition">
        <!-->最大版本</-->
        AND version_id =  #{VO.versionId}
        <!-->固定过去2年加今年</-->
        AND period_year IN
        <foreach collection='VO.targetPeriod' item="item" open="(" close=")" index="index"
                 separator=",">
            #{item}
        </foreach>
        <if test ='VO.costType == "PSP"'>
            AND SOFTWARE_MARK = #{VO.softwareMark}
        </if>
        AND BASE_PERIOD_ID = #{VO.basePeriodId}
        AND REGION_CODE = #{VO.regionCode}
        AND REPOFFICE_CODE = #{VO.repofficeCode}
        AND BG_CODE = #{VO.bgCode}
        AND OVERSEA_FLAG = #{VO.overseaFlag}
        <if test ='VO.relationType != null and VO.relationType != ""'>
            AND RELATION_TYPE = #{VO.relationType}
        </if>
        <if test ='VO.replaceRelationType != null and VO.replaceRelationType != ""'>
            AND REPLACE_RELATION_TYPE = #{VO.replaceRelationType}
        </if>
        <if test ='VO.replaceRelationName != null and VO.replaceRelationName != ""'>
            AND REPLACE_RELATION_NAME = #{VO.replaceRelationName}
        </if>
    </sql>

    <sql id="queryTopCondition">
        <!-->最大版本</-->
        AND version_id =  #{VO.versionId}
        <!-->固定过去2年加今年</-->
        AND period_year IN
        <foreach collection='VO.targetPeriod' item="item" open="(" close=")" index="index"
                 separator=",">
            #{item}
        </foreach>
        AND GROUP_LEVEL = 'SPART'
        <if test ='VO.costType == "PSP"'>
            AND SOFTWARE_MARK = #{VO.softwareMark}
        </if>
        AND BASE_PERIOD_ID = #{VO.basePeriodId}
        AND REGION_CODE = #{VO.regionCode}
        AND REPOFFICE_CODE = #{VO.repofficeCode}
        AND BG_CODE = #{VO.bgCode}
        AND OVERSEA_FLAG = #{VO.overseaFlag}
    </sql>

    <sql id="queryBlurCondition">
        <!-->最大版本</-->
        AND version_id =  #{VO.versionId}
        <!-->固定过去2年加今年</-->
        AND period_year IN
        <foreach collection='VO.targetPeriod' item="item" open="(" close=")" index="index"
                 separator=",">
            #{item}
        </foreach>
        AND GROUP_LEVEL_TYPE = 'PBI'
        <if test ='VO.costType == "PSP"'>
            AND SOFTWARE_MARK = #{VO.softwareMark}
        </if>
        AND GRANULARITY_TYPE = #{VO.granularityType}
        AND REGION_CODE = #{VO.regionCode}
        AND REPOFFICE_CODE = #{VO.repofficeCode}
        AND BG_CODE = #{VO.bgCode}
        AND OVERSEA_FLAG = #{VO.overseaFlag}
        <if test ='VO.relationType != null and VO.relationType != ""'>
            AND RELATION_TYPE = #{VO.relationType}
        </if>
        <if test ='VO.replaceRelationType != null and VO.replaceRelationType != ""'>
            AND REPLACE_RELATION_TYPE = #{VO.replaceRelationType}
        </if>
        <if test ='VO.replaceRelationName != null and VO.replaceRelationName != ""'>
            AND REPLACE_RELATION_NAME = #{VO.replaceRelationName}
        </if>
    </sql>

    <sql id="queryPreBlurCondition">
        <!-->最大版本</-->
        AND version_id =  #{VO.versionId}
        <!-->固定过去2年加今年</-->
        AND period_year IN
        <foreach collection='VO.targetPeriod' item="item" open="(" close=")" index="index"
                 separator=",">
            #{item}
        </foreach>
        AND GROUP_LEVEL_TYPE = 'PRE_CUS'
        AND GROUP_LEVEL = #{VO.groupLevel}
        <if test ='VO.costType == "PSP"'>
            AND SOFTWARE_MARK = #{VO.softwareMark}
        </if>
        AND BASE_PERIOD_ID = #{VO.basePeriodId}
        AND GRANULARITY_TYPE = #{VO.granularityType}
        AND REGION_CODE = #{VO.regionCode}
        AND REPOFFICE_CODE = #{VO.repofficeCode}
        AND BG_CODE = #{VO.bgCode}
        AND OVERSEA_FLAG = #{VO.overseaFlag}
        AND GROUP_CODE = #{VO.groupCode}
        AND pbi_dim_code = #{VO.prodRndTeamCode}
    </sql>

    <sql id="queryBlurTopCondition">
        <!-->最大版本</-->
        AND version_id =  #{VO.versionId}
        <!-->固定过去2年加今年</-->
        AND period_year IN
        <foreach collection='VO.targetPeriod' item="item" open="(" close=")" index="index"
                 separator=",">
            #{item}
        </foreach>
        <if test ='VO.costType == "PSP"'>
            AND SOFTWARE_MARK = #{VO.softwareMark}
        </if>
        AND REGION_CODE = #{VO.regionCode}
        AND REPOFFICE_CODE = #{VO.repofficeCode}
        AND BG_CODE = #{VO.bgCode}
        AND OVERSEA_FLAG = #{VO.overseaFlag}
    </sql>

    <sql id="queryQtyCondition">
        <!-->最大版本</-->
        AND version_id =  #{VO.versionId}
        <!-->固定过去2年加今年</-->
        AND period_year IN
        <foreach collection='VO.targetPeriod' item="item" open="(" close=")" index="index"
                 separator=",">
            #{item}
        </foreach>
        <if test ='VO.costType == "PSP"'>
            AND SOFTWARE_MARK = #{VO.softwareMark}
        </if>
        AND REGION_CODE = #{VO.regionCode}
        AND REPOFFICE_CODE = #{VO.repofficeCode}
        AND BG_CODE = #{VO.bgCode}
        AND OVERSEA_FLAG = #{VO.overseaFlag}
        <if test ='VO.relationType != null and VO.relationType != ""'>
            AND RELATION_TYPE = #{VO.relationType}
        </if>
        <if test ='VO.replaceRelationType != null and VO.replaceRelationType != ""'>
            AND REPLACE_RELATION_TYPE = #{VO.replaceRelationType}
        </if>
        <if test ='VO.replaceRelationName != null and VO.replaceRelationName != ""'>
            AND REPLACE_RELATION_NAME = #{VO.replaceRelationName}
        </if>
    </sql>

    <sql id="queryCVCondition">
        <!-->最大版本</-->
        AND t1.version_id =  #{VO.versionId}
        <!-->固定过去2年加今年</-->
        AND t1.period_year IN
        <foreach collection='VO.targetPeriod' item="item" open="(" close=")" index="index"
                 separator=",">
            #{item}
        </foreach>
        <if test ='VO.costType == "PSP"'>
            AND t1.SOFTWARE_MARK = #{VO.softwareMark}
        </if>
        AND t1.REGION_CODE = #{VO.regionCode}
        AND t1.REPOFFICE_CODE = #{VO.repofficeCode}
        AND t1.BG_CODE = #{VO.bgCode}
        AND t1.OVERSEA_FLAG = #{VO.overseaFlag}
        <if test ='VO.relationType != null and VO.relationType != ""'>
            AND t1.RELATION_TYPE = #{VO.relationType}
        </if>
        <if test ='VO.replaceRelationType != null and VO.replaceRelationType != ""'>
            AND t1.REPLACE_RELATION_TYPE = #{VO.replaceRelationType}
        </if>
        <if test ='VO.replaceRelationName != null and VO.replaceRelationName != ""'>
            AND t1.REPLACE_RELATION_NAME = #{VO.replaceRelationName}
        </if>
    </sql>

    <sql id ="userPermission">
        <choose>
            <when test='VO.groupLevel == "LV0" and VO.lv1DimensionSet != null and VO.lv1DimensionSet.size() > 0 '>
                <foreach collection='VO.lv1DimensionSet' item="code" open="AND prod_list_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='VO.groupLevel == "LV1" and VO.lv2DimensionSet != null and VO.lv2DimensionSet.size() > 0 '>
                <foreach collection='VO.lv2DimensionSet' item="code" open="AND prod_list_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='VO.groupLevel == "LV2" and VO.lv3DimensionSet != null and VO.lv3DimensionSet.size() > 0 '>
                <foreach collection='VO.lv3DimensionSet' item="code" open="AND prod_list_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <otherwise>
            </otherwise>
        </choose>
    </sql>
    <select id="getSpartIndexCostList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        t1.period_year ,t1.period_id periodId,
        ROUND(nvl(COST_INDEX,0), 1) AS costIndex,
        CODE_TYPE codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_IDX_T t1
        WHERE
        GROUP_LEVEL = 'SPART'
        <include refid="queryCondition"/>
        AND CODE_TYPE = 'NEW'
        <if test='newSpartCode !=""'>
            AND GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
        </if>
        <if test='VO.newProdTeamCode !=""'>
            AND parent_code = #{VO.newProdTeamCode,jdbcType=VARCHAR}
        </if>
        AND DEL_FLAG = 'N'
        UNION ALL
        SELECT
        t2.period_year,t2.period_id periodId,
        ROUND(nvl(COST_INDEX,0), 1) AS costIndex,
        code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_IDX_T t2
        WHERE
        GROUP_LEVEL = 'SPART'
        <include refid="queryCondition"/>
        AND CODE_TYPE = 'OLD'
        <if test='oldSpartCode !=""'>
            AND GROUP_CODE = #{oldSpartCode,jdbcType=VARCHAR}
        </if>
        <if test='VO.oldProdTeamCode !=""'>
            AND parent_code = #{VO.oldProdTeamCode,jdbcType=VARCHAR}
        </if>
        AND DEL_FLAG = 'N'
        ORDER BY periodId asc
    </select>

    <select id="getReplSameSpartIndexCostList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        t1.period_year ,t1.period_id periodId,
        ROUND(nvl(COST_INDEX,0), 1) AS costIndex,
        CODE_TYPE codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_REPL_SAME_COST_IDX_T t1
        WHERE
        GROUP_LEVEL = 'SPART'
        <include refid="queryCondition"/>
        AND CODE_TYPE = 'NEW'
        <if test='newSpartCode !=""'>
            AND GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
        </if>
        <if test='VO.newProdTeamCode !=""'>
            AND parent_code = #{VO.newProdTeamCode,jdbcType=VARCHAR}
        </if>
        AND DEL_FLAG = 'N'
        ORDER BY periodId asc
    </select>

    <select id="getPbiIndexCostList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        t1.period_year,t1.period_id periodId,
        ROUND(nvl(COST_INDEX,0), 1) AS costIndex,code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_IDX_T t1
        WHERE
        GROUP_LEVEL = #{VO.groupLevel,jdbcType=VARCHAR}
        <include refid="queryCondition"/>
        <choose>
            <when test='VO.groupLevel == "LV1"'>
                AND PROD_LIST_CODE = #{VO.lv1ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV2"'>
                AND PROD_LIST_CODE = #{VO.lv2ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV3"'>
                AND PROD_LIST_CODE = #{VO.lv3ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV4"'>
                AND PROD_LIST_CODE = #{VO.lv4ProdTeamCode}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <include refid="userPermission"></include>
        AND DEL_FLAG = 'N'
        ORDER BY  periodId asc
    </select>

    <select id="getPbiReplSameIndexCostList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        t1.period_year,t1.period_id periodId,
        ROUND(nvl(COST_INDEX,0), 1) AS costIndex,code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_REPL_SAME_COST_IDX_T t1
        WHERE
        GROUP_LEVEL = #{VO.groupLevel,jdbcType=VARCHAR}
        <include refid="queryCondition"/>
        <choose>
            <when test='VO.groupLevel == "LV1"'>
                AND PROD_LIST_CODE = #{VO.lv1ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV2"'>
                AND PROD_LIST_CODE = #{VO.lv2ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV3"'>
                AND PROD_LIST_CODE = #{VO.lv3ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV4"'>
                AND PROD_LIST_CODE = #{VO.lv4ProdTeamCode}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <include refid="userPermission"></include>
        AND DEL_FLAG = 'N'
        ORDER BY periodId asc
    </select>

    <select id="getTopSpartIndexCostList" resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        period_year,
        period_id periodId,
        ROUND(nvl(COST_INDEX,0), 1) AS costIndex
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_COST_IDX_T
        WHERE
        main_Flag ='N'
        <include refid="queryTopCondition"/>
        AND GROUP_CODE = #{spartCode,jdbcType=VARCHAR}
        <if test='lv4ProdTeamCode !=""'>
            AND parent_code = #{lv4ProdTeamCode}
        </if>
        and DEL_FLAG = 'N'
        ORDER BY  periodId asc
    </select>

    <select id="getVersionIdsYear" resultType="java.lang.Integer">
        SELECT coalesce(to_char(MAX(PERIOD_YEAR)),to_char(now(),'YYYY'))
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${tablePrefixCostType}_${tablePrefixPbi}_MON_REPL_COST_IDX_T
        WHERE DEL_FLAG = 'N'
    </select>

    <select id="getBlurVersionIdsYear" resultType="java.lang.Integer">
        SELECT coalesce(to_char(MAX(PERIOD_YEAR)),to_char(now(),'YYYY'))
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${tablePrefixCostType}_BASE_CUS_MON_REPL_COST_IDX_T
        WHERE DEL_FLAG = 'N'
        AND GRANULARITY_TYPE = #{granularityType,jdbcType=VARCHAR}
    </select>

    <select id="getBlurSpartIndexCostList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        period_year,
        period_id periodId,
        ROUND(nvl(COST_INDEX,0), 1)  AS costIndex,
        code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_REPL_COST_IDX_T
        WHERE custom_id =  #{VO.customId}
        <include refid="queryBlurCondition"/>
        AND DEL_FLAG = 'N'
        ORDER BY  periodId asc
    </select>

    <select id="getPreBlurSpartIndexCostList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        period_year,
        period_id periodId,
        ROUND(nvl(COST_INDEX,0), 1)  AS costIndex,
        code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_REPL_COST_IDX_T
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <include refid="queryPreBlurCondition"/>
             and DEL_FLAG = 'N'
        </trim>
        ORDER BY periodId asc
    </select>

    <select id="getSpartIndexQtyList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        period_year,
        period_id periodId,
        SHIPMENT_QTY shipmentQty,
        CODE_TYPE codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_CV_T
        WHERE
        GROUP_LEVEL = 'SPART'
        <include refid="queryQtyCondition"/>
        AND CODE_TYPE = 'NEW'
        <if test='newSpartCode != null and newSpartCode != ""'>
            AND GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
        </if>
        AND DEL_FLAG = 'N'
        UNION ALL
        SELECT
        period_year,
        period_id periodId,
        SHIPMENT_QTY shipmentQty,
        code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_CV_T
        WHERE
        GROUP_LEVEL = 'SPART'
        <include refid="queryQtyCondition"/>
        AND CODE_TYPE = 'OLD'
        <if test='oldSpartCode != null and oldSpartCode != ""'>
            AND GROUP_CODE = #{oldSpartCode,jdbcType=VARCHAR}
        </if>
        AND DEL_FLAG = 'N'
        ORDER BY  periodId asc
    </select>

    <select id="getBlurTopSpartIndexCostList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        period_year,
        period_id periodId,
        ROUND(cost_index, 2) AS costIndex
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_COST_IDX_T
        WHERE
        GROUP_CODE = #{spartCode,jdbcType=VARCHAR}
        <include refid="queryBlurTopCondition"/>
        AND main_flag ='N'
        <if test='customId != ""'>
            AND custom_id = #{customId}
        </if>
        AND GRANULARITY_TYPE = #{VO.granularityType}
        AND DEL_FLAG = 'N'
        ORDER BY periodId asc
    </select>

    <select id="getPbiIndexQtyList" resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        period_year,
        period_id periodId,
        round(SHIPMENT_QTY,0) shipmentQty,
        code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_CV_T t1
        WHERE
        GROUP_LEVEL = #{VO.groupLevel,jdbcType=VARCHAR}
        <include refid="queryQtyCondition"/>
        <choose>
            <when test='VO.groupLevel == "LV1"'>
                AND PROD_LIST_CODE = #{VO.lv1ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV2"'>
                AND PROD_LIST_CODE = #{VO.lv2ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV3"'>
                AND PROD_LIST_CODE = #{VO.lv3ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV4"'>
                AND PROD_LIST_CODE = #{VO.lv4ProdTeamCode}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <include refid="userPermission"></include>
        AND DEL_FLAG = 'N'
        ORDER BY  periodId asc
    </select>
    <select id="getBlurSpartIndexQtyList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        period_year,
        period_id periodId,
        PART_QTY shipmentQty,
        code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_REPL_INFO_IDX_T
        WHERE
        GROUP_LEVEL_TYPE ='PBI'
        <include refid="queryQtyCondition"/>
        AND GRANULARITY_TYPE = #{VO.granularityType}
        AND CUSTOM_ID = #{VO.customId,jdbcType=VARCHAR}
        AND DEL_FLAG = 'N'
        ORDER BY  periodId asc
    </select>

    <select id="getPreBlurSpartIndexQtyList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        period_year,
        period_id periodId,
        PART_QTY shipmentQty,
        code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_REPL_INFO_IDX_T
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <include refid="queryPreBlurCondition"/>
             AND DEL_FLAG = 'N'
        </trim>
        ORDER BY  periodId asc
    </select>

    <select id="getPbiIndexCVList" resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        t1.period_year,
        t1.period_id periodId,
        <if test='VO.tablePrefixCostType == "PSP"'>
            t1.RMB_COST_AMT rmbCostAmt,
        </if>
        <if test='VO.tablePrefixCostType != "PSP" and VO.groupLevel == "SPART"'>
            GS_DECRYPT(NVL(t1.RMB_COST_AMT,gs_encrypt('',#{keyStr}, 'aes128', 'cbc', 'sha256')),#{keyStr,jdbcType=VARCHAR}, 'AES128', 'CBC', 'SHA256') rmbCostAmt,
        </if>
        <if test='VO.tablePrefixCostType != "PSP" and VO.groupLevel != "SPART"'>
            t1.RMB_COST_AMT rmbCostAmt,
        </if>
        t1.CODE_TYPE CodeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_CV_T t1
        WHERE
        t1.GROUP_LEVEL = #{VO.groupLevel,jdbcType=VARCHAR}
        <include refid="queryCVCondition"/>
        <choose>
            <when test='VO.groupLevel == "LV1"'>
                AND t1.PROD_LIST_CODE = #{VO.lv1ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV2"'>
                AND t1.PROD_LIST_CODE = #{VO.lv2ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV3"'>
                AND t1.PROD_LIST_CODE = #{VO.lv3ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV4"'>
                AND t1.PROD_LIST_CODE = #{VO.lv4ProdTeamCode}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <include refid="userPermission"></include>
        AND t1.DEL_FLAG = 'N'
        ORDER BY  periodId asc
    </select>

    <select id="getSpartIndexAccCostList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        period_year,period_id periodId,
        ROUND(cost_index, 2) AS costIndex,
        CODE_TYPE codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_IDX_T
        WHERE
        GROUP_LEVEL = 'SPART'
        <include refid="queryCondition"/>
        AND CODE_TYPE = 'NEW'
        <if test='newSpartCode !=""'>
            AND GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
        </if>
        <if test='VO.newProdTeamCode !=""'>
            AND PARENT_CODE = #{VO.newProdTeamCode}
        </if>
        AND DEL_FLAG = 'N'
        UNION ALL
        SELECT
        period_year,period_id periodId,
        cost_index costIndex,
        code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_IDX_T
        WHERE
        GROUP_LEVEL = 'SPART'
        <include refid="queryCondition"/>
        AND CODE_TYPE = 'OLD'
        <if test='oldSpartCode !=""'>
            AND GROUP_CODE = #{oldSpartCode,jdbcType=VARCHAR}
        </if>
        <if test='VO.oldProdTeamCode !=""'>
            AND PARENT_CODE = #{VO.oldProdTeamCode}
        </if>
        AND DEL_FLAG = 'N'
        ORDER BY  periodId asc
    </select>

    <select id="getReplSameSpartIndexAccCostList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        period_year,period_id periodId,
        ROUND(cost_index, 2) AS costIndex,
        CODE_TYPE codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_REPL_SAME_YTD_COST_IDX_T
        WHERE GROUP_LEVEL = 'SPART'
        <include refid="queryCondition"/>
        <if test='newSpartCode !=""'>
            AND GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
        </if>
        <if test='VO.newProdTeamCode !=""'>
            AND PARENT_CODE = #{VO.newProdTeamCode}
        </if>
        AND  DEL_FLAG = 'N'
        ORDER BY periodId asc
    </select>

    <select id="getTopSpartIndexAccCostList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        period_year,
        period_id periodId,
        ROUND(nvl(COST_INDEX,0), 2) AS costIndex
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_COST_IDX_T
        WHERE
        main_flag ='N'
        <include refid="queryTopCondition"/>
        AND GROUP_CODE = #{spartCode,jdbcType=VARCHAR}
        <if test='lv4ProdTeamCode != ""'>
            AND parent_code = #{lv4ProdTeamCode}
        </if>
        AND DEL_FLAG = 'N'
        ORDER BY  periodId asc
    </select>
    <select id="getPbiIndexAccCostList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        period_year,
        period_id periodId,
        ROUND(cost_index, 2) AS costIndex,
        code_type codeType
        FROM
        <!-->代替换月累计表</-->
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_IDX_T
        WHERE
        GROUP_LEVEL = #{VO.groupLevel,jdbcType=VARCHAR}
        <include refid="queryCondition"/>
        <choose>
            <when test='VO.groupLevel == "LV1"'>
                AND PROD_LIST_CODE = #{VO.lv1ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV2"'>
                AND PROD_LIST_CODE = #{VO.lv2ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV3"'>
                AND PROD_LIST_CODE = #{VO.lv3ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV4"'>
                AND PROD_LIST_CODE = #{VO.lv4ProdTeamCode}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <include refid="userPermission"></include>
        AND DEL_FLAG = 'N'
        ORDER BY  periodId asc
    </select>

    <select id="getReplSamePbiIndexAccCostList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        period_year,
        period_id periodId,
        ROUND(cost_index, 2) AS costIndex,
        code_type codeType
        FROM
        <!-->替代换月累计表</-->
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_REPL_SAME_YTD_COST_IDX_T
        WHERE
        GROUP_LEVEL = #{VO.groupLevel,jdbcType=VARCHAR}
        <include refid="queryCondition"/>
        <choose>
            <when test='VO.groupLevel == "LV1"'>
                AND PROD_LIST_CODE = #{VO.lv1ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV2"'>
                AND PROD_LIST_CODE = #{VO.lv2ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV3"'>
                AND PROD_LIST_CODE = #{VO.lv3ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV4"'>
                AND PROD_LIST_CODE = #{VO.lv4ProdTeamCode}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <include refid="userPermission"></include>
        and  DEL_FLAG = 'N'
        ORDER BY  periodId asc
    </select>

    <select id="getBlurSpartIndexAccCostList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        period_year,
        period_id periodId,
        ROUND(cost_index, 2) AS costIndex,
        code_type codeType
        FROM
        <!-->代替换月累计表</-->
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_COST_IDX_T
        WHERE  CUSTOM_ID = #{VO.customId}
        <include refid="queryBlurCondition"/>
        AND DEL_FLAG = 'N'
        ORDER BY periodId asc
    </select>

    <select id="getPreBlurSpartIndexAccCostList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        period_year,
        period_id periodId,
        ROUND(cost_index, 2) AS costIndex,
        code_type codeType
        FROM
        <!-->代替换月累计表</-->
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_COST_IDX_T
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <include refid="queryPreBlurCondition"/>
             and DEL_FLAG = 'N'
        </trim>
        ORDER BY periodId asc
    </select>
    <select id="getBlurTopSpartIndexAccCostList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        period_year,
        period_id periodId,
        ROUND(nvl(cost_index,0), 2) AS costIndex
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_COST_IDX_T
        WHERE  custom_Id = #{customId}
        <include refid="queryBlurTopCondition"/>
        AND GROUP_CODE = #{spartCode,jdbcType=VARCHAR}
        AND main_flag ='N'
        AND DEL_FLAG = 'N'
        ORDER BY periodId asc
    </select>

    <select id="getSpartIndexAccQtyList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        period_year,
        period_id periodId,
        SHIPMENT_QTY shipmentQty,
        CODE_TYPE codeType
        FROM
        <!-->代替换月累计表</-->
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_CV_T
        WHERE
        GROUP_LEVEL = 'SPART'
        AND CODE_TYPE = 'NEW'
        <include refid="queryQtyCondition"/>
        <if test='VO.newProdTeamCode !=""'>
            AND parent_code = #{VO.newProdTeamCode}
        </if>
        <if test='newSpartCode !=""'>
            AND GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
        </if>
        AND DEL_FLAG = 'N'
        UNION ALL
        SELECT
        period_year,
        period_id periodId,
        SHIPMENT_QTY shipmentQty,
        code_type codeType
        FROM
        <!-->代替换月累计表</-->
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_CV_T
        WHERE
        GROUP_LEVEL = 'SPART'
        AND CODE_TYPE = 'OLD'
        <include refid="queryQtyCondition"/>
        <if test='oldSpartCode !=""'>
            AND GROUP_CODE = #{oldSpartCode,jdbcType=VARCHAR}
        </if>
        <if test='VO.oldProdTeamCode !=""'>
            AND parent_code = #{VO.oldProdTeamCode}
        </if>
        AND DEL_FLAG = 'N'
        ORDER BY periodId asc
    </select>

    <select id="getPbiIndexAccQtyList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        period_year,period_id periodId,
        SHIPMENT_QTY shipmentQty,code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_CV_T
        WHERE
        GROUP_LEVEL = #{VO.groupLevel,jdbcType=VARCHAR}
        <include refid="queryQtyCondition"/>
        <choose>
            <when test='VO.groupLevel == "LV1"'>
                AND PROD_LIST_CODE = #{VO.lv1ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV2"'>
                AND PROD_LIST_CODE = #{VO.lv2ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV3"'>
                AND PROD_LIST_CODE = #{VO.lv3ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV4"'>
                AND PROD_LIST_CODE = #{VO.lv4ProdTeamCode}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <include refid="userPermission"></include>
        AND DEL_FLAG = 'N'
        ORDER BY periodId asc
    </select>

    <select id="getBlurSpartIndexAccQtyList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        t1.period_year,
        t1.period_id periodId,
        t1.PART_QTY shipmentQty,
        t1.code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_INFO_IDX_T t1
        WHERE
        GROUP_LEVEL_TYPE ='PBI'
        AND CUSTOM_ID =  #{VO.customId}
        <include refid="queryQtyCondition"/>
        AND GRANULARITY_TYPE = #{VO.granularityType}
        <if test='VO.newProdTeamCode !=""'>
        AND parent_code = #{VO.newProdTeamCode}
        </if>
        AND DEL_FLAG = 'N'
        ORDER BY periodId asc
    </select>

    <select id="getPreBlurSpartIndexAccQtyList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        t1.period_year,
        t1.period_id periodId,
        t1.PART_QTY shipmentQty,
        t1.code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_INFO_IDX_T t1
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <include refid="queryPreBlurCondition"/>
             AND  DEL_FLAG = 'N'
        </trim>
        ORDER BY periodId asc
    </select>

    <select id="getPbiIndexAccCVList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        period_year,period_id periodId,
        <if test='VO.tablePrefixCostType == "PSP"'>
            t1.RMB_COST_AMT rmbCostAmt,
        </if>
        <if test='VO.tablePrefixCostType != "PSP" and (VO.groupLevel == "SPART" or VO.groupLevel == "TOP-SPART")'>
            GS_DECRYPT(NVL(t1.RMB_COST_AMT,gs_encrypt('',#{keyStr}, 'aes128', 'cbc', 'sha256')),#{keyStr,jdbcType=VARCHAR}, 'AES128', 'CBC', 'SHA256') rmbCostAmt,
        </if>
        <if test='VO.tablePrefixCostType != "PSP" and VO.groupLevel != "SPART"'>
            t1.RMB_COST_AMT rmbCostAmt,
        </if>
        CODE_TYPE codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_CV_T t1
        WHERE
        t1.GROUP_LEVEL = #{VO.groupLevel,jdbcType=VARCHAR}
        <include refid="queryCVCondition"/>
        <choose>
            <when test='VO.groupLevel == "LV1"'>
                AND t1.PROD_LIST_CODE = #{VO.lv1ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV2"'>
                AND t1.PROD_LIST_CODE = #{VO.lv2ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV3"'>
                AND t1.PROD_LIST_CODE = #{VO.lv3ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV4"'>
                AND t1.PROD_LIST_CODE = #{VO.lv4ProdTeamCode}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <include refid="userPermission"></include>
        AND  t1.DEL_FLAG = 'N'
        ORDER BY  periodId asc
    </select>
    <select id="getMonthMaxPeriodId" resultType="java.lang.Integer">
        SELECT MAX(PERIOD_ID)
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${tablePrefixCostType}_${tablePrefixPbi}_MON_COST_IDX_T
        WHERE DEL_FLAG = 'N'
    </select>
    <select id="getMaxPeriodId" resultType="java.lang.Integer">
        SELECT MAX(PERIOD_ID)
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_${tablePrefixCostType}_${tablePrefixPbi}_MON_REPL_COST_IDX_T
        WHERE DEL_FLAG = 'N'
    </select>
    <select id="getSpartIndexCostExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN costIndex ELSE NULL END ) as newCostIndex,
        max(CASE WHEN codeType ='OLD' THEN costIndex ELSE NULL END ) as oldCostIndex,
        max(CASE WHEN codeType ='SAME' THEN costIndex ELSE NULL END ) as sameCostIndex
        FROM(
        SELECT
        t1.period_year ,t1.period_id periodId,
        ROUND(nvl(COST_INDEX,0), 1) AS costIndex,
        CODE_TYPE codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_IDX_T t1
        WHERE GROUP_LEVEL = 'SPART'
        <include refid="queryCondition"/>
        AND CODE_TYPE = 'NEW'
        <if test='newSpartCode !=""'>
            AND GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
        </if>
        <if test='VO.newProdTeamCode !=""'>
            AND parent_code = #{VO.newProdTeamCode}
        </if>
        AND  DEL_FLAG = 'N'
        UNION ALL
        SELECT
        t2.period_year,t2.period_id periodId,
        ROUND(nvl(COST_INDEX,0),1) AS costIndex,
        code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_IDX_T t2
        WHERE GROUP_LEVEL = 'SPART'
        <include refid="queryCondition"/>
        AND CODE_TYPE = 'OLD'
        <if test='oldSpartCode !=""'>
            AND GROUP_CODE = #{oldSpartCode,jdbcType=VARCHAR}
        </if>
        <if test='VO.oldProdTeamCode !=""'>
            AND parent_code = #{VO.oldProdTeamCode}
        </if>
        AND DEL_FLAG = 'N'
        UNION ALL
        SELECT
        t1.period_year ,t1.period_id periodId,
        ROUND(nvl(COST_INDEX,0), 1) AS costIndex,
        'SAME' codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_REPL_SAME_COST_IDX_T t1
        WHERE GROUP_LEVEL = 'SPART'
        <include refid="queryCondition"/>
        <if test='newSpartCode !=""'>
            AND GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
        </if>
        <if test='VO.newProdTeamCode !=""'>
            AND parent_code = #{VO.newProdTeamCode,jdbcType=VARCHAR}
        </if>
        AND DEL_FLAG = 'N'
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>
    <select id="getPbiIndexCostExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN costIndex ELSE NULL END ) as newCostIndex,
        max(CASE WHEN codeType ='OLD' THEN costIndex ELSE NULL END ) as oldCostIndex,
        max(CASE WHEN codeType ='SAME' THEN costIndex ELSE NULL END ) as sameCostIndex
        FROM(
        SELECT
        t1.period_year,t1.period_id periodId,
        ROUND(nvl(COST_INDEX,0), 2) AS costIndex,code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_IDX_T t1
        WHERE
        GROUP_LEVEL = #{VO.groupLevel,jdbcType=VARCHAR}
        <include refid="queryCondition"/>
        <choose>
            <when test='VO.groupLevel == "LV1"'>
                AND PROD_LIST_CODE = #{VO.lv1ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV2"'>
                AND PROD_LIST_CODE = #{VO.lv2ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV3"'>
                AND PROD_LIST_CODE = #{VO.lv3ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV4"'>
                AND PROD_LIST_CODE = #{VO.lv4ProdTeamCode}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <include refid="userPermission"></include>
        AND  DEL_FLAG = 'N'
        UNION ALL
        SELECT
        t1.period_year,t1.period_id periodId,
        ROUND(nvl(COST_INDEX,0), 1) AS costIndex,'SAME'codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_REPL_SAME_COST_IDX_T t1
        WHERE
        GROUP_LEVEL = #{VO.groupLevel,jdbcType=VARCHAR}
        <include refid="queryCondition"/>
        <choose>
            <when test='VO.groupLevel == "LV1"'>
                AND PROD_LIST_CODE = #{VO.lv1ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV2"'>
                AND PROD_LIST_CODE = #{VO.lv2ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV3"'>
                AND PROD_LIST_CODE = #{VO.lv3ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV4"'>
                AND PROD_LIST_CODE = #{VO.lv4ProdTeamCode}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <include refid="userPermission"></include>
        AND DEL_FLAG = 'N'
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>
    <select id="getBlurSpartIndexCostExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN costIndex ELSE NULL END ) as newCostIndex,
        max(CASE WHEN codeType ='OLD' THEN costIndex ELSE NULL END ) as oldCostIndex,
        max(CASE WHEN codeType ='SAME' THEN costIndex ELSE NULL END ) as sameCostIndex
        FROM(
        SELECT
        period_year,
        period_id periodId,
        ROUND(nvl(COST_INDEX,0), 1) AS costIndex,
        'NEW' codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_REPL_COST_IDX_T
        WHERE custom_id = #{VO.customId}
        <include refid="queryBlurCondition"/>
        AND code_type = 'NEW'
        AND DEL_FLAG = 'N'
        UNION ALL
        SELECT
        period_year,
        period_id periodId,
        ROUND(nvl(COST_INDEX,0),1) AS costIndex,
        'OLD' codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_REPL_COST_IDX_T
        WHERE  custom_id =  #{VO.customId}
        <include refid="queryBlurCondition"/>
        AND code_type = 'OLD'
        AND DEL_FLAG = 'N'
        UNION ALL
        SELECT
        period_year,
        period_id periodId,
        ROUND(nvl(COST_INDEX,0), 1)  AS costIndex,
        'SAME' codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_REPL_COST_IDX_T
        WHERE custom_id =  #{VO.customId} AND code_type = 'SAME'
        <include refid="queryBlurCondition"/>
        AND  DEL_FLAG = 'N'
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>

    <select id="getPreBlurSpartIndexCostExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN costIndex ELSE NULL END ) as newCostIndex,
        max(CASE WHEN codeType ='OLD' THEN costIndex ELSE NULL END ) as oldCostIndex,
        max(CASE WHEN codeType ='SAME' THEN costIndex ELSE NULL END ) as sameCostIndex
        FROM(
        SELECT
        period_year,
        period_id periodId,
        ROUND(nvl(COST_INDEX,0), 1)  AS costIndex,
        code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_REPL_COST_IDX_T
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <include refid="queryPreBlurCondition"/>
             AND DEL_FLAG = 'N'
        </trim>
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>

    <select id="getSpartIndexAccCostExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN costIndex ELSE NULL END ) as newCostIndex,
        max(CASE WHEN codeType ='OLD' THEN costIndex ELSE NULL END ) as oldCostIndex,
        max(CASE WHEN codeType ='SAME' THEN costIndex ELSE NULL END ) as sameCostIndex
        FROM(
        SELECT
        period_year,period_id periodId,
        ROUND(nvl(COST_INDEX,0), 1) AS costIndex,
        CODE_TYPE codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_IDX_T
        WHERE GROUP_LEVEL = 'SPART'
        <include refid="queryCondition"/>
        AND CODE_TYPE = 'NEW'
        <if test='newSpartCode !=""'>
            AND GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
        </if>
        <if test='VO.newProdTeamCode !=""'>
            AND parent_code = #{VO.newProdTeamCode}
        </if>
        AND DEL_FLAG = 'N'
        UNION ALL
        SELECT
        period_year,period_id periodId,
        cost_index costIndex,
        code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_IDX_T
        WHERE GROUP_LEVEL = 'SPART'
        <include refid="queryCondition"/>
        AND CODE_TYPE = 'OLD'
        <if test='oldSpartCode !=""'>
            AND GROUP_CODE = #{oldSpartCode,jdbcType=VARCHAR}
        </if>
        <if test='VO.oldProdTeamCode !=""'>
            AND parent_code = #{VO.oldProdTeamCode}
        </if>
        AND DEL_FLAG = 'N'
        UNION ALL
        SELECT
        period_year,period_id periodId,
        ROUND(cost_index, 2) AS costIndex,
        'SAME' codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_REPL_SAME_YTD_COST_IDX_T
        WHERE GROUP_LEVEL = 'SPART'
        <include refid="queryCondition"/>
        <if test='newSpartCode !=""'>
            AND GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
        </if>
        <if test='VO.newProdTeamCode !=""'>
            AND PARENT_CODE = #{VO.newProdTeamCode}
        </if>
        AND  DEL_FLAG = 'N'
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>
    <select id="getPbiIndexAccCostExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year ,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN costIndex ELSE NULL END ) as newCostIndex,
        max(CASE WHEN codeType ='OLD' THEN costIndex ELSE NULL END ) as oldCostIndex,
        max(CASE WHEN codeType ='SAME' THEN costIndex ELSE NULL END ) as sameCostIndex
        FROM(SELECT
        period_year,
        period_id periodId,
        ROUND(nvl(COST_INDEX,0), 1) AS costIndex,
        code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_IDX_T
        WHERE GROUP_LEVEL = #{VO.groupLevel,jdbcType=VARCHAR}
        <include refid="queryCondition"/>
        AND  DEL_FLAG = 'N'
        <choose>
            <when test='VO.groupLevel == "LV1"'>
                AND PROD_LIST_CODE = #{VO.lv1ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV2"'>
                AND PROD_LIST_CODE = #{VO.lv2ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV3"'>
                AND PROD_LIST_CODE = #{VO.lv3ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV4"'>
                AND PROD_LIST_CODE = #{VO.lv4ProdTeamCode}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <include refid="userPermission"></include>
        UNION ALL
        SELECT
        period_year,
        period_id periodId,
        ROUND(cost_index, 2) AS costIndex,
        'SAME' codeType
        FROM
        <!-->替代换月累计表</-->
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_REPL_SAME_YTD_COST_IDX_T
        WHERE
        GROUP_LEVEL = #{VO.groupLevel,jdbcType=VARCHAR}
        <include refid="queryCondition"/>
        AND DEL_FLAG = 'N'
        <choose>
            <when test='VO.groupLevel == "LV1"'>
                AND PROD_LIST_CODE = #{VO.lv1ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV2"'>
                AND PROD_LIST_CODE = #{VO.lv2ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV3"'>
                AND PROD_LIST_CODE = #{VO.lv3ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV4"'>
                AND PROD_LIST_CODE = #{VO.lv4ProdTeamCode}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <include refid="userPermission"></include>
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>
    <select id="getBlurSpartIndexAccCostExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN costIndex ELSE NULL END ) as newCostIndex,
        max(CASE WHEN codeType ='OLD' THEN costIndex ELSE NULL END ) as oldCostIndex,
        max(CASE WHEN codeType ='SAME' THEN costIndex ELSE NULL END ) as sameCostIndex
        FROM(SELECT
        period_year,
        period_id periodId,
        ROUND(nvl(COST_INDEX,0), 1) AS costIndex,
        'NEW' codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_COST_IDX_T
        WHERE  CUSTOM_ID = #{VO.customId}
        <include refid="queryBlurCondition"/>
        AND code_type = 'NEW'
        AND DEL_FLAG = 'N'
        UNION ALL
        SELECT
        period_year,period_id periodId,
        ROUND(nvl(COST_INDEX,0), 1) AS costIndex,
        'OLD' codeType
        FROM
        <!-->代替换月累计表</-->
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_COST_IDX_T
        WHERE  CUSTOM_ID = #{VO.customId}
        <include refid="queryBlurCondition"/>
        AND code_type = 'OLD'
        AND DEL_FLAG = 'N'
        UNION ALL
        SELECT
        period_year,period_id periodId,
        ROUND(nvl(COST_INDEX,0), 1) AS costIndex,
        'SAME' codeType
        FROM
        <!-->代替换月累计表</-->
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_COST_IDX_T
        WHERE  CUSTOM_ID = #{VO.customId}  AND code_type = 'SAME'
        <include refid="queryBlurCondition"/>
        AND DEL_FLAG = 'N'
        ORDER BY  periodId asc
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>

    <select id="getPreBlurSpartIndexAccCostExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN costIndex ELSE NULL END ) as newCostIndex,
        max(CASE WHEN codeType ='OLD' THEN costIndex ELSE NULL END ) as oldCostIndex,
        max(CASE WHEN codeType ='SAME' THEN costIndex ELSE NULL END ) as sameCostIndex
        FROM(SELECT
        period_year,
        period_id periodId,
        ROUND(cost_index, 2) AS costIndex,
        code_type codeType
        FROM
        <!-->代替换月累计表</-->
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_COST_IDX_T
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <include refid="queryPreBlurCondition"/>
            AND DEL_FLAG = 'N'
        </trim>
       )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>

    <select id="getSpartIndexQtyExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN round(shipmentQty,0) ELSE NULL END ) as newQty,
        max(CASE WHEN codeType ='OLD' THEN round(shipmentQty,0) ELSE NULL END ) as oldQty
        FROM(SELECT
        period_year,
        period_id periodId,
        SHIPMENT_QTY shipmentQty,
        CODE_TYPE codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_CV_T
        WHERE
        CODE_TYPE = 'NEW'
        AND GROUP_LEVEL = 'SPART'
        <include refid="queryQtyCondition"/>
        <if test='newSpartCode != null and newSpartCode != ""'>
            AND GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
        </if>
        AND DEL_FLAG = 'N'
        UNION ALL
        SELECT
        period_year,
        period_id periodId,
        SHIPMENT_QTY shipmentQty,
        code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_CV_T
        WHERE
        GROUP_LEVEL = 'SPART'
        AND CODE_TYPE = 'OLD'
        <include refid="queryQtyCondition"/>
        <if test='oldSpartCode != null and oldSpartCode != ""'>
            AND GROUP_CODE = #{oldSpartCode,jdbcType=VARCHAR}
        </if>
        AND DEL_FLAG = 'N'
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>

    <select id="getPbiIndexQtyExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN round(shipmentQty,0) ELSE NULL END ) as newQty,
        max(CASE WHEN codeType ='OLD' THEN  round(shipmentQty,0) ELSE NULL END ) as oldQty
        FROM(SELECT
        period_year,
        period_id periodId,
        SHIPMENT_QTY shipmentQty,
        code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_CV_T t1
        WHERE
        GROUP_LEVEL = #{VO.groupLevel,jdbcType=VARCHAR}
        <include refid="queryCVCondition"/>
        AND REGION_CODE = #{VO.regionCode}
        AND REPOFFICE_CODE = #{VO.repofficeCode}
        AND BG_CODE = #{VO.bgCode}
        AND OVERSEA_FLAG = #{VO.overseaFlag}
        AND DEL_FLAG = 'N'
        <choose>
            <when test='VO.groupLevel == "LV1"'>
                AND PROD_LIST_CODE = #{VO.lv1ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV2"'>
                AND PROD_LIST_CODE = #{VO.lv2ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV3"'>
                AND PROD_LIST_CODE = #{VO.lv3ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV4"'>
                AND PROD_LIST_CODE = #{VO.lv4ProdTeamCode}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <include refid="userPermission"></include>
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>
    <select id="getBlurSpartIndexQtyExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN round(shipmentQty,0) ELSE NULL END ) as newQty,
        max(CASE WHEN codeType ='OLD' THEN round(shipmentQty,0) ELSE NULL END ) as oldQty
        FROM(SELECT
        period_year,
        period_id periodId,
        PART_QTY shipmentQty,
        code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_REPL_INFO_IDX_T
        WHERE
        CUSTOM_ID = #{VO.customId,jdbcType=VARCHAR}
        <include refid="queryQtyCondition"/>
        AND GROUP_LEVEL_TYPE ='PBI'
        AND GRANULARITY_TYPE = #{VO.granularityType}
        AND code_type = 'NEW'
        AND DEL_FLAG = 'N'
        UNION ALL
        SELECT
        period_year,
        period_id periodId,
        PART_QTY shipmentQty,
        code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_REPL_INFO_IDX_T
        WHERE
        CUSTOM_ID = #{VO.customId,jdbcType=VARCHAR}
        <include refid="queryQtyCondition"/>
        AND GRANULARITY_TYPE = #{VO.granularityType}
        AND GROUP_LEVEL_TYPE ='PBI'
        AND code_type = 'OLD'
        AND DEL_FLAG = 'N'
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>

    <select id="getPreBlurSpartIndexQtyExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year ,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN round(shipmentQty,0) ELSE NULL END ) as newQty,
        max(CASE WHEN codeType ='OLD' THEN round(shipmentQty,0) ELSE NULL END ) as oldQty
        FROM(SELECT
        period_year,
        period_id periodId,
        PART_QTY shipmentQty,
        code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_REPL_INFO_IDX_T
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <include refid="queryPreBlurCondition"/>
            AND DEL_FLAG = 'N'
        </trim>
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>

    <select id="getSpartIndexAccQtyExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN round(shipmentQty,0) ELSE NULL END ) as newQty,
        max(CASE WHEN codeType ='OLD' THEN round(shipmentQty,0) ELSE NULL END ) as oldQty
        FROM(SELECT
        period_year,
        period_id periodId,
        SHIPMENT_QTY shipmentQty,
        CODE_TYPE codeType
        FROM
        <!-->代替换月累计表</-->
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_CV_T
        WHERE
        GROUP_LEVEL = 'SPART'
        <include refid="queryQtyCondition"/>
        AND CODE_TYPE = 'NEW'
        <if test='newSpartCode !=""'>
            AND GROUP_CODE = #{newSpartCode,jdbcType=VARCHAR}
        </if>
        <if test='VO.newProdTeamCode !=""'>
            AND parent_code = #{VO.newProdTeamCode}
        </if>
        AND DEL_FLAG = 'N'
        UNION ALL
        SELECT
        period_year,
        period_id periodId,
        SHIPMENT_QTY shipmentQty,
        code_type codeType
        FROM
        <!-->代替换月累计表</-->
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_CV_T
        WHERE
        GROUP_LEVEL = 'SPART'
        <include refid="queryQtyCondition"/>
        AND CODE_TYPE = 'OLD'
        <if test='oldSpartCode !=""'>
            AND GROUP_CODE = #{oldSpartCode,jdbcType=VARCHAR}
        </if>
        <if test='VO.oldProdTeamCode !=""'>
            AND parent_code = #{VO.oldProdTeamCode}
        </if>
        AND DEL_FLAG = 'N'
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>

    <select id="getPbiIndexAccQtyExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN round(shipmentQty,2) ELSE NULL END ) as newQty,
        max(CASE WHEN codeType ='OLD' THEN round(shipmentQty,2) ELSE NULL END ) as oldQty
        FROM(SELECT
        period_year,period_id periodId,
        SHIPMENT_QTY shipmentQty,code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_CV_T
        WHERE
        GROUP_LEVEL = #{VO.groupLevel,jdbcType=VARCHAR}
        <include refid="queryQtyCondition"/>
        <choose>
            <when test='VO.groupLevel == "LV1"'>
                AND PROD_LIST_CODE = #{VO.lv1ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV2"'>
                AND PROD_LIST_CODE = #{VO.lv2ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV3"'>
                AND PROD_LIST_CODE = #{VO.lv3ProdTeamCode}
            </when>
            <when test='VO.groupLevel == "LV4"'>
                AND PROD_LIST_CODE = #{VO.lv4ProdTeamCode}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <include refid="userPermission"></include>
        AND DEL_FLAG = 'N'
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>
    <select id="getBlurSpartIndexAccQtyExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN round(shipmentQty,0) ELSE NULL END ) as newQty,
        max(CASE WHEN codeType ='OLD' THEN round(shipmentQty,0) ELSE NULL END ) as oldQty
        FROM(SELECT
        t1.period_year,
        t1.period_id periodId,
        t1.PART_QTY shipmentQty,
        code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_INFO_IDX_T t1
        WHERE
        CUSTOM_ID = #{VO.customId}
        <include refid="queryQtyCondition"/>
        AND GROUP_LEVEL_TYPE ='PBI'
        AND GRANULARITY_TYPE = #{VO.granularityType}
        AND code_type = 'NEW'
        <if test='VO.newProdTeamCode !=""'>
            AND parent_code = #{VO.newProdTeamCode}
        </if>
        AND DEL_FLAG = 'N'
        UNION ALL
        SELECT
        t2.period_year,
        t2.period_id periodId,
        t2.PART_QTY shipmentQty,
        code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_INFO_IDX_T t2
        WHERE
        CUSTOM_ID = #{VO.customId}
        <include refid="queryQtyCondition"/>
        AND GROUP_LEVEL_TYPE ='PBI'
        AND GRANULARITY_TYPE = #{VO.granularityType}
        AND code_type = 'OLD'
        AND DEL_FLAG = 'N'
        <if test='VO.oldProdTeamCode !=""'>
            AND parent_code = #{VO.oldProdTeamCode}
        </if>
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>

    <select id="getPreBlurSpartIndexAccQtyExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO">
        SELECT
        period_year,
        periodId,
        max(CASE WHEN codeType ='NEW' THEN round(shipmentQty,0) ELSE NULL END ) as newQty,
        max(CASE WHEN codeType ='OLD' THEN round(shipmentQty,0) ELSE NULL END ) as oldQty
        FROM(SELECT
        t1.period_year,
        t1.period_id periodId,
        t1.PART_QTY shipmentQty,
        t1.code_type codeType
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_INFO_IDX_T t1
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <include refid="queryPreBlurCondition"/>
            AND DEL_FLAG = 'N'
        </trim>
        )
        group by period_year,periodId
        ORDER BY periodId asc
    </select>

    <select id="getSpartsL1Name" resultType="java.lang.String">
        SELECT distinct lv1_cn_name
        FROM
            fin_dm_opt_foi.DM_FCST_ICT_CODE_REPL_INFO_T
        WHERE DEL_FLAG = 'N'
          AND version_id = #{VO.versionId}
          AND old_spart_code = #{oldSpartCode}
          ANd new_spart_code = #{newSpartCode}
    </select>
    <select id="getBlurCombShipQuery"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        period_year,
        period_id periodId,
        round(PART_QTY,0)  shipmentQty
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_REPL_INFO_IDX_T
        WHERE
        CUSTOM_ID =  #{customId}
        <include refid="queryQtyCondition"/>
        AND GRANULARITY_TYPE = #{VO.granularityType}
        AND GROUP_CODE =  #{spartCode}
        <if test='lv4ProdTeamCode !=""'>
            AND parent_code = #{lv4ProdTeamCode}
        </if>
        AND DEL_FLAG = 'N'
        ORDER BY  periodId asc
    </select>
    <select id="getCombShipQuery"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        period_year,
        period_id periodId,
        round(SHIPMENT_QTY,0)  shipmentQty
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_CV_T
        WHERE
        GROUP_LEVEL = 'TOP-SPART'
        <include refid="queryQtyCondition"/>
        AND GROUP_CODE = #{spartCode}
        <if test='lv4ProdTeamCode != ""'>
            AND parent_code = #{lv4ProdTeamCode}
        </if>
        AND DEL_FLAG = 'N'
    </select>
    <select id="getBlurAccCombShipQuery"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        period_year,
        period_id periodId,
        round(PART_QTY,0) shipmentQty
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_INFO_IDX_T
        WHERE
        CUSTOM_ID = #{customId}
        <include refid="queryQtyCondition"/>
        AND GRANULARITY_TYPE = #{VO.granularityType}
        AND GROUP_CODE =  #{spartCode}
        <if test='lv4ProdTeamCode != ""'>
            AND parent_code = #{lv4ProdTeamCode}
        </if>
        AND DEL_FLAG = 'N'
        ORDER BY  periodId asc
    </select>
    <select id="getCombAccShipQuery"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        period_year,
        period_id periodId,
        round(SHIPMENT_QTY,0) shipmentQty
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_CV_T
        WHERE
        GROUP_CODE = #{spartCode}
        <include refid="queryQtyCondition"/>
        <if test='lv4ProdTeamCode != ""'>
            AND parent_code = #{lv4ProdTeamCode}
        </if>
        AND DEL_FLAG = 'N'
    </select>
    <select id="getBlurSpartCVList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        t1.period_year,
        t1.period_id periodId,
        t1.RMB_COST_AMT rmbCostAmt
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_REPL_INFO_IDX_T t1
        WHERE
        t1.custom_id = #{customId}
        AND t1.GROUP_CODE = #{spartCode}
        <include refid="queryCVCondition"/>
        AND t1.GRANULARITY_TYPE = #{VO.granularityType}
        <if test='lv4ProdTeamCode != ""'>
            AND t1.parent_code = #{lv4ProdTeamCode}
        </if>
        AND t1.DEL_FLAG = 'N'
        ORDER BY  periodId asc
    </select>
    <select id="getSpartCVList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        period_year,period_id periodId,
        <if test='VO.tablePrefixCostType == "PSP"'>
            t1.RMB_COST_AMT rmbCostAmt
        </if>
        <if test='VO.tablePrefixCostType != "PSP" and (groupType == "SPART" or groupType == "TOP-SPART")'>
            GS_DECRYPT(NVL(t1.RMB_COST_AMT,gs_encrypt('',#{keyStr}, 'aes128', 'cbc', 'sha256')),#{keyStr,jdbcType=VARCHAR}, 'AES128', 'CBC', 'SHA256') rmbCostAmt
        </if>
        <if test='VO.tablePrefixCostType != "PSP" and groupType != "SPART" and groupType != "TOP-SPART"'>
            t1.RMB_COST_AMT rmbCostAmt
        </if>
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_MON_REPL_COST_CV_T t1
        WHERE
        t1.GROUP_LEVEL = #{groupType}
        <include refid="queryCVCondition"/>
        <if test='spartCode != null and spartCode != ""'>
            AND t1.GROUP_CODE = #{spartCode}
        </if>
        <if test='lv4ProdTeamCode != ""'>
            AND t1.parent_code = #{lv4ProdTeamCode}
        </if>
          AND  t1.DEL_FLAG = 'N'
        ORDER BY  periodId asc
    </select>
    <select id="getBlurSpartCVExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        t1.period_year,
        t1.period_id periodId,
        t1.RMB_COST_AMT rmbCostAmt
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_REPL_INFO_IDX_T t1
        WHERE
        t1.custom_id = #{customId}
        <include refid="queryCVCondition"/>
        AND t1.GRANULARITY_TYPE = #{VO.granularityType}
        AND t1.code_type = #{codeType}
        <if test='lv4ProdTeamCode != ""'>
            AND t1.parent_code = #{lv4ProdTeamCode}
        </if>
        AND  t1.DEL_FLAG = 'N'
        ORDER BY  periodId asc
    </select>

    <select id="getPreBlurSpartCVExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        t1.period_year,
        t1.period_id periodId,
        t1.RMB_COST_AMT rmbCostAmt,
        t1.code_type
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_MON_REPL_INFO_IDX_T t1
      <trim prefix="WHERE" prefixOverrides="AND |OR ">
        <include refid="queryPreBlurCondition"/>
        <if test='codeType != "" and codeType != null'>
            AND t1.code_type = #{codeType}
        </if>
        AND t1.DEL_FLAG = 'N'
      </trim>
        ORDER BY  periodId asc
    </select>
    <select id="getBlurSpartCVAccList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        t1.period_year,t1.period_id periodId,
        t1.RMB_COST_AMT rmbCostAmt
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_INFO_IDX_T t1
        WHERE
        t1.CUSTOM_ID = #{customId}
        <include refid="queryCVCondition"/>
        AND t1.GRANULARITY_TYPE = #{VO.granularityType}
        <if test='lv4ProdTeamCode != ""'>
            AND t1.parent_code = #{lv4ProdTeamCode}
        </if>
        AND t1.DEL_FLAG = 'N'
        ORDER BY  periodId asc
    </select>
    <select id="getSpartCVAccList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        period_year,period_id periodId,
        <if test='VO.tablePrefixCostType == "PSP"'>
            t1.RMB_COST_AMT rmbCostAmt
        </if>
        <if test='VO.tablePrefixCostType != "PSP" and (groupType == "SPART" or groupType == "TOP-SPART")'>
            GS_DECRYPT(NVL(t1.RMB_COST_AMT,gs_encrypt('',#{keyStr}, 'aes128', 'cbc', 'sha256')),#{keyStr,jdbcType=VARCHAR}, 'AES128', 'CBC', 'SHA256') rmbCostAmt
        </if>
        <if test='VO.tablePrefixCostType != "PSP" and groupType != "SPART" and groupType != "TOP-SPART"'>
            t1.RMB_COST_AMT rmbCostAmt
        </if>
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_${VO.tablePrefixPbi}_YTD_REPL_COST_CV_T t1
        WHERE
        t1.DEL_FLAG = 'N'
        <include refid="queryCVCondition"/>
        AND t1.GROUP_LEVEL = #{groupType}
        <if test='spartCode != ""'>
            AND t1.GROUP_CODE = #{spartCode}
        </if>
        <if test='lv4ProdTeamCode != ""'>
            AND t1.PARENT_CODE = #{lv4ProdTeamCode}
        </if>
        ORDER BY  periodId asc
    </select>

    <select id="getBlurSpartCVAccExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        t1.period_year,t1.period_id periodId,
        t1.RMB_COST_AMT rmbCostAmt
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_INFO_IDX_T t1
        WHERE
        t1.CUSTOM_ID = #{customId}
        <include refid="queryCVCondition"/>
        AND t1.GRANULARITY_TYPE = #{VO.granularityType}
        AND t1.CODE_TYPE = #{codeType}
        <if test='lv4ProdTeamCode != ""'>
            AND t1.parent_code = #{lv4ProdTeamCode}
        </if>
        AND t1.DEL_FLAG = 'N'
        ORDER BY  periodId asc
    </select>

    <select id="getPreBlurSpartCVAccExpList"
            resultType="com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO">
        SELECT
        t1.period_year,t1.period_id periodId,
        t1.RMB_COST_AMT rmbCostAmt,
        t1.CODE_TYPE
        FROM
        fin_dm_opt_foi.DM_FCST_ICT_${VO.tablePrefixCostType}_BASE_CUS_YTD_REPL_INFO_IDX_T t1
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <include refid="queryPreBlurCondition"/>
            <if test='codeType != "" and codeType != null'>
                AND t1.code_type = #{codeType}
            </if>
             AND t1.DEL_FLAG = 'N'
        </trim>
        ORDER BY  periodId asc
    </select>
</mapper>
