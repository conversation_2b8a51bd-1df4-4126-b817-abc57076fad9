/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.config;

import cn.hutool.core.util.StrUtil;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstDataRefreshStatusDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstTopSpartInfoDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstVersionInfoDao;
import com.huawei.it.fcst.industry.pbi.enums.IndustryConstEnum;
import com.huawei.it.fcst.industry.pbi.impl.template.ExcelConfigTemplateEnum;
import com.huawei.it.fcst.industry.pbi.service.config.IHistoryTopSpartService;
import com.huawei.it.fcst.export.IExcelTemplateBeanManager;
import com.huawei.it.fcst.export.IExportProcessorService;
import com.huawei.it.fcst.industry.pbi.utils.FcstIndustryUtil;
import com.huawei.it.fcst.industry.pbi.vo.common.CommonBaseVO;
import com.huawei.it.fcst.industry.pbi.vo.common.DmFcstDataRefreshStatus;
import com.huawei.it.fcst.industry.pbi.vo.config.DmFcstTopSpartInfoDTO;
import com.huawei.it.fcst.industry.pbi.vo.config.HistoryTopSpartInfoVO;
import com.huawei.it.fcst.industry.pbi.vo.version.DmFcstVersionInfoDTO;
import com.huawei.it.fcst.util.ObjectCopyUtil;
import com.huawei.it.fcst.util.UserInfoUtils;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.inject.Inject;
import javax.inject.Named;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/7/10
 */
@Named("historyTopSpartService")
@JalorResource(code = "historyTopSpartService", desc = "NEW ICT-配置管理历史spart清单")
public class HistoryTopSpartService implements IHistoryTopSpartService {

    @Inject
    private IDmFcstVersionInfoDao dmFcstVersionInfoDao;

    @Inject
    private IDmFcstTopSpartInfoDao dmFcstTopSpartInfoDao;

    @Inject
    private IExportProcessorService iExportProcessorService;

    @Inject
    private IDmFcstDataRefreshStatusDao dmFcstDataRefreshStatusDao;

    @Override
    @JalorOperation(code = "getTopSpartPageList", desc = "历史spart清单分页查询")
    public ResultDataVO getTopSpartPageList(HistoryTopSpartInfoVO historyTopSpartInfoVO) throws CommonApplicationException {
        if (null == historyTopSpartInfoVO.getVersionId()) {
            throw new CommonApplicationException("入参清单数据版本为空");
        }
        // 必填参数校验
        vaildParam(historyTopSpartInfoVO);
        // 设置报告期范围
        DmFcstVersionInfoDTO versionVO = dmFcstVersionInfoDao.findDmFocVersionDTOById(historyTopSpartInfoVO.getVersionId());
        if (StrUtil.isBlank(versionVO.getVersion())) {
            throw new CommonApplicationException("入参清单数据版本名称为空");
        }
        FcstIndustryUtil.setRegionCode(historyTopSpartInfoVO);
        List<String> yearPeriodList = FcstIndustryUtil.getPeriod(versionVO.getVersion());
        // 设置品类查询对象
        DmFcstTopSpartInfoDTO build = setTopSpartVO(historyTopSpartInfoVO, yearPeriodList);
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(historyTopSpartInfoVO.getPageSize());
        pageVO.setCurPage(historyTopSpartInfoVO.getPageIndex());
        PagedResult<DmFcstTopSpartInfoDTO> cateByPage = dmFcstTopSpartInfoDao.findTopSpartByPage(build, pageVO);
        Map result = new LinkedHashMap();
        List<DmFcstTopSpartInfoDTO> spartInfoResult = cateByPage.getResult();
        for (DmFcstTopSpartInfoDTO dmFcstTopSpartInfoDTO : spartInfoResult) {
            // topSpart标签
            if (StringUtils.isBlank(dmFcstTopSpartInfoDTO.getTopFlag())) {
                dmFcstTopSpartInfoDTO.setTopFlag("N");
            }
            dmFcstTopSpartInfoDTO.setTopFlag(IndustryConstEnum.getMainFlag(dmFcstTopSpartInfoDTO.getTopFlag()).getDesc());
            // 软硬件标识
            if (IndustryConstEnum.COST_TYPE.PSP.getValue().equals(dmFcstTopSpartInfoDTO.getCostType())) {
                dmFcstTopSpartInfoDTO.setSoftwareMark(IndustryConstEnum.getSoftwareMark(dmFcstTopSpartInfoDTO.getSoftwareMark()).getDesc());
            }
            // 主力编码
            dmFcstTopSpartInfoDTO.setMainFlag(IndustryConstEnum.getMainFlag(dmFcstTopSpartInfoDTO.getMainFlag()).getDesc());
        }
        result.put("result", cateByPage.getResult());
        result.put("pageVO", cateByPage.getPageVO());
        result.put("version", versionVO.getVersion());
        return ResultDataVO.success(result);
    }

    private void vaildParam(HistoryTopSpartInfoVO historyTopSpartInfoVO) throws CommonApplicationException {
        // 必填参数校验
        if (StringUtils.isAnyBlank(historyTopSpartInfoVO.getBgCode(),historyTopSpartInfoVO.getOverseaFlag(),
                historyTopSpartInfoVO.getRegionCode(), historyTopSpartInfoVO.getRepofficeCode())) {
            throw new CommonApplicationException("必填参数为空");
        }
    }

    private DmFcstTopSpartInfoDTO setTopSpartVO(CommonBaseVO commonBaseVO, List<String> yearPeriodList) {
        DmFcstTopSpartInfoDTO topSpartDTO = ObjectCopyUtil.copy(commonBaseVO, DmFcstTopSpartInfoDTO.class);
        topSpartDTO.setYearPeriodList(yearPeriodList);
        if (CollectionUtils.isNotEmpty(yearPeriodList)) {
            topSpartDTO.setPeriodYear0(yearPeriodList.get(0));
            topSpartDTO.setPeriodYear1(yearPeriodList.get(1));
            topSpartDTO.setPeriodYear2(yearPeriodList.get(2));
            topSpartDTO.setPeriodYear3(yearPeriodList.get(3));
            topSpartDTO.setPeriodYear4(yearPeriodList.get(4));
        }
        return topSpartDTO;
    }

    @Override
    @JalorOperation(code = "topSpartExport", desc = "历史spart清单导出")
    @Audit(module = "historyTopSpartService-topSpartExport", operation = "topSpartExport", message = "历史spart清单导出")
    public ResultDataVO topSpartExport(HttpServletResponse response, HistoryTopSpartInfoVO historyTopSpartInfoVO) throws ApplicationException {
        // 必填参数校验
        vaildParam(historyTopSpartInfoVO);
        IExcelTemplateBeanManager templateBeanManager;
        if (IndustryConstEnum.COST_TYPE.STD.getValue().equals(historyTopSpartInfoVO.getCostType())) {
            templateBeanManager = ExcelConfigTemplateEnum.getByCode("01", "");
        } else {
            templateBeanManager = ExcelConfigTemplateEnum.getByCode("02", "");
        }
        Map<String, Object> parameters = new HashMap<>();
        String module = "成本指数-ICT-配置管理";
        parameters.put("exportModuleName", module);
        parameters.put("exportFileName", historyTopSpartInfoVO.getFileName());
        DmFcstDataRefreshStatus dataRefreshStatus = new DmFcstDataRefreshStatus();
        dataRefreshStatus.setStatus("TASK_INIT");
        dataRefreshStatus.setDelFlag("N");
        Long userId = UserInfoUtils.getUserId();
        dataRefreshStatus.setCreatedBy(userId);
        dataRefreshStatus.setLastUpdatedBy(userId);
        dataRefreshStatus.setCreationDate(new Date());
        dataRefreshStatus.setTaskFlag("CONFIG_" + historyTopSpartInfoVO.getCostType() + "_" + "_EXPORT_" + historyTopSpartInfoVO.getGranularityType());
        dataRefreshStatus.setTaskId(dmFcstDataRefreshStatusDao.getDataRefrashKey());
        dataRefreshStatus.setRoleId(UserInfoUtils.getRoleId());
        // 创建异步任务记录，进行新增操作
        dmFcstDataRefreshStatusDao.createDmFcstDataRefreshStatus(dataRefreshStatus);
        parameters.put("taskId", dataRefreshStatus.getTaskId());
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(10000);
        iExportProcessorService.asyncFillEasyExcelExport(templateBeanManager, historyTopSpartInfoVO, parameters, pageVO);
        return ResultDataVO.success(dataRefreshStatus);
    }

    @Override
    @JalorOperation(code = "queryDataRefreshStatus", desc = "轮询任务状态")
    public ResultDataVO queryDataRefreshStatus(DmFcstDataRefreshStatus dataRefreshStatus) throws CommonApplicationException {
        if (null == dataRefreshStatus.getTaskId() || dataRefreshStatus.getTaskId() <= 0L) {
            throw new CommonApplicationException("该任务ID有误!");
        }
        DmFcstDataRefreshStatus dmFcstDataRefreshStatus = dmFcstDataRefreshStatusDao.findDmFcstDataRefreshStatusById(dataRefreshStatus);
        return ResultDataVO.success(dmFcstDataRefreshStatus);
    }

}
