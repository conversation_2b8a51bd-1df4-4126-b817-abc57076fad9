<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IDmFocTotalDmsViewInfoDao">
    <resultMap type="com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO" id="resultMap">
        <result property="id" column="id"/>
        <result property="lv0ProdRndTeamCode" column="lv0_prod_rnd_team_code"/>
        <result property="lv0ProdRdTeamCnName" column="lv0_prod_rd_team_cn_name"/>
        <result property="lv1ProdRndTeamCode" column="lv1_prod_rnd_team_code"/>
        <result property="lv1ProdRdTeamCnName" column="lv1_prod_rd_team_cn_name"/>
        <result property="lv2ProdRndTeamCode" column="lv2_prod_rnd_team_code"/>
        <result property="lv2ProdRdTeamCnName" column="lv2_prod_rd_team_cn_name"/>
        <result property="lv3ProdRndTeamCode" column="lv3_prod_rnd_team_code"/>
        <result property="lv3ProdRdTeamCnName" column="lv3_prod_rd_team_cn_name"/>
        <result property="prodRndTeamCode" column="prod_rnd_team_code"/>
        <result property="itemCode" column="item_code"/>
        <result property="itemCnName" column="item_cn_name"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDdate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="groupCode" column="groupCode"/>
        <result property="groupLevel" column="groupLevel"/>
        <result property="groupCnName" column="groupCnName"/>
        <result property="weightRate" column="weight_rate"/>
    </resultMap>
    <sql id="weight_rate">
        <if test='viewFlag != null and viewFlag == "0"'>
            <choose>
                <when test='teamLevel == "LV1"'>
                    DISTINCT amp.dimension_code  AS groupCode, amp.dimension_cn_name AS groupCnName, 'DIMENSION' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "1"'>
            <choose>
                <when test='groupLevel == "DIMENSION"'>
                    DISTINCT amp.dimension_subcategory_code AS groupCode, amp.dimension_subcategory_cn_name AS groupCnName, 'SUBCATEGORY' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT amp.dimension_code AS groupCode, amp.dimension_cn_name AS groupCnName, 'DIMENSION' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "2"'>
            <choose>
                <when test='groupLevel == "SUBCATEGORY"'>
                    DISTINCT amp.dimension_sub_detail_code AS groupCode, amp.dimension_sub_detail_cn_name AS groupCnName, 'SUB_DETAIL' AS groupLevel,weight_rate
                </when>
                <when test='groupLevel == "DIMENSION"'>
                    DISTINCT amp.dimension_subcategory_code AS groupCode, amp.dimension_subcategory_cn_name AS groupCnName, 'SUBCATEGORY' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT amp.dimension_code AS groupCode, amp.dimension_cn_name AS groupCnName, 'DIMENSION' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "9"'>
            <choose>
                <when test='groupLevel == "SUB_DETAIL"'>
                    DISTINCT amp.spart_code AS groupCode, amp.spart_cn_name AS groupCnName, 'SPART' AS groupLevel,weight_rate
                </when>
                <when test='groupLevel == "SUBCATEGORY"'>
                    DISTINCT amp.dimension_sub_detail_code AS groupCode, amp.dimension_sub_detail_cn_name AS groupCnName, 'SUB_DETAIL' AS groupLevel,weight_rate
                </when>
                <when test='groupLevel == "DIMENSION"'>
                    DISTINCT amp.dimension_subcategory_code AS groupCode, amp.dimension_subcategory_cn_name AS groupCnName, 'SUBCATEGORY' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT amp.dimension_code AS groupCode, amp.dimension_cn_name AS groupCnName, 'DIMENSION' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "3"'>
            <choose>
                <when test='teamLevel == "LV2"'>
                    DISTINCT amp.dimension_code AS groupCode, amp.dimension_cn_name AS groupCnName, 'DIMENSION' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code, lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "4"'>
            <choose>
                <when test='groupLevel == "DIMENSION"'>
                    DISTINCT amp.dimension_subcategory_code AS groupCode, amp.dimension_subcategory_cn_name AS groupCnName, 'SUBCATEGORY' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT amp.dimension_code AS groupCode, amp.dimension_cn_name AS groupCnName, 'DIMENSION' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code, lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "5"'>
            <choose>
                <when test='groupLevel == "SUBCATEGORY"'>
                    DISTINCT amp.dimension_sub_detail_code AS groupCode, amp.dimension_sub_detail_cn_name AS groupCnName, 'SUB_DETAIL' AS groupLevel,weight_rate
                </when>
                <when test='groupLevel == "DIMENSION"'>
                    DISTINCT amp.dimension_subcategory_code AS groupCode, amp.dimension_subcategory_cn_name AS groupCnName, 'SUBCATEGORY' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT amp.dimension_code AS groupCode, amp.dimension_cn_name AS groupCnName, 'DIMENSION' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code, lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "10"'>
            <choose>
                <when test='groupLevel == "SUB_DETAIL"'>
                    DISTINCT amp.spart_code AS groupCode, amp.spart_cn_name AS groupCnName, 'SPART' AS groupLevel,weight_rate
                </when>
                <when test='groupLevel == "SUBCATEGORY"'>
                    DISTINCT amp.dimension_sub_detail_code AS groupCode, amp.dimension_sub_detail_cn_name AS groupCnName, 'SUB_DETAIL' AS groupLevel,weight_rate
                </when>
                <when test='groupLevel == "DIMENSION"'>
                    DISTINCT amp.dimension_subcategory_code AS groupCode, amp.dimension_subcategory_cn_name AS groupCnName, 'SUBCATEGORY' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT amp.dimension_code AS groupCode, amp.dimension_cn_name AS groupCnName, 'DIMENSION' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code, lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "6"'>
            <choose>
                <when test='teamLevel == "LV3"'>
                    DISTINCT amp.dimension_code AS groupCode, amp.dimension_cn_name AS groupCnName, 'DIMENSION' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS
                    groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code, lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "7"'>
            <choose>
                <when test='groupLevel == "DIMENSION"'>
                    DISTINCT amp.dimension_subcategory_code AS groupCode, amp.dimension_subcategory_cn_name AS groupCnName, 'SUBCATEGORY' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV3"'>
                    DISTINCT amp.dimension_code AS groupCode, amp.dimension_cn_name AS groupCnName, 'DIMENSION' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS
                    groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code, lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "8"'>
            <choose>
                <when test='groupLevel == "SUBCATEGORY"'>
                    DISTINCT amp.dimension_sub_detail_code AS groupCode, amp.dimension_sub_detail_cn_name AS groupCnName, 'SUB_DETAIL' AS groupLevel,weight_rate
                </when>
                <when test='groupLevel == "DIMENSION"'>
                    DISTINCT amp.dimension_subcategory_code AS groupCode, amp.dimension_subcategory_cn_name AS groupCnName, 'SUBCATEGORY' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV3"'>
                    DISTINCT amp.dimension_code AS groupCode, amp.dimension_cn_name AS groupCnName, 'DIMENSION' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS
                    groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code, lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "11"'>
            <choose>
                <when test='groupLevel == "SUB_DETAIL"'>
                    DISTINCT amp.spart_code AS groupCode, amp.spart_cn_name AS groupCnName, 'SPART' AS groupLevel,weight_rate
                </when>
                <when test='groupLevel == "SUBCATEGORY"'>
                    DISTINCT amp.dimension_sub_detail_code AS groupCode, amp.dimension_sub_detail_cn_name AS groupCnName, 'SUB_DETAIL' AS groupLevel,weight_rate
                </when>
                <when test='groupLevel == "DIMENSION"'>
                    DISTINCT amp.dimension_subcategory_code AS groupCode, amp.dimension_subcategory_cn_name AS groupCnName, 'SUBCATEGORY' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV3"'>
                    DISTINCT amp.dimension_code AS groupCode, amp.dimension_cn_name AS groupCnName, 'DIMENSION' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS
                    groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code, lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "12"'>
            <choose>
                <when test='groupLevel == "SUB_DETAIL"'>
                    DISTINCT amp.spart_code AS groupCode, amp.spart_cn_name AS groupCnName, 'SPART' AS groupLevel,weight_rate
                </when>
                <when test='groupLevel == "SUBCATEGORY"'>
                    DISTINCT amp.dimension_sub_detail_code AS groupCode, amp.dimension_sub_detail_cn_name AS groupCnName, 'SUB_DETAIL' AS groupLevel,weight_rate
                </when>
                <when test='groupLevel == "DIMENSION"'>
                    DISTINCT amp.dimension_subcategory_code AS groupCode, amp.dimension_subcategory_cn_name AS groupCnName, 'SUBCATEGORY' AS groupLevel,weight_rate
                </when>
                <when test='groupLevel == "COA" or groupLevel == "LV4"'>
                    DISTINCT amp.dimension_code AS groupCode, amp.dimension_cn_name AS groupCnName, 'DIMENSION' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV3"'>
                   <if test='industryOrg != "IAS"'>
                       DISTINCT amp.coa_code AS groupCode, amp.coa_cn_name AS groupCnName, 'COA' AS groupLevel,weight_rate
                   </if>
                    <if test='industryOrg == "IAS"'>
                        DISTINCT amp.lv4_prod_rnd_team_code AS groupCode, amp.lv4_prod_rd_team_cn_name AS groupCnName, 'LV4' AS groupLevel,weight_rate
                    </if>
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS
                    groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code, lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
    </sql>
    <sql id="absolute_rate">
        <if test='viewFlag != null and viewFlag == "0"'>
            <choose>
                <when test='teamLevel == "LV1"'>
                    DISTINCT amp.dimension_code AS groupCode, amp.dimension_cn_name AS groupCnName, 'DIMENSION' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "1"'>
            <choose>
                <when test='groupLevel == "DIMENSION"'>
                    DISTINCT amp.dimension_subcategory_code AS groupCode, amp.dimension_subcategory_cn_name AS groupCnName, 'SUBCATEGORY' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT amp.dimension_code AS groupCode, amp.dimension_cn_name AS groupCnName, 'DIMENSION' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "2"'>
            <choose>
                <when test='groupLevel == "SUBCATEGORY"'>
                    DISTINCT amp.dimension_sub_detail_code AS groupCode, amp.dimension_sub_detail_cn_name AS groupCnName, 'SUB_DETAIL' AS groupLevel,absolute_weight
                </when>
                <when test='groupLevel == "DIMENSION"'>
                    DISTINCT amp.dimension_subcategory_code AS groupCode, amp.dimension_subcategory_cn_name AS groupCnName, 'SUBCATEGORY' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT amp.dimension_code AS groupCode, amp.dimension_cn_name AS groupCnName, 'DIMENSION' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "9"'>
            <choose>
                <when test='groupLevel == "SUB_DETAIL"'>
                    DISTINCT amp.spart_code AS groupCode, amp.spart_cn_name AS groupCnName, 'SPART' AS groupLevel,absolute_weight
                </when>
                <when test='groupLevel == "SUBCATEGORY"'>
                    DISTINCT amp.dimension_sub_detail_code AS groupCode, amp.dimension_sub_detail_cn_name AS groupCnName, 'SUB_DETAIL' AS groupLevel,absolute_weight
                </when>
                <when test='groupLevel == "DIMENSION"'>
                    DISTINCT amp.dimension_subcategory_code AS groupCode, amp.dimension_subcategory_cn_name AS groupCnName, 'SUBCATEGORY' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT amp.dimension_code AS groupCode, amp.dimension_cn_name AS groupCnName, 'DIMENSION' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "3"'>
            <choose>
                <when test='teamLevel == "LV2"'>
                    DISTINCT amp.dimension_code AS groupCode, amp.dimension_cn_name AS groupCnName, 'DIMENSION' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code, lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "4"'>
            <choose>
                <when test='groupLevel == "DIMENSION"'>
                    DISTINCT amp.dimension_subcategory_code AS groupCode, amp.dimension_subcategory_cn_name AS groupCnName, 'SUBCATEGORY' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT amp.dimension_code AS groupCode, amp.dimension_cn_name AS groupCnName, 'DIMENSION' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code, lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "5"'>
            <choose>
                <when test='groupLevel == "SUBCATEGORY"'>
                    DISTINCT amp.dimension_sub_detail_code AS groupCode, amp.dimension_sub_detail_cn_name AS groupCnName, 'SUB_DETAIL' AS groupLevel,absolute_weight
                </when>
                <when test='groupLevel == "DIMENSION"'>
                    DISTINCT amp.dimension_subcategory_code AS groupCode, amp.dimension_subcategory_cn_name AS groupCnName, 'SUBCATEGORY' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT amp.dimension_code AS groupCode, amp.dimension_cn_name AS groupCnName, 'DIMENSION' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code, lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "10"'>
            <choose>
                <when test='groupLevel == "SUB_DETAIL"'>
                    DISTINCT amp.spart_code AS groupCode, amp.spart_cn_name AS groupCnName, 'SPART' AS groupLevel,absolute_weight
                </when>
                <when test='groupLevel == "SUBCATEGORY"'>
                    DISTINCT amp.dimension_sub_detail_code AS groupCode, amp.dimension_sub_detail_cn_name AS groupCnName, 'SUB_DETAIL' AS groupLevel,absolute_weight
                </when>
                <when test='groupLevel == "DIMENSION"'>
                    DISTINCT amp.dimension_subcategory_code AS groupCode, amp.dimension_subcategory_cn_name AS groupCnName, 'SUBCATEGORY' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT amp.dimension_code AS groupCode, amp.dimension_cn_name AS groupCnName, 'DIMENSION' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code, lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "6"'>
            <choose>
                <when test='teamLevel == "LV3"'>
                    DISTINCT amp.dimension_code AS groupCode, amp.dimension_cn_name AS groupCnName, 'DIMENSION' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS
                    groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code, lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "7"'>
            <choose>
                <when test='groupLevel == "DIMENSION"'>
                    DISTINCT amp.dimension_subcategory_code AS groupCode, amp.dimension_subcategory_cn_name AS groupCnName, 'SUBCATEGORY' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV3"'>
                    DISTINCT amp.dimension_code AS groupCode, amp.dimension_cn_name AS groupCnName, 'DIMENSION' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS
                    groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code, lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "8"'>
            <choose>
                <when test='groupLevel == "SUBCATEGORY"'>
                    DISTINCT amp.dimension_sub_detail_code AS groupCode, amp.dimension_sub_detail_cn_name AS groupCnName, 'SUB_DETAIL' AS groupLevel,absolute_weight
                </when>
                <when test='groupLevel == "DIMENSION"'>
                    DISTINCT amp.dimension_subcategory_code AS groupCode, amp.dimension_subcategory_cn_name AS groupCnName, 'SUBCATEGORY' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV3"'>
                    DISTINCT amp.dimension_code AS groupCode, amp.dimension_cn_name AS groupCnName, 'DIMENSION' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code, lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS
                    groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code, lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "11"'>
            <choose>
                <when test='groupLevel == "SUB_DETAIL"'>
                    DISTINCT amp.spart_code AS groupCode, amp.spart_cn_name AS groupCnName, 'SPART' AS groupLevel,absolute_weight
                </when>
                <when test='groupLevel == "SUBCATEGORY"'>
                    DISTINCT amp.dimension_sub_detail_code AS groupCode, amp.dimension_sub_detail_cn_name AS groupCnName, 'SUB_DETAIL' AS groupLevel,absolute_weight
                </when>
                <when test='groupLevel == "DIMENSION"'>
                    DISTINCT amp.dimension_subcategory_code AS groupCode, amp.dimension_subcategory_cn_name AS groupCnName, 'SUBCATEGORY' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV3"'>
                    DISTINCT amp.dimension_code AS groupCode, amp.dimension_cn_name AS groupCnName, 'DIMENSION' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code, lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS
                    groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code, lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "12"'>
            <choose>
                <when test='groupLevel == "SUB_DETAIL"'>
                    DISTINCT amp.spart_code AS groupCode, amp.spart_cn_name AS groupCnName, 'SPART' AS groupLevel,absolute_weight
                </when>
                <when test='groupLevel == "SUBCATEGORY"'>
                    DISTINCT amp.dimension_sub_detail_code AS groupCode, amp.dimension_sub_detail_cn_name AS groupCnName, 'SUB_DETAIL' AS groupLevel,absolute_weight
                </when>
                <when test='groupLevel == "DIMENSION"'>
                    DISTINCT amp.dimension_subcategory_code AS groupCode, amp.dimension_subcategory_cn_name AS groupCnName, 'SUBCATEGORY' AS groupLevel,absolute_weight
                </when>
                <when test='groupLevel == "COA" or groupLevel == "LV4"'>
                    DISTINCT amp.dimension_code AS groupCode, amp.dimension_cn_name AS groupCnName, 'DIMENSION' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV3"'>
                    <if test='industryOrg != "IAS"'>
                        DISTINCT amp.coa_code AS groupCode, amp.coa_cn_name AS groupCnName, 'COA' AS groupLevel,absolute_weight
                    </if>
                    <if test='industryOrg == "IAS"'>
                        DISTINCT amp.lv4_prod_rnd_team_code AS groupCode, amp.lv4_prod_rd_team_cn_name AS groupCnName, 'LV4' AS groupLevel,absolute_weight
                    </if>
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code, lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS
                    groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code, lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
    </sql>
    <select id="totalViewInfoList" resultMap="resultMap">
        SELECT
        <if test='isMultipleSelect == false'>
            <include refid="weight_rate"></include>
        </if>
        <if test='isMultipleSelect == true'>
            <include refid="absolute_rate"></include>
        </if>
        <choose>
            <when test='groupLevel=="SUB_DETAIL"'>
                from fin_dm_opt_foi.${tablePreFix}_total_dms_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_total_dms_annual_weight_t weight
                on amp.view_flag = weight.view_flag and weight.cost_type = 'T'
                <if test='industryOrg == "ENERGY"'>
                    AND nvl(amp.coa_code,'snull') =  nvl( weight.coa_code,'snull')
                </if>
                AND nvl(amp.dimension_code,'snull') =  nvl( weight.dimension_code,'snull')
                AND nvl(amp.dimension_subcategory_code,'snull') =  nvl( weight.dimension_subcategory_code,'snull')
                AND nvl(amp.dimension_sub_detail_code,'snull') =  nvl( weight.dimension_sub_detail_code,'snull')
                AND nvl(amp.spart_code,'snull') =  nvl( weight.spart_code,'snull')
                AND amp.lv0_prod_list_code = weight.lv0_prod_list_code
                AND amp.caliber_flag = weight.caliber_flag
                AND amp.oversea_flag = weight.oversea_flag
                AND amp.group_level = weight.group_level
                <if  test='viewFlag == "9"'>
                    and amp.lv1_prod_rnd_team_code = weight.prod_rnd_team_code
                    and amp.spart_code = weight.group_code
                </if>
                <if  test='viewFlag == "10"'>
                    and amp.lv2_prod_rnd_team_code = weight.prod_rnd_team_code
                    and amp.spart_code = weight.group_code
                </if>
                <if  test='viewFlag == "11"'>
                    and amp.lv3_prod_rnd_team_code = weight.prod_rnd_team_code
                    and amp.spart_code = weight.group_code
                </if>
                <if  test='viewFlag == "12"'>
                    <if test='industryOrg == "ENERGY"'>
                        and amp.lv3_prod_rnd_team_code = weight.prod_rnd_team_code
                    </if>
                    <if test='industryOrg == "IAS"'>
                        and amp.lv4_prod_rnd_team_code = weight.prod_rnd_team_code
                    </if>
                    and amp.spart_code = weight.group_code
                </if>
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
                and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>
            <when test='groupLevel=="SUBCATEGORY"'>
                from fin_dm_opt_foi.${tablePreFix}_total_dms_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_total_dms_annual_weight_t weight
                on amp.view_flag = weight.view_flag and weight.cost_type = 'T'
                <if test='industryOrg == "ENERGY"'>
                    AND nvl(amp.coa_code,'snull') =  nvl( weight.coa_code,'snull')
                </if>
                AND nvl(amp.dimension_code,'snull') =  nvl( weight.dimension_code,'snull')
                AND nvl(amp.dimension_subcategory_code,'snull') =  nvl( weight.dimension_subcategory_code,'snull')
                and nvl(amp.dimension_sub_detail_code,'snull') =  nvl( weight.dimension_sub_detail_code,'snull')
                AND amp.lv0_prod_list_code = weight.lv0_prod_list_code
                AND amp.caliber_flag = weight.caliber_flag
                AND amp.oversea_flag = weight.oversea_flag
                AND amp.group_level = weight.group_level
                <if  test='viewFlag == "2" or viewFlag == "9"'>
                    and amp.lv1_prod_rnd_team_code = weight.prod_rnd_team_code
                    and amp.dimension_subcategory_code = weight.parent_code
                    and amp.dimension_sub_detail_code = weight.group_code
                </if>
                <if  test='viewFlag == "5" or viewFlag == "10"'>
                    and amp.lv2_prod_rnd_team_code = weight.prod_rnd_team_code
                    and amp.dimension_subcategory_code = weight.parent_code
                    and amp.dimension_sub_detail_code = weight.group_code
                </if>
                <if  test='viewFlag == "8" or viewFlag == "11"'>
                    and amp.lv3_prod_rnd_team_code = weight.prod_rnd_team_code
                    and amp.dimension_subcategory_code = weight.parent_code
                    and amp.dimension_sub_detail_code = weight.group_code
                </if>
                <if  test='viewFlag == "12"'>
                    <if test='industryOrg == "ENERGY"'>
                        and amp.lv3_prod_rnd_team_code = weight.prod_rnd_team_code
                    </if>
                    <if test='industryOrg == "IAS"'>
                        and amp.lv4_prod_rnd_team_code = weight.prod_rnd_team_code
                    </if>
                    and amp.dimension_subcategory_code = weight.parent_code
                    and amp.dimension_sub_detail_code = weight.group_code
                </if>
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
                and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>
            <when test='groupLevel=="DIMENSION"'>
                from fin_dm_opt_foi.${tablePreFix}_total_dms_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_total_dms_annual_weight_t weight
                on amp.view_flag = weight.view_flag and weight.cost_type = 'T'
                AND nvl(amp.dimension_code,'snull') =  nvl( weight.dimension_code,'snull')
                and nvl(amp.dimension_subcategory_code,'snull') =  nvl( weight.dimension_subcategory_code,'snull')
                AND amp.lv0_prod_list_code = weight.lv0_prod_list_code
                AND amp.caliber_flag = weight.caliber_flag
                AND amp.oversea_flag = weight.oversea_flag
                AND amp.group_level = weight.group_level
                <if  test='viewFlag == "1" or viewFlag == "2" or viewFlag == "9"'>
                    and amp.lv1_prod_rnd_team_code = weight.prod_rnd_team_code
                    and amp.dimension_code = weight.parent_code
                    and amp.dimension_subcategory_code = weight.group_code
                </if>
                <if  test='viewFlag == "4" or viewFlag == "5" or viewFlag == "10"'>
                    and amp.lv2_prod_rnd_team_code = weight.prod_rnd_team_code
                    and amp.dimension_code = weight.parent_code
                    and amp.dimension_subcategory_code = weight.group_code
                </if>
                <if  test='viewFlag == "7" or viewFlag == "8" or viewFlag == "11"'>
                    and amp.lv3_prod_rnd_team_code = weight.prod_rnd_team_code
                    and amp.dimension_code = weight.parent_code
                    and amp.dimension_subcategory_code = weight.group_code
                </if>
                <if  test='viewFlag == "12"'>
                <if test='industryOrg == "ENERGY"'>
                    and amp.lv3_prod_rnd_team_code = weight.prod_rnd_team_code
                    and nvl(amp.coa_code,'snull') =  nvl( weight.coa_code,'snull')
                </if>
                    <if test='industryOrg == "IAS"'>
                        and amp.lv4_prod_rnd_team_code = weight.prod_rnd_team_code
                    </if>
                    and amp.dimension_code = weight.parent_code
                    and amp.dimension_subcategory_code = weight.group_code
                </if>
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
                and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>
            <when test='groupLevel == "COA" or groupLevel == "LV4"'>
                from fin_dm_opt_foi.${tablePreFix}_total_dms_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_total_dms_annual_weight_t weight
                on amp.view_flag = weight.view_flag and weight.cost_type = 'T'
                AND nvl(amp.dimension_code,'snull') =  nvl( weight.dimension_code,'snull')
                AND amp.lv0_prod_list_code = weight.lv0_prod_list_code
                AND amp.caliber_flag = weight.caliber_flag
                AND amp.oversea_flag = weight.oversea_flag
                AND amp.group_level = weight.group_level
                <if test='industryOrg == "ENERGY"'>
                    AND nvl(amp.coa_code,'snull') =  nvl( weight.coa_code,'snull')
                    and amp.lv3_prod_rnd_team_code = weight.prod_rnd_team_code
                    and amp.coa_code = weight.parent_code
                </if>
                <if test='industryOrg == "IAS"'>
                    and amp.lv4_prod_rnd_team_code = weight.prod_rnd_team_code
                    and amp.lv4_prod_rnd_team_code = weight.parent_code
                </if>
                and amp.dimension_code = weight.group_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
                and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV3" and viewFlag == "12"'>
                from fin_dm_opt_foi.${tablePreFix}_total_dms_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_total_dms_annual_weight_t weight
                on amp.view_flag = weight.view_flag and weight.cost_type = 'T'
                and amp.lv0_prod_list_code = weight.lv0_prod_list_code
                and amp.caliber_flag = weight.caliber_flag
                and amp.oversea_flag = weight.oversea_flag
                and amp.group_level = weight.group_level
                <if test='industryOrg == "ENERGY"'>
                    and amp.coa_code = weight.group_code
                </if>
                <if test='industryOrg == "IAS"'>
                    and amp.lv4_prod_rnd_team_code = weight.group_code
                </if>
                and amp.lv3_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
                and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV3" and viewFlag != "12"'>
                from fin_dm_opt_foi.${tablePreFix}_total_dms_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_total_dms_annual_weight_t weight
                on amp.view_flag = weight.view_flag and weight.cost_type = 'T'
                and amp.lv0_prod_list_code = weight.lv0_prod_list_code
                and amp.caliber_flag = weight.caliber_flag
                and amp.oversea_flag = weight.oversea_flag
                and amp.group_level = weight.group_level
                and amp.dimension_code = weight.group_code
                and amp.lv3_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
                and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV2" and (viewFlag =="6" or viewFlag =="7" or viewFlag =="8" or viewFlag =="11"  or viewFlag =="12")'>
                from fin_dm_opt_foi.${tablePreFix}_total_dms_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_total_dms_annual_weight_t weight
                on amp.view_flag = weight.view_flag and weight.cost_type = 'T'
                and amp.lv0_prod_list_code = weight.lv0_prod_list_code
                and amp.caliber_flag = weight.caliber_flag
                and amp.oversea_flag = weight.oversea_flag
                and amp.group_level = weight.group_level
                and amp.lv3_prod_rnd_team_code = weight.group_code
                and amp.lv2_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
                and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV2" and (viewFlag =="3" or viewFlag =="4" or viewFlag =="5" or viewFlag =="10")'>
                from fin_dm_opt_foi.${tablePreFix}_total_dms_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_total_dms_annual_weight_t weight
                on amp.view_flag = weight.view_flag and weight.cost_type = 'T'
                and amp.lv0_prod_list_code = weight.lv0_prod_list_code
                and amp.caliber_flag = weight.caliber_flag
                and amp.oversea_flag = weight.oversea_flag
                and amp.group_level = weight.group_level
                and amp.dimension_code = weight.group_code
                and amp.lv2_prod_rnd_team_code =weight.prod_rnd_team_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
                and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV1" and viewFlag !="0" and viewFlag !="1" and viewFlag !="2" and viewFlag !="9"'>
                from fin_dm_opt_foi.${tablePreFix}_total_dms_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_total_dms_annual_weight_t weight
                on amp.view_flag = weight.view_flag and weight.cost_type = 'T'
                and amp.lv0_prod_list_code = weight.lv0_prod_list_code
                and amp.caliber_flag = weight.caliber_flag
                and amp.oversea_flag = weight.oversea_flag
                and amp.group_level = weight.group_level
                and amp.lv2_prod_rnd_team_code = weight.group_code
                and amp.lv1_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
                and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV1" and (viewFlag =="0" or viewFlag =="1" or viewFlag =="2" or viewFlag =="9")'>
                from fin_dm_opt_foi.${tablePreFix}_total_dms_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_total_dms_annual_weight_t weight
                on amp.view_flag = weight.view_flag and weight.cost_type = 'T'
                and amp.lv0_prod_list_code = weight.lv0_prod_list_code
                and amp.caliber_flag = weight.caliber_flag
                and amp.oversea_flag = weight.oversea_flag
                and amp.group_level = weight.group_level
                and amp.dimension_code = weight.group_code
                and amp.lv1_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
                and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV0"'>
                from fin_dm_opt_foi.${tablePreFix}_total_dms_view_info_d amp
            </when>
        </choose>
        WHERE amp.view_flag = #{viewFlag,jdbcType=VARCHAR} and amp.del_flag ='N'  and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        and amp.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        and amp.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        and amp.page_flag =  #{pageFlag,jdbcType=VARCHAR}
        <if test='dimensionCodeList != null and dimensionCodeList.size() > 0'>
            <foreach collection='dimensionCodeList' item="code" open="AND amp.dimension_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionSubcategoryCodeList != null and dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='dimensionSubcategoryCodeList' item="code" open="AND amp.dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionSubDetailCodeList != null and dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='dimensionSubDetailCodeList' item="code" open="AND amp.dimension_sub_detail_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='spartCodeList != null and spartCodeList.size() > 0'>
            <foreach collection='spartCodeList' item="code" open="AND amp.spart_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='coaCodeList != null and coaCodeList.size() > 0'>
            <foreach collection='coaCodeList' item="code" open="AND amp.coa_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <choose>
            <when test='teamLevel == "LV1" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND amp.lv1_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='teamLevel == "LV2" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND amp.lv2_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='teamLevel == "LV3" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND amp.lv3_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='teamLevel == "LV4" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND amp.lv4_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='nextGroupLevel!=null and nextGroupLevel!=""'>
            AND amp.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
        </if>
        <if test='teamLevel == "LV0" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND amp.lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamLevel == "LV1" and viewFlag !="0" and viewFlag !="1" and viewFlag !="2" and viewFlag !="9" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND amp.lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='isMultipleSelect == false and teamLevel != "LV0"'>
            order by weight_rate desc
        </if>
        <if test='isMultipleSelect == true and teamLevel != "LV0"'>
            order by absolute_weight desc
        </if>
    </select>

    <select id="totalViewFlagInfoDmsList" resultMap="resultMap">
        SELECT *
        from (
                 SELECT
                     distinct view_flag, lv0_prod_rnd_team_code, lv0_prod_rd_team_cn_name,'D' as granularityType
                 FROM
                     fin_dm_opt_foi.${tablePreFix}_total_dms_view_info_d
                 where del_flag = 'N' and caliber_flag = #{caliberFlag}
             )
        ORDER BY view_flag
    </select>
</mapper>