/*
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.month;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;

import java.util.HashSet;

/**
 * <AUTHOR>
 * @since 2023/5/9
 */
public class MonthAnalysisVOTest extends BaseVOCoverUtilsTest<MonthAnalysisVO> {
    @Override
    protected Class<MonthAnalysisVO> getTClass() {
        return MonthAnalysisVO.class;
    }

    @Test
    public void testMethod() {
        String s = MonthAnalysisVO.builder()
            .caliberFlag("R")
            .viewFlag("1")
            .basePeriodId(202101)
            .categoryCnName("name1")
            .cnName("name")
            .curPage(1)
            .pageSize(10)
            .fileName("name1")
            .l1NameList(Lists.newArrayList("l1"))
            .l2NameList(Lists.newArrayList("l2"))
            .lv1DimensionSet(new HashSet<>())
            .lv0DimensionSet(new HashSet<>())
            .lv2DimensionSet(new HashSet<>())
            .groupCodeList(Lists.newArrayList("code"))
            .subGroupCodeList(Lists.newArrayList("1213"))
            .groupCodeOrder("1")
            .groupLevel("lv2")
            .parentLevel("lv1")
            .parentCodeList(Lists.newArrayList("code1"))
            .lv1ProdRdTeamCnName(Lists.newArrayList("name1"))
            .lv2ProdRdTeamCnName(Lists.newArrayList("lv2"))
            .lv3ProdRdTeamCnName(Lists.newArrayList("lv3"))
            .lv3CegCnName("l3")
            .profitsName("l1")
            .topCateVersionId(121L)
            .versionId(120L)
            .yoyFlag("N")
            .build()
            .toString();
        Assert.assertNotNull(s);
    }

    
}