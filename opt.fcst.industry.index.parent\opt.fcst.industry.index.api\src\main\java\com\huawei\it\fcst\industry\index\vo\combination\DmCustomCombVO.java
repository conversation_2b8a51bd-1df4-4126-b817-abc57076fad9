/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.combination;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * DmCustomCombVO Class
 *
 * <AUTHOR>
 * @since 2023/8/1
 */
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
public class DmCustomCombVO extends DmFocViewInfoVO implements Serializable {

    private static final long serialVersionUID = -4328673596196374783L;

    // 如果同步则传ALL，如果不同步分两种情况，从年度页面创建的则是:ANNUAL,从月度页面创建传：MONTH
    private String pageFlag;

    private Long customId;

    private String customCnName;

    private String lv0ProdRndTeamCode;

    private String lv0ProdRdTeamCnName;

    private String lv1ProdRndTeamCode;

    private String lv1ProdRdTeamCnName;

    private String lv2ProdRndTeamCode;

    private String lv2ProdRdTeamCnName;

    private String lv3ProdRndTeamCode;

    private String lv3ProdRdTeamCnName;

    private String lv4ProdRndTeamCode;

    private String lv4ProdRdTeamCnName;

    private String l1Name;

    private String l2Name;

    private String coaCode;

    private String coaCnName;

    private Integer count;

    private String dimensionCode;

    private String dimensionCnName;

    private String dimensionSubCategoryCode;

    private String dimensionSubCategoryCnName;

    private String dimensionSubDetailCode;

    private String dimensionSubDetailCnName;

    // DIMENSION (量纲)/ SUBCATEGORY (量纲子类)/ SUB_DETAIL (量纲子类明细)
    private String groupLevel;

    private String groupCnName;

    private String groupCode;

    // 某个code是否失效,Y有效，N无效
    private String subEnableFlag;

    private String userId;

    private String roleId;

    // 组合整体失效
    private String enableFlag;

    // 业务口径（R:收入时点/C：发货成本）
    private String caliberFlag;

    private String viewFlag;

    // 维度类型（U：通用/P：盈利颗粒度/D：量纲颗粒度）
    private String granularityType;

    // 国内海外标识(I:国内/O:海外/G:全球)
    private String overseaFlag;

    // bg code
    private String lv0ProdListCode;

    // bg名称
    private String lv0ProdListCnName;

    private String parentCode;

    private String parentCnName;

    private String l3CegShortCnName;

    private String l3CegCnName;

    private String l3CegCode;

    private String l4CegShortCnName;

    private String l4CegCnName;

    private String l4CegCode;

    private String categoryCode;

    private String categoryCnName;

    // 发货对象code
    private String shippingObjectCode;

    // 发货对象中文名称
    private String shippingObjectCnName;

    // 制造对象code
    private String manufactureObjectCode;

    // 制造对象中文名称
    private String manufactureObjectCnName;

    private String createdBy;

    private String permissionFlag;

    private String removeFlag;

    private String connectCode;

    private String connectParentCode;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS",timezone = "GMT+8")
    private Timestamp creationDate;

    private String lastUpdatedBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS",timezone = "GMT+8")
    private Timestamp lastUpdateDate;

    private Long id;

    private String prodRndTeamCode;

    private String itemCode;

    private String itemCnName;

    private String delFlag;

    private String viewFlagValue;

    private Long versionId;

    private Double weightRate;

    /**
     * 是否组合
     */
    private Boolean isCombination;

    private List<DmFocViewInfoVO> children;

    private String topItemCode;

    // 是否解除过同步
    private String isSeparate;

    private int itemNum;

    private String spartCode;

    private String spartCnName;

    // Y代表折叠，N代表未折叠
    private String foldFlag;

}
