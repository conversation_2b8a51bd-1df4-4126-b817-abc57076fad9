/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.relation;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

/**
 * DmDimMaterialCodeVOTest Class
 *
 * <AUTHOR>
 * @since 2023/4/23
 */
public class DmDimMaterialCodeVOTest extends BaseVOCoverUtilsTest<DmDimMaterialCodeVO> {

    @Override
    protected Class<DmDimMaterialCodeVO> getTClass() {
        return DmDimMaterialCodeVO.class;
    }

    @Test
    public void testMethod() {
        DmDimMaterialCodeVO dimensionParamVO = new DmDimMaterialCodeVO();
        dimensionParamVO.builder().itemSubtypeCnName("adjust").build();
        Assert.assertNotNull(dimensionParamVO);
    }
}