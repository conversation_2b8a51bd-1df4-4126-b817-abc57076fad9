/*
 *
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.industry.index.impl.annual;

import com.huawei.it.fcst.constant.Constants;
import com.huawei.it.fcst.industry.index.utils.FileProcessUtis;
import com.huawei.it.fcst.industry.index.utils.UserInfoUtils;
import com.huawei.it.fcst.industry.index.vo.common.DmFoiImpExpRecordVO;
import com.huawei.it.fcst.industry.index.vo.common.UploadInfoVO;
import com.huawei.it.fcst.util.FileNameUtil;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import com.huawei.it.jalor5.core.util.PathUtil;
import com.huawei.it.jalor5.core.util.StreamUtil;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

import javax.inject.Inject;
import javax.inject.Named;

import static java.io.File.separator;

/**
 * StatisticsExcelService Class
 *
 * <AUTHOR>
 * @since 2023/3/14
 */
@Slf4j
@Named("statisticsExcelService")
@JalorResource(code = "statisticsExcelService", desc = "statisticsExcelService")
public class StatisticsExcelService {
    @Inject
    private PersonalCenterService personalCenterService;

    public static DmFoiImpExpRecordVO uploadExportExcel(Workbook workbook, int number, String fileName, Long userId) throws IOException, CommonApplicationException {
        String name = PathUtil.getTempPathByDay(Constants.DEF.getValue()) + separator + FileNameUtil.dealFileName(fileName) + getTempName() + ".xlsx";
        File file = new File(name);
        if (!file.createNewFile()) {
            log.error(">>> create new file failed. ");
        }
        FileOutputStream out = null;
        try {
            out = new FileOutputStream(file);
            workbook.write(out);
            out.flush();
            // 上传
            String userIdStr;
            if (userId!=null) {
                userIdStr = String.valueOf(userId);
            } else {
                userIdStr = String.valueOf(UserInfoUtils.getUserId());
            }
            String fileSourceKey = FileProcessUtis.uploadToS3(file, userIdStr);

            DmFoiImpExpRecordVO dmFoiImpExpRecordVO = new DmFoiImpExpRecordVO();
            dmFoiImpExpRecordVO.setFileSourceKey(fileSourceKey);
            long fileSize = file.length() / 1024;
            dmFoiImpExpRecordVO.setFileSize(String.valueOf(fileSize));
            dmFoiImpExpRecordVO.setRecordNum(number);
            dmFoiImpExpRecordVO.setFileName(fileName);
            return dmFoiImpExpRecordVO;
        } catch (Exception e) {
            log.error("file create fail");
        } finally {
            StreamUtil.closeStreams(out);
            if (file != null) {
                if (!file.delete()) {
                    log.error(">>> file delete failed.");
                }
            }
        }
        return null;
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void insertExportExcelRecord(DmFoiImpExpRecordVO dmFoiImpExpRecordVO) {
        String moduleType = dmFoiImpExpRecordVO.getModuleType();
        dmFoiImpExpRecordVO.setPageModule(moduleType);
        dmFoiImpExpRecordVO.setRecSts("OK");
        personalCenterService.statisticsExportRecord(dmFoiImpExpRecordVO);
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void insertMonthExportRecord(DmFoiImpExpRecordVO dmFoiImpExpRecord) {
        String moduleType = dmFoiImpExpRecord.getModuleType();
        dmFoiImpExpRecord.setPageModule(moduleType);
        dmFoiImpExpRecord.setRecSts("OK");
        personalCenterService.statisticsMonthExportRecord(dmFoiImpExpRecord);
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void insertImportExcel(DmFoiImpExpRecordVO dmFoiImpExpRecordVO, UploadInfoVO uploadInfoVO, boolean flag, Integer recordNum) throws CommonApplicationException {
        String fileName = uploadInfoVO.getFileName();
        long fileSize = uploadInfoVO.getFileSize();
        Map<String, Object> params = uploadInfoVO.getParams();
        setImportExcelParams(fileName, dmFoiImpExpRecordVO, fileSize, params, recordNum);
        String userId = String.valueOf(uploadInfoVO.getUserId());
        if (flag) {
            dmFoiImpExpRecordVO.setRecSts("OK");
        } else {
            dmFoiImpExpRecordVO.setRecSts("FAIL");
        }
        personalCenterService.statisticsImportRecord(dmFoiImpExpRecordVO, userId);
    }

    private static void setImportExcelParams(String fileName, DmFoiImpExpRecordVO dmFoiImpExpRecordVO, long size, Map<String, Object> params, Integer recordNum) {
        dmFoiImpExpRecordVO.setFileName(fileName);
        dmFoiImpExpRecordVO.setFileSize(String.valueOf(size));
        dmFoiImpExpRecordVO.setRecordNum(recordNum);
        if (StringUtils.isEmpty(dmFoiImpExpRecordVO.getModuleType())) {
            Object module = params.get("module");
            dmFoiImpExpRecordVO.setPageModule(String.valueOf(module));
        } else {
            dmFoiImpExpRecordVO.setPageModule(dmFoiImpExpRecordVO.getModuleType());
        }
    }

    public void getImportUploadFileKey(DmFoiImpExpRecordVO dmFoiImpExpRecordVO, Long userId, int type, InputStream inputStream) throws CommonApplicationException, IOException {
        File file = transformFile(inputStream);
        String fileSourceKey = FileProcessUtis.uploadToS3(file, String.valueOf(userId));
        switch (type) {
            // 正常和异常相同文件
            case 1:
                dmFoiImpExpRecordVO.setFileSourceKey(fileSourceKey);
                dmFoiImpExpRecordVO.setFileErrorKey(fileSourceKey);
                break;
            // 只有正常文件
            case 2:
                dmFoiImpExpRecordVO.setFileSourceKey(fileSourceKey);
                break;
            default:
                dmFoiImpExpRecordVO.setFileSourceKey(fileSourceKey);
        }
        if (file != null) {
            if (!file.delete()) {
                log.error(">>> file delete failed.");
            }
        }
    }

    public File transformFile(InputStream inputStream) throws IOException {
        String name = PathUtil.getTempPathByDay(Constants.DEF.getValue()) + separator + getTempName() + ".xlsx";
        File tempFile = new File(name);
        FileUtils.copyInputStreamToFile(inputStream, tempFile);
        return tempFile;
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void insertFailExportExcelRecord(DmFoiImpExpRecordVO dmFoiImpExpRecordVO, int fileSize,
                                            int number, String moduleType) {
        dmFoiImpExpRecordVO.setPageModule(moduleType);
        dmFoiImpExpRecordVO.setFileSize(String.valueOf(fileSize));
        dmFoiImpExpRecordVO.setRecordNum(number);
        dmFoiImpExpRecordVO.setRecSts("FAIL");
        personalCenterService.statisticsExportRecord(dmFoiImpExpRecordVO);
    }

    private static String getTempName() {
        return DateFormatUtils.format(new Date(), "yyyyMMddHHmmss");
    }
}
