/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.service.config;

import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.config.HistoryInputVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import io.swagger.annotations.Api;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * IConfigManufactureService Class
 *
 * <AUTHOR>
 * @since 2023/9/28
 */
@Path("/configManufacture")
@Api(value = "制造对象清单服务")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface IConfigManufactureService {
    /**
     * [制造对象清单查询接口]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @POST
    @Path("/manufactureList")
    ResultDataVO getManufactureList(HistoryInputVO historyInputVO) throws CommonApplicationException;

    /**
     * [历史规格品对象清单查询接口]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @POST
    @Path("/itemList")
    ResultDataVO getItemList(HistoryInputVO historyInputVO) throws CommonApplicationException;

    /**
     * [制造成本清单导出接口]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @POST
    @Path("/manufactureExport")
    ResultDataVO exportManufacture(HistoryInputVO historyInputVO) throws CommonApplicationException;

}
