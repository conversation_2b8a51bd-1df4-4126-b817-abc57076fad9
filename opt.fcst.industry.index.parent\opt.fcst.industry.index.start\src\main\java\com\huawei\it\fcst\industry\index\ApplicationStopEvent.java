/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2019. All rights reserved.
 */

package com.huawei.it.fcst.industry.index;

import com.huawei.it.fcst.industry.index.impl.mqs.MessageConsumer;
import com.huawei.it.fcst.industry.index.impl.mqs.MessageProducer;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.ioc.Jalor;
import com.huawei.it.jalor5.core.log.ILogger;
import com.huawei.it.jalor5.core.log.JalorLoggerFactory;
import com.huawei.it.jalor5.core.server.IBeforeShutdownHandler;
import com.huawei.it.jalor5.core.server.ShutdownEventArgs;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 20230703
 */

@Component
@Slf4j
public class ApplicationStopEvent implements IBeforeShutdownHandler {

    private MessageConsumer consumer;
    private MessageProducer producer;

    static ILogger logger = JalorLoggerFactory.getLogger(ApplicationStopEvent.class);

    @Override
    public int getOrder() {
        return 10;
    }

    @Override
    public void execute(Object sender, ShutdownEventArgs args) throws ApplicationException {
        try {
            if (producer == null) {
                producer = Jalor.getContext().getBean("messageProducer", MessageProducer.class);
            }
            producer.stopProducer();
        } catch (Exception exception) {
            logger.error("stop ump producer error {}",exception.getMessage());
        }
        try {
            if (consumer == null) {
                consumer = Jalor.getContext().getBean("messageConsumer", MessageConsumer.class);
            }
            consumer.stopConsumer();
        } catch (Exception exception) {
            logger.error("stop ump producer error : {} ",exception.getMessage());
        }
    }
}
