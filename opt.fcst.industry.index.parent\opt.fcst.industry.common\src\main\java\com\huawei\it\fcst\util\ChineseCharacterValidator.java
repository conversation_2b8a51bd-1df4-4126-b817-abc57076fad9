/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */
package com.huawei.it.fcst.util;

/**
 * <AUTHOR>
 * @since 2025/7/7
 */
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ChineseCharacterValidator {

    private static final  Pattern pattern = Pattern.compile("[\\u4e00-\\u9fff]");

    public static boolean validateInput(String input) {
        // 1. 检查中文字符数量是否至少为30个
        int chineseCount = countChineseCharacters(input);
        return chineseCount >= 30;
    }

    private static int countChineseCharacters(String input) {
        // 匹配所有中文字符
        Matcher matcher = pattern.matcher(input);
        int count = 0;
        while (matcher.find()) {
            count++;
        }
        return count;
    }
}

