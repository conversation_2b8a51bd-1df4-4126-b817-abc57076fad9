<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
  http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!-- HW MQS uat 框架问题没有解决暂时注释-->
<!--	<beans profile="uat,pro,production">
		<bean id="bigTaskConduit" class="com.huawei.it.jalor5.async.conduit.MQSConduit">
			&lt;!&ndash; 集群地址 &ndash;&gt;
			<property name="hosts" value="${industry.mqs.topic.url}"/>
			&lt;!&ndash; 主题 &ndash;&gt;
			<property name="topic" value="${industry.mqs.bigtaskconduit.topic}"/>
			&lt;!&ndash; 是否加密传输 &ndash;&gt;
			<property name="encryptTransport" value="${industry.mqs.encryptTransport:false}"/>
		</bean>
		<bean id="bigTaskMessageListener" class="com.huawei.it.jalor5.async.MQSMessageListener">
			&lt;!&ndash; 集群地址 &ndash;&gt;
			<property name="hosts" value="${industry.mqs.topic.url}"/>
			&lt;!&ndash; 主题 &ndash;&gt;
			<property name="topic" value="${industry.mqs.bigtaskconduit.topic}"/>
			&lt;!&ndash; 是否加密传输 &ndash;&gt;
			<property name="encryptTransport" value="${industry.mqs.encryptTransport:false}"/>
			&lt;!&ndash; 订阅消息标签，默认*表示消费所有类型的消息 &ndash;&gt;
			<property name="tags" value="*"/>
		</bean>
	</beans>-->

</beans>

