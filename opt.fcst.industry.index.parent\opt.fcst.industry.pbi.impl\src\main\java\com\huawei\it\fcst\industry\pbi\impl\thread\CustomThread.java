/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.thread;

import com.huawei.it.fcst.industry.pbi.enums.GroupLevelEnum;
import com.huawei.it.fcst.industry.pbi.impl.comb.IctCustomCommonService;
import com.huawei.it.fcst.industry.pbi.utils.FcstIndustryUtil;
import com.huawei.it.fcst.industry.pbi.vo.comb.CommonViewVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstDimInfoVO;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.CountDownLatch;

public class CustomThread implements Runnable {

    private static final Logger LOGGER = LoggerFactory.getLogger(CustomThread.class);

    private CommonViewVO commonView;

    private List<DmFcstDimInfoVO> allGroupCodeList;

    private IctCustomCommonService ictCustomCommonService;

    private CountDownLatch countDownLatch;

    private IRequestContext requestContext;

    /**
     * 构造方法
     *
     * @param commonView       参数
     * @param allGroupCodeList 结果
     */
    public CustomThread(CountDownLatch countDownLatch, CommonViewVO commonView, List<DmFcstDimInfoVO> allGroupCodeList, IRequestContext requestContext, IctCustomCommonService ictCustomCommonService) {
        this.commonView = commonView;
        this.allGroupCodeList = allGroupCodeList;
        this.ictCustomCommonService = ictCustomCommonService;
        this.countDownLatch = countDownLatch;
        this.requestContext = requestContext;
    }

    @Override
    public void run() {
        synchronized (CustomThread.class) {
            try {
                RequestContextManager.setCurrent(requestContext);
                // 如果不是品类层级或者发货对象层级，需要一直找到品类层级或发货对象层级为止
                while (!GroupLevelEnum.LV4.getValue().equals(commonView.getGroupLevel())) {
                    String groupLevel = FcstIndustryUtil.getViewNextGroupLevel(commonView);
                    commonView.setGroupLevel(groupLevel);
                    ictCustomCommonService.getDbListForAllPage(commonView, allGroupCodeList);
                }
            } catch (Exception exception) {
                countDownLatch.countDown();
                LOGGER.error("error getIctFoldGroupCodeList :{} ", exception.getLocalizedMessage());
            } finally {
                countDownLatch.countDown();
                RequestContextManager.removeCurrent();
            }
        }
    }
}