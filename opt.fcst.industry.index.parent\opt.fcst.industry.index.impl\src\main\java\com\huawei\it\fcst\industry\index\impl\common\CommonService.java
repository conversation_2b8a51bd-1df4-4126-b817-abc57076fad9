/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.common;

import cn.hutool.core.util.StrUtil;
import com.huawei.it.fcst.industry.index.cache.IndustryCacheHandler;
import com.huawei.it.fcst.industry.index.constant.Constant;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocPageInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTopItemInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocVersionDao;
import com.huawei.it.fcst.industry.index.dao.IProdGroupsViewDao;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumD;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumMadeD;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumMadeP;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumMadeU;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumP;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumU;
import com.huawei.it.fcst.industry.index.enums.IndustryConst;
import com.huawei.it.fcst.industry.index.enums.IndustryIndexEnum;
import com.huawei.it.fcst.industry.index.enums.ModuleEnum;
import com.huawei.it.fcst.industry.index.enums.ResultCodeEnum;
import com.huawei.it.fcst.industry.index.impl.iauth.DataDimensionService;
import com.huawei.it.fcst.industry.index.service.common.ICommonService;
import com.huawei.it.fcst.industry.index.utils.UserInfoUtils;
import com.huawei.it.fcst.industry.index.vo.common.CommonViewVO;
import com.huawei.it.fcst.industry.index.vo.common.DataPermissionsVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.common.TableNameVO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoDTO;
import com.huawei.it.fcst.industry.index.vo.month.DmFocPageInfoVO;
import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;
import com.huawei.it.fcst.industry.index.vo.replace.ReplaceAnalysisVO;
import com.huawei.it.fcst.industry.index.vo.view.ViewInfoVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.KeyValuePairVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.lookup.LookupItemVO;
import com.huawei.it.jalor5.lookup.service.ILookupItemQueryService;
import com.huawei.it.jalor5.registry.service.IRegistryQueryService;
import com.huawei.it.jalor5.security.ProgramItemVO;
import com.huawei.it.jalor5.security.ProgramVO;
import com.huawei.it.jalor5.security.RoleVO;
import com.huawei.it.jalor5.security.UserVO;

import com.alibaba.fastjson.JSONObject;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.inject.Inject;
import javax.inject.Named;

/**
 * CommonService Class
 *
 * <AUTHOR>
 * @since 2023-3-12
 */
@Slf4j
@Named("commonService")
@JalorResource(code = "CommonService", desc = "公共服务")
public class CommonService implements ICommonService {
    private static final String CONCAT_FLAG = "-";

    @Inject
    private IDmFocPageInfoDao dmFocPageInfoDao;

    @Inject
    private IDmFocTopItemInfoDao dmFocTopItemInfoDao;

    @Inject
    private IDmFocVersionDao dmFocVersionDao;

    @Inject
    private IDmFocMonthCostIdxDao iDmFocMonthCostIdxDao;

    @Autowired
    private DataDimensionService dataDimensionService;

    @Autowired
    private ILookupItemQueryService lookupItemQueryService;

    @Autowired
    private IRegistryQueryService registryQueryService;

    @Autowired
    private IDmFocMadeMonthCostIdxDao dmFocMadeMonthCostIdxDao;

    @Autowired
    private IProdGroupsViewDao prodGroupsViewDao;

    @Autowired
    private IndustryCacheHandler industryCacheHandler;

    // 当前数据刷新月
    private String refreshMonth;

    @Override
    @JalorOperation(code = "getNavigationBarList", desc = "查询导航栏列表信息")
    public ResultDataVO getNavigationList(DmFocPageInfoVO dmFocPageInfoVO) {
        if (StringUtils.isEmpty(dmFocPageInfoVO.getUserId())) {
            dmFocPageInfoVO.setUserId(UserInfoUtils.getUserAccount());
        }
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        dmFocPageInfoVO.setRoleId(String.valueOf(currentRole.getRoleId()));
        return ResultDataVO.success(dmFocPageInfoDao.findPageInfoVOList(dmFocPageInfoVO));
    }

    @Override
    @JalorOperation(code = "findRefreshTime", desc = "查询TOP品类/规格品清单刷新时间")
    public ResultDataVO findRefreshTime(String industryOrg) {
        log.info("Begin CommonService::getRefreshTime");
        String tablePreFix = TableNameVO.getTablePreFix(industryOrg);
        return ResultDataVO.success(dmFocTopItemInfoDao.findRefreshTime(tablePreFix));
    }

    /**
     * 校验是否有操作页面的权限【包括更新、删除操作】
     *
     * @param tablePreFix String
     * @param pageId      page Id
     * @return true or false
     */
    private boolean verifyModifyPagePermission(String tablePreFix, Long pageId) {
        // 查询页面信息
        DmFocPageInfoVO pageInfoVO = dmFocPageInfoDao.findDmFocPageInfoVOById(tablePreFix, pageId);
        return UserInfoUtils.getUserAccount().equals(pageInfoVO.getUserId());
    }

    @Audit(module = "commonService-saveOrUpdatePageInfo", operation = "saveOrUpdatePageInfo", message = "保存或修改页面信息")
    @Override
    @Transactional(rollbackFor = Exception.class)
    @JalorOperation(code = "saveOrUpdatePageInfo", desc = "保存或修改页面信息")
    public ResultDataVO saveOrUpdatePageInfo(DmFocPageInfoVO dmFocPageInfoVO) {
        log.info("Begin CommonService::savePageInfo");
        // 更新页面信息
        Long userId = UserInfoUtils.getUserId();
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        dmFocPageInfoVO.setRoleId(String.valueOf(currentRole.getRoleId()));
        ResultDataVO PARAM_ERROR = validatePageNameLength(dmFocPageInfoVO);
        if (PARAM_ERROR != null) {
            return PARAM_ERROR;
        }
        if (!ObjectUtils.isEmpty(dmFocPageInfoVO.getPageId())) {
            // 校验是否有操作页面的权限
            if (!(verifyModifyPagePermission(dmFocPageInfoVO.getTablePreFix(), dmFocPageInfoVO.getPageId()))) {
                return ResultDataVO.failure(ResultCodeEnum.UNAUTHORIZED_OPERATION);
            }
            dmFocPageInfoVO.setLastUpdatedBy(userId);
            dmFocPageInfoDao.updateDmFocPageInfoVO(dmFocPageInfoVO);
            return ResultDataVO.success(dmFocPageInfoVO);
        }
        // 新增页面信息
        dmFocPageInfoVO.setUserId(UserInfoUtils.getUserAccount());
        dmFocPageInfoVO.setCreatedBy(userId);
        dmFocPageInfoVO.setLastUpdatedBy(userId);
        // 保存版本ID 方便后续查询品类名称
        if (!IndustryConst.INDUSTRY_ORG.ICT_NEW.getValue().equals(dmFocPageInfoVO.getIndustryOrg())) {
            dmFocPageInfoVO.setVersionId(getVersionId(IndustryIndexEnum.DataType.CATE.getValue(),dmFocPageInfoVO.getTablePreFix()));
        }
        // 查询当前用户是否有另存为过的记录，有则将defaultFlag设置为N，否则设置为Y
        DmFocPageInfoVO pageInfoVO = new DmFocPageInfoVO();
        pageInfoVO.setUserId(UserInfoUtils.getUserAccount());
        pageInfoVO.setRoleId(String.valueOf(currentRole.getRoleId()));
        pageInfoVO.setCaliberFlag(dmFocPageInfoVO.getCaliberFlag());
        pageInfoVO.setIndustryOrg(dmFocPageInfoVO.getIndustryOrg());
        List<DmFocPageInfoVO> pageInfoVOList = dmFocPageInfoDao.findPageInfoVOList(pageInfoVO);
        pageInfoVOList = Optional.ofNullable(pageInfoVOList).orElse(new ArrayList<>()).stream().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pageInfoVOList)) {
            dmFocPageInfoVO.setDefaultFlag(Constant.StrEnum.STR_Y.getValue());
        } else {
            dmFocPageInfoVO.setDefaultFlag(Constant.StrEnum.STR_N.getValue());
        }
        dmFocPageInfoDao.createDmFocPageInfoVO(dmFocPageInfoVO);
        return ResultDataVO.success(dmFocPageInfoVO);
    }

    @Nullable
    private ResultDataVO validatePageNameLength(DmFocPageInfoVO dmFocPageInfoVO) {
        if (StringUtils.isNotEmpty(dmFocPageInfoVO.getPageName())) {
            try {
                int length = dmFocPageInfoVO.getPageName().getBytes("UTF-8").length;
                if (length > 50) {
                    return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
                }
            } catch (UnsupportedEncodingException exception) {
                log.info("dmFocPageInfoVO.getPageName().getBytes: {}", exception);
            }
        }
        return null;
    }

    @Audit(module = "commonService-setDefaultFlag", operation = "setDefaultFlag", message = "更新页面信息默认标识")
    @Override
    @Transactional(rollbackFor = Exception.class)
    @JalorOperation(code = "setDefaultFlag", desc = "更新页面信息默认标识")
    public ResultDataVO setDefaultFlag(DmFocPageInfoVO dmFocPageInfoVO) {
        log.info(">>>Begin CommonService::updateDefaultFlag");
        // 找出原来的默认标识记录并将其设置为非默认N
        DmFocPageInfoVO defaultFlagY = getDefaultFlagY(dmFocPageInfoVO.getCaliberFlag(), dmFocPageInfoVO.getIndustryOrg());
        if (defaultFlagY != null) {
            // 校验是否有操作页面的权限
            if (!(verifyModifyPagePermission(dmFocPageInfoVO.getTablePreFix(), defaultFlagY.getPageId()))) {
                return ResultDataVO.failure(ResultCodeEnum.UNAUTHORIZED_OPERATION);
            }
            defaultFlagY.setDefaultFlag(Constant.StrEnum.STR_N.getValue());
            defaultFlagY.setIndustryOrg(dmFocPageInfoVO.getIndustryOrg());
            dmFocPageInfoDao.updateDmFocPageInfoVO(defaultFlagY);
        }
        dmFocPageInfoVO.setLastUpdatedBy(UserInfoUtils.getUserId());
        // 校验是否有操作页面的权限
        if (!(verifyModifyPagePermission(dmFocPageInfoVO.getTablePreFix(), dmFocPageInfoVO.getPageId()))) {
            return ResultDataVO.failure(ResultCodeEnum.UNAUTHORIZED_OPERATION);
        }
        dmFocPageInfoDao.updateDmFocPageInfoVO(dmFocPageInfoVO);
        return ResultDataVO.success();
    }

    /**
     * 查询默认页面信息
     *
     * @return DmFoiPageInfoVO
     */
    private DmFocPageInfoVO getDefaultFlagY(String caliberFlag, String industryOrg) {
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        DmFocPageInfoVO pageInfoVO = new DmFocPageInfoVO();
        pageInfoVO.setUserId(UserInfoUtils.getUserAccount());
        pageInfoVO.setRoleId(String.valueOf(currentRole.getRoleId()));
        pageInfoVO.setCaliberFlag(caliberFlag);
        pageInfoVO.setDefaultFlag(Constant.StrEnum.STR_Y.getValue());
        pageInfoVO.setIndustryOrg(industryOrg);
        List<DmFocPageInfoVO> pageInfoVOList = dmFocPageInfoDao.findPageInfoVOList(pageInfoVO);
        if (CollectionUtils.isNotEmpty(pageInfoVOList)) {
            return pageInfoVOList.get(0);
        }
        return null;
    }

    @Override
    @JalorOperation(code = "findInitCondition", desc = "查询页面信息初始化条件")
    public ResultDataVO findInitCondition(Long pageId, String industryOrg) {
        log.info("Begin CommonService::getInitCondition");
        if (ObjectUtils.isEmpty(pageId)) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR.getCode(),
                    ResultCodeEnum.PARAM_ERROR.getMessage().concat("pageId必填！"));
        }
        String tablePreFix = TableNameVO.getTablePreFix(industryOrg);
        return ResultDataVO.success(dmFocPageInfoDao.findDmFocPageInfoVOById(tablePreFix, pageId));
    }

    @Audit(module = "commonService-deletePageInfo", operation = "deletePageInfo", message = "删除页面信息")
    @Override
    @Transactional(rollbackFor = Exception.class)
    @JalorOperation(code = "deletePageInfo", desc = "删除页面信息")
    public ResultDataVO deletePageInfo(Long pageId, String defaultFlag, String caliberFlag, String industryOrg) {
        log.info("Begin CommonService::getDeletePageInfo");
        if (ObjectUtils.isEmpty(pageId)) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR.getCode(),
                    ResultCodeEnum.PARAM_ERROR.getMessage().concat("pageId必填！"));
        }
        String tablePreFix = TableNameVO.getTablePreFix(industryOrg);
        // 校验是否有操作页面的权限
        if (!(verifyModifyPagePermission(tablePreFix, pageId))) {
            return ResultDataVO.failure(ResultCodeEnum.UNAUTHORIZED_OPERATION);
        }
        DmFocPageInfoVO pageInfoVO = new DmFocPageInfoVO();
        pageInfoVO.setIndustryOrg(industryOrg);
        pageInfoVO.setPageId(pageId);
        pageInfoVO.setDelFlag(Constant.StrEnum.STR_Y.getValue());
        pageInfoVO.setLastUpdatedBy(UserInfoUtils.getUserId());
        dmFocPageInfoDao.updateDmFocPageInfoVO(pageInfoVO);
        // 如果删除的是默认标识的记录，需要重新找出创建时间最早记录，并将其设置为默认标识Y
        updateDefaultFlag(defaultFlag, caliberFlag, industryOrg);
        // 查询出默认页面信息 返回到前端
        return ResultDataVO.success(getDefaultFlagY(caliberFlag, industryOrg));
    }

    private void updateDefaultFlag(String defaultFlag, String caliberFlag,String industryOrg) {
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        if (Constant.StrEnum.STR_Y.getValue().equals(defaultFlag)) {
            DmFocPageInfoVO paramVO = DmFocPageInfoVO.builder()
                    .userId(UserInfoUtils.getUserAccount())
                    .roleId(String.valueOf(currentRole.getRoleId()))
                    .caliberFlag(caliberFlag).build();
            paramVO.setIndustryOrg(industryOrg);
            List<DmFocPageInfoVO> pageInfoVOList = dmFocPageInfoDao.findPageInfoVOList(paramVO);
            if (CollectionUtils.isNotEmpty(pageInfoVOList)) {
                DmFocPageInfoVO dmFoiPageInfoVO = pageInfoVOList.get(0);
                dmFoiPageInfoVO.setDefaultFlag(Constant.StrEnum.STR_Y.getValue());
                dmFoiPageInfoVO.setLastUpdatedBy(UserInfoUtils.getUserId());
                dmFoiPageInfoVO.setIndustryOrg(industryOrg);
                dmFocPageInfoDao.updateDmFocPageInfoVO(dmFoiPageInfoVO);
            }
        }
    }

    @Override
    @JalorOperation(code = "findActualMonthNum", desc = "查询本期实际数截至月")
    public Long findActualMonthNum(String industryOrg) {
        log.info(">>>Begin CommonService::getActualMonthNum");
        return iDmFocMonthCostIdxDao.findActualMonthNum(TableNameVO.getTablePreFix(industryOrg));
    }

    @Override
    @JalorOperation(code = "findActualPeriodId", desc = "查询切换基期的最大最小实际月份")
    public ResultDataVO findActualPeriodId(MonthAnalysisVO monthAnalysisVO) {
        Map<String, Long> startEndTime = dmFocMadeMonthCostIdxDao
                .findStartEndTime(monthAnalysisVO.getGranularityType(), monthAnalysisVO.getTablePreFix());
        return ResultDataVO.success(startEndTime);
    }

    @Override
    public Long getVersionId(String dataType,String tablePreFix) {
        log.info("Begin CommonService::getVersionId and dataType={}", dataType);
        DmFocVersionInfoDTO dmFocVersionVO = dmFocVersionDao.findVersionIdByDataType(dataType,tablePreFix);
        dmFocVersionVO = Optional.ofNullable(dmFocVersionVO).orElse(new DmFocVersionInfoDTO());
        return dmFocVersionVO.getVersionId();
    }

    @Override
    @JalorOperation(code = "getDataDictionaryByPath", desc = "根据路径查询数据字典")
    public ResultDataVO getDataDictionaryByPath(String path) throws ApplicationException {
        log.info(">>>Begin CommonService::getDataDictionaryByPath and path:{}", path);
        return ResultDataVO.success(registryQueryService.findValueByPath(path, true));
    }

    @Override
    public String getReplaceGroupCnName(MonthAnalysisVO monthAnalysisVO) {
        String displayName = "ICT产业";
        // 名称拼接
        String lv1CnName = getConnectName(monthAnalysisVO.getLv1ProdRdTeamCnName(), displayName, "LV1");
        String lv2CnName = getConnectName(monthAnalysisVO.getLv2ProdRdTeamCnName(), lv1CnName, "LV2");
        String lv3CnName = getConnectName(monthAnalysisVO.getLv3ProdRdTeamCnName(), lv2CnName, "LV3");
        return getConnectName(monthAnalysisVO.getGroupCnNameList(), lv3CnName, "BIND");
    }

    private String getConnectName(List<String> prodRdTeamCnNameList, String displayName, String groupLevel) {
        if (CollectionUtils.isNotEmpty(prodRdTeamCnNameList)) {
            if (prodRdTeamCnNameList.size() == 1) {
                displayName = displayName + "-" + prodRdTeamCnNameList.get(0);
            } else {
                String prodRdTeamCnNameStr = prodRdTeamCnNameList.stream().collect(Collectors.joining(","));
                displayName = displayName + "-多" + groupLevel + "(" + prodRdTeamCnNameStr + ")";
            }
        }
        return displayName;
    }

    @Override
    public String getGroupCnName(MonthAnalysisVO monthAnalysisVO) {
        String groupCnName = "";
        if (IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(monthAnalysisVO.getGranularityType())) {
            groupCnName = getUniversalCnName(monthAnalysisVO, groupCnName);
        } else if (IndustryIndexEnum.GRANULARITY_TYPE.PROFITS.getValue().equals(monthAnalysisVO.getGranularityType())) {
            groupCnName = getPftCnName(monthAnalysisVO, groupCnName);
        } else {
            groupCnName = getDimensionCnName(monthAnalysisVO, groupCnName);
        }
        return groupCnName;
    }

    @Override
    public String getManufactureGroupCnName(MonthAnalysisVO monthAnalysisVO) {
        String groupCnName = "";
        if (IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(monthAnalysisVO.getGranularityType())) {
            groupCnName = getManufactureUniversalCnName(monthAnalysisVO, groupCnName);
        } else if (IndustryIndexEnum.GRANULARITY_TYPE.PROFITS.getValue().equals(monthAnalysisVO.getGranularityType())) {
            groupCnName = getMadePftCnName(monthAnalysisVO, groupCnName);
        } else {
            groupCnName = getMadeDimensionCnName(monthAnalysisVO, groupCnName);
        }
        return groupCnName;
    }


    private String getDimensionCnName(MonthAnalysisVO monthAnalysisVO, String groupCnName) {
        GroupLevelEnumD levelEnumsD = GroupLevelEnumD.getInstance(monthAnalysisVO.getGroupLevel());
        switch (levelEnumsD) {
            case LV0:
                groupCnName = IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc();
                break;
            case LV1:
                groupCnName = lv1LevelPath(monthAnalysisVO, IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc());
                break;
            case LV2:
                groupCnName = lv2LevelPath(monthAnalysisVO, IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc());
                break;
            case LV3:
                groupCnName = lv3LevelPath(monthAnalysisVO, groupCnName);
                break;
            case LV4:
                groupCnName = lv4LevelPath(monthAnalysisVO, groupCnName);
                break;
            case COA:
                groupCnName = getCoaLevelPath(monthAnalysisVO, groupCnName);
                break;
            case DIMENSION:
                groupCnName = getDimensionLevelPath(monthAnalysisVO, groupCnName);
                break;
            case SUBCATEGORY:
                groupCnName = getDimensionSubCateLevelPath(monthAnalysisVO, groupCnName);
                break;
            case SUB_DETAIL:
                groupCnName = getDimensionSubDetailLevelPath(monthAnalysisVO, groupCnName);
                break;
            case SPART:
                groupCnName = getDimensionSpartLevelPath(monthAnalysisVO, groupCnName);
                break;
            case CEG:
                groupCnName = getDimensionCegCnName(monthAnalysisVO, groupCnName);
                break;
            case MODL:
                groupCnName = getDimensionModlCnName(monthAnalysisVO, groupCnName);
                break;
            case CATEGORY:
                groupCnName = getDimensionCategoryName(monthAnalysisVO, groupCnName);
                break;
            default:
                break;
        }
        return groupCnName;
    }

    private String getMadeDimensionCnName(MonthAnalysisVO monthAnalysisVO, String madeGroupCnName) {
        GroupLevelEnumMadeD levelEnumMadeD = GroupLevelEnumMadeD.getInstance(monthAnalysisVO.getGroupLevel());
        switch (levelEnumMadeD) {
            case LV0:
                madeGroupCnName = IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc();
                break;
            case LV1:
                madeGroupCnName = lv1LevelPath(monthAnalysisVO, IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc());
                break;
            case LV2:
                madeGroupCnName = lv2LevelPath(monthAnalysisVO, IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc());
                break;
            case LV3:
                madeGroupCnName = lv3LevelPath(monthAnalysisVO, IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc());
                break;
            case LV4:
                madeGroupCnName = lv4LevelPath(monthAnalysisVO, IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc());
                break;
            case COA:
                madeGroupCnName = getCoaLevelPath(monthAnalysisVO, madeGroupCnName);
                break;
            case DIMENSION:
                madeGroupCnName = getDimensionLevelPath(monthAnalysisVO, madeGroupCnName);
                break;
            case SUBCATEGORY:
                madeGroupCnName = getDimensionSubCateLevelPath(monthAnalysisVO, madeGroupCnName);
                break;
            case SUB_DETAIL:
                madeGroupCnName = getDimensionSubDetailLevelPath(monthAnalysisVO, madeGroupCnName);
                break;
            case SPART:
                madeGroupCnName = getDimensionSpartLevelPath(monthAnalysisVO, madeGroupCnName);
                break;
            case SHIPPING_OBJECT:
                madeGroupCnName = getMadeDimensionCegCnName(monthAnalysisVO, madeGroupCnName);
                break;
            case MANUFACTURE_OBJECT:
                madeGroupCnName = getMadeDimensionModlCnName(monthAnalysisVO, madeGroupCnName);
                break;
            default:
                break;
        }
        return madeGroupCnName;
    }

    private String getPftCnName(MonthAnalysisVO monthVO, String groupCnName) {
        GroupLevelEnumP levelEnumsP = GroupLevelEnumP.getInstance(monthVO.getGroupLevel());
        switch (levelEnumsP) {
            case LV0:
                groupCnName = IndustryConst.getIndustryOrgName(monthVO.getIndustryOrg()).getDesc();
                break;
            case LV1:
                groupCnName = lv1LevelPath(monthVO, IndustryConst.getIndustryOrgName(monthVO.getIndustryOrg()).getDesc());
                break;
            case LV2:
                groupCnName = lv2LevelPath(monthVO, IndustryConst.getIndustryOrgName(monthVO.getIndustryOrg()).getDesc());
                break;
            case L1:
                groupCnName = l1LevelPath(monthVO);
                break;
            case L2:
                groupCnName = l2LevelPath(monthVO);
                break;
            case CEG:
                groupCnName = getUniversalCegCnName(monthVO, groupCnName);
                break;
            case MODL:
                groupCnName = getUniversalModlCnName(monthVO, groupCnName);
                break;
            case CATEGORY:
                groupCnName = getUniversalCategoryName(monthVO, groupCnName);
                break;
            default:
                break;
        }
        return groupCnName;
    }

    private String getMadePftCnName(MonthAnalysisVO monthAnalysisVO, String groupCnName) {
        GroupLevelEnumMadeP levelEnumMadeP = GroupLevelEnumMadeP.getInstance(monthAnalysisVO.getGroupLevel());
        switch (levelEnumMadeP) {
            case LV0:
                groupCnName = IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc();
                break;
            case LV1:
                groupCnName = lv1LevelPath(monthAnalysisVO, IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc());
                break;
            case LV2:
                groupCnName = lv2LevelPath(monthAnalysisVO, IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc());
                break;
            case L1:
                groupCnName = l1LevelPath(monthAnalysisVO);
                break;
            case L2:
                groupCnName = l2LevelPath(monthAnalysisVO);
                break;
            case SHIPPING_OBJECT:
                groupCnName = getMadeUniversalCegCnName(monthAnalysisVO, groupCnName);
                break;
            case MANUFACTURE_OBJECT:
                groupCnName = getMadeUniversalModlCnName(monthAnalysisVO, groupCnName);
                break;
            default:
                break;
        }
        return groupCnName;
    }

    public String getUniversalCnName(MonthAnalysisVO monthAnalysisVO, String groupCnName) {
        GroupLevelEnumU levelEnumsU = GroupLevelEnumU.getInstance(monthAnalysisVO.getGroupLevel());
        switch (levelEnumsU) {
            case LV0:
                groupCnName = IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc();
                break;
            case LV1:
                groupCnName = getUnLv1GroupCnName(monthAnalysisVO, groupCnName);
                break;
            case LV2:
                groupCnName = getUnLv2GroupCnName(monthAnalysisVO, groupCnName);
                break;
            case LV3:
                groupCnName = lv3LevelPath(monthAnalysisVO, IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc());
                break;
            case LV4:
                groupCnName = lv4LevelPath(monthAnalysisVO, IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc());
                break;
            case CEG:
                groupCnName = getPftCegCnName(monthAnalysisVO, groupCnName);
                break;
            case MODL:
                groupCnName = getPftModlCnName(monthAnalysisVO, groupCnName);
                break;
            case CATEGORY:
                groupCnName = getPftCategoryName(monthAnalysisVO, groupCnName);
                break;
            default:
                break;
        }
        return groupCnName;
    }

    private String getManufactureUniversalCnName(MonthAnalysisVO monthAnalysisVO, String groupCnName) {
        GroupLevelEnumMadeU levelEnumMadeU = GroupLevelEnumMadeU.getInstance(monthAnalysisVO.getGroupLevel());
        switch (levelEnumMadeU) {
            case LV0:
                groupCnName = IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc();
                break;
            case LV1:
                groupCnName = getMadeUnLv1GroupCnName(monthAnalysisVO, groupCnName);
                break;
            case LV2:
                groupCnName = getMadeUnLv2GroupCnName(monthAnalysisVO, groupCnName);
                break;
            case LV3:
                groupCnName = lv3LevelPath(monthAnalysisVO, IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc());
                break;
            case LV4:
                groupCnName = lv4LevelPath(monthAnalysisVO, IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc());
                break;
            case SHIPPING_OBJECT:
                groupCnName = getPftShippingObjectCnName(monthAnalysisVO, groupCnName);
                break;
            case MANUFACTURE_OBJECT:
                groupCnName = getPftManufactureObjectCnName(monthAnalysisVO, groupCnName);
                break;
            default:
                break;
        }
        return groupCnName;
    }

    @NotNull
    public String lv3LevelPath(MonthAnalysisVO monthAnalysisVO, String name) {
        String groupCnName;
        if (monthAnalysisVO.getLv3ProdRdTeamCnName().size() == 1) {
            groupCnName = lv2LevelPath(monthAnalysisVO, name) + CONCAT_FLAG + monthAnalysisVO.getLv3ProdRdTeamCnName().get(0);
        } else {
            String mutilLv3ProdRdTeamCnName = monthAnalysisVO.getLv3ProdRdTeamCnName().stream().map(String::valueOf).collect(Collectors.joining(","));
            groupCnName = lv2LevelPath(monthAnalysisVO, name) + CONCAT_FLAG + "多LV3(" + mutilLv3ProdRdTeamCnName + ")";
        }
        return groupCnName;
    }

    @NotNull
    private String lv4LevelPath(MonthAnalysisVO monthAnalysisVO, String name) {
        String groupCnName;
        if (monthAnalysisVO.getLv4ProdRdTeamCnName().size() == 1) {
            groupCnName = lv3LevelPath(monthAnalysisVO, name) + CONCAT_FLAG + monthAnalysisVO.getLv4ProdRdTeamCnName().get(0);
        } else {
            String mutilLv4ProdRdTeamCnName = monthAnalysisVO.getLv4ProdRdTeamCnName().stream().map(String::valueOf).collect(Collectors.joining(","));
            groupCnName = lv3LevelPath(monthAnalysisVO, name) + CONCAT_FLAG + "多LV3.5(" + mutilLv4ProdRdTeamCnName + ")";
        }
        return groupCnName;
    }

    @NotNull
    private String lv2LevelPath(MonthAnalysisVO monthAnalysisVO, String name) {
        String groupCnName;
        if (monthAnalysisVO.getLv2ProdRdTeamCnName().size() == 1) {
            groupCnName = lv1LevelPath(monthAnalysisVO, name) + CONCAT_FLAG + monthAnalysisVO.getLv2ProdRdTeamCnName().get(0);
        } else {
            String mutilLv2ProdRdTeamCnName = monthAnalysisVO.getLv2ProdRdTeamCnName().stream().map(String::valueOf).collect(Collectors.joining(","));
            groupCnName = lv1LevelPath(monthAnalysisVO, name) + CONCAT_FLAG + "多LV2(" + mutilLv2ProdRdTeamCnName + ")";
        }
        return groupCnName;
    }

    @NotNull
    private String lv1LevelPath(MonthAnalysisVO monthAnalysisVO, String name) {
        String groupCnName;
        if (monthAnalysisVO.getLv1ProdRdTeamCnName().size() == 1) {
            groupCnName = name + CONCAT_FLAG + monthAnalysisVO.getLv1ProdRdTeamCnName().get(0);
        } else {
            String mutilLv1ProdRdTeamCnName = monthAnalysisVO.getLv1ProdRdTeamCnName().stream().map(String::valueOf).collect(Collectors.joining(","));
            groupCnName = name + CONCAT_FLAG + "多LV1(" + mutilLv1ProdRdTeamCnName + ")";
        }
        return groupCnName;
    }

    @NotNull
    private String l1LevelPath(MonthAnalysisVO monthAnalysisVO) {
        String groupCnName;
        String lv2LevelPathStr = lv2LevelPath(monthAnalysisVO, IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc());
        if (monthAnalysisVO.getL1NameList().size() == 1) {
            groupCnName = lv2LevelPathStr + CONCAT_FLAG + monthAnalysisVO.getL1NameList().get(0);
        } else {
            String mutilL1Name = monthAnalysisVO.getL1NameList().stream().map(String::valueOf).collect(Collectors.joining(","));
            groupCnName = lv2LevelPathStr + CONCAT_FLAG + "多L1(" + mutilL1Name + ")";
        }
        return groupCnName;
    }

    @NotNull
    private String l2LevelPath(MonthAnalysisVO monthAnalysisVO) {
        String groupCnName;
        String l1LevelPathStr = l1LevelPath(monthAnalysisVO);
        if (monthAnalysisVO.getL2NameList().size() == 1) {
            groupCnName = l1LevelPathStr + CONCAT_FLAG + monthAnalysisVO.getL2NameList().get(0);
        } else {
            String mutilL2Name = monthAnalysisVO.getL2NameList().stream().map(String::valueOf).collect(Collectors.joining(","));
            groupCnName = l1LevelPathStr + CONCAT_FLAG + "多L2(" + mutilL2Name + ")";
        }
        return groupCnName;
    }


    @NotNull
    private String getCoaLevelPath(MonthAnalysisVO monthAnalysisVO, String groupCnName) {
        String mutilCoaCnName = monthAnalysisVO.getCoaCnName().stream().map(String::valueOf).collect(Collectors.joining(","));
        return setCoaCnName(monthAnalysisVO, groupCnName, mutilCoaCnName, IndustryIndexEnum.VIEW_FLAG_D.VIEW13, lv3LevelPath(monthAnalysisVO, IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc()));
    }

    @NotNull
    private String getDimensionLevelPath(MonthAnalysisVO monthAnalysisVO, String groupCnName) {
        String mutilDimensionCnName = monthAnalysisVO.getDimensionCnName().stream().map(String::valueOf).collect(Collectors.joining(","));
        groupCnName = setDimensionCnName(monthAnalysisVO, groupCnName, mutilDimensionCnName, IndustryIndexEnum.VIEW_FLAG_D.VIEW1, IndustryIndexEnum.VIEW_FLAG_D.VIEW2, IndustryIndexEnum.VIEW_FLAG_D.VIEW3, IndustryIndexEnum.VIEW_FLAG_D.VIEW10, lv1LevelPath(monthAnalysisVO, IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc()));
        groupCnName = setDimensionCnName(monthAnalysisVO, groupCnName, mutilDimensionCnName, IndustryIndexEnum.VIEW_FLAG_D.VIEW4, IndustryIndexEnum.VIEW_FLAG_D.VIEW5, IndustryIndexEnum.VIEW_FLAG_D.VIEW6, IndustryIndexEnum.VIEW_FLAG_D.VIEW11, lv2LevelPath(monthAnalysisVO, IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc()));
        groupCnName = setDimensionCnName(monthAnalysisVO, groupCnName, mutilDimensionCnName, IndustryIndexEnum.VIEW_FLAG_D.VIEW7, IndustryIndexEnum.VIEW_FLAG_D.VIEW8, IndustryIndexEnum.VIEW_FLAG_D.VIEW9, IndustryIndexEnum.VIEW_FLAG_D.VIEW12, lv3LevelPath(monthAnalysisVO, IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc()));

        // 视角13 coa-dimension
        if (!IndustryConst.INDUSTRY_ORG.IAS.getValue().equals(monthAnalysisVO.getIndustryOrg()) && IndustryIndexEnum.VIEW_FLAG_D.VIEW13.getValue().equals(monthAnalysisVO.getViewFlag())) {
            if (monthAnalysisVO.getDimensionCnName().size() == 1) {
                groupCnName = getCoaLevelPath(monthAnalysisVO, groupCnName) + CONCAT_FLAG + monthAnalysisVO.getDimensionCnName().get(0);
            } else {
                groupCnName = getCoaLevelPath(monthAnalysisVO, groupCnName) + CONCAT_FLAG + "多量纲(" + mutilDimensionCnName + ")";
            }
        }
        // 视角13 lv4-dimension
        if (IndustryConst.INDUSTRY_ORG.IAS.getValue().equals(monthAnalysisVO.getIndustryOrg()) && IndustryIndexEnum.VIEW_FLAG_D.VIEW13.getValue().equals(monthAnalysisVO.getViewFlag())) {
            if (monthAnalysisVO.getDimensionCnName().size() == 1) {
                groupCnName = lv4LevelPath(monthAnalysisVO, groupCnName) + CONCAT_FLAG + monthAnalysisVO.getDimensionCnName().get(0);
            } else {
                groupCnName = lv4LevelPath(monthAnalysisVO, groupCnName) + CONCAT_FLAG + "多量纲(" + mutilDimensionCnName + ")";
            }
        }
        return groupCnName;
    }

    private String setCoaCnName(MonthAnalysisVO monthAnalysisVO, String groupCnName, String mutilCoaCnName, IndustryIndexEnum.VIEW_FLAG_D view13, String levelPath) {
        if (view13.getValue().equals(monthAnalysisVO.getViewFlag())) {
            if (monthAnalysisVO.getCoaCnName().size() == 1) {
                groupCnName = levelPath + CONCAT_FLAG + monthAnalysisVO.getCoaCnName().get(0);
            } else {
                groupCnName = levelPath + CONCAT_FLAG + "多COA(" + mutilCoaCnName + ")";
            }
        }
        return groupCnName;
    }

    private String setDimensionCnName(MonthAnalysisVO monthAnalysisVO, String groupCnName, String mutilDimensionCnName, IndustryIndexEnum.VIEW_FLAG_D view1, IndustryIndexEnum.VIEW_FLAG_D view2, IndustryIndexEnum.VIEW_FLAG_D view3, IndustryIndexEnum.VIEW_FLAG_D view9, String levelPath) {
        if (view1.getValue().equals(monthAnalysisVO.getViewFlag()) || view2.getValue().equals(monthAnalysisVO.getViewFlag())
                || view3.getValue().equals(monthAnalysisVO.getViewFlag()) || view9.getValue().equals(monthAnalysisVO.getViewFlag())) {
            if (monthAnalysisVO.getDimensionCnName().size() == 1) {
                groupCnName = levelPath + CONCAT_FLAG + monthAnalysisVO.getDimensionCnName().get(0);
            } else {
                groupCnName = levelPath + CONCAT_FLAG + "多量纲(" + mutilDimensionCnName + ")";
            }
        }
        return groupCnName;
    }

    @NotNull
    private String getDimensionSubCateLevelPath(MonthAnalysisVO monthAnalysisVO, String groupCnName) {
        boolean flag = IndustryIndexEnum.VIEW_FLAG_D.VIEW2.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D.VIEW3.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D.VIEW5.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D.VIEW6.getValue().equals(monthAnalysisVO.getViewFlag());
        boolean dimensionViewFlag = IndustryIndexEnum.VIEW_FLAG_D.VIEW8.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D.VIEW9.getValue().equals(monthAnalysisVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_D.VIEW10.getValue().equals(monthAnalysisVO.getViewFlag());
        boolean dimensionSubViewFlag = IndustryIndexEnum.VIEW_FLAG_D.VIEW11.getValue().equals(monthAnalysisVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_D.VIEW12.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D.VIEW13.getValue().equals(monthAnalysisVO.getViewFlag());
        if (flag || dimensionViewFlag || dimensionSubViewFlag) {
            if (monthAnalysisVO.getDimensionSubCategoryCnName().size() == 1) {
                groupCnName = getDimensionLevelPath(monthAnalysisVO, groupCnName) + CONCAT_FLAG + monthAnalysisVO.getDimensionSubCategoryCnName().get(0);
            } else {
                String mutilDimensionSubCateCnName = monthAnalysisVO.getDimensionSubCategoryCnName().stream().map(String::valueOf).collect(Collectors.joining(","));
                groupCnName = getDimensionLevelPath(monthAnalysisVO, IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc()) + CONCAT_FLAG + "多量纲子类(" + mutilDimensionSubCateCnName + ")";
            }
        }
        return groupCnName;
    }

    @NotNull
    private String getDimensionSubDetailLevelPath(MonthAnalysisVO monthAnalysisVO, String groupCnName) {
        Boolean viewFlag = IndustryIndexEnum.VIEW_FLAG_D.VIEW3.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D.VIEW6.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D.VIEW9.getValue().equals(monthAnalysisVO.getViewFlag());
        Boolean viewFlag1 = IndustryIndexEnum.VIEW_FLAG_D.VIEW10.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D.VIEW11.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D.VIEW12.getValue().equals(monthAnalysisVO.getViewFlag());
        if (viewFlag || viewFlag1 || IndustryIndexEnum.VIEW_FLAG_D.VIEW13.getValue().equals(monthAnalysisVO.getViewFlag())) {
            if (monthAnalysisVO.getDimensionSubDetailCnName().size() == 1) {
                groupCnName = getDimensionSubCateLevelPath(monthAnalysisVO, groupCnName) + CONCAT_FLAG + monthAnalysisVO.getDimensionSubDetailCnName().get(0);
            } else {
                String mutilDimensionSubDetailCnName = monthAnalysisVO.getDimensionSubDetailCnName().stream().map(String::valueOf).collect(Collectors.joining(","));
                groupCnName = getDimensionSubCateLevelPath(monthAnalysisVO, IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc()) + CONCAT_FLAG + "多量纲子类明细(" + mutilDimensionSubDetailCnName + ")";
            }
        }
        return groupCnName;
    }

    private String getDimensionSpartLevelPath(MonthAnalysisVO monthAnalysisVO, String groupCnName) {
        if (IndustryIndexEnum.VIEW_FLAG_D.VIEW10.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D.VIEW11.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D.VIEW12.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D.VIEW13.getValue().equals(monthAnalysisVO.getViewFlag())) {
            if (monthAnalysisVO.getSpartCnName().size() == 1) {
                groupCnName = getDimensionSubDetailLevelPath(monthAnalysisVO, groupCnName) + CONCAT_FLAG + monthAnalysisVO.getSpartCnName().get(0);
            } else {
                String mutilSpartCnName = monthAnalysisVO.getSpartCnName().stream().map(String::valueOf).collect(Collectors.joining(","));
                groupCnName = getDimensionSubDetailLevelPath(monthAnalysisVO, IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc()) + CONCAT_FLAG + "多SPART(" + mutilSpartCnName + ")";
            }
        }
        return groupCnName;
    }

    private String getDimensionCategoryName(MonthAnalysisVO monthAnalysisVO, String groupCnName) {
        return getDimensionModlCnName(monthAnalysisVO, groupCnName) + CONCAT_FLAG + monthAnalysisVO.getCategoryCnName();
    }

    private String getDimensionModlCnName(MonthAnalysisVO monthAnalysisVO, String groupCnName) {
        return getDimensionCegCnName(monthAnalysisVO, groupCnName) + CONCAT_FLAG + monthAnalysisVO.getLv4CegCnName();
    }

    private String getMadeDimensionModlCnName(MonthAnalysisVO monthAnalysisVO, String groupCnName) {
        return getMadeDimensionCegCnName(monthAnalysisVO, groupCnName) + CONCAT_FLAG + monthAnalysisVO.getManufactureObjectCnName();
    }

    private String getDimensionCegCnName(MonthAnalysisVO monthAnalysisVO, String groupCnName) {
        if (IndustryIndexEnum.VIEW_FLAG_D.VIEW1.getValue().equals(monthAnalysisVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_D.VIEW4.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D.VIEW7.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = getDimensionLevelPath(monthAnalysisVO, groupCnName) + CONCAT_FLAG + monthAnalysisVO.getLv3CegCnName();
        }
        if (IndustryIndexEnum.VIEW_FLAG_D.VIEW2.getValue().equals(monthAnalysisVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_D.VIEW5.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D.VIEW8.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = getDimensionSubCateLevelPath(monthAnalysisVO, groupCnName) + CONCAT_FLAG + monthAnalysisVO.getLv3CegCnName();
        }
        if (IndustryIndexEnum.VIEW_FLAG_D.VIEW3.getValue().equals(monthAnalysisVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_D.VIEW6.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D.VIEW9.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = getDimensionSubDetailLevelPath(monthAnalysisVO, groupCnName) + CONCAT_FLAG + monthAnalysisVO.getLv3CegCnName();
        }
        if (IndustryIndexEnum.VIEW_FLAG_D.VIEW10.getValue().equals(monthAnalysisVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_D.VIEW11.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D.VIEW12.getValue().equals(monthAnalysisVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_D.VIEW13.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = getDimensionSpartLevelPath(monthAnalysisVO, groupCnName) + CONCAT_FLAG + monthAnalysisVO.getLv3CegCnName();
        }
        return groupCnName;
    }

    private String getMadeDimensionCegCnName(MonthAnalysisVO monthAnalysisVO, String groupCnName) {
        if (IndustryIndexEnum.VIEW_FLAG_D_M.VIEW1.getValue().equals(monthAnalysisVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_D_M.VIEW4.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D_M.VIEW7.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = getDimensionLevelPath(monthAnalysisVO, groupCnName) + CONCAT_FLAG + monthAnalysisVO.getShippingObjectCnName();
        }
        if (IndustryIndexEnum.VIEW_FLAG_D_M.VIEW2.getValue().equals(monthAnalysisVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_D_M.VIEW5.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D_M.VIEW8.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = getDimensionSubCateLevelPath(monthAnalysisVO, groupCnName) + CONCAT_FLAG + monthAnalysisVO.getShippingObjectCnName();
        }
        if (IndustryIndexEnum.VIEW_FLAG_D_M.VIEW3.getValue().equals(monthAnalysisVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_D_M.VIEW6.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D_M.VIEW9.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = getDimensionSubDetailLevelPath(monthAnalysisVO, groupCnName) + CONCAT_FLAG + monthAnalysisVO.getShippingObjectCnName();
        }
        if (IndustryIndexEnum.VIEW_FLAG_D_M.VIEW10.getValue().equals(monthAnalysisVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_D_M.VIEW11.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_D_M.VIEW12.getValue().equals(monthAnalysisVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_D_M.VIEW13.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = getDimensionSpartLevelPath(monthAnalysisVO, groupCnName) + CONCAT_FLAG + monthAnalysisVO.getShippingObjectCnName();
        }
        return groupCnName;
    }

    private String getUniversalCategoryName(MonthAnalysisVO monthAnalysisVO, String groupCnName) {
        return getUniversalModlCnName(monthAnalysisVO, groupCnName) + CONCAT_FLAG + monthAnalysisVO.getCategoryCnName();
    }

    private String getUniversalModlCnName(MonthAnalysisVO monthAnalysisVO, String groupCnName) {
        return getUniversalCegCnName(monthAnalysisVO, groupCnName) + CONCAT_FLAG + monthAnalysisVO.getLv4CegCnName();
    }

    private String getMadeUniversalModlCnName(MonthAnalysisVO monthAnalysisVO, String groupCnName) {
        return getMadeUniversalCegCnName(monthAnalysisVO, groupCnName) + CONCAT_FLAG + monthAnalysisVO.getManufactureObjectCnName();
    }

    private String getUniversalCegCnName(MonthAnalysisVO monthAnalysisVO, String groupCnName) {
        String industryOrgName = IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc();
        if (IndustryIndexEnum.VIEW_FLAG_P.VIEW1.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = industryOrgName + CONCAT_FLAG + monthAnalysisVO.getLv3CegCnName();
        }
        if (IndustryIndexEnum.VIEW_FLAG_P.VIEW2.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = lv1LevelPath(monthAnalysisVO, industryOrgName) + CONCAT_FLAG + monthAnalysisVO.getLv3CegCnName();
        }
        if (IndustryIndexEnum.VIEW_FLAG_P.VIEW3.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = lv2LevelPath(monthAnalysisVO, industryOrgName) + CONCAT_FLAG + monthAnalysisVO.getLv3CegCnName();
        }
        if (IndustryIndexEnum.VIEW_FLAG_P.VIEW4.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = l1LevelPath(monthAnalysisVO) + CONCAT_FLAG + monthAnalysisVO.getLv3CegCnName();
        }
        if (IndustryIndexEnum.VIEW_FLAG_P.VIEW5.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = l2LevelPath(monthAnalysisVO) + CONCAT_FLAG + monthAnalysisVO.getLv3CegCnName();
        }
        return groupCnName;
    }

    private String getMadeUniversalCegCnName(MonthAnalysisVO monthAnalysisVO, String groupCnName) {
        String industryOrgName = IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc();
        if (IndustryIndexEnum.VIEW_FLAG_P.VIEW1.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = industryOrgName + CONCAT_FLAG + monthAnalysisVO.getShippingObjectCnName();
        }
        if (IndustryIndexEnum.VIEW_FLAG_P.VIEW2.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = lv1LevelPath(monthAnalysisVO, industryOrgName) + CONCAT_FLAG + monthAnalysisVO.getShippingObjectCnName();
        }
        if (IndustryIndexEnum.VIEW_FLAG_P.VIEW3.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = lv2LevelPath(monthAnalysisVO, industryOrgName) + CONCAT_FLAG + monthAnalysisVO.getShippingObjectCnName();
        }
        if (IndustryIndexEnum.VIEW_FLAG_P.VIEW4.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = l1LevelPath(monthAnalysisVO) + CONCAT_FLAG + monthAnalysisVO.getShippingObjectCnName();
        }
        if (IndustryIndexEnum.VIEW_FLAG_P.VIEW5.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = l2LevelPath(monthAnalysisVO) + CONCAT_FLAG + monthAnalysisVO.getShippingObjectCnName();
        }
        return groupCnName;
    }

    @NotNull
    private String getUnLv2GroupCnName(MonthAnalysisVO monthAnalysisVO, String groupCnName) {
        if (monthAnalysisVO.getLv2ProdRdTeamCnName().size() == 1) {
            groupCnName = getUnLv1GroupCnName(monthAnalysisVO, groupCnName) + CONCAT_FLAG + monthAnalysisVO.getLv2ProdRdTeamCnName().get(0);
        } else {
            String mutilLv2ProdRdTeamCnName = monthAnalysisVO.getLv2ProdRdTeamCnName().stream().map(String::valueOf).collect(Collectors.joining(","));
            groupCnName = getUnLv1GroupCnName(monthAnalysisVO, groupCnName) + CONCAT_FLAG + "多LV2(" + mutilLv2ProdRdTeamCnName + ")";
        }
        if (!IndustryIndexEnum.VIEW_FLAG_U.VIEW5.getValue().equals(monthAnalysisVO.getViewFlag())
                && !IndustryIndexEnum.VIEW_FLAG_U.VIEW6.getValue().equals(monthAnalysisVO.getViewFlag())
                && !IndustryIndexEnum.VIEW_FLAG_U.VIEW7.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = lv2LevelPath(monthAnalysisVO, IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc());
        }
        return groupCnName;
    }

    @NotNull
    private String getMadeUnLv2GroupCnName(MonthAnalysisVO monthAnalysisVO, String groupCnName) {
        if (monthAnalysisVO.getLv2ProdRdTeamCnName().size() == 1) {
            groupCnName = getMadeUnLv1GroupCnName(monthAnalysisVO, groupCnName) + CONCAT_FLAG + monthAnalysisVO.getLv2ProdRdTeamCnName().get(0);
        } else {
            String mutilLv2ProdRdTeamCnName = monthAnalysisVO.getLv2ProdRdTeamCnName().stream().map(String::valueOf).collect(Collectors.joining(","));
            groupCnName = getMadeUnLv1GroupCnName(monthAnalysisVO, groupCnName) + CONCAT_FLAG + "多LV2(" + mutilLv2ProdRdTeamCnName + ")";
        }
        if (!IndustryIndexEnum.VIEW_FLAG_U_M.VIEW6.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = lv2LevelPath(monthAnalysisVO, IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc());
        }
        return groupCnName;
    }

    @NotNull
    private String getUnLv1GroupCnName(MonthAnalysisVO monthAnalysisVO, String groupCnName) {
        String industryOrgName = IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc();
        String mutilLv1ProdRdTeamCnName = monthAnalysisVO.getLv1ProdRdTeamCnName().stream().map(String::valueOf).collect(Collectors.joining(","));
        // 采购成本,反向视角
        if (IndustryIndexEnum.VIEW_FLAG_U.VIEW5.getValue().equals(monthAnalysisVO.getViewFlag())) {
            if (monthAnalysisVO.getLv1ProdRdTeamCnName().size() == 1) {
                groupCnName = industryOrgName + CONCAT_FLAG + monthAnalysisVO.getLv3CegCnName() +
                        CONCAT_FLAG + monthAnalysisVO.getLv1ProdRdTeamCnName().get(0);
            } else {
                groupCnName = industryOrgName + CONCAT_FLAG + monthAnalysisVO.getLv3CegCnName() +
                        CONCAT_FLAG + "多LV1(" + mutilLv1ProdRdTeamCnName + ")";
            }
        }
        groupCnName = setGroupCnNameByView(monthAnalysisVO, groupCnName, mutilLv1ProdRdTeamCnName);
        // 正常视角
        if (!IndustryIndexEnum.VIEW_FLAG_U.VIEW5.getValue().equals(monthAnalysisVO.getViewFlag())
                && !IndustryIndexEnum.VIEW_FLAG_U.VIEW6.getValue().equals(monthAnalysisVO.getViewFlag())
                && !IndustryIndexEnum.VIEW_FLAG_U.VIEW7.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = lv1LevelPath(monthAnalysisVO, industryOrgName);
        }
        return groupCnName;
    }

    private String setGroupCnNameByView(MonthAnalysisVO monthAnalysisVO, String groupCnName, String mutilLv1ProdRdTeamCnName) {
        String industryOrgName = IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc();
        if (IndustryIndexEnum.VIEW_FLAG_U.VIEW6.getValue().equals(monthAnalysisVO.getViewFlag())) {
            if (monthAnalysisVO.getLv1ProdRdTeamCnName().size() == 1) {
                groupCnName = industryOrgName + CONCAT_FLAG + monthAnalysisVO.getLv3CegCnName() + CONCAT_FLAG + monthAnalysisVO.getLv4CegCnName() +
                        CONCAT_FLAG + monthAnalysisVO.getLv1ProdRdTeamCnName().get(0);
            } else {
                groupCnName = industryOrgName + CONCAT_FLAG + monthAnalysisVO.getLv3CegCnName() + CONCAT_FLAG + monthAnalysisVO.getLv4CegCnName() +
                        CONCAT_FLAG + "多LV1(" + mutilLv1ProdRdTeamCnName + ")";
            }
        }
        if (IndustryIndexEnum.VIEW_FLAG_U.VIEW7.getValue().equals(monthAnalysisVO.getViewFlag())) {
            if (monthAnalysisVO.getLv1ProdRdTeamCnName().size() == 1) {
                groupCnName = industryOrgName + CONCAT_FLAG + monthAnalysisVO.getLv3CegCnName() + CONCAT_FLAG + monthAnalysisVO.getLv4CegCnName() +
                        CONCAT_FLAG + monthAnalysisVO.getCategoryCnName() +
                        CONCAT_FLAG + monthAnalysisVO.getLv1ProdRdTeamCnName().get(0);
            } else {
                groupCnName = industryOrgName + CONCAT_FLAG + monthAnalysisVO.getLv3CegCnName() + CONCAT_FLAG + monthAnalysisVO.getLv4CegCnName() +
                        CONCAT_FLAG + monthAnalysisVO.getCategoryCnName() +
                        CONCAT_FLAG + "多LV1(" + mutilLv1ProdRdTeamCnName + ")";
            }
        }
        return groupCnName;
    }

    @NotNull
    private String getMadeUnLv1GroupCnName(MonthAnalysisVO monthAnalysisVO, String groupCnName) {
        String industryOrgName = IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc();
        String mutilLv1ProdRdTeamCnName = monthAnalysisVO.getLv1ProdRdTeamCnName().stream().map(String::valueOf).collect(Collectors.joining(","));
        // 制造成本,反向视角
        if (IndustryIndexEnum.VIEW_FLAG_U_M.VIEW6.getValue().equals(monthAnalysisVO.getViewFlag())) {
            if (monthAnalysisVO.getLv1ProdRdTeamCnName().size() == 1) {
                groupCnName = industryOrgName + CONCAT_FLAG + monthAnalysisVO.getShippingObjectCnName() + CONCAT_FLAG + monthAnalysisVO.getManufactureObjectCnName() +
                        CONCAT_FLAG + monthAnalysisVO.getLv1ProdRdTeamCnName().get(0);
            } else {
                groupCnName = industryOrgName + CONCAT_FLAG + monthAnalysisVO.getShippingObjectCnName() + CONCAT_FLAG + monthAnalysisVO.getManufactureObjectCnName() +
                        CONCAT_FLAG + "多LV1(" + mutilLv1ProdRdTeamCnName + ")";
            }
        }
        // 正常视角
        if (!IndustryIndexEnum.VIEW_FLAG_U_M.VIEW6.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = lv1LevelPath(monthAnalysisVO, IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc());
        }
        return groupCnName;
    }

    private String getPftCategoryName(MonthAnalysisVO monthAnalysisVO, String groupCnName) {
        if (!IndustryIndexEnum.VIEW_FLAG_U.VIEW5.getValue().equals(monthAnalysisVO.getViewFlag()) &&
                !IndustryIndexEnum.VIEW_FLAG_U.VIEW6.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = getPftModlCnName(monthAnalysisVO, groupCnName) + CONCAT_FLAG + monthAnalysisVO.getCategoryCnName();
        }
        return groupCnName;
    }

    private String getPftModlCnName(MonthAnalysisVO monthAnalysisVO, String groupCnName) {
        if (!IndustryIndexEnum.VIEW_FLAG_U.VIEW5.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = getPftCegCnName(monthAnalysisVO, groupCnName) + CONCAT_FLAG + monthAnalysisVO.getLv4CegCnName();
        }
        return groupCnName;
    }

    private String getPftShippingObjectCnName(MonthAnalysisVO monthAnalysisVO, String groupCnName) {
        String industryOrgName = IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc();
        if (IndustryIndexEnum.VIEW_FLAG_U.VIEW5.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_U.VIEW6.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_U.VIEW7.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_U.VIEW1.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = industryOrgName + CONCAT_FLAG + monthAnalysisVO.getShippingObjectCnName();
        }
        if (IndustryIndexEnum.VIEW_FLAG_U.VIEW2.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = lv1LevelPath(monthAnalysisVO, industryOrgName) + CONCAT_FLAG + monthAnalysisVO.getShippingObjectCnName();
        }
        if (IndustryIndexEnum.VIEW_FLAG_U.VIEW3.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = lv2LevelPath(monthAnalysisVO, industryOrgName) + CONCAT_FLAG + monthAnalysisVO.getShippingObjectCnName();
        }
        if (IndustryIndexEnum.VIEW_FLAG_U.VIEW4.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = lv3LevelPath(monthAnalysisVO, industryOrgName) + CONCAT_FLAG + monthAnalysisVO.getShippingObjectCnName();
        }
        if (IndustryIndexEnum.VIEW_FLAG_U.VIEW8.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = lv4LevelPath(monthAnalysisVO, industryOrgName) + CONCAT_FLAG + monthAnalysisVO.getShippingObjectCnName();
        }
        return groupCnName;
    }

    private String getPftManufactureObjectCnName(MonthAnalysisVO monthAnalysisVO, String groupCnName) {
        if (!IndustryIndexEnum.VIEW_FLAG_U.VIEW5.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = getPftShippingObjectCnName(monthAnalysisVO, groupCnName) + CONCAT_FLAG + monthAnalysisVO.getManufactureObjectCnName();
        }
        return groupCnName;
    }

    private String getPftCegCnName(MonthAnalysisVO monthAnalysisVO, String groupCnName) {
        if (IndustryIndexEnum.VIEW_FLAG_U.VIEW5.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_U.VIEW6.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_U.VIEW7.getValue().equals(monthAnalysisVO.getViewFlag())
                || IndustryIndexEnum.VIEW_FLAG_U.VIEW1.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc() + CONCAT_FLAG + monthAnalysisVO.getLv3CegCnName();
        }
        if (IndustryIndexEnum.VIEW_FLAG_U.VIEW2.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = lv1LevelPath(monthAnalysisVO, IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc()) + CONCAT_FLAG + monthAnalysisVO.getLv3CegCnName();
        }
        if (IndustryIndexEnum.VIEW_FLAG_U.VIEW3.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = lv2LevelPath(monthAnalysisVO, IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc()) + CONCAT_FLAG + monthAnalysisVO.getLv3CegCnName();
        }
        if (IndustryIndexEnum.VIEW_FLAG_U.VIEW4.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = lv3LevelPath(monthAnalysisVO, IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc()) + CONCAT_FLAG + monthAnalysisVO.getLv3CegCnName();
        }
        if (IndustryIndexEnum.VIEW_FLAG_U.VIEW8.getValue().equals(monthAnalysisVO.getViewFlag())) {
            groupCnName = lv4LevelPath(monthAnalysisVO, IndustryConst.getIndustryOrgName(monthAnalysisVO.getIndustryOrg()).getDesc()) + CONCAT_FLAG + monthAnalysisVO.getLv3CegCnName();
        }
        return groupCnName;
    }

    /**
     * @param viewInfoVO
     * @throws ApplicationException
     */
    @Override
    public void setViewFlagValueWithLookUp(DmFocViewInfoVO viewInfoVO) throws ApplicationException {
        industryCacheHandler.initLookupValue();
        // 查询Lookup中配置的通用视角
        setViewFLagValue(viewInfoVO, IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL, Constant.StrEnum.MANUFACTURE_LOOKUP_UNIVERSAL_VIEW, Constant.StrEnum.PURCHASE_LOOKUP_UNIVERSAL_VIEW, Constant.StrEnum.ALL_LOOKUP_UNIVERSAL_VIEW);
        // 查询Lookup中配置的盈利视角
        setViewFLagValue(viewInfoVO, IndustryIndexEnum.GRANULARITY_TYPE.PROFITS, Constant.StrEnum.MANUFACTURE_LOOKUP_PROFITS_VIEW, Constant.StrEnum.PURCHASE_LOOKUP_PROFITS_VIEW, Constant.StrEnum.ALL_LOOKUP_PROFITS_VIEW);
        // 查询Lookup中配置的量纲视角
        setViewFLagValue(viewInfoVO, IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION, Constant.StrEnum.MANUFACTURE_LOOKUP_DIMENSION_VIEW, Constant.StrEnum.PURCHASE_LOOKUP_DIMENSION_VIEW, Constant.StrEnum.ALL_LOOKUP_DIMENSION_VIEW);
    }

    private void setViewFLagValue(DmFocViewInfoVO viewInfoVO, IndustryIndexEnum.GRANULARITY_TYPE universal, Constant.StrEnum manufactureLookupUniversalView, Constant.StrEnum purchaseLookupUniversalView, Constant.StrEnum allLookupUniversalView) throws ApplicationException {
        String lookUpView;
        if (universal.getValue().equals(viewInfoVO.getGranularityType())) {
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(viewInfoVO.getCostType())) {
                lookUpView = manufactureLookupUniversalView.getValue();
            } else if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(viewInfoVO.getCostType())) {
                lookUpView = purchaseLookupUniversalView.getValue();
            } else {
                lookUpView = allLookupUniversalView.getValue();
            }
            List<LookupItemVO> universalItemList = lookupItemQueryService.findItemListByClassify(lookUpView);
            for (LookupItemVO lookupItemVO : universalItemList) {
                if (viewInfoVO.getViewFlag().equals(lookupItemVO.getItemCode())) {
                    // 视角名称区分ICT和数字能源和IAS
                    IndustryConst.INDUSTRY_ORG industryOrgName = IndustryConst.getIndustryOrgName(viewInfoVO.getIndustryOrg());
                    switch (industryOrgName) {
                        case ICT:
                            viewInfoVO.setViewFlagValue(lookupItemVO.getItemName().replace("$", ModuleEnum.ICT.getCnName()));
                            break;
                        case ENERGY:
                            viewInfoVO.setViewFlagValue(lookupItemVO.getItemName().replace("$", ModuleEnum.ENERGY.getCnName()));
                            break;
                        case IAS:
                            viewInfoVO.setViewFlagValue(lookupItemVO.getItemName().replace("$", ModuleEnum.IAS.getCnName()));
                            iasSpecialViewFlagValueSet(viewInfoVO, lookupItemVO);
                            break;
                        default:
                            break;
                    }
                }
            }
        }
    }

    public void iasSpecialViewFlagValueSet(DmFocViewInfoVO dmFocViewInfoVO, LookupItemVO lookupItemVO) {
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(lookupItemVO.getItemAttr1()) && IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(lookupItemVO.getItemAttr2())) {
            if (IndustryIndexEnum.VIEW_FLAG_U.VIEW5.getValue().equals(lookupItemVO.getItemCode())) {
                setViewFlagValue(dmFocViewInfoVO, lookupItemVO, "⑤", "⑥");
            }
            if (IndustryIndexEnum.VIEW_FLAG_U.VIEW6.getValue().equals(lookupItemVO.getItemCode())) {
                setViewFlagValue(dmFocViewInfoVO, lookupItemVO, "⑥", "⑦");
            }
            if (IndustryIndexEnum.VIEW_FLAG_U.VIEW7.getValue().equals(lookupItemVO.getItemCode())) {
                setViewFlagValue(dmFocViewInfoVO, lookupItemVO, "⑦", "⑧");
            }
        }
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(lookupItemVO.getItemAttr1()) && IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(lookupItemVO.getItemAttr2())) {
            if (IndustryIndexEnum.VIEW_FLAG_U.VIEW6.getValue().equals(lookupItemVO.getItemCode())) {
                setViewFlagValue(dmFocViewInfoVO, lookupItemVO, "⑥", "⑦");
            }
        }
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(lookupItemVO.getItemAttr2())) {
            if (IndustryIndexEnum.VIEW_FLAG_D.VIEW13.getValue().equals(lookupItemVO.getItemCode())) {
                dmFocViewInfoVO.setViewFlagValue(lookupItemVO.getItemName().replace("COA","LV3.5").replace("$",ModuleEnum.IAS.getCnName()));
            }
        }
    }

    private void setViewFlagValue(DmFocViewInfoVO dmFocViewInfoVO, LookupItemVO lookupItemVO, String oldStr, String newStr) {
        if (lookupItemVO.getItemName().contains("LV3")) {
            dmFocViewInfoVO.setViewFlagValue(StrUtil.replace(lookupItemVO.getItemName(), oldStr, newStr).replace("$", ModuleEnum.IAS.getCnName()));
        } else {
            dmFocViewInfoVO.setViewFlagValue(StrUtil.replace(lookupItemVO.getItemName() + "-LV3", oldStr, newStr).replace("$", ModuleEnum.IAS.getCnName()));
        }
    }

    @Override
    @JalorOperation(code = "getIndustryCurrentRoleDataPermission", desc = "查询产业成本当前登录角色数据范围权限")
    public DataPermissionsVO getCurrentRoleDataPermission(String industryOrg) {
        log.info(">>>Begin CommonService::getCurrentRoleDataPermission");
        DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        log.info(">>>CurrentRoleInfo:<{}-{}>", currentRole.getRoleId(), currentRole.getRoleName());
        dataPermissionsVO.setRoleId(currentRole.getRoleId());
        dataPermissionsVO.setRoleName(currentRole.getRoleName());
        List<ProgramVO> currentPrograms = currentUser.getCurrentPrograms();
        Set<String> dimensionValues = new HashSet<>();
        overallViewRoleDimension(currentRole, dimensionValues);
        for (ProgramVO programVO : currentPrograms) {
            List<ProgramItemVO> programItemVOList = programVO.getItems();
            for (ProgramItemVO programItemVO : programItemVOList) {
                // (ICT + 数字能源 + IAS)的数据范围
                if (Constant.StrEnum.DIMENSION_CODE.getValue().equals(programItemVO.getDimension().getDimensionCode())) {
                    dimensionValues.addAll(programItemVO.getValues().stream().map(KeyValuePairVO::getKey).collect(Collectors.toSet()));
                }
            }
        }
        setAllDimensionPermission(dataPermissionsVO, dimensionValues, industryOrg);
        return dataPermissionsVO;
    }

    private void overallViewRoleDimension(RoleVO currentRole, Set<String> dimensionValues) {
        // 判断是否总体查看人
        List<LookupItemVO> overallViewerRoleLsit = null;
        try {
            overallViewerRoleLsit = lookupItemQueryService.findItemListByClassify("OVERALL_VIEWER_ROLE");
        } catch (ApplicationException ex) {
            log.error("get lookupItemQueryService error: {}", ex.getMessage());
        }
        List<String> roleNameList = overallViewerRoleLsit.stream().map(LookupItemVO::getItemName).collect(Collectors.toList());
        if (roleNameList.contains(currentRole.getRoleName())) {
            dimensionValues.add("ALL");
        }
    }

    @Override
    @JalorOperation(code = "distinctIndustryOrg", desc = "区分产业组织机构")
    public ResultDataVO distinctIndustryOrg() {
        log.info(">>>Begin CommonService::distinctIndustryOrg");
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        log.info(">>>CurrentRoleInfo:<{}-{}>", currentRole.getRoleId(), currentRole.getRoleName());
        List<ProgramVO> currentPrograms = currentUser.getCurrentPrograms();
        Set<String> dimensionValues = new HashSet<>();
        List<String> industryOrg = new ArrayList<>();
        // 判断是否总体查看人
        overallViewRoleDimension(currentRole, dimensionValues);
        for (ProgramVO programVO : currentPrograms) {
            List<ProgramItemVO> programItemVOList = programVO.getItems();
            for (ProgramItemVO programItemVO : programItemVOList) {
                // (ICT + 数字能源 + IAS + ICT_NEW)的数据范围
                if (Constant.StrEnum.DIMENSION_CODE.getValue().equals(programItemVO.getDimension().getDimensionCode())) {
                    dimensionValues.addAll(programItemVO.getValues().stream().map(KeyValuePairVO::getKey).collect(Collectors.toSet()));
                } else {
                    if (Constant.StrEnum.PROD_DIMENSION_CODE.getValue().equals(programItemVO.getDimension().getDimensionCode())) {
                        dimensionValues.addAll(programItemVO.getValues().stream().map(KeyValuePairVO::getKey).collect(Collectors.toSet()));
                    }
                }
            }
        }
        if (dimensionValues.contains("ALL") || dimensionValues.contains("@ALLCONDITION@")) {
            industryOrg.add("ICT");
            industryOrg.add("ENERGY");
            industryOrg.add("IAS");
            industryOrg.add("ICT_NEW");
        } else {
            setIndustryOrg(dimensionValues, industryOrg);
        }
        return ResultDataVO.success(industryOrg.stream().distinct().collect(Collectors.toList()));
    }

    private void setIndustryOrg(Set<String> dimensionValues, List<String> industryOrg) {
        List<String> dimensionList = new ArrayList<>(dimensionValues);
        List<String> ictDimensionValue = prodGroupsViewDao.getIctDimensionValue(dimensionList);
        if (CollectionUtils.isNotEmpty(ictDimensionValue)) {
            industryOrg.add("ICT");
        }
        List<String> energyDimensionValue = prodGroupsViewDao.getEnergyDimensionValue(dimensionList);
        if (CollectionUtils.isNotEmpty(energyDimensionValue)) {
            industryOrg.add("ENERGY");
        }
        List<String> iasDimensionValue = prodGroupsViewDao.getIasDimensionValue(dimensionList);
        if (CollectionUtils.isNotEmpty(iasDimensionValue)) {
            industryOrg.add("IAS");
        }
        List<String> ictNewDimensionValue = dataDimensionService.getIctNewDimensionValue(dimensionList);
        if (CollectionUtils.isNotEmpty(ictNewDimensionValue)) {
            industryOrg.add("ICT_NEW");
        }
    }



    private void setAllDimensionPermission(DataPermissionsVO dataPermissionsVO, Set<String> dimensionValues, String industryOrg) {
        if (CollectionUtils.isNotEmpty(dimensionValues)) {
            if (!dimensionValues.contains("ALL") && !dimensionValues.contains("@ALLCONDITION@")) {
                // 解析维度code编码
                parsingDimensionCode(dataPermissionsVO, dimensionValues, industryOrg);
            }
        } else {
            dataPermissionsVO.getLv0DimensionSet().add("NO_PERMISSION");
            dataPermissionsVO.getLv1DimensionSet().add("NO_PERMISSION");
            dataPermissionsVO.getLv2DimensionSet().add("NO_PERMISSION");
        }
    }

    private void parsingDimensionCode(DataPermissionsVO dataPermissionsVO, Set<String> dimensionValues, String industryOrg) {
        Set<String> lv0DimensionSet = dataPermissionsVO.getLv0DimensionSet();
        Set<String> lv1DimensionSet = dataPermissionsVO.getLv1DimensionSet();
        Set<String> lv2DimensionSet = dataPermissionsVO.getLv2DimensionSet();
        lv0DimensionSet(dimensionValues, lv0DimensionSet, industryOrg);
        lv1DimensionSet(dimensionValues, lv1DimensionSet, industryOrg);
        lv2DimensionSet(dimensionValues, lv2DimensionSet, industryOrg);

    }

    private void lv2DimensionSet(Set<String> dimensionValues, Set<String> lv2DimensionSet, String industryOrg) {
        for (String lv2ProdCode : dimensionValues) {
            if (IndustryConst.INDUSTRY_ORG.ICT.getValue().equals(industryOrg)) {
                if (lv2ProdCode.contains("_LV2")) {
                    lv2DimensionSet.add(lv2ProdCode.split("_")[0]);
                }
            } else if (IndustryConst.INDUSTRY_ORG.ENERGY.getValue().equals(industryOrg)) {
                if (lv2ProdCode.contains("_L2_E")) {
                    lv2DimensionSet.add(lv2ProdCode.split("_")[0]);
                }
            } else {
                if (lv2ProdCode.contains("_L2_IAS")) {
                    lv2DimensionSet.add(lv2ProdCode.split("_")[0]);
                }
            }
        }
        if (lv2DimensionSet.size() == 0) {
            lv2DimensionSet.add("NO_PERMISSION");
        }
    }

    private void lv1DimensionSet(Set<String> dimensionValues, Set<String> lv1DimensionSet, String industryOrg) {
        for (String lv1ProdCode : dimensionValues) {
            if (IndustryConst.INDUSTRY_ORG.ICT.getValue().equals(industryOrg)) {
                if (lv1ProdCode.contains("_LV1")) {
                    lv1DimensionSet.add(lv1ProdCode.split("_")[0]);
                }
            } else if (IndustryConst.INDUSTRY_ORG.ENERGY.getValue().equals(industryOrg)) {
                if (lv1ProdCode.contains("_L1_E")) {
                    lv1DimensionSet.add(lv1ProdCode.split("_")[0]);
                }
            } else {
                if (lv1ProdCode.contains("_L1_IAS")) {
                    lv1DimensionSet.add(lv1ProdCode.split("_")[0]);
                }
            }
        }
        if (lv1DimensionSet.size() == 0) {
            lv1DimensionSet.add("NO_PERMISSION");
        }
    }

    private void lv0DimensionSet(Set<String> dimensionValues, Set<String> lv0DimensionSet, String industryOrg) {
        for (String lv0ProdCode : dimensionValues) {
            if (IndustryConst.INDUSTRY_ORG.ICT.getValue().equals(industryOrg)) {
                if (lv0ProdCode.contains("_LV0")) {
                    lv0DimensionSet.add(lv0ProdCode.split("_")[0]);
                }
            } else if (IndustryConst.INDUSTRY_ORG.ENERGY.getValue().equals(industryOrg)) {
                if (lv0ProdCode.contains("_L0_E")) {
                    lv0DimensionSet.add(lv0ProdCode.split("_")[0]);
                }
            } else {
                if (lv0ProdCode.contains("_L0_IAS")) {
                    lv0DimensionSet.add(lv0ProdCode.split("_")[0]);
                }
            }
        }
        if (lv0DimensionSet.size() == 0) {
            lv0DimensionSet.add("NO_PERMISSION");
        }
    }

    /**
     * 获取当前登陆用户的角色对应的数据维度范围
     * 树型结构从iauth下发的维度，可能存在LV1的子没勾选全，所有当前的Lv1就没权限，需要过滤LV1code编码
     *
     * @return DataPermissionsVO 维度对象
     */
    @Override
    public DataPermissionsVO getDimensionList(String costType, String tablePreFix, String industryOrg) {
        DataPermissionsVO dataPermissionsVO = getCurrentRoleDataPermission(industryOrg);
        List<ViewInfoVO> allProdDimensionList = dataDimensionService.getCurrentLv2ProdRndTeamList(costType, tablePreFix);
        Set<String> lv1DimensionSet = new HashSet<>();
        for (String lv1Code : dataPermissionsVO.getLv1DimensionSet()) {
            Set<String> lv2ProdCodeSet = allProdDimensionList.stream()
                    .filter(item -> lv1Code.equals(item.getLv1ProdRndTeamCode()))
                    .map(ViewInfoVO::getLv2ProdRndTeamCode).collect(Collectors.toSet());
            if (dataPermissionsVO.getLv2DimensionSet().size() == 0) {
                lv1DimensionSet.add(lv1Code);
                continue;
            }
            if (dataPermissionsVO.getLv2DimensionSet().containsAll(lv2ProdCodeSet)) {
                lv1DimensionSet.add(lv1Code);
            }
        }
        dataPermissionsVO.setLv1DimensionSet(lv1DimensionSet);
        return dataPermissionsVO;
    }

    @Override
    @JalorOperation(code = "hasRolePermission", desc = "校验另存页是否有查看的权限")
    public Boolean hasRolePermission(DmFocPageInfoVO dmFocPageInfoVO) {
        JSONObject jsonObject = JSONObject.parseObject(dmFocPageInfoVO.getSaveThreshold());
        List<ViewInfoVO> allProdDimensionList = dataDimensionService.getCurrentLv2ProdRndTeamList(jsonObject.get("costType").toString(), dmFocPageInfoVO.getTablePreFix());
        CommonViewVO commonViewVO = new CommonViewVO();
        getCommonViewVO(allProdDimensionList, commonViewVO);
        Set<String> lv0DimensionSet = commonViewVO.getLv0DimensionSet();
        Set<String> lv2DimensionSet = commonViewVO.getLv2DimensionSet();
        Set<String> lv1DimensionSet = commonViewVO.getLv1DimensionSet();
        Boolean permissionFlag = false;
        if (lv0DimensionSet.size() == 0 && lv1DimensionSet.size() == 0 && lv2DimensionSet.size() == 0){
            return true;
        }
        permissionFlag = setPermissionFlag(lv2DimensionSet, lv1DimensionSet, permissionFlag, jsonObject);
        if (permissionFlag == null) {
            return false;
        }
        return permissionFlag;
    }

    @Nullable
    private Boolean setPermissionFlag(Set<String> lv2DimensionSet, Set<String> lv1DimensionSet, Boolean permissionFlag, JSONObject jsonObject) {
        List<String> lv1 = (List<String>) jsonObject.get("lv1");
        if (CollectionUtils.isNotEmpty(lv1)) {
            List<String> lv1Flag = (List<String>)jsonObject.get("lv1Flag");
            if (lv1Flag.contains("no")) {
                return false;
            } else {
                if (lv1DimensionSet.containsAll(lv1)) {
                    permissionFlag = true;
                } else {
                    return false;
                }
            }
        }
        return permissionFlag;
    }

    @Override
    public void setProdRndTeamCode(MonthAnalysisVO monthAnalysisVO) {
        String viewFlag = monthAnalysisVO.getViewFlag();
        String parentLevel = monthAnalysisVO.getParentLevel();
        // 通用和盈利颗粒度(视角2)和量纲颗粒度(视角1,2,3,10):LV0重量级团队设置为null
        if (setProdTeamCodeWithView2(monthAnalysisVO, viewFlag, parentLevel)) {
            return;
        }

        // 通用和盈利颗粒度(视角3)和量纲颗粒度(视角4,5,6,11):LV0重量级团队和lv1重量级团队设置为null
        if (setProdTeamCodeWithView3(monthAnalysisVO, viewFlag, parentLevel)) {
            return;
        }

        // 通用(视角4)和量纲颗粒度(视角7,8,9,12):LV0重量级团队和lv1重量级团队和lv2重量级团队设置为null
        if (setProdTeamCodeWithView4(monthAnalysisVO, viewFlag, parentLevel)) {
            return;
        }
        // ias的特殊viewFlag
        if (setProdTeamCodeWithIasView(monthAnalysisVO, viewFlag, parentLevel)) {
            return;
        }

        // 盈利(视角4,5)：LV0重量级团队和LV1重量级团队设置为null
        if (IndustryIndexEnum.GRANULARITY_TYPE.PROFITS.getValue().equals(monthAnalysisVO.getGranularityType())) {
            if ((IndustryIndexEnum.VIEW_FLAG_P.VIEW4.getValue().equals(viewFlag) || IndustryIndexEnum.VIEW_FLAG_P.VIEW5.getValue().equals(viewFlag))
                    && (GroupLevelEnumP.LV0.getValue().equals(parentLevel) || GroupLevelEnumP.LV1.getValue().equals(parentLevel))) {
                monthAnalysisVO.setProdRndTeamCodeList(null);
            }
        }
    }

    private boolean setProdTeamCodeWithView4(MonthAnalysisVO monthAnalysisVO, String viewFlag, String parentLevel) {
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(monthAnalysisVO.getGranularityType())) {
            boolean viewFlagBoolean = IndustryIndexEnum.VIEW_FLAG_D.VIEW7.getValue().equals(viewFlag) || IndustryIndexEnum.VIEW_FLAG_D.VIEW8.getValue().equals(viewFlag)
                    || IndustryIndexEnum.VIEW_FLAG_D.VIEW9.getValue().equals(viewFlag);
            if (viewFlagBoolean || IndustryIndexEnum.VIEW_FLAG_D.VIEW12.getValue().equals(viewFlag) || IndustryIndexEnum.VIEW_FLAG_D.VIEW13.getValue().equals(viewFlag)) {
                if (GroupLevelEnumD.LV0.getValue().equals(parentLevel) || GroupLevelEnumD.LV1.getValue().equals(parentLevel) || GroupLevelEnumD.LV2.getValue().equals(parentLevel)) {
                    monthAnalysisVO.setProdRndTeamCodeList(null);
                    return true;
                }
            }
        } else {
            if (IndustryIndexEnum.VIEW_FLAG_U.VIEW4.getValue().equals(viewFlag) && (GroupLevelEnumU.LV0.getValue().equals(parentLevel)
                    || GroupLevelEnumU.LV1.getValue().equals(parentLevel) || GroupLevelEnumU.LV2.getValue().equals(parentLevel))) {
                monthAnalysisVO.setProdRndTeamCodeList(null);
                return true;
            }
        }
        return false;
    }

    private boolean setProdTeamCodeWithIasView(MonthAnalysisVO monthAnalysisVO, String viewFlag, String parentLevel) {
        if (Constant.StrEnum.IAS_ORG.getValue().equals(monthAnalysisVO.getIndustryOrg())) {
            boolean flag = GroupLevelEnumU.LV0.getValue().equals(parentLevel) || GroupLevelEnumU.LV1.getValue().equals(parentLevel) || GroupLevelEnumU.LV2.getValue().equals(parentLevel);
            if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(monthAnalysisVO.getGranularityType())) {
                boolean viewFlagCondition = IndustryIndexEnum.VIEW_FLAG_D.VIEW13.getValue().equals(viewFlag);
                if (viewFlagCondition && (flag|| GroupLevelEnumD.LV3.getValue().equals(parentLevel))) {
                    monthAnalysisVO.setProdRndTeamCodeList(null);
                    return true;
                }
            } else {
                // 通用和盈利颗粒度的最长视角，viewFlag =7
                if (IndustryIndexEnum.VIEW_FLAG_U.VIEW8.getValue().equals(viewFlag) && (flag || GroupLevelEnumU.LV3.getValue().equals(parentLevel))) {
                    monthAnalysisVO.setProdRndTeamCodeList(null);
                    return true;
                }
            }
        }
        return false;
    }

    private boolean setProdTeamCodeWithView3(MonthAnalysisVO monthAnalysisVO, String viewFlag, String parentLevel) {
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(monthAnalysisVO.getGranularityType())) {
            if ((IndustryIndexEnum.VIEW_FLAG_D.VIEW4.getValue().equals(viewFlag) || IndustryIndexEnum.VIEW_FLAG_D.VIEW5.getValue().equals(viewFlag)
                    || IndustryIndexEnum.VIEW_FLAG_D.VIEW6.getValue().equals(viewFlag) || IndustryIndexEnum.VIEW_FLAG_D.VIEW11.getValue().equals(viewFlag))) {
                if (GroupLevelEnumD.LV0.getValue().equals(parentLevel) || GroupLevelEnumD.LV1.getValue().equals(parentLevel)) {
                    monthAnalysisVO.setProdRndTeamCodeList(null);
                    return true;
                }
            }
        } else {
            if (IndustryIndexEnum.VIEW_FLAG_U.VIEW3.getValue().equals(viewFlag) && (GroupLevelEnumU.LV0.getValue().equals(parentLevel)
                    || GroupLevelEnumU.LV1.getValue().equals(parentLevel))) {
                monthAnalysisVO.setProdRndTeamCodeList(null);
                return true;
            }
        }
        return false;
    }

    private boolean setProdTeamCodeWithView2(MonthAnalysisVO monthAnalysisVO, String viewFlag, String parentLevel) {
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(monthAnalysisVO.getGranularityType())) {
            Boolean dimesnionViewflag = (IndustryIndexEnum.VIEW_FLAG_D.VIEW1.getValue().equals(viewFlag) || IndustryIndexEnum.VIEW_FLAG_D.VIEW2.getValue().equals(viewFlag)
                    || IndustryIndexEnum.VIEW_FLAG_D.VIEW3.getValue().equals(viewFlag) || IndustryIndexEnum.VIEW_FLAG_D.VIEW10.getValue().equals(viewFlag));
            if (dimesnionViewflag && GroupLevelEnumD.LV0.getValue().equals(parentLevel)) {
                monthAnalysisVO.setProdRndTeamCodeList(null);
                return true;
            }
        } else {
            if (IndustryIndexEnum.VIEW_FLAG_U.VIEW2.getValue().equals(viewFlag) && GroupLevelEnumU.LV0.getValue().equals(parentLevel)) {
                monthAnalysisVO.setProdRndTeamCodeList(null);
                return true;
            }
        }
        return false;
    }

    /**
     * 设置权限返回值
     *
     * @param allProdDimensionList
     * @return CommonViewVO
     */
    public void getCommonViewVO(List<ViewInfoVO> allProdDimensionList, CommonViewVO commonViewVO) {
        DataPermissionsVO currentRoleDataPermission = getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Set<String> lv0DimensionSet = currentRoleDataPermission.getLv0DimensionSet();
        Set<String> lv1DimensionSet = currentRoleDataPermission.getLv1DimensionSet();
        Set<String> lv2DimensionSet = currentRoleDataPermission.getLv2DimensionSet();

        List<String> lv0ProdDimensionList = allProdDimensionList.stream().map(ViewInfoVO::getLv0ProdRndTeamCode).distinct().collect(Collectors.toList());
        List<String> lv1ProdDimensionList = allProdDimensionList.stream().map(ViewInfoVO::getLv1ProdRndTeamCode).distinct().collect(Collectors.toList());
        List<String> lv2ProdDimensionList = allProdDimensionList.stream().map(ViewInfoVO::getLv2ProdRndTeamCode).distinct().collect(Collectors.toList());
        if (lv0DimensionSet.containsAll(lv0ProdDimensionList) && lv1DimensionSet.containsAll(lv1ProdDimensionList)
                && lv2DimensionSet.containsAll(lv2ProdDimensionList)) {
            lv0DimensionSet.clear();
            lv1DimensionSet.clear();
            lv2DimensionSet.clear();
        }
        commonViewVO.setLv0DimensionSet(lv0DimensionSet);
        commonViewVO.setLv1DimensionSet(lv1DimensionSet);
        commonViewVO.setLv2DimensionSet(lv2DimensionSet);
    }

    public void setUserPermission(ReplaceAnalysisVO replaceAnalysisVO) {
        // 数据权限范围
        DataPermissionsVO dimensionList = getDimensionList(IndustryIndexEnum.COST_TYPE.T.getValue(), IndustryConst.TABLE_NAME.ICT_TABLE.getValue(), IndustryConst.INDUSTRY_ORG.ICT.getValue());
        replaceAnalysisVO.setLv0DimensionSet(dimensionList.getLv0DimensionSet());
        replaceAnalysisVO.setLv1DimensionSet(dimensionList.getLv1DimensionSet());
        replaceAnalysisVO.setLv2DimensionSet(dimensionList.getLv2DimensionSet());
    }
}