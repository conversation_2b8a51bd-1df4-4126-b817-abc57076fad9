/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.impl.provider.annual;

import com.huawei.it.fcst.export.IExcelExportDataProvider;
import com.huawei.it.fcst.export.vo.ExcelExportContext;
import com.huawei.it.fcst.industry.price.impl.annual.AnnualAmpPriceService;
import com.huawei.it.fcst.industry.price.vo.annual.AnnualAnalysisVO;
import com.huawei.it.fcst.industry.price.vo.annual.DmFocAnnualAmpVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import javax.inject.Inject;
import javax.inject.Named;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 导出，成本涨跌图
 */
@Named("IExcelExport.AnnualCurrentExportProvider")
public class AnnualCurrentExportDataProvider implements IExcelExportDataProvider {

    @Inject
    private AnnualAmpPriceService annualAmpPriceService;

    private static final Logger LOGGER = LoggerFactory.getLogger(AnnualCurrentExportDataProvider.class);

    @Override
    public List<?> getData(Serializable conditionObject, PageVO pageVO) throws CommonApplicationException, InterruptedException {
        AnnualAnalysisVO annualVO = (AnnualAnalysisVO) conditionObject;
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        BeanUtils.copyProperties(annualVO, annualAnalysisVO);

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        // 对比分析
        annualAmpPriceService.getCurrentAmpList(annualAnalysisVO, dmFocAnnualAmpVOList);
        // 设置无效的涨跌幅提示语
        annualAmpPriceService.setNoEffectiveAmp(dmFocAnnualAmpVOList, annualAnalysisVO, "excel");
        return dmFocAnnualAmpVOList;
    }

    @Override
    public Map<String, Object> getHeader(Serializable conditionObject, Map<String, Object> parameters) {
        ExcelExportContext context = (ExcelExportContext) conditionObject;
        AnnualAnalysisVO annualVO = (AnnualAnalysisVO) context.getConditionObject();

        // 自定义表头
        Map<String, Object> headMap = new HashMap<>();
        headMap.put("displayName", annualVO.getDisplayName());
        headMap.put("name", annualVO.getName());
        headMap.put("overseaFlagCnName", annualVO.getOverseaFlagCnName());
        headMap.put("bgCnName", annualVO.getBgCnName());
        headMap.put("actualMonth", annualVO.getActualMonth());
        headMap.put("regionCnName", annualVO.getRegionCnName());
        headMap.put("repofficeCnName", annualVO.getRepofficeCnName());
        headMap.put("signTopCustCategoryCnName", annualVO.getSignTopCustCategoryCnName());
        headMap.put("signSubsidiaryCustcatgCnName", annualVO.getSignSubsidiaryCustcatgCnName());
        return headMap;
    }
}
