/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.vo.common;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * DmFcstDimInfoVO Class
 *
 * <AUTHOR>
 * @since 2024/7/1
 */
// 此@data注解不可以被替换成getter和setter
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DmFcstPriceDimInfoVO {

    private Long id;

    private Integer num;

    private Integer lv1Num;

    private Integer lv2Num;

    private Integer lv3Num;

    private Integer lv4Num;

    private String bgCode;

    private String bgCnName;

    private String delFlag;

    private String viewFlag;

    private String createdBy;

    private Timestamp creationDate;

    private String lastUpdatedBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp lastUpdateDate;

    private String groupCode;

    private String groupLevel;

    private String groupCnName;

    private String permissionFlag;

    private Long versionId;

    private String spartCode;

    private String spartCnName;

    private String regionCode;

    private String regionCnName;

    private String repofficeCode;

    private String repofficeCnName;

    /**
     *  国内海外标识(N:国内/Y:海外/G:全球)
     */
    private String overseaFlag;

    /**
     *  国内海外标识名称
     */
    private String overseaCnName;

    private String pageType;

    private String useFlag;

    private String statusFlag;

    /**
     * 关联出的pbi层级
     */
    private String associatedFlag;

    private String signTopCustCategoryCode;

    @ApiModelProperty("签约客户_大T系统部名称")
    private String signTopCustCategoryCnName;

    @ApiModelProperty("签约客户_子网系统部名称")
    private String signSubsidiaryCustcatgCnName;

    private String lv0ProdListCode;

    private String lv0ProdListCnName;

    private String lv1ProdListCode;

    private String lv1ProdListCnName;

    private String lv2ProdListCode;

    private String lv2ProdListCnName;

    private String lv3ProdListCode;

    private String lv3ProdListCnName;

    private String lv4ProdListCode;

    private String lv4ProdListCnName;

    private String pageFlag;

    private String prodListCode;

    private String lv0Code;

    private Long customId;

    private String customCnName;

}
