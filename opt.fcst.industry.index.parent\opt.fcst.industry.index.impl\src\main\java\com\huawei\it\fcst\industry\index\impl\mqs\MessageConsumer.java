/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.industry.index.impl.mqs;

import java.io.IOException;
import java.net.UnknownHostException;
import java.util.List;

import com.huawei.his.mqs.client.consumer.ConsumeFromWhere;
import com.huawei.his.mqs.client.consumer.ConsumeStatus;
import com.huawei.his.mqs.client.consumer.Consumer;
import com.huawei.his.mqs.client.consumer.MessageModel;
import com.huawei.his.mqs.client.listener.MessageListener;
import com.huawei.his.mqs.common.exception.UmpException;
import com.huawei.his.mqs.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.huawei.it.fcst.industry.index.impl.dataprocess.TaskExecutorProcessService;
import com.huawei.it.fcst.config.ConfigUtil;
import com.huawei.it.fcst.industry.index.utils.UmpClient;
import com.huawei.it.fcst.industry.index.vo.ciphertext.CipherTextDataVO;
import com.huawei.it.soa.util.SoaAppTokenClientUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023年07月03日
 */
@Component
@Slf4j
public class MessageConsumer {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    @Autowired
    private TaskExecutorProcessService taskExecutorProcessService;

    /**
     * 初始化消费者
     *
     * @param listener 消息监听
     * @throws UmpException         异常
     * @throws UnknownHostException 异常
     */
    private void startConsumer(MessageListener listener) throws UmpException, UnknownHostException {
        Consumer consumer = UmpClient.INSTANCE.getConsumer();
        // 设置统一消息平台的服务器地址(dev环境)
        consumer.setUmpNamesrvUrls(ConfigUtil.getInstance().getTopicUrl());
        // 设置客户端账号
        consumer.setAppId(SoaAppTokenClientUtil.getSoaAppId());
        // 设置客户端密钥
        consumer.setAppSecret(SoaAppTokenClientUtil.getSoaCredential());
        // 设置Topic Name
        consumer.setTopic(ConfigUtil.getInstance().getTopName());
        // 设置订阅消息的标签，可以指定消费某一类型的消息，默认*表示消费所有类型的消息
        consumer.setTags("*");
        // 设置是否需要加密传输
        consumer.setEncryptTransport(false);
        // 设置集群消费
        consumer.setMessageModel(MessageModel.CLUSTERING);
        // 每次都从最新的开始消费
        consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
        // 设置消费组为主机名
        consumer.setSubGroup("index_industry");
        // 设置以组消费
        consumer.setGroupWithTags(true);

        consumer.subscribe(listener);

        consumer.start();
    }

    /**
     * 消息监听
     *
     * @return MessageListener
     */
    private MessageListener buildMsgHandler() {
        return new MessageListener() {
            /**
             * 消息客户端
             */
            public ConsumeStatus consume(Message message) {
                try {
                    consumeMessage(message);
                } catch (Exception e) {
                    log.error("consumer msg error .", e);
                }
                return ConsumeStatus.CONSUME_SUCCESS;
            }
        };
    }

    /**
     * 执行消息
     *
     * @param message 指令
     */
    public void consumeMessage(Message message) throws IOException {
        long startTm = System.currentTimeMillis();
        TypeReference<List<CipherTextDataVO>> type = new ListCellTypeRef();
        final List<CipherTextDataVO> objects = OBJECT_MAPPER.readValue(message.getBody(), type);
        taskExecutorProcessService.process(objects,message.getTags());
        long useTm = System.currentTimeMillis() - startTm;
        log.info("Consume Message Use time is [{}]: {} . ", message.getTags(), useTm);
    }

    /**
     * 应用启动的时候启动消费者
     *
     * @param arg0 参数
     * @throws Exception io异常
     */
    public void run(String... arg0) throws Exception {
        startConsumer(buildMsgHandler());
    }

    /**
     * 应用停止的时候，停止消费者
     *
     * @throws UmpException 异常
     */
    public void stopConsumer() throws UmpException {
        Consumer consumer = UmpClient.INSTANCE.getConsumer();
        if (consumer != null && consumer.isStarted()) {
            consumer.shutdown();
        }
    }

    private static final class ListCellTypeRef extends TypeReference<List<CipherTextDataVO>> {
    }
}
