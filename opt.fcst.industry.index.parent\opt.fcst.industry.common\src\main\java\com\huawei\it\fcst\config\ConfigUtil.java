/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2019. All rights reserved.
 *
 */

package com.huawei.it.fcst.config;

import com.huawei.it.jalor5.core.ioc.Jalor;
import com.huawei.it.soa.util.SoaAppTokenClientUtil;
import com.huawei.it.usf.client.factory.SSLVerifierClientHttpRequestFactory;

import com.alibaba.fastjson.JSONObject;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * 系统properties配置变量绑定
 *
 * <AUTHOR>
 * @since 20230327
 */

@Getter
@Slf4j
@Component("configUtil")
public class ConfigUtil implements CommandLineRunner {
    private static RestTemplate vegaRestTemplate = new RestTemplate(new SSLVerifierClientHttpRequestFactory());

    private static final Object LOCK = new Object();

    private static volatile String plainText;

    private static volatile String plainText16;

    private static ConfigUtil configUtil;

    @Value("${industry.index.kms.keyId}")
    private String keyId;

    @Value("${industry.index.kms.ciphertext}")
    private String ciphertext;

    @Value("${industry.index.kms.env}")
    private String kmsEnv;

    @Value("${kms.getDataKeyDecodeByKeyId.url}")
    private String getDataKeyDecodeByKeyId;

    @Value("${industry.mqs.topic.name}")
    private String topName;

    @Value("${industry.mqs.topic.url}")
    private String topicUrl;

    private ConfigUtil() {
    }

    /**
     * getInstance
     *
     * @return EnvUtil
     */
    public static ConfigUtil getInstance() {
        if (Objects.nonNull(configUtil)) {
            return configUtil;
        }
        synchronized (LOCK) {
            if (configUtil == null) {
                configUtil = Jalor.getContext().getBean("configUtil", ConfigUtil.class);
            }
        }
        return configUtil;
    }

    /**
     * 获取解密完整字符串
     *
     * @return String
     */
    public String getPlainTextValue() {
        if (!StringUtils.isEmpty(plainText)) {
            return plainText;
        }
        synchronized (LOCK) {
            try {
                JSONObject body = new JSONObject();
                body.put("appid", SoaAppTokenClientUtil.getSoaAppId());
                Map<String, String> map = new HashMap();
                map.put("x-hic-info", body.toJSONString());
                map.put("Authorization", SoaAppTokenClientUtil.getBasicTokenByAppCredential());
                map.put("x-hic-env", this.kmsEnv);
                Map<String, Object> param = new HashMap();
                param.put("keyId", this.keyId);
                param.put("dataKeyEncode", this.ciphertext);
                param.put("type", "getDataKeyDecodeByKeyId");
                HttpHeaders httpHeaders = buildHttpHeader(map);
                HttpEntity httpEntity = new HttpEntity(param, httpHeaders);
                ResponseEntity<Map> responseEntity = vegaRestTemplate.postForEntity(getDataKeyDecodeByKeyId, httpEntity,
                        Map.class);
                if (responseEntity.getStatusCode().is2xxSuccessful()) {
                    this.plainText = (String) responseEntity.getBody().get("dataKeyDecode");
                }
                System.setProperty("plainText", plainText);
                return plainText;
            } catch (Exception exception) {
                log.error(exception.getMessage());
            }
        }
        return "";
    }

    /**
     * 前段加密32 位
     *
     * @return 加密颜值
     */
    public String get32PlainText() {
        if (StringUtils.isEmpty(plainText)) {
            getPlainTextValue();
        }
        if (StringUtils.isEmpty(plainText)) {
            return "";
        }
        return Optional.ofNullable(plainText.substring(0, 32)).orElse("");
    }

    /**
     * db 加解密颜值16
     *
     * @return 加密颜值
     */
    public String get16PlainText() {
        if (!StringUtils.isEmpty(plainText16)) {
            return plainText16;
        }
        if (StringUtils.isEmpty(plainText)) {
            getPlainTextValue();
        }
        if (StringUtils.isEmpty(plainText)) {
            return "";
        }
        plainText16 = Optional.ofNullable(plainText.substring(0, 16)).orElse("");
        return plainText16;
    }

    private HttpHeaders buildHttpHeader(Map headerMap) {
        HttpHeaders headers = new HttpHeaders();
        if (headerMap != null) {
            // 遍历请求头map 将参数写入 HttpHeader
            ((Set<Map.Entry>) headerMap.entrySet()).stream()
                    .filter(itm -> Objects.nonNull(itm.getKey()))
                    .forEach(itm -> headers.add(itm.getKey().toString(), safe2String(itm.getValue())));
        }
        return headers;
    }

    private String safe2String(Object value) {
        return value != null ? value.toString() : null;
    }

    @Override
    public void run(String... args) throws Exception {
        getPlainTextValue();
    }
}
