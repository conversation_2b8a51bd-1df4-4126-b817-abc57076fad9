/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.fcst.util;

import java.security.SecureRandom;

/**
 * 随机字符串工具，当前暂时仅服务于FileNameUtil，
 * 当用户传的文件VO对象因为某些原因，文件名为空的时候，
 * 生成一个字母开头的长度为16的随机字符串作为文件名。
 *
 * <AUTHOR>
 * @since 2025-06-23 18:12
 */
public class RandomStringGenerator {
    // 包含大小写字母和数字的字符池
    private static final String CHAR_POOL = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    private static final SecureRandom random = new SecureRandom();

    private static final int DEFAULT_LENGTH = 16;

    public static String generateFileName() {
        return generateFileName(DEFAULT_LENGTH);
    }

    public static String generateFileName(int strSize) {
        StringBuilder sb = new StringBuilder(strSize);
        // 首位为字母
        int randomIndex = random.nextInt(CHAR_POOL.length() - 10);
        sb.append(CHAR_POOL.charAt(randomIndex));
        for (int i = 1; i < strSize; i++) {
            // 从字符池中随机选取字符
            randomIndex = random.nextInt(CHAR_POOL.length());
            sb.append(CHAR_POOL.charAt(randomIndex));
        }
        return sb.toString();
    }
}
