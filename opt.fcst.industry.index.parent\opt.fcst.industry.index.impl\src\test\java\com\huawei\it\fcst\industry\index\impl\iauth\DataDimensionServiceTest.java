/*
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.iauth;

import com.huawei.it.fcst.industry.index.dao.IProdGroupsViewDao;
import com.huawei.it.fcst.industry.index.vo.view.ViewInfoVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.security.UserVO;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/10
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class DataDimensionServiceTest {

    @InjectMocks
    private DataDimensionService dataDimensionService;

    @Mock
    private IProdGroupsViewDao prodGroupsViewDao;

    @Before
    public void before() throws Exception {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);
    }

    @Test
    public void getDimensionWithTree() throws CommonApplicationException {
        List<String> parameter = new ArrayList<>();
        Assertions.assertNotNull(dataDimensionService.getDimensionWithTree(parameter));
    }

    @Test
    public void getDimensionWithTree2t() throws CommonApplicationException {
        List<String> parameter = new ArrayList<>();
        parameter.add("param");
        Assertions.assertNotNull(dataDimensionService.getDimensionWithTree(parameter));
    }


    @Test
    public void getCurrentLv2ProdRndTeamList(){
        Assertions.assertNotNull(dataDimensionService.getCurrentLv2ProdRndTeamList("P","dm_foc"));
    }

    @Test
    public void getCurrentLv2ProdRndTeamList2T(){
        Assertions.assertNotNull(dataDimensionService.getCurrentLv2ProdRndTeamList("M","dm_foc"));
    }

    @Test
    public void getCurrentLv2ProdRndTeamList3T(){
        Assertions.assertNotNull(dataDimensionService.getCurrentLv2ProdRndTeamList("T","dm_foc"));
    }


    @Test
    public void getDimensionWithTreePage() throws ApplicationException {
        PageVO pageVO = new PageVO();
        ViewInfoVO vo=new ViewInfoVO();
        Assertions.assertNull(dataDimensionService.getDimensionWithTree(vo,pageVO));
    }

}