/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.annual.AnnualAnalysisVO;
import com.huawei.it.fcst.industry.index.vo.annual.DmFocAnnualAmpVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * IAnnualAmpDao
 *
 * <AUTHOR>
 * @since 2023/10/19
 */
public interface ITotalAnnualAmpDao {

    List<DmFocAnnualAmpVO> totalAllIndustryCost(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> distributeCostList(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> distributeCostExcelList(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> totalMultiIndustryCostChart(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> totalMultiIndustryCostChartMutilSelect(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> findTotalGroupCodeOrderByWeight(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> findTotalProdteamCodeOrderByWeight(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> findTotalGranularityTypeOrderByWeight(AnnualAnalysisVO annualAnalysisVO);

    List<String> getTotalAnnualPeriodYear(@Param("tablePreFix") String tablePreFix,@Param("versionId")Long versionId);

    List<DmFocAnnualAmpVO> totalIndustryCostList(AnnualAnalysisVO annualAnalysisVO);


}
