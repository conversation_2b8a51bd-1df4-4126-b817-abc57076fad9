/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.vo.annual;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * DmFocAnnualAmpVO Class
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
// 此@data注解不可以被替换成getter和setter
@Data
@NoArgsConstructor
@ApiModel(value = "年度出参VO")
public class DmFocAnnualAmpVO {

    @ApiModelProperty(value = "id")
    private int id;

    @ApiModelProperty(value = "版本id")
    private Long versionId;

    @ApiModelProperty(value = "l0名称")
    private String lv0ProdRndTeamCnName;

    @ApiModelProperty(value = "l0编码")
    private String lv0ProdRndTeamCode;

    @ApiModelProperty(value = "spart编码")
    private String spartCode;

    @ApiModelProperty(value = "spart名称")
    private String spartCnName;

    @ApiModelProperty(value = "年份")
    private String periodYear;

    @ApiModelProperty(value = "重量级团队编码")
    private String prodRndTeamCode;

    @ApiModelProperty(value = "重量级团队名称")
    private String prodRndTeamCnName;

    @ApiModelProperty(value = "l2名称")
    private String lv2ProdRndTeamCnName;

    @ApiModelProperty(value = "l2编码")
    private String lv2ProdRndTeamCode;

    @ApiModelProperty(value = "l1名称")
    private String lv1ProdRndTeamCnName;

    @ApiModelProperty(value = "l1编码")
    private String lv1ProdRndTeamCode;

    @ApiModelProperty(value = "l4名称")
    private String lv4ProdRndTeamCnName;

    @ApiModelProperty(value = "l4编码")
    private String lv4ProdRndTeamCode;

    @ApiModelProperty(value = "l3名称")
    private String lv3ProdRndTeamCnName;

    @ApiModelProperty(value = "l3编码")
    private String lv3ProdRndTeamCode;

    @ApiModelProperty(value = "编码")
    private String groupCode;

    @ApiModelProperty(value = "名称")
    private String groupCnName;

    @ApiModelProperty(value = "层级")
    private String groupLevel;

    @ApiModelProperty(value = "父编码")
    private String parentCode;

    @ApiModelProperty(value = "父名称")
    private String parentCnName;

    @ApiModelProperty(value = "父层级")
    private String parentLevel;

    @ApiModelProperty(value = "涨跌幅")
    private String annualAmp;

    @ApiModelProperty(value = "标识")
    private String delFlag;

    @ApiModelProperty(value = "视角")
    private String viewFlag;

    @ApiModelProperty(value = "补齐标识")
    private String appendFlag;

    @ApiModelProperty(value = "权重")
    private String weightRate;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private Timestamp creationDate;

    @ApiModelProperty(value = "更新人")
    private String lastUpdatedBy;

    @ApiModelProperty(value = "更新时间")
    private Timestamp lastUpdateDdate;

    // 提示信息
    @ApiModelProperty(value = "提示信息")
    private String hoverMsg;

    // 哪年补齐
    @ApiModelProperty(value = "补齐年份")
    private String appendYear;


    // 组合 customName
    @ApiModelProperty(value = "虚化名称")
    private String customCnName;

    //  组合 customCode
    @ApiModelProperty(value = "虚化id")
    private Long customId;

    @ApiModelProperty(value = "权重*涨跌")
    private String weightAnnualAmpPercent;

    @ApiModelProperty(value = "状态码")
    private String statusCode;
}
