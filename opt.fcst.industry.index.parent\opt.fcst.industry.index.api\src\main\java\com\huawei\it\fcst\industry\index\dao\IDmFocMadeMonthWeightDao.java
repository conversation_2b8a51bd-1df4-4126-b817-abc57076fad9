/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.month.DmFocMonthWeightVO;
import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * The DAO to access DmFocMonthWeightT entity
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2023-03-10 10:44:46
 */
public interface IDmFocMadeMonthWeightDao {

    /**
     * 查询当前groupCode层级下所有子项的权重
     *
     * @param searchParamsVO 参数参数
     * @return list
     */
    List<DmFocMonthWeightVO> findMadeWeightList(@Param("searchParamsVO") MonthAnalysisVO searchParamsVO);

    /**
     * 查询当前groupCode层级下所有子项的权重
     *
     * @param searchParamsVO 参数参数
     * @return list
     */
    List<DmFocMonthWeightVO> findMadeNormalWeightList(@Param("searchParamsVO") MonthAnalysisVO searchParamsVO);

    /**
     * 查询当前groupCode层级下所有子项的权重
     *
     * @param searchParamsVO 参数参数
     * @return list
     */
    List<DmFocMonthWeightVO> findMadeCombWeightList(@Param("searchParamsVO") MonthAnalysisVO searchParamsVO);


}
