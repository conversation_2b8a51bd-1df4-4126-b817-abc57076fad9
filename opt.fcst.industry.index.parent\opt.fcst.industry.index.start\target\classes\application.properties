application.appName=industry_service
application.scope=fcst.headquarters
application.dbType=gauss
application.serviceVersion=1.0
application.appId=com.huawei.finance.ai.opt.foi
application.subAppId=fcst_industry_index_service
application.tenantId=business.fcst_industry_index_service
server.servlet.context-path=/fcst/industry/index

#itp Gauss datasource Start
datasource.jdbcDriverClass.1=org.postgresql.Driver
datasource.url.1=*************************************************
datasource.maxIdle.1=15
datasource.minIdle.1=10
datasource.maxTotal.1=100
datasource.maxWaitMillis.1=600000
datasource.removeAbandonedOnBorrow.1=true
datasource.removeAbandonedTimeout.1=120
datasource.timeBetweenEvictionRunsMillis.1=30000
#itp Gauss datasource End

server.port=8003
dev.server.port=60009
cxf.jaxrs.client.headers.accept=text/plain
spring.profiles.active=${spring_profiles_active:dev}
httpClient.sgov.environment=${sgov_environment}


#eureka config
registry.eureka.registration.enabled=true
registry.eureka.serviceUrl.default=${registry_eureka_serviceurl_default}

#usf config
usf.service.app=${application.appId}
usf.service.subApp=${application.subAppId}
usf.service.version=1.0
usf.service.environment=${fox_usf_service_environment}
usf.server.handlers=tracer-server,security-provider
usf.client.handlers=user-setter, security-consumer, loadbalancer-ribbon, tracer-client
#支持添加在配置文件或配置中心
jalor.dispersed-filter.enabled=true
#Start JWT Auth
security.enabled=true
security.jwt.group.alg=HS512
security.jwt.gateway.alg=RS512
jalor.jwtVerify.skipAudienceIssuer=true
jalor.jwtFilter.exclusions=*/servlet/rebuildSession,*/services/jalor/security/*,*/services/jalor/sitemap/*,*.html
security.group.publickey=${security.group.publickey}
security.group.privatekey=${security.group.privatekey}
security.gateway.publickey=${security.gateway.publickey}
security.gateway.privatekey=${security.gateway.privatekey}
security.registration.privatekey=${security.registration.privatekey}
sso.ignore.path=*/servlet/rebuildSession,*/servlet/ischeduler/*,*/servlet/changeRole
#End JWT Auth

#public service alg
authentication.mode=jalor
jalor.stubPrivilegeInitCache.enabled=true
jalor5.web.ssoFilter.exclusions=*\\.js,*\\.jpg,*\\.png,*\\.gif,*\\.bmp,*\\.css,*\\.class,*\\.properties,*/initUserPermission,*/rpcInitUserPermission,*/registryQueryService/*,*/personalized/setting/list,*/helperprocxy/*,*/logService/*,*/findCurrentUserThemeSettings/*,*/tryFindOrCreateUser/*,*/eureka/serverext/*,*/jalor/security/mypermissionquery/*,*/auditLog/createAuditLog,*/publicservices/*,*/jalor/eureka/serverext/list,*/servlet/cache,*/servlet/userCacheClean,*/servlet/health
jalor5.web.cxfServlet.hideServiceListPage=true

#Access Authentication Configuration
jalor.web.xss-filter.enabled=false
jalor.web.resource-filter.enabled=true
jalor.web.requestSecurity-filter.enabled=false
jalor.web.csrf-filter.enabled=false

#spring-actuator
endpoints.refresh.enabled=false
endpoints.enabled=false
endpoints.info.enabled = true

#Spring Cloud Config
ribbon.UseIPAddrForServer=true
endpoints.jmx.enabled=false
spring.data.redis.repositories.enabled=false


idaas.appConfUrl=https://uniportal-beta.huawei.com/pubconf/config/client/v1/sdkconf

#Swagger
cxf.jaxrs.client.classes-scan-packages=com.huawei.it
swagger.enable=true
#KAFKA
kafka.servers=${kafka_servers}
message.topic=${message_topic}

hic.cfgcenter.url=${hic_cfgcenter_url}
hic.region=${hic_region}
httpClient.sgov.credential=${httpClient_sgov_credential}

