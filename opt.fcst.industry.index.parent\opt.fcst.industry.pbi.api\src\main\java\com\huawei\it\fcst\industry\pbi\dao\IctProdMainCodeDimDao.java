/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.dao;

import com.huawei.it.fcst.industry.pbi.vo.config.IctProdMainCodeDimVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * IctProdMainCodeDimDao Interface
 *
 * <AUTHOR>
 * @since 2024-07-05
 */
public interface IctProdMainCodeDimDao {
    /**
     * 查询主力产品主力编码各层级下拉框列表
     *
     * @param prodMainCodeDimVO 查询参数VO
     * @return list
     */
    List<IctProdMainCodeDimVO> findMainCodeDropboxList(IctProdMainCodeDimVO prodMainCodeDimVO);

    /**
     * 查询主力产品主力编码新增/编辑时的各层级下拉框列表
     *
     * @param prodMainCodeDimVO 查询参数VO
     * @return list
     */
    List<IctProdMainCodeDimVO> findMainCodeEditDropboxList(IctProdMainCodeDimVO prodMainCodeDimVO);

    /**
     * 分页查询主力产品主力编码新增/编辑时Spart层级下拉框列表
     *
     * @param prodMainCodeDimVO 查询参数VO
     * @return list
     */
    PagedResult<IctProdMainCodeDimVO> findMainCodeSpartDropboxList(
            @Param("prodMainCodeDimVO") IctProdMainCodeDimVO prodMainCodeDimVO,
            @Param("pageVO") PageVO pageVO);

    /**
     * 查询全量的Spart编码
     *
     * @return List
     */
    List<String> findAllSpartCodeList();

    /**
     * 查询主力产品主力编码替换各层级下拉框列表
     *
     * @param prodMainCodeDimVO 查询参数VO
     * @return list
     */
    PagedResult<IctProdMainCodeDimVO> findMainCodeDimListByPage(
            @Param("prodMainCodeDimVO") IctProdMainCodeDimVO prodMainCodeDimVO,
            @Param("pageVO") PageVO pageVO);

    List<IctProdMainCodeDimVO> findMainCodeDimVOList(@Param("prodMainCodeDimVO") IctProdMainCodeDimVO prodMainCodeDimVO);

    int batchInsertMainCodeDimVOs(List<IctProdMainCodeDimVO> mainCodeDimVOList);

    int deleteMainCodeDimVOsByVersionId(Long versionId);

    /**
     * 写入映射维表
     *  dm_fcst_ict_std_pbi_main_code_dim_t
     *  dm_fcst_ict_psp_pbi_main_code_dim_t
     *
     * @param costType 成本类型 (PSP: PSP	标准成本 STD)
     * @return String
     */
    String callFuncMainCodeDimMapping(@Param("costType") String costType);

}