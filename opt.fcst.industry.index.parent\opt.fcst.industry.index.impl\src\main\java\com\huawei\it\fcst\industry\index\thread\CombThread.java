/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.thread;

import com.huawei.it.fcst.industry.index.enums.GroupLevelAllEnum;
import com.huawei.it.fcst.industry.index.enums.IndustryIndexEnum;
import com.huawei.it.fcst.industry.index.impl.combination.CustomCommonService;
import com.huawei.it.fcst.industry.index.utils.FcstIndexMadeUtil;
import com.huawei.it.fcst.industry.index.utils.FcstIndexUtil;
import com.huawei.it.fcst.industry.index.vo.common.CommonViewVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.CountDownLatch;

public class CombThread implements Runnable {
    private static final Logger LOGGER = LoggerFactory.getLogger(CombThread.class);

    private CommonViewVO commonView;

    private List<DmFocViewInfoVO> allGroupCodeList;

    private CustomCommonService customCommonService;

    private CountDownLatch countDownLatch;

    private IRequestContext requestContext;

    /**
     * 构造方法
     *
     * @param commonView 参数
     * @param allGroupCodeList 结果
     */
    public CombThread(CountDownLatch countDownLatch, CommonViewVO commonView, List<DmFocViewInfoVO> allGroupCodeList, IRequestContext requestContext, CustomCommonService customCommonService) {
        this.commonView = commonView;
        this.allGroupCodeList = allGroupCodeList;
        this.customCommonService = customCommonService;
        this.countDownLatch = countDownLatch;
        this.requestContext = requestContext;
    }

    @Override
    public void run() {
        synchronized (CombThread.class) {
            try {
                RequestContextManager.setCurrent(requestContext);
                String groupLevel;
                // 如果不是最细层级，需要一直找到最细层级为止
                while (!GroupLevelAllEnum.CATEGORY.getValue().equals(commonView.getGroupLevel())&&!GroupLevelAllEnum.MANUFACTURE_OBJECT.getValue().equals(commonView.getGroupLevel())) {
                    if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(commonView.getCostType())) {
                        groupLevel = FcstIndexMadeUtil.getNextGroupLevelByView(commonView.getViewFlag(),
                                commonView.getGroupLevel(), commonView.getGranularityType(),commonView.getIndustryOrg());
                    } else {
                        groupLevel = FcstIndexUtil.getNextGroupLevelByView(commonView.getViewFlag(),
                                commonView.getGroupLevel(), commonView.getGranularityType(),commonView.getIndustryOrg());
                    }
                    commonView.setGroupLevel(groupLevel);
                    customCommonService.getDbListForAllCondition(commonView,allGroupCodeList);
                }
            } catch (Exception exception) {
                countDownLatch.countDown();
                LOGGER.error("error getFoldGroupCodeList :{} ", exception.getLocalizedMessage());
            } finally {
                countDownLatch.countDown();
                RequestContextManager.removeCurrent();
            }
        }
    }
}