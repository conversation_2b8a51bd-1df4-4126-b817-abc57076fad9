/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.impl.month;

import com.huawei.it.fcst.industry.price.dao.IPriceMonthCostIdxDao;
import com.huawei.it.fcst.industry.price.dao.IPriceVarifyTaskDao;
import com.huawei.it.fcst.industry.price.vo.month.PriceMonthAnalysisVO;
import com.huawei.it.fcst.util.ObjectCopyUtil;
import com.huawei.it.fcst.vo.DmFocVarifyTaskVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Inject;
import javax.inject.Named;
import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 定价指数-综合指数分析-异步处理服务
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@Slf4j
@EnableAsync
@Named(value = "asyncPriceMonthService")
public class AsyncPriceMonthService {

    @Inject
    private IPriceVarifyTaskDao varifyTaskDao;

    @Inject
    private IPriceMonthCostIdxDao priceMonthCostIdxDao;

    /**
     * 调用切换基期函数刷新数据
     *
     * @param monthAnalysisVO 页面参数VO
     * @param dmVarifyTaskVO
     */
    @Async("asyncServiceExecutor")
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void refreshDataByFunction(PriceMonthAnalysisVO monthAnalysisVO, DmFocVarifyTaskVO dmVarifyTaskVO) {
        log.info(">>>Begin AsyncPriceMonthService#refreshDataByFunction");
        PriceMonthAnalysisVO paramsVO = ObjectCopyUtil.copy(monthAnalysisVO, PriceMonthAnalysisVO.class);
        // 参数转换，将集合转换成字符串的入参
        paramsVO.setGroupCode(getCodesStr(paramsVO.getGroupCodeList()));
        paramsVO.setParentCode(getCodesStr(paramsVO.getParentCodeList()));
        List<String> customIdList = paramsVO.getCustomIdList().stream().map(String::valueOf).collect(Collectors.toList());
        paramsVO.setCustomIds(getCodesStr(customIdList));
        // 调用切换基期函数刷新数据
        String successFlag = priceMonthCostIdxDao.callFuncRefreshData(paramsVO);
        log.info(">>>AsyncPriceMonthService#refreshDataByFunction and result:{}", successFlag);
        if ("PROCESSING".equals(dmVarifyTaskVO.getStatus())) {
            if ("SUCCESS".equals(successFlag)) {
                dmVarifyTaskVO.setStatus("SUCCESS");
            } else {
                dmVarifyTaskVO.setStatus("FAILED");
            }
        }
        if ("PROCESSING".equals(dmVarifyTaskVO.getCombStatus())) {
            if ("SUCCESS".equals(successFlag)) {
                dmVarifyTaskVO.setCombStatus("SUCCESS");
            } else {
                dmVarifyTaskVO.setCombStatus("FAILED");
            }
        }
        dmVarifyTaskVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        varifyTaskDao.updateVerifyTask(dmVarifyTaskVO);
    }

    @NotNull
    private String getCodesStr(List<String> codeList) {
        if (CollectionUtils.isEmpty(codeList)) {
            return "";
        }
        return codeList.stream().collect(Collectors.joining(","));
    }

}