/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.template;

import com.huawei.it.fcst.export.IExcelTemplateBeanManager;
import com.huawei.it.fcst.export.vo.ExcelTemplateBeanManager;
import com.huawei.it.fcst.export.vo.SheetBeanMetaVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import lombok.Getter;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * ReplaceTemplateEnum Class
 *
 * <AUTHOR>
 * @since 2024/7/11
 */
@Getter
public enum ReplaceTemplateEnum implements IExcelTemplateBeanManager {

    REPLACE_01("01", "ReplaceExportTemplate", "配置管理-ICT-新旧编码替换", "配置管理-新旧编码替换-明细数据") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            ExcelTemplateBeanManager excelTemplateBeanManager = new ExcelTemplateBeanManager();
            List<SheetBeanMetaVO> replacelList = new ArrayList<>();
            replacelList.add(new SheetBeanMetaVO(REPLACE_01.templateName, 0, "ReplaceExportDataProvider", "新旧编码替换", Boolean.FALSE));
            excelTemplateBeanManager.setSheetBeans(replacelList);
            excelTemplateBeanManager.setTemplateName(REPLACE_01.templateName);
            excelTemplateBeanManager.setModuleType(REPLACE_01.moduleType);
            excelTemplateBeanManager.setDesc(REPLACE_01.desc);
            return excelTemplateBeanManager;
        }
    };
    private String code;
    private String templateName;
    private String moduleType;
    private String desc;

    ReplaceTemplateEnum(String code, String templateName, String moduleType, String desc) {
        this.code = code;
        this.templateName = templateName;
        this.moduleType = moduleType;
        this.desc = desc;
    }
    static final String REPLACE_PAGE = "REPLACE";
    static final String SUB_LEVEL_CODE = "{0}_{1}";

    /**
     * 获取模板信息
     *
     * @param levelCode 层级code
     * @param roleName 角色名称
     * @return 枚举信息
     * @throws CommonApplicationException
     */
    public static ReplaceTemplateEnum getByCode(String levelCode, String roleName) throws CommonApplicationException {
        String key = MessageFormat.format(SUB_LEVEL_CODE, REPLACE_PAGE, levelCode);
        for (ReplaceTemplateEnum value : ReplaceTemplateEnum.values()) {
            if (value.name().equalsIgnoreCase(key)) {
                return value;
            }
        }
        throw new CommonApplicationException("Check the replace template definition relationship.");
    }
}
