/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.utils;

import com.huawei.it.fcst.industry.index.constant.Constant;

import com.huawei.it.jalor5.core.util.StreamUtil;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.Date;

import javax.servlet.http.HttpServletResponse;

/**
 * 导入导出工具类
 *
 * <AUTHOR>
 * @since 2022-10-27
 */
public class ExcelExportUtil<T> {

    // 写入数据的起始行
    private int rowIndex;

    // 写入样式的起始行
    private int styleIndex;

    // 对象字节码
    private Class clazz;

    // 对象中的所有属性
    private Field[] fields;

    public int getRowIndex() {
        return rowIndex;
    }

    public void setRowIndex(int rowIndex) {
        this.rowIndex = rowIndex;
    }

    public int getStyleIndex() {
        return styleIndex;
    }

    public void setStyleIndex(int styleIndex) {
        this.styleIndex = styleIndex;
    }

    public Class getClazz() {
        return clazz;
    }

    public void setClazz(Class clazz) {
        this.clazz = clazz;
    }

    public Field[] getFields() {
        return fields;
    }

    public void setFields(Field[] fields) {
        this.fields = fields;
    }

    public ExcelExportUtil() {
    }

    public ExcelExportUtil(int rowIndex, int styleIndex, Class clazz) {
        this.rowIndex = rowIndex;
        this.styleIndex = styleIndex;
        this.clazz = clazz;
        this.fields = clazz.getDeclaredFields();
    }

    /**
     * 下载Excel到浏览器
     *
     * @param workbook 工作簿
     * @param fileName 导出文件名
     * @param httpServletResponse 响应
     * @throws Exception
     */
    public static void downloadExcel(Workbook workbook, String fileName, HttpServletResponse httpServletResponse)
        throws Exception {
        fileName = URLEncoder.encode(fileName, "UTF-8");
        httpServletResponse.setContentType("application/vnd.ms-excel;charset=UTF-8");
        httpServletResponse.setHeader("content-disposition", "attachment;filename=" + new String(fileName.getBytes("ISO8859-1"), "UTF-8"));
        httpServletResponse.setHeader("filename", fileName);
        workbook.write(httpServletResponse.getOutputStream());
    }

    /**
     * 根据Excel导出模板创建工作簿
     *
     * @param templatePath 导出模板路径
     * @return Workbook 工作簿
     * @throws Exception
     */
    public static XSSFWorkbook getWorkbookByTemplate(String templatePath) throws Exception {
        // 1、加载导出模板
        InputStream inputStream = null;
        XSSFWorkbook xssfWorkbook = null;
        try {
            Resource resource = new ClassPathResource(templatePath);
            inputStream = resource.getInputStream();
            xssfWorkbook = new XSSFWorkbook(inputStream);
        } catch (IOException e) {
            throw new RuntimeException("创建workbook失败");
        } finally {
            StreamUtil.closeStreams(inputStream);
        }
        // 2、根据模板创建工作簿
        return xssfWorkbook;
    }

    /**
     * 获取当前时间的字符串格式
     *
     * @return String
     */
    public static String getDateTime() {
        return DateFormatUtils.format(new Date(), Constant.StrEnum.DATETIME_SS.getValue());
    }


}