<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IDmFocManufactureReviewDao">
    <resultMap type="com.huawei.it.fcst.industry.index.vo.config.ManufactureBottomVO" id="resultMap">
        <result property="versionId" column="version_id"/>
        <result property="shippingObjectCode" column="shipping_object_code"/>
        <result property="shippingObjectCnName" column="shipping_object_cn_name"/>
        <result property="manufactureObjectCode" column="manufacture_object_code"/>
        <result property="manufactureObjectCnName" column="manufacture_object_cn_name"/>
        <result property="itemCode" column="item_code"/>
        <result property="itemCnName" column="item_cn_name"/>
        <result property="startPeriod" column="start_period"/>
        <result property="endPeriod" column="end_period"/>
        <result property="modifyReason" column="modify_reason"/>
        <result property="modifyType" column="modify_type"/>
        <result property="impactQty" column="impact_qty"/>
        <result property="crmAvgCnt" column="rmb_avg_cnt"/>
        <result property="caliberFlag" column="caliber_flag"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>

    </resultMap>

    <sql id="allFields">
        version_id,
        shipping_object_code,
        shipping_object_cn_name,
        manufacture_object_code,
        manufacture_object_cn_name,
        item_code,
        item_cn_name,
        start_period,
        end_period,
        modify_reason,
        modify_type,
        impact_qty,
        caliber_flag,
        created_by,
        creation_date,
        last_updated_by,
        last_update_date,
        del_flag,
        page_flag
    </sql>

    <sql id="allFieldsByPage">
        t1.version_id,
        t1.shipping_object_code,
        t1.shipping_object_cn_name,
        t1.manufacture_object_code,
        t1.manufacture_object_cn_name,
        t1.item_code,
        t1.item_cn_name,
        t1.start_period,
        t1.end_period,
        t1.modify_reason,
        t1.modify_type,
        t1.impact_qty,
        t1.caliber_flag,
        t1.created_by,
        t1.creation_date,
        t1.last_updated_by,
        t1.last_update_date,
        t1.del_flag
    </sql>

    <sql id="allValues">
        #{versionId,jdbcType=BIGINT},
        #{shippingObjectCode,jdbcType=VARCHAR},
        #{shippingObjectCnName,jdbcType=VARCHAR},
        #{manufactureObjectCode,jdbcType=VARCHAR},
        #{manufactureObjectCnName,jdbcType=VARCHAR},
        #{itemCode,jdbcType=VARCHAR},
        #{itemCnName,jdbcType=VARCHAR},
        #{startPeriod,jdbcType=BIGINT},
        #{endPeriod,jdbcType=BIGINT},
        #{modifyReason,jdbcType=VARCHAR},
        #{modifyType,jdbcType=VARCHAR},
        #{impactQty,jdbcType=BIGINT},
        #{caliberFlag,jdbcType=VARCHAR},
        #{createdBy,jdbcType=VARCHAR},
        #{creationDate,jdbcType=TIMESTAMP},
        #{lastUpdatedBy,jdbcType=VARCHAR},
        #{lastUpdateDate,jdbcType=TIMESTAMP},
        #{delFlag,jdbcType=VARCHAR}
    </sql>

    <sql id="uniqueKeyField">
        "version_id"=#{versionId,jdbcType=BIGINT}
    </sql>

    <select id="findBaseDropDown" resultMap="resultMap">
        SELECT DISTINCT
        <if test='shippingObjectCode != null and shippingObjectCode !=""'>
            manufacture_object_code,manufacture_object_cn_name,
        </if>
        <if test='manufactureObjectCode != null and manufactureObjectCode !=""'>
            item_code,item_cn_name,
        </if>
        shipping_object_code,shipping_object_cn_name
        FROM fin_dm_opt_foi.${tablePreFix}_made_data_review_info_t
        WHERE
        del_flag = 'N'
        <if test='versionId != null'>
            and version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='caliberFlag != null'>
            AND caliber_flag=#{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='pageFlag != null and pageFlag!=""'>
            AND (page_flag=#{pageFlag,jdbcType=VARCHAR} or page_flag is null)
        </if>
        <if test='shippingObjectCode != null and shippingObjectCode!=""'>
            AND shipping_object_code=#{shippingObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='manufactureObjectCode != null and manufactureObjectCode!=""'>
            AND manufacture_object_code=#{manufactureObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='itemCode != null and itemCode!=""'>
            AND item_code=#{itemCode,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="findBaseReviewList" resultType="java.util.Map">
        SELECT
        n1.*,
        n2.VERSION
        FROM (SELECT
        "version_id",
        "shipping_object_code",
        "shipping_object_cn_name",
        "manufacture_object_code",
        "manufacture_object_cn_name",
        "item_code",
        "item_cn_name",
        "start_period",
        "end_period",
        "modify_reason",
        "modify_type",
        (CASE WHEN modify_type = 'INSERT' THEN modify_reason END)  as modify_reason_i,
        (CASE WHEN modify_type = 'MODIFY' THEN modify_reason END)  as modify_reason_m,
        (CASE WHEN modify_type = 'REVOKE' THEN modify_reason END)  as modify_reason_r,
        "impact_qty",
        "caliber_flag",
        "last_updated_by",
        "last_update_date"
        FROM fin_dm_opt_foi.${tablePreFix}_made_data_review_info_t
        WHERE
        del_flag = 'N'
        <if test='versionId != null'>
            AND version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='caliberFlag != null'>
            AND caliber_flag=#{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='pageFlag != null and pageFlag!=""'>
            AND (page_flag=#{pageFlag,jdbcType=VARCHAR} or page_flag is null)
        </if>
        <if test='shippingObjectCode != null and shippingObjectCode!=""'>
            AND shipping_object_code=#{shippingObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='manufactureObjectCode != null and manufactureObjectCode!=""'>
            AND manufacture_object_code=#{manufactureObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='itemCode != null and itemCode!=""'>
            AND item_code=#{itemCode,jdbcType=VARCHAR}
        </if>
        <choose>
            <when test='pageFlag!= null and pageFlag == "abnormal"'>
                AND modify_type !='REVOKE'
            </when>
            <otherwise>
            </otherwise>
        </choose>
        )n1
        LEFT JOIN (
        SELECT version_id, version
        FROM fin_dm_opt_foi.${tablePreFix}_version_info_t
        WHERE del_flag = 'N'
        <if test='dataType != null and dataType!=""'>
            AND data_type=#{dataType,jdbcType=VARCHAR}
        </if>) n2
        ON n1.version_id = n2.version_id
        ORDER BY version
    </select>

    <select id="findMadeReviewVOList" resultMap="resultMap">
        SELECT
        <include refid="allFields"/>
        FROM fin_dm_opt_foi.${tablePreFix}_made_data_review_info_t
        WHERE
        del_flag = 'N' and (page_flag ='abnormal' or page_flag is null)
        <if test='versionId != null'>
            and version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='caliberFlag != null'>
            AND caliber_flag=#{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='shippingObjectCode != null and shippingObjectCode!=""'>
            AND shipping_object_code=#{shippingObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='manufactureObjectCode != null and manufactureObjectCode!=""'>
            AND manufacture_object_code=#{manufactureObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='itemCode != null and itemCode!=""'>
            AND item_code=#{itemCode,jdbcType=VARCHAR}
        </if>
    </select>

    <insert id="createMadeReviewList" parameterType="java.util.List">
        INSERT INTO fin_dm_opt_foi.${tablePreFix}_made_data_review_info_t
        (<include refid="allFields"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.versionId,jdbcType=NUMERIC},
            #{item.shippingObjectCode,jdbcType=VARCHAR},
            #{item.shippingObjectCnName,jdbcType=VARCHAR},
            #{item.manufactureObjectCode,jdbcType=VARCHAR},
            #{item.manufactureObjectCnName,jdbcType=VARCHAR},
            #{item.itemCode,jdbcType=VARCHAR},
            #{item.itemCnName,jdbcType=VARCHAR},
            #{item.startPeriod,jdbcType=BIGINT},
            #{item.endPeriod,jdbcType=BIGINT},
            #{item.modifyReason,jdbcType=VARCHAR},
            #{item.modifyType,jdbcType=VARCHAR},
            #{item.impactQty,jdbcType=BIGINT},
            #{item.caliberFlag,jdbcType=VARCHAR},
            #{item.createdBy,jdbcType=VARCHAR},
            #{item.creationDate,jdbcType=TIMESTAMP},
            #{item.lastUpdatedBy,jdbcType=VARCHAR},
            #{item.lastUpdateDate,jdbcType=TIMESTAMP},
            #{item.delFlag,jdbcType=VARCHAR},
            #{item.pageFlag,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <select id="getMadeItemDropDown" resultMap="resultMap">
        SELECT DISTINCT
        <if test='shippingObjectCode != null and shippingObjectCode !=""'>
            manufacture_object_code,manufacture_object_cn_name,
        </if>
        <if test='manufactureObjectCode != null and manufactureObjectCode !=""'>
            item_code,item_cn_name,
        </if>
        shipping_object_code,shipping_object_cn_name
        FROM
        fin_dm_opt_foi.${tablePreFix}_made_view_info_d
        WHERE
        del_flag = 'N'
        AND group_level = 'ITEM'
        <if test='shippingObjectCode != null and shippingObjectCode!=""'>
            AND shipping_object_code=#{shippingObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='manufactureObjectCode != null and manufactureObjectCode!=""'>
            AND manufacture_object_code=#{manufactureObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='caliberFlag != null and caliberFlag!=""'>
            AND caliber_flag=#{caliberFlag,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getMadeImpactQty" resultMap="resultMap">
        SELECT
            COUNT(1) OVER() AS CNT
            ,COUNT(1) OVER(
            PARTITION BY RMB_AVG_AMT ORDER BY RMB_AVG_AMT DESC) AS RMB_AVG_CNT
        FROM(
                SELECT
                    SUM(RMB_COST_AMT)/SUM(SHIP_QUANTITY) AS RMB_AVG_AMT
                FROM fin_dm_opt_foi.${tablePreFix}_made_mid_month_item_t
                WHERE
        <if test='itemCode != null'>
            item_code=#{itemCode,jdbcType=VARCHAR}
        </if>
        <if test='startPeriod != null and endPeriod != null'>
            AND period_id
            BETWEEN #{startPeriod,jdbcType=BIGINT} AND #{endPeriod,jdbcType=BIGINT}
        </if>
        <if test='caliberFlag != null'>
            AND caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
                GROUP BY LV0_PROD_LIST_CODE
                       ,OVERSEA_FLAG
                       ,LV0_PROD_RND_TEAM_CODE
                       ,LV1_PROD_RND_TEAM_CODE
                       ,LV2_PROD_RND_TEAM_CODE
                       ,LV3_PROD_RND_TEAM_CODE

                UNION ALL
                SELECT
                    SUM(RMB_COST_AMT)/SUM(SHIP_QUANTITY) AS RMB_AVG_AMT
                FROM fin_dm_opt_foi.${tablePreFix}_made_pft_mid_month_item_t
                WHERE
        <if test='itemCode != null'>
            item_code=#{itemCode,jdbcType=VARCHAR}
        </if>
        <if test='startPeriod != null and endPeriod != null'>
            AND period_id
            BETWEEN #{startPeriod,jdbcType=BIGINT} AND #{endPeriod,jdbcType=BIGINT}
        </if>
        <if test='caliberFlag != null'>
            AND caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
                GROUP BY LV0_PROD_LIST_CODE
                       ,OVERSEA_FLAG
                       ,LV0_PROD_RND_TEAM_CODE
                       ,LV1_PROD_RND_TEAM_CODE
                       ,LV2_PROD_RND_TEAM_CODE
                       ,LV3_PROD_RND_TEAM_CODE
                       ,L1_NAME
                       ,L2_NAME

                UNION ALL
                SELECT
                    SUM(RMB_COST_AMT)/SUM(SHIP_QUANTITY) AS RMB_AVG_AMT
                FROM fin_dm_opt_foi.${tablePreFix}_made_dms_mid_month_item_t
                WHERE
        <if test='itemCode != null'>
            item_code=#{itemCode,jdbcType=VARCHAR}
        </if>
        <if test='startPeriod != null and endPeriod != null'>
            AND period_id
            BETWEEN #{startPeriod,jdbcType=BIGINT} AND #{endPeriod,jdbcType=BIGINT}
        </if>
        <if test='caliberFlag != null'>
            AND caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
                GROUP BY LV0_PROD_LIST_CODE
                       ,OVERSEA_FLAG
                       ,LV0_PROD_RND_TEAM_CODE
                       ,LV1_PROD_RND_TEAM_CODE
                       ,LV2_PROD_RND_TEAM_CODE
                       ,LV3_PROD_RND_TEAM_CODE
                       ,DIMENSION_CODE
                       ,DIMENSION_SUBCATEGORY_CODE
                       ,DIMENSION_SUB_DETAIL_CODE
            ) LIMIT 1;
    </select>

    <sql id="setValues">
        <if test='versionId != null'>
            "version_id"=#{versionId,jdbcType=BIGINT},
        </if>
        <if test='shippingObjectCode != null'>
            "shipping_object_code"=#{shippingObjectCode,jdbcType=VARCHAR},
        </if>
        <if test='shippingObjectCnName != null'>
            "shipping_object_cn_name"=#{shippingObjectCnName,jdbcType=VARCHAR},
        </if>
        <if test='manufactureObjectCode != null'>
            "manufacture_object_code"=#{manufactureObjectCode,jdbcType=VARCHAR},
        </if>
        <if test='manufactureObjectCnName != null'>
            "manufacture_object_cn_name"=#{manufactureObjectCnName,jdbcType=VARCHAR},
        </if>
        <if test='itemCode != null'>
            "item_code"=#{itemCode,jdbcType=VARCHAR},
        </if>
        <if test='itemCnName != null'>
            "item_cn_name"=#{itemCnName,jdbcType=VARCHAR},
        </if>
        <if test='startPeriod != null'>
            "start_period"=#{startPeriod,jdbcType=BIGINT},
        </if>
        <if test='endPeriod != null'>
            "end_period"=#{endPeriod,jdbcType=BIGINT},
        </if>
        <if test='modifyReason != null'>
            "modify_reason"=#{modifyReason,jdbcType=VARCHAR},
        </if>
        <if test='modifyType != null'>
            "modify_type"=#{modifyType,jdbcType=VARCHAR},
        </if>
        <if test='impactQty != null'>
            "impact_qty"=#{impactQty,jdbcType=BIGINT},
        </if>
        <if test='caliberFlag != null'>
            "caliber_flag"=#{caliberFlag,jdbcType=VARCHAR},
        </if>
        <if test='createdBy != null'>
            "created_by"=#{createdBy,jdbcType=VARCHAR},
        </if>
        <if test='creationDate != null'>
            "creation_date"=#{creationDate,jdbcType=TIMESTAMP},
        </if>
        <if test='lastUpdatedBy != null'>
            "last_updated_by"=#{lastUpdatedBy,jdbcType=VARCHAR},
        </if>
        <if test='lastUpdateDate != null'>
            "last_update_date"=#{lastUpdateDate,jdbcType=TIMESTAMP},
        </if>
        <if test='delFlag != null'>
            "del_flag"=#{delFlag,jdbcType=VARCHAR}
        </if>

    </sql>

    <sql id="searchFields">
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='manufactureBottomVO.versionId != null and manufactureBottomVO.versionId != 0'>
                AND t1.version_id=#{manufactureBottomVO.versionId,jdbcType=BIGINT}
            </if>
            <if test='manufactureBottomVO.pageFlag != null and manufactureBottomVO.pageFlag != ""'>
                AND (t1.page_flag=#{manufactureBottomVO.pageFlag} or t1.page_flag is null)
            </if>
            <if test='manufactureBottomVO.shippingObjectCode != null and manufactureBottomVO.shippingObjectCode != ""'>
                AND t1.shipping_object_code=#{manufactureBottomVO.shippingObjectCode,jdbcType=VARCHAR}
            </if>
            <if test='manufactureBottomVO.shippingObjectCnName != null and manufactureBottomVO.shippingObjectCnName != ""'>
                AND t1.shipping_object_cn_name=#{manufactureBottomVO.shippingObjectCnName,jdbcType=VARCHAR}
            </if>
            <if test='manufactureBottomVO.manufactureObjectCode != null and manufactureBottomVO.manufactureObjectCode != ""'>
                AND t1.manufacture_object_code=#{manufactureBottomVO.manufactureObjectCode,jdbcType=VARCHAR}
            </if>
            <if test='manufactureBottomVO.manufactureObjectCnName != null and manufactureBottomVO.manufactureObjectCnName != ""'>
                AND t1.manufacture_object_cn_name=#{manufactureBottomVO.manufactureObjectCnName,jdbcType=VARCHAR}
            </if>
            <if test='manufactureBottomVO.itemCode != null and manufactureBottomVO.itemCode != ""'>
                AND t1.item_code=#{manufactureBottomVO.itemCode,jdbcType=VARCHAR}
            </if>
            <if test='manufactureBottomVO.itemCnName != null and manufactureBottomVO.itemCnName != ""'>
                AND t1.item_cn_name=#{manufactureBottomVO.itemCnName,jdbcType=VARCHAR}
            </if>
            <if test='manufactureBottomVO.startPeriod != null'>
                AND t1.start_period=#{manufactureBottomVO.startPeriod,jdbcType=BIGINT}
            </if>
            <if test='manufactureBottomVO.endPeriod != null'>
                AND t1.end_period=#{manufactureBottomVO.endPeriod,jdbcType=BIGINT}
            </if>
            <if test='manufactureBottomVO.modifyReason != null and manufactureBottomVO.modifyReason != ""'>
                AND t1.modify_reason=#{manufactureBottomVO.modifyReason,jdbcType=VARCHAR}
            </if>
            <if test='manufactureBottomVO.modifyType != null and manufactureBottomVO.modifyType != ""'>
                AND t1.modify_type=#{manufactureBottomVO.modifyType,jdbcType=VARCHAR}
            </if>
            <if test='manufactureBottomVO.impactQty != null'>
                AND t1.impact_qty=#{manufactureBottomVO.impactQty,jdbcType=BIGINT}
            </if>
            <if test='manufactureBottomVO.caliberFlag != null and manufactureBottomVO.caliberFlag != ""'>
                AND t1.caliber_flag=#{manufactureBottomVO.caliberFlag,jdbcType=VARCHAR}
            </if>
            <if test='manufactureBottomVO.createdBy != null'>
                AND t1.created_by=#{manufactureBottomVO.createdBy,jdbcType=VARCHAR}
            </if>
            <if test='manufactureBottomVO.creationDate != null'>
                AND t1.creation_date=#{manufactureBottomVO.creationDate,jdbcType=TIMESTAMP}
            </if>
            <if test='manufactureBottomVO.lastUpdatedBy != null'>
                AND t1.last_updated_by=#{manufactureBottomVO.lastUpdatedBy,jdbcType=VARCHAR}
            </if>
            <if test='manufactureBottomVO.lastUpdateDate != null'>
                AND t1.last_update_date=#{manufactureBottomVO.lastUpdateDate,jdbcType=TIMESTAMP}
            </if>
            <if test='manufactureBottomVO.delFlag != null and manufactureBottomVO.delFlag != ""'>
                AND t1.del_flag=#{manufactureBottomVO.delFlag,jdbcType=VARCHAR}
            </if>
        </trim>

    </sql>

    <select id="findByPage" resultMap="resultMap">
        SELECT n1.version,
        <include refid="allFieldsByPage"/>
        FROM fin_dm_opt_foi.${manufactureBottomVO.tablePreFix}_made_data_review_info_t t1
        left join
        fin_dm_opt_foi.${manufactureBottomVO.tablePreFix}_version_info_t n1
        on t1.version_id = n1.version_id
        <include refid="searchFields"/>
        <if test='manufactureBottomVO.dataType != null and manufactureBottomVO.dataType != ""'>
            AND n1.data_type=#{manufactureBottomVO.dataType,jdbcType=VARCHAR}
        </if>
        <if test='manufactureBottomVO.pageFlag!= null and manufactureBottomVO.pageFlag == "abnormal"'>
            AND t1.modify_type !='REVOKE'
        </if>
        and n1.del_flag ='N'
        LIMIT #{pageVO.pageSize} OFFSET #{pageVO.mysqlStartIndex}
    </select>

    <select id="findByPageCount" resultType="int">
        SELECT COUNT(1)
        FROM fin_dm_opt_foi.${manufactureBottomVO.tablePreFix}_made_data_review_info_t t1
        <include refid="searchFields"/>
        <if test='manufactureBottomVO.pageFlag!= null and manufactureBottomVO.pageFlag == "abnormal"'>
            AND t1.modify_type !='REVOKE'
        </if>
    </select>

</mapper>
