/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.impl.provider.month;

import com.huawei.it.fcst.export.IExcelExportDataProvider;
import com.huawei.it.fcst.export.vo.ExcelExportContext;
import com.huawei.it.fcst.export.vo.ExportList;
import com.huawei.it.fcst.industry.price.service.month.IPriceMonthAnalysisService;
import com.huawei.it.fcst.industry.price.vo.month.PriceMonthAnalysisVO;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import javax.inject.Inject;
import javax.inject.Named;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 定价指数-同比环比图导出数据提供类
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
@Slf4j
@Named("IExcelExport.PriceYoyAndPopChartExpDataProvider")
public class PriceYoyAndPopChartExpDataProvider implements IExcelExportDataProvider {

    @Inject
    private IPriceMonthAnalysisService monthAnalysisService;

    @Override
    public List<?> getData(Serializable conditionObject, PageVO pageVO) throws ApplicationException {
        log.info(">>>Begin PriceYoyAndPopChartExpDataProvider::getData");
        PriceMonthAnalysisVO monthAnalysisVO = (PriceMonthAnalysisVO)conditionObject;
        PriceMonthAnalysisVO paramsVO = new PriceMonthAnalysisVO();
        BeanUtils.copyProperties(monthAnalysisVO, paramsVO);
        ResultDataVO resultDataVO = monthAnalysisService.getPriceIndexYoyAndPopChart(paramsVO);
        List<PriceMonthAnalysisVO> dataList = (List<PriceMonthAnalysisVO>) resultDataVO.getData();
        dataList = Optional.ofNullable(dataList).orElse(new ArrayList<>());
        ExportList exportList = new ExportList();
        List<PriceMonthAnalysisVO> yoyDataList = dataList.stream()
                .filter(item -> "YOY".equals(item.getRateFlag()))
                .collect(Collectors.toList());
        yoyDataList.stream().forEach(item -> item.setYoyRate(item.getRatePercent()));
        List<PriceMonthAnalysisVO> popDataList = dataList.stream()
                .filter(item -> "POP".equals(item.getRateFlag()))
                .collect(Collectors.toList());
        popDataList.stream().forEach(item -> item.setPopRate(item.getRatePercent()));
        yoyDataList.stream().forEach(yoyData -> {
            for (PriceMonthAnalysisVO popData : popDataList) {
                if (yoyData.getPeriodId().compareTo(popData.getPeriodId()) == 0) {
                    yoyData.setPopRate(popData.getPopRate());
                    break;
                }
            }
        });
        if (monthAnalysisVO.getIsMultipleSelect()) {
            // 设置SPART层级时数据展示的格式
            yoyDataList.stream().forEach(item -> {
                if ("SPART".equals(monthAnalysisVO.getGroupLevel())) {
                    item.setGroupCnName(item.getGroupCode());
                }
            });
        }
        exportList.addAll(yoyDataList);
        exportList.setTotalRows(exportList.size());
        return exportList;
    }

    @Override
    public Map<String, Object> getHeader(Serializable conditionObject, Map<String, Object> parameters) throws ApplicationException {
        ExcelExportContext context = (ExcelExportContext)conditionObject;
        PriceMonthAnalysisVO priceMonthAnalysisVO = (PriceMonthAnalysisVO)context.getConditionObject();
        return monthAnalysisService.getHeaderInfo(priceMonthAnalysisVO);
    }
}