/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.enums;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;


/**
 * <AUTHOR>
 * @since 2023/11/3
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class GroupLevelAllEnumTest {

    @Test
    public void getInstance() {
        GroupLevelAllEnum groupLevelAllEnum = GroupLevelAllEnum.getInstance("");
        Assert.assertNull(groupLevelAllEnum);
    }
    @Test
    public void getInstanceLvOne() {
        GroupLevelAllEnum groupLevelAllEnum = GroupLevelAllEnum.getInstance("LV1");
        Assert.assertNotNull(groupLevelAllEnum);
    }
}