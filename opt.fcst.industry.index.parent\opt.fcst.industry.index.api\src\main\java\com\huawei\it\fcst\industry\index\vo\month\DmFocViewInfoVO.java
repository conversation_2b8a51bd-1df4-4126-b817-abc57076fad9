/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.month;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * The Entity of DmFocViewInfoD
 *
 * <AUTHOR>
 * @since 2023-03-16
 */
@Builder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class DmFocViewInfoVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * GROUP层级(ICT/LV1/LV2/CEG/CATEGORY/ITEM)
     **/
    @JsonProperty("group_level")
    private String groupLevel;

    /**
     * 重量级团队LV0代码
     **/
    @JsonProperty("lv0_prod_rnd_team_code")
    private String lv0ProdRndTeamCode;

    /**
     * 重量级团队LV0中文名称
     **/
    @JsonProperty("lv0_prod_rd_team_cn_name")
    private String lv0ProdRdTeamCnName;

    /**
     * 重量级团队LV1代码
     **/
    @JsonProperty("lv1_prod_rnd_team_code")
    private String lv1ProdRndTeamCode;

    /**
     * 重量级团队LV1中文名称
     **/
    @JsonProperty("lv1_prod_rd_team_cn_name")
    private String lv1ProdRdTeamCnName;

    /**
     * 重量级团队LV2代码
     **/
    @JsonProperty("lv2_prod_rnd_team_code")
    private String lv2ProdRndTeamCode;

    /**
     * 重量级团队LV2中文名称
     **/
    @JsonProperty("lv2_prod_rd_team_cn_name")
    private String lv2ProdRdTeamCnName;

    /**
     * 专家团CODE
     **/
    @JsonProperty("l3_ceg_code")
    private String l3CegCode;

    /**
     * 专家团中文名称
     **/
    @JsonProperty("l3_ceg_cn_name")
    private String l3CegCnName;

    /**
     * 专家团中文简称
     **/
    @JsonProperty("l3_ceg_short_cn_name")
    private String l3CegShortCnName;

    /**
     * 品类CODE
     **/
    @JsonProperty("category_code")
    private String categoryCode;

    /**
     * 品类中文名称
     **/
    @JsonProperty("category_cn_name")
    private String categoryCnName;

    /**
     * 子项ITEM编码
     **/
    @JsonProperty("item_code")
    private String itemCode;

    /**
     * 子项ITEM中文名称
     **/
    @JsonProperty("item_cn_name")
    private String itemCnName;

    /**
     * 创建人
     **/
    @JsonProperty("created_by")
    private String createdBy;

    /**
     * 创建日期
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    @JsonProperty("creation_date")
    private Timestamp creationDate;

    /**
     * 最后更新人
     **/
    @JsonProperty("last_updated_by")
    private String lastUpdatedBy;

    /**
     * 最后更新日期
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    @JsonProperty("last_update_date")
    private Timestamp lastUpdateDate;

    /**
     * 删除标识(未删除：N，已删除：Y)
     **/
    @JsonProperty("del_flag")
    private String delFlag;

    /**
     * 视角标识，用于区分不同视角下的品类数据(0: ICT层级, 1:ICT-LV1层级, 2:ICT-LV1-LV2层级)
     **/
    @JsonProperty("view_flag")
    private String viewFlag;

    private Long versionId;

}
