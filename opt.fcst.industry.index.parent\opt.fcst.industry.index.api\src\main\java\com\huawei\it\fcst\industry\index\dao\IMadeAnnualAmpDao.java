/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.annual.AnnualAnalysisVO;
import com.huawei.it.fcst.industry.index.vo.annual.DmFocAnnualAmpVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * IAnnualAmpDao
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
public interface IMadeAnnualAmpDao {
    List<DmFocAnnualAmpVO> madeAllIndustryCost(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> madeAllIndustryRevCost(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> madeAllIndustryCombCost(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> madeMultiIndustryCostChart(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> madeMultiIndustryCostChartMultiSelect(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> madeMultiIndustryCostNormalChart(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> findMadeGroupCodeOrderByWeight(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> findMadeCombItemCodeOrderByWeight(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> findMadeProdteamCodeOrderByWeight(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> findMadeRevGroupCodeOrderByWeight(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> findMadeGranularityTypeOrderByWeight(AnnualAnalysisVO annualAnalysisVO);

    List<String> getMadeAnnualPeriodYear(@Param("tablePreFix") String tablePreFix,@Param("versionId")Long versionId);

    List<DmFocAnnualAmpVO> madeMultiIndustryCombChart(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> madeIndustryCostList(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> madeIndustryRevCostList(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> madeIndustryCostAllList(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> madeIndustryCostNormalList(AnnualAnalysisVO annualAnalysisVO);

    int madeIndustryCostAllItemCount(AnnualAnalysisVO annualAnalysisVO);

    int madeIndustryCostCombItemCount(AnnualAnalysisVO annualAnalysisVO);

    int madeIndustryCostNormalItemCount(AnnualAnalysisVO annualAnalysisVO);

    PagedResult<DmFocAnnualAmpVO> madeIndustryCombCharPage(@Param("annualAnalysisVO") AnnualAnalysisVO annualAnalysisVO,@Param("pageVO") PageVO pageVO);

    List<DmFocAnnualAmpVO> madeIndustryCombCharlist(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> madeMultiIndustryChartRevMultiSelect(AnnualAnalysisVO annualAnalysisVO);

}
