/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.scheduler;

import com.huawei.it.fcst.config.ConfigUtil;
import com.huawei.it.fcst.industry.pbi.dao.IDmVirtualizedTaskDao;
import com.huawei.it.fcst.enums.GroupTaskType;
import com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstBaseCusDimVO;
import com.huawei.it.jalor5.core.annotation.NoJalorTransation;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.Serializable;

import javax.inject.Inject;

/**
 * 编码替换func执行
 *
 * <AUTHOR>
 * @since 202407
 */
@Slf4j
@Service("task.codeReplaceTaskProcessService")
public class CodeReplaceTaskProcessService extends AbstractTaskProcessService {

    @Inject
    private IDmVirtualizedTaskDao virtualizedTaskDao;
    @Override
    public GroupTaskType getTaskType() {
        return GroupTaskType.REPLACE_DIM;
    }

    @Override
    @NoJalorTransation
    public Boolean process(Serializable serializable,Serializable beforeResult) {
        DmFcstBaseCusDimVO dmFcstBaseCusDimVO = (DmFcstBaseCusDimVO)serializable;
        // 设置任务执行类型
        dmFcstBaseCusDimVO.setPageType(this.getTaskType().name());
        log.info("CodeReplaceTaskProcessService:start->{},虚化id:{}",dmFcstBaseCusDimVO.getPageType(),dmFcstBaseCusDimVO.getCustomId());
        // 执行函数
        return callReplCodeMonthly(dmFcstBaseCusDimVO);
    }

    /**
     * 编码替代 函数执行
     *
     * @param dmFcstBaseCusDimVO
     * @return
     */
    private Boolean callReplCodeMonthly(DmFcstBaseCusDimVO dmFcstBaseCusDimVO){
        if(!FUNC_STATUS_SUCCESS.equalsIgnoreCase(callReplaceCodeMonthFunc(dmFcstBaseCusDimVO))){
            return Boolean.FALSE;
        }
        return FUNC_STATUS_SUCCESS.equalsIgnoreCase(callReplaceCodeMonthYtdFuncTask(dmFcstBaseCusDimVO));
    }

    /**
     * 编码替代的实时月度虚化函数：f_dm_fcst_ict_real_time_base_cus_mon_repl_cost_info_t(f_cost_type character varying ,  f_granularity_type character varying , f_keystr text DEFAULT NULL::character varying,f_custom_id integer, f_version_id integer DEFAULT NULL::integer, OUT x_result_status text)
     *
     * @param dmFcstBaseCusDimVO
     * @return
     */
    private String callReplaceCodeMonthFunc(DmFcstBaseCusDimVO dmFcstBaseCusDimVO){
        log.info("start ————> f_dm_fcst_ict_real_time_base_cus_mon_repl_cost_info_t,虚化id:{}",dmFcstBaseCusDimVO.getCustomId());
        String result = virtualizedTaskDao.callReplaceCodeMonthFunc(dmFcstBaseCusDimVO, ConfigUtil.getInstance().get16PlainText());
        log.info("end ————> f_dm_fcst_ict_real_time_base_cus_mon_repl_cost_info_t,虚化id:{},{}",dmFcstBaseCusDimVO.getCustomId(),result);
        return result;
    }

    /**
     * 编码替代的实时月累计虚化函数：f_dm_fcst_ict_real_time_base_cus_ytd_repl_cost_info_t(f_cost_type character varying ,  f_granularity_type character varying , f_keystr text DEFAULT NULL::character varying, f_custom_id integer,f_version_id integer DEFAULT NULL::integer, OUT x_result_status text)
     *
     * @param dmFcstBaseCusDimVO
     * @return
     */
    private String callReplaceCodeMonthYtdFuncTask(DmFcstBaseCusDimVO dmFcstBaseCusDimVO){
        log.info("start ————>f_dm_fcst_ict_real_time_base_cus_ytd_repl_cost_info_t,虚化id:{}",dmFcstBaseCusDimVO.getCustomId());
        String result = virtualizedTaskDao.callReplaceCodeMonthYtdFuncTask(dmFcstBaseCusDimVO,ConfigUtil.getInstance().get16PlainText());
        log.info("end ————>f_dm_fcst_ict_real_time_base_cus_ytd_repl_cost_info_t,虚化id:{},{}",dmFcstBaseCusDimVO.getCustomId(),result);
        return result;
    }
}
