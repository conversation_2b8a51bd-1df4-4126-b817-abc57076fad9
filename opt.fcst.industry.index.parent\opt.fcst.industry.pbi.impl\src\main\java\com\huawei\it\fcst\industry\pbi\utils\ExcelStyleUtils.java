/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.utils;

import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;

/**
 * Excel样式工具类
 *
 * <AUTHOR>
 * @since 2024-10-14
 */
public class ExcelStyleUtils {

    /**
     * 表头样式
     */
    public static WriteCellStyle getHeadStyle() {
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 字体
        WriteFont headWriteFont = new WriteFont();
        // 设置字体名字
        headWriteFont.setFontName("微软雅黑");
        // 设置字体大小
        headWriteFont.setFontHeightInPoints((short) 12);
        // 字体加粗
        headWriteFont.setBold(true);
        // 设置字体颜色为黑色
        headWriteFont.setColor(IndexedColors.BLACK.getIndex());
        // 在样式中应用设置的字体
        headWriteCellStyle.setWriteFont(headWriteFont);

        // 设置表头样式
        // 设置底边框
        headWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        // 设置底边框颜色
        headWriteCellStyle.setBottomBorderColor((short) 0);
        // 设置左边框
        headWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        // 设置左边框颜色
        headWriteCellStyle.setLeftBorderColor((short) 0);
        // 设置右边框
        headWriteCellStyle.setBorderRight(BorderStyle.THIN);
        // 设置右边框颜色
        headWriteCellStyle.setRightBorderColor((short) 0);
        // 设置顶边框
        headWriteCellStyle.setBorderTop(BorderStyle.THIN);
        // 设置顶边框颜色
        headWriteCellStyle.setTopBorderColor((short) 0);
        // 设置自动换行
        headWriteCellStyle.setWrapped(false);
        // 设置水平对齐的样式为居中对齐;
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        // 设置垂直对齐的样式为居中对齐;
        headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置文本收缩至合适
        headWriteCellStyle.setShrinkToFit(false);
        // 设置单元格背景色
        headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        return headWriteCellStyle;
    }

    /**
     * 内容样式
     */
    public static WriteCellStyle getContentStyle() {
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 设置字体
        WriteFont contentWriteFont = new WriteFont();
        // 设置字体大小
        contentWriteFont.setFontHeightInPoints((short) 11);
        // 设置字体名字
        contentWriteFont.setFontName("宋体");
        // 在样式中应用设置的字体;
        contentWriteCellStyle.setWriteFont(contentWriteFont);

        // 设置样式
        // 设置底边框
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        // 设置底边框颜色
        contentWriteCellStyle.setBottomBorderColor((short) 0);
        // 设置左边框
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        // 设置左边框颜色
        contentWriteCellStyle.setLeftBorderColor((short) 0);
        // 设置右边框
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        // 设置右边框颜色
        contentWriteCellStyle.setRightBorderColor((short) 0);
        // 设置顶边框
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        // 设置顶边框颜色
        contentWriteCellStyle.setTopBorderColor((short) 0);
        // 水平居中
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        // 垂直居中
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置自动换行
        contentWriteCellStyle.setWrapped(false);
        // 设置文本收缩至合适
        contentWriteCellStyle.setShrinkToFit(false);
        return contentWriteCellStyle;
    }

}