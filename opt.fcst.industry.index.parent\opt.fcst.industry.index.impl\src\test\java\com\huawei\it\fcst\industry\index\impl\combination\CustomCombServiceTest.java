/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.combination;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.huawei.it.fcst.industry.index.dao.IDmFocCustomCombDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocCustomCombTempDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocDataRefreshStatusDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocEnergyCustomCombTempDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocIasCustomCombDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocIasCustomCombTempDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeCustomCombDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocVersionDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocViewInfoDao;
import com.huawei.it.fcst.industry.index.impl.annual.AnnualCommonService;
import com.huawei.it.fcst.industry.index.impl.common.CommonService;
import com.huawei.it.fcst.industry.index.impl.iauth.DataDimensionService;
import com.huawei.it.fcst.config.ConfigUtil;
import com.huawei.it.fcst.industry.index.vo.combination.CombTransformVO;
import com.huawei.it.fcst.industry.index.vo.combination.CombinationVO;
import com.huawei.it.fcst.industry.index.vo.combination.DmCustomCombVO;
import com.huawei.it.fcst.industry.index.vo.combination.DmFocDataRefreshStatus;
import com.huawei.it.fcst.industry.index.vo.common.CommonViewVO;
import com.huawei.it.fcst.industry.index.vo.common.DataPermissionsVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoDTO;
import com.huawei.it.fcst.industry.index.vo.view.ViewInfoVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.ioc.Jalor;
import com.huawei.it.jalor5.core.ioc.delegate.JalorApplicationContext;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.security.RoleVO;
import com.huawei.it.jalor5.security.UserVO;

import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.AsyncResult;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * CustomCombServiceTest Class
 *
 * <AUTHOR>
 * @since 2023/9/15
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class CustomCombServiceTest {
    private static final Logger LOGGER = LoggerFactory.getLogger(CustomCombService.class);

    @InjectMocks
    private CustomCombService customCombService;

    @Mock
    private JalorApplicationContext applicationContext;

    @Mock
    private IRequestContext requestContext;

    @Mock
    private ConfigUtil configUtil;

    @Mock
    private IDmFocCustomCombDao dmFocCustomCombDao;

    @Mock
    private CommonService commonService;

    @Mock
    private CustomCommonService customCommonService;

    @Mock
    private AsyncService asyncService;

    @Mock
    private IDmFocVersionDao dmFocVersionDao;

    @Mock
    private IDmFocDataRefreshStatusDao dataRefreshStatusDao;

    @Mock
    private IDmFocViewInfoDao dmFocViewInfoDao;

    @Mock
    private DataDimensionService dataDimensionService;

    @Mock
    private IDmFocMadeCustomCombDao dmFocMadeCustomCombDao;

    @Mock
    private AnnualCommonService annualCommonService;

    @Mock
    private IDmFocCustomCombTempDao dmFocCustomCombTempDao;

    @Mock
    private IDmFocIasCustomCombDao dmFocIasCustomCombDao;

    @Mock
    private IDmFocEnergyCustomCombTempDao dmFocEnergyCustomCombTempDao;

    @Mock
    private IDmFocIasCustomCombTempDao dmFocIasCustomCombTempDao;

    /**
     * initClass
     *
     * @throws ApplicationException
     */
    @Before
    public void initClass() throws ApplicationException {
        Jalor.setContext(applicationContext);
        RequestContextManager.setCurrent(requestContext);
        UserVO user = new UserVO();
        user.setUserId(-1);
        RoleVO role = new RoleVO();
        role.setRoleId(-1);
        user.setCurrentRole(role);
        Mockito.when(RequestContext.getCurrent().getUser()).thenReturn(user);
        Mockito.when(Jalor.getContext().getBean("configUtil", ConfigUtil.class))
                .thenReturn(configUtil);
    }

    @Test
    public void createCombinationTest() throws Exception {
        CombinationVO combinationVO = new CombinationVO();
        List<DmCustomCombVO> customCombNameList = new ArrayList<>();
        customCombNameList.add(new DmCustomCombVO());
        Mockito.when(dmFocCustomCombDao.getCustomCombListByName(combinationVO)).thenReturn(customCombNameList);
        Assert.assertThrows(CommonApplicationException.class, () -> customCombService.createCombination(combinationVO));

        Mockito.when(dmFocCustomCombDao.getCustomCombListByName(combinationVO)).thenReturn(null);
        List<DmCustomCombVO> customVOList = new ArrayList<>();
        combinationVO.setCustomVOList(customVOList);
        combinationVO.setPageFlag("ALL");
        combinationVO.setCostType("P");
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Mockito.when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(currentRoleDataPermission);
        List<DmCustomCombVO> otherCustomVOList = new ArrayList<>();
        Mockito.when(customCommonService.filterAnotherPageData(Mockito.any(), Mockito.anyList())).thenReturn(otherCustomVOList);
        DmFocVersionInfoDTO dmFocVersionVO = new DmFocVersionInfoDTO();
        Mockito.when(dmFocVersionDao.findVersionIdByDataType(Mockito.any(),any())).thenReturn(dmFocVersionVO);
        customCombService.createCombination(combinationVO);
        Assert.assertTrue(true);
    }

    @Test
    public void createTempTableTest() throws Exception {
        CombinationVO combinationVO = new CombinationVO();

        List<DmCustomCombVO> parentCustomVOList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setGroupCnName("name1");
        dmCustomCombVO.setGroupCode("111");
        parentCustomVOList.add(dmCustomCombVO);
        combinationVO.setParentCustomVOList(parentCustomVOList);
        combinationVO.setIndustryOrg("ICT");
        combinationVO.setCostType("P");
        customCombService.createTempTable(combinationVO);

        CombinationVO combinationVO2 = new CombinationVO();
        combinationVO2.setParentCustomVOList(parentCustomVOList);
        combinationVO2.setIndustryOrg("ICT");
        combinationVO2.setCostType("M");
        customCombService.createTempTable(combinationVO2);

        CombinationVO combinationVO3 = new CombinationVO();
        combinationVO3.setParentCustomVOList(parentCustomVOList);
        combinationVO3.setIndustryOrg("ENERGY");
        combinationVO3.setCostType("P");
        customCombService.createTempTable(combinationVO3);

        CombinationVO combinationVO4 = new CombinationVO();
        combinationVO4.setParentCustomVOList(parentCustomVOList);
        combinationVO4.setIndustryOrg("ENERGY");
        combinationVO4.setCostType("M");
        customCombService.createTempTable(combinationVO4);

        CombinationVO combinationVO5 = new CombinationVO();
        combinationVO5.setParentCustomVOList(parentCustomVOList);
        combinationVO5.setIndustryOrg("IAS");
        combinationVO5.setCostType("P");
        customCombService.createTempTable(combinationVO5);

        CombinationVO combinationVO6 = new CombinationVO();
        combinationVO6.setParentCustomVOList(parentCustomVOList);
        combinationVO6.setIndustryOrg("IAS");
        combinationVO6.setCostType("M");
        customCombService.createTempTable(combinationVO6);
        Assert.assertNull(null);
    }

    @Test
    public void deleteTempTableTest() throws Exception {

        CombinationVO combinationVO = new CombinationVO();
        combinationVO.setCostType("P");
        customCombService.deleteTempTable(combinationVO);

        CombinationVO combinationVO2 = new CombinationVO();
        combinationVO2.setCostType("M");
        customCombService.deleteTempTable(combinationVO2);
        Assert.assertNull(null);
    }

    @Test
    public void getTempTableListTest() throws Exception {

        CombinationVO combinationVO = new CombinationVO();
        combinationVO.setCostType("P");
        customCombService.getTempTableList(combinationVO);

        CombinationVO combinationVO2 = new CombinationVO();
        combinationVO2.setCostType("P");
        combinationVO2.setGranularityType("U");
        List<DmCustomCombVO> dmCustomCombList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setGroupCode("111");
        dmCustomCombVO.setGroupLevel("LV1");
        dmCustomCombList.add(dmCustomCombVO);
        PowerMockito.doReturn(dmCustomCombList).when(dmFocCustomCombTempDao).getTempTableCustomCombList(combinationVO2);
        customCombService.getTempTableList(combinationVO2);

        CombinationVO combinationVO3 = new CombinationVO();
        combinationVO3.setCostType("M");
        combinationVO3.setGranularityType("U");
        combinationVO3.setLv0ProdRndTeamCode("1001");
        combinationVO3.setViewFlag("2");
        combinationVO3.setGroupLevel("LV1");
        combinationVO3.setIndustryOrg("ICT");
        List<DmCustomCombVO> dmCustomCombList3 = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO33 = new DmCustomCombVO();
        dmCustomCombVO33.setGroupCode("111");
        dmCustomCombVO33.setGroupLevel("LV1");
        dmCustomCombList3.add(dmCustomCombVO33);
        PowerMockito.doReturn(dmCustomCombList3).when(dmFocCustomCombTempDao).getTempTableManufactureCustomCombList(combinationVO3);
        customCombService.getTempTableList(combinationVO3);

        CombinationVO combinationV4 = new CombinationVO();
        combinationV4.setCostType("P");
        combinationV4.setGranularityType("U");
        combinationV4.setLv0ProdRndTeamCode("1001");
        combinationV4.setViewFlag("2");
        combinationV4.setGroupLevel("LV2");
        combinationV4.setIndustryOrg("ICT");
        List<DmCustomCombVO> dmCustomCombList4 = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO44 = new DmCustomCombVO();
        dmCustomCombVO44.setGroupCode("111");
        dmCustomCombVO44.setGroupLevel("CEG");
        dmCustomCombVO44.setSelectFlag("current");
        dmCustomCombList4.add(dmCustomCombVO44);
        PowerMockito.doReturn(dmCustomCombList4).when(dmFocCustomCombTempDao).getTempTableCustomCombList(combinationV4);
        customCombService.getTempTableList(combinationV4);
        Assert.assertTrue(true);
    }

    @Test
    public void removeTempTableListTest() throws Exception {

        CombinationVO combinationVO = new CombinationVO();
        combinationVO.setCostType("P");
        combinationVO.setIndustryOrg("ICT");
        combinationVO.setTablePreFix("dm_foc");
        combinationVO.setFirstFlag("Y");
        combinationVO.setRemoveFlag("right");
        List<DmCustomCombVO> customCombList = new ArrayList<>();
        List<DmCustomCombVO> customVOList = new ArrayList<>();
        List<DmCustomCombVO> customVOList2 = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setGroupLevel("CEG");
        dmCustomCombVO.setGroupCode("213aa");
        dmCustomCombVO.setFoldFlag("Y");
        customVOList.add(dmCustomCombVO);
        customVOList2.add(dmCustomCombVO);

        DmCustomCombVO dmCustomCombVO2 = new DmCustomCombVO();
        dmCustomCombVO2.setGroupLevel("CATEGORY");
        dmCustomCombVO2.setGroupCode("33332");
        dmCustomCombVO2.setFoldFlag("N");
        customVOList.add(dmCustomCombVO2);
        combinationVO.setCustomVOList(customVOList);
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(1L);
        Mockito.when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(dmFocVersionInfoDTO);

        Future<Boolean> groupCodeFlag = new AsyncResult<>(Boolean.TRUE);
        IRequestContext requestContext = RequestContext.getCurrent();

        when(asyncService.getFoldGroupCodeList(combinationVO, customVOList2,customCombList,requestContext)).thenReturn(groupCodeFlag);

        customCombService.removeTempTableList(combinationVO);

        CombinationVO combinationVO2 = new CombinationVO();
        BeanUtils.copyProperties(combinationVO, combinationVO2);
        combinationVO2.setRemoveFlag("left");

        List<DmCustomCombVO> filterCustomVOList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO1 = new DmCustomCombVO();
        dmCustomCombVO1.setGroupCode("111");
        dmCustomCombVO1.setGroupLevel("LV1");
        filterCustomVOList.add(dmCustomCombVO1);
        combinationVO2.setGranularityType("U");
        combinationVO2.setPageSymbol("MONTH");
        combinationVO2.setFilterCustomVOList(filterCustomVOList);
        combinationVO2.setViewFlag("3");

        List<DmCustomCombVO> remainList = new ArrayList<>();

        when(asyncService.getFoldGroupCodeList(combinationVO2, customVOList2,customCombList,requestContext)).thenReturn(groupCodeFlag);
        when(asyncService.getFoldGroupCodeList(combinationVO2, remainList, customCombList,requestContext)).thenReturn(groupCodeFlag);

        customCombService.removeTempTableList(combinationVO2);

        Assert.assertNull(null);
    }

    @Test
    public void removeTempTableList2Test() throws Exception {

        CombinationVO combinationVO2 = new CombinationVO();
        combinationVO2.setCostType("P");
        combinationVO2.setIndustryOrg("ICT");
        combinationVO2.setTablePreFix("dm_foc");
        combinationVO2.setFirstFlag("Y");
        combinationVO2.setRemoveFlag("left");
        combinationVO2.setViewFlag("3");

        List<DmCustomCombVO> filterCustomVOList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO1 = new DmCustomCombVO();
        dmCustomCombVO1.setGroupCode("111");
        dmCustomCombVO1.setGroupLevel("LV1");
        filterCustomVOList.add(dmCustomCombVO1);
        combinationVO2.setGranularityType("U");
        combinationVO2.setPageSymbol("MONTH");
        combinationVO2.setFilterCustomVOList(filterCustomVOList);

        List<DmCustomCombVO> customCombList = new ArrayList<>();
        List<DmCustomCombVO> customVOList = new ArrayList<>();
        List<DmCustomCombVO> customVOList2 = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setGroupLevel("CEG");
        dmCustomCombVO.setGroupCode("213aa");
        dmCustomCombVO.setFoldFlag("Y");
        customVOList.add(dmCustomCombVO);
        customVOList2.add(dmCustomCombVO);

        DmCustomCombVO dmCustomCombVO2 = new DmCustomCombVO();
        dmCustomCombVO2.setGroupLevel("CATEGORY");
        dmCustomCombVO2.setGroupCode("33332");
        dmCustomCombVO2.setFoldFlag("N");
        customVOList.add(dmCustomCombVO2);
        combinationVO2.setCustomVOList(customVOList);
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(1L);
        Mockito.when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(dmFocVersionInfoDTO);

        Future<Boolean> groupCodeFlag = new AsyncResult<>(Boolean.TRUE);
        IRequestContext requestContext = RequestContext.getCurrent();

        when(asyncService.getFoldGroupCodeList(combinationVO2, customVOList2,customCombList,requestContext)).thenReturn(groupCodeFlag);
        List<DmCustomCombVO> remainList = new ArrayList<>();
        when(asyncService.getFoldGroupCodeList(combinationVO2, remainList,customCombList,requestContext)).thenReturn(groupCodeFlag);

        customCombService.removeTempTableList(combinationVO2);

        Assert.assertNull(null);
    }

    @Test
    public void removeTempTableList3Test() throws Exception {

        CombinationVO combinationVO2 = new CombinationVO();
        combinationVO2.setCostType("P");
        combinationVO2.setIndustryOrg("ICT");
        combinationVO2.setTablePreFix("dm_foc");
        combinationVO2.setFirstFlag("Y");
        combinationVO2.setRemoveFlag("right");
        combinationVO2.setViewFlag("3");

        List<DmCustomCombVO> filterCustomVOList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO1 = new DmCustomCombVO();
        dmCustomCombVO1.setGroupCode("111");
        dmCustomCombVO1.setGroupLevel("LV1");
        filterCustomVOList.add(dmCustomCombVO1);
        combinationVO2.setGranularityType("U");
        combinationVO2.setPageSymbol("MONTH");
        combinationVO2.setFilterCustomVOList(filterCustomVOList);

        List<DmCustomCombVO> customCombList = new ArrayList<>();
        List<DmCustomCombVO> customVOList = new ArrayList<>();
        List<DmCustomCombVO> customVOList2 = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setGroupLevel("CEG");
        dmCustomCombVO.setGroupCode("213aa");
        dmCustomCombVO.setFoldFlag("Y");
        customVOList.add(dmCustomCombVO);
        customVOList2.add(dmCustomCombVO);

        DmCustomCombVO dmCustomCombVO2 = new DmCustomCombVO();
        dmCustomCombVO2.setGroupLevel("CATEGORY");
        dmCustomCombVO2.setGroupCode("33332");
        dmCustomCombVO2.setFoldFlag("N");
        customVOList.add(dmCustomCombVO2);
        combinationVO2.setCustomVOList(customVOList);
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(1L);
        Mockito.when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(dmFocVersionInfoDTO);

        Future<Boolean> groupCodeFlag = new AsyncResult<>(Boolean.TRUE);
        IRequestContext requestContext = RequestContext.getCurrent();

        when(asyncService.getFoldGroupCodeList(combinationVO2, customVOList2,customCombList,requestContext)).thenReturn(groupCodeFlag);
        List<DmCustomCombVO> remainList = new ArrayList<>();
        when(asyncService.getFoldGroupCodeList(combinationVO2, remainList,customCombList,requestContext)).thenReturn(groupCodeFlag);

        customCombService.removeTempTableList(combinationVO2);

        Assert.assertNull(null);
    }

    @Test
    public void getMethodTest() throws Exception {

        CombinationVO combinationParamVO = new CombinationVO();
        List<DmCustomCombVO> filterCustomVOList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO1 = new DmCustomCombVO();
        dmCustomCombVO1.setGroupCode("111");
        dmCustomCombVO1.setGroupLevel("LV2");
        filterCustomVOList.add(dmCustomCombVO1);
        DmCustomCombVO dmCustomCombVO2 = new DmCustomCombVO();
        dmCustomCombVO2.setGroupCode("111");
        dmCustomCombVO2.setGroupLevel("LV3");
        filterCustomVOList.add(dmCustomCombVO2);

        Map<String, List<DmCustomCombVO>> dmCustomMap = filterCustomVOList.stream().collect(Collectors.groupingBy(DmCustomCombVO::getGroupLevel));
        Whitebox.invokeMethod(customCombService, "getSelectCodeByLevel", combinationParamVO, dmCustomMap);
        combinationParamVO.setCostType("M");
        combinationParamVO.setChangeCustomVOList(filterCustomVOList);
        Whitebox.invokeMethod(customCombService, "updateChangeListSelectFlag", combinationParamVO);
        combinationParamVO.setCostType("P");
        Whitebox.invokeMethod(customCombService, "updateChangeListSelectFlag", combinationParamVO);
        Assert.assertTrue(true);
    }

    @Test
    public void getMethod2Test() throws Exception {

        CommonViewVO commonViewVO = new CommonViewVO();
        commonViewVO.setLv0ProdRndTeamCode("104364");
        commonViewVO.setCostType("P");
        commonViewVO.setViewFlag("3");
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setGranularityType("U");
        commonViewVO.setGroupLevel("LV1");

        Whitebox.invokeMethod(customCombService, "findNextGroupLevel", commonViewVO);

        commonViewVO.setCostType("M");

        Whitebox.invokeMethod(customCombService, "findNextGroupLevel", commonViewVO);
        CombinationVO combinationVO = new CombinationVO();
        combinationVO.setIndustryOrg("ICT");
        combinationVO.setCostType("P");
        DmFocVersionInfoDTO dmFocVersionVO = new DmFocVersionInfoDTO();
        dmFocVersionVO.setVersionId(1L);
        Mockito.when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(dmFocVersionVO);
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        Mockito.when(annualCommonService.getThreeYears(combinationVO.getCostType(),combinationVO.getIndustryOrg())).thenReturn(threeYears);
        Whitebox.invokeMethod(customCombService, "setMoveParam", combinationVO);
        Assert.assertTrue(true);
    }

    @Test
    public void getMethod3Test() throws Exception {

        List<DmCustomCombVO> filterCustomVOList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setConnectCode("111");
        dmCustomCombVO.setConnectParentCode("333");
        filterCustomVOList.add(dmCustomCombVO);

        DmCustomCombVO dmCustomCombVO2 = new DmCustomCombVO();
        dmCustomCombVO2.setConnectCode("444");
        dmCustomCombVO2.setConnectParentCode("222");
        filterCustomVOList.add(dmCustomCombVO2);
        List<DmCustomCombVO> findParentList =new ArrayList<>();
        List<DmCustomCombVO> remainList = new ArrayList<>();
        List<DmCustomCombVO> customCombList = new ArrayList<>();

        Whitebox.invokeMethod(customCombService, "findParentChildList", filterCustomVOList,findParentList,remainList,customCombList);
        CombinationVO combinationVO = new CombinationVO();
        combinationVO.setFirstFlag("N");
        combinationVO.setId("11");

        Whitebox.invokeMethod(customCombService, "leftOrRightMoveList", combinationVO,customCombList,filterCustomVOList);
        combinationVO.setCostType("P");
        Whitebox.invokeMethod(customCombService, "removeByConnectCode", combinationVO,customCombList);

        combinationVO.setCostType("M");
        Whitebox.invokeMethod(customCombService, "removeByConnectCode", combinationVO,customCombList);
        DmCustomCombVO dmCustomCombVO1 = new DmCustomCombVO();
        dmCustomCombVO1.setSelectFlag("parent");
        dmCustomCombVO1.setConnectCode("111");
        customCombList.add(dmCustomCombVO1);
        Whitebox.invokeMethod(customCombService, "leftRemove", customCombList, combinationVO,filterCustomVOList);
        combinationVO.setCostType("P");
        Whitebox.invokeMethod(customCombService, "leftRemove", customCombList, combinationVO,filterCustomVOList);
        List<String> groupLevelList = new ArrayList<>();
        combinationVO.setGranularityType("U");
        combinationVO.setViewFlag("7");
        Whitebox.invokeMethod(customCombService, "prodTeamCodeLv4", groupLevelList, combinationVO);
        combinationVO.setGranularityType("D");
        combinationVO.setViewFlag("12");
        Whitebox.invokeMethod(customCombService, "prodTeamCodeLv4", groupLevelList, combinationVO);

        Assert.assertTrue(true);
    }

    @Test
    public void getMethod4Test() throws Exception {

        CommonViewVO commonViewVO = new CommonViewVO();
        commonViewVO.setLv0ProdRndTeamCode("104364");
        commonViewVO.setGranularityType("U");
        commonViewVO.setViewFlag("3");
        commonViewVO.setGroupLevel("LV1");
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setCostType("P");
        commonViewVO.setKeyWord("aaa");
        commonViewVO.setFilterGroupLevel("LV1");
        Whitebox.invokeMethod(customCombService, "getProdRndTeamCodeTree", commonViewVO);
        commonViewVO.setCostType("M");
        Whitebox.invokeMethod(customCombService, "getProdRndTeamCodeTree", commonViewVO);

        Map<String, List<DmFocViewInfoVO>> groupLevelMap = new HashMap<>();
        List<DmFocViewInfoVO> dmCustomCombList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setConnectCode("222");
        dmFocViewInfoVO.setLv2ProdRndTeamCode("2211");
        dmFocViewInfoVO.setLv3ProdRndTeamCode("22144");
        dmFocViewInfoVO.setDimensionCode("13434");
        dmFocViewInfoVO.setDimensionSubDetailCode("13434");
        dmFocViewInfoVO.setDimensionSubCategoryCode("222");
        dmFocViewInfoVO.setSpartCode("213");
        dmCustomCombList.add(dmFocViewInfoVO);
        boolean flag = true;
        commonViewVO.setGranularityType("D");
        commonViewVO.setIndustryOrg("ICT");
        Whitebox.invokeMethod(customCombService, "getParentCodeList", commonViewVO, groupLevelMap, dmCustomCombList,flag);

        commonViewVO.setIndustryOrg("ENERGY");
        Whitebox.invokeMethod(customCombService, "getParentCodeList", commonViewVO, groupLevelMap, dmCustomCombList,flag);
        String granularityPageSymbol = "D_ANNUAL";
        List<DmFocViewInfoVO> allParentList = new ArrayList<>();
        List<String> groupLevelList = new ArrayList<>();
        groupLevelList.add("LV1");
        Whitebox.invokeMethod(customCombService, "queryParentCodeListFromDB", commonViewVO, granularityPageSymbol, allParentList,groupLevelList);
        String beforeGroupLevel = "LV2";
        commonViewVO.setViewFlag("7");
        commonViewVO.setGranularityType("U");
        Whitebox.invokeMethod(customCombService, "getBeforeGroupLevel", beforeGroupLevel, commonViewVO,groupLevelList);

        String beforeGroupLevel2 = "LV3";
        Whitebox.invokeMethod(customCombService, "getBeforeGroupLevel", beforeGroupLevel2, commonViewVO,groupLevelList);

        String beforeGroupLevel3 = "LV4";
        Whitebox.invokeMethod(customCombService, "getBeforeGroupLevel", beforeGroupLevel3, commonViewVO,groupLevelList);

        String beforeGroupLevel4 = "CEG";
        Whitebox.invokeMethod(customCombService, "getBeforeGroupLevel", beforeGroupLevel4, commonViewVO,groupLevelList);

        String beforeGroupLevel5 = "MODL";
        Whitebox.invokeMethod(customCombService, "getBeforeGroupLevel", beforeGroupLevel5, commonViewVO,groupLevelList);

        String beforeGroupLevel6 = "CATEGORY";
        Whitebox.invokeMethod(customCombService, "getBeforeGroupLevel", beforeGroupLevel6, commonViewVO,groupLevelList);


        Whitebox.invokeMethod(customCombService, "getManufactureBeforeGroupLevel", beforeGroupLevel3, commonViewVO,groupLevelList);
        String beforeGroupLevel7 = "MANUFACTURE_OBJECT";
        Whitebox.invokeMethod(customCombService, "getManufactureBeforeGroupLevel", beforeGroupLevel7, commonViewVO,groupLevelList);

        String beforeGroupLevel8 = "SHIPPING_OBJECT";
        Whitebox.invokeMethod(customCombService, "getManufactureBeforeGroupLevel", beforeGroupLevel8, commonViewVO,groupLevelList);

        Whitebox.invokeMethod(customCombService, "purchaseNineGroupLevel", beforeGroupLevel4,groupLevelList);
        Whitebox.invokeMethod(customCombService, "purchaseNineGroupLevel", beforeGroupLevel5,groupLevelList);
        Whitebox.invokeMethod(customCombService, "purchaseNineGroupLevel", beforeGroupLevel6,groupLevelList);

        Whitebox.invokeMethod(customCombService, "purchaseNineGroupLevel", "SPART",groupLevelList);
        Whitebox.invokeMethod(customCombService, "purchaseNineGroupLevel", "SUB_DETAIL",groupLevelList);
        Whitebox.invokeMethod(customCombService, "purchaseNineGroupLevel", "DIMENSION",groupLevelList);
        Whitebox.invokeMethod(customCombService, "purchaseNineGroupLevel", "SUBCATEGORY",groupLevelList);
        commonViewVO.setGranularityType("D");
        commonViewVO.setViewFlag("6");
        Whitebox.invokeMethod(customCombService, "getFrontManufactureDimensionGroupLevel", beforeGroupLevel7, commonViewVO,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getFrontManufactureDimensionGroupLevel", beforeGroupLevel8, commonViewVO,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getFrontManufactureDimensionGroupLevel", beforeGroupLevel2, commonViewVO,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getFrontManufactureDimensionGroupLevel", beforeGroupLevel, commonViewVO,groupLevelList);

        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionNineGroupLevel", "DIMENSION",groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionNineGroupLevel", "SUBCATEGORY",groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionNineGroupLevel", "SUB_DETAIL",groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionNineGroupLevel", "SPART",groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionNineGroupLevel", beforeGroupLevel7,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionNineGroupLevel", beforeGroupLevel8,groupLevelList);
        Assert.assertTrue(true);
    }

    @Test
    public void getMethod5Test() throws Exception {
        String beforeGroupLevel = "CATEGORY";
        List<String> groupLevelList = new ArrayList<>();
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionTenSubGroupLevel", beforeGroupLevel,groupLevelList);

        String beforeGroupLevel2 = "MODL";
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionTenSubGroupLevel", beforeGroupLevel2,groupLevelList);

        String beforeGroupLevel3 = "CEG";
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionTenSubGroupLevel", beforeGroupLevel3,groupLevelList);

        String beforeGroupLevel4 = "SPART";
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionTenSubGroupLevel", beforeGroupLevel4,groupLevelList);

        String beforeGroupLevel5 = "SUB_DETAIL";
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionTenSubGroupLevel", beforeGroupLevel5,groupLevelList);

        String beforeGroupLevel6 = "SUBCATEGORY";
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionTenSubGroupLevel", beforeGroupLevel6,groupLevelList);

        String beforeGroupLevel7 = "DIMENSION";
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionTenSubGroupLevel", beforeGroupLevel7,groupLevelList);

        String beforeGroupLevel8 = "LV2";
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionTenSubGroupLevel", beforeGroupLevel8,groupLevelList);

        Whitebox.invokeMethod(customCombService, "getBeforeDimensionElevenGroupLevel", beforeGroupLevel,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionElevenGroupLevel", beforeGroupLevel2,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionElevenGroupLevel", beforeGroupLevel3,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionElevenGroupLevel", beforeGroupLevel4,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionElevenGroupLevel", beforeGroupLevel5,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionElevenGroupLevel", beforeGroupLevel6,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionElevenGroupLevel", beforeGroupLevel7,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionElevenGroupLevel", beforeGroupLevel8,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionElevenGroupLevel", "LV3",groupLevelList);

        Whitebox.invokeMethod(customCombService, "getBeforeDimensionTwelveGroupLevel", beforeGroupLevel,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionTwelveGroupLevel", beforeGroupLevel2,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionTwelveGroupLevel", beforeGroupLevel3,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionTwelveGroupLevel", beforeGroupLevel4,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionTwelveGroupLevel", beforeGroupLevel5,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionTwelveGroupLevel", beforeGroupLevel6,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionTwelveGroupLevel", beforeGroupLevel7,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionTwelveGroupLevel", "COA",groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionTwelveGroupLevel", "LV3",groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionTwelveGroupLevel", beforeGroupLevel8,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionTwelveGroupLevelSub", beforeGroupLevel,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionTwelveGroupLevelSub", beforeGroupLevel2,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionTwelveGroupLevelSub", beforeGroupLevel3,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionTwelveGroupLevelSub", beforeGroupLevel4,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionTwelveGroupLevelSub", beforeGroupLevel5,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionTwelveGroupLevelSub", beforeGroupLevel6,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionTwelveGroupLevelSub", beforeGroupLevel7,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionTwelveGroupLevelSub", beforeGroupLevel8,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionTwelveGroupLevelSub", "LV4",groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeDimensionTwelveGroupLevelSub", "LV3",groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionTenGroupLevel", "MANUFACTURE_OBJECT",groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionTenGroupLevel", "SHIPPING_OBJECT",groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionTenGroupLevel", beforeGroupLevel4,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionTenGroupLevel", beforeGroupLevel5,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionTenGroupLevel", beforeGroupLevel6,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionTenGroupLevel", beforeGroupLevel7,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionTenGroupLevel", beforeGroupLevel8,groupLevelList);
        CommonViewVO commonViewVO = new CommonViewVO();
        commonViewVO.setViewFlag("9");
        Whitebox.invokeMethod(customCombService, "getBeforeSubDimensionGroupLevel", beforeGroupLevel7,commonViewVO,groupLevelList);
        commonViewVO.setViewFlag("10");
        Whitebox.invokeMethod(customCombService, "getBeforeSubDimensionGroupLevel", beforeGroupLevel7,commonViewVO,groupLevelList);

        commonViewVO.setViewFlag("11");
        Whitebox.invokeMethod(customCombService, "getBeforeSubDimensionGroupLevel", beforeGroupLevel7,commonViewVO,groupLevelList);

        commonViewVO.setViewFlag("12");
        commonViewVO.setIndustryOrg("ENERGY");
        Whitebox.invokeMethod(customCombService, "getBeforeSubDimensionGroupLevel", beforeGroupLevel7,commonViewVO,groupLevelList);

        commonViewVO.setIndustryOrg("IAS");
        Whitebox.invokeMethod(customCombService, "getBeforeSubDimensionGroupLevel", beforeGroupLevel7,commonViewVO,groupLevelList);
        commonViewVO.setViewFlag("9");
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionGroupLevel", beforeGroupLevel7,commonViewVO,groupLevelList);

        commonViewVO.setViewFlag("10");
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionGroupLevel", beforeGroupLevel7,commonViewVO,groupLevelList);

        commonViewVO.setViewFlag("11");
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionGroupLevel", beforeGroupLevel6,commonViewVO,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionGroupLevel", beforeGroupLevel7,commonViewVO,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionGroupLevel", beforeGroupLevel5,commonViewVO,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionGroupLevel", "MANUFACTURE_OBJECT",commonViewVO,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionGroupLevel", "SHIPPING_OBJECT",commonViewVO,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionGroupLevel", "SPART",commonViewVO,groupLevelList);

        commonViewVO.setViewFlag("12");
        commonViewVO.setIndustryOrg("ENERGY");
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionGroupLevel", beforeGroupLevel7,commonViewVO,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionGroupLevel", "MANUFACTURE_OBJECT",commonViewVO,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionGroupLevel", "SHIPPING_OBJECT",commonViewVO,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionGroupLevel", "SPART",commonViewVO,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionGroupLevel", beforeGroupLevel5,commonViewVO,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionGroupLevel", beforeGroupLevel6,commonViewVO,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionGroupLevel", beforeGroupLevel8,commonViewVO,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionGroupLevel", "COA",commonViewVO,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionGroupLevel", "LV3",commonViewVO,groupLevelList);

        commonViewVO.setIndustryOrg("IAS");
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionGroupLevel", beforeGroupLevel7,commonViewVO,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionGroupLevel", "MANUFACTURE_OBJECT",commonViewVO,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionGroupLevel", "SHIPPING_OBJECT",commonViewVO,groupLevelList);
        Whitebox.invokeMethod(customCombService, "getBeforeManuDimensionSixGroupLevel", beforeGroupLevel7,groupLevelList);
        Assert.assertTrue(true);
    }

    @Test
    public void removeListByCustomId2Test() throws Exception {
        CommonViewVO commonViewVO = new CommonViewVO();
        List<DmFocViewInfoVO> allProdTeamCodeList = new ArrayList<>();
        commonViewVO.setCostType("P");
        commonViewVO.setId("3322221");
        Whitebox.invokeMethod(customCombService, "removeListByCustomId", commonViewVO,allProdTeamCodeList);
        commonViewVO.setCostType("M");
        List<DmCustomCombVO> customCodeList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setConnectCode("21111");
        customCodeList.add(dmCustomCombVO);
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setConnectCode("222");
        allProdTeamCodeList.add(dmFocViewInfoVO);
        Mockito.when(dmFocCustomCombTempDao.getTempManufactureCustomCombList(commonViewVO)).thenReturn(customCodeList);
        Whitebox.invokeMethod(customCombService, "removeListByCustomId", commonViewVO,allProdTeamCodeList);
        Assert.assertNull(null);
    }

    @Test
    public void getCurrentPageCustomCombListTest() throws Exception {
        Method method = PowerMockito.method(CustomCombService.class, "getCurrentPageCustomCombList", CombinationVO.class, String.class, String.class, List.class, Long.class, Timestamp.class);
        CombinationVO combinationVO = new CombinationVO();
        combinationVO.setPageFlag("ALL");
        String userIdStr = "-1";
        String roleId = "-1";
        List<DmCustomCombVO> customVOList = new ArrayList<>();
        Long customId = -1L;
        Timestamp timestamp = new Timestamp(new Date().getTime());
        method.invoke(customCombService, combinationVO, userIdStr, roleId, customVOList, customId, timestamp);

        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        customVOList.add(dmCustomCombVO);
        method.invoke(customCombService, combinationVO, userIdStr, roleId, customVOList, customId, timestamp);

        combinationVO.setPageFlag("N");
        method.invoke(customCombService, combinationVO, userIdStr, roleId, customVOList, customId, timestamp);
        Assert.assertTrue(true);
    }

    @Test
    public void renameCombinationTest() throws Exception {
        CombinationVO combinationVO = new CombinationVO();
        List<DmCustomCombVO> customCombNameList = new ArrayList<>();
        customCombNameList.add(new DmCustomCombVO());
        Mockito.when(dmFocCustomCombDao.getCustomCombListByName(combinationVO)).thenReturn(customCombNameList);
        Assert.assertThrows(CommonApplicationException.class, () -> customCombService.renameCombination(combinationVO));

        Mockito.when(dmFocCustomCombDao.getCustomCombListByName(combinationVO)).thenReturn(null);
        List<DmCustomCombVO> customVOList = new ArrayList<>();
        combinationVO.setCustomVOList(customVOList);
        combinationVO.setPageFlag("ALL");
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Mockito.when(commonService.getCurrentRoleDataPermission(any())).thenReturn(currentRoleDataPermission);
        List<DmCustomCombVO> otherCustomVOList = new ArrayList<>();
        Mockito.when(customCommonService.filterAnotherPageData(Mockito.any(), Mockito.anyList())).thenReturn(otherCustomVOList);
        DmFocVersionInfoDTO dmFocVersionVO = new DmFocVersionInfoDTO();
        Mockito.when(dmFocVersionDao.findVersionIdByDataType(Mockito.any(),any())).thenReturn(dmFocVersionVO);
        customCombService.renameCombination(combinationVO);

        combinationVO.setPageFlag("N");
        customCombService.renameCombination(combinationVO);
        Assert.assertTrue(true);
    }

    @Test
    public void updateCombinationTest() throws Exception {
        CombinationVO combinationVO = new CombinationVO();
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Mockito.when(commonService.getCurrentRoleDataPermission(any())).thenReturn(currentRoleDataPermission);
        DmFocVersionInfoDTO dmFocVersionVO = new DmFocVersionInfoDTO();
        Mockito.when(dmFocVersionDao.findVersionIdByDataType(Mockito.any(),any())).thenReturn(dmFocVersionVO);
        customCombService.updateCombination(combinationVO);
        Assert.assertTrue(true);
    }

    @Test
    public void deleteCombinationTest() throws Exception {
        CombinationVO combinationVO = new CombinationVO();
        combinationVO.setPageFlag("ALL");
        customCombService.deleteCombination(combinationVO);

        combinationVO.setPageFlag("N");
        customCombService.deleteCombination(combinationVO);
        Assert.assertTrue(true);
    }

    @Test
    public void getProdRndTeamTreeTest() throws Exception {
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Mockito.when(commonService.getCurrentRoleDataPermission(any())).thenReturn(currentRoleDataPermission);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        Mockito.when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(annualVersion);
        CommonViewVO commonViewVO = new CommonViewVO();
        commonViewVO.setCostType("U");
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(commonViewVO.getCostType(),commonViewVO.getIndustryOrg());
        customCombService.getProdRndTeamTree(commonViewVO);
        Assert.assertTrue(true);
    }

    @Test
    public void getCombinationListTest() throws Exception {
        CommonViewVO commonViewVO = new CommonViewVO();
        customCombService.getCombinationList(commonViewVO);

        commonViewVO.setGranularityType("U");
        commonViewVO.setPageFlag("N");
        commonViewVO.setCostType("P");
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Mockito.when(commonService.getCurrentRoleDataPermission(any())).thenReturn(currentRoleDataPermission);
        customCombService.getCombinationList(commonViewVO);

        List<DmFocViewInfoVO> dmCustomCombList = new ArrayList<>();
        DmFocViewInfoVO dm1 = new DmFocViewInfoVO();
        dm1.setGroupLevel("CEG");
        dm1.setConnectCode("1");
        dmCustomCombList.add(dm1);
        DmFocViewInfoVO dm2 = new DmFocViewInfoVO();
        dm2.setGroupLevel("MODL");
        dm2.setConnectCode("2");
        dmCustomCombList.add(dm2);
        DmFocViewInfoVO dm3 = new DmFocViewInfoVO();
        dm3.setGroupLevel("CATEGORY");
        dm3.setConnectCode("3");
        dmCustomCombList.add(dm3);
        DmFocViewInfoVO dm4 = new DmFocViewInfoVO();
        dm4.setGroupLevel("test");
        dm4.setConnectCode("4");
        dmCustomCombList.add(dm4);
        commonViewVO.setPageFlag("ALL");
        commonViewVO.setViewFlag("0");
        Mockito.when(dmFocCustomCombDao.getCombinationList(Mockito.any())).thenReturn(dmCustomCombList);
        customCombService.getCombinationList(commonViewVO);
        Assert.assertTrue(true);
    }

    @Test
    public void getCombinationList4Test() throws Exception {
        CommonViewVO commonViewVO = new CommonViewVO();
        customCombService.getCombinationList(commonViewVO);

        commonViewVO.setGranularityType("U");
        commonViewVO.setPageFlag("ALL_ANNUAL");
        commonViewVO.setCostType("M");
        commonViewVO.setPageSymbol("ANNUAL");
        commonViewVO.setViewFlag("3");
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Mockito.when(commonService.getCurrentRoleDataPermission(any())).thenReturn(currentRoleDataPermission);
        List<DmFocViewInfoVO> dmCustomComb2List = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setConnectCode("1111");
        dmFocViewInfoVO.setGroupCode("code");
        dmFocViewInfoVO.setViewFlag("3");
        dmFocViewInfoVO.setGroupLevel("LV2");
        dmFocViewInfoVO.setCustomId(22L);
        dmCustomComb2List.add(dmFocViewInfoVO);
        Mockito.when(dmFocMadeCustomCombDao.getManufacutreCombinationList(Mockito.any())).thenReturn(dmCustomComb2List);

        List<DmFocViewInfoVO> parentList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO3 = new DmFocViewInfoVO();
        dmFocViewInfoVO3.setConnectCode("1111");
        dmFocViewInfoVO3.setGroupCode("code");
        dmFocViewInfoVO3.setViewFlag("3");
        dmFocViewInfoVO3.setGroupLevel("LV2");
        parentList.add(dmFocViewInfoVO3);
        Mockito.when(dmFocMadeCustomCombDao.getManuParentCodeListGeneralAnnual(Mockito.any())).thenReturn(parentList);

        customCombService.getCombinationList(commonViewVO);

        List<DmFocViewInfoVO> dmCustomCombList = new ArrayList<>();
        DmFocViewInfoVO dm1 = new DmFocViewInfoVO();
        dm1.setGroupLevel("CEG");
        dm1.setConnectCode("1");
        dmCustomCombList.add(dm1);
        DmFocViewInfoVO dm2 = new DmFocViewInfoVO();
        dm2.setGroupLevel("MODL");
        dm2.setConnectCode("2");
        dmCustomCombList.add(dm2);
        DmFocViewInfoVO dm3 = new DmFocViewInfoVO();
        dm3.setGroupLevel("CATEGORY");
        dm3.setConnectCode("3");
        dmCustomCombList.add(dm3);
        DmFocViewInfoVO dm4 = new DmFocViewInfoVO();
        dm4.setGroupLevel("test");
        dm4.setConnectCode("4");
        dmCustomCombList.add(dm4);
        commonViewVO.setPageFlag("ALL");
        commonViewVO.setViewFlag("0");
        Mockito.when(dmFocCustomCombDao.getCombinationList(Mockito.any())).thenReturn(dmCustomCombList);
        customCombService.getCombinationList(commonViewVO);
        Assert.assertTrue(true);
    }

    @Test
    public void getCombinationList5Test() throws Exception {
        CommonViewVO commonViewVO = new CommonViewVO();

        commonViewVO.setGranularityType("D");
        commonViewVO.setPageFlag("ALL_ANNUAL");
        commonViewVO.setCostType("M");
        commonViewVO.setPageSymbol("ANNUAL");
        commonViewVO.setViewFlag("3");
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Mockito.when(commonService.getCurrentRoleDataPermission(any())).thenReturn(currentRoleDataPermission);
        List<DmFocViewInfoVO> dmCustomComb2List = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setConnectCode("1111");
        dmFocViewInfoVO.setGroupCode("code");
        dmFocViewInfoVO.setViewFlag("3");
        dmFocViewInfoVO.setGroupLevel("LV2");
        dmFocViewInfoVO.setCustomId(22L);
        dmCustomComb2List.add(dmFocViewInfoVO);
        Mockito.when(dmFocMadeCustomCombDao.getManufacutreCombinationList(commonViewVO)).thenReturn(dmCustomComb2List);

        List<DmFocViewInfoVO> parentList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO3 = new DmFocViewInfoVO();
        dmFocViewInfoVO3.setConnectCode("1111");
        dmFocViewInfoVO3.setGroupCode("code");
        dmFocViewInfoVO3.setViewFlag("3");
        dmFocViewInfoVO3.setGroupLevel("LV2");
        parentList.add(dmFocViewInfoVO3);
        Mockito.when(dmFocMadeCustomCombDao.getManuParentCodeListGeneralAnnual(commonViewVO)).thenReturn(parentList);
        customCombService.getCombinationList(commonViewVO);
        Assert.assertTrue(true);
    }

    @Test
    public void getCombinationList6Test() throws Exception {
        CommonViewVO commonViewVO = new CommonViewVO();

        commonViewVO.setGranularityType("D");
        commonViewVO.setPageFlag("ALL_ANNUAL");
        commonViewVO.setCostType("M");
        commonViewVO.setPageSymbol("ANNUAL");
        commonViewVO.setViewFlag("3");
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Mockito.when(commonService.getCurrentRoleDataPermission(any())).thenReturn(currentRoleDataPermission);
        List<DmFocViewInfoVO> dmCustomComb2List = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setConnectCode("1111");
        dmFocViewInfoVO.setGroupCode("code");
        dmFocViewInfoVO.setViewFlag("3");
        dmFocViewInfoVO.setGroupLevel("LV2");
        dmFocViewInfoVO.setCustomId(22L);
        dmFocViewInfoVO.setLv1ProdRndTeamCode("2221");
        dmFocViewInfoVO.setLv2ProdRndTeamCode("333222");
        dmFocViewInfoVO.setLv3ProdRndTeamCode("33444");
        dmFocViewInfoVO.setDimensionCode("dimens11");
        dmFocViewInfoVO.setDimensionSubCategoryCode("dimensubs11");
        dmFocViewInfoVO.setDimensionSubDetailCode("dimensubsdetail11");
        dmCustomComb2List.add(dmFocViewInfoVO);
        Mockito.when(dmFocMadeCustomCombDao.getManufacutreCombinationList(commonViewVO)).thenReturn(dmCustomComb2List);

        List<DmFocViewInfoVO> parentList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO3 = new DmFocViewInfoVO();
        dmFocViewInfoVO3.setConnectCode("1111");
        dmFocViewInfoVO3.setGroupCode("code");
        dmFocViewInfoVO3.setViewFlag("3");
        dmFocViewInfoVO3.setGroupLevel("LV2");
        parentList.add(dmFocViewInfoVO3);
        Mockito.when(dmFocMadeCustomCombDao.getManuParentCodeListGeneralAnnual(commonViewVO)).thenReturn(parentList);
        customCombService.getCombinationList(commonViewVO);
        Assert.assertTrue(true);
    }

    @Test
    public void getCombinationList7Test() throws Exception {
        CommonViewVO commonViewVO = new CommonViewVO();

        commonViewVO.setGranularityType("D");
        commonViewVO.setPageFlag("ALL_ANNUAL");
        commonViewVO.setCostType("M");
        commonViewVO.setPageSymbol("ANNUAL");
        commonViewVO.setViewFlag("3");

        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        Set<String> lv2DimensionSet  = new HashSet<>();
        Set<String> lv3DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("111");
        lv2DimensionSet.add("222");
        lv2DimensionSet.add("333");
        lv3DimensionSet.add("55555");

        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        currentRoleDataPermission.setLv2DimensionSet(lv2DimensionSet);
        Mockito.when(commonService.getCurrentRoleDataPermission(any())).thenReturn(currentRoleDataPermission);
        List<ViewInfoVO> allProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setLv2ProdRndTeamCode("9999");
        viewInfoVO.setLv1ProdRndTeamCode("4552");
        viewInfoVO.setLv3ProdRndTeamCode("334");
        allProdDimensionList.add(viewInfoVO);
        Mockito.when(dataDimensionService.getCurrentLv2ProdRndTeamList(any(),any())).thenReturn(allProdDimensionList);

        List<DmFocViewInfoVO> dmCustomComb2List = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setConnectCode("1111");
        dmFocViewInfoVO.setGroupCode("code");
        dmFocViewInfoVO.setViewFlag("3");
        dmFocViewInfoVO.setGroupLevel("LV2");
        dmFocViewInfoVO.setCustomId(22L);
        dmFocViewInfoVO.setLv1ProdRndTeamCode("2221");
        dmFocViewInfoVO.setLv2ProdRndTeamCode("333222");
        dmFocViewInfoVO.setLv3ProdRndTeamCode("33444");
        dmFocViewInfoVO.setDimensionCode("dimens11");
        dmFocViewInfoVO.setDimensionSubCategoryCode("dimensubs11");
        dmFocViewInfoVO.setDimensionSubDetailCode("dimensubsdetail11");
        dmCustomComb2List.add(dmFocViewInfoVO);
        Mockito.when(dmFocMadeCustomCombDao.getManufacutreCombinationList(Mockito.any())).thenReturn(dmCustomComb2List);

        List<DmFocViewInfoVO> parentList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO3 = new DmFocViewInfoVO();
        dmFocViewInfoVO3.setConnectCode("1111");
        dmFocViewInfoVO3.setGroupCode("code");
        dmFocViewInfoVO3.setViewFlag("3");
        dmFocViewInfoVO3.setGroupLevel("LV2");
        parentList.add(dmFocViewInfoVO3);

        DmFocViewInfoVO dmFocViewInfoVO4 = new DmFocViewInfoVO();
        dmFocViewInfoVO4.setConnectCode("223");
        dmFocViewInfoVO4.setGroupCode("code1");
        dmFocViewInfoVO4.setViewFlag("3");
        dmFocViewInfoVO4.setGroupLevel("LV1");
        parentList.add(dmFocViewInfoVO4);

        DmFocViewInfoVO dmFocViewInfoVO5 = new DmFocViewInfoVO();
        dmFocViewInfoVO5.setConnectCode("234455");
        dmFocViewInfoVO5.setGroupCode("code444");
        dmFocViewInfoVO5.setViewFlag("3");
        dmFocViewInfoVO5.setGroupLevel("SHIPPING_OBJECT");
        parentList.add(dmFocViewInfoVO5);

        DmFocViewInfoVO dmFocViewInfoVO6 = new DmFocViewInfoVO();
        dmFocViewInfoVO6.setConnectCode("66777");
        dmFocViewInfoVO6.setGroupCode("code777");
        dmFocViewInfoVO6.setViewFlag("3");
        dmFocViewInfoVO6.setGroupLevel("MANUFACTURE_OBJECT");
        parentList.add(dmFocViewInfoVO6);
        Mockito.when(dmFocMadeCustomCombDao.getManuParentCodeListGeneralAnnual(Mockito.any())).thenReturn(parentList);
        customCombService.getCombinationList(commonViewVO);

        getCombinationListForCondition();
        Assert.assertTrue(true);
    }

    private void getCombinationListForCondition() {
        CommonViewVO commonViewVO2 = new CommonViewVO();
        commonViewVO2.setGranularityType("U");
        commonViewVO2.setPageFlag("ALL_ANNUAL");
        commonViewVO2.setCostType("M");
        commonViewVO2.setPageSymbol("ANNUAL");
        commonViewVO2.setViewFlag("3");
        customCombService.getCombinationList(commonViewVO2);

        CommonViewVO commonViewVO3 = new CommonViewVO();
        commonViewVO3.setGranularityType("P");
        commonViewVO3.setPageFlag("ALL_ANNUAL");
        commonViewVO3.setCostType("M");
        commonViewVO3.setPageSymbol("ANNUAL");
        commonViewVO3.setViewFlag("3");
        customCombService.getCombinationList(commonViewVO3);

        CommonViewVO commonViewVO4 = new CommonViewVO();
        commonViewVO4.setGranularityType("U");
        commonViewVO4.setPageFlag("ALL_MONTH");
        commonViewVO4.setCostType("M");
        commonViewVO4.setPageSymbol("MONTH");
        commonViewVO4.setViewFlag("2");
        customCombService.getCombinationList(commonViewVO4);
        CommonViewVO commonViewVO5 = new CommonViewVO();
        commonViewVO5.setGranularityType("P");
        commonViewVO5.setPageFlag("ALL_MONTH");
        commonViewVO5.setCostType("M");
        commonViewVO5.setPageSymbol("MONTH");
        commonViewVO5.setViewFlag("2");
        customCombService.getCombinationList(commonViewVO5);

        CommonViewVO commonViewVO6 = new CommonViewVO();
        commonViewVO6.setGranularityType("D");
        commonViewVO6.setPageFlag("ALL_MONTH");
        commonViewVO6.setCostType("M");
        commonViewVO6.setPageSymbol("MONTH");
        commonViewVO6.setViewFlag("2");
        customCombService.getCombinationList(commonViewVO6);
    }

    @Test
    public void getCombinationList8Test() throws Exception {
        CommonViewVO commonViewVO = new CommonViewVO();

        commonViewVO.setGranularityType("D");
        commonViewVO.setPageFlag("ANNUAL");
        commonViewVO.setCostType("M");
        commonViewVO.setPageSymbol("ANNUAL");
        commonViewVO.setViewFlag("2");

        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        Set<String> lv2DimensionSet  = new HashSet<>();
        Set<String> lv3DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("111");
        lv2DimensionSet.add("222");
        lv2DimensionSet.add("333");
        lv3DimensionSet.add("55555");

        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        currentRoleDataPermission.setLv2DimensionSet(lv2DimensionSet);
        Mockito.when(commonService.getCurrentRoleDataPermission(any())).thenReturn(currentRoleDataPermission);
        List<ViewInfoVO> allProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setLv2ProdRndTeamCode("9999");
        viewInfoVO.setLv1ProdRndTeamCode("4552");
        viewInfoVO.setLv3ProdRndTeamCode("334");
        allProdDimensionList.add(viewInfoVO);
        Mockito.when(dataDimensionService.getCurrentLv2ProdRndTeamList(any(),any())).thenReturn(allProdDimensionList);

        List<DmFocViewInfoVO> dmCustomComb2List = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setConnectCode("1111");
        dmFocViewInfoVO.setGroupCode("code");
        dmFocViewInfoVO.setViewFlag("2");
        dmFocViewInfoVO.setGroupLevel("LV2");
        dmFocViewInfoVO.setCustomId(22L);
        dmFocViewInfoVO.setLv1ProdRndTeamCode("2221");
        dmFocViewInfoVO.setLv2ProdRndTeamCode("333222");
        dmFocViewInfoVO.setLv3ProdRndTeamCode("33444");
        dmFocViewInfoVO.setDimensionCode("dimens11");
        dmFocViewInfoVO.setDimensionSubCategoryCode("dimensubs11");
        dmFocViewInfoVO.setDimensionSubDetailCode("dimensubsdetail11");
        dmCustomComb2List.add(dmFocViewInfoVO);

        DmFocViewInfoVO dmFocViewInfoVO2 = new DmFocViewInfoVO();
        dmFocViewInfoVO2.setConnectCode("111221");
        dmFocViewInfoVO2.setGroupCode("code");
        dmFocViewInfoVO2.setViewFlag("8");
        dmFocViewInfoVO2.setGroupLevel("MANUFACTURE_OBJECT");
        dmFocViewInfoVO2.setCustomId(33L);
        dmFocViewInfoVO2.setLv1ProdRndTeamCode("2221");
        dmFocViewInfoVO2.setLv2ProdRndTeamCode("333222");
        dmFocViewInfoVO2.setLv3ProdRndTeamCode("33444");
        dmFocViewInfoVO2.setDimensionCode("dimens11");
        dmFocViewInfoVO2.setDimensionSubCategoryCode("dimensubs11");
        dmFocViewInfoVO2.setDimensionSubDetailCode("dimensubsdetail11");
        dmCustomComb2List.add(dmFocViewInfoVO2);
        Mockito.when(dmFocMadeCustomCombDao.getManufacutreCombinationList(Mockito.any())).thenReturn(dmCustomComb2List);

        List<DmFocViewInfoVO> parentList = getDmFocViewInfoParentCodeList();
        Mockito.when(dmFocMadeCustomCombDao.getManuParentCodeListGeneralAnnual(Mockito.any())).thenReturn(parentList);
        customCombService.getCombinationList(commonViewVO);

        getCombinationForPage();
        getCombinationListForPageFlag();
        Assert.assertTrue(true);
    }

    private void getCombinationForPage() {
        CommonViewVO commonViewVO2 = new CommonViewVO();
        commonViewVO2.setGranularityType("U");
        commonViewVO2.setPageFlag("ALL_ANNUAL");
        commonViewVO2.setCostType("M");
        commonViewVO2.setPageSymbol("ANNUAL");
        commonViewVO2.setViewFlag("2");
        customCombService.getCombinationList(commonViewVO2);

        CommonViewVO commonViewVO3 = new CommonViewVO();
        commonViewVO3.setGranularityType("P");
        commonViewVO3.setPageFlag("ALL_ANNUAL");
        commonViewVO3.setCostType("M");
        commonViewVO3.setPageSymbol("ANNUAL");
        commonViewVO3.setViewFlag("2");
        customCombService.getCombinationList(commonViewVO3);

        CommonViewVO commonViewVO4 = new CommonViewVO();
        commonViewVO4.setGranularityType("U");
        commonViewVO4.setPageFlag("ALL_MONTH");
        commonViewVO4.setCostType("M");
        commonViewVO4.setPageSymbol("MONTH");
        commonViewVO4.setViewFlag("2");
        customCombService.getCombinationList(commonViewVO4);
        CommonViewVO commonViewVO5 = new CommonViewVO();
        commonViewVO5.setGranularityType("P");
        commonViewVO5.setPageFlag("ALL_MONTH");
        commonViewVO5.setCostType("M");
        commonViewVO5.setPageSymbol("MONTH");
        commonViewVO5.setViewFlag("2");
        customCombService.getCombinationList(commonViewVO5);

        CommonViewVO commonViewVO6 = new CommonViewVO();
        commonViewVO6.setGranularityType("D");
        commonViewVO6.setPageFlag("ALL_MONTH");
        commonViewVO6.setCostType("M");
        commonViewVO6.setPageSymbol("MONTH");
        commonViewVO6.setViewFlag("2");
        customCombService.getCombinationList(commonViewVO6);

        CommonViewVO commonViewVO7 = new CommonViewVO();
        commonViewVO7.setGranularityType("U");
        commonViewVO7.setPageFlag("ALL_MONTH");
        commonViewVO7.setCostType("M");
        commonViewVO7.setPageSymbol("MONTH");
        commonViewVO7.setViewFlag("0");
        customCombService.getCombinationList(commonViewVO7);
    }

    private void getCombinationListForPageFlag() {
        CommonViewVO commonViewVO8 = new CommonViewVO();
        commonViewVO8.setGranularityType("U");
        commonViewVO8.setPageFlag("ALL_MONTH");
        commonViewVO8.setCostType("M");
        commonViewVO8.setPageSymbol("MONTH");
        commonViewVO8.setViewFlag("1");
        customCombService.getCombinationList(commonViewVO8);

        CommonViewVO commonViewVO9 = new CommonViewVO();
        commonViewVO9.setGranularityType("P");
        commonViewVO9.setPageFlag("ALL_MONTH");
        commonViewVO9.setCostType("M");
        commonViewVO9.setPageSymbol("MONTH");
        commonViewVO9.setViewFlag("0");
        customCombService.getCombinationList(commonViewVO9);

        CommonViewVO commonViewVO10 = new CommonViewVO();
        commonViewVO10.setGranularityType("P");
        commonViewVO10.setPageFlag("ALL_MONTH");
        commonViewVO10.setCostType("M");
        commonViewVO10.setPageSymbol("MONTH");
        commonViewVO10.setViewFlag("1");
        customCombService.getCombinationList(commonViewVO10);

        CommonViewVO commonViewVO11 = new CommonViewVO();
        commonViewVO11.setGranularityType("P");
        commonViewVO11.setPageFlag("ALL_MONTH");
        commonViewVO11.setCostType("M");
        commonViewVO11.setPageSymbol("MONTH");
        commonViewVO11.setViewFlag("4");
        customCombService.getCombinationList(commonViewVO11);

        CommonViewVO commonViewVO12 = new CommonViewVO();
        commonViewVO12.setGranularityType("D");
        commonViewVO12.setPageFlag("ALL_MONTH");
        commonViewVO12.setCostType("M");
        commonViewVO12.setPageSymbol("MONTH");
        commonViewVO12.setViewFlag("0");
        customCombService.getCombinationList(commonViewVO12);

        CommonViewVO commonViewVO13 = new CommonViewVO();
        commonViewVO13.setGranularityType("D");
        commonViewVO13.setPageFlag("ALL_MONTH");
        commonViewVO13.setCostType("M");
        commonViewVO13.setPageSymbol("MONTH");
        commonViewVO13.setViewFlag("0");
        customCombService.getCombinationList(commonViewVO13);

        CommonViewVO commonViewVO14 = new CommonViewVO();
        commonViewVO14.setGranularityType("D");
        commonViewVO14.setPageFlag("ALL_MONTH");
        commonViewVO14.setCostType("M");
        commonViewVO14.setPageSymbol("MONTH");
        commonViewVO14.setViewFlag("1");
        customCombService.getCombinationList(commonViewVO14);

        CommonViewVO commonViewVO15 = new CommonViewVO();
        commonViewVO15.setGranularityType("D");
        commonViewVO15.setPageFlag("ALL_MONTH");
        commonViewVO15.setCostType("M");
        commonViewVO15.setPageSymbol("MONTH");
        commonViewVO15.setViewFlag("4");
        customCombService.getCombinationList(commonViewVO15);

        CommonViewVO commonViewVO16 = new CommonViewVO();
        commonViewVO16.setGranularityType("D");
        commonViewVO16.setPageFlag("ALL_MONTH");
        commonViewVO16.setCostType("M");
        commonViewVO16.setPageSymbol("MONTH");
        commonViewVO16.setViewFlag("5");
        customCombService.getCombinationList(commonViewVO16);

        CommonViewVO commonViewVO17 = new CommonViewVO();
        commonViewVO17.setGranularityType("D");
        commonViewVO17.setPageFlag("ALL_MONTH");
        commonViewVO17.setCostType("M");
        commonViewVO17.setPageSymbol("MONTH");
        commonViewVO17.setViewFlag("6");
        customCombService.getCombinationList(commonViewVO17);

        CommonViewVO commonViewVO18 = new CommonViewVO();
        commonViewVO18.setGranularityType("D");
        commonViewVO18.setPageFlag("ALL_MONTH");
        commonViewVO18.setCostType("M");
        commonViewVO18.setPageSymbol("MONTH");
        commonViewVO18.setViewFlag("7");
        customCombService.getCombinationList(commonViewVO18);

        CommonViewVO commonViewVO19 = new CommonViewVO();
        commonViewVO19.setGranularityType("D");
        commonViewVO19.setPageFlag("ALL_MONTH");
        commonViewVO19.setCostType("M");
        commonViewVO19.setPageSymbol("MONTH");
        commonViewVO19.setViewFlag("8");
        customCombService.getCombinationList(commonViewVO19);
    }

    @NotNull
    private List<DmFocViewInfoVO> getDmFocViewInfoParentCodeList() {
        List<DmFocViewInfoVO> parentList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO3 = new DmFocViewInfoVO();
        dmFocViewInfoVO3.setConnectCode("1111");
        dmFocViewInfoVO3.setGroupCode("code");
        dmFocViewInfoVO3.setViewFlag("2");
        dmFocViewInfoVO3.setGroupLevel("LV1");
        parentList.add(dmFocViewInfoVO3);

        DmFocViewInfoVO dmFocViewInfoVO33 = new DmFocViewInfoVO();
        dmFocViewInfoVO33.setConnectCode("1111");
        dmFocViewInfoVO33.setGroupCode("code");
        dmFocViewInfoVO33.setViewFlag("2");
        dmFocViewInfoVO33.setGroupLevel("LV2");
        parentList.add(dmFocViewInfoVO33);

        DmFocViewInfoVO dmFocViewInfoVO333 = new DmFocViewInfoVO();
        dmFocViewInfoVO333.setConnectCode("11311");
        dmFocViewInfoVO333.setGroupCode("co2de");
        dmFocViewInfoVO333.setViewFlag("2");
        dmFocViewInfoVO333.setGroupLevel("SHIPPING_OBJECT");
        parentList.add(dmFocViewInfoVO333);

        DmFocViewInfoVO dmFocViewInfoVO3333 = new DmFocViewInfoVO();
        dmFocViewInfoVO3333.setConnectCode("113311");
        dmFocViewInfoVO3333.setGroupCode("co2d3e");
        dmFocViewInfoVO3333.setViewFlag("2");
        dmFocViewInfoVO3333.setGroupLevel("MANUFACTURE_OBJECT");
        parentList.add(dmFocViewInfoVO3333);

        DmFocViewInfoVO dmFocViewInfoVO4 = new DmFocViewInfoVO();
        dmFocViewInfoVO4.setConnectCode("222");
        dmFocViewInfoVO4.setGroupCode("code244");
        dmFocViewInfoVO4.setViewFlag("2");
        dmFocViewInfoVO4.setGroupLevel("LV2");
        parentList.add(dmFocViewInfoVO4);

        DmFocViewInfoVO dmFocViewInfoVO5 = new DmFocViewInfoVO();
        dmFocViewInfoVO5.setConnectCode("45656");
        dmFocViewInfoVO5.setGroupCode("code24433");
        dmFocViewInfoVO5.setViewFlag("2");
        dmFocViewInfoVO5.setGroupLevel("SHIPPING_OBJECT");
        parentList.add(dmFocViewInfoVO5);

        DmFocViewInfoVO dmFocViewInfoVO6 = new DmFocViewInfoVO();
        dmFocViewInfoVO6.setConnectCode("4565633");
        dmFocViewInfoVO6.setGroupCode("code2443344");
        dmFocViewInfoVO6.setViewFlag("2");
        dmFocViewInfoVO6.setGroupLevel("MANUFACTURE_OBJECT");
        parentList.add(dmFocViewInfoVO6);

        DmFocViewInfoVO dmFocViewInfoVO7 = new DmFocViewInfoVO();
        dmFocViewInfoVO7.setConnectCode("4565633");
        dmFocViewInfoVO7.setGroupCode("code2443344");
        dmFocViewInfoVO7.setViewFlag("0");
        dmFocViewInfoVO7.setGroupLevel("MANUFACTURE_OBJECT");
        parentList.add(dmFocViewInfoVO7);

        DmFocViewInfoVO dmFocViewInfoVO8 = new DmFocViewInfoVO();
        dmFocViewInfoVO8.setConnectCode("456562233");
        dmFocViewInfoVO8.setGroupCode("code244333344");
        dmFocViewInfoVO8.setViewFlag("1");
        dmFocViewInfoVO8.setGroupLevel("MANUFACTURE_OBJECT");
        parentList.add(dmFocViewInfoVO8);

        DmFocViewInfoVO dmFocViewInfoVO9 = new DmFocViewInfoVO();
        dmFocViewInfoVO9.setConnectCode("4565622233");
        dmFocViewInfoVO9.setGroupCode("code243333344");
        dmFocViewInfoVO9.setViewFlag("1");
        dmFocViewInfoVO9.setGroupLevel("SHIPPING_OBJECT");
        parentList.add(dmFocViewInfoVO9);

        DmFocViewInfoVO dmFocViewInfoVO10 = new DmFocViewInfoVO();
        dmFocViewInfoVO10.setConnectCode("45656222233");
        dmFocViewInfoVO10.setGroupCode("code2433333344");
        dmFocViewInfoVO10.setViewFlag("4");
        dmFocViewInfoVO10.setGroupLevel("SHIPPING_OBJECT");
        parentList.add(dmFocViewInfoVO10);
        DmFocViewInfoVO dmFocViewInfoVO11 = new DmFocViewInfoVO();
        dmFocViewInfoVO11.setConnectCode("222");
        dmFocViewInfoVO11.setGroupCode("code24333333344");
        dmFocViewInfoVO11.setViewFlag("4");
        dmFocViewInfoVO11.setGroupLevel("MANUFACTURE_OBJECT");
        parentList.add(dmFocViewInfoVO11);
        DmFocViewInfoVO dmFocViewInfoVO12 = new DmFocViewInfoVO();
        dmFocViewInfoVO12.setConnectCode("3355");
        dmFocViewInfoVO12.setGroupCode("code2433388");
        dmFocViewInfoVO12.setViewFlag("4");
        dmFocViewInfoVO12.setGroupLevel("LV1");
        parentList.add(dmFocViewInfoVO12);

        DmFocViewInfoVO dmFocViewInfoVO13 = new DmFocViewInfoVO();
        dmFocViewInfoVO13.setConnectCode("335533");
        dmFocViewInfoVO13.setGroupCode("code24888");
        dmFocViewInfoVO13.setViewFlag("4");
        dmFocViewInfoVO13.setGroupLevel("LV2");
        parentList.add(dmFocViewInfoVO13);

        addDimensionViewFlagList(parentList);
        return parentList;
    }

    private void addDimensionViewFlagList(List<DmFocViewInfoVO> parentList) {
        DmFocViewInfoVO dmFocViewInfoVO14 = new DmFocViewInfoVO();
        dmFocViewInfoVO14.setConnectCode("3355333");
        dmFocViewInfoVO14.setGroupCode("code24888");
        dmFocViewInfoVO14.setViewFlag("5");
        dmFocViewInfoVO14.setGroupLevel("LV1");
        parentList.add(dmFocViewInfoVO14);

        DmFocViewInfoVO dmFocViewInfoVO15 = new DmFocViewInfoVO();
        dmFocViewInfoVO15.setConnectCode("33553333");
        dmFocViewInfoVO15.setGroupCode("code248838");
        dmFocViewInfoVO15.setViewFlag("5");
        dmFocViewInfoVO15.setGranularityType("D");
        dmFocViewInfoVO15.setGroupLevel("MANUFACTURE_OBJECT");
        parentList.add(dmFocViewInfoVO15);

        DmFocViewInfoVO dmFocViewInfoVO16 = new DmFocViewInfoVO();
        dmFocViewInfoVO16.setConnectCode("32311333");
        dmFocViewInfoVO16.setGroupCode("code23488388");
        dmFocViewInfoVO16.setViewFlag("6");
        dmFocViewInfoVO16.setGranularityType("D");
        dmFocViewInfoVO16.setGroupLevel("MANUFACTURE_OBJECT");
        parentList.add(dmFocViewInfoVO16);

        DmFocViewInfoVO dmFocViewInfoVO17 = new DmFocViewInfoVO();
        dmFocViewInfoVO17.setConnectCode("3231133333");
        dmFocViewInfoVO17.setGroupCode("code2348844388");
        dmFocViewInfoVO17.setViewFlag("7");
        dmFocViewInfoVO17.setGranularityType("D");
        dmFocViewInfoVO17.setGroupLevel("MANUFACTURE_OBJECT");
        parentList.add(dmFocViewInfoVO17);

        DmFocViewInfoVO dmFocViewInfoVO18 = new DmFocViewInfoVO();
        dmFocViewInfoVO18.setConnectCode("323113333333");
        dmFocViewInfoVO18.setGroupCode("code234884423388");
        dmFocViewInfoVO18.setViewFlag("8");
        dmFocViewInfoVO18.setGranularityType("D");
        dmFocViewInfoVO18.setGroupLevel("MANUFACTURE_OBJECT");
        parentList.add(dmFocViewInfoVO18);
    }

    @Test
    public void getCombinationNameListTest() throws Exception {
        CombinationVO combinationVO = new CombinationVO();
        customCombService.getCombinationNameList(combinationVO);
        Assert.assertTrue(true);
    }

    @Test
    public void getGroupLevelListTest() throws Exception {
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Mockito.when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(currentRoleDataPermission);
        CombinationVO combinationVO = new CombinationVO();
        combinationVO.setGranularityType("P");
        combinationVO.setViewFlag("3");
        customCombService.getGroupLevelList(combinationVO);

        combinationVO.setGranularityType("P");
        combinationVO.setViewFlag("4");
        customCombService.getGroupLevelList(combinationVO);

        combinationVO.setViewFlag("4");
        combinationVO.setGranularityType("D");
        customCombService.getGroupLevelList(combinationVO);

        combinationVO.setViewFlag("0");
        customCombService.getGroupLevelList(combinationVO);

        combinationVO.setViewFlag("3");
        customCombService.getGroupLevelList(combinationVO);

        combinationVO.setViewFlag("6");
        customCombService.getGroupLevelList(combinationVO);

        combinationVO.setViewFlag("2");
        customCombService.getGroupLevelList(combinationVO);

        combinationVO.setViewFlag("5");
        customCombService.getGroupLevelList(combinationVO);

        combinationVO.setViewFlag("6");
        customCombService.getGroupLevelList(combinationVO);

        combinationVO.setViewFlag("9");
        customCombService.getGroupLevelList(combinationVO);
        Assert.assertTrue(true);

    }

    @Test
    public void getGroupLevelList3Test() throws Exception {
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        Set<String> lv2DimensionSet  = new HashSet<>();
        Set<String> lv3DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("111");
        lv2DimensionSet.add("222");
        lv2DimensionSet.add("333");
        lv3DimensionSet.add("55555");

        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        currentRoleDataPermission.setLv2DimensionSet(lv2DimensionSet);
        Mockito.when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(currentRoleDataPermission);
        List<ViewInfoVO> allProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setLv2ProdRndTeamCode("9999");
        viewInfoVO.setLv1ProdRndTeamCode("4552");
        viewInfoVO.setLv3ProdRndTeamCode("334");
        allProdDimensionList.add(viewInfoVO);
        Mockito.when(dataDimensionService.getCurrentLv2ProdRndTeamList(any(),any())).thenReturn(allProdDimensionList);

        CombinationVO combinationVO = new CombinationVO();
        combinationVO.setGranularityType("P");
        combinationVO.setViewFlag("3");
        customCombService.getGroupLevelList(combinationVO);
        Assert.assertTrue(true);
    }

    @Test
    public void getGroupLevelList4Test() throws Exception {
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        Set<String> lv2DimensionSet  = new HashSet<>();
        Set<String> lv3DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("111");
        lv1DimensionSet.add("NO_PERMISSION");
        lv2DimensionSet.add("222");
        lv2DimensionSet.add("NO_PERMISSION");
        lv3DimensionSet.add("NO_PERMISSION");
        lv3DimensionSet.add("4455");

        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        currentRoleDataPermission.setLv2DimensionSet(lv2DimensionSet);
        Mockito.when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(currentRoleDataPermission);
        List<ViewInfoVO> allProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setLv2ProdRndTeamCode("9999");
        viewInfoVO.setLv1ProdRndTeamCode("4552");
        viewInfoVO.setLv3ProdRndTeamCode("334");
        allProdDimensionList.add(viewInfoVO);
        Mockito.when(dataDimensionService.getCurrentLv2ProdRndTeamList(any(),any())).thenReturn(allProdDimensionList);

        CombinationVO combinationVO = new CombinationVO();
        combinationVO.setGranularityType("P");
        combinationVO.setViewFlag("4");
        combinationVO.setCostType("M");
        customCombService.getGroupLevelList(combinationVO);
        Assert.assertTrue(true);
    }

    @Test
    public void createCombination1Test() throws CommonApplicationException {
        CombinationVO combinationVO=new CombinationVO();
        List<DmCustomCombVO> customCombNameList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setCustomId(155L);
        customCombNameList.add(dmCustomCombVO);
        when(dmFocCustomCombDao.getCustomCombListByName(any())).thenReturn(customCombNameList);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = customCombService.createCombination(combinationVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void createCombination2Test() throws CommonApplicationException {
        CombinationVO combinationVO=new CombinationVO();
        List<DmCustomCombVO> customCombNameList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setCustomId(155L);
        customCombNameList.add(dmCustomCombVO);
        combinationVO.setCustomVOList(customCombNameList);
        combinationVO.setPageFlag("all");

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = customCombService.createCombination(combinationVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void createCombination3Test() throws CommonApplicationException {
        CombinationVO combinationVO=new CombinationVO();
        List<DmCustomCombVO> customCombNameList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setCustomId(155L);
        customCombNameList.add(dmCustomCombVO);
        combinationVO.setCustomVOList(customCombNameList);
        combinationVO.setPageFlag("ALL");
        DataPermissionsVO currentRoleDataPermission=new DataPermissionsVO();
        Set<String> set = new HashSet<>();
        set.add("10");
        set.add("15");
        currentRoleDataPermission.setLv0DimensionSet(set);
        currentRoleDataPermission.setLv1DimensionSet(set);
        currentRoleDataPermission.setLv2DimensionSet(set);
        when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(currentRoleDataPermission);

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = customCombService.createCombination(combinationVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void createCombination4Test() throws CommonApplicationException {
        CombinationVO combinationVO=new CombinationVO();
        List<DmCustomCombVO> customCombNameList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setCustomId(155L);
        customCombNameList.add(dmCustomCombVO);
        combinationVO.setCustomVOList(customCombNameList);
        combinationVO.setPageFlag("ALL");
        combinationVO.setCostType("M");
        DataPermissionsVO currentRoleDataPermission=new DataPermissionsVO();
        Set<String> set = new HashSet<>();
        set.add("10");
        set.add("15");
        currentRoleDataPermission.setLv0DimensionSet(set);
        currentRoleDataPermission.setLv1DimensionSet(set);
        currentRoleDataPermission.setLv2DimensionSet(set);
        when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(currentRoleDataPermission);
        when(customCommonService.filterAnotherPageData(combinationVO,customCombNameList)).thenReturn(customCombNameList);

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = customCombService.createCombination(combinationVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void renameCombination() {
        CombinationVO combinationVO=new CombinationVO();
        List<DmCustomCombVO> customCombNameList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setCustomId(155L);
        customCombNameList.add(dmCustomCombVO);
        when(dmFocCustomCombDao.getCustomCombListByName(any())).thenReturn(customCombNameList);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = customCombService.renameCombination(combinationVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void renameCombination2Test() {
        CombinationVO combinationVO=new CombinationVO();
        List<DmCustomCombVO> customCombNameList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setCustomId(155L);
        customCombNameList.add(dmCustomCombVO);
        combinationVO.setCustomVOList(customCombNameList);
        combinationVO.setPageFlag("all");

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = customCombService.renameCombination(combinationVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void renameCombination3Test() {
        CombinationVO combinationVO=new CombinationVO();
        List<DmCustomCombVO> customCombNameList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setCustomId(155L);
        customCombNameList.add(dmCustomCombVO);
        combinationVO.setCustomVOList(customCombNameList);
        combinationVO.setPageFlag("ALL");
        combinationVO.setPageSymbol("page");

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = customCombService.renameCombination(combinationVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void updateCombination() {
        CombinationVO combinationVO=new CombinationVO();
        List<DmCustomCombVO> customCombNameList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setCustomId(155L);
        customCombNameList.add(dmCustomCombVO);
        combinationVO.setCustomVOList(customCombNameList);
        combinationVO.setPageFlag("ALL");
        DataPermissionsVO currentRoleDataPermission=new DataPermissionsVO();
        Set<String> set = new HashSet<>();
        set.add("10");
        set.add("15");
        currentRoleDataPermission.setLv0DimensionSet(set);
        currentRoleDataPermission.setLv1DimensionSet(set);
        currentRoleDataPermission.setLv2DimensionSet(set);
        when(commonService.getCurrentRoleDataPermission(any())).thenReturn(currentRoleDataPermission);

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = customCombService.updateCombination(combinationVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void deleteCombination() {
        CombinationVO combinationVO = new CombinationVO();
        combinationVO.setPageFlag("P");
        ResultDataVO resultDataVO = customCombService.deleteCombination(combinationVO);
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void deleteCombination2Test() {
        CombinationVO combinationVO = new CombinationVO();
        combinationVO.setPageFlag("ALL");
        ResultDataVO resultDataVO = customCombService.deleteCombination(combinationVO);
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void getProdRndTeamTree() {
        CombinationVO combinationVO=new CombinationVO();
        List<DmCustomCombVO> customCombNameList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setCustomId(155L);
        customCombNameList.add(dmCustomCombVO);
        combinationVO.setCustomVOList(customCombNameList);
        combinationVO.setPageFlag("ALL");
        combinationVO.setGranularityType("G");
        combinationVO.setPageSymbol("all");
        combinationVO.setCustomId(19L);
        combinationVO.setCostType("P");
        DataPermissionsVO currentRoleDataPermission=new DataPermissionsVO();
        Set<String> set = new HashSet<>();
        set.add("10");
        set.add("15");
        currentRoleDataPermission.setLv0DimensionSet(set);
        currentRoleDataPermission.setLv1DimensionSet(set);
        currentRoleDataPermission.setLv2DimensionSet(set);
        List<DmFocViewInfoVO> customCodeList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setGroupLevel("CBG");
        dmFocViewInfoVO.setConnectCode("885FF");
        customCodeList.add(dmFocViewInfoVO);

        when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(currentRoleDataPermission);
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(115L);
        when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(dmFocVersionInfoDTO);
        when(dmFocVersionDao.findVersionIdByDataType(any(),any())).thenReturn(dmFocVersionInfoDTO);
        when(dmFocCustomCombDao.getCombinationList(any())).thenReturn(customCodeList);

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = customCombService.getProdRndTeamTree(combinationVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void getProdRndTeamTree2Test() {
        CombinationVO combinationVO=new CombinationVO();
        List<DmCustomCombVO> customCombNameList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setCustomId(155L);
        customCombNameList.add(dmCustomCombVO);
        combinationVO.setCustomVOList(customCombNameList);
        combinationVO.setPageFlag("ALL");
        combinationVO.setGranularityType("P");
        combinationVO.setPageSymbol("all");
        combinationVO.setCostType("M");
        combinationVO.setCustomId(19L);
        combinationVO.setFilterGroupLevel("F");
        List<String> list = new ArrayList<>();
        list.add("996");
        list.add("1419");
        combinationVO.setLv1NoPermissList(list);
        combinationVO.setLv2NoPermissList(list);
        DataPermissionsVO currentRoleDataPermission=new DataPermissionsVO();
        Set<String> set = new HashSet<>();
        set.add("10");
        set.add("15");
        currentRoleDataPermission.setLv0DimensionSet(set);
        currentRoleDataPermission.setLv1DimensionSet(set);
        currentRoleDataPermission.setLv2DimensionSet(set);
        List<DmFocViewInfoVO> customCodeList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setGroupLevel("CBG");
        dmFocViewInfoVO.setConnectCode("885FF");
        dmFocViewInfoVO.setLv1ProdRndTeamCode("lv1");
        customCodeList.add(dmFocViewInfoVO);

        when(commonService.getCurrentRoleDataPermission(any())).thenReturn(currentRoleDataPermission);
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(115L);
        when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(dmFocVersionInfoDTO);
        when(dmFocVersionDao.findVersionIdByDataType(any(),any())).thenReturn(dmFocVersionInfoDTO);
        when(dmFocMadeCustomCombDao.getManufacutreCombinationList(any())).thenReturn(customCodeList);

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = customCombService.getProdRndTeamTree(combinationVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void getCombinationList() {
        CommonViewVO commonViewVO=new CommonViewVO();
        ResultDataVO combinationList = customCombService.getCombinationList(commonViewVO);
        Assert.assertNotNull(combinationList);
    }

    @Test
    public void getCombinationList2Test() {
        CommonViewVO commonViewVO=new CommonViewVO();
        commonViewVO.setGranularityType("U");
        commonViewVO.setPageFlag("all");
        commonViewVO.setViewFlag("3");
        commonViewVO.setCostType("P");

        List<DmFocViewInfoVO> dmCustomCombList = new ArrayList<>();
        DmFocViewInfoVO dmFocVersionInfoDTO = new DmFocViewInfoVO();
        dmFocVersionInfoDTO.setConnectCode("code");
        dmFocVersionInfoDTO.setGroupLevel("lv3");
        dmCustomCombList.add(dmFocVersionInfoDTO);
        when(dmFocCustomCombDao.getCombinationList(any())).thenReturn(dmCustomCombList);

        DataPermissionsVO currentRoleDataPermission=new DataPermissionsVO();
        currentRoleDataPermission.setRoleId(10);
        when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(currentRoleDataPermission);

        ResultDataVO combinationList = customCombService.getCombinationList(commonViewVO);
        Assert.assertNotNull(combinationList);
    }

    @Test
    public void getCombinationList3Test() {
        CommonViewVO commonViewVO=new CommonViewVO();
        commonViewVO.setGranularityType("U");
        commonViewVO.setPageFlag("ALL");
        commonViewVO.setViewFlag("3");
        commonViewVO.setCostType("P");
        Set<String> set = new HashSet<>();
        set.add("NO_PERMISSION");
        commonViewVO.setLv0DimensionSet(set);

        List<DmFocViewInfoVO> dmCustomCombList = new ArrayList<>();
        DmFocViewInfoVO dmFocVersionInfoDTO = new DmFocViewInfoVO();
        dmFocVersionInfoDTO.setConnectCode("code");
        dmFocVersionInfoDTO.setGroupLevel("CATEGORY");
        DmFocViewInfoVO dmFocVersionInfoDTO2 = new DmFocViewInfoVO();
        dmFocVersionInfoDTO2.setConnectCode("code2");
        dmFocVersionInfoDTO2.setGroupLevel("CEG");
        DmFocViewInfoVO dmFocVersionInfoDTO3 = new DmFocViewInfoVO();
        dmFocVersionInfoDTO3.setConnectCode("code2");
        dmFocVersionInfoDTO3.setGroupLevel("MODL");
        DmFocViewInfoVO dmFocVersionInfoDTO4 = new DmFocViewInfoVO();
        dmFocVersionInfoDTO4.setConnectCode("code2");
        dmFocVersionInfoDTO4.setGroupLevel("MODEL");

        dmCustomCombList.add(dmFocVersionInfoDTO);
        dmCustomCombList.add(dmFocVersionInfoDTO2);
        dmCustomCombList.add(dmFocVersionInfoDTO3);
        dmCustomCombList.add(dmFocVersionInfoDTO4);
        when(dmFocCustomCombDao.getCombinationList(any())).thenReturn(dmCustomCombList);

        DataPermissionsVO currentRoleDataPermission=new DataPermissionsVO();
        currentRoleDataPermission.setRoleId(10);
        when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(currentRoleDataPermission);

        ResultDataVO combinationList = customCombService.getCombinationList(commonViewVO);
        Assert.assertNotNull(combinationList);
    }

    @Test
    public void getCombinationList9Test() {
        CommonViewVO commonViewVO=new CommonViewVO();
        commonViewVO.setGranularityType("D");
        commonViewVO.setPageFlag("ALL_MONTH");
        commonViewVO.setPageSymbol("MONTH");
        commonViewVO.setViewFlag("3");
        commonViewVO.setCostType("P");
        Set<String> set = new HashSet<>();
        set.add("NO_PERMISSION");
        commonViewVO.setLv0DimensionSet(set);

        List<DmFocViewInfoVO> dmCustomCombList = new ArrayList<>();
        DmFocViewInfoVO dmFocVersionInfoDTO = new DmFocViewInfoVO();
        dmFocVersionInfoDTO.setConnectCode("code");
        dmFocVersionInfoDTO.setGroupLevel("CATEGORY");
        DmFocViewInfoVO dmFocVersionInfoDTO2 = new DmFocViewInfoVO();
        dmFocVersionInfoDTO2.setConnectCode("code2");
        dmFocVersionInfoDTO2.setGroupLevel("CEG");
        DmFocViewInfoVO dmFocVersionInfoDTO3 = new DmFocViewInfoVO();
        dmFocVersionInfoDTO3.setConnectCode("code2");
        dmFocVersionInfoDTO3.setGroupLevel("MODL");
        DmFocViewInfoVO dmFocVersionInfoDTO4 = new DmFocViewInfoVO();
        dmFocVersionInfoDTO4.setConnectCode("code2");
        dmFocVersionInfoDTO4.setGroupLevel("MODEL");

        dmCustomCombList.add(dmFocVersionInfoDTO);
        dmCustomCombList.add(dmFocVersionInfoDTO2);
        dmCustomCombList.add(dmFocVersionInfoDTO3);
        dmCustomCombList.add(dmFocVersionInfoDTO4);
        when(dmFocCustomCombDao.getCombinationList(any())).thenReturn(dmCustomCombList);

        DataPermissionsVO currentRoleDataPermission=new DataPermissionsVO();
        currentRoleDataPermission.setRoleId(10);
        when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(currentRoleDataPermission);

        ResultDataVO combinationList = customCombService.getCombinationList(commonViewVO);
        Assert.assertNotNull(combinationList);
    }

    @Test
    public void getCombinationNameList() {
        CombinationVO combinationVO=new CombinationVO();
        ResultDataVO combinationNameList = customCombService.getCombinationNameList(combinationVO);
        Assert.assertNotNull(combinationNameList);
    }

    @Test
    public void initEnableFlagTest() {
        customCombService.initEnableFlag();
        Mockito.when(dmFocCustomCombDao.getCustomCombList(Mockito.any())).thenReturn(new ArrayList<>());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Mockito.when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(currentRoleDataPermission);
        customCombService.initEnableFlag();
        List<DmCustomCombVO> customCombList = new ArrayList<>();
        DmCustomCombVO com = new DmCustomCombVO();
        com.setGroupLevel("ICT");
        com.setGroupCode("1");
        customCombList.add(com);
        DmCustomCombVO com2 = new DmCustomCombVO();
        com2.setGroupLevel("LV1");
        com2.setGroupCode("2");
        customCombList.add(com2);
        DmCustomCombVO com3 = new DmCustomCombVO();
        com3.setGroupLevel("LV2");
        com3.setGroupCode("3");
        customCombList.add(com3);
        DmCustomCombVO com4 = new DmCustomCombVO();
        com4.setGroupLevel("LV3");
        com4.setGroupCode("4");
        customCombList.add(com4);
        DmCustomCombVO com5 = new DmCustomCombVO();
        com5.setGroupLevel("LV4");
        com5.setGroupCode("5");
        customCombList.add(com5);
        DmFocVersionInfoDTO dmFocVersionVO = new DmFocVersionInfoDTO();
        dmFocVersionVO.setVersionId(11L);
        Mockito.when(dmFocCustomCombDao.getCustomCombList(Mockito.any())).thenReturn(customCombList);
        Mockito.when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(currentRoleDataPermission);
        Mockito.when(dmFocVersionDao.findVersionIdByDataType(any(),any())).thenReturn(dmFocVersionVO);
        Mockito.when(dmFocVersionDao.findVersionIdByDataType(any(),any())).thenReturn(dmFocVersionVO);
        customCombService.initEnableFlag();

        Set<String> set = new HashSet<>();
        set.add("NO_PERMISSION");
        currentRoleDataPermission.setLv0DimensionSet(set);
        currentRoleDataPermission.setLv1DimensionSet(set);
        currentRoleDataPermission.setLv2DimensionSet(set);
        customCombService.initEnableFlag();

        set.add("ICT");
        set.add("LV1");
        set.add("LV2");
        set.add("LV3");
        set.add("LV4");
        set.add("LV5");
        set.remove("NO_PERMISSION");
        DmCustomCombVO com6 = new DmCustomCombVO();
        com6.setGroupLevel("ICT");
        com6.setGroupCode("ICT");
        customCombList.add(com6);
        DmCustomCombVO com7 = new DmCustomCombVO();
        com7.setGroupLevel("LV1");
        com7.setGroupCode("LV1");
        customCombList.add(com7);
        DmCustomCombVO com8 = new DmCustomCombVO();
        com8.setGroupLevel("LV2");
        com8.setGroupCode("LV2");
        customCombList.add(com8);
        DmCustomCombVO com9 = new DmCustomCombVO();
        com9.setGroupLevel("LV3");
        com9.setGroupCode("LV3");
        customCombList.add(com9);
        DmCustomCombVO com10 = new DmCustomCombVO();
        com10.setGroupLevel("LV4");
        com10.setGroupCode("LV4");
        customCombList.add(com10);
        customCombService.initEnableFlag();
        Assert.assertTrue(true);
    }

    @Test
    public void initEnableFlag2Test() {
        DmFocDataRefreshStatus dmFocDataRefreshStatus = new DmFocDataRefreshStatus();
        dmFocDataRefreshStatus.setTaskId(22L);

        Mockito.when(dataRefreshStatusDao.findDmFocDataRefreshStatus(Mockito.any())).thenReturn(dmFocDataRefreshStatus);

        Mockito.when(dmFocCustomCombDao.getCustomCombList(Mockito.any())).thenReturn(new ArrayList<>());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Mockito.when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(currentRoleDataPermission);
        customCombService.initEnableFlag();

        DmFocDataRefreshStatus dmFocDataRefreshStatus2 = new DmFocDataRefreshStatus();
        dmFocDataRefreshStatus2.setTaskId(33L);

        Mockito.when(dataRefreshStatusDao.findDmFocDataRefreshStatusByDay(Mockito.any())).thenReturn(dmFocDataRefreshStatus2);
        customCombService.initEnableFlag();
        Assert.assertTrue(true);
    }

    @Test
    public void initEnableFlag3Test() {

        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Mockito.when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(currentRoleDataPermission);

        DmFocDataRefreshStatus dmFocDataRefreshStatus2 = new DmFocDataRefreshStatus();
        dmFocDataRefreshStatus2.setTaskId(33L);

        Mockito.when(dataRefreshStatusDao.findDmFocDataRefreshStatusByDay(Mockito.any())).thenReturn(dmFocDataRefreshStatus2);
        customCombService.initEnableFlag();
        Assert.assertTrue(true);
    }

    @Test
    public void initEnableFlag4Test() {

        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Mockito.when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(currentRoleDataPermission);

        DmFocDataRefreshStatus dmFocDataRefreshStatus = new DmFocDataRefreshStatus();
        dmFocDataRefreshStatus.setTaskId(33L);

        Mockito.when(dataRefreshStatusDao.findDmFocDataRefreshStatus(Mockito.any())).thenReturn(dmFocDataRefreshStatus);
        customCombService.initEnableFlag();
        Assert.assertTrue(true);
    }

    @Test
    public void getPermissionForProdTeamCodeListTest() throws Exception {
        CommonViewVO commonViewVO = new CommonViewVO();
        Method method = PowerMockito.method(CustomCombService.class, "getPermissionForProdTeamCodeList", CommonViewVO.class);
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Mockito.when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(currentRoleDataPermission);
        method.invoke(customCombService, commonViewVO);

        Set<String> dimensionSet = new HashSet<>();
        dimensionSet.add("1");
        currentRoleDataPermission.setLv0DimensionSet(dimensionSet);
        method.invoke(customCombService, commonViewVO);

        currentRoleDataPermission.setLv0DimensionSet(dimensionSet);
        currentRoleDataPermission.setLv1DimensionSet(dimensionSet);
        method.invoke(customCombService, commonViewVO);

        currentRoleDataPermission.setLv0DimensionSet(dimensionSet);
        currentRoleDataPermission.setLv1DimensionSet(dimensionSet);
        currentRoleDataPermission.setLv2DimensionSet(dimensionSet);
        method.invoke(customCombService, commonViewVO);

        currentRoleDataPermission.setLv0DimensionSet(dimensionSet);
        currentRoleDataPermission.setLv1DimensionSet(dimensionSet);
        currentRoleDataPermission.setLv2DimensionSet(dimensionSet);
        method.invoke(customCombService, commonViewVO);
        Assert.assertTrue(true);
    }

    @Test
    public void getPermissionForProdTeamCodeListTwoTest() throws Exception {
        CommonViewVO commonViewVO = new CommonViewVO();
        Method method = PowerMockito.method(CustomCombService.class, "getPermissionForProdTeamCodeList", CommonViewVO.class);
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Mockito.when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(currentRoleDataPermission);
        method.invoke(customCombService, commonViewVO);

        Set<String> dimensionSet = new HashSet<>();
        dimensionSet.add("1");
        currentRoleDataPermission.setLv0DimensionSet(dimensionSet);
        currentRoleDataPermission.setLv1DimensionSet(dimensionSet);
        currentRoleDataPermission.setLv2DimensionSet(dimensionSet);
        method.invoke(customCombService, commonViewVO);

        currentRoleDataPermission.setLv0DimensionSet(dimensionSet);
        currentRoleDataPermission.setLv1DimensionSet(dimensionSet);
        method.invoke(customCombService, commonViewVO);

        currentRoleDataPermission.setLv1DimensionSet(dimensionSet);
        currentRoleDataPermission.setLv2DimensionSet(dimensionSet);
        method.invoke(customCombService, commonViewVO);

        currentRoleDataPermission.setLv0DimensionSet(dimensionSet);
        currentRoleDataPermission.setLv1DimensionSet(dimensionSet);
        currentRoleDataPermission.setLv2DimensionSet(dimensionSet);
        List<DmFocViewInfoVO> lv1ProdCodeList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setLv1ProdRndTeamCode("1");
        lv1ProdCodeList.add(dmFocViewInfoVO);
        DmFocViewInfoVO dmFocViewInfoVO2 = new DmFocViewInfoVO();
        dmFocViewInfoVO2.setLv1ProdRndTeamCode("2");
        lv1ProdCodeList.add(dmFocViewInfoVO2);
        Mockito.when(dmFocViewInfoDao.reverseFindLv1ProdCode(commonViewVO)).thenReturn(lv1ProdCodeList);

        List<DmFocViewInfoVO> lv2ProdCodeList = new ArrayList<>();
        DmFocViewInfoVO lv2DmFocViewInfoVO = new DmFocViewInfoVO();
        lv2DmFocViewInfoVO.setLv2ProdRndTeamCode("2");
        lv2ProdCodeList.add(lv2DmFocViewInfoVO);
        DmFocViewInfoVO lv2DmFocViewInfoVO2 = new DmFocViewInfoVO();
        lv2DmFocViewInfoVO2.setLv2ProdRndTeamCode("3");
        lv2ProdCodeList.add(lv2DmFocViewInfoVO2);
        method.invoke(customCombService, commonViewVO);

        commonViewVO.setGranularityType("P");
        List<DmFocViewInfoVO> lv1ProdCodes = new ArrayList<>();
        DmFocViewInfoVO dm1 = new DmFocViewInfoVO();
        dm1.setLv1ProdRndTeamCode("1");
        lv1ProdCodes.add(dm1);
        DmFocViewInfoVO dm2 = new DmFocViewInfoVO();
        dm2.setLv1ProdRndTeamCode("2");
        lv1ProdCodes.add(dm2);
        DmFocViewInfoVO dm6 = new DmFocViewInfoVO();
        dm6.setLv1ProdRndTeamCode("6");
        lv1ProdCodes.add(dm6);
        method.invoke(customCombService, commonViewVO);
        Assert.assertTrue(true);
    }

    @Test
    public void getProdTeamLevelTest() throws Exception {
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        List<String> groupLevelList = new ArrayList<>();
        CombinationVO combinationVO = new CombinationVO();
        Method method = PowerMockito.method(CustomCombService.class, "getProdTeamLevel", List.class, CombinationVO.class);
        method.invoke(customCombService, groupLevelList, combinationVO);

        Set<String> dimensionSet = new HashSet<>();
        dimensionSet.add("NO_PERMISSION");
        combinationVO.setGranularityType("D");
        currentRoleDataPermission.setLv0DimensionSet(dimensionSet);
        currentRoleDataPermission.setLv1DimensionSet(dimensionSet);
        currentRoleDataPermission.setLv2DimensionSet(dimensionSet);
        method.invoke(customCombService, groupLevelList, combinationVO);

        dimensionSet.add("1");
        dimensionSet.remove("NO_PERMISSION");
        method.invoke(customCombService, groupLevelList, combinationVO);

        combinationVO.setGranularityType("E");
        combinationVO.setViewFlag("0");
        method.invoke(customCombService, groupLevelList, combinationVO);

        combinationVO.setViewFlag("1");
        method.invoke(customCombService, groupLevelList, combinationVO);

        combinationVO.setViewFlag("2");
        method.invoke(customCombService, groupLevelList, combinationVO);
        combinationVO.setViewFlag("3");
        method.invoke(customCombService, groupLevelList, combinationVO);
        Assert.assertTrue(true);
    }

    @Test
    public void getSubProdTeamLevelTest() throws Exception {
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        List<String> groupLevelList = new ArrayList<>();
        CombinationVO combinationVO = new CombinationVO();
        boolean lv2PermissionFlag = false;
        Method method = PowerMockito.method(CustomCombService.class, "getSubProdTeamLevel", List.class, CombinationVO.class, boolean.class);
        Set<String> lv3DimensionSet = new HashSet<>();
        method.invoke(customCombService, groupLevelList, combinationVO, lv2PermissionFlag);

        lv3DimensionSet.add("1");
        method.invoke(customCombService, groupLevelList, combinationVO, lv2PermissionFlag);

        lv3DimensionSet.add("NO_PERMISSION");
        method.invoke(customCombService, groupLevelList, combinationVO, lv2PermissionFlag);

        lv3DimensionSet.remove("NO_PERMISSION");
        method.invoke(customCombService, groupLevelList, combinationVO, lv2PermissionFlag);

        combinationVO.setGranularityType("D");
        combinationVO.setViewFlag("0");
        method.invoke(customCombService, groupLevelList, combinationVO, lv2PermissionFlag);

        combinationVO.setViewFlag("1");
        method.invoke(customCombService, groupLevelList, combinationVO, lv2PermissionFlag);

        combinationVO.setViewFlag("2");
        method.invoke(customCombService, groupLevelList, combinationVO, lv2PermissionFlag);

        combinationVO.setViewFlag("3");
        method.invoke(customCombService, groupLevelList, combinationVO, lv2PermissionFlag);

        combinationVO.setViewFlag("4");
        method.invoke(customCombService, groupLevelList, combinationVO, lv2PermissionFlag);

        lv2PermissionFlag = true;
        combinationVO.setViewFlag("5");
        method.invoke(customCombService, groupLevelList, combinationVO, lv2PermissionFlag);

        combinationVO.setViewFlag("6");
        method.invoke(customCombService, groupLevelList, combinationVO, lv2PermissionFlag);

        combinationVO.setViewFlag("7");
        method.invoke(customCombService, groupLevelList, combinationVO, lv2PermissionFlag);

        combinationVO.setViewFlag("8");
        method.invoke(customCombService, groupLevelList, combinationVO, lv2PermissionFlag);

        combinationVO.setViewFlag("9");
        method.invoke(customCombService, groupLevelList, combinationVO, lv2PermissionFlag);

        combinationVO.setViewFlag("6");
        method.invoke(customCombService, groupLevelList, combinationVO, lv2PermissionFlag);

        combinationVO.setGranularityType("U");
        combinationVO.setViewFlag("3");
        method.invoke(customCombService, groupLevelList, combinationVO, lv2PermissionFlag);
        Assert.assertTrue(true);
    }

    @Test
    public void getProdRndTeamCodeTreeTest() throws Exception {
        CommonViewVO commonViewVO = new CommonViewVO();
        Method method = PowerMockito.method(CustomCombService.class, "getProdRndTeamCodeTree", CommonViewVO.class);
        method.invoke(customCombService, commonViewVO);

        commonViewVO.setFilterGroupLevel("1");
        method.invoke(customCombService, commonViewVO);
        Assert.assertTrue(true);
    }

    @Test
    public void setPermissionFlagTest() throws Exception {
        CommonViewVO commonViewVO = new CommonViewVO();
        List<DmFocViewInfoVO> dmCustomCombList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfo = new DmFocViewInfoVO();
        dmFocViewInfo.setGroupCode("111");
        dmFocViewInfo.setGroupLevel("LV1");
        dmCustomCombList.add(dmFocViewInfo);
        List<String> lv1NoPermissList = new ArrayList<>();
        lv1NoPermissList.add("211");
        commonViewVO.setLv1NoPermissList(lv1NoPermissList);
        commonViewVO.setLv2NoPermissList(lv1NoPermissList);

        List<DmFocViewInfoVO> allGroupLevelConditionList = new ArrayList<>();

        Method method = PowerMockito.method(CustomCombService.class, "setPermissionFlag", CommonViewVO.class, List.class,List.class);
        method.invoke(customCombService, commonViewVO, dmCustomCombList, allGroupLevelConditionList);

        List<String> list = new ArrayList<>();
        list.add("1");

        commonViewVO.setLv1NoPermissList(list);
        commonViewVO.setLv2NoPermissList(list);
        method.invoke(customCombService, commonViewVO, dmCustomCombList, allGroupLevelConditionList);

        commonViewVO.setLv1NoPermissList(list);
        commonViewVO.setLv2NoPermissList(list);
        method.invoke(customCombService, commonViewVO, dmCustomCombList, allGroupLevelConditionList);

        commonViewVO.setLv1NoPermissList(list);
        commonViewVO.setLv2NoPermissList(list);
        method.invoke(customCombService, commonViewVO, dmCustomCombList, allGroupLevelConditionList);

        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setGroupCode("1");
        dmFocViewInfoVO.setGroupLevel("LV1");
        dmCustomCombList.add(dmFocViewInfoVO);
        DmFocViewInfoVO dmFocViewInfoVO2 = new DmFocViewInfoVO();
        dmFocViewInfoVO2.setGroupCode("2");
        dmFocViewInfoVO2.setGroupLevel("LV1");
        dmCustomCombList.add(dmFocViewInfoVO2);
        method.invoke(customCombService, commonViewVO, dmCustomCombList, allGroupLevelConditionList);
        Assert.assertTrue(true);
    }

    @Test
    public void setProdTeamCodeListTest() throws Exception {
        CommonViewVO commonViewVO = new CommonViewVO();
        List<DmFocViewInfoVO> dmCustomCombList = new ArrayList<>();
        Method method = PowerMockito.method(CustomCombService.class, "setProdTeamCodeList", CommonViewVO.class, List.class);
        method.invoke(customCombService, commonViewVO, dmCustomCombList);

        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmCustomCombList.add(dmFocViewInfoVO);
        DmFocViewInfoVO dmFocViewInfoVO2 = new DmFocViewInfoVO();
        dmFocViewInfoVO2.setLv1ProdRndTeamCode("1");
        dmFocViewInfoVO2.setLv2ProdRndTeamCode("2");
        dmFocViewInfoVO2.setLv3ProdRndTeamCode("3");
        dmCustomCombList.add(dmFocViewInfoVO2);
        method.invoke(customCombService, commonViewVO, dmCustomCombList);
        Assert.assertTrue(true);
    }

    @Test
    public void getParentCodeListTest() throws Exception {
        CommonViewVO commonViewVO = new CommonViewVO();
        commonViewVO.setGranularityPageSymbol("1");
        Map<String, List<DmFocViewInfoVO>> groupLevelMap = new HashMap<>();
        List<DmFocViewInfoVO> dmCustomCombList = new ArrayList<>();
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Mockito.when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(currentRoleDataPermission);
        Method method = PowerMockito.method(CustomCombService.class, "getParentCodeList", CommonViewVO.class, Map.class, List.class, boolean.class);
        method.invoke(customCombService, commonViewVO, groupLevelMap, dmCustomCombList, true);

        Set<String> lv0DimensionSet = new HashSet<>();
        commonViewVO.setLv0DimensionSet(lv0DimensionSet);
        commonViewVO.setViewFlag("3");
        method.invoke(customCombService, commonViewVO, groupLevelMap, dmCustomCombList, true);

        lv0DimensionSet.add("1");
        commonViewVO.setViewFlag("4");
        method.invoke(customCombService, commonViewVO, groupLevelMap, dmCustomCombList, true);

        lv0DimensionSet.add("NO_PERMISSION");
        commonViewVO.setViewFlag("5");
        method.invoke(customCombService, commonViewVO, groupLevelMap, dmCustomCombList, true);

        lv0DimensionSet.remove("NO_PERMISSION");
        commonViewVO.setViewFlag("6");
        List<DmFocViewInfoVO> dmFocViewInfoVOS = new ArrayList<>();
        dmFocViewInfoVOS.add(new DmFocViewInfoVO());
        groupLevelMap.put("6", dmFocViewInfoVOS);
        method.invoke(customCombService, commonViewVO, groupLevelMap, dmCustomCombList, false);

        commonViewVO.setGranularityType("U");
        method.invoke(customCombService, commonViewVO, groupLevelMap, dmCustomCombList, false);
        Assert.assertTrue(true);
    }

    @Test
    public void queryParentCodeListFromDBTest() throws Exception {
        CommonViewVO commonViewVO = new CommonViewVO();
        String granularityPageSymbol = "1";
        List<DmFocViewInfoVO> allParentList = new ArrayList<>();
        List<String> groupLevelList = new ArrayList<>();
        Method method = PowerMockito.method(CustomCombService.class, "queryParentCodeListFromDB", CommonViewVO.class, String.class, List.class, List.class);
        method.invoke(customCombService, commonViewVO, granularityPageSymbol, allParentList, groupLevelList);

        groupLevelList.add("ICT");
        method.invoke(customCombService, commonViewVO, granularityPageSymbol, allParentList, groupLevelList);

        groupLevelList.add("LV1");
        method.invoke(customCombService, commonViewVO, granularityPageSymbol, allParentList, groupLevelList);

        granularityPageSymbol = "U_ANNUAL";
        method.invoke(customCombService, commonViewVO, granularityPageSymbol, allParentList, groupLevelList);

        granularityPageSymbol = "U_MONTH";
        method.invoke(customCombService, commonViewVO, granularityPageSymbol, allParentList, groupLevelList);

        granularityPageSymbol = "P_ANNUAL";
        method.invoke(customCombService, commonViewVO, granularityPageSymbol, allParentList, groupLevelList);

        granularityPageSymbol = "P_MONTH";
        method.invoke(customCombService, commonViewVO, granularityPageSymbol, allParentList, groupLevelList);

        granularityPageSymbol = "D_MONTH";
        method.invoke(customCombService, commonViewVO, granularityPageSymbol, allParentList, groupLevelList);

        granularityPageSymbol = "D_ANNUAL1";
        method.invoke(customCombService, commonViewVO, granularityPageSymbol, allParentList, groupLevelList);
        Assert.assertTrue(true);
    }

    @Test
    public void getDimensionAnnualTest() throws Exception {
        CommonViewVO commonViewVO = new CommonViewVO();
        List<DmFocViewInfoVO> allParentList = new ArrayList<>();
        Method method = PowerMockito.method(CustomCombService.class, "getDimensionAnnual", CommonViewVO.class, List.class);
        method.invoke(customCombService, commonViewVO, allParentList);
        Assert.assertTrue(true);
    }

    @Test
    public void getDimensionMonthTest() throws Exception {
        CommonViewVO commonViewVO = new CommonViewVO();
        List<DmFocViewInfoVO> allParentList = new ArrayList<>();
        Method method = PowerMockito.method(CustomCombService.class, "getDimensionMonth", CommonViewVO.class, List.class);
        method.invoke(customCombService, commonViewVO, allParentList);
        Assert.assertTrue(true);
    }

    @Test
    public void getBeforeGroupLevelTest() throws Exception {
        String beforeGroupLevel = "";
        CommonViewVO commonViewVO = new CommonViewVO();
        List<String> groupLevelList = new ArrayList<>();
        Method method = PowerMockito.method(CustomCombService.class, "getBeforeGroupLevel", String.class, CommonViewVO.class, List.class);
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        commonViewVO.setGranularityType("U");
        commonViewVO.setViewFlag("0");
        beforeGroupLevel = "CATEGORY";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "MODL";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        commonViewVO.setViewFlag("1");
        beforeGroupLevel = "CATEGORY";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "MODL";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "CEG";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        commonViewVO.setViewFlag("2");
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        commonViewVO.setViewFlag("3");
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        commonViewVO.setViewFlag("4");
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);
        Assert.assertTrue(true);
    }

    @Test
    public void getManufactureBeforeGroupLevelTest() throws Exception {
        String beforeGroupLevel = "";
        CommonViewVO commonViewVO = new CommonViewVO();
        List<String> groupLevelList = new ArrayList<>();
        Method method = PowerMockito.method(CustomCombService.class, "getManufactureBeforeGroupLevel", String.class, CommonViewVO.class, List.class);
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        commonViewVO.setGranularityType("U");
        commonViewVO.setViewFlag("0");
        beforeGroupLevel = "MANUFACTURE_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        commonViewVO.setViewFlag("1");
        beforeGroupLevel = "SHIPPING_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "MANUFACTURE_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        commonViewVO.setViewFlag("2");
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "SHIPPING_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "LV2";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        commonViewVO.setViewFlag("3");
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "LV3";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "SHIPPING_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "MANUFACTURE_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        Assert.assertTrue(true);
    }

    @Test
    public void getBeforeUniversalTwoTest() throws Exception {
        String groupLevel = "";
        List<String> groupLevelList = new ArrayList<>();
        Method method = PowerMockito.method(CustomCombService.class, "getBeforeUniversalTwo", String.class, List.class);
        method.invoke(customCombService, groupLevel, groupLevelList);

        groupLevel = "CATEGORY";
        method.invoke(customCombService, groupLevel, groupLevelList);

        groupLevel = "MODL";
        method.invoke(customCombService, groupLevel, groupLevelList);

        groupLevel = "CEG";
        method.invoke(customCombService, groupLevel, groupLevelList);

        groupLevel = "LV2";
        method.invoke(customCombService, groupLevel, groupLevelList);
        Assert.assertTrue(true);
    }

    @Test
    public void getBeforeUniversalThreeTest() throws Exception {
        String groupLevel = "";
        List<String> groupLevelList = new ArrayList<>();
        Method method = PowerMockito.method(CustomCombService.class, "getBeforeUniversalThree", String.class, List.class);
        method.invoke(customCombService, groupLevel, groupLevelList);

        groupLevel = "CATEGORY";
        method.invoke(customCombService, groupLevel, groupLevelList);

        groupLevel = "MODL";
        method.invoke(customCombService, groupLevel, groupLevelList);

        groupLevel = "CEG";
        method.invoke(customCombService, groupLevel, groupLevelList);

        groupLevel = "LV2";
        method.invoke(customCombService, groupLevel, groupLevelList);

        groupLevel = "LV3";
        method.invoke(customCombService, groupLevel, groupLevelList);
        Assert.assertTrue(true);
    }

    @Test
    public void getBeforeProfitAndDimensionGroupLevelTest() throws Exception {
        String beforeProGroupLevel = "";
        CommonViewVO commonViewVO = new CommonViewVO();
        List<String> groupLevelList = new ArrayList<>();
        Method method = PowerMockito.method(CustomCombService.class, "getBeforeProfitAndDimensionGroupLevel", String.class, CommonViewVO.class, List.class);
        method.invoke(customCombService, beforeProGroupLevel, commonViewVO, groupLevelList);

        commonViewVO.setGranularityType("P");
        commonViewVO.setViewFlag("0");
        beforeProGroupLevel = "CATEGORY";
        method.invoke(customCombService, beforeProGroupLevel, commonViewVO, groupLevelList);

        beforeProGroupLevel = "MODL";
        method.invoke(customCombService, beforeProGroupLevel, commonViewVO, groupLevelList);

        commonViewVO.setViewFlag("1");
        beforeProGroupLevel = "CATEGORY";
        method.invoke(customCombService, beforeProGroupLevel, commonViewVO, groupLevelList);

        beforeProGroupLevel = "MODL";
        method.invoke(customCombService, beforeProGroupLevel, commonViewVO, groupLevelList);

        beforeProGroupLevel = "CEG";
        method.invoke(customCombService, beforeProGroupLevel, commonViewVO, groupLevelList);

        commonViewVO.setViewFlag("2");
        method.invoke(customCombService, beforeProGroupLevel, commonViewVO, groupLevelList);

        commonViewVO.setViewFlag("3");
        method.invoke(customCombService, beforeProGroupLevel, commonViewVO, groupLevelList);

        commonViewVO.setViewFlag("4");
        method.invoke(customCombService, beforeProGroupLevel, commonViewVO, groupLevelList);
        Assert.assertTrue(true);
    }

    @Test
    public void getManufactureProfitAndDimensionGroupLevelTest() throws Exception {
        String beforeGroupLevel = "";
        CommonViewVO commonViewVO = new CommonViewVO();
        List<String> groupLevelList = new ArrayList<>();
        Method method = PowerMockito.method(CustomCombService.class, "getManufactureProfitAndDimensionGroupLevel", String.class, CommonViewVO.class, List.class);
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        commonViewVO.setGranularityType("P");
        commonViewVO.setViewFlag("0");
        beforeGroupLevel = "MANUFACTURE_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        commonViewVO.setViewFlag("1");
        beforeGroupLevel = "SHIPPING_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "MANUFACTURE_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        commonViewVO.setViewFlag("2");
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "SHIPPING_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "LV2";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        commonViewVO.setViewFlag("3");
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "L1";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "SHIPPING_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "MANUFACTURE_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        commonViewVO.setViewFlag("4");
        beforeGroupLevel = "LV2";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "L1";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "L2";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "SHIPPING_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "MANUFACTURE_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        Assert.assertTrue(true);
    }

    @Test
    public void getManufactureDimensionGroupLevelTest() throws Exception {
        String beforeGroupLevel = "";
        CommonViewVO commonViewVO = new CommonViewVO();
        List<String> groupLevelList = new ArrayList<>();
        Method method = PowerMockito.method(CustomCombService.class, "getManufactureDimensionGroupLevel", String.class, CommonViewVO.class, List.class);
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        commonViewVO.setGranularityType("D");
        commonViewVO.setViewFlag("0");
        beforeGroupLevel = "MANUFACTURE_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "SHIPPING_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "DIMENSION";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        commonViewVO.setViewFlag("1");
        beforeGroupLevel = "SHIPPING_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "MANUFACTURE_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "SUBCATEGORY";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "DIMENSION";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        commonViewVO.setViewFlag("2");

        beforeGroupLevel = "SHIPPING_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "MANUFACTURE_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "SUB_DETAIL";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "SUBCATEGORY";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "DIMENSION";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        commonViewVO.setViewFlag("3");
        beforeGroupLevel = "SHIPPING_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "MANUFACTURE_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "DIMENSION";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "LV2";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        fourAndFiveDimension(commonViewVO, method, groupLevelList);

        Assert.assertTrue(true);
    }

    private void fourAndFiveDimension(CommonViewVO commonViewVO, Method method, List<String> groupLevelList) throws IllegalAccessException, InvocationTargetException {
        String beforeGroupLevel;
        commonViewVO.setViewFlag("4");
        beforeGroupLevel = "LV2";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "SHIPPING_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "MANUFACTURE_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "DIMENSION";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "SUBCATEGORY";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        commonViewVO.setViewFlag("5");
        beforeGroupLevel = "LV2";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "SHIPPING_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "MANUFACTURE_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "DIMENSION";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "SUBCATEGORY";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "SUB_DETAIL";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);
    }

    @Test
    public void getBeforeManuDimensionGroupLevelTest() throws Exception {
        String beforeGroupLevel = "";
        CommonViewVO commonViewVO = new CommonViewVO();
        commonViewVO.setViewFlag("6");
        List<String> groupLevelList = new ArrayList<>();
        Method method = PowerMockito.method(CustomCombService.class, "getBeforeManuDimensionGroupLevel", String.class, CommonViewVO.class, List.class);
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        commonViewVO.setGranularityType("D");
        commonViewVO.setViewFlag("6");
        beforeGroupLevel = "MANUFACTURE_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "SHIPPING_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "DIMENSION";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "LV3";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "LV2";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        commonViewVO.setViewFlag("7");
        beforeGroupLevel = "SHIPPING_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "MANUFACTURE_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "SUBCATEGORY";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "DIMENSION";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "LV3";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "LV2";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        commonViewVO.setViewFlag("8");
        beforeGroupLevel = "SHIPPING_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "MANUFACTURE_OBJECT";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "SUB_DETAIL";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "SUBCATEGORY";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "DIMENSION";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "LV3";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);

        beforeGroupLevel = "LV2";
        method.invoke(customCombService, beforeGroupLevel, commonViewVO, groupLevelList);
        Assert.assertTrue(true);
    }

    @Test
    public void getBeforeProfitSubTwoGroupLevelTest() throws Exception {
        String beforeProGroupLevel = "";
        List<String> groupLevelList = new ArrayList<>();
        Method method = PowerMockito.method(CustomCombService.class, "getBeforeProfitSubTwoGroupLevel", String.class, List.class);
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "CATEGORY";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "MODL";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "CEG";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "LV2";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);
        Assert.assertTrue(true);
    }

    @Test
    public void getBeforeProfitSubThreeGroupLevelTest() throws Exception {
        String beforeProGroupLevel = "";
        List<String> groupLevelList = new ArrayList<>();
        Method method = PowerMockito.method(CustomCombService.class, "getBeforeProfitSubThreeGroupLevel", String.class, List.class);
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "CATEGORY";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "MODL";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "CEG";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "LV2";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "L1";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);
        Assert.assertTrue(true);
    }

    @Test
    public void getBeforeProfitSubFourGroupLevelTest() throws Exception {
        String beforeProGroupLevel = "";
        List<String> groupLevelList = new ArrayList<>();
        Method method = PowerMockito.method(CustomCombService.class, "getBeforeProfitSubFourGroupLevel", String.class, List.class);
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "CATEGORY";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "MODL";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "CEG";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "LV2";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "L1";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "L2";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);
        Assert.assertTrue(true);
    }

    @Test
    public void getBeforeDimensionGroupLevelTest() throws Exception {
        String groupLevel = "";
        CommonViewVO commonViewVO = new CommonViewVO();
        List<String> groupLevelList = new ArrayList<>();
        Method method = PowerMockito.method(CustomCombService.class, "getBeforeDimensionGroupLevel", String.class, CommonViewVO.class, List.class);
        method.invoke(customCombService, groupLevel, commonViewVO, groupLevelList);

        commonViewVO.setGranularityType("D");
        commonViewVO.setViewFlag("0");
        groupLevel = "CATEGORY";
        method.invoke(customCombService, groupLevel, commonViewVO, groupLevelList);

        groupLevel = "MODL";
        method.invoke(customCombService, groupLevel, commonViewVO, groupLevelList);

        groupLevel = "CEG";
        method.invoke(customCombService, groupLevel, commonViewVO, groupLevelList);

        groupLevel = "DIMENSION";
        method.invoke(customCombService, groupLevel, commonViewVO, groupLevelList);

        commonViewVO.setViewFlag("1");
        method.invoke(customCombService, groupLevel, commonViewVO, groupLevelList);

        commonViewVO.setViewFlag("2");
        method.invoke(customCombService, groupLevel, commonViewVO, groupLevelList);

        commonViewVO.setViewFlag("3");
        method.invoke(customCombService, groupLevel, commonViewVO, groupLevelList);

        Assert.assertTrue(true);
    }

    @Test
    public void beforeDimensionOneGroupLevelTest() throws Exception {
        String beforeProGroupLevel = "";
        List<String> groupLevelList = new ArrayList<>();
        Method method = PowerMockito.method(CustomCombService.class, "beforeDimensionOneGroupLevel", String.class, List.class);
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "CATEGORY";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "MODL";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "CEG";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "SUBCATEGORY";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "DIMENSION";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "L2";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);
        Assert.assertTrue(true);
    }

    @Test
    public void beforeDimensionTwoGroupLevelTest() throws Exception {
        String beforeProGroupLevel = "";
        List<String> groupLevelList = new ArrayList<>();
        Method method = PowerMockito.method(CustomCombService.class, "beforeDimensionTwoGroupLevel", String.class, List.class);
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "CATEGORY";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "MODL";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "CEG";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "SUB_DETAIL";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "SUBCATEGORY";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "DIMENSION";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);
        Assert.assertTrue(true);
    }

    @Test
    public void getFrontDimensionGroupLevelTest() throws Exception {
        String groupLevel = "";
        CommonViewVO commonViewVO = new CommonViewVO();
        List<String> groupLevelList = new ArrayList<>();
        Method method = PowerMockito.method(CustomCombService.class, "getFrontDimensionGroupLevel", String.class, CommonViewVO.class, List.class);
        method.invoke(customCombService, groupLevel, commonViewVO, groupLevelList);

        commonViewVO.setGranularityType("D");
        commonViewVO.setViewFlag("3");
        groupLevel = "CATEGORY";
        method.invoke(customCombService, groupLevel, commonViewVO, groupLevelList);

        groupLevel = "MODL";
        method.invoke(customCombService, groupLevel, commonViewVO, groupLevelList);

        groupLevel = "CEG";
        method.invoke(customCombService, groupLevel, commonViewVO, groupLevelList);

        groupLevel = "DIMENSION";
        method.invoke(customCombService, groupLevel, commonViewVO, groupLevelList);

        groupLevel = "LV2";
        method.invoke(customCombService, groupLevel, commonViewVO, groupLevelList);

        groupLevel = "DIMENSION";
        method.invoke(customCombService, groupLevel, commonViewVO, groupLevelList);

        commonViewVO.setViewFlag("4");
        method.invoke(customCombService, groupLevel, commonViewVO, groupLevelList);

        commonViewVO.setViewFlag("5");
        method.invoke(customCombService, groupLevel, commonViewVO, groupLevelList);

        commonViewVO.setViewFlag("6");
        method.invoke(customCombService, groupLevel, commonViewVO, groupLevelList);

        Assert.assertTrue(true);
    }

    @Test
    public void getBeforeDimensionFourGroupLevelTest() throws Exception {
        String beforeProGroupLevel = "";
        List<String> groupLevelList = new ArrayList<>();
        Method method = PowerMockito.method(CustomCombService.class, "getBeforeDimensionFourGroupLevel", String.class, List.class);
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "CATEGORY";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "MODL";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "CEG";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "SUBCATEGORY";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "DIMENSION";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "LV2";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);
        Assert.assertTrue(true);
    }

    @Test
    public void getBeforeDimensionFiveGroupLevelTest() throws Exception {
        String beforeProGroupLevel = "";
        List<String> groupLevelList = new ArrayList<>();
        Method method = PowerMockito.method(CustomCombService.class, "getBeforeDimensionFiveGroupLevel", String.class, List.class);
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "CATEGORY";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "MODL";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "CEG";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "SUB_DETAIL";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "SUBCATEGORY";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "DIMENSION";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "LV2";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);
        Assert.assertTrue(true);
    }

    @Test
    public void getBeforeSubDimensionGroupLevelTest() throws Exception {
        String groupLevel = "";
        CommonViewVO commonViewVO = new CommonViewVO();
        List<String> groupLevelList = new ArrayList<>();
        commonViewVO.setViewFlag("5");
        Method method = PowerMockito.method(CustomCombService.class, "getBeforeSubDimensionGroupLevel", String.class, CommonViewVO.class, List.class);
        method.invoke(customCombService, groupLevel, commonViewVO, groupLevelList);

        commonViewVO.setGranularityType("D");
        commonViewVO.setViewFlag("6");
        groupLevel = "CATEGORY";
        method.invoke(customCombService, groupLevel, commonViewVO, groupLevelList);

        groupLevel = "MODL";
        method.invoke(customCombService, groupLevel, commonViewVO, groupLevelList);

        groupLevel = "CEG";
        method.invoke(customCombService, groupLevel, commonViewVO, groupLevelList);

        groupLevel = "DIMENSION";
        method.invoke(customCombService, groupLevel, commonViewVO, groupLevelList);

        groupLevel = "LV2";
        method.invoke(customCombService, groupLevel, commonViewVO, groupLevelList);

        groupLevel = "DIMENSION";
        method.invoke(customCombService, groupLevel, commonViewVO, groupLevelList);

        groupLevel = "LV3";
        method.invoke(customCombService, groupLevel, commonViewVO, groupLevelList);

        commonViewVO.setViewFlag("7");
        method.invoke(customCombService, groupLevel, commonViewVO, groupLevelList);

        commonViewVO.setViewFlag("8");
        method.invoke(customCombService, groupLevel, commonViewVO, groupLevelList);

        commonViewVO.setViewFlag("6");
        method.invoke(customCombService, groupLevel, commonViewVO, groupLevelList);

        Assert.assertTrue(true);
    }

    @Test
    public void getBeforeDimensionSevenGroupLevelTest() throws Exception {
        String beforeProGroupLevel = "";
        List<String> groupLevelList = new ArrayList<>();
        Method method = PowerMockito.method(CustomCombService.class, "getBeforeDimensionSevenGroupLevel", String.class, List.class);
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "CATEGORY";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "MODL";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "CEG";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "SUBCATEGORY";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "DIMENSION";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "LV3";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "LV2";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);
        Assert.assertTrue(true);
    }

    @Test
    public void getBeforeDimensionEightGroupLevelTest() throws Exception {
        String beforeProGroupLevel = "";
        List<String> groupLevelList = new ArrayList<>();
        Method method = PowerMockito.method(CustomCombService.class, "getBeforeDimensionEightGroupLevel", String.class, List.class);
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "CATEGORY";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "MODL";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "CEG";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "SUB_DETAIL";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "DIMENSION";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);
        Assert.assertTrue(true);
    }

    @Test
    public void getBeforeDimensionEightSubGroupLevelTest() throws Exception {
        String beforeProGroupLevel = "";
        List<String> groupLevelList = new ArrayList<>();
        Method method = PowerMockito.method(CustomCombService.class, "getBeforeDimensionEightSubGroupLevel", String.class, List.class);
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "SUBCATEGORY";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "DIMENSION";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "LV3";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);

        beforeProGroupLevel = "LV2";
        method.invoke(customCombService, beforeProGroupLevel, groupLevelList);
        Assert.assertTrue(true);
    }

    @Test
    public void removeListByCustomIdTest() throws Exception {
        CommonViewVO commonViewVO = new CommonViewVO();
        List<DmFocViewInfoVO> allProdTeamCodeList = new ArrayList<>();
        Method method = PowerMockito.method(CustomCombService.class, "removeListByCustomId", CommonViewVO.class, List.class);
        method.invoke(customCombService, commonViewVO, allProdTeamCodeList);

        commonViewVO.setCustomId(1L);
        commonViewVO.setCostType("P");
        List<DmFocViewInfoVO> customCodeList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setGroupLevel("1");
        dmFocViewInfoVO.setConnectCode("1");
        customCodeList.add(dmFocViewInfoVO);
        allProdTeamCodeList.add(dmFocViewInfoVO);
        DmFocViewInfoVO dmFocViewInfoVO2 = new DmFocViewInfoVO();
        dmFocViewInfoVO2.setGroupLevel("2");
        dmFocViewInfoVO2.setConnectCode("2");
        customCodeList.add(dmFocViewInfoVO2);
        allProdTeamCodeList.add(dmFocViewInfoVO2);

        DmFocViewInfoVO dmFocViewInfoVO3 = new DmFocViewInfoVO();
        dmFocViewInfoVO3.setGroupLevel("1");
        dmFocViewInfoVO3.setConnectCode("3");
        customCodeList.add(dmFocViewInfoVO3);

        DmFocViewInfoVO dmFocViewInfoVO4 = new DmFocViewInfoVO();
        dmFocViewInfoVO4.setGroupLevel("4");
        dmFocViewInfoVO4.setConnectCode("2");
        customCodeList.add(dmFocViewInfoVO4);

        DmFocViewInfoVO dmFocViewInfoVO5 = new DmFocViewInfoVO();
        dmFocViewInfoVO5.setGroupLevel("5");
        dmFocViewInfoVO5.setConnectCode("5");
        customCodeList.add(dmFocViewInfoVO5);
        Mockito.when(dmFocCustomCombDao.getCombinationList(commonViewVO)).thenReturn(customCodeList);
        method.invoke(customCombService, commonViewVO, allProdTeamCodeList);
        Assert.assertTrue(true);
    }

    @Test
    public void combineOtherPageGroupCodeTest() throws Exception {
        CombinationVO combinationVO = new CombinationVO();
        String userIdStr = "1";
        String roleId = "1";
        List<DmCustomCombVO> otherCustomVOList = new ArrayList<>();
        Long customId = 1L;
        Timestamp timestamp = new Timestamp(new Date().getTime());
        Method method = PowerMockito.method(CustomCombService.class, "combineOtherPageGroupCode", CombinationVO.class, String.class, String.class, List.class, Long.class, Timestamp.class);
        method.invoke(customCombService, combinationVO, userIdStr, roleId, otherCustomVOList, customId, timestamp);

        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        otherCustomVOList.add(dmCustomCombVO);
        method.invoke(customCombService, combinationVO, userIdStr, roleId, otherCustomVOList, customId, timestamp);
        Assert.assertTrue(true);
    }

    @Test
    public void callFunctionRefreshDataTest() throws Exception {
        CombinationVO combinationVO = new CombinationVO();
        Long userId = 1L;
        DmFocDataRefreshStatus dataRefreshStatus = new DmFocDataRefreshStatus();
        CombTransformVO combTransformVO = new CombTransformVO();
        Method method = PowerMockito.method(CustomCombService.class, "callFunctionRefreshData", CombinationVO.class, Long.class, DmFocDataRefreshStatus.class, CombTransformVO.class);
        DmFocVersionInfoDTO dmFocVersionVO = new DmFocVersionInfoDTO();
        Mockito.when(dmFocVersionDao.findVersionIdByDataType(Mockito.any(),any())).thenReturn(dmFocVersionVO);
        method.invoke(customCombService, combinationVO, userId, dataRefreshStatus, combTransformVO);
        Assert.assertTrue(true);
    }

}