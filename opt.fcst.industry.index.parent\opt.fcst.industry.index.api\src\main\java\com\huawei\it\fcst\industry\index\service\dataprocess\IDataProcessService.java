/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.service.dataprocess;

import com.huawei.it.fcst.industry.index.vo.ciphertext.CipherTextDataVO;
import com.huawei.it.fcst.industry.index.vo.ciphertext.FunctionParamVO;
import com.huawei.it.fcst.industry.index.vo.ciphertext.VarifyTaskVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.soa.common.SoaException;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.io.IOException;

/**
 * 功能描述 处理
 *
 * <AUTHOR>
 * @since 2023年03月22日
 */
@Path("dataProcess")
@Consumes(MediaType.APPLICATION_JSON + ";charset=UTF-8")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface IDataProcessService {
    /**
     * 解密数据入口
     * @param cipherTextDataVO 参数
     * @throws ApplicationException
     */
    @Path("start/executor")
    @POST
    void startDataProcessTask(CipherTextDataVO cipherTextDataVO) throws IOException, ApplicationException, SoaException;

    /**
     * 数据初始化动作
     *
     * @param cipherTextDataVO 参数信息
     * @throws IOException 异常
     * @throws ApplicationException 异常
     */
    @Path("init")
    @POST
    void initDataProcessTask(CipherTextDataVO cipherTextDataVO) throws IOException, ApplicationException;

    @POST
    @Path("cell")
    void startDBFunction(@RequestBody FunctionParamVO functionParamVO,@Context HttpServletRequest request) throws ApplicationException, IOException, SoaException;

    /**
     *
     * @param cipherTextDataVO 入参
     * @param requestContext 执行上下文
     * @throws IOException 异常
     * @throws ApplicationException 异常
     */
    void dataProcess(CipherTextDataVO cipherTextDataVO, IRequestContext requestContext) throws IOException, ApplicationException, SoaException;

    /**
     *
     * @param cipherTextDataVO 入参
     * @param varifyTaskVO 入参
     * @throws IOException 异常
     * @throws ApplicationException 异常
     */
    void dataProcess(CipherTextDataVO cipherTextDataVO, VarifyTaskVO varifyTaskVO) throws IOException, ApplicationException;

    /**
     * cell 存储过程
     * @param functionParamVO 参数
     */
    void startDBFunctionTask(FunctionParamVO functionParamVO) throws IOException, SoaException, CommonApplicationException;

    /**
     * 执行数据前删除历史数据
     * @param cipherTextDataVO 入参
     */
    void delByPeriodId(CipherTextDataVO cipherTextDataVO);
}
