/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.dao;

import com.huawei.it.fcst.industry.price.vo.common.CommonPriceBaseVO;
import com.huawei.it.fcst.industry.price.vo.common.DmFcstPriceDimInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * IDmFocViewInfoDao
 *
 * <AUTHOR>
 * @since 2023/3/14
 */
public interface IDmFcstPriceDimInfoDao {

    List<DmFcstPriceDimInfoVO> getBgList(CommonPriceBaseVO commonViewVO);

    List<DmFcstPriceDimInfoVO> getOverseaFlagList(CommonPriceBaseVO commonViewVO);

    List<DmFcstPriceDimInfoVO> getRegionCodeList(CommonPriceBaseVO commonViewVO);

    List<DmFcstPriceDimInfoVO> getPermissionRegionCodeList(CommonPriceBaseVO commonViewVO);

    List<DmFcstPriceDimInfoVO> getRepofficeCodeList(CommonPriceBaseVO commonViewVO);

    List<DmFcstPriceDimInfoVO> getKeyAccountDeptList(CommonPriceBaseVO commonViewVO);

    List<DmFcstPriceDimInfoVO> getSubAccountDeptList(CommonPriceBaseVO commonBaseVO);

    List<DmFcstPriceDimInfoVO> baseDropdownDimInfoList(@Param("searchVO")CommonPriceBaseVO commonViewVO);

    Integer getSpartNum(CommonPriceBaseVO commonViewVO);

    List<DmFcstPriceDimInfoVO> getLv4CodeWithSpart(CommonPriceBaseVO commonViewVO);

    List<DmFcstPriceDimInfoVO> getBgCodePermissionList(CommonPriceBaseVO commonViewVO);

    List<DmFcstPriceDimInfoVO> getLv1CodeList(CommonPriceBaseVO commonViewVO);

    List<DmFcstPriceDimInfoVO> getLv0Code(CommonPriceBaseVO commonViewVO);

    DmFcstPriceDimInfoVO findRefreshTime();
}
