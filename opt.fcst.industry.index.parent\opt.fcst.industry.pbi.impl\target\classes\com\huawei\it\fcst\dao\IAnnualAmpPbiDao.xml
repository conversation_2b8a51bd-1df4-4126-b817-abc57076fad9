<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.pbi.dao.IAnnualAmpPbiDao">
    <resultMap type="com.huawei.it.fcst.industry.pbi.vo.annual.DmFocAnnualAmpVO" id="annualResultMap">
        <result property="id" column="id"/>
        <result property="versionId" column="version_id"/>
        <result property="periodYear" column="period_year"/>
        <result property="lv0ProdRndTeamCode" column="lv0_prod_rnd_team_code"/>
        <result property="prodRndTeamCode" column="prod_rnd_team_code"/>
        <result property="prodRndTeamCode" column="prod_rnd_team_catg_code"/>
        <result property="prodRndTeamCnName" column="prod_rnd_team_cn_name"/>
        <result property="parentLevel" column="parent_level"/>
        <result property="parentCode" column="parent_code"/>
        <result property="parentCnName" column="parent_cn_name"/>
        <result property="groupCode" column="group_code"/>
        <result property="groupCnName" column="group_cn_name"/>
        <result property="groupLevel" column="group_level"/>
        <result property="annualAmp" column="annual_amp"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDdate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="appendFlag" column="append_flag"/>
        <result property="weightRate" column="weight_rate"/>
        <result property="statusCode" column="status_code"/>
        <result property="groupCodeAndName" column="groupCodeAndName"/>
        <result property="weightAnnualAmpPercent" column="weightAnnualAmpPercent"/>
        <result property="appendYear" column="append_year"/>
        <result property="dimensionCode" column="dimension_code"/>
        <result property="dimensionCnName" column="dimension_cn_name"/>
        <result property="dimensionSubCategoryCode" column="dimension_subcategory_code"/>
        <result property="dimensionSubCategoryCnName" column="dimension_subcategory_cn_name"/>
        <result property="dimensionSubDetailCode" column="dimension_sub_detail_code"/>
        <result property="dimensionSubDetailCnName" column="dimension_sub_detail_cn_name"/>
        <result property="customCnName" column="custom_cn_name"/>
        <result property="customId" column="custom_id"/>
        <result property="rmbCostAmt" column="rmb_cost_amt"/>
    </resultMap>

    <select id="allIndustryNormalCost" resultMap="annualResultMap">
        select distinct
        amp.group_cn_name,amp.period_year,amp.group_level,amp.group_code,
        CONCAT(ROUND(amp.annual_amp*100,1),'%') as annual_amp,
        status.status_code,amp.dimension_code,amp.dimension_subcategory_code,amp.dimension_sub_detail_code,status.append_year,
        <choose>
            <when test='granularityType == "IRB"'>
                amp.prod_rd_team_cn_name as prod_rnd_team_cn_name,
            </when>
             <when test='granularityType == "INDUS"'>
                amp.industry_catg_cn_name as prod_rnd_team_cn_name,
            </when>
             <when test='granularityType == "PROD"'>
                amp.prod_list_cn_name as prod_rnd_team_cn_name,
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='isMultipleSelect == false'>
            weight.weight_rate*100 AS weight_rate
        </if>
        <if test='isMultipleSelect == true'>
            weight.absolute_weight*100 AS weight_rate
        </if>
        from fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_AMP_T amp
        left join fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_WEIGHT_T weight
        on amp.view_flag = weight.view_flag and amp.group_code = weight.group_code
        and amp.group_level = weight.group_level
        and amp.parent_code = weight.parent_code
        and amp.version_id =weight.version_id and amp.period_year = weight.period_year
        and amp.oversea_flag = weight.oversea_flag
        and amp.bg_code = weight.bg_code
        and amp.region_code = weight.region_code
        and amp.repoffice_code = weight.repoffice_code
        and nvl(amp.dimension_code,'snull') = nvl( weight.dimension_code,'snull')
        and nvl(amp.dimension_subcategory_code,'snull') = nvl( weight.dimension_subcategory_code,'snull')
        and nvl(amp.dimension_sub_detail_code,'snull') = nvl( weight.dimension_sub_detail_code,'snull')
        and nvl ( amp.main_flag, 'snull' ) = nvl ( weight.main_flag, 'snull' )
        and nvl ( amp.code_attributes, 'snull' ) = nvl ( weight.code_attributes, 'snull' )
        <choose>
            <when test='granularityType == "IRB"'>
                and amp.prod_rnd_team_code = weight.prod_rnd_team_code
            </when>
            <when test='granularityType == "INDUS"'>
                and amp.industry_catg_code = weight.industry_catg_code
            </when>
            <when test='granularityType == "PROD"'>
                and amp.prod_list_code = weight.prod_list_code
            </when>
            <otherwise>
            </otherwise>
        </choose>
        left join fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_STATUS_T status
        on amp.group_code = status.group_code and amp.group_level = status.group_level
        and amp.period_year = status.period_year and amp.version_id = status.version_id
        and amp.view_flag = status.view_flag
        and amp.parent_code = status.parent_code
        and amp.oversea_flag = status.oversea_flag and amp.bg_code = status.bg_code
        and amp.region_code = status.region_code
        and amp.repoffice_code = status.repoffice_code
        and nvl ( amp.dimension_code, 'snull' ) = nvl ( status.dimension_code, 'snull' )
        and nvl ( amp.dimension_subcategory_code, 'snull' ) = nvl ( status.dimension_subcategory_code, 'snull' )
        and nvl ( amp.dimension_sub_detail_code, 'snull' ) = nvl ( status.dimension_sub_detail_code, 'snull' )
        and nvl ( amp.main_flag, 'snull' ) = nvl ( status.main_flag, 'snull' )
        and nvl ( amp.code_attributes, 'snull' ) = nvl ( status.code_attributes, 'snull' )
        <choose>
            <when test='granularityType == "IRB"'>
                and amp.prod_rnd_team_code = status.prod_rnd_team_code
            </when>
             <when test='granularityType == "INDUS"'>
                and amp.industry_catg_code = status.industry_catg_code
            </when>
             <when test='granularityType == "PROD"'>
                and amp.prod_list_code = status.prod_list_code
            </when>
            <otherwise>
            </otherwise>
        </choose>
        where amp.del_flag = 'N'
        <if test='overseaFlag != null and overseaFlag != ""'>
            and amp.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and amp.bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and amp.region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and amp.repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag != null and mainFlag != ""'>
            and amp.main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes != null and codeAttributes != ""'>
            and amp.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and amp.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and amp.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='dimensionCode != null and dimensionCode != ""'>
            and amp.dimension_code = #{dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubCategoryCode != null and dimensionSubCategoryCode != ""'>
            and amp.dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubDetailCode != null and dimensionSubDetailCode != ""'>
            and amp.dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
        <if test='groupCodeList != null and groupCodeList.size() > 0'>
            <foreach collection='groupCodeList' item="code" open="AND amp.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='prodRndTeamCodeList != null and prodRndTeamCodeList.size() > 0'>
            <choose>
                <when test='granularityType == "IRB"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.prod_rnd_team_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                 <when test='granularityType == "INDUS"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.industry_catg_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                 <when test='granularityType == "PROD"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.prod_list_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='periodYear != null and periodYear != ""'>
            and amp.period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and amp.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND amp.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        order by amp.period_year,weight_rate desc
    </select>

    <select id="getAnnualperiodYearList" resultType="java.lang.String">
        select distinct period_year
        from fin_dm_opt_foi.DM_FCST_ICT_PSP_${granularityType}_ANNL_AMP_T where version_id = #{versionId,jdbcType=NUMERIC}
        order by period_year desc limit 3
    </select>

    <select id="findGroupCodeOrderByWeight" resultMap="annualResultMap">
        select group_code, group_cn_name,
        <if test='isMultipleSelect == false'>
            sum(ROUND(weight_rate*100,1)) weight_rate
        </if>
        <if test='isMultipleSelect == true'>
            sum(ROUND(absolute_weight*100,1)) weight_rate
        </if>
        from fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_WEIGHT_T
        where del_flag = 'N'
        <if test='overseaFlag != null and overseaFlag != ""'>
            and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag != null and mainFlag != ""'>
            and main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes != null and codeAttributes != ""'>
            and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and version_id = #{versionId}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='dimensionCode != null and dimensionCode != ""'>
            and dimension_code = #{dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubCategoryCode != null and dimensionSubCategoryCode != ""'>
            and dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubDetailCode != null and dimensionSubDetailCode != ""'>
            and dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
        <if test='year != null and year != ""'>
            and period_year = #{year,jdbcType=VARCHAR}
        </if>
        <if test='parentCodeList != null and parentCodeList.size() > 0'>
            <foreach collection='parentCodeList' item="code" open="AND parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='prodRndTeamCodeList != null and prodRndTeamCodeList.size() > 0'>
            <choose>
                <when test='granularityType == "IRB"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND prod_rnd_team_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='granularityType == "INDUS"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND industry_catg_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='granularityType == "PROD"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND prod_list_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        GROUP BY group_code,group_cn_name
        ORDER BY weight_rate DESC
    </select>

    <select id="multiIndustryPbiCostChart" resultMap="annualResultMap">
        select amp.parent_code,amp.parent_cn_name,
        amp.period_year,
        CONCAT(ROUND( SUM ( amp.annual_amp*100 ), 1 ),'%') AS annual_amp,
        amp.group_code, amp.group_cn_name, amp.group_level,
        <if test='isMultipleSelect == false'>
            CONCAT(ROUND(SUM ( weight.weight_rate*100 ),1),'%') AS weight_rate,
        </if>
        <if test='isMultipleSelect == true'>
            CONCAT(ROUND(SUM ( weight.absolute_weight*100 ),1),'%') AS weight_rate,
        </if>
        CONCAT (amp.group_code, ' ', amp.group_cn_name ) AS groupCodeAndName,
        status.status_code,weight.append_flag, max(status.append_year) AS append_year,
        amp.dimension_code,amp.dimension_subcategory_code,amp.dimension_sub_detail_code,
        <choose>
            <when test='granularityType == "IRB"'>
                amp.prod_rnd_team_code as prod_rnd_team_catg_code,amp.prod_rd_team_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "INDUS"'>
                amp.industry_catg_code as prod_rnd_team_catg_code, amp.industry_catg_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "PROD"'>
                amp.prod_list_code as prod_rnd_team_catg_code, amp.prod_list_cn_name as prod_rnd_team_cn_name
            </when>
            <otherwise>
            </otherwise>
        </choose>
        from fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_AMP_T amp
        left join fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_WEIGHT_T weight
        on amp.view_flag = weight.view_flag and amp.group_code = weight.group_code
        and amp.group_level = weight.group_level
        and amp.parent_code = weight.parent_code
        and amp.version_id =weight.version_id and amp.period_year = weight.period_year
        and amp.oversea_flag = weight.oversea_flag
        and amp.bg_code = weight.bg_code
        and amp.region_code = weight.region_code
        and amp.repoffice_code = weight.repoffice_code
        and nvl(amp.dimension_code,'snull') = nvl( weight.dimension_code,'snull')
        and nvl(amp.dimension_subcategory_code,'snull') = nvl( weight.dimension_subcategory_code,'snull')
        and nvl(amp.dimension_sub_detail_code,'snull') = nvl( weight.dimension_sub_detail_code,'snull')
        and nvl ( amp.main_flag, 'snull' ) = nvl ( weight.main_flag, 'snull' )
        and nvl ( amp.code_attributes, 'snull' ) = nvl ( weight.code_attributes, 'snull' )
        <choose>
            <when test='granularityType == "IRB"'>
                and amp.prod_rnd_team_code = weight.prod_rnd_team_code
            </when>
            <when test='granularityType == "INDUS"'>
                and amp.industry_catg_code = weight.industry_catg_code
            </when>
            <when test='granularityType == "PROD"'>
                and amp.prod_list_code = weight.prod_list_code
            </when>
            <otherwise>
            </otherwise>
        </choose>
        left join fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_STATUS_T status
        on amp.group_code = status.group_code and amp.group_level = status.group_level
        and amp.period_year = status.period_year and amp.version_id = status.version_id
        and amp.view_flag = status.view_flag
        and amp.parent_code = status.parent_code
        and amp.oversea_flag = status.oversea_flag
        and amp.bg_code = status.bg_code
        and amp.region_code = status.region_code
        and amp.repoffice_code = status.repoffice_code
        and nvl(amp.dimension_code,'snull') = nvl( status.dimension_code,'snull')
        and nvl(amp.dimension_subcategory_code,'snull') = nvl( status.dimension_subcategory_code,'snull')
        and nvl(amp.dimension_sub_detail_code,'snull') = nvl( status.dimension_sub_detail_code,'snull')
        and nvl ( amp.main_flag, 'snull' ) = nvl ( status.main_flag, 'snull' )
        and nvl ( amp.code_attributes, 'snull' ) = nvl ( status.code_attributes, 'snull' )
        <choose>
            <when test='granularityType == "IRB"'>
                and amp.prod_rnd_team_code = status.prod_rnd_team_code
            </when>
            <when test='granularityType == "INDUS"'>
                and amp.industry_catg_code = status.industry_catg_code
            </when>
            <when test='granularityType == "PROD"'>
                and amp.prod_list_code = status.prod_list_code
            </when>
            <otherwise>
            </otherwise>
        </choose>
        where amp.del_flag = 'N'
        <if test='overseaFlag != null and overseaFlag != ""'>
            and amp.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and amp.bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and amp.region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and amp.repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag != null and mainFlag != ""'>
            and amp.main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes != null and codeAttributes != ""'>
            and amp.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and amp.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and amp.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='periodYear != null and periodYear != ""'>
            and amp.period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and amp.version_id = #{versionId}
        </if>
        <if test='dimensionCode != null and dimensionCode != ""'>
            and amp.dimension_code = #{dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubCategoryCode != null and dimensionSubCategoryCode != ""'>
            and amp.dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubDetailCode != null and dimensionSubDetailCode != ""'>
            and amp.dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
        <if test='parentCodeList != null and parentCodeList.size() > 0'>
            <foreach collection='parentCodeList' item="code" open="AND amp.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='prodRndTeamCodeList != null and prodRndTeamCodeList.size() > 0'>
            <choose>
                <when test='granularityType == "IRB"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.prod_rnd_team_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='granularityType == "INDUS"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.industry_catg_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='granularityType == "PROD"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.prod_list_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND amp.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        <if test='groupCodeOrder != null and groupCodeOrder != ""'>
            <foreach collection="groupCodeOrder.split(',')" item="item" open="and amp.group_code IN (" close=")"
                     index="index" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY amp.parent_code,amp.parent_cn_name,prod_rnd_team_cn_name,prod_rnd_team_catg_code,weight.append_flag,
        amp.group_level,amp.group_code,amp.group_cn_name,groupCodeAndName,amp.period_year,status.status_code,
        amp.dimension_code,amp.dimension_subcategory_code, amp.dimension_sub_detail_code
        order by locate(amp.group_code, #{groupCodeOrder}), amp.period_year
    </select>

    <select id="multiIndustryMinLevelChart" resultMap="annualResultMap">
        select amp.parent_code,amp.parent_cn_name,
        amp.period_year,
        CONCAT(ROUND( SUM ( amp.annual_amp*100 ), 1 ),'%') AS annual_amp,
        amp.group_code, amp.group_cn_name, amp.group_level,
        CONCAT(ROUND(SUM ( weight.weight_rate*100 ),1),'%') AS weight_rate,
        CONCAT (amp.group_code, ' ', amp.group_cn_name ) AS groupCodeAndName,
        status.status_code, max(status.append_year) AS append_year,
        amp.dimension_code,amp.dimension_subcategory_code,amp.dimension_sub_detail_code,
        amp.lv_code as prod_rnd_team_catg_code,amp.lv_cn_name as prod_rnd_team_cn_name
        from fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_MID_GROUP_AMP_T amp
        left join fin_dm_opt_foi.DM_FCST_ICT_${combTablePreFix}_BASE_CUS_ANNL_WEIGHT_T weight
        on amp.view_flag = weight.view_flag
        and amp.group_code = weight.group_code
        and amp.group_level = weight.group_level
        and amp.parent_code = weight.parent_code
        and amp.version_id =weight.version_id and amp.period_year = weight.period_year
        and amp.oversea_flag = weight.oversea_flag
        and amp.bg_code = weight.bg_code
        and amp.region_code = weight.region_code
        and amp.repoffice_code = weight.repoffice_code
        and nvl ( amp.main_flag, 'snull' ) = nvl ( weight.main_flag, 'snull' )
        and nvl ( amp.code_attributes, 'snull' ) = nvl ( weight.code_attributes, 'snull' )
        and amp.lv_code = weight.lv4_code
        and weight.custom_id = #{customId}
        left join fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_STATUS_T status
        on amp.group_code = status.group_code and amp.group_level = status.group_level
        and amp.period_year = status.period_year and amp.version_id = status.version_id
        and amp.view_flag = status.view_flag
        and amp.parent_code = status.parent_code
        and amp.oversea_flag = status.oversea_flag
        and amp.bg_code = status.bg_code
        and amp.region_code = status.region_code
        and amp.repoffice_code = status.repoffice_code
        and nvl(amp.dimension_code,'snull') = nvl( status.dimension_code,'snull')
        and nvl(amp.dimension_subcategory_code,'snull') = nvl( status.dimension_subcategory_code,'snull')
        and nvl(amp.dimension_sub_detail_code,'snull') = nvl( status.dimension_sub_detail_code,'snull')
        and nvl ( amp.main_flag, 'snull' ) = nvl ( status.main_flag, 'snull' )
        and nvl ( amp.code_attributes, 'snull' ) = nvl ( status.code_attributes, 'snull' )
        <choose>
            <when test='granularityType == "IRB"'>
                and amp.lv_code = status.prod_rnd_team_code
            </when>
            <when test='granularityType == "INDUS"'>
                and amp.lv_code = status.industry_catg_code
            </when>
            <when test='granularityType == "PROD"'>
                and amp.lv_code = status.prod_list_code
            </when>
            <otherwise>
            </otherwise>
        </choose>
        where amp.del_flag = 'N'
        <if test='overseaFlag != null and overseaFlag != ""'>
            and amp.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and amp.bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and amp.region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and amp.repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag != null and mainFlag != ""'>
            and amp.main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes != null and codeAttributes != ""'>
            and amp.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and amp.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and amp.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='periodYear != null and periodYear != ""'>
            and amp.period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and amp.version_id = #{versionId}
        </if>
        <if test='dimensionCode != null and dimensionCode != ""'>
            and amp.dimension_code = #{dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubCategoryCode != null and dimensionSubCategoryCode != ""'>
            and amp.dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubDetailCode != null and dimensionSubDetailCode != ""'>
            and amp.dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != "" and viewFlag == "PROD_SPART"'>
            and amp.group_level = 'SPART'
        </if>
        <if test='viewFlag != null and viewFlag != "" and viewFlag == "DIMENSION"'>
            and amp.group_level = 'SUB_DETAIL'
        </if>
        <if test='groupCodeList != null and groupCodeList.size() > 0'>
            <foreach collection='groupCodeList' item="code" open="AND amp.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='prodRndTeamCodeList != null and prodRndTeamCodeList.size() > 0'>
            <choose>
                <when test='teamLevel == "LV0"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.lv0_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='teamLevel == "LV1"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.lv1_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='teamLevel == "LV2"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.lv2_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='teamLevel == "LV3"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.lv3_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='teamLevel == "LV4"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.lv4_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND amp.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        <if test='prodRndTeamCodeOrder != null and prodRndTeamCodeOrder != ""'>
            <foreach collection="prodRndTeamCodeOrder.split(',')" item="item" open="and amp.lv_code IN (" close=")"
                     index="index" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY amp.parent_code,amp.parent_cn_name,prod_rnd_team_cn_name,prod_rnd_team_catg_code,
        amp.group_level,amp.group_code,amp.group_cn_name,groupCodeAndName,amp.period_year,status.status_code,
        amp.dimension_code,amp.dimension_subcategory_code, amp.dimension_sub_detail_code
        <if test='condition != null and condition !="" and condition =="minLevel"'>
            order by locate(amp.lv_code, #{prodRndTeamCodeOrder}), amp.period_year
        </if>
        <if test='condition == null or condition ==""'>
            order by locate(amp.group_code, #{groupCodeOrder}), amp.period_year
        </if>
    </select>

    <select id="industryPbiCostList" resultMap="annualResultMap">
        select prod_rnd_team_cn_name,prod_rnd_team_code,
        dimension_code,dimension_cn_name,
        dimension_subcategory_code,dimension_subcategory_cn_name,
        dimension_sub_detail_code,dimension_sub_detail_cn_name,
        parent_code,parent_cn_name,period_year,annual_amp,group_code,group_level,group_cn_name,
        weight_rate, status_code, annual_amp * weight_rate/100 as weightAnnualAmpPercent, append_year,
        append_flag from
        (select distinct amp.group_level,
        amp.dimension_code,amp.dimension_cn_name,
        amp.dimension_subcategory_code,amp.dimension_subcategory_cn_name,
        amp.dimension_sub_detail_code,amp.dimension_sub_detail_cn_name,
        amp.parent_code,amp.parent_cn_name,amp.period_year,
        ROUND(amp.annual_amp*100,1) as annual_amp,amp.group_code,
        amp.group_cn_name,
        <choose>
            <when test='isMultipleSelect == true'>
                weight.absolute_weight*100 as weight_rate,
            </when>
            <otherwise>
                weight.weight_rate*100 as weight_rate,
            </otherwise>
        </choose>
        status.status_code as status_code,weight.append_flag, status.append_year,
        <choose>
            <when test='granularityType == "IRB"'>
                amp.prod_rnd_team_code,amp.prod_rd_team_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "INDUS"'>
                amp.industry_catg_code as prod_rnd_team_code, amp.industry_catg_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "PROD"'>
                amp.prod_list_code as prod_rnd_team_code, amp.prod_list_cn_name as prod_rnd_team_cn_name
            </when>
            <otherwise>
            </otherwise>
        </choose>
        from fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_AMP_T amp
        left join fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_WEIGHT_T weight
        on amp.view_flag = weight.view_flag and amp.group_code = weight.group_code
        and amp.group_level = weight.group_level
        and amp.parent_code = weight.parent_code
        and amp.version_id =weight.version_id and amp.period_year = weight.period_year
        and amp.oversea_flag = weight.oversea_flag
        and amp.bg_code = weight.bg_code
        and amp.region_code = weight.region_code
        and amp.repoffice_code = weight.repoffice_code
        and nvl(amp.dimension_code,'snull') = nvl( weight.dimension_code,'snull')
        and nvl(amp.dimension_subcategory_code,'snull') = nvl( weight.dimension_subcategory_code,'snull')
        and nvl(amp.dimension_sub_detail_code,'snull') = nvl( weight.dimension_sub_detail_code,'snull')
        and nvl ( amp.main_flag, 'snull' ) = nvl ( weight.main_flag, 'snull' )
        and nvl ( amp.code_attributes, 'snull' ) = nvl ( weight.code_attributes, 'snull' )
        <choose>
            <when test='granularityType == "IRB"'>
                and amp.prod_rnd_team_code = weight.prod_rnd_team_code
            </when>
            <when test='granularityType == "INDUS"'>
                and amp.industry_catg_code = weight.industry_catg_code
            </when>
            <when test='granularityType == "PROD"'>
                and amp.prod_list_code = weight.prod_list_code
            </when>
            <otherwise>
            </otherwise>
        </choose>
        left join fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_STATUS_T status
        on amp.group_code = status.group_code and amp.group_level = status.group_level
        and amp.period_year = status.period_year and amp.version_id = status.version_id
        and amp.view_flag = status.view_flag
        and amp.parent_code = status.parent_code
        and amp.oversea_flag = status.oversea_flag
        and amp.bg_code = status.bg_code
        and amp.region_code = status.region_code
        and amp.repoffice_code = status.repoffice_code
        and nvl(amp.dimension_code,'snull') = nvl( status.dimension_code,'snull')
        and nvl(amp.dimension_subcategory_code,'snull') = nvl( status.dimension_subcategory_code,'snull')
        and nvl(amp.dimension_sub_detail_code,'snull') = nvl( status.dimension_sub_detail_code,'snull')
        and nvl ( amp.main_flag, 'snull' ) = nvl ( status.main_flag, 'snull' )
        and nvl ( amp.code_attributes, 'snull' ) = nvl ( status.code_attributes, 'snull' )
        <choose>
            <when test='granularityType == "IRB"'>
                and amp.prod_rnd_team_code = status.prod_rnd_team_code
            </when>
            <when test='granularityType == "INDUS"'>
                and amp.industry_catg_code = status.industry_catg_code
            </when>
            <when test='granularityType == "PROD"'>
                and amp.prod_list_code = status.prod_list_code
            </when>
            <otherwise>
            </otherwise>
        </choose>
        where amp.del_flag = 'N'
        <if test='overseaFlag != null and overseaFlag != ""'>
            and amp.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and amp.bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and amp.region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and amp.repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag != null and mainFlag != ""'>
            and amp.main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes != null and codeAttributes != ""'>
            and amp.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and amp.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and amp.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='dimensionCode != null and dimensionCode != ""'>
            and amp.dimension_code = #{dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubCategoryCode != null and dimensionSubCategoryCode != ""'>
            and amp.dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubDetailCode != null and dimensionSubDetailCode != ""'>
            and amp.dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and amp.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='periodYear != null and periodYear !=""'>
            and amp.period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='year != null and year !=""'>
            and amp.period_year = #{year,jdbcType=VARCHAR}
        </if>
        <if test='parentCodeList != null and parentCodeList.size() > 0'>
            <foreach collection='parentCodeList' item="code" open="AND amp.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='prodRndTeamCodeList != null and prodRndTeamCodeList.size() > 0'>
            <choose>
                <when test='granularityType == "IRB"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.prod_rnd_team_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='granularityType == "INDUS"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.industry_catg_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='granularityType == "PROD"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.prod_list_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND amp.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        )
        order by period_year,weight_rate desc,group_cn_name
    </select>

    <select id="industryMinLevelCostList" resultMap="annualResultMap">
        select prod_rnd_team_cn_name,prod_rnd_team_code,
        dimension_code,dimension_cn_name,
        dimension_subcategory_code,dimension_subcategory_cn_name,
        dimension_sub_detail_code,dimension_sub_detail_cn_name,
        parent_code,parent_cn_name,period_year,annual_amp,group_code,group_level,group_cn_name,
        weight_rate, status_code, annual_amp * weight_rate/100 as weightAnnualAmpPercent, append_year
        from
        (select distinct amp.group_level,
        amp.dimension_code,amp.dimension_cn_name,
        amp.dimension_subcategory_code,amp.dimension_subcategory_cn_name,
        amp.dimension_sub_detail_code,amp.dimension_sub_detail_cn_name,
        amp.parent_code,amp.parent_cn_name,amp.period_year,
        ROUND(amp.annual_amp*100,1) as annual_amp,amp.group_code,
        amp.group_cn_name,weight.weight_rate*100 as weight_rate,
        status.status_code as status_code, status.append_year,
        amp.lv_code as prod_rnd_team_code,amp.lv_cn_name as prod_rnd_team_cn_name
        from fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_MID_GROUP_AMP_T amp
        left join fin_dm_opt_foi.DM_FCST_ICT_${combTablePreFix}_BASE_CUS_ANNL_WEIGHT_T weight
        on amp.view_flag = weight.view_flag and amp.group_code = weight.group_code
        and amp.group_level = weight.group_level
        and amp.parent_code = weight.parent_code
        and amp.version_id =weight.version_id and amp.period_year = weight.period_year
        and amp.oversea_flag = weight.oversea_flag
        and amp.bg_code = weight.bg_code
        and amp.region_code = weight.region_code
        and amp.repoffice_code = weight.repoffice_code
        and nvl ( amp.main_flag, 'snull' ) = nvl ( weight.main_flag, 'snull' )
        and nvl ( amp.code_attributes, 'snull' ) = nvl ( weight.code_attributes, 'snull' )
        and amp.lv_code = weight.lv4_code
        and weight.custom_id = #{customId}
        left join fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_STATUS_T status
        on amp.group_code = status.group_code and amp.group_level = status.group_level
        and amp.period_year = status.period_year and amp.version_id = status.version_id
        and amp.view_flag = status.view_flag
        and amp.parent_code = status.parent_code
        and amp.oversea_flag = status.oversea_flag
        and amp.bg_code = status.bg_code
        and amp.region_code = status.region_code
        and amp.repoffice_code = status.repoffice_code
        and nvl(amp.dimension_code,'snull') = nvl( status.dimension_code,'snull')
        and nvl(amp.dimension_subcategory_code,'snull') = nvl( status.dimension_subcategory_code,'snull')
        and nvl(amp.dimension_sub_detail_code,'snull') = nvl( status.dimension_sub_detail_code,'snull')
        and nvl ( amp.main_flag, 'snull' ) = nvl ( status.main_flag, 'snull' )
        and nvl ( amp.code_attributes, 'snull' ) = nvl ( status.code_attributes, 'snull' )
        <choose>
            <when test='granularityType == "IRB"'>
                and amp.lv_code = status.prod_rnd_team_code
            </when>
            <when test='granularityType == "INDUS"'>
                and amp.lv_code = status.industry_catg_code
            </when>
            <when test='granularityType == "PROD"'>
                and amp.lv_code = status.prod_list_code
            </when>
            <otherwise>
            </otherwise>
        </choose>
        where amp.del_flag = 'N'
        <if test='overseaFlag != null and overseaFlag != ""'>
            and amp.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and amp.bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and amp.region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and amp.repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag != null and mainFlag != ""'>
            and amp.main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes != null and codeAttributes != ""'>
            and amp.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and amp.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and amp.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='dimensionCode != null and dimensionCode != ""'>
            and amp.dimension_code = #{dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubCategoryCode != null and dimensionSubCategoryCode != ""'>
            and amp.dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubDetailCode != null and dimensionSubDetailCode != ""'>
            and amp.dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and amp.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='periodYear != null and periodYear !=""'>
            and amp.period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='year != null and year !=""'>
            and amp.period_year = #{year,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != "" and viewFlag == "PROD_SPART"'>
            and amp.group_level = 'SPART'
        </if>
        <if test='viewFlag != null and viewFlag != "" and viewFlag == "DIMENSION"'>
            and amp.group_level = 'SUB_DETAIL'
        </if>
        <if test='groupCodeList != null and groupCodeList.size() > 0'>
            <foreach collection='groupCodeList' item="code" open="AND amp.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='prodRndTeamCodeList != null and prodRndTeamCodeList.size() > 0'>
            <choose>
                <when test='teamLevel == "LV0"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.lv0_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='teamLevel == "LV1"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.lv1_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='teamLevel == "LV2"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.lv2_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='teamLevel == "LV3"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.lv3_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='teamLevel == "LV4"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.lv4_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND amp.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        )
        order by period_year,weight_rate desc,group_cn_name
    </select>

    <select id="distributePbiCostChart" resultMap="annualResultMap">
        select distinct
        dimension_code,dimension_cn_name,
        dimension_subcategory_code,dimension_subcategory_cn_name,
        dimension_sub_detail_code,dimension_sub_detail_cn_name,
        group_level,group_code,group_cn_name,parent_code,parent_cn_name,
        period_year,ROUND(rmb_cost_amt/10000,1) rmb_cost_amt,
        <choose>
            <when test='granularityType == "IRB"'>
                prod_rd_team_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "INDUS"'>
                industry_catg_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "PROD"'>
                prod_list_cn_name as prod_rnd_team_cn_name
            </when>
            <otherwise>
            </otherwise>
        </choose>
        from fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_ANNL_COST_T
        where del_flag = 'N'
        <if test='overseaFlag != null and overseaFlag != ""'>
            and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag != null and mainFlag != ""'>
            and main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes != null and codeAttributes != ""'>
            and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='dimensionCode != null and dimensionCode != ""'>
            and dimension_code = #{dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubCategoryCode != null and dimensionSubCategoryCode != ""'>
            and dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubDetailCode != null and dimensionSubDetailCode != ""'>
            and dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
        <if test='groupCodeList != null and groupCodeList.size() > 0'>
            <foreach collection='groupCodeList' item="code" open="AND group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='prodRndTeamCodeList != null and prodRndTeamCodeList.size() > 0'>
            <choose>
                <when test='granularityType == "IRB"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND prod_rnd_team_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='granularityType == "INDUS"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND industry_catg_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='granularityType == "PROD"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND prod_list_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='periodYear != null and periodYear != ""'>
            and period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        order by period_year,rmb_cost_amt desc
    </select>

</mapper>