/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.month;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * The Entity of DmFocMonthYoyT
 *
 * <AUTHOR>
 * @since 2023/03/21
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DmFocMonthYoyVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     **/
    private Long id;

    /**
     * 版本ID
     **/
    private Long versionId;

    /**
     * 会计年
     **/
    private Long periodYear;

    /**
     * 会计期
     **/
    private Long periodId;

    /**
     * 基期
     **/
    private Long basePeriodId;

    /**
     * 重量级团队CODE
     **/
    private String prodRndTeamCode;

    /**
     * 量级团队中文名称
     **/
    private String prodRndTeamCnName;

    /**
     * 分层级CODE
     **/
    private String groupCode;

    /**
     * 分层级中文名称
     **/
    private String groupCnName;

    /**
     * GROUP层级(ICT/LV1/LV2/CEG/CATEGORY/ITEM)
     **/
    private String groupLevel;

    /**
     * 同比值/环比值
     **/
    private Double yoyRate;

    /**
     * 同比值/环比占比
     **/
    private String yoyPercent;

    /**
     * 同环比标识（Y：同比，P：环比）
     **/
    private String yoyFlag;

    /**
     * 父级CODE
     **/
    private String parentCode;

    /**
     * 父级Name
     **/
    private String parentCnName;

    /**
     * 创建人
     **/
    private String createdBy;

    /**
     * 创建日期
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private Timestamp creationDate;

    /**
     * 最后更新人
     **/
    private String lastUpdatedBy;

    /**
     * 最后更新日期
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private Timestamp lastUpdateDate;

    /**
     * 删除标识(未删除：N，已删除：Y)
     **/
    private String delFlag;

    /**
     * 视角标识，用于区分不同视角下的品类数据(0: ICT层级, 1:ICT-LV1层级, 2:ICT-LV1-LV2层级)
     **/
    private String viewFlag;

    /**
     * l1
     **/
    private String l1Name;

    /**
     * l2
     **/
    private String l2Name;


    /**
     * 量纲颗粒度code
     **/
    private String dmsCode;

    /**
     * 量纲颗粒度名称
     **/
    private String dmsCnName;

    // COA编码
    private String coaCode;

    // COA名称
    private String coaCnName;

    /**
     * 量纲颗粒度code
     **/
    private String dimensionCode;

    /**
     * 量纲颗粒度名称
     **/
    private String dimensionCnName;

    /**
     * 量纲颗粒度code
     **/
    private String dimensionSubCategoryCode;

    /**
     * 量纲颗粒度名称
     **/
    private String dimensionSubCategoryCnName;

    /**
     * 量纲颗粒度code
     **/
    private String dimensionSubDetailCode;

    /**
     * 量纲颗粒度名称
     **/
    private String dimensionSubDetailCnName;

    /**
     * spart code
     **/
    private String spartCode;

    /**
     * spart名称
     **/
    private String spartCnName;

    /**
     * 组合名称
     **/
    private String customCnName;

    /**
     * 顶部的成本类型下拉框: 制造成本：MANUFACTURE，采购成本：PURCHASE，总成本：TOTAL
     *
     */
    private String costType;
}
