/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.replace;

import cn.hutool.core.map.MapUtil;
import com.huawei.it.fcst.industry.index.enums.IndustryIndexEnum;
import com.huawei.it.fcst.industry.index.vo.annual.CommonAnnualVO;
import com.huawei.it.fcst.industry.index.vo.annual.DmFocAnnualAmpVO;
import com.huawei.it.fcst.industry.index.vo.replace.ReplaceAnalysisVO;
import com.huawei.it.fcst.util.PinyinUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import javax.inject.Named;
import java.math.BigDecimal;
import java.text.Collator;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/11/30
 */
@Named("commonAmpService")
public class CommonAmpService {

    /**
     * setNoEffectiveAmp 设置无效的涨跌幅提示语
     *
     * @param dmFocAnnualAmpVOList
     * @param type
     */
    public void setNoEffectiveAmp(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList, String type) {
        dmFocAnnualAmpVOList.stream().forEach(annualAmp -> {
            String statusCode = annualAmp.getStatusCode();
            if (StringUtils.isNotBlank(statusCode)) {
                setGroupCodeHoverMsg(statusCode, annualAmp);
            }
        });
        setPromptMessage(dmFocAnnualAmpVOList, type);
    }

    private void setGroupCodeHoverMsg(String statusCode, DmFocAnnualAmpVO dmFocAnnualAmpVO) {
        switch (statusCode) {
            case "1":
                dmFocAnnualAmpVO.setHoverMsg(dmFocAnnualAmpVO.getPeriodYear() + "年无数据，无法计算涨跌幅");
                break;
            case "2":
                dmFocAnnualAmpVO.setHoverMsg(subtractNum(dmFocAnnualAmpVO.getPeriodYear(), 1) + "年无数据，无法计算涨跌幅");
                break;
            case "4":
                dmFocAnnualAmpVO.setHoverMsg(subtractNum(dmFocAnnualAmpVO.getPeriodYear(), 1) + "、" + dmFocAnnualAmpVO.getPeriodYear() + "年无数据，无法计算涨跌幅");
                break;
            default:
                break;
        }
    }

    private void setPromptMessage(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList, String type) {
        // 当涨跌幅为0且有提示语时，涨跌幅设置为0*
        dmFocAnnualAmpVOList.stream().forEach(annualAmp -> {
            if (StringUtils.isNotBlank(annualAmp.getHoverMsg()) && !"5".equals(annualAmp.getStatusCode())
                    && ("0.0%".equals(annualAmp.getAnnualAmp()) || "0.0".equals(annualAmp.getAnnualAmp()))) {
                if ("data".equals(type)) {
                    annualAmp.setAnnualAmp("0*");
                    annualAmp.setWeightAnnualAmpPercent("0*");
                } else {
                    annualAmp.setAnnualAmp(null);
                    annualAmp.setWeightAnnualAmpPercent(null);
                }
            }
            if (StringUtils.isNotBlank(annualAmp.getAppendFlag()) && "Y".equals(annualAmp.getAppendFlag())) {
                if ("data".equals(type)) {
                    annualAmp.setWeightRate("0*");
                } else {
                    annualAmp.setWeightRate(null);
                }
            }
        });
    }

    public String subtractNum(String year, int num) {
        BigDecimal bigDecimal = new BigDecimal(year);
        BigDecimal subtract = bigDecimal.subtract(new BigDecimal(num));
        return subtract.toString();
    }

    public List<DmFocAnnualAmpVO> dealGroupLevelWeightAndAmp(List<DmFocAnnualAmpVO> annualAmpAndWeightList, ReplaceAnalysisVO replaceAnalysisVO, boolean flag) {
        sortByWeight(annualAmpAndWeightList);
        // 获取权重*涨跌最大值 为前端提供
        if (flag) {
            if(IndustryIndexEnum.REPLACE_COST_TYPE.STD.getValue().equals(replaceAnalysisVO.getCostType())) {
                getMaxAnnualAmp(annualAmpAndWeightList, replaceAnalysisVO);
            } else {
                getMaxWeightAnnualAmp(annualAmpAndWeightList, replaceAnalysisVO);
            }
        }
        // 拼接%，保留一位小数
        concatPercent(annualAmpAndWeightList, flag);
        return annualAmpAndWeightList;
    }

    private void concatPercent(List<DmFocAnnualAmpVO> annualAmpAndWeightList, boolean flag) {
        annualAmpAndWeightList.stream().forEach(annual -> {
            String annualAmp = annual.getAnnualAmp();
            String weightRate = annual.getWeightRate();
            String weightAnnualAmpPercent = annual.getWeightAnnualAmpPercent();
            if (StringUtils.isNotBlank(annualAmp) && !annualAmp.equals("0*")) {
                annual.setAnnualAmp(annualAmp + "%");
            }
            if (StringUtils.isNotBlank(weightRate) && !weightRate.equals("0*")) {
                if (flag) {
                    weightRate = new BigDecimal(weightRate).setScale(1, BigDecimal.ROUND_HALF_UP).toString();
                }
                annual.setWeightRate(weightRate + "%");
            }
            if (StringUtils.isNotBlank(weightAnnualAmpPercent) && !weightAnnualAmpPercent.equals("0*")) {
                String newWeightAnnualAmpPercent = new BigDecimal(weightAnnualAmpPercent).setScale(1, BigDecimal.ROUND_HALF_UP).toString();
                annual.setWeightAnnualAmpPercent(newWeightAnnualAmpPercent + "%");
            }
        });
    }

    private void getMaxWeightAnnualAmp(List<DmFocAnnualAmpVO> annualAmpAndWeightList, ReplaceAnalysisVO replaceAnalysisVO) {
        List<String> annualPercentList = new ArrayList<>();
        List<String> weightAnnualAmpPercentList = annualAmpAndWeightList.stream().map(DmFocAnnualAmpVO::getWeightAnnualAmpPercent).collect(Collectors.toList());
        Double weightPercent = 0.0D;
        for (String weightAnnualAmpPercent : weightAnnualAmpPercentList) {
            if (StringUtils.isNotBlank(weightAnnualAmpPercent)) {
                weightPercent = Math.abs(Double.parseDouble(weightAnnualAmpPercent));
            }
            annualPercentList.add(new BigDecimal(String.valueOf(weightPercent)).setScale(1, BigDecimal.ROUND_HALF_UP).toString());
        }
        if (CollectionUtils.isNotEmpty(annualPercentList)) {
            String maxWeightAnnual = Collections.max(annualPercentList);
            replaceAnalysisVO.setMaxValue(maxWeightAnnual);
        }
    }

    private void getMaxAnnualAmp(List<DmFocAnnualAmpVO> annualAmpAndWeightList, ReplaceAnalysisVO replaceAnalysisVO) {
        List<String> annualAmpPercentList = new ArrayList<>();
        List<DmFocAnnualAmpVO> stdAnnualAmpList = annualAmpAndWeightList.stream().filter(result -> IndustryIndexEnum.REPLACE_COST_TYPE.STD.getValue().equals(result.getCostType())).collect(Collectors.toList());
        List<String> annualAmpList = stdAnnualAmpList.stream().map(DmFocAnnualAmpVO::getAnnualAmp).collect(Collectors.toList());
        Double annualAmp = 0.0D;
        for (String annualAmpStr : annualAmpList) {
            if (StringUtils.isNotBlank(annualAmpStr)) {
                annualAmp = Math.abs(Double.parseDouble(annualAmpStr));
            }
            annualAmpPercentList.add(new BigDecimal(String.valueOf(annualAmp)).setScale(1, BigDecimal.ROUND_HALF_UP).toString());
        }
        if (CollectionUtils.isNotEmpty(annualAmpPercentList)) {
            String maxAnnualAmp = Collections.max(annualAmpPercentList);
            replaceAnalysisVO.setMaxValue(maxAnnualAmp);
        }
    }

    public void setAnnaulAmpVOList(List<DmFocAnnualAmpVO> ananualAmpVOList, Map<String, List<DmFocAnnualAmpVO>> mapDmFocAnnualAmpList) {
        for (String groupCodeAndCnName: mapDmFocAnnualAmpList.keySet()) {
            List<DmFocAnnualAmpVO> resultAnnualAmpVO = MapUtil.get(mapDmFocAnnualAmpList, groupCodeAndCnName, List.class);
            DmFocAnnualAmpVO resultMap = new DmFocAnnualAmpVO();
            for (int i = 0; i < resultAnnualAmpVO.size(); i++) {
                String groupCode = resultAnnualAmpVO.get(i).getGroupCode();
                String groupName = resultAnnualAmpVO.get(i).getGroupCnName();
                String periodYear = resultAnnualAmpVO.get(i).getPeriodYear();
                if (IndustryIndexEnum.REPLACE_COST_TYPE.STD.getValue().equals(resultAnnualAmpVO.get(i).getCostType())) {
                    String annualAmp = resultAnnualAmpVO.get(i).getAnnualAmp();
                    String weightRate = resultAnnualAmpVO.get(i).getWeightRate();
                    resultMap.setAnnualAmp(annualAmp);
                    resultMap.setWeightRate(weightRate);
                }
                if (IndustryIndexEnum.REPLACE_COST_TYPE.SAME.getValue().equals(resultAnnualAmpVO.get(i).getCostType())) {
                    String sameAnnualAmp = resultAnnualAmpVO.get(i).getAnnualAmp();
                    String sameWeightRate = resultAnnualAmpVO.get(i).getWeightRate();
                    String sameWeightAnnualAmpPercent = resultAnnualAmpVO.get(i).getWeightAnnualAmpPercent();
                    resultMap.setSameWeightAnnualAmpPercent(sameWeightAnnualAmpPercent);
                    resultMap.setSameAnnualAmp(sameAnnualAmp);
                    resultMap.setSameWeightRate(sameWeightRate);
                }
                if (IndustryIndexEnum.REPLACE_COST_TYPE.REPLACE.getValue().equals(resultAnnualAmpVO.get(i).getCostType())) {
                    String replAnnualAmp = resultAnnualAmpVO.get(i).getAnnualAmp();
                    String replWeightRate = resultAnnualAmpVO.get(i).getWeightRate();
                    String replWeightAnnualAmpPercent = resultAnnualAmpVO.get(i).getWeightAnnualAmpPercent();
                    resultMap.setReplWeightAnnualAmpPercent(replWeightAnnualAmpPercent);
                    resultMap.setReplAnnualAmp(replAnnualAmp);
                    resultMap.setReplWeightRate(replWeightRate);
                }
                resultMap.setGroupCnName(groupName);
                resultMap.setPeriodYear(periodYear);
                if (StringUtils.isNotBlank(groupCode)) {
                    resultMap.setGroupCode(groupCode);
                }
            }
            ananualAmpVOList.add(resultMap);
        }
    }

    /**
     * 按照权重排序
     *
     * @param annualAmpWeightList 参数
     */
    public void sortByWeight(List<DmFocAnnualAmpVO> annualAmpWeightList) {
        Collections.sort(annualAmpWeightList, new Comparator<DmFocAnnualAmpVO>() {
            @Override
            public int compare(DmFocAnnualAmpVO dmFocAnnualAmp1, DmFocAnnualAmpVO dmFocAnnualAmp2) {
                String weightRate1 = dmFocAnnualAmp1.getWeightRate();
                String weightRate2 = dmFocAnnualAmp2.getWeightRate();
                Double weightNum1 = null;
                Double weightNum2 = null;
                if (StringUtils.isNotBlank(weightRate1) && !weightRate1.equals("%")) {
                    weightRate1 = subPercentStr(weightRate1);
                    weightNum1 = Double.parseDouble(weightRate1);
                }
                if (StringUtils.isNotBlank(weightRate2) && !weightRate2.equals("%")) {
                    weightRate2 = subPercentStr(weightRate2);
                    weightNum2 = Double.parseDouble(weightRate2);
                }
                if (weightNum1 == null && weightNum2 == null) {
                    return sortByGroupCnName(dmFocAnnualAmp1, dmFocAnnualAmp2);
                }
                if (weightNum1 == null) {
                    return 1;
                }
                if (weightNum2 == null) {
                    return -1;
                }
                if (weightNum1 != null && weightNum2 != null && ObjectUtils.notEqual(weightNum1, weightNum2)) {
                    return weightNum2.compareTo(weightNum1);
                }
                if (weightNum1 != null && weightNum2 != null && Double.toString(weightNum1).equals(Double.toString(weightNum2))) {
                    return sortByGroupCnName(dmFocAnnualAmp1, dmFocAnnualAmp2);
                }
                return 0;
            }
        });
    }

    private String subPercentStr(String str) {
        if (str.contains("%")) {
            int index = str.indexOf("%");
            return str.substring(0, index);
        }
        return str;
    }

    /**
     * 权重相同时，根据中文或英文首字符进行排序
     *
     * @param dmFocAnnualAmp1 参数1
     * @param dmFocAnnualAmp2 参数2
     * @return 比较结果
     */
    public int sortByGroupCnName(DmFocAnnualAmpVO dmFocAnnualAmp1, DmFocAnnualAmpVO dmFocAnnualAmp2) {
        String groupCnName1 = dmFocAnnualAmp1.getGroupCnName() == null ? dmFocAnnualAmp1.getGroupCode() : dmFocAnnualAmp1.getGroupCnName();
        String groupCnName2 = dmFocAnnualAmp2.getGroupCnName() == null ? dmFocAnnualAmp2.getGroupCode() : dmFocAnnualAmp2.getGroupCnName();

        String alphabetOne = groupCnName1.substring(0, 1);
        // 判断首字符是否为中文，如果是中文便将首字符拼音的首字母和&符号加在字符串前面
        if (alphabetOne.matches("[\\u4e00-\\u9fa5]+")) {
            groupCnName1 = PinyinUtil.chineseToPingyin(alphabetOne) + "&" + groupCnName1;
        }
        String alphabetTwo = groupCnName2.substring(0, 1);
        // 判断首字符是否为中文，如果是中文便将首字符拼音的首字母和&符号加在字符串前面
        if (alphabetTwo.matches("[\\u4e00-\\u9fa5]+")) {
            groupCnName2 = PinyinUtil.chineseToPingyin(alphabetTwo) + "&" + groupCnName2;
        }
        return Collator.getInstance(Locale.CHINA).compare(groupCnName1, groupCnName2);
    }

    public int compareWeightColumn(String weightRate1, String weightRate2) {
        CommonAnnualVO commonAnnualVO = transformStr(weightRate1, weightRate2);
        Double numOne = commonAnnualVO.getNum1();
        Double numTwo = commonAnnualVO.getNum2();
        if (numOne != null && numTwo != null && ObjectUtils.notEqual(numOne, numTwo)) {
            return numOne.compareTo(numTwo);
        }
        if (numOne == null && numTwo == null) {
            return 0;
        }
        if (numOne == null) {
            return -1;
        }
        if (numTwo == null) {
            return 1;
        }
        return 0;
    }

    private CommonAnnualVO transformStr(String strOne, String strTwo) {
        CommonAnnualVO commonVO = new CommonAnnualVO();
        Double num1 = null;
        Double num2 = null;
        if (StringUtils.isNotBlank(strOne) && !strOne.equals("0*")) {
            strOne = subPercentStr(strOne);
            num1 = Double.parseDouble(strOne);
        }
        if (StringUtils.isNotBlank(strTwo) && !strTwo.equals("0*")) {
            strTwo = subPercentStr(strTwo);
            num2 = Double.parseDouble(strTwo);
        }
        commonVO.setNum1(num1);
        commonVO.setNum2(num2);
        return commonVO;
    }

}
