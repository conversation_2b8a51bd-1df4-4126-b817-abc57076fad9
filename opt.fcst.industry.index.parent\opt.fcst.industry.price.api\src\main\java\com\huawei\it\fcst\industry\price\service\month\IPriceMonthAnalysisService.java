/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.service.month;

import com.huawei.it.fcst.industry.price.vo.month.PriceMonthAnalysisVO;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.Map;

/**
 * 定价指数月度分析页面接口类
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Path("/monthlyAnalysis")
@Produces(MediaType.APPLICATION_JSON)
public interface IPriceMonthAnalysisService {

    /**
     * [查询多选下拉框列表]
     *     综合指数分析-定价指数图（多子项）正常维度多选下拉框
     *
     * @param monthAnalysisVO MonthAnalysisVO
     * @return ResultDataVO
     * @throws ApplicationException
     */
    @POST
    @Path("/multi/boxList")
    ResultDataVO getMultiBoxList(PriceMonthAnalysisVO monthAnalysisVO) throws ApplicationException;


    /**
     * [查询多选下拉框列表]
     *     综合指数分析-定价指数图（多子项）虚化维度多选下拉框
     *
     * @param monthAnalysisVO 页面查询参数
     * @return ResultDataVO ResultDataVO
     * @throws ApplicationException
     */
    @POST
    @Path("/multi/dropdown/list")
    ResultDataVO getMultiDropdownList(PriceMonthAnalysisVO monthAnalysisVO) throws ApplicationException;

    /**
     * 定价指数图查询
     *
     * @param monthAnalysisVO 页面查询参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/priceIndex/chart")
    ResultDataVO getPriceIndexChart(PriceMonthAnalysisVO monthAnalysisVO) throws ApplicationException;

    /**
     * 查询定价指数图（多子项）
     *
     * @param monthAnalysisVO 页面查询参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/multi/priceIndex/chart")
    ResultDataVO getMultiPriceIndexChart(PriceMonthAnalysisVO monthAnalysisVO) throws ApplicationException;

    /**
     * 查询定价指数同环比图
     *
     * @param monthAnalysisVO 页面查询参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/priceIndex/yoyAndpop")
    ResultDataVO getPriceIndexYoyAndPopChart(PriceMonthAnalysisVO monthAnalysisVO) throws ApplicationException;

    /**
     * 定价指数-月度分析-切换基期
     *
     * @param monthAnalysisVO 参数VO
     * @return ResultDataVO
     * @throws ApplicationException
     */
    @POST
    @Path("/index/switchBasePeriodId")
    ResultDataVO switchBasePeriodId(PriceMonthAnalysisVO monthAnalysisVO) throws ApplicationException;

    /**
     * 定价指数-综合指数分析-数据下载
     *
     * @param monthAnalysisVO 页面查询参数
     * @param response 响应
     * @return ResultDataVO ResultDataVO
     * @throws ApplicationException
     */
    @POST
    @Path("/detailData/export")
    ResultDataVO detailDataExport(PriceMonthAnalysisVO monthAnalysisVO, @Context HttpServletResponse response) throws ApplicationException;

    /**
     * 获取导出表头信息
     *
     * @param monthAnalysisVO 页面查询参数
     * @return Map
     * @throws ApplicationException
     */
    Map<String, Object> getHeaderInfo(PriceMonthAnalysisVO monthAnalysisVO) throws ApplicationException;

}