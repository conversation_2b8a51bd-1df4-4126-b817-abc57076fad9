/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.replace;

import com.huawei.it.fcst.industry.index.vo.annual.DmFocAnnualAmpVO;
import com.huawei.it.jalor5.core.base.PageVO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * CommonAnnualVO Class
 *
 * <AUTHOR>
 * @since 2023/4/7
 */
@Getter
@Setter
@NoArgsConstructor
public class ResultAnnualVO {

    private List<DmFocAnnualAmpVO> result;

    private PageVO pageVo;

    private String maxValue;
}
