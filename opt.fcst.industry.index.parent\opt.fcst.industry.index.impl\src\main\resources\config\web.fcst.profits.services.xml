<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jaxws="http://cxf.apache.org/jaxws"
	xmlns:jaxrs="http://cxf.apache.org/jaxrs"
	xsi:schemaLocation="
	 http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
     http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
     http://cxf.apache.org/jaxws http://cxf.apache.org/schemas/jaxws.xsd
     http://cxf.apache.org/jaxrs http://cxf.apache.org/schemas/jaxrs.xsd">
	<jaxrs:server id="fcstConfigManageRest" address="/">
		<jaxrs:serviceBeans>
			<ref bean="annualAmpService"/>
			<ref bean="annualCommonService"/>
			<ref bean="personalCenterService"/>
			<ref bean="statisticsExcelService"/>
			<ref bean="configDimensionManageService"/>
			<ref bean="configDimensionService"/>
			<ref bean="configHistoryService"/>
			<ref bean="monthAnalysisService"/>
			<ref bean="commonService"/>
			<ref bean="dataDimensionService"/>
			<ref bean="dataProcess"/>
			<ref bean="customCombService"/>
			<ref bean="compareAnalysisService"/>
			<ref bean="customService"/>
			<ref bean="configReviewService"/>
			<ref bean="configManufactureService"/>
			<ref bean="configBottomReviewService"/>
			<ref bean="noticeInfoService"/>
			<ref bean="guideService"/>
			<ref bean="standardService"/>
			<ref bean="replaceAmpService"/>
			<ref bean="replaceDropDownService"/>
		</jaxrs:serviceBeans>
		<jaxrs:providers>
			<ref bean="jsonProvider" />
			<ref bean="errorHandlerProvider" />
		</jaxrs:providers>
	</jaxrs:server>
</beans>