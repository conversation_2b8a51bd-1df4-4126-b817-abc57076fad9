/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.config.DimensionParamVO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocCatgCegIctDTO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/3/13
 */
public interface IDmFocCatgCegIctDao {
    /**
     * 通过版本ID获取专项采购认证部信息
     *
     * @param versionId 参数
     * @return List<DmFocCatgCegIctDTO>
     */
    List<DmFocCatgCegIctDTO> getL3ListByVersionId(@Param("tablePreFix") String tablePreFix, @Param("versionId") Long versionId,@Param("l3CegCode")String l3CegCode);

    PagedResult<DmFocCatgCegIctDTO> findByPage(@Param("dimensionParamVO")DimensionParamVO dimensionParamVO, @Param("pageVO")PageVO pageVO);

    int updateDmFocCatgCegIctList(List<DmFocCatgCegIctDTO> dmFocCatgCegIctDTOList);

    int createDmFocCatgCegIctLists(@Param("list") List<DmFocCatgCegIctDTO> dmFocCatgCegIctDTOList, @Param("tablePreFix") String tablePreFix);

    List<DmFocCatgCegIctDTO> findDmFocCatgCegIctStatus(@Param("tablePreFix") String tablePreFix,@Param("versionId") Long versionId);

    List<DmFocCatgCegIctDTO> findL3ListByKeyword(DimensionParamVO build);
}
