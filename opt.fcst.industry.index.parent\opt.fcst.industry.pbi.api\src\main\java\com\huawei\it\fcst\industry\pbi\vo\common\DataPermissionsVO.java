/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * DataPermissionsVO Class
 *
 * <AUTHOR>
 * @since 2022-11-10
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "数据权限维度VO")
public class DataPermissionsVO implements Serializable {
    private static final long serialVersionUID = 6110390781244573970L;

    private String roleName;

    private int roleId;

    @ApiModelProperty(value = "ICT经管LV0集合")
    private Set<String> lv0DimensionSet = new HashSet<>();

    @ApiModelProperty(value = "LV1集合")
    private Set<String> lv1DimensionSet  = new HashSet<>();

    @ApiModelProperty(value = "LV2集合")
    private Set<String> lv2DimensionSet  = new HashSet<>();

    @ApiModelProperty(value = "ICT项目需要用到LV3层级")
    private Set<String> lv3DimensionSet  = new HashSet<>();

    @ApiModelProperty(value = "成本类型集合")
    private Set<String> costTypeSet  = new HashSet<>();

    @ApiModelProperty(value = "BG集合")
    private Set<String> bgDimensionSet  = new HashSet<>();

    private Set<String> overseaFlagDimensionSet = new HashSet<>();

    private Set<String> regionCodeDimensionSet = new HashSet<>();

    // 非补齐的地区部set
    private Set<String> regionCodeDimensionTrueSet = new HashSet<>();

    private Set<String> repofficeCodeDimensionSet = new HashSet<>();

    private Map<String, Map<String, Set<String>>> locationMap = new HashMap<>();

}