/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.month;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.huawei.it.fcst.config.ConfigUtil;
import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.industry.pbi.dao.IDmCustomDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstDataRefreshStatusDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstDimInfoDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFocVarifyTaskDao;
import com.huawei.it.fcst.industry.pbi.dao.IctMonthCostIdxDao;
import com.huawei.it.fcst.industry.pbi.enums.IndustryConstEnum;
import com.huawei.it.fcst.industry.pbi.impl.common.AsyncIctQueryService;
import com.huawei.it.fcst.industry.pbi.impl.mix.MixExportService;
import com.huawei.it.fcst.industry.pbi.service.common.IIctCommonService;
import com.huawei.it.fcst.industry.pbi.vo.comb.CombTransformVO;
import com.huawei.it.fcst.industry.pbi.vo.comb.CommonViewVO;
import com.huawei.it.fcst.industry.pbi.vo.common.DmFcstDataRefreshStatus;
import com.huawei.it.fcst.industry.pbi.vo.common.DmFocVarifyTaskVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstDimInfoVO;
import com.huawei.it.fcst.industry.pbi.vo.mix.MixSearchVO;
import com.huawei.it.fcst.util.ObjectCopyUtil;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.annotation.NoJalorTransation;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.registry.service.IRegistryQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Inject;
import javax.inject.Named;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * AsyncMonthAnalysisService Class
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@Slf4j
@EnableAsync
@Named(value = "asyncMonthAnalysisService")
@JalorResource(code = "asyncMonthAnalysisService", desc = "ICT异步处理服务")
public class AsyncMonthAnalysisService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncMonthAnalysisService.class);

    @Inject
    private IDmFocVarifyTaskDao varifyTaskDao;

    @Inject
    private IctMonthCostIdxDao monthCostIdxDao;

    @Inject
    private IDmCustomDao dmCustomDao;

    @Inject
    private IDmFcstDimInfoDao dmFcstDimInfoDao;

    @Inject
    private IIctCommonService commonService;

    @Inject
    private IDmFcstDataRefreshStatusDao dataRefreshStatusDao;

    @Autowired
    private AsyncIctQueryService asyncIctQueryService;

    @Autowired
    private IDmFcstDataRefreshStatusDao dmFcstDataRefreshStatusDao;

    @Inject
    private MixExportService mixExportService;

    @Inject
    private IRegistryQueryService registryQueryService;

    private static final String TASK_FAIL = "TASK_FAIL";
    private static final String TASK_SUCCESS = "TASK_SUCCESS";

    /**
     * 调用切换基期函数刷新数据
     *
     * @param jsonStr      json string
     * @param dmFocVarifyTaskVO
     */
    @Async("asyncServiceExecutor")
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void refreshDataByFunction(String jsonStr, DmFocVarifyTaskVO dmFocVarifyTaskVO) {
        log.info(">>>Begin AsyncMonthAnalysisService#refreshDataByFunction and params: {}", jsonStr);
        // 调用切换基期函数刷新数据
        DmFocVarifyTaskVO taskVO = ObjectCopyUtil.copy(dmFocVarifyTaskVO, DmFocVarifyTaskVO.class);
        String successFlag = monthCostIdxDao.callFuncRefreshData(jsonStr);
        log.info(">>>AsyncMonthAnalysisService#refreshDataByFunction and result:{}", successFlag);
        if ("PROCESSING".equals(taskVO.getStatus())) {
            taskVO.setCombStatus(null);
            if ("SUCCESS".equals(successFlag)) {
                taskVO.setStatus("SUCCESS");
            } else {
                taskVO.setStatus("FAILED");
            }
        }
        taskVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        varifyTaskDao.updateVerifyTask(taskVO);
    }

    @Async("asyncServiceExecutor")
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void refreshCombDataByFunction(String jsonStr, DmFocVarifyTaskVO dmFocVarifyTaskVO) {
        log.info(">>>Begin AsyncMonthAnalysisService#refreshCombDataByFunction and params: {}", jsonStr);
        // 调用切换基期函数刷新数据
        DmFocVarifyTaskVO taskVO = ObjectCopyUtil.copy(dmFocVarifyTaskVO, DmFocVarifyTaskVO.class);
        String successFlag = monthCostIdxDao.callFuncRefreshData(jsonStr);
        log.info(">>>AsyncMonthAnalysisService#refreshCombDataByFunction and result:{}", successFlag);
        if ("PROCESSING".equals(taskVO.getCombStatus())) {
            taskVO.setStatus(null);
            if ("SUCCESS".equals(successFlag)) {
                taskVO.setCombStatus("SUCCESS");
            } else {
                taskVO.setCombStatus("FAILED");
            }
        }
        taskVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        varifyTaskDao.updateVerifyTask(taskVO);
    }

    @Async("asyncServiceExecutor")
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void getCurrentIndexDataByFunction(MixSearchVO mixSearchVO, DmFocVarifyTaskVO varifyTaskVO) throws InterruptedException, ApplicationException {
        log.info(">>>Begin AsyncMonthAnalysisService#getCurrentIndexDataByFunction");
        DmFocVarifyTaskVO taskVO = new DmFocVarifyTaskVO();
        taskVO.setTaskType(varifyTaskVO.getTaskType());
        taskVO.setStatus(varifyTaskVO.getStatus());
        taskVO.setId(varifyTaskVO.getTaskId());
        String minutes = registryQueryService.findValueByPath("App.Config.Time.mixCurrentIndexCalculate", true);
        taskVO.setMinutes(minutes);
        // 超过一定时间没有执行成功的历史任务，自动失败
        varifyTaskDao.updateMixVerifyTask(taskVO);
        // 只有没人在跑月度函数时，才能往下走
        while (true) {
            // 两秒请求一次查询sql
            Thread.sleep(2000);
            DmFcstDataRefreshStatus dataRefreshStatus = new DmFcstDataRefreshStatus();
            List<DmFcstDataRefreshStatus> dataRefreshStatusList = dmFcstDataRefreshStatusDao.findMonthDataRefreshStatusList(dataRefreshStatus);
            List<DmFocVarifyTaskVO> dmFocVarifyTaskVOList = varifyTaskDao.searchVerifyTask(taskVO);
            if (CollectionUtils.isEmpty(dmFocVarifyTaskVOList) && CollectionUtils.isEmpty(dataRefreshStatusList)) {
                break;
            }
        }
        log.info(">>>勾稽管理-开始插入数据调函数");
        // 调用函数计算单指数图
        List<String> statusList = new ArrayList<>();
        List<String> costTypeList = mixSearchVO.getCostTypeList();
        // 设置参数
        CombTransformVO combVO = getFunctionParam(mixSearchVO, varifyTaskVO);
        for (String costType : costTypeList) {
            // 插入筛选条件到汇总组合维表中
            // ps：如果只多选lv2，也需要查询到对应的lv3.5和所有spart
            mixSearchVO.setCostType(costType);
            List<DmFcstDimInfoVO> prodTeamCodeSpartList = getProdTeamCodeSpart(mixSearchVO);
            // 组装数据
            combineInterLockComb(prodTeamCodeSpartList, mixSearchVO);
            // 插入数据
            insertCombRecursion(prodTeamCodeSpartList, costType, 0L, 500L);
            combVO.setCostType(costType);
            log.info(">>>调用勾稽管理函数,成本类型：{},目录树:{},id:{},版本：{}", costType, combVO.getGranularityType(),combVO.getCustomId(), combVO.getMonthVersionId());
            List<String> interlockCombFlagList = asyncIctQueryService.findInterlockCombFunction(combVO);
            statusList.addAll(interlockCombFlagList);
            log.info(">>>调用勾稽管理-单指数图函数, result:{}", JSON.toJSON(interlockCombFlagList));
        }
        if ("PROCESSING".equals(varifyTaskVO.getStatus())) {
            if (statusList.contains("FAILED") || statusList.contains("0")) {
                varifyTaskVO.setStatus("FAILED");
            } else {
                varifyTaskVO.setStatus("SUCCESS");
            }
        }
        varifyTaskVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        varifyTaskDao.updateVerifyTask(varifyTaskVO);
    }

    public void getLv4ProdTeamCode(MixSearchVO mixSearchVO) {
        log.info(">>>Begin asyncIctQueryService::getLv4ProdTeamCode");
        mixSearchVO.setCostType(IndustryConstEnum.COST_TYPE.PSP.getValue());
        Future<List<DmFcstDimInfoVO>> pspProdTeamCodeList = getProdTeamCodeByType(mixSearchVO);
        mixSearchVO.setCostType(IndustryConstEnum.COST_TYPE.STD.getValue());
        Future<List<DmFcstDimInfoVO>> stdProdTeamCodeList = getProdTeamCodeByType(mixSearchVO);
        while (true) {
            if (pspProdTeamCodeList.isDone()&& stdProdTeamCodeList.isDone()) {
                break;
            }
        }
        try {
            List<DmFcstDimInfoVO> pspDimInfoVOList = pspProdTeamCodeList.get();
            List<String> pspProdList = pspDimInfoVOList.stream().map(DmFcstDimInfoVO::getLv4Code).distinct().collect(Collectors.toList());
            mixSearchVO.setPspProdRndTeamCodeList(pspProdList);
            List<DmFcstDimInfoVO> stdDimInfoVOList = stdProdTeamCodeList.get();
            List<String> stdProdList = stdDimInfoVOList.stream().map(DmFcstDimInfoVO::getLv4Code).distinct().collect(Collectors.toList());
            mixSearchVO.setStdProdRndTeamCodeList(stdProdList);
        } catch (Exception ex) {
            log.error(">>>>>An exception occurs with asyncIctQueryService::getLv4ProdTeamCode");
            log.error(ex.getMessage());
        }

    }

    @Async("ictAsyncServiceExecutor")
    public Future<List<DmFcstDimInfoVO>> getProdTeamCodeByType(MixSearchVO mixSearchVO) {
        List<DmFcstDimInfoVO> prodTeamCodeList = getProdTeamCodeSpart(mixSearchVO);
        return new AsyncResult<>(prodTeamCodeList);
    }

    private List<DmFcstDimInfoVO> getProdTeamCodeSpart(MixSearchVO mixSearchVO) {
        CommonViewVO commonViewVO = new CommonViewVO();
        BeanUtil.copyProperties(mixSearchVO, commonViewVO);
        // 获取最大层级
        commonViewVO.setPageFlag("MONTH");
        commonViewVO.setMaxLevel(mixSearchVO.getGroupLevel());
        commonViewVO.setLv1CodeList(mixSearchVO.getLv1ProdRndTeamCodeList());
        commonViewVO.setLv2CodeList(mixSearchVO.getLv2ProdRndTeamCodeList());
        commonViewVO.setLv3CodeList(mixSearchVO.getLv3ProdRndTeamCodeList());
        commonViewVO.setLv4CodeList(mixSearchVO.getLv4ProdRndTeamCodeList());
        commonViewVO.setMonVersionId(mixSearchVO.getVersionId());
        commonViewVO.setVersionId(commonService.getVersionId("ANNUAL"));
        List<DmFcstDimInfoVO> dimInfoList = new ArrayList<>();
        String granularityType = commonViewVO.getGranularityType();
        if ("Y".equals(commonViewVO.getMainFlag())) {
            switch (granularityType) {
                case "IRB":
                    dimInfoList = dmFcstDimInfoDao.getMainIrbSpartList(commonViewVO);
                    break;
                case "INDUS":
                    dimInfoList = dmFcstDimInfoDao.getMainIndusSpartList(commonViewVO);
                    break;
                case "PROD":
                    dimInfoList = dmFcstDimInfoDao.getMainProdSpartList(commonViewVO);
                    break;
                default:
                    break;
            }
        } else {
            switch (granularityType) {
                case "IRB":
                    dimInfoList = dmFcstDimInfoDao.getIrbSpartList(commonViewVO);
                    break;
                case "INDUS":
                    dimInfoList = dmFcstDimInfoDao.getIndusSpartList(commonViewVO);
                    break;
                case "PROD":
                    dimInfoList = dmFcstDimInfoDao.getProdSpartList(commonViewVO);
                    break;
                default:
                    break;
            }
        }
        return dimInfoList;
    }

    private void combineInterLockComb(List<DmFcstDimInfoVO> prodTeamCodeSpartList, MixSearchVO mixSearchVO) {
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        String customName;
        if (mixSearchVO.isRatioRateFlag()) {
            customName = "多选SPART";
        } else {
            String groupLevel = CommonConstant.groupLevelMap.get(mixSearchVO.getGroupLevel());
            customName = "多选"+ groupLevel;
        }
        if (IndustryConstEnum.COST_TYPE.STD.getValue().equals(mixSearchVO.getCostType())) {
            mixSearchVO.setSoftwareMark(IndustryConstEnum.SOFTWARE_MARK.HARDWARE.getValue());
        }
        String userAccount = mixSearchVO.getUserAccount();
        String userId = mixSearchVO.getUserId();
        String roleId = mixSearchVO.getRoleId();
        String codeAttributes;
        if ("Y".equals(mixSearchVO.getMainFlag()) && StringUtils.isBlank(mixSearchVO.getCodeAttributes())) {
            codeAttributes = "全选";
        } else {
            codeAttributes = mixSearchVO.getCodeAttributes();
        }
        prodTeamCodeSpartList.forEach(vo->{
            vo.setCustomId(mixSearchVO.getCustomId());
            vo.setCustomCnName(customName);
            vo.setCreatedBy(userId);
            vo.setCreationDate(timestamp);
            vo.setLastUpdatedBy(userId);
            vo.setLastUpdateDate(timestamp);
            vo.setRoleId(roleId);
            vo.setUserId(userAccount);
            vo.setEnableFlag(CommonConstant.ENABLE_FLAG_Y);
            vo.setSubEnableFlag(CommonConstant.ENABLE_FLAG_Y);
            vo.setIsSeparate("N");
            vo.setUseFlag("CALC");
            vo.setGranularityType(mixSearchVO.getGranularityType());
            vo.setViewFlag(mixSearchVO.getViewFlag());
            vo.setOverseaFlag(mixSearchVO.getOverseaFlag());
            vo.setSoftwareMark(mixSearchVO.getSoftwareMark());
            vo.setMainFlag(mixSearchVO.getMainFlag());
            vo.setCodeAttributes(codeAttributes);
            vo.setPageFlag("INTERLOCK");
            vo.setRegionCode(mixSearchVO.getRegionCode());
            vo.setRegionCnName(mixSearchVO.getRegionCnName());
            vo.setRepofficeCode(mixSearchVO.getRepofficeCode());
            vo.setRepofficeCnName(mixSearchVO.getRepofficeCnName());
            vo.setBgCode(mixSearchVO.getBgCode());
            vo.setBgCnName(mixSearchVO.getBgCnName());
        });
    }

    private CombTransformVO getFunctionParam(MixSearchVO mixSearchVO, DmFocVarifyTaskVO varifyTaskVO) {
        CombTransformVO combTransformVO = new CombTransformVO();
        combTransformVO.setCustomId(varifyTaskVO.getTaskId());
        combTransformVO.setGranularityType(mixSearchVO.getGranularityType());
        // 获取加密key
        String plainText = ConfigUtil.getInstance().get16PlainText();
        combTransformVO.setEncryptKey(plainText);
        combTransformVO.setPageSymbol("INTERLOCK");
        // 获取版本号
        combTransformVO.setMonthVersionId(mixSearchVO.getVersionId());
        return combTransformVO;
    }

    private void insertCombRecursion(List<DmFcstDimInfoVO> customList, String costType, Long start, Long limit) {
        List<DmFcstDimInfoVO> combSubList =
                customList.stream().skip(start).limit(limit).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(combSubList)) {
            return;
        }
        dmCustomDao.createCustomList(combSubList, costType);
        insertCombRecursion(customList, costType, start + limit, limit);
    }

    @Async("ictAsyncServiceExecutor")
    @NoJalorTransation
    public void asyncExportMixData(MixSearchVO mixSearchVO, XSSFWorkbook workbook, CombTransformVO combTransformVO) {
        final long millis = System.currentTimeMillis();
        final Date now = new Date(millis);
        Long taskId = combTransformVO.getTaskId();
        Long userId = combTransformVO.getUserId();
        DmFcstDataRefreshStatus refreshStatus = build(userId, taskId, now);

        boolean result = false;
        try {
            mixExportService.exportDetailData(mixSearchVO, workbook, combTransformVO.getCurrent(), userId);
            updateStates(refreshStatus, TASK_SUCCESS);
            dataRefreshStatusDao.updateDmFcstDataRefreshStatus(refreshStatus);
            result = true;
        } catch (Exception exception) {
            LOGGER.error("export mix excel error: taskId={}, {}", taskId, exception.getMessage());
        }
        if (!result) {
            try {
                Thread.sleep(20);
            } catch (InterruptedException exception) {
                Thread.currentThread().interrupt();
            }
            try {
                retrySave(refreshStatus);
            } catch (Exception exception) {
                LOGGER.error("export mix excel error: {}", taskId, exception.getMessage());
            }
        }
    }

    private DmFcstDataRefreshStatus build(long userId, Long taskId, Date now) {
        DmFcstDataRefreshStatus refreshStatus = new DmFcstDataRefreshStatus();
        refreshStatus.setTaskId(taskId);
        refreshStatus.setLastUpdatedBy(userId);
        refreshStatus.setLastUpdateDate(now);
        return refreshStatus;
    }

    private void updateStates(DmFcstDataRefreshStatus dmFcstDataRefreshStatus, String status) {
        dmFcstDataRefreshStatus.setStatus(status);
        dmFcstDataRefreshStatus.setLastUpdateDate(new Date());
    }

    private void retrySave(DmFcstDataRefreshStatus plan) {
        updateStates(plan, TASK_FAIL);
        dataRefreshStatusDao.updateDmFcstDataRefreshStatus(plan);
    }
}