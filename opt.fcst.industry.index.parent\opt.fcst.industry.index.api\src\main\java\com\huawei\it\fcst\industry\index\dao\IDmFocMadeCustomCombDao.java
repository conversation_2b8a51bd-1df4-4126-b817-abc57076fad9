/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.combination.CombTransformVO;
import com.huawei.it.fcst.industry.index.vo.combination.CombinationVO;
import com.huawei.it.fcst.industry.index.vo.combination.DmCustomCombVO;
import com.huawei.it.fcst.industry.index.vo.common.CommonViewVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;

import java.util.List;

/**
 * IDmFocMadeCustomCombDao Class
 *
 * <AUTHOR>
 * @since 2023/11/2
 */
public interface IDmFocMadeCustomCombDao {

    List<DmFocViewInfoVO> getManufacutreCombinationList(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> prodTeamCodeManufactureForGeneralMonth(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> prodTeamCodeManufactureForGeneralAnnual(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> prodTeamCodeManufactureForProfitAnnual(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> prodTeamCodeManufactureForProfitMonth(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> prodTeamCodeManufactureForDimensionAnnual(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> prodTeamCodeManufactureForDimensionMonth(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> getManuParentCodeListGeneralAnnual(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> getManuParentCodeListGeneralMonth(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> getManuParentCodeListProfitAnnual(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> getManuParentCodeListProfitMonth(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> getManuParentCodeListDimensionAnnual(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> getManuParentCodeListDimensionMonth(CommonViewVO commonViewVO);

    List<DmCustomCombVO> groupCodeForManuGeneralAnnualList(CombinationVO combinationVO);

    List<DmCustomCombVO> groupCodeForManuGeneralMonthList(CombinationVO combinationVO);

    List<DmCustomCombVO> groupCodeForManuProfitAnnualList(CombinationVO combinationVO);

    List<DmCustomCombVO> groupCodeForManuProfitMonthList(CombinationVO combinationVO);

    List<DmCustomCombVO> groupCodeForManuDimensionAnnualList(CombinationVO combinationVO);

    List<DmCustomCombVO> groupCodeForManuDimensionMonthList(CombinationVO combinationVO);

    List<DmFocViewInfoVO> reverseViewInfoListGeneralMonth(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> reverseViewInfoListGeneralAnnual(CommonViewVO commonViewVO);

    String cusMadeViewAnnualCost(CombTransformVO combTransformVO);

    String cusMadeItemDtlDecode(CombTransformVO combTransformVO);

    String cusMadeItemAppend(CombTransformVO combTransformVO);

    String customMadeAnnual(CombTransformVO combTransformVO);

    String customMadeMonthComb(CombTransformVO combTransformVO);
}
