/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.dao;

import com.huawei.it.fcst.industry.price.vo.config.PriceHistorySpartListVO;
import com.huawei.it.fcst.industry.price.vo.config.PriceHistorySpartSearchVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;

import java.util.List;

/**
 * IPriceTopSpartInfoDao Interface
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
public interface IPriceTopSpartInfoDao {

    /**
     * 历史SPART清单各层级下拉框查询
     *
     * @param paramVO 参数VO
     * @return PagedResult<PriceHistorySpartListVO>
     */
    List<PriceHistorySpartListVO> findDropdownList(PriceHistorySpartSearchVO paramVO);

    /**
     * 历史SPART清单分页查询
     *
     * @param paramVO 参数VO
     * @param pageVO 分页参数VO
     * @return PagedResult<PriceHistorySpartListVO>
     */
    PagedResult<PriceHistorySpartListVO> findTopSpartByPage(PriceHistorySpartSearchVO paramVO, PageVO pageVO);

}