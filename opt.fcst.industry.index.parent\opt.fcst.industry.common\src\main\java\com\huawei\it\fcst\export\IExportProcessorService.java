/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.export;

import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;

import javax.servlet.http.HttpServletResponse;

import java.io.Serializable;
import java.util.Map;

/**
 * 导出任务入口
 *
 * <AUTHOR>
 * @since 202407
 */
public interface IExportProcessorService {
    /**
     * 导出实现类方法
     *
     * @param response    当前请求响应
     * @param beanManager 模板对象
     * @param condition   查询条件
     * @param parameters  自定义参数， 固定取：{exportModuleName;exportFileName}
     * @throws ApplicationException 异常信息
     */
    void fillEasyExcelExport(HttpServletResponse response, IExcelTemplateBeanManager beanManager,
            Serializable condition, Map<String, Object> parameters) throws ApplicationException;

    /**
     * 导出实现类
     *
     * @param response    当前请求响应
     * @param beanManager 模板对象
     * @param condition   查询条件
     * @param parameters  可自定义项参数，固定取：{exportModuleName;exportFileName}
     * @param pageVO      分页对象
     * @throws ApplicationException 异常信息
     */
    void fillEasyExcelExport(HttpServletResponse response, IExcelTemplateBeanManager beanManager,
            Serializable condition, Map<String, Object> parameters, PageVO pageVO) throws ApplicationException;

    /**
     * 导出实现类
     *
     * @param beanManager 模板对象
     * @param condition   查询条件
     * @param parameters  可自定义项参数，固定取：{exportModuleName;exportFileName}
     * @param pageVO      分页对象
     * @throws ApplicationException 异常信息
     */
    void asyncFillEasyExcelExport(IExcelTemplateBeanManager beanManager, Serializable condition,
            Map<String, Object> parameters, PageVO pageVO) throws ApplicationException;

}
