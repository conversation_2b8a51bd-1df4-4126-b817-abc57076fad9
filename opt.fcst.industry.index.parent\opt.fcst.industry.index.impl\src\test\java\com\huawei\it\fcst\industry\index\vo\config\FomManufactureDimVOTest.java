/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.config;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

/**
 * FomManufactureDimVOTest Class
 *
 * <AUTHOR>
 * @since 2023/11/3
 */
public class FomManufactureDimVOTest extends BaseVOCoverUtilsTest<FomManufactureDimVO> {

    @Override
    protected Class<FomManufactureDimVO> getTClass() {
        return FomManufactureDimVO.class;
    }

    @Test
    public void testMethod() {
        FomManufactureDimVO dimensionParamVO = new FomManufactureDimVO();
        dimensionParamVO.setPageIndex(1);
        dimensionParamVO.getPageIndex();
        dimensionParamVO.setItemCode("11");
        dimensionParamVO.getItemCode();
        dimensionParamVO.getCnDesc();
        dimensionParamVO.setCnDesc("cc");
        dimensionParamVO.setLv0OrgCn("lo");
        dimensionParamVO.getLv0OrgCn();
        dimensionParamVO.setBussinessObject("buss");
        dimensionParamVO.getBussinessObject();
        dimensionParamVO.setShippingObject("ship");
        dimensionParamVO.getShippingObject();
        dimensionParamVO.setManufactureBu("bu");
        dimensionParamVO.getManufactureBu();
        dimensionParamVO.setManufactureObject("man");
        dimensionParamVO.getManufactureObject();
        dimensionParamVO.setDataType("da");
        dimensionParamVO.getDataType();
        dimensionParamVO.setGranularityType("U");
        dimensionParamVO.getGranularityType();
        dimensionParamVO.setTopManufactureObjectCnName("11");
        dimensionParamVO.getTopManufactureObjectCnName();
        dimensionParamVO.setTopManufactureObjectCode("11");
        dimensionParamVO.getTopShippingObjectCode();
        dimensionParamVO.setTopShippingObjectCode("top");
        dimensionParamVO.getTopShippingObjectCnName();
        dimensionParamVO.setTopShippingObjectCnName("22");
        dimensionParamVO.setTotalSize(1);
        dimensionParamVO.getTotalSize();
        dimensionParamVO.setTopManufactureObjectCode("1313");
        dimensionParamVO.getTopManufactureObjectCnName();
        Assert.assertNotNull(dimensionParamVO);
    }
}