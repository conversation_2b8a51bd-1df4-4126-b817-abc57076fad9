/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.common;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

import java.sql.Timestamp;
import java.util.Date;

/**
 * DmFocViewInfoVOTest Class
 *
 * <AUTHOR>
 * @since 2023/4/22
 */
public class DmFocViewInfoVOTest extends BaseVOCoverUtilsTest<DmFocViewInfoVO> {

    @Override
    protected Class<DmFocViewInfoVO> getTClass() {
        return DmFocViewInfoVO.class;
    }

    @Test
    public void testMethod() {
        DmFocViewInfoVO dmFocActualCostVO = new DmFocViewInfoVO();
        dmFocActualCostVO.getCreationDate();
        dmFocActualCostVO.getId();
        dmFocActualCostVO.setId(10L);
        dmFocActualCostVO.getCategoryCode();
        dmFocActualCostVO.setCategoryCode("cate");
        dmFocActualCostVO.setDelFlag("Y");
        dmFocActualCostVO.getDelFlag();
        dmFocActualCostVO.setGroupCode("code");
        dmFocActualCostVO.getGroupCode();
        dmFocActualCostVO.setGroupCnName("gName");
        dmFocActualCostVO.getGroupCnName();
        dmFocActualCostVO.setGroupLevel("LV3");
        dmFocActualCostVO.getGroupLevel();
        dmFocActualCostVO.setItemCode("item");
        dmFocActualCostVO.getItemCode();
        dmFocActualCostVO.setItemCnName("ItemName");
        dmFocActualCostVO.getItemCnName();
        dmFocActualCostVO.getLastUpdatedBy();
        dmFocActualCostVO.setLastUpdatedBy("1175");
        dmFocActualCostVO.getLastUpdateDdate();
        dmFocActualCostVO.setVersionId(14L);
        dmFocActualCostVO.getVersionId();
        dmFocActualCostVO.setViewFlagValue("view");
        dmFocActualCostVO.getViewFlagValue();
        dmFocActualCostVO.setViewFlag("0");
        dmFocActualCostVO.getViewFlag();
        Timestamp timestamp = new Timestamp(new Date().getTime());
        dmFocActualCostVO.setCreationDate(timestamp);
        dmFocActualCostVO.setLastUpdateDdate(timestamp);
        dmFocActualCostVO.setViewFlag("1");
        Assert.assertNotNull(dmFocActualCostVO);
    }
}