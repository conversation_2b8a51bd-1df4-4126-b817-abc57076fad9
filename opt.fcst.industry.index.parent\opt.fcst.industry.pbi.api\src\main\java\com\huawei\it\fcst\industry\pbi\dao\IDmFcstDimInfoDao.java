/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.dao;

import com.huawei.it.fcst.industry.pbi.vo.comb.CommonViewVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstDimInfoVO;

import java.util.List;

/**
 * IDmFcstDimInfoDao Class
 *
 * <AUTHOR>
 * @since 2024/9/13
 */
public interface IDmFcstDimInfoDao {

    List<DmFcstDimInfoVO> getIrbDimInfoList(CommonViewVO commonViewVO);

    List<DmFcstDimInfoVO> getIrbSpartList(CommonViewVO commonViewVO);

    List<DmFcstDimInfoVO> getIndusDimInfoList(CommonViewVO commonViewVO);

    List<DmFcstDimInfoVO> getIndusSpartList(CommonViewVO commonViewVO);

    List<DmFcstDimInfoVO> getProdDimInfoList(CommonViewVO commonViewVO);

    List<DmFcstDimInfoVO> getProdSpartList(CommonViewVO commonViewVO);

    // 主力编码list
    List<DmFcstDimInfoVO> getMainIrbDimInfoList(CommonViewVO commonViewVO);

    List<DmFcstDimInfoVO> getMainIrbSpartList(CommonViewVO commonViewVO);

    List<DmFcstDimInfoVO> getMainIndusDimInfoList(CommonViewVO commonViewVO);

    List<DmFcstDimInfoVO> getMainIndusSpartList(CommonViewVO commonViewVO);

    List<DmFcstDimInfoVO> getMainProdDimInfoList(CommonViewVO commonViewVO);

    List<DmFcstDimInfoVO> getMainProdSpartList(CommonViewVO commonViewVO);

    // 获取父层级的编码和名称
    List<DmFcstDimInfoVO> getParentMainIrbDimInfoList(CommonViewVO commonViewVO);

    List<DmFcstDimInfoVO> getParentMainAnnualIrbDimInfoList(CommonViewVO commonViewVO);

    List<DmFcstDimInfoVO> getParentMainIndusDimInfoList(CommonViewVO commonViewVO);

    List<DmFcstDimInfoVO> getParentMainAnnualIndusDimInfoList(CommonViewVO commonViewVO);

    List<DmFcstDimInfoVO> getParentMainProdDimInfoList(CommonViewVO commonViewVO);

    List<DmFcstDimInfoVO> getParentMainAnnualProdDimInfoList(CommonViewVO commonViewVO);

    List<DmFcstDimInfoVO> getParentIrbDimInfoList(CommonViewVO commonViewVO);

    List<DmFcstDimInfoVO> getParentIndusDimInfoList(CommonViewVO commonViewVO);

    List<DmFcstDimInfoVO> getParentProdDimInfoList(CommonViewVO commonViewVO);

    List<DmFcstDimInfoVO> getMainAnnualIrbDimInfoList(CommonViewVO commonViewVO);

    List<DmFcstDimInfoVO> getMainAnnualIndusDimInfoList(CommonViewVO commonViewVO);

    List<DmFcstDimInfoVO> getMainAnnualProdDimInfoList(CommonViewVO commonViewVO);

    List<DmFcstDimInfoVO> getMainAnnualIrbSpartList(CommonViewVO commonViewVO);

    List<DmFcstDimInfoVO> getMainAnnualIndusSpartList(CommonViewVO commonViewVO);

    List<DmFcstDimInfoVO> getMainAnnualProdSpartList(CommonViewVO commonViewVO);
}
