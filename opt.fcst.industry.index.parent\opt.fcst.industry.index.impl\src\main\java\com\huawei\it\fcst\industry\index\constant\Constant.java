/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2018-2019. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.constant;

/**
 * 常量类.
 *
 * <AUTHOR>
 * @since 2023/03/10
 */
public class Constant {
    /**
     * 字符串常量
     */
    public enum StrEnum {
        DIMENSION_CODE("getDimensionWithTree"),
        PROD_DIMENSION_CODE("getProdDimensionWithTree"),
        PRICE_DIMENSION_CODE("getPriceDimensionWithTree"),
        ICT_ORG("ICT"),
        ENERGY_ORG("ENERGY"),
        IAS_ORG("IAS"),
        ZERO_ONE("01"),
        SIX("06"),
        TWELVE("12"),
        ICT("104364"),
        ENERGY("153462"),
        IAS("104210"),
        UNDERLINE("#*#"),
        XLSX(".xlsx"),
        OTHER_WEIGHT("其他"),
        BASE_PERIOD("基期："),
        VARCHAR("VARCHAR"),
        NUMBER("Number"),
        QUERY_EXPLAIN_CHART_URL("App.Explain.queryShapChartUrl"),
        /**
         * The ERROR.
         */
        SUCCESS("SUCCESS"),
        DATA_UNMODIFIED("采购映射维表未修改，维表最新版本号数据与上次同步时的数据一致"),
        DATA_UNMODIFIED_MADE("制造量纲维表未修改，维表最新版本号数据与上次同步时的数据一致"),
        DATA_SYNC_FAIL("数据同步失败"),
        CURRENT_PERIOD("本期实际数截止月："),
        REFRASH_PERIOD("当前数据刷新月："),
        DATETIME_SS("yyyyMMddHHmmss"),
        ALL_LOOKUP_UNIVERSAL_VIEW("ALL_UNIVERSAL_VIEW"),
        ALL_LOOKUP_PROFITS_VIEW("ALL_PROFITS_VIEW"),
        ALL_LOOKUP_DIMENSION_VIEW("ALL_DIMENSION_VIEW"),
        MANUFACTURE_LOOKUP_UNIVERSAL_VIEW("MANUFACTURE_UNIVERSAL_VIEW"),
        MANUFACTURE_LOOKUP_PROFITS_VIEW("MANUFACTURE_PROFITS_VIEW"),
        MANUFACTURE_LOOKUP_DIMENSION_VIEW("MANUFACTURE_DIMENSION_VIEW"),
        PURCHASE_LOOKUP_UNIVERSAL_VIEW("UNIVERSAL_VIEW"),
        PURCHASE_LOOKUP_PROFITS_VIEW("PROFITS_VIEW"),
        PURCHASE_LOOKUP_DIMENSION_VIEW("DIMENSION_VIEW"),
        LOOK_COST_TYPE("COST_TYPE"),
        EXCEL_TEXT("@"),
        NO_PERMISSION_TO_DOWNLOAD("No permission to download."),
        INDUSTRY_COST_INDEX_TEMPLATE1_PATH("excel.export.template/IndustryCostIdxExpTemplate1.xlsx"),
        INDUSTRY_COST_INDEX_TEMPLATE2_PATH("excel.export.template/IndustryCostIdxExpTemplate2.xlsx"),
        INDUSTRY_COST_INDEX_TEMPLATE3_PATH("excel.export.template/IndustryCostIdxExpTemplate3.xlsx"),
        INDUSTRY_COST_INDEX_TEMPLATE4_PATH("excel.export.template/IndustryCostIdxExpTemplate4.xlsx"),
        INDUSTRY_COST_INDEX_TEMPLATE5_PATH("excel.export.template/IndustryCostIdxExpTemplate5.xlsx"),
        INDUSTRY_COST_INDEX_TEMPLATE6_PATH("excel.export.template/IndustryCostIdxExpTemplate6.xlsx"),
        INDUSTRY_COST_INDEX_TEMPLATE7_PATH("excel.export.template/IndustryCostIdxExpTemplate7.xlsx"),
        REPLACE_AMP_TEMPLATE1_PATH("excel.export.template/ReplaceAmpExpTemplate1.xlsx"),
        REPLACE_AMP_TEMPLATE2_PATH("excel.export.template/ReplaceAmpExpTemplate2.xlsx"),
        REPLACE_AMP_TEMPLATE3_PATH("excel.export.template/ReplaceAmpExpTemplate3.xlsx"),
        REPLACE_AMP_TEMPLATE4_PATH("excel.export.template/ReplaceAmpExpTemplate4.xlsx"),
        REPLACE_AMP_TEMPLATE5_PATH("excel.export.template/ReplaceAmpExpTemplate5.xlsx"),
        REPLACE_AMP_TEMPLATE6_PATH("excel.export.template/ReplaceAmpExpTemplate6.xlsx"),
        STAND_AMP_TEMPLATE1_PATH("excel.export.template/StandAmpExpTemplate1.xlsx"),
        STAND_AMP_TEMPLATE2_PATH("excel.export.template/StandAmpExpTemplate2.xlsx"),
        STAND_AMP_TEMPLATE3_PATH("excel.export.template/StandAmpExpTemplate3.xlsx"),
        STAND_AMP_TEMPLATE4_PATH("excel.export.template/StandAmpExpTemplate4.xlsx"),
        STAND_AMP_TEMPLATE5_PATH("excel.export.template/StandAmpExpTemplate5.xlsx"),
        STAND_AMP_TEMPLATE6_PATH("excel.export.template/StandAmpExpTemplate6.xlsx"),
        PRICE_INDEX_EXP_FILE_NAME("月度分析-明细数据"),
        COST_TYPE("成本类型："),
        COMPARE_FLAG("产业-多选主体"),
        CALIBER_FLAG("数据口径："),
        GRANULE("颗粒度："),
        INDUSTRY_VIEW("视角："),
        OVERSEA_FLAG("国内/海外："),
        BG_FLAG("BG："),
        HEAP_TITLE2("%s名称"),
        HEAP_TITLE3("%s编码"),
        REPLACE_AMP_TITLE("替代成本涨跌图 %s"),
        REPLACE_MULTI_AMP_TITLE("替代成本涨跌一览表 %s"),
        INDUSTRY_REPALCE_RMB_COST_PER_CHART("参与替代编码金额占比 %s"),
        INDUSTRY_INDEX_AMP_CHART("成本涨跌图 %s"),
        INDUSTRY_INDEX_AMP_CHILDREN_CHART("成本涨跌图多子项 %s"),
        INDUSTRY_INDEX_AMP_VIEW("成本涨跌幅一览表 %s"),
        MONTH_ACC_AMP_CHART("月度累计成本涨跌图 %s"),
        PURCHASE_INDEX_TITLE("产业成本指数图 %s"),
        MANUFACTURE_INDEX_TITLE("制造成本指数图 %s"),
        TOTAL_INDEX_TITLE("成本指数图 %s"),
        PURCHASE_INDEX_YEAR_TITLE("产业成本指数图—同比环比 %s"),
        MANUFACTURE_INDEX_YEAR_TITLE("制造成本指数图—同比环比 %s"),
        TOTAL_INDEX_YEAR_TITLE("成本指数图—同比环比 %s"),
        WEIGHT_TITLE("权重图 %s"),
        DISTRIBUTE_TITLE("成本分布图 %s"),
        HEAP_TITLE("热力图 %s"),
        STR_Y("Y"),
        STR_N("N"),
        BIND("BIND"),
        LV4("LV3.5");

        private String value;

        StrEnum(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    public enum IntegerEnum {
        ONE(1),
        TWO(2),
        THREE(3),
        PAGE_SIZE(10000);
        private int value;

        IntegerEnum(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }

    public enum PoolNumEnum {
        QUEUE_CAPACITY(50);
        private int value;

        PoolNumEnum(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }
    }
}