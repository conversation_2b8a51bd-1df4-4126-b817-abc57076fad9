/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.utils;

import org.apache.poi.ss.SpreadsheetVersion;
import org.apache.poi.ss.formula.EvaluationWorkbook;
import org.apache.poi.ss.formula.functions.T;
import org.apache.poi.ss.formula.udf.UDFFinder;
import org.apache.poi.ss.usermodel.AutoFilter;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellRange;
import org.apache.poi.ss.usermodel.CellReferenceType;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Comment;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.Drawing;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Footer;
import org.apache.poi.ss.usermodel.Header;
import org.apache.poi.ss.usermodel.Hyperlink;
import org.apache.poi.ss.usermodel.Name;
import org.apache.poi.ss.usermodel.PictureData;
import org.apache.poi.ss.usermodel.PrintSetup;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.SheetConditionalFormatting;
import org.apache.poi.ss.usermodel.SheetVisibility;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellAddress;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.PaneInformation;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

/**
 * ExcelExportUtilTest Class
 *
 * <AUTHOR>
 * @since 2023/4/28
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class ExcelExportUtilTest {
    @InjectMocks
    private ExcelExportUtil excelExportUtil;

    @Mock
    private Workbook workbook=new Workbook() {
        @Override
        public int getActiveSheetIndex() {
         return 10;
        }

        @Override
        public void setActiveSheet(int i) {

        }

        @Override
        public int getFirstVisibleTab() {
            return 0;
        }

        @Override
        public void setFirstVisibleTab(int i) {

        }

        @Override
        public void setSheetOrder(String s, int i) {

        }

        @Override
        public void setSelectedTab(int i) {

        }

        @Override
        public void setSheetName(int i, String s) {

        }

        @Override
        public String getSheetName(int i) {
            return null;
        }

        @Override
        public int getSheetIndex(String s) {
            return 0;
        }

        @Override
        public int getSheetIndex(Sheet sheet) {
            return 0;
        }

        @Override
        public Sheet createSheet() {
            return null;
        }

        @Override
        public Sheet createSheet(String s) {
            return null;
        }

        @Override
        public Sheet cloneSheet(int i) {
            return null;
        }

        @Override
        public Iterator<Sheet> sheetIterator() {
            return null;
        }

        @Override
        public int getNumberOfSheets() {
            return 0;
        }

        @Override
        public Sheet getSheetAt(int i) {
            return sheet;
        }

        @Override
        public Sheet getSheet(String s) {
            return null;
        }

        @Override
        public void removeSheetAt(int i) {

        }

        @Override
        public Font createFont() {
            return null;
        }

        @Override
        public Font findFont(boolean b, short i, short i1, String s, boolean b1, boolean b2, short i2, byte b3) {
            return null;
        }

        @Override
        public int getNumberOfFonts() {
            return 0;
        }

        @Override
        public int getNumberOfFontsAsInt() {
            return 0;
        }

        @Override
        public Font getFontAt(int i) {
            return null;
        }

        @Override
        public CellStyle createCellStyle() {
            return null;
        }

        @Override
        public int getNumCellStyles() {
            return 0;
        }

        @Override
        public CellStyle getCellStyleAt(int i) {
            return null;
        }

        @Override
        public void write(OutputStream outputStream) throws IOException {

        }

        @Override
        public void close() throws IOException {

        }

        @Override
        public int getNumberOfNames() {
            return 0;
        }

        @Override
        public Name getName(String s) {
            return null;
        }

        @Override
        public List<? extends Name> getNames(String s) {
            return null;
        }

        @Override
        public List<? extends Name> getAllNames() {
            return null;
        }

        @Override
        public Name createName() {
            return null;
        }

        @Override
        public void removeName(Name name) {

        }

        @Override
        public int linkExternalWorkbook(String s, Workbook workbook) {
            return 0;
        }

        @Override
        public void setPrintArea(int i, String s) {

        }

        @Override
        public void setPrintArea(int i, int i1, int i2, int i3, int i4) {

        }

        @Override
        public String getPrintArea(int i) {
            return null;
        }

        @Override
        public void removePrintArea(int i) {

        }

        @Override
        public Row.MissingCellPolicy getMissingCellPolicy() {
            return null;
        }

        @Override
        public void setMissingCellPolicy(Row.MissingCellPolicy missingCellPolicy) {

        }

        @Override
        public DataFormat createDataFormat() {
            return null;
        }

        @Override
        public int addPicture(byte[] bytes, int i) {
            return 0;
        }

        @Override
        public List<? extends PictureData> getAllPictures() {
            return null;
        }

        @Override
        public CreationHelper getCreationHelper() {
            return null;
        }

        @Override
        public boolean isHidden() {
            return false;
        }

        @Override
        public void setHidden(boolean b) {

        }

        @Override
        public boolean isSheetHidden(int i) {
            return false;
        }

        @Override
        public boolean isSheetVeryHidden(int i) {
            return false;
        }

        @Override
        public void setSheetHidden(int i, boolean b) {

        }

        @Override
        public SheetVisibility getSheetVisibility(int i) {
            return null;
        }

        @Override
        public void setSheetVisibility(int i, SheetVisibility sheetVisibility) {

        }

        @Override
        public void addToolPack(UDFFinder udfFinder) {

        }

        @Override
        public void setForceFormulaRecalculation(boolean b) {

        }

        @Override
        public boolean getForceFormulaRecalculation() {
            return false;
        }

        @Override
        public SpreadsheetVersion getSpreadsheetVersion() {
            return null;
        }

        @Override
        public int addOlePackage(byte[] bytes, String s, String s1, String s2) throws IOException {
            return 0;
        }

        @Override
        public EvaluationWorkbook createEvaluationWorkbook() {
            return null;
        }

        @Override
        public CellReferenceType getCellReferenceType() {
            return null;
        }

        @Override
        public void setCellReferenceType(CellReferenceType cellReferenceType) {

        }

        @NotNull
        @Override
        public Iterator<Sheet> iterator() {
            return null;
        }
    };

    @Mock
    private Row row;

    @Mock
    private Sheet sheet=new Sheet() {
        @Override
        public Row createRow(int i) {

            return null;
        }

        @Override
        public void removeRow(Row row) {

        }

        @Override
        public Row getRow(int i) {
            return row;
        }

        @Override
        public int getPhysicalNumberOfRows() {
            return 0;
        }

        @Override
        public int getFirstRowNum() {
            return 0;
        }

        @Override
        public int getLastRowNum() {
            return 0;
        }

        @Override
        public void setColumnHidden(int i, boolean b) {

        }

        @Override
        public boolean isColumnHidden(int i) {
            return false;
        }

        @Override
        public void setRightToLeft(boolean b) {

        }

        @Override
        public boolean isRightToLeft() {
            return false;
        }

        @Override
        public void setColumnWidth(int i, int i1) {

        }

        @Override
        public int getColumnWidth(int i) {
            return 0;
        }

        @Override
        public float getColumnWidthInPixels(int i) {
            return 0;
        }

        @Override
        public void setDefaultColumnWidth(int i) {

        }

        @Override
        public int getDefaultColumnWidth() {
            return 0;
        }

        @Override
        public short getDefaultRowHeight() {
            return 0;
        }

        @Override
        public float getDefaultRowHeightInPoints() {
            return 0;
        }

        @Override
        public void setDefaultRowHeight(short i) {

        }

        @Override
        public void setDefaultRowHeightInPoints(float v) {

        }

        @Override
        public CellStyle getColumnStyle(int i) {
            return null;
        }

        @Override
        public int addMergedRegion(CellRangeAddress cellRangeAddress) {
            return 0;
        }

        @Override
        public int addMergedRegionUnsafe(CellRangeAddress cellRangeAddress) {
            return 0;
        }

        @Override
        public void validateMergedRegions() {

        }

        @Override
        public void setVerticallyCenter(boolean b) {

        }

        @Override
        public void setHorizontallyCenter(boolean b) {

        }

        @Override
        public boolean getHorizontallyCenter() {
            return false;
        }

        @Override
        public boolean getVerticallyCenter() {
            return false;
        }

        @Override
        public void removeMergedRegion(int i) {

        }

        @Override
        public void removeMergedRegions(Collection<Integer> collection) {

        }

        @Override
        public int getNumMergedRegions() {
            return 0;
        }

        @Override
        public CellRangeAddress getMergedRegion(int i) {
            return null;
        }

        @Override
        public List<CellRangeAddress> getMergedRegions() {
            return null;
        }

        @Override
        public Iterator<Row> rowIterator() {
            return null;
        }

        @Override
        public void setForceFormulaRecalculation(boolean b) {

        }

        @Override
        public boolean getForceFormulaRecalculation() {
            return false;
        }

        @Override
        public void setAutobreaks(boolean b) {

        }

        @Override
        public void setDisplayGuts(boolean b) {

        }

        @Override
        public void setDisplayZeros(boolean b) {

        }

        @Override
        public boolean isDisplayZeros() {
            return false;
        }

        @Override
        public void setFitToPage(boolean b) {

        }

        @Override
        public void setRowSumsBelow(boolean b) {

        }

        @Override
        public void setRowSumsRight(boolean b) {

        }

        @Override
        public boolean getAutobreaks() {
            return false;
        }

        @Override
        public boolean getDisplayGuts() {
            return false;
        }

        @Override
        public boolean getFitToPage() {
            return false;
        }

        @Override
        public boolean getRowSumsBelow() {
            return false;
        }

        @Override
        public boolean getRowSumsRight() {
            return false;
        }

        @Override
        public boolean isPrintGridlines() {
            return false;
        }

        @Override
        public void setPrintGridlines(boolean b) {

        }

        @Override
        public boolean isPrintRowAndColumnHeadings() {
            return false;
        }

        @Override
        public void setPrintRowAndColumnHeadings(boolean b) {

        }

        @Override
        public PrintSetup getPrintSetup() {
            return null;
        }

        @Override
        public Header getHeader() {
            return null;
        }

        @Override
        public Footer getFooter() {
            return null;
        }

        @Override
        public void setSelected(boolean b) {

        }

        @Override
        public double getMargin(short i) {
            return 0;
        }

        @Override
        public void setMargin(short i, double v) {

        }

        @Override
        public boolean getProtect() {
            return false;
        }

        @Override
        public void protectSheet(String s) {

        }

        @Override
        public boolean getScenarioProtect() {
            return false;
        }

        @Override
        public void setZoom(int i) {

        }

        @Override
        public short getTopRow() {
            return 0;
        }

        @Override
        public short getLeftCol() {
            return 0;
        }

        @Override
        public void showInPane(int i, int i1) {

        }

        @Override
        public void shiftRows(int i, int i1, int i2) {

        }

        @Override
        public void shiftRows(int i, int i1, int i2, boolean b, boolean b1) {

        }

        @Override
        public void shiftColumns(int i, int i1, int i2) {

        }

        @Override
        public void createFreezePane(int i, int i1, int i2, int i3) {

        }

        @Override
        public void createFreezePane(int i, int i1) {

        }

        @Override
        public void createSplitPane(int i, int i1, int i2, int i3, int i4) {

        }

        @Override
        public PaneInformation getPaneInformation() {
            return null;
        }

        @Override
        public void setDisplayGridlines(boolean b) {

        }

        @Override
        public boolean isDisplayGridlines() {
            return false;
        }

        @Override
        public void setDisplayFormulas(boolean b) {

        }

        @Override
        public boolean isDisplayFormulas() {
            return false;
        }

        @Override
        public void setDisplayRowColHeadings(boolean b) {

        }

        @Override
        public boolean isDisplayRowColHeadings() {
            return false;
        }

        @Override
        public void setRowBreak(int i) {

        }

        @Override
        public boolean isRowBroken(int i) {
            return false;
        }

        @Override
        public void removeRowBreak(int i) {

        }

        @Override
        public int[] getRowBreaks() {
            return new int[0];
        }

        @Override
        public int[] getColumnBreaks() {
            return new int[0];
        }

        @Override
        public void setColumnBreak(int i) {

        }

        @Override
        public boolean isColumnBroken(int i) {
            return false;
        }

        @Override
        public void removeColumnBreak(int i) {

        }

        @Override
        public void setColumnGroupCollapsed(int i, boolean b) {

        }

        @Override
        public void groupColumn(int i, int i1) {

        }

        @Override
        public void ungroupColumn(int i, int i1) {

        }

        @Override
        public void groupRow(int i, int i1) {

        }

        @Override
        public void ungroupRow(int i, int i1) {

        }

        @Override
        public void setRowGroupCollapsed(int i, boolean b) {

        }

        @Override
        public void setDefaultColumnStyle(int i, CellStyle cellStyle) {

        }

        @Override
        public void autoSizeColumn(int i) {

        }

        @Override
        public void autoSizeColumn(int i, boolean b) {

        }

        @Override
        public Comment getCellComment(CellAddress cellAddress) {
            return null;
        }

        @Override
        public Map<CellAddress, ? extends Comment> getCellComments() {
            return null;
        }

        @Override
        public Drawing<?> getDrawingPatriarch() {
            return null;
        }

        @Override
        public Drawing<?> createDrawingPatriarch() {
            return null;
        }

        @Override
        public Workbook getWorkbook() {
            return null;
        }

        @Override
        public String getSheetName() {
            return null;
        }

        @Override
        public boolean isSelected() {
            return false;
        }

        @Override
        public CellRange<? extends Cell> setArrayFormula(String s, CellRangeAddress cellRangeAddress) {
            return null;
        }

        @Override
        public CellRange<? extends Cell> removeArrayFormula(Cell cell) {
            return null;
        }

        @Override
        public DataValidationHelper getDataValidationHelper() {
            return null;
        }

        @Override
        public List<? extends DataValidation> getDataValidations() {
            return null;
        }

        @Override
        public void addValidationData(DataValidation dataValidation) {

        }

        @Override
        public AutoFilter setAutoFilter(CellRangeAddress cellRangeAddress) {
            return null;
        }

        @Override
        public SheetConditionalFormatting getSheetConditionalFormatting() {
            return null;
        }

        @Override
        public CellRangeAddress getRepeatingRows() {
            return null;
        }

        @Override
        public CellRangeAddress getRepeatingColumns() {
            return null;
        }

        @Override
        public void setRepeatingRows(CellRangeAddress cellRangeAddress) {

        }

        @Override
        public void setRepeatingColumns(CellRangeAddress cellRangeAddress) {

        }

        @Override
        public int getColumnOutlineLevel(int i) {
            return 0;
        }

        @Override
        public Hyperlink getHyperlink(int i, int i1) {
            return null;
        }

        @Override
        public Hyperlink getHyperlink(CellAddress cellAddress) {
            return null;
        }

        @Override
        public List<? extends Hyperlink> getHyperlinkList() {
            return null;
        }

        @Override
        public CellAddress getActiveCell() {
            return null;
        }

        @Override
        public void setActiveCell(CellAddress cellAddress) {

        }

        @NotNull
        @Override
        public Iterator<Row> iterator() {
            return null;
        }
    };

    @Mock
    private InputStream inputStream;

    @Mock
    private HttpServletResponse response;

    @Test
    public void getRowIndex() {
        int rowIndex = excelExportUtil.getRowIndex();
        Assert.assertNotNull(rowIndex);
    }

    @Test
    public void getStyleIndex() {
        int rowIndex = excelExportUtil.getStyleIndex();
        Assert.assertNotNull(rowIndex);
    }

    @Test
    public void getClazz() {
        Class clazz = excelExportUtil.getClazz();
        Assert.assertNull(clazz);
    }

    @Test
    public void getFields() {
        Field[] fields = excelExportUtil.getFields();
        Assert.assertNull(fields);
    }

    @Test
    public void singleSheetExport() throws Exception {
        String fileName="产业";
        List<T> dataList=null;

        HttpServletResponse response=null;
        Assert.assertNull(null);
    }

    @Test
    public void downloadExcel() throws Exception {
        String fileName="产业";
        excelExportUtil.downloadExcel(workbook,fileName,response);
        Assert.assertNull(null);
    }

    @Test
    public void getDateTime() {
        String dateTime = excelExportUtil.getDateTime();
        Assert.assertNotNull(dateTime);
    }

    @Test
    public void getWorkbookByTemplate() throws Exception {
        String templatePath="http://baidu.com";
        XSSFWorkbook byTemplate = null;
        try {
            byTemplate = excelExportUtil.getWorkbookByTemplate(templatePath);
        } catch (Exception e) {

        }
        Assert.assertNull(byTemplate);
    }
}