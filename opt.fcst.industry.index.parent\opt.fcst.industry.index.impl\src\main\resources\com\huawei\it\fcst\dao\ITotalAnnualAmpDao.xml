<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.ITotalAnnualAmpDao">
    <resultMap type="com.huawei.it.fcst.industry.index.vo.annual.DmFocAnnualAmpVO" id="annualResultMap">
        <result property="id" column="id"/>
        <result property="versionId" column="version_id"/>
        <result property="periodYear" column="period_year"/>
        <result property="prodRndTeamCode" column="prod_rnd_team_code"/>
        <result property="prodRndTeamCnName" column="prod_rnd_team_cn_name"/>
        <result property="parentCode" column="parent_code"/>
        <result property="parentCnName" column="parent_cn_name"/>
        <result property="groupCode" column="group_code"/>
        <result property="groupCnName" column="group_cn_name"/>
        <result property="groupLevel" column="group_level"/>
        <result property="annualAmp" column="annual_amp"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDdate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="appendFlag" column="append_flag"/>
        <result property="weightRate" column="weight_rate"/>
        <result property="statusCode" column="status_code"/>
        <result property="groupCodeAndName" column="groupCodeAndName"/>
        <result property="weightAnnualAmpPercent" column="weightAnnualAmpPercent"/>
        <result property="appendYear" column="append_year"/>
        <result property="lv0ProdRndTeamCode" column="lv0_prod_rnd_team_code"/>
        <result property="lv0ProdRndTeamCnName" column="lv0_prod_rd_team_cn_name"/>
        <result property="lv1ProdRndTeamCode" column="lv1_prod_rnd_team_code"/>
        <result property="lv1ProdRndTeamCnName" column="lv1_prod_rd_team_cn_name"/>
        <result property="lv2ProdRndTeamCnName" column="lv2_prod_rd_team_cn_name"/>
        <result property="lv2ProdRndTeamCode" column="lv2_prod_rnd_team_code"/>
        <result property="lv3ProdRndTeamCnName" column="lv3_prod_rd_team_cn_name"/>
        <result property="lv3ProdRndTeamCode" column="lv3_prod_rnd_team_code"/>
        <result property="lv4ProdRndTeamCnName" column="lv4_prod_rd_team_cn_name"/>
        <result property="lv4ProdRndTeamCode" column="lv4_prod_rnd_team_code"/>
        <result property="l1Name" column="l1_name"/>
        <result property="l2Name" column="l2_name"/>
        <result property="dimensionCode" column="dimension_code"/>
        <result property="dimensionCnName" column="dimension_cn_name"/>
        <result property="dimensionSubCategoryCode" column="dimension_subcategory_code"/>
        <result property="dimensionSubCategoryCnName" column="dimension_subcategory_cn_name"/>
        <result property="dimensionSubDetailCode" column="dimension_sub_detail_code"/>
        <result property="dimensionSubDetailCnName" column="dimension_sub_detail_cn_name"/>
        <result property="spartCnName" column="spart_cn_name"/>
        <result property="spartCode" column="spart_code"/>
        <result property="coaCnName" column="coa_cn_name"/>
        <result property="coaCode" column="coa_code"/>
        <result property="rmbCostAmt" column="rmb_cost_amt"/>
        <result property="costType" column="cost_type"/>
        <result property="percentage" column="percentage"/>
        <result property="rmbCostAmtDouble" column="rmbCostAmtDouble"/>
        <result property="percentageDouble" column="percentageDouble"/>
    </resultMap>

    <select id="totalAllIndustryCost" resultMap="annualResultMap">
        <if test='granularityType == "U"'>
            select distinct amp.prod_rd_team_cn_name as prod_rnd_team_cn_name,
            amp.group_cn_name,amp.group_code,amp.period_year,
            CONCAT(ROUND(amp.annual_amp*100,1),'%') as annual_amp,amp.group_level,
            status.status_code,status.append_year,'T' as cost_type,
            info.lv0_prod_rd_team_cn_name,info.lv0_prod_rnd_team_code
            <choose>
                <when test='teamLevel == "LV1"'>
                    ,info.lv1_prod_rd_team_cn_name,info.lv1_prod_rnd_team_code
                </when>
                <when test='teamLevel == "LV2"'>
                    ,info.lv1_prod_rd_team_cn_name,info.lv1_prod_rnd_team_code,info.lv2_prod_rd_team_cn_name,info.lv2_prod_rnd_team_code
                </when>
                <when test='teamLevel == "LV3"'>
                    ,info.lv1_prod_rd_team_cn_name,info.lv1_prod_rnd_team_code,info.lv2_prod_rd_team_cn_name,info.lv2_prod_rnd_team_code,info.lv3_prod_rd_team_cn_name,info.lv3_prod_rnd_team_code
                </when>
                <when test='teamLevel == "LV4"'>
                    ,info.lv1_prod_rd_team_cn_name,info.lv1_prod_rnd_team_code,info.lv2_prod_rd_team_cn_name,info.lv2_prod_rnd_team_code,info.lv3_prod_rd_team_cn_name,info.lv3_prod_rnd_team_code,
                    info.lv4_prod_rd_team_cn_name,info.lv4_prod_rnd_team_code
                </when>
                <otherwise>
                </otherwise>
            </choose>
            from fin_dm_opt_foi.${tablePreFix}_total_annual_amp_t amp left join
            fin_dm_opt_foi.${tablePreFix}_total_annual_status_code_t status
            on amp.group_code = status.group_code and amp.group_level = status.group_level
            and amp.period_year = status.period_year and amp.version_id = status.version_id
            and amp.view_flag = status.view_flag and amp.prod_rnd_team_code = status.prod_rnd_team_code
            and amp.caliber_flag = status.caliber_flag and amp.parent_code = status.parent_code
            and amp.oversea_flag = status.oversea_flag and amp.lv0_prod_list_code = status.lv0_prod_list_code
            left join fin_dm_opt_foi.${tablePreFix}_total_view_info_d info
            on amp.group_level = info.group_level
            and amp.view_flag = info.view_flag and amp.caliber_flag = info.caliber_flag
            and amp.oversea_flag = info.oversea_flag and amp.lv0_prod_list_code = info.lv0_prod_list_code
            and info.page_flag ='ANNUAL'
            <choose>
                <when test='teamLevel == "LV1"'>
                    and amp.prod_rnd_team_code = info.lv1_prod_rnd_team_code
                </when>
                <when test='teamLevel == "LV2"'>
                    and amp.prod_rnd_team_code = info.lv2_prod_rnd_team_code
                </when>
                <when test='teamLevel == "LV3"'>
                    and amp.prod_rnd_team_code = info.lv3_prod_rnd_team_code
                </when>
                <when test='teamLevel == "LV4"'>
                    and amp.prod_rnd_team_code = info.lv4_prod_rnd_team_code
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='granularityType == "P"'>
            select distinct amp.prod_rd_team_cn_name as prod_rnd_team_cn_name, amp.l1_name,amp.l2_name,
            amp.group_cn_name,amp.group_code,amp.period_year,
            CONCAT(ROUND(amp.annual_amp*100,1),'%') as annual_amp,amp.group_level,
            status.status_code,status.append_year,'T' as cost_type,
            info.lv0_prod_rd_team_cn_name,info.lv0_prod_rnd_team_code
            <choose>
                <when test='teamLevel == "LV1"'>
                    ,info.lv1_prod_rd_team_cn_name,info.lv1_prod_rnd_team_code
                </when>
                <when test='teamLevel == "LV2"'>
                    ,info.lv1_prod_rd_team_cn_name,info.lv1_prod_rnd_team_code,info.lv2_prod_rd_team_cn_name,info.lv2_prod_rnd_team_code
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <choose>
                <when test='groupLevel == "L1"'>
                    ,info.l1_name
                </when>
                <when test='groupLevel == "L2"'>
                    ,info.l1_name,info.l2_name
                </when>
                <otherwise>
                </otherwise>
            </choose>
            from fin_dm_opt_foi.${tablePreFix}_total_pft_annual_amp_t amp left join
            fin_dm_opt_foi.${tablePreFix}_total_pft_annual_status_code_t status
            on amp.group_code = status.group_code and amp.group_level = status.group_level
            and amp.period_year = status.period_year and amp.version_id = status.version_id
            and amp.view_flag = status.view_flag and amp.prod_rnd_team_code = status.prod_rnd_team_code
            and nvl ( amp.l1_name, 'snull' ) = nvl ( status.l1_name, 'snull' )
            and nvl ( amp.l2_name, 'snull' ) = nvl ( status.l2_name, 'snull' )
            and amp.caliber_flag = status.caliber_flag and amp.parent_code = status.parent_code
            and amp.oversea_flag = status.oversea_flag and amp.lv0_prod_list_code = status.lv0_prod_list_code
            left join fin_dm_opt_foi.${tablePreFix}_total_pft_view_info_d info
            on amp.group_level = info.group_level
            and amp.view_flag = info.view_flag and amp.caliber_flag = info.caliber_flag
            and amp.oversea_flag = info.oversea_flag and amp.lv0_prod_list_code = info.lv0_prod_list_code
            and info.page_flag ='ANNUAL'
            <choose>
                <when test='teamLevel == "LV1"'>
                    and amp.prod_rnd_team_code = info.lv1_prod_rnd_team_code
                </when>
                <when test='teamLevel == "LV2"'>
                    and amp.prod_rnd_team_code = info.lv2_prod_rnd_team_code
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <choose>
                <when test='groupLevel == "L1"'>
                    and amp.group_code = info.l1_name
                </when>
                <when test='groupLevel == "L2"'>
                    and amp.l1_name = info.l1_name
                    and amp.group_code = info.l2_name
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='granularityType == "D"'>
            select distinct amp.prod_rd_team_cn_name as prod_rnd_team_cn_name,amp.group_level,
            amp.dimension_code,amp.dimension_cn_name,amp.dimension_subcategory_code,amp.dimension_subcategory_cn_name,
            amp.dimension_sub_detail_code,amp.dimension_sub_detail_cn_name,amp.spart_code,amp.spart_cn_name,
            <if test='industryOrg == "ENERGY"'>
                amp.coa_code,amp.coa_cn_name,
            </if>
            amp.group_cn_name,amp.period_year,amp.group_code,
            CONCAT(ROUND(amp.annual_amp*100,1),'%') as annual_amp,
            status.status_code,status.append_year,'T' as cost_type,
            info.lv0_prod_rd_team_cn_name,info.lv0_prod_rnd_team_code
            <choose>
                <when test='teamLevel == "LV1"'>
                    ,info.lv1_prod_rd_team_cn_name,info.lv1_prod_rnd_team_code
                </when>
                <when test='teamLevel == "LV2"'>
                    ,info.lv1_prod_rd_team_cn_name,info.lv1_prod_rnd_team_code,info.lv2_prod_rd_team_cn_name,info.lv2_prod_rnd_team_code
                </when>
                <when test='teamLevel == "LV3"'>
                    ,info.lv1_prod_rd_team_cn_name,info.lv1_prod_rnd_team_code,info.lv2_prod_rd_team_cn_name,info.lv2_prod_rnd_team_code,info.lv3_prod_rd_team_cn_name,info.lv3_prod_rnd_team_code
                </when>
                <when test='teamLevel == "LV4"'>
                    ,info.lv1_prod_rd_team_cn_name,info.lv1_prod_rnd_team_code,info.lv2_prod_rd_team_cn_name,info.lv2_prod_rnd_team_code,info.lv3_prod_rd_team_cn_name,info.lv3_prod_rnd_team_code,
                    info.lv4_prod_rd_team_cn_name,info.lv4_prod_rnd_team_code
                </when>
                <otherwise>
                </otherwise>
            </choose>
            from fin_dm_opt_foi.${tablePreFix}_total_dms_annual_amp_t amp left join
            fin_dm_opt_foi.${tablePreFix}_total_dms_annual_status_code_t status
            on amp.group_code = status.group_code and amp.group_level = status.group_level
            and amp.period_year = status.period_year and amp.version_id = status.version_id
            and amp.view_flag = status.view_flag and amp.prod_rnd_team_code = status.prod_rnd_team_code
            and amp.caliber_flag = status.caliber_flag and amp.parent_code = status.parent_code
            and amp.oversea_flag = status.oversea_flag and amp.lv0_prod_list_code = status.lv0_prod_list_code
            and nvl(amp.dimension_code,'snull') = nvl( status.dimension_code,'snull')
            and nvl(amp.dimension_subcategory_code,'snull') = nvl( status.dimension_subcategory_code,'snull')
            and nvl(amp.dimension_sub_detail_code,'snull') = nvl( status.dimension_sub_detail_code,'snull')
            and nvl(amp.spart_code,'snull') = nvl( status.spart_code,'snull')
            <if test='industryOrg == "ENERGY"'>
                and nvl(amp.coa_code,'snull') = nvl( status.coa_code,'snull')
            </if>
            left join fin_dm_opt_foi.${tablePreFix}_total_dms_view_info_d info
            on amp.group_level = info.group_level
            and amp.view_flag = info.view_flag and amp.caliber_flag = info.caliber_flag
            and amp.oversea_flag = info.oversea_flag and amp.lv0_prod_list_code = info.lv0_prod_list_code
            and info.page_flag ='ANNUAL'
            <choose>
                <when test='teamLevel == "LV1"'>
                    and amp.prod_rnd_team_code = info.lv1_prod_rnd_team_code
                </when>
                <when test='teamLevel == "LV2"'>
                    and amp.prod_rnd_team_code = info.lv2_prod_rnd_team_code
                </when>
                <when test='teamLevel == "LV3"'>
                    and amp.prod_rnd_team_code = info.lv3_prod_rnd_team_code
                </when>
                <when test='teamLevel == "LV4"'>
                    and amp.prod_rnd_team_code = info.lv4_prod_rnd_team_code
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <choose>
                <when test='groupLevel == "DIMENSION"'>
                    and amp.group_code = info.dimension_code
                </when>
                <when test='groupLevel == "SUBCATEGORY"'>
                    and amp.group_code = info.dimension_subcategory_code
                </when>
                <when test='groupLevel == "SUB_DETAIL"'>
                    and amp.group_code = info.dimension_sub_detail_code
                </when>
                <when test='groupLevel == "SPART"'>
                    and amp.group_code = info.spart_code
                </when>
                <when test='groupLevel == "COA"'>
                    and amp.group_code = info.coa_code
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and groupLevel != "LV2" and groupLevel != "LV3" and groupLevel != "LV4"'>
                <choose>
                    <when test='groupLevel == "COA"'>
                        and nvl ( amp.coa_code, 'snull' ) = nvl ( info.coa_code, 'snull' )
                    </when>
                    <when test='groupLevel == "DIMENSION"'>
                        <if test='viewFlag == "12" and industryOrg =="ENERGY"'>
                            and nvl ( amp.coa_code, 'snull' ) = nvl ( info.coa_code, 'snull' )
                        </if>
                        and nvl ( amp.dimension_code, 'snull' ) = nvl ( info.dimension_code, 'snull' )
                    </when>
                    <when test='groupLevel == "SUBCATEGORY"'>
                        <if test='viewFlag == "12" and industryOrg =="ENERGY"'>
                            and nvl ( amp.coa_code, 'snull' ) = nvl ( info.coa_code, 'snull' )
                        </if>
                        and nvl ( amp.dimension_code, 'snull' ) = nvl ( info.dimension_code, 'snull' )
                        and nvl ( amp.dimension_subcategory_code, 'snull' ) = nvl ( info.dimension_subcategory_code,
                        'snull' )
                    </when>
                    <when test='groupLevel == "SUB_DETAIL"'>
                        <if test='viewFlag == "12" and industryOrg =="ENERGY"'>
                            and nvl ( amp.coa_code, 'snull' ) = nvl ( info.coa_code, 'snull' )
                        </if>
                        and nvl ( amp.dimension_code, 'snull' ) = nvl ( info.dimension_code, 'snull' )
                        and nvl ( amp.dimension_subcategory_code, 'snull' ) = nvl ( info.dimension_subcategory_code,
                        'snull' )
                        and nvl ( amp.dimension_sub_detail_code, 'snull' ) = nvl ( info.dimension_sub_detail_code,
                        'snull' )
                    </when>
                    <when test='groupLevel == "SPART"'>
                        <if test='viewFlag == "12" and industryOrg =="ENERGY"'>
                            and nvl ( amp.coa_code, 'snull' ) = nvl ( info.coa_code, 'snull' )
                        </if>
                        and nvl ( amp.dimension_code, 'snull' ) = nvl ( info.dimension_code, 'snull' )
                        and nvl ( amp.dimension_subcategory_code, 'snull' ) = nvl ( info.dimension_subcategory_code,
                        'snull' )
                        and nvl ( amp.dimension_sub_detail_code, 'snull' ) = nvl ( info.dimension_sub_detail_code,
                        'snull' )
                        and nvl ( amp.spart_code, 'snull' ) = nvl ( info.spart_code, 'snull' )
                    </when>
                </choose>
            </if>
        </if>
        where amp.del_flag = 'N'
        <if test='caliberFlag != null and caliberFlag != ""'>
            and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag != null and overseaFlag != ""'>
            and amp.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='lv0ProdListCode != null and lv0ProdListCode != ""'>
            and amp.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and amp.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and amp.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='dimensionCodeList != null and dimensionCodeList.size() > 0'>
            <foreach collection='dimensionCodeList' item="code" open="AND amp.dimension_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionSubcategoryCodeList != null and dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='dimensionSubcategoryCodeList' item="code"
                     open="AND amp.dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionSubDetailCodeList != null and dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='dimensionSubDetailCodeList' item="code" open="AND amp.dimension_sub_detail_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='spartCodeList != null and spartCodeList.size() > 0'>
            <foreach collection='spartCodeList' item="code" open="AND amp.spart_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='coaCodeList != null and coaCodeList.size() > 0'>
            <foreach collection='coaCodeList' item="code" open="AND amp.coa_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='l1NameList != null and l1NameList.size() > 0'>
            <foreach collection='l1NameList' item="code" open="AND amp.l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='l2NameList != null and l2NameList.size() > 0'>
            <foreach collection='l2NameList' item="code" open="AND amp.l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupCodeList != null and groupCodeList.size() > 0'>
            <foreach collection='groupCodeList' item="code" open="AND amp.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamCodeList != null and teamCodeList.size() > 0'>
            <foreach collection='teamCodeList' item="code" open="AND amp.prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='periodYear != null'>
            and amp.period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and amp.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND amp.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        <if test='groupLevel =="LV1" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND amp.prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel =="LV2" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND amp.prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        order by amp.period_year
    </select>

    <select id="distributeCostList" resultMap="annualResultMap">
        <if test='granularityType == "U"'>
            select distinct prod_rnd_team_cn_name,group_cn_name,group_code,group_level,
            period_year,rmb_cost_amt,percentage,cost_type
            from fin_dm_opt_foi.${tablePreFix}_total_annual_weight_t
        </if>
        <if test='granularityType == "P"'>
            select distinct prod_rnd_team_cn_name, l1_name,l2_name,group_level,
            group_cn_name,group_code,period_year,rmb_cost_amt,percentage,cost_type
            from fin_dm_opt_foi.${tablePreFix}_total_pft_annual_weight_t
        </if>
        <if test='granularityType == "D"'>
            select distinct prod_rnd_team_cn_name,
            dimension_code,dimension_cn_name,
            dimension_subcategory_code,dimension_subcategory_cn_name,
            dimension_sub_detail_code,dimension_sub_detail_cn_name,
            spart_code,spart_cn_name,
            <if test='industryOrg == "ENERGY"'>
                coa_code,coa_cn_name,
            </if>
            group_level,group_code,group_cn_name,
            period_year,rmb_cost_amt,percentage,cost_type
            from fin_dm_opt_foi.${tablePreFix}_total_dms_annual_weight_t
        </if>
        where del_flag = 'N' and cost_type in ('M','P')
        <if test='caliberFlag != null and caliberFlag != ""'>
            and caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag != null and overseaFlag != ""'>
            and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='lv0ProdListCode != null and lv0ProdListCode != ""'>
            and lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='dimensionCodeList != null and dimensionCodeList.size() > 0'>
            <foreach collection='dimensionCodeList' item="code" open="AND dimension_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionSubcategoryCodeList != null and dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='dimensionSubcategoryCodeList' item="code" open="AND dimension_subcategory_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionSubDetailCodeList != null and dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='dimensionSubDetailCodeList' item="code" open="AND dimension_sub_detail_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='spartCodeList != null and spartCodeList.size() > 0'>
            <foreach collection='spartCodeList' item="code" open="AND spart_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='coaCodeList != null and coaCodeList.size() > 0'>
            <foreach collection='coaCodeList' item="code" open="AND coa_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='l1NameList != null and l1NameList.size() > 0'>
            <foreach collection='l1NameList' item="code" open="AND l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='l2NameList != null and l2NameList.size() > 0'>
            <foreach collection='l2NameList' item="code" open="AND l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupCodeList != null and groupCodeList.size() > 0'>
            <foreach collection='groupCodeList' item="code" open="AND group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamCodeList != null and teamCodeList.size() > 0'>
            <foreach collection='teamCodeList' item="code" open="AND prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='periodYear != null'>
            and period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        <if test='groupLevel =="LV1" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel =="LV2" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        order by period_year
    </select>

    <select id="distributeCostExcelList" resultMap="annualResultMap">
        <if test='granularityType == "U"'>
            select
            prod_rnd_team_cn_name,group_cn_name,group_level,group_code,period_year,IFNULL(SUM(rmb_cost_amt),0)/100000000
            as rmbCostAmtDouble,IFNULL(SUM(percentage ),0)*100 as percentageDouble,cost_type
            from fin_dm_opt_foi.${tablePreFix}_total_annual_weight_t
        </if>
        <if test='granularityType == "P"'>
            select prod_rnd_team_cn_name, l1_name,l2_name,group_level
            group_cn_name,group_code,period_year,IFNULL(SUM (rmb_cost_amt ),0 )/100000000 as
            rmbCostAmtDouble,IFNULL(SUM(percentage ),0)*100 as percentageDouble,cost_type
            from fin_dm_opt_foi.${tablePreFix}_total_pft_annual_weight_t
        </if>
        <if test='granularityType == "D"'>
            select prod_rnd_team_cn_name,
            dimension_code,dimension_cn_name,
            dimension_subcategory_code,dimension_subcategory_cn_name,
            dimension_sub_detail_code,dimension_sub_detail_cn_name,
            group_level,group_cn_name,group_code,
            period_year,IFNULL(SUM(rmb_cost_amt),0 )/100000000 as rmbCostAmtDouble,IFNULL(SUM(percentage ),0)*100 as
            percentageDouble,cost_type
            from fin_dm_opt_foi.${tablePreFix}_total_dms_annual_weight_t
        </if>
        where del_flag = 'N'
        <if test='caliberFlag != null and caliberFlag != ""'>
            and caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag != null and overseaFlag != ""'>
            and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='lv0ProdListCode != null and lv0ProdListCode != ""'>
            and lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='dimensionCodeList != null and dimensionCodeList.size() > 0'>
            <foreach collection='dimensionCodeList' item="code" open="AND dimension_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionSubcategoryCodeList != null and dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='dimensionSubcategoryCodeList' item="code" open="AND dimension_subcategory_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionSubDetailCodeList != null and dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='dimensionSubDetailCodeList' item="code" open="AND dimension_sub_detail_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='coaCodeList != null and coaCodeList.size() > 0'>
            <foreach collection='coaCodeList' item="code" open="AND coa_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='l1NameList != null and l1NameList.size() > 0'>
            <foreach collection='l1NameList' item="code" open="AND l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='l2NameList != null and l2NameList.size() > 0'>
            <foreach collection='l2NameList' item="code" open="AND l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupCodeList != null and groupCodeList.size() > 0'>
            <foreach collection='groupCodeList' item="code" open="AND group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamCodeList != null and teamCodeList.size() > 0'>
            <foreach collection='teamCodeList' item="code" open="AND prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='periodYear != null'>
            and period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        <if test='groupLevel =="LV1" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel =="LV2" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        group by prod_rnd_team_cn_name,group_cn_name,group_code,group_level,period_year,cost_type
        <if test='granularityType == "P"'>
            ,l1_name,l2_name
        </if>
        <if test='granularityType == "D"'>
            ,dimension_code,dimension_cn_name,
            dimension_subcategory_code,dimension_subcategory_cn_name,
            dimension_sub_detail_code,dimension_sub_detail_cn_name,group_level
        </if>
        order by period_year
    </select>

    <select id="findTotalProdteamCodeOrderByWeight" resultMap="annualResultMap">
        select group_code, group_cn_name,ROUND(absolute_weight*100,1) weight_rate
        <if test='granularityType == "U"'>
            from fin_dm_opt_foi.${tablePreFix}_total_annual_weight_t
        </if>
        <if test='granularityType == "P"'>
            from fin_dm_opt_foi.${tablePreFix}_total_pft_annual_weight_t
        </if>
        where del_flag = 'N' and cost_type = 'T'
        <if test='caliberFlag != null and caliberFlag != ""'>
            and caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag != null and overseaFlag != ""'>
            and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='lv0ProdListCode != null and lv0ProdListCode != ""'>
            and lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='teamLevel != null and teamLevel != ""'>
            and group_level = #{teamLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='year != null and year !=""'>
            and period_year = #{year,jdbcType=VARCHAR}
        </if>
        <if test='teamCodeList != null and teamCodeList.size() > 0'>
            <foreach collection='teamCodeList' item="code" open="AND group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel =="LV1" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel =="LV2" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        ORDER BY weight_rate DESC
    </select>

    <select id="findTotalGranularityTypeOrderByWeight" resultMap="annualResultMap">
        select group_code, group_cn_name,ROUND(absolute_weight*100,1) weight_rate
        <if test='granularityType == "P"'>
            from fin_dm_opt_foi.${tablePreFix}_total_pft_annual_weight_t
            where del_flag = 'N'
        </if>
        <if test='granularityType == "D"'>
            from fin_dm_opt_foi.${tablePreFix}_total_dms_annual_weight_t
            where del_flag = 'N'
        </if>
        and cost_type = 'T'
        <if test='caliberFlag != null and caliberFlag != ""'>
            and caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag != null and overseaFlag != ""'>
            and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='lv0ProdListCode != null and lv0ProdListCode != ""'>
            and lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='year != null and year !=""'>
            and period_year = #{year,jdbcType=VARCHAR}
        </if>
        <if test='groupCodeList != null and groupCodeList.size() > 0'>
            <foreach collection='groupCodeList' item="code" open="AND group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamCodeList != null and teamCodeList.size() > 0'>
            <foreach collection='teamCodeList' item="code" open="AND prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionCodeList != null and dimensionCodeList.size() > 0'>
            <foreach collection='dimensionCodeList' item="code" open="AND dimension_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionSubcategoryCodeList != null and dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='dimensionSubcategoryCodeList' item="code" open="AND dimension_subcategory_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionSubDetailCodeList != null and dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='dimensionSubDetailCodeList' item="code" open="AND dimension_sub_detail_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='spartCodeList != null and spartCodeList.size() > 0'>
            <foreach collection='spartCodeList' item="code" open="AND spart_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='coaCodeList != null and coaCodeList.size() > 0'>
            <foreach collection='coaCodeList' item="code" open="AND coa_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='l1NameList != null and l2NameList.size() > 0'>
            <foreach collection='l1NameList' item="code" open="AND l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='l2NameList != null and l2NameList.size() > 0'>
            <foreach collection='l2NameList' item="code" open="AND l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel =="LV1" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel =="LV2" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        ORDER BY weight_rate DESC
    </select>

    <select id="findTotalGroupCodeOrderByWeight" resultMap="annualResultMap">
        select group_code, group_cn_name,ROUND(weight_rate*100,1) weight_rate
        <if test='granularityType == "U"'>
            from fin_dm_opt_foi.${tablePreFix}_total_annual_weight_t
        </if>
        <if test='granularityType == "P"'>
            from fin_dm_opt_foi.${tablePreFix}_total_pft_annual_weight_t
        </if>
        <if test='granularityType == "D"'>
            from fin_dm_opt_foi.${tablePreFix}_total_dms_annual_weight_t
        </if>
        where del_flag = 'N' and cost_type = 'T'
        <if test='caliberFlag != null and caliberFlag != ""'>
            and caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag != null and overseaFlag != ""'>
            and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='lv0ProdListCode != null and lv0ProdListCode != ""'>
            and lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='year != null and year !=""'>
            and period_year = #{year,jdbcType=VARCHAR}
        </if>
        <if test='dimensionCodeList != null and dimensionCodeList.size() > 0'>
            <foreach collection='dimensionCodeList' item="code" open="AND dimension_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionSubcategoryCodeList != null and dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='dimensionSubcategoryCodeList' item="code" open="AND dimension_subcategory_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionSubDetailCodeList != null and dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='dimensionSubDetailCodeList' item="code" open="AND dimension_sub_detail_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='spartCodeList != null and spartCodeList.size() > 0'>
            <foreach collection='spartCodeList' item="code" open="AND spart_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='coaCodeList != null and coaCodeList.size() > 0'>
            <foreach collection='coaCodeList' item="code" open="AND coa_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='l1NameList != null and l1NameList.size() > 0'>
            <foreach collection='l1NameList' item="code" open="AND l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='l2NameList != null and l2NameList.size() > 0'>
            <foreach collection='l2NameList' item="code" open="AND l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='parentCodeList != null and parentCodeList.size() > 0'>
            <foreach collection='parentCodeList' item="code" open="AND parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamCodeList != null and teamCodeList.size() > 0'>
            <foreach collection='teamCodeList' item="code" open="AND prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='subGroupCodeList != null and subGroupCodeList.size() > 0'>
            <foreach collection='subGroupCodeList' item="code" open="AND group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel =="LV1" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel =="LV2" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        ORDER BY weight_rate DESC
    </select>

    <select id="totalMultiIndustryCostChart" resultMap="annualResultMap">
        select amp1.prod_rd_team_cn_name AS prod_rnd_team_cn_name,
        amp1.prod_rnd_team_code,
        amp1.period_year,
        CONCAT(ROUND( SUM ( amp1.annual_amp*100 ), 1 ),'%') AS annual_amp,
        amp1.group_level,
        amp1.group_code,
        amp1.group_cn_name,
        CONCAT(ROUND(SUM ( weight.weight_rate*100 ),1),'%') AS weight_rate,
        CONCAT (amp1.group_code, ' ', amp1.group_cn_name ) AS groupCodeAndName,
        status.status_code,max(status.append_year) AS append_year,weight.append_flag
        <if test='granularityType == "U" and viewFlag!="4" and viewFlag!="5" and viewFlag!="6"'>
            from fin_dm_opt_foi.${tablePreFix}_total_annual_amp_t amp1
            left join fin_dm_opt_foi.${tablePreFix}_total_annual_weight_t weight
            on amp1.view_flag = weight.view_flag and weight.cost_type = 'T'
            and amp1.group_code = weight.group_code and amp1.group_level = weight.group_level
            and amp1.prod_rnd_team_code = weight.prod_rnd_team_code and amp1.PARENT_CODE = weight.PARENT_CODE
            and amp1.version_id =weight.version_id and amp1.period_year = weight.period_year
            and amp1.caliber_flag = weight.caliber_flag and amp1.oversea_flag = weight.oversea_flag
            and amp1.lv0_prod_list_code = weight.lv0_prod_list_code
            left join fin_dm_opt_foi.${tablePreFix}_total_annual_status_code_t status
            on amp1.group_code = status.group_code and amp1.group_level = status.group_level
            and amp1.period_year = status.period_year and amp1.version_id = status.version_id
            and amp1.view_flag = status.view_flag and amp1.prod_rnd_team_code = status.prod_rnd_team_code
            and amp1.caliber_flag = status.caliber_flag and amp1.PARENT_CODE = status.PARENT_CODE
            and amp1.oversea_flag = status.oversea_flag and amp1.lv0_prod_list_code = status.lv0_prod_list_code
        </if>
        <if test='granularityType == "P"'>
            from fin_dm_opt_foi.${tablePreFix}_total_pft_annual_amp_t amp1
            left join fin_dm_opt_foi.${tablePreFix}_total_pft_annual_weight_t weight
            on amp1.view_flag = weight.view_flag and weight.cost_type = 'T' and amp1.group_code = weight.group_code
            and amp1.group_level = weight.group_level and amp1.PARENT_CODE = weight.PARENT_CODE
            and amp1.prod_rnd_team_code = weight.prod_rnd_team_code
            and nvl ( amp1.l1_name, 'snull' ) = nvl ( weight.l1_name, 'snull' )
            and nvl ( amp1.l2_name, 'snull' ) = nvl ( weight.l2_name, 'snull' )
            and amp1.version_id =weight.version_id and amp1.period_year = weight.period_year
            and amp1.caliber_flag = weight.caliber_flag
            and amp1.oversea_flag = weight.oversea_flag
            and amp1.lv0_prod_list_code = weight.lv0_prod_list_code
            left join fin_dm_opt_foi.${tablePreFix}_total_pft_annual_status_code_t status
            on amp1.group_code = status.group_code and amp1.group_level = status.group_level
            and nvl ( amp1.l1_name, 'snull' ) = nvl ( status.l1_name, 'snull' )
            and nvl ( amp1.l2_name, 'snull' ) = nvl ( status.l2_name, 'snull' )
            and amp1.period_year = status.period_year and amp1.version_id = status.version_id
            and amp1.view_flag = status.view_flag and amp1.prod_rnd_team_code = status.prod_rnd_team_code
            and amp1.caliber_flag = status.caliber_flag and amp1.PARENT_CODE = status.PARENT_CODE
            and amp1.oversea_flag = status.oversea_flag and amp1.lv0_prod_list_code = status.lv0_prod_list_code
        </if>
        <if test='granularityType == "D"'>
            from fin_dm_opt_foi.${tablePreFix}_total_dms_annual_amp_t amp1
            left join fin_dm_opt_foi.${tablePreFix}_total_dms_annual_weight_t weight
            on amp1.view_flag = weight.view_flag and weight.cost_type = 'T'
            and amp1.group_code = weight.group_code and amp1.group_level = weight.group_level
            and amp1.prod_rnd_team_code = weight.prod_rnd_team_code and amp1.PARENT_CODE = weight.PARENT_CODE
            and amp1.version_id =weight.version_id and amp1.period_year = weight.period_year
            and amp1.caliber_flag = weight.caliber_flag and amp1.oversea_flag = weight.oversea_flag
            and amp1.lv0_prod_list_code = weight.lv0_prod_list_code
            and nvl(amp1.dimension_code,'snull') = nvl( weight.dimension_code,'snull')
            and nvl(amp1.dimension_subcategory_code,'snull') = nvl( weight.dimension_subcategory_code,'snull')
            and nvl(amp1.dimension_sub_detail_code,'snull') = nvl( weight.dimension_sub_detail_code,'snull')
            and nvl(amp1.spart_code,'snull') = nvl( weight.spart_code,'snull')
            <if test='industryOrg == "ENERGY"'>
                and nvl(amp1.coa_code,'snull') = nvl( weight.coa_code,'snull')
            </if>
            left join fin_dm_opt_foi.${tablePreFix}_total_dms_annual_status_code_t status
            on amp1.group_code = status.group_code and amp1.group_level = status.group_level
            and amp1.period_year = status.period_year and amp1.version_id = status.version_id
            and amp1.view_flag = status.view_flag and amp1.prod_rnd_team_code = status.prod_rnd_team_code
            and amp1.caliber_flag = status.caliber_flag and amp1.PARENT_CODE = status.PARENT_CODE
            and amp1.oversea_flag = status.oversea_flag and amp1.lv0_prod_list_code = status.lv0_prod_list_code
            and nvl(amp1.dimension_code,'snull') = nvl( status.dimension_code,'snull')
            and nvl(amp1.dimension_subcategory_code,'snull') = nvl( status.dimension_subcategory_code,'snull')
            and nvl(amp1.dimension_sub_detail_code,'snull') = nvl( status.dimension_sub_detail_code,'snull')
            and nvl(amp1.spart_code,'snull') = nvl( status.spart_code,'snull')
            <if test='industryOrg == "ENERGY"'>
                and nvl(amp1.coa_code,'snull') = nvl( status.coa_code,'snull')
            </if>
        </if>
        where amp1.del_flag = 'N'
        <if test='caliberFlag != null and caliberFlag != ""'>
            and amp1.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag != null and overseaFlag != ""'>
            and amp1.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='lv0ProdListCode != null and lv0ProdListCode != ""'>
            and amp1.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and amp1.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and amp1.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and amp1.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='dimensionCodeList != null and dimensionCodeList.size() > 0'>
            <foreach collection='dimensionCodeList' item="code" open="AND amp1.dimension_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionSubcategoryCodeList != null and dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='dimensionSubcategoryCodeList' item="code"
                     open="AND amp1.dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionSubDetailCodeList != null and dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='dimensionSubDetailCodeList' item="code" open="AND amp1.dimension_sub_detail_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='spartCodeList != null and spartCodeList.size() > 0'>
            <foreach collection='spartCodeList' item="code" open="AND amp1.spart_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='coaCodeList != null and coaCodeList.size() > 0'>
            <foreach collection='coaCodeList' item="code" open="AND amp1.coa_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='l1NameList != null and l1NameList.size() > 0'>
            <foreach collection='l1NameList' item="code" open="AND amp1.l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='l2NameList != null and l2NameList.size() > 0'>
            <foreach collection='l2NameList' item="code" open="AND amp1.l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='parentCodeList != null and parentCodeList.size() > 0'>
            <foreach collection='parentCodeList' item="code" open="AND amp1.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamCodeList != null and teamCodeList.size() > 0'>
            <foreach collection='teamCodeList' item="code" open="AND amp1.prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND amp1.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        <if test='groupCodeOrder != null and groupCodeOrder != ""'>
            <foreach collection="groupCodeOrder.split(',')" item="item" open="and amp1.group_code IN (" close=")"
                     index="index" separator=",">
                #{item}
            </foreach>
        </if>
        <if test='groupLevel =="LV1" and  lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND amp1.prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel =="LV2" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND amp1.prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        GROUP BY amp1.prod_rd_team_cn_name,amp1.prod_rnd_team_code,weight.append_flag,
        amp1.group_level,amp1.group_code,amp1.group_cn_name,groupCodeAndName,amp1.period_year,status.status_code
        order by locate(amp1.group_code, #{groupCodeOrder}), amp1.period_year
    </select>

    <select id="totalMultiIndustryCostChartMutilSelect" resultMap="annualResultMap">
        <if test='granularityType == "U"'>
            select amp1.prod_rnd_team_code,amp1.prod_rd_team_cn_name as prod_rnd_team_cn_name,
            amp1.parent_code,amp1.parent_cn_name,amp1.period_year,
            CONCAT(ROUND( SUM ( amp1.annual_amp*100 ), 1 ),'%') AS annual_amp,
            amp1.group_code,amp1.group_cn_name,amp1.group_level,
            CONCAT(ROUND(SUM ( weight.absolute_weight*100 ),1),'%') AS weight_rate,
            CONCAT ( amp1.group_code, ' ', amp1.group_cn_name ) AS groupCodeAndName,
            status.status_code,max(status.append_year) AS append_year,
            'T' AS cost_type,weight.append_flag
            from fin_dm_opt_foi.${tablePreFix}_total_annual_amp_t amp1
            left join fin_dm_opt_foi.${tablePreFix}_total_annual_weight_t weight
            on amp1.view_flag = weight.view_flag and weight.cost_type = 'T'
            and amp1.group_code = weight.group_code and amp1.group_level = weight.group_level
            and amp1.prod_rnd_team_code = weight.prod_rnd_team_code and amp1.PARENT_CODE = weight.PARENT_CODE
            and amp1.version_id =weight.version_id and amp1.period_year = weight.period_year
            and amp1.caliber_flag = weight.caliber_flag and amp1.oversea_flag = weight.oversea_flag
            and amp1.lv0_prod_list_code = weight.lv0_prod_list_code
            left join fin_dm_opt_foi.${tablePreFix}_total_annual_status_code_t status
            on amp1.group_code = status.group_code and amp1.group_level = status.group_level
            and amp1.period_year = status.period_year and amp1.version_id = status.version_id
            and amp1.view_flag = status.view_flag and amp1.prod_rnd_team_code = status.prod_rnd_team_code
            and amp1.caliber_flag = status.caliber_flag and amp1.PARENT_CODE = status.PARENT_CODE
            and amp1.oversea_flag = status.oversea_flag and amp1.lv0_prod_list_code = status.lv0_prod_list_code
        </if>
        <if test='granularityType == "P"'>
            select amp1.prod_rd_team_cn_name as prod_rnd_team_cn_name,amp1.prod_rnd_team_code,
            amp1.l1_name, amp1.l2_name,amp1.parent_code,amp1.parent_cn_name,amp1.period_year,
            CONCAT(ROUND( SUM ( amp1.annual_amp*100 ), 1 ),'%') AS annual_amp,
            amp1.group_code, amp1.group_cn_name, amp1.group_level,
            CONCAT(ROUND(SUM ( weight.absolute_weight*100 ),1),'%') AS weight_rate,
            CONCAT ( amp1.group_code, ' ', amp1.group_cn_name ) AS groupCodeAndName,
            status.status_code,max(status.append_year) AS append_year,
            'T' AS cost_type,weight.append_flag
            from fin_dm_opt_foi.${tablePreFix}_total_pft_annual_amp_t amp1
            left join fin_dm_opt_foi.${tablePreFix}_total_pft_annual_weight_t weight
            on amp1.view_flag = weight.view_flag and weight.cost_type = 'T' and amp1.group_code = weight.group_code
            and amp1.group_level = weight.group_level and amp1.PARENT_CODE = weight.PARENT_CODE
            and amp1.prod_rnd_team_code = weight.prod_rnd_team_code
            and nvl ( amp1.l1_name, 'snull' ) = nvl ( weight.l1_name, 'snull' )
            and nvl ( amp1.l2_name, 'snull' ) = nvl ( weight.l2_name, 'snull' )
            and amp1.version_id =weight.version_id and amp1.period_year = weight.period_year
            and amp1.caliber_flag = weight.caliber_flag and amp1.oversea_flag = weight.oversea_flag
            and amp1.lv0_prod_list_code = weight.lv0_prod_list_code
            left join fin_dm_opt_foi.${tablePreFix}_total_pft_annual_status_code_t status
            on amp1.group_code = status.group_code and amp1.group_level = status.group_level
            and nvl ( amp1.l1_name, 'snull' ) = nvl ( status.l1_name, 'snull' )
            and nvl ( amp1.l2_name, 'snull' ) = nvl ( status.l2_name, 'snull' )
            and amp1.period_year = status.period_year and amp1.version_id = status.version_id
            and amp1.view_flag = status.view_flag and amp1.prod_rnd_team_code = status.prod_rnd_team_code
            and amp1.caliber_flag = status.caliber_flag and amp1.PARENT_CODE = status.PARENT_CODE
            and amp1.oversea_flag = status.oversea_flag and amp1.lv0_prod_list_code = status.lv0_prod_list_code
        </if>
        <if test='granularityType == "D"'>
            select amp1.prod_rd_team_cn_name as prod_rnd_team_cn_name,amp1.prod_rnd_team_code,
            amp1.dimension_code,amp1.dimension_cn_name,
            amp1.dimension_subcategory_code,amp1.dimension_subcategory_cn_name,
            amp1.dimension_sub_detail_code,amp1.dimension_sub_detail_cn_name,amp1.spart_code,amp1.spart_cn_name,
            <if test='industryOrg == "ENERGY"'>
                amp1.coa_code,amp1.coa_cn_name,
            </if>
            amp1.parent_code,amp1.parent_cn_name,amp1.period_year,
            CONCAT(ROUND( SUM ( amp1.annual_amp*100 ), 1 ),'%') AS annual_amp,
            amp1.group_code,amp1.group_level,amp1.group_cn_name,
            CONCAT(ROUND(SUM ( weight.absolute_weight*100 ),1),'%') AS weight_rate,
            CONCAT(amp1.group_code, ' ', amp1.group_cn_name ) AS groupCodeAndName,
            status.status_code,max(status.append_year) AS append_year,
            'T' AS cost_type,weight.append_flag
            from fin_dm_opt_foi.${tablePreFix}_total_dms_annual_amp_t amp1
            left join fin_dm_opt_foi.${tablePreFix}_total_dms_annual_weight_t weight
            on amp1.view_flag = weight.view_flag and weight.cost_type = 'T'
            and amp1.group_code = weight.group_code and amp1.group_level = weight.group_level
            and amp1.prod_rnd_team_code = weight.prod_rnd_team_code and amp1.PARENT_CODE = weight.PARENT_CODE
            and amp1.version_id =weight.version_id and amp1.period_year = weight.period_year
            and amp1.caliber_flag = weight.caliber_flag and amp1.oversea_flag = weight.oversea_flag
            and amp1.lv0_prod_list_code = weight.lv0_prod_list_code
            and nvl(amp1.dimension_code,'snull') = nvl( weight.dimension_code,'snull')
            and nvl(amp1.dimension_subcategory_code,'snull') = nvl( weight.dimension_subcategory_code,'snull')
            and nvl(amp1.dimension_sub_detail_code,'snull') = nvl( weight.dimension_sub_detail_code,'snull')
            and nvl(amp1.spart_code,'snull') = nvl( weight.spart_code,'snull')
            <if test='industryOrg == "ENERGY"'>
                and nvl(amp1.coa_code,'snull') = nvl( weight.coa_code,'snull')
            </if>
            left join fin_dm_opt_foi.${tablePreFix}_total_dms_annual_status_code_t status
            on amp1.group_code = status.group_code and amp1.group_level = status.group_level
            and amp1.period_year = status.period_year and amp1.version_id = status.version_id
            and amp1.view_flag = status.view_flag and amp1.prod_rnd_team_code = status.prod_rnd_team_code
            and amp1.caliber_flag = status.caliber_flag and amp1.PARENT_CODE = status.PARENT_CODE
            and amp1.oversea_flag = status.oversea_flag and amp1.lv0_prod_list_code = status.lv0_prod_list_code
            and nvl(amp1.dimension_code,'snull') = nvl( status.dimension_code,'snull')
            and nvl(amp1.dimension_subcategory_code,'snull') = nvl( status.dimension_subcategory_code,'snull')
            and nvl(amp1.dimension_sub_detail_code,'snull') = nvl( status.dimension_sub_detail_code,'snull')
            and nvl(amp1.spart_code,'snull') = nvl( status.spart_code,'snull')
            <if test='industryOrg == "ENERGY"'>
                and nvl(amp1.coa_code,'snull') = nvl( status.coa_code,'snull')
            </if>
        </if>
        where amp1.del_flag = 'N'
        <if test='caliberFlag != null and caliberFlag != ""'>
            and amp1.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag != null and overseaFlag != ""'>
            and amp1.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='lv0ProdListCode != null and lv0ProdListCode != ""'>
            and amp1.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and amp1.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and amp1.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and amp1.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='periodYear != null'>
            and amp1.period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='coaCodeList != null and coaCodeList.size() > 0'>
            <foreach collection='coaCodeList' item="code" open="AND amp1.coa_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionCodeList != null and dimensionCodeList.size() > 0'>
            <foreach collection='dimensionCodeList' item="code" open="AND amp1.dimension_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionSubcategoryCodeList != null and dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='dimensionSubcategoryCodeList' item="code"
                     open="AND amp1.dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionSubDetailCodeList != null and dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='dimensionSubDetailCodeList' item="code" open="AND amp1.dimension_sub_detail_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='l1NameList != null and l1NameList.size() > 0'>
            <foreach collection='l1NameList' item="code" open="AND amp1.l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='l2NameList != null and l2NameList.size() > 0'>
            <foreach collection='l2NameList' item="code" open="AND amp1.l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='parentCodeList != null and parentCodeList.size() > 0'>
            <foreach collection='parentCodeList' item="code" open="AND amp1.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamCodeList != null and teamCodeList.size() > 0'>
            <foreach collection='teamCodeList' item="code" open="AND amp1.prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='subGroupCodeList != null and subGroupCodeList.size() > 0'>
            <foreach collection='subGroupCodeList' item="code" open="AND amp1.group_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND amp1.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        <if test='groupLevel =="LV1" and  lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND amp1.prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel =="LV2" and  lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND amp1.prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='granularityType == "U"'>
            GROUP BY amp1.prod_rd_team_cn_name,amp1.prod_rnd_team_code, amp1.parent_code,amp1.parent_cn_name,
            amp1.group_code,amp1.group_cn_name,
            groupCodeAndName,amp1.period_year,status.status_code,amp1.group_level,weight.append_flag
            order by locate(amp1.prod_rnd_team_code, #{parentCodeOrder}),weight_rate desc
        </if>
        <if test='granularityType == "P"'>
            GROUP BY amp1.prod_rd_team_cn_name,amp1.prod_rnd_team_code,amp1.l1_name,
            amp1.l2_name,amp1.parent_code,amp1.parent_cn_name, amp1.group_code,amp1.group_cn_name,
            groupCodeAndName,amp1.period_year,status.status_code,amp1.group_level,weight.append_flag
            <choose>
                <when test='parentLevel=="LV1" or parentLevel=="LV2"'>
                    order by locate(amp1.prod_rnd_team_code, #{parentCodeOrder}),weight_rate desc
                </when>
                <when test='parentLevel=="L1"'>
                    order by locate(amp1.l1_name, #{parentCodeOrder}),weight_rate desc
                </when>
                <when test='parentLevel=="L2"'>
                    order by locate(amp1.l2_name, #{parentCodeOrder}),weight_rate desc
                </when>
                <otherwise>
                    order by weight_rate desc
                </otherwise>
            </choose>
        </if>
        <if test='granularityType == "D"'>
            GROUP BY amp1.prod_rd_team_cn_name,amp1.prod_rnd_team_code,
            amp1.dimension_code,amp1.dimension_cn_name,
            amp1.dimension_subcategory_code,amp1.dimension_subcategory_cn_name,
            amp1.dimension_sub_detail_code,amp1.dimension_sub_detail_cn_name,amp1.spart_code,amp1.spart_cn_name,
            <if test='industryOrg == "ENERGY"'>
                amp1.coa_code,amp1.coa_cn_name,
            </if>
            amp1.parent_code,amp1.parent_cn_name,amp1.group_code,amp1.group_cn_name,
            groupCodeAndName,amp1.period_year,status.status_code,amp1.group_level,weight.append_flag
            <choose>
                <when test='parentLevel=="LV1" or parentLevel=="LV2" or parentLevel=="LV3" or parentLevel=="LV4"'>
                    order by locate(amp1.prod_rnd_team_code, #{parentCodeOrder}),weight_rate desc
                </when>
                <when test='parentLevel=="DIMENSION"'>
                    order by locate(amp1.dimension_code, #{parentCodeOrder}),weight_rate desc
                </when>
                <when test='parentLevel=="SUBCATEGORY"'>
                    order by locate(amp1.dimension_subcategory_code, #{parentCodeOrder}),weight_rate desc
                </when>
                <when test='parentLevel=="SUB_DETAIL"'>
                    order by locate(amp1.dimension_sub_detail_code, #{parentCodeOrder}),weight_rate desc
                </when>
                <when test='parentLevel=="SPART"'>
                    order by locate(amp1.spart_code, #{parentCodeOrder}),weight_rate desc
                </when>
                <when test='parentLevel=="COA"'>
                    order by locate(amp1.coa_code, #{parentCodeOrder}),weight_rate desc
                </when>
                <otherwise>
                    order by weight_rate desc
                </otherwise>
            </choose>
        </if>
    </select>

    <select id="totalIndustryCostList" resultMap="annualResultMap">
        <if test='granularityType == "U"'>
            select prod_rnd_team_cn_name,prod_rnd_team_code,parent_code,parent_cn_name,period_year,
            annual_amp,group_level,group_code, group_cn_name,
            weight_rate, status_code, append_year, 'T' AS cost_type,annual_amp * weight_rate/100 as
            weightAnnualAmpPercent,append_flag from
            (select distinct amp1.prod_rd_team_cn_name as prod_rnd_team_cn_name,amp1.prod_rnd_team_code,
            amp1.parent_code,amp1.parent_cn_name,amp1.period_year,amp1.group_level,
            ROUND(amp1.annual_amp*100,1) as annual_amp, amp1.group_code, amp1.group_cn_name,
            <choose>
                <when test='isMultipleSelect == true'>
                    weight.absolute_weight*100 as weight_rate,
                </when>
                <otherwise>
                    weight.weight_rate*100 as weight_rate,
                </otherwise>
            </choose>
            status.status_code as status_code,status.append_year,weight.append_flag
            from fin_dm_opt_foi.${tablePreFix}_total_annual_amp_t amp1
            left join fin_dm_opt_foi.${tablePreFix}_total_annual_weight_t weight
            on amp1.view_flag = weight.view_flag and amp1.group_code = weight.group_code
            and amp1.group_level = weight.group_level and amp1.PARENT_CODE = weight.PARENT_CODE
            and amp1.prod_rnd_team_code = weight.prod_rnd_team_code
            and amp1.caliber_flag = weight.caliber_flag and weight.cost_type = 'T'
            and amp1.version_id =weight.version_id and amp1.period_year = weight.period_year
            and amp1.oversea_flag = weight.oversea_flag
            and amp1.lv0_prod_list_code = weight.lv0_prod_list_code
            left join fin_dm_opt_foi.${tablePreFix}_total_annual_status_code_t status
            on amp1.group_code = status.group_code and amp1.group_level = status.group_level
            and amp1.period_year = status.period_year and amp1.version_id = status.version_id
            and amp1.view_flag = status.view_flag and amp1.prod_rnd_team_code = status.prod_rnd_team_code
            and amp1.caliber_flag = status.caliber_flag and amp1.PARENT_CODE = status.PARENT_CODE
            and amp1.oversea_flag = status.oversea_flag
            and amp1.lv0_prod_list_code = status.lv0_prod_list_code
        </if>
        <if test='granularityType == "P"'>
            select prod_rnd_team_cn_name,prod_rnd_team_code,parent_code,
            l1_name,l2_name,parent_cn_name,period_year,
            annual_amp,group_code,group_cn_name,group_level,
            weight_rate, status_code, append_year, 'T' AS cost_type,annual_amp * weight_rate/100 as
            weightAnnualAmpPercent,append_flag from
            (select distinct amp1.prod_rd_team_cn_name as
            prod_rnd_team_cn_name,amp1.prod_rnd_team_code,amp1.l1_name,amp1.l2_name,
            amp1.parent_code,amp1.parent_cn_name,amp1.period_year,amp1.group_level,
            ROUND(amp1.annual_amp*100,1) as annual_amp,amp1.group_code,amp1.group_cn_name,
            <choose>
                <when test='isMultipleSelect == true'>
                    weight.absolute_weight*100 as weight_rate,
                </when>
                <otherwise>
                    weight.weight_rate*100 as weight_rate,
                </otherwise>
            </choose>
            status.status_code as status_code,status.append_year,weight.append_flag
            from fin_dm_opt_foi.${tablePreFix}_total_pft_annual_amp_t amp1
            left join fin_dm_opt_foi.${tablePreFix}_total_pft_annual_weight_t weight
            on amp1.view_flag = weight.view_flag and amp1.group_code = weight.group_code
            and amp1.group_level = weight.group_level and amp1.PARENT_CODE = weight.PARENT_CODE
            and amp1.prod_rnd_team_code = weight.prod_rnd_team_code and weight.cost_type = 'T'
            and nvl ( amp1.l1_name, 'snull' ) = nvl ( weight.l1_name, 'snull' )
            and nvl ( amp1.l2_name, 'snull' ) = nvl ( weight.l2_name, 'snull' )
            and amp1.version_id =weight.version_id
            and amp1.period_year = weight.period_year
            and amp1.caliber_flag = weight.caliber_flag
            and amp1.oversea_flag = weight.oversea_flag
            and amp1.lv0_prod_list_code = weight.lv0_prod_list_code
            left join fin_dm_opt_foi.${tablePreFix}_total_pft_annual_status_code_t status
            on amp1.group_code = status.group_code and amp1.group_level = status.group_level
            and nvl ( amp1.l1_name, 'snull' ) = nvl ( status.l1_name, 'snull' )
            and nvl ( amp1.l2_name, 'snull' ) = nvl ( status.l2_name, 'snull' )
            and amp1.period_year = status.period_year and amp1.version_id = status.version_id
            and amp1.view_flag = status.view_flag and amp1.prod_rnd_team_code = status.prod_rnd_team_code
            and amp1.caliber_flag = status.caliber_flag and amp1.PARENT_CODE = status.PARENT_CODE
            and amp1.oversea_flag = status.oversea_flag
            and amp1.lv0_prod_list_code = status.lv0_prod_list_code
        </if>
        <if test='granularityType == "D"'>
            select prod_rnd_team_cn_name,prod_rnd_team_code,
            dimension_code,dimension_cn_name,
            dimension_subcategory_code,dimension_subcategory_cn_name,
            dimension_sub_detail_code,dimension_sub_detail_cn_name,spart_cn_name,spart_code,
            <if test='industryOrg == "ENERGY"'>
                coa_code,coa_cn_name,
            </if>
            parent_code,parent_cn_name,period_year, annual_amp,group_level,group_code,group_cn_name,
            weight_rate, status_code, append_year, 'T' AS cost_type,
            annual_amp * weight_rate/100 as weightAnnualAmpPercent,append_flag from
            (select distinct amp1.group_level,amp1.prod_rd_team_cn_name as
            prod_rnd_team_cn_name,amp1.prod_rnd_team_code,
            amp1.dimension_code,amp1.dimension_cn_name,
            amp1.dimension_subcategory_code,amp1.dimension_subcategory_cn_name,
            amp1.dimension_sub_detail_code,amp1.dimension_sub_detail_cn_name,amp1.spart_cn_name,amp1.spart_code,
            <if test='industryOrg == "ENERGY"'>
                amp1.coa_code,amp1.coa_cn_name,
            </if>
            amp1.parent_code,amp1.parent_cn_name,amp1.period_year,
            ROUND(amp1.annual_amp*100,1) as annual_amp,amp1.group_code,amp1.group_cn_name,
            <choose>
                <when test='isMultipleSelect == true'>
                    weight.absolute_weight*100 as weight_rate,
                </when>
                <otherwise>
                    weight.weight_rate*100 as weight_rate,
                </otherwise>
            </choose>
            status.status_code as status_code,status.append_year,weight.append_flag
            from fin_dm_opt_foi.${tablePreFix}_total_dms_annual_amp_t amp1
            left join fin_dm_opt_foi.${tablePreFix}_total_dms_annual_weight_t weight
            on amp1.view_flag = weight.view_flag and weight.cost_type = 'T'
            and amp1.group_code = weight.group_code
            and amp1.group_level = weight.group_level and amp1.PARENT_CODE = weight.PARENT_CODE
            and amp1.prod_rnd_team_code = weight.prod_rnd_team_code
            and amp1.caliber_flag = weight.caliber_flag and amp1.version_id = weight.version_id
            and amp1.period_year = weight.period_year and amp1.oversea_flag = weight.oversea_flag
            and amp1.lv0_prod_list_code = weight.lv0_prod_list_code
            AND nvl(amp1.dimension_code,'snull') = nvl( weight.dimension_code,'snull')
            AND nvl(amp1.dimension_subcategory_code,'snull') = nvl( weight.dimension_subcategory_code,'snull')
            AND nvl(amp1.dimension_sub_detail_code,'snull') = nvl( weight.dimension_sub_detail_code,'snull')
            AND nvl(amp1.spart_code,'snull') = nvl( weight.spart_code,'snull')
            <if test='industryOrg == "ENERGY"'>
                and nvl(amp1.coa_code,'snull') = nvl( weight.coa_code,'snull')
            </if>
            left join fin_dm_opt_foi.${tablePreFix}_total_dms_annual_status_code_t status
            on amp1.group_code = status.group_code and amp1.group_level = status.group_level
            and amp1.period_year = status.period_year and amp1.version_id = status.version_id
            and amp1.view_flag = status.view_flag and amp1.prod_rnd_team_code = status.prod_rnd_team_code
            and amp1.caliber_flag = status.caliber_flag and amp1.PARENT_CODE = status.PARENT_CODE
            and amp1.oversea_flag = status.oversea_flag and amp1.lv0_prod_list_code = status.lv0_prod_list_code
            AND nvl(amp1.dimension_code,'snull') = nvl( status.dimension_code,'snull')
            AND nvl(amp1.dimension_subcategory_code,'snull') = nvl( status.dimension_subcategory_code,'snull')
            AND nvl(amp1.dimension_sub_detail_code,'snull') = nvl( status.dimension_sub_detail_code,'snull')
            AND nvl(amp1.spart_code,'snull') = nvl( status.spart_code,'snull')
            <if test='industryOrg == "ENERGY"'>
                and nvl(amp1.coa_code,'snull') = nvl( status.coa_code,'snull')
            </if>
        </if>
        where amp1.del_flag = 'N'
        <if test='groupLevel != null and groupLevel != ""'>
            and amp1.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and amp1.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='caliberFlag != null and caliberFlag != ""'>
            and amp1.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag != null and overseaFlag != ""'>
            and amp1.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='lv0ProdListCode != null and lv0ProdListCode != ""'>
            and amp1.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionCodeList != null and dimensionCodeList.size() > 0'>
            <foreach collection='dimensionCodeList' item="code" open="AND amp1.dimension_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionSubcategoryCodeList != null and dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='dimensionSubcategoryCodeList' item="code"
                     open="AND amp1.dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionSubDetailCodeList != null and dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='dimensionSubDetailCodeList' item="code" open="AND amp1.dimension_sub_detail_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='spartCodeList != null and spartCodeList.size() > 0'>
            <foreach collection='spartCodeList' item="code" open="AND amp1.spart_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='coaCodeList != null and coaCodeList.size() > 0'>
            <foreach collection='coaCodeList' item="code" open="AND amp1.coa_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='l1NameList != null and l1NameList.size() > 0'>
            <foreach collection='l1NameList' item="code" open="AND amp1.l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='l2NameList != null and l2NameList.size() > 0'>
            <foreach collection='l2NameList' item="code" open="AND amp1.l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='parentCodeList != null and parentCodeList.size() > 0'>
            <foreach collection='parentCodeList' item="code" open="AND amp1.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamCodeList != null and teamCodeList.size() > 0'>
            <foreach collection='teamCodeList' item="code" open="AND amp1.prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='versionId != null'>
            and amp1.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='periodYear != null and periodYear !=""'>
            and amp1.period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='year != null and year !=""'>
            and amp1.period_year = #{year,jdbcType=VARCHAR}
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND amp1.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        <if test='subGroupCodeList != null and subGroupCodeList.size() > 0'>
            <foreach collection='subGroupCodeList' item="code" open="AND amp1.group_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel =="LV1" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND amp1.prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel =="LV2" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND amp1.prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        )
        order by period_year,weight_rate desc,group_cn_name
    </select>

    <select id="getTotalAnnualPeriodYear" resultType="java.lang.String">
        select distinct period_year
        from fin_dm_opt_foi.${tablePreFix}_total_annual_amp_t where version_id = #{versionId,jdbcType=NUMERIC}
        order by period_year desc limit 3
    </select>
</mapper>