/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.config;

import com.huawei.it.fcst.industry.index.constant.CommonConstant;
import com.huawei.it.fcst.industry.index.constant.Constant;
import com.huawei.it.fcst.industry.index.dao.IDmDimCatgModlCegIctDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocCatgCegIctDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocVersionDao;
import com.huawei.it.fcst.industry.index.enums.IndustryConst;
import com.huawei.it.fcst.industry.index.enums.ResultCodeEnum;
import com.huawei.it.fcst.industry.index.impl.relation.ConfigDimensionManageService;
import com.huawei.it.fcst.industry.index.service.config.IConfigDimensionService;
import com.huawei.it.fcst.industry.index.utils.UserInfoUtils;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.common.TableNameVO;
import com.huawei.it.fcst.industry.index.vo.config.BackDimensionDTO;
import com.huawei.it.fcst.industry.index.vo.config.DimensionParamVO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocCatgCegIctDTO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoDTO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoVO;
import com.huawei.it.fcst.industry.index.vo.relation.BackDimensionVO;
import com.huawei.it.fcst.industry.index.vo.relation.DimensionInputVO;
import com.huawei.it.fcst.industry.index.vo.relation.DmDimMaterialCodeVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.core.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.inject.Inject;
import javax.inject.Named;

/**
 * ConfigDimensionService Class
 *
 * <AUTHOR>
 * @since 2023/3/13
 */
@Slf4j
@Named("configDimensionService")
@JalorResource(code = "configDimensionService", desc = "配置管理维护维表服务")
public class ConfigDimensionService implements IConfigDimensionService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ConfigDimensionService.class);

    @Inject
    private IDmFocVersionDao dmFocVersionDao;

    @Inject
    private IDmFocCatgCegIctDao dmFocCatgCegIctDao;

    @Inject
    private IDmDimCatgModlCegIctDao dmDimCatgModlCegIctDao;

    @Inject
    private IDmDimCatgModlCegIctDao iDmDimCatgModlCegIctDao;

    @Inject
    private ConfigDimensionManageService configDimensionManageService;

    @Override
    @JalorOperation(code = "findVersion", desc = "查询版本信息")
    public ResultDataVO findVersion(DmFocVersionInfoVO dmFocVersionInfoVo) {
        LOGGER.info("Begin ConfigManageService::findVersion");
        dmFocVersionInfoVo.setTablePreFix(TableNameVO.getTablePreFix(dmFocVersionInfoVo.getIndustryOrg()));
        if (!IndustryConst.DataType.DIM.getValue().equals(dmFocVersionInfoVo.getDataType())) {
            dmFocVersionInfoVo.setStatus(IndustryConst.STATUS.IS_STATUS.getValue());
        }
        return ResultDataVO.success(dmFocVersionDao.findDmFocVersionList(dmFocVersionInfoVo));
    }

    @Override
    @JalorOperation(code = "getListByVersionId", desc = "查询专项采购认证部下拉框")
    public ResultDataVO getListByVersionId(String industryOrg, Long versionId, String l3CegCode) {
        LOGGER.info("Begin ConfigDimensionService::getListByVersionId and params: versionId={}", versionId);
        String tablePreFix = TableNameVO.getTablePreFix(industryOrg);
        List<DmFocCatgCegIctDTO> listByVersionId = dmFocCatgCegIctDao.getL3ListByVersionId(tablePreFix, versionId, l3CegCode);
        listByVersionId = Optional.ofNullable(listByVersionId).orElse(new ArrayList<>());
        return ResultDataVO.success(listByVersionId);
    }

    @Override
    @JalorOperation(code = "getShortCnNameAndCode", desc = "新增编辑下获取专家团编码和简称")
    public ResultDataVO getShortCnNameAndCode(String industryOrg, String keyword, String l3CegCode) {
        String tablePreFix = TableNameVO.getTablePreFix(industryOrg);
        // 获取最新的版本ID
        DmFocVersionInfoDTO versionInfoDTO = new DmFocVersionInfoDTO();
        versionInfoDTO.setDataType(IndustryConst.DataType.DIM.getValue());
        versionInfoDTO.setTablePreFix(tablePreFix);
        List<DmFocVersionInfoDTO> dmFocVersionDTOList = dmFocVersionDao.findDmFocVersionDTOList(versionInfoDTO);
        // 关键字（keyword）查询专家团下拉框
        DimensionParamVO build = DimensionParamVO.builder()
                .tablePreFix(tablePreFix)
                .keyword(keyword)
                .l3CegCode(l3CegCode)
                .versionId(dmFocVersionDTOList.get(0).getVersionId())
                .build();
        if(StringUtils.isEmpty(l3CegCode)) {
            build.setCegLevel("3");
        }else {
            build.setCegLevel("4");
        }
        List<DmFocCatgCegIctDTO> l3OrL4ListByKeyword = dmFocCatgCegIctDao.findL3ListByKeyword(build);
        // 添加其他项到专家团/模块
        if (StringUtils.isEmpty(keyword) || IndustryConst.OtherCeg.NAME.getValue().equals(keyword)) {
            DmFocCatgCegIctDTO otherBuild = DmFocCatgCegIctDTO.builder()
                    .l3CegCode(IndustryConst.OtherCeg.CODE.getValue())
                    .l3CegCnName(IndustryConst.OtherCeg.NAME.getValue())
                    .l3CegShortCnName(IndustryConst.OtherCeg.NAME.getValue())
                    .l4CegCode(IndustryConst.OtherCeg.CODE.getValue())
                    .l4CegCnName(IndustryConst.OtherCeg.NAME.getValue())
                    .l4CegShortCnName(IndustryConst.OtherCeg.NAME.getValue())
                    .build();
            l3OrL4ListByKeyword.add(otherBuild);
        }
        l3OrL4ListByKeyword = Optional.ofNullable(l3OrL4ListByKeyword).orElse(new ArrayList<>());
        return ResultDataVO.success(l3OrL4ListByKeyword);
    }

    @Override
    @JalorOperation(code = "getCategoryCodeByName", desc = "新增编辑下获取品类名称和编码")
    public ResultDataVO getCategoryCodeByName(String keyword, String industryOrg) {
        // 获取最新的版本ID
        DmFocVersionInfoDTO versionInfoDTO = new DmFocVersionInfoDTO();
        versionInfoDTO.setDataType(IndustryConst.DataType.DIM.getValue());
        // 关键字（keyword）查询品类下拉框
        DmDimMaterialCodeVO build = DmDimMaterialCodeVO.builder().keyword(keyword).build();
        List<DmDimMaterialCodeVO> resultList = dmDimCatgModlCegIctDao.findDimMaterialCodeD(build);
        List<Map> dmFocCatgCegIctDTOS = iDmDimCatgModlCegIctDao.findCatgCegIctList(TableNameVO.getTablePreFix(industryOrg));
        resultList.stream().forEach(dmDimMaterialCodeVO -> {
            for (Map map : dmFocCatgCegIctDTOS) {
                if(map.get("category_code").equals(dmDimMaterialCodeVO.getItemSubtypeCode())) {
                    String cnName = (String) map.get("category_cn_name");
                    dmDimMaterialCodeVO.setItemSubtypeCnName(cnName);
                }
            }
        });
        resultList = Optional.ofNullable(resultList).orElse(new ArrayList<>());
        return ResultDataVO.success(resultList);
    }

    @Override
    @JalorOperation(code = "relationList", desc = "映射关系维表查询接口")
    public ResultDataVO relationList(DimensionParamVO dimensionParamVO) throws CommonApplicationException {
        LOGGER.info("Begin ConfigDimensionService::relationList");
        if (null == dimensionParamVO.getVersionId()) {
            throw new CommonApplicationException("入参维度版本为空");
        }
        String tablePreFix = TableNameVO.getTablePreFix(dimensionParamVO.getIndustryOrg());
        dimensionParamVO.setTablePreFix(tablePreFix);
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(dimensionParamVO.getPageSize());
        pageVO.setCurPage(dimensionParamVO.getPageIndex());
        dimensionParamVO.setDelFlag(CommonConstant.IS_NOT);
        PagedResult<DmFocCatgCegIctDTO> byPage = dmFocCatgCegIctDao.findByPage(dimensionParamVO, pageVO);

        Map map = new LinkedHashMap();
        map.put("result", byPage.getResult());
        map.put("pageVO", byPage.getPageVO());
        return ResultDataVO.success(map);
    }

    @Override
    @JalorOperation(code = "dimensionUpdate", desc = "映射关系维表刷新接口")
    @Audit(module = "configDimensionService-dimensionUpdate", operation = "dimensionUpdate",
            message = "映射关系维表刷新接口")
    public ResultDataVO dimensionUpdate(DimensionInputVO dimensionInputVO) throws CommonApplicationException {
        LOGGER.info("Begin ConfigDimensionService::dimensionUpdate");
        if (null == dimensionInputVO.getVersionId()) {
            throw new CommonApplicationException("维度版本为空！");
        }
        String tablePreFix = dimensionInputVO.getTablePreFix();
        DmFocVersionInfoDTO versionVOById = dmFocVersionDao.findDmFocVersionDTOById(dimensionInputVO.getVersionId(), tablePreFix);
        if (IndustryConst.STATUS.IS_STATUS.getValue().equals(versionVOById.getStatus())
                && CollectionUtils.isEmpty(dimensionInputVO.getDimCatgModlCegIctList())) {
            return ResultDataVO.failure(ResultCodeEnum.FAILURE);
        }
        if (CollectionUtils.isEmpty(dimensionInputVO.getDimCatgModlCegIctList())) {
            updateVersionDTO(dimensionInputVO, versionVOById);
        } else {
            ResultDataVO resultDataVO = configDimensionManageService.relationSave(dimensionInputVO);
            BackDimensionVO backDimensionVO = (BackDimensionVO) resultDataVO.getData();
            DmFocVersionInfoDTO versionVO = new DmFocVersionInfoDTO();
            versionVO.setVersionId(backDimensionVO.getDmFocPlanVersionVO().getVersionId());
            versionVO.setStatus(IndustryConst.STATUS.IS_STATUS.getValue());
            versionVO.setTablePreFix(tablePreFix);
            dmFocVersionDao.updateDmFocVersionDTO(versionVO);
        }
        return ResultDataVO.success(ResultCodeEnum.SUCCESS);
    }

    private void updateVersionDTO(DimensionInputVO dimensionInputVO, DmFocVersionInfoDTO versionVOById) {
        versionVOById.setTablePreFix(dimensionInputVO.getTablePreFix());
        DmFocVersionInfoDTO versionVO = new DmFocVersionInfoDTO();
        versionVO.setStatus(IndustryConst.STATUS.IS_STATUS.getValue());
        versionVO.setVersionId(dimensionInputVO.getVersionId());
        versionVO.setTablePreFix(dimensionInputVO.getTablePreFix());
        versionVO.setLastUpdateDate(new Date());
        versionVO.setLastUpdatedBy(UserInfoUtils.getUserId());
        String newVersionName = getNewVersionName(versionVOById);
        versionVO.setVersion(newVersionName);
        dmFocVersionDao.updateDmFocVersionDTO(versionVO);
    }

    @NotNull
    private String getNewVersionName(DmFocVersionInfoDTO foiPlanVersionVO) {
        String oldVersionName = foiPlanVersionVO.getVersion();
        String newVersionName = "";
        String dateString = DateUtil.today().replace("-", "");

        if (StrUtil.isNotEmpty(oldVersionName)) {
            newVersionName = getCurrentVersionName(oldVersionName, dateString,foiPlanVersionVO.getTablePreFix());
        } else {
            newVersionName = dateString + "-001";
        }
        return newVersionName;
    }

    @NotNull
    private String getCurrentVersionName(String oldVersionName, String dateString,String tablePreFix) {
        String newVersionName;
        String[] split = oldVersionName.split("-");
        if (StrUtil.isNotEmpty(split[0]) && split[0].equals(dateString)) {
            newVersionName = dateString + "-" + split[1];
        } else {
            DmFocVersionInfoDTO versionVo = new DmFocVersionInfoDTO();
            versionVo.setLastUpdateStr(DateUtil.today());
            versionVo.setDataType(IndustryConst.DataType.DIM.getValue());
            versionVo.setTablePreFix(tablePreFix);
            List<DmFocVersionInfoDTO> versionVoLists = dmFocVersionDao.findDmFocVersionDTOList(versionVo);
            if (CollectionUtils.isNotEmpty(versionVoLists)) {
                newVersionName = configDimensionManageService.getVersionNameSub(versionVoLists.get(0).getVersion(), dateString);
            } else {
                newVersionName = dateString + "-001";
            }
        }
        return newVersionName;
    }

    @Override
    @JalorOperation(code = "getSystemTime", desc = "获取系统时间")
    public ResultDataVO getSystemTime() {
        return ResultDataVO.success(new Date().getTime());
    }

    @Override
    @JalorOperation(code = "relationDelete", desc = "维度关系删除功能")
    @Audit(module = "configDimensionService-relationDelete", operation = "relationDelete",
            message = "维度关系删除功能")
    public ResultDataVO relationDelete(List<DmFocCatgCegIctDTO> dmFocCatgCegIctDTOList) throws CommonApplicationException {
        LOGGER.info("Begin ConfigDimensionService::relationDelete");
        if (!CollectionUtil.isNullOrEmpty(dmFocCatgCegIctDTOList) && dmFocCatgCegIctDTOList.size() > com.huawei.it.fcst.constant.Constant.IntegerEnum.MAX_SIZE.getValue()) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR, "操作的数据过多！");
        }
        BackDimensionDTO backDimensionDTO = new BackDimensionDTO();
        String tablePreFix = TableNameVO.getTablePreFix(dmFocCatgCegIctDTOList.get(0).getIndustryOrg());
        for (DmFocCatgCegIctDTO dmFocCatgCegIctDTO : dmFocCatgCegIctDTOList) {
            dmFocCatgCegIctDTO.setTablePreFix(tablePreFix);
            if (null == dmFocCatgCegIctDTO.getVersionId()) {
                throw new CommonApplicationException("维度版本为空！");
            }
            if (StrUtil.isEmpty(dmFocCatgCegIctDTO.getCategoryCnName()) || StrUtil.isEmpty(dmFocCatgCegIctDTO.getL3CegCnName())) {
                throw new CommonApplicationException("专项采购认证部和品类名称不能为空！");
            }
        }
        // 获取系统版本的刷新状态
        DmFocVersionInfoVO build = DmFocVersionInfoVO.builder()
                .dataType(IndustryConst.DataType.DIM.getValue())
                .status(IndustryConst.STATUS.NOT_STATUS.getValue())
                .tablePreFix(tablePreFix)
                .build();
        List<DmFocVersionInfoDTO> versionList = dmFocVersionDao.findDmFocVersionList(build);
        // 校验入参版本和系统版本是否一致
        DmFocVersionInfoDTO versionInfoDTO = checkVersionRefreshStatus(versionList, dmFocCatgCegIctDTOList);
        backDimensionDTO.setDmFocVersionInfoDTO(versionInfoDTO);

        dmFocCatgCegIctDTOList.stream().forEach(dmFocCatgCegIctDTO -> {
            dmFocCatgCegIctDTO.setVersionId(versionInfoDTO.getVersionId());
            dmFocCatgCegIctDTO.setDelFlag(CommonConstant.IS_YES);
            dmFocCatgCegIctDTO.setLastUpdatedBy(UserInfoUtils.getUserId());
            dmFocCatgCegIctDTO.setLastUpdatedDate(new Date());
        });
        // 删除目标数据
        dmFocCatgCegIctDao.updateDmFocCatgCegIctList(dmFocCatgCegIctDTOList);
        return ResultDataVO.success(backDimensionDTO);
    }

    private DmFocVersionInfoDTO checkVersionRefreshStatus(List<DmFocVersionInfoDTO> versionList, List<DmFocCatgCegIctDTO> dmFocCatgCegIctDTOList) {
        // 系统存在未刷新版本
        String industryOrg = dmFocCatgCegIctDTOList.get(0).getIndustryOrg();
        String tablePreFix = TableNameVO.getTablePreFix(industryOrg);
        if (CollectionUtils.isNotEmpty(versionList)) {
            DmFocVersionInfoDTO dmFocVersionInfoDTO = versionList.get(0);
            dmFocVersionInfoDTO.setTablePreFix(tablePreFix);
            // 获取未刷新版本，校验未刷新的版本与当前入参版本是否一致
            Long notRefreshVersionId = dmFocVersionInfoDTO.getVersionId();
            Long paramVersionId = dmFocCatgCegIctDTOList.get(0).getVersionId();
            if (!notRefreshVersionId.equals(paramVersionId)) {
                // 版本不一致，组装数据后保存数据
                List<DmFocCatgCegIctDTO> combineDataList = combineDataForSave(notRefreshVersionId,paramVersionId,tablePreFix);
                // 暂时清空未刷新的所有数据
                iDmDimCatgModlCegIctDao.deleteDmDimCatgModlCegIctT(notRefreshVersionId,tablePreFix);
                // 版本不一致 将组合数据填充到未刷新版本内
                dmFocCatgCegIctDao.createDmFocCatgCegIctLists(combineDataList,tablePreFix);
            }
            setNewVersionName(dmFocVersionInfoDTO);
            return dmFocVersionInfoDTO;
        } else {
            // 系统不存在未刷新版本，新建版本
            DmFocVersionInfoDTO newVersion = createVersionVO(tablePreFix);
            // 获取已刷新版本对应的维度数据
            List<DmFocCatgCegIctDTO> cegIctStatus = dmFocCatgCegIctDao.findDmFocCatgCegIctStatus(tablePreFix, dmFocCatgCegIctDTOList.get(0).getVersionId());
            // 维度数据插入到新版本里
            cegIctStatus.stream().forEach(dmFocCatgCegIctDTO ->
                dmFocCatgCegIctDTO.setVersionId(newVersion.getVersionId()));
            dmFocCatgCegIctDao.createDmFocCatgCegIctLists(cegIctStatus,tablePreFix);
            return newVersion;
        }
    }

    private List<DmFocCatgCegIctDTO> combineDataForSave(Long notRefreshVersionId, Long paramVersionId,String tablePreFix) {
        List<DmFocCatgCegIctDTO> combineDataList = new ArrayList<>();
        List<DmFocCatgCegIctDTO> notRefreshListWithVersion = dmFocCatgCegIctDao.findDmFocCatgCegIctStatus(tablePreFix, notRefreshVersionId);
        List<DmFocCatgCegIctDTO> paramListWithVersion = dmFocCatgCegIctDao.findDmFocCatgCegIctStatus(tablePreFix, paramVersionId);
        // 未刷新版本数据统一两版本不一致的简称
        notRefreshListWithVersion.stream().forEach(dmDimCatgModlCegIctVO -> {
            for (DmFocCatgCegIctDTO paramVO : paramListWithVersion) {
                if (paramVO.getL3CegCode().equals(dmDimCatgModlCegIctVO.getL3CegCode())
                        && !paramVO.getL3CegShortCnName().equals(dmDimCatgModlCegIctVO.getL3CegShortCnName())) {
                    dmDimCatgModlCegIctVO.setL3CegShortCnName(paramVO.getL3CegShortCnName());
                }
                if (paramVO.getL4CegCode().equals(dmDimCatgModlCegIctVO.getL4CegCode())
                        && !paramVO.getL4CegShortCnName().equals(dmDimCatgModlCegIctVO.getL4CegShortCnName())) {
                    dmDimCatgModlCegIctVO.setL4CegShortCnName(paramVO.getL4CegShortCnName());
                }
            }
        });
        // 筛选出两版本相同品类的VO，并从未刷新版本中剔除掉，剩下不同品类的VO，存放进入参版本数据中
        for (DmFocCatgCegIctDTO dmDimCatgModlCegIctVO : paramListWithVersion) {
            for (DmFocCatgCegIctDTO cegIctVO : notRefreshListWithVersion) {
                if (dmDimCatgModlCegIctVO.getCategoryCode().equals(cegIctVO.getCategoryCode())) {
                    combineDataList.add(cegIctVO);
                }
            }
        }
        List<DmFocCatgCegIctDTO> collect = combineDataList.stream().distinct().collect(Collectors.toList());
        notRefreshListWithVersion.removeAll(collect);
        // 组合两个版本数据到入参版本
        paramListWithVersion.addAll(notRefreshListWithVersion);
        setDimCatgCegIctVO(paramListWithVersion, notRefreshVersionId);
        return paramListWithVersion;
    }

    private void setDimCatgCegIctVO(List<DmFocCatgCegIctDTO> paramListWithVersion, Long notRefreshVersionId) {
        // 循环集合设置版本信息
        paramListWithVersion.stream().forEach(dto -> {
            dto.setVersionId(notRefreshVersionId);
            dto.setLastUpdatedDate(new Date());
            dto.setLastUpdatedBy(UserInfoUtils.getUserId());
        });
    }

    private void setNewVersionName(DmFocVersionInfoDTO dmFocVersionInfoDTO) {
        // 更新版本名称
        String newVersionName = getNewVersionName(dmFocVersionInfoDTO);
        dmFocVersionInfoDTO.setVersion(newVersionName);
        dmFocVersionInfoDTO.setLastUpdatedBy(UserInfoUtils.getUserId());
        dmFocVersionInfoDTO.setLastUpdateDate(new Date());
        dmFocVersionInfoDTO.setIsRunning(CommonConstant.IS_NOT);
        dmFocVersionDao.updateDmFocVersionDTO(dmFocVersionInfoDTO);
    }

    private DmFocVersionInfoDTO createVersionVO(String tablePreFix) {
        DmFocVersionInfoDTO versionInfoDTO = new DmFocVersionInfoDTO();
        versionInfoDTO.setVersionId(dmFocVersionDao.getVersionKey(tablePreFix));
        versionInfoDTO.setVersion(generateVersionName(tablePreFix));
        versionInfoDTO.setVersionType(IndustryConst.VersionType.ADJUST.getValue());
        versionInfoDTO.setStatus(IndustryConst.STATUS.NOT_STATUS.getValue());
        versionInfoDTO.setDataType(IndustryConst.DataType.DIM.getValue());
        versionInfoDTO.setCreatedBy(UserInfoUtils.getUserId());
        versionInfoDTO.setCreationDate(new Date());
        versionInfoDTO.setLastUpdatedBy(UserInfoUtils.getUserId());
        versionInfoDTO.setLastUpdateDate(new Date());
        versionInfoDTO.setDelFlag(CommonConstant.IS_NOT);
        versionInfoDTO.setIsRunning(CommonConstant.IS_NOT);
        versionInfoDTO.setTablePreFix(tablePreFix);
        dmFocVersionDao.createDmFocVersionDTO(versionInfoDTO);
        return versionInfoDTO;
    }

    public String generateVersionName(String tablePreFix) {
        DmFocVersionInfoDTO versionDto = new DmFocVersionInfoDTO();
        versionDto.setLastUpdateStr(DateUtil.today());
        versionDto.setDataType(IndustryConst.DataType.DIM.getValue());
        versionDto.setTablePreFix(tablePreFix);
        List<DmFocVersionInfoDTO> versionMap = dmFocVersionDao.findDmFocVersionDTOList(versionDto);
        return configDimensionManageService.getVersionName(versionMap);
    }

    @Override
    @JalorOperation(code = "getSyncDimPurchar", desc = "同步映射维表数据")
    @Audit(module = "configDimensionService-getSyncDimPurchar", operation = "getSyncDimPurchar",
        message = "同步映射维表数据")
    public ResultDataVO getSyncDimPurchar(String industryOrg) throws CommonApplicationException {
        LOGGER.info(">>>Begin ConfigDimensionService::getSyncDimPurchar");
        String result = dmFocVersionDao.syncDimPurcharFunction(industryOrg);
        if (Constant.StrEnum.DATA_UNMODIFIED.getValue().equals(result)) {
            return ResultDataVO.success(Constant.StrEnum.DATA_UNMODIFIED.getValue());
        }
        if (!(Constant.StrEnum.SUCCESS.getValue().equalsIgnoreCase(result))) {
            throw new CommonApplicationException(Constant.StrEnum.DATA_SYNC_FAIL.getValue());
        }
        return ResultDataVO.success(result);
    }


    @Override
    @JalorOperation(code = "syncDimPurchar", desc = "1-8号0点同步映射维表数据")
    @Audit(module = "configDimensionService-syncDimPurchar", operation = "syncDimPurchar", message = "1-8号0点同步映射维表数据")
    public void syncDimPurchar() {
        log.info(">>>>>>Start to execute the syncDimPurcharFunction.<<<<<<");
        String resIct = dmFocVersionDao.syncDimPurcharFunction(IndustryConst.INDUSTRY_ORG_FUNC.I.getValue());
        String resEnergy = dmFocVersionDao.syncDimPurcharFunction(IndustryConst.INDUSTRY_ORG_FUNC.E.getValue());
        String resIas = dmFocVersionDao.syncDimPurcharFunction(IndustryConst.INDUSTRY_ORG_FUNC.IAS.getValue());
        log.info(">>>result resIct: {}", resIct);
        log.info(">>>result resEnergy: {}", resEnergy);
        log.info(">>>result resIas: {}", resIas);
        log.info(">>>>>>>>>The syncDimPurcharFunction is executed successfully.<<<<<<<<<");
    }
}
