/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.month.DmFocActualCostVO;
import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * The DAO to access DmFocActualCostT entity
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2023-03-10 11:16:09
 */
public interface IDmFocTotalActualCostDao {
    /**
     * 分层级查询热力图的name排序
     * @param monthAnalysisVO 参数
     * @return List<String>
     */
    List<DmFocActualCostVO> findTotalGroupCnNameByCode(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);

    /**
     * 分层级查询热力图的name排序
     * @param monthAnalysisVO 参数
     * @return List<String>
     */
    List<DmFocActualCostVO> findTotalMutilGroupCnNameByCode(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);


    /**
     * 月度分析页面-热力图的code排序
     * @param monthAnalysisVO 参数
     * @return String
     */
    String findTotalGroupCodeOrder(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);

    /**
     * 查询月度分析-热力图
     * @param monthAnalysisVO 参数
     * @return List<DmFocActualCostVO>
     */
    List<DmFocActualCostVO> findTotalActualCostAmtList(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);

    /**
     * 查询月度分析-热力图导出数据
     * @param monthAnalysisVO 参数
     * @return List<DmFocActualCostVO>
     */
    List<DmFocActualCostVO> findHeapMapExpData(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);

}
