/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.annual;

import com.alibaba.fastjson.JSONObject;
import com.huawei.it.fcst.industry.index.cache.IndustryCacheHandler;
import com.huawei.it.fcst.industry.index.cache.IndustryGlobalParameterUtil;
import com.huawei.it.fcst.industry.index.constant.Constant;
import com.huawei.it.fcst.industry.index.dao.IAnnualAmpDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocDataRefreshStatusDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocDmsViewInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeDmsViewInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadePftViewInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeViewInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocPftViewInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTotalDmsViewInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTotalPftViewInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTotalViewInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocVersionDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocViewInfoDao;
import com.huawei.it.fcst.industry.index.dao.IMadeAnnualAmpDao;
import com.huawei.it.fcst.industry.index.dao.ITotalAnnualAmpDao;
import com.huawei.it.fcst.industry.index.enums.IndustryIndexEnum;
import com.huawei.it.fcst.industry.index.impl.common.CommonService;
import com.huawei.it.fcst.industry.index.impl.iauth.DataDimensionService;
import com.huawei.it.fcst.industry.index.utils.TestUtils;
import com.huawei.it.fcst.industry.index.vo.combination.DmFocDataRefreshStatus;
import com.huawei.it.fcst.industry.index.vo.common.CommonViewVO;
import com.huawei.it.fcst.industry.index.vo.common.DataPermissionsVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoDTO;
import com.huawei.it.fcst.industry.index.vo.view.ViewInfoVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.lookup.LookupItemVO;
import com.huawei.it.jalor5.lookup.service.ILookupItemQueryService;
import com.huawei.it.jalor5.registry.service.IRegistryQueryService;
import com.huawei.it.jalor5.security.RoleVO;
import com.huawei.it.jalor5.security.UserVO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.verify;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * AnnualCommonServiceTest Class
 *
 * <AUTHOR>
 * @since 2023/4/13
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest( {IndustryGlobalParameterUtil.class})
public class AnnualCommonServiceTest {

    private static final Logger LOGGER = LogManager.getLogger(AnnualCommonServiceTest.class);

    @InjectMocks
    private AnnualCommonService annualCommonService;

    @Mock
    private IDmFocViewInfoDao dmFocViewInfoDao;

    @Mock
    private IDmFocVersionDao dmFocVersionDao;

    @Mock
    private IRegistryQueryService registryService;

    @Mock
    private CommonService commonService;

    @Mock
    private DataDimensionService dataDimensionService;

    @Mock
    private IDmFocPftViewInfoDao dmFocPftViewInfoDao;

    @Mock
    private IDmFocDmsViewInfoDao dmFocDmsViewInfoDao;

    @Mock
    private IAnnualAmpDao annualAmpDao;

    @Mock
    private IDmFocDataRefreshStatusDao dataRefreshStatusDao;

    @Mock
    private ILookupItemQueryService lookupItemQueryService;

    private JSONObject viewInfoJson;

    @Mock
    private IMadeAnnualAmpDao madeAnnualAmpDao;

    @Mock
    private ITotalAnnualAmpDao totalAnnualAmpDao;

    @Mock
    private IDmFocMadeViewInfoDao dmFocMadeViewInfoDao;

    @Mock
    private IDmFocTotalViewInfoDao dmFocTotalViewInfoDao;

    @Mock
    private IDmFocMadePftViewInfoDao dmFocMadePftViewInfoDao;

    @Mock
    private IDmFocTotalPftViewInfoDao dmFocTotalPftViewInfoDao;

    @Mock
    private IDmFocMadeDmsViewInfoDao dmFocMadeDmsViewInfoDao;

    @Mock
    private IDmFocTotalDmsViewInfoDao dmFocTotalDmsViewInfoDao;

    @Mock
    private IndustryCacheHandler industryCacheHandler;

    @Before
    public void before() throws Exception {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RoleVO role = new RoleVO();
        role.setRoleName("admin");
        role.setRoleId(1000);
        user.setCurrentRole(role);
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);
        viewInfoJson = TestUtils.getTestArg(
                "/com/huawei/it/fcst/industry/index/impl/annual/AnnualCommonService/viewInfoList.json");
    }

    @Test
    public void viewInfoList() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setPageFlag("monthPage");
        commonViewVO.setKeyWord("11");
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setGranularityType("U");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setCostType("M");
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        Long versionId = 20L;
        PowerMockito.doReturn(threeYears).when(madeAnnualAmpDao).getMadeAnnualPeriodYear(commonViewVO.getTablePreFix(),versionId);
        DmFocVersionInfoDTO  versionInfoDTO = new  DmFocVersionInfoDTO();
        versionInfoDTO.setVersionId(285L);
        PowerMockito.doReturn(versionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        PowerMockito.doReturn(286L).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList2Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setPageFlag("monthPage");
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setGranularityType("U");
        commonViewVO.setViewFlag("1");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("1");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("1");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList3Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setPageFlag("monthPage");
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setGranularityType("U");
        commonViewVO.setViewFlag("1");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv2DimensionSet  = new HashSet<>();
        lv2DimensionSet.add("12324");
        currentRoleDataPermission.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("1");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("1");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList4Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setPageFlag("monthPage");
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setGranularityType("U");
        commonViewVO.setViewFlag("1");
        commonViewVO.setPermissionTag(false);
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(any());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv2DimensionSet  = new HashSet<>();
        lv2DimensionSet.add("153324");
        currentRoleDataPermission.setLv2DimensionSet(lv2DimensionSet);
        Set<String> lv3DimensionSet  = new HashSet<>();
        lv3DimensionSet.add("140701");
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("1");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("1");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }
    @Test
    public void viewInfoList41Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setPageFlag("monthPage");
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setGranularityType("U");
        commonViewVO.setViewFlag("2");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("153324");
        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("2");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("2");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList42Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setPageFlag("monthPage");
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setGranularityType("U");
        commonViewVO.setViewFlag("1");
        commonViewVO.setPermissionTag(false);
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("153324");
        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("1");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("1");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList5Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setPageFlag("monthPage");
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setGranularityType("U");
        commonViewVO.setViewFlag("2");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        commonViewVO.setPermissionTag(true);
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv2DimensionSet  = new HashSet<>();
        lv2DimensionSet.add("153324");
        currentRoleDataPermission.setLv2DimensionSet(lv2DimensionSet);
        Set<String> lv3DimensionSet  = new HashSet<>();
        lv3DimensionSet.add("140701");
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("2");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("2");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv2ProdVOList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setLv1ProdRndTeamCode("134557");
        dmFocViewInfoVO.setLv1ProdRdTeamCnName("光");
        dmFocViewInfoVO.setGroupCode("100001");
        dmFocViewInfoVO.setGroupCnName("无线");
        dmFocViewInfoVO.setGroupLevel("LV1");
        Lv2ProdVOList.add(dmFocViewInfoVO);
        PowerMockito.doReturn(Lv2ProdVOList).when(dmFocViewInfoDao).viewInfoListForMonth(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList51Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setPageFlag("monthPage");
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setGranularityType("U");
        commonViewVO.setViewFlag("3");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        commonViewVO.setPermissionTag(false);
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv2DimensionSet  = new HashSet<>();
        lv2DimensionSet.add("153324");
        currentRoleDataPermission.setLv2DimensionSet(lv2DimensionSet);
        Set<String> lv3DimensionSet  = new HashSet<>();
        lv3DimensionSet.add("140701");
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("2");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("2");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv2ProdVOList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setLv1ProdRndTeamCode("134557");
        dmFocViewInfoVO.setLv1ProdRdTeamCnName("光");
        dmFocViewInfoVO.setGroupCode("100001");
        dmFocViewInfoVO.setGroupCnName("无线");
        dmFocViewInfoVO.setGroupLevel("LV2");
        Lv2ProdVOList.add(dmFocViewInfoVO);
        PowerMockito.doReturn(Lv2ProdVOList).when(dmFocViewInfoDao).viewInfoListForMonth(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }


    @Test
    public void viewInfoList6Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setGranularityType("U");
        commonViewVO.setViewFlag("2");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv2DimensionSet  = new HashSet<>();
        lv2DimensionSet.add("153324");
        currentRoleDataPermission.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("2");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("2");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv2ProdVOList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setLv1ProdRndTeamCode("134557");
        dmFocViewInfoVO.setLv1ProdRdTeamCnName("光");
        Lv2ProdVOList.add(dmFocViewInfoVO);
        PowerMockito.doReturn(Lv2ProdVOList).when(dmFocViewInfoDao).viewInfoList(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList61Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setGranularityType("U");
        commonViewVO.setViewFlag("2");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setKeyWord("key");
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        commonViewVO.setPermissionTag(false);
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv2DimensionSet  = new HashSet<>();
        lv2DimensionSet.add("153324");
        currentRoleDataPermission.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("2");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("2");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv2ProdVOList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setLv1ProdRndTeamCode("134557");
        dmFocViewInfoVO.setLv1ProdRdTeamCnName("光");
        Lv2ProdVOList.add(dmFocViewInfoVO);
        PowerMockito.doReturn(Lv2ProdVOList).when(dmFocViewInfoDao).viewInfoList(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList7Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setGranularityType("P");
        commonViewVO.setViewFlag("2");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv2DimensionSet  = new HashSet<>();
        lv2DimensionSet.add("153324");
        currentRoleDataPermission.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("2");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("2");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv2ProdVOList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setLv1ProdRndTeamCode("134557");
        dmFocViewInfoVO.setLv1ProdRdTeamCnName("光");
        Lv2ProdVOList.add(dmFocViewInfoVO);
        PowerMockito.doReturn(Lv2ProdVOList).when(dmFocViewInfoDao).viewInfoList(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList71Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setGranularityType("P");
        commonViewVO.setViewFlag("2");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv2DimensionSet  = new HashSet<>();
        lv2DimensionSet.add("15332422");
        currentRoleDataPermission.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("2");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocPftViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("2");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv2ProdVOList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setLv1ProdRndTeamCode("134557");
        dmFocViewInfoVO.setLv1ProdRdTeamCnName("光");
        dmFocViewInfoVO.setGroupLevel("LV1");
        dmFocViewInfoVO.setGroupCode("100001");
        dmFocViewInfoVO.setGroupCnName("无线");
        Lv2ProdVOList.add(dmFocViewInfoVO);
        PowerMockito.doReturn(Lv2ProdVOList).when(dmFocPftViewInfoDao).viewInfoList(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList72Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setGranularityType("P");
        commonViewVO.setViewFlag("2");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        commonViewVO.setCostType("P");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("15332422");
        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("2");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocPftViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("2");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv2ProdVOList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setLv1ProdRndTeamCode("134557");
        dmFocViewInfoVO.setLv1ProdRdTeamCnName("光");
        dmFocViewInfoVO.setGroupLevel("LV1");
        dmFocViewInfoVO.setGroupCode("100001");
        dmFocViewInfoVO.setGroupCnName("无线");
        Lv2ProdVOList.add(dmFocViewInfoVO);
        PowerMockito.doReturn(Lv2ProdVOList).when(dmFocPftViewInfoDao).viewInfoList(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList73Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setGranularityType("P");
        commonViewVO.setViewFlag("2");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setKeyWord("key");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        commonViewVO.setCostType("P");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv2DimensionSet  = new HashSet<>();
        lv2DimensionSet.add("153324");
        currentRoleDataPermission.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("2");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("2");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv2ProdVOList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setLv1ProdRndTeamCode("134557");
        dmFocViewInfoVO.setLv1ProdRdTeamCnName("光");
        Lv2ProdVOList.add(dmFocViewInfoVO);
        PowerMockito.doReturn(Lv2ProdVOList).when(dmFocViewInfoDao).viewInfoList(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList74Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setGranularityType("P");
        commonViewVO.setViewFlag("0");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setGroupLevel("LV3");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        commonViewVO.setCostType("P");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("15332422");
        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("2");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("15332422");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocPftViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("2");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv2ProdVOList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setLv1ProdRndTeamCode("134557");
        dmFocViewInfoVO.setLv1ProdRdTeamCnName("光");
        dmFocViewInfoVO.setGroupLevel("LV1");
        dmFocViewInfoVO.setGroupCode("15332422");
        dmFocViewInfoVO.setGroupCnName("无线");
        Lv2ProdVOList.add(dmFocViewInfoVO);
        PowerMockito.doReturn(Lv2ProdVOList).when(dmFocPftViewInfoDao).viewInfoList(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList8Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setPageFlag("monthPage");
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setGranularityType("P");
        commonViewVO.setViewFlag("2");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        commonViewVO.setCostType("P");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv2DimensionSet  = new HashSet<>();
        lv2DimensionSet.add("153324");
        currentRoleDataPermission.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("2");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocPftViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("2");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv2ProdVOList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setLv1ProdRndTeamCode("134557");
        dmFocViewInfoVO.setLv1ProdRdTeamCnName("光");
        Lv2ProdVOList.add(dmFocViewInfoVO);
        PowerMockito.doReturn(Lv2ProdVOList).when(dmFocPftViewInfoDao).viewInfoListForMonth(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList81Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setPageFlag("monthPage");
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setGranularityType("P");
        commonViewVO.setViewFlag("2");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setKeyWord("key");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        commonViewVO.setCostType("P");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv2DimensionSet  = new HashSet<>();
        lv2DimensionSet.add("153324");
        currentRoleDataPermission.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("2");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocPftViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("2");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv2ProdVOList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setLv1ProdRndTeamCode("134557");
        dmFocViewInfoVO.setLv1ProdRdTeamCnName("光");
        Lv2ProdVOList.add(dmFocViewInfoVO);
        PowerMockito.doReturn(Lv2ProdVOList).when(dmFocPftViewInfoDao).viewInfoListForMonth(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList9Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setPageFlag("monthPage");
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setGranularityType("U");
        commonViewVO.setViewFlag("2");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        commonViewVO.setCostType("P");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv2DimensionSet  = new HashSet<>();
        lv2DimensionSet.add("15332411");
        currentRoleDataPermission.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("2");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("2");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv2ProdVOList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setLv1ProdRndTeamCode("134557");
        dmFocViewInfoVO.setLv1ProdRdTeamCnName("光");
        dmFocViewInfoVO.setGroupCode("100001");
        dmFocViewInfoVO.setGroupCnName("无线");
        dmFocViewInfoVO.setGroupLevel("LV1");
        Lv2ProdVOList.add(dmFocViewInfoVO);
        PowerMockito.doReturn(Lv2ProdVOList).when(dmFocViewInfoDao).viewInfoListForMonth(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }


    @Test
    public void viewInfoList10Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setPageFlag("monthPage");
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setGranularityType("U");
        commonViewVO.setViewFlag("2");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        commonViewVO.setCostType("P");
        commonViewVO.setPermissionTag(true);
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("2");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("2");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv2ProdVOList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setLv1ProdRndTeamCode("134557");
        dmFocViewInfoVO.setLv1ProdRdTeamCnName("光");
        dmFocViewInfoVO.setGroupCode("100001");
        dmFocViewInfoVO.setGroupCnName("无线");
        dmFocViewInfoVO.setGroupLevel("LV1");
        Lv2ProdVOList.add(dmFocViewInfoVO);
        PowerMockito.doReturn(Lv2ProdVOList).when(dmFocViewInfoDao).viewInfoListForMonth(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList11Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setPageFlag("monthPage");
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setGranularityType("U");
        commonViewVO.setViewFlag("2");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV1");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        commonViewVO.setCostType("P");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("2");
        focViewInfoVO.setGroupLevel("LV2");
        focViewInfoVO.setGroupCode("153324");
        focViewInfoVO.setGroupCnName("5G&LTE FDD");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("2");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList111Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setPageFlag("monthPage");
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setGranularityType("U");
        commonViewVO.setViewFlag("2");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV1");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        commonViewVO.setCostType("P");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(any());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv2DimensionSet  = new HashSet<>();
        lv2DimensionSet.add("140701");
        currentRoleDataPermission.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("2");
        focViewInfoVO.setGroupLevel("LV2");
        focViewInfoVO.setGroupCode("153324");
        focViewInfoVO.setGroupCnName("5G&LTE FDD");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("2");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList12Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setPageFlag("monthPage");
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setGranularityType("U");
        commonViewVO.setViewFlag("2");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV1");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        commonViewVO.setCostType("P");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("2");
        focViewInfoVO.setGroupLevel("LV2");
        focViewInfoVO.setGroupCode("153324");
        focViewInfoVO.setGroupCnName("5G&LTE FDD");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("2");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList13Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setPageFlag("monthPage");
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setGranularityType("U");
        commonViewVO.setViewFlag("2");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV1");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        commonViewVO.setCostType("P");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("2");
        focViewInfoVO.setGroupLevel("LV2");
        focViewInfoVO.setGroupCode("153324");
        focViewInfoVO.setGroupCnName("5G&LTE FDD");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("2");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }


    @Test
    public void viewInfoList14Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setPageFlag("monthPage");
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setGranularityType("U");
        commonViewVO.setViewFlag("3");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV1");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        commonViewVO.setCostType("P");
        commonViewVO.setTeamCodeList(Lists.newArrayList("104364"));
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("3");
        focViewInfoVO.setGroupLevel("LV2");
        focViewInfoVO.setGroupCode("153324");
        focViewInfoVO.setGroupCnName("5G&LTE FDD");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv3ProdVOList = new ArrayList<>();
        DmFocViewInfoVO lv3ProdVO = new DmFocViewInfoVO();
        lv3ProdVO.setViewFlag("3");
        lv3ProdVO.setGroupLevel("LV2");
        lv3ProdVO.setGroupCode("153324");
        lv3ProdVO.setGroupCnName("5G&LTE FDD");
        lv3ProdVO.setLv2ProdRndTeamCode("123132");
        lv3ProdVO.setLv2ProdRdTeamCnName("Other");
        lv3ProdVO.setLv1ProdRndTeamCode("100001");
        lv3ProdVO.setLv1ProdRdTeamCnName("无线");
        Lv3ProdVOList.add(lv3ProdVO);
        PowerMockito.doReturn(Lv3ProdVOList).when(dmFocViewInfoDao).viewInfoListForMonth(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList15Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setGranularityType("U");
        commonViewVO.setViewFlag("3");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV1");
        commonViewVO.setPermissionTag(false);
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        commonViewVO.setCostType("P");
        commonViewVO.setTeamCodeList(Lists.newArrayList("100001"));
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("3");
        focViewInfoVO.setGroupLevel("LV2");
        focViewInfoVO.setGroupCode("153324");
        focViewInfoVO.setGroupCnName("5G&LTE FDD");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoList(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv3ProdVOList = new ArrayList<>();
        DmFocViewInfoVO lv3ProdVO = new DmFocViewInfoVO();
        lv3ProdVO.setViewFlag("3");
        lv3ProdVO.setGroupLevel("LV2");
        lv3ProdVO.setGroupCode("153324");
        lv3ProdVO.setGroupCnName("5G&LTE FDD");
        lv3ProdVO.setLv2ProdRndTeamCode("123132");
        lv3ProdVO.setLv2ProdRdTeamCnName("Other");
        lv3ProdVO.setLv1ProdRndTeamCode("100001");
        lv3ProdVO.setLv1ProdRdTeamCnName("无线");
        Lv3ProdVOList.add(lv3ProdVO);
        PowerMockito.doReturn(Lv3ProdVOList).when(dmFocViewInfoDao).viewInfoList(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList16Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setGranularityType("U");
        commonViewVO.setViewFlag("3");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        commonViewVO.setCostType("P");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("3");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoList(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv1ProdVOList = new ArrayList<>();
        DmFocViewInfoVO lv1ProdVO = new DmFocViewInfoVO();
        lv1ProdVO.setViewFlag("3");
        lv1ProdVO.setLv1ProdRndTeamCode("100002");
        lv1ProdVO.setLv1ProdRdTeamCnName("光");
        Lv1ProdVOList.add(lv1ProdVO);
        PowerMockito.doReturn(Lv1ProdVOList).when(dmFocViewInfoDao).reverseFindLv1ProdCode(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList17Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setPageFlag("monthPage");
        commonViewVO.setGranularityType("U");
        commonViewVO.setViewFlag("3");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        commonViewVO.setCostType("P");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("10000123");
        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("3");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv1ProdVOList = new ArrayList<>();
        DmFocViewInfoVO lv1ProdVO = new DmFocViewInfoVO();
        lv1ProdVO.setViewFlag("3");
        lv1ProdVO.setLv1ProdRndTeamCode("100002");
        lv1ProdVO.setLv1ProdRdTeamCnName("光");
        Lv1ProdVOList.add(lv1ProdVO);
        PowerMockito.doReturn(Lv1ProdVOList).when(dmFocViewInfoDao).reverseFindLv1ProdCodeMonth(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }


    @Test
    public void viewInfoList171Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setPageFlag("monthPage");
        commonViewVO.setGranularityType("U");
        commonViewVO.setViewFlag("3");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        commonViewVO.setCostType("P");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("3");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv1ProdVOList = new ArrayList<>();
        DmFocViewInfoVO lv1ProdVO = new DmFocViewInfoVO();
        lv1ProdVO.setViewFlag("3");
        lv1ProdVO.setLv1ProdRndTeamCode("100002");
        lv1ProdVO.setLv1ProdRdTeamCnName("光");
        Lv1ProdVOList.add(lv1ProdVO);
        PowerMockito.doReturn(Lv1ProdVOList).when(dmFocViewInfoDao).reverseFindLv1ProdCodeMonth(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList172Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setPageFlag("ANNUAL");
        commonViewVO.setGranularityType("U");
        commonViewVO.setViewFlag("3");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setCostType("M");
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("10000123");
        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("3");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv1ProdVOList = new ArrayList<>();
        DmFocViewInfoVO lv1ProdVO = new DmFocViewInfoVO();
        lv1ProdVO.setViewFlag("3");
        lv1ProdVO.setLv1ProdRndTeamCode("100002");
        lv1ProdVO.setLv1ProdRdTeamCnName("光");
        Lv1ProdVOList.add(lv1ProdVO);
        PowerMockito.doReturn(Lv1ProdVOList).when(dmFocViewInfoDao).reverseFindLv1ProdCodeMonth(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(madeAnnualAmpDao).getMadeAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList173Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setPageFlag("ANNUAL");
        commonViewVO.setGranularityType("P");
        commonViewVO.setViewFlag("3");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setCostType("M");
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("10000123");
        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("3");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv1ProdVOList = new ArrayList<>();
        DmFocViewInfoVO lv1ProdVO = new DmFocViewInfoVO();
        lv1ProdVO.setViewFlag("3");
        lv1ProdVO.setLv1ProdRndTeamCode("100002");
        lv1ProdVO.setLv1ProdRdTeamCnName("光");
        Lv1ProdVOList.add(lv1ProdVO);
        PowerMockito.doReturn(Lv1ProdVOList).when(dmFocViewInfoDao).reverseFindLv1ProdCodeMonth(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(madeAnnualAmpDao).getMadeAnnualPeriodYear(commonViewVO.getTablePreFix(),12L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList174Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setPageFlag("ANNUAL");
        commonViewVO.setGranularityType("P");
        commonViewVO.setViewFlag("3");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setCostType("T");
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("10000123");
        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("3");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv1ProdVOList = new ArrayList<>();
        DmFocViewInfoVO lv1ProdVO = new DmFocViewInfoVO();
        lv1ProdVO.setViewFlag("3");
        lv1ProdVO.setLv1ProdRndTeamCode("100002");
        lv1ProdVO.setLv1ProdRdTeamCnName("光");
        Lv1ProdVOList.add(lv1ProdVO);
        PowerMockito.doReturn(Lv1ProdVOList).when(dmFocViewInfoDao).reverseFindLv1ProdCodeMonth(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(totalAnnualAmpDao).getTotalAnnualPeriodYear(commonViewVO.getTablePreFix(),12L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList175Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setPageFlag("ANNUAL");
        commonViewVO.setGranularityType("P");
        commonViewVO.setViewFlag("3");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setCostType("P");
        commonViewVO.setKeyWord("Key");
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("10000123");
        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("3");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv1ProdVOList = new ArrayList<>();
        DmFocViewInfoVO lv1ProdVO = new DmFocViewInfoVO();
        lv1ProdVO.setViewFlag("3");
        lv1ProdVO.setLv1ProdRndTeamCode("100002");
        lv1ProdVO.setLv1ProdRdTeamCnName("光");
        Lv1ProdVOList.add(lv1ProdVO);
        PowerMockito.doReturn(Lv1ProdVOList).when(dmFocViewInfoDao).reverseFindLv1ProdCodeMonth(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList176Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setPageFlag("ANNUAL");
        commonViewVO.setGranularityType("P");
        commonViewVO.setViewFlag("3");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setCostType("M");
        commonViewVO.setKeyWord("Key");
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("10000123");
        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("3");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv1ProdVOList = new ArrayList<>();
        DmFocViewInfoVO lv1ProdVO = new DmFocViewInfoVO();
        lv1ProdVO.setViewFlag("3");
        lv1ProdVO.setLv1ProdRndTeamCode("100002");
        lv1ProdVO.setLv1ProdRdTeamCnName("光");
        Lv1ProdVOList.add(lv1ProdVO);
        PowerMockito.doReturn(Lv1ProdVOList).when(dmFocViewInfoDao).reverseFindLv1ProdCodeMonth(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(madeAnnualAmpDao).getMadeAnnualPeriodYear(commonViewVO.getTablePreFix(),12L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList177Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setPageFlag("MONTH");
        commonViewVO.setGranularityType("P");
        commonViewVO.setViewFlag("3");
        commonViewVO.setPeriodYear("2023");commonViewVO.setTeamLevel("LV0");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setCostType("M");
        commonViewVO.setKeyWord("Key");
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("10000123");
        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("3");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv1ProdVOList = new ArrayList<>();
        DmFocViewInfoVO lv1ProdVO = new DmFocViewInfoVO();
        lv1ProdVO.setViewFlag("3");
        lv1ProdVO.setLv1ProdRndTeamCode("100002");
        lv1ProdVO.setLv1ProdRdTeamCnName("光");
        Lv1ProdVOList.add(lv1ProdVO);
        PowerMockito.doReturn(Lv1ProdVOList).when(dmFocViewInfoDao).reverseFindLv1ProdCodeMonth(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(madeAnnualAmpDao).getMadeAnnualPeriodYear(commonViewVO.getTablePreFix(),12L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList178Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setPageFlag("MONTH");
        commonViewVO.setGranularityType("P");
        commonViewVO.setViewFlag("3");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setCostType("M");
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("10000123");
        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("3");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv1ProdVOList = new ArrayList<>();
        DmFocViewInfoVO lv1ProdVO = new DmFocViewInfoVO();
        lv1ProdVO.setViewFlag("3");
        lv1ProdVO.setLv1ProdRndTeamCode("100002");
        lv1ProdVO.setLv1ProdRdTeamCnName("光");
        Lv1ProdVOList.add(lv1ProdVO);
        PowerMockito.doReturn(Lv1ProdVOList).when(dmFocViewInfoDao).reverseFindLv1ProdCodeMonth(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(madeAnnualAmpDao).getMadeAnnualPeriodYear(commonViewVO.getTablePreFix(),12L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList179Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setPageFlag("MONTH");
        commonViewVO.setGranularityType("P");
        commonViewVO.setViewFlag("3");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("ICT");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setCostType("T");
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("10000123");
        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("3");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv1ProdVOList = new ArrayList<>();
        DmFocViewInfoVO lv1ProdVO = new DmFocViewInfoVO();
        lv1ProdVO.setViewFlag("3");
        lv1ProdVO.setLv1ProdRndTeamCode("100002");
        lv1ProdVO.setLv1ProdRdTeamCnName("光");
        Lv1ProdVOList.add(lv1ProdVO);
        PowerMockito.doReturn(Lv1ProdVOList).when(dmFocViewInfoDao).reverseFindLv1ProdCodeMonth(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(totalAnnualAmpDao).getTotalAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList180Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setPageFlag("MONTH");
        commonViewVO.setGranularityType("U");
        commonViewVO.setViewFlag("3");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setCostType("T");
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("10000123");
        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("3");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv1ProdVOList = new ArrayList<>();
        DmFocViewInfoVO lv1ProdVO = new DmFocViewInfoVO();
        lv1ProdVO.setViewFlag("3");
        lv1ProdVO.setLv1ProdRndTeamCode("100002");
        lv1ProdVO.setLv1ProdRdTeamCnName("光");
        Lv1ProdVOList.add(lv1ProdVO);
        PowerMockito.doReturn(Lv1ProdVOList).when(dmFocViewInfoDao).reverseFindLv1ProdCodeMonth(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(totalAnnualAmpDao).getTotalAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList181Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setPageFlag("MONTH");
        commonViewVO.setGranularityType("U");
        commonViewVO.setViewFlag("3");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setCostType("M");
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("10000123");
        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("3");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv1ProdVOList = new ArrayList<>();
        DmFocViewInfoVO lv1ProdVO = new DmFocViewInfoVO();
        lv1ProdVO.setViewFlag("3");
        lv1ProdVO.setLv1ProdRndTeamCode("100002");
        lv1ProdVO.setLv1ProdRdTeamCnName("光");
        Lv1ProdVOList.add(lv1ProdVO);
        PowerMockito.doReturn(Lv1ProdVOList).when(dmFocViewInfoDao).reverseFindLv1ProdCodeMonth(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(madeAnnualAmpDao).getMadeAnnualPeriodYear(commonViewVO.getTablePreFix(),12L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList182Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setPageFlag("MONTH");
        commonViewVO.setGranularityType("U");
        commonViewVO.setViewFlag("3");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setCostType("M");
        commonViewVO.setKeyWord("Key");
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("10000123");
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("3");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv1ProdVOList = new ArrayList<>();
        DmFocViewInfoVO lv1ProdVO = new DmFocViewInfoVO();
        lv1ProdVO.setViewFlag("3");
        lv1ProdVO.setLv1ProdRndTeamCode("100002");
        lv1ProdVO.setLv1ProdRdTeamCnName("光");
        Lv1ProdVOList.add(lv1ProdVO);
        PowerMockito.doReturn(Lv1ProdVOList).when(dmFocViewInfoDao).reverseFindLv1ProdCodeMonth(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(madeAnnualAmpDao).getMadeAnnualPeriodYear(commonViewVO.getTablePreFix(),12L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList183Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setPageFlag("ANNUAL");
        commonViewVO.setGranularityType("U");
        commonViewVO.setViewFlag("3");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setCostType("M");
        commonViewVO.setKeyWord("Key");
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("10000123");
        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("3");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv1ProdVOList = new ArrayList<>();
        DmFocViewInfoVO lv1ProdVO = new DmFocViewInfoVO();
        lv1ProdVO.setViewFlag("3");
        lv1ProdVO.setLv1ProdRndTeamCode("100002");
        lv1ProdVO.setLv1ProdRdTeamCnName("光");
        Lv1ProdVOList.add(lv1ProdVO);
        PowerMockito.doReturn(Lv1ProdVOList).when(dmFocViewInfoDao).reverseFindLv1ProdCodeMonth(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(madeAnnualAmpDao).getMadeAnnualPeriodYear(commonViewVO.getTablePreFix(),12L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList184Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setPageFlag("ANNUAL");
        commonViewVO.setGranularityType("U");
        commonViewVO.setViewFlag("3");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setCostType("P");
        commonViewVO.setKeyWord("Key");
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("10000123");
        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("3");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv1ProdVOList = new ArrayList<>();
        DmFocViewInfoVO lv1ProdVO = new DmFocViewInfoVO();
        lv1ProdVO.setViewFlag("3");
        lv1ProdVO.setLv1ProdRndTeamCode("100002");
        lv1ProdVO.setLv1ProdRdTeamCnName("光");
        Lv1ProdVOList.add(lv1ProdVO);
        PowerMockito.doReturn(Lv1ProdVOList).when(dmFocViewInfoDao).reverseFindLv1ProdCodeMonth(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList185Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setPageFlag("ANNUAL");
        commonViewVO.setGranularityType("D");
        commonViewVO.setViewFlag("3");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setCostType("M");
        commonViewVO.setKeyWord("Key");
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("10000123");
        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("3");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv1ProdVOList = new ArrayList<>();
        DmFocViewInfoVO lv1ProdVO = new DmFocViewInfoVO();
        lv1ProdVO.setViewFlag("3");
        lv1ProdVO.setLv1ProdRndTeamCode("100002");
        lv1ProdVO.setLv1ProdRdTeamCnName("光");
        Lv1ProdVOList.add(lv1ProdVO);
        PowerMockito.doReturn(Lv1ProdVOList).when(dmFocViewInfoDao).reverseFindLv1ProdCodeMonth(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(madeAnnualAmpDao).getMadeAnnualPeriodYear(commonViewVO.getTablePreFix(),12L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList186Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setPageFlag("ANNUAL");
        commonViewVO.setGranularityType("D");
        commonViewVO.setViewFlag("3");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setCostType("M");
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("10000123");
        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("3");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv1ProdVOList = new ArrayList<>();
        DmFocViewInfoVO lv1ProdVO = new DmFocViewInfoVO();
        lv1ProdVO.setViewFlag("3");
        lv1ProdVO.setLv1ProdRndTeamCode("100002");
        lv1ProdVO.setLv1ProdRdTeamCnName("光");
        Lv1ProdVOList.add(lv1ProdVO);
        PowerMockito.doReturn(Lv1ProdVOList).when(dmFocViewInfoDao).reverseFindLv1ProdCodeMonth(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(madeAnnualAmpDao).getMadeAnnualPeriodYear(commonViewVO.getTablePreFix(),12L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList187Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setPageFlag("ANNUAL");
        commonViewVO.setGranularityType("D");
        commonViewVO.setViewFlag("3");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setCostType("T");
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("10000123");
        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("3");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv1ProdVOList = new ArrayList<>();
        DmFocViewInfoVO lv1ProdVO = new DmFocViewInfoVO();
        lv1ProdVO.setViewFlag("3");
        lv1ProdVO.setLv1ProdRndTeamCode("100002");
        lv1ProdVO.setLv1ProdRdTeamCnName("光");
        Lv1ProdVOList.add(lv1ProdVO);
        PowerMockito.doReturn(Lv1ProdVOList).when(dmFocViewInfoDao).reverseFindLv1ProdCodeMonth(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(totalAnnualAmpDao).getTotalAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList188Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setPageFlag("CONFIG");
        commonViewVO.setGranularityType("D");
        commonViewVO.setViewFlag("3");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setCostType("T");
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("10000123");
        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("3");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv1ProdVOList = new ArrayList<>();
        DmFocViewInfoVO lv1ProdVO = new DmFocViewInfoVO();
        lv1ProdVO.setViewFlag("3");
        lv1ProdVO.setLv1ProdRndTeamCode("100002");
        lv1ProdVO.setLv1ProdRdTeamCnName("光");
        Lv1ProdVOList.add(lv1ProdVO);
        PowerMockito.doReturn(Lv1ProdVOList).when(dmFocViewInfoDao).reverseFindLv1ProdCodeMonth(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(totalAnnualAmpDao).getTotalAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList189Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setPageFlag("CONFIG");
        commonViewVO.setGranularityType("D");
        commonViewVO.setViewFlag("3");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setCostType("M");
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("10000123");
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("3");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv1ProdVOList = new ArrayList<>();
        DmFocViewInfoVO lv1ProdVO = new DmFocViewInfoVO();
        lv1ProdVO.setViewFlag("3");
        lv1ProdVO.setLv1ProdRndTeamCode("100002");
        lv1ProdVO.setLv1ProdRdTeamCnName("光");
        Lv1ProdVOList.add(lv1ProdVO);
        PowerMockito.doReturn(Lv1ProdVOList).when(dmFocViewInfoDao).reverseFindLv1ProdCodeMonth(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(madeAnnualAmpDao).getMadeAnnualPeriodYear(commonViewVO.getTablePreFix(),12L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList190Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setPageFlag("CONFIG");
        commonViewVO.setGranularityType("D");
        commonViewVO.setViewFlag("3");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setCostType("M");
        commonViewVO.setKeyWord("Key");
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("10000123");
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("3");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv1ProdVOList = new ArrayList<>();
        DmFocViewInfoVO lv1ProdVO = new DmFocViewInfoVO();
        lv1ProdVO.setViewFlag("3");
        lv1ProdVO.setLv1ProdRndTeamCode("100002");
        lv1ProdVO.setLv1ProdRdTeamCnName("光");
        Lv1ProdVOList.add(lv1ProdVO);
        PowerMockito.doReturn(Lv1ProdVOList).when(dmFocViewInfoDao).reverseFindLv1ProdCodeMonth(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(madeAnnualAmpDao).getMadeAnnualPeriodYear(commonViewVO.getTablePreFix(),12L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList191Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setPageFlag("ANNUAL");
        commonViewVO.setGranularityType("U");
        commonViewVO.setViewFlag("3");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setCostType("T");
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("10000123");
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("3");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv1ProdVOList = new ArrayList<>();
        DmFocViewInfoVO lv1ProdVO = new DmFocViewInfoVO();
        lv1ProdVO.setViewFlag("3");
        lv1ProdVO.setLv1ProdRndTeamCode("100002");
        lv1ProdVO.setLv1ProdRdTeamCnName("光");
        Lv1ProdVOList.add(lv1ProdVO);
        PowerMockito.doReturn(Lv1ProdVOList).when(dmFocViewInfoDao).reverseFindLv1ProdCodeMonth(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(totalAnnualAmpDao).getTotalAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewInfoList18Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setCaliberFlag("C");
        commonViewVO.setPageFlag("monthPage");
        commonViewVO.setGranularityType("P");
        commonViewVO.setViewFlag("3");
        commonViewVO.setGroupLevel("LV0");
        commonViewVO.setPeriodYear("2023");
        commonViewVO.setTeamLevel("LV0");
        commonViewVO.setPermissionTag(true);
        commonViewVO.setIndustryOrg("ICT");
        commonViewVO.setTablePreFix("dm_foc");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = new DmFocVersionInfoDTO();
        dmFocVersionInfoDTO.setVersionId(11L);
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(commonViewVO.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(commonViewVO.getIndustryOrg());
        Long versionId = 10L;
        PowerMockito.doReturn(versionId).when(commonService).getVersionId(IndustryIndexEnum.DataType.ITEM.getValue(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> dmFocMonthAmpVOList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("3");
        focViewInfoVO.setGroupLevel("LV1");
        focViewInfoVO.setGroupCode("100001");
        focViewInfoVO.setGroupCnName("无线");
        dmFocMonthAmpVOList.add(focViewInfoVO);
        PowerMockito.doReturn(dmFocMonthAmpVOList).when(dmFocPftViewInfoDao).viewInfoListForMonth(commonViewVO);
        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        List<DmFocViewInfoVO> Lv1ProdVOList = new ArrayList<>();
        DmFocViewInfoVO lv1ProdVO = new DmFocViewInfoVO();
        lv1ProdVO.setViewFlag("3");
        lv1ProdVO.setLv1ProdRndTeamCode("100002");
        lv1ProdVO.setLv1ProdRdTeamCnName("光");
        lv1ProdVO.setGroupLevel("LV1");
        lv1ProdVO.setGroupCode("100001");
        lv1ProdVO.setGroupCnName("无线");
        Lv1ProdVOList.add(lv1ProdVO);
        PowerMockito.doReturn(Lv1ProdVOList).when(dmFocPftViewInfoDao).viewInfoListForMonth(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        Assertions.assertNotNull(annualCommonService.viewInfoList(commonViewVO));
    }

    @Test
    public void viewFlagInfoList() throws ApplicationException {
        CommonViewVO viewFlag = new CommonViewVO();
        viewFlag.setCaliberFlag("C");
        viewFlag.setGranularityType("U");
        viewFlag.setCostType("P");
        viewFlag.setIndustryOrg("ICT");
        viewFlag.setTablePreFix("dm_foc");
        List<ViewInfoVO> allProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        allProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(allProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(viewFlag.getCostType(),viewFlag.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(viewFlag.getIndustryOrg());

        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        List<DmFocViewInfoVO> viewFlagList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("0");
        focViewInfoVO.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO);

        DmFocViewInfoVO focViewInfoVO1 = new DmFocViewInfoVO();
        focViewInfoVO1.setViewFlag("1");
        focViewInfoVO1.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO1);
        DmFocViewInfoVO focViewInfoVO2 = new DmFocViewInfoVO();
        focViewInfoVO2.setViewFlag("2");
        focViewInfoVO2.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO2);
        DmFocViewInfoVO focViewInfoVO3 = new DmFocViewInfoVO();
        focViewInfoVO3.setViewFlag("3");
        focViewInfoVO3.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO3);
        DmFocViewInfoVO focViewInfoVO4 = new DmFocViewInfoVO();
        focViewInfoVO4.setViewFlag("4");
        focViewInfoVO4.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO4);
        Assertions.assertNotNull(annualCommonService.viewFlagInfoList(commonViewVO));
    }

    @Test
    public void viewFlagInfoList1Test() throws ApplicationException {
        CommonViewVO viewFlag = new CommonViewVO();
        viewFlag.setCaliberFlag("C");
        viewFlag.setGranularityType("U");
        viewFlag.setCostType("P");
        viewFlag.setIndustryOrg("ICT");
        viewFlag.setTablePreFix("dm_foc");
        List<ViewInfoVO> allProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        allProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(allProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(viewFlag.getCostType(),viewFlag.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(viewFlag.getIndustryOrg());
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        List<DmFocViewInfoVO> viewFlagList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("0");
        focViewInfoVO.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO);

        DmFocViewInfoVO focViewInfoVO1 = new DmFocViewInfoVO();
        focViewInfoVO1.setViewFlag("1");
        focViewInfoVO1.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO1);
        DmFocViewInfoVO focViewInfoVO2 = new DmFocViewInfoVO();
        focViewInfoVO2.setViewFlag("2");
        focViewInfoVO2.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO2);
        DmFocViewInfoVO focViewInfoVO3 = new DmFocViewInfoVO();
        focViewInfoVO3.setViewFlag("3");
        focViewInfoVO3.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO3);
        DmFocViewInfoVO focViewInfoVO4 = new DmFocViewInfoVO();
        focViewInfoVO4.setViewFlag("4");
        focViewInfoVO4.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO4);
        PowerMockito.doReturn(viewFlagList).when(dmFocPftViewInfoDao).viewFlagInfoProfitsList(any());
        Assertions.assertNotNull(annualCommonService.viewFlagInfoList(commonViewVO));
    }


    @Test
    public void viewFlagInfoList2Test() throws ApplicationException {
        CommonViewVO viewFlag = new CommonViewVO();
        viewFlag.setCaliberFlag("C");
        viewFlag.setGranularityType("U");
        viewFlag.setCostType("P");
        viewFlag.setIndustryOrg("ICT");
        viewFlag.setTablePreFix("dm_foc");
        List<ViewInfoVO> allProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("15332433");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        allProdDimensionList.add(viewInfoVO);
        ViewInfoVO viewInfoV1 = new ViewInfoVO();
        viewInfoV1.setViewFlag("3");
        viewInfoV1.setLv1ProdRndTeamCode("123324");
        viewInfoV1.setLv1ProdRnTeamName("无线");
        viewInfoV1.setLv2ProdRndTeamCode("1533243");
        viewInfoV1.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoV1.setLv3ProdRndTeamCode("140701");
        viewInfoV1.setLv3ProdRnTeamName("FDD NR");
        allProdDimensionList.add(viewInfoV1);
        ViewInfoVO viewInfoV2 = new ViewInfoVO();
        viewInfoV2.setViewFlag("3");
        viewInfoV2.setLv1ProdRndTeamCode("1223324");
        viewInfoV2.setLv1ProdRnTeamName("无线1");
        viewInfoV2.setLv2ProdRndTeamCode("15332423");
        viewInfoV2.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoV2.setLv3ProdRndTeamCode("1420701");
        viewInfoV2.setLv3ProdRnTeamName("FDD NR");
        allProdDimensionList.add(viewInfoV2);
        PowerMockito.doReturn(allProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(viewFlag.getCostType(),viewFlag.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("100001");
        lv1DimensionSet.add("123324");
        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        Set<String> lv2DimensionSet  = new HashSet<>();
        lv2DimensionSet.add("12324");
        currentRoleDataPermission.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(viewFlag.getIndustryOrg());
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setLv1DimensionSet(lv1DimensionSet);
        commonViewVO.setLv2DimensionSet(lv2DimensionSet);
        List<DmFocViewInfoVO> viewFlagList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("0");
        focViewInfoVO.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO);

        DmFocViewInfoVO focViewInfoVO1 = new DmFocViewInfoVO();
        focViewInfoVO1.setViewFlag("1");
        focViewInfoVO1.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO1);
        DmFocViewInfoVO focViewInfoVO2 = new DmFocViewInfoVO();
        focViewInfoVO2.setViewFlag("2");
        focViewInfoVO2.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO2);
        DmFocViewInfoVO focViewInfoVO3 = new DmFocViewInfoVO();
        focViewInfoVO3.setViewFlag("3");
        focViewInfoVO3.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO3);
        DmFocViewInfoVO focViewInfoVO4 = new DmFocViewInfoVO();
        focViewInfoVO4.setViewFlag("4");
        focViewInfoVO4.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO4);
        Assertions.assertNotNull(annualCommonService.viewFlagInfoList(commonViewVO));
    }

    @Test
    public void viewFlagInfoList21Test() throws ApplicationException {
        CommonViewVO viewFlag = new CommonViewVO();
        viewFlag.setCaliberFlag("C");
        viewFlag.setGranularityType("U");
        viewFlag.setCostType("P");
        viewFlag.setIndustryOrg("ICT");
        viewFlag.setTablePreFix("dm_foc");
        List<ViewInfoVO> allProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("15332433");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        allProdDimensionList.add(viewInfoVO);
        ViewInfoVO viewInfoV1 = new ViewInfoVO();
        viewInfoV1.setViewFlag("3");
        viewInfoV1.setLv1ProdRndTeamCode("123324");
        viewInfoV1.setLv1ProdRnTeamName("无线");
        viewInfoV1.setLv2ProdRndTeamCode("1533243");
        viewInfoV1.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoV1.setLv3ProdRndTeamCode("140701");
        viewInfoV1.setLv3ProdRnTeamName("FDD NR");
        allProdDimensionList.add(viewInfoV1);
        ViewInfoVO viewInfoV2 = new ViewInfoVO();
        viewInfoV2.setViewFlag("3");
        viewInfoV2.setLv1ProdRndTeamCode("1223324");
        viewInfoV2.setLv1ProdRnTeamName("无线1");
        viewInfoV2.setLv2ProdRndTeamCode("15332423");
        viewInfoV2.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoV2.setLv3ProdRndTeamCode("1420701");
        viewInfoV2.setLv3ProdRnTeamName("FDD NR");
        allProdDimensionList.add(viewInfoV2);
        PowerMockito.doReturn(allProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(viewFlag.getCostType(),viewFlag.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("100001");
        lv1DimensionSet.add("123324");
        currentRoleDataPermission.setLv0DimensionSet(lv1DimensionSet);
        Set<String> lv2DimensionSet  = new HashSet<>();
        lv2DimensionSet.add("12324");
        Set<String> lv3DimensionSet  = new HashSet<>();
        lv3DimensionSet.add("12324");
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(viewFlag.getIndustryOrg());
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setLv0DimensionSet(lv3DimensionSet);
        List<DmFocViewInfoVO> viewFlagList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("0");
        focViewInfoVO.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO);

        DmFocViewInfoVO focViewInfoVO1 = new DmFocViewInfoVO();
        focViewInfoVO1.setViewFlag("1");
        focViewInfoVO1.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO1);
        DmFocViewInfoVO focViewInfoVO2 = new DmFocViewInfoVO();
        focViewInfoVO2.setViewFlag("2");
        focViewInfoVO2.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO2);
        DmFocViewInfoVO focViewInfoVO3 = new DmFocViewInfoVO();
        focViewInfoVO3.setViewFlag("3");
        focViewInfoVO3.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO3);
        DmFocViewInfoVO focViewInfoVO4 = new DmFocViewInfoVO();
        focViewInfoVO4.setViewFlag("4");
        focViewInfoVO4.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO4);
        Assertions.assertNotNull(annualCommonService.viewFlagInfoList(commonViewVO));
    }

    @Test
    public void viewFlagInfoList22Test() throws ApplicationException {
        CommonViewVO viewFlag = new CommonViewVO();
        viewFlag.setCaliberFlag("C");
        viewFlag.setGranularityType("U");
        viewFlag.setCostType("P");
        viewFlag.setIndustryOrg("ICT");
        viewFlag.setTablePreFix("dm_foc");
        List<ViewInfoVO> allProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("15332433");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        allProdDimensionList.add(viewInfoVO);
        ViewInfoVO viewInfoV1 = new ViewInfoVO();
        viewInfoV1.setViewFlag("3");
        viewInfoV1.setLv1ProdRndTeamCode("123324");
        viewInfoV1.setLv1ProdRnTeamName("无线");
        viewInfoV1.setLv2ProdRndTeamCode("1533243");
        viewInfoV1.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoV1.setLv3ProdRndTeamCode("140701");
        viewInfoV1.setLv3ProdRnTeamName("FDD NR");
        allProdDimensionList.add(viewInfoV1);
        ViewInfoVO viewInfoV2 = new ViewInfoVO();
        viewInfoV2.setViewFlag("3");
        viewInfoV2.setLv1ProdRndTeamCode("1223324");
        viewInfoV2.setLv1ProdRnTeamName("无线1");
        viewInfoV2.setLv2ProdRndTeamCode("15332423");
        viewInfoV2.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoV2.setLv3ProdRndTeamCode("1420701");
        viewInfoV2.setLv3ProdRnTeamName("FDD NR");
        allProdDimensionList.add(viewInfoV2);
        PowerMockito.doReturn(allProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(viewFlag.getCostType(),viewFlag.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("100001");
        lv1DimensionSet.add("123324");
        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        Set<String> lv2DimensionSet  = new HashSet<>();
        lv2DimensionSet.add("12324");
        Set<String> lv3DimensionSet  = new HashSet<>();
        lv3DimensionSet.add("12324");
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(viewFlag.getIndustryOrg());
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setLv1DimensionSet(lv1DimensionSet);
        List<DmFocViewInfoVO> viewFlagList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("0");
        focViewInfoVO.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO);

        DmFocViewInfoVO focViewInfoVO1 = new DmFocViewInfoVO();
        focViewInfoVO1.setViewFlag("1");
        focViewInfoVO1.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO1);
        DmFocViewInfoVO focViewInfoVO2 = new DmFocViewInfoVO();
        focViewInfoVO2.setViewFlag("2");
        focViewInfoVO2.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO2);
        DmFocViewInfoVO focViewInfoVO3 = new DmFocViewInfoVO();
        focViewInfoVO3.setViewFlag("3");
        focViewInfoVO3.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO3);
        DmFocViewInfoVO focViewInfoVO4 = new DmFocViewInfoVO();
        focViewInfoVO4.setViewFlag("4");
        focViewInfoVO4.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO4);
        Assertions.assertNotNull(annualCommonService.viewFlagInfoList(commonViewVO));
    }

    @Test
    public void viewFlagInfoList23Test() throws ApplicationException {
        CommonViewVO viewFlag = new CommonViewVO();
        viewFlag.setCaliberFlag("C");
        viewFlag.setGranularityType("U");
        viewFlag.setCostType("P");
        viewFlag.setIndustryOrg("ICT");
        viewFlag.setTablePreFix("dm_foc");
        List<ViewInfoVO> allProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("15332433");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        allProdDimensionList.add(viewInfoVO);
        ViewInfoVO viewInfoV1 = new ViewInfoVO();
        viewInfoV1.setViewFlag("3");
        viewInfoV1.setLv1ProdRndTeamCode("123324");
        viewInfoV1.setLv1ProdRnTeamName("无线");
        viewInfoV1.setLv2ProdRndTeamCode("1533243");
        viewInfoV1.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoV1.setLv3ProdRndTeamCode("140701");
        viewInfoV1.setLv3ProdRnTeamName("FDD NR");
        allProdDimensionList.add(viewInfoV1);
        ViewInfoVO viewInfoV2 = new ViewInfoVO();
        viewInfoV2.setViewFlag("3");
        viewInfoV2.setLv1ProdRndTeamCode("1223324");
        viewInfoV2.setLv1ProdRnTeamName("无线1");
        viewInfoV2.setLv2ProdRndTeamCode("15332423");
        viewInfoV2.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoV2.setLv3ProdRndTeamCode("1420701");
        viewInfoV2.setLv3ProdRnTeamName("FDD NR");
        allProdDimensionList.add(viewInfoV2);
        PowerMockito.doReturn(allProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(viewFlag.getCostType(),viewFlag.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("100001");
        lv1DimensionSet.add("123324");
        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        Set<String> lv2DimensionSet  = new HashSet<>();
        lv2DimensionSet.add("15332433");
        currentRoleDataPermission.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(viewFlag.getIndustryOrg());
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setLv1DimensionSet(lv1DimensionSet);
        commonViewVO.setLv2DimensionSet(lv2DimensionSet);

        List<DmFocViewInfoVO> viewFlagList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("0");
        focViewInfoVO.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO);

        DmFocViewInfoVO focViewInfoVO1 = new DmFocViewInfoVO();
        focViewInfoVO1.setViewFlag("1");
        focViewInfoVO1.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO1);
        DmFocViewInfoVO focViewInfoVO2 = new DmFocViewInfoVO();
        focViewInfoVO2.setViewFlag("2");
        focViewInfoVO2.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO2);
        DmFocViewInfoVO focViewInfoVO3 = new DmFocViewInfoVO();
        focViewInfoVO3.setViewFlag("3");
        focViewInfoVO3.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO3);
        DmFocViewInfoVO focViewInfoVO4 = new DmFocViewInfoVO();
        focViewInfoVO4.setViewFlag("4");
        focViewInfoVO4.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO4);
        Assertions.assertNotNull(annualCommonService.viewFlagInfoList(commonViewVO));
    }

    @Test
    public void viewFlagInfoList24Test() throws ApplicationException {
        CommonViewVO viewFlag = new CommonViewVO();
        viewFlag.setCaliberFlag("C");
        viewFlag.setGranularityType("U");
        viewFlag.setCostType("P");
        viewFlag.setIndustryOrg("ICT");
        viewFlag.setTablePreFix("dm_foc");
        List<ViewInfoVO> allProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("15332433");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        allProdDimensionList.add(viewInfoVO);
        ViewInfoVO viewInfoV1 = new ViewInfoVO();
        viewInfoV1.setViewFlag("3");
        viewInfoV1.setLv1ProdRndTeamCode("123324");
        viewInfoV1.setLv1ProdRnTeamName("无线");
        viewInfoV1.setLv2ProdRndTeamCode("1533243");
        viewInfoV1.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoV1.setLv3ProdRndTeamCode("140701");
        viewInfoV1.setLv3ProdRnTeamName("FDD NR");
        allProdDimensionList.add(viewInfoV1);
        ViewInfoVO viewInfoV2 = new ViewInfoVO();
        viewInfoV2.setViewFlag("3");
        viewInfoV2.setLv1ProdRndTeamCode("1223324");
        viewInfoV2.setLv1ProdRnTeamName("无线1");
        viewInfoV2.setLv2ProdRndTeamCode("15332423");
        viewInfoV2.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoV2.setLv3ProdRndTeamCode("1420701");
        viewInfoV2.setLv3ProdRnTeamName("FDD NR");
        allProdDimensionList.add(viewInfoV2);
        PowerMockito.doReturn(allProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(viewFlag.getCostType(),viewFlag.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("100001");
        lv1DimensionSet.add("123324");
        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        Set<String> lv2DimensionSet  = new HashSet<>();
        lv2DimensionSet.add("15332433");
        currentRoleDataPermission.setLv2DimensionSet(lv2DimensionSet);
        Set<String> lv3DimensionSet  = new HashSet<>();
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(viewFlag.getIndustryOrg());
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setLv1DimensionSet(lv1DimensionSet);
        commonViewVO.setLv2DimensionSet(lv2DimensionSet);

        List<DmFocViewInfoVO> viewFlagList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("0");
        focViewInfoVO.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO);

        DmFocViewInfoVO focViewInfoVO1 = new DmFocViewInfoVO();
        focViewInfoVO1.setViewFlag("1");
        focViewInfoVO1.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO1);
        DmFocViewInfoVO focViewInfoVO2 = new DmFocViewInfoVO();
        focViewInfoVO2.setViewFlag("2");
        focViewInfoVO2.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO2);
        DmFocViewInfoVO focViewInfoVO3 = new DmFocViewInfoVO();
        focViewInfoVO3.setViewFlag("3");
        focViewInfoVO3.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO3);
        DmFocViewInfoVO focViewInfoVO4 = new DmFocViewInfoVO();
        focViewInfoVO4.setViewFlag("4");
        focViewInfoVO4.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO4);
        Assertions.assertNotNull(annualCommonService.viewFlagInfoList(commonViewVO));
    }

    @Test
    public void viewFlagInfoList25Test() throws ApplicationException {
        CommonViewVO viewFlag = new CommonViewVO();
        viewFlag.setCaliberFlag("C");
        viewFlag.setGranularityType("U");
        viewFlag.setCostType("P");
        viewFlag.setIndustryOrg("ICT");
        viewFlag.setTablePreFix("dm_foc");
        List<ViewInfoVO> allProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("15332433");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        allProdDimensionList.add(viewInfoVO);
        ViewInfoVO viewInfoV1 = new ViewInfoVO();
        viewInfoV1.setViewFlag("3");
        viewInfoV1.setLv1ProdRndTeamCode("123324");
        viewInfoV1.setLv1ProdRnTeamName("无线");
        viewInfoV1.setLv2ProdRndTeamCode("1533243");
        viewInfoV1.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoV1.setLv3ProdRndTeamCode("140701");
        viewInfoV1.setLv3ProdRnTeamName("FDD NR");
        allProdDimensionList.add(viewInfoV1);
        ViewInfoVO viewInfoV2 = new ViewInfoVO();
        viewInfoV2.setViewFlag("3");
        viewInfoV2.setLv1ProdRndTeamCode("1223324");
        viewInfoV2.setLv1ProdRnTeamName("无线1");
        viewInfoV2.setLv2ProdRndTeamCode("15332423");
        viewInfoV2.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoV2.setLv3ProdRndTeamCode("1420701");
        viewInfoV2.setLv3ProdRnTeamName("FDD NR");
        allProdDimensionList.add(viewInfoV2);
        PowerMockito.doReturn(allProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(viewFlag.getCostType(),viewFlag.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("100001");
        lv1DimensionSet.add("123324");
        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        Set<String> lv2DimensionSet  = new HashSet<>();
        lv2DimensionSet.add("15332433");
        currentRoleDataPermission.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(viewFlag.getIndustryOrg());
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setLv1DimensionSet(lv1DimensionSet);
        commonViewVO.setLv2DimensionSet(lv2DimensionSet);
        List<DmFocViewInfoVO> viewFlagList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("0");
        focViewInfoVO.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO);

        DmFocViewInfoVO focViewInfoVO1 = new DmFocViewInfoVO();
        focViewInfoVO1.setViewFlag("1");
        focViewInfoVO1.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO1);
        DmFocViewInfoVO focViewInfoVO2 = new DmFocViewInfoVO();
        focViewInfoVO2.setViewFlag("2");
        focViewInfoVO2.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO2);
        DmFocViewInfoVO focViewInfoVO3 = new DmFocViewInfoVO();
        focViewInfoVO3.setViewFlag("3");
        focViewInfoVO3.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO3);
        DmFocViewInfoVO focViewInfoVO4 = new DmFocViewInfoVO();
        focViewInfoVO4.setViewFlag("4");
        focViewInfoVO4.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO4);
        Assertions.assertNotNull(annualCommonService.viewFlagInfoList(commonViewVO));
    }

    @Test
    public void viewFlagInfoList3Test() throws ApplicationException {
        CommonViewVO viewFlag = new CommonViewVO();
        viewFlag.setCaliberFlag("C");
        viewFlag.setGranularityType("U");
        viewFlag.setCostType("P");
        viewFlag.setIndustryOrg("ICT");
        viewFlag.setTablePreFix("dm_foc");
        List<ViewInfoVO> allProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        allProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(allProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(viewFlag.getCostType(),viewFlag.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("12324");
        lv1DimensionSet.add("123324");
        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        Set<String> lv2DimensionSet  = new HashSet<>();
        lv2DimensionSet.add("12324");
        lv2DimensionSet.add("1236324");
        currentRoleDataPermission.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(viewFlag.getIndustryOrg());
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setLv1DimensionSet(lv1DimensionSet);
        commonViewVO.setLv2DimensionSet(lv2DimensionSet);

        List<DmFocViewInfoVO> viewFlagList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("0");
        focViewInfoVO.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO);
        DmFocViewInfoVO focViewInfoVO1 = new DmFocViewInfoVO();
        focViewInfoVO1.setViewFlag("1");
        focViewInfoVO1.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO1);
        DmFocViewInfoVO focViewInfoVO2 = new DmFocViewInfoVO();
        focViewInfoVO2.setViewFlag("2");
        focViewInfoVO2.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO2);
        DmFocViewInfoVO focViewInfoVO3 = new DmFocViewInfoVO();
        focViewInfoVO3.setViewFlag("3");
        focViewInfoVO3.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO3);
        DmFocViewInfoVO focViewInfoVO4 = new DmFocViewInfoVO();
        focViewInfoVO4.setViewFlag("4");
        focViewInfoVO4.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO4);
        PowerMockito.doReturn(viewFlagList).when(dmFocPftViewInfoDao).viewFlagInfoProfitsList(any());
        Assertions.assertNotNull(annualCommonService.viewFlagInfoList(commonViewVO));
    }

    @Test
    public void viewFlagInfoList31() throws ApplicationException {
        CommonViewVO viewFlag = new CommonViewVO();
        viewFlag.setCaliberFlag("C");
        viewFlag.setGranularityType("U");
        viewFlag.setCostType("P");
        viewFlag.setIndustryOrg("ICT");
        viewFlag.setTablePreFix("dm_foc");
        List<ViewInfoVO> allProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        allProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(allProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(viewFlag.getCostType(),viewFlag.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("12324");
        lv1DimensionSet.add("123324");
        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        Set<String> lv2DimensionSet  = new HashSet<>();
        lv2DimensionSet.add("12324");
        lv2DimensionSet.add("1236324");
        currentRoleDataPermission.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(viewFlag.getIndustryOrg());
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setLv0DimensionSet(lv1DimensionSet);
        List<DmFocViewInfoVO> viewFlagList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("0");
        focViewInfoVO.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO);

        DmFocViewInfoVO focViewInfoVO1 = new DmFocViewInfoVO();
        focViewInfoVO1.setViewFlag("1");
        focViewInfoVO1.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO1);
        DmFocViewInfoVO focViewInfoVO2 = new DmFocViewInfoVO();
        focViewInfoVO2.setViewFlag("2");
        focViewInfoVO2.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO2);
        DmFocViewInfoVO focViewInfoVO3 = new DmFocViewInfoVO();
        focViewInfoVO3.setViewFlag("3");
        focViewInfoVO3.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO3);
        DmFocViewInfoVO focViewInfoVO4 = new DmFocViewInfoVO();
        focViewInfoVO4.setViewFlag("4");
        focViewInfoVO4.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO4);
        PowerMockito.doReturn(viewFlagList).when(dmFocPftViewInfoDao).viewFlagInfoProfitsList(any());
        Assertions.assertNotNull(annualCommonService.viewFlagInfoList(commonViewVO));
    }

    @Test
    public void viewFlagInfoList32Test() throws ApplicationException {
        CommonViewVO viewFlag = new CommonViewVO();
        viewFlag.setCaliberFlag("C");
        viewFlag.setGranularityType("U");
        viewFlag.setCostType("P");
        viewFlag.setIndustryOrg("ICT");
        viewFlag.setTablePreFix("dm_foc");
        List<ViewInfoVO> allProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        allProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(allProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(viewFlag.getCostType(),viewFlag.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("12324");
        lv1DimensionSet.add("123324");
        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        Set<String> lv2DimensionSet  = new HashSet<>();
        lv2DimensionSet.add("12324");
        lv2DimensionSet.add("1236324");
        currentRoleDataPermission.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(viewFlag.getIndustryOrg());
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setLv1DimensionSet(lv1DimensionSet);
        List<DmFocViewInfoVO> viewFlagList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("0");
        focViewInfoVO.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO);

        DmFocViewInfoVO focViewInfoVO1 = new DmFocViewInfoVO();
        focViewInfoVO1.setViewFlag("1");
        focViewInfoVO1.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO1);
        DmFocViewInfoVO focViewInfoVO2 = new DmFocViewInfoVO();
        focViewInfoVO2.setViewFlag("2");
        focViewInfoVO2.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO2);
        DmFocViewInfoVO focViewInfoVO3 = new DmFocViewInfoVO();
        focViewInfoVO3.setViewFlag("3");
        focViewInfoVO3.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO3);
        DmFocViewInfoVO focViewInfoVO4 = new DmFocViewInfoVO();
        focViewInfoVO4.setViewFlag("4");
        focViewInfoVO4.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO4);
        PowerMockito.doReturn(viewFlagList).when(dmFocPftViewInfoDao).viewFlagInfoProfitsList(any());
        Assertions.assertNotNull(annualCommonService.viewFlagInfoList(commonViewVO));
    }

    @Test
    public void viewFlagInfoList4Test() throws ApplicationException {
        CommonViewVO viewFlag = new CommonViewVO();
        viewFlag.setCaliberFlag("C");
        viewFlag.setGranularityType("U");
        viewFlag.setCostType("P");
        viewFlag.setIndustryOrg("ICT");
        viewFlag.setTablePreFix("dm_foc");
        List<ViewInfoVO> allProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        allProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(allProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(viewFlag.getCostType(),viewFlag.getTablePreFix());
        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("12324");
        lv1DimensionSet.add("1233324");
        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        Set<String> lv2DimensionSet  = new HashSet<>();
        lv2DimensionSet.add("12324");
        lv2DimensionSet.add("12324");
        currentRoleDataPermission.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(viewFlag.getIndustryOrg());
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setLv1DimensionSet(lv1DimensionSet);
        commonViewVO.setLv2DimensionSet(lv2DimensionSet);

        List<DmFocViewInfoVO> viewFlagList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("0");
        focViewInfoVO.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO);

        DmFocViewInfoVO focViewInfoVO1 = new DmFocViewInfoVO();
        focViewInfoVO1.setViewFlag("1");
        focViewInfoVO1.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO1);
        DmFocViewInfoVO focViewInfoVO2 = new DmFocViewInfoVO();
        focViewInfoVO2.setViewFlag("2");
        focViewInfoVO2.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO2);
        DmFocViewInfoVO focViewInfoVO3 = new DmFocViewInfoVO();
        focViewInfoVO3.setViewFlag("3");
        focViewInfoVO3.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO3);
        DmFocViewInfoVO focViewInfoVO4 = new DmFocViewInfoVO();
        focViewInfoVO4.setViewFlag("4");
        focViewInfoVO4.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO4);
        PowerMockito.doReturn(viewFlagList).when(dmFocPftViewInfoDao).viewFlagInfoProfitsList(any());
        Assertions.assertNotNull(annualCommonService.viewFlagInfoList(commonViewVO));
    }


    @Test
    public void viewFlagInfoList5Test() throws ApplicationException {
        CommonViewVO viewFlag = new CommonViewVO();
        viewFlag.setCaliberFlag("C");
        viewFlag.setGranularityType("U");
        viewFlag.setCostType("P");
        viewFlag.setIndustryOrg("ICT");
        viewFlag.setTablePreFix("dm_foc");
        List<ViewInfoVO> allProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("3");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        allProdDimensionList.add(viewInfoVO);
        ViewInfoVO viewInfoV1 = new ViewInfoVO();
        viewInfoV1.setViewFlag("3");
        viewInfoV1.setLv1ProdRndTeamCode("100003");
        viewInfoV1.setLv1ProdRnTeamName("光");
        viewInfoV1.setLv2ProdRndTeamCode("1533242");
        viewInfoV1.setLv2ProdRnTeamName("5G&LTE TDD");
        viewInfoV1.setLv3ProdRndTeamCode("140731");
        viewInfoV1.setLv3ProdRnTeamName("FDD TR");
        allProdDimensionList.add(viewInfoV1);
        PowerMockito.doReturn(allProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(viewFlag.getCostType(),viewFlag.getTablePreFix());

        DataPermissionsVO currentRoleDataPermission = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        lv1DimensionSet.add("100001");
        currentRoleDataPermission.setLv1DimensionSet(lv1DimensionSet);
        Set<String> lv2DimensionSet  = new HashSet<>();
        lv2DimensionSet.add("15334324");
        currentRoleDataPermission.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(currentRoleDataPermission).when(commonService).getCurrentRoleDataPermission(viewFlag.getIndustryOrg());
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setLv1DimensionSet(lv1DimensionSet);
        commonViewVO.setLv2DimensionSet(lv2DimensionSet);

        List<DmFocViewInfoVO> viewFlagList = new ArrayList<>();
        DmFocViewInfoVO focViewInfoVO = new DmFocViewInfoVO();
        focViewInfoVO.setViewFlag("0");
        focViewInfoVO.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO);

        DmFocViewInfoVO focViewInfoVO1 = new DmFocViewInfoVO();
        focViewInfoVO1.setViewFlag("1");
        focViewInfoVO1.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO1);
        DmFocViewInfoVO focViewInfoVO2 = new DmFocViewInfoVO();
        focViewInfoVO2.setViewFlag("2");
        focViewInfoVO2.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO2);
        DmFocViewInfoVO focViewInfoVO3 = new DmFocViewInfoVO();
        focViewInfoVO3.setViewFlag("3");
        focViewInfoVO3.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO3);
        DmFocViewInfoVO focViewInfoVO4 = new DmFocViewInfoVO();
        focViewInfoVO4.setViewFlag("4");
        focViewInfoVO4.setProdRndTeamCode("104364");
        viewFlagList.add(focViewInfoVO4);
        PowerMockito.doReturn(viewFlagList).when(dmFocPftViewInfoDao).viewFlagInfoProfitsList(any());
        Assertions.assertNotNull(annualCommonService.viewFlagInfoList(commonViewVO));
    }

    @Test
    public void viewInfoListTest() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        DataPermissionsVO dataPermissionsVO = JSONObject.parseObject(viewInfoJson.getString("DataPermissionsVO"),DataPermissionsVO.class);
        Long longArg = viewInfoJson.getLong("Long");
        List<ViewInfoVO> viewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<ViewInfoVO>"),ViewInfoVO.class);
        DmFocVersionInfoDTO dmFocVersionInfoDTO = JSONObject.parseObject(viewInfoJson.getString("DmFocVersionInfoDTO"),DmFocVersionInfoDTO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        when(dmFocViewInfoDao.getCombinationParent(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationByGroupLevel(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.reverseFindLv1ProdCode(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.reverseFindLv1ProdCodeMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(dmFocVersionInfoDTO);
        when(commonService.getVersionId(any(String.class),any())).thenReturn(longArg);
        when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(dataPermissionsVO);
        when(dataDimensionService.getCurrentLv2ProdRndTeamList("P","dm_foc")).thenReturn(viewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        ResultDataVO result = annualCommonService.viewInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        commonViewVO.setCustomIdList(null);
        result = annualCommonService.viewInfoList(commonViewVO);
        Assertions.assertNotNull(result);
    }


    @Test
    public void getAnnualPeriodYearTest() {
        ResultDataVO result = annualCommonService.getAnnualPeriodYear("dm_foc","ICT");
        Assertions.assertNotNull(result);
    }

    @Test
    public void getAnnualPeriodYearTest2T() {
        ResultDataVO result = annualCommonService.getAnnualPeriodYear("dm_foc","IAS");
        Assertions.assertNotNull(result);
    }

    @Test
    public void getAnnualPeriodYearTest3T() {
        ResultDataVO result = annualCommonService.getAnnualPeriodYear("dm_foc","ENERGY");
        Assertions.assertNotNull(result);
    }

    @Test
    public void getBgInfoListTest() {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        when(dmFocViewInfoDao.getLv0ProdList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        ResultDataVO result = annualCommonService.getBgInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        commonViewVO.setViewFlag("6");
        result = annualCommonService.getBgInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        commonViewVO.setViewFlag("5");
        result = annualCommonService.getBgInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        commonViewVO.setViewFlag("4");
        result = annualCommonService.getBgInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        commonViewVO.setGranularityType("4");
        result = annualCommonService.getBgInfoList(commonViewVO);
        Assertions.assertNotNull(result);
    }

    @Test
    public void getBgInfoList2Test() {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        when(dmFocViewInfoDao.getLv0ProdList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        ResultDataVO result = annualCommonService.getBgInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        commonViewVO.setViewFlag("6");
        result = annualCommonService.getBgInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        commonViewVO.setViewFlag("5");
        result = annualCommonService.getBgInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        commonViewVO.setViewFlag("4");
        result = annualCommonService.getBgInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        commonViewVO.setGranularityType("4");
        commonViewVO.setCostType("M");
        result = annualCommonService.getBgInfoList(commonViewVO);
        Assertions.assertNotNull(result);
    }

    @Test
    public void getBgInfoList3Test() {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        when(dmFocViewInfoDao.getLv0ProdList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        ResultDataVO result = annualCommonService.getBgInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        commonViewVO.setViewFlag("6");
        result = annualCommonService.getBgInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        commonViewVO.setViewFlag("5");
        result = annualCommonService.getBgInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        commonViewVO.setViewFlag("4");
        result = annualCommonService.getBgInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        commonViewVO.setGranularityType("4");
        commonViewVO.setCostType("T");
        result = annualCommonService.getBgInfoList(commonViewVO);
        Assertions.assertNotNull(result);
    }

    @Test
    public void getDmViewInfoVOListTest() {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        DataPermissionsVO dataPermissionsVO = JSONObject.parseObject(viewInfoJson.getString("DataPermissionsVO"),DataPermissionsVO.class);
        Long longArg = viewInfoJson.getLong("Long");
        List<ViewInfoVO> viewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<ViewInfoVO>"),ViewInfoVO.class);
        DmFocVersionInfoDTO dmFocVersionInfoDTO = JSONObject.parseObject(viewInfoJson.getString("DmFocVersionInfoDTO"),DmFocVersionInfoDTO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        when(dmFocViewInfoDao.getCombinationParent(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationByGroupLevel(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.reverseFindLv1ProdCode(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.reverseFindLv1ProdCodeMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(dmFocVersionInfoDTO);
        when(commonService.getVersionId(any(String.class),any())).thenReturn(longArg);
        when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(dataPermissionsVO);
        when(dataDimensionService.getCurrentLv2ProdRndTeamList("P","dm_foc")).thenReturn(viewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        IndustryIndexEnum.GRANULARITY_TYPE granule = IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL;

        List<DmFocViewInfoVO> result = annualCommonService.getDmViewInfoVOList(commonViewVO,granule,true);
        Assertions.assertNotNull(result);
        commonViewVO.setPageFlag("MONTH");
        result = annualCommonService.getDmViewInfoVOList(commonViewVO,granule,true);
        Assertions.assertNotNull(result);
        granule = IndustryIndexEnum.GRANULARITY_TYPE.PROFITS;
        result = annualCommonService.getDmViewInfoVOList(commonViewVO,granule,true);
        Assertions.assertNotNull(result);
        granule = IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION;
        result = annualCommonService.getDmViewInfoVOList(commonViewVO,granule,true);
        Assertions.assertNotNull(result);
        commonViewVO.setPageFlag("Test");
        result = annualCommonService.getDmViewInfoVOList(commonViewVO,granule,true);
        Assertions.assertNotNull(result);
        result = annualCommonService.getDmViewInfoVOList(commonViewVO,granule,false);
        Assertions.assertNotNull(result);
    }

    @Test
    public void isContainCombTest() throws Exception {
        CommonViewVO commonViewVO = new CommonViewVO();
        commonViewVO.setPermissionTag(false);
        Boolean result = Whitebox.invokeMethod(annualCommonService, "isContainComb", commonViewVO);
        Assertions.assertFalse(result);
        commonViewVO.setKeyWord("dd");
        result = Whitebox.invokeMethod(annualCommonService, "isContainComb", commonViewVO);
        Assertions.assertTrue(result);
        commonViewVO.setGroupCodeList(Arrays.asList("dd"));
        result = Whitebox.invokeMethod(annualCommonService, "isContainComb", commonViewVO);
        Assertions.assertTrue(result);
        commonViewVO.setPermissionTag(true);
        result = Whitebox.invokeMethod(annualCommonService, "isContainComb", commonViewVO);
        Assertions.assertTrue(result);
    }

    @Test
    public void setInitSearchViewVOTest() {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        DataPermissionsVO dataPermissionsVO = JSONObject.parseObject(viewInfoJson.getString("DataPermissionsVO"),DataPermissionsVO.class);
        Long longArg = viewInfoJson.getLong("Long");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = JSONObject.parseObject(viewInfoJson.getString("DmFocVersionInfoDTO"),DmFocVersionInfoDTO.class);
        when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(dmFocVersionInfoDTO);
        when(commonService.getVersionId(any(String.class),any())).thenReturn(longArg);
        when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(dataPermissionsVO);
        commonViewVO.setCostType("T");
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(totalAnnualAmpDao).getTotalAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        annualCommonService.setInitSearchViewVO(commonViewVO);
        verify(commonService, atLeastOnce()).getCurrentRoleDataPermission("ICT");
    }

    @Test
    public void reverseViewInfoListTest() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        DataPermissionsVO dataPermissionsVO = JSONObject.parseObject(viewInfoJson.getString("DataPermissionsVO"),DataPermissionsVO.class);
        Long longArg = viewInfoJson.getLong("Long");
        List<ViewInfoVO> viewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<ViewInfoVO>"),ViewInfoVO.class);
        DmFocVersionInfoDTO dmFocVersionInfoDTO = JSONObject.parseObject(viewInfoJson.getString("DmFocVersionInfoDTO"),DmFocVersionInfoDTO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        when(dmFocViewInfoDao.reverseFindLv1ProdCode(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.reverseFindLv1ProdCodeMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationParent(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationByGroupLevel(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.revViewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.revViewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(dmFocVersionInfoDTO);
        when(commonService.getVersionId(any(String.class),any())).thenReturn(longArg);
        when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(dataPermissionsVO);
        when(dataDimensionService.getCurrentLv2ProdRndTeamList("P","dm_foc")).thenReturn(viewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualAmpDao).getAnnualPeriodYear(commonViewVO.getTablePreFix(),10L);
        ResultDataVO result = annualCommonService.reverseViewInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        commonViewVO.setPageFlag("MONTH");
        dmFocViewInfoVOList.forEach(obj -> {obj.setGroupLevel("LV1"); obj.setGroupCode("100001");});
        result = annualCommonService.reverseViewInfoList(commonViewVO);
        Assertions.assertNotNull(result);
    }

    @Test
    public void reverseViewInfoList1Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        DataPermissionsVO dataPermissionsVO = JSONObject.parseObject(viewInfoJson.getString("DataPermissionsVO"),DataPermissionsVO.class);
        Long longArg = viewInfoJson.getLong("Long");
        List<ViewInfoVO> viewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<ViewInfoVO>"),ViewInfoVO.class);
        DmFocVersionInfoDTO dmFocVersionInfoDTO = JSONObject.parseObject(viewInfoJson.getString("DmFocVersionInfoDTO"),DmFocVersionInfoDTO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        when(dmFocViewInfoDao.reverseFindLv1ProdCode(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.reverseFindLv1ProdCodeMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationParent(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationByGroupLevel(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.revViewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.revViewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(dmFocVersionInfoDTO);
        when(commonService.getVersionId(any(String.class),any())).thenReturn(longArg);
        when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(dataPermissionsVO);
        when(dataDimensionService.getCurrentLv2ProdRndTeamList("P","dm_foc")).thenReturn(viewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        commonViewVO.setCostType("M");
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(madeAnnualAmpDao).getMadeAnnualPeriodYear(commonViewVO.getTablePreFix(),12L);
        ResultDataVO result = annualCommonService.reverseViewInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        commonViewVO.setPageFlag("MONTH");
        dmFocViewInfoVOList.forEach(obj -> {obj.setGroupLevel("LV1"); obj.setGroupCode("100001");});
        result = annualCommonService.reverseViewInfoList(commonViewVO);
        Assertions.assertNotNull(result);
    }

    @Test
    public void reverseViewInfoList2Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        DataPermissionsVO dataPermissionsVO = JSONObject.parseObject(viewInfoJson.getString("DataPermissionsVO"),DataPermissionsVO.class);
        Long longArg = viewInfoJson.getLong("Long");
        List<ViewInfoVO> viewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<ViewInfoVO>"),ViewInfoVO.class);
        DmFocVersionInfoDTO dmFocVersionInfoDTO = JSONObject.parseObject(viewInfoJson.getString("DmFocVersionInfoDTO"),DmFocVersionInfoDTO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        when(dmFocViewInfoDao.reverseFindLv1ProdCode(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.reverseFindLv1ProdCodeMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationParent(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationByGroupLevel(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.revViewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.revViewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(dmFocVersionInfoDTO);
        when(commonService.getVersionId(any(String.class),any())).thenReturn(longArg);
        when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(dataPermissionsVO);
        when(dataDimensionService.getCurrentLv2ProdRndTeamList("P","dm_foc")).thenReturn(viewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocMadeViewInfoDao.madeRevViewInfoList(any())).thenReturn(dmFocViewInfoVOList);
        commonViewVO.setCostType("M");
        commonViewVO.setViewFlag("4");
        commonViewVO.setPurLevel("CEG");
        commonViewVO.setTeamLevel("ICT");
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(madeAnnualAmpDao).getMadeAnnualPeriodYear(commonViewVO.getTablePreFix(),12L);
        ResultDataVO result = annualCommonService.reverseViewInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        commonViewVO.setPageFlag("MONTH");
        dmFocViewInfoVOList.forEach(obj -> {obj.setGroupLevel("LV1"); obj.setGroupCode("100001");});
        result = annualCommonService.reverseViewInfoList(commonViewVO);
        Assertions.assertNotNull(result);
    }

    @Test
    public void reverseViewInfoList3Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        DataPermissionsVO dataPermissionsVO = JSONObject.parseObject(viewInfoJson.getString("DataPermissionsVO"),DataPermissionsVO.class);
        Long longArg = viewInfoJson.getLong("Long");
        List<ViewInfoVO> viewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<ViewInfoVO>"),ViewInfoVO.class);
        DmFocVersionInfoDTO dmFocVersionInfoDTO = JSONObject.parseObject(viewInfoJson.getString("DmFocVersionInfoDTO"),DmFocVersionInfoDTO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        when(dmFocViewInfoDao.reverseFindLv1ProdCode(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.reverseFindLv1ProdCodeMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationParent(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationByGroupLevel(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.revViewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.revViewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(dmFocVersionInfoDTO);
        when(commonService.getVersionId(any(String.class),any())).thenReturn(longArg);
        when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(dataPermissionsVO);
        when(dataDimensionService.getCurrentLv2ProdRndTeamList("P","dm_foc")).thenReturn(viewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocMadeViewInfoDao.madeRevViewInfoList(any())).thenReturn(dmFocViewInfoVOList);
        commonViewVO.setCostType("M");
        commonViewVO.setViewFlag("5");
        commonViewVO.setPurLevel("MODL");
        commonViewVO.setTeamLevel("ICT");
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(madeAnnualAmpDao).getMadeAnnualPeriodYear(commonViewVO.getTablePreFix(),12L);
        ResultDataVO result = annualCommonService.reverseViewInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        commonViewVO.setPageFlag("MONTH");
        dmFocViewInfoVOList.forEach(obj -> {obj.setGroupLevel("LV1"); obj.setGroupCode("100001");});

        result = annualCommonService.reverseViewInfoList(commonViewVO);
        Assertions.assertNotNull(result);
    }

    @Test
    public void reverseViewInfoList4Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        DataPermissionsVO dataPermissionsVO = JSONObject.parseObject(viewInfoJson.getString("DataPermissionsVO"),DataPermissionsVO.class);
        Long longArg = viewInfoJson.getLong("Long");
        List<ViewInfoVO> viewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<ViewInfoVO>"),ViewInfoVO.class);
        DmFocVersionInfoDTO dmFocVersionInfoDTO = JSONObject.parseObject(viewInfoJson.getString("DmFocVersionInfoDTO"),DmFocVersionInfoDTO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        when(dmFocViewInfoDao.reverseFindLv1ProdCode(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.reverseFindLv1ProdCodeMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationParent(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationByGroupLevel(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.revViewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.revViewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(dmFocVersionInfoDTO);
        when(commonService.getVersionId(any(String.class),any())).thenReturn(longArg);
        when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(dataPermissionsVO);
        when(dataDimensionService.getCurrentLv2ProdRndTeamList("P","dm_foc")).thenReturn(viewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocMadeViewInfoDao.madeRevViewInfoList(any())).thenReturn(dmFocViewInfoVOList);
        commonViewVO.setCostType("M");
        commonViewVO.setViewFlag("6");
        commonViewVO.setPurLevel("CATEGORY");
        commonViewVO.setTeamLevel("ICT");
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(madeAnnualAmpDao).getMadeAnnualPeriodYear(commonViewVO.getTablePreFix(),12L);
        ResultDataVO result = annualCommonService.reverseViewInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        commonViewVO.setPageFlag("MONTH");
        dmFocViewInfoVOList.forEach(obj -> {obj.setGroupLevel("LV1"); obj.setGroupCode("100001");});
        result = annualCommonService.reverseViewInfoList(commonViewVO);
        Assertions.assertNotNull(result);
    }

    @Test
    public void reverseViewInfoList5Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        DataPermissionsVO dataPermissionsVO = JSONObject.parseObject(viewInfoJson.getString("DataPermissionsVO"),DataPermissionsVO.class);
        Long longArg = viewInfoJson.getLong("Long");
        List<ViewInfoVO> viewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<ViewInfoVO>"),ViewInfoVO.class);
        DmFocVersionInfoDTO dmFocVersionInfoDTO = JSONObject.parseObject(viewInfoJson.getString("DmFocVersionInfoDTO"),DmFocVersionInfoDTO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        when(dmFocViewInfoDao.reverseFindLv1ProdCode(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.reverseFindLv1ProdCodeMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationParent(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationByGroupLevel(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.revViewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.revViewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(dmFocVersionInfoDTO);
        when(commonService.getVersionId(any(String.class),any())).thenReturn(longArg);
        when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(dataPermissionsVO);
        when(dataDimensionService.getCurrentLv2ProdRndTeamList("P","dm_foc")).thenReturn(viewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocMadeViewInfoDao.madeRevViewInfoList(any())).thenReturn(dmFocViewInfoVOList);
        commonViewVO.setCostType("M");
        commonViewVO.setViewFlag("6");
        commonViewVO.setPurLevel("CATEGORY");
        commonViewVO.setTeamLevel("LV1");
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(madeAnnualAmpDao).getMadeAnnualPeriodYear(commonViewVO.getTablePreFix(),12L);
        ResultDataVO result = annualCommonService.reverseViewInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        commonViewVO.setPageFlag("MONTH");
        dmFocViewInfoVOList.forEach(obj -> {obj.setGroupLevel("LV1"); obj.setGroupCode("100001");});

        result = annualCommonService.reverseViewInfoList(commonViewVO);
        Assertions.assertNotNull(result);
    }

    @Test
    public void reverseViewInfoList6Test() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        DataPermissionsVO dataPermissionsVO = JSONObject.parseObject(viewInfoJson.getString("DataPermissionsVO"),DataPermissionsVO.class);
        Long longArg = viewInfoJson.getLong("Long");
        List<ViewInfoVO> viewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<ViewInfoVO>"),ViewInfoVO.class);
        DmFocVersionInfoDTO dmFocVersionInfoDTO = JSONObject.parseObject(viewInfoJson.getString("DmFocVersionInfoDTO"),DmFocVersionInfoDTO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        when(dmFocViewInfoDao.reverseFindLv1ProdCode(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.reverseFindLv1ProdCodeMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationParent(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationByGroupLevel(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.revViewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.revViewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(dmFocVersionInfoDTO);
        when(commonService.getVersionId(any(String.class),any())).thenReturn(longArg);
        when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(dataPermissionsVO);
        when(dataDimensionService.getCurrentLv2ProdRndTeamList("P","dm_foc")).thenReturn(viewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocMadeViewInfoDao.madeRevViewInfoList(any())).thenReturn(dmFocViewInfoVOList);
        commonViewVO.setCostType("M");
        commonViewVO.setViewFlag("6");
        commonViewVO.setPurLevel("CATEGORY");
        commonViewVO.setTeamLevel("LV1");
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(madeAnnualAmpDao).getMadeAnnualPeriodYear(commonViewVO.getTablePreFix(),12L);
        ResultDataVO result = annualCommonService.reverseViewInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        commonViewVO.setPageFlag("ANNUAL");
        dmFocViewInfoVOList.forEach(obj -> {obj.setGroupLevel("LV1"); obj.setGroupCode("100001");});

        result = annualCommonService.reverseViewInfoList(commonViewVO);
        Assertions.assertNotNull(result);
    }

    @Test
    public void queryDataRefreshStatusTest() throws ApplicationException {
        DmFocDataRefreshStatus dataRefreshStatus = JSONObject.parseObject(viewInfoJson.getString("DmFocDataRefreshStatus"),DmFocDataRefreshStatus.class);
        ResultDataVO result = annualCommonService.queryDataRefreshStatus(dataRefreshStatus);
        Assertions.assertNotNull(result);

        try {
            dataRefreshStatus.setStatus("TASK_FAIL");
            annualCommonService.queryDataRefreshStatus(dataRefreshStatus);
        } catch (ApplicationException e) {
            assertThatExceptionOfType(ApplicationException.class);
        }

        try {
            dataRefreshStatus.setTaskId(0L);
            annualCommonService.queryDataRefreshStatus(dataRefreshStatus);
        } catch (ApplicationException e) {
            assertThatExceptionOfType(ApplicationException.class);
        }

        try {
            dataRefreshStatus.setTaskId(null);
            annualCommonService.queryDataRefreshStatus(dataRefreshStatus);
        } catch (ApplicationException e) {
            assertThatExceptionOfType(ApplicationException.class);
        }
    }

    @Test
    public void handlePermissionTeamLevelTest() {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        DataPermissionsVO dataPermissionsVO = JSONObject.parseObject(viewInfoJson.getString("DataPermissionsVO"),DataPermissionsVO.class);
        Long longArg = viewInfoJson.getLong("Long");
        List<ViewInfoVO> viewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<ViewInfoVO>"),ViewInfoVO.class);
        DmFocVersionInfoDTO dmFocVersionInfoDTO = JSONObject.parseObject(viewInfoJson.getString("DmFocVersionInfoDTO"),DmFocVersionInfoDTO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        List<DmFocViewInfoVO> dmFocAnnualAmpVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        when(dmFocViewInfoDao.reverseFindLv1ProdCode(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.reverseFindLv1ProdCodeMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationParent(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationByGroupLevel(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(dmFocVersionInfoDTO);
        when(commonService.getVersionId(any(String.class),any())).thenReturn(longArg);
        when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(dataPermissionsVO);
        when(dataDimensionService.getCurrentLv2ProdRndTeamList("P","dm_foc")).thenReturn(viewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        annualCommonService.handlePermissionTeamLevel(dmFocAnnualAmpVOList,commonViewVO);
        assertThatNoException();
        verify(dataDimensionService, atLeastOnce()).getCurrentLv2ProdRndTeamList("P","dm_foc");
    }

    @Test
    public void handlePermissionTeamLevel2Test() {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        DataPermissionsVO dataPermissionsVO = JSONObject.parseObject(viewInfoJson.getString("DataPermissionsVO"),DataPermissionsVO.class);
        Long longArg = viewInfoJson.getLong("Long");
        commonViewVO.setViewFlag("2");
        commonViewVO.setTeamLevel("ICT");
        commonViewVO.setCostType("M");
        List<ViewInfoVO> viewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<ViewInfoVO>"),ViewInfoVO.class);
        DmFocVersionInfoDTO dmFocVersionInfoDTO = JSONObject.parseObject(viewInfoJson.getString("DmFocVersionInfoDTO"),DmFocVersionInfoDTO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        List<DmFocViewInfoVO> dmFocAnnualAmpVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        when(dmFocViewInfoDao.reverseFindLv1ProdCode(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.reverseFindLv1ProdCodeMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationParent(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationByGroupLevel(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(dmFocVersionInfoDTO);
        when(commonService.getVersionId(any(String.class),any())).thenReturn(longArg);
        when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(dataPermissionsVO);
        when(dataDimensionService.getCurrentLv2ProdRndTeamList("P","dm_foc")).thenReturn(viewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        annualCommonService.handlePermissionTeamLevel(dmFocAnnualAmpVOList,commonViewVO);
        assertThatNoException();
    }

    @Test
    public void handlePermissionTeamLevel3Test() {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        DataPermissionsVO dataPermissionsVO = JSONObject.parseObject(viewInfoJson.getString("DataPermissionsVO"),DataPermissionsVO.class);
        Long longArg = viewInfoJson.getLong("Long");
        commonViewVO.setViewFlag("2");
        commonViewVO.setTeamLevel("ICT");
        commonViewVO.setCostType("T");
        List<ViewInfoVO> viewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<ViewInfoVO>"),ViewInfoVO.class);
        DmFocVersionInfoDTO dmFocVersionInfoDTO = JSONObject.parseObject(viewInfoJson.getString("DmFocVersionInfoDTO"),DmFocVersionInfoDTO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        List<DmFocViewInfoVO> dmFocAnnualAmpVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        when(dmFocViewInfoDao.reverseFindLv1ProdCode(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.reverseFindLv1ProdCodeMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationParent(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationByGroupLevel(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(dmFocVersionInfoDTO);
        when(commonService.getVersionId(any(String.class),any())).thenReturn(longArg);
        when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(dataPermissionsVO);
        when(dataDimensionService.getCurrentLv2ProdRndTeamList("P","dm_foc")).thenReturn(viewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        annualCommonService.handlePermissionTeamLevel(dmFocAnnualAmpVOList,commonViewVO);
        assertThatNoException();
    }

    @Test
    public void handlePermissionTeamLevel4Test() {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        DataPermissionsVO dataPermissionsVO = JSONObject.parseObject(viewInfoJson.getString("DataPermissionsVO"),DataPermissionsVO.class);
        Long longArg = viewInfoJson.getLong("Long");
        commonViewVO.setViewFlag("2");
        commonViewVO.setTeamLevel("ICT");
        commonViewVO.setCostType("M");
        commonViewVO.setPageFlag("ANNUAL");
        List<ViewInfoVO> viewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<ViewInfoVO>"),ViewInfoVO.class);
        DmFocVersionInfoDTO dmFocVersionInfoDTO = JSONObject.parseObject(viewInfoJson.getString("DmFocVersionInfoDTO"),DmFocVersionInfoDTO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        List<DmFocViewInfoVO> dmFocAnnualAmpVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        when(dmFocViewInfoDao.reverseFindLv1ProdCode(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.reverseFindLv1ProdCodeMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationParent(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationByGroupLevel(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(dmFocVersionInfoDTO);
        when(commonService.getVersionId(any(String.class),any())).thenReturn(longArg);
        when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(dataPermissionsVO);
        when(dataDimensionService.getCurrentLv2ProdRndTeamList("P","dm_foc")).thenReturn(viewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        annualCommonService.handlePermissionTeamLevel(dmFocAnnualAmpVOList,commonViewVO);
        assertThatNoException();
    }

    @Test
    public void handlePermissionTeamLevel5Test() {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        DataPermissionsVO dataPermissionsVO = JSONObject.parseObject(viewInfoJson.getString("DataPermissionsVO"),DataPermissionsVO.class);
        Long longArg = viewInfoJson.getLong("Long");
        commonViewVO.setViewFlag("3");
        commonViewVO.setTeamLevel("ICT");
        commonViewVO.setCostType("M");
        // commonViewVO.setPageFlag("ANNUAL");
        commonViewVO.setGranularityType("D");
        List<ViewInfoVO> viewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<ViewInfoVO>"),ViewInfoVO.class);
        DmFocVersionInfoDTO dmFocVersionInfoDTO = JSONObject.parseObject(viewInfoJson.getString("DmFocVersionInfoDTO"),DmFocVersionInfoDTO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        List<DmFocViewInfoVO> dmFocAnnualAmpVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        when(dmFocViewInfoDao.reverseFindLv1ProdCode(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.reverseFindLv1ProdCodeMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationParent(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationByGroupLevel(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(dmFocVersionInfoDTO);
        when(commonService.getVersionId(any(String.class),any())).thenReturn(longArg);
        when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(dataPermissionsVO);
        when(dataDimensionService.getCurrentLv2ProdRndTeamList("P","dm_foc")).thenReturn(viewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        annualCommonService.handlePermissionTeamLevel(dmFocAnnualAmpVOList,commonViewVO);
        assertThatNoException();
    }

    @Test
    public void handlePermissionTeamLevel6Test() {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        DataPermissionsVO dataPermissionsVO = JSONObject.parseObject(viewInfoJson.getString("DataPermissionsVO"),DataPermissionsVO.class);
        Long longArg = viewInfoJson.getLong("Long");
        commonViewVO.setViewFlag("7");
        commonViewVO.setTeamLevel("ICT");
        commonViewVO.setCostType("M");
        commonViewVO.setTeamLevel("LV1");
        commonViewVO.setGranularityType("D");
        List<ViewInfoVO> viewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<ViewInfoVO>"),ViewInfoVO.class);
        DmFocVersionInfoDTO dmFocVersionInfoDTO = JSONObject.parseObject(viewInfoJson.getString("DmFocVersionInfoDTO"),DmFocVersionInfoDTO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        List<DmFocViewInfoVO> dmFocAnnualAmpVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        when(dmFocViewInfoDao.reverseFindLv1ProdCode(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.reverseFindLv1ProdCodeMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationParent(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationByGroupLevel(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(dmFocVersionInfoDTO);
        when(commonService.getVersionId(any(String.class),any())).thenReturn(longArg);
        when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(dataPermissionsVO);
        when(dataDimensionService.getCurrentLv2ProdRndTeamList("P","dm_foc")).thenReturn(viewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        annualCommonService.handlePermissionTeamLevel(dmFocAnnualAmpVOList,commonViewVO);
        assertThatNoException();
    }

    @Test
    public void handlePermissionTeamLevel7Test() {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        DataPermissionsVO dataPermissionsVO = JSONObject.parseObject(viewInfoJson.getString("DataPermissionsVO"),DataPermissionsVO.class);
        Long longArg = viewInfoJson.getLong("Long");
        commonViewVO.setViewFlag("7");
        commonViewVO.setTeamLevel("ICT");
        commonViewVO.setCostType("T");
        commonViewVO.setTeamLevel("LV1");
        commonViewVO.setGranularityType("D");
        List<ViewInfoVO> viewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<ViewInfoVO>"),ViewInfoVO.class);
        DmFocVersionInfoDTO dmFocVersionInfoDTO = JSONObject.parseObject(viewInfoJson.getString("DmFocVersionInfoDTO"),DmFocVersionInfoDTO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        List<DmFocViewInfoVO> dmFocAnnualAmpVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        when(dmFocViewInfoDao.reverseFindLv1ProdCode(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.reverseFindLv1ProdCodeMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationParent(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationByGroupLevel(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(dmFocVersionInfoDTO);
        when(commonService.getVersionId(any(String.class),any())).thenReturn(longArg);
        when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(dataPermissionsVO);
        when(dataDimensionService.getCurrentLv2ProdRndTeamList("P","dm_foc")).thenReturn(viewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        annualCommonService.handlePermissionTeamLevel(dmFocAnnualAmpVOList,commonViewVO);
        assertThatNoException();
    }

    @Test
    public void handlePermissionTeamLevel8Test() {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        DataPermissionsVO dataPermissionsVO = JSONObject.parseObject(viewInfoJson.getString("DataPermissionsVO"),DataPermissionsVO.class);
        Long longArg = viewInfoJson.getLong("Long");
        commonViewVO.setViewFlag("7");
        commonViewVO.setTeamLevel("ICT");
        commonViewVO.setCostType("M");
        commonViewVO.setTeamLevel("LV1");
        commonViewVO.setPageFlag("ANNUAL");
        commonViewVO.setGranularityType("D");
        List<ViewInfoVO> viewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<ViewInfoVO>"),ViewInfoVO.class);
        DmFocVersionInfoDTO dmFocVersionInfoDTO = JSONObject.parseObject(viewInfoJson.getString("DmFocVersionInfoDTO"),DmFocVersionInfoDTO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        List<DmFocViewInfoVO> dmFocAnnualAmpVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        when(dmFocViewInfoDao.reverseFindLv1ProdCode(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.reverseFindLv1ProdCodeMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationParent(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationByGroupLevel(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(dmFocVersionInfoDTO);
        when(commonService.getVersionId(any(String.class),any())).thenReturn(longArg);
        when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(dataPermissionsVO);
        when(dataDimensionService.getCurrentLv2ProdRndTeamList("P","dm_foc")).thenReturn(viewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        annualCommonService.handlePermissionTeamLevel(dmFocAnnualAmpVOList,commonViewVO);
        assertThatNoException();
    }

    @Test
    public void reverseViewPermissionTest() {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        DataPermissionsVO dataPermissionsVO = JSONObject.parseObject(viewInfoJson.getString("DataPermissionsVO"),DataPermissionsVO.class);
        Long longArg = viewInfoJson.getLong("Long");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = JSONObject.parseObject(viewInfoJson.getString("DmFocVersionInfoDTO"),DmFocVersionInfoDTO.class);
        List<ViewInfoVO> viewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<ViewInfoVO>"),ViewInfoVO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        List<DmFocViewInfoVO> dmFocAnnualAmpVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        when(dmFocViewInfoDao.reverseFindLv1ProdCode(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.reverseFindLv1ProdCodeMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationParent(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.getCombinationByGroupLevel(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(dmFocVersionInfoDTO);
        when(commonService.getVersionId(any(String.class),any())).thenReturn(longArg);
        when(commonService.getCurrentRoleDataPermission("ICT")).thenReturn(dataPermissionsVO);
        when(dataDimensionService.getCurrentLv2ProdRndTeamList("P","dm_foc")).thenReturn(viewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocPftViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        annualCommonService.reverseViewPermission(dmFocAnnualAmpVOList,commonViewVO,viewInfoVOList);
        assertThatNoException();
        commonViewVO.getLv0DimensionSet().clear();
        commonViewVO.setTeamLevel("LV1");
        annualCommonService.reverseViewPermission(dmFocAnnualAmpVOList,commonViewVO,viewInfoVOList);
        assertThatNoException();
        commonViewVO.getLv1DimensionSet().clear();
        commonViewVO.setViewFlag("6");
        annualCommonService.reverseViewPermission(dmFocAnnualAmpVOList,commonViewVO,viewInfoVOList);
        assertThatNoException();
        commonViewVO.getLv2DimensionSet().clear();
        commonViewVO.setViewFlag("6");
        commonViewVO.setTeamLevel("CATEGORY");
        annualCommonService.reverseViewPermission(dmFocAnnualAmpVOList,commonViewVO,viewInfoVOList);
        assertThatNoException();
        commonViewVO.setViewFlag("5");
        annualCommonService.reverseViewPermission(dmFocAnnualAmpVOList,commonViewVO,viewInfoVOList);
        assertThatNoException();
        commonViewVO.setViewFlag("5");
        commonViewVO.setTeamLevel("MODL");
        annualCommonService.reverseViewPermission(dmFocAnnualAmpVOList,commonViewVO,viewInfoVOList);
        assertThatNoException();
        commonViewVO.setViewFlag("4");
        annualCommonService.reverseViewPermission(dmFocAnnualAmpVOList,commonViewVO,viewInfoVOList);
        assertThatNoException();
        commonViewVO.setViewFlag("4");
        commonViewVO.setTeamLevel("CEG");
        annualCommonService.reverseViewPermission(dmFocAnnualAmpVOList,commonViewVO,viewInfoVOList);
        assertThatNoException();
        annualCommonService.reverseViewPermission(dmFocAnnualAmpVOList,commonViewVO,viewInfoVOList);
        assertThatNoException();
    }

    @Test
    public void hasPermissionTeamLevelPfiTest() {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        commonViewVO.getLv0DimensionSet().clear();
        commonViewVO.getLv1DimensionSet().clear();
        commonViewVO.getLv2DimensionSet().clear();
        annualCommonService.hasPermissionTeamLevelPfi(dmFocViewInfoVOList,commonViewVO);
        verify(dataDimensionService, Mockito.never()).getCurrentLv2ProdRndTeamList("P","dm_foc");
    }

    @Test
    public void sortLv1ByCodeTest() {
        List<DmFocViewInfoVO> dmFocAnnualAmpVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        List<DmFocViewInfoVO> result = annualCommonService.sortLv1ByCode(dmFocAnnualAmpVOList);
        Assertions.assertNotNull(result);
    }

    @Test
    public void viewFlagInfoListTest() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        List<ViewInfoVO> viewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<ViewInfoVO>"),ViewInfoVO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        when(dataDimensionService.getCurrentLv2ProdRndTeamList("P","dm_foc")).thenReturn(viewInfoVOList);
        when(dmFocPftViewInfoDao.viewFlagInfoProfitsList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocDmsViewInfoDao.viewFlagInfoDmsList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        commonViewVO.getLv0DimensionSet().clear();
        ResultDataVO result = annualCommonService.viewFlagInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        result = annualCommonService.viewFlagInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        commonViewVO.getLv1DimensionSet().clear();
        result = annualCommonService.viewFlagInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        result = annualCommonService.viewFlagInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        commonViewVO.getLv2DimensionSet().clear();
        result = annualCommonService.viewFlagInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        result = annualCommonService.viewFlagInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        result = annualCommonService.viewFlagInfoList(commonViewVO);
        Assertions.assertNotNull(result);

        result = annualCommonService.viewFlagInfoList(commonViewVO);
        Assertions.assertNotNull(result);
    }

    @Test
    public void viewFlagInfoListTest1T() throws ApplicationException {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        List<ViewInfoVO> viewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<ViewInfoVO>"),ViewInfoVO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        when(dataDimensionService.getCurrentLv2ProdRndTeamList("P","dm_foc")).thenReturn(viewInfoVOList);
        when(dmFocPftViewInfoDao.viewFlagInfoProfitsList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocDmsViewInfoDao.viewFlagInfoDmsList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        commonViewVO.getLv0DimensionSet().clear();
        ResultDataVO result = annualCommonService.viewFlagInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        result = annualCommonService.viewFlagInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        commonViewVO.getLv1DimensionSet().clear();
        result = annualCommonService.viewFlagInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        result = annualCommonService.viewFlagInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        commonViewVO.getLv2DimensionSet().clear();
        result = annualCommonService.viewFlagInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        result = annualCommonService.viewFlagInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        result = annualCommonService.viewFlagInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        result = annualCommonService.viewFlagInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        result = annualCommonService.viewFlagInfoList(commonViewVO);
        Assertions.assertNotNull(result);

        result = annualCommonService.viewFlagInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        result = annualCommonService.viewFlagInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        result = annualCommonService.viewFlagInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        result = annualCommonService.viewFlagInfoList(commonViewVO);
        Assertions.assertNotNull(result);
        result = annualCommonService.viewFlagInfoList(commonViewVO);
        Assertions.assertNotNull(result);
    }

    @Test
    public void setViewFlagListWithOneTest() throws Exception {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        List<ViewInfoVO> viewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<ViewInfoVO>"),ViewInfoVO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);

        Whitebox.invokeMethod(annualCommonService, "setViewFlagListWithOne", commonViewVO, viewInfoVOList,
                dmFocViewInfoVOList, "U");
        assertThatNoException();
        Whitebox.invokeMethod(annualCommonService, "setViewFlagListWithOne", commonViewVO, viewInfoVOList,
                dmFocViewInfoVOList, "P");
        assertThatNoException();
        Whitebox.invokeMethod(annualCommonService, "setViewFlagListWithOne", commonViewVO, viewInfoVOList,
                dmFocViewInfoVOList, "D");
        assertThatNoException();
        viewInfoVOList.forEach(obj -> obj.setLv2ProdRndTeamCode("test"));
        Whitebox.invokeMethod(annualCommonService, "setViewFlagListWithOne", commonViewVO, viewInfoVOList,
                dmFocViewInfoVOList, "D");
        assertThatNoException();
        Whitebox.invokeMethod(annualCommonService, "setViewFlagListWithOne", commonViewVO, viewInfoVOList,
                dmFocViewInfoVOList, "D");
        assertThatNoException();
        commonViewVO.getLv2DimensionSet().clear();
        Whitebox.invokeMethod(annualCommonService, "setViewFlagListWithOne", commonViewVO, viewInfoVOList,
                dmFocViewInfoVOList, "D");
        assertThatNoException();

        commonViewVO.getLv1DimensionSet().add("NO_PERMISSION");
        Whitebox.invokeMethod(annualCommonService, "setViewFlagListWithOne", commonViewVO, viewInfoVOList,
                dmFocViewInfoVOList, "D");
        assertThatNoException();
    }

    @Test
    public void setUniversalLookUpTest() throws Exception {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        List<DmFocViewInfoVO> viewFlagList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"), DmFocViewInfoVO.class);
        List<LookupItemVO> lookups = JSONObject.parseArray(viewInfoJson.getString("List<LookupItemVO>"),
                LookupItemVO.class);
        when(lookupItemQueryService.findItemListByClassify(any(String.class))).thenReturn(lookups);
        viewFlagList.forEach(obj -> {
            obj.setViewFlag("1");
            obj.setGranularityType("U");
        });
        mockStatic(IndustryGlobalParameterUtil.class);
        when(IndustryGlobalParameterUtil.findViewListByItemCode(Constant.StrEnum.PURCHASE_LOOKUP_UNIVERSAL_VIEW.getValue())).thenReturn(lookups);
        Whitebox.invokeMethod(annualCommonService, "setUniversalLookUp",viewFlagList,commonViewVO.getIndustryOrg(), IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL, Constant.StrEnum.PURCHASE_LOOKUP_UNIVERSAL_VIEW);
        assertThatNoException();
        viewFlagList.forEach(obj -> {
            obj.setViewFlag("1");
            obj.setGranularityType("D");
        });
        mockStatic(IndustryGlobalParameterUtil.class);
        when(IndustryGlobalParameterUtil.findViewListByItemCode(Constant.StrEnum.PURCHASE_LOOKUP_DIMENSION_VIEW.getValue())).thenReturn(lookups);
        Whitebox.invokeMethod(annualCommonService, "setUniversalLookUp", viewFlagList,"IAS", IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION, Constant.StrEnum.PURCHASE_LOOKUP_DIMENSION_VIEW);
        assertThatNoException();

        viewFlagList.forEach(obj -> {
            obj.setViewFlag("1");
            obj.setGranularityType("P");
        });
        mockStatic(IndustryGlobalParameterUtil.class);
        when(IndustryGlobalParameterUtil.findViewListByItemCode(Constant.StrEnum.PURCHASE_LOOKUP_PROFITS_VIEW.getValue())).thenReturn(lookups);
        Whitebox.invokeMethod(annualCommonService, "setUniversalLookUp", viewFlagList,"ENERGY", IndustryIndexEnum.GRANULARITY_TYPE.PROFITS, Constant.StrEnum.PURCHASE_LOOKUP_PROFITS_VIEW);
        assertThatNoException();
    }

    @Test
    public void getMonthPageTest() throws Exception {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        when(dmFocViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        commonViewVO.setPageFlag("CONFIG");
        List<DmFocViewInfoVO> result = Whitebox.invokeMethod(annualCommonService, "getMonthPage", commonViewVO);
        Assertions.assertNotNull(result);
        commonViewVO.setKeyWord("test");
        result = Whitebox.invokeMethod(annualCommonService, "getMonthPage", commonViewVO);
        Assertions.assertNotNull(result);
    }

    @Test
    public void getProfitMonthPageTest() throws Exception {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        when(dmFocPftViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        commonViewVO.setPageFlag("CONFIG");
        List<DmFocViewInfoVO> result = Whitebox.invokeMethod(annualCommonService, "getProfitMonthPage", commonViewVO);
        Assertions.assertNotNull(result);
        commonViewVO.setKeyWord("test");
        result = Whitebox.invokeMethod(annualCommonService, "getProfitMonthPage", commonViewVO);
        Assertions.assertNotNull(result);
    }

    @Test
    public void getDmsMonthPageTest() throws Exception {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        when(dmFocDmsViewInfoDao.viewInfoKeyWordForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        commonViewVO.setPageFlag("CONFIG");
        List<DmFocViewInfoVO> result = Whitebox.invokeMethod(annualCommonService, "getDmsMonthPage", commonViewVO);
        Assertions.assertNotNull(result);
        commonViewVO.setKeyWord("test");
        result = Whitebox.invokeMethod(annualCommonService, "getDmsMonthPage", commonViewVO);
        Assertions.assertNotNull(result);
    }

    @Test
    public void getAnnualAndRelationPageTest() throws Exception {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setPageFlag("CONFIG");
        List<DmFocViewInfoVO> result = Whitebox.invokeMethod(annualCommonService, "getAnnualAndRelationPage", commonViewVO);
        Assertions.assertNotNull(result);
    }

    @Test
    public void getReverseAnnualAndRelationPageTest() throws Exception {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        List<ViewInfoVO> viewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<ViewInfoVO>"),ViewInfoVO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        when(dmFocViewInfoDao.revViewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        List<DmFocViewInfoVO> result = Whitebox.invokeMethod(annualCommonService, "getReverseAnnualAndRelationPage",
                commonViewVO, viewInfoVOList);
        Assertions.assertNotNull(result);

        dmFocViewInfoVOList.clear();
        result = Whitebox.invokeMethod(annualCommonService, "getReverseAnnualAndRelationPage",
                commonViewVO, viewInfoVOList);
        Assertions.assertNotNull(result);

        commonViewVO.setPageFlag("CONFIG");
        result = Whitebox.invokeMethod(annualCommonService, "getReverseAnnualAndRelationPage",
                commonViewVO, viewInfoVOList);
        Assertions.assertNotNull(result);
    }

    @Test
    public void getReverseUniversalMonthPageTest() throws Exception {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        List<ViewInfoVO> viewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<ViewInfoVO>"),ViewInfoVO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        when(dmFocViewInfoDao.revViewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        List<DmFocViewInfoVO> result = Whitebox.invokeMethod(annualCommonService, "getReverseUniversalMonthPage",
                commonViewVO, viewInfoVOList);
        Assertions.assertNotNull(result);

        dmFocViewInfoVOList.clear();
        result = Whitebox.invokeMethod(annualCommonService, "getReverseUniversalMonthPage",
                commonViewVO, viewInfoVOList);
        Assertions.assertNotNull(result);

        commonViewVO.setPageFlag("CONFIG");
        result = Whitebox.invokeMethod(annualCommonService, "getReverseUniversalMonthPage",
                commonViewVO, viewInfoVOList);
        Assertions.assertNotNull(result);
    }

    @Test
    public void getLv1AndLv2ProdVOListTest() throws Exception {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setMaxViewFlag("3");
        commonViewVO.getLv1DimensionSet().add("NO_PERMISSION");
        commonViewVO.getLv2DimensionSet().add("NO_PERMISSION");
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        when(dmFocViewInfoDao.viewInfoList(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        when(dmFocViewInfoDao.viewInfoListForMonth(any(CommonViewVO.class))).thenReturn(dmFocViewInfoVOList);
        commonViewVO.setPageFlag("MONTH");
        dmFocViewInfoVOList.clear();
        commonViewVO.getLv1DimensionSet().add("NO_PERMISSION");
        commonViewVO.getLv2DimensionSet().add("NO_PERMISSION");
        Whitebox.invokeMethod(annualCommonService, "getLv1AndLv2ProdVOList", commonViewVO);
        verify(dmFocViewInfoDao, atLeastOnce()).viewInfoList(any(CommonViewVO.class));

        commonViewVO.getLv2DimensionSet().clear();
        Whitebox.invokeMethod(annualCommonService, "getLv1AndLv2ProdVOList", commonViewVO);
        verify(dmFocViewInfoDao, atLeastOnce()).viewInfoList(any(CommonViewVO.class));

        commonViewVO.getLv1DimensionSet().clear();
        Whitebox.invokeMethod(annualCommonService, "getLv1AndLv2ProdVOList", commonViewVO);
        verify(dmFocViewInfoDao, atLeastOnce()).viewInfoList(any(CommonViewVO.class));

        commonViewVO.getLv2DimensionSet().add("NO_PERMISSION");
        Whitebox.invokeMethod(annualCommonService, "getLv1AndLv2ProdVOList", commonViewVO);
        verify(dmFocViewInfoDao, atLeastOnce()).viewInfoList(any(CommonViewVO.class));
    }

    @Test
    public void getProfitAnnualAndRelationPageTest() throws Exception {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"),CommonViewVO.class);
        commonViewVO.setPageFlag("CONFIG");
        List<DmFocViewInfoVO> result = Whitebox.invokeMethod(annualCommonService, "getProfitAnnualAndRelationPage",
                commonViewVO);
        Assertions.assertNotNull(result);
    }

    @Test
    public void getDmsAnnualAndRelationPageTest() throws Exception {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"), CommonViewVO.class);
        commonViewVO.setPageFlag("CONFIG");
        List<DmFocViewInfoVO> result = Whitebox.invokeMethod(annualCommonService, "getDmsAnnualAndRelationPage",
                commonViewVO);
        Assertions.assertNotNull(result);
        commonViewVO.setKeyWord("tdd");
        result = Whitebox.invokeMethod(annualCommonService, "getDmsAnnualAndRelationPage", commonViewVO);
        Assertions.assertNotNull(result);
    }

    @Test
    public void handleViewFlagThreeTest() throws Exception {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"), CommonViewVO.class);
        List<ViewInfoVO> viewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<ViewInfoVO>"),ViewInfoVO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        commonViewVO.setGranularityType("D");
        Whitebox.invokeMethod(annualCommonService, "handleViewFlagThree",dmFocViewInfoVOList,
                commonViewVO, viewInfoVOList);
        assertThatNoException();
        commonViewVO.setViewFlag("8");
        Whitebox.invokeMethod(annualCommonService, "handleViewFlagThree",dmFocViewInfoVOList,
                commonViewVO, viewInfoVOList);
        assertThatNoException();
        commonViewVO.setViewFlag("7");
        Whitebox.invokeMethod(annualCommonService, "handleViewFlagThree",dmFocViewInfoVOList,
                commonViewVO, viewInfoVOList);
        assertThatNoException();
        commonViewVO.setViewFlag("6");
        Whitebox.invokeMethod(annualCommonService, "handleViewFlagThree",dmFocViewInfoVOList,
                commonViewVO, viewInfoVOList);
        assertThatNoException();
    }

    @Test
    public void handleLv1ProdTeamCodeWithThreeTest() throws Exception {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"), CommonViewVO.class);
        List<ViewInfoVO> viewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<ViewInfoVO>"),ViewInfoVO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        DmFocViewInfoVO foc = dmFocViewInfoVOList.get(0);
        foc.setGroupLevel("LV1");
        boolean result = Whitebox.invokeMethod(annualCommonService, "handleLv1ProdTeamCodeWithThree", commonViewVO,
                viewInfoVOList, foc);
        Assertions.assertTrue(result);

        viewInfoVOList.forEach(obj -> obj.setLv1ProdRndTeamCode("test1"));
        result = Whitebox.invokeMethod(annualCommonService, "handleLv1ProdTeamCodeWithThree", commonViewVO,
                viewInfoVOList, foc);
        Assertions.assertTrue(result);

        viewInfoVOList.forEach(obj -> obj.setLv1ProdRndTeamCode("test"));
        viewInfoVOList.forEach(obj -> obj.setLv2ProdRndTeamCode("test"));
        result = Whitebox.invokeMethod(annualCommonService, "handleLv1ProdTeamCodeWithThree", commonViewVO,
                viewInfoVOList, foc);
        Assertions.assertTrue(result);
        commonViewVO.getLv2DimensionSet().clear();
        result = Whitebox.invokeMethod(annualCommonService, "handleLv1ProdTeamCodeWithThree", commonViewVO,
                viewInfoVOList, foc);
        Assertions.assertTrue(result);
    }

    @Test
    public void handleViewFlagTwoTest() throws Exception {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"), CommonViewVO.class);
        List<ViewInfoVO> viewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<ViewInfoVO>"),ViewInfoVO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        commonViewVO.setGranularityType("D");
        Whitebox.invokeMethod(annualCommonService, "handleViewFlagTwo",dmFocViewInfoVOList,
                commonViewVO, viewInfoVOList);
        assertThatNoException();
        commonViewVO.setViewFlag("5");
        Whitebox.invokeMethod(annualCommonService, "handleViewFlagTwo",dmFocViewInfoVOList,
                commonViewVO, viewInfoVOList);
        assertThatNoException();
        commonViewVO.setViewFlag("4");
        Whitebox.invokeMethod(annualCommonService, "handleViewFlagTwo",dmFocViewInfoVOList,
                commonViewVO, viewInfoVOList);
        assertThatNoException();
        commonViewVO.setViewFlag("3");
        Whitebox.invokeMethod(annualCommonService, "handleViewFlagTwo",dmFocViewInfoVOList,
                commonViewVO, viewInfoVOList);
        assertThatNoException();
    }

    @Test
    public void handleViewFlagOneTest() throws Exception {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"), CommonViewVO.class);
        List<ViewInfoVO> viewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<ViewInfoVO>"),ViewInfoVO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),DmFocViewInfoVO.class);
        commonViewVO.setGranularityType("D");
        Whitebox.invokeMethod(annualCommonService, "handleViewFlagOne",dmFocViewInfoVOList,
                commonViewVO, viewInfoVOList);
        assertThatNoException();
        commonViewVO.setViewFlag("2");
        Whitebox.invokeMethod(annualCommonService, "handleViewFlagOne",dmFocViewInfoVOList,
                commonViewVO, viewInfoVOList);
        assertThatNoException();
        commonViewVO.setViewFlag("1");
        Whitebox.invokeMethod(annualCommonService, "handleViewFlagOne",dmFocViewInfoVOList,
                commonViewVO, viewInfoVOList);
        assertThatNoException();
        commonViewVO.setViewFlag("0");
        Whitebox.invokeMethod(annualCommonService, "handleViewFlagOne",dmFocViewInfoVOList,
                commonViewVO, viewInfoVOList);
        assertThatNoException();
    }

    @Test
    public void lv1ProdTeamCodePermissionTest() throws Exception {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"), CommonViewVO.class);
        List<ViewInfoVO> viewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<ViewInfoVO>"), ViewInfoVO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),
                DmFocViewInfoVO.class);
        dmFocViewInfoVOList.forEach(obj -> obj.setGroupLevel("LV1"));

        List<ViewInfoVO> lv3ProdDimensionList = new ArrayList<>();
        ViewInfoVO viewInfoVO = new ViewInfoVO();
        viewInfoVO.setViewFlag("1");
        viewInfoVO.setLv1ProdRndTeamCode("100001");
        viewInfoVO.setLv1ProdRnTeamName("无线");
        viewInfoVO.setLv2ProdRndTeamCode("153324");
        viewInfoVO.setLv2ProdRnTeamName("5G&LTE FDD");
        viewInfoVO.setLv3ProdRndTeamCode("140701");
        viewInfoVO.setLv3ProdRnTeamName("FDD NR");
        lv3ProdDimensionList.add(viewInfoVO);
        PowerMockito.doReturn(lv3ProdDimensionList).when(dataDimensionService).getCurrentLv2ProdRndTeamList(commonViewVO.getCostType(),commonViewVO.getTablePreFix());
        Whitebox.invokeMethod(annualCommonService, "lv1ProdTeamCodePermission", dmFocViewInfoVOList, commonViewVO,
                lv3ProdDimensionList);
        assertThatNoException();
    }

    @Test
    public void lv1PermissionTest() throws Exception {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"), CommonViewVO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),
                DmFocViewInfoVO.class);
        Whitebox.invokeMethod(annualCommonService, "lv1Permission", commonViewVO, dmFocViewInfoVOList);
        assertThatNoException();
        commonViewVO.setViewFlag("3");
        Whitebox.invokeMethod(annualCommonService, "lv1Permission", commonViewVO, dmFocViewInfoVOList);
        assertThatNoException();
        commonViewVO.setTeamLevel("ICT");
        Whitebox.invokeMethod(annualCommonService, "lv1Permission", commonViewVO, dmFocViewInfoVOList);
        assertThatNoException();
        commonViewVO.setGranularityType("D");
        commonViewVO.setViewFlag("7");
        Whitebox.invokeMethod(annualCommonService, "lv1Permission", commonViewVO, dmFocViewInfoVOList);
        assertThatNoException();
        commonViewVO.setViewFlag("6");
        Whitebox.invokeMethod(annualCommonService, "lv1Permission", commonViewVO, dmFocViewInfoVOList);
        assertThatNoException();
        commonViewVO.setViewFlag("5");
        Whitebox.invokeMethod(annualCommonService, "lv1Permission", commonViewVO, dmFocViewInfoVOList);
        assertThatNoException();
        commonViewVO.setTeamLevel("LV2");
        Whitebox.invokeMethod(annualCommonService, "lv1Permission", commonViewVO, dmFocViewInfoVOList);
        assertThatNoException();
        commonViewVO.setViewFlag("4");
        commonViewVO.setTeamLevel("ICT");
        Whitebox.invokeMethod(annualCommonService, "lv1Permission", commonViewVO, dmFocViewInfoVOList);
        assertThatNoException();
    }

    @Test
    public void setLv1ProCodeListForView3Test() throws Exception {
        CommonViewVO commonViewVO = JSONObject.parseObject(viewInfoJson.getString("CommonViewVO"), CommonViewVO.class);
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),
                DmFocViewInfoVO.class);
        commonViewVO.setReverseFlag(false);
        commonViewVO.setPageFlag("ANNUAL");
        Whitebox.invokeMethod(annualCommonService, "setLv1ProCodeListForView3", commonViewVO, dmFocViewInfoVOList);
        assertThatNoException();
        commonViewVO.setReverseFlag(true);
        Whitebox.invokeMethod(annualCommonService, "setLv1ProCodeListForView3", commonViewVO, dmFocViewInfoVOList);
        assertThatNoException();
        commonViewVO.setPageFlag("MONTH");
        Whitebox.invokeMethod(annualCommonService, "setLv1ProCodeListForView3", commonViewVO, dmFocViewInfoVOList);
        assertThatNoException();
    }


    @Test
    public void distinctAnnualAmpListTest() throws Exception {
        List<DmFocViewInfoVO> dmFocViewInfoVOList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),
                DmFocViewInfoVO.class);
        List<DmFocViewInfoVO> focList = JSONObject.parseArray(viewInfoJson.getString("List<DmFocViewInfoVO>"),
                DmFocViewInfoVO.class);
        dmFocViewInfoVOList.forEach(obj -> obj.setGroupLevel("L2"));
        Whitebox.invokeMethod(annualCommonService, "distinctAnnualAmpList", dmFocViewInfoVOList, focList);
        assertThatNoException();
        dmFocViewInfoVOList.forEach(obj -> obj.setGroupLevel("L1"));
        Whitebox.invokeMethod(annualCommonService, "distinctAnnualAmpList", dmFocViewInfoVOList, focList);
        assertThatNoException();
        dmFocViewInfoVOList.forEach(obj -> obj.setGroupLevel("SUB_DETAIL"));
        Whitebox.invokeMethod(annualCommonService, "distinctAnnualAmpList", dmFocViewInfoVOList, focList);
        assertThatNoException();
        dmFocViewInfoVOList.forEach(obj -> obj.setGroupLevel("SUBCATEGORY"));
        Whitebox.invokeMethod(annualCommonService, "distinctAnnualAmpList", dmFocViewInfoVOList, focList);
        assertThatNoException();
        dmFocViewInfoVOList.forEach(obj -> obj.setGroupLevel("DIMENSION"));
        Whitebox.invokeMethod(annualCommonService, "distinctAnnualAmpList", dmFocViewInfoVOList, focList);
        assertThatNoException();
        dmFocViewInfoVOList.forEach(obj -> obj.setGroupLevel("CATEGORY"));
        Whitebox.invokeMethod(annualCommonService, "distinctAnnualAmpList", dmFocViewInfoVOList, focList);
        assertThatNoException();
        dmFocViewInfoVOList.forEach(obj -> obj.setGroupLevel("MODL"));
        Whitebox.invokeMethod(annualCommonService, "distinctAnnualAmpList", dmFocViewInfoVOList, focList);
        assertThatNoException();
        dmFocViewInfoVOList.forEach(obj -> obj.setGroupLevel("CEG"));
        Whitebox.invokeMethod(annualCommonService, "distinctAnnualAmpList", dmFocViewInfoVOList, focList);
        assertThatNoException();
    }

    @Test
    public void currentDataRefreshStatus() throws Exception {
        DmFocDataRefreshStatus dmFocDataRefreshStatus=new DmFocDataRefreshStatus();
        dmFocDataRefreshStatus.setStatus("1");
        PowerMockito.doReturn(dmFocDataRefreshStatus).when(dataRefreshStatusDao).findDmFocDataRefreshStatus(any());
        ResultDataVO resultDataVO = annualCommonService.currentDataRefreshStatus(dmFocDataRefreshStatus);
        Assert.assertNotNull(resultDataVO);
    }
}