/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.config;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

/**
 * DmFocVersionInfoVOTest Class
 *
 * <AUTHOR>
 * @since 2023/4/23
 */
public class DmFocVersionInfoVOTest extends BaseVOCoverUtilsTest<DmFocVersionInfoVO> {

    @Override
    protected Class<DmFocVersionInfoVO> getTClass() {
        return DmFocVersionInfoVO.class;
    }

    @Test
    public void testMethod() {
        DmFocVersionInfoVO dimensionParamVO = new DmFocVersionInfoVO();
        dimensionParamVO.setLastUpdateStr("str");
        dimensionParamVO.getLastUpdateStr();
        dimensionParamVO.setStatus(10L);
        dimensionParamVO.getStatus();
        dimensionParamVO.builder().dataType("adjust").build();
        Assert.assertNotNull(dimensionParamVO);
    }
}