/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.common;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024/6/26
 */
// 不能修改成getter和setter!!
@Data
@NoArgsConstructor
public class CommonBaseVO {

    /**
     * 成本类型
     */
    private String costType;

    /**
     * psp成本软硬件标识
     **/
    private String softwareMark;

    private String softwareMarkCnName;

    /**
     * PBI目录树
     */
    private String granularityType;

    private String tablePreFix;

    /**
     * 国内/海外
     */
    private String overseaFlag;


    private String viewFlag;

    /**
     * 会计月开始时间
     */
    private Integer periodStartTime;

    /**
     * 会计月结束时间
     */
    private Integer periodEndTime;

    private String lv0CnName;


    /**
     * BG编码
     */
    private String bgCode;

    private String bgCnName;

    /**
     * 地区部
     */
    private String regionCode;

    private String regionCnName;

    /**
     * 代表处
     */
    private String repofficeCode;

    private String repofficeCnName;

    /**
     * 主力编码
     */
    private String mainFlag;

    private String mainFlagCnName;

    /**
     * 编码属性
     */
    private String codeAttributes;

    private String codeAttributesCnName;

    /**
     * 重量级团队list
     */
    private List<String> prodRndTeamCodeList = new ArrayList<>();

    /**
     * 量纲
     */
    private String dimensionCode;

    private String parentLevel;

    /**
     * 量纲子类
     */
    private String dimensionSubCategoryCode;

    /**
     * 量纲子类明细
     */
    private String dimensionSubDetailCode;

    /**
     * spart
     */
    private String spartCode;

    /**
     * spartCodeList 多选使用
     */
    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<String> spartCodeList;

    /**
     * 当前层级
     */
    private String groupLevel;

    private String parentCode;

    private String groupCode;

    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<String> groupCodeList = new ArrayList<>();

    /**
     * 下个层级
     */
    private String nextGroupLevel;

    // 是否行销分析师
    private Boolean marketingAnalyst;

    /**
     * L0多选
     */
    private List<String> lv0ProdRndTeamCodeList;

    /**
     * L1多选
     */
    private List<String> lv1ProdRndTeamCodeList;

    /**
     * L2多选
     */
    private List<String> lv2ProdRndTeamCodeList;

    /**
     * L3多选
     */
    private List<String> lv3ProdRndTeamCodeList;

    /**
     * L3.5多选
     */
    private List<String> lv4ProdRndTeamCodeList;


    private List<String> lv1ProdRdTeamCnNameList;

    private List<String> lv2ProdRdTeamCnNameList;

    private List<String> lv3ProdRdTeamCnNameList;

    private List<String> lv4ProdRdTeamCnNameList;

    /**
     * 阈值
     */
    private Double ratioPspStd;

    /**
     * 重量级团队
     */
    private String prodRndTeamCode;

    /**
     * L0
     */
    private String lv0ProdRndTeamCode;

    /**
     * L1
     */
    private String lv1ProdRndTeamCode;

    /**
     * L2
     */
    private String lv2ProdRndTeamCode;

    /**
     * L3
     */
    private String lv3ProdRndTeamCode;

    /**
     * L3.5
     */
    private String lv4ProdRndTeamCode;


    private String displayName;

    private String displayNameIndex;

    private String spartCnName;

    /**
     * 量纲
     */
    private String dimensionCnName;

    /**
     * 量纲子类
     */
    private String dimensionSubCategoryCnName;

    /**
     * 量纲子类明细
     */
    private String dimensionSubDetailCnName;

    /**
     * 页面标识
     */
    private String pageType;

    /**
     * 另存页标识
     */
    private Boolean saveFlag;

    /**
     * lv0-lv4
     */
    private String lvCode;

    private String pageFlag;

    /**
     * ICT产业集合
     */
    private Set<String> lv0DimensionSet = new HashSet<>();

    /**
     * LV1集合
     */
    private Set<String> lv1DimensionSet  = new HashSet<>();

    /**
     * LV2集合
     */
    private Set<String> lv2DimensionSet  = new HashSet<>();

    /**
     * ICT项目需要用到LV3层级
     */
    private Set<String> lv3DimensionSet  = new HashSet<>();

    private Set<String> overseaFlagDimensionSet  = new HashSet<>();

    private Set<String> regionCodeDimensionSet = new HashSet<>();

    private Set<String> repofficeCodeDimensionSet = new HashSet<>();

    /**
     * 版本
     */
    private Long versionId;

    private Long annualVersionId;

    /**
     * 月度版本ID
     */
    private Long monVersionId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户工号
     */
    private String userAccount;

    /**
     *
     * 角色id
     */
    private String roleId;

    /**
     * 组合id
     */
    private List<Long> combIdList;

    /**
     * 组合id
     */
    private List<Long> parentCombIdList;


    private String filterGroupLevel;


    private String keyword;

    private String isSavePage;

    // 组合整体失效
    private String enableFlag;

    /**
     *
     *  页码
     */
    private int pageIndex;

    /**
     *
     *  一页数量
     */
    private int pageSize;

    /**
     *
     *  总条数
     */
    private int totalSize;

    private String ytdFlag;

    // 是否是历史版本（true是历史版本，false是最新版本）
    private Boolean isHistoryVersion;

    // 是否包含软硬件标识（true：包含软硬件标识，false：不包含软硬件标识）
    private Boolean isHasSoftWare;

    // 是否区域分析师
    private Boolean isRegionAnalyst = false;



}
