/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.combination;

import com.huawei.it.fcst.industry.index.dao.IDmFocDataRefreshStatusDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocProcurementReviewDao;
import com.huawei.it.fcst.industry.index.impl.config.ConfigExportService;
import com.huawei.it.fcst.industry.index.vo.combination.CombTransformVO;
import com.huawei.it.fcst.industry.index.vo.combination.CombinationVO;
import com.huawei.it.fcst.industry.index.vo.combination.DmCustomCombVO;
import com.huawei.it.fcst.industry.index.vo.combination.DmFocDataRefreshStatus;
import com.huawei.it.fcst.industry.index.vo.config.HistoryInputVO;
import com.huawei.it.fcst.industry.index.vo.config.ProcurementBottomVO;
import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;

import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.security.UserVO;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

/**
 * AsyncServiceTest Class
 *
 * <AUTHOR>
 * @since 2023/9/14
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class AsyncServiceTest {

    @InjectMocks
    private AsyncService asyncService;

    @Mock
    private IDmFocDataRefreshStatusDao dataRefreshStatusDao;

    @Mock
    private IDmFocProcurementReviewDao procurementReviewDao;

    @Mock
    private CustomService customService;

    @Mock
    private XSSFWorkbook workbook;

    @Mock
    private ConfigExportService configExportService;

    @Before
    public void before() throws Exception {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);
    }

    @Test
    public void asyncCreateCustom() throws Exception {

        List<DmCustomCombVO> customVOList = new ArrayList<>();
        CombTransformVO combTransformVO = new CombTransformVO();
        List<DmCustomCombVO> otherCustomVOList = new ArrayList<>();
        combTransformVO.setUserId(1111L);
        combTransformVO.setTaskId(22L);
        asyncService.asyncCreateCustom(customVOList,combTransformVO,otherCustomVOList);
        Assert.assertNull(null);
    }

    @Test
    public void asyncCombRename() throws Exception {

        CombTransformVO combTransformVO = new CombTransformVO();
        combTransformVO.setUserId(1111L);
        combTransformVO.setTaskId(22L);
        asyncService.asyncCombRename(combTransformVO);
        Assert.assertNull(null);
    }

    @Test
    public void asyncCombUpdate() throws Exception {
        CombTransformVO combTransformVO = new CombTransformVO();
        combTransformVO.setUserId(1111L);
        combTransformVO.setTaskId(22L);
        CombinationVO combinationVO = new CombinationVO();
        asyncService.asyncCombUpdate(combinationVO, combTransformVO);
        Assert.assertNull(null);
    }

    @Test
    public void exportMonthData() throws Exception {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setVersionId(15L);
        CombTransformVO combTransformVO = new CombTransformVO();
        String groupCnName="元器件";
        combTransformVO.setUserId(1111L);
        combTransformVO.setTaskId(22L);
        asyncService.exportMonthData(monthAnalysisVO,workbook,groupCnName,combTransformVO);
        Assert.assertNull(null);
    }

    @Test
    public void asyncInitData() throws Exception {
        List<DmCustomCombVO> customCombList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setCustomId(15L);
        dmCustomCombVO.setGroupCode("2101D");
        customCombList.add(dmCustomCombVO);
        CombinationVO combinationVO = new CombinationVO();
        combinationVO.setViewFlag("2");
        CombTransformVO combTransformVO = new CombTransformVO();
        combTransformVO.setUserId(1111L);
        combTransformVO.setTaskId(22L);
        List<DmCustomCombVO> customCombEnergyList = new ArrayList<>();
        List<DmCustomCombVO> manufactureCustomCombEnergyList= new ArrayList<>();
        asyncService.asyncInitData(customCombList,customCombList,customCombEnergyList,manufactureCustomCombEnergyList,combinationVO,combTransformVO);
        Assert.assertNull(null);
    }

    @Test
    public void asyncImpactQtyTest() throws Exception {

        ProcurementBottomVO procurementBottomVO = new ProcurementBottomVO();
        DmFocDataRefreshStatus dataRefreshStatus = new DmFocDataRefreshStatus();
        dataRefreshStatus.setTaskId(11L);
        dataRefreshStatus.setUserId(2222L);
        ProcurementBottomVO newImpactQty = new ProcurementBottomVO();
        PowerMockito.doReturn(newImpactQty).when(procurementReviewDao).getNewImpactQty(any());
        asyncService.asyncImpactQty(procurementBottomVO,dataRefreshStatus);

        ProcurementBottomVO newImpactQty2 = null;
        PowerMockito.doReturn(newImpactQty2).when(procurementReviewDao).getNewImpactQty(any());
        asyncService.asyncImpactQty(procurementBottomVO,dataRefreshStatus);
        Assert.assertNull(null);
    }

    @Test
    public void getFoldGroupCodeListTest() throws Exception {
        CombinationVO combinationVO = new CombinationVO();
        combinationVO.setGranularityType("U");
        combinationVO.setPageSymbol("MONTH");
        List<DmCustomCombVO> customVOList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setLv0ProdRndTeamCode("111");
        dmCustomCombVO.setLv1ProdRndTeamCode("222");
        dmCustomCombVO.setLv2ProdRndTeamCode("333");
        dmCustomCombVO.setLv3ProdRndTeamCode("444");
        customVOList.add(dmCustomCombVO);
        List<DmCustomCombVO> filterCustomCombVOList = new ArrayList<>();
        IRequestContext requestContext = RequestContext.getCurrent();

        asyncService.getFoldGroupCodeList(combinationVO,customVOList, filterCustomCombVOList, requestContext);
        Assert.assertTrue(true);
    }

    @Test
    public void fillPurchaseExportDataTest() throws Exception {

        IRequestContext requestContext = RequestContext.getCurrent();
        HistoryInputVO historyInputVO= new HistoryInputVO();
        DmFocDataRefreshStatus dataRefreshStatus = new DmFocDataRefreshStatus();
        historyInputVO.setIndustryOrg("ICT");
        historyInputVO.setTablePreFix("dm_foc");
        historyInputVO.setVersionId(11L);

        asyncService.fillPurchaseExportData(historyInputVO,dataRefreshStatus, requestContext);
        Assert.assertNull(null);
    }

    @Test
    public void fillMadeExportDataTest() throws Exception {

        IRequestContext requestContext = RequestContext.getCurrent();
        HistoryInputVO historyInputVO= new HistoryInputVO();
        DmFocDataRefreshStatus dataRefreshStatus = new DmFocDataRefreshStatus();
        historyInputVO.setIndustryOrg("ICT");
        historyInputVO.setTablePreFix("dm_foc");
        historyInputVO.setVersionId(11L);

        asyncService.fillMadeExportData(historyInputVO,dataRefreshStatus, requestContext);
        Assert.assertNull(null);
    }

}