{"PagedResult<DmFocAnnualAmpVO>": {"pageVO": {"totalRows": 1, "curPage": 1, "pageSize": 1, "resultMode": 1, "startIndex": 1, "endIndex": 1, "orderBy": "test", "filterStr": "test", "filters": [{"fn": "test", "ft": "test", "fv": {}, "fr": "test"}]}, "result": [{}]}, "DmFocVersionInfoDTO": {"versionId": 1, "version": "test", "versionType": "test", "parentVersionId": 1, "dataType": "test", "startDate": "test", "endDate": "test", "lastUpdateStr": "test", "step": 1, "status": 1, "userId": 1, "creationDate": 1694773658435, "createdBy": 1, "lastUpdatedBy": 1, "lastUpdateDate": 1694773658435, "delFlag": "test"}, "List<DmFocAnnualAmpVO>": [{"id": 1, "versionId": 1, "periodYear": "test", "prodRndTeamCode": "test", "prodRndTeamCnName": "test", "groupCode": "test", "groupCnName": "test", "groupLevel": "LV1", "annualAmp": "1", "parentCode": "test", "parentCnName": "test", "createdBy": "test", "creationDate": 1694773658426, "lastUpdatedBy": "test", "lastUpdateDdate": 1694773658426, "delFlag": "test", "viewFlag": "test", "appendFlag": "test", "weightRate": "0.1", "groupCodeAndName": "test", "weightAnnualAmpPercent": "1.1", "weightAnnualAmpPercentOrder": "10", "statusCode": "test", "hoverMsg": "test", "key": "test", "iv": "test", "appendYear": "test", "l1Name": "test", "l2Name": "test", "dmsCnName": "test", "dmsCode": "test", "customCnName": "test", "customId": 1, "isHasComb": "test", "dimensionCode": "test", "dimensionCnName": "test", "dimensionSubcategoryCode": "test", "dimensionSubcategoryCnName": "test", "dimensionSubDetailCode": "test", "dimensionSubDetailCnName": "test", "costType": "P"}], "int": 1, "AnnualAnalysisVO": {"parentCodeList": ["test", "test_##null", "test_##dddd"], "groupLevel": "LV1", "parentLevel": "test", "multiLevel": "test", "viewFlag": "1", "teamLevel": "test", "yearList": ["test"], "pageIndex": 1, "pageSize": 1, "groupCodeList": ["test"], "subGroupCodeList": ["test"], "orderColumn": "test", "orderMethod": "test", "lv1ProdRdTeamCnName": ["test"], "lv2ProdRdTeamCnName": ["test"], "lv3ProdRdTeamCnName": ["test"], "l1NameList": ["test"], "l2NameList": ["test"], "lv3CegCnName": "test", "lv4CegCnName": "test", "categoryCnName": "test", "year": "test", "versionId": 1, "fileName": "test", "teamCodeList": ["test"], "maxValue": "test", "granularityType": "test", "groupCodeOrder": "test", "caliberFlag": "test", "lv0DimensionSet": ["test"], "lv1DimensionSet": ["test"], "lv2DimensionSet": ["test"], "lv3DimensionSet": ["test"], "periodYear": 1, "overseaFlag": "G", "lv0ProdListCode": "test", "dmsCodeList": ["test"], "dimensionCnName": ["test"], "dimensionSubCategoryCnName": ["test"], "dimensionSubDetailCnName": ["test"], "isMultipleSelect": false, "isShowChildContent": true, "parentCodeOrder": "test", "isContainComb": false, "reverseSymbol": true, "customLevel": "test", "reverseLv1Flag": true, "costType": "P", "customIdList": ["test"], "combinaCodeList": ["test"], "dimensionCodeList": ["test"], "dimensionSubcategoryCodeList": ["test"], "dimensionSubDetailCodeList": ["test"]}, "List<DmFocViewInfoVO>": [{"overseaFlag": "G", "isCombination": true, "caliberFlag": "test", "lv2ProdRdTeamCnName": "test", "itemCode": "test", "dimensionCode": "test", "customCnName": "test", "itemCnName": "test", "l4CegShortCnName": "test", "children": [{"id": 1, "lv0ProdRndTeamCode": "test", "lv0ProdRdTeamCnName": "test", "lv1ProdRndTeamCode": "test", "lv1ProdRdTeamCnName": "test", "lv2ProdRndTeamCode": "test", "lv2ProdRdTeamCnName": "test", "lv3ProdRndTeamCode": "test", "lv3ProdRdTeamCnName": "test", "lv0ProdListCode": "test", "lv0ProdListCnName": "test", "prodRndTeamCode": "test", "l3CegCode": "test", "l3CegCnName": "test", "categoryCode": "test", "categoryCnName": "test", "itemCode": "test", "itemCnName": "test", "createdBy": "test", "creationDate": 1694830657308, "lastUpdatedBy": "test", "lastUpdateDdate": 1694830657308, "delFlag": "test", "viewFlag": "1", "viewFlagValue": "test", "groupCode": "test", "groupLevel": "LV1", "groupCnName": "test", "versionId": 1, "permissionFlag": "test", "removeFlag": "test", "weightRate": 1.0, "granularityType": "test", "subEnableFlag": "test", "isCombination": true, "customId": 1, "parentCode": "test", "parentCnName": "test", "pageFlag": "test", "l1Name": "test", "l2Name": "test", "dimensionCode": "test", "dimensionCnName": "test", "dimensionSubCategoryCode": "test", "dimensionSubCategoryCnName": "test", "dimensionSubDetailCode": "test", "dimensionSubDetailCnName": "test", "userId": "test", "roleId": "test", "enableFlag": "test", "caliberFlag": "test", "overseaFlag": "G", "l3CegShortCnName": "test", "l4CegShortCnName": "test", "l4CegCnName": "test", "l4CegCode": "test", "customCnName": "test", "isSeparate": "test", "connectCode": "test", "connectParentCode": "test", "children": [null], "topItemCode": "test", "itemNum": 1}], "permissionFlag": "test", "groupLevel": "LV1", "lv2ProdRndTeamCode": "test", "id": 1, "l4CegCnName": "test", "groupCode": "test", "lastUpdatedBy": "test", "pageFlag": "test", "dimensionSubDetailCnName": "test", "l3CegCode": "test", "creationDate": 1694830657307, "lv0ProdRndTeamCode": "test", "isSeparate": "test", "versionId": 1, "l3CegCnName": "test", "prodRndTeamCode": "test", "l2Name": "test", "lv1ProdRndTeamCode": "test", "dimensionSubCategoryCnName": "test", "granularityType": "test", "categoryCnName": "test", "delFlag": "test", "connectCode": "test", "customId": 1, "parentCnName": "test", "itemNum": 1, "l1Name": "test", "subEnableFlag": "test", "parentCode": "test", "weightRate": 1.0, "enableFlag": "test", "lv1ProdRdTeamCnName": "test", "dimensionSubDetailCode": "test", "removeFlag": "test", "dimensionCnName": "test", "roleId": "test", "l4CegCode": "test", "dimensionSubCategoryCode": "test", "l3CegShortCnName": "test", "categoryCode": "test", "lv3ProdRndTeamCode": "test", "userId": "test", "viewFlag": "1", "lv0ProdRdTeamCnName": "test", "createdBy": "test", "lv3ProdRdTeamCnName": "test", "groupCnName": "test", "lv0ProdListCode": "test", "topItemCode": "test", "viewFlagValue": "test", "lv0ProdListCnName": "test", "connectParentCode": "test", "lastUpdateDdate": 1694830657307}]}