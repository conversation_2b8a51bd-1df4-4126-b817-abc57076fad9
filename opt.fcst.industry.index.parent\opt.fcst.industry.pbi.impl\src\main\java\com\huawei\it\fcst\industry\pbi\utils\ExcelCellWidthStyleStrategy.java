/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.utils;

import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.CellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.Cell;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ExcelCellWidthStyleStrategy Class
 *
 * <AUTHOR>
 * @since 2024-10-14
 */
public class ExcelCellWidthStyleStrategy extends AbstractColumnWidthStyleStrategy {

    // 可以根据这里的最大宽度，按自己需要进行调整,搭配单元格样式实现类中的，自动换行，效果更好
    // 定义最大列宽常量
    private static final int MAX_COLUMN_WIDTH = 100;

    // 创建一个用于缓存的哈希表
    private Map<Integer, Map<Integer, Integer>> CACHE = new HashMap(8);

    @Override
    protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<WriteCellData<?>> cellDataList, Cell cell,
            Head head, Integer relativeRowIndex, Boolean isHead) {
        // 判断是否需要设置列宽，如果是表头或者单元格数据列表不为空则需要设置列宽
        boolean needSetWidth = isHead ||!CollectionUtils.isEmpty(cellDataList);
        if (needSetWidth) {
            // 获取指定工作表编号对应的最大列宽映射表，如果没有则创建一个新的
            Map<Integer, Integer> maxColumnWidthMap = (Map) CACHE.get(writeSheetHolder.getSheetNo());
            if (maxColumnWidthMap == null) {
                maxColumnWidthMap = new HashMap(16);
                CACHE.put(writeSheetHolder.getSheetNo(), maxColumnWidthMap);
            }

            // 计算列宽
            Integer columnWidth = this.dataLength(cellDataList, cell, isHead);
            if (columnWidth >= 0) {
                // 如果列宽大于最大列宽，则将列宽设置为最大列宽
                if (columnWidth > MAX_COLUMN_WIDTH) {
                    columnWidth = MAX_COLUMN_WIDTH;
                }

                // 获取当前列的最大列宽
                Integer maxColumnWidth = (Integer) ((Map) maxColumnWidthMap).get(cell.getColumnIndex());
                if (maxColumnWidth == null || columnWidth > maxColumnWidth) {
                    // 更新最大列宽映射表中的当前列的最大列宽
                    ((Map) maxColumnWidthMap).put(cell.getColumnIndex(), columnWidth);
                    // 设置工作表中当前列的宽度
                    writeSheetHolder.getSheet().setColumnWidth(cell.getColumnIndex(), columnWidth * 256);
                }

            }
        }
    }

    // 计算数据长度的方法
    private Integer dataLength(List<WriteCellData<?>> cellDataList, Cell cell, Boolean isHead) {
        if (isHead) {
            // 如果是表头，返回表头字符串的字节长度
            return cell.getStringCellValue().getBytes().length;
        } else {
            // 获取单元格数据
            CellData cellData = (CellData) cellDataList.get(0);
            // 获取单元格数据类型
            CellDataTypeEnum type = cellData.getType();
            if (type == null) {
                // 如果数据类型为空，返回 -1
                return -1;
            } else {
                // 根据不同的数据类型返回相应的值的字节长度
                switch (type) {
                    case STRING:
                        return cellData.getStringValue().getBytes(StandardCharsets.UTF_8).length + 4;
                    case BOOLEAN:
                        return cellData.getBooleanValue().toString().getBytes(StandardCharsets.UTF_8).length + 4;
                    case NUMBER:
                        return cellData.getNumberValue().toString().getBytes(StandardCharsets.UTF_8).length + 4;
                    default:
                        return -1;
                }
            }
        }
    }
}