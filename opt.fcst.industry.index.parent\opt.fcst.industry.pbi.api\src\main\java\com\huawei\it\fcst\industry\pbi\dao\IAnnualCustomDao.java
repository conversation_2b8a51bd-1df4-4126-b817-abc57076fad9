/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.dao;

import com.huawei.it.fcst.industry.pbi.vo.annual.AnnualAnalysisVO;
import com.huawei.it.fcst.industry.pbi.vo.annual.DmFocAnnualAmpVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * IAnnualAmpDao
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
public interface IAnnualCustomDao {

    List<DmFocAnnualAmpVO> allIndustryCustomCost(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> allIndustrySummaryCombCost(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> findCombCodeOrderByWeight(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> findSummaryCodeOrderByWeight(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> findCombCodeOrderMinLevelByWeight(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> multiIndustryCustomCombChart(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> distributeCustomChart(AnnualAnalysisVO annualAnalysisVO);

    PagedResult<DmFocAnnualAmpVO> industryCombCustomPage(@Param("annualAnalysisVO") AnnualAnalysisVO annualAnalysisVO, @Param("pageVO") PageVO pageVO);

    List<DmFocAnnualAmpVO> industrySummaryCombList(@Param("annualAnalysisVO") AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> industryCombCustomExcel(@Param("annualAnalysisVO") AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> distributeSummaryCustomChart(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> multiCombSpartChart(AnnualAnalysisVO annualAnalysisVO);
}
