/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.dataprocess;

import cn.hutool.core.util.NumberUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.huawei.his.mqs.common.message.Message;
import com.huawei.it.auth.exception.AuthException;
import com.huawei.it.fcst.config.ConfigUtil;
import com.huawei.it.fcst.industry.index.constant.CommonConstant;
import com.huawei.it.fcst.industry.index.constant.Constant;
import com.huawei.it.fcst.industry.index.dao.IDataCipherTextDao;
import com.huawei.it.fcst.industry.index.impl.mqs.MessageProducer;
import com.huawei.it.fcst.industry.index.service.dataprocess.IDataProcessService;
import com.huawei.it.fcst.industry.index.vo.ciphertext.CipherTextDataVO;
import com.huawei.it.fcst.industry.index.vo.ciphertext.FunctionParamVO;
import com.huawei.it.fcst.industry.index.vo.ciphertext.VarifyTaskVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.ioc.ISelfInject;
import com.huawei.it.jalor5.core.ioc.Jalor;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.core.util.CollectionUtil;
import com.huawei.it.jalor5.core.util.JsonUtil;
import com.huawei.it.jalor5.lookup.LookupItemVO;
import com.huawei.it.jalor5.lookup.service.ILookupItemQueryService;
import com.huawei.it.jalor5.registry.RegistryVO;
import com.huawei.it.jalor5.registry.service.IRegistryQueryService;
import com.huawei.it.jalor5.registry.service.IRegistryService;
import com.huawei.it.soa.common.SoaException;
import com.huawei.it.soa.util.SoaAppTokenClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.cursor.Cursor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.inject.Named;
import javax.servlet.FilterRegistration;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023年03月22日
 */
@JalorResource(code = "DataProcess", desc = "DataProcess")
@Slf4j
@Named("dataProcess")
public class DataProcessService implements IDataProcessService, ISelfInject<IDataProcessService> {
    private static final ObjectMapper MAPPER = new ObjectMapper();

    // 每处理100批次检查一次内存
    private static final double MEMORY_THRESHOLD = 0.8; // 内存使用率阈值80%
    public static final int ENCRYPT_DATA_BATCH_SIZE = 2000;

    // 内存监控相关
    private final MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();

    @Value("${category.lts.url.verify}")
    private String urlVerify;

    private IDataProcessService self;
    @Autowired
    private IDataCipherTextDao iDataCipherTextDao;

    @Autowired
    private TaskExecutorProcessService taskExecutorProcess;

    @Autowired
    private IRegistryQueryService iRegistryQueryService;
    @Autowired
    private IRegistryService registryService;
    @Autowired
    private MessageProducer messageProducer;

    @Autowired
    HttpServletRequest httpServletRequest;

    @Autowired
    private ServletContext servletContext;

    @Autowired
    private ILookupItemQueryService iLookupItemQueryService;

    @Audit(module = "dataProcessService-startDataProcessTask", operation = "startDataProcessTask", message = "解密数据入口")
    @Override
    @JalorOperation(code = "startDataProcessTask", desc = "解密数据入口")
    public void startDataProcessTask(CipherTextDataVO cipherTextDataVO) throws IOException, ApplicationException, SoaException {
        // 参数校验
        periodPramVerify(cipherTextDataVO);
        // 更新状态
        updateProcessStatus();
        String jobId = httpServletRequest.getHeader("jobId");
        String callback = httpServletRequest.getHeader("callback");
        log.info("lts调度status: {},{}", jobId, callback);
        if (StringUtils.isNoneEmpty(jobId, callback)) {
            cipherTextDataVO.setJobId(jobId);
            cipherTextDataVO.setCallback(callback);
        }
        self.dataProcess(cipherTextDataVO, RequestContext.getCurrent(Boolean.TRUE));
    }

    @Audit(module = "dataProcessService-initDataProcessTask", operation = "initDataProcessTask", message = "初始化解密数据入口")
    @Override
    @JalorOperation(code = "initDataProcessTask", desc = "初始化解密数据入口")
    public void initDataProcessTask(CipherTextDataVO cipherTextDataVO) throws ApplicationException, IOException {
        if (CollectionUtil.isNullOrEmpty(cipherTextDataVO.getPeriodIds())) {
            throw new CommonApplicationException("periodIds is null");
        }
        if (cipherTextDataVO.getPeriodIds().size() > 12) {
            throw new CommonApplicationException("There are more than 12 accounting period parameter lists");
        }
        int count = iDataCipherTextDao.countVarifyTask();
        if (count > 0) {
            log.error("varify task is processing");
            throw new CommonApplicationException("varify task is processing");
        }
        boolean sourceflag = CommonConstant.R_SOURCE_TABLE_NAME.equals(cipherTextDataVO.getSourceTableName()) && CommonConstant.R_TARGET_TABLE_NAME.equals(cipherTextDataVO.getTargetTableName());
        if (!((CommonConstant.S_SOURCE_TABLE_NAME.equals(cipherTextDataVO.getSourceTableName())
                && CommonConstant.S_TARGET_TABLE_NAME.equals(cipherTextDataVO.getTargetTableName())) || sourceflag)) {
            throw new CommonApplicationException("SourceTableName:" + cipherTextDataVO.getSourceTableName() + " TargetTableName:" + cipherTextDataVO.getTargetTableName());
        }
        String jobId = httpServletRequest.getHeader("jobId");
        String callback = httpServletRequest.getHeader("callback");
        for (Long periodId : cipherTextDataVO.getPeriodIds()) {
            CipherTextDataVO cipherTextVO = new CipherTextDataVO();
            cipherTextVO.setPeriodId(periodId);
            cipherTextVO.setSourceTableName(cipherTextDataVO.getSourceTableName());
            cipherTextVO.setTargetTableName(cipherTextDataVO.getTargetTableName());
            cipherTextVO.setJobId(jobId);
            cipherTextVO.setCallback(callback);
            // 任务执行情况记录，插入任务记录
            VarifyTaskVO varifyTaskVO = new VarifyTaskVO();
            varifyTaskVO.setPeriodId(periodId);
            insertVerifyTask(varifyTaskVO);
            // 记录日志
            self.dataProcess(cipherTextVO, varifyTaskVO);
        }
    }

    private void insertVerifyTask(VarifyTaskVO varifyTaskVO) {
        Long taskId = iDataCipherTextDao.getVerifyTaskId(varifyTaskVO.getTablePreFix());
        varifyTaskVO.setTaskId(taskId);
        varifyTaskVO.setTaskType("data_cipher");
        varifyTaskVO.setStatus("PROCESSING");
        varifyTaskVO.setCombStatus(null);
        iDataCipherTextDao.insertVerifyTask(varifyTaskVO);
    }

    @Override
    @JalorOperation(code = "startDBFunction", desc = "LTS调度")
    @Audit(module = "dataProcessService-startDBFunction", operation = "startDBFunction", message = "LTS调度")
    public void startDBFunction(FunctionParamVO functionParamVO, HttpServletRequest request) throws IOException, SoaException, CommonApplicationException {
        log.info("LTS调度");
        Map<String, ? extends FilterRegistration> filters = servletContext.getFilterRegistrations();
        log.info("Registered Filters:");
        for (String key : filters.keySet()) {
            FilterRegistration filter = filters.get(key);
            log.info("=====================================================================>>>filter: " + filter.getClassName());
        }
        functionParamVO.setJobId(request.getHeader("jobId"));
        functionParamVO.setCallback(request.getHeader("callback"));
        self.startDBFunctionTask(functionParamVO);
    }

    @Async("taskExecutor")
    @Override
    @Transactional
    public void dataProcess(CipherTextDataVO cipherTextDataVO, IRequestContext requestContext) throws IOException, ApplicationException, SoaException {
        long startTime = System.currentTimeMillis();
        log.info("数据处理开始 - 源表: {}, 目标表: {}, 会计期: {}",
                cipherTextDataVO.getSourceTableName(),
                cipherTextDataVO.getTargetTableName(),
                cipherTextDataVO.getPeriodId());

        // log监控
        logMemoryUsage("处理开始");

        RequestContextManager.setCurrent(requestContext);
        ThreadPoolTaskExecutor threadPoolTaskExecutor = Jalor.getContext().getBean("taskExecutor", ThreadPoolTaskExecutor.class);
        RegistryVO registrySwitch = iRegistryQueryService.findRegistryByPathNoAssert(CommonConstant.PROCESS_STATUS, true);
        String keyStr = ConfigUtil.getInstance().get16PlainText();
        Date creationDate = new Date();
        final List<CipherTextDataVO> cipherTextDataVOList = new ArrayList<>();
        String status = "SUCCESS";
        AtomicInteger count = new AtomicInteger();
        // 阻塞线程，不让查询的线程执行任务，主线程执行任务导致事务不能提交，session 内存过大失败问题
        Semaphore semaphore = new Semaphore(Constant.PoolNumEnum.QUEUE_CAPACITY.getValue());
        // 执行游标获取数据数据，遍历直到数据处理完
        try (Cursor<CipherTextDataVO> cursor = iDataCipherTextDao.getDataListStream(cipherTextDataVO)) {
            for (CipherTextDataVO textDataVO : cursor) {
                cipherTextDataVOList.add(textDataVO);
                if (cipherTextDataVOList.size() % ENCRYPT_DATA_BATCH_SIZE == 0) {
                    count.addAndGet(cipherTextDataVOList.size());
                    log.info("执行：{}", System.currentTimeMillis());
                    List<CipherTextDataVO> copyCipherTextDataVOList = new ArrayList<>(cipherTextDataVOList);
                    try {
                        if (semaphore.tryAcquire(1, TimeUnit.MINUTES)) {
                            taskExecutorProcess.process(copyCipherTextDataVOList, RequestContext.getCurrent(Boolean.TRUE), keyStr, creationDate, semaphore, cipherTextDataVO.getTargetTableName());
                        }
                    } catch (InterruptedException ex) {
                        log.error(ex.getMessage());
                        // 提交任务
                        taskExecutorProcess.process(copyCipherTextDataVOList, RequestContext.getCurrent(Boolean.TRUE), keyStr, creationDate, semaphore, cipherTextDataVO.getTargetTableName());
                    }
                    cipherTextDataVOList.clear();
                    log.info("任务挤压量 {}", threadPoolTaskExecutor.getThreadPoolExecutor().getQueue().size());
                }
            }
            if (!cipherTextDataVOList.isEmpty()) {
                count.addAndGet(cipherTextDataVOList.size());
                List<CipherTextDataVO> copyCipherTextDataVOList = new ArrayList<>(cipherTextDataVOList);
                semaphore.acquire(1);
                taskExecutorProcess.process(copyCipherTextDataVOList, RequestContext.getCurrent(Boolean.TRUE), keyStr, creationDate, semaphore, cipherTextDataVO.getTargetTableName());
                cipherTextDataVOList.clear();
            }
        } catch (IOException | InterruptedException ex) {
            log.error(ex.getMessage());
            status = "FAIL";
        } finally {
            log.info("end");
            registrySwitch.setValue("END");
            registrySwitch.setDescription("本次执行数据：" + count.get());
            registryService.updateRegistry(registrySwitch);

            // 最终监控
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            logMemoryUsage("处理结束");
            logThreadPoolStatus(threadPoolTaskExecutor, "处理结束");

            log.info("数据处理完成 - 总耗时: {} ms, 处理记录数: {}, 批次数: {}, 平均每批次耗时: {} ms",
                    duration, count.get(), ENCRYPT_DATA_BATCH_SIZE,
                    duration / ENCRYPT_DATA_BATCH_SIZE);

            if (StringUtils.isNoneEmpty(cipherTextDataVO.getJobId(), cipherTextDataVO.getCallback())) {
                executeLts(status, cipherTextDataVO.getJobId(), cipherTextDataVO.getCallback());
            }
        }
    }

    @Async("taskExecutor")
    @Override
    @Transactional
    public void dataProcess(CipherTextDataVO parmaVO, VarifyTaskVO varifyTaskVO) {
        final List<CipherTextDataVO> list = new ArrayList<>();
        log.info("parmaVO getPeriodId {}", parmaVO.getPeriodId());
        varifyTaskVO.setStatus("SUCCESS");
        // 执行游标获取数据数据，遍历直到数据处理完
        try (Cursor<CipherTextDataVO> cursor = iDataCipherTextDao.getDataListStream(parmaVO)) {
            for (CipherTextDataVO textDataVO : cursor) {
                list.add(textDataVO);
                if (list.size() == 1000) {
                    log.info("执行：{}", System.currentTimeMillis());
                    List<CipherTextDataVO> temp = list.stream().collect(Collectors.toList());
                    sendMassage(temp, parmaVO);
                    list.clear();
                }
            }
            if (!list.isEmpty()) {
                List<CipherTextDataVO> temp = list.stream().collect(Collectors.toList());
                sendMassage(temp, parmaVO);
                list.clear();
            }
        } catch (IOException exception) {
            varifyTaskVO.setStatus("FAIL");
            log.info("parmaVO getPeriodId status fail");
        } finally {
            // 任务执行结束更新任务表,更新状态
            final CompletableFuture<Void> voidCompletableFuture = CompletableFuture.runAsync(() -> {
                try {
                    this.updateTaskAndLts(varifyTaskVO, parmaVO);
                } catch (IOException | CommonApplicationException e) {
                    e.printStackTrace();
                } catch (SoaException e) {
                    e.printStackTrace();
                }
            });
            log.info("varify task end");
        }
    }

    private void updateTaskAndLts(VarifyTaskVO varifyTaskVO, CipherTextDataVO parmaVO) throws IOException, SoaException, CommonApplicationException {
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        varifyTaskVO.setLastUpdateDate(timestamp);
        iDataCipherTextDao.updateVerifyTask(varifyTaskVO);
        // LTS状态回写
        if (StringUtils.isNoneEmpty(parmaVO.getJobId(), parmaVO.getCallback())) {
            executeLts(varifyTaskVO.getStatus(), parmaVO.getJobId(), parmaVO.getCallback());
        }
    }

    /**
     * 消息发送
     *
     * @param temp  数据列表
     * @param param 参数
     * @throws JsonProcessingException 序列化异常
     */
    private void sendMassage(List<CipherTextDataVO> temp, CipherTextDataVO param) throws JsonProcessingException {
        Message message = new Message();
        // 设置消息业务标示，便于追踪消息轨迹
        message.setBusinessId(temp.get(0).getPrimaryId());
        // 设置消息标签
        message.setTags(param.getTargetTableName());
        message.setProperty("targetTableName", param.getTargetTableName());
        message.setProperty("sourceTableName", param.getSourceTableName());
        // ，注：二进制消息MQS不做转换,Producer和Consumer需协商好序列化和反序列化方式
        message.setBody(MAPPER.writeValueAsBytes(temp));
        messageProducer.sendMessage(message);
    }

    /**
     * 参数校验处理
     *
     * @param cipherTextDataVO 入参对象
     * @throws ApplicationException 异常信息
     */
    private void periodPramVerify(CipherTextDataVO cipherTextDataVO) throws ApplicationException {
        RegistryVO registrySwitch = iRegistryQueryService.findRegistryByPathNoAssert(CommonConstant.DATA_PROCESS_SWITCH, true);
        // 来源表和目标表名称校验，是否能匹配上
        boolean condition = CommonConstant.R_SOURCE_TABLE_NAME.equals(cipherTextDataVO.getSourceTableName())
                && CommonConstant.R_TARGET_TABLE_NAME.equals(cipherTextDataVO.getTargetTableName());
        boolean conditionTwo = CommonConstant.STD_SOURCE_TABLE_NAME.equals(cipherTextDataVO.getSourceTableName())
                && CommonConstant.STD_TARGET_TABLE_NAME.equals(cipherTextDataVO.getTargetTableName());
        if (!((CommonConstant.S_SOURCE_TABLE_NAME.equals(cipherTextDataVO.getSourceTableName())
                && CommonConstant.S_TARGET_TABLE_NAME.equals(cipherTextDataVO.getTargetTableName())) || condition || conditionTwo)) {
            throw new CommonApplicationException("SourceTableName:" + cipherTextDataVO.getSourceTableName() + " TargetTableName:" + cipherTextDataVO.getTargetTableName());
        }
        // 数据初始化开关，上线第一次使用，如果表数据有问题全量刷新，数据库数据删除后再调用lts 任务
        if (Objects.nonNull(registrySwitch) && StringUtils.equalsIgnoreCase("open", registrySwitch.getValue()) && Objects.isNull(cipherTextDataVO.getPeriodId())) {
            // 初始化数据设置会计期为空
            cipherTextDataVO.setPeriodId(null);
        } else {
            String periodId = String.valueOf(cipherTextDataVO.getPeriodId());
            if (StringUtils.isEmpty(periodId) || periodId.length() != 6) {
                throw new CommonApplicationException("Parameter error");
            }
        }
    }

    /**
     * 任务执行控制，执行前更新状态，如果任务在执行，不允许在次执行，要等到任务完成后再执行
     *
     * @throws ApplicationException 异常信息
     */
    private void updateProcessStatus() throws ApplicationException {
        // 设置任务执行方式
        RegistryVO processStatus = iRegistryQueryService.findRegistryByPathNoAssert(CommonConstant.PROCESS_STATUS, true);
        if (Objects.isNull(processStatus)) {
            throw new CommonApplicationException("Check whether the data dictionary node is configured");
        }
        if (CommonConstant.PROCESS_PROCESSING.equalsIgnoreCase(processStatus.getValue())) {
            // 抛异常不让执行
            throw new CommonApplicationException("The task is executing");
        } else {
            processStatus.setValue(CommonConstant.PROCESS_PROCESSING);
            registryService.updateRegistry(processStatus);
        }
    }

    @Async("taskExecutor")
    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void startDBFunctionTask(FunctionParamVO functionParamVO) throws IOException, SoaException, CommonApplicationException {
        log.info("lts调度start");
        Map<String, Object> parameter = new HashMap<>();
        try {
            Set<String> whiteFunctionNameSet = new HashSet<>(CommonConstant.FUNC_NAME);
            try {
                whiteFunctionNameSet.addAll(iLookupItemQueryService.findItemListByClassify("extra_white_function").stream()
                        .map(LookupItemVO::getItemName).collect(Collectors.toSet()));
            } catch (Exception e) {
                log.error("fail to get lookup value", e);
            }

            if (StringUtils.isEmpty(functionParamVO.getFuncName()) || whiteFunctionNameSet.stream().noneMatch(item -> item.equalsIgnoreCase(functionParamVO.getFuncName()))) {
                parameter.put("funcName", functionParamVO.getFuncName());
                parameter.put("X_RESULT_STATUS", "The function name does not exist");
            } else {
                startFunctionTask(functionParamVO, parameter);
            }
        } catch (Exception exception) {
            log.info("lts解密函数调度失败");
            parameter.put("X_RESULT_STATUS", "FAILED");
            log.error(exception.getMessage());
        } finally {
            // LTS状态回写
            log.info("lts调度 end before");
            String status = parameter.get("X_RESULT_STATUS").toString();
            log.info("lts调度 end before functionParamVO.getFuncName() = {} , parameter = {}", functionParamVO.getFuncName(), status);
            log.info("lts调度 end before{}, functionParamVO.getCallback() = {}", functionParamVO.getJobId(), functionParamVO.getCallback());
            executeLts(status, functionParamVO.getJobId(), functionParamVO.getCallback());
            log.info("lts调度 end");
        }
    }

    @Override
    public void delByPeriodId(CipherTextDataVO cipherTextDataVO) {
        iDataCipherTextDao.delByPeriodId(cipherTextDataVO);
    }

    private void startFunctionTask(FunctionParamVO functionParamVO, Map<String, Object> parameter) {
        log.info("startFunctionTask");
        if (CommonConstant.DM_FOC_DATA_ENCRYPT.equals(functionParamVO.getFuncName()) && (StringUtils.isEmpty(functionParamVO.getPeriodId()) || !functionParamVO.getPeriodId().contains("/"))) {
            parameter.put("funcName", functionParamVO.getFuncName());
            parameter.put("X_RESULT_STATUS", "The periodId does not exist or error");
            log.error("The periodId does not exist or error{}", functionParamVO.getPeriodId());
        } else {
            Set<String> periodFunctionNameSet = new HashSet<>(CommonConstant.FUNC_NAME_PERIOD);
            try {
                periodFunctionNameSet.addAll(iLookupItemQueryService.findItemListByClassify("extra_period_function").stream()
                        .map(LookupItemVO::getItemName).collect(Collectors.toSet()));
            } catch (Exception e) {
                log.error("fail to get lookup value", e);
            }
            if (periodFunctionNameSet.stream().anyMatch(item -> item.equalsIgnoreCase(functionParamVO.getFuncName()))) {
                String[] splitPeriod = functionParamVO.getPeriodId().split("/");
                String periodId = splitPeriod[0] + splitPeriod[1];
                parameter.put("f_period_id", NumberUtil.parseLong(periodId));
            }
            parameter.put("f_industry_flag", functionParamVO.getIndustryFlag());
            parameter.put("f_year", functionParamVO.getYear());
            parameter.put("f_version_id", functionParamVO.getVersionId());
            parameter.put("f_view_flag", functionParamVO.getViewFlag());
            parameter.put("f_caliber", functionParamVO.getCaliberFlag());
            parameter.put("f_dimension", functionParamVO.getDimensionType());
            parameter.put("funcName", functionParamVO.getFuncName().toUpperCase(Locale.ROOT));
            parameter.put("f_keystr", ConfigUtil.getInstance().get16PlainText());
            parameter.put("f_custom_id", functionParamVO.getCustomId());
            parameter.put("f_customization_id", functionParamVO.getCustomizationId());
            parameter.put("f_item_version", functionParamVO.getItemVersion());
            parameter.put("f_oversea_flag", functionParamVO.getOverseaFlag());
            parameter.put("f_lev_num", functionParamVO.getLevNum());
            parameter.put("f_page_type", functionParamVO.getPageType());
            parameter.put("f_cost_type", functionParamVO.getCostType());
            parameter.put("f_ytd_flag", functionParamVO.getYtdFlag());
            parameter.put("f_granularity_type", functionParamVO.getGranularityType());
            parameter.put("f_lv0_prod_list_code", functionParamVO.getLv0ProdListCode());
            parameter.put("X_RESULT_STATUS", "");
            iDataCipherTextDao.startFunctionTask(parameter);
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("funcName", functionParamVO.getFuncName());
            resultMap.put("X_RESULT_STATUS", parameter.get("X_RESULT_STATUS"));
        }
    }

    private Map executeLts(String status, String jobId, String callback) throws IOException, AuthException, SoaException, CommonApplicationException {
        RestTemplate vegaRestTemplate = new RestTemplate();
        if (StringUtils.isNotBlank(callback) && !callback.startsWith(urlVerify)) {
            throw new CommonApplicationException("路径不正确");
        }
        String url = callback + "commitAsyncJobStatus";
        Map<String, String> argsMap = new HashMap<>();
        argsMap.put("LTS_JOB_ID", jobId);
        argsMap.put("appid", SoaAppTokenClientUtil.getSoaAppId());
        // 1 成功 ; 0 失败
        if ("SUCCESS".equalsIgnoreCase(status)) {
            argsMap.put("LTS_JOB_STATUS", "1");
        } else {
            argsMap.put("LTS_JOB_STATUS", "0");
        }
        argsMap.put("V", "1.0");
        argsMap.put("LTS_JOB_OPT", "0");
        MultiValueMap<String, Object> formPara = new LinkedMultiValueMap<>();
        formPara.add("appid", SoaAppTokenClientUtil.getSoaAppId());
        formPara.add("w3c", SoaAppTokenClientUtil.getSoaAppId());
        formPara.add("source", "lts");
        formPara.add("asyncjobinfo", JsonUtil.objectToJson(argsMap));
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.add("Authorization", SoaAppTokenClientUtil.getBasicTokenByAppCredential());
        return vegaRestTemplate.postForObject(url, new HttpEntity<>(formPara, headers), Map.class);
    }

    /**
     * 监控内存使用情况
     *
     * @param context 上下文信息，用于日志记录
     */
    private void logMemoryUsage(String context) {
        try {
            MemoryUsage heapMemoryUsage = memoryBean.getHeapMemoryUsage();
            MemoryUsage nonHeapMemoryUsage = memoryBean.getNonHeapMemoryUsage();

            long heapUsed = heapMemoryUsage.getUsed();
            long heapMax = heapMemoryUsage.getMax();
            long nonHeapUsed = nonHeapMemoryUsage.getUsed();

            double heapUsagePercent = (double) heapUsed / heapMax * 100;

            log.info("内存监控 [{}] - 堆内存使用: {}/{} MB ({:.2f}%), 非堆内存使用: {} MB",
                context,
                heapUsed / (1024 * 1024),
                heapMax / (1024 * 1024),
                heapUsagePercent,
                nonHeapUsed / (1024 * 1024));

            // 内存使用率过高时发出警告
            if (heapUsagePercent > MEMORY_THRESHOLD * 100) {
                log.warn("内存使用率过高 [{}] - 当前使用率: {:.2f}%, 建议进行GC", context, heapUsagePercent);
            }
        } catch (Exception e) {
            log.error("内存监控失败: {}", e.getMessage());
        }
    }

    /**
     * 检查线程池状态
     *
     * @param threadPoolTaskExecutor 线程池执行器
     * @param context 上下文信息
     */
    private void logThreadPoolStatus(ThreadPoolTaskExecutor threadPoolTaskExecutor, String context) {
        try {
            int activeCount = threadPoolTaskExecutor.getActiveCount();
            int poolSize = threadPoolTaskExecutor.getPoolSize();
            int maxPoolSize = threadPoolTaskExecutor.getMaxPoolSize();
            int queueSize = threadPoolTaskExecutor.getThreadPoolExecutor().getQueue().size();

            log.info("线程池状态 [{}] - 活跃线程: {}, maxPoolSize: {}/{}, 队列大小: {}",
                context, activeCount, poolSize, maxPoolSize, queueSize);

            // 队列积压过多时发出警告
            if (queueSize > maxPoolSize * 2) {
                log.warn("线程池队列积压严重 [{}] - 队列大小: {}, 建议优化处理速度", context, queueSize);
            }
        } catch (Exception e) {
            log.error("线程池状态监控失败: {}", e.getMessage());
        }
    }


    @Override
    public void setSelf(IDataProcessService self) {
        this.self = self;
    }
}
