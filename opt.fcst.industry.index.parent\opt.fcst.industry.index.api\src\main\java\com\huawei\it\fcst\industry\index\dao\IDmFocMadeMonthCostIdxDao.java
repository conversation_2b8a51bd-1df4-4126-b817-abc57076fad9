/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.month.DmFocMonthCostIdxVO;
import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * The DAO to access DmFocMonthCostIdxT entity
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2023-03-10 11:14:15
 */
public interface IDmFocMadeMonthCostIdxDao {

    /**
     * 校验所选基期是否有数据
     *
     * @param monthAnalysisVO 参数Dto
     * @return int
     */
    int findMadePriceIdxByBasePeriodId(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);

    int findMadeCombPriceIdxByBasePeriodId(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);

    /**
     * Find all DmFoiPriceIndexVO records.
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<DmFocMonthCostIdxVO> findMadePriceIndexVOList(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);

    /**
     * Find all DmFoiPriceIndexVO records.
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<DmFocMonthCostIdxVO> findMadeComparePriceIndexVOList(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);

    /**
     * Find all DmFoiPriceIndexVO records.
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<DmFocMonthCostIdxVO> findMadeCombPriceIndexVOList(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);

    /**
     * [查询采购价格指数（多维度）图数据]
     *     多维度：多专家团、多品类、多Item
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<DmFocMonthCostIdxVO> findMadePriceIndexByMultiDim(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);


    List<DmFocMonthCostIdxVO> findMadePriceIndexNormalByMultiDim(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);

    /**
     * [查询采购价格指数（多维度）图数据]
     *     多维度：多专家团、多品类、多Item
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<DmFocMonthCostIdxVO> findMadePriceIndexCombByMultiDim(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);


    /**
     * [查询采购价格指数（多维度）图数据]
     *     多ItemCode集合
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    PagedResult<DmFocMonthCostIdxVO> findAllItemCodeForComb(@Param("monthAnalysisVO") MonthAnalysisVO monthAnalysisVO, @Param("pageVO") PageVO pageVO);

    /**
     * [查询采购价格指数（多维度）图数据]
     *     多ItemCode集合
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    PagedResult<DmFocMonthCostIdxVO> findAllItemCodeForAll(@Param("monthAnalysisVO") MonthAnalysisVO monthAnalysisVO, @Param("pageVO") PageVO pageVO);

    /**
     * [查询采购价格指数（多维度）图数据]导出
     *     多维度：多专家团、多品类、多Item
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<DmFocMonthCostIdxVO> findPriceIndexExpData(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);

    /**
     * 查询制造成本下的开始和结束时间
     *
     * @param granularityType 颗粒度（U：通用，P：盈利，D:量纲）
     * @return List
     */
    Map<String,Long> findStartEndTime(@Param("granularityType") String granularityType, @Param("tablePreFix") String tablePreFix);

    List<DmFocMonthCostIdxVO> findAmpMadePriceIndexChart(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);

}
