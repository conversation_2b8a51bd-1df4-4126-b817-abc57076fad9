/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.vo.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024/6/26
 */
// 不能修改成getter和setter!!
@Data
@NoArgsConstructor
@ApiModel(value = "定价指数基础VO")
public class CommonPriceBaseVO {

    @ApiModelProperty("版本")
    private Long versionId;

    @ApiModelProperty("各层级Level")
    private String groupLevel;

    @ApiModelProperty("各层级编码")
    private String groupCode;

    @ApiModelProperty("产品LV0编码")
    private String lv0ProdListCode;

    @ApiModelProperty("产品LV0编码集合")
    private List<String> lv0ProdListCodeList;

    @ApiModelProperty("产品LV1编码集合")
    private List<String> lv1ProdListCodeList;

    @ApiModelProperty("产品LV2编码集合")
    private List<String> lv2ProdListCodeList;

    @ApiModelProperty("产品LV3编码集合")
    private List<String> lv3ProdListCodeList;

    @ApiModelProperty("产品LV4编码集合")
    private List<String> lv4ProdListCodeList;

    @ApiModelProperty("产品LV0名称集合")
    private List<String> lv0ProdListCnNameList;

    @ApiModelProperty("产品LV1名称集合")
    private List<String> lv1ProdListCnNameList;

    @ApiModelProperty("产品LV2名称集合")
    private List<String> lv2ProdListCnNameList;

    @ApiModelProperty("产品LV3名称集合")
    private List<String> lv3ProdListCnNameList;

    @ApiModelProperty("产品LV4名称集合")
    private List<String> lv4ProdListCnNameList;

    @ApiModelProperty(value = "L1-L3.5编码list")
    private List<String> prodListCodeList;

    @ApiModelProperty("SPART编码集合")
    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<String> spartCodeList;

    @ApiModelProperty("SPART编码")
    private String spartCode;

    @ApiModelProperty("SPART名称")
    private String spartCnName;

    @ApiModelProperty("国内海外标识")
    private String overseaFlag;

    @ApiModelProperty("bg编码")
    private String bgCode;

    @ApiModelProperty("bg名称")
    private String bgCnName;

    @ApiModelProperty("地区部编码")
    private String regionCode;

    @ApiModelProperty("地区部名称")
    private String regionCnName;

    @ApiModelProperty("代表处编码")
    private String repofficeCode;

    @ApiModelProperty("代表处名称")
    private String repofficeCnName;

    @ApiModelProperty("签约客户_大T系统部编码")
    private String signTopCustCategoryCode;

    @ApiModelProperty("签约客户_大T系统部名称")
    private String signTopCustCategoryCnName;

    @ApiModelProperty("签约客户_子网系统部名称")
    private String signSubsidiaryCustcatgCnName;

    @ApiModelProperty("路径区分")
    private String viewFlag;

    @ApiModelProperty("页码")
    private int pageIndex;

    @ApiModelProperty("一页数量")
    private int pageSize;

    @ApiModelProperty("总条数")
    private int totalSize;

    @ApiModelProperty("lvCode")
    private String lvCode;

    @ApiModelProperty("父层级")
    private String parentLevel;

    @ApiModelProperty("区分页面标识")
    private String pageType;

    @ApiModelProperty("是否另存页标识")
    private Boolean saveFlag;

    @ApiModelProperty("下个层级")
    private String nextGroupLevel;

    @ApiModelProperty("会计月开始时间")
    private Integer periodStartTime;

    @ApiModelProperty("会计月结束时间")
    private Integer periodEndTime;

    @ApiModelProperty("ICT产业集合")
    private Set<String> lv0DimensionSet = new HashSet<>();

    @ApiModelProperty("pbi目录树")
    private Set<String> lv1DimensionSet  = new HashSet<>();

    @ApiModelProperty("LV1集合")
    private Set<String> lv2DimensionSet  = new HashSet<>();

    private Set<String> lv3DimensionSet  = new HashSet<>();

    @ApiModelProperty("路径展示")
    private String displayName;

    @ApiModelProperty("关键字搜索")
    private String keyword;

    @ApiModelProperty("最大年")
    private Integer maxYear;

    private Set<String> overseaFlagDimensionSet  = new HashSet<>();

    private Set<String> regionCodeDimensionSet = new HashSet<>();

    private Set<String> repofficeCodeDimensionSet = new HashSet<>();

    // 是否区域分析师
    private Boolean isRegionAnalyst = false;

}
