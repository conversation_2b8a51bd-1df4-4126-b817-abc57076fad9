/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.common;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

/**
 * UploadInfoVOTest Class
 *
 * <AUTHOR>
 * @since 2023/4/22
 */
public class UploadInfoVOTest extends BaseVOCoverUtilsTest<UploadInfoVO> {

    @Override
    protected Class<UploadInfoVO> getTClass() {
        return UploadInfoVO.class;
    }

    @Test
    public void testMethod() {
        UploadInfoVO dmFocActualCostVO = new UploadInfoVO();
        DmFoiImpExpRecordVO recordVO = new DmFoiImpExpRecordVO();
        recordVO.setUserId("1175");
        dmFocActualCostVO.setDmFoiImpExpRecord(recordVO);
        dmFocActualCostVO.getDmFoiImpExpRecord();
        Assert.assertNotNull(dmFocActualCostVO);
    }
}