/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.util;

import com.huawei.it.fcst.common.StatisticsExcelService;
import com.huawei.it.fcst.export.vo.PbiDmFoiImpExpRecordVO;
import com.huawei.it.fcst.vo.AbstractExcelTitleVO;
import com.huawei.it.fcst.vo.BranchExcelTitleVO;
import com.huawei.it.fcst.vo.ExcelInputVO;
import com.huawei.it.fcst.vo.ExcelUploadVO;
import com.huawei.it.fcst.vo.ExportExcelVo;
import com.huawei.it.fcst.vo.LeafExcelTitleVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.util.CollectionUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.DefaultIndexedColorMap;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.awt.Color;
import java.io.IOException;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * The class PoiEnum.java
 *
 * <AUTHOR>
 * @since 2022年1月12日
 */
public enum PoiEnum {
    INSTANCE;

    private static final Logger LOGGER = LoggerFactory.getLogger(PoiEnum.class);

    private static final String MICROSOFT_YAHEI = "微软雅黑";

    private static final String SONGSTYLE = "宋体";

    private static final String TITLECOLORFLAG = "TITLECOLORFLAG";

    private static final String CONFIGHEADERCOLORFLAG = "CONFIGHEADERCOLORFLAG";

    private static final String HEADERCOLORFLAG = "HEADERCOLORFLAG";

    private static final String BASEPERIODFLAG = "BASEPERIODFLAG";

    private static final XSSFColor WHITE_COLOR = new XSSFColor(new Color(197, 217, 241), new DefaultIndexedColorMap());

    private static final XSSFColor BLUE_COLOR = new XSSFColor(new Color(100, 198, 255), new DefaultIndexedColorMap());

    private static final XSSFColor DARK_BLUE_COLOR =  new XSSFColor(new Color(31, 73, 125), new DefaultIndexedColorMap());

    private static final int  FONT_SIZE = 11;

    /**
     * 导出excel
     *
     * @param os                OutputStream
     * @param exportExcelVoList List<ExportExcelVo>
     * @throws IOException
     */
    public static PbiDmFoiImpExpRecordVO exportExcel(OutputStream os, List<ExportExcelVo> exportExcelVoList)
            throws IOException, CommonApplicationException {
        SXSSFWorkbook fileWorkbook = new SXSSFWorkbook();

        ExcelUploadVO excelVO = new ExcelUploadVO();
        // 生成一个新的excel放到服务器上
        getWorkbook(fileWorkbook, exportExcelVoList, excelVO);
        String fileName = excelVO.getFileName();
        Long userId = excelVO.getUserId();

        // 插入数据，上传文件
        return StatisticsExcelService.uploadExportExcel(fileWorkbook, excelVO.getCount(), fileName, userId);
    }

    private static void getWorkbook(SXSSFWorkbook workbook, List<ExportExcelVo> exportExcelVoList,
                                    ExcelUploadVO excelUploadVO) throws IOException {
        int count = 0;
        for (ExportExcelVo exportVo : exportExcelVoList) {
            List<AbstractExcelTitleVO> excelTitleVOS = exportVo.getTitleVoList(); // 表头
            List<AbstractExcelTitleVO> selectedLeafExcelTitleVO = exportVo.getSelectedLeafExcelTitleVO();
            int titleRowCount = exportVo.getTitleRowCount();
            String sheetName = exportVo.getSheetName();
            if (sheetName.contains("/") || sheetName.contains("\\")) {
                sheetName = sheetName.replace("/", "-");
            }
            SXSSFSheet sheet = workbook.createSheet(sheetName);
            Map<Integer, Row> titleRowMap = new HashMap<>();
            for (int idx = 1; idx < titleRowCount; idx++) {
                Row row = sheet.createRow(idx);
                if (!titleRowMap.containsKey(idx)) {
                    titleRowMap.put(idx, row);
                }
            }
            boolean isLock = isLockExcelSheet(selectedLeafExcelTitleVO);
            // 设置Excel单元格样式
            CellStyles cellStyles = getCellStyles(workbook);
            // 插入表单信息
            List<AbstractExcelTitleVO> formsVOs = exportVo.getFormInfoVo();
            ExcelInputVO excelInputVO = new ExcelInputVO();
            excelInputVO.setExportExcelVo(exportVo);
            excelInputVO.setExcelTitleVOS(excelTitleVOS);
            excelInputVO.setSheet(sheet);
            excelInputVO.setSelectedLeafExcelTitleVO(selectedLeafExcelTitleVO);
            excelInputVO.setFormsVOs(formsVOs);
            extracted(workbook, titleRowMap, cellStyles,excelInputVO);
            // 插入数据
            List<Map> list = exportVo.getList();
            Boolean mergeCell = exportVo.getMergeCell();
            Long userId = exportVo.getUserId();
            count = count + list.size();
            excelUploadVO.setCount(count);
            excelUploadVO.setFileName(FileNameUtil.dealFileName(exportVo.getFileName()));
            excelUploadVO.setMergeCell(mergeCell);
            excelUploadVO.setUserId(userId);
            addData(titleRowCount, list, selectedLeafExcelTitleVO, sheet, cellStyles);
            if (isLock) {
                sheet.setAutoFilter(
                        new CellRangeAddress(sheet.getFirstRowNum() + titleRowCount - 1, sheet.getLastRowNum(), 0,
                                selectedLeafExcelTitleVO.size() - 1));
                lockSheet(sheet);
            }
            // 避免占用太多内存
            sheet.flushRows();
        }
    }

    public static void extracted(SXSSFWorkbook workbook, Map<Integer, Row> titleRowMap, CellStyles cellStyles,ExcelInputVO  excelInputVO) {
        SXSSFSheet sheet = excelInputVO.getSheet();
        ExportExcelVo exportExcelVo = excelInputVO.getExportExcelVo();
        List<AbstractExcelTitleVO> selectedLeafExcelTitleVOs = excelInputVO.getSelectedLeafExcelTitleVO();
        List<AbstractExcelTitleVO> excelTitleVOS = excelInputVO.getExcelTitleVOS();
        List<AbstractExcelTitleVO> formsVOs = excelInputVO.getFormsVOs();
        if (exportExcelVo.getMergeCell()) {
            // 合并单元格
            CellRangeAddress rangeAddress = new CellRangeAddress(0, 0, 0, selectedLeafExcelTitleVOs.size() - 1);
            sheet.addMergedRegion(rangeAddress);
            addTitleInfoVo(sheet, formsVOs, selectedLeafExcelTitleVOs, cellStyles);
            // 插入表头信息
            if (!CollectionUtil.isNullOrEmpty(excelTitleVOS) && !CollectionUtil.isNullOrEmpty(
                    selectedLeafExcelTitleVOs)) {
                creatHead(workbook, excelTitleVOS, titleRowMap, sheet);
            }
        } else {
            addFormInfoVo(sheet, formsVOs, cellStyles, selectedLeafExcelTitleVOs);
            // 插入表头信息
            if (!CollectionUtil.isNullOrEmpty(excelTitleVOS) && !CollectionUtil.isNullOrEmpty(
                    selectedLeafExcelTitleVOs)) {
                creatConfigHead(workbook, excelTitleVOS, selectedLeafExcelTitleVOs, titleRowMap, sheet);
            }
        }
    }

    public static boolean isLockExcelSheet(List<AbstractExcelTitleVO> selectedLeafExcelTitleVOs) {
        boolean isLock = false;
        if (!CollectionUtil.isNullOrEmpty(selectedLeafExcelTitleVOs)) {
            for (AbstractExcelTitleVO selectedLeafTitleVO : selectedLeafExcelTitleVOs) {
                if (selectedLeafTitleVO.getEditable() != null && !selectedLeafTitleVO.getEditable()) {
                    isLock = true;
                    break;
                }
            }
        }
        return isLock;
    }

    public static CellStyles getCellStyles(SXSSFWorkbook sxssfWorkbook) {
        CellStyle bodyRateStyle = createRateValueHeaderCellStyle(sxssfWorkbook);
        CellStyle bodyStyles = createLeftCellStyle(sxssfWorkbook);
        CellStyle bodyEditStyles = createLeftEditCellStyle(sxssfWorkbook);
        CellStyle bodyTitleStyles1 = createTitleCellStyle1(sxssfWorkbook);
        CellStyle bodyTitleStyles2 = createTitleCellStyle2(sxssfWorkbook);
        CellStyle bodyTitleStyles3 = createTitleCellStyle3(sxssfWorkbook);
        return CellStyles.builder()
                .bodyRateStyle(bodyRateStyle)
                .bodyStyles(bodyStyles)
                .bodyEditStyles(bodyEditStyles)
                .bodyTitleStyles1(bodyTitleStyles1)
                .bodyTitleStyles2(bodyTitleStyles2)
                .bodyTitleStyles3(bodyTitleStyles3)
                .build();
    }

    private static CellStyle createTitleCellStyle3(SXSSFWorkbook sxssfWorkbook) {
        CellStyle baseCellStyle = createBaseCellStyle(sxssfWorkbook);
        baseCellStyle.setLocked(true);
        Font font = setFont(sxssfWorkbook, baseCellStyle);
        baseCellStyle.setFont(font);
        return baseCellStyle;
    }

    private static CellStyle createRateValueHeaderCellStyle(SXSSFWorkbook sxssfWorkbook) {
        CellStyle style = createBaseCellStyle(sxssfWorkbook);
        DataFormat dataFormat = sxssfWorkbook.createDataFormat();
        style.setDataFormat(dataFormat.getFormat("0.00%"));
        style.setLocked(true);
        return style;
    }

    private static CellStyle createLeftCellStyle(SXSSFWorkbook workbook) {
        CellStyle cellStyle = createBaseCellStyle(workbook);
        cellStyle.setLocked(true);
        return cellStyle;
    }

    private static CellStyle createLeftEditCellStyle(SXSSFWorkbook workbook) {
        CellStyle leftEditCellStyle = createBaseCellStyle(workbook);
        leftEditCellStyle.setLocked(false);
        return leftEditCellStyle;
    }

    private static CellStyle createTitleCellStyle1(SXSSFWorkbook workbook) {
        CellStyle titleCellStyle = createCellStyle(workbook, TITLECOLORFLAG, false);
        Font ft = setFont(workbook, titleCellStyle);
        ft.setColor((short) 1);
        titleCellStyle.setFont(ft);
        titleCellStyle.setLocked(true);
        return titleCellStyle;
    }

    private static CellStyle createTitleCellStyle2(SXSSFWorkbook workbook) {
        CellStyle cellTitleStyle = createCellStyle(workbook, "", true);
        Font font = setFont(workbook, cellTitleStyle);
        font.setBold(false);
        cellTitleStyle.setAlignment(HorizontalAlignment.LEFT);
        cellTitleStyle.setFont(font);
        cellTitleStyle.setLocked(true);
        return cellTitleStyle;
    }

    private static void addFormInfoVo(SXSSFSheet sheet, List<AbstractExcelTitleVO> formsVOs, CellStyles cellStyles, List<AbstractExcelTitleVO> selectedLeafExcelTitleVO) {
        Row row = sheet.createRow(0);
        int columnIndex = 0;
        if (null != formsVOs) {
            for (AbstractExcelTitleVO titleVO : formsVOs) {
                if (titleVO instanceof LeafExcelTitleVO) {
                    if ("version".equals(((LeafExcelTitleVO) titleVO).getColumnCode())) {
                        columnIndex = 0;
                    } else {
                        columnIndex = selectedLeafExcelTitleVO.size() - 1;
                    }
                    String cellValue = getInfoCellValue(titleVO);
                    Cell headCelStyle = getHeadCell3(cellStyles, row, columnIndex);
                    setInfoCellValue(headCelStyle, columnIndex, cellValue, titleVO.getWidth(),
                            ((LeafExcelTitleVO) titleVO).getDataType());
                    columnIndex++;
                }
            }
        }
    }

    private static void addTitleInfoVo(SXSSFSheet sheet, List<AbstractExcelTitleVO> formsVOs,
                                       List<AbstractExcelTitleVO> selectedLeafExcelTitleVO, CellStyles cellStyles) {
        Row row = sheet.createRow(0);
        int columnIndex = 0;
        for (AbstractExcelTitleVO excelTitleVO : formsVOs) {
            Cell headCelStyle = null;
            if (excelTitleVO instanceof LeafExcelTitleVO && ("title").equals(((LeafExcelTitleVO) excelTitleVO).getColumnCode())) {
                headCelStyle = getTitleCell(cellStyles, row, columnIndex);
            } else {
                headCelStyle = getActualMonthCell(cellStyles, row, columnIndex);
            }
            String cellValue = getInfoCellValue(excelTitleVO);
            setInfoCellValue(headCelStyle, columnIndex, cellValue, excelTitleVO.getWidth(),
                    ((LeafExcelTitleVO) excelTitleVO).getDataType());
            columnIndex = columnIndex + selectedLeafExcelTitleVO.size();
        }
    }

    private static void setInfoCellValue(Cell headCelStyle, int columnIndex, String cellValue, Integer width,
                                         CellType dataType) {
        if (StringUtils.isEmpty(cellValue)) {
            headCelStyle.setCellType(CellType.BLANK);
        } else {
            headCelStyle.setCellValue(cellValue);
            headCelStyle.setCellType(dataType);
        }
        headCelStyle.getSheet().setColumnWidth(columnIndex, width);
    }

    private static String getInfoCellValue(AbstractExcelTitleVO abstractExcelTitleVO) {
        Object cellValue = abstractExcelTitleVO.getValue();
        if (ObjectUtils.isEmpty(cellValue)) {
            cellValue = "";
        }
        return cellValue.toString();
    }

    public static void lockSheet(SXSSFSheet sheet) {
        sheet.lockSelectLockedCells(false);
        sheet.lockSelectUnlockedCells(false);
        sheet.lockFormatCells(false);
        sheet.lockFormatColumns(false);
        sheet.lockFormatRows(false);
        sheet.lockDeleteRows(false);
        sheet.lockAutoFilter(false);
        sheet.enableLocking();
    }

    public static void addData(int titleRowCount, List<Map> list, List<AbstractExcelTitleVO> selectedLeafExcelTitleVO,
                               Sheet sheet, CellStyles cellStyles) {
        int rowNum = titleRowCount;
        for (Map rowMap : list) {
            Row row = sheet.createRow(rowNum++);
            int columnIndex = 0;
            addDataSub(selectedLeafExcelTitleVO, cellStyles, rowMap, row, columnIndex);
        }
    }

    private static void addDataSub(List<AbstractExcelTitleVO> selectedLeafExcelTitleVO, CellStyles cellStyles, Map rowMap, Row row, int columnIndex) {
        for (AbstractExcelTitleVO excelTitleVO : selectedLeafExcelTitleVO) {
            if (excelTitleVO instanceof LeafExcelTitleVO) {
                String cellValue = getCellValue(rowMap, (LeafExcelTitleVO) excelTitleVO);
                if (((LeafExcelTitleVO) excelTitleVO).getDataType().equals(CellType.NUMERIC)) {
                    Cell headCelStyle = getHeadCel12(cellStyles, row, columnIndex);
                    setCellValueAndStyle(headCelStyle, columnIndex, cellValue, excelTitleVO.getWidth(),
                            CellType.NUMERIC);
                } else if (excelTitleVO.getEditable()) {
                    Cell headCelStyle = getHeadCel13(cellStyles, row, columnIndex);
                    setCellValueAndStyle(headCelStyle, columnIndex, cellValue, excelTitleVO.getWidth(),
                            ((LeafExcelTitleVO) excelTitleVO).getDataType());
                } else {
                    Cell headCelStyle = getHeadBodyStyles(cellStyles, row, columnIndex);
                    setCellValueAndStyle(headCelStyle, columnIndex, cellValue, excelTitleVO.getWidth(),
                            ((LeafExcelTitleVO) excelTitleVO).getDataType());
                }
                columnIndex++;
            }
        }
    }

    private static Cell getHeadBodyStyles(CellStyles cellStyles, Row row, int columnIndex) {
        Cell headCelStyle = row.createCell(columnIndex);
        if (cellStyles != null) {
            headCelStyle.setCellStyle(cellStyles.getBodyStyles());
        }
        return headCelStyle;
    }

    private static Cell getHeadCell3(CellStyles cellStyles, Row row, int columnIndex) {
        Cell headCelStyle = row.createCell(columnIndex);
        if (cellStyles != null) {
            headCelStyle.setCellStyle(cellStyles.getBodyTitleStyles3());
        }
        return headCelStyle;
    }

    private static Cell getTitleCell(CellStyles cellStyles, Row row, int columnIndex) {
        Cell headCelStyle = row.createCell(columnIndex);
        if (cellStyles != null) {
            headCelStyle.setCellStyle(cellStyles.getBodyTitleStyles1());
        }
        return headCelStyle;
    }

    private static Cell getActualMonthCell(CellStyles cellStyles, Row row, int columnIndex) {
        Cell headCelStyle = row.createCell(columnIndex);
        if (cellStyles != null) {
            headCelStyle.setCellStyle(cellStyles.getBodyTitleStyles2());
        }
        return headCelStyle;
    }

    private static Cell getHeadCel12(CellStyles cellStyle, Row row, int columnIndex) {
        Cell headCelStyle = row.createCell(columnIndex);
        if (cellStyle != null) {
            headCelStyle.setCellStyle(cellStyle.getBodyRateStyle());
        }
        return headCelStyle;
    }

    private static Cell getHeadCel13(CellStyles cellStyle, Row row, int columnIndex) {
        Cell headCelStyle = row.createCell(columnIndex);
        if (cellStyle != null) {
            headCelStyle.setCellStyle(cellStyle.getBodyEditStyles());
        }
        return headCelStyle;
    }

    private static String getCellValue(Map rowMap, LeafExcelTitleVO leafExcelTitleVO) {
        Object cellValue = rowMap.get(leafExcelTitleVO.getDataKey());
        if (ObjectUtils.isEmpty(cellValue)) {
            cellValue = "";
        }
        return cellValue.toString();
    }

    private static void creatHead(SXSSFWorkbook workbook, List<AbstractExcelTitleVO> excelTitleVOS,
                                  Map<Integer, Row> titleRowMap, Sheet sheet) {
        for (AbstractExcelTitleVO excelTitle : excelTitleVOS) {
            if (excelTitle.getSelected() == null || !excelTitle.getSelected()) {
                continue;
            }
            creatHeadSub(workbook, excelTitleVOS, titleRowMap, sheet, excelTitle);
        }
    }

    private static void creatHeadSub(SXSSFWorkbook workbook, List<AbstractExcelTitleVO> excelTitleVOS, Map<Integer, Row> titleRowMap, Sheet sheet, AbstractExcelTitleVO leafExcelTitleVO) {
        if (leafExcelTitleVO instanceof LeafExcelTitleVO && ("costType").equals(((LeafExcelTitleVO) leafExcelTitleVO).getColumnCode())) {
            CellStyle headStyle1 = createCellStyle(workbook, BASEPERIODFLAG, true);
            Font font = setFont2(workbook, headStyle1);
            headStyle1.setFont(font);
            setExcelCellValueAndStyle(excelTitleVOS, titleRowMap, headStyle1, leafExcelTitleVO);
        } else {
            CellStyle headStyle1 = createCellStyle(workbook, HEADERCOLORFLAG, true);
            Font ft = setFont(workbook, headStyle1);
            headStyle1.setFont(ft);
            setExcelCellValueAndStyle(excelTitleVOS, titleRowMap, headStyle1, leafExcelTitleVO);
        }
        if (leafExcelTitleVO.getX1() < leafExcelTitleVO.getX2() || leafExcelTitleVO.getY1() < leafExcelTitleVO.getY2()) {
            sheet.addMergedRegion(
                    new CellRangeAddress(leafExcelTitleVO.getX1(), leafExcelTitleVO.getX2(), leafExcelTitleVO.getY1(),
                            leafExcelTitleVO.getY2()));
        }
        if (leafExcelTitleVO instanceof BranchExcelTitleVO) {
            if (!CollectionUtil.isNullOrEmpty(((BranchExcelTitleVO) leafExcelTitleVO).getChildExcelTitleList())) {
                creatHead(workbook, ((BranchExcelTitleVO) leafExcelTitleVO).getChildExcelTitleList(),
                        titleRowMap, sheet);
            }
        }
    }

    private static void creatConfigHead(SXSSFWorkbook workbook, List<AbstractExcelTitleVO> excelTitleVOS,
                                        List<AbstractExcelTitleVO> selectedLeafExcelTitleVO, Map<Integer, Row> titleRowMap, Sheet sheet) {
        for (AbstractExcelTitleVO excelTitle : excelTitleVOS) {
            if (excelTitle.getSelected() == null || !excelTitle.getSelected()) {
                continue;
            }
            configHeadSub(workbook, selectedLeafExcelTitleVO, titleRowMap, sheet, excelTitle);
        }
    }

    private static void configHeadSub(SXSSFWorkbook workbook, List<AbstractExcelTitleVO> selectedLeafExcelTitleVO, Map<Integer, Row> titleRowMap, Sheet sheet, AbstractExcelTitleVO abstractExcelTitleVO) {
        if (!abstractExcelTitleVO.getEditable() && ((LeafExcelTitleVO) abstractExcelTitleVO).getDataType()
                .equals(CellType.STRING)) {
            CellStyle headStyle1 = createCellStyle(workbook, CONFIGHEADERCOLORFLAG, true);
            Font font = setFont(workbook, headStyle1);
            headStyle1.setFont(font);
            setCellValueAndStyle(selectedLeafExcelTitleVO, titleRowMap, headStyle1, abstractExcelTitleVO);
        }
        if (abstractExcelTitleVO.getEditable() && ((LeafExcelTitleVO) abstractExcelTitleVO).getDataType().equals(CellType.STRING)) {
            CellStyle headStyle2 = createCellStyle(workbook, CONFIGHEADERCOLORFLAG, true);
            Font ft2 = setFont(workbook, headStyle2);
            headStyle2.setFont(ft2);
            setCellValueAndStyle(selectedLeafExcelTitleVO, titleRowMap, headStyle2, abstractExcelTitleVO);
        }
        if (((LeafExcelTitleVO) abstractExcelTitleVO).getDataType().equals(CellType.NUMERIC)) {
            CellStyle headStyle3 = createCellStyle(workbook, CONFIGHEADERCOLORFLAG, true);
            Font ft3 = setFont(workbook, headStyle3);
            ft3.setColor((short) 2);
            headStyle3.setFont(ft3);
            setCellValueAndStyle(selectedLeafExcelTitleVO, titleRowMap, headStyle3, abstractExcelTitleVO);
        }
        if (abstractExcelTitleVO.getX1() < abstractExcelTitleVO.getX2() || abstractExcelTitleVO.getY1() < abstractExcelTitleVO.getY2()) {
            sheet.addMergedRegion(
                    new CellRangeAddress(abstractExcelTitleVO.getX1(), abstractExcelTitleVO.getX2(), abstractExcelTitleVO.getY1(),
                            abstractExcelTitleVO.getY2()));
        }
        if (abstractExcelTitleVO instanceof BranchExcelTitleVO) {
            if (!CollectionUtil.isNullOrEmpty(((BranchExcelTitleVO) abstractExcelTitleVO).getChildExcelTitleList())) {
                creatConfigHead(workbook, ((BranchExcelTitleVO) abstractExcelTitleVO).getChildExcelTitleList(),
                        selectedLeafExcelTitleVO, titleRowMap, sheet);
            }
        }
    }

    private static void setCellValueAndStyle(List<AbstractExcelTitleVO> selectedLeafExcelTitleVO,
                                             Map<Integer, Row> titleRowMap, CellStyle headStyle, AbstractExcelTitleVO excelTitle) {
        for (int rowIndex = excelTitle.getX1(); rowIndex <= excelTitle.getX2(); rowIndex++) {
            Row row = titleRowMap.get(rowIndex);
            for (int columnIndex = excelTitle.getY1(); columnIndex <= excelTitle.getY2(); columnIndex++) {
                setCells(selectedLeafExcelTitleVO, headStyle, excelTitle, rowIndex, row, columnIndex);
            }
        }
    }

    private static void setExcelCellValueAndStyle(List<AbstractExcelTitleVO> excelTitleVOS,
                                              Map<Integer, Row> titleRowMap, CellStyle headStyle, AbstractExcelTitleVO abstractExcelTitleVO) {
        for (int rowIndex = abstractExcelTitleVO.getX1(); rowIndex <= abstractExcelTitleVO.getX2(); rowIndex++) {
            Row row = titleRowMap.get(rowIndex);
            for (int columnIndex = abstractExcelTitleVO.getY1(); columnIndex <= abstractExcelTitleVO.getY2(); columnIndex++) {
                setCells(excelTitleVOS, headStyle, abstractExcelTitleVO, rowIndex, row, columnIndex);
            }
        }
    }

    private static void setCells(List<AbstractExcelTitleVO> excelTitleVOS, CellStyle headStyle, AbstractExcelTitleVO abstractExcelTitleVO, int rowIndex, Row row, int columnIndex) {
        String cellValue = "";
        if (rowIndex == abstractExcelTitleVO.getX1() && columnIndex == abstractExcelTitleVO.getY1()) {
            cellValue = abstractExcelTitleVO.getValue();
        }
        Cell headCelStyle = row.createCell(columnIndex);
        if (headStyle != null) {
            headCelStyle.setCellStyle(headStyle);
        }
        setCellValueAndStyle(headCelStyle, columnIndex, cellValue, excelTitleVOS.get(columnIndex).getWidth(), CellType.STRING);
    }

    /**
     * 创建表头样式.
     *
     * @param workbook    参数
     * @param titleColor  excel表格部分颜色
     * @param isAlignment 是否左对齐
     * @return [CellStyle]
     * <AUTHOR>
     * @since 2020年12月16日
     */
    private static CellStyle createCellStyle(SXSSFWorkbook workbook, String titleColor, Boolean isAlignment) {
        CellStyle style = workbook.createCellStyle();
        if ((("").equals(titleColor) || BASEPERIODFLAG.equals(titleColor))) {
            style.setBorderBottom(BorderStyle.THIN);
            style.setBorderLeft(BorderStyle.THIN);
            style.setBorderTop(BorderStyle.THIN);
            style.setBorderRight(BorderStyle.THIN);
        }
        // 水平居左
        if (isAlignment) {
            style.setAlignment(HorizontalAlignment.LEFT);
        } else {
            style.setAlignment(HorizontalAlignment.CENTER);
        }
        // 垂直居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置背景色 用于标题
        if (CONFIGHEADERCOLORFLAG.equals(titleColor)) {
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            ((XSSFCellStyle) style).setFillForegroundColor(BLUE_COLOR);
        }
        if (HEADERCOLORFLAG.equals(titleColor)) {
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            ((XSSFCellStyle) style).setFillForegroundColor(WHITE_COLOR);
        }
        if (BASEPERIODFLAG.equals(titleColor)) {
            style.setAlignment(HorizontalAlignment.LEFT);
        }
        if (TITLECOLORFLAG.equals(titleColor)) {
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            ((XSSFCellStyle) style).setFillForegroundColor(DARK_BLUE_COLOR);
        }
        return style;
    }

    /**
     * 创建单元格的格式.
     *
     * @param workbook 参数
     * @return [CellStyle]
     * <AUTHOR>
     * @since 2020年12月16日
     */
    public static CellStyle createBaseCellStyle(Workbook workbook) {
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setWrapText(true);
        // 水平居中
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        // 垂直居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 创建字体
        Font font = workbook.createFont();
        // 加粗字体
        font.setBold(false);
        font.setFontName(SONGSTYLE);
        font.setFontHeightInPoints((short) FONT_SIZE);
        // 加载字体
        cellStyle.setFont(font);
        return cellStyle;
    }

    private static Font setFont(SXSSFWorkbook workbook, CellStyle style) {
        // 创建字体
        Font ft = workbook.createFont();
        // 加粗字体
        ft.setBold(true);
        ft.setFontName(MICROSOFT_YAHEI);
        ft.setFontHeightInPoints((short) FONT_SIZE);
        style.setFont(ft);
        return ft;
    }

    private static Font setFont2(SXSSFWorkbook workbook, CellStyle style) {
        // 创建字体
        Font ft = workbook.createFont();
        ft.setFontName(MICROSOFT_YAHEI);
        ft.setFontHeightInPoints((short) FONT_SIZE);
        style.setFont(ft);
        return ft;
    }

    /**
     * setCellValue设置单元格内容及样式
     *
     * @param headCelStyle   单元格
     * @param cell        列
     * @param value       值
     * @param columnWidth 列宽
     * @param dataType    单元格数据类型
     */
    public static void setCellValueAndStyle(Cell headCelStyle, int cell, String value, int columnWidth,
                                            CellType dataType) {
        if (StringUtils.isEmpty(value)) {
            headCelStyle.setCellType(CellType.BLANK);
        } else {
            if ("NUMERIC".equals(dataType.name())) {
                headCelStyle.setCellValue(Double.parseDouble(value));
            } else {
                headCelStyle.setCellValue(value);
            }
            headCelStyle.setCellType(dataType);
        }
        headCelStyle.getSheet().setColumnWidth(cell, columnWidth);
    }
}
