/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.replace;

import com.huawei.it.fcst.annotations.ExportAttribute;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * DmFocAnnualAmpVO Class
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DmReplaceAllAmpExp {

    @ExportAttribute(sort = 0)
    private String periodYear;

    @ExportAttribute(sort = 1)
    private String groupCnName;

    @ExportAttribute(sort = 2)
    private String weightRate;

    @ExportAttribute(sort = 3)
    private String annualAmp;

    @ExportAttribute(sort = 4)
    private String weightAnnualAmpPercent;


}
