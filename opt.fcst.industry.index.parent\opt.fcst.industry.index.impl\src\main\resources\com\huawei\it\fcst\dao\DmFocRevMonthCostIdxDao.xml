<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IDmFocRecMonthCostIdxDao">
    <resultMap type="com.huawei.it.fcst.industry.index.vo.month.DmFocMonthCostIdxVO" id="resultMap">
        <result property="id" column="id"/>
        <result property="versionId" column="version_id"/>
        <result property="periodYear" column="period_year"/>
        <result property="periodId" column="period_id"/>
        <result property="basePeriodId" column="base_period_id"/>
        <result property="purCode" column="pur_code"/>
        <result property="purCnName" column="pur_cn_name"/>
        <result property="groupCode" column="group_code"/>
        <result property="groupCnName" column="group_cn_name"/>
        <result property="groupLevel" column="group_level"/>
        <result property="costIndex" column="cost_index"/>
        <result property="parentCode" column="parent_code"/>
        <result property="parentCnName" column="parent_cn_name"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="appendFlag" column="append_flag"/>
        <result property="weightRate" column="weight_rate"/>
        <result property="weightPercent" column="weight_percent"/>
        <result property="scenarioFlag" column="scenario_flag"/>
        <result property="costType" column="cost_type"/>
        <result property="lv0ProdRdTeamCnName" column="lv0_prod_rd_team_cn_name"/>
        <result property="lv0ProdRndTeamCode" column="lv0_prod_rnd_team_code"/>
        <result property="lv1ProdRdTeamCnName" column="lv1_prod_rd_team_cn_name"/>
        <result property="lv1ProdRndTeamCode" column="lv1_prod_rnd_team_code"/>
        <result property="lv2ProdRdTeamCnName" column="lv2_prod_rd_team_cn_name"/>
        <result property="lv2ProdRndTeamCode" column="lv2_prod_rnd_team_code"/>
        <result property="lv3ProdRdTeamCnName" column="lv3_prod_rd_team_cn_name"/>
        <result property="lv3ProdRndTeamCode" column="lv3_prod_rnd_team_code"/>
        <result property="l3CegCnName" column="top_l3_ceg_short_cn_name"/>
        <result property="l4CegCnName" column="top_l4_ceg_short_cn_name"/>
        <result property="categoryCnName" column="top_category_cn_name"/>
        <result property="categoryCode" column="top_category_code"/>
    </resultMap>

    <select id="findRecPriceIndexByBasePeriodId" resultType="int">
        SELECT COUNT(1)
        FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_rev_month_cost_idx_t
        WHERE del_flag = 'N'
        <if test='searchParamsVO.basePeriodId != null'>
            AND base_period_id = #{searchParamsVO.basePeriodId}
        </if>
        <if test='searchParamsVO.viewFlag != null'>
            AND view_flag = #{searchParamsVO.viewFlag}
        </if>
        <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
            AND oversea_flag = #{searchParamsVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
            AND lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
            AND caliber_Flag = #{searchParamsVO.caliberFlag}
        </if>
        <if test='searchParamsVO.versionId != null and searchParamsVO.versionId != ""'>
            AND version_id = #{searchParamsVO.versionId}
        </if>
        <if test='searchParamsVO.purCodeList != null and searchParamsVO.purCodeList.size() > 0'>
            <foreach collection='searchParamsVO.purCodeList' item="code" open="AND pur_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupCodeList != null and searchParamsVO.groupCodeList.size() > 0'>
            <foreach collection='searchParamsVO.groupCodeList' item="code" open="AND group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.subGroupCodeList != null and searchParamsVO.subGroupCodeList.size() > 0'>
            <foreach collection='searchParamsVO.subGroupCodeList' item="code" open="AND group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.parentCodeList != null and searchParamsVO.parentCodeList.size() > 0'>
            <foreach collection='searchParamsVO.parentCodeList' item="code" open="AND parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
    </select>

    <select id="findRevPriceIndexVOList" resultMap="resultMap">
        SELECT
        DISTINCT
        t.group_level,
        t.group_code,
        t.period_year,
        t.period_id,
        CASE t.group_level
        WHEN 'CATEGORY' THEN t.group_code || '' || t.group_cn_name
        WHEN 'ITEM' THEN t.group_code
        ELSE t.group_cn_name
        END AS group_cn_name,
        ROUND(t.cost_index, 2) AS cost_index,
        t.version_id,
        'P' AS cost_type,
        t.last_update_date
        FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_rev_month_cost_idx_t t
        WHERE t.del_flag = 'N'
        <if test='searchParamsVO.viewFlag != null'>
            AND t.view_flag = #{searchParamsVO.viewFlag}
        </if>
        <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
            AND t.caliber_Flag = #{searchParamsVO.caliberFlag}
        </if>
        <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
            and t.oversea_flag = #{searchParamsVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
            and t.lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.purCodeList != null and searchParamsVO.purCodeList.size() > 0'>
            <foreach collection='searchParamsVO.purCodeList' item="code" open="AND t.pur_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupCodeList != null and searchParamsVO.groupCodeList != ""'>
            <foreach collection='searchParamsVO.groupCodeList' item="code" open="AND t.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.basePeriodId != null'>
            AND t.base_period_id = #{searchParamsVO.basePeriodId}
        </if>
        <if test='searchParamsVO.versionId != null'>
            AND t.version_id = #{searchParamsVO.versionId}
        </if>
        <if test='searchParamsVO.groupLevel != null and searchParamsVO.groupLevel != ""'>
            AND t.group_level = #{searchParamsVO.groupLevel}
        </if>
        <if test='searchParamsVO.periodStartTime != null and searchParamsVO.periodStartTime != ""'>
            AND t.period_id <![CDATA[ >= ]]> #{searchParamsVO.periodStartTime}
        </if>
        <if test='searchParamsVO.periodEndTime != null and searchParamsVO.periodEndTime != ""'>
            AND t.period_id <![CDATA[ <= ]]> #{searchParamsVO.periodEndTime}
        </if>
        <if test='searchParamsVO.groupLevel =="LV1" and searchParamsVO.lv1DimensionSet != null and searchParamsVO.lv1DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv1DimensionSet' item="code" open="AND t.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupLevel =="LV2" and searchParamsVO.lv2DimensionSet != null and searchParamsVO.lv2DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv2DimensionSet' item="code" open="AND t.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        ORDER BY t.period_id
    </select>

    <select id="findRevPriceIndexChartByMultiDim" resultMap="resultMap">
        <choose>
            <when test='searchParamsVO.groupLevel == "ITEM"'>
                SELECT DISTINCT
                T1.parent_code,
                T3.group_cn_name AS parent_cn_name,
                T1.period_id,
                T1.group_level,
                T1.group_code,
                CASE T1.group_level
                WHEN 'CATEGORY' THEN T1.group_code || ' ' || T1.group_cn_name
                WHEN 'ITEM' THEN T1.group_code
                ELSE T1.group_cn_name
                END AS group_cn_name,
                'P' AS cost_type,
                ROUND(T1.cost_index, 2) AS cost_index,
                T1.append_flag
                FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_rev_month_cost_idx_t T1
                LEFT JOIN fin_dm_opt_foi.${searchParamsVO.tablePreFix}_rev_month_cost_idx_t T3 ON T3.group_code = T1.parent_code
                AND T3.period_id = T1.period_id
                AND T3.base_period_id =T1.base_period_id
                <if test ='searchParamsVO.groupLevel == "LV1" or searchParamsVO.groupLevel == "LV2" or searchParamsVO.groupLevel == "LV3" or searchParamsVO.groupLevel == "LV4"'>
                    AND T3.pur_code =  T1.pur_code
                </if>
                AND T3.del_flag = 'N' AND T3.view_flag = #{searchParamsVO.viewFlag}
                AND T3.caliber_Flag = #{searchParamsVO.caliberFlag}
                AND T3.version_id = #{searchParamsVO.versionId}
                AND T3.group_level = #{searchParamsVO.parentLevel}
                AND T3.oversea_flag = #{searchParamsVO.overseaFlag}
                AND T3.lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode}
                WHERE T1.del_flag = 'N'
            </when>
            <otherwise>
                SELECT DISTINCT T1.parent_code,
                T3.group_cn_name AS parent_cn_name,
                T1.period_id,T1.group_level, T1.group_code,
                CASE T1.group_level
                WHEN 'CATEGORY' THEN T1.group_code || ' ' || T1.group_cn_name
                WHEN 'ITEM' THEN T1.group_code
                ELSE T1.group_cn_name
                END AS group_cn_name,
                ROUND(T1.cost_index, 2) AS cost_index,
                T1.append_flag,
                T2.weight_rate,
                'P' AS cost_type,
                ROUND(T2.weight_rate * 100, 2) || '%' AS weight_percent
                FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_rev_month_cost_idx_t T1
                LEFT JOIN fin_dm_opt_foi.${searchParamsVO.tablePreFix}_rev_month_cost_idx_t T3 ON T3.group_code = T1.parent_code
                AND T3.period_id = T1.period_id
                AND T3.base_period_id =T1.base_period_id
                <if test ='searchParamsVO.groupLevel == "LV1" or searchParamsVO.groupLevel == "LV2" or searchParamsVO.groupLevel == "LV3" or searchParamsVO.groupLevel == "LV4"'>
                    AND T3.pur_code =  T1.pur_code
                </if>
                AND T3.del_flag = 'N' AND T3.view_flag = #{searchParamsVO.viewFlag}
                AND T3.caliber_Flag = #{searchParamsVO.caliberFlag}
                AND T3.version_id = #{searchParamsVO.versionId}
                AND T3.group_level = #{searchParamsVO.parentLevel}
                AND T3.oversea_flag = #{searchParamsVO.overseaFlag}
                AND T3.lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode}
                LEFT JOIN fin_dm_opt_foi.${searchParamsVO.tablePreFix}_rev_month_weight_t T2
                ON T1.group_code = T2.group_code
                AND T1.pur_code = T2.pur_code
                AND T1.parent_code = T2.parent_code
                AND T2.del_flag = 'N' AND T2.period_year_type = 'S'
                WHERE T1.del_flag = 'N'
                AND T2.version_id = #{searchParamsVO.versionId}
                AND T2.view_flag = #{searchParamsVO.viewFlag}
                <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
                    AND T2.oversea_flag = #{searchParamsVO.overseaFlag}
                </if>
                <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
                    AND T2.lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode}
                </if>
                <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
                    AND T2.caliber_Flag = #{searchParamsVO.caliberFlag}
                </if>
                <if test='searchParamsVO.groupLevel != null and searchParamsVO.groupLevel !=""'>
                    AND T2.group_level = #{searchParamsVO.groupLevel}
                </if>
                <if test='searchParamsVO.purCodeList != null and searchParamsVO.purCodeList.size() > 0'>
                    <foreach collection='searchParamsVO.purCodeList' item="code" open="AND T2.pur_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='searchParamsVO.parentCodeList != null and searchParamsVO.parentCodeList.size() > 0'>
                    <foreach collection='searchParamsVO.parentCodeList' item="code" open="AND T2.parent_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>

            </otherwise>
        </choose>
        AND T1.version_id = #{searchParamsVO.versionId}
        AND T1.view_flag = #{searchParamsVO.viewFlag}
        <if test='searchParamsVO.basePeriodId != null'>
            AND T1.base_period_id = #{searchParamsVO.basePeriodId}
        </if>
        <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
            AND T1.caliber_Flag = #{searchParamsVO.caliberFlag}
        </if>
        <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
            and T1.oversea_flag = #{searchParamsVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
            and T1.lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.groupLevel != null and searchParamsVO.groupLevel != ""'>
            AND T1.group_level = #{searchParamsVO.groupLevel}
        </if>
        <if test='searchParamsVO.purCodeList != null and searchParamsVO.purCodeList.size() > 0'>
            <foreach collection='searchParamsVO.purCodeList' item="code" open="AND T1.pur_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.parentCodeList != null and searchParamsVO.parentCodeList.size() > 0'>
            <foreach collection='searchParamsVO.parentCodeList' item="code" open="AND T1.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.subGroupCodeList != null and searchParamsVO.subGroupCodeList.size() > 0'>
            <foreach collection='searchParamsVO.subGroupCodeList' item="code" open="AND T1.group_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.periodStartTime != null and searchParamsVO.periodStartTime != ""'>
            AND T1.period_id <![CDATA[ >= ]]> #{searchParamsVO.periodStartTime}
        </if>
        <if test='searchParamsVO.periodEndTime != null and searchParamsVO.periodEndTime != ""'>
            AND T1.period_id <![CDATA[ <= ]]> #{searchParamsVO.periodEndTime}
        </if>
        <if test='searchParamsVO.groupLevel =="LV1" and searchParamsVO.lv1DimensionSet != null and searchParamsVO.lv1DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv1DimensionSet' item="code" open="AND T1.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupLevel =="LV2" and searchParamsVO.lv2DimensionSet != null and searchParamsVO.lv2DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv2DimensionSet' item="code" open="AND T1.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        ORDER BY T1.group_code, T1.period_id
    </select>

    <select id="findRevAmpPurchasePriceIndexChart" resultMap="resultMap">
        SELECT DISTINCT T1.parent_code,
        T1.parent_cn_name,
        T1.period_id,T1.group_level, T1.group_code,
        CASE T1.group_level
        WHEN 'CATEGORY' THEN T1.group_code || ' ' || T1.group_cn_name
        WHEN 'ITEM' THEN T1.group_code
        ELSE T1.group_cn_name
        END AS group_cn_name,
        ROUND(T1.cost_index, 2) AS cost_index,
        T1.append_flag,
        T2.weight_rate,
        'P' AS cost_type,
        ROUND(T2.weight_rate * 100, 2) || '%' AS weight_percent
        FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_rev_month_cost_idx_t T1
        LEFT JOIN fin_dm_opt_foi.${searchParamsVO.tablePreFix}_rev_month_weight_t T2
        ON T1.group_code = T2.group_code
        AND T1.pur_code = T2.pur_code
        AND T1.parent_code = T2.parent_code
        AND T2.del_flag = 'N' AND T2.period_year_type = 'S'
        WHERE T1.del_flag = 'N'
        AND T2.version_id = #{searchParamsVO.versionId}
        AND T2.view_flag = #{searchParamsVO.viewFlag}
        <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
            AND T2.oversea_flag = #{searchParamsVO.overseaFlag}
        </if>
        <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
            AND T2.lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode}
        </if>
        <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
            AND T2.caliber_Flag = #{searchParamsVO.caliberFlag}
        </if>
        <if test='searchParamsVO.groupLevel != null and searchParamsVO.groupLevel !=""'>
            AND T2.group_level = #{searchParamsVO.groupLevel}
        </if>
        <if test='searchParamsVO.purCodeList != null and searchParamsVO.purCodeList.size() > 0'>
            <foreach collection='searchParamsVO.purCodeList' item="code" open="AND T2.pur_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.parentCodeList != null and searchParamsVO.parentCodeList.size() > 0'>
            <foreach collection='searchParamsVO.parentCodeList' item="code" open="AND T2.parent_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        AND T1.version_id = #{searchParamsVO.versionId}
        AND T1.view_flag = #{searchParamsVO.viewFlag}
        <if test='searchParamsVO.basePeriodId != null'>
            AND T1.base_period_id = #{searchParamsVO.basePeriodId}
        </if>
        <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
            AND T1.caliber_Flag = #{searchParamsVO.caliberFlag}
        </if>
        <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
            and T1.oversea_flag = #{searchParamsVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
            and T1.lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.groupLevel != null and searchParamsVO.groupLevel != ""'>
            AND T1.group_level = #{searchParamsVO.groupLevel}
        </if>
        <if test='searchParamsVO.purCodeList != null and searchParamsVO.purCodeList.size() > 0'>
            <foreach collection='searchParamsVO.purCodeList' item="code" open="AND T1.pur_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.parentCodeList != null and searchParamsVO.parentCodeList.size() > 0'>
            <foreach collection='searchParamsVO.parentCodeList' item="code" open="AND T1.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.subGroupCodeList != null and searchParamsVO.subGroupCodeList.size() > 0'>
            <foreach collection='searchParamsVO.subGroupCodeList' item="code" open="AND T1.group_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.periodStartTime != null and searchParamsVO.periodStartTime != ""'>
            AND T1.period_id <![CDATA[ >= ]]> #{searchParamsVO.periodStartTime}
        </if>
        <if test='searchParamsVO.periodEndTime != null and searchParamsVO.periodEndTime != ""'>
            AND T1.period_id <![CDATA[ <= ]]> #{searchParamsVO.periodEndTime}
        </if>
        <if test='searchParamsVO.begin != null and searchParamsVO.begin != "" and searchParamsVO.end != null and searchParamsVO.end != ""'>
            AND (T1.period_id <![CDATA[ = ]]> #{searchParamsVO.begin} or T1.period_id <![CDATA[ = ]]> #{searchParamsVO.end})
        </if>
        <if test='searchParamsVO.groupLevel =="LV1" and searchParamsVO.lv1DimensionSet != null and searchParamsVO.lv1DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv1DimensionSet' item="code" open="AND T1.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupLevel =="LV2" and searchParamsVO.lv2DimensionSet != null and searchParamsVO.lv2DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv2DimensionSet' item="code" open="AND T1.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        ORDER BY T1.group_code, T1.period_id
    </select>

    <select id="findRevPriceIndexExpData" resultMap="resultMap">
        SELECT
        t.pur_code,
        t.pur_cn_name,
        t.parent_code,
        t1.group_cn_name AS parent_cn_name,
        t.period_id,
        t.group_code,
        t.group_cn_name,
        ROUND(t.cost_index, 2) AS cost_index,
        'P' AS cost_type
        FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_rev_month_cost_idx_t t
        LEFT JOIN fin_dm_opt_foi.${searchParamsVO.tablePreFix}_rev_month_cost_idx_t t1 ON t1.group_code = t.parent_code
        AND t1.period_id = t.period_id
        AND t1.base_period_id = t.base_period_id
        <if test ='searchParamsVO.groupLevel == "LV1" or searchParamsVO.groupLevel == "LV2" or searchParamsVO.groupLevel == "LV3" or searchParamsVO.groupLevel == "LV4"'>
            AND t1.pur_code = t.pur_code
        </if>
        AND t1.del_flag = 'N' AND t1.view_flag = #{searchParamsVO.viewFlag}
        AND t1.caliber_Flag = #{searchParamsVO.caliberFlag}
        AND t1.version_id = #{searchParamsVO.versionId,jdbcType=NUMERIC}
        AND t1.group_level = #{searchParamsVO.parentLevel}
        AND t1.oversea_flag = #{searchParamsVO.overseaFlag}
        AND t1.lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode}
        WHERE t.del_flag = 'N'
        AND t.view_flag = #{searchParamsVO.viewFlag}
        <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
            and t.oversea_flag = #{searchParamsVO.overseaFlag}
        </if>
        <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
            and t.lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode}
        </if>
        <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
            AND t.caliber_Flag = #{searchParamsVO.caliberFlag}
        </if>
        <if test='searchParamsVO.basePeriodId != null and searchParamsVO.basePeriodId != ""'>
            AND t.base_period_id = #{searchParamsVO.basePeriodId}
        </if>
        <if test='searchParamsVO.versionId != null'>
            AND t.version_id = #{searchParamsVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='searchParamsVO.purCodeList != null and searchParamsVO.purCodeList.size() > 0'>
            <foreach collection='searchParamsVO.purCodeList' item="code" open="AND t.pur_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.parentCodeList != null and searchParamsVO.parentCodeList.size() > 0'>
            <foreach collection='searchParamsVO.parentCodeList' item="code" open="AND t.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupLevel != null and searchParamsVO.groupLevel != ""'>
            AND t.group_level = #{searchParamsVO.groupLevel}
        </if>
        <if test='searchParamsVO.periodStartTime != null and searchParamsVO.periodStartTime != ""'>
            AND t.period_id <![CDATA[ >= ]]> #{searchParamsVO.periodStartTime}
        </if>
        <if test='searchParamsVO.periodEndTime != null and searchParamsVO.periodEndTime != ""'>
            AND t.period_id <![CDATA[ <= ]]> #{searchParamsVO.periodEndTime}
        </if>
        <if test='searchParamsVO.groupLevel =="LV1" and searchParamsVO.lv1DimensionSet != null and searchParamsVO.lv1DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv1DimensionSet' item="code" open="AND t.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupLevel =="LV2" and searchParamsVO.lv2DimensionSet != null and searchParamsVO.lv2DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv2DimensionSet' item="code" open="AND t.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        ORDER BY t.period_id
    </select>

</mapper>
