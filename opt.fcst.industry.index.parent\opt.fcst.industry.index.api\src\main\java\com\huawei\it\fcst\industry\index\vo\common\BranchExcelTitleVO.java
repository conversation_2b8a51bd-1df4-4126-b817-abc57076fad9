/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.common;

import java.util.ArrayList;
import java.util.List;

/**
 * BranchExcelTitleVO Class
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
public class BranchExcelTitleVO extends AbstractExcelTitleVO {
    /**
     * 对于复杂列头，是树型层次关系，这个字段保存其下层子列头标题
     */
    private List<AbstractExcelTitleVO> childExcelTitleList = new ArrayList<>();

    /**
     * 添加子列头
     *
     * @param excelTitleVO AbstractExcelTitleVO
     */
    public void addChild(AbstractExcelTitleVO excelTitleVO) {
        childExcelTitleList.add(excelTitleVO);
    }

    public BranchExcelTitleVO(String value, Integer width) {
        this.setValue(value);
        this.setWidth(width);
    }

    public List<AbstractExcelTitleVO> getChildExcelTitleList() {
        return childExcelTitleList;
    }

    public void setChildExcelTitleList(List<AbstractExcelTitleVO> childExcelTitleList) {
        this.childExcelTitleList = childExcelTitleList;
    }

}
