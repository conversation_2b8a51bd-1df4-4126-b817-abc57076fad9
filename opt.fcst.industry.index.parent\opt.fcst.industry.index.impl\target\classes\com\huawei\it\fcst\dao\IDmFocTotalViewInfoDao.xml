<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IDmFocTotalViewInfoDao">
    <resultMap type="com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO" id="resultMap">
        <result property="id" column="id"/>
        <result property="lv0ProdRndTeamCode" column="lv0_prod_rnd_team_code"/>
        <result property="lv0ProdRdTeamCnName" column="lv0_prod_rd_team_cn_name"/>
        <result property="lv1ProdRndTeamCode" column="lv1_prod_rnd_team_code"/>
        <result property="lv1ProdRdTeamCnName" column="lv1_prod_rd_team_cn_name"/>
        <result property="lv2ProdRndTeamCode" column="lv2_prod_rnd_team_code"/>
        <result property="lv2ProdRdTeamCnName" column="lv2_prod_rd_team_cn_name"/>
        <result property="lv3ProdRndTeamCode" column="lv3_prod_rnd_team_code"/>
        <result property="lv3ProdRdTeamCnName" column="lv3_prod_rd_team_cn_name"/>
        <result property="lv0ProdListCode" column="lv0_prod_list_code"/>
        <result property="lv0ProdListCnName" column="lv0_prod_list_cn_name"/>
        <result property="prodRndTeamCode" column="prod_rnd_team_code"/>
        <result property="itemCode" column="item_code"/>
        <result property="itemCnName" column="item_cn_name"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDdate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="groupCode" column="groupCode"/>
        <result property="groupLevel" column="groupLevel"/>
        <result property="groupCnName" column="groupCnName"/>
        <result property="weightRate" column="weight_rate"/>
        <result property="granularityType" column="granularityType"/>
    </resultMap>

    <select id="totalReverseFindLv1ProdCode" resultMap="resultMap">
        SELECT DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name
        <if test='granularityType=="U"'>
            FROM  fin_dm_opt_foi.${tablePreFix}_total_view_info_d
        </if>
        <if test='granularityType=="P"'>
            FROM  fin_dm_opt_foi.${tablePreFix}_total_pft_view_info_d
        </if>
        <if test='granularityType=="D"'>
            FROM  fin_dm_opt_foi.${tablePreFix}_total_dms_view_info_d
        </if>
        WHERE view_flag = #{viewFlag,jdbcType=VARCHAR} and del_flag ='N' and caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        and oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        and page_flag =  #{pageFlag,jdbcType=VARCHAR}
        <if test='lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
    </select>
    <sql id="weight_rate">
        <if test='viewFlag != null and viewFlag == "1"'>
            <choose>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "2"'>
            <choose>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                    groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "3"'>
            <choose>
                <when test='teamLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName,
                    'LV3' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "7"'>
            <choose>
                <when test='teamLevel == "LV3"'>
                    DISTINCT lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code AS groupCode, lv4_prod_rd_team_cn_name AS groupCnName,
                    'LV4' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName,
                    'LV3' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
    </sql>
    <sql id="absolute_weight">
        <if test='viewFlag != null and viewFlag == "1"'>
            <choose>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "2"'>
            <choose>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                    groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "3"'>
            <choose>
                <when test='teamLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName,
                    'LV3' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "7"'>
            <choose>
                <when test='teamLevel == "LV3"'>
                    DISTINCT lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code AS groupCode, lv4_prod_rd_team_cn_name AS groupCnName,
                    'LV4' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName,
                    'LV3' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
    </sql>
    <select id="totalViewInfoList" resultMap="resultMap">
        SELECT
        <if test='isMultipleSelect == false'>
            <include refid="weight_rate"></include>
        </if>
        <if test='isMultipleSelect == true'>
            <include refid="absolute_weight"></include>
        </if>
        <choose>
            <when test='teamLevel == "LV0" and viewFlag != "0"'>
                from fin_dm_opt_foi.${tablePreFix}_total_view_info_d amp
            </when>
            <when test='teamLevel == "LV3"'>
                from fin_dm_opt_foi.${tablePreFix}_total_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_total_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.lv4_prod_rnd_team_code = weight.group_code
                and amp.lv3_prod_rnd_team_code = weight.parent_code and weight.cost_type = 'T'
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV2" and viewFlag !="2"'>
                from fin_dm_opt_foi.${tablePreFix}_total_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_total_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.lv3_prod_rnd_team_code = weight.group_code
                and amp.lv2_prod_rnd_team_code = weight.parent_code and weight.cost_type = 'T'
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV1" and viewFlag !="1"'>
                from fin_dm_opt_foi.${tablePreFix}_total_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_total_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.lv2_prod_rnd_team_code = weight.group_code
                and amp.lv1_prod_rnd_team_code = weight.parent_code and weight.cost_type = 'T'
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
        </choose>
        WHERE amp.view_flag = #{viewFlag,jdbcType=VARCHAR} and amp.del_flag ='N' and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        and amp.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and amp.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        and amp.page_flag =  #{pageFlag,jdbcType=VARCHAR}
        <choose>
            <when test='teamLevel == "LV1" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND amp.lv1_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='teamLevel == "LV2" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND amp.lv2_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='teamLevel == "LV3" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND amp.lv3_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='teamLevel == "LV4" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND amp.lv4_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='nextGroupLevel!=null and nextGroupLevel!=""'>
            AND amp.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
        </if>
        <if test='teamLevel == "LV0" and viewFlag !="0" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND amp.lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamLevel == "LV1" and viewFlag !="1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND amp.lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='isMultipleSelect == false'>
            <include refid="orderby_weight"></include>
        </if>
        <if test='isMultipleSelect == true'>
            <include refid="orderby_absweight"></include>
        </if>
    </select>
    <sql id = "orderby_weight">
        <if test='teamLevel != "LV0"'>
            order by weight_rate desc
        </if>
    </sql>
    <sql id = "orderby_absweight">
        <if test='teamLevel != "LV0"'>
            order by absolute_weight desc
        </if>
    </sql>

    <select id="totalViewFlagInfoList" resultMap="resultMap">
        SELECT *
        from (
                 SELECT DISTINCT view_flag, lv0_prod_rnd_team_code, lv0_prod_rd_team_cn_name, 'U' as granularityType
                 FROM
                     fin_dm_opt_foi.${tablePreFix}_total_view_info_d
                 where del_flag = 'N' and caliber_flag = #{caliberFlag}
             )
        ORDER BY view_flag
    </select>

    <select id="getTotalLv0ProdList" resultMap="resultMap">
        select  distinct  CASE WHEN LV0_PROD_LIST_CODE = 'GR' THEN 1
            WHEN LV0_PROD_LIST_CODE = 'PDCG901160' THEN 2
            WHEN LV0_PROD_LIST_CODE = 'PDCG901159' THEN 3
            ELSE 4 END,
        lv0_prod_list_code,lv0_prod_list_cn_name
        <if test='granularityType == "U"'>
            FROM fin_dm_opt_foi.${tablePreFix}_total_view_info_d
        </if>
        <if test='granularityType == "P"'>
            FROM fin_dm_opt_foi.${tablePreFix}_total_pft_view_info_d
        </if>
        <if test='granularityType == "D"'>
            FROM fin_dm_opt_foi.${tablePreFix}_total_dms_view_info_d
        </if>
        where del_flag = 'N' and view_flag = #{viewFlag} and caliber_flag = #{caliberFlag}
        <if test='lv0ProdListCode!=null and lv0ProdListCode!=""'>
            and lv0_prod_list_code = #{lv0ProdListCode}
        </if>
        ORDER BY CASE WHEN LV0_PROD_LIST_CODE = 'GR' THEN 1
        WHEN LV0_PROD_LIST_CODE = 'PDCG901160' THEN 2
        WHEN LV0_PROD_LIST_CODE = 'PDCG901159' THEN 3
        ELSE 4 END ASC
    </select>
</mapper>