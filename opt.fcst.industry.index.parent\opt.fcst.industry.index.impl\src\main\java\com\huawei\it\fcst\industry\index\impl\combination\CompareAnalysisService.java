/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.combination;

import com.huawei.it.fcst.industry.index.dao.IDmFocMadeViewInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocViewInfoDao;
import com.huawei.it.fcst.industry.index.enums.GroupLevelAllEnum;
import com.huawei.it.fcst.industry.index.enums.IndustryIndexEnum;
import com.huawei.it.fcst.industry.index.service.combination.ICompareAnalysisService;
import com.huawei.it.fcst.industry.index.utils.FcstIndexMadeUtil;
import com.huawei.it.fcst.industry.index.utils.FcstIndexUtil;
import com.huawei.it.fcst.industry.index.utils.UserInfoUtils;
import com.huawei.it.fcst.industry.index.vo.common.CommonViewVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.security.RoleVO;
import com.huawei.it.jalor5.security.UserVO;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Named;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * CompareAnalysisService
 *
 * <AUTHOR>
 * @since 2023/11/21
 */
@Named("compareAnalysisService")
@JalorResource(code = "compareAnalysisService", desc = "对比分析")
public class CompareAnalysisService implements ICompareAnalysisService {

    @Autowired
    private CustomCombService customCombService;

    @Autowired
    private IDmFocViewInfoDao dmFocViewInfoDao;

    @Autowired
    private IDmFocMadeViewInfoDao dmFocMadeViewInfoDao;

    @Override
    @JalorOperation(code = "allDimensionList", desc = "年度对比分析树")
    public ResultDataVO getDimensionList(CommonViewVO commonViewVO) {
        List<DmFocViewInfoVO> allDimensionList = new ArrayList<>();
        // 获取组合维度 (只获取组合项，不看组合的子项,去掉失效的组合)
        List<DmFocViewInfoVO> customCombVOList = new ArrayList<>();
        if (StringUtils.isEmpty(commonViewVO.getLv0ProdRndTeamCode())||StringUtils.isNotEmpty(commonViewVO.getFilterGroupLevel())||StringUtils.isNotEmpty(commonViewVO.getKeyWord())) {
            customCombVOList = getCombDimensionTree(commonViewVO);
        }
        // 获取正常维度树
        ResultDataVO getDimensionTree = customCombService.getProdRndTeamTree(commonViewVO);
        List<DmFocViewInfoVO> data = (List<DmFocViewInfoVO>) getDimensionTree.getData();
        allDimensionList.addAll(data);
        allDimensionList.addAll(customCombVOList);
        return ResultDataVO.success(allDimensionList);
    }

    @NotNull
    private List<DmFocViewInfoVO> getCombDimensionTree(CommonViewVO commonViewVO) {
        // 获取组合维度 (只获取组合项，不看组合的子项,去掉失效的组合)
        CommonViewVO combViewVO = new CommonViewVO();
        BeanUtils.copyProperties(commonViewVO, combViewVO);
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        combViewVO.setRoleId(String.valueOf(currentRole.getRoleId()));
        combViewVO.setUserId(String.valueOf(UserInfoUtils.getUserId()));
        combViewVO.setGroupLevel("LV0");
        // 另存页面查询所有组合，年度月度页面只查询有效的组合
        if (!"Y".equals(commonViewVO.getIsSavePage())) {
            combViewVO.setEnableFlag("Y");
        }
        List<DmFocViewInfoVO> dmCustomCombVOList = new ArrayList<>();
        boolean viewFlagCondition = "4".equals(commonViewVO.getViewFlag()) || "5".equals(commonViewVO.getViewFlag()) || "6".equals(commonViewVO.getViewFlag());
        if (("U".equals(commonViewVO.getGranularityType()) || "M".equals(commonViewVO.getGranularityType())) && viewFlagCondition) {
            viewFlagCondition = true;
        } else {
            viewFlagCondition = false;
        }
        // 递归循环获取所有层级汇总组合
        if (!IndustryIndexEnum.COST_TYPE.T.getValue().equals(commonViewVO.getCostType()) && !viewFlagCondition) {
            recursionCustomCombList(combViewVO, dmCustomCombVOList);
        }
        // 汇总组合的返回值处理
        dmCustomCombVOList = dmCustomCombVOList.stream().map(customCombVO -> {
            DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
            dmFocViewInfoVO.setGroupCode(customCombVO.getCustomId()+"");
            dmFocViewInfoVO.setConnectCode(customCombVO.getCustomId()+"");
            dmFocViewInfoVO.setCustomId(customCombVO.getCustomId());
            dmFocViewInfoVO.setGroupCode(customCombVO.getGroupCode());
            dmFocViewInfoVO.setGroupCnName(customCombVO.getGroupCnName());
            dmFocViewInfoVO.setEnableFlag(customCombVO.getEnableFlag());
            dmFocViewInfoVO.setGroupLevel(customCombVO.getGroupLevel());
            dmFocViewInfoVO.setIsCombination(true);
            return dmFocViewInfoVO;
        }).collect(Collectors.toList());
        return dmCustomCombVOList;
    }

    private void recursionCustomCombList(CommonViewVO combViewVO, List<DmFocViewInfoVO> dmCustomCombVOList) {
        String nextGroupLevel;
        // 根据groupLevel计算子项level
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(combViewVO.getCostType())) {
            nextGroupLevel = FcstIndexMadeUtil.getNextGroupLevelByView(combViewVO.getViewFlag(), combViewVO.getGroupLevel(), combViewVO.getGranularityType(),combViewVO.getIndustryOrg());
        } else {
            nextGroupLevel = FcstIndexUtil.getNextGroupLevelByView(combViewVO.getViewFlag(), combViewVO.getGroupLevel(), combViewVO.getGranularityType(),combViewVO.getIndustryOrg());
        }
        combViewVO.setNextGroupLevel(nextGroupLevel);
        // 各层级的汇总组合
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(combViewVO.getCostType())) {
            dmCustomCombVOList.addAll(dmFocViewInfoDao.getCombinationParent(combViewVO));
        }
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(combViewVO.getCostType())) {
            dmCustomCombVOList.addAll(dmFocMadeViewInfoDao.getMadeCombinationParent(combViewVO));
        }
        combViewVO.setGroupLevel(nextGroupLevel);
        if (GroupLevelAllEnum.ITEM.getValue().equals(nextGroupLevel)|| StringUtils.isNotEmpty(combViewVO.getFilterGroupLevel())||StringUtils.isNotEmpty(combViewVO.getKeyWord())) {
            return;
        }
        recursionCustomCombList(combViewVO, dmCustomCombVOList);
    }

}
