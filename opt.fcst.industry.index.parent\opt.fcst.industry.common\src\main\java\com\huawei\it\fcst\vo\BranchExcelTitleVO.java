/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.vo;

import java.util.ArrayList;
import java.util.List;

/**
 * BranchExcelTitleVO Class
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
public class BranchExcelTitleVO extends AbstractExcelTitleVO {
    /**
     * 对于复杂列头，是树型层次关系，这个字段保存其下层子列头标题
     */
    private List<AbstractExcelTitleVO> childExcelTitleList = new ArrayList<>();

    public BranchExcelTitleVO(String value, Integer width) {
        this.setValue(value);
        this.setWidth(width);
    }

    public void setChildExcelTitleList(List<AbstractExcelTitleVO> childExcelTitleList) {
        this.childExcelTitleList = childExcelTitleList;
    }

    public List<AbstractExcelTitleVO> getChildExcelTitleList() {
        return childExcelTitleList;
    }
}
