/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.vo.drop;

import com.huawei.it.fcst.industry.price.vo.common.CommonPriceBaseVO;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2024/11/8
 */
@Setter
@Getter
@EqualsAndHashCode(callSuper=true)
@NoArgsConstructor
public class BasePriceCusDimVO extends CommonPriceBaseVO {

    /**
     * 用户id
     */
    private String userId;

    /**
     * 角色id
     */
    private String roleId;

    /**
     * 虚化组合id
     */
    private Long customId;

    /**
     * 虚化组合状态
     */
    private String statusFlag;

}
