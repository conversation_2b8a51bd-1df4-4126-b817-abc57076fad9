/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.month.DmFocMonthCostIdxVO;
import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * The DAO to access DmFocMonthCostIdxT entity
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2023-03-10 11:14:15
 */
public interface IDmFocMonthCostIdxDao {
    /**
     *  根据基期、版本ID、分层级code编码刷新价格指数(F_DM_FOC_POINT_INDEX)
     *
     * @param searchParamsVO 查询参数VO
     * @return str Success flag
     */
    String insertPriceIndexWithFun(@Param("searchParamsVO") MonthAnalysisVO searchParamsVO);

    /**
     *  根据基期、版本ID、分层级code编码刷新总成本下的价格指数(F_DM_FOC_TOTAL_POINT_INDEX)
     *
     * @param searchParamsVO 查询参数VO
     * @return str Success flag
     */
    String insertTotalPriceIdxWithFun(@Param("searchParamsVO") MonthAnalysisVO searchParamsVO);

    /**
     * 校验所选基期是否有数据
     *
     * @param monthAnalysisVO 参数Dto
     * @return int
     */
    int findPriceIndexByBasePeriodId(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);

    int findCombPriceIndexByBasePeriodId(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);

    /**
     * Find all DmFoiPriceIndexVO records.
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<DmFocMonthCostIdxVO> findCompareDmFocPriceIndexVOList(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);

    /**
     * Find all DmFoiPriceIndexVO records.
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<DmFocMonthCostIdxVO> findDmFocPriceIndexVOList(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);

    /**
     * Find all DmFoiPriceIndexVO records.
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<DmFocMonthCostIdxVO> findDmFocCombPriceIndexVOList(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);

    /**
     * [查询采购价格指数（多维度）图数据]
     *     多维度：多专家团、多品类、多Item
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<DmFocMonthCostIdxVO> findPriceIndexChartByMultiDim(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);


    List<DmFocMonthCostIdxVO> findPriceIndexNormalChartByMultiDim(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);

    /**
     * [查询采购价格指数（多维度）图数据]
     *     多维度：多专家团、多品类、多Item
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<DmFocMonthCostIdxVO> findPriceIndexCombChartByMultiDim(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);


    /**
     * [查询采购价格指数（多维度）图数据]
     *     多ItemCode集合
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    PagedResult<DmFocMonthCostIdxVO> findAllItemCodeForComb(@Param("monthAnalysisVO") MonthAnalysisVO monthAnalysisVO, @Param("pageVO") PageVO pageVO);

    /**
     * [查询采购价格指数（多维度）图数据]
     *     多ItemCode集合
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    PagedResult<DmFocMonthCostIdxVO> findAllItemCodeForAll(@Param("monthAnalysisVO") MonthAnalysisVO monthAnalysisVO, @Param("pageVO") PageVO pageVO);

    /**
     * [查询采购价格指数（多维度）图数据]导出
     *     多维度：多专家团、多品类、多Item
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<DmFocMonthCostIdxVO> findPriceIndexExpData(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);

    Long findActualMonthNum(@Param("tablePreFix") String tablePreFix);

    /**
     * 本期实际数截止月
     *
     * @return 本期实际数截止月
     * @param tablePreFix
     */
    String findActualMonth(@Param("tablePreFix") String tablePreFix);

    List<DmFocMonthCostIdxVO> findAmpPurchasePriceIndexChart(@Param("searchParamsVO")MonthAnalysisVO monthAnalysisVO);


}
