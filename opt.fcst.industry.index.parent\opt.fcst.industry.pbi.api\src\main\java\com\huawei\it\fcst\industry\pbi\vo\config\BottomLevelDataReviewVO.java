/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.config;

import com.huawei.it.fcst.industry.pbi.vo.common.CommonBaseVO;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024/9/6
 */
@Setter
@Getter
@EqualsAndHashCode
@NoArgsConstructor
public class BottomLevelDataReviewVO extends CommonBaseVO implements Serializable {

    /**
     * 文件名称
     */
    private String fileName;

    /**
     *
     *  合同号
     */
    private String contractNo;

    /**
     *
     *  lv1编码
     */
    private String lv1Code;

    /**
     *
     *  lv1名称
     */
    private String lv1CnName;

    /**
     *
     *  lv2编码
     */
    private String lv2Code;

    /**
     *
     *  lv2名称
     */
    private String lv2CnName;

    /**
     *
     *  lv3编码
     */
    private String lv3Code;

    /**
     *
     *  lv3名称
     */
    private String lv3CnName;

    /**
     *
     *  lv4编码
     */
    private String lv4Code;

    /**
     *
     *  lv4名称
     */
    private String lv4CnName;


    /**
     *
     *  关键字搜索
     */
    private String keyword;

    /**
     *
     *  合同号
     */
    private String hwContractNum;

    /**
     *
     *  页面区分
     */
    private String pageFlag;

    private String beginDate;

    private String endDate;

    private String modifyType;

    private Long annualVersionId;

    private Set<String> costTypeList;

}
