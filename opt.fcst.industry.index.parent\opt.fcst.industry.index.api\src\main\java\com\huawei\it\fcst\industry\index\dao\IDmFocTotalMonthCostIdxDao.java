/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.month.DmFocMonthCostIdxVO;
import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 月度分析-总成本指数相关DAO查询
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2023-03-10 11:14:15
 */
public interface IDmFocTotalMonthCostIdxDao {

    /**
     * 校验所选基期是否有数据
     *
     * @param monthAnalysisVO 参数Dto
     * @return int
     */
    int findTotalPriceIdxByBasePeriodId(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);

    /**
     * Find all DmFoiPriceIndexVO records.
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<DmFocMonthCostIdxVO> findTotalPriceIndexVOList(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);

    /**
     * Find all DmFoiPriceIndexVO records.
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<DmFocMonthCostIdxVO> findTotalComparePriceIndexVOList(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);

    /**
     * [查询采购价格指数（多维度）图数据]
     *     多维度：多专家团、多品类、多Item
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<DmFocMonthCostIdxVO> findTotalPriceIndexByMultiDim(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);

    List<DmFocMonthCostIdxVO> findTotalAmpMadePriceIndexChart(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);

    /**
     * [查询采购价格指数（多维度）图数据]导出
     *     多维度：多专家团、多品类、多Item
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<DmFocMonthCostIdxVO> findPriceIndexExpData(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);

}
