/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.annual;

import com.huawei.it.fcst.industry.pbi.vo.common.CommonBaseVO;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * AnnualAnalysisVO Class
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
@Getter
@Setter
@EqualsAndHashCode
@NoArgsConstructor
public class AnnualAnalysisVO extends CommonBaseVO implements Serializable {
    private static final long serialVersionUID = -1576704097209028139L;

    private List<String> parentCodeList;

    private List<String> customParentCodeList;

    private List<String> customGroupCodeList;

    private List<String> groupCodeList;

    private List<String> customIdList;

    private List<String> subGroupCodeList;

    private List<String> connectParentGroupCodeList;

    private String parentCodeOrder;

    private String condition;

    private Long customId;

    private Boolean isContainComb;

    private Boolean isComparFlag = false;

    private String orderCnName;

    private List<AnnualAnalysisVO> annualParamList;

    private String teamLevel;

    private String orderColumn;

    private String orderMethod;

    private String year;

    private String ytdFlag;

    private String ytdFlagCnName;

    private Long versionId;

    private String fileName;

    private String costTypeCnName;

    private String granularityTypeCnName;

    private String overseaFlagCnName;

    private String regionCnName;

    private String repofficeCnName;

    // 实际数截止月
    private String actualMonth;

    public String maxValue;

    private String groupCodeOrder;

    private String prodRndTeamCodeOrder;

    /**
     * 会计期年份
     */
    private String periodYear;

    private Long periodId;

    private List<String> yearList;


    /**
     * 是否多选
     */
    private Boolean isMultipleSelect;

    private String combTablePreFix;

    private String nextGroupName;

    private String name;

    // 是否需要虚化
    private Boolean isNeedBlur;

}

