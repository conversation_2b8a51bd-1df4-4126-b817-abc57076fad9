/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.template;

import com.huawei.it.fcst.export.IExcelTemplateBeanManager;
import com.huawei.it.fcst.export.vo.ExcelTemplateBeanManager;
import com.huawei.it.fcst.export.vo.SheetBeanMetaVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import lombok.Getter;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * 年度导出模板
 *
 * <AUTHOR>
 * @since 2024/06
 */
@Getter
public enum AnnualTemplateEnum implements IExcelTemplateBeanManager {
    ANNUAL_01("01", "AnnualExportTemplate", "年度分析", "年度分析-明细数据") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            ExcelTemplateBeanManager excelTemplateBeanManager = new ExcelTemplateBeanManager();
            List<SheetBeanMetaVO> annualList = new ArrayList<>();
            annualList.add(new SheetBeanMetaVO(ANNUAL_01.templateName, 0, "AnnualDistributeExportDataProvider", "成本分布图", Boolean.FALSE));
            annualList.add(new SheetBeanMetaVO(ANNUAL_01.templateName, 1, "AnnualExportDataProvider", "成本涨跌图", Boolean.FALSE));
            annualList.add(new SheetBeanMetaVO(ANNUAL_01.templateName, 2, "AnnualChildExportDataProvider", "成本涨跌图多子项", Boolean.FALSE));
            annualList.add(new SheetBeanMetaVO(ANNUAL_01.templateName, 3, "AnnualViewExportDataProvider", "成本涨跌幅一览表", Boolean.FALSE));
            excelTemplateBeanManager.setSheetBeans(annualList);
            excelTemplateBeanManager.setTemplateName(ANNUAL_01.templateName);
            excelTemplateBeanManager.setModuleType(ANNUAL_01.moduleType);
            excelTemplateBeanManager.setDesc(ANNUAL_01.desc);
            return excelTemplateBeanManager;
        }
    },

    ANNUAL_02("02", "AnnualExportTemplate2", "年度分析", "年度分析-明细数据") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            ExcelTemplateBeanManager excelTemplateBeanManager = new ExcelTemplateBeanManager();
            List<SheetBeanMetaVO> annualList = new ArrayList<>();
            annualList.add(new SheetBeanMetaVO(ANNUAL_02.templateName, 0, "AnnualExportDataProvider", "成本涨跌图", Boolean.FALSE));
            excelTemplateBeanManager.setSheetBeans(annualList);
            excelTemplateBeanManager.setTemplateName(ANNUAL_02.templateName);
            excelTemplateBeanManager.setModuleType(ANNUAL_02.moduleType);
            excelTemplateBeanManager.setDesc(ANNUAL_02.desc);
            return excelTemplateBeanManager;
        }
    },
    ANNUAL_03("03", "AnnualExportTemplate3", "年度分析", "年度分析-明细数据") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            ExcelTemplateBeanManager excelTemplateBeanManager = new ExcelTemplateBeanManager();
            List<SheetBeanMetaVO> annualList = new ArrayList<>();
            annualList.add(new SheetBeanMetaVO(ANNUAL_03.templateName, 0, "AnnualExportDataProvider", "成本涨跌图", Boolean.FALSE));
            annualList.add(new SheetBeanMetaVO(ANNUAL_03.templateName, 1, "AnnualChildExportDataProvider", "成本涨跌图多子项", Boolean.FALSE));
            annualList.add(new SheetBeanMetaVO(ANNUAL_03.templateName, 2, "AnnualViewExportDataProvider", "成本涨跌幅一览表", Boolean.FALSE));
            excelTemplateBeanManager.setSheetBeans(annualList);
            excelTemplateBeanManager.setTemplateName(ANNUAL_03.templateName);
            excelTemplateBeanManager.setModuleType(ANNUAL_03.moduleType);
            excelTemplateBeanManager.setDesc(ANNUAL_03.desc);
            return excelTemplateBeanManager;
        }
    },
    ANNUAL_04("04", "AnnualExportTemplate4", "年度分析", "年度分析-明细数据") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            ExcelTemplateBeanManager excelTemplateBeanManager = new ExcelTemplateBeanManager();
            List<SheetBeanMetaVO> annualList = new ArrayList<>();
            annualList.add(new SheetBeanMetaVO(ANNUAL_04.templateName, 0, "AnnualExportDataProvider", "成本涨跌图", Boolean.FALSE));
            annualList.add(new SheetBeanMetaVO(ANNUAL_04.templateName, 1, "AnnualChildExportDataProvider", "成本涨跌图多子项", Boolean.FALSE));
            annualList.add(new SheetBeanMetaVO(ANNUAL_04.templateName, 2, "AnnualViewExportDataProvider", "成本涨跌幅一览表", Boolean.FALSE));
            excelTemplateBeanManager.setSheetBeans(annualList);
            excelTemplateBeanManager.setTemplateName(ANNUAL_04.templateName);
            excelTemplateBeanManager.setModuleType(ANNUAL_04.moduleType);
            excelTemplateBeanManager.setDesc(ANNUAL_04.desc);
            return excelTemplateBeanManager;
        }
    },
    ANNUAL_05("05", "AnnualExportTemplate5", "年度分析", "年度分析-明细数据") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            ExcelTemplateBeanManager excelTemplateBeanManager = new ExcelTemplateBeanManager();
            List<SheetBeanMetaVO> annualList = new ArrayList<>();
            annualList.add(new SheetBeanMetaVO(ANNUAL_05.templateName, 0, "AnnualDistributeExportDataProvider", "成本分布图", Boolean.FALSE));
            annualList.add(new SheetBeanMetaVO(ANNUAL_05.templateName, 1, "AnnualExportDataProvider", "成本涨跌图", Boolean.FALSE));
            annualList.add(new SheetBeanMetaVO(ANNUAL_05.templateName, 2, "AnnualChildExportDataProvider", "成本涨跌图多子项", Boolean.FALSE));
            annualList.add(new SheetBeanMetaVO(ANNUAL_05.templateName, 3, "AnnualViewExportDataProvider", "成本涨跌幅一览表", Boolean.FALSE));
            excelTemplateBeanManager.setSheetBeans(annualList);
            excelTemplateBeanManager.setTemplateName(ANNUAL_05.templateName);
            excelTemplateBeanManager.setModuleType(ANNUAL_05.moduleType);
            excelTemplateBeanManager.setDesc(ANNUAL_05.desc);
            return excelTemplateBeanManager;
        }
    },
    ANNUAL_06("06", "AnnualExportTemplate6", "年度分析", "年度分析-明细数据") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            ExcelTemplateBeanManager excelTemplateBeanManager = new ExcelTemplateBeanManager();
            List<SheetBeanMetaVO> annualList = new ArrayList<>();
            annualList.add(new SheetBeanMetaVO(ANNUAL_06.templateName, 0, "AnnualDistributeExportDataProvider", "成本分布图", Boolean.FALSE));
            annualList.add(new SheetBeanMetaVO(ANNUAL_06.templateName, 1, "AnnualExportDataProvider", "成本涨跌图", Boolean.FALSE));
            excelTemplateBeanManager.setSheetBeans(annualList);
            excelTemplateBeanManager.setTemplateName(ANNUAL_06.templateName);
            excelTemplateBeanManager.setModuleType(ANNUAL_06.moduleType);
            excelTemplateBeanManager.setDesc(ANNUAL_06.desc);
            return excelTemplateBeanManager;
        }
    },
    // 单选模板
    ANNUAL_07("07", "AnnualExportTemplate7", "年度分析", "年度分析-明细数据") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            ExcelTemplateBeanManager excelTemplateBeanManager = new ExcelTemplateBeanManager();
            List<SheetBeanMetaVO> annualList = new ArrayList<>();
            annualList.add(new SheetBeanMetaVO(ANNUAL_07.templateName, 0, "AnnualDistributeExportDataProvider", "成本分布图", Boolean.FALSE));
            annualList.add(new SheetBeanMetaVO(ANNUAL_07.templateName, 1, "AnnualExportDataProvider", "成本涨跌图", Boolean.FALSE));
            annualList.add(new SheetBeanMetaVO(ANNUAL_07.templateName, 2, "AnnualChildExportDataProvider", "成本涨跌图多子项", Boolean.FALSE));
            annualList.add(new SheetBeanMetaVO(ANNUAL_07.templateName, 3, "AnnualViewExportDataProvider", "成本涨跌幅一览表", Boolean.FALSE));
            excelTemplateBeanManager.setSheetBeans(annualList);
            excelTemplateBeanManager.setTemplateName(ANNUAL_07.templateName);
            excelTemplateBeanManager.setModuleType(ANNUAL_07.moduleType);
            excelTemplateBeanManager.setDesc(ANNUAL_07.desc);
            return excelTemplateBeanManager;
        }
    },
    // 多选模板
    ANNUAL_08("08", "AnnualExportTemplate8", "年度分析", "年度分析-明细数据") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            ExcelTemplateBeanManager annualExcelTemplateBeanManager = new ExcelTemplateBeanManager();
            List<SheetBeanMetaVO> annualList = new ArrayList<>();
            annualList.add(new SheetBeanMetaVO(ANNUAL_08.templateName, 0, "AnnualDistributeExportDataProvider", "成本分布图", Boolean.FALSE));
            annualList.add(new SheetBeanMetaVO(ANNUAL_08.templateName, 1, "AnnualExportDataProvider", "成本涨跌图", Boolean.FALSE));
            annualList.add(new SheetBeanMetaVO(ANNUAL_08.templateName, 2, "AnnualChildExportDataProvider", "成本涨跌图多子项", Boolean.FALSE));
            annualList.add(new SheetBeanMetaVO(ANNUAL_08.templateName, 3, "AnnualViewExportDataProvider", "成本涨跌幅一览表", Boolean.FALSE));
            annualExcelTemplateBeanManager.setSheetBeans(annualList);
            annualExcelTemplateBeanManager.setTemplateName(ANNUAL_08.templateName);
            annualExcelTemplateBeanManager.setModuleType(ANNUAL_08.moduleType);
            annualExcelTemplateBeanManager.setDesc(ANNUAL_08.desc);
            return annualExcelTemplateBeanManager;
        }
    };
    private String code;
    private String templateName;
    private String moduleType;
    private String desc;

    AnnualTemplateEnum(String code, String templateName, String moduleType, String desc) {
        this.code = code;
        this.templateName = templateName;
        this.moduleType = moduleType;
        this.desc = desc;
    }
    static final String ANNUAL = "ANNUAL";
    static final String SUB_LEVEL_CODE = "{0}_{1}";

    /**
     * 获取模板信息
     *
     * @param levelCode 层级code
     * @param roleName 角色名称
     * @return 枚举信息
     * @throws CommonApplicationException
     */
    public static AnnualTemplateEnum getByCode(String levelCode, String roleName) throws CommonApplicationException {
        String key = MessageFormat.format(SUB_LEVEL_CODE, ANNUAL, levelCode);
        for (AnnualTemplateEnum value : AnnualTemplateEnum.values()) {
            if (value.name().equalsIgnoreCase(key)) {
                return value;
            }
        }
        throw new CommonApplicationException("Check the annual template definition relationship.");
    }
}
