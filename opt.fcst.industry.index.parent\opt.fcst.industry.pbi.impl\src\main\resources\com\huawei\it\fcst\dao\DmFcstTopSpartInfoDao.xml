<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.pbi.dao.IDmFcstTopSpartInfoDao">
    <resultMap type="com.huawei.it.fcst.industry.pbi.vo.config.DmFcstTopSpartInfoDTO" id="resultMap">
        <result property="delFlag" column="del_flag"/>
        <result property="lv0ProdRdTeamCnName" column="lv0_prod_rd_team_cn_name"/>
        <result property="lv0ProdRndTeamCode" column="lv0_prod_rnd_team_code"/>
        <result property="lv1ProdRdTeamCnName" column="lv1_prod_rd_team_cn_name"/>
        <result property="lv1ProdRndTeamCode" column="lv1_prod_rnd_team_code"/>
        <result property="lv2ProdRdTeamCnName" column="lv2_prod_rd_team_cn_name"/>
        <result property="lv2ProdRndTeamCode" column="lv2_prod_rnd_team_code"/>
        <result property="lv3ProdRdTeamCnName" column="lv3_prod_rd_team_cn_name"/>
        <result property="lv3ProdRndTeamCode" column="lv3_prod_rnd_team_code"/>
        <result property="lv4ProdRdTeamCnName" column="lv4_prod_rd_team_cn_name"/>
        <result property="lv4ProdRndTeamCode" column="lv4_prod_rnd_team_code"/>
        <result property="overseaFlag" column="oversea_flag"/>
        <result property="lv0ProdListCode" column="lv0_prod_list_code"/>
        <result property="topFlag" column="is_top_flag"/>
        <result property="periodYear" column="period_year"/>
        <result property="weightRate" column="weight_rate"/>
        <result property="parentCode" column="parent_code"/>
        <result property="parentWeightRate" column="parent_weight_rate"/>
        <result property="weight0" column="weight0"/>
        <result property="weight1" column="weight1"/>
        <result property="weight2" column="weight2"/>
        <result property="weight3" column="weight3"/>
        <result property="weight4" column="weight4"/>
        <result property="weight4" column="weight4"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="versionId" column="version_id"/>
        <result property="creationDate" column="creation_date"/>
        <result property="createdBy" column="created_by"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="costType" column="cost_type"/>
        <result property="granularityType" column="granularity_type"/>
        <result property="bgCode" column="bg_code"/>
        <result property="bgCnName" column="bg_cn_name"/>
        <result property="bgCode" column="bg_code"/>
        <result property="bgCnName" column="bg_cn_name"/>
        <result property="regionCode" column="region_code"/>
        <result property="regionCnName" column="region_cn_name"/>
        <result property="repofficeCode" column="repoffice_code"/>
        <result property="repofficeCnName" column="repoffice_cn_name"/>
        <result property="topSpartCode" column="top_spart_code"/>
        <result property="softwareMark" column="software_mark"/>
        <result property="mainFlag" column="main_flag"/>
    </resultMap>

<sql id="searchFieldsExport">
    del_flag = 'N'
    AND view_flag = 'PROD_SPART'
    <choose>
        <when test='granularityType == "IRB"'>
            <if test='lv0ProdRndTeamCode != null and lv0ProdRndTeamCode!=""'>
                AND lv0_prod_rnd_team_code = #{lv0ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv1ProdRndTeamCode != null and lv1ProdRndTeamCode!=""'>
                AND lv1_prod_rnd_team_code = #{lv1ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv2ProdRndTeamCode != null and lv2ProdRndTeamCode!=""'>
                AND lv2_prod_rnd_team_code = #{lv2ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv3ProdRndTeamCode != null and lv3ProdRndTeamCode!=""'>
                AND lv3_prod_rnd_team_code = #{lv3ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv4ProdRndTeamCode != null and lv4ProdRndTeamCode!=""'>
                AND lv4_prod_rnd_team_code = #{lv4ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
        </when>
        <when test='granularityType == "PROD"'>
            <if test='lv0ProdRndTeamCode != null and lv0ProdRndTeamCode!=""'>
                AND lv0_prod_list_code = #{lv0ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv1ProdRndTeamCode != null and lv1ProdRndTeamCode!=""'>
                AND lv1_prod_list_code = #{lv1ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv2ProdRndTeamCode != null and lv2ProdRndTeamCode!=""'>
                AND lv2_prod_list_code = #{lv2ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv3ProdRndTeamCode != null and lv3ProdRndTeamCode!=""'>
                AND lv3_prod_list_code = #{lv3ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv4ProdRndTeamCode != null and lv4ProdRndTeamCode!=""'>
                AND lv4_prod_list_code = #{lv4ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
        </when>
        <when test='granularityType == "INDUS"'>
            <if test='lv0ProdRndTeamCode != null and lv0ProdRndTeamCode!=""'>
                AND lv0_industry_catg_code = #{lv0ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv1ProdRndTeamCode != null and lv1ProdRndTeamCode!=""'>
                AND lv1_industry_catg_code = #{lv1ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv2ProdRndTeamCode != null and lv2ProdRndTeamCode!=""'>
                AND lv2_industry_catg_code = #{lv2ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv3ProdRndTeamCode != null and lv3ProdRndTeamCode!=""'>
                AND lv3_industry_catg_code = #{lv3ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv4ProdRndTeamCode != null and lv4ProdRndTeamCode!=""'>
                AND lv4_industry_catg_code = #{lv4ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
        </when>
    </choose>
    <if test='overseaFlag != null and overseaFlag!=""'>
        AND oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
    </if>
    <if test='softwareMark != null and softwareMark!=""'>
        AND software_mark = #{softwareMark,jdbcType=VARCHAR}
    </if>
    <if test='bgCode != null and bgCode!=""'>
        AND bg_code = #{bgCode,jdbcType=VARCHAR}
    </if>
    <if test='regionCode != null and regionCode!=""'>
        AND region_code = #{regionCode,jdbcType=VARCHAR}
    </if>
    <if test='repofficeCode != null and repofficeCode!=""'>
        AND repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
    </if>
    <if test='topSpartCode != null and topSpartCode!=""'>
        AND top_spart_code = #{topSpartCode,jdbcType=VARCHAR}
    </if>
    <if test='versionId != null'>
        AND version_id = #{versionId,jdbcType=VARCHAR}
    </if>
    <if test='lastUpdatedBy != null'>
        AND last_updated_by LIKE CONCAT(CONCAT('%', #{lastUpdatedBy,jdbcType=VARCHAR}) ,'%')
    </if>
    <if test='creationDate != null'>
        AND creation_date=#{creationDate,jdbcType=TIMESTAMP}
    </if>
    <if test='createdBy != null'>
        AND created_by LIKE CONCAT(CONCAT('%', #{createdBy,jdbcType=VARCHAR}) ,'%')
    </if>
    <if test='lastUpdateDate != null'>
        AND last_update_date=#{lastUpdateDate,jdbcType=TIMESTAMP}
    </if>
</sql>
    <sql id="searchFields">
            AND del_flag = 'N'
            AND view_flag = 'PROD_SPART'
        <choose>
            <when test='_parameter.get("0").granularityType == "IRB"'>
                <if test='_parameter.get("0").lv0ProdRndTeamCode != null and _parameter.get("0").lv0ProdRndTeamCode!=""'>
                    AND lv0_prod_rnd_team_code = #{0.lv0ProdRndTeamCode,jdbcType=VARCHAR}
                </if>
                <if test='_parameter.get("0").lv1ProdRndTeamCode != null and _parameter.get("0").lv1ProdRndTeamCode!=""'>
                    AND lv1_prod_rnd_team_code = #{0.lv1ProdRndTeamCode,jdbcType=VARCHAR}
                </if>
                <if test='_parameter.get("0").lv2ProdRndTeamCode != null and _parameter.get("0").lv2ProdRndTeamCode!=""'>
                    AND lv2_prod_rnd_team_code = #{0.lv2ProdRndTeamCode,jdbcType=VARCHAR}
                </if>
                <if test='_parameter.get("0").lv3ProdRndTeamCode != null and _parameter.get("0").lv3ProdRndTeamCode!=""'>
                    AND lv3_prod_rnd_team_code = #{0.lv3ProdRndTeamCode,jdbcType=VARCHAR}
                </if>
                <if test='_parameter.get("0").lv4ProdRndTeamCode != null and _parameter.get("0").lv4ProdRndTeamCode!=""'>
                    AND lv4_prod_rnd_team_code = #{0.lv4ProdRndTeamCode,jdbcType=VARCHAR}
                </if>
            </when>
            <when test='_parameter.get("0").granularityType == "PROD"'>
                <if test='_parameter.get("0").lv0ProdRndTeamCode != null and _parameter.get("0").lv0ProdRndTeamCode!=""'>
                    AND lv0_prod_list_code = #{0.lv0ProdRndTeamCode,jdbcType=VARCHAR}
                </if>
                <if test='_parameter.get("0").lv1ProdRndTeamCode != null and _parameter.get("0").lv1ProdRndTeamCode!=""'>
                    AND lv1_prod_list_code = #{0.lv1ProdRndTeamCode,jdbcType=VARCHAR}
                </if>
                <if test='_parameter.get("0").lv2ProdRndTeamCode != null and _parameter.get("0").lv2ProdRndTeamCode!=""'>
                    AND lv2_prod_list_code = #{0.lv2ProdRndTeamCode,jdbcType=VARCHAR}
                </if>
                <if test='_parameter.get("0").lv3ProdRndTeamCode != null and _parameter.get("0").lv3ProdRndTeamCode!=""'>
                    AND lv3_prod_list_code = #{0.lv3ProdRndTeamCode,jdbcType=VARCHAR}
                </if>
                <if test='_parameter.get("0").lv4ProdRndTeamCode != null and _parameter.get("0").lv4ProdRndTeamCode!=""'>
                    AND lv4_prod_list_code = #{0.lv4ProdRndTeamCode,jdbcType=VARCHAR}
                </if>
            </when>
            <when test='_parameter.get("0").granularityType == "INDUS"'>
                <if test='_parameter.get("0").lv0ProdRndTeamCode != null and _parameter.get("0").lv0ProdRndTeamCode!=""'>
                    AND lv0_industry_catg_code = #{0.lv0ProdRndTeamCode,jdbcType=VARCHAR}
                </if>
                <if test='_parameter.get("0").lv1ProdRndTeamCode != null and _parameter.get("0").lv1ProdRndTeamCode!=""'>
                    AND lv1_industry_catg_code = #{0.lv1ProdRndTeamCode,jdbcType=VARCHAR}
                </if>
                <if test='_parameter.get("0").lv2ProdRndTeamCode != null and _parameter.get("0").lv2ProdRndTeamCode!=""'>
                    AND lv2_industry_catg_code = #{0.lv2ProdRndTeamCode,jdbcType=VARCHAR}
                </if>
                <if test='_parameter.get("0").lv3ProdRndTeamCode != null and _parameter.get("0").lv3ProdRndTeamCode!=""'>
                    AND lv3_industry_catg_code = #{0.lv3ProdRndTeamCode,jdbcType=VARCHAR}
                </if>
                <if test='_parameter.get("0").lv4ProdRndTeamCode != null and _parameter.get("0").lv4ProdRndTeamCode!=""'>
                    AND lv4_industry_catg_code = #{0.lv4ProdRndTeamCode,jdbcType=VARCHAR}
                </if>
            </when>
        </choose>
            <if test='_parameter.get("0").softwareMark != null and _parameter.get("0").softwareMark!=""'>
                AND software_mark = #{0.softwareMark,jdbcType=VARCHAR}
            </if>
            <if test='_parameter.get("0").overseaFlag != null and _parameter.get("0").overseaFlag!=""'>
                AND oversea_flag = #{0.overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='_parameter.get("0").bgCode != null and _parameter.get("0").bgCode!=""'>
                AND bg_code = #{0.bgCode,jdbcType=VARCHAR}
            </if>
            <if test='_parameter.get("0").regionCode != null and _parameter.get("0").regionCode!=""'>
                AND region_code = #{0.regionCode,jdbcType=VARCHAR}
            </if>
            <if test='_parameter.get("0").repofficeCode != null and _parameter.get("0").repofficeCode!=""'>
                AND repoffice_code = #{0.repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='_parameter.get("0").topSpartCode != null and _parameter.get("0").topSpartCode!=""'>
                AND top_spart_code = #{0.topSpartCode,jdbcType=VARCHAR}
            </if>
            <if test='_parameter.get("0").versionId != null'>
                AND version_id = #{0.versionId,jdbcType=VARCHAR}
            </if>
            <if test='_parameter.get("0").lastUpdatedBy != null'>
                AND last_updated_by LIKE CONCAT(CONCAT('%', #{0.lastUpdatedBy,jdbcType=VARCHAR}) ,'%')
            </if>
            <if test='_parameter.get("0").creationDate != null'>
                AND creation_date=#{0.creationDate,jdbcType=TIMESTAMP}
            </if>
            <if test='_parameter.get("0").createdBy != null'>
                AND created_by LIKE CONCAT(CONCAT('%', #{0.createdBy,jdbcType=VARCHAR}) ,'%')
            </if>
            <if test='_parameter.get("0").lastUpdateDate != null'>
                AND last_update_date=#{0.lastUpdateDate,jdbcType=TIMESTAMP}
            </if>
    </sql>

    <select id="findTopSpartByPage" resultMap="resultMap">
        select d1.*,d2.is_top_flag FROM (
        SELECT
        <choose>
            <when test='_parameter.get("0").granularityType == "IRB"'>
                lv0_prod_rd_team_cn_name,
                lv0_prod_rnd_team_code,
                lv1_prod_rd_team_cn_name,
                lv1_prod_rnd_team_code,
                lv2_prod_rd_team_cn_name,
                lv2_prod_rnd_team_code,
                lv3_prod_rd_team_cn_name,
                lv3_prod_rnd_team_code,
                lv4_prod_rd_team_cn_name,
                lv4_prod_rnd_team_code,
               '重量级团队目录' as granularity_type,
               <if test='_parameter.get("0").costType == "PSP"'>
                   'PSP成本' as cost_type,
               </if>
                <if test='_parameter.get("0").costType == "STD"'>
                    '标准成本' as cost_type,
                </if>
            </when>
            <when test='_parameter.get("0").granularityType == "INDUS"'>
                lv0_industry_catg_code as lv0_prod_rnd_team_code,
                lv0_industry_catg_cn_name as lv0_prod_rd_team_cn_name,
                lv1_industry_catg_code as lv1_prod_rnd_team_code,
                lv1_industry_catg_cn_name as lv1_prod_rd_team_cn_name,
                lv2_industry_catg_code as lv2_prod_rnd_team_code,
                lv2_industry_catg_cn_name as lv2_prod_rd_team_cn_name,
                lv3_industry_catg_code as lv3_prod_rnd_team_code,
                lv3_industry_catg_cn_name as lv3_prod_rd_team_cn_name,
                lv4_industry_catg_code as lv4_prod_rnd_team_code,
                lv4_industry_catg_cn_name as lv4_prod_rd_team_cn_name,
                '产业目录' as granularity_type,
                <if test='_parameter.get("0").costType == "PSP"'>
                    'PSP成本' as cost_type,
                </if>
                <if test='_parameter.get("0").costType == "STD"'>
                    '标准成本' as cost_type,
                </if>
            </when>
            <when test='_parameter.get("0").granularityType == "PROD"'>
                lv0_prod_list_code as lv0_prod_rnd_team_code,
                lv0_prod_list_cn_name as lv0_prod_rd_team_cn_name,
                lv1_prod_list_code as lv1_prod_rnd_team_code,
                lv1_prod_list_cn_name as lv1_prod_rd_team_cn_name,
                lv2_prod_list_code as lv2_prod_rnd_team_code,
                lv2_prod_list_cn_name as lv2_prod_rd_team_cn_name,
                lv3_prod_list_code as lv3_prod_rnd_team_code,
                lv3_prod_list_cn_name as lv3_prod_rd_team_cn_name,
                lv4_prod_list_code as lv4_prod_rnd_team_code,
                lv4_prod_list_cn_name as lv4_prod_rd_team_cn_name,
                '销售目录' as granularity_type,
                <if test='_parameter.get("0").costType == "PSP"'>
                    'PSP成本' as cost_type,
                </if>
                <if test='_parameter.get("0").costType == "STD"'>
                    '标准成本' as cost_type,
                </if>
            </when>
        </choose>
        top_spart_cn_name,
        top_spart_code,
        region_code,
        region_cn_name,
        repoffice_code,
        repoffice_cn_name,
        bg_code,
        bg_cn_name,
        oversea_flag,
        software_mark,
        main_flag,
        SUM ( CASE WHEN period_year = #{0.periodYear0} THEN  weight_rate END ) AS weight0,
        SUM ( CASE WHEN period_year = #{0.periodYear1} THEN  weight_rate END ) AS weight1,
        SUM ( CASE WHEN period_year = #{0.periodYear2} THEN  weight_rate END ) AS weight2,
        SUM ( CASE WHEN period_year = #{0.periodYear3} THEN  weight_rate END ) AS weight3,
        SUM ( CASE WHEN period_year = #{0.periodYear4} THEN  weight_rate END ) AS weight4
        <if test='_parameter.get("0").costType == "PSP"'>
            <choose>
                <when test='_parameter.get("0").granularityType == "IRB"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_irb_top_spart_info_t
                </when>
                <when test='_parameter.get("0").granularityType == "INDUS"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_indus_top_spart_info_t
                </when>
                <when test='_parameter.get("0").granularityType == "PROD"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_top_spart_info_t
                </when>
            </choose>
        </if>
        <if test='_parameter.get("0").costType == "STD"'>
            <choose>
                <when test='_parameter.get("0").granularityType == "IRB"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_irb_top_spart_info_t
                </when>
                <when test='_parameter.get("0").granularityType == "INDUS"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_indus_top_spart_info_t
                </when>
                <when test='_parameter.get("0").granularityType == "PROD"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_top_spart_info_t
                </when>
            </choose>
        </if>
        where 1=1
        <include refid="searchFields"/>
        GROUP BY
        <choose>
            <when test='_parameter.get("0").granularityType == "IRB"'>
                lv0_prod_rd_team_cn_name,
                lv0_prod_rnd_team_code,
                lv1_prod_rd_team_cn_name,
                lv1_prod_rnd_team_code,
                lv2_prod_rd_team_cn_name,
                lv2_prod_rnd_team_code,
                lv3_prod_rd_team_cn_name,
                lv3_prod_rnd_team_code,
                lv4_prod_rd_team_cn_name,
                lv4_prod_rnd_team_code,
                granularity_type,
                cost_type,
            </when>
            <when test='_parameter.get("0").granularityType == "INDUS"'>
                lv0_industry_catg_code,
                lv0_industry_catg_cn_name,
                lv1_industry_catg_code,
                lv1_industry_catg_cn_name,
                lv2_industry_catg_code,
                lv2_industry_catg_cn_name,
                lv3_industry_catg_code,
                lv3_industry_catg_cn_name,
                lv4_industry_catg_code,
                lv4_industry_catg_cn_name,
                granularity_type,
                cost_type,
            </when>
            <when test='_parameter.get("0").granularityType == "PROD"'>
                lv0_prod_list_code,
                lv0_prod_list_cn_name,
                lv1_prod_list_code,
                lv1_prod_list_cn_name,
                lv2_prod_list_code,
                lv2_prod_list_cn_name,
                lv3_prod_list_code,
                lv3_prod_list_cn_name,
                lv4_prod_list_code,
                lv4_prod_list_cn_name,
                granularity_type,
                cost_type,
            </when>
        </choose>
        top_spart_code,
        top_spart_cn_name,
        region_code,
        region_cn_name,
        repoffice_code,
        repoffice_cn_name,
        bg_code,
        bg_cn_name,
        oversea_flag,
        software_mark,
        main_flag
        )d1 left join (
        SELECT DISTINCT
        <choose>
            <when test='_parameter.get("0").granularityType == "IRB"'>
                lv0_prod_rnd_team_code,
                lv1_prod_rnd_team_code,
                lv2_prod_rnd_team_code,
                lv3_prod_rnd_team_code,
                lv4_prod_rnd_team_code,
            </when>
            <when test='_parameter.get("0").granularityType == "INDUS"'>
                lv0_industry_catg_code,
                lv1_industry_catg_code,
                lv2_industry_catg_code,
                lv3_industry_catg_code,
                lv4_industry_catg_code,
            </when>
            <when test='_parameter.get("0").granularityType == "PROD"'>
                lv0_prod_list_code,
                lv1_prod_list_code,
                lv2_prod_list_code,
                lv3_prod_list_code,
                lv4_prod_list_code,
            </when>
        </choose>
        top_spart_code,
        top_spart_cn_name,
        region_code,
        region_cn_name,
        repoffice_code,
        repoffice_cn_name,
        bg_code,
        bg_cn_name,
        oversea_flag,
        software_mark,
        is_top_flag,
        main_flag
        <if test='_parameter.get("0").costType == "PSP"'>
            <choose>
                <when test='_parameter.get("0").granularityType == "IRB"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_irb_top_spart_info_t
                </when>
                <when test='_parameter.get("0").granularityType == "INDUS"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_indus_top_spart_info_t
                </when>
                <when test='_parameter.get("0").granularityType == "PROD"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_top_spart_info_t
                </when>
            </choose>
        </if>
        <if test='_parameter.get("0").costType == "STD"'>
            <choose>
                <when test='_parameter.get("0").granularityType == "IRB"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_irb_top_spart_info_t
                </when>
                <when test='_parameter.get("0").granularityType == "INDUS"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_indus_top_spart_info_t
                </when>
                <when test='_parameter.get("0").granularityType == "PROD"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_top_spart_info_t
                </when>
            </choose>
        </if>
        WHERE
        double_flag = 'Y'
        AND is_top_flag = 'Y'
        <include refid="searchFields"/>
        ) d2 on
        d1.top_spart_code = d2.top_spart_code
        and d1.top_spart_cn_name = d2.top_spart_cn_name
        and d1.main_flag = d2.main_flag
        <choose>
            <when test='_parameter.get("0").granularityType == "IRB"'>
                AND nvl(d1.lv0_prod_rnd_team_code,'snull') = nvl(d2.lv0_prod_rnd_team_code,'snull')
                AND nvl(d1.lv1_prod_rnd_team_code,'snull') = nvl(d2.lv1_prod_rnd_team_code,'snull')
                AND nvl(d1.lv2_prod_rnd_team_code,'snull') = nvl(d2.lv2_prod_rnd_team_code,'snull')
                AND nvl(d1.lv3_prod_rnd_team_code,'snull') = nvl(d2.lv3_prod_rnd_team_code,'snull')
                AND nvl(d1.lv4_prod_rnd_team_code,'snull') = nvl(d2.lv4_prod_rnd_team_code,'snull')
            </when>
            <when test='_parameter.get("0").granularityType == "INDUS"'>
                AND nvl(d1.lv0_prod_rnd_team_code,'snull') = nvl(d2.lv0_industry_catg_code,'snull')
                AND nvl(d1.lv1_prod_rnd_team_code,'snull') = nvl(d2.lv1_industry_catg_code,'snull')
                AND nvl(d1.lv2_prod_rnd_team_code,'snull') = nvl(d2.lv2_industry_catg_code,'snull')
                AND nvl(d1.lv3_prod_rnd_team_code,'snull') = nvl(d2.lv3_industry_catg_code,'snull')
                AND nvl(d1.lv4_prod_rnd_team_code,'snull') = nvl(d2.lv4_industry_catg_code,'snull')
            </when>
            <when test='_parameter.get("0").granularityType == "PROD"'>
                AND nvl(d1.lv0_prod_rnd_team_code,'snull') = nvl(d2.lv0_prod_list_code,'snull')
                AND nvl(d1.lv1_prod_rnd_team_code,'snull') = nvl(d2.lv1_prod_list_code,'snull')
                AND nvl(d1.lv2_prod_rnd_team_code,'snull') = nvl(d2.lv2_prod_list_code,'snull')
                AND nvl(d1.lv3_prod_rnd_team_code,'snull') = nvl(d2.lv3_prod_list_code,'snull')
                AND nvl(d1.lv4_prod_rnd_team_code,'snull') = nvl(d2.lv4_prod_list_code,'snull')
            </when>
        </choose>
        ORDER BY
        if(isnull(d1.weight4),1,0),d1.weight4 DESC
        LIMIT #{1.pageSize} OFFSET #{1.mysqlStartIndex}
    </select>

    <select id="findTopSpartByPageCount" resultType="int">
        SELECT COUNT(1) from
        (
        SELECT
        <choose>
            <when test='_parameter.get("0").granularityType == "IRB"'>
                lv0_prod_rd_team_cn_name,
                lv0_prod_rnd_team_code,
                lv1_prod_rd_team_cn_name,
                lv1_prod_rnd_team_code,
                lv2_prod_rd_team_cn_name,
                lv2_prod_rnd_team_code,
                lv3_prod_rd_team_cn_name,
                lv3_prod_rnd_team_code,
                lv4_prod_rd_team_cn_name,
                lv4_prod_rnd_team_code,
            </when>
            <when test='_parameter.get("0").granularityType == "INDUS"'>
                lv0_industry_catg_code,
                lv0_industry_catg_cn_name,
                lv1_industry_catg_code,
                lv1_industry_catg_cn_name,
                lv2_industry_catg_code,
                lv2_industry_catg_cn_name,
                lv3_industry_catg_code,
                lv3_industry_catg_cn_name,
                lv4_industry_catg_code,
                lv4_industry_catg_cn_name,
            </when>
            <when test='_parameter.get("0").granularityType == "PROD"'>
                lv0_prod_list_code,
                lv0_prod_list_cn_name,
                lv1_prod_list_code,
                lv1_prod_list_cn_name,
                lv2_prod_list_code,
                lv2_prod_list_cn_name,
                lv3_prod_list_code,
                lv3_prod_list_cn_name,
                lv4_prod_list_code,
                lv4_prod_list_cn_name,
            </when>
        </choose>
        top_spart_cn_name,
        top_spart_code,
        region_code,
        region_cn_name,
        repoffice_code,
        repoffice_cn_name,
        bg_code,
        bg_cn_name,
        oversea_flag,
        software_mark,
        main_flag,
        SUM ( CASE WHEN period_year = #{0.periodYear0} THEN  weight_rate END ) AS weight0,
        SUM ( CASE WHEN period_year = #{0.periodYear1} THEN  weight_rate END ) AS weight1,
        SUM ( CASE WHEN period_year = #{0.periodYear2} THEN  weight_rate END ) AS weight2,
        SUM ( CASE WHEN period_year = #{0.periodYear3} THEN  weight_rate END ) AS weight3,
        SUM ( CASE WHEN period_year = #{0.periodYear4} THEN  weight_rate END ) AS weight4

        <if test='_parameter.get("0").costType == "PSP"'>
            <choose>
                <when test='_parameter.get("0").granularityType == "IRB"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_irb_top_spart_info_t
                </when>
                <when test='_parameter.get("0").granularityType == "INDUS"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_indus_top_spart_info_t
                </when>
                <when test='_parameter.get("0").granularityType == "PROD"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_top_spart_info_t
                </when>
            </choose>
        </if>
        <if test='_parameter.get("0").costType == "STD"'>
            <choose>
                <when test='_parameter.get("0").granularityType == "IRB"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_irb_top_spart_info_t
                </when>
                <when test='_parameter.get("0").granularityType == "INDUS"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_indus_top_spart_info_t
                </when>
                <when test='_parameter.get("0").granularityType == "PROD"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_top_spart_info_t
                </when>
            </choose>
        </if>
        where 1=1
        <include refid="searchFields"/>
        GROUP BY
        <choose>
            <when test='_parameter.get("0").granularityType == "IRB"'>
                lv0_prod_rd_team_cn_name,
                lv0_prod_rnd_team_code,
                lv1_prod_rd_team_cn_name,
                lv1_prod_rnd_team_code,
                lv2_prod_rd_team_cn_name,
                lv2_prod_rnd_team_code,
                lv3_prod_rd_team_cn_name,
                lv3_prod_rnd_team_code,
                lv4_prod_rd_team_cn_name,
                lv4_prod_rnd_team_code,
            </when>
            <when test='_parameter.get("0").granularityType == "INDUS"'>
                lv0_industry_catg_code,
                lv0_industry_catg_cn_name,
                lv1_industry_catg_code,
                lv1_industry_catg_cn_name,
                lv2_industry_catg_code,
                lv2_industry_catg_cn_name,
                lv3_industry_catg_code,
                lv3_industry_catg_cn_name,
                lv4_industry_catg_code,
                lv4_industry_catg_cn_name,
            </when>
            <when test='_parameter.get("0").granularityType == "PROD"'>
                lv0_prod_list_code,
                lv0_prod_list_cn_name,
                lv1_prod_list_code,
                lv1_prod_list_cn_name,
                lv2_prod_list_code,
                lv2_prod_list_cn_name,
                lv3_prod_list_code,
                lv3_prod_list_cn_name,
                lv4_prod_list_code,
                lv4_prod_list_cn_name,
            </when>
        </choose>
        top_spart_code,
        top_spart_cn_name,
        region_code,
        region_cn_name,
        repoffice_code,
        repoffice_cn_name,
        bg_code,
        bg_cn_name,
        oversea_flag,
        software_mark,
        main_flag)d
    </select>

    <select id="findTopSpartVOList" resultType="java.util.Map" fetchSize="10000">
        select d1.*, d2.is_top_flag FROM (
        SELECT
        <choose>
            <when test='granularityType == "IRB"'>
                lv0_prod_rd_team_cn_name,
                lv0_prod_rnd_team_code,
                lv1_prod_rd_team_cn_name,
                lv1_prod_rnd_team_code,
                lv2_prod_rd_team_cn_name,
                lv2_prod_rnd_team_code,
                lv3_prod_rd_team_cn_name,
                lv3_prod_rnd_team_code,
                lv4_prod_rd_team_cn_name,
                lv4_prod_rnd_team_code,
            </when>
            <when test='granularityType == "INDUS"'>
                lv0_industry_catg_code,
                lv0_industry_catg_cn_name,
                lv1_industry_catg_code,
                lv1_industry_catg_cn_name,
                lv2_industry_catg_code,
                lv2_industry_catg_cn_name,
                lv3_industry_catg_code,
                lv3_industry_catg_cn_name,
                lv4_industry_catg_code,
                lv4_industry_catg_cn_name,
            </when>
            <when test='granularityType == "PROD"'>
                lv0_prod_list_code,
                lv0_prod_list_cn_name,
                lv1_prod_list_code,
                lv1_prod_list_cn_name,
                lv2_prod_list_code,
                lv2_prod_list_cn_name,
                lv3_prod_list_code,
                lv3_prod_list_cn_name,
                lv4_prod_list_code,
                lv4_prod_list_cn_name,
            </when>
        </choose>
        top_spart_cn_name,
        top_spart_code,
        region_code,
        region_cn_name,
        repoffice_code,
        repoffice_cn_name,
        bg_code,
        bg_cn_name,
        oversea_flag,
        SUM ( CASE WHEN period_year = #{periodYear0} THEN  weight_rate END ) AS weight0,
        SUM ( CASE WHEN period_year = #{periodYear1} THEN  weight_rate END ) AS weight1,
        SUM ( CASE WHEN period_year = #{periodYear2} THEN  weight_rate END ) AS weight2,
        SUM ( CASE WHEN period_year = #{periodYear3} THEN  weight_rate END ) AS weight3,
        SUM ( CASE WHEN period_year = #{periodYear4} THEN  weight_rate END ) AS weight4
        <if test='costType == "PSP"'>
            <choose>
                <when test='granularityType == "IRB"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_irb_top_spart_info_t
                </when>
                <when test='granularityType == "INDUS"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_indus_top_spart_info_t
                </when>
                <when test='granularityType == "PROD"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_top_spart_info_t
                </when>
            </choose>
        </if>
        <if test='costType == "STD"'>
            <choose>
                <when test='granularityType == "IRB"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_irb_top_spart_info_t
                </when>
                <when test='granularityType == "INDUS"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_indus_top_spart_info_t
                </when>
                <when test='granularityType == "PROD"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_top_spart_info_t
                </when>
            </choose>
        </if>
        where 1=1
        <include refid="searchFieldsExport"/>
        GROUP BY
        <choose>
            <when test='granularityType == "IRB"'>
                lv0_prod_rd_team_cn_name,
                lv0_prod_rnd_team_code,
                lv1_prod_rd_team_cn_name,
                lv1_prod_rnd_team_code,
                lv2_prod_rd_team_cn_name,
                lv2_prod_rnd_team_code,
                lv3_prod_rd_team_cn_name,
                lv3_prod_rnd_team_code,
                lv4_prod_rd_team_cn_name,
                lv4_prod_rnd_team_code,
            </when>
            <when test='granularityType == "INDUS"'>
                lv0_industry_catg_code,
                lv0_industry_catg_cn_name,
                lv1_industry_catg_code,
                lv1_industry_catg_cn_name,
                lv2_industry_catg_code,
                lv2_industry_catg_cn_name,
                lv3_industry_catg_code,
                lv3_industry_catg_cn_name,
                lv4_industry_catg_code,
                lv4_industry_catg_cn_name,
            </when>
            <when test='granularityType == "PROD"'>
                lv0_prod_list_code,
                lv0_prod_list_cn_name,
                lv1_prod_list_code,
                lv1_prod_list_cn_name,
                lv2_prod_list_code,
                lv2_prod_list_cn_name,
                lv3_prod_list_code,
                lv3_prod_list_cn_name,
                lv4_prod_list_code,
                lv4_prod_list_cn_name,
            </when>
        </choose>
        top_spart_code,
        top_spart_cn_name,
        region_code,
        region_cn_name,
        repoffice_code,
        repoffice_cn_name,
        bg_code,
        bg_cn_name,
        oversea_flag
        )d1 left join (
        SELECT DISTINCT
        <choose>
            <when test='granularityType == "IRB"'>
                lv0_prod_rnd_team_code,
                lv1_prod_rnd_team_code,
                lv2_prod_rnd_team_code,
                lv3_prod_rnd_team_code,
                lv4_prod_rnd_team_code,
            </when>
            <when test='granularityType == "INDUS"'>
                lv0_industry_catg_code,
                lv1_industry_catg_code,
                lv2_industry_catg_code,
                lv3_industry_catg_code,
                lv4_industry_catg_code,
            </when>
            <when test='granularityType == "PROD"'>
                lv0_prod_list_code,
                lv1_prod_list_code,
                lv2_prod_list_code,
                lv3_prod_list_code,
                lv4_prod_list_code,
            </when>
        </choose>
        top_spart_code,
        top_spart_cn_name,
        region_code,
        region_cn_name,
        repoffice_code,
        repoffice_cn_name,
        bg_code,
        bg_cn_name,
        oversea_flag,
        is_top_flag
        <if test='costType == "PSP"'>
            <choose>
                <when test='granularityType == "IRB"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_irb_top_spart_info_t
                </when>
                <when test='granularityType == "INDUS"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_indus_top_spart_info_t
                </when>
                <when test='granularityType == "PROD"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_top_spart_info_t
                </when>
            </choose>
        </if>
        <if test='costType == "STD"'>
            <choose>
                <when test='granularityType == "IRB"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_irb_top_spart_info_t
                </when>
                <when test='granularityType == "INDUS"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_indus_top_spart_info_t
                </when>
                <when test='granularityType == "PROD"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_top_spart_info_t
                </when>
            </choose>
        </if>
        WHERE
        double_flag = 'Y'
        AND is_top_flag = 'Y'
        <include refid="searchFieldsExport"/>
        ) d2 on
        d1.top_spart_code = d2.top_spart_code
        and d1.top_spart_cn_name = d2.top_spart_cn_name
        <choose>
            <when test='granularityType == "IRB"'>
                AND nvl(d1.lv0_prod_rnd_team_code,'snull') = nvl(d2.lv0_prod_rnd_team_code,'snull')
                AND nvl(d1.lv1_prod_rnd_team_code,'snull') = nvl(d2.lv1_prod_rnd_team_code,'snull')
                AND nvl(d1.lv2_prod_rnd_team_code,'snull') = nvl(d2.lv2_prod_rnd_team_code,'snull')
                AND nvl(d1.lv3_prod_rnd_team_code,'snull') = nvl(d2.lv3_prod_rnd_team_code,'snull')
                AND nvl(d1.lv4_prod_rnd_team_code,'snull') = nvl(d2.lv4_prod_rnd_team_code,'snull')
            </when>
            <when test='granularityType == "INDUS"'>
                AND nvl(d1.lv0_industry_catg_code,'snull') = nvl(d2.lv0_industry_catg_code,'snull')
                AND nvl(d1.lv1_industry_catg_code,'snull') = nvl(d2.lv1_industry_catg_code,'snull')
                AND nvl(d1.lv2_industry_catg_code,'snull') = nvl(d2.lv2_industry_catg_code,'snull')
                AND nvl(d1.lv3_industry_catg_code,'snull') = nvl(d2.lv3_industry_catg_code,'snull')
                AND nvl(d1.lv4_industry_catg_code,'snull') = nvl(d2.lv4_industry_catg_code,'snull')
            </when>
            <when test='granularityType == "PROD"'>
                AND nvl(d1.lv0_prod_list_code,'snull') = nvl(d2.lv0_prod_list_code,'snull')
                AND nvl(d1.lv1_prod_list_code,'snull') = nvl(d2.lv1_prod_list_code,'snull')
                AND nvl(d1.lv2_prod_list_code,'snull') = nvl(d2.lv2_prod_list_code,'snull')
                AND nvl(d1.lv3_prod_list_code,'snull') = nvl(d2.lv3_prod_list_code,'snull')
                AND nvl(d1.lv4_prod_list_code,'snull') = nvl(d2.lv4_prod_list_code,'snull')
            </when>
        </choose>
        ORDER BY
        if(isnull(d1.weight4),1,0),d1.weight4 DESC
    </select>

    <select id="findTopSpartAllCount" resultType="int">
        SELECT COUNT(1) from
        (  SELECT
        <choose>
            <when test='granularityType == "IRB"'>
                lv0_prod_rd_team_cn_name,
                lv0_prod_rnd_team_code,
                lv1_prod_rd_team_cn_name,
                lv1_prod_rnd_team_code,
                lv2_prod_rd_team_cn_name,
                lv2_prod_rnd_team_code,
                lv3_prod_rd_team_cn_name,
                lv3_prod_rnd_team_code,
                lv4_prod_rd_team_cn_name,
                lv4_prod_rnd_team_code,
            </when>
            <when test='granularityType == "INDUS"'>
                lv0_industry_catg_code,
                lv0_industry_catg_cn_name,
                lv1_industry_catg_code,
                lv1_industry_catg_cn_name,
                lv2_industry_catg_code,
                lv2_industry_catg_cn_name,
                lv3_industry_catg_code,
                lv3_industry_catg_cn_name,
                lv4_industry_catg_code,
                lv4_industry_catg_cn_name,
            </when>
            <when test='granularityType == "PROD"'>
                lv0_prod_list_code,
                lv0_prod_list_cn_name,
                lv1_prod_list_code,
                lv1_prod_list_cn_name,
                lv2_prod_list_code,
                lv2_prod_list_cn_name,
                lv3_prod_list_code,
                lv3_prod_list_cn_name,
                lv4_prod_list_code,
                lv4_prod_list_cn_name,
            </when>
        </choose>
        top_spart_cn_name,
        top_spart_code,
        region_code,
        region_cn_name,
        repoffice_code,
        repoffice_cn_name,
        bg_code,
        bg_cn_name,
        oversea_flag,
        SUM ( CASE WHEN period_year = #{periodYear0} THEN  weight_rate END ) AS weight0,
        SUM ( CASE WHEN period_year = #{periodYear1} THEN  weight_rate END ) AS weight1,
        SUM ( CASE WHEN period_year = #{periodYear2} THEN  weight_rate END ) AS weight2,
        SUM ( CASE WHEN period_year = #{periodYear3} THEN  weight_rate END ) AS weight3,
        SUM ( CASE WHEN period_year = #{periodYear4} THEN  weight_rate END ) AS weight4
        <if test='costType == "PSP"'>
            <choose>
                <when test='granularityType == "IRB"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_irb_top_spart_info_t
                </when>
                <when test='granularityType == "INDUS"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_indus_top_spart_info_t
                </when>
                <when test='granularityType == "PROD"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_top_spart_info_t
                </when>
            </choose>
        </if>
        <if test='costType == "STD"'>
            <choose>
                <when test='granularityType == "IRB"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_irb_top_spart_info_t
                </when>
                <when test='granularityType == "INDUS"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_indus_top_spart_info_t
                </when>
                <when test='granularityType == "PROD"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_top_spart_info_t
                </when>
            </choose>
        </if>
        where 1=1
        <include refid="searchFieldsExport"/>
        GROUP BY
        <choose>
            <when test='granularityType == "IRB"'>
                lv0_prod_rd_team_cn_name,
                lv0_prod_rnd_team_code,
                lv1_prod_rd_team_cn_name,
                lv1_prod_rnd_team_code,
                lv2_prod_rd_team_cn_name,
                lv2_prod_rnd_team_code,
                lv3_prod_rd_team_cn_name,
                lv3_prod_rnd_team_code,
                lv4_prod_rd_team_cn_name,
                lv4_prod_rnd_team_code,
            </when>
            <when test='granularityType == "INDUS"'>
                lv0_industry_catg_code,
                lv0_industry_catg_cn_name,
                lv1_industry_catg_code,
                lv1_industry_catg_cn_name,
                lv2_industry_catg_code,
                lv2_industry_catg_cn_name,
                lv3_industry_catg_code,
                lv3_industry_catg_cn_name,
                lv4_industry_catg_code,
                lv4_industry_catg_cn_name,
            </when>
            <when test='granularityType == "PROD"'>
                lv0_prod_list_code,
                lv0_prod_list_cn_name,
                lv1_prod_list_code,
                lv1_prod_list_cn_name,
                lv2_prod_list_code,
                lv2_prod_list_cn_name,
                lv3_prod_list_code,
                lv3_prod_list_cn_name,
                lv4_prod_list_code,
                lv4_prod_list_cn_name,
            </when>
        </choose>
        top_spart_code,
        top_spart_cn_name,
        region_code,
        region_cn_name,
        repoffice_code,
        repoffice_cn_name,
        bg_code,
        bg_cn_name,
        oversea_flag)d
    </select>
</mapper>
