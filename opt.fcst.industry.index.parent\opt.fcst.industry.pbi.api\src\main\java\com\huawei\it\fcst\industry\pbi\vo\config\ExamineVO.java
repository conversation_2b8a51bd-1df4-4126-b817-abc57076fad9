/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.config;

import lombok.Builder;
import lombok.Data;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * ExamineVO Class
 *
 * <AUTHOR>
 * @since 2024/9/11
 */
@Data
@Builder
public class ExamineVO {

    private Map<String, String> lv1ProdRndTeamMap = new HashMap<>();
    private Map<String, String> lv2ProdRndTeamMap = new HashMap<>();

    private Map<String, String> lv3ProdRndTeamMap = new HashMap<>();

    private Map<String, String> lv4ProdRndTeamMap = new HashMap<>();

    private Map<String, String> lv1IndustryCatgMap = new HashMap<>();

    private Map<String, String> lv2IndustryCatgMap = new HashMap<>();

    private Map<String, String> lv3IndustryCatgMap = new HashMap<>();

    private Map<String, String> lv4IndustryCatgMap = new HashMap<>();

    private Map<String, String> lv1ProdListMap = new HashMap<>();

    private Map<String, String> lv2ProdListMap = new HashMap<>();

    private Map<String, String> lv3ProdListMap = new HashMap<>();

    private Map<String, String> lv4ProdListMap = new HashMap<>();

    private Map<String, String> bgMap = new HashMap<>();

    private Map<String, String> regionMap = new HashMap<>();

    private Map<String, String> repofficeMap = new HashMap<>();

    private Map<String, String> overseaFlagMap = new HashMap<>();

    private Set<String> pspSpartCodeSet = new HashSet<>();

    private Set<String> stdSpartCodeSet = new HashSet<>();

    private Set<String> pspHwContractNumSet = new HashSet<>();

    private Set<String> stdHwContractNumSet = new HashSet<>();

    private Long versionId;

    private Long annualVersionId;

    private Long spartCont;

    private Long contractNumberCont;

    private Long begin;

    private Long end;

}
