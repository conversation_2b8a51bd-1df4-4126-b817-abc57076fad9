/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.combination;

import com.huawei.it.fcst.industry.index.dao.IDmFocCustomCombDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocEnergyCustomCombDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocEnergyMadeCustomCombDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeCustomCombDao;
import com.huawei.it.fcst.industry.index.vo.combination.CombTransformVO;
import com.huawei.it.fcst.industry.index.vo.combination.CombinationVO;
import com.huawei.it.fcst.industry.index.vo.combination.DmCustomCombVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

/**
 * AsyncCustomServiceTest Class
 *
 * <AUTHOR>
 * @since 2023/11/10
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class AsyncCustomServiceTest {

    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncCustomServiceTest.class);

    @InjectMocks
    private AsyncCustomService asyncCustomService;

    @Mock
    private IDmFocCustomCombDao dmFocCustomCombDao;

    @Mock
    private IDmFocMadeCustomCombDao dmFocMadeCustomCombDao;

    @Mock
    private IDmFocEnergyCustomCombDao dmFocEnergyCustomCombDao;

    @Mock
    private IDmFocEnergyMadeCustomCombDao dmFocEnergyMadeCustomCombDao;

    @Test
    public void granularityTypePageConditionTest() throws Exception {
        List<DmCustomCombVO> allDmCustomCombList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setCaliberFlag("C");
        dmCustomCombVO.setCustomId(11L);
        dmCustomCombVO.setOverseaFlag("O");
        dmCustomCombVO.setViewFlag("3");
        dmCustomCombVO.setLv0ProdListCode("BG");
        dmCustomCombVO.setPageFlag("ALL_ANNUAL");
        dmCustomCombVO.setGroupLevel("LV2");
        dmCustomCombVO.setLv1ProdRndTeamCode("111");
        dmCustomCombVO.setLv2ProdRndTeamCode("222");
        dmCustomCombVO.setLv3ProdRndTeamCode("333");
        dmCustomCombVO.setConnectCode("221111");
        allDmCustomCombList.add(dmCustomCombVO);
        CombinationVO combination = new CombinationVO();
        combination.setCostType("P");
        queryDbForCostType(allDmCustomCombList, combination);

        CombinationVO combination2 = new CombinationVO();
        combination2.setCostType("M");
        queryDbForCostType(allDmCustomCombList, combination2);
        Assert.assertTrue(true);
    }

    private void queryDbForCostType(List<DmCustomCombVO> allDmCustomCombList, CombinationVO combination) throws InterruptedException {
        String granularityTypePageFlag = "U_ANNUAL";
        asyncCustomService.granularityTypePageCondition(allDmCustomCombList, combination, granularityTypePageFlag);

        String granularityType2PageFlag = "U_MONTH";
        asyncCustomService.granularityTypePageCondition(allDmCustomCombList, combination, granularityType2PageFlag);

        String granularityType3PageFlag = "P_MONTH";
        asyncCustomService.granularityTypePageCondition(allDmCustomCombList, combination, granularityType3PageFlag);

        String granularityType4PageFlag = "P_ANNUAL";
        asyncCustomService.granularityTypePageCondition(allDmCustomCombList, combination, granularityType4PageFlag);

        String granularityType5PageFlag = "D_MONTH";
        asyncCustomService.granularityTypePageCondition(allDmCustomCombList, combination, granularityType5PageFlag);

        String granularityType6PageFlag = "D_ANNUAL";
        asyncCustomService.granularityTypePageCondition(allDmCustomCombList, combination, granularityType6PageFlag);
    }

    @Test
    public void granularityTypePageCondition2Test() throws Exception {
        List<DmCustomCombVO> allDmCustomCombList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setCaliberFlag("C");
        dmCustomCombVO.setCustomId(11L);
        dmCustomCombVO.setOverseaFlag("O");
        dmCustomCombVO.setViewFlag("3");
        dmCustomCombVO.setLv0ProdListCode("BG");
        dmCustomCombVO.setPageFlag("ALL_ANNUAL");
        dmCustomCombVO.setGroupLevel("LV2");
        dmCustomCombVO.setLv1ProdRndTeamCode("111");
        dmCustomCombVO.setLv2ProdRndTeamCode("222");
        dmCustomCombVO.setLv3ProdRndTeamCode("333");
        dmCustomCombVO.setConnectCode("221111");
        allDmCustomCombList.add(dmCustomCombVO);
        CombinationVO combination = new CombinationVO();
        List<DmCustomCombVO> groupCodeForGeneralAnnualList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO1 = new DmCustomCombVO();
        dmCustomCombVO1.setConnectCode("221122113");
        groupCodeForGeneralAnnualList.add(dmCustomCombVO1);
        Mockito.when(dmFocCustomCombDao.groupCodeForGeneralAnnualList(combination)).thenReturn(groupCodeForGeneralAnnualList);

        List<DmCustomCombVO> groupCodeForGeneral2AnnualList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO2 = new DmCustomCombVO();
        dmCustomCombVO2.setConnectCode("221111");
        groupCodeForGeneralAnnualList.add(dmCustomCombVO2);
        Mockito.when(dmFocMadeCustomCombDao.groupCodeForManuGeneralAnnualList(combination)).thenReturn(groupCodeForGeneral2AnnualList);

        combination.setCostType("P");
        String granularityTypePageFlag = "U_ANNUAL";
        asyncCustomService.granularityTypePageCondition(allDmCustomCombList, combination, granularityTypePageFlag);

        CombinationVO combination2 = new CombinationVO();
        combination2.setCostType("M");
        String granularityTypePage2Flag = "U_ANNUAL";
        asyncCustomService.granularityTypePageCondition(allDmCustomCombList, combination, granularityTypePage2Flag);
        Assert.assertTrue(true);
    }

    @Test
    public void granularityTypePageCondition3Test() throws Exception {
        List<DmCustomCombVO> allDmCustomCombList = new ArrayList<>();
        CombinationVO combination = new CombinationVO();
        String granularityTypePageFlag = "";
        Whitebox.invokeMethod(asyncCustomService, "granularityTypePageCondition", allDmCustomCombList, combination,granularityTypePageFlag);
        Assert.assertTrue(true);
    }

    @Test
    public void methodGetViewInfoListTest() throws Exception {
        String granularityTypePageFlag = "D_ANNUAL";
        List<DmCustomCombVO> allGroupCodeList = new ArrayList<>();
        CombinationVO combinationVO = new CombinationVO();
        combinationVO.setIndustryOrg("ICT");
        Whitebox.invokeMethod(asyncCustomService, "getViewInfoList", granularityTypePageFlag,allGroupCodeList,combinationVO);
        String granularityTypePageFlag2 = "D_MONTH";
        Whitebox.invokeMethod(asyncCustomService, "getViewInfoList", granularityTypePageFlag2,allGroupCodeList,combinationVO);

        Whitebox.invokeMethod(asyncCustomService, "getViewInfoList", "D_MONTH1",allGroupCodeList,combinationVO);

        Whitebox.invokeMethod(asyncCustomService, "getManufacutreViewInfoList", granularityTypePageFlag,allGroupCodeList,combinationVO);
        Whitebox.invokeMethod(asyncCustomService, "getManufacutreViewInfoList", granularityTypePageFlag2,allGroupCodeList,combinationVO);
        Whitebox.invokeMethod(asyncCustomService, "getManufacutreViewInfoList", "D_MONTH1",allGroupCodeList,combinationVO);
        Assert.assertNull(null);
    }

    @Test
    public void callCustomFcnctionTest() throws Exception {
        CombTransformVO combTransformVO = new CombTransformVO();
        String cusViewAnnualFlag = "SUCCESS";

        String cusFailAnnualFlag = "0";
        PowerMockito.doReturn(cusViewAnnualFlag).when(dmFocCustomCombDao).cusViewAnnualCost(any());

        PowerMockito.doReturn(cusViewAnnualFlag).when(dmFocCustomCombDao).customAnnual(any());
        asyncCustomService.callCustomAnnual(combTransformVO);
        PowerMockito.doReturn(cusFailAnnualFlag).when(dmFocCustomCombDao).cusViewAnnualCost(any());
        try {
            asyncCustomService.callCustomAnnual(combTransformVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("函数失败");
        }
        Assert.assertTrue(true);
    }

    @Test
    public void callCustomFcnction2Test() throws Exception {
        CombTransformVO combTransformVO = new CombTransformVO();

        String cusFailAnnualFlag = "0";
        String cusViewAnnualFlag = "SUCCESS";

        PowerMockito.doReturn(cusViewAnnualFlag).when(dmFocCustomCombDao).cusViewAnnualCost(any());
        PowerMockito.doReturn(cusFailAnnualFlag).when(dmFocCustomCombDao).customAnnual(any());
        try {
            asyncCustomService.callCustomAnnual(combTransformVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("函数失败");
        }
        Assert.assertTrue(true);
    }

    @Test
    public void callCustomFcnction3Test() throws Exception {
        CombTransformVO combTransformVO = new CombTransformVO();
        String cusViewFlag = "SUCCESS";
        String cusFailFlag = "0";

        PowerMockito.doReturn(cusViewFlag).when(dmFocCustomCombDao).cusItemDtlDecode(any());
        PowerMockito.doReturn(cusViewFlag).when(dmFocCustomCombDao).cusItemAppend(any());
        PowerMockito.doReturn(cusViewFlag).when(dmFocCustomCombDao).customMonthComb(any());
        asyncCustomService.callCustomMonth(combTransformVO);

        PowerMockito.doReturn(cusFailFlag).when(dmFocCustomCombDao).cusItemDtlDecode(any());

        try {
            asyncCustomService.callCustomMonth(combTransformVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("函数失败");
        }
        Assert.assertTrue(true);
    }

    @Test
    public void callCustomFcnction4Test() throws Exception {
        CombTransformVO combTransformVO = new CombTransformVO();
        String cusViewFlag = "SUCCESS";
        String cusFailFlag = "0";

        PowerMockito.doReturn(cusViewFlag).when(dmFocCustomCombDao).cusItemDtlDecode(any());
        PowerMockito.doReturn(cusViewFlag).when(dmFocCustomCombDao).cusItemAppend(any());
        PowerMockito.doReturn(cusViewFlag).when(dmFocCustomCombDao).customMonthComb(any());
        asyncCustomService.callCustomMonth(combTransformVO);

        PowerMockito.doReturn(cusFailFlag).when(dmFocCustomCombDao).cusItemAppend(any());

        try {
            asyncCustomService.callCustomMonth(combTransformVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("函数失败");
        }
        Assert.assertTrue(true);
    }

    @Test
    public void callCustomFcnction5Test() throws Exception {
        CombTransformVO combTransformVO = new CombTransformVO();
        String cusViewFlag = "SUCCESS";
        String cusFailFlag = "0";

        PowerMockito.doReturn(cusViewFlag).when(dmFocCustomCombDao).cusItemDtlDecode(any());
        PowerMockito.doReturn(cusViewFlag).when(dmFocCustomCombDao).cusItemAppend(any());
        PowerMockito.doReturn(cusViewFlag).when(dmFocCustomCombDao).customMonthComb(any());
        asyncCustomService.callCustomMonth(combTransformVO);

        PowerMockito.doReturn(cusFailFlag).when(dmFocCustomCombDao).customMonthComb(any());
        try {
            asyncCustomService.callCustomMonth(combTransformVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("函数失败");
        }
        Assert.assertTrue(true);
    }

    @Test
    public void callMadeCustomAnnual1Test() throws Exception {
        CombTransformVO combTransformVO = new CombTransformVO();
        String cusViewFlag = "SUCCESS";
        String cusFailFlag = "0";

        PowerMockito.doReturn(cusViewFlag).when(dmFocMadeCustomCombDao).cusMadeViewAnnualCost(any());
        PowerMockito.doReturn(cusViewFlag).when(dmFocMadeCustomCombDao).customMadeAnnual(any());
        asyncCustomService.callMadeCustomAnnual(combTransformVO);

        PowerMockito.doReturn(cusFailFlag).when(dmFocMadeCustomCombDao).cusMadeViewAnnualCost(any());
        try {
            asyncCustomService.callMadeCustomAnnual(combTransformVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("函数失败");
        }
        Assert.assertTrue(true);
    }

    @Test
    public void callMadeCustomAnnual2Test() throws Exception {
        CombTransformVO combTransformVO = new CombTransformVO();
        String cusViewFlag = "SUCCESS";
        String cusFailFlag = "0";

        PowerMockito.doReturn(cusViewFlag).when(dmFocMadeCustomCombDao).cusMadeViewAnnualCost(any());

        PowerMockito.doReturn(cusFailFlag).when(dmFocMadeCustomCombDao).customMadeAnnual(any());
        try {
            asyncCustomService.callMadeCustomAnnual(combTransformVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("函数失败");
        }
        Assert.assertTrue(true);
    }

    @Test
    public void callMadeCustomMonthTest() throws Exception {
        CombTransformVO combTransformVO = new CombTransformVO();
        String cusViewFlag = "SUCCESS";

        PowerMockito.doReturn(cusViewFlag).when(dmFocMadeCustomCombDao).cusMadeItemDtlDecode(any());
        PowerMockito.doReturn(cusViewFlag).when(dmFocMadeCustomCombDao).cusMadeItemAppend(any());
        PowerMockito.doReturn(cusViewFlag).when(dmFocMadeCustomCombDao).customMadeMonthComb(any());
        asyncCustomService.callMadeCustomMonth(combTransformVO);

        Assert.assertTrue(true);
    }

    @Test
    public void callMadeCustomMonth2Test() throws Exception {
        CombTransformVO combTransformVO = new CombTransformVO();
        String cusFailViewFlag = "0";

        PowerMockito.doReturn(cusFailViewFlag).when(dmFocMadeCustomCombDao).cusMadeItemDtlDecode(any());

        try {
            asyncCustomService.callMadeCustomMonth(combTransformVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("函数失败");
        }
        Assert.assertTrue(true);
    }

    @Test
    public void callMadeCustomMonth3Test() throws Exception {
        CombTransformVO combTransformVO = new CombTransformVO();
        String cusFailViewFlag = "0";
        String cusViewFlag = "SUCCESS";

        PowerMockito.doReturn(cusViewFlag).when(dmFocMadeCustomCombDao).cusMadeItemDtlDecode(any());
        PowerMockito.doReturn(cusViewFlag).when(dmFocMadeCustomCombDao).cusMadeItemAppend(any());
        PowerMockito.doReturn(cusFailViewFlag).when(dmFocMadeCustomCombDao).customMadeMonthComb(any());

        try {
            asyncCustomService.callMadeCustomMonth(combTransformVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("函数失败");
        }
        Assert.assertTrue(true);
    }

    @Test
    public void callMadeCustomMonth4Test() throws Exception {
        CombTransformVO combTransformVO = new CombTransformVO();
        String cusFailViewFlag = "0";
        String cusViewFlag = "SUCCESS";

        PowerMockito.doReturn(cusViewFlag).when(dmFocMadeCustomCombDao).cusMadeItemDtlDecode(any());
        PowerMockito.doReturn(cusFailViewFlag).when(dmFocMadeCustomCombDao).cusMadeItemAppend(any());

        try {
            asyncCustomService.callMadeCustomMonth(combTransformVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("函数失败");
        }
        Assert.assertTrue(true);
    }



}