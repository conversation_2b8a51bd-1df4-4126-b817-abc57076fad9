/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.service.common;

import com.huawei.it.fcst.industry.price.vo.common.CommonPriceBaseVO;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import io.swagger.annotations.Api;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * <AUTHOR>
 * @since 2024/11/7
 */
@Path("/dropDown")
@Api(value = "定价指数-下拉框接口")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface IDropDownCommonService {

    /**
     * BG下拉框
     *
     * @param commonBaseVO 参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/bgInfo/list")
    ResultDataVO getBgInfoList(CommonPriceBaseVO commonBaseVO);

    /**
     * 国内/海外
     *
     * @param commonBaseVO 参数
     * @return ResultDataVO ResultDataVO
     * @throws ApplicationException Application Exception
     */
    @POST
    @Path("/overseaFlag/list")
    ResultDataVO overseaFlagList(CommonPriceBaseVO commonBaseVO) throws ApplicationException;

    /**
     * 地区部
     *
     * @param commonBaseVO 参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/regionCode/list")
    ResultDataVO regionCodeList(CommonPriceBaseVO commonBaseVO);

    /**
     * 代表处
     *
     * @param commonBaseVO 参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/repofficeCode/list")
    ResultDataVO repofficeCodeList(CommonPriceBaseVO commonBaseVO);

    /**
     * 大T系统部
     *
     * @param commonBaseVO 参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/keyAccountDept/list")
    ResultDataVO keyAccountDeptList(CommonPriceBaseVO commonBaseVO);

    /**
     * 子网系统部下拉框
     *
     * @param commonBaseVO 参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/subAccountDept/list")
    ResultDataVO subAccountDeptList(CommonPriceBaseVO commonBaseVO);

    /**
     * 年度月度各层级下拉框
     *
     * @param commonBaseVO 参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/list")
    ResultDataVO dropDownList(CommonPriceBaseVO commonBaseVO);

    /**
     * 获取lv0编码
     *
     * @return String
     */
    @POST
    @Path("/lv0/code")
    ResultDataVO lv0CodeDropdown(CommonPriceBaseVO commonViewVO);

}
