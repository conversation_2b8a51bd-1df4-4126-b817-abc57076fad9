/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.combination;

import static org.mockito.ArgumentMatchers.any;

import com.huawei.it.fcst.industry.index.dao.IDmFocCustomCombDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocEnergyCustomCombDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocEnergyMadeCustomCombDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeCustomCombDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTotalCustomCombDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocVersionDao;
import com.huawei.it.fcst.industry.index.impl.annual.AnnualCommonService;
import com.huawei.it.fcst.industry.index.impl.common.CommonService;
import com.huawei.it.fcst.industry.index.vo.combination.CombinationVO;
import com.huawei.it.fcst.industry.index.vo.combination.DmCustomCombVO;
import com.huawei.it.fcst.industry.index.vo.common.CommonViewVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoDTO;

import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * CustomCommonServiceTest Class
 *
 * <AUTHOR>
 * @since 2023/9/19
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class CustomCommonServiceTest {
    private static final Logger LOGGER = LoggerFactory.getLogger(CustomCommonService.class);

    @InjectMocks
    private CustomCommonService customCommonService;

    @Mock
    private IDmFocCustomCombDao dmFocCustomCombDao;

    @Mock
    private CommonService commonService;

    @Mock
    private IDmFocVersionDao dmFocVersionDao;

    @Mock
    private AnnualCommonService annualCommonService;

    @Mock
    private IDmFocMadeCustomCombDao dmFocMadeCustomCombDao;

    @Mock
    private IDmFocEnergyMadeCustomCombDao dmFocEnergyMadeCustomCombDao;

    @Mock
    private IDmFocTotalCustomCombDao dmFocTotalCustomCombDao;

    @Mock
    private IDmFocEnergyCustomCombDao dmFocEnergyCustomCombDao;

    @Test
    public void filterAnotherPageData() {
        CombinationVO combinationVO=new CombinationVO();
        List<DmCustomCombVO > customVOList=new ArrayList<>();
        combinationVO.setPageFlag("1");
        combinationVO.setGranularityType("U");
        combinationVO.setPageSymbol("ANNUAL");

        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setGroupLevel("LV1");
        dmCustomCombVO.setGroupCode("1165D");
        dmCustomCombVO.setConnectCode("122552446");
        DmCustomCombVO dmCustomCombVO2 = new DmCustomCombVO();
        dmCustomCombVO2.setGroupLevel("LV2");
        dmCustomCombVO2.setGroupCode("1165D");
        dmCustomCombVO2.setConnectCode("12552446");
        DmCustomCombVO dmCustomCombVO3 = new DmCustomCombVO();
        dmCustomCombVO3.setGroupLevel("LV3");
        dmCustomCombVO3.setGroupCode("1165D");
        dmCustomCombVO3.setConnectCode("125446");
        DmCustomCombVO dmCustomCombVO4 = new DmCustomCombVO();
        dmCustomCombVO4.setGroupLevel("L1");
        dmCustomCombVO4.setGroupCode("1165D");
        dmCustomCombVO4.setConnectCode("224666");
        DmCustomCombVO dmCustomCombVO5 = new DmCustomCombVO();
        dmCustomCombVO5.setGroupLevel("L2");
        dmCustomCombVO5.setGroupCode("1165D");
        dmCustomCombVO5.setConnectCode("234422");
        DmCustomCombVO dmCustomCombVO6 = new DmCustomCombVO();
        dmCustomCombVO6.setGroupLevel("DIMENSION");
        dmCustomCombVO6.setGroupCode("1165D");
        dmCustomCombVO6.setConnectCode("2223");
        DmCustomCombVO dmCustomCombVO7 = new DmCustomCombVO();
        dmCustomCombVO7.setGroupLevel("SUBCATEGORY");
        dmCustomCombVO7.setGroupCode("1165D");
        dmCustomCombVO7.setConnectCode("33222");
        DmCustomCombVO dmCustomCombVO8 = new DmCustomCombVO();
        dmCustomCombVO8.setGroupLevel("SUB_DETAIL");
        dmCustomCombVO8.setGroupCode("1165D");
        dmCustomCombVO8.setConnectCode("112223");
        customVOList.add(dmCustomCombVO);
        customVOList.add(dmCustomCombVO2);
        customVOList.add(dmCustomCombVO3);
        customVOList.add(dmCustomCombVO4);
        customVOList.add(dmCustomCombVO5);
        customVOList.add(dmCustomCombVO6);
        customVOList.add(dmCustomCombVO7);
        customVOList.add(dmCustomCombVO8);

        DmFocVersionInfoDTO combinationVO2=new DmFocVersionInfoDTO();
        combinationVO2.setVersionId(188L);
        List<DmFocViewInfoVO> dmGroupCodeList=new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmCustomCombVO.setCustomId(154L);
        dmCustomCombVO.setConnectCode("11122");
        dmGroupCodeList.add(dmFocViewInfoVO);

        Mockito.when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(combinationVO2);
        Mockito.when(dmFocCustomCombDao.prodTeamCodeForGeneralMonth(any())).thenReturn(dmGroupCodeList);
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(combinationVO.getCostType(),combinationVO.getIndustryOrg());
        List<DmCustomCombVO> dmCustomCombVOS = customCommonService.filterAnotherPageData(combinationVO, customVOList);
        Assert.assertNotNull(dmCustomCombVOS);

    }

    @Test
    public void filterAnotherPageData2Test() {
        CombinationVO combinationVO=new CombinationVO();
        List<DmCustomCombVO > customVOList=new ArrayList<>();
        combinationVO.setPageFlag("1");
        combinationVO.setGranularityType("U");
        combinationVO.setPageSymbol("MONTH");
        Set<String> set = new HashSet<>();
        set.add("NO_PERMISSION");
        combinationVO.setLv0DimensionSet(set);

        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setGroupLevel("LV1");
        dmCustomCombVO.setGroupCode("1165D");
        dmCustomCombVO.setCustomId(154L);
        dmCustomCombVO.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO2 = new DmCustomCombVO();
        dmCustomCombVO2.setGroupLevel("LV2");
        dmCustomCombVO2.setGroupCode("1165D");
        dmCustomCombVO2.setCustomId(154L);
        dmCustomCombVO2.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO3 = new DmCustomCombVO();
        dmCustomCombVO3.setGroupLevel("LV3");
        dmCustomCombVO3.setGroupCode("1165D");
        dmCustomCombVO3.setCustomId(154L);
        dmCustomCombVO3.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO4 = new DmCustomCombVO();
        dmCustomCombVO4.setGroupLevel("L1");
        dmCustomCombVO4.setGroupCode("1165D");
        dmCustomCombVO4.setCustomId(154L);
        dmCustomCombVO4.setConnectCode("11126");
        DmCustomCombVO dmCustomCombVO5 = new DmCustomCombVO();
        dmCustomCombVO5.setGroupLevel("L2");
        dmCustomCombVO5.setGroupCode("1165D");
        dmCustomCombVO5.setCustomId(154L);
        dmCustomCombVO5.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO6 = new DmCustomCombVO();
        dmCustomCombVO6.setGroupLevel("DIMENSION");
        dmCustomCombVO6.setGroupCode("1165D");
        dmCustomCombVO6.setCustomId(154L);
        dmCustomCombVO6.setConnectCode("11129");
        DmCustomCombVO dmCustomCombVO7 = new DmCustomCombVO();
        dmCustomCombVO7.setGroupLevel("SUBCATEGORY");
        dmCustomCombVO7.setGroupCode("1165D");
        dmCustomCombVO7.setCustomId(154L);
        dmCustomCombVO7.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO8 = new DmCustomCombVO();
        dmCustomCombVO8.setGroupLevel("SUB_DETAIL");
        dmCustomCombVO8.setGroupCode("1165D");
        dmCustomCombVO8.setCustomId(154L);
        dmCustomCombVO8.setConnectCode("11128");
        customVOList.add(dmCustomCombVO);
        customVOList.add(dmCustomCombVO2);
        customVOList.add(dmCustomCombVO3);
        customVOList.add(dmCustomCombVO4);
        customVOList.add(dmCustomCombVO5);
        customVOList.add(dmCustomCombVO6);
        customVOList.add(dmCustomCombVO7);
        customVOList.add(dmCustomCombVO8);

        DmFocVersionInfoDTO combinationVO2=new DmFocVersionInfoDTO();
        combinationVO2.setVersionId(188L);
        List<DmFocViewInfoVO> dmGroupCodeList=new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();

        dmGroupCodeList.add(dmFocViewInfoVO);
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(combinationVO.getCostType(),combinationVO.getIndustryOrg());
        Mockito.when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(combinationVO2);
        Mockito.when(dmFocCustomCombDao.prodTeamCodeForGeneralMonth(any())).thenReturn(dmGroupCodeList);

        List<DmCustomCombVO> dmCustomCombVOS = customCommonService.filterAnotherPageData(combinationVO, customVOList);
        Assert.assertNotNull(dmCustomCombVOS);

    }

    @Test
    public void filterAnotherPageData3Test() {
        CombinationVO combinationVO=new CombinationVO();
        List<DmCustomCombVO > customVOList=new ArrayList<>();
        combinationVO.setPageFlag("1");
        combinationVO.setGranularityType("P");
        combinationVO.setPageSymbol("MONTH");
        Set<String> set = new HashSet<>();
        set.add("NO_PERMISSION");
        combinationVO.setLv0DimensionSet(set);

        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setGroupLevel("LV1");
        dmCustomCombVO.setGroupCode("1165D");
        dmCustomCombVO.setCustomId(154L);
        dmCustomCombVO.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO2 = new DmCustomCombVO();
        dmCustomCombVO2.setGroupLevel("LV2");
        dmCustomCombVO2.setGroupCode("1165D");
        dmCustomCombVO2.setCustomId(154L);
        dmCustomCombVO2.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO3 = new DmCustomCombVO();
        dmCustomCombVO3.setGroupLevel("LV3");
        dmCustomCombVO3.setGroupCode("1165D");
        dmCustomCombVO3.setCustomId(154L);
        dmCustomCombVO3.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO4 = new DmCustomCombVO();
        dmCustomCombVO4.setGroupLevel("L1");
        dmCustomCombVO4.setGroupCode("1165D");
        dmCustomCombVO4.setCustomId(154L);
        dmCustomCombVO4.setConnectCode("11126");
        DmCustomCombVO dmCustomCombVO5 = new DmCustomCombVO();
        dmCustomCombVO5.setGroupLevel("L2");
        dmCustomCombVO5.setGroupCode("1165D");
        dmCustomCombVO5.setCustomId(154L);
        dmCustomCombVO5.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO6 = new DmCustomCombVO();
        dmCustomCombVO6.setGroupLevel("DIMENSION");
        dmCustomCombVO6.setGroupCode("1165D");
        dmCustomCombVO6.setCustomId(154L);
        dmCustomCombVO6.setConnectCode("11129");
        DmCustomCombVO dmCustomCombVO7 = new DmCustomCombVO();
        dmCustomCombVO7.setGroupLevel("SUBCATEGORY");
        dmCustomCombVO7.setGroupCode("1165D");
        dmCustomCombVO7.setCustomId(154L);
        dmCustomCombVO7.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO8 = new DmCustomCombVO();
        dmCustomCombVO8.setGroupLevel("SUB_DETAIL");
        dmCustomCombVO8.setGroupCode("1165D");
        dmCustomCombVO8.setCustomId(154L);
        dmCustomCombVO8.setConnectCode("11128");
        customVOList.add(dmCustomCombVO);
        customVOList.add(dmCustomCombVO2);
        customVOList.add(dmCustomCombVO3);
        customVOList.add(dmCustomCombVO4);
        customVOList.add(dmCustomCombVO5);
        customVOList.add(dmCustomCombVO6);
        customVOList.add(dmCustomCombVO7);
        customVOList.add(dmCustomCombVO8);

        DmFocVersionInfoDTO combinationVO2=new DmFocVersionInfoDTO();
        combinationVO2.setVersionId(188L);
        List<DmFocViewInfoVO> dmGroupCodeList=new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();

        dmGroupCodeList.add(dmFocViewInfoVO);
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(combinationVO.getCostType(),combinationVO.getIndustryOrg());
        Mockito.when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(combinationVO2);
        Mockito.when(dmFocCustomCombDao.prodTeamCodeForGeneralMonth(any())).thenReturn(dmGroupCodeList);

        List<DmCustomCombVO> dmCustomCombVOS = customCommonService.filterAnotherPageData(combinationVO, customVOList);
        Assert.assertNotNull(dmCustomCombVOS);

    }

    @Test
    public void filterAnotherPageData4Test() {
        CombinationVO combinationVO=new CombinationVO();
        List<DmCustomCombVO > customVOList=new ArrayList<>();
        combinationVO.setPageFlag("1");
        combinationVO.setGranularityType("P");
        combinationVO.setPageSymbol("ANNUAL");
        Set<String> set = new HashSet<>();
        set.add("NO_PERMISSION");
        combinationVO.setLv0DimensionSet(set);

        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setGroupLevel("LV1");
        dmCustomCombVO.setGroupCode("1165D");
        dmCustomCombVO.setCustomId(154L);
        dmCustomCombVO.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO2 = new DmCustomCombVO();
        dmCustomCombVO2.setGroupLevel("LV2");
        dmCustomCombVO2.setGroupCode("1165D");
        dmCustomCombVO2.setCustomId(154L);
        dmCustomCombVO2.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO3 = new DmCustomCombVO();
        dmCustomCombVO3.setGroupLevel("LV3");
        dmCustomCombVO3.setGroupCode("1165D");
        dmCustomCombVO3.setCustomId(154L);
        dmCustomCombVO3.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO4 = new DmCustomCombVO();
        dmCustomCombVO4.setGroupLevel("L1");
        dmCustomCombVO4.setGroupCode("1165D");
        dmCustomCombVO4.setCustomId(154L);
        dmCustomCombVO4.setConnectCode("11126");
        DmCustomCombVO dmCustomCombVO5 = new DmCustomCombVO();
        dmCustomCombVO5.setGroupLevel("L2");
        dmCustomCombVO5.setGroupCode("1165D");
        dmCustomCombVO5.setCustomId(154L);
        dmCustomCombVO5.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO6 = new DmCustomCombVO();
        dmCustomCombVO6.setGroupLevel("DIMENSION");
        dmCustomCombVO6.setGroupCode("1165D");
        dmCustomCombVO6.setCustomId(154L);
        dmCustomCombVO6.setConnectCode("11129");
        DmCustomCombVO dmCustomCombVO7 = new DmCustomCombVO();
        dmCustomCombVO7.setGroupLevel("SUBCATEGORY");
        dmCustomCombVO7.setGroupCode("1165D");
        dmCustomCombVO7.setCustomId(154L);
        dmCustomCombVO7.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO8 = new DmCustomCombVO();
        dmCustomCombVO8.setGroupLevel("SUB_DETAIL");
        dmCustomCombVO8.setGroupCode("1165D");
        dmCustomCombVO8.setCustomId(154L);
        dmCustomCombVO8.setConnectCode("11128");
        customVOList.add(dmCustomCombVO);
        customVOList.add(dmCustomCombVO2);
        customVOList.add(dmCustomCombVO3);
        customVOList.add(dmCustomCombVO4);
        customVOList.add(dmCustomCombVO5);
        customVOList.add(dmCustomCombVO6);
        customVOList.add(dmCustomCombVO7);
        customVOList.add(dmCustomCombVO8);
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(combinationVO.getCostType(),combinationVO.getIndustryOrg());
        DmFocVersionInfoDTO combinationVO2=new DmFocVersionInfoDTO();
        combinationVO2.setVersionId(188L);
        List<DmFocViewInfoVO> dmGroupCodeList=new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();

        dmGroupCodeList.add(dmFocViewInfoVO);

        Mockito.when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(combinationVO2);
        Mockito.when(dmFocCustomCombDao.prodTeamCodeForGeneralMonth(any())).thenReturn(dmGroupCodeList);

        List<DmCustomCombVO> dmCustomCombVOS = customCommonService.filterAnotherPageData(combinationVO, customVOList);
        Assert.assertNotNull(dmCustomCombVOS);

    }

    @Test
    public void filterAnotherPageData5Test() {
        CombinationVO combinationVO=new CombinationVO();
        List<DmCustomCombVO > customVOList=new ArrayList<>();
        combinationVO.setPageFlag("1");
        combinationVO.setGranularityType("D");
        combinationVO.setPageSymbol("MONTH");
        Set<String> set = new HashSet<>();
        set.add("NO_PERMISSION");
        combinationVO.setLv0DimensionSet(set);

        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setGroupLevel("LV1");
        dmCustomCombVO.setGroupCode("1165D");
        dmCustomCombVO.setCustomId(154L);
        dmCustomCombVO.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO2 = new DmCustomCombVO();
        dmCustomCombVO2.setGroupLevel("LV2");
        dmCustomCombVO2.setGroupCode("1165D");
        dmCustomCombVO2.setCustomId(154L);
        dmCustomCombVO2.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO3 = new DmCustomCombVO();
        dmCustomCombVO3.setGroupLevel("LV3");
        dmCustomCombVO3.setGroupCode("1165D");
        dmCustomCombVO3.setCustomId(154L);
        dmCustomCombVO3.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO4 = new DmCustomCombVO();
        dmCustomCombVO4.setGroupLevel("L1");
        dmCustomCombVO4.setGroupCode("1165D");
        dmCustomCombVO4.setCustomId(154L);
        dmCustomCombVO4.setConnectCode("11126");
        DmCustomCombVO dmCustomCombVO5 = new DmCustomCombVO();
        dmCustomCombVO5.setGroupLevel("L2");
        dmCustomCombVO5.setGroupCode("1165D");
        dmCustomCombVO5.setCustomId(154L);
        dmCustomCombVO5.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO6 = new DmCustomCombVO();
        dmCustomCombVO6.setGroupLevel("DIMENSION");
        dmCustomCombVO6.setGroupCode("1165D");
        dmCustomCombVO6.setCustomId(154L);
        dmCustomCombVO6.setConnectCode("11129");
        DmCustomCombVO dmCustomCombVO7 = new DmCustomCombVO();
        dmCustomCombVO7.setGroupLevel("SUBCATEGORY");
        dmCustomCombVO7.setGroupCode("1165D");
        dmCustomCombVO7.setCustomId(154L);
        dmCustomCombVO7.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO8 = new DmCustomCombVO();
        dmCustomCombVO8.setGroupLevel("SUB_DETAIL");
        dmCustomCombVO8.setGroupCode("1165D");
        dmCustomCombVO8.setCustomId(154L);
        dmCustomCombVO8.setConnectCode("11128");
        customVOList.add(dmCustomCombVO);
        customVOList.add(dmCustomCombVO2);
        customVOList.add(dmCustomCombVO3);
        customVOList.add(dmCustomCombVO4);
        customVOList.add(dmCustomCombVO5);
        customVOList.add(dmCustomCombVO6);
        customVOList.add(dmCustomCombVO7);
        customVOList.add(dmCustomCombVO8);

        DmFocVersionInfoDTO combinationVO2=new DmFocVersionInfoDTO();
        combinationVO2.setVersionId(188L);
        List<DmFocViewInfoVO> dmGroupCodeList=new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(combinationVO.getCostType(),combinationVO.getIndustryOrg());
        dmGroupCodeList.add(dmFocViewInfoVO);

        Mockito.when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(combinationVO2);
        Mockito.when(dmFocCustomCombDao.prodTeamCodeForGeneralMonth(any())).thenReturn(dmGroupCodeList);

        List<DmCustomCombVO> dmCustomCombVOS = customCommonService.filterAnotherPageData(combinationVO, customVOList);
        Assert.assertNotNull(dmCustomCombVOS);

    }

    @Test
    public void filterAnotherPageData6Test() {
        CombinationVO combinationVO=new CombinationVO();
        List<DmCustomCombVO > customVOList=new ArrayList<>();
        combinationVO.setPageFlag("1");
        combinationVO.setGranularityType("D");
        combinationVO.setPageSymbol("ANNUAL");
        Set<String> set = new HashSet<>();
        set.add("NO_PERMISSION");
        combinationVO.setLv0DimensionSet(set);
        combinationVO.setViewFlag("0");

        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setGroupLevel("LV1");
        dmCustomCombVO.setGroupCode("1165D");
        dmCustomCombVO.setCustomId(154L);
        dmCustomCombVO.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO2 = new DmCustomCombVO();
        dmCustomCombVO2.setGroupLevel("LV2");
        dmCustomCombVO2.setGroupCode("1165D");
        dmCustomCombVO2.setCustomId(154L);
        dmCustomCombVO2.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO3 = new DmCustomCombVO();
        dmCustomCombVO3.setGroupLevel("LV3");
        dmCustomCombVO3.setGroupCode("1165D");
        dmCustomCombVO3.setCustomId(154L);
        dmCustomCombVO3.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO4 = new DmCustomCombVO();
        dmCustomCombVO4.setGroupLevel("L1");
        dmCustomCombVO4.setGroupCode("1165D");
        dmCustomCombVO4.setCustomId(154L);
        dmCustomCombVO4.setConnectCode("11126");
        DmCustomCombVO dmCustomCombVO5 = new DmCustomCombVO();
        dmCustomCombVO5.setGroupLevel("L2");
        dmCustomCombVO5.setGroupCode("1165D");
        dmCustomCombVO5.setCustomId(154L);
        dmCustomCombVO5.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO6 = new DmCustomCombVO();
        dmCustomCombVO6.setGroupLevel("DIMENSION");
        dmCustomCombVO6.setGroupCode("1165D");
        dmCustomCombVO6.setCustomId(154L);
        dmCustomCombVO6.setConnectCode("11129");
        DmCustomCombVO dmCustomCombVO7 = new DmCustomCombVO();
        dmCustomCombVO7.setGroupLevel("SUBCATEGORY");
        dmCustomCombVO7.setGroupCode("1165D");
        dmCustomCombVO7.setCustomId(154L);
        dmCustomCombVO7.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO8 = new DmCustomCombVO();
        dmCustomCombVO8.setGroupLevel("SUB_DETAIL");
        dmCustomCombVO8.setGroupCode("1165D");
        dmCustomCombVO8.setCustomId(154L);
        dmCustomCombVO8.setConnectCode("11128");
        customVOList.add(dmCustomCombVO);
        customVOList.add(dmCustomCombVO2);
        customVOList.add(dmCustomCombVO3);
        customVOList.add(dmCustomCombVO4);
        customVOList.add(dmCustomCombVO5);
        customVOList.add(dmCustomCombVO6);
        customVOList.add(dmCustomCombVO7);
        customVOList.add(dmCustomCombVO8);

        DmFocVersionInfoDTO combinationVO2=new DmFocVersionInfoDTO();
        combinationVO2.setVersionId(188L);
        List<DmFocViewInfoVO> dmGroupCodeList=new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(combinationVO.getCostType(),combinationVO.getIndustryOrg());
        dmGroupCodeList.add(dmFocViewInfoVO);

        Mockito.when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(combinationVO2);
        Mockito.when(dmFocCustomCombDao.prodTeamCodeForGeneralMonth(any())).thenReturn(dmGroupCodeList);

        List<DmCustomCombVO> dmCustomCombVOS = customCommonService.filterAnotherPageData(combinationVO, customVOList);
        Assert.assertNotNull(dmCustomCombVOS);

    }

    @Test
    public void filterAnotherPageData7Test() {
        CombinationVO combinationVO=new CombinationVO();
        List<DmCustomCombVO > customVOList=new ArrayList<>();
        combinationVO.setPageFlag("1");
        combinationVO.setGranularityType("U");
        combinationVO.setPageSymbol("ANNUAL");
        Set<String> set = new HashSet<>();
        set.add("NO_PERMISSION");
        combinationVO.setLv0DimensionSet(set);
        combinationVO.setViewFlag("4");

        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setGroupLevel("LV1");
        dmCustomCombVO.setGroupCode("1165D");
        dmCustomCombVO.setCustomId(154L);
        dmCustomCombVO.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO2 = new DmCustomCombVO();
        dmCustomCombVO2.setGroupLevel("LV2");
        dmCustomCombVO2.setGroupCode("1165D");
        dmCustomCombVO2.setCustomId(154L);
        dmCustomCombVO2.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO3 = new DmCustomCombVO();
        dmCustomCombVO3.setGroupLevel("LV3");
        dmCustomCombVO3.setGroupCode("1165D");
        dmCustomCombVO3.setCustomId(154L);
        dmCustomCombVO3.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO4 = new DmCustomCombVO();
        dmCustomCombVO4.setGroupLevel("L1");
        dmCustomCombVO4.setGroupCode("1165D");
        dmCustomCombVO4.setCustomId(154L);
        dmCustomCombVO4.setConnectCode("11126");
        DmCustomCombVO dmCustomCombVO5 = new DmCustomCombVO();
        dmCustomCombVO5.setGroupLevel("L2");
        dmCustomCombVO5.setGroupCode("1165D");
        dmCustomCombVO5.setCustomId(154L);
        dmCustomCombVO5.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO6 = new DmCustomCombVO();
        dmCustomCombVO6.setGroupLevel("DIMENSION");
        dmCustomCombVO6.setGroupCode("1165D");
        dmCustomCombVO6.setCustomId(154L);
        dmCustomCombVO6.setConnectCode("11129");
        DmCustomCombVO dmCustomCombVO7 = new DmCustomCombVO();
        dmCustomCombVO7.setGroupLevel("SUBCATEGORY");
        dmCustomCombVO7.setGroupCode("1165D");
        dmCustomCombVO7.setCustomId(154L);
        dmCustomCombVO7.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO8 = new DmCustomCombVO();
        dmCustomCombVO8.setGroupLevel("SUB_DETAIL");
        dmCustomCombVO8.setGroupCode("1165D");
        dmCustomCombVO8.setCustomId(154L);
        dmCustomCombVO8.setConnectCode("11128");
        customVOList.add(dmCustomCombVO);
        customVOList.add(dmCustomCombVO2);
        customVOList.add(dmCustomCombVO3);
        customVOList.add(dmCustomCombVO4);
        customVOList.add(dmCustomCombVO5);
        customVOList.add(dmCustomCombVO6);
        customVOList.add(dmCustomCombVO7);
        customVOList.add(dmCustomCombVO8);

        DmFocVersionInfoDTO combinationVO2=new DmFocVersionInfoDTO();
        combinationVO2.setVersionId(188L);
        List<DmFocViewInfoVO> dmGroupCodeList=new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(combinationVO.getCostType(),combinationVO.getIndustryOrg());
        dmGroupCodeList.add(dmFocViewInfoVO);

        Mockito.when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(combinationVO2);
        Mockito.when(dmFocCustomCombDao.prodTeamCodeForGeneralMonth(any())).thenReturn(dmGroupCodeList);

        List<DmCustomCombVO> dmCustomCombVOS = customCommonService.filterAnotherPageData(combinationVO, customVOList);
        Assert.assertNotNull(dmCustomCombVOS);

    }

    @Test
    public void filterAnotherPageData8Test() {
        CombinationVO combinationVO=new CombinationVO();
        List<DmCustomCombVO > customVOList=new ArrayList<>();
        combinationVO.setPageFlag("1");
        combinationVO.setGranularityType("U");
        combinationVO.setPageSymbol("MONTH");
        Set<String> set = new HashSet<>();
        set.add("ALL");
        combinationVO.setLv0DimensionSet(set);
        combinationVO.setViewFlag("5");

        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setGroupLevel("LV1");
        dmCustomCombVO.setGroupCode("1165D");
        dmCustomCombVO.setCustomId(154L);
        dmCustomCombVO.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO2 = new DmCustomCombVO();
        dmCustomCombVO2.setGroupLevel("LV2");
        dmCustomCombVO2.setGroupCode("1165D");
        dmCustomCombVO2.setCustomId(154L);
        dmCustomCombVO2.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO3 = new DmCustomCombVO();
        dmCustomCombVO3.setGroupLevel("LV3");
        dmCustomCombVO3.setGroupCode("1165D");
        dmCustomCombVO3.setCustomId(154L);
        dmCustomCombVO3.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO4 = new DmCustomCombVO();
        dmCustomCombVO4.setGroupLevel("L1");
        dmCustomCombVO4.setGroupCode("1165D");
        dmCustomCombVO4.setCustomId(154L);
        dmCustomCombVO4.setConnectCode("11126");
        DmCustomCombVO dmCustomCombVO5 = new DmCustomCombVO();
        dmCustomCombVO5.setGroupLevel("L2");
        dmCustomCombVO5.setGroupCode("1165D");
        dmCustomCombVO5.setCustomId(154L);
        dmCustomCombVO5.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO6 = new DmCustomCombVO();
        dmCustomCombVO6.setGroupLevel("DIMENSION");
        dmCustomCombVO6.setGroupCode("1165D");
        dmCustomCombVO6.setCustomId(154L);
        dmCustomCombVO6.setConnectCode("11129");
        DmCustomCombVO dmCustomCombVO7 = new DmCustomCombVO();
        dmCustomCombVO7.setGroupLevel("SUBCATEGORY");
        dmCustomCombVO7.setGroupCode("1165D");
        dmCustomCombVO7.setCustomId(154L);
        dmCustomCombVO7.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO8 = new DmCustomCombVO();
        dmCustomCombVO8.setGroupLevel("SUB_DETAIL");
        dmCustomCombVO8.setGroupCode("1165D");
        dmCustomCombVO8.setCustomId(154L);
        dmCustomCombVO8.setConnectCode("11128");
        customVOList.add(dmCustomCombVO);
        customVOList.add(dmCustomCombVO2);
        customVOList.add(dmCustomCombVO3);
        customVOList.add(dmCustomCombVO4);
        customVOList.add(dmCustomCombVO5);
        customVOList.add(dmCustomCombVO6);
        customVOList.add(dmCustomCombVO7);
        customVOList.add(dmCustomCombVO8);

        DmFocVersionInfoDTO combinationVO2=new DmFocVersionInfoDTO();
        combinationVO2.setVersionId(188L);
        List<DmFocViewInfoVO> dmGroupCodeList=new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(combinationVO.getCostType(),combinationVO.getIndustryOrg());
        dmGroupCodeList.add(dmFocViewInfoVO);

        Mockito.when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(combinationVO2);
        Mockito.when(dmFocCustomCombDao.prodTeamCodeForGeneralMonth(any())).thenReturn(dmGroupCodeList);

        List<DmCustomCombVO> dmCustomCombVOS = customCommonService.filterAnotherPageData(combinationVO, customVOList);
        Assert.assertNotNull(dmCustomCombVOS);

    }

    @Test
    public void filterAnotherPageData9Test() {
        CombinationVO combinationVO=new CombinationVO();
        List<DmCustomCombVO > customVOList=new ArrayList<>();
        combinationVO.setPageFlag("3");
        combinationVO.setGranularityType("U");
        combinationVO.setPageSymbol("MONTH");
        Set<String> set = new HashSet<>();
        set.add("ALL");
        combinationVO.setLv0DimensionSet(set);
        combinationVO.setViewFlag("3");

        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setGroupLevel("LV1");
        dmCustomCombVO.setGroupCode("1165D");
        dmCustomCombVO.setCustomId(154L);
        dmCustomCombVO.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO2 = new DmCustomCombVO();
        dmCustomCombVO2.setGroupLevel("LV2");
        dmCustomCombVO2.setGroupCode("1165D");
        dmCustomCombVO2.setCustomId(154L);
        dmCustomCombVO2.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO3 = new DmCustomCombVO();
        dmCustomCombVO3.setGroupLevel("LV3");
        dmCustomCombVO3.setGroupCode("1165D");
        dmCustomCombVO3.setCustomId(154L);
        dmCustomCombVO3.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO4 = new DmCustomCombVO();
        dmCustomCombVO4.setGroupLevel("L1");
        dmCustomCombVO4.setGroupCode("1165D");
        dmCustomCombVO4.setCustomId(154L);
        dmCustomCombVO4.setConnectCode("11126");
        DmCustomCombVO dmCustomCombVO5 = new DmCustomCombVO();
        dmCustomCombVO5.setGroupLevel("L2");
        dmCustomCombVO5.setGroupCode("1165D");
        dmCustomCombVO5.setCustomId(154L);
        dmCustomCombVO5.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO6 = new DmCustomCombVO();
        dmCustomCombVO6.setGroupLevel("DIMENSION");
        dmCustomCombVO6.setGroupCode("1165D");
        dmCustomCombVO6.setCustomId(154L);
        dmCustomCombVO6.setConnectCode("11129");
        DmCustomCombVO dmCustomCombVO7 = new DmCustomCombVO();
        dmCustomCombVO7.setGroupLevel("SUBCATEGORY");
        dmCustomCombVO7.setGroupCode("1165D");
        dmCustomCombVO7.setCustomId(154L);
        dmCustomCombVO7.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO8 = new DmCustomCombVO();
        dmCustomCombVO8.setGroupLevel("SUB_DETAIL");
        dmCustomCombVO8.setGroupCode("1165D");
        dmCustomCombVO8.setCustomId(154L);
        dmCustomCombVO8.setConnectCode("11128");
        customVOList.add(dmCustomCombVO);
        customVOList.add(dmCustomCombVO2);
        customVOList.add(dmCustomCombVO3);
        customVOList.add(dmCustomCombVO4);
        customVOList.add(dmCustomCombVO5);
        customVOList.add(dmCustomCombVO6);
        customVOList.add(dmCustomCombVO7);
        customVOList.add(dmCustomCombVO8);

        DmFocVersionInfoDTO combinationVO2=new DmFocVersionInfoDTO();
        combinationVO2.setVersionId(188L);
        List<DmFocViewInfoVO> dmGroupCodeList=new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(combinationVO.getCostType(),combinationVO.getIndustryOrg());
        dmGroupCodeList.add(dmFocViewInfoVO);

        Mockito.when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(combinationVO2);
        Mockito.when(dmFocCustomCombDao.prodTeamCodeForGeneralMonth(any())).thenReturn(dmGroupCodeList);

        List<DmCustomCombVO> dmCustomCombVOS = customCommonService.filterAnotherPageData(combinationVO, customVOList);
        Assert.assertNotNull(dmCustomCombVOS);

    }

    @Test
    public void filterAnotherPageData10Test() {
        CombinationVO combinationVO=new CombinationVO();
        List<DmCustomCombVO > customVOList=new ArrayList<>();
        combinationVO.setPageFlag("3");
        combinationVO.setGranularityType("D");
        combinationVO.setPageSymbol("MONTH");
        Set<String> set = new HashSet<>();
        set.add("ALL");
        combinationVO.setLv0DimensionSet(set);
        combinationVO.setViewFlag("6");

        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setGroupLevel("LV1");
        dmCustomCombVO.setGroupCode("1165D");
        dmCustomCombVO.setCustomId(154L);
        dmCustomCombVO.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO2 = new DmCustomCombVO();
        dmCustomCombVO2.setGroupLevel("LV2");
        dmCustomCombVO2.setGroupCode("1165D");
        dmCustomCombVO2.setCustomId(154L);
        dmCustomCombVO2.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO3 = new DmCustomCombVO();
        dmCustomCombVO3.setGroupLevel("LV3");
        dmCustomCombVO3.setGroupCode("1165D");
        dmCustomCombVO3.setCustomId(154L);
        dmCustomCombVO3.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO4 = new DmCustomCombVO();
        dmCustomCombVO4.setGroupLevel("L1");
        dmCustomCombVO4.setGroupCode("1165D");
        dmCustomCombVO4.setCustomId(154L);
        dmCustomCombVO4.setConnectCode("11126");
        DmCustomCombVO dmCustomCombVO5 = new DmCustomCombVO();
        dmCustomCombVO5.setGroupLevel("L2");
        dmCustomCombVO5.setGroupCode("1165D");
        dmCustomCombVO5.setCustomId(154L);
        dmCustomCombVO5.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO6 = new DmCustomCombVO();
        dmCustomCombVO6.setGroupLevel("DIMENSION");
        dmCustomCombVO6.setGroupCode("1165D");
        dmCustomCombVO6.setCustomId(154L);
        dmCustomCombVO6.setConnectCode("11129");
        DmCustomCombVO dmCustomCombVO7 = new DmCustomCombVO();
        dmCustomCombVO7.setGroupLevel("SUBCATEGORY");
        dmCustomCombVO7.setGroupCode("1165D");
        dmCustomCombVO7.setCustomId(154L);
        dmCustomCombVO7.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO8 = new DmCustomCombVO();
        dmCustomCombVO8.setGroupLevel("SUB_DETAIL");
        dmCustomCombVO8.setGroupCode("1165D");
        dmCustomCombVO8.setCustomId(154L);
        dmCustomCombVO8.setConnectCode("11128");
        customVOList.add(dmCustomCombVO);
        customVOList.add(dmCustomCombVO2);
        customVOList.add(dmCustomCombVO3);
        customVOList.add(dmCustomCombVO4);
        customVOList.add(dmCustomCombVO5);
        customVOList.add(dmCustomCombVO6);
        customVOList.add(dmCustomCombVO7);
        customVOList.add(dmCustomCombVO8);

        DmFocVersionInfoDTO combinationVO2=new DmFocVersionInfoDTO();
        combinationVO2.setVersionId(188L);
        List<DmFocViewInfoVO> dmGroupCodeList=new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(combinationVO.getCostType(),combinationVO.getIndustryOrg());
        dmGroupCodeList.add(dmFocViewInfoVO);

        Mockito.when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(combinationVO2);
        Mockito.when(dmFocCustomCombDao.prodTeamCodeForGeneralMonth(any())).thenReturn(dmGroupCodeList);

        List<DmCustomCombVO> dmCustomCombVOS = customCommonService.filterAnotherPageData(combinationVO, customVOList);
        Assert.assertNotNull(dmCustomCombVOS);

    }

    @Test
    public void filterAnotherPageData11Test() {
        CombinationVO combinationVO=new CombinationVO();
        List<DmCustomCombVO > customVOList=new ArrayList<>();
        combinationVO.setPageFlag("3");
        combinationVO.setGranularityType("D");
        combinationVO.setPageSymbol("MONTH");
        Set<String> set = new HashSet<>();
        set.add("ALL");
        combinationVO.setLv0DimensionSet(set);
        combinationVO.setViewFlag("2");

        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setGroupLevel("LV1");
        dmCustomCombVO.setGroupCode("1165D");
        dmCustomCombVO.setCustomId(154L);
        dmCustomCombVO.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO2 = new DmCustomCombVO();
        dmCustomCombVO2.setGroupLevel("LV2");
        dmCustomCombVO2.setGroupCode("1165D");
        dmCustomCombVO2.setCustomId(154L);
        dmCustomCombVO2.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO3 = new DmCustomCombVO();
        dmCustomCombVO3.setGroupLevel("LV3");
        dmCustomCombVO3.setGroupCode("1165D");
        dmCustomCombVO3.setCustomId(154L);
        dmCustomCombVO3.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO4 = new DmCustomCombVO();
        dmCustomCombVO4.setGroupLevel("L1");
        dmCustomCombVO4.setGroupCode("1165D");
        dmCustomCombVO4.setCustomId(154L);
        dmCustomCombVO4.setConnectCode("11126");
        DmCustomCombVO dmCustomCombVO5 = new DmCustomCombVO();
        dmCustomCombVO5.setGroupLevel("L2");
        dmCustomCombVO5.setGroupCode("1165D");
        dmCustomCombVO5.setCustomId(154L);
        dmCustomCombVO5.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO6 = new DmCustomCombVO();
        dmCustomCombVO6.setGroupLevel("DIMENSION");
        dmCustomCombVO6.setGroupCode("1165D");
        dmCustomCombVO6.setCustomId(154L);
        dmCustomCombVO6.setConnectCode("11129");
        DmCustomCombVO dmCustomCombVO7 = new DmCustomCombVO();
        dmCustomCombVO7.setGroupLevel("SUBCATEGORY");
        dmCustomCombVO7.setGroupCode("1165D");
        dmCustomCombVO7.setCustomId(154L);
        dmCustomCombVO7.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO8 = new DmCustomCombVO();
        dmCustomCombVO8.setGroupLevel("SUB_DETAIL");
        dmCustomCombVO8.setGroupCode("1165D");
        dmCustomCombVO8.setCustomId(154L);
        dmCustomCombVO8.setConnectCode("11128");
        customVOList.add(dmCustomCombVO);
        customVOList.add(dmCustomCombVO2);
        customVOList.add(dmCustomCombVO3);
        customVOList.add(dmCustomCombVO4);
        customVOList.add(dmCustomCombVO5);
        customVOList.add(dmCustomCombVO6);
        customVOList.add(dmCustomCombVO7);
        customVOList.add(dmCustomCombVO8);
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(combinationVO.getCostType(),combinationVO.getIndustryOrg());
        DmFocVersionInfoDTO combinationVO2=new DmFocVersionInfoDTO();
        combinationVO2.setVersionId(188L);
        List<DmFocViewInfoVO> dmGroupCodeList=new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();

        dmGroupCodeList.add(dmFocViewInfoVO);

        Mockito.when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(combinationVO2);
        Mockito.when(dmFocCustomCombDao.prodTeamCodeForGeneralMonth(any())).thenReturn(dmGroupCodeList);

        List<DmCustomCombVO> dmCustomCombVOS = customCommonService.filterAnotherPageData(combinationVO, customVOList);
        Assert.assertNotNull(dmCustomCombVOS);

    }

    @Test
    public void filterAnotherPageData12Test() {
        CombinationVO combinationVO=new CombinationVO();
        List<DmCustomCombVO > customVOList=new ArrayList<>();
        combinationVO.setPageFlag("3");
        combinationVO.setGranularityType("D");
        combinationVO.setPageSymbol("MONTH");
        Set<String> set = new HashSet<>();
        set.add("ALL");
        combinationVO.setLv0DimensionSet(set);
        combinationVO.setViewFlag("5");

        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setGroupLevel("LV1");
        dmCustomCombVO.setGroupCode("1165D");
        dmCustomCombVO.setCustomId(154L);
        dmCustomCombVO.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO2 = new DmCustomCombVO();
        dmCustomCombVO2.setGroupLevel("LV2");
        dmCustomCombVO2.setGroupCode("1165D");
        dmCustomCombVO2.setCustomId(154L);
        dmCustomCombVO2.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO3 = new DmCustomCombVO();
        dmCustomCombVO3.setGroupLevel("LV3");
        dmCustomCombVO3.setGroupCode("1165D");
        dmCustomCombVO3.setCustomId(154L);
        dmCustomCombVO3.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO4 = new DmCustomCombVO();
        dmCustomCombVO4.setGroupLevel("L1");
        dmCustomCombVO4.setGroupCode("1165D");
        dmCustomCombVO4.setCustomId(154L);
        dmCustomCombVO4.setConnectCode("11126");
        DmCustomCombVO dmCustomCombVO5 = new DmCustomCombVO();
        dmCustomCombVO5.setGroupLevel("L2");
        dmCustomCombVO5.setGroupCode("1165D");
        dmCustomCombVO5.setCustomId(154L);
        dmCustomCombVO5.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO6 = new DmCustomCombVO();
        dmCustomCombVO6.setGroupLevel("DIMENSION");
        dmCustomCombVO6.setGroupCode("1165D");
        dmCustomCombVO6.setCustomId(154L);
        dmCustomCombVO6.setConnectCode("11129");
        DmCustomCombVO dmCustomCombVO7 = new DmCustomCombVO();
        dmCustomCombVO7.setGroupLevel("SUBCATEGORY");
        dmCustomCombVO7.setGroupCode("1165D");
        dmCustomCombVO7.setCustomId(154L);
        dmCustomCombVO7.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO8 = new DmCustomCombVO();
        dmCustomCombVO8.setGroupLevel("SUB_DETAIL");
        dmCustomCombVO8.setGroupCode("1165D");
        dmCustomCombVO8.setCustomId(154L);
        dmCustomCombVO8.setConnectCode("11128");
        customVOList.add(dmCustomCombVO);
        customVOList.add(dmCustomCombVO2);
        customVOList.add(dmCustomCombVO3);
        customVOList.add(dmCustomCombVO4);
        customVOList.add(dmCustomCombVO5);
        customVOList.add(dmCustomCombVO6);
        customVOList.add(dmCustomCombVO7);
        customVOList.add(dmCustomCombVO8);
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(combinationVO.getCostType(),combinationVO.getIndustryOrg());
        DmFocVersionInfoDTO combinationVO2=new DmFocVersionInfoDTO();
        combinationVO2.setVersionId(188L);
        List<DmFocViewInfoVO> dmGroupCodeList=new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();

        dmGroupCodeList.add(dmFocViewInfoVO);

        Mockito.when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(combinationVO2);
        Mockito.when(dmFocCustomCombDao.prodTeamCodeForGeneralMonth(any())).thenReturn(dmGroupCodeList);

        List<DmCustomCombVO> dmCustomCombVOS = customCommonService.filterAnotherPageData(combinationVO, customVOList);
        Assert.assertNotNull(dmCustomCombVOS);

    }

    @Test
    public void filterAnotherPageData13Test() {
        CombinationVO combinationVO=new CombinationVO();
        List<DmCustomCombVO > customVOList=new ArrayList<>();
        combinationVO.setPageFlag("3");
        combinationVO.setGranularityType("D");
        combinationVO.setPageSymbol("MONTH");
        Set<String> set = new HashSet<>();
        set.add("ALL");
        combinationVO.setLv0DimensionSet(set);
        combinationVO.setViewFlag("8");

        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setGroupLevel("LV1");
        dmCustomCombVO.setGroupCode("1165D");
        dmCustomCombVO.setCustomId(154L);
        dmCustomCombVO.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO2 = new DmCustomCombVO();
        dmCustomCombVO2.setGroupLevel("LV2");
        dmCustomCombVO2.setGroupCode("1165D");
        dmCustomCombVO2.setCustomId(154L);
        dmCustomCombVO2.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO3 = new DmCustomCombVO();
        dmCustomCombVO3.setGroupLevel("LV3");
        dmCustomCombVO3.setGroupCode("1165D");
        dmCustomCombVO3.setCustomId(154L);
        dmCustomCombVO3.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO4 = new DmCustomCombVO();
        dmCustomCombVO4.setGroupLevel("L1");
        dmCustomCombVO4.setGroupCode("1165D");
        dmCustomCombVO4.setCustomId(154L);
        dmCustomCombVO4.setConnectCode("11126");
        DmCustomCombVO dmCustomCombVO5 = new DmCustomCombVO();
        dmCustomCombVO5.setGroupLevel("L2");
        dmCustomCombVO5.setGroupCode("1165D");
        dmCustomCombVO5.setCustomId(154L);
        dmCustomCombVO5.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO6 = new DmCustomCombVO();
        dmCustomCombVO6.setGroupLevel("DIMENSION");
        dmCustomCombVO6.setGroupCode("1165D");
        dmCustomCombVO6.setCustomId(154L);
        dmCustomCombVO6.setConnectCode("11129");
        DmCustomCombVO dmCustomCombVO7 = new DmCustomCombVO();
        dmCustomCombVO7.setGroupLevel("SUBCATEGORY");
        dmCustomCombVO7.setGroupCode("1165D");
        dmCustomCombVO7.setCustomId(154L);
        dmCustomCombVO7.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO8 = new DmCustomCombVO();
        dmCustomCombVO8.setGroupLevel("SUB_DETAIL");
        dmCustomCombVO8.setGroupCode("1165D");
        dmCustomCombVO8.setCustomId(154L);
        dmCustomCombVO8.setConnectCode("11128");
        customVOList.add(dmCustomCombVO);
        customVOList.add(dmCustomCombVO2);
        customVOList.add(dmCustomCombVO3);
        customVOList.add(dmCustomCombVO4);
        customVOList.add(dmCustomCombVO5);
        customVOList.add(dmCustomCombVO6);
        customVOList.add(dmCustomCombVO7);
        customVOList.add(dmCustomCombVO8);
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(combinationVO.getCostType(),combinationVO.getIndustryOrg());
        DmFocVersionInfoDTO combinationVO2=new DmFocVersionInfoDTO();
        combinationVO2.setVersionId(188L);
        List<DmFocViewInfoVO> dmGroupCodeList=new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();

        dmGroupCodeList.add(dmFocViewInfoVO);

        Mockito.when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(combinationVO2);
        Mockito.when(dmFocCustomCombDao.prodTeamCodeForGeneralMonth(any())).thenReturn(dmGroupCodeList);

        List<DmCustomCombVO> dmCustomCombVOS = customCommonService.filterAnotherPageData(combinationVO, customVOList);
        Assert.assertNotNull(dmCustomCombVOS);
    }

    @Test
    public void filterAnotherPageData14Test() {
        CombinationVO combinationVO=new CombinationVO();
        List<DmCustomCombVO > customVOList=new ArrayList<>();
        combinationVO.setPageFlag("3");
        combinationVO.setGranularityType("P");
        combinationVO.setPageSymbol("MONTH");
        Set<String> set = new HashSet<>();
        set.add("ALL");
        combinationVO.setLv0DimensionSet(set);
        combinationVO.setViewFlag("3");

        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setGroupLevel("LV1");
        dmCustomCombVO.setGroupCode("1165D");
        dmCustomCombVO.setCustomId(154L);
        dmCustomCombVO.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO2 = new DmCustomCombVO();
        dmCustomCombVO2.setGroupLevel("LV2");
        dmCustomCombVO2.setGroupCode("1165D");
        dmCustomCombVO2.setCustomId(154L);
        dmCustomCombVO2.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO3 = new DmCustomCombVO();
        dmCustomCombVO3.setGroupLevel("LV3");
        dmCustomCombVO3.setGroupCode("1165D");
        dmCustomCombVO3.setCustomId(154L);
        dmCustomCombVO3.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO4 = new DmCustomCombVO();
        dmCustomCombVO4.setGroupLevel("L1");
        dmCustomCombVO4.setGroupCode("1165D");
        dmCustomCombVO4.setCustomId(154L);
        dmCustomCombVO4.setConnectCode("11126");
        DmCustomCombVO dmCustomCombVO5 = new DmCustomCombVO();
        dmCustomCombVO5.setGroupLevel("L2");
        dmCustomCombVO5.setGroupCode("1165D");
        dmCustomCombVO5.setCustomId(154L);
        dmCustomCombVO5.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO6 = new DmCustomCombVO();
        dmCustomCombVO6.setGroupLevel("DIMENSION");
        dmCustomCombVO6.setGroupCode("1165D");
        dmCustomCombVO6.setCustomId(154L);
        dmCustomCombVO6.setConnectCode("11129");
        DmCustomCombVO dmCustomCombVO7 = new DmCustomCombVO();
        dmCustomCombVO7.setGroupLevel("SUBCATEGORY");
        dmCustomCombVO7.setGroupCode("1165D");
        dmCustomCombVO7.setCustomId(154L);
        dmCustomCombVO7.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO8 = new DmCustomCombVO();
        dmCustomCombVO8.setGroupLevel("SUB_DETAIL");
        dmCustomCombVO8.setGroupCode("1165D");
        dmCustomCombVO8.setCustomId(154L);
        dmCustomCombVO8.setConnectCode("11128");
        customVOList.add(dmCustomCombVO);
        customVOList.add(dmCustomCombVO2);
        customVOList.add(dmCustomCombVO3);
        customVOList.add(dmCustomCombVO4);
        customVOList.add(dmCustomCombVO5);
        customVOList.add(dmCustomCombVO6);
        customVOList.add(dmCustomCombVO7);
        customVOList.add(dmCustomCombVO8);
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(combinationVO.getCostType(),combinationVO.getIndustryOrg());
        DmFocVersionInfoDTO combinationVO2=new DmFocVersionInfoDTO();
        combinationVO2.setVersionId(188L);
        List<DmFocViewInfoVO> dmGroupCodeList=new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();

        dmGroupCodeList.add(dmFocViewInfoVO);

        Mockito.when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(combinationVO2);
        Mockito.when(dmFocCustomCombDao.prodTeamCodeForGeneralMonth(any())).thenReturn(dmGroupCodeList);

        List<DmCustomCombVO> dmCustomCombVOS = customCommonService.filterAnotherPageData(combinationVO, customVOList);
        Assert.assertNotNull(dmCustomCombVOS);
    }

    @Test
    public void filterAnotherPageData15Test() {
        CombinationVO combinationVO=new CombinationVO();
        List<DmCustomCombVO > customVOList=new ArrayList<>();
        combinationVO.setPageFlag("3");
        combinationVO.setGranularityType("P");
        combinationVO.setPageSymbol("MONTH");
        Set<String> set = new HashSet<>();
        set.add("ALL");
        combinationVO.setLv0DimensionSet(set);
        combinationVO.setViewFlag("4");

        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setGroupLevel("LV1");
        dmCustomCombVO.setGroupCode("1165D");
        dmCustomCombVO.setCustomId(154L);
        dmCustomCombVO.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO2 = new DmCustomCombVO();
        dmCustomCombVO2.setGroupLevel("LV2");
        dmCustomCombVO2.setGroupCode("1165D");
        dmCustomCombVO2.setCustomId(154L);
        dmCustomCombVO2.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO3 = new DmCustomCombVO();
        dmCustomCombVO3.setGroupLevel("LV3");
        dmCustomCombVO3.setGroupCode("1165D");
        dmCustomCombVO3.setCustomId(154L);
        dmCustomCombVO3.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO4 = new DmCustomCombVO();
        dmCustomCombVO4.setGroupLevel("L1");
        dmCustomCombVO4.setGroupCode("1165D");
        dmCustomCombVO4.setCustomId(154L);
        dmCustomCombVO4.setConnectCode("11126");
        DmCustomCombVO dmCustomCombVO5 = new DmCustomCombVO();
        dmCustomCombVO5.setGroupLevel("L2");
        dmCustomCombVO5.setGroupCode("1165D");
        dmCustomCombVO5.setCustomId(154L);
        dmCustomCombVO5.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO6 = new DmCustomCombVO();
        dmCustomCombVO6.setGroupLevel("DIMENSION");
        dmCustomCombVO6.setGroupCode("1165D");
        dmCustomCombVO6.setCustomId(154L);
        dmCustomCombVO6.setConnectCode("11129");
        DmCustomCombVO dmCustomCombVO7 = new DmCustomCombVO();
        dmCustomCombVO7.setGroupLevel("SUBCATEGORY");
        dmCustomCombVO7.setGroupCode("1165D");
        dmCustomCombVO7.setCustomId(154L);
        dmCustomCombVO7.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO8 = new DmCustomCombVO();
        dmCustomCombVO8.setGroupLevel("SUB_DETAIL");
        dmCustomCombVO8.setGroupCode("1165D");
        dmCustomCombVO8.setCustomId(154L);
        dmCustomCombVO8.setConnectCode("11128");
        customVOList.add(dmCustomCombVO);
        customVOList.add(dmCustomCombVO2);
        customVOList.add(dmCustomCombVO3);
        customVOList.add(dmCustomCombVO4);
        customVOList.add(dmCustomCombVO5);
        customVOList.add(dmCustomCombVO6);
        customVOList.add(dmCustomCombVO7);
        customVOList.add(dmCustomCombVO8);
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(combinationVO.getCostType(),combinationVO.getIndustryOrg());
        DmFocVersionInfoDTO combinationVO2=new DmFocVersionInfoDTO();
        combinationVO2.setVersionId(188L);
        List<DmFocViewInfoVO> dmGroupCodeList=new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();

        dmGroupCodeList.add(dmFocViewInfoVO);

        Mockito.when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(combinationVO2);
        Mockito.when(dmFocCustomCombDao.prodTeamCodeForGeneralMonth(any())).thenReturn(dmGroupCodeList);

        List<DmCustomCombVO> dmCustomCombVOS = customCommonService.filterAnotherPageData(combinationVO, customVOList);
        Assert.assertNotNull(dmCustomCombVOS);
    }

    @Test
    public void filterAnotherPageData16Test() {
        CombinationVO combinationVO=new CombinationVO();
        List<DmCustomCombVO > customVOList=new ArrayList<>();
        combinationVO.setPageFlag("3");
        combinationVO.setGranularityType("P");
        combinationVO.setPageSymbol("MONTH");
        Set<String> set = new HashSet<>();
        set.add("ALL");
        combinationVO.setLv0DimensionSet(set);
        combinationVO.setViewFlag("4");

        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setGroupLevel("CEG");
        dmCustomCombVO.setGroupCode("1165D");
        dmCustomCombVO.setCustomId(154L);
        dmCustomCombVO.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO2 = new DmCustomCombVO();
        dmCustomCombVO2.setGroupLevel("MODL");
        dmCustomCombVO2.setGroupCode("1165D");
        dmCustomCombVO2.setCustomId(154L);
        dmCustomCombVO2.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO3 = new DmCustomCombVO();
        dmCustomCombVO3.setGroupLevel("CATEGORY");
        dmCustomCombVO3.setGroupCode("1165D");
        dmCustomCombVO3.setCustomId(154L);
        dmCustomCombVO3.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO4 = new DmCustomCombVO();
        dmCustomCombVO4.setGroupLevel("L1");
        dmCustomCombVO4.setGroupCode("1165D");
        dmCustomCombVO4.setCustomId(154L);
        dmCustomCombVO4.setConnectCode("11126");
        DmCustomCombVO dmCustomCombVO5 = new DmCustomCombVO();
        dmCustomCombVO5.setGroupLevel("L2");
        dmCustomCombVO5.setGroupCode("1165D");
        dmCustomCombVO5.setCustomId(154L);
        dmCustomCombVO5.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO6 = new DmCustomCombVO();
        dmCustomCombVO6.setGroupLevel("DIMENSION");
        dmCustomCombVO6.setGroupCode("1165D");
        dmCustomCombVO6.setCustomId(154L);
        dmCustomCombVO6.setConnectCode("11129");
        DmCustomCombVO dmCustomCombVO7 = new DmCustomCombVO();
        dmCustomCombVO7.setGroupLevel("SUBCATEGORY");
        dmCustomCombVO7.setGroupCode("1165D");
        dmCustomCombVO7.setCustomId(154L);
        dmCustomCombVO7.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO8 = new DmCustomCombVO();
        dmCustomCombVO8.setGroupLevel("SUB_DETAIL");
        dmCustomCombVO8.setGroupCode("1165D");
        dmCustomCombVO8.setCustomId(154L);
        dmCustomCombVO8.setConnectCode("11128");
        customVOList.add(dmCustomCombVO);
        customVOList.add(dmCustomCombVO2);
        customVOList.add(dmCustomCombVO3);
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(combinationVO.getCostType(),combinationVO.getIndustryOrg());
        DmFocVersionInfoDTO combinationVO2=new DmFocVersionInfoDTO();
        combinationVO2.setVersionId(188L);
        List<DmFocViewInfoVO> dmGroupCodeList=new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();

        dmGroupCodeList.add(dmFocViewInfoVO);

        Mockito.when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(combinationVO2);
        Mockito.when(dmFocCustomCombDao.prodTeamCodeForGeneralMonth(any())).thenReturn(dmGroupCodeList);

        List<DmCustomCombVO> dmCustomCombVOS = customCommonService.filterAnotherPageData(combinationVO, customVOList);
        Assert.assertNotNull(dmCustomCombVOS);
    }

    @Test
    public void filterAnotherPageData17Test() {
        CombinationVO combinationVO=new CombinationVO();
        List<DmCustomCombVO > customVOList=new ArrayList<>();
        combinationVO.setPageFlag("3");
        combinationVO.setGranularityType("U");
        combinationVO.setPageSymbol("MONTH");
        Set<String> set = new HashSet<>();
        set.add("ALL");
        combinationVO.setLv0DimensionSet(set);
        combinationVO.setViewFlag("4");

        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setGroupLevel("LV1");
        dmCustomCombVO.setGroupCode("1165D");
        dmCustomCombVO.setCustomId(154L);
        dmCustomCombVO.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO2 = new DmCustomCombVO();
        dmCustomCombVO2.setGroupLevel("LV2");
        dmCustomCombVO2.setGroupCode("1165D");
        dmCustomCombVO2.setCustomId(154L);
        dmCustomCombVO2.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO3 = new DmCustomCombVO();
        dmCustomCombVO3.setGroupLevel("LV3");
        dmCustomCombVO3.setGroupCode("1165D");
        dmCustomCombVO3.setCustomId(154L);
        dmCustomCombVO3.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO4 = new DmCustomCombVO();
        dmCustomCombVO4.setGroupLevel("L1");
        dmCustomCombVO4.setGroupCode("1165D");
        dmCustomCombVO4.setCustomId(154L);
        dmCustomCombVO4.setConnectCode("11126");
        DmCustomCombVO dmCustomCombVO5 = new DmCustomCombVO();
        dmCustomCombVO5.setGroupLevel("L2");
        dmCustomCombVO5.setGroupCode("1165D");
        dmCustomCombVO5.setCustomId(154L);
        dmCustomCombVO5.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO6 = new DmCustomCombVO();
        dmCustomCombVO6.setGroupLevel("DIMENSION");
        dmCustomCombVO6.setGroupCode("1165D");
        dmCustomCombVO6.setCustomId(154L);
        dmCustomCombVO6.setConnectCode("11129");
        DmCustomCombVO dmCustomCombVO7 = new DmCustomCombVO();
        dmCustomCombVO7.setGroupLevel("SUBCATEGORY");
        dmCustomCombVO7.setGroupCode("1165D");
        dmCustomCombVO7.setCustomId(154L);
        dmCustomCombVO7.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO8 = new DmCustomCombVO();
        dmCustomCombVO8.setGroupLevel("SUB_DETAIL");
        dmCustomCombVO8.setGroupCode("1165D");
        dmCustomCombVO8.setCustomId(154L);
        dmCustomCombVO8.setConnectCode("11128");
        customVOList.add(dmCustomCombVO);
        customVOList.add(dmCustomCombVO2);
        customVOList.add(dmCustomCombVO3);
        customVOList.add(dmCustomCombVO4);
        customVOList.add(dmCustomCombVO5);
        customVOList.add(dmCustomCombVO6);
        customVOList.add(dmCustomCombVO7);
        customVOList.add(dmCustomCombVO8);
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(combinationVO.getCostType(),combinationVO.getIndustryOrg());
        DmFocVersionInfoDTO combinationVO2=new DmFocVersionInfoDTO();
        combinationVO2.setVersionId(188L);
        List<DmFocViewInfoVO> dmGroupCodeList=new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();

        dmGroupCodeList.add(dmFocViewInfoVO);

        Mockito.when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(combinationVO2);
        Mockito.when(dmFocCustomCombDao.prodTeamCodeForGeneralMonth(any())).thenReturn(dmGroupCodeList);

        List<DmCustomCombVO> dmCustomCombVOS = customCommonService.filterAnotherPageData(combinationVO, customVOList);
        Assert.assertNotNull(dmCustomCombVOS);
    }

    @Test
    public void filterAnotherPageData18Test() {
        CombinationVO combinationVO=new CombinationVO();
        List<DmCustomCombVO > customVOList=new ArrayList<>();
        combinationVO.setPageFlag("ANNUAL");
        combinationVO.setViewFlag("3");
        combinationVO.setGranularityType("U");
        combinationVO.setPageSymbol("ANNUAL");
        combinationVO.setCostType("M");
        Set<String> set = new HashSet<>();
        set.add("ALL");
        combinationVO.setLv0DimensionSet(set);
        combinationVO.setViewFlag("4");

        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setGroupLevel("LV1");
        dmCustomCombVO.setGroupCode("1165D");
        dmCustomCombVO.setCustomId(154L);
        dmCustomCombVO.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO2 = new DmCustomCombVO();
        dmCustomCombVO2.setGroupLevel("LV2");
        dmCustomCombVO2.setGroupCode("1165D");
        dmCustomCombVO2.setLv1ProdRndTeamCode("111222");
        dmCustomCombVO2.setLv2ProdRndTeamCode("222233");
        dmCustomCombVO2.setCustomId(154L);
        dmCustomCombVO2.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO3 = new DmCustomCombVO();
        dmCustomCombVO3.setGroupLevel("LV3");
        dmCustomCombVO3.setLv2ProdRndTeamCode("1122223333");
        dmCustomCombVO3.setLv3ProdRndTeamCode("22223333");
        dmCustomCombVO3.setGroupCode("1165D");
        dmCustomCombVO3.setCustomId(154L);
        dmCustomCombVO3.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO4 = new DmCustomCombVO();
        dmCustomCombVO4.setGroupLevel("L1");
        dmCustomCombVO4.setGroupCode("1165D");
        dmCustomCombVO4.setCustomId(154L);
        dmCustomCombVO4.setConnectCode("11126");
        DmCustomCombVO dmCustomCombVO5 = new DmCustomCombVO();
        dmCustomCombVO5.setGroupLevel("L2");
        dmCustomCombVO5.setGroupCode("1165D");
        dmCustomCombVO5.setCustomId(154L);
        dmCustomCombVO5.setConnectCode("11122");
        DmCustomCombVO dmCustomCombVO6 = new DmCustomCombVO();
        dmCustomCombVO6.setGroupLevel("DIMENSION");
        dmCustomCombVO6.setDimensionCode("1165D");
        dmCustomCombVO6.setGroupCode("1165D");
        dmCustomCombVO6.setCustomId(154L);
        dmCustomCombVO6.setConnectCode("11129");
        DmCustomCombVO dmCustomCombVO7 = new DmCustomCombVO();
        dmCustomCombVO7.setGroupLevel("SUBCATEGORY");
        dmCustomCombVO7.setDimensionSubCategoryCode("1165D");
        dmCustomCombVO7.setGroupCode("1165D");
        dmCustomCombVO7.setCustomId(154L);
        dmCustomCombVO7.setConnectCode("11122");
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(combinationVO.getCostType(),combinationVO.getIndustryOrg());
        getCustomCombVO(customVOList);
        customVOList.add(dmCustomCombVO);
        customVOList.add(dmCustomCombVO2);
        customVOList.add(dmCustomCombVO3);
        customVOList.add(dmCustomCombVO4);
        customVOList.add(dmCustomCombVO5);
        customVOList.add(dmCustomCombVO6);
        customVOList.add(dmCustomCombVO7);

        DmFocVersionInfoDTO combinationVO2=new DmFocVersionInfoDTO();
        combinationVO2.setVersionId(188L);
        List<DmFocViewInfoVO> dmGroupCodeList=new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();

        dmGroupCodeList.add(dmFocViewInfoVO);

        Mockito.when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(combinationVO2);
        Mockito.when(dmFocCustomCombDao.prodTeamCodeForGeneralMonth(any())).thenReturn(dmGroupCodeList);

        List<DmCustomCombVO> dmCustomCombVOS = customCommonService.filterAnotherPageData(combinationVO, customVOList);
        Assert.assertNotNull(dmCustomCombVOS);
    }

    @NotNull
    private void getCustomCombVO(List<DmCustomCombVO> customVOList) {
        DmCustomCombVO dmCustomCombVO8 = new DmCustomCombVO();
        dmCustomCombVO8.setGroupLevel("SUB_DETAIL");
        dmCustomCombVO8.setDimensionSubDetailCode("11265D");
        dmCustomCombVO8.setGroupCode("11265D");
        dmCustomCombVO8.setCustomId(154L);
        dmCustomCombVO8.setConnectCode("11128");
        customVOList.add(dmCustomCombVO8);

        DmCustomCombVO dmCustomCombVO9 = new DmCustomCombVO();
        dmCustomCombVO9.setGroupLevel("SHIPPING_OBJECT");
        dmCustomCombVO9.setGroupCode("116533D");
        dmCustomCombVO9.setCustomId(154L);
        dmCustomCombVO9.setConnectCode("11128");
        customVOList.add(dmCustomCombVO9);

        DmCustomCombVO dmCustomCombVO10 = new DmCustomCombVO();
        dmCustomCombVO10.setGroupLevel("MANUFACTURE_OBJECT");
        dmCustomCombVO10.setGroupCode("11625D");
        dmCustomCombVO10.setCustomId(154L);
        dmCustomCombVO10.setConnectCode("112128");
        customVOList.add(dmCustomCombVO10);

        DmCustomCombVO dmCustomCombVO99 = new DmCustomCombVO();
        dmCustomCombVO99.setGroupLevel("L1");
        dmCustomCombVO99.setL1Name("名称11");
        dmCustomCombVO99.setGroupCode("1165333D");
        dmCustomCombVO99.setCustomId(154L);
        dmCustomCombVO99.setConnectCode("112128");
        customVOList.add(dmCustomCombVO99);

        DmCustomCombVO dmCustomCombVO999 = new DmCustomCombVO();
        dmCustomCombVO999.setGroupLevel("L2");
        dmCustomCombVO999.setL1Name("名称222");
        dmCustomCombVO999.setGroupCode("1165333D");
        dmCustomCombVO999.setCustomId(154L);
        dmCustomCombVO999.setConnectCode("111228");
        customVOList.add(dmCustomCombVO999);
    }

    @Test
    public void getManufactureDbListForAllConditionTest() throws Exception {
        String granularityPageSymbol = "";
        CommonViewVO commonViewVO = new CommonViewVO();
        List<DmFocViewInfoVO> allGroupCodeList = new ArrayList<>();
        Method method = PowerMockito.method(CustomCommonService.class, "getManufactureDbListForAllCondition", String.class, CommonViewVO.class, List.class);
        method.invoke(customCommonService, granularityPageSymbol, commonViewVO, allGroupCodeList);

        granularityPageSymbol = "U_MONTH";
        method.invoke(customCommonService, granularityPageSymbol, commonViewVO, allGroupCodeList);

        granularityPageSymbol = "U_ANNUAL";
        method.invoke(customCommonService, granularityPageSymbol, commonViewVO, allGroupCodeList);

        granularityPageSymbol = "P_MONTH";
        method.invoke(customCommonService, granularityPageSymbol, commonViewVO, allGroupCodeList);

        granularityPageSymbol = "P_ANNUAL";
        method.invoke(customCommonService, granularityPageSymbol, commonViewVO, allGroupCodeList);

        granularityPageSymbol = "D_MONTH";
        method.invoke(customCommonService, granularityPageSymbol, commonViewVO, allGroupCodeList);

        granularityPageSymbol = "D_ANNUAL";
        method.invoke(customCommonService, granularityPageSymbol, commonViewVO, allGroupCodeList);
        Assert.assertTrue(true);
    }

    @Test
    public void getCombMethodTest() throws Exception {
        CombinationVO combinationVO = new CombinationVO();
        List<DmCustomCombVO> customVOList = new ArrayList<>();
        DmCustomCombVO dmCustomCombVO = new DmCustomCombVO();
        dmCustomCombVO.setLv1ProdRndTeamCode("111");
        dmCustomCombVO.setLv2ProdRndTeamCode("222");
        customVOList.add(dmCustomCombVO);
        combinationVO.setGranularityType("D");
        combinationVO.setIndustryOrg("ENERGY");
        Whitebox.invokeMethod(customCommonService, "setProdTeamAndDimesionCode", combinationVO,customVOList);

        combinationVO.setIndustryOrg("ICT");
        Whitebox.invokeMethod(customCommonService, "setProdTeamAndDimesionCode", combinationVO,customVOList);
        boolean condition= false;
        CommonViewVO commonViewVO = new CommonViewVO();
        commonViewVO.setGroupLevel("LV4");
        commonViewVO.setViewFlag("7");
        commonViewVO.setGranularityType("U");
        List<DmFocViewInfoVO> allGroupCodeList = new ArrayList<>();

        Whitebox.invokeMethod(customCommonService, "prodTeamCodeWithLv4", commonViewVO,allGroupCodeList, condition);

        boolean condition2 = true;
        commonViewVO.setFilterGroupLevel("LV4");
        commonViewVO.setViewFlag("7");
        commonViewVO.setGranularityType("U");

        Whitebox.invokeMethod(customCommonService, "prodTeamCodeWithLv4", commonViewVO,allGroupCodeList, condition2);

        commonViewVO.setFilterGroupLevel("LV4");
        commonViewVO.setViewFlag("7");
        commonViewVO.setGranularityType("U");
        commonViewVO.setCostType("T");
        commonViewVO.setGranularityPageSymbol("U_MONTH");
        Whitebox.invokeMethod(customCommonService, "prodTeamCodeWithLv4", commonViewVO,allGroupCodeList, condition);

        commonViewVO.setFilterGroupLevel("LV4");
        commonViewVO.setViewFlag("12");
        commonViewVO.setGranularityType("D");
        commonViewVO.setIndustryOrg("IAS");
        commonViewVO.setCostType("P");
        commonViewVO.setGranularityPageSymbol("D_MONTH");
        Whitebox.invokeMethod(customCommonService, "prodTeamCodeWithLv4", commonViewVO,allGroupCodeList, condition);
        CommonViewVO commonViewVO2 = new CommonViewVO();
        commonViewVO2.setKeyWord("hhah");
        commonViewVO2.setCostType("P");
        commonViewVO2.setGroupLevel("CEG");
        commonViewVO2.setFilterGroupLevel("CEG");
        commonViewVO2.setGranularityPageSymbol("D_MONTH");
        boolean flag = false;
        Whitebox.invokeMethod(customCommonService, "getGroupCodeForPurchaseLevel", commonViewVO2,allGroupCodeList, flag);

        commonViewVO2.setKeyWord("hhah");
        commonViewVO2.setCostType("P");
        commonViewVO2.setGroupLevel("MODL");
        commonViewVO2.setFilterGroupLevel("MODL");
        boolean flag2 = true;
        Whitebox.invokeMethod(customCommonService, "getGroupCodeForPurchaseLevel", commonViewVO2,allGroupCodeList, flag2);

        commonViewVO2.setKeyWord("hhah");
        commonViewVO2.setCostType("P");
        commonViewVO2.setExpandFlag("Y");
        commonViewVO2.setGroupLevel("CATEGORY");
        commonViewVO2.setFilterGroupLevel("CATEGORY");
        boolean flag3 = false;
        Whitebox.invokeMethod(customCommonService, "getGroupCodeForPurchaseLevel", commonViewVO2,allGroupCodeList, flag3);

        boolean flag4 = false;
        boolean flag5 = false;
        commonViewVO2.setGroupLevel("SPART");
        commonViewVO2.setGranularityType("D");
        commonViewVO2.setViewFlag("12");
        Whitebox.invokeMethod(customCommonService, "dimensionSpartCoaGroupLevelList", commonViewVO2,allGroupCodeList, flag4, flag5);
        commonViewVO2.setGroupLevel(null);
        commonViewVO2.setFilterGroupLevel("SPART");
        Whitebox.invokeMethod(customCommonService, "dimensionSpartCoaGroupLevelList", commonViewVO2,allGroupCodeList, flag4, flag5);

        commonViewVO2.setGroupLevel("COA");
        commonViewVO2.setIndustryOrg("ENERGY");
        Whitebox.invokeMethod(customCommonService, "dimensionSpartCoaGroupLevelList", commonViewVO2,allGroupCodeList, flag4, true);

        commonViewVO2.setFilterGroupLevel("COA");
        commonViewVO2.setGroupLevel(null);
        commonViewVO2.setIndustryOrg("ENERGY");
        Whitebox.invokeMethod(customCommonService, "dimensionSpartCoaGroupLevelList", commonViewVO2,allGroupCodeList, false, true);
        String granularityPageSymbol = "D_ANNUAL";
        commonViewVO2.setIndustryOrg("ICT");
        Whitebox.invokeMethod(customCommonService, "getManufactureDbListForAllCondition", granularityPageSymbol,commonViewVO2,allGroupCodeList);
        String granularityPageSymbol2 = "D_MONTH";
        Whitebox.invokeMethod(customCommonService, "getManufactureDbListForAllCondition", granularityPageSymbol2,commonViewVO2,allGroupCodeList);
        Assert.assertNull(null);
    }

    @Test
    public void getComb2MethodTest() throws Exception {
        String granularityPageSymbol = "U_MONTH";
        CommonViewVO commonViewVO = new CommonViewVO();
        List<DmFocViewInfoVO> allGroupCodeList = new ArrayList<>();
        Whitebox.invokeMethod(customCommonService, "getPurchaseDbListForAllCondition", granularityPageSymbol,commonViewVO,allGroupCodeList);

        String granularityPageSymbol2 = "U_ANNUAL";
        Whitebox.invokeMethod(customCommonService, "getPurchaseDbListForAllCondition", granularityPageSymbol2,commonViewVO,allGroupCodeList);

        String granularityPageSymbol3 = "P_MONTH";
        Whitebox.invokeMethod(customCommonService, "getPurchaseDbListForAllCondition", granularityPageSymbol3,commonViewVO,allGroupCodeList);

        String granularityPageSymbol4 = "P_ANNUAL";
        Whitebox.invokeMethod(customCommonService, "getPurchaseDbListForAllCondition", granularityPageSymbol4,commonViewVO,allGroupCodeList);

        String granularityPageSymbol5 = "D_ANNUAL";
        commonViewVO.setIndustryOrg("ICT");
        Whitebox.invokeMethod(customCommonService, "getPurchaseDbListForAllCondition", granularityPageSymbol5,commonViewVO,allGroupCodeList);

        commonViewVO.setIndustryOrg("ENERGY");
        Whitebox.invokeMethod(customCommonService, "getPurchaseDbListForAllCondition", granularityPageSymbol5,commonViewVO,allGroupCodeList);

        String granularityPageSymbol6 = "D_MONTH";
        commonViewVO.setIndustryOrg("ICT");
        Whitebox.invokeMethod(customCommonService, "getPurchaseDbListForAllCondition", granularityPageSymbol6,commonViewVO,allGroupCodeList);

        Whitebox.invokeMethod(customCommonService, "getTotalDbListForAllCondition", granularityPageSymbol3,commonViewVO,allGroupCodeList);

        Whitebox.invokeMethod(customCommonService, "getTotalDbListForAllCondition", granularityPageSymbol4,commonViewVO,allGroupCodeList);

        Whitebox.invokeMethod(customCommonService, "getTotalDbListForAllCondition", granularityPageSymbol5,commonViewVO,allGroupCodeList);
        commonViewVO.setIndustryOrg("ICT");
        Whitebox.invokeMethod(customCommonService, "getTotalDbListForAllCondition", granularityPageSymbol6,commonViewVO,allGroupCodeList);

        commonViewVO.setIndustryOrg("ENERGY");
        Whitebox.invokeMethod(customCommonService, "getTotalDbListForAllCondition", granularityPageSymbol6,commonViewVO,allGroupCodeList);

    }
}