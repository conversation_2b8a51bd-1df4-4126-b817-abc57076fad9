/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.config;

import com.huawei.it.fcst.industry.index.dao.IDmFocVersionDao;
import com.huawei.it.fcst.industry.index.dao.IFomManufactureDimDao;
import com.huawei.it.fcst.industry.index.enums.IndustryConst;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoDTO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoVO;
import com.huawei.it.fcst.industry.index.vo.config.FomManufactureDimVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.security.UserVO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * ConfigReviewServiceTest Class
 *
 * <AUTHOR>
 * @since 2023/10/26
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class ConfigReviewServiceTest {
    private static final Logger LOGGER = LoggerFactory.getLogger(ConfigReviewService.class);

    @InjectMocks
    private ConfigReviewService configReviewService;

    @Mock
    private IDmFocVersionDao dmFocVersionDao;

    @Mock
    private IFomManufactureDimDao fomManufactureDimDao;

    @Before
    public void before() throws Exception {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);
    }

    @Test
    public void findVersion() throws CommonApplicationException {
        DmFocVersionInfoVO dmFocVersionInfoVo = new DmFocVersionInfoVO();
        dmFocVersionInfoVo.setDataType("ITEM");
        ResultDataVO version = configReviewService.findVersion(dmFocVersionInfoVo);
        Assert.assertNotNull(version);
    }

    @Test
    public void findVersion2Test() throws CommonApplicationException {
        DmFocVersionInfoVO dmFocVersionInfoVo = new DmFocVersionInfoVO();
        dmFocVersionInfoVo.setDataType("DIMENSION");
        ResultDataVO version = configReviewService.findVersion(dmFocVersionInfoVo);
        Assert.assertNotNull(version);
    }

    @Test
    public void getManufactureByVersionId() {
        FomManufactureDimVO fomManufactureDimVO = new FomManufactureDimVO();
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configReviewService.getManufactureByVersionId(fomManufactureDimVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void getManufactureByVersionId2Test() {
        FomManufactureDimVO fomManufactureDimVO = new FomManufactureDimVO();
        fomManufactureDimVO.setVersionId(15L);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configReviewService.getManufactureByVersionId(fomManufactureDimVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void getManufactureByVersionId3Test() {
        FomManufactureDimVO fomManufactureDimVO = new FomManufactureDimVO();
        fomManufactureDimVO.setVersionId(15L);
        fomManufactureDimVO.setDataType("DIMENSION");
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configReviewService.getManufactureByVersionId(fomManufactureDimVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void getManufactureByVersionId4Test() {
        FomManufactureDimVO fomManufactureDimVO = new FomManufactureDimVO();
        fomManufactureDimVO.setVersionId(15L);
        fomManufactureDimVO.setDataType("ITEM");
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configReviewService.getManufactureByVersionId(fomManufactureDimVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void dimensionList() {
        FomManufactureDimVO fomManufactureDimVO = new FomManufactureDimVO();
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configReviewService.dimensionList(fomManufactureDimVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void dimensionList2Test() {
        FomManufactureDimVO fomManufactureDimVO = new FomManufactureDimVO();
        fomManufactureDimVO.setVersionId(20L);
        fomManufactureDimVO.setPageIndex(1);
        fomManufactureDimVO.setPageSize(20);
        ResultDataVO resultDataVO = new ResultDataVO();
        PagedResult<FomManufactureDimVO> byPage=new PagedResult<>();
        List<FomManufactureDimVO> list = new ArrayList<>();
        FomManufactureDimVO manufactureDimVO = new FomManufactureDimVO();
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(15);
        manufactureDimVO.setManufactureBu("66");
        list.add(manufactureDimVO);
        byPage.setResult(list);
        byPage.setPageVO(pageVO);

        when(fomManufactureDimDao.findManufactureByPage(any(),any())).thenReturn(byPage);
        try {
            resultDataVO = configReviewService.dimensionList(fomManufactureDimVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }
}