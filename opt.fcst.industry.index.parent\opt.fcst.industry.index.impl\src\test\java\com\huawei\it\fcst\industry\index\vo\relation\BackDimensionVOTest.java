/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.relation;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * BackDimensionVOTest Class
 *
 * <AUTHOR>
 * @since 2023/4/23
 */
public class BackDimensionVOTest extends BaseVOCoverUtilsTest<BackDimensionVO> {

    @Override
    protected Class<BackDimensionVO> getTClass() {
        return BackDimensionVO.class;
    }

    @Test
    public void testMethod() {
        BackDimensionVO dimensionParamVO = new BackDimensionVO();
        List<DmDimCatgModlCegIctVO> errorList=new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setL3CegCnName("5502");
        dimensionParamVO.setErrorList(errorList);
        dimensionParamVO.getErrorList();
        Assert.assertNotNull(dimensionParamVO);
    }
}