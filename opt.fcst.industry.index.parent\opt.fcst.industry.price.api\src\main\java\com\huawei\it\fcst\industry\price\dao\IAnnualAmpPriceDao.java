/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.dao;

import com.huawei.it.fcst.industry.price.vo.annual.AnnualAnalysisVO;
import com.huawei.it.fcst.industry.price.vo.annual.DmFocAnnualAmpVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * IAnnualAmpPriceDao Class
 *
 * <AUTHOR>
 * @since 2024/11/6
 */
public interface IAnnualAmpPriceDao {

    List<DmFocAnnualAmpVO> allAmpNormalCost(AnnualAnalysisVO annualAnalysisVO);

    List<String> getPeriodYearList(@Param("versionId") Long versionId);

    List<DmFocAnnualAmpVO> findAmpGroupCodeOrderByWeight(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> multiAmpNormalChart(AnnualAnalysisVO annualAnalysisVO);

    List<DmFocAnnualAmpVO> industryNormalCostList(AnnualAnalysisVO annualAnalysisVO);
}
