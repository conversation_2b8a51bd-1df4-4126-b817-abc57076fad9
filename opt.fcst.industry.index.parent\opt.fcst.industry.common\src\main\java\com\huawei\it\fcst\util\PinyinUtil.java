/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.it.fcst.util;

import com.hankcs.hanlp.dictionary.py.Pinyin;
import com.hankcs.hanlp.dictionary.py.PinyinDictionary;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/21
 */
public class PinyinUtil {
    /**
     * @param chineseInput 参数
     * @return String
     */
    public static String chineseToPingyin(String chineseInput) {
        List<Pinyin> pinyinList = PinyinDictionary.convertToPinyin(chineseInput);
        StringBuilder stringBuilder = new StringBuilder();
        pinyinList.stream().forEach(pinyin->{
            // 输出拼音全部小写,不带声调
            stringBuilder.append(pinyin.getPinyinWithoutTone());
        });
        return stringBuilder.substring(0, 1);
    }
}

