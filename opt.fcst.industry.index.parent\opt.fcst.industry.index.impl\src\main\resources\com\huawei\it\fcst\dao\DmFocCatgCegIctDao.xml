<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IDmFocCatgCegIctDao">
    <resultMap type="com.huawei.it.fcst.industry.index.vo.config.DmFocCatgCegIctDTO" id="resultMap">
        <result property="delFlag" column="del_flag"/>
        <result property="versionId" column="version_id"/>
        <result property="categoryCode" column="category_code"/>
        <result property="categoryCnName" column="category_cn_name"/>
        <result property="l3CegCnName" column="l3_ceg_cn_name"/>
        <result property="l3CegCode" column="l3_ceg_code"/>
        <result property="l3CegShortCnName" column="l3_ceg_short_cn_name"/>
        <result property="l4CegCnName" column="l4_ceg_cn_name"/>
        <result property="l4CegCode" column="l4_ceg_code"/>
        <result property="l4CegShortCnName" column="l4_ceg_short_cn_name"/>
        <result property="createdBy" column="created_by"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedDate" column="last_updated_date"/>
    </resultMap>

    <sql id="allFields">
        del_flag ,
        version_id,
        l3_ceg_code,
        l3_ceg_cn_name,
        l3_ceg_short_cn_name,
        l4_ceg_code,
        l4_ceg_cn_name,
        l4_ceg_short_cn_name,
        category_code,
        category_cn_name,
        created_by,
        creation_date,
        last_updated_by,
        last_updated_date
    </sql>

    <sql id="allValue">
        #{delFlag,jdbcType=VARCHAR},
        #{versionId,jdbcType=NUMERIC},
        #{l3CegCode,jdbcType=VARCHAR},
        #{l3CegCnName,jdbcType=VARCHAR},
        #{l3CegShortCnName,jdbcType=VARCHAR},
        #{categoryCode,jdbcType=VARCHAR},
        #{categoryCnName,jdbcType=VARCHAR},
        #{createdBy,jdbcType=VARCHAR},
        #{creationDate,jdbcType=TIMESTAMP},
        #{lastUpdatedBy,jdbcType=VARCHAR},
        #{lastUpdatedDate,jdbcType=TIMESTAMP}
    </sql>

    <sql id="allValues">
        #{item.delFlag,jdbcType=VARCHAR},
        #{item.versionId,jdbcType=NUMERIC},
        #{item.l3CegCode,jdbcType=VARCHAR},
        #{item.l3CegCnName,jdbcType=VARCHAR},
        #{item.l3CegShortCnName,jdbcType=VARCHAR},
        #{item.l4CegCode,jdbcType=VARCHAR},
        #{item.l4CegCnName,jdbcType=VARCHAR},
        #{item.l4CegShortCnName,jdbcType=VARCHAR},
        #{item.categoryCode,jdbcType=VARCHAR},
        #{item.categoryCnName,jdbcType=VARCHAR},
        #{item.createdBy,jdbcType=VARCHAR},
        #{item.creationDate,jdbcType=TIMESTAMP},
        #{item.lastUpdatedBy,jdbcType=VARCHAR},
        #{item.lastUpdatedDate,jdbcType=TIMESTAMP}
    </sql>

    <sql id="uniqueKeyField">
        version_id=#{versionId,jdbcType=NUMERIC}
    </sql>

    <sql id="setValues">
        <if test='delFlag != null'>
            del_flag = #{delFlag,jdbcType=VARCHAR},
        </if>
        <if test='lastUpdatedBy != null'>
            last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
        </if>
        <if test='versionType != null'>
            version_type = #{versionType,jdbcType=VARCHAR},
        </if>
        <if test='parentVersionId != null'>
            parent_version_id = #{parentVersionId,jdbcType=NUMERIC},
        </if>
        <if test='dataType != null'>
            data_type = #{dataType,jdbcType=VARCHAR},
        </if>
        <if test='versionId != null'>
            version_id = #{versionId,jdbcType=NUMERIC},
        </if>
        <if test='creationDate != null'>
            creation_date = #{creationDate,jdbcType=TIMESTAMP},
        </if>
        <if test='version != null'>
            version = #{version,jdbcType=VARCHAR},
        </if>
        <if test='createdBy != null'>
            created_by = #{createdBy,jdbcType=VARCHAR},
        </if>
        <if test='status != null'>
            status = #{status,jdbcType=NUMERIC},
        </if>
        <if test='lastUpdateDate != null'>
            last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
        </if>
        <if test='step != null'>
            step = #{step,jdbcType=INTEGER},
        </if>
    </sql>

    <sql id="searchFields">
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='dimensionParamVO.delFlag != null'>
                AND del_flag=#{dimensionParamVO.delFlag,jdbcType=VARCHAR}
            </if>
            <if test='dimensionParamVO.lastUpdatedBy != null'>
                AND last_updated_by=#{dimensionParamVO.lastUpdatedBy,jdbcType=VARCHAR}
            </if>
            <if test='dimensionParamVO.versionId != null'>
                AND version_id=#{dimensionParamVO.versionId,jdbcType=NUMERIC}
            </if>
            <if test='dimensionParamVO.l3CegCode != null and dimensionParamVO.l3CegCode!=""'>
                AND l3_ceg_code = #{dimensionParamVO.l3CegCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionParamVO.l4CegCode != null and dimensionParamVO.l4CegCode!=""'>
                AND l4_ceg_code = #{dimensionParamVO.l4CegCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionParamVO.creationDate != null'>
                AND creation_date=#{dimensionParamVO.creationDate,jdbcType=TIMESTAMP}
            </if>
            <if test='dimensionParamVO.createdBy != null'>
                AND created_by=#{dimensionParamVO.createdBy,jdbcType=VARCHAR}
            </if>
            <if test='dimensionParamVO.lastUpdateDate != null'>
                AND last_update_date=#{dimensionParamVO.lastUpdateDate,jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <select id="getL3ListByVersionId" resultMap="resultMap">
        SELECT
        <choose>
            <when test='l3CegCode != null and l3CegCode!=""'>
                DISTINCT l4_ceg_code,l4_ceg_cn_name
            </when>
            <otherwise>
                DISTINCT l3_ceg_code,l3_ceg_cn_name
            </otherwise>
        </choose>
        FROM
            fin_dm_opt_foi.${tablePreFix}_catg_ceg_ict_d
        WHERE
            del_flag = 'N'
        <if test='versionId != null and versionId!=""'>
            AND version_id=#{versionId,jdbcType=NUMERIC}
        </if>
        <if test='l3CegCode != null and l3CegCode!=""'>
            AND l3_ceg_code=#{l3CegCode,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="findDmFocCatgCegIctStatus" resultMap="resultMap">
        SELECT DISTINCT
        <include refid="allFields"/>
        FROM
            fin_dm_opt_foi.${tablePreFix}_catg_ceg_ict_d
        WHERE
            del_flag = 'N'
          AND version_id=#{versionId,jdbcType=NUMERIC}
    </select>

    <select id="findByPage" resultMap="resultMap">
        SELECT
        <include refid="allFields"/>
        FROM fin_dm_opt_foi.${dimensionParamVO.tablePreFix}_catg_ceg_ict_d
        <include refid="searchFields"/>
        LIMIT #{pageVO.pageSize} OFFSET  #{pageVO.startIndex}-1
    </select>

    <select id="findByPageCount" resultType="int">
        SELECT COUNT(1) from
        (
        SELECT
        <include refid="allFields"/>
        FROM fin_dm_opt_foi.${dimensionParamVO.tablePreFix}_catg_ceg_ict_d
        <include refid="searchFields"/>
            )
    </select>

    <select id="findL3ListByKeyword" resultMap="resultMap">
        SELECT DISTINCT
        <choose>
            <when test='l3CegCode != null and l3CegCode!=""'>
                 n1.l4_ceg_code,n1.l4_ceg_cn_name,n3.l4_ceg_short_cn_name
            </when>
            <otherwise>
                 n1.l3_ceg_code,n1.l3_ceg_cn_name,n3.l3_ceg_short_cn_name
            </otherwise>
        </choose>
        FROM
        DMDIM.DM_DIM_CEG_D n1
        LEFT JOIN (
        SELECT
        <choose>
            <when test='l3CegCode != null and l3CegCode!=""'>
                DISTINCT l4_ceg_code,l4_ceg_cn_name,l4_ceg_short_cn_name
            </when>
            <otherwise>
                DISTINCT l3_ceg_code,l3_ceg_cn_name,l3_ceg_short_cn_name
            </otherwise>
        </choose>
        FROM fin_dm_opt_foi.${tablePreFix}_catg_ceg_ict_d n2
        WHERE
        n2.del_flag = 'N'
        <if test='versionId != null and versionId!=""'>
            AND n2.version_id = #{versionId,jdbcType=VARCHAR}
        </if>
        <if test='l3CegCode != null and l3CegCode!=""'>
            AND n2.l3_ceg_code=#{l3CegCode,jdbcType=VARCHAR}
        </if>
        ) n3
        ON
        <choose>
            <when test='l3CegCode != null and l3CegCode!=""'>
                n1.l4_ceg_cn_name = n3.l4_ceg_cn_name
            </when>
            <otherwise>
                n1.l3_ceg_cn_name = n3.l3_ceg_cn_name
            </otherwise>
        </choose>
        WHERE
        n1.del_flag = 'N'
        AND n1.ceg_level = #{cegLevel,jdbcType=VARCHAR}
        <if test='l3CegCode != null and l3CegCode!=""'>
            AND n1.l3_ceg_code = #{l3CegCode,jdbcType=VARCHAR}
        </if>
        <if test="keyword != null and keyword != ''">
            AND UPPER ( n1.l3_ceg_cn_name ) LIKE CONCAT ( CONCAT ( '%', UPPER (#{keyword})) ,'%')
        </if>
    </select>

    <update id="updateDmFocCatgCegIctList" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
        UPDATE fin_dm_opt_foi.${item.tablePreFix}_catg_ceg_ict_d
        <set>
            <if test='item.delFlag != null'>
                del_flag = #{item.delFlag,jdbcType=VARCHAR},
            </if>
            <if test='item.categoryCnName != null'>
                category_cn_name = #{item.categoryCnName,jdbcType=VARCHAR},
            </if>
            <if test='item.l3CegCnName != null'>
                l3_ceg_cn_name = #{item.l3CegCnName,jdbcType=VARCHAR},
            </if>
            <if test='item.l3CegShortCnName != null'>
                l3_ceg_short_cn_name = #{item.l3CegShortCnName,jdbcType=VARCHAR},
            </if>
            <if test='item.l3CegCode != null'>
                l3_ceg_code = #{item.l3CegCode,jdbcType=VARCHAR},
            </if>
            <if test='item.l4CegCnName != null'>
                l4_ceg_cn_name = #{item.l4CegCnName,jdbcType=VARCHAR},
            </if>
            <if test='item.l4CegShortCnName != null'>
                l4_ceg_short_cn_name = #{item.l4CegShortCnName,jdbcType=VARCHAR},
            </if>
            <if test='item.l4CegCode != null'>
                l4_ceg_code = #{item.l4CegCode,jdbcType=VARCHAR},
            </if>
            <if test='item.creationDate != null'>
                creation_date = #{item.creationDate,jdbcType=TIMESTAMP},
            </if>
            <if test='item.createdBy != null'>
                created_by = #{item.createdBy,jdbcType=VARCHAR},
            </if>
            <if test='item.lastUpdatedBy != null'>
                last_updated_by = #{item.lastUpdatedBy,jdbcType=VARCHAR},
            </if>
            <if test='item.lastUpdatedDate != null'>
                last_updated_date = #{item.lastUpdatedDate,jdbcType=TIMESTAMP},
            </if>
        </set>
        WHERE
        version_id = #{item.versionId,jdbcType=NUMERIC}
        and category_code = #{item.categoryCode,jdbcType=VARCHAR}
        </foreach>
    </update>

    <insert id="createDmFocCatgCegIctLists" parameterType="java.util.List">
        INSERT INTO fin_dm_opt_foi.${tablePreFix}_catg_ceg_ict_d
        (<include refid="allFields"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (<include refid="allValues"/>)
        </foreach>
    </insert>

</mapper>
