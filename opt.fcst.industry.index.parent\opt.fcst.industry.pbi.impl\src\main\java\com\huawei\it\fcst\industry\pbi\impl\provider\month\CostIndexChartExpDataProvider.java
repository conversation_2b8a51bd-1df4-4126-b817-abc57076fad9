/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.provider.month;

import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.constant.Constant;
import com.huawei.it.fcst.industry.pbi.dao.IctMonthCostIdxDao;
import com.huawei.it.fcst.industry.pbi.enums.IndustryConstEnum;
import com.huawei.it.fcst.export.IExcelExportDataProvider;
import com.huawei.it.fcst.industry.pbi.service.month.IIctMonthAnalysisService;
import com.huawei.it.fcst.industry.pbi.utils.FcstIndustryUtil;
import com.huawei.it.fcst.export.vo.ExcelExportContext;
import com.huawei.it.fcst.export.vo.ExportList;
import com.huawei.it.fcst.industry.pbi.vo.month.IctMonthAnalysisVO;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;

import javax.inject.Inject;
import javax.inject.Named;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 产业成本指数图导出数据提供类
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
@Slf4j
@Named("IExcelExport.CostIndexChartExpDataProvider")
public class CostIndexChartExpDataProvider implements IExcelExportDataProvider {

    @Inject
    private IIctMonthAnalysisService monthAnalysisService;

    @Inject
    private IctMonthCostIdxDao monthCostIdxDao;

    @Override
    public List<?> getData(Serializable conditionObject, PageVO pageVO) throws ApplicationException, InterruptedException {
        log.info(">>>Begin CostIndexChartExpDataProvider::getData");
        IctMonthAnalysisVO monthAnalysisVO = (IctMonthAnalysisVO)conditionObject;
        IctMonthAnalysisVO paramsVO = new IctMonthAnalysisVO();
        BeanUtils.copyProperties(monthAnalysisVO, paramsVO);
        // 对比分析参数处理
        ResultDataVO industryCostIndexChartVO = new ResultDataVO();
        if (paramsVO.getCompareAnalysisVO().getIsCompareFlag()) {
            List<IctMonthAnalysisVO> compareMonthVO = paramsVO.getCompareAnalysisVO().getCompareMonthVO();
            if (CollectionUtils.isNotEmpty(compareMonthVO)) {
                paramsVO.setBasePeriodId(compareMonthVO.get(0).getBasePeriodId());
                industryCostIndexChartVO = monthAnalysisService.getIndustryCompareIndexChart(compareMonthVO);
            }
        } else {
            industryCostIndexChartVO = monthAnalysisService.getIndustryCostIndexChart(paramsVO);
        }
        List<IctMonthAnalysisVO> dataList = (List<IctMonthAnalysisVO>) industryCostIndexChartVO.getData();
        dataList.stream().forEach(ele ->ele.setCostIndex(null == ele.getCostIndex() ? null : new BigDecimal(ele.getCostIndex()).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue()));
        dataList = Optional.ofNullable(dataList).orElse(new ArrayList<>());
        ExportList exportList = new ExportList();
        exportList.addAll(dataList);
        exportList.setTotalRows(dataList.size());
        return exportList;
    }

    @Override
    public Map<String, Object> getHeader(Serializable conditionObject, Map<String, Object> parameters) throws ApplicationException {
        ExcelExportContext context = (ExcelExportContext)conditionObject;
        IctMonthAnalysisVO monthAnalysisChartVO = (IctMonthAnalysisVO)context.getConditionObject();
        // 自定义表头
        Map<String,Object> headMap = new HashMap<>();
        headMap.put("basePeriodId", FcstIndustryUtil.getActualMonth(monthAnalysisChartVO.getBasePeriodId().toString()));
        List<String> compareCnNameList = new ArrayList<>();
        if (monthAnalysisChartVO.getCompareAnalysisVO().getIsCompareFlag()) {
            List<IctMonthAnalysisVO> compareMonthVO = monthAnalysisChartVO.getCompareAnalysisVO().getCompareMonthVO();
            if (CollectionUtils.isNotEmpty(compareMonthVO)) {
                for (IctMonthAnalysisVO compareMonthAnalysisVO : compareMonthVO) {
                    compareCnNameList.addAll(compareMonthAnalysisVO.getGroupCnNameList());
                }
                String displayName = Constant.StrEnum.COMPARE_FLAG.getValue() + "(" + compareCnNameList.stream().map(String::valueOf).collect(Collectors.joining(",")) + ")";
                monthAnalysisChartVO.setDisplayNameIndex(displayName);
                monthAnalysisChartVO.setGroupCnNameIndex("各层级");
            }
            headMap.put("displayName", monthAnalysisChartVO.getDisplayNameIndex());
            headMap.put("groupCnName", monthAnalysisChartVO.getGroupCnNameIndex());
        } else {
            headMap.put("displayName", monthAnalysisChartVO.getDisplayName());
            headMap.put("groupCnName", monthAnalysisChartVO.getGroupCnName());
        }
        headMap.put("costType", monthAnalysisChartVO.getCostType());
        headMap.put("granularityTypeCnName", monthAnalysisChartVO.getGranularityTypeCnName());
        headMap.put("overseaFlagCnName", monthAnalysisChartVO.getOverseaFlagCnName());
        headMap.put("bgCnName", monthAnalysisChartVO.getBgCnName());
        headMap.put("actualMonth", FcstIndustryUtil.getActualMonth(monthCostIdxDao.findActualMonth(monthAnalysisChartVO)));
        headMap.put("regionCnName", monthAnalysisChartVO.getRegionCnName());
        headMap.put("repofficeCnName", monthAnalysisChartVO.getRepofficeCnName());
        headMap.put("mainFlagCnName", monthAnalysisChartVO.getMainFlagCnName());
        headMap.put("codeAttributes", monthAnalysisChartVO.getCodeAttributes());
        headMap.put("softwareMarkStr", "PSP".equals(monthAnalysisChartVO.getCostType()) ? CommonConstant.SOFTWARE_MARK + IndustryConstEnum.getSoftwareMark(monthAnalysisChartVO.getSoftwareMark()).getDesc() : "");
        return headMap;
    }
}