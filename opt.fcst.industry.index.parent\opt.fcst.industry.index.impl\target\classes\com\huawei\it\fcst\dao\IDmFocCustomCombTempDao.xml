<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IDmFocCustomCombTempDao">
    <resultMap type="com.huawei.it.fcst.industry.index.vo.combination.DmCustomCombVO" id="customResultMap">
        <result property="id" column="id"/>
        <result property="count" column="count"/>
        <result property="pageFlag" column="page_flag"/>
        <result property="lv0ProdRndTeamCode" column="LV0_PROD_RND_TEAM_CODE"/>
        <result property="lv0ProdRdTeamCnName" column="LV0_PROD_RD_TEAM_CN_NAME"/>
        <result property="lv1ProdRndTeamCode" column="LV1_PROD_RND_TEAM_CODE"/>
        <result property="lv1ProdRdTeamCnName" column="LV1_PROD_RD_TEAM_CN_NAME"/>
        <result property="lv2ProdRndTeamCode" column="LV2_PROD_RND_TEAM_CODE"/>
        <result property="lv2ProdRdTeamCnName" column="LV2_PROD_RD_TEAM_CN_NAME"/>
        <result property="lv3ProdRndTeamCode" column="LV3_PROD_RND_TEAM_CODE"/>
        <result property="lv3ProdRdTeamCnName" column="LV3_PROD_RD_TEAM_CN_NAME"/>
        <result property="lv4ProdRndTeamCode" column="LV4_PROD_RND_TEAM_CODE"/>
        <result property="lv4ProdRdTeamCnName" column="LV4_PROD_RD_TEAM_CN_NAME"/>
        <result property="l1Name" column="l1_name"/>
        <result property="l2Name" column="l2_name"/>
        <result property="dimensionCode" column="dimension_code"/>
        <result property="dimensionCnName" column="dimension_cn_name"/>
        <result property="dimensionSubCategoryCode" column="dimension_subcategory_code"/>
        <result property="dimensionSubCategoryCnName" column="dimension_subcategory_cn_name"/>
        <result property="dimensionSubDetailCode" column="dimension_sub_detail_code"/>
        <result property="dimensionSubDetailCnName" column="dimension_sub_detail_cn_name"/>
        <result property="groupLevel" column="group_level"/>
        <result property="groupCnName" column="group_cn_name"/>
        <result property="groupCode" column="group_code"/>
        <result property="userId" column="user_id"/>
        <result property="roleId" column="role_id"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="caliberFlag" column="caliber_flag"/>
        <result property="granularityType" column="granularity_type"/>
        <result property="overseaFlag" column="oversea_flag"/>
        <result property="enableFlag" column="enable_flag"/>
        <result property="subEnableFlag" column="sub_enable_flag"/>
        <result property="lv0ProdListCode" column="lv0_prod_list_code"/>
        <result property="lv0ProdListCnName" column="lv0_prod_list_cn_name"/>
        <result property="l3CegCnName" column="TOP_L3_CEG_SHORT_CN_NAME"/>
        <result property="l3CegCode" column="top_l3_ceg_code"/>
        <result property="l3CegCnName" column="top_l3_ceg_cn_name"/>
        <result property="l3CegCnName" column="L3_CEG_SHORT_CN_NAME"/>
        <result property="l3CegCode" column="L3_CEG_CODE"/>
        <result property="l3CegCnName" column="l3_ceg_cn_name"/>
        <result property="l4CegCnName" column="TOP_L4_CEG_SHORT_CN_NAME"/>
        <result property="l4CegCode" column="TOP_L4_CEG_CODE"/>
        <result property="l4CegCnName" column="top_l4_ceg_cn_name"/>
        <result property="l4CegCnName" column="L4_CEG_SHORT_CN_NAME"/>
        <result property="l4CegCode" column="L4_CEG_CODE"/>
        <result property="l4CegCnName" column="l4_ceg_cn_name"/>
        <result property="categoryCode" column="category_code"/>
        <result property="categoryCnName" column="category_cn_name"/>
        <result property="customCnName" column="custom_cn_name"/>
        <result property="parentCode" column="parent_code"/>
        <result property="isSeparate" column="is_separate"/>
        <result property="creationDate" column="creation_date"/>
        <result property="createdBy" column="created_by"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="coaCode" column="coa_code"/>
        <result property="coaCnName" column="coa_cn_name"/>
        <result property="spartCnName" column="spart_cn_name"/>
        <result property="spartCode" column="spart_code"/>
        <result property="connectCode" column="connectCode"/>
        <result property="connectParentCode" column="connectParentCode"/>
        <result property="connectCode" column="connect_code"/>
        <result property="connectParentCode" column="connect_parent_code"/>
        <result property="shippingObjectCode" column="shipping_object_code"/>
        <result property="shippingObjectCnName" column="shipping_object_cn_name"/>
        <result property="manufactureObjectCode" column="manufacture_object_code"/>
        <result property="manufactureObjectCnName" column="manufacture_object_cn_name"/>
        <result property="selectFlag" column="select_flag"/>
    </resultMap>

    <insert id="createTempCustomCombList">
        insert into fin_dm_opt_foi.dm_foc_custom_comb_mid_d(id,custom_cn_name, page_flag,group_level,LV0_PROD_RND_TEAM_CODE,
        LV0_PROD_RD_TEAM_CN_NAME, LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME,
        LV2_PROD_RND_TEAM_CODE,LV2_PROD_RD_TEAM_CN_NAME,LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME,l1_name,l2_name,
        dimension_code, dimension_cn_name, dimension_subcategory_code, dimension_subcategory_cn_name,
        dimension_sub_detail_code,
        dimension_sub_detail_cn_name,
        spart_code,spart_cn_name,l3_ceg_code,l3_ceg_short_cn_name,l4_ceg_code,l4_ceg_short_cn_name,category_code,category_cn_name,lv0_prod_list_code,lv0_prod_list_cn_name,view_flag,caliber_flag,granularity_type,oversea_flag,enable_flag,sub_enable_flag,group_cn_name,group_code,user_id,role_id,parent_code,created_by,
        creation_date,last_updated_by, last_update_date,del_flag,is_separate,connect_code,connect_parent_code,select_flag)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.customCnName}, #{item.pageFlag},#{item.groupLevel},#{item.lv0ProdRndTeamCode},
            #{item.lv0ProdRdTeamCnName}, #{item.lv1ProdRndTeamCode}, #{item.lv1ProdRdTeamCnName},
            #{item.lv2ProdRndTeamCode},#{item.lv2ProdRdTeamCnName},#{item.lv3ProdRndTeamCode},#{item.lv3ProdRdTeamCnName},#{item.l1Name},#{item.l2Name},
            #{item.dimensionCode}, #{item.dimensionCnName}, #{item.dimensionSubCategoryCode},
            #{item.dimensionSubCategoryCnName}, #{item.dimensionSubDetailCode},
            #{item.dimensionSubDetailCnName},
            #{item.spartCode},#{item.spartCnName},#{item.l3CegCode},#{item.l3CegCnName},#{item.l4CegCode},#{item.l4CegCnName},#{item.categoryCode},#{item.categoryCnName},#{item.lv0ProdListCode},#{item.lv0ProdListCnName},#{item.viewFlag},#{item.caliberFlag},#{item.granularityType},#{item.overseaFlag},#{item.enableFlag},#{item.subEnableFlag},#{item.groupCnName},#{item.groupCode},#{item.userId},#{item.roleId},#{item.parentCode},#{item.createdBy},
            #{item.creationDate},#{item.lastUpdatedBy}, #{item.lastUpdateDate},'N',#{item.isSeparate},#{item.connectCode},#{item.connectParentCode},#{item.selectFlag})
        </foreach>
    </insert>

    <insert id="createTempEnergyCustomCombList">
        insert into fin_dm_opt_foi.dm_foc_energy_custom_comb_mid_d(id,custom_cn_name, page_flag,group_level,LV0_PROD_RND_TEAM_CODE,
        LV0_PROD_RD_TEAM_CN_NAME, LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME,
        LV2_PROD_RND_TEAM_CODE,LV2_PROD_RD_TEAM_CN_NAME,LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME,l1_name,l2_name, coa_code, coa_cn_name,
        dimension_code, dimension_cn_name, dimension_subcategory_code, dimension_subcategory_cn_name,
        dimension_sub_detail_code,
        dimension_sub_detail_cn_name,
        spart_code,spart_cn_name,l3_ceg_code,l3_ceg_short_cn_name,l4_ceg_code,l4_ceg_short_cn_name,category_code,category_cn_name,lv0_prod_list_code,lv0_prod_list_cn_name,view_flag,caliber_flag,granularity_type,oversea_flag,enable_flag,sub_enable_flag,group_cn_name,group_code,user_id,role_id,parent_code,created_by,
        creation_date,last_updated_by, last_update_date,del_flag,is_separate,connect_code,connect_parent_code,select_flag)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.customCnName}, #{item.pageFlag},#{item.groupLevel},#{item.lv0ProdRndTeamCode},
            #{item.lv0ProdRdTeamCnName}, #{item.lv1ProdRndTeamCode}, #{item.lv1ProdRdTeamCnName},
            #{item.lv2ProdRndTeamCode},#{item.lv2ProdRdTeamCnName},#{item.lv3ProdRndTeamCode},#{item.lv3ProdRdTeamCnName},#{item.l1Name},#{item.l2Name},#{item.coaCode},#{item.coaCnName},
            #{item.dimensionCode}, #{item.dimensionCnName}, #{item.dimensionSubCategoryCode},
            #{item.dimensionSubCategoryCnName}, #{item.dimensionSubDetailCode},
            #{item.dimensionSubDetailCnName},
            #{item.spartCode},#{item.spartCnName},#{item.l3CegCode},#{item.l3CegCnName},#{item.l4CegCode},#{item.l4CegCnName},#{item.categoryCode},#{item.categoryCnName},#{item.lv0ProdListCode},#{item.lv0ProdListCnName},#{item.viewFlag},#{item.caliberFlag},#{item.granularityType},#{item.overseaFlag},#{item.enableFlag},#{item.subEnableFlag},#{item.groupCnName},#{item.groupCode},#{item.userId},#{item.roleId},#{item.parentCode},#{item.createdBy},
            #{item.creationDate},#{item.lastUpdatedBy}, #{item.lastUpdateDate},'N',#{item.isSeparate},#{item.connectCode},#{item.connectParentCode},#{item.selectFlag})
        </foreach>
    </insert>

    <insert id="createTempIasCustomCombList">
        insert into fin_dm_opt_foi.dm_foc_ias_custom_comb_mid_d(id,custom_cn_name, page_flag,group_level,LV0_PROD_RND_TEAM_CODE,
        LV0_PROD_RD_TEAM_CN_NAME, LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME,
        LV2_PROD_RND_TEAM_CODE,LV2_PROD_RD_TEAM_CN_NAME,LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME,LV4_PROD_RND_TEAM_CODE,LV4_PROD_RD_TEAM_CN_NAME,l1_name,l2_name,
        dimension_code, dimension_cn_name, dimension_subcategory_code, dimension_subcategory_cn_name,
        dimension_sub_detail_code,
        dimension_sub_detail_cn_name,
        spart_code,spart_cn_name,l3_ceg_code,l3_ceg_short_cn_name,l4_ceg_code,l4_ceg_short_cn_name,category_code,category_cn_name,lv0_prod_list_code,lv0_prod_list_cn_name,view_flag,caliber_flag,granularity_type,oversea_flag,enable_flag,sub_enable_flag,group_cn_name,group_code,user_id,role_id,parent_code,created_by,
        creation_date,last_updated_by, last_update_date,del_flag,is_separate,connect_code,connect_parent_code,select_flag)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.customCnName}, #{item.pageFlag},#{item.groupLevel},#{item.lv0ProdRndTeamCode},
            #{item.lv0ProdRdTeamCnName}, #{item.lv1ProdRndTeamCode}, #{item.lv1ProdRdTeamCnName},#{item.lv2ProdRndTeamCode},#{item.lv2ProdRdTeamCnName},#{item.lv3ProdRndTeamCode},#{item.lv3ProdRdTeamCnName},
             #{item.lv4ProdRndTeamCode},#{item.lv4ProdRdTeamCnName},#{item.l1Name},#{item.l2Name},#{item.dimensionCode}, #{item.dimensionCnName}, #{item.dimensionSubCategoryCode},
            #{item.dimensionSubCategoryCnName}, #{item.dimensionSubDetailCode},#{item.dimensionSubDetailCnName},#{item.spartCode},#{item.spartCnName},#{item.l3CegCode},#{item.l3CegCnName},
             #{item.l4CegCode},#{item.l4CegCnName},#{item.categoryCode},#{item.categoryCnName},#{item.lv0ProdListCode},#{item.lv0ProdListCnName},#{item.viewFlag},#{item.caliberFlag},#{item.granularityType},#{item.overseaFlag},
             #{item.enableFlag},#{item.subEnableFlag},#{item.groupCnName},#{item.groupCode},#{item.userId},#{item.roleId},#{item.parentCode},#{item.createdBy},
            #{item.creationDate},#{item.lastUpdatedBy}, #{item.lastUpdateDate},'N',#{item.isSeparate},#{item.connectCode},#{item.connectParentCode},#{item.selectFlag})
        </foreach>
    </insert>

    <insert id="createTempCustomCombByCustomId">
        insert into fin_dm_opt_foi.dm_foc_custom_comb_mid_d(id,custom_cn_name, page_flag,group_level,LV0_PROD_RND_TEAM_CODE,
        LV0_PROD_RD_TEAM_CN_NAME, LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME,
        LV2_PROD_RND_TEAM_CODE,LV2_PROD_RD_TEAM_CN_NAME,LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME,l1_name,l2_name,
        dimension_code, dimension_cn_name, dimension_subcategory_code, dimension_subcategory_cn_name,
        dimension_sub_detail_code,
        dimension_sub_detail_cn_name,
        spart_code,spart_cn_name,l3_ceg_code,l3_ceg_short_cn_name,l4_ceg_code,l4_ceg_short_cn_name,category_code,category_cn_name,lv0_prod_list_code,lv0_prod_list_cn_name,view_flag,caliber_flag,granularity_type,oversea_flag,enable_flag,sub_enable_flag,group_cn_name,group_code,user_id,role_id,parent_code,created_by,
        creation_date,last_updated_by, last_update_date,del_flag,is_separate,select_flag,connect_code,connect_parent_code)
        select #{id},#{id},#{pageSymbol}, group_level,LV0_PROD_RND_TEAM_CODE,
        LV0_PROD_RD_TEAM_CN_NAME, LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME,
        LV2_PROD_RND_TEAM_CODE,LV2_PROD_RD_TEAM_CN_NAME,LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME,l1_name,l2_name,
        dimension_code, dimension_cn_name, dimension_subcategory_code, dimension_subcategory_cn_name,
        dimension_sub_detail_code,
        dimension_sub_detail_cn_name,
        spart_code,spart_cn_name,l3_ceg_code,l3_ceg_short_cn_name,l4_ceg_code,l4_ceg_short_cn_name,category_code,category_cn_name,lv0_prod_list_code,lv0_prod_list_cn_name,view_flag,caliber_flag,granularity_type,oversea_flag,enable_flag,sub_enable_flag,group_cn_name,group_code,user_id,role_id,parent_code,created_by,
        creation_date,last_updated_by, last_update_date,del_flag,is_separate,'current',
        <if test='granularityType == "U"'>
            <choose>
                <when test='viewFlag =="0"'>
                    concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
                    DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' ||  LV3_PROD_RND_TEAM_CODE),
                    DECODE(l3_ceg_code,'','','#*#' || l3_ceg_code),
                    DECODE(l4_ceg_code,'','','#*#' || l4_ceg_code),
                    DECODE(category_code,'','','#*#' ||category_code)
                    ) as connectCode,
                    concat(
                    DECODE(l3_ceg_code,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code),
                    DECODE(category_code,'','','#*#' ||l4_ceg_code)
                    ) as connectParentCode
                </when>
                <when test='viewFlag =="1"'>
                    concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
                    DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' || LV3_PROD_RND_TEAM_CODE),
                    DECODE(l3_ceg_code,'','','#*#' || l3_ceg_code),
                    DECODE(l4_ceg_code,'','','#*#' || l4_ceg_code),
                    DECODE(category_code,'','','#*#' || category_code)
                    ) as connectCode,
                    concat(
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(l3_ceg_code,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                    DECODE(l4_ceg_code,'','','#*#' || l3_ceg_code),
                    DECODE(category_code,'','','#*#' || l4_ceg_code)
                    ) as connectParentCode
                </when>
                <when test='viewFlag =="2"'>
                    concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
                    DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' || LV3_PROD_RND_TEAM_CODE),
                    DECODE(l3_ceg_code,'','','#*#' || l3_ceg_code),
                    DECODE(l4_ceg_code,'','','#*#' || l4_ceg_code),
                    DECODE(category_code,'','','#*#' ||category_code)
                    ) as connectCode,
                    concat(
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                    DECODE(l3_ceg_code,'','','#*#' || LV2_PROD_RND_TEAM_CODE ),
                    DECODE(l4_ceg_code,'','','#*#' || l3_ceg_code ),
                    DECODE(category_code,'','','#*#' || l4_ceg_code)
                    ) as connectParentCode
                </when>
                <when test='viewFlag =="3"'>
                    concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
                    DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' || LV3_PROD_RND_TEAM_CODE),
                    DECODE(l3_ceg_code,'','','#*#' || l3_ceg_code),
                    DECODE(l4_ceg_code,'','','#*#' || l4_ceg_code),
                    DECODE(category_code,'','','#*#' || category_code)
                    ) as connectCode,
                    concat(
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                    DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
                    DECODE(l3_ceg_code,'','','#*#' || LV3_PROD_RND_TEAM_CODE),
                    DECODE(l4_ceg_code,'','','#*#' || l3_ceg_code),
                    DECODE(category_code,'','','#*#' || l4_ceg_code)
                    ) as connectParentCode
                </when>
                <when test='viewFlag == "4"'>
                    concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                    DECODE(l3_ceg_code,'','','#*#' ||l3_ceg_code ),
                    DECODE(l4_ceg_code,'','','#*#' ||l4_ceg_code ),
                    DECODE(category_code,'','','#*#' ||category_code ),
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV2_PROD_RND_TEAM_CODE)
                    ) as connectCode,
                    concat(
                    DECODE(l3_ceg_code,'','','#*#' ||LV0_PROD_RND_TEAM_CODE ),
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' ||l3_ceg_code ),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE)
                    ) as connectParentCode
                </when>
                <when test='viewFlag == "5"'>
                    concat(
                    DECODE(l3_ceg_code,'','',LV0_PROD_RND_TEAM_CODE ),
                    DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' ||l4_ceg_code ),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE)
                    ) as connectParentCode
                </when>
                <when test='viewFlag == "6"'>
                    concat(
                    DECODE(l3_ceg_code,'','',LV0_PROD_RND_TEAM_CODE ),
                    DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                    DECODE(category_code,'','','#*#' ||l4_ceg_code ),
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' ||category_code ),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE)
                    ) as connectParentCode
                </when>
            </choose>
        </if>
        <if test='granularityType !="U"'>
            concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
            DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
            DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
            DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' ||LV3_PROD_RND_TEAM_CODE ),
            DECODE(l1_name,'','','#*#' ||l1_name ),
            DECODE(l2_name,'','','#*#' ||l2_name ),
            DECODE(dimension_code,'','','#*#' ||dimension_code ),
            DECODE(dimension_subcategory_code,'','','#*#' ||dimension_subcategory_code ),
            DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_sub_detail_code ),
            DECODE(spart_code,'','','#*#' ||spart_code ),
            DECODE(l3_ceg_code,'','','#*#' ||l3_ceg_code ),
            DECODE(l4_ceg_code,'','','#*#' ||l4_ceg_code ),
            DECODE(category_code,'','','#*#' ||category_code)
            ) as connectCode,
            <if test='granularityType == "P"'>
                <choose>
                    <when test='viewFlag =="0"'>
                        concat(
                        DECODE(l3_ceg_code,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="1"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(l3_ceg_code,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="2"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(l3_ceg_code,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="3"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(l1_name,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(l3_ceg_code,'','','#*#' ||l1_name ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="4"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(l1_name,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(l2_name,'','','#*#' ||l1_name ),
                        DECODE(l3_ceg_code,'','','#*#' ||l2_name ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                </choose>
            </if>
            <if test='granularityType == "D"'>
                <choose>
                    <when test='viewFlag =="0"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(l3_ceg_code,'','','#*#' ||dimension_code ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="1"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(l3_ceg_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="2"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(l3_ceg_code,'','','#*#' ||dimension_sub_detail_code ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="3"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(l3_ceg_code,'','','#*#' ||dimension_code ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="4"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(l3_ceg_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="5"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(l3_ceg_code,'','','#*#' ||dimension_sub_detail_code ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="6"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV3_PROD_RND_TEAM_CODE ),
                        DECODE(l3_ceg_code,'','','#*#' ||dimension_code ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="7"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV3_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(l3_ceg_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="8"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV3_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(l3_ceg_code,'','','#*#' ||dimension_sub_detail_code ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="9"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(spart_code,'','','#*#' ||dimension_sub_detail_code ),
                        DECODE(l3_ceg_code,'','','#*#' ||spart_code ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="10"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(spart_code,'','','#*#' ||dimension_sub_detail_code ),
                        DECODE(l3_ceg_code,'','','#*#' ||spart_code ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="11"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV3_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(spart_code,'','','#*#' ||dimension_sub_detail_code ),
                        DECODE(l3_ceg_code,'','','#*#' ||spart_code ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                </choose>
            </if>
        </if>
        from fin_dm_opt_foi.dm_foc_custom_comb_d where custom_id =#{customId} and page_flag = #{pageFlag}
    </insert>

    <insert id="createTempEnergyCustomCombByCustomId">
        insert into fin_dm_opt_foi.dm_foc_energy_custom_comb_mid_d(id,custom_cn_name, group_level,LV0_PROD_RND_TEAM_CODE,
        LV0_PROD_RD_TEAM_CN_NAME, LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME,
        LV2_PROD_RND_TEAM_CODE,LV2_PROD_RD_TEAM_CN_NAME,LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME,l1_name,l2_name,coa_code,coa_cn_name,
        dimension_code, dimension_cn_name, dimension_subcategory_code, dimension_subcategory_cn_name,
        dimension_sub_detail_code,
        dimension_sub_detail_cn_name,
        spart_code,spart_cn_name,l3_ceg_code,l3_ceg_short_cn_name,l4_ceg_code,l4_ceg_short_cn_name,category_code,category_cn_name,lv0_prod_list_code,lv0_prod_list_cn_name,view_flag,caliber_flag,granularity_type,oversea_flag,enable_flag,sub_enable_flag,group_cn_name,group_code,user_id,role_id,parent_code,created_by,
        creation_date,last_updated_by, last_update_date,del_flag,is_separate,select_flag,connect_code,connect_parent_code)
        select #{id},#{id}, group_level,LV0_PROD_RND_TEAM_CODE,
        LV0_PROD_RD_TEAM_CN_NAME, LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME,
        LV2_PROD_RND_TEAM_CODE,LV2_PROD_RD_TEAM_CN_NAME,LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME,l1_name,l2_name,coa_code,coa_cn_name,
        dimension_code, dimension_cn_name, dimension_subcategory_code, dimension_subcategory_cn_name,
        dimension_sub_detail_code,
        dimension_sub_detail_cn_name,
        spart_code,spart_cn_name,l3_ceg_code,l3_ceg_short_cn_name,l4_ceg_code,l4_ceg_short_cn_name,category_code,category_cn_name,lv0_prod_list_code,lv0_prod_list_cn_name,view_flag,caliber_flag,granularity_type,oversea_flag,enable_flag,sub_enable_flag,group_cn_name,group_code,user_id,role_id,parent_code,created_by,
        creation_date,last_updated_by, last_update_date,del_flag,is_separate,'current',
        <if test='granularityType == "U"'>
            <choose>
                <when test='viewFlag =="0"'>
                    concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
                    DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' ||  LV3_PROD_RND_TEAM_CODE),
                    DECODE(l3_ceg_code,'','','#*#' || l3_ceg_code),
                    DECODE(l4_ceg_code,'','','#*#' || l4_ceg_code),
                    DECODE(category_code,'','','#*#' ||category_code)
                    ) as connectCode,
                    concat(
                    DECODE(l3_ceg_code,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code),
                    DECODE(category_code,'','','#*#' ||l4_ceg_code)
                    ) as connectParentCode
                </when>
                <when test='viewFlag =="1"'>
                    concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
                    DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' || LV3_PROD_RND_TEAM_CODE),
                    DECODE(l3_ceg_code,'','','#*#' || l3_ceg_code),
                    DECODE(l4_ceg_code,'','','#*#' || l4_ceg_code),
                    DECODE(category_code,'','','#*#' || category_code)
                    ) as connectCode,
                    concat(
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(l3_ceg_code,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                    DECODE(l4_ceg_code,'','','#*#' || l3_ceg_code),
                    DECODE(category_code,'','','#*#' || l4_ceg_code)
                    ) as connectParentCode
                </when>
                <when test='viewFlag =="2"'>
                    concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
                    DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' || LV3_PROD_RND_TEAM_CODE),
                    DECODE(l3_ceg_code,'','','#*#' || l3_ceg_code),
                    DECODE(l4_ceg_code,'','','#*#' || l4_ceg_code),
                    DECODE(category_code,'','','#*#' ||category_code)
                    ) as connectCode,
                    concat(
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                    DECODE(l3_ceg_code,'','','#*#' || LV2_PROD_RND_TEAM_CODE ),
                    DECODE(l4_ceg_code,'','','#*#' || l3_ceg_code ),
                    DECODE(category_code,'','','#*#' || l4_ceg_code)
                    ) as connectParentCode
                </when>
                <when test='viewFlag =="3"'>
                    concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
                    DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' || LV3_PROD_RND_TEAM_CODE),
                    DECODE(l3_ceg_code,'','','#*#' || l3_ceg_code),
                    DECODE(l4_ceg_code,'','','#*#' || l4_ceg_code),
                    DECODE(category_code,'','','#*#' || category_code)
                    ) as connectCode,
                    concat(
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                    DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
                    DECODE(l3_ceg_code,'','','#*#' || LV3_PROD_RND_TEAM_CODE),
                    DECODE(l4_ceg_code,'','','#*#' || l3_ceg_code),
                    DECODE(category_code,'','','#*#' || l4_ceg_code)
                    ) as connectParentCode
                </when>
                <when test='viewFlag == "4"'>
                    concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                    DECODE(l3_ceg_code,'','','#*#' ||l3_ceg_code ),
                    DECODE(l4_ceg_code,'','','#*#' ||l4_ceg_code ),
                    DECODE(category_code,'','','#*#' ||category_code ),
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV2_PROD_RND_TEAM_CODE)
                    ) as connectCode,
                    concat(
                    DECODE(l3_ceg_code,'','','#*#' ||LV0_PROD_RND_TEAM_CODE ),
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' ||l3_ceg_code ),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE)
                    ) as connectParentCode
                </when>
                <when test='viewFlag == "5"'>
                    concat(
                    DECODE(l3_ceg_code,'','',LV0_PROD_RND_TEAM_CODE ),
                    DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' ||l4_ceg_code ),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE)
                    ) as connectParentCode
                </when>
                <when test='viewFlag == "6"'>
                    concat(
                    DECODE(l3_ceg_code,'','',LV0_PROD_RND_TEAM_CODE ),
                    DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                    DECODE(category_code,'','','#*#' ||l4_ceg_code ),
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' ||category_code ),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE)
                    ) as connectParentCode
                </when>
            </choose>
        </if>
        <if test='granularityType !="U"'>
            concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
            DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
            DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
            DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' ||LV3_PROD_RND_TEAM_CODE ),
            DECODE(l1_name,'','','#*#' ||l1_name ),
            DECODE(l2_name,'','','#*#' ||l2_name ),
            DECODE(coa_code,'','','#*#' ||coa_code ),
            DECODE(dimension_code,'','','#*#' ||dimension_code ),
            DECODE(dimension_subcategory_code,'','','#*#' ||dimension_subcategory_code ),
            DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_sub_detail_code ),
            DECODE(spart_code,'','','#*#' ||spart_code ),
            DECODE(l3_ceg_code,'','','#*#' ||l3_ceg_code ),
            DECODE(l4_ceg_code,'','','#*#' ||l4_ceg_code ),
            DECODE(category_code,'','','#*#' ||category_code)
            ) as connectCode,
            <if test='granularityType == "P"'>
                <choose>
                    <when test='viewFlag =="0"'>
                        concat(
                        DECODE(l3_ceg_code,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="1"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(l3_ceg_code,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="2"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(l3_ceg_code,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="3"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(l1_name,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(l3_ceg_code,'','','#*#' ||l1_name ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="4"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(l1_name,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(l2_name,'','','#*#' ||l1_name ),
                        DECODE(l3_ceg_code,'','','#*#' ||l2_name ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                </choose>
            </if>
            <if test='granularityType == "D"'>
                <choose>
                    <when test='viewFlag =="0"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(l3_ceg_code,'','','#*#' ||dimension_code ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="1"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(l3_ceg_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="2"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(l3_ceg_code,'','','#*#' ||dimension_sub_detail_code ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="3"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(l3_ceg_code,'','','#*#' ||dimension_code ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="4"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(l3_ceg_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="5"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(l3_ceg_code,'','','#*#' ||dimension_sub_detail_code ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="6"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV3_PROD_RND_TEAM_CODE ),
                        DECODE(l3_ceg_code,'','','#*#' ||dimension_code ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="7"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV3_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(l3_ceg_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="8"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV3_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(l3_ceg_code,'','','#*#' ||dimension_sub_detail_code ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="9"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(spart_code,'','','#*#' ||dimension_sub_detail_code ),
                        DECODE(l3_ceg_code,'','','#*#' ||spart_code ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="10"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(spart_code,'','','#*#' ||dimension_sub_detail_code ),
                        DECODE(l3_ceg_code,'','','#*#' ||spart_code ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="11"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV3_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(spart_code,'','','#*#' ||dimension_sub_detail_code ),
                        DECODE(l3_ceg_code,'','','#*#' ||spart_code ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="12"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(coa_code,'','','#*#' ||LV3_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||coa_code ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(spart_code,'','','#*#' ||dimension_sub_detail_code ),
                        DECODE(l3_ceg_code,'','','#*#' ||spart_code ),
                        DECODE(l4_ceg_code,'','','#*#' ||l3_ceg_code ),
                        DECODE(category_code,'','','#*#' ||l4_ceg_code)
                        ) as connectParentCode
                    </when>
                </choose>
            </if>
        </if>
        from fin_dm_opt_foi.dm_foc_energy_custom_comb_d where custom_id =#{customId}
    </insert>

    <insert id="createTempManufactureCustomcombList">
        insert into fin_dm_opt_foi.dm_foc_made_custom_comb_mid_d(id,custom_cn_name, page_flag,group_level,LV0_PROD_RND_TEAM_CODE, LV0_PROD_RD_TEAM_CN_NAME, LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME,
        LV2_PROD_RND_TEAM_CODE,LV2_PROD_RD_TEAM_CN_NAME,LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME,l1_name,l2_name,
        dimension_code, dimension_cn_name, dimension_subcategory_code, dimension_subcategory_cn_name, dimension_sub_detail_code,
        dimension_sub_detail_cn_name, spart_code, spart_cn_name, SHIPPING_OBJECT_CODE ,SHIPPING_OBJECT_CN_NAME,MANUFACTURE_OBJECT_CODE ,MANUFACTURE_OBJECT_CN_NAME,lv0_prod_list_code,lv0_prod_list_cn_name,view_flag,caliber_flag,granularity_type,oversea_flag,enable_flag,sub_enable_flag,group_cn_name,group_code,user_id,role_id,parent_code,created_by,
        creation_date,last_updated_by, last_update_date,del_flag,is_separate,connect_code,connect_parent_code,select_flag)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.customCnName},#{item.pageFlag}, #{item.groupLevel},#{item.lv0ProdRndTeamCode}, #{item.lv0ProdRdTeamCnName}, #{item.lv1ProdRndTeamCode}, #{item.lv1ProdRdTeamCnName},
            #{item.lv2ProdRndTeamCode},#{item.lv2ProdRdTeamCnName},#{item.lv3ProdRndTeamCode},#{item.lv3ProdRdTeamCnName},#{item.l1Name},#{item.l2Name},
            #{item.dimensionCode}, #{item.dimensionCnName}, #{item.dimensionSubCategoryCode}, #{item.dimensionSubCategoryCnName}, #{item.dimensionSubDetailCode},
            #{item.dimensionSubDetailCnName}, #{item.spartCode},#{item.spartCnName},#{item.shippingObjectCode},#{item.shippingObjectCnName},#{item.manufactureObjectCode},#{item.manufactureObjectCnName},#{item.lv0ProdListCode},#{item.lv0ProdListCnName},#{item.viewFlag},#{item.caliberFlag},#{item.granularityType},#{item.overseaFlag},#{item.enableFlag},#{item.subEnableFlag},#{item.groupCnName},#{item.groupCode},#{item.userId},#{item.roleId},#{item.parentCode},#{item.createdBy},
            #{item.creationDate},#{item.lastUpdatedBy}, #{item.lastUpdateDate},'N',#{item.isSeparate},#{item.connectCode},#{item.connectParentCode},#{item.selectFlag})
        </foreach>
    </insert>

    <insert id="createTempEnergyManufactureCustomcombList">
        insert into fin_dm_opt_foi.dm_foc_energy_made_custom_comb_mid_d(id,custom_cn_name, page_flag,group_level,LV0_PROD_RND_TEAM_CODE, LV0_PROD_RD_TEAM_CN_NAME, LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME,
        LV2_PROD_RND_TEAM_CODE,LV2_PROD_RD_TEAM_CN_NAME,LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME,l1_name,l2_name,coa_code, coa_cn_name,
        dimension_code, dimension_cn_name, dimension_subcategory_code, dimension_subcategory_cn_name, dimension_sub_detail_code,
        dimension_sub_detail_cn_name, spart_code, spart_cn_name, SHIPPING_OBJECT_CODE ,SHIPPING_OBJECT_CN_NAME,MANUFACTURE_OBJECT_CODE ,MANUFACTURE_OBJECT_CN_NAME,lv0_prod_list_code,lv0_prod_list_cn_name,view_flag,caliber_flag,granularity_type,oversea_flag,enable_flag,sub_enable_flag,group_cn_name,group_code,user_id,role_id,parent_code,created_by,
        creation_date,last_updated_by, last_update_date,del_flag,is_separate,connect_code,connect_parent_code,select_flag)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.customCnName}, #{item.pageFlag},#{item.groupLevel},#{item.lv0ProdRndTeamCode}, #{item.lv0ProdRdTeamCnName}, #{item.lv1ProdRndTeamCode}, #{item.lv1ProdRdTeamCnName},
            #{item.lv2ProdRndTeamCode},#{item.lv2ProdRdTeamCnName},#{item.lv3ProdRndTeamCode},#{item.lv3ProdRdTeamCnName},#{item.l1Name},#{item.l2Name},#{item.coaCode},#{item.coaCnName},
            #{item.dimensionCode}, #{item.dimensionCnName}, #{item.dimensionSubCategoryCode}, #{item.dimensionSubCategoryCnName}, #{item.dimensionSubDetailCode},
            #{item.dimensionSubDetailCnName}, #{item.spartCode},#{item.spartCnName},#{item.shippingObjectCode},#{item.shippingObjectCnName},#{item.manufactureObjectCode},#{item.manufactureObjectCnName},#{item.lv0ProdListCode},#{item.lv0ProdListCnName},#{item.viewFlag},#{item.caliberFlag},#{item.granularityType},#{item.overseaFlag},#{item.enableFlag},#{item.subEnableFlag},#{item.groupCnName},#{item.groupCode},#{item.userId},#{item.roleId},#{item.parentCode},#{item.createdBy},
            #{item.creationDate},#{item.lastUpdatedBy}, #{item.lastUpdateDate},'N',#{item.isSeparate},#{item.connectCode},#{item.connectParentCode},#{item.selectFlag})
        </foreach>
    </insert>

    <insert id="createTempIasManufactureCustomcombList">
        insert into fin_dm_opt_foi.dm_foc_ias_made_custom_comb_mid_d(id,custom_cn_name, page_flag,group_level,LV0_PROD_RND_TEAM_CODE, LV0_PROD_RD_TEAM_CN_NAME, LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME,
        LV2_PROD_RND_TEAM_CODE,LV2_PROD_RD_TEAM_CN_NAME,LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME,LV4_PROD_RND_TEAM_CODE,LV4_PROD_RD_TEAM_CN_NAME,l1_name,l2_name,
        dimension_code, dimension_cn_name, dimension_subcategory_code, dimension_subcategory_cn_name, dimension_sub_detail_code,
        dimension_sub_detail_cn_name, spart_code, spart_cn_name, SHIPPING_OBJECT_CODE ,SHIPPING_OBJECT_CN_NAME,MANUFACTURE_OBJECT_CODE ,MANUFACTURE_OBJECT_CN_NAME,lv0_prod_list_code,lv0_prod_list_cn_name,view_flag,caliber_flag,granularity_type,oversea_flag,enable_flag,sub_enable_flag,group_cn_name,group_code,user_id,role_id,parent_code,created_by,
        creation_date,last_updated_by, last_update_date,del_flag,is_separate,connect_code,connect_parent_code,select_flag)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.customCnName}, #{item.pageFlag},#{item.groupLevel},#{item.lv0ProdRndTeamCode}, #{item.lv0ProdRdTeamCnName}, #{item.lv1ProdRndTeamCode}, #{item.lv1ProdRdTeamCnName},
            #{item.lv2ProdRndTeamCode},#{item.lv2ProdRdTeamCnName},#{item.lv3ProdRndTeamCode},#{item.lv3ProdRdTeamCnName},#{item.lv4ProdRndTeamCode},#{item.lv4ProdRdTeamCnName},#{item.l1Name},#{item.l2Name},
            #{item.dimensionCode}, #{item.dimensionCnName}, #{item.dimensionSubCategoryCode}, #{item.dimensionSubCategoryCnName}, #{item.dimensionSubDetailCode},#{item.dimensionSubDetailCnName}, #{item.spartCode},#{item.spartCnName},
             #{item.shippingObjectCode},#{item.shippingObjectCnName},#{item.manufactureObjectCode},#{item.manufactureObjectCnName},#{item.lv0ProdListCode},#{item.lv0ProdListCnName},#{item.viewFlag},#{item.caliberFlag},#{item.granularityType},
             #{item.overseaFlag},#{item.enableFlag},#{item.subEnableFlag},#{item.groupCnName},#{item.groupCode},#{item.userId},#{item.roleId},#{item.parentCode},#{item.createdBy},
            #{item.creationDate},#{item.lastUpdatedBy}, #{item.lastUpdateDate},'N',#{item.isSeparate},#{item.connectCode},#{item.connectParentCode},#{item.selectFlag})
        </foreach>
    </insert>

    <insert id="createMadeTempCustomCombByCustomId">
        insert into fin_dm_opt_foi.dm_foc_made_custom_comb_mid_d(id,custom_cn_name, page_flag,group_level,LV0_PROD_RND_TEAM_CODE, LV0_PROD_RD_TEAM_CN_NAME, LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME,
        LV2_PROD_RND_TEAM_CODE,LV2_PROD_RD_TEAM_CN_NAME,LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME,l1_name,l2_name,
        dimension_code, dimension_cn_name, dimension_subcategory_code, dimension_subcategory_cn_name, dimension_sub_detail_code,
        dimension_sub_detail_cn_name, spart_code, spart_cn_name, SHIPPING_OBJECT_CODE ,SHIPPING_OBJECT_CN_NAME,MANUFACTURE_OBJECT_CODE ,MANUFACTURE_OBJECT_CN_NAME,lv0_prod_list_code,lv0_prod_list_cn_name,view_flag,caliber_flag,granularity_type,oversea_flag,enable_flag,sub_enable_flag,group_cn_name,group_code,user_id,role_id,parent_code,created_by,
        creation_date,last_updated_by, last_update_date,del_flag,is_separate,select_flag,connect_code,connect_parent_code)
        select #{id},#{id}, #{pageSymbol},group_level,LV0_PROD_RND_TEAM_CODE, LV0_PROD_RD_TEAM_CN_NAME, LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME,
        LV2_PROD_RND_TEAM_CODE,LV2_PROD_RD_TEAM_CN_NAME,LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME,l1_name,l2_name,
        dimension_code, dimension_cn_name, dimension_subcategory_code, dimension_subcategory_cn_name, dimension_sub_detail_code,
        dimension_sub_detail_cn_name, spart_code, spart_cn_name, SHIPPING_OBJECT_CODE ,SHIPPING_OBJECT_CN_NAME,MANUFACTURE_OBJECT_CODE ,MANUFACTURE_OBJECT_CN_NAME,lv0_prod_list_code,lv0_prod_list_cn_name,view_flag,caliber_flag,granularity_type,oversea_flag,enable_flag,sub_enable_flag,group_cn_name,group_code,user_id,role_id,parent_code,created_by,
        creation_date,last_updated_by, last_update_date,del_flag,is_separate,'current',
        <if test='granularityType == "U"'>
            <choose>
                <when test='viewFlag =="0"'>
                    concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
                    DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' ||  LV3_PROD_RND_TEAM_CODE),
                    DECODE(shipping_object_code,'','','#*#' || shipping_object_code),
                    DECODE(manufacture_object_code,'','','#*#' || manufacture_object_code)
                    ) as connectCode,
                    concat(
                    DECODE(shipping_object_code,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code)
                    ) as connectParentCode
                </when>
                <when test='viewFlag =="1"'>
                    concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
                    DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' || LV3_PROD_RND_TEAM_CODE),
                    DECODE(shipping_object_code,'','','#*#' || shipping_object_code),
                    DECODE(manufacture_object_code,'','','#*#' || manufacture_object_code)
                    ) as connectCode,
                    concat(
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(shipping_object_code,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                    DECODE(manufacture_object_code,'','','#*#' || shipping_object_code)
                    ) as connectParentCode
                </when>
                <when test='viewFlag =="2"'>
                    concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
                    DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' || LV3_PROD_RND_TEAM_CODE),
                    DECODE(shipping_object_code,'','','#*#' || shipping_object_code),
                    DECODE(manufacture_object_code,'','','#*#' || manufacture_object_code)
                    ) as connectCode,
                    concat(
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                    DECODE(shipping_object_code,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
                    DECODE(manufacture_object_code,'','','#*#' || shipping_object_code)
                    ) as connectParentCode
                </when>
                <when test='viewFlag =="3"'>
                    concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
                    DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' || LV3_PROD_RND_TEAM_CODE),
                    DECODE(shipping_object_code,'','','#*#' || shipping_object_code),
                    DECODE(manufacture_object_code,'','','#*#' || manufacture_object_code)
                    ) as connectCode,
                    concat(
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                    DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
                    DECODE(shipping_object_code,'','','#*#' || LV3_PROD_RND_TEAM_CODE),
                    DECODE(manufacture_object_code,'','','#*#' || shipping_object_code)
                    ) as connectParentCode
                </when>
            </choose>
        </if>
        <if test='granularityType !="U"'>
            concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
            DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
            DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
            DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' ||LV3_PROD_RND_TEAM_CODE ),
            DECODE(l1_name,'','','#*#' ||l1_name ),
            DECODE(l2_name,'','','#*#' ||l2_name ),
            DECODE(dimension_code,'','','#*#' ||dimension_code ),
            DECODE(dimension_subcategory_code,'','','#*#' ||dimension_subcategory_code ),
            DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_sub_detail_code ),
            DECODE(spart_code,'','','#*#' ||spart_code ),
            DECODE(shipping_object_code,'','','#*#' ||shipping_object_code ),
            DECODE(manufacture_object_code,'','','#*#' ||manufacture_object_code)
            ) as connectCode,
            <if test='granularityType == "P"'>
                <choose>
                    <when test='viewFlag =="0"'>
                        concat(
                        DECODE(shipping_object_code,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="1"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(shipping_object_code,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="2"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(shipping_object_code,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="3"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(l1_name,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(shipping_object_code,'','','#*#' ||l1_name ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="4"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(l1_name,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(l2_name,'','','#*#' ||l1_name ),
                        DECODE(shipping_object_code,'','','#*#' ||l2_name ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                </choose>
            </if>
            <if test='granularityType == "D"'>
                <choose>
                    <when test='viewFlag =="0"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(shipping_object_code,'','','#*#' ||dimension_code ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="1"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(shipping_object_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="2"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(shipping_object_code,'','','#*#' ||dimension_sub_detail_code ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="3"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(shipping_object_code,'','','#*#' ||dimension_code ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="4"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(shipping_object_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="5"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(shipping_object_code,'','','#*#' ||dimension_sub_detail_code ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="6"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV3_PROD_RND_TEAM_CODE ),
                        DECODE(shipping_object_code,'','','#*#' ||dimension_code ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="7"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV3_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(shipping_object_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="8"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV3_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(shipping_object_code,'','','#*#' ||dimension_sub_detail_code ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="9"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(spart_code,'','','#*#' ||dimension_sub_detail_code ),
                        DECODE(shipping_object_code,'','','#*#' ||spart_code ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="10"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(spart_code,'','','#*#' ||dimension_sub_detail_code ),
                        DECODE(shipping_object_code,'','','#*#' ||spart_code ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="11"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV3_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(spart_code,'','','#*#' ||dimension_sub_detail_code ),
                        DECODE(shipping_object_code,'','','#*#' ||spart_code ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                </choose>
            </if>
        </if>
        from fin_dm_opt_foi.dm_foc_made_custom_comb_d where custom_id =#{customId} and page_flag = #{pageFlag}
    </insert>

    <insert id="createMadeTempEnergyCustomCombByCustomId">
        insert into fin_dm_opt_foi.dm_foc_energy_made_custom_comb_mid_d(id,custom_cn_name, group_level,LV0_PROD_RND_TEAM_CODE, LV0_PROD_RD_TEAM_CN_NAME, LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME,
        LV2_PROD_RND_TEAM_CODE,LV2_PROD_RD_TEAM_CN_NAME,LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME,l1_name,l2_name,coa_code,coa_cn_name,
        dimension_code, dimension_cn_name, dimension_subcategory_code, dimension_subcategory_cn_name, dimension_sub_detail_code,
        dimension_sub_detail_cn_name, spart_code, spart_cn_name, SHIPPING_OBJECT_CODE ,SHIPPING_OBJECT_CN_NAME,MANUFACTURE_OBJECT_CODE ,MANUFACTURE_OBJECT_CN_NAME,lv0_prod_list_code,lv0_prod_list_cn_name,view_flag,caliber_flag,granularity_type,oversea_flag,enable_flag,sub_enable_flag,group_cn_name,group_code,user_id,role_id,parent_code,created_by,
        creation_date,last_updated_by, last_update_date,del_flag,is_separate,select_flag,connect_code,connect_parent_code)
        select #{id},#{id}, group_level,LV0_PROD_RND_TEAM_CODE, LV0_PROD_RD_TEAM_CN_NAME, LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME,
        LV2_PROD_RND_TEAM_CODE,LV2_PROD_RD_TEAM_CN_NAME,LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME,l1_name,l2_name,coa_code,coa_cn_name,
        dimension_code, dimension_cn_name, dimension_subcategory_code, dimension_subcategory_cn_name, dimension_sub_detail_code,
        dimension_sub_detail_cn_name, spart_code, spart_cn_name, SHIPPING_OBJECT_CODE ,SHIPPING_OBJECT_CN_NAME,MANUFACTURE_OBJECT_CODE ,MANUFACTURE_OBJECT_CN_NAME,lv0_prod_list_code,lv0_prod_list_cn_name,view_flag,caliber_flag,granularity_type,oversea_flag,enable_flag,sub_enable_flag,group_cn_name,group_code,user_id,role_id,parent_code,created_by,
        creation_date,last_updated_by, last_update_date,del_flag,is_separate,'current',
        <if test='granularityType == "U"'>
            <choose>
                <when test='viewFlag =="0"'>
                    concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
                    DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' ||  LV3_PROD_RND_TEAM_CODE),
                    DECODE(shipping_object_code,'','','#*#' || shipping_object_code),
                    DECODE(manufacture_object_code,'','','#*#' || manufacture_object_code)
                    ) as connectCode,
                    concat(
                    DECODE(shipping_object_code,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code)
                    ) as connectParentCode
                </when>
                <when test='viewFlag =="1"'>
                    concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
                    DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' || LV3_PROD_RND_TEAM_CODE),
                    DECODE(shipping_object_code,'','','#*#' || shipping_object_code),
                    DECODE(manufacture_object_code,'','','#*#' || manufacture_object_code)
                    ) as connectCode,
                    concat(
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(shipping_object_code,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                    DECODE(manufacture_object_code,'','','#*#' || shipping_object_code)
                    ) as connectParentCode
                </when>
                <when test='viewFlag =="2"'>
                    concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
                    DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' || LV3_PROD_RND_TEAM_CODE),
                    DECODE(shipping_object_code,'','','#*#' || shipping_object_code),
                    DECODE(manufacture_object_code,'','','#*#' || manufacture_object_code)
                    ) as connectCode,
                    concat(
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                    DECODE(shipping_object_code,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
                    DECODE(manufacture_object_code,'','','#*#' || shipping_object_code)
                    ) as connectParentCode
                </when>
                <when test='viewFlag =="3"'>
                    concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
                    DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' || LV3_PROD_RND_TEAM_CODE),
                    DECODE(shipping_object_code,'','','#*#' || shipping_object_code),
                    DECODE(manufacture_object_code,'','','#*#' || manufacture_object_code)
                    ) as connectCode,
                    concat(
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                    DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
                    DECODE(shipping_object_code,'','','#*#' || LV3_PROD_RND_TEAM_CODE),
                    DECODE(manufacture_object_code,'','','#*#' || shipping_object_code)
                    ) as connectParentCode
                </when>
            </choose>
        </if>
        <if test='granularityType !="U"'>
            concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
            DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
            DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
            DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' ||LV3_PROD_RND_TEAM_CODE ),
            DECODE(l1_name,'','','#*#' ||l1_name ),
            DECODE(l2_name,'','','#*#' ||l2_name ),
            DECODE(coa_code,'','','#*#' ||coa_code ),
            DECODE(dimension_code,'','','#*#' ||dimension_code ),
            DECODE(dimension_subcategory_code,'','','#*#' ||dimension_subcategory_code ),
            DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_sub_detail_code ),
            DECODE(spart_code,'','','#*#' ||spart_code ),
            DECODE(shipping_object_code,'','','#*#' ||shipping_object_code ),
            DECODE(manufacture_object_code,'','','#*#' ||manufacture_object_code)
            ) as connectCode,
            <if test='granularityType == "P"'>
                <choose>
                    <when test='viewFlag =="0"'>
                        concat(
                        DECODE(shipping_object_code,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code)
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="1"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(shipping_object_code,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="2"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(shipping_object_code,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="3"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(l1_name,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(shipping_object_code,'','','#*#' ||l1_name ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="4"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(l1_name,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(l2_name,'','','#*#' ||l1_name ),
                        DECODE(shipping_object_code,'','','#*#' ||l2_name ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                </choose>
            </if>
            <if test='granularityType == "D"'>
                <choose>
                    <when test='viewFlag =="0"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(shipping_object_code,'','','#*#' ||dimension_code ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="1"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(shipping_object_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="2"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(shipping_object_code,'','','#*#' ||dimension_sub_detail_code ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="3"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(shipping_object_code,'','','#*#' ||dimension_code ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="4"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(shipping_object_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="5"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(shipping_object_code,'','','#*#' ||dimension_sub_detail_code ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="6"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV3_PROD_RND_TEAM_CODE ),
                        DECODE(shipping_object_code,'','','#*#' ||dimension_code ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="7"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV3_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(shipping_object_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="8"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV3_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(shipping_object_code,'','','#*#' ||dimension_sub_detail_code ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="9"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(spart_code,'','','#*#' ||dimension_sub_detail_code ),
                        DECODE(shipping_object_code,'','','#*#' ||spart_code ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="10"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(spart_code,'','','#*#' ||dimension_sub_detail_code ),
                        DECODE(shipping_object_code,'','','#*#' ||spart_code ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="11"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||LV3_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(spart_code,'','','#*#' ||dimension_sub_detail_code ),
                        DECODE(shipping_object_code,'','','#*#' ||spart_code ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                    <when test='viewFlag =="12"'>
                        concat(
                        DECODE(LV1_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE ),
                        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' ||LV1_PROD_RND_TEAM_CODE ),
                        DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' ||LV2_PROD_RND_TEAM_CODE ),
                        DECODE(coa_code,'','','#*#' ||LV3_PROD_RND_TEAM_CODE ),
                        DECODE(dimension_code,'','','#*#' ||coa_code ),
                        DECODE(dimension_subcategory_code,'','','#*#' ||dimension_code ),
                        DECODE(dimension_sub_detail_code,'','','#*#' ||dimension_subcategory_code ),
                        DECODE(spart_code,'','','#*#' ||dimension_sub_detail_code ),
                        DECODE(shipping_object_code,'','','#*#' ||spart_code ),
                        DECODE(manufacture_object_code,'','','#*#' ||shipping_object_code )
                        ) as connectParentCode
                    </when>
                </choose>
            </if>
        </if>
        from fin_dm_opt_foi.dm_foc_made_custom_comb_d where custom_id =#{customId}
    </insert>


    <select id="getTempCustomCombList" resultMap="customResultMap">
        select custom_cn_name,id,LV0_PROD_RND_TEAM_CODE, LV0_PROD_RD_TEAM_CN_NAME,
        LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME,
        LV2_PROD_RND_TEAM_CODE,LV2_PROD_RD_TEAM_CN_NAME,LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME,l1_name,l2_name,
        dimension_code, dimension_cn_name, dimension_subcategory_code, dimension_subcategory_cn_name,
        dimension_sub_detail_code,spart_code,spart_cn_name,
        dimension_sub_detail_cn_name,l3_ceg_code,l3_ceg_short_cn_name,l4_ceg_code,l4_ceg_short_cn_name,category_code,category_cn_name,
        group_level,group_code,group_cn_name,user_id,role_id,view_flag,caliber_flag,granularity_type,oversea_flag,
        enable_flag,sub_enable_flag,lv0_prod_list_code,lv0_prod_list_cn_name,parent_code,is_separate,creation_date,last_update_date,created_by,last_updated_by,connect_code,connect_parent_code
        <if test='industryOrg =="ENERGY"'>
            ,coa_code,coa_cn_name
        </if>
        <if test='industryOrg =="IAS"'>
            ,LV4_PROD_RND_TEAM_CODE,LV4_PROD_RD_TEAM_CN_NAME
        </if>
        from fin_dm_opt_foi.${tablePreFix}_custom_comb_mid_d where del_flag ='N' and enable_flag='Y'
        and user_id = #{userId} and role_id = #{roleId} and select_flag = 'current'
        <if test='id != null and id!=""'>
            and id = #{id}
        </if>
        <if test='pageSymbol != null and pageSymbol!=""'>
            and page_flag = #{pageSymbol}
        </if>
    </select>

    <select id="getTempManufactureCustomCombList" resultMap="customResultMap">
        select custom_cn_name,id,LV0_PROD_RND_TEAM_CODE, LV0_PROD_RD_TEAM_CN_NAME,
        LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME,
        LV2_PROD_RND_TEAM_CODE,LV2_PROD_RD_TEAM_CN_NAME,LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME,l1_name,l2_name,
        dimension_code, dimension_cn_name, dimension_subcategory_code, dimension_subcategory_cn_name,
        dimension_sub_detail_code,spart_code,spart_cn_name,
        dimension_sub_detail_cn_name,shipping_object_code,shipping_object_cn_name,manufacture_object_code,manufacture_object_cn_name,
        group_level,group_code,group_cn_name,user_id,role_id,view_flag,caliber_flag,granularity_type,oversea_flag,
        enable_flag,sub_enable_flag,lv0_prod_list_code,lv0_prod_list_cn_name,parent_code,is_separate,creation_date,last_update_date,created_by,last_updated_by,connect_code,connect_parent_code
        <if test='industryOrg =="ENERGY"'>
            ,coa_code,coa_cn_name
        </if>
        <if test='industryOrg =="IAS"'>
            ,LV4_PROD_RND_TEAM_CODE,LV4_PROD_RD_TEAM_CN_NAME
        </if>
        from fin_dm_opt_foi.${tablePreFix}_made_custom_comb_mid_d where del_flag ='N'
        and user_id = #{userId} and role_id = #{roleId} and select_flag = 'current'
        <if test='id != null and id!=""'>
            and id = #{id}
        </if>
        <if test='pageSymbol != null and pageSymbol!=""'>
            and page_flag = #{pageSymbol}
        </if>
    </select>

    <select id="getTempParentCustomCombList" resultType="java.lang.String">
        select connect_code
        from fin_dm_opt_foi.${tablePreFix}_custom_comb_mid_d where del_flag ='N'
        and user_id = #{userId} and role_id = #{roleId} and select_flag = 'parent'
        <if test='id != null and id!=""'>
            and id = #{id}
        </if>
        <if test='pageSymbol != null and pageSymbol!=""'>
            and page_flag = #{pageSymbol}
        </if>
    </select>

    <select id="getTempParentManufactureCustomCombList" resultType="java.lang.String">
        select connect_code
        from fin_dm_opt_foi.${tablePreFix}_made_custom_comb_mid_d where del_flag ='N' and enable_flag='Y'
        and user_id = #{userId} and role_id = #{roleId} and select_flag = 'parent'
        <if test='id != null and id!=""'>
            and id = #{id}
        </if>
        <if test='pageSymbol != null and pageSymbol!=""'>
            and page_flag = #{pageSymbol}
        </if>
    </select>

    <select id="getTempTableCustomCombList" resultMap="customResultMap">
        select custom_cn_name,id,LV0_PROD_RND_TEAM_CODE, LV0_PROD_RD_TEAM_CN_NAME,
        LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME,
        LV2_PROD_RND_TEAM_CODE,LV2_PROD_RD_TEAM_CN_NAME,LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME,l1_name,l2_name,
        dimension_code, dimension_cn_name, dimension_subcategory_code, dimension_subcategory_cn_name,
        dimension_sub_detail_code,spart_code,spart_cn_name,
        dimension_sub_detail_cn_name,l3_ceg_code,l3_ceg_short_cn_name,l4_ceg_code,l4_ceg_short_cn_name,category_code,category_cn_name,
        group_level,group_code,group_cn_name,user_id,role_id,view_flag,caliber_flag,granularity_type,oversea_flag,
        enable_flag,sub_enable_flag,lv0_prod_list_code,lv0_prod_list_cn_name,parent_code,is_separate,creation_date,last_update_date,created_by,last_updated_by,
        connect_code,connect_parent_code,select_flag
        <if test='industryOrg == "ENERGY"' >
            ,coa_code,coa_cn_name
        </if>
        <if test='industryOrg == "IAS"' >
            ,LV4_PROD_RND_TEAM_CODE,LV4_PROD_RD_TEAM_CN_NAME
        </if>
        from fin_dm_opt_foi.${tablePreFix}_custom_comb_mid_d where del_flag ='N'
        and user_id = #{userId} and role_id = #{roleId}
        <if test='id != null and id!=""'>
            and id = #{id}
        </if>
        <if test='pageSymbol != null and pageSymbol!=""'>
            and page_flag = #{pageSymbol}
        </if>
        <if test='lv0ProdRndTeamCode != null and lv0ProdRndTeamCode != ""'>
            AND lv0_prod_rnd_team_code = #{lv0ProdRndTeamCode}
        </if>
        <if test='lv1ProdRndTeamCode != null and lv1ProdRndTeamCode != ""'>
            AND lv1_prod_rnd_team_code = #{lv1ProdRndTeamCode}
        </if>
        <if test='lv2ProdRndTeamCode != null and lv2ProdRndTeamCode != ""'>
            AND lv2_prod_rnd_team_code = #{lv2ProdRndTeamCode}
        </if>
        <if test='lv3ProdRndTeamCode != null and lv3ProdRndTeamCode != ""'>
            AND lv3_prod_rnd_team_code = #{lv3ProdRndTeamCode}
        </if>
        <if test='lv4ProdRndTeamCode != null and lv4ProdRndTeamCode != ""'>
            AND lv4_prod_rnd_team_code = #{lv4ProdRndTeamCode}
        </if>
        <if test='l1Name != null and l1Name != ""'>
            AND l1_name = #{l1Name}
        </if>
        <if test='l2Name != null and l2Name != ""'>
            AND l2_name = #{l2Name}
        </if>
        <if test='dimensionCode != null and dimensionCode != ""'>
            AND dimension_code = #{dimensionCode}
        </if>
        <if test='dimensionSubCategoryCode != null and dimensionSubCategoryCode != ""'>
            AND dimension_subcategory_code = #{dimensionSubCategoryCode}
        </if>
        <if test='dimensionSubDetailCode != null and dimensionSubDetailCode != ""'>
            AND dimension_sub_detail_code = #{dimensionSubDetailCode}
        </if>
        <if test='spartCode != null and spartCode != ""'>
            AND spart_code = #{spartCode}
        </if>
        <if test='l3CegCode != null and l3CegCode != ""'>
            AND l3_ceg_code = #{l3CegCode}
        </if>
        <if test='l4CegCode != null and l4CegCode != ""'>
            AND l4_ceg_code = #{l4CegCode}
        </if>
        <if test='categoryCode != null and categoryCode != ""'>
            AND category_code = #{categoryCode}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            AND group_level = #{groupLevel}
        </if>
        <if test='keyWord != null and keyWord != ""'>
            AND group_cn_name LIKE CONCAT(CONCAT('%', #{keyWord}::text ,'%'))
        </if>
        <if test='groupLevelList != null and groupLevelList.size() > 0'>
            <foreach collection='groupLevelList' item="groupLevel" open="AND group_level IN (" close=")" index="index"
                     separator=",">
                #{groupLevel}
            </foreach>
        </if>
        <if test='groupLevelList != null and groupLevelList.size() > 0'>
            <foreach collection='groupLevelList' item="groupLevel" open="AND group_level IN (" close=")" index="index"
                     separator=",">
                #{groupLevel}
            </foreach>
        </if>
    </select>

    <select id="getTempTableManufactureCustomCombList" resultMap="customResultMap">
        select custom_cn_name,id,LV0_PROD_RND_TEAM_CODE, LV0_PROD_RD_TEAM_CN_NAME,
        LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME,
        LV2_PROD_RND_TEAM_CODE,LV2_PROD_RD_TEAM_CN_NAME,LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME,l1_name,l2_name,
        dimension_code, dimension_cn_name, dimension_subcategory_code, dimension_subcategory_cn_name,
        dimension_sub_detail_code,spart_code,spart_cn_name,
        dimension_sub_detail_cn_name,shipping_object_code,shipping_object_cn_name,manufacture_object_code,manufacture_object_cn_name,
        group_level,group_code,group_cn_name,user_id,role_id,view_flag,caliber_flag,granularity_type,oversea_flag,
        enable_flag,sub_enable_flag,lv0_prod_list_code,lv0_prod_list_cn_name,parent_code,is_separate,creation_date,last_update_date,created_by,last_updated_by,
        connect_code,connect_parent_code,select_flag
        <if test='industryOrg == "ENERGY"' >
            ,coa_code,coa_cn_name
        </if>
        <if test='industryOrg == "IAS"' >
            ,LV4_PROD_RND_TEAM_CODE,LV4_PROD_RD_TEAM_CN_NAME
        </if>
        from fin_dm_opt_foi.${tablePreFix}_made_custom_comb_mid_d where del_flag ='N'
        and user_id = #{userId} and role_id = #{roleId}
        <if test='id != null and id!=""'>
            and id = #{id}
        </if>
        <if test='pageSymbol != null and pageSymbol!=""'>
            and page_flag = #{pageSymbol}
        </if>
        <if test='lv0ProdRndTeamCode != null and lv0ProdRndTeamCode != ""'>
            AND lv0_prod_rnd_team_code = #{lv0ProdRndTeamCode}
        </if>
        <if test='lv1ProdRndTeamCode != null and lv1ProdRndTeamCode != ""'>
            AND lv1_prod_rnd_team_code = #{lv1ProdRndTeamCode}
        </if>
        <if test='lv2ProdRndTeamCode != null and lv2ProdRndTeamCode != ""'>
            AND lv2_prod_rnd_team_code = #{lv2ProdRndTeamCode}
        </if>
        <if test='lv3ProdRndTeamCode != null and lv3ProdRndTeamCode != ""'>
            AND lv3_prod_rnd_team_code = #{lv3ProdRndTeamCode}
        </if>
        <if test='lv4ProdRndTeamCode != null and lv4ProdRndTeamCode != ""'>
            AND lv4_prod_rnd_team_code = #{lv4ProdRndTeamCode}
        </if>
        <if test='l1Name != null and l1Name != ""'>
            AND l1_name = #{l1Name}
        </if>
        <if test='l2Name != null and l2Name != ""'>
            AND l2_name = #{l2Name}
        </if>
        <if test='dimensionCode != null and dimensionCode != ""'>
            AND dimension_code = #{dimensionCode}
        </if>
        <if test='dimensionSubCategoryCode != null and dimensionSubCategoryCode != ""'>
            AND dimension_subcategory_code = #{dimensionSubCategoryCode}
        </if>
        <if test='dimensionSubDetailCode != null and dimensionSubDetailCode != ""'>
            AND dimension_sub_detail_code = #{dimensionSubDetailCode}
        </if>
        <if test='spartCode != null and spartCode != ""'>
            AND spart_code = #{spartCode}
        </if>
        <if test='shippingObjectCode != null and shippingObjectCode != ""'>
            AND shipping_object_code = #{shippingObjectCode}
        </if>
        <if test='manufactureObjectCode != null and manufactureObjectCode != ""'>
            AND manufacture_object_code = #{manufactureObjectCode}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            AND group_level = #{groupLevel}
        </if>
        <if test='keyWord != null and keyWord != ""'>
            AND group_cn_name LIKE CONCAT(CONCAT('%', #{keyWord}::text ,'%'))
        </if>
        <if test='groupLevelList != null and groupLevelList.size() > 0'>
            <foreach collection='groupLevelList' item="groupLevel" open="AND group_level IN (" close=")" index="index"
                     separator=",">
                #{groupLevel}
            </foreach>
        </if>
    </select>

    <select id="getCountCustomComb" resultType="java.lang.Integer">
        select count(1)
        from fin_dm_opt_foi.${tablePreFix}_custom_comb_mid_d where del_flag ='N'
        and user_id = #{userId} and role_id = #{roleId}
        <if test='id != null and id!=""'>
            and id = #{id}
        </if>
        <if test='pageSymbol != null and pageSymbol!=""'>
            and page_flag = #{pageSymbol}
        </if>
        <if test='connectCode != null and connectCode!=""'>
            and connect_parent_code = #{connectCode}
        </if>
    </select>

    <select id="getCountManufactureCustomComb" resultType="java.lang.Integer">
        select count(1)
        from fin_dm_opt_foi.${tablePreFix}_made_custom_comb_mid_d where del_flag ='N'
        and user_id = #{userId} and role_id = #{roleId}
        <if test='id != null and id!=""'>
            and id = #{id}
        </if>
        <if test='pageSymbol != null and pageSymbol!=""'>
            and page_flag = #{pageSymbol}
        </if>
        <if test='connectCode != null and connectCode!=""'>
            and connect_parent_code = #{connectCode}
        </if>
    </select>

    <delete id="deleteCustomTemp">
        delete from fin_dm_opt_foi.${tablePreFix}_custom_comb_mid_d where user_id = #{userId} and role_id = #{roleId}
        <if test='id != null and id!=""'>
            and id = #{id}
        </if>
        <if test='pageSymbol != null and pageSymbol!=""'>
            and page_flag = #{pageSymbol}
        </if>
        <if test='connectCode != null and connectCode!=""'>
            and connect_code = #{connectCode}
        </if>
    </delete>

    <delete id="deleteManufacutreCustomTemp">
        delete from fin_dm_opt_foi.${tablePreFix}_made_custom_comb_mid_d where user_id = #{userId} and role_id = #{roleId}
        <if test='id != null and id!=""'>
            and id = #{id}
        </if>
        <if test='pageSymbol != null and pageSymbol!=""'>
            and page_flag = #{pageSymbol}
        </if>
        <if test='connectCode != null and connectCode!=""'>
            and connect_code = #{connectCode}
        </if>
    </delete>

    <delete id="deleteCustomTempByConnectCode">
        <foreach collection="currentCustomList" item="item" index="index" open="" close="" separator=";">
            delete from fin_dm_opt_foi.${combinationVO.tablePreFix}_custom_comb_mid_d where user_id = #{combinationVO.userId} and role_id =
            #{combinationVO.roleId}
            and id = #{combinationVO.id}
            and connect_code = #{item.connectCode}
            <if test='combinationVO.pageSymbol != null and combinationVO.pageSymbol!=""'>
                and page_flag = #{combinationVO.pageSymbol}
            </if>
        </foreach>
    </delete>

    <delete id="deleteManufacutreCustomTempByConnectCode">
        <foreach collection="currentCustomList" item="item" index="index" open="" close="" separator=";">
            delete from fin_dm_opt_foi.${combinationVO.tablePreFix}_made_custom_comb_mid_d where user_id = #{combinationVO.userId} and
            role_id = #{combinationVO.roleId}
            and id = #{combinationVO.id}
            and connect_code = #{item.connectCode}
            <if test='combinationVO.pageSymbol != null and combinationVO.pageSymbol!=""'>
                and page_flag = #{combinationVO.pageSymbol}
            </if>
        </foreach>
    </delete>

    <update id="updateMadeChageSelectFlagList">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update fin_dm_opt_foi.${combinationVO.tablePreFix}_made_custom_comb_mid_d
            set
            select_flag = #{item.selectFlag}
            where id = #{item.id} and user_id = #{item.userId} and role_id = #{item.roleId} and connect_code = #{item.connectCode}
            <if test='combinationVO.pageSymbol != null and combinationVO.pageSymbol!=""'>
                and page_flag = #{combinationVO.pageSymbol}
            </if>
        </foreach>
    </update>

    <update id="updateChageSelectFlagList">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update fin_dm_opt_foi.${combinationVO.tablePreFix}_custom_comb_mid_d
            set
            select_flag = #{item.selectFlag}
            where id = #{combinationVO.id} and user_id = #{combinationVO.userId} and role_id = #{combinationVO.roleId} and connect_code = #{item.connectCode}
            <if test='combinationVO.pageSymbol != null and combinationVO.pageSymbol!=""'>
                and page_flag = #{combinationVO.pageSymbol}
            </if>
        </foreach>
    </update>

</mapper>