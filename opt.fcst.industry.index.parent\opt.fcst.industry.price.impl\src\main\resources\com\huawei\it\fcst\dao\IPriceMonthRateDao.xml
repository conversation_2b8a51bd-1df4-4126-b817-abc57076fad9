<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.price.dao.IPriceMonthRateDao">
    <resultMap type="com.huawei.it.fcst.industry.price.vo.month.PriceMonthAnalysisVO" id="resultMap">
        <result property="basePeriodId" column="base_period_id"/>
        <result property="versionId" column="version_id"/>
        <result property="customId" column="custom_id"/>
        <result property="customCnName" column="custom_cn_name"/>
        <result property="periodYear" column="period_year"/>
        <result property="periodId" column="period_id"/>
        <result property="basePeriodId" column="base_period_id"/>
        <result property="groupCode" column="group_code"/>
        <result property="groupCnName" column="group_cn_name"/>
        <result property="groupLevel" column="group_level"/>
        <result property="rate" column="rate"/>
        <result property="ratePercent" column="rate_percent"/>
        <result property="rateFlag" column="rate_flag"/>
        <result property="parentCode" column="parent_code"/>
        <result property="parentCnName" column="parent_cn_name"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="regionCode" column="region_code"/>
        <result property="regionCnName" column="region_cn_name"/>
        <result property="repofficeCode" column="repoffice_code"/>
        <result property="repofficeCnName" column="repoffice_cn_name"/>
        <result property="bgCode" column="bg_code"/>
        <result property="bgCnName" column="bg_cn_name"/>
        <result property="overseaFlag" column="oversea_flag"/>
        <result property="signTopCustCategoryCode" column="sign_top_cust_category_code"/>
        <result property="signTopCustCategoryCnName" column="sign_top_cust_category_cn_name"/>
        <result property="signSubsidiaryCustcatgCnName" column="sign_subsidiary_custcatg_cn_name"/>
    </resultMap>

    <sql id="costRateChartFields">
        DISTINCT
        version_id,
        period_id,
        group_code,
        group_cn_name,
        group_level,
        ROUND(rate, 2) AS rate,
        ROUND(rate * 100, 2) || '%' AS rate_percent,
        rate_flag
    </sql>

    <select id="findPriceRateVOList" resultMap="resultMap">
        SELECT
        <include refid="costRateChartFields"/>
        FROM fin_dm_opt_foi.dm_fcst_price_mon_rate_t
        WHERE del_flag = 'N'
        <include refid="searchWhere"/>
        ORDER BY period_id
    </select>
    
    <sql id="searchWhere">
        <if test='monthAnalysisVO.periodStartTime != null'>
            AND period_id <![CDATA[ >= ]]> #{monthAnalysisVO.periodStartTime}
        </if>
        <if test='monthAnalysisVO.periodEndTime != null'>
            AND period_id <![CDATA[ <= ]]> #{monthAnalysisVO.periodEndTime}
        </if>
        <if test='monthAnalysisVO.versionId != null'>
            AND version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='monthAnalysisVO.rateFlag != null and monthAnalysisVO.rateFlag != ""'>
            AND rate_flag = #{monthAnalysisVO.rateFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.viewFlag != null and monthAnalysisVO.viewFlag != ""'>
            AND view_flag = #{monthAnalysisVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            AND oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
            AND bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
            AND region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
            AND repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.signTopCustCategoryCode != null and monthAnalysisVO.signTopCustCategoryCode != ""'>
            AND sign_top_cust_category_code = #{monthAnalysisVO.signTopCustCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.signSubsidiaryCustcatgCnName != null and monthAnalysisVO.signSubsidiaryCustcatgCnName != ""'>
            AND sign_subsidiary_custcatg_cn_name = #{monthAnalysisVO.signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupLevel != null and monthAnalysisVO.groupLevel != ""'>
            and group_level = #{monthAnalysisVO.groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupCodeList != null and monthAnalysisVO.groupCodeList != ""'>
            <foreach collection='monthAnalysisVO.groupCodeList' item="code" open="AND group_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.groupLevel != "LV0" and monthAnalysisVO.parentCodeList != null and monthAnalysisVO.parentCodeList != ""'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code" open="AND parent_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.groupLevel=="LV0" and monthAnalysisVO.lv1DimensionSet != null and monthAnalysisVO.lv1DimensionSet.size() > 0'>
            <foreach collection='monthAnalysisVO.lv1DimensionSet' item="code" open="and group_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.groupLevel=="LV1" and monthAnalysisVO.lv2DimensionSet != null and monthAnalysisVO.lv2DimensionSet.size() > 0'>
            <foreach collection='monthAnalysisVO.lv2DimensionSet' item="code" open="and group_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
    </sql>

    <select id="findBlurPriceRateVOList" resultMap="resultMap">
        SELECT
        <include refid="costRateChartFields"/>
        FROM fin_dm_opt_foi.dm_fcst_price_base_cus_mon_rate_t
        WHERE del_flag = 'N'
        <include refid="blurSearchWhere"/>
        ORDER BY period_id
    </select>

    <sql id="blurSearchWhere">
        <if test='monthAnalysisVO.periodStartTime != null'>
            AND period_id <![CDATA[ >= ]]> #{monthAnalysisVO.periodStartTime}
        </if>
        <if test='monthAnalysisVO.periodEndTime != null'>
            AND period_id <![CDATA[ <= ]]> #{monthAnalysisVO.periodEndTime}
        </if>
        <if test='monthAnalysisVO.versionId != null'>
            AND version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='monthAnalysisVO.customIdList != null and monthAnalysisVO.customIdList != ""'>
            <foreach collection='monthAnalysisVO.customIdList' item="code" open="AND custom_id IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.parentLevel != null and monthAnalysisVO.parentLevel != ""'>
            AND parent_level = #{monthAnalysisVO.parentLevel,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.rateFlag != null and monthAnalysisVO.rateFlag != ""'>
            AND rate_flag = #{monthAnalysisVO.rateFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.viewFlag != null and monthAnalysisVO.viewFlag != ""'>
            AND view_flag = #{monthAnalysisVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            AND oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
            AND bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
            AND region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
            AND repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.signTopCustCategoryCode != null and monthAnalysisVO.signTopCustCategoryCode != ""'>
            AND sign_top_cust_category_code = #{monthAnalysisVO.signTopCustCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.signSubsidiaryCustcatgCnName != null and monthAnalysisVO.signSubsidiaryCustcatgCnName != ""'>
            AND sign_subsidiary_custcatg_cn_name = #{monthAnalysisVO.signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupLevel != null and monthAnalysisVO.groupLevel != ""'>
            and group_level = #{monthAnalysisVO.groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.customGroupCodeList != null and monthAnalysisVO.customGroupCodeList != ""'>
            <foreach collection='monthAnalysisVO.customGroupCodeList' item="code" open="AND group_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.groupLevel != "LV0" and monthAnalysisVO.parentCodeList != null and monthAnalysisVO.parentCodeList != ""'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code" open="AND parent_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
    </sql>

</mapper>
