/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.config;

import cn.hutool.core.date.DateUtil;
import com.huawei.it.fcst.industry.index.dao.IDmDimCatgModlCegIctDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocCatgCegIctDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocVersionDao;
import com.huawei.it.fcst.industry.index.enums.IndustryConst;
import com.huawei.it.fcst.industry.index.impl.relation.ConfigDimensionManageService;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.config.DimensionParamVO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocCatgCegIctDTO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoDTO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoVO;
import com.huawei.it.fcst.industry.index.vo.relation.BackDimensionVO;
import com.huawei.it.fcst.industry.index.vo.relation.DimensionInputVO;
import com.huawei.it.fcst.industry.index.vo.relation.DmDimCatgModlCegIctVO;
import com.huawei.it.fcst.industry.index.vo.relation.DmDimMaterialCodeVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.security.UserVO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * ConfigDimensionServiceTest Class
 *
 * <AUTHOR>
 * @since 2023/4/7
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class ConfigDimensionServiceTest {
    private static final Logger LOGGER = LoggerFactory.getLogger(ConfigDimensionService.class);

    @InjectMocks
    private ConfigDimensionService configDimensionService;

    @Mock
    private IDmFocVersionDao dmFocVersionDao;

    @Mock
    private IDmFocCatgCegIctDao dmFocCatgCegIctDao;

    @Mock
    private IDmDimCatgModlCegIctDao dmDimCatgModlCegIctDao;

    @Mock
    private IDmDimCatgModlCegIctDao iDmDimCatgModlCegIctDao;

    @Mock
    private ConfigDimensionManageService configDimensionManageService;

    @Before
    public void before() throws Exception {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);
    }

    @Test
    public void findVersion() throws CommonApplicationException {
        DmFocVersionInfoVO dmFocVersionInfoVo = new DmFocVersionInfoVO();
        dmFocVersionInfoVo.setDataType("ITEM");
        ResultDataVO version = configDimensionService.findVersion(dmFocVersionInfoVo);
        Assert.assertNotNull(version);
    }

    @Test
    public void findVersion2() throws CommonApplicationException {
        DmFocVersionInfoVO dmFocVersionInfoVo = new DmFocVersionInfoVO();
        dmFocVersionInfoVo.setDataType("DIMENSION");
        ResultDataVO version = configDimensionService.findVersion(dmFocVersionInfoVo);
        Assert.assertNotNull(version);
    }

    @Test
    public void getListByVersionId() throws CommonApplicationException {
        Long versionId = 10L;
        String l3CegCode=null;
        ResultDataVO byVersionId = configDimensionService.getListByVersionId("ICT",versionId,l3CegCode);
        Assert.assertNotNull(byVersionId);
    }

    @Test
    public void getListByVersionId2() throws CommonApplicationException {
        Long versionId = 10L;
        String l3CegCode="117501";
        ResultDataVO byVersionId = configDimensionService.getListByVersionId("ICT",versionId,l3CegCode);
        Assert.assertNotNull(byVersionId);
    }

    @Test
    public void getShortCnNameAndCode() {
        String keyword = "其他";
        String l3CegCode="12305";
        List<DmFocVersionInfoDTO> dmFocVersionDTOList = new ArrayList<>();
        DmFocVersionInfoDTO infoDTO = new DmFocVersionInfoDTO();
        infoDTO.setVersionId(11L);
        dmFocVersionDTOList.add(infoDTO);

        when(dmFocVersionDao.findDmFocVersionDTOList(any())).thenReturn(dmFocVersionDTOList);
        ResultDataVO nameAndCode = configDimensionService.getShortCnNameAndCode("ICT",keyword,l3CegCode);
        Assert.assertNotNull(nameAndCode);
    }

    @Test
    public void getShortCnNameAndCode2Test() {
        String keyword = "";
        String l3CegCode="";
        List<DmFocVersionInfoDTO> dmFocVersionDTOList = new ArrayList<>();
        DmFocVersionInfoDTO infoDTO = new DmFocVersionInfoDTO();
        infoDTO.setVersionId(11L);
        dmFocVersionDTOList.add(infoDTO);

        when(dmFocVersionDao.findDmFocVersionDTOList(any())).thenReturn(dmFocVersionDTOList);
        ResultDataVO nameAndCode = configDimensionService.getShortCnNameAndCode("ICT",keyword,l3CegCode);
        Assert.assertNotNull(nameAndCode);
    }

    @Test
    public void getShortCnNameAndCode3Test() {
        String keyword = "other";
        String l3CegCode="12305";
        List<DmFocVersionInfoDTO> dmFocVersionDTOList = new ArrayList<>();
        DmFocVersionInfoDTO infoDTO = new DmFocVersionInfoDTO();
        infoDTO.setVersionId(11L);
        dmFocVersionDTOList.add(infoDTO);

        when(dmFocVersionDao.findDmFocVersionDTOList(any())).thenReturn(dmFocVersionDTOList);
        ResultDataVO nameAndCode = configDimensionService.getShortCnNameAndCode("ICT",keyword,l3CegCode);
        Assert.assertNotNull(nameAndCode);
    }

    @Test
    public void getCategoryCodeByName3Test() {
        String keyword = "其他";
        List<DmFocVersionInfoDTO> dmFocVersionDTOList = new ArrayList<>();
        DmFocVersionInfoDTO infoDTO = new DmFocVersionInfoDTO();
        infoDTO.setVersionId(11L);
        dmFocVersionDTOList.add(infoDTO);

        List<DmDimMaterialCodeVO> resultList=new ArrayList<>();
        DmDimMaterialCodeVO codeVO = new DmDimMaterialCodeVO();
        codeVO.setItemSubtypeCode("0307");
        resultList.add(codeVO);

        List<Map> dmFocCatgCegIctDTOS=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("category_code", "0307");
        map.put("category_cn_name", "电缆");
        dmFocCatgCegIctDTOS.add(map);

        when(dmFocVersionDao.findDmFocVersionDTOList(any())).thenReturn(dmFocVersionDTOList);
        when(dmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(resultList);
        when(iDmDimCatgModlCegIctDao.findCatgCegIctList(any())).thenReturn(dmFocCatgCegIctDTOS);
        ResultDataVO codeByName = configDimensionService.getCategoryCodeByName(keyword,"ICT");
        Assert.assertNotNull(codeByName);
    }

    @Test
    public void getCategoryCodeByName2Test() {
        String keyword = "其他";
        List<DmFocVersionInfoDTO> dmFocVersionDTOList = new ArrayList<>();
        DmFocVersionInfoDTO infoDTO = new DmFocVersionInfoDTO();
        infoDTO.setVersionId(11L);
        dmFocVersionDTOList.add(infoDTO);

        List<DmDimMaterialCodeVO> resultList=new ArrayList<>();
        DmDimMaterialCodeVO codeVO = new DmDimMaterialCodeVO();
        codeVO.setItemSubtypeCode("0308");
        resultList.add(codeVO);

        List<Map> dmFocCatgCegIctDTOS=new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("category_code", "0307");
        map.put("category_cn_name", "电缆");
        dmFocCatgCegIctDTOS.add(map);

        when(dmFocVersionDao.findDmFocVersionDTOList(any())).thenReturn(dmFocVersionDTOList);
        when(dmDimCatgModlCegIctDao.findDimMaterialCodeD(any())).thenReturn(resultList);
        when(iDmDimCatgModlCegIctDao.findCatgCegIctList(any())).thenReturn(dmFocCatgCegIctDTOS);
        ResultDataVO codeByName = configDimensionService.getCategoryCodeByName(keyword,"ICT");
        Assert.assertNotNull(codeByName);
    }

    @Test
    public void relationList() throws CommonApplicationException {
        DimensionParamVO dimensionParamVO = new DimensionParamVO();
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configDimensionService.relationList(dimensionParamVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }

        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationList2Test() throws CommonApplicationException {
        DimensionParamVO dimensionParamVO = new DimensionParamVO();
        dimensionParamVO.setVersionId(15L);
        dimensionParamVO.setPageSize(10);
        dimensionParamVO.setPageIndex(1);
        PagedResult<DmFocCatgCegIctDTO> byPage = new PagedResult<>();
        DmFocCatgCegIctDTO cegIctDTO = new DmFocCatgCegIctDTO();
        cegIctDTO.setCategoryCode("4512D");
        byPage.setResult(Arrays.asList(cegIctDTO));
        when(dmFocCatgCegIctDao.findByPage(any(), any())).thenReturn(byPage);

        ResultDataVO resultDataVO = configDimensionService.relationList(dimensionParamVO);
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void dimensionUpdate() {
        DimensionInputVO dimensionInputVO=new DimensionInputVO();

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configDimensionService.dimensionUpdate(dimensionInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void dimensionUpdate2Test() {
        DimensionInputVO dimensionInputVO=new DimensionInputVO();
        dimensionInputVO.setVersionId(10L);
        DmFocVersionInfoDTO versionVOById = new DmFocVersionInfoDTO();
        List<DmDimCatgModlCegIctVO> list = new ArrayList<>();
        dimensionInputVO.setDimCatgModlCegIctList(list);
        versionVOById.setStatus(1L);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionVOById);

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configDimensionService.dimensionUpdate(dimensionInputVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void dimensionUpdate22Test() {
        DimensionInputVO dimensionInputVO=new DimensionInputVO();
        dimensionInputVO.setVersionId(10L);
        DmFocVersionInfoDTO versionVOById = new DmFocVersionInfoDTO();
        List<DmDimCatgModlCegIctVO> list = new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setVersionId(15L);
        list.add(cegIctVO);
        dimensionInputVO.setDimCatgModlCegIctList(list);
        versionVOById.setStatus(0L);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionVOById);

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configDimensionService.dimensionUpdate(dimensionInputVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void dimensionUpdate3Test() throws CommonApplicationException {
        DimensionInputVO dimensionInputVO=new DimensionInputVO();
        dimensionInputVO.setVersionId(10L);
        DmFocVersionInfoDTO versionVOById = new DmFocVersionInfoDTO();
        versionVOById.setStatus(0L);
        versionVOById.setVersion("20230501");

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionVOById);

        ResultDataVO resultDataVO = configDimensionService.dimensionUpdate(dimensionInputVO);
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void dimensionUpdate4Test() throws CommonApplicationException {
        DimensionInputVO dimensionInputVO=new DimensionInputVO();
        dimensionInputVO.setVersionId(10L);

        DmFocVersionInfoDTO versionVOById = new DmFocVersionInfoDTO();
        versionVOById.setStatus(0L);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionVOById);

        ResultDataVO resultDataVO = configDimensionService.dimensionUpdate(dimensionInputVO);
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void dimensionUpdate5Test() throws CommonApplicationException {
        DimensionInputVO dimensionInputVO=new DimensionInputVO();
        dimensionInputVO.setVersionId(10L);
        DmFocVersionInfoDTO versionVOById = new DmFocVersionInfoDTO();
        versionVOById.setStatus(0L);
        versionVOById.setVersion("20230501");

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionVOById);

        ResultDataVO resultDataVO = configDimensionService.dimensionUpdate(dimensionInputVO);
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void dimensionUpdate6Test() throws CommonApplicationException {
        DimensionInputVO dimensionInputVO=new DimensionInputVO();
        dimensionInputVO.setVersionId(10L);
        DmFocVersionInfoDTO versionVOById = new DmFocVersionInfoDTO();
        versionVOById.setStatus(0L);
        String dateString = DateUtil.today().replace("-", "");
        dateString =dateString+"-001";
        versionVOById.setVersion(dateString);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionVOById);

        ResultDataVO resultDataVO = configDimensionService.dimensionUpdate(dimensionInputVO);
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void dimensionUpdate7Test() throws CommonApplicationException {
        DimensionInputVO dimensionInputVO=new DimensionInputVO();
        dimensionInputVO.setVersionId(10L);
        DmFocVersionInfoDTO versionVOById = new DmFocVersionInfoDTO();
        versionVOById.setStatus(0L);
        versionVOById.setVersion("20201011");

        List<DmFocVersionInfoDTO> versionVoLists=new ArrayList<>();
        versionVoLists.add(versionVOById);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionVOById);
        when(dmFocVersionDao.findDmFocVersionDTOList(any())).thenReturn(versionVoLists);

        ResultDataVO resultDataVO = configDimensionService.dimensionUpdate(dimensionInputVO);
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void dimensionUpdate8Test() throws CommonApplicationException {
        DimensionInputVO dimensionInputVO=new DimensionInputVO();
        dimensionInputVO.setVersionId(10L);
        List<DmDimCatgModlCegIctVO> list = new ArrayList<>();
        DmDimCatgModlCegIctVO cegIctVO = new DmDimCatgModlCegIctVO();
        cegIctVO.setL3CegCode("17571");
        cegIctVO.setL3CegShortCnName("贵州");
        cegIctVO.setL3CegCnName("贵州代表处PP");
        cegIctVO.setCategoryCode("0105");
        cegIctVO.setCategoryCnName("数据通信设备");
        list.add(cegIctVO);
        dimensionInputVO.setDimCatgModlCegIctList(list);
        DmFocVersionInfoDTO versionVOById = new DmFocVersionInfoDTO();
        versionVOById.setStatus(0L);
        versionVOById.setVersion("20201011-001");

        List<DmFocVersionInfoDTO> versionVoLists=new ArrayList<>();
        versionVoLists.add(versionVOById);
        ResultDataVO resultDataVO=new ResultDataVO();

        DmFocVersionInfoDTO dmFocPlanVersionVO = new DmFocVersionInfoDTO();
        dmFocPlanVersionVO.setVersion("20230605");
        dmFocPlanVersionVO.setVersionId(14L);
        BackDimensionVO backDimensionVO=new BackDimensionVO();
        backDimensionVO.setDmFocPlanVersionVO(dmFocPlanVersionVO);
        resultDataVO.setData(backDimensionVO);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionVOById);
        when(dmFocVersionDao.findDmFocVersionDTOList(any())).thenReturn(versionVoLists);
        when(configDimensionManageService.relationSave(any())).thenReturn(resultDataVO);

        ResultDataVO resultDataVO1 = configDimensionService.dimensionUpdate(dimensionInputVO);
        Assert.assertNotNull(resultDataVO1);
    }

    @Test
    public void dimensionUpdate9Test() throws CommonApplicationException {
        DimensionInputVO dimensionInputVO=new DimensionInputVO();
        dimensionInputVO.setVersionId(10L);
        DmFocVersionInfoDTO versionVOById = new DmFocVersionInfoDTO();
        versionVOById.setStatus(0L);
        versionVOById.setVersion("20201011-auto");

        List<DmFocVersionInfoDTO> versionVoLists=new ArrayList<>();
        versionVoLists.add(versionVOById);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionVOById);
        when(dmFocVersionDao.findDmFocVersionDTOList(any())).thenReturn(versionVoLists);

        ResultDataVO resultDataVO = configDimensionService.dimensionUpdate(dimensionInputVO);
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void dimensionUpdate91Test() throws CommonApplicationException {
        DimensionInputVO dimensionInputVO=new DimensionInputVO();
        dimensionInputVO.setVersionId(10L);
        DmFocVersionInfoDTO versionVOById = new DmFocVersionInfoDTO();
        versionVOById.setStatus(0L);
        versionVOById.setVersion("20201011-1");

        List<DmFocVersionInfoDTO> versionVoLists=new ArrayList<>();
        versionVoLists.add(versionVOById);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionVOById);
        when(dmFocVersionDao.findDmFocVersionDTOList(any())).thenReturn(versionVoLists);

        ResultDataVO resultDataVO = configDimensionService.dimensionUpdate(dimensionInputVO);
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void dimensionUpdate1Test() throws CommonApplicationException {
        DimensionInputVO dimensionInputVO=new DimensionInputVO();
        dimensionInputVO.setVersionId(10L);
        DmFocVersionInfoDTO versionVOById = new DmFocVersionInfoDTO();
        versionVOById.setStatus(0L);
        versionVOById.setVersion("20201011-011");

        List<DmFocVersionInfoDTO> versionVoLists=new ArrayList<>();
        versionVoLists.add(versionVOById);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionVOById);
        when(dmFocVersionDao.findDmFocVersionDTOList(any())).thenReturn(versionVoLists);

        ResultDataVO resultDataVO = configDimensionService.dimensionUpdate(dimensionInputVO);
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void getSystemTime() {
        ResultDataVO systemTime = configDimensionService.getSystemTime();
        Assert.assertNotNull(systemTime);
    }

    @Test
    public void relationDelete() throws CommonApplicationException {
        List<DmFocCatgCegIctDTO> dmFocCatgCegIctDTOList = new ArrayList<>();
        DmFocCatgCegIctDTO cegIctDTO = new DmFocCatgCegIctDTO();
        dmFocCatgCegIctDTOList.add(cegIctDTO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configDimensionService.relationDelete(dmFocCatgCegIctDTOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationDelete2Test() {
        List<DmFocCatgCegIctDTO> dmFocCatgCegIctDTOList = new ArrayList<>();
        DmFocCatgCegIctDTO cegIctDTO = new DmFocCatgCegIctDTO();
        cegIctDTO.setVersionId(15L);
        dmFocCatgCegIctDTOList.add(cegIctDTO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configDimensionService.relationDelete(dmFocCatgCegIctDTOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationDelete21Test() {
        List<DmFocCatgCegIctDTO> dmFocCatgCegIctDTOList = new ArrayList<>();
        DmFocCatgCegIctDTO cegIctDTO = new DmFocCatgCegIctDTO();
        cegIctDTO.setVersionId(15L);
        cegIctDTO.setL3CegCnName("l3name");
        dmFocCatgCegIctDTOList.add(cegIctDTO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configDimensionService.relationDelete(dmFocCatgCegIctDTOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationDelete22Test() {
        List<DmFocCatgCegIctDTO> dmFocCatgCegIctDTOList = new ArrayList<>();
        DmFocCatgCegIctDTO cegIctDTO = new DmFocCatgCegIctDTO();
        cegIctDTO.setVersionId(15L);
        cegIctDTO.setCategoryCnName("cate");
        dmFocCatgCegIctDTOList.add(cegIctDTO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configDimensionService.relationDelete(dmFocCatgCegIctDTOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationDelete3Test() {
        List<DmFocCatgCegIctDTO> dmFocCatgCegIctDTOList = new ArrayList<>();
        DmFocCatgCegIctDTO cegIctDTO = new DmFocCatgCegIctDTO();
        cegIctDTO.setVersionId(15L);
        cegIctDTO.setL3CegCnName("元器件");
        cegIctDTO.setCategoryCnName("电线电缆");
        dmFocCatgCegIctDTOList.add(cegIctDTO);
        ResultDataVO resultDataVO = new ResultDataVO();
        List<DmFocVersionInfoDTO> versionList=new ArrayList<>();
        DmFocVersionInfoDTO versionInfoDTO = new DmFocVersionInfoDTO();
        versionInfoDTO.setVersion("20230410-001");
        versionInfoDTO.setVersionId(11L);
        versionList.add(versionInfoDTO);

        when(dmFocVersionDao.findDmFocVersionList(any())).thenReturn(versionList);

        try {
            resultDataVO = configDimensionService.relationDelete(dmFocCatgCegIctDTOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationDelete4Test() {
        List<DmFocCatgCegIctDTO> dmFocCatgCegIctDTOList = new ArrayList<>();
        DmFocCatgCegIctDTO cegIctDTO = new DmFocCatgCegIctDTO();
        cegIctDTO.setVersionId(15L);
        cegIctDTO.setL3CegCnName("元器件");
        cegIctDTO.setCategoryCnName("电线电缆");
        dmFocCatgCegIctDTOList.add(cegIctDTO);
        ResultDataVO resultDataVO = new ResultDataVO();
        List<DmFocVersionInfoDTO> versionList=new ArrayList<>();
        DmFocVersionInfoDTO versionInfoDTO = new DmFocVersionInfoDTO();
        versionInfoDTO.setVersion("20230410-001");
        versionInfoDTO.setVersionId(11L);
        versionList.add(versionInfoDTO);

        when(dmFocVersionDao.findDmFocVersionList(any())).thenReturn(versionList);

        try {
            resultDataVO = configDimensionService.relationDelete(dmFocCatgCegIctDTOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationDelete5Test() {
        List<DmFocCatgCegIctDTO> dmFocCatgCegIctDTOList = new ArrayList<>();
        DmFocCatgCegIctDTO cegIctDTO = new DmFocCatgCegIctDTO();
        cegIctDTO.setVersionId(15L);
        cegIctDTO.setL3CegCnName("元器件");
        cegIctDTO.setCategoryCnName("电线电缆");
        dmFocCatgCegIctDTOList.add(cegIctDTO);
        ResultDataVO resultDataVO = new ResultDataVO();
        when(dmFocVersionDao.getVersionKey("dm_foc")).thenReturn(10L);
        when(dmFocCatgCegIctDao.findDmFocCatgCegIctStatus("dm_foc",10L)).thenReturn(dmFocCatgCegIctDTOList);

        try {
            resultDataVO = configDimensionService.relationDelete(dmFocCatgCegIctDTOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationDelete6Test() {
        List<DmFocCatgCegIctDTO> dmFocCatgCegIctDTOList = new ArrayList<>();
        DmFocCatgCegIctDTO cegIctDTO = new DmFocCatgCegIctDTO();
        cegIctDTO.setVersionId(15L);
        cegIctDTO.setL3CegCnName("元器件");
        cegIctDTO.setCategoryCnName("电线电缆");
        dmFocCatgCegIctDTOList.add(cegIctDTO);
        ResultDataVO resultDataVO = new ResultDataVO();

        List<DmFocVersionInfoDTO> versionMap=new ArrayList<>();
        DmFocVersionInfoDTO versionInfoDTO = new DmFocVersionInfoDTO();
        versionInfoDTO.setVersionId(11L);
        versionMap.add(versionInfoDTO);

        when(dmFocVersionDao.getVersionKey("dm_foc")).thenReturn(10L);
        when(dmFocCatgCegIctDao.findDmFocCatgCegIctStatus("dm_foc",10L)).thenReturn(dmFocCatgCegIctDTOList);
        when(dmFocVersionDao.findDmFocVersionDTOList(any())).thenReturn(versionMap);

        try {
            resultDataVO = configDimensionService.relationDelete(dmFocCatgCegIctDTOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationDelete7Test() {
        List<DmFocCatgCegIctDTO> dmFocCatgCegIctDTOList = new ArrayList<>();
        DmFocCatgCegIctDTO cegIctDTO = new DmFocCatgCegIctDTO();
        cegIctDTO.setVersionId(15L);
        cegIctDTO.setL3CegCnName("元器件");
        cegIctDTO.setCategoryCnName("电线电缆");
        dmFocCatgCegIctDTOList.add(cegIctDTO);
        ResultDataVO resultDataVO = new ResultDataVO();

        List<DmFocVersionInfoDTO> versionMap=new ArrayList<>();
        DmFocVersionInfoDTO versionInfoDTO = new DmFocVersionInfoDTO();
        versionInfoDTO.setVersionId(11L);
        versionInfoDTO.setVersion("20230501-002");
        versionMap.add(versionInfoDTO);

        when(dmFocVersionDao.getVersionKey("dm_foc")).thenReturn(10L);
        when(dmFocCatgCegIctDao.findDmFocCatgCegIctStatus("dm_foc",10L)).thenReturn(dmFocCatgCegIctDTOList);
        when(dmFocVersionDao.findDmFocVersionDTOList(any())).thenReturn(versionMap);

        try {
            resultDataVO = configDimensionService.relationDelete(dmFocCatgCegIctDTOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationDelete8Test() {
        List<DmFocCatgCegIctDTO> dmFocCatgCegIctDTOList = new ArrayList<>();
        DmFocCatgCegIctDTO cegIctDTO = new DmFocCatgCegIctDTO();
        cegIctDTO.setVersionId(15L);
        cegIctDTO.setL3CegCnName("元器件");
        cegIctDTO.setL3CegCode("1175A");
        cegIctDTO.setL3CegShortCnName("元器");
        cegIctDTO.setL4CegCode("11562");
        cegIctDTO.setL4CegCnName("元器_粉圆");
        cegIctDTO.setL4CegShortCnName("粉圆");
        cegIctDTO.setCategoryCode("2011D");
        cegIctDTO.setCategoryCnName("电线电缆");
        dmFocCatgCegIctDTOList.add(cegIctDTO);

        List<DmFocCatgCegIctDTO> dmFocCatgCegIctDTOList2 = new ArrayList<>();
        DmFocCatgCegIctDTO cegIctDTO2 = new DmFocCatgCegIctDTO();
        cegIctDTO2.setVersionId(15L);
        cegIctDTO2.setL3CegCnName("元器件");
        cegIctDTO2.setL3CegCode("1175A");
        cegIctDTO2.setL3CegShortCnName("元器2");
        cegIctDTO2.setCategoryCode("2011D");
        cegIctDTO2.setCategoryCnName("电线电缆");
        dmFocCatgCegIctDTOList2.add(cegIctDTO2);

        ResultDataVO resultDataVO = new ResultDataVO();
        List<DmFocVersionInfoDTO> versionMap=new ArrayList<>();
        DmFocVersionInfoDTO versionInfoDTO = new DmFocVersionInfoDTO();
        versionInfoDTO.setVersionId(11L);
        versionInfoDTO.setVersion("20230501-002");
        versionMap.add(versionInfoDTO);
        when(dmFocVersionDao.getVersionKey("dm_foc")).thenReturn(10L);
        when(dmFocCatgCegIctDao.findDmFocCatgCegIctStatus("dm_foc",15L)).thenReturn(dmFocCatgCegIctDTOList);
        when(dmFocCatgCegIctDao.findDmFocCatgCegIctStatus("dm_foc",11L)).thenReturn(dmFocCatgCegIctDTOList2);
        when(dmFocVersionDao.findDmFocVersionDTOList(any())).thenReturn(versionMap);
        when(dmFocVersionDao.findDmFocVersionList(any())).thenReturn(versionMap);

        try {
            resultDataVO = configDimensionService.relationDelete(dmFocCatgCegIctDTOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationDelete9Test() {
        List<DmFocCatgCegIctDTO> dmFocCatgCegIctDTOList = new ArrayList<>();
        DmFocCatgCegIctDTO cegIctDTO = new DmFocCatgCegIctDTO();
        cegIctDTO.setVersionId(15L);
        cegIctDTO.setL3CegCnName("元器件");
        cegIctDTO.setL3CegCode("1175A");
        cegIctDTO.setL3CegShortCnName("元器");
        cegIctDTO.setCategoryCode("2011D");
        cegIctDTO.setCategoryCnName("电线电缆");
        dmFocCatgCegIctDTOList.add(cegIctDTO);

        List<DmFocCatgCegIctDTO> dmFocCatgCegIctDTOList2 = new ArrayList<>();
        DmFocCatgCegIctDTO cegIctDTO2 = new DmFocCatgCegIctDTO();
        cegIctDTO2.setVersionId(15L);
        cegIctDTO2.setL3CegCnName("元器件");
        cegIctDTO2.setL3CegCode("1175A");
        cegIctDTO2.setL3CegShortCnName("元器2");
        cegIctDTO2.setCategoryCode("2011D");
        cegIctDTO2.setCategoryCnName("电线电缆");
        dmFocCatgCegIctDTOList2.add(cegIctDTO2);

        ResultDataVO resultDataVO = new ResultDataVO();
        List<DmFocVersionInfoDTO> versionMap=new ArrayList<>();
        DmFocVersionInfoDTO versionInfoDTO = new DmFocVersionInfoDTO();
        versionInfoDTO.setVersionId(15L);
        versionInfoDTO.setVersion("20230501-002");
        versionMap.add(versionInfoDTO);

        when(dmFocVersionDao.getVersionKey("dm_foc")).thenReturn(10L);
        when(dmFocCatgCegIctDao.findDmFocCatgCegIctStatus("dm_foc",15L)).thenReturn(dmFocCatgCegIctDTOList);
        when(dmFocCatgCegIctDao.findDmFocCatgCegIctStatus("dm_foc",11L)).thenReturn(dmFocCatgCegIctDTOList2);
        when(dmFocVersionDao.findDmFocVersionDTOList(any())).thenReturn(versionMap);
        when(dmFocVersionDao.findDmFocVersionList(any())).thenReturn(versionMap);

        try {
            resultDataVO = configDimensionService.relationDelete(dmFocCatgCegIctDTOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void relationDelete10Test() {
        List<DmFocCatgCegIctDTO> dmFocCatgCegIctDTOList = new ArrayList<>();
        DmFocCatgCegIctDTO cegIctDTO = new DmFocCatgCegIctDTO();
        cegIctDTO.setVersionId(15L);
        cegIctDTO.setL3CegCnName("元器件");
        cegIctDTO.setL3CegCode("1175A");
        cegIctDTO.setL3CegShortCnName("元器");
        cegIctDTO.setL4CegCnName("元器_粉圆");
        cegIctDTO.setL4CegCode("11562");
        cegIctDTO.setL4CegShortCnName("粉圆");
        cegIctDTO.setCategoryCode("2011D");
        cegIctDTO.setCategoryCode("2011D");
        cegIctDTO.setCategoryCnName("电线电缆");
        dmFocCatgCegIctDTOList.add(cegIctDTO);

        List<DmFocCatgCegIctDTO> dmFocCatgCegIctDTOList2 = new ArrayList<>();
        DmFocCatgCegIctDTO cegIctDTO2 = new DmFocCatgCegIctDTO();
        cegIctDTO2.setVersionId(15L);
        cegIctDTO2.setL3CegCnName("元器件");
        cegIctDTO2.setL3CegCode("1175B");
        cegIctDTO2.setL3CegShortCnName("元器");
        cegIctDTO2.setCategoryCode("2011E");
        cegIctDTO2.setCategoryCnName("电线电缆");
        dmFocCatgCegIctDTOList2.add(cegIctDTO2);

        ResultDataVO resultDataVO = new ResultDataVO();
        List<DmFocVersionInfoDTO> versionMap=new ArrayList<>();
        DmFocVersionInfoDTO versionInfoDTO = new DmFocVersionInfoDTO();
        versionInfoDTO.setVersionId(11L);
        versionInfoDTO.setVersion("20230501-002");
        versionMap.add(versionInfoDTO);
        when(dmFocVersionDao.getVersionKey(null)).thenReturn(10L);
        when(dmFocCatgCegIctDao.findDmFocCatgCegIctStatus("",15L)).thenReturn(dmFocCatgCegIctDTOList);
        when(dmFocCatgCegIctDao.findDmFocCatgCegIctStatus("",11L)).thenReturn(dmFocCatgCegIctDTOList2);
        when(dmFocVersionDao.findDmFocVersionDTOList(any())).thenReturn(versionMap);
        when(dmFocVersionDao.findDmFocVersionList(any())).thenReturn(versionMap);

        try {
            resultDataVO = configDimensionService.relationDelete(dmFocCatgCegIctDTOList);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void getSyncDimPurchar() {
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configDimensionService.getSyncDimPurchar(IndustryConst.INDUSTRY_ORG_FUNC.I.getValue());
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void getSyncDimPurchar2Test() {
        ResultDataVO resultDataVO = new ResultDataVO();
        when(dmFocVersionDao.syncDimPurcharFunction(IndustryConst.INDUSTRY_ORG_FUNC.I.getValue())).thenReturn("SUCCESS");
        try {
            resultDataVO = configDimensionService.getSyncDimPurchar(IndustryConst.INDUSTRY_ORG_FUNC.I.getValue());
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }
}