/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.config;

import com.huawei.it.fcst.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * DimensionParamVO Class
 *
 * <AUTHOR>
 * @since 2023/3/13
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "配置管理页面维度关系入参")
public class DimensionParamVO extends BaseVO {
    /**
     *
     * 专项采购认证部名称
     */
    private String l3CegCnName;

    /**
     *
     * 专项采购认证部编码
     */
    private String l3CegCode;

    /**
     *
     * 模块名称
     */
    private String l4CegCnName;

    /**
     *
     * 模块编码
     */
    private String l4CegCode;

    /**
     *
     * 品类编码
     */
    private String categoryCode;

    /**
     *
     * 品类名称
     */
    private String categoryName;

    /**
     *
     * 版本号
     */
    private Long  versionId;

    /**
     *
     *  页码
     */
    private Integer pageIndex;

    /**
     *
     *  一页数量
     */
    private Integer pageSize;

    /**
     *
     *  总条数
     */
    private Integer totalSize;

    /**
     *
     *  关键字模糊查询
     */
    private String keyword;

    /**
     * 专家团维表层级（专家团：3、模块：4）
     */
    private String cegLevel;



    private List<DmFocCatgCegIctDTO> dmFocCatgCegIctDTOList;

    /**
     * 发货对象编码
     **/
    @ApiModelProperty("top_shipping_object_code")
    private String topShippingObjectCode;

    /**
     * 制造对象编码
     **/
    @ApiModelProperty("top_manufacture_object_code")
    private String topManufactureObjectCode;

    /**
     * 表名称前缀
     */
    private String tablePreFix;

    /**
     * 采购组织
     */
    private String industryOrg;

}
