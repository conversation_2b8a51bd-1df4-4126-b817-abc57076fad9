/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.dao;

import com.huawei.it.fcst.industry.pbi.vo.comb.CommonViewVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstDimInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * IDmCustomTempDao Class
 *
 * <AUTHOR>
 * @since 2024/9/13
 */
public interface IDmCustomTempDao {

    List<DmFcstDimInfoVO> getTempCustomList(CommonViewVO commonViewVO);

    void createTempCombList(@Param("list")List<DmFcstDimInfoVO> customSubList, @Param("costType") String costType);

    void createTempCombByCustomId(CommonViewVO commonViewVO);

    void deleteCustomCombTemp(CommonViewVO commonViewVO);

    List<DmFcstDimInfoVO> getTempTableCustomCombList(CommonViewVO commonViewVO);

    List<String> getTempParentCombList(CommonViewVO commonViewVO);

    void updateCombChageSelectFlagList(@Param("combinationVO")CommonViewVO commonViewVO, @Param("list")List<DmFcstDimInfoVO> changeCustomVOList);

    void deleteCombTempByConnectCode(@Param("combinationVO") CommonViewVO commonViewVO, @Param("currentCustomList")List<DmFcstDimInfoVO> currentCustomList);

    Integer getCountCombTemp(CommonViewVO commonViewVO);
}
