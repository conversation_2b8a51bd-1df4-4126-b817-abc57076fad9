/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.month;

import com.huawei.it.fcst.annotations.ExportAttribute;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 *  The Entity of IndustryIndexExpVO
 *
 * <AUTHOR>
 * @since 2023/03/15
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndustryIndexExpVO implements Serializable {
    private static final long serialVersionUID = -3527986743977395982L;

    /**
     * 年 月
     **/
    @ExportAttribute(sort = 0)
    @ApiModelProperty(value = "会计期年月")
    private Long periodId;

    @ExportAttribute(sort = 1)
    @ApiModelProperty(value = "会计期年月")
    private String costType;

    /**
     * 名称
     **/
    @ExportAttribute(sort = 2)
    @ApiModelProperty(value = "名称")
    private String groupCnName;

    /**
     * 价格指数
     **/
    @ExportAttribute(sort = 3, dataType = "Number")
    @ApiModelProperty(value = "价格指数")
    private Double costIndex;
}