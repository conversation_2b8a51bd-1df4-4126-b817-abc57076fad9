/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.enums;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;
import org.junit.Assert;
import org.junit.Test;

/**
 * ModuleEnumTest Class
 *
 * <AUTHOR>
 * @since 2023/5/12
 */
public class ModuleEnumTest extends BaseVOCoverUtilsTest<ModuleEnum> {

    @Override
    protected Class<ModuleEnum> getTClass() {
        return ModuleEnum.class;
    }

    @Test
    public void testMethod() {
        ModuleEnum.MONTH.setCnName("cn");
        ModuleEnum.MONTH.setEnName("en");
        Assert.assertNotNull(true);
    }
}