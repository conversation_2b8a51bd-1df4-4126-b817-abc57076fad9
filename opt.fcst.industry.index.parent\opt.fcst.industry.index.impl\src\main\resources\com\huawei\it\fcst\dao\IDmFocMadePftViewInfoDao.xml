<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IDmFocMadePftViewInfoDao">
    <resultMap type="com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO" id="resultMap">
        <result property="id" column="id"/>
        <result property="lv0ProdRndTeamCode" column="lv0_prod_rnd_team_code"/>
        <result property="lv0ProdRdTeamCnName" column="lv0_prod_rd_team_cn_name"/>
        <result property="lv1ProdRndTeamCode" column="lv1_prod_rnd_team_code"/>
        <result property="lv1ProdRdTeamCnName" column="lv1_prod_rd_team_cn_name"/>
        <result property="lv2ProdRndTeamCode" column="lv2_prod_rnd_team_code"/>
        <result property="lv2ProdRdTeamCnName" column="lv2_prod_rd_team_cn_name"/>
        <result property="prodRndTeamCode" column="prod_rnd_team_code"/>
        <result property="shippingObjectCode" column="shipping_object_code"/>
        <result property="shippingObjectCnName" column="shipping_object_cn_name"/>
        <result property="manufactureObjectCode" column="manufacture_object_code"/>
        <result property="manufactureObjectCnName" column="manufacture_object_cn_name"/>
        <result property="itemCode" column="item_code"/>
        <result property="itemCnName" column="item_cn_name"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDdate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="parentCode" column="parentCode"/>
        <result property="parentCnName" column="parentCnName"/>
        <result property="groupCode" column="groupCode"/>
        <result property="groupLevel" column="groupLevel"/>
        <result property="groupCnName" column="groupCnName"/>
        <result property="weightRate" column="weight_rate"/>
    </resultMap>
    <sql id="weight_rate" >
        <if test='viewFlag != null and viewFlag == "0"'>
            <choose>
                <when test='groupLevel == "MANUFACTURE_OBJECT"'>
                    DISTINCT item_code AS groupCode, item_cn_name AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "SHIPPING_OBJECT"'>
                    DISTINCT amp.manufacture_object_code AS groupCode, amp.manufacture_object_cn_name AS groupCnName,'MANUFACTURE_OBJECT' AS groupLevel
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT amp.shipping_object_code AS groupCode, amp.shipping_object_cn_name AS groupCnName, 'SHIPPING_OBJECT' AS groupLevel,weight_rate
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "1"'>
            <choose>
                <when test='groupLevel == "MANUFACTURE_OBJECT"'>
                    DISTINCT item_code AS groupCode, item_cn_name AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "SHIPPING_OBJECT"'>
                    DISTINCT amp.manufacture_object_code AS groupCode, amp.manufacture_object_cn_name AS groupCnName,'MANUFACTURE_OBJECT' AS groupLevel
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT amp.shipping_object_code AS groupCode, amp.shipping_object_cn_name AS groupCnName, 'SHIPPING_OBJECT' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "2"'>
            <choose>
                <when test='groupLevel == "MANUFACTURE_OBJECT"'>
                    DISTINCT item_code AS groupCode, item_cn_name AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "SHIPPING_OBJECT"'>
                    DISTINCT amp.manufacture_object_code AS groupCode, amp.manufacture_object_cn_name AS groupCnName,'MANUFACTURE_OBJECT' AS groupLevel
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT amp.shipping_object_code AS groupCode, amp.shipping_object_cn_name AS groupCnName, 'SHIPPING_OBJECT' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                    groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "3"'>
            <choose>
                <when test='groupLevel == "MANUFACTURE_OBJECT"'>
                    DISTINCT item_code AS groupCode, item_cn_name AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "SHIPPING_OBJECT"'>
                    DISTINCT amp.manufacture_object_code AS groupCode, amp.manufacture_object_cn_name AS groupCnName,'MANUFACTURE_OBJECT' AS groupLevel
                </when>
                <when test='groupLevel == "L1"'>
                    DISTINCT amp.shipping_object_code AS groupCode, amp.shipping_object_cn_name AS groupCnName, 'SHIPPING_OBJECT' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT amp.L1_NAME AS groupCode, amp.L1_NAME AS groupCnName, 'L1' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "4"'>
            <choose>
                <when test='groupLevel == "MANUFACTURE_OBJECT"'>
                    DISTINCT item_code AS groupCode, item_cn_name AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "SHIPPING_OBJECT"'>
                    DISTINCT amp.manufacture_object_code AS groupCode, amp.manufacture_object_cn_name AS groupCnName,'MANUFACTURE_OBJECT' AS groupLevel
                </when>
                <when test='groupLevel=="L2"'>
                    DISTINCT amp.shipping_object_code AS groupCode, amp.shipping_object_cn_name AS groupCnName, 'SHIPPING_OBJECT' AS groupLevel,weight_rate
                </when>
                <when test='groupLevel=="L1"'>
                    DISTINCT amp.L2_NAME AS groupCode, amp.L2_NAME AS groupCnName, 'L2' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT amp.L1_NAME AS groupCode, amp.L1_NAME AS groupCnName, 'L1' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
    </sql>
    <sql id="absolute_weight" >
        <if test='viewFlag != null and viewFlag == "0"'>
            <choose>
                <when test='groupLevel == "MANUFACTURE_OBJECT"'>
                    DISTINCT item_code AS groupCode, item_cn_name AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "SHIPPING_OBJECT"'>
                    DISTINCT amp.manufacture_object_code AS groupCode, amp.manufacture_object_cn_name AS groupCnName,'MANUFACTURE_OBJECT' AS groupLevel
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT amp.shipping_object_code AS groupCode, amp.shipping_object_cn_name AS groupCnName, 'SHIPPING_OBJECT' AS groupLevel,absolute_weight
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "1"'>
            <choose>
                <when test='groupLevel == "MANUFACTURE_OBJECT"'>
                    DISTINCT item_code AS groupCode, item_cn_name AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "SHIPPING_OBJECT"'>
                    DISTINCT amp.manufacture_object_code AS groupCode, amp.manufacture_object_cn_name AS groupCnName,'MANUFACTURE_OBJECT' AS groupLevel
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT amp.shipping_object_code AS groupCode, amp.shipping_object_cn_name AS groupCnName, 'SHIPPING_OBJECT' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "2"'>
            <choose>
                <when test='groupLevel == "MANUFACTURE_OBJECT"'>
                    DISTINCT item_code AS groupCode, item_cn_name AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "SHIPPING_OBJECT"'>
                    DISTINCT amp.manufacture_object_code AS groupCode, amp.manufacture_object_cn_name AS groupCnName,'MANUFACTURE_OBJECT' AS groupLevel
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT amp.shipping_object_code AS groupCode, amp.shipping_object_cn_name AS groupCnName, 'SHIPPING_OBJECT' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                    groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "3"'>
            <choose>
                <when test='groupLevel == "MANUFACTURE_OBJECT"'>
                    DISTINCT item_code AS groupCode, item_cn_name AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "SHIPPING_OBJECT"'>
                    DISTINCT amp.manufacture_object_code AS groupCode, amp.manufacture_object_cn_name AS groupCnName,'MANUFACTURE_OBJECT' AS groupLevel
                </when>
                <when test='groupLevel=="L1"'>
                    DISTINCT amp.shipping_object_code AS groupCode, amp.shipping_object_cn_name AS groupCnName, 'SHIPPING_OBJECT' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT amp.L1_NAME  AS groupCode, amp.L1_NAME  AS groupCnName, 'L1' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "4"'>
            <choose>
                <when test='groupLevel == "MANUFACTURE_OBJECT"'>
                    DISTINCT item_code AS groupCode, item_cn_name AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "SHIPPING_OBJECT"'>
                    DISTINCT amp.manufacture_object_code AS groupCode, amp.manufacture_object_cn_name AS groupCnName,'MANUFACTURE_OBJECT' AS groupLevel
                </when>
                <when test='groupLevel=="L2"'>
                    DISTINCT amp.shipping_object_code AS groupCode, amp.shipping_object_cn_name AS groupCnName, 'SHIPPING_OBJECT' AS groupLevel,absolute_weight
                </when>
                <when test='groupLevel=="L1"'>
                    DISTINCT amp.L2_NAME  AS groupCode, amp.L2_NAME  AS groupCnName, 'L2' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT amp.L1_NAME  AS groupCode, amp.L1_NAME  AS groupCnName, 'L1' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
    </sql>
    <select id="madeViewInfoList" resultMap="resultMap">
        SELECT
        <if test='isMultipleSelect == false'>
            <include refid="weight_rate"></include>
        </if>
        <if test='isMultipleSelect == true'>
            <include refid="absolute_weight"></include>
        </if>
        <choose>
            <when test='groupLevel== "MANUFACTURE_OBJECT" || groupLevel=="SHIPPING_OBJECT"'>
                from fin_dm_opt_foi.${tablePreFix}_made_pft_view_info_d amp
            </when>
            <when test='teamLevel == "LV0" and viewFlag != "0"'>
                from fin_dm_opt_foi.${tablePreFix}_made_pft_view_info_d amp
            </when>
            <when test='groupLevel=="L2"'>
                from fin_dm_opt_foi.${tablePreFix}_made_pft_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_made_pft_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.shipping_object_code = weight.group_code
                and amp.l2_name = weight.parent_code and amp.l1_name = weight.l1_name
                and amp.lv2_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>
            <when test='groupLevel=="L1" and viewFlag =="4"'>
                from fin_dm_opt_foi.${tablePreFix}_made_pft_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_made_pft_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.l2_name = weight.profits_name
                and amp.l1_name = weight.parent_code and amp.lv2_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                AND weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>
            <when test='groupLevel=="L1" and viewFlag =="3"'>
                from fin_dm_opt_foi.${tablePreFix}_made_pft_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_made_pft_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.shipping_object_code = weight.group_code
                and amp.l1_name = weight.parent_code and amp.lv2_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV2" and(viewFlag =="3" or viewFlag =="4")'>
                from fin_dm_opt_foi.${tablePreFix}_made_pft_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_made_pft_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.l1_name = weight.profits_name
                and amp.lv2_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV2" and viewFlag =="2"'>
                from fin_dm_opt_foi.${tablePreFix}_made_pft_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_made_pft_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.shipping_object_code = weight.group_code
                and amp.lv2_prod_rnd_team_code =weight.prod_rnd_team_code
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV1" and viewFlag !="1"'>
                from fin_dm_opt_foi.${tablePreFix}_made_pft_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_made_pft_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.lv2_prod_rnd_team_code = weight.group_code
                and amp.lv1_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV1" and viewFlag =="1"'>
                from fin_dm_opt_foi.${tablePreFix}_made_pft_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_made_pft_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.shipping_object_code = weight.group_code
                and amp.lv1_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV0" and viewFlag =="0"'>
                from fin_dm_opt_foi.${tablePreFix}_made_pft_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_made_pft_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.shipping_object_code = weight.group_code
                and amp.lv0_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N'  and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>
        </choose>
        WHERE amp.view_flag = #{viewFlag,jdbcType=VARCHAR} and amp.del_flag ='N'  and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        and amp.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and amp.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        <choose>
            <when test='teamLevel == "LV1" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND amp.lv1_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='teamLevel == "LV2" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND amp.lv2_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <choose>
            <when test='groupLevel == "L1" and groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND amp.l1_name IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='groupLevel == "L2" and groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND amp.l2_name IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='shippingObjectCode != null  and shippingObjectCode!=""'>
            AND amp.shipping_object_code = #{shippingObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='manufactureObjectCode != null  and manufactureObjectCode!=""'>
            AND amp.manufacture_object_code = #{manufactureObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='l1NameList != null and l1NameList.size() > 0'>
            <foreach collection='l1NameList' item="code" open="AND amp.l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='l2NameList != null and l2NameList.size() > 0'>
            <foreach collection='l2NameList' item="code" open="AND amp.l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='nextGroupLevel!=null and nextGroupLevel!=""'>
            AND amp.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
        </if>
        <if test='teamLevel == "LV0" and viewFlag !="0" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND amp.lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamLevel == "LV1" and viewFlag !="1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND amp.lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='isMultipleSelect == false'>
            <include refid="orderby_weight"></include>
        </if>
        <if test='isMultipleSelect == true'>
            <include refid="orderby_absweight"></include>
        </if>
    </select>
    <sql id ="orderby_weight">
        <if test='groupLevel!= "MANUFACTURE_OBJECT" and groupLevel!="SHIPPING_OBJECT" and teamLevel != "LV0"'>
            order by weight_rate desc
        </if>
        <if test='groupLevel!= "MANUFACTURE_OBJECT" and groupLevel!="SHIPPING_OBJECT" and teamLevel == "LV0" and viewFlag == "0"'>
            order by weight_rate desc
        </if>
    </sql>
    <sql id ="orderby_absweight">
        <if test='groupLevel!= "MANUFACTURE_OBJECT" and groupLevel!="SHIPPING_OBJECT" and teamLevel != "LV0"'>
            order by absolute_weight desc
        </if>
        <if test='groupLevel!= "MANUFACTURE_OBJECT" and groupLevel!="SHIPPING_OBJECT" and teamLevel == "LV0" and viewFlag == "0"'>
            order by absolute_weight desc
        </if>
    </sql>
    <sql id="monthWeight_rate">
        <if test='viewFlag != null and viewFlag == "0"'>
            <choose>
                <when test='groupLevel == "MANUFACTURE_OBJECT"'>
                    DISTINCT TOP_ITEM_CODE AS groupCode, TOP_ITEM_CN_NAME AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "SHIPPING_OBJECT"'>
                    DISTINCT amp.TOP_MANUFACTURE_OBJECT_CODE AS groupCode, amp.TOP_MANUFACTURE_OBJECT_CN_NAME AS groupCnName,'MANUFACTURE_OBJECT' AS groupLevel
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT amp.TOP_SHIPPING_OBJECT_CODE AS groupCode, amp.TOP_SHIPPING_OBJECT_CN_NAME AS groupCnName, 'SHIPPING_OBJECT' AS groupLevel,weight.weight_rate
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "1"'>
            <choose>
                <when test='groupLevel == "MANUFACTURE_OBJECT"'>
                    DISTINCT TOP_ITEM_CODE AS groupCode, TOP_ITEM_CN_NAME AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "SHIPPING_OBJECT"'>
                    DISTINCT amp.TOP_MANUFACTURE_OBJECT_CODE AS groupCode, amp.TOP_MANUFACTURE_OBJECT_CN_NAME AS groupCnName,'MANUFACTURE_OBJECT' AS groupLevel
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT amp.TOP_SHIPPING_OBJECT_CODE AS groupCode, amp.TOP_SHIPPING_OBJECT_CN_NAME AS groupCnName, 'SHIPPING_OBJECT' AS groupLevel,weight.weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "2"'>
            <choose>
                <when test='groupLevel == "MANUFACTURE_OBJECT"'>
                    DISTINCT TOP_ITEM_CODE AS groupCode, TOP_ITEM_CN_NAME AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "SHIPPING_OBJECT"'>
                    DISTINCT amp.TOP_MANUFACTURE_OBJECT_CODE AS groupCode, amp.TOP_MANUFACTURE_OBJECT_CN_NAME AS groupCnName,'MANUFACTURE_OBJECT' AS groupLevel
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT amp.TOP_SHIPPING_OBJECT_CODE AS groupCode, amp.TOP_SHIPPING_OBJECT_CN_NAME AS groupCnName, 'SHIPPING_OBJECT' AS groupLevel,weight.weight_rate
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                    groupLevel,weight.weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "3"'>
            <choose>
                <when test='groupLevel == "MANUFACTURE_OBJECT"'>
                    DISTINCT TOP_ITEM_CODE AS groupCode, TOP_ITEM_CN_NAME AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "SHIPPING_OBJECT"'>
                    DISTINCT amp.TOP_MANUFACTURE_OBJECT_CODE AS groupCode, amp.TOP_MANUFACTURE_OBJECT_CN_NAME AS groupCnName,'MANUFACTURE_OBJECT' AS groupLevel
                </when>
                <when test='groupLevel == "L1"'>
                    DISTINCT amp.TOP_SHIPPING_OBJECT_CODE AS groupCode, amp.TOP_SHIPPING_OBJECT_CN_NAME AS groupCnName, 'SHIPPING_OBJECT' AS groupLevel,weight.weight_rate
                </when>
                <when test='groupLevel == "LV2"'>
                    DISTINCT amp.l1_name AS groupCode, amp.l1_name AS groupCnName, 'L1' AS groupLevel,weight.weight_rate
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,weight.weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "4"'>
            <choose>
                <when test='groupLevel == "MANUFACTURE_OBJECT"'>
                    DISTINCT TOP_ITEM_CODE AS groupCode, TOP_ITEM_CN_NAME AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "SHIPPING_OBJECT"'>
                    DISTINCT amp.TOP_MANUFACTURE_OBJECT_CODE AS groupCode, amp.TOP_MANUFACTURE_OBJECT_CN_NAME AS groupCnName,'MANUFACTURE_OBJECT' AS groupLevel
                </when>
                <when test='groupLevel == "L2"'>
                    DISTINCT amp.TOP_SHIPPING_OBJECT_CODE AS groupCode, amp.TOP_SHIPPING_OBJECT_CN_NAME AS groupCnName, 'SHIPPING_OBJECT' AS groupLevel,weight.weight_rate
                </when>
                <when test='groupLevel == "L1"'>
                    DISTINCT amp.l2_name AS groupCode, amp.l2_name  AS groupCnName, 'L2' AS groupLevel,weight.weight_rate
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT amp.l1_name  AS groupCode, amp.l1_name  AS groupCnName, 'L1' AS groupLevel,weight.weight_rate
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,weight.weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
    </sql>
    <sql id="monthAbsolute_rate">
        <if test='viewFlag != null and viewFlag == "0"'>
            <choose>
                <when test='groupLevel == "MANUFACTURE_OBJECT"'>
                    DISTINCT TOP_ITEM_CODE AS groupCode, TOP_ITEM_CN_NAME AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "SHIPPING_OBJECT"'>
                    DISTINCT amp.TOP_MANUFACTURE_OBJECT_CODE AS groupCode, amp.TOP_MANUFACTURE_OBJECT_CN_NAME AS groupCnName,'MANUFACTURE_OBJECT' AS groupLevel
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT amp.TOP_SHIPPING_OBJECT_CODE AS groupCode, amp.TOP_SHIPPING_OBJECT_CN_NAME AS groupCnName, 'SHIPPING_OBJECT' AS groupLevel,weight.absolute_weight
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "1"'>
            <choose>
                <when test='groupLevel == "MANUFACTURE_OBJECT"'>
                    DISTINCT TOP_ITEM_CODE AS groupCode, TOP_ITEM_CN_NAME AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "SHIPPING_OBJECT"'>
                    DISTINCT amp.TOP_MANUFACTURE_OBJECT_CODE AS groupCode, amp.TOP_MANUFACTURE_OBJECT_CN_NAME AS groupCnName,'MANUFACTURE_OBJECT' AS groupLevel
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT amp.TOP_SHIPPING_OBJECT_CODE AS groupCode, amp.TOP_SHIPPING_OBJECT_CN_NAME AS groupCnName, 'SHIPPING_OBJECT' AS groupLevel,weight.absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "2"'>
            <choose>
                <when test='groupLevel == "MANUFACTURE_OBJECT"'>
                    DISTINCT TOP_ITEM_CODE AS groupCode, TOP_ITEM_CN_NAME AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "SHIPPING_OBJECT"'>
                    DISTINCT amp.TOP_MANUFACTURE_OBJECT_CODE AS groupCode, amp.TOP_MANUFACTURE_OBJECT_CN_NAME AS groupCnName,'MANUFACTURE_OBJECT' AS groupLevel
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT amp.TOP_SHIPPING_OBJECT_CODE AS groupCode, amp.TOP_SHIPPING_OBJECT_CN_NAME AS groupCnName, 'SHIPPING_OBJECT' AS groupLevel,weight.absolute_weight
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                    groupLevel,weight.absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "3"'>
            <choose>
                <when test='groupLevel == "MANUFACTURE_OBJECT"'>
                    DISTINCT TOP_ITEM_CODE AS groupCode, TOP_ITEM_CN_NAME AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "SHIPPING_OBJECT"'>
                    DISTINCT amp.TOP_MANUFACTURE_OBJECT_CODE AS groupCode, amp.TOP_MANUFACTURE_OBJECT_CN_NAME AS groupCnName,'MANUFACTURE_OBJECT' AS groupLevel
                </when>
                <when test='groupLevel == "L1"'>
                    DISTINCT amp.TOP_SHIPPING_OBJECT_CODE AS groupCode, amp.TOP_SHIPPING_OBJECT_CN_NAME AS groupCnName, 'SHIPPING_OBJECT' AS groupLevel,weight.absolute_weight
                </when>
                <when test='groupLevel == "LV2"'>
                    DISTINCT amp.l1_name  AS groupCode, amp.l1_name AS groupCnName, 'L1' AS groupLevel,weight.absolute_weight
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,weight.absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "4"'>
            <choose>
                <when test='groupLevel == "MANUFACTURE_OBJECT"'>
                    DISTINCT TOP_ITEM_CODE AS groupCode, TOP_ITEM_CN_NAME AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "SHIPPING_OBJECT"'>
                    DISTINCT amp.TOP_MANUFACTURE_OBJECT_CODE AS groupCode, amp.TOP_MANUFACTURE_OBJECT_CN_NAME AS groupCnName,'MANUFACTURE_OBJECT' AS groupLevel
                </when>
                <when test='groupLevel == "L2"'>
                    DISTINCT amp.TOP_SHIPPING_OBJECT_CODE AS groupCode, amp.TOP_SHIPPING_OBJECT_CN_NAME AS groupCnName, 'SHIPPING_OBJECT' AS groupLevel,weight.absolute_weight
                </when>
                <when test='groupLevel == "L1"'>
                    DISTINCT amp.l2_name AS groupCode, amp.l2_name  AS groupCnName, 'L2' AS groupLevel,weight.absolute_weight
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT amp.l1_name AS groupCode, amp.l1_name  AS groupCnName, 'L1' AS groupLevel,weight.absolute_weight
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,weight.absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
    </sql>
    <select id="madeViewInfoListForMonth" resultMap="resultMap">
        SELECT
        <if test='isMultipleSelect == false'>
            <include refid="monthWeight_rate"></include>
        </if>
        <if test='isMultipleSelect == true'>
            <include refid="monthAbsolute_rate"></include>
        </if>
        <choose>
            <when test='groupLevel== "MANUFACTURE_OBJECT" || groupLevel=="SHIPPING_OBJECT"'>
                from fin_dm_opt_foi.${tablePreFix}_made_pft_top_item_info_t amp
            </when>
            <when test='teamLevel == "LV0" and viewFlag != "0"'>
                from fin_dm_opt_foi.${tablePreFix}_made_pft_top_item_info_t amp
            </when>
            <when test='groupLevel=="L2"'>
                from fin_dm_opt_foi.${tablePreFix}_made_pft_top_item_info_t amp left join fin_dm_opt_foi.${tablePreFix}_made_pft_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.top_shipping_object_code = weight.group_code
                and amp.l2_name = weight.parent_code and amp.l1_name = weight.l1_name
                and amp.lv2_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.group_level = 'SHIPPING_OBJECT'
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>
            <when test='groupLevel=="L1" and viewFlag =="4"'>
                from fin_dm_opt_foi.${tablePreFix}_made_pft_top_item_info_t amp left join fin_dm_opt_foi.${tablePreFix}_made_pft_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.l2_name = weight.profits_name
                and amp.l1_name = weight.parent_code
                and amp.lv2_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.group_level = 'L2'
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>
            <when test='groupLevel=="L1" and viewFlag =="3"'>
                from fin_dm_opt_foi.${tablePreFix}_made_pft_top_item_info_t amp left join fin_dm_opt_foi.${tablePreFix}_made_pft_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.top_shipping_object_code = weight.group_code
                and amp.l1_name = weight.parent_code and amp.lv2_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV2" and (viewFlag =="3" or viewFlag =="4")'>
                from fin_dm_opt_foi.${tablePreFix}_made_pft_top_item_info_t amp left join fin_dm_opt_foi.${tablePreFix}_made_pft_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.l1_name = weight.profits_name
                and amp.lv2_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV2" and viewFlag =="2"'>
                from fin_dm_opt_foi.${tablePreFix}_made_pft_top_item_info_t amp left join fin_dm_opt_foi.${tablePreFix}_made_pft_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.top_shipping_object_code = weight.group_code
                and amp.lv2_prod_rnd_team_code =weight.prod_rnd_team_code
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV1" and viewFlag !="1"'>
                from fin_dm_opt_foi.${tablePreFix}_made_pft_top_item_info_t amp left join fin_dm_opt_foi.${tablePreFix}_made_pft_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.lv2_prod_rnd_team_code = weight.group_code
                and amp.lv1_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV1" and viewFlag =="1"'>
                from fin_dm_opt_foi.${tablePreFix}_made_pft_top_item_info_t amp left join fin_dm_opt_foi.${tablePreFix}_made_pft_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.top_shipping_object_code = weight.group_code
                and amp.lv1_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV0" and viewFlag =="0"'>
                from fin_dm_opt_foi.${tablePreFix}_made_pft_top_item_info_t amp left join fin_dm_opt_foi.${tablePreFix}_made_pft_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.top_shipping_object_code = weight.group_code
                and amp.lv0_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>

        </choose>
        where amp.del_flag = 'N'
        AND amp.is_top_flag ='Y'
        AND amp.double_flag ='Y'
        AND amp.version_id = #{monthVersionId,jdbcType=NUMERIC}
        AND amp.view_flag = #{viewFlag,jdbcType=VARCHAR}
        AND amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        AND amp.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        AND amp.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        <choose>
            <when test='teamLevel == "LV1" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND amp.lv1_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='teamLevel == "LV2" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND amp.lv2_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <choose>
            <when test='groupLevel == "L1" and groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND amp.l1_name IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='groupLevel == "L2" and groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND amp.l2_name IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='shippingObjectCode != null  and shippingObjectCode!=""'>
            AND amp.top_shipping_object_code = #{shippingObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='manufactureObjectCode != null  and manufactureObjectCode!=""'>
            AND amp.top_manufacture_object_code = #{manufactureObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='l1NameList != null and l1NameList.size() > 0'>
            <foreach collection='l1NameList' item="code" open="AND amp.l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='l2NameList != null and l2NameList.size() > 0'>
            <foreach collection='l2NameList' item="code" open="AND amp.l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamLevel == "LV0" and viewFlag !="0" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND amp.lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamLevel == "LV1" and viewFlag !="1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND amp.lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='isMultipleSelect == false'>
            <include refid="month_orderby_weight"></include>
        </if>
        <if test='isMultipleSelect == true'>
            <include refid="month_orderby_absweight"></include>
        </if>
    </select>

    <select id="madeObjectViewInfoList" resultMap="resultMap">
        SELECT
        <if test='isMultipleSelect == false'>
            <include refid="monthWeight_rate"></include>
        </if>
        <if test='isMultipleSelect == true'>
            <include refid="monthAbsolute_rate"></include>
        </if>
        <choose>
            <when test='groupLevel== "MANUFACTURE_OBJECT" || groupLevel=="SHIPPING_OBJECT"'>
                from fin_dm_opt_foi.${tablePreFix}_pft_top_made_info_t amp
            </when>
            <when test='teamLevel == "LV0" and viewFlag != "0"'>
                from fin_dm_opt_foi.${tablePreFix}_pft_top_made_info_t amp
            </when>
            <when test='groupLevel=="L2"'>
                from fin_dm_opt_foi.${tablePreFix}_pft_top_made_info_t amp left join fin_dm_opt_foi.${tablePreFix}_made_pft_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.top_shipping_object_code = weight.group_code
                and amp.l2_name = weight.parent_code and amp.l1_name = weight.l1_name
                and amp.lv2_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' AND weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.group_level = 'SHIPPING_OBJECT'
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>
            <when test='groupLevel=="L1" and viewFlag =="4"'>
                from fin_dm_opt_foi.${tablePreFix}_pft_top_made_info_t amp left join fin_dm_opt_foi.${tablePreFix}_made_pft_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.l2_name = weight.profits_name
                and amp.l1_name = weight.parent_code
                and amp.lv2_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' AND weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.group_level = 'L2'
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>
            <when test='groupLevel=="L1" and viewFlag =="3"'>
                from fin_dm_opt_foi.${tablePreFix}_pft_top_made_info_t amp left join fin_dm_opt_foi.${tablePreFix}_made_pft_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.top_shipping_object_code = weight.group_code
                and amp.l1_name = weight.parent_code and amp.lv2_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' AND weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV2" and (viewFlag =="3" or viewFlag =="4")'>
                from fin_dm_opt_foi.${tablePreFix}_pft_top_made_info_t amp left join fin_dm_opt_foi.${tablePreFix}_made_pft_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.l1_name = weight.profits_name
                and amp.lv2_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' AND weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV2" and viewFlag =="2"'>
                from fin_dm_opt_foi.${tablePreFix}_pft_top_made_info_t amp left join fin_dm_opt_foi.${tablePreFix}_made_pft_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.top_shipping_object_code = weight.group_code
                and amp.lv2_prod_rnd_team_code =weight.prod_rnd_team_code
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV1" and viewFlag !="1"'>
                from fin_dm_opt_foi.${tablePreFix}_pft_top_made_info_t amp left join fin_dm_opt_foi.${tablePreFix}_made_pft_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.lv2_prod_rnd_team_code = weight.group_code
                and amp.lv1_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV1" and viewFlag =="1"'>
                from fin_dm_opt_foi.${tablePreFix}_pft_top_made_info_t amp left join fin_dm_opt_foi.${tablePreFix}_made_pft_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.top_shipping_object_code = weight.group_code
                and amp.lv1_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV0" and viewFlag =="0"'>
                from fin_dm_opt_foi.${tablePreFix}_pft_top_made_info_t amp left join fin_dm_opt_foi.${tablePreFix}_made_pft_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.top_shipping_object_code = weight.group_code
                and amp.lv0_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
            </when>

        </choose>
        where amp.del_flag = 'N'
        AND amp.is_top_flag ='Y'
        AND amp.double_flag ='Y'
        AND amp.version_id = #{monthVersionId,jdbcType=NUMERIC}
        AND amp.view_flag = #{viewFlag,jdbcType=VARCHAR}
        AND amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        AND amp.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        AND amp.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        <choose>
            <when test='teamLevel == "LV1" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND amp.lv1_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='teamLevel == "LV2" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND amp.lv2_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <choose>
            <when test='groupLevel == "L1" and groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND amp.l1_name IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='groupLevel == "L2" and groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND amp.l2_name IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='shippingObjectCode != null  and shippingObjectCode!=""'>
            AND amp.top_shipping_object_code = #{shippingObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='manufactureObjectCode != null  and manufactureObjectCode!=""'>
            AND amp.top_manufacture_object_code = #{manufactureObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='l1NameList != null and l1NameList.size() > 0'>
            <foreach collection='l1NameList' item="code" open="AND amp.l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='l2NameList != null and l2NameList.size() > 0'>
            <foreach collection='l2NameList' item="code" open="AND amp.l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamLevel == "LV0" and viewFlag !="0" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND amp.lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamLevel == "LV1" and viewFlag !="1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND amp.lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='isMultipleSelect == false'>
            <include refid="month_orderby_weight"></include>
        </if>
        <if test='isMultipleSelect == true'>
            <include refid="month_orderby_absweight"></include>
        </if>
    </select>

    <sql id="month_orderby_weight">
        <if test='groupLevel!= "MANUFACTURE_OBJECT" and groupLevel!="SHIPPING_OBJECT" and teamLevel != "LV0"'>
            order by weight.weight_rate desc
        </if>
        <if test='groupLevel!= "MANUFACTURE_OBJECT" and groupLevel!="SHIPPING_OBJECT" and teamLevel == "LV0" and viewFlag == "0"'>
            order by weight.weight_rate desc
        </if>
    </sql>

    <sql id="month_orderby_absweight">
        <if test='groupLevel!= "MANUFACTURE_OBJECT" and groupLevel!="SHIPPING_OBJECT" and teamLevel != "LV0"'>
            order by weight.absolute_weight desc
        </if>
        <if test='groupLevel!= "MANUFACTURE_OBJECT" and groupLevel!="SHIPPING_OBJECT" and teamLevel == "LV0" and viewFlag == "0"'>
            order by weight.absolute_weight desc
        </if>
    </sql>

    <select id="madeViewInfoKeyWordForMonth" resultMap="resultMap">
        SELECT
        <if test='groupLevel == "MANUFACTURE_OBJECT"'>
            DISTINCT  top_manufacture_object_code as manufacture_object_code,top_manufacture_object_cn_name as manufacture_object_cn_name,
            top_shipping_object_code as shipping_object_code ,top_shipping_object_cn_name as shipping_object_cn_name
        </if>
        from fin_dm_opt_foi.${tablePreFix}_made_pft_top_item_info_t
        where del_flag = 'N'
        AND is_top_flag ='Y'
        AND double_flag ='Y'
        AND version_id = #{monthVersionId}
        AND view_flag = #{viewFlag,jdbcType=VARCHAR}
        AND caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        AND oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        AND lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        <choose>
            <when test='teamLevel == "LV1" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='teamLevel == "LV2" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test="keyWord != null and keyWord != ''">
            AND (UPPER(top_manufacture_object_code) LIKE CONCAT(CONCAT('%', UPPER(#{keyWord})) ,'%')
            OR UPPER(top_manufacture_object_cn_name) LIKE CONCAT(CONCAT('%', UPPER(#{keyWord})) ,'%')
            )
        </if>
    </select>

    <select id="madeObjectViewInfoKeyWord" resultMap="resultMap">
        SELECT
        <if test='groupLevel == "MANUFACTURE_OBJECT"'>
            DISTINCT  top_manufacture_object_code as manufacture_object_code,top_manufacture_object_cn_name as manufacture_object_cn_name,
            top_shipping_object_code as shipping_object_code ,top_shipping_object_cn_name as shipping_object_cn_name
        </if>
        from fin_dm_opt_foi.${tablePreFix}_pft_top_made_info_t
        where del_flag = 'N'
        AND is_top_flag ='Y'
        AND double_flag ='Y'
        AND version_id = #{monthVersionId}
        AND view_flag = #{viewFlag,jdbcType=VARCHAR}
        AND caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        AND oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        AND lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        <choose>
            <when test='teamLevel == "LV1" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='teamLevel == "LV2" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test="keyWord != null and keyWord != ''">
            AND (UPPER(top_manufacture_object_code) LIKE CONCAT(CONCAT('%', UPPER(#{keyWord})) ,'%')
            OR UPPER(top_manufacture_object_cn_name) LIKE CONCAT(CONCAT('%', UPPER(#{keyWord})) ,'%')
            )
        </if>
    </select>
    <select id="madeViewInfoKeyWordList" resultMap="resultMap">
        SELECT
        <if test='groupLevel == "MANUFACTURE_OBJECT"'>
            DISTINCT shipping_object_code,shipping_object_cn_name,manufacture_object_code,manufacture_object_cn_name
        </if>
        from fin_dm_opt_foi.${tablePreFix}_made_pft_view_info_d
        WHERE view_flag = #{viewFlag} and del_flag ='N'
        AND caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        AND oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        AND lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        <choose>
            <when test='teamLevel == "LV1" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='teamLevel == "LV2" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='groupLevel!=null and groupLevel!=""'>
            AND group_level = #{groupLevel}
        </if>
        <if test="keyWord != null and keyWord != ''">
            AND (UPPER(manufacture_object_code) LIKE CONCAT(CONCAT('%', UPPER(#{keyWord})) ,'%')
            OR UPPER(manufacture_object_cn_name) LIKE CONCAT(CONCAT('%', UPPER(#{keyWord})) ,'%')
            )
        </if>
    </select>

    <select id="madeViewFlagInfoProfitsList" resultMap="resultMap">
        SELECT *
        from (
        SELECT DISTINCT
        view_flag, lv0_prod_rnd_team_code, lv0_prod_rd_team_cn_name,'P' as granularityType
        FROM
        fin_dm_opt_foi.${tablePreFix}_made_pft_view_info_d
        where del_flag = 'N' and caliber_flag = #{caliberFlag}
        )
        ORDER BY view_flag
    </select>
</mapper>