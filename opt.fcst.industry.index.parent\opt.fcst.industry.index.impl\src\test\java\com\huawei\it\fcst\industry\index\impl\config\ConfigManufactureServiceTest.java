/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.config;

import com.huawei.it.fcst.industry.index.dao.IDmFocDataRefreshStatusDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTopCateInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTopItemInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocVersionDao;
import com.huawei.it.fcst.industry.index.impl.annual.StatisticsExcelService;
import com.huawei.it.fcst.industry.index.impl.combination.AsyncService;
import com.huawei.it.fcst.industry.index.impl.common.CommonService;
import com.huawei.it.fcst.industry.index.utils.ExcelUtil;
import com.huawei.it.fcst.industry.index.utils.UserInfoUtils;
import com.huawei.it.fcst.industry.index.vo.combination.DmFocDataRefreshStatus;
import com.huawei.it.fcst.industry.index.vo.common.DmFoiImpExpRecordVO;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocTopCateInfoDTO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocTopItemInfoDTO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoDTO;
import com.huawei.it.fcst.industry.index.vo.config.HistoryInputVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.security.UserVO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.inject.Inject;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.powermock.api.mockito.PowerMockito.mockStatic;

/**
 * ConfigManufactureServiceTest Class
 *
 * <AUTHOR>
 * @since 2023/10/26
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest( {UserInfoUtils.class})
public class ConfigManufactureServiceTest {
    private static final Logger LOGGER = LoggerFactory.getLogger(ConfigManufactureService.class);

    @InjectMocks
    private ConfigManufactureService configManufactureService;

    @Mock
    private IDmFocVersionDao dmFocVersionDao;

    @Mock
    private IDmFocTopCateInfoDao dmFocTopCateInfoDao;

    @Mock
    private IDmFocTopItemInfoDao dmFocTopItemInfoDao;

    @Mock
    private ExcelUtil excelUtil;

    @Mock
    private IDmFocDataRefreshStatusDao dataRefreshStatusDao;

    @Mock
    private AsyncService asyncService;

    @Mock
    private ConfigExportService configExportService;

    @Before
    public void before() throws Exception {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);
    }

    @Test
    public void getManufactureList() {
        HistoryInputVO historyInputVO=new HistoryInputVO();
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configManufactureService.getManufactureList(historyInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void getManufactureList2Test() {
        HistoryInputVO historyInputVO=new HistoryInputVO();
        historyInputVO.setVersionId(15L);
        DmFocVersionInfoDTO versionVO=new DmFocVersionInfoDTO();
        versionVO.setVersionId(15L);
        ResultDataVO resultDataVO = new ResultDataVO();

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionVO);
        try {
            resultDataVO = configManufactureService.getManufactureList(historyInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void getManufactureList3Test() {
        HistoryInputVO historyInputVO=new HistoryInputVO();
        historyInputVO.setVersionId(15L);
        historyInputVO.setPageIndex(1);
        historyInputVO.setPageSize(10);
        historyInputVO.setOverseaFlag("O");
        ResultDataVO resultDataVO = new ResultDataVO();

        DmFocVersionInfoDTO versionVO=new DmFocVersionInfoDTO();
        versionVO.setVersionId(15L);
        versionVO.setVersion("202306-001");

        PagedResult<DmFocTopCateInfoDTO> cateByPage=new PagedResult<>();
        List<DmFocTopCateInfoDTO> list = new ArrayList<>();
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(15);
        DmFocTopCateInfoDTO cateInfoDTO = new DmFocTopCateInfoDTO();
        cateInfoDTO.setViewFlag("3");
        list.add(cateInfoDTO);
        cateByPage.setPageVO(pageVO);
        cateByPage.setResult(list);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionVO);
        when(dmFocTopCateInfoDao.findManufactureByPage(any(),any())).thenReturn(cateByPage);
        try {
            resultDataVO = configManufactureService.getManufactureList(historyInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void getItemList() {
        HistoryInputVO historyInputVO=new HistoryInputVO();
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configManufactureService.getItemList(historyInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void getItemList2Test() {
        HistoryInputVO historyInputVO=new HistoryInputVO();
        historyInputVO.setVersionId(15L);
        DmFocVersionInfoDTO versionVO=new DmFocVersionInfoDTO();
        versionVO.setVersionId(15L);
        ResultDataVO resultDataVO = new ResultDataVO();
        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionVO);
        try {
            resultDataVO = configManufactureService.getItemList(historyInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void getItemList3Test() {
        HistoryInputVO historyInputVO=new HistoryInputVO();
        historyInputVO.setVersionId(15L);
        historyInputVO.setPageIndex(1);
        historyInputVO.setPageSize(10);
        historyInputVO.setOverseaFlag("O");
        ResultDataVO resultDataVO = new ResultDataVO();

        DmFocVersionInfoDTO versionVO=new DmFocVersionInfoDTO();
        versionVO.setVersionId(15L);
        versionVO.setVersion("202306-001");

        PagedResult<DmFocTopItemInfoDTO> cateByPage=new PagedResult<>();
        List<DmFocTopItemInfoDTO> list = new ArrayList<>();
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(15);
        DmFocTopItemInfoDTO cateInfoDTO = new DmFocTopItemInfoDTO();
        cateInfoDTO.setViewFlag("3");
        list.add(cateInfoDTO);
        cateByPage.setPageVO(pageVO);
        cateByPage.setResult(list);

        when(configExportService.setTopItemVO(any(),any())).thenReturn(cateInfoDTO);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionVO);
        when(dmFocTopItemInfoDao.findMfcItemByPage(any(),any())).thenReturn(cateByPage);
        try {
            resultDataVO = configManufactureService.getItemList(historyInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void exportManufacture() {
        HistoryInputVO historyInputVO=new HistoryInputVO();
        HttpServletResponse response=null;
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = configManufactureService.exportManufacture(historyInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void exportManufacture2Test() throws Exception {
        HistoryInputVO historyInputVO=new HistoryInputVO();
        historyInputVO.setVersionId(66L);
        historyInputVO.setViewFlag("3");
        historyInputVO.setGranularityType("U");
        historyInputVO.setCostType("制造成本");
        historyInputVO.setCaliberFlag("C");
        historyInputVO.setOverseaFlag("O");
        historyInputVO.setModelType("ITEM");
        HttpServletResponse response=null;
        ResultDataVO resultDataVO = new ResultDataVO();

        DmFocVersionInfoDTO versionDTO=new DmFocVersionInfoDTO();
        versionDTO.setVersion("202309-001");
        versionDTO.setVersionId(66L);

        List<Map> tableHeaderList = new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("is_top_flag", "Y");
        map.put("weight0", "33.6");
        map.put("weight1", "12.2");
        map.put("lv0_prod_list_cn_name", "BG");

        DmFocDataRefreshStatus dataRefreshStatus = new DmFocDataRefreshStatus();
        dataRefreshStatus.setStatus("TASK_INIT");
        dataRefreshStatus.setDelFlag("N");
        Long userId = UserInfoUtils.getUserId();
        dataRefreshStatus.setCreatedBy(userId);
        dataRefreshStatus.setLastUpdatedBy(userId);
        dataRefreshStatus.setCreationDate(new Date());
        dataRefreshStatus.setTaskFlag("CONFIG_" + historyInputVO.getIndustryOrg() + "_" + historyInputVO.getCostType() + "_EXPORT_" + historyInputVO.getCaliberFlag() + "_" + historyInputVO.getModelType());
        dataRefreshStatus.setTaskId(1L);
        int roleId =111;
        mockStatic(UserInfoUtils.class);
        PowerMockito.when(UserInfoUtils.class, "getRoleId").thenReturn(roleId);
        dataRefreshStatus.setRoleId(roleId);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionDTO);
        when(dmFocTopItemInfoDao.findMfcItemAllCount(any())).thenReturn(500);
        try {
            resultDataVO = configManufactureService.exportManufacture(historyInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void exportManufacture3Test() throws Exception {
        HistoryInputVO historyInputVO=new HistoryInputVO();
        historyInputVO.setVersionId(66L);
        historyInputVO.setViewFlag("3");
        historyInputVO.setGranularityType("U");
        historyInputVO.setCostType("制造成本");
        historyInputVO.setCaliberFlag("C");
        historyInputVO.setOverseaFlag("O");
        HttpServletResponse response=null;
        ResultDataVO resultDataVO = new ResultDataVO();

        DmFocVersionInfoDTO versionDTO=new DmFocVersionInfoDTO();
        versionDTO.setVersionId(66L);
        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionDTO);

        DmFocDataRefreshStatus dataRefreshStatus = new DmFocDataRefreshStatus();
        dataRefreshStatus.setStatus("TASK_INIT");
        dataRefreshStatus.setDelFlag("N");
        Long userId = UserInfoUtils.getUserId();
        dataRefreshStatus.setCreatedBy(userId);
        dataRefreshStatus.setLastUpdatedBy(userId);
        dataRefreshStatus.setCreationDate(new Date());
        dataRefreshStatus.setTaskFlag("CONFIG_" + historyInputVO.getIndustryOrg() + "_" + historyInputVO.getCostType() + "_EXPORT_" + historyInputVO.getCaliberFlag() + "_" + historyInputVO.getModelType());
        dataRefreshStatus.setTaskId(1L);
        int roleId =111;
        mockStatic(UserInfoUtils.class);
        PowerMockito.when(UserInfoUtils.class, "getRoleId").thenReturn(roleId);
        dataRefreshStatus.setRoleId(roleId);
        try {
            resultDataVO = configManufactureService.exportManufacture(historyInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void exportManufacture4Test() throws Exception {
        HistoryInputVO historyInputVO=new HistoryInputVO();
        historyInputVO.setVersionId(66L);
        historyInputVO.setViewFlag("3");
        historyInputVO.setGranularityType("U");
        historyInputVO.setCostType("制造成本");
        historyInputVO.setCaliberFlag("C");
        historyInputVO.setOverseaFlag("O");
        historyInputVO.setModelType("CATEGORY");
        HttpServletResponse response=null;
        ResultDataVO resultDataVO = new ResultDataVO();

        DmFocVersionInfoDTO versionDTO=new DmFocVersionInfoDTO();
        versionDTO.setVersion("202309-001");
        versionDTO.setVersionId(66L);

        List<Map> tableHeaderList = new ArrayList<>();
        Map<String,String> map = new HashMap<>();
        map.put("is_top_flag", "Y");
        map.put("weight0", "33.6");
        map.put("weight1", "12.2");
        map.put("lv0_prod_list_cn_name", "BG");
        tableHeaderList.add(map);

        DmFoiImpExpRecordVO expRecordVO=new DmFoiImpExpRecordVO();
        expRecordVO.setPeriodId("202306");

        DmFocDataRefreshStatus dataRefreshStatus = new DmFocDataRefreshStatus();
        dataRefreshStatus.setStatus("TASK_INIT");
        dataRefreshStatus.setDelFlag("N");
        Long userId = UserInfoUtils.getUserId();
        dataRefreshStatus.setCreatedBy(userId);
        dataRefreshStatus.setLastUpdatedBy(userId);
        dataRefreshStatus.setCreationDate(new Date());
        dataRefreshStatus.setTaskFlag("CONFIG_" + historyInputVO.getIndustryOrg() + "_" + historyInputVO.getCostType() + "_EXPORT_" + historyInputVO.getCaliberFlag() + "_" + historyInputVO.getModelType());
        dataRefreshStatus.setTaskId(1L);
        int roleId =111;
        mockStatic(UserInfoUtils.class);
        PowerMockito.when(UserInfoUtils.class, "getRoleId").thenReturn(roleId);
        dataRefreshStatus.setRoleId(roleId);

        when(dmFocVersionDao.findDmFocVersionDTOById(any(),any())).thenReturn(versionDTO);
        when(dmFocTopCateInfoDao.findManufactureAllCount(any())).thenReturn(500);
        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);
        try {
            resultDataVO = configManufactureService.exportManufacture(historyInputVO);
        } catch (CommonApplicationException e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }
}