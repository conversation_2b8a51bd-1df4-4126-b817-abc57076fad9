/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.impl.common;

import com.huawei.it.fcst.constant.CommonConstant;
import com.huawei.it.fcst.enums.CommonConstEnum;
import com.huawei.it.fcst.industry.price.dao.IDmFcstPriceBaseCusDimDao;
import com.huawei.it.fcst.industry.price.dao.IDmFcstPriceDimInfoDao;
import com.huawei.it.fcst.industry.price.enums.GroupLevelEnum;
import com.huawei.it.fcst.industry.price.impl.annual.AnnualAmpPriceService;
import com.huawei.it.fcst.industry.price.service.common.IDropDownCommonService;
import com.huawei.it.fcst.industry.price.utils.FcstIndustryUtil;
import com.huawei.it.fcst.industry.price.vo.common.CommonPriceBaseVO;
import com.huawei.it.fcst.industry.price.vo.common.DmFcstPriceDimInfoVO;
import com.huawei.it.fcst.industry.price.vo.common.PriceDataPermissionsVO;
import com.huawei.it.fcst.industry.price.vo.drop.BasePriceCusDimVO;
import com.huawei.it.fcst.industry.price.vo.drop.DmFcstBasePriceCusDimVO;
import com.huawei.it.fcst.util.ObjectCopyUtil;
import com.huawei.it.fcst.util.UserInfoUtils;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Inject;
import javax.inject.Named;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/6/28
 */
@Slf4j
@Named("dropDownCommonService")
@JalorResource(code = "dropDownCommonService", desc = "定价指数-各层级下拉框")
public class DropDownCommonService implements IDropDownCommonService {

    @Autowired
    private IDmFcstPriceDimInfoDao dmFcstPriceDimInfoDao;

    @Autowired
    private IDmFcstPriceBaseCusDimDao dmFcstBaseCusDao;

    @Autowired
    private PriceCommonService commonService;

    @Autowired
    private PriceDataPermissionService priceDataPermissionService;

    @Inject
    private AnnualAmpPriceService annualAmpPriceService;

    @JalorOperation(code = "getBgInfoList", desc = "bg下拉框")
    @Override
    public ResultDataVO getBgInfoList(CommonPriceBaseVO commonBaseVO) {
        commonBaseVO.setVersionId(commonService.getVersionId("ANNUAL"));
        List<DmFcstPriceDimInfoVO> bgList = dmFcstPriceDimInfoDao.getBgList(commonBaseVO);
        // 获取BG数据权限
        PriceDataPermissionsVO currentRoleDataPermission = priceDataPermissionService.getCurrentRoleDataPermission();
        commonBaseVO.setLv2DimensionSet(currentRoleDataPermission.getLv2DimensionSet());
        Set<String> bgDimensionSet = currentRoleDataPermission.getLv1DimensionSet();
        Map<String, Map<String, Set<String>>> locationMap = currentRoleDataPermission.getLocationMap();
        Map<String, Set<String>> regionSetMap = locationMap.get(commonBaseVO.getOverseaFlag());
        Set<String> bgSet = new HashSet<>();
        if (null != regionSetMap) {
            bgSet = regionSetMap.keySet();
        }
        if (bgDimensionSet.contains("NO_PERMISSION") && bgDimensionSet.size() == 1) {
            List<DmFcstPriceDimInfoVO> bgCodePermissionList = dmFcstPriceDimInfoDao.getBgCodePermissionList(commonBaseVO);
            Set<String> bgCodeSetList = bgCodePermissionList.stream().map(DmFcstPriceDimInfoVO::getBgCode).collect(Collectors.toSet());
            bgList = bgList.stream().filter(ele->bgCodeSetList.contains(ele.getBgCode())).collect(Collectors.toList());
        } else if (bgDimensionSet.size() == 0) {
            return ResultDataVO.success(bgList);
        } else {
            bgList = bgList.stream().filter(ele->bgDimensionSet.contains(ele.getBgCode())).collect(Collectors.toList());
        }
        // 定价区域分析师需要控制权限
        if (com.huawei.it.fcst.industry.price.constant.CommonConstant.REGION_ANALYST_PRICE.equals(UserInfoUtils.getRoleName()) && CollectionUtils.isNotEmpty(bgSet)){
            Set<String> bgCodeSet = new HashSet<>();
            bgCodeSet.addAll(bgSet);
            bgList = bgList.stream().filter(ele -> bgCodeSet.contains(ele.getBgCode())).collect(Collectors.toList());
        }
        // 排序
        Collections.sort(bgList, Comparator.comparingInt(o -> CommonConstant.BG_CODE_RULE.getOrDefault(o.getBgCode(), Integer.MAX_VALUE)));
        return ResultDataVO.success(bgList);
    }

    @Override
    @JalorOperation(code = "overseaFlagList", desc = "国内海外下拉框")
    public ResultDataVO overseaFlagList(CommonPriceBaseVO commonBaseVO) throws ApplicationException {
        log.info(">>>Begin DropDownCommonService#overseaFlagList");
        commonBaseVO.setVersionId(commonService.getVersionId("ANNUAL"));
        List<DmFcstPriceDimInfoVO> overseaFlagList = dmFcstPriceDimInfoDao.getOverseaFlagList(commonBaseVO);
        // 获取当前角色的数据权限
        PriceDataPermissionsVO currentRoleDataPermission = priceDataPermissionService.getCurrentRoleDataPermission();
        // 获取国内海外权限
        Set<String> overseaDimensionSet = currentRoleDataPermission.getOverseaDimensionSet();
        // 定价区域分析师需要控制权限
        if (com.huawei.it.fcst.industry.price.constant.CommonConstant.REGION_ANALYST_PRICE.equals(UserInfoUtils.getRoleName())
                && CollectionUtils.isNotEmpty(overseaDimensionSet)) {
            // 过滤出申请的权限
            overseaFlagList = overseaFlagList.stream()
                    .filter(item -> overseaDimensionSet.contains(item.getOverseaFlag()))
                    .collect(Collectors.toList());
        }
        return ResultDataVO.success(overseaFlagList);
    }

    @JalorOperation(code = "regionCodeList", desc = "地区部下拉框")
    @Override
    public ResultDataVO regionCodeList(CommonPriceBaseVO commonBaseVO) {
        commonBaseVO.setVersionId(commonService.getVersionId("ANNUAL"));
        // 获取当前角色的数据权限
        PriceDataPermissionsVO currentRoleDataPermission = priceDataPermissionService.getCurrentRoleDataPermission();
        commonBaseVO.setOverseaFlagDimensionSet(currentRoleDataPermission.getOverseaDimensionSet());
        commonBaseVO.setIsRegionAnalyst(com.huawei.it.fcst.industry.price.constant.CommonConstant.REGION_ANALYST_PRICE.equals(UserInfoUtils.getRoleName()));
        List<DmFcstPriceDimInfoVO> regionCodeList = dmFcstPriceDimInfoDao.getRegionCodeList(commonBaseVO);
        // 定价区域分析师需要控制权限
        if (com.huawei.it.fcst.industry.price.constant.CommonConstant.REGION_ANALYST_PRICE.equals(UserInfoUtils.getRoleName())) {
            Map<String, Map<String, Set<String>>> locationMap = currentRoleDataPermission.getLocationMap();
            Map<String, Set<String>> bgSetMap = locationMap.get(commonBaseVO.getOverseaFlag());
            // 获取地区部权限
            Set<String> regionCodeOriginDimensionSet = currentRoleDataPermission.getRegionCodeDimensionSet();
            Set<String> regionDimensionTrueSet = currentRoleDataPermission.getRegionCodeDimensionTrueSet();
            Set<String> regionCodeDimensionSet = new HashSet<>();
            if (null != bgSetMap) {
                regionCodeDimensionSet = bgSetMap.get(commonBaseVO.getBgCode());
            } else {
                regionCodeDimensionSet.addAll(regionCodeOriginDimensionSet);
            }
            List<DmFcstPriceDimInfoVO> originRegionCodeList = new ArrayList<>();
            originRegionCodeList.addAll(regionCodeList);
            if (regionCodeDimensionSet.size() != 0 && regionCodeList.size() !=0) {
                Set<String> regionDimensionSet = new HashSet<>();
                regionDimensionSet.addAll(regionCodeDimensionSet);
                // 过滤出申请的权限
                regionCodeList = regionCodeList.stream().filter(re ->
                        regionDimensionSet.contains(re.getRegionCode())).collect(Collectors.toList());
                Set<String> regionCodeSet = originRegionCodeList.stream().filter(origin->
                        !"ALL".equals(origin.getRegionCode())).map(DmFcstPriceDimInfoVO::getRegionCode).collect(Collectors.toSet());
                if (regionDimensionTrueSet.containsAll(regionCodeSet) || regionDimensionTrueSet.size() == 0) {
                    DmFcstPriceDimInfoVO dmFcstDimInfoVO = new DmFcstPriceDimInfoVO();
                    dmFcstDimInfoVO.setRegionCode("ALL");
                    dmFcstDimInfoVO.setRegionCnName("全选");
                    regionCodeList.add(dmFcstDimInfoVO);
                }
            }
        }
        // 排序
        Collections.sort(regionCodeList, Comparator.comparingInt(o -> CommonConstant.ALL_CODE_RULE.getOrDefault(o.getRegionCode(), Integer.MAX_VALUE)));
        return ResultDataVO.success(regionCodeList);
    }

    @JalorOperation(code = "repofficeCodeList", desc = "代表处下拉框")
    @Override
    public ResultDataVO repofficeCodeList(CommonPriceBaseVO commonBaseVO) {
        commonBaseVO.setVersionId(commonService.getVersionId("ANNUAL"));
        if ("GLOBAL".equals(commonBaseVO.getRegionCode())) {
            commonBaseVO.setRegionCode(null);
        }
        // 获取当前角色的数据权限
        PriceDataPermissionsVO currentRoleDataPermission = priceDataPermissionService.getCurrentRoleDataPermission();
        commonBaseVO.setIsRegionAnalyst(com.huawei.it.fcst.industry.price.constant.CommonConstant.REGION_ANALYST_PRICE.equals(UserInfoUtils.getRoleName()));
        // 获取国内海外权限
        commonBaseVO.setOverseaFlagDimensionSet(currentRoleDataPermission.getOverseaDimensionSet());
        // 获取地区部权限
        commonBaseVO.setRegionCodeDimensionSet(currentRoleDataPermission.getRegionCodeDimensionSet());
        List<DmFcstPriceDimInfoVO> repofficeCodeList = dmFcstPriceDimInfoDao.getRepofficeCodeList(commonBaseVO);
        // 定价区域分析师需要控制权限
        if (com.huawei.it.fcst.industry.price.constant.CommonConstant.REGION_ANALYST_PRICE.equals(UserInfoUtils.getRoleName())) {
            // 获取代表处权限
            Set<String> repofficeCodeDimensionSet = currentRoleDataPermission.getRepofficeCodeDimensionSet();
            List<DmFcstPriceDimInfoVO> originRepofficeCodeList = new ArrayList<>();
            originRepofficeCodeList.addAll(repofficeCodeList);
            if (!repofficeCodeDimensionSet.isEmpty()) {
                repofficeCodeList = repofficeCodeList.stream().filter(repo ->
                        repofficeCodeDimensionSet.contains(repo.getRepofficeCode())).collect(Collectors.toList());
                Set<String> repofficeCodeSet = originRepofficeCodeList.stream().filter(code ->
                        !"ALL".equals(code.getRepofficeCode())).map(DmFcstPriceDimInfoVO::getRepofficeCode).collect(Collectors.toSet());
                if (repofficeCodeDimensionSet.containsAll(repofficeCodeSet) && repofficeCodeSet.size() == repofficeCodeList.size()) {
                    DmFcstPriceDimInfoVO dmFcstDimInfoVO = new DmFcstPriceDimInfoVO();
                    dmFcstDimInfoVO.setRepofficeCode("ALL");
                    dmFcstDimInfoVO.setRepofficeCnName("全选");
                    repofficeCodeList.add(dmFcstDimInfoVO);
                }
            }
        }
        // 排序
        Collections.sort(repofficeCodeList, Comparator.comparingInt(o -> CommonConstant.ALL_CODE_RULE.getOrDefault(o.getRepofficeCode(), Integer.MAX_VALUE)));
        return ResultDataVO.success(repofficeCodeList);
    }

    @JalorOperation(code = "keyAccountDeptList", desc = "大T系统部下拉框")
    @Override
    public ResultDataVO keyAccountDeptList(CommonPriceBaseVO commonBaseVO) {
        commonBaseVO.setVersionId(commonService.getVersionId("ANNUAL"));
        List<DmFcstPriceDimInfoVO> keyAccountDeptList = dmFcstPriceDimInfoDao.getKeyAccountDeptList(commonBaseVO);
        // 获取当前角色的数据权限
        PriceDataPermissionsVO currentRoleDataPermission = priceDataPermissionService.getCurrentRoleDataPermission();
        // 获取大T系统部权限
        Set<String> signTopCustCategoryCodeSet = currentRoleDataPermission.getSignTopCustCategoryCodeSet();
        // 定价区域分析师需要控制权限
        if (com.huawei.it.fcst.industry.price.constant.CommonConstant.REGION_ANALYST_PRICE.equals(UserInfoUtils.getRoleName())
                && CollectionUtils.isNotEmpty(signTopCustCategoryCodeSet)) {
            // 过滤出申请的权限
            keyAccountDeptList = keyAccountDeptList.stream()
                    .filter(item -> signTopCustCategoryCodeSet.contains(item.getSignTopCustCategoryCode()))
                    .collect(Collectors.toList());
        }
        // 排序
        Collections.sort(keyAccountDeptList, Comparator.comparingInt(o -> CommonConstant.ALL_CODE_RULE.getOrDefault(o.getSignTopCustCategoryCode(), Integer.MAX_VALUE)));
        return ResultDataVO.success(keyAccountDeptList);
    }

    @JalorOperation(code = "subAccountDeptList", desc = "子网系统下拉框")
    @Override
    public ResultDataVO subAccountDeptList(CommonPriceBaseVO commonBaseVO) {
        commonBaseVO.setVersionId(commonService.getVersionId("ANNUAL"));
        List<DmFcstPriceDimInfoVO> subAccountDeptList = dmFcstPriceDimInfoDao.getSubAccountDeptList(commonBaseVO);
        Set<String> subAccountDeptSet = subAccountDeptList.stream().filter(code ->
                !"全选".equals(code.getSignSubsidiaryCustcatgCnName())).map(DmFcstPriceDimInfoVO::getSignSubsidiaryCustcatgCnName).collect(Collectors.toSet());
        // 获取当前角色的数据权限
        PriceDataPermissionsVO currentRoleDataPermission = priceDataPermissionService.getCurrentRoleDataPermission();
        // 获取大T系统部权限
        Set<String> signSubsidiaryCustcatgCodeSet = currentRoleDataPermission.getSignSubsidiaryCustcatgCodeSet();
        // 定价区域分析师需要控制权限
        if (com.huawei.it.fcst.industry.price.constant.CommonConstant.REGION_ANALYST_PRICE.equals(UserInfoUtils.getRoleName())
                && CollectionUtils.isNotEmpty(signSubsidiaryCustcatgCodeSet)) {
            // 过滤出申请的权限
            subAccountDeptList = subAccountDeptList.stream()
                    .filter(item -> signSubsidiaryCustcatgCodeSet.contains(item.getSignSubsidiaryCustcatgCnName()))
                    .collect(Collectors.toList());
            if (signSubsidiaryCustcatgCodeSet.containsAll(subAccountDeptSet)) {
                DmFcstPriceDimInfoVO dmFcstDimInfoVO = new DmFcstPriceDimInfoVO();
                dmFcstDimInfoVO.setSignTopCustCategoryCode("ALL");
                dmFcstDimInfoVO.setSignTopCustCategoryCnName("全选");
                dmFcstDimInfoVO.setSignSubsidiaryCustcatgCnName("全选");
                subAccountDeptList.add(dmFcstDimInfoVO);
            }
        }
        // 排序
        Collections.sort(subAccountDeptList, Comparator.comparingInt(o -> CommonConstant.ALL_CN_RULE.getOrDefault(o.getSignSubsidiaryCustcatgCnName(), Integer.MAX_VALUE)));
        return ResultDataVO.success(subAccountDeptList);
    }

    @JalorOperation(code = "dropDownList", desc = "年度月度下拉框")
    @Override
    public ResultDataVO dropDownList(CommonPriceBaseVO commonBaseVO) {
        PriceDataPermissionsVO currentRoleDataPermission = priceDataPermissionService.getCurrentRoleDataPermission();
        commonBaseVO.setLv2DimensionSet(currentRoleDataPermission.getLv2DimensionSet());
        FcstIndustryUtil.setSpecailCode(commonBaseVO);
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(commonBaseVO.getPageSize());
        pageVO.setCurPage(commonBaseVO.getPageIndex());
        BasePriceCusDimVO baseCusDimVO = ObjectCopyUtil.copy(commonBaseVO, BasePriceCusDimVO.class);
        baseCusDimVO.setRoleId(String.valueOf(UserInfoUtils.getRoleId()));
        Long userId = UserInfoUtils.getUserId();
        baseCusDimVO.setUserId(String.valueOf(userId));
        commonBaseVO.setVersionId(commonService.getVersionId("ANNUAL"));
        List<DmFcstPriceDimInfoVO> allDropDownDimInfoList = new ArrayList<>();
        // 查询区间年
        List<String> yearList = annualAmpPriceService.getYearList();
        if (CollectionUtils.isNotEmpty(yearList)) {
            commonBaseVO.setMaxYear(Integer.valueOf(yearList.get(0)));
        }
        // 获取各层级下拉框数据
        getDropdownDimInfoList(commonBaseVO, pageVO, baseCusDimVO, allDropDownDimInfoList);
        Map dropDownMap = new LinkedHashMap();
        dropDownMap.put("result", allDropDownDimInfoList);
        dropDownMap.put("pageVo", pageVO);
        return ResultDataVO.success(dropDownMap);
    }

    private void getDropdownDimInfoList(CommonPriceBaseVO commonBaseVO, PageVO pageVO, BasePriceCusDimVO baseCusDimVO, List<DmFcstPriceDimInfoVO> allDropDownDimInfoVOList) {
        List<DmFcstPriceDimInfoVO> dimInfoVOList = dmFcstPriceDimInfoDao.baseDropdownDimInfoList(commonBaseVO);
        FcstIndustryUtil.setLvCode(baseCusDimVO);
        // 查询虚化的数据
        setCustomStatus(baseCusDimVO, dimInfoVOList);
        List<DmFcstPriceDimInfoVO> sortedDimInfoVOList = dimInfoVOList.stream().sorted(Comparator.comparing(DmFcstPriceDimInfoVO::getStatusFlag,
                Comparator.nullsLast(String::compareTo))).collect(Collectors.toList());
        if (!commonBaseVO.getSaveFlag() && GroupLevelEnum.SPART.getValue().equals(commonBaseVO.getNextGroupLevel())) {
            // 分页
            int count = sortedDimInfoVOList.size();
            sortedDimInfoVOList = getFcstSpartDimInfoPageList(sortedDimInfoVOList, pageVO);
            pageVO.setTotalRows(count);
        }
        allDropDownDimInfoVOList.addAll(sortedDimInfoVOList);
    }


    @NotNull
    private List<DmFcstPriceDimInfoVO> getFcstSpartDimInfoPageList(List<DmFcstPriceDimInfoVO> sortedFcstDimInfoVOList, PageVO pageVO) {
        int count = sortedFcstDimInfoVOList.size();
        int curPage = pageVO.getCurPage() - 1;
        int pageSize = pageVO.getPageSize();
        List<DmFcstPriceDimInfoVO> fcstDimInfoVOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(sortedFcstDimInfoVOList) && count > 0) {
            int fromIndex = curPage * pageSize;
            int totalIndex = (curPage + 1) * pageSize;
            if (totalIndex > count) {
                totalIndex = count;
            }
            fcstDimInfoVOList = sortedFcstDimInfoVOList.subList(fromIndex, totalIndex);
        }
        return fcstDimInfoVOList;
    }

    private void setCustomStatus(BasePriceCusDimVO baseCusDimVO, List<DmFcstPriceDimInfoVO> dimInfoVOList) {
        if (GroupLevelEnum.SPART.getValue().equals(baseCusDimVO.getNextGroupLevel())) {
            List<DmFcstBasePriceCusDimVO> fiterBaseCusDimStatus = getBaseCusDimList(baseCusDimVO);
            for (DmFcstBasePriceCusDimVO baseCusDim : fiterBaseCusDimStatus) {
                for (DmFcstPriceDimInfoVO dimInfoVO : dimInfoVOList) {
                    setStatusFlag(baseCusDim, dimInfoVO);
                }
            }
        }
    }

    @NotNull
    private List<DmFcstBasePriceCusDimVO> getBaseCusDimList(BasePriceCusDimVO baseCusDimVO) {
        List<DmFcstBasePriceCusDimVO> dmFcstBaseCusDimList = dmFcstBaseCusDao.baseCusDimStatus(baseCusDimVO);
        // 组合的返回值需要处理lvCode字段
        // 由于lvCode是拼接的，需要将拼接的字符串转换为数组后，匹配
        List<String> paramLvCodeList = Arrays.asList(baseCusDimVO.getLvCode().replace("'", "").split(","));
        List<DmFcstBasePriceCusDimVO> fiterBaseCusDimStatus = new ArrayList<>();
        // 获取已有虚化组合
        for (DmFcstBasePriceCusDimVO dmFcstBaseCusVO : dmFcstBaseCusDimList) {
            String lvCodeStr = dmFcstBaseCusVO.getLvCode().replace("'", "");
            List<String> lvCodeList = Arrays.asList(lvCodeStr.split(","));
            if (paramLvCodeList.containsAll(lvCodeList) && paramLvCodeList.size() == lvCodeList.size()) {
                fiterBaseCusDimStatus.add(dmFcstBaseCusVO);
            }
        }
        return fiterBaseCusDimStatus;
    }

    private void setStatusFlag(DmFcstBasePriceCusDimVO baseCusDim, DmFcstPriceDimInfoVO dimInfoVO) {
        if (baseCusDim.getGroupCode().equals(dimInfoVO.getGroupCode())) {
            if (CommonConstEnum.STATUS_FLAG.D.getValue().equals(baseCusDim.getStatusFlag())) {
                dimInfoVO.setStatusFlag(CommonConstEnum.STATUS_FLAG.ING.getValue());
            } else {
                dimInfoVO.setStatusFlag(baseCusDim.getStatusFlag());
            }
            dimInfoVO.setCustomId(baseCusDim.getCustomId());
        }
    }

    @JalorOperation(code = "lv0CodeDropdown", desc = "获取lv0编码")
    @Override
    public ResultDataVO lv0CodeDropdown(CommonPriceBaseVO commonViewVO) {
        commonViewVO.setVersionId(commonService.getVersionId("ANNUAL"));
        return ResultDataVO.success(dmFcstPriceDimInfoDao.getLv0Code(commonViewVO));
    }

}
