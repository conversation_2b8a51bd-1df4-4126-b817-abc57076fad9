/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.relation;

import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoDTO;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * BackDimensionVO Class
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "配置管理页面维度关系入参")
public class BackDimensionVO implements Serializable {
    private static final long serialVersionUID = 4682328884171838915L;

    /**
     *
     * 版本对象
     */
    private DmFocVersionInfoDTO dmFocPlanVersionVO;

    /**
     *
     * 错误列表
     */
    private List<DmDimCatgModlCegIctVO> errorList;

}
