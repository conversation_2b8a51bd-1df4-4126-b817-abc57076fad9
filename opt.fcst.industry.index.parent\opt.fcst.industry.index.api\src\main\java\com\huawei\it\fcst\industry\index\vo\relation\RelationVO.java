/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.relation;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * RelationVO Class
 *
 * <AUTHOR>
 * @since 2023/3/17
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RelationVO implements Serializable {
    private static final long serialVersionUID = -3705604104001180986L;

    private List<DmDimCatgModlCegIctVO> dimCegCodeD;

    private List<String> l3CodeList;

    private List<String> l3NameList;

    private List<String> l4CodeList;

    private List<String> l4NameList;

    private List<DmDimMaterialCodeVO> dimMaterialCodeD;

    private List<String> materialCodeList;

    private List<String> materialNameList;

    private List<String> l3AllShortName;

    private Map<String, List<DmDimCatgModlCegIctVO>> duplicateMap;

    private Map<String, List<DmDimCatgModlCegIctVO>> l3MoreShortNameMap;

    private Map<String, List<DmDimCatgModlCegIctVO>> l4MoreShortNameMap;

    private Map<String, List<DmDimCatgModlCegIctVO>> l3OnlyShortNameMap;

    private Map<String, List<DmDimCatgModlCegIctVO>> l4OnlyShortNameMap;

    private Map<String, List<DmDimCatgModlCegIctVO>> l3AndL4Map;

    private Map<String, List<DmDimCatgModlCegIctVO>> l3CategoryMap;

    private Map<String, List<DmDimCatgModlCegIctVO>> l4CategoryMap;
}
