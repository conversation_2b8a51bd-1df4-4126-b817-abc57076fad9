/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.service.calculate;

import com.huawei.it.fcst.industry.pbi.vo.common.CommonBaseVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.CodeReplInfoVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.InterLockInfoVO;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * <AUTHOR>
 * @since 2024/6/26
 */
@Path("/comb")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface ISummaryCalculateService {

    /**
     * 年度月度各层级下拉框(虚化计算)
     *
     * @param commonBaseVO 参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/summaryCalculate")
    ResultDataVO dropDownSummaryCalculate(CommonBaseVO commonBaseVO) throws CommonApplicationException;


    /**
     * 编码替代各层级下拉框(虚化计算)
     *
     * @param codeReplInfoVO 参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/codeReplace/summaryCalculate")
    ResultDataVO dropDownCodeReplaceSummaryCalculate(CodeReplInfoVO codeReplInfoVO) throws CommonApplicationException;


    /**
     * 勾稽管理各层级下拉框(虚化计算)
     *
     * @param interLockInfoVO 参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/interLock/summaryCalculate")
    ResultDataVO dropDownInterLockSummaryCalculate(InterLockInfoVO interLockInfoVO) throws CommonApplicationException;


    /**
     * 勾稽管理各层级下拉框(虚化计算)
     *
     * @param interLockInfoVO 参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/interLock/getLv4CodeList")
    ResultDataVO getLv4CodeList(InterLockInfoVO interLockInfoVO);


}
