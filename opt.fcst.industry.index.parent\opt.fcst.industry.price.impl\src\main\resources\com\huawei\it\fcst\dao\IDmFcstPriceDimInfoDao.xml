<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.price.dao.IDmFcstPriceDimInfoDao">
    <resultMap type="com.huawei.it.fcst.industry.price.vo.common.DmFcstPriceDimInfoVO" id="resultMap">
        <result property="id" column="id"/>
        <result property="overseaFlag" column="oversea_flag"/>
        <result property="overseaCnName" column="oversea_cn_name"/>
        <result property="bgCode" column="bg_code"/>
        <result property="bgCnName" column="bg_cn_name"/>
        <result property="spartCode" column="spart_code"/>
        <result property="spartCnName" column="spart_cn_name"/>
        <result property="regionCode" column="region_code"/>
        <result property="regionCnName" column="region_cn_name"/>
        <result property="repofficeCode" column="repoffice_code"/>
        <result property="repofficeCnName" column="repoffice_cn_name"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="groupCode" column="groupCode"/>
        <result property="groupLevel" column="groupLevel"/>
        <result property="groupCnName" column="groupCnName"/>
        <result property="groupLevel" column="group_level"/>
        <result property="num" column="num"/>
        <result property="lv0Code" column="lv0_code"/>
        <result property="lv0ProdListCode" column="lv0_prod_list_code"/>
        <result property="lv1ProdListCode" column="lv1_prod_list_code"/>
        <result property="lv2ProdListCode" column="lv2_prod_list_code"/>
        <result property="lv3ProdListCode" column="lv3_prod_list_code"/>
        <result property="signTopCustCategoryCode" column="sign_top_cust_category_code"/>
        <result property="signTopCustCategoryCnName" column="sign_top_cust_category_cn_name"/>
        <result property="signSubsidiaryCustcatgCnName" column="sign_subsidiary_custcatg_cn_name"/>
        <result property="prodListCode" column="prod_list_code"/>

    </resultMap>

    <select id="getBgList" resultMap="resultMap">
        select distinct bg_code,bg_cn_name
        from fin_dm_opt_foi.dm_fcst_price_dim_info_t
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            and bg_code is not null
            <if test='versionId!=null'>
                and version_id = #{versionId}
            </if>
        </trim>
    </select>

    <select id="getOverseaFlagList" resultMap="resultMap">
        SELECT DISTINCT oversea_flag,
        DECODE(oversea_flag, 'G', '全球', 'Y', '海外', '国内') AS oversea_cn_name,
        DECODE(oversea_flag, 'G', '1', 'N', '2', '3') AS order_num
        FROM fin_dm_opt_foi.dm_fcst_price_dim_info_t
        WHERE del_flag = 'N'
        AND oversea_flag IS NOT NULL
        <if test='versionId != null'>
            AND version_id = #{versionId}
        </if>
        <if test='bgCode!=null and bgCode!=""'>
            AND bg_code = #{bgCode}
        </if>
        ORDER BY order_num
    </select>

    <select id="getRegionCodeList" resultMap="resultMap">
        select distinct oversea_flag,region_code,region_cn_name
        from fin_dm_opt_foi.dm_fcst_price_dim_info_t
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            and region_code is not null
            <if test='overseaFlag!=null and overseaFlag!=""'>
                and oversea_flag = #{overseaFlag}
            </if>
            <if test='bgCode!=null and bgCode!=""'>
                and bg_code = #{bgCode}
            </if>
            <if test='versionId!=null'>
                and version_id = #{versionId}
            </if>
            <if test='viewFlag != null and viewFlag != ""'>
                and view_flag = #{viewFlag,jdbcType=VARCHAR}
            </if>
            <if test='isRegionAnalyst == true and overseaFlagDimensionSet != null and overseaFlagDimensionSet.size() > 0'>
                <foreach collection='overseaFlagDimensionSet' item="code" open="AND oversea_flag IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getPermissionRegionCodeList" resultMap="resultMap">
        select distinct region_code,region_cn_name
        from fin_dm_opt_foi.dm_fcst_price_dim_info_t
        where
        view_flag = 'LOCAL_AGENT' and region_code !='ALL'
        <if test='regionCodeDimensionSet != null and regionCodeDimensionSet.size() > 0'>
            <foreach collection='regionCodeDimensionSet' item="code" open="AND region_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>

    </select>

    <select id="getRepofficeCodeList" resultMap="resultMap">
        select distinct oversea_flag,repoffice_code,repoffice_cn_name,region_code,region_cn_name
        from fin_dm_opt_foi.dm_fcst_price_dim_info_t
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            and repoffice_code is not null
            <if test='overseaFlag!=null and overseaFlag!=""'>
                and oversea_flag = #{overseaFlag}
            </if>
            <if test='bgCode!=null and bgCode!=""'>
                and bg_code = #{bgCode}
            </if>
            <if test='regionCode!=null and regionCode!=""'>
                and region_code = #{regionCode}
            </if>
            <if test='repofficeCode!=null and repofficeCode!=""'>
                and repoffice_code = #{repofficeCode}
            </if>
            <if test='versionId!=null'>
                and version_id = #{versionId}
            </if>
            <if test='viewFlag != null and viewFlag != ""'>
                and view_flag = #{viewFlag,jdbcType=VARCHAR}
            </if>
            <if test='isRegionAnalyst == true and overseaFlagDimensionSet != null and overseaFlagDimensionSet.size() > 0'>
                <foreach collection='overseaFlagDimensionSet' item="code" open="AND oversea_flag IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='isRegionAnalyst == true  and regionCode!= "ALL" and regionCodeDimensionSet != null and regionCodeDimensionSet.size() > 0'>
                <foreach collection='regionCodeDimensionSet' item="code" open="AND region_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getKeyAccountDeptList" resultMap="resultMap">
        select distinct sign_top_cust_category_code,sign_top_cust_category_cn_name
        from fin_dm_opt_foi.dm_fcst_price_dim_info_t
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            and sign_top_cust_category_code is not null
            <if test='bgCode!=null and bgCode!=""'>
                and bg_code = #{bgCode}
            </if>
            <if test='versionId!=null'>
                and version_id = #{versionId}
            </if>
            <if test='viewFlag != null and viewFlag != ""'>
                and view_flag = #{viewFlag,jdbcType=VARCHAR}
            </if>
        </trim>
    </select>

    <select id="getSubAccountDeptList" resultMap="resultMap">
        select distinct sign_subsidiary_custcatg_cn_name,sign_top_cust_category_code,sign_top_cust_category_cn_name
        from fin_dm_opt_foi.dm_fcst_price_dim_info_t
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            and sign_subsidiary_custcatg_cn_name is not null
            <if test='bgCode!=null and bgCode!=""'>
                and bg_code = #{bgCode}
            </if>
            <if test='signTopCustCategoryCode!=null and signTopCustCategoryCode!=""'>
                and sign_top_cust_category_code = #{signTopCustCategoryCode}
            </if>
            <if test='signSubsidiaryCustcatgCnName != null and signSubsidiaryCustcatgCnName != ""'>
                and sign_subsidiary_custcatg_cn_name = #{signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
            </if>
            <if test='versionId!=null'>
                and version_id = #{versionId}
            </if>
            <if test='viewFlag != null and viewFlag != ""'>
                and view_flag = #{viewFlag,jdbcType=VARCHAR}
            </if>
        </trim>
    </select>

    <select id="baseDropdownDimInfoList" resultMap="resultMap">
        select
        <choose>
            <when test='searchVO.nextGroupLevel == "SPART"'>
                DISTINCT spart_code AS groupCode, spart_cn_name AS groupCnName, 'SPART' AS groupLevel
            </when>
            <when test='searchVO.nextGroupLevel == "LV4"'>
                DISTINCT LV4_PROD_LIST_CODE AS groupCode, LV4_PROD_LIST_CN_NAME AS groupCnName, 'LV4' AS
                groupLevel
            </when>
            <when test='searchVO.nextGroupLevel == "LV3"'>
                DISTINCT LV3_PROD_LIST_CODE AS groupCode, LV3_PROD_LIST_CN_NAME AS groupCnName, 'LV3' AS
                groupLevel
            </when>
            <when test='searchVO.nextGroupLevel == "LV2"'>
                DISTINCT LV2_PROD_LIST_CODE AS groupCode, LV2_PROD_LIST_CN_NAME AS groupCnName,'LV2' AS
                groupLevel
            </when>
            <when test='searchVO.nextGroupLevel == "LV1"'>
                DISTINCT LV1_PROD_LIST_CODE AS groupCode, LV1_PROD_LIST_CN_NAME AS groupCnName,'LV1' AS
                groupLevel
            </when>
            <when test='searchVO.nextGroupLevel == "LV0"'>
                DISTINCT LV0_PROD_LIST_CODE AS groupCode, LV0_PROD_LIST_CN_NAME AS groupCnName,'LV0' AS
                groupLevel
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <include refid="baseDimSearchWhere"></include>
        <if test='searchVO.nextGroupLevel == "SPART"'>
            order by spart_code asc
        </if>
    </select>

    <sql id="baseDimSearchWhere">
        from fin_dm_opt_foi.dm_fcst_price_dim_info_t
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='searchVO.regionCode!=null and searchVO.regionCode!=""'>
                and region_code = #{searchVO.regionCode,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.repofficeCode!=null and searchVO.repofficeCode!=""'>
                and repoffice_code = #{searchVO.repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.bgCode!=null and searchVO.bgCode!=""'>
                and bg_code = #{searchVO.bgCode,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.overseaFlag!=null and searchVO.overseaFlag!=""'>
                and oversea_flag = #{searchVO.overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.viewFlag!=null and searchVO.viewFlag!=""'>
                and view_flag = #{searchVO.viewFlag,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.signTopCustCategoryCode != null and searchVO.signTopCustCategoryCode != ""'>
                and sign_top_cust_category_code = #{searchVO.signTopCustCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.signSubsidiaryCustcatgCnName != null and searchVO.signSubsidiaryCustcatgCnName != ""'>
                and sign_subsidiary_custcatg_cn_name = #{searchVO.signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.versionId!=null'>
                and version_id = #{searchVO.versionId,jdbcType=NUMERIC}
            </if>
            <if test='searchVO.nextGroupLevel !=null and searchVO.nextGroupLevel !=""'>
                and group_level = #{searchVO.nextGroupLevel,jdbcType=VARCHAR}
            </if>
            <if test='searchVO.lv0ProdListCodeList != null and searchVO.lv0ProdListCodeList.size() > 0'>
                <foreach collection='searchVO.lv0ProdListCodeList' item="code" open="AND LV0_PROD_LIST_CODE IN ("
                         close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='searchVO.lv1ProdListCodeList != null and searchVO.lv1ProdListCodeList.size() > 0'>
                <foreach collection='searchVO.lv1ProdListCodeList' item="code" open="AND LV1_PROD_LIST_CODE IN ("
                         close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='searchVO.lv2ProdListCodeList != null and searchVO.lv2ProdListCodeList.size() > 0'>
                <foreach collection='searchVO.lv2ProdListCodeList' item="code" open="AND LV2_PROD_LIST_CODE IN ("
                         close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='searchVO.lv3ProdListCodeList != null and searchVO.lv3ProdListCodeList.size() > 0'>
                <foreach collection='searchVO.lv3ProdListCodeList' item="code" open="AND LV3_PROD_LIST_CODE IN ("
                         close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='searchVO.lv4ProdListCodeList != null and searchVO.lv4ProdListCodeList.size() > 0'>
                <foreach collection='searchVO.lv4ProdListCodeList' item="code" open="AND LV4_PROD_LIST_CODE IN ("
                         close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='searchVO.spartCodeList != null and searchVO.spartCodeList.size() > 0'>
                <foreach collection='searchVO.spartCodeList' item="code" open="AND spart_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='searchVO.nextGroupLevel == "SPART"'>
                and spart_code !='SNULL'
            </if>
            <if test='searchVO.lv2DimensionSet != null and searchVO.lv2DimensionSet.size() > 0'>
                <foreach collection='searchVO.lv2DimensionSet' item="code" open="AND LV1_PROD_LIST_CODE IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='searchVO.keyword != null and searchVO.keyword != ""'>
                AND spart_code LIKE '%'||#{searchVO.keyword} ||'%'
            </if>
        </trim>
    </sql>

    <select id="getLv0Code" resultMap="resultMap">
        select distinct lv0_prod_list_code as lv0_code
        from fin_dm_opt_foi.dm_fcst_price_dim_info_t
        <if test='bgCode != null and bgCode!= ""'>
            where bg_code =#{bgCode}
        </if>
        <if test='versionId!=null'>
            and version_id = #{versionId,jdbcType=NUMERIC}
        </if>
    </select>

    <select id="getSpartNum" resultType="java.lang.Integer">
        select count(spart_code) AS num
        from fin_dm_opt_foi.dm_fcst_price_dim_info_t
        <include refid="getNumCondition"></include>
    </select>

    <select id="getLv4CodeWithSpart" resultMap="resultMap">
        select lv4_prod_list_code as prod_list_code
        from fin_dm_opt_foi.dm_fcst_price_dim_info_t
        <include refid="getNumCondition"></include>
    </select>

    <sql id="getNumCondition">
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='versionId!=null'>
                and version_id = #{versionId,jdbcType=NUMERIC}
            </if>
            <if test='regionCode!=null and regionCode!=""'>
                and region_code = #{regionCode,jdbcType=VARCHAR}
            </if>
            <if test='repofficeCode!=null and repofficeCode!=""'>
                and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='bgCode!=null and bgCode!=""'>
                and bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='overseaFlag!=null and overseaFlag!=""'>
                and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='viewFlag!=null and viewFlag!=""'>
                and view_flag = #{viewFlag,jdbcType=VARCHAR}
            </if>
            <if test='pageType == "ANNUAL" and groupLevel!=null and groupLevel!=""'>
                and group_level = #{groupLevel,jdbcType=VARCHAR}
            </if>
            <if test='signTopCustCategoryCode != null and signTopCustCategoryCode != ""'>
                and sign_top_cust_category_code = #{signTopCustCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test='signSubsidiaryCustcatgCnName != null and signSubsidiaryCustcatgCnName != ""'>
                and sign_subsidiary_custcatg_cn_name = #{signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
            </if>
            <if test='lv0ProdListCodeList != null and lv0ProdListCodeList.size() > 0'>
                <foreach collection='lv0ProdListCodeList' item="code" open="AND lv0_prod_list_code IN ("
                         close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv1ProdListCodeList != null and lv1ProdListCodeList.size() > 0'>
                <foreach collection='lv1ProdListCodeList' item="code" open="AND lv1_prod_list_code IN ("
                         close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv2ProdListCodeList != null and lv2ProdListCodeList.size() > 0'>
                <foreach collection='lv2ProdListCodeList' item="code" open="AND lv2_prod_list_code IN ("
                         close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv3ProdListCodeList != null and lv3ProdListCodeList.size() > 0'>
                <foreach collection='lv3ProdListCodeList' item="code" open="AND lv3_prod_list_code IN ("
                         close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv4ProdListCodeList != null and lv4ProdListCodeList.size() > 0'>
                <foreach collection='lv4ProdListCodeList' item="code" open="AND lv4_prod_list_code IN ("
                         close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='spartCodeList!=null and spartCodeList.size() > 0'>
                <foreach collection='spartCodeList' item="code" open="AND spart_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='nextGroupLevel == "SPART"'>
                and spart_code !='SNULL'
            </if>
            <if test='lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND LV1_PROD_LIST_CODE IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </sql>

    <select id="getBgCodePermissionList" resultMap="resultMap">
        select distinct bg_code,bg_cn_name
        from fin_dm_opt_foi.dm_fcst_price_dim_info_t
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND lv1_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getLv1CodeList" resultMap="resultMap">
        select * from (select DISTINCT lv0_prod_list_code, lv1_prod_list_code FROM fin_dm_opt_foi.dm_fcst_price_dim_info_t
        WHERE DEL_FLAG = 'N' and group_level ='LV2' and version_id = #{versionId})
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND lv0_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="findRefreshTime" resultMap="resultMap">
        SELECT last_update_date, version_id
        FROM fin_dm_opt_foi.DM_FCST_PRICE_TOP_SPART_INFO_T
        WHERE del_flag = 'N'
          AND is_top_flag = 'Y'
          AND version_id = (
            SELECT version_id
            FROM fin_dm_opt_foi.DM_FCST_PRICE_VERSION_INFO_T
            WHERE del_flag = 'N'
              AND is_running = 'N'
              AND status = 1
              AND data_type = 'MONTH'
              AND version_type IN ('AUTO', 'FINAL')
            ORDER BY creation_date DESC
            LIMIT 1
            )
        ORDER BY creation_date DESC
            LIMIT 1 OFFSET 0
    </select>
</mapper>