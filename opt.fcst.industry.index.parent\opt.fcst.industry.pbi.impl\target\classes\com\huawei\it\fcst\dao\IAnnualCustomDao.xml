<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.pbi.dao.IAnnualCustomDao">
    <resultMap type="com.huawei.it.fcst.industry.pbi.vo.annual.DmFocAnnualAmpVO" id="annualResultMap">
        <result property="id" column="id"/>
        <result property="versionId" column="version_id"/>
        <result property="periodYear" column="period_year"/>
        <result property="prodRndTeamCode" column="prod_rnd_team_code"/>
        <result property="prodRndTeamCnName" column="prod_rnd_team_cn_name"/>
        <result property="parentLevel" column="parent_level"/>
        <result property="parentCode" column="parent_code"/>
        <result property="parentCnName" column="parent_cn_name"/>
        <result property="groupCode" column="group_code"/>
        <result property="groupCnName" column="group_cn_name"/>
        <result property="groupLevel" column="group_level"/>
        <result property="annualAmp" column="annual_amp"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDdate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="appendFlag" column="append_flag"/>
        <result property="weightRate" column="weight_rate"/>
        <result property="statusCode" column="status_code"/>
        <result property="groupCodeAndName" column="groupCodeAndName"/>
        <result property="weightAnnualAmpPercent" column="weightAnnualAmpPercent"/>
        <result property="appendYear" column="append_year"/>
        <result property="dimensionCode" column="dimension_code"/>
        <result property="dimensionCnName" column="dimension_cn_name"/>
        <result property="dimensionSubCategoryCode" column="dimension_subcategory_code"/>
        <result property="dimensionSubCategoryCnName" column="dimension_subcategory_cn_name"/>
        <result property="dimensionSubDetailCode" column="dimension_sub_detail_code"/>
        <result property="dimensionSubDetailCnName" column="dimension_sub_detail_cn_name"/>
        <result property="customCnName" column="custom_cn_name"/>
        <result property="customId" column="custom_id"/>
        <result property="rmbCostAmt" column="rmb_cost_amt"/>
    </resultMap>

    <select id="allIndustryCustomCost" resultMap="annualResultMap">
        select distinct amp.custom_id,amp.custom_cn_name,
        amp.group_level,amp.group_cn_name,amp.group_code,
        amp.period_year,
        CONCAT(ROUND(amp.annual_amp*100,1),'%') as annual_amp,
        status.status_code,'P' as cost_type
        from fin_dm_opt_foi.DM_FCST_ICT_${combTablePreFix}_BASE_CUS_ANNL_AMP_T amp
        left join fin_dm_opt_foi.DM_FCST_ICT_${combTablePreFix}_BASE_CUS_ANNL_STATUS_T status
        on amp.group_code = status.group_code and amp.group_level = status.group_level
        and amp.custom_id = status.custom_id
        and nvl(amp.parent_code,'snull') = nvl(status.parent_code,'snull')
        and nvl(amp.parent_level,'snull') = nvl(status.parent_level,'snull')
        and amp.period_year = status.period_year
        and amp.version_id = status.version_id and amp.view_flag = status.view_flag
        and amp.granularity_type =status.granularity_type
        and amp.oversea_flag = status.oversea_flag
        and amp.bg_code = status.bg_code
        and amp.region_code = status.region_code
        and amp.repoffice_code = status.repoffice_code
        where amp.del_flag = 'N'
        <if test='groupLevel != null and groupLevel != ""'>
            and amp.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='customId != null'>
            and amp.custom_id = #{customId}
        </if>
        <if test='granularityType != null and granularityType != ""'>
            and amp.granularity_type = #{granularityType,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag != null and overseaFlag != ""'>
            and amp.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and amp.bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and amp.region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and amp.repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag != null and mainFlag != ""'>
            and amp.main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes != null and codeAttributes != ""'>
            and amp.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='parentLevel != null and parentLevel != ""'>
            and amp.parent_level = #{parentLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and amp.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='periodYear != null and periodYear != ""'>
            and amp.period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and amp.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND amp.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        order by amp.period_year
    </select>

    <select id="findCombCodeOrderByWeight" resultMap="annualResultMap">
        select distinct group_code, group_cn_name,ROUND( sum(weight_rate) * 100, 1 ) weight_rate
        from fin_dm_opt_foi.DM_FCST_ICT_${combTablePreFix}_BASE_CUS_ANNL_WEIGHT_T
        where del_flag = 'N'
        <if test='viewFlag != null and viewFlag != "" and viewFlag == "PROD_SPART"'>
            and group_level = 'SPART'
        </if>
        <if test='viewFlag != null and viewFlag != "" and viewFlag == "DIMENSION"'>
            and group_level = 'SUB_DETAIL'
        </if>
        <if test='customId != null'>
            and custom_id = #{customId}
        </if>
        <if test='granularityType != null and granularityType != ""'>
            and granularity_type = #{granularityType,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag != null and overseaFlag != ""'>
            and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag != null and mainFlag != ""'>
            and main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes != null and codeAttributes != ""'>
            and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='parentLevel != null and parentLevel != ""'>
            and parent_level = #{parentLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='periodYear != null and periodYear !=""'>
            and period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='year != null and year != ""'>
            and period_year = #{year,jdbcType=VARCHAR}
        </if>
        <if test='year != null and year != ""'>
            and period_year = #{year,jdbcType=VARCHAR}
        </if>
        <if test='parentCodeList != null and parentCodeList.size() > 0'>
            <foreach collection='parentCodeList' item="code" open="AND parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        group by group_code, group_cn_name
        ORDER BY weight_rate DESC
    </select>

    <select id="findCombCodeOrderMinLevelByWeight" resultMap="annualResultMap">
        select distinct amp.lv_code as prod_rnd_team_code,amp.lv_cn_name as prod_rnd_team_cn_name,
        amp.group_code, amp.group_cn_name, ROUND(SUM(weight.weight_rate*100 ),1) as weight_rate
        from fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_MID_GROUP_AMP_T amp
        left join fin_dm_opt_foi.DM_FCST_ICT_${combTablePreFix}_BASE_CUS_ANNL_WEIGHT_T weight
        on amp.view_flag = weight.view_flag
        and amp.group_code = weight.group_code
        and amp.group_level = weight.group_level
        and amp.parent_code = weight.parent_code
        and amp.version_id =weight.version_id and amp.period_year = weight.period_year
        and amp.oversea_flag = weight.oversea_flag
        and amp.bg_code = weight.bg_code
        and amp.region_code = weight.region_code
        and amp.repoffice_code = weight.repoffice_code
        and nvl ( amp.main_flag, 'snull' ) = nvl ( weight.main_flag, 'snull' )
        and nvl ( amp.code_attributes, 'snull' ) = nvl ( weight.code_attributes, 'snull' )
        and amp.lv_code = weight.lv4_code
        where amp.del_flag = 'N'
        <if test='viewFlag != null and viewFlag != "" and viewFlag == "PROD_SPART"'>
            and weight.group_level = 'SPART'
        </if>
        <if test='viewFlag != null and viewFlag != "" and viewFlag == "DIMENSION"'>
            and weight.group_level = 'SUB_DETAIL'
        </if>
        <if test='groupLevel != null and groupLevel != "" and (groupLevel =="SPART" or groupLevel =="SUB_DETAIL")'>
            and weight.logic_num = 1
        </if>
        <if test='customId != null'>
            and weight.custom_id = #{customId}
        </if>
        <if test='granularityType != null and granularityType != ""'>
            and weight.granularity_type = #{granularityType,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag != null and overseaFlag != ""'>
            and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and weight.bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and weight.region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and weight.repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag != null and mainFlag != ""'>
            and weight.main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes != null and codeAttributes != ""'>
            and weight.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and weight.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and weight.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='periodYear != null and periodYear !=""'>
            and weight.period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='year != null and year != ""'>
            and weight.period_year = #{year,jdbcType=VARCHAR}
        </if>
        <if test='groupCodeList != null and groupCodeList.size() > 0'>
            <foreach collection='groupCodeList' item="code" open="AND weight.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='prodRndTeamCodeList != null and prodRndTeamCodeList.size() > 0'>
        <choose>
            <when test='teamLevel == "LV0"'>
                <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.lv0_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='teamLevel == "LV1"'>
                <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.lv1_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='teamLevel == "LV2"'>
                <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.lv2_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='teamLevel == "LV3"'>
                <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.lv3_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='teamLevel == "LV4"'>
                <foreach collection='prodRndTeamCodeList' item="code" open="AND amp.lv4_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        </if>
        group by prod_rnd_team_code,prod_rnd_team_cn_name,amp.group_code, amp.group_cn_name
        ORDER BY weight_rate DESC
    </select>

    <select id="multiIndustryCustomCombChart" resultMap="annualResultMap">
        select
        amp.custom_cn_name,
        amp.custom_id,
        amp.parent_code,
        amp.parent_level,
        amp.parent_cn_name,
        amp.period_year,
        amp.group_level,
        CONCAT(ROUND( SUM ( amp.annual_amp*100 ), 1 ),'%') AS annual_amp,
        amp.group_code AS group_code,
        amp.group_cn_name AS group_cn_name,
        CONCAT(ROUND(SUM ( weight.weight_rate*100 ),1),'%') AS weight_rate,
        CONCAT (amp.group_code, ' ', amp.group_cn_name ) AS groupCodeAndName,
        status.status_code
        from fin_dm_opt_foi.DM_FCST_ICT_${combTablePreFix}_BASE_CUS_ANNL_AMP_T amp
        left join fin_dm_opt_foi.DM_FCST_ICT_${combTablePreFix}_BASE_CUS_ANNL_WEIGHT_T weight
        on amp.view_flag = weight.view_flag and amp.group_code = weight.group_code
        and nvl(amp.parent_code,'snull') = nvl(weight.parent_code,'snull')
        and nvl(amp.parent_level,'snull') = nvl(weight.parent_level,'snull')
        and amp.custom_id = weight.custom_id
        and amp.version_id =weight.version_id and amp.period_year = weight.period_year
        and amp.oversea_flag = weight.oversea_flag
        and amp.bg_code = weight.bg_code
        and amp.granularity_type = weight.granularity_type
        left join fin_dm_opt_foi.DM_FCST_ICT_${combTablePreFix}_BASE_CUS_ANNL_STATUS_T status
        on amp.group_code = status.group_code and amp.group_level = status.group_level
        and nvl(amp.parent_code,'snull') = nvl(status.parent_code,'snull')
        and nvl(amp.parent_level,'snull') = nvl(status.parent_level,'snull')
        and amp.custom_id = status.custom_id
        and amp.period_year = status.period_year
        and amp.version_id = status.version_id
        and amp.view_flag = status.view_flag
        and amp.bg_code = status.bg_code
        and amp.oversea_flag = status.oversea_flag
        and amp.granularity_type = status.granularity_type
        where amp.del_flag = 'N'
        <if test='viewFlag != null and viewFlag != "" and viewFlag == "PROD_SPART"'>
            and amp.group_level = 'SPART'
        </if>
        <if test='viewFlag != null and viewFlag != "" and viewFlag == "DIMENSION"'>
            and amp.group_level = 'SUB_DETAIL'
        </if>
        <if test='customId != null'>
            and amp.custom_id = #{customId}
        </if>
        <if test='granularityType != null and granularityType != ""'>
            and amp.granularity_type = #{granularityType,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag != null and overseaFlag != ""'>
            and amp.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and amp.bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and amp.region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and amp.repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag != null and mainFlag != ""'>
            and amp.main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes != null and codeAttributes != ""'>
            and amp.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='parentLevel != null and parentLevel != ""'>
            and amp.parent_level = #{parentLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and amp.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and amp.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='periodYear != null and periodYear !=""'>
            and amp.period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='parentCodeList != null and parentCodeList.size() > 0'>
            <foreach collection='parentCodeList' item="code" open="AND amp.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupCodeOrder != null and groupCodeOrder != ""'>
            <foreach collection="groupCodeOrder.split(',')" item="item" open="and amp.group_code IN (" close=")"
                     index="index" separator=",">
                #{item}
            </foreach>
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND amp.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        GROUP BY amp.parent_level,amp.custom_cn_name,amp.custom_id,amp.parent_code,amp.parent_cn_name,
        amp.group_code,amp.group_cn_name,amp.group_level,groupCodeAndName,amp.period_year,status.status_code
        order by locate(amp.group_code, #{groupCodeOrder}), weight_rate desc
    </select>

    <select id="distributeCustomChart" resultMap="annualResultMap">
        select distinct
        group_level,group_code,group_cn_name,parent_level,parent_code,parent_cn_name,
        period_year,ROUND(rmb_cost_amt/10000,1) rmb_cost_amt
        from fin_dm_opt_foi.DM_FCST_ICT_${costType}_BASE_CUS_ANNL_COST_T
        where del_flag = 'N'
        <if test='groupLevel != null and groupLevel != ""'>
            and group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='customId != null'>
            AND custom_id = #{customId}
        </if>
        <if test='granularityType != null and granularityType != ""'>
            AND granularity_type = #{granularityType}
        </if>
        <if test='overseaFlag != null and overseaFlag != ""'>
            and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag != null and mainFlag != ""'>
            and main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes != null and codeAttributes != ""'>
            and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='groupCodeList != null and groupCodeList.size() > 0'>
            <foreach collection='groupCodeList' item="code" open="AND group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='periodYear != null and periodYear != ""'>
            and period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        order by period_year,rmb_cost_amt desc
    </select>


    <select id="industryCombCustomPage" resultMap="annualResultMap">
        select
        custom_cn_name,custom_id,parent_level,parent_code,parent_cn_name,period_year,annual_amp,group_code,group_cn_name,group_level,
        weight_rate, status_code, annual_amp * weight_rate/100 as weightAnnualAmpPercent
        from
        (select distinct
        amp.custom_cn_name,amp.custom_id,amp.parent_code,amp.parent_level,amp.group_level,amp.parent_cn_name,amp.period_year,
        ROUND(amp.annual_amp*100,1) as annual_amp,amp.group_code,amp.group_cn_name,
        <choose>
            <when test='annualAnalysisVO.isMultipleSelect == true'>
                weight.absolute_weight*100 as weight_rate,
            </when>
            <otherwise>
                weight.weight_rate*100 as weight_rate,
            </otherwise>
        </choose>
        status.status_code
        from fin_dm_opt_foi.DM_FCST_ICT_${annualAnalysisVO.combTablePreFix}_BASE_CUS_ANNL_AMP_T amp
        left join fin_dm_opt_foi.DM_FCST_ICT_${annualAnalysisVO.combTablePreFix}_BASE_CUS_ANNL_WEIGHT_T weight
        on amp.view_flag = weight.view_flag and amp.group_code = weight.group_code
        and nvl(amp.parent_code,'snull') = nvl(weight.parent_code,'snull')
        and nvl(amp.parent_level,'snull') = nvl(weight.parent_level,'snull')
        and amp.custom_id = weight.custom_id
        and amp.version_id =weight.version_id and amp.period_year = weight.period_year
        and amp.oversea_flag = weight.oversea_flag
        and amp.bg_code = weight.bg_code
        and amp.granularity_type = weight.granularity_type
        left join fin_dm_opt_foi.DM_FCST_ICT_${annualAnalysisVO.combTablePreFix}_BASE_CUS_ANNL_STATUS_T status
        on amp.group_code = status.group_code and amp.group_level = status.group_level
        and nvl(amp.parent_code,'snull') = nvl(status.parent_code,'snull')
        and nvl(amp.parent_level,'snull') = nvl(status.parent_level,'snull')
        and amp.custom_id = status.custom_id
        and amp.period_year = status.period_year
        and amp.version_id = status.version_id
        and amp.view_flag = status.view_flag
        and amp.bg_code = status.bg_code
        and amp.oversea_flag = status.oversea_flag
        and amp.granularity_type = status.granularity_type
        where amp.del_flag = 'N'
        <if test='annualAnalysisVO.viewFlag != null and annualAnalysisVO.viewFlag != "" and annualAnalysisVO.viewFlag == "PROD_SPART"'>
            and amp.group_level = 'SPART'
        </if>
        <if test='annualAnalysisVO.viewFlag != null and annualAnalysisVO.viewFlag != "" and annualAnalysisVO.viewFlag == "DIMENSION"'>
            and amp.group_level = 'SUB_DETAIL'
        </if>
        <if test='annualAnalysisVO.customId != null'>
            and amp.custom_id = #{annualAnalysisVO.customId}
        </if>
        <if test='annualAnalysisVO.granularityType != null and annualAnalysisVO.granularityType != ""'>
            and amp.granularity_type = #{annualAnalysisVO.granularityType,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.overseaFlag != null and annualAnalysisVO.overseaFlag != ""'>
            and amp.oversea_flag = #{annualAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.bgCode != null and annualAnalysisVO.bgCode != ""'>
            and amp.bg_code = #{annualAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.regionCode != null and annualAnalysisVO.regionCode != ""'>
            and amp.region_code = #{annualAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.repofficeCode != null and annualAnalysisVO.repofficeCode != ""'>
            and amp.repoffice_code = #{annualAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.mainFlag != null and annualAnalysisVO.mainFlag != ""'>
            and amp.main_flag = #{annualAnalysisVO.mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.codeAttributes != null and annualAnalysisVO.codeAttributes != ""'>
            and amp.code_attributes = #{annualAnalysisVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.parentLevel != null and annualAnalysisVO.parentLevel != ""'>
            and amp.parent_level = #{annualAnalysisVO.parentLevel,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.viewFlag != null and annualAnalysisVO.viewFlag != ""'>
            and amp.view_flag = #{annualAnalysisVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.versionId != null'>
            and amp.version_id = #{annualAnalysisVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='annualAnalysisVO.periodYear != null and annualAnalysisVO.periodYear !=""'>
            and amp.period_year = #{annualAnalysisVO.periodYear,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.parentCodeList != null and annualAnalysisVO.parentCodeList.size() > 0'>
            <foreach collection='annualAnalysisVO.parentCodeList' item="code" open="AND amp.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='annualAnalysisVO.year != null and annualAnalysisVO.year !=""'>
            and amp.period_year = #{annualAnalysisVO.year,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.yearList != null and annualAnalysisVO.yearList.size() > 0'>
            <foreach collection='annualAnalysisVO.yearList' item="year" open="AND amp.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        )
        order by period_year,weight_rate desc,group_cn_name
        LIMIT #{pageVO.pageSize} OFFSET #{pageVO.startIndex} - 1
    </select>

    <select id="industryCombCustomPageCount" resultType="int">
        select count(1)
        from
        (select distinct
        amp.custom_cn_name,amp.custom_id,amp.parent_code,amp.parent_level,amp.group_level,amp.parent_cn_name,amp.period_year,
        ROUND(amp.annual_amp*100,1) as annual_amp,amp.group_code,amp.group_cn_name,
        <choose>
            <when test='annualAnalysisVO.isMultipleSelect == true'>
                weight.absolute_weight*100 as weight_rate,
            </when>
            <otherwise>
                weight.weight_rate*100 as weight_rate,
            </otherwise>
        </choose>
        status.status_code
        from fin_dm_opt_foi.DM_FCST_ICT_${annualAnalysisVO.combTablePreFix}_BASE_CUS_ANNL_AMP_T amp
        left join fin_dm_opt_foi.DM_FCST_ICT_${annualAnalysisVO.combTablePreFix}_BASE_CUS_ANNL_WEIGHT_T weight
        on amp.view_flag = weight.view_flag and amp.group_code = weight.group_code
        and nvl(amp.parent_code,'snull') = nvl(weight.parent_code,'snull')
        and nvl(amp.parent_level,'snull') = nvl(weight.parent_level,'snull')
        and amp.custom_id = weight.custom_id
        and amp.version_id =weight.version_id and amp.period_year = weight.period_year
        and amp.oversea_flag = weight.oversea_flag
        and amp.bg_code = weight.bg_code
        and amp.granularity_type = weight.granularity_type
        left join fin_dm_opt_foi.DM_FCST_ICT_${annualAnalysisVO.combTablePreFix}_BASE_CUS_ANNL_STATUS_T status
        on amp.group_code = status.group_code and amp.group_level = status.group_level
        and nvl(amp.parent_code,'snull') = nvl(status.parent_code,'snull')
        and nvl(amp.parent_level,'snull') = nvl(status.parent_level,'snull')
        and amp.custom_id = status.custom_id
        and amp.period_year = status.period_year
        and amp.version_id = status.version_id
        and amp.view_flag = status.view_flag
        and amp.bg_code = status.bg_code
        and amp.oversea_flag = status.oversea_flag
        and amp.granularity_type = status.granularity_type
        where amp.del_flag = 'N'
        <if test='annualAnalysisVO.viewFlag != null and annualAnalysisVO.viewFlag != "" and annualAnalysisVO.viewFlag == "PROD_SPART"'>
            and amp.group_level = 'SPART'
        </if>
        <if test='annualAnalysisVO.viewFlag != null and annualAnalysisVO.viewFlag != "" and annualAnalysisVO.viewFlag == "DIMENSION"'>
            and amp.group_level = 'SUB_DETAIL'
        </if>
        <if test='annualAnalysisVO.customId != null'>
            and amp.custom_id = #{annualAnalysisVO.customId}
        </if>
        <if test='annualAnalysisVO.granularityType != null and annualAnalysisVO.granularityType != ""'>
            and amp.granularity_type = #{annualAnalysisVO.granularityType,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.overseaFlag != null and annualAnalysisVO.overseaFlag != ""'>
            and amp.oversea_flag = #{annualAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.bgCode != null and annualAnalysisVO.bgCode != ""'>
            and amp.bg_code = #{annualAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.regionCode != null and annualAnalysisVO.regionCode != ""'>
            and amp.region_code = #{annualAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.repofficeCode != null and annualAnalysisVO.repofficeCode != ""'>
            and amp.repoffice_code = #{annualAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.mainFlag != null and annualAnalysisVO.mainFlag != ""'>
            and amp.main_flag = #{annualAnalysisVO.mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.codeAttributes != null and annualAnalysisVO.codeAttributes != ""'>
            and amp.code_attributes = #{annualAnalysisVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.parentLevel != null and annualAnalysisVO.parentLevel != ""'>
            and amp.parent_level = #{annualAnalysisVO.parentLevel,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.viewFlag != null and annualAnalysisVO.viewFlag != ""'>
            and amp.view_flag = #{annualAnalysisVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.versionId != null'>
            and amp.version_id = #{annualAnalysisVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='annualAnalysisVO.periodYear != null and annualAnalysisVO.periodYear !=""'>
            and amp.period_year = #{annualAnalysisVO.periodYear,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.parentCodeList != null and annualAnalysisVO.parentCodeList.size() > 0'>
            <foreach collection='annualAnalysisVO.parentCodeList' item="code" open="AND amp.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='annualAnalysisVO.year != null and annualAnalysisVO.year !=""'>
            and amp.period_year = #{annualAnalysisVO.year,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.yearList != null and annualAnalysisVO.yearList.size() > 0'>
            <foreach collection='annualAnalysisVO.yearList' item="year" open="AND amp.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        )
    </select>

    <select id="industryCombCustomExcel" resultMap="annualResultMap">
        select
        custom_cn_name,custom_id,parent_level,parent_code,parent_cn_name,period_year,annual_amp,group_code,group_cn_name,group_level,
        weight_rate, status_code, annual_amp * weight_rate/100 as weightAnnualAmpPercent
        from
        (select distinct
        amp.custom_cn_name,amp.custom_id,amp.parent_code,amp.parent_level,amp.parent_cn_name,amp.period_year,
        ROUND(amp.annual_amp*100,1) as annual_amp,amp.group_code,amp.group_cn_name,amp.group_level,
        <choose>
            <when test='annualAnalysisVO.isMultipleSelect == true'>
                weight.absolute_weight*100 as weight_rate,
            </when>
            <otherwise>
                weight.weight_rate*100 as weight_rate,
            </otherwise>
        </choose>
        status.status_code
        from fin_dm_opt_foi.DM_FCST_ICT_${annualAnalysisVO.combTablePreFix}_BASE_CUS_ANNL_AMP_T amp
        left join fin_dm_opt_foi.DM_FCST_ICT_${annualAnalysisVO.combTablePreFix}_BASE_CUS_ANNL_WEIGHT_T weight
        on amp.view_flag = weight.view_flag and amp.group_code = weight.group_code
        and nvl(amp.parent_code,'snull') = nvl(weight.parent_code,'snull')
        and nvl(amp.parent_level,'snull') = nvl(weight.parent_level,'snull')
        and amp.custom_id = weight.custom_id
        and amp.version_id =weight.version_id and amp.period_year = weight.period_year
        and amp.oversea_flag = weight.oversea_flag
        and amp.bg_code = weight.bg_code
        and amp.granularity_type = weight.granularity_type
        left join fin_dm_opt_foi.DM_FCST_ICT_${annualAnalysisVO.combTablePreFix}_BASE_CUS_ANNL_STATUS_T status
        on amp.group_code = status.group_code and amp.group_level = status.group_level
        and nvl(amp.parent_code,'snull') = nvl(status.parent_code,'snull')
        and nvl(amp.parent_level,'snull') = nvl(status.parent_level,'snull')
        and amp.custom_id = status.custom_id
        and amp.period_year = status.period_year
        and amp.version_id = status.version_id
        and amp.view_flag = status.view_flag
        and amp.bg_code = status.bg_code
        and amp.oversea_flag = status.oversea_flag
        and amp.granularity_type = status.granularity_type
        where amp.del_flag = 'N'
        <if test='annualAnalysisVO.viewFlag != null and annualAnalysisVO.viewFlag != "" and annualAnalysisVO.viewFlag == "PROD_SPART"'>
            and amp.group_level = 'SPART'
        </if>
        <if test='annualAnalysisVO.viewFlag != null and annualAnalysisVO.viewFlag != "" and annualAnalysisVO.viewFlag == "DIMENSION"'>
            and amp.group_level = 'SUB_DETAIL'
        </if>
        <if test='annualAnalysisVO.customId != null'>
            and amp.custom_id = #{annualAnalysisVO.customId}
        </if>
        <if test='annualAnalysisVO.granularityType != null and annualAnalysisVO.granularityType != ""'>
            and amp.granularity_type = #{annualAnalysisVO.granularityType,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.overseaFlag != null and annualAnalysisVO.overseaFlag != ""'>
            and amp.oversea_flag = #{annualAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.bgCode != null and annualAnalysisVO.bgCode != ""'>
            and amp.bg_code = #{annualAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.regionCode != null and annualAnalysisVO.regionCode != ""'>
            and amp.region_code = #{annualAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.repofficeCode != null and annualAnalysisVO.repofficeCode != ""'>
            and amp.repoffice_code = #{annualAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.mainFlag != null and annualAnalysisVO.mainFlag != ""'>
            and amp.main_flag = #{annualAnalysisVO.mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.codeAttributes != null and annualAnalysisVO.codeAttributes != ""'>
            and amp.code_attributes = #{annualAnalysisVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.parentLevel != null and annualAnalysisVO.parentLevel != ""'>
            and amp.parent_level = #{annualAnalysisVO.parentLevel,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.viewFlag != null and annualAnalysisVO.viewFlag != ""'>
            and amp.view_flag = #{annualAnalysisVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.versionId != null'>
            and amp.version_id = #{annualAnalysisVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='annualAnalysisVO.periodYear != null and annualAnalysisVO.periodYear !=""'>
            and amp.period_year = #{annualAnalysisVO.periodYear,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.parentCodeList != null and annualAnalysisVO.parentCodeList.size() > 0'>
            <foreach collection='annualAnalysisVO.parentCodeList' item="code" open="AND amp.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='annualAnalysisVO.year != null and annualAnalysisVO.year !=""'>
            and amp.period_year = #{annualAnalysisVO.year,jdbcType=VARCHAR}
        </if>
        <if test='annualAnalysisVO.yearList != null and annualAnalysisVO.yearList.size() > 0'>
            <foreach collection='annualAnalysisVO.yearList' item="year" open="AND amp.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        )
        order by period_year,weight_rate desc,group_cn_name
    </select>

</mapper>