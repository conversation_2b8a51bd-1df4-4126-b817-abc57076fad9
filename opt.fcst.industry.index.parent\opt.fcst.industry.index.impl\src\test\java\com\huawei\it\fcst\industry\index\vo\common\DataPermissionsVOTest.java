/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.common;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

import java.util.HashSet;
import java.util.Set;

/**
 * DataPermissionsVOTest Class
 *
 * <AUTHOR>
 * @since 2023/5/15
 */
public class DataPermissionsVOTest extends BaseVOCoverUtilsTest<DataPermissionsVO> {

    @Override
    protected Class<DataPermissionsVO> getTClass() {
        return DataPermissionsVO.class;
    }

    @Test
    public void testMethod() {
        DataPermissionsVO dmFocActualCostVO = new DataPermissionsVO();
        dmFocActualCostVO.getRoleId();
        dmFocActualCostVO.getRoleName();
        dmFocActualCostVO.getLv2DimensionSet();
        Set<String> lv0DimensionSet = new HashSet<>();
        lv0DimensionSet.add("lv0");
        DataPermissionsVO.builder()
            .lv0DimensionSet(lv0DimensionSet)
            .lv1DimensionSet(lv0DimensionSet)
            .lv2DimensionSet(lv0DimensionSet)
            .roleId(10)
            .roleName("role")
            .build();
        Assert.assertNotNull(dmFocActualCostVO);
    }
}