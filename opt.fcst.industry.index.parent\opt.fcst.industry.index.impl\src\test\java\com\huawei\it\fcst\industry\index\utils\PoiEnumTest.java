/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.utils;

import static org.mockito.ArgumentMatchers.any;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.when;

import com.huawei.it.fcst.industry.index.constant.CommonConstant;
import com.huawei.it.fcst.industry.index.vo.common.AbstractExcelTitleVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFoiImpExpRecordVO;
import com.huawei.it.fcst.industry.index.vo.common.ExportExcelVo;
import com.huawei.it.fcst.industry.index.vo.common.HeaderVo;
import com.huawei.it.fcst.industry.index.vo.common.LeafExcelTitleVO;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.security.UserVO;

import org.apache.poi.ss.usermodel.CellType;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * PoiEnumTest Class
 *
 * <AUTHOR>
 * @since 2023/5/11
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest( {FileProcessUtis.class})
public class PoiEnumTest {
    private static final Logger LOGGER = LoggerFactory.getLogger(PoiEnum.class);

    @Before
    public void before() {
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);
    }

    @Test
    public void exportExcel() throws Exception {
        OutputStream os = new FileOutputStream("file.txt");

        List<AbstractExcelTitleVO> formInfoVo = new ArrayList<>();
        LeafExcelTitleVO columnCnVersion =
                new LeafExcelTitleVO(
                        "版本号",
                        CommonConstant.WIDTH,
                        true,
                        "version_num",
                        "version_num",
                        CellType.STRING,
                        false);
        formInfoVo.add(columnCnVersion);
        LeafExcelTitleVO columnCnVersionName =
                new LeafExcelTitleVO(
                        "版本", CommonConstant.WIDTH, true, "version_name", "version_name", CellType.STRING, false);
        formInfoVo.add(columnCnVersionName);

        List<Map> industryList = new ArrayList<>();
        Map map = new HashMap();
        map.put("periodYear", "2023");
        map.put("groupCnName", "code1");
        map.put("weightRate", "1.0");
        map.put("annualAmp", "2.0");
        map.put("weightAnnualAmpPercent", "2.0");
        industryList.add(map);
        LeafExcelTitleVO column;
        List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
        Set<String> titles = new LinkedHashSet<>();
        for (HeaderVo header : CommonConstant.INDUSTRY_AMP_HEADER) {
            column =
                    new LeafExcelTitleVO(
                            header.getTitle(),
                            header.getWidth(),
                            true,
                            header.getField(),
                            header.getField(),
                            header.getDataType(),
                            header.getIsEditable());
            titleVoList.add(column);
            titles.add(header.getField());
        }

        List<ExportExcelVo> exportExcelVoList = new ArrayList<>();
        ExportExcelVo exportExcelVo =
                ExportExcelVo.builder()
                        .formInfoVo(formInfoVo)
                        .titleVoList(titleVoList)
                        .list(industryList)
                        .sheetName("产业成本")
                        .mergeCell(false)
                        .build();
        exportExcelVoList.add(exportExcelVo);
        for (ExportExcelVo exportExcel : exportExcelVoList) {
            List<AbstractExcelTitleVO> leafExcelTitleVO = new ArrayList<>();
            int titleRowCount = adjustTitleVoList(exportExcel.getTitleVoList(), leafExcelTitleVO);;
            List<AbstractExcelTitleVO> selectedLeafExcelTitleVO = leafExcelTitleVO;
            if (exportExcel.getMergeCell()) {
                selectedLeafExcelTitleVO = leafExcelTitleVO.stream()
                        .limit(leafExcelTitleVO.size() - 1)
                        .collect(Collectors.toList());
            }
            exportExcel.setSelectedLeafExcelTitleVO(selectedLeafExcelTitleVO);
            exportExcel.setTitleRowCount(titleRowCount);
        }
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        DmFoiImpExpRecordVO dmFoiImpExpRecordVO = PoiEnum.exportExcel(os, exportExcelVoList);
        Assert.assertNotNull(dmFoiImpExpRecordVO);
    }

    @Test
    public void exportExcel2T() throws Exception {
        OutputStream os = null;

        List<AbstractExcelTitleVO> formInfoVo = new ArrayList<>();
        LeafExcelTitleVO columnCnVersion =
                new LeafExcelTitleVO(
                        "版本号",
                        CommonConstant.WIDTH,
                        true,
                        "version_num",
                        "version_num",
                        CellType.STRING,
                        false);
        formInfoVo.add(columnCnVersion);
        LeafExcelTitleVO columnCnVersionName =
                new LeafExcelTitleVO(
                        "版本", CommonConstant.WIDTH, true, "version_name", "version_name", CellType.STRING, false);
        formInfoVo.add(columnCnVersionName);

        List<Map> industryList = new ArrayList<>();
        Map map = new HashMap();
        map.put("periodYear", "2023");
        map.put("groupCnName", "code1");
        map.put("weightRate", "1.0");
        map.put("annualAmp", "2.0");
        map.put("weightAnnualAmpPercent", "2.0");
        industryList.add(map);
        LeafExcelTitleVO column;
        List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
        Set<String> titles = new LinkedHashSet<>();

        List<HeaderVo> INDUSTRY_LV1_VIEW_HEADER = new LinkedList<>();

        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("年", "periodYear", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("重量级团队LV1", "groupCnName", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("权重", "weightRate", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("涨跌", "annualAmp", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("权重×涨跌", "weightAnnualAmpPercent", CellType.STRING, false, 12 * 480));

        for (HeaderVo header : INDUSTRY_LV1_VIEW_HEADER) {
            column =
                    new LeafExcelTitleVO(
                            header.getTitle(),
                            header.getWidth(),
                            true,
                            header.getField(),
                            header.getField(),
                            header.getDataType(),
                            header.getIsEditable());
            titleVoList.add(column);
            titles.add(header.getField());
        }

        List<ExportExcelVo> exportExcelVoList = new ArrayList<>();
        ExportExcelVo exportExcelVo =
                ExportExcelVo.builder()
                        .formInfoVo(formInfoVo)
                        .titleVoList(titleVoList)
                        .list(industryList)
                        .sheetName("产业成本/")
                        .mergeCell(false)
                        .build();
        exportExcelVoList.add(exportExcelVo);
        for (ExportExcelVo exportExcel : exportExcelVoList) {
            List<AbstractExcelTitleVO> leafExcelTitleVO = new ArrayList<>();
            int titleRowCount = adjustTitleVoList(exportExcel.getTitleVoList(), leafExcelTitleVO);;
            List<AbstractExcelTitleVO> selectedLeafExcelTitleVO = leafExcelTitleVO;
            if (exportExcel.getMergeCell()) {
                selectedLeafExcelTitleVO = leafExcelTitleVO.stream()
                        .limit(leafExcelTitleVO.size() - 1)
                        .collect(Collectors.toList());
            }
            exportExcel.setSelectedLeafExcelTitleVO(selectedLeafExcelTitleVO);
            exportExcel.setTitleRowCount(titleRowCount);
        }
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        DmFoiImpExpRecordVO dmFoiImpExpRecordVO = PoiEnum.exportExcel(os, exportExcelVoList);
        Assert.assertNotNull(dmFoiImpExpRecordVO);
    }

    @Test
    public void exportExcel3T() throws Exception {
        OutputStream os = null;

        List<AbstractExcelTitleVO> formInfoVo = new ArrayList<>();
        LeafExcelTitleVO columnCnVersion = new LeafExcelTitleVO("产业成本涨跌图", CommonConstant.WIDTH, true,
                "title", "title", CellType.STRING, true);
        formInfoVo.add(columnCnVersion);
        LeafExcelTitleVO columnCnVersionName = new LeafExcelTitleVO("基期", 14 * 580, true, "period",
                "period", CellType.STRING, true);
        formInfoVo.add(columnCnVersionName);

        List<Map> industryList = new ArrayList<>();
        Map map = new HashMap();
        map.put("actualMonth", "202302");
        map.put("groupCnName", "code1");
        map.put("weightRate", 1.0);
        map.put("annualAmp", "2.0");
        map.put("weightAnnualAmpPercent", "2.0");
        industryList.add(map);
        LeafExcelTitleVO column;
        List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
        Set<String> titles = new LinkedHashSet<>();
        List<HeaderVo> INDUSTRY_LV1_VIEW_HEADER = new LinkedList<>();
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("月", "actualMonth", CellType.STRING, true, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("重量级团队LV1", "groupCnName", CellType.STRING, true, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("权重", "weightRate", CellType.NUMERIC, true, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("涨跌", "annualAmp", CellType.STRING, true, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("权重×涨跌", "weightAnnualAmpPercent", CellType.STRING, true, 12 * 480));
        for (HeaderVo header : INDUSTRY_LV1_VIEW_HEADER) {
            column =
                    new LeafExcelTitleVO(
                            header.getTitle(),
                            header.getWidth(),
                            true,
                            header.getField(),
                            header.getField(),
                            header.getDataType(),
                            header.getIsEditable());
            titleVoList.add(column);
            titles.add(header.getField());
        }

        List<ExportExcelVo> exportExcelVoList = new ArrayList<>();
        ExportExcelVo exportExcelVo =
                ExportExcelVo.builder()
                        .formInfoVo(formInfoVo)
                        .titleVoList(titleVoList)
                        .list(industryList)
                        .sheetName("产业成本")
                        .mergeCell(true)
                        .build();
        exportExcelVoList.add(exportExcelVo);
        for (ExportExcelVo exportExcel : exportExcelVoList) {
            List<AbstractExcelTitleVO> leafExcelTitleVO = new ArrayList<>();
            int titleRowCount = adjustTitleVoList(exportExcel.getTitleVoList(), leafExcelTitleVO);;
            List<AbstractExcelTitleVO> selectedLeafExcelTitleVO = leafExcelTitleVO;
            if (exportExcel.getMergeCell()) {
                selectedLeafExcelTitleVO = leafExcelTitleVO.stream()
                        .limit(leafExcelTitleVO.size() - 1)
                        .collect(Collectors.toList());
            }
            exportExcel.setSelectedLeafExcelTitleVO(selectedLeafExcelTitleVO);
            exportExcel.setTitleRowCount(titleRowCount);
        }
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        DmFoiImpExpRecordVO dmFoiImpExpRecordVO = PoiEnum.exportExcel(os, exportExcelVoList);
        Assert.assertNotNull(dmFoiImpExpRecordVO);
    }

    @Test
    public void exportExcel4T() throws Exception {
        OutputStream os = null;

        List<AbstractExcelTitleVO> formInfoVo = new ArrayList<>();
        LeafExcelTitleVO columnCnVersion = new LeafExcelTitleVO("产业成本涨跌图", CommonConstant.WIDTH, true,
                "title", "title", CellType.STRING, true);
        formInfoVo.add(columnCnVersion);
        LeafExcelTitleVO columnCnVersionName = new LeafExcelTitleVO("视角：1", 14 * 580, true, "viewFlagValue",
                "viewFlagValue", CellType.STRING, true);
        formInfoVo.add(columnCnVersionName);

        List<Map> industryList = new ArrayList<>();
        Map map = new HashMap();
        map.put("periodYear", "2023");
        map.put("annualAmp", "2.0");

        industryList.add(map);
        LeafExcelTitleVO column;
        List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
        Set<String> titles = new LinkedHashSet<>();

        for (HeaderVo header : CommonConstant.INDUSTRY_AMP_HEADER) {
            column =
                    new LeafExcelTitleVO(
                            header.getTitle(),
                            header.getWidth(),
                            true,
                            header.getField(),
                            header.getField(),
                            header.getDataType(),
                            header.getIsEditable());
            titleVoList.add(column);
            titles.add(header.getField());
        }

        LeafExcelTitleVO columnActualMonth = new LeafExcelTitleVO("202305", CommonConstant.WIDTH, true,
                "actualMonth", "actualMonth", CellType.STRING, true);
        titleVoList.add(columnActualMonth);

        List<ExportExcelVo> exportExcelVoList = new ArrayList<>();
        ExportExcelVo exportExcelVo =
                ExportExcelVo.builder()
                        .formInfoVo(formInfoVo)
                        .titleVoList(titleVoList)
                        .list(industryList)
                        .sheetName("产业成本")
                        .mergeCell(true)
                        .build();
        exportExcelVoList.add(exportExcelVo);
        for (ExportExcelVo exportExcel : exportExcelVoList) {
            List<AbstractExcelTitleVO> leafExcelTitleVO = new ArrayList<>();
            int titleRowCount = adjustTitleVoList(exportExcel.getTitleVoList(), leafExcelTitleVO);;
            List<AbstractExcelTitleVO> selectedLeafExcelTitleVO = leafExcelTitleVO;
            if (exportExcel.getMergeCell()) {
                selectedLeafExcelTitleVO = leafExcelTitleVO.stream()
                        .limit(leafExcelTitleVO.size() - 1)
                        .collect(Collectors.toList());
            }
            exportExcel.setSelectedLeafExcelTitleVO(selectedLeafExcelTitleVO);
            exportExcel.setTitleRowCount(titleRowCount);
        }
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        DmFoiImpExpRecordVO dmFoiImpExpRecordVO = PoiEnum.exportExcel(os, exportExcelVoList);
        Assert.assertNotNull(dmFoiImpExpRecordVO);
    }

    @Test
    public void exportExcel5T() throws Exception {
        OutputStream os = null;

        List<AbstractExcelTitleVO> formInfoVo = new ArrayList<>();
        LeafExcelTitleVO columnCnVersion =
                new LeafExcelTitleVO(
                        "",
                        CommonConstant.WIDTH,
                        true,
                        "version",
                        "version",
                        CellType.STRING,
                        false);
        formInfoVo.add(columnCnVersion);
        LeafExcelTitleVO columnCnVersionName =
                new LeafExcelTitleVO(
                        "版本名称", CommonConstant.WIDTH, true, "versionName", "versionName", CellType.STRING, false);
        formInfoVo.add(columnCnVersionName);

        List<Map> industryList = new ArrayList<>();
        Map map = new HashMap();
        map.put("year", "2023");
        map.put("groupCnName", "code1");
        map.put("weightRate", 1.0);
        map.put("annualAmp", "1.1");
        map.put("teamCode", "lv2");
        map.put("category", "111");
        map.put("weightAnnualAmpPercent", "2.0");
        industryList.add(map);
        LeafExcelTitleVO column;
        List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
        Set<String> titles = new LinkedHashSet<>();

        List<HeaderVo> INDUSTRY_LV1_VIEW_HEADER = new LinkedList<>();

        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("年", "year", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("名称", "groupCnName", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("权重", "weightRate", CellType.NUMERIC, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("涨跌", "annualAmp", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("lv2", "teamCode", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("品类", "category", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("权重×涨跌", "weightAnnualAmpPercent", CellType.STRING, false, 12 * 480));

        for (HeaderVo header : INDUSTRY_LV1_VIEW_HEADER) {
            column =
                    new LeafExcelTitleVO(
                            header.getTitle(),
                            header.getWidth(),
                            true,
                            header.getField(),
                            header.getField(),
                            header.getDataType(),
                            header.getIsEditable());
            titleVoList.add(column);
            titles.add(header.getField());
        }

        List<ExportExcelVo> exportExcelVoList = new ArrayList<>();
        ExportExcelVo exportExcelVo =
                ExportExcelVo.builder()
                        .formInfoVo(formInfoVo)
                        .titleVoList(titleVoList)
                        .list(industryList)
                        .sheetName("产业成本/")
                        .mergeCell(false)
                        .build();
        exportExcelVoList.add(exportExcelVo);
        for (ExportExcelVo exportExcel : exportExcelVoList) {
            List<AbstractExcelTitleVO> leafExcelTitleVO = new ArrayList<>();
            int titleRowCount = adjustTitleVoList(exportExcel.getTitleVoList(), leafExcelTitleVO);;
            List<AbstractExcelTitleVO> selectedLeafExcelTitleVO = leafExcelTitleVO;
            if (exportExcel.getMergeCell()) {
                selectedLeafExcelTitleVO = leafExcelTitleVO.stream()
                        .limit(leafExcelTitleVO.size() - 1)
                        .collect(Collectors.toList());
            }
            exportExcel.setSelectedLeafExcelTitleVO(selectedLeafExcelTitleVO);
            exportExcel.setTitleRowCount(titleRowCount);
        }
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        DmFoiImpExpRecordVO dmFoiImpExpRecordVO = PoiEnum.exportExcel(os, exportExcelVoList);
        Assert.assertNotNull(dmFoiImpExpRecordVO);
    }

    @Test
    public void exportExcel6T() throws Exception {
        OutputStream os = null;
        List<AbstractExcelTitleVO> formInfoVo = new ArrayList<>();
        LeafExcelTitleVO columnCnVersion = new LeafExcelTitleVO("", CommonConstant.WIDTH, true,
                "caliberFlag", "caliberFlag", CellType.STRING, false);
        LeafExcelTitleVO columnCnVersion2 = new LeafExcelTitleVO("", CommonConstant.WIDTH, true,
                "viewFlagValue", "viewFlagValue", CellType.STRING, false);
        LeafExcelTitleVO columnCnVersion3 = new LeafExcelTitleVO("", CommonConstant.WIDTH, true,
                "actualMonth", "actualMonth", CellType.STRING, false);
        formInfoVo.add(columnCnVersion);
        formInfoVo.add(columnCnVersion2);
        formInfoVo.add(columnCnVersion3);
        LeafExcelTitleVO columnCnVersionName =
            new LeafExcelTitleVO(
                "版本名称", CommonConstant.WIDTH, true, "versionName", "versionName", CellType.STRING, false);
        formInfoVo.add(columnCnVersionName);

        List<Map> industryList = new ArrayList<>();
        Map map = new HashMap();
        map.put("year", "2023");
        map.put("groupCnName", "code1");
        map.put("weightRate", 1.0);
        map.put("annualAmp", "1.1");
        map.put("teamCode", "lv2");
        map.put("category", "111");
        map.put("weightAnnualAmpPercent", "2.0");
        Map map2 = new HashMap();
        map2.put("year", "2023");
        map2.put("groupCnName", "code1");
        map2.put("weightRate", 1.0);
        map2.put("annualAmp", "1.1");
        map2.put("teamCode", "lv3");
        map2.put("category", "111");
        map2.put("weightAnnualAmpPercent", "2.0");
        industryList.add(map);
        industryList.add(map2);
        LeafExcelTitleVO column;
        List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
        Set<String> titles = new LinkedHashSet<>();
        List<HeaderVo> INDUSTRY_LV1_VIEW_HEADER = new LinkedList<>();
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("年", "year", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("名称", "groupCnName", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("权重", "weightRate", CellType.NUMERIC, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("涨跌", "annualAmp", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("lv2", "teamCode", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("品类", "category", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("权重×涨跌", "weightAnnualAmpPercent", CellType.STRING, false, 12 * 480));
        for (HeaderVo header : INDUSTRY_LV1_VIEW_HEADER) {
            column =
                new LeafExcelTitleVO(
                    header.getTitle(),
                    header.getWidth(),
                    true,
                    header.getField(),
                    header.getField(),
                    header.getDataType(),
                    header.getIsEditable());
            titleVoList.add(column);
            titles.add(header.getField());
        }

        List<ExportExcelVo> exportExcelVoList = new ArrayList<>();
        ExportExcelVo exportExcelVo = ExportExcelVo.builder()
                .formInfoVo(formInfoVo)
                .titleVoList(titleVoList)
                .list(industryList)
                .sheetName("产业成本/")
                .mergeCell(false)
                .threeTitleList(formInfoVo)
                .fourTitleList(formInfoVo)
                .build();
        exportExcelVoList.add(exportExcelVo);
        for (ExportExcelVo exportExcel : exportExcelVoList) {
            List<AbstractExcelTitleVO> leafExcelTitleVO = new ArrayList<>();
            int titleRowCount = adjustTitleVoList(exportExcel.getTitleVoList(), leafExcelTitleVO);;
            List<AbstractExcelTitleVO> selectedLeafExcelTitleVO = leafExcelTitleVO;
            if (exportExcel.getMergeCell()) {
                selectedLeafExcelTitleVO = leafExcelTitleVO.stream()
                    .limit(leafExcelTitleVO.size() - 1)
                    .collect(Collectors.toList());
            }
            exportExcel.setSelectedLeafExcelTitleVO(selectedLeafExcelTitleVO);
            exportExcel.setTitleRowCount(titleRowCount);
        }
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        DmFoiImpExpRecordVO dmFoiImpExpRecordVO = PoiEnum.exportExcel(os, exportExcelVoList);
        Assert.assertNotNull(dmFoiImpExpRecordVO);
    }

    @Test
    public void exportExcel7T() throws Exception {
        OutputStream os = null;
        List<AbstractExcelTitleVO> formInfoVo = new ArrayList<>();
        LeafExcelTitleVO columnCnVersion =
            new LeafExcelTitleVO(
                "",
                CommonConstant.WIDTH,
                true,
                "version",
                "version",
                CellType.STRING,
                false);
        formInfoVo.add(columnCnVersion);
        LeafExcelTitleVO columnCnVersionName =
            new LeafExcelTitleVO(
                "版本名称", CommonConstant.WIDTH, null, "versionName", "versionName", CellType.STRING, false);
        formInfoVo.add(columnCnVersionName);

        List<Map> industryList = new ArrayList<>();
        Map map = new HashMap();
        map.put("year", "2023");
        map.put("groupCnName", "code1");
        map.put("weightRate", 1.0);
        map.put("annualAmp", "1.1");
        map.put("teamCode", "lv2");
        map.put("category", "111");
        map.put("weightAnnualAmpPercent", "2.0");
        industryList.add(map);
        LeafExcelTitleVO column;
        List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
        Set<String> titles = new LinkedHashSet<>();

        List<HeaderVo> INDUSTRY_LV1_VIEW_HEADER = new LinkedList<>();

        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("年", "year", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("颗粒度", "granule", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("名称", "groupCnName", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("权重", "weightRate", CellType.NUMERIC, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("涨跌", "annualAmp", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("lv2", "teamCode", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("品类", "category", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("权重×涨跌", "weightAnnualAmpPercent", CellType.STRING, false, 12 * 480));

        for (HeaderVo header : INDUSTRY_LV1_VIEW_HEADER) {
            column =
                new LeafExcelTitleVO(
                    header.getTitle(),
                    header.getWidth(),
                    true,
                    header.getField(),
                    header.getField(),
                    header.getDataType(),
                    header.getIsEditable());
            titleVoList.add(column);
            titles.add(header.getField());
        }

        List<ExportExcelVo> exportExcelVoList = new ArrayList<>();
        ExportExcelVo exportExcelVo =
            ExportExcelVo.builder()
                .formInfoVo(formInfoVo)
                .titleVoList(titleVoList)
                .list(industryList)
                .sheetName("产业成本/")
                .mergeCell(true)
                .build();
        exportExcelVoList.add(exportExcelVo);
        for (ExportExcelVo exportExcel : exportExcelVoList) {
            List<AbstractExcelTitleVO> leafExcelTitleVO = new ArrayList<>();
            int titleRowCount = adjustTitleVoList(exportExcel.getTitleVoList(), leafExcelTitleVO);;
            List<AbstractExcelTitleVO> selectedLeafExcelTitleVO = leafExcelTitleVO;
            if (exportExcel.getMergeCell()) {
                selectedLeafExcelTitleVO = leafExcelTitleVO.stream()
                    .limit(leafExcelTitleVO.size() - 1)
                    .collect(Collectors.toList());
            }
            exportExcel.setSelectedLeafExcelTitleVO(selectedLeafExcelTitleVO);
            exportExcel.setTitleRowCount(titleRowCount);
        }
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        DmFoiImpExpRecordVO dmFoiImpExpRecordVO = PoiEnum.exportExcel(os, exportExcelVoList);
        Assert.assertNotNull(dmFoiImpExpRecordVO);
    }

    @Test
    public void exportExcel8T() throws Exception {
        OutputStream os = null;

        List<AbstractExcelTitleVO> formInfoVo = new ArrayList<>();
        LeafExcelTitleVO columnCnVersion =
            new LeafExcelTitleVO(
                "",
                CommonConstant.WIDTH,
                true,
                "overSeaFlagValue",
                "overSeaFlagValue",
                CellType.STRING,
                false);
        formInfoVo.add(columnCnVersion);
        LeafExcelTitleVO columnCnVersionName =
            new LeafExcelTitleVO(
                "版本名称", CommonConstant.WIDTH, null, "versionName", "versionName", CellType.STRING, false);
        formInfoVo.add(columnCnVersionName);

        List<Map> industryList = new ArrayList<>();
        Map map = new HashMap();
        map.put("year", "2023");
        map.put("groupCnName", "code1");
        map.put("weightRate", 1.0);
        map.put("annualAmp", "1.1");
        map.put("teamCode", "lv2");
        map.put("category", "111");
        map.put("weightAnnualAmpPercent", "2.0");
        industryList.add(map);
        LeafExcelTitleVO column;
        List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
        Set<String> titles = new LinkedHashSet<>();

        List<HeaderVo> INDUSTRY_LV1_VIEW_HEADER = new LinkedList<>();

        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("年", "year", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("颗粒度", "granule", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("名称", "groupCnName", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("权重", "weightRate", CellType.NUMERIC, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("涨跌", "annualAmp", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("lv2", "teamCode", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("品类", "category", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("权重×涨跌", "weightAnnualAmpPercent", CellType.STRING, false, 12 * 480));

        for (HeaderVo header : INDUSTRY_LV1_VIEW_HEADER) {
            column =
                new LeafExcelTitleVO(
                    header.getTitle(),
                    header.getWidth(),
                    true,
                    header.getField(),
                    header.getField(),
                    header.getDataType(),
                    header.getIsEditable());
            titleVoList.add(column);
            titles.add(header.getField());
        }

        List<ExportExcelVo> exportExcelVoList = new ArrayList<>();
        ExportExcelVo exportExcelVo =
            ExportExcelVo.builder()
                .formInfoVo(formInfoVo)
                .titleVoList(titleVoList)
                .list(industryList)
                .sheetName("产业成本/")
                .mergeCell(true)
                .threeTitleList(formInfoVo)
                .build();
        exportExcelVoList.add(exportExcelVo);
        for (ExportExcelVo exportExcel : exportExcelVoList) {
            List<AbstractExcelTitleVO> leafExcelTitleVO = new ArrayList<>();
            int titleRowCount = adjustTitleVoList(exportExcel.getTitleVoList(), leafExcelTitleVO);;
            List<AbstractExcelTitleVO> selectedLeafExcelTitleVO = leafExcelTitleVO;
            if (exportExcel.getMergeCell()) {
                selectedLeafExcelTitleVO = leafExcelTitleVO.stream()
                    .limit(leafExcelTitleVO.size() - 1)
                    .collect(Collectors.toList());
            }
            exportExcel.setSelectedLeafExcelTitleVO(selectedLeafExcelTitleVO);
            exportExcel.setTitleRowCount(titleRowCount);
        }
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        DmFoiImpExpRecordVO dmFoiImpExpRecordVO = PoiEnum.exportExcel(os, exportExcelVoList);
        Assert.assertNotNull(dmFoiImpExpRecordVO);
    }

    @Test
    public void exportExcel9T() throws Exception {
        OutputStream os = null;
        List<AbstractExcelTitleVO> formInfoVo = new ArrayList<>();
        LeafExcelTitleVO columnCnVersion = new LeafExcelTitleVO("", CommonConstant.WIDTH, true,
                "overSeaFlagValue", "overSeaFlagValue", CellType.STRING, false);
        formInfoVo.add(columnCnVersion);
        LeafExcelTitleVO columnCnVersionName = new LeafExcelTitleVO("版本名称", CommonConstant.WIDTH,
                null, "bgFlagValue", "bgFlagValue", CellType.STRING, false);
        formInfoVo.add(columnCnVersionName);
        List<Map> industryList = new ArrayList<>();
        Map map = new HashMap();
        map.put("year", "2023");
        map.put("groupCnName", "code1");
        map.put("weightRate", 1.0);
        map.put("annualAmp", "1.1");
        map.put("teamCode", "lv2");
        map.put("category", "111");
        map.put("weightAnnualAmpPercent", "2.0");
        industryList.add(map);
        LeafExcelTitleVO column;
        List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
        Set<String> titles = new LinkedHashSet<>();

        List<HeaderVo> INDUSTRY_LV1_VIEW_HEADER = new LinkedList<>();
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("年", "year", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("颗粒度", "granule", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("名称", "groupCnName", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("权重", "weightRate", CellType.NUMERIC, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("涨跌", "annualAmp", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("lv2", "teamCode", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("品类", "category", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("权重×涨跌", "weightAnnualAmpPercent", CellType.STRING, false, 12 * 480));

        for (HeaderVo header : INDUSTRY_LV1_VIEW_HEADER) {
            column = new LeafExcelTitleVO(header.getTitle(), header.getWidth(), true, header.getField(),
                    header.getField(), header.getDataType(), header.getIsEditable());
            titleVoList.add(column);
            titles.add(header.getField());
        }

        List<ExportExcelVo> exportExcelVoList = new ArrayList<>();
        ExportExcelVo exportExcelVo = ExportExcelVo.builder().formInfoVo(formInfoVo).titleVoList(titleVoList)
                .list(industryList).sheetName("产业成本/").mergeCell(true).threeTitleList(formInfoVo).fourTitleList(formInfoVo).build();
        exportExcelVoList.add(exportExcelVo);
        for (ExportExcelVo exportExcel : exportExcelVoList) {
            List<AbstractExcelTitleVO> leafExcelTitleVO = new ArrayList<>();
            int titleRowCount = adjustTitleVoList(exportExcel.getTitleVoList(), leafExcelTitleVO);;
            List<AbstractExcelTitleVO> selectedLeafExcelTitleVO = leafExcelTitleVO;
            if (exportExcel.getMergeCell()) {
                selectedLeafExcelTitleVO = leafExcelTitleVO.stream()
                    .limit(leafExcelTitleVO.size() - 1)
                    .collect(Collectors.toList());
            }
            List<AbstractExcelTitleVO> titleVoList1 = exportExcel.getTitleVoList();
            for (AbstractExcelTitleVO abstractExcelTitleVO : titleVoList1) {
                if ("年".equals(abstractExcelTitleVO.getValue())){
                    abstractExcelTitleVO.setSelected(false);
                }
                if ("品类".equals(abstractExcelTitleVO.getValue())){
                    abstractExcelTitleVO.setSelected(null);
                }
                if ("名称".equals(abstractExcelTitleVO.getValue())){
                    abstractExcelTitleVO.setX2(2);
                }
                if ("颗粒度".equals(abstractExcelTitleVO.getValue())){
                    abstractExcelTitleVO.setY2(2);
                }
                if ("权重".equals(abstractExcelTitleVO.getValue())){
                    abstractExcelTitleVO.setY2(2);
                    abstractExcelTitleVO.setX2(2);
                }
            }

            exportExcel.setSelectedLeafExcelTitleVO(selectedLeafExcelTitleVO);
            exportExcel.setTitleRowCount(titleRowCount);
            exportExcel.setTitleRowCount(3);
        }
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        DmFoiImpExpRecordVO dmFoiImpExpRecordVO = new DmFoiImpExpRecordVO();
        try {
            dmFoiImpExpRecordVO = PoiEnum.exportExcel(os, exportExcelVoList);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }

        Assert.assertNotNull(dmFoiImpExpRecordVO);
    }

    @Test
    public void exportExcel10T() throws Exception {
        OutputStream os = null;
        List<AbstractExcelTitleVO> formInfoVo = new ArrayList<>();
        LeafExcelTitleVO columnCnVersion = new LeafExcelTitleVO(
                "", CommonConstant.WIDTH, true, "overSeaFlagValue", "overSeaFlagValue", CellType.STRING, false);
        LeafExcelTitleVO columnCnVersion2 = new LeafExcelTitleVO(
            "", CommonConstant.WIDTH, true, "granule", "granule", CellType.STRING, false);
        LeafExcelTitleVO columnCnVersion3 = new LeafExcelTitleVO(
            "", CommonConstant.WIDTH, true, "overseaFlag", "overseaFlag", CellType.STRING, false);
        LeafExcelTitleVO columnCnVersion4 = new LeafExcelTitleVO(
            "", CommonConstant.WIDTH, true, "bgFlag", "bgFlag", CellType.STRING, false);
        formInfoVo.add(columnCnVersion);
        formInfoVo.add(columnCnVersion2);
        formInfoVo.add(columnCnVersion3);
        formInfoVo.add(columnCnVersion4);
        LeafExcelTitleVO columnCnVersionName = new LeafExcelTitleVO("版本名称", CommonConstant.WIDTH,
                null, "bgFlagValue", "bgFlagValue", CellType.STRING, false);
        formInfoVo.add(columnCnVersionName);

        List<Map> industryList = new ArrayList<>();
        Map map = new HashMap();
        map.put("year", "2023");
        map.put("groupCnName", "code1");
        map.put("weightRate", 1.0);
        map.put("annualAmp", "1.1");
        map.put("teamCode", "lv2");
        map.put("category", "111");
        map.put("weightAnnualAmpPercent", "2.0");
        industryList.add(map);
        LeafExcelTitleVO column;
        List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
        Set<String> titles = new LinkedHashSet<>();

        List<HeaderVo> INDUSTRY_LV1_VIEW_HEADER = new LinkedList<>();
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("年", "year", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("颗粒度", "granule", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("名称", "groupCnName", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("权重", "weightRate", CellType.NUMERIC, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("涨跌", "annualAmp", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("lv2", "teamCode", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("品类", "category", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("权重×涨跌", "weightAnnualAmpPercent", CellType.STRING, false, 12 * 480));

        for (HeaderVo header : INDUSTRY_LV1_VIEW_HEADER) {
            column = new LeafExcelTitleVO(
                header.getTitle(),
                header.getWidth(),
                true,
                header.getField(),
                header.getField(),
                header.getDataType(),
                header.getIsEditable());
            titleVoList.add(column);
            titles.add(header.getField());
        }

        List<ExportExcelVo> exportExcelVoList = new ArrayList<>();
        ExportExcelVo exportExcelVo = ExportExcelVo.builder().formInfoVo(formInfoVo).titleVoList(titleVoList)
                .list(industryList).sheetName("产业成本/").mergeCell(false).threeTitleList(formInfoVo).fourTitleList(formInfoVo).build();
        exportExcelVoList.add(exportExcelVo);
        for (ExportExcelVo exportExcel : exportExcelVoList) {
            List<AbstractExcelTitleVO> leafExcelTitleVO = new ArrayList<>();
            int titleRowCount = adjustTitleVoList(exportExcel.getTitleVoList(), leafExcelTitleVO);;
            List<AbstractExcelTitleVO> selectedLeafExcelTitleVO = leafExcelTitleVO;
            if (exportExcel.getMergeCell()) {
                selectedLeafExcelTitleVO = leafExcelTitleVO.stream()
                    .limit(leafExcelTitleVO.size() - 1)
                    .collect(Collectors.toList());
            }
            List<AbstractExcelTitleVO> titleVoList1 = exportExcel.getTitleVoList();
            for (AbstractExcelTitleVO abstractExcelTitleVO : titleVoList1) {
                if ("年".equals(abstractExcelTitleVO.getValue())){
                    abstractExcelTitleVO.setSelected(false);
                }
                if ("品类".equals(abstractExcelTitleVO.getValue())){
                    abstractExcelTitleVO.setSelected(null);
                }
            }
            exportExcel.setSelectedLeafExcelTitleVO(selectedLeafExcelTitleVO);
            exportExcel.setTitleRowCount(titleRowCount);
            exportExcel.setTitleRowCount(3);
        }
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        DmFoiImpExpRecordVO dmFoiImpExpRecordVO = PoiEnum.exportExcel(os, exportExcelVoList);
        Assert.assertNotNull(dmFoiImpExpRecordVO);
    }

    @Test
    public void exportExcel11T() throws Exception {
        OutputStream os = null;
        List<AbstractExcelTitleVO> formInfoVo = new ArrayList<>();
        LeafExcelTitleVO columnCnVersion = new LeafExcelTitleVO(
            "", CommonConstant.WIDTH, true, "overSeaFlagValue", "overSeaFlagValue", CellType.STRING, false);
        LeafExcelTitleVO columnCnVersion2 = new LeafExcelTitleVO(
            "", CommonConstant.WIDTH, true, "granule", "granule", CellType.STRING, false);
        LeafExcelTitleVO columnCnVersion3 = new LeafExcelTitleVO(
            "", CommonConstant.WIDTH, true, "overseaFlag", "overseaFlag", CellType.STRING, false);
        LeafExcelTitleVO columnCnVersion4 = new LeafExcelTitleVO(
            "", CommonConstant.WIDTH, true, "bgFlag", "bgFlag", CellType.STRING, false);
        formInfoVo.add(columnCnVersion);
        formInfoVo.add(columnCnVersion2);
        formInfoVo.add(columnCnVersion3);
        formInfoVo.add(columnCnVersion4);
        LeafExcelTitleVO columnCnVersionName =
            new LeafExcelTitleVO(
                "版本名称", CommonConstant.WIDTH, null, "bgFlagValue", "bgFlagValue", CellType.STRING, false);
        formInfoVo.add(columnCnVersionName);

        List<Map> industryList = new ArrayList<>();
        Map map = new HashMap();
        map.put("year", "2023");
        map.put("groupCnName", "code1");
        map.put("weightRate", 1.0);
        map.put("annualAmp", "1.1");
        map.put("teamCode", "lv2");
        map.put("category", "111");
        map.put("weightAnnualAmpPercent", "2.0");
        industryList.add(map);
        LeafExcelTitleVO column;
        List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
        Set<String> titles = new LinkedHashSet<>();

        List<HeaderVo> INDUSTRY_LV1_VIEW_HEADER = new LinkedList<>();

        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("年", "year", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("颗粒度", "granule", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("名称", "groupCnName", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("权重", "weightRate", CellType.NUMERIC, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("涨跌", "annualAmp", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("lv2", "teamCode", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("品类", "category", CellType.STRING, false, 12 * 480));
        INDUSTRY_LV1_VIEW_HEADER.add(new HeaderVo("权重×涨跌", "weightAnnualAmpPercent", CellType.STRING, false, 12 * 480));

        for (HeaderVo header : INDUSTRY_LV1_VIEW_HEADER) {
            column = new LeafExcelTitleVO(
                header.getTitle(),
                header.getWidth(),
                true,
                header.getField(),
                header.getField(),
                header.getDataType(),
                header.getIsEditable());
            titleVoList.add(column);
            titles.add(header.getField());
        }

        List<ExportExcelVo> exportExcelVoList = new ArrayList<>();
        ExportExcelVo exportExcelVo =
            ExportExcelVo.builder()
                .formInfoVo(formInfoVo)
                .titleVoList(titleVoList)
                .list(industryList)
                .sheetName("产业成本/")
                .mergeCell(false)
                .threeTitleList(formInfoVo)
                .fourTitleList(formInfoVo)
                .build();
        exportExcelVoList.add(exportExcelVo);
        for (ExportExcelVo exportExcel : exportExcelVoList) {
            List<AbstractExcelTitleVO> leafExcelTitleVO = new ArrayList<>();
            int titleRowCount = adjustTitleVoList(exportExcel.getTitleVoList(), leafExcelTitleVO);;
            List<AbstractExcelTitleVO> selectedLeafExcelTitleVO = leafExcelTitleVO;
            if (exportExcel.getMergeCell()) {
                selectedLeafExcelTitleVO = leafExcelTitleVO.stream()
                    .limit(leafExcelTitleVO.size() - 1)
                    .collect(Collectors.toList());
            }
            List<AbstractExcelTitleVO> titleVoList1 = exportExcel.getTitleVoList();
            for (AbstractExcelTitleVO abstractExcelTitleVO : titleVoList1) {
                if ("年".equals(abstractExcelTitleVO.getValue())){
                    abstractExcelTitleVO.setSelected(false);
                }
                if ("品类".equals(abstractExcelTitleVO.getValue())){
                    abstractExcelTitleVO.setSelected(null);
                }
            }

            exportExcel.setSelectedLeafExcelTitleVO(selectedLeafExcelTitleVO);
            exportExcel.setTitleRowCount(titleRowCount);
            exportExcel.setTitleRowCount(5);
        }
        mockStatic(FileProcessUtis.class);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        DmFoiImpExpRecordVO dmFoiImpExpRecordVO = PoiEnum.exportExcel(os, exportExcelVoList);
        Assert.assertNotNull(dmFoiImpExpRecordVO);
    }

    public int adjustTitleVoList(List<AbstractExcelTitleVO> excelTitleVOS,
                                 List<AbstractExcelTitleVO> selectedLeafExcelTitleVO) {
        ExcelUtilPro excelUtilPro = new ExcelUtilPro();
        excelUtilPro.initTitleVoList(excelTitleVOS, selectedLeafExcelTitleVO, 1, 0);
        // 叶子结点的高度调整，
        int maxX2 = 0;
        for (AbstractExcelTitleVO leafExcelTitleVO : selectedLeafExcelTitleVO) {
            if (leafExcelTitleVO.getX2() > maxX2) {
                maxX2 = leafExcelTitleVO.getX2();
            }
        }
        for (AbstractExcelTitleVO leafExcelTitleVO : selectedLeafExcelTitleVO) {
            if (leafExcelTitleVO.getX2() < maxX2) {
                leafExcelTitleVO.setX2(maxX2);
            }
        }
        return maxX2 + 1;
    }
}