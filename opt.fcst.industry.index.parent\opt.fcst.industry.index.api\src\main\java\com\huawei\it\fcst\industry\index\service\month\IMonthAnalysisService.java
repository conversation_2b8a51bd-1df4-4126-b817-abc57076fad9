/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.service.month;

import com.huawei.it.fcst.industry.index.vo.ciphertext.VarifyTaskVO;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import io.swagger.annotations.Api;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.List;

/**
 * IMonthAnalysisService Interface
 *
 * <AUTHOR>
 * @since 2022-10-10
 */
@Path("/monthlyAnalysis")
@Api(value = "月度分析接口服务")
@Produces(MediaType.APPLICATION_JSON)
public interface IMonthAnalysisService {

    /**
     * [查询多选下拉框列表]
     *     产业成本指数（多维度）图数据的右上角多选下拉框使用
     *
     * @param monthAnalysisVO MonthAnalysisVO
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @POST
    @Path("/multi/boxList")
    ResultDataVO getMultiBoxList(MonthAnalysisVO monthAnalysisVO) throws ApplicationException;


    /**
     * [查询多选下拉框列表]
     *     产业成本指数（多维度）图数据的右上角多选下拉框使用
     *
     * @param monthAnalysisVO MonthAnalysisVO
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @POST
    @Path("/multi/itemBoxList")
    ResultDataVO getMultiItemBoxList(MonthAnalysisVO monthAnalysisVO) throws ApplicationException;

    /**
     * [查询多选下拉框列表]
     *     切换基期异步任务状态查询
     *
     * @param varifyTaskVO VarifyTaskVO
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @POST
    @Path("/index/queryDataRefreshStatus")
    ResultDataVO queryDataRefreshStatus(VarifyTaskVO varifyTaskVO) throws ApplicationException;

    /**
     * [查询产业成本价格指数图数据]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @POST
    @Path("/priceIndex/taskStatus")
    ResultDataVO getIndustryCostIndexTaskStatus(MonthAnalysisVO monthAnalysisVO);

    /**
     * [查询产业成本价格指数图数据]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @POST
    @Path("/priceIndex/chart")
    ResultDataVO getIndustryCostIndexChart(MonthAnalysisVO monthAnalysisVO);

    /**
     * [产业成本对比分析指数图]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @POST
    @Path("/getCompareIndex/chart")
    ResultDataVO getCompareIndexChart(List<MonthAnalysisVO> monthAnalysisVoList) throws InterruptedException;

    /**
     * [查询产业成本指数同比环比图数据]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @POST
    @Path("/priceIndex/yoyAndpop")
    ResultDataVO getIndustryCostYoyAndPopChart(MonthAnalysisVO monthAnalysisVO) throws CommonApplicationException;

    /**
     * [查询产业成本指数（多维度）图数据]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @POST
    @Path("/priceIndex/dimension/taskStatus")
    ResultDataVO getIndustryCostMultiIndexTaskStatus(MonthAnalysisVO monthAnalysisVO) throws CommonApplicationException;

    /**
     * [查询产业成本指数（多维度）图数据]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @POST
    @Path("/priceIndex/dimension/chart")
    ResultDataVO getIndustryCostMultiDimensionChart(MonthAnalysisVO monthAnalysisVO) throws CommonApplicationException;

    /**
     * [查询自制制造成本分布图数据]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @POST
    @Path("/costDistribution/chart")
    ResultDataVO getCostDistributionChart(MonthAnalysisVO monthAnalysisVO) throws CommonApplicationException;

    /**
     * [查询产业成本权重图数据]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @POST
    @Path("/weight/chart")
    ResultDataVO getIndustryCostWeightChart(MonthAnalysisVO monthAnalysisVO) throws CommonApplicationException;

    /**
     * [查询产业成本价格热力图数据]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @POST
    @Path("/heatmap")
    ResultDataVO getIndustryCostHeatmapChart(MonthAnalysisVO monthAnalysisVO) throws CommonApplicationException;

    /**
     * 查询涨跌根因分析
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @POST
    @Path("/amp/chart")
    ResultDataVO getAmpChartList(MonthAnalysisVO monthAnalysisVO, @Context HttpServletRequest request) throws ApplicationException;

    /**
     * [月度分析明细数据下载]
     *
     * @param monthAnalysisVO 查询参数VO
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @POST
    @Path("/detailData/export")
    ResultDataVO detailDataExport(MonthAnalysisVO monthAnalysisVO, @Context HttpServletResponse response) throws Exception;

    /**
     * [月度分析明细数据下载 多指数图数据量校验]
     *
     * @param monthAnalysisVO 查询参数VO
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @POST
    @Path("/detailData/exportVaild")
    ResultDataVO detailDataExportVaild(MonthAnalysisVO monthAnalysisVO) throws CommonApplicationException;
}
