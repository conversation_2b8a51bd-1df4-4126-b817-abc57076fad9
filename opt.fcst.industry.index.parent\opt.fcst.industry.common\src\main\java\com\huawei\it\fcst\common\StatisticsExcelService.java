/*
 *
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.common;

import com.huawei.it.fcst.constant.Constants;
import com.huawei.it.fcst.export.vo.PbiDmFoiImpExpRecordVO;
import com.huawei.it.fcst.util.FileNameUtil;
import com.huawei.it.fcst.util.FileProcessUtis;
import com.huawei.it.fcst.util.UserInfoUtils;
import com.huawei.it.fcst.vo.UploadInfoVO;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import com.huawei.it.jalor5.core.util.PathUtil;
import com.huawei.it.jalor5.core.util.StreamUtil;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Inject;
import javax.inject.Named;

import static java.io.File.separator;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * StatisticsExcelService Class
 *
 * <AUTHOR>
 * @since 2023/3/14
 */
@Slf4j
@Named("statisticsService")
@JalorResource(code = "statisticsService", desc = "statisticsService")
public class StatisticsExcelService {
    @Inject
    private PersonalCenterService personalCenterService;

    public static PbiDmFoiImpExpRecordVO uploadExportExcel(Workbook workbook, int number, String fileName, Long userId) throws IOException, CommonApplicationException {
        String name = PathUtil.getTempPathByDay(Constants.DEF.getValue()) + separator + FileNameUtil.dealFileName(fileName) + getTempName() + ".xlsx";
        File file = new File(name);
        if (!file.createNewFile()) {
            log.error(">>> create new file failed. ");
        }
        FileOutputStream outputStream = null;
        try {
            outputStream = new FileOutputStream(file);
            workbook.write(outputStream);
            outputStream.flush();
            // 上传
            String userStr;
            if (userId != null) {
                userStr = String.valueOf(userId);
            } else {
                userStr = String.valueOf(UserInfoUtils.getUserId());
            }
            String fileSourceKey = FileProcessUtis.uploadToS3(file, userStr);

            PbiDmFoiImpExpRecordVO dmFoiRecordVO = new PbiDmFoiImpExpRecordVO();
            dmFoiRecordVO.setFileSourceKey(fileSourceKey);
            long fileSize = file.length() / 1024;
            dmFoiRecordVO.setFileSize(String.valueOf(fileSize));
            dmFoiRecordVO.setRecordNum(Long.valueOf(number));
            dmFoiRecordVO.setFileName(fileName);
            dmFoiRecordVO.setUserId(userStr);
            return dmFoiRecordVO;
        } catch (Exception e) {
            log.error("file create fail");
        } finally {
            StreamUtil.closeStreams(outputStream);
            if (file != null) {
                if (!file.delete()) {
                    log.error(">>> file delete failed.");
                }
            }
        }
        return null;
    }

    private static String getTempName() {
        return DateFormatUtils.format(new Date(), "yyyyMMddHHmmss");
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void insertExportExcelRecord(PbiDmFoiImpExpRecordVO dmFoiRecordVO) {
        String moduleType = dmFoiRecordVO.getModuleType();
        dmFoiRecordVO.setPageModule(moduleType);
        dmFoiRecordVO.setRecSts("OK");
        personalCenterService.statisticsExportRecord(dmFoiRecordVO);
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void insertImportExcel(PbiDmFoiImpExpRecordVO dmFoiRecordVO, UploadInfoVO uploadVO, boolean flag, Integer recordNum) throws CommonApplicationException {
        String fileName = uploadVO.getFileName();
        long fileSize = uploadVO.getFileSize();
        Map<String, Object> params = uploadVO.getParams();
        setImportExcelParams(fileName, dmFoiRecordVO, fileSize, params, recordNum);
        String userId = String.valueOf(uploadVO.getUserId());
        if (flag) {
            dmFoiRecordVO.setRecSts("OK");
        } else {
            dmFoiRecordVO.setRecSts("FAIL");
        }
        personalCenterService.statisticsImportRecord(dmFoiRecordVO, userId);
    }

    private static void setImportExcelParams(String fileName, PbiDmFoiImpExpRecordVO dmFoiRecordVO, long size, Map<String, Object> params, Integer recordNum) {
        dmFoiRecordVO.setFileName(fileName);
        dmFoiRecordVO.setFileSize(String.valueOf(size));
        dmFoiRecordVO.setRecordNum(Long.valueOf(recordNum));
        if (StringUtils.isEmpty(dmFoiRecordVO.getModuleType())) {
            Object module = params.get("module");
            dmFoiRecordVO.setPageModule(String.valueOf(module));
        } else {
            dmFoiRecordVO.setPageModule(dmFoiRecordVO.getModuleType());
        }
    }

    public void getImportUploadFileKey(PbiDmFoiImpExpRecordVO dmFoiRecordVO, Long userId, int type, InputStream inputStream) throws CommonApplicationException, IOException {
        File file = transformFile(inputStream);
        String fileSourceKey = FileProcessUtis.uploadToS3(file, String.valueOf(userId));
        switch (type) {
            // 正常和异常相同文件
            case 1:
                dmFoiRecordVO.setFileSourceKey(fileSourceKey);
                dmFoiRecordVO.setFileErrorKey(fileSourceKey);
                break;
            // 只有正常文件
            case 2:
                dmFoiRecordVO.setFileSourceKey(fileSourceKey);
                break;
            default:
                dmFoiRecordVO.setFileSourceKey(fileSourceKey);
        }
        if (file != null) {
            if (!file.delete()) {
                log.error(">>> file delete failed.");
            }
        }
    }

    public File transformFile(InputStream inputStream) throws IOException {
        String tempName = PathUtil.getTempPathByDay(Constants.DEF.getValue()) + separator + getTempName() + ".xlsx";
        File file = new File(tempName);
        FileUtils.copyInputStreamToFile(inputStream, file);
        return file;
    }
}
