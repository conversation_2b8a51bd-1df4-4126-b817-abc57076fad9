<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.pbi.dao.IctProdMainCodeDimDao">
    <resultMap type="com.huawei.it.fcst.industry.pbi.vo.config.IctProdMainCodeDimVO" id="resultMap">
        <result property="primaryId"         column="primary_id"/>
        <result property="versionId"         column="version_id"/>
        <result property="bgCode"            column="bg_code"/>
        <result property="bgCnName"          column="bg_cn_name"/>
        <result property="lv1ProdListCode"   column="lv1_prod_list_code"/>
        <result property="lv1ProdListCnName" column="lv1_prod_list_cn_name"/>
        <result property="lv2ProdListCode"   column="lv2_prod_list_code"/>
        <result property="lv2ProdListCnName" column="lv2_prod_list_cn_name"/>
        <result property="lv3ProdListCode"   column="lv3_prod_list_code"/>
        <result property="lv3ProdListCnName" column="lv3_prod_list_cn_name"/>
        <result property="lv4ProdListCode"   column="lv4_prod_list_code"/>
        <result property="lv4ProdListCnName" column="lv4_prod_list_cn_name"/>
        <result property="productCode"       column="product_code"/>
        <result property="spartCode"         column="spart_code"/>
        <result property="spartCnName"       column="spart_cn_name"/>
        <result property="codeAttributes"    column="code_attributes"/>
        <result property="createdBy"         column="created_by"/>
        <result property="creationDate"      column="creation_date"/>
        <result property="lastUpdatedBy"     column="last_updated_by"/>
        <result property="lastUpdateDate"    column="last_update_date"/>
        <result property="delFlag"			 column="del_flag"/>
    </resultMap>

    <sql id="allFields">
        primary_id
        ,version_id
        ,bg_code
        ,bg_cn_name
        ,lv1_prod_list_code
        ,lv2_prod_list_code
        ,lv3_prod_list_code
        ,lv4_prod_list_code
        ,lv1_prod_list_cn_name
        ,lv2_prod_list_cn_name
        ,lv3_prod_list_cn_name
        ,lv4_prod_list_cn_name
        ,product_code
        ,spart_code
        ,spart_cn_name
        ,code_attributes
        ,created_by
        ,creation_date
        ,last_updated_by
        ,last_update_date
        ,del_flag
    </sql>

    <select id="findMainCodeDropboxList" resultMap="resultMap">
        SELECT DISTINCT
        <choose>
            <when test='groupLevel == "LV0"'>
                bg_code, bg_cn_name
            </when>
            <when test='groupLevel == "LV1"'>
                lv1_prod_list_code, lv1_prod_list_cn_name
            </when>
            <when test='groupLevel == "LV2"'>
                lv2_prod_list_code, lv2_prod_list_cn_name
            </when>
            <when test='groupLevel == "LV3"'>
                lv3_prod_list_code, lv3_prod_list_cn_name
            </when>
            <when test='groupLevel == "LV4"'>
                lv4_prod_list_code, lv4_prod_list_cn_name
            </when>
            <otherwise>
                spart_code, spart_cn_name
            </otherwise>
        </choose>
        FROM fin_dm_opt_foi.dm_fcst_ict_prod_main_code_dim_t
        WHERE del_flag = 'N'
        AND version_id = #{versionId,jdbcType=NUMERIC}
        <if test='bgCode != null and bgCode != ""'>
            AND bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <include refid="dropdownWhere" />
    </select>

    <select id="findMainCodeEditDropboxList" resultMap="resultMap">
        SELECT DISTINCT
        <choose>
            <when test='groupLevel == "LV0"'>
                lv0_prod_list_code as bg_code, lv0_prod_list_cn_name as bg_cn_name
            </when>
            <when test='groupLevel == "LV1"'>
                lv0_prod_list_code as bg_code, lv1_prod_list_code, lv1_prod_list_cn_name
            </when>
            <when test='groupLevel == "LV2"'>
                lv0_prod_list_code as bg_code, lv2_prod_list_code, lv2_prod_list_cn_name
            </when>
            <when test='groupLevel == "LV3"'>
                lv0_prod_list_code as bg_code, lv3_prod_list_code, lv3_prod_list_cn_name
            </when>
            <otherwise>
                lv0_prod_list_code as bg_code, lv4_prod_list_code, lv4_prod_list_cn_name
            </otherwise>
        </choose>
        FROM dmdim.dm_dim_product_d
        WHERE del_flag = 'N'
        AND scd_active_ind = 1
        <if test='groupLevel != null and groupLevel != "" and groupLevel == "LV0"'>
            AND lv0_prod_list_code in ('PDCG901159','PDCG901160')
        </if>
        <if test='groupLevel != null and groupLevel != "" and groupLevel == "LV2"'>
            AND lv2_prod_list_code != lv1_prod_list_code
        </if>
        <if test='groupLevel != null and groupLevel != "" and groupLevel == "LV3"'>
            AND lv3_prod_list_code != lv2_prod_list_code
        </if>
        <if test='groupLevel != null and groupLevel != "" and groupLevel == "LV4"'>
            AND lv4_prod_list_code != lv3_prod_list_code
        </if>
        <if test='bgCode != null and bgCode != ""'>
            AND lv0_prod_list_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='bgCodeList != null and bgCodeList.size() > 0'>
            <foreach collection='bgCodeList' item="name" open="AND lv0_prod_list_code  IN (" close=")" index="index"
                     separator=",">
                #{name}
            </foreach>
        </if>
        <include refid="dropdownWhere" />
    </select>

    <select id="findAllSpartCodeList" resultType="java.lang.String">
        SELECT DISTINCT spart_code
        FROM (
                 SELECT REPLACE(REPLACE(spart_code, chr(10), ''),' ','') spart_code
                 FROM fin_dm_opt_foi.dwl_prod_prod_unit_i
                 WHERE spart_code IS NOT NULL
             )
    </select>

    <select id="findMainCodeSpartDropboxList" resultMap="resultMap">
        SELECT DISTINCT spart_code from (
        SELECT replace(REPLACE(spart_code, chr(10), ''),' ','') spart_code
        FROM fin_dm_opt_foi.dwl_prod_prod_unit_i
        WHERE spart_code IS NOT NULL
        <if test='prodMainCodeDimVO.spartCode != null and prodMainCodeDimVO.spartCode != ""'>
            AND spart_code LIKE '%' || #{prodMainCodeDimVO.spartCode,jdbcType=VARCHAR} || '%'
        </if>
        )
        ORDER BY spart_code
        LIMIT #{pageVO.pageSize} OFFSET #{pageVO.startIndex} - 1
    </select>

    <select id="findMainCodeSpartDropboxListCount" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM (
        SELECT DISTINCT spart_code from (
        SELECT replace(REPLACE(spart_code, chr(10), ''),' ','') spart_code
        FROM fin_dm_opt_foi.dwl_prod_prod_unit_i
        WHERE spart_code IS NOT NULL
        <if test='prodMainCodeDimVO.spartCode != null and prodMainCodeDimVO.spartCode != ""'>
            AND spart_code LIKE '%' || #{prodMainCodeDimVO.spartCode,jdbcType=VARCHAR} || '%'
        </if>
        )
        )
    </select>

    <select id="findMainCodeDimVOList" resultMap="resultMap">
        SELECT
        <include refid="allFields"/>
        FROM fin_dm_opt_foi.dm_fcst_ict_prod_main_code_dim_t
        WHERE del_flag = 'N'
        <include refid="searchWhere"/>
    </select>

    <select id="findMainCodeDimListByPage" resultMap="resultMap">
        SELECT
        <include refid="allFields"/>
        FROM fin_dm_opt_foi.dm_fcst_ict_prod_main_code_dim_t
        WHERE del_flag = 'N'
        <include refid="searchWhere"/>
        ORDER BY last_update_date DESC
        LIMIT #{pageVO.pageSize} OFFSET #{pageVO.startIndex} - 1
    </select>

    <select id="findMainCodeDimListByPageCount" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM (
        SELECT
        <include refid="allFields"/>
        FROM fin_dm_opt_foi.dm_fcst_ict_prod_main_code_dim_t
        WHERE del_flag = 'N'
        <include refid="searchWhere"/>
        )
    </select>

    <sql id="dropdownWhere">
        <if test='lv1ProdListCode != null and lv1ProdListCode != ""'>
            AND lv1_prod_list_code = #{lv1ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='lv2ProdListCode != null and lv2ProdListCode != ""'>
            AND lv2_prod_list_code = #{lv2ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='lv3ProdListCode != null and lv3ProdListCode != ""'>
            AND lv3_prod_list_code = #{lv3ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='lv4ProdListCode != null and lv4ProdListCode != ""'>
            AND lv4_prod_list_code = #{lv4ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='lv1ProdListCnName != null and lv1ProdListCnName != ""'>
            AND lv1_prod_list_cn_name LIKE '%' || #{lv1ProdListCnName,jdbcType=VARCHAR} || '%'
        </if>
        <if test='lv2ProdListCnName != null and lv2ProdListCnName != ""'>
            AND lv2_prod_list_cn_name LIKE '%' || #{lv2ProdListCnName,jdbcType=VARCHAR} || '%'
        </if>
        <if test='lv3ProdListCnName != null and lv3ProdListCnName != ""'>
            AND lv3_prod_list_cn_name LIKE '%' || #{lv3ProdListCnName,jdbcType=VARCHAR} || '%'
        </if>
        <if test='lv4ProdListCnName != null and lv4ProdListCnName != ""'>
            AND lv4_prod_list_cn_name LIKE '%' || #{lv4ProdListCnName,jdbcType=VARCHAR} || '%'
        </if>
        <if test='spartCode != null and spartCode != ""'>
            AND spart_code = #{spartCode,jdbcType=VARCHAR}
        </if>
    </sql>

    <sql id="searchWhere">
        <if test='prodMainCodeDimVO.primaryId != null'>
            AND primary_id = #{prodMainCodeDimVO.primaryId,jdbcType=NUMERIC}
        </if>
        <if test='prodMainCodeDimVO.versionId != null'>
            AND version_id = #{prodMainCodeDimVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='prodMainCodeDimVO.bgCode != null and prodMainCodeDimVO.bgCode != ""'>
            AND bg_code = #{prodMainCodeDimVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='prodMainCodeDimVO.lv1ProdListCode != null and prodMainCodeDimVO.lv1ProdListCode != ""'>
            AND lv1_prod_list_code = #{prodMainCodeDimVO.lv1ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='prodMainCodeDimVO.lv2ProdListCode != null and prodMainCodeDimVO.lv2ProdListCode != ""'>
            AND lv2_prod_list_code = #{prodMainCodeDimVO.lv2ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='prodMainCodeDimVO.lv3ProdListCode != null and prodMainCodeDimVO.lv3ProdListCode != ""'>
            AND lv3_prod_list_code = #{prodMainCodeDimVO.lv3ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='prodMainCodeDimVO.lv4ProdListCode != null and prodMainCodeDimVO.lv4ProdListCode != ""'>
            AND lv4_prod_list_code = #{prodMainCodeDimVO.lv4ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='prodMainCodeDimVO.spartCode != null and prodMainCodeDimVO.spartCode != ""'>
            AND spart_code = #{prodMainCodeDimVO.spartCode,jdbcType=VARCHAR}
        </if>
    </sql>

    <insert id="batchInsertMainCodeDimVOs" parameterType="java.util.List">
        INSERT INTO fin_dm_opt_foi.dm_fcst_ict_prod_main_code_dim_t (
        version_id
        ,bg_code
        ,bg_cn_name
        ,lv1_prod_list_code
        ,lv2_prod_list_code
        ,lv3_prod_list_code
        ,lv4_prod_list_code
        ,lv1_prod_list_cn_name
        ,lv2_prod_list_cn_name
        ,lv3_prod_list_cn_name
        ,lv4_prod_list_cn_name
        ,product_code
        ,spart_code
        ,spart_cn_name
        ,code_attributes
        ,created_by
        ,creation_date
        ,last_updated_by
        ,last_update_date
        ,del_flag
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.versionId,jdbcType=NUMERIC},
            #{item.bgCode,jdbcType=VARCHAR},
            #{item.bgCnName,jdbcType=VARCHAR},
            #{item.lv1ProdListCode,jdbcType=VARCHAR},
            #{item.lv2ProdListCode,jdbcType=VARCHAR},
            #{item.lv3ProdListCode,jdbcType=VARCHAR},
            #{item.lv4ProdListCode,jdbcType=VARCHAR},
            #{item.lv1ProdListCnName,jdbcType=VARCHAR},
            #{item.lv2ProdListCnName,jdbcType=VARCHAR},
            #{item.lv3ProdListCnName,jdbcType=VARCHAR},
            #{item.lv4ProdListCnName,jdbcType=VARCHAR},
            #{item.productCode,jdbcType=VARCHAR},
            #{item.spartCode,jdbcType=VARCHAR},
            #{item.spartCnName,jdbcType=VARCHAR},
            #{item.codeAttributes,jdbcType=VARCHAR},
            #{item.createdBy,jdbcType=NUMERIC},
            now(),
            #{item.lastUpdatedBy,jdbcType=NUMERIC},
            now(),
            'N'
            )
        </foreach>
    </insert>

    <delete id="deleteMainCodeDimVOsByVersionId" parameterType="java.lang.Long">
        DELETE FROM fin_dm_opt_foi.dm_fcst_ict_prod_main_code_dim_t
        WHERE version_id = #{versionId,jdbcType=NUMERIC}
    </delete>

    <select id="callFuncMainCodeDimMapping" resultType="java.lang.String">
        select fin_dm_opt_foi.f_dm_fcst_main_code_dim_mapping(#{costType})
    </select>

</mapper>
