/*
 *
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * DmFocDataRefreshStatus Class
 *
 * <AUTHOR>
 * @since 2023/8/10
 */
@Setter
@Getter
@NoArgsConstructor
public class DmFcstDataRefreshStatus implements Serializable {

    private static final long serialVersionUID = 2292657719644968069L;

    /**
     * 任务id
     **/
    private Long taskId;

    /**
     * 删除标识(未删除：N，已删除：Y)
     **/
    private String delFlag;

    /**
     * 修改人
     **/
    private Long lastUpdatedBy;

    /**
     * 修改时间
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdateDate;

    /**
     * 创建人
     **/
    private Long createdBy;

    /**
     * 创建时间
     **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date creationDate;

    /**
     * 任务状态
     **/
    private String status;

    private int roleId;

    private Long userId;

    // INIT初始化的任务，COMB汇总组合的所有操作
    private String taskFlag;
}
