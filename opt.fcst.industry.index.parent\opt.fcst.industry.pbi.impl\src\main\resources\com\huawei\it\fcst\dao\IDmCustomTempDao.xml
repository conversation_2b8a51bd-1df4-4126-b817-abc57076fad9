<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.pbi.dao.IDmCustomTempDao">
    <resultMap type="com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstDimInfoVO" id="resultMap">
        <result property="id" column="id"/>
        <result property="bgCode" column="bg_code"/>
        <result property="bgCnName" column="bg_cn_name"/>
        <result property="dimensionCode" column="dimension_code"/>
        <result property="dimensionCnName" column="dimension_cn_name"/>
        <result property="dimensionSubCategoryCode" column="dimension_subCategory_code"/>
        <result property="dimensionSubCategoryCnName" column="dimension_subCategory_cn_name"/>
        <result property="dimensionSubDetailCode" column="dimension_sub_detail_code"/>
        <result property="dimensionSubDetailCnName" column="dimension_sub_detail_cn_name"/>
        <result property="spartCode" column="spart_code"/>
        <result property="spartCnName" column="spart_cn_name"/>
        <result property="regionCode" column="region_code"/>
        <result property="regionCnName" column="region_cn_name"/>
        <result property="repofficeCode" column="repoffice_code"/>
        <result property="repofficeCnName" column="repoffice_cn_name"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="groupCode" column="groupCode"/>
        <result property="groupCnName" column="groupCnName"/>
        <result property="groupLevel" column="group_level"/>
        <result property="groupCnName" column="group_cn_name"/>
        <result property="groupCode" column="group_code"/>
        <result property="isSeparate" column="is_separate"/>
        <result property="lv0Code" column="lv0_code"/>
        <result property="lv1Code" column="lv1_code"/>
        <result property="lv1CnName" column="lv1_cn_name"/>
        <result property="lv2Code" column="lv2_code"/>
        <result property="codeAttributes" column="code_attributes"/>
        <result property="mainFlag" column="main_flag"/>
        <result property="softwareMark" column="software_mark"/>
        <result property="overseaFlag" column="oversea_flag"/>
        <result property="subEnableFlag" column="sub_enable_flag"/>
        <result property="lv0Code" column="LV0_CODE" />
        <result property="lv0CnName" column="LV0_CN_NAME" />
        <result property="lv1Code" column="LV1_CODE" />
        <result property="lv1CnName" column="LV1_CN_NAME" />
        <result property="lv2Code" column="LV2_CODE" />
        <result property="lv2CnName" column="LV2_CN_NAME" />
        <result property="lv3Code" column="LV3_CODE" />
        <result property="lv3CnName" column="LV3_CN_NAME" />
        <result property="lv4Code" column="LV4_CODE" />
        <result property="lv4CnName" column="LV4_CN_NAME" />
        <result property="selectFlag" column="select_flag" />
        <result property="ytdFlag" column="ytd_flag" />
        <result property="granularityType" column="granularity_type" />
        <result property="connectCode" column="connect_code" />
        <result property="connectParentCode" column="connect_parent_code" />
    </resultMap>

    <select id="getTempCustomList" resultMap="resultMap">
        select custom_cn_name,id,LV0_CODE, LV0_CN_NAME,
        LV1_CODE, LV1_CN_NAME,
        LV2_CODE,LV2_CN_NAME,LV3_CODE,LV3_CN_NAME,LV4_CODE,LV4_CN_NAME,
        group_level,group_code,group_cn_name,user_id,role_id,granularity_type,oversea_flag,
        enable_flag,sub_enable_flag,bg_code,bg_cn_name,is_separate,main_flag,code_attributes,software_mark,
        region_code,region_cn_name,repoffice_code,repoffice_cn_name,ytd_flag,
        creation_date,last_update_date,created_by,last_updated_by,connect_code,connect_parent_code
        <if test='costType =="PSP"'>
            from fin_dm_opt_foi.dm_fcst_ict_psp_custom_comb_mid_dim_t
        </if>
        <if test='costType =="STD"'>
            from fin_dm_opt_foi.dm_fcst_ict_std_custom_comb_mid_dim_t
        </if>
        where del_flag ='N' and enable_flag='Y'
        and select_flag = 'current'
        <if test='id != null and id!=""'>
            and id = #{id}
        </if>
        <if test='pageSymbol != null and pageSymbol!=""'>
            and page_flag = #{pageSymbol}
        </if>
    </select>

    <insert id="createTempCombList">
        insert into
        <if test='costType =="PSP"'>
            fin_dm_opt_foi.dm_fcst_ict_psp_custom_comb_mid_dim_t
        </if>
        <if test='costType =="STD"'>
            fin_dm_opt_foi.dm_fcst_ict_std_custom_comb_mid_dim_t
        </if>
        (id,custom_cn_name, page_flag,group_level,LV0_CODE,
        LV0_CN_NAME, LV1_CODE, LV1_CN_NAME,
        LV2_CODE,LV2_CN_NAME,LV3_CODE,LV3_CN_NAME,LV4_CODE,LV4_CN_NAME,bg_code,bg_cn_name,group_code,group_cn_name,granularity_type,oversea_flag,
        enable_flag,sub_enable_flag,user_id,role_id,main_flag,code_attributes,
        region_code,region_cn_name,repoffice_code,repoffice_cn_name,software_mark,
        created_by,creation_date,last_updated_by,last_update_date,
        del_flag,is_separate,connect_code,connect_parent_code,select_flag)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.customCnName}, #{item.pageFlag},#{item.groupLevel},#{item.lv0Code},#{item.lv0CnName},
            #{item.lv1Code},#{item.lv1CnName},
            #{item.lv2Code},#{item.lv2CnName},#{item.lv3Code},#{item.lv3CnName},#{item.lv4Code},#{item.lv4CnName},
            #{item.bgCode},#{item.bgCnName},#{item.groupCode},#{item.groupCnName},#{item.granularityType},#{item.overseaFlag},#{item.enableFlag},#{item.subEnableFlag},
            #{item.userId},#{item.roleId},#{item.mainFlag},#{item.codeAttributes}, #{item.regionCode},#{item.regionCnName},#{item.repofficeCode},#{item.repofficeCnName},#{item.softwareMark},
            #{item.createdBy},#{item.creationDate},#{item.lastUpdatedBy}, #{item.lastUpdateDate},'N',#{item.isSeparate},#{item.connectCode},#{item.connectParentCode},#{item.selectFlag})
        </foreach>
    </insert>

    <insert id="createTempCombByCustomId">
        insert into
        <if test='costType =="PSP"'>
            fin_dm_opt_foi.dm_fcst_ict_psp_custom_comb_mid_dim_t
        </if>
        <if test='costType =="STD"'>
            fin_dm_opt_foi.dm_fcst_ict_std_custom_comb_mid_dim_t
        </if>
        (id,custom_cn_name, page_flag,group_level,LV0_CODE,
        LV0_CN_NAME, LV1_CODE, LV1_CN_NAME,LV2_CODE,LV2_CN_NAME,LV3_CODE,LV3_CN_NAME,LV4_CODE,LV4_CN_NAME,
        bg_code,bg_cn_name,granularity_type,oversea_flag,software_mark, region_code,region_cn_name,repoffice_code,repoffice_cn_name,
        main_flag,code_attributes,enable_flag,sub_enable_flag,group_cn_name,group_code,ytd_flag,user_id,role_id,created_by,creation_date,
        last_updated_by, last_update_date,del_flag,is_separate,select_flag,connect_code,connect_parent_code)
        select #{id},#{id},#{pageSymbol}, group_level,LV0_CODE,LV0_CN_NAME, LV1_CODE, LV1_CN_NAME,LV2_CODE,LV2_CN_NAME,
        LV3_CODE,LV3_CN_NAME,LV4_CODE,LV4_CN_NAME,bg_code,bg_cn_name,granularity_type,oversea_flag,software_mark,
        region_code,region_cn_name,repoffice_code,repoffice_cn_name,main_flag,code_attributes,
        enable_flag,sub_enable_flag,group_cn_name,group_code,ytd_flag,user_id,role_id,created_by,
        creation_date,last_updated_by, last_update_date,del_flag,is_separate,'current',
        concat(DECODE(LV0_CODE,'','',LV0_CODE),
        DECODE(LV1_CODE,'','','#*#' || LV1_CODE),
        DECODE(LV2_CODE,'','','#*#' || LV2_CODE),
        DECODE(LV3_CODE,'','','#*#' ||  LV3_CODE),
        DECODE(LV4_CODE,'','','#*#' ||  LV4_CODE)
        ) as connectCode,
        CASE WHEN group_level = 'LV4' THEN concat(DECODE(LV0_CODE,'','',LV0_CODE),
        DECODE(LV1_CODE,'','','#*#' || LV1_CODE),
        DECODE(LV2_CODE,'','','#*#' || LV2_CODE),
        DECODE(LV3_CODE,'','','#*#' ||  LV3_CODE))
        WHEN group_level = 'LV3' THEN concat(DECODE(LV0_CODE,'','',LV0_CODE),
        DECODE(LV1_CODE,'','','#*#' || LV1_CODE),
        DECODE(LV2_CODE,'','','#*#' || LV2_CODE)
        )
        WHEN group_level = 'LV2' THEN concat(DECODE(LV0_CODE,'','',LV0_CODE),
        DECODE(LV1_CODE,'','','#*#' || LV1_CODE)
        )
        WHEN group_level = 'LV1'
        <choose>
            <when test='lv0DimensionSet != null and lv0DimensionSet.size() == 1 and lv0DimensionSet.contains("NO_PERMISSION")'>
                THEN null
            </when>
            <otherwise>
                THEN concat(DECODE(LV0_CODE,'','',LV0_CODE))
            </otherwise>
        </choose>
        ELSE null END as connectParentCode
        from
        <if test='costType =="PSP"'>
            fin_dm_opt_foi.dm_fcst_ict_psp_custom_comb_dim_t
        </if>
        <if test='costType =="STD"'>
            fin_dm_opt_foi.dm_fcst_ict_std_custom_comb_dim_t
        </if>
        where use_flag ='PAGE' and custom_id =#{customId} and page_flag = #{pageFlag}
    </insert>

    <delete id="deleteCustomCombTemp">
        delete from
        <if test='costType =="PSP"'>
            fin_dm_opt_foi.dm_fcst_ict_psp_custom_comb_mid_dim_t
        </if>
        <if test='costType =="STD"'>
            fin_dm_opt_foi.dm_fcst_ict_std_custom_comb_mid_dim_t
        </if>
        where user_id = #{userAccount} and role_id = #{roleId}
        <if test='id != null and id!=""'>
            and id = #{id}
        </if>
        <if test='pageSymbol != null and pageSymbol!=""'>
            and page_flag = #{pageSymbol}
        </if>
        <if test='connectCode != null and connectCode!=""'>
            and connect_code = #{connectCode}
        </if>
    </delete>

    <select id="getTempTableCustomCombList" resultMap="resultMap">
        select custom_cn_name,id,LV0_CODE, LV0_CN_NAME,
        LV1_CODE, LV1_CN_NAME,LV2_CODE,LV2_CN_NAME,LV3_CODE,LV3_CN_NAME,LV4_CODE,LV4_CN_NAME,
        group_level,group_code,group_cn_name,user_id,role_id,granularity_type,oversea_flag,software_mark,code_attributes,
        region_code,region_cn_name,repoffice_code,repoffice_cn_name,main_flag,enable_flag,sub_enable_flag,
        bg_code,bg_cn_name,is_separate,creation_date,
        last_update_date,created_by,last_updated_by,connect_code,connect_parent_code,select_flag
        <if test='costType =="PSP"'>
            from fin_dm_opt_foi.dm_fcst_ict_psp_custom_comb_mid_dim_t
        </if>
        <if test='costType =="STD"'>
            from fin_dm_opt_foi.dm_fcst_ict_std_custom_comb_mid_dim_t
        </if>
        where del_flag ='N'
        and user_id = #{userAccount} and role_id = #{roleId}
        <if test='id != null and id!=""'>
            and id = #{id}
        </if>
        <if test='pageSymbol != null and pageSymbol!=""'>
            and page_flag = #{pageSymbol}
        </if>
        <if test='lv0Code != null and lv0Code != ""'>
            AND LV0_CODE = #{lv0Code}
        </if>
        <if test='lv1Code != null and lv1Code != ""'>
            AND LV1_CODE = #{lv1Code}
        </if>
        <if test='lv2Code != null and lv2Code != ""'>
            AND LV2_CODE = #{lv2Code}
        </if>
        <if test='lv3Code != null and lv3Code != ""'>
            AND LV3_CODE = #{lv3Code}
        </if>
        <if test='lv4Code != null and lv4Code != ""'>
            AND LV4_CODE = #{lv4Code}
        </if>
        <if test='lv0CnName == null or lv0CnName == ""'>
            AND group_level in ('LV0','LV1','LV2','LV3')
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            AND group_level = #{groupLevel}
        </if>
        <if test='keyword != null and keyword != ""'>
            AND group_cn_name LIKE CONCAT(CONCAT('%', #{keyword}::text ,'%'))
        </if>
    </select>

    <select id="getTempParentCombList" resultType="java.lang.String">
        select connect_code
        <if test='costType =="PSP"'>
            from fin_dm_opt_foi.dm_fcst_ict_psp_custom_comb_mid_dim_t
        </if>
        <if test='costType =="STD"'>
            from fin_dm_opt_foi.dm_fcst_ict_std_custom_comb_mid_dim_t
        </if>
        where del_flag ='N'
        and user_id = #{userAccount} and role_id = #{roleId}
        <if test='id != null and id!=""'>
            and id = #{id}
        </if>
        <if test='pageSymbol != null and pageSymbol!=""'>
            and page_flag = #{pageSymbol}
        </if>
    </select>

    <update id="updateCombChageSelectFlagList">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update
            <if test='combinationVO.costType =="PSP"'>
                fin_dm_opt_foi.dm_fcst_ict_psp_custom_comb_mid_dim_t
            </if>
            <if test='combinationVO.costType =="STD"'>
                fin_dm_opt_foi.dm_fcst_ict_std_custom_comb_mid_dim_t
            </if>
            set
            select_flag = #{item.selectFlag}
            where id = #{combinationVO.id} and user_id = #{combinationVO.userAccount} and role_id = #{combinationVO.roleId} and connect_code = #{item.connectCode}
            <if test='combinationVO.pageSymbol != null and combinationVO.pageSymbol!=""'>
                and page_flag = #{combinationVO.pageSymbol}
            </if>
        </foreach>
    </update>

    <delete id="deleteCombTempByConnectCode">
        <foreach collection="currentCustomList" item="item" index="index" open="" close="" separator=";">
            delete from
            <if test='combinationVO.costType =="PSP"'>
                fin_dm_opt_foi.dm_fcst_ict_psp_custom_comb_mid_dim_t
            </if>
            <if test='combinationVO.costType =="STD"'>
                fin_dm_opt_foi.dm_fcst_ict_std_custom_comb_mid_dim_t
            </if>
            where user_id = #{combinationVO.userAccount}
            and role_id =#{combinationVO.roleId}
            and id = #{combinationVO.id}
            and connect_code = #{item.connectCode}
            <if test='combinationVO.pageSymbol != null and combinationVO.pageSymbol!=""'>
                and page_flag = #{combinationVO.pageSymbol}
            </if>
        </foreach>
    </delete>

    <select id="getCountCombTemp" resultType="java.lang.Integer">
        select count(1)
        <if test='costType =="PSP"'>
            from fin_dm_opt_foi.dm_fcst_ict_psp_custom_comb_mid_dim_t
        </if>
        <if test='costType =="STD"'>
            from fin_dm_opt_foi.dm_fcst_ict_std_custom_comb_mid_dim_t
        </if>
        where del_flag ='N'
        and user_id = #{userAccount} and role_id = #{roleId}
        <if test='id != null and id!=""'>
            and id = #{id}
        </if>
        <if test='pageSymbol != null and pageSymbol!=""'>
            and page_flag = #{pageSymbol}
        </if>
        <if test='connectCode != null and connectCode!=""'>
            and connect_parent_code = #{connectCode}
        </if>
    </select>

</mapper>