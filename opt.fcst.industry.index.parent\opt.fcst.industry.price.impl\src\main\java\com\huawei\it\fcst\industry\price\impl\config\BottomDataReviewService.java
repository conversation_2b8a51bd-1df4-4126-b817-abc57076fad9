/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.impl.config;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.huawei.it.fcst.common.StatisticsExcelService;
import com.huawei.it.fcst.enums.CommonConstEnum;
import com.huawei.it.fcst.export.IExcelTemplateBeanManager;
import com.huawei.it.fcst.export.IExportProcessorService;
import com.huawei.it.fcst.export.vo.PbiDmFoiImpExpRecordVO;
import com.huawei.it.fcst.industry.price.constant.CommonConstant;
import com.huawei.it.fcst.industry.price.dao.IDmFcstPriceVersionInfoDao;
import com.huawei.it.fcst.industry.price.dao.IDmRawDataExamineDao;
import com.huawei.it.fcst.industry.price.enums.GroupLevelEnum;
import com.huawei.it.fcst.industry.price.impl.common.AsyncPriceQueryService;
import com.huawei.it.fcst.industry.price.impl.template.DataReviewTemplateEnum;
import com.huawei.it.fcst.industry.price.service.common.IPriceCommonService;
import com.huawei.it.fcst.industry.price.service.config.IBottomDataReviewService;
import com.huawei.it.fcst.industry.price.vo.config.BottomDataReviewVO;
import com.huawei.it.fcst.industry.price.vo.config.DataReviewVO;
import com.huawei.it.fcst.industry.price.vo.config.DmRawDataExamineDTO;
import com.huawei.it.fcst.industry.price.vo.config.ExamineVO;
import com.huawei.it.fcst.industry.price.vo.version.DmFcstVersionInfoVO;
import com.huawei.it.fcst.util.ExcelExportUtil;
import com.huawei.it.fcst.util.ExcelUtils;
import com.huawei.it.fcst.util.UserInfoUtils;
import com.huawei.it.fcst.vo.AbstractExcelTitleVO;
import com.huawei.it.fcst.vo.ExcelVO;
import com.huawei.it.fcst.vo.ExportExcelVo;
import com.huawei.it.fcst.vo.HeaderVo;
import com.huawei.it.fcst.vo.LeafExcelTitleVO;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.fcst.vo.UploadInfoVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.core.util.StreamUtil;
import com.huawei.it.jalor5.registry.service.IRegistryQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Named;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/9/6
 */
@Slf4j
@Named("bottomDataReviewService")
@JalorResource(code = "bottomDataReviewService", desc = "定价指数-底层数据审视")
public class BottomDataReviewService implements IBottomDataReviewService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BottomDataReviewService.class);

    @Autowired
    private IDmRawDataExamineDao dmRawDataExamineDao;

    @Autowired
    private IExportProcessorService exportProcessorService;

    @Autowired
    private IDmFcstPriceVersionInfoDao dmFcstVersionInfoDao;

    @Autowired
    private IPriceCommonService commonService;

    @Autowired
    private ExcelUtils excelUtils;

    @Autowired
    private IRegistryQueryService registryQueryService;

    @Autowired
    private StatisticsExcelService statisticsExcelService;

    @Autowired
    private AsyncPriceQueryService asyncPriceQueryService;

    private static final Pattern DATE_PATTERN = Pattern.compile("\\d{4}(0[1-9]|1[0-2])");

    private static final Pattern SCIENT_NUMBER_PATTERN = Pattern.compile("^[+-]?\\d+\\.\\d+([eE][+-]?\\d+)?");

    private static final String BG_NAME_OLD = "运营商网络";

    private static final String BG_NAME_NEW = "运营商";

    private static final String ICT_OPERATION_PERIOD = "App.Config.Time.IctOperationPeriod";

    @JalorOperation(code = "findVersionList", desc = "获取版本下拉框")
    @Override
    public ResultDataVO findVersionList() throws CommonApplicationException {
        DmFcstVersionInfoVO versionInfoVO = new DmFcstVersionInfoVO();
        versionInfoVO.setDataType("DATA_REVIEW");
        return ResultDataVO.success(dmFcstVersionInfoDao.findVersionList(versionInfoVO));
    }

    /**
     * 上面表单各层级下拉框查询(依据版本动态查询)
     *
     * @param bottomDataReviewVO
     * @return
     * @throws ApplicationException
     */
    @JalorOperation(code = "getDataReviewDropboxList", desc = "不同层级下拉框查询")
    @Override
    public ResultDataVO getDataReviewDropboxList(BottomDataReviewVO bottomDataReviewVO) throws CommonApplicationException {
        return ResultDataVO.success(dmRawDataExamineDao.findExamineDropDownList(bottomDataReviewVO));
    }

    /**
     * 新增数据时各层级下拉框查询
     *
     * @param bottomDataReviewVO
     * @return
     * @throws ApplicationException
     */
    @JalorOperation(code = "getDataReviewEditDropboxList", desc = "编辑数据时层级下拉框查询")
    @Override
    public ResultDataVO getDataReviewEditDropboxList(BottomDataReviewVO bottomDataReviewVO) throws CommonApplicationException {
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(bottomDataReviewVO.getPageSize());
        pageVO.setCurPage(bottomDataReviewVO.getPageIndex());
        List<DmRawDataExamineDTO> rawDataExamineList = new ArrayList<>();
        if (GroupLevelEnum.SPART.getValue().equals(bottomDataReviewVO.getGroupLevel())) {
            PagedResult<DmRawDataExamineDTO> spartPageList = dmRawDataExamineDao.findSpartCodeByPageList(bottomDataReviewVO, pageVO);
            List<DmRawDataExamineDTO> result = spartPageList.getResult();
            pageVO = spartPageList.getPageVO();
            rawDataExamineList = result;
        } else {
            rawDataExamineList = dmRawDataExamineDao.findRawDataExamineList(bottomDataReviewVO);
        }
        Map result = new LinkedHashMap();
        result.put("result", rawDataExamineList);
        result.put("pageVo", pageVO);
        return ResultDataVO.success(result);
    }

    @JalorOperation(code = "findDataReviewByPage", desc = "分页查询")
    @Override
    public ResultDataVO findDataReviewByPage(BottomDataReviewVO bottomDataReviewVO) throws CommonApplicationException {
        if (ObjectUtils.isEmpty(bottomDataReviewVO.getPageIndex()) || ObjectUtils.isEmpty(
                bottomDataReviewVO.getPageSize())) {
            throw new CommonApplicationException("分页信息参数不正确");
        }
        PageVO pageVO = new PageVO();
        pageVO.setCurPage(bottomDataReviewVO.getPageIndex());
        pageVO.setPageSize(bottomDataReviewVO.getPageSize());
        // 数据库查询分页数据
        PagedResult<DmRawDataExamineDTO> reviewDataPageResult = dmRawDataExamineDao.findDataReviewListByPage(bottomDataReviewVO, pageVO);

        Map<String, Object> resultMap = new LinkedHashMap<>();
        resultMap.put("result", reviewDataPageResult.getResult());
        resultMap.put("pageVO", reviewDataPageResult.getPageVO());
        return ResultDataVO.success(resultMap);
    }

    @JalorOperation(code = "saveDataReview", desc = "保存数据")
    @Audit(module = "bottomDataReviewService-saveDataReview", operation = "saveDataReview", message = "保存数据")
    @Override
    public ResultDataVO saveDataReview(DataReviewVO dataReviewVO) throws Exception {

        List<DmRawDataExamineDTO> reviewList = dataReviewVO.getReviewList();
        // 查询当前版本对应维度数据
        List<DmRawDataExamineDTO> originExamineResultList = dmRawDataExamineDao.findAllDataReviewList(dataReviewVO);
        // 非空校验
        StringBuilder stringBuilder = new StringBuilder();
        checkReviewParam(reviewList, stringBuilder, false);
        // 剔除编辑操作后的数据
        List<DmRawDataExamineDTO> filterResultList = getDistinctRecordList(reviewList, originExamineResultList, dataReviewVO);
        if (CollectionUtils.isNotEmpty(filterResultList)) {
            // 校验与系统数据的准确性，时间是否重叠
            List<DmRawDataExamineDTO> insertModifyList = reviewList.stream().filter(review -> !CommonConstEnum.ModifyType.REVOKE.getValue().equals(review.getModifyType())).collect(Collectors.toList());
            checkReviewSystem(insertModifyList, filterResultList);
        }
        Long newVersionId = commonService.createNewVersionInfo("DATA_REVIEW");
        // 保存数据
        saveReviewData(reviewList, filterResultList, newVersionId, "data");
        return ResultDataVO.success(newVersionId);
    }

    @JalorOperation(code = "importDataReview", desc = "导入数据")
    @Audit(module = "bottomDataReviewService-importDataReview", operation = "importDataReview", message = "导入数据")
    @Override
    public ResultDataVO importDataReview(Attachment attachment, Long versionId) throws CommonApplicationException, IOException {
        ByteArrayOutputStream byteArrayOutputStream = null;
        try {
            if (null == versionId) {
                throw new CommonApplicationException("入参清单刷新版本为空");
            }
            Timestamp creationDate = new Timestamp(System.currentTimeMillis());

            List<ExcelVO> heads = new ArrayList<>();
            List<HeaderVo> model = new LinkedList<>();
            // 获取excel表头模型和行数
            Map<String, Object> headMap = getHeaderModuleMap(heads, model);
            // 导入信息记录对象
            Long userId = UserInfoUtils.getUserId();
            UploadInfoVO uploadInfoVO = getUploadInfoParam(attachment, headMap, userId);
            uploadInfoVO.setCreationDate(creationDate);
            byteArrayOutputStream = excelUtils.putInputStreamCacher(attachment.getDataHandler().getInputStream());
            List<LinkedHashMap<String, Object>> maps = validImpModel(attachment, heads, model, uploadInfoVO,
                    byteArrayOutputStream);
            // 将map数据转为json数组
            JSONArray jsonArray = mapToObject(model, maps);
            List<DmRawDataExamineDTO> dataReviewVOList = jsonArray.toJavaList(DmRawDataExamineDTO.class);
            List<DmRawDataExamineDTO> errorList = new ArrayList<>();
            StringBuilder errMsgBuilder = new StringBuilder();
            if (CollectionUtils.isEmpty(dataReviewVOList)) {
                throw new CommonApplicationException("导入的模板为空，没数据");
            }
            // 查询当前版本对应维度数据
            DataReviewVO dataReviewVO = new DataReviewVO();
            dataReviewVO.setVersionId(versionId);
            List<DmRawDataExamineDTO> examineResultList = dmRawDataExamineDao.findAllDataReviewList(dataReviewVO);
            // 非空校验，以及其他参数校验
            StringBuilder stringBuilder = new StringBuilder();
            checkReviewParam(dataReviewVOList, stringBuilder, true);
            String message = "";
            if (ObjectUtils.isNotEmpty(stringBuilder)) {
                uploadViewErrorFile(uploadInfoVO, dataReviewVOList, byteArrayOutputStream, stringBuilder);
                throw new CommonApplicationException("操作类型不合法");
            }
            // 数据的匹配校验
            Long annualVersionId = dmFcstVersionInfoDao.findVersionIdByDataType("ANNUAL").getVersionId();

            int errorCount = validDimensionData(dataReviewVOList, errorList, errMsgBuilder, annualVersionId, examineResultList);
            // 剔除修改或撤销操作类型的数据
            List<DmRawDataExamineDTO> allResultList = getDistinctRecordList(dataReviewVOList, examineResultList, dataReviewVO);
            // 整体性的校验
            checkImportExamineData(dataReviewVOList, allResultList, errMsgBuilder, annualVersionId);


            List<Map> normalList = getExamineNormalList(dataReviewVOList);
            List<Map> allDataList = getUploadDataList(uploadInfoVO, dataReviewVOList, errorList);
            if (0 == errorCount && ObjectUtils.isEmpty(errMsgBuilder)) {
                // 筛选出不存在的撤销记录，
                List<DmRawDataExamineDTO> existList = getExistList(dataReviewVO, dataReviewVOList, examineResultList);
                message = "本次共导入" + dataReviewVOList.size() + "条，成功导入" + dataReviewVO.getNum() + "条，失败录入0条";
                saveReviewExamineData(model, uploadInfoVO, existList, allResultList, normalList, allDataList);
            } else {
                // 上传文件
                uploadImpExpRecord(model, uploadInfoVO, dataReviewVOList, errMsgBuilder, normalList, allDataList);
                throw new CommonApplicationException("输入值不合法");
            }
            return ResultDataVO.success(message);
        } catch (ApplicationException exception) {
            throw new RuntimeException(exception);
        } finally {
            if (null != byteArrayOutputStream) {
                byteArrayOutputStream.close();
            }
        }
    }

    @JalorOperation(code = "downloadTemplate", desc = "导出模板下载")
    @Audit(module = "bottomDataReviewService-downloadTemplate", operation = "downloadTemplate", message = "导出模板下载")
    @Override
    public ResultDataVO downloadTemplate(DataReviewVO dataReviewVO, HttpServletResponse response) throws Exception {

        String exportTemplate = CommonConstant.DATA_REVIEW_TEMPLATE_PATH;
        String moduleType = CommonConstant.DATA_REVIEW_MODULE_TYPE;
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate(exportTemplate);
        String fileName = "配置管理".concat(moduleType.substring(moduleType.indexOf("-"))).concat("导入模板");
        // Excel文件下载到浏览器
        ExcelExportUtil.downloadExcel(workbook, fileName, response);
        // 插入记录，并上传导出文件到S3服务器
        insertExportRecord(0, workbook, fileName, moduleType);
        return ResultDataVO.success();
    }

    public void insertExportRecord(int totalRows, Workbook workbook, String fileName, String moduleType) throws IOException, CommonApplicationException {
        // 插入数据，上传文件
        Long userId = UserInfoUtils.getUserId();
        PbiDmFoiImpExpRecordVO dmImpExpRecordVO = StatisticsExcelService.uploadExportExcel(workbook, totalRows, fileName, userId);
        // 插入数据
        dmImpExpRecordVO.setModuleType(moduleType);
        dmImpExpRecordVO.setCreationDate(new Timestamp(System.currentTimeMillis()));
        dmImpExpRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        statisticsExcelService.insertExportExcelRecord(dmImpExpRecordVO);
    }

    private void saveReviewExamineData(List<HeaderVo> model,
                                       UploadInfoVO uploadInfoVO, List<DmRawDataExamineDTO> dataReviewVOList, List<DmRawDataExamineDTO> allResultList, List<Map> normalList,
                                       List<Map> dataList) throws ApplicationException, IOException {
        Long versionId = commonService.createNewVersionInfo("DATA_REVIEW");

        // 先汇总，然后异常录入页面需要去除type=撤销的
        allResultList.addAll(dataReviewVOList);
        List<DmRawDataExamineDTO> operationRecordList = allResultList.stream().filter(viewVO -> !CommonConstEnum.ModifyType.REVOKE.getValue().equals(viewVO.getModifyType())).collect(Collectors.toList());

        saveReviewData(dataReviewVOList, operationRecordList, versionId, "excel");

        // 记录上传成功excel到个人中心
        uploadImportSuccessFile(model, uploadInfoVO, normalList, dataList);
    }

    private void uploadImpExpRecord(List<HeaderVo> model, UploadInfoVO uploadInfoVO, List<DmRawDataExamineDTO> dataReviewVOList, StringBuilder errorBuilder, List<Map> normalList, List<Map> dimensionDataList) throws CommonApplicationException, IOException {
        PbiDmFoiImpExpRecordVO recordVO = uploadImportExcel(uploadInfoVO, dimensionDataList, model, normalList, false);
        // 截取异常反馈信息到2000
        checkFeedBackStr(errorBuilder, recordVO);
        recordVO.setCreationDate(uploadInfoVO.getCreationDate());
        recordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        recordVO.setExceptionFeedback(errorBuilder.toString());
        recordVO.setModuleType(CommonConstant.DATA_REVIEW_MODULE_TYPE);
        statisticsExcelService.insertImportExcel(recordVO, uploadInfoVO, false, dataReviewVOList.size());
    }

    private void uploadImportSuccessFile(List<HeaderVo> model, UploadInfoVO uploadInfoVO, List<Map> normalList,
                                         List<Map> dataList) throws CommonApplicationException, IOException {
        // 上传文件
        PbiDmFoiImpExpRecordVO recordVO = uploadImportExcel(uploadInfoVO, dataList, model, normalList, true);
        // 导入成功信息记录
        recordVO.setCreationDate(uploadInfoVO.getCreationDate());
        recordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        statisticsExcelService.insertImportExcel(recordVO, uploadInfoVO, true, uploadInfoVO.getRowNumber());
    }

    private List<Map> getExamineNormalList(List<DmRawDataExamineDTO> dataExamineList) {
        List<Map> normalList = new ArrayList<>();
        for (DmRawDataExamineDTO examineDTO : dataExamineList) {
            Map rawMap = new LinkedHashMap();
            putRawDataExamineMap(examineDTO, rawMap);
            normalList.add(rawMap);
        }
        return normalList;
    }

    private List<Map> getUploadDataList(UploadInfoVO uploadInfoVO, List<DmRawDataExamineDTO> dataReviewList,
                                        List<DmRawDataExamineDTO> errorList) {
        List<Map> dataList = new ArrayList<>();
        for (DmRawDataExamineDTO examineDTO : errorList) {
            Map modelMap = new LinkedHashMap();
            putRawDataExamineMap(examineDTO, modelMap);
            modelMap.put("errorMsg", examineDTO.getErrorMessage());
            dataList.add(modelMap);
        }
        uploadInfoVO.setRowNumber(dataReviewList.size());
        return dataList;
    }

    /**
     * 封装成map
     *
     * @param examineDTO 参数
     * @param rawDataMap     map
     */
    private void putRawDataExamineMap(DmRawDataExamineDTO examineDTO, Map rawDataMap) {
        CommonConstEnum.ModifyType modifyType = CommonConstEnum.getModifyType(examineDTO.getModifyType());
        if (null != modifyType) {
            rawDataMap.put("modifyType", modifyType.getDesc());
        } else {
            rawDataMap.put("modifyType", modifyType);
        }
        CommonConstEnum.OVERSEA_FLAG overSeaFlag = CommonConstEnum.getOverSeaFlag(examineDTO.getOverseaFlag());
        if (null != overSeaFlag) {
            rawDataMap.put("overseaFlag", overSeaFlag.getDesc());
        } else {
            rawDataMap.put("overseaFlag", examineDTO.getOverseaFlag());
        }
        rawDataMap.put("bgCnName", examineDTO.getBgCnName());
        rawDataMap.put("regionCnName", examineDTO.getRegionCnName());
        rawDataMap.put("repofficeCnName", examineDTO.getRepofficeCnName());
        rawDataMap.put("signTopCustCategoryCnName", examineDTO.getSignTopCustCategoryCnName());
        rawDataMap.put("signSubsidiaryCustcatgCnName", examineDTO.getSignSubsidiaryCustcatgCnName());
        rawDataMap.put("lv1CnName", examineDTO.getLv1CnName());
        rawDataMap.put("lv1Code", examineDTO.getLv1Code());
        rawDataMap.put("lv2CnName", examineDTO.getLv2CnName());
        rawDataMap.put("lv2Code", examineDTO.getLv2Code());
        rawDataMap.put("lv3CnName", examineDTO.getLv3CnName());
        rawDataMap.put("lv3Code", examineDTO.getLv3Code());
        rawDataMap.put("lv4CnName", examineDTO.getLv4CnName());
        rawDataMap.put("lv4Code", examineDTO.getLv4Code());
        rawDataMap.put("spartCode", examineDTO.getSpartCode());
        rawDataMap.put("hwContractNum", examineDTO.getHwContractNum());
        rawDataMap.put("beginDate", examineDTO.getBeginDate());
        rawDataMap.put("endDate", examineDTO.getEndDate());
        if (CommonConstant.MODIFY_TYPE.contains(examineDTO.getModifyType())) {
            rawDataMap.put("modifyReasonM", examineDTO.getModifyReason());
            rawDataMap.put("modifyReasonR", "");
        } else {
            rawDataMap.put("modifyReasonM", "");
            rawDataMap.put("modifyReasonR", examineDTO.getModifyReasonR());
        }
    }

    private void checkImportExamineData(List<DmRawDataExamineDTO> dataReviewVOList, List<DmRawDataExamineDTO> allResultList, StringBuilder errMsgBuilder, Long annualVersionId) throws CommonApplicationException {
        // 当前对象是否与系统数据重复(主要校验新增的数据跟系统是否重复，因修改和撤销的数据已经被去重了)，
        StringBuilder repeatBuilder = new StringBuilder();
        checkRepeatData(dataReviewVOList, allResultList, repeatBuilder);
        if (ObjectUtils.isNotEmpty(repeatBuilder)) {
            errMsgBuilder.append("在该时期范围内某些编码修改操作已存在;");
        }
        // 校验 spart和合同号是否存在
        Set<String> spartCodeSet = new HashSet<>();
        Set<String> hwContractNumSet = new HashSet<>();
        dataReviewVOList.forEach(vo -> {
            spartCodeSet.add(vo.getSpartCode());
            hwContractNumSet.add(vo.getHwContractNum());
        });
        ExamineVO examineVO = ExamineVO.builder().spartCodeSet(spartCodeSet)
                .hwContractNumSet(hwContractNumSet).build();

        // 查询数据库
        asyncPriceQueryService.findSpartContract(examineVO);
        if (spartCodeSet.size() != examineVO.getSpartCont()) {
            errMsgBuilder.append("某些SPART编码不存在;");
        }
        if (hwContractNumSet.size() != examineVO.getContractNumberCont()) {
            errMsgBuilder.append("某些合同号不存在;");
        }
    }

    private List<DmRawDataExamineDTO> getExistList(DataReviewVO dataReviewVO, List<DmRawDataExamineDTO> dataReviewVOList, List<DmRawDataExamineDTO> examineResultList) throws CommonApplicationException {
        Map<String, List<DmRawDataExamineDTO>> remokeMap = dataReviewVO.getRemokeMap();
        Map<String, List<DmRawDataExamineDTO>> originRemokeMap = examineResultList.stream().collect(Collectors.groupingBy(
                        this::getReviewConnectCode));
        Set<String> originKeySet = originRemokeMap.keySet();
        Set<String> keySet = remokeMap.keySet();

        List<String> diffKeySet = keySet.stream().filter(key -> !originKeySet.contains(key)).collect(Collectors.toList());
        List<DmRawDataExamineDTO> remokeDataReviewList = new ArrayList<>();
        diffKeySet.forEach(diff->{
            List<DmRawDataExamineDTO> remokeList = MapUtil.get(remokeMap, diff, List.class);
            remokeDataReviewList.addAll(remokeList);
        });

        int num = dataReviewVOList.size() - remokeDataReviewList.size();
        dataReviewVO.setNum(num);
        List<DmRawDataExamineDTO> existDataReviewList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(remokeDataReviewList)) {
            for (DmRawDataExamineDTO examineDTO : remokeDataReviewList) {
                List<DmRawDataExamineDTO> existList = dataReviewVOList.stream().filter(dataReview -> !dataReview.equals(examineDTO)).collect(Collectors.toList());
                existDataReviewList.addAll(existList);
            }
        } else {
            existDataReviewList.addAll(dataReviewVOList);
        }

        return existDataReviewList;
    }

    private boolean isScientifiNotation(String str) {
        if (SCIENT_NUMBER_PATTERN.matcher(str).matches()) {
            return true;
        }
        return false;
    }

    private void uploadViewErrorFile(UploadInfoVO uploadInfoVO, List<DmRawDataExamineDTO> dataReviewVOList, ByteArrayOutputStream byteArrayOutputStream, StringBuilder stringBuilder) throws CommonApplicationException, IOException {
        InputStream inputStream = null;
        try {
            PbiDmFoiImpExpRecordVO dmImpExpRecordVO = new PbiDmFoiImpExpRecordVO();
            dmImpExpRecordVO.setExceptionFeedback(stringBuilder.toString());
            inputStream = excelUtils.getInputStream(byteArrayOutputStream);
            statisticsExcelService.getImportUploadFileKey(dmImpExpRecordVO, uploadInfoVO.getUserId(), 1,
                    inputStream);
            dmImpExpRecordVO.setCreationDate(uploadInfoVO.getCreationDate());
            dmImpExpRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
            dmImpExpRecordVO.setModuleType(CommonConstant.DATA_REVIEW_MODULE_TYPE);
            statisticsExcelService.insertImportExcel(dmImpExpRecordVO, uploadInfoVO, false, dataReviewVOList.size());
        } catch (IOException exception) {
            LOGGER.info("上传s3错误：{}", exception.getMessage());
        } finally {
            StreamUtil.closeStreams(inputStream);
            StreamUtil.closeStreams(byteArrayOutputStream);
        }
    }

    public PbiDmFoiImpExpRecordVO uploadImportExcel(UploadInfoVO uploadVO, List<Map> dataList, List<HeaderVo> model,
                                                 List<Map> normalList, boolean flag) throws CommonApplicationException, IOException {
        // 上传源文件
        PbiDmFoiImpExpRecordVO dmImpExpRecordVO = exportRelationExcel(normalList, uploadVO, model);
        String fileSourceKey = dmImpExpRecordVO.getFileSourceKey();

        PbiDmFoiImpExpRecordVO recordVO = new PbiDmFoiImpExpRecordVO();
        if (!flag) {
            model.add(new HeaderVo("错误信息", "errorMsg", CellType.STRING, false, 12 * 480));
            // 上传异常文件
            recordVO = exportRelationExcel(dataList, uploadVO, model);
            String fileErrorKey = recordVO.getFileSourceKey();
            recordVO.setFileErrorKey(fileErrorKey);
        }
        recordVO.setFileSourceKey(fileSourceKey);
        return recordVO;
    }

    private PbiDmFoiImpExpRecordVO exportRelationExcel(List<Map> dataList, UploadInfoVO uploadInfoVO,
                                                    List<HeaderVo> headers) throws CommonApplicationException, IOException {
        String sheetName = uploadInfoVO.getSheetName();
        List<AbstractExcelTitleVO> titleVOList = new ArrayList<>();

        // 设置第二行表头模型
        Set<String> titles = new LinkedHashSet<>();
        setHeader(titleVOList, headers, titles);
        ExportExcelVo exportExcelVO = ExportExcelVo.builder()
                .titleVoList(titleVOList)
                .list(dataList)
                .sheetName(sheetName)
                .mergeCell(false)
                .build();
        List<ExportExcelVo> exportExcelVoList = new ArrayList<>();
        exportExcelVoList.add(exportExcelVO);
        return excelUtils.expSelectColumnExcel(exportExcelVoList, null);
    }

    private void checkFeedBackStr(StringBuilder errorBuilder, PbiDmFoiImpExpRecordVO DmFoiImpExpRecordVO)
            throws UnsupportedEncodingException {
        String errorFeedback = errorBuilder.toString();
        int length = errorFeedback.getBytes("UTF-8").length;
        if (length >= 2000) {
            String subErrorFeedback = errorFeedback.substring(0, 666);
            DmFoiImpExpRecordVO.setExceptionFeedback(subErrorFeedback);
        } else {
            DmFoiImpExpRecordVO.setExceptionFeedback(errorFeedback);
        }
    }

    private void setHeader(List<AbstractExcelTitleVO> titleVOList, List<HeaderVo> headers, Set<String> titles) {
        LeafExcelTitleVO column;
        for (HeaderVo header : headers) {
            column = new LeafExcelTitleVO(header.getTitle(), header.getWidth(), true, header.getField(),
                    header.getField(), header.getDataType(), header.getIsEditable());
            titleVOList.add(column);
            titles.add(header.getField());
        }
    }

    private int validDimensionData(List<DmRawDataExamineDTO> dataReviewVOList, List<DmRawDataExamineDTO> errorList, StringBuilder errMsgBuilder, Long versionId, List<DmRawDataExamineDTO> examineResultList) {
        AtomicInteger atomicInteger = new AtomicInteger();
        // 查询底表数据
        BottomDataReviewVO bottomDataReviewVO = new BottomDataReviewVO();

        // 所有重量级团队
        List<DmRawDataExamineDTO> prodTeamCodeRawDataList = dmRawDataExamineDao.findProdCodeRawDataExamineList(bottomDataReviewVO);
        // bg，国内海外等
        List<DmRawDataExamineDTO> otherRawDataList = dmRawDataExamineDao.findOtherRawDataExamineList(bottomDataReviewVO);
        // 起始终止时间
        DmRawDataExamineDTO rawDataExamineDTO = dmRawDataExamineDao.findPriceBeginEndDate();

        // 设置重量级团队
        ExamineVO examineVO = setLvMap(prodTeamCodeRawDataList);
        examineVO.setBegin(rawDataExamineDTO.getBegin());
        examineVO.setEnd(rawDataExamineDTO.getEnd());
        Map<String, String> bgMap = new HashMap<>();
        Map<String, String> regionMap = new HashMap<>();
        Map<String, String> repofficeMap = new HashMap<>();
        Map<String, String> signTopCustCategoryMap = new HashMap<>();
        Set<String> signSubsidiaryCustcatgSet = new HashSet<>();
        otherRawDataList.forEach(other -> {
            bgMap.put(other.getBgCnName(), other.getBgCode());
            regionMap.put(other.getRegionCnName(), other.getRegionCode());
            repofficeMap.put(other.getRepofficeCnName(), other.getRepofficeCode());
            signTopCustCategoryMap.put(other.getSignTopCustCategoryCnName(), other.getSignTopCustCategoryCode());
            signSubsidiaryCustcatgSet.add(other.getSignSubsidiaryCustcatgCnName());
        });
        examineVO.setBgMap(bgMap);
        examineVO.setRegionMap(regionMap);
        examineVO.setRepofficeMap(repofficeMap);
        examineVO.setSignTopCustCategoryMap(signTopCustCategoryMap);
        examineVO.setSignSubsidiaryCustcatgSet(signSubsidiaryCustcatgSet);
        examineVO.setOverseaFlagMap(CommonConstant.overseaFlagMap);
        dataReviewVOList.forEach(reviewVO -> {
            try {
                atomicInteger.addAndGet(setExamineErrorCount(reviewVO, examineVO, errorList, errMsgBuilder, examineResultList));
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
        });
        return atomicInteger.get();
    }

    private ExamineVO setLvMap(List<DmRawDataExamineDTO> prodTeamCodeList) {
        Map<String, String> lv1ProdListMap = new HashMap<>();
        Map<String, String> lv2ProdListMap = new HashMap<>();
        Map<String, String> lv3ProdListMap = new HashMap<>();
        Map<String, String> lv4ProdListMap = new HashMap<>();
        prodTeamCodeList.forEach(prodTeamCode -> {
            lv1ProdListMap.put(prodTeamCode.getBgCnName() + "_" + prodTeamCode.getLv1ProdListCnName(), prodTeamCode.getLv1ProdListCode());
            lv2ProdListMap.put(prodTeamCode.getBgCnName() + "_" +prodTeamCode.getLv2ProdListCnName(), prodTeamCode.getLv2ProdListCode());
            lv3ProdListMap.put(prodTeamCode.getBgCnName() + "_" +prodTeamCode.getLv3ProdListCnName(), prodTeamCode.getLv3ProdListCode());
            lv4ProdListMap.put(prodTeamCode.getBgCnName() + "_" +prodTeamCode.getLv4ProdListCnName(), prodTeamCode.getLv4ProdListCode());
        });
        return ExamineVO.builder()
                .lv1ProdListMap(lv1ProdListMap)
                .lv2ProdListMap(lv2ProdListMap)
                .lv3ProdListMap(lv3ProdListMap)
                .lv4ProdListMap(lv4ProdListMap)
                .build();
    }

    private int setExamineErrorCount(DmRawDataExamineDTO rawExamineDTO,
                                     ExamineVO examineVO, List<DmRawDataExamineDTO> errorList, StringBuilder errMsgBuilder, List<DmRawDataExamineDTO> examineResultList) throws UnsupportedEncodingException {
        StringBuilder builder = new StringBuilder();
        int errorCount = 0;
        // 非空校验
        validEmptyColumn(rawExamineDTO, builder);
        // 校验编码和名称，spart如果出现科学计数法需要重新设置值
        validProdTeamData(rawExamineDTO, builder, examineVO);
        // 校验bg，国内，海外,大T等
        validOtherData(rawExamineDTO, builder, examineVO);
        // 校验起始期终止期是否在范围内
        validDateData(rawExamineDTO, builder, examineVO);

        String reviewConnectStr = getReviewExamineConnectName(rawExamineDTO);
        StringBuilder overBuilder = new StringBuilder();
        // 新增需要校验
        if (CommonConstEnum.ModifyType.INSERT.getValue().equals(rawExamineDTO.getModifyType())) {
            List<DmRawDataExamineDTO> examineResult = examineResultList.stream().filter(oneVO -> reviewConnectStr.equals(getReviewExamineConnectName(oneVO))).collect(Collectors.toList());
            examineResult.forEach(oneVO -> {
                // 比较起始终止日期
                compareStartAndEndDate(overBuilder, rawExamineDTO, oneVO, reviewConnectStr);
            });
            // 表示有时间重叠
            if (overBuilder.length() > 0) {
                builder.append(overBuilder + "和已存在数据起始终止时间重叠;");
            }
        }
        if (builder.length() > 0) {
            errorCount = errorCount + 1;
            rawExamineDTO.setErrorMessage(builder.toString());
            errMsgBuilder.append(builder);
        }
        errorList.add(rawExamineDTO);
        return errorCount;
    }

    private void validOtherData(DmRawDataExamineDTO dataExamineDTO, StringBuilder builder, ExamineVO examineVO) {
        // 校验编码和名称是否匹配
        Map<String, String> bgMap = examineVO.getBgMap();

        Map<String, String> signTopCustCategoryMap = examineVO.getSignTopCustCategoryMap();

        Set<String> bgCnNameSet = bgMap.keySet();
        Set<String> signTopCustCategorySet = signTopCustCategoryMap.keySet();
        Set<String> signSubsidiaryCustcatgSet = examineVO.getSignSubsidiaryCustcatgSet();

        if (!bgCnNameSet.contains(dataExamineDTO.getBgCnName())) {
            String bgStr = "bg名称(" + dataExamineDTO.getBgCnName() + ")不存在;";
            builder.append(bgStr);
        } else {
            // 获取到bg名称对应的编码
            dataExamineDTO.setBgCode(bgMap.get(dataExamineDTO.getBgCnName()));
        }
        if (BG_NAME_OLD.equals(dataExamineDTO.getBgCnName()) || BG_NAME_NEW.equals(dataExamineDTO.getBgCnName())) {
            if (StringUtils.isAllBlank(dataExamineDTO.getOverseaFlag(), dataExamineDTO.getRegionCnName(), dataExamineDTO.getRepofficeCnName())) {
                dataExamineDTO.setOverseaFlag(null);
                dataExamineDTO.setRegionCnName(null);
                dataExamineDTO.setRepofficeCnName(null);
                if (!signTopCustCategorySet.contains(dataExamineDTO.getSignTopCustCategoryCnName())) {
                    String signTopCustCategoryStr = "大T系统部名称(" + dataExamineDTO.getSignTopCustCategoryCnName() + ")不存在;";
                    builder.append(signTopCustCategoryStr);
                } else {
                    dataExamineDTO.setSignTopCustCategoryCode(signTopCustCategoryMap.get(dataExamineDTO.getSignTopCustCategoryCnName()));
                }
                if (!signSubsidiaryCustcatgSet.contains(dataExamineDTO.getSignSubsidiaryCustcatgCnName())) {
                    String signSubsidiaryCustcatgStr = "子网系统名称(" + dataExamineDTO.getSignSubsidiaryCustcatgCnName() + ")不存在;";
                    builder.append(signSubsidiaryCustcatgStr);
                }
            } else {
                // 国内,海外，地区部，代表处
                getLocationName(examineVO, dataExamineDTO, builder);
            }
        } else {
            getLocationName(examineVO, dataExamineDTO, builder);
        }
    }

    private void getLocationName(ExamineVO examineVO, DmRawDataExamineDTO dataExamineDTO, StringBuilder builder) {
        if (StringUtils.isAllBlank(dataExamineDTO.getSignTopCustCategoryCnName(), dataExamineDTO.getSignSubsidiaryCustcatgCnName())) {
            // 把默认的大T或子网为""的设置为null
            dataExamineDTO.setSignTopCustCategoryCnName(null);
            dataExamineDTO.setSignSubsidiaryCustcatgCnName(null);
            Map<String, String> regionMap = examineVO.getRegionMap();

            Map<String, String> repofficeMap = examineVO.getRepofficeMap();
            Map<String, String> overseaFlagMap = examineVO.getOverseaFlagMap();
            Set<String> regionSet = regionMap.keySet();
            Set<String> repofficeSet = repofficeMap.keySet();
            Set<String> overseaFlagSet = overseaFlagMap.keySet();
            if (!regionSet.contains(dataExamineDTO.getRegionCnName())) {
                String regionStr = "地区部名称(" + dataExamineDTO.getRegionCnName() + ")不存在;";
                builder.append(regionStr);
            } else {
                dataExamineDTO.setRegionCode(regionMap.get(dataExamineDTO.getRegionCnName()));
            }
            if (!repofficeSet.contains(dataExamineDTO.getRepofficeCnName())) {
                String repofficeCnNameStr = "代表处名称(" + dataExamineDTO.getRepofficeCnName() + ")不存在;";
                builder.append(repofficeCnNameStr);
            } else {
                dataExamineDTO.setRepofficeCode(repofficeMap.get(dataExamineDTO.getRepofficeCnName()));
            }
            if (!overseaFlagSet.contains(dataExamineDTO.getOverseaFlag())) {
                String overseaFlagStr = "国内/海外名称(" + dataExamineDTO.getOverseaFlag() + ")不存在;";
                builder.append(overseaFlagStr);
            } else {
                dataExamineDTO.setOverseaFlag(overseaFlagMap.get(dataExamineDTO.getOverseaFlag()));
            }
        } else {
            // 国内海外，地区部代表处不为空，且大T,子网也不为空
            String errorStr = "国内/海外、地区部、代表处不为空,大T、子网需要为空";
            builder.append(errorStr);
        }
    }

    private void validDateData(DmRawDataExamineDTO rawDataExamineDTO, StringBuilder builder, ExamineVO examineVO) {
        if (null == rawDataExamineDTO.getBeginDate() || !DATE_PATTERN.matcher(String.valueOf(rawDataExamineDTO.getBeginDate())).matches() || examineVO.getBegin() > rawDataExamineDTO.getBeginDate()|| rawDataExamineDTO.getBeginDate() > rawDataExamineDTO.getEndDate()) {
            builder.append("起始期值不合法;");
        }
        if (null == rawDataExamineDTO.getEndDate() || !DATE_PATTERN.matcher(String.valueOf(rawDataExamineDTO.getEndDate())).matches() || examineVO.getEnd() < rawDataExamineDTO.getEndDate()) {
            builder.append("终止期值不合法;");
        }
    }

    private void validEmptyColumn(DmRawDataExamineDTO rawExamineDTO, StringBuilder builder) throws UnsupportedEncodingException {
        if (StringUtils.isBlank(rawExamineDTO.getModifyType())) {
            builder.append("操作类型为空;");
        } else {
            String modifyTypeValue = CommonConstEnum.getModifyTypeValue(rawExamineDTO.getModifyType());
            rawExamineDTO.setModifyType(modifyTypeValue);
        }
        // 判断字段是否为空
        validSubEmptyColumn(rawExamineDTO, builder);
        if (CommonConstant.MODIFY_TYPE.contains(rawExamineDTO.getModifyType())) {
            if (StringUtils.isBlank(rawExamineDTO.getModifyReasonM())) {
                builder.append("修改理由为空;");
            } else {
                // 裁剪字符
                rawExamineDTO.setModifyReason(rawExamineDTO.getModifyReasonM());
                subModifyReasonLength(rawExamineDTO);
            }
        } else {
            if (StringUtils.isBlank(rawExamineDTO.getModifyReasonR())) {
                builder.append("撤销理由为空;");
            } else {
                // 裁剪字符
                rawExamineDTO.setModifyReason(rawExamineDTO.getModifyReasonR());
                subModifyReasonLength(rawExamineDTO);
            }
        }
    }

    private void validSubEmptyColumn(DmRawDataExamineDTO rawExamineDTO, StringBuilder builder) throws UnsupportedEncodingException {
        if (StringUtils.isBlank(rawExamineDTO.getBgCnName())) {
            builder.append("bg为空;");
        }
        if (BG_NAME_OLD.equals(rawExamineDTO.getBgCnName()) || BG_NAME_NEW.equals(rawExamineDTO.getBgCnName())) {
            if (StringUtils.isAllBlank(rawExamineDTO.getOverseaFlag(), rawExamineDTO.getRegionCnName(), rawExamineDTO.getRepofficeCnName())) {
                if (StringUtils.isBlank(rawExamineDTO.getSignTopCustCategoryCnName())) {
                    builder.append("大T系统部为空;");
                }
                if (StringUtils.isBlank(rawExamineDTO.getSignSubsidiaryCustcatgCnName())) {
                    builder.append("子网系统为空;");
                }
            } else {
                checkEmptySub(rawExamineDTO, builder);
            }
        } else {
            checkEmptySub(rawExamineDTO, builder);
        }
        if (StringUtils.isBlank(rawExamineDTO.getLv1CnName())) {
            builder.append("L1为空;");
        }
        if (StringUtils.isBlank(rawExamineDTO.getLv2CnName())) {
            builder.append("L2为空;");
        }
        if (StringUtils.isBlank(rawExamineDTO.getLv3CnName())) {
            builder.append("L3为空;");
        }
        if (StringUtils.isBlank(rawExamineDTO.getLv4CnName())) {
            builder.append("L3.5为空;");
        }
        if (null == rawExamineDTO.getHwContractNum()) {
            builder.append("合同号为空;");
        } else {
            // 去空格和换行符
            String hwContractNum = rawExamineDTO.getHwContractNum();
            rawExamineDTO.setHwContractNum(trimColumn(hwContractNum));
        }
        if (null == rawExamineDTO.getSpartCode()) {
            builder.append("SPART编码为空;");
        } else {
            // 去空格和换行符
            String spartCode = rawExamineDTO.getSpartCode();
            rawExamineDTO.setSpartCode(trimColumn(spartCode));
        }
        if (null == rawExamineDTO.getBeginDate()) {
            builder.append("起始期为空;");
        }
        if (null == rawExamineDTO.getEndDate()) {
            builder.append("终止期为空;");
        }
    }

    private void checkEmptySub(DmRawDataExamineDTO rawDataExamineDTO, StringBuilder builder) {
        if (StringUtils.isBlank(rawDataExamineDTO.getOverseaFlag())) {
            builder.append("国内/海外为空;");
        }
        if (StringUtils.isBlank(rawDataExamineDTO.getRegionCnName())) {
            builder.append("地区部为空;");
        }
        if (StringUtils.isBlank(rawDataExamineDTO.getRepofficeCnName())) {
            builder.append("代表处为空;");
        }
    }

    private String trimColumn(String str) {
        return str.trim().replace("\n", "").replace("\r", "");
    }

    private void validProdTeamData(DmRawDataExamineDTO rawDataExamineDTO,
                                   StringBuilder builder, ExamineVO examineVO) {
        // 校验编码和名称是否匹配
        validProdProdTeam(rawDataExamineDTO, examineVO, builder);
        // 防止科学计数法
        if (isScientifiNotation(rawDataExamineDTO.getSpartCode())) {
            rawDataExamineDTO.setSpartCode(new BigDecimal(rawDataExamineDTO.getSpartCode()).toPlainString());
        }
    }

    private void validProdProdTeam(DmRawDataExamineDTO rawExamineDTO, ExamineVO examineVO, StringBuilder builder) {

        Map<String, String> lv1ProdListMap = examineVO.getLv1ProdListMap();
        Map<String, String> lv2ProdListMap = examineVO.getLv2ProdListMap();
        Map<String, String> lv3ProdListMap = examineVO.getLv3ProdListMap();
        Map<String, String> lv4ProdListMap = examineVO.getLv4ProdListMap();

        Set<String> lv1ProdListCnName = lv1ProdListMap.keySet();
        Set<String> lv2ProdListCnName = lv2ProdListMap.keySet();
        Set<String> lv3ProdListCnName = lv3ProdListMap.keySet();
        Set<String> lv4ProdListCnName = lv4ProdListMap.keySet();
        String lv1WithBg = rawExamineDTO.getBgCnName() + "_" + rawExamineDTO.getLv1CnName();
        String lv2WithBg = rawExamineDTO.getBgCnName() + "_" + rawExamineDTO.getLv2CnName();
        String lv3WithBg = rawExamineDTO.getBgCnName() + "_" + rawExamineDTO.getLv3CnName();
        String lv4WithBg = rawExamineDTO.getBgCnName() + "_" + rawExamineDTO.getLv4CnName();
        if (!lv1ProdListCnName.contains(lv1WithBg)) {
            String lv1ProdListStr = "L1名称(" + rawExamineDTO.getLv1CnName() + ")不存在;";
            builder.append(lv1ProdListStr);
        } else {
            rawExamineDTO.setLv1Code(lv1ProdListMap.get(lv1WithBg));
        }
        if (!lv2ProdListCnName.contains(lv2WithBg)) {
            String lv2ProdListStr = "L2名称(" + rawExamineDTO.getLv2CnName() + ")不存在;";
            builder.append(lv2ProdListStr);
        } else {
            rawExamineDTO.setLv2Code(lv2ProdListMap.get(lv2WithBg));
        }
        if (!lv3ProdListCnName.contains(lv3WithBg)) {
            String lv3ProdListStr = "L3名称(" + rawExamineDTO.getLv3CnName() + ")不存在;";
            builder.append(lv3ProdListStr);
        } else {
            rawExamineDTO.setLv3Code(lv3ProdListMap.get(lv3WithBg));
        }
        if (!lv4ProdListCnName.contains(lv4WithBg)) {
            String lv4ProdListStr = "L3.5名称(" + rawExamineDTO.getLv4CnName() + ")不存在;";
            builder.append(lv4ProdListStr);
        } else {
            rawExamineDTO.setLv4Code(lv4ProdListMap.get(lv4WithBg));
        }
    }

    /**
     * @param attachment            参数
     * @param heads                 参数
     * @param model                 参数
     * @param uploadVO              参数
     * @param byteArrayOutputStream 参数
     * @return List 结果
     * @throws CommonApplicationException
     * @throws IOException
     */
    public List<LinkedHashMap<String, Object>> validImpModel(Attachment attachment, List<ExcelVO> heads,
                                                             List<HeaderVo> model, UploadInfoVO uploadVO, ByteArrayOutputStream byteArrayOutputStream)
            throws CommonApplicationException, IOException {
        List<LinkedHashMap<String, Object>> maps = new ArrayList<LinkedHashMap<String, Object>>();
        try {
            maps = excelUtils.importExcel(attachment, heads, uploadVO, byteArrayOutputStream);
        } catch (IOException e) {
            LOGGER.info("prce dataReview importExcel failed {}", e.getMessage());
        }
        InputStream inputStream = null;
        InputStream inputStreamByte = null;
        try {
            if (CollectionUtils.isEmpty(maps)) {
                PbiDmFoiImpExpRecordVO dmImpExpRecordVO = new PbiDmFoiImpExpRecordVO();
                dmImpExpRecordVO.setExceptionFeedback("列名出错");
                inputStream = excelUtils.getInputStream(byteArrayOutputStream);
                statisticsExcelService.getImportUploadFileKey(dmImpExpRecordVO, uploadVO.getUserId(), 1,
                        inputStream);
                dmImpExpRecordVO.setCreationDate(uploadVO.getCreationDate());
                dmImpExpRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                statisticsExcelService.insertImportExcel(dmImpExpRecordVO, uploadVO, false, 0);
                throw new CommonApplicationException("列名出错");
            }
            // 模板校验
            List<String> firstModelList = model.stream().map(HeaderVo::getTitle).collect(Collectors.toList());
            List<Object> firstValueList = maps.get(0).values().stream().collect(Collectors.toList());
            if (model.size() != maps.get(0).size() || firstValueList.containsAll(firstModelList)) {
                PbiDmFoiImpExpRecordVO dmImpExpRecordVO = new PbiDmFoiImpExpRecordVO();
                dmImpExpRecordVO.setExceptionFeedback("列名出错");
                inputStream = excelUtils.getInputStream(byteArrayOutputStream);
                dmImpExpRecordVO.setCreationDate(uploadVO.getCreationDate());
                dmImpExpRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                statisticsExcelService.getImportUploadFileKey(dmImpExpRecordVO, uploadVO.getUserId(), 1, inputStream);
                statisticsExcelService.insertImportExcel(dmImpExpRecordVO, uploadVO, false, 0);
                throw new CommonApplicationException("列名出错");
            } else {
                for (HeaderVo headerVo : model) {
                    String title = headerVo.getTitle();
                    Map<String, Object> objectMap = maps.get(0);
                    if (!objectMap.containsKey(title)) {
                        PbiDmFoiImpExpRecordVO dmFoiImpExpRecordVO = new PbiDmFoiImpExpRecordVO();
                        dmFoiImpExpRecordVO.setExceptionFeedback("列名出错");
                        inputStreamByte = excelUtils.getInputStream(byteArrayOutputStream);
                        dmFoiImpExpRecordVO.setCreationDate(uploadVO.getCreationDate());
                        dmFoiImpExpRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                        statisticsExcelService.getImportUploadFileKey(dmFoiImpExpRecordVO, uploadVO.getUserId(), 1, inputStreamByte);
                        statisticsExcelService.insertImportExcel(dmFoiImpExpRecordVO, uploadVO, false, 0);
                        throw new CommonApplicationException("列名出错");
                    }
                }
            }
        } catch (IOException e) {
            LOGGER.info("读取列名出错：{}", e.getMessage());
        } finally {
            StreamUtil.closeStreams(inputStream);
            StreamUtil.closeStreams(inputStreamByte);
            StreamUtil.closeStreams(byteArrayOutputStream);
        }
        return maps;
    }

    /**
     * [服务名称]mapToObject
     *
     * @param model 参数
     * @param maps  参数
     * @return JSONArray
     */
    public JSONArray mapToObject(List<HeaderVo> model, List<LinkedHashMap<String, Object>> maps) {
        JSONArray jsonArray = new JSONArray();
        for (int i = 0; i < maps.size(); i++) {
            Map<String, Object> dataMap = maps.get(i);
            JSONObject jsonObject = new JSONObject();
            Set<String> keySets = dataMap.keySet();
            mapToObjectSub(model, dataMap, jsonObject, keySets);
            jsonArray.add(jsonObject);
        }
        return jsonArray;
    }

    private void mapToObjectSub(List<HeaderVo> model, Map<String, Object> dataMap, JSONObject jsonObject,
                                Set<String> keySets) {
        for (String value : keySets) {
            for (HeaderVo headerVO : model) {
                if (value.equals(headerVO.getTitle())) {
                    String field = formatField(headerVO.getField(), CommonConstant.PATTERN_COLUMN);
                    jsonObject.put(field, dataMap.get(headerVO.getTitle()));
                }
            }
        }
    }

    private String formatField(String field, Pattern pattern) {
        Matcher matcher = pattern.matcher(field);
        if (matcher.find()) {
            String group = matcher.group(1);
            String replaceStr = field.replace(group, matcher.group(2).toUpperCase(Locale.ROOT));
            field = formatField(replaceStr, pattern);
        }
        return field;
    }

    private Map<String, Object> getHeaderModuleMap(List<ExcelVO> heads, List<HeaderVo> model) {
        Map<String, Object> params = new HashMap<>();
        model.addAll(CommonConstant.DATA_REVIEW_COLUMN_HEADER);
        setHeaderValues(heads, model);
        params.put("module", CommonConstant.DATA_REVIEW_MODULE_TYPE);
        try {
            Long maxRowNum = NumberUtil.parseLong(
                    registryQueryService.findValueByPath("Jalor.Excel.ExcelImportMaxCount", true));
            params.put("maxRowNum", maxRowNum);
        } catch (ApplicationException exception) {
            LOGGER.info("find maxRowNum error {}", exception.getMessage());
        }
        return params;
    }

    private void setHeaderValues(List<ExcelVO> excelVOlist, List<HeaderVo> headerVOList) {
        for (HeaderVo header : headerVOList) {
            ExcelVO vo = new ExcelVO();
            vo.setHeadName(header.getTitle());
            vo.setHeadType(CommonConstant.VARCHAR);
            excelVOlist.add(vo);
        }
    }

    private String getFilename(Attachment attachment) throws UnsupportedEncodingException {
        String fileName = new String(attachment.getDataHandler().getName().getBytes("ISO8859-1"), "UTF-8");
        int index = fileName.indexOf(".");
        return fileName.substring(0, index);
    }

    private UploadInfoVO getUploadInfoParam(Attachment attachment,
                                            Map<String, Object> headMap, Long userId) throws IOException {
        UploadInfoVO infoVO = new UploadInfoVO();
        infoVO.setFileName(getFilename(attachment).concat(new SimpleDateFormat("_yyyy-MM-dd HH_mm_ss").format(new Date())));
        infoVO.setFileSize(attachment.getDataHandler().getInputStream().available() / 1024);
        infoVO.setParams(headMap);
        infoVO.setUserId(userId);
        return infoVO;
    }

    private String getReviewConnectCode(DmRawDataExamineDTO paramVO) {
        return paramVO.getSpartCode() + "#" + paramVO.getOverseaFlag()
                + "#" + paramVO.getBgCnName() + "#" + paramVO.getRegionCnName()
                + "#" + paramVO.getRepofficeCnName() + "#" + paramVO.getSignTopCustCategoryCode() + "#" + paramVO.getSignSubsidiaryCustcatgCnName()
                + "#" + paramVO.getLv1CnName() + "#" + paramVO.getLv2CnName()
                + "#" + paramVO.getLv3CnName() + "#" + paramVO.getLv4CnName() + "#" + paramVO.getHwContractNum()
                + "#" + paramVO.getBeginDate() + "#" + paramVO.getEndDate();
    }

    private String getReviewExamineConnectName(DmRawDataExamineDTO paramVO) {
        return paramVO.getSpartCode() + "#" + paramVO.getOverseaFlag()
                + "#" + paramVO.getBgCnName() + "#" + paramVO.getRegionCnName()
                + "#" + paramVO.getRepofficeCnName() + "#" + paramVO.getSignTopCustCategoryCode() + "#" + paramVO.getSignSubsidiaryCustcatgCnName()
                + "#" + paramVO.getLv1CnName() + "#" + paramVO.getLv2CnName()
                + "#" + paramVO.getLv3CnName() + "#" + paramVO.getLv4CnName() + "#" + paramVO.getHwContractNum();
    }

    private void saveReviewData(List<DmRawDataExamineDTO> reviewList, List<DmRawDataExamineDTO> resultList, Long versionId, String type) {
        reviewList.forEach(examineDTO -> {
            examineDTO.setCreatedBy(UserInfoUtils.getUserCn());
            examineDTO.setCreationDate(new Date());
            examineDTO.setDelFlag(CommonConstant.IS_NOT);
            examineDTO.setVersionId(versionId);
            examineDTO.setLastUpdateDate(new Date());
            examineDTO.setLastUpdatedBy(UserInfoUtils.getUserCn());
            examineDTO.setDelFlag(CommonConstant.IS_NOT);
            examineDTO.setPageFlag(CommonConstant.HISTORY_PAGE);
        });
        dmRawDataExamineDao.createPriceDataReviewList(reviewList);
        if ("data".equals(type)) {
            List<DmRawDataExamineDTO> insertModifyList = reviewList.stream().filter(review -> !CommonConstEnum.ModifyType.REVOKE.getValue().equals(review.getModifyType())).collect(Collectors.toList());
            resultList.addAll(insertModifyList);
        }
        resultList.forEach(vo -> {
            vo.setVersionId(versionId);
            vo.setPageFlag(CommonConstant.ABNORMAL_PAGE);
        });
        dmRawDataExamineDao.createPriceDataReviewList(resultList);
    }

    private void checkReviewParam(List<DmRawDataExamineDTO> dataExamineList, StringBuilder stringBuilder, boolean flag)
            throws CommonApplicationException, UnsupportedEncodingException {
        // 非空校验，以及截取修改理由长度
        if (flag) {
            Set<String> modifyTypeSet = dataExamineList.stream().map(DmRawDataExamineDTO::getModifyType).collect(Collectors.toSet());
            List<String> modifyTypeList = EnumSet.allOf(CommonConstEnum.ModifyType.class).stream().map(CommonConstEnum.ModifyType::getDesc).collect(Collectors.toList());
            if (modifyTypeSet.stream().noneMatch(modifyType -> modifyTypeList.contains(modifyType))) {
                stringBuilder.append("操作类型全部错误;");
            }
        } else {
            StringBuilder strBuilder = checkDataEmpty(dataExamineList, stringBuilder);
            if (ObjectUtils.isNotEmpty(strBuilder)) {
                throw new CommonApplicationException("检测到:" + strBuilder);
            }
        }
    }

    private void checkExamineData(StringBuilder paramBuilder, List<DmRawDataExamineDTO> paramEntryValue, List<DmRawDataExamineDTO> allResultList) {
        if (CollectionUtils.isEmpty(allResultList)) {
            allResultList.addAll(paramEntryValue);
        }
        paramEntryValue.forEach(examineDTO -> {
            // 拼接后对比是否相等
            String reviewConnectStr = getReviewExamineConnectName(examineDTO);
            allResultList.forEach(oneVO -> {
                String allReviewConnectStr = getReviewExamineConnectName(oneVO);
                if (reviewConnectStr.equals(allReviewConnectStr)) {
                    // 比较起始终止日期
                    compareStartAndEndDate(paramBuilder, examineDTO, oneVO, reviewConnectStr);
                }
            });
        });
    }

    private void compareStartAndEndDate(StringBuilder strBuilder, DmRawDataExamineDTO examineDTO, DmRawDataExamineDTO oneVO, String reviewConnectStr) {
        if (oneVO.getBeginDate() > examineDTO.getBeginDate() && oneVO.getEndDate() < examineDTO.getEndDate()) {
            if (!strBuilder.toString().contains(reviewConnectStr)) {
                strBuilder.append(reviewConnectStr + " ");
                return;
            }
        }
        if (oneVO.getBeginDate() < examineDTO.getBeginDate() && oneVO.getEndDate() < examineDTO.getEndDate()
                && oneVO.getEndDate() > examineDTO.getBeginDate()) {
            if (!strBuilder.toString().contains(reviewConnectStr)) {
                strBuilder.append(reviewConnectStr + " ");
                return;
            }
        }
        if (oneVO.getBeginDate() > examineDTO.getBeginDate() && oneVO.getEndDate() > examineDTO.getEndDate()
                && oneVO.getBeginDate() < examineDTO.getEndDate()) {
            if (!strBuilder.toString().contains(reviewConnectStr)) {
                strBuilder.append(reviewConnectStr + " ");
                return;
            }
        }
        if (oneVO.getBeginDate().equals(examineDTO.getBeginDate()) || oneVO.getEndDate().equals(examineDTO.getEndDate())
                || oneVO.getBeginDate().equals(examineDTO.getEndDate()) || oneVO.getEndDate().equals(examineDTO.getBeginDate())) {
            if (!strBuilder.toString().contains(reviewConnectStr)) {
                strBuilder.append(reviewConnectStr + " ");
            }
        }
    }

    private void checkReviewSystem(List<DmRawDataExamineDTO> reviewList, List<DmRawDataExamineDTO> allResultList)
            throws CommonApplicationException {
        // 校验入参数据和系统数据是否重复
        StringBuilder doubleBuilder = new StringBuilder();
        checkRepeatData(reviewList, allResultList, doubleBuilder);
        if (ObjectUtils.isNotEmpty(doubleBuilder)) {
            throw new CommonApplicationException("检测到:" + doubleBuilder + "在该时期范围修改操作已存在,请勿重复提交!");
        }
        // 校验同一条件下，系统已存在的数据和新增或者修改的数据的，起始终止时间是否重叠
        StringBuilder crossoverBuilder = new StringBuilder();
        checkExamineData(crossoverBuilder, reviewList, allResultList);
        if (ObjectUtils.isNotEmpty(crossoverBuilder)) {
            throw new CommonApplicationException("检测到SPART:" + crossoverBuilder + "和已存在数据起始终止时间重叠,请修改后保存!");
        }
    }

    private void checkRepeatData(List<DmRawDataExamineDTO> reviewList, List<DmRawDataExamineDTO> tempList, StringBuilder doubleBuilder) {
        List<DmRawDataExamineDTO> combineList = new ArrayList<>();
        combineList.addAll(reviewList);
        combineList.addAll(tempList);
        // 新插入的数据 + 当前verisonId的历史数据，需要总体 校验是否重复
        Map<String, List<DmRawDataExamineDTO>> repeatList = combineList.stream()
                .collect(Collectors.groupingBy(this::getReviewConnectCode));
        List<String> count = repeatList.keySet().stream()
                .filter(key -> repeatList.get(key).size() > 1).distinct().collect(Collectors.toList());
        if (count.size() > 0) {
            for (String combine : count) {
                String combineCode = combine.split("#")[0];
                if (!doubleBuilder.toString().contains(combineCode)) {
                    doubleBuilder.append(combineCode + ",");
                }
            }
        }
    }

    private StringBuilder checkDataEmpty(List<DmRawDataExamineDTO> dataExamineList, StringBuilder emptyBuilder)
            throws UnsupportedEncodingException {
        // 校验入参是否空值
        for (DmRawDataExamineDTO dataExamineDTO : dataExamineList) {
            if (null == dataExamineDTO.getBeginDate()) {
                return emptyBuilder.append("起始期为空;");
            }
            if (null == dataExamineDTO.getEndDate()) {
                return emptyBuilder.append("终止期为空;");
            }
            if (StringUtils.isBlank(dataExamineDTO.getModifyReason())) {
                return emptyBuilder.append("修改理由为空;");
            } else {
                // 裁剪字符
                subModifyReasonLength(dataExamineDTO);
            }
        }
        return emptyBuilder;
    }

    private void subModifyReasonLength(DmRawDataExamineDTO dmRawDataExamineDTO) throws UnsupportedEncodingException {
        String modifyReason = dmRawDataExamineDTO.getModifyReason().trim();
        int length = modifyReason.getBytes("UTF-8").length;
        if (length >= 2000) {
            String str = modifyReason.substring(0, 666);
            dmRawDataExamineDTO.setModifyReason(str);
        } else {
            dmRawDataExamineDTO.setModifyReason(modifyReason);
        }
    }

    private List<DmRawDataExamineDTO> getDistinctRecordList(List<DmRawDataExamineDTO> reviewList,
                                                            List<DmRawDataExamineDTO> examineResultList, DataReviewVO dataReviewVO) {
        // 历史数据，type=新增的不需要去重，修改和撤销才去重
        Map<String, List<DmRawDataExamineDTO>> repeatModifyMap = reviewList.stream().filter(review -> CommonConstEnum.ModifyType.MODIFY.getValue().equals(review.getModifyType()))
                .collect(Collectors.groupingBy(
                        this::getReviewExamineConnectName));
        Map<String, List<DmRawDataExamineDTO>> repeatRemokeMap = reviewList.stream().filter(review -> CommonConstEnum.ModifyType.REVOKE.getValue().equals(review.getModifyType()))
                .collect(Collectors.groupingBy(
                        this::getReviewConnectCode));
        // 历史数据去除正在修改和撤销的记录
        List<String> modifyKeySet = new ArrayList<>(repeatModifyMap.keySet());
        List<String> remokeKeySet = new ArrayList<>(repeatRemokeMap.keySet());
        dataReviewVO.setRemokeMap(repeatRemokeMap);
        List<DmRawDataExamineDTO> examineResult = examineResultList.stream().filter(vo -> !modifyKeySet.contains(getReviewExamineConnectName(vo))).collect(Collectors.toList());
        return examineResult.stream().filter(vo -> !remokeKeySet.contains(getReviewConnectCode(vo))).collect(Collectors.toList());
    }

    @JalorOperation(code = "dataReviewExport", desc = "数据导出")
    @Audit(module = "bottomDataReviewService-dataReviewExport", operation = "dataReviewExport", message = "数据导出")
    @Override
    public ResultDataVO dataReviewExport(HttpServletResponse response, BottomDataReviewVO bottomDataReviewVO) throws ApplicationException {
        IExcelTemplateBeanManager templateBeanManager;
        String module;
        if ("abnormal".equals(bottomDataReviewVO.getPageFlag())) {
            templateBeanManager = DataReviewTemplateEnum.getByCode("01", "");
            module = CommonConstant.DATA_REVIEW_ABNORMAL;
        } else {
            templateBeanManager = DataReviewTemplateEnum.getByCode("02", "");
            module = CommonConstant.DATA_REVIEW_OPERATION;
        }
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("exportModuleName", module);
        parameters.put("exportFileName", bottomDataReviewVO.getFileName());
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(10000);
        exportProcessorService.fillEasyExcelExport(response, templateBeanManager, bottomDataReviewVO, parameters, pageVO);
        return ResultDataVO.success();
    }

    @JalorOperation(code = "refreshSystem", desc = "预发布")
    @Override
    public ResultDataVO refreshSystem(Long versionId) throws ApplicationException {
        String ictOperationPeriod = registryQueryService.findValueByPath(ICT_OPERATION_PERIOD, true);
        if (StringUtils.isNotBlank(ictOperationPeriod)) {
            String[] dateRange = ictOperationPeriod.split("-");
            int monthValue = LocalDate.now().getDayOfMonth();
            if (StringUtils.isNotBlank(dateRange[0]) && StringUtils.isNotBlank(dateRange[1])) {
                int beginDate = Integer.parseInt(dateRange[0]);
                int endDate = Integer.parseInt(dateRange[1]);
                if (monthValue < beginDate || monthValue > endDate) {
                    throw  new CommonApplicationException("日期不在规定操作时间范围内");
                }
            }
        }
        dmFcstVersionInfoDao.updateRunningStatusFlag();
        dmFcstVersionInfoDao.updateStatusFlag(versionId);
        return ResultDataVO.success(versionId);
    }

    @JalorOperation(code = "findRefreshTime", desc = "获取系统刷新时间")
    @Override
    public ResultDataVO findRefreshTime() {
        Map<String, Object> resultMap = new HashMap<>();
        List<DmFcstVersionInfoVO> dataReviewVersion = dmFcstVersionInfoDao.findMaxDataReviewVersion();
        if (CollectionUtils.isNotEmpty(dataReviewVersion)) {
            resultMap.put("version", dataReviewVersion.get(0));
            resultMap.put("refreshTime", dmFcstVersionInfoDao.findRefreshTime());
        } else {
            resultMap.put("version", null);
            resultMap.put("refreshTime", null);
        }
        return ResultDataVO.success(resultMap);
    }
}
