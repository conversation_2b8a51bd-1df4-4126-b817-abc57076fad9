/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.config;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.huawei.it.fcst.enums.CommonConstEnum;
import com.huawei.it.fcst.industry.index.constant.CommonConstant;
import com.huawei.it.fcst.industry.index.constant.Constant;
import com.huawei.it.fcst.industry.index.dao.IDmFocDataRefreshStatusDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocManufactureReviewDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocProcurementReviewDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocVersionDao;
import com.huawei.it.fcst.industry.index.enums.IndustryConst;
import com.huawei.it.fcst.industry.index.enums.IndustryIndexEnum;
import com.huawei.it.fcst.industry.index.enums.ModuleEnum;
import com.huawei.it.fcst.industry.index.enums.ResultCodeEnum;
import com.huawei.it.fcst.industry.index.impl.annual.StatisticsExcelService;
import com.huawei.it.fcst.industry.index.impl.combination.AsyncService;
import com.huawei.it.fcst.industry.index.impl.relation.ConfigDimensionManageService;
import com.huawei.it.fcst.industry.index.service.config.IConfigBottomReviewService;
import com.huawei.it.fcst.config.ConfigUtil;
import com.huawei.it.fcst.industry.index.utils.ExcelUtil;
import com.huawei.it.fcst.industry.index.utils.FcstIndexUtil;
import com.huawei.it.fcst.industry.index.utils.UserInfoUtils;
import com.huawei.it.fcst.industry.index.vo.combination.DmFocDataRefreshStatus;
import com.huawei.it.fcst.industry.index.vo.common.AbstractExcelTitleVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFoiImpExpRecordVO;
import com.huawei.it.fcst.industry.index.vo.common.ExportExcelVo;
import com.huawei.it.fcst.industry.index.vo.common.HeaderVo;
import com.huawei.it.fcst.industry.index.vo.common.LeafExcelTitleVO;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.common.TableNameVO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoDTO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoVO;
import com.huawei.it.fcst.industry.index.vo.config.HistoryInputVO;
import com.huawei.it.fcst.industry.index.vo.config.ManufactureBottomVO;
import com.huawei.it.fcst.industry.index.vo.config.ProcurementBottomVO;
import com.huawei.it.fcst.util.ChineseCharacterValidator;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.core.util.CollectionUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellType;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.inject.Inject;
import javax.inject.Named;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * ConfigBottomReviewService Class
 *
 * <AUTHOR>
 * @since 2023/12/7
 */
@Named("configBottomReviewService")
@JalorResource(code = "configBottomReviewService", desc = "配置管理底层数据审视服务")
public class ConfigBottomReviewService implements IConfigBottomReviewService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ConfigBottomReviewService.class);

    private static final String INIT = "TASK_INIT";

    private static final String IS_NOT = "N";

    private static final String IMPACTQTY = "IMPACTQTY";

    @Inject
    private IDmFocVersionDao dmFocVersionDao;

    @Inject
    private IDmFocProcurementReviewDao procurementReviewDao;

    @Inject
    private IDmFocManufactureReviewDao manufactureReviewDao;

    @Inject
    private IDmFocDataRefreshStatusDao dataRefreshStatusDao;

    @Inject
    private StatisticsExcelService statisticsExcelService;

    @Inject
    private ExcelUtil excelUtil;

    @Inject
    private AsyncService asyncService;

    @Inject
    private ConfigDimensionManageService configDimensionManageService;


    @Override
    @JalorOperation(code = "getProcurementDropDown", desc = "采购成本-底层数据审视下拉框接口")
    public ResultDataVO getProcurementDropDown(ProcurementBottomVO procurementBottomVO) {
        LOGGER.info(">>>Begin ConfigBottomReviewService::getProcurementDropDown");
        List<ProcurementBottomVO> dropDownList = procurementReviewDao.findBaseDropDown(procurementBottomVO);
        dropDownList = Optional.ofNullable(dropDownList).orElse(new ArrayList<>());
        return ResultDataVO.success(dropDownList);
    }

    @Override
    @JalorOperation(code = "getProFormDropDown", desc = "采购成本-异常数据录入表单下拉框接口")
    public ResultDataVO getProFormDropDown(ProcurementBottomVO procurementBottomVO) {
        LOGGER.info(">>>Begin ConfigBottomReviewService::getProFormDropDown");
        if (StringUtils.isEmpty(procurementBottomVO.getItemCode())) {
            List<ProcurementBottomVO> dropDownList = procurementReviewDao.getConfigItemDropDown(procurementBottomVO);
            dropDownList = Optional.ofNullable(dropDownList).orElse(new ArrayList<>());
            return ResultDataVO.success(dropDownList);
        }
        if (null != procurementBottomVO.getStartPeriod() && null != procurementBottomVO.getEndPeriod()) {
            // 获取ITEM影响数量
            procurementBottomVO.setKeyStr(ConfigUtil.getInstance().get16PlainText());
            procurementBottomVO.setPeriodId(procurementBottomVO.getStartPeriod());
            if (CollectionUtils.isNotEmpty(procurementReviewDao.checkStartPeriod(procurementBottomVO))) {
                return ResultDataVO.success(procurementReviewDao.getImpactQty(procurementBottomVO));
            } else {
                return getAsyncImpactQty(procurementBottomVO);
            }
        }
        return ResultDataVO.success();
    }

    @Override
    @JalorOperation(code = "getImpactQty", desc = "采购成本-查询影响ITEM数量接口")
    public ResultDataVO getImpactQty(Long id, String industryOrg) {
        String tablePreFix = TableNameVO.getTablePreFix(industryOrg);
        return ResultDataVO.success(procurementReviewDao.getItemImpactQty(id, tablePreFix));
    }

    @NotNull
    private ResultDataVO getAsyncImpactQty(ProcurementBottomVO procurementBottomVO) {
        // 使用异步通过预测数表查询
        DmFocDataRefreshStatus dataRefreshStatus = new DmFocDataRefreshStatus();
        Long userId = UserInfoUtils.getUserId();
        dataRefreshStatus.setStatus(INIT);
        dataRefreshStatus.setDelFlag(IS_NOT);
        dataRefreshStatus.setTaskFlag(IMPACTQTY);
        dataRefreshStatus.setCreatedBy(userId);
        dataRefreshStatus.setLastUpdatedBy(userId);
        dataRefreshStatus.setCreationDate(new Date());
        dataRefreshStatus.setTaskId(dataRefreshStatusDao.getDataRefrashKey());
        dataRefreshStatus.setUserId(userId);
        dataRefreshStatus.setRoleId(UserInfoUtils.getCurrentUser().getCurrentRole().getRoleId());
        // 创建异步任务记录
        dataRefreshStatusDao.createDmFocDataRefreshStatus(dataRefreshStatus);
        procurementBottomVO.setTaskId(dataRefreshStatus.getTaskId());
        // 异步任务，执行逻辑
        asyncService.asyncImpactQty(procurementBottomVO, dataRefreshStatus);

        return ResultDataVO.success(dataRefreshStatus);
    }

    @Override
    @JalorOperation(code = "saveProModify", desc = "采购成本-底层数据审视保存接口")
    @Audit(module = "configBottomReviewService-saveProModify", operation = "saveProModify",
            message = "采购成本-底层数据审视保存接口")
    public ResultDataVO saveProModify(List<ProcurementBottomVO> procurementVOList) throws Exception {
        if (!CollectionUtil.isNullOrEmpty(procurementVOList) && procurementVOList.size() > com.huawei.it.fcst.constant.Constant.IntegerEnum.MAX_SIZE.getValue()) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR, "操作的数据过多！");
        }
        String dataType = "";
        String caliberFlag = procurementVOList.get(0).getCaliberFlag();
        String industryOrg = procurementVOList.get(0).getIndustryOrg();
        // 获取版本类型
        if (IndustryIndexEnum.CALIBER_FLAG.C.getValue().equals(caliberFlag)) {
            dataType = IndustryConst.DataType.REVIEW_PURC_C.getValue();
        } else {
            dataType = IndustryConst.DataType.REVIEW_PURC_R.getValue();
        }
        // 校验入参信息
        for (ProcurementBottomVO procurementBottomVO : procurementVOList) {
            if (!ChineseCharacterValidator.validateInput(procurementBottomVO.getModifyReason())){
                throw new CommonApplicationException("修改理由最少输入30个中文");
            }
            if (null == procurementBottomVO.getVersionId()) {
                DmFocVersionInfoVO build = DmFocVersionInfoVO.builder().dataType(dataType).tablePreFix(TableNameVO.getTablePreFix(industryOrg)).build();
                if (CollectionUtils.isNotEmpty(dmFocVersionDao.findMftVersionList(build))) {
                    throw new CommonApplicationException("入参维度版本为空");
                }
            }
            if (CommonConstEnum.ModifyType.REVOKE.getValue().equals(procurementBottomVO.getModifyType())) {
                throw new CommonApplicationException("该ITEM已撤销,无法进行修改!");
            }
        }
        Long versionId = procurementVOList.get(0).getVersionId();
        // 查询当前版本对应维度数据
        ProcurementBottomVO bottomVO = ProcurementBottomVO.builder()
                .versionId(versionId)
                .caliberFlag(caliberFlag)
                .build();
        bottomVO.setIndustryOrg(industryOrg);
        List<ProcurementBottomVO> bottomReviewList = procurementReviewDao.findBottomReviewVOList(bottomVO);
        // 剔除编辑操作后的数据
        List<ProcurementBottomVO> tempList = getTempList(procurementVOList, bottomReviewList);
        // 校验入参数据准确性
        checkProcurementParam(procurementVOList);
        if (CollectionUtils.isNotEmpty(tempList)) {
            // 校验与系统数据的准确性
            checkProcurementSystem(procurementVOList, tempList);
        }
        DmFocVersionInfoDTO newVersion = createVersionVO(dataType, versionId, industryOrg);
        // 保存数据
        saveProcurementReviewData(procurementVOList, tempList, newVersion.getVersionId(), industryOrg);
        return ResultDataVO.success(newVersion);
    }

    private void saveProcurementReviewData(List<ProcurementBottomVO> procurementVOList, List<ProcurementBottomVO> tempList, Long versionId, String industryOrg) {
        for (ProcurementBottomVO bottomVO : procurementVOList) {
            bottomVO.setCreatedBy(UserInfoUtils.getUserCn());
            bottomVO.setCreationDate(new Date());
            bottomVO.setDelFlag(CommonConstant.IS_NOT);
            bottomVO.setVersionId(versionId);
            bottomVO.setLastUpdateDate(new Date());
            bottomVO.setLastUpdatedBy(UserInfoUtils.getUserCn());
        }
        tempList.addAll(procurementVOList);
        tempList.stream().forEach(procurementBottomVO -> {
            procurementBottomVO.setVersionId(versionId);
            procurementBottomVO.setPageFlag(CommonConstant.ABNORMAL_PAGE);
        });
        String tablePreFix = TableNameVO.getTablePreFix(industryOrg);
        procurementReviewDao.createProcurementList(tempList, tablePreFix);
        procurementVOList.stream().forEach(proVo -> proVo.setPageFlag(CommonConstant.HISTORY_PAGE));
        procurementReviewDao.createProcurementList(procurementVOList, tablePreFix);
    }

    @Override
    @JalorOperation(code = "revokeProModify", desc = "采购成本-底层数据审视撤销接口")
    @Audit(module = "configBottomReviewService-revokeProModify", operation = "revokeProModify",
            message = "采购成本-底层数据审视撤销接口")
    public ResultDataVO revokeProModify(List<ProcurementBottomVO> procurementVOList) throws CommonApplicationException {
        LOGGER.info("Begin ConfigDimensionService::revokeProModify");
        if (!CollectionUtil.isNullOrEmpty(procurementVOList) && procurementVOList.size() > com.huawei.it.fcst.constant.Constant.IntegerEnum.MAX_SIZE.getValue()) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR, "操作的数据过多！");
        }
        for (ProcurementBottomVO procurementBottomVO : procurementVOList) {
            if (null == procurementBottomVO.getVersionId()) {
                throw new CommonApplicationException("维度版本为空！");
            }
            if (!CommonConstEnum.ModifyType.REVOKE.getValue().equals(procurementBottomVO.getModifyType())) {
                throw new CommonApplicationException("撤销理由不符,无法进行撤销!");
            }
        }
        String dataType = procurementVOList.get(0).getDataType();
        Long versionId = procurementVOList.get(0).getVersionId();
        String industryOrg = procurementVOList.get(0).getIndustryOrg();
        ProcurementBottomVO bottomVO = ProcurementBottomVO.builder()
                .versionId(versionId)
                .caliberFlag(procurementVOList.get(0).getCaliberFlag())
                .build();
        bottomVO.setIndustryOrg(industryOrg);
        List<ProcurementBottomVO> baseReviewVOList = procurementReviewDao.findBottomReviewVOList(bottomVO);
        // 创建新版本
        DmFocVersionInfoDTO newVersion = createVersionVO(dataType, versionId, industryOrg);
        // 组转数据
        for (ProcurementBottomVO procurementBottomVO : baseReviewVOList) {
            procurementBottomVO.setVersionId(newVersion.getVersionId());
            for (ProcurementBottomVO paramVO : procurementVOList) {
                if (procurementBottomVO.getItemCode().equals(paramVO.getItemCode())
                        && procurementBottomVO.getStartPeriod().equals(paramVO.getStartPeriod())
                        && procurementBottomVO.getEndPeriod().equals(paramVO.getEndPeriod())) {
                    procurementBottomVO.setModifyReason(paramVO.getModifyReason());
                    procurementBottomVO.setModifyType(CommonConstEnum.ModifyType.REVOKE.getValue());
                    procurementBottomVO.setLastUpdateDate(new Date());
                    procurementBottomVO.setLastUpdatedBy(UserInfoUtils.getUserCn());
                    procurementBottomVO.setPageFlag(CommonConstant.HISTORY_PAGE);
                } else {
                    procurementBottomVO.setPageFlag(CommonConstant.ABNORMAL_PAGE);
                }
            }
        }
        // 插入新数据
        procurementReviewDao.createProcurementList(baseReviewVOList, TableNameVO.getTablePreFix(industryOrg));
        return ResultDataVO.success(newVersion);
    }

    private DmFocVersionInfoDTO createVersionVO(String dataType, Long versionId, String industryOrg) {
        String tablePreFix = TableNameVO.getTablePreFix(industryOrg);
        DmFocVersionInfoDTO versionInfoDTO = new DmFocVersionInfoDTO();
        versionInfoDTO.setParentVersionId(versionId);
        versionInfoDTO.setVersionId(dmFocVersionDao.getVersionKey(tablePreFix));
        versionInfoDTO.setVersion(generateVersionName(dataType, tablePreFix));
        versionInfoDTO.setVersionType(IndustryConst.VersionType.ADJUST.getValue());
        versionInfoDTO.setStatus(IndustryConst.STATUS.IS_STATUS.getValue());
        versionInfoDTO.setDataType(dataType);
        versionInfoDTO.setCreatedBy(UserInfoUtils.getUserId());
        versionInfoDTO.setCreationDate(new Date());
        versionInfoDTO.setLastUpdatedBy(UserInfoUtils.getUserId());
        versionInfoDTO.setLastUpdateDate(new Date());
        versionInfoDTO.setDelFlag(CommonConstant.IS_NOT);
        versionInfoDTO.setIsRunning(CommonConstant.IS_NOT);
        versionInfoDTO.setTablePreFix(tablePreFix);
        dmFocVersionDao.createDmFocVersionDTO(versionInfoDTO);
        return versionInfoDTO;
    }

    private String generateVersionName(String dataType, String tablePreFix) {
        DmFocVersionInfoDTO versionDto = new DmFocVersionInfoDTO();
        versionDto.setLastUpdateStr(DateUtil.today());
        versionDto.setDataType(dataType);
        versionDto.setTablePreFix(tablePreFix);
        List<DmFocVersionInfoDTO> versionMap = dmFocVersionDao.findDmFocVersionDTOList(versionDto);
        return configDimensionManageService.getVersionName(versionMap);
    }

    private void checkProcurementParam(List<ProcurementBottomVO> procurementVOList)
            throws CommonApplicationException, UnsupportedEncodingException {
        // 校验入参是否空值
        StringBuilder emptyBuilder = new StringBuilder();
        StringBuilder finallyBuilder = checkEmptyItem(procurementVOList, emptyBuilder);
        if (ObjectUtils.isNotEmpty(finallyBuilder)) {
            throw new CommonApplicationException("检测到:" + finallyBuilder);
        }
        // 校验入参同一规格品起始终止时间是否重叠
        StringBuilder paramBuilder = new StringBuilder();
        checkParamItem(procurementVOList, paramBuilder);
        if (ObjectUtils.isNotEmpty(paramBuilder)) {
            throw new CommonApplicationException("检测到ITEM:" + paramBuilder + "的起始终止时间重叠,请修改后保存!");
        }
    }

    private void checkProcurementSystem(List<ProcurementBottomVO> procurementVOList, List<ProcurementBottomVO> tempList)
            throws CommonApplicationException {
        // 校验入参数据和系统数据是否重复
        StringBuilder doubleBuilder = new StringBuilder();
        checkRepeatData(procurementVOList, tempList, doubleBuilder);
        if (ObjectUtils.isNotEmpty(doubleBuilder)) {
            throw new CommonApplicationException("检测到ITEM:" + doubleBuilder + "在该时期范围修改操作已存在,请勿重复提交!");
        }
        // 校验入参规格品和系统相同规格品的起始终止时间是否重叠
        StringBuilder crossoverBuilder = new StringBuilder();
        checkCrossData(crossoverBuilder, procurementVOList, tempList);
        if (ObjectUtils.isNotEmpty(crossoverBuilder)) {
            throw new CommonApplicationException("检测到ITEM:" + crossoverBuilder + "和已存在数据起始终止时间重叠,请修改后保存!");
        }
    }

    private StringBuilder checkEmptyItem(List<ProcurementBottomVO> procurementVOList, StringBuilder emptyBuilder)
            throws UnsupportedEncodingException {
        for (ProcurementBottomVO bottomVO : procurementVOList) {
            if (StrUtil.isBlank(bottomVO.getL3CegCode())) {
                return emptyBuilder.append("专项采购认证部编码为空;");
            }
            if (StrUtil.isBlank(bottomVO.getL3CegShortCnName())) {
                return emptyBuilder.append("专项采购认证部为空;");
            }
            if (StrUtil.isBlank(bottomVO.getL4CegCode())) {
                return emptyBuilder.append("模块编码为空;");
            }
            if (StrUtil.isBlank(bottomVO.getL4CegShortCnName())) {
                return emptyBuilder.append("模块为空;");
            }
            if (StrUtil.isBlank(bottomVO.getCategoryCode())) {
                return emptyBuilder.append("品类编码为空;");
            }
            if (StrUtil.isBlank(bottomVO.getCategoryCnName())) {
                return emptyBuilder.append("品类为空;");
            }
            if (StrUtil.isBlank(bottomVO.getItemCode())) {
                return emptyBuilder.append("ITEM编码为空;");
            }
            if (StrUtil.isBlank(bottomVO.getItemCnName())) {
                return emptyBuilder.append("ITEM为空;");
            }
            if (null == bottomVO.getStartPeriod()) {
                return emptyBuilder.append("起始期为空;");
            }
            if (null == bottomVO.getEndPeriod()) {
                return emptyBuilder.append("终止期为空;");
            }
            if (StrUtil.isBlank(bottomVO.getModifyReason())) {
                return emptyBuilder.append("修改理由为空;");
            } else {
                getModifyReason(bottomVO);
            }
            if (StrUtil.isBlank(bottomVO.getImpactQty())) {
                return emptyBuilder.append("影响ITEM数量为空;");
            }
        }
        return emptyBuilder;
    }

    private void getModifyReason(ProcurementBottomVO bottomVO) throws UnsupportedEncodingException {
        String modifyReason = bottomVO.getModifyReason().trim();
        int length = modifyReason.getBytes("UTF-8").length;
        if (length >= 2000) {
            String substring = modifyReason.substring(0, 666);
            bottomVO.setModifyReason(substring);
        } else {
            bottomVO.setModifyReason(modifyReason);
        }
    }

    @NotNull
    private List<ProcurementBottomVO> getTempList(List<ProcurementBottomVO> procurementVOList,
                                                  List<ProcurementBottomVO> bottomReviewList) {
        List<ProcurementBottomVO> tempList = new ArrayList<>();
        tempList.addAll(bottomReviewList);
        for (int i = 0; i < procurementVOList.size(); i++) {
            if (CommonConstEnum.ModifyType.MODIFY.getValue().equals(procurementVOList.get(i).getModifyType())) {
                for (int j = 0; j < tempList.size(); j++) {
                    if (procurementVOList.get(i).getOldItemCode().equals(tempList.get(j).getItemCode())) {
                        if (procurementVOList.get(i).getOldStartPeriod().equals(tempList.get(j).getStartPeriod())
                                && procurementVOList.get(i).getOldEndPeriod().equals(tempList.get(j).getEndPeriod())) {
                            tempList.remove(tempList.get(j));
                        }
                    }
                }
            }
        }
        return tempList;
    }

    private void checkRepeatData(List<ProcurementBottomVO> procurementVOList, List<ProcurementBottomVO> tempList, StringBuilder doubleBuilder) {
        List<ProcurementBottomVO> combineList = new ArrayList<>();
        combineList.addAll(procurementVOList);
        combineList.addAll(tempList);
        // 数据重复校验
        Map<String, List<ProcurementBottomVO>> repeatList = combineList.stream()
                .collect(Collectors.groupingBy(
                        x -> x.getItemCode() + "#" + x.getStartPeriod() + "#" + x.getEndPeriod()));
        List<String> count = repeatList.keySet().stream()
                .filter(key -> repeatList.get(key).size() > 1).distinct().collect(Collectors.toList());
        if (count.size() > 0) {
            for (String combine : count) {
                String item = combine.split("#")[0];
                if (!doubleBuilder.toString().contains(item)) {
                    doubleBuilder.append(item + " ");
                }
            }
        }
    }

    private void checkParamItem(List<ProcurementBottomVO> procurementVOList, StringBuilder paramBuilder) {
        Map<String, List<ProcurementBottomVO>> procurementList = procurementVOList.stream().map(param -> {
            ProcurementBottomVO paramVo = new ProcurementBottomVO();
            paramVo.setItemCode(param.getItemCode());
            paramVo.setItemCnName(param.getItemCnName());
            paramVo.setStartPeriod(param.getStartPeriod());
            paramVo.setEndPeriod(param.getEndPeriod());
            return paramVo;
        }).distinct().collect(Collectors.groupingBy(ProcurementBottomVO::getItemCode));
        List<String> itemCodeList = procurementList.keySet().stream().filter(key -> procurementList.get(key).size() > 1).distinct().collect(Collectors.toList());

        if (itemCodeList.size() > 0) {
            for (Map.Entry<String, List<ProcurementBottomVO>> paramEntry : procurementList.entrySet()) {
                String entryKey = paramEntry.getKey();
                itemCodeList.stream().forEach(all -> {
                    if (entryKey.equals(all)) {
                        List<ProcurementBottomVO> paramEntryValue = paramEntry.getValue();
                        List<ProcurementBottomVO> tempList = new ArrayList<>();
                        checkCrossData(paramBuilder, paramEntryValue, tempList);
                    }
                });
            }
        }
    }

    private void checkCrossData(StringBuilder paramBuilder, List<ProcurementBottomVO> paramEntryValue, List<ProcurementBottomVO> tempList) {
        if (CollectionUtils.isEmpty(tempList)) {
            tempList.addAll(paramEntryValue);
        }
        for (ProcurementBottomVO procurementVO : paramEntryValue) {
            for (ProcurementBottomVO oneVO : tempList) {
                if (procurementVO.getItemCode().equals(oneVO.getItemCode())) {
                    // 比较起始终止日期
                    compareStartAndEndPeriod(paramBuilder, procurementVO, oneVO);
                }
            }
        }
    }

    private void compareStartAndEndPeriod(StringBuilder paramBuilder, ProcurementBottomVO procurementVO, ProcurementBottomVO oneVO) {
        if (oneVO.getStartPeriod() > procurementVO.getStartPeriod() && oneVO.getEndPeriod() < procurementVO.getEndPeriod()) {
            if (!paramBuilder.toString().contains(procurementVO.getItemCode())) {
                paramBuilder.append(procurementVO.getItemCode() + " ");
                return;
            }
        }
        if (oneVO.getStartPeriod() < procurementVO.getStartPeriod() && oneVO.getEndPeriod() < procurementVO.getEndPeriod()
                && oneVO.getEndPeriod() > procurementVO.getStartPeriod()) {
            if (!paramBuilder.toString().contains(procurementVO.getItemCode())) {
                paramBuilder.append(procurementVO.getItemCode() + " ");
                return;
            }
        }
        if (oneVO.getStartPeriod() > procurementVO.getStartPeriod() && oneVO.getEndPeriod() > procurementVO.getEndPeriod()
                && oneVO.getStartPeriod() < procurementVO.getEndPeriod()) {
            if (!paramBuilder.toString().contains(procurementVO.getItemCode())) {
                paramBuilder.append(procurementVO.getItemCode() + " ");
                return;
            }
        }
        if (oneVO.getStartPeriod().equals(procurementVO.getStartPeriod()) || oneVO.getEndPeriod().equals(procurementVO.getEndPeriod())
                || oneVO.getStartPeriod().equals(procurementVO.getEndPeriod()) || oneVO.getEndPeriod().equals(procurementVO.getStartPeriod())) {
            if (!paramBuilder.toString().contains(procurementVO.getItemCode())) {
                paramBuilder.append(procurementVO.getItemCode() + " ");
            }
        }
    }

    @Override
    @JalorOperation(code = "exportProcurement", desc = "采购成本-底层数据审视导出接口")
    @Audit(module = "configBottomReviewService-exportProcurement", operation = "exportProcurement",
            message = "采购成本-底层数据审视导出接口")
    public ResultDataVO exportProcurement(ProcurementBottomVO procurementBottomVO, HttpServletResponse response) {
        LOGGER.info(">>>Begin ConfigBottomReviewService::exportProModify");
        try {
            exportPro(procurementBottomVO, response);
        } catch (IOException | ApplicationException e) {
            LOGGER.error("exportExcel error: {}", e.getMessage());
        }
        return ResultDataVO.success(MapUtil.of(new Object[][]{{"expFlag", true}}));
    }

    private void exportPro(ProcurementBottomVO procurementBottomVO, HttpServletResponse response) throws ApplicationException, IOException {
        Timestamp creationDate = new Timestamp(System.currentTimeMillis());
        List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
        List<HeaderVo> headers = new LinkedList<>();
        String modelType = procurementBottomVO.getModelType();
        String sheetName = "";
        // 个人中心页面模块
        String pageModule = setPageModule(procurementBottomVO.getIndustryOrg(), procurementBottomVO.getCostType());
        ProcurementBottomVO bottomVO = ProcurementBottomVO.builder()
                .dataType(procurementBottomVO.getDataType())
                .caliberFlag(procurementBottomVO.getCaliberFlag())
                .pageFlag(procurementBottomVO.getPageFlag())
                .build();
        if (CommonConstant.ABNORMAL_PAGE.equals(procurementBottomVO.getPageFlag())) {
            bottomVO.setVersionId(procurementBottomVO.getVersionId());
        }
        // 适配表名称前缀
        bottomVO.setIndustryOrg(procurementBottomVO.getIndustryOrg());
        List<Map> bottomReviewList = procurementReviewDao.findBottomReviewList(bottomVO);
        DmFocVersionInfoDTO baseReviewVersion = dmFocVersionDao.findBaseReviewVersion(bottomVO);
        setTablePreFix(modelType, bottomReviewList);
        if (IndustryConst.DataReview.MODIFY.getValue().equals(modelType)) {
            sheetName = CommonConstant.ENTRY_ABNORMAL_DATA;
            getCommonHead(headers);
            getModifyHead(headers);
        } else {
            sheetName = CommonConstant.HISTORICAL_MODIFICATION_RECORDS;
            headers.add(new HeaderVo("操作类型", "modify_type", CellType.STRING, true, 10 * 480));
            headers.add(new HeaderVo("版本号", "version", CellType.STRING, true, 12 * 480));
            getCommonHead(headers);
            getRecordHead(headers);
        }
        // 文件名
        String filename = getFilename(procurementBottomVO);
        // 获取版本信息
        String version = null;
        if (null != baseReviewVersion) {
            version = baseReviewVersion.getVersion();
        }
        // 获取表头模型
        List<AbstractExcelTitleVO> excelTitleVOS = new ArrayList<>();
        HistoryInputVO build = HistoryInputVO.builder()
                .caliberFlag(procurementBottomVO.getCaliberFlag())
                .fileName(filename)
                .costType(procurementBottomVO.getCostType())
                .version(version)
                .modelType(modelType)
                .topCateHeader(headers)
                .build();
        List<ExportExcelVo> exportExcelVoList = getExportExcelVOs(build, titleVoList, sheetName, bottomReviewList, excelTitleVOS);
        DmFoiImpExpRecordVO expRecordVO = excelUtil.expSelectColumnExcel(exportExcelVoList, response);
        expRecordVO.setModuleType(pageModule);
        // 插入导出数据
        expRecordVO.setCreationDate(creationDate);
        expRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        statisticsExcelService.insertExportExcelRecord(expRecordVO);
    }

    private void setTablePreFix(String modelType, List<Map> bottomReviewList) {
        for (Map baseMap : bottomReviewList) {
            if (ObjectUtils.isNotEmpty(baseMap.get("modify_reason_i"))) {
                baseMap.put("modify_reason_m", baseMap.get("modify_reason_i"));
            }
            if (IndustryConst.DataReview.RECORD.getValue().equals(modelType)) {
                String modifyType = String.valueOf(baseMap.get("modify_type"));
                switch (modifyType) {
                    case "INSERT":
                        baseMap.put("modify_type", "新增");
                        break;
                    case "MODIFY":
                        baseMap.put("modify_type", "修改");
                        break;
                    case "REVOKE":
                        baseMap.put("modify_type", "撤销");
                        break;
                    default:
                        break;
                }
            }
        }
    }

    @NotNull
    private String setPageModule(String industryOrg, String costType) {
        String pageModule = null;
        IndustryConst.INDUSTRY_ORG industryOrgName = IndustryConst.getIndustryOrgName(industryOrg);
        String costTypeName = IndustryIndexEnum.COST_TYPE.P.getValue().equals(costType) ? ModuleEnum.REVIEW_PURC.getCnName() : ModuleEnum.REVIEW_MADE.getCnName();
        switch (industryOrgName) {
            case ICT:
                pageModule = IndustryConst.INDUSTRY_ORG_PAGE.ICT_MODEL.getValue() + costTypeName;
                break;
            case ENERGY:
                pageModule = IndustryConst.INDUSTRY_ORG_PAGE.ENERGY_MODEL.getValue() + costTypeName;
                break;
            case IAS:
                pageModule = IndustryConst.INDUSTRY_ORG_PAGE.IAS_MODEL.getValue() + costTypeName;
                break;
        }

        return pageModule;
    }

    @NotNull
    private String getFilename(ProcurementBottomVO procurementBottomVO) {
        return CommonConstant.ENTRY_ABNORMAL_DATA.concat(
                new SimpleDateFormat("_yyyy-MM-dd HH_mm_ss").format(new Date()));
    }

    @NotNull
    private List<ExportExcelVo> getExportExcelVOs(HistoryInputVO historyInputVO, List<AbstractExcelTitleVO> titleVoList, String sheetName,
                                                  List<Map> bottomReviewList, List<AbstractExcelTitleVO> excelTitleVOS) {
        LeafExcelTitleVO costTypeVO = new LeafExcelTitleVO(Constant.StrEnum.COST_TYPE.getValue() + IndustryIndexEnum.getCostTypeFlag(
                historyInputVO.getCostType()).getDesc(),
                CommonConstant.VIEW_WIDTH, true, "costType", "costType", CellType.STRING, false);
        excelTitleVOS.add(costTypeVO);
        LeafExcelTitleVO caliberFlagVO = new LeafExcelTitleVO(Constant.StrEnum.CALIBER_FLAG.getValue() + IndustryIndexEnum.getCaliberFlag(
                historyInputVO.getCaliberFlag()).getDesc(),
                CommonConstant.VIEW_WIDTH, true, "caliberFlag", "caliberFlag", CellType.STRING, false);
        excelTitleVOS.add(caliberFlagVO);
        if (IndustryConst.DataReview.MODIFY.getValue().equals(historyInputVO.getModelType())) {
            LeafExcelTitleVO columnCnVersionName = new LeafExcelTitleVO(CommonConstant.VERSION_REFRESH + historyInputVO.getVersion(),
                    CommonConstant.VERSION_WIDTH, true, "versionName", "versionName", CellType.STRING, false);
            excelTitleVOS.add(columnCnVersionName);
        }
        // 设置第二行表头模型
        Set<String> titles = new LinkedHashSet<>();
        FcstIndexUtil.setHeader(titleVoList, historyInputVO.getTopCateHeader(), titles);
        ExportExcelVo exportExcelVo = ExportExcelVo.builder()
                .formInfoVo(excelTitleVOS)
                .titleVoList(titleVoList)
                .list(bottomReviewList)
                .sheetName(sheetName)
                .fileName(historyInputVO.getFileName())
                .mergeCell(false)
                .build();
        List<ExportExcelVo> exportExcelVoList = new ArrayList<>();
        exportExcelVoList.add(exportExcelVo);
        return exportExcelVoList;
    }

    private void getCommonHead(List<HeaderVo> commonHeader) {
        commonHeader.add(new HeaderVo("专项采购认证部", "l3_ceg_short_cn_name", CellType.STRING, true, 12 * 480));
        commonHeader.add(new HeaderVo("模块", "l4_ceg_short_cn_name", CellType.STRING, true, 12 * 480));
        commonHeader.add(new HeaderVo("品类", "category_cn_name", CellType.STRING, true, 12 * 480));
        commonHeader.add(new HeaderVo("ITEM", "item_code", CellType.STRING, true, 12 * 480));
        commonHeader.add(new HeaderVo("起始期", "start_period", CellType.STRING, true, 10 * 480));
        commonHeader.add(new HeaderVo("终止期", "end_period", CellType.STRING, true, 10 * 480));
    }

    @Override
    @JalorOperation(code = "getManufactureDropDown", desc = "制造成本-底层数据审视下拉框接口")
    public ResultDataVO getManufactureDropDown(ManufactureBottomVO manufactureBottomVO) {
        LOGGER.info(">>>Begin ConfigBottomReviewService::exportProModify");
        List<ManufactureBottomVO> madeDropDownList = manufactureReviewDao.findBaseDropDown(manufactureBottomVO);
        madeDropDownList = Optional.ofNullable(madeDropDownList).orElse(new ArrayList<>());
        return ResultDataVO.success(madeDropDownList);
    }

    @Override
    @JalorOperation(code = "getMftFormDropDown", desc = "制造成本-底层数据审视表单下拉框接口")
    public ResultDataVO getMftFormDropDown(ManufactureBottomVO manufactureBottomVO) {
        LOGGER.info(">>>Begin ConfigBottomReviewService::exportProModify");
        if (StringUtils.isEmpty(manufactureBottomVO.getItemCode())) {
            List<ManufactureBottomVO> madeDropDownList = manufactureReviewDao.getMadeItemDropDown(manufactureBottomVO);
            madeDropDownList = Optional.ofNullable(madeDropDownList).orElse(new ArrayList<>());
            return ResultDataVO.success(madeDropDownList);
        }
        if (null != manufactureBottomVO.getStartPeriod() && null != manufactureBottomVO.getEndPeriod()) {
            // 影响ITEM数量
            ManufactureBottomVO madeImpactQty = manufactureReviewDao.getMadeImpactQty(manufactureBottomVO);
            return ResultDataVO.success(madeImpactQty);
        }
        return ResultDataVO.success();
    }

    @Override
    @JalorOperation(code = "saveManufactureModify", desc = "制造成本-底层数据审视保存接口")
    @Audit(module = "configBottomReviewService-saveManufactureModify", operation = "saveManufactureModify",
            message = "制造成本-底层数据审视保存接口")
    public ResultDataVO saveManufactureModify(List<ManufactureBottomVO> manufactureVOList) throws Exception {
        if (!CollectionUtil.isNullOrEmpty(manufactureVOList) && manufactureVOList.size() > com.huawei.it.fcst.constant.Constant.IntegerEnum.MAX_SIZE.getValue()) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR, "操作的数据过多！");
        }
        String dataType = "";
        String caliberFlag = manufactureVOList.get(0).getCaliberFlag();
        String industryOrg = manufactureVOList.get(0).getIndustryOrg();
        // 获取版本类型
        if (IndustryIndexEnum.CALIBER_FLAG.C.getValue().equals(caliberFlag)) {
            dataType = IndustryConst.DataType.REVIEW_MADE_C.getValue();
        } else {
            dataType = IndustryConst.DataType.REVIEW_MADE_R.getValue();
        }
        // 校验入参
        for (ManufactureBottomVO manufactureBottomVO : manufactureVOList) {
            if (!ChineseCharacterValidator.validateInput(manufactureBottomVO.getModifyReason())){
                throw new CommonApplicationException("修改理由最少输入30个中文");
            }
            if (null == manufactureBottomVO.getVersionId()) {
                DmFocVersionInfoVO build = DmFocVersionInfoVO.builder().dataType(dataType).tablePreFix(TableNameVO.getTablePreFix(industryOrg)).build();
                if (CollectionUtils.isNotEmpty(dmFocVersionDao.findMftVersionList(build))) {
                    throw new CommonApplicationException("入参维度版本为空");
                }
            }
            if (CommonConstEnum.ModifyType.REVOKE.getValue().equals(manufactureBottomVO.getModifyType())) {
                throw new CommonApplicationException("该ITEM已撤销,无法进行修改!");
            }
        }
        Long versionId = manufactureVOList.get(0).getVersionId();
        // 查询当前版本对应维度数据
        ManufactureBottomVO baseVO = ManufactureBottomVO.builder()
                .versionId(versionId)
                .caliberFlag(caliberFlag)
                .build();
        baseVO.setIndustryOrg(industryOrg);
        List<ManufactureBottomVO> madeReviewVOList = manufactureReviewDao.findMadeReviewVOList(baseVO);
        // 剔除编辑操作后的数据
        List<ManufactureBottomVO> tempList = getMadeTempList(manufactureVOList, madeReviewVOList);
        // 校验入参数据准确性
        checkManufactureParam(manufactureVOList);
        if (CollectionUtils.isNotEmpty(tempList)) {
            // 校验与系统数据准确性
            checkManufactureSystem(manufactureVOList, tempList);
        }
        DmFocVersionInfoDTO newVersion = createVersionVO(dataType, versionId, industryOrg);
        // 保存数据
        saveMadeReviewData(manufactureVOList, tempList, newVersion.getVersionId(), industryOrg);
        return ResultDataVO.success(newVersion);
    }

    private void saveMadeReviewData(List<ManufactureBottomVO> manufactureVOList, List<ManufactureBottomVO> tempList, Long versionId, String industryOrg) {
        for (ManufactureBottomVO baseVO : manufactureVOList) {
            baseVO.setCreatedBy(UserInfoUtils.getUserCn());
            baseVO.setCreationDate(new Date());
            baseVO.setDelFlag(CommonConstant.IS_NOT);
            baseVO.setVersionId(versionId);
            baseVO.setLastUpdateDate(new Date());
            baseVO.setLastUpdatedBy(UserInfoUtils.getUserCn());
        }
        tempList.addAll(manufactureVOList);
        tempList.stream().forEach(procurementBottomVO -> {
            procurementBottomVO.setVersionId(versionId);
            procurementBottomVO.setPageFlag(CommonConstant.ABNORMAL_PAGE);
        });
        String tablePreFix = TableNameVO.getTablePreFix(industryOrg);
        manufactureReviewDao.createMadeReviewList(tempList, tablePreFix);
        manufactureVOList.stream().forEach(manu -> manu.setPageFlag(CommonConstant.HISTORY_PAGE));
        manufactureReviewDao.createMadeReviewList(manufactureVOList, tablePreFix);
    }

    private void checkManufactureParam(List<ManufactureBottomVO> manufactureVOList)
            throws CommonApplicationException, UnsupportedEncodingException {
        // 校验入参是否空值
        StringBuilder emptyBuilder = new StringBuilder();
        checkMadeEmptyItem(manufactureVOList, emptyBuilder);
        if (ObjectUtils.isNotEmpty(emptyBuilder)) {
            throw new CommonApplicationException("检测到:" + emptyBuilder);
        }
        // 校验入参同一规格品起始终止时间是否重叠
        StringBuilder paramBuilder = new StringBuilder();
        checkMadeParamItem(manufactureVOList, paramBuilder);
        if (ObjectUtils.isNotEmpty(paramBuilder)) {
            throw new CommonApplicationException("检测到ITEM:" + paramBuilder + "的起始终止时间重叠,请修改后保存!");
        }
    }

    private void checkManufactureSystem(List<ManufactureBottomVO> manufactureVOList, List<ManufactureBottomVO> tempList)
            throws CommonApplicationException {
        // 校验入参数据和系统数据是否重复
        StringBuilder doubleBuilder = new StringBuilder();
        checkMadeRepeatData(manufactureVOList, tempList, doubleBuilder);
        if (ObjectUtils.isNotEmpty(doubleBuilder)) {
            throw new CommonApplicationException("检测到ITEM:" + doubleBuilder + "在该时期范围修改操作已存在,请勿重复提交!");
        }
        // 校验入参规格品和系统相同规格品的起始终止时间是否重叠
        StringBuilder crossoverBuilder = new StringBuilder();
        checkMadeCrossData(crossoverBuilder, manufactureVOList, tempList);
        if (ObjectUtils.isNotEmpty(crossoverBuilder)) {
            throw new CommonApplicationException("检测到ITEM:" + crossoverBuilder + "和已存在数据起始终止时间重叠,请修改后保存!");
        }
    }

    private void checkMadeCrossData(StringBuilder crossoverBuilder, List<ManufactureBottomVO> manufactureVOList, List<ManufactureBottomVO> tempList) {
        if (CollectionUtils.isEmpty(tempList)) {
            tempList.addAll(manufactureVOList);
        }
        for (ManufactureBottomVO manufactureVO : manufactureVOList) {
            for (ManufactureBottomVO madeVO : tempList) {
                if (manufactureVO.getItemCode().equals(madeVO.getItemCode())) {
                    compareMadeStartAndEndPeriod(crossoverBuilder, manufactureVO, madeVO);
                }
            }
        }
    }

    private void compareMadeStartAndEndPeriod(StringBuilder crossoverBuilder, ManufactureBottomVO manufactureVO, ManufactureBottomVO madeVO) {
        // 比较起始终止日期
        if (madeVO.getStartPeriod() > manufactureVO.getStartPeriod() && madeVO.getEndPeriod() < manufactureVO.getEndPeriod()) {
            if (!crossoverBuilder.toString().contains(manufactureVO.getItemCode())) {
                crossoverBuilder.append(manufactureVO.getItemCode() + " ");
                return;
            }
        }
        if (madeVO.getStartPeriod() < manufactureVO.getStartPeriod() && madeVO.getEndPeriod() < manufactureVO.getEndPeriod()
                && madeVO.getEndPeriod() > manufactureVO.getStartPeriod()) {
            if (!crossoverBuilder.toString().contains(manufactureVO.getItemCode())) {
                crossoverBuilder.append(manufactureVO.getItemCode() + " ");
                return;
            }
        }
        if (madeVO.getStartPeriod() > manufactureVO.getStartPeriod() && madeVO.getEndPeriod() > manufactureVO.getEndPeriod()
                && madeVO.getStartPeriod() < manufactureVO.getEndPeriod()) {
            if (!crossoverBuilder.toString().contains(manufactureVO.getItemCode())) {
                crossoverBuilder.append(manufactureVO.getItemCode() + " ");
                return;
            }
        }
        if (madeVO.getStartPeriod().equals(manufactureVO.getStartPeriod()) || madeVO.getEndPeriod().equals(manufactureVO.getEndPeriod())
                || madeVO.getStartPeriod().equals(manufactureVO.getEndPeriod()) || madeVO.getEndPeriod().equals(manufactureVO.getStartPeriod())) {
            if (!crossoverBuilder.toString().contains(manufactureVO.getItemCode())) {
                crossoverBuilder.append(manufactureVO.getItemCode() + " ");
            }
        }
    }

    private void checkMadeRepeatData(List<ManufactureBottomVO> manufactureVOList, List<ManufactureBottomVO> tempList, StringBuilder doubleBuilder) {
        List<ManufactureBottomVO> combineMadeList = new ArrayList<>();
        combineMadeList.addAll(manufactureVOList);
        combineMadeList.addAll(tempList);
        // 数据重复校验
        Map<String, List<ManufactureBottomVO>> repeatList = combineMadeList.stream()
                .collect(Collectors.groupingBy(
                        x -> x.getItemCode() + "#" + x.getStartPeriod() + "#" + x.getEndPeriod()));
        List<String> repeatCount = repeatList.keySet().stream().
                filter(key -> repeatList.get(key).size() > 1).distinct().collect(Collectors.toList());
        if (repeatCount.size() > 0) {
            for (String combine : repeatCount) {
                String item = combine.split("#")[0];
                if (!doubleBuilder.toString().contains(item)) {
                    doubleBuilder.append(item + " ");
                }
            }
        }
    }

    private void checkMadeParamItem(List<ManufactureBottomVO> manufactureVOList, StringBuilder paramBuilder) {
        Map<String, List<ManufactureBottomVO>> manufactureList = manufactureVOList.stream().map(param -> {
            ManufactureBottomVO paramVo = new ManufactureBottomVO();
            paramVo.setItemCode(param.getItemCode());
            paramVo.setItemCnName(param.getItemCnName());
            paramVo.setStartPeriod(param.getStartPeriod());
            paramVo.setEndPeriod(param.getEndPeriod());
            return paramVo;
        }).distinct().collect(Collectors.groupingBy(ManufactureBottomVO::getItemCode));
        List<String> itemCodeList = manufactureList.keySet().stream().filter(key -> manufactureList.get(key).size() > 1).distinct().collect(Collectors.toList());

        if (itemCodeList.size() > 0) {
            for (Map.Entry<String, List<ManufactureBottomVO>> paramEntry : manufactureList.entrySet()) {
                String entryKey = paramEntry.getKey();
                itemCodeList.stream().forEach(all -> {
                    if (entryKey.equals(all)) {
                        List<ManufactureBottomVO> paramEntryValue = paramEntry.getValue();
                        List<ManufactureBottomVO> tempList = new ArrayList<>();
                        checkMadeCrossData(paramBuilder, paramEntryValue, tempList);
                    }
                });
            }
        }
    }

    private StringBuilder checkMadeEmptyItem(List<ManufactureBottomVO> manufactureVOList, StringBuilder emptyBuilder)
            throws UnsupportedEncodingException {
        for (ManufactureBottomVO baseVO : manufactureVOList) {
            if (StrUtil.isBlank(baseVO.getShippingObjectCode())) {
                return emptyBuilder.append("发货对象编码为空;");
            }
            if (StrUtil.isBlank(baseVO.getShippingObjectCnName())) {
                return emptyBuilder.append("发货对象名称;");
            }
            if (StrUtil.isBlank(baseVO.getManufactureObjectCode())) {
                return emptyBuilder.append("制造对象编码为空;");
            }
            if (StrUtil.isBlank(baseVO.getManufactureObjectCnName())) {
                return emptyBuilder.append("制造对象名称为空;");
            }
            if (StrUtil.isBlank(baseVO.getItemCode())) {
                return emptyBuilder.append("ITEM编码为空;");
            }
            if (null == baseVO.getStartPeriod()) {
                return emptyBuilder.append("起始期为空;");
            }
            if (null == baseVO.getEndPeriod()) {
                return emptyBuilder.append("终止期为空;");
            }
            if (StrUtil.isBlank(baseVO.getModifyReason())) {
                return emptyBuilder.append("修改理由为空;");
            } else {
                // 截取修改理由
                interceptModifyReason(baseVO);
            }
            if (StrUtil.isBlank(baseVO.getImpactQty())) {
                return emptyBuilder.append("影响ITEM数量为空;");
            }
        }
        return emptyBuilder;
    }

    private void interceptModifyReason(ManufactureBottomVO baseVO) throws UnsupportedEncodingException {
        String modifyReason = baseVO.getModifyReason().trim();
        int length = modifyReason.getBytes("UTF-8").length;
        if (length >= 2000) {
            String substring = modifyReason.substring(0, 666);
            baseVO.setModifyReason(substring);
        } else {
            baseVO.setModifyReason(modifyReason);
        }
    }

    private List<ManufactureBottomVO> getMadeTempList(List<ManufactureBottomVO> manufactureVOList, List<ManufactureBottomVO> madeReviewVOList) {
        List<ManufactureBottomVO> tempList = new ArrayList<>();
        tempList.addAll(madeReviewVOList);
        for (int i = 0; i < manufactureVOList.size(); i++) {
            if (CommonConstEnum.ModifyType.MODIFY.getValue().equals(manufactureVOList.get(i).getModifyType())) {
                for (int j = 0; j < tempList.size(); j++) {
                    if (manufactureVOList.get(i).getOldItemCode().equals(tempList.get(j).getItemCode())) {
                        if (manufactureVOList.get(i).getOldStartPeriod().equals(tempList.get(j).getStartPeriod())
                                && manufactureVOList.get(i).getOldEndPeriod().equals(tempList.get(j).getEndPeriod())) {
                            tempList.remove(tempList.get(j));
                        }
                    }
                }
            }
        }
        return tempList;
    }

    @Override
    @JalorOperation(code = "revokeManufactureModify", desc = "制造成本-底层数据审视撤销接口")
    @Audit(module = "configBottomReviewService-revokeManufactureModify", operation = "revokeManufactureModify",
            message = "制造成本-底层数据审视撤销接口")
    public ResultDataVO revokeManufactureModify(List<ManufactureBottomVO> manufactureVOList)
            throws CommonApplicationException {
        LOGGER.info(">>>Begin ConfigDimensionService::revokeManufactureModify");
        if (!CollectionUtil.isNullOrEmpty(manufactureVOList) && manufactureVOList.size() > com.huawei.it.fcst.constant.Constant.IntegerEnum.MAX_SIZE.getValue()) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR, "操作的数据过多！");
        }
        for (ManufactureBottomVO manufactureBottomVO : manufactureVOList) {
            if (null == manufactureBottomVO.getVersionId()) {
                throw new CommonApplicationException("维度版本为空！");
            }
            if (!CommonConstEnum.ModifyType.REVOKE.getValue().equals(manufactureBottomVO.getModifyType())) {
                throw new CommonApplicationException("撤销理由不符,无法进行撤销!");
            }
        }
        Long versionId = manufactureVOList.get(0).getVersionId();
        String dataType = manufactureVOList.get(0).getDataType();
        String industryOrg = manufactureVOList.get(0).getIndustryOrg();
        ManufactureBottomVO baseVO = ManufactureBottomVO.builder()
                .versionId(versionId)
                .caliberFlag(manufactureVOList.get(0).getCaliberFlag())
                .build();
        baseVO.setIndustryOrg(industryOrg);
        List<ManufactureBottomVO> madeReviewVOList = manufactureReviewDao.findMadeReviewVOList(baseVO);
        // 创建新版本
        DmFocVersionInfoDTO newVersion = createVersionVO(dataType, versionId, industryOrg);
        // 组转数据
        for (ManufactureBottomVO madeBaseVO : madeReviewVOList) {
            madeBaseVO.setVersionId(newVersion.getVersionId());
            for (ManufactureBottomVO paramVO : manufactureVOList) {
                if (madeBaseVO.getItemCode().equals(paramVO.getItemCode())
                        && madeBaseVO.getStartPeriod().equals(paramVO.getStartPeriod())
                        && madeBaseVO.getEndPeriod().equals(paramVO.getEndPeriod())) {
                    madeBaseVO.setModifyReason(paramVO.getModifyReason());
                    madeBaseVO.setModifyType(CommonConstEnum.ModifyType.REVOKE.getValue());
                    madeBaseVO.setLastUpdateDate(new Date());
                    madeBaseVO.setLastUpdatedBy(UserInfoUtils.getUserCn());
                    madeBaseVO.setPageFlag(CommonConstant.HISTORY_PAGE);
                } else {
                    madeBaseVO.setPageFlag(CommonConstant.ABNORMAL_PAGE);
                }
            }
        }
        // 插入新数据
        manufactureReviewDao.createMadeReviewList(madeReviewVOList, TableNameVO.getTablePreFix(industryOrg));
        return ResultDataVO.success(newVersion);
    }

    @Override
    @JalorOperation(code = "exportManufacture", desc = "制造成本-底层数据审视导出接口")
    @Audit(module = "configBottomReviewService-exportManufacture", operation = "exportManufacture",
            message = "制造成本-底层数据审视导出接口")
    public ResultDataVO exportManufacture(ManufactureBottomVO manufactureBottomVO, HttpServletResponse response) {
        LOGGER.info(">>>Begin ConfigBottomReviewService::exportManufacture");
        try {
            exportMft(manufactureBottomVO, response);
        } catch (IOException | ApplicationException e) {
            LOGGER.error("exportExcel error: {}", e.getMessage());
        }
        return ResultDataVO.success(MapUtil.of(new Object[][]{{"expFlag", true}}));
    }

    private void exportMft(ManufactureBottomVO manufactureBottomVO, HttpServletResponse response)
            throws CommonApplicationException, IOException {
        Timestamp creationDate = new Timestamp(System.currentTimeMillis());
        List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
        List<HeaderVo> headers = new LinkedList<>();
        String modelType = manufactureBottomVO.getModelType();
        String sheetName = "";
        // 个人中心页面模块
        String pageModel = setPageModule(manufactureBottomVO.getIndustryOrg(),manufactureBottomVO.getCostType());
        ManufactureBottomVO baseVO = ManufactureBottomVO.builder()
                .caliberFlag(manufactureBottomVO.getCaliberFlag())
                .pageFlag(manufactureBottomVO.getPageFlag())
                .dataType(manufactureBottomVO.getDataType())
                .build();
        if (CommonConstant.ABNORMAL_PAGE.equals(manufactureBottomVO.getPageFlag())) {
            baseVO.setVersionId(manufactureBottomVO.getVersionId());
        }
        baseVO.setIndustryOrg(manufactureBottomVO.getIndustryOrg());
        List<Map> baseReviewList = manufactureReviewDao.findBaseReviewList(baseVO);
        DmFocVersionInfoDTO madeReviewVersion = dmFocVersionDao.findMadeReviewVersion(baseVO);
        setTablePreFix(modelType, baseReviewList);
        if (IndustryConst.DataReview.MODIFY.getValue().equals(modelType)) {
            sheetName = CommonConstant.ENTRY_ABNORMAL_DATA;
            getManufactureHead(headers);
            getModifyHead(headers);
        } else {
            sheetName = CommonConstant.HISTORICAL_MODIFICATION_RECORDS;
            headers.add(new HeaderVo("操作类型", "modify_type", CellType.STRING, true, 10 * 480));
            headers.add(new HeaderVo("版本号", "version", CellType.STRING, true, 12 * 480));
            getManufactureHead(headers);
            getRecordHead(headers);
        }
        // 文件名
        String filename = getMadeFilename(manufactureBottomVO);
        // 获取版本信息
        String version = null;
        if (null != madeReviewVersion) {
            version = madeReviewVersion.getVersion();
        }
        // 获取表头模型
        List<AbstractExcelTitleVO> excelTitleVOS = new ArrayList<>();
        HistoryInputVO build = HistoryInputVO.builder()
                .caliberFlag(manufactureBottomVO.getCaliberFlag())
                .costType(manufactureBottomVO.getCostType())
                .fileName(filename)
                .version(version)
                .modelType(modelType)
                .topCateHeader(headers)
                .build();
        List<ExportExcelVo> exportExcelVoList = getExportExcelVOs(build, titleVoList, sheetName, baseReviewList, excelTitleVOS);
        DmFoiImpExpRecordVO expRecordVO = excelUtil.expSelectColumnExcel(exportExcelVoList, response);
        expRecordVO.setModuleType(pageModel);
        // 插入导出数据
        expRecordVO.setCreationDate(creationDate);
        expRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        statisticsExcelService.insertExportExcelRecord(expRecordVO);
    }

    @NotNull
    private String getMadeFilename(ManufactureBottomVO manufactureBottomVO) {
        return CommonConstant.ENTRY_ABNORMAL_DATA.concat(
                new SimpleDateFormat("_yyyy-MM-dd HH_mm_ss").format(new Date()));
    }

    private void getManufactureHead(List<HeaderVo> headers) {
        headers.add(new HeaderVo("发货对象", "shipping_object_cn_name", CellType.STRING, true, 12 * 480));
        headers.add(new HeaderVo("制造对象", "manufacture_object_cn_name", CellType.STRING, true, 12 * 480));
        headers.add(new HeaderVo("ITEM", "item_code", CellType.STRING, true, 12 * 480));
        headers.add(new HeaderVo("起始期", "start_period", CellType.STRING, true, 10 * 480));
        headers.add(new HeaderVo("终止期", "end_period", CellType.STRING, true, 10 * 480));
    }

    private void getRecordHead(List<HeaderVo> headers) {
        headers.add(new HeaderVo("操作人", "last_updated_by", CellType.STRING, true, 12 * 480));
        headers.add(new HeaderVo("操作时间", "last_update_date", CellType.STRING, true, 12 * 480));
        headers.add(new HeaderVo("修改/撤销理由", "modify_reason", CellType.STRING, true, 12 * 480));
        headers.add(new HeaderVo("影响ITEM数量", "impact_qty", CellType.STRING, true, 12 * 480));
    }

    private void getModifyHead(List<HeaderVo> headers) {
        headers.add(new HeaderVo("修改理由", "modify_reason_m", CellType.STRING, true, 12 * 480));
        headers.add(new HeaderVo("撤销理由", "modify_reason_r", CellType.STRING, true, 12 * 480));
        headers.add(new HeaderVo("影响ITEM数量", "impact_qty", CellType.STRING, true, 12 * 480));
    }

    @JalorOperation(code = "getProModifyListByPage", desc = "采购成本-ITEM异常数据录入分页查询接口&&历史修改记录查询")
    @Override
    public ResultDataVO getProModifyListByPage(ProcurementBottomVO procurementBottomVO)
            throws CommonApplicationException {
        LOGGER.info("Begin configBottomReviewService::getProModifyListByPage");
        if (ObjectUtils.isEmpty(procurementBottomVO.getPageIndex()) || ObjectUtils.isEmpty(
                procurementBottomVO.getPageSize())) {
            throw new CommonApplicationException("分页信息参数不正确");
        }
        PageVO pageVO = new PageVO();
        pageVO.setCurPage(procurementBottomVO.getPageIndex());
        pageVO.setPageSize(procurementBottomVO.getPageSize());
        procurementBottomVO.setDelFlag(CommonConstant.IS_NOT);
        // 数据库查询分页数据
        PagedResult<ProcurementBottomVO> procurementPageResult = procurementReviewDao.findByPage(procurementBottomVO, pageVO);

        Map procurementMap = new LinkedHashMap();
        procurementMap.put("result", procurementPageResult.getResult());
        procurementMap.put("pageVO", procurementPageResult.getPageVO());
        return ResultDataVO.success(procurementMap);
    }

    @JalorOperation(code = "getMftModifyListByPage", desc = "制造成本-ITEM异常数据录入分页查询接口&&历史修改记录查询")
    @Override
    public ResultDataVO getMftModifyListByPage(ManufactureBottomVO manufactureBottomVO)
            throws CommonApplicationException {
        LOGGER.info("Begin configBottomReviewService::getMftModifyListByPage");
        if (ObjectUtils.isEmpty(manufactureBottomVO.getPageIndex()) || ObjectUtils.isEmpty(
                manufactureBottomVO.getPageSize())) {
            throw new CommonApplicationException("分页信息参数不正确");
        }
        PageVO pageVO = new PageVO();
        pageVO.setCurPage(manufactureBottomVO.getPageIndex());
        pageVO.setPageSize(manufactureBottomVO.getPageSize());
        manufactureBottomVO.setDelFlag(CommonConstant.IS_NOT);
        // 数据库查询分页数据
        PagedResult<ManufactureBottomVO> manufacturePageResult = manufactureReviewDao.findByPage(manufactureBottomVO, pageVO);

        Map manufactureMap = new LinkedHashMap();
        manufactureMap.put("result", manufacturePageResult.getResult());
        manufactureMap.put("pageVO", manufacturePageResult.getPageVO());
        return ResultDataVO.success(manufactureMap);
    }
}
