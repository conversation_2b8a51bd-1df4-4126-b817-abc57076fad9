package com.huawei.it.fcst.industry.index.impl.dataprocess;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.util.concurrent.ForwardingBlockingQueue;
import com.huawei.it.fcst.industry.index.constant.CommonConstant;
import com.huawei.it.fcst.industry.index.dao.IDataCipherTextDao;
import com.huawei.it.fcst.industry.index.impl.month.MonthAnalysisServiceTest;
import com.huawei.it.fcst.industry.index.impl.mqs.MessageProducer;
import com.huawei.it.fcst.industry.index.service.dataprocess.IDataProcessService;
import com.huawei.it.fcst.config.ConfigUtil;
import com.huawei.it.fcst.industry.index.utils.TestUtils;
import com.huawei.it.fcst.industry.index.utils.UserInfoUtils;
import com.huawei.it.fcst.industry.index.vo.ciphertext.CipherTextDataVO;
import com.huawei.it.fcst.industry.index.vo.ciphertext.FunctionParamVO;
import com.huawei.it.fcst.industry.index.vo.ciphertext.VarifyTaskVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.ioc.Jalor;
import com.huawei.it.jalor5.core.ioc.delegate.JalorApplicationContext;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.registry.RegistryVO;
import com.huawei.it.jalor5.registry.service.IRegistryQueryService;
import com.huawei.it.jalor5.registry.service.IRegistryService;
import com.huawei.it.jalor5.security.UserVO;
import com.huawei.it.soa.common.SoaException;
import com.huawei.it.soa.util.SoaAppTokenClientUtil;
import nonapi.io.github.classgraph.concurrency.AutoCloseableExecutorService;
import org.apache.ibatis.cursor.Cursor;
import org.apache.ibatis.cursor.defaults.DefaultCursor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.multipart.support.DefaultMultipartHttpServletRequest;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;

import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.mockito.ArgumentMatchers.any;
import static org.powermock.api.mockito.PowerMockito.mock;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 * @since 2023/5/10
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest( {UserInfoUtils.class,ConfigUtil.class, SoaAppTokenClientUtil.class, Jalor.class})
public class DataProcessServiceTest {
    private static final Logger LOGGER = LogManager.getLogger(MonthAnalysisServiceTest.class);
    private static final ObjectMapper MAPPER = new ObjectMapper();
    @InjectMocks
    private DataProcessService dataProcess;
    @Mock
    private IDataCipherTextDao iDataCipherTextDao;

    @Mock
    private TaskExecutorProcessService taskExecutorProcess;

    @Mock
    private IRegistryQueryService iRegistryQueryService;

    @Mock
    private IRegistryService registryService;

    @Mock
    private IDataProcessService self;

    @Mock
    private ConfigUtil configUtil;

    @Mock
    private MessageProducer messageProducer;

    @Mock
    DefaultMultipartHttpServletRequest httpServletRequest;

    private final static String DM_FOC_DATA_ENCRYPT = "F_DM_FOC_DATA_ENCRYPT";
    private static final String DATA_PROCESS_SWITCH = "App.Config.Industry.InitDataSwitch";
    private static final String PROCESS_STATUS = "App.Config.Industry.ProcessStatus";
    private static final String PROCESS_PROCESSING = "PROCESSING";

    private JSONObject json;

    @Before
    public void before() throws Exception {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);
        json = TestUtils.getTestArg(
            "/com/huawei/it/fcst/industry/index/impl/dataprocess/DataProcessService/processTask.json");
    }

    @Test
    public void startDataProcessTask() throws ApplicationException, IOException, SoaException {
        RegistryVO registrySwitch = new RegistryVO();
        CipherTextDataVO cipherTextDataVO = new CipherTextDataVO();
        cipherTextDataVO.setPeriodId(202201L);
        cipherTextDataVO.setPrimaryId("234");
        cipherTextDataVO.setRmbCostAmt("234");
        cipherTextDataVO.setRmbFactRateGcAmt("234");
        cipherTextDataVO.setCreationDate(new Date());
        cipherTextDataVO.setSourceTableName("DWL_PROD_BOM_ITEM_REV_DETAIL_I");
        cipherTextDataVO.setTargetTableName("DM_FOC_REVENUE_JAVA_PRIMARY_ENCRYPT_T");
        registrySwitch.setValue("open");
        PowerMockito.doReturn(registrySwitch).when(iRegistryQueryService).findRegistryByPathNoAssert(DATA_PROCESS_SWITCH,true);
        RegistryVO processStatus = new RegistryVO();
        processStatus.setValue("PROCESSINGS");
        PowerMockito.doReturn(processStatus).when(iRegistryQueryService).findRegistryByPathNoAssert(PROCESS_STATUS,true);
        PowerMockito.doReturn(1). when(iDataCipherTextDao).delByPeriodId(cipherTextDataVO);
        dataProcess.startDataProcessTask(cipherTextDataVO);
        assertThatNoException();
    }

    @Test
    public void startDataProcessTask1Test() throws ApplicationException, IOException, SoaException {
        RegistryVO registrySwitch = new RegistryVO();
        CipherTextDataVO cipherTextDataVO = new CipherTextDataVO();
        cipherTextDataVO.setSourceTableName("DWL_PROD_BOM_ITEM_SHIP_DIM_I");
        cipherTextDataVO.setTargetTableName("DM_FOC_JAVA_PRIMARY_ENCRYPT_T");
        registrySwitch.setValue("open");
        PowerMockito.doReturn(registrySwitch).when(iRegistryQueryService).findRegistryByPathNoAssert(DATA_PROCESS_SWITCH,true);
        RegistryVO processStatus = new RegistryVO();
        processStatus.setValue("PROCESSINGS");
        PowerMockito.doReturn(processStatus).when(iRegistryQueryService).findRegistryByPathNoAssert(PROCESS_STATUS,true);
        PowerMockito.doReturn(1). when(iDataCipherTextDao).delByPeriodId(cipherTextDataVO);
        when(httpServletRequest.getHeader(any(String.class))).thenReturn("test");
        dataProcess.startDataProcessTask(cipherTextDataVO);
        assertThatNoException();
    }
    @Test
    public void startDataProcessTask5Test() throws ApplicationException, IOException, SoaException {
        RegistryVO registrySwitch = new RegistryVO();
        CipherTextDataVO cipherTextDataVO = new CipherTextDataVO();
        cipherTextDataVO.setPeriodId(202201L);
        cipherTextDataVO.setPrimaryId("234");
        cipherTextDataVO.setRmbCostAmt("234");
        cipherTextDataVO.setRmbFactRateGcAmt("234");
        cipherTextDataVO.setCreationDate(new Date());
        cipherTextDataVO.setSourceTableName("DWL_PROD_BOM_ITEM_REV_DETAIL_I");
        cipherTextDataVO.setTargetTableName("DM_FOC_REVENUE_JAVA_PRIMARY_ENCRYPT_T");
        registrySwitch.setValue("opens");
        PowerMockito.doReturn(registrySwitch).when(iRegistryQueryService).findRegistryByPathNoAssert(DATA_PROCESS_SWITCH,true);
        RegistryVO processStatus = new RegistryVO();
        processStatus.setValue("PROCESSINGS");
        PowerMockito.doReturn(processStatus).when(iRegistryQueryService).findRegistryByPathNoAssert(PROCESS_STATUS,true);
        PowerMockito.doReturn(1). when(iDataCipherTextDao).delByPeriodId(cipherTextDataVO);
        dataProcess.startDataProcessTask(cipherTextDataVO);
        assertThatNoException();
    }

    @Test
    public void startDataProcessTask2Test() throws ApplicationException, IOException {
        try {
            CipherTextDataVO cipherTextDataVO = new CipherTextDataVO();
            dataProcess.startDataProcessTask(cipherTextDataVO);
        } catch (CommonApplicationException | SoaException e) {
            assertThatExceptionOfType(ApplicationException.class);
            LOGGER.info("Parameter error");
        }
    }

    @Test
    public void startDataProcessTask3Test() throws ApplicationException, IOException {
        try {
            CipherTextDataVO cipherTextDataVO = new CipherTextDataVO();
            cipherTextDataVO.setPeriodId(2022014L);
            dataProcess.startDataProcessTask(cipherTextDataVO);
        } catch (CommonApplicationException | SoaException e) {
            assertThatExceptionOfType(ApplicationException.class);
            LOGGER.info("Parameter error");
        }
    }
    @Test
    public void startDataProcessTask4Test() throws ApplicationException, IOException {
        try {
            RegistryVO registrySwitch = new RegistryVO();
            CipherTextDataVO cipherTextDataVO = new CipherTextDataVO();
            cipherTextDataVO.setPeriodId(202201L);
            registrySwitch.setValue("open");
            PowerMockito.doReturn(registrySwitch).when(iRegistryQueryService).findRegistryByPathNoAssert(DATA_PROCESS_SWITCH,true);
            dataProcess.startDataProcessTask(cipherTextDataVO);
        } catch (CommonApplicationException | SoaException e) {
            assertThatExceptionOfType(ApplicationException.class);
            LOGGER.info("Check whether the data dictionary node is configured");
        }
    }
    @Test
    public void startDataProcessTask6Test() throws ApplicationException, IOException {
        try {
            RegistryVO registrySwitch = new RegistryVO();
            CipherTextDataVO cipherTextDataVO = new CipherTextDataVO();
            cipherTextDataVO.setPeriodId(202201L);
            cipherTextDataVO.setPrimaryId("234");
            cipherTextDataVO.setRmbCostAmt("234");
            cipherTextDataVO.setRmbFactRateGcAmt("234");
            cipherTextDataVO.setCreationDate(new Date());
            registrySwitch.setValue("open");
            PowerMockito.doReturn(registrySwitch).when(iRegistryQueryService).findRegistryByPathNoAssert(DATA_PROCESS_SWITCH,true);
            RegistryVO processStatus = new RegistryVO();
            processStatus.setValue("PROCESSING");
            PowerMockito.doReturn(processStatus).when(iRegistryQueryService).findRegistryByPathNoAssert(PROCESS_STATUS,true);
            dataProcess.startDataProcessTask(cipherTextDataVO);
        } catch (CommonApplicationException | SoaException e) {
            assertThatExceptionOfType(ApplicationException.class);
            LOGGER.info("The task is executing");
        }
    }

    @Test
    public void startDBFunction() throws Exception {
        FunctionParamVO functionParamVO = new FunctionParamVO();
        functionParamVO.setJobId("1111111");
        functionParamVO.setCallback("http://123242355");
        mockStatic(UserInfoUtils.class);
        when(UserInfoUtils.class, "getUserId").thenReturn(12424L);
        self.startDBFunctionTask(functionParamVO);
        dataProcess.startDBFunction(functionParamVO,httpServletRequest);
        assertThatNoException();
    }

    @Test
    public void startDBFunctionTask() throws Exception {
        FunctionParamVO functionParamVO = new FunctionParamVO();
        functionParamVO.setFuncName("F_DM_FOC_DATA_ENCRYPT");
        functionParamVO.setPeriodId("2022/03");
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn("1333333333333").when(configUtil).get16PlainText();
        Map<String, Object> parameter = new HashMap<String, Object>();
        parameter.put("X_RESULT_STATUS","success");
        PowerMockito.doReturn(parameter).when(iDataCipherTextDao).startFunctionTask(parameter);
        dataProcess.startDBFunctionTask(functionParamVO);
        assertThatNoException();
    }

    // true false false
    @Test
    public void startDBFunctionTask1Test() throws Exception {
        FunctionParamVO functionParamVO = new FunctionParamVO();
        functionParamVO.setFuncName("F_DM_FOC_DATA_ENCRYPT");
        functionParamVO.setPeriodId("2022/03");
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn("1333333333333").when(configUtil).get16PlainText();
        Map<String, Object> parameter = new HashMap<String, Object>();
        parameter.put("X_RESULT_STATUS","SUCCESS");
        PowerMockito.doReturn(parameter).when(iDataCipherTextDao).startFunctionTask(parameter);
        mockStatic(SoaAppTokenClientUtil.class);
        when(SoaAppTokenClientUtil.class, "getSoaAppId").thenReturn("12444");
        dataProcess.startDBFunctionTask(functionParamVO);
        assertThatNoException();
    }

    @Test
    public void startDBFunctionTask2Test() throws IOException, SoaException, CommonApplicationException {
        FunctionParamVO functionParamVO = new FunctionParamVO();
        functionParamVO.setPeriodId("2022/03");
        dataProcess.startDBFunctionTask(functionParamVO);
        assertThatNoException();
    }

    @Test
    public void startDBFunctionTask3Test() throws Exception {
        FunctionParamVO functionParamVO = new FunctionParamVO();
        functionParamVO.setFuncName("F_DM_FOC_DATA_ENCRYPT11");
        functionParamVO.setPeriodId("2022/03");
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn("1333333333333").when(configUtil).get16PlainText();
        Map<String, Object> parameter = new HashMap<String, Object>();
        parameter.put("X_RESULT_STATUS","success");
        PowerMockito.doReturn(parameter).when(iDataCipherTextDao).startFunctionTask(parameter);
        dataProcess.startDBFunctionTask(functionParamVO);
        assertThatNoException();
    }

    // true false true
    @Test
    public void startDBFunctionTask4Test() throws Exception {
        FunctionParamVO functionParamVO = new FunctionParamVO();
        functionParamVO.setFuncName("F_DM_FOC_DATA_ENCRYPT");
        functionParamVO.setPeriodId("2022-03");
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn("1333333333333").when(configUtil).get16PlainText();
        Map<String, Object> parameter = new HashMap<String, Object>();
        parameter.put("X_RESULT_STATUS","SUCCESS");
        PowerMockito.doReturn(parameter).when(iDataCipherTextDao).startFunctionTask(parameter);
        mockStatic(SoaAppTokenClientUtil.class);
        when(SoaAppTokenClientUtil.class, "getSoaAppId").thenReturn("12444");
        dataProcess.startDBFunctionTask(functionParamVO);
        assertThatNoException();
    }

    // false false true
    @Test
    public void startDBFunctionTask5Test() throws Exception {
        FunctionParamVO functionParamVO = new FunctionParamVO();
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn("1333333333333").when(configUtil).get16PlainText();
        Map<String, Object> parameter = new HashMap<String, Object>();
        parameter.put("X_RESULT_STATUS","SUCCESS");
        PowerMockito.doReturn(parameter).when(iDataCipherTextDao).startFunctionTask(parameter);
        mockStatic(SoaAppTokenClientUtil.class);
        when(SoaAppTokenClientUtil.class, "getSoaAppId").thenReturn("12444");
        dataProcess.startDBFunctionTask(functionParamVO);
        assertThatNoException();
    }

    @Test
    public void dataProcess() throws Exception {
        CipherTextDataVO cipherTextDataVO=new CipherTextDataVO();
        IRequestContext requestContext=null;

        try {
            dataProcess.dataProcess(cipherTextDataVO, requestContext);
        } catch (Exception e) {
            assertThatExceptionOfType(ApplicationException.class);
            LOGGER.error("入参清单数据版本为空");
        }
    }

    @Test
    public void initDataProcessTaskTest() throws ApplicationException, IOException {
        CipherTextDataVO cipherTextDataVO = JSONObject.parseObject(json.getString("CipherTextDataVO"),CipherTextDataVO.class);
        when(iDataCipherTextDao.countVarifyTask()).thenReturn(0);
        dataProcess.initDataProcessTask(cipherTextDataVO);
        assertThatNoException();
        try {
            cipherTextDataVO.setTargetTableName("DM_FOC_REVENUE_JAVA_PRIMARY_ENCRYPT_Tdd");
            dataProcess.initDataProcessTask(cipherTextDataVO);
        } catch (Exception e) {
            assertThatExceptionOfType(ApplicationException.class);
        }

        try {
            cipherTextDataVO.setSourceTableName("DWL_PROD_BOM_ITEM_REV_DETAIL_Idd");
            dataProcess.initDataProcessTask(cipherTextDataVO);
        } catch (Exception e) {
            assertThatExceptionOfType(ApplicationException.class);
        }

        cipherTextDataVO.setSourceTableName("DWL_PROD_BOM_ITEM_REV_DETAIL_I");
        cipherTextDataVO.setTargetTableName("DM_FOC_REVENUE_JAVA_PRIMARY_ENCRYPT_T");
        dataProcess.initDataProcessTask(cipherTextDataVO);
        assertThatNoException();
        try {
            cipherTextDataVO.setTargetTableName("DM_FOC_REVENUE_JAVA_PRIMARY_ENCRYPT_Tdd");
            dataProcess.initDataProcessTask(cipherTextDataVO);
        } catch (Exception e) {
            assertThatExceptionOfType(ApplicationException.class);
        }

        try {
            cipherTextDataVO.setSourceTableName("DWL_PROD_BOM_ITEM_REV_DETAIL_Idd");
            dataProcess.initDataProcessTask(cipherTextDataVO);
        } catch (Exception e) {
            assertThatExceptionOfType(ApplicationException.class);
        }

        try {
            when(iDataCipherTextDao.countVarifyTask()).thenReturn(2);
            dataProcess.initDataProcessTask(cipherTextDataVO);
        } catch (Exception e) {
            assertThatExceptionOfType(ApplicationException.class);
        }

        try {
            cipherTextDataVO.setPeriodIds(Arrays.asList(1L,2L,3L,4L,5L,6L,7L,8L,9L,10L,11L,12L,13L,14L));
            dataProcess.initDataProcessTask(cipherTextDataVO);
        } catch (Exception e) {
            assertThatExceptionOfType(ApplicationException.class);
        }

        try {
            cipherTextDataVO.setPeriodIds(null);
            dataProcess.initDataProcessTask(cipherTextDataVO);
        } catch (Exception e) {
            assertThatExceptionOfType(ApplicationException.class);
        }
    }

    @Test
    public void dataProcessTest() throws Exception {
        CipherTextDataVO cipherTextDataVO = JSONObject.parseObject(json.getString("CipherTextDataVO"), CipherTextDataVO.class);
        RegistryVO registry = JSONObject.parseObject(json.getString("RegistryVO"), RegistryVO.class);
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn("1333333333333").when(configUtil).get16PlainText();
        IRequestContext requestContext = RequestContext.getCurrent();
        mockStatic(Jalor.class);
        JalorApplicationContext context = PowerMockito.mock(JalorApplicationContext.class);
        when(Jalor.getContext()).thenReturn(context);
        ThreadPoolTaskExecutor thread = PowerMockito.mock(ThreadPoolTaskExecutor.class);
        when(context.getBean("taskExecutor", ThreadPoolTaskExecutor.class)).thenReturn(thread);

        ThreadPoolExecutor pool = PowerMockito.mock(AutoCloseableExecutorService.class);
        when(thread.getThreadPoolExecutor()).thenReturn(pool);
        BlockingQueue que = PowerMockito.mock(ForwardingBlockingQueue.class);
        when(pool.getQueue()).thenReturn(que);
        when(que.size()).thenReturn(1);

        when(iRegistryQueryService.findRegistryByPathNoAssert(any(String.class), any(boolean.class))).thenReturn(registry);
        List<CipherTextDataVO> datas = new ArrayList<>();
        for (long idx = 1; idx <= 2000; idx++
        ) {
            CipherTextDataVO data = new CipherTextDataVO();
            data.setPeriodId(idx);
            datas.add(data);
        }
        Cursor<CipherTextDataVO> cursor = mock(DefaultCursor.class);
        when(cursor.iterator()).thenReturn(datas.listIterator());
        when(iDataCipherTextDao.getDataListStream(any(CipherTextDataVO.class))).thenReturn(cursor);
        dataProcess.dataProcess(cipherTextDataVO, requestContext);
        assertThatNoException();
        datas.removeIf(obj -> obj.getPeriodId() < 1999);
        cipherTextDataVO.setCallback(null);
        when(cursor.iterator()).thenReturn(datas.listIterator());
        dataProcess.dataProcess(cipherTextDataVO, requestContext);
        assertThatNoException();
    }

    @Test
    public void dataProcess1Test() throws Exception {
        CipherTextDataVO cipherTextDataVO = JSONObject.parseObject(json.getString("CipherTextDataVO"), CipherTextDataVO.class);
        VarifyTaskVO varifyTaskVO = JSONObject.parseObject(json.getString("VarifyTaskVO"), VarifyTaskVO.class);

        List<CipherTextDataVO> datas = new ArrayList<>();
        for (long idx = 1; idx <= 1000; idx++
        ) {
            CipherTextDataVO data = new CipherTextDataVO();
            data.setPeriodId(idx);
            datas.add(data);
        }
        Cursor<CipherTextDataVO> cursor = mock(DefaultCursor.class);
        when(cursor.iterator()).thenReturn(datas.listIterator());
        when(iDataCipherTextDao.getDataListStream(any(CipherTextDataVO.class))).thenReturn(cursor);
        dataProcess.dataProcess(cipherTextDataVO, varifyTaskVO);
        assertThatNoException();

        datas.removeIf(obj -> obj.getPeriodId() < 999);
        cipherTextDataVO.setCallback(null);
        when(cursor.iterator()).thenReturn(datas.listIterator());
        dataProcess.dataProcess(cipherTextDataVO, varifyTaskVO);
        assertThatNoException();
    }

    @Test
    public void periodPramVerifyTest() throws Exception {
        CipherTextDataVO cipherTextDataVO = JSONObject.parseObject(json.getString("CipherTextDataVO"), CipherTextDataVO.class);
        RegistryVO registry = JSONObject.parseObject(json.getString("RegistryVO"), RegistryVO.class);
        when(iRegistryQueryService.findRegistryByPathNoAssert(any(String.class), any(boolean.class))).thenReturn(registry);
        cipherTextDataVO.setSourceTableName(CommonConstant.S_SOURCE_TABLE_NAME);
        cipherTextDataVO.setTargetTableName(CommonConstant.S_TARGET_TABLE_NAME);
        cipherTextDataVO.setPeriodId(111111L);
        Whitebox.invokeMethod(dataProcess, "periodPramVerify", cipherTextDataVO);
        assertThatNoException();
        try {
            cipherTextDataVO.setPeriodId(1L);
            Whitebox.invokeMethod(dataProcess, "periodPramVerify", cipherTextDataVO);
        } catch (Exception e) {
            assertThatExceptionOfType(ApplicationException.class);
        }
        try {
            cipherTextDataVO.setPeriodId(null);
            Whitebox.invokeMethod(dataProcess, "periodPramVerify", cipherTextDataVO);
        } catch (Exception e) {
            assertThatExceptionOfType(ApplicationException.class);
        }
        registry.setValue("open");
        Whitebox.invokeMethod(dataProcess, "periodPramVerify", cipherTextDataVO);
        assertThatNoException();
        cipherTextDataVO.setPeriodId(111111L);
        Whitebox.invokeMethod(dataProcess, "periodPramVerify", cipherTextDataVO);
        assertThatNoException();
        cipherTextDataVO.setSourceTableName(CommonConstant.R_SOURCE_TABLE_NAME);
        cipherTextDataVO.setTargetTableName(CommonConstant.R_TARGET_TABLE_NAME);
        when(iRegistryQueryService.findRegistryByPathNoAssert(any(String.class), any(boolean.class))).thenReturn(null);
        Whitebox.invokeMethod(dataProcess, "periodPramVerify", cipherTextDataVO);
        assertThatNoException();
        try {
            cipherTextDataVO.setTargetTableName("dddd");
            Whitebox.invokeMethod(dataProcess, "periodPramVerify", cipherTextDataVO);
        } catch (Exception e) {
            assertThatExceptionOfType(ApplicationException.class);
        }
        try {
            cipherTextDataVO.setSourceTableName(CommonConstant.S_SOURCE_TABLE_NAME);
            Whitebox.invokeMethod(dataProcess, "periodPramVerify", cipherTextDataVO);
        } catch (Exception e) {
            assertThatExceptionOfType(ApplicationException.class);
        }
        try {
            cipherTextDataVO.setSourceTableName("dddd");
            Whitebox.invokeMethod(dataProcess, "periodPramVerify", cipherTextDataVO);
        } catch (Exception e) {
            assertThatExceptionOfType(ApplicationException.class);
        }
    }

    @Test
    public void updateProcessStatusTest() throws Exception {
        RegistryVO registry = JSONObject.parseObject(json.getString("RegistryVO"), RegistryVO.class);
        try {
            Whitebox.invokeMethod(dataProcess, "updateProcessStatus");
        } catch (Exception e) {
            assertThatExceptionOfType(ApplicationException.class);
        }
        when(iRegistryQueryService.findRegistryByPathNoAssert(any(String.class), any(boolean.class))).thenReturn(
            registry);

        try {
            registry.setValue("PROCESSING");
            Whitebox.invokeMethod(dataProcess, "updateProcessStatus");
        } catch (Exception e) {
            assertThatExceptionOfType(ApplicationException.class);
        }
    }

    @Test
    public void delByPeriodIdTest() throws Exception {
        dataProcess.delByPeriodId(null);
        assertThatNoException();
    }

    @Test
    public void startFunctionTaskTest() throws Exception {
        FunctionParamVO param = new FunctionParamVO();
        param.setFuncName(CommonConstant.DM_FOC_DATA_ENCRYPT);
        Map<String, Object> parameter = new HashMap<>();
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn("1333333333333").when(configUtil).get16PlainText();
        Whitebox.invokeMethod(dataProcess, "startFunctionTask", param, parameter);
        assertThatNoException();
        param.setPeriodId("123");
        Whitebox.invokeMethod(dataProcess, "startFunctionTask", param, parameter);
        assertThatNoException();
        param.setPeriodId("123/sd");
        Whitebox.invokeMethod(dataProcess, "startFunctionTask", param, parameter);
        assertThatNoException();
        param.setFuncName("dfs");
        Whitebox.invokeMethod(dataProcess, "startFunctionTask", param, parameter);
        assertThatNoException();
    }

    @Test
    public void executeLtsTest() throws Exception {
        mockStatic(SoaAppTokenClientUtil.class);
        when(SoaAppTokenClientUtil.getBasicTokenByAppCredential()).thenReturn("test");
        Whitebox.invokeMethod(dataProcess, "executeLts", "SUCCESS", "parameter", "test");
        assertThatNoException();
    }
}