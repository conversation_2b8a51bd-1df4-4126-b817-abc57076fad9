/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.vo.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * DataPermissionsVO Class
 *
 * <AUTHOR>
 * @since 2022-11-10
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "数据权限维度VO")
public class PriceDataPermissionsVO implements Serializable {
    private static final long serialVersionUID = 6110390781244573970L;

    private int roleId;

    private String roleName;

    @ApiModelProperty(value = "目录树")
    private Set<String> lv0DimensionSet = new HashSet<>();

    @ApiModelProperty(value = "LV0集合")
    private Set<String> lv1DimensionSet  = new HashSet<>();

    @ApiModelProperty(value = "LV1集合")
    private Set<String> lv2DimensionSet  = new HashSet<>();

    @ApiModelProperty(value = "国内/海外集合")
    private Set<String> overseaDimensionSet = new HashSet<>();

    @ApiModelProperty(value = "地区部集合")
    private Set<String> regionCodeDimensionSet = new HashSet<>();

    @ApiModelProperty(value = "非补齐的地区部集合")
    private Set<String> regionCodeDimensionTrueSet = new HashSet<>();

    @ApiModelProperty(value = "代表处集合")
    private Set<String> repofficeCodeDimensionSet = new HashSet<>();

    @ApiModelProperty(value = "大T系统部集合")
    private Set<String> signTopCustCategoryCodeSet = new HashSet<>();

    @ApiModelProperty(value = "子网系统部集合")
    private Set<String> signSubsidiaryCustcatgCodeSet = new HashSet<>();

    @ApiModelProperty(value = "权限链路映射,如：BG-国内海外-地区部")
    private Map<String, Map<String, Set<String>>> locationMap = new HashMap<>();

}