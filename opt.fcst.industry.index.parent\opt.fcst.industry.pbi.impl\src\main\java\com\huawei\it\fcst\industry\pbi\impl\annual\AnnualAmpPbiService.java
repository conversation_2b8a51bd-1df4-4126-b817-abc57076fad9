/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.annual;

import com.huawei.it.fcst.constant.Constant;
import com.huawei.it.fcst.enums.ResultCodeEnum;
import com.huawei.it.fcst.export.IExcelTemplateBeanManager;
import com.huawei.it.fcst.export.IExportProcessorService;
import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.industry.pbi.dao.IAnnualAmpPbiDao;
import com.huawei.it.fcst.industry.pbi.dao.IAnnualCustomDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstVersionInfoDao;
import com.huawei.it.fcst.industry.pbi.dao.IctMonthCostIdxDao;
import com.huawei.it.fcst.industry.pbi.enums.GroupLevelEnum;
import com.huawei.it.fcst.industry.pbi.enums.IndustryConstEnum;
import com.huawei.it.fcst.industry.pbi.impl.common.DropDownService;
import com.huawei.it.fcst.industry.pbi.impl.common.IctCommonService;
import com.huawei.it.fcst.industry.pbi.impl.common.IctExecutorConfig;
import com.huawei.it.fcst.industry.pbi.impl.template.AnnualTemplateEnum;
import com.huawei.it.fcst.industry.pbi.service.annual.IAnnualAmpPbiService;
import com.huawei.it.fcst.industry.pbi.utils.FcstIndustryUtil;
import com.huawei.it.fcst.industry.pbi.vo.annual.AnnualAnalysisVO;
import com.huawei.it.fcst.industry.pbi.vo.annual.CommonAnnualVO;
import com.huawei.it.fcst.industry.pbi.vo.annual.DmFocAnnualAmpVO;
import com.huawei.it.fcst.industry.pbi.vo.common.CommonBaseVO;
import com.huawei.it.fcst.industry.pbi.vo.version.DmFcstVersionInfoDTO;
import com.huawei.it.fcst.util.PinyinUtil;
import com.huawei.it.fcst.util.UserInfoUtils;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.core.util.CollectionUtil;
import com.huawei.it.jalor5.security.RoleVO;
import com.huawei.it.jalor5.security.UserVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Inject;
import javax.inject.Named;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.Collator;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * AnnualAmpService Class
 *
 * <AUTHOR>
 * @since 2023/3/13
 */
@Named("annualAmpPbiPbiService")
@JalorResource(code = "annualAmpPbiPbiService", desc = "new ICT-年度分析页面")
public class AnnualAmpPbiService implements IAnnualAmpPbiService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AnnualAmpPbiService.class);

    @Autowired
    private IAnnualAmpPbiDao annualAmpPbiDao;

    @Autowired
    private IAnnualCustomDao annualCustomDao;

    @Inject
    private IctMonthCostIdxDao monthCostIdxDao;

    @Autowired
    private IDmFcstVersionInfoDao dmFcstVersionInfoDao;

    @Autowired
    private IExportProcessorService iExportProcessorService;

    @Autowired
    private IctCommonService ictCommonService;

    @Autowired
    private IctExecutorConfig ictExecutorConfig;

    @Autowired
    private DropDownService dropDownService;

    @JalorOperation(code = "allIndustryPbiCost", desc = "查询当前层级pbi成本涨跌幅")
    @Override
    public ResultDataVO allIndustryPbiCost(AnnualAnalysisVO annualAnalysisVO) throws CommonApplicationException {
        //  获取用户权限
        dropDownService.setPermissionParameter(annualAnalysisVO);
        if (!annualAnalysisVO.getIsMultipleSelect()) {
            // 计算最近的三年
            List<String> threeYears = getYearListByVerison(annualAnalysisVO.getGranularityType(), annualAnalysisVO.getVersionId());
            annualAnalysisVO.setYearList(threeYears);
        }
        FcstIndustryUtil.setRegionCode(annualAnalysisVO);
        setCommonTablePreFix(annualAnalysisVO);
        // 设置版本ID
        setVersionId(annualAnalysisVO);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        getCurrentAmpList(annualAnalysisVO, dmFocAnnualAmpVOList);
        // 设置无效的涨跌幅提示语
        setNoEffectiveAmp(dmFocAnnualAmpVOList, annualAnalysisVO, "data");
        return ResultDataVO.success(dmFocAnnualAmpVOList);
    }

    private void setVersionId(AnnualAnalysisVO annualAnalysisVO) {
        // 如果前端有传versionId就用前端传的，否则使用最新的versionId
        if (annualAnalysisVO.getVersionId() == null) {
            // 获取最新的version_id
            annualAnalysisVO.setVersionId(dmFcstVersionInfoDao.findVersionIdByDataType("ANNUAL").getVersionId());
        }
    }

    @JalorOperation(code = "multiIndustryPbiCostChart", desc = "查询多子项成本涨跌幅")
    @Override
    public ResultDataVO multiIndustryPbiCostChart(AnnualAnalysisVO annualAnalysisVO) throws CommonApplicationException {
        //  获取用户权限
        dropDownService.setPermissionParameter(annualAnalysisVO);
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(annualAnalysisVO.getPageSize());
        pageVO.setCurPage(annualAnalysisVO.getPageIndex());
        // 设置参数
        setMultiSearchParamsVO(annualAnalysisVO);
        // 采购层级排序多选时先要排序父层级
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = new ArrayList<>();
        String combTablePreFix = getCombTablePreFix(annualAnalysisVO);
        // 虚化的表取值
        annualAnalysisVO.setCombTablePreFix(combTablePreFix);
        if (annualAnalysisVO.getIsNeedBlur()) {
            // 走虚化且是最细力度的时候，需要取正常结果表数据
            if (CommonConstant.MIN_GROUP_LEVEL.contains(annualAnalysisVO.getParentLevel())) {
                dmFocAnnualAmpVOResult.addAll(multiChildMinLevelNotContainsComb(annualAnalysisVO, pageVO));
            } else {
                dmFocAnnualAmpVOResult.addAll(multiChildContainsComb(annualAnalysisVO, pageVO));
            }
        }
        if (!annualAnalysisVO.getIsContainComb()) {
            if (!CommonConstant.MIN_GROUP_LEVEL.contains(annualAnalysisVO.getParentLevel()) && CollectionUtils.isNotEmpty(annualAnalysisVO.getParentCodeList())) {
                dmFocAnnualAmpVOResult.addAll(multiChildNotContainsComb(annualAnalysisVO, pageVO));
            }
        } else {
            // 只选汇总组合
            dmFocAnnualAmpVOResult.addAll(multiChildContainsSummaryComb(annualAnalysisVO, pageVO));
        }
        // 多选时分页
        if (annualAnalysisVO.getIsMultipleSelect() && !CommonConstant.MIN_GROUP_LEVEL.contains(annualAnalysisVO.getParentLevel())) {
            sortByWeight(dmFocAnnualAmpVOResult);
        }
        // 设置无效的涨跌幅提示语
        setNoEffectiveAmp(dmFocAnnualAmpVOResult, annualAnalysisVO, "data");
        // 如果不需要虚化，且nextgroupLevel是量纲层级或者spart层级，则需要屏蔽权重
        if ((!annualAnalysisVO.getIsNeedBlur() && CommonConstant.ENCRYPT_GROUP_LEVEL.stream().anyMatch(level -> level.equals(annualAnalysisVO.getGroupLevel()))) || annualAnalysisVO.getIsNeedBlur()) {
            dmFocAnnualAmpVOResult.stream().forEach(result -> result.setWeightRate(null));
        }
        Map result = new LinkedHashMap();
        result.put("result", dmFocAnnualAmpVOResult);
        result.put("pageVo", pageVO);
        return ResultDataVO.success(result);
    }

    @JalorOperation(code = "industryCostList", desc = "查询多子项产业成本一览表")
    @Override
    public ResultDataVO industryCostList(AnnualAnalysisVO annualAnalysisVO) throws CommonApplicationException {
        //  获取用户权限
        dropDownService.setPermissionParameter(annualAnalysisVO);
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(annualAnalysisVO.getPageSize());
        pageVO.setCurPage(annualAnalysisVO.getPageIndex());
        setIndustrySearchParamVO(annualAnalysisVO);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpOrderByWeight = new ArrayList<>();
        // 包含汇总组合的查询
        String combTablePreFix = getCombTablePreFix(annualAnalysisVO);
        // 虚化的表取值
        annualAnalysisVO.setCombTablePreFix(combTablePreFix);
        if (annualAnalysisVO.getIsNeedBlur()) {
            // 走虚化且是最细力度的时候，需要取正常结果表数据
            if (CommonConstant.MIN_GROUP_LEVEL.stream().anyMatch(level -> level.equals(annualAnalysisVO.getParentLevel()))) {
                dmFocAnnualAmpOrderByWeight.addAll(overviewListMinLevelNotContainsCustom(annualAnalysisVO));
            } else {
                dmFocAnnualAmpOrderByWeight.addAll(overviewListContainsCustom(annualAnalysisVO));
            }
        }
        if (!annualAnalysisVO.getIsContainComb()) {
            if (!CommonConstant.MIN_GROUP_LEVEL.contains(annualAnalysisVO.getParentLevel()) && CollectionUtils.isNotEmpty(annualAnalysisVO.getParentCodeList())) {
                dmFocAnnualAmpOrderByWeight.addAll(overviewListNotContainsCustom(annualAnalysisVO));
            }
        } else {
            // 只有汇总组合
            List<DmFocAnnualAmpVO> groupCodeOrderList = annualCustomDao.findSummaryCodeOrderByWeight(annualAnalysisVO);

            limitSummaryGroupCodePage(groupCodeOrderList, pageVO, annualAnalysisVO);

            dmFocAnnualAmpOrderByWeight.addAll(annualCustomDao.industrySummaryCombList(annualAnalysisVO));
            annualAnalysisVO.setGroupLevel(GroupLevelEnum.SPART.getValue());
        }
        // 按照权重*涨跌大小排序；各层级权重排序;获取权重*涨跌最大值
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = dealGroupLevelWeightAndAmp(dmFocAnnualAmpOrderByWeight, annualAnalysisVO, true, pageVO);
        // 设置无效的涨跌幅提示语
        setNoEffectiveAmp(dmFocAnnualAmpVOResult, annualAnalysisVO, "data");
        // 排序和分页
        List<DmFocAnnualAmpVO> annualAmpAndWeightList = orderColumnAndLimitPage(dmFocAnnualAmpVOResult, annualAnalysisVO, pageVO);
        Map result = new LinkedHashMap();
        // 不走虚化且是量纲维度或spart，或虚化，需要加密
        if ((!annualAnalysisVO.getIsNeedBlur() && CommonConstant.ENCRYPT_GROUP_LEVEL.stream().anyMatch(level -> level.equals(annualAnalysisVO.getGroupLevel()))) || annualAnalysisVO.getIsNeedBlur()) {
            setWeightAnnualAmpPercent(annualAnalysisVO, annualAmpAndWeightList);
        } else {
            result.put("maxValue", annualAnalysisVO.getMaxValue() + "%");
        }
        result.put("result", annualAmpAndWeightList);
        result.put("pageVo", pageVO);
        return ResultDataVO.success(result);
    }

    @Override
    @JalorOperation(code = "distributePbiCostChart", desc = "查询当前层级pbi成本分布图")
    public ResultDataVO distributePbiCostChart(AnnualAnalysisVO annualAnalysisVO) throws CommonApplicationException {
        //  获取用户权限
        dropDownService.setPermissionParameter(annualAnalysisVO);
        if (!annualAnalysisVO.getIsMultipleSelect()) {
            // 计算最近的三年
            List<String> threeYears = getYearListByVerison(annualAnalysisVO.getGranularityType(), annualAnalysisVO.getVersionId());
            annualAnalysisVO.setYearList(threeYears);
        }
        FcstIndustryUtil.setRegionCode(annualAnalysisVO);
        setCommonTablePreFix(annualAnalysisVO);
        List<DmFocAnnualAmpVO> dmFocDistributeCostList = new ArrayList<>();
        // 设置版本ID
        setVersionId(annualAnalysisVO);
        if (annualAnalysisVO.getIsNeedBlur()) {
            // 虚化
            dmFocDistributeCostList.addAll(annualCustomDao.distributeCustomChart(annualAnalysisVO));
        }
        if (CollectionUtils.isNotEmpty(annualAnalysisVO.getGroupCodeList())) {
            dmFocDistributeCostList.addAll(annualAmpPbiDao.distributePbiCostChart(annualAnalysisVO));
        }
        if (annualAnalysisVO.getIsContainComb()) {
            dmFocDistributeCostList.addAll(annualCustomDao.distributeSummaryCustomChart(annualAnalysisVO));
        }
        return ResultDataVO.success(dmFocDistributeCostList);
    }

    @Override
    @JalorOperation(code = "getComparePbiAmpCost", desc = "查询对比分析-当前层级涨跌图")
    public ResultDataVO getComparePbiAmpCost(List<AnnualAnalysisVO> annualAnalysisVOList) throws InterruptedException {
        LOGGER.info(">>>Begin annual::getComparePbiAmpCost");
        IRequestContext requestContext = RequestContext.getCurrent();
        if (!CollectionUtil.isNullOrEmpty(annualAnalysisVOList) && annualAnalysisVOList.size() > Constant.IntegerEnum.MAX_SIZE.getValue()) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR, "操作的数据过多！");
        }
        List<DmFocAnnualAmpVO> annualAmpVOList = new ArrayList();
        Executor executorPool = ictExecutorConfig.ictAsyncServiceExecutor();
        CountDownLatch countDownLatch = new CountDownLatch(annualAnalysisVOList.size());
        for (AnnualAnalysisVO annualAnalysis : annualAnalysisVOList) {
            //  获取用户权限
            dropDownService.setPermissionParameter(annualAnalysis);
            Runnable runnable = () -> {
                RequestContextManager.setCurrent(requestContext);
                try {
                    if (CommonConstant.PROD_GROUP_LEVEL.contains(annualAnalysis.getGroupLevel())) {
                        annualAnalysis.setViewFlag(IndustryConstEnum.VIEW_FLAG.PROD_SPART.getValue());
                    }
                    List<DmFocAnnualAmpVO> dmFocAnnualAmpList = getCompareCurrentAmpCost(annualAnalysis);
                    annualAmpVOList.addAll(dmFocAnnualAmpList);
                } catch (Exception exception) {
                    LOGGER.error("error getComparePbiAmpCost :{} ", exception.getLocalizedMessage());
                } finally {
                    countDownLatch.countDown();
                    RequestContextManager.removeCurrent();
                    LOGGER.info("剩余线程个数：{},当前线程名称：{}", countDownLatch.getCount(), Thread.currentThread().getName());
                }
            };
            executorPool.execute(runnable);
        }
        countDownLatch.await();
        return ResultDataVO.success(annualAmpVOList);
    }

    @Override
    @JalorOperation(code = "exportAnnualDetail", desc = "年度分析-数据下载")
    @Audit(module = "annualAmpPbiService-exportAnnualDetail", operation = "exportAnnualDetail", message = "年度分析-数据下载")
    public ResultDataVO exportAnnualDetail(HttpServletResponse response, AnnualAnalysisVO annualAnalysisVO) throws ApplicationException {
        //  获取用户权限
        dropDownService.setPermissionParameter(annualAnalysisVO);
        // 计算最近的三年
        List<String> threeYears = getYearListByVerison(annualAnalysisVO.getGranularityType(), annualAnalysisVO.getVersionId());
        annualAnalysisVO.setYearList(threeYears);
        // 设置版本ID
        setVersionId(annualAnalysisVO);
        setCommonTablePreFix(annualAnalysisVO);
        // 设置标题
        ictCommonService.setExcelDisplayName(annualAnalysisVO);
        if ("Y".equals(annualAnalysisVO.getMainFlag())) {
            // 编码属性
            annualAnalysisVO.setCodeAttributesCnName("编码属性：" + annualAnalysisVO.getCodeAttributes());
        } else {
            // 不选spart范围，或spart范围选择N
            annualAnalysisVO.setCodeAttributesCnName("");
        }
        FcstIndustryUtil.setRegionCode(annualAnalysisVO);
        // 设置筛选字段的中文名
        getColumnCnName(annualAnalysisVO);
        // 实际数截止月
        String mixActualMonth = dmFcstVersionInfoDao.findAnnualActualMonth(annualAnalysisVO);
        annualAnalysisVO.setActualMonth(mixActualMonth);
        if (annualAnalysisVO.getIsContainComb()) {
            annualAnalysisVO.setNextGroupName(CommonConstant.SPART_NAME);
            annualAnalysisVO.setOrderCnName("排序");
        } else {
            if (!CommonConstant.MIN_GROUP_LEVEL.contains(annualAnalysisVO.getGroupLevel())) {
                Map map = FcstIndustryUtil.getNextGroupLevel(annualAnalysisVO);
                String nextGroupLevel = String.valueOf(map.get("nextGroupLevel"));
                String nextGroupName = String.valueOf(map.get("nextGroupName"));
                annualAnalysisVO.setNextGroupName(nextGroupName);
                annualAnalysisVO.setNextGroupLevel(nextGroupLevel);
            }
        }
        String name = GroupLevelEnum.getInstance(annualAnalysisVO.getGroupLevel()).getName();
        annualAnalysisVO.setName(name);
        // 获取模板
        IExcelTemplateBeanManager templateBeanManager = getExcelTemplate(annualAnalysisVO);

        Map<String, Object> parameters = new HashMap<>();
        String module = "成本指数-ICT-年度分析";
        parameters.put("exportModuleName", module);
        parameters.put("exportFileName", annualAnalysisVO.getFileName());
        iExportProcessorService.fillEasyExcelExport(response, templateBeanManager, annualAnalysisVO, parameters);
        return ResultDataVO.success();
    }

    public void getCurrentAmpList(AnnualAnalysisVO annualAnalysisVO, List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList) throws CommonApplicationException {
        // 需要虚化
        if (annualAnalysisVO.getIsNeedBlur()) {
            String combTablePreFix = getCombTablePreFix(annualAnalysisVO);
            // 虚化的表取值
            annualAnalysisVO.setCombTablePreFix(combTablePreFix);
            dmFocAnnualAmpVOList.addAll(annualCustomDao.allIndustryCustomCost(annualAnalysisVO));
        }
        // 正常项
        if (CollectionUtils.isNotEmpty(annualAnalysisVO.getGroupCodeList())) {
            dmFocAnnualAmpVOList.addAll(annualAmpPbiDao.allIndustryNormalCost(annualAnalysisVO));
        }
        // 汇总组合
        if (annualAnalysisVO.getIsContainComb()) {
            dmFocAnnualAmpVOList.addAll(annualCustomDao.allIndustrySummaryCombCost(annualAnalysisVO));
        }
    }

    public List<DmFocAnnualAmpVO> getCompareCurrentAmpCost(AnnualAnalysisVO annualAnalysisVO) throws CommonApplicationException {
        FcstIndustryUtil.setRegionCode(annualAnalysisVO);
        setCommonTablePreFix(annualAnalysisVO);
        // 设置版本ID
        setVersionId(annualAnalysisVO);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();

        getCurrentAmpList(annualAnalysisVO, dmFocAnnualAmpVOList);
        // 设置无效的涨跌幅提示语
        setNoEffectiveAmp(dmFocAnnualAmpVOList, annualAnalysisVO, "data");
        return dmFocAnnualAmpVOList;
    }

    private void getColumnCnName(AnnualAnalysisVO annualAnalysisVO) {
        String softwareMarkCnName = "";
        if ("PSP".equals(annualAnalysisVO.getCostType()) && annualAnalysisVO.getIsHasSoftWare()) {
            softwareMarkCnName = IndustryConstEnum.getSoftwareMark(annualAnalysisVO.getSoftwareMark()).getDesc();
        }
        annualAnalysisVO.setSoftwareMarkCnName(softwareMarkCnName);
        String ytdFlagCnName = IndustryConstEnum.getYtdFlag(annualAnalysisVO.getYtdFlag()).getDesc();
        annualAnalysisVO.setYtdFlagCnName(ytdFlagCnName);
    }

    private IExcelTemplateBeanManager getExcelTemplate(AnnualAnalysisVO annualAnalysisVO) throws CommonApplicationException {
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        IExcelTemplateBeanManager templateBeanManager;
        if (CommonConstant.SPECIAL_ROLES.contains(currentRole.getRoleName())) {
            if (CollectionUtils.isNotEmpty(annualAnalysisVO.getLv4ProdRdTeamCnNameList()) && GroupLevelEnum.LV4.getValue().equals(annualAnalysisVO.getGroupLevel())) {
                templateBeanManager = AnnualTemplateEnum.getByCode("06", "");
            } else {
                templateBeanManager = getiExcelTemplateBeanManager(annualAnalysisVO);
            }
        } else {
            templateBeanManager = getiExcelTemplateBeanManager(annualAnalysisVO);
        }
        return templateBeanManager;
    }

    @NotNull
    private IExcelTemplateBeanManager getiExcelTemplateBeanManager(AnnualAnalysisVO annualAnalysisVO) throws CommonApplicationException {
        IExcelTemplateBeanManager templateBeanManager;
        if (annualAnalysisVO.getIsContainComb()) {
            // 汇总组合场景
            templateBeanManager = getCombTemplate(annualAnalysisVO);
        } else if (!annualAnalysisVO.getIsNeedBlur()) {
            // 非虚化场景
            if (CommonConstant.MIN_GROUP_LEVEL.stream().anyMatch(level -> level.equals(annualAnalysisVO.getGroupLevel()))) {
                templateBeanManager = AnnualTemplateEnum.getByCode("02", "");
            } else if (CommonConstant.ENCRYPT_GROUP_LEVEL.stream().anyMatch(level -> level.equals(annualAnalysisVO.getGroupLevel()))) {
                // 需要加密权重
                templateBeanManager = getMinLevelExcelTemplate(annualAnalysisVO);
            } else {
                templateBeanManager = getTemplate(annualAnalysisVO);
            }
            // 非虚化场景时在L3.5层级下的涨跌幅一览表中添加SPART对应的量纲名称与量纲子类名称
            // 一览表排列顺序为：SPART  量纲子类  量纲  权重排序  涨跌幅  权重×涨跌幅
            if (IndustryConstEnum.VIEW_FLAG.PROD_SPART.getValue().equals(annualAnalysisVO.getViewFlag())
                    && GroupLevelEnum.LV4.getValue().equals(annualAnalysisVO.getGroupLevel())) {
                templateBeanManager = annualAnalysisVO.getIsMultipleSelect()
                        ? AnnualTemplateEnum.getByCode("08", "")
                        : AnnualTemplateEnum.getByCode("07", "");
            }
        } else {
            // 虚化场景
            if (CommonConstant.MIN_GROUP_LEVEL.stream().anyMatch(level -> level.equals(annualAnalysisVO.getGroupLevel()))) {
                templateBeanManager = getMinLevelExcelTemplate(annualAnalysisVO);
            } else {
                templateBeanManager = getTemplate(annualAnalysisVO);
            }
        }
        return templateBeanManager;
    }

    @NotNull
    private IExcelTemplateBeanManager getMinLevelExcelTemplate(AnnualAnalysisVO annualAnalysisVO) throws CommonApplicationException {
        IExcelTemplateBeanManager templateBeanManager;
        if (annualAnalysisVO.getIsMultipleSelect()) {
            // 需要加密权重
            templateBeanManager = AnnualTemplateEnum.getByCode("04", "");
        } else {
            templateBeanManager = AnnualTemplateEnum.getByCode("03", "");
        }
        return templateBeanManager;
    }

    private IExcelTemplateBeanManager getTemplate(AnnualAnalysisVO annualAnalysisVO) throws CommonApplicationException {
        if (annualAnalysisVO.getIsMultipleSelect()) {
            return AnnualTemplateEnum.getByCode("05", "");
        } else {
            return AnnualTemplateEnum.getByCode("01", "");
        }
    }

    private IExcelTemplateBeanManager getCombTemplate(AnnualAnalysisVO annualAnalysisVO) throws CommonApplicationException {
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        if (annualAnalysisVO.getIsMultipleSelect() && CollectionUtils.isNotEmpty(annualAnalysisVO.getGroupCodeList())) {
            return AnnualTemplateEnum.getByCode("06", "");
        } else if (annualAnalysisVO.getIsMultipleSelect() && CollectionUtils.isEmpty(annualAnalysisVO.getGroupCodeList())) {
            if (CommonConstant.SPECIAL_ROLES.contains(currentRole.getRoleName())) {
                return AnnualTemplateEnum.getByCode("06", "");
            } else {
                return AnnualTemplateEnum.getByCode("08", "");
            }
        } else {
            if (CommonConstant.SPECIAL_ROLES.contains(currentRole.getRoleName())) {
                return AnnualTemplateEnum.getByCode("06", "");
            } else {
                return AnnualTemplateEnum.getByCode("07", "");
            }
        }
    }

    public List<DmFocAnnualAmpVO> overviewListNotContainsCustom(AnnualAnalysisVO annualAnalysisVO) {
        // 默认按照权重大小排序
        return annualAmpPbiDao.industryPbiCostList(annualAnalysisVO);
    }

    public List<DmFocAnnualAmpVO> overviewListMinLevelNotContainsCustom(AnnualAnalysisVO annualAnalysisVO) {

        return annualAmpPbiDao.industryMinLevelCostList(annualAnalysisVO);
    }

    private List<DmFocAnnualAmpVO> overviewListContainsCustom(AnnualAnalysisVO annualAnalysisVO) {
        return annualCustomDao.industryCombCustomExcel(annualAnalysisVO);
    }

    public List<DmFocAnnualAmpVO> dealGroupLevelWeightAndAmp(List<DmFocAnnualAmpVO> annualAmpAndWeightList, AnnualAnalysisVO annualAnalysisVO, boolean flag, PageVO pageVO) {
        sortByWeight(annualAmpAndWeightList);
        if ((!annualAnalysisVO.getIsNeedBlur() && CommonConstant.ENCRYPT_GROUP_LEVEL.stream().anyMatch(level -> level.equals(annualAnalysisVO.getGroupLevel()))) || annualAnalysisVO.getIsNeedBlur()) {
            dealEncryptLevelweightData(annualAmpAndWeightList, flag, annualAnalysisVO, pageVO);
            // 重新使用权重编号排序list
            Collections.sort(annualAmpAndWeightList, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnualAmp1, DmFocAnnualAmpVO dmFocAnnualAmp2) {
                    String weightRate1 = dmFocAnnualAmp1.getWeightRate();
                    String weightRate2 = dmFocAnnualAmp2.getWeightRate();
                    return compareWeightColumn(weightRate1, weightRate2);
                }
            });
        }
        // 获取权重*涨跌最大值 为前端提供
        if (flag && (!annualAnalysisVO.getIsNeedBlur() && !CommonConstant.ENCRYPT_GROUP_LEVEL.stream().anyMatch(level -> level.equals(annualAnalysisVO.getGroupLevel())))) {
            getMaxWeightAnnualAmp(annualAmpAndWeightList, annualAnalysisVO);
        }
        // 拼接%，保留一位小数
        concatPercent(annualAmpAndWeightList, annualAnalysisVO, flag);
        return annualAmpAndWeightList;
    }

    private void concatPercent(List<DmFocAnnualAmpVO> annualAmpAndWeightList, AnnualAnalysisVO annualAnalysisVO, boolean flag) {
        annualAmpAndWeightList.stream().forEach(annual -> {
            String annualAmp = annual.getAnnualAmp();
            String weightRate = annual.getWeightRate();
            String weightAnnualAmpPercent = annual.getWeightAnnualAmpPercent();
            if (StringUtils.isNotBlank(annualAmp) && !annualAmp.equals("0*")) {
                annual.setAnnualAmp(annualAmp + "%");
            }
            if (!annualAnalysisVO.getIsNeedBlur() && !CommonConstant.ENCRYPT_GROUP_LEVEL.stream().anyMatch(level -> level.equals(annualAnalysisVO.getGroupLevel()))) {
                if (StringUtils.isNotBlank(weightRate) && !weightRate.equals("0*")) {
                    if (flag) {
                        weightRate = new BigDecimal(weightRate).setScale(1, BigDecimal.ROUND_HALF_UP).toString();
                    }
                    annual.setWeightRate(weightRate + "%");
                }
                if (StringUtils.isNotBlank(weightAnnualAmpPercent) && !weightAnnualAmpPercent.equals("0*")) {
                    String newWeightAnnualAmpPercent = new BigDecimal(weightAnnualAmpPercent).setScale(1, BigDecimal.ROUND_HALF_UP).toString();
                    annual.setWeightAnnualAmpPercent(newWeightAnnualAmpPercent + "%");
                }
            }
        });
    }

    private void dealEncryptLevelweightData(List<DmFocAnnualAmpVO> annualAmpWeightList, boolean flag, AnnualAnalysisVO annualAnalysisVO, PageVO pageVO) {
        List<Integer> numList = new ArrayList<>();
        if ((annualAnalysisVO.getIsNeedBlur() || annualAnalysisVO.getIsContainComb()) && pageVO != null) {
            int curTotal = (pageVO.getCurPage() - 1) * pageVO.getPageSize() + annualAmpWeightList.size();
            numList = Stream.iterate((pageVO.getCurPage() - 1) * pageVO.getPageSize() + 1, item -> item + 1).limit(curTotal).collect(Collectors.toList());
        } else {
            numList = Stream.iterate(1, item -> item + 1).limit(annualAmpWeightList.size()).collect(Collectors.toList());
        }
        // 权重排序
        for (int i = 0; i < annualAmpWeightList.size(); i++) {
            annualAmpWeightList.get(i).setWeightRate(numList.get(i).toString());
        }
        // 权重*涨跌 排序
        sortByWeightAndAmp(annualAmpWeightList);
        // 权重*涨跌 数据设置成序号
        encryptionWeightValue(annualAmpWeightList, flag, numList, annualAnalysisVO);
    }

    private void encryptionWeightValue(List<DmFocAnnualAmpVO> annualAmpWeightList, boolean flag, List<Integer> numList, AnnualAnalysisVO annualAnalysisVO) {
        // 获取权重*涨跌最大值
        getMaxWeightAnnualAmp(annualAmpWeightList, annualAnalysisVO);
        for (int i = 0; i < annualAmpWeightList.size(); i++) {
            if (flag) {
                annualAmpWeightList.get(i).setWeightAnnualAmpPercentOrder(numList.get(i).toString());
                String encryptWeightAnnualAmp = annualAmpWeightList.get(i).getWeightAnnualAmpPercent();
                if (StringUtils.isNotBlank(encryptWeightAnnualAmp) && !"0*".equals(encryptWeightAnnualAmp)) {
                    encryptWeightAnnualAmp = new BigDecimal(encryptWeightAnnualAmp).setScale(1, BigDecimal.ROUND_HALF_UP).toString();
                }
                annualAmpWeightList.get(i).setWeightAnnualAmpPercent(encryptWeightAnnualAmp);
            } else {
                annualAmpWeightList.get(i).setWeightAnnualAmpPercent(numList.get(i).toString());
            }
        }
    }

    private void setWeightAnnualAmpPercent(AnnualAnalysisVO analysisVO, List<DmFocAnnualAmpVO> annualAmpAndWeightList) {
        for (DmFocAnnualAmpVO dmFocAnnualVO : annualAmpAndWeightList) {
            if (StringUtils.isNotBlank(dmFocAnnualVO.getWeightAnnualAmpPercent()) && StringUtils.isNotBlank(analysisVO.getMaxValue())) {
                if ("0".equals(analysisVO.getMaxValue()) || "0.0".equals(analysisVO.getMaxValue())) {
                    dmFocAnnualVO.setWeightAnnualAmpPercent("0*");
                    continue;
                }
                if ("0*".equals(dmFocAnnualVO.getWeightAnnualAmpPercent())) {
                    continue;
                }
                double double1 = Double.parseDouble(dmFocAnnualVO.getWeightAnnualAmpPercent());
                double double2 = Double.parseDouble(analysisVO.getMaxValue());
                double weightPercent = double1 / double2;
                double targetValue = weightPercent * 0.9;
                if (Math.abs(targetValue) * 1000 > 0 && Math.abs(targetValue) * 1000 <= 2) {
                    targetValue = targetValue > 0 ? 0.002 : -0.002;
                }
                dmFocAnnualVO.setWeightAnnualAmpPercent(Double.toString(targetValue * 60));
            }
        }
    }

    private List<DmFocAnnualAmpVO> orderColumnAndLimitPage(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult, AnnualAnalysisVO annualVO, PageVO pageVO) {
        String orderColumn = annualVO.getOrderColumn();
        if (StringUtils.isNotBlank(orderColumn)) {
            switch (orderColumn) {
                case "weightRate":
                    dmFocAnnualAmpVOPagedResult = sortByWeightRateBar(dmFocAnnualAmpVOPagedResult, annualVO);
                    break;
                case "groupCnName":
                    groupCnNameOrder(dmFocAnnualAmpVOPagedResult, annualVO);
                    break;
                case "groupCode":
                    groupCodeOrder(dmFocAnnualAmpVOPagedResult, annualVO);
                    break;
                case "annualAmp":
                    dmFocAnnualAmpVOPagedResult = getAnnualAmpPagedResult(dmFocAnnualAmpVOPagedResult, annualVO);
                    break;
                case "weightAnnualAmpPercent":
                case "weightAnnualAmpPercentOrder":
                    if ("asc".equals(annualVO.getOrderMethod())) {
                        dmFocAnnualAmpVOPagedResult = sortByWeightAnnualBar(dmFocAnnualAmpVOPagedResult, annualVO, true);
                    } else {
                        dmFocAnnualAmpVOPagedResult = sortByWeightAnnualBar(dmFocAnnualAmpVOPagedResult, annualVO, false);
                    }
                    break;
                default:
                    break;
            }
        } else {
            // 根据状态码区分-和有数据的记录后，默认权重大小排序
            dmFocAnnualAmpVOPagedResult = sortWeightBarAndRecordList(dmFocAnnualAmpVOPagedResult, annualVO);
        }

        if (annualVO.getIsNeedBlur() || !annualVO.getIsContainComb()) {
            // 分页
            int count = dmFocAnnualAmpVOPagedResult.size();
            dmFocAnnualAmpVOPagedResult = getDmFocLimitPage(dmFocAnnualAmpVOPagedResult, pageVO);
            pageVO.setTotalRows(count);
        }
        return dmFocAnnualAmpVOPagedResult;
    }

    @NotNull
    private List<DmFocAnnualAmpVO> getAnnualAmpPagedResult(List<DmFocAnnualAmpVO> dmFocAnnualAmpPagedResult, AnnualAnalysisVO annualAnalysisVO) {
        List<DmFocAnnualAmpVO> barAnnualAmpList = dmFocAnnualAmpPagedResult.stream().filter(result -> "0*".equals(result.getAnnualAmp())).collect(Collectors.toList());
        List<DmFocAnnualAmpVO> otherAmpList = dmFocAnnualAmpPagedResult.stream().filter(vo -> !barAnnualAmpList.contains(vo)).collect(Collectors.toList());

        annualAmpOrder(otherAmpList, annualAnalysisVO);

        List<DmFocAnnualAmpVO> newAmpPagedResult = new ArrayList<>();
        newAmpPagedResult.addAll(otherAmpList);
        newAmpPagedResult.addAll(barAnnualAmpList);
        return newAmpPagedResult;
    }

    private List<DmFocAnnualAmpVO> sortWeightBarAndRecordList(List<DmFocAnnualAmpVO> dmFocAnnualAmpPagedResult, AnnualAnalysisVO analysisVO) {
        if (!analysisVO.getIsNeedBlur() && !CommonConstant.ENCRYPT_GROUP_LEVEL.stream().anyMatch(level -> level.equals(analysisVO.getGroupLevel()))) {
            List<DmFocAnnualAmpVO> barAnnualAmpVOList = dmFocAnnualAmpPagedResult.stream().filter(result -> "0*".equals(result.getWeightRate())).collect(Collectors.toList());
            List<DmFocAnnualAmpVO> otherList = dmFocAnnualAmpPagedResult.stream().filter(vo -> !barAnnualAmpVOList.contains(vo)).collect(Collectors.toList());
            sortByWeight(otherList);
            List<DmFocAnnualAmpVO> newDmFocAnnualAmpPagedResult = new ArrayList<>();
            newDmFocAnnualAmpPagedResult.addAll(otherList);
            newDmFocAnnualAmpPagedResult.addAll(barAnnualAmpVOList);
            return newDmFocAnnualAmpPagedResult;
        }
        return dmFocAnnualAmpPagedResult;
    }

    private List<DmFocAnnualAmpVO> sortByWeightAnnualBar(List<DmFocAnnualAmpVO> dmFocAnnualAmpPagedResult, AnnualAnalysisVO analysisVO, boolean flag) {
        List<DmFocAnnualAmpVO> otherWeightList = dmFocAnnualAmpPagedResult;
        List<DmFocAnnualAmpVO> newBarWeightVOList = new ArrayList<>();
        if (!CommonConstant.ENCRYPT_GROUP_LEVEL.stream().anyMatch(level -> level.equals(analysisVO.getGroupLevel()))) {
            List<DmFocAnnualAmpVO> barAnnualAmpVOList = dmFocAnnualAmpPagedResult.stream().filter(result -> "0*".equals(result.getWeightAnnualAmpPercent())).collect(Collectors.toList());
            otherWeightList = dmFocAnnualAmpPagedResult.stream().filter(vo -> !barAnnualAmpVOList.contains(vo)).collect(Collectors.toList());
            newBarWeightVOList.addAll(barAnnualAmpVOList);
        }
        orderWeightAnnualAmpPercent(otherWeightList, analysisVO, flag);
        if (!CommonConstant.ENCRYPT_GROUP_LEVEL.stream().anyMatch(level -> level.equals(analysisVO.getGroupLevel()))) {
            List<DmFocAnnualAmpVO> newAnnualAmpVOPagedResult = new ArrayList<>();
            newAnnualAmpVOPagedResult.addAll(otherWeightList);
            newAnnualAmpVOPagedResult.addAll(newBarWeightVOList);
            return newAnnualAmpVOPagedResult;
        } else {
            return otherWeightList;
        }
    }

    private List<DmFocAnnualAmpVO> sortByWeightRateBar(List<DmFocAnnualAmpVO> dmFocAnnualAmpPagedResult, AnnualAnalysisVO analysisVO) {
        List<DmFocAnnualAmpVO> otherList = dmFocAnnualAmpPagedResult;
        List<DmFocAnnualAmpVO> newBarAnnualAmpVOList = new ArrayList<>();
        if (!CommonConstant.ENCRYPT_GROUP_LEVEL.stream().anyMatch(level -> level.equals(analysisVO.getGroupLevel()))) {
            List<DmFocAnnualAmpVO> barAnnualAmpVOList = dmFocAnnualAmpPagedResult.stream().filter(result -> "0*".equals(result.getWeightRate())).collect(Collectors.toList());
            otherList = dmFocAnnualAmpPagedResult.stream().filter(vo -> !barAnnualAmpVOList.contains(vo)).collect(Collectors.toList());
            newBarAnnualAmpVOList.addAll(barAnnualAmpVOList);
        }
        weightRateOrder(otherList, analysisVO);
        if (!CommonConstant.ENCRYPT_GROUP_LEVEL.stream().anyMatch(level -> level.equals(analysisVO.getGroupLevel()))) {
            List<DmFocAnnualAmpVO> newAnnualAmpVOPagedResult = new ArrayList<>();
            newAnnualAmpVOPagedResult.addAll(otherList);
            newAnnualAmpVOPagedResult.addAll(newBarAnnualAmpVOList);
            return newAnnualAmpVOPagedResult;
        } else {
            return otherList;
        }
    }

    private void weightRateOrder(List<DmFocAnnualAmpVO> dmFocAnnualAmpPagedResult, AnnualAnalysisVO annualAnalysisVO) {
        if ("asc".equals(annualAnalysisVO.getOrderMethod())) {
            Collections.sort(dmFocAnnualAmpPagedResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnual1, DmFocAnnualAmpVO dmFocAnnual2) {
                    String weightRate1 = dmFocAnnual1.getWeightRate();
                    String weightRate2 = dmFocAnnual2.getWeightRate();
                    return compareWeightColumn(weightRate1, weightRate2);
                }
            });
        } else {
            Collections.sort(dmFocAnnualAmpPagedResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnual1, DmFocAnnualAmpVO dmFocAnnual2) {
                    String weightRate1 = dmFocAnnual1.getWeightRate();
                    String weightRate2 = dmFocAnnual2.getWeightRate();
                    return compareWeightColumn(weightRate2, weightRate1);
                }
            });
        }
    }

    private void groupCnNameOrder(List<DmFocAnnualAmpVO> dmFocAnnualPagedResult, AnnualAnalysisVO annualAnalysisVO) {
        if ("asc".equals(annualAnalysisVO.getOrderMethod())) {
            Collections.sort(dmFocAnnualPagedResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnual1, DmFocAnnualAmpVO dmFocAnnual2) {
                    return sortByGroupCnName(dmFocAnnual1, dmFocAnnual2);
                }
            });
        } else {
            Collections.sort(dmFocAnnualPagedResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnual1, DmFocAnnualAmpVO dmFocAnnual2) {
                    return sortByGroupCnName(dmFocAnnual2, dmFocAnnual1);
                }
            });
        }
    }

    private void groupCodeOrder(List<DmFocAnnualAmpVO> dmFocAnnualPagResult, AnnualAnalysisVO annualAnalysisVO) {
        if ("asc".equals(annualAnalysisVO.getOrderMethod())) {
            Collections.sort(dmFocAnnualPagResult, Comparator.comparing(DmFocAnnualAmpVO::getGroupCode));
        } else {
            Collections.sort(dmFocAnnualPagResult, Comparator.comparing(DmFocAnnualAmpVO::getGroupCode).reversed());
        }
    }

    private void annualAmpOrder(List<DmFocAnnualAmpVO> dmFocAnnualPagedResult, AnnualAnalysisVO analysisVO) {
        if ("asc".equals(analysisVO.getOrderMethod())) {
            Collections.sort(dmFocAnnualPagedResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnual1, DmFocAnnualAmpVO dmFocAnnual2) {
                    String annualAmp1 = dmFocAnnual1.getAnnualAmp();
                    String annualAmp2 = dmFocAnnual2.getAnnualAmp();
                    return compareWeightColumn(annualAmp1, annualAmp2);
                }
            });
        } else {
            Collections.sort(dmFocAnnualPagedResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnual1, DmFocAnnualAmpVO dmFocAnnual2) {
                    String annualAmp1 = dmFocAnnual1.getAnnualAmp();
                    String annualAmp2 = dmFocAnnual2.getAnnualAmp();
                    return compareWeightColumn(annualAmp2, annualAmp1);
                }
            });
        }
    }

    private void getMaxWeightAnnualAmp(List<DmFocAnnualAmpVO> annualAmpAndWeightList, AnnualAnalysisVO annualAnalysisVO) {
        List<String> annualPercentList = new ArrayList<>();
        List<String> weightAnnualAmpPercentList = annualAmpAndWeightList.stream().map(DmFocAnnualAmpVO::getWeightAnnualAmpPercent).collect(Collectors.toList());
        Double weightPercent = 0.0D;
        for (String weightAnnualAmpPercent : weightAnnualAmpPercentList) {
            if (StringUtils.isNotBlank(weightAnnualAmpPercent)) {
                weightPercent = Math.abs(Double.parseDouble(weightAnnualAmpPercent));
            }
            annualPercentList.add(new BigDecimal(String.valueOf(weightPercent)).setScale(1, BigDecimal.ROUND_HALF_UP).toString());
        }
        if (CollectionUtils.isNotEmpty(annualPercentList)) {
            String maxWeightAnnual = Collections.max(annualPercentList);
            annualAnalysisVO.setMaxValue(maxWeightAnnual);
        }
    }

    private int compareWeightColumn(String weightRate1, String weightRate2) {
        CommonAnnualVO commonAnnualVO = transformStr(weightRate1, weightRate2);
        Double number1 = commonAnnualVO.getNum1();
        Double number2 = commonAnnualVO.getNum2();
        if (number1 != null && number2 != null && ObjectUtils.notEqual(number1, number2)) {
            return number1.compareTo(number2);
        }
        if (number1 == null && number2 == null) {
            return 0;
        }
        if (number1 == null) {
            return -1;
        }
        if (number2 == null) {
            return 1;
        }
        return 0;
    }

    private CommonAnnualVO transformStr(String str1, String str2) {
        CommonAnnualVO commonAnnualVO = new CommonAnnualVO();
        Double number1 = null;
        Double number2 = null;
        if (StringUtils.isNotBlank(str1) && !str1.equals("0*")) {
            str1 = subPercentStr(str1);
            number1 = Double.parseDouble(str1);
        }
        if (StringUtils.isNotBlank(str2) && !str2.equals("0*")) {
            str2 = subPercentStr(str2);
            number2 = Double.parseDouble(str2);
        }
        commonAnnualVO.setNum1(number1);
        commonAnnualVO.setNum2(number2);
        return commonAnnualVO;
    }

    private void sortByWeightAndAmp(List<DmFocAnnualAmpVO> annualAmpAndWeightList) {
        Collections.sort(annualAmpAndWeightList, new Comparator<DmFocAnnualAmpVO>() {
            @Override
            public int compare(DmFocAnnualAmpVO dmFocAnnual1, DmFocAnnualAmpVO dmFocAnnual2) {
                String weightAnnualAmpPercent1 = dmFocAnnual1.getWeightAnnualAmpPercent();
                String weightAnnualAmpPercent2 = dmFocAnnual2.getWeightAnnualAmpPercent();
                Double weightAmpNum1 = null;
                Double weightAmpNum2 = null;
                if (StringUtils.isNotBlank(weightAnnualAmpPercent1) && !weightAnnualAmpPercent1.equals("0*")) {
                    weightAnnualAmpPercent1 = subPercentStr(weightAnnualAmpPercent1);
                    weightAmpNum1 = Double.parseDouble(weightAnnualAmpPercent1);
                }
                if (StringUtils.isNotBlank(weightAnnualAmpPercent2) && !weightAnnualAmpPercent2.equals("0*")) {
                    weightAnnualAmpPercent2 = subPercentStr(weightAnnualAmpPercent2);
                    weightAmpNum2 = Double.parseDouble(weightAnnualAmpPercent2);
                }
                if (weightAmpNum1 == null && weightAmpNum2 == null) {
                    return 0;
                }
                if (weightAmpNum1 == null) {
                    return 1;
                }
                if (weightAmpNum2 == null) {
                    return -1;
                }
                if (weightAmpNum1 != null && weightAmpNum2 != null && ObjectUtils.notEqual(weightAmpNum1, weightAmpNum2)) {
                    return weightAmpNum2.compareTo(weightAmpNum1);
                }
                return 0;
            }
        });
    }

    private void setIndustrySearchParamVO(AnnualAnalysisVO annualAnalysisVO) throws CommonApplicationException {
        annualAnalysisVO.setParentCodeList(annualAnalysisVO.getGroupCodeList());
        annualAnalysisVO.setParentLevel(annualAnalysisVO.getGroupLevel());

        annualAnalysisVO.setParentLevel(annualAnalysisVO.getGroupLevel());
        if (!annualAnalysisVO.getIsNeedBlur() && !CommonConstant.MIN_GROUP_LEVEL.stream().anyMatch(level -> level.equals(annualAnalysisVO.getGroupLevel()))) {
            String nextGroupLevel = FcstIndustryUtil.getViewNextGroupLevel(annualAnalysisVO);
            annualAnalysisVO.setGroupLevel(nextGroupLevel);
        }
        // 设置重量级团队code为null
        resetProdRndTeamCode(annualAnalysisVO);
        if (!annualAnalysisVO.getIsMultipleSelect()) {
            // 计算最近的三年
            annualAnalysisVO.setYearList(getYearListByVerison(annualAnalysisVO.getGranularityType(), annualAnalysisVO.getVersionId()));
        }
        FcstIndustryUtil.setRegionCode(annualAnalysisVO);
        setCommonTablePreFix(annualAnalysisVO);
        // 设置版本ID
        setVersionId(annualAnalysisVO);
    }

    private List<DmFocAnnualAmpVO> multiChildNotContainsComb(AnnualAnalysisVO annualAnalysisVO, PageVO pageVO) {
        // 设置重量级团队
        resetProdRndTeamCode(annualAnalysisVO);

        List<DmFocAnnualAmpVO> groupCodeOrderList = annualAmpPbiDao.findGroupCodeOrderByWeight(annualAnalysisVO);
        // 权重排序
        sortByWeight(groupCodeOrderList);
        String groupCodeOrder = limitGroupCodePage(groupCodeOrderList, pageVO, "normal");
        annualAnalysisVO.setGroupCodeOrder(groupCodeOrder);
        return annualAmpPbiDao.multiIndustryPbiCostChart(annualAnalysisVO);
    }

    private List<DmFocAnnualAmpVO> multiChildMinLevelNotContainsComb(AnnualAnalysisVO annualAnalysisVO, PageVO pageVO) {
        // 设置重量级团队
        resetProdRndTeamCode(annualAnalysisVO);

        List<DmFocAnnualAmpVO> groupCodeOrderList = annualCustomDao.findCombCodeOrderMinLevelByWeight(annualAnalysisVO);
        // 权重排序
        sortByWeight(groupCodeOrderList);
        String groupCodeOrder = limitGroupCodePage(groupCodeOrderList, pageVO, "minLevel");
        annualAnalysisVO.setProdRndTeamCodeOrder(groupCodeOrder);
        annualAnalysisVO.setCondition("minLevel");
        return annualAmpPbiDao.multiIndustryMinLevelChart(annualAnalysisVO);
    }

    public void setGroupCnNameDimensionLevel(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList) {
        if (CollectionUtils.isNotEmpty(dmFocAnnualAmpVOList)) {
            dmFocAnnualAmpVOList.stream().forEach(annualAmpVO -> {
                if (CommonConstant.GROUP_LEVEL_DIMENSION.contains(annualAmpVO.getGroupLevel())) {
                    annualAmpVO.setGroupCnName(annualAmpVO.getGroupCode() + " " + annualAmpVO.getGroupCnName());
                }
            });
        }
    }

    public void setGroupCnNameMixLevel(List<DmFocAnnualAmpVO> dmFocAnnualList) {
        if (CollectionUtils.isNotEmpty(dmFocAnnualList)) {
            dmFocAnnualList.stream().forEach(annualAmpVO -> {
                if (GroupLevelEnum.SUB_DETAIL.getValue().equals(annualAmpVO.getGroupLevel())) {
                    annualAmpVO.setGroupCnName(annualAmpVO.getGroupCode() + " " + annualAmpVO.getGroupCnName());
                    annualAmpVO.setParentCnName(annualAmpVO.getProdRndTeamCnName());
                }
                if (GroupLevelEnum.SPART.getValue().equals(annualAmpVO.getGroupLevel())) {
                    annualAmpVO.setParentCnName(annualAmpVO.getProdRndTeamCnName());
                }
            });
        }
    }

    private List<DmFocAnnualAmpVO> multiChildContainsComb(AnnualAnalysisVO annualAnalysisVO, PageVO pageVO) {

        List<DmFocAnnualAmpVO> groupCodeOrderList = annualCustomDao.findCombCodeOrderByWeight(annualAnalysisVO);
        // 权重排序
        sortByWeight(groupCodeOrderList);
        String groupCodeOrder = limitGroupCodePage(groupCodeOrderList, pageVO, "normal");
        annualAnalysisVO.setGroupCodeOrder(groupCodeOrder);
        return annualCustomDao.multiIndustryCustomCombChart(annualAnalysisVO);
    }

    private List<DmFocAnnualAmpVO> multiChildContainsSummaryComb(AnnualAnalysisVO annualAnalysisVO, PageVO pageVO) {

        // 如果是单选或最细层级，可以进行分页
        List<DmFocAnnualAmpVO> groupCodeOrderList = annualCustomDao.findSummaryCodeOrderByWeight(annualAnalysisVO);
        limitSummaryGroupCodePage(groupCodeOrderList, pageVO, annualAnalysisVO);
        return annualCustomDao.multiCombSpartChart(annualAnalysisVO);
    }

    private String limitGroupCodePage(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult, PageVO pageVO, String condition) {
        List<DmFocAnnualAmpVO> annualAmpAndWeightList = getDmFocLimitPage(dmFocAnnualAmpVOPagedResult, pageVO);
        if ("minLevel".equals(condition)) {
            return annualAmpAndWeightList.stream().map(DmFocAnnualAmpVO::getProdRndTeamCode).collect(Collectors.joining(","));
        }
        return annualAmpAndWeightList.stream().map(DmFocAnnualAmpVO::getGroupCode).collect(Collectors.joining(","));
    }

    @NotNull
    private List<DmFocAnnualAmpVO> getDmFocLimitPage(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult, PageVO pageVO) {
        int count = dmFocAnnualAmpVOPagedResult.size();
        pageVO.setTotalRows(count);
        int curPage = pageVO.getCurPage() - 1;
        int pageSize = pageVO.getPageSize();
        List<DmFocAnnualAmpVO> annualAmpAndWeightList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dmFocAnnualAmpVOPagedResult) && count > 0) {
            int fromIndex = curPage * pageSize;
            int totalIndex = (curPage + 1) * pageSize;
            if (totalIndex > count) {
                totalIndex = count;
            }
            annualAmpAndWeightList = dmFocAnnualAmpVOPagedResult.subList(fromIndex, totalIndex);
        }
        return annualAmpAndWeightList;
    }

    private void limitSummaryGroupCodePage(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult, PageVO pageVO, AnnualAnalysisVO annualAnalysisVO) {
        List<DmFocAnnualAmpVO> annualAmpAndWeightList = getDmFocLimitPage(dmFocAnnualAmpVOPagedResult, pageVO);
        List<String> connectParentGroupCodeList = new ArrayList<>();
        String groupCodeOrder = annualAmpAndWeightList.stream().map(DmFocAnnualAmpVO::getGroupCode).collect(Collectors.joining(","));
        annualAnalysisVO.setGroupCodeOrder(groupCodeOrder);
        annualAmpAndWeightList.forEach(annualVO -> connectParentGroupCodeList.add(annualVO.getCustomId() + "#*#" + annualVO.getGroupCode() + "#*#" + annualVO.getParentCode()));
        annualAnalysisVO.setConnectParentGroupCodeList(connectParentGroupCodeList);
    }

    public void setParentCnNameDimensionLevel(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList) {
        if (CollectionUtils.isNotEmpty(dmFocAnnualAmpVOList)) {
            dmFocAnnualAmpVOList.stream().forEach(annualAmpVO -> {
                if (CommonConstant.GROUP_LEVEL_DIMENSION.contains(annualAmpVO.getParentLevel())) {
                    annualAmpVO.setParentCnName(annualAmpVO.getParentCode() + " " + annualAmpVO.getParentCnName());
                }
                if (CommonConstant.GROUP_LEVEL_DIMENSION.contains(annualAmpVO.getGroupLevel())) {
                    annualAmpVO.setGroupCnName(annualAmpVO.getGroupCode() + " " + annualAmpVO.getGroupCnName());
                }
            });
        }
    }

    private void setMultiSearchParamsVO(AnnualAnalysisVO annualAnalysisVO) throws CommonApplicationException {
        // 计算最近的三年
        List<String> threeYears = getYearListByVerison(annualAnalysisVO.getGranularityType(), annualAnalysisVO.getVersionId());
        // 获取当前年份YTD
        if (CollectionUtils.isNotEmpty(threeYears)) {
            annualAnalysisVO.setYear(threeYears.get(0));
        }
        if (!annualAnalysisVO.getIsMultipleSelect()) {
            annualAnalysisVO.setYearList(threeYears);
        }
        setCommonTablePreFix(annualAnalysisVO);
        annualAnalysisVO.setParentCodeList(annualAnalysisVO.getGroupCodeList());

        annualAnalysisVO.setParentLevel(annualAnalysisVO.getGroupLevel());
        String nextGroupLevel = FcstIndustryUtil.getViewNextGroupLevel(annualAnalysisVO);
        annualAnalysisVO.setGroupLevel(nextGroupLevel);

        FcstIndustryUtil.setRegionCode(annualAnalysisVO);
        // 设置版本ID
        setVersionId(annualAnalysisVO);
    }

    public void setCommonTablePreFix(CommonBaseVO commonBaseVO) throws CommonApplicationException {
        FcstIndustryUtil.checkTablePreFixParam(commonBaseVO);
        commonBaseVO.setTablePreFix(commonBaseVO.getCostType() + "_" + commonBaseVO.getGranularityType());
    }

    public void resetProdRndTeamCode(CommonBaseVO commonBaseVO) {
        // 如果parentlevel为LV0/LV1/LV2/LV3/L，也就是当parentLevel的下一层级还是重量级团队的时候，需要把prodRndTeamCodeList置为null
        boolean parentLevelFlag = CommonConstant.PROD_GROUP_LEVEL.stream().anyMatch(level -> level.equals(commonBaseVO.getParentLevel()));
        boolean currentLevelFlag = CommonConstant.PROD_GROUP_LEVEL.stream().anyMatch(level -> level.equals(commonBaseVO.getGroupLevel()));
        if (parentLevelFlag && currentLevelFlag) {
            commonBaseVO.setProdRndTeamCodeList(null);
        }
    }

    public String getCombTablePreFix(CommonBaseVO commonBaseVO) throws CommonApplicationException {
        FcstIndustryUtil.checkTablePreFixParam(commonBaseVO);
        return commonBaseVO.getCostType();
    }

    /**
     * 按照权重排序
     *
     * @param ampWeightList 参数
     */
    private void sortByWeight(List<DmFocAnnualAmpVO> ampWeightList) {
        Collections.sort(ampWeightList, new Comparator<DmFocAnnualAmpVO>() {
            @Override
            public int compare(DmFocAnnualAmpVO dmFocAnnualAmp1, DmFocAnnualAmpVO dmFocAnnualAmp2) {
                String weightRate1 = dmFocAnnualAmp1.getWeightRate();
                String weightRate2 = dmFocAnnualAmp2.getWeightRate();
                Double weightNumOne = null;
                Double weightNumTwo = null;
                if (StringUtils.isNotBlank(weightRate1) && !weightRate1.equals("%")) {
                    weightRate1 = subPercentStr(weightRate1);
                    weightNumOne = Double.parseDouble(weightRate1);
                }
                if (StringUtils.isNotBlank(weightRate2) && !weightRate2.equals("%")) {
                    weightRate2 = subPercentStr(weightRate2);
                    weightNumTwo = Double.parseDouble(weightRate2);
                }
                if (weightNumOne == null && weightNumTwo == null) {
                    return sortByGroupCnName(dmFocAnnualAmp1, dmFocAnnualAmp2);
                }
                if (weightNumOne == null) {
                    return 1;
                }
                if (weightNumTwo == null) {
                    return -1;
                }
                if (weightNumOne != null && weightNumTwo != null && ObjectUtils.notEqual(weightNumOne, weightNumTwo)) {
                    return weightNumTwo.compareTo(weightNumOne);
                }
                if (weightNumOne != null && weightNumTwo != null && Double.toString(weightNumOne).equals(Double.toString(weightNumTwo))) {
                    return sortByGroupCnName(dmFocAnnualAmp1, dmFocAnnualAmp2);
                }
                return 0;
            }
        });
    }

    private void orderWeightAnnualAmpPercent(List<DmFocAnnualAmpVO> dmFocAnnualAmpPagedResult, AnnualAnalysisVO annualAnalysisVO, boolean flag) {
        if ((!annualAnalysisVO.getIsNeedBlur() && CommonConstant.ENCRYPT_GROUP_LEVEL.stream().anyMatch(level -> level.equals(annualAnalysisVO.getGroupLevel()))) || annualAnalysisVO.getIsNeedBlur()) {
            Collections.sort(dmFocAnnualAmpPagedResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnualAmp1, DmFocAnnualAmpVO dmFocAnnualAmp2) {
                    String weightAmpPercent1 = dmFocAnnualAmp1.getWeightAnnualAmpPercentOrder();
                    String weightAmpPercent2 = dmFocAnnualAmp2.getWeightAnnualAmpPercentOrder();
                    if (flag) {
                        return compareWeightColumn(weightAmpPercent1, weightAmpPercent2);
                    }
                    return compareWeightColumn(weightAmpPercent2, weightAmpPercent1);
                }
            });
        } else {
            // 权重*涨跌 排序
            Collections.sort(dmFocAnnualAmpPagedResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnualAmp1, DmFocAnnualAmpVO dmFocAnnualAmp2) {
                    String weightAmpPercent1 = dmFocAnnualAmp1.getWeightAnnualAmpPercent();
                    String weightAmpPercent2 = dmFocAnnualAmp2.getWeightAnnualAmpPercent();
                    if (flag) {
                        return compareWeightColumn(weightAmpPercent1, weightAmpPercent2);
                    }
                    return compareWeightColumn(weightAmpPercent2, weightAmpPercent1);
                }
            });
        }
    }

    private String subPercentStr(String str) {
        if (str.contains("%")) {
            int index = str.indexOf("%");
            return str.substring(0, index);
        }
        return str;
    }

    /**
     * 权重相同时，根据中文或英文首字符进行排序
     *
     * @param dmFocAnnualAmp1 参数1
     * @param dmFocAnnualAmp2 参数2
     * @return 比较结果
     */
    private int sortByGroupCnName(DmFocAnnualAmpVO dmFocAnnualAmp1, DmFocAnnualAmpVO dmFocAnnualAmp2) {
        String groupCnName1 = dmFocAnnualAmp1.getGroupCnName() == null ? dmFocAnnualAmp1.getGroupCode() : dmFocAnnualAmp1.getGroupCnName();
        String groupCnName2 = dmFocAnnualAmp2.getGroupCnName() == null ? dmFocAnnualAmp2.getGroupCode() : dmFocAnnualAmp2.getGroupCnName();

        String alphabet1 = groupCnName1.substring(0, 1);
        // 判断首字符是否为中文，如果是中文便将首字符拼音的首字母和&符号加在字符串前面
        if (alphabet1.matches("[\\u4e00-\\u9fa5]+")) {
            groupCnName1 = PinyinUtil.chineseToPingyin(alphabet1) + "&" + groupCnName1;
        }
        String alphabet2 = groupCnName2.substring(0, 1);
        // 判断首字符是否为中文，如果是中文便将首字符拼音的首字母和&符号加在字符串前面
        if (alphabet2.matches("[\\u4e00-\\u9fa5]+")) {
            groupCnName2 = PinyinUtil.chineseToPingyin(alphabet2) + "&" + groupCnName2;
        }
        return Collator.getInstance(Locale.CHINA).compare(groupCnName1, groupCnName2);
    }

    public List<String> getYearListByVerison(String granularityType, Long versionId) {
        DmFcstVersionInfoDTO dmFocVersionDTOById = dmFcstVersionInfoDao.findDmFocVersionDTOById(versionId);
        if (dmFocVersionDTOById != null) {
            DmFcstVersionInfoDTO versionInfoDTO = new DmFcstVersionInfoDTO();
            versionInfoDTO.setVersion(dmFocVersionDTOById.getVersion());
            versionInfoDTO.setDataType("MONTH");
            DmFcstVersionInfoDTO dmFcstVersionInfoDTO = dmFcstVersionInfoDao.findVersionListByVersion(versionInfoDTO);
            return annualAmpPbiDao.getAnnualperiodYearList(granularityType, dmFcstVersionInfoDTO.getVersionId());
        }
        return Collections.EMPTY_LIST;
    }

    public List<String> getYearList(String granularityType) {
        DmFcstVersionInfoDTO versionInfoDTO = dmFcstVersionInfoDao.findVersionIdByDataType("MONTH");
        return annualAmpPbiDao.getAnnualperiodYearList(granularityType, versionInfoDTO.getVersionId());
    }

    public void setNoEffectiveAmp(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList, AnnualAnalysisVO annualAnalysisVO, String type) {
        String groupLevel = annualAnalysisVO.getGroupLevel();
        boolean encryptFlag = (!annualAnalysisVO.getIsNeedBlur() && CommonConstant.MIN_GROUP_LEVEL.stream().anyMatch(level -> level.equals(groupLevel))) || annualAnalysisVO.getIsNeedBlur();
        dmFocAnnualAmpVOList.stream().forEach(annualAmp -> {
            String statusCode = annualAmp.getStatusCode();
            // 最小层级
            if (encryptFlag) {
                if (StringUtils.isNotBlank(statusCode)) {
                    getHoverMsgForMinCondition(statusCode, annualAmp);
                }
            } else {
                if (StringUtils.isNotBlank(statusCode)) {
                    setGroupCodeHoverMsg(statusCode, annualAmp);
                }
            }
        });
        setPromptMessage(dmFocAnnualAmpVOList, type, groupLevel, encryptFlag);
    }

    private void getHoverMsgForMinCondition(String statusCode, DmFocAnnualAmpVO annualVO) {
        switch (statusCode) {
            case "1":
                annualVO.setHoverMsg(annualVO.getPeriodYear() + "年无数据，无法计算涨跌幅");
                break;
            case "2":
                annualVO.setHoverMsg(subtractNum(annualVO.getPeriodYear(), 1) + "、" + annualVO.getPeriodYear() + "年无数据，无法计算涨跌幅");
                break;
            case "4":
                annualVO.setHoverMsg(subtractNum(annualVO.getPeriodYear(), 1) + "无数据，且无法用历史年均本补齐");
                break;
            case "5":
                annualVO.setHoverMsg(annualVO.getAnnualAmp() + ";" + subtractNum(annualVO.getPeriodYear(), 1) + "年无数据，由" + annualVO.getAppendYear() + "年均本补齐");
                break;
            default:
                break;
        }
    }

    private void setGroupCodeHoverMsg(String statusCode, DmFocAnnualAmpVO annualVO) {
        switch (statusCode) {
            case "1":
                annualVO.setHoverMsg(annualVO.getPeriodYear() + "年无数据，无法计算涨跌幅");
                break;
            case "2":
                annualVO.setHoverMsg(subtractNum(annualVO.getPeriodYear(), 1) + "年无数据，无法计算涨跌幅");
                break;
            case "4":
                annualVO.setHoverMsg(subtractNum(annualVO.getPeriodYear(), 1) + "、" + annualVO.getPeriodYear() + "年无数据，无法计算涨跌幅");
                break;
            default:
                break;
        }
    }

    private void setPromptMessage(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList, String type, String groupLevel, boolean encryptFlag) {
        // 当涨跌幅为0且有提示语时，涨跌幅设置为0*
        dmFocAnnualAmpVOList.stream().forEach(annual -> {
            if (StringUtils.isNotBlank(annual.getHoverMsg()) && !"5".equals(annual.getStatusCode())
                    && ("0.0%".equals(annual.getAnnualAmp()) || "0.0".equals(annual.getAnnualAmp()))) {
                if ("data".equals(type)) {
                    annual.setAnnualAmp("0*");
                    annual.setWeightAnnualAmpPercent("0*");
                } else {
                    annual.setAnnualAmp(null);
                    if (!encryptFlag) {
                        annual.setWeightAnnualAmpPercent(null);
                    }
                }
            }
            if (!encryptFlag && StringUtils.isNotBlank(annual.getAppendFlag())
                    && "Y".equals(annual.getAppendFlag())) {
                if ("data".equals(type)) {
                    annual.setWeightRate("0*");
                } else {
                    annual.setWeightRate(null);
                }
            }
        });
    }

    private String subtractNum(String periodYear, int number) {
        BigDecimal yearDecimal = new BigDecimal(periodYear);
        BigDecimal year = yearDecimal.subtract(new BigDecimal(number));
        return year.toString();
    }

    @Override
    @JalorOperation(code = "getAnnualVersionList", desc = "查询年度分析页面版本列表")
    public ResultDataVO getAnnualVersionList(DmFcstVersionInfoDTO versionInfoDTO) throws ApplicationException {
        LOGGER.info(">>>Begin AnnualAmpPbiService::getAnnualVersionList");
        // 必填字段校验
        if (StringUtils.isBlank(versionInfoDTO.getVersion())) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        versionInfoDTO.setDataType("ANNUAL");
        return ResultDataVO.success(dmFcstVersionInfoDao.findVersionListByVersion(versionInfoDTO));
    }

    @Override
    @JalorOperation(code = "findActualPeriodId", desc = "查询年度实际数的开始和结束时间")
    public ResultDataVO findActualPeriodId(DmFcstVersionInfoDTO versionInfoDTO) throws ApplicationException {
        LOGGER.info(">>>Begin AnnualAmpPbiService::findActualPeriodId");
        if (versionInfoDTO.getVersionId() == null) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        DmFcstVersionInfoDTO dmFocVersionDTOById = dmFcstVersionInfoDao.findDmFocVersionDTOById(versionInfoDTO.getVersionId());
        versionInfoDTO.setVersion(dmFocVersionDTOById.getVersion());
        versionInfoDTO.setDataType("MONTH");
        DmFcstVersionInfoDTO dmFcstVersionInfoDTO = dmFcstVersionInfoDao.findVersionListByVersion(versionInfoDTO);
        if (dmFcstVersionInfoDTO == null) {
            throw new CommonApplicationException("查询不到对应的版本信息!");
        }
        return ResultDataVO.success(monthCostIdxDao.findStartEndTime(dmFcstVersionInfoDTO.getVersionId()));
    }
}
