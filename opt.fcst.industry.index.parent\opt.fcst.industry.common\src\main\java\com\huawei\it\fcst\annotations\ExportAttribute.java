/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.annotations;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 定义对象中的属性标记它在导出Excel时的列位置
 *
 * <AUTHOR>
 * @since 2022-10-27
 */
@Retention(RetentionPolicy.RUNTIME) // 运行时注解
@Target(ElementType.FIELD) // 字段属性注解
public @interface ExportAttribute {

    // 对应的列名称
    String name() default "";

    // 列序号 属性上 sort=0 在Excel导出时它就是第一列
    int sort();

    // 数据类型：String、Number、Date等
    String dataType() default "String";

    // 字段类型对应的格式
    String format() default "";
}