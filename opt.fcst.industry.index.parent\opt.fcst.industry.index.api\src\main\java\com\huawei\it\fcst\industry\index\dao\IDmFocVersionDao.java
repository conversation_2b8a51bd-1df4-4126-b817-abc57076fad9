/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoDTO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoVO;
import com.huawei.it.fcst.industry.index.vo.config.ManufactureBottomVO;
import com.huawei.it.fcst.industry.index.vo.config.ProcurementBottomVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/3/13
 */
public interface IDmFocVersionDao {
    /**
     * 根据数据类型查询对应的最新版本ID
     *
     * @param dataType 数据类型（category：TOP品类、item：规格品）
     * @return DmFoiPlanVersionVO
     */
    DmFocVersionInfoDTO findVersionIdByDataType(@Param("dataType") String dataType,@Param("tablePreFix") String tablePreFix);

    List<DmFocVersionInfoDTO> findDmFocVersionList(DmFocVersionInfoVO vo);

    List<DmFocVersionInfoDTO> findMftVersionList(DmFocVersionInfoVO vo);

    List<DmFocVersionInfoDTO> findMadeVersionList(DmFocVersionInfoVO vo);

    DmFocVersionInfoDTO findBaseReviewVersion(ProcurementBottomVO bottomVO);

    DmFocVersionInfoDTO findMadeReviewVersion(ManufactureBottomVO baseVO);

    /**
     * 通过id查询版本信息
     *
     * @param versionId 参数
     * @return DmFocVersionInfoDTO
     */
    DmFocVersionInfoDTO findDmFocVersionDTOById(@Param("versionId") Long versionId,@Param("tablePreFix") String tablePreFix);

    List<DmFocVersionInfoDTO> findDmFocVersionDTOList(DmFocVersionInfoDTO versionVo);

    List<DmFocVersionInfoDTO> findDmFocPlanVersionVOList(DmFocVersionInfoDTO vo);

    /**
     * Find DmFocPlanVersionVO by id.
     *
     * @param versionId is id
     * @return DmFocPlanVersionVO
     */
    DmFocVersionInfoDTO findDmFocPlanVersionVOById(@Param("versionId")Long versionId,@Param("tablePreFix")String tablePreFix);

    /**
     * 刷新维护维表
     *
     * @param versionVO 参数
     * @return int
     */
    int updateDmFocVersionDTO(DmFocVersionInfoDTO versionVO);

    int createDmFocVersionDTO(DmFocVersionInfoDTO dmFocVersionInfoDTO);

    Long getVersionKey(@Param("tablePreFix") String tablePreFix);

    DmFocVersionInfoDTO findAnnualVersion(@Param("tablePreFix") String tablePreFix);

    /**
     * 采购价格指数配置页面的映射维表数据，同步至产业成本指数映射维表
     *
     * @return String
     */
    String syncDimPurcharFunction(@Param("industryOrg")String industryOrg);
}
