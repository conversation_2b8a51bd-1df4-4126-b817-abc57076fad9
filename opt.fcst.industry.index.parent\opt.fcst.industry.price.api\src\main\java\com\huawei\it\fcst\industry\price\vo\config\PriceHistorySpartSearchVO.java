/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.vo.config;

import com.huawei.it.fcst.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 历史Spart清单查询条件VO
 *
 * <AUTHOR>
 * @since 2024-11-6
 */
@Data
@NoArgsConstructor
@ApiModel(value = "历史Spart清单查询条件VO")
public class PriceHistorySpartSearchVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -6421617453673579121L;

    @ApiModelProperty("版本")
    private Long versionId;

    @ApiModelProperty("版本名称")
    private String versionName;

    /**
     * 下拉框各层级Level
     * BG --> BG
     * 国内/海外 --> OS
     * 地区部 --> REG
     * 代表处 --> REP
     * 大T系统部 --> DT
     * 子网系统 --> SN
     * L1-L3.5 --> LV1-LV4
     * SPART --> SPART
     */
    @ApiModelProperty("各层级Level")
    private String groupLevel;

    @ApiModelProperty("BG编码")
    private String bgCode;

    @ApiModelProperty("BG名称")
    private String bgCnName;

    @ApiModelProperty("产品LV0编码")
    private String lv0ProdListCode;

    @ApiModelProperty("产品LV0中文名称")
    private String lv0ProdListCnName;

    @ApiModelProperty("产品LV1编码")
    private String lv1ProdListCode;

    @ApiModelProperty("产品LV1中文名称")
    private String lv1ProdListCnName;

    @ApiModelProperty("产品LV2编码")
    private String lv2ProdListCode;

    @ApiModelProperty("产品LV2中文名称")
    private String lv2ProdListCnName;

    @ApiModelProperty("产品LV3编码")
    private String lv3ProdListCode;

    @ApiModelProperty("产品LV3中文名称")
    private String lv3ProdListCnName;

    @ApiModelProperty("产品LV4编码")
    private String lv4ProdListCode;

    @ApiModelProperty("产品LV4中文名称")
    private String lv4ProdListCnName;

    @ApiModelProperty("TOP_SPART编码")
    private String topSpartCode;

    @ApiModelProperty("TOP_SPART中文名称")
    private String topSpartCnName;

    @ApiModelProperty("国内海外标识")
    private String overseaFlag;

    @ApiModelProperty("国内海外标识中文名称")
    private String overseaFlagCnName;

    @ApiModelProperty("地区部名称")
    private String regionCnName;

    @ApiModelProperty("地区部编码")
    private String regionCode;

    @ApiModelProperty("代表处编码")
    private String repofficeCode;

    @ApiModelProperty("代表处名称")
    private String repofficeCnName;

    @ApiModelProperty("签约客户_大T系统部编码")
    private String signTopCustCategoryCode;

    @ApiModelProperty("签约客户_大T系统部名称")
    private String signTopCustCategoryCnName;

    @ApiModelProperty("签约客户_子网系统部编码")
    private String signSubsidiaryCustcatgCode;

    @ApiModelProperty("签约客户_子网系统部名称")
    private String signSubsidiaryCustcatgCnName;

    @ApiModelProperty("路径区分")
    private String viewFlag;

    private String periodYear0;

    private String periodYear1;

    private String periodYear2;

    private String periodYear3;

    /**
     *
     *  报告期集合
     */
    List<String> yearPeriodList;

    private String weight0;

    private String weight1;

    private String weight2;

    private String weight3;

    private Integer pageIndex;

    private Integer pageSize;
}
