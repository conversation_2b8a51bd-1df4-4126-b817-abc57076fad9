/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.dao;

import com.huawei.it.fcst.vo.DmFocVarifyTaskVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023年03月22日
 */
public interface IPriceVarifyTaskDao {

    List<DmFocVarifyTaskVO> searchVerifyTask(@Param("varifyTaskVO") DmFocVarifyTaskVO varifyTaskVO);

    Long getVerifyTaskId();

    void insertVerifyTask(DmFocVarifyTaskVO varifyTaskVO);

    void updateVerifyTask(DmFocVarifyTaskVO varifyTaskVO);

    void updateMixVerifyTask(DmFocVarifyTaskVO varifyTaskVO);

}
