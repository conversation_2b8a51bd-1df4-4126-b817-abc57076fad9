/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.dao;

import com.huawei.it.fcst.industry.price.vo.annual.AnnualAnalysisVO;
import com.huawei.it.fcst.industry.price.vo.version.DmFcstVersionInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/3/13
 */
public interface IDmFcstPriceVersionInfoDao {
    /**
     * 根据数据类型查询对应的最新版本ID
     *
     * @param dataType 数据类型（category：TOP品类、item：规格品）
     * @return DmFoiPlanVersionVO
     */
    DmFcstVersionInfoVO findVersionIdByDataType(@Param("dataType") String dataType);

    /**
     * Delete DmFoiPlanVersionVO by id.
     *
     * @param versionId versionId
     * @return int
     */
    int deleteDmFcstVersionInfoById(Long versionId);

    int createDmFcstVersionInfoDTO(DmFcstVersionInfoVO dmFcstVersionInfoVO);

    /**
     * 根据版本信息查询版本列表信息
     * @param versionInfoVO
     * @return
     */
    List<DmFcstVersionInfoVO> findVersionListByVerName(DmFcstVersionInfoVO versionInfoVO);

    Long getVersionKey();

    List<DmFcstVersionInfoVO> findVersionList(DmFcstVersionInfoVO versionInfoVO);

    List<DmFcstVersionInfoVO> findRefreshTime();

    void updateRunningStatusFlag();

    void updateStatusFlag(Long versionId);

    List<DmFcstVersionInfoVO> findMaxDataReviewVersion();

    DmFcstVersionInfoVO findDmFocVersionById(Long versionId);

    String findAnnualActualMonth(AnnualAnalysisVO annualAnalysisVO);
}
