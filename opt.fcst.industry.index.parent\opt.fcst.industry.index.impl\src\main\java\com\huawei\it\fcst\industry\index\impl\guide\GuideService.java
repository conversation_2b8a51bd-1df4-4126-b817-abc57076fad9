/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.guide;

import com.huawei.it.fcst.industry.index.dao.IDmFocGuideInfoDao;
import com.huawei.it.fcst.industry.index.service.guide.IGuideService;
import com.huawei.it.fcst.industry.index.utils.UserInfoUtils;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.guide.GuideParamVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Named;
import java.util.regex.Pattern;

/**
 * GuideService Class
 *
 * <AUTHOR>
 * @since 2024/2/29
 */
@Named("guideService")
@JalorResource(code = "guideService", desc = "操作指南服务")
public class GuideService implements IGuideService {

    @Autowired
    private IDmFocGuideInfoDao dmFocGuideInfoDao;

    private static final Pattern PATTERN_URL = Pattern.compile("^https?://[a-z\\d-.]+\\.huawei\\.com([/?].*)?$");

    @JalorOperation(code = "findGuideInfoList", desc = "分页查询操作指南")
    @Override
    public ResultDataVO findGuideInfoList(GuideParamVO guideParamVO) {
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(guideParamVO.getPageSize());
        pageVO.setCurPage(guideParamVO.getPageIndex());
        return ResultDataVO.success(dmFocGuideInfoDao.findGuideInfoList(guideParamVO, pageVO));
    }

    @JalorOperation(code = "saveGuideInfo", desc = "新增操作指南")
    @Override
    @Audit(module = "guideService-saveGuideInfo", operation = "saveGuideInfo", message = "新增操作指南")
    public ResultDataVO saveGuideInfo(GuideParamVO guideParamVO) throws CommonApplicationException {
        if (!PATTERN_URL.matcher(guideParamVO.getGuideUrl()).matches()) {
            throw new CommonApplicationException("URL链接必须是华为内部链接地址");
        }
        guideParamVO.setId(dmFocGuideInfoDao.getGuideKey());
        Long userId = UserInfoUtils.getUserId();
        guideParamVO.setCreatedBy(userId);
        guideParamVO.setLastUpdatedBy(userId);
        dmFocGuideInfoDao.saveGuideInfo(guideParamVO);
        return ResultDataVO.success();
    }

    @JalorOperation(code = "deleteGuide", desc = "删除操作指南")
    @Audit(module = "guideService-deleteGuide", operation = "deleteGuide", message = "删除操作指南")
    @Override
    public ResultDataVO deleteGuide(GuideParamVO guideParamVO) {
        dmFocGuideInfoDao.deleteGuide(guideParamVO);
        return ResultDataVO.success();
    }
}
