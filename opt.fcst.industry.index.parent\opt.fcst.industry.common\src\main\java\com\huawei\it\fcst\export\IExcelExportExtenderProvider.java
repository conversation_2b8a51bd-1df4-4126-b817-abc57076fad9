/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.export;

import com.huawei.it.fcst.export.vo.ExcelExportContext;
import com.huawei.it.jalor5.core.exception.ApplicationException;

import org.apache.poi.ss.usermodel.Sheet;

/**
 * 导出动态扩展类
 *
 * <AUTHOR>
 * @since 202407
 */
public interface IExcelExportExtenderProvider {
    /**
     * 动态列头设置
     *
     * @param context 导出上下文
     * @param sheet   sheet
     * @throws ApplicationException 异常
     */
    void dynamicColumn(ExcelExportContext context, Sheet sheet) throws ApplicationException;

}
