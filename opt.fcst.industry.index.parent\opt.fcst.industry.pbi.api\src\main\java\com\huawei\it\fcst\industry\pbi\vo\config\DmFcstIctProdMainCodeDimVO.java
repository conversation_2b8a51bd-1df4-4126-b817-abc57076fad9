/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 主力产品主力编码新增/编辑/删除对应的实体类VO
 *
 * <AUTHOR>
 * @since 2024-07-11
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DmFcstIctProdMainCodeDimVO implements Serializable {

    private static final long serialVersionUID = -1851743327489525893L;

    /**
     * 页面入参版本ID
     */
    private Long versionId;

    /**
     * 主键ID集合
     */
    private List<Long> primaryIdList;

    /**
     * 新增主力编码集合
     */
    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<IctProdMainCodeDimVO> insertMainCodeDimVOList;

    /**
     * 编辑主力编码集合
     */
    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<IctProdMainCodeDimVO> editMainCodeDimVOList;

}