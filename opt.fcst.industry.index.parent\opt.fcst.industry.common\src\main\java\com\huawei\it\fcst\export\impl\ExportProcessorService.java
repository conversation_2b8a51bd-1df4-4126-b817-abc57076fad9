/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.export.impl;

import com.huawei.it.fcst.enums.MessageName;
import com.huawei.it.fcst.export.IExcelTemplateBeanManager;
import com.huawei.it.fcst.export.IExportProcessorService;
import com.huawei.it.fcst.export.vo.ExcelExportContext;
import com.huawei.it.fcst.export.vo.ExcelTemplateBeanManager;
import com.huawei.it.jalor5.async.AsyncMessage;
import com.huawei.it.jalor5.async.IMessageProcessor;
import com.huawei.it.jalor5.async.IMessageSender;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.inject.Inject;
import javax.inject.Named;
import javax.servlet.http.HttpServletResponse;

import java.io.Serializable;
import java.util.Map;

/**
 * 导出实现类
 */
@Slf4j
@Named("exportProcessorService")
public class ExportProcessorService implements IExportProcessorService {
    @Inject
    private IMessageSender messageSender;

    @Qualifier("industryExportMessageProcessor")
    @Autowired
    private IMessageProcessor exportMessageProcessor;

    @Override
    public void fillEasyExcelExport(HttpServletResponse response, IExcelTemplateBeanManager beanManager,
            Serializable condition, Map<String, Object> parameters) throws ApplicationException {
        this.fillEasyExcelExport(response, beanManager, condition, parameters, null);
    }

    @Override
    public void fillEasyExcelExport(HttpServletResponse response, IExcelTemplateBeanManager beanManager,
            Serializable condition, Map<String, Object> parameters, PageVO pageVO) throws ApplicationException {
        // 记录导出开始
        ExcelTemplateBeanManager excelTemPlateBeanManager = beanManager.getExcelTemPlateBeanManager();
        ExcelExportContext context = new ExcelExportContext(condition, excelTemPlateBeanManager);
        context.setParameters(parameters);
        context.setResponse(response);
        context.setIsAsync(Boolean.FALSE);
        context.setPageVO(pageVO);
        AsyncMessage asyncMessage = new AsyncMessage(MessageName.OPT_PBI_EXPORT.name());
        asyncMessage.setContent(context);
        exportMessageProcessor.process(asyncMessage);
    }

    @Override
    public void asyncFillEasyExcelExport(IExcelTemplateBeanManager beanManager, Serializable condition,
            Map<String, Object> parameters, PageVO pageVO) throws ApplicationException {
        // 异步任务传入taskID ，由于历史原因，这里没有做统一的逻辑处理
        ExcelTemplateBeanManager excelTemPlateBeanManager = beanManager.getExcelTemPlateBeanManager();
        ExcelExportContext context = new ExcelExportContext(condition, excelTemPlateBeanManager);
        context.setParameters(parameters);
        context.setPageVO(pageVO);
        AsyncMessage asyncMessage = new AsyncMessage(MessageName.OPT_PBI_EXPORT.name());
        asyncMessage.setContent(context);
        messageSender.send(asyncMessage);
    }

}
