/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.month;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huawei.it.fcst.industry.index.vo.common.TableNameVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * The Entity of DmFocPageInfoT
 *
 * <AUTHOR>
 * @since 2023/3/14
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "页面信息记录表实体类映射")
public class DmFocPageInfoVO  extends TableNameVO implements Serializable {
    private static final long serialVersionUID = 8131831472868194648L;

    /**
     * 表单编码               
     **/
    @ApiModelProperty(value = "表单编码")
    private Long pageId;

    /**
     * 表单编码
     **/
    @ApiModelProperty(value = "版本ID")
    private Long versionId;

    /**
     * 页面标识（Y：年度分析另存、M：月度分析另存）
     **/
    @ApiModelProperty(value = "页面标识（Y：年度分析另存、M：月度分析另存）")
    private String pageFlag;

    /**
     * 用户id                 
     **/
    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "页面名称")
    private String pageName;

    /**
     * 保存阈值            
     **/
    @ApiModelProperty(value = "保存阈值")
    private String saveThreshold;

    @ApiModelProperty(value = "默认标识：Y-默认，N-非默认")
    private String defaultFlag;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date creationDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "修改时间")
    private Date lastUpdateDate;

    @ApiModelProperty(value = "创建人")
    private Long createdBy;

    @ApiModelProperty(value = "修改人")
    private Long lastUpdatedBy;

    @ApiModelProperty(value = "是否删除")
    private String delFlag;

    @ApiModelProperty(value = "角色ID")
    private String roleId;

    @ApiModelProperty(value = "数据范围")
    private String dataRange;


    @ApiModelProperty(value = "颗粒度（U：通用，P：盈利)")
    private String granule;

    @ApiModelProperty(value = "业务口径（R:收入时点/C：发货成本）")
    private String caliberFlag;


    @ApiModelProperty(value = "成本类型")
    private String costType;


}
