/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.month;

import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumD;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumMadeU;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumP;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumU;
import com.huawei.it.fcst.industry.index.enums.IndustryConst;
import com.huawei.it.fcst.industry.index.enums.IndustryIndexEnum;
import com.huawei.it.fcst.industry.index.enums.ModuleEnum;
import com.huawei.it.fcst.industry.index.impl.annual.StatisticsExcelService;
import com.huawei.it.fcst.industry.index.vo.common.DmFoiImpExpRecordVO;
import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.request.IRequestContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;

import javax.inject.Named;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.concurrent.Future;

/**
 * MonthExportService Class
 *
 * <AUTHOR>
 * @since 2023/10/23
 */
@Slf4j
@Named("monthExportService")
public class MonthExportService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MonthExportService.class);

    @Autowired
    private StatisticsExcelService statisticsExcelService;

    @Autowired
    private AsyncExportService asyncExportService;

    public void exportData(MonthAnalysisVO monthAnalysisVO, XSSFWorkbook workbook, String groupCnName, IRequestContext current, Long userId) throws Exception {
        int totalRows = 0;
        int excelTotalRows = exportExcelData(monthAnalysisVO, totalRows, workbook, groupCnName, current);

        // 插入导出记录信息，并上传导出文件到S3服务器
        insertExportRecord(excelTotalRows, workbook, monthAnalysisVO, userId);
    }

    private void insertExportRecord(int totalRows, Workbook workbook, MonthAnalysisVO monthAnalysisVO, Long userId) throws IOException, CommonApplicationException {
        // 插入数据，上传文件
        String fileName = monthAnalysisVO.getFileName();
        DmFoiImpExpRecordVO dmFoiImpExpRecordVO = StatisticsExcelService.uploadExportExcel(workbook, totalRows, fileName, userId);
        if (IndustryConst.INDUSTRY_ORG.ICT.getValue().equals(monthAnalysisVO.getIndustryOrg())) {
            dmFoiImpExpRecordVO.setModuleType(IndustryConst.INDUSTRY_ORG_PAGE.getValue(monthAnalysisVO.getIndustryOrg())
                    + ModuleEnum.SAME_CODE_MONH.getCnName() +"("+IndustryIndexEnum.getCostType(monthAnalysisVO.getCostType()).getDesc()+")");
        } else{
            dmFoiImpExpRecordVO.setModuleType(IndustryConst.INDUSTRY_ORG_PAGE.getValue(monthAnalysisVO.getIndustryOrg())
                    + ModuleEnum.MONTH.getCnName() +"("+IndustryIndexEnum.getCostType(monthAnalysisVO.getCostType()).getDesc()+")");
        }
        dmFoiImpExpRecordVO.setUserId(String.valueOf(userId));
        // 设置创建时间和结束时间
        dmFoiImpExpRecordVO.setCreationDate(monthAnalysisVO.getCreationDate());
        dmFoiImpExpRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        dmFoiImpExpRecordVO.setEndDate(new Timestamp(System.currentTimeMillis()));
        // 插入数据
        statisticsExcelService.insertMonthExportRecord(dmFoiImpExpRecordVO);
    }

    public int exportExcelData(MonthAnalysisVO monthAnalysisVO, int totalRows, XSSFWorkbook workbook, String groupCnName, IRequestContext current) throws Exception {
        Integer exportDataCount = getExportDataReverseOrComb(monthAnalysisVO, workbook, groupCnName, current);
        if (exportDataCount != null) {
            return exportDataCount;
        }

        Integer exportDataCount1 = getExportDataWithTemplate1(monthAnalysisVO, workbook, groupCnName, current);
        if (exportDataCount1 != null) {
            return exportDataCount1;
        }

        Integer exportDataCountOther = getExportDataWithOtherTemp(monthAnalysisVO, workbook, groupCnName, current);
        if (exportDataCountOther != null) {
            return exportDataCountOther;
        }
        return totalRows;
    }

    @Nullable
    private Integer getExportDataReverseOrComb(MonthAnalysisVO searchParamsVO, XSSFWorkbook workbook, String groupCnName, IRequestContext current) throws Exception {
        if (!searchParamsVO.getIsShowPriceChart()) {
            return exportTemplate6(searchParamsVO, workbook, groupCnName, current);
        }
        if (!searchParamsVO.getIsShowChildContent()) {
            return exportTemplate5(searchParamsVO, workbook, groupCnName, current);
        }
        if (!searchParamsVO.getIsTotalChildChart()) {
            return exportTemplate7(searchParamsVO, workbook, groupCnName, current);
        }
        // 勾选了汇总组合导出模板3
        if (searchParamsVO.getIsContainComb()) {
            // 导出模板3的数据
            return exportTemplate3(searchParamsVO, workbook, groupCnName, current);
        }
        // 反向视角 通用颗粒度 5，6，7
        if (IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(searchParamsVO.getGranularityType())) {
            if (IndustryIndexEnum.VIEW_FLAG_U.VIEW5.getValue().equals(searchParamsVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_U.VIEW6.getValue().equals(searchParamsVO.getViewFlag())
                    || IndustryIndexEnum.VIEW_FLAG_U.VIEW7.getValue().equals(searchParamsVO.getViewFlag())) {
                // 导出模板4的数据
                return exportTemplate4(searchParamsVO, workbook, groupCnName, current);
            }
        }
        return null;
    }

    @Nullable
    private Integer getExportDataWithTemplate1(MonthAnalysisVO searchParamsVO, XSSFWorkbook workbook, String groupCnName, IRequestContext current) throws Exception {
        // ICT,LV1,LV2 LV3,L1,L2层级,使用模板1导出数据
        boolean groupLevelCondition = GroupLevelEnumP.L1.getValue().equals(searchParamsVO.getGroupLevel())
                || GroupLevelEnumP.L2.getValue().equals(searchParamsVO.getGroupLevel())
                || GroupLevelEnumU.LV3.getValue().equals(searchParamsVO.getGroupLevel())
                || GroupLevelEnumU.LV4.getValue().equals(searchParamsVO.getGroupLevel());
        if (GroupLevelEnumU.LV0.getValue().equals(searchParamsVO.getGroupLevel()) ||
                GroupLevelEnumU.LV1.getValue().equals(searchParamsVO.getGroupLevel()) ||
                GroupLevelEnumU.LV2.getValue().equals(searchParamsVO.getGroupLevel()) ||groupLevelCondition) {
            // 导出模板1的数据
            return exportTemplate1(searchParamsVO, workbook, groupCnName, current);
        }
        return null;
    }

    @Nullable
    private Integer getExportDataWithOtherTemp(MonthAnalysisVO searchParamsVO, XSSFWorkbook workbook, String groupCnName, IRequestContext current) throws Exception {
        // 量纲层级维度,使用模板1导出数据
        if (GroupLevelEnumD.DIMENSION.getValue().equals(searchParamsVO.getGroupLevel()) ||
                GroupLevelEnumD.SUBCATEGORY.getValue().equals(searchParamsVO.getGroupLevel()) ||
                GroupLevelEnumD.SUB_DETAIL.getValue().equals(searchParamsVO.getGroupLevel())||
                GroupLevelEnumD.SPART.getValue().equals(searchParamsVO.getGroupLevel())) {
            // 导出模板1的数据
            return exportTemplate1(searchParamsVO, workbook, groupCnName, current);
        }
        // CEG品类层级,使用模板2导出数据
        if (GroupLevelEnumU.CEG.getValue().equals(searchParamsVO.getGroupLevel()) || GroupLevelEnumU.MODL.getValue().equals(searchParamsVO.getGroupLevel())|| GroupLevelEnumMadeU.SHIPPING_OBJECT.getValue().equals(searchParamsVO.getGroupLevel())) {
            // 导出模板2的数据
            return exportTemplate2(searchParamsVO, workbook, groupCnName, current);
        }
        // 专项采购认证部层级,使用模板3导出数据
        if (GroupLevelEnumU.CATEGORY.getValue().equals(searchParamsVO.getGroupLevel())|| GroupLevelEnumMadeU.MANUFACTURE_OBJECT.getValue().equals(searchParamsVO.getGroupLevel())) {
            // 导出模板3的数据
            return exportTemplate3(searchParamsVO, workbook, groupCnName, current);
        }
        return null;
    }

    private int exportTemplate1(MonthAnalysisVO searchParamsVO, XSSFWorkbook workbook, String groupCnName, IRequestContext current) throws Exception {
        // 导出模板2，对应的sheet页为：产业成本指数图 产业成本指数同步环比图    产业成本指数（多指数） 产业成本权重图,热力图
        // 填充产业成本价格指数图Sheet页数据
        Future<Integer> priceIdxTotal = asyncExportService.fillPriceIdxSheet(workbook, 0, groupCnName, searchParamsVO, current);

        // 填充产业成本同比环比图Sheet页数据
        Future<Integer> monthYoyTotal = asyncExportService.fillMonthYoySheet(workbook, 1, groupCnName, searchParamsVO, current);

        // 填充产业成本价格指数图（多指数）的Sheet页数据
        Future<Integer> multiIdxTotal = asyncExportService.fillMultiIndexTemplate1Sheet(searchParamsVO, workbook, 2, groupCnName, current);

        // 填充权重图Sheet页的数据
        Future<Integer> weightTotal = asyncExportService.fillWeightTemplate1Sheet(searchParamsVO, workbook, 4, groupCnName, current);

        // 填充热力图Sheet页的数据
        Future<Integer> heapTotal = asyncExportService.fillHeapTemplate1Sheet(searchParamsVO, workbook, 5, groupCnName, current);

        Future<Integer> distributeCostTotal = new AsyncResult<>(0);
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(searchParamsVO.getCostType())) {
            distributeCostTotal = asyncExportService.fillDistributeCostSheet(searchParamsVO, workbook, 3, groupCnName, current);
        }
        while (true) {
            boolean threeFlag = priceIdxTotal.isDone() && multiIdxTotal.isDone() && weightTotal.isDone();
            boolean allDoneFlag = threeFlag && heapTotal.isDone() && monthYoyTotal.isDone();
            if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(searchParamsVO.getCostType())) {
                boolean totalFlag = distributeCostTotal.isDone();
                if (allDoneFlag && totalFlag) {
                    break;
                }
            } else {
                if (allDoneFlag) {
                    break;
                }
            }
        }
        if (!IndustryIndexEnum.COST_TYPE.T.getValue().equals(searchParamsVO.getCostType())) {
            workbook.removeSheetAt(3);
        }
        return priceIdxTotal.get() + monthYoyTotal.get() + multiIdxTotal.get() + weightTotal.get() + heapTotal.get() + distributeCostTotal.get();
    }

    private int exportTemplate2(MonthAnalysisVO searchParamsVO, XSSFWorkbook workbook, String groupCnName, IRequestContext current) throws Exception {
        // 导出模板1，对应的sheet页为：产业成本指数图 产业成本指数同步环比图    产业成本指数（多指数） 产业成本权重图  产业成本热力图
        // 填充价格指数图Sheet页数据
        Future<Integer> priceIdxTotal = asyncExportService.fillPriceIdxSheet(workbook, 0, groupCnName, searchParamsVO, current);

        // 填充产业成本同比环比图Sheet页数据
        Future<Integer> monthYoyTotal = asyncExportService.fillMonthYoySheet(workbook, 1, groupCnName, searchParamsVO, current);

        // 填充采购价格指数图（多指数）的Sheet页数据
        Future<Integer> multiIdxTotal = asyncExportService.fillMultiIndexTemplate2Sheet(searchParamsVO, workbook, 2, groupCnName, current);

        // 填充权重图Sheet页的数据
        Future<Integer> weightTotal = asyncExportService.fillWeightTemplate2Sheet(searchParamsVO, workbook, 4, groupCnName, current);

        // 填充热力图Sheet页的数据
        Future<Integer> heapTotal = asyncExportService.fillHeapTemplate2Sheet(searchParamsVO, workbook, 5, groupCnName, current);

        Future<Integer> distributeTotal = new AsyncResult<>(0);
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(searchParamsVO.getCostType())) {
            distributeTotal = asyncExportService.fillDistributeCostSheet(searchParamsVO, workbook, 3, groupCnName, current);
        }
        while (true) {
            boolean threeDoneFlag = priceIdxTotal.isDone() && multiIdxTotal.isDone() && weightTotal.isDone();
            boolean allDoneFlag = threeDoneFlag && heapTotal.isDone() && monthYoyTotal.isDone();
            if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(searchParamsVO.getCostType())) {
                boolean total = distributeTotal.isDone();
                if (allDoneFlag && total) {
                    break;
                }
            } else {
                if (allDoneFlag) {
                    break;
                }
            }
        }
        if (!IndustryIndexEnum.COST_TYPE.T.getValue().equals(searchParamsVO.getCostType())) {
            workbook.removeSheetAt(3);
        }
        return priceIdxTotal.get() + multiIdxTotal.get() + weightTotal.get() + heapTotal.get() + monthYoyTotal.get() + distributeTotal.get();
    }

    private int exportTemplate3(MonthAnalysisVO searchParamsVO, XSSFWorkbook workbook, String groupCnName, IRequestContext current) throws Exception {
        // 导出模板1，对应的sheet页为：产业成本指数图 产业成本指数同步环比图    产业成本指数（多指数） 产业成本权重图

        // 填充价格指数图Sheet页数据
        Future<Integer> priceIdxTotal = asyncExportService.fillPriceIdxSheet(workbook, 0, groupCnName, searchParamsVO, current);

        // 填充产业成本同比环比图Sheet页数据
        Future<Integer> monthYoyTotal = asyncExportService.fillMonthYoySheet(workbook, 1, groupCnName, searchParamsVO, current);

        // 填充采购价格指数图（多指数）的Sheet页数据
        Future<Integer> multiIdxTotal = asyncExportService.fillMultiIndexTemplate2Sheet(searchParamsVO, workbook, 2, groupCnName, current);

        // 填充权重图Sheet页的数据
        Future<Integer> weightTotal = asyncExportService.fillWeightTemplate2Sheet(searchParamsVO, workbook, 4, groupCnName, current);

        Future<Integer> distributeCostTotal = new AsyncResult<>(0);
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(searchParamsVO.getCostType())) {
            distributeCostTotal = asyncExportService.fillDistributeCostSheet(searchParamsVO, workbook, 3, groupCnName, current);
        }
        while (true) {
            boolean allDoneFlag = priceIdxTotal.isDone() && multiIdxTotal.isDone() && weightTotal.isDone() && monthYoyTotal.isDone();
            if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(searchParamsVO.getCostType())) {
                boolean total = distributeCostTotal.isDone();
                if (allDoneFlag && total) {
                    break;
                }
            } else {
                if (allDoneFlag) {
                    break;
                }
            }
        }
        if (!IndustryIndexEnum.COST_TYPE.T.getValue().equals(searchParamsVO.getCostType())) {
            workbook.removeSheetAt(3);
        }
        return priceIdxTotal.get() + multiIdxTotal.get() + weightTotal.get() + monthYoyTotal.get() + distributeCostTotal.get();
    }

    private int exportTemplate4(MonthAnalysisVO searchParamsVO, XSSFWorkbook workbook, String groupCnName, IRequestContext current) throws Exception {
        // 导出模板1，对应的sheet页为：产业成本指数图 产业成本指数同步环比图    产业成本指数（多指数） 产业成本权重图

        // 填充价格指数图Sheet页数据
        Future<Integer> priceIdxTotal = asyncExportService.fillPriceIdxSheet(workbook, 0, groupCnName, searchParamsVO, current);

        // 填充采购价格指数图（多指数）的Sheet页数据
        Future<Integer> multiIdxTotal = asyncExportService.fillMultiIndexTemplate1Sheet(searchParamsVO, workbook, 2, groupCnName, current);
        Future<Integer> distributeTotal = new AsyncResult<>(0);
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(searchParamsVO.getCostType())) {
            distributeTotal = asyncExportService.fillDistributeCostSheet(searchParamsVO, workbook, 1, groupCnName, current);
        }

        while (true) {
            boolean allDoneFlag = priceIdxTotal.isDone() && multiIdxTotal.isDone();
            if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(searchParamsVO.getCostType())) {
                if (allDoneFlag && distributeTotal.isDone()) {
                    break;
                }
            } else {
                if (allDoneFlag) {
                    break;
                }
            }
        }
        if (!IndustryIndexEnum.COST_TYPE.T.getValue().equals(searchParamsVO.getCostType())) {
            workbook.removeSheetAt(1);
        }
        return priceIdxTotal.get() + multiIdxTotal.get() + distributeTotal.get();
    }

    private int exportTemplate5(MonthAnalysisVO searchParamsVO, XSSFWorkbook workbook, String groupCnName, IRequestContext current) throws Exception {
        // 导出模板1，对应的sheet页为：产业成本指数图 产业成本指数同步环比图    产业成本指数（多指数） 产业成本权重图

        // 填充价格指数图Sheet页数据
        Future<Integer> priceIdxTotal = asyncExportService.fillPriceIdxSheet(workbook, 0, groupCnName, searchParamsVO, current);

        // 填充产业成本同比环比图Sheet页数据
        Future<Integer> monthYoyTotal = asyncExportService.fillMonthYoySheet(workbook, 1, groupCnName, searchParamsVO, current);

        // 填充权重图Sheet页的数据
        Future<Integer> weightTotal = asyncExportService.fillWeightTemplate2Sheet(searchParamsVO, workbook, 3, groupCnName, current);
        Future<Integer> distributeTotal = new AsyncResult<>(0);
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(searchParamsVO.getCostType())) {
            distributeTotal = asyncExportService.fillDistributeCostSheet(searchParamsVO, workbook, 2, groupCnName, current);
        }
        while (true) {
            boolean allSheetDoneFlag = priceIdxTotal.isDone() && weightTotal.isDone() && monthYoyTotal.isDone();
            if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(searchParamsVO.getCostType())) {
                boolean total = distributeTotal.isDone();
                if (allSheetDoneFlag && total) {
                    break;
                }
            } else {
                if (allSheetDoneFlag) {
                    break;
                }
            }
        }
        if (!IndustryIndexEnum.COST_TYPE.T.getValue().equals(searchParamsVO.getCostType())) {
            workbook.removeSheetAt(2);
        }
        return priceIdxTotal.get() + weightTotal.get() + monthYoyTotal.get() + distributeTotal.get();
    }

    private int exportTemplate6(MonthAnalysisVO monthAnalysisVO, XSSFWorkbook workbook, String groupCnName, IRequestContext current) throws Exception {
        // 填充采购价格指数图（多指数）的Sheet页数据
        Future<Integer> multiIdxTotal = asyncExportService.fillMultiIndexTemplate1Sheet(monthAnalysisVO, workbook, 0, groupCnName, current);

        while (true) {
            boolean allDoneFlag = multiIdxTotal.isDone();
            if (allDoneFlag) {
                break;
            }
        }
        return multiIdxTotal.get();
    }

    private int exportTemplate7(MonthAnalysisVO searchParamsVO, XSSFWorkbook workbook, String groupCnName, IRequestContext current) throws Exception {

        // 填充价格指数图Sheet页数据
        Future<Integer> priceIdxTotal = asyncExportService.fillPriceIdxSheet(workbook, 0, groupCnName, searchParamsVO, current);

        // 填充产业成本同比环比图Sheet页数据
        Future<Integer> monthYoyTotal = asyncExportService.fillMonthYoySheet(workbook, 1, groupCnName, searchParamsVO, current);
        Future<Integer> distributeTotal = asyncExportService.fillDistributeCostSheet(searchParamsVO, workbook, 2, groupCnName, current);

        while (true) {
            boolean allDoneFlag = priceIdxTotal.isDone() && monthYoyTotal.isDone() && distributeTotal.isDone();
            if (allDoneFlag) {
                break;
            }
        }
        return priceIdxTotal.get() + monthYoyTotal.get() + distributeTotal.get();
    }
}
