<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.price.dao.IPriceTopSpartInfoDao">
    <resultMap type="com.huawei.it.fcst.industry.price.vo.config.PriceHistorySpartListVO" id="resultMap">
        <result property="versionId" column="version_id"/>
        <result property="periodYear" column="period_year"/>
        <result property="lv0ProdListCode" column="lv0_prod_list_code"/>
        <result property="lv1ProdListCode" column="lv1_prod_list_code"/>
        <result property="lv2ProdListCode" column="lv2_prod_list_code"/>
        <result property="lv3ProdListCode" column="lv3_prod_list_code"/>
        <result property="lv4ProdListCode" column="lv4_prod_list_code"/>
        <result property="lv0ProdListCnName" column="lv0_prod_list_cn_name"/>
        <result property="lv1ProdListCnName" column="lv1_prod_list_cn_name"/>
        <result property="lv2ProdListCnName" column="lv2_prod_list_cn_name"/>
        <result property="lv3ProdListCnName" column="lv3_prod_list_cn_name"/>
        <result property="lv4ProdListCnName" column="lv4_prod_list_cn_name"/>
        <result property="topSpartCode" column="top_spart_code"/>
        <result property="topSpartCnName" column="top_spart_cn_name"/>
        <result property="overseaFlag" column="oversea_flag"/>
        <result property="overseaFlagCnName" column="oversea_flag_cn_name"/>
        <result property="bgCode" column="bg_code"/>
        <result property="bgCnName" column="bg_cn_name"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="regionCode" column="region_code"/>
        <result property="regionCnName" column="region_cn_name"/>
        <result property="repofficeCode" column="repoffice_code"/>
        <result property="repofficeCnName" column="repoffice_cn_name"/>
        <result property="signTopCustCategoryCode" column="sign_top_cust_category_code"/>
        <result property="signTopCustCategoryCnName" column="sign_top_cust_category_cn_name"/>
        <result property="signSubsidiaryCustcatgCode" column="sign_subsidiary_custcatg_cn_name"/>
        <result property="signSubsidiaryCustcatgCnName" column="sign_subsidiary_custcatg_cn_name"/>
        <result property="isTopFlag" column="is_top_flag"/>
        <result property="ytdTopFlag" column="ytd_top_flag"/>
        <result property="weightRate" column="weight_rate"/>
        <result property="periodYear0" column="period_year0"/>
        <result property="periodYear1" column="period_year1"/>
        <result property="periodYear2" column="period_year2"/>
        <result property="periodYear3" column="period_year3"/>
        <result property="weight0" column="weight0"/>
        <result property="weight1" column="weight1"/>
        <result property="weight2" column="weight2"/>
        <result property="weight3" column="weight3"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="doubleFlag" column="double_flag"/>
    </resultMap>

    <sql id="searchFields">
        <if test='_parameter.get("0").viewFlag != null and _parameter.get("0").viewFlag!=""'>
            AND view_flag = #{0.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").bgCode != null and _parameter.get("0").bgCode!=""'>
            AND bg_code = #{0.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").lv1ProdListCode != null and _parameter.get("0").lv1ProdListCode!=""'>
            AND lv1_prod_list_code = #{0.lv1ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").lv2ProdListCode != null and _parameter.get("0").lv2ProdListCode!=""'>
            AND lv2_prod_list_code = #{0.lv2ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").lv3ProdListCode != null and _parameter.get("0").lv3ProdListCode!=""'>
            AND lv3_prod_list_code = #{0.lv3ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").lv4ProdListCode != null and _parameter.get("0").lv4ProdListCode!=""'>
            AND lv4_prod_list_code = #{0.lv4ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").overseaFlag != null and _parameter.get("0").overseaFlag!=""'>
            AND oversea_flag = #{0.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").regionCode != null and _parameter.get("0").regionCode!=""'>
            AND region_code = #{0.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").repofficeCode != null and _parameter.get("0").repofficeCode!=""'>
            AND repoffice_code = #{0.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").signTopCustCategoryCode != null and _parameter.get("0").signTopCustCategoryCode != ""'>
            AND sign_top_cust_category_code = #{0.signTopCustCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").signSubsidiaryCustcatgCnName != null and _parameter.get("0").signSubsidiaryCustcatgCnName != ""'>
            AND sign_subsidiary_custcatg_cn_name = #{0.signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").topSpartCode != null and _parameter.get("0").topSpartCode!=""'>
            AND top_spart_code = #{0.topSpartCode,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").versionId != null'>
            AND version_id = #{0.versionId,jdbcType=VARCHAR}
        </if>
        <if test='_parameter.get("0").lastUpdatedBy != null'>
            AND last_updated_by LIKE CONCAT(CONCAT('%', #{0.lastUpdatedBy,jdbcType=VARCHAR}) ,'%')
        </if>
        <if test='_parameter.get("0").creationDate != null'>
            AND creation_date=#{0.creationDate,jdbcType=TIMESTAMP}
        </if>
        <if test='_parameter.get("0").createdBy != null'>
            AND created_by LIKE CONCAT(CONCAT('%', #{0.createdBy,jdbcType=VARCHAR}) ,'%')
        </if>
        <if test='_parameter.get("0").lastUpdateDate != null'>
            AND last_update_date=#{0.lastUpdateDate,jdbcType=TIMESTAMP}
        </if>
    </sql>

    <select id="findTopSpartByPage" resultMap="resultMap">
        SELECT
            d1.bg_code,
            d1.bg_cn_name,
            d1.lv0_prod_list_code,
            d1.lv1_prod_list_code,
            d1.lv2_prod_list_code,
            d1.lv3_prod_list_code,
            d1.lv4_prod_list_code,
            d1.lv0_prod_list_cn_name,
            d1.lv1_prod_list_cn_name,
            d1.lv2_prod_list_cn_name,
            d1.lv3_prod_list_cn_name,
            d1.lv4_prod_list_cn_name,
            d1.top_spart_cn_name,
            d1.top_spart_code,
            d1.region_code,
            d1.region_cn_name,
            d1.repoffice_code,
            d1.repoffice_cn_name,
            d1.sign_top_cust_category_cn_name AS sign_top_cust_category_code,
            d1.sign_top_cust_category_cn_name,
            d1.sign_subsidiary_custcatg_cn_name,
            d1.period_year0,
            d1.period_year1,
            d1.period_year2,
            d1.period_year3,
            COALESCE (CAST(d1.weight0 AS varchar), '-') AS weight0,
            COALESCE (CAST(d1.weight1 AS varchar), '-') AS weight1,
            COALESCE (CAST(d1.weight2 AS varchar), '-') AS weight2,
            COALESCE (CAST(d1.weight3 AS varchar), '-') AS weight3,
            DECODE(d1.oversea_flag, 'G', '全球', 'Y', '海外', '国内') AS oversea_flag_cn_name,
            DECODE(d1.is_top_flag, 'Y', '是', '否') AS is_top_flag
        FROM (
        SELECT
            bg_code,
            bg_cn_name,
            lv0_prod_list_code,
            lv1_prod_list_code,
            lv2_prod_list_code,
            lv3_prod_list_code,
            lv4_prod_list_code,
            lv0_prod_list_cn_name,
            lv1_prod_list_cn_name,
            lv2_prod_list_cn_name,
            lv3_prod_list_cn_name,
            lv4_prod_list_cn_name,
            top_spart_cn_name,
            top_spart_code,
            region_code,
            region_cn_name,
            repoffice_code,
            repoffice_cn_name,
            sign_top_cust_category_cn_name AS sign_top_cust_category_code,
            sign_top_cust_category_cn_name,
            sign_subsidiary_custcatg_cn_name,
            oversea_flag,
            #{0.periodYear0} AS period_year0,
            #{0.periodYear1} AS period_year1,
            #{0.periodYear2} AS period_year2,
            #{0.periodYear3} AS period_year3,
            SUM ( CASE WHEN period_year = #{0.periodYear0} THEN weight_rate END ) AS weight0,
            SUM ( CASE WHEN period_year = #{0.periodYear1} THEN weight_rate END ) AS weight1,
            SUM ( CASE WHEN period_year = #{0.periodYear2} THEN weight_rate END ) AS weight2,
            SUM ( CASE WHEN period_year = #{0.periodYear3} THEN weight_rate END ) AS weight3,
            MAX(CASE WHEN period_year = #{0.periodYear3} THEN is_top_flag ELSE NULL END) AS is_top_flag
        FROM fin_dm_opt_foi.dm_fcst_price_top_spart_info_t
        where del_flag = 'N'
        <include refid="searchFields"/>
        GROUP BY
        lv0_prod_list_code,
        lv0_prod_list_cn_name,
        lv1_prod_list_code,
        lv1_prod_list_cn_name,
        lv2_prod_list_code,
        lv2_prod_list_cn_name,
        lv3_prod_list_code,
        lv3_prod_list_cn_name,
        lv4_prod_list_code,
        lv4_prod_list_cn_name,
        top_spart_code,
        top_spart_cn_name,
        bg_code,
        bg_cn_name,
        region_code,
        region_cn_name,
        repoffice_code,
        repoffice_cn_name,
        sign_top_cust_category_cn_name,
        sign_subsidiary_custcatg_cn_name,
        oversea_flag
        ) d1
        ORDER BY IF(ISNULL(d1.weight3),1,0), d1.weight3 DESC
        LIMIT #{1.pageSize} OFFSET #{1.mysqlStartIndex}
    </select>

    <select id="findTopSpartByPageCount" resultType="int">
        SELECT COUNT(1) FROM (
        SELECT 1 FROM fin_dm_opt_foi.dm_fcst_price_top_spart_info_t
        WHERE del_flag = 'N'
        <include refid="searchFields"/>
        GROUP BY
        lv0_prod_list_code,
        lv0_prod_list_cn_name,
        lv1_prod_list_code,
        lv1_prod_list_cn_name,
        lv2_prod_list_code,
        lv2_prod_list_cn_name,
        lv3_prod_list_code,
        lv3_prod_list_cn_name,
        lv4_prod_list_code,
        lv4_prod_list_cn_name,
        top_spart_code,
        top_spart_cn_name,
        bg_code,
        bg_cn_name,
        region_code,
        region_cn_name,
        repoffice_code,
        repoffice_cn_name,
        sign_top_cust_category_cn_name,
        sign_subsidiary_custcatg_cn_name,
        oversea_flag
        ) t
    </select>

    <select id="findDropdownList" resultMap="resultMap">
        SELECT DISTINCT version_id
        <choose>
            <when test='groupLevel=="BG"'>
                ,bg_code, bg_cn_name, bg_code AS rank
            </when>
            <when test='groupLevel=="OS"'>
                ,oversea_flag
                ,decode(oversea_flag, 'G', '全球', 'Y', '海外', '国内') AS oversea_flag_cn_name
                ,oversea_flag AS rank
            </when>
            <when test='groupLevel=="REG"'>
                ,region_code, region_cn_name, DECODE(region_code, 'ALL', '000000', region_code) rank
            </when>
            <when test='groupLevel=="REP"'>
                ,repoffice_code, repoffice_cn_name, DECODE(repoffice_code, 'ALL', '000000', repoffice_code) rank
            </when>
            <when test='groupLevel=="DT"'>
                ,sign_top_cust_category_code
                ,sign_top_cust_category_cn_name
                ,DECODE(sign_top_cust_category_code, 'ALL', '000000', sign_top_cust_category_code) rank
            </when>
            <when test='groupLevel=="SN"'>
                ,sign_subsidiary_custcatg_cn_name AS sign_subsidiary_custcatg_code, sign_subsidiary_custcatg_cn_name
                ,DECODE(sign_subsidiary_custcatg_cn_name, '全选', '000000', sign_subsidiary_custcatg_cn_name) rank
            </when>
            <when test='groupLevel=="LV1"'>
                ,lv1_prod_list_code, lv1_prod_list_cn_name, lv1_prod_list_code AS rank
            </when>
            <when test='groupLevel=="LV2"'>
                ,lv2_prod_list_code, lv2_prod_list_cn_name, lv2_prod_list_code AS rank
            </when>
            <when test='groupLevel=="LV3"'>
                ,lv3_prod_list_code, lv3_prod_list_cn_name, lv3_prod_list_code AS rank
            </when>
            <when test='groupLevel=="LV4"'>
                ,lv4_prod_list_code, lv4_prod_list_cn_name, lv4_prod_list_code AS rank
            </when>
            <when test='groupLevel=="SPART"'>
                ,top_spart_code, top_spart_cn_name, top_spart_code AS rank
            </when>
        </choose>
        FROM fin_dm_opt_foi.dm_fcst_price_top_spart_info_t
        WHERE del_flag = 'N'
        <if test='versionId != null'>
            AND version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            AND view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            AND bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel=="OS"'>
            AND oversea_flag IS NOT NULL
        </if>
        <if test='groupLevel=="DT"'>
            AND sign_top_cust_category_code IS NOT NULL
        </if>
        <if test='groupLevel=="SN"'>
            AND sign_subsidiary_custcatg_cn_name IS NOT NULL
        </if>
        <if test='overseaFlag != null and overseaFlag != ""'>
            AND oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            AND region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            AND repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='signTopCustCategoryCode != null and signTopCustCategoryCode != ""'>
            AND sign_top_cust_category_code = #{signTopCustCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='signSubsidiaryCustcatgCnName != null and signSubsidiaryCustcatgCnName != ""'>
            AND sign_subsidiary_custcatg_cn_name = #{signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
        </if>
        <if test='lv1ProdListCode != null and lv1ProdListCode != ""'>
            AND lv1_prod_list_code = #{lv1ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='lv2ProdListCode != null and lv2ProdListCode != ""'>
            AND lv2_prod_list_code = #{lv2ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='lv3ProdListCode != null and lv3ProdListCode != ""'>
            AND lv3_prod_list_code = #{lv3ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='lv4ProdListCode != null and lv4ProdListCode != ""'>
            AND lv4_prod_list_code = #{lv4ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='topSpartCode != null and topSpartCode != ""'>
            AND top_spart_code = #{topSpartCode,jdbcType=VARCHAR}
        </if>
        ORDER BY rank
    </select>

</mapper>
