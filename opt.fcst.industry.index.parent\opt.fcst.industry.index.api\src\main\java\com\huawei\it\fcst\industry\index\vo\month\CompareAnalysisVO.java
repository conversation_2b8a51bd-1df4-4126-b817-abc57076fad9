/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.month;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * 对比分析VO
 *
 * <AUTHOR>
 * @since 2023/12/20
 */

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CompareAnalysisVO {

    private Boolean isCompareFlag;

    List<MonthAnalysisVO> compareMonthVO;
}
