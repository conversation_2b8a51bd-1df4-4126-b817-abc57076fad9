/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.mqs;

import com.huawei.his.mqs.common.exception.UmpException;
import com.huawei.his.mqs.common.message.Message;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.security.UserVO;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * MessageProducerTest Class
 *
 * <AUTHOR>
 * @since 2023/7/17
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class MessageProducerTest {
    private static final Logger LOGGER = LoggerFactory.getLogger(MessageProducer.class);

    @InjectMocks
    private MessageProducer messageProducer;

    @Before
    public void before() throws Exception {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);
    }

    @Test
    public void startProducer() throws Exception {
        try {
            messageProducer.startProducer();
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNull(null);
    }

    @Test
    public void sendMessage() {
        Message message=new Message();
        message.setTags("T");
        boolean b = messageProducer.sendMessage(message);
        Assert.assertFalse(b);
    }

    @Test
    public void stopProducer() throws UmpException {
        messageProducer.stopProducer();
        Assertions.assertNull(null);
    }
}