/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.service.notice;

import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocNoticeInfoDTO;
import com.huawei.it.fcst.industry.index.vo.notice.NoticeInfoVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.cxf.jaxrs.ext.multipart.Multipart;

import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.FormParam;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;

/**
 * <AUTHOR>
 * @since 2024/2/19
 */
@Path("notice")
@Consumes(MediaType.APPLICATION_JSON + ";charset=UTF-8")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface INoticeInfoService {

    /**
     * 公告分页查询 列表信息查询
     * @param noticeInfoVO NoticeInfoVO
     * @return 列表信息查询
     */
    @Path("/infoList")
    @POST
    ResultDataVO findNoticeInfoListByPage(NoticeInfoVO noticeInfoVO);

    /**
     * 公告详情查看
     * @param id Long
     * @return 列表信息查询
     */
    @Path("/infoDetail")
    @GET
    ResultDataVO findNoticeInfoDetail(@QueryParam("id") Long id);

    /**
     * 公告详情查看
     * @param  noticeInfoVO NoticeInfoVO
     * @return 列表信息查询
     */
    @Path("/fingNoticeByType")
    @POST
    ResultDataVO fingNoticeByType(NoticeInfoVO noticeInfoVO);

    /**
     * 公告新增
     *
     * @param attachment attachment
     * @param noticeType String
     * @return
     * @throws CommonApplicationException
     */
    @Path("/saveInfo")
    @POST
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    ResultDataVO saveNoticeInfo(@Multipart("files") Attachment attachment, @FormParam("noticeType") String noticeType, @FormParam("noticeTitle")String noticeTitle, @FormParam("noticeTag") String noticeTag, @FormParam("fileType")String fileType, @FormParam("isHistory")String isHistory) throws CommonApplicationException;


    /**
     *
     * @param dmFocNoticeInfoDTO
     * @return
     * @throws CommonApplicationException
     */
    @Path("/saveContentInfo")
    @POST
    ResultDataVO saveNoticeContentInfo(DmFocNoticeInfoDTO dmFocNoticeInfoDTO) throws CommonApplicationException;

    /**
     * [删除公告信息]
     *
     * @param id 编码
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @DELETE
    @Path("/delete/{id}")
    ResultDataVO deleteNoticeInfo(@PathParam("id") Long id);
}
