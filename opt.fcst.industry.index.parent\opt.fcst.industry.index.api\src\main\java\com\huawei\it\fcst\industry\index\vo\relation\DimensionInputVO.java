/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.relation;

import com.huawei.it.fcst.industry.index.vo.common.TableNameVO;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * DimensionInputVO Class
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "配置管理页面维度关系入参")
public class DimensionInputVO extends TableNameVO implements Serializable {
    private static final long serialVersionUID = 4682328884171838915L;

    /**
     *
     * 专项采购认证部名称
     */
    private String l3CegCnName;

    /**
     *
     * 专项采购认证部编码
     */
    private String l3CegCode;

    /**
     *
     * 模块名称
     */
    private String l4CegCnName;

    /**
     *
     * 模块编码（Group LV4）
     */
    private String l4CegCode;

    /**
     *
     * 品类编码
     */
    private String categoryCode;

    /**
     *
     * 品类名称
     */
    private String categoryName;

    /**
     *
     * 版本号
     */
    private Long  versionId;

    /**
     *
     *  页码
     */
    private Integer pageIndex;

    /**
     *
     *  一页数量
     */
    private Integer pageSize;

    /**
     *
     *  总条数
     */
    private Integer totalSize;


    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<DmDimCatgModlCegIctVO> dimCatgModlCegIctList;

}
