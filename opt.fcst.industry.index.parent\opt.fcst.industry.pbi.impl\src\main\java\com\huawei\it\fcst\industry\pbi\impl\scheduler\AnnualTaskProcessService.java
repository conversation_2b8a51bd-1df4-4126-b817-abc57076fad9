/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.scheduler;

import com.huawei.it.fcst.config.ConfigUtil;
import com.huawei.it.fcst.enums.GroupTaskType;
import com.huawei.it.fcst.industry.pbi.dao.IDmVirtualizedTaskDao;
import com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstBaseCusDimVO;
import com.huawei.it.jalor5.core.annotation.NoJalorTransation;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.io.Serializable;

import javax.inject.Inject;

/**
 * 年度func执行
 *
 * <AUTHOR>
 * @since 202407
 */
@Slf4j
@Service("task.annualTaskProcessService")
public class AnnualTaskProcessService extends AbstractTaskProcessService {
    @Inject
    private IDmVirtualizedTaskDao virtualizedTaskDao;
    @Override
    public GroupTaskType getTaskType() {
        return GroupTaskType.ANNUAL;
    }

    @Override
    @NoJalorTransation
    public Boolean process(Serializable serializable,Serializable beforeResult) {
        DmFcstBaseCusDimVO dmFcstBaseCusDimVO = (DmFcstBaseCusDimVO)serializable;
        log.info("AnnualTaskProcessService:start->{},虚化id:{}",dmFcstBaseCusDimVO.getPageType(),dmFcstBaseCusDimVO.getCustomId());
        return FUNC_STATUS_SUCCESS.equalsIgnoreCase(virtualizedTaskDao.callAnnualFuncTask(dmFcstBaseCusDimVO,
                ConfigUtil.getInstance().get16PlainText()));
    }
}
