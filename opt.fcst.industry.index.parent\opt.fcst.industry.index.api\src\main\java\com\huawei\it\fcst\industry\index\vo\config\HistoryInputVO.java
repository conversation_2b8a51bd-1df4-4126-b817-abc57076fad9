/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.config;

import com.huawei.it.fcst.industry.index.vo.common.HeaderVo;

import com.huawei.it.fcst.industry.index.vo.common.TableNameVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.LinkedList;
import java.util.List;

/**
 * HistoryInputVO Class
 *
 * <AUTHOR>
 * @since 2023/3/14
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "配置管理页面历史清单入参")
public class HistoryInputVO  extends TableNameVO {
    /**
     * 视角标识，用于区分不同视角下的品类数据(0: ICT层级, 1:ICT-LV1层级, 2:ICT-LV1-LV2层级)
     **/
    @ApiModelProperty("view_flag")
    private String viewFlag;

    /**
     * 版本id
     **/
    @ApiModelProperty("version_id")
    private Long versionId;

    /**
     * 版本
     **/
    @ApiModelProperty("version")
    private String version;

    /**
     * 国内海外标识(I:国内/O:海外/G:全球)
     **/
    @ApiModelProperty("oversea_flag")
    private String overseaFlag;

    /**
     * BG编码
     **/
    @ApiModelProperty("lv0_prod_list_code")
    private String lv0ProdListCode;

    /**
     * BG名称
     **/
    @ApiModelProperty("lv0_prod_list_cn_name")
    private String lv0ProdListCnName;

    /**
     * 重量级团队LV0编码
     **/
    @ApiModelProperty("lv0_prod_rnd_team_code")
    private String lv0ProdRndTeamCode;

    /**
     * 重量级团队LV1编码
     **/
    @ApiModelProperty("lv1_prod_rnd_team_code")
    private String lv1ProdRndTeamCode;

    /**
     * 重量级团队LV2编码
     **/
    @ApiModelProperty("lv2_prod_rnd_team_code")
    private String lv2ProdRndTeamCode;

    /**
     * 重量级团队LV3编码
     **/
    @ApiModelProperty("lv3_prod_rnd_team_code")
    private String lv3ProdRndTeamCode;

    /**
     * 重量级团队LV4编码
     **/
    @ApiModelProperty("lv4_prod_rnd_team_code")
    private String lv4ProdRndTeamCode;

    /**
     * 专家团编码
     **/
    @ApiModelProperty("top_l3_ceg_code")
    private String topL3CegCode;

    /**
     * 模块编码
     **/
    @ApiModelProperty("top_l4_ceg_code")
    private String topL4CegCode;

    /**
     * 品类编码
     **/
    @ApiModelProperty("top_category_code")
    private String topCategoryCode;

    @ApiModelProperty(value = "维度类型（U：通用/ P盈利颗粒度/ D量纲颗粒度）")
    private String granularityType;

    @ApiModelProperty(value = "业务口径（R:收入时点/C：发货成本）")
    private String caliberFlag;

    /**
     *  区分模型（Top品类,规格品)
     */
    private String modelType;

    /**
     *  量纲编码
     */
    private String dimensionCode;

    /**
     *  量纲子类编码
     */
    private String dimensionSubCategoryCode;

    /**
     *  量纲子类明细编码
     */
    private String dimensionSubDetailCode;

    /**
     *  spart层级编码
     */
    private String spartCode;

    /**
     *
     *  页码
     */
    private Integer pageIndex;

    /**
     *
     *  一页数量
     */
    private Integer pageSize;

    /**
     *
     *  总条数
     */
    private Integer totalSize;

    private String fileName;

    /**
     *
     *  导出的category表头
     */
    List<HeaderVo> topCateHeader = new LinkedList<>();

    /**
     *
     *  导出的item表头
     */
    List<HeaderVo> topItemHeader = new LinkedList<>();

    /**
     * 盈利颗粒度L1
     **/
    private String l1Name;

    /**
     * 盈利颗粒度L2
     **/
    private String l2Name;

    private String coaCode;

    /**
     * 发货对象编码
     **/
    @ApiModelProperty("top_shipping_object_code")
    private String topShippingObjectCode;

    /**
     * 制造对象编码
     **/
    @ApiModelProperty("top_manufacture_object_code")
    private String topManufactureObjectCode;

    /**
     * 成本类型
     **/
    private String costType;

    private Integer allCount;
}
