<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IDmDimCatgModlCegIctDao">
    <resultMap type="com.huawei.it.fcst.industry.index.vo.relation.DmDimCatgModlCegIctVO" id="resultMap">
        <result property="delFlag" column="del_flag"/>
        <result property="categoryCnName" column="category_cn_name"/>
        <result property="categoryCode" column="category_code"/>
        <result property="l3CegCnName" column="l3_ceg_cn_name"/>
        <result property="l3CegCode" column="l3_ceg_code"/>
        <result property="l3CegShortCnName" column="l3_ceg_short_cn_name"/>
        <result property="l4CegCnName" column="l4_ceg_cn_name"/>
        <result property="l4CegCode" column="l4_ceg_code"/>
        <result property="l4CegShortCnName" column="l4_ceg_short_cn_name"/>
        <result property="creationDate" column="creation_date"/>
        <result property="createdBy" column="created_by"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdatedDate" column="last_updated_date"/>
        <result property="versionId" column="version_id"/>
    </resultMap>

    <resultMap type="com.huawei.it.fcst.industry.index.vo.relation.DmDimMaterialCodeVO" id="resultMaterialMap">
        <result property="itemSubtypeCnName" column="item_subtype_cn_name"/>
        <result property="itemSubtypeCode" column="item_subtype_code"/>
    </resultMap>

    <sql id="allFields">
        del_flag,
        category_cn_name,
        category_code,
        l3_ceg_code,
        l3_ceg_cn_name,
        l3_ceg_short_cn_name,
        l4_ceg_code,
        l4_ceg_cn_name,
        l4_ceg_short_cn_name,
        creation_date,
        created_by,
        last_updated_by,
        last_updated_date,
        version_id
    </sql>

    <sql id="allValues">
        #{delFlag,jdbcType=VARCHAR},
        #{categoryCnName,jdbcType=VARCHAR},
        #{categoryCode,jdbcType=VARCHAR},
        #{l3CegCnName,jdbcType=VARCHAR},
        #{l3CegShortCnName,jdbcType=VARCHAR},
        #{creationDate,jdbcType=TIMESTAMP},
        #{createdBy,jdbcType=VARCHAR},
        #{lastUpdatedBy,jdbcType=VARCHAR},
        #{lastUpdatedDate,jdbcType=TIMESTAMP},
        #{versionId,jdbcType=NUMERIC}
    </sql>

    <sql id="setValues">
        <if test='delFlag != null'>
            del_flag = #{delFlag,jdbcType=VARCHAR},
        </if>
        <if test='categoryCnName != null'>
            category_cn_name = #{categoryCnName,jdbcType=VARCHAR},
        </if>
        <if test='categoryCode != null'>
            category_code = #{categoryCode,jdbcType=VARCHAR},
        </if>
        <if test='l3CegCnName != null'>
            l3_ceg_cn_name = #{l3CegCnName,jdbcType=VARCHAR},
        </if>
        <if test='l3CegShortCnName != null'>
            l3_ceg_short_cn_name = #{l3CegShortCnName,jdbcType=VARCHAR},
        </if>
        <if test='creationDate != null'>
            creation_date = #{creationDate,jdbcType=TIMESTAMP},
        </if>
        <if test='createdBy != null'>
            created_by = #{createdBy,jdbcType=VARCHAR},
        </if>
        <if test='lastUpdatedBy != null'>
            last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
        </if>
        <if test='lastUpdatedDate != null'>
            last_updated_date = #{lastUpdatedDate,jdbcType=TIMESTAMP},
        </if>
        <if test='versionId != null'>
            version_id = #{versionId,jdbcType=NUMERIC},
        </if>
    </sql>

    <sql id="searchFields">
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
                AND del_flag = 'N'
            <if test='_parameter.get("0").categoryCnName != null and _parameter.get("0").categoryCnName != ""'>
                AND category_cn_name = #{0.categoryCnName,jdbcType=VARCHAR}
            </if>
            <if test='_parameter.get("0").categoryCode != null and _parameter.get("0").categoryCode != ""'>
                AND category_code = #{0.categoryCode,jdbcType=VARCHAR}
            </if>
            <if test='_parameter.get("0").l3CegCnName != null and _parameter.get("0").l3CegCnName != ""'>
                AND l3_ceg_cn_name = #{0.l3CegCnName,jdbcType=VARCHAR}
            </if>
            <if test='_parameter.get("0").l3CegShortCnName != null'>
                AND l3_ceg_short_cn_name = #{0.l3CegShortCnName,jdbcType=VARCHAR}
            </if>
            <if test='_parameter.get("0").creationDate != null'>
                AND creation_date = #{0.creationDate,jdbcType=TIMESTAMP}
            </if>
            <if test='_parameter.get("0").createdBy != null'>
                AND created_by = #{0.createdBy,jdbcType=VARCHAR}
            </if>
            <if test='_parameter.get("0").lastUpdatedBy != null'>
                AND last_updated_by = #{0.lastUpdatedBy,jdbcType=VARCHAR}
            </if>
            <if test='_parameter.get("0").lastUpdatedDate != null'>
                AND last_updated_date = #{0.lastUpdatedDate,jdbcType=TIMESTAMP}
            </if>
            <if test='_parameter.get("0").versionId != null'>
                AND version_id = #{0.versionId,jdbcType=NUMERIC}
            </if>
        </trim>
    </sql>


    <sql id="searchField">
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            AND del_flag = 'N'
            <if test='categoryCnName != null'>
                AND category_cn_name = #{categoryCnName,jdbcType=VARCHAR}
            </if>
            <if test='categoryCode != null'>
                AND category_code = #{categoryCode,jdbcType=VARCHAR}
            </if>
            <if test='l3CegCnName != null'>
                AND l3_ceg_cn_name = #{l3CegCnName,jdbcType=VARCHAR}
            </if>
            <if test='l3CegShortCnName != null'>
                AND l3_ceg_short_cn_name = #{l3CegShortCnName,jdbcType=VARCHAR}
            </if>
            <if test='l3CegCode != null'>
                AND l3_ceg_code = #{l3CegCode,jdbcType=VARCHAR}
            </if>
            <if test='l4CegCnName != null'>
                AND l4_ceg_cn_name = #{l4CegCnName,jdbcType=VARCHAR}
            </if>
            <if test='l4CegShortCnName != null'>
                AND l4_ceg_short_cn_name = #{l4CegShortCnName,jdbcType=VARCHAR}
            </if>
            <if test='l4CegCode != null'>
                AND l4_ceg_code = #{l4CegCode,jdbcType=VARCHAR}
            </if>
            <if test='creationDate != null'>
                AND creation_date = #{creationDate,jdbcType=TIMESTAMP}
            </if>
            <if test='createdBy != null'>
                AND created_by = #{createdBy,jdbcType=VARCHAR}
            </if>
            <if test='lastUpdatedBy != null'>
                AND last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR}
            </if>
            <if test='lastUpdatedDate != null'>
                AND last_updated_date = #{lastUpdatedDate,jdbcType=TIMESTAMP}
            </if>
            <if test='versionId != null'>
                AND version_id = #{versionId,jdbcType=NUMERIC}
            </if>
        </trim>
    </sql>

    <select id="findDmDimCatgModlCegIctList" resultMap="resultMap">
        SELECT 
        <include refid="allFields"/>
        FROM fin_dm_opt_foi.${tablePreFix}_catg_ceg_ict_d
        <include refid="searchField"/>
    </select>

    <select id="findExportCatgModlCegIctList" resultType="java.util.LinkedHashMap">
        SELECT
        l3_ceg_cn_name,
        l3_ceg_short_cn_name,
        l3_ceg_code,
        l4_ceg_cn_name,
        l4_ceg_short_cn_name,
        l4_ceg_code,
        category_code,
        category_cn_name
        FROM fin_dm_opt_foi.${tablePreFix}_catg_ceg_ict_d
        <include refid="searchField"/>
    </select>

    <select id="findDimMaterialCodeD"  resultMap="resultMaterialMap">
        SELECT DISTINCT
        item_subtype_code,
        item_subtype_cn_name
        FROM
        dwrdim.dwr_dim_material_d
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            del_flag = 'N'
            <if test='itemSubtypeCode != null and itemSubtypeCode != ""'>
                item_subtype_code = #{itemSubtypeCode,jdbcType=VARCHAR}
            </if>
            <if test='itemSubtypeCnName != null and itemSubtypeCnName != ""'>
                item_subtype_cn_name = #{itemSubtypeCnName,jdbcType=VARCHAR}
            </if>
            <if test="keyword != null and keyword != ''">
                AND INSTR(UPPER(item_subtype_cn_name),UPPER(#{keyword}))>0
            </if>
        </trim>
        ORDER BY 1
    </select>

    <select id="findDimCegCodeD" resultMap="resultMap">
        SELECT  DISTINCT l3_ceg_code,l3_ceg_cn_name,l4_ceg_code,l4_ceg_cn_name
        FROM dmdim.dm_dim_ceg_d
        WHERE del_flag = 'N'
    </select>

    <select id="findCatgCegIctList" resultType="java.util.Map">
        SELECT DISTINCT
            n1.category_code,
            n1.category_cn_name
        FROM
            fin_dm_opt_foi.${tablePreFix}_catg_ceg_ict_d n1
        LEFT JOIN dwrdim.dwr_dim_material_d n2
        ON n1.category_code = n2.item_subtype_code
        WHERE
            n1.del_flag = 'N'
          AND n2.item_subtype_cn_name = '源为空'
    </select>

    <select id="findCatgModlCegIctListByShortName" resultMap="resultMap">
        SELECT DISTINCT
        <include refid="allFields"/>
        FROM fin_dm_opt_foi.${tablePreFix}_catg_ceg_ict_d
        where
        del_flag ='N' and version_id = #{versionId,jdbcType=NUMERIC}
        <if test='l3CegShortCnName != null'>
            and l3_ceg_short_cn_name = #{l3CegShortCnName,jdbcType=VARCHAR}
        </if>
        <if test='l3CegCnName != null'>
            and l3_ceg_cn_name = #{l3CegCnName,jdbcType=VARCHAR}
        </if>
        <if test='l4CegShortCnName != null'>
            and l4_ceg_short_cn_name = #{l4CegShortCnName,jdbcType=VARCHAR}
        </if>
        <if test='l4CegCnName != null'>
            and l4_ceg_cn_name = #{l4CegCnName,jdbcType=VARCHAR}
        </if>
    </select>


    <insert id="createDmDimCatgModlCegIctTList" parameterType="java.util.List">
            INSERT INTO fin_dm_opt_foi.${tablePreFix}_catg_ceg_ict_d
            (<include refid="allFields"/>) 
            VALUES 
            <foreach collection="list" item="item" separator=",">
                (#{item.delFlag,jdbcType=VARCHAR},
                 #{item.categoryCnName,jdbcType=VARCHAR},
                 #{item.categoryCode,jdbcType=VARCHAR},
                 #{item.l3CegCode,jdbcType=VARCHAR},
                 #{item.l3CegCnName,jdbcType=VARCHAR},
                 #{item.l3CegShortCnName,jdbcType=VARCHAR},
                 #{item.l4CegCode,jdbcType=VARCHAR},
                 #{item.l4CegCnName,jdbcType=VARCHAR},
                 #{item.l4CegShortCnName,jdbcType=VARCHAR},
                 #{item.creationDate,jdbcType=TIMESTAMP},
                 #{item.createdBy,jdbcType=VARCHAR},
                 #{item.lastUpdatedBy,jdbcType=VARCHAR},
                 #{item.lastUpdatedDate,jdbcType=TIMESTAMP},
                 #{item.versionId,jdbcType=NUMERIC})
            </foreach> 
    </insert>

    <update id="updateL3ShortName" parameterType="com.huawei.it.fcst.industry.index.vo.relation.DmDimCatgModlCegIctVO">
        UPDATE fin_dm_opt_foi.${tablePreFix}_catg_ceg_ict_d
        <set>
            <if test='l3CegShortCnName != null'>
                l3_ceg_short_cn_name = #{l3CegShortCnName,jdbcType=VARCHAR},
            </if>
            <if test='lastUpdatedBy != null'>
                last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
            </if>
            <if test='lastUpdatedDate != null'>
                last_updated_date = #{lastUpdatedDate,jdbcType=TIMESTAMP},
            </if>
        </set>
        WHERE
        version_id = #{versionId,jdbcType=NUMERIC}
        and l3_ceg_code = #{l3CegCode,jdbcType=VARCHAR}
    </update>

    <update id="updateL4ShortName" parameterType="com.huawei.it.fcst.industry.index.vo.relation.DmDimCatgModlCegIctVO">
        UPDATE fin_dm_opt_foi.${tablePreFix}_catg_ceg_ict_d
        <set>
            <if test='l4CegShortCnName != null'>
                l4_ceg_short_cn_name = #{l4CegShortCnName,jdbcType=VARCHAR},
            </if>
            <if test='lastUpdatedBy != null'>
                last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
            </if>
            <if test='lastUpdatedDate != null'>
                last_updated_date = #{lastUpdatedDate,jdbcType=TIMESTAMP},
            </if>
        </set>
        WHERE
        version_id = #{versionId,jdbcType=NUMERIC}
        and l4_ceg_code = #{l4CegCode,jdbcType=VARCHAR}
    </update>

    <update id="updateL3AndL4" parameterType="com.huawei.it.fcst.industry.index.vo.relation.DmDimCatgModlCegIctVO">
        UPDATE fin_dm_opt_foi.${tablePreFix}_catg_ceg_ict_d
        <set>
            <if test='l3CegCnName != null'>
                l3_ceg_cn_name = #{l3CegCnName,jdbcType=VARCHAR},
            </if>
            <if test='l3CegShortCnName != null'>
                l3_ceg_short_cn_name = #{l3CegShortCnName,jdbcType=VARCHAR},
            </if>
            <if test='l3CegCode != null'>
                l3_ceg_code = #{l3CegCode,jdbcType=VARCHAR},
            </if>
            <if test='l4CegCnName != null'>
                l4_ceg_cn_name = #{l4CegCnName,jdbcType=VARCHAR},
            </if>
            <if test='l4CegShortCnName != null'>
                l4_ceg_short_cn_name = #{l4CegShortCnName,jdbcType=VARCHAR},
            </if>
            <if test='l4CegCode != null'>
                l4_ceg_code = #{l4CegCode,jdbcType=VARCHAR},
            </if>
            <if test='categoryCnName != null'>
                category_cn_name = #{categoryCnName,jdbcType=VARCHAR},
            </if>
            <if test='categoryCode != null'>
                category_code = #{categoryCode,jdbcType=VARCHAR},
            </if>
            <if test='lastUpdatedBy != null'>
                last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
            </if>
            <if test='lastUpdatedDate != null'>
                last_updated_date = #{lastUpdatedDate,jdbcType=TIMESTAMP},
            </if>
        </set>
        WHERE
        version_id = #{versionId,jdbcType=NUMERIC}
        and category_code = #{oldCategoryCode,jdbcType=VARCHAR}
    </update>

    <update id="updateCateByVo" parameterType="com.huawei.it.fcst.industry.index.vo.relation.DmDimCatgModlCegIctVO">
        UPDATE fin_dm_opt_foi.${tablePreFix}_catg_ceg_ict_d
        <set>
            <if test='l3CegCnName != null'>
                l3_ceg_cn_name = #{l3CegCnName,jdbcType=VARCHAR},
            </if>
            <if test='l3CegShortCnName != null'>
                l3_ceg_short_cn_name = #{l3CegShortCnName,jdbcType=VARCHAR},
            </if>
            <if test='l3CegCode != null'>
                l3_ceg_code = #{l3CegCode,jdbcType=VARCHAR},
            </if>
            <if test='l4CegCnName != null'>
                l4_ceg_cn_name = #{l4CegCnName,jdbcType=VARCHAR},
            </if>
            <if test='l4CegShortCnName != null'>
                l4_ceg_short_cn_name = #{l4CegShortCnName,jdbcType=VARCHAR},
            </if>
            <if test='l4CegCode != null'>
                l4_ceg_code = #{l4CegCode,jdbcType=VARCHAR},
            </if>
            <if test='categoryCnName != null'>
                category_cn_name = #{categoryCnName,jdbcType=VARCHAR},
            </if>
            <if test='categoryCode != null'>
                category_code = #{categoryCode,jdbcType=VARCHAR},
            </if>
            <if test='lastUpdatedBy != null'>
                last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
            </if>
            <if test='lastUpdatedDate != null'>
                last_updated_date = #{lastUpdatedDate,jdbcType=TIMESTAMP},
            </if>
        </set>
        WHERE
        version_id = #{versionId,jdbcType=NUMERIC}
        and category_code = #{oldCategoryCode,jdbcType=VARCHAR}
    </update>

    <delete id="deleteDmDimCatgModlCegIctT" >
        DELETE 
        FROM fin_dm_opt_foi.${tablePreFix}_catg_ceg_ict_d
        WHERE
        version_id=#{versionId,jdbcType=NUMERIC}
    </delete>
</mapper>
