/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.utils;

import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.security.RoleVO;
import com.huawei.it.jalor5.security.UserVO;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * UserInfoUtilsTest Class
 *
 * <AUTHOR>
 * @since 2023/4/22
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({RequestContext.class})
public class UserInfoUtilsTest {

    @InjectMocks
    private UserInfoUtils userInfoUtils;

    @Mock
    private IRequestContext requestContext;

    private static final Logger LOGGER = LoggerFactory.getLogger(UserInfoUtilsTest.class);

    @Test
    public void getCurrentEmployeeNo() {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);

        String currentEmployeeNo = userInfoUtils.getCurrentEmployeeNo();
        Assertions.assertNotNull(currentEmployeeNo);
    }

    @Test
    public void getCurrentEmployeeNo2T() throws Exception {
        MockitoAnnotations.openMocks(this);
        UserVO user = null;
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);
        try {
            String currentEmployeeNo = userInfoUtils.getCurrentEmployeeNo();
        } catch (Exception exception) {
            LOGGER.error("获取用户为空");
        }
        Assertions.assertNull(null);
    }

    @Test
    public void getUserId() {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);

        Long userId = userInfoUtils.getUserId();
        Assertions.assertNotNull(userId);
    }

    @Test
    public void getUserId2T() {
        MockitoAnnotations.openMocks(this);
        UserVO user = null;
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);

        try {
            Long userId = userInfoUtils.getUserId();
        } catch (Exception exception) {
            LOGGER.error("获取用户为空");
        }
        Assertions.assertNull(null);
    }

    @Test
    public void getUserCn() {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);

        String userCn = userInfoUtils.getUserCn();
        Assertions.assertNotNull(userCn);
    }

    @Test
    public void getUserCn2T() {
        MockitoAnnotations.openMocks(this);
        UserVO user = null;
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);

        String userCn = null;
        try {
            userCn = userInfoUtils.getUserCn();
        } catch (Exception exception) {
            LOGGER.error("获取用户为空");
        }
        Assertions.assertNull(userCn);
    }

    @Test
    public void getUserAccount() {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);

        String userAccount = userInfoUtils.getUserAccount();
        Assertions.assertNotNull(userAccount);
    }

    @Test
    public void getUserAccount2T() {
        MockitoAnnotations.openMocks(this);
        UserVO user = null;
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);

        try {
            String userAccount = userInfoUtils.getUserAccount();
        } catch (Exception exception) {
            LOGGER.error("获取用户为空");
        }
        Assertions.assertNull(null);
    }

    @Test
    public void getCurrentUser() {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);

        UserVO userVO = userInfoUtils.getCurrentUser();
        Assertions.assertNotNull(userVO);
    }

    @Test
    public void getUserValidRoleIds() {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        List<RoleVO> validRoles = new ArrayList<>();
        RoleVO roleVO = new RoleVO();
        roleVO.setRoleId(1);
        validRoles.add(roleVO);
        user.setValidRoles(validRoles);
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);

        Set<String> userValidRoleIds = userInfoUtils.getUserValidRoleIds();

        Assertions.assertNotNull(userValidRoleIds);
    }

    @Test
    public void getUserValidRoleIds2T() {
        MockitoAnnotations.openMocks(this);
        UserVO user = null;
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);

        Set<String> userValidRoleIds = null;
        try {
            userValidRoleIds = userInfoUtils.getUserValidRoleIds();
        } catch (Exception exception) {
            LOGGER.error("获取用户为空");
        }
        Assertions.assertNull(userValidRoleIds);
    }

    @Test
    public void getUserValidRoleNames() {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        List<RoleVO> validRoles = new ArrayList<>();
        RoleVO roleVO = new RoleVO();
        roleVO.setRoleId(1);
        roleVO.setRoleName("name1");
        validRoles.add(roleVO);
        user.setValidRoles(validRoles);
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);

        Set<String> userValidRoleNames = userInfoUtils.getUserValidRoleNames();

        Assertions.assertNotNull(userValidRoleNames);
    }

    @Test
    public void getUserValidRoleNames2T() {
        MockitoAnnotations.openMocks(this);
        UserVO user = null;
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);

        Set<String> userValidRoleNames = null;
        try {
            userValidRoleNames = userInfoUtils.getUserValidRoleNames();
        } catch (Exception exception) {
            LOGGER.error("获取用户为空");
        }

        Assertions.assertNull(userValidRoleNames);
    }

    @Test
    public void getUserRoleInfo() {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        List<RoleVO> validRoles = new ArrayList<>();
        RoleVO roleVO = new RoleVO();
        roleVO.setRoleId(1);
        roleVO.setRoleName("name1");
        validRoles.add(roleVO);
        user.setValidRoles(validRoles);
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);

        Map<Integer, String> userRoleInfo = userInfoUtils.getUserRoleInfo();

        Assertions.assertNotNull(userRoleInfo);
    }

    @Test
    public void getUserRoleInfo2T() {
        MockitoAnnotations.openMocks(this);
        UserVO user = null;
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);

        Map<Integer, String> userRoleInfo = null;
        try {
            userRoleInfo = userInfoUtils.getUserRoleInfo();
        } catch (Exception exception) {
            LOGGER.error("获取用户为空");
        }

        Assertions.assertNull(userRoleInfo);
    }

    @Test
    public void getUserEmail() {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        user.setEmail("<EMAIL>");
        List<RoleVO> validRoles = new ArrayList<>();
        RoleVO roleVO = new RoleVO();
        roleVO.setRoleId(1);
        roleVO.setRoleName("name1");
        validRoles.add(roleVO);
        user.setValidRoles(validRoles);
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);

        String userEmail = userInfoUtils.getUserEmail();

        Assertions.assertNotNull(userEmail);
    }

    @Test
    public void getUserEmail2T() {
        MockitoAnnotations.openMocks(this);
        UserVO user = null;
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);

        String userEmail = null;
        try {
            userEmail = userInfoUtils.getUserEmail();
        } catch (Exception exception) {
            LOGGER.error("获取用户为空");
        }

        Assertions.assertNull(userEmail);
    }

}