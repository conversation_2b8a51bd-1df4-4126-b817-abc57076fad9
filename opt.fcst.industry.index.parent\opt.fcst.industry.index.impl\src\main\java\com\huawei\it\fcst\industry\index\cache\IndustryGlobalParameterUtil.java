/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 *
 */

package com.huawei.it.fcst.industry.index.cache;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.huawei.it.jalor5.lookup.LookupItemVO;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024年03月22日
 */
public class IndustryGlobalParameterUtil {
    private volatile static Map<String, List<LookupItemVO>> LOOKUP_CONFIG = new ConcurrentHashMap<>();

    /**
     * 存放lookup对象
     *
     * @param value lookup对象
     */
    public static void putAllLookupValue(Map<String, List<LookupItemVO>> value) {
        LOOKUP_CONFIG.putAll(value);
    }

    /**
     * 清理lookup值
     */
    public static void cleanLookupValue() {
        LOOKUP_CONFIG.clear();
    }


    /**
     * lookup根据itemCode 获取lookup值
     *
     * @return lookup List
     */
    public static List<LookupItemVO> findViewListByItemCode(String itemCode) {
        if (LOOKUP_CONFIG.get(itemCode) != null) {
            return LOOKUP_CONFIG.get(itemCode);
        }
        return Collections.EMPTY_LIST;
    }

}
