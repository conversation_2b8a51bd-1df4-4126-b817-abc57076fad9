/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.enums;

/**
 * Group层级枚举类
 *
 * <AUTHOR>
 * @since 2023/3/21
 */
public enum GroupLevelEnumMadeP {
    // Group层级（LV0：LV0、LV1：重量级团队LV1、LV2: 重量级团队LV2、SHIPPING_OBJECT：发货对象、MANUFACTURE_OBJECT：制造对象、item：规格品）
    LV0("LV0", "LV0"),
    LV1("LV1", "重量级团队LV1"),
    LV2("LV2", "重量级团队LV2"),
    L1("L1", "颗粒度L1"),
    L2("L2", "颗粒度L2"),
    SHIPPING_OBJECT("SHIPPING_OBJECT", "发货对象"),
    MANUFACTURE_OBJECT("MANUFACTURE_OBJECT", "制造对象"),
    ITEM("ITEM", "ITEM");

    private String value;
    private String name;

    GroupLevelEnumMadeP(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据key获取对应的实例
     *
     * @param key group level
     * @return GroupLevelEnumU
     */
    public static GroupLevelEnumMadeP getInstance(String key) {
        for (GroupLevelEnumMadeP value : GroupLevelEnumMadeP.values()) {
            if (value.getValue().equalsIgnoreCase(key)) {
                return value;
            }
        }
        return null;
    }
}