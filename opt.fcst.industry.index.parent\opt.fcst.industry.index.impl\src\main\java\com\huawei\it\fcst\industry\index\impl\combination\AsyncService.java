/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.combination;

import com.alibaba.ttl.threadpool.TtlExecutors;
import com.huawei.it.fcst.industry.index.dao.IDmFocDataRefreshStatusDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocProcurementReviewDao;
import com.huawei.it.fcst.industry.index.impl.config.ConfigExportService;
import com.huawei.it.fcst.industry.index.impl.month.MonthExportService;
import com.huawei.it.fcst.industry.index.thread.CombThread;
import com.huawei.it.fcst.industry.index.utils.ObjectCopyUtil;
import com.huawei.it.fcst.industry.index.vo.combination.CombTransformVO;
import com.huawei.it.fcst.industry.index.vo.combination.CombinationVO;
import com.huawei.it.fcst.industry.index.vo.combination.DmCustomCombVO;
import com.huawei.it.fcst.industry.index.vo.combination.DmFocDataRefreshStatus;
import com.huawei.it.fcst.industry.index.vo.common.CommonViewVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;
import com.huawei.it.fcst.industry.index.vo.config.HistoryInputVO;
import com.huawei.it.fcst.industry.index.vo.config.ProcurementBottomVO;
import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.annotation.NoJalorTransation;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.request.IRequestContext;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Named;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * AsyncService Class
 *
 * <AUTHOR>
 * @since 2023/8/8
 */
@EnableAsync
@Named(value = "asyncService")
@JalorResource(code = "asyncService", desc = "异步处理服务")
public class AsyncService {
    private static final Logger LOGGER = LogManager.getLogger(AsyncService.class);

    @Autowired
    private IDmFocDataRefreshStatusDao dataRefreshStatusDao;

    @Autowired
    private IDmFocProcurementReviewDao procurementReviewDao;

    @Autowired
    private CustomService customService;

    @Autowired
    private MonthExportService monthExportService;

    @Autowired
    private CustomCommonService customCommonService;

    @Autowired
    private ConfigExportService configExportService;

    protected final static ExecutorService EXECUTOR_SERVICE;

    static {
        ThreadPoolExecutor pool = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors() * 10, Runtime.getRuntime().availableProcessors() * 30, 60,
                TimeUnit.SECONDS, new LinkedBlockingQueue<>(Runtime.getRuntime().availableProcessors() * 200),
                new ThreadPoolExecutor.CallerRunsPolicy());
        // 如果设置为true,当任务执行完后，所有的线程在指定的空闲时间后，poolSize会为0
        // 如果不设置，或者设置为false，那么，poolSize会保留为核心线程的数量
        pool.allowCoreThreadTimeOut(true);
        EXECUTOR_SERVICE = TtlExecutors.getTtlExecutorService(pool);
    }

    private static final String TASK_FAIL = "TASK_FAIL";
    private static final String TASK_SUCCESS = "TASK_SUCCESS";

    @Async("asyncServiceExecutor")
    @NoJalorTransation
    public void asyncCreateCustom(List<DmCustomCombVO> customVOList, CombTransformVO combTransformVO, List<DmCustomCombVO> otherCustomVOList) {
        final long millis = System.currentTimeMillis();
        final Date now = new Date(millis);
        Long taskId = combTransformVO.getTaskId();
        Long userId = combTransformVO.getUserId();
        DmFocDataRefreshStatus refreshStatus = build(userId, taskId, now);

        boolean result = false;
        try {
            customService.createCustom(customVOList, combTransformVO, otherCustomVOList);
            updateStates(refreshStatus, TASK_SUCCESS);
            dataRefreshStatusDao.updateDmFocDataRefreshStatus(refreshStatus);
            result = true;
        } catch (Exception exception) {
            LOGGER.error("create custom error: {}", taskId, exception.getMessage());
        }
        if (!result) {
            try {
                Thread.sleep(20);
            } catch (InterruptedException exception) {
                Thread.currentThread().interrupt();
            }
            try {
                retrySave(refreshStatus);
            } catch (Exception exception) {
                LOGGER.error("create custom error: {}", taskId, exception.getMessage());
            }
        }
    }

    private DmFocDataRefreshStatus build(long userId, Long taskId, Date now) {
        DmFocDataRefreshStatus dmHqPlanDataRefreshStatus = new DmFocDataRefreshStatus();
        dmHqPlanDataRefreshStatus.setTaskId(taskId);
        dmHqPlanDataRefreshStatus.setLastUpdatedBy(userId);
        dmHqPlanDataRefreshStatus.setLastUpdateDate(now);
        return dmHqPlanDataRefreshStatus;
    }

    private void updateStates(DmFocDataRefreshStatus plan, String status) {
        plan.setStatus(status);
        plan.setLastUpdateDate(new Date());
    }

    private void retrySave(DmFocDataRefreshStatus plan) {
        updateStates(plan, TASK_FAIL);
        dataRefreshStatusDao.updateDmFocDataRefreshStatus(plan);
    }


    @Async("asyncServiceExecutor")
    @NoJalorTransation
    public void asyncCombRename(CombTransformVO combTransformVO) {
        final long millis = System.currentTimeMillis();
        final Date now = new Date(millis);
        Long taskId = combTransformVO.getTaskId();
        Long userId = combTransformVO.getUserId();
        DmFocDataRefreshStatus refreshStatus = build(userId, taskId, now);

        boolean result = false;
        try {
            customService.callCombFunction(combTransformVO);
            updateStates(refreshStatus, TASK_SUCCESS);
            dataRefreshStatusDao.updateDmFocDataRefreshStatus(refreshStatus);
            result = true;
        } catch (Exception exception) {
            LOGGER.error("rename custom error: {}", taskId, exception.getMessage());
        }
        if (!result) {
            try {
                Thread.sleep(20);
            } catch (InterruptedException exception) {
                Thread.currentThread().interrupt();
            }
            try {
                retrySave(refreshStatus);
            } catch (Exception exception) {
                LOGGER.error("rename custom error: {}", taskId, exception.getMessage());
            }
        }
    }

    @Async("asyncServiceExecutor")
    @NoJalorTransation
    public void asyncCombUpdate(CombinationVO combinationVO, CombTransformVO combTransformVO) {
        final long millis = System.currentTimeMillis();
        final Date now = new Date(millis);
        Long taskId = combTransformVO.getTaskId();
        Long userId = combTransformVO.getUserId();
        DmFocDataRefreshStatus refreshStatus = build(userId, taskId, now);

        boolean result = false;
        try {
            customService.updateCombList(combinationVO, combTransformVO);
            updateStates(refreshStatus, TASK_SUCCESS);
            dataRefreshStatusDao.updateDmFocDataRefreshStatus(refreshStatus);
            result = true;
        } catch (Exception exception) {
            LOGGER.error("update custom error: {}", taskId, exception.getMessage());
        }
        if (!result) {
            try {
                Thread.sleep(20);
            } catch (InterruptedException exception) {
                Thread.currentThread().interrupt();
            }
            try {
                retrySave(refreshStatus);
            } catch (Exception exception) {
                LOGGER.error("update custom error: {}", taskId, exception.getMessage());
            }
        }
    }

    @Async("asyncServiceExecutor")
    @NoJalorTransation
    public void exportMonthData(MonthAnalysisVO monthAnalysisVO, XSSFWorkbook workbook, String groupCnName, CombTransformVO combTransformVO) {
        final long millis = System.currentTimeMillis();
        final Date now = new Date(millis);
        Long taskId = combTransformVO.getTaskId();
        Long userId = combTransformVO.getUserId();
        DmFocDataRefreshStatus refreshStatus = build(userId, taskId, now);

        boolean result = false;
        try {
            monthExportService.exportData(monthAnalysisVO, workbook, groupCnName, combTransformVO.getCurrent(), userId);
            updateStates(refreshStatus, TASK_SUCCESS);
            dataRefreshStatusDao.updateDmFocDataRefreshStatus(refreshStatus);
            result = true;
        } catch (Exception exception) {
            LOGGER.error("export month excel error: taskId={}, {}", taskId, exception.getMessage());
        }
        if (!result) {
            try {
                Thread.sleep(20);
            } catch (InterruptedException exception) {
                Thread.currentThread().interrupt();
            }
            try {
                retrySave(refreshStatus);
            } catch (Exception exception) {
                LOGGER.error("export month excel error: {}", taskId, exception.getMessage());
            }
        }
    }

    @Async("asyncServiceExecutor")
    @NoJalorTransation
    public void asyncInitData(List<DmCustomCombVO> customCombList, List<DmCustomCombVO> manufactureCustomCombList, List<DmCustomCombVO> customCombEnergyList, List<DmCustomCombVO> manufactureCustomCombEnergyList, CombinationVO combinationVO, CombTransformVO combTransformVO) {
        final long millis = System.currentTimeMillis();
        final Date now = new Date(millis);
        Long taskId = combTransformVO.getTaskId();
        Long userId = combTransformVO.getUserId();
        DmFocDataRefreshStatus refreshStatus = build(userId, taskId, now);

        boolean result = false;
        try {
            customService.asyncInitFlag(customCombList, manufactureCustomCombList, customCombEnergyList, manufactureCustomCombEnergyList, combinationVO, combTransformVO);
            updateStates(refreshStatus, TASK_SUCCESS);
            dataRefreshStatusDao.updateDmFocDataRefreshStatus(refreshStatus);
            result = true;
        } catch (Exception exception) {
            LOGGER.error("init comb is effective error: {}", taskId, exception.getMessage());
        }
        if (!result) {
            try {
                Thread.sleep(20);
            } catch (InterruptedException exception) {
                Thread.currentThread().interrupt();
            }
            try {
                retrySave(refreshStatus);
            } catch (Exception exception) {
                LOGGER.error("init comb is effective error: {}", taskId, exception.getMessage());
            }
        }
    }

    @Async("asyncServiceExecutor")
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void asyncImpactQty(ProcurementBottomVO procurementBottomVO, DmFocDataRefreshStatus dataRefreshStatus) {
        final long millis = System.currentTimeMillis();
        final Date now = new Date(millis);
        Long taskId = dataRefreshStatus.getTaskId();
        Long userId = dataRefreshStatus.getUserId();
        DmFocDataRefreshStatus refreshStatus = build(userId, taskId, now);

        boolean result = false;
        try {
            // 获取影响item数量
            ProcurementBottomVO newImpactQty = procurementReviewDao.getNewImpactQty(procurementBottomVO);
            if (null == newImpactQty) {
                newImpactQty = new ProcurementBottomVO();
            }
            newImpactQty.setCnt(newImpactQty.getCnt() == null ? 0 : newImpactQty.getCnt());
            newImpactQty.setCrmAvgCnt(newImpactQty.getCrmAvgCnt() == null ? 0 : newImpactQty.getCrmAvgCnt());
            newImpactQty.setTaskId(taskId);
            newImpactQty.setItemCode(procurementBottomVO.getItemCode());
            newImpactQty.setStartPeriod(procurementBottomVO.getStartPeriod());
            newImpactQty.setEndPeriod(procurementBottomVO.getEndPeriod());
            newImpactQty.setUserId(userId);
            newImpactQty.setRoleId(dataRefreshStatus.getRoleId());
            newImpactQty.setIndustryOrg(procurementBottomVO.getIndustryOrg());
            procurementReviewDao.insertItemImpactQty(newImpactQty);
            updateStates(refreshStatus, TASK_SUCCESS);
            dataRefreshStatusDao.updateDmFocDataRefreshStatus(refreshStatus);
            result = true;
        } catch (Exception exception) {
            LOGGER.error("item impact qty error: {}", taskId, exception.getMessage());
        }
        if (!result) {
            try {
                Thread.sleep(20);
            } catch (InterruptedException exception) {
                Thread.currentThread().interrupt();
            }
            try {
                retrySave(refreshStatus);
            } catch (Exception exception) {
                LOGGER.error("item impact qty error: {}", taskId, exception.getMessage());
            }
        }
    }

    @Async("asyncServiceExecutor")
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public Future<Boolean> getFoldGroupCodeList(CombinationVO combinationVO, List<DmCustomCombVO> customVOList, List<DmCustomCombVO> filterCustomCombVOList, IRequestContext requestContext) throws InterruptedException {
        List<DmFocViewInfoVO> allGroupCodeList = new ArrayList<>();

        CountDownLatch countDownLatch = new CountDownLatch(customVOList.size());
        for (DmCustomCombVO dmCustomCombVO : customVOList) {
            CommonViewVO commonView = ObjectCopyUtil.copy(combinationVO, CommonViewVO.class);
            commonView.setGranularityPageSymbol(commonView.getGranularityType() + "_" + commonView.getPageSymbol());
            commonView.setLv0Flag("Y");
            commonView.setLv0ProdRndTeamCode(dmCustomCombVO.getLv0ProdRndTeamCode());
            commonView.setLv1ProdRndTeamCode(dmCustomCombVO.getLv1ProdRndTeamCode());
            commonView.setLv2ProdRndTeamCode(dmCustomCombVO.getLv2ProdRndTeamCode());
            commonView.setLv3ProdRndTeamCode(dmCustomCombVO.getLv3ProdRndTeamCode());
            commonView.setLv4ProdRndTeamCode(dmCustomCombVO.getLv4ProdRndTeamCode());
            commonView.setL1Name(dmCustomCombVO.getL1Name());
            commonView.setL2Name(dmCustomCombVO.getL2Name());
            commonView.setCoaCode(dmCustomCombVO.getCoaCode());
            commonView.setDimensionCode(dmCustomCombVO.getDimensionCode());
            commonView.setDimensionSubCategoryCode(dmCustomCombVO.getDimensionSubCategoryCode());
            commonView.setDimensionSubDetailCode(dmCustomCombVO.getDimensionSubDetailCode());
            commonView.setSpartCode(dmCustomCombVO.getSpartCode());
            commonView.setL3CegCode(dmCustomCombVO.getL3CegCode());
            commonView.setL4CegCode(dmCustomCombVO.getL4CegCode());
            commonView.setShippingObjectCode(dmCustomCombVO.getShippingObjectCode());
            commonView.setGroupLevel(dmCustomCombVO.getGroupLevel());
            EXECUTOR_SERVICE.execute(new CombThread(countDownLatch, commonView, allGroupCodeList, requestContext, customCommonService));
        }
        countDownLatch.await();
        List<DmCustomCombVO> allCustomCombVOList = ObjectCopyUtil.copyList(allGroupCodeList, DmCustomCombVO.class);
        filterCustomCombVOList.addAll(allCustomCombVOList);
        return new AsyncResult<>(Boolean.TRUE);
    }

    @Async("asyncServiceExecutor")
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void fillPurchaseExportData(HistoryInputVO historyInputVO, DmFocDataRefreshStatus dataRefreshStatus, IRequestContext current) {
        LOGGER.info(">>>Begin AsyncExportService::fillPurchaseExportData");
        try {
            configExportService.purchaseExport(historyInputVO, current);
            // 更新任务状态表
            updateStates(dataRefreshStatus, TASK_SUCCESS);
            dataRefreshStatusDao.updateDmFocDataRefreshStatus(dataRefreshStatus);
        } catch (ApplicationException | IOException ex) {
            LOGGER.error("ConfigAsynExportService::fillPurchaseExportData Exception: {}", ex);
            // 更新任务状态表
            updateStates(dataRefreshStatus, TASK_FAIL);
            dataRefreshStatusDao.updateDmFocDataRefreshStatus(dataRefreshStatus);
        }
    }

    @Async("asyncServiceExecutor")
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void fillMadeExportData(HistoryInputVO historyInputVO, DmFocDataRefreshStatus dataRefreshStatus, IRequestContext current) {
        LOGGER.info(">>>Begin AsyncExportService::fillMadeExportData");
        try {
            configExportService.madeExport(historyInputVO, current);
            // 更新任务状态表
            updateStates(dataRefreshStatus, TASK_SUCCESS);
            dataRefreshStatusDao.updateDmFocDataRefreshStatus(dataRefreshStatus);
        } catch (ApplicationException | IOException ex) {
            LOGGER.error("ConfigAsynExportService::fillMadeExportData Exception: {}", ex);
            // 更新任务状态表
            updateStates(dataRefreshStatus, TASK_FAIL);
            dataRefreshStatusDao.updateDmFocDataRefreshStatus(dataRefreshStatus);
        }
    }
}
