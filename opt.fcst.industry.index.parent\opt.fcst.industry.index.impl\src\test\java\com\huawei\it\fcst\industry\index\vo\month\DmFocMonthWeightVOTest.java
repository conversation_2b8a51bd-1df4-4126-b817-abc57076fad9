/*
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.month;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

import java.sql.Timestamp;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/5/9
 */
public class DmFocMonthWeightVOTest extends BaseVOCoverUtilsTest<DmFocMonthWeightVO> {
    @Override
    protected Class<DmFocMonthWeightVO> getTClass() {
        return DmFocMonthWeightVO.class;
    }

    @Test
    public void testMethod() {
        DmFocMonthWeightVO dmFocActualCostVO = new DmFocMonthWeightVO();
        dmFocActualCostVO.setDelFlag("Y");
        dmFocActualCostVO.getDelFlag();
        dmFocActualCostVO.setCreatedBy("1175");
        dmFocActualCostVO.getCreatedBy();
        dmFocActualCostVO.getCreationDate();
        dmFocActualCostVO.setGroupCode("1163A");
        dmFocActualCostVO.getGroupCode();
        dmFocActualCostVO.setVersionId(11L);
        dmFocActualCostVO.getVersionId();
        dmFocActualCostVO.setGroupCnName("元器");
        dmFocActualCostVO.getGroupCnName();
        dmFocActualCostVO.setGroupLevel("LV3");
        dmFocActualCostVO.getGroupLevel();
        dmFocActualCostVO.setId(12L);
        dmFocActualCostVO.getId();
        dmFocActualCostVO.setViewFlag("0");
        dmFocActualCostVO.getViewFlag();
        dmFocActualCostVO.setParentCode("54211");
        dmFocActualCostVO.getParentCode();
        dmFocActualCostVO.setPeriodYear("2023");
        dmFocActualCostVO.getPeriodYear();
        dmFocActualCostVO.getLastUpdateDate();
        dmFocActualCostVO.setLastUpdatedBy("166");
        dmFocActualCostVO.getLastUpdatedBy();
        dmFocActualCostVO.setProdRndTeamCode("code");
        dmFocActualCostVO.getProdRndTeamCode();
        dmFocActualCostVO.setProdRndTeamCnName("test");
        dmFocActualCostVO.getProdRndTeamCnName();
        dmFocActualCostVO.setWeightPercent("11");
        dmFocActualCostVO.getWeightPercent();
        dmFocActualCostVO.setWeightRate(11.2D);
        dmFocActualCostVO.getWeightRate();
        dmFocActualCostVO.setPeriodYearType("S");
        dmFocActualCostVO.getPeriodYearType();
        dmFocActualCostVO.setWeightRateStr("str");
        dmFocActualCostVO.getWeightRateStr();
        dmFocActualCostVO.setGroupCnName("exp");
        dmFocActualCostVO.getGroupCnName();
        Timestamp timestamp = new Timestamp(new Date().getTime());
        dmFocActualCostVO.setCreationDate(timestamp);
        dmFocActualCostVO.setLastUpdateDate(timestamp);
        DmFocMonthWeightVO.builder().id(11L).createdBy("test1").creationDate(timestamp).lastUpdateDate(timestamp)
                .lastUpdatedBy("test1").delFlag("N").groupCnName("name1").groupCnName("name1").groupLevel("lv1").parentCode("122")
                .periodYear("2021").periodYearType("type").prodRndTeamCnName("lv1").prodRndTeamCode("lv1").versionId(11L).viewFlag("1")
                .weightPercent("1.0").weightRate(1.0D).weightRateStr("1.0").build().toString();
        Assert.assertNotNull(dmFocActualCostVO);
    }

}