/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.common;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * CommonViewVO Class
 *
 * <AUTHOR>
 * @since 2023/3/15
 */
// 不能修改成getter和setter
@Data
@NoArgsConstructor
public class CommonViewVO  extends TableNameVO implements Serializable {
    private static final long serialVersionUID = -3781852458067854849L;

    private String groupLevel;

    private String viewFlag;

    /**
     * 反向视角，设置不同的视角查询
     */
    private String revViewFlag;

    private String customCnName;

    private String id;

    private String connectCode;

    /**
     * 层级code集合
     */
    private List<String> groupCodeList;

    private List<String> groupLevelList;

    private Boolean isGroupLevelFlag;

    /**
     * 组合层级code集合
     */
    private List<String> combGroupCodeList;

    /**
     * 重量级团队code集合
     */
    private List<String> teamCodeList;

    /**
     * 组合重量级团队code集合
     */
    private List<String> combTeamCodeList;


    private String teamLevel;

    private String keyWord;

    private Long versionId;

    private Long monthVersionId;

    private String pageFlag;

    private String isSavePage;

    private String nextGroupLevel;

    /**
     * 量纲维度code集合
     */
    private List<String> dmsCodeList;

    private List<String> lv1CodeList;

    private List<String> lv2CodeList;

    private List<String> lv3CodeList;

    private List<String> lv4CodeList;

    private List<String> combDmsCodeList;

    /**
     * 颗粒度（U：通用，P：盈利 D:量纲）
     */
    private String granularityType;

    /**
     * 业务口径（R:收入时点/C：发货成本）
     */
    private String caliberFlag;

    private String  periodYear;

    private List<String> l1NameList;

    private List<String> l2NameList;

    private List<String> combL1NameList;

    private List<String> combL2NameList;

    /**
     * 国内/海外
     */
    private String overseaFlag;

    /**
     * BG编码
     */
    private String lv0ProdListCode;

    /**
     * ICT产业集合
     */
    private Set<String> lv0DimensionSet = new HashSet<>();

    /**
     * 重量级团队LV1集合
     */
    private Set<String> lv1DimensionSet  = new HashSet<>();

    /**
     * 重量级团队LV2集合
     */
    private Set<String> lv2DimensionSet  = new HashSet<>();

    /**
     * 重量级团队LV1集合
     */
    private Set<String> lv1ProdRndTeamCodeSet  = new HashSet<>();

    /**
     * 重量级团队LV2集合
     */
    private Set<String> lv2ProdRndTeamCodeSet  = new HashSet<>();

    /**
     * 重量级团队LV3集合
     */
    private Set<String> lv3ProdRndTeamCodeSet  = new HashSet<>();

    private Set<String> lv4ProdRndTeamCodeSet  = new HashSet<>();

    private Set<String> connectDimensionCodeList  = new HashSet<>();

    /**
     * 是否组合
     */
    private Boolean isCombination;

    private String pageSymbol;

    private String granularityPageSymbol;

    private String filterGroupLevel;

    private Long customId;

    /**
     * 组合id
     */
    private List<Long> customIdList;

    /**
     * 父组合组合id
     */
    private List<Long> parentCustomIdList;

    private String userId;

    private String roleId;

    private String lv0Flag;

    private String lv0ProdRndTeamCode;

    // Y表示展开的查询，N或者为Null表示初次查询
    private String expandFlag;

    private String lv1ProdRndTeamCode;

    private String lv2ProdRndTeamCode;

    private String lv3ProdRndTeamCode;

    private String lv4ProdRndTeamCode;

    private String coaCode;

    private String dimensionCode;

    private String dimensionSubCategoryCode;

    private String dimensionSubDetailCode;

    private String spartCode;

    private String l1Name;

    private String l2Name;

    private String l3CegCode;

    private String l4CegCode;

    private String categoryCode;

    private List<String> l3CegCodeList;

    private List<String> l4CegCodeList;

    private List<String> categoryCodeList;

    /**
     * 是否多选
     */
    private Boolean isMultipleSelect;

    /**
     * 入参区分是否是获取权限得查询
     */
    private Boolean permissionTag;

    // 区分是全品类清单，或者规格品清单
    private String dataType;

    /**
     * coacode集合
     */
    private List<String> coaCodeList;

    /**
     * 量纲code集合
     */
    private List<String> dimensionCodeList;

    /**
     * 量纲子类code集合
     */
    private List<String> dimensionSubcategoryCodeList;

    /**
     * 量纲子类明细code集合
     */
    private List<String> dimensionSubDetailCodeList;

    /**
     * spart code集合
     */
    private List<String> spartCodeList;

    /**
     * 组合coacode集合
     */
    private List<String> combCoaCodeList;

    /**
     * 组合量纲code集合
     */
    private List<String> combDimensionCodeList;

    /**
     * 组合量纲子类code集合
     */
    private List<String> combDimensionSubcategoryCodeList;

    /**
     * 组合量纲子类明细code集合
     */
    private List<String> combDimensionSubDetailCodeList;

    /**
     * 组合spart code集合
     */
    private List<String> combSpartCodeList;

    /**
     * 最大视角
     */
    private String  maxViewFlag;

    private List<String> lv1NoPermissList;

    private List<String> lv2NoPermissList;

    private List<String> purCodeList;

    private String purLevel;

    private Boolean reverseFlag;

    /**
     * 成本类型
     */
    private String costType;

    // 发货对象code
    private String shippingObjectCode;

    private List<String> shippingObjectCodeList;

    // 发货对象中文名称
    private String shippingObjectCnName;

    // 制造对象code
    private String manufactureObjectCode;

    // 制造对象中文名称
    private String manufactureObjectCnName;

    private List<String> manufactureObjectCodeList;

    // 配置页面区分规格品和品类(制造对象)
    private String configDataType;

    // 组合整体失效
    private String enableFlag;

}
