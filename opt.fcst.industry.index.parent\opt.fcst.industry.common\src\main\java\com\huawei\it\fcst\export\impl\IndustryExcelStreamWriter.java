/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.export.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillWrapper;

import com.huawei.it.fcst.exeption.TemplateNotFundException;
import com.huawei.it.fcst.export.IExcelExportDataProvider;
import com.huawei.it.fcst.export.IExcelExportExtenderProvider;
import com.huawei.it.fcst.util.LoadedExcelTemplate;
import com.huawei.it.fcst.export.vo.ExcelExportContext;
import com.huawei.it.fcst.export.vo.ExportList;
import com.huawei.it.fcst.export.vo.SheetBeanMetaVO;
import com.huawei.it.jalor5.core.base.PageConfig;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.ioc.Jalor;
import com.huawei.it.jalor5.core.ioc.delegate.JalorApplicationContext;
import com.huawei.it.jalor5.core.util.CollectionUtil;
import com.huawei.it.jalor5.core.util.StreamUtil;
import com.huawei.it.jalor5.excel.exception.ExcelApplicationException;

import lombok.extern.slf4j.Slf4j;

import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.springframework.beans.BeanUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 导出模板数据填充
 *
 * <AUTHOR>
 * @since 202407
 */
@Slf4j
public class IndustryExcelStreamWriter {
    /**
     * 导出上下问信息
     */
    private ExcelExportContext context;

    /**
     * 构造器
     *
     * @param context 导出上下文
     */
    public IndustryExcelStreamWriter(ExcelExportContext context) {
        this.context = context;
    }

    /**
     * 导出处理类
     *
     * @throws ApplicationException 异常信息
     */
    public void fillEasyExcelExport() throws ApplicationException {
        List<SheetBeanMetaVO> beanConfigs = context.getBeanMappings();
        // 加载模板功能
        ExcelWriter excelWriter = fillDynamicEasyExcelExport(context);
        context.setExcelWriter(excelWriter);
        JalorApplicationContext app = Jalor.getContext();
        try {
            Long total = 0L;
            for (SheetBeanMetaVO bean : beanConfigs) {
                Object object = app.getBean("IExcelExport." + bean.getBeanName(), IExcelExportDataProvider.class);
                WriteSheet sheet = EasyExcel.writerSheet(bean.getSheetNo()).build();
                pushHeaderData(context, excelWriter, sheet, (IExcelExportDataProvider) object);
                int num = pushLineData(context, excelWriter, sheet, (IExcelExportDataProvider) object);
                total += num;
            }
            context.setTotal(total);
            excelWriter.finish();
        } catch (Exception exception) {
            throw new ExcelApplicationException("excel export error");
        }
    }

    /**
     * 动态列逻辑处理
     *
     * @param context 上下文
     * @return excel
     * @throws ApplicationException 异常
     */
    public ExcelWriter fillDynamicEasyExcelExport(ExcelExportContext context) throws ApplicationException {
        List<SheetBeanMetaVO> collect = context.getBeanMappings()
                .stream()
                .filter(SheetBeanMetaVO::getDynamicDeader)
                .collect(Collectors.toList());
        if (CollectionUtil.isNullOrEmpty(collect)) {
            return LoadedExcelTemplate.getExcelWriterTemplate(
                    MessageFormat.format("{0}/{1}.xlsx", context.getFileStore(), context.getFileName()),
                    context.getTemplateName());
        }
        ByteArrayOutputStream outputStream = null;
        InputStream inputStream = null;
        try {
            Workbook workbook = LoadedExcelTemplate.getExcelTemplate(context.getTemplateName());
            JalorApplicationContext app = Jalor.getContext();
            for (SheetBeanMetaVO bean : collect) {
                XSSFSheet sheetAt = (XSSFSheet) workbook.getSheetAt(bean.getSheetNo());
                Object object = app.getBean("IExcelExport." + bean.getBeanName(), IExcelExportDataProvider.class);
                if (object instanceof IExcelExportExtenderProvider) {
                    ((IExcelExportExtenderProvider) object).dynamicColumn(context, sheetAt);
                }
            }
            outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            inputStream = new ByteArrayInputStream(outputStream.toByteArray());
            return EasyExcel.write(MessageFormat.format("{0}/{1}.xlsx", context.getFileStore(), context.getFileName()))
                    .withTemplate(inputStream)
                    .build();
        } catch (Exception exception) {
            throw new TemplateNotFundException("动态创建模板失败");
        } finally {
            if (inputStream != null) {
                StreamUtil.closeStreams(inputStream);
            }
            if (outputStream != null) {
                StreamUtil.closeStreams(outputStream);
            }
        }
    }

    /**
     * 数据输出到excel
     *
     * @param excelExportContext 上下文
     * @param excelWriter        excelWriter
     * @param sheet              sheet
     * @param object             bean
     * @return 处理条数
     * @throws ApplicationException 异常
     */
    private void pushHeaderData(ExcelExportContext excelExportContext, ExcelWriter excelWriter, WriteSheet sheet,
            IExcelExportDataProvider object) throws ApplicationException {
        try {
            Map<String, Object> parameters = new HashMap<>();
            if (context.getParameters() != null) {
                BeanUtils.copyProperties(context.getParameters(), parameters);
            }
            // 定义导出头
            excelWriter.fill(object.getHeader(context, parameters), sheet);
        } catch (Exception exception) {
            log.error("excelExportContext{0},object:{1}", excelExportContext, object.getClass());
        }
    }

    /**
     * 数据输出到excel
     *
     * @param excelExportContext 上下文
     * @param excelWriter        excelWriter
     * @param sheet              sheet
     * @param object             bean
     * @return 处理条数
     * @throws ApplicationException 异常
     */
    private int pushLineData(ExcelExportContext excelExportContext, ExcelWriter excelWriter, WriteSheet sheet,
            IExcelExportDataProvider object) throws ApplicationException {
        try {
            PageVO pageVO = getPageVO();
            List<?> data = object.getData(excelExportContext.getConditionObject(), pageVO);
            if (data instanceof ExportList) {
                // 填充数据
                ExportList<?> exportList = (ExportList) data;
                excelWriter.fill(new FillWrapper("list", data), sheet);
                if (exportList.getTotalRows() > pageVO.getPageSize()) {
                    // 迭代写入分页数据
                    for (int i = 2; i <= pageVO.getTotalPages(); i++) {
                        pageVO.setCurPage(i);
                        ExportList<?> tempData = (ExportList) object.getData(excelExportContext.getConditionObject(),
                                pageVO);
                        excelWriter.fill(new FillWrapper("list", tempData), sheet);
                    }
                }
                return ((ExportList<?>) data).getTotalRows();
            } else {
                excelWriter.fill(new FillWrapper("list", data), sheet);
                return data.size();
            }
        } catch (Exception exception) {
            log.error("excelExportContext{0},object:{1}", excelExportContext, object.getClass());
            return 0;
        }
    }

    /**
     * 构造分页对象
     *
     * @return PageVO
     */
    private PageVO getPageVO() {
        PageVO pageVO = new PageVO();
        if (context.getPageVO() != null) {
            BeanUtils.copyProperties(context.getPageVO(), pageVO);
        } else {
            pageVO.setCurPage(1);
            pageVO.setPageSize(PageConfig.DEFAULT.getMaxPageSize());
        }
        return pageVO;
    }

}
