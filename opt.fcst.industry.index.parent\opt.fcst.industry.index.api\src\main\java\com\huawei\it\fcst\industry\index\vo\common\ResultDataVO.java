/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.common;

import com.huawei.it.fcst.industry.index.enums.ResultCodeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * ResultDataVO Class
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
@Getter
@Setter
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "统一返回数据实体类")
public class ResultDataVO implements Serializable {
    private static final long serialVersionUID = 2518961769461376274L;

    @ApiModelProperty(value = "返回内容")
    private Object data;

    @ApiModelProperty(value = "响应状态码")
    private String code;

    @ApiModelProperty(value = "返回信息描述")
    private String message;

    public ResultDataVO(ResultCodeEnum resultCode, Object data) {
        this.code = resultCode.getCode();
        this.message = resultCode.getMessage();
        this.data = data;
    }

    public ResultDataVO(ResultCodeEnum resultCode) {
        this.code = resultCode.getCode();
        this.message = resultCode.getMessage();
    }

    public ResultDataVO(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static ResultDataVO failure(String code, String msg) {
        return new ResultDataVO(code, msg);
    }

    public static ResultDataVO failure(ResultCodeEnum resultCode) {
        return new ResultDataVO(resultCode);
    }

    public static ResultDataVO failure(ResultCodeEnum resultCode, Object data) {
        return new ResultDataVO(resultCode, data);
    }

    public static ResultDataVO success() {
        return new ResultDataVO(ResultCodeEnum.SUCCESS);
    }

    public static ResultDataVO success(Object data) {
        return new ResultDataVO(ResultCodeEnum.SUCCESS, data);
    }


}