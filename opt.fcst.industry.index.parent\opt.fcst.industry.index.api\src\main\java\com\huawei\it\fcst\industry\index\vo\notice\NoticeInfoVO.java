/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.notice;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/2/19
 */
@Getter
@Setter
@NoArgsConstructor
public class NoticeInfoVO {
    /**
     * id
     **/
    private Long id;

    /**
     * 公共类型
     **/
    private String noticeType;


    /**
     * 公共类型集合
     **/
    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<String> noticeTypeList;


    /**
     * 公共主题
     **/
    private String noticeTheme;

    /**
     * 公告标题
     **/
    private String noticeTitle;

    /**
     * 公告内容
     **/
    private String noticeContent;

    /**
     * 公告标签
     **/
    private String noticeTag;

    /**
     * 源文件
     **/
    private String fileSourceKey;

    /**
     * 录入文件类型
     **/
    private String fileType;

    /**
     * 历史公告
     **/
    private String isHistory;

    /**
     *
     *  页码
     */
    private Integer pageIndex;

    /**
     *
     *  一页数量
     */
    private Integer pageSize;

    /**
     *
     *  总条数
     */
    private Integer totalSize;

}
