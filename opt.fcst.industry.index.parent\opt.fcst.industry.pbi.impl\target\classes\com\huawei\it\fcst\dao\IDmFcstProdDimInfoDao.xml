<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.pbi.dao.IDmFcstProdDimInfoDao">
    <resultMap type="com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstDimInfoVO" id="resultMap">
        <result property="id" column="id"/>
        <result property="bgCode" column="bg_code"/>
        <result property="bgCnName" column="bg_cn_name"/>
        <result property="dimensionCode" column="dimension_code"/>
        <result property="dimensionCnName" column="dimension_cn_name"/>
        <result property="dimensionSubCategoryCode" column="dimension_subCategory_code"/>
        <result property="dimensionSubCategoryCnName" column="dimension_subCategory_cn_name"/>
        <result property="dimensionSubDetailCode" column="dimension_sub_detail_code"/>
        <result property="dimensionSubDetailCnName" column="dimension_sub_detail_cn_name"/>
        <result property="spartCode" column="spart_code"/>
        <result property="spartCnName" column="spart_cn_name"/>
        <result property="regionCode" column="region_code"/>
        <result property="regionCnName" column="region_cn_name"/>
        <result property="repofficeCode" column="repoffice_code"/>
        <result property="repofficeCnName" column="repoffice_cn_name"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDdate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="groupCode" column="groupCode"/>
        <result property="groupLevel" column="groupLevel"/>
        <result property="groupCnName" column="groupCnName"/>
        <result property="groupLevel" column="group_level"/>
        <result property="num" column="num"/>
        <result property="lv0Code" column="lv0_code"/>
        <result property="lv1Code" column="lv1_code"/>
        <result property="lv1CnName" column="lv1_cn_name"/>
        <result property="lv2Code" column="lv2_code"/>
        <result property="prodRndTeamCode" column="prod_rnd_team_code"/>
    </resultMap>

    <select id="findCostGapSpartCodeList" resultMap="resultMap">
        SELECT  distinct group_code as groupCode,group_cn_name as groupCnName,'SPART' as groupLevel
        FROM fin_dm_opt_foi.dm_fcst_ict_prod_cost_gap_detail_t a
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
        <if test='costType != null and costType != ""'>
            and cost_type = #{costType,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag != null and mainFlag != ""'>
            and main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes != null and codeAttributes != ""'>
            and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='prodRndTeamCode != null and prodRndTeamCode != ""'>
            and prod_rnd_team_code = #{prodRndTeamCode,jdbcType=VARCHAR}
        </if>
            and exists (select 1 from fin_dm_opt_foi.dm_fcst_ict_psp_prod_top_spart_info_t b where a.prod_rnd_team_code = b.lv4_prod_list_code
            and b.version_id = #{versionId,jdbcType=NUMERIC}
            and b.is_top_flag ='Y'
            and b.double_flag = 'Y'
            <if test='mainFlag != null and mainFlag != ""'>
                and b.main_flag = #{mainFlag,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes != null and codeAttributes != ""'>
                and b.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='bgCode != null and bgCode != ""'>
                and b.bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='repofficeCode != null and repofficeCode != ""'>
                and b.repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='regionCode != null and regionCode != ""'>
                and b.region_code = #{regionCode,jdbcType=VARCHAR}
            </if>
            <if test='viewFlag != null and viewFlag != ""'>
                and b.view_flag = #{viewFlag,jdbcType=VARCHAR}
            </if>
            <if test='lv0ProdRndTeamCode!=null and lv0ProdRndTeamCode!=""'>
                and b.lv0_prod_list_code = #{lv0ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv1ProdRndTeamCode!=null and lv1ProdRndTeamCode!=""'>
                and b.lv1_prod_list_code = #{lv1ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv2ProdRndTeamCode!=null and lv2ProdRndTeamCode!=""'>
                and b.lv2_prod_list_code = #{lv2ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv3ProdRndTeamCode!=null and lv3ProdRndTeamCode!=""'>
                and b.lv3_prod_list_code = #{lv3ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv4ProdRndTeamCode!=null and lv4ProdRndTeamCode!=""'>
                and b.lv4_prod_list_code = #{lv4ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            )
        <if test='repofficeCode != null and repofficeCode != ""'>
            and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='ratioPspStd != null'>
            and ratio_psp_std > #{ratioPspStd,jdbcType=NUMERIC}
        </if>
        <if test='versionId != null'>
            and version_id = #{versionId,jdbcType=NUMERIC}
        </if>
            and group_level = 'SPART'
            and group_code !='SNULL'
        </trim>
    </select>

    <select id="baseProdDimInfoList"  resultMap="resultMap">
        select
        <choose>
            <when test='nextGroupLevel == "SPART"'>
               <if test='pageType == "ANNUAL"'>
                    DISTINCT spart_code AS groupCode, spart_cn_name AS groupCnName, 'SPART' AS groupLevel
                </if>
                <if test='pageType != "ANNUAL"'>
                    DISTINCT top_spart_code AS groupCode, top_spart_cn_name AS groupCnName, 'SPART' AS groupLevel
                </if>
            </when>
            <when test='nextGroupLevel == "SUB_DETAIL"'>
                DISTINCT dimension_sub_detail_code AS groupCode, dimension_sub_detail_cn_name AS groupCnName, 'SUB_DETAIL' AS groupLevel
            </when>
            <when test='nextGroupLevel == "SUBCATEGORY"'>
                DISTINCT dimension_subcategory_code AS groupCode, dimension_subcategory_cn_name AS groupCnName, 'SUBCATEGORY' AS groupLevel
            </when>
            <when test='nextGroupLevel == "DIMENSION"'>
                DISTINCT dimension_code AS groupCode, dimension_cn_name AS groupCnName, 'DIMENSION' AS groupLevel
            </when>
            <when test='nextGroupLevel == "LV4"'>
                DISTINCT lv4_prod_list_code AS groupCode, lv4_prod_list_cn_name AS groupCnName, 'LV4' AS groupLevel
            </when>
            <when test='nextGroupLevel == "LV3"'>
                DISTINCT lv3_prod_list_code AS groupCode, lv3_prod_list_cn_name AS groupCnName, 'LV3' AS groupLevel
            </when>
            <when test='nextGroupLevel == "LV2"'>
                DISTINCT lv2_prod_list_code AS groupCode, lv2_prod_list_cn_name AS groupCnName,'LV2' AS groupLevel
            </when>
            <when test='nextGroupLevel == "LV1"'>
                DISTINCT lv1_prod_list_code AS groupCode, lv1_prod_list_cn_name AS groupCnName,'LV1' AS groupLevel
            </when>
            <when test='nextGroupLevel == "LV0"'>
                DISTINCT lv0_prod_list_code AS groupCode, lv0_prod_list_cn_name AS groupCnName,'LV0' AS groupLevel
            </when>
            <when test='nextGroupLevel == "CODE"'>
                DISTINCT code_attributes AS groupCode, code_attributes AS groupCnName,'CODE' AS groupLevel
            </when>
            <otherwise>
            </otherwise>
    </choose>
        <if test='costType == "PSP"'>
            <choose>
                <when test='pageType == "ANNUAL"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_dim_info_t
                </when>
                <when test='(pageType == "MONTH" or pageType == "CONFIG") and viewFlag =="PROD_SPART"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_top_spart_info_t
                </when>
                <when test='pageType == "MONTH" and viewFlag =="DIMENSION"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_dim_info_t
                </when>
                <when test='pageType == "REPLACE_DIM"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_top_spart_info_t
                </when>
            </choose>
        </if>
        <if test='costType == "STD"'>
            <choose>
                <when test='pageType == "ANNUAL"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_dim_info_t
                </when>
                <when test='(pageType == "MONTH" or pageType == "CONFIG") and viewFlag =="PROD_SPART"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_top_spart_info_t
                </when>
                <when test='pageType == "MONTH" and viewFlag =="DIMENSION"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_dim_info_t
                </when>
                <when test='pageType == "REPLACE_DIM"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_top_spart_info_t
                </when>
            </choose>
        </if>
     <trim prefix="WHERE" prefixOverrides="AND |OR ">
        <if test='pageType == "REPLACE_DIM" or (pageType == "MONTH" and viewFlag == "PROD_SPART")'>
            and is_top_flag ='Y'
            and double_flag = 'Y'
        </if>
         <if test='pageType == "CONFIG"'>
             and double_flag = 'Y'
         </if>
        <if test='regionCode!=null and regionCode!=""'>
            and region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
         <if test='repofficeCode!=null and repofficeCode!=""'>
             and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
         </if>
         <if test='bgCode!=null and bgCode!=""'>
             and bg_code = #{bgCode,jdbcType=VARCHAR}
         </if>
         <if test='overseaFlag!=null and overseaFlag!=""'>
             and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
         </if>
         <if test='viewFlag!=null and viewFlag!=""'>
             and view_flag = #{viewFlag,jdbcType=VARCHAR}
         </if>
         <if test='mainFlag!=null and mainFlag!=""'>
             and main_flag = #{mainFlag,jdbcType=VARCHAR}
         </if>
         <if test='codeAttributes!=null and codeAttributes!=""'>
             and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
         </if>
         <if test='versionId!=null'>
             and version_id = #{versionId,jdbcType=NUMERIC}
         </if>
         <if test='pageType == "ANNUAL" and nextGroupLevel!=null and nextGroupLevel!="" and nextGroupLevel !="CODE"'>
             and group_level = #{nextGroupLevel,jdbcType=VARCHAR}
         </if>
         <if test='pageType == "MONTH" and viewFlag == "DIMENSION"  and nextGroupLevel!=null and nextGroupLevel!="" and nextGroupLevel !="CODE"'>
             and group_level = #{nextGroupLevel,jdbcType=VARCHAR}
         </if>
         <if test='lv0ProdRndTeamCodeList != null and lv0ProdRndTeamCodeList.size() > 0'>
             <foreach collection='lv0ProdRndTeamCodeList' item="code" open="AND lv0_prod_list_code IN (" close=")" index="index"
                      separator=",">
                 #{code}
             </foreach>
         </if>
         <if test='lv1ProdRndTeamCodeList != null and lv1ProdRndTeamCodeList.size() > 0'>
             <foreach collection='lv1ProdRndTeamCodeList' item="code" open="AND lv1_prod_list_code IN (" close=")" index="index"
                      separator=",">
                 #{code}
             </foreach>
         </if>
         <if test='lv2ProdRndTeamCodeList != null and lv2ProdRndTeamCodeList.size() > 0'>
             <foreach collection='lv2ProdRndTeamCodeList' item="code" open="AND lv2_prod_list_code IN (" close=")" index="index"
                      separator=",">
                 #{code}
             </foreach>
         </if>
         <if test='lv3ProdRndTeamCodeList != null and lv3ProdRndTeamCodeList.size() > 0'>
             <foreach collection='lv3ProdRndTeamCodeList' item="code" open="AND lv3_prod_list_code IN (" close=")" index="index"
                      separator=",">
                 #{code}
             </foreach>
         </if>
         <if test='lv4ProdRndTeamCodeList != null and lv4ProdRndTeamCodeList.size() > 0'>
             <foreach collection='lv4ProdRndTeamCodeList' item="code" open="AND lv4_prod_list_code IN (" close=")" index="index"
                      separator=",">
                 #{code}
             </foreach>
         </if>
         <if test='lv0ProdRndTeamCode!=null and lv0ProdRndTeamCode!=""'>
             and lv0_prod_list_code = #{lv0ProdRndTeamCode,jdbcType=VARCHAR}
         </if>
         <if test='lv1ProdRndTeamCode!=null and lv1ProdRndTeamCode!=""'>
             and lv1_prod_list_code = #{lv1ProdRndTeamCode,jdbcType=VARCHAR}
         </if>
         <if test='lv2ProdRndTeamCode!=null and lv2ProdRndTeamCode!=""'>
             and lv2_prod_list_code = #{lv2ProdRndTeamCode,jdbcType=VARCHAR}
         </if>
         <if test='lv3ProdRndTeamCode!=null and lv3ProdRndTeamCode!=""'>
             and lv3_prod_list_code = #{lv3ProdRndTeamCode,jdbcType=VARCHAR}
         </if>
         <if test='lv4ProdRndTeamCode!=null and lv4ProdRndTeamCode!=""'>
             and lv4_prod_list_code = #{lv4ProdRndTeamCode,jdbcType=VARCHAR}
         </if>
         <if test='dimensionCode!=null and dimensionCode!=""'>
             and dimension_code = #{dimensionCode,jdbcType=VARCHAR}
         </if>
         <if test='dimensionSubCategoryCode!=null and dimensionSubCategoryCode!=""'>
             and dimension_subCategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
         </if>
         <if test='dimensionSubDetailCode!=null and dimensionSubDetailCode!=""'>
             and dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
         </if>
         <if test='spartCode!=null and spartCode!="" and pageType == "ANNUAL"'>
             and spart_code = #{spartCode,jdbcType=VARCHAR}
         </if>
         <if test='spartCode!=null and spartCode!="" and pageType != "ANNUAL"'>
             and top_spart_code = #{spartCode,jdbcType=VARCHAR}
         </if>
         <if test='nextGroupLevel == "SPART" and pageType == "ANNUAL"'>
             and spart_code !='SNULL'
         </if>
         <if test='nextGroupLevel == "SPART" and pageType != "ANNUAL"'>
             and top_spart_code !='SNULL'
         </if>
         <if test='nextGroupLevel == "SUB_DETAIL"'>
             and dimension_sub_detail_code !='SNULL'
         </if>
         <if test='nextGroupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
             <foreach collection='lv2DimensionSet' item="code" open="AND lv1_prod_list_code IN (" close=")" index="index"
                      separator=",">
                 #{code}
             </foreach>
         </if>
         <if test='nextGroupLevel == "LV2" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
             <foreach collection='lv3DimensionSet' item="code" open="AND lv2_prod_list_code IN (" close=")" index="index"
                      separator=",">
                 #{code}
             </foreach>
         </if>
     </trim>
    </select>

    <sql id="topSpartInnerJoin">
        ON t2.del_flag = 'N'
        AND t2.main_flag = 'Y'
        AND t2.is_top_flag = 'Y'
        AND t2.double_flag = 'Y'
        <if test='versionId != null'>
            AND t2.version_id = #{monVersionId,jdbcType=NUMERIC}
        </if>
        AND t2.bg_code = t1.bg_code
        AND t2.code_attributes = t1.code_attributes
        AND t2.lv1_prod_list_code = t1.lv1_prod_list_code
        AND t2.lv2_prod_list_code = t1.lv2_prod_list_code
        AND t2.lv3_prod_list_code = t1.lv3_prod_list_code
        AND t2.lv4_prod_list_code = t1.lv4_prod_list_code
        AND t2.top_spart_code = t1.spart_code
    </sql>

    <select id="mainFlagDimInfoList"  resultMap="resultMap">
        select
        <choose>
            <when test='nextGroupLevel == "SPART"'>
                DISTINCT t1.spart_code AS groupCode, t1.spart_cn_name AS groupCnName, 'SPART' AS groupLevel
            </when>
            <when test='nextGroupLevel == "LV4"'>
                DISTINCT t1.lv4_prod_list_code AS groupCode, t1.lv4_prod_list_cn_name AS groupCnName, 'LV4' AS groupLevel
            </when>
            <when test='nextGroupLevel == "LV3"'>
                DISTINCT t1.lv3_prod_list_code AS groupCode, t1.lv3_prod_list_cn_name AS groupCnName, 'LV3' AS groupLevel
            </when>
            <when test='nextGroupLevel == "LV2"'>
                DISTINCT t1.lv2_prod_list_code AS groupCode, t1.lv2_prod_list_cn_name AS groupCnName,'LV2' AS groupLevel
            </when>
            <when test='nextGroupLevel == "LV1"'>
                DISTINCT t1.lv1_prod_list_code AS groupCode, t1.lv1_prod_list_cn_name AS groupCnName,'LV1' AS groupLevel
            </when>
            <when test='nextGroupLevel == "CODE"'>
                DISTINCT t1.code_attributes AS groupCode, t1.code_attributes AS groupCnName,'CODE' AS groupLevel
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='costType == "PSP"'>
            FROM fin_dm_opt_foi.dm_fcst_ict_psp_pbi_main_code_dim_t t1
            INNER JOIN fin_dm_opt_foi.dm_fcst_ict_psp_prod_top_spart_info_t t2
            <include refid="topSpartInnerJoin"/>
        </if>
        <if test='costType == "STD"'>
            FROM fin_dm_opt_foi.dm_fcst_ict_std_pbi_main_code_dim_t t1
            INNER JOIN fin_dm_opt_foi.dm_fcst_ict_std_prod_top_spart_info_t t2
            <include refid="topSpartInnerJoin"/>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='bgCode!=null and bgCode!=""'>
                and t1.bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and t1.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='versionId!=null'>
                and t1.version_id = #{versionId,jdbcType=NUMERIC}
            </if>
            <if test='lv1ProdRndTeamCodeList != null and lv1ProdRndTeamCodeList.size() > 0'>
                <foreach collection='lv1ProdRndTeamCodeList' item="code" open="AND t1.lv1_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv2ProdRndTeamCodeList != null and lv2ProdRndTeamCodeList.size() > 0'>
                <foreach collection='lv2ProdRndTeamCodeList' item="code" open="AND t1.lv2_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv3ProdRndTeamCodeList != null and lv3ProdRndTeamCodeList.size() > 0'>
                <foreach collection='lv3ProdRndTeamCodeList' item="code" open="AND t1.lv3_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv4ProdRndTeamCodeList != null and lv4ProdRndTeamCodeList.size() > 0'>
                <foreach collection='lv4ProdRndTeamCodeList' item="code" open="AND t1.lv4_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv1ProdRndTeamCode!=null and lv1ProdRndTeamCode!=""'>
                and t1.lv1_prod_list_code = #{lv1ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv2ProdRndTeamCode!=null and lv2ProdRndTeamCode!=""'>
                and t1.lv2_prod_list_code = #{lv2ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv3ProdRndTeamCode!=null and lv3ProdRndTeamCode!=""'>
                and t1.lv3_prod_list_code = #{lv3ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv4ProdRndTeamCode!=null and lv4ProdRndTeamCode!=""'>
                and t1.lv4_prod_list_code = #{lv4ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!=""'>
                and t1.spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='nextGroupLevel == "SPART"'>
                and t1.spart_code !='SNULL'
            </if>
            <if test='nextGroupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND t1.lv1_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='nextGroupLevel == "LV2" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND t1.lv2_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="mainFlagAnnualDimInfoList"  resultMap="resultMap">
        select
        <choose>
            <when test='nextGroupLevel == "SPART"'>
                DISTINCT t1.spart_code AS groupCode, t1.spart_cn_name AS groupCnName, 'SPART' AS groupLevel
            </when>
            <when test='nextGroupLevel == "LV4"'>
                DISTINCT t1.lv4_prod_list_code AS groupCode, t1.lv4_prod_list_cn_name AS groupCnName, 'LV4' AS groupLevel
            </when>
            <when test='nextGroupLevel == "LV3"'>
                DISTINCT t1.lv3_prod_list_code AS groupCode, t1.lv3_prod_list_cn_name AS groupCnName, 'LV3' AS groupLevel
            </when>
            <when test='nextGroupLevel == "LV2"'>
                DISTINCT t1.lv2_prod_list_code AS groupCode, t1.lv2_prod_list_cn_name AS groupCnName,'LV2' AS groupLevel
            </when>
            <when test='nextGroupLevel == "LV1"'>
                DISTINCT t1.lv1_prod_list_code AS groupCode, t1.lv1_prod_list_cn_name AS groupCnName,'LV1' AS groupLevel
            </when>
            <when test='nextGroupLevel == "CODE"'>
                DISTINCT t1.code_attributes AS groupCode, t1.code_attributes AS groupCnName,'CODE' AS groupLevel
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='costType == "PSP"'>
            from fin_dm_opt_foi.dm_fcst_ict_psp_pbi_main_code_dim_t t1 INNER JOIN fin_dm_opt_foi.DM_FCST_ICT_PSP_PROD_DIM_INFO_T t2
            ON t2.del_flag = 'N'
            and t2.main_flag = #{mainFlag}
            and t2.region_code = #{regionCode}
            and t2.repoffice_code = #{repofficeCode}
            and t2.oversea_flag = #{overseaFlag}
            and t2.view_flag = #{viewFlag}
            and nvl(t1.lv1_prod_list_code,'snull') = nvl(t2.lv1_prod_list_code,'snull')
            and nvl(t1.lv2_prod_list_code,'snull') = nvl(t2.lv2_prod_list_code,'snull')
            and nvl(t1.lv3_prod_list_code,'snull') = nvl(t2.lv3_prod_list_code,'snull')
            and nvl(t1.lv4_prod_list_code,'snull') = nvl(t2.lv4_prod_list_code,'snull')
            and nvl(t1.spart_code,'snull') = nvl(t2.spart_code,'snull')
            and t1.bg_code = t2.bg_code
            and t1.version_id = t2.version_id
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and nvl(t1.code_attributes,'snull') = nvl(t2.code_attributes,'snull')
            </if>
        </if>
        <if test='costType == "STD"'>
            from fin_dm_opt_foi.dm_fcst_ict_std_pbi_main_code_dim_t t1 INNER JOIN fin_dm_opt_foi.DM_FCST_ICT_STD_PROD_DIM_INFO_T t2
            ON t2.del_flag = 'N'
            and t2.main_flag = #{mainFlag}
            and t2.region_code = #{regionCode}
            and t2.repoffice_code = #{repofficeCode}
            and t2.oversea_flag = #{overseaFlag}
            and t2.view_flag = #{viewFlag}
            and nvl(t1.lv1_prod_list_code,'snull') = nvl(t2.lv1_prod_list_code,'snull')
            and nvl(t1.lv2_prod_list_code,'snull') = nvl(t2.lv2_prod_list_code,'snull')
            and nvl(t1.lv3_prod_list_code,'snull') = nvl(t2.lv3_prod_list_code,'snull')
            and nvl(t1.lv4_prod_list_code,'snull') = nvl(t2.lv4_prod_list_code,'snull')
            and nvl(t1.spart_code,'snull') = nvl(t2.spart_code,'snull')
            and t1.bg_code = t2.bg_code
            and t1.version_id = t2.version_id
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and nvl(t1.code_attributes,'snull') = nvl(t2.code_attributes,'snull')
            </if>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='bgCode!=null and bgCode!=""'>
                and t1.bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and t1.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='versionId!=null'>
                and t1.version_id = #{versionId,jdbcType=NUMERIC}
            </if>
            <if test='lv1ProdRndTeamCodeList != null and lv1ProdRndTeamCodeList.size() > 0'>
                <foreach collection='lv1ProdRndTeamCodeList' item="code" open="AND t1.lv1_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv2ProdRndTeamCodeList != null and lv2ProdRndTeamCodeList.size() > 0'>
                <foreach collection='lv2ProdRndTeamCodeList' item="code" open="AND t1.lv2_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv3ProdRndTeamCodeList != null and lv3ProdRndTeamCodeList.size() > 0'>
                <foreach collection='lv3ProdRndTeamCodeList' item="code" open="AND t1.lv3_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv4ProdRndTeamCodeList != null and lv4ProdRndTeamCodeList.size() > 0'>
                <foreach collection='lv4ProdRndTeamCodeList' item="code" open="AND t1.lv4_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv1ProdRndTeamCode!=null and lv1ProdRndTeamCode!=""'>
                and t1.lv1_prod_list_code = #{lv1ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv2ProdRndTeamCode!=null and lv2ProdRndTeamCode!=""'>
                and t1.lv2_prod_list_code = #{lv2ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv3ProdRndTeamCode!=null and lv3ProdRndTeamCode!=""'>
                and t1.lv3_prod_list_code = #{lv3ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv4ProdRndTeamCode!=null and lv4ProdRndTeamCode!=""'>
                and t1.lv4_prod_list_code = #{lv4ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionCode!=null and dimensionCode!=""'>
                and t1.dimension_code = #{dimensionCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubCategoryCode!=null and dimensionSubCategoryCode!=""'>
                and t1.dimension_subCategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubDetailCode!=null and dimensionSubDetailCode!=""'>
                and t1.dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!=""'>
                and t1.spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='nextGroupLevel == "SPART"'>
                and t1.spart_code !='SNULL'
            </if>
            <if test='nextGroupLevel == "SUB_DETAIL"'>
                and t1.dimension_sub_detail_code !='SNULL'
            </if>
            <if test='nextGroupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND t1.lv1_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='nextGroupLevel == "LV2" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND t1.lv2_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getProdSpartOrDimensionNum"  resultType="java.lang.Integer">
        select
        <choose>
            <when test='groupLevel == "SPART"'>
                <if test='pageType == "ANNUAL"'>
                    count(spart_code) AS num
                </if>
                <if test='pageType != "ANNUAL"'>
                    count(top_spart_code) AS num
                </if>
            </when>
            <when test='groupLevel == "SUB_DETAIL"'>
                count(dimension_sub_detail_code) AS num
            </when>
            <when test='groupLevel == "SUBCATEGORY"'>
                count(dimension_subcategory_code) AS num
            </when>
            <when test='groupLevel == "DIMENSION"'>
                count(dimension_code) AS num
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='costType == "PSP"'>
            <choose>
                <when test='pageType == "ANNUAL"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_dim_info_t
                </when>
                <when test='pageType == "MONTH" and viewFlag =="PROD_SPART"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_top_spart_info_t
                </when>
                <when test='pageType == "MONTH" and viewFlag =="DIMENSION"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_dim_info_t
                </when>
                <when test='pageType == "REPLACE_DIM"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_top_spart_info_t
                </when>
            </choose>
        </if>
        <if test='costType == "STD"'>
            <choose>
                <when test='pageType == "ANNUAL"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_dim_info_t
                </when>
                <when test='pageType == "MONTH" and viewFlag =="PROD_SPART"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_top_spart_info_t
                </when>
                <when test='pageType == "MONTH" and viewFlag =="DIMENSION"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_dim_info_t
                </when>
                <when test='pageType == "REPLACE_DIM"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_top_spart_info_t
                </when>
            </choose>
        </if>
        <include refid="getNumSqlCondition"></include>
    </select>

    <select id="getLv4CodeWithSpartOrDimension"  resultMap="resultMap">
        select lv4_prod_list_code as prod_rnd_team_code
        <if test='costType == "PSP"'>
            <choose>
                <when test='pageType == "ANNUAL"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_dim_info_t
                </when>
                <when test='pageType == "MONTH" and viewFlag =="PROD_SPART"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_top_spart_info_t
                </when>
                <when test='pageType == "MONTH" and viewFlag =="DIMENSION"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_dim_info_t
                </when>
                <when test='pageType == "REPLACE_DIM"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_top_spart_info_t
                </when>
            </choose>
        </if>
        <if test='costType == "STD"'>
            <choose>
                <when test='pageType == "ANNUAL"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_dim_info_t
                </when>
                <when test='pageType == "MONTH" and viewFlag =="PROD_SPART"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_top_spart_info_t
                </when>
                <when test='pageType == "MONTH" and viewFlag =="DIMENSION"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_dim_info_t
                </when>
                <when test='pageType == "REPLACE_DIM"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_top_spart_info_t
                </when>
            </choose>
        </if>
     <include refid="getNumSqlCondition"></include>
    </select>

    <sql id="getNumSqlCondition">
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='pageType == "REPLACE_DIM" or (pageType == "MONTH" and viewFlag == "PROD_SPART")'>
                and is_top_flag ='Y'
                and double_flag = 'Y'
            </if>
            <if test='versionId!=null'>
                and version_id = #{versionId,jdbcType=NUMERIC}
            </if>
            <if test='regionCode!=null and regionCode!=""'>
                and region_code = #{regionCode,jdbcType=VARCHAR}
            </if>
            <if test='repofficeCode!=null and repofficeCode!=""'>
                and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='bgCode!=null and bgCode!=""'>
                and bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='overseaFlag!=null and overseaFlag!=""'>
                and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='viewFlag!=null and viewFlag!=""'>
                and view_flag = #{viewFlag,jdbcType=VARCHAR}
            </if>
            <if test='mainFlag!=null and mainFlag!=""'>
                and main_flag = #{mainFlag,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='pageType == "ANNUAL" and groupLevel!=null and groupLevel!=""'>
                and group_level = #{groupLevel,jdbcType=VARCHAR}
            </if>
            <if test='pageType == "MONTH" and viewFlag == "DIMENSION" and groupLevel!=null and groupLevel!=""'>
                and group_level = #{groupLevel,jdbcType=VARCHAR}
            </if>
            <if test='lv0ProdRndTeamCodeList != null and lv0ProdRndTeamCodeList.size() > 0'>
                <foreach collection='lv0ProdRndTeamCodeList' item="code" open="AND lv0_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv1ProdRndTeamCodeList != null and lv1ProdRndTeamCodeList.size() > 0'>
                <foreach collection='lv1ProdRndTeamCodeList' item="code" open="AND lv1_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv2ProdRndTeamCodeList != null and lv2ProdRndTeamCodeList.size() > 0'>
                <foreach collection='lv2ProdRndTeamCodeList' item="code" open="AND lv2_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv3ProdRndTeamCodeList != null and lv3ProdRndTeamCodeList.size() > 0'>
                <foreach collection='lv3ProdRndTeamCodeList' item="code" open="AND lv3_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv4ProdRndTeamCodeList != null and lv4ProdRndTeamCodeList.size() > 0'>
                <foreach collection='lv4ProdRndTeamCodeList' item="code" open="AND lv4_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv0ProdRndTeamCode!=null and lv0ProdRndTeamCode!=""'>
                and lv0_prod_list_code = #{lv0ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv1ProdRndTeamCode!=null and lv1ProdRndTeamCode!=""'>
                and lv1_prod_list_code = #{lv1ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv2ProdRndTeamCode!=null and lv2ProdRndTeamCode!=""'>
                and lv2_prod_list_code = #{lv2ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv3ProdRndTeamCode!=null and lv3ProdRndTeamCode!=""'>
                and lv3_prod_list_code = #{lv3ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='lv4ProdRndTeamCode!=null and lv4ProdRndTeamCode!=""'>
                and lv4_prod_list_code = #{lv4ProdRndTeamCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionCode!=null and dimensionCode!=""'>
                and dimension_code = #{dimensionCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubCategoryCode!=null and dimensionSubCategoryCode!=""'>
                and dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubDetailCode!=null and dimensionSubDetailCode!=""'>
                and dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!="" and pageType == "ANNUAL"'>
                and spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!="" and pageType != "ANNUAL"'>
                and top_spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='nextGroupLevel == "SPART" and pageType == "ANNUAL"'>
                and spart_code !='SNULL'
            </if>
            <if test='nextGroupLevel == "SPART" and pageType != "ANNUAL"'>
                and top_spart_code !='SNULL'
            </if>
            <if test='nextGroupLevel == "SUB_DETAIL"'>
                and dimension_sub_detail_code !='SNULL'
            </if>
            <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND lv1_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel == "LV2" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND lv2_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </sql>
    <select id="getLv1AndLv2CodeList" resultMap="resultMap">
        select * from (select DISTINCT lv0_prod_list_code as lv0_code,lv1_prod_list_code as lv1_code,lv2_prod_list_code as lv2_code FROM fin_dm_opt_foi.dm_fcst_ict_psp_prod_dim_info_t
        WHERE  DEL_FLAG = 'N' and group_level ='LV2'
        union
        select DISTINCT lv0_prod_list_code as lv0_code,lv1_prod_list_code as lv1_code,lv2_prod_list_code as lv2_code FROM fin_dm_opt_foi.dm_fcst_ict_std_prod_dim_info_t
        WHERE  DEL_FLAG = 'N' and group_level ='LV2')
        <trim  prefix="WHERE" prefixOverrides="AND |OR ">
        <if test='lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND lv1_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
            <foreach collection='lv3DimensionSet' item="code" open="AND lv2_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        </trim>
    </select>

    <select id="reverseFindLv1Code" resultMap="resultMap">
        SELECT DISTINCT lv1_prod_list_code as lv1_code,lv1_prod_list_cn_name as lv1_cn_name
        <if test='costType == "PSP"'>
            FROM  fin_dm_opt_foi.dm_fcst_ict_psp_prod_dim_info_t
        </if>
        <if test='costType == "STD"'>
            FROM  fin_dm_opt_foi.dm_fcst_ict_std_prod_dim_info_t
        </if>
        WHERE  del_flag ='N'
        <if test='lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
            <foreach collection='lv3DimensionSet' item="code" open="AND lv2_prod_list_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='regionCode!=null and regionCode!=""'>
            and region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode!=null and repofficeCode!=""'>
            and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='bgCode!=null and bgCode!=""'>
            and bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag!=null and overseaFlag!=""'>
            and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag!=null and viewFlag!=""'>
            and view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag!=null and mainFlag!=""'>
            and main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes!=null and codeAttributes!=""'>
            and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="reverseFindLv1CodeMonth" resultMap="resultMap">
        SELECT DISTINCT lv1_prod_list_code as lv1_code,lv1_prod_list_cn_name as lv1_cn_name
        <if test='costType == "PSP"'>
            FROM  fin_dm_opt_foi.dm_fcst_ict_psp_prod_top_spart_info_t
        </if>
        <if test='costType == "STD"'>
            FROM  fin_dm_opt_foi.dm_fcst_ict_std_prod_top_spart_info_t
        </if>
        WHERE  del_flag ='N'  and is_top_flag ='Y'  and version_id = #{versionId,jdbcType=NUMERIC}
        <if test='lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
            <foreach collection='lv3DimensionSet' item="code" open="AND lv2_prod_list_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='regionCode!=null and regionCode!=""'>
            and region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode!=null and repofficeCode!=""'>
            and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='bgCode!=null and bgCode!=""'>
            and bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag!=null and overseaFlag!=""'>
            and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag!=null and viewFlag!=""'>
            and view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag!=null and mainFlag!=""'>
            and main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes!=null and codeAttributes!=""'>
            and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
    </select>
</mapper>