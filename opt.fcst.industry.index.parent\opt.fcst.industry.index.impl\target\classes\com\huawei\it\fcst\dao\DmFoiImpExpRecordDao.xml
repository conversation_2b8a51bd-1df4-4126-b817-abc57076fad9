<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IDmFoiImpExpRecordDao">
    <resultMap type="com.huawei.it.fcst.industry.index.vo.common.DmFoiImpExpRecordVO" id="resultMap">
        <result property="endDate" column="end_date"/>
        <result property="optType" column="opt_type"/>
        <result property="recSts" column="rec_sts"/>
        <result property="delFlag" column="del_flag"/>
        <result property="pageModule" column="page_module"/>
        <result property="fileName" column="file_name"/>
        <result property="exceptionFeedback" column="exception_feedback"/>
        <result property="recordNum" column="record_num"/>
        <result property="creationDate" column="creation_date"/>
        <result property="createdBy" column="created_by"/>
        <result property="pageModule" column="page_module"/>
        <result property="fileSize" column="file_size"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="fileErrorKey" column="file_error_key"/>
        <result property="id" column="id"/>
        <result property="fileSourceKey" column="file_source_key"/>
        <result property="periodId" column="period_id"/>
        <result property="status" column="status"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="fileType" column="fileType"/>
    </resultMap>

    <insert id="insertStatisticsImportRecord">
        insert into fin_dm_opt_foi.dm_foi_imp_exp_record_t(id, file_name, file_size, status, exception_feedback,
                                                         record_num, created_by, creation_date,
                                                         last_updated_by, end_date, last_update_date,del_flag, page_module,file_source_key,file_error_key,opt_type,rec_sts,file_type)
        values (fin_dm_opt_foi.dm_foi_imp_and_exp_record_s.NEXTVAL, #{fileName}, #{fileSize}, #{status}, #{exceptionFeedback},
                #{recordNum}, #{createdBy}, #{creationDate}, #{lastUpdatedBy}, #{lastUpdateDate},#{lastUpdateDate}, 'N',
                #{pageModule},#{fileSourceKey},#{fileErrorKey},#{optType},#{recSts},#{fileType})
    </insert>

    <insert id="insertStatisticsExportRecord">
        insert into fin_dm_opt_foi.dm_foi_imp_exp_record_t(id, file_name, file_size, status, exception_feedback,
                                                 record_num, created_by, creation_date,
                                                 last_updated_by, end_date,last_update_date, del_flag, page_module,file_source_key,opt_type,rec_sts,file_type)
        values (fin_dm_opt_foi.dm_foi_imp_and_exp_record_s.NEXTVAL, #{fileName}, #{fileSize}, #{status}, #{exceptionFeedback},
                #{recordNum}, #{createdBy}, #{creationDate}, #{lastUpdatedBy}, #{lastUpdateDate},#{lastUpdateDate}, 'N',
                #{pageModule},#{fileSourceKey},#{optType},#{recSts},#{fileType})
    </insert>


</mapper>
