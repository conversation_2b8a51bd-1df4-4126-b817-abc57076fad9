/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.vo.config;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * DmFcstIctRawDataExamineDTO Class
 *
 * <AUTHOR>
 * @since 2024/11/7
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "底层数据审视出参VO")
public class DmRawDataExamineDTO {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("bg编码")
    private String bgCode;

    @ApiModelProperty("bg名称")
    private String bgCnName;

    @ApiModelProperty("l1编码")
    private String lv1Code;

    @ApiModelProperty("l1名称")
    private String lv1CnName;

    @ApiModelProperty("l2编码")
    private String lv2Code;

    @ApiModelProperty("l2名称")
    private String lv2CnName;

    @ApiModelProperty("l3编码")
    private String lv3Code;

    @ApiModelProperty("l3名称")
    private String lv3CnName;

    @ApiModelProperty("l4编码")
    private String lv4Code;

    @ApiModelProperty("l4名称")
    private String lv4CnName;

    @ApiModelProperty("地区部编码")
    private String regionCode;

    @ApiModelProperty("修改理由")
    private String modifyReason;

    @ApiModelProperty("修改理由")
    private String modifyReasonM;

    @ApiModelProperty("撤销理由")
    private String modifyReasonR;

    @ApiModelProperty("操作类型")
    private String modifyType;

    @ApiModelProperty("开始时间")
    private Long beginDate;

    @ApiModelProperty("结束时间")
    private Long endDate;

    @ApiModelProperty("开始时间")
    private Long begin;

    @ApiModelProperty("结束时间")
    private Long end;

    @ApiModelProperty("地区部名称")
    private String regionCnName;

    /**
     * 代表处编码
     **/
    @ApiModelProperty("代表处编码")
    private String repofficeCode;

    /**
     * 代表处中文名称
     **/
    @ApiModelProperty("代表处名称")
    private String repofficeCnName;

    /**
     * 国内海外标识
     **/
    @ApiModelProperty("国内/海外")
    private String overseaFlag;

    @ApiModelProperty("spart编码")
    private String spartCode;

    @ApiModelProperty("合同号")
    private String hwContractNum;

    @ApiModelProperty("版本号")
    private Long versionId;

    @ApiModelProperty("版本")
    private String version;

    @ApiModelProperty(value = "刷新系统状态")
    private String statusFlag;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date creationDate;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String lastUpdatedBy;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdateDate;

    @ApiModelProperty(value = "标识")
    private String delFlag;

    @ApiModelProperty(value = "操作人")
    private String createdByStr;

    @ApiModelProperty(value = "更新人")
    private String lastUpdatedByStr;

    @ApiModelProperty(value = "页面")
    private String pageFlag;

    @ApiModelProperty(value = "错误信息")
    private String errorMessage;

    @ApiModelProperty(value = "l1编码")
    private String lv1ProdListCode;

    private String lv1ProdListCnName;

    private String lv2ProdListCode;

    private String lv2ProdListCnName;

    private String lv3ProdListCode;

    private String lv3ProdListCnName;

    private String lv4ProdListCode;

    private String lv4ProdListCnName;

    @ApiModelProperty("大T系统部编码")
    private String signTopCustCategoryCode;

    @ApiModelProperty("大T系统部名称")
    private String signTopCustCategoryCnName;

    @ApiModelProperty("子网系统部名称")
    private String signSubsidiaryCustcatgCnName;

}
