/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.ciphertext;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

/**
 * FunctionParamVOTest Class
 *
 * <AUTHOR>
 * @since 2023/5/9
 */
public class FunctionParamVOTest extends BaseVOCoverUtilsTest<FunctionParamVO> {

    @Override
    protected Class<FunctionParamVO> getTClass() {
        return FunctionParamVO.class;
    }
}