/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.config;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.huawei.it.fcst.common.StatisticsExcelService;
import com.huawei.it.fcst.constant.Constants;
import com.huawei.it.fcst.export.vo.PbiDmFoiImpExpRecordVO;
import com.huawei.it.fcst.constant.Constant;
import com.huawei.it.fcst.industry.pbi.dao.ICostReductDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstDataRefreshStatusDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstVersionInfoDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmfcstIctCodeReplInfoDao;
import com.huawei.it.fcst.industry.pbi.dao.IctProdMainCodeDimDao;
import com.huawei.it.fcst.industry.pbi.impl.costReduct.CostReductImportListener;
import com.huawei.it.fcst.industry.pbi.impl.replace.CodeReplaceImportListener;
import com.huawei.it.fcst.industry.pbi.service.common.IIctCommonService;
import com.huawei.it.fcst.industry.pbi.utils.ExcelCellWidthStyleStrategy;
import com.huawei.it.fcst.industry.pbi.utils.ExcelStyleUtils;
import com.huawei.it.fcst.industry.pbi.utils.FcstIndustryUtil;
import com.huawei.it.fcst.industry.pbi.vo.common.DmFcstDataRefreshStatus;
import com.huawei.it.fcst.industry.pbi.vo.config.CostReductImportVO;
import com.huawei.it.fcst.industry.pbi.vo.config.ImportContextVO;
import com.huawei.it.fcst.industry.pbi.vo.config.MainCodeImportVO;
import com.huawei.it.fcst.industry.pbi.vo.replace.ReplaceImportVO;
import com.huawei.it.fcst.util.FileProcessUtis;
import com.huawei.it.fcst.util.UserInfoUtils;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.core.util.PathUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import static java.io.File.separator;

import javax.inject.Inject;
import javax.inject.Named;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.Date;

/**
 * 【产业成本指数-ICT经管】配置管理异步服务类
 *
 * <AUTHOR>
 * @since 2024-09-16
 */
@Slf4j
@Named
@EnableAsync
public class AsyncIctConfigService {

    @Inject
    private IDmFcstVersionInfoDao versionInfoDao;

    @Inject
    private IctProdMainCodeDimDao prodMainCodeDimDao;

    @Inject
    private IDmfcstIctCodeReplInfoDao dmfcstIctCodeReplInfoDao;

    @Inject
    private ICostReductDao costReductDao;

    @Inject
    private IDmfcstIctCodeReplInfoDao codeReplInfoDao;

    @Inject
    private IDmFcstDataRefreshStatusDao dataRefreshStatusDao;

    @Inject
    private IIctCommonService ictCommonService;

    @Inject
    private StatisticsExcelService statisticsExcelService;

    /**
     * 异步导入主力产品主力编码记录（全量覆盖当前版本的数据）
     *
     * @param attachment 上传的附件
     * @param importContextVO vo
     */
    @Async("asyncServiceExecutor")
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void importMainCodeDimVOList(Attachment attachment, ImportContextVO importContextVO) throws Exception {
        log.info(">>>Begin AsyncIctConfigService::importMainCodeDimVOList");
        RequestContextManager.setCurrent(importContextVO.getContext());
        long startTime = System.currentTimeMillis();
        // 导入所需上下文信息
        // 创建版本信息
        Long newVersionId = ictCommonService.createNewVersionInfo("MAIN_DIM");
        importContextVO.setNewVersionId(newVersionId);
        // 个人中心导入所需文件
        File file = new File(PathUtil.getTempPathByDay(Constants.DEF.getValue()) + separator + importContextVO.getUploadInfoVO().getFileName());
        FileOutputStream out = null;
        try {
            out = new FileOutputStream(file);
            // 写入文件
            ExcelWriter workbookWriter = getExcelWriter(out, MainCodeImportVO.class);
            importContextVO.setWorkbookWriter(workbookWriter);
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "主力产品主力编码").build();
            importContextVO.setWriteSheet(writeSheet);
            // 预测量表导入监听器
            MainCodeImportListener mainCodeImportListener = new MainCodeImportListener(importContextVO, prodMainCodeDimDao,dmfcstIctCodeReplInfoDao);
            // 获取excel传入的文件流
            EasyExcel.read(attachment.getDataHandler().getInputStream())
                    .registerReadListener(mainCodeImportListener)
                    .head(MainCodeImportVO.class)
                    .sheet(0) // 导入数据的sheet页编号，0代表第一个sheet页，如果不填，则会导入所有sheet页的数据
                    .doRead();
            // 关闭writer
            workbookWriter.close();
            // 刷新流
            out.flush();
            // 设置个人中心上传信息
            uploadPersonCenter(importContextVO, file, attachment);
        } catch (Exception ex) {
            log.error("AsyncIctConfigService::importMainCodeDimVOList Exception:", ex);
            // 删除版本信息和数据
            delMainCodeVersionData(newVersionId);
            // 更新任务状态表
            updateDataRefreshStatus(importContextVO.getDataRefreshStatus(), Constant.TaskStatus.TASK_FAIL.getValue());
        } finally {
            // 清除临时文件
            deleteFile(file);
            closeOutputStream(out);
            // 清除公服缓存
            RequestContextManager.removeCurrent();
            log.info("End AsyncIctConfigService::importMainCodeDimVOList and total time:{}", (System.currentTimeMillis() - startTime));
        }
    }

    private void uploadPersonCenter(ImportContextVO importContextVO, File file, Attachment attachment)
            throws ApplicationException, UnsupportedEncodingException {
        // 上传文件至S3服务器
        String fileSourceKey = getFileSourceKey(attachment, file, importContextVO);
        PbiDmFoiImpExpRecordVO recordVO = new PbiDmFoiImpExpRecordVO();
        recordVO.setFileSourceKey(fileSourceKey);
        recordVO.setFileSize(String.valueOf(file.length() / 1024));
        recordVO.setRecordNum(Long.valueOf(importContextVO.getTotalNum()));
        recordVO.setFileName(importContextVO.getUploadInfoVO().getFileName());
        recordVO.setCreationDate(importContextVO.getUploadInfoVO().getCreationDate());
        recordVO.setLastUpdateDate(FcstIndustryUtil.getCurTime());
        // 无问题导入成功
        if (StringUtils.isBlank(importContextVO.getErrorMsg()) && importContextVO.getTotalNum() != 0) {
            // 导入成功信息记录
            statisticsExcelService.insertImportExcel(recordVO, importContextVO.getUploadInfoVO(), true, importContextVO.getTotalNum());
            updateDataRefreshStatus(importContextVO.getDataRefreshStatus(), Constant.TaskStatus.TASK_SUCCESS.getValue());
        } else {
            // 文件错误或者行数为0
            log.info(">>>>>The errorMsg is: {}", importContextVO.getErrorMsg());
            String errorMsg = importContextVO.getErrorMsg();
            if (importContextVO.getTotalNum() == 0) {
                errorMsg = "导入文件数据为空";
            }
            recordVO.setExceptionFeedback(getErrorModelFeedback(errorMsg));
            recordVO.setFileErrorKey(recordVO.getFileSourceKey());
            recordVO.setFileSourceKey("");
            statisticsExcelService.insertImportExcel(recordVO, importContextVO.getUploadInfoVO(), false, importContextVO.getTotalNum());
            throw new CommonApplicationException("输入值不合法");
        }
    }

    private String getFileSourceKey(Attachment attachment, File file, ImportContextVO importContextVO) {
        String fileSourceKey = null;
        try {
            if (importContextVO.getTotalNum() == 0) {
                File tempFile = new File(PathUtil.getTempPathByDay(Constants.DEF.getValue()) + separator + importContextVO.getUploadInfoVO().getFileName() + ".xlsx");
                FileUtils.copyInputStreamToFile(attachment.getDataHandler().getInputStream(), tempFile);
                fileSourceKey = FileProcessUtis.uploadToS3(tempFile, String.valueOf(UserInfoUtils.getUserId()));
                deleteFile(tempFile);
            } else {
                fileSourceKey = FileProcessUtis.uploadToS3(file, String.valueOf(UserInfoUtils.getUserId()));
            }
        } catch (Exception ex) {
            log.error(">>>AsyncIctConfigService::getFileSourceKey Exception: {}", ex.getMessage());
        }
        return fileSourceKey;
    }

    private void delMainCodeVersionData(Long versionId) {
        // 删除版本信息
        versionInfoDao.deleteDmFcstVersionInfoById(versionId);
        // 删除导入的数据信息
        prodMainCodeDimDao.deleteMainCodeDimVOsByVersionId(versionId);
    }

    private void delCostRedVersionData(Long versionId) {
        // 删除版本信息
        versionInfoDao.deleteDmFcstVersionInfoById(versionId);
        // 删除导入的数据信息
        costReductDao.deleteCostRedDataListByVersionId(versionId);
    }

    private void delCodeReplaceVersionData(Long versionId) {
        // 删除版本信息
        versionInfoDao.deleteDmFcstVersionInfoById(versionId);
        // 删除导入的数据信息
        codeReplInfoDao.delCodeReplInfoListByVersionId(versionId);
    }

    private void closeOutputStream(FileOutputStream out) {
        if (out != null) {
            try {
                out.close();
            } catch (IOException e) {
                log.error("Stream 关闭失败");
            }
        }
    }

    private String getErrorModelFeedback(String errorMsg) throws UnsupportedEncodingException {
        if (errorMsg.getBytes("UTF-8").length >= 2000) {
            return errorMsg.substring(0, 666);
        }
        return errorMsg;
    }

    // 更新异步导入表
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updateDataRefreshStatus(DmFcstDataRefreshStatus dataRefreshStatus, String status) {
        dataRefreshStatus.setStatus(status);
        dataRefreshStatus.setLastUpdateDate(new Date());
        dataRefreshStatusDao.updateDmFcstDataRefreshStatus(dataRefreshStatus);
    }

    private void deleteFile(File file) {
        if (file != null && file.exists()) {
            if (!file.delete()) {
                log.error("delete file failed");
            }
        }
    }

    /**
     * 异步导入降成本目标（全量覆盖当前版本的数据）
     *
     * @param attachment 上传的附件
     * @param importContextVO importContextVO
     */
    @Async("asyncServiceExecutor")
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void importCostReductData(Attachment attachment, ImportContextVO importContextVO) {
        log.info(">>>Begin AsyncIctConfigService::importCostReductData");
        RequestContextManager.setCurrent(importContextVO.getContext());
        long startTime = System.currentTimeMillis();
        // 导入所需上下文信息
        // 创建版本信息
        Long newVersionId = ictCommonService.createNewVersionInfo("RED_DIM");
        importContextVO.setNewVersionId(newVersionId);
        // 个人中心导入所需文件
        File file = new File(PathUtil.getTempPathByDay(Constants.DEF.getValue()) + separator + importContextVO.getUploadInfoVO().getFileName());
        FileOutputStream out = null;
        try {
            out = new FileOutputStream(file);
            // 写入文件
            ExcelWriter workbookWriter = getExcelWriter(out, CostReductImportVO.class);
            importContextVO.setWorkbookWriter(workbookWriter);
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "降成本目标").build();
            importContextVO.setWriteSheet(writeSheet);
            // 预测量表导入监听器
            CostReductImportListener costReductImportListener = new CostReductImportListener(importContextVO, costReductDao);
            // 获取excel传入的文件流
            EasyExcel.read(attachment.getDataHandler().getInputStream())
                    .registerReadListener(costReductImportListener)
                    .head(CostReductImportVO.class)
                    .sheet(0)
                    .doRead();
            // 关闭writer
            workbookWriter.close();
            // 刷新流
            out.flush();
            // 设置个人中心上传信息
            uploadPersonCenter(importContextVO, file, attachment);
        } catch (Exception ex) {
            log.error("AsyncIctConfigService::importCostReductData Exception: {}", ex.getMessage());
            // 删除版本信息和数据
            delCostRedVersionData(newVersionId);
            // 更新任务状态表
            updateDataRefreshStatus(importContextVO.getDataRefreshStatus(), Constant.TaskStatus.TASK_FAIL.getValue());
        } finally {
            // 清除临时文件
            deleteFile(file);
            closeOutputStream(out);
            // 清除公服缓存
            RequestContextManager.removeCurrent();
            log.info("End AsyncIctConfigService::importCostReductData and total time:{}", (System.currentTimeMillis() - startTime));
        }
    }

    /**
     * 异步导入新旧编码替换（全量覆盖当前版本的数据）
     *
     * @param attachment 上传的附件
     * @param importContextVO dataRefreshStatus
     */
    @Async("asyncServiceExecutor")
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void importReplaceInfoList(Attachment attachment, ImportContextVO importContextVO) {
        log.info(">>>Begin AsyncIctConfigService::importReplaceInfoList");
        RequestContextManager.setCurrent(importContextVO.getContext());
        long startTime = System.currentTimeMillis();
        // 导入所需上下文信息
        // 创建版本信息
        Long newVersionId = ictCommonService.createNewVersionInfo("REPLACE_DIM");
        importContextVO.setNewVersionId(newVersionId);
        // 个人中心导入所需文件
        File file = new File(PathUtil.getTempPathByDay(Constants.DEF.getValue()) + separator + importContextVO.getUploadInfoVO().getFileName());
        FileOutputStream out = null;
        try {
            out = new FileOutputStream(file);
            // 写入文件
            ExcelWriter workbookWriter = getExcelWriter(out, ReplaceImportVO.class);
            importContextVO.setWorkbookWriter(workbookWriter);
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "新旧编码替换").build();
            importContextVO.setWriteSheet(writeSheet);
            // 预测量表导入监听器
            CodeReplaceImportListener codeReplaceImportListener = new CodeReplaceImportListener(importContextVO, codeReplInfoDao);
            // 获取excel传入的文件流
            EasyExcel.read(attachment.getDataHandler().getInputStream())
                    .registerReadListener(codeReplaceImportListener)
                    .head(ReplaceImportVO.class)
                    .sheet(0)
                    .doRead();
            // 关闭writer
            workbookWriter.close();
            // 刷新流
            out.flush();
            // 设置个人中心上传信息
            uploadPersonCenter(importContextVO, file, attachment);
        } catch (Exception ex) {
            log.error("AsyncIctConfigService::importReplaceInfoList Exception: {}", ex.getMessage());
            // 删除版本信息和数据
            delCodeReplaceVersionData(newVersionId);
            // 更新任务状态表
            updateDataRefreshStatus(importContextVO.getDataRefreshStatus(), Constant.TaskStatus.TASK_FAIL.getValue());
        } finally {
            // 清除临时文件
            deleteFile(file);
            closeOutputStream(out);
            // 清除公服缓存
            RequestContextManager.removeCurrent();
            log.info("End AsyncIctConfigService::importReplaceInfoList and total time:{}", (System.currentTimeMillis() - startTime));
        }
    }

    private ExcelWriter getExcelWriter(FileOutputStream out, Class<?> clazz) {
        HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(
                ExcelStyleUtils.getHeadStyle(), ExcelStyleUtils.getContentStyle());
        return EasyExcel.write(out, clazz)
                .registerWriteHandler(horizontalCellStyleStrategy)
                .registerWriteHandler(new ExcelCellWidthStyleStrategy())
                .build();
    }

}