/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.common;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;
import org.apache.poi.ss.usermodel.CellType;
import org.junit.Assert;
import org.junit.Test;

/**
 * HeaderVoTest Class
 *
 * <AUTHOR>
 * @since 2023/4/22
 */
public class HeaderVoTest extends BaseVOCoverUtilsTest<HeaderVo> {

    @Override
    protected Class<HeaderVo> getTClass() {
        return HeaderVo.class;
    }

    @Test
    public void testMethod() {
        HeaderVo headerVo = new HeaderVo();
        headerVo.setDataType(CellType.NUMERIC);
        Assert.assertNotNull(headerVo);
    }
}