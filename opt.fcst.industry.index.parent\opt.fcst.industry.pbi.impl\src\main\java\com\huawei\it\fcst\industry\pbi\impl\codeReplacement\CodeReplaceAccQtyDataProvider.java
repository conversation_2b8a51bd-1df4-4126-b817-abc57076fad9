/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.codeReplacement;

import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.industry.pbi.dao.ICodeReplacementDao;
import com.huawei.it.fcst.industry.pbi.utils.CostReductUtils;
import com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementExpVO;
import com.huawei.it.fcst.industry.pbi.vo.codeReplacement.CodeReplacementVO;
import com.huawei.it.jalor5.core.base.PageVO;

import java.io.Serializable;
import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

/**
 * 导出服务-月度累计新老编码发货量分布
 */
@Named("IExcelExport.CodeReplaceAccQtyDataProvider")
public class CodeReplaceAccQtyDataProvider extends CodeReplaceQtyDataProvider {
    @Inject
    private ICodeReplacementDao iCodeReplacementDao;

    @Inject
    private CodeReplacementService codeReplacementService;

    // 普通查询
    @Override
    public List<?> getData(Serializable conditionObject, PageVO pageVO) {
        CodeReplacementExpVO dataVO = (CodeReplacementExpVO) conditionObject;
        List<CodeReplacementExpVO> spartIndexCostList = null;
        if (CostReductUtils.getSpecailRoleMark(dataVO)) {
            return spartIndexCostList;
        }
        // 预处理的虚化
        if (dataVO.getPreCus()) {
            CostReductUtils.getPreCusIndexCostList(dataVO);
            spartIndexCostList = iCodeReplacementDao.getPreBlurSpartIndexAccQtyExpList(dataVO);
        } else {
            spartIndexCostList = codeReplaceAccQtyData(dataVO, spartIndexCostList);
        }
        return spartIndexCostList;
    }

    private List<CodeReplacementExpVO> codeReplaceAccQtyData(CodeReplacementExpVO dataVO, List<CodeReplacementExpVO> spartIndexCostList) {
        // 选择层级可分为SPART层级与PBI维度层级，区别在于关系、替代关系与新旧SpartCode是否有值。
        String selectionLevel = dataVO.getQueryLevel();
        String isNeedBlur = dataVO.getIsNeedBlur();
        // 选择层级可分为SPART层级与PBI维度层级，区别在于关系、替代关系与新旧SpartCode是否有值。
        if (CommonConstant.SELECTION_LEVEL_SPART_LEVEL.equals(selectionLevel)) {
            // 是否虚化
            if (Boolean.TRUE.equals(Boolean.valueOf(isNeedBlur))) {
                spartIndexCostList = iCodeReplacementDao.getBlurSpartIndexAccQtyExpList(dataVO,
                        CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()), CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()));
            } else {
                spartIndexCostList = iCodeReplacementDao.getSpartIndexAccQtyExpList(dataVO,
                        CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()), CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()));
            }
        } else if (CommonConstant.SELECTION_LEVEL_TOP_SPART_LEVEL.equals(selectionLevel)) {
            // topSpart 有 4情况。 此处做按单个的虚化属性做数据查询
            if (isNeedBlur.split("\\|").length == 2) {
                List<CodeReplacementVO> shipNewDataInfo = codeReplacementService.getShipAccDataInfo(dataVO,
                        Boolean.valueOf(isNeedBlur.split("\\|")[0]), CostReductUtils.getSpartCodeStr(dataVO.getNewSpartCode()),
                        dataVO.getNewCustomId(), dataVO.getNewProdTeamCode());
                List<CodeReplacementVO> shipOldDataInfo = codeReplacementService.getShipAccDataInfo(dataVO,
                        Boolean.valueOf(isNeedBlur.split("\\|")[1]), CostReductUtils.getSpartCodeStr(dataVO.getOldSpartCode()),
                        dataVO.getOldCustomId(), dataVO.getOldProdTeamCode());
                spartIndexCostList = CostReductUtils.processShipList(shipNewDataInfo, shipOldDataInfo);
            }
        } else if (CommonConstant.SELECTION_LEVEL_PBI_LEVEL.equals(selectionLevel)) {
            spartIndexCostList = iCodeReplacementDao.getPbiIndexAccQtyExpList(dataVO);
        }
        return spartIndexCostList;
    }

}
