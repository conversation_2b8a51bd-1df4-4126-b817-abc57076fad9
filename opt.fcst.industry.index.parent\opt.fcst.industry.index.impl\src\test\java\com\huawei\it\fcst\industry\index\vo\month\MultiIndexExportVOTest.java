/*
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.month;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @since 2023/5/9
 */
public class MultiIndexExportVOTest extends BaseVOCoverUtilsTest<MultiIndexExportVO> {
    @Override
    protected Class<MultiIndexExportVO> getTClass() { return MultiIndexExportVO.class; }

    @Test
    public void testMethod() {
        MultiIndexExportVO dmFocActualCostVO = new MultiIndexExportVO();
        dmFocActualCostVO.setPeriodId(2023L);
        dmFocActualCostVO.getPeriodId();
        dmFocActualCostVO.setGroupCnName("天线配件");
        dmFocActualCostVO.getGroupCnName();
        dmFocActualCostVO.setCostIndex(116.2);
        dmFocActualCostVO.getCostIndex();
        dmFocActualCostVO.getCostType();
        dmFocActualCostVO.setCostType("11");
        dmFocActualCostVO.getParentCnName();
        dmFocActualCostVO.setParentCnName("666");
        MultiIndexExportVO.builder().periodId(2011L).costIndex(11.0)
            .costType("11").groupCnName("33").parentCnName("44").build();
        Assert.assertNotNull(dmFocActualCostVO);
    }
}