/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.config;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * DmFocReplaceVO Class
 *
 * <AUTHOR>
 * @since 2024/7/10
 */
@Getter
@Setter
@NoArgsConstructor
public class DmFcstIctRawDataExamineDTO {

    private Long id;

    private String bgCode;

    private String bgCnName;

    /**
     * 目录树
     **/
    @ApiModelProperty("granularityType")
    private String granularityType;

    private String lv1Code;

    private String lv1CnName;

    private String lv2Code;

    private String lv2CnName;

    private String lv3Code;

    private String lv3CnName;

    private String lv4Code;

    private String lv4CnName;

    private String regionCode;

    /**
     * 修改理由
     **/
    @ApiModelProperty("modify_reason")
    private String modifyReason;

    private String modifyReasonM;

    private String modifyReasonR;

    /**
     * 修改类型
     **/
    @ApiModelProperty("modify_type")
    private String modifyType;

    /**
     * 开始时间
     **/
    @ApiModelProperty("begin_date")
    private Long beginDate;

    /**
     * 结束时间
     **/
    @ApiModelProperty("end_date")
    private Long endDate;

    private Long begin;

    private Long end;

    /**
     * 地区部中文名称
     **/
    @ApiModelProperty("region_cn_name")
    private String regionCnName;

    /**
     * 代表处编码
     **/
    @ApiModelProperty("repoffice_code")
    private String repofficeCode;

    /**
     * 代表处中文名称
     **/
    @ApiModelProperty("repoffice_cn_name")
    private String repofficeCnName;

    /**
     * 国内海外标识
     **/
    @ApiModelProperty("oversea_flag")
    private String overseaFlag;

    @ApiModelProperty(value = "成本类型")
    private String costType;

    private String spartCode;

    private String hwContractNum;

    private Long versionId;

    private String version;

    @ApiModelProperty(value = "刷新系统状态")
    private String statusFlag;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date creationDate;

    private String createdBy;

    private String lastUpdatedBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdateDate;

    private String delFlag;
    @ApiModelProperty(value = "操作人")
    private String createdByStr;

    private String lastUpdatedByStr;

    private String pageFlag;

    private String errorMessage;

    private String lv1ProdRndTeamCode;

    private String lv1ProdRdTeamCnName;

    private String lv2ProdRndTeamCode;

    private String lv2ProdRdTeamCnName;

    private String lv3ProdRndTeamCode;

    private String lv3ProdRdTeamCnName;

    private String lv4ProdRndTeamCode;

    private String lv4ProdRdTeamCnName;

    private String lv1IndustryCatgCode;

    private String lv1IndustryCatgCnName;

    private String lv2IndustryCatgCode;

    private String lv2IndustryCatgCnName;

    private String lv3IndustryCatgCode;

    private String lv3IndustryCatgCnName;

    private String lv4IndustryCatgCode;

    private String lv4IndustryCatgCnName;

    private String lv1ProdListCode;

    private String lv1ProdListCnName;

    private String lv2ProdListCode;

    private String lv2ProdListCnName;

    private String lv3ProdListCode;

    private String lv3ProdListCnName;

    private String lv4ProdListCode;

    private String lv4ProdListCnName;

}
