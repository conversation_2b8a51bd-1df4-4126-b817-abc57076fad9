/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.calculate;

import com.huawei.it.fcst.enums.CommonConstEnum;
import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstBaseCusDimDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstBaseCusUserDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstCodeReplInfoDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstIndusDimInfoDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstIrbDimInfoDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstProdDimInfoDao;
import com.huawei.it.fcst.industry.pbi.enums.GroupLevelEnum;
import com.huawei.it.fcst.industry.pbi.enums.IndustryConstEnum;
import com.huawei.it.fcst.industry.pbi.impl.common.DropDownService;
import com.huawei.it.fcst.industry.pbi.service.calculate.ISummaryCalculateService;
import com.huawei.it.fcst.industry.pbi.utils.FcstIndustryUtil;
import com.huawei.it.fcst.industry.pbi.vo.common.CommonBaseVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.BaseCusDimVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.CodeReplInfoVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstBaseCusDimVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstBaseCusUserVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstDimInfoVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.InterLockInfoVO;
import com.huawei.it.fcst.util.ObjectCopyUtil;
import com.huawei.it.fcst.util.UserInfoUtils;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.security.RoleVO;
import com.huawei.it.jalor5.security.UserVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Named;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/6/28
 */
@Named("summaryCalculateService")
@JalorResource(code = "summaryCalculateService", desc = "NEW ICT-虚化汇总计算")
public class SummaryCalculateService implements ISummaryCalculateService {

    @Autowired
    private IDmFcstIrbDimInfoDao dmFcstIrbDimInfoDao;

    @Autowired
    private IDmFcstIndusDimInfoDao dmFcstIndusDimInfoDao;

    @Autowired
    private IDmFcstProdDimInfoDao dmFcstProdDimInfoDao;

    @Autowired
    private IDmFcstBaseCusDimDao dmFcstBaseCusDimDao;

    @Autowired
    private IDmFcstCodeReplInfoDao iDmFcstCodeReplInfoDao;

    @Autowired
    private IDmFcstBaseCusUserDao dmFcstBaseCusUserDao;

    @Autowired
    private DropDownService dropDownService;

    /**
     * 简单说就是spart维度或者量纲维度只有有选的时候才有可能存在虚化逻辑，如果spart维度和量纲维度都不选则一定不存在虚化逻辑
     * 前置条件：1：先判断主力编码："N"的时候有可能触发虚化，需要校验spart维度或者量纲维度数据量,"Y"的时候不可能触发虚化;
     * 2：先要判断LV3.5有没有选，没选并选择了spart维度或者量纲维度才去校验spart维度或者量纲维度数据量,大于0需要虚化;
     *
     * @param commonBaseVO 参数
     * @return
     */
    @Override
    @Audit(module = "summaryCalculateService-dropDownCodeSummaryCalculate", operation = "dropDownCodeSummaryCalculate", message = "年度月度虚化组合计算")
    @JalorOperation(code = "dropDownCodeSummaryCalculate", desc = "年度月度虚化组合计算")
    public ResultDataVO dropDownSummaryCalculate(CommonBaseVO commonBaseVO) throws CommonApplicationException {
        // 入参校验
        verifyParameters(commonBaseVO);
        List<Map<String, Object>> allCalculateMap = new ArrayList<>();
        FcstIndustryUtil.setSpecailCode(commonBaseVO);
        // spart层级可以多选
        if (GroupLevelEnum.SPART.getValue().equals(commonBaseVO.getGroupLevel())) {
            List<String> spartCodeList = commonBaseVO.getSpartCodeList();
            for (String groupCode : spartCodeList) {
                Map<String, Object> calculateSpartMap = new HashMap<>();
                commonBaseVO.setSpartCode(groupCode);
                commonBaseVO.setGroupCode(groupCode);
                List<String> setSpartCodeList = new ArrayList<>();
                setSpartCodeList.add(groupCode);
                commonBaseVO.setSpartCodeList(setSpartCodeList);
                setCalculateMap(commonBaseVO, calculateSpartMap);
                allCalculateMap.add(calculateSpartMap);
            }
        } else {
            // 量纲层级单选
            Map<String, Object> calculateMap = new HashMap<>();
            commonBaseVO.setGroupCode(commonBaseVO.getGroupCodeList().get(0));
            setCalculateMap(commonBaseVO, calculateMap);
            allCalculateMap.add(calculateMap);
        }
        return ResultDataVO.success(allCalculateMap);
    }

    private void verifyParameters(CommonBaseVO commonBaseVO) throws CommonApplicationException {
        if (GroupLevelEnum.SPART.getValue().equals(commonBaseVO.getGroupLevel()) && CollectionUtils.isEmpty(commonBaseVO.getSpartCodeList())) {
            throw new CommonApplicationException("参数错误");
        }
        if (CommonConstant.GROUP_LEVEL_DIMENSION.contains(commonBaseVO.getGroupLevel()) && StringUtils.isAllBlank(commonBaseVO.getDimensionCode(),
                commonBaseVO.getDimensionSubDetailCode(), commonBaseVO.getDimensionSubCategoryCode())) {
            throw new CommonApplicationException("参数错误");
        }
        if (IndustryConstEnum.COST_TYPE.STD.getValue().equals(commonBaseVO.getCostType())) {
            commonBaseVO.setSoftwareMark(IndustryConstEnum.SOFTWARE_MARK.HARDWARE.getValue());
        }
        if (IndustryConstEnum.PAGE_TYPE.ANNUAL.getValue().equals(commonBaseVO.getPageType())) {
            if (IndustryConstEnum.COST_TYPE.PSP.getValue().equals(commonBaseVO.getCostType())
                    && (commonBaseVO.getIsHasSoftWare() != null && commonBaseVO.getIsHasSoftWare()
                    && StringUtils.isBlank(commonBaseVO.getSoftwareMark()))) {
                throw new CommonApplicationException("参数错误");
            }
        } else {
            if (IndustryConstEnum.COST_TYPE.PSP.getValue().equals(commonBaseVO.getCostType())
                    && StringUtils.isBlank(commonBaseVO.getSoftwareMark())) {
                throw new CommonApplicationException("参数错误");
            }
        }
    }

    private void setCalculateMap(CommonBaseVO commonBaseVO, Map<String, Object> calculateMap) throws CommonApplicationException {
        // 获取版本号
        if (null == commonBaseVO.getVersionId()) {
            dropDownService.setVersionId(commonBaseVO);
        }
        int num;
        if ("Y".equals(commonBaseVO.getYtdFlag()) && IndustryConstEnum.PAGE_TYPE.ANNUAL.getValue().equals(commonBaseVO.getPageType())) {
            FcstIndustryUtil.setCommonTablePreFix(commonBaseVO);
            num = dmFcstIrbDimInfoDao.getYtdProdTeamCodeNum(commonBaseVO);
        } else {
            num = dropDownService.getSpartOrDimensionNum(commonBaseVO);
        }
        // spart维度或者量纲维度存在不为空的话，有可能存在虚化
        if (StringUtils.isNotBlank(commonBaseVO.getSpartCode()) || StringUtils.isNotBlank(commonBaseVO.getDimensionCode())
                || StringUtils.isNotBlank(commonBaseVO.getDimensionSubCategoryCode()) || StringUtils.isNotBlank(commonBaseVO.getDimensionSubDetailCode())) {
            // 校验lv3.5有没有选择
            // 多选时
            List<String> lv4ProdRndTeamCodeList = commonBaseVO.getLv4ProdRndTeamCodeList();
            boolean multiSelect = CollectionUtils.isNotEmpty(lv4ProdRndTeamCodeList) && lv4ProdRndTeamCodeList.size() == 1;
            // 单选时
            boolean singleSelect = StringUtils.isNotBlank(commonBaseVO.getLv4ProdRndTeamCode());
            if (multiSelect || singleSelect) {
                // 不用虚化
                String prodRndTeamCode;
                if (CollectionUtils.isNotEmpty(lv4ProdRndTeamCodeList)) {
                    prodRndTeamCode = lv4ProdRndTeamCodeList.get(0);
                } else {
                    prodRndTeamCode = commonBaseVO.getLv4ProdRndTeamCode();
                }
                calculateMap.put("prodRndTeamCode", prodRndTeamCode);
                calculateMap.put("groupCode", commonBaseVO.getGroupCode());
                calculateMap.put("num", num);
                calculateMap.put("status", false);
            } else {
                verifyAndCreatComb(commonBaseVO, calculateMap, num);
            }
        } else {
            // 不用虚化
            calculateMap.put("groupCode", commonBaseVO.getGroupCode());
            calculateMap.put("status", false);
            calculateMap.put("num", num);
        }
    }

    private void verifyAndCreatComb(CommonBaseVO commonBaseVO, Map<String, Object> calculateMap, int num) throws CommonApplicationException {
        // 需要虚化,需要校验一下对应条件下Spart维度或者量纲维度数据量，是否大于1
        if (num > 1) {
            // 处理虚化逻辑
            handleBlurred(commonBaseVO, calculateMap, num);
        } else {
            List<DmFcstDimInfoVO> lv4CodeList;
            if ("Y".equals(commonBaseVO.getYtdFlag()) && IndustryConstEnum.PAGE_TYPE.ANNUAL.getValue().equals(commonBaseVO.getPageType())) {
                FcstIndustryUtil.setCommonTablePreFix(commonBaseVO);
                lv4CodeList = dmFcstIrbDimInfoDao.getYtdProdTeamCode(commonBaseVO);
            } else {
                lv4CodeList =getLv4Code(commonBaseVO);
            }
            List<String> lv4Code = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(lv4CodeList)) {
                lv4Code = lv4CodeList.stream().map(ele -> ele.getProdRndTeamCode()).collect(Collectors.toList());
            }
            calculateMap.put("prodRndTeamCode", lv4Code);
            calculateMap.put("status", false);
            calculateMap.put("num", num);
            calculateMap.put("groupCode", commonBaseVO.getGroupCode());
        }
    }

    private void handleBlurred(CommonBaseVO commonBaseVO, Map<String, Object> calculateMap, int num) {
        calculateMap.put("status", true);
        calculateMap.put("num", num);
        calculateMap.put("groupCode", commonBaseVO.getGroupCode());
        // 计算状态校验 第一步：查询组合表能不能查到历史计算过的记录
        // 第二步：如果记录能查到,再和用户账号做匹配，匹配的上就直接返回已计算,匹配不上的，先新增加一条组合信息绑定到个人，计算状态默认已计算
        // 第三步：如果记录查不到,先新增组合记录，再调用计算，回写计算状态
        BaseCusDimVO baseCusDimVO = ObjectCopyUtil.copy(commonBaseVO, BaseCusDimVO.class);
        FcstIndustryUtil.setLvCode(baseCusDimVO);
        baseCusDimVO.setCodeType("TOP");
        dropDownService.setVersionId(baseCusDimVO);
        if (StringUtils.isBlank(baseCusDimVO.getCodeAttributes()) && "Y".equals(baseCusDimVO.getMainFlag())) {
            baseCusDimVO.setCodeAttributes("全选");
        }
        List<DmFcstBaseCusDimVO> combInfoList = dmFcstBaseCusDimDao.getBaseCusDimInfoList(baseCusDimVO);
        // 由于lv0Code是拼接的，需要将拼接的字符串转换为数组后，匹配
        List<String> paramLv0CodeList = Arrays.asList(baseCusDimVO.getLvCode().replace("'", "").split(","));
        // 获取已有组合
        DmFcstBaseCusDimVO dmFcstCombDimInfo = new DmFcstBaseCusDimVO();
        for (DmFcstBaseCusDimVO dmFcstBaseCusDimVO : combInfoList) {
            String lv0CodeStr = dmFcstBaseCusDimVO.getLvCode().replace("'", "");
            List<String> lv0CodeList = Arrays.asList(lv0CodeStr.split(","));
            if (paramLv0CodeList.containsAll(lv0CodeList) && paramLv0CodeList.size() == lv0CodeList.size()) {
                // 已存在组合
                dmFcstCombDimInfo = dmFcstBaseCusDimVO;
                break;
            }
        }
        // 如果是年度的历史版本无需增加一条个人信息绑定到组合ID（因为暂未开放此功能）
        boolean annualHistoryVersionFlag = IndustryConstEnum.PAGE_TYPE.ANNUAL.getValue().equals(commonBaseVO.getPageType())
                && commonBaseVO.getIsHistoryVersion() != null && commonBaseVO.getIsHistoryVersion();
        if (null != dmFcstCombDimInfo.getCustomId()) {
            // 获取计算状态
            String statusFlag = dmFcstCombDimInfo.getStatusFlag();
            // 组合id
            Long customId = dmFcstCombDimInfo.getCustomId();
            calculateMap.put("calculateStatus", statusFlag);
            calculateMap.put("customId", customId);
            // 设置个人信息
            DmFcstBaseCusUserVO dmFcstBaseCusUserVO = getDmFcstBaseCusUserVO(customId, baseCusDimVO.getCostType());
            // 查询个人信息表
            List<DmFcstBaseCusUserVO> cusIdByUser = dmFcstBaseCusUserDao.getCusIdByUser(dmFcstBaseCusUserVO);
            if (CollectionUtils.isEmpty(cusIdByUser) && !annualHistoryVersionFlag) {
                // 先新增加一条个人信息绑定到组合id
                insertCombUserInfoVO(dmFcstBaseCusUserVO);
            }
        } else {
            if (!annualHistoryVersionFlag) {
                // 查不到记录，新增个人组合记录，计算状态先设置为计算中
                FcstIndustryUtil.setLvCode(baseCusDimVO);
                DmFcstBaseCusDimVO dmFcstBaseCusDimVO = ObjectCopyUtil.copy(baseCusDimVO, DmFcstBaseCusDimVO.class);
                dmFcstBaseCusDimVO.setStatusFlag(CommonConstEnum.STATUS_FLAG.D.getValue());
                dmFcstBaseCusDimVO.setCodeType("TOP");
                // 新增组合
                Long customId = insertBaseCombDimVO(dmFcstBaseCusDimVO);
                // 设置个人信息
                DmFcstBaseCusUserVO dmFcstBaseCusUserVO = getDmFcstBaseCusUserVO(customId, baseCusDimVO.getCostType());
                // 新增个人信息
                insertCombUserInfoVO(dmFcstBaseCusUserVO);
                calculateMap.put("calculateStatus", CommonConstEnum.STATUS_FLAG.D.getValue());
            }
        }
    }

    private List<DmFcstDimInfoVO> getLv4Code(CommonBaseVO commonBaseVO) {
        IndustryConstEnum.GRANULARITY_TYPE granularityType = IndustryConstEnum.getGranularityType(commonBaseVO.getGranularityType());
        List<DmFcstDimInfoVO> irbWithSpartOrDimension = new ArrayList<>();
        switch (granularityType) {
            case IRB:
                irbWithSpartOrDimension = dmFcstIrbDimInfoDao.getLv4CodeWithSpartOrDimension(commonBaseVO);
                break;
            case INDUS:
                irbWithSpartOrDimension = dmFcstIndusDimInfoDao.getLv4CodeWithSpartOrDimension(commonBaseVO);
                break;
            case PROD:
                irbWithSpartOrDimension = dmFcstProdDimInfoDao.getLv4CodeWithSpartOrDimension(commonBaseVO);
                break;
        }
        return irbWithSpartOrDimension;
    }

    @NotNull
    private DmFcstBaseCusUserVO getDmFcstBaseCusUserVO(Long customId, String costType) {
        DmFcstBaseCusUserVO dmFcstBaseCusUserVO = new DmFcstBaseCusUserVO();
        dmFcstBaseCusUserVO.setCustomId(customId);
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        Long userId = UserInfoUtils.getUserId();
        dmFcstBaseCusUserVO.setRoleId(String.valueOf(currentRole.getRoleId()));
        dmFcstBaseCusUserVO.setUserId(String.valueOf(userId));
        dmFcstBaseCusUserVO.setCreatedBy(userId);
        dmFcstBaseCusUserVO.setLastUpdatedBy(userId);
        dmFcstBaseCusUserVO.setDelFlag("N");
        dmFcstBaseCusUserVO.setCostType(costType);
        return dmFcstBaseCusUserVO;
    }

    private Long insertBaseCombDimVO(DmFcstBaseCusDimVO dmFcstCombDimInfo) {
        // 设置组合名称字段
        Long baseCusDimKey = dmFcstBaseCusDimDao.getBaseCusDimKey(dmFcstCombDimInfo.getCostType());
        dmFcstCombDimInfo.setCustomId(baseCusDimKey);
        dmFcstCombDimInfo.setCustomCnName(baseCusDimKey + "");
        Long userId = UserInfoUtils.getUserId();
        dmFcstCombDimInfo.setCreatedBy(userId);
        dmFcstCombDimInfo.setLastUpdatedBy(userId);
        if (StringUtils.isBlank(dmFcstCombDimInfo.getCodeAttributes()) && "Y".equals(dmFcstCombDimInfo.getMainFlag())) {
            dmFcstCombDimInfo.setCodeAttributes("全选");
        }
        dmFcstBaseCusDimDao.createDmFcstCusDimDTO(dmFcstCombDimInfo);
        return baseCusDimKey;
    }

    private void insertCombUserInfoVO(DmFcstBaseCusUserVO dmFcstBaseCusUserVO) {
        dmFcstBaseCusUserDao.createDmFcstCusUserInfoDTO(dmFcstBaseCusUserVO);
    }

    @Override
    @Audit(module = "summaryCalculateService-dropDownCodeReplaceSummaryCalculate", operation = "dropDownCodeReplaceSummaryCalculate", message = "编码替换虚化组合计算")
    @JalorOperation(code = "dropDownCodeReplaceSummaryCalculate", desc = "编码替换虚化组合计算")
    public ResultDataVO dropDownCodeReplaceSummaryCalculate(CodeReplInfoVO codeReplInfoVO) throws CommonApplicationException {
        // 入参校验
        if (StringUtils.isAllBlank(codeReplInfoVO.getReplaceRelationName(), codeReplInfoVO.getSpartCode())) {
            throw new CommonApplicationException("参数错误");
        }
        if (IndustryConstEnum.COST_TYPE.PSP.getValue().equals(codeReplInfoVO.getCostType()) && StringUtils.isBlank(codeReplInfoVO.getSoftwareMark())) {
            throw new CommonApplicationException("参数错误");
        }
        if (IndustryConstEnum.COST_TYPE.STD.getValue().equals(codeReplInfoVO.getCostType())) {
            codeReplInfoVO.setSoftwareMark(IndustryConstEnum.SOFTWARE_MARK.HARDWARE.getValue());
        }
        Map<String, Object> calculateMap = new HashMap<>();
        FcstIndustryUtil.setSpecailCode(codeReplInfoVO);
        // 月度虚化计算校验
        if (StringUtils.isBlank(codeReplInfoVO.getRelationType()) && StringUtils.isBlank(codeReplInfoVO.getReplaceRelationType()) && StringUtils.isBlank(codeReplInfoVO.getReplaceRelationName())) {
            // 校验lv3.5有没有选择
            if (StringUtils.isNotBlank(codeReplInfoVO.getLv4ProdRndTeamCode())) {
                // 不用虚化
                calculateMap.put("status", false);
                calculateMap.put("groupCode", codeReplInfoVO.getGroupCode());
            } else {
                // 需要虚化
                codeReplInfoVO.setPageType(IndustryConstEnum.PAGE_TYPE.MONTH.getValue());
                dropDownService.setVersionId(codeReplInfoVO);
                // 获取数量
                int num = dropDownService.getSpartOrDimensionNum(codeReplInfoVO);
                verifyAndCreatComb(codeReplInfoVO, calculateMap, num);
            }
        } else {
            // 替换关系维表虚化校验
            // 校验lv3.5选择和替换关系名称选择了，不用虚化
            if (StringUtils.isNotBlank(codeReplInfoVO.getLv4ProdRndTeamCode()) && StringUtils.isNotBlank(codeReplInfoVO.getReplaceRelationName())) {
                // 不用虚化
                calculateMap.put("status", false);
                calculateMap.put("groupCode", codeReplInfoVO.getGroupCode());
            } else {
                //  存在虚化，做虚化的数据检验
                vaildCalculate(codeReplInfoVO, calculateMap);
            }
        }
        return ResultDataVO.success(calculateMap);
    }

    private void vaildCalculate(CodeReplInfoVO codeReplInfoVO, Map<String, Object> calculateMap) {
        // 需要虚化校验,校验选择条件下LV3.5的数据是否>1
        CodeReplInfoVO codeReplInfoOld = ObjectCopyUtil.copy(codeReplInfoVO, CodeReplInfoVO.class);
        codeReplInfoOld.setNewSpartCode(null);
        List<DmFcstDimInfoVO> oldLv4CodeList = iDmFcstCodeReplInfoDao.getCoceReplLv4Code(codeReplInfoOld);
        CodeReplInfoVO codeReplInfoNew = ObjectCopyUtil.copy(codeReplInfoVO, CodeReplInfoVO.class);
        codeReplInfoNew.setOldSpartCode(null);
        List<DmFcstDimInfoVO> newLv4CodeList = iDmFcstCodeReplInfoDao.getCoceReplLv4Code(codeReplInfoNew);

        List<DmFcstDimInfoVO> allLv4CodeList = new ArrayList<>();
        allLv4CodeList.addAll(oldLv4CodeList);
        allLv4CodeList.addAll(newLv4CodeList);
        List<DmFcstDimInfoVO> distinctLv4CodeList = allLv4CodeList.stream().distinct().collect(Collectors.toList());
        if (distinctLv4CodeList.size() > 1) {
            calculateMap.put("status", true);
            // 创建虚化组合和设置计算状态
            codeReplaceInsertComb(codeReplInfoVO, calculateMap);
        } else {
            List<String> lv4Code = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(distinctLv4CodeList)) {
                lv4Code = distinctLv4CodeList.stream().map(ele -> ele.getLv4Code()).collect(Collectors.toList());
            }
            calculateMap.put("prodRndTeamCode", lv4Code);
            calculateMap.put("status", false);
        }
    }

    private void codeReplaceInsertComb(CodeReplInfoVO codeReplInfoVO, Map<String, Object> calculateMap) {
        BaseCusDimVO baseCusDimVO = ObjectCopyUtil.copy(codeReplInfoVO, BaseCusDimVO.class);
        FcstIndustryUtil.setLvCode(baseCusDimVO);
        List<DmFcstBaseCusDimVO> combInfoList = dmFcstBaseCusDimDao.getBaseCusDimInfoList(baseCusDimVO);
        if (CollectionUtils.isNotEmpty(combInfoList)) {
            DmFcstBaseCusDimVO dmFcstBaseCusDimVO = combInfoList.get(0);
            // 组合id
            Long customId = dmFcstBaseCusDimVO.getCustomId();
            // 设置个人信息
            DmFcstBaseCusUserVO dmFcstBaseCusUserVO = getDmFcstBaseCusUserVO(customId, baseCusDimVO.getCostType());
            // 查询个人信息表
            List<DmFcstBaseCusUserVO> cusIdByUser = dmFcstBaseCusUserDao.getCusIdByUser(dmFcstBaseCusUserVO);
            if (CollectionUtils.isEmpty(cusIdByUser)) {
                // 新增个人记录信息关系组合id
                insertCombUserInfoVO(dmFcstBaseCusUserVO);
            }
            calculateMap.put("calculateStatus", dmFcstBaseCusDimVO.getStatusFlag());
            calculateMap.put("customId", customId);
        } else {
            // 查不到记录，新增组合记录和个人操作信息记录，计算状态先设置为计算中
            FcstIndustryUtil.setLvCode(baseCusDimVO);
            DmFcstBaseCusDimVO dmFcstBaseCusDimVO = ObjectCopyUtil.copy(baseCusDimVO, DmFcstBaseCusDimVO.class);
            dmFcstBaseCusDimVO.setStatusFlag(CommonConstEnum.STATUS_FLAG.D.getValue());
            // 新增组合
            Long customId = insertBaseCombDimVO(dmFcstBaseCusDimVO);
            // 设置个人信息
            DmFcstBaseCusUserVO dmFcstBaseCusUserVO = getDmFcstBaseCusUserVO(customId, baseCusDimVO.getCostType());
            // 新增个人信息
            insertCombUserInfoVO(dmFcstBaseCusUserVO);
            calculateMap.put("calculateStatus", CommonConstEnum.STATUS_FLAG.D.getValue());
        }
    }

    @Override
    @JalorOperation(code = "dropDownInterLockSummaryCalculate", desc = "勾稽管理虚化组合计算")
    @Audit(module = "summaryCalculateService-dropDownInterLockSummaryCalculate", operation = "dropDownInterLockSummaryCalculate", message = "勾稽管理虚化组合计算")
    public ResultDataVO dropDownInterLockSummaryCalculate(InterLockInfoVO interLockInfoVO) throws CommonApplicationException {
        // 入参校验
        verifyInterLockParameters(interLockInfoVO);
        FcstIndustryUtil.setSpecailCode(interLockInfoVO);
        List<String> costTypeList = interLockInfoVO.getCostTypeList();
        List<CommonBaseVO> commonBaseVOList = new ArrayList<>();
        for (String costType : costTypeList) {
            InterLockInfoVO interLockInfoDto = ObjectCopyUtil.copy(interLockInfoVO, InterLockInfoVO.class);
            interLockInfoDto.setCostTypeList(null);
            interLockInfoDto.setCostType(costType);
            if (IndustryConstEnum.COST_TYPE.STD.getValue().equals(costType)) {
                interLockInfoDto.setSoftwareMark(IndustryConstEnum.SOFTWARE_MARK.HARDWARE.getValue());
            }
            commonBaseVOList.add(interLockInfoDto);
        }
        List<Map<String, Object>> allCalculateMap = new ArrayList<>();
        for (CommonBaseVO baseVO : commonBaseVOList) {
            // spart层级存在多选
            if (GroupLevelEnum.SPART.getValue().equals(interLockInfoVO.getGroupLevel())) {
                List<String> spartCodeList = baseVO.getSpartCodeList();
                for (String spartCode : spartCodeList) {
                    baseVO.setSpartCode(spartCode);
                    baseVO.setGroupCode(spartCode);
                    List<String> setSpartCodeList = new ArrayList<>();
                    setSpartCodeList.add(spartCode);
                    baseVO.setSpartCodeList(setSpartCodeList);
                    Map<String, Object> calculateMap = new HashMap<>();
                    setCalculateMap(baseVO, calculateMap);
                    calculateMap.put("costType", baseVO.getCostType());
                    allCalculateMap.add(calculateMap);
                }
            } else {
                baseVO.setGroupCode(baseVO.getGroupCodeList().get(0));
                Map<String, Object> calculateMap = new HashMap<>();
                setCalculateMap(baseVO, calculateMap);
                calculateMap.put("costType", baseVO.getCostType());
                allCalculateMap.add(calculateMap);
            }
        }
        // 不存在的移除，依据num是否为0
        allCalculateMap.removeIf(model -> model.get("num").equals(0));
        return ResultDataVO.success(allCalculateMap);
    }

    private void verifyInterLockParameters(InterLockInfoVO interLockInfoVO) throws CommonApplicationException {
        // 入参校验
        if (CommonConstant.GROUP_LEVEL_DIMENSION.contains(interLockInfoVO.getGroupLevel()) && StringUtils.isAllBlank(interLockInfoVO.getDimensionCode(),
                interLockInfoVO.getDimensionSubDetailCode(), interLockInfoVO.getDimensionSubCategoryCode())) {
            throw new CommonApplicationException("参数错误");
        }
        if (GroupLevelEnum.SPART.getValue().equals(interLockInfoVO.getGroupLevel()) && CollectionUtils.isEmpty(interLockInfoVO.getSpartCodeList())) {
            throw new CommonApplicationException("参数错误");
        }
        if (IndustryConstEnum.COST_TYPE.PSP.getValue().equals(interLockInfoVO.getCostType()) && StringUtils.isBlank(interLockInfoVO.getSoftwareMark())) {
            throw new CommonApplicationException("参数错误");
        }
    }

    @Override
    @JalorOperation(code = "getLv4CodeList", desc = "获取LV4层级")
    public ResultDataVO getLv4CodeList(InterLockInfoVO interLockInfoVO) {
        List<String> costTypeList = interLockInfoVO.getCostTypeList();
        FcstIndustryUtil.setSpecailCode(interLockInfoVO);
        List<InterLockInfoVO> interLockInfoVOList = new ArrayList<>();
        for (String costType : costTypeList) {
            InterLockInfoVO interLockInfoDto = ObjectCopyUtil.copy(interLockInfoVO, InterLockInfoVO.class);
            interLockInfoDto.setCostTypeList(null);
            interLockInfoDto.setCostType(costType);
            interLockInfoVOList.add(interLockInfoDto);
        }
        List<DmFcstDimInfoVO> lv4CodeList = new ArrayList<>();
        for (InterLockInfoVO baseVO : interLockInfoVOList) {
            List<DmFcstDimInfoVO> lv4Code = getLv4Code(baseVO);
            lv4CodeList.addAll(lv4Code);
        }
        lv4CodeList = lv4CodeList.stream().distinct().collect(Collectors.toList());
        return ResultDataVO.success(lv4CodeList);
    }

}
