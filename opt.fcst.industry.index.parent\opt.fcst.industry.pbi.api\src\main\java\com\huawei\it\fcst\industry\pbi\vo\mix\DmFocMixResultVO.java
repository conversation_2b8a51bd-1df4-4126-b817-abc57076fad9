/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.mix;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;

/**
 * DmFocMixResultVO Class
 *
 * <AUTHOR>
 * @since 2024/7/8
 */
@Getter
@Setter
@NoArgsConstructor
public class DmFocMixResultVO {

    private String periodYear;

    private String periodId;

    private String lv0ProdRndTeamCnName;

    private String lv0ProdRndTeamCode;

    private String lv1ProdRndTeamCnName;

    private String lv1ProdRndTeamCode;

    private String lv2ProdRndTeamCnName;

    private String lv2ProdRndTeamCode;

    private String lv3ProdRndTeamCnName;

    private String lv3ProdRndTeamCode;

    private String lv4ProdRndTeamCode;

    private String lv4ProdRndTeamCnName;

    private String prodRndTeamCode;

    private String prodRndTeamCnName;

    private String groupCode;

    private String groupCnName;

    private String groupLevel;

    private String childGroupCnName;

    private String parentCode;

    private String parentCnName;

    private String parentLevel;

    private String createdBy;

    private Timestamp creationDate;

    private String lastUpdatedBy;

    private Timestamp lastUpdateDdate;

    private String delFlag;

    private String viewFlag;

    private String ytdFlag;

    /**
     * 量纲颗粒度code
     **/
    private String dimensionCode;

    /**
     * 量纲颗粒度名称
     **/
    private String dimensionCnName;

    /**
     * 量纲颗粒度code
     **/
    private String dimensionSubCategoryCode;

    /**
     * 量纲颗粒度名称
     **/
    private String dimensionSubCategoryCnName;

    /**
     * 量纲颗粒度code
     **/
    private String dimensionSubDetailCode;

    /**
     * 量纲颗粒度名称
     **/
    private String dimensionSubDetailCnName;

    /**
     * 金额
     **/
    private Double rmbCostAmt = 0.0D;

    private Double rmbCostAmtAcc = 0.0D;

    private String costIndex;

    private String pspCostIndex;

    private String stdCostIndex;

    private String pspAccCostIndex;

    private String stdAccCostIndex;

    private String weightRate;

    /**
     * 成本类型
     **/
    private String costType;

    private String relationflag;

    // 差异额
    private String gapPspStd;

    // 差异率
    private String ratioPspStd;

    // 月度累计
    private String gapPspStdAcc;

    // 月度累计差异率
    private String ratioPspStdAcc;

    private Long customId;

    // psp成本额
    private Double pspRmbCostAmt = 0.0D;

    private Double pspRmbCostAmtAcc = 0.0D;

    // 标准成本额
    private Double stdRmbCostAmt = 0.0D;

    private Double stdRmbCostAmtAcc = 0.0D;
}
