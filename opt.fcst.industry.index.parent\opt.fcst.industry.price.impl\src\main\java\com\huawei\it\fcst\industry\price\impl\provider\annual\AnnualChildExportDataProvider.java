/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.impl.provider.annual;

import cn.hutool.core.map.MapUtil;
import com.huawei.it.fcst.export.IExcelExportDataProvider;
import com.huawei.it.fcst.export.vo.ExcelExportContext;
import com.huawei.it.fcst.industry.price.dao.IAnnualAmpCustomDao;
import com.huawei.it.fcst.industry.price.dao.IAnnualAmpPriceDao;
import com.huawei.it.fcst.industry.price.enums.GroupLevelEnum;
import com.huawei.it.fcst.industry.price.impl.annual.AnnualAmpPriceService;
import com.huawei.it.fcst.industry.price.utils.FcstIndustryUtil;
import com.huawei.it.fcst.industry.price.vo.annual.AnnualAnalysisVO;
import com.huawei.it.fcst.industry.price.vo.annual.DmFocAnnualAmpVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;

import javax.inject.Inject;
import javax.inject.Named;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * AnnualChildExportDataProvider Class 导出 成本涨跌图多子项
 *
 * <AUTHOR>
 * @since 2024/7/5
 */
@Named("IExcelExport.AnnualChildExportProvider")
public class AnnualChildExportDataProvider implements IExcelExportDataProvider {

    @Inject
    private IAnnualAmpPriceDao annualAmpPriceDao;

    @Inject
    private IAnnualAmpCustomDao annualAmpCustomDao;

    @Inject
    private AnnualAmpPriceService annualAmpPriceService;

    @Override
    public List<?> getData(Serializable conditionObject, PageVO pageVO) throws CommonApplicationException {

        AnnualAnalysisVO annualVO = (AnnualAnalysisVO) conditionObject;
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        BeanUtils.copyProperties(annualVO, annualAnalysisVO);
        List<String> yearList = new ArrayList<>();
        if (!annualAnalysisVO.getIsMultipleSelect()) {
            List<String> threeYears = annualAnalysisVO.getYearList();
            yearList = threeYears.stream().sorted(Comparator.comparing(String::valueOf)).collect(Collectors.toList());
        }
        annualAnalysisVO.setParentCodeList(annualAnalysisVO.getGroupCodeList());
        annualAnalysisVO.setParentLevel(annualAnalysisVO.getGroupLevel());
        annualAnalysisVO.setCustomParentCodeList(annualAnalysisVO.getCustomGroupCodeList());
        String nextGroupLevel = FcstIndustryUtil.getViewNextGroupLevel(annualAnalysisVO);
        annualAnalysisVO.setGroupLevel(nextGroupLevel);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = new ArrayList<>();

        if (annualAnalysisVO.getIsNeedBlur()) {
            // 走虚化且是最细力度的时候，需要取正常结果表数据
            dmFocAnnualAmpVOResult.addAll(multiChildNotContainsCombMinLevelExcel(annualAnalysisVO));
        }
        if (!GroupLevelEnum.SPART.getValue().equals(annualAnalysisVO.getParentLevel()) && CollectionUtils.isNotEmpty(annualAnalysisVO.getParentCodeList())) {
            dmFocAnnualAmpVOResult.addAll(multiChildNotContainsCombExcel(annualAnalysisVO));
        }
        // 设置无效的涨跌幅提示语
        annualAmpPriceService.setNoEffectiveAmp(dmFocAnnualAmpVOResult, annualAnalysisVO, "excel");
        HashMap<String, List<DmFocAnnualAmpVO>> resultColumnMap = dmFocAnnualAmpVOResult.stream().collect(
                Collectors.groupingBy(item -> getGroupKey(item), HashMap::new, Collectors.toList()));
        Set<String> resultRowSet = resultColumnMap.keySet();
        List<Map> dataList = new ArrayList<>();
        setChildDataList(dataList, yearList, resultColumnMap, resultRowSet, annualAnalysisVO);

        return dataList;
    }

    @Override
    public Map<String, Object> getHeader(Serializable conditionObject, Map<String, Object> parameters) throws ApplicationException {
        ExcelExportContext context = (ExcelExportContext) conditionObject;
        AnnualAnalysisVO annualAnalysisVO = (AnnualAnalysisVO) context.getConditionObject();

        // 自定义表头
        Map<String, Object> headMap = new HashMap<>();
        headMap.put("displayName", annualAnalysisVO.getDisplayName());
        headMap.put("name", annualAnalysisVO.getNextGroupName());
        headMap.put("overseaFlagCnName", annualAnalysisVO.getOverseaFlagCnName());
        headMap.put("bgCnName", annualAnalysisVO.getBgCnName());
        headMap.put("actualMonth", annualAnalysisVO.getActualMonth());
        headMap.put("regionCnName", annualAnalysisVO.getRegionCnName());
        headMap.put("repofficeCnName", annualAnalysisVO.getRepofficeCnName());
        headMap.put("signTopCustCategoryCnName", annualAnalysisVO.getSignTopCustCategoryCnName());
        headMap.put("signSubsidiaryCustcatgCnName", annualAnalysisVO.getSignSubsidiaryCustcatgCnName());

        List<String> yearList = annualAnalysisVO.getYearList();
        if (!annualAnalysisVO.getIsMultipleSelect()) {
            List<String> threeYearList = yearList.stream().sorted(Comparator.comparing(String::valueOf)).collect(Collectors.toList());
            for (int i = 0; i < threeYearList.size(); i++) {
                headMap.put("year" + i, threeYearList.get(i));
            }
        } else {
            String year = annualAnalysisVO.getPeriodYear() + "年";
            if (annualAnalysisVO.getPeriodYear().equals(yearList.get(0))) {
                year = year + "YTD";
            }
            headMap.put("year" + 0, year);
        }
        return headMap;
    }

    private List<DmFocAnnualAmpVO> multiChildNotContainsCombExcel(AnnualAnalysisVO analysisVO) {

        return annualAmpPriceDao.multiAmpNormalChart(analysisVO);
    }

    private List<DmFocAnnualAmpVO> multiChildNotContainsCombMinLevelExcel(AnnualAnalysisVO annualAnalysisVO) {
        return annualAmpCustomDao.multiAmpMinLevelChart(annualAnalysisVO);
    }

    private String getGroupKey(DmFocAnnualAmpVO dmFocAnnualAmpVO) {
        return dmFocAnnualAmpVO.getParentCnName() + "_" + dmFocAnnualAmpVO.getParentCode() + "_"  + dmFocAnnualAmpVO.getGroupCnName() + "_" + dmFocAnnualAmpVO.getGroupCode();
    }

    private void setChildDataList(List<Map> dataList, List<String> threeYears, HashMap<String, List<DmFocAnnualAmpVO>> resultColumnMap, Set<String> resultRowSet, AnnualAnalysisVO annualVO) {
        for (String keyStr : resultRowSet) {
            List<DmFocAnnualAmpVO> resultAnnualAmpVO = MapUtil.get(resultColumnMap, keyStr, List.class);
            resultAnnualAmpVO.stream().sorted(Comparator.comparing(DmFocAnnualAmpVO::getPeriodYear));
            Map<String, Object> resultMap = new HashMap<>();
            for (int i = 0; i < resultAnnualAmpVO.size(); i++) {
                String parentCnName = resultAnnualAmpVO.get(i).getParentCnName();
                String prodRndTeamCnName = resultAnnualAmpVO.get(i).getProdRndTeamCnName();
                String groupCode = resultAnnualAmpVO.get(i).getGroupCode();
                String groupCnName = resultAnnualAmpVO.get(i).getGroupCnName();
                String annualAmp = resultAnnualAmpVO.get(i).getAnnualAmp();
                String periodYear = resultAnnualAmpVO.get(i).getPeriodYear();
                if (annualVO.getIsNeedBlur() && GroupLevelEnum.SPART.getValue().equals(annualVO.getParentLevel())) {
                    resultMap.put("parentCnName", prodRndTeamCnName);
                } else {
                    resultMap.put("parentCnName", parentCnName);
                }
                if (GroupLevelEnum.SPART.getValue().equals(annualVO.getGroupLevel())) {
                    resultMap.put("groupCnName", groupCnName);
                    resultMap.put("groupCode", groupCode);
                } else {
                    resultMap.put("groupCnName", groupCnName);
                }
                if (!annualVO.getIsMultipleSelect()) {
                    for (int j = 0; j < threeYears.size(); j++) {
                        if (threeYears.get(j).equals(periodYear)) {
                            resultMap.put("annualAmp" + j, annualAmp != null ? annualAmp : null);
                        }
                    }
                } else {
                    resultMap.put("annualAmp" + 0, annualAmp != null ? annualAmp : null);
                }
            }
            dataList.add(resultMap);
        }
    }

}
