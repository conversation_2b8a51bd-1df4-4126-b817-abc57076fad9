/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.config;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * DimensionParamVOTest Class
 *
 * <AUTHOR>
 * @since 2023/4/23
 */
public class DimensionParamVOTest extends BaseVOCoverUtilsTest<DimensionParamVO> {

    @Override
    protected Class<DimensionParamVO> getTClass() {
        return DimensionParamVO.class;
    }

    @Test
    public void testMethod() {
        DimensionParamVO dimensionParamVO = new DimensionParamVO();
        dimensionParamVO.setPageIndex(1);
        dimensionParamVO.getPageIndex();
        dimensionParamVO.setPageSize(10);
        dimensionParamVO.getPageSize();
        dimensionParamVO.setTotalSize(8);
        dimensionParamVO.getTotalSize();
        dimensionParamVO.setVersionId(100L);
        dimensionParamVO.getVersionId();
        dimensionParamVO.setL3CegCode("2331L");
        dimensionParamVO.getL3CegCode();
        dimensionParamVO.setL3CegCnName("2331L");
        dimensionParamVO.getL3CegCnName();
        dimensionParamVO.setDelFlag("Y");
        dimensionParamVO.getDelFlag();
        dimensionParamVO.setCategoryCode("111");
        dimensionParamVO.getCategoryCode();
        dimensionParamVO.setCategoryName("test");
        dimensionParamVO.getCategoryName();
        dimensionParamVO.setKeyword("yyy");
        dimensionParamVO.getKeyword();
        dimensionParamVO.getCegLevel();
        dimensionParamVO.getL4CegCnName();
        dimensionParamVO.setL4CegCnName("11");
        dimensionParamVO.getL4CegCode();
        dimensionParamVO.setL4CegCode("22");
        dimensionParamVO.getTopManufactureObjectCode();
        dimensionParamVO.setTopManufactureObjectCode("33");
        dimensionParamVO.getTopShippingObjectCode();
        dimensionParamVO.setTopShippingObjectCode("44");

        List<DmFocCatgCegIctDTO> dmFocCatgCegIctDTOList=new ArrayList<>();
        DmFocCatgCegIctDTO ictDTO = new DmFocCatgCegIctDTO();
        ictDTO.setVersionId(10L);
        dimensionParamVO.setDmFocCatgCegIctDTOList(dmFocCatgCegIctDTOList);
        dimensionParamVO.getDmFocCatgCegIctDTOList();

        DimensionParamVO.builder().keyword("元器")
            .categoryCode("11").categoryName("22").cegLevel("33").dmFocCatgCegIctDTOList(dmFocCatgCegIctDTOList)
            .l4CegCnName("44").l4CegCode("55").pageIndex(1).pageSize(10)
            .topManufactureObjectCode("66").topShippingObjectCode("77").totalSize(12)
            .build();
        Assert.assertNotNull(dimensionParamVO);
    }
}