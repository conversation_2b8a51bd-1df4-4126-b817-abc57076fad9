/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.drop;

import com.huawei.it.fcst.industry.pbi.vo.common.CommonBaseVO;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/26
 */
@Setter
@Getter
@EqualsAndHashCode
@NoArgsConstructor
public class InterLockInfoVO extends CommonBaseVO {

    /**
     * 阈值
     */
    private Double ratioPspStd;

    /**
     * 成本类型
     */
    @Size(max=100,message = "安全考虑，限制数量不能超过100")
    private List<String> costTypeList;

    /**
     * 重量级团队
     */
    private String prodRndTeamCode;


    /**
     * 重量级团队集合
     */
    private List<String> prodRndTeamCodeList;

}
