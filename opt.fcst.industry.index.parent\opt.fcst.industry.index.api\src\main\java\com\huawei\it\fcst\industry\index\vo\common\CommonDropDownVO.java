/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.common;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * CommonViewVO Class
 *
 * <AUTHOR>
 * @since 2023/3/15
 */
// 不能修改成getter和setter
@Data
@NoArgsConstructor
public class CommonDropDownVO implements Serializable {
    private static final long serialVersionUID = -3781852458067854849L;

    private String groupLevel;

    private String viewFlag;

    /**
     * 层级code集合
     */
    private List<String> groupCodeList;

    private String keyWord;

    private Long versionId;

    private String nextGroupLevel;

    /**
     * 业务口径（R:收入时点/C：发货成本）
     */
    private String caliberFlag;

    private String  periodYear;

    /**
     * 国内/海外
     */
    private String overseaFlag;

    /**
     * BG编码
     */
    private String lv0ProdListCode;

    /**
     * L0多选
     */
    private List<String> lv0ProdRndTeamCodeList;

    /**
     * L1多选
     */
    private List<String> lv1ProdRndTeamCodeList;

    /**
     * L2多选
     */
    private List<String> lv2ProdRndTeamCodeList;

    /**
     * L3多选
     */
    private List<String> lv3ProdRndTeamCodeList;

    /**
     * ICT产业集合
     */
    private Set<String> lv0DimensionSet = new HashSet<>();

    /**
     * 重量级团队LV1集合
     */
    private Set<String> lv1DimensionSet  = new HashSet<>();

    /**
     * 重量级团队LV2集合
     */
    private Set<String> lv2DimensionSet  = new HashSet<>();

    private Boolean isMultipleSelect;

    /**
     * 数据标识（研发替代：REPLACE、标准成本：STANDARD）
     */
    private String dataFlag;

}
