/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.provider.config;

import com.huawei.it.fcst.enums.CommonConstEnum;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstTopSpartInfoDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstVersionInfoDao;
import com.huawei.it.fcst.industry.pbi.enums.IndustryConstEnum;
import com.huawei.it.fcst.export.IExcelExportDataProvider;
import com.huawei.it.fcst.industry.pbi.utils.FcstIndustryUtil;
import com.huawei.it.fcst.industry.pbi.vo.common.CommonBaseVO;
import com.huawei.it.fcst.industry.pbi.vo.config.DmFcstTopSpartInfoDTO;
import com.huawei.it.fcst.industry.pbi.vo.config.HistoryTopSpartInfoVO;
import com.huawei.it.fcst.export.vo.ExcelExportContext;
import com.huawei.it.fcst.export.vo.ExportList;
import com.huawei.it.fcst.industry.pbi.vo.version.DmFcstVersionInfoDTO;
import com.huawei.it.fcst.util.ObjectCopyUtil;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.inject.Inject;
import javax.inject.Named;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 导出 历史spart清单
 *
 * <AUTHOR>
 * @since 2024/7/11
 */
@Named("IExcelExport.TopSpartExportDataProvider")
public class TopSpartExportDataProvider implements IExcelExportDataProvider {

    @Inject
    private IDmFcstVersionInfoDao dmFcstVersionInfoDao;

    @Inject
    private IDmFcstTopSpartInfoDao dmFcstTopSpartInfoDao;

    @Override
    public List<?> getData(Serializable conditionObject, PageVO pageVO) {
        HistoryTopSpartInfoVO historyTopSpartInfoVO = (HistoryTopSpartInfoVO) conditionObject;
        FcstIndustryUtil.setRegionCode(historyTopSpartInfoVO);
        // 通过id获取版本信息
        DmFcstVersionInfoDTO versionDTO = dmFcstVersionInfoDao.findDmFocVersionDTOById(historyTopSpartInfoVO.getVersionId());
        // 获取需要导出的数据和动态表头
        List<String> yearPeriodList = FcstIndustryUtil.getPeriod(versionDTO.getVersion());
        // 设置品类查询对象
        DmFcstTopSpartInfoDTO build = setTopSpartVO(historyTopSpartInfoVO, yearPeriodList);

        PagedResult<DmFcstTopSpartInfoDTO> pagedResult = dmFcstTopSpartInfoDao.findTopSpartByPage(build, pageVO);
        List<DmFcstTopSpartInfoDTO> result = pagedResult.getResult();
        for (DmFcstTopSpartInfoDTO dmFcstTopSpartInfoDTO : result) {
            if (StringUtils.isNotBlank(dmFcstTopSpartInfoDTO.getWeight0())) {
                BigDecimal weight0 = new BigDecimal(dmFcstTopSpartInfoDTO.getWeight0());
                weight0 = weight0.multiply(BigDecimal.valueOf(100));
                String weight0Str = weight0.setScale(2, RoundingMode.HALF_UP).toString() + "%";
                dmFcstTopSpartInfoDTO.setWeight0(weight0Str);
            }
            if (StringUtils.isNotBlank(dmFcstTopSpartInfoDTO.getWeight1())) {
                BigDecimal weight1 = new BigDecimal(dmFcstTopSpartInfoDTO.getWeight1());
                weight1 = weight1.multiply(BigDecimal.valueOf(100));
                String weight1Str = weight1.setScale(2, RoundingMode.HALF_UP).toString() + "%";
                dmFcstTopSpartInfoDTO.setWeight1(weight1Str);
            }
            if (StringUtils.isNotBlank(dmFcstTopSpartInfoDTO.getWeight2())) {
                BigDecimal weight2 = new BigDecimal(dmFcstTopSpartInfoDTO.getWeight2());
                weight2 = weight2.multiply(BigDecimal.valueOf(100));
                String weight2Str = weight2.setScale(2, RoundingMode.HALF_UP).toString() + "%";
                dmFcstTopSpartInfoDTO.setWeight2(weight2Str);
            }
            if (StringUtils.isNotBlank(dmFcstTopSpartInfoDTO.getWeight3())) {
                BigDecimal weight3 = new BigDecimal(dmFcstTopSpartInfoDTO.getWeight3());
                weight3 = weight3.multiply(BigDecimal.valueOf(100));
                String weight3Str = weight3.setScale(2, RoundingMode.HALF_UP).toString() + "%";
                dmFcstTopSpartInfoDTO.setWeight3(weight3Str);
            }
            if (StringUtils.isNotBlank(dmFcstTopSpartInfoDTO.getWeight4())) {
                BigDecimal weight4 = new BigDecimal(dmFcstTopSpartInfoDTO.getWeight4());
                weight4 = weight4.multiply(BigDecimal.valueOf(100));
                String weight4Str = weight4.setScale(2, RoundingMode.HALF_UP).toString() + "%";
                dmFcstTopSpartInfoDTO.setWeight4(weight4Str);
            }
            // topSpart标签
            if (StringUtils.isBlank(dmFcstTopSpartInfoDTO.getTopFlag())) {
                dmFcstTopSpartInfoDTO.setTopFlag("N");
            }
            dmFcstTopSpartInfoDTO.setTopFlag(IndustryConstEnum.getMainFlag(dmFcstTopSpartInfoDTO.getTopFlag()).getDesc());
            // 设置国内海外
            dmFcstTopSpartInfoDTO.setOverseaFlag(CommonConstEnum.getOverSeaFlag(dmFcstTopSpartInfoDTO.getOverseaFlag()).getDesc());
            // 软硬件标识
            if (IndustryConstEnum.COST_TYPE.PSP.getValue().equals(dmFcstTopSpartInfoDTO.getCostType())) {
                dmFcstTopSpartInfoDTO.setSoftwareMark(IndustryConstEnum.getSoftwareMark(dmFcstTopSpartInfoDTO.getSoftwareMark()).getDesc());
            }
            // 主力编码
            dmFcstTopSpartInfoDTO.setMainFlag(IndustryConstEnum.getMainFlag(dmFcstTopSpartInfoDTO.getMainFlag()).getDesc());
        }
        ExportList  list = new ExportList();
        list.addAll(result);
        list.setTotalRows(pagedResult.getPageVO().getTotalRows());
        return list;
    }


    private DmFcstTopSpartInfoDTO setTopSpartVO(CommonBaseVO commonBaseVO, List<String> yearPeriodList) {
        DmFcstTopSpartInfoDTO topSpartInfoDTO = ObjectCopyUtil.copy(commonBaseVO, DmFcstTopSpartInfoDTO.class);
        topSpartInfoDTO.setYearPeriodList(yearPeriodList);
        if (CollectionUtils.isNotEmpty(yearPeriodList)) {
            topSpartInfoDTO.setPeriodYear0(yearPeriodList.get(0));
            topSpartInfoDTO.setPeriodYear1(yearPeriodList.get(1));
            topSpartInfoDTO.setPeriodYear2(yearPeriodList.get(2));
            topSpartInfoDTO.setPeriodYear3(yearPeriodList.get(3));
            topSpartInfoDTO.setPeriodYear4(yearPeriodList.get(4));
        }
        return topSpartInfoDTO;
    }

    @Override
    public Map<String, Object> getHeader(Serializable conditionObject, Map<String, Object> parameters) throws ApplicationException {
        ExcelExportContext context = (ExcelExportContext) conditionObject;
        HistoryTopSpartInfoVO historyTopSpartInfoVO = (HistoryTopSpartInfoVO) context.getConditionObject();
        // 自定义表头
        Map<String, Object> headMap = new HashMap<>();
        // 通过id获取版本信息
        DmFcstVersionInfoDTO versionDTO = dmFcstVersionInfoDao.findDmFocVersionDTOById(historyTopSpartInfoVO.getVersionId());
        // 获取需要导出的数据和动态表头
        List<String> yearPeriodList = FcstIndustryUtil.getPeriod(versionDTO.getVersion());
        headMap.put("version", versionDTO.getVersion());
        if (CollectionUtils.isNotEmpty(yearPeriodList)) {
            for (int i = 0; i < yearPeriodList.size(); i++) {
                headMap.put("year" + i, yearPeriodList.get(i));
            }
        }
        return headMap;
    }
}
