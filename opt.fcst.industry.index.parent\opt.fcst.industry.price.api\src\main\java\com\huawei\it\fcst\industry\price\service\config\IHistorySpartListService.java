/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.service.config;

import com.huawei.it.fcst.industry.price.vo.config.PriceHistorySpartSearchVO;
import com.huawei.it.fcst.industry.price.vo.version.DmFcstVersionInfoVO;
import com.huawei.it.fcst.vo.DmFcstDataRefreshStatus;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;

/**
 * <AUTHOR>
 * @since 2024/6/26
 */
@Api(tags = "历史SPART清单API服务")
@Path("/history")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface IHistorySpartListService {

    /**
     * [查询版本列表信息]
     *
     * @return ResultDataVO
     * @throws ApplicationException
     */
    @POST
    @Path("/version/list")
    ResultDataVO getVersionList(DmFcstVersionInfoVO versionInfoVO);

    /**
     * [查询历史SPART清单各层级下拉框列表]
     *
     * @return ResultDataVO
     * @throws ApplicationException
     */
    @POST
    @Path("/dropdown/list")
    ResultDataVO getDropdownList(PriceHistorySpartSearchVO historySpartSearchVO) throws CommonApplicationException;

    /**
     * 历史SPART清单分页查询
     *
     * @param historySpartSearchVO 参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @ApiOperation("分页查询历史SPART清单")
    @Path("/topSpart/pageList")
    ResultDataVO getTopSpartPageList(PriceHistorySpartSearchVO historySpartSearchVO) throws ApplicationException;

    /**
     * 历史SPART清单导出
     *
     * @param historySpartSearchVO 参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/topSpart/export")
    ResultDataVO historySpartListExport(@Context HttpServletResponse response, PriceHistorySpartSearchVO historySpartSearchVO) throws ApplicationException;

    /**
     * 异步导出获取任务状态查询
     *
     * @param dataRefreshStatus 参数
     * @return 结果
     * @throws ApplicationException 异常
     */
    @POST
    @Path("/query/status")
    ResultDataVO queryDataRefreshStatus(DmFcstDataRefreshStatus dataRefreshStatus) throws ApplicationException;

}
