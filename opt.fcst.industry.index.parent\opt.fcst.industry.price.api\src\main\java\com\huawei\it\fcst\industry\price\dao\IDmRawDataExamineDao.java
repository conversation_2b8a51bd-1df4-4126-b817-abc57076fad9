/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.dao;

import com.huawei.it.fcst.industry.price.vo.config.BottomDataReviewVO;
import com.huawei.it.fcst.industry.price.vo.config.DataReviewVO;
import com.huawei.it.fcst.industry.price.vo.config.DmRawDataExamineDTO;
import com.huawei.it.fcst.industry.price.vo.config.ExamineVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * IDmFcstIctRawDataExamineDao Class
 *
 * <AUTHOR>
 * @since 2024/9/6
 */
public interface IDmRawDataExamineDao {

    List<DmRawDataExamineDTO> findRawDataExamineList(BottomDataReviewVO bottomDataReviewVO);

    PagedResult<DmRawDataExamineDTO> findSpartCodeByPageList(@Param("searchVO") BottomDataReviewVO bottomDataReviewVO, @Param("pageVO") PageVO pageVO);

    List<DmRawDataExamineDTO> findExamineDropDownList(BottomDataReviewVO bottomDataReviewVO);

    PagedResult<DmRawDataExamineDTO> findDataReviewListByPage(@Param("reviewVO") BottomDataReviewVO bottomDataReviewVO, @Param("pageVO") PageVO pageVO);

    List<DmRawDataExamineDTO> findAllDataReviewList(@Param("reviewVO") DataReviewVO dataReviewVO);

    void createPriceDataReviewList(@Param("list") List<DmRawDataExamineDTO> allResultList);

    List<DmRawDataExamineDTO> findProdCodeRawDataExamineList(BottomDataReviewVO bottomDataReviewVO);

    List<DmRawDataExamineDTO> findOtherRawDataExamineList(BottomDataReviewVO bottomDataReviewVO);

    Long findSpartDataExamineCount(ExamineVO examineVO);

    Long findContractExamineCount(ExamineVO examineVO);

    DmRawDataExamineDTO findPriceBeginEndDate();
}
