/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.service.config;

import com.huawei.it.fcst.industry.pbi.vo.config.BottomLevelDataReviewVO;
import com.huawei.it.fcst.industry.pbi.vo.config.DataReviewVO;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.cxf.jaxrs.ext.multipart.Multipart;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.Consumes;
import javax.ws.rs.FormParam;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.io.IOException;

/**
 * 产业成本指数（ICT）配置管理页面底层数据审视API接口类
 * <AUTHOR>
 * @since 2024/9/6
 */
@Path("/dataReview")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface IBottomLevelDataReviewService {

    /**
     * 获取版本下拉框
     *
     * @return 结果
     */
    @Path("/version/list")
    @POST
    ResultDataVO findVersionList() throws CommonApplicationException;

    /**
     * 新增数据时各层级下拉框查询
     *
     * @return ResultDataVO
     */
    @POST
    @Path("/dropDownBox/list")
    ResultDataVO getDataReviewDropboxList(BottomLevelDataReviewVO bottomLevelDataReviewVO)throws CommonApplicationException;

    /**
     * 新增数据时各层级下拉框查询
     *
     * @return ResultDataVO
     */
    @POST
    @Path("/edit/dropbox/list")
    ResultDataVO getDataReviewEditDropboxList(BottomLevelDataReviewVO bottomLevelDataReviewVO)throws CommonApplicationException;

    /**
     * 异常数据录入查询接口&&操作记录查询
     *
     * @return ResultDataVO
     */
    @POST
    @Path("/findByPage")
    ResultDataVO findDataReviewByPage(BottomLevelDataReviewVO bottomLevelDataReviewVO) throws CommonApplicationException;

    /**
     * 底层数据审视保存，新增，修改，撤销都是保存接口
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @Path("/save")
    @POST
    ResultDataVO saveDataReview(DataReviewVO dataReviewVO) throws Exception;

    /**
     * [底层数据审视导出接口]
     *
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @Path("/examineImport")
    @POST
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    ResultDataVO importDataReview(@Multipart("files") Attachment attachment, @FormParam("versionId") Long versionId) throws CommonApplicationException, IOException;

    @Path("/download/template")
    @POST
    ResultDataVO downloadTemplate(DataReviewVO dataReviewVO, @Context HttpServletResponse response) throws Exception;

    /**
     * 底层数据审视清单导出
     *
     * @param bottomLevelDataReviewVO 参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/export")
    ResultDataVO dataReviewExport(@Context HttpServletResponse response, BottomLevelDataReviewVO bottomLevelDataReviewVO) throws ApplicationException;

    /**
     * 底层数据审视清单导出
     *
     * @param bottomLevelDataReviewVO 参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/histroy/export")
    ResultDataVO dataReviewHistroyExport(@Context HttpServletResponse response, BottomLevelDataReviewVO bottomLevelDataReviewVO) throws ApplicationException;

    /**
     * 系统刷新
     *
     * @return 结果
     */
    @Path("/refreshSystem")
    @GET
    ResultDataVO refreshSystem(@QueryParam("versionId")Long versionId) throws ApplicationException;

    /**
     * 系统刷新时间
     *
     * @return 结果
     */
    @Path("/findRefreshTime")
    @POST
    ResultDataVO findRefreshTime();


}
