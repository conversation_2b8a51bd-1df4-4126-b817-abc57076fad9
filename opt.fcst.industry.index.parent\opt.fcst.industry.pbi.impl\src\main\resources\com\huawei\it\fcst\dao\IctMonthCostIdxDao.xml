<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.pbi.dao.IctMonthCostIdxDao">
    <resultMap type="com.huawei.it.fcst.industry.pbi.vo.month.IctMonthAnalysisVO" id="resultMap">
        <result property="basePeriodId" column="base_period_id"/>
        <result property="versionId" column="version_id"/>
        <result property="customId" column="custom_id"/>
        <result property="combId" column="comb_id"/>
        <result property="customCnName" column="custom_cn_name"/>
        <result property="granularityType" column="granularity_type"/>
        <result property="periodYear" column="period_year"/>
        <result property="periodId" column="period_id"/>
        <result property="lv0ProdRdTeamCnName" column="lv0_prod_rd_team_cn_name"/>
        <result property="lv0ProdRndTeamCode" column="lv0_prod_rnd_team_code"/>
        <result property="lv1ProdRdTeamCnName" column="lv1_prod_rd_team_cn_name"/>
        <result property="lv1ProdRndTeamCode" column="lv1_prod_rnd_team_code"/>
        <result property="lv2ProdRdTeamCnName" column="lv2_prod_rd_team_cn_name"/>
        <result property="lv2ProdRndTeamCode" column="lv2_prod_rnd_team_code"/>
        <result property="lv3ProdRdTeamCnName" column="lv3_prod_rd_team_cn_name"/>
        <result property="lv3ProdRndTeamCode" column="lv3_prod_rnd_team_code"/>
        <result property="lv4ProdRdTeamCnName" column="lv4_prod_rd_team_cn_name"/>
        <result property="lv4ProdRndTeamCode" column="lv4_prod_rnd_team_code"/>
        <result property="prodRndTeamCode" column="prod_rnd_team_code"/>
        <result property="prodRndTeamCnName" column="prod_rd_team_cn_name"/>
        <result property="prodListCode" column="prod_list_code"/>
        <result property="prodListCnName" column="prod_list_cn_name"/>
        <result property="industryCatgCode" column="industry_catg_code"/>
        <result property="industryCatgCnName" column="industry_catg_cn_name"/>
        <result property="dimensionCode" column="dimension_code"/>
        <result property="dimensionCnName" column="dimension_cn_name"/>
        <result property="dimensionSubCategoryCode" column="dimension_subcategory_code"/>
        <result property="dimensionSubCategoryCnName" column="dimension_subcategory_cn_name"/>
        <result property="dimensionSubDetailCode" column="dimension_sub_detail_code"/>
        <result property="dimensionSubDetailCnName" column="dimension_sub_detail_cn_name"/>
        <result property="spartCode" column="spart_code"/>
        <result property="spartCnName" column="spart_cn_name"/>
        <result property="groupCode" column="group_code"/>
        <result property="groupCnName" column="group_cn_name"/>
        <result property="groupLevel" column="group_level"/>
        <result property="costIndex" column="cost_index"/>
        <result property="ytdCostIndex" column="ytd_cost_index"/>
        <result property="costReductionRate" column="cost_reduction_rate"/>
        <result property="costReductionCnName" column="cost_reduction_cn_name"/>
        <result property="costReductionLevel" column="cost_reduction_level"/>
        <result property="weightRate" column="weight_rate"/>
        <result property="weightPercent" column="weight_percent"/>
        <result property="parentLevel" column="parent_level"/>
        <result property="parentCode" column="parent_code"/>
        <result property="parentCnName" column="parent_cn_name"/>
        <result property="mainFlag" column="main_flag"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="appendFlag" column="append_flag"/>
        <result property="regionCode" column="region_code"/>
        <result property="regionCnName" column="region_cn_name"/>
        <result property="repofficeCode" column="repoffice_code"/>
        <result property="repofficeCnName" column="repoffice_cn_name"/>
        <result property="bgCode" column="bg_code"/>
        <result property="bgCnName" column="bg_cn_name"/>
        <result property="overseaFlag" column="oversea_flag"/>
        <result property="codeAttributes" column="code_attributes"/>
    </resultMap>

    <sql id="costIdxChartFields">
        DISTINCT
        custom_id,
        version_id,
        period_id,
        group_code,
        group_cn_name,
        group_level,
        cost_index
    </sql>

    <sql id="searchWhere">
        <if test='monthAnalysisVO.versionId != null'>
            AND t1.version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='!monthAnalysisVO.isNeedBlur and monthAnalysisVO.basePeriodId != null'>
            AND t1.base_period_id = #{monthAnalysisVO.basePeriodId}
        </if>
        <if test='monthAnalysisVO.periodStartTime != null'>
            AND t1.period_id <![CDATA[ >= ]]> #{monthAnalysisVO.periodStartTime}
        </if>
        <if test='monthAnalysisVO.periodEndTime != null'>
            AND t1.period_id <![CDATA[ <= ]]> #{monthAnalysisVO.periodEndTime}
        </if>
        <if test='monthAnalysisVO.softwareMark != null and monthAnalysisVO.softwareMark != ""'>
            AND t1.software_mark = #{monthAnalysisVO.softwareMark,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.viewFlag != null and monthAnalysisVO.viewFlag != ""'>
            AND t1.view_flag = #{monthAnalysisVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            AND t1.oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
            AND t1.bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
            AND t1.region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
            AND t1.repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.mainFlag != null and monthAnalysisVO.mainFlag != ""'>
            AND t1.main_flag = #{monthAnalysisVO.mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.codeAttributes != null and monthAnalysisVO.codeAttributes != ""'>
            AND t1.code_attributes = #{monthAnalysisVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupLevel != null and monthAnalysisVO.groupLevel != ""'>
            and t1.group_level = #{monthAnalysisVO.groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupCodeList != null and monthAnalysisVO.groupCodeList != ""'>
            <foreach collection='monthAnalysisVO.groupCodeList' item="code" open="AND t1.group_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.granularityType=="IRB" and monthAnalysisVO.prodRndTeamCodeList != null and monthAnalysisVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND t1.prod_rnd_team_code IN ("
                     close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.granularityType=="PROD" and monthAnalysisVO.prodRndTeamCodeList != null and monthAnalysisVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND t1.prod_list_code IN ("
                     close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.granularityType=="INDUS" and monthAnalysisVO.prodRndTeamCodeList != null and monthAnalysisVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND t1.industry_catg_code IN ("
                     close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.dimensionCode != null and monthAnalysisVO.dimensionCode != ""'>
            AND t1.dimension_code = #{monthAnalysisVO.dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.dimensionSubCategoryCode != null and monthAnalysisVO.dimensionSubCategoryCode != ""'>
            AND t1.dimension_subcategory_code = #{monthAnalysisVO.dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.dimensionSubDetailCode != null and monthAnalysisVO.dimensionSubDetailCode != ""'>
            AND t1.dimension_sub_detail_code = #{monthAnalysisVO.dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
    </sql>

    <sql id="blurSearchWhere">
        <if test='monthAnalysisVO.periodStartTime != null'>
            AND period_id <![CDATA[ >= ]]> #{monthAnalysisVO.periodStartTime}
        </if>
        <if test='monthAnalysisVO.periodEndTime != null'>
            AND period_id <![CDATA[ <= ]]> #{monthAnalysisVO.periodEndTime}
        </if>
        <if test='monthAnalysisVO.versionId != null'>
            AND version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='monthAnalysisVO.customIdList != null and monthAnalysisVO.customIdList != ""'>
            <foreach collection='monthAnalysisVO.customIdList' item="code" open="AND custom_id IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.softwareMark != null and monthAnalysisVO.softwareMark != ""'>
            AND software_mark = #{monthAnalysisVO.softwareMark,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.granularityType != null and monthAnalysisVO.granularityType != ""'>
            AND granularity_type = #{monthAnalysisVO.granularityType,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.viewFlag != null and monthAnalysisVO.viewFlag != ""'>
            AND view_flag = #{monthAnalysisVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            AND oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
            AND bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
            AND region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
            AND repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.mainFlag != null and monthAnalysisVO.mainFlag != ""'>
            AND main_flag = #{monthAnalysisVO.mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.codeAttributes != null and monthAnalysisVO.codeAttributes != ""'>
            AND code_attributes = #{monthAnalysisVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupLevel != null and monthAnalysisVO.groupLevel != ""'>
            and group_level = #{monthAnalysisVO.groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.customGroupCodeList != null and monthAnalysisVO.customGroupCodeList != ""'>
            <foreach collection='monthAnalysisVO.customGroupCodeList' item="code" open="AND group_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
    </sql>

    <select id="findCostIndexVOList" resultMap="resultMap">
        SELECT
        DISTINCT
        <if test='monthAnalysisVO.granularityType == "IRB"'>
            info.lv0_prod_rd_team_cn_name,info.lv0_prod_rnd_team_code,
            <choose>
                <when test='monthAnalysisVO.teamLevel == "LV1"'>
                    info.lv1_prod_rd_team_cn_name,info.lv1_prod_rnd_team_code,
                </when>
                <when test='monthAnalysisVO.teamLevel == "LV2"'>
                    info.lv1_prod_rd_team_cn_name,info.lv1_prod_rnd_team_code,info.lv2_prod_rd_team_cn_name,info.lv2_prod_rnd_team_code,
                </when>
                <when test='monthAnalysisVO.teamLevel == "LV3"'>
                    info.lv1_prod_rd_team_cn_name,info.lv1_prod_rnd_team_code,info.lv2_prod_rd_team_cn_name,info.lv2_prod_rnd_team_code,info.lv3_prod_rd_team_cn_name,info.lv3_prod_rnd_team_code,
                </when>
                <when test='monthAnalysisVO.teamLevel == "LV4"'>
                    info.lv1_prod_rd_team_cn_name,info.lv1_prod_rnd_team_code,info.lv2_prod_rd_team_cn_name,info.lv2_prod_rnd_team_code,info.lv3_prod_rd_team_cn_name,info.lv3_prod_rnd_team_code,
                    info.lv4_prod_rd_team_cn_name,info.lv4_prod_rnd_team_code,
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='monthAnalysisVO.granularityType == "PROD"'>
            info.lv0_prod_list_cn_name as lv0_prod_rd_team_cn_name,info.lv0_prod_list_code as lv0_prod_rnd_team_code,
            <choose>
                <when test='monthAnalysisVO.teamLevel == "LV1"'>
                    info.lv1_prod_list_cn_name as lv1_prod_rd_team_cn_name,info.lv1_prod_list_code as
                    lv1_prod_rnd_team_code,
                </when>
                <when test='monthAnalysisVO.teamLevel == "LV2"'>
                    info.lv1_prod_list_cn_name as lv1_prod_rd_team_cn_name,info.lv1_prod_list_code as
                    lv1_prod_rnd_team_code,
                    info.lv2_prod_list_cn_name as lv2_prod_rd_team_cn_name,info.lv2_prod_list_code as
                    lv2_prod_rnd_team_code,
                </when>
                <when test='monthAnalysisVO.teamLevel == "LV3"'>
                    info.lv1_prod_list_cn_name as lv1_prod_rd_team_cn_name,info.lv1_prod_list_code as
                    lv1_prod_rnd_team_code,
                    info.lv2_prod_list_cn_name as lv2_prod_rd_team_cn_name,info.lv2_prod_list_code as
                    lv2_prod_rnd_team_code,
                    info.lv3_prod_list_cn_name as lv3_prod_rd_team_cn_name,info.lv3_prod_list_code as
                    lv3_prod_rnd_team_code,
                </when>
                <when test='monthAnalysisVO.teamLevel == "LV4"'>
                    info.lv1_prod_list_cn_name as lv1_prod_rd_team_cn_name,info.lv1_prod_list_code as
                    lv1_prod_rnd_team_code,
                    info.lv2_prod_list_cn_name as lv2_prod_rd_team_cn_name,info.lv2_prod_list_code as
                    lv2_prod_rnd_team_code,
                    info.lv3_prod_list_cn_name as lv3_prod_rd_team_cn_name,info.lv3_prod_list_code as
                    lv3_prod_rnd_team_code,
                    info.lv4_prod_list_cn_name as lv4_prod_rd_team_cn_name,info.lv4_prod_list_code as
                    lv4_prod_rnd_team_code,
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='monthAnalysisVO.granularityType == "INDUS"'>
            info.lv0_industry_catg_cn_name as lv0_prod_rd_team_cn_name,info.lv0_industry_catg_code as
            lv0_prod_rnd_team_code,
            <choose>
                <when test='monthAnalysisVO.teamLevel == "LV1"'>
                    info.lv1_industry_catg_cn_name as lv1_prod_rd_team_cn_name,info.lv1_industry_catg_code as
                    lv1_prod_rnd_team_code,
                </when>
                <when test='monthAnalysisVO.teamLevel == "LV2"'>
                    info.lv1_industry_catg_cn_name as lv1_prod_rd_team_cn_name,info.lv1_industry_catg_code as
                    lv1_prod_rnd_team_code,
                    info.lv2_industry_catg_cn_name as lv2_prod_rd_team_cn_name,info.lv2_industry_catg_code as
                    lv2_prod_rnd_team_code,
                </when>
                <when test='monthAnalysisVO.teamLevel == "LV3"'>
                    info.lv1_industry_catg_cn_name as lv1_prod_rd_team_cn_name,info.lv1_industry_catg_code as
                    lv1_prod_rnd_team_code,
                    info.lv2_industry_catg_cn_name as lv2_prod_rd_team_cn_name,info.lv2_industry_catg_code as
                    lv2_prod_rnd_team_code,
                    info.lv3_industry_catg_cn_name as lv3_prod_rd_team_cn_name,info.lv3_industry_catg_code as
                    lv3_prod_rnd_team_code,
                </when>
                <when test='monthAnalysisVO.teamLevel == "LV4"'>
                    info.lv1_industry_catg_cn_name as lv1_prod_rd_team_cn_name,info.lv1_industry_catg_code as
                    lv1_prod_rnd_team_code,
                    info.lv2_industry_catg_cn_name as lv2_prod_rd_team_cn_name,info.lv2_industry_catg_code as
                    lv2_prod_rnd_team_code,
                    info.lv3_industry_catg_cn_name as lv3_prod_rd_team_cn_name,info.lv3_industry_catg_code as
                    lv3_prod_rnd_team_code,
                    info.lv4_industry_catg_cn_name as lv4_prod_rd_team_cn_name,info.lv4_industry_catg_code as
                    lv4_prod_rnd_team_code,
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        t1.dimension_code,t1.dimension_cn_name,
        t1.dimension_subcategory_code,t1.dimension_subcategory_cn_name,
        t1.dimension_sub_detail_code,t1.dimension_sub_detail_cn_name,
        <if test='monthAnalysisVO.groupLevel == "SPART"'>
            t1.group_code as spart_code,t1.group_cn_name as spart_cn_name,
        </if>
        t1.version_id,
        t1.period_id,
        t1.group_code,
        t1.group_cn_name,
        t1.group_level,
        t1.cost_index
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.tablePreFix}_mon_cost_idx_t t1
        <if test='monthAnalysisVO.viewFlag == "PROD_SPART"'>
            left join fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.tablePreFix}_top_spart_info_t info
        </if>
        <if test='monthAnalysisVO.viewFlag == "DIMENSION"'>
            left join fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.tablePreFix}_dim_info_t info
        </if>
        on t1.view_flag = info.view_flag
        AND t1.oversea_flag = info.oversea_flag
        AND t1.bg_code = info.bg_code
        AND t1.region_code = info.region_code
        AND t1.repoffice_code = info.repoffice_code
        AND t1.main_flag = info.main_flag
        AND t1.software_mark = info.software_mark
        AND nvl(t1.code_attributes,'CA') = nvl(info.code_attributes,'CA')
        <if test='monthAnalysisVO.viewFlag == "PROD_SPART"'>
            AND t1.version_id = info.version_id
        </if>
        <if test='monthAnalysisVO.isNeedBlur != true'>
            <if test='monthAnalysisVO.granularityType == "IRB"'>
                <choose>
                    <when test='monthAnalysisVO.teamLevel == "LV0"'>
                        AND t1.prod_rnd_team_code = info.lv0_prod_rnd_team_code
                    </when>
                    <when test='monthAnalysisVO.teamLevel == "LV1"'>
                        AND t1.prod_rnd_team_code = info.lv1_prod_rnd_team_code
                    </when>
                    <when test='monthAnalysisVO.teamLevel == "LV2"'>
                        AND t1.prod_rnd_team_code = info.lv2_prod_rnd_team_code
                    </when>
                    <when test='monthAnalysisVO.teamLevel == "LV3"'>
                        AND t1.prod_rnd_team_code = info.lv3_prod_rnd_team_code
                    </when>
                    <when test='monthAnalysisVO.teamLevel == "LV4"'>
                        AND t1.prod_rnd_team_code = info.lv4_prod_rnd_team_code
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
            <if test='monthAnalysisVO.granularityType == "PROD"'>
                <choose>
                    <when test='monthAnalysisVO.teamLevel == "LV0"'>
                        AND t1.prod_list_code = info.lv0_prod_list_code
                    </when>
                    <when test='monthAnalysisVO.teamLevel == "LV1"'>
                        AND t1.prod_list_code = info.lv1_prod_list_code
                    </when>
                    <when test='monthAnalysisVO.teamLevel == "LV2"'>
                        AND t1.prod_list_code = info.lv2_prod_list_code
                    </when>
                    <when test='monthAnalysisVO.teamLevel == "LV3"'>
                        AND t1.prod_list_code = info.lv3_prod_list_code
                    </when>
                    <when test='monthAnalysisVO.teamLevel == "LV4"'>
                        AND t1.prod_list_code = info.lv4_prod_list_code
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
            <if test='monthAnalysisVO.granularityType == "INDUS"'>
                <choose>
                    <when test='monthAnalysisVO.teamLevel == "LV0"'>
                        AND t1.industry_catg_code = info.lv0_industry_catg_code
                    </when>
                    <when test='monthAnalysisVO.teamLevel == "LV1"'>
                        AND t1.industry_catg_code = info.lv1_industry_catg_code
                    </when>
                    <when test='monthAnalysisVO.teamLevel == "LV2"'>
                        AND t1.industry_catg_code = info.lv2_industry_catg_code
                    </when>
                    <when test='monthAnalysisVO.teamLevel == "LV3"'>
                        AND t1.industry_catg_code = info.lv3_industry_catg_code
                    </when>
                    <when test='monthAnalysisVO.teamLevel == "LV4"'>
                        AND t1.industry_catg_code = info.lv4_industry_catg_code
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
        </if>
        <if test='monthAnalysisVO.viewFlag == "DIMENSION" and monthAnalysisVO.groupLevel != "LV0" and monthAnalysisVO.groupLevel != "LV1" and monthAnalysisVO.groupLevel != "LV2" and monthAnalysisVO.groupLevel != "LV3" and monthAnalysisVO.groupLevel != "LV4"'>
            and nvl ( t1.dimension_code, 'snull' ) = nvl ( info.dimension_code, 'snull' )
            and nvl ( t1.dimension_subcategory_code, 'snull' ) = nvl ( info.dimension_subcategory_code, 'snull' )
            and nvl ( t1.dimension_sub_detail_code, 'snull' ) = nvl ( info.dimension_sub_detail_code,'snull' )
        </if>
        <choose>
            <when test='monthAnalysisVO.groupLevel == "DIMENSION"'>
                and t1.group_code = info.dimension_code
            </when>
            <when test='monthAnalysisVO.groupLevel == "SUBCATEGORY"'>
                and t1.group_code = info.dimension_subcategory_code
            </when>
            <when test='monthAnalysisVO.groupLevel == "SUB_DETAIL"'>
                and t1.group_code = info.dimension_sub_detail_code
            </when>
            <when test='monthAnalysisVO.groupLevel == "SPART"'>
                and t1.group_code = info.top_spart_code
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <include refid="searchWhere"/>
            <choose>
                <when test='monthAnalysisVO.groupLevel == "DIMENSION"'>
                    <if test='monthAnalysisVO.groupCodeList != null and monthAnalysisVO.groupCodeList != ""'>
                        <foreach collection='monthAnalysisVO.groupCodeList' item="code" open="AND t1.dimension_code IN ("
                                 close=")" index="index" separator=",">
                            #{code}
                        </foreach>
                    </if>
                </when>
                <when test='monthAnalysisVO.groupLevel == "SUBCATEGORY"'>
                    <if test='monthAnalysisVO.groupCodeList != null and monthAnalysisVO.groupCodeList != ""'>
                        <foreach collection='monthAnalysisVO.groupCodeList' item="code"
                                 open="AND t1.dimension_subcategory_code IN (" close=")" index="index" separator=",">
                            #{code}
                        </foreach>
                    </if>
                </when>
                <when test='monthAnalysisVO.groupLevel == "SUB_DETAIL"'>
                    <if test='monthAnalysisVO.groupCodeList != null and monthAnalysisVO.groupCodeList != ""'>
                        <foreach collection='monthAnalysisVO.groupCodeList' item="code"
                                 open="AND t1.dimension_sub_detail_code IN (" close=")" index="index" separator=",">
                            #{code}
                        </foreach>
                    </if>
                </when>
                <when test='monthAnalysisVO.groupLevel == "SPART"'>
                    <if test='monthAnalysisVO.groupCodeList != null and monthAnalysisVO.groupCodeList != ""'>
                        <foreach collection='monthAnalysisVO.groupCodeList' item="code" open="AND info.top_spart_code IN ("
                                 close=")" index="index" separator=",">
                            #{code}
                        </foreach>
                    </if>
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <if test='monthAnalysisVO.viewFlag == "SPART" and monthAnalysisVO.intervalYear != null and monthAnalysisVO.intervalYear != ""'>
                AND info.period_year = #{monthAnalysisVO.intervalYear}
            </if>
            <if test='monthAnalysisVO.softwareMark != null and monthAnalysisVO.softwareMark != ""'>
                AND info.software_mark = #{monthAnalysisVO.softwareMark,jdbcType=VARCHAR}
            </if>
            <if test='monthAnalysisVO.viewFlag != null and monthAnalysisVO.viewFlag != ""'>
                AND info.view_flag = #{monthAnalysisVO.viewFlag,jdbcType=VARCHAR}
            </if>
            <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
                AND info.oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
                AND info.bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
            </if>
            <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
                AND info.region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
            </if>
            <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
                AND info.repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='monthAnalysisVO.mainFlag != null and monthAnalysisVO.mainFlag != ""'>
                AND info.main_flag = #{monthAnalysisVO.mainFlag,jdbcType=VARCHAR}
            </if>
        </trim>
        ORDER BY t1.period_id
    </select>

    <select id="findMainFlagCostIndexVOList" resultMap="resultMap">
        SELECT
        DISTINCT
        <if test='monthAnalysisVO.granularityType == "IRB"'>
            <choose>
                <when test='monthAnalysisVO.teamLevel == "LV1"'>
                    info.lv1_prod_rd_team_cn_name,info.lv1_prod_rnd_team_code,
                </when>
                <when test='monthAnalysisVO.teamLevel == "LV2"'>
                    info.lv1_prod_rd_team_cn_name,info.lv1_prod_rnd_team_code,info.lv2_prod_rd_team_cn_name,info.lv2_prod_rnd_team_code,
                </when>
                <when test='monthAnalysisVO.teamLevel == "LV3"'>
                    info.lv1_prod_rd_team_cn_name,info.lv1_prod_rnd_team_code,info.lv2_prod_rd_team_cn_name,info.lv2_prod_rnd_team_code,info.lv3_prod_rd_team_cn_name,info.lv3_prod_rnd_team_code,
                </when>
                <when test='monthAnalysisVO.teamLevel == "LV4"'>
                    info.lv1_prod_rd_team_cn_name,info.lv1_prod_rnd_team_code,info.lv2_prod_rd_team_cn_name,info.lv2_prod_rnd_team_code,info.lv3_prod_rd_team_cn_name,info.lv3_prod_rnd_team_code,
                    info.lv4_prod_rd_team_cn_name,info.lv4_prod_rnd_team_code,
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='monthAnalysisVO.granularityType == "PROD"'>
            <choose>
                <when test='monthAnalysisVO.teamLevel == "LV1"'>
                    info.lv1_prod_list_cn_name as lv1_prod_rd_team_cn_name,info.lv1_prod_list_code as
                    lv1_prod_rnd_team_code,
                </when>
                <when test='monthAnalysisVO.teamLevel == "LV2"'>
                    info.lv1_prod_list_cn_name as lv1_prod_rd_team_cn_name,info.lv1_prod_list_code as
                    lv1_prod_rnd_team_code,
                    info.lv2_prod_list_cn_name as lv2_prod_rd_team_cn_name,info.lv2_prod_list_code as
                    lv2_prod_rnd_team_code,
                </when>
                <when test='monthAnalysisVO.teamLevel == "LV3"'>
                    info.lv1_prod_list_cn_name as lv1_prod_rd_team_cn_name,info.lv1_prod_list_code as
                    lv1_prod_rnd_team_code,
                    info.lv2_prod_list_cn_name as lv2_prod_rd_team_cn_name,info.lv2_prod_list_code as
                    lv2_prod_rnd_team_code,
                    info.lv3_prod_list_cn_name as lv3_prod_rd_team_cn_name,info.lv3_prod_list_code as
                    lv3_prod_rnd_team_code,
                </when>
                <when test='monthAnalysisVO.teamLevel == "LV4"'>
                    info.lv1_prod_list_cn_name as lv1_prod_rd_team_cn_name,info.lv1_prod_list_code as
                    lv1_prod_rnd_team_code,
                    info.lv2_prod_list_cn_name as lv2_prod_rd_team_cn_name,info.lv2_prod_list_code as
                    lv2_prod_rnd_team_code,
                    info.lv3_prod_list_cn_name as lv3_prod_rd_team_cn_name,info.lv3_prod_list_code as
                    lv3_prod_rnd_team_code,
                    info.lv4_prod_list_cn_name as lv4_prod_rd_team_cn_name,info.lv4_prod_list_code as
                    lv4_prod_rnd_team_code,
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='monthAnalysisVO.granularityType == "INDUS"'>
            <choose>
                <when test='monthAnalysisVO.teamLevel == "LV1"'>
                    info.lv1_industry_catg_cn_name as lv1_prod_rd_team_cn_name,info.lv1_industry_catg_code as
                    lv1_prod_rnd_team_code,
                </when>
                <when test='monthAnalysisVO.teamLevel == "LV2"'>
                    info.lv1_industry_catg_cn_name as lv1_prod_rd_team_cn_name,info.lv1_industry_catg_code as
                    lv1_prod_rnd_team_code,
                    info.lv2_industry_catg_cn_name as lv2_prod_rd_team_cn_name,info.lv2_industry_catg_code as
                    lv2_prod_rnd_team_code,
                </when>
                <when test='monthAnalysisVO.teamLevel == "LV3"'>
                    info.lv1_industry_catg_cn_name as lv1_prod_rd_team_cn_name,info.lv1_industry_catg_code as
                    lv1_prod_rnd_team_code,
                    info.lv2_industry_catg_cn_name as lv2_prod_rd_team_cn_name,info.lv2_industry_catg_code as
                    lv2_prod_rnd_team_code,
                    info.lv3_industry_catg_cn_name as lv3_prod_rd_team_cn_name,info.lv3_industry_catg_code as
                    lv3_prod_rnd_team_code,
                </when>
                <when test='monthAnalysisVO.teamLevel == "LV4"'>
                    info.lv1_industry_catg_cn_name as lv1_prod_rd_team_cn_name,info.lv1_industry_catg_code as
                    lv1_prod_rnd_team_code,
                    info.lv2_industry_catg_cn_name as lv2_prod_rd_team_cn_name,info.lv2_industry_catg_code as
                    lv2_prod_rnd_team_code,
                    info.lv3_industry_catg_cn_name as lv3_prod_rd_team_cn_name,info.lv3_industry_catg_code as
                    lv3_prod_rnd_team_code,
                    info.lv4_industry_catg_cn_name as lv4_prod_rd_team_cn_name,info.lv4_industry_catg_code as
                    lv4_prod_rnd_team_code,
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='monthAnalysisVO.groupLevel == "SPART"'>
            t1.group_code as spart_code,t1.group_cn_name as spart_cn_name,
        </if>
        t1.version_id,
        t1.period_id,
        t1.group_code,
        t1.group_cn_name,
        t1.group_level,
        t1.cost_index
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.tablePreFix}_mon_cost_idx_t t1
        <if test='monthAnalysisVO.teamLevel != "LV0"'>
            <if test='monthAnalysisVO.viewFlag == "PROD_SPART"'>
                inner join fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.blurTablePreFix}_pbi_main_code_dim_t info
            </if>
            on t1.bg_code = info.bg_code
            <if test='monthAnalysisVO.isNeedBlur != true'>
                <if test='monthAnalysisVO.granularityType == "IRB"'>
                    <choose>
                        <when test='monthAnalysisVO.teamLevel == "LV1"'>
                            AND t1.prod_rnd_team_code = info.lv1_prod_rnd_team_code
                        </when>
                        <when test='monthAnalysisVO.teamLevel == "LV2"'>
                            AND t1.prod_rnd_team_code = info.lv2_prod_rnd_team_code
                        </when>
                        <when test='monthAnalysisVO.teamLevel == "LV3"'>
                            AND t1.prod_rnd_team_code = info.lv3_prod_rnd_team_code
                        </when>
                        <when test='monthAnalysisVO.teamLevel == "LV4"'>
                            AND t1.prod_rnd_team_code = info.lv4_prod_rnd_team_code
                        </when>
                        <otherwise>
                        </otherwise>
                    </choose>
                </if>
                <if test='monthAnalysisVO.granularityType == "PROD"'>
                    <choose>
                        <when test='monthAnalysisVO.teamLevel == "LV1"'>
                            AND t1.prod_list_code = info.lv1_prod_list_code
                        </when>
                        <when test='monthAnalysisVO.teamLevel == "LV2"'>
                            AND t1.prod_list_code = info.lv2_prod_list_code
                        </when>
                        <when test='monthAnalysisVO.teamLevel == "LV3"'>
                            AND t1.prod_list_code = info.lv3_prod_list_code
                        </when>
                        <when test='monthAnalysisVO.teamLevel == "LV4"'>
                            AND t1.prod_list_code = info.lv4_prod_list_code
                        </when>
                        <otherwise>
                        </otherwise>
                    </choose>
                </if>
                <if test='monthAnalysisVO.granularityType == "INDUS"'>
                    <choose>
                        <when test='monthAnalysisVO.teamLevel == "LV1"'>
                            AND t1.industry_catg_code = info.lv1_industry_catg_code
                        </when>
                        <when test='monthAnalysisVO.teamLevel == "LV2"'>
                            AND t1.industry_catg_code = info.lv2_industry_catg_code
                        </when>
                        <when test='monthAnalysisVO.teamLevel == "LV3"'>
                            AND t1.industry_catg_code = info.lv3_industry_catg_code
                        </when>
                        <when test='monthAnalysisVO.teamLevel == "LV4"'>
                            AND t1.industry_catg_code = info.lv4_industry_catg_code
                        </when>
                        <otherwise>
                        </otherwise>
                    </choose>
                </if>
            </if>
            <if test='monthAnalysisVO.groupLevel == "SPART"'>
                and t1.group_code = info.spart_code
            </if>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <include refid="searchWhere"/>
        <if test='monthAnalysisVO.teamLevel != "LV0"'>
            <if test='monthAnalysisVO.annualVersionId != null'>
                AND info.version_id = #{monthAnalysisVO.annualVersionId}
            </if>
            <if test='monthAnalysisVO.codeAttributes !="全选" and monthAnalysisVO.codeAttributes != null and monthAnalysisVO.codeAttributes != ""'>
                AND info.code_attributes = #{monthAnalysisVO.codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='monthAnalysisVO.groupLevel == "SPART" and monthAnalysisVO.groupCodeList != null and monthAnalysisVO.groupCodeList != ""'>
                <foreach collection='monthAnalysisVO.groupCodeList' item="code" open="AND info.spart_code IN ("
                         close=")" index="index" separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
                AND info.bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
            </if>
            <if test='monthAnalysisVO.granularityType == "IRB"'>
                <choose>
                    <when test='monthAnalysisVO.teamLevel == "LV1"'>
                        <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND info.lv1_prod_rnd_team_code IN ("
                                 close=")" index="index" separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='monthAnalysisVO.teamLevel == "LV2"'>
                        <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND info.lv2_prod_rnd_team_code IN ("
                                 close=")" index="index" separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='monthAnalysisVO.teamLevel == "LV3"'>
                        <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND info.lv3_prod_rnd_team_code IN ("
                                 close=")" index="index" separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='monthAnalysisVO.teamLevel == "LV4"'>
                        <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND info.lv4_prod_rnd_team_code IN ("
                                 close=")" index="index" separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
            <if test='monthAnalysisVO.granularityType == "PROD"'>
                <choose>
                    <when test='monthAnalysisVO.teamLevel == "LV1"'>
                        <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND info.lv1_prod_list_code IN ("
                                 close=")" index="index" separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='monthAnalysisVO.teamLevel == "LV2"'>
                        <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND info.lv2_prod_list_code IN ("
                                 close=")" index="index" separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='monthAnalysisVO.teamLevel == "LV3"'>
                        <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND info.lv3_prod_list_code IN ("
                                 close=")" index="index" separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='monthAnalysisVO.teamLevel == "LV4"'>
                        <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND info.lv4_prod_list_code IN ("
                                 close=")" index="index" separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
            <if test='monthAnalysisVO.granularityType == "INDUS"'>
                <choose>
                    <when test='monthAnalysisVO.teamLevel == "LV1"'>
                        <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND info.lv1_industry_catg_code IN ("
                                 close=")" index="index" separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='monthAnalysisVO.teamLevel == "LV2"'>
                        <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND info.lv2_industry_catg_code IN ("
                                 close=")" index="index" separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='monthAnalysisVO.teamLevel == "LV3"'>
                        <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND info.lv3_industry_catg_code IN ("
                                 close=")" index="index" separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='monthAnalysisVO.teamLevel == "LV4"'>
                        <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND info.lv4_industry_catg_code IN ("
                                 close=")" index="index" separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
        </if>
        </trim>
        ORDER BY t1.period_id
    </select>

    <select id="findDmFcstCombCostIndexVOList" resultMap="resultMap">
        SELECT
        DISTINCT
        custom_id as comb_id,
        custom_cn_name,
        group_level,
        group_code,
        group_cn_name,
        period_id,
        cost_index,
        last_update_date
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.blurTablePreFix}_cus_mon_cost_idx_t
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <include refid="combSerchWhere"></include>
             and del_flag = 'N'
        </trim>
        ORDER BY period_id
    </select>

    <sql id="combSerchWhere">
        <if test='monthAnalysisVO.versionId != null'>
            AND version_id = #{monthAnalysisVO.versionId}
        </if>
        <if test='monthAnalysisVO.granularityType != null'>
            AND granularity_type = #{monthAnalysisVO.granularityType}
        </if>
        <if test='monthAnalysisVO.combIdList != null and monthAnalysisVO.combIdList.size() > 0'>
            <foreach collection='monthAnalysisVO.combIdList' item="code" open="AND custom_id IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.basePeriodId != null'>
            AND base_period_id = #{monthAnalysisVO.basePeriodId}
        </if>
        <if test='monthAnalysisVO.periodstartTime != null and monthAnalysisVO.periodstartTime != ""'>
            AND period_id <![CDATA[ >= ]]> #{monthAnalysisVO.periodStartTime}
        </if>
        <if test='monthAnalysisVO.periodEndTime != null and monthAnalysisVO.periodEndTime != ""'>
            AND period_id <![CDATA[ <= ]]> #{monthAnalysisVO.periodEndTime}
        </if>
        <if test='monthAnalysisVO.softwareMark != null and monthAnalysisVO.softwareMark != ""'>
            AND software_mark = #{monthAnalysisVO.softwareMark,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            AND oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
            AND bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
            AND region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
            AND repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.mainFlag != null and monthAnalysisVO.mainFlag != ""'>
            AND main_flag = #{monthAnalysisVO.mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.codeAttributes != null and monthAnalysisVO.codeAttributes != ""'>
            AND code_attributes = #{monthAnalysisVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupLevel != null and monthAnalysisVO.groupLevel != ""'>
            and group_level = #{monthAnalysisVO.groupLevel,jdbcType=VARCHAR}
        </if>
    </sql>

    <select id="findBlurCostIndexVOList" resultMap="resultMap">
        SELECT
        <include refid="costIdxChartFields"/>
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.blurTablePreFix}_base_cus_mon_cost_idx_t
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <include refid="blurSearchWhere"/>
            and del_flag = 'N' and ytd_flag is null
        </trim>
        ORDER BY period_id
    </select>

    <sql id="multiCostIdxChartFields">
        DISTINCT
        <choose>
            <when test='monthAnalysisVO.granularityType == "PROD"'>
                t1.prod_list_cn_name,
                t1.prod_list_code,
            </when>
            <when test='monthAnalysisVO.granularityType == "INDUS"'>
                t1.industry_catg_cn_name,
                t1.industry_catg_code,
            </when>
            <otherwise>
                t1.prod_rd_team_cn_name,
                t1.prod_rnd_team_code,
            </otherwise>
        </choose>
        t1.parent_code,
        t1.parent_cn_name,
        t1.period_id,
        t1.group_level,
        t1.group_code,
        t1.group_cn_name,
        t1.cost_index,
        t2.weight_rate,
        ROUND(t2.weight_rate * 100, 2) || '%' AS weight_percent
    </sql>

    <sql id="multiIdxSearchWhere">
        <if test='monthAnalysisVO.basePeriodId != null'>
            AND t1.base_period_id = #{monthAnalysisVO.basePeriodId}
        </if>
        <if test='monthAnalysisVO.periodStartTime != null'>
            AND t1.period_id <![CDATA[ >= ]]> #{monthAnalysisVO.periodStartTime}
        </if>
        <if test='monthAnalysisVO.periodEndTime != null'>
            AND t1.period_id <![CDATA[ <= ]]> #{monthAnalysisVO.periodEndTime}
        </if>
        <if test='monthAnalysisVO.softwareMark != null and monthAnalysisVO.softwareMark != ""'>
            AND t1.software_mark = #{monthAnalysisVO.softwareMark,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.viewFlag != null and monthAnalysisVO.viewFlag != ""'>
            AND t1.view_flag = #{monthAnalysisVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            AND t1.oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
            AND t1.bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
            AND t1.region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
            AND t1.repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.mainFlag != null and monthAnalysisVO.mainFlag != ""'>
            AND t1.main_flag = #{monthAnalysisVO.mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.codeAttributes != null and monthAnalysisVO.codeAttributes != ""'>
            AND t1.code_attributes = #{monthAnalysisVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupLevel != null and monthAnalysisVO.groupLevel != ""'>
            and t1.group_level = #{monthAnalysisVO.groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.parentCodeList != null and monthAnalysisVO.parentCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code" open="AND t1.parent_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.subGroupCodeList != null and monthAnalysisVO.subGroupCodeList != ""'>
            <foreach collection='monthAnalysisVO.subGroupCodeList' item="code" open="AND t1.group_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.granularityType=="IRB" and monthAnalysisVO.prodRndTeamCodeList != null and monthAnalysisVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND t1.prod_rnd_team_code IN ("
                     close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.granularityType=="PROD" and monthAnalysisVO.prodRndTeamCodeList != null and monthAnalysisVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND t1.prod_list_code IN ("
                     close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.granularityType=="INDUS" and monthAnalysisVO.prodRndTeamCodeList != null and monthAnalysisVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND t1.industry_catg_code IN ("
                     close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.dimensionCode != null and monthAnalysisVO.dimensionCode != ""'>
            AND t1.dimension_code = #{monthAnalysisVO.dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.dimensionSubCategoryCode != null and monthAnalysisVO.dimensionSubCategoryCode != ""'>
            AND t1.dimension_subcategory_code = #{monthAnalysisVO.dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.dimensionSubDetailCode != null and monthAnalysisVO.dimensionSubDetailCode != ""'>
            AND t1.dimension_sub_detail_code = #{monthAnalysisVO.dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
    </sql>

    <select id="findMultiCostIndexVOList" resultMap="resultMap">
        SELECT
        <include refid="multiCostIdxChartFields"/>
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.tablePreFix}_mon_cost_idx_t t1
        LEFT JOIN fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.tablePreFix}_mon_weight_t t2
        ON t1.group_level = t2.group_level
        AND t1.version_id = t2.version_id
        AND t1.group_code = t2.group_code
        AND t1.parent_code = t2.parent_code
        <choose>
            <when test='monthAnalysisVO.granularityType == "PROD"'>
                AND t1.prod_list_code = t2.prod_list_code
            </when>
            <when test='monthAnalysisVO.granularityType == "INDUS"'>
                AND t1.industry_catg_code = t2.industry_catg_code
            </when>
            <otherwise>
                AND t1.prod_rnd_team_code = t2.prod_rnd_team_code
            </otherwise>
        </choose>
        AND t1.view_flag = t2.view_flag
        AND t1.oversea_flag = t2.oversea_flag
        AND t1.bg_code = t2.bg_code
        AND t1.region_code = t2.region_code
        AND t1.repoffice_code = t2.repoffice_code
        AND t1.main_flag = t2.main_flag
        AND t1.software_mark = t2.software_mark
        <if test='monthAnalysisVO.mainFlag == "Y"'>
            AND t1.code_attributes = t2.code_attributes
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='monthAnalysisVO.versionId != null'>
                AND t1.version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
            </if>
            <if test='monthAnalysisVO.intervalYear != null and monthAnalysisVO.intervalYear != ""'>
                AND t2.period_year = #{monthAnalysisVO.intervalYear,jdbcType=VARCHAR}
            </if>
            <include refid="multiIdxSearchWhere"/>
            <if test='monthAnalysisVO.begin != null and monthAnalysisVO.begin != "" and monthAnalysisVO.end != null and monthAnalysisVO.end != ""'>
                AND (t1.period_id <![CDATA[ = ]]> #{monthAnalysisVO.begin} or t1.period_id <![CDATA[ = ]]>
                #{monthAnalysisVO.end})
            </if>
            AND t2.del_flag = 'N'
            AND t1.del_flag = 'N'
        </trim>
        ORDER BY t2.weight_rate,t1.group_code, t1.period_id
    </select>

    <select id="findMultiCostIndexCount" resultType="int">
        SELECT count(1)
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.tablePreFix}_mon_cost_idx_t t1
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='monthAnalysisVO.versionId != null'>
                AND t1.version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
            </if>
            AND t1.del_flag = 'N'
            <include refid="multiIdxSearchWhere"/>
        </trim>
    </select>

    <select id="findPriceIndexCombChartByMultiDim" resultMap="resultMap">
        SELECT DISTINCT
        T2.custom_id as comb_id,
        T2.custom_cn_name,
        T2.parent_code,
        T2.parent_cn_name,
        T1.period_id,
        T1.group_level,
        T1.group_code,
        T1.group_cn_name,
        T1.cost_index
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.tablePreFix}_mon_cost_idx_t t1
        LEFT JOIN fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.blurTablePreFix}_cus_mon_weight_t t2
        on t1.group_code = t2.group_code
        and t1.version_id = t2.version_id
        and t1.parent_code = t2.parent_code
        and t1.oversea_flag = t2.oversea_flag
        and t1.bg_code = t2.bg_code
        and t1.region_code = t2.region_code
        and t1.repoffice_code = t2.repoffice_code
        and nvl ( t1.main_flag, 'snull' ) = nvl ( t2.main_flag, 'snull' )
        and nvl ( t1.code_attributes, 'snull' ) = nvl ( t2.code_attributes, 'snull' )
        <if test='monthAnalysisVO.costType == "PSP"'>
            and t1.software_mark = t2.software_mark
        </if>
        WHERE T1.version_id = #{monthAnalysisVO.versionId}
        AND T1.del_flag = 'N' and T1.group_level = 'SPART'
        <if test='monthAnalysisVO.granularityType != null'>
            AND T2.granularity_type = #{monthAnalysisVO.granularityType}
        </if>
        <if test='monthAnalysisVO.basePeriodId != null'>
            AND T1.base_period_id = #{monthAnalysisVO.basePeriodId}
        </if>
        <if test='monthAnalysisVO.softwareMark != null and monthAnalysisVO.softwareMark != ""'>
            AND T1.software_mark = #{monthAnalysisVO.softwareMark,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            AND T1.oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
            AND T1.bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
            AND T1.region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
            AND T1.repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.mainFlag != null and monthAnalysisVO.mainFlag != ""'>
            AND T1.main_flag = #{monthAnalysisVO.mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.codeAttributes != null and monthAnalysisVO.codeAttributes != ""'>
            AND T1.code_attributes = #{monthAnalysisVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.combIdList != null and monthAnalysisVO.combIdList.size() > 0'>
            <foreach collection='monthAnalysisVO.combIdList' item="code" open="AND T2.custom_id IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.combSubGroupCodeList != null and monthAnalysisVO.combSubGroupCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.combSubGroupCodeList' item="code"
                     open="AND T2.custom_id || '_##' || T2.parent_code || '_##' || T2.group_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.periodStartTime != null and monthAnalysisVO.periodStartTime != ""'>
            AND T1.period_id <![CDATA[ >= ]]> #{monthAnalysisVO.periodStartTime}
        </if>
        <if test='monthAnalysisVO.periodEndTime != null and monthAnalysisVO.periodEndTime != ""'>
            AND T1.period_id <![CDATA[ <= ]]> #{monthAnalysisVO.periodEndTime}
        </if>
        ORDER BY T1.group_code, T1.period_id
    </select>

    <select id="findBlurMinMultiCostIndexList" resultMap="resultMap">
        SELECT DISTINCT
        t1.parent_code,
        t1.parent_cn_name,
        t1.period_id,
        t1.group_level,
        t1.group_code,
        t1.group_cn_name,
        t1.cost_index,
        t1.lv4_code AS prod_rnd_team_code,
        t1.lv4_cn_name AS prod_rd_team_cn_name,
        t2.weight_rate,
        ROUND(t2.weight_rate * 100, 2) || '%' AS weight_percent
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.tablePreFix}_mon_mid_cost_idx_t t1
        LEFT JOIN fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.blurTablePreFix}_base_cus_mon_weight_t t2
         on t1.version_id = t2.version_id
        <if test='monthAnalysisVO.intervalYear != null and monthAnalysisVO.intervalYear != ""'>
            AND t2.period_year = #{monthAnalysisVO.intervalYear,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.customIdList != null and monthAnalysisVO.customIdList != ""'>
            <foreach collection='monthAnalysisVO.customIdList' item="code" open="AND t2.custom_id IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        AND t2.granularity_type = #{monthAnalysisVO.granularityType,jdbcType=VARCHAR}
        AND t1.group_code = t2.group_code
        AND t1.group_level = t2.group_level
        AND t1.view_flag = t2.view_flag
        AND t1.oversea_flag = t2.oversea_flag
        AND t1.bg_code = t2.bg_code
        AND t1.region_code = t2.region_code
        AND t1.repoffice_code = t2.repoffice_code
        AND t1.lv4_code = t2.lv4_code
        AND t1.main_flag = t2.main_flag
        AND t2.del_flag = 'N'
        <if test='monthAnalysisVO.costType == "PSP"'>
            AND t1.software_mark = t2.software_mark
        </if>
        <if test='monthAnalysisVO.mainFlag == "Y"'>
            AND t1.code_attributes = t2.code_attributes
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='monthAnalysisVO.versionId != null'>
                AND t1.version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
            </if>
             AND t1.del_flag = 'N'
            <if test='monthAnalysisVO.basePeriodId != null'>
                AND t1.base_period_id = #{monthAnalysisVO.basePeriodId}
            </if>
            <if test='monthAnalysisVO.periodStartTime != null'>
                AND t1.period_id <![CDATA[ >= ]]> #{monthAnalysisVO.periodStartTime}
            </if>
            <if test='monthAnalysisVO.periodEndTime != null'>
                AND t1.period_id <![CDATA[ <= ]]> #{monthAnalysisVO.periodEndTime}
            </if>
            <if test='monthAnalysisVO.softwareMark != null and monthAnalysisVO.softwareMark != ""'>
                AND t1.software_mark = #{monthAnalysisVO.softwareMark,jdbcType=VARCHAR}
            </if>
            <if test='monthAnalysisVO.viewFlag != null and monthAnalysisVO.viewFlag != ""'>
                AND t1.view_flag = #{monthAnalysisVO.viewFlag,jdbcType=VARCHAR}
            </if>
            <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
                AND t1.oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
                AND t1.bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
            </if>
            <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
                AND t1.region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
            </if>
            <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
                AND t1.repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='monthAnalysisVO.mainFlag != null and monthAnalysisVO.mainFlag != ""'>
                AND t1.main_flag = #{monthAnalysisVO.mainFlag,jdbcType=VARCHAR}
            </if>
            <if test='monthAnalysisVO.codeAttributes != null and monthAnalysisVO.codeAttributes != ""'>
                AND t1.code_attributes = #{monthAnalysisVO.codeAttributes,jdbcType=VARCHAR}
            </if>
            <choose>
                <when test='monthAnalysisVO.viewFlag == "PROD_SPART"'>
                    AND t1.group_level = 'SPART'
                </when>
                <when test='monthAnalysisVO.viewFlag == "DIMENSION"'>
                    AND t1.group_level = 'SUB_DETAIL'
                </when>
            </choose>
            <if test='monthAnalysisVO.lv4ProdRndTeamCodeList != null and monthAnalysisVO.lv4ProdRndTeamCodeList != ""'>
                <foreach collection='monthAnalysisVO.lv4ProdRndTeamCodeList' item="code" open="AND t1.lv4_code IN ("
                         close=")" index="index" separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='monthAnalysisVO.customGroupCodeList != null and monthAnalysisVO.customGroupCodeList != ""'>
                <foreach collection='monthAnalysisVO.customGroupCodeList' item="code" open="AND t1.group_code IN ("
                         close=")" index="index" separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='monthAnalysisVO.customSubGroupCodeList != null and monthAnalysisVO.customSubGroupCodeList != ""'>
                <foreach collection='monthAnalysisVO.customSubGroupCodeList' item="code" open="AND t1.group_code IN ("
                         close=")" index="index" separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='monthAnalysisVO.prodRndTeamCodeList != null and monthAnalysisVO.prodRndTeamCodeList.size() > 0'>
                <choose>
                    <when test='monthAnalysisVO.teamLevel == "LV0"'>
                        <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND t1.lv0_code IN ("
                                 close=")" index="index" separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='monthAnalysisVO.teamLevel == "LV1"'>
                        <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND t1.lv1_code IN ("
                                 close=")" index="index" separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='monthAnalysisVO.teamLevel == "LV2"'>
                        <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND t1.lv2_code IN ("
                                 close=")" index="index" separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='monthAnalysisVO.teamLevel == "LV3"'>
                        <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND t1.lv3_code IN ("
                                 close=")" index="index" separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <when test='monthAnalysisVO.teamLevel == "LV4"'>
                        <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND t1.lv4_code IN ("
                                 close=")" index="index" separator=",">
                            #{code}
                        </foreach>
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
        </trim>
        ORDER BY t2.weight_rate,t1.group_code, t1.period_id
    </select>

    <select id="findBlurMultiCostIndexVOList" resultMap="resultMap">
        SELECT DISTINCT
        t1.parent_code,
        t1.parent_cn_name,
        t1.period_id,
        t1.group_level,
        t1.group_code,
        t1.group_cn_name,
        t1.cost_index,
        t2.weight_rate,
        ROUND(t2.weight_rate * 100, 2) || '%' AS weight_percent
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.blurTablePreFix}_base_cus_mon_cost_idx_t t1
        LEFT JOIN fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.blurTablePreFix}_base_cus_mon_weight_t t2
        ON t1.group_level = t2.group_level
        AND t1.version_id = t2.version_id
        AND t1.group_code = t2.group_code
        AND t1.parent_level = t2.parent_level
        AND t1.parent_code = t2.parent_code
        AND t1.custom_id = t2.custom_id
        AND t1.view_flag = t2.view_flag
        AND t1.granularity_type = t2.granularity_type
        AND t1.oversea_flag = t2.oversea_flag
        AND t1.bg_code = t2.bg_code
        AND t1.region_code = t2.region_code
        AND t1.repoffice_code = t2.repoffice_code
        AND t1.main_flag = t2.main_flag
        <if test='monthAnalysisVO.costType == "PSP"'>
            AND t1.software_mark = t2.software_mark
        </if>
        <if test='monthAnalysisVO.mainFlag == "Y"'>
            AND t1.code_attributes = t2.code_attributes
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <include refid="blurMultiIdxSearchWhere"/>
            <if test='monthAnalysisVO.intervalYear != null and monthAnalysisVO.intervalYear != ""'>
                AND t2.period_year = #{monthAnalysisVO.intervalYear,jdbcType=VARCHAR}
            </if>
            AND t2.del_flag = 'N' AND t1.del_flag = 'N' and t1.ytd_flag is null
        </trim>
        ORDER BY t2.weight_rate,t1.group_code, t1.period_id
    </select>

    <sql id="blurMultiIdxSearchWhere">
        <if test='monthAnalysisVO.versionId != null'>
            AND t1.version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='monthAnalysisVO.basePeriodId != null'>
            AND t1.base_period_id = #{monthAnalysisVO.basePeriodId}
        </if>
        <if test='monthAnalysisVO.periodStartTime != null'>
            AND t1.period_id <![CDATA[ >= ]]> #{monthAnalysisVO.periodStartTime}
        </if>
        <if test='monthAnalysisVO.periodEndTime != null'>
            AND t1.period_id <![CDATA[ <= ]]> #{monthAnalysisVO.periodEndTime}
        </if>
        <if test='monthAnalysisVO.begin != null and monthAnalysisVO.begin != "" and monthAnalysisVO.end != null and monthAnalysisVO.end != ""'>
            AND (t1.period_id <![CDATA[ = ]]> #{monthAnalysisVO.begin} or t1.period_id <![CDATA[ = ]]>
            #{monthAnalysisVO.end})
        </if>
        <if test='monthAnalysisVO.customIdList != null and monthAnalysisVO.customIdList != ""'>
            <foreach collection='monthAnalysisVO.customIdList' item="code" open="AND t1.custom_id IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.granularityType != null and monthAnalysisVO.granularityType != ""'>
            AND t1.granularity_type = #{monthAnalysisVO.granularityType,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.softwareMark != null and monthAnalysisVO.softwareMark != ""'>
            AND t1.software_mark = #{monthAnalysisVO.softwareMark,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.viewFlag != null and monthAnalysisVO.viewFlag != ""'>
            AND t1.view_flag = #{monthAnalysisVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            AND t1.oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
            AND t1.bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
            AND t1.region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
            AND t1.repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.mainFlag != null and monthAnalysisVO.mainFlag != ""'>
            AND t1.main_flag = #{monthAnalysisVO.mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.codeAttributes != null and monthAnalysisVO.codeAttributes != ""'>
            AND t1.code_attributes = #{monthAnalysisVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.parentLevel != null and monthAnalysisVO.parentLevel != ""'>
            AND t1.parent_level = #{monthAnalysisVO.parentLevel,jdbcType=VARCHAR}
        </if>
        <choose>
            <when test='monthAnalysisVO.viewFlag == "PROD_SPART"'>
                AND t1.group_level = 'SPART'
            </when>
            <when test='monthAnalysisVO.viewFlag == "DIMENSION"'>
                AND t1.group_level = 'SUB_DETAIL'
            </when>
        </choose>
        <if test='monthAnalysisVO.parentCodeList != null and monthAnalysisVO.parentCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code" open="AND t1.parent_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.customSubGroupCodeList != null and monthAnalysisVO.customSubGroupCodeList != ""'>
            <foreach collection='monthAnalysisVO.customSubGroupCodeList' item="code" open="AND t1.group_code IN ("
                     close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
    </sql>

    <sql id="reduceCostIdxFields">
        DISTINCT
        version_id,
        period_year,
        period_id,
        parent_code,
        group_code,
        group_cn_name,
        group_level,
        cost_index,
	    ytd_cost_index,
        cost_reduction_rate,
        cost_reduction_cn_name,
        cost_reduction_level
    </sql>

    <sql id="reduceCostIndexSearchWhere">
        <if test='monthAnalysisVO.versionId != null'>
            AND version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='monthAnalysisVO.periodYear != null'>
            AND period_year = #{monthAnalysisVO.periodYear}
        </if>
        <if test='monthAnalysisVO.viewFlag != null and monthAnalysisVO.viewFlag != ""'>
            AND view_flag = #{monthAnalysisVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.softwareMark != null and monthAnalysisVO.softwareMark != ""'>
            AND software_mark = #{monthAnalysisVO.softwareMark,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            AND oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
            AND bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
            AND region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
            AND repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.mainFlag != null and monthAnalysisVO.mainFlag != ""'>
            AND main_flag = #{monthAnalysisVO.mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.codeAttributes != null and monthAnalysisVO.codeAttributes != ""'>
            AND code_attributes = #{monthAnalysisVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupLevel != null and monthAnalysisVO.groupLevel != ""'>
            AND group_level = #{monthAnalysisVO.groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.costReductionLevel != null and monthAnalysisVO.costReductionLevel != ""'>
            AND cost_reduction_level = #{monthAnalysisVO.costReductionLevel,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.viewFlag == "PROD_SPART" or monthAnalysisVO.groupLevel == "SPART"'>
            <if test='monthAnalysisVO.groupCodeList != null and monthAnalysisVO.groupCodeList != ""'>
                <foreach collection='monthAnalysisVO.groupCodeList' item="code" open="AND group_code IN (" close=")"
                         index="index" separator=",">
                    #{code}
                </foreach>
            </if>
        </if>
        <if test='monthAnalysisVO.granularityType=="IRB" and monthAnalysisVO.prodRndTeamCodeList != null and monthAnalysisVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND prod_rnd_team_code IN ("
                     close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.granularityType=="PROD" and monthAnalysisVO.prodRndTeamCodeList != null and monthAnalysisVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND prod_list_code IN ("
                     close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.granularityType=="INDUS" and monthAnalysisVO.prodRndTeamCodeList != null and monthAnalysisVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND industry_catg_code IN ("
                     close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.viewFlag == "DIMENSION"'>
            <if test='monthAnalysisVO.groupLevel == "DIMENSION" and monthAnalysisVO.dimensionCode != null and monthAnalysisVO.dimensionCode != ""'>
                AND group_code = #{monthAnalysisVO.dimensionCode,jdbcType=VARCHAR}
            </if>
            <if test='monthAnalysisVO.groupLevel == "SUBCATEGORY" and monthAnalysisVO.dimensionSubCategoryCode != null and monthAnalysisVO.dimensionSubCategoryCode != ""'>
                AND group_code = #{monthAnalysisVO.dimensionSubCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test='monthAnalysisVO.groupLevel == "SUB_DETAIL" and monthAnalysisVO.dimensionSubDetailCode != null and monthAnalysisVO.dimensionSubDetailCode != ""'>
                AND group_code = #{monthAnalysisVO.dimensionSubDetailCode,jdbcType=VARCHAR}
            </if>
        </if>
    </sql>

    <sql id="blurReduceCostIndexSearchWhere">
        <if test='monthAnalysisVO.versionId != null'>
            AND version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='monthAnalysisVO.periodYear != null'>
            AND period_year = #{monthAnalysisVO.periodYear}
        </if>
        <if test='monthAnalysisVO.viewFlag != null and monthAnalysisVO.viewFlag != ""'>
            AND view_flag = #{monthAnalysisVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.softwareMark != null and monthAnalysisVO.softwareMark != ""'>
            AND software_mark = #{monthAnalysisVO.softwareMark,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            AND oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
            AND bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
            AND region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
            AND repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.mainFlag != null and monthAnalysisVO.mainFlag != ""'>
            AND main_flag = #{monthAnalysisVO.mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.codeAttributes != null and monthAnalysisVO.codeAttributes != ""'>
            AND code_attributes = #{monthAnalysisVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupLevel != null and monthAnalysisVO.groupLevel != ""'>
            AND group_level = #{monthAnalysisVO.groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.customGroupCodeList != null and monthAnalysisVO.customGroupCodeList != ""'>
            <foreach collection='monthAnalysisVO.customGroupCodeList' item="code" open="AND group_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.customIdList != null and monthAnalysisVO.customIdList != ""'>
            <foreach collection='monthAnalysisVO.customIdList' item="code" open="AND custom_id IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
    </sql>

    <sql id="combReduceCostIndexSearchWhere">
        <if test='monthAnalysisVO.versionId != null'>
            AND version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='monthAnalysisVO.periodYear != null'>
            AND period_year = #{monthAnalysisVO.periodYear}
        </if>
        <if test='monthAnalysisVO.softwareMark != null and monthAnalysisVO.softwareMark != ""'>
            AND software_mark = #{monthAnalysisVO.softwareMark,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            AND oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
            AND bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
            AND region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
            AND repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.mainFlag != null and monthAnalysisVO.mainFlag != ""'>
            AND main_flag = #{monthAnalysisVO.mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.codeAttributes != null and monthAnalysisVO.codeAttributes != ""'>
            AND code_attributes = #{monthAnalysisVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupLevel != null and monthAnalysisVO.groupLevel != ""'>
            AND group_level = #{monthAnalysisVO.groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.combIdList != null and monthAnalysisVO.combIdList != ""'>
            <foreach collection='monthAnalysisVO.combIdList' item="code" open="AND custom_id IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
    </sql>

    <select id="findReduceCostTargetList" resultMap="resultMap">
        SELECT
        <include refid="reduceCostIdxFields"/>
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.blurTablePreFix}_mon_cost_red_idx_t
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <include refid="reduceCostIndexSearchWhere"/>
            and del_flag = 'N'
        </trim>
        ORDER BY period_id
    </select>

    <select id="findBlurReduceCostTargetList" resultMap="resultMap">
        SELECT
        DISTINCT
        version_id,
        period_year,
        period_id,
        parent_code,
        group_code,
        group_cn_name,
        group_level,
        cost_index,
        ytd_cost_index,
        cost_reduction_rate
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.blurTablePreFix}_base_cus_mon_cost_red_idx_t
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <include refid="blurReduceCostIndexSearchWhere"/>
            and del_flag = 'N'
        </trim>
        ORDER BY period_id
    </select>

    <select id="findCombReduceCostTargetList" resultMap="resultMap">
        SELECT
        DISTINCT
        version_id,
        period_year,
        period_id,
        group_code,
        group_cn_name,
        group_level,
        cost_index,
        ytd_cost_index,
        cost_reduction_rate
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.blurTablePreFix}_cus_mon_cost_red_idx_t
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <include refid="combReduceCostIndexSearchWhere"/>
            and del_flag = 'N'
        </trim>
        ORDER BY period_id
    </select>
    <select id="findActualMonth" resultType="java.lang.String">
        SELECT IFNULL(max(period_id), 0)
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.tablePreFix}_mon_cost_idx_t
        WHERE del_flag = 'N'
          AND group_level = 'LV0'
          AND bg_code = 'GR'
          AND oversea_flag = 'G'
          AND region_code = 'GLOBAL'
          AND version_id = (
            SELECT version_id
            FROM fin_dm_opt_foi.dm_fcst_ict_version_info_t
            WHERE del_flag = 'N'
              AND is_running = 'N'
              AND status = 1
              AND data_type = 'MONTH'
              AND version_type IN ('AUTO', 'FINAL')
            ORDER BY creation_date DESC
            LIMIT 1
            )
    </select>

    <select id="findStartEndTime" resultType="com.huawei.it.fcst.industry.pbi.vo.month.PeriodIdDimVO">
        SELECT version_id AS versionId,
        granularitytype AS granularityType,
        MIN(starttime) AS startTime,
        MAX(endtime) AS endTime
        FROM fin_dm_opt_foi.dm_fcst_ict_period_id_dim
        WHERE 1 = 1
        <if test='versionId != null'>
            AND version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        GROUP BY version_id, granularitytype
    </select>

    <select id="findCostIndexCount" resultType="int">
        SELECT count(1)
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.tablePreFix}_mon_cost_idx_t t1
        WHERE del_flag = 'N'
        <include refid="searchWhere"/>
    </select>

    <select id="findCombCostIndexCount" resultType="int">
        SELECT
        count(1)
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.blurTablePreFix}_cus_mon_cost_idx_t
        WHERE del_flag = 'N'
        <include refid="combSerchWhere"></include>
    </select>

    <select id="findMixCombCostIndexCount" resultType="int">
        select sum(count) from (
        SELECT
        count(1) as count
        FROM fin_dm_opt_foi.dm_fcst_ict_psp_cus_mon_cost_idx_t
        WHERE del_flag = 'N'
        <include refid="combSerchWhere"></include>
        union all
        SELECT
        count(1) as count
        FROM fin_dm_opt_foi.dm_fcst_ict_std_cus_mon_cost_idx_t
        WHERE del_flag = 'N'
        <include refid="combSerchWhere"></include>
        )
    </select>

    <select id="findMixCostIndexCount" resultType="int">
        select sum(count) from (
        SELECT count(1) as count
        FROM fin_dm_opt_foi.dm_fcst_ict_psp_${monthAnalysisVO.granularityType}_mon_cost_idx_t t1
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <include refid="searchWhere"/>
            and del_flag = 'N'
        </trim>
        union all
        SELECT count(1) as count
        FROM fin_dm_opt_foi.dm_fcst_ict_std_${monthAnalysisVO.granularityType}_mon_cost_idx_t t1
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <include refid="searchWhere"/>
            and del_flag = 'N'
        </trim>
        )
    </select>

    <select id="findBasePeriodId" resultType="java.lang.String">
        SELECT MAX(period_year) - 1 || '01'
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.tablePreFix}_mon_cost_idx_t
        WHERE version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
          AND del_flag = 'N'
    </select>

    <select id="findMixBasePeriodId" resultType="java.lang.String">
        SELECT MAX(period_year) - 1 || '01'
        FROM fin_dm_opt_foi.dm_fcst_ict_std_${monthAnalysisVO.granularityType}_mon_cost_idx_t
        WHERE version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
          AND del_flag = 'N'
    </select>

    <select id="callFuncRefreshData" resultType="java.lang.String">
        SELECT fin_dm_opt_foi.f_dm_fcst_ict_period_id_reset(#{jsonStr})
    </select>

</mapper>
