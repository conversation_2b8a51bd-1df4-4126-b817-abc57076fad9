/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.utils;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.huawei.it.fcst.industry.index.constant.CommonConstant;
import com.huawei.it.fcst.industry.index.impl.annual.StatisticsExcelService;
import com.huawei.it.fcst.industry.index.vo.common.AbstractExcelTitleVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFoiImpExpRecordVO;
import com.huawei.it.fcst.industry.index.vo.common.ExcelVO;
import com.huawei.it.fcst.industry.index.vo.common.ExportExcelVo;
import com.huawei.it.fcst.industry.index.vo.common.HeaderVo;
import com.huawei.it.fcst.industry.index.vo.common.LeafExcelTitleVO;
import com.huawei.it.fcst.industry.index.vo.common.UploadInfoVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.security.UserVO;

import com.sun.istack.NotNull;
import com.sun.istack.Nullable;

import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.ss.usermodel.CellType;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.servlet.ServletOutputStream;
import javax.servlet.WriteListener;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.core.MultivaluedMap;

/**
 * ExcelUtilTest Class
 *
 * <AUTHOR>
 * @since 2023/4/27
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class ExcelUtilTest {
    private static final Logger LOGGER = LogManager.getLogger(ExcelUtilTest.class);
    @InjectMocks
    private ExcelUtil excelUtil;

    @Mock
    private ExcelUtilPro excelUtilPro;

    @Mock
    private StatisticsExcelService statisticsExcelService;

    @Mock
    private UserInfoUtils userInfoUtils;

    @Before
    public void before() throws Exception {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);
    }

    HttpServletResponse response = new HttpServletResponse() {
        @Override
        public void addCookie(Cookie cookie) {

        }

        @Override
        public boolean containsHeader(String s) {
            return false;
        }

        @Override
        public String encodeURL(String s) {
            return null;
        }

        @Override
        public String encodeRedirectURL(String s) {
            return null;
        }

        @Override
        public String encodeUrl(String s) {
            return null;
        }

        @Override
        public String encodeRedirectUrl(String s) {
            return null;
        }

        @Override
        public void sendError(int i, String s) throws IOException {

        }

        @Override
        public void sendError(int i) throws IOException {

        }

        @Override
        public void sendRedirect(String s) throws IOException {

        }

        @Override
        public void setDateHeader(String s, long l) {

        }

        @Override
        public void addDateHeader(String s, long l) {

        }

        @Override
        public void setHeader(String s, String s1) {

        }

        @Override
        public void addHeader(String s, String s1) {

        }

        @Override
        public void setIntHeader(String s, int i) {

        }

        @Override
        public void addIntHeader(String s, int i) {

        }

        @Override
        public void setStatus(int i) {

        }

        @Override
        public void setStatus(int i, String s) {

        }

        @Override
        public int getStatus() {
            return 0;
        }

        @Override
        public String getHeader(String s) {
            return null;
        }

        @Override
        public Collection<String> getHeaders(String s) {
            return null;
        }

        @Override
        public Collection<String> getHeaderNames() {
            return null;
        }

        @Override
        public String getCharacterEncoding() {
            return null;
        }

        @Override
        public String getContentType() {
            return null;
        }

        @Override
        public ServletOutputStream getOutputStream() throws IOException {
            ServletOutputStream ServletOutputStream = new ServletOutputStream() {
                @Override
                public void write(int b) throws IOException {

                }

                @Override
                public boolean isReady() {
                    return false;
                }

                @Override
                public void setWriteListener(WriteListener writeListener) {

                }
            };
            return ServletOutputStream;
        }

        @Override
        public PrintWriter getWriter() throws IOException {
            return null;
        }

        @Override
        public void setCharacterEncoding(String s) {

        }

        @Override
        public void setContentLength(int i) {

        }

        @Override
        public void setContentLengthLong(long l) {

        }

        @Override
        public void setContentType(String s) {

        }

        @Override
        public void setBufferSize(int i) {

        }

        @Override
        public int getBufferSize() {
            return 0;
        }

        @Override
        public void flushBuffer() throws IOException {

        }

        @Override
        public void resetBuffer() {

        }

        @Override
        public boolean isCommitted() {
            return false;
        }

        @Override
        public void reset() {

        }

        @Override
        public void setLocale(Locale locale) {

        }

        @Override
        public Locale getLocale() {
            return null;
        }
    };

    private Attachment attachment = new Attachment("", new DataHandler(new DataSource() {
        @Override
        public InputStream getInputStream() throws IOException {
            return new InputStream() {
                @Override
                public int read() throws IOException {
                    return 0;
                }
            };
        }

        @Override
        public OutputStream getOutputStream() throws IOException {
            return null;
        }

        @Override
        public String getContentType() {
            return null;
        }

        @Override
        public String getName() {
            return "test.xlsxs";
        }
    }),
            new MultivaluedMap() {
                @Override
                public int size() {
                    return 0;
                }

                @Override
                public boolean isEmpty() {
                    return false;
                }

                @Override
                public boolean containsKey(Object key) {
                    return false;
                }

                @Override
                public boolean containsValue(Object value) {
                    return false;
                }

                @Override
                public Object get(Object key) {
                    return null;
                }

                @Nullable
                @Override
                public Object put(Object key, Object value) {
                    return null;
                }

                @Override
                public Object remove(Object key) {
                    return null;
                }

                @Override
                public void putAll(@NotNull Map m) {

                }

                @Override
                public void clear() {

                }

                @NotNull
                @Override
                public Set keySet() {
                    return null;
                }

                @NotNull
                @Override
                public Collection values() {
                    return null;
                }

                @NotNull
                @Override
                public Set<Map.Entry> entrySet() {
                    return new Set<Map.Entry>() {
                        @Override
                        public int size() {
                            return 0;
                        }

                        @Override
                        public boolean isEmpty() {
                            return false;
                        }

                        @Override
                        public boolean contains(Object o) {
                            return false;
                        }

                        @NotNull
                        @Override
                        public Iterator<Map.Entry> iterator() {
                            return new Iterator<Map.Entry>() {
                                @Override
                                public boolean hasNext() {
                                    return false;
                                }

                                @Override
                                public Map.Entry next() {
                                    return null;
                                }
                            };
                        }

                        @NotNull
                        @Override
                        public Object[] toArray() {
                            return new Object[0];
                        }

                        @NotNull
                        @Override
                        public <T> T[] toArray(@NotNull T[] a) {
                            return null;
                        }

                        @Override
                        public boolean add(Map.Entry entry) {
                            return false;
                        }

                        @Override
                        public boolean remove(Object o) {
                            return false;
                        }

                        @Override
                        public boolean containsAll(@NotNull Collection<?> c) {
                            return false;
                        }

                        @Override
                        public boolean addAll(@NotNull Collection<? extends Map.Entry> c) {
                            return false;
                        }

                        @Override
                        public boolean retainAll(@NotNull Collection<?> c) {
                            return false;
                        }

                        @Override
                        public boolean removeAll(@NotNull Collection<?> c) {
                            return false;
                        }

                        @Override
                        public void clear() {

                        }
                    };
                }

                @Override
                public boolean equals(Object o) {
                    return false;
                }

                @Override
                public int hashCode() {
                    return 0;
                }

                @Override
                public void putSingle(Object o, Object o2) {

                }

                @Override
                public void add(Object o, Object o2) {

                }

                @Override
                public Object getFirst(Object o) {
                    return null;
                }

                @Override
                public void addAll(Object o, Object[] objects) {

                }

                @Override
                public void addAll(Object o, List list) {

                }

                @Override
                public void addFirst(Object o, Object o2) {

                }

                @Override
                public boolean equalsIgnoreValueOrder(MultivaluedMap multivaluedMap) {
                    return false;
                }
            });

    private Attachment attachment2 = new Attachment("", new DataHandler(new DataSource() {
        @Override
        public InputStream getInputStream() throws IOException {
            return new InputStream() {
                @Override
                public int read() throws IOException {
                    return 0;
                }
            };
        }

        @Override
        public OutputStream getOutputStream() throws IOException {
            return null;
        }

        @Override
        public String getContentType() {
            return null;
        }

        @Override
        public String getName() {
            return "test.xlsx";
        }
    }),
            new MultivaluedMap() {
                @Override
                public int size() {
                    return 0;
                }

                @Override
                public boolean isEmpty() {
                    return false;
                }

                @Override
                public boolean containsKey(Object key) {
                    return false;
                }

                @Override
                public boolean containsValue(Object value) {
                    return false;
                }

                @Override
                public Object get(Object key) {
                    return null;
                }

                @Nullable
                @Override
                public Object put(Object key, Object value) {
                    return null;
                }

                @Override
                public Object remove(Object key) {
                    return null;
                }

                @Override
                public void putAll(@NotNull Map m) {

                }

                @Override
                public void clear() {

                }

                @NotNull
                @Override
                public Set keySet() {
                    return null;
                }

                @NotNull
                @Override
                public Collection values() {
                    return null;
                }

                @NotNull
                @Override
                public Set<Map.Entry> entrySet() {
                    return new Set<Map.Entry>() {
                        @Override
                        public int size() {
                            return 0;
                        }

                        @Override
                        public boolean isEmpty() {
                            return false;
                        }

                        @Override
                        public boolean contains(Object o) {
                            return false;
                        }

                        @NotNull
                        @Override
                        public Iterator<Map.Entry> iterator() {
                            return new Iterator<Map.Entry>() {
                                @Override
                                public boolean hasNext() {
                                    return false;
                                }

                                @Override
                                public Map.Entry next() {
                                    return null;
                                }
                            };
                        }

                        @NotNull
                        @Override
                        public Object[] toArray() {
                            return new Object[0];
                        }

                        @NotNull
                        @Override
                        public <T> T[] toArray(@NotNull T[] a) {
                            return null;
                        }

                        @Override
                        public boolean add(Map.Entry entry) {
                            return false;
                        }

                        @Override
                        public boolean remove(Object o) {
                            return false;
                        }

                        @Override
                        public boolean containsAll(@NotNull Collection<?> c) {
                            return false;
                        }

                        @Override
                        public boolean addAll(@NotNull Collection<? extends Map.Entry> c) {
                            return false;
                        }

                        @Override
                        public boolean retainAll(@NotNull Collection<?> c) {
                            return false;
                        }

                        @Override
                        public boolean removeAll(@NotNull Collection<?> c) {
                            return false;
                        }

                        @Override
                        public void clear() {

                        }
                    };
                }

                @Override
                public boolean equals(Object o) {
                    return false;
                }

                @Override
                public int hashCode() {
                    return 0;
                }

                @Override
                public void putSingle(Object o, Object o2) {

                }

                @Override
                public void add(Object o, Object o2) {

                }

                @Override
                public Object getFirst(Object o) {
                    return null;
                }

                @Override
                public void addAll(Object o, Object[] objects) {

                }

                @Override
                public void addAll(Object o, List list) {

                }

                @Override
                public void addFirst(Object o, Object o2) {

                }

                @Override
                public boolean equalsIgnoreValueOrder(MultivaluedMap multivaluedMap) {
                    return false;
                }
            });

    @Test
    public void expSelectColumnExcel() throws Exception {
        List<AbstractExcelTitleVO> formInfoVo = new ArrayList<>();
        LeafExcelTitleVO columnCnVersion =
                new LeafExcelTitleVO(
                        CommonConstant.VERSION_TITLE,
                        CommonConstant.WIDTH,
                        true,
                        "version",
                        "version",
                        CellType.STRING,
                        false);
        formInfoVo.add(columnCnVersion);
        LeafExcelTitleVO columnCnVersionName =
                new LeafExcelTitleVO(
                        "202301-TOP品类-Auto", CommonConstant.WIDTH, true, "versionName", "versionName", CellType.STRING, false);
        formInfoVo.add(columnCnVersionName);

        List<Map> cateItemWithWeightList = new ArrayList<>();
        Map map = new HashMap();
        map.put("l3_ceg_short_cn_name", "cpu");
        map.put("l3_ceg_cn_name", "cpu");
        map.put("l4_ceg_short_cn_name", "cpu");
        map.put("l4_ceg_cn_name", "cpu");
        map.put("category_name", "cpu");
        map.put("category_code", "1234E");
        cateItemWithWeightList.add(map);
        LeafExcelTitleVO column;
        List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
        Set<String> titles = new LinkedHashSet<>();
        for (HeaderVo header : CommonConstant.MAPPING_RELATIONAL_DIMENSION_HEADER) {
            column =
                    new LeafExcelTitleVO(
                            header.getTitle(),
                            header.getWidth(),
                            true,
                            header.getField(),
                            header.getField(),
                            header.getDataType(),
                            header.getIsEditable());
            titleVoList.add(column);
            titles.add(header.getField());
        }

        List<ExportExcelVo> exportExcelVoList = new ArrayList<>();
        ExportExcelVo exportExcelVo =
                ExportExcelVo.builder()
                        .formInfoVo(formInfoVo)
                        .titleVoList(titleVoList)
                        .list(cateItemWithWeightList)
                        .sheetName("品类清单")
                        .mergeCell(false)
                        .build();
        exportExcelVoList.add(exportExcelVo);

        exportExcelVo.setSelectedLeafExcelTitleVO(titleVoList);
        exportExcelVo.setFormInfoVo(titleVoList);
        exportExcelVoList.add(exportExcelVo);

        int titleRowCount = 256;
        DmFoiImpExpRecordVO dmFoiImpExpRecord = new DmFoiImpExpRecordVO();
        dmFoiImpExpRecord.setFileName("test");

        when(excelUtilPro.adjustTitleVoList(any(), any())).thenReturn(titleRowCount);

        DmFoiImpExpRecordVO dmFoiImpExpRecordVO = new DmFoiImpExpRecordVO();
        try {
            dmFoiImpExpRecordVO = excelUtil.expSelectColumnExcel(exportExcelVoList, response);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(dmFoiImpExpRecordVO);
    }

    @Test
    public void expSelectColumnExcel2Test() throws Exception {
        List<AbstractExcelTitleVO> formInfoVo = new ArrayList<>();
        LeafExcelTitleVO columnCnVersion =
            new LeafExcelTitleVO(
                CommonConstant.VERSION_TITLE,
                CommonConstant.WIDTH,
                true,
                "version",
                "version",
                CellType.STRING,
                false);
        formInfoVo.add(columnCnVersion);
        LeafExcelTitleVO columnCnVersionName =
            new LeafExcelTitleVO(
                "202301-TOP品类-Auto", CommonConstant.WIDTH, true, "versionName", "versionName", CellType.STRING, false);
        formInfoVo.add(columnCnVersionName);

        List<Map> cateItemWithWeightList = new ArrayList<>();
        Map map = new HashMap();
        map.put("l3_ceg_short_cn_name", "cpu");
        map.put("l3_ceg_cn_name", "cpu");
        map.put("l4_ceg_short_cn_name", "cpu");
        map.put("l4_ceg_cn_name", "cpu");
        map.put("category_name", "cpu");
        map.put("category_code", "1234E");
        cateItemWithWeightList.add(map);
        LeafExcelTitleVO column;
        List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
        Set<String> titles = new LinkedHashSet<>();
        for (HeaderVo header : CommonConstant.MAPPING_RELATIONAL_DIMENSION_HEADER) {
            column =
                new LeafExcelTitleVO(
                    header.getTitle(),
                    header.getWidth(),
                    true,
                    header.getField(),
                    header.getField(),
                    header.getDataType(),
                    header.getIsEditable());
            titleVoList.add(column);
            titles.add(header.getField());
        }

        List<ExportExcelVo> exportExcelVoList = new ArrayList<>();
        ExportExcelVo exportExcelVo =
            ExportExcelVo.builder()
                .formInfoVo(formInfoVo)
                .titleVoList(titleVoList)
                .list(cateItemWithWeightList)
                .sheetName("品类清单")
                .mergeCell(true)
                .build();
        exportExcelVoList.add(exportExcelVo);

        exportExcelVo.setSelectedLeafExcelTitleVO(titleVoList);
        exportExcelVo.setFormInfoVo(titleVoList);
        exportExcelVoList.add(exportExcelVo);

        int titleRowCount = 256;
        DmFoiImpExpRecordVO dmFoiImpExpRecord = new DmFoiImpExpRecordVO();
        dmFoiImpExpRecord.setFileName("test");

        when(excelUtilPro.adjustTitleVoList(any(), any())).thenReturn(titleRowCount);

        DmFoiImpExpRecordVO dmFoiImpExpRecordVO = new DmFoiImpExpRecordVO();
        try {
            dmFoiImpExpRecordVO = excelUtil.expSelectColumnExcel(exportExcelVoList, response);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(dmFoiImpExpRecordVO);
    }

    @Test
    public void importExcel() throws IOException {
        List<ExcelVO> heads = new ArrayList();
        List<HeaderVo> model = new LinkedList<>();
        model.addAll(CommonConstant.MAPPING_RELATIONAL_DIMENSION_HEADER);
        setValues(heads, model);
        UploadInfoVO uploadInfoVO = new UploadInfoVO();
        uploadInfoVO.setFileName("3434");
        uploadInfoVO.setFileSize(12);
        Map<String, Object> params = new HashMap<>();
        params.put("maxRowNum", 25000);
        uploadInfoVO.setParams(params);
        byte[] byte1 = {10, 11};
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        byte[] bytes = outputStream.toByteArray();
        bytes = byte1;
        List<LinkedHashMap<String, Object>> linkedHashMaps = new ArrayList<>();
        try {
            linkedHashMaps = excelUtil.importExcel(attachment, heads, uploadInfoVO, outputStream);
        } catch (CommonApplicationException e) {
            LOGGER.info("只能使用XLSX OR XLS格式EXCEL的文件.");
        }
        Assert.assertNotNull(linkedHashMaps);
    }

    @Test
    public void importExcel2Test() throws IOException {

        List<ExcelVO> heads = new ArrayList();
        List<HeaderVo> model = new LinkedList<>();
        model.addAll(CommonConstant.MAPPING_RELATIONAL_DIMENSION_HEADER);
        setValues(heads, model);
        UploadInfoVO uploadInfoVO = new UploadInfoVO();
        uploadInfoVO.setFileName("3434");
        uploadInfoVO.setFileSize(12);
        Map<String, Object> params = new HashMap<>();
        params.put("maxRowNum", 25000);
        uploadInfoVO.setParams(params);

        Resource resource = new ClassPathResource("excel.export.template/UTtestTemplate1.xlsx");
        InputStream inputStream = resource.getInputStream();
        ByteArrayOutputStream byteArrayOutputStream = putInputStreamCacher(inputStream);
        List<LinkedHashMap<String, Object>> linkedHashMaps = new ArrayList<>();
        try {
            linkedHashMaps = excelUtil.importExcel(attachment2, heads, uploadInfoVO, byteArrayOutputStream);
        } catch (CommonApplicationException e) {
            LOGGER.info("列名出错");
        }
        Assert.assertNotNull(linkedHashMaps);
    }

    @Test
    public void importExcel3Test() throws IOException, CommonApplicationException {

        List<ExcelVO> heads = new ArrayList();
        List<HeaderVo> model = new LinkedList<>();
        List<HeaderVo> MONTH_HEADER = new LinkedList<>();
        MONTH_HEADER.add(new HeaderVo("年月", "year",CellType.STRING,true, 12 * 480));
        MONTH_HEADER.add(new HeaderVo("指数", "cost",CellType.STRING,true,12 * 480));
        model.addAll(MONTH_HEADER);

        setValues(heads, model);
        UploadInfoVO uploadInfoVO = new UploadInfoVO();
        uploadInfoVO.setFileName("3434");
        uploadInfoVO.setFileSize(12);
        Map<String, Object> params = new HashMap<>();
        params.put("maxRowNum", 25000);
        uploadInfoVO.setParams(params);

        Resource resource = new ClassPathResource("excel.export.template/UTtestTemplate2.xlsx");
        InputStream inputStream = resource.getInputStream();
        ByteArrayOutputStream byteArrayOutputStream = putInputStreamCacher(inputStream);
        List<LinkedHashMap<String, Object>> linkedHashMaps = new ArrayList<>();

        linkedHashMaps = excelUtil.importExcel(attachment2, heads, uploadInfoVO, byteArrayOutputStream);

        Assert.assertNotNull(linkedHashMaps);
    }

    @Test
    public void readExcel() throws IOException {
        Attachment attachment = new Attachment("", new DataHandler(new DataSource() {
            @Override
            public InputStream getInputStream() throws IOException {
                return new InputStream() {
                    @Override
                    public int read() throws IOException {
                        return 0;
                    }
                };
            }

            @Override
            public OutputStream getOutputStream() throws IOException {
                return null;
            }

            @Override
            public String getContentType() {
                return null;
            }

            @Override
            public String getName() {
                return "test.xlsxs";
            }
        }),
                getMultivaluedMap());
        List<ExcelVO> heads = new ArrayList();
        List<HeaderVo> model = new LinkedList<>();
        model.addAll(CommonConstant.MAPPING_RELATIONAL_DIMENSION_HEADER);
        setValues(heads, model);
        UploadInfoVO uploadInfoVO = new UploadInfoVO();
        uploadInfoVO.setFileName("3434");
        uploadInfoVO.setFileSize(12);
        Map<String, Object> params = new HashMap<>();
        params.put("maxRowNum", 25000);
        uploadInfoVO.setParams(params);
        byte[] byte1 = {10, 11};
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        List<LinkedHashMap<String, Object>> linkedHashMaps = new ArrayList<>();
        try {
            linkedHashMaps = excelUtil.readExcel(attachment, heads, uploadInfoVO, outputStream);
        } catch (CommonApplicationException e) {
            LOGGER.info("只能使用XLSX OR XLS格式EXCEL的文件.");
        }
        Assert.assertNotNull(linkedHashMaps);
    }

    @org.jetbrains.annotations.NotNull
    private MultivaluedMap getMultivaluedMap() {
        return new MultivaluedMap() {
            @Override
            public int size() {
                return 0;
            }

            @Override
            public boolean isEmpty() {
                return false;
            }

            @Override
            public boolean containsKey(Object key) {
                return false;
            }

            @Override
            public boolean containsValue(Object value) {
                return false;
            }

            @Override
            public Object get(Object key) {
                return null;
            }

            @Nullable
            @Override
            public Object put(Object key, Object value) {
                return null;
            }

            @Override
            public Object remove(Object key) {
                return null;
            }

            @Override
            public void putAll(@NotNull Map m) {

            }

            @Override
            public void clear() {

            }

            @NotNull
            @Override
            public Set keySet() {
                return null;
            }

            @NotNull
            @Override
            public Collection values() {
                return null;
            }

            @NotNull
            @Override
            public Set<Entry> entrySet() {
                return new Set<Entry>() {
                    @Override
                    public int size() {
                        return 0;
                    }

                    @Override
                    public boolean isEmpty() {
                        return false;
                    }

                    @Override
                    public boolean contains(Object o) {
                        return false;
                    }

                    @NotNull
                    @Override
                    public Iterator<Entry> iterator() {
                        return new Iterator<Entry>() {
                            @Override
                            public boolean hasNext() {
                                return false;
                            }

                            @Override
                            public Entry next() {
                                return null;
                            }
                        };
                    }

                    @NotNull
                    @Override
                    public Object[] toArray() {
                        return new Object[0];
                    }

                    @NotNull
                    @Override
                    public <T> T[] toArray(@NotNull T[] a) {
                        return null;
                    }

                    @Override
                    public boolean add(Entry entry) {
                        return false;
                    }

                    @Override
                    public boolean remove(Object o) {
                        return false;
                    }

                    @Override
                    public boolean containsAll(@NotNull Collection<?> c) {
                        return false;
                    }

                    @Override
                    public boolean addAll(@NotNull Collection<? extends Entry> c) {
                        return false;
                    }

                    @Override
                    public boolean retainAll(@NotNull Collection<?> c) {
                        return false;
                    }

                    @Override
                    public boolean removeAll(@NotNull Collection<?> c) {
                        return false;
                    }

                    @Override
                    public void clear() {

                    }
                };
            }

            @Override
            public boolean equals(Object o) {
                return false;
            }

            @Override
            public int hashCode() {
                return 0;
            }

            @Override
            public void putSingle(Object o, Object o2) {

            }

            @Override
            public void add(Object o, Object o2) {

            }

            @Override
            public Object getFirst(Object o) {
                return null;
            }

            @Override
            public void addAll(Object o, Object[] objects) {

            }

            @Override
            public void addAll(Object o, List list) {

            }

            @Override
            public void addFirst(Object o, Object o2) {

            }

            @Override
            public boolean equalsIgnoreValueOrder(MultivaluedMap multivaluedMap) {
                return false;
            }
        };
    }

    @Test
    public void putInputStreamCacher() {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int len;
        InputStream inputStream = new InputStream() {
            @Override
            public int read() throws IOException {
                return -1;
            }
        };
        try {
            while ((len = inputStream.read(buffer)) > -1) {
                byteArrayOutputStream.write(buffer, 0, len);
            }
            byteArrayOutputStream.flush();
        } catch (IOException exception) {
            LOGGER.info("存储输入流异常：{}", exception.getMessage());
        }
        ByteArrayOutputStream outputStream = excelUtil.putInputStreamCacher(inputStream);
        Assert.assertNotNull(outputStream);
    }

    @Test
    public void getInputStream() {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        InputStream inputStream = excelUtil.getInputStream(byteArrayOutputStream);
        Assert.assertNotNull(inputStream);
    }

    @Test
    public void expSelTitle() throws CommonApplicationException, IOException {
        List<ExportExcelVo> exportExcelVoList = new ArrayList<>();
        ExportExcelVo exportExcelVo = new ExportExcelVo();
        exportExcelVo.setTitleRowCount(10);
        exportExcelVo.setFileName("产业指数");
        exportExcelVo.setSheetName("产业/sheet");
        exportExcelVo.setMergeCell(true);
        LeafExcelTitleVO column;
        List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
        Set<String> titles = new LinkedHashSet<>();
        for (HeaderVo header : CommonConstant.MAPPING_RELATIONAL_DIMENSION_HEADER) {
            column =
                    new LeafExcelTitleVO(
                            header.getTitle(),
                            header.getWidth(),
                            true,
                            header.getField(),
                            header.getField(),
                            header.getDataType(),
                            header.getIsEditable());
            titleVoList.add(column);
            titles.add(header.getField());
        }
        exportExcelVo.setSelectedLeafExcelTitleVO(titleVoList);
        exportExcelVo.setFormInfoVo(titleVoList);
        Map<String, String> map = new HashMap<>();
        map.put("category_code", "1155");
        map.put("category_cn_name", "元器");
        List<Map> list = new ArrayList<>();
        list.add(map);
        exportExcelVo.setList(list);
        exportExcelVoList.add(exportExcelVo);

        DmFoiImpExpRecordVO dmFoiImpExpRecordVO = excelUtil.expSelTitle(response, exportExcelVoList);
        Assert.assertNull(dmFoiImpExpRecordVO);
    }

    @Test
    public void expSelTitle2Test() throws CommonApplicationException, IOException {
        response = null;
        List<ExportExcelVo> exportExcelVoList = new ArrayList<>();
        ExportExcelVo exportExcelVo = new ExportExcelVo();
        exportExcelVo.setTitleRowCount(10);
        exportExcelVo.setFileName("产业指数");
        exportExcelVo.setSheetName("产业/sheet");
        exportExcelVo.setMergeCell(true);
        LeafExcelTitleVO column;
        List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
        Set<String> titles = new LinkedHashSet<>();
        for (HeaderVo header : CommonConstant.MAPPING_RELATIONAL_DIMENSION_HEADER) {
            column =
                    new LeafExcelTitleVO(
                            header.getTitle(),
                            header.getWidth(),
                            true,
                            header.getField(),
                            header.getField(),
                            header.getDataType(),
                            header.getIsEditable());
            titleVoList.add(column);
            titles.add(header.getField());
        }
        exportExcelVo.setSelectedLeafExcelTitleVO(titleVoList);
        exportExcelVo.setFormInfoVo(titleVoList);
        Map<String, String> map = new HashMap<>();
        map.put("category_code", "1155");
        map.put("category_cn_name", "元器");
        List<Map> list = new ArrayList<>();
        list.add(map);
        exportExcelVo.setList(list);
        exportExcelVoList.add(exportExcelVo);

        DmFoiImpExpRecordVO dmFoiImpExpRecordVO = excelUtil.expSelTitle(response, exportExcelVoList);
        Assert.assertNull(dmFoiImpExpRecordVO);
    }

    private void setValues(List<ExcelVO> list, List<HeaderVo> headerVoList) {
        for (HeaderVo header : headerVoList) {
            ExcelVO vo = new ExcelVO();
            vo.setHeadName(header.getTitle());
            vo.setHeadType(CommonConstant.VARCHAR);
            list.add(vo);
        }
    }

    private ByteArrayOutputStream putInputStreamCacher(InputStream inputStream) {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int len;
        try {
            while ((len = inputStream.read(buffer)) > -1 ) {
                byteArrayOutputStream.write(buffer, 0, len);
            }
            byteArrayOutputStream.flush();
        } catch (IOException exception) {
            LOGGER.info("存储输入流异常：{}",exception.getMessage());
        }
        return byteArrayOutputStream;
    }
}