/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.config;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * DataReviewVO Class
 *
 * <AUTHOR>
 * @since 2024/9/10
 */
@Data
@NoArgsConstructor
public class DataReviewVO implements Serializable {

    private static final long serialVersionUID = 5877161359390939921L;

    @Size(max=5000,message = "安全考虑，限制数量不能超过5000")
    private List<DmFcstIctRawDataExamineDTO> reviewList;

    private String modifyType;

    private String pageType;

    private String granularityType;

    private String costType;

    private Long versionId;

    private Long overseaFlag;

    private Long bgCode;

    private Long regionCode;

    private Long repofficeCode;

    private Long hwContractNum;

}
