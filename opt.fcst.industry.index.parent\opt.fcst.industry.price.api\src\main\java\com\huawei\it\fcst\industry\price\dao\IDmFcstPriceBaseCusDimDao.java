/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.dao;

import com.huawei.it.fcst.industry.price.vo.drop.BasePriceCusDimVO;
import com.huawei.it.fcst.industry.price.vo.drop.DmFcstBasePriceCusDimVO;

import java.util.List;

/**
 * IDmFcstBaseCusDimDao
 *
 * <AUTHOR>
 * @since 2024/11/6
 */
public interface IDmFcstPriceBaseCusDimDao {

    List<DmFcstBasePriceCusDimVO> baseCusDimStatus(BasePriceCusDimVO baseCusDimVO);

    List<DmFcstBasePriceCusDimVO> getBaseCusDimInfoList(BasePriceCusDimVO baseCusDimVO);

    int createDmFcstCusDimDTO(DmFcstBasePriceCusDimVO dmFcstBaseCusDimVO);

    Long getBaseCusDimKey();

    /**
     * 获取需要执行的任务清单
     *
     * @return 待执行任务列表
     */
    List<DmFcstBasePriceCusDimVO> getNeedTaskList();

    /**
     * 更新任务状态
     *
     * @param dmFcstBaseCusDimVO 组合对象
     * @return update 结果
     */
    int updateTaskStatus(DmFcstBasePriceCusDimVO dmFcstBaseCusDimVO);

    /**
     * 任务触发批量更新任务状态
     *
     * @param list 任务列表
     * @return 更新条数
     */
    int updateStatusFlag(List<DmFcstBasePriceCusDimVO> list);

    /**
     * 任务触发批量更新任务状态
     *
     * @return 更新条数
     */
    List<DmFcstBasePriceCusDimVO> getExceptionTaskList();
}
