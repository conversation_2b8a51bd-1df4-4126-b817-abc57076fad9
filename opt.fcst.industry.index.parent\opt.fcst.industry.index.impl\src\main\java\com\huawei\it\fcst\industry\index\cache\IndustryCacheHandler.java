/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 *
 */

package com.huawei.it.fcst.industry.index.cache;

import com.huawei.it.jalor5.core.cache.GlobalParameterEventArgs;
import com.huawei.it.jalor5.core.cache.IGlobalParameterChangedHandler;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.lookup.LookupItemVO;
import com.huawei.it.jalor5.lookup.service.ILookupItemQueryService;
import com.huawei.it.jalor5.vegahsa.client.rpc.factory.RpcClientFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/3/18
 */
@Component
public class IndustryCacheHandler implements IGlobalParameterChangedHandler {
    @Override
    public void execute(Object o, GlobalParameterEventArgs globalParameterEventArgs) throws ApplicationException {
        IndustryGlobalParameterUtil.cleanLookupValue();
        initLookupValue();
    }

    /**
     * 初始化lookup配置
     *
     * @throws ApplicationException 异常
     */
    public void initLookupValue() throws ApplicationException {
        ILookupItemQueryService rpc = RpcClientFactory.getSubAppRpcClinet(ILookupItemQueryService.class);
        final Map<String, List<LookupItemVO>> methodType = rpc.findItemListByClassifies("ALL_UNIVERSAL_VIEW,ALL_PROFITS_VIEW,ALL_DIMENSION_VIEW,MANUFACTURE_UNIVERSAL_VIEW,MANUFACTURE_PROFITS_VIEW,MANUFACTURE_DIMENSION_VIEW,UNIVERSAL_VIEW,PROFITS_VIEW,DIMENSION_VIEW,HOME_PAGE_BULLETIN_TYPE");
        if (methodType != null) {
            IndustryGlobalParameterUtil.putAllLookupValue(methodType);
        }
    }
}
