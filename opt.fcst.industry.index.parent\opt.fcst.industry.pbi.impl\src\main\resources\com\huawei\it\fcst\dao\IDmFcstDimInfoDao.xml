<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.pbi.dao.IDmFcstDimInfoDao">
    <resultMap type="com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstDimInfoVO" id="resultMap">
        <result property="id" column="id"/>
        <result property="bgCode" column="bg_code"/>
        <result property="bgCnName" column="bg_cn_name"/>
        <result property="dimensionCode" column="dimension_code"/>
        <result property="dimensionCnName" column="dimension_cn_name"/>
        <result property="dimensionSubCategoryCode" column="dimension_subCategory_code"/>
        <result property="dimensionSubCategoryCnName" column="dimension_subCategory_cn_name"/>
        <result property="dimensionSubDetailCode" column="dimension_sub_detail_code"/>
        <result property="dimensionSubDetailCnName" column="dimension_sub_detail_cn_name"/>
        <result property="spartCode" column="spart_code"/>
        <result property="spartCnName" column="spart_cn_name"/>
        <result property="spartCode" column="top_spart_code"/>
        <result property="spartCnName" column="top_spart_cn_name"/>
        <result property="regionCode" column="region_code"/>
        <result property="regionCnName" column="region_cn_name"/>
        <result property="repofficeCode" column="repoffice_code"/>
        <result property="repofficeCnName" column="repoffice_cn_name"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="groupCode" column="groupCode"/>
        <result property="groupCnName" column="groupCnName"/>
        <result property="groupLevel" column="group_level"/>
        <result property="groupCnName" column="group_cn_name"/>
        <result property="groupCode" column="group_code"/>
        <result property="num" column="num"/>
        <result property="lv0Code" column="lv0_code"/>
        <result property="lv1Code" column="lv1_code"/>
        <result property="lv1CnName" column="lv1_cn_name"/>
        <result property="lv2Code" column="lv2_code"/>
        <result property="mainFlag" column="main_flag"/>
        <result property="lv0Code" column="LV0_PROD_RND_TEAM_CODE" />
        <result property="lv0CnName" column="LV0_PROD_RD_TEAM_CN_NAME" />
        <result property="lv1Code" column="LV1_PROD_RND_TEAM_CODE" />
        <result property="lv1CnName" column="LV1_PROD_RD_TEAM_CN_NAME" />
        <result property="lv2Code" column="LV2_PROD_RND_TEAM_CODE" />
        <result property="lv2CnName" column="LV2_PROD_RD_TEAM_CN_NAME" />
        <result property="lv3Code" column="LV3_PROD_RND_TEAM_CODE" />
        <result property="lv3CnName" column="LV3_PROD_RD_TEAM_CN_NAME" />
        <result property="lv4Code" column="LV4_PROD_RND_TEAM_CODE" />
        <result property="lv4CnName" column="LV4_PROD_RD_TEAM_CN_NAME" />
        <result property="lv0Code" column="lv0_industry_catg_code" />
        <result property="lv0CnName" column="lv0_industry_catg_cn_name" />
        <result property="lv1Code" column="lv1_industry_catg_code" />
        <result property="lv1CnName" column="lv1_industry_catg_cn_name" />
        <result property="lv2Code" column="lv2_industry_catg_code" />
        <result property="lv2CnName" column="lv2_industry_catg_cn_name" />
        <result property="lv3Code" column="lv3_industry_catg_code" />
        <result property="lv3CnName" column="lv3_industry_catg_cn_name" />
        <result property="lv4Code" column="lv4_industry_catg_code" />
        <result property="lv4CnName" column="lv4_industry_catg_cn_name" />
        <result property="lv0Code" column="lv0_prod_list_code" />
        <result property="lv0CnName" column="lv0_prod_list_cn_name" />
        <result property="lv1Code" column="lv1_prod_list_code" />
        <result property="lv1CnName" column="lv1_prod_list_cn_name" />
        <result property="lv2Code" column="lv2_prod_list_code" />
        <result property="lv2CnName" column="lv2_prod_list_cn_name" />
        <result property="lv3Code" column="lv3_prod_list_code" />
        <result property="lv3CnName" column="lv3_prod_list_cn_name" />
        <result property="lv4Code" column="lv4_prod_list_code" />
        <result property="lv4CnName" column="lv4_prod_list_cn_name" />
        <result property="connectCode" column="connectCode" />
        <result property="connectParentCode" column="connectParentCode" />
    </resultMap>

    <sql id="topIrbSpartInnerJoin">
        ON t2.del_flag = 'N'
        AND t2.main_flag = 'Y'
        AND t2.is_top_flag = 'Y'
        AND t2.double_flag = 'Y'
        <if test='versionId != null'>
            AND t2.version_id = #{monVersionId,jdbcType=NUMERIC}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and t2.region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and t2.repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='softwareMark != null and softwareMark != ""'>
            and t2.software_mark =#{softwareMark,jdbcType=VARCHAR}
        </if>
        AND t2.bg_code = t1.bg_code
        AND t2.code_attributes = t1.code_attributes
        and t2.lv1_prod_rnd_team_code = t1.lv1_prod_rnd_team_code
        and t2.lv2_prod_rnd_team_code = t1.lv2_prod_rnd_team_code
        and t2.lv3_prod_rnd_team_code = t1.lv3_prod_rnd_team_code
        and t2.lv4_prod_rnd_team_code = t1.lv4_prod_rnd_team_code
        AND t2.top_spart_code = t1.spart_code
    </sql>

    <sql id="topIndusSpartInnerJoin">
        ON t2.del_flag = 'N'
        AND t2.main_flag = 'Y'
        AND t2.is_top_flag = 'Y'
        AND t2.double_flag = 'Y'
        <if test='versionId != null'>
            AND t2.version_id = #{monVersionId,jdbcType=NUMERIC}
        </if>
        AND t2.bg_code = t1.bg_code
        AND t2.code_attributes = t1.code_attributes
        AND t2.lv1_industry_catg_code = t1.lv1_industry_catg_code
        AND t2.lv2_industry_catg_code = t1.lv2_industry_catg_code
        AND t2.lv3_industry_catg_code = t1.lv3_industry_catg_code
        AND t2.lv4_industry_catg_code = t1.lv4_industry_catg_code
        AND t2.top_spart_code = t1.spart_code
    </sql>

    <sql id="topProdSpartInnerJoin">
        ON t2.del_flag = 'N'
        AND t2.main_flag = 'Y'
        AND t2.is_top_flag = 'Y'
        AND t2.double_flag = 'Y'
        <if test='versionId != null'>
            AND t2.version_id = #{monVersionId,jdbcType=NUMERIC}
        </if>
        AND t2.bg_code = t1.bg_code
        AND t2.code_attributes = t1.code_attributes
        AND t2.lv1_prod_list_code = t1.lv1_prod_list_code
        AND t2.lv2_prod_list_code = t1.lv2_prod_list_code
        AND t2.lv3_prod_list_code = t1.lv3_prod_list_code
        AND t2.lv4_prod_list_code = t1.lv4_prod_list_code
        AND t2.top_spart_code = t1.spart_code
    </sql>

    <sql id="aunualIrbSpartLevel">
        lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
        lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,
        lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,
        lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,
        lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,
        spart_code AS group_code,spart_cn_name AS group_cn_name,'SPART' AS group_level,
        concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
        DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
        DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' || LV3_PROD_RND_TEAM_CODE),
        DECODE(LV4_PROD_RND_TEAM_CODE,'','','#*#' || LV4_PROD_RND_TEAM_CODE),
        DECODE(spart_code,'','','#*#' || spart_code)
        ) as connectCode,
        concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
        DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
        DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' || LV3_PROD_RND_TEAM_CODE),
        DECODE(LV4_PROD_RND_TEAM_CODE,'','','#*#' || LV4_PROD_RND_TEAM_CODE)
        ) AS connectParentCode
    </sql>

    <sql id="lv4IrbLevel">
        lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
        lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,
        lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,
        lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,
        lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,
        lv4_prod_rnd_team_code AS group_code,lv4_prod_rd_team_cn_name AS group_cn_name,'LV4' AS group_level,
        concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
        DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
        DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' || LV3_PROD_RND_TEAM_CODE),
        DECODE(LV4_PROD_RND_TEAM_CODE,'','','#*#' || LV4_PROD_RND_TEAM_CODE)
        ) as connectCode,
        concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
        DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
        DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' || LV3_PROD_RND_TEAM_CODE)
        ) as connectParentCode
    </sql>

    <sql id="lv3IrbLevel">
        lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
        lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,
        lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,
        lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,
        lv3_prod_rnd_team_code AS group_code,lv3_prod_rd_team_cn_name AS group_cn_name,'LV3' AS group_level,
        concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
        DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
        DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' || LV3_PROD_RND_TEAM_CODE)
        ) as connectCode,
        concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
        DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE)
        ) as connectParentCode
    </sql>

    <sql id='lv2IrbLevel'>
        lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
        lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,
        lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,
        lv2_prod_rnd_team_code AS group_code,lv2_prod_rd_team_cn_name AS group_cn_name,'LV2' AS group_level,
        concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
        DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
        DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE)
        ) as connectCode,
        concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
        DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE)
        ) as connectParentCode
    </sql>

    <sql id='lv1IrbHasPermission'>
        lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
        lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,
        lv1_prod_rnd_team_code AS group_code,lv1_prod_rd_team_cn_name AS group_cn_name,'LV1' AS group_level,
        concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
        DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE)
        ) as connectCode,
        concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE)
        ) as connectParentCode
    </sql>

    <sql id='lv1IrbNotPermission'>
        lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,LV1_PROD_RND_TEAM_CODE,lv1_prod_rd_team_cn_name,
        LV1_PROD_RND_TEAM_CODE AS group_code,lv1_prod_rd_team_cn_name AS group_cn_name,'LV1' AS group_level,
        concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
        DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE)
        ) as connectCode
    </sql>

    <sql id='lv0IrbLevel'>
        lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
        lv0_prod_rnd_team_code AS group_code,lv0_prod_rd_team_cn_name AS group_cn_name,'LV0' AS group_level,
        concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE)
        ) as connectCode
    </sql>

    <sql id='annualIndusSpartLevel'>
        lv0_industry_catg_code,lv0_industry_catg_cn_name,
        lv1_industry_catg_code,lv1_industry_catg_cn_name,
        lv2_industry_catg_code,lv2_industry_catg_cn_name,
        lv3_industry_catg_code,lv3_industry_catg_cn_name,
        lv4_industry_catg_code,lv4_industry_catg_cn_name,
        spart_code AS group_code,spart_cn_name AS group_cn_name,'SPART' AS group_level,
        concat(DECODE(lv0_industry_catg_code,'','',lv0_industry_catg_code),
        DECODE(lv1_industry_catg_code,'','','#*#' || lv1_industry_catg_code),
        DECODE(lv2_industry_catg_code,'','','#*#' || lv2_industry_catg_code),
        DECODE(lv3_industry_catg_code,'','','#*#' || lv3_industry_catg_code),
        DECODE(lv4_industry_catg_code,'','','#*#' || lv4_industry_catg_code),
        DECODE(spart_code,'','','#*#' || spart_code)
        ) as connectCode,
        concat(DECODE(lv0_industry_catg_code,'','',lv0_industry_catg_code),
        DECODE(lv1_industry_catg_code,'','','#*#' || lv1_industry_catg_code),
        DECODE(lv2_industry_catg_code,'','','#*#' || lv2_industry_catg_code),
        DECODE(lv3_industry_catg_code,'','','#*#' || lv3_industry_catg_code),
        DECODE(lv4_industry_catg_code,'','','#*#' || lv4_industry_catg_code)
        ) AS connectParentCode
    </sql>

    <sql id='lv4IndusLevel'>
        lv0_industry_catg_code,lv0_industry_catg_cn_name,
        lv1_industry_catg_code,lv1_industry_catg_cn_name,
        lv2_industry_catg_code,lv2_industry_catg_cn_name,
        lv3_industry_catg_code,lv3_industry_catg_cn_name,
        lv4_industry_catg_code,lv4_industry_catg_cn_name,
        lv4_industry_catg_code AS group_code,lv4_industry_catg_cn_name AS group_cn_name,'LV4' AS
        group_level,
        concat(DECODE(lv0_industry_catg_code,'','',lv0_industry_catg_code),
        DECODE(lv1_industry_catg_code,'','','#*#' || lv1_industry_catg_code),
        DECODE(lv2_industry_catg_code,'','','#*#' || lv2_industry_catg_code),
        DECODE(lv3_industry_catg_code,'','','#*#' || lv3_industry_catg_code),
        DECODE(lv4_industry_catg_code,'','','#*#' || lv4_industry_catg_code)
        ) as connectCode,
        concat(DECODE(lv0_industry_catg_code,'','',lv0_industry_catg_code),
        DECODE(lv1_industry_catg_code,'','','#*#' || lv1_industry_catg_code),
        DECODE(lv2_industry_catg_code,'','','#*#' || lv2_industry_catg_code),
        DECODE(lv3_industry_catg_code,'','','#*#' || lv3_industry_catg_code)
        ) as connectParentCode
    </sql>

    <sql id='lv3IndusLevel'>
        lv0_industry_catg_code,lv0_industry_catg_cn_name,
        lv1_industry_catg_code,lv1_industry_catg_cn_name,
        lv2_industry_catg_code,lv2_industry_catg_cn_name,
        lv3_industry_catg_code,lv3_industry_catg_cn_name,
        lv3_industry_catg_code AS group_code,lv3_industry_catg_cn_name AS group_cn_name,'LV3' AS
        group_level,
        concat(DECODE(lv0_industry_catg_code,'','',lv0_industry_catg_code),
        DECODE(lv1_industry_catg_code,'','','#*#' || lv1_industry_catg_code),
        DECODE(lv2_industry_catg_code,'','','#*#' || lv2_industry_catg_code),
        DECODE(lv3_industry_catg_code,'','','#*#' || lv3_industry_catg_code)
        ) as connectCode,
        concat(DECODE(lv0_industry_catg_code,'','',lv0_industry_catg_code),
        DECODE(lv1_industry_catg_code,'','','#*#' || lv1_industry_catg_code),
        DECODE(lv2_industry_catg_code,'','','#*#' || lv2_industry_catg_code)
        ) as connectParentCode
    </sql>

    <sql id='lv2IndusLevel'>
        lv0_industry_catg_code,lv0_industry_catg_cn_name,
        lv1_industry_catg_code,lv1_industry_catg_cn_name,
        lv2_industry_catg_code,lv2_industry_catg_cn_name,
        lv2_industry_catg_code AS group_code,lv2_industry_catg_cn_name AS group_cn_name,'LV2' AS
        group_level,
        concat(DECODE(lv0_industry_catg_code,'','',lv0_industry_catg_code),
        DECODE(lv1_industry_catg_code,'','','#*#' || lv1_industry_catg_code),
        DECODE(lv2_industry_catg_code,'','','#*#' || lv2_industry_catg_code)
        ) as connectCode,
        concat(DECODE(lv0_industry_catg_code,'','',lv0_industry_catg_code),
        DECODE(lv1_industry_catg_code,'','','#*#' || lv1_industry_catg_code)
        ) as connectParentCode
    </sql>

    <sql id='lv1IndusLevelHasPermission'>
        lv0_industry_catg_code,lv0_industry_catg_cn_name,
        lv1_industry_catg_code,lv1_industry_catg_cn_name,
        lv1_industry_catg_code AS group_code,lv1_industry_catg_cn_name AS group_cn_name,'LV1' AS
        group_level,
        concat(DECODE(lv0_industry_catg_code,'','',lv0_industry_catg_code),
        DECODE(lv1_industry_catg_code,'','','#*#' || lv1_industry_catg_code)
        ) as connectCode,
        concat(DECODE(lv0_industry_catg_code,'','',lv0_industry_catg_code)
        ) as connectParentCode
    </sql>

    <sql id='lv1IndusLevelNotPermission'>
        lv0_industry_catg_code,lv0_industry_catg_cn_name,
        lv1_industry_catg_code,lv1_industry_catg_cn_name,
        lv1_industry_catg_code AS group_code,lv1_industry_catg_cn_name AS group_cn_name,'LV1' AS
        group_level,
        concat(DECODE(lv0_industry_catg_code,'','',lv0_industry_catg_code),
        DECODE(lv1_industry_catg_code,'','','#*#' || lv1_industry_catg_code)
        ) as connectCode
    </sql>

    <sql id='lv0IndusLevel'>
        lv0_industry_catg_code,lv0_industry_catg_cn_name,
        lv0_industry_catg_code AS group_code,lv0_industry_catg_cn_name AS group_cn_name,'LV0' AS
        group_level,
        concat(DECODE(lv0_industry_catg_code,'','',lv0_industry_catg_code)
        ) as connectCode
    </sql>

    <sql id='annualProdSpartLevel'>
        lv0_prod_list_code,lv0_prod_list_cn_name,
        lv1_prod_list_code,lv1_prod_list_cn_name,
        lv2_prod_list_code,lv2_prod_list_cn_name,
        lv3_prod_list_code,lv3_prod_list_cn_name,
        lv4_prod_list_code,lv4_prod_list_cn_name,
        spart_code, spart_cn_name,
        spart_code AS group_code,spart_cn_name AS group_cn_name,'SPART' AS group_level,
        concat(DECODE(lv0_prod_list_code,'','',lv0_prod_list_code),
        DECODE(lv1_prod_list_code,'','','#*#' || lv1_prod_list_code),
        DECODE(lv2_prod_list_code,'','','#*#' || lv2_prod_list_code),
        DECODE(lv3_prod_list_code,'','','#*#' || lv3_prod_list_code),
        DECODE(lv4_prod_list_code,'','','#*#' || lv4_prod_list_code),
        DECODE(spart_code,'','','#*#' || spart_code)
        ) as connectCode,
        concat(DECODE(lv0_prod_list_code,'','',lv0_prod_list_code),
        DECODE(lv1_prod_list_code,'','','#*#' || lv1_prod_list_code),
        DECODE(lv2_prod_list_code,'','','#*#' || lv2_prod_list_code),
        DECODE(lv3_prod_list_code,'','','#*#' || lv3_prod_list_code),
        DECODE(lv4_prod_list_code,'','','#*#' || lv4_prod_list_code)
        ) AS connectParentCode
    </sql>

    <sql id='lv4ProdLevel'>
        lv0_prod_list_code,lv0_prod_list_cn_name,
        lv1_prod_list_code,lv1_prod_list_cn_name,
        lv2_prod_list_code,lv2_prod_list_cn_name,
        lv3_prod_list_code,lv3_prod_list_cn_name,
        lv4_prod_list_code,lv4_prod_list_cn_name,
        lv4_prod_list_code AS group_code,lv4_prod_list_cn_name AS group_cn_name,'LV4' AS
        group_level,
        concat(DECODE(lv0_prod_list_code,'','',lv0_prod_list_code),
        DECODE(lv1_prod_list_code,'','','#*#' || lv1_prod_list_code),
        DECODE(lv2_prod_list_code,'','','#*#' || lv2_prod_list_code),
        DECODE(lv3_prod_list_code,'','','#*#' || lv3_prod_list_code),
        DECODE(lv4_prod_list_code,'','','#*#' || lv4_prod_list_code)
        ) as connectCode,
        concat(DECODE(lv0_prod_list_code,'','',lv0_prod_list_code),
        DECODE(lv1_prod_list_code,'','','#*#' || lv1_prod_list_code),
        DECODE(lv2_prod_list_code,'','','#*#' || lv2_prod_list_code),
        DECODE(lv3_prod_list_code,'','','#*#' || lv3_prod_list_code)
        ) as connectParentCode
    </sql>

    <sql id='lv3ProdLevel'>
        lv0_prod_list_code,lv0_prod_list_cn_name,
        lv1_prod_list_code,lv1_prod_list_cn_name,
        lv2_prod_list_code,lv2_prod_list_cn_name,
        lv3_prod_list_code,lv3_prod_list_cn_name,
        lv3_prod_list_code AS group_code,lv3_prod_list_cn_name AS group_cn_name,'LV3' AS
        group_level,
        concat(DECODE(lv0_prod_list_code,'','',lv0_prod_list_code),
        DECODE(lv1_prod_list_code,'','','#*#' || lv1_prod_list_code),
        DECODE(lv2_prod_list_code,'','','#*#' || lv2_prod_list_code),
        DECODE(lv3_prod_list_code,'','','#*#' || lv3_prod_list_code)
        ) as connectCode,
        concat(DECODE(lv0_prod_list_code,'','',lv0_prod_list_code),
        DECODE(lv1_prod_list_code,'','','#*#' || lv1_prod_list_code),
        DECODE(lv2_prod_list_code,'','','#*#' || lv2_prod_list_code)
        ) as connectParentCode
    </sql>

    <sql id='lv2ProdLevel'>
        lv0_prod_list_code,lv0_prod_list_cn_name,
        lv1_prod_list_code,lv1_prod_list_cn_name,
        lv2_prod_list_code,lv2_prod_list_cn_name,
        lv2_prod_list_code AS group_code,lv2_prod_list_cn_name AS group_cn_name,'LV2' AS
        group_level,
        concat(DECODE(lv0_prod_list_code,'','',lv0_prod_list_code),
        DECODE(lv1_prod_list_code,'','','#*#' || lv1_prod_list_code),
        DECODE(lv2_prod_list_code,'','','#*#' || lv2_prod_list_code)
        ) as connectCode,
        concat(DECODE(lv0_prod_list_code,'','',lv0_prod_list_code),
        DECODE(lv1_prod_list_code,'','','#*#' || lv1_prod_list_code)
        ) as connectParentCode
    </sql>

    <sql id='lv1ProdLevelHasPermission'>
        lv0_prod_list_code,lv0_prod_list_cn_name,
        lv1_prod_list_code,lv1_prod_list_cn_name,
        lv1_prod_list_code AS group_code,lv1_prod_list_cn_name AS group_cn_name,'LV1' AS
        group_level,
        concat(DECODE(lv0_prod_list_code,'','',lv0_prod_list_code),
        DECODE(lv1_prod_list_code,'','','#*#' || lv1_prod_list_code)
        ) as connectCode,
        concat(DECODE(lv0_prod_list_code,'','',lv0_prod_list_code)
        ) as connectParentCode
    </sql>

    <sql id='lv1ProdLevelNotPermission'>
        lv0_prod_list_code,lv0_prod_list_cn_name,
        lv1_prod_list_code,lv1_prod_list_cn_name,
        lv1_prod_list_code AS group_code,lv1_prod_list_cn_name AS group_cn_name,'LV1' AS group_level,
        concat(DECODE(lv0_prod_list_code,'','',lv0_prod_list_code),
        DECODE(lv1_prod_list_code,'','','#*#' || lv1_prod_list_code)
        ) as connectCode
    </sql>

    <sql id='lv0ProdLevel'>
        lv0_prod_list_code,lv0_prod_list_cn_name,
        lv0_prod_list_code AS group_code,lv0_prod_list_cn_name AS group_cn_name,'LV0' AS group_level,
        concat(DECODE(lv0_prod_list_code,'','',lv0_prod_list_code)
        ) as connectCode
    </sql>

    <sql id="irbMainLv3Level">
        t2.lv0_prod_rnd_team_code,t2.lv0_prod_rd_team_cn_name,
        t1.lv1_prod_rnd_team_code,t1.lv1_prod_rd_team_cn_name, t1.lv2_prod_rnd_team_code,t1.lv2_prod_rd_team_cn_name,
        t1.lv3_prod_rnd_team_code,t1.lv3_prod_rd_team_cn_name,
        t1.lv3_prod_rnd_team_code AS group_code,t1.lv3_prod_rd_team_cn_name AS group_cn_name,'LV3' AS group_level,
        concat(t2.lv0_prod_rnd_team_code,'#*#',t1.lv1_prod_rnd_team_code,'#*#',t1.lv2_prod_rnd_team_code,'#*#',t1.lv3_prod_rnd_team_code) as connectCode,
        concat(t2.lv0_prod_rnd_team_code,'#*#',t1.lv1_prod_rnd_team_code,'#*#',t1.lv2_prod_rnd_team_code) AS connectParentCode
    </sql>

    <sql id="irbMainSpartLevel">
        t2.lv0_prod_rnd_team_code,t2.lv0_prod_rd_team_cn_name,
        t1.lv1_prod_rnd_team_code,t1.lv1_prod_rd_team_cn_name,
        t1.lv2_prod_rnd_team_code,t1.lv2_prod_rd_team_cn_name,
        t1.lv3_prod_rnd_team_code,t1.lv3_prod_rd_team_cn_name,
        t1.lv4_prod_rnd_team_code,t1.lv4_prod_rd_team_cn_name,
        t1.spart_code AS group_code,t1.spart_cn_name AS group_cn_name,'SPART' AS group_level,
        concat(DECODE(t2.lv0_prod_rnd_team_code,'','',t2.lv0_prod_rnd_team_code),
        DECODE(t1.LV1_PROD_RND_TEAM_CODE,'','','#*#' || t1.LV1_PROD_RND_TEAM_CODE),
        DECODE(t1.LV2_PROD_RND_TEAM_CODE,'','','#*#' || t1.LV2_PROD_RND_TEAM_CODE),
        DECODE(t1.LV3_PROD_RND_TEAM_CODE,'','','#*#' || t1.LV3_PROD_RND_TEAM_CODE),
        DECODE(t1.LV4_PROD_RND_TEAM_CODE,'','','#*#' || t1.LV4_PROD_RND_TEAM_CODE),
        DECODE(t1.spart_code,'','','#*#' || t1.spart_code)
        ) as connectCode,
        concat(DECODE(t2.lv0_prod_rnd_team_code,'','',t2.lv0_prod_rnd_team_code),
        DECODE(t1.LV1_PROD_RND_TEAM_CODE,'','','#*#' || t1.LV1_PROD_RND_TEAM_CODE),
        DECODE(t1.LV2_PROD_RND_TEAM_CODE,'','','#*#' || t1.LV2_PROD_RND_TEAM_CODE),
        DECODE(t1.LV3_PROD_RND_TEAM_CODE,'','','#*#' || t1.LV3_PROD_RND_TEAM_CODE),
        DECODE(t1.LV4_PROD_RND_TEAM_CODE,'','','#*#' || t1.LV4_PROD_RND_TEAM_CODE)
        ) AS connectParentCode
    </sql>

    <sql id='irbMainLV4Level'>
        t2.lv0_prod_rnd_team_code, t2.lv0_prod_rd_team_cn_name,
        t1.lv1_prod_rnd_team_code, t1.lv1_prod_rd_team_cn_name,
        t1.lv2_prod_rnd_team_code, t1.lv2_prod_rd_team_cn_name,
        t1.lv3_prod_rnd_team_code, t1.lv3_prod_rd_team_cn_name,
        t1.lv4_prod_rnd_team_code, t1.lv4_prod_rd_team_cn_name,
        t1.lv4_prod_rnd_team_code AS group_code,t1.lv4_prod_rd_team_cn_name AS group_cn_name,'LV4' AS group_level,
        concat(DECODE(t2.lv0_prod_rnd_team_code,'','',t2.lv0_prod_rnd_team_code),
        DECODE(t1.LV1_PROD_RND_TEAM_CODE,'','','#*#' || t1.LV1_PROD_RND_TEAM_CODE),
        DECODE(t1.LV2_PROD_RND_TEAM_CODE,'','','#*#' || t1.LV2_PROD_RND_TEAM_CODE),
        DECODE(t1.LV3_PROD_RND_TEAM_CODE,'','','#*#' || t1.LV3_PROD_RND_TEAM_CODE),
        DECODE(t1.LV4_PROD_RND_TEAM_CODE,'','','#*#' || t1.LV4_PROD_RND_TEAM_CODE)
        ) as connectCode,
        concat(DECODE(t2.lv0_prod_rnd_team_code,'','',t2.lv0_prod_rnd_team_code),
        DECODE(t1.LV1_PROD_RND_TEAM_CODE,'','','#*#' || t1.LV1_PROD_RND_TEAM_CODE),
        DECODE(t1.LV2_PROD_RND_TEAM_CODE,'','','#*#' || t1.LV2_PROD_RND_TEAM_CODE),
        DECODE(t1.LV3_PROD_RND_TEAM_CODE,'','','#*#' || t1.LV3_PROD_RND_TEAM_CODE)
        ) as connectParentCode
    </sql>

    <sql id="irbMainLv2Level">
        t2.lv0_prod_rnd_team_code,t2.lv0_prod_rd_team_cn_name,
        t1.lv1_prod_rnd_team_code,t1.lv1_prod_rd_team_cn_name, t1.lv2_prod_rnd_team_code,t1.lv2_prod_rd_team_cn_name,
        t1.lv2_prod_rnd_team_code AS group_code,t1.lv2_prod_rd_team_cn_name AS group_cn_name,'LV2' AS group_level,
        concat(t2.lv0_prod_rnd_team_code,'#*#',t1.lv1_prod_rnd_team_code,'#*#',t1.lv2_prod_rnd_team_code) as connectCode,
        concat(t2.lv0_prod_rnd_team_code,'#*#',t1.lv1_prod_rnd_team_code) AS connectParentCode
    </sql>

    <sql id="irbMainLv1HasPermission">
        t2.lv0_prod_rnd_team_code,t2.lv0_prod_rd_team_cn_name,
        t1.lv1_prod_rnd_team_code,t1.lv1_prod_rd_team_cn_name,
        t1.lv1_prod_rnd_team_code AS group_code,t1.lv1_prod_rd_team_cn_name AS group_cn_name,'LV1' AS group_level,
        concat(t2.lv0_prod_rnd_team_code,'#*#',t1.lv1_prod_rnd_team_code) as connectCode,
        concat(t2.lv0_prod_rnd_team_code) AS connectParentCode
    </sql>

    <sql id="irbMainLv1NotPermission">
        t2.lv0_prod_rnd_team_code,t2.lv0_prod_rd_team_cn_name,
        t1.lv1_prod_rnd_team_code,t1.lv1_prod_rd_team_cn_name,
        t1.lv1_prod_rnd_team_code AS group_code,t1.lv1_prod_rd_team_cn_name AS group_cn_name,'LV1' AS group_level,
        concat(t2.lv0_prod_rnd_team_code,'#*#',t1.lv1_prod_rnd_team_code) as connectCode
    </sql>

    <sql id="irbMainLv0Level">
        t2.lv0_prod_rnd_team_code,t2.lv0_prod_rd_team_cn_name,
        t2.lv0_prod_rnd_team_code AS group_code,t2.lv0_prod_rd_team_cn_name AS group_cn_name,'LV0' AS group_level,
        concat(t2.lv0_prod_rnd_team_code) as connectCode
    </sql>

    <sql id="indusMainSpartLevel">
        t2.lv0_industry_catg_code,t2.lv0_industry_catg_cn_name,
        t1.lv1_industry_catg_code,t1.lv1_industry_catg_cn_name,
        t1.lv2_industry_catg_code,t1.lv2_industry_catg_cn_name,
        t1.lv3_industry_catg_code,t1.lv3_industry_catg_cn_name,
        t1.lv4_industry_catg_code,t1.lv4_industry_catg_cn_name,
        t1.spart_code AS group_code,t1.spart_cn_name AS group_cn_name,'SPART' AS group_level,
        concat(DECODE(t2.lv0_industry_catg_code,'','',t2.lv0_industry_catg_code),
        DECODE(t1.lv1_industry_catg_code,'','','#*#' || t1.lv1_industry_catg_code),
        DECODE(t1.lv2_industry_catg_code,'','','#*#' || t1.lv2_industry_catg_code),
        DECODE(t1.lv3_industry_catg_code,'','','#*#' || t1.lv3_industry_catg_code),
        DECODE(t1.lv4_industry_catg_code,'','','#*#' || t1.lv4_industry_catg_code),
        DECODE(t1.spart_code,'','','#*#' || t1.spart_code)
        ) as connectCode,
        concat(DECODE(t2.lv0_industry_catg_code,'','',t2.lv0_industry_catg_code),
        DECODE(t1.lv1_industry_catg_code,'','','#*#' || t1.lv1_industry_catg_code),
        DECODE(t1.lv2_industry_catg_code,'','','#*#' || t1.lv2_industry_catg_code),
        DECODE(t1.lv3_industry_catg_code,'','','#*#' || t1.lv3_industry_catg_code),
        DECODE(t1.lv4_industry_catg_code,'','','#*#' || t1.lv4_industry_catg_code)
        ) AS connectParentCode
    </sql>

    <sql id="indusMainLv4Level">
        t2.lv0_industry_catg_code,t2.lv0_industry_catg_cn_name,
        t1.lv1_industry_catg_code,t1.lv1_industry_catg_cn_name,
        t1.lv2_industry_catg_code,t1.lv2_industry_catg_cn_name,
        t1.lv3_industry_catg_code,t1.lv3_industry_catg_cn_name,
        t1.lv4_industry_catg_code,t1.lv4_industry_catg_cn_name,
        t1.lv4_industry_catg_code AS group_code,t1.lv4_industry_catg_cn_name AS group_cn_name,'LV4' AS group_level,
        concat(DECODE(t2.lv0_industry_catg_code,'','',t2.lv0_industry_catg_codee),
        DECODE(t1.lv1_industry_catg_code,'','','#*#' || t1.lv1_industry_catg_code),
        DECODE(t1.lv2_industry_catg_code,'','','#*#' || t1.lv2_industry_catg_code),
        DECODE(t1.lv3_industry_catg_code,'','','#*#' || t1.lv3_industry_catg_code),
        DECODE(t1.lv4_industry_catg_code,'','','#*#' || t1.lv4_industry_catg_code)
        ) as connectCode,
        concat(DECODE(t2.lv0_industry_catg_code,'','',t2.lv0_industry_catg_code),
        DECODE(t1.lv1_industry_catg_code,'','','#*#' || t1.lv1_industry_catg_code),
        DECODE(t1.lv2_industry_catg_code,'','','#*#' || t1.lv2_industry_catg_code),
        DECODE(t1.lv3_industry_catg_code,'','','#*#' || t1.lv3_industry_catg_code)
        ) AS connectParentCode
    </sql>

    <sql id="indusMainLv3Level">
        t2.lv0_industry_catg_code,t2.lv0_industry_catg_cn_name,
        t1.lv1_industry_catg_code,t1.lv1_industry_catg_cn_name,
        t1.lv2_industry_catg_code,t1.lv2_industry_catg_cn_name,
        t1.lv3_industry_catg_code,t1.lv3_industry_catg_cn_name,
        t1.lv3_industry_catg_code AS group_code,t1.lv3_industry_catg_cn_name AS group_cn_name,'LV3' AS group_level,
        concat(DECODE(t2.lv0_industry_catg_code,'','',t2.lv0_industry_catg_code),
        DECODE(t1.lv1_industry_catg_code,'','','#*#' || t1.lv1_industry_catg_code),
        DECODE(t1.lv2_industry_catg_code,'','','#*#' || t1.lv2_industry_catg_code),
        DECODE(t1.lv3_industry_catg_code,'','','#*#' || t1.lv3_industry_catg_code)
        ) as connectCode,
        concat(DECODE(t2.lv0_industry_catg_code,'','',t2.lv0_industry_catg_code),
        DECODE(t1.lv1_industry_catg_code,'','','#*#' || t1.lv1_industry_catg_code),
        DECODE(t1.lv2_industry_catg_code,'','','#*#' || t1.lv2_industry_catg_code)
        ) as connectParentCode
    </sql>

    <sql id="indusMainLv2Level">
        t2.lv0_industry_catg_code,t2.lv0_industry_catg_cn_name,
        t1.lv1_industry_catg_code,t1.lv1_industry_catg_cn_name,
        t1.lv2_industry_catg_code,t1.lv2_industry_catg_cn_name,
        t1.lv2_industry_catg_code AS group_code,t1.lv2_industry_catg_cn_name AS group_cn_name,'LV2' AS group_level,
        concat(DECODE(t2.lv0_industry_catg_code,'','',t2.lv0_industry_catg_code),
        DECODE(t1.lv1_industry_catg_code,'','','#*#' || t1.lv1_industry_catg_code),
        DECODE(t1.lv2_industry_catg_code,'','','#*#' || t1.lv2_industry_catg_code)
        ) as connectCode,
        concat(DECODE(t2.lv0_industry_catg_code,'','',t2.lv0_industry_catg_code),
        DECODE(t1.lv1_industry_catg_code,'','','#*#' || t1.lv1_industry_catg_code)
        ) as connectParentCode
    </sql>

    <sql id="indusMainLv1HasPermission">
        t2.lv0_industry_catg_code,t2.lv0_industry_catg_cn_name,
        t1.lv1_industry_catg_code,t1.lv1_industry_catg_cn_name,
        t1.lv1_industry_catg_code AS group_code,t1.lv1_industry_catg_cn_name AS group_cn_name,'LV1' AS
        group_level,
        concat(DECODE(t2.lv0_industry_catg_code,'','',t2.lv0_industry_catg_code),
        DECODE(t1.lv1_industry_catg_code,'','','#*#' || t1.lv1_industry_catg_code)
        ) as connectCode,
        concat(DECODE(t2.lv0_industry_catg_code,'','',t2.lv0_industry_catg_code)
        ) as connectParentCode
    </sql>

    <sql id="indusMainLv1NotPermission">
        t2.lv0_industry_catg_code,t2.lv0_industry_catg_cn_name,
        t1.lv1_industry_catg_code,t1.lv1_industry_catg_cn_name,
        t1.lv1_industry_catg_code AS group_code,t1.lv1_industry_catg_cn_name AS group_cn_name,'LV1' AS
        group_level
        concat(DECODE(t2.lv0_industry_catg_code,'','',t2.lv0_industry_catg_code),
        DECODE(t1.lv1_industry_catg_code,'','','#*#' || t1.lv1_industry_catg_code)
        ) as connectCode
    </sql>

    <sql id="indusMainLv0Level">
        t2.lv0_industry_catg_code,t2.lv0_industry_catg_cn_name,
        t2.lv0_industry_catg_code AS group_code,t2.lv0_industry_catg_cn_name AS group_cn_name,'LV0' AS
        group_level,
        concat(DECODE(t2.lv0_industry_catg_code,'','',t2.lv0_industry_catg_code)
        ) as connectCode
    </sql>

    <sql id='spartMainProdLevel'>
        t2.lv0_prod_list_code, t2.lv0_prod_list_cn_name,
        t1.lv1_prod_list_code,t1.lv1_prod_list_cn_name,
        t1.lv2_prod_list_code,t1.lv2_prod_list_cn_name,
        t1.lv3_prod_list_code,t1.lv3_prod_list_cn_name,
        t1.lv4_prod_list_code,t1.lv4_prod_list_cn_name,
        t1.spart_code AS group_code,t1.spart_cn_name AS group_cn_name,'SPART' AS group_level,
        concat(DECODE(t2.lv0_prod_list_code,'','',t2.lv0_prod_list_code),
        DECODE(t1.lv1_prod_list_code,'','','#*#' || t1.lv1_prod_list_code),
        DECODE(t1.lv2_prod_list_code,'','','#*#' || t1.lv2_prod_list_code),
        DECODE(t1.lv3_prod_list_code,'','','#*#' || t1.lv3_prod_list_code),
        DECODE(t1.lv4_prod_list_code,'','','#*#' || t1.lv4_prod_list_code),
        DECODE(t1.spart_code,'','','#*#' || t1.spart_code)
        ) as connectCode,
        concat(DECODE(t2.lv0_prod_list_code,'','',t2.lv0_prod_list_code),
        DECODE(t1.lv1_prod_list_code,'','','#*#' || t1.lv1_prod_list_code),
        DECODE(t1.lv2_prod_list_code,'','','#*#' || t1.lv2_prod_list_code),
        DECODE(t1.lv3_prod_list_code,'','','#*#' || t1.lv3_prod_list_code),
        DECODE(t1.lv4_prod_list_code,'','','#*#' || t1.lv4_prod_list_code)
        ) AS connectParentCode
    </sql>

    <sql id='lv4MainProdLevel'>
        t2.lv0_prod_list_code ,t2.lv0_prod_list_cn_name,
        t1.lv1_prod_list_code,t1.lv1_prod_list_cn_name,
        t1.lv2_prod_list_code,t1.lv2_prod_list_cn_name,
        t1.lv3_prod_list_code,t1.lv3_prod_list_cn_name,
        t1.lv4_prod_list_code,t1.lv4_prod_list_cn_name,
        t1.lv4_prod_list_code AS group_code,t1.lv4_prod_list_cn_name AS group_cn_name,'LV4' AS group_level,
        concat(DECODE(t2.lv0_prod_list_code,'','',t2.lv0_prod_list_code),
        DECODE(t1.lv1_prod_list_code,'','','#*#' || t1.lv1_prod_list_code),
        DECODE(t1.lv2_prod_list_code,'','','#*#' || t1.lv2_prod_list_code),
        DECODE(t1.lv3_prod_list_code,'','','#*#' || t1.lv3_prod_list_code),
        DECODE(t1.lv4_prod_list_code,'','','#*#' || t1.lv4_prod_list_code)
        ) as connectCode,
        concat(DECODE(t2.lv0_prod_list_code,'','',t2.lv0_prod_list_code),
        DECODE(t1.lv1_prod_list_code,'','','#*#' || t1.lv1_prod_list_code),
        DECODE(t1.lv2_prod_list_code,'','','#*#' || t1.lv2_prod_list_code),
        DECODE(t1.lv3_prod_list_code,'','','#*#' || t1.lv3_prod_list_code)
        ) AS connectParentCode
    </sql>

    <sql id='lv3MainProdLevel'>
        t2.lv0_prod_list_code ,t2.lv0_prod_list_cn_name,
        t1.lv1_prod_list_code,t1.lv1_prod_list_cn_name,
        t1.lv2_prod_list_code,t1.lv2_prod_list_cn_name,
        t1.lv3_prod_list_code,t1.lv3_prod_list_cn_name,
        t1.lv3_prod_list_code AS group_code,t1.lv3_prod_list_cn_name AS group_cn_name,'LV3' AS
        group_level,
        concat(DECODE(t2.lv0_prod_list_code,'','',t2.lv0_prod_list_code),
        DECODE(t1.lv1_prod_list_code,'','','#*#' || t1.lv1_prod_list_code),
        DECODE(t1.lv2_prod_list_code,'','','#*#' || t1.lv2_prod_list_code),
        DECODE(t1.lv3_prod_list_code,'','','#*#' || t1.lv3_prod_list_code)
        ) as connectCode,
        concat(DECODE(t2.lv0_prod_list_code,'','',t2.lv0_prod_list_code),
        DECODE(t1.lv1_prod_list_code,'','','#*#' || t1.lv1_prod_list_code),
        DECODE(t1.lv2_prod_list_code,'','','#*#' || t1.lv2_prod_list_code)
        ) as connectParentCode
    </sql>

    <sql id='lv2MainProdLevel'>
        t2.lv0_prod_list_code,t2.lv0_prod_list_cn_name,
        t1.lv1_prod_list_code,t1.lv1_prod_list_cn_name,
        t1.lv2_prod_list_code,t1.lv2_prod_list_cn_name,
        t1.lv2_prod_list_code AS group_code,t1.lv2_prod_list_cn_name AS group_cn_name,'LV2' AS
        group_level,
        concat(DECODE(t2.lv0_prod_list_code,'','',t2.lv0_prod_list_code),
        DECODE(t1.lv1_prod_list_code,'','','#*#' || t1.lv1_prod_list_code),
        DECODE(t1.lv2_prod_list_code,'','','#*#' || t1.lv2_prod_list_code)
        ) as connectCode,
        concat(DECODE(t2.lv0_prod_list_code,'','',t2.lv0_prod_list_code),
        DECODE(t1.lv1_prod_list_code,'','','#*#' || t1.lv1_prod_list_code)
        ) as connectParentCode
    </sql>

    <sql id='lv1MainProdLevelHasPermission'>
        t2.lv0_prod_list_code,t2.lv0_prod_list_cn_name,
        t1.lv1_prod_list_code,t1.lv1_prod_list_cn_name,
        t1.lv1_prod_list_code AS group_code,t1.lv1_prod_list_cn_name AS group_cn_name,'LV1' AS
        group_level,
        concat(DECODE(t2.lv0_prod_list_code,'','',t2.lv0_prod_list_code),
        DECODE(t1.lv1_prod_list_code,'','','#*#' || t1.lv1_prod_list_code)
        ) as connectCode,
        concat(DECODE(t2.lv0_prod_list_code,'','',t2.lv0_prod_list_code)
        ) as connectParentCode
    </sql>

    <sql id='lv1MainProdLevelNotPermission'>
        t2.lv0_prod_list_code,t2.lv0_prod_list_cn_name,
        t1.lv1_prod_list_code,t1.lv1_prod_list_cn_name,
        t1.lv1_prod_list_code AS group_code,t1.lv1_prod_list_cn_name AS group_cn_name,'LV1' AS
        group_level,
        concat(DECODE(t2.lv0_prod_list_code,'','',t2.lv0_prod_list_code),
        DECODE(t1.lv1_prod_list_code,'','','#*#' || t1.lv1_prod_list_code)
        ) as connectCode
    </sql>

    <sql id='lv0MainProdLevel'>
        t2.lv0_prod_list_code,t2.lv0_prod_list_cn_name,
        t2.lv0_prod_list_code AS group_code,t2.lv0_prod_list_cn_name AS group_cn_name,'LV0' AS
        group_level,
        concat(DECODE(t2.lv0_prod_list_code,'','',t2.lv0_prod_list_code)
        ) as connectCode
    </sql>

    <sql id='mainFlagCondition'>
       <choose>
           <when test='granularityType =="IRB"'>
               <if test='groupLevel !="LV0" and lv1CodeList != null and lv1CodeList.size() > 0'>
                   <foreach collection='lv1CodeList' item="code" open="AND t1.lv1_prod_rnd_team_code IN (" close=")"
                            index="index"
                            separator=",">
                       #{code}
                   </foreach>
               </if>
               <if test='groupLevel !="LV0" and groupLevel !="LV1" and lv2CodeList != null and lv2CodeList.size() > 0'>
                   <foreach collection='lv2CodeList' item="code" open="AND t1.lv2_prod_rnd_team_code IN (" close=")"
                            index="index"
                            separator=",">
                       #{code}
                   </foreach>
               </if>
               <if test='groupLevel !="LV0" and groupLevel !="LV1" and groupLevel !="LV2" and lv3CodeList != null and lv3CodeList.size() > 0'>
                   <foreach collection='lv3CodeList' item="code" open="AND t1.lv3_prod_rnd_team_code IN (" close=")"
                            index="index"
                            separator=",">
                       #{code}
                   </foreach>
               </if>
               <if test='groupLevel !="LV0" and groupLevel !="LV1" and groupLevel !="LV2" and groupLevel !="LV3" and lv4CodeList != null and lv4CodeList.size() > 0'>
                   <foreach collection='lv4CodeList' item="code" open="AND t1.lv4_prod_rnd_team_code IN (" close=")"
                            index="index"
                            separator=",">
                       #{code}
                   </foreach>
               </if>
           </when>
           <when test='granularityType =="INDUS"'>
               <if test='groupLevel !="LV0" and lv1CodeList != null and lv1CodeList.size() > 0'>
                   <foreach collection='lv1CodeList' item="code" open="AND t1.lv1_industry_catg_code IN (" close=")"
                            index="index"
                            separator=",">
                       #{code}
                   </foreach>
               </if>
               <if test='groupLevel !="LV0" and groupLevel !="LV1" and lv2CodeList != null and lv2CodeList.size() > 0'>
                   <foreach collection='lv2CodeList' item="code" open="AND t1.lv2_industry_catg_code IN (" close=")"
                            index="index"
                            separator=",">
                       #{code}
                   </foreach>
               </if>
               <if test='groupLevel !="LV0" and groupLevel !="LV1" and groupLevel !="LV2" and lv3CodeList != null and lv3CodeList.size() > 0'>
                   <foreach collection='lv3CodeList' item="code" open="AND t1.lv3_industry_catg_code IN (" close=")"
                            index="index"
                            separator=",">
                       #{code}
                   </foreach>
               </if>
               <if test='groupLevel !="LV0" and groupLevel !="LV1" and groupLevel !="LV2" and groupLevel !="LV3" and lv4CodeList != null and lv4CodeList.size() > 0'>
                   <foreach collection='lv4CodeList' item="code" open="AND t1.lv4_industry_catg_code IN (" close=")"
                            index="index"
                            separator=",">
                       #{code}
                   </foreach>
               </if>
           </when>
           <when test='granularityType =="PROD"'>
               <if test='groupLevel !="LV0" and lv1CodeList != null and lv1CodeList.size() > 0'>
                   <foreach collection='lv1CodeList' item="code" open="AND t1.lv1_prod_list_code IN (" close=")"
                            index="index"
                            separator=",">
                       #{code}
                   </foreach>
               </if>
               <if test='groupLevel !="LV0" and groupLevel !="LV1" and lv2CodeList != null and lv2CodeList.size() > 0'>
                   <foreach collection='lv2CodeList' item="code" open="AND t1.lv2_prod_list_code IN (" close=")"
                            index="index"
                            separator=",">
                       #{code}
                   </foreach>
               </if>
               <if test='groupLevel !="LV0" and groupLevel !="LV1" and groupLevel !="LV2" and lv3CodeList != null and lv3CodeList.size() > 0'>
                   <foreach collection='lv3CodeList' item="code" open="AND t1.lv3_prod_list_code IN (" close=")"
                            index="index"
                            separator=",">
                       #{code}
                   </foreach>
               </if>
               <if test='groupLevel !="LV0" and groupLevel !="LV1" and groupLevel !="LV2" and groupLevel !="LV3" and lv4CodeList != null and lv4CodeList.size() > 0'>
                   <foreach collection='lv4CodeList' item="code" open="AND t1.lv4_prod_list_code IN (" close=")"
                            index="index"
                            separator=",">
                       #{code}
                   </foreach>
               </if>
           </when>
           <otherwise></otherwise>
       </choose>

    </sql>

    <select id="getIrbDimInfoList" resultMap="resultMap">
        select DISTINCT
        <choose>
            <when test='groupLevel == "SPART"'>
                <if test='pageFlag == "ANNUAL"'>
                    <include refid="aunualIrbSpartLevel"/>
                </if>
                <if test='pageFlag == "MONTH"'>
                    lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,
                    lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,
                    lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,
                    lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,
                    top_spart_code AS group_code,top_spart_cn_name AS group_cn_name,'SPART' AS group_level,
                    concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
                    DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' || LV3_PROD_RND_TEAM_CODE),
                    DECODE(LV4_PROD_RND_TEAM_CODE,'','','#*#' || LV4_PROD_RND_TEAM_CODE),
                    DECODE(top_spart_code,'','','#*#' || top_spart_code)
                    ) as connectCode,
                    concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                    DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                    DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
                    DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' || LV3_PROD_RND_TEAM_CODE),
                    DECODE(LV4_PROD_RND_TEAM_CODE,'','','#*#' || LV4_PROD_RND_TEAM_CODE)
                    ) AS connectParentCode
                </if>
            </when>
            <when test='groupLevel == "SUB_DETAIL"'>
                lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,
                lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,
                lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,
                lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,
                dimension_code,dimension_cn_name,dimension_subcategory_code,dimension_subcategory_cn_name,
                dimension_sub_detail_code,dimension_sub_detail_cn_name,
                dimension_sub_detail_code AS group_code,dimension_sub_detail_cn_name AS group_cn_name,'SUB_DETAIL' AS
                group_level,
                concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
                DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' || LV3_PROD_RND_TEAM_CODE),
                DECODE(LV4_PROD_RND_TEAM_CODE,'','','#*#' || LV4_PROD_RND_TEAM_CODE),
                DECODE(dimension_code,'','','#*#' || dimension_code),
                DECODE(dimension_subcategory_code,'','','#*#' || dimension_subcategory_code),
                DECODE(dimension_sub_detail_code,'','','#*#' || dimension_sub_detail_code)
                ) as connectCode,
                concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
                DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' || LV3_PROD_RND_TEAM_CODE),
                DECODE(LV4_PROD_RND_TEAM_CODE,'','','#*#' || LV4_PROD_RND_TEAM_CODE),
                DECODE(dimension_code,'','','#*#' || dimension_code),
                DECODE(dimension_subcategory_code,'','','#*#' || dimension_subcategory_code)
                ) as connectParentCode
            </when>
            <when test='groupLevel == "SUBCATEGORY"'>
                lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,
                lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,
                lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,
                lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,
                dimension_code,dimension_cn_name,dimension_subcategory_code,dimension_subcategory_cn_name,
                dimension_subcategory_code AS group_code,dimension_subcategory_cn_name AS group_cn_name,'SUBCATEGORY' AS
                group_level,
                concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
                DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' || LV3_PROD_RND_TEAM_CODE),
                DECODE(LV4_PROD_RND_TEAM_CODE,'','','#*#' || LV4_PROD_RND_TEAM_CODE),
                DECODE(dimension_code,'','','#*#' || dimension_code),
                DECODE(dimension_subcategory_code,'','','#*#' || dimension_subcategory_code)
                ) as connectCode,
                concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
                DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' || LV3_PROD_RND_TEAM_CODE),
                DECODE(LV4_PROD_RND_TEAM_CODE,'','','#*#' || LV4_PROD_RND_TEAM_CODE),
                DECODE(dimension_code,'','','#*#' || dimension_code)
                ) as connectParentCode
            </when>
            <when test='groupLevel == "DIMENSION"'>
                lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,
                lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,
                lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,
                lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,
                dimension_code,dimension_cn_name,
                dimension_code AS group_code,dimension_cn_name AS group_cn_name,'DIMENSION' AS group_level,
                concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
                DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' || LV3_PROD_RND_TEAM_CODE),
                DECODE(LV4_PROD_RND_TEAM_CODE,'','','#*#' || LV4_PROD_RND_TEAM_CODE),
                DECODE(dimension_code,'','','#*#' || dimension_code)
                ) as connectCode,
                concat(DECODE(LV0_PROD_RND_TEAM_CODE,'','',LV0_PROD_RND_TEAM_CODE),
                DECODE(LV1_PROD_RND_TEAM_CODE,'','','#*#' || LV1_PROD_RND_TEAM_CODE),
                DECODE(LV2_PROD_RND_TEAM_CODE,'','','#*#' || LV2_PROD_RND_TEAM_CODE),
                DECODE(LV3_PROD_RND_TEAM_CODE,'','','#*#' || LV3_PROD_RND_TEAM_CODE),
                DECODE(LV4_PROD_RND_TEAM_CODE,'','','#*#' || LV4_PROD_RND_TEAM_CODE)
                ) as connectParentCode
            </when>
            <when test='groupLevel == "LV4"'>
                <include refid="lv4IrbLevel"/>
            </when>
            <when test='groupLevel == "LV3"'>
                <include refid="lv3IrbLevel"/>
            </when>
            <when test='groupLevel == "LV2"'>
                <include refid="lv2IrbLevel"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag =="Y"'>
                <include refid="lv1IrbHasPermission"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag =="N"'>
                <include refid="lv1IrbNotPermission"/>
            </when>
            <when test='groupLevel == "LV0"'>
                <include refid="lv0IrbLevel"/>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='costType == "PSP"'>
            <choose>
                <when test='pageFlag == "ANNUAL"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_irb_dim_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag == "PROD_SPART"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_irb_top_spart_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag == "DIMENSION"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_irb_dim_info_t
                </when>
            </choose>
        </if>
        <if test='costType == "STD"'>
            <choose>
                <when test='pageFlag == "ANNUAL"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_irb_dim_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag == "PROD_SPART"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_irb_top_spart_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag == "DIMENSION"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_irb_dim_info_t
                </when>
            </choose>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='pageFlag == "MONTH" and viewFlag == "PROD_SPART"'>
                and is_top_flag ='Y'
                and double_flag = 'Y'
            </if>
            <if test='regionCode!=null and regionCode!=""'>
                and region_code = #{regionCode,jdbcType=VARCHAR}
            </if>
            <if test='repofficeCode!=null and repofficeCode!=""'>
                and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='bgCode!=null and bgCode!=""'>
                and bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='overseaFlag!=null and overseaFlag!=""'>
                and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='viewFlag!=null and viewFlag!=""'>
                and view_flag = #{viewFlag,jdbcType=VARCHAR}
            </if>
            <if test='mainFlag!=null and mainFlag!=""'>
                and main_flag = #{mainFlag,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='softwareMark!=null and softwareMark!=""'>
                and software_mark = #{softwareMark,jdbcType=VARCHAR}
            </if>
            <if test='versionId!=null '>
                <choose>
                    <when test='pageFlag == "MONTH" and viewFlag == "PROD_SPART"'>
                        and version_id = #{monVersionId}
                    </when>
                    <otherwise>
                        and version_id = #{versionId}
                    </otherwise>
                </choose>
            </if>
            <if test='pageFlag == "ANNUAL" and groupLevel !=null and groupLevel !=""'>
                and group_level = #{groupLevel,jdbcType=VARCHAR}
            </if>
            <if test='pageFlag == "MONTH" and viewFlag == "DIMENSION"  and groupLevel!=null and groupLevel!=""'>
                and group_level = #{groupLevel,jdbcType=VARCHAR}
            </if>
            <if test='lv0Code!=null and lv0Code!=""'>
                and lv0_prod_rnd_team_code = #{lv0Code,jdbcType=VARCHAR}
            </if>
            <if test='lv1Code!=null and lv1Code!=""'>
                and lv1_prod_rnd_team_code = #{lv1Code,jdbcType=VARCHAR}
            </if>
            <if test='lv2Code!=null and lv2Code!=""'>
                and lv2_prod_rnd_team_code = #{lv2Code,jdbcType=VARCHAR}
            </if>
            <if test='lv3Code!=null and lv3Code!=""'>
                and lv3_prod_rnd_team_code = #{lv3Code,jdbcType=VARCHAR}
            </if>
            <if test='lv4Code!=null and lv4Code!=""'>
                and lv4_prod_rnd_team_code = #{lv4Code,jdbcType=VARCHAR}
            </if>
            <if test='dimensionCode!=null and dimensionCode!=""'>
                and dimension_code = #{dimensionCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubCategoryCode!=null and dimensionSubCategoryCode!=""'>
                and dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubDetailCode!=null and dimensionSubDetailCode!=""'>
                and dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!="" and pageFlag == "ANNUAL"'>
                and spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!="" and pageFlag == "MONTH"'>
                and top_spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='groupLevel == "SPART" and pageFlag == "ANNUAL"'>
                and spart_code !='SNULL'
            </if>
            <if test='groupLevel == "SPART" and pageFlag == "MONTH"'>
                and top_spart_code !='SNULL'
            </if>
            <if test='groupLevel == "SUB_DETAIL"'>
                and dimension_sub_detail_code !='SNULL'
            </if>
            <if test='keyword != null and keyword != ""'>
                and group_cn_name LIKE CONCAT(CONCAT('%', #{keyword}::text ,'%'))
            </if>
            <if test='groupLevel != "LV0" and lv1CodeList != null and lv1CodeList.size() > 0'>
                <foreach collection='lv1CodeList' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv2CodeList != null and lv2CodeList.size() > 0'>
                <foreach collection='lv2CodeList' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and groupLevel != "LV2" and lv3CodeList != null and lv3CodeList.size() > 0'>
                <foreach collection='lv3CodeList' item="code" open="AND lv3_prod_rnd_team_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and groupLevel != "LV2" and groupLevel != "LV3" and lv4CodeList != null and lv4CodeList.size() > 0'>
                <foreach collection='lv4CodeList' item="code" open="AND lv4_prod_rnd_team_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getIrbSpartList" resultMap="resultMap">
        select DISTINCT
        lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
        lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,
        lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,
        lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,
        lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,
        #{maxLevel} AS group_level,
        <if test='pageFlag == "ANNUAL"'>
            spart_code,spart_cn_name
        </if>
        <if test='pageFlag == "MONTH"'>
            top_spart_code as spart_code,top_spart_cn_name as spart_cn_name
        </if>
        <if test='costType == "PSP"'>
            <choose>
                <when test='pageFlag == "ANNUAL"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_irb_dim_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag == "PROD_SPART"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_irb_top_spart_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag == "DIMENSION"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_irb_dim_info_t
                </when>
            </choose>
        </if>
        <if test='costType == "STD"'>
            <choose>
                <when test='pageFlag == "ANNUAL"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_irb_dim_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag == "PROD_SPART"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_irb_top_spart_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag == "DIMENSION"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_irb_dim_info_t
                </when>
            </choose>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='pageFlag == "MONTH" and viewFlag == "PROD_SPART"'>
                and is_top_flag ='Y'
                and double_flag = 'Y'
            </if>
            <if test='regionCode!=null and regionCode!=""'>
                and region_code = #{regionCode,jdbcType=VARCHAR}
            </if>
            <if test='repofficeCode!=null and repofficeCode!=""'>
                and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='bgCode!=null and bgCode!=""'>
                and bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='overseaFlag!=null and overseaFlag!=""'>
                and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='viewFlag!=null and viewFlag!=""'>
                and view_flag = #{viewFlag,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='costType=="PSP" and softwareMark!=null and softwareMark!=""'>
                and software_mark = #{softwareMark,jdbcType=VARCHAR}
            </if>
            <if test='versionId!=null '>
                <choose>
                    <when test='pageFlag == "MONTH" and viewFlag == "PROD_SPART"'>
                        and version_id = #{monVersionId}
                    </when>
                    <otherwise>
                        and version_id = #{versionId}
                    </otherwise>
                </choose>
            </if>
            <if test='pageFlag == "ANNUAL" and groupLevel !=null and groupLevel !=""'>
                and group_level = #{groupLevel,jdbcType=VARCHAR}
            </if>
            <if test='pageFlag == "MONTH" and viewFlag == "DIMENSION"  and groupLevel!=null and groupLevel!=""'>
                and group_level = #{groupLevel,jdbcType=VARCHAR}
            </if>
            <if test='lv0Code!=null and lv0Code!=""'>
                and lv0_prod_rnd_team_code = #{lv0Code,jdbcType=VARCHAR}
            </if>
            <if test='lv1Code!=null and lv1Code!=""'>
                and lv1_prod_rnd_team_code = #{lv1Code,jdbcType=VARCHAR}
            </if>
            <if test='lv2Code!=null and lv2Code!=""'>
                and lv2_prod_rnd_team_code = #{lv2Code,jdbcType=VARCHAR}
            </if>
            <if test='lv3Code!=null and lv3Code!=""'>
                and lv3_prod_rnd_team_code = #{lv3Code,jdbcType=VARCHAR}
            </if>
            <if test='lv4Code!=null and lv4Code!=""'>
                and lv4_prod_rnd_team_code = #{lv4Code,jdbcType=VARCHAR}
            </if>
            <if test='dimensionCode!=null and dimensionCode!=""'>
                and dimension_code = #{dimensionCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubCategoryCode!=null and dimensionSubCategoryCode!=""'>
                and dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubDetailCode!=null and dimensionSubDetailCode!=""'>
                and dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!="" and pageFlag == "ANNUAL"'>
                and spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!="" and pageFlag == "MONTH"'>
                and top_spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='groupLevel == "SPART" and pageFlag == "ANNUAL"'>
                and spart_code !='SNULL'
            </if>
            <if test='groupLevel == "SPART" and pageFlag == "MONTH"'>
                and top_spart_code !='SNULL'
            </if>
            <if test='groupLevel == "SUB_DETAIL"'>
                and dimension_sub_detail_code !='SNULL'
            </if>
            <if test='lv1CodeList != null and lv1CodeList.size() > 0'>
                <foreach collection='lv1CodeList' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv2CodeList != null and lv2CodeList.size() > 0'>
                <foreach collection='lv2CodeList' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv3CodeList != null and lv3CodeList.size() > 0'>
                <foreach collection='lv3CodeList' item="code" open="AND lv3_prod_rnd_team_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv4CodeList != null and lv4CodeList.size() > 0'>
                <foreach collection='lv4CodeList' item="code" open="AND lv4_prod_rnd_team_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='spartCodeList != null and spartCodeList.size() > 0'>
                <foreach collection='spartCodeList' item="code" open="AND spart_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getIndusDimInfoList" resultMap="resultMap">
        select DISTINCT
        <choose>
            <when test='groupLevel == "SPART"'>
                <if test='pageFlag == "ANNUAL"'>
                    <include refid="annualIndusSpartLevel"/>
                </if>
                <if test='pageFlag == "MONTH"'>
                    lv0_industry_catg_code,lv0_industry_catg_cn_name,
                    lv1_industry_catg_code,lv1_industry_catg_cn_name,
                    lv2_industry_catg_code,lv2_industry_catg_cn_name,
                    lv3_industry_catg_code,lv3_industry_catg_cn_name,
                    lv4_industry_catg_code,lv4_industry_catg_cn_name,
                    top_spart_code AS group_code,top_spart_cn_name AS group_cn_name,'SPART' AS group_level,
                    concat(DECODE(lv0_industry_catg_code,'','',lv0_industry_catg_code),
                    DECODE(lv1_industry_catg_code,'','','#*#' || lv1_industry_catg_code),
                    DECODE(lv2_industry_catg_code,'','','#*#' || lv2_industry_catg_code),
                    DECODE(lv3_industry_catg_code,'','','#*#' || lv3_industry_catg_code),
                    DECODE(lv4_industry_catg_code,'','','#*#' || lv4_industry_catg_code),
                    DECODE(top_spart_code,'','','#*#' || top_spart_code)
                    ) as connectCode,
                    concat(DECODE(lv0_industry_catg_code,'','',lv0_industry_catg_code),
                    DECODE(lv1_industry_catg_code,'','','#*#' || lv1_industry_catg_code),
                    DECODE(lv2_industry_catg_code,'','','#*#' || lv2_industry_catg_code),
                    DECODE(lv3_industry_catg_code,'','','#*#' || lv3_industry_catg_code),
                    DECODE(lv4_industry_catg_code,'','','#*#' || lv4_industry_catg_code)
                    ) AS connectParentCode
                </if>
            </when>
            <when test='groupLevel == "SUB_DETAIL"'>
                lv0_industry_catg_code,lv0_industry_catg_cn_name,
                lv1_industry_catg_code,lv1_industry_catg_cn_name,
                lv2_industry_catg_code,lv2_industry_catg_cn_name,
                lv3_industry_catg_code,lv3_industry_catg_cn_name,
                lv4_industry_catg_code,lv4_industry_catg_cn_name,
                dimension_code,dimension_cn_name,dimension_subcategory_code,dimension_subcategory_cn_name,
                dimension_sub_detail_code,dimension_sub_detail_cn_name,
                dimension_sub_detail_code AS group_code,dimension_sub_detail_cn_name AS group_cn_name,'SUB_DETAIL' AS
                group_level,
                concat(DECODE(lv0_industry_catg_code,'','',lv0_industry_catg_code),
                DECODE(lv1_industry_catg_code,'','','#*#' || lv1_industry_catg_code),
                DECODE(lv2_industry_catg_code,'','','#*#' || lv2_industry_catg_code),
                DECODE(lv3_industry_catg_code,'','','#*#' || lv3_industry_catg_code),
                DECODE(lv4_industry_catg_code,'','','#*#' || lv4_industry_catg_code),
                DECODE(dimension_code,'','','#*#' || dimension_code),
                DECODE(dimension_subcategory_code,'','','#*#' || dimension_subcategory_code),
                DECODE(dimension_sub_detail_code,'','','#*#' || dimension_sub_detail_code)
                ) as connectCode,
                concat(DECODE(lv0_industry_catg_code,'','',lv0_industry_catg_code),
                DECODE(lv1_industry_catg_code,'','','#*#' || lv1_industry_catg_code),
                DECODE(lv2_industry_catg_code,'','','#*#' || lv2_industry_catg_code),
                DECODE(lv3_industry_catg_code,'','','#*#' || lv3_industry_catg_code),
                DECODE(lv4_industry_catg_code,'','','#*#' || lv4_industry_catg_code),
                DECODE(dimension_code,'','','#*#' || dimension_code),
                DECODE(dimension_subcategory_code,'','','#*#' || dimension_subcategory_code)
                ) as connectParentCode
            </when>
            <when test='groupLevel == "SUBCATEGORY"'>
                lv0_industry_catg_code,lv0_industry_catg_cn_name,
                lv1_industry_catg_code,lv1_industry_catg_cn_name,
                lv2_industry_catg_code,lv2_industry_catg_cn_name,
                lv3_industry_catg_code,lv3_industry_catg_cn_name,
                lv4_industry_catg_code,lv4_industry_catg_cn_name,
                dimension_code,dimension_cn_name,dimension_subcategory_code,dimension_subcategory_cn_name,
                dimension_subcategory_code AS group_code,dimension_subcategory_cn_name AS group_cn_name,'SUBCATEGORY' AS
                group_level,
                concat(DECODE(lv0_industry_catg_code,'','',lv0_industry_catg_code),
                DECODE(lv1_industry_catg_code,'','','#*#' || lv1_industry_catg_code),
                DECODE(lv2_industry_catg_code,'','','#*#' || lv2_industry_catg_code),
                DECODE(lv3_industry_catg_code,'','','#*#' || lv3_industry_catg_code),
                DECODE(lv4_industry_catg_code,'','','#*#' || lv4_industry_catg_code),
                DECODE(dimension_code,'','','#*#' || dimension_code),
                DECODE(dimension_subcategory_code,'','','#*#' || dimension_subcategory_code)
                ) as connectCode,
                concat(DECODE(lv0_industry_catg_code,'','',lv0_industry_catg_code),
                DECODE(lv1_industry_catg_code,'','','#*#' || lv1_industry_catg_code),
                DECODE(lv2_industry_catg_code,'','','#*#' || lv2_industry_catg_code),
                DECODE(lv3_industry_catg_code,'','','#*#' || lv3_industry_catg_code),
                DECODE(lv4_industry_catg_code,'','','#*#' || lv4_industry_catg_code),
                DECODE(dimension_code,'','','#*#' || dimension_code)
                ) as connectParentCode
            </when>
            <when test='groupLevel == "DIMENSION"'>
                lv0_industry_catg_code,lv0_industry_catg_cn_name,
                lv1_industry_catg_code,lv1_industry_catg_cn_name,
                lv2_industry_catg_code,lv2_industry_catg_cn_name,
                lv3_industry_catg_code,lv3_industry_catg_cn_name,
                lv4_industry_catg_code,lv4_industry_catg_cn_name,
                dimension_code,dimension_cn_name,
                dimension_code AS group_code,dimension_cn_name AS group_cn_name,'DIMENSION' AS
                group_level,
                concat(DECODE(lv0_industry_catg_code,'','',lv0_industry_catg_code),
                DECODE(lv1_industry_catg_code,'','','#*#' || lv1_industry_catg_code),
                DECODE(lv2_industry_catg_code,'','','#*#' || lv2_industry_catg_code),
                DECODE(lv3_industry_catg_code,'','','#*#' || lv3_industry_catg_code),
                DECODE(lv4_industry_catg_code,'','','#*#' || lv4_industry_catg_code),
                DECODE(dimension_code,'','','#*#' || dimension_code)
                ) as connectCode,
                concat(DECODE(lv0_industry_catg_code,'','',lv0_industry_catg_code),
                DECODE(lv1_industry_catg_code,'','','#*#' || lv1_industry_catg_code),
                DECODE(lv2_industry_catg_code,'','','#*#' || lv2_industry_catg_code),
                DECODE(lv3_industry_catg_code,'','','#*#' || lv3_industry_catg_code),
                DECODE(lv4_industry_catg_code,'','','#*#' || lv4_industry_catg_code)
                ) as connectParentCode
            </when>
            <when test='groupLevel == "LV4"'>
                <include refid="lv4IndusLevel"/>
            </when>
            <when test='groupLevel == "LV3"'>
                <include refid="lv3IndusLevel"/>
            </when>
            <when test='groupLevel == "LV2"'>
                <include refid="lv2IndusLevel"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag=="Y"'>
                <include refid="lv1IndusLevelHasPermission"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag=="N"'>
                <include refid="lv1IndusLevelNotPermission"/>
            </when>
            <when test='groupLevel == "LV0"'>
                <include refid="lv0IndusLevel"/>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='costType == "PSP"'>
            <choose>
                <when test='pageFlag == "ANNUAL"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_indus_dim_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag =="PROD_SPART"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_indus_top_spart_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag =="DIMENSION"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_indus_dim_info_t
                </when>
            </choose>
        </if>
        <if test='costType == "STD"'>
            <choose>
                <when test='pageFlag == "ANNUAL"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_indus_dim_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag =="PROD_SPART"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_indus_top_spart_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag =="DIMENSION"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_indus_dim_info_t
                </when>
            </choose>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='pageFlag == "MONTH" and viewFlag == "PROD_SPART"'>
                and is_top_flag ='Y'
                and double_flag = 'Y'
            </if>
            <if test='regionCode!=null and regionCode!=""'>
                and region_code = #{regionCode,jdbcType=VARCHAR}
            </if>
            <if test='repofficeCode!=null and repofficeCode!=""'>
                and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='bgCode!=null and bgCode!=""'>
                and bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='overseaFlag!=null and overseaFlag!=""'>
                and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='viewFlag!=null and viewFlag!=""'>
                and view_flag = #{viewFlag,jdbcType=VARCHAR}
            </if>
            <if test='mainFlag!=null and mainFlag!=""'>
                and main_flag = #{mainFlag,jdbcType=VARCHAR}
            </if>
            <if test='softwareMark!=null and softwareMark!=""'>
                and software_mark = #{softwareMark,jdbcType=VARCHAR}
            </if>
            <if test='versionId!=null '>
                <choose>
                    <when test='pageFlag == "MONTH" and viewFlag == "PROD_SPART"'>
                        and version_id = #{monVersionId}
                    </when>
                    <otherwise>
                        and version_id = #{versionId}
                    </otherwise>
                </choose>
            </if>
            <if test='pageFlag == "ANNUAL" and groupLevel!=null and groupLevel!="" '>
                and group_level = #{groupLevel,jdbcType=VARCHAR}
            </if>
            <if test='pageFlag == "MONTH" and viewFlag == "DIMENSION" and groupLevel!=null and groupLevel!=""'>
                and group_level = #{groupLevel,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='lv0Code!=null and lv0Code!=""'>
                and lv0_industry_catg_code = #{lv0Code,jdbcType=VARCHAR}
            </if>
            <if test='lv1Code!=null and lv1Code!=""'>
                and lv1_industry_catg_code = #{lv1Code,jdbcType=VARCHAR}
            </if>
            <if test='lv2Code!=null and lv2Code!=""'>
                and lv2_industry_catg_code = #{lv2Code,jdbcType=VARCHAR}
            </if>
            <if test='lv3Code!=null and lv3Code!=""'>
                and lv3_industry_catg_code = #{lv3Code,jdbcType=VARCHAR}
            </if>
            <if test='lv4Code!=null and lv4Code!=""'>
                and lv4_industry_catg_code = #{lv4Code,jdbcType=VARCHAR}
            </if>
            <if test='dimensionCode!=null and dimensionCode!=""'>
                and dimension_code = #{dimensionCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubCategoryCode!=null and dimensionSubCategoryCode!=""'>
                and dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubDetailCode!=null and dimensionSubDetailCode!=""'>
                and dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!="" and pageFlag == "ANNUAL"'>
                and spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!="" and pageFlag == "MONTH"'>
                and top_spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='groupLevel == "SPART" and pageFlag == "ANNUAL"'>
                and spart_code !='SNULL'
            </if>
            <if test='groupLevel == "SPART" and pageFlag != "ANNUAL"'>
                and top_spart_code !='SNULL'
            </if>
            <if test='groupLevel == "SUB_DETAIL"'>
                and dimension_sub_detail_code !='SNULL'
            </if>
            <if test='keyword != null and keyword != ""'>
                and group_cn_name LIKE CONCAT(CONCAT('%', #{keyword}::text ,'%'))
            </if>
            <if test='groupLevel != "LV0" and lv1CodeList != null and lv1CodeList.size() > 0'>
                <foreach collection='lv1CodeList' item="code" open="AND lv1_industry_catg_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv2CodeList != null and lv2CodeList.size() > 0'>
                <foreach collection='lv2CodeList' item="code" open="AND lv2_industry_catg_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and groupLevel != "LV2" and lv3CodeList != null and lv3CodeList.size() > 0'>
                <foreach collection='lv3CodeList' item="code" open="AND lv3_industry_catg_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and groupLevel != "LV2" and groupLevel != "LV3" and lv4CodeList != null and lv4CodeList.size() > 0'>
                <foreach collection='lv4CodeList' item="code" open="AND lv4_industry_catg_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND lv1_industry_catg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND lv2_industry_catg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getIndusSpartList" resultMap="resultMap">
        select DISTINCT
        lv0_industry_catg_code,lv0_industry_catg_cn_name,#{maxLevel} AS group_level,
        lv1_industry_catg_code,lv1_industry_catg_cn_name,
        lv2_industry_catg_code,lv2_industry_catg_cn_name,
        lv3_industry_catg_code,lv3_industry_catg_cn_name,
        lv4_industry_catg_code,lv4_industry_catg_cn_name,
        <if test='pageFlag == "ANNUAL"'>
            spart_code,spart_cn_name
        </if>
        <if test='pageFlag == "MONTH"'>
            top_spart_code as spart_code,top_spart_cn_name as spart_cn_name
        </if>
        <if test='costType == "PSP"'>
            <choose>
                <when test='pageFlag == "ANNUAL"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_indus_dim_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag =="PROD_SPART"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_indus_top_spart_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag =="DIMENSION"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_indus_dim_info_t
                </when>
            </choose>
        </if>
        <if test='costType == "STD"'>
            <choose>
                <when test='pageFlag == "ANNUAL"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_indus_dim_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag =="PROD_SPART"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_indus_top_spart_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag =="DIMENSION"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_indus_dim_info_t
                </when>
            </choose>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='pageFlag == "MONTH" and viewFlag == "PROD_SPART"'>
                and is_top_flag ='Y'
                and double_flag = 'Y'
            </if>
            <if test='regionCode!=null and regionCode!=""'>
                and region_code = #{regionCode,jdbcType=VARCHAR}
            </if>
            <if test='repofficeCode!=null and repofficeCode!=""'>
                and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='bgCode!=null and bgCode!=""'>
                and bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='overseaFlag!=null and overseaFlag!=""'>
                and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='viewFlag!=null and viewFlag!=""'>
                and view_flag = #{viewFlag,jdbcType=VARCHAR}
            </if>
            <if test='costType=="PSP" and softwareMark!=null and softwareMark!=""'>
                and software_mark = #{softwareMark,jdbcType=VARCHAR}
            </if>
            <if test='versionId!=null '>
                <choose>
                    <when test='pageFlag == "MONTH" and viewFlag == "PROD_SPART"'>
                        and version_id = #{monVersionId}
                    </when>
                    <otherwise>
                        and version_id = #{versionId}
                    </otherwise>
                </choose>
            </if>
            <if test='pageFlag == "ANNUAL" and groupLevel!=null and groupLevel!="" '>
                and group_level = #{groupLevel,jdbcType=VARCHAR}
            </if>
            <if test='pageFlag == "MONTH" and viewFlag == "DIMENSION" and groupLevel!=null and groupLevel!=""'>
                and group_level = #{groupLevel,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='lv0Code!=null and lv0Code!=""'>
                and lv0_industry_catg_code = #{lv0Code,jdbcType=VARCHAR}
            </if>
            <if test='lv1Code!=null and lv1Code!=""'>
                and lv1_industry_catg_code = #{lv1Code,jdbcType=VARCHAR}
            </if>
            <if test='lv2Code!=null and lv2Code!=""'>
                and lv2_industry_catg_code = #{lv2Code,jdbcType=VARCHAR}
            </if>
            <if test='lv3Code!=null and lv3Code!=""'>
                and lv3_industry_catg_code = #{lv3Code,jdbcType=VARCHAR}
            </if>
            <if test='lv4Code!=null and lv4Code!=""'>
                and lv4_industry_catg_code = #{lv4Code,jdbcType=VARCHAR}
            </if>
            <if test='dimensionCode!=null and dimensionCode!=""'>
                and dimension_code = #{dimensionCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubCategoryCode!=null and dimensionSubCategoryCode!=""'>
                and dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubDetailCode!=null and dimensionSubDetailCode!=""'>
                and dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!="" and pageFlag == "ANNUAL"'>
                and spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!="" and pageFlag == "MONTH"'>
                and top_spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='groupLevel == "SPART" and pageFlag == "ANNUAL"'>
                and spart_code !='SNULL'
            </if>
            <if test='groupLevel == "SPART" and pageFlag != "ANNUAL"'>
                and top_spart_code !='SNULL'
            </if>
            <if test='groupLevel == "SUB_DETAIL"'>
                and dimension_sub_detail_code !='SNULL'
            </if>
            <if test='lv1CodeList != null and lv1CodeList.size() > 0'>
                <foreach collection='lv1CodeList' item="code" open="AND lv1_industry_catg_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv2CodeList != null and lv2CodeList.size() > 0'>
                <foreach collection='lv2CodeList' item="code" open="AND lv2_industry_catg_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv3CodeList != null and lv3CodeList.size() > 0'>
                <foreach collection='lv3CodeList' item="code" open="AND lv3_industry_catg_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv4CodeList != null and lv4CodeList.size() > 0'>
                <foreach collection='lv4CodeList' item="code" open="AND lv4_industry_catg_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='spartCodeList != null and spartCodeList.size() > 0'>
                <foreach collection='spartCodeList' item="code" open="AND spart_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND lv1_industry_catg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND lv2_industry_catg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getProdDimInfoList" resultMap="resultMap">
        select DISTINCT
        <choose>
            <when test='groupLevel == "SPART"'>
                <if test='pageFlag == "ANNUAL"'>
                    <include refid="annualProdSpartLevel"/>
                </if>
                <if test='pageFlag == "MONTH"'>
                    lv0_prod_list_code,lv0_prod_list_cn_name,
                    lv1_prod_list_code,lv1_prod_list_cn_name,
                    lv2_prod_list_code,lv2_prod_list_cn_name,
                    lv3_prod_list_code,lv3_prod_list_cn_name,
                    lv4_prod_list_code,lv4_prod_list_cn_name,
                    top_spart_code AS spart_code,top_spart_cn_name AS spart_cn_name,
                    top_spart_code AS group_code,top_spart_cn_name AS group_cn_name,'SPART' AS group_level,
                    concat(DECODE(lv0_prod_list_code,'','',lv0_prod_list_code),
                    DECODE(lv1_prod_list_code,'','','#*#' || lv1_prod_list_code),
                    DECODE(lv2_prod_list_code,'','','#*#' || lv2_prod_list_code),
                    DECODE(lv3_prod_list_code,'','','#*#' || lv3_prod_list_code),
                    DECODE(lv4_prod_list_code,'','','#*#' || lv4_prod_list_code),
                    DECODE(top_spart_code,'','','#*#' || top_spart_code)
                    ) as connectCode,
                    concat(DECODE(lv0_prod_list_code,'','',lv0_prod_list_code),
                    DECODE(lv1_prod_list_code,'','','#*#' || lv1_prod_list_code),
                    DECODE(lv2_prod_list_code,'','','#*#' || lv2_prod_list_code),
                    DECODE(lv3_prod_list_code,'','','#*#' || lv3_prod_list_code),
                    DECODE(lv4_prod_list_code,'','','#*#' || lv4_prod_list_code)
                    ) AS connectParentCode
                </if>
            </when>
            <when test='groupLevel == "SUB_DETAIL"'>
                lv0_prod_list_code,lv0_prod_list_cn_name,
                lv1_prod_list_code,lv1_prod_list_cn_name,
                lv2_prod_list_code,lv2_prod_list_cn_name,
                lv3_prod_list_code,lv3_prod_list_cn_name,
                lv4_prod_list_code,lv4_prod_list_cn_name,
                dimension_code,dimension_cn_name,dimension_subcategory_code,dimension_subcategory_cn_name,
                dimension_sub_detail_code,dimension_sub_detail_cn_name,
                dimension_sub_detail_code AS group_code,dimension_sub_detail_cn_name AS group_cn_name,'SUB_DETAIL' AS
                group_level,
                concat(DECODE(lv0_prod_list_code,'','',lv0_prod_list_code),
                DECODE(lv1_prod_list_code,'','','#*#' || lv1_prod_list_code),
                DECODE(lv2_prod_list_code,'','','#*#' || lv2_prod_list_code),
                DECODE(lv3_prod_list_code,'','','#*#' || lv3_prod_list_code),
                DECODE(lv4_prod_list_code,'','','#*#' || lv4_prod_list_code),
                DECODE(dimension_code,'','','#*#' || dimension_code),
                DECODE(dimension_subcategory_code,'','','#*#' || dimension_subcategory_code),
                DECODE(dimension_sub_detail_code,'','','#*#' || dimension_sub_detail_code)
                ) as connectCode,
                concat(DECODE(lv0_prod_list_code,'','',lv0_prod_list_code),
                DECODE(lv1_prod_list_code,'','','#*#' || lv1_prod_list_code),
                DECODE(lv2_prod_list_code,'','','#*#' || lv2_prod_list_code),
                DECODE(lv3_prod_list_code,'','','#*#' || lv3_prod_list_code),
                DECODE(lv4_prod_list_code,'','','#*#' || lv4_prod_list_code),
                DECODE(dimension_code,'','','#*#' || dimension_code),
                DECODE(dimension_subcategory_code,'','','#*#' || dimension_subcategory_code)
                ) as connectParentCode
            </when>
            <when test='groupLevel == "SUBCATEGORY"'>
                lv0_prod_list_code,lv0_prod_list_cn_name,
                lv1_prod_list_code,lv1_prod_list_cn_name,
                lv2_prod_list_code,lv2_prod_list_cn_name,
                lv3_prod_list_code,lv3_prod_list_cn_name,
                lv4_prod_list_code,lv4_prod_list_cn_name,
                dimension_code,dimension_cn_name,dimension_subcategory_code,dimension_subcategory_cn_name,
                dimension_subcategory_code AS group_code,dimension_subcategory_cn_name AS group_cn_name,'SUBCATEGORY' AS
                group_level,
                concat(DECODE(lv0_prod_list_code,'','',lv0_prod_list_code),
                DECODE(lv1_prod_list_code,'','','#*#' || lv1_prod_list_code),
                DECODE(lv2_prod_list_code,'','','#*#' || lv2_prod_list_code),
                DECODE(lv3_prod_list_code,'','','#*#' || lv3_prod_list_code),
                DECODE(lv4_prod_list_code,'','','#*#' || lv4_prod_list_code),
                DECODE(dimension_code,'','','#*#' || dimension_code),
                DECODE(dimension_subcategory_code,'','','#*#' || dimension_subcategory_code)
                ) as connectCode,
                concat(DECODE(lv0_prod_list_code,'','',lv0_prod_list_code),
                DECODE(lv1_prod_list_code,'','','#*#' || lv1_prod_list_code),
                DECODE(lv2_prod_list_code,'','','#*#' || lv2_prod_list_code),
                DECODE(lv3_prod_list_code,'','','#*#' || lv3_prod_list_code),
                DECODE(lv4_prod_list_code,'','','#*#' || lv4_prod_list_code),
                DECODE(dimension_code,'','','#*#' || dimension_code)
                ) as connectParentCode
            </when>
            <when test='groupLevel == "DIMENSION"'>
                lv0_prod_list_code,lv0_prod_list_cn_name,
                lv1_prod_list_code,lv1_prod_list_cn_name,
                lv2_prod_list_code,lv2_prod_list_cn_name,
                lv3_prod_list_code,lv3_prod_list_cn_name,
                lv4_prod_list_code,lv4_prod_list_cn_name,
                dimension_code,dimension_cn_name,
                dimension_code AS group_code,dimension_cn_name AS group_cn_name,'DIMENSION' AS
                group_level,
                concat(DECODE(lv0_prod_list_code,'','',lv0_prod_list_code),
                DECODE(lv1_prod_list_code,'','','#*#' || lv1_prod_list_code),
                DECODE(lv2_prod_list_code,'','','#*#' || lv2_prod_list_code),
                DECODE(lv3_prod_list_code,'','','#*#' || lv3_prod_list_code),
                DECODE(lv4_prod_list_code,'','','#*#' || lv4_prod_list_code),
                DECODE(dimension_code,'','','#*#' || dimension_code)
                ) as connectCode,
                concat(DECODE(lv0_prod_list_code,'','',lv0_prod_list_code),
                DECODE(lv1_prod_list_code,'','','#*#' || lv1_prod_list_code),
                DECODE(lv2_prod_list_code,'','','#*#' || lv2_prod_list_code),
                DECODE(lv3_prod_list_code,'','','#*#' || lv3_prod_list_code),
                DECODE(lv4_prod_list_code,'','','#*#' || lv4_prod_list_code)
                ) as connectParentCode
            </when>
            <when test='groupLevel == "LV4"'>
                <include refid="lv4ProdLevel"/>
            </when>
            <when test='groupLevel == "LV3"'>
                <include refid="lv3ProdLevel"/>
            </when>
            <when test='groupLevel == "LV2"'>
                <include refid="lv2ProdLevel"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag =="Y"'>
                <include refid="lv1ProdLevelHasPermission"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag =="N"'>
                <include refid="lv1ProdLevelNotPermission"/>
            </when>
            <when test='groupLevel == "LV0"'>
                <include refid="lv0ProdLevel"/>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='costType == "PSP"'>
            <choose>
                <when test='pageFlag == "ANNUAL"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_dim_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag =="PROD_SPART"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_top_spart_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag =="DIMENSION"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_dim_info_t
                </when>
            </choose>
        </if>
        <if test='costType == "STD"'>
            <choose>
                <when test='pageFlag == "ANNUAL"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_dim_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag =="PROD_SPART"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_top_spart_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag =="DIMENSION"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_dim_info_t
                </when>
            </choose>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='pageFlag == "MONTH" and viewFlag == "PROD_SPART"'>
                and is_top_flag ='Y'
                and double_flag = 'Y'
            </if>
            <if test='regionCode!=null and regionCode!=""'>
                and region_code = #{regionCode,jdbcType=VARCHAR}
            </if>
            <if test='repofficeCode!=null and repofficeCode!=""'>
                and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='bgCode!=null and bgCode!=""'>
                and bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='overseaFlag!=null and overseaFlag!=""'>
                and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='viewFlag!=null and viewFlag!=""'>
                and view_flag = #{viewFlag,jdbcType=VARCHAR}
            </if>
            <if test='mainFlag!=null and mainFlag!=""'>
                and main_flag = #{mainFlag,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='softwareMark!=null and softwareMark!=""'>
                and software_mark = #{softwareMark,jdbcType=VARCHAR}
            </if>
            <if test='versionId!=null '>
                <choose>
                    <when test='pageFlag == "MONTH" and viewFlag == "PROD_SPART"'>
                        and version_id = #{monVersionId}
                    </when>
                    <otherwise>
                        and version_id = #{versionId}
                    </otherwise>
                </choose>
            </if>
            <if test='pageFlag == "ANNUAL" and groupLevel!=null and groupLevel!="" and groupLevel !="CODE"'>
                and group_level = #{groupLevel,jdbcType=VARCHAR}
            </if>
            <if test='pageFlag == "MONTH" and viewFlag == "DIMENSION"  and groupLevel!=null and groupLevel!="" and groupLevel !="CODE"'>
                and group_level = #{groupLevel,jdbcType=VARCHAR}
            </if>
            <if test='lv0Code!=null and lv0Code!=""'>
                and lv0_prod_list_code = #{lv0Code,jdbcType=VARCHAR}
            </if>
            <if test='lv1Code!=null and lv1Code!=""'>
                and lv1_prod_list_code = #{lv1Code,jdbcType=VARCHAR}
            </if>
            <if test='lv2Code!=null and lv2Code!=""'>
                and lv2_prod_list_code = #{lv2Code,jdbcType=VARCHAR}
            </if>
            <if test='lv3Code!=null and lv3Code!=""'>
                and lv3_prod_list_code = #{lv3Code,jdbcType=VARCHAR}
            </if>
            <if test='lv4Code!=null and lv4Code!=""'>
                and lv4_prod_list_code = #{lv4Code,jdbcType=VARCHAR}
            </if>
            <if test='dimensionCode!=null and dimensionCode!=""'>
                and dimension_code = #{dimensionCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubCategoryCode!=null and dimensionSubCategoryCode!=""'>
                and dimension_subCategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubDetailCode!=null and dimensionSubDetailCode!=""'>
                and dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!="" and pageFlag == "ANNUAL"'>
                and spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!="" and pageFlag != "ANNUAL"'>
                and top_spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='groupLevel == "SPART" and pageFlag == "ANNUAL"'>
                and spart_code !='SNULL'
            </if>
            <if test='groupLevel == "SPART" and pageFlag != "ANNUAL"'>
                and top_spart_code !='SNULL'
            </if>
            <if test='groupLevel == "SUB_DETAIL"'>
                and dimension_sub_detail_code !='SNULL'
            </if>
            <if test='keyword != null and keyword != ""'>
                and group_cn_name LIKE CONCAT(CONCAT('%', #{keyword}::text ,'%'))
            </if>
            <if test='groupLevel != "LV0" and lv1CodeList != null and lv1CodeList.size() > 0'>
                <foreach collection='lv1CodeList' item="code" open="AND lv1_prod_list_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv2CodeList != null and lv2CodeList.size() > 0'>
                <foreach collection='lv2CodeList' item="code" open="AND lv2_prod_list_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and groupLevel != "LV2" and lv3CodeList != null and lv3CodeList.size() > 0'>
                <foreach collection='lv3CodeList' item="code" open="AND lv3_prod_list_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and groupLevel != "LV2" and groupLevel != "LV3" and lv4CodeList != null and lv4CodeList.size() > 0'>
                <foreach collection='lv4CodeList' item="code" open="AND lv4_prod_list_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND lv1_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND lv2_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getProdSpartList" resultMap="resultMap">
        select DISTINCT
        lv0_prod_list_code,lv0_prod_list_cn_name, #{maxLevel} AS group_level,
        lv1_prod_list_code,lv1_prod_list_cn_name,
        lv2_prod_list_code,lv2_prod_list_cn_name,
        lv3_prod_list_code,lv3_prod_list_cn_name,
        lv4_prod_list_code,lv4_prod_list_cn_name,
        <if test='pageFlag == "ANNUAL"'>
            spart_code,spart_cn_name
        </if>
        <if test='pageFlag == "MONTH"'>
            top_spart_code as spart_code,top_spart_cn_name as spart_cn_name
        </if>
        <if test='costType == "PSP"'>
            <choose>
                <when test='pageFlag == "ANNUAL"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_dim_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag =="PROD_SPART"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_top_spart_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag =="DIMENSION"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_dim_info_t
                </when>
            </choose>
        </if>
        <if test='costType == "STD"'>
            <choose>
                <when test='pageFlag == "ANNUAL"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_dim_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag =="PROD_SPART"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_top_spart_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag =="DIMENSION"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_dim_info_t
                </when>
            </choose>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='pageFlag == "MONTH" and viewFlag == "PROD_SPART"'>
                and is_top_flag ='Y'
                and double_flag = 'Y'
            </if>
            <if test='regionCode!=null and regionCode!=""'>
                and region_code = #{regionCode,jdbcType=VARCHAR}
            </if>
            <if test='repofficeCode!=null and repofficeCode!=""'>
                and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='bgCode!=null and bgCode!=""'>
                and bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='overseaFlag!=null and overseaFlag!=""'>
                and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='viewFlag!=null and viewFlag!=""'>
                and view_flag = #{viewFlag,jdbcType=VARCHAR}
            </if>
            <if test='costType=="PSP" and softwareMark!=null and softwareMark!=""'>
                and software_mark = #{softwareMark,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='versionId!=null '>
                <choose>
                    <when test='pageFlag == "MONTH" and viewFlag == "PROD_SPART"'>
                        and version_id = #{monVersionId}
                    </when>
                    <otherwise>
                        and version_id = #{versionId}
                    </otherwise>
                </choose>
            </if>
            <if test='pageFlag == "ANNUAL" and groupLevel!=null and groupLevel!=""'>
                and group_level = #{groupLevel,jdbcType=VARCHAR}
            </if>
            <if test='pageFlag == "MONTH" and viewFlag == "DIMENSION"  and groupLevel!=null and groupLevel!=""'>
                and group_level = #{groupLevel,jdbcType=VARCHAR}
            </if>
            <if test='lv0Code!=null and lv0Code!=""'>
                and lv0_prod_list_code = #{lv0Code,jdbcType=VARCHAR}
            </if>
            <if test='lv1Code!=null and lv1Code!=""'>
                and lv1_prod_list_code = #{lv1Code,jdbcType=VARCHAR}
            </if>
            <if test='lv2Code!=null and lv2Code!=""'>
                and lv2_prod_list_code = #{lv2Code,jdbcType=VARCHAR}
            </if>
            <if test='lv3Code!=null and lv3Code!=""'>
                and lv3_prod_list_code = #{lv3Code,jdbcType=VARCHAR}
            </if>
            <if test='lv4Code!=null and lv4Code!=""'>
                and lv4_prod_list_code = #{lv4Code,jdbcType=VARCHAR}
            </if>
            <if test='dimensionCode!=null and dimensionCode!=""'>
                and dimension_code = #{dimensionCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubCategoryCode!=null and dimensionSubCategoryCode!=""'>
                and dimension_subCategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubDetailCode!=null and dimensionSubDetailCode!=""'>
                and dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!="" and pageFlag == "ANNUAL"'>
                and spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!="" and pageFlag != "ANNUAL"'>
                and top_spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='groupLevel == "SPART" and pageFlag == "ANNUAL"'>
                and spart_code !='SNULL'
            </if>
            <if test='groupLevel == "SPART" and pageFlag != "ANNUAL"'>
                and top_spart_code !='SNULL'
            </if>
            <if test='groupLevel == "SUB_DETAIL"'>
                and dimension_sub_detail_code !='SNULL'
            </if>
            <if test='lv1CodeList != null and lv1CodeList.size() > 0'>
                <foreach collection='lv1CodeList' item="code" open="AND lv1_prod_list_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv2CodeList != null and lv2CodeList.size() > 0'>
                <foreach collection='lv2CodeList' item="code" open="AND lv2_prod_list_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv3CodeList != null and lv3CodeList.size() > 0'>
                <foreach collection='lv3CodeList' item="code" open="AND lv3_prod_list_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv4CodeList != null and lv4CodeList.size() > 0'>
                <foreach collection='lv4CodeList' item="code" open="AND lv4_prod_list_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='spartCodeList != null and spartCodeList.size() > 0'>
                <foreach collection='spartCodeList' item="code" open="AND spart_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND lv1_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND lv2_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getMainIrbDimInfoList" resultMap="resultMap">
        SELECT DISTINCT
        <choose>
            <when test='groupLevel == "SPART"'>
                <include refid="irbMainSpartLevel"/>
            </when>
            <when test='groupLevel == "LV4"'>
                <include refid="irbMainLV4Level"/>
            </when>
            <when test='groupLevel == "LV3"'>
                <include refid="irbMainLv3Level"/>
            </when>
            <when test='groupLevel == "LV2"'>
                <include refid="irbMainLv2Level"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag =="Y"'>
                <include refid="irbMainLv1HasPermission"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag =="N"'>
                <include refid="irbMainLv1NotPermission"/>
            </when>
            <when test='groupLevel == "LV0"'>
                <include refid="irbMainLv0Level"/>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='costType == "PSP"'>
            FROM fin_dm_opt_foi.dm_fcst_ict_psp_pbi_main_code_dim_t t1
            INNER JOIN fin_dm_opt_foi.dm_fcst_ict_psp_irb_top_spart_info_t t2
            <include refid="topIrbSpartInnerJoin"/>
        </if>
        <if test='costType == "STD"'>
            FROM fin_dm_opt_foi.dm_fcst_ict_std_pbi_main_code_dim_t t1
            INNER JOIN fin_dm_opt_foi.dm_fcst_ict_std_irb_top_spart_info_t t2
            <include refid="topIrbSpartInnerJoin"/>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='versionId!=null'>
                and t1.version_id = #{versionId}
            </if>
            <if test='bgCode!=null and bgCode!=""'>
                and t1.bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and t1.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='lv1Code!=null and lv1Code!=""'>
                and t1.lv1_prod_rnd_team_code = #{lv1Code,jdbcType=VARCHAR}
            </if>
            <if test='lv2Code!=null and lv2Code!=""'>
                and t1.lv2_prod_rnd_team_code = #{lv2Code,jdbcType=VARCHAR}
            </if>
            <if test='lv3Code!=null and lv3Code!=""'>
                and t1.lv3_prod_rnd_team_code = #{lv3Code,jdbcType=VARCHAR}
            </if>
            <if test='lv4Code!=null and lv4Code!=""'>
                and t1.lv4_prod_rnd_team_code = #{lv4Code,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!=""'>
                and t1.spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='groupLevel == "SPART"'>
                and t1.spart_code !='SNULL'
            </if>
            <if test='keyword != null and keyword != ""'>
                and group_cn_name LIKE CONCAT(CONCAT('%', #{keyword}::text ,'%'))
            </if>
            <if test='groupLevel != "LV0" and lv1CodeList != null and lv1CodeList.size() > 0'>
                <foreach collection='lv1CodeList' item="code" open="AND t1.lv1_prod_rnd_team_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv2CodeList != null and lv2CodeList.size() > 0'>
                <foreach collection='lv2CodeList' item="code" open="AND t1.lv2_prod_rnd_team_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and groupLevel != "LV2" and lv3CodeList != null and lv3CodeList.size() > 0'>
                <foreach collection='lv3CodeList' item="code" open="AND t1.lv3_prod_rnd_team_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and groupLevel != "LV2" and groupLevel != "LV3" and lv4CodeList != null and lv4CodeList.size() > 0'>
                <foreach collection='lv4CodeList' item="code" open="AND t1.lv4_prod_rnd_team_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND t1.lv1_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND t1.lv2_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getMainIrbSpartList" resultMap="resultMap">
        SELECT DISTINCT t2.lv0_prod_rnd_team_code,t2.lv0_prod_rd_team_cn_name,
        t1.lv1_prod_rnd_team_code,t1.lv1_prod_rd_team_cn_name,
        t1.lv2_prod_rnd_team_code,t1.lv2_prod_rd_team_cn_name,
        t1.lv3_prod_rnd_team_code,t1.lv3_prod_rd_team_cn_name,
        t1.lv4_prod_rnd_team_code,t1.lv4_prod_rd_team_cn_name,
        t1.spart_code,t1.spart_cn_name,#{maxLevel} AS group_level
        <if test='costType == "PSP"'>
            FROM fin_dm_opt_foi.dm_fcst_ict_psp_pbi_main_code_dim_t t1
            INNER JOIN fin_dm_opt_foi.dm_fcst_ict_psp_irb_top_spart_info_t t2
            <include refid="topIrbSpartInnerJoin"/>
        </if>
        <if test='costType == "STD"'>
            FROM fin_dm_opt_foi.dm_fcst_ict_std_pbi_main_code_dim_t t1
            INNER JOIN fin_dm_opt_foi.dm_fcst_ict_std_irb_top_spart_info_t t2
            <include refid="topIrbSpartInnerJoin"/>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='bgCode!=null and bgCode!=""'>
                and t1.bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and t1.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='versionId!=null'>
                and t1.version_id = #{versionId}
            </if>
            <if test='lv1Code!=null and lv1Code!=""'>
                and t1.lv1_prod_rnd_team_code = #{lv1Code,jdbcType=VARCHAR}
            </if>
            <if test='lv2Code!=null and lv2Code!=""'>
                and t1.lv2_prod_rnd_team_code = #{lv2Code,jdbcType=VARCHAR}
            </if>
            <if test='lv3Code!=null and lv3Code!=""'>
                and t1.lv3_prod_rnd_team_code = #{lv3Code,jdbcType=VARCHAR}
            </if>
            <if test='lv4Code!=null and lv4Code!=""'>
                and t1.lv4_prod_rnd_team_code = #{lv4Code,jdbcType=VARCHAR}
            </if>
            <if test='groupLevel == "SPART"'>
                and t1.spart_code !='SNULL'
            </if>
            <if test='lv1CodeList != null and lv1CodeList.size() > 0'>
                <foreach collection='lv1CodeList' item="code" open="AND t1.lv1_prod_rnd_team_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv2CodeList != null and lv2CodeList.size() > 0'>
                <foreach collection='lv2CodeList' item="code" open="AND t1.lv2_prod_rnd_team_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv3CodeList != null and lv3CodeList.size() > 0'>
                <foreach collection='lv3CodeList' item="code" open="AND t1.lv3_prod_rnd_team_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv4CodeList != null and lv4CodeList.size() > 0'>
                <foreach collection='lv4CodeList' item="code" open="AND t1.lv4_prod_rnd_team_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='spartCodeList != null and spartCodeList.size() > 0'>
                <foreach collection='spartCodeList' item="code" open="AND t1.spart_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND t1.lv1_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND t1.lv2_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getMainIndusDimInfoList" resultMap="resultMap">
        SELECT DISTINCT
        <choose>
            <when test='groupLevel == "SPART"'>
                <include refid="indusMainSpartLevel"/>
            </when>
            <when test='groupLevel == "LV4"'>
                <include refid="indusMainLv4Level"/>
            </when>
            <when test='groupLevel == "LV3"'>
                <include refid="indusMainLv3Level"/>
            </when>
            <when test='groupLevel == "LV2"'>
                <include refid="indusMainLv2Level"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag=="Y"'>
                <include refid="indusMainLv1HasPermission"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag=="N"'>
                <include refid="indusMainLv1NotPermission"/>
            </when>
            <when test='groupLevel == "LV0"'>
                <include refid="indusMainLv0Level"/>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='costType == "PSP"'>
            FROM fin_dm_opt_foi.dm_fcst_ict_psp_pbi_main_code_dim_t t1
            INNER JOIN fin_dm_opt_foi.dm_fcst_ict_psp_indus_top_spart_info_t t2
            <include refid="topIndusSpartInnerJoin"/>
        </if>
        <if test='costType == "STD"'>
            FROM fin_dm_opt_foi.dm_fcst_ict_std_pbi_main_code_dim_t t1
            INNER JOIN fin_dm_opt_foi.dm_fcst_ict_std_indus_top_spart_info_t t2
            <include refid="topIndusSpartInnerJoin"/>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='bgCode!=null and bgCode!=""'>
                and t1.bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='versionId!=null'>
                and t1.version_id = #{versionId,jdbcType=NUMERIC}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and t1.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='lv1Code!=null and lv1Code!=""'>
                and t1.lv1_industry_catg_code = #{lv1Code,jdbcType=VARCHAR}
            </if>
            <if test='lv2Code!=null and lv2Code!=""'>
                and t1.lv2_industry_catg_code = #{lv2Code,jdbcType=VARCHAR}
            </if>
            <if test='lv3Code!=null and lv3Code!=""'>
                and t1.lv3_industry_catg_code = #{lv3Code,jdbcType=VARCHAR}
            </if>
            <if test='lv4Code!=null and lv4Code!=""'>
                and t1.lv4_industry_catg_code = #{lv4Code,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!=""'>
                and t1.spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='groupLevel == "SPART"'>
                and t1.spart_code !='SNULL'
            </if>
            <if test='keyword != null and keyword != ""'>
                and group_cn_name LIKE CONCAT(CONCAT('%', #{keyword}::text ,'%'))
            </if>
            <if test='groupLevel != "LV0" and lv1CodeList != null and lv1CodeList.size() > 0'>
                <foreach collection='lv1CodeList' item="code" open="AND t1.lv1_industry_catg_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv2CodeList != null and lv2CodeList.size() > 0'>
                <foreach collection='lv2CodeList' item="code" open="AND t1.lv2_industry_catg_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and lv3CodeList != null and lv3CodeList.size() > 0'>
                <foreach collection='lv3CodeList' item="code" open="AND t1.lv3_industry_catg_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and groupLevel != "LV2" and groupLevel != "LV3" and lv4CodeList != null and lv4CodeList.size() > 0'>
                <foreach collection='lv4CodeList' item="code" open="AND t1.lv4_industry_catg_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND t1.lv1_industry_catg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND t1.lv2_industry_catg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getMainIndusSpartList" resultMap="resultMap">
        SELECT DISTINCT
        t2.lv0_industry_catg_code,t2.lv0_industry_catg_cn_name,
        t1.lv1_industry_catg_code,t1.lv1_industry_catg_cn_name,
        t1.lv2_industry_catg_code,t1.lv2_industry_catg_cn_name,
        t1.lv3_industry_catg_code,t1.lv3_industry_catg_cn_name,
        t1.lv4_industry_catg_code,t1.lv4_industry_catg_cn_name,
        t1.spart_code,t1.spart_cn_name, #{maxLevel} AS group_level
        <if test='costType == "PSP"'>
            FROM fin_dm_opt_foi.dm_fcst_ict_psp_pbi_main_code_dim_t t1
            INNER JOIN fin_dm_opt_foi.dm_fcst_ict_psp_indus_top_spart_info_t t2
            <include refid="topIndusSpartInnerJoin"/>
        </if>
        <if test='costType == "STD"'>
            FROM fin_dm_opt_foi.dm_fcst_ict_std_pbi_main_code_dim_t t1
            INNER JOIN fin_dm_opt_foi.dm_fcst_ict_std_indus_top_spart_info_t t2
            <include refid="topIndusSpartInnerJoin"/>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='bgCode!=null and bgCode!=""'>
                and t1.bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='versionId!=null'>
                and t1.version_id = #{versionId,jdbcType=NUMERIC}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and t1.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='lv1Code!=null and lv1Code!=""'>
                and t1.lv1_industry_catg_code = #{lv1Code,jdbcType=VARCHAR}
            </if>
            <if test='lv2Code!=null and lv2Code!=""'>
                and t1.lv2_industry_catg_code = #{lv2Code,jdbcType=VARCHAR}
            </if>
            <if test='lv3Code!=null and lv3Code!=""'>
                and t1.lv3_industry_catg_code = #{lv3Code,jdbcType=VARCHAR}
            </if>
            <if test='lv4Code!=null and lv4Code!=""'>
                and t1.lv4_industry_catg_code = #{lv4Code,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!=""'>
                and t1.spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='groupLevel == "SPART"'>
                and t1.spart_code !='SNULL'
            </if>
            <if test='lv1CodeList != null and lv1CodeList.size() > 0'>
                <foreach collection='lv1CodeList' item="code" open="AND t1.lv1_industry_catg_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv2CodeList != null and lv2CodeList.size() > 0'>
                <foreach collection='lv2CodeList' item="code" open="AND t1.lv2_industry_catg_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv3CodeList != null and lv3CodeList.size() > 0'>
                <foreach collection='lv3CodeList' item="code" open="AND t1.lv3_industry_catg_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv4CodeList != null and lv4CodeList.size() > 0'>
                <foreach collection='lv4CodeList' item="code" open="AND t1.lv4_industry_catg_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='spartCodeList != null and spartCodeList.size() > 0'>
                <foreach collection='spartCodeList' item="code" open="AND t1.spart_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND t1.lv1_industry_catg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND t1.lv2_industry_catg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getMainProdDimInfoList" resultMap="resultMap">
        select DISTINCT
        <choose>
            <when test='groupLevel == "SPART"'>
                <include refid="spartMainProdLevel"/>
            </when>
            <when test='groupLevel == "LV4"'>
                <include refid="lv4MainProdLevel"/>
            </when>
            <when test='groupLevel == "LV3"'>
                <include refid="lv3MainProdLevel"/>
            </when>
            <when test='groupLevel == "LV2"'>
                <include refid="lv2MainProdLevel"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag =="Y"'>
                <include refid="lv1MainProdLevelHasPermission"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag =="N"'>
                <include refid="lv1MainProdLevelNotPermission"/>
            </when>
            <when test='groupLevel == "LV0"'>
                <include refid="lv0MainProdLevel"/>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='costType == "PSP"'>
            FROM fin_dm_opt_foi.dm_fcst_ict_psp_pbi_main_code_dim_t t1
            INNER JOIN fin_dm_opt_foi.dm_fcst_ict_psp_prod_top_spart_info_t t2
            <include refid="topProdSpartInnerJoin"/>
        </if>
        <if test='costType == "STD"'>
            FROM fin_dm_opt_foi.dm_fcst_ict_std_pbi_main_code_dim_t t1
            INNER JOIN fin_dm_opt_foi.dm_fcst_ict_std_prod_top_spart_info_t t2
            <include refid="topProdSpartInnerJoin"/>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='bgCode!=null and bgCode!=""'>
                and t1.bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and t1.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='versionId!=null'>
                and t1.version_id = #{versionId,jdbcType=NUMERIC}
            </if>
            <if test='lv1Code!=null and lv1Code!=""'>
                and t1.lv1_prod_list_code = #{lv1Code,jdbcType=VARCHAR}
            </if>
            <if test='lv2Code!=null and lv2Code!=""'>
                and t1.lv2_prod_list_code = #{lv2Code,jdbcType=VARCHAR}
            </if>
            <if test='lv3Code!=null and lv3Code!=""'>
                and t1.lv3_prod_list_code = #{lv3Code,jdbcType=VARCHAR}
            </if>
            <if test='lv4Code!=null and lv4Code!=""'>
                and t1.lv4_prod_list_code = #{lv4Code,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!=""'>
                and t1.spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='groupLevel == "SPART"'>
                and t1.spart_code !='SNULL'
            </if>
            <if test='keyword != null and keyword != ""'>
                and group_cn_name LIKE CONCAT(CONCAT('%', #{keyword}::text ,'%'))
            </if>
            <if test='groupLevel != "LV0" and lv1CodeList != null and lv1CodeList.size() > 0'>
                <foreach collection='lv1CodeList' item="code" open="AND t1.lv1_prod_list_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv2CodeList != null and lv2CodeList.size() > 0'>
                <foreach collection='lv2CodeList' item="code" open="AND t1.lv2_prod_list_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and groupLevel != "LV2" and lv3CodeList != null and lv3CodeList.size() > 0'>
                <foreach collection='lv3CodeList' item="code" open="AND t1.lv3_prod_list_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and groupLevel != "LV2" and groupLevel != "LV3" and lv4CodeList != null and lv4CodeList.size() > 0'>
                <foreach collection='lv4CodeList' item="code" open="AND t1.lv4_prod_list_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND t1.lv1_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND t1.lv2_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getMainProdSpartList"  resultMap="resultMap">
        select DISTINCT
        t2.lv0_prod_list_code,t2.lv0_prod_list_cn_name,
        t1.lv1_prod_list_code,t1.lv1_prod_list_cn_name,
        t1.lv2_prod_list_code,t1.lv2_prod_list_cn_name,
        t1.lv3_prod_list_code,t1.lv3_prod_list_cn_name,
        t1.lv4_prod_list_code,t1.lv4_prod_list_cn_name,
        t1.spart_code,t1.spart_cn_name ,#{maxLevel} AS group_level
        <if test='costType == "PSP"'>
            FROM fin_dm_opt_foi.dm_fcst_ict_psp_pbi_main_code_dim_t t1
            INNER JOIN fin_dm_opt_foi.dm_fcst_ict_psp_prod_top_spart_info_t t2
            <include refid="topProdSpartInnerJoin"/>
        </if>
        <if test='costType == "STD"'>
            FROM fin_dm_opt_foi.dm_fcst_ict_std_pbi_main_code_dim_t t1
            INNER JOIN fin_dm_opt_foi.dm_fcst_ict_std_prod_top_spart_info_t t2
            <include refid="topProdSpartInnerJoin"/>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='bgCode!=null and bgCode!=""'>
                and t1.bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and t1.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='versionId!=null'>
                and t1.version_id = #{versionId,jdbcType=NUMERIC}
            </if>
            <if test='lv1Code!=null and lv1Code!=""'>
                and t1.lv1_prod_list_code = #{lv1Code,jdbcType=VARCHAR}
            </if>
            <if test='lv2Code!=null and lv2Code!=""'>
                and t1.lv2_prod_list_code = #{lv2Code,jdbcType=VARCHAR}
            </if>
            <if test='lv3Code!=null and lv3Code!=""'>
                and t1.lv3_prod_list_code = #{lv3Code,jdbcType=VARCHAR}
            </if>
            <if test='lv4Code!=null and lv4Code!=""'>
                and t1.lv4_prod_list_code = #{lv4Code,jdbcType=VARCHAR}
            </if>
            <if test='groupLevel == "SPART"'>
                and t1.spart_code !='SNULL'
            </if>
            <if test='lv1CodeList != null and lv1CodeList.size() > 0'>
                <foreach collection='lv1CodeList' item="code" open="AND t1.lv1_prod_list_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv2CodeList != null and lv2CodeList.size() > 0'>
                <foreach collection='lv2CodeList' item="code" open="AND t1.lv2_prod_list_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv3CodeList != null and lv3CodeList.size() > 0'>
                <foreach collection='lv3CodeList' item="code" open="AND t1.lv3_prod_list_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv4CodeList != null and lv4CodeList.size() > 0'>
                <foreach collection='lv4CodeList' item="code" open="AND t1.lv4_prod_list_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='spartCodeList != null and spartCodeList.size() > 0'>
                <foreach collection='spartCodeList' item="code" open="AND t1.spart_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND t1.lv1_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND t1.lv2_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getParentMainIrbDimInfoList" resultMap="resultMap">
        SELECT DISTINCT
        <choose>
            <when test='groupLevel == "LV4"'>
                <include refid="irbMainLv3Level"/>
            </when>
            <when test='groupLevel == "LV3"'>
                <include refid="irbMainLv2Level"/>
            </when>
            <when test='groupLevel == "LV2" and lv0Flag =="Y"'>
                <include refid="irbMainLv1HasPermission"/>
            </when>
            <when test='groupLevel == "LV2" and lv0Flag =="N"'>
                <include refid="irbMainLv1NotPermission"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag =="Y"'>
                <include refid="irbMainLv0Level"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag =="N"'>
                <include refid="irbMainLv1NotPermission"/>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='costType == "PSP"'>
            FROM fin_dm_opt_foi.dm_fcst_ict_psp_pbi_main_code_dim_t t1
            INNER JOIN fin_dm_opt_foi.dm_fcst_ict_psp_irb_top_spart_info_t t2
            <include refid="topIrbSpartInnerJoin"/>
        </if>
        <if test='costType == "STD"'>
            FROM fin_dm_opt_foi.dm_fcst_ict_std_pbi_main_code_dim_t t1
            INNER JOIN fin_dm_opt_foi.dm_fcst_ict_std_irb_top_spart_info_t t2
            <include refid="topIrbSpartInnerJoin"/>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='bgCode!=null and bgCode!=""'>
                and t1.bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and t1.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='versionId!=null'>
                and t1.version_id = #{versionId,jdbcType=NUMERIC}
            </if>
            <if test='lv1Code!=null and lv1Code!=""'>
                and t1.lv1_prod_rnd_team_code = #{lv1Code,jdbcType=VARCHAR}
            </if>
            <if test='lv2Code!=null and lv2Code!=""'>
                and t1.lv2_prod_rnd_team_code = #{lv2Code,jdbcType=VARCHAR}
            </if>
            <if test='lv3Code!=null and lv3Code!=""'>
                and t1.lv3_prod_rnd_team_code = #{lv3Code,jdbcType=VARCHAR}
            </if>
            <if test='lv4Code!=null and lv4Code!=""'>
                and t1.lv4_prod_rnd_team_code = #{lv4Code,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!=""'>
                and t1.spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='groupLevel == "SPART"'>
                and t1.spart_code !='SNULL'
            </if>
            <include refid="mainFlagCondition"/>
            <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND t1.lv1_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND t1.lv2_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getParentMainAnnualIrbDimInfoList"  resultMap="resultMap">
        select DISTINCT
        <choose>
            <when test='groupLevel == "LV4"'>
                <include refid="irbMainLv3Level"/>
            </when>
            <when test='groupLevel == "LV3"'>
                <include refid="irbMainLv2Level"/>
            </when>
            <when test='groupLevel == "LV2" and lv0Flag =="Y"'>
                <include refid="irbMainLv1HasPermission"/>
            </when>
            <when test='groupLevel == "LV2" and lv0Flag =="N"'>
                <include refid="irbMainLv1NotPermission"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag =="Y"'>
                <include refid="irbMainLv0Level"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag =="N"'>
                <include refid="irbMainLv1NotPermission"/>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='costType == "PSP"'>
            from fin_dm_opt_foi.dm_fcst_ict_psp_pbi_main_code_dim_t t1 INNER JOIN fin_dm_opt_foi.DM_FCST_ICT_PSP_IRB_DIM_INFO_T t2
            ON t2.del_flag = 'N'
            and t2.main_flag = #{mainFlag}
            and t2.region_code = #{regionCode}
            and t2.repoffice_code = #{repofficeCode}
            and t2.oversea_flag = #{overseaFlag}
            and t2.view_flag = #{viewFlag}
            and t2.software_mark = #{softwareMark}
            and nvl(t1.lv1_prod_rnd_team_code,'snull') = nvl(t2.lv1_prod_rnd_team_code,'snull')
            and nvl(t1.lv2_prod_rnd_team_code,'snull') = nvl(t2.lv2_prod_rnd_team_code,'snull')
            and nvl(t1.lv3_prod_rnd_team_code,'snull') = nvl(t2.lv3_prod_rnd_team_code,'snull')
            and nvl(t1.lv4_prod_rnd_team_code,'snull') = nvl(t2.lv4_prod_rnd_team_code,'snull')
            and nvl(t1.spart_code,'snull') = nvl(t2.spart_code,'snull')
            and t1.bg_code = t2.bg_code
            and t1.version_id = t2.version_id
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and nvl(t1.code_attributes,'snull') = nvl(t2.code_attributes,'snull')
            </if>
        </if>
        <if test='costType == "STD"'>
            from fin_dm_opt_foi.dm_fcst_ict_std_pbi_main_code_dim_t t1 INNER JOIN fin_dm_opt_foi.DM_FCST_ICT_STD_IRB_DIM_INFO_T t2
            ON t2.del_flag = 'N'
            and t2.main_flag = #{mainFlag}
            and t2.region_code = #{regionCode}
            and t2.repoffice_code = #{repofficeCode}
            and t2.oversea_flag = #{overseaFlag}
            and t2.view_flag = #{viewFlag}
            and nvl(t1.lv1_prod_rnd_team_code,'snull') = nvl(t2.lv1_prod_rnd_team_code,'snull')
            and nvl(t1.lv2_prod_rnd_team_code,'snull') = nvl(t2.lv2_prod_rnd_team_code,'snull')
            and nvl(t1.lv3_prod_rnd_team_code,'snull') = nvl(t2.lv3_prod_rnd_team_code,'snull')
            and nvl(t1.lv4_prod_rnd_team_code,'snull') = nvl(t2.lv4_prod_rnd_team_code,'snull')
            and nvl(t1.spart_code,'snull') = nvl(t2.spart_code,'snull')
            and t1.bg_code = t2.bg_code
            and t1.version_id = t2.version_id
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and nvl(t1.code_attributes,'snull') = nvl(t2.code_attributes,'snull')
            </if>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='bgCode!=null and bgCode!=""'>
                and t1.bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and t1.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='versionId!=null'>
                and t1.version_id = #{versionId,jdbcType=NUMERIC}
            </if>
            <include refid="mainFlagCondition"/>
            <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND t1.lv1_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND t1.lv2_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getParentMainIndusDimInfoList" resultMap="resultMap">
        SELECT DISTINCT
        <choose>
            <when test='groupLevel == "LV4"'>
                <include refid="indusMainLv3Level"/>
            </when>
            <when test='groupLevel == "LV3"'>
                <include refid="indusMainLv2Level"/>
            </when>
            <when test='groupLevel == "LV2" and lv0Flag=="Y"'>
                <include refid="indusMainLv1HasPermission"/>
            </when>
            <when test='groupLevel == "LV2" and lv0Flag=="N"'>
                <include refid="indusMainLv1NotPermission"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag=="Y"'>
                <include refid="indusMainLv0Level"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag=="N"'>
                <include refid="indusMainLv1NotPermission"/>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='costType == "PSP"'>
            FROM fin_dm_opt_foi.dm_fcst_ict_psp_pbi_main_code_dim_t t1
            INNER JOIN fin_dm_opt_foi.dm_fcst_ict_psp_indus_top_spart_info_t t2
            <include refid="topIndusSpartInnerJoin"/>
        </if>
        <if test='costType == "STD"'>
            FROM fin_dm_opt_foi.dm_fcst_ict_std_pbi_main_code_dim_t t1
            INNER JOIN fin_dm_opt_foi.dm_fcst_ict_std_indus_top_spart_info_t t2
            <include refid="topIndusSpartInnerJoin"/>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='bgCode!=null and bgCode!=""'>
                and t1.bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='versionId!=null'>
                and t1.version_id = #{versionId,jdbcType=NUMERIC}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and t1.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='lv1Code!=null and lv1Code!=""'>
                and t1.lv1_industry_catg_code = #{lv1Code,jdbcType=VARCHAR}
            </if>
            <if test='lv2Code!=null and lv2Code!=""'>
                and t1.lv2_industry_catg_code = #{lv2Code,jdbcType=VARCHAR}
            </if>
            <if test='lv3Code!=null and lv3Code!=""'>
                and t1.lv3_industry_catg_code = #{lv3Code,jdbcType=VARCHAR}
            </if>
            <if test='lv4Code!=null and lv4Code!=""'>
                and t1.lv4_industry_catg_code = #{lv4Code,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!=""'>
                and t1.spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='groupLevel == "SPART"'>
                and t1.spart_code !='SNULL'
            </if>
            <if test='groupLevel !="LV0" and lv1CodeList != null and lv1CodeList.size() > 0'>
                <foreach collection='lv1CodeList' item="code" open="AND t1.lv1_industry_catg_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel !="LV0" and groupLevel !="LV1" and lv2CodeList != null and lv2CodeList.size() > 0'>
                <foreach collection='lv2CodeList' item="code" open="AND t1.lv2_industry_catg_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel !="LV0" and groupLevel !="LV1" and groupLevel !="LV2" and lv3CodeList != null and lv3CodeList.size() > 0'>
                <foreach collection='lv3CodeList' item="code" open="AND t1.lv3_industry_catg_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel !="LV0" and groupLevel !="LV1" and groupLevel !="LV2" and groupLevel !="LV3" and lv4CodeList != null and lv4CodeList.size() > 0'>
                <foreach collection='lv4CodeList' item="code" open="AND t1.lv4_industry_catg_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND t1.lv1_industry_catg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND t1.lv2_industry_catg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getParentMainAnnualIndusDimInfoList"  resultMap="resultMap">
        select DISTINCT
        <choose>
            <when test='groupLevel == "LV4"'>
                <include refid="indusMainLv3Level"/>
            </when>
            <when test='groupLevel == "LV3"'>
                <include refid="indusMainLv2Level"/>
            </when>
            <when test='groupLevel == "LV2" and lv0Flag=="Y"'>
                <include refid="indusMainLv1HasPermission"/>
            </when>
            <when test='groupLevel == "LV2" and lv0Flag=="N"'>
                <include refid="indusMainLv1NotPermission"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag=="Y"'>
                <include refid="indusMainLv0Level"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag=="N"'>
                <include refid="indusMainLv1NotPermission"/>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='costType == "PSP"'>
            from fin_dm_opt_foi.dm_fcst_ict_psp_pbi_main_code_dim_t t1 INNER JOIN fin_dm_opt_foi.DM_FCST_ICT_PSP_INDUS_DIM_INFO_T t2
            ON t2.del_flag = 'N'
            and t2.main_flag = #{mainFlag}
            and t2.region_code = #{regionCode}
            and t2.repoffice_code = #{repofficeCode}
            and t2.oversea_flag = #{overseaFlag}
            and t2.view_flag = #{viewFlag}
            and t2.software_mark = #{softwareMark}
            and nvl(t1.lv1_industry_catg_code,'snull') = nvl(t2.lv1_industry_catg_code,'snull')
            and nvl(t1.lv2_industry_catg_code,'snull') = nvl(t2.lv2_industry_catg_code,'snull')
            and nvl(t1.lv3_industry_catg_code,'snull') = nvl(t2.lv3_industry_catg_code,'snull')
            and nvl(t1.lv4_industry_catg_code,'snull') = nvl(t2.lv4_industry_catg_code,'snull')
            and nvl(t1.spart_code,'snull') = nvl(t2.spart_code,'snull')
            and t1.bg_code = t2.bg_code
            and t1.version_id = t2.version_id
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and nvl(t1.code_attributes,'snull') = nvl(t2.code_attributes,'snull')
            </if>
        </if>
        <if test='costType == "STD"'>
            from fin_dm_opt_foi.dm_fcst_ict_std_pbi_main_code_dim_t t1 INNER JOIN fin_dm_opt_foi.DM_FCST_ICT_STD_INDUS_DIM_INFO_T t2
            ON t2.del_flag = 'N'
            and t2.main_flag = #{mainFlag}
            and t2.region_code = #{regionCode}
            and t2.repoffice_code = #{repofficeCode}
            and t2.oversea_flag = #{overseaFlag}
            and t2.view_flag = #{viewFlag}
            and t2.software_mark = #{softwareMark}
            and nvl(t1.lv1_industry_catg_code,'snull') = nvl(t2.lv1_industry_catg_code,'snull')
            and nvl(t1.lv2_industry_catg_code,'snull') = nvl(t2.lv2_industry_catg_code,'snull')
            and nvl(t1.lv3_industry_catg_code,'snull') = nvl(t2.lv3_industry_catg_code,'snull')
            and nvl(t1.lv4_industry_catg_code,'snull') = nvl(t2.lv4_industry_catg_code,'snull')
            and nvl(t1.spart_code,'snull') = nvl(t2.spart_code,'snull')
            and t1.bg_code = t2.bg_code
            and t1.version_id = t2.version_id
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and nvl(t1.code_attributes,'snull') = nvl(t2.code_attributes,'snull')
            </if>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='bgCode!=null and bgCode!=""'>
                and t1.bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='versionId!=null'>
                and t1.version_id = #{versionId,jdbcType=NUMERIC}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and t1.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <include refid="mainFlagCondition"/>
            <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND t1.lv1_industry_catg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND t1.lv2_industry_catg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getParentMainProdDimInfoList" resultMap="resultMap">
        select DISTINCT
        <choose>
            <when test='groupLevel == "LV4"'>
                <include refid="lv3MainProdLevel"/>
            </when>
            <when test='groupLevel == "LV3"'>
                <include refid="lv2MainProdLevel"/>
            </when>
            <when test='groupLevel == "LV2" and lv0Flag =="Y"'>
                <include refid="lv1MainProdLevelHasPermission"/>
            </when>
            <when test='groupLevel == "LV2" and lv0Flag =="N"'>
                <include refid="lv1MainProdLevelNotPermission"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag =="Y"'>
                <include refid="lv0MainProdLevel"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag =="N"'>
                <include refid="lv1MainProdLevelNotPermission"/>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='costType == "PSP"'>
            FROM fin_dm_opt_foi.dm_fcst_ict_psp_pbi_main_code_dim_t t1
            INNER JOIN fin_dm_opt_foi.dm_fcst_ict_psp_prod_top_spart_info_t t2
            <include refid="topProdSpartInnerJoin"/>
        </if>
        <if test='costType == "STD"'>
            FROM fin_dm_opt_foi.dm_fcst_ict_std_pbi_main_code_dim_t t1
            INNER JOIN fin_dm_opt_foi.dm_fcst_ict_std_prod_top_spart_info_t t2
            <include refid="topProdSpartInnerJoin"/>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='bgCode!=null and bgCode!=""'>
                and t1.bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and t1.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='versionId!=null'>
                and t1.version_id = #{versionId,jdbcType=NUMERIC}
            </if>
            <if test='lv1Code!=null and lv1Code!=""'>
                and t1.lv1_prod_list_code = #{lv1Code,jdbcType=VARCHAR}
            </if>
            <if test='lv2Code!=null and lv2Code!=""'>
                and t1.lv2_prod_list_code = #{lv2Code,jdbcType=VARCHAR}
            </if>
            <if test='lv3Code!=null and lv3Code!=""'>
                and t1.lv3_prod_list_code = #{lv3Code,jdbcType=VARCHAR}
            </if>
            <if test='lv4Code!=null and lv4Code!=""'>
                and t1.lv4_prod_list_code = #{lv4Code,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!=""'>
                and t1.spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='groupLevel == "SPART"'>
                and t1.spart_code !='SNULL'
            </if>
            <if test='groupLevel !="LV0" and lv1CodeList != null and lv1CodeList.size() > 0'>
                <foreach collection='lv1CodeList' item="code" open="AND t1.lv1_prod_list_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel !="LV0" and groupLevel !="LV1" and lv2CodeList != null and lv2CodeList.size() > 0'>
                <foreach collection='lv2CodeList' item="code" open="AND t1.lv2_prod_list_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel !="LV0" and groupLevel !="LV1" and groupLevel !="LV2" and lv3CodeList != null and lv3CodeList.size() > 0'>
                <foreach collection='lv3CodeList' item="code" open="AND t1.lv3_prod_list_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel !="LV0" and groupLevel !="LV1" and groupLevel !="LV2" and groupLevel !="LV3" and lv4CodeList != null and lv4CodeList.size() > 0'>
                <foreach collection='lv4CodeList' item="code" open="AND t1.lv4_prod_list_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND t1.lv1_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND t1.lv2_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getParentMainAnnualProdDimInfoList" resultMap="resultMap">
        select DISTINCT
        <choose>
            <when test='groupLevel == "LV4"'>
                <include refid="lv3MainProdLevel"/>
            </when>
            <when test='groupLevel == "LV3"'>
                <include refid="lv2MainProdLevel"/>
            </when>
            <when test='groupLevel == "LV2" and lv0Flag =="Y"'>
                <include refid="lv1MainProdLevelHasPermission"/>
            </when>
            <when test='groupLevel == "LV2" and lv0Flag =="N"'>
                <include refid="lv1MainProdLevelNotPermission"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag =="Y"'>
                <include refid="lv0MainProdLevel"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag =="N"'>
                <include refid="lv1MainProdLevelNotPermission"/>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='costType == "PSP"'>
            from fin_dm_opt_foi.dm_fcst_ict_psp_pbi_main_code_dim_t t1 INNER JOIN fin_dm_opt_foi.DM_FCST_ICT_PSP_PROD_DIM_INFO_T t2
            ON t2.del_flag = 'N'
            and t2.main_flag = #{mainFlag}
            and t2.region_code = #{regionCode}
            and t2.repoffice_code = #{repofficeCode}
            and t2.oversea_flag = #{overseaFlag}
            and t2.view_flag = #{viewFlag}
            and t2.software_mark = #{softwareMark}
            and nvl(t1.lv1_prod_list_code,'snull') = nvl(t2.lv1_prod_list_code,'snull')
            and nvl(t1.lv2_prod_list_code,'snull') = nvl(t2.lv2_prod_list_code,'snull')
            and nvl(t1.lv3_prod_list_code,'snull') = nvl(t2.lv3_prod_list_code,'snull')
            and nvl(t1.lv4_prod_list_code,'snull') = nvl(t2.lv4_prod_list_code,'snull')
            and nvl(t1.spart_code,'snull') = nvl(t2.spart_code,'snull')
            and t1.bg_code = t2.bg_code
            and t1.version_id = t2.version_id
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and nvl(t1.code_attributes,'snull') = nvl(t2.code_attributes,'snull')
            </if>
        </if>
        <if test='costType == "STD"'>
            from fin_dm_opt_foi.dm_fcst_ict_std_pbi_main_code_dim_t t1 INNER JOIN fin_dm_opt_foi.DM_FCST_ICT_STD_PROD_DIM_INFO_T t2
            ON t2.del_flag = 'N'
            and t2.main_flag = #{mainFlag}
            and t2.region_code = #{regionCode}
            and t2.repoffice_code = #{repofficeCode}
            and t2.oversea_flag = #{overseaFlag}
            and t2.view_flag = #{viewFlag}
            and t2.software_mark = #{softwareMark}
            and nvl(t1.lv1_prod_list_code,'snull') = nvl(t2.lv1_prod_list_code,'snull')
            and nvl(t1.lv2_prod_list_code,'snull') = nvl(t2.lv2_prod_list_code,'snull')
            and nvl(t1.lv3_prod_list_code,'snull') = nvl(t2.lv3_prod_list_code,'snull')
            and nvl(t1.lv4_prod_list_code,'snull') = nvl(t2.lv4_prod_list_code,'snull')
            and nvl(t1.spart_code,'snull') = nvl(t2.spart_code,'snull')
            and t1.bg_code = t2.bg_code
            and t1.version_id = t2.version_id
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and nvl(t1.code_attributes,'snull') = nvl(t2.code_attributes,'snull')
            </if>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='bgCode!=null and bgCode!=""'>
                and t1.bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and t1.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='versionId!=null'>
                and t1.version_id = #{versionId,jdbcType=NUMERIC}
            </if>
            <include refid="mainFlagCondition"/>
            <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND t1.lv1_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND t1.lv2_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getParentIrbDimInfoList" resultMap="resultMap">
        select DISTINCT
        <choose>
            <when test='groupLevel == "LV4"'>
                <include refid="lv3IrbLevel"/>
            </when>
            <when test='groupLevel == "LV3"'>
                <include refid="lv2IrbLevel"/>
            </when>
            <when test='groupLevel == "LV2" and lv0Flag =="Y"'>
                <include refid="lv1IrbHasPermission"/>
            </when>
            <when test='groupLevel == "LV2" and lv0Flag =="N"'>
                <include refid="lv1IrbNotPermission"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag =="Y"'>
                <include refid="lv0IrbLevel"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag =="N"'>
                <include refid="lv1IrbNotPermission"/>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='costType == "PSP"'>
            <choose>
                <when test='pageFlag == "ANNUAL"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_irb_dim_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag == "PROD_SPART"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_irb_top_spart_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag == "DIMENSION"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_irb_dim_info_t
                </when>
            </choose>
        </if>
        <if test='costType == "STD"'>
            <choose>
                <when test='pageFlag == "ANNUAL"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_irb_dim_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag == "PROD_SPART"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_irb_top_spart_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag == "DIMENSION"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_irb_dim_info_t
                </when>
            </choose>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='pageFlag == "MONTH" and viewFlag == "PROD_SPART"'>
                and is_top_flag ='Y'
                and double_flag = 'Y'
            </if>
            <if test='regionCode!=null and regionCode!=""'>
                and region_code = #{regionCode,jdbcType=VARCHAR}
            </if>
            <if test='repofficeCode!=null and repofficeCode!=""'>
                and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='bgCode!=null and bgCode!=""'>
                and bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='overseaFlag!=null and overseaFlag!=""'>
                and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='viewFlag!=null and viewFlag!=""'>
                and view_flag = #{viewFlag,jdbcType=VARCHAR}
            </if>
            <if test='mainFlag!=null and mainFlag!=""'>
                and main_flag = #{mainFlag,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='versionId!=null '>
                <choose>
                    <when test='pageFlag == "MONTH" and viewFlag == "PROD_SPART"'>
                        and version_id = #{monVersionId}
                    </when>
                    <otherwise>
                        and version_id = #{versionId}
                    </otherwise>
                </choose>
            </if>
            <if test='pageFlag == "ANNUAL" and groupLevel !=null and groupLevel !=""'>
                and group_level = #{groupLevel,jdbcType=VARCHAR}
            </if>
            <if test='pageFlag == "MONTH" and viewFlag == "DIMENSION"  and groupLevel!=null and groupLevel!=""'>
                and group_level = #{groupLevel,jdbcType=VARCHAR}
            </if>
            <if test='lv0Code!=null and lv0Code!=""'>
                and lv0_prod_rnd_team_code = #{lv0Code,jdbcType=VARCHAR}
            </if>
            <if test='lv1Code!=null and lv1Code!=""'>
                and lv1_prod_rnd_team_code = #{lv1Code,jdbcType=VARCHAR}
            </if>
            <if test='lv2Code!=null and lv2Code!=""'>
                and lv2_prod_rnd_team_code = #{lv2Code,jdbcType=VARCHAR}
            </if>
            <if test='lv3Code!=null and lv3Code!=""'>
                and lv3_prod_rnd_team_code = #{lv3Code,jdbcType=VARCHAR}
            </if>
            <if test='lv4Code!=null and lv4Code!=""'>
                and lv4_prod_rnd_team_code = #{lv4Code,jdbcType=VARCHAR}
            </if>
            <if test='dimensionCode!=null and dimensionCode!=""'>
                and dimension_code = #{dimensionCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubCategoryCode!=null and dimensionSubCategoryCode!=""'>
                and dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubDetailCode!=null and dimensionSubDetailCode!=""'>
                and dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!="" and pageFlag == "ANNUAL"'>
                and spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!="" and pageFlag == "MONTH"'>
                and top_spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='groupLevel == "SPART" and pageFlag == "ANNUAL"'>
                and spart_code !='SNULL'
            </if>
            <if test='groupLevel == "SPART" and pageFlag == "MONTH"'>
                and top_spart_code !='SNULL'
            </if>
            <if test='groupLevel == "SUB_DETAIL"'>
                and dimension_sub_detail_code !='SNULL'
            </if>
            <if test='groupLevel !="LV0" and lv1CodeList != null and lv1CodeList.size() > 0'>
                <foreach collection='lv1CodeList' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel !="LV0" and groupLevel !="LV1" and lv2CodeList != null and lv2CodeList.size() > 0'>
                <foreach collection='lv2CodeList' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel !="LV0" and groupLevel !="LV1" and groupLevel !="LV2" and lv3CodeList != null and lv3CodeList.size() > 0'>
                <foreach collection='lv3CodeList' item="code" open="AND lv3_prod_rnd_team_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel !="LV0" and groupLevel !="LV1" and groupLevel !="LV2" and groupLevel !="LV3" and lv4CodeList != null and lv4CodeList.size() > 0'>
                <foreach collection='lv4CodeList' item="code" open="AND lv4_prod_rnd_team_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getParentIndusDimInfoList" resultMap="resultMap">
        select DISTINCT
        <choose>
            <when test='groupLevel == "LV4"'>
                <include refid="lv3IndusLevel"/>
            </when>
            <when test='groupLevel == "LV3"'>
                <include refid="lv2IndusLevel"/>
            </when>
            <when test='groupLevel == "LV2" and lv0Flag=="Y"'>
                <include refid="lv1IndusLevelHasPermission"/>
            </when>
            <when test='groupLevel == "LV2" and lv0Flag=="N"'>
                <include refid="lv1IndusLevelNotPermission"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag=="Y"'>
                <include refid="lv0IndusLevel"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag=="N"'>
                <include refid="lv1IndusLevelNotPermission"/>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='costType == "PSP"'>
            <choose>
                <when test='pageFlag == "ANNUAL"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_indus_dim_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag =="PROD_SPART"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_indus_top_spart_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag =="DIMENSION"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_indus_dim_info_t
                </when>
            </choose>
        </if>
        <if test='costType == "STD"'>
            <choose>
                <when test='pageFlag == "ANNUAL"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_indus_dim_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag =="PROD_SPART"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_indus_top_spart_info_t
                </when>
                <when test='pageFlag == "MONTH" and viewFlag =="DIMENSION"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_indus_dim_info_t
                </when>
            </choose>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='pageFlag == "MONTH" and viewFlag == "PROD_SPART"'>
                and is_top_flag ='Y'
                and double_flag = 'Y'
            </if>
            <if test='regionCode!=null and regionCode!=""'>
                and region_code = #{regionCode,jdbcType=VARCHAR}
            </if>
            <if test='repofficeCode!=null and repofficeCode!=""'>
                and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='bgCode!=null and bgCode!=""'>
                and bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='overseaFlag!=null and overseaFlag!=""'>
                and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='viewFlag!=null and viewFlag!=""'>
                and view_flag = #{viewFlag,jdbcType=VARCHAR}
            </if>
            <if test='mainFlag!=null and mainFlag!=""'>
                and main_flag = #{mainFlag,jdbcType=VARCHAR}
            </if>
            <if test='versionId!=null '>
                <choose>
                    <when test='pageFlag == "MONTH" and viewFlag == "PROD_SPART"'>
                        and version_id = #{monVersionId}
                    </when>
                    <otherwise>
                        and version_id = #{versionId}
                    </otherwise>
                </choose>
            </if>
            <if test='pageFlag == "ANNUAL" and groupLevel!=null and groupLevel!="" '>
                and group_level = #{groupLevel,jdbcType=VARCHAR}
            </if>
            <if test='pageFlag == "MONTH" and viewFlag == "DIMENSION" and groupLevel!=null and groupLevel!=""'>
                and group_level = #{groupLevel,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='lv0Code!=null and lv0Code!=""'>
                and lv0_industry_catg_code = #{lv0Code,jdbcType=VARCHAR}
            </if>
            <if test='lv1Code!=null and lv1Code!=""'>
                and lv1_industry_catg_code = #{lv1Code,jdbcType=VARCHAR}
            </if>
            <if test='lv2Code!=null and lv2Code!=""'>
                and lv2_industry_catg_code = #{lv2Code,jdbcType=VARCHAR}
            </if>
            <if test='lv3Code!=null and lv3Code!=""'>
                and lv3_industry_catg_code = #{lv3Code,jdbcType=VARCHAR}
            </if>
            <if test='lv4Code!=null and lv4Code!=""'>
                and lv4_industry_catg_code = #{lv4Code,jdbcType=VARCHAR}
            </if>
            <if test='dimensionCode!=null and dimensionCode!=""'>
                and dimension_code = #{dimensionCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubCategoryCode!=null and dimensionSubCategoryCode!=""'>
                and dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubDetailCode!=null and dimensionSubDetailCode!=""'>
                and dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!="" and pageFlag == "ANNUAL"'>
                and spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!="" and pageFlag == "MONTH"'>
                and top_spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='groupLevel == "SPART" and pageFlag == "ANNUAL"'>
                and spart_code !='SNULL'
            </if>
            <if test='groupLevel == "SPART" and pageFlag != "ANNUAL"'>
                and top_spart_code !='SNULL'
            </if>
            <if test='groupLevel == "SUB_DETAIL"'>
                and dimension_sub_detail_code !='SNULL'
            </if>
            <if test='groupLevel !="LV0" and lv1CodeList != null and lv1CodeList.size() > 0'>
                <foreach collection='lv1CodeList' item="code" open="AND lv1_industry_catg_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel !="LV0" and groupLevel !="LV1" and lv2CodeList != null and lv2CodeList.size() > 0'>
                <foreach collection='lv2CodeList' item="code" open="AND lv2_industry_catg_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel !="LV0" and groupLevel !="LV1" and groupLevel !="LV2" and lv3CodeList != null and lv3CodeList.size() > 0'>
                <foreach collection='lv3CodeList' item="code" open="AND lv3_industry_catg_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel !="LV0" and groupLevel !="LV1" and groupLevel !="LV2" and groupLevel !="LV3" and lv4CodeList != null and lv4CodeList.size() > 0'>
                <foreach collection='lv4CodeList' item="code" open="AND lv4_industry_catg_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND lv1_industry_catg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND lv2_industry_catg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getParentProdDimInfoList"  resultMap="resultMap">
        select DISTINCT
        <choose>
            <when test='groupLevel == "LV4"'>
                <include refid="lv3ProdLevel"/>
            </when>
            <when test='groupLevel == "LV3"'>
                <include refid="lv2ProdLevel"/>
            </when>
            <when test='groupLevel == "LV2" and lv0Flag =="Y"'>
                <include refid="lv1ProdLevelHasPermission"/>
            </when>
            <when test='groupLevel == "LV2" and lv0Flag =="N"'>
                <include refid="lv1ProdLevelNotPermission"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag =="N"'>
                <include refid="lv1ProdLevelNotPermission"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag =="Y"'>
                <include refid="lv0ProdLevel"/>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='costType == "PSP"'>
            <choose>
                <when test='pageFlag == "ANNUAL"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_dim_info_t t1
                </when>
                <when test='pageFlag == "MONTH" and viewFlag =="PROD_SPART"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_top_spart_info_t t1
                </when>
                <when test='pageFlag == "MONTH" and viewFlag =="DIMENSION"'>
                    from fin_dm_opt_foi.dm_fcst_ict_psp_prod_dim_info_t t1
                </when>
            </choose>
        </if>
        <if test='costType == "STD"'>
            <choose>
                <when test='pageFlag == "ANNUAL"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_dim_info_t t1
                </when>
                <when test='pageFlag == "MONTH" and viewFlag =="PROD_SPART"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_top_spart_info_t t1
                </when>
                <when test='pageFlag == "MONTH" and viewFlag =="DIMENSION"'>
                    from fin_dm_opt_foi.dm_fcst_ict_std_prod_dim_info_t t1
                </when>
            </choose>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='pageFlag == "MONTH" and viewFlag == "PROD_SPART"'>
                and is_top_flag ='Y'
                and double_flag = 'Y'
            </if>
            <if test='regionCode!=null and regionCode!=""'>
                and region_code = #{regionCode,jdbcType=VARCHAR}
            </if>
            <if test='repofficeCode!=null and repofficeCode!=""'>
                and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
            </if>
            <if test='bgCode!=null and bgCode!=""'>
                and bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='overseaFlag!=null and overseaFlag!=""'>
                and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
            </if>
            <if test='viewFlag!=null and viewFlag!=""'>
                and view_flag = #{viewFlag,jdbcType=VARCHAR}
            </if>
            <if test='mainFlag!=null and mainFlag!=""'>
                and main_flag = #{mainFlag,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='versionId!=null '>
                <choose>
                    <when test='pageFlag == "MONTH" and viewFlag == "PROD_SPART"'>
                        and version_id = #{monVersionId}
                    </when>
                    <otherwise>
                        and version_id = #{versionId}
                    </otherwise>
                </choose>
            </if>
            <if test='pageFlag == "ANNUAL" and groupLevel!=null and groupLevel!="" and groupLevel !="CODE"'>
                and group_level = #{groupLevel,jdbcType=VARCHAR}
            </if>
            <if test='pageFlag == "MONTH" and viewFlag == "DIMENSION"  and groupLevel!=null and groupLevel!="" and groupLevel !="CODE"'>
                and group_level = #{groupLevel,jdbcType=VARCHAR}
            </if>
            <if test='lv0Code!=null and lv0Code!=""'>
                and lv0_prod_list_code = #{lv0Code,jdbcType=VARCHAR}
            </if>
            <if test='lv1Code!=null and lv1Code!=""'>
                and lv1_prod_list_code = #{lv1Code,jdbcType=VARCHAR}
            </if>
            <if test='lv2Code!=null and lv2Code!=""'>
                and lv2_prod_list_code = #{lv2Code,jdbcType=VARCHAR}
            </if>
            <if test='lv3Code!=null and lv3Code!=""'>
                and lv3_prod_list_code = #{lv3Code,jdbcType=VARCHAR}
            </if>
            <if test='lv4Code!=null and lv4Code!=""'>
                and lv4_prod_list_code = #{lv4Code,jdbcType=VARCHAR}
            </if>
            <if test='dimensionCode!=null and dimensionCode!=""'>
                and dimension_code = #{dimensionCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubCategoryCode!=null and dimensionSubCategoryCode!=""'>
                and dimension_subCategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubDetailCode!=null and dimensionSubDetailCode!=""'>
                and dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!="" and pageFlag == "ANNUAL"'>
                and spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!="" and pageFlag != "ANNUAL"'>
                and top_spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='groupLevel == "SPART" and pageFlag == "ANNUAL"'>
                and spart_code !='SNULL'
            </if>
            <if test='groupLevel == "SPART" and pageFlag != "ANNUAL"'>
                and top_spart_code !='SNULL'
            </if>
            <if test='groupLevel == "SUB_DETAIL"'>
                and dimension_sub_detail_code !='SNULL'
            </if>
            <if test='groupLevel !="LV0" and lv1CodeList != null and lv1CodeList.size() > 0'>
                <foreach collection='lv1CodeList' item="code" open="AND lv1_prod_list_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel !="LV0" and groupLevel !="LV1" and lv2CodeList != null and lv2CodeList.size() > 0'>
                <foreach collection='lv2CodeList' item="code" open="AND lv2_prod_list_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel !="LV0" and groupLevel !="LV1" and groupLevel !="LV2" and lv3CodeList != null and lv3CodeList.size() > 0'>
                <foreach collection='lv3CodeList' item="code" open="AND lv3_prod_list_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel !="LV0" and groupLevel !="LV1" and groupLevel !="LV2" and groupLevel !="LV3" and lv4CodeList != null and lv4CodeList.size() > 0'>
                <foreach collection='lv4CodeList' item="code" open="AND lv4_prod_list_code IN (" close=")"
                         index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND lv1_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND lv2_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getMainAnnualIrbDimInfoList"  resultMap="resultMap">
        select DISTINCT
        <choose>
            <when test='groupLevel == "SPART"'>
                <include refid="irbMainSpartLevel"/>
            </when>
            <when test='groupLevel == "LV4"'>
                <include refid="irbMainLV4Level"/>
            </when>
            <when test='groupLevel == "LV3"'>
                <include refid="irbMainLv3Level"/>
            </when>
            <when test='groupLevel == "LV2"'>
                <include refid="irbMainLv2Level"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag =="Y"'>
                <include refid="irbMainLv1HasPermission"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag =="N"'>
                <include refid="irbMainLv1NotPermission"/>
            </when>
            <when test='groupLevel == "LV0"'>
                <include refid="irbMainLv0Level"/>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='costType == "PSP"'>
            from fin_dm_opt_foi.dm_fcst_ict_psp_pbi_main_code_dim_t t1 INNER JOIN fin_dm_opt_foi.DM_FCST_ICT_PSP_IRB_DIM_INFO_T t2
            ON t2.del_flag = 'N'
            and t2.main_flag = #{mainFlag}
            and t2.region_code = #{regionCode}
            and t2.repoffice_code = #{repofficeCode}
            and t2.oversea_flag = #{overseaFlag}
            and t2.view_flag = #{viewFlag}
            and t2.software_mark = #{softwareMark}
            and nvl(t1.lv1_prod_rnd_team_code,'snull') = nvl(t2.lv1_prod_rnd_team_code,'snull')
            and nvl(t1.lv2_prod_rnd_team_code,'snull') = nvl(t2.lv2_prod_rnd_team_code,'snull')
            and nvl(t1.lv3_prod_rnd_team_code,'snull') = nvl(t2.lv3_prod_rnd_team_code,'snull')
            and nvl(t1.lv4_prod_rnd_team_code,'snull') = nvl(t2.lv4_prod_rnd_team_code,'snull')
            and nvl(t1.spart_code,'snull') = nvl(t2.spart_code,'snull')
            and t1.bg_code = t2.bg_code
            and t1.version_id = t2.version_id
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and nvl(t1.code_attributes,'snull') = nvl(t2.code_attributes,'snull')
            </if>
        </if>
        <if test='costType == "STD"'>
            from fin_dm_opt_foi.dm_fcst_ict_std_pbi_main_code_dim_t t1 INNER JOIN fin_dm_opt_foi.DM_FCST_ICT_STD_IRB_DIM_INFO_T t2
            ON t2.del_flag = 'N'
            and t2.main_flag = #{mainFlag}
            and t2.region_code = #{regionCode}
            and t2.repoffice_code = #{repofficeCode}
            and t2.oversea_flag = #{overseaFlag}
            and t2.view_flag = #{viewFlag}
            and nvl(t1.lv1_prod_rnd_team_code,'snull') = nvl(t2.lv1_prod_rnd_team_code,'snull')
            and nvl(t1.lv2_prod_rnd_team_code,'snull') = nvl(t2.lv2_prod_rnd_team_code,'snull')
            and nvl(t1.lv3_prod_rnd_team_code,'snull') = nvl(t2.lv3_prod_rnd_team_code,'snull')
            and nvl(t1.lv4_prod_rnd_team_code,'snull') = nvl(t2.lv4_prod_rnd_team_code,'snull')
            and nvl(t1.spart_code,'snull') = nvl(t2.spart_code,'snull')
            and t1.bg_code = t2.bg_code
            and t1.version_id = t2.version_id
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and nvl(t1.code_attributes,'snull') = nvl(t2.code_attributes,'snull')
            </if>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='bgCode!=null and bgCode!=""'>
                and t1.bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and t1.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='versionId!=null'>
                and t1.version_id = #{versionId,jdbcType=NUMERIC}
            </if>
            <if test='groupLevel != "LV0" and lv1CodeList != null and lv1CodeList.size() > 0'>
                <foreach collection='lv1CodeList' item="code" open="AND t1.lv1_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv2CodeList != null and lv2CodeList.size() > 0'>
                <foreach collection='lv2CodeList' item="code" open="AND t1.lv2_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and groupLevel != "LV2" and lv3CodeList != null and lv3CodeList.size() > 0'>
                <foreach collection='lv3CodeList' item="code" open="AND t1.lv3_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and groupLevel != "LV2" and groupLevel != "LV3" and lv4CodeList != null and lv4CodeList.size() > 0'>
                <foreach collection='lv4CodeList' item="code" open="AND t1.lv4_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv1Code!=null and lv1Code!=""'>
                and t1.lv1_prod_rnd_team_code = #{lv1Code,jdbcType=VARCHAR}
            </if>
            <if test='lv2Code!=null and lv2Code!=""'>
                and t1.lv2_prod_rnd_team_code = #{lv2Code,jdbcType=VARCHAR}
            </if>
            <if test='lv3Code!=null and lv3Code!=""'>
                and t1.lv3_prod_rnd_team_code = #{lv3Code,jdbcType=VARCHAR}
            </if>
            <if test='lv4Code!=null and lv4Code!=""'>
                and t1.lv4_prod_rnd_team_code = #{lv4Code,jdbcType=VARCHAR}
            </if>
            <if test='dimensionCode!=null and dimensionCode!=""'>
                and t1.dimension_code = #{dimensionCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubCategoryCode!=null and dimensionSubCategoryCode!=""'>
                and t1.dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubDetailCode!=null and dimensionSubDetailCode!=""'>
                and t1.dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!=""'>
                and t1.spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='groupLevel == "SPART"'>
                and t1.spart_code !='SNULL'
            </if>
            <if test='groupLevel == "SUB_DETAIL"'>
                and t1.dimension_sub_detail_code !='SNULL'
            </if>
            <if test='keyword != null and keyword != ""'>
                and group_cn_name LIKE CONCAT(CONCAT('%', #{keyword}::text ,'%'))
            </if>
            <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND t1.lv1_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND t1.lv2_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getMainAnnualIrbSpartList"  resultMap="resultMap">
        select DISTINCT
        t2.lv0_prod_rnd_team_code,t2.lv0_prod_rd_team_cn_name,
        t1.lv1_prod_rnd_team_code,t1.lv1_prod_rd_team_cn_name,
        t1.lv2_prod_rnd_team_code,t1.lv2_prod_rd_team_cn_name,
        t1.lv3_prod_rnd_team_code,t1.lv3_prod_rd_team_cn_name,
        t1.lv4_prod_rnd_team_code,t1.lv4_prod_rd_team_cn_name,
        t1.spart_code,t1.spart_cn_name ,#{maxLevel} AS group_level
        <if test='costType == "PSP"'>
            from fin_dm_opt_foi.dm_fcst_ict_psp_pbi_main_code_dim_t t1 INNER JOIN fin_dm_opt_foi.DM_FCST_ICT_PSP_IRB_DIM_INFO_T t2
            ON t2.del_flag = 'N'
            and t2.main_flag = #{mainFlag}
            and t2.region_code = #{regionCode}
            and t2.repoffice_code = #{repofficeCode}
            and t2.oversea_flag = #{overseaFlag}
            and t2.view_flag = #{viewFlag}
            and t2.software_mark = #{softwareMark}
            and nvl(t1.lv1_prod_rnd_team_code,'snull') = nvl(t2.lv1_prod_rnd_team_code,'snull')
            and nvl(t1.lv2_prod_rnd_team_code,'snull') = nvl(t2.lv2_prod_rnd_team_code,'snull')
            and nvl(t1.lv3_prod_rnd_team_code,'snull') = nvl(t2.lv3_prod_rnd_team_code,'snull')
            and nvl(t1.lv4_prod_rnd_team_code,'snull') = nvl(t2.lv4_prod_rnd_team_code,'snull')
            and nvl(t1.spart_code,'snull') = nvl(t2.spart_code,'snull')
            and t1.bg_code = t2.bg_code
            and t1.version_id = t2.version_id
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and nvl(t1.code_attributes,'snull') = nvl(t2.code_attributes,'snull')
            </if>
        </if>
        <if test='costType == "STD"'>
            from fin_dm_opt_foi.dm_fcst_ict_std_pbi_main_code_dim_t t1 INNER JOIN fin_dm_opt_foi.DM_FCST_ICT_STD_IRB_DIM_INFO_T t2
            ON t2.del_flag = 'N'
            and t2.main_flag = #{mainFlag}
            and t2.region_code = #{regionCode}
            and t2.repoffice_code = #{repofficeCode}
            and t2.oversea_flag = #{overseaFlag}
            and t2.view_flag = #{viewFlag}
            and nvl(t1.lv1_prod_rnd_team_code,'snull') = nvl(t2.lv1_prod_rnd_team_code,'snull')
            and nvl(t1.lv2_prod_rnd_team_code,'snull') = nvl(t2.lv2_prod_rnd_team_code,'snull')
            and nvl(t1.lv3_prod_rnd_team_code,'snull') = nvl(t2.lv3_prod_rnd_team_code,'snull')
            and nvl(t1.lv4_prod_rnd_team_code,'snull') = nvl(t2.lv4_prod_rnd_team_code,'snull')
            and nvl(t1.spart_code,'snull') = nvl(t2.spart_code,'snull')
            and t1.bg_code = t2.bg_code
            and t1.version_id = t2.version_id
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and nvl(t1.code_attributes,'snull') = nvl(t2.code_attributes,'snull')
            </if>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='bgCode!=null and bgCode!=""'>
                and t1.bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and t1.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='versionId!=null'>
                and t1.version_id = #{versionId,jdbcType=NUMERIC}
            </if>
            <if test='lv1CodeList != null and lv1CodeList.size() > 0'>
                <foreach collection='lv1CodeList' item="code" open="AND t1.lv1_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv2CodeList != null and lv2CodeList.size() > 0'>
                <foreach collection='lv2CodeList' item="code" open="AND t1.lv2_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv3CodeList != null and lv3CodeList.size() > 0'>
                <foreach collection='lv3CodeList' item="code" open="AND t1.lv3_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv4CodeList != null and lv4CodeList.size() > 0'>
                <foreach collection='lv4CodeList' item="code" open="AND t1.lv4_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv1Code!=null and lv1Code!=""'>
                and t1.lv1_prod_rnd_team_code = #{lv1Code,jdbcType=VARCHAR}
            </if>
            <if test='lv2Code!=null and lv2Code!=""'>
                and t1.lv2_prod_rnd_team_code = #{lv2Code,jdbcType=VARCHAR}
            </if>
            <if test='lv3Code!=null and lv3Code!=""'>
                and t1.lv3_prod_rnd_team_code = #{lv3Code,jdbcType=VARCHAR}
            </if>
            <if test='lv4Code!=null and lv4Code!=""'>
                and t1.lv4_prod_rnd_team_code = #{lv4Code,jdbcType=VARCHAR}
            </if>
            <if test='dimensionCode!=null and dimensionCode!=""'>
                and t1.dimension_code = #{dimensionCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubCategoryCode!=null and dimensionSubCategoryCode!=""'>
                and t1.dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubDetailCode!=null and dimensionSubDetailCode!=""'>
                and t1.dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!=""'>
                and t1.spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='groupLevel == "SPART"'>
                and t1.spart_code !='SNULL'
            </if>
            <if test='groupLevel == "SUB_DETAIL"'>
                and t1.dimension_sub_detail_code !='SNULL'
            </if>
            <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND t1.lv1_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND t1.lv2_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getMainAnnualIndusSpartList" resultMap="resultMap">
        select DISTINCT
        t2.lv0_industry_catg_code,t2.lv0_industry_catg_cn_name,
        t1.spart_code,t1.spart_cn_name,#{maxLevel} AS group_level,
        t1.lv1_industry_catg_code,t1.lv1_industry_catg_cn_name,
        t1.lv2_industry_catg_code,t1.lv2_industry_catg_cn_name,
        t1.lv3_industry_catg_code,t1.lv3_industry_catg_cn_name,
        t1.lv4_industry_catg_code,t1.lv4_industry_catg_cn_name
        <if test='costType == "PSP"'>
            from fin_dm_opt_foi.dm_fcst_ict_psp_pbi_main_code_dim_t t1 INNER JOIN fin_dm_opt_foi.DM_FCST_ICT_PSP_INDUS_DIM_INFO_T t2
            ON t2.del_flag = 'N'
            and t2.main_flag = #{mainFlag}
            and t2.region_code = #{regionCode}
            and t2.repoffice_code = #{repofficeCode}
            and t2.oversea_flag = #{overseaFlag}
            and t2.view_flag = #{viewFlag}
            and t2.software_mark = #{softwareMark}
            and nvl(t1.lv1_industry_catg_code,'snull') = nvl(t2.lv1_industry_catg_code,'snull')
            and nvl(t1.lv2_industry_catg_code,'snull') = nvl(t2.lv2_industry_catg_code,'snull')
            and nvl(t1.lv3_industry_catg_code,'snull') = nvl(t2.lv3_industry_catg_code,'snull')
            and nvl(t1.lv4_industry_catg_code,'snull') = nvl(t2.lv4_industry_catg_code,'snull')
            and nvl(t1.spart_code,'snull') = nvl(t2.spart_code,'snull')
            and t1.bg_code = t2.bg_code
            and t1.version_id = t2.version_id
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and nvl(t1.code_attributes,'snull') = nvl(t2.code_attributes,'snull')
            </if>
        </if>
        <if test='costType == "STD"'>
            from fin_dm_opt_foi.dm_fcst_ict_std_pbi_main_code_dim_t t1 INNER JOIN fin_dm_opt_foi.DM_FCST_ICT_STD_INDUS_DIM_INFO_T t2
            ON t2.del_flag = 'N'
            and t2.main_flag = #{mainFlag}
            and t2.region_code = #{regionCode}
            and t2.repoffice_code = #{repofficeCode}
            and t2.oversea_flag = #{overseaFlag}
            and t2.view_flag = #{viewFlag}
            and nvl(t1.lv1_industry_catg_code,'snull') = nvl(t2.lv1_industry_catg_code,'snull')
            and nvl(t1.lv2_industry_catg_code,'snull') = nvl(t2.lv2_industry_catg_code,'snull')
            and nvl(t1.lv3_industry_catg_code,'snull') = nvl(t2.lv3_industry_catg_code,'snull')
            and nvl(t1.lv4_industry_catg_code,'snull') = nvl(t2.lv4_industry_catg_code,'snull')
            and nvl(t1.spart_code,'snull') = nvl(t2.spart_code,'snull')
            and t1.bg_code = t2.bg_code
            and t1.version_id = t2.version_id
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and nvl(t1.code_attributes,'snull') = nvl(t2.code_attributes,'snull')
            </if>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='bgCode!=null and bgCode!=""'>
                and t1.bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='versionId!=null'>
                and t1.version_id = #{versionId,jdbcType=NUMERIC}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and t1.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='lv1CodeList != null and lv1CodeList.size() > 0'>
                <foreach collection='lv1CodeList' item="code" open="AND t1.lv1_industry_catg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv2CodeList != null and lv2CodeList.size() > 0'>
                <foreach collection='lv2CodeList' item="code" open="AND t1.lv2_industry_catg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv3CodeList != null and lv3CodeList.size() > 0'>
                <foreach collection='lv3CodeList' item="code" open="AND t1.lv3_industry_catg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv4CodeList != null and lv4CodeList.size() > 0'>
                <foreach collection='lv4CodeList' item="code" open="AND t1.lv4_industry_catg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv1Code!=null and lv1Code!=""'>
                and t1.lv1_industry_catg_code = #{lv1Code,jdbcType=VARCHAR}
            </if>
            <if test='lv2Code!=null and lv2Code!=""'>
                and t1.lv2_industry_catg_code = #{lv2Code,jdbcType=VARCHAR}
            </if>
            <if test='lv3Code!=null and lv3Code!=""'>
                and t1.lv3_industry_catg_code = #{lv3Code,jdbcType=VARCHAR}
            </if>
            <if test='lv4Code!=null and lv4Code!=""'>
                and t1.lv4_industry_catg_code = #{lv4Code,jdbcType=VARCHAR}
            </if>
            <if test='dimensionCode!=null and dimensionCode!=""'>
                and t1.dimension_code = #{dimensionCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubCategoryCode!=null and dimensionSubCategoryCode!=""'>
                and t1.dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubDetailCode!=null and dimensionSubDetailCode!=""'>
                and t1.dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!=""'>
                and t1.spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='groupLevel == "SPART"'>
                and t1.spart_code !='SNULL'
            </if>
            <if test='groupLevel == "SUB_DETAIL"'>
                and t1.dimension_sub_detail_code !='SNULL'
            </if>
            <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND t1.lv1_industry_catg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND t1.lv2_industry_catg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getMainAnnualIndusDimInfoList"  resultMap="resultMap">
        select DISTINCT
        <choose>
            <when test='groupLevel == "SPART"'>
                <include refid="indusMainSpartLevel"/>
            </when>
            <when test='groupLevel == "LV4"'>
                <include refid="indusMainLv4Level"/>
            </when>
            <when test='groupLevel == "LV3"'>
                <include refid="indusMainLv3Level"/>
            </when>
            <when test='groupLevel == "LV2"'>
                <include refid="indusMainLv2Level"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag=="Y"'>
                <include refid="indusMainLv1HasPermission"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag=="N"'>
                <include refid="indusMainLv1NotPermission"/>
            </when>
            <when test='groupLevel == "LV0"'>
                <include refid="indusMainLv0Level"/>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='costType == "PSP"'>
            from fin_dm_opt_foi.dm_fcst_ict_psp_pbi_main_code_dim_t t1 INNER JOIN fin_dm_opt_foi.DM_FCST_ICT_PSP_INDUS_DIM_INFO_T t2
            ON t2.del_flag = 'N'
            and t2.main_flag = #{mainFlag}
            and t2.region_code = #{regionCode}
            and t2.repoffice_code = #{repofficeCode}
            and t2.oversea_flag = #{overseaFlag}
            and t2.view_flag = #{viewFlag}
            and t2.software_mark = #{softwareMark}
            and nvl(t1.lv1_industry_catg_code,'snull') = nvl(t2.lv1_industry_catg_code,'snull')
            and nvl(t1.lv2_industry_catg_code,'snull') = nvl(t2.lv2_industry_catg_code,'snull')
            and nvl(t1.lv3_industry_catg_code,'snull') = nvl(t2.lv3_industry_catg_code,'snull')
            and nvl(t1.lv4_industry_catg_code,'snull') = nvl(t2.lv4_industry_catg_code,'snull')
            and nvl(t1.spart_code,'snull') = nvl(t2.spart_code,'snull')
            and t1.bg_code = t2.bg_code
            and t1.version_id = t2.version_id
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and nvl(t1.code_attributes,'snull') = nvl(t2.code_attributes,'snull')
            </if>
        </if>
        <if test='costType == "STD"'>
            from fin_dm_opt_foi.dm_fcst_ict_std_pbi_main_code_dim_t t1 INNER JOIN fin_dm_opt_foi.DM_FCST_ICT_STD_INDUS_DIM_INFO_T t2
            ON t2.del_flag = 'N'
            and t2.main_flag = #{mainFlag}
            and t2.region_code = #{regionCode}
            and t2.repoffice_code = #{repofficeCode}
            and t2.oversea_flag = #{overseaFlag}
            and t2.view_flag = #{viewFlag}
            and t2.software_mark = #{softwareMark}
            and nvl(t1.lv1_industry_catg_code,'snull') = nvl(t2.lv1_industry_catg_code,'snull')
            and nvl(t1.lv2_industry_catg_code,'snull') = nvl(t2.lv2_industry_catg_code,'snull')
            and nvl(t1.lv3_industry_catg_code,'snull') = nvl(t2.lv3_industry_catg_code,'snull')
            and nvl(t1.lv4_industry_catg_code,'snull') = nvl(t2.lv4_industry_catg_code,'snull')
            and nvl(t1.spart_code,'snull') = nvl(t2.spart_code,'snull')
            and t1.bg_code = t2.bg_code
            and t1.version_id = t2.version_id
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and nvl(t1.code_attributes,'snull') = nvl(t2.code_attributes,'snull')
            </if>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='bgCode!=null and bgCode!=""'>
                and t1.bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='versionId!=null'>
                and t1.version_id = #{versionId,jdbcType=NUMERIC}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and t1.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='groupLevel != "LV0" and lv1CodeList != null and lv1CodeList.size() > 0'>
                <foreach collection='lv1CodeList' item="code" open="AND t1.lv1_industry_catg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv2CodeList != null and lv2CodeList.size() > 0'>
                <foreach collection='lv2CodeList' item="code" open="AND t1.lv2_industry_catg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and groupLevel != "LV2" and lv3CodeList != null and lv3CodeList.size() > 0'>
                <foreach collection='lv3CodeList' item="code" open="AND t1.lv3_industry_catg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and groupLevel != "LV2" and groupLevel != "LV3" and lv4CodeList != null and lv4CodeList.size() > 0'>
                <foreach collection='lv4CodeList' item="code" open="AND t1.lv4_industry_catg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv1Code!=null and lv1Code!=""'>
                and t1.lv1_industry_catg_code = #{lv1Code,jdbcType=VARCHAR}
            </if>
            <if test='lv2Code!=null and lv2Code!=""'>
                and t1.lv2_industry_catg_code = #{lv2Code,jdbcType=VARCHAR}
            </if>
            <if test='lv3Code!=null and lv3Code!=""'>
                and t1.lv3_industry_catg_code = #{lv3Code,jdbcType=VARCHAR}
            </if>
            <if test='lv4Code!=null and lv4Code!=""'>
                and t1.lv4_industry_catg_code = #{lv4Code,jdbcType=VARCHAR}
            </if>
            <if test='dimensionCode!=null and dimensionCode!=""'>
                and t1.dimension_code = #{dimensionCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubCategoryCode!=null and dimensionSubCategoryCode!=""'>
                and t1.dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubDetailCode!=null and dimensionSubDetailCode!=""'>
                and t1.dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!=""'>
                and t1.spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='groupLevel == "SPART"'>
                and t1.spart_code !='SNULL'
            </if>
            <if test='groupLevel == "SUB_DETAIL"'>
                and t1.dimension_sub_detail_code !='SNULL'
            </if>
            <if test='keyword != null and keyword != ""'>
                and group_cn_name LIKE CONCAT(CONCAT('%', #{keyword}::text ,'%'))
            </if>
            <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND t1.lv1_industry_catg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND t1.lv2_industry_catg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getMainAnnualProdDimInfoList" resultMap="resultMap">
        select DISTINCT
        <choose>
            <when test='groupLevel == "SPART"'>
                <include refid="spartMainProdLevel"/>
            </when>
            <when test='groupLevel == "LV4"'>
                <include refid="lv4MainProdLevel"/>
            </when>
            <when test='groupLevel == "LV3"'>
                <include refid="lv3MainProdLevel"/>
            </when>
            <when test='groupLevel == "LV2"'>
                <include refid="lv2MainProdLevel"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag =="Y"'>
                <include refid="lv1MainProdLevelHasPermission"/>
            </when>
            <when test='groupLevel == "LV1" and lv0Flag =="N"'>
                <include refid="lv1MainProdLevelNotPermission"/>
            </when>
            <when test='groupLevel == "LV0"'>
                <include refid="lv0MainProdLevel"/>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='costType == "PSP"'>
            from fin_dm_opt_foi.dm_fcst_ict_psp_pbi_main_code_dim_t t1 INNER JOIN fin_dm_opt_foi.DM_FCST_ICT_PSP_PROD_DIM_INFO_T t2
            ON t2.del_flag = 'N'
            and t2.main_flag = #{mainFlag}
            and t2.region_code = #{regionCode}
            and t2.repoffice_code = #{repofficeCode}
            and t2.oversea_flag = #{overseaFlag}
            and t2.view_flag = #{viewFlag}
            and t2.software_mark = #{softwareMark}
            and nvl(t1.lv1_prod_list_code,'snull') = nvl(t2.lv1_prod_list_code,'snull')
            and nvl(t1.lv2_prod_list_code,'snull') = nvl(t2.lv2_prod_list_code,'snull')
            and nvl(t1.lv3_prod_list_code,'snull') = nvl(t2.lv3_prod_list_code,'snull')
            and nvl(t1.lv4_prod_list_code,'snull') = nvl(t2.lv4_prod_list_code,'snull')
            and nvl(t1.spart_code,'snull') = nvl(t2.spart_code,'snull')
            and t1.bg_code = t2.bg_code
            and t1.version_id = t2.version_id
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and nvl(t1.code_attributes,'snull') = nvl(t2.code_attributes,'snull')
            </if>
        </if>
        <if test='costType == "STD"'>
            from fin_dm_opt_foi.dm_fcst_ict_std_pbi_main_code_dim_t t1 INNER JOIN fin_dm_opt_foi.DM_FCST_ICT_STD_PROD_DIM_INFO_T t2
            ON t2.del_flag = 'N'
            and t2.main_flag = #{mainFlag}
            and t2.region_code = #{regionCode}
            and t2.repoffice_code = #{repofficeCode}
            and t2.oversea_flag = #{overseaFlag}
            and t2.view_flag = #{viewFlag}
            and nvl(t1.lv1_prod_list_code,'snull') = nvl(t2.lv1_prod_list_code,'snull')
            and nvl(t1.lv2_prod_list_code,'snull') = nvl(t2.lv2_prod_list_code,'snull')
            and nvl(t1.lv3_prod_list_code,'snull') = nvl(t2.lv3_prod_list_code,'snull')
            and nvl(t1.lv4_prod_list_code,'snull') = nvl(t2.lv4_prod_list_code,'snull')
            and nvl(t1.spart_code,'snull') = nvl(t2.spart_code,'snull')
            and t1.bg_code = t2.bg_code
            and t1.version_id = t2.version_id
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and nvl(t1.code_attributes,'snull') = nvl(t2.code_attributes,'snull')
            </if>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='bgCode!=null and bgCode!=""'>
                and t1.bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and t1.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='versionId!=null'>
                and t1.version_id = #{versionId,jdbcType=NUMERIC}
            </if>
            <if test='groupLevel != "LV0" and lv1CodeList != null and lv1CodeList.size() > 0'>
                <foreach collection='lv1CodeList' item="code" open="AND t1.lv1_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv2CodeList != null and lv2CodeList.size() > 0'>
                <foreach collection='lv2CodeList' item="code" open="AND t1.lv2_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and groupLevel != "LV2" and lv3CodeList != null and lv3CodeList.size() > 0'>
                <foreach collection='lv3CodeList' item="code" open="AND t1.lv3_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and groupLevel != "LV2" and groupLevel != "LV3" and lv4CodeList != null and lv4CodeList.size() > 0'>
                <foreach collection='lv4CodeList' item="code" open="AND t1.lv4_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv1Code!=null and lv1Code!=""'>
                and t1.lv1_prod_list_code = #{lv1Code,jdbcType=VARCHAR}
            </if>
            <if test='lv2Code!=null and lv2Code!=""'>
                and t1.lv2_prod_list_code = #{lv2Code,jdbcType=VARCHAR}
            </if>
            <if test='lv3Code!=null and lv3Code!=""'>
                and t1.lv3_prod_list_code = #{lv3Code,jdbcType=VARCHAR}
            </if>
            <if test='lv4Code!=null and lv4Code!=""'>
                and t1.lv4_prod_list_code = #{lv4Code,jdbcType=VARCHAR}
            </if>
            <if test='dimensionCode!=null and dimensionCode!=""'>
                and t1.dimension_code = #{dimensionCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubCategoryCode!=null and dimensionSubCategoryCode!=""'>
                and t1.dimension_subCategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubDetailCode!=null and dimensionSubDetailCode!=""'>
                and t1.dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!=""'>
                and t1.spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='nextGroupLevel == "SPART"'>
                and t1.spart_code !='SNULL'
            </if>
            <if test='nextGroupLevel == "SUB_DETAIL"'>
                and t1.dimension_sub_detail_code !='SNULL'
            </if>
            <if test='keyword != null and keyword != ""'>
                and group_cn_name LIKE CONCAT(CONCAT('%', #{keyword}::text ,'%'))
            </if>
            <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND t1.lv1_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND t1.lv2_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getMainAnnualProdSpartList"  resultMap="resultMap">
        select DISTINCT
        t2.lv0_prod_list_code,t2.lv0_prod_list_cn_name,
        t1.spart_code,t1.spart_cn_name,#{maxLevel} AS group_level,
        t1.lv1_prod_list_code,t1.lv1_prod_list_cn_name,
        t1.lv2_prod_list_code,t1.lv2_prod_list_cn_name,
        t1.lv3_prod_list_code,t1.lv3_prod_list_cn_name,
        t1.lv4_prod_list_code,t1.lv4_prod_list_cn_name
        <if test='costType == "PSP"'>
            from fin_dm_opt_foi.dm_fcst_ict_psp_pbi_main_code_dim_t t1 INNER JOIN fin_dm_opt_foi.DM_FCST_ICT_PSP_PROD_DIM_INFO_T t2
            ON t2.del_flag = 'N'
            and t2.main_flag = #{mainFlag}
            and t2.region_code = #{regionCode}
            and t2.repoffice_code = #{repofficeCode}
            and t2.oversea_flag = #{overseaFlag}
            and t2.view_flag = #{viewFlag}
            and t2.software_mark = #{softwareMark}
            and nvl(t1.lv1_prod_list_code,'snull') = nvl(t2.lv1_prod_list_code,'snull')
            and nvl(t1.lv2_prod_list_code,'snull') = nvl(t2.lv2_prod_list_code,'snull')
            and nvl(t1.lv3_prod_list_code,'snull') = nvl(t2.lv3_prod_list_code,'snull')
            and nvl(t1.lv4_prod_list_code,'snull') = nvl(t2.lv4_prod_list_code,'snull')
            and nvl(t1.spart_code,'snull') = nvl(t2.spart_code,'snull')
            and t1.bg_code = t2.bg_code
            and t1.version_id = t2.version_id
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and nvl(t1.code_attributes,'snull') = nvl(t2.code_attributes,'snull')
            </if>
        </if>
        <if test='costType == "STD"'>
            from fin_dm_opt_foi.dm_fcst_ict_std_pbi_main_code_dim_t t1 INNER JOIN fin_dm_opt_foi.DM_FCST_ICT_STD_PROD_DIM_INFO_T t2
            ON t2.del_flag = 'N'
            and t2.main_flag = #{mainFlag}
            and t2.region_code = #{regionCode}
            and t2.repoffice_code = #{repofficeCode}
            and t2.oversea_flag = #{overseaFlag}
            and t2.view_flag = #{viewFlag}
            and nvl(t1.lv1_prod_list_code,'snull') = nvl(t2.lv1_prod_list_code,'snull')
            and nvl(t1.lv2_prod_list_code,'snull') = nvl(t2.lv2_prod_list_code,'snull')
            and nvl(t1.lv3_prod_list_code,'snull') = nvl(t2.lv3_prod_list_code,'snull')
            and nvl(t1.lv4_prod_list_code,'snull') = nvl(t2.lv4_prod_list_code,'snull')
            and nvl(t1.spart_code,'snull') = nvl(t2.spart_code,'snull')
            and t1.bg_code = t2.bg_code
            and t1.version_id = t2.version_id
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and nvl(t1.code_attributes,'snull') = nvl(t2.code_attributes,'snull')
            </if>
        </if>
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='bgCode!=null and bgCode!=""'>
                and t1.bg_code = #{bgCode,jdbcType=VARCHAR}
            </if>
            <if test='codeAttributes!=null and codeAttributes!=""'>
                and t1.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
            </if>
            <if test='versionId!=null'>
                and t1.version_id = #{versionId,jdbcType=NUMERIC}
            </if>
            <if test='lv1CodeList != null and lv1CodeList.size() > 0'>
                <foreach collection='lv1CodeList' item="code" open="AND t1.lv1_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv2CodeList != null and lv2CodeList.size() > 0'>
                <foreach collection='lv2CodeList' item="code" open="AND t1.lv2_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv3CodeList != null and lv3CodeList.size() > 0'>
                <foreach collection='lv3CodeList' item="code" open="AND t1.lv3_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv4CodeList != null and lv4CodeList.size() > 0'>
                <foreach collection='lv4CodeList' item="code" open="AND t1.lv4_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='lv1Code!=null and lv1Code!=""'>
                and t1.lv1_prod_list_code = #{lv1Code,jdbcType=VARCHAR}
            </if>
            <if test='lv2Code!=null and lv2Code!=""'>
                and t1.lv2_prod_list_code = #{lv2Code,jdbcType=VARCHAR}
            </if>
            <if test='lv3Code!=null and lv3Code!=""'>
                and t1.lv3_prod_list_code = #{lv3Code,jdbcType=VARCHAR}
            </if>
            <if test='lv4Code!=null and lv4Code!=""'>
                and t1.lv4_prod_list_code = #{lv4Code,jdbcType=VARCHAR}
            </if>
            <if test='dimensionCode!=null and dimensionCode!=""'>
                and t1.dimension_code = #{dimensionCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubCategoryCode!=null and dimensionSubCategoryCode!=""'>
                and t1.dimension_subCategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test='dimensionSubDetailCode!=null and dimensionSubDetailCode!=""'>
                and t1.dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
            </if>
            <if test='spartCode!=null and spartCode!=""'>
                and t1.spart_code = #{spartCode,jdbcType=VARCHAR}
            </if>
            <if test='nextGroupLevel == "SPART"'>
                and t1.spart_code !='SNULL'
            </if>
            <if test='nextGroupLevel == "SUB_DETAIL"'>
                and t1.dimension_sub_detail_code !='SNULL'
            </if>
            <if test='groupLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
                <foreach collection='lv2DimensionSet' item="code" open="AND t1.lv1_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
            <if test='groupLevel != "LV0" and groupLevel != "LV1" and lv3DimensionSet != null and lv3DimensionSet.size() > 0'>
                <foreach collection='lv3DimensionSet' item="code" open="AND t1.lv2_prod_list_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </if>
        </trim>
    </select>

</mapper>