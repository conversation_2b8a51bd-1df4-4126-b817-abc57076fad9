/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.costReduct;

import com.huawei.it.fcst.enums.ResultCodeEnum;
import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.constant.Constant;
import com.huawei.it.fcst.industry.pbi.dao.ICostReductDao;
import com.huawei.it.fcst.industry.pbi.impl.common.IctCommonService;
import com.huawei.it.fcst.industry.pbi.impl.config.AsyncIctConfigService;
import com.huawei.it.fcst.industry.pbi.impl.template.CostReductTemplateEnum;
import com.huawei.it.fcst.industry.pbi.service.costReduct.ICostReductionTargetService;
import com.huawei.it.fcst.export.IExcelTemplateBeanManager;
import com.huawei.it.fcst.export.IExportProcessorService;
import com.huawei.it.fcst.industry.pbi.utils.CostReductUtils;
import com.huawei.it.fcst.industry.pbi.utils.FcstIndustryUtil;
import com.huawei.it.fcst.industry.pbi.vo.common.DmFcstDataRefreshStatus;
import com.huawei.it.fcst.industry.pbi.vo.config.CostReductVO;
import com.huawei.it.fcst.industry.pbi.vo.config.ImportContextVO;
import com.huawei.it.fcst.util.ExcelUtils;
import com.huawei.it.fcst.util.UserInfoUtils;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.fcst.vo.UploadInfoVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.core.util.CollectionUtil;

import com.google.common.collect.Lists;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import javax.inject.Inject;
import javax.inject.Named;
import javax.servlet.http.HttpServletResponse;

/**
 * CostReductionTargetService
 *
 * @since 2024-07-10
 */
@Named("costReductionTargetService")
@JalorResource(code = "costReductionTargetService", desc = "newIct-配置管理-降成本目标服务")
public class CostReductionTargetService implements ICostReductionTargetService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CostReductionTargetService.class);

    @Inject
    private ICostReductDao iCostReductDao;

    @Autowired
    private IExportProcessorService iExportProcessorService;

    @Autowired
    private IctCommonService ictCommonService;

    @Inject
    private AsyncIctConfigService asyncIctConfigService;

    @Inject
    private ExcelUtils excelUtils;

    /**
     * [配置管理-降成本目标-下拉框接口]
     *
     * @param costReductVO costDeductVO
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @JalorOperation(code = "getCostReductDropDown", desc = "配置管理-降成本目标-下拉框接口")
    @Override
    public ResultDataVO getCostReductDropDown(CostReductVO costReductVO) throws CommonApplicationException {
        return ResultDataVO.success(iCostReductDao.findCostReductDropDown(costReductVO));
    }

    /**
     * [配置管理-降成本目标-层级信息下拉框]
     *
     * @param costReductVO
     * @return ResultDataVO
     * @throws CommonApplicationException
     */
    @JalorOperation(code = "getGroupLevelInfo", desc = "配置管理-降成本目标-层级信息下拉框")
    @Override
    public ResultDataVO getGroupLevelInfo(CostReductVO costReductVO) throws CommonApplicationException {
        return ResultDataVO.success(iCostReductDao.getGroupLevelInfo(costReductVO));
    }

    /**
     * 配置管理-降成本目标-查询接口
     *
     * @param costReductVO
     * @param pageVO pageVO params
     * @return ResultDataVO
     */
    @JalorOperation(code = "getCostReductListByPage", desc = "配置管理-降成本目标-查询接口")
    @Override
    public ResultDataVO getCostReductListByPage(CostReductVO costReductVO, PageVO pageVO) {
        return ResultDataVO.success(iCostReductDao.findCostReductByPage(costReductVO, pageVO));
    }

    /**
     * 配置管理-降成本目标-保存接口
     *
     * @param costReductVOList CostReductVOList
     * @return ResultDataVO ResultDataVO
     * @throws CommonApplicationException
     */
    @JalorOperation(code = "saveCostReductData", desc = "配置管理-降成本目标-保存接口")
    @Audit(module = "costReductionTargetService-saveCostReductData", operation = "saveCostReductData", message = "配置管理-降成本目标-保存接口")
    @Override
    public ResultDataVO saveCostReductData(List<CostReductVO> costReductVOList) throws ApplicationException {
        LOGGER.info(">>>Begin CostReductService::saveCostReductData");
        // 空数据检查
        if (CollectionUtil.isNullOrEmpty(costReductVOList)) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR, "无数据可保存");
        } else {
            // 入参数据量控制
            if (costReductVOList.size() > Constant.IntegerEnum.ONE_HUNDRED.getValue()) {
                return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR, "操作的数据过多！");
            }
            // 当前用户id
            Long userId = UserInfoUtils.getUserId();
            // 创建版本信息
            Long newVersionId = ictCommonService.createNewVersionInfo("RED_DIM");
            String oldVersionId = costReductVOList.get(0).getVersionId();
            processVersionData(newVersionId.toString(), oldVersionId, userId);
            // versionId不变
            costReductVOList.stream().forEach(vo -> {
                vo.setVersionId(String.valueOf(newVersionId));
                vo.setCreatedBy(userId);
                vo.setLastUpdatedBy(userId);
            });
            // 整理录入修改数据，新增 INSERT，更新 UPDATE
            Map<String, List<CostReductVO>> infoCollection = costReductVOList.stream()
                .collect(Collectors.groupingBy(CostReductVO::getOperationType));
            // 新增数据集合
            List<CostReductVO> insertList = infoCollection.get("INSERT");
            if (!CollectionUtil.isNullOrEmpty(insertList)) {
                ResultDataVO executeResult = insertCostReductData(userId, insertList);
                if (!StringUtils.equals(executeResult.getCode(), "200")) {
                    return executeResult;
                }
            }
            // 修改数据集合
            List<CostReductVO> updateList = infoCollection.get("UPDATE");
            if (!CollectionUtil.isNullOrEmpty(updateList)) {
                ResultDataVO executeResult = updateCostReductData(userId, updateList);
                if (!StringUtils.equals(executeResult.getCode(), "200")) {
                    return executeResult;
                }
            }
            return ResultDataVO.success();
        }
    }

    // 新增数据校验
    private void processVersionData(String newVersionId, String oldVersionId, Long userId) {
        iCostReductDao.copyHisData(newVersionId, oldVersionId, userId);
    }

    // 版本数据处理
    private ResultDataVO insertCostReductData(Long userId, List<CostReductVO> dataList) {
        // 看历史版本数据是否包含有要保存的数据，有则不保存
        List<String> infoList = iCostReductDao.getSpecialInfoList(dataList.get(0).getVersionId());
        dataList = dataList.stream().filter(vo -> !infoList.contains(vo.toString())).collect(Collectors.toList());
        if(CollectionUtil.isNullOrEmpty(dataList)){
            return ResultDataVO.failure(ResultCodeEnum.NO_DATA,"无数据可保存！");
        }
        // 检查数据
        ResultDataVO checkResult = checkData(dataList);
        if (!StringUtils.equals(checkResult.getCode(), "200")) {
            return checkResult;
        }
        insertDataList(dataList, userId);
        return ResultDataVO.success();
    }

    private ResultDataVO updateCostReductData(Long userId, List<CostReductVO> updateList) {
        List<CostReductVO> updateDataList = new ArrayList<>();
        ResultDataVO checkResult = checkData(updateList);
        if (!StringUtils.equals(checkResult.getCode(), "200")) {
            return checkResult;
        }
        updateList.stream().forEach(newVo -> {
            // 获取更改前vo信息,需对比前后修改关系
            CostReductVO oldDataVo = newVo.getOldData();
            // 属性相同，直接跳过
            if (compareData(oldDataVo, newVo)) {
                return;
            }
            updateDataList.add(newVo);
        });
        if (CollectionUtils.isNotEmpty(updateDataList)) {
            iCostReductDao.updateCostReductDataList(updateDataList, userId);
        }
        return ResultDataVO.success();
    }

    private boolean compareData(CostReductVO oldDataVo, CostReductVO newVo) {
        return StringUtils.equals(oldDataVo.getStringInfo(), newVo.getStringInfo());
    }

    /**
     * 检查数据
     *
     * @param dataList 数据集合
     * @return
     */
    @Nullable
    private ResultDataVO checkData(List<CostReductVO> dataList) {
        AtomicInteger nullNum = new AtomicInteger();        // 非空校验
        AtomicInteger lengthNum = new AtomicInteger();        // 长度校验
        AtomicInteger priceNum = new AtomicInteger();       // 价格值校验
        AtomicInteger dataNum = new AtomicInteger();        // 重复校验
        List<String> dulipDataList = new ArrayList<>(dataList.size());
        dataList.stream().forEach(vo -> {
            if (CostReductUtils.checkCostReductIfNullValue(vo)) {
                nullNum.getAndIncrement();
            }
            if (CostReductUtils.checkCostReductDataLength(vo)) {
                lengthNum.getAndIncrement();
            }
            if (CostReductUtils.checkRatioValue(vo)) {
                priceNum.getAndIncrement();
            }
            if (dulipDataList.contains(vo.toString())) {
                dataNum.getAndIncrement();
            } else {
                dulipDataList.add(vo.toString());
            }
        });

        dulipDataList.clear();
        return getResultData(nullNum, priceNum, dataNum, lengthNum);
    }

    @NotNull
    private ResultDataVO getResultData(AtomicInteger nullNum, AtomicInteger priceNum, AtomicInteger dataNum,
        AtomicInteger lengthNum) {
        if (nullNum.get() > 0) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR, "存在必输项为空的数据，不可保存");
        }
        if (lengthNum.get() > 0) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR, "字段过长，不可保存");
        }
        if (priceNum.get() > 0) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR, "降成本目标数据格式不正确，请检查数据");
        }
        if (dataNum.get() > 0) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR, "数据重复，请检查数据");
        }
        return ResultDataVO.success();
    }

    // 插入数据
    private void insertDataList(List<CostReductVO> addList, Long userId) {
        // 数据量小于1000条直接插入，否则分批插入
        if (addList.size() <= 1000) {
            iCostReductDao.batchInsertCostReductVOs(addList, userId);
            return;
        }
        Lists.partition(addList, 1000)
            .stream()
            .forEach(voList -> iCostReductDao.batchInsertCostReductVOs(voList, userId));
    }

    /**
     * 配置管理-降成本目标-删除接口
     *
     * @param costReductVOList costReductVOList
     * @return ResultDataVO
     */
    @JalorOperation(code = "deleteCostReductData", desc = "配置管理-降成本目标-删除接口")
    @Audit(module = "costReductionTargetService-deleteCostReductData", operation = "deleteCostReductData", message = "配置管理-降成本目标-删除接口")
    @Override
    public ResultDataVO deleteCostReductData(List<CostReductVO> costReductVOList) throws ApplicationException {
        // 空数据检查
        if (CollectionUtil.isNullOrEmpty(costReductVOList)) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR, "无数据可删除");
        } else {
            // 入参数据量控制
            if (costReductVOList.size() > Constant.IntegerEnum.ONE_HUNDRED.getValue()) {
                return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR, "操作的数据过多！");
            }
            // 当前用户id
            Long userId = UserInfoUtils.getUserId();
            String oldVersionId = costReductVOList.get(0).getVersionId();
            // 创建版本信息
            Long newVersionId = ictCommonService.createNewVersionInfo("RED_DIM");
            processVersionData(newVersionId.toString(), oldVersionId, userId);
            // versionId不变
            costReductVOList.stream().forEach(vo -> {
                vo.setVersionId(String.valueOf(newVersionId));
                vo.setCreatedBy(userId);
                vo.setLastUpdatedBy(userId);
            });
            iCostReductDao.deleteCostReductDataList(costReductVOList);
            return ResultDataVO.success();
        }
    }

    /**
     * 配置管理-降成本目标-导出接口
     *
     * @param costReductVO
     * @param response
     * @return ResultDataVO
     */
    @JalorOperation(code = "exportCostReductData", desc = "配置管理-降成本目标-导出接口")
    @Audit(module = "costReductionTargetService-exportCostReductData", operation = "exportCostReductData", message = "配置管理-降成本目标-导出接口")
    @Override
    public ResultDataVO exportCostReductData(CostReductVO costReductVO, HttpServletResponse response) throws Exception {
        LOGGER.info(">>>Begin CostReductionTargetService::exportCostReductData");
        IExcelTemplateBeanManager templateBeanManager = CostReductTemplateEnum.getByCode("01", "");
        Map parmMap = new HashMap();
        parmMap.put("exportModuleName", "成本指数-ICT-降成本目标");
        parmMap.put("exportFileName", "配置管理-降成本目标" + System.currentTimeMillis());
        iExportProcessorService.fillEasyExcelExport(response, templateBeanManager, costReductVO, parmMap);
        return ResultDataVO.success();
    }

    @Override
    @JalorOperation(code = "importCostReductData", desc = "配置管理-降成本目标-导入接口")
    @Audit(module = "costReductionTargetService-importCostReductData", operation = "importCostReductData", message = "配置管理-降成本目标-导入接口")
    public ResultDataVO importCostReductData(Attachment attachment, Long versionId) throws Exception {
        LOGGER.info(">>>Begin CostReductionTargetService::importCostReductData");
        if (ObjectUtils.isEmpty(versionId)) {
            throw new CommonApplicationException("版本ID不能为空！");
        }
        // 校验导入的Excel文件格式和文件大小
        excelUtils.verifyExcelFile(attachment, CommonConstant.RED_TARGET_TYPE);
        // 保存请求上下文信息
        IRequestContext context = RequestContextManager.getCurrent();
        // 设置任务刷新状态
        DmFcstDataRefreshStatus dataRefreshStatus = ictCommonService.saveDataRefreshStatus("RED_DIM_IMPORT");
        // 获取模块类型和导入数量限制
        Map<String, Object> params = ictCommonService.getHeaderMap(CommonConstant.RED_TARGET_TYPE);
        // 导入信息记录对象VO
        UploadInfoVO uploadInfoVO = FcstIndustryUtil.getUploadInfoVO(attachment, versionId, params, CommonConstant.RED_TARGET_TYPE);
        ImportContextVO importContextVO = ImportContextVO.builder().versionId(versionId).context(context)
                .userId(UserInfoUtils.getUserId()).userCn(UserInfoUtils.getUserCn())
                .dataRefreshStatus(dataRefreshStatus)
                .uploadInfoVO(uploadInfoVO).build();
        asyncIctConfigService.importCostReductData(attachment, importContextVO);
        return ResultDataVO.success(dataRefreshStatus);
    }

    /**
     * 配置管理-降成本目标-数据重复检查
     *
     * @param costReductVOList CostReductVOList
     * @return ResultDataVO
     */
    @JalorOperation(code = "checkCostReductData", desc = "配置管理-降成本目标-数据重复检查")
    @Audit(module = "costReductionTargetService-checkCostReductData", operation = "checkCostReductData", message = "配置管理-降成本目标-数据重复检查")
    @Override
    public ResultDataVO checkCostReductData(List<CostReductVO> costReductVOList) throws ApplicationException {
        // 空数据检查
        if (CollectionUtil.isNullOrEmpty(costReductVOList)) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR, "无数据可保存");
        } else {
            // 入参数据量控制
            if (costReductVOList.size() > Constant.IntegerEnum.MAX_SIZE.getValue()) {
                return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR, "操作的数据过多！");
            }
            List<String> infoList = iCostReductDao.getSpecialInfoList(costReductVOList.get(0).getVersionId());
            List<CostReductVO> collect = costReductVOList.stream()
                .filter(vo -> !infoList.contains(vo.toString()))
                .collect(Collectors.toList());
            if (CollectionUtil.isNullOrEmpty(collect)) {
                return ResultDataVO.success("数据已存在！");
            }
            return ResultDataVO.success();
        }
    }
}
