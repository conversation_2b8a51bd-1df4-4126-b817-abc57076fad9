<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.pbi.dao.IDmVirtualizedTaskDao">
    <select id="callAnnualFuncTask" resultType="java.lang.String">
        select fin_dm_opt_foi.f_dm_fcst_point_base_cus_annl (
            #{query.costType,jdbcType=VARCHAR},
            #{query.granularityType ,jdbcType=VARCHAR},
            #{query.customId,jdbcType=NUMERIC},
            #{keyStr,jdbcType=VARCHAR}
            )
    </select>
    <select id="callMonthFuncTask" resultType="java.lang.String">
        select fin_dm_opt_foi.f_fcst_ict_point_base_cus_mon_result_t (
                       #{query.costType,jdbcType=VARCHAR},
                       #{query.granularityType ,jdbcType=VARCHAR},
                       #{query.pageType ,jdbcType=VARCHAR},
                       #{query.customId,jdbcType=NUMERIC},
                       #{keyStr,jdbcType=VARCHAR}
                   )
    </select>

    <select id="callMontCostAmtFunc" resultType="java.lang.String">
        select fin_dm_opt_foi.f_dm_fcst_mon_cost_amt(
                       #{query.costType,jdbcType=VARCHAR},
                       #{query.granularityType ,jdbcType=VARCHAR},
                       #{query.viewFlag ,jdbcType=VARCHAR},
                       #{keyStr,jdbcType=VARCHAR},
                       #{query.customId,jdbcType=NUMERIC}
                   )
    </select>
    <select id="callReplaceCodeMonthFunc" resultType="java.lang.String">
        select fin_dm_opt_foi.f_dm_fcst_ict_real_time_base_cus_mon_repl_cost_info_t (
                       #{query.costType,jdbcType=VARCHAR},
                       #{query.granularityType ,jdbcType=VARCHAR},
                       #{keyStr,jdbcType=VARCHAR},
                       #{query.customId,jdbcType=NUMERIC}
                   )
    </select>

    <select id="callReplaceCodeMonthYtdFuncTask" resultType="java.lang.String">
        select fin_dm_opt_foi.f_dm_fcst_ict_real_time_base_cus_ytd_repl_cost_info_t (
                       #{query.costType,jdbcType=VARCHAR},
                       #{query.granularityType ,jdbcType=VARCHAR},
                       #{keyStr,jdbcType=VARCHAR},
                       #{query.customId,jdbcType=NUMERIC}
                   )
    </select>

    <select id="callMonthTopSpartCostAmtFunc" resultType="java.lang.String">
        select fin_dm_opt_foi.f_dm_fcst_ict_top_base_cus_mon_repl_cost_info_t (
                       #{query.costType,jdbcType=VARCHAR},
                       #{query.granularityType ,jdbcType=VARCHAR},
                       #{keyStr,jdbcType=VARCHAR},
                       #{query.customId,jdbcType=NUMERIC}
                   )
    </select>

    <select id="callMonthTopSpartYtdCostAmtFunc" resultType="java.lang.String">
        select fin_dm_opt_foi.f_dm_fcst_ict_top_base_cus_ytd_repl_cost_info_t (
                       #{query.costType,jdbcType=VARCHAR},
                       #{query.granularityType ,jdbcType=VARCHAR},
                       #{keyStr,jdbcType=VARCHAR},
                       #{query.customId,jdbcType=NUMERIC}
                   )
    </select>

</mapper>
