/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.view.ViewInfoVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 功能描述 数据维度配置接口
 *
 * <AUTHOR>
 * @since 2023年03月13日
 */
public interface IProdGroupsViewDao {

    /**
     * 获取维度树型上报查询
     * @param viewInfoVO 查询条件
     * @param pageVO 分页对象
     * @return 数据分页对象
     * @throws ApplicationException 运行时异常
     */
    PagedResult<ViewInfoVO> getDimensionWithTreePageList(@Param("viewInfoVO") ViewInfoVO viewInfoVO, @Param("pageVO") PageVO pageVO) throws CommonApplicationException;

    /**
     * 获取维度树型上报查询
     * @param viewInfoVO 查询条件
     * @param pageVO 分页对象
     * @return 数据分页对象
     * @throws ApplicationException 运行时异常
     */
    PagedResult<ViewInfoVO> getProdDimensionWithTreePageList(@Param("viewInfoVO") ViewInfoVO viewInfoVO, @Param("pageVO") PageVO pageVO) throws CommonApplicationException;

    /**
     * 获取维度树型上报查询
     * @param viewInfoVO 查询条件
     * @param pageVO 分页对象
     * @return 数据分页对象
     * @throws ApplicationException 运行时异常
     */
    PagedResult<ViewInfoVO> getPriceDimensionWithTreePageList(@Param("viewInfoVO") ViewInfoVO viewInfoVO, @Param("pageVO") PageVO pageVO) throws CommonApplicationException;

    /**
     * 获取LV0/Lv1/lv2维度树型上报数据
     * @param dimensionValueList 查询条件
     * @return 列表信息查询
     */
    List<ViewInfoVO> getDimensionWithTree(@Param("dimensionValueList") List<String> dimensionValueList);


    /**
     * new ict 维度
     * @param dimensionValueList 查询条件
     * @return 列表信息查询
     */
    List<ViewInfoVO> getIctWithTree(@Param("dimensionValueList") List<String> dimensionValueList);

    /**
     * new ict prod 维度
     * @param dimensionValueList 查询条件
     * @return 列表信息查询
     */
    List<ViewInfoVO> getIctProdWithTree(@Param("dimensionValueList") List<String> dimensionValueList);

    /**
     * new ict 维度
     * @param dimensionValueList 查询条件
     * @return 列表信息查询
     */
    List<ViewInfoVO> getPriceProdWithTree(@Param("dimensionValueList") List<String> dimensionValueList);

    /**
     * 获取LV0/Lv1/lv2维度树型上报数据
     * @param dimensionDisplayList
     * @return 列表信息查询
     */
    int insertDimensionWithTree(List<ViewInfoVO> dimensionDisplayList);

    List<ViewInfoVO> getTotalLv2ProdRndTeamList(String tablePreFix);

    List<ViewInfoVO> getPurchaseLv2ProdRndTeamList(String tablePreFix);

    List<ViewInfoVO> getManuFactureLv2ProdRndTeamList(String tablePreFix);

    List<String> getIctDimensionValue(@Param("dimensionValueList") List<String> dimensionValueList);

    List<String> getIctLv1DimensionValue(@Param("dimensionValueList") List<String> dimensionValueList);

    List<String> getEnergyDimensionValue(@Param("dimensionValueList") List<String> dimensionValueList);

    List<String> getEnergyLv1DimensionValue(@Param("dimensionValueList") List<String> dimensionValueList);

    List<String> getIasDimensionValue(@Param("dimensionValueList") List<String> dimensionValueList);

    List<String> getIctIrbDimensionValue(@Param("dimensionValueList") List<String> dimensionValueList);

    List<String> getIctProdDimensionValue(@Param("dimensionValueList") List<String> dimensionValueList);

    List<String> getIctIndusDimensionValue(@Param("dimensionValueList") List<String> dimensionValueList);

    List<String> getPriceDimensionValue(@Param("dimensionValueList") List<String> dimensionValueList);

    List<ViewInfoVO> getLocationWithTree(@Param("dimensionValueList") List<String> dimensionValueList);

    PagedResult<ViewInfoVO> getLocationWithTreePageList(@Param("viewInfoVO") ViewInfoVO viewInfoVO, @Param("pageVO") PageVO pageVO);

    List<ViewInfoVO> getPriceLocationWithTree(@Param("dimensionValueList") List<String> dimensionValueList);

    PagedResult<ViewInfoVO> getPriceLocationWithTreePageList(@Param("viewInfoVO") ViewInfoVO viewInfoVO, @Param("pageVO") PageVO pageVO);

    List<ViewInfoVO> getKeyAndSubAccountWithTree(@Param("dimensionValueList") List<String> dimensionValueList);

    PagedResult<ViewInfoVO> getKeyAndSubAccountWithTreePageList(@Param("viewInfoVO") ViewInfoVO viewInfoVO, @Param("pageVO") PageVO pageVO);

}
