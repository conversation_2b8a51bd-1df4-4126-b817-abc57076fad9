/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.standard;

import com.huawei.it.fcst.annotations.ExportAttribute;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * OverviewAmpVO Class
 *
 * <AUTHOR>
 * @since 2024/11/1
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OverviewAmpVO {

    @ExportAttribute(sort = 0)
    private String periodYear;

    @ExportAttribute(sort = 1)
    private String groupCnName;

    private String groupCode;

    @ExportAttribute(sort = 2)
    private String sameWeightRate;

    @ExportAttribute(sort = 3)
    private String sameAnnualAmp;

    @ExportAttribute(sort = 4)
    private String sameWeightAnnualAmpPercent;

    @ExportAttribute(sort = 5)
    private String replWeightRate;

    @ExportAttribute(sort = 6)
    private String replAnnualAmp;

    @ExportAttribute(sort = 7)
    private String replWeightAnnualAmpPercent;

    @ExportAttribute(sort = 8)
    private String weightRate;

    @ExportAttribute(sort = 9)
    private String annualAmp;

}
