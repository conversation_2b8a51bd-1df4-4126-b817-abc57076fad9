/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.common.CommonViewVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;

import java.util.List;

/**
 * IDmFocViewInfoDao
 *
 * <AUTHOR>
 * @since 2023/3/14
 */
public interface IDmFocMadeDmsViewInfoDao {
    List<DmFocViewInfoVO> madeViewInfoList(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> madeViewInfoListForMonth(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> madeObjectViewInfoList(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> madeViewFlagInfoDmsList(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> madeViewInfoKeyWordList(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> madeViewInfoKeyWordForMonth(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> madeObjectViewInfoKeyWord(CommonViewVO commonViewVO);
}
