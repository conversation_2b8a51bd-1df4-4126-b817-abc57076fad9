/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.config;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

import java.util.Date;

/**
 * DmFocCatgCegIctDTOTest Class
 *
 * <AUTHOR>
 * @since 2023/4/23
 */
public class DmFocCatgCegIctDTOTest extends BaseVOCoverUtilsTest<DmFocCatgCegIctDTO> {

    @Override
    protected Class<DmFocCatgCegIctDTO> getTClass() {
        return DmFocCatgCegIctDTO.class;
    }

    @Test
    public void testMethod() {
        DmFocCatgCegIctDTO dimensionParamVO = new DmFocCatgCegIctDTO();
        dimensionParamVO.setVersionId(100L);
        dimensionParamVO.getVersionId();
        dimensionParamVO.setL3CegCode("2331L");
        dimensionParamVO.getL3CegCode();
        dimensionParamVO.setL3CegCnName("2331L");
        dimensionParamVO.getL3CegCnName();
        dimensionParamVO.setDelFlag("Y");
        dimensionParamVO.getDelFlag();
        dimensionParamVO.setCategoryCode("111");
        dimensionParamVO.getCategoryCode();
        dimensionParamVO.setCategoryCnName("test");
        dimensionParamVO.getCategoryCnName();
        dimensionParamVO.setL3CegShortCnName("short");
        dimensionParamVO.getL3CegShortCnName();
        dimensionParamVO.setLastUpdateDate(new Date());
        dimensionParamVO.getLastUpdateDate();
        dimensionParamVO.setSaveMethod("S");
        dimensionParamVO.getSaveMethod();
        dimensionParamVO.setL4CegCnName("模块");
        dimensionParamVO.setL4CegShortCnName("模块简称");
        dimensionParamVO.getL4CegShortCnName();
        dimensionParamVO.getL4CegCnName();
        dimensionParamVO.setLastUpdatedDate(new Date());
        dimensionParamVO.getLastUpdateDate();
        dimensionParamVO.builder().l3CegCode("1190D").build();
        Assert.assertNotNull(dimensionParamVO);
    }
}