/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.replace;

import cn.hutool.core.map.MapUtil;
import com.huawei.it.fcst.industry.index.constant.CommonConstant;
import com.huawei.it.fcst.industry.index.constant.Constant;
import com.huawei.it.fcst.industry.index.dao.IReplaceAmpDao;
import com.huawei.it.fcst.industry.index.dao.IStandardDao;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumU;
import com.huawei.it.fcst.industry.index.enums.IndustryConst;
import com.huawei.it.fcst.industry.index.enums.IndustryIndexEnum;
import com.huawei.it.fcst.industry.index.impl.common.CommonService;
import com.huawei.it.fcst.industry.index.impl.common.ReplaceDropDownService;
import com.huawei.it.fcst.industry.index.impl.month.AsyncQueryService;
import com.huawei.it.fcst.industry.index.utils.FcstIndexUtil;
import com.huawei.it.fcst.industry.index.utils.ObjectCopyUtil;
import com.huawei.it.fcst.industry.index.vo.annual.DmFocAnnualAmpVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;
import com.huawei.it.fcst.industry.index.vo.replace.DmReplaceAllAmpExp;
import com.huawei.it.fcst.industry.index.vo.replace.DmReplaceAllBindAmpExp;
import com.huawei.it.fcst.industry.index.vo.replace.DmReplaceMutliAmpExp;
import com.huawei.it.fcst.industry.index.vo.replace.ReplaceAnalysisVO;
import com.huawei.it.fcst.industry.index.vo.standard.CurrentAmpVO;
import com.huawei.it.fcst.industry.index.vo.standard.CurrentPriceIndexVO;
import com.huawei.it.fcst.industry.index.vo.standard.DistributeVO;
import com.huawei.it.fcst.industry.index.vo.standard.MultiAmpVO;
import com.huawei.it.fcst.industry.index.vo.standard.OverviewAmpVO;
import com.huawei.it.fcst.industry.index.vo.standard.StandardAnalysisVO;
import com.huawei.it.fcst.util.ExcelExportUtil;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.scheduling.annotation.EnableAsync;

import javax.inject.Named;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/11/30
 */
@Slf4j
@EnableAsync
@Named(value = "asyncAnnualExportService")
public class AsyncReplaceExportService {

    @Autowired
    private ReplaceDropDownService replaceDropDownService;

    @Autowired
    private IReplaceAmpDao replaceAmpDao;

    @Autowired
    private CommonService commonService;

    @Autowired
    private CommonAmpService commonAmpService;

    @Autowired
    private IStandardDao standardDao;

    @Autowired
    private AsyncQueryService asyncQueryService;

    /**
     * 填充制造成本sheet页数据
     *
     * @param searchParamsVO 参数VO
     * @param workbook 工作簿
     * @param sheetIdx sheet页下标
     * @param groupCnName groupCnName
     * @param current 当前请求上下文
     * @return Future<Integer> total rows
     * @throws Exception
     */
    @Async("asyncServiceExecutor")
    public Future<Integer> fillAmpSheet(Workbook workbook, int sheetIdx, String groupCnName,
                                        ReplaceAnalysisVO searchParamsVO, IRequestContext current) throws Exception {
        log.info(">>>Begin AsyncAnnualExportService::fillAmpSheet");
        // 1、查询制造成本涨跌图导出数据
        RequestContextManager.setCurrent(current);
        ReplaceAnalysisVO replaceAmpVO = ObjectCopyUtil.copy(searchParamsVO, ReplaceAnalysisVO.class);
        List<String> threeYears = replaceDropDownService.getThreeYears();
        replaceAmpVO.setYearList(threeYears);
        List<DmFocAnnualAmpVO> currentAnnualAmpList = replaceAmpDao.allReplaceAmpCost(replaceAmpVO);
        commonAmpService.setNoEffectiveAmp(currentAnnualAmpList, "excel");
        for (DmFocAnnualAmpVO currentAnnualAmp : currentAnnualAmpList) {
            if (threeYears.get(0).equals(currentAnnualAmp.getPeriodYear())) {
                currentAnnualAmp.setPeriodYear(currentAnnualAmp.getPeriodYear() + " YTD");
            }
        }
        DmFocViewInfoVO dmFocViewInfoVO = getViewInfoFlag(replaceAmpVO.getViewFlag());
        String ampTitle = FcstIndexUtil.getTitle(Constant.StrEnum.REPLACE_AMP_TITLE.getValue(), groupCnName);
        Sheet ampSheet = workbook.getSheetAt(sheetIdx);
        ampSheet.getRow(0).getCell(0).setCellValue(ampTitle);
        ampSheet.getRow(0).getCell(2).setCellValue(Constant.StrEnum.CALIBER_FLAG.getValue() +  IndustryIndexEnum.getCaliberFlag(replaceAmpVO.getCaliberFlag()).getDesc());
        ampSheet.getRow(1).getCell(2).setCellValue("视角: " + dmFocViewInfoVO.getViewFlagValue());
        // 填充采购价格指数图Sheet数据
        new ExcelExportUtil<DmFocAnnualAmpVO>(2, 2, DmFocAnnualAmpVO.class).fillSheetData(ampSheet,
                currentAnnualAmpList);
        ampSheet.getRow(2).getCell(2).setCellValue("本期实际数截止月：" + standardDao.findActualMonthNum().toString());
        log.info(">>>End AsyncAnnualExportService::fillAmpSheet");
        return new AsyncResult<>(currentAnnualAmpList.size());
    }

    /**
     * 填充制造成本sheet页数据
     *
     * @param searchParamsVO 参数VO
     * @param workbook 工作簿
     * @param sheetIdx sheet页下标
     * @param groupCnName groupCnName
     * @param current 当前请求上下文
     * @return Future<Integer> total rows
     * @throws Exception
     */
    @Async("asyncServiceExecutor")
    public Future<Integer> fillMultiAmpSheet(Workbook workbook, int sheetIdx, String groupCnName,
                                             ReplaceAnalysisVO searchParamsVO, IRequestContext current) throws Exception {
        log.info(">>>Begin AsyncAnnualExportService::fillMultiAmpSheet");
        // 1、查询制造成本涨跌图导出数据
        RequestContextManager.setCurrent(current);
        ReplaceAnalysisVO replaceAmpVO = ObjectCopyUtil.copy(searchParamsVO, ReplaceAnalysisVO.class);
        List<String> threeYears = replaceDropDownService.getThreeYears();
        replaceAmpVO.setYearList(threeYears);
        List<DmFocAnnualAmpVO> currentAnnualAmpList = replaceAmpDao.allReplaceAmpCost(replaceAmpVO);
        commonAmpService.setNoEffectiveAmp(currentAnnualAmpList, "excel");
        for (DmFocAnnualAmpVO currentAnnualAmp : currentAnnualAmpList) {
            if (threeYears.get(0).equals(currentAnnualAmp.getPeriodYear())) {
                currentAnnualAmp.setPeriodYear(currentAnnualAmp.getPeriodYear() + " YTD");
            }
        }
        List<DmReplaceMutliAmpExp> multiAmpExpList = new ArrayList<>();
        Optional.ofNullable(currentAnnualAmpList).orElse(new ArrayList<>()).stream().forEach(item -> {
            String name = item.getGroupCnName();
            if ("BIND".equals(replaceAmpVO.getGroupLevel())) {
                name = item.getGroupCnName() + "(" + item.getParentCnName() +")";
            }
            DmReplaceMutliAmpExp dmReplaceMultiAmpExp = DmReplaceMutliAmpExp.builder()
                    .periodYear(item.getPeriodYear())
                    .annualAmp(item.getAnnualAmp())
                    .groupCnName(name).build();
            multiAmpExpList.add(dmReplaceMultiAmpExp);
        });
        // bing组的名称需要改变一下
        String groupName;
        if ("BIND".equals(replaceAmpVO.getGroupLevel())) {
            groupName = "BIND";
        } else {
            groupName = GroupLevelEnumU.getInstance(replaceAmpVO.getGroupLevel()).getName();
        }
        DmFocViewInfoVO dmFocViewInfoVO = getViewInfoFlag(replaceAmpVO.getViewFlag());
        String ampTitle = FcstIndexUtil.getTitle(Constant.StrEnum.REPLACE_AMP_TITLE.getValue(), groupCnName);
        Sheet ampSheet = workbook.getSheetAt(sheetIdx);
        ampSheet.getRow(0).getCell(0).setCellValue(ampTitle);
        ampSheet.getRow(0).getCell(3).setCellValue(Constant.StrEnum.CALIBER_FLAG.getValue() +  IndustryIndexEnum.getCaliberFlag(replaceAmpVO.getCaliberFlag()).getDesc());
        ampSheet.getRow(1).getCell(1).setCellValue(FcstIndexUtil.getTitle(Constant.StrEnum.HEAP_TITLE2.getValue(), groupName));
        ampSheet.getRow(1).getCell(3).setCellValue("视角: " + dmFocViewInfoVO.getViewFlagValue());
        // 填充采购价格指数图Sheet数据
        new ExcelExportUtil<DmReplaceMutliAmpExp>(2, 2, DmReplaceMutliAmpExp.class).fillSheetData(ampSheet,
                multiAmpExpList);
        ampSheet.getRow(2).getCell(3).setCellValue("本期实际数截止月：" + standardDao.findActualMonthNum().toString());
        log.info(">>>End AsyncAnnualExportService::fillMultiAmpSheet");
        return new AsyncResult<>(currentAnnualAmpList.size());
    }

    @NotNull
    private DmFocViewInfoVO getViewInfoFlag(String viewFlag) throws ApplicationException {
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setViewFlag(viewFlag);
        dmFocViewInfoVO.setGranularityType(IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue());
        dmFocViewInfoVO.setIndustryOrg(IndustryConst.INDUSTRY_ORG.ICT.getValue());
        dmFocViewInfoVO.setCostType(IndustryIndexEnum.COST_TYPE.T.getValue());
        commonService.setViewFlagValueWithLookUp(dmFocViewInfoVO);
        return dmFocViewInfoVO;
    }

    /**
     * 一览表非bind层级填充制造成本sheet页数据
     *
     * @param searchParamsVO 参数VO
     * @param workbook 工作簿
     * @param sheetIdx sheet页下标
     * @param groupCnName groupCnName
     * @param current 当前请求上下文
     * @return Future<Integer> total rows
     * @throws Exception
     */
    @Async("asyncServiceExecutor")
    public Future<Integer> fillAllAmpSheet(Workbook workbook, int sheetIdx, String groupCnName,
                                           ReplaceAnalysisVO searchParamsVO, IRequestContext current) throws Exception {
        log.info(">>>Begin AsyncReplaceExportService::fillAllAmpSheet");
        // 1、查询制造成本一览表导出数据
        RequestContextManager.setCurrent(current);
        ReplaceAnalysisVO replaceAllAmpVO = ObjectCopyUtil.copy(searchParamsVO, ReplaceAnalysisVO.class);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpResult = getAllAmpList(replaceAllAmpVO);
        List<DmReplaceAllAmpExp> dmReplaceAllAmpExpList = new ArrayList<>();
        Optional.of(dmFocAnnualAmpResult).orElse(new ArrayList<>()).forEach(item -> {
            String name = item.getGroupCnName();
            if ("BIND".equals(replaceAllAmpVO.getGroupLevel())) {
                name = item.getGroupCnName() + "(" + item.getParentCnName() +")";
            }
            DmReplaceAllAmpExp dmReplaceAllAmpExp = DmReplaceAllAmpExp.builder()
                    .periodYear(item.getPeriodYear())
                    .annualAmp(item.getAnnualAmp())
                    .groupCnName(name)
                    .weightAnnualAmpPercent(item.getWeightAnnualAmpPercent())
                    .weightRate(item.getWeightRate()).build();
            dmReplaceAllAmpExpList.add(dmReplaceAllAmpExp);
        });
        String ampTitle = FcstIndexUtil.getTitle(Constant.StrEnum.REPLACE_MULTI_AMP_TITLE.getValue(), groupCnName);
        // bing组的名称需要改变一下
        String groupName;
        if ("BIND".equals(replaceAllAmpVO.getGroupLevel())) {
            groupName = "BIND";
        } else {
            groupName = GroupLevelEnumU.getInstance(replaceAllAmpVO.getGroupLevel()).getName();
        }
        DmFocViewInfoVO dmFocViewInfoVO = getViewInfoFlag(replaceAllAmpVO.getViewFlag());
        Sheet ampSheet = workbook.getSheetAt(sheetIdx);
        ampSheet.getRow(0).getCell(0).setCellValue(ampTitle);
        ampSheet.getRow(0).getCell(5).setCellValue(Constant.StrEnum.CALIBER_FLAG.getValue() +  IndustryIndexEnum.getCaliberFlag(replaceAllAmpVO.getCaliberFlag()).getDesc());
        ampSheet.getRow(1).getCell(1).setCellValue(FcstIndexUtil.getTitle(Constant.StrEnum.HEAP_TITLE2.getValue(), groupName));
        ampSheet.getRow(1).getCell(5).setCellValue("视角: " + dmFocViewInfoVO.getViewFlagValue());
        // 填充制造成本一览表Sheet数据
        new ExcelExportUtil<DmReplaceAllAmpExp>(2, 2, DmReplaceAllAmpExp.class).fillSheetData(ampSheet,
                dmReplaceAllAmpExpList);
        ampSheet.getRow(2).getCell(5).setCellValue("本期实际数截止月：" + standardDao.findActualMonthNum().toString());
        log.info(">>>End AsyncReplaceExportService::fillAllAmpSheet");
        return new AsyncResult<>(dmReplaceAllAmpExpList.size());
    }


    /**
     * 一览表bind层级填充制造成本sheet页数据
     *
     * @param searchParamsVO 参数VO
     * @param workbook 工作簿
     * @param sheetIdx sheet页下标
     * @param groupCnName groupCnName
     * @param current 当前请求上下文
     * @return Future<Integer> total rows
     * @throws Exception
     */
    @Async("asyncServiceExecutor")
    public Future<Integer> fillAllBindAmpSheet(Workbook workbook, int sheetIdx, String groupCnName,
                                           ReplaceAnalysisVO searchParamsVO, IRequestContext current) throws Exception {
        log.info(">>>Begin AsyncReplaceExportService::fillAllBindAmpSheet");
        // 1、查询制造成本一览表导出数据
        RequestContextManager.setCurrent(current);
        ReplaceAnalysisVO replaceAllAmpVO = ObjectCopyUtil.copy(searchParamsVO, ReplaceAnalysisVO.class);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpResult = getAllAmpList(replaceAllAmpVO);
        List<DmReplaceAllBindAmpExp> dmReplaceAllAmpExpList = new ArrayList<>();
        Optional.of(dmFocAnnualAmpResult).orElse(new ArrayList<>()).forEach(item -> {
            DmReplaceAllBindAmpExp dmReplaceAllAmpExp = DmReplaceAllBindAmpExp.builder()
                    .periodYear(item.getPeriodYear())
                    .annualAmp(item.getAnnualAmp())
                    .groupCode(item.getGroupCode())
                    .groupCnName(item.getGroupCnName() + "(" + item.getParentCnName() +")")
                    .weightAnnualAmpPercent(item.getWeightAnnualAmpPercent())
                    .weightRate(item.getWeightRate()).build();
            dmReplaceAllAmpExpList.add(dmReplaceAllAmpExp);
        });
        String ampTitle = FcstIndexUtil.getTitle(Constant.StrEnum.REPLACE_MULTI_AMP_TITLE.getValue(), groupCnName);
        // bing组的名称需要改变一下
        DmFocViewInfoVO dmFocViewInfoVO = getViewInfoFlag(replaceAllAmpVO.getViewFlag());
        Sheet ampSheet = workbook.getSheetAt(sheetIdx);
        ampSheet.getRow(0).getCell(0).setCellValue(ampTitle);
        ampSheet.getRow(0).getCell(6).setCellValue(Constant.StrEnum.CALIBER_FLAG.getValue() +  IndustryIndexEnum.getCaliberFlag(replaceAllAmpVO.getCaliberFlag()).getDesc());
        ampSheet.getRow(1).getCell(1).setCellValue(FcstIndexUtil.getTitle(Constant.StrEnum.HEAP_TITLE3.getValue(), "BIND"));
        ampSheet.getRow(1).getCell(2).setCellValue(FcstIndexUtil.getTitle(Constant.StrEnum.HEAP_TITLE2.getValue(), "BIND"));
        ampSheet.getRow(1).getCell(6).setCellValue("视角: " + dmFocViewInfoVO.getViewFlagValue());
        // 填充制造成本一览表Sheet数据
        new ExcelExportUtil<DmReplaceAllBindAmpExp>(2, 2, DmReplaceAllBindAmpExp.class).fillSheetData(ampSheet,
                dmReplaceAllAmpExpList);
        ampSheet.getRow(2).getCell(6).setCellValue("本期实际数截止月：" + standardDao.findActualMonthNum().toString());
        log.info(">>>End AsyncReplaceExportService::fillAllBindAmpSheet");
        return new AsyncResult<>(dmReplaceAllAmpExpList.size());
    }

    @NotNull
    private List<DmFocAnnualAmpVO> getAllAmpList(ReplaceAnalysisVO replaceAllAmpVO) {
        List <DmFocAnnualAmpVO> allAmpList = new ArrayList<>();
        String nextGroupLevel;
        if ("BIND".equals(replaceAllAmpVO.getParentLevel())) {
            nextGroupLevel = "BIND";
        } else {
            if ("LV3".equals(replaceAllAmpVO.getParentLevel())) {
                nextGroupLevel = "BIND";
            } else {
                nextGroupLevel = FcstIndexUtil.getNextGroupLevelByView(replaceAllAmpVO.getViewFlag(), replaceAllAmpVO.getParentLevel(), "U", "ICT");
            }
            replaceAllAmpVO.setParentCodeList(replaceAllAmpVO.getGroupCodeList());
            replaceAllAmpVO.setGroupCodeList(null);
        }
        replaceAllAmpVO.setGroupLevel(nextGroupLevel);
        List<String> threeYears = replaceDropDownService.getThreeYears();
        replaceAllAmpVO.setYearList(threeYears);
        new ArrayList<>();
        // 默认按照权重大小排序
        String ytdYear = "";
        if (CollectionUtils.isNotEmpty(threeYears)) {
            ytdYear = threeYears.get(0);
        }
        replaceAllAmpVO.setDataType("TOTAL");
        List<DmFocAnnualAmpVO> annualAmpAndWeightList = replaceAmpDao.industryReplaceCostList(replaceAllAmpVO);
        // 处理各层级的权重和权重*涨跌
        for (String year : threeYears) {
            replaceAllAmpVO.setYear(year);
            // 筛选出对应年份
            List<DmFocAnnualAmpVO> annualAmpAndWeightYear = annualAmpAndWeightList.stream().filter(annual -> replaceAllAmpVO.getYear().equals(annual.getPeriodYear())).collect(Collectors.toList());
            // item层级按照权重*涨跌大小排序；各层级权重排序;获取权重*涨跌最大值
            List<DmFocAnnualAmpVO> dmFocAnnualAmpResult = commonAmpService.dealGroupLevelWeightAndAmp(annualAmpAndWeightYear, replaceAllAmpVO, false);
            // 判断当statuscode存在时，涨跌幅需要设置成null
            commonAmpService.setNoEffectiveAmp(dmFocAnnualAmpResult, "excel");
            allAmpList.addAll(dmFocAnnualAmpResult);
        }
        return allAmpList;
    }

    @Async("asyncServiceExecutor")
    public Future<Integer> fillStandDistributeSheet(Workbook workbook, int sheetIdx, String groupCnName,
                                                    StandardAnalysisVO standardAnalysisVO, IRequestContext current) throws Exception {
        log.info(">>>Begin AsyncAnnualExportService::fillStandDistributeSheet");
        // 1、查询制造成本涨跌图导出数据
        RequestContextManager.setCurrent(current);
        StandardAnalysisVO analysisVO = ObjectCopyUtil.copy(standardAnalysisVO, StandardAnalysisVO.class);

        List<DmFocAnnualAmpVO> distributeCostList = standardDao.distributeAmpCostChart(analysisVO);
        List<String> threeYears = analysisVO.getYearList();
        for (DmFocAnnualAmpVO distribute : distributeCostList) {
            String periodYear = null;
            if (CollectionUtils.isNotEmpty(threeYears)) {
                periodYear = threeYears.get(0);
            }
            if (StringUtils.isNotBlank(distribute.getPeriodYear())) {
                if (distribute.getPeriodYear().equals(periodYear)) {
                    distribute.setPeriodYear(distribute.getPeriodYear() + " YTD");
                }
            }
        }
        List<DistributeVO> distributeVOList = new ArrayList<>();
        distributeCostList.forEach(item -> {
            DistributeVO distributeVO = DistributeVO.builder()
                    .periodYear(item.getPeriodYear())
                    .groupCnName(item.getGroupCnName())
                    .rmbCostPer(item.getRmbCostPer()).build();
            distributeVOList.add(distributeVO);
        });
        String viewFlag = analysisVO.getViewFlag();
        DmFocViewInfoVO dmFocViewInfoVO = getViewInfoFlag(viewFlag);
        String title = FcstIndexUtil.getTitle(Constant.StrEnum.INDUSTRY_REPALCE_RMB_COST_PER_CHART.getValue(), groupCnName);
        Sheet distributeSheet = workbook.getSheetAt(sheetIdx);
        distributeSheet.getRow(0).getCell(0).setCellValue(title);
        distributeSheet.getRow(0).getCell(3).setCellValue(Constant.StrEnum.CALIBER_FLAG.getValue() +  IndustryIndexEnum.getCaliberFlag(analysisVO.getCaliberFlag()).getDesc());
        distributeSheet.getRow(1).getCell(3).setCellValue("视角: " + dmFocViewInfoVO.getViewFlagValue());
        // 填充Sheet数据
        new ExcelExportUtil<DistributeVO>(2, 2, DistributeVO.class).fillSheetData(distributeSheet,
                distributeVOList);
        distributeSheet.getRow(2).getCell(3).setCellValue(Constant.StrEnum.CURRENT_PERIOD.getValue() + standardDao.findActualMonthNum().toString());
        log.info(">>>End AsyncAnnualExportService::fillStandDistributeSheet");
        return new AsyncResult<>(distributeVOList.size());
    }

    @Async("asyncServiceExecutor")
    public Future<Integer> fillStandCurrentAmpSheet(Workbook workbook, int sheetIdx, String groupCnName,
                                                    StandardAnalysisVO standardAnalysisVO, IRequestContext current) throws Exception {
        log.info(">>>Begin AsyncAnnualExportService::fillStandCurrentAmpSheet");
        // 1、查询制造成本涨跌图导出数据
        RequestContextManager.setCurrent(current);
        StandardAnalysisVO standardVO = ObjectCopyUtil.copy(standardAnalysisVO, StandardAnalysisVO.class);
        List<DmFocAnnualAmpVO> currentAnnualAmpList = new ArrayList<>();
        asyncQueryService.findCurrentStandDataList(currentAnnualAmpList, standardVO);
        commonAmpService.setNoEffectiveAmp(currentAnnualAmpList, "excel");
        List<String> threeYears = standardVO.getYearList();
        List<CurrentAmpVO> currentAmpVOList = new ArrayList<>();
        for (DmFocAnnualAmpVO currentAmp : currentAnnualAmpList) {
            String periodYear = null;
            if (CollectionUtils.isNotEmpty(threeYears)) {
                periodYear = threeYears.get(0);
            }
            if (StringUtils.isNotBlank(currentAmp.getPeriodYear())) {
                if (currentAmp.getPeriodYear().equals(periodYear)) {
                    currentAmp.setPeriodYear(currentAmp.getPeriodYear() + " YTD");
                }
            }
            currentAmp.setCostTypeValue(IndustryIndexEnum.getStandCostType(currentAmp.getCostType()).getDesc());
            CurrentAmpVO currentAmpVO = CurrentAmpVO.builder()
                    .periodYear(currentAmp.getPeriodYear())
                    .costType(currentAmp.getCostTypeValue())
                    .groupCnName(currentAmp.getGroupCnName())
                    .annualAmp(currentAmp.getAnnualAmp()).build();
            currentAmpVOList.add(currentAmpVO);
        }
        String viewFlag = standardVO.getViewFlag();
        DmFocViewInfoVO dmFocViewInfoVO = getViewInfoFlag(viewFlag);
        String ampTitle = FcstIndexUtil.getTitle(Constant.StrEnum.INDUSTRY_INDEX_AMP_CHART.getValue(), groupCnName);
        Sheet ampSheet = workbook.getSheetAt(sheetIdx);
        ampSheet.getRow(0).getCell(0).setCellValue(ampTitle);
        ampSheet.getRow(0).getCell(4).setCellValue(Constant.StrEnum.CALIBER_FLAG.getValue() +  IndustryIndexEnum.getCaliberFlag(standardVO.getCaliberFlag()).getDesc());
        ampSheet.getRow(1).getCell(4).setCellValue("视角: " + dmFocViewInfoVO.getViewFlagValue());
        // 填充Sheet数据
        new ExcelExportUtil<CurrentAmpVO>(2, 2, CurrentAmpVO.class).fillSheetData(ampSheet,
                currentAmpVOList);
        ampSheet.getRow(2).getCell(4).setCellValue(Constant.StrEnum.CURRENT_PERIOD.getValue() + standardDao.findActualMonthNum().toString());
        log.info(">>>End AsyncAnnualExportService::fillStandCurrentAmpSheet");
        return new AsyncResult<>(currentAmpVOList.size());
    }

    @Async("asyncServiceExecutor")
    public Future<Integer> fillStandMultiAmpSheet(Workbook workbook, int sheetIdx, String groupCnName,
                                                  StandardAnalysisVO standardAnalysisVO, IRequestContext current) throws Exception {
        log.info(">>>Begin AsyncAnnualExportService::fillStandMultiAmpSheet");
        // 1、查询制造成本涨跌图导出数据
        RequestContextManager.setCurrent(current);
        StandardAnalysisVO standardVO = ObjectCopyUtil.copy(standardAnalysisVO, StandardAnalysisVO.class);
        standardVO.setParentLevel(standardVO.getGroupLevel());
        standardVO.setParentCodeList(standardVO.getGroupCodeList());
        String groupLevel = FcstIndexUtil.getNextGroupLevelByView(standardVO.getViewFlag(),
                standardVO.getParentLevel(), "U", "ICT");
        standardVO.setGroupLevel(groupLevel);
        resetProdRndTeamCodeList(standardVO);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpResultList = new ArrayList<>();
        asyncQueryService.findMultiStandDataList(dmFocAnnualAmpResultList, standardVO);
        commonAmpService.setNoEffectiveAmp(dmFocAnnualAmpResultList, "excel");
        // 判断当statuscode存在时，涨跌幅需要设置成null
        LinkedHashMap<String, List<DmFocAnnualAmpVO>> resultColumnMap = dmFocAnnualAmpResultList.stream().collect(
                Collectors.groupingBy(this::getStandGroupKey, LinkedHashMap::new, Collectors.toList()));
        Set<String> resultRowSet = resultColumnMap.keySet();
        List<Map<String, String>> dataList = new ArrayList<>();
        List<String> periodYears = standardVO.getYearList();
        if (standardVO.getIsMultipleSelect()) {
            List<String> yearList = new ArrayList<>();
            yearList.add(String.valueOf(standardVO.getPeriodYear()));
            periodYears = yearList;
        } else {
            // 最近的三年
            standardVO.setYearList(periodYears);
        }
        setStandardDataList(dataList, periodYears, resultColumnMap, resultRowSet);
        List<MultiAmpVO> multiAmpVOList = new ArrayList<>();
        dataList.forEach(data->{
            MultiAmpVO multiAmpVO = MultiAmpVO.builder()
                    .groupCnName(data.get("groupCnName"))
                    .costType(data.get("costType"))
                    .annualAmp0(data.get("annualAmp0"))
                    .annualAmp1(data.get("annualAmp1"))
                    .annualAmp2(data.get("annualAmp2"))
                    .build();
            multiAmpVOList.add(multiAmpVO);
        });
        DmFocViewInfoVO dmFocViewInfoVO = getViewInfoFlag(standardVO.getViewFlag());
        String multiAmpTitle = FcstIndexUtil.getTitle(Constant.StrEnum.INDUSTRY_INDEX_AMP_CHART.getValue(), groupCnName);
        Sheet multiAmpSheet = workbook.getSheetAt(sheetIdx);
        multiAmpSheet.getRow(0).getCell(0).setCellValue(multiAmpTitle);
        multiAmpSheet.getRow(0).getCell(5).setCellValue(Constant.StrEnum.CALIBER_FLAG.getValue() +  IndustryIndexEnum.getCaliberFlag(standardVO.getCaliberFlag()).getDesc());
        multiAmpSheet.getRow(1).getCell(5).setCellValue("视角: " + dmFocViewInfoVO.getViewFlagValue());
        // 填充Sheet数据
        new ExcelExportUtil<MultiAmpVO>(2, 2, MultiAmpVO.class).fillSheetData(multiAmpSheet,
                multiAmpVOList);
        multiAmpSheet.getRow(2).getCell(5).setCellValue(Constant.StrEnum.CURRENT_PERIOD.getValue() + standardDao.findActualMonthNum().toString());
        log.info(">>>End AsyncAnnualExportService::fillStandMultiAmpSheet");
        return new AsyncResult<>(multiAmpVOList.size());
    }

    @Async("asyncServiceExecutor")
    public Future<Integer> fillMultiSelectAmpSheet(Workbook workbook, int sheetIdx, String groupCnName,
                                                   StandardAnalysisVO analysisVO, IRequestContext current) throws Exception {
        log.info(">>>Begin AsyncAnnualExportService::fillMultiSelectAmpSheet");
        // 1、查询制造成本涨跌图导出数据
        RequestContextManager.setCurrent(current);
        StandardAnalysisVO standardAnalysisVO = ObjectCopyUtil.copy(analysisVO, StandardAnalysisVO.class);
        standardAnalysisVO.setParentLevel(standardAnalysisVO.getGroupLevel());
        standardAnalysisVO.setParentCodeList(standardAnalysisVO.getGroupCodeList());
        String groupLevel = FcstIndexUtil.getNextGroupLevelByView(standardAnalysisVO.getViewFlag(),
                standardAnalysisVO.getParentLevel(), "U", "ICT");
        standardAnalysisVO.setGroupLevel(groupLevel);
        resetProdRndTeamCodeList(standardAnalysisVO);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpResultList = new ArrayList<>();
        asyncQueryService.findMultiStandDataList(dmFocAnnualAmpResultList, standardAnalysisVO);
        commonAmpService.setNoEffectiveAmp(dmFocAnnualAmpResultList, "excel");
        // 判断当statuscode存在时，涨跌幅需要设置成null
        LinkedHashMap<String, List<DmFocAnnualAmpVO>> resultColumnMap = dmFocAnnualAmpResultList.stream().collect(
                Collectors.groupingBy(this::getStandGroupKey, LinkedHashMap::new, Collectors.toList()));
        Set<String> resultSet = resultColumnMap.keySet();
        List<Map<String, String>> dataList = new ArrayList<>();
        List<String> periodYears = standardAnalysisVO.getYearList();
        if (standardAnalysisVO.getIsMultipleSelect()) {
            List<String> yearList = new ArrayList<>();
            yearList.add(String.valueOf(standardAnalysisVO.getPeriodYear()));
            periodYears = yearList;
        } else {
            // 最近的三年
            standardAnalysisVO.setYearList(periodYears);
        }
        setStandardDataList(dataList, periodYears, resultColumnMap, resultSet);
        List<MultiAmpVO> multiAmpVOList = new ArrayList<>();
        dataList.forEach(data->{
            MultiAmpVO multiAmpVO = MultiAmpVO.builder()
                    .groupCnName(data.get("groupCnName"))
                    .costType(data.get("costType"))
                    .annualAmp0(data.get("annualAmp0"))
                    .build();
            multiAmpVOList.add(multiAmpVO);
        });
        DmFocViewInfoVO dmFocViewInfoVO = getViewInfoFlag(standardAnalysisVO.getViewFlag());
        String multiAmpTitle = FcstIndexUtil.getTitle(Constant.StrEnum.INDUSTRY_INDEX_AMP_CHART.getValue(), groupCnName);
        Sheet multiAmpSheet = workbook.getSheetAt(sheetIdx);
        multiAmpSheet.getRow(0).getCell(0).setCellValue(multiAmpTitle);
        multiAmpSheet.getRow(0).getCell(3).setCellValue(Constant.StrEnum.CALIBER_FLAG.getValue() +  IndustryIndexEnum.getCaliberFlag(standardAnalysisVO.getCaliberFlag()).getDesc());
        multiAmpSheet.getRow(1).getCell(3).setCellValue("视角: " + dmFocViewInfoVO.getViewFlagValue());
        // 填充Sheet数据
        new ExcelExportUtil<MultiAmpVO>(2, 2, MultiAmpVO.class).fillSheetData(multiAmpSheet,
                multiAmpVOList);
        multiAmpSheet.getRow(2).getCell(3).setCellValue(Constant.StrEnum.CURRENT_PERIOD.getValue() + standardDao.findActualMonthNum().toString());
        log.info(">>>End AsyncAnnualExportService::fillMultiSelectAmpSheet");
        return new AsyncResult<>(multiAmpVOList.size());
    }

    @Async("asyncServiceExecutor")
    public Future<Integer> fillOverviewAmpSheet(Workbook workbook, int sheetIdx, String groupCnName,
                                                StandardAnalysisVO standardAnalysisVO, IRequestContext current) throws Exception {
        log.info(">>>Begin AsyncAnnualExportService::fillOverviewAmpSheet");
        // 1、查询制造成本涨跌图导出数据
        RequestContextManager.setCurrent(current);
        StandardAnalysisVO standardVO = ObjectCopyUtil.copy(standardAnalysisVO, StandardAnalysisVO.class);

        List<String> threeYears = standardVO.getYearList();
        standardVO.setParentLevel(standardVO.getGroupLevel());
        String groupLevel = FcstIndexUtil.getNextGroupLevelByView(standardVO.getViewFlag(),
                standardVO.getParentLevel(), "U", "ICT");
        standardVO.setGroupLevel(groupLevel);
        // 根据groupLevel计算子项level
        resetProdRndTeamCodeList(standardVO);
        standardVO.setParentCodeList(standardVO.getGroupCodeList());
        // 默认按照权重大小排序
        List<DmFocAnnualAmpVO> annualAmpWeightList = new ArrayList<>();
        List<String> periodYearList = new ArrayList<>();
        if (standardVO.getIsMultipleSelect()) {
            periodYearList.add(String.valueOf(standardVO.getPeriodYear()));
        } else {
            // 最近的三年
            periodYearList = threeYears;
        }
        for (int i = periodYearList.size() - 1; i >= 0; i--) {
            List<DmFocAnnualAmpVO> annualAmpList = new ArrayList<>();
            standardVO.setYear(periodYearList.get(i));
            standardVO.setDataType("TOTAL");
            asyncQueryService.findOverviewDataList(annualAmpList, standardVO);
            annualAmpList.forEach(annual->annual.setDataType(standardVO.getDataType()));
            annualAmpWeightList.addAll(annualAmpList);
        }
        commonAmpService.setNoEffectiveAmp(annualAmpWeightList, "excel");
        List<DmFocAnnualAmpVO> ananualAmpVOList = new ArrayList<>();
        Map<String, List<DmFocAnnualAmpVO>> mapDmFocAnnualAmpList = annualAmpWeightList.stream().collect(
                Collectors.groupingBy(item -> item.getGroupCode() +"_"+ item.getGroupCnName() + "_" +item.getPeriodYear() , HashMap::new, Collectors.toList()));
        // 三个成本的数据汇总在一起
        commonAmpService.setAnnaulAmpVOList(ananualAmpVOList, mapDmFocAnnualAmpList);
        List<OverviewAmpVO> overviewAmpList = new ArrayList<>();
        List<String> yearList = threeYears.stream().sorted(String::compareTo).collect(Collectors.toList());
        for (String year : yearList) {
            standardVO.setYear(year);
            // 筛选出对应年份
            List<DmFocAnnualAmpVO> annualAmpAndWeightYear = ananualAmpVOList.stream().filter(annual -> standardVO.getYear().equals(annual.getPeriodYear())).collect(Collectors.toList());
            // 权重排序
            commonAmpService.sortByWeight(annualAmpAndWeightYear);
            // 处理各层级的权重和权重*涨跌
            concatDataPercent(annualAmpAndWeightYear);
            annualAmpAndWeightYear.forEach(data->{
                OverviewAmpVO overviewAmpVO = OverviewAmpVO.builder()
                        .periodYear(data.getPeriodYear())
                        .groupCnName(data.getGroupCnName())
                        .weightRate(data.getWeightRate())
                        .annualAmp(data.getAnnualAmp())
                        .replWeightRate(data.getReplWeightRate())
                        .replAnnualAmp(data.getReplAnnualAmp())
                        .sameWeightRate(data.getSameWeightRate())
                        .sameAnnualAmp(data.getSameAnnualAmp())
                        .replWeightAnnualAmpPercent(data.getReplWeightAnnualAmpPercent())
                        .sameWeightAnnualAmpPercent(data.getSameWeightAnnualAmpPercent())
                        .build();
                overviewAmpList.add(overviewAmpVO);
            });
        }
        DmFocViewInfoVO dmFocViewInfoVO = getViewInfoFlag(standardVO.getViewFlag());
        String overviewAmpTitle = FcstIndexUtil.getTitle(Constant.StrEnum.INDUSTRY_INDEX_AMP_CHART.getValue(), groupCnName);
        Sheet overviewAmpSheet = workbook.getSheetAt(sheetIdx);
        overviewAmpSheet.getRow(0).getCell(0).setCellValue(overviewAmpTitle);
        overviewAmpSheet.getRow(0).getCell(10).setCellValue(Constant.StrEnum.CALIBER_FLAG.getValue() +  IndustryIndexEnum.getCaliberFlag(standardVO.getCaliberFlag()).getDesc());
        overviewAmpSheet.getRow(1).getCell(10).setCellValue("视角: " + dmFocViewInfoVO.getViewFlagValue());
        // 填充Sheet数据
        new ExcelExportUtil<OverviewAmpVO>(2, 2, OverviewAmpVO.class).fillSheetData(overviewAmpSheet,
                overviewAmpList);
        overviewAmpSheet.getRow(2).getCell(10).setCellValue(Constant.StrEnum.CURRENT_PERIOD.getValue() + standardDao.findActualMonthNum().toString());
        log.info(">>>End AsyncAnnualExportService::fillOverviewAmpSheet");
        return new AsyncResult<>(overviewAmpList.size());
    }

    private void concatDataPercent(List<DmFocAnnualAmpVO> annualAmpList) {
        annualAmpList.stream().forEach(annualAmpVO -> {
            String annualAmp = annualAmpVO.getAnnualAmp();
            String weightRate = annualAmpVO.getWeightRate();
            String replAnnualAmp = annualAmpVO.getReplAnnualAmp();
            String replWeightRate = annualAmpVO.getReplWeightRate();
            String sameAnnualAmp = annualAmpVO.getSameAnnualAmp();
            String sameWeightRate = annualAmpVO.getSameWeightRate();
            String sameWeightAnnualAmpPercent = annualAmpVO.getSameWeightAnnualAmpPercent();
            String replWeightAnnualAmpPercent = annualAmpVO.getReplWeightAnnualAmpPercent();
            if (StringUtils.isNotBlank(annualAmp) && !annualAmp.equals("0*")) {
                annualAmpVO.setAnnualAmp(annualAmp + "%");
            }
            if (StringUtils.isNotBlank(replAnnualAmp) && !replAnnualAmp.equals("0*")) {
                annualAmpVO.setReplAnnualAmp(replAnnualAmp + "%");
            }
            if (StringUtils.isNotBlank(sameAnnualAmp) && !sameAnnualAmp.equals("0*")) {
                annualAmpVO.setSameAnnualAmp(sameAnnualAmp + "%");
            }
            if (StringUtils.isNotBlank(weightRate) && !weightRate.equals("0*")) {
                String weightRateRound = new BigDecimal(weightRate).setScale(1, BigDecimal.ROUND_HALF_UP).toString();
                annualAmpVO.setWeightRate(weightRateRound + "%");
            }
            if (StringUtils.isNotBlank(replWeightRate) && !replWeightRate.equals("0*")) {
                String replWeightRateRound = new BigDecimal(replWeightRate).setScale(1, BigDecimal.ROUND_HALF_UP).toString();
                annualAmpVO.setReplWeightRate(replWeightRateRound + "%");
            }
            if (StringUtils.isNotBlank(sameWeightRate) && !sameWeightRate.equals("0*")) {
                String sameWeightRateRound = new BigDecimal(sameWeightRate).setScale(1, BigDecimal.ROUND_HALF_UP).toString();
                annualAmpVO.setSameWeightRate(sameWeightRateRound + "%");
            }
            if (StringUtils.isNotBlank(sameWeightAnnualAmpPercent) && !sameWeightAnnualAmpPercent.equals("0*")) {
                String sameWeightAnnualPercent = new BigDecimal(sameWeightAnnualAmpPercent).setScale(1, BigDecimal.ROUND_HALF_UP).toString();
                annualAmpVO.setSameWeightAnnualAmpPercent(sameWeightAnnualPercent + "%");
            }
            if (StringUtils.isNotBlank(replWeightAnnualAmpPercent) && !replWeightAnnualAmpPercent.equals("0*")) {
                String replWeightAnnualPercent = new BigDecimal(replWeightAnnualAmpPercent).setScale(1, BigDecimal.ROUND_HALF_UP).toString();
                annualAmpVO.setReplWeightAnnualAmpPercent(replWeightAnnualPercent + "%");
            }
        });
    }

    @Async("asyncServiceExecutor")
    public Future<Integer> fillCurrentPriceIndexSheet(Workbook workbook, int sheetIdx, String groupCnName,
                                                      StandardAnalysisVO standardAnalysisVO, IRequestContext current) throws Exception {
        log.info(">>>Begin AsyncAnnualExportService::fillCurrentPriceIndexSheet");
        // 1、查询制造成本涨跌图导出数据
        RequestContextManager.setCurrent(current);
        StandardAnalysisVO standardVO = ObjectCopyUtil.copy(standardAnalysisVO, StandardAnalysisVO.class);
        List<DmFocAnnualAmpVO> priceIndexVOList = new ArrayList<>();
        Long actualMonthNum = standardDao.findActualMonthNum();
        FcstIndexUtil.handleNowPeriod(standardVO, actualMonthNum.toString());
        if (!"0".equals(actualMonthNum.toString())) {
            asyncQueryService.findMonthAccAllDataList(priceIndexVOList, standardVO);
        }
        List<CurrentPriceIndexVO> priceIndexList = new ArrayList<>();
        for (DmFocAnnualAmpVO annualAmpVO : priceIndexVOList) {
            annualAmpVO.setCostTypeValue(IndustryIndexEnum.getStandCostType(annualAmpVO.getCostType()).getDesc());
            CurrentPriceIndexVO currentPriceIndexVO = CurrentPriceIndexVO.builder()
                    .periodId(annualAmpVO.getPeriodId())
                    .costType(annualAmpVO.getCostTypeValue())
                    .groupCnName(annualAmpVO.getGroupCnName())
                    .costIndex(annualAmpVO.getCostIndex() != null ? new BigDecimal(annualAmpVO.getCostIndex()).setScale(2, BigDecimal.ROUND_HALF_UP) +"%" : "").build();
            priceIndexList.add(currentPriceIndexVO);
        }
        String viewFlag = standardVO.getViewFlag();
        DmFocViewInfoVO dmFocViewInfoVO = getViewInfoFlag(viewFlag);
        String ampTitle = FcstIndexUtil.getTitle(Constant.StrEnum.INDUSTRY_INDEX_AMP_CHART.getValue(), groupCnName);
        Sheet priceSheet = workbook.getSheetAt(sheetIdx);
        priceSheet.getRow(0).getCell(0).setCellValue(ampTitle);
        priceSheet.getRow(0).getCell(4).setCellValue(Constant.StrEnum.CALIBER_FLAG.getValue() +  IndustryIndexEnum.getCaliberFlag(standardVO.getCaliberFlag()).getDesc());
        priceSheet.getRow(1).getCell(4).setCellValue("视角: " + dmFocViewInfoVO.getViewFlagValue());
        // 填充Sheet数据
        new ExcelExportUtil<CurrentPriceIndexVO>(2, 2, CurrentPriceIndexVO.class).fillSheetData(priceSheet,
                priceIndexList);
        priceSheet.getRow(2).getCell(4).setCellValue(Constant.StrEnum.CURRENT_PERIOD.getValue() + standardDao.findActualMonthNum().toString());
        log.info(">>>End AsyncAnnualExportService::fillCurrentPriceIndexSheet");
        return new AsyncResult<>(priceIndexList.size());
    }

    public void resetProdRndTeamCodeList(StandardAnalysisVO standardAnalysisVO) {
        // 如果parentlevel为LV0/LV1/LV2/LV3，也就是当parentLevel的下一层级还是重量级团队的时候，需要把prodRndTeamCodeList置为null
        boolean parentFlag = CommonConstant.PROD_RND_TEAM_GROUP_LEVEL.stream().anyMatch(level -> level.equals(standardAnalysisVO.getParentLevel()));
        boolean currentFlag = CommonConstant.PROD_RND_TEAM_GROUP_LEVEL.stream().anyMatch(level -> level.equals(standardAnalysisVO.getGroupLevel()));
        if (parentFlag && currentFlag) {
            standardAnalysisVO.setProdRndTeamCodeList(null);
        }
    }

    private String getStandGroupKey(DmFocAnnualAmpVO dmFocAnnualAmpVO) {
        return dmFocAnnualAmpVO.getCostType() + "_" + dmFocAnnualAmpVO.getParentCode() + "_" + dmFocAnnualAmpVO.getParentCnName() + "_" + dmFocAnnualAmpVO.getGroupCnName() + "_" + dmFocAnnualAmpVO.getGroupCode();
    }

    private void setStandardDataList(List<Map<String, String>> dataList, List<String> threeYears, LinkedHashMap<String, List<DmFocAnnualAmpVO>> resultColumnMap, Set<String> resultRowSet) {
        List<String> yearList = threeYears.stream().sorted(String::compareTo).collect(Collectors.toList());
        for (String keyStr : resultRowSet) {
            List<DmFocAnnualAmpVO> resultAnnualAmpVO = MapUtil.get(resultColumnMap, keyStr, List.class);
            List<DmFocAnnualAmpVO> resultAnnualAmpList = resultAnnualAmpVO.stream().sorted(Comparator.comparing(DmFocAnnualAmpVO::getPeriodYear)).collect(Collectors.toList());
            Map<String, String> resultMap = new HashMap<>();
            for (int i = 0; i < resultAnnualAmpList.size(); i++) {
                String groupCnName = resultAnnualAmpList.get(i).getGroupCnName();
                String costType = resultAnnualAmpList.get(i).getCostType();
                String costTypeValue = IndustryIndexEnum.getStandCostType(costType).getDesc();
                String annualAmp = resultAnnualAmpList.get(i).getAnnualAmp();
                String periodYear = resultAnnualAmpList.get(i).getPeriodYear();
                resultMap.put("groupCnName", groupCnName);
                resultMap.put("costType", costTypeValue);
                for (int j = 0; j < yearList.size(); j++) {
                    if (yearList.get(j).equals(periodYear)) {
                        resultMap.put("annualAmp" + j, annualAmp);
                    }
                }
            }
            dataList.add(resultMap);
        }
    }

}
