/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.template;

import com.huawei.it.fcst.export.IExcelTemplateBeanManager;
import com.huawei.it.fcst.export.vo.ExcelTemplateBeanManager;
import com.huawei.it.fcst.export.vo.SheetBeanMetaVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import lombok.Getter;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * 定义月度导出模板
 *
 * <AUTHOR>
 * @since 2024/06
 */
@Getter
public enum CostReductTemplateEnum implements IExcelTemplateBeanManager {
    OBJECT_01("01", "objective", "配置管理-降成本目标", "配置管理-降成本目标") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            ExcelTemplateBeanManager excelTemplateBeanManager = new ExcelTemplateBeanManager();
            List<SheetBeanMetaVO> replaceL1 = new ArrayList<>();
            replaceL1.add(
                new SheetBeanMetaVO(OBJECT_01.templateName, 0, "CostReductDataProvider", "配置管理-降成本目标", Boolean.FALSE));
            excelTemplateBeanManager.setSheetBeans(replaceL1);
            excelTemplateBeanManager.setTemplateName(OBJECT_01.templateName);
            excelTemplateBeanManager.setModuleType(OBJECT_01.moduleType);
            excelTemplateBeanManager.setDesc(OBJECT_01.desc);
            return excelTemplateBeanManager;
        }
    };

    private String code;

    private String templateName;

    private String moduleType;

    private String desc;

    CostReductTemplateEnum(String code, String templateName, String moduleType, String desc) {
        this.code = code;
        this.templateName = templateName;
        this.moduleType = moduleType;
        this.desc = desc;
    }

    static final String NAME = "OBJECT";

    static final String SUB_LEVEL_CODE = "{0}_{1}";

    /**
     * 获取模板信息
     *
     * @param levelCode 层级code
     * @param roleName 角色名称
     * @return 枚举信息
     * @throws CommonApplicationException
     */
    public static CostReductTemplateEnum getByCode(String levelCode, String roleName)
        throws CommonApplicationException {
        String key = MessageFormat.format(SUB_LEVEL_CODE, NAME, levelCode);
        for (CostReductTemplateEnum value : CostReductTemplateEnum.values()) {
            if (value.name().equalsIgnoreCase(key)) {
                return value;
            }
        }
        throw new CommonApplicationException("Check the template definition relationship.");
    }
}
