/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.service.replace;

import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.replace.ReplaceAnalysisVO;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;

/**
 * IReplaceAmpService
 *
 * <AUTHOR>
 * @since 2024/8/29
 */

@Path("/replace")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface IReplaceAmpService {

    /**
     * 查询当前层级涨跌幅
     *
     * @param replaceAnalysisVO 参数
     * @return 结果
     */
    @Path("/costAmpChart")
    @POST
    ResultDataVO costAmpChart(ReplaceAnalysisVO replaceAnalysisVO);

    /**
     * 查询成本涨跌一览表
     *
     * @param replaceAnalysisVO 参数
     * @return 结果
     */
    @Path("/industryCostList")
    @POST
    ResultDataVO industryCostList(ReplaceAnalysisVO replaceAnalysisVO);

    /**
     * 数据下载
     *
     * @param replaceAnalysisVO 参数
     * @param response 响应
     * @return 结果
     */
    @Path("/exportDetail")
    @POST
    ResultDataVO exportDetail(ReplaceAnalysisVO replaceAnalysisVO, @Context HttpServletResponse response) throws Exception;
}
