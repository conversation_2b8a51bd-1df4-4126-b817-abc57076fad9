/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.constant;

import com.huawei.it.fcst.industry.index.enums.GroupLevelAllEnum;
import com.huawei.it.fcst.industry.index.vo.common.HeaderVo;
import org.apache.poi.ss.usermodel.CellType;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * CommonConstant Class
 *
 * <AUTHOR>
 * @since 2023/3/14
 */
public class CommonConstant {
    public static final List<HeaderVo> MAPPING_RELATIONAL_DIMENSION_HEADER = new LinkedList<>();
    static {
        MAPPING_RELATIONAL_DIMENSION_HEADER.add(new HeaderVo("专项采购认证部", "l3_ceg_cn_name",CellType.STRING,true, 12 * 480));
        MAPPING_RELATIONAL_DIMENSION_HEADER.add(new HeaderVo("专项采购认证部简称", "l3_ceg_short_cn_name",CellType.STRING,true,12 * 480));
        MAPPING_RELATIONAL_DIMENSION_HEADER.add(new HeaderVo("专项采购认证部编码", "l3_ceg_code",CellType.STRING,true, 12 * 480));
        MAPPING_RELATIONAL_DIMENSION_HEADER.add(new HeaderVo("模块", "l4_ceg_cn_name",CellType.STRING,true, 12 * 480));
        MAPPING_RELATIONAL_DIMENSION_HEADER.add(new HeaderVo("模块简称", "l4_ceg_short_cn_name",CellType.STRING,true,12 * 480));
        MAPPING_RELATIONAL_DIMENSION_HEADER.add(new HeaderVo("模块编码", "l4_ceg_code",CellType.STRING,true, 12 * 480));
        MAPPING_RELATIONAL_DIMENSION_HEADER.add(new HeaderVo("品类编码","category_code", CellType.STRING,true,12 * 480));
        MAPPING_RELATIONAL_DIMENSION_HEADER.add(new HeaderVo("品类名称", "category_cn_name",CellType.STRING,true, 15 * 500));
    }

    public static final List<HeaderVo> INDUSTRY_DISTRIBUTE_HEADER = new LinkedList<>();
    static {
        INDUSTRY_DISTRIBUTE_HEADER.add(new HeaderVo("采购成本占比", "purchasePercentage", CellType.STRING, true, 12 * 480));
        INDUSTRY_DISTRIBUTE_HEADER.add(new HeaderVo("制造成本占比", "manufacturePercentage", CellType.STRING, true, 12 * 480));
        INDUSTRY_DISTRIBUTE_HEADER.add(new HeaderVo("采购成本金额(亿元)", "purchaseRmbCostAmt", CellType.STRING, true, 12 * 480));
        INDUSTRY_DISTRIBUTE_HEADER.add(new HeaderVo("制造成本金额(亿元)", "manufactureRmbCostAmt", CellType.STRING, true, 12 * 480));
        INDUSTRY_DISTRIBUTE_HEADER.add(new HeaderVo("总成本金额(亿元)", "totalRmbCostAmt", CellType.STRING, true, 12 * 480));
    }

    public static final List<HeaderVo> INDUSTRY_AMP_STAND_HEADER = new LinkedList<>();
    static {
        INDUSTRY_AMP_STAND_HEADER.add(new HeaderVo("年", "periodYear", CellType.STRING, true, 12 * 480));
        INDUSTRY_AMP_STAND_HEADER.add(new HeaderVo("成本类型", "costTypeValue", CellType.STRING, true, 12 * 480));
        INDUSTRY_AMP_STAND_HEADER.add(new HeaderVo("名称", "groupCnName", CellType.STRING, true, 12 * 480));
        INDUSTRY_AMP_STAND_HEADER.add(new HeaderVo("涨跌幅", "annualAmp", CellType.STRING, true, 12 * 480));
    }

    public static final List<HeaderVo> INDUSTRY_AMP_HEADER = new LinkedList<>();
    static {
        INDUSTRY_AMP_HEADER.add(new HeaderVo("年", "periodYear", CellType.STRING, true, 12 * 480));
        INDUSTRY_AMP_HEADER.add(new HeaderVo("成本类型", "costSubType", CellType.STRING, true, 12 * 480));
        INDUSTRY_AMP_HEADER.add(new HeaderVo("名称", "groupCnName", CellType.STRING, true, 12 * 480));
        INDUSTRY_AMP_HEADER.add(new HeaderVo("涨跌幅", "annualAmp", CellType.STRING, true, 12 * 480));
    }

    public static final List<HeaderVo> INDUSTRY_DISTRIBUTE_GROUP_CN_NAME = new LinkedList<>();
    static {
        INDUSTRY_DISTRIBUTE_GROUP_CN_NAME.add(new HeaderVo("名称", "groupCnName", CellType.STRING, true, 12 * 480));
    }

    public static final List<HeaderVo> INDUSTRY_DISTRIBUTE_STAND_CN_NAME = new LinkedList<>();
    static {
        INDUSTRY_DISTRIBUTE_STAND_CN_NAME.add(new HeaderVo("名称", "groupCnName", CellType.STRING, true, 12 * 480));
    }

    public static final List<HeaderVo> INDUSTRY_SHEET_STAND_HEADER = new LinkedList<>();
    static {
        INDUSTRY_SHEET_STAND_HEADER.add(new HeaderVo("年", "periodYear", CellType.STRING, true, 12 * 480));
    }

    public static final List<HeaderVo> INDUSTRY_SHEET3_HEADER1 = new LinkedList<>();
    static {
        INDUSTRY_SHEET3_HEADER1.add(new HeaderVo("年", "periodYear", CellType.STRING, true, 12 * 480));
    }

    public static final List<HeaderVo> INDUSTRY_SHEET3_HEADER2 = new LinkedList<>();
    static {
        INDUSTRY_SHEET3_HEADER2.add(new HeaderVo("权重", "weightRate", CellType.STRING, true, 12 * 480));
        INDUSTRY_SHEET3_HEADER2.add(new HeaderVo("涨跌", "annualAmp", CellType.STRING, true, 12 * 480));
        INDUSTRY_SHEET3_HEADER2.add(new HeaderVo("权重×涨跌", "weightAnnualAmpPercent", CellType.STRING, true, 12 * 480));
    }

    public static final List<HeaderVo> STANDARD_DISTRIBUTE_HEADER = new LinkedList<>();
    static {
        STANDARD_DISTRIBUTE_HEADER.add(new HeaderVo("占比", "rmbCostPer", CellType.STRING, true, 12 * 480));
    }

    public static final List<HeaderVo> PRICE_INDEX_ACC_HEADER = new LinkedList<>();
    static {
        PRICE_INDEX_ACC_HEADER.add(new HeaderVo("年月", "periodId", CellType.STRING, true, 12 * 480));
        PRICE_INDEX_ACC_HEADER.add(new HeaderVo("成本类型", "costTypeValue", CellType.STRING, true, 12 * 480));
        PRICE_INDEX_ACC_HEADER.add(new HeaderVo("名称", "groupCnName", CellType.STRING, true, 12 * 480));
        PRICE_INDEX_ACC_HEADER.add(new HeaderVo("指数", "costIndex", CellType.STRING, true, 12 * 480));
    }

    public final static Set<String> PROD_RND_TEAM_GROUP_LEVEL = new HashSet<>();
    static {
        PROD_RND_TEAM_GROUP_LEVEL.add(GroupLevelAllEnum.LV1.getValue());
        PROD_RND_TEAM_GROUP_LEVEL.add(GroupLevelAllEnum.LV2.getValue());
        PROD_RND_TEAM_GROUP_LEVEL.add(GroupLevelAllEnum.LV3.getValue());
    }

    public static final List<HeaderVo> INDUSTRY_SHEET3_HEADER3 = new LinkedList<>();
    static {
        INDUSTRY_SHEET3_HEADER3.add(new HeaderVo("权重排序", "weightRate", CellType.STRING, true, 12 * 480));
        INDUSTRY_SHEET3_HEADER3.add(new HeaderVo("涨跌", "annualAmp", CellType.STRING, true, 12 * 480));
        INDUSTRY_SHEET3_HEADER3.add(new HeaderVo("权重×涨跌排序", "weightAnnualAmpPercent", CellType.STRING, true, 12 * 480));
    }

    public static final Integer WIDTH = 12 * 480;
    public static final String AMP_CN_NAME = "涨跌幅";
    public static final String AMP_EN_NAME = "annualAmp";
    public static final String MAPPING_RELATIONAL_DIMENSION = "映射关系维表清单";
    public static final String ENTRY_ABNORMAL_DATA = "ITEM异常数据录入";
    public static final String HISTORICAL_MODIFICATION_RECORDS = "历史修改记录";
    public static final String IS_NOT = "N";
    public static final String SAVE_METHOD = "P";
    public static final Pattern PATTERN = Pattern.compile("[0-9]*");
    public static final Pattern PATTERN_COLUMN = Pattern.compile("(_([a-z]))");

    public static final String VARCHAR = "VARCHAR";
    public static final String VERSION_TITLE = "清单数据版本";
    public static final String DIMENSION_VERSION = "维表版本：";
    public static final String HISTORY_PAGE = "history";
    public static final String ABNORMAL_PAGE = "abnormal";

    public static final Integer VIEW_WIDTH = 15 * 500 ;
    public static final Integer VERSION_WIDTH = 15 * 480 ;
    public static final String WEIGHT_CN_NAME = "权重";
    public static final String WEIGHT_EN_NAME = "weight";
    public static final String TOTAL_CN = "合计：";
    public static final String IS_YES = "Y";
    public static final String VERSION_REFRESH = "清单刷新版本：";
    public static final String INDUSTRT_STANDARD = "成本指数-产业-ICT-标准成本";

    public final static List<String> LV1_CODE = new ArrayList<>();
    /**
     * 无线:100001,
     * 光:134557 ,
     * 数据通信:137565
     * 计算：133277
     * 数据存储: 101775,
     * 云核心网: 100011
     * 其他的lv1 ....
     */
    static {
        LV1_CODE.add("100001");
        LV1_CODE.add("134557");
        LV1_CODE.add("137565");
        LV1_CODE.add("133277");
        LV1_CODE.add("101775");
        LV1_CODE.add("100011");
    }

    /**
     * 存储过程名称列表
     */
    public final static Set<String> FUNC_NAME = new HashSet<>();
    static {
        FUNC_NAME.add("F_DM_FOC_DIFF_VIEW_DIM");
        FUNC_NAME.add("F_DM_FOC_MID_MONTH_ITEM");
        FUNC_NAME.add("F_DM_FOC_MID_MONTH_CATE");
        FUNC_NAME.add("F_DM_FOC_ITEM_DTL_DECODE");
        FUNC_NAME.add("F_DM_FOC_TOP_CATE_INFO");
        FUNC_NAME.add("F_DM_FOC_ITEM_ACTUAL_APPEND");
        FUNC_NAME.add("F_DM_FOC_TOP_ITEM_INFO");
        FUNC_NAME.add("F_DM_FOC_ITEM_FCST_APPEND");
        FUNC_NAME.add("F_DM_FOC_ONLY_TOP_DELETE");
        FUNC_NAME.add("F_DM_FOC_MID_MONTH_IDX");
        FUNC_NAME.add("F_DM_FOC_ACTUAL_COST");
        FUNC_NAME.add("F_DM_FOC_MONTH_WEIGHT");
        FUNC_NAME.add("F_DM_FOC_MONTH_COST_IDX");
        FUNC_NAME.add("F_DM_FOC_MONTH_RATE");
        FUNC_NAME.add("F_DM_FOC_VIEW_ANNL_COST_T");
        FUNC_NAME.add("F_DM_FOC_VIEW_ANNL_COST_T_0613");
        FUNC_NAME.add("F_DM_FOC_ANNUAL_WEIGHT_T");
        FUNC_NAME.add("F_DM_FOC_ANNUAL_WEIGHT_T_0613");
        FUNC_NAME.add("F_DM_FOC_ANNUAL_AMP_T");
        FUNC_NAME.add("F_DM_FOC_DATA_ENCRYPT");
        FUNC_NAME.add("F_DM_VIEW_ANNUAL_STATUS");
        FUNC_NAME.add("F_DM_FOC_REVENUE_ITEM_SHIP_DTL");
        FUNC_NAME.add("F_DM_FOC_ITEM_DTL_INNER");
        FUNC_NAME.add("F_DM_FOC_REVERSES_TOP_CATE_INFO");
        FUNC_NAME.add("F_DM_FOC_REVERSES_TOP_ITEM_INFO");
        FUNC_NAME.add("F_DM_FOC_REVERSE_ANNUAL_WEIGHT");
        FUNC_NAME.add("F_DM_FOC_MID_MONTH_ITEM_FLAG");
        FUNC_NAME.add("F_DM_FOC_CUS_VIEW_ANNL_COST_T");
        FUNC_NAME.add("F_DM_FOC_CUS_ITEM_DTL_DECODE");
        FUNC_NAME.add("F_DM_FOC_CUS_ITEM_APPEND");
        FUNC_NAME.add("F_DM_FOC_MID_MONTH_CATE_DMS");
        FUNC_NAME.add("F_DM_FOC_TOP_CATE_INFO_DMS");
        FUNC_NAME.add("F_DM_FOC_TOP_ITEM_INFO_DMS");
        FUNC_NAME.add("F_DM_FOC_ONLY_TOP_DELETE_DMS");
        FUNC_NAME.add("F_DM_FOC_DIFF_VIEW_DIM_DMS");
        FUNC_NAME.add("F_DM_FOC_MONTH_WEIGHT_DMS");
        FUNC_NAME.add("F_DM_FOC_ACTUAL_COST_DMS");
        FUNC_NAME.add("F_DM_FOC_ITEM_DTL_DECODE_DMS");
        FUNC_NAME.add("F_DM_FOC_ITEM_ACTUAL_APPEND_DMS");
        FUNC_NAME.add("F_DM_FOC_ITEM_FCST_APPEND_DMS");
        FUNC_NAME.add("F_DM_FOC_MONTH_CUSTOM_COMB");
        FUNC_NAME.add("F_DM_FOC_MID_MONTH_IDX_DMS");
        FUNC_NAME.add("F_DM_FOC_VIEW_ANNL_COST_D");
        FUNC_NAME.add("F_DM_FOC_ANNUAL_WEIGHT_T_D");
        FUNC_NAME.add("F_DM_FOC_ANNUAL_ITEM_AMP_T");
        FUNC_NAME.add("F_DM_FOC_ANNUAL_AMP_T_D");
        FUNC_NAME.add("F_DM_FOC_VIEW_ANNL_COST_INSERT");
        FUNC_NAME.add("F_DM_FOC_CUSTOM_ANNUAL");
        FUNC_NAME.add("F_DM_FOC_MID_MONTH_ITEM_FIX");
        FUNC_NAME.add("F_DM_FOC_MID_MONTH_ITEM_DMS");
        FUNC_NAME.add("F_DM_FOC_DMS_ITEM_APPEND_COMBINE");
        FUNC_NAME.add("F_DM_FOC_ITEM_APPEND_COMBINE");
        FUNC_NAME.add("F_DM_FCST_DATA_ENCRYPT");
        FUNC_NAME.add("F_DM_FCST_STD_PROD_UNIT_T");
        FUNC_NAME.add("F_DM_FCST_MID_MONTH_SPART");
        FUNC_NAME.add("F_DM_FCST_SPART_ACTUAL_APPEND");
        FUNC_NAME.add("F_DM_FCST_SPART_DTL_DECODE");
        FUNC_NAME.add("F_DM_FCST_TOP_SPART_INFO");
        FUNC_NAME.add("F_DM_FCST_MON_COST_AMT");
        FUNC_NAME.add("F_DM_FCST_YTD_MON_COST_AMT");
        FUNC_NAME.add("F_DM_FCST_ICT_SUM_DETAIL_SPART_T");
        FUNC_NAME.add("F_DM_FCST_ICT_MON_MID_COST_IDX_T");
        FUNC_NAME.add("F_DM_FCST_ICT_MON_MID_COST_RED_IDX_T");
        FUNC_NAME.add("F_DM_FCST_ICT_MON_WEIGHT_T");
        FUNC_NAME.add("F_FCST_ICT_PRE_BASE_CUS_MON_RESULT_T");
        FUNC_NAME.add("F_DM_FCST_BASE_CUS_ANNL");
        FUNC_NAME.add("F_DM_FCST_MID_ANNL_AVG");
        FUNC_NAME.add("F_DM_FCST_DIM_INFO");
        FUNC_NAME.add("F_DM_FCST_ANNL_WEIGHT");
        FUNC_NAME.add("F_DM_FCST_ANNL_AMP");
        FUNC_NAME.add("F_DM_FCST_ANNL_STATUS");
        FUNC_NAME.add("F_DM_FCST_ICT_MON_REPL_COST_INFO_T");
        FUNC_NAME.add("F_DM_FCST_ICT_YTD_REPL_COST_INFO_T");
        FUNC_NAME.add("F_DM_FCST_MID_MONTH_SPART_ENTIRE");
        FUNC_NAME.add("F_DM_FCST_SPART_ACTUAL_APPEND_ENTIRE");
        FUNC_NAME.add("F_DM_FCST_SPART_DTL_DECODE_ENTIRE");
        FUNC_NAME.add("F_DM_FCST_ANNL_COST");
        FUNC_NAME.add("F_DM_FCST_ICT_MON_YTD_AVG_T");
        FUNC_NAME.add("F_DM_FCST_ICT_REPL_SAME_COST_IDX_T");
        FUNC_NAME.add("F_FCST_ICT_POINT_CUS_MON_RESULT_T");
        FUNC_NAME.add("F_DM_FCST_ICT_REPL_PRE_BASE_CUS_RESULT");
        FUNC_NAME.add("F_DM_FCST_CUS_ANNL");
        FUNC_NAME.add("F_DM_FOC_REPL_ACTUAL_APPEND");
        FUNC_NAME.add("F_DM_FOC_REPL_SAME_MID_MTD_INDEX_T");

    }

    /**
     * 存储过程名称列表
     */
    public final static Set<String> FUNC_NAME_PERIOD = new HashSet<>();
    static {
        FUNC_NAME_PERIOD.add("F_DM_FOC_DATA_ENCRYPT");
        FUNC_NAME_PERIOD.add("F_DM_FOC_MID_MONTH_ITEM");
        FUNC_NAME_PERIOD.add("F_DM_FOC_MID_MONTH_ITEM_FIX");
        FUNC_NAME_PERIOD.add("F_DM_FOC_MID_MONTH_ITEM_DMS");
        FUNC_NAME_PERIOD.add("F_DM_FCST_DATA_ENCRYPT");
    }

    /**
     * 不包含spart层级和coa层级列表
     */
    public final static Set<String> GROUP_LEVEL = new HashSet<>();
    static {
        GROUP_LEVEL.add(GroupLevelAllEnum.COA.getValue());
        GROUP_LEVEL.add(GroupLevelAllEnum.DIMENSION.getValue());
        GROUP_LEVEL.add(GroupLevelAllEnum.SUBCATEGORY.getValue());
        GROUP_LEVEL.add(GroupLevelAllEnum.SUB_DETAIL.getValue());
        GROUP_LEVEL.add(GroupLevelAllEnum.CATEGORY.getValue());
    }

    /**
     * 不包含spart层级
     */
    public final static Set<String> GROUP_LEVEL_DIMENSION= new HashSet<>();
    static {
        GROUP_LEVEL_DIMENSION.add(GroupLevelAllEnum.COA.getValue());
        GROUP_LEVEL_DIMENSION.add(GroupLevelAllEnum.DIMENSION.getValue());
        GROUP_LEVEL_DIMENSION.add(GroupLevelAllEnum.SUBCATEGORY.getValue());
        GROUP_LEVEL_DIMENSION.add(GroupLevelAllEnum.SUB_DETAIL.getValue());
    }

    /**
     * 量纲层级列表
     */
    public final static Set<String> GROUP_LEVEL_ALL_DIMENSION= new HashSet<>();
    static {
        GROUP_LEVEL_ALL_DIMENSION.add(GroupLevelAllEnum.COA.getValue());
        GROUP_LEVEL_ALL_DIMENSION.add(GroupLevelAllEnum.DIMENSION.getValue());
        GROUP_LEVEL_ALL_DIMENSION.add(GroupLevelAllEnum.SUBCATEGORY.getValue());
        GROUP_LEVEL_ALL_DIMENSION.add(GroupLevelAllEnum.SUB_DETAIL.getValue());
        GROUP_LEVEL_ALL_DIMENSION.add(GroupLevelAllEnum.SPART.getValue());
    }

    /**
     * 重量级团队层级列表
     */
    public final static Set<String> GROUP_LEVEL_PROD= new HashSet<>();
    static {
        GROUP_LEVEL_PROD.add(GroupLevelAllEnum.LV1.getValue());
        GROUP_LEVEL_PROD.add(GroupLevelAllEnum.LV2.getValue());
        GROUP_LEVEL_PROD.add(GroupLevelAllEnum.LV3.getValue());
        GROUP_LEVEL_PROD.add(GroupLevelAllEnum.LV4.getValue());
    }

    /**
     * 重量级团队层级列表
     */
    public final static Set<String> GROUP_LEVEL_PROFITS= new HashSet<>();
    static {
        GROUP_LEVEL_PROFITS.add(GroupLevelAllEnum.L1.getValue());
        GROUP_LEVEL_PROFITS.add(GroupLevelAllEnum.L2.getValue());
    }

    /**
     * 采购层级列表
     */
    public final static Set<String> GROUP_LEVEL_P = new HashSet<>();
    static {
        GROUP_LEVEL_P.add(GroupLevelAllEnum.CEG.getValue());
        GROUP_LEVEL_P.add(GroupLevelAllEnum.MODL.getValue());
        GROUP_LEVEL_P.add(GroupLevelAllEnum.CATEGORY.getValue());
        GROUP_LEVEL_P.add(GroupLevelAllEnum.ITEM.getValue());
    }

    /**
     * 制造层级列表
     */
    public final static Set<String> GROUP_LEVEL_M = new HashSet<>();
    static {
        GROUP_LEVEL_M.add(GroupLevelAllEnum.SHIPPING_OBJECT.getValue());
        GROUP_LEVEL_M.add(GroupLevelAllEnum.MANUFACTURE_OBJECT.getValue());
        GROUP_LEVEL_M.add(GroupLevelAllEnum.ITEM.getValue());
    }

    /**
     * 特殊层级列表
     */
    public final static Set<String> GROUP_LEVEL_P_M = new HashSet<>();
    static {
        GROUP_LEVEL_P_M.add(GroupLevelAllEnum.MODL.getValue());
        GROUP_LEVEL_P_M.add(GroupLevelAllEnum.CATEGORY.getValue());
        GROUP_LEVEL_P_M.add(GroupLevelAllEnum.ITEM.getValue());
        GROUP_LEVEL_P_M.add(GroupLevelAllEnum.MANUFACTURE_OBJECT.getValue());
    }

    /**
     * 维度汇总
     */
    public final static Set<String> DIMENSION_CODE = new HashSet<>();
    static {
        DIMENSION_CODE.add(Constant.StrEnum.DIMENSION_CODE.getValue());
        DIMENSION_CODE.add(Constant.StrEnum.PROD_DIMENSION_CODE.getValue());
        DIMENSION_CODE.add(Constant.StrEnum.PRICE_DIMENSION_CODE.getValue());
    }

    public final static Map<String, Object> HEADERS_MAP = new HashMap<>();
    static {
        HEADERS_MAP.put("jpg", "FFD8FF");
        HEADERS_MAP.put("png", "89504E47");
        HEADERS_MAP.put("pdf", "25504446");
    }

    public static final String SUCCESS = "SUCCESS";
    public static final String SUCCESS_STATUS = "1";
    public final static String DM_FOC_DATA_ENCRYPT = "F_DM_FOC_DATA_ENCRYPT";
    public static final String DATA_PROCESS_SWITCH = "App.Config.Industry.InitDataSwitch";
    public static final String PROCESS_STATUS = "App.Config.Industry.ProcessStatus";
    public static final String PROCESS_PROCESSING = "PROCESSING";
    public static final String S_SOURCE_TABLE_NAME = "DWL_PROD_BOM_ITEM_SHIP_DIM_I";
    public static final String R_SOURCE_TABLE_NAME = "DWL_PROD_BOM_ITEM_REV_DETAIL_I";
    public static final String S_TARGET_TABLE_NAME = "DM_FOC_JAVA_PRIMARY_ENCRYPT_T";
    public static final String R_TARGET_TABLE_NAME = "DM_FOC_REVENUE_JAVA_PRIMARY_ENCRYPT_T";
    public static final String STD_SOURCE_TABLE_NAME = "DWL_PROD_PROD_UNIT_KMS_I";
    public static final String STD_TARGET_TABLE_NAME = "DM_FCST_JAVA_PRIMARY_ENCRYPT_T";
    public static final List<String> SOURCE_TABLE_NAME = Arrays.asList("DWL_PROD_BOM_ITEM_SHIP_DIM_I", "DWL_PROD_BOM_ITEM_REV_DETAIL_I", "DWL_PROD_PROD_UNIT_KMS_I");

    public static final List<String> TARGET_TABLE_NAME = Arrays.asList("DM_FOC_JAVA_PRIMARY_ENCRYPT_T", "DM_FOC_REVENUE_JAVA_PRIMARY_ENCRYPT_T", "DM_FCST_JAVA_PRIMARY_ENCRYPT_T");
}
