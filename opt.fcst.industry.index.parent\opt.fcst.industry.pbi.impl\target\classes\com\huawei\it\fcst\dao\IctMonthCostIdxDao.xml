<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.pbi.dao.IctMonthCostIdxDao">

    <resultMap type="com.huawei.it.fcst.industry.pbi.vo.month.IctMonthAnalysisVO" id="resultMap">
        <result property="basePeriodId" column="base_period_id"/>
        <result property="versionId" column="version_id"/>
        <result property="customId" column="custom_id"/>
        <result property="customCnName" column="custom_cn_name"/>
        <result property="granularityType" column="granularity_type"/>
        <result property="periodYear" column="period_year"/>
        <result property="periodId" column="period_id"/>
        <result property="prodRndTeamCode" column="prod_rnd_team_code"/>
        <result property="prodRndTeamCnName" column="prod_rd_team_cn_name"/>
        <result property="prodListCode" column="prod_list_code"/>
        <result property="prodListCnName" column="prod_list_cn_name"/>
        <result property="industryCatgCode" column="industry_catg_code"/>
        <result property="industryCatgCnName" column="industry_catg_cn_name"/>
        <result property="dimensionCode" column="dimension_code"/>
        <result property="dimensionCnName" column="dimension_cn_name"/>
        <result property="dimensionSubCategoryCode" column="dimension_subcategory_code"/>
        <result property="dimensionSubcategoryCnName" column="dimension_subcategory_cn_name"/>
        <result property="dimensionSubDetailCode" column="dimension_sub_detail_code"/>
        <result property="dimensionSubDetailCnName" column="dimension_sub_detail_cn_name"/>
        <result property="groupCode" column="group_code"/>
        <result property="groupCnName" column="group_cn_name"/>
        <result property="groupLevel" column="group_level"/>
        <result property="costIndex" column="cost_index"/>
        <result property="ytdCostIndex" column="ytd_cost_index"/>
        <result property="costReductionRate" column="cost_reduction_rate"/>
        <result property="costReductionCnName" column="cost_reduction_cn_name"/>
        <result property="costReductionLevel" column="cost_reduction_level"/>
        <result property="weightRate" column="weight_rate"/>
        <result property="weightPercent" column="weight_percent"/>
        <result property="parentLevel" column="parent_level"/>
        <result property="parentCode" column="parent_code"/>
        <result property="parentCnName" column="parent_cn_name"/>
        <result property="mainFlag" column="main_flag"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="appendFlag" column="append_flag"/>
        <result property="regionCode" column="region_code"/>
        <result property="regionCnName" column="region_cn_name"/>
        <result property="repofficeCode" column="repoffice_code"/>
        <result property="repofficeCnName" column="repoffice_cn_name"/>
        <result property="bgCode" column="bg_code"/>
        <result property="bgCnName" column="bg_cn_name"/>
        <result property="overseaFlag" column="oversea_flag"/>
        <result property="codeAttributes" column="code_attributes"/>
    </resultMap>

    <sql id="costIdxChartFields">
        DISTINCT
        version_id,
        period_id,
        group_code,
        group_cn_name,
        group_level,
        ROUND(cost_index, 2) AS cost_index
    </sql>

    <sql id="searchWhere">
        <if test='!monthAnalysisVO.isNeedBlur and monthAnalysisVO.basePeriodId != null'>
            AND base_period_id = #{monthAnalysisVO.basePeriodId}
        </if>
        <if test='monthAnalysisVO.periodStartTime != null'>
            AND period_id <![CDATA[ >= ]]> #{monthAnalysisVO.periodStartTime}
        </if>
        <if test='monthAnalysisVO.periodEndTime != null'>
            AND period_id <![CDATA[ <= ]]> #{monthAnalysisVO.periodEndTime}
        </if>
        <if test='monthAnalysisVO.versionId != null'>
            AND version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='monthAnalysisVO.isNeedBlur'>
            <if test='monthAnalysisVO.customId != null'>
                AND custom_id = #{monthAnalysisVO.customId,jdbcType=NUMERIC}
            </if>
            <if test='monthAnalysisVO.customCnName != null and monthAnalysisVO.customCnName != ""'>
                AND custom_cn_name = #{monthAnalysisVO.customCnName,jdbcType=VARCHAR}
            </if>
            <if test='monthAnalysisVO.granularityType != null and monthAnalysisVO.granularityType != ""'>
                AND granularity_type = #{monthAnalysisVO.granularityType,jdbcType=VARCHAR}
            </if>
        </if>
        <if test='monthAnalysisVO.viewFlag != null and monthAnalysisVO.viewFlag != ""'>
            AND view_flag = #{monthAnalysisVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            AND oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
            AND bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
            AND region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
            AND repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.mainFlag != null and monthAnalysisVO.mainFlag != ""'>
            AND main_flag = #{monthAnalysisVO.mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.codeAttributes != null and monthAnalysisVO.codeAttributes != ""'>
            AND code_attributes = #{monthAnalysisVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupLevel != null and monthAnalysisVO.groupLevel != ""'>
            and group_level = #{monthAnalysisVO.groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupCodeList != null and monthAnalysisVO.groupCodeList != ""'>
            <foreach collection='monthAnalysisVO.groupCodeList' item="code" open="AND group_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.granularityType=="IRB" and monthAnalysisVO.prodRndTeamCodeList != null and monthAnalysisVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND prod_rnd_team_code IN ("
                     close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.granularityType=="PROD" and monthAnalysisVO.prodRndTeamCodeList != null and monthAnalysisVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND prod_list_code IN ("
                     close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.granularityType=="INDUS" and monthAnalysisVO.prodRndTeamCodeList != null and monthAnalysisVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND industry_catg_code IN ("
                     close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.dimensionCode != null and monthAnalysisVO.dimensionCode != ""'>
            AND dimension_code = #{monthAnalysisVO.dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.dimensionSubCategoryCode != null and monthAnalysisVO.dimensionSubCategoryCode != ""'>
            AND dimension_subcategory_code = #{monthAnalysisVO.dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.dimensionSubDetailCode != null and monthAnalysisVO.dimensionSubDetailCode != ""'>
            AND dimension_sub_detail_code = #{monthAnalysisVO.dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
    </sql>

    <sql id="blurSearchWhere">
        <if test='monthAnalysisVO.periodStartTime != null'>
            AND period_id <![CDATA[ >= ]]> #{monthAnalysisVO.periodStartTime}
        </if>
        <if test='monthAnalysisVO.periodEndTime != null'>
            AND period_id <![CDATA[ <= ]]> #{monthAnalysisVO.periodEndTime}
        </if>
        <if test='monthAnalysisVO.versionId != null'>
            AND version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='monthAnalysisVO.customId != null'>
            AND custom_id = #{monthAnalysisVO.customId,jdbcType=NUMERIC}
        </if>
        <if test='monthAnalysisVO.customCnName != null and monthAnalysisVO.customCnName != ""'>
            AND custom_cn_name = #{monthAnalysisVO.customCnName,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.granularityType != null and monthAnalysisVO.granularityType != ""'>
            AND granularity_type = #{monthAnalysisVO.granularityType,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.viewFlag != null and monthAnalysisVO.viewFlag != ""'>
            AND view_flag = #{monthAnalysisVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            AND oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
            AND bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
            AND region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
            AND repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.mainFlag != null and monthAnalysisVO.mainFlag != ""'>
            AND main_flag = #{monthAnalysisVO.mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.codeAttributes != null and monthAnalysisVO.codeAttributes != ""'>
            AND code_attributes = #{monthAnalysisVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupLevel != null and monthAnalysisVO.groupLevel != ""'>
            and group_level = #{monthAnalysisVO.groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupCodeList != null and monthAnalysisVO.groupCodeList != ""'>
            <foreach collection='monthAnalysisVO.groupCodeList' item="code" open="AND group_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
    </sql>

    <select id="findCostIndexVOList" resultMap="resultMap">
        SELECT
        <include refid="costIdxChartFields"/>
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.tablePreFix}_mon_cost_idx_t
        WHERE del_flag = 'N'
        <include refid="searchWhere"/>
        ORDER BY period_id
    </select>

    <select id="findBlurCostIndexVOList" resultMap="resultMap">
        SELECT
        <include refid="costIdxChartFields"/>
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.blurTablePreFix}_base_cus_mon_cost_idx_t
        WHERE del_flag = 'N'
        <include refid="blurSearchWhere"/>
        ORDER BY period_id
    </select>

    <sql id="multiCostIdxChartFields">
        DISTINCT
        <choose>
            <when test='monthAnalysisVO.granularityType == "PROD"'>
                t1.prod_list_cn_name,
                t1.prod_list_code,
            </when>
            <when test='monthAnalysisVO.granularityType == "INDUS"'>
                t1.industry_catg_cn_name,
                t1.industry_catg_code,
            </when>
            <otherwise>
                t1.prod_rd_team_cn_name,
                t1.prod_rnd_team_code,
            </otherwise>
        </choose>
        t1.parent_code,
        t1.parent_cn_name,
        t1.period_id,
        t1.group_level,
        t1.group_code,
        t1.group_cn_name,
        ROUND(t1.cost_index, 2) AS cost_index,
        t2.weight_rate,
        ROUND(t2.weight_rate * 100, 2) || '%' AS weight_percent
    </sql>

    <sql id="multiIdxSearchWhere">
        <if test='monthAnalysisVO.basePeriodId != null'>
            AND t1.base_period_id = #{monthAnalysisVO.basePeriodId}
        </if>
        <if test='monthAnalysisVO.periodStartTime != null'>
            AND t1.period_id <![CDATA[ >= ]]> #{monthAnalysisVO.periodStartTime}
        </if>
        <if test='monthAnalysisVO.periodEndTime != null'>
            AND t1.period_id <![CDATA[ <= ]]> #{monthAnalysisVO.periodEndTime}
        </if>
        <if test='monthAnalysisVO.versionId != null'>
            AND t1.version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='monthAnalysisVO.viewFlag != null and monthAnalysisVO.viewFlag != ""'>
            AND t1.view_flag = #{monthAnalysisVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            AND t1.oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
            AND t1.bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
            AND t1.region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
            AND t1.repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.mainFlag != null and monthAnalysisVO.mainFlag != ""'>
            AND t1.main_flag = #{monthAnalysisVO.mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.codeAttributes != null and monthAnalysisVO.codeAttributes != ""'>
            AND t1.code_attributes = #{monthAnalysisVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupLevel != null and monthAnalysisVO.groupLevel != ""'>
            and t1.group_level = #{monthAnalysisVO.groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.parentCodeList != null and monthAnalysisVO.parentCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code" open="AND t1.parent_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.subGroupCodeList != null and monthAnalysisVO.subGroupCodeList != ""'>
            <foreach collection='monthAnalysisVO.subGroupCodeList' item="code" open="AND t1.group_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.granularityType=="IRB" and monthAnalysisVO.prodRndTeamCodeList != null and monthAnalysisVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND t1.prod_rnd_team_code IN ("
                     close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.granularityType=="PROD" and monthAnalysisVO.prodRndTeamCodeList != null and monthAnalysisVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND t1.prod_list_code IN ("
                     close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.granularityType=="INDUS" and monthAnalysisVO.prodRndTeamCodeList != null and monthAnalysisVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND t1.industry_catg_code IN ("
                     close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.dimensionCode != null and monthAnalysisVO.dimensionCode != ""'>
            AND t1.dimension_code = #{monthAnalysisVO.dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.dimensionSubCategoryCode != null and monthAnalysisVO.dimensionSubCategoryCode != ""'>
            AND t1.dimension_subcategory_code = #{monthAnalysisVO.dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.dimensionSubDetailCode != null and monthAnalysisVO.dimensionSubDetailCode != ""'>
            AND t1.dimension_sub_detail_code = #{monthAnalysisVO.dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
    </sql>

    <select id="findMultiCostIndexVOList" resultMap="resultMap">
        SELECT
        <include refid="multiCostIdxChartFields"/>
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.tablePreFix}_mon_cost_idx_t t1
        LEFT JOIN fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.tablePreFix}_mon_weight_t t2
        ON t2.del_flag = 'N'
        <if test='monthAnalysisVO.intervalYear != null and monthAnalysisVO.intervalYear != ""'>
            AND t2.period_year = #{monthAnalysisVO.intervalYear,jdbcType=VARCHAR}
        </if>
        AND t1.group_code = t2.group_code
        and t1.group_level = t2.group_level
        AND t1.parent_code = t2.parent_code
        <choose>
            <when test='monthAnalysisVO.granularityType == "PROD"'>
                AND t1.prod_list_code = t2.prod_list_code
            </when>
            <when test='monthAnalysisVO.granularityType == "INDUS"'>
                AND t1.industry_catg_code = t2.industry_catg_code
            </when>
            <otherwise>
                AND t1.prod_rnd_team_code = t2.prod_rnd_team_code
            </otherwise>
        </choose>
        AND t1.version_id = t2.version_id
        AND t1.view_flag = t2.view_flag
        AND t1.oversea_flag = t2.oversea_flag
        AND t1.bg_code = t2.bg_code
        AND t1.region_code = t2.region_code
        AND t1.repoffice_code = t2.repoffice_code
        AND t1.main_flag = t2.main_flag
        <if test='monthAnalysisVO.mainFlag == "Y"'>
            AND t1.code_attributes = t2.code_attributes
        </if>
        WHERE t1.del_flag = 'N'
        <include refid="multiIdxSearchWhere"/>
        ORDER BY t1.group_code, t1.period_id
    </select>

    <select id="findBlurMinMultiCostIndexList" resultMap="resultMap">
        SELECT DISTINCT
            t1.parent_code,
            t1.parent_cn_name,
            t1.period_id,
            t1.group_level,
            t1.group_code,
            t1.group_cn_name,
            ROUND(t1.cost_index, 2) AS cost_index,
            t1.lv4_code AS prod_rnd_team_code,
            t1.lv4_cn_name AS prod_rd_team_cn_name,
            t2.weight_rate,
            ROUND(t2.weight_rate * 100, 2) || '%' AS weight_percent
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.tablePreFix}_mon_mid_cost_idx_t t1
        LEFT JOIN fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.blurTablePreFix}_base_cus_mon_weight_t t2
        ON t2.del_flag = 'N'
        <if test='monthAnalysisVO.intervalYear != null and monthAnalysisVO.intervalYear != ""'>
            AND t2.period_year = #{monthAnalysisVO.intervalYear,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.customId != null'>
            AND t2.custom_id = #{monthAnalysisVO.customId,jdbcType=NUMERIC}
        </if>
        AND t2.granularity_type = #{monthAnalysisVO.granularityType,jdbcType=VARCHAR}
        AND t1.group_code = t2.group_code
        AND t1.group_level = t2.group_level
        AND t1.version_id = t2.version_id
        AND t1.view_flag = t2.view_flag
        AND t1.oversea_flag = t2.oversea_flag
        AND t1.bg_code = t2.bg_code
        AND t1.region_code = t2.region_code
        AND t1.repoffice_code = t2.repoffice_code
        AND t1.lv4_code = t2.lv4_code
        AND t1.main_flag = t2.main_flag
        <if test='monthAnalysisVO.mainFlag == "Y"'>
            AND t1.code_attributes = t2.code_attributes
        </if>
        WHERE t1.del_flag = 'N'
        <if test='monthAnalysisVO.basePeriodId != null'>
            AND t1.base_period_id = #{monthAnalysisVO.basePeriodId}
        </if>
        <if test='monthAnalysisVO.periodStartTime != null'>
            AND t1.period_id <![CDATA[ >= ]]> #{monthAnalysisVO.periodStartTime}
        </if>
        <if test='monthAnalysisVO.periodEndTime != null'>
            AND t1.period_id <![CDATA[ <= ]]> #{monthAnalysisVO.periodEndTime}
        </if>
        <if test='monthAnalysisVO.versionId != null'>
            AND t1.version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='monthAnalysisVO.viewFlag != null and monthAnalysisVO.viewFlag != ""'>
            AND t1.view_flag = #{monthAnalysisVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            AND t1.oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
            AND t1.bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
            AND t1.region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
            AND t1.repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.mainFlag != null and monthAnalysisVO.mainFlag != ""'>
            AND t1.main_flag = #{monthAnalysisVO.mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.codeAttributes != null and monthAnalysisVO.codeAttributes != ""'>
            AND t1.code_attributes = #{monthAnalysisVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.parentLevel != null and monthAnalysisVO.parentLevel != ""'>
            AND t1.parent_level = #{monthAnalysisVO.parentLevel,jdbcType=VARCHAR}
        </if>
        <choose>
            <when test='monthAnalysisVO.viewFlag == "PROD_SPART"'>
                AND t1.group_level = 'SPART'
            </when>
            <when test='monthAnalysisVO.viewFlag == "DIMENSION"'>
                AND t1.group_level = 'SUB_DETAIL'
            </when>
        </choose>
        <if test='monthAnalysisVO.lv4ProdRndTeamCodeList != null and monthAnalysisVO.lv4ProdRndTeamCodeList != ""'>
            <foreach collection='monthAnalysisVO.lv4ProdRndTeamCodeList' item="code" open="AND t1.lv4_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.subGroupCodeList != null and monthAnalysisVO.subGroupCodeList != ""'>
            <foreach collection='monthAnalysisVO.subGroupCodeList' item="code" open="AND t1.group_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.prodRndTeamCodeList != null and monthAnalysisVO.prodRndTeamCodeList.size() > 0'>
            <choose>
                <when test='monthAnalysisVO.teamLevel == "LV0"'>
                    <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND t1.lv0_code IN (" close=")" index="index" separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='monthAnalysisVO.teamLevel == "LV1"'>
                    <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND t1.lv1_code IN (" close=")" index="index" separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='monthAnalysisVO.teamLevel == "LV2"'>
                    <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND t1.lv2_code IN (" close=")" index="index" separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='monthAnalysisVO.teamLevel == "LV3"'>
                    <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND t1.lv3_code IN (" close=")" index="index" separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='monthAnalysisVO.teamLevel == "LV4"'>
                    <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND t1.lv4_code IN (" close=")" index="index" separator=",">
                        #{code}
                    </foreach>
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        ORDER BY t1.group_code, t1.period_id
    </select>

    <select id="findBlurMultiCostIndexVOList" resultMap="resultMap">
        SELECT DISTINCT
        t1.parent_code,
        t1.parent_cn_name,
        t1.period_id,
        t1.group_level,
        t1.group_code,
        t1.group_cn_name,
        ROUND(t1.cost_index, 2) AS cost_index,
        t2.weight_rate,
        ROUND(t2.weight_rate * 100, 2) || '%' AS weight_percent
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.blurTablePreFix}_base_cus_mon_cost_idx_t t1
        LEFT JOIN fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.blurTablePreFix}_base_cus_mon_weight_t t2
        ON t2.del_flag = 'N'
        <if test='monthAnalysisVO.intervalYear != null and monthAnalysisVO.intervalYear != ""'>
            AND t2.period_year = #{monthAnalysisVO.intervalYear,jdbcType=VARCHAR}
        </if>
        AND t1.group_code = t2.group_code
        AND t1.group_level = t2.group_level
        AND t1.parent_level = t2.parent_level
        AND t1.parent_code = t2.parent_code
        AND t1.version_id = t2.version_id
        AND t1.custom_id = t2.custom_id
        AND t1.view_flag = t2.view_flag
        AND t1.granularity_type = t2.granularity_type
        AND t1.oversea_flag = t2.oversea_flag
        AND t1.bg_code = t2.bg_code
        AND t1.region_code = t2.region_code
        AND t1.repoffice_code = t2.repoffice_code
        AND t1.main_flag = t2.main_flag
        <if test='monthAnalysisVO.mainFlag == "Y"'>
            AND t1.code_attributes = t2.code_attributes
        </if>
        WHERE t1.del_flag = 'N'
        <include refid="blurMultiIdxSearchWhere"/>
        ORDER BY t1.group_code, t1.period_id
    </select>

    <sql id="blurMultiIdxSearchWhere">
        <if test='monthAnalysisVO.basePeriodId != null'>
            AND t1.base_period_id = #{monthAnalysisVO.basePeriodId}
        </if>
        <if test='monthAnalysisVO.periodStartTime != null'>
            AND t1.period_id <![CDATA[ >= ]]> #{monthAnalysisVO.periodStartTime}
        </if>
        <if test='monthAnalysisVO.periodEndTime != null'>
            AND t1.period_id <![CDATA[ <= ]]> #{monthAnalysisVO.periodEndTime}
        </if>
        <if test='monthAnalysisVO.versionId != null'>
            AND t1.version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='monthAnalysisVO.customId != null'>
            AND t1.custom_id = #{monthAnalysisVO.customId,jdbcType=NUMERIC}
        </if>
        <if test='monthAnalysisVO.granularityType != null and monthAnalysisVO.granularityType != ""'>
            AND t1.granularity_type = #{monthAnalysisVO.granularityType,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.viewFlag != null and monthAnalysisVO.viewFlag != ""'>
            AND t1.view_flag = #{monthAnalysisVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            AND t1.oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
            AND t1.bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
            AND t1.region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
            AND t1.repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.mainFlag != null and monthAnalysisVO.mainFlag != ""'>
            AND t1.main_flag = #{monthAnalysisVO.mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.codeAttributes != null and monthAnalysisVO.codeAttributes != ""'>
            AND t1.code_attributes = #{monthAnalysisVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.parentLevel != null and monthAnalysisVO.parentLevel != ""'>
            AND t1.parent_level = #{monthAnalysisVO.parentLevel,jdbcType=VARCHAR}
        </if>
        <choose>
            <when test='monthAnalysisVO.viewFlag == "PROD_SPART"'>
                AND t1.group_level = 'SPART'
            </when>
            <when test='monthAnalysisVO.viewFlag == "DIMENSION"'>
                AND t1.group_level = 'SUB_DETAIL'
            </when>
        </choose>
        <if test='monthAnalysisVO.parentCodeList != null and monthAnalysisVO.parentCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code" open="AND t1.parent_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.subGroupCodeList != null and monthAnalysisVO.subGroupCodeList != ""'>
            <foreach collection='monthAnalysisVO.subGroupCodeList' item="code" open="AND t1.group_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
    </sql>

    <sql id="reduceCostIdxFields">
        DISTINCT
        version_id,
        period_year,
        period_id,
        parent_code,
        group_code,
        group_cn_name,
        group_level,
        COALESCE(ROUND(cost_index, 2 ), 0) AS cost_index,
	    COALESCE(ROUND(ytd_cost_index, 2 ), 0) AS ytd_cost_index,
        ROUND(cost_reduction_rate, 2) AS cost_reduction_rate,
        cost_reduction_cn_name,
        cost_reduction_level
    </sql>

    <sql id="reduceCostIndexSearchWhere">
        <if test='monthAnalysisVO.periodYear != null'>
            AND period_year = #{monthAnalysisVO.periodYear}
        </if>
        <if test='monthAnalysisVO.versionId != null'>
            AND version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='monthAnalysisVO.viewFlag != null and monthAnalysisVO.viewFlag != ""'>
            AND view_flag = #{monthAnalysisVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            AND oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
            AND bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
            AND region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
            AND repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.mainFlag != null and monthAnalysisVO.mainFlag != ""'>
            AND main_flag = #{monthAnalysisVO.mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.codeAttributes != null and monthAnalysisVO.codeAttributes != ""'>
            AND code_attributes = #{monthAnalysisVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupLevel != null and monthAnalysisVO.groupLevel != ""'>
            AND group_level = #{monthAnalysisVO.groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.costReductionLevel != null and monthAnalysisVO.costReductionLevel != ""'>
            AND cost_reduction_level = #{monthAnalysisVO.costReductionLevel,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.viewFlag == "PROD_SPART" or monthAnalysisVO.groupLevel == "SPART"'>
            <if test='monthAnalysisVO.groupCodeList != null and monthAnalysisVO.groupCodeList != ""'>
                <foreach collection='monthAnalysisVO.groupCodeList' item="code" open="AND group_code IN (" close=")" index="index" separator=",">
                    #{code}
                </foreach>
            </if>
        </if>
        <if test='monthAnalysisVO.granularityType=="IRB" and monthAnalysisVO.prodRndTeamCodeList != null and monthAnalysisVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND prod_rnd_team_code IN ("
                     close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.granularityType=="PROD" and monthAnalysisVO.prodRndTeamCodeList != null and monthAnalysisVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND prod_list_code IN ("
                     close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.granularityType=="INDUS" and monthAnalysisVO.prodRndTeamCodeList != null and monthAnalysisVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND industry_catg_code IN ("
                     close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.viewFlag == "DIMENSION"'>
            <if test='monthAnalysisVO.groupLevel == "DIMENSION" and monthAnalysisVO.dimensionCode != null and monthAnalysisVO.dimensionCode != ""'>
                AND group_code = #{monthAnalysisVO.dimensionCode,jdbcType=VARCHAR}
            </if>
            <if test='monthAnalysisVO.groupLevel == "SUBCATEGORY" and monthAnalysisVO.dimensionSubCategoryCode != null and monthAnalysisVO.dimensionSubCategoryCode != ""'>
                AND group_code = #{monthAnalysisVO.dimensionSubCategoryCode,jdbcType=VARCHAR}
            </if>
            <if test='monthAnalysisVO.groupLevel == "SUB_DETAIL" and monthAnalysisVO.dimensionSubDetailCode != null and monthAnalysisVO.dimensionSubDetailCode != ""'>
                AND group_code = #{monthAnalysisVO.dimensionSubDetailCode,jdbcType=VARCHAR}
            </if>
        </if>
    </sql>

    <select id="findReduceCostTargetList" resultMap="resultMap">
        SELECT
        <include refid="reduceCostIdxFields"/>
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.blurTablePreFix}_mon_cost_red_idx_t
        WHERE del_flag = 'N'
        <include refid="reduceCostIndexSearchWhere"/>
        ORDER BY period_id
    </select>

    <select id="findActualMonth" resultType="java.lang.String">
        SELECT IFNULL(max(period_id), 0)
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.tablePreFix}_mon_cost_idx_t
        WHERE del_flag = 'N'
        AND group_level = 'LV0'
        AND bg_code = 'GR'
        AND oversea_flag = 'G'
        AND region_code = 'GLOBAL'
        AND version_id = (
            SELECT version_id
            FROM fin_dm_opt_foi.dm_fcst_ict_version_info_t
            WHERE del_flag = 'N'
            AND is_running = 'N'
            AND status = 1
            AND data_type = 'MONTH'
            AND version_type IN ('AUTO', 'FINAL')
            ORDER BY creation_date DESC
            LIMIT 1
        )
    </select>

    <select id="findStartEndTime" resultType="com.huawei.it.fcst.industry.pbi.vo.month.PeriodIdDimVO">
        SELECT version_id AS versionId,
               granularitytype AS granularityType,
               MIN(starttime) AS startTime,
               MAX(endtime) AS endTime
        FROM fin_dm_opt_foi.dm_fcst_ict_period_id_dim
        WHERE 1 = 1
        <if test='versionId != null'>
            AND version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        GROUP BY version_id, granularitytype
    </select>

    <select id="findCostIndexCount" resultType="int">
        SELECT count(1)
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.tablePreFix}_mon_cost_idx_t
        WHERE del_flag = 'N'
        <include refid="searchWhere"/>
    </select>

    <select id="findMixCostIndexCount" resultType="int">
        select sum(count) from (
        SELECT count(1) as count
        FROM fin_dm_opt_foi.dm_fcst_ict_psp_${monthAnalysisVO.granularityType}_mon_cost_idx_t
        WHERE del_flag = 'N'
        <include refid="searchWhere"/>
        union all
        SELECT count(1) as count
        FROM fin_dm_opt_foi.dm_fcst_ict_std_${monthAnalysisVO.granularityType}_mon_cost_idx_t
        WHERE del_flag = 'N'
        <include refid="searchWhere"/>
        )
    </select>

    <select id="findBasePeriodId" resultType="java.lang.String">
        SELECT MAX(period_year)-1 || '01'
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.tablePreFix}_mon_cost_idx_t
        WHERE del_flag = 'N'
        AND version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
    </select>

    <select id="findMixBasePeriodId" resultType="java.lang.String">
        SELECT MAX(period_year)-1 || '01'
        FROM fin_dm_opt_foi.dm_fcst_ict_psp_${monthAnalysisVO.granularityType}_mon_cost_idx_t
        WHERE del_flag = 'N'
          AND version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
    </select>

    <select id="callFuncRefreshData" resultType="java.lang.String">
        SELECT fin_dm_opt_foi.f_dm_fcst_ict_period_id_reset(#{jsonStr})
    </select>

</mapper>
