/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.service.annual;

import com.huawei.it.fcst.industry.index.vo.combination.DmFocDataRefreshStatus;
import com.huawei.it.fcst.industry.index.vo.common.CommonViewVO;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;

/**
 * IAnnualCommonService Class
 *
 * <AUTHOR>
 * @since 2023/3/14
 */
@Path("/common")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface IAnnualCommonService {

    /**
     * 获取会计期年份下拉框
     *
     * @return String
     */
    @GET
    @Path("/periodYear/list")
    ResultDataVO getAnnualPeriodYear(@QueryParam("costType") String costType,@QueryParam("industryOrg") String industryOrg);

    /**
     * BG下拉框
     *
     * @param commonViewVO 参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/bgInfo/list")
    ResultDataVO getBgInfoList(CommonViewVO commonViewVO);

    /**
     * 不同层级的code下拉框
     *
     * @param commonViewVO 参数
     * @return ResultDataVO ResultDataVO
     */
    @Path("/viewInfo/list")
    @POST
    ResultDataVO viewInfoList(CommonViewVO commonViewVO) throws ApplicationException;


    /**
     * 反向不同层级的code下拉框
     *
     * @param commonViewVO 参数
     * @return ResultDataVO ResultDataVO
     */
    @Path("/viewInfo/reverseList")
    @POST
    ResultDataVO reverseViewInfoList(CommonViewVO commonViewVO) throws ApplicationException;

    /**
     * 视角下拉框
     *
     * @return ResultDataVO
     */
    @Path("/viewFlagInfo/list")
    @POST
    ResultDataVO viewFlagInfoList(CommonViewVO commonViewVO) throws ApplicationException;

    /**
     * 查新系统的所有视角
     *
     * @return ResultDataVO
     */
    @Path("/allViewInfo/list")
    @GET
    ResultDataVO allViewInfo(@QueryParam("industryOrg") String industryOrg) throws ApplicationException;

    /**
     * 数据获取状态查询
     *
     * @param dataRefreshStatus 参数
     * @return 结果
     * @throws CommonApplicationException 异常
     */
    @POST
    @Path("/query/status")
    ResultDataVO queryDataRefreshStatus(DmFocDataRefreshStatus dataRefreshStatus) throws CommonApplicationException;

    /**
     * 当前用户，当前角色的task_id查询
     *
     * @return 结果
     * @throws CommonApplicationException 异常
     */
    @POST
    @Path("/current/status")
    ResultDataVO currentDataRefreshStatus(DmFocDataRefreshStatus dataRefreshStatus) throws CommonApplicationException;
}
