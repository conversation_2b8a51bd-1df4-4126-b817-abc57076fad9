/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.enums;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 * @since 2023/11/3
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class GroupLevelEnumMadeUTest {
    @Test
    public void getInstance() {
        GroupLevelEnumMadeU levelEnumMadeU = GroupLevelEnumMadeU.getInstance("");
        Assert.assertNull(levelEnumMadeU);
    }
    @Test
    public void getInstanceLv3T() {
        GroupLevelEnumMadeU levelEnumMadeU = GroupLevelEnumMadeU.getInstance("LV3");
        Assert.assertNotNull(levelEnumMadeU);
    }
}