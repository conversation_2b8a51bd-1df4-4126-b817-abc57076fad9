/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ObjectCopyUtil Class
 *
 * <AUTHOR>
 * @since 2022-11-28
 */
@Slf4j
public class ObjectCopyUtil {
    /**
     * 多个实体的复制
     *
     * @param source obj source
     * @param clazz class
     * @param <T> return clazz
     * @return T
     */
    public static <T> List<T> copyList(List source, Class<T> clazz) {
        List<T> target = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(source)) {
            for (Object c : source) {
                T obj = copy(c, clazz);
                target.add(obj);
            }
        }
        return target;
    }

    /**
     * 单个实体之间的复制
     *
     * @param source obj source
     * @param target obj class
     * @return T
     */
    public static <T> T copy(Object source, Class<T> target) {
        if (Objects.isNull(source)) {
            return null;
        }
        T obj = null;
        try {
            obj = target.newInstance();
        } catch (Exception ex) {
            log.error(ex.getMessage());
        }
        BeanUtils.copyProperties(source, obj);
        return obj;
    }

}