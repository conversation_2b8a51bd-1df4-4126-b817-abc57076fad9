/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.month.DmFocMonthYoyVO;
import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * The DAO to access DmFocMonthYoyT entity
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2023-03-21 14:14:05
 */
public interface IDmFocMonthYoyDao {

    /**
     * Find all DmFoiPriceIndexVO records.
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<DmFocMonthYoyVO> findDmFocMonthYoyVOList(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);

    /**
     * Find all DmFoiPriceIndexVO records.
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<DmFocMonthYoyVO> findDmFocMonthCombYoyVOList(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);


}
