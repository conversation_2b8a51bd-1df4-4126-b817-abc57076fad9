<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IDmFocViewInfoDao">
    <resultMap type="com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO" id="resultMap">
        <result property="id" column="id"/>
        <result property="lv0ProdRndTeamCode" column="lv0_prod_rnd_team_code"/>
        <result property="lv0ProdRdTeamCnName" column="lv0_prod_rd_team_cn_name"/>
        <result property="lv1ProdRndTeamCode" column="lv1_prod_rnd_team_code"/>
        <result property="lv1ProdRdTeamCnName" column="lv1_prod_rd_team_cn_name"/>
        <result property="lv2ProdRndTeamCode" column="lv2_prod_rnd_team_code"/>
        <result property="lv2ProdRdTeamCnName" column="lv2_prod_rd_team_cn_name"/>
        <result property="lv3ProdRndTeamCode" column="lv3_prod_rnd_team_code"/>
        <result property="lv3ProdRdTeamCnName" column="lv3_prod_rd_team_cn_name"/>
        <result property="lv0ProdListCode" column="lv0_prod_list_code"/>
        <result property="lv0ProdListCnName" column="lv0_prod_list_cn_name"/>
        <result property="prodRndTeamCode" column="prod_rnd_team_code"/>
        <result property="l3CegCode" column="l3_ceg_code"/>
        <result property="l3CegCnName" column="l3_ceg_cn_name"/>
        <result property="l4CegCode" column="l4_ceg_code"/>
        <result property="l4CegCnName" column="l4_ceg_cn_name"/>
        <result property="categoryCode" column="category_code"/>
        <result property="categoryCnName" column="category_cn_name"/>
        <result property="itemCode" column="item_code"/>
        <result property="itemCnName" column="item_cn_name"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDdate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="groupCode" column="groupCode"/>
        <result property="groupLevel" column="groupLevel"/>
        <result property="groupLevel" column="group_level"/>
        <result property="groupCnName" column="groupCnName"/>
        <result property="weightRate" column="weight_rate"/>
        <result property="granularityType" column="granularityType"/>
        <result property="customId" column="custom_id"/>
        <result property="topItemCode" column="top_item_code"/>
        <result property="itemNum" column="itemNum"/>
        <result property="enableFlag" column="enable_flag"/>
    </resultMap>

    <select id="reverseFindLv1ProdCode" resultMap="resultMap">
        SELECT DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name
        <if test='granularityType=="U"'>
            FROM  fin_dm_opt_foi.${tablePreFix}_view_info_d
        </if>
        <if test='granularityType=="P"'>
            FROM  fin_dm_opt_foi.${tablePreFix}_pft_view_info_d
        </if>
        <if test='granularityType=="D"'>
            FROM  fin_dm_opt_foi.${tablePreFix}_dms_view_info_d
        </if>
        WHERE view_flag = #{viewFlag,jdbcType=VARCHAR} and del_flag ='N' and caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        and oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        <if test='lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='reverseFlag == true and purLevel =="CEG" and purCodeList != null  and purCodeList.size() > 0'>
            <foreach collection='purCodeList' item="code" open="AND l3_ceg_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='reverseFlag == true and purLevel =="MODL" and purCodeList != null  and purCodeList.size() > 0 '>
            <foreach collection='purCodeList' item="code" open="AND l4_ceg_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='reverseFlag == true and purLevel =="CATEGORY" and purCodeList != null  and purCodeList.size() > 0'>
            <foreach collection='purCodeList' item="code" open="AND category_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
    </select>

    <select id="reverseFindLv1ProdCodeMonth" resultMap="resultMap">
        SELECT DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name
        <if test='granularityType=="U"'>
            FROM  fin_dm_opt_foi.${tablePreFix}_top_item_info_t
        </if>
        <if test='granularityType=="P"'>
            FROM  fin_dm_opt_foi.${tablePreFix}_pft_top_item_info_t
        </if>
        <if test='granularityType=="D"'>
            FROM  fin_dm_opt_foi.${tablePreFix}_dms_top_item_info_t
        </if>
        WHERE view_flag = #{viewFlag} and del_flag ='N' and caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        and oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        and IS_TOP_FLAG ='Y' and DOUBLE_FLAG ='Y' and version_id = #{monthVersionId,jdbcType=NUMERIC}
        <if test='lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='reverseFlag == true and purLevel =="CEG" and purCodeList != null and purCodeList.size() > 0'>
            <foreach collection='purCodeList' item="code" open="AND top_l3_ceg_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='reverseFlag == true and purLevel =="MODL" and purCodeList != null and  purCodeList.size() > 0 '>
            <foreach collection='purCodeList' item="code" open="AND top_l4_ceg_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='reverseFlag == true and purLevel =="CATEGORY" and purCodeList != null and  purCodeList.size() > 0'>
            <foreach collection='purCodeList' item="code" open="AND top_category_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
    </select>
    <sql id="weight_rate">
        <if test='viewFlag != null and viewFlag == "0"'>
            <choose>
                <when test='groupLevel == "CATEGORY"'>
                    DISTINCT item_code AS groupCode, item_cn_name AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "MODL"'>
                    DISTINCT category_code AS groupCode, category_code || ' ' || category_cn_name AS groupCnName,
                    'CATEGORY' AS groupLevel
                </when>
                <when test='groupLevel == "CEG"'>
                    DISTINCT l4_ceg_code AS groupCode, l4_ceg_short_cn_name AS groupCnName,'MODL' AS groupLevel
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT l3_ceg_code AS groupCode, l3_ceg_short_cn_name AS groupCnName, 'CEG' AS groupLevel,weight_rate
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "1"'>
            <choose>
                <when test='groupLevel == "CATEGORY"'>
                    DISTINCT item_code AS groupCode, item_cn_name AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "MODL"'>
                    DISTINCT category_code AS groupCode, category_code || ' ' || category_cn_name AS groupCnName,
                    'CATEGORY' AS groupLevel
                </when>
                <when test='groupLevel == "CEG"'>
                    DISTINCT l4_ceg_code AS groupCode, l4_ceg_short_cn_name AS groupCnName,'MODL' AS groupLevel
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT l3_ceg_code AS groupCode, l3_ceg_short_cn_name AS groupCnName, 'CEG' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "2"'>
            <choose>
                <when test='groupLevel == "CATEGORY"'>
                    DISTINCT item_code AS groupCode, item_cn_name AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "MODL"'>
                    DISTINCT category_code AS groupCode, category_code || ' ' || category_cn_name AS groupCnName, 'CATEGORY' AS groupLevel
                </when>
                <when test='groupLevel == "CEG"'>
                    DISTINCT l4_ceg_code AS groupCode, l4_ceg_short_cn_name AS groupCnName,'MODL' AS groupLevel
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT l3_ceg_code AS groupCode, l3_ceg_short_cn_name AS groupCnName, 'CEG' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                    groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "3"'>
            <choose>
                <when test='groupLevel == "CATEGORY"'>
                    DISTINCT item_code AS groupCode, item_cn_name AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "MODL"'>
                    DISTINCT category_code AS groupCode, category_code || ' ' || category_cn_name AS groupCnName, 'CATEGORY' AS groupLevel
                </when>
                <when test='groupLevel == "CEG"'>
                    DISTINCT l4_ceg_code AS groupCode, l4_ceg_short_cn_name AS groupCnName,'MODL' AS groupLevel
                </when>
                <when test='teamLevel == "LV3"'>
                    DISTINCT l3_ceg_code AS groupCode, l3_ceg_short_cn_name AS groupCnName, 'CEG' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName,
                    'LV3' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "7"'>
            <choose>
                <when test='groupLevel == "CATEGORY"'>
                    DISTINCT item_code AS groupCode, item_cn_name AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "MODL"'>
                    DISTINCT category_code AS groupCode, category_code || ' ' || category_cn_name AS groupCnName, 'CATEGORY' AS groupLevel
                </when>
                <when test='groupLevel == "CEG"'>
                    DISTINCT l4_ceg_code AS groupCode, l4_ceg_short_cn_name AS groupCnName,'MODL' AS groupLevel
                </when>
                <when test='teamLevel == "LV4"'>
                    DISTINCT l3_ceg_code AS groupCode, l3_ceg_short_cn_name AS groupCnName, 'CEG' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV3"'>
                    DISTINCT lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code AS groupCode, lv4_prod_rd_team_cn_name AS groupCnName,
                    'LV4' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName,
                    'LV3' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
    </sql>
    <sql id="absolute_weight">
        <if test='viewFlag != null and viewFlag == "0"'>
            <choose>
                <when test='groupLevel == "CATEGORY"'>
                    DISTINCT item_code AS groupCode, item_cn_name AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "MODL"'>
                    DISTINCT category_code AS groupCode, category_code || ' ' || category_cn_name AS groupCnName,
                    'CATEGORY' AS groupLevel
                </when>
                <when test='groupLevel == "CEG"'>
                    DISTINCT l4_ceg_code AS groupCode, l4_ceg_short_cn_name AS groupCnName,'MODL' AS groupLevel
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT l3_ceg_code AS groupCode, l3_ceg_short_cn_name AS groupCnName, 'CEG' AS groupLevel,absolute_weight
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "1"'>
            <choose>
                <when test='groupLevel == "CATEGORY"'>
                    DISTINCT item_code AS groupCode, item_cn_name AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "MODL"'>
                    DISTINCT category_code AS groupCode, category_code || ' ' || category_cn_name AS groupCnName,
                    'CATEGORY' AS groupLevel
                </when>
                <when test='groupLevel == "CEG"'>
                    DISTINCT l4_ceg_code AS groupCode, l4_ceg_short_cn_name AS groupCnName,'MODL' AS groupLevel
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT l3_ceg_code AS groupCode, l3_ceg_short_cn_name AS groupCnName, 'CEG' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "2"'>
            <choose>
                <when test='groupLevel == "CATEGORY"'>
                    DISTINCT item_code AS groupCode, item_cn_name AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "MODL"'>
                    DISTINCT category_code AS groupCode, category_code || ' ' || category_cn_name AS groupCnName, 'CATEGORY' AS groupLevel
                </when>
                <when test='groupLevel == "CEG"'>
                    DISTINCT l4_ceg_code AS groupCode, l4_ceg_short_cn_name AS groupCnName,'MODL' AS groupLevel
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT l3_ceg_code AS groupCode, l3_ceg_short_cn_name AS groupCnName, 'CEG' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                    groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "3"'>
            <choose>
                <when test='groupLevel == "CATEGORY"'>
                    DISTINCT item_code AS groupCode, item_cn_name AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "MODL"'>
                    DISTINCT category_code AS groupCode, category_code || ' ' || category_cn_name AS groupCnName, 'CATEGORY' AS groupLevel
                </when>
                <when test='groupLevel == "CEG"'>
                    DISTINCT l4_ceg_code AS groupCode, l4_ceg_short_cn_name AS groupCnName,'MODL' AS groupLevel
                </when>
                <when test='teamLevel == "LV3"'>
                    DISTINCT l3_ceg_code AS groupCode, l3_ceg_short_cn_name AS groupCnName, 'CEG' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName,
                    'LV3' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "7"'>
            <choose>
                <when test='groupLevel == "CATEGORY"'>
                    DISTINCT item_code AS groupCode, item_cn_name AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "MODL"'>
                    DISTINCT category_code AS groupCode, category_code || ' ' || category_cn_name AS groupCnName, 'CATEGORY' AS groupLevel
                </when>
                <when test='groupLevel == "CEG"'>
                    DISTINCT l4_ceg_code AS groupCode, l4_ceg_short_cn_name AS groupCnName,'MODL' AS groupLevel
                </when>
                <when test='teamLevel == "LV4"'>
                    DISTINCT l3_ceg_code AS groupCode, l3_ceg_short_cn_name AS groupCnName, 'CEG' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV3"'>
                    DISTINCT lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code AS groupCode, lv4_prod_rd_team_cn_name AS groupCnName,
                    'LV4' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName,
                    'LV3' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
    </sql>
    <select id="viewInfoList" resultMap="resultMap">
        SELECT
        <if test='isMultipleSelect == false'>
            <include refid="weight_rate"></include>
        </if>
        <if test='isMultipleSelect == true'>
            <include refid="absolute_weight"></include>
        </if>
        <choose>
            <when test='groupLevel == "CATEGORY" || groupLevel =="CEG" || groupLevel=="MODL"'>
                from fin_dm_opt_foi.${tablePreFix}_view_info_d amp
            </when>
            <when test='teamLevel == "LV0" and viewFlag != "0"'>
                from fin_dm_opt_foi.${tablePreFix}_view_info_d amp
            </when>
            <when test='teamLevel =="LV4"'>
                from fin_dm_opt_foi.${tablePreFix}_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.l3_ceg_code = weight.group_code
                and amp.lv4_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.period_year = #{periodYear,jdbcType=VARCHAR} and weight.version_id = #{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel =="LV3" and viewFlag =="7"'>
                from fin_dm_opt_foi.${tablePreFix}_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.lv4_prod_rnd_team_code = weight.group_code
                and amp.lv3_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel =="LV3" and viewFlag !="7"'>
                from fin_dm_opt_foi.${tablePreFix}_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.l3_ceg_code = weight.group_code
                and amp.lv3_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV2" and viewFlag =="2"'>
                from fin_dm_opt_foi.${tablePreFix}_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.l3_ceg_code = weight.group_code
                and amp.lv2_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV2" and viewFlag !="2"'>
                from fin_dm_opt_foi.${tablePreFix}_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.lv3_prod_rnd_team_code = weight.group_code
                and amp.lv2_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV1" and viewFlag =="1"'>
                from fin_dm_opt_foi.${tablePreFix}_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.l3_ceg_code = weight.group_code
                and amp.lv1_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV1" and viewFlag !="1"'>
                from fin_dm_opt_foi.${tablePreFix}_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.lv2_prod_rnd_team_code = weight.group_code
                and amp.lv1_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV0" and viewFlag == "0"'>
                from fin_dm_opt_foi.${tablePreFix}_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.l3_ceg_code = weight.group_code
                and amp.lv0_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
        </choose>
        WHERE amp.view_flag = #{viewFlag,jdbcType=VARCHAR} and amp.del_flag ='N' and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        and amp.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and amp.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        <include refid="prod_rnd_team_code_level"></include>
        <choose>
            <when test='groupLevel == "CEG" and groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND amp.l3_ceg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='groupLevel == "MODL" and groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND amp.l4_ceg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='groupLevel == "CATEGORY" and groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND amp.category_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='nextGroupLevel!=null and nextGroupLevel!=""'>
            AND amp.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
        </if>
        <if test='teamLevel == "LV0" and viewFlag !="0" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND amp.lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamLevel == "LV1" and viewFlag !="1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND amp.lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='isMultipleSelect == false'>
            <include refid="orderby_weight"></include>
        </if>
        <if test='isMultipleSelect == true'>
            <include refid="orderby_absweight"></include>
        </if>

    </select>
    <sql id = "orderby_weight">
        <if test='groupLevel != "CATEGORY" and groupLevel != "CEG" and groupLevel != "MODL" and teamLevel != "LV0"'>
            order by weight_rate desc
        </if>
        <if test='groupLevel != "CATEGORY" and groupLevel != "CEG" and groupLevel != "MODL" and teamLevel == "LV0" and viewFlag == "0"'>
            order by weight_rate desc
        </if>
    </sql>
    <sql id = "orderby_absweight">
        <if test='groupLevel != "CATEGORY" and groupLevel != "CEG" and groupLevel != "MODL" and teamLevel != "LV0"'>
            order by absolute_weight desc
        </if>
        <if test='groupLevel != "CATEGORY" and groupLevel != "CEG" and groupLevel != "MODL" and teamLevel == "LV0" and viewFlag == "0"'>
            order by absolute_weight desc
        </if>
    </sql>
    <sql id ="monthWeight_rate" >
        <if test='viewFlag != null and viewFlag == "0"'>
            <choose>
                <when test='groupLevel == "CATEGORY"'>
                    DISTINCT TOP_ITEM_CODE AS groupCode, TOP_ITEM_CN_NAME AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "MODL"'>
                    DISTINCT TOP_CATEGORY_CODE AS groupCode, TOP_CATEGORY_CODE || ' ' || TOP_CATEGORY_CN_NAME AS
                    groupCnName, 'CATEGORY' AS groupLevel
                </when>
                <when test='groupLevel == "CEG"'>
                    DISTINCT TOP_L4_CEG_CODE AS groupCode, TOP_L4_CEG_SHORT_CN_NAME AS groupCnName,'MODL' AS groupLevel
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT TOP_L3_CEG_CODE AS groupCode, TOP_L3_CEG_SHORT_CN_NAME AS groupCnName, 'CEG' AS groupLevel,weight.weight_rate
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "1"'>
            <choose>
                <when test='groupLevel == "CATEGORY"'>
                    DISTINCT TOP_ITEM_CODE AS groupCode, TOP_ITEM_CN_NAME AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "MODL"'>
                    DISTINCT TOP_CATEGORY_CODE AS groupCode, TOP_CATEGORY_CODE || ' ' || TOP_CATEGORY_CN_NAME AS
                    groupCnName, 'CATEGORY' AS groupLevel
                </when>
                <when test='groupLevel == "CEG"'>
                    DISTINCT TOP_L4_CEG_CODE AS groupCode, TOP_L4_CEG_SHORT_CN_NAME AS groupCnName,'MODL' AS groupLevel
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT TOP_L3_CEG_CODE AS groupCode, TOP_L3_CEG_SHORT_CN_NAME AS groupCnName, 'CEG' AS groupLevel,weight.weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "2"'>
            <choose>
                <when test='groupLevel == "CATEGORY"'>
                    DISTINCT TOP_ITEM_CODE AS groupCode, TOP_ITEM_CN_NAME AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "MODL"'>
                    DISTINCT TOP_CATEGORY_CODE AS groupCode, TOP_CATEGORY_CODE || ' ' || TOP_CATEGORY_CN_NAME AS
                    groupCnName, 'CATEGORY' AS groupLevel
                </when>
                <when test='groupLevel == "CEG"'>
                    DISTINCT TOP_L4_CEG_CODE AS groupCode, TOP_L4_CEG_SHORT_CN_NAME AS groupCnName,'MODL' AS groupLevel
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT TOP_L3_CEG_CODE AS groupCode, TOP_L3_CEG_SHORT_CN_NAME AS groupCnName, 'CEG' AS groupLevel,weight.weight_rate
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                    groupLevel,weight.weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "3"'>
            <choose>
                <when test='groupLevel == "CATEGORY"'>
                    DISTINCT TOP_ITEM_CODE AS groupCode, TOP_ITEM_CN_NAME AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "MODL"'>
                    DISTINCT TOP_CATEGORY_CODE AS groupCode, TOP_CATEGORY_CODE || ' ' || TOP_CATEGORY_CN_NAME AS
                    groupCnName, 'CATEGORY' AS groupLevel
                </when>
                <when test='groupLevel == "CEG"'>
                    DISTINCT TOP_L4_CEG_CODE AS groupCode, TOP_L4_CEG_SHORT_CN_NAME AS groupCnName,'MODL' AS groupLevel
                </when>
                <when test='teamLevel == "LV3"'>
                    DISTINCT TOP_L3_CEG_CODE AS groupCode, TOP_L3_CEG_SHORT_CN_NAME AS groupCnName, 'CEG' AS groupLevel,weight.weight_rate
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS
                    groupLevel,weight.weight_rate
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,weight.weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "7"'>
            <choose>
                <when test='groupLevel == "CATEGORY"'>
                    DISTINCT TOP_ITEM_CODE AS groupCode, TOP_ITEM_CN_NAME AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "MODL"'>
                    DISTINCT TOP_CATEGORY_CODE AS groupCode, TOP_CATEGORY_CODE || ' ' || TOP_CATEGORY_CN_NAME AS
                    groupCnName, 'CATEGORY' AS groupLevel
                </when>
                <when test='groupLevel == "CEG"'>
                    DISTINCT TOP_L4_CEG_CODE AS groupCode, TOP_L4_CEG_SHORT_CN_NAME AS groupCnName,'MODL' AS groupLevel
                </when>
                <when test='teamLevel == "LV4"'>
                    DISTINCT TOP_L3_CEG_CODE AS groupCode, TOP_L3_CEG_SHORT_CN_NAME AS groupCnName, 'CEG' AS groupLevel,weight.weight_rate
                </when>
                <when test='teamLevel == "LV3"'>
                    DISTINCT lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code AS groupCode, lv4_prod_rd_team_cn_name AS groupCnName, 'LV4' AS
                    groupLevel,weight.weight_rate
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS
                    groupLevel,weight.weight_rate
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,weight.weight_rate
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
    </sql>

    <sql id ="monthAbsolute_rate" >
        <if test='viewFlag != null and viewFlag == "0"'>
            <choose>
                <when test='groupLevel == "CATEGORY"'>
                    DISTINCT TOP_ITEM_CODE AS groupCode, TOP_ITEM_CN_NAME AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "MODL"'>
                    DISTINCT TOP_CATEGORY_CODE AS groupCode, TOP_CATEGORY_CODE || ' ' || TOP_CATEGORY_CN_NAME AS
                    groupCnName, 'CATEGORY' AS groupLevel
                </when>
                <when test='groupLevel == "CEG"'>
                    DISTINCT TOP_L4_CEG_CODE AS groupCode, TOP_L4_CEG_SHORT_CN_NAME AS groupCnName,'MODL' AS groupLevel
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT TOP_L3_CEG_CODE AS groupCode, TOP_L3_CEG_SHORT_CN_NAME AS groupCnName, 'CEG' AS groupLevel,weight.absolute_weight
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "1"'>
            <choose>
                <when test='groupLevel == "CATEGORY"'>
                    DISTINCT TOP_ITEM_CODE AS groupCode, TOP_ITEM_CN_NAME AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "MODL"'>
                    DISTINCT TOP_CATEGORY_CODE AS groupCode, TOP_CATEGORY_CODE || ' ' || TOP_CATEGORY_CN_NAME AS
                    groupCnName, 'CATEGORY' AS groupLevel
                </when>
                <when test='groupLevel == "CEG"'>
                    DISTINCT TOP_L4_CEG_CODE AS groupCode, TOP_L4_CEG_SHORT_CN_NAME AS groupCnName,'MODL' AS groupLevel
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT TOP_L3_CEG_CODE AS groupCode, TOP_L3_CEG_SHORT_CN_NAME AS groupCnName, 'CEG' AS groupLevel,weight.absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "2"'>
            <choose>
                <when test='groupLevel == "CATEGORY"'>
                    DISTINCT TOP_ITEM_CODE AS groupCode, TOP_ITEM_CN_NAME AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "MODL"'>
                    DISTINCT TOP_CATEGORY_CODE AS groupCode, TOP_CATEGORY_CODE || ' ' || TOP_CATEGORY_CN_NAME AS
                    groupCnName, 'CATEGORY' AS groupLevel
                </when>
                <when test='groupLevel == "CEG"'>
                    DISTINCT TOP_L4_CEG_CODE AS groupCode, TOP_L4_CEG_SHORT_CN_NAME AS groupCnName,'MODL' AS groupLevel
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT TOP_L3_CEG_CODE AS groupCode, TOP_L3_CEG_SHORT_CN_NAME AS groupCnName, 'CEG' AS groupLevel,weight.absolute_weight
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                    groupLevel,weight.absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "3"'>
            <choose>
                <when test='groupLevel == "CATEGORY"'>
                    DISTINCT TOP_ITEM_CODE AS groupCode, TOP_ITEM_CN_NAME AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "MODL"'>
                    DISTINCT TOP_CATEGORY_CODE AS groupCode, TOP_CATEGORY_CODE || ' ' || TOP_CATEGORY_CN_NAME AS
                    groupCnName, 'CATEGORY' AS groupLevel
                </when>
                <when test='groupLevel == "CEG"'>
                    DISTINCT TOP_L4_CEG_CODE AS groupCode, TOP_L4_CEG_SHORT_CN_NAME AS groupCnName,'MODL' AS groupLevel
                </when>
                <when test='teamLevel == "LV3"'>
                    DISTINCT TOP_L3_CEG_CODE AS groupCode, TOP_L3_CEG_SHORT_CN_NAME AS groupCnName, 'CEG' AS groupLevel,weight.absolute_weight
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS
                    groupLevel,weight.absolute_weight
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,weight.absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "7"'>
            <choose>
                <when test='groupLevel == "CATEGORY"'>
                    DISTINCT TOP_ITEM_CODE AS groupCode, TOP_ITEM_CN_NAME AS groupCnName, 'ITEM' AS groupLevel
                </when>
                <when test='groupLevel == "MODL"'>
                    DISTINCT TOP_CATEGORY_CODE AS groupCode, TOP_CATEGORY_CODE || ' ' || TOP_CATEGORY_CN_NAME AS
                    groupCnName, 'CATEGORY' AS groupLevel
                </when>
                <when test='groupLevel == "CEG"'>
                    DISTINCT TOP_L4_CEG_CODE AS groupCode, TOP_L4_CEG_SHORT_CN_NAME AS groupCnName,'MODL' AS groupLevel
                </when>
                <when test='teamLevel == "LV4"'>
                    DISTINCT TOP_L3_CEG_CODE AS groupCode, TOP_L3_CEG_SHORT_CN_NAME AS groupCnName, 'CEG' AS groupLevel,weight.absolute_weight
                </when>
                <when test='teamLevel == "LV3"'>
                    DISTINCT lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code AS groupCode, lv4_prod_rd_team_cn_name AS groupCnName, 'LV4' AS
                    groupLevel,weight.absolute_weight
                </when>
                <when test='teamLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS
                    groupLevel,weight.absolute_weight
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS groupLevel,weight.absolute_weight
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
    </sql>

    <select id="viewInfoListForMonth" resultMap="resultMap">
        SELECT
        <if test='isMultipleSelect == false'>
            <include refid="monthWeight_rate"></include>
        </if>
        <if test='isMultipleSelect == true'>
            <include refid="monthAbsolute_rate"></include>
        </if>
        <choose>
            <when test='groupLevel == "CATEGORY" || groupLevel =="CEG" || groupLevel=="MODL"'>
                from fin_dm_opt_foi.${tablePreFix}_top_item_info_t amp
            </when>
            <when test='teamLevel == "LV0" and viewFlag != "0"'>
                from fin_dm_opt_foi.${tablePreFix}_top_item_info_t amp
            </when>
            <when test='teamLevel =="LV4"'>
                from fin_dm_opt_foi.${tablePreFix}_top_item_info_t amp left join fin_dm_opt_foi.${tablePreFix}_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.top_l3_ceg_code = weight.group_code
                and amp.lv4_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
                and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel =="LV3" and viewFlag =="7"'>
                from fin_dm_opt_foi.${tablePreFix}_top_item_info_t amp left join fin_dm_opt_foi.${tablePreFix}_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.lv4_prod_rnd_team_code = weight.group_code
                and amp.lv3_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N'  and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel =="LV3" and viewFlag !="7"'>
                from fin_dm_opt_foi.${tablePreFix}_top_item_info_t amp left join fin_dm_opt_foi.${tablePreFix}_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.top_l3_ceg_code = weight.group_code
                and amp.lv3_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N'  and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV2" and viewFlag !="2"'>
                from fin_dm_opt_foi.${tablePreFix}_top_item_info_t amp left join fin_dm_opt_foi.${tablePreFix}_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.lv3_prod_rnd_team_code = weight.group_code
                and amp.lv2_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N'  AND weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV2" and viewFlag =="2"'>
                from fin_dm_opt_foi.${tablePreFix}_top_item_info_t amp left join fin_dm_opt_foi.${tablePreFix}_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.top_l3_ceg_code = weight.group_code
                and amp.lv2_prod_rnd_team_code =weight.prod_rnd_team_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N'  AND weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV1" and viewFlag !="1"'>
                from fin_dm_opt_foi.${tablePreFix}_top_item_info_t amp left join fin_dm_opt_foi.${tablePreFix}_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.lv2_prod_rnd_team_code = weight.group_code
                and amp.lv1_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N'  AND weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV1" and viewFlag =="1"'>
                from fin_dm_opt_foi.${tablePreFix}_top_item_info_t amp left join fin_dm_opt_foi.${tablePreFix}_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.top_l3_ceg_code = weight.group_code
                and amp.lv1_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N'  AND weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV0" and viewFlag =="0"'>
                from fin_dm_opt_foi.${tablePreFix}_top_item_info_t amp left join fin_dm_opt_foi.${tablePreFix}_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.top_l3_ceg_code = weight.group_code
                and amp.lv0_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N'  AND weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
        </choose>
        where amp.del_flag = 'N'
        AND amp.is_top_flag ='Y'
        AND amp.double_flag ='Y'
        AND amp.version_id = #{monthVersionId,jdbcType=NUMERIC}
        AND amp.view_flag = #{viewFlag,jdbcType=VARCHAR}
        AND amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        and amp.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        and amp.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
      <include refid="prod_rnd_team_code_level"></include>
        <choose>
            <when test='groupLevel == "CEG" and groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND amp.top_l3_ceg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='groupLevel == "MODL" and groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND amp.top_l4_ceg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='groupLevel == "CATEGORY" and groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND amp.top_category_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='teamLevel == "LV0" and viewFlag !="0" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND amp.lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamLevel == "LV1" and viewFlag !="1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND amp.lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='isMultipleSelect == false'>
            <include refid="month_orderweight"></include>
        </if>
        <if test='isMultipleSelect == true'>
            <include refid="month_order_absweight"></include>
        </if>
    </select>

    <sql id="prod_rnd_team_code_level">
        <choose>
            <when test='teamLevel == "LV1" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND amp.lv1_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='teamLevel == "LV2" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND amp.lv2_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='teamLevel == "LV3" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND amp.lv3_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='teamLevel == "LV4" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND amp.lv4_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <otherwise>
            </otherwise>
        </choose>
    </sql>

    <select id="viewInfoListForTopCate" resultMap="resultMap">
        SELECT
        <if test='isMultipleSelect == false'>
            <include refid="monthWeight_rate"></include>
        </if>
        <if test='isMultipleSelect == true'>
            <include refid="monthAbsolute_rate"></include>
        </if>
        <choose>
            <when test='groupLevel == "CATEGORY" || groupLevel =="CEG" || groupLevel=="MODL"'>
                from fin_dm_opt_foi.${tablePreFix}_top_cate_info_t amp
            </when>
            <when test='teamLevel == "LV0" and viewFlag != "0"'>
                from fin_dm_opt_foi.${tablePreFix}_top_cate_info_t amp
            </when>
            <when test='teamLevel =="LV4"'>
                from fin_dm_opt_foi.${tablePreFix}_top_cate_info_t amp left join fin_dm_opt_foi.${tablePreFix}_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.top_l3_ceg_code = weight.group_code
                and amp.lv4_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.period_year = #{periodYear,jdbcType=VARCHAR} and weight.version_id = #{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
                and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel =="LV3" and viewFlag =="7"'>
                from fin_dm_opt_foi.${tablePreFix}_top_cate_info_t amp left join fin_dm_opt_foi.${tablePreFix}_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.lv4_prod_rnd_team_code = weight.group_code
                and amp.lv3_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N'  AND weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel =="LV3" and viewFlag !="7"'>
                from fin_dm_opt_foi.${tablePreFix}_top_cate_info_t amp left join fin_dm_opt_foi.${tablePreFix}_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.top_l3_ceg_code = weight.group_code
                and amp.lv3_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N'  AND weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV2" and viewFlag !="2"'>
                from fin_dm_opt_foi.${tablePreFix}_top_cate_info_t amp left join fin_dm_opt_foi.${tablePreFix}_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.lv3_prod_rnd_team_code = weight.group_code
                and amp.lv2_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N'  AND weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV2" and viewFlag =="2"'>
                from fin_dm_opt_foi.${tablePreFix}_top_cate_info_t amp left join fin_dm_opt_foi.${tablePreFix}_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.top_l3_ceg_code = weight.group_code
                and amp.lv2_prod_rnd_team_code =weight.prod_rnd_team_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N'  AND weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV1" and viewFlag !="1"'>
                from fin_dm_opt_foi.${tablePreFix}_top_cate_info_t amp left join fin_dm_opt_foi.${tablePreFix}_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.lv2_prod_rnd_team_code = weight.group_code
                and amp.lv1_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N'  AND weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV1" and viewFlag =="1"'>
                from fin_dm_opt_foi.${tablePreFix}_top_cate_info_t amp left join fin_dm_opt_foi.${tablePreFix}_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.top_l3_ceg_code = weight.group_code
                and amp.lv1_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N'  AND weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
            <when test='teamLevel == "LV0" and viewFlag =="0"'>
                from fin_dm_opt_foi.${tablePreFix}_top_cate_info_t amp left join fin_dm_opt_foi.${tablePreFix}_annual_weight_t weight
                on amp.view_flag = weight.view_flag and amp.top_l3_ceg_code = weight.group_code
                and amp.lv0_prod_rnd_team_code = weight.prod_rnd_team_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N'  AND weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
        </choose>
        where amp.del_flag = 'N'
        AND amp.is_top_flag ='Y'
        AND amp.double_flag ='Y'
        AND amp.version_id = #{monthVersionId,jdbcType=NUMERIC}
        AND amp.view_flag = #{viewFlag,jdbcType=VARCHAR}
        AND amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        and amp.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        and amp.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        <include refid="prod_rnd_team_code_level"></include>
        <choose>
            <when test='groupLevel == "CEG" and groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND amp.top_l3_ceg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='groupLevel == "MODL" and groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND amp.top_l4_ceg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='groupLevel == "CATEGORY" and groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND amp.top_category_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='teamLevel == "LV0" and viewFlag !="0" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND amp.lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamLevel == "LV1" and viewFlag !="1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND amp.lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='isMultipleSelect == false'>
            <include refid="month_orderweight"></include>
        </if>
        <if test='isMultipleSelect == true'>
            <include refid="month_order_absweight"></include>
        </if>
    </select>

    <sql id ="month_orderweight">
        <if test='groupLevel != "CATEGORY" and groupLevel != "CEG" and groupLevel != "MODL" and teamLevel != "LV0"'>
            order by weight.weight_rate desc
        </if>
        <if test='groupLevel != "CATEGORY" and groupLevel != "CEG" and groupLevel != "MODL" and teamLevel == "LV0" and viewFlag == "0"'>
            order by weight.weight_rate desc
        </if>
    </sql>

    <sql id ="month_order_absweight">
        <if test='groupLevel != "CATEGORY" and groupLevel != "CEG" and groupLevel != "MODL" and teamLevel != "LV0"'>
            order by weight.absolute_weight desc
        </if>
        <if test='groupLevel != "CATEGORY" and groupLevel != "CEG" and groupLevel != "MODL" and teamLevel == "LV0" and viewFlag == "0"'>
            order by weight.absolute_weight desc
        </if>
    </sql>

    <select id="viewInfoKeyWordForMonth" resultMap="resultMap">
        SELECT
        <if test='groupLevel == "CATEGORY"'>
            DISTINCT TOP_L3_CEG_CODE as L3_CEG_CODE ,TOP_L3_CEG_SHORT_CN_NAME as L3_CEG_CN_NAME,
            TOP_L4_CEG_CODE as L4_CEG_CODE ,TOP_L4_CEG_SHORT_CN_NAME as L4_CEG_CN_NAME,
            TOP_CATEGORY_CODE as CATEGORY_CODE, TOP_CATEGORY_CODE || ' ' || TOP_CATEGORY_CN_NAME AS category_cn_name
        </if>
        from fin_dm_opt_foi.${tablePreFix}_top_item_info_t
        where del_flag = 'N'
        AND is_top_flag ='Y'
        AND double_flag ='Y'
        AND version_id = #{monthVersionId}
        AND view_flag = #{viewFlag}
        AND caliber_flag = #{caliberFlag}
        AND oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        AND lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        <choose>
            <when test='teamLevel == "LV1" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='teamLevel == "LV2" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='teamLevel == "LV3" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND lv3_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='teamLevel == "LV4" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND lv4_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test="keyWord != null and keyWord != ''">
            AND (UPPER(TOP_CATEGORY_CODE) LIKE CONCAT(CONCAT('%', UPPER(#{keyWord})) ,'%')
            OR UPPER(TOP_CATEGORY_CN_NAME) LIKE CONCAT(CONCAT('%', UPPER(#{keyWord})) ,'%')
            )
        </if>
    </select>

    <select id="viewInfoKeyWordForTopCate" resultMap="resultMap">
        SELECT
        <if test='groupLevel == "CATEGORY"'>
            DISTINCT TOP_L3_CEG_CODE as L3_CEG_CODE ,TOP_L3_CEG_SHORT_CN_NAME as L3_CEG_CN_NAME,
            TOP_L4_CEG_CODE as L4_CEG_CODE ,TOP_L4_CEG_SHORT_CN_NAME as L4_CEG_CN_NAME,
            TOP_CATEGORY_CODE as CATEGORY_CODE, TOP_CATEGORY_CODE || ' ' || TOP_CATEGORY_CN_NAME AS category_cn_name
        </if>
        from fin_dm_opt_foi.${tablePreFix}_top_cate_info_t
        where del_flag = 'N'
        AND is_top_flag ='Y'
        AND double_flag ='Y'
        AND version_id = #{monthVersionId}
        AND view_flag = #{viewFlag}
        AND caliber_flag = #{caliberFlag}
        AND oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        AND lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        <choose>
            <when test='teamLevel == "LV1" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='teamLevel == "LV2" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='teamLevel == "LV3" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND lv3_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='teamLevel == "LV4" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND lv4_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test="keyWord != null and keyWord != ''">
            AND (UPPER(TOP_CATEGORY_CODE) LIKE CONCAT(CONCAT('%', UPPER(#{keyWord})) ,'%')
            OR UPPER(TOP_CATEGORY_CN_NAME) LIKE CONCAT(CONCAT('%', UPPER(#{keyWord})) ,'%')
            )
        </if>
    </select>

    <select id="viewInfoKeyWordList" resultMap="resultMap">
        SELECT
        <if test='groupLevel == "CATEGORY"'>
            DISTINCT l3_ceg_code,l3_ceg_short_cn_name as l3_ceg_cn_name,l4_ceg_code,l4_ceg_short_cn_name as l4_ceg_cn_name, category_code, category_code || ' ' || category_cn_name AS
            category_cn_name
        </if>
        from fin_dm_opt_foi.${tablePreFix}_view_info_d
        WHERE view_flag = #{viewFlag} and del_flag ='N'
        AND caliber_flag = #{caliberFlag}
        AND oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        AND lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        <choose>
            <when test='teamLevel == "LV1" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='teamLevel == "LV2" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='teamLevel == "LV3" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND lv3_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='teamLevel == "LV4" and teamCodeList != null and teamCodeList.size() > 0'>
                <foreach collection='teamCodeList' item="code" open="AND lv4_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='groupLevel!=null and groupLevel!=""'>
            AND group_level = #{groupLevel}
        </if>
        <if test="keyWord != null and keyWord != ''">
            AND (UPPER(category_code) LIKE CONCAT(CONCAT('%', UPPER(#{keyWord})) ,'%')
            OR UPPER(category_cn_name) LIKE CONCAT(CONCAT('%', UPPER(#{keyWord})) ,'%')
            )
        </if>
    </select>

    <select id="viewFlagInfoList" resultMap="resultMap">
        select distinct view_flag, lv0_prod_rnd_team_code, lv0_prod_rd_team_cn_name, dimension_type as granularityType
        from
            fin_dm_opt_foi.${tablePreFix}_full_view_dim
        where  cost_type = #{costType}  and caliber_flag = #{caliberFlag} and  dimension_type = #{granularityType}
        order by view_flag
    </select>
    <select id="getLv0ProdList" resultMap="resultMap">
        select  distinct  CASE WHEN lv0_prod_list_code = 'GR' THEN 1
            WHEN lv0_prod_list_code = 'PDCG901160' THEN 2
            WHEN lv0_prod_list_code = 'PDCG901159' THEN 3
            ELSE 4 END,lv0_prod_list_code,lv0_prod_list_cn_name
        from fin_dm_opt_foi.${tablePreFix}_full_view_dim
        where view_flag = #{viewFlag} and caliber_flag = #{caliberFlag}
            and cost_type = #{costType} and dimension_type = #{granularityType}
        <if test='lv0ProdListCode!=null and lv0ProdListCode!=""'>
            and lv0_prod_list_code = #{lv0ProdListCode}
        </if>
        ORDER BY CASE WHEN lv0_prod_list_code = 'GR' THEN 1
        WHEN lv0_prod_list_code = 'PDCG901160' THEN 2
        WHEN lv0_prod_list_code = 'PDCG901159' THEN 3
        ELSE 4 END ASC
    </select>

    <select id="getCombinationSubByGroupLevel" resultMap="resultMap">
        SELECT
        <choose>
            <when test='nextGroupLevel == "LV1"'>
                distinct custom_id,lv1_prod_rnd_team_code AS groupCode ,CONCAT(lv1_prod_rd_team_cn_name,'(',custom_cn_name,')') AS groupCnName, 'LV1' AS group_level
            </when>
            <when test='nextGroupLevel == "LV2"'>
                distinct custom_id,lv2_prod_rnd_team_code AS groupCode ,CONCAT(lv2_prod_rd_team_cn_name,'(',custom_cn_name,')') AS groupCnName, 'LV2' AS group_level
            </when>
            <when test='nextGroupLevel == "LV3"'>
                distinct custom_id,lv3_prod_rnd_team_code AS groupCode ,CONCAT(lv3_prod_rd_team_cn_name,'(',custom_cn_name,')') AS groupCnName, 'LV3' AS group_level
            </when>
            <when test='nextGroupLevel == "LV4"'>
                distinct custom_id,lv4_prod_rnd_team_code AS groupCode ,CONCAT(lv4_prod_rd_team_cn_name,'(',custom_cn_name,')') AS groupCnName, 'LV4' AS group_level
            </when>
            <when test='nextGroupLevel == "L1"'>
                distinct custom_id,l1_name AS groupCode ,CONCAT(l1_name,'(',custom_cn_name,')') AS groupCnName, 'L1' AS group_level
            </when>
            <when test='nextGroupLevel == "L2"'>
                distinct custom_id,l2_name AS groupCode ,CONCAT(l2_name,'(',custom_cn_name,')') AS groupCnName, 'L2' AS group_level
            </when>
            <when test='nextGroupLevel == "CEG"'>
                distinct custom_id,l3_ceg_code AS groupCode ,CONCAT(l3_ceg_short_cn_name,'(',custom_cn_name,')') AS groupCnName, 'CEG' AS group_level
            </when>
            <when test='nextGroupLevel == "MODL"'>
                distinct custom_id,l4_ceg_code AS groupCode ,CONCAT(l4_ceg_short_cn_name,'(',custom_cn_name,')') AS groupCnName, 'MODL' AS group_level
            </when>
            <when test='nextGroupLevel == "COA"'>
                distinct custom_id,coa_code AS groupCode ,CONCAT(coa_cn_name,'(',custom_cn_name,')') AS groupCnName, 'COA' AS group_level
            </when>
            <when test='nextGroupLevel == "DIMENSION"'>
                distinct custom_id,dimension_code AS groupCode ,CONCAT(dimension_cn_name,'(',custom_cn_name,')') AS groupCnName, 'DIMENSION' AS group_level
            </when>
            <when test='nextGroupLevel == "SUBCATEGORY"'>
                distinct custom_id,dimension_subcategory_code AS groupCode ,CONCAT(dimension_subcategory_cn_name,'(',custom_cn_name,')') AS groupCnName, 'SUBCATEGORY' AS group_level
            </when>
            <when test='nextGroupLevel == "SUB_DETAIL"'>
                distinct custom_id,dimension_sub_detail_code AS groupCode ,CONCAT(dimension_sub_detail_cn_name,'(',custom_cn_name,')') AS groupCnName, 'SUB_DETAIL' AS group_level
            </when>
            <when test='nextGroupLevel == "SPART"'>
                distinct custom_id,spart_code AS groupCode ,CONCAT(spart_cn_name,'(',custom_cn_name,')') AS groupCnName, 'SPART' AS group_level
            </when>
            <when test='nextGroupLevel == "CATEGORY"'>
                distinct custom_id,category_code AS groupCode ,CONCAT(category_cn_name,'(',custom_cn_name,')') AS groupCnName, 'CATEGORY' AS group_level
            </when>
            <when test='nextGroupLevel == "ITEM"'>
                distinct custom_id,group_code AS groupCode ,CONCAT(group_cn_name,'(',custom_cn_name,')') AS groupCnName, 'ITEM' AS group_level
            </when>
        </choose>
        from fin_dm_opt_foi.${tablePreFix}_custom_comb_d
        where del_flag ='N'
        <if test='lv0ProdListCode != null and lv0ProdListCode!=""'>
            and lv0_prod_list_code = #{lv0ProdListCode}
        </if>
        <if test='viewFlag != null and viewFlag!=""'>
            and view_flag = #{viewFlag}
        </if>
        <if test='caliberFlag != null and caliberFlag!=""'>
            and caliber_flag = #{caliberFlag}
        </if>
        <if test='granularityType != null and granularityType!=""'>
            and granularity_type = #{granularityType}
        </if>
        <if test='overseaFlag != null and overseaFlag!=""'>
            and oversea_flag = #{overseaFlag}
        </if>
        <if test='roleId != null and roleId!=""'>
            and role_id = #{roleId}
        </if>
        <if test='userId != null and userId!=""'>
            and user_id = #{userId}
        </if>
        <if test='pageFlag != null and pageFlag!=""'>
            and (page_flag = #{pageFlag} or page_flag = concat('ALL_',#{pageFlag}::text))
        </if>
        <if test='nextGroupLevel =="LV1"'>
            and lv1_prod_rnd_team_code is not null
        </if>
        <if test='nextGroupLevel =="LV2"'>
            and lv2_prod_rnd_team_code is not null
        </if>
        <if test='nextGroupLevel =="LV3"'>
            and lv3_prod_rnd_team_code is not null
        </if>
        <if test='nextGroupLevel =="LV4"'>
            and lv4_prod_rnd_team_code is not null
        </if>
        <if test='nextGroupLevel =="L1"'>
            and l1_name is not null
        </if>
        <if test='nextGroupLevel =="L2"'>
            and l2_name is not null
        </if>
        <if test='nextGroupLevel =="CEG"'>
            and l3_ceg_code is not null
        </if>
        <if test='nextGroupLevel =="MODL"'>
            and l4_ceg_code is not null
        </if>
        <if test='nextGroupLevel =="COA"'>
            and coa_code is not null
        </if>
        <if test='nextGroupLevel =="DIMENSION"'>
            and dimension_code is not null
        </if>
        <if test='nextGroupLevel =="CATEGORY"'>
            and category_code is not null
        </if>
        <if test='nextGroupLevel =="SUBCATEGORY"'>
            and dimension_subcategory_code is not null
        </if>
        <if test='nextGroupLevel =="SUB_DETAIL"'>
            and dimension_sub_detail_code is not null
        </if>
        <if test='nextGroupLevel =="SPART"'>
            and spart_code is not null
        </if>
        <if test='nextGroupLevel =="ITEM"'>
            and group_level = #{nextGroupLevel}
        </if>
        <if test='customIdList != null and customIdList.size() > 0'>
            <foreach collection='customIdList' item="code" open="AND custom_id IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='combL1NameList != null and combL1NameList.size() > 0'>
            <foreach collection='combL1NameList' item="code" open="AND l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='combL2NameList != null and combL2NameList.size() > 0'>
            <foreach collection='combL2NameList' item="code" open="AND l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='combDimensionSubDetailCodeList != null and combDimensionSubDetailCodeList.size() > 0'>
            <foreach collection='combDimensionSubDetailCodeList' item="code" open="AND dimension_sub_detail_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='combDimensionSubcategoryCodeList != null and combDimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='combDimensionSubcategoryCodeList' item="code" open="AND dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='combDimensionCodeList != null and combDimensionCodeList.size() > 0'>
            <foreach collection='combDimensionCodeList' item="code" open="AND dimension_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='combCoaCodeList != null and combCoaCodeList.size() > 0'>
            <foreach collection='combCoaCodeList' item="code" open="AND coa_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='combSpartCodeList != null and combSpartCodeList.size() > 0'>
            <foreach collection='combSpartCodeList' item="code" open="AND spart_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <choose>
            <when test='teamLevel == "LV1" and combTeamCodeList != null and combTeamCodeList.size() > 0'>
                <foreach collection='combTeamCodeList' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='teamLevel == "LV2" and combTeamCodeList != null and combTeamCodeList.size() > 0'>
                <foreach collection='combTeamCodeList' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='teamLevel == "LV3" and combTeamCodeList != null and combTeamCodeList.size() > 0'>
                <foreach collection='combTeamCodeList' item="code" open="AND lv3_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='teamLevel == "LV4" and combTeamCodeList != null and combTeamCodeList.size() > 0'>
                <foreach collection='combTeamCodeList' item="code" open="AND lv4_prod_rnd_team_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <choose>
            <when test='groupLevel == "CEG" and combGroupCodeList != null and combGroupCodeList.size() > 0'>
                <foreach collection='combGroupCodeList' item="code" open="AND l3_ceg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='groupLevel == "MODL" and combGroupCodeList != null and combGroupCodeList.size() > 0'>
                <foreach collection='combGroupCodeList' item="code" open="AND l4_ceg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='groupLevel == "CATEGORY" and combGroupCodeList != null and combGroupCodeList.size() > 0'>
                <foreach collection='combGroupCodeList' item="code" open="AND category_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <otherwise>
            </otherwise>
        </choose>
    </select>

    <select id="getCombinationByGroupLevel" resultMap="resultMap">
        SELECT
        <choose>
            <when test='nextGroupLevel == "LV1"'>
                distinct custom_id,lv1_prod_rnd_team_code AS groupCode ,CONCAT(lv1_prod_rd_team_cn_name,'(',custom_cn_name,')') AS groupCnName, 'LV1' AS group_level
            </when>
            <when test='nextGroupLevel == "LV2"'>
                distinct custom_id,lv2_prod_rnd_team_code AS groupCode ,CONCAT(lv2_prod_rd_team_cn_name,'(',custom_cn_name,')') AS groupCnName, 'LV2' AS group_level
            </when>
            <when test='nextGroupLevel == "LV3"'>
                distinct custom_id,lv3_prod_rnd_team_code AS groupCode ,CONCAT(lv3_prod_rd_team_cn_name,'(',custom_cn_name,')') AS groupCnName, 'LV3' AS group_level
            </when>
            <when test='nextGroupLevel == "LV4"'>
                distinct custom_id,lv4_prod_rnd_team_code AS groupCode ,CONCAT(lv4_prod_rd_team_cn_name,'(',custom_cn_name,')') AS groupCnName, 'LV4' AS group_level
            </when>
            <when test='nextGroupLevel == "L1"'>
                distinct custom_id,l1_name AS groupCode ,CONCAT(l1_name,'(',custom_cn_name,')') AS groupCnName, 'L1' AS group_level
            </when>
            <when test='nextGroupLevel == "L2"'>
                distinct custom_id,l2_name AS groupCode ,CONCAT(l2_name,'(',custom_cn_name,')') AS groupCnName, 'L2' AS group_level
            </when>
            <when test='nextGroupLevel == "CEG"'>
                distinct custom_id,l3_ceg_code AS groupCode ,CONCAT(l3_ceg_short_cn_name,'(',custom_cn_name,')') AS groupCnName, 'CEG' AS group_level
            </when>
            <when test='nextGroupLevel == "MODL"'>
                distinct custom_id,l4_ceg_code AS groupCode ,CONCAT(l4_ceg_short_cn_name,'(',custom_cn_name,')') AS groupCnName, 'MODL' AS group_level
            </when>
            <when test='nextGroupLevel == "COA"'>
                distinct custom_id,coa_code AS groupCode ,CONCAT(coa_cn_name,'(',custom_cn_name,')') AS groupCnName, 'COA' AS group_level
            </when>
            <when test='nextGroupLevel == "DIMENSION"'>
                distinct custom_id,dimension_code AS groupCode ,CONCAT(dimension_cn_name,'(',custom_cn_name,')') AS groupCnName, 'DIMENSION' AS group_level
            </when>
            <when test='nextGroupLevel == "SUBCATEGORY"'>
                distinct custom_id,dimension_subcategory_code AS groupCode ,CONCAT(dimension_subcategory_cn_name,'(',custom_cn_name,')') AS groupCnName, 'SUBCATEGORY' AS group_level
            </when>
            <when test='nextGroupLevel == "SUB_DETAIL"'>
                distinct custom_id,dimension_sub_detail_code AS groupCode ,CONCAT(dimension_sub_detail_cn_name,'(',custom_cn_name,')') AS groupCnName, 'SUB_DETAIL' AS group_level
            </when>
            <when test='nextGroupLevel == "SPART"'>
                distinct custom_id,spart_code AS groupCode ,CONCAT(spart_cn_name,'(',custom_cn_name,')') AS groupCnName, 'SPART' AS group_level
            </when>
            <when test='nextGroupLevel == "CATEGORY"'>
                distinct custom_id,category_code AS groupCode ,CONCAT(category_cn_name,'(',custom_cn_name,')') AS groupCnName, 'CATEGORY' AS group_level
            </when>
            <when test='nextGroupLevel == "ITEM"'>
                distinct custom_id,group_code AS groupCode ,CONCAT(group_cn_name,'(',custom_cn_name,')') AS groupCnName, 'ITEM' AS group_level
            </when>
        </choose>
        from fin_dm_opt_foi.${tablePreFix}_custom_comb_d
        where del_flag ='N'
        <if test='lv0ProdListCode != null and lv0ProdListCode!=""'>
            and lv0_prod_list_code = #{lv0ProdListCode}
        </if>
        <if test='viewFlag != null and viewFlag!=""'>
            and view_flag = #{viewFlag}
        </if>
        <if test='caliberFlag != null and caliberFlag!=""'>
            and caliber_flag = #{caliberFlag}
        </if>
        <if test='granularityType != null and granularityType!=""'>
            and granularity_type = #{granularityType}
        </if>
        <if test='overseaFlag != null and overseaFlag!=""'>
            and oversea_flag = #{overseaFlag}
        </if>
        <if test='roleId != null and roleId!=""'>
            and role_id = #{roleId}
        </if>
        <if test='userId != null and userId!=""'>
            and user_id = #{userId}
        </if>
        <if test='pageFlag != null and pageFlag!=""'>
            and (page_flag = #{pageFlag} or page_flag = concat('ALL_',#{pageFlag}::text))
        </if>
        <if test='nextGroupLevel =="LV1"'>
            and lv1_prod_rnd_team_code is not null
        </if>
        <if test='nextGroupLevel =="LV2"'>
            and lv2_prod_rnd_team_code is not null
        </if>
        <if test='nextGroupLevel =="LV3"'>
            and lv3_prod_rnd_team_code is not null
        </if>
        <if test='nextGroupLevel =="LV4"'>
            and lv4_prod_rnd_team_code is not null
        </if>
        <if test='nextGroupLevel =="L1"'>
            and l1_name is not null
        </if>
        <if test='nextGroupLevel =="L2"'>
            and l2_name is not null
        </if>
        <if test='nextGroupLevel =="CEG"'>
            and l3_ceg_code is not null
        </if>
        <if test='nextGroupLevel =="MODL"'>
            and l4_ceg_code is not null
        </if>
        <if test='nextGroupLevel =="COA"'>
            and coa_code is not null
        </if>
        <if test='nextGroupLevel =="DIMENSION"'>
            and dimension_code is not null
        </if>
        <if test='nextGroupLevel =="CATEGORY"'>
            and category_code is not null
        </if>
        <if test='nextGroupLevel =="SUBCATEGORY"'>
            and dimension_subcategory_code is not null
        </if>
        <if test='nextGroupLevel =="SUB_DETAIL"'>
            and dimension_sub_detail_code is not null
        </if>
        <if test='nextGroupLevel =="SPART"'>
            and spart_code is not null
        </if>
        <if test='nextGroupLevel =="ITEM"'>
            and group_level = #{nextGroupLevel}
        </if>
        <if test='parentCustomIdList != null and parentCustomIdList.size() > 0'>
            <foreach collection='parentCustomIdList' item="code" open="AND custom_id IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
    </select>

    <select id="getCombinationParent" resultMap="resultMap">
        with combOrder as (select custom_id,groupCnName,enable_flag,min(levelOrder)as levelOrder  from (
        SELECT distinct custom_id,custom_cn_name AS groupCnName,enable_flag,
        CASE group_level
        WHEN 'LV1' THEN 1
        WHEN 'LV2' THEN 2
        WHEN 'LV3' THEN 3
        WHEN 'LV4' THEN 4
        WHEN 'L1' THEN 5
        WHEN 'L2' THEN 6
        WHEN 'COA' THEN 7
        WHEN 'DIMENSION' THEN 8
        WHEN 'SUBCATEGORY' THEN 9
        WHEN 'SUB_DETAIL' THEN 10
        WHEN 'SPART' THEN 11
        WHEN 'CEG' THEN 12
        WHEN 'MODL' THEN 13
        WHEN 'CATEGORY' THEN 14
        ELSE 15
        END AS levelOrder
        from fin_dm_opt_foi.${tablePreFix}_custom_comb_d
        where del_flag ='N'
        <if test='lv0ProdListCode != null and lv0ProdListCode!=""'>
            and lv0_prod_list_code = #{lv0ProdListCode}
        </if>
        <if test='viewFlag != null and viewFlag!=""'>
            and view_flag = #{viewFlag}
        </if>
        <if test='caliberFlag != null and caliberFlag!=""'>
            and caliber_flag = #{caliberFlag}
        </if>
        <if test='granularityType != null and granularityType!=""'>
            and granularity_type = #{granularityType}
        </if>
        <if test='overseaFlag != null and overseaFlag!=""'>
            and oversea_flag = #{overseaFlag}
        </if>
        <if test='roleId != null and roleId!=""'>
            and role_id = #{roleId}
        </if>
        <if test='userId != null and userId!=""'>
            and user_id = #{userId}
        </if>
        <if test='pageFlag != null and pageFlag!=""'>
            and (page_flag = #{pageFlag} or page_flag = concat('ALL_',#{pageFlag}::text))
        </if>
        <if test='enableFlag != null and enableFlag!=""'>
            and enable_flag = #{enableFlag}
        </if>
        <if test='keyWord != null and keyWord!=""'>
            and groupCnName LIKE CONCAT(CONCAT('%', #{keyWord}::text ,'%'))
        </if>
         )
        group by custom_id,groupCnName,enable_flag
        )
        select d1.* from (
        SELECT DISTINCT
        custom_id,
        custom_cn_name AS groupCnName,enable_flag,
        group_level,
        CASE group_level
        WHEN 'LV1' THEN 1
        WHEN 'LV2' THEN 2
        WHEN 'LV3' THEN 3
        WHEN 'LV4' THEN 4
        WHEN 'L1' THEN 5
        WHEN 'L2' THEN 6
        WHEN 'COA' THEN 7
        WHEN 'DIMENSION' THEN 8
        WHEN 'SUBCATEGORY' THEN 9
        WHEN 'SUB_DETAIL' THEN 10
        WHEN 'SPART' THEN 11
        WHEN 'CEG' THEN 12
        WHEN 'MODL' THEN 13
        WHEN 'CATEGORY' THEN 14
        ELSE 15
        END AS levelOrder
        FROM
        fin_dm_opt_foi.${tablePreFix}_custom_comb_d
        WHERE
        del_flag = 'N'
        <if test='lv0ProdListCode != null and lv0ProdListCode!=""'>
            and lv0_prod_list_code = #{lv0ProdListCode}
        </if>
        <if test='viewFlag != null and viewFlag!=""'>
            and view_flag = #{viewFlag}
        </if>
        <if test='caliberFlag != null and caliberFlag!=""'>
            and caliber_flag = #{caliberFlag}
        </if>
        <if test='granularityType != null and granularityType!=""'>
            and granularity_type = #{granularityType}
        </if>
        <if test='overseaFlag != null and overseaFlag!=""'>
            and oversea_flag = #{overseaFlag}
        </if>
        <if test='roleId != null and roleId!=""'>
            and role_id = #{roleId}
        </if>
        <if test='userId != null and userId!=""'>
            and user_id = #{userId}
        </if>
        <if test='pageFlag != null and pageFlag!=""'>
            and (page_flag = #{pageFlag} or page_flag = concat('ALL_',#{pageFlag}::text))
        </if>
        <if test='enableFlag != null and enableFlag!=""'>
            and enable_flag = #{enableFlag}
        </if>
        <if test='keyWord != null and keyWord!=""'>
            and groupCnName LIKE CONCAT(CONCAT('%', #{keyWord}::text ,'%'))
        </if>
        ) d1 inner join combOrder d2  on d1.custom_id = d2.custom_id  and d1.groupCnName = d2.groupCnName and d1.levelOrder = d2.levelOrder
        <if test='nextGroupLevel != null and nextGroupLevel!=""'>
            where d1.group_level = #{nextGroupLevel}
        </if>
    </select>

    <select id="getAllCombItemCode" resultMap="resultMap">
        select item_code,count(1) itemNum from (
        select group_code as item_code
        from fin_dm_opt_foi.${tablePreFix}_custom_annual_amp_t
        where del_flag = 'N'
        and group_level = 'ITEM'
        <if test='granularityType != null and granularityType != ""'>
            and granularity_type = #{granularityType,jdbcType=VARCHAR}
        </if>
        <if test='caliberFlag != null and caliberFlag != ""'>
            and caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag != null and overseaFlag != ""'>
            and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='lv0ProdListCode != null and lv0ProdListCode != ""'>
            and lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='parentLevel != null and parentLevel != ""'>
            and parent_level = #{parentLevel,jdbcType=VARCHAR}
        </if>
        <if test='periodYear != null and periodYear != ""'>
            and period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='customIdList != null and customIdList.size() > 0'>
            <foreach collection='customIdList' item="code" open="AND custom_id IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='combinaCodeList != null and combinaCodeList.size() > 0'>
            <foreach collection='combinaCodeList' item="code" open="AND custom_id || '_##' || parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>)
        group by item_code
        order by itemNum desc,item_code
    </select>

    <select id="getAllNormalItemCode" resultMap="resultMap">
        select item_code,count(1) itemNum from (
        SELECT item_code
        <choose>
            <when test='granularityType == "U"'>
                from fin_dm_opt_foi.${tablePreFix}_view_info_d
                where view_flag = #{viewFlag,jdbcType=VARCHAR} and del_flag ='N' and caliber_flag =
                #{caliberFlag,jdbcType=VARCHAR}
                and oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and lv0_prod_list_code =
                #{lv0ProdListCode,jdbcType=VARCHAR}
                and group_level = 'ITEM'
                <choose>
                    <when test='parentLevel =="LV1"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND lv1_prod_rnd_team_code IN ("
                                     close=")" index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="LV2"'>
                        <if test='groupCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND lv2_prod_rnd_team_code IN ("
                                     close=")" index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="LV3"'>
                        <if test='groupCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND lv3_prod_rnd_team_code IN ("
                                     close=")" index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="LV4"'>
                        <if test='groupCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND lv4_prod_rnd_team_code IN ("
                                     close=")" index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="CEG"'>
                        <if test='groupCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND l3_ceg_code IN (" close=")"
                                     index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="MODL"'>
                        <if test='groupCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND l4_ceg_code IN (" close=")"
                                     index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="CATEGORY"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND category_code IN (" close=")"
                                     index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                </choose>
            </when>
            <when test='granularityType == "P"'>
                from fin_dm_opt_foi.${tablePreFix}_pft_view_info_d
                where view_flag = #{viewFlag,jdbcType=VARCHAR} and del_flag ='N' and caliber_flag =
                #{caliberFlag,jdbcType=VARCHAR}
                and oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and lv0_prod_list_code =
                #{lv0ProdListCode,jdbcType=VARCHAR}
                and group_level = 'ITEM'
                <choose>
                    <when test='parentLevel =="LV1"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND lv1_prod_rnd_team_code IN ("
                                     close=")" index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="LV2"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND lv2_prod_rnd_team_code IN ("
                                     close=")" index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="L1"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND l1_name IN (" close=")"
                                     index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="L2"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND l2_name IN (" close=")"
                                     index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="CEG"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND l3_ceg_code IN (" close=")"
                                     index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="MODL"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND l4_ceg_code IN (" close=")"
                                     index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="CATEGORY"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND category_code IN (" close=")"
                                     index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                </choose>
            </when>
            <when test='granularityType == "D"'>
                from fin_dm_opt_foi.${tablePreFix}_dms_view_info_d
                where view_flag = #{viewFlag,jdbcType=VARCHAR} and del_flag ='N' and caliber_flag =
                #{caliberFlag,jdbcType=VARCHAR}
                and oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and lv0_prod_list_code =
                #{lv0ProdListCode,jdbcType=VARCHAR}
                and group_level = 'ITEM'
                <choose>
                    <when test='parentLevel =="LV1"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND lv1_prod_rnd_team_code IN ("
                                     close=")" index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="LV2"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND lv2_prod_rnd_team_code IN ("
                                     close=")" index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="LV3"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND lv3_prod_rnd_team_code IN ("
                                     close=")" index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="LV4"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND lv4_prod_rnd_team_code IN ("
                                     close=")" index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="COA"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND coa_code IN (" close=")"
                                     index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="DIMENSION"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND dimension_code IN (" close=")"
                                     index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="SUBCATEGORY"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND dimension_subcategory_code IN ("
                                     close=")" index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="SUB_DETAIL"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND dimension_sub_detail_code IN ("
                                     close=")" index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="SPART"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND spart_code IN ("
                                     close=")" index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="CEG"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND l3_ceg_code IN (" close=")"
                                     index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="MODL"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND l4_ceg_code IN (" close=")"
                                     index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="CATEGORY"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND category_code IN (" close=")"
                                     index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                </choose>
            </when>
        </choose>
        <if test='teamLevel =="LV1" and teamCodeList != null and teamCodeList.size() > 0'>
            <foreach collection='teamCodeList' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamLevel =="LV2" and teamCodeList != null and teamCodeList.size() > 0'>
            <foreach collection='teamCodeList' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamLevel =="LV3" and teamCodeList != null and teamCodeList.size() > 0'>
            <foreach collection='teamCodeList' item="code" open="AND lv3_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamLevel =="LV4" and teamCodeList != null and teamCodeList.size() > 0'>
            <foreach collection='teamCodeList' item="code" open="AND lv4_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
      )
        group by item_code
        order by itemNum desc,item_code
    </select>

    <select id="getAllItemCode" resultMap="resultMap">
        select item_code,count(1) itemNum from (
        select group_code as item_code
        from fin_dm_opt_foi.${tablePreFix}_custom_annual_amp_t
        where del_flag = 'N'
        and group_level = 'ITEM'
        <if test='granularityType != null and granularityType != ""'>
            and granularity_type = #{granularityType,jdbcType=VARCHAR}
        </if>
        <if test='caliberFlag != null and caliberFlag != ""'>
            and caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag != null and overseaFlag != ""'>
            and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='lv0ProdListCode != null and lv0ProdListCode != ""'>
            and lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='parentLevel != null and parentLevel != ""'>
            and parent_level = #{parentLevel,jdbcType=VARCHAR}
        </if>
        <if test='periodYear != null and periodYear != ""'>
            and period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='customIdList != null and customIdList.size() > 0'>
            <foreach collection='customIdList' item="code" open="AND custom_id IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='combinaCodeList != null and combinaCodeList.size() > 0'>
            <foreach collection='combinaCodeList' item="code" open="AND custom_id || '_##' || parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        union all
        SELECT item_code
        <choose>
            <when test='granularityType == "U"'>
                from fin_dm_opt_foi.${tablePreFix}_view_info_d
                where view_flag = #{viewFlag,jdbcType=VARCHAR} and del_flag ='N' and caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and group_level = 'ITEM'
                <choose>
                    <when test='parentLevel =="LV1"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND lv1_prod_rnd_team_code IN ("
                                     close=")" index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="LV2"'>
                        <if test='groupCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND lv2_prod_rnd_team_code IN ("
                                     close=")" index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="LV3"'>
                        <if test='groupCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND lv3_prod_rnd_team_code IN ("
                                     close=")" index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="LV4"'>
                        <if test='groupCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND lv4_prod_rnd_team_code IN ("
                                     close=")" index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="CEG"'>
                        <if test='groupCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND l3_ceg_code IN (" close=")"
                                     index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="MODL"'>
                        <if test='groupCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND l4_ceg_code IN (" close=")"
                                     index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="CATEGORY"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND category_code IN (" close=")"
                                     index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                </choose>
            </when>
            <when test='granularityType == "P"'>
                from fin_dm_opt_foi.${tablePreFix}_pft_view_info_d
                where view_flag = #{viewFlag,jdbcType=VARCHAR} and del_flag ='N' and caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and group_level = 'ITEM'
                <choose>
                    <when test='parentLevel =="LV1"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND lv1_prod_rnd_team_code IN ("
                                     close=")" index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="LV2"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND lv2_prod_rnd_team_code IN ("
                                     close=")" index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="L1"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND l1_name IN (" close=")"
                                     index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="L2"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND l2_name IN (" close=")"
                                     index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="CEG"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND l3_ceg_code IN (" close=")"
                                     index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="MODL"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND l4_ceg_code IN (" close=")"
                                     index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="CATEGORY"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND category_code IN (" close=")"
                                     index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                </choose>
            </when>
            <when test='granularityType == "D"'>
                from fin_dm_opt_foi.${tablePreFix}_dms_view_info_d
                where view_flag = #{viewFlag,jdbcType=VARCHAR} and del_flag ='N' and caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                and oversea_flag = #{overseaFlag,jdbcType=VARCHAR} and lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                and group_level = 'ITEM'
                <choose>
                    <when test='parentLevel =="LV1"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND lv1_prod_rnd_team_code IN ("
                                     close=")" index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="LV2"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND lv2_prod_rnd_team_code IN ("
                                     close=")" index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="LV3"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND lv3_prod_rnd_team_code IN ("
                                     close=")" index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="LV4"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND lv4_prod_rnd_team_code IN ("
                                     close=")" index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="COA"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND coa_code IN (" close=")"
                                     index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="DIMENSION"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND dimension_code IN (" close=")"
                                     index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="SUBCATEGORY"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND dimension_subcategory_code IN ("
                                     close=")" index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="SUB_DETAIL"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND dimension_sub_detail_code IN ("
                                     close=")" index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="SPART"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND spart_code IN ("
                                     close=")" index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="CEG"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND l3_ceg_code IN (" close=")"
                                     index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="MODL"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND l4_ceg_code IN (" close=")"
                                     index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                    <when test='parentLevel =="CATEGORY"'>
                        <if test='parentCodeList != null and parentCodeList.size() > 0'>
                            <foreach collection='parentCodeList' item="code" open="AND category_code IN (" close=")"
                                     index="index"
                                     separator=",">
                                #{code}
                            </foreach>
                        </if>
                    </when>
                </choose>
            </when>
        </choose>
        <if test='teamLevel =="LV1" and teamCodeList != null and teamCodeList.size() > 0'>
            <foreach collection='teamCodeList' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamLevel =="LV2" and teamCodeList != null and teamCodeList.size() > 0'>
            <foreach collection='teamCodeList' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamLevel =="LV3" and teamCodeList != null and teamCodeList.size() > 0'>
            <foreach collection='teamCodeList' item="code" open="AND lv3_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamLevel =="LV4" and teamCodeList != null and teamCodeList.size() > 0'>
            <foreach collection='teamCodeList' item="code" open="AND lv4_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
       )
        group by item_code
        order by itemNum desc,item_code
    </select>

</mapper>