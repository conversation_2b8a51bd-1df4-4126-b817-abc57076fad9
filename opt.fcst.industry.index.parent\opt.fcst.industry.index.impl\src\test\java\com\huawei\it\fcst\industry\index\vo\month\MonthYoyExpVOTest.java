/*
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.month;


import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @since 2023/5/9
 */
public class MonthYoyExpVOTest extends BaseVOCoverUtilsTest<MonthYoyExpVO> {
    @Override
    protected Class<MonthYoyExpVO> getTClass() { return MonthYoyExpVO.class; }

    @Test
    public void testMethod() {
        MonthYoyExpVO dmFocActualCostVO = new MonthYoyExpVO();
        dmFocActualCostVO.setPeriodId(2023L);
        dmFocActualCostVO.getPeriodId();
        dmFocActualCostVO.setPopPercent("POP");
        dmFocActualCostVO.getPopPercent();
        dmFocActualCostVO.setYoyPercent("0.223");
        dmFocActualCostVO.getYoyPercent();
        dmFocActualCostVO.getCostType();
        dmFocActualCostVO.setCostType("11");
        dmFocActualCostVO.getGroupCnName();
        dmFocActualCostVO.setGroupCnName("22");
        MonthYoyExpVO.builder().periodId(2011L)
            .costType("11").groupCnName("44").build();
        Assert.assertNotNull(dmFocActualCostVO);
    }
}