/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.annual;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.huawei.it.fcst.industry.index.config.ExecutorConfig;
import com.huawei.it.fcst.industry.index.constant.CommonConstant;
import com.huawei.it.fcst.industry.index.constant.Constant;
import com.huawei.it.fcst.industry.index.dao.IAnnualAmpDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeViewInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocVersionDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocViewInfoDao;
import com.huawei.it.fcst.industry.index.dao.IMadeAnnualAmpDao;
import com.huawei.it.fcst.industry.index.dao.ITotalAnnualAmpDao;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumD;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumMadeD;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumMadeU;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumP;
import com.huawei.it.fcst.industry.index.enums.GroupLevelEnumU;
import com.huawei.it.fcst.industry.index.enums.IndustryConst;
import com.huawei.it.fcst.industry.index.enums.IndustryIndexEnum;
import com.huawei.it.fcst.industry.index.enums.ModuleEnum;
import com.huawei.it.fcst.industry.index.enums.ResultCodeEnum;
import com.huawei.it.fcst.industry.index.impl.common.CommonService;
import com.huawei.it.fcst.industry.index.impl.month.AsyncQueryService;
import com.huawei.it.fcst.industry.index.impl.replace.CommonAmpService;
import com.huawei.it.fcst.industry.index.service.annual.IAnnualAmpService;
import com.huawei.it.fcst.industry.index.utils.ExcelUtil;
import com.huawei.it.fcst.industry.index.utils.FcstIndexMadeUtil;
import com.huawei.it.fcst.industry.index.utils.FcstIndexUtil;
import com.huawei.it.fcst.industry.index.vo.annual.AnnualAnalysisVO;
import com.huawei.it.fcst.industry.index.vo.annual.AnnualParamVO;
import com.huawei.it.fcst.industry.index.vo.annual.DmFocAnnualAmpVO;
import com.huawei.it.fcst.industry.index.vo.common.AbstractExcelTitleVO;
import com.huawei.it.fcst.industry.index.vo.common.CommonViewVO;
import com.huawei.it.fcst.industry.index.vo.common.DataPermissionsVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFoiImpExpRecordVO;
import com.huawei.it.fcst.industry.index.vo.common.ExportExcelVo;
import com.huawei.it.fcst.industry.index.vo.common.HeaderVo;
import com.huawei.it.fcst.industry.index.vo.common.LeafExcelTitleVO;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoDTO;
import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.core.util.CollectionUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellType;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Named;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * AnnualAmpService Class
 *
 * <AUTHOR>
 * @since 2023/3/13
 */
@Named("annualAmpService")
@JalorResource(code = "annualAmpService", desc = "年度分析页面")
public class AnnualAmpService implements IAnnualAmpService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AnnualAmpService.class);

    @Autowired
    private IAnnualAmpDao annualAmpDao;

    @Autowired
    private IMadeAnnualAmpDao madeAnnualAmpDao;

    @Autowired
    private ITotalAnnualAmpDao totalAnnualAmpDao;

    @Autowired
    private IDmFocVersionDao dmFocVersionDao;

    @Autowired
    private ExcelUtil excelUtil;

    @Autowired
    private StatisticsExcelService statisticsExcelService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private IDmFocMonthCostIdxDao iDmFocMonthCostIdxDao;

    @Autowired
    private IDmFocViewInfoDao dmFocViewInfoDao;

    @Autowired
    private IDmFocMadeViewInfoDao dmFocMadeViewInfoDao;

    @Autowired
    private AnnualCommonService annualCommonService;

    @Autowired
    private AsyncQueryService asyncQueryService;

    @Autowired
    private ExecutorConfig executorConfig;

    @Autowired
    private CommonAmpService commonAmpService;

    @JalorOperation(code = "allIndustryCost", desc = "查询当前层级产业成本涨跌幅")
    @Override
    public ResultDataVO allIndustryCost(AnnualAnalysisVO annualAnalysisVO) {
        if (!annualAnalysisVO.getIsMultipleSelect()) {
            // 计算最近的三年
            List<String> threeYears = annualCommonService.getThreeYears(annualAnalysisVO.getCostType(), annualAnalysisVO.getIndustryOrg());
            annualAnalysisVO.setYearList(threeYears);
        }
        // 获取最新的version_id
        annualAnalysisVO.setVersionId(dmFocVersionDao.findAnnualVersion(annualAnalysisVO.getTablePreFix()).getVersionId());
        // 判断是否反向视角并赋值
        isReverseViewFlag(annualAnalysisVO);
        List<DmFocAnnualAmpVO> dmFocAnnualVOList = new ArrayList<>();
        IndustryIndexEnum.COST_TYPE costType = IndustryIndexEnum.getCostType(annualAnalysisVO.getCostType());
        switch (costType) {
            case T:
                asyncQueryService.findTotalAllIndustryCost(dmFocAnnualVOList, annualAnalysisVO);
                break;
            case P:
            case M:
                if (annualAnalysisVO.getIsContainComb()) {
                    // 包含汇总组合
                    dmFocAnnualVOList = currentLevelCustomCombCondition(annualAnalysisVO);
                } else {
                    dmFocAnnualVOList = currentLevelNotContainComb(annualAnalysisVO);
                }
                break;
            default:
                break;
        }
        if (CollectionUtils.isNotEmpty(dmFocAnnualVOList)) {
            dmFocAnnualVOList.stream().forEach(annualAmpVO -> {
                if (!annualAmpVO.getGroupCode().equals(String.valueOf(annualAmpVO.getCustomId())) && (CommonConstant.GROUP_LEVEL_DIMENSION.contains(annualAmpVO.getGroupLevel()))) {
                    annualAmpVO.setGroupCnName(annualAmpVO.getGroupCode() + " " + annualAmpVO.getGroupCnName());
                }
            });
        }
        Map result = getResult(annualAnalysisVO, dmFocAnnualVOList);
        return ResultDataVO.success(result);
    }

    private void setGroupCnNameDimensionLevel(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList) {
        if (CollectionUtils.isNotEmpty(dmFocAnnualAmpVOList)) {
            dmFocAnnualAmpVOList.stream().forEach(annualAmpVO -> {
                if (CommonConstant.GROUP_LEVEL_DIMENSION.contains(annualAmpVO.getGroupLevel())) {
                    annualAmpVO.setGroupCnName(annualAmpVO.getGroupCode() + " " + annualAmpVO.getGroupCnName());
                }
            });
        }
    }

    private void setParentCnNameDimensionLevel(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList) {
        if (CollectionUtils.isNotEmpty(dmFocAnnualAmpVOList)) {
            dmFocAnnualAmpVOList.stream().forEach(annualAmpVO -> {
                if (CommonConstant.GROUP_LEVEL_DIMENSION.contains(annualAmpVO.getParentLevel())) {
                    annualAmpVO.setParentCnName(annualAmpVO.getParentCode() + " " + annualAmpVO.getParentCnName());
                }
            });
        }
    }

    @NotNull
    private Map getResult(AnnualAnalysisVO annualAnalysisVO, List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList) {
        Map result = new LinkedHashMap();
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(annualAnalysisVO.getCostType())) {
            // 设置无效的涨跌幅提示语
            setNoEffectiveAmp(dmFocAnnualAmpVOList, annualAnalysisVO, "data");
            result.put("result", dmFocAnnualAmpVOList);
        } else {
            // 分页处理
            PageVO pageVO = new PageVO();
            pageVO.setPageSize(annualAnalysisVO.getPageSize());
            pageVO.setCurPage(annualAnalysisVO.getPageIndex());
            int count = dmFocAnnualAmpVOList.size();
            pageVO.setTotalRows(count);
            int curPage = pageVO.getCurPage() - 1;
            int pageSize = pageVO.getPageSize();
            List<DmFocAnnualAmpVO> annualAmpAndWeightList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(dmFocAnnualAmpVOList) && count > 0) {
                int fromIndex = curPage * pageSize;
                int totalIndex = (curPage + 1) * pageSize;
                if (totalIndex > count) {
                    totalIndex = count;
                }
                annualAmpAndWeightList = dmFocAnnualAmpVOList.subList(fromIndex, totalIndex);
            }
            // 设置无效的涨跌幅提示语
            setNoEffectiveAmp(annualAmpAndWeightList, annualAnalysisVO, "data");
            result.put("result", annualAmpAndWeightList);
            result.put("pageVo", pageVO);
        }
        return result;
    }

    @JalorOperation(code = "multiIndustryCostChart", desc = "查询当前层级多子项产业成本涨跌幅")
    @Override
    public ResultDataVO multiIndustryCostChart(AnnualAnalysisVO annualAnalysisVO) {
        if (!annualAnalysisVO.getIsContainComb()) {
            if (CollectionUtils.isEmpty(annualAnalysisVO.getSubGroupCodeList()) || StringUtils.isEmpty(annualAnalysisVO.getViewFlag())) {
                return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
            }
        }
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(annualAnalysisVO.getPageSize());
        pageVO.setCurPage(annualAnalysisVO.getPageIndex());
        setMutliSearchParamsVO(annualAnalysisVO);
        // 采购层级排序多选时先要排序父层级
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = new ArrayList<>();
        String groupLevel = annualAnalysisVO.getGroupLevel();
        IndustryIndexEnum.COST_TYPE costType = IndustryIndexEnum.getCostType(annualAnalysisVO.getCostType());
        switch (costType) {
            case T:
                dmFocAnnualAmpVOResult = multiChildNotContainsCustomComb(annualAnalysisVO, pageVO);
                break;
            case P:
            case M:
                if (!annualAnalysisVO.getIsContainComb()) {
                    // 不包含汇总组合
                    dmFocAnnualAmpVOResult = multiChildNotContainsCustomComb(annualAnalysisVO, pageVO);
                } else {
                    // 包含汇总组合的情况
                    dmFocAnnualAmpVOResult = multiChildContainsCustomComb(annualAnalysisVO, pageVO);
                    groupLevel = GroupLevelEnumU.ITEM.getValue();
                    annualAnalysisVO.setGroupLevel(groupLevel);
                }
                break;
            default:
                break;
        }
        // 设置无效的涨跌幅提示语
        setNoEffectiveAmp(dmFocAnnualAmpVOResult, annualAnalysisVO, "data");
        // 如果是item层级，则需要屏蔽权重
        if (GroupLevelEnumU.ITEM.getValue().equals(groupLevel) || GroupLevelEnumD.SPART.getValue().equals(groupLevel)) {
            dmFocAnnualAmpVOResult.stream().forEach(result -> result.setWeightRate(null));
        }
        Map result = new LinkedHashMap();
        result.put("result", dmFocAnnualAmpVOResult);
        result.put("pageVo", pageVO);
        return ResultDataVO.success(result);
    }

    private void setMutliSearchParamsVO(AnnualAnalysisVO annualAnalysisVO) {
        // 判断是否反向视角并赋值
        isReverseViewFlag(annualAnalysisVO);
        // 计算最近的三年
        List<String> threeYears = annualCommonService.getThreeYears(annualAnalysisVO.getCostType(), annualAnalysisVO.getIndustryOrg());
        // 获取最新的version_id
        annualAnalysisVO.setVersionId(dmFocVersionDao.findAnnualVersion(annualAnalysisVO.getTablePreFix()).getVersionId());
        // 获取当前年份2023YTD
        if (CollectionUtils.isNotEmpty(threeYears)) {
            int countYear = threeYears.size() - 1;
            annualAnalysisVO.setYear(threeYears.get(countYear));
        }
        if (!annualAnalysisVO.getIsMultipleSelect()) {
            annualAnalysisVO.setYearList(threeYears);
        }
        annualAnalysisVO.setParentLevel(annualAnalysisVO.getGroupLevel());
        // 根据groupLevel计算子项level
        String groupLevel;
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
            groupLevel = FcstIndexMadeUtil.getNextGroupLevelByView(annualAnalysisVO.getViewFlag(),
                    annualAnalysisVO.getParentLevel(), annualAnalysisVO.getGranularityType(), annualAnalysisVO.getIndustryOrg());
        } else {
            groupLevel = FcstIndexUtil.getNextGroupLevelByView(annualAnalysisVO.getViewFlag(),
                    annualAnalysisVO.getParentLevel(), annualAnalysisVO.getGranularityType(),annualAnalysisVO.getIndustryOrg());
        }
        // 多选查询时先按照父层级多选项排序
        multiSelectSetParentCode(annualAnalysisVO, groupLevel);
        annualAnalysisVO.setParentCodeList(annualAnalysisVO.getGroupCodeList());
        annualAnalysisVO.setGroupLevel(groupLevel);
    }

    private void multiSelectSetParentCode(AnnualAnalysisVO annualAnalysisVO, String groupLevel) {
        if (!annualAnalysisVO.getIsContainComb()) {
            if (annualAnalysisVO.getIsMultipleSelect() && (CommonConstant.GROUP_LEVEL_P.contains(groupLevel) || CommonConstant.GROUP_LEVEL_M.contains(groupLevel))) {
                purchaseLevelOrderByWithParent(annualAnalysisVO);
            }
            List<DmFocAnnualAmpVO> revParentCodeOrderList = new ArrayList<>();
            if (annualAnalysisVO.getIsMultipleSelect() && annualAnalysisVO.getReverseSymbol() && CommonConstant.GROUP_LEVEL_PROD.contains(groupLevel)) {
                // 反向多选时父层级是采购层级先排序
                if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType())) {
                    revParentCodeOrderList = annualAmpDao.findRevGroupCodeOrderByWeight(annualAnalysisVO);
                }
                // 反向多选时父层级是制造层级先排序
                if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
                    revParentCodeOrderList = madeAnnualAmpDao.findMadeRevGroupCodeOrderByWeight(annualAnalysisVO);
                }
            }
            annualAnalysisVO.setParentCodeOrder(revParentCodeOrderList.stream().map(DmFocAnnualAmpVO::getGroupCode).collect(Collectors.joining(",")));
        }
    }

    @JalorOperation(code = "industryCostList", desc = "查询多项产业成本一览表")
    @Override
    public ResultDataVO industryCostList(AnnualAnalysisVO annualAnalysisVO) {
        if (!annualAnalysisVO.getIsContainComb()) {
            if (CollectionUtils.isEmpty(annualAnalysisVO.getSubGroupCodeList()) || StringUtils.isEmpty(annualAnalysisVO.getViewFlag())) {
                return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
            }
        }
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(annualAnalysisVO.getPageSize());
        pageVO.setCurPage(annualAnalysisVO.getPageIndex());
        setIndustrySearchParamVO(annualAnalysisVO);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOOrderByWeight = new ArrayList<>();
        // 包含汇总组合的查询
        if (annualAnalysisVO.getIsContainComb()) {
            dmFocAnnualAmpVOOrderByWeight = getCombAnnualAmpOrderByWeight(annualAnalysisVO, pageVO, dmFocAnnualAmpVOOrderByWeight);
        } else {
            dmFocAnnualAmpVOOrderByWeight = getAnnualAmpOrderByWeight(annualAnalysisVO, dmFocAnnualAmpVOOrderByWeight);
        }
        // item层级按照权重*涨跌大小排序；各层级权重排序;获取权重*涨跌最大值
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = dealGroupLevelWeightAndAmp(dmFocAnnualAmpVOOrderByWeight, annualAnalysisVO, true, pageVO);
        // 设置无效的涨跌幅提示语
        setNoEffectiveAmp(dmFocAnnualAmpVOResult, annualAnalysisVO, "data");
        // 排序和分页
        List<DmFocAnnualAmpVO> annualAmpAndWeightList = orderColumnAndLimitPage(dmFocAnnualAmpVOResult, annualAnalysisVO, pageVO);
        Map result = new LinkedHashMap();
        if (GroupLevelEnumU.ITEM.getValue().equals(annualAnalysisVO.getGroupLevel()) || GroupLevelEnumD.SPART.getValue().equals(annualAnalysisVO.getGroupLevel())) {
            setWeightAnnualAmpPercent(annualAnalysisVO, annualAmpAndWeightList);
        } else {
            result.put("maxValue", annualAnalysisVO.getMaxValue() + "%");
        }
        result.put("result", annualAmpAndWeightList);
        result.put("pageVo", pageVO);
        return ResultDataVO.success(result);
    }

    private void setWeightAnnualAmpPercent(AnnualAnalysisVO annualAnalysisVO, List<DmFocAnnualAmpVO> annualAmpAndWeightList) {
        for (DmFocAnnualAmpVO dmFocAnnualAmpVO : annualAmpAndWeightList) {
            if (StringUtils.isNotBlank(dmFocAnnualAmpVO.getWeightAnnualAmpPercent()) && StringUtils.isNotBlank(annualAnalysisVO.getMaxValue())) {
                if ("0".equals(annualAnalysisVO.getMaxValue()) || "0.0".equals(annualAnalysisVO.getMaxValue())) {
                    dmFocAnnualAmpVO.setWeightAnnualAmpPercent("0*");
                    continue;
                }
                if ("0*".equals(dmFocAnnualAmpVO.getWeightAnnualAmpPercent())) {
                    continue;
                }
                double double1 = Double.parseDouble(dmFocAnnualAmpVO.getWeightAnnualAmpPercent());
                double double2 = Double.parseDouble(annualAnalysisVO.getMaxValue());
                double weightPercent = double1 / double2;
                double targetValue = weightPercent * 0.9;
                if (Math.abs(targetValue) * 1000 > 0 && Math.abs(targetValue) * 1000 <= 2) {
                    targetValue = targetValue > 0 ? 0.002 : -0.002;
                }
                dmFocAnnualAmpVO.setWeightAnnualAmpPercent(Double.toString(targetValue * 60));
            }
        }
    }

    private List<DmFocAnnualAmpVO> getCombAnnualAmpOrderByWeight(AnnualAnalysisVO annualAnalysisVO, PageVO pageVO, List<DmFocAnnualAmpVO> dmFocAnnualAmpVOOrderByWeight) {
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType())) {
            dmFocAnnualAmpVOOrderByWeight = overviewListContainsComb(annualAnalysisVO, pageVO);
        }
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
            dmFocAnnualAmpVOOrderByWeight = overviewMadeListContainsComb(annualAnalysisVO, pageVO);
        }
        annualAnalysisVO.setGroupLevel(GroupLevelEnumU.ITEM.getValue());
        return dmFocAnnualAmpVOOrderByWeight;
    }

    private List<DmFocAnnualAmpVO> getAnnualAmpOrderByWeight(AnnualAnalysisVO annualAnalysisVO, List<DmFocAnnualAmpVO> dmFocAnnualAmpVOOrderByWeight) {
        // 默认按照权重大小排序
        if (!annualAnalysisVO.getReverseSymbol()) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType())) {
                dmFocAnnualAmpVOOrderByWeight = annualAmpDao.industryCostList(annualAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
                dmFocAnnualAmpVOOrderByWeight = madeAnnualAmpDao.madeIndustryCostList(annualAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(annualAnalysisVO.getCostType())) {
                dmFocAnnualAmpVOOrderByWeight = getTotalIndustryCostList(annualAnalysisVO, dmFocAnnualAmpVOOrderByWeight);
            }
        } else {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType())) {
                dmFocAnnualAmpVOOrderByWeight = annualAmpDao.industryRevCostList(annualAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
                dmFocAnnualAmpVOOrderByWeight = madeAnnualAmpDao.madeIndustryRevCostList(annualAnalysisVO);
            }
        }
        setGroupCnNameDimensionLevel(dmFocAnnualAmpVOOrderByWeight);
        return dmFocAnnualAmpVOOrderByWeight;
    }

    private List<DmFocAnnualAmpVO> getAnnualAmpOrderByWeightExcel(AnnualAnalysisVO annualAnalysisVO, List<DmFocAnnualAmpVO> dmFocAnnualAmpVOOrderByWeight) {
        // 默认按照权重大小排序
        if (!annualAnalysisVO.getReverseSymbol()) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType())) {
                dmFocAnnualAmpVOOrderByWeight = annualAmpDao.industryCostList(annualAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
                dmFocAnnualAmpVOOrderByWeight = madeAnnualAmpDao.madeIndustryCostList(annualAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(annualAnalysisVO.getCostType())) {
                dmFocAnnualAmpVOOrderByWeight.addAll(annualAmpDao.industryCostList(annualAnalysisVO));
                dmFocAnnualAmpVOOrderByWeight.addAll(madeAnnualAmpDao.madeIndustryCostList(annualAnalysisVO));
                dmFocAnnualAmpVOOrderByWeight.addAll(totalAnnualAmpDao.totalIndustryCostList(annualAnalysisVO));
            }
            mutilSelectGroupCnName(annualAnalysisVO, dmFocAnnualAmpVOOrderByWeight, "mutil");
        } else {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType())) {
                dmFocAnnualAmpVOOrderByWeight = annualAmpDao.industryRevCostList(annualAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
                dmFocAnnualAmpVOOrderByWeight = madeAnnualAmpDao.madeIndustryRevCostList(annualAnalysisVO);
            }
        }
        setGroupCnNameDimensionLevel(dmFocAnnualAmpVOOrderByWeight);
        return dmFocAnnualAmpVOOrderByWeight;
    }

    private List<DmFocAnnualAmpVO> getTotalIndustryCostList(AnnualAnalysisVO annualAnalysisVO, List<DmFocAnnualAmpVO> dmFocAnnualAmpVOOrderByWeight) {
        IndustryIndexEnum.COST_TYPE costSubType = IndustryIndexEnum.getCostType(annualAnalysisVO.getCostSubType());
        switch (costSubType) {
            case T:
                dmFocAnnualAmpVOOrderByWeight = totalAnnualAmpDao.totalIndustryCostList(annualAnalysisVO);
                break;
            case M:
                dmFocAnnualAmpVOOrderByWeight = madeAnnualAmpDao.madeIndustryCostList(annualAnalysisVO);
                break;
            case P:
                dmFocAnnualAmpVOOrderByWeight = annualAmpDao.industryCostList(annualAnalysisVO);
                break;
            default:
                break;
        }
        return dmFocAnnualAmpVOOrderByWeight;
    }

    @Override
    @JalorOperation(code = "distributeCostChart", desc = "查询当前层级的成本分布图")
    public ResultDataVO distributeCostChart(AnnualAnalysisVO annualAnalysisVO) {
        if (!annualAnalysisVO.getIsMultipleSelect()) {
            // 计算最近的三年
            List<String> threeYears = annualCommonService.getThreeYears(annualAnalysisVO.getCostType(), annualAnalysisVO.getIndustryOrg());
            annualAnalysisVO.setYearList(threeYears);
        }
        // 获取最新的version_id
        annualAnalysisVO.setVersionId(dmFocVersionDao.findAnnualVersion(annualAnalysisVO.getTablePreFix()).getVersionId());
        List<DmFocAnnualAmpVO> dmFocDistributeCostList = totalAnnualAmpDao.distributeCostList(annualAnalysisVO);
        setGroupCnNameDimensionLevel(dmFocDistributeCostList);
        return ResultDataVO.success(dmFocDistributeCostList);
    }

    @Override
    @JalorOperation(code = "getCompareAmpCost", desc = "查询对比分析涨跌图")
    public ResultDataVO getCompareAmpCost(List<AnnualAnalysisVO> annualAnalysisList) throws InterruptedException {

        LOGGER.info(">>>Begin MonthCommonService::getCompareIndexChart");
        IRequestContext requestContext = RequestContext.getCurrent();
        if (!CollectionUtil.isNullOrEmpty(annualAnalysisList) && annualAnalysisList.size() > com.huawei.it.fcst.constant.Constant.IntegerEnum.MAX_SIZE.getValue()) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR, "操作的数据过多！");
        }
        List<DmFocAnnualAmpVO> annualAmpList = new ArrayList();
        // 设置重量级团队level和code
        setCompareTeamCodeLevelParam(annualAnalysisList);
        Executor executorPool = executorConfig.asyncServiceExecutor();
        CountDownLatch countDownLatch = new CountDownLatch(annualAnalysisList.size());
        for (AnnualAnalysisVO annualAnalysisVO : annualAnalysisList) {
            Runnable runnable = () -> {
                RequestContextManager.setCurrent(requestContext);
                try {
                    List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = getIndustryAmpCost(annualAnalysisVO);
                    annualAmpList.addAll(dmFocAnnualAmpVOList);
                } catch (Exception exception) {
                    LOGGER.error("error getCompareAmpCost :{} ", exception.getLocalizedMessage());
                } finally {
                    countDownLatch.countDown();
                    RequestContextManager.removeCurrent();
                    LOGGER.info("剩余线程个数：{},当前线程名称：{}", countDownLatch.getCount(), Thread.currentThread().getName());
                }
            };
            executorPool.execute(runnable);
        }
        countDownLatch.await();
        return ResultDataVO.success(annualAmpList);
    }

    private void setCompareTeamCodeLevelParam(List<AnnualAnalysisVO> annualAnalysisList) {
        // 入参处理重量级团队
        for (AnnualAnalysisVO annualAnalysisVO : annualAnalysisList) {
            setSingleCompareTeamCodeLevel(annualAnalysisVO);
        }
    }

    private void setSingleCompareTeamCodeLevel(AnnualAnalysisVO annualAnalysisVO) {
        // 入参处理重量级团队
        if (!annualAnalysisVO.getIsContainComb()) {
            String prodRndTeamCode = null;
            String teamLevel = null;
            if (StringUtils.isNotBlank(annualAnalysisVO.getLv0ProdRndTeamCode())) {
                prodRndTeamCode = annualAnalysisVO.getLv0ProdRndTeamCode();
                teamLevel = "LV0";
            }
            if (StringUtils.isNotBlank(annualAnalysisVO.getLv1ProdRndTeamCode())) {
                prodRndTeamCode = annualAnalysisVO.getLv1ProdRndTeamCode();
                teamLevel = "LV1";
            }
            if (StringUtils.isNotBlank(annualAnalysisVO.getLv2ProdRndTeamCode())) {
                prodRndTeamCode = annualAnalysisVO.getLv2ProdRndTeamCode();
                teamLevel = "LV2";
            }
            if (StringUtils.isNotBlank(annualAnalysisVO.getLv3ProdRndTeamCode())) {
                prodRndTeamCode = annualAnalysisVO.getLv3ProdRndTeamCode();
                teamLevel = "LV3";
            }
            if (StringUtils.isNotBlank(annualAnalysisVO.getLv4ProdRndTeamCode())) {
                prodRndTeamCode = annualAnalysisVO.getLv4ProdRndTeamCode();
                teamLevel = "LV4";
            }
            List<String> prodRndTeamCodeList = new ArrayList<>();
            prodRndTeamCodeList.add(prodRndTeamCode);
            annualAnalysisVO.setTeamCodeList(prodRndTeamCodeList);
            annualAnalysisVO.setTeamLevel(teamLevel);
        } else {
            annualAnalysisVO.setTeamCodeList(annualAnalysisVO.getGroupCodeList());
        }
    }

    private List<DmFocAnnualAmpVO> getIndustryAmpCost(AnnualAnalysisVO annualAnalysisVO) {
        // 获取最新的version_id
        annualAnalysisVO.setVersionId(dmFocVersionDao.findAnnualVersion(annualAnalysisVO.getTablePreFix()).getVersionId());
        // 判断是否反向视角并赋值
        isReverseViewFlag(annualAnalysisVO);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        IndustryIndexEnum.COST_TYPE costType = IndustryIndexEnum.getCostType(annualAnalysisVO.getCostType());
        switch (costType) {
            case T:
                asyncQueryService.findTotalAllIndustryCost(dmFocAnnualAmpVOList, annualAnalysisVO);
                break;
            case P:
            case M:
                if (annualAnalysisVO.getIsContainComb()) {
                    // 包含汇总组合
                    dmFocAnnualAmpVOList = currentLevelCustomCombCondition(annualAnalysisVO);
                } else {
                    dmFocAnnualAmpVOList = currentLevelNotContainComb(annualAnalysisVO);
                }
                break;
            default:
                break;
        }
        setNoEffectiveAmp(dmFocAnnualAmpVOList, annualAnalysisVO, "data");
        return dmFocAnnualAmpVOList;
    }

    private void setIndustrySearchParamVO(AnnualAnalysisVO annualAnalysisVO) {
        // 判断是否反向视角并赋值
        isReverseViewFlag(annualAnalysisVO);
        annualAnalysisVO.setParentCodeList(annualAnalysisVO.getGroupCodeList());
        annualAnalysisVO.setParentLevel(annualAnalysisVO.getGroupLevel());
        // 根据parentLevel计算子项level
        String groupLevel;
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
            groupLevel = FcstIndexMadeUtil.getNextGroupLevelByView(annualAnalysisVO.getViewFlag(),
                    annualAnalysisVO.getGroupLevel(), annualAnalysisVO.getGranularityType(),annualAnalysisVO.getIndustryOrg());
        } else {
            groupLevel = FcstIndexUtil.getNextGroupLevelByView(annualAnalysisVO.getViewFlag(),
                    annualAnalysisVO.getGroupLevel(), annualAnalysisVO.getGranularityType(),annualAnalysisVO.getIndustryOrg());
        }
        annualAnalysisVO.setGroupLevel(groupLevel);
        // 如果grouplevel为LV0/LV1/LV2，则teamcode置为null
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setViewFlag(annualAnalysisVO.getViewFlag());
        monthAnalysisVO.setParentLevel(annualAnalysisVO.getParentLevel());
        monthAnalysisVO.setGroupLevel(groupLevel);
        monthAnalysisVO.setProdRndTeamCodeList(annualAnalysisVO.getTeamCodeList());
        monthAnalysisVO.setGranularityType(annualAnalysisVO.getGranularityType());
        monthAnalysisVO.setIndustryOrg(annualAnalysisVO.getIndustryOrg());
        commonService.setProdRndTeamCode(monthAnalysisVO);
        annualAnalysisVO.setTeamCodeList(monthAnalysisVO.getProdRndTeamCodeList());
        // 获取最新的version_id
        DmFocVersionInfoDTO annualVersion = dmFocVersionDao.findAnnualVersion(annualAnalysisVO.getTablePreFix());
        annualAnalysisVO.setVersionId(annualVersion.getVersionId());
        if (!annualAnalysisVO.getIsMultipleSelect()) {
            // 计算最近的三年
            annualAnalysisVO.setYearList(annualCommonService.getThreeYears(annualAnalysisVO.getCostType(), annualAnalysisVO.getIndustryOrg()));
        }
    }

    @Audit(module = "annualAmpService-exportDetail", operation = "exportDetail", message = "年度分析数据下载")
    @JalorOperation(code = "exportDetail", desc = "年度分析数据下载")
    @Override
    public ResultDataVO exportDetail(AnnualAnalysisVO annualAnalysisVO, HttpServletResponse response) throws ApplicationException, IOException, InterruptedException {
        LOGGER.info("begin annual excel export");
        // 获取当前用户角色对应的数据范围
        DataPermissionsVO dimensionList = commonService.getDimensionList(annualAnalysisVO.getCostType(), annualAnalysisVO.getTablePreFix(), annualAnalysisVO.getIndustryOrg());
        annualAnalysisVO.setLv1DimensionSet(dimensionList.getLv1DimensionSet());
        annualAnalysisVO.setLv2DimensionSet(dimensionList.getLv2DimensionSet());
        exportAnnualDetail(annualAnalysisVO, response);
        return ResultDataVO.success();
    }

    /**
     * 判断是否反向视角
     *
     * @param annualVO 参数
     */
    private void isReverseViewFlag(AnnualAnalysisVO annualVO) {
        annualVO.setReverseSymbol(false);
        boolean viewFlagCondition = IndustryIndexEnum.VIEW_FLAG_U.VIEW5.getValue().equals(annualVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_U.VIEW6.getValue().equals(annualVO.getViewFlag()) || IndustryIndexEnum.VIEW_FLAG_U.VIEW7.getValue().equals(annualVO.getViewFlag());
        if (IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(annualVO.getGranularityType()) && viewFlagCondition) {
            annualVO.setReverseSymbol(true);
        }
    }

    private void mutilSelectGroupCnName(AnnualAnalysisVO annualAnalysisVO, List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList, String methodFlag) {
        // 多选时图例名称拼接
        if (CommonConstant.GROUP_LEVEL_P_M.contains(annualAnalysisVO.getGroupLevel())) {
            // 通用颗粒度
            if (IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(annualAnalysisVO.getGranularityType())) {
                universalGroupCnNameStr(dmFocAnnualAmpVOList, methodFlag);
            }
            // 盈利颗粒度
            if (IndustryIndexEnum.GRANULARITY_TYPE.PROFITS.getValue().equals(annualAnalysisVO.getGranularityType())) {
                profitGroupCnNameStr(annualAnalysisVO, dmFocAnnualAmpVOList, methodFlag);
            }
            // 量纲颗粒度
            if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(annualAnalysisVO.getGranularityType())) {
                dimensionGroupCnNameStr(dmFocAnnualAmpVOList, methodFlag);
            }
        }
        // 盈利颗粒度名称拼接
        profitLevelCnName(annualAnalysisVO, dmFocAnnualAmpVOList, methodFlag);
        // 量纲颗粒度名称拼接
        dimensionLevelCnName(annualAnalysisVO, dmFocAnnualAmpVOList, methodFlag);
        // ceg层级或者发货对象
        universalCegAndShippingObject(annualAnalysisVO, dmFocAnnualAmpVOList, methodFlag);
    }

    private void universalCegAndShippingObject(AnnualAnalysisVO annualAnalysisVO, List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList, String methodFlag) {
        if (IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(annualAnalysisVO.getGranularityType())) {
            if ("all".equals(methodFlag)) {
                if (GroupLevelEnumD.CEG.getValue().equals(annualAnalysisVO.getGroupLevel()) || GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue().equals(annualAnalysisVO.getGroupLevel())) {
                    dmFocAnnualAmpVOList.stream().forEach(ele -> {
                        ele.setGroupCnName(ele.getGroupCnName() + "(" + ele.getProdRndTeamCnName() + "))");
                    });
                }
            }
        }
    }

    private void profitLevelCnName(AnnualAnalysisVO annualAnalysisVO, List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList, String methodFlag) {
        if (IndustryIndexEnum.GRANULARITY_TYPE.PROFITS.getValue().equals(annualAnalysisVO.getGranularityType())) {
            if ("all".equals(methodFlag)) {
                allIndustryChartCnName(annualAnalysisVO, dmFocAnnualAmpVOList);
            } else {
                profitMutliChartCnName(annualAnalysisVO, dmFocAnnualAmpVOList);
            }
        }

    }

    private void profitMutliChartCnName(AnnualAnalysisVO annualAnalysisVO, List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList) {
        // L1维度
        if (GroupLevelEnumP.L1.getValue().equals(annualAnalysisVO.getParentLevel())) {
            dmFocAnnualAmpVOList.stream().forEach(ele -> {
                ele.setParentCnName(ele.getL1Name() + "(" + ele.getProdRndTeamCnName() + ")");
            });
        }
        // L2维度
        if (GroupLevelEnumP.L2.getValue().equals(annualAnalysisVO.getParentLevel())) {
            dmFocAnnualAmpVOList.stream().forEach(ele -> {
                ele.setParentCnName(ele.getL2Name() + "(" + ele.getL1Name() + "(" + ele.getProdRndTeamCnName() + "))");
            });
        }
    }

    private void allIndustryChartCnName(AnnualAnalysisVO annualAnalysisVO, List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList) {
        // L1维度
        if (GroupLevelEnumP.L1.getValue().equals(annualAnalysisVO.getGroupLevel())) {
            if (CollectionUtils.isNotEmpty(dmFocAnnualAmpVOList)) {
                dmFocAnnualAmpVOList.stream().forEach(ele -> {
                    ele.setGroupCnName(ele.getGroupCnName() + "(" + ele.getProdRndTeamCnName() + ")");
                });
            }
        }
        if (GroupLevelEnumP.L2.getValue().equals(annualAnalysisVO.getGroupLevel())) {
            if (CollectionUtils.isNotEmpty(dmFocAnnualAmpVOList)) {
                dmFocAnnualAmpVOList.stream().forEach(ele -> {
                    ele.setGroupCnName(ele.getGroupCnName() + "(" + ele.getL1Name() + "(" + ele.getProdRndTeamCnName() + "))");
                });
            }
        }
        // CEG层级或者发货对象
        if (GroupLevelEnumD.CEG.getValue().equals(annualAnalysisVO.getGroupLevel()) || GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue().equals(annualAnalysisVO.getGroupLevel())) {
            dmFocAnnualAmpVOList.stream().forEach(ele -> {
                setProfitGroupName(ele);
            });
        }
    }

    private void dimensionLevelCnName(AnnualAnalysisVO annualAnalysisVO, List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList, String methodFlag) {
        if (IndustryIndexEnum.GRANULARITY_TYPE.DIMENSION.getValue().equals(annualAnalysisVO.getGranularityType())) {
            // 量纲层级名称拼接
            if ("all".equals(methodFlag)) {
                dimensionAllChartCnName(annualAnalysisVO, dmFocAnnualAmpVOList);
            } else {
                dimensionMutliSubCharCnName(annualAnalysisVO, dmFocAnnualAmpVOList);
            }
        }
    }

    private void dimensionAllChartCnName(AnnualAnalysisVO annualAnalysisVO, List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList) {
        // coa维度,量纲维度,量纲子类维度,量纲子类明细维度,量纲SPART维度
        if (CommonConstant.GROUP_LEVEL_ALL_DIMENSION.contains(annualAnalysisVO.getGroupLevel())) {
            dmFocAnnualAmpVOList.stream().forEach(ele -> {
                ele.setGroupCnName(getDmsCnName(ele));
            });
        }
        // ceg层级或者发货对象
        if (GroupLevelEnumD.CEG.getValue().equals(annualAnalysisVO.getGroupLevel()) || GroupLevelEnumMadeD.SHIPPING_OBJECT.getValue().equals(annualAnalysisVO.getGroupLevel())) {
            dmFocAnnualAmpVOList.stream().forEach(ele -> {
                String dmsCnName = getDmsCnName(ele);
                ele.setGroupCnName(ele.getGroupCnName() + "(" + dmsCnName + ")");
            });
        }
    }

    private void dimensionMutliSubCharCnName(AnnualAnalysisVO annualAnalysisVO, List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList) {
        // coa维度
        if (GroupLevelEnumD.COA.getValue().equals(annualAnalysisVO.getParentLevel())) {
            dmFocAnnualAmpVOList.stream().forEach(ele -> {
                ele.setParentCnName(String.format("%s %s%s%s%s", ele.getParentCode(), ele.getParentCnName(), "(", ele.getProdRndTeamCnName(), ")"));
            });
        }
        // 量纲维度
        if (GroupLevelEnumD.DIMENSION.getValue().equals(annualAnalysisVO.getParentLevel())) {
            dmFocAnnualAmpVOList.stream().forEach(ele -> {
                if (StringUtils.isNotBlank(ele.getCoaCode()) && StringUtils.isNotBlank(ele.getCoaCnName())) {
                    ele.setParentCnName(String.format("%s %s%s%s %s%s%s%s", ele.getParentCode(), ele.getParentCnName(), "(", ele.getCoaCode(), ele.getCoaCnName(), "(", ele.getProdRndTeamCnName(), "))"));
                } else {
                    ele.setParentCnName(String.format("%s %s%s%s%s", ele.getParentCode(), ele.getParentCnName(), "(", ele.getProdRndTeamCnName(), ")"));
                }
            });
        }
        // 量纲子类维度
        if (GroupLevelEnumD.SUBCATEGORY.getValue().equals(annualAnalysisVO.getParentLevel())) {
            dmFocAnnualAmpVOList.stream().forEach(ele -> {
                ele.setParentCnName(String.format("%s %s%s%s %s%s%s%s", ele.getParentCode(), ele.getParentCnName(), "(", ele.getDimensionCode(), ele.getDimensionCnName(), "(", ele.getProdRndTeamCnName(), "))"));
            });
        }
        // 量纲子类明细维度
        if (GroupLevelEnumD.SUB_DETAIL.getValue().equals(annualAnalysisVO.getParentLevel())) {
            dmFocAnnualAmpVOList.stream().forEach(ele -> {
                ele.setParentCnName(String.format("%s %s%s%s %s%s%s %s%s%s%s", ele.getParentCode(), ele.getParentCnName(), "(", ele.getDimensionSubCategoryCode(), ele.getDimensionSubCategoryCnName(), "(", ele.getDimensionCode(), ele.getDimensionCnName(), "(", ele.getProdRndTeamCnName(), ")))"));
            });
        }
        // 量纲SPART维度
        if (GroupLevelEnumD.SPART.getValue().equals(annualAnalysisVO.getParentLevel())) {
            dmFocAnnualAmpVOList.stream().forEach(ele -> {
                ele.setParentCnName(String.format("%s %s%s%s %s%s%s %s%s%s %s%s%s%s", ele.getParentCode(), ele.getParentCnName(), "(", ele.getDimensionSubDetailCode(), ele.getDimensionSubDetailCnName(), "(", ele.getDimensionSubCategoryCode(), ele.getDimensionSubCategoryCnName(), "(", ele.getDimensionCode(), ele.getDimensionCnName(), "(", ele.getProdRndTeamCnName(), "))))"));
            });
        }
    }

    private void universalGroupCnNameStr(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList, String methodFlag) {
        dmFocAnnualAmpVOList.stream().forEach(ele -> {
            if ("all".equals(methodFlag)) {
                StringBuffer groupCnName = new StringBuffer();
                groupCnName.append(ele.getGroupCnName()).append("(").append(ele.getProdRndTeamCnName()).append(")");
                ele.setGroupCnName(groupCnName.toString());
            } else {
                ele.setParentCnName(ele.getParentCnName() + "(" + ele.getProdRndTeamCnName() + ")");
            }
        });
    }

    private void profitGroupCnNameStr(AnnualAnalysisVO annualAnalysisVO, List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList, String methodFlag) {
        if (GroupLevelEnumP.L1.getValue().equals(annualAnalysisVO.getMultiLevel())) {
            dmFocAnnualAmpVOList.stream().forEach(ele -> {
                // 涨跌图
                if ("all".equals(methodFlag)) {
                    ele.setGroupCnName(ele.getGroupCnName() + "(" + ele.getL1Name() + "(" + ele.getProdRndTeamCnName() + "))");
                } else {
                    // 多子项图
                    ele.setParentCnName(ele.getParentCnName() + "(" + ele.getL1Name() + "(" + ele.getProdRndTeamCnName() + "))");
                }
            });
        } else if (GroupLevelEnumP.L2.getValue().equals(annualAnalysisVO.getMultiLevel())) {
            parentLevelWithL2(dmFocAnnualAmpVOList, methodFlag);
        } else {
            universalGroupCnNameStr(dmFocAnnualAmpVOList, methodFlag);
        }
    }

    private void parentLevelWithL2(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList, String methodFlag) {
        dmFocAnnualAmpVOList.stream().forEach(ele -> {
            // 涨跌图
            if ("all".equals(methodFlag)) {
                ele.setGroupCnName(ele.getGroupCnName() + "(" + ele.getL2Name() + "(" + ele.getL1Name() + "(" + ele.getProdRndTeamCnName() + ")))");
            } else {
                ele.setParentCnName(ele.getParentCnName() + "(" + ele.getL2Name() + "(" + ele.getL1Name() + "(" + ele.getProdRndTeamCnName() + ")))");
            }
        });
    }

    private void dimensionGroupCnNameStr(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList, String methodFlag) {
        dmFocAnnualAmpVOList.stream().forEach(ele -> {
            String dmsCnName = getDmsCnName(ele);
            // 涨跌图
            if ("all".equals(methodFlag)) {
                ele.setGroupCnName(ele.getGroupCnName() + "(" + dmsCnName + ")");
            } else {
                // 多子项图
                ele.setParentCnName(ele.getParentCnName() + "(" + dmsCnName + ")");
            }
        });
    }

    private String getDmsCnName(DmFocAnnualAmpVO ele) {
        String dmsCnName = null;
        if (StringUtils.isNotBlank(ele.getCoaCode()) && StringUtils.isNotBlank(ele.getCoaCnName())) {
            dmsCnName = String.format("%s %s%s%s%s", ele.getCoaCode(), ele.getCoaCnName(), "(", ele.getProdRndTeamCnName(), ")");
        }
        if (StringUtils.isNotBlank(ele.getCoaCode()) && StringUtils.isNotBlank(ele.getDimensionCode()) && StringUtils.isNotBlank(ele.getDimensionCnName())) {
            dmsCnName = String.format("%s %s%s%s %s%s%s%s", ele.getDimensionCode(), ele.getDimensionCnName(), "(", ele.getCoaCode(), ele.getCoaCnName(), "(", ele.getProdRndTeamCnName(), "))");
        }
        if (StringUtils.isBlank(ele.getCoaCode()) && StringUtils.isNotBlank(ele.getDimensionCode()) && StringUtils.isNotBlank(ele.getDimensionCnName())) {
            dmsCnName = String.format("%s %s%s%s%s", ele.getDimensionCode(), ele.getDimensionCnName(), "(", ele.getProdRndTeamCnName(), ")");
        }
        if (StringUtils.isNotBlank(ele.getCoaCode()) && StringUtils.isNotBlank(ele.getDimensionSubCategoryCode()) && StringUtils.isNotBlank(ele.getDimensionSubCategoryCnName())) {
            dmsCnName = String.format("%s %s%s%s %s%s%s %s%s%s%s", ele.getDimensionSubCategoryCode(), ele.getDimensionSubCategoryCnName(), "(", ele.getDimensionCode(), ele.getDimensionCnName(), "(", ele.getCoaCode(), ele.getCoaCnName(), "(", ele.getProdRndTeamCnName(), ")))");
        }
        dmsCnName = getDmsCnNameOther(ele, dmsCnName);
        return dmsCnName;
    }

    private String getDmsCnNameOther(DmFocAnnualAmpVO ele, String dmsCnName) {
        if (StringUtils.isBlank(ele.getCoaCode()) && StringUtils.isNotBlank(ele.getDimensionSubCategoryCode()) && StringUtils.isNotBlank(ele.getDimensionSubCategoryCnName())) {
            dmsCnName = String.format("%s %s%s%s %s%s%s%s", ele.getDimensionSubCategoryCode(), ele.getDimensionSubCategoryCnName(), "(", ele.getDimensionCode(), ele.getDimensionCnName(), "(", ele.getProdRndTeamCnName(), "))");
        }
        if (StringUtils.isNotBlank(ele.getCoaCode()) && StringUtils.isNotBlank(ele.getDimensionSubDetailCode()) && StringUtils.isNotBlank(ele.getDimensionSubDetailCnName())) {
            dmsCnName = String.format("%s %s%s%s %s%s%s %s%s%s %s%s%s%s", ele.getDimensionSubDetailCode(), ele.getDimensionSubDetailCnName(), "(", ele.getDimensionSubCategoryCode(), ele.getDimensionSubCategoryCnName(), "(", ele.getDimensionCode(), ele.getDimensionCnName(), "(", ele.getCoaCode(), ele.getCoaCnName(), "(", ele.getProdRndTeamCnName(), "))))");
        }
        if (StringUtils.isBlank(ele.getCoaCode()) && StringUtils.isNotBlank(ele.getDimensionSubDetailCode()) && StringUtils.isNotBlank(ele.getDimensionSubDetailCnName())) {
            dmsCnName = String.format("%s %s%s%s %s%s%s %s%s%s%s", ele.getDimensionSubDetailCode(), ele.getDimensionSubDetailCnName(), "(", ele.getDimensionSubCategoryCode(), ele.getDimensionSubCategoryCnName(), "(", ele.getDimensionCode(), ele.getDimensionCnName(), "(", ele.getProdRndTeamCnName(), ")))");
        }
        if (StringUtils.isNotBlank(ele.getCoaCode()) && StringUtils.isNotBlank(ele.getSpartCode()) && StringUtils.isNotBlank(ele.getSpartCnName())) {
            dmsCnName = String.format("%s%s%s %s%s %s%s %s%s %s%s%s%s", ele.getSpartCode(), "(", ele.getDimensionSubDetailCode(), ele.getDimensionSubDetailCnName(), "(", ele.getDimensionSubCategoryCode(), ele.getDimensionSubCategoryCnName(), "(", ele.getDimensionCode(), ele.getDimensionCnName(), "(", ele.getCoaCode(), ele.getCoaCnName(), "(", ele.getProdRndTeamCnName(), ")))))");
        }
        if (StringUtils.isBlank(ele.getCoaCode()) && StringUtils.isNotBlank(ele.getSpartCode()) && StringUtils.isNotBlank(ele.getSpartCnName())) {
            dmsCnName = String.format("%s%s%s %s%s%s %s%s%s %s%s%s%s", ele.getSpartCode(), "(", ele.getDimensionSubDetailCode(), ele.getDimensionSubDetailCnName(), "(", ele.getDimensionSubCategoryCode(), ele.getDimensionSubCategoryCnName(), "(", ele.getDimensionCode(), ele.getDimensionCnName(), "(", ele.getProdRndTeamCnName(), "))))");
        }
        return dmsCnName;
    }

    private void setProfitGroupName(DmFocAnnualAmpVO ele) {
        if (StringUtils.isNotBlank(ele.getL2Name())) {
            ele.setGroupCnName(ele.getGroupCnName() + "(" + ele.getL2Name() + "(" + ele.getL1Name() + "(" + ele.getProdRndTeamCnName() + ")))");
            return;
        }
        if (StringUtils.isNotBlank(ele.getL1Name())) {
            ele.setGroupCnName(ele.getGroupCnName() + "(" + ele.getL1Name() + "(" + ele.getProdRndTeamCnName() + "))");
        }
    }

    private void setNoEffectiveAmp(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList, AnnualAnalysisVO annualAnalysisVO, String type) {
        String groupLevel = annualAnalysisVO.getGroupLevel();
        dmFocAnnualAmpVOList.stream().forEach(annualAmp -> {
            String statusCode = annualAmp.getStatusCode();
            // item层级
            if (GroupLevelEnumU.ITEM.getValue().equals(groupLevel)) {
                if (StringUtils.isNotBlank(statusCode)) {
                    getHoverMsgForItemCondition(statusCode, annualAmp);
                }
            } else {
                if (StringUtils.isNotBlank(statusCode)) {
                    setGroupCodeHoverMsg(statusCode, annualAmp);
                }
            }
        });
        setPromptMessage(dmFocAnnualAmpVOList, type, groupLevel);
    }

    private void getHoverMsgForItemCondition(String statusCode, DmFocAnnualAmpVO annualAmpVO) {
        switch (statusCode) {
            case "1":
                annualAmpVO.setHoverMsg(annualAmpVO.getPeriodYear() + "年无数据，无法计算涨跌幅");
                break;
            case "2":
                annualAmpVO.setHoverMsg(commonAmpService.subtractNum(annualAmpVO.getPeriodYear(), 1) + "、" + annualAmpVO.getPeriodYear() + "年无数据，无法计算涨跌幅");
                break;
            case "4":
                annualAmpVO.setHoverMsg(commonAmpService.subtractNum(annualAmpVO.getPeriodYear(), 1) + "无数据，且无法用历史年均本补齐");
                break;
            case "5":
                annualAmpVO.setHoverMsg(annualAmpVO.getAnnualAmp() + ";" + commonAmpService.subtractNum(annualAmpVO.getPeriodYear(), 1) + "年无数据，由" + annualAmpVO.getAppendYear() + "年均本补齐");
                break;
            default:
                break;
        }
    }

    private void setGroupCodeHoverMsg(String statusCode, DmFocAnnualAmpVO annualAmpVO) {
        switch (statusCode) {
            case "1":
                annualAmpVO.setHoverMsg(annualAmpVO.getPeriodYear() + "年无数据，无法计算涨跌幅");
                break;
            case "2":
                annualAmpVO.setHoverMsg(commonAmpService.subtractNum(annualAmpVO.getPeriodYear(), 1) + "年无数据，无法计算涨跌幅");
                break;
            case "4":
                annualAmpVO.setHoverMsg(commonAmpService.subtractNum(annualAmpVO.getPeriodYear(), 1) + "、" + annualAmpVO.getPeriodYear() + "年无数据，无法计算涨跌幅");
                break;
            default:
                break;
        }
    }

    private void setPromptMessage(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList, String type, String groupLevel) {
        // 当涨跌幅为0且有提示语时，涨跌幅设置为0*
        dmFocAnnualAmpVOList.stream().forEach(annualAmp -> {
            if (StringUtils.isNotBlank(annualAmp.getHoverMsg()) && !"5".equals(annualAmp.getStatusCode())
                    && ("0.0%".equals(annualAmp.getAnnualAmp()) || "0.0".equals(annualAmp.getAnnualAmp()))) {
                if ("data".equals(type)) {
                    annualAmp.setAnnualAmp("0*");
                    annualAmp.setWeightAnnualAmpPercent("0*");
                } else {
                    annualAmp.setAnnualAmp(null);
                    if (!GroupLevelEnumU.ITEM.getValue().equals(groupLevel)) {
                        annualAmp.setWeightAnnualAmpPercent(null);
                    }
                }
            }
            if (!GroupLevelEnumU.ITEM.getValue().equals(groupLevel) && StringUtils.isNotBlank(annualAmp.getAppendFlag())
                    && "Y".equals(annualAmp.getAppendFlag())) {
                if ("data".equals(type)) {
                    annualAmp.setWeightRate("0*");
                } else {
                    annualAmp.setWeightRate(null);
                }
            }
        });
    }

    private List<DmFocAnnualAmpVO> currentLevelCustomCombCondition(AnnualAnalysisVO annualAnalysisVO) {
        // 区分出汇总组合和非汇总组合
        distinguishIfCombine(annualAnalysisVO);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        // 包含汇总组合，且单选汇总组合
        if (!annualAnalysisVO.getIsMultipleSelect()) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType())) {
                dmFocAnnualAmpVOList = annualAmpDao.allIndustryCombCost(annualAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
                dmFocAnnualAmpVOList = madeAnnualAmpDao.madeAllIndustryCombCost(annualAnalysisVO);
            }
            if ("excel".equals(annualAnalysisVO.getExcelExp())) {
                setCombGroupCnName(dmFocAnnualAmpVOList);
            }
        } else {
            // 包含汇总组合，且选了汇总组合+其他正常的code
            getAnnualAmpList(annualAnalysisVO, dmFocAnnualAmpVOList);
        }
        return dmFocAnnualAmpVOList;
    }

    private void getAnnualAmpList(AnnualAnalysisVO annualAnalysisVO, List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList) {
        // 包含汇总组合
        if (CollectionUtils.isNotEmpty(annualAnalysisVO.getCombinaCodeList())) {
            List<DmFocAnnualAmpVO> combAnnualList = new ArrayList<>();
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType())) {
                combAnnualList = annualAmpDao.allIndustryCombCost(annualAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
                combAnnualList = madeAnnualAmpDao.madeAllIndustryCombCost(annualAnalysisVO);
            }
            if ("excel".equals(annualAnalysisVO.getExcelExp())) {
                setCombGroupCnName(combAnnualList);
            }
            dmFocAnnualAmpVOList.addAll(combAnnualList);
        }
        // 非汇总组合
        if (CollectionUtils.isNotEmpty(annualAnalysisVO.getGroupCodeList())) {
            List<DmFocAnnualAmpVO> normalAnnualList = new ArrayList<>();
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType())) {
                normalAnnualList = annualAmpDao.allIndustryCost(annualAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
                normalAnnualList = madeAnnualAmpDao.madeAllIndustryCost(annualAnalysisVO);
            }
            dmFocAnnualAmpVOList.addAll(normalAnnualList);
        }
    }

    private List<DmFocAnnualAmpVO> currentLevelNotContainComb(AnnualAnalysisVO annualAnalysisVO) {
        if (!annualAnalysisVO.getReverseSymbol()) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType())) {
                return annualAmpDao.allIndustryCost(annualAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
                return madeAnnualAmpDao.madeAllIndustryCost(annualAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(annualAnalysisVO.getCostType())) {
                List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
                asyncQueryService.findTotalAllIndustryCost(dmFocAnnualAmpVOList, annualAnalysisVO);
                return dmFocAnnualAmpVOList;
            }
        } else {
            // 根据groupLevel计算子项level
            String groupLevel;
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
                groupLevel = FcstIndexMadeUtil.getNextGroupLevelByView(annualAnalysisVO.getViewFlag(),
                        annualAnalysisVO.getGroupLevel(), annualAnalysisVO.getGranularityType(),annualAnalysisVO.getIndustryOrg());
            } else {
                groupLevel = FcstIndexUtil.getNextGroupLevelByView(annualAnalysisVO.getViewFlag(),
                        annualAnalysisVO.getGroupLevel(), annualAnalysisVO.getGranularityType(),annualAnalysisVO.getIndustryOrg());
            }
            annualAnalysisVO.setNextGroupLevel(groupLevel);
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType())) {
                return annualAmpDao.allIndustryRevCost(annualAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
                return madeAnnualAmpDao.madeAllIndustryRevCost(annualAnalysisVO);
            }
        }
        return null;
    }

    private void setCombGroupCnName(List<DmFocAnnualAmpVO> combAnnualList) {
        for (DmFocAnnualAmpVO combAnnualAmpVO : combAnnualList) {
            if (!combAnnualAmpVO.getGroupCnName().contains(combAnnualAmpVO.getCustomCnName())) {
                StringBuffer groupCnName = new StringBuffer();
                groupCnName.append(combAnnualAmpVO.getGroupCnName()).append("(").append(combAnnualAmpVO.getCustomCnName()).append(")");
                combAnnualAmpVO.setGroupCnName(groupCnName.toString());
            }
        }
    }

    private void distinguishIfCombine(AnnualAnalysisVO annualAnalysisVO) {
        List<String> combinaCodeList = new ArrayList<>();
        List<String> groupCodeList = new ArrayList<>();
        List<String> prodTeamCodeList = new ArrayList<>();
        List<String> allGroupCode = annualAnalysisVO.getGroupCodeList();
        List<String> teamCodeList = annualAnalysisVO.getTeamCodeList();
        annualAnalysisVO.setGroupCodeList(null);
        annualAnalysisVO.setCodeFlag(true);
        // 区分出汇总组合和非汇总组合
        for (String groupCode : allGroupCode) {
            if (groupCode.contains("_##")) {
                if ("null".equals(groupCode.split("_##")[1])) {
                    combinaCodeList.add(groupCode.split("_##")[0] + "_##" + groupCode.split("_##")[0]);
                    annualAnalysisVO.setCodeFlag(false);
                } else {
                    combinaCodeList.add(groupCode);
                }
                annualAnalysisVO.setCombinaCodeList(combinaCodeList);
            } else {
                groupCodeList.add(groupCode);
                annualAnalysisVO.setGroupCodeList(groupCodeList);
            }
        }
        teamCodeList.stream().forEach(code -> {
            if (code.contains("_##")) {
                String prodTeamCode = code.split("_##")[1];
                prodTeamCodeList.add(prodTeamCode);
            } else {
                prodTeamCodeList.add(code);
            }
        });
        annualAnalysisVO.setProdTeamCodeList(prodTeamCodeList);
    }

    private void distinguishIfCombineForMutil(AnnualAnalysisVO annualAnalysisVO) {
        List<String> combinaCodeList = new ArrayList<>();
        List<String> parentCodeList = new ArrayList<>();
        List<String> allParentCodeList = annualAnalysisVO.getParentCodeList();
        annualAnalysisVO.setParentCodeList(null);
        for (String parentCode : allParentCodeList) {
            if (parentCode.contains("_##")) {
                if ("null".equals(parentCode.split("_##")[1])) {
                    combinaCodeList.add(parentCode.split("_##")[0] + "_##" + parentCode.split("_##")[0]);
                } else {
                    combinaCodeList.add(parentCode);
                }
                annualAnalysisVO.setCombinaCodeList(combinaCodeList);
            } else {
                parentCodeList.add(parentCode);
                annualAnalysisVO.setParentCodeList(parentCodeList);
            }
        }
    }

    private List<DmFocAnnualAmpVO> multiChildNotContainsCustomComb(AnnualAnalysisVO annualAnalysisVO, PageVO pageVO) {
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = new ArrayList<>();
        // 如果parentlevel为LV0/LV1/LV2，也就是当parentLevel的下一层级还是重量级团队的时候，需要把prodRndTeamCodeList置为null
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setViewFlag(annualAnalysisVO.getViewFlag());
        monthAnalysisVO.setParentLevel(annualAnalysisVO.getParentLevel());
        monthAnalysisVO.setGroupLevel(annualAnalysisVO.getGroupLevel());
        monthAnalysisVO.setProdRndTeamCodeList(annualAnalysisVO.getTeamCodeList());
        monthAnalysisVO.setGranularityType(annualAnalysisVO.getGranularityType());
        monthAnalysisVO.setIndustryOrg(annualAnalysisVO.getIndustryOrg());
        commonService.setProdRndTeamCode(monthAnalysisVO);
        annualAnalysisVO.setTeamCodeList(monthAnalysisVO.getProdRndTeamCodeList());
        List<DmFocAnnualAmpVO> groupCodeOrderList = new ArrayList<>();
        // 单选
        if (!annualAnalysisVO.getIsMultipleSelect()) {
            groupCodeOrderList = getGroupOrderList(annualAnalysisVO, groupCodeOrderList);
            // 权重排序
            sortByWeight(groupCodeOrderList);
            String groupCodeOrder = limitGroupCodePage(groupCodeOrderList, pageVO);
            annualAnalysisVO.setGroupCodeOrder(groupCodeOrder);
            dmFocAnnualAmpVOResult = getMultiChildAmpResult(annualAnalysisVO, dmFocAnnualAmpVOResult);
        } else {
            // 多选
            dmFocAnnualAmpVOResult = mutliSelectAnnualAmpResult(annualAnalysisVO, pageVO);
        }
        setGroupCnNameDimensionLevel(dmFocAnnualAmpVOResult);
        return dmFocAnnualAmpVOResult;
    }

    private List<DmFocAnnualAmpVO> getMultiChildAmpResult(AnnualAnalysisVO annualAnalysisVO, List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult) {
        if (!annualAnalysisVO.getReverseSymbol()) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType())) {
                dmFocAnnualAmpVOResult = annualAmpDao.multiIndustryCostChart(annualAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
                dmFocAnnualAmpVOResult = madeAnnualAmpDao.madeMultiIndustryCostChart(annualAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(annualAnalysisVO.getCostType())) {
                dmFocAnnualAmpVOResult = getTotalAnnualAmpResult(annualAnalysisVO, dmFocAnnualAmpVOResult);
            }
        } else {
            dmFocAnnualAmpVOResult = getAnnualAmpVOResult(annualAnalysisVO, dmFocAnnualAmpVOResult);
        }
        return dmFocAnnualAmpVOResult;
    }

    private List<DmFocAnnualAmpVO> getGroupOrderList(AnnualAnalysisVO annualAnalysisVO, List<DmFocAnnualAmpVO> groupCodeOrderList) {
        if (!annualAnalysisVO.getReverseSymbol()) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType())) {
                groupCodeOrderList = annualAmpDao.findGroupCodeOrderByWeight(annualAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
                groupCodeOrderList = madeAnnualAmpDao.findMadeGroupCodeOrderByWeight(annualAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(annualAnalysisVO.getCostType())) {
                groupCodeOrderList = getTotalGroupCodeOrderList(annualAnalysisVO, groupCodeOrderList);
            }
        } else {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType())) {
                groupCodeOrderList = annualAmpDao.findRevGroupCodeOrderByWeight(annualAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
                groupCodeOrderList = madeAnnualAmpDao.findMadeRevGroupCodeOrderByWeight(annualAnalysisVO);
            }
        }
        return groupCodeOrderList;
    }

    private List<DmFocAnnualAmpVO> getTotalAnnualAmpResult(AnnualAnalysisVO annualAnalysisVO, List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult) {
        IndustryIndexEnum.COST_TYPE costSubType = IndustryIndexEnum.getCostType(annualAnalysisVO.getCostSubType());
        switch (costSubType) {
            case T:
                dmFocAnnualAmpVOResult = totalAnnualAmpDao.totalMultiIndustryCostChart(annualAnalysisVO);
                break;
            case M:
                dmFocAnnualAmpVOResult = madeAnnualAmpDao.madeMultiIndustryCostChart(annualAnalysisVO);
                break;
            case P:
                dmFocAnnualAmpVOResult = annualAmpDao.multiIndustryCostChart(annualAnalysisVO);
                break;
            default:
                break;
        }
        return dmFocAnnualAmpVOResult;
    }

    private List<DmFocAnnualAmpVO> getTotalGroupCodeOrderList(AnnualAnalysisVO annualAnalysisVO, List<DmFocAnnualAmpVO> groupCodeOrderList) {
        IndustryIndexEnum.COST_TYPE costSubType = IndustryIndexEnum.getCostType(annualAnalysisVO.getCostSubType());
        switch (costSubType) {
            case T:
                groupCodeOrderList = totalAnnualAmpDao.findTotalGroupCodeOrderByWeight(annualAnalysisVO);
                break;
            case M:
                groupCodeOrderList = madeAnnualAmpDao.findMadeGroupCodeOrderByWeight(annualAnalysisVO);
                break;
            case P:
                groupCodeOrderList = annualAmpDao.findGroupCodeOrderByWeight(annualAnalysisVO);
                break;
            default:
                break;
        }
        return groupCodeOrderList;
    }

    @NotNull
    private List<DmFocAnnualAmpVO> mutliSelectAnnualAmpResult(AnnualAnalysisVO annualAnalysisVO, PageVO pageVO) {
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = new ArrayList<>();
        List<DmFocAnnualAmpVO> totalAnnualAmpData = new ArrayList<>();
        dmFocAnnualAmpVOResult = getMultiAnualAmpResult(annualAnalysisVO, dmFocAnnualAmpVOResult, totalAnnualAmpData);
        HashMap<String, List<DmFocAnnualAmpVO>> divbyGroupCode = dmFocAnnualAmpVOResult.stream().collect(
                Collectors.groupingBy(result -> result.getL1Name() + "_" + result.getL2Name() + "_" + result.getCoaCode() + "_"
                        + result.getDimensionSubDetailCode() + "_"
                        + result.getDimensionSubCategoryCode() + "_"
                        + result.getDimensionCode() + "_"
                        + result.getSpartCode() + "_"
                        + result.getGroupCode() + "_"
                        + result.getParentCnName() + "_"
                        + result.getProdRndTeamCnName(), HashMap::new, Collectors.toList()));
        pageVO.setTotalRows(divbyGroupCode.size());
        LinkedHashMap<String, List<DmFocAnnualAmpVO>> groupCodeOrderByWeight = new LinkedHashMap<>();
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(annualAnalysisVO.getCostType())) {
            dmFocAnnualAmpVOResult = totalAnnualAmpData;
        }
        // 对比编码，设置编码
        putResultEntryKey(dmFocAnnualAmpVOResult, divbyGroupCode, groupCodeOrderByWeight);
        int curPage = pageVO.getCurPage() - 1;
        int pageSize = pageVO.getPageSize();
        List<DmFocAnnualAmpVO> annualAmpAndWeightList = new ArrayList<>();
        if (groupCodeOrderByWeight.size() > 0) {
            int fromIndex = curPage * pageSize;
            int totalIndex = (curPage + 1) * pageSize;
            if (totalIndex > groupCodeOrderByWeight.size()) {
                totalIndex = groupCodeOrderByWeight.size();
            }
            List<String> groupCodeList = new ArrayList<>();
            for (String groupCode : groupCodeOrderByWeight.keySet()) {
                groupCodeList.add(groupCode);
            }
            for (int i = fromIndex; i < totalIndex; i++) {
                String groupCode = groupCodeList.get(i);
                List<DmFocAnnualAmpVO> dmFocAnnualAmpVOS = groupCodeOrderByWeight.get(groupCode);
                annualAmpAndWeightList.addAll(dmFocAnnualAmpVOS);
            }
        }
        dmFocAnnualAmpVOResult = annualAmpAndWeightList;
        return dmFocAnnualAmpVOResult;
    }

    private void putResultEntryKey(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult, HashMap<String, List<DmFocAnnualAmpVO>> divbyGroupCode, LinkedHashMap<String, List<DmFocAnnualAmpVO>> groupCodeOrderByWeight) {
        for (DmFocAnnualAmpVO dmFocAnnualAmpVO : dmFocAnnualAmpVOResult) {
            for (Map.Entry<String, List<DmFocAnnualAmpVO>> stringListEntry : divbyGroupCode.entrySet()) {
                String connectCnName = dmFocAnnualAmpVO.getL1Name() + "_" + dmFocAnnualAmpVO.getL2Name() + "_"
                        + dmFocAnnualAmpVO.getCoaCode() + "_"
                        + dmFocAnnualAmpVO.getDimensionSubDetailCode() + "_"
                        + dmFocAnnualAmpVO.getDimensionSubCategoryCode() + "_"
                        + dmFocAnnualAmpVO.getDimensionCode() + "_"
                        + dmFocAnnualAmpVO.getSpartCode() + "_"
                        + dmFocAnnualAmpVO.getGroupCode() + "_"
                        + dmFocAnnualAmpVO.getParentCnName() + "_"
                        + dmFocAnnualAmpVO.getProdRndTeamCnName();
                if (connectCnName.equals(stringListEntry.getKey())) {
                    groupCodeOrderByWeight.put(stringListEntry.getKey(), stringListEntry.getValue());
                }
            }
        }
    }

    private List<DmFocAnnualAmpVO> getMultiAnualAmpResult(AnnualAnalysisVO annualAnalysisVO, List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult, List<DmFocAnnualAmpVO> totalAnnualAmpData) {
        if (!annualAnalysisVO.getReverseSymbol()) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType())) {
                dmFocAnnualAmpVOResult = annualAmpDao.multiIndustryCostChartMultiSelect(annualAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
                dmFocAnnualAmpVOResult = madeAnnualAmpDao.madeMultiIndustryCostChartMultiSelect(annualAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(annualAnalysisVO.getCostType())) {
                asyncQueryService.findMultiTotalAnnualAmpDataList(dmFocAnnualAmpVOResult, totalAnnualAmpData, annualAnalysisVO);
            }
        } else {
            dmFocAnnualAmpVOResult = getAnnualAmpVOResult(annualAnalysisVO, dmFocAnnualAmpVOResult);
        }
        return dmFocAnnualAmpVOResult;
    }

    private List<DmFocAnnualAmpVO> getAnnualAmpVOResult(AnnualAnalysisVO annualAnalysisVO, List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult) {
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType())) {
            dmFocAnnualAmpVOResult = annualAmpDao.multiIndustryChartRevMultiSelect(annualAnalysisVO);
        }
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
            dmFocAnnualAmpVOResult = madeAnnualAmpDao.madeMultiIndustryChartRevMultiSelect(annualAnalysisVO);
        }
        return dmFocAnnualAmpVOResult;
    }

    private List<DmFocAnnualAmpVO> multiChildNotContainsCombExcel(AnnualAnalysisVO annualAnalysisVO, String groupLevel) {
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = new ArrayList<>();
        // 如果grouplevel为LV0/LV1/LV2，则teamcode置为null
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setViewFlag(annualAnalysisVO.getViewFlag());
        monthAnalysisVO.setParentLevel(annualAnalysisVO.getParentLevel());
        monthAnalysisVO.setGroupLevel(groupLevel);
        monthAnalysisVO.setProdRndTeamCodeList(annualAnalysisVO.getTeamCodeList());
        monthAnalysisVO.setGranularityType(annualAnalysisVO.getGranularityType());
        monthAnalysisVO.setIndustryOrg(annualAnalysisVO.getIndustryOrg());
        commonService.setProdRndTeamCode(monthAnalysisVO);
        annualAnalysisVO.setTeamCodeList(monthAnalysisVO.getProdRndTeamCodeList());
        dmFocAnnualAmpVOResult = getAnnualAmpResultExcel(annualAnalysisVO, dmFocAnnualAmpVOResult);
        // 权重排序
        sortByWeight(dmFocAnnualAmpVOResult);
        if (annualAnalysisVO.getIsMultipleSelect() && !annualAnalysisVO.getReverseSymbol()) {
            // 多选时设置前端图例名称 需要拼接上重量级团队、盈利颗粒度、量纲颗粒度
            mutilSelectGroupCnName(annualAnalysisVO, dmFocAnnualAmpVOResult, "mutil");
        }
        return dmFocAnnualAmpVOResult;
    }

    private List<DmFocAnnualAmpVO> getAnnualAmpResultExcel(AnnualAnalysisVO annualAnalysisVO, List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult) {
        if (!annualAnalysisVO.getReverseSymbol()) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType())) {
                dmFocAnnualAmpVOResult = annualAmpDao.multiIndustryCostChartMultiSelect(annualAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
                dmFocAnnualAmpVOResult = madeAnnualAmpDao.madeMultiIndustryCostChartMultiSelect(annualAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(annualAnalysisVO.getCostType())) {
                List<DmFocAnnualAmpVO> totalAnnualAmpData = new ArrayList<>();
                asyncQueryService.findMultiTotalAnnualAmpDataList(dmFocAnnualAmpVOResult, totalAnnualAmpData, annualAnalysisVO);
            }
        } else {
            dmFocAnnualAmpVOResult = getAnnualAmpVOResult(annualAnalysisVO, dmFocAnnualAmpVOResult);
        }
        return dmFocAnnualAmpVOResult;
    }

    private List<DmFocAnnualAmpVO> overviewListContainsComb(AnnualAnalysisVO annualAnalysisVO, PageVO pageVO) {
        distinguishIfCombineForMutil(annualAnalysisVO);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOOrderByWeight = new ArrayList<>();
        if (!annualAnalysisVO.getIsMultipleSelect()) {
            // 单选汇总组合
            PagedResult<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = annualAmpDao.industryCombCharPage(annualAnalysisVO, pageVO);
            dmFocAnnualAmpVOOrderByWeight = dmFocAnnualAmpVOPagedResult.getResult();
            setParentCnNameDimensionLevel(dmFocAnnualAmpVOOrderByWeight);
        } else {
            dmFocAnnualAmpVOOrderByWeight = mutliCombSelectAnnualAmpResult(annualAnalysisVO, pageVO, dmFocAnnualAmpVOOrderByWeight);
        }
        return dmFocAnnualAmpVOOrderByWeight;
    }

    private List<DmFocAnnualAmpVO> overviewMadeListContainsComb(AnnualAnalysisVO annualAnalysisVO, PageVO pageVO) {
        distinguishIfCombineForMutil(annualAnalysisVO);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOOrderByWeight = new ArrayList<>();
        if (!annualAnalysisVO.getIsMultipleSelect()) {
            // 单选汇总组合
            PagedResult<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = madeAnnualAmpDao.madeIndustryCombCharPage(annualAnalysisVO, pageVO);
            dmFocAnnualAmpVOOrderByWeight = dmFocAnnualAmpVOPagedResult.getResult();
            setParentCnNameDimensionLevel(dmFocAnnualAmpVOOrderByWeight);
        } else {
            dmFocAnnualAmpVOOrderByWeight = mutliMadeCombSelectAnnualAmpResult(annualAnalysisVO, pageVO, dmFocAnnualAmpVOOrderByWeight);
        }
        return dmFocAnnualAmpVOOrderByWeight;
    }

    private List<DmFocAnnualAmpVO> mutliCombSelectAnnualAmpResult(AnnualAnalysisVO annualAnalysisVO, PageVO pageVO, List<DmFocAnnualAmpVO> dmFocAnnualAmpVOOrderByWeight) {
        // 汇总组合和正常的item列表
        int count = 0;
        annualAnalysisVO.setSubGroupCodeList(null);
        if (CollectionUtils.isNotEmpty(annualAnalysisVO.getCombinaCodeList()) && CollectionUtils.isNotEmpty(annualAnalysisVO.getParentCodeList())) {
            // 最后一项两个都选择
            count = annualAmpDao.industryCostAllItemCount(annualAnalysisVO);
            industryCostAllItemData(annualAnalysisVO, dmFocAnnualAmpVOOrderByWeight, "search");
        } else if (CollectionUtils.isNotEmpty(annualAnalysisVO.getCombinaCodeList()) && CollectionUtils.isEmpty(annualAnalysisVO.getParentCodeList())) {
            // 最后一项只选择了汇总组合
            count = annualAmpDao.industryCostCombItemCount(annualAnalysisVO);
            dmFocAnnualAmpVOOrderByWeight = annualAmpDao.industryCombCharlist(annualAnalysisVO);
            setParentCnNameDimensionLevel(dmFocAnnualAmpVOOrderByWeight);
        } else if (CollectionUtils.isNotEmpty(annualAnalysisVO.getParentCodeList()) && CollectionUtils.isEmpty(annualAnalysisVO.getCombinaCodeList())) {
            // 最后一项只选择了非组合
            count = annualAmpDao.industryCostNormalItemCount(annualAnalysisVO);
            dmFocAnnualAmpVOOrderByWeight = annualAmpDao.industryCostNormalList(annualAnalysisVO);
        }
        pageVO.setTotalRows(count);
        return dmFocAnnualAmpVOOrderByWeight;
    }


    private List<DmFocAnnualAmpVO> mutliMadeCombSelectAnnualAmpResult(AnnualAnalysisVO annualAnalysisVO, PageVO pageVO, List<DmFocAnnualAmpVO> dmFocAnnualAmpVOOrderByWeight) {
        // 汇总组合和正常的item列表
        int count = 0;
        annualAnalysisVO.setSubGroupCodeList(null);
        if (CollectionUtils.isNotEmpty(annualAnalysisVO.getCombinaCodeList()) && CollectionUtils.isNotEmpty(annualAnalysisVO.getParentCodeList())) {
            // 最后一项两个都选择
            count = madeAnnualAmpDao.madeIndustryCostAllItemCount(annualAnalysisVO);
            industryCostAllItemData(annualAnalysisVO, dmFocAnnualAmpVOOrderByWeight, "search");
        } else if (CollectionUtils.isNotEmpty(annualAnalysisVO.getCombinaCodeList()) && CollectionUtils.isEmpty(annualAnalysisVO.getParentCodeList())) {
            // 最后一项只选择了汇总组合
            count = madeAnnualAmpDao.madeIndustryCostCombItemCount(annualAnalysisVO);
            dmFocAnnualAmpVOOrderByWeight = madeAnnualAmpDao.madeIndustryCombCharlist(annualAnalysisVO);
            setParentCnNameDimensionLevel(dmFocAnnualAmpVOOrderByWeight);
        } else if (CollectionUtils.isNotEmpty(annualAnalysisVO.getParentCodeList()) && CollectionUtils.isEmpty(annualAnalysisVO.getCombinaCodeList())) {
            // 最后一项只选择了非组合
            count = madeAnnualAmpDao.madeIndustryCostNormalItemCount(annualAnalysisVO);
            dmFocAnnualAmpVOOrderByWeight = madeAnnualAmpDao.madeIndustryCostNormalList(annualAnalysisVO);
        }
        pageVO.setTotalRows(count);
        return dmFocAnnualAmpVOOrderByWeight;
    }

    private void setCombCnName(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOOrderByWeight) {
        // 多选时名称处理
        for (DmFocAnnualAmpVO dmFocAnnualAmpVO : dmFocAnnualAmpVOOrderByWeight) {
            if (null == dmFocAnnualAmpVO.getParentCnName()) {
                dmFocAnnualAmpVO.setParentCnName(dmFocAnnualAmpVO.getCustomCnName());
                continue;
            }
            if (!dmFocAnnualAmpVO.getParentCnName().equals(dmFocAnnualAmpVO.getCustomCnName())) {
                dmFocAnnualAmpVO.setParentCnName(dmFocAnnualAmpVO.getParentCnName() + "(" + dmFocAnnualAmpVO.getCustomCnName() + ")");
            }
        }
    }

    private void industryCostAllItemData(AnnualAnalysisVO annualAnalysisVO, List<DmFocAnnualAmpVO> dmFocAnnualAmpVOOrderByWeight, String methodType) {
        List<DmFocAnnualAmpVO> allItemList = new ArrayList<>();
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType())) {
            allItemList = annualAmpDao.industryCostAllList(annualAnalysisVO);
        }
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
            allItemList = madeAnnualAmpDao.madeIndustryCostAllList(annualAnalysisVO);
        }
        Map<String, List<DmFocAnnualAmpVO>> distinctItemList = allItemList.stream().collect(Collectors.groupingBy(DmFocAnnualAmpVO::getIsHasComb));
        for (Map.Entry<String, List<DmFocAnnualAmpVO>> normalAndCombItemList : distinctItemList.entrySet()) {
            String key = normalAndCombItemList.getKey();
            if ("normal".equals(key)) {
                List<DmFocAnnualAmpVO> noramlItemList = normalAndCombItemList.getValue();
                // 多选时设置前端图例名称 需要拼接上重量级团队、盈利颗粒度、量纲颗粒度
                if ("exp".equals(methodType)) {
                    mutilSelectGroupCnName(annualAnalysisVO, noramlItemList, "mutil");
                }
            }
            if ("comb".equals(key)) {
                List<DmFocAnnualAmpVO> combItemList = normalAndCombItemList.getValue();
                setCombParentCnName(combItemList);
            }
        }
        dmFocAnnualAmpVOOrderByWeight.addAll(allItemList);
    }

    private List<DmFocAnnualAmpVO> industryCostListContainsComb(AnnualAnalysisVO annualAnalysisVO) {
        distinguishIfCombineForMutil(annualAnalysisVO);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOOrderByWeight = new ArrayList<>();
        if (!annualAnalysisVO.getIsMultipleSelect()) {
            // 单选汇总组合
            dmFocAnnualAmpVOOrderByWeight = annualAmpDao.industryCombCharlist(annualAnalysisVO);
            setParentCnNameDimensionLevel(dmFocAnnualAmpVOOrderByWeight);
            setCombCnName(dmFocAnnualAmpVOOrderByWeight);
        } else {
            // 汇总组合和正常的item列表
            if (CollectionUtils.isNotEmpty(annualAnalysisVO.getParentCodeList()) && CollectionUtils.isNotEmpty(annualAnalysisVO.getCombinaCodeList())) {
                industryCostAllItemData(annualAnalysisVO, dmFocAnnualAmpVOOrderByWeight, "exp");
            } else if (CollectionUtils.isNotEmpty(annualAnalysisVO.getParentCodeList()) && CollectionUtils.isEmpty(annualAnalysisVO.getCombinaCodeList())) {
                dmFocAnnualAmpVOOrderByWeight = annualAmpDao.industryCostNormalList(annualAnalysisVO);
                // 多选时设置前端图例名称 需要拼接上重量级团队、盈利颗粒度、量纲颗粒度
                mutilSelectGroupCnName(annualAnalysisVO, dmFocAnnualAmpVOOrderByWeight, "mutil");
            } else if (CollectionUtils.isNotEmpty(annualAnalysisVO.getCombinaCodeList()) && CollectionUtils.isEmpty(annualAnalysisVO.getParentCodeList())) {
                dmFocAnnualAmpVOOrderByWeight = annualAmpDao.industryCombCharlist(annualAnalysisVO);
                setParentCnNameDimensionLevel(dmFocAnnualAmpVOOrderByWeight);
                // 多选时名称处理
                setCombCnName(dmFocAnnualAmpVOOrderByWeight);
            }
        }
        return dmFocAnnualAmpVOOrderByWeight;
    }

    private List<DmFocAnnualAmpVO> madeIndustryCostListContainsComb(AnnualAnalysisVO annualAnalysisVO) {
        distinguishIfCombineForMutil(annualAnalysisVO);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOOrderByWeight = new ArrayList<>();
        if (!annualAnalysisVO.getIsMultipleSelect()) {
            // 单选汇总组合
            dmFocAnnualAmpVOOrderByWeight = madeAnnualAmpDao.madeIndustryCombCharlist(annualAnalysisVO);
            setParentCnNameDimensionLevel(dmFocAnnualAmpVOOrderByWeight);
            setCombCnName(dmFocAnnualAmpVOOrderByWeight);
        } else {
            // 汇总组合和正常的item列表
            if (CollectionUtils.isNotEmpty(annualAnalysisVO.getParentCodeList()) && CollectionUtils.isNotEmpty(annualAnalysisVO.getCombinaCodeList())) {
                industryCostAllItemData(annualAnalysisVO, dmFocAnnualAmpVOOrderByWeight, "exp");
            } else if (CollectionUtils.isNotEmpty(annualAnalysisVO.getParentCodeList()) && CollectionUtils.isEmpty(annualAnalysisVO.getCombinaCodeList())) {
                dmFocAnnualAmpVOOrderByWeight = madeAnnualAmpDao.madeIndustryCostNormalList(annualAnalysisVO);
                // 多选时设置前端图例名称 需要拼接上重量级团队、盈利颗粒度、量纲颗粒度
                mutilSelectGroupCnName(annualAnalysisVO, dmFocAnnualAmpVOOrderByWeight, "mutil");
            } else if (CollectionUtils.isNotEmpty(annualAnalysisVO.getCombinaCodeList()) && CollectionUtils.isEmpty(annualAnalysisVO.getParentCodeList())) {
                dmFocAnnualAmpVOOrderByWeight = madeAnnualAmpDao.madeIndustryCombCharlist(annualAnalysisVO);
                setParentCnNameDimensionLevel(dmFocAnnualAmpVOOrderByWeight);
                // 多选时名称处理
                setCombCnName(dmFocAnnualAmpVOOrderByWeight);
            }
        }
        return dmFocAnnualAmpVOOrderByWeight;
    }

    private void setCombParentCnName(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOOrderByWeight) {
        for (DmFocAnnualAmpVO dmFocAnnualAmpVO : dmFocAnnualAmpVOOrderByWeight) {
            // 组合的顶层
            if (null == dmFocAnnualAmpVO.getParentCnName()) {
                dmFocAnnualAmpVO.setParentCnName(dmFocAnnualAmpVO.getProdRndTeamCnName());
            } else if (dmFocAnnualAmpVO.getProdRndTeamCnName().equals(dmFocAnnualAmpVO.getParentCnName())) {
                dmFocAnnualAmpVO.setParentCnName(dmFocAnnualAmpVO.getProdRndTeamCnName());
            } else {
                // 组合的子项
                dmFocAnnualAmpVO.setParentCnName(dmFocAnnualAmpVO.getParentCnName() + "(" + dmFocAnnualAmpVO.getProdRndTeamCnName() + ")");
            }
        }
    }

    private void purchaseLevelOrderByWithParent(AnnualAnalysisVO annualAnalysisVO) {
        List<DmFocAnnualAmpVO> parentProdTeamCodeOrderList = new ArrayList<>();
        String parentCodeOrder;
        // 多选时父层级是重量级团队层级先排序
        if (CommonConstant.GROUP_LEVEL_PROD.contains(annualAnalysisVO.getParentLevel())) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType())) {
                parentProdTeamCodeOrderList = annualAmpDao.findProdteamCodeOrderByWeight(annualAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
                parentProdTeamCodeOrderList = madeAnnualAmpDao.findMadeProdteamCodeOrderByWeight(annualAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(annualAnalysisVO.getCostType())) {
                parentProdTeamCodeOrderList = totalAnnualAmpDao.findTotalProdteamCodeOrderByWeight(annualAnalysisVO);
            }
        }
        if (CommonConstant.GROUP_LEVEL_PROFITS.contains(annualAnalysisVO.getParentLevel())
                || CommonConstant.GROUP_LEVEL_ALL_DIMENSION.contains(annualAnalysisVO.getParentLevel())) {
            // 多选时父层级是量纲层级先排序和多选时父层级是盈利颗粒度先排序
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType())) {
                parentProdTeamCodeOrderList = annualAmpDao.findGranularityTypeOrderByWeight(annualAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
                parentProdTeamCodeOrderList = madeAnnualAmpDao.findMadeGranularityTypeOrderByWeight(annualAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(annualAnalysisVO.getCostType())) {
                parentProdTeamCodeOrderList = totalAnnualAmpDao.findTotalGranularityTypeOrderByWeight(annualAnalysisVO);
            }
        }
        parentCodeOrder = parentProdTeamCodeOrderList.stream().map(DmFocAnnualAmpVO::getGroupCode).collect(Collectors.joining(","));
        annualAnalysisVO.setParentCodeOrder(parentCodeOrder);
    }

    private List<DmFocAnnualAmpVO> multiChildContainsCustomComb(AnnualAnalysisVO annualAnalysisVO, PageVO pageVO) {
        distinguishIfCombineForMutil(annualAnalysisVO);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = new ArrayList<>();
        // 单选汇总组合
        if (!annualAnalysisVO.getIsMultipleSelect()) {
            List<DmFocAnnualAmpVO> groupCodeOrderList = new ArrayList<>();
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType())) {
                groupCodeOrderList = annualAmpDao.findCombItemCodeOrderByWeight(annualAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
                groupCodeOrderList = madeAnnualAmpDao.findMadeCombItemCodeOrderByWeight(annualAnalysisVO);
            }
            // 权重排序
            sortByWeight(groupCodeOrderList);
            String groupCodeOrder = limitGroupCodePage(groupCodeOrderList, pageVO);
            annualAnalysisVO.setGroupCodeOrder(groupCodeOrder);
            annualAnalysisVO.setSubGroupCodeList(null);
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType())) {
                dmFocAnnualAmpVOResult = annualAmpDao.multiIndustryCombChart(annualAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
                dmFocAnnualAmpVOResult = madeAnnualAmpDao.madeMultiIndustryCombChart(annualAnalysisVO);
            }
            setParentCnNameDimensionLevel(dmFocAnnualAmpVOResult);
        } else {
            mutilHasCustomCombAndNormal(annualAnalysisVO, pageVO, dmFocAnnualAmpVOResult);
        }
        return dmFocAnnualAmpVOResult;
    }

    private void mutilHasCustomCombAndNormal(AnnualAnalysisVO annualAnalysisVO, PageVO pageVO, List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult) {
        // 所有组合维度的item集合
        List<DmFocViewInfoVO> itemList = new ArrayList<>();
        itemList = getItemList(annualAnalysisVO, itemList);
        int count = itemList.size();
        pageVO.setTotalRows(count);
        int curPage = pageVO.getCurPage() - 1;
        int pageSize = pageVO.getPageSize();
        if (CollectionUtils.isNotEmpty(itemList) && count > 0) {
            int fromIndex = curPage * pageSize;
            int totalIndex = (curPage + 1) * pageSize;
            if (totalIndex > count) {
                totalIndex = count;
            }
            List<String> allItem = itemList.subList(fromIndex, totalIndex).stream().map(DmFocViewInfoVO::getItemCode).collect(Collectors.toList());
            annualAnalysisVO.setSubGroupCodeList(allItem);
        }
        getAnnualAmpResult(annualAnalysisVO, dmFocAnnualAmpVOResult, "search");
    }

    private List<DmFocViewInfoVO> getItemList(AnnualAnalysisVO annualAnalysisVO, List<DmFocViewInfoVO> itemList) {
        if (CollectionUtils.isNotEmpty(annualAnalysisVO.getParentCodeList()) && CollectionUtils.isNotEmpty(annualAnalysisVO.getCombinaCodeList())) {
            itemList = allItemCodeList(annualAnalysisVO, itemList);
        } else if (CollectionUtils.isNotEmpty(annualAnalysisVO.getCombinaCodeList()) && CollectionUtils.isEmpty(annualAnalysisVO.getParentCodeList())) {
            itemList = allCombItemCodeList(annualAnalysisVO, itemList);
        } else if (CollectionUtils.isNotEmpty(annualAnalysisVO.getParentCodeList()) && CollectionUtils.isEmpty(annualAnalysisVO.getCombinaCodeList())) {
            itemList = allNormalItemCodeList(annualAnalysisVO, itemList);
        }
        return itemList;
    }

    private List<DmFocViewInfoVO> allNormalItemCodeList(AnnualAnalysisVO annualAnalysisVO, List<DmFocViewInfoVO> itemList) {
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType())) {
            itemList = dmFocViewInfoDao.getAllNormalItemCode(annualAnalysisVO);
        }
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
            itemList = dmFocMadeViewInfoDao.getMadeAllNormalItemCode(annualAnalysisVO);
        }
        return itemList;
    }

    private List<DmFocViewInfoVO> allCombItemCodeList(AnnualAnalysisVO annualAnalysisVO, List<DmFocViewInfoVO> itemList) {
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType())) {
            itemList = dmFocViewInfoDao.getAllCombItemCode(annualAnalysisVO);
        }
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
            itemList = dmFocMadeViewInfoDao.getMadeAllCombItemCode(annualAnalysisVO);
        }
        return itemList;
    }

    private List<DmFocViewInfoVO> allItemCodeList(AnnualAnalysisVO annualAnalysisVO, List<DmFocViewInfoVO> itemList) {
        if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType())) {
            itemList = dmFocViewInfoDao.getAllItemCode(annualAnalysisVO);
        }
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
            itemList = dmFocMadeViewInfoDao.getMadeAllItemCode(annualAnalysisVO);
        }
        return itemList;
    }

    private void getAnnualAmpResult(AnnualAnalysisVO annualAnalysisVO, List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult, String type) {
        // 多选有汇总组合
        if (CollectionUtils.isNotEmpty(annualAnalysisVO.getCombinaCodeList())) {
            List<DmFocAnnualAmpVO> combResult = new ArrayList<>();
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType())) {
                // 汇总组合item列表
                combResult = annualAmpDao.multiIndustryCombChart(annualAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
                // 汇总组合item列表
                combResult = madeAnnualAmpDao.madeMultiIndustryCombChart(annualAnalysisVO);
            }
            setParentCnNameDimensionLevel(combResult);
            // 多选时名称处理
            if ("exp".equals(type)) {
                setCombCnName(combResult);
            }
            dmFocAnnualAmpVOResult.addAll(combResult);
        }
        if (CollectionUtils.isNotEmpty(annualAnalysisVO.getParentCodeList())) {
            // 正常的item列表
            List<DmFocAnnualAmpVO> normalItemList = new ArrayList<>();
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType())) {
                normalItemList = annualAmpDao.multiIndustryCostNormalChart(annualAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
                normalItemList = madeAnnualAmpDao.madeMultiIndustryCostNormalChart(annualAnalysisVO);
            }
            // 多选时设置前端图例名称 需要拼接上重量级团队、盈利颗粒度、量纲颗粒度
            if ("exp".equals(type)) {
                mutilSelectGroupCnName(annualAnalysisVO, normalItemList, "mutil");
            }
            dmFocAnnualAmpVOResult.addAll(normalItemList);
        }
    }

    private List<DmFocAnnualAmpVO> multiChildContainsCombExcel(AnnualAnalysisVO annualAnalysisVO) {
        distinguishIfCombineForMutil(annualAnalysisVO);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = new ArrayList<>();
        // 单选汇总组合
        if (!annualAnalysisVO.getIsMultipleSelect()) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType())) {
                dmFocAnnualAmpVOResult = annualAmpDao.multiIndustryCombChart(annualAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
                dmFocAnnualAmpVOResult = madeAnnualAmpDao.madeMultiIndustryCombChart(annualAnalysisVO);
            }
            setParentCnNameDimensionLevel(dmFocAnnualAmpVOResult);
            // 多选时名称处理
            setCombCnName(dmFocAnnualAmpVOResult);
        } else {
            // 多选，并包含汇总组合
            getAnnualAmpResult(annualAnalysisVO, dmFocAnnualAmpVOResult, "exp");
        }
        return dmFocAnnualAmpVOResult;
    }

    private List<DmFocAnnualAmpVO> orderColumnAndLimitPage(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult, AnnualAnalysisVO annualAnalysisVO, PageVO pageVO) {
        String orderColumn = annualAnalysisVO.getOrderColumn();
        if (StringUtils.isNotBlank(orderColumn)) {
            switch (orderColumn) {
                case "weightRate":
                    dmFocAnnualAmpVOPagedResult = sortByWeightRateBar(dmFocAnnualAmpVOPagedResult, annualAnalysisVO);
                    break;
                case "groupCnName":
                    groupCnNameOrder(dmFocAnnualAmpVOPagedResult, annualAnalysisVO);
                    break;
                case "groupCode":
                    groupCodeOrder(dmFocAnnualAmpVOPagedResult, annualAnalysisVO);
                    break;
                case "annualAmp":
                    dmFocAnnualAmpVOPagedResult = getAnnualAmpPagedResult(dmFocAnnualAmpVOPagedResult, annualAnalysisVO);
                    break;
                case "weightAnnualAmpPercent":
                case "weightAnnualAmpPercentOrder":
                    if ("asc".equals(annualAnalysisVO.getOrderMethod())) {
                        dmFocAnnualAmpVOPagedResult = sortByWeightAnnualBar(dmFocAnnualAmpVOPagedResult, annualAnalysisVO, true);
                    } else {
                        dmFocAnnualAmpVOPagedResult = sortByWeightAnnualBar(dmFocAnnualAmpVOPagedResult, annualAnalysisVO, false);
                    }
                    break;
                default:
                    break;
            }
        } else {
            // 根据状态码区分-和有数据的记录后，默认权重大小排序
            dmFocAnnualAmpVOPagedResult = sortWeightBarAndRecordList(dmFocAnnualAmpVOPagedResult, annualAnalysisVO);
        }

        if (!annualAnalysisVO.getIsContainComb()) {
            // 分页
            int count = dmFocAnnualAmpVOPagedResult.size();
            dmFocAnnualAmpVOPagedResult = getDmFocAnnualAmpList(dmFocAnnualAmpVOPagedResult, pageVO);
            pageVO.setTotalRows(count);
        }
        return dmFocAnnualAmpVOPagedResult;
    }

    @NotNull
    private List<DmFocAnnualAmpVO> getAnnualAmpPagedResult(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult, AnnualAnalysisVO annualAnalysisVO) {
        List<DmFocAnnualAmpVO> barAnnualAmpList = dmFocAnnualAmpVOPagedResult.stream().filter(result -> "0*".equals(result.getAnnualAmp())).collect(Collectors.toList());
        List<DmFocAnnualAmpVO> otherAmpList = dmFocAnnualAmpVOPagedResult.stream().filter(vo -> !barAnnualAmpList.contains(vo)).collect(Collectors.toList());

        annualAmpOrder(otherAmpList, annualAnalysisVO);

        List<DmFocAnnualAmpVO> newAmpVOPagedResult = new ArrayList<>();
        newAmpVOPagedResult.addAll(otherAmpList);
        newAmpVOPagedResult.addAll(barAnnualAmpList);
        return newAmpVOPagedResult;
    }

    private List<DmFocAnnualAmpVO> sortWeightBarAndRecordList(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult, AnnualAnalysisVO annualAnalysisVO) {
        if (!GroupLevelEnumU.ITEM.getValue().equals(annualAnalysisVO.getGroupLevel()) && !GroupLevelEnumD.SPART.getValue().equals(annualAnalysisVO.getGroupLevel())) {
            List<DmFocAnnualAmpVO> barAnnualAmpVOList = dmFocAnnualAmpVOPagedResult.stream().filter(result -> "0*".equals(result.getWeightRate())).collect(Collectors.toList());
            List<DmFocAnnualAmpVO> otherList = dmFocAnnualAmpVOPagedResult.stream().filter(vo -> !barAnnualAmpVOList.contains(vo)).collect(Collectors.toList());
            sortByWeight(otherList);
            List<DmFocAnnualAmpVO> newDmFocAnnualAmpVOPagedResult = new ArrayList<>();
            newDmFocAnnualAmpVOPagedResult.addAll(otherList);
            newDmFocAnnualAmpVOPagedResult.addAll(barAnnualAmpVOList);
            return newDmFocAnnualAmpVOPagedResult;
        }
        return dmFocAnnualAmpVOPagedResult;
    }

    private List<DmFocAnnualAmpVO> sortByWeightAnnualBar(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult, AnnualAnalysisVO annualAnalysisVO, boolean flag) {
        List<DmFocAnnualAmpVO> otherWeightList = dmFocAnnualAmpVOPagedResult;
        List<DmFocAnnualAmpVO> newBarWeightVOList = new ArrayList<>();
        if (!GroupLevelEnumU.ITEM.getValue().equals(annualAnalysisVO.getGroupLevel()) || GroupLevelEnumD.SPART.getValue().equals(annualAnalysisVO.getGroupLevel())) {
            List<DmFocAnnualAmpVO> barAnnualAmpVOList = dmFocAnnualAmpVOPagedResult.stream().filter(result -> "0*".equals(result.getWeightAnnualAmpPercent())).collect(Collectors.toList());
            otherWeightList = dmFocAnnualAmpVOPagedResult.stream().filter(vo -> !barAnnualAmpVOList.contains(vo)).collect(Collectors.toList());
            newBarWeightVOList.addAll(barAnnualAmpVOList);
        }
        orderWeightAnnualAmpPercent(otherWeightList, annualAnalysisVO, flag);
        if (!GroupLevelEnumU.ITEM.getValue().equals(annualAnalysisVO.getGroupLevel()) || GroupLevelEnumD.SPART.getValue().equals(annualAnalysisVO.getGroupLevel())) {
            List<DmFocAnnualAmpVO> newAnnualAmpVOPagedResult = new ArrayList<>();
            newAnnualAmpVOPagedResult.addAll(otherWeightList);
            newAnnualAmpVOPagedResult.addAll(newBarWeightVOList);
            return newAnnualAmpVOPagedResult;
        } else {
            return otherWeightList;
        }
    }

    private List<DmFocAnnualAmpVO> sortByWeightRateBar(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult, AnnualAnalysisVO annualAnalysisVO) {
        List<DmFocAnnualAmpVO> otherList = dmFocAnnualAmpVOPagedResult;
        List<DmFocAnnualAmpVO> newBarAnnualAmpVOList = new ArrayList<>();
        if (!GroupLevelEnumU.ITEM.getValue().equals(annualAnalysisVO.getGroupLevel()) || GroupLevelEnumD.SPART.getValue().equals(annualAnalysisVO.getGroupLevel())) {
            List<DmFocAnnualAmpVO> barAnnualAmpVOList = dmFocAnnualAmpVOPagedResult.stream().filter(result -> "0*".equals(result.getWeightRate())).collect(Collectors.toList());
            otherList = dmFocAnnualAmpVOPagedResult.stream().filter(vo -> !barAnnualAmpVOList.contains(vo)).collect(Collectors.toList());
            newBarAnnualAmpVOList.addAll(barAnnualAmpVOList);
        }
        weightRateOrder(otherList, annualAnalysisVO);
        if (!GroupLevelEnumU.ITEM.getValue().equals(annualAnalysisVO.getGroupLevel()) || GroupLevelEnumD.SPART.getValue().equals(annualAnalysisVO.getGroupLevel())) {
            List<DmFocAnnualAmpVO> newAnnualAmpVOPagedResult = new ArrayList<>();
            newAnnualAmpVOPagedResult.addAll(otherList);
            newAnnualAmpVOPagedResult.addAll(newBarAnnualAmpVOList);
            return newAnnualAmpVOPagedResult;
        } else {
            return otherList;
        }
    }

    @NotNull
    private List<DmFocAnnualAmpVO> getDmFocAnnualAmpList(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult, PageVO pageVO) {
        int count = dmFocAnnualAmpVOPagedResult.size();
        int curPage = pageVO.getCurPage() - 1;
        int pageSize = pageVO.getPageSize();
        List<DmFocAnnualAmpVO> annualAmpAndWeightList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dmFocAnnualAmpVOPagedResult) && count > 0) {
            int fromIndex = curPage * pageSize;
            int totalIndex = (curPage + 1) * pageSize;
            if (totalIndex > count) {
                totalIndex = count;
            }
            annualAmpAndWeightList = dmFocAnnualAmpVOPagedResult.subList(fromIndex, totalIndex);
        }
        return annualAmpAndWeightList;
    }

    private void weightRateOrder(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult, AnnualAnalysisVO annualAnalysisVO) {
        if ("asc".equals(annualAnalysisVO.getOrderMethod())) {
            Collections.sort(dmFocAnnualAmpVOPagedResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnualAmp1, DmFocAnnualAmpVO dmFocAnnualAmp2) {
                    String weightRate1 = dmFocAnnualAmp1.getWeightRate();
                    String weightRate2 = dmFocAnnualAmp2.getWeightRate();
                    return commonAmpService.compareWeightColumn(weightRate1, weightRate2);
                }
            });
        } else {
            Collections.sort(dmFocAnnualAmpVOPagedResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnualAmp1, DmFocAnnualAmpVO dmFocAnnualAmp2) {
                    String weightRate1 = dmFocAnnualAmp1.getWeightRate();
                    String weightRate2 = dmFocAnnualAmp2.getWeightRate();
                    return commonAmpService.compareWeightColumn(weightRate2, weightRate1);
                }
            });
        }
    }

    private void groupCnNameOrder(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult, AnnualAnalysisVO annualAnalysisVO) {
        if ("asc".equals(annualAnalysisVO.getOrderMethod())) {
            Collections.sort(dmFocAnnualAmpVOPagedResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnualAmp1, DmFocAnnualAmpVO dmFocAnnualAmp2) {
                    return commonAmpService.sortByGroupCnName(dmFocAnnualAmp1, dmFocAnnualAmp2);
                }
            });
        } else {
            Collections.sort(dmFocAnnualAmpVOPagedResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnualAmp1, DmFocAnnualAmpVO dmFocAnnualAmp2) {
                    return commonAmpService.sortByGroupCnName(dmFocAnnualAmp2, dmFocAnnualAmp1);
                }
            });
        }
    }

    private void groupCodeOrder(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult, AnnualAnalysisVO annualAnalysisVO) {
        if ("asc".equals(annualAnalysisVO.getOrderMethod())) {
            Collections.sort(dmFocAnnualAmpVOPagedResult, Comparator.comparing(DmFocAnnualAmpVO::getGroupCode));
        } else {
            Collections.sort(dmFocAnnualAmpVOPagedResult, Comparator.comparing(DmFocAnnualAmpVO::getGroupCode).reversed());
        }
    }

    private void annualAmpOrder(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult, AnnualAnalysisVO annualAnalysisVO) {
        if ("asc".equals(annualAnalysisVO.getOrderMethod())) {
            Collections.sort(dmFocAnnualAmpVOPagedResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnualAmp1, DmFocAnnualAmpVO dmFocAnnualAmp2) {
                    String annualAmp1 = dmFocAnnualAmp1.getAnnualAmp();
                    String annualAmp2 = dmFocAnnualAmp2.getAnnualAmp();
                    return commonAmpService.compareWeightColumn(annualAmp1, annualAmp2);
                }
            });
        } else {
            Collections.sort(dmFocAnnualAmpVOPagedResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnualAmp1, DmFocAnnualAmpVO dmFocAnnualAmp2) {
                    String annualAmp1 = dmFocAnnualAmp1.getAnnualAmp();
                    String annualAmp2 = dmFocAnnualAmp2.getAnnualAmp();
                    return commonAmpService.compareWeightColumn(annualAmp2, annualAmp1);
                }
            });
        }
    }

    private String subPercentStr(String str) {
        if (str.contains("%")) {
            int index = str.indexOf("%");
            return str.substring(0, index);
        }
        return str;
    }

    private void orderWeightAnnualAmpPercent(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult, AnnualAnalysisVO annualAnalysisVO, boolean flag) {
        if (GroupLevelEnumU.ITEM.getValue().equals(annualAnalysisVO.getGroupLevel())) {
            Collections.sort(dmFocAnnualAmpVOPagedResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnualAmp1, DmFocAnnualAmpVO dmFocAnnualAmp2) {
                    String weightAmpPercent1 = dmFocAnnualAmp1.getWeightAnnualAmpPercentOrder();
                    String weightAmpPercent2 = dmFocAnnualAmp2.getWeightAnnualAmpPercentOrder();
                    if (flag) {
                        return commonAmpService.compareWeightColumn(weightAmpPercent1, weightAmpPercent2);
                    }
                    return commonAmpService.compareWeightColumn(weightAmpPercent2, weightAmpPercent1);
                }
            });
        } else {
            // 权重*涨跌 排序
            Collections.sort(dmFocAnnualAmpVOPagedResult, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnualAmp1, DmFocAnnualAmpVO dmFocAnnualAmp2) {
                    String weightAmpPercent1 = dmFocAnnualAmp1.getWeightAnnualAmpPercent();
                    String weightAmpPercent2 = dmFocAnnualAmp2.getWeightAnnualAmpPercent();
                    if (flag) {
                        return commonAmpService.compareWeightColumn(weightAmpPercent1, weightAmpPercent2);
                    }
                    return commonAmpService.compareWeightColumn(weightAmpPercent2, weightAmpPercent1);
                }
            });
        }
    }

    private void exportAnnualDetail(AnnualAnalysisVO annualAnalysisVO, HttpServletResponse response) throws ApplicationException, IOException, InterruptedException {
        Timestamp creationDate = new Timestamp(System.currentTimeMillis());
        String sName = "";
        List<Map> list = new ArrayList<>();
        List<ExportExcelVo> exportExcelVoList = new ArrayList<>();
        // 获取最新的version_id
        DmFocVersionInfoDTO annualVersion = dmFocVersionDao.findAnnualVersion(annualAnalysisVO.getTablePreFix());
        annualAnalysisVO.setVersionId(annualVersion.getVersionId());
        String fileName = annualAnalysisVO.getFileName();
        // 判断有几个sheet页
        int sheetIndex = judgeSheetIndex(annualAnalysisVO);
        int typeNum = 1;
        if (!annualAnalysisVO.getIsShowHistogramChart()) {
            typeNum = 2;
        }
        boolean totalFlag = false;
        String sheetName;
        for (int type = typeNum; type <= sheetIndex; type++) {
            if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(annualAnalysisVO.getCostType())) {
                totalFlag = true;
                sheetName = getTotalSheetName(type);
            } else {
                sheetName = getSheetName(type, annualAnalysisVO);
            }
            // 获取第一行title
            List<AbstractExcelTitleVO> formsVO = getFormsVO(annualAnalysisVO, type, sheetName, totalFlag);
            // 获取数据
            Map headerAndList = getHeaderAndList(annualAnalysisVO, type, sheetName, totalFlag);
            List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
            List<AbstractExcelTitleVO> threeTitleList = new ArrayList<>();
            List<AbstractExcelTitleVO> fourTitleList = new ArrayList<>();
            List<AbstractExcelTitleVO> fiveTitleList = new ArrayList<>();
            List<AbstractExcelTitleVO> sixTitleList = new ArrayList<>();
            List<AbstractExcelTitleVO> sevenTitleList = new ArrayList<>();
            Set<String> titles = new LinkedHashSet<>();
            List<HeaderVo> headers = new ArrayList<>();
            if (headerAndList.get("dataList") instanceof List
                    && headerAndList.get("sheetName") instanceof String
                    && headerAndList.get("headers") instanceof List) {
                list = (List<Map>) headerAndList.get("dataList");
                sName = (String) headerAndList.get("sheetName");
                headers = (List<HeaderVo>) headerAndList.get("headers");
            }
            // 设置第二行表头模型
            setHeader(titleVoList, headers, titles, annualAnalysisVO);
            setTitles(threeTitleList, fourTitleList, fiveTitleList, sixTitleList, sevenTitleList, annualAnalysisVO);
            ExportExcelVo exportExcelVo = ExportExcelVo.builder().formInfoVo(formsVO).titleVoList(titleVoList).list(list).sheetName(sName)
                    .fileName(fileName).threeTitleList(threeTitleList).fourTitleList(fourTitleList).fiveTitleList(fiveTitleList)
                    .sixTitleList(sixTitleList).sevenTitleList(sevenTitleList).mergeCell(true).build();
            exportExcelVoList.add(exportExcelVo);
        }
        DmFoiImpExpRecordVO expRecordVO = excelUtil.expSelectColumnExcel(exportExcelVoList, response);
        // 插入导出数据到数据库
        setParamUserCenter(annualAnalysisVO, expRecordVO, creationDate);
        statisticsExcelService.insertExportExcelRecord(expRecordVO);
    }

    private void setParamUserCenter(AnnualAnalysisVO annualAnalysisVO, DmFoiImpExpRecordVO expRecordVO, Timestamp creationDate) {
        if (IndustryConst.INDUSTRY_ORG.ICT.getValue().equals(annualAnalysisVO.getIndustryOrg())) {
            expRecordVO.setModuleType(IndustryConst.INDUSTRY_ORG_PAGE.ICT_MODEL.getValue() + ModuleEnum.SAME_CODE_ANNUAL.getCnName()
                    + "(" + IndustryIndexEnum.getCostType(annualAnalysisVO.getCostType()).getDesc() + ")");
        } else if (IndustryConst.INDUSTRY_ORG.ENERGY.getValue().equals(annualAnalysisVO.getIndustryOrg())) {
            expRecordVO.setModuleType(IndustryConst.INDUSTRY_ORG_PAGE.ENERGY_MODEL.getValue() + ModuleEnum.ANNUAL.getCnName()
                    + "(" + IndustryIndexEnum.getCostType(annualAnalysisVO.getCostType()).getDesc() + ")");
        } else {
            expRecordVO.setModuleType(IndustryConst.INDUSTRY_ORG_PAGE.IAS_MODEL.getValue() + ModuleEnum.ANNUAL.getCnName()
                    + "(" + IndustryIndexEnum.getCostType(annualAnalysisVO.getCostType()).getDesc() + ")");
        }
        expRecordVO.setCreationDate(creationDate);
        expRecordVO.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
    }

    private int judgeSheetIndex(AnnualAnalysisVO annualAnalysisVO) {
        int sheetIndex = 3;
        if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(annualAnalysisVO.getCostType())) {
            sheetIndex = 4;
        }
        if (!annualAnalysisVO.getIsTotalChildChart()) {
            sheetIndex = 2;
        }
        if (!annualAnalysisVO.getIsShowChildContent()) {
            sheetIndex = 1;
        }
        return sheetIndex;
    }

    private void setHeader(List<AbstractExcelTitleVO> titleVoList, List<HeaderVo> headers, Set<String> titles, AnnualAnalysisVO annualAnalysisVO) {
        LeafExcelTitleVO column = null;
        for (HeaderVo header : headers) {
            column = new LeafExcelTitleVO(header.getTitle(), header.getWidth(), true, header.getField(),
                    header.getField(), header.getDataType(), header.getIsEditable());
            titleVoList.add(column);
            titles.add(header.getField());
        }
        LeafExcelTitleVO costType = new LeafExcelTitleVO("成本类型：" + IndustryIndexEnum.getCostType(annualAnalysisVO.getCostType()).getDesc(), 14 * 580, true, "overSeaFlagValue",
                "costType", CellType.STRING, true);
        titleVoList.add(costType);
    }

    private void setTitles(List<AbstractExcelTitleVO> threeTitleList, List<AbstractExcelTitleVO> fourTitleList, List<AbstractExcelTitleVO> fiveTitleList, List<AbstractExcelTitleVO> sixTitleList, List<AbstractExcelTitleVO> sevenTitleList, AnnualAnalysisVO annualAnalysisVO) throws ApplicationException {
        // 获取本期数据截止月份
        String actualMonth = iDmFocMonthCostIdxDao.findActualMonth(annualAnalysisVO.getTablePreFix());
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setViewFlag(annualAnalysisVO.getViewFlag());
        dmFocViewInfoVO.setGranularityType(annualAnalysisVO.getGranularityType());
        dmFocViewInfoVO.setCostType(annualAnalysisVO.getCostType());
        dmFocViewInfoVO.setIndustryOrg(annualAnalysisVO.getIndustryOrg());
        commonService.setViewFlagValueWithLookUp(dmFocViewInfoVO);

        if (IndustryIndexEnum.GRANULARITY_TYPE.UNIVERSAL.getValue().equals(annualAnalysisVO.getGranularityType())) {
            LeafExcelTitleVO granuleValue = new LeafExcelTitleVO("颗粒度：通用", CommonConstant.WIDTH, true,
                    "granule", "granule", CellType.STRING, false);
            threeTitleList.add(granuleValue);
        } else if (IndustryIndexEnum.GRANULARITY_TYPE.PROFITS.getValue().equals(annualAnalysisVO.getGranularityType())) {
            LeafExcelTitleVO granuleValue = new LeafExcelTitleVO("颗粒度：盈利", CommonConstant.WIDTH, true,
                    "granule", "granule", CellType.STRING, false);
            threeTitleList.add(granuleValue);
        } else {
            LeafExcelTitleVO granuleValue = new LeafExcelTitleVO("颗粒度：量纲", CommonConstant.WIDTH, true,
                    "granule", "granule", CellType.STRING, false);
            threeTitleList.add(granuleValue);
        }
        LeafExcelTitleVO overSeaFlagValue = new LeafExcelTitleVO("国内/海外: " + IndustryConst.getOverseaFlag(annualAnalysisVO.getOverseaFlag()).getDesc(), 14 * 580, true, "overSeaFlagValue",
                "overSeaFlagValue", CellType.STRING, true);
        fourTitleList.add(overSeaFlagValue);

        List<DmFocViewInfoVO> bgInfo = getBgList(annualAnalysisVO);
        LeafExcelTitleVO bgFlagValue = new LeafExcelTitleVO("BG: " + bgInfo.get(0).getLv0ProdListCnName(), 14 * 580, true, "bgFlagValue",
                "bgFlagValue", CellType.STRING, true);
        fiveTitleList.add(bgFlagValue);

        LeafExcelTitleVO columnViewFlagValue = new LeafExcelTitleVO("视角: " + dmFocViewInfoVO.getViewFlagValue(), 14 * 580, true, "viewFlagValue",
                "viewFlagValue", CellType.STRING, true);
        sixTitleList.add(columnViewFlagValue);

        LeafExcelTitleVO columnActualMonth = new LeafExcelTitleVO(actualMonth, CommonConstant.WIDTH, true,
                "actualMonth", "actualMonth", CellType.STRING, true);
        sevenTitleList.add(columnActualMonth);
    }

    private List<DmFocViewInfoVO> getBgList(AnnualAnalysisVO annualAnalysisVO) {
        CommonViewVO commonViewVO = new CommonViewVO();
        commonViewVO.setGranularityType(annualAnalysisVO.getGranularityType());
        commonViewVO.setViewFlag(annualAnalysisVO.getViewFlag());
        commonViewVO.setCaliberFlag(annualAnalysisVO.getCaliberFlag());
        commonViewVO.setLv0ProdListCode(annualAnalysisVO.getLv0ProdListCode());
        commonViewVO.setCostType(annualAnalysisVO.getCostType());
        commonViewVO.setIndustryOrg(annualAnalysisVO.getIndustryOrg());
        ResultDataVO bgInfoList = annualCommonService.getBgInfoList(commonViewVO);
        return (List<DmFocViewInfoVO>) bgInfoList.getData();
    }

    // 第一行单元格样式
    private List<AbstractExcelTitleVO> getFormsVO(AnnualAnalysisVO annualAnalysisVO, int type, String sheetName, boolean totalFlag) {
        List<AbstractExcelTitleVO> formInfoVo = new ArrayList<>(2);
        String titleHeader = sheetName;
        if (totalFlag && 3 == type) {
            titleHeader = getTotalSheetName(3);
        }
        if (!totalFlag && 2 == type) {
            titleHeader = getSheetName(1, annualAnalysisVO);
        }
        LeafExcelTitleVO columnCnVersion = new LeafExcelTitleVO(titleHeader + "  " + getTitleName(annualAnalysisVO, type, totalFlag), CommonConstant.WIDTH, true,
                "title", "title", CellType.STRING, true);
        formInfoVo.add(columnCnVersion);
        if ("R".equals(annualAnalysisVO.getCaliberFlag())) {
            LeafExcelTitleVO columnCaliberFlag = new LeafExcelTitleVO("数据口径：收入", CommonConstant.WIDTH, true,
                    "caliberFlag", "caliberFlag", CellType.STRING, true);
            formInfoVo.add(columnCaliberFlag);
        } else {
            LeafExcelTitleVO columnCaliberFlag = new LeafExcelTitleVO("数据口径：发货", CommonConstant.WIDTH, true,
                    "caliberFlag", "caliberFlag", CellType.STRING, true);
            formInfoVo.add(columnCaliberFlag);
        }
        return formInfoVo;
    }

    private String getSheetName(int type, AnnualAnalysisVO annualAnalysisVO) {
        String sheetName = null;
        switch (type) {
            case 1:
                sheetName = IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType()) ? ModuleEnum.INDUSTRY_INDEX_AMP_CHART.getCnName() : ModuleEnum.MANUFACTURE_INDEX_AMP_CHART.getCnName();
                break;
            case 2:
                sheetName = IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType()) ? ModuleEnum.INDUSTRY_INDEX_AMP_CHILDREN_CHART.getCnName() : ModuleEnum.MANUFACTURE_INDEX_AMP_CHILDREN_CHART.getCnName();
                break;
            case 3:
                sheetName = IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType()) ? ModuleEnum.INDUSTRY_INDEX_AMP_VIEW.getCnName() : ModuleEnum.MANUFACTURE_INDEX_AMP_VIEW.getCnName();
                break;
            default:
                break;
        }
        return sheetName;
    }

    private String getTotalSheetName(int type) {
        String sheetName = null;
        switch (type) {
            case 1:
                sheetName = ModuleEnum.INDUSTRY_INDEX_DISTRIBUTE_CHART.getCnName();
                break;
            case 2:
                sheetName = ModuleEnum.TOTAL_INDEX_AMP_CHART.getCnName();
                break;
            case 3:
                sheetName = ModuleEnum.TOTAL_INDEX_AMP_CHILDREN_CHART.getCnName();
                break;
            case 4:
                sheetName = ModuleEnum.TOTAL_INDEX_AMP_VIEW.getCnName();
                break;
            default:
                break;
        }
        return sheetName;
    }

    private String getTitleName(AnnualAnalysisVO annualAnalysisVO, int type, boolean totalFlag) {
        String titleName = null;
        switch (type) {
            case 1:
            case 2:
            case 3:
            case 4:
                boolean currentTotalFlag = totalFlag && type == 2;
                boolean currentFlag = !totalFlag && type == 1;
                if (annualAnalysisVO.getIsCompareFlag() && (currentTotalFlag || currentFlag)) {
                    List<String> compareCnNameList = new ArrayList<>();
                    List<AnnualParamVO> annualParamList = annualAnalysisVO.getAnnualParamList();
                    annualParamList.stream().forEach(annual -> compareCnNameList.addAll(annual.getGroupCnNameList()));
                    titleName = Constant.StrEnum.COMPARE_FLAG.getValue() + "(" + compareCnNameList.stream().map(String::valueOf).collect(Collectors.joining(",")) + ")";
                } else {
                    MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
                    BeanUtils.copyProperties(annualAnalysisVO, monthAnalysisVO);
                    if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(monthAnalysisVO.getCostType())) {
                        titleName = commonService.getGroupCnName(monthAnalysisVO);
                    } else {
                        titleName = commonService.getManufactureGroupCnName(monthAnalysisVO);
                    }
                }
                break;
            default:
                break;
        }
        return titleName;
    }

    private Map getHeaderAndList(AnnualAnalysisVO annualAnalysisVO, int type, String sheetName, boolean totalFlag) throws InterruptedException {
        List<String> yearList = new ArrayList<>();
        if (annualAnalysisVO.getIsMultipleSelect()) {
            yearList.add(String.valueOf(annualAnalysisVO.getPeriodYear()));
        } else {
            yearList = annualCommonService.getThreeYears(annualAnalysisVO.getCostType(), annualAnalysisVO.getIndustryOrg());
        }
        // 获取数据表头
        List<HeaderVo> headers;
        List<Map> dataList = new ArrayList<>();
        if (totalFlag) {
            headers = getTotalDynamicHeader(yearList, type, annualAnalysisVO);
            // 查询数据库数据
            getTotalDataList(type, annualAnalysisVO, dataList);
        } else {
            headers = getDynamicHeader(yearList, type, annualAnalysisVO);
            getDataList(type, annualAnalysisVO, dataList);
        }
        HashMap map = new HashMap(5);
        map.put("sheetName", sheetName);
        map.put("headers", headers);
        map.put("dataList", dataList);
        return map;
    }

    private static final Map beanToMap(DmFocAnnualAmpVO dmFocAnnualAmpVO) {
        return JSON.parseObject(JSON.toJSONString(dmFocAnnualAmpVO), new TypeReference<Map>() {
        });
    }

    private void getDataList(int type, AnnualAnalysisVO annualAnalysisVO, List<Map> dataList) throws InterruptedException {
        List<String> threeYears = annualCommonService.getThreeYears(annualAnalysisVO.getCostType(), annualAnalysisVO.getIndustryOrg());
        // 判断是否反向视角并赋值
        isReverseViewFlag(annualAnalysisVO);
        annualAnalysisVO.setYearList(threeYears);
        switch (type) {
            case 1:
                typeCurrentAmp(annualAnalysisVO, dataList, threeYears);
                break;
            case 2:
                typeTwo(annualAnalysisVO, dataList, threeYears);
                break;
            case 3:
                typeThree(annualAnalysisVO, dataList, threeYears);
                break;
            default:
                break;
        }
    }

    private void typeCurrentAmp(AnnualAnalysisVO annualAnalysisVO, List<Map> dataList, List<String> threeYears) throws InterruptedException {
        if (annualAnalysisVO.getIsCompareFlag()) {
            IRequestContext requestContext = RequestContext.getCurrent();
            List<AnnualParamVO> annualParamList = annualAnalysisVO.getAnnualParamList();
            Executor executorPool = executorConfig.asyncServiceExecutor();
            CountDownLatch countDownLatch = new CountDownLatch(annualParamList.size());
            for (AnnualParamVO annualParamVO : annualParamList) {
                Runnable runnable = () -> {
                    RequestContextManager.setCurrent(requestContext);
                    try {
                        AnnualAnalysisVO annualVO = new AnnualAnalysisVO();
                        BeanUtils.copyProperties(annualParamVO, annualVO);
                        annualVO.setReverseSymbol(annualAnalysisVO.getReverseSymbol());
                        annualVO.setVersionId(annualAnalysisVO.getVersionId());
                        setSingleCompareTeamCodeLevel(annualVO);
                        List<Map> analysisDataList = new ArrayList<>();
                        typeOne(annualVO, analysisDataList, threeYears);
                        dataList.addAll(analysisDataList);
                    } catch (Exception exception) {
                        LOGGER.error("error typeCurrentAmp :{} ", exception.getLocalizedMessage());
                    } finally {
                        countDownLatch.countDown();
                        RequestContextManager.removeCurrent();
                        LOGGER.info("剩余线程个数：{},当前线程名称：{}", countDownLatch.getCount(), Thread.currentThread().getName());
                    }
                };
                executorPool.execute(runnable);
            }
            countDownLatch.await();
        } else {
            typeOne(annualAnalysisVO, dataList, threeYears);
        }
    }

    private void getTotalDataList(int type, AnnualAnalysisVO annualAnalysisVO, List<Map> dataList) throws InterruptedException {
        List<String> threeYears = annualCommonService.getThreeYears(annualAnalysisVO.getCostType(), annualAnalysisVO.getIndustryOrg());
        // 判断是否反向视角并赋值
        isReverseViewFlag(annualAnalysisVO);
        annualAnalysisVO.setYearList(threeYears);
        switch (type) {
            case 1:
                typeDistribute(annualAnalysisVO, dataList);
                break;
            case 2:
                typeCurrentAmp(annualAnalysisVO, dataList, threeYears);
                break;
            case 3:
                typeTwo(annualAnalysisVO, dataList, threeYears);
                break;
            case 4:
                typeThree(annualAnalysisVO, dataList, threeYears);
                break;
            default:
                break;
        }
    }

    private void typeDistribute(AnnualAnalysisVO annualAnalysisVO, List<Map> dataList) {
        Map<String, List<DmFocAnnualAmpVO>> periodYearDistributeList;
        List<DmFocAnnualAmpVO> dmFocDistributeCostList = totalAnnualAmpDao.distributeCostExcelList(annualAnalysisVO);
        setGroupCnNameDimensionLevel(dmFocDistributeCostList);
        if (!annualAnalysisVO.getIsMultipleSelect()) {
            // 单选
            periodYearDistributeList = dmFocDistributeCostList.stream().sorted(Comparator.comparing(DmFocAnnualAmpVO::getPeriodYear)).collect(Collectors.groupingBy(DmFocAnnualAmpVO::getPeriodYear, LinkedHashMap::new, Collectors.toList()));
            singleSelectDataList(dataList, periodYearDistributeList);
        } else {
            // 多选拼接名称
            mutilSelectGroupCnName(annualAnalysisVO, dmFocDistributeCostList, "all");
            HashMap<String, List<DmFocAnnualAmpVO>> resultColumnMap = dmFocDistributeCostList.stream().collect(
                    Collectors.groupingBy(result -> result.getGroupCnName() + "_" + result.getGroupCode(), HashMap::new, Collectors.toList()));
            Set<String> resultRowSet = resultColumnMap.keySet();
            multipleSelectDataList(dataList, resultRowSet, resultColumnMap);
        }
    }

    private void multipleSelectDataList(List<Map> dataList, Set<String> resultRowSet, HashMap<String, List<DmFocAnnualAmpVO>> resultColumnMap) {
        for (String keyStr : resultRowSet) {
            List<DmFocAnnualAmpVO> resultAnnualAmpVO = MapUtil.get(resultColumnMap, keyStr, List.class);
            resultAnnualAmpVO.stream().sorted(Comparator.comparing(DmFocAnnualAmpVO::getPeriodYear));
            Map<String, Object> resultMap = new HashMap<>();
            Double purchasePercentageSum = 0.0D;
            Double manufacturePercentageSum = 0.0D;
            Double purchaseRmbCostAmtSum = 0.0D;
            Double manufactureRmbCostAmtSum = 0.0D;
            Double totalRmbCostAmtSum = 0.0D;
            String groupCnName = null;
            for (int i = 0; i < resultAnnualAmpVO.size(); i++) {
                groupCnName = resultAnnualAmpVO.get(i).getGroupCnName();
                String costType = resultAnnualAmpVO.get(i).getCostType();
                if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(costType)) {
                    Double purchaseRmbCostAmt = resultAnnualAmpVO.get(i).getRmbCostAmtDouble();
                    Double percentage = resultAnnualAmpVO.get(i).getPercentageDouble();
                    purchaseRmbCostAmtSum = purchaseRmbCostAmtSum + purchaseRmbCostAmt;
                    purchasePercentageSum = purchasePercentageSum + percentage;
                }

                if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(costType)) {
                    Double manufactureRmbCostAmt = resultAnnualAmpVO.get(i).getRmbCostAmtDouble();
                    Double manufacturePercentage = resultAnnualAmpVO.get(i).getPercentageDouble();
                    manufactureRmbCostAmtSum = manufactureRmbCostAmtSum + manufactureRmbCostAmt;
                    manufacturePercentageSum = manufacturePercentageSum + manufacturePercentage;
                }
                if (IndustryIndexEnum.COST_TYPE.T.getValue().equals(costType)) {
                    Double totalRmbCostAmt = resultAnnualAmpVO.get(i).getRmbCostAmtDouble();
                    totalRmbCostAmtSum = totalRmbCostAmtSum + totalRmbCostAmt;
                }
            }
            BigDecimal purchaseDecimal = new BigDecimal(String.valueOf(purchasePercentageSum));
            String purchasePercentageStr = purchaseDecimal.setScale(1, RoundingMode.HALF_UP).toString() + "%";

            BigDecimal manufactureDecimal = new BigDecimal(String.valueOf(manufacturePercentageSum));
            String manufacturePercentageStr = manufactureDecimal.setScale(1, RoundingMode.HALF_UP).toString() + "%";

            BigDecimal purchaseRmb = new BigDecimal(String.valueOf(purchaseRmbCostAmtSum));
            BigDecimal manufactureRmb = new BigDecimal(String.valueOf(manufactureRmbCostAmtSum));
            BigDecimal totalRmb = new BigDecimal(String.valueOf(totalRmbCostAmtSum));

            String purchaseRmbStr = purchaseRmb.setScale(1, RoundingMode.HALF_UP).toString();
            String manufactureRmbStr = manufactureRmb.setScale(1, RoundingMode.HALF_UP).toString();
            String totalRmbStr = totalRmb.setScale(1, RoundingMode.HALF_UP).toString();
            resultMap.put("groupCnName", groupCnName);
            resultMap.put("purchasePercentage", purchasePercentageStr);
            resultMap.put("manufacturePercentage", manufacturePercentageStr);

            resultMap.put("purchaseRmbCostAmt", purchaseRmbStr);
            resultMap.put("manufactureRmbCostAmt", manufactureRmbStr);
            resultMap.put("totalRmbCostAmt", totalRmbStr);
            dataList.add(resultMap);
        }
    }

    private void singleSelectDataList(List<Map> dataList, Map<String, List<DmFocAnnualAmpVO>> periodYearDistributeList) {
        for (Map.Entry<String, List<DmFocAnnualAmpVO>> distributeEntry : periodYearDistributeList.entrySet()) {
            List<DmFocAnnualAmpVO> purchaseRmbCostAmtList = distributeEntry.getValue().stream().filter(dist -> "P".equals(dist.getCostType())).collect(Collectors.toList());
            List<DmFocAnnualAmpVO> manufactureRmbCostAmtList = distributeEntry.getValue().stream().filter(dist -> "M".equals(dist.getCostType())).collect(Collectors.toList());
            List<DmFocAnnualAmpVO> totalRmbCostAmtList = distributeEntry.getValue().stream().filter(dist -> "T".equals(dist.getCostType())).collect(Collectors.toList());

            Double purchaseRmbCostAmt = purchaseRmbCostAmtList.stream().collect(Collectors.summarizingDouble(DmFocAnnualAmpVO::getRmbCostAmtDouble)).getSum();
            Double manufactureRmbCostAmt = manufactureRmbCostAmtList.stream().collect(Collectors.summarizingDouble(DmFocAnnualAmpVO::getRmbCostAmtDouble)).getSum();
            Double totalRmbCostAmt = totalRmbCostAmtList.stream().collect(Collectors.summarizingDouble(DmFocAnnualAmpVO::getRmbCostAmtDouble)).getSum();

            Double purchasePercentage = purchaseRmbCostAmtList.stream().collect(Collectors.summarizingDouble(DmFocAnnualAmpVO::getPercentageDouble)).getSum();
            Double manufacturePercentage = manufactureRmbCostAmtList.stream().collect(Collectors.summarizingDouble(DmFocAnnualAmpVO::getPercentageDouble)).getSum();
            Map<String, Object> resultMap = new HashMap<>();

            resultMap.put("periodYear", distributeEntry.getKey());
            BigDecimal purchaseDecimal = new BigDecimal(String.valueOf(purchasePercentage));
            String purchasePercentageStr = purchaseDecimal.setScale(1, RoundingMode.HALF_UP).toString() + "%";
            resultMap.put("purchasePercentage", purchasePercentageStr);

            BigDecimal manufacturDecimal = new BigDecimal(String.valueOf(manufacturePercentage));
            String manufacturePercentageStr = manufacturDecimal.setScale(1, RoundingMode.HALF_UP).toString() + "%";
            resultMap.put("manufacturePercentage", manufacturePercentageStr);

            BigDecimal purchaseRmbCost = new BigDecimal(String.valueOf(purchaseRmbCostAmt));
            String purchaseRmbCostStr = purchaseRmbCost.setScale(1, RoundingMode.HALF_UP).toString();
            resultMap.put("purchaseRmbCostAmt", purchaseRmbCostStr);

            BigDecimal manufactureRmbCost = new BigDecimal(String.valueOf(manufactureRmbCostAmt));
            String manufactureRmbCostStr = manufactureRmbCost.setScale(1, RoundingMode.HALF_UP).toString();
            resultMap.put("manufactureRmbCostAmt", manufactureRmbCostStr);

            BigDecimal totalRmbCost = new BigDecimal(String.valueOf(totalRmbCostAmt));
            String totalRmbCostStr = totalRmbCost.setScale(1, RoundingMode.HALF_UP).toString();
            resultMap.put("totalRmbCostAmt", totalRmbCostStr);
            dataList.add(resultMap);
        }
    }

    private void typeOne(AnnualAnalysisVO annualAnalysisVO, List<Map> dataList, List<String> threeYears) {
        AnnualAnalysisVO annualVO = new AnnualAnalysisVO();
        BeanUtils.copyProperties(annualAnalysisVO, annualVO);

        List<DmFocAnnualAmpVO> currentAnnualAmpList = new ArrayList<>();
        if (annualVO.getIsContainComb()) {
            annualVO.setExcelExp("excel");
            currentAnnualAmpList = currentLevelCustomCombCondition(annualVO);
        } else {
            currentAnnualAmpList = currentLevelNotContainComb(annualVO);
            if (annualAnalysisVO.getIsMultipleSelect()) {
                mutilSelectGroupCnName(annualVO, currentAnnualAmpList, "all");
            }
        }
        setNoEffectiveAmp(currentAnnualAmpList, annualVO, "excel");
        for (DmFocAnnualAmpVO currentAnnualAmp : currentAnnualAmpList) {
            currentAnnualAmp.setCostSubType(IndustryIndexEnum.getCostType(currentAnnualAmp.getCostType()).getDesc());
            String periodYear = null;
            if (CollectionUtils.isNotEmpty(threeYears)) {
                int countYear = threeYears.size() - 1;
                periodYear = threeYears.get(countYear);
            }
            if (StringUtils.isNotBlank(currentAnnualAmp.getPeriodYear())) {
                if (currentAnnualAmp.getPeriodYear().equals(periodYear)) {
                    currentAnnualAmp.setPeriodYear(currentAnnualAmp.getPeriodYear() + " YTD");
                }
            }
            Map currentAnnualAmpMap = beanToMap(currentAnnualAmp);
            dataList.add(currentAnnualAmpMap);
        }
    }

    private void typeTwo(AnnualAnalysisVO annualAnalysisVO, List<Map> dataList, List<String> threeYears) {
        if (!annualAnalysisVO.getIsMultipleSelect()) {
            // 计算最近的三年
            annualAnalysisVO.setYearList(threeYears);
        } else {
            List<String> yearList = new ArrayList<>();
            yearList.add(String.valueOf(annualAnalysisVO.getPeriodYear()));
            threeYears = yearList;
        }
        AnnualAnalysisVO annualVO = new AnnualAnalysisVO();
        BeanUtils.copyProperties(annualAnalysisVO, annualVO);
        // 根据groupLevel计算子项level
        annualVO.setParentLevel(annualVO.getGroupLevel());
        String groupLevel;
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualVO.getCostType())) {
            groupLevel = FcstIndexMadeUtil.getNextGroupLevelByView(annualVO.getViewFlag(),
                    annualVO.getParentLevel(), annualVO.getGranularityType(),annualVO.getIndustryOrg());
        } else {
            groupLevel = FcstIndexUtil.getNextGroupLevelByView(annualVO.getViewFlag(),
                    annualVO.getParentLevel(), annualVO.getGranularityType(),annualVO.getIndustryOrg());
        }
        annualVO.setParentCodeList(annualVO.getGroupCodeList());
        annualVO.setGroupLevel(groupLevel);
        if (!annualVO.getIsContainComb()) {
            multiSelectSetParentCode(annualVO, groupLevel);
        }
        List<DmFocAnnualAmpVO> annualAmpChartList = new ArrayList<>();
        if (!annualVO.getIsContainComb()) {
            annualAmpChartList = multiChildNotContainsCombExcel(annualVO, groupLevel);
        } else {
            // 包含汇总组合的情况
            annualAmpChartList = multiChildContainsCombExcel(annualVO);
            if (annualVO.getIsMultipleSelect()) {
                // 判断当statuscode存在时，涨跌幅需要设置成null
                setNoEffectiveAmp(annualAmpChartList, annualVO, "excel");
                setDataCombList(dataList, threeYears, annualAmpChartList);
                return;
            }
        }
        // 判断当statuscode存在时，涨跌幅需要设置成null
        setNoEffectiveAmp(annualAmpChartList, annualVO, "excel");
        HashMap<String, List<DmFocAnnualAmpVO>> resultColumnMap = annualAmpChartList.stream().collect(
                Collectors.groupingBy(item -> getGroupKey(item), HashMap::new, Collectors.toList()));
        Set<String> resultRowSet = resultColumnMap.keySet();
        setDataList(dataList, threeYears, resultColumnMap, resultRowSet);
    }

    private void setDataCombList(List<Map> dataList, List<String> threeYears, List<DmFocAnnualAmpVO> annualAmpChartList) {
        for (int i = 0; i < annualAmpChartList.size(); i++) {
            Map<String, Object> resultMap = new HashMap<>();
            String costType = annualAmpChartList.get(i).getCostType();
            String parentCnName = annualAmpChartList.get(i).getParentCnName();
            String groupCode = annualAmpChartList.get(i).getGroupCode();
            String groupCnName = annualAmpChartList.get(i).getGroupCnName();
            String annualAmp = annualAmpChartList.get(i).getAnnualAmp();
            String periodYear = annualAmpChartList.get(i).getPeriodYear();
            resultMap.put("costSubType", IndustryIndexEnum.getCostType(costType).getDesc());
            resultMap.put("parentCnName", parentCnName);
            resultMap.put("groupCnName", groupCnName);
            if (StringUtils.isNotBlank(groupCode)) {
                resultMap.put("groupCode", groupCode);
            }
            for (int j = 0; j < threeYears.size(); j++) {
                if (threeYears.get(j).equals(periodYear)) {
                    resultMap.put("annualAmp" + j, annualAmp != null ? annualAmp : null);
                }
            }
            dataList.add(resultMap);
        }
    }

    private void setDataList(List<Map> dataList, List<String> threeYears, HashMap<String, List<DmFocAnnualAmpVO>> resultColumnMap, Set<String> resultRowSet) {
        for (String keyStr : resultRowSet) {
            List<DmFocAnnualAmpVO> resultAnnualAmpVO = MapUtil.get(resultColumnMap, keyStr, List.class);
            resultAnnualAmpVO.stream().sorted(Comparator.comparing(DmFocAnnualAmpVO::getPeriodYear));
            Map<String, Object> resultMap = new HashMap<>();
            for (int i = 0; i < resultAnnualAmpVO.size(); i++) {
                String costType = resultAnnualAmpVO.get(i).getCostType();
                String costSubType = IndustryIndexEnum.getCostType(costType).getDesc();
                String parentCnName = resultAnnualAmpVO.get(i).getParentCnName();
                String groupCode = resultAnnualAmpVO.get(i).getGroupCode();
                String groupCnName = resultAnnualAmpVO.get(i).getGroupCnName();
                String annualAmp = resultAnnualAmpVO.get(i).getAnnualAmp();
                String periodYear = resultAnnualAmpVO.get(i).getPeriodYear();
                resultMap.put("costSubType", costSubType);
                resultMap.put("parentCnName", parentCnName);
                resultMap.put("groupCnName", groupCnName);
                if (StringUtils.isNotBlank(groupCode)) {
                    resultMap.put("groupCode", groupCode);
                }
                for (int j = 0; j < threeYears.size(); j++) {
                    if (threeYears.get(j).equals(periodYear)) {
                        resultMap.put("annualAmp" + j, annualAmp != null ? annualAmp : null);
                    }
                }
            }
            dataList.add(resultMap);
        }
    }

    private void typeThree(AnnualAnalysisVO annualVO, List<Map> dataList, List<String> threeYears) {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        BeanUtils.copyProperties(annualVO, annualAnalysisVO);
        annualAnalysisVO.setParentLevel(annualAnalysisVO.getGroupLevel());
        // 根据groupLevel计算子项level
        String groupLevel;
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualVO.getCostType())) {
            groupLevel = FcstIndexMadeUtil.getNextGroupLevelByView(annualAnalysisVO.getViewFlag(),
                    annualAnalysisVO.getParentLevel(), annualAnalysisVO.getGranularityType(),annualAnalysisVO.getIndustryOrg());
        } else {
            groupLevel = FcstIndexUtil.getNextGroupLevelByView(annualAnalysisVO.getViewFlag(),
                    annualAnalysisVO.getParentLevel(), annualAnalysisVO.getGranularityType(),annualAnalysisVO.getIndustryOrg());
        }
        annualAnalysisVO.setGroupLevel(groupLevel);

        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setViewFlag(annualAnalysisVO.getViewFlag());
        monthAnalysisVO.setParentLevel(annualAnalysisVO.getParentLevel());
        monthAnalysisVO.setGroupLevel(annualAnalysisVO.getGroupLevel());
        monthAnalysisVO.setProdRndTeamCodeList(annualAnalysisVO.getTeamCodeList());
        monthAnalysisVO.setGranularityType(annualAnalysisVO.getGranularityType());
        monthAnalysisVO.setIndustryOrg(annualAnalysisVO.getIndustryOrg());
        commonService.setProdRndTeamCode(monthAnalysisVO);
        annualAnalysisVO.setTeamCodeList(monthAnalysisVO.getProdRndTeamCodeList());
        annualAnalysisVO.setParentCodeList(annualAnalysisVO.getGroupCodeList());
        List<DmFocAnnualAmpVO> annualAmpAndWeightList = new ArrayList<>();
        if (annualAnalysisVO.getIsContainComb()) {
            if (IndustryIndexEnum.COST_TYPE.P.getValue().equals(annualAnalysisVO.getCostType())) {
                annualAmpAndWeightList = industryCostListContainsComb(annualAnalysisVO);
            }
            if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
                annualAmpAndWeightList = madeIndustryCostListContainsComb(annualAnalysisVO);
            }
            annualAnalysisVO.setGroupLevel(GroupLevelEnumD.ITEM.getValue());
        } else {
            // 默认按照权重大小排序
            annualAmpAndWeightList = getAnnualAmpOrderByWeightExcel(annualAnalysisVO, annualAmpAndWeightList);
        }
        for (String year : threeYears) {
            annualAnalysisVO.setYear(year);
            // 筛选出对应年份
            List<DmFocAnnualAmpVO> annualAmpAndWeightYear = annualAmpAndWeightList.stream().filter(annual -> annualAnalysisVO.getYear().equals(annual.getPeriodYear())).collect(Collectors.toList());
            // 处理各层级的权重和权重*涨跌
            List<DmFocAnnualAmpVO> dmFocAnnualAmpResult = dealGroupLevelWeightAndAmp(annualAmpAndWeightYear, annualAnalysisVO, false, null);
            // 判断当statuscode存在时，涨跌幅需要设置成null
            setNoEffectiveAmp(dmFocAnnualAmpResult, annualAnalysisVO, "excel");
            for (DmFocAnnualAmpVO annualAmpWeight : dmFocAnnualAmpResult) {
                annualAmpWeight.setCostSubType(IndustryIndexEnum.getCostType(annualAmpWeight.getCostType()).getDesc());
                Map annualAmpWeightMap = beanToMap(annualAmpWeight);
                dataList.add(annualAmpWeightMap);
            }
        }
    }

    private List<DmFocAnnualAmpVO> dealGroupLevelWeightAndAmp(List<DmFocAnnualAmpVO> annualAmpAndWeightList, AnnualAnalysisVO annualAnalysisVO, boolean flag, PageVO pageVO) {
        sortByWeight(annualAmpAndWeightList);
        if (GroupLevelEnumU.ITEM.getValue().equals(annualAnalysisVO.getGroupLevel()) || GroupLevelEnumD.SPART.getValue().equals(annualAnalysisVO.getGroupLevel())) {
            dealItemLevelweightData(annualAmpAndWeightList, flag, annualAnalysisVO, pageVO);
            // 重新使用权重编号排序list
            Collections.sort(annualAmpAndWeightList, new Comparator<DmFocAnnualAmpVO>() {
                @Override
                public int compare(DmFocAnnualAmpVO dmFocAnnualAmp1, DmFocAnnualAmpVO dmFocAnnualAmp2) {
                    String weightRate1 = dmFocAnnualAmp1.getWeightRate();
                    String weightRate2 = dmFocAnnualAmp2.getWeightRate();
                    return commonAmpService.compareWeightColumn(weightRate1, weightRate2);
                }
            });
        }
        // 获取权重*涨跌最大值 为前端提供
        if (!GroupLevelEnumU.ITEM.getValue().equals(annualAnalysisVO.getGroupLevel()) && !GroupLevelEnumD.SPART.getValue().equals(annualAnalysisVO.getGroupLevel())) {
            getMaxWeightAnnualAmp(annualAmpAndWeightList, annualAnalysisVO);
        }
        // 拼接%，保留一位小数
        concatPercent(annualAmpAndWeightList, annualAnalysisVO);
        return annualAmpAndWeightList;
    }

    private void dealItemLevelweightData(List<DmFocAnnualAmpVO> annualAmpAndWeightList, boolean flag, AnnualAnalysisVO annualAnalysisVO, PageVO pageVO) {
        List<Integer> numList = new ArrayList<>();
        if (annualAnalysisVO.getIsContainComb() && pageVO != null) {
            int curTotal = (pageVO.getCurPage() - 1) * pageVO.getPageSize() + annualAmpAndWeightList.size();
            numList = Stream.iterate((pageVO.getCurPage() - 1) * pageVO.getPageSize() + 1, item -> item + 1).limit(curTotal).collect(Collectors.toList());
        } else {
            numList = Stream.iterate(1, item -> item + 1).limit(annualAmpAndWeightList.size()).collect(Collectors.toList());
        }
        // 权重排序
        for (int i = 0; i < annualAmpAndWeightList.size(); i++) {
            annualAmpAndWeightList.get(i).setWeightRate(numList.get(i).toString());
        }
        // 权重*涨跌 排序
        sortByWeightAnnualAmp(annualAmpAndWeightList);
        // 权重*涨跌 数据设置成序号
        encryptionWeightValue(annualAmpAndWeightList, flag, numList, annualAnalysisVO);
    }

    private void encryptionWeightValue(List<DmFocAnnualAmpVO> annualAmpAndWeightList, boolean flag, List<Integer> numList, AnnualAnalysisVO annualAnalysisVO) {
        // 获取权重*涨跌最大值
        getMaxWeightAnnualAmp(annualAmpAndWeightList, annualAnalysisVO);
        for (int i = 0; i < annualAmpAndWeightList.size(); i++) {
            if (flag) {
                annualAmpAndWeightList.get(i).setWeightAnnualAmpPercentOrder(numList.get(i).toString());
                String encryptWeightAnnualAmp = annualAmpAndWeightList.get(i).getWeightAnnualAmpPercent();
                if (StringUtils.isNotBlank(encryptWeightAnnualAmp) && !"0*".equals(encryptWeightAnnualAmp)) {
                    encryptWeightAnnualAmp = new BigDecimal(encryptWeightAnnualAmp).setScale(1, BigDecimal.ROUND_HALF_UP).toString();
                }
                annualAmpAndWeightList.get(i).setWeightAnnualAmpPercent(encryptWeightAnnualAmp);
            } else {
                annualAmpAndWeightList.get(i).setWeightAnnualAmpPercent(numList.get(i).toString());
            }
        }
    }

    private void getMaxWeightAnnualAmp(List<DmFocAnnualAmpVO> annualAmpAndWeightList, AnnualAnalysisVO annualAnalysisVO) {
        List<String> annualAmpPercentList = new ArrayList<>();
        List<String> weightAnnualAmpPercentList = annualAmpAndWeightList.stream().map(DmFocAnnualAmpVO::getWeightAnnualAmpPercent).collect(Collectors.toList());
        Double weightAnnualPercent = 0.0D;
        for (String weightAnnualAmpPercent : weightAnnualAmpPercentList) {
            if (StringUtils.isNotBlank(weightAnnualAmpPercent)) {
                weightAnnualPercent = Math.abs(Double.parseDouble(weightAnnualAmpPercent));
            }
            annualAmpPercentList.add(new BigDecimal(String.valueOf(weightAnnualPercent)).setScale(1, BigDecimal.ROUND_HALF_UP).toString());
        }
        if (CollectionUtils.isNotEmpty(annualAmpPercentList)) {
            String maxWeightAnnual = Collections.max(annualAmpPercentList);
            annualAnalysisVO.setMaxValue(maxWeightAnnual);
        }
    }

    private void sortByWeightAnnualAmp(List<DmFocAnnualAmpVO> annualAmpAndWeightList) {
        Collections.sort(annualAmpAndWeightList, new Comparator<DmFocAnnualAmpVO>() {
            @Override
            public int compare(DmFocAnnualAmpVO dmFocAnnualAmp1, DmFocAnnualAmpVO dmFocAnnualAmp2) {
                String weightAmpPercent1 = dmFocAnnualAmp1.getWeightAnnualAmpPercent();
                String weightAmpPercent2 = dmFocAnnualAmp2.getWeightAnnualAmpPercent();
                Double weightAmpPercentNum1 = null;
                Double weightAmpPercentNum2 = null;
                if (StringUtils.isNotBlank(weightAmpPercent1) && !weightAmpPercent1.equals("0*")) {
                    weightAmpPercent1 = subPercentStr(weightAmpPercent1);
                    weightAmpPercentNum1 = Double.parseDouble(weightAmpPercent1);
                }
                if (StringUtils.isNotBlank(weightAmpPercent2) && !weightAmpPercent2.equals("0*")) {
                    weightAmpPercent2 = subPercentStr(weightAmpPercent2);
                    weightAmpPercentNum2 = Double.parseDouble(weightAmpPercent2);
                }
                if (weightAmpPercentNum1 == null && weightAmpPercentNum2 == null) {
                    return 0;
                }
                if (weightAmpPercentNum1 == null) {
                    return 1;
                }
                if (weightAmpPercentNum2 == null) {
                    return -1;
                }
                if (weightAmpPercentNum1 != null && weightAmpPercentNum2 != null && ObjectUtils.notEqual(weightAmpPercentNum1, weightAmpPercentNum2)) {
                    return weightAmpPercentNum2.compareTo(weightAmpPercentNum1);
                }
                return 0;
            }
        });
    }

    /**
     * 按照权重排序
     *
     * @param annualAmpAndWeightList 参数
     */
    private void sortByWeight(List<DmFocAnnualAmpVO> annualAmpAndWeightList) {
        Collections.sort(annualAmpAndWeightList, new Comparator<DmFocAnnualAmpVO>() {
            @Override
            public int compare(DmFocAnnualAmpVO dmFocAnnualAmp1, DmFocAnnualAmpVO dmFocAnnualAmp2) {
                String weightRate1 = dmFocAnnualAmp1.getWeightRate();
                String weightRate2 = dmFocAnnualAmp2.getWeightRate();
                Double weightRateNum1 = null;
                Double weightRateNum2 = null;
                if (StringUtils.isNotBlank(weightRate1) && !weightRate1.equals("%")) {
                    weightRate1 = subPercentStr(weightRate1);
                    weightRateNum1 = Double.parseDouble(weightRate1);
                }
                if (StringUtils.isNotBlank(weightRate2) && !weightRate2.equals("%")) {
                    weightRate2 = subPercentStr(weightRate2);
                    weightRateNum2 = Double.parseDouble(weightRate2);
                }
                if (weightRateNum1 == null && weightRateNum2 == null) {
                    return commonAmpService.sortByGroupCnName(dmFocAnnualAmp1, dmFocAnnualAmp2);
                }
                if (weightRateNum1 == null) {
                    return 1;
                }
                if (weightRateNum2 == null) {
                    return -1;
                }
                if (weightRateNum1 != null && weightRateNum2 != null && ObjectUtils.notEqual(weightRateNum1, weightRateNum2)) {
                    return weightRateNum2.compareTo(weightRateNum1);
                }
                if (weightRateNum1 != null && weightRateNum2 != null && Double.toString(weightRateNum1).equals(Double.toString(weightRateNum2))) {
                    return commonAmpService.sortByGroupCnName(dmFocAnnualAmp1, dmFocAnnualAmp2);
                }
                return 0;
            }
        });
    }

    private void concatPercent(List<DmFocAnnualAmpVO> annualAmpAndWeightList, AnnualAnalysisVO annualAnalysisVO) {
        annualAmpAndWeightList.stream().forEach(annual -> {
            String annualAmp = annual.getAnnualAmp();
            String weightRate = annual.getWeightRate();
            String weightAnnualAmpPercent = annual.getWeightAnnualAmpPercent();
            if (StringUtils.isNotBlank(annualAmp) && !annualAmp.equals("0*")) {
                annual.setAnnualAmp(annualAmp + "%");
            }
            if (!GroupLevelEnumU.ITEM.getValue().equals(annualAnalysisVO.getGroupLevel()) && !GroupLevelEnumD.SPART.getValue().equals(annualAnalysisVO.getGroupLevel())) {
                if (StringUtils.isNotBlank(weightRate) && !weightRate.equals("0*")) {
                    String newWeightRate = new BigDecimal(weightRate).setScale(1, BigDecimal.ROUND_HALF_UP).toString();
                    annual.setWeightRate(newWeightRate + "%");
                }
                if (StringUtils.isNotBlank(weightAnnualAmpPercent) && !weightAnnualAmpPercent.equals("0*")) {
                    String newWeightAnnualAmpPercent = new BigDecimal(weightAnnualAmpPercent).setScale(1, BigDecimal.ROUND_HALF_UP).toString();
                    annual.setWeightAnnualAmpPercent(newWeightAnnualAmpPercent + "%");
                }
            }
        });
    }

    private String limitGroupCodePage(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult, PageVO pageVO) {
        int count = dmFocAnnualAmpVOPagedResult.size();
        pageVO.setTotalRows(count);
        int curPage = pageVO.getCurPage() - 1;
        int pageSize = pageVO.getPageSize();
        List<DmFocAnnualAmpVO> annualAmpAndWeightList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dmFocAnnualAmpVOPagedResult) && count > 0) {
            int fromIndex = curPage * pageSize;
            int totalIndex = (curPage + 1) * pageSize;
            if (totalIndex > count) {
                totalIndex = count;
            }
            annualAmpAndWeightList = dmFocAnnualAmpVOPagedResult.subList(fromIndex, totalIndex);
        }
        return annualAmpAndWeightList.stream().map(DmFocAnnualAmpVO::getGroupCode).collect(Collectors.joining(","));
    }

    private List<HeaderVo> getDynamicHeader(List<String> yearList, int type, AnnualAnalysisVO annualAnalysisVO) {
        // 动态表头设置
        List<HeaderVo> header = new LinkedList<>();
        switch (type) {
            case 1:
                header = CommonConstant.INDUSTRY_AMP_HEADER;
                break;
            case 2:
                header = getAmpByChildHeader(yearList, annualAnalysisVO);
                break;
            case 3:
                header = getAmpAndWeightByChildHeader(annualAnalysisVO);
                break;
            default:
                break;
        }
        return header;
    }

    private List<HeaderVo> getTotalDynamicHeader(List<String> yearList, int type, AnnualAnalysisVO annualAnalysisVO) {
        // 动态表头设置
        List<HeaderVo> header = new LinkedList<>();
        switch (type) {
            case 1:
                if (!annualAnalysisVO.getIsMultipleSelect()) {
                    header.addAll(CommonConstant.INDUSTRY_SHEET3_HEADER1);
                } else {
                    header.addAll(CommonConstant.INDUSTRY_DISTRIBUTE_GROUP_CN_NAME);
                }
                header.addAll(CommonConstant.INDUSTRY_DISTRIBUTE_HEADER);
                break;
            case 2:
                header = CommonConstant.INDUSTRY_AMP_HEADER;
                break;
            case 3:
                header = getAmpByChildHeader(yearList, annualAnalysisVO);
                break;
            case 4:
                header = getAmpAndWeightByChildHeader(annualAnalysisVO);
                break;
            default:
                break;
        }
        return header;
    }

    private String getGroupKey(DmFocAnnualAmpVO dmFocAnnualAmpVO) {
        return dmFocAnnualAmpVO.getCostType() + "_" + dmFocAnnualAmpVO.getCoaCode() + "_" + dmFocAnnualAmpVO.getProdRndTeamCode() + "_" + dmFocAnnualAmpVO.getProdRndTeamCnName() + "_" + dmFocAnnualAmpVO.getParentCode() + "_" + dmFocAnnualAmpVO.getParentCnName() + "_" + dmFocAnnualAmpVO.getGroupCnName() + "_" + dmFocAnnualAmpVO.getGroupCode();
    }

    private List<HeaderVo> getAmpByChildHeader(List<String> yearList, AnnualAnalysisVO annualAnalysisVO) {
        // 动态表头设置
        String periodAmpCnName;
        // 根据不同筛选条件，获得是哪个层级的表头
        List<HeaderVo> header = new LinkedList<>();
        setGroupHeader(annualAnalysisVO, header);
        List<String> threeYears = annualCommonService.getThreeYears(annualAnalysisVO.getCostType(), annualAnalysisVO.getIndustryOrg());
        String periodYear = null;
        if (CollectionUtils.isNotEmpty(threeYears)) {
            int countYear = threeYears.size() - 1;
            periodYear = threeYears.get(countYear);
        }
        for (int i = 0; i < yearList.size(); i++) {
            if (i == 2 || periodYear.equals(String.valueOf(annualAnalysisVO.getPeriodYear()))) {
                periodAmpCnName = yearList.get(i) + "年YTD" + CommonConstant.AMP_CN_NAME;
            } else {
                periodAmpCnName = yearList.get(i) + "年" + CommonConstant.AMP_CN_NAME;
            }
            String periodAmpEnName = CommonConstant.AMP_EN_NAME + i;
            header.add(new HeaderVo(periodAmpCnName, periodAmpEnName, CellType.STRING, true, 12 * 480));
        }
        return header;
    }

    private void setGroupHeader(AnnualAnalysisVO annualAnalysisVO, List<HeaderVo> header) {
        MonthAnalysisVO monthAnalysisVO = new MonthAnalysisVO();
        monthAnalysisVO.setViewFlag(annualAnalysisVO.getViewFlag());
        monthAnalysisVO.setGroupLevel(annualAnalysisVO.getGroupLevel());
        monthAnalysisVO.setGranularityType(annualAnalysisVO.getGranularityType());
        Map map;
        if (IndustryIndexEnum.COST_TYPE.M.getValue().equals(annualAnalysisVO.getCostType())) {
            map = FcstIndexMadeUtil.getNextGroupLevel(monthAnalysisVO);
        } else {
            map = FcstIndexUtil.getNextGroupLevel(monthAnalysisVO);
        }
        String name = map.get("nextGroupName").toString();
        if (annualAnalysisVO.getIsContainComb()) {
            header.add(new HeaderVo("成本类型", "costSubType", CellType.STRING, true, 12 * 480));
            header.add(new HeaderVo("上层级名称", "parentCnName", CellType.STRING, true, 12 * 480));
            header.add(new HeaderVo("ITEM名称", "groupCnName", CellType.STRING, true, 12 * 480));
            header.add(new HeaderVo("ITEM编码", "groupCode", CellType.STRING, true, 12 * 480));
        } else {
            String titleName = FcstIndexUtil.getTitle(Constant.StrEnum.HEAP_TITLE2.getValue(), name);
            String titleCode = FcstIndexUtil.getTitle(Constant.StrEnum.HEAP_TITLE3.getValue(), name);
            header.add(new HeaderVo("成本类型", "costSubType", CellType.STRING, true, 12 * 480));
            header.add(new HeaderVo("上层级名称", "parentCnName", CellType.STRING, true, 12 * 480));
            header.add(new HeaderVo(titleName, "groupCnName", CellType.STRING, true, 12 * 480));
            if (GroupLevelEnumU.CATEGORY.getValue().equals(map.get("nextGroupLevel").toString())
                    || GroupLevelEnumU.ITEM.getValue().equals(map.get("nextGroupLevel").toString())) {
                header.add(new HeaderVo(titleCode, "groupCode", CellType.STRING, true, 12 * 480));
            }
        }
    }

    private List<HeaderVo> getAmpAndWeightByChildHeader(AnnualAnalysisVO annualAnalysisVO) {
        // 动态表头设置
        // 根据不同筛选条件，获得是哪个层级的表头
        List<HeaderVo> header = new LinkedList<>();
        header.addAll(CommonConstant.INDUSTRY_SHEET3_HEADER1);
        setGroupHeader(annualAnalysisVO, header);
        if (GroupLevelEnumU.CATEGORY.getValue().equals(annualAnalysisVO.getGroupLevel()) || GroupLevelEnumMadeU.MANUFACTURE_OBJECT.getValue().equals(annualAnalysisVO.getGroupLevel()) || annualAnalysisVO.getIsContainComb()) {
            header.addAll(CommonConstant.INDUSTRY_SHEET3_HEADER3);
        } else {
            header.addAll(CommonConstant.INDUSTRY_SHEET3_HEADER2);
        }
        return header;
    }
}
