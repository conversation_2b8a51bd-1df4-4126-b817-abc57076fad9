<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.price.dao.IAnnualAmpPriceDao">
    <resultMap type="com.huawei.it.fcst.industry.price.vo.annual.DmFocAnnualAmpVO" id="annualResultMap">
        <result property="id" column="id"/>
        <result property="versionId" column="version_id"/>
        <result property="periodYear" column="period_year"/>
        <result property="prodRndTeamCode" column="prod_rnd_team_code"/>
        <result property="prodRndTeamCode" column="prod_rnd_team_catg_code"/>
        <result property="prodRndTeamCnName" column="prod_rnd_team_cn_name"/>
        <result property="lv0ProdRndTeamCnName" column="lv0_prod_rd_team_cn_name"/>
        <result property="lv0ProdRndTeamCode" column="lv0_prod_rnd_team_code"/>
        <result property="lv1ProdRndTeamCnName" column="lv1_prod_rd_team_cn_name"/>
        <result property="lv1ProdRndTeamCode" column="lv1_prod_rnd_team_code"/>
        <result property="lv2ProdRndTeamCnName" column="lv2_prod_rd_team_cn_name"/>
        <result property="lv2ProdRndTeamCode" column="lv2_prod_rnd_team_code"/>
        <result property="lv3ProdRndTeamCnName" column="lv3_prod_rd_team_cn_name"/>
        <result property="lv3ProdRndTeamCode" column="lv3_prod_rnd_team_code"/>
        <result property="lv4ProdRndTeamCode" column="lv4_prod_rnd_team_code"/>
        <result property="lv4ProdRndTeamCnName" column="lv4_prod_rd_team_cn_name"/>
        <result property="lv0ProdRndTeamCnName" column="lv0_industry_catg_cn_name"/>
        <result property="lv0ProdRndTeamCode" column="lv0_industry_catg_code"/>
        <result property="lv1ProdRndTeamCnName" column="lv1_industry_catg_cn_name"/>
        <result property="lv1ProdRndTeamCode" column="lv1_industry_catg_code"/>
        <result property="lv2ProdRndTeamCnName" column="lv2_industry_catg_cn_name"/>
        <result property="lv2ProdRndTeamCode" column="lv2_industry_catg_code"/>
        <result property="lv3ProdRndTeamCnName" column="lv3_industry_catg_cn_name"/>
        <result property="lv3ProdRndTeamCode" column="lv3_industry_catg_code"/>
        <result property="lv4ProdRndTeamCnName" column="lv4_industry_catg_cn_name"/>
        <result property="lv4ProdRndTeamCode" column="lv4_industry_catg_code"/>
        <result property="lv0ProdRndTeamCnName" column="lv0_prod_list_cn_name"/>
        <result property="lv0ProdRndTeamCode" column="lv0_prod_list_code"/>
        <result property="lv1ProdRndTeamCnName" column="lv1_prod_list_cn_name"/>
        <result property="lv1ProdRndTeamCode" column="lv1_prod_list_code"/>
        <result property="lv2ProdRndTeamCnName" column="lv2_prod_list_cn_name"/>
        <result property="lv2ProdRndTeamCode" column="lv2_prod_list_code"/>
        <result property="lv2ProdRndTeamCode" column="lv2_industry_catg_code"/>
        <result property="lv3ProdRndTeamCnName" column="lv3_prod_list_cn_name"/>
        <result property="lv3ProdRndTeamCode" column="lv3_prod_list_code"/>
        <result property="lv4ProdRndTeamCnName" column="lv4_prod_list_cn_name"/>
        <result property="lv4ProdRndTeamCode" column="lv4_prod_list_code"/>
        <result property="parentLevel" column="parent_level"/>
        <result property="parentCode" column="parent_code"/>
        <result property="parentCnName" column="parent_cn_name"/>
        <result property="groupCode" column="group_code"/>
        <result property="groupCnName" column="group_cn_name"/>
        <result property="groupLevel" column="group_level"/>
        <result property="annualAmp" column="annual_amp"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDdate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="appendFlag" column="append_flag"/>
        <result property="weightRate" column="weight_rate"/>
        <result property="statusCode" column="status_code"/>
        <result property="weightAnnualAmpPercent" column="weightAnnualAmpPercent"/>
        <result property="appendYear" column="append_year"/>
        <result property="spartCode" column="spart_code"/>
        <result property="spartCnName" column="spart_cn_name"/>
        <result property="customCnName" column="custom_cn_name"/>
        <result property="customId" column="custom_id"/>
    </resultMap>
    <sql id="userPermission">
        <if test='groupLevel=="LV0" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="and amp.group_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel=="LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="and amp.group_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
    </sql>
    <select id="allAmpNormalCost" resultMap="annualResultMap">
        select distinct
        amp.group_cn_name,amp.period_year,amp.group_level,amp.group_code,
        CONCAT(ROUND(amp.annual_amp*100,1),'%') as annual_amp,
        status.status_code,status.append_year,
        <if test='isMultipleSelect == false'>
            weight.weight_rate*100 AS weight_rate
        </if>
        <if test='isMultipleSelect == true'>
            weight.absolute_weight*100 AS weight_rate
        </if>
        from fin_dm_opt_foi.DM_FCST_PRICE_ANNL_AMP_T amp
        left join fin_dm_opt_foi.DM_FCST_PRICE_ANNL_WEIGHT_T weight
        on amp.view_flag = weight.view_flag and amp.group_code = weight.group_code
        and amp.version_id =weight.version_id and amp.period_year = weight.period_year
        and amp.prod_list_code = weight.prod_list_code
        and amp.bg_code = weight.bg_code
        and amp.group_level = weight.group_level
        and nvl(amp.parent_code,'snull') = nvl( weight.parent_code,'snull')
        and nvl(amp.region_code,'snull') = nvl( weight.region_code,'snull')
        and nvl(amp.oversea_flag,'snull') = nvl( weight.oversea_flag,'snull')
        and nvl(amp.region_code,'snull') = nvl( weight.region_code,'snull')
        and nvl(amp.repoffice_code,'snull') = nvl( weight.repoffice_code,'snull')
        and nvl(amp.sign_top_cust_category_code,'snull') = nvl( weight.sign_top_cust_category_code,'snull')
        and nvl(amp.sign_subsidiary_custcatg_cn_name,'snull') = nvl( weight.sign_subsidiary_custcatg_cn_name,'snull')
        left join fin_dm_opt_foi.DM_FCST_PRICE_ANNL_STATUS_T status
        on amp.group_code = status.group_code and amp.group_level = status.group_level
        and amp.period_year = status.period_year and amp.version_id = status.version_id
        and amp.view_flag = status.view_flag
        and amp.parent_code = status.parent_code
        and amp.oversea_flag = status.oversea_flag
        and amp.bg_code = status.bg_code
        and nvl(amp.parent_code,'snull') = nvl( status.parent_code,'snull')
        and nvl(amp.oversea_flag,'snull') = nvl( status.oversea_flag,'snull')
        and nvl(amp.region_code,'snull') = nvl( status.region_code,'snull')
        and nvl(amp.repoffice_code,'snull') = nvl( status.repoffice_code,'snull')
        and nvl ( amp.sign_top_cust_category_code, 'snull' ) = nvl ( status.sign_top_cust_category_code, 'snull' )
        and nvl ( amp.sign_subsidiary_custcatg_cn_name, 'snull' ) = nvl ( status.sign_subsidiary_custcatg_cn_name, 'snull' )
        where amp.del_flag = 'N'
        <if test='overseaFlag != null and overseaFlag != ""'>
            and amp.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and amp.bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and amp.region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and amp.repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and amp.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and amp.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='signTopCustCategoryCode != null and signTopCustCategoryCode != ""'>
            and amp.sign_top_cust_category_code = #{signTopCustCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='signSubsidiaryCustcatgCnName != null and signSubsidiaryCustcatgCnName != ""'>
            and amp.sign_subsidiary_custcatg_cn_name = #{signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
        </if>
        <if test='groupCodeList != null and groupCodeList.size() > 0'>
            <foreach collection='groupCodeList' item="code" open="AND amp.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='parentCodeList != null and parentCodeList.size() > 0'>
            <foreach collection='parentCodeList' item="code" open="AND amp.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='periodYear != null and periodYear != ""'>
            and amp.period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and amp.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND amp.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
       <include refid="userPermission"></include>
        order by amp.period_year,weight_rate desc
    </select>

    <select id="getPeriodYearList" resultType="java.lang.String">
        select distinct period_year
        from fin_dm_opt_foi.DM_FCST_PRICE_ANNL_AMP_T where version_id = #{versionId,jdbcType=NUMERIC}
        order by period_year desc limit 3
    </select>

    <select id="findAmpGroupCodeOrderByWeight" resultMap="annualResultMap">
        select group_code, group_cn_name,
        <if test='isMultipleSelect == false'>
            sum(weight_rate) weight_rate
        </if>
        <if test='isMultipleSelect == true'>
            sum(absolute_weight) weight_rate
        </if>
        from fin_dm_opt_foi.DM_FCST_PRICE_ANNL_WEIGHT_T
        where del_flag = 'N'
        <if test='overseaFlag != null and overseaFlag != ""'>
            and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='signTopCustCategoryCode != null and signTopCustCategoryCode != ""'>
            and sign_top_cust_category_code = #{signTopCustCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='signSubsidiaryCustcatgCnName != null and signSubsidiaryCustcatgCnName != ""'>
            and sign_subsidiary_custcatg_cn_name = #{signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and version_id = #{versionId}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='year != null and year != ""'>
            and period_year = #{year,jdbcType=VARCHAR}
        </if>
        <if test='parentCodeList != null and parentCodeList.size() > 0'>
            <foreach collection='parentCodeList' item="code" open="AND parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        GROUP BY group_code,group_cn_name
        ORDER BY weight_rate DESC,group_cn_name
    </select>

    <select id="multiAmpNormalChart" resultMap="annualResultMap">
        select amp.parent_code,amp.parent_cn_name,
        amp.period_year,
        CONCAT(ROUND( SUM ( amp.annual_amp*100 ), 1 ),'%') AS annual_amp,
        amp.group_code, amp.group_cn_name, amp.group_level,
        <if test='isMultipleSelect == false'>
            CONCAT(ROUND(SUM ( weight.weight_rate*100 ),1),'%') AS weight_rate,
        </if>
        <if test='isMultipleSelect == true'>
            CONCAT(ROUND(SUM ( weight.absolute_weight*100 ),1),'%') AS weight_rate,
        </if>
        status.status_code,weight.append_flag, max(status.append_year) AS append_year
        from fin_dm_opt_foi.DM_FCST_PRICE_ANNL_AMP_T amp
        left join fin_dm_opt_foi.DM_FCST_PRICE_ANNL_WEIGHT_T weight
        on amp.view_flag = weight.view_flag and amp.group_code = weight.group_code
        and amp.group_level = weight.group_level
        and amp.parent_code = weight.parent_code
        and amp.version_id =weight.version_id
        and amp.bg_code = weight.bg_code
        and weight.period_year = #{year}
        and nvl(amp.oversea_flag,'snull') = nvl( weight.oversea_flag,'snull')
        and nvl(amp.region_code,'snull') = nvl( weight.region_code,'snull')
        and nvl(amp.repoffice_code,'snull') = nvl( weight.repoffice_code,'snull')
        and nvl(amp.sign_top_cust_category_code,'snull') = nvl( weight.sign_top_cust_category_code,'snull')
        and nvl(amp.sign_subsidiary_custcatg_cn_name,'snull') = nvl( weight.sign_subsidiary_custcatg_cn_name,'snull')
        left join fin_dm_opt_foi.DM_FCST_PRICE_ANNL_STATUS_T status
        on amp.group_code = status.group_code and amp.group_level = status.group_level
        and amp.period_year = status.period_year and amp.version_id = status.version_id
        and amp.view_flag = status.view_flag
        and amp.parent_code = status.parent_code
        and amp.bg_code = status.bg_code
        and nvl(amp.oversea_flag,'snull') = nvl( status.oversea_flag,'snull')
        and nvl(amp.region_code,'snull') = nvl( status.region_code,'snull')
        and nvl(amp.repoffice_code,'snull') = nvl( status.repoffice_code,'snull')
        and nvl(amp.sign_top_cust_category_code,'snull') = nvl( status.sign_top_cust_category_code,'snull')
        and nvl(amp.sign_subsidiary_custcatg_cn_name,'snull') = nvl( status.sign_subsidiary_custcatg_cn_name,'snull')
        where amp.del_flag = 'N'
        <if test='overseaFlag != null and overseaFlag != ""'>
            and amp.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and amp.bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and amp.region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and amp.repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='signTopCustCategoryCode != null and signTopCustCategoryCode != ""'>
            and amp.sign_top_cust_category_code = #{signTopCustCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='signSubsidiaryCustcatgCnName != null and signSubsidiaryCustcatgCnName != ""'>
            and amp.sign_subsidiary_custcatg_cn_name = #{signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and amp.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and amp.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='periodYear != null and periodYear != ""'>
            and amp.period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and amp.version_id = #{versionId}
        </if>
        <if test='parentCodeList != null and parentCodeList.size() > 0'>
            <foreach collection='parentCodeList' item="code" open="AND amp.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND amp.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        <if test='groupCodeOrder != null and groupCodeOrder != ""'>
            <foreach collection="groupCodeOrder.split(',')" item="item" open="and amp.group_code IN (" close=")"
                     index="index" separator=",">
                #{item}
            </foreach>
        </if>
        <include refid="userPermission"></include>
        GROUP BY amp.parent_code,amp.parent_cn_name,weight.append_flag,
        amp.group_level,amp.group_code,amp.group_cn_name,amp.period_year,status.status_code
        order by locate(amp.group_code, #{groupCodeOrder}), amp.period_year
    </select>

    <select id="industryNormalCostList" resultMap="annualResultMap">
        select
        parent_code,parent_cn_name,period_year,annual_amp,group_code,group_level,group_cn_name,
        weight_rate, status_code, annual_amp * weight_rate/100 as weightAnnualAmpPercent, append_year,
        append_flag from
        (select amp.group_level,
        amp.parent_code,amp.parent_cn_name,amp.period_year,
        ROUND(sum(amp.annual_amp*100),1) as annual_amp,amp.group_code,
        amp.group_cn_name,
        <choose>
            <when test='isMultipleSelect == true'>
                sum(weight.absolute_weight*100) as weight_rate,
            </when>
            <otherwise>
                sum(weight.weight_rate*100) as weight_rate,
            </otherwise>
        </choose>
        status.status_code as status_code,weight.append_flag, status.append_year
        from fin_dm_opt_foi.DM_FCST_PRICE_ANNL_AMP_T amp
        left join fin_dm_opt_foi.DM_FCST_PRICE_ANNL_WEIGHT_T weight
        on amp.view_flag = weight.view_flag and amp.group_code = weight.group_code
        and amp.group_level = weight.group_level
        and amp.parent_code = weight.parent_code
        and amp.version_id =weight.version_id
        and amp.bg_code = weight.bg_code
        and nvl(amp.oversea_flag,'snull') = nvl( weight.oversea_flag,'snull')
        and nvl(amp.region_code,'snull') = nvl( weight.region_code,'snull')
        and nvl(amp.repoffice_code,'snull') = nvl( weight.repoffice_code,'snull')
        and nvl(amp.sign_top_cust_category_code,'snull') = nvl( weight.sign_top_cust_category_code,'snull')
        and nvl(amp.sign_subsidiary_custcatg_cn_name,'snull') = nvl( weight.sign_subsidiary_custcatg_cn_name,'snull')
        and weight.period_year = #{lastYear}
        left join fin_dm_opt_foi.DM_FCST_PRICE_ANNL_STATUS_T status
        on amp.group_code = status.group_code and amp.group_level = status.group_level
        and amp.period_year = status.period_year and amp.version_id = status.version_id
        and amp.view_flag = status.view_flag
        and amp.parent_code = status.parent_code
        and amp.bg_code = status.bg_code
        and nvl(amp.oversea_flag,'snull') = nvl( status.oversea_flag,'snull')
        and nvl(amp.region_code,'snull') = nvl( status.region_code,'snull')
        and nvl(amp.repoffice_code,'snull') = nvl( status.repoffice_code,'snull')
        and nvl(amp.sign_top_cust_category_code,'snull') = nvl( status.sign_top_cust_category_code,'snull')
        and nvl(amp.sign_subsidiary_custcatg_cn_name,'snull') = nvl( status.sign_subsidiary_custcatg_cn_name,'snull')
        where amp.del_flag = 'N'
        <if test='overseaFlag != null and overseaFlag != ""'>
            and amp.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and amp.bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='signTopCustCategoryCode != null and signTopCustCategoryCode != ""'>
            and amp.sign_top_cust_category_code = #{signTopCustCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='signSubsidiaryCustcatgCnName != null and signSubsidiaryCustcatgCnName != ""'>
            and amp.sign_subsidiary_custcatg_cn_name = #{signSubsidiaryCustcatgCnName,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and amp.region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and amp.repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and amp.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and amp.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and amp.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='periodYear != null and periodYear !=""'>
            and amp.period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='year != null and year !=""'>
            and amp.period_year = #{year,jdbcType=VARCHAR}
        </if>
        <if test='parentCodeList != null and parentCodeList.size() > 0'>
            <foreach collection='parentCodeList' item="code" open="AND amp.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND amp.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        <include refid="userPermission"></include>
        GROUP BY
        amp.group_level,amp.parent_code,amp.parent_cn_name,amp.period_year,
        amp.group_code,amp.group_cn_name,
        status.status_code,weight.append_flag, status.append_year
        )
        order by period_year,weight_rate desc,group_cn_name
    </select>

</mapper>