/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.config;

import com.huawei.it.fcst.vo.BaseVO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * CodeReplacementVO
 *
 * @since 2024-07-04
 */

@Setter
@Getter
@NoArgsConstructor
public class CostReductVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -1576704097209028139L;

    /**
     * 会计期
     */
    private String periodId;


    private String versionId;

    /**
     * L1
     */
    private String lv1ProdRndTeamCode;

    /**
     * L1 name
     */
    private String lv1ProdRdTeamCnName;

    /**
     * L2
     */
    private String lv2ProdRndTeamCode;

    /**
     * L2 name
     */
    private String lv2ProdRdTeamCnName;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 目标值
     */
    private String objective;

    private String groupLevel;
    @Override
    public String toString() {
        return lv1ProdRndTeamCode + lv2ProdRndTeamCode + lv1ProdRdTeamCnName + lv2ProdRdTeamCnName + periodId;
    }

    private CostReductVO oldData;

        public String getStringInfo() {
            return "CostReductVO{" + "periodId='" + periodId + '\'' + ", lv1ProdRndTeamCode='" + lv1ProdRndTeamCode
                + '\'' + ", lv1ProdRdTeamCnName='" + lv1ProdRdTeamCnName + '\'' + ", lv2ProdRndTeamCode='"
                + lv2ProdRndTeamCode + '\'' + ", lv2ProdRdTeamCnName='" + lv2ProdRdTeamCnName + '\'' + ", objective='"
                + objective + '\'' + '}';
        }
}
