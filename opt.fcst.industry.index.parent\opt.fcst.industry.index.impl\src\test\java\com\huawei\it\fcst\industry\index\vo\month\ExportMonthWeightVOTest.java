/*
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.month;

import com.huawei.it.fcst.industry.index.vo.BaseVOCoverUtilsTest;

import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @since 2023/5/9
 */
public class ExportMonthWeightVOTest extends BaseVOCoverUtilsTest<ExportMonthWeightVO> {
    @Override
    protected Class<ExportMonthWeightVO> getTClass() { return ExportMonthWeightVO.class; }

    @Test
    public void testMethod() {
        ExportMonthWeightVO dmFocActualCostVO = new ExportMonthWeightVO();
        dmFocActualCostVO.setVersionId(100L);
        dmFocActualCostVO.getVersionId();
        dmFocActualCostVO.setWeightPercent("55");
        dmFocActualCostVO.getWeightPercent();
        dmFocActualCostVO.setWeightRate(11D);
        dmFocActualCostVO.getWeightRate();
        dmFocActualCostVO.setGroupCnName("exp");
        dmFocActualCostVO.getGroupCnName();
        dmFocActualCostVO.setId(4L);
        dmFocActualCostVO.getId();
        dmFocActualCostVO.getCostType();
        dmFocActualCostVO.setCostType("11");
        dmFocActualCostVO.getParentCnName();
        dmFocActualCostVO.setParentCnName("666");
        ExportMonthWeightVO.builder().weightRate(11.1)
            .costType("11").groupCnName("22").id(15L).parentCnName("pa")
            .versionId(66L).weightPercent("99").
            build();
        Assert.assertNotNull(dmFocActualCostVO);
    }
}