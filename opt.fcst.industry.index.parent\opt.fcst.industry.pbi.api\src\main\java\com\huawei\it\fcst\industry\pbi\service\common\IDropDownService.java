/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.service.common;

import com.huawei.it.fcst.industry.pbi.vo.common.CommonBaseVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.CodeReplInfoVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.InterLockInfoVO;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * <AUTHOR>
 * @since 2024/6/26
 */
@Path("/dropDown")
@Produces(MediaType.APPLICATION_JSON + ";charset=UTF-8")
public interface IDropDownService {

    /**
     * BG下拉框
     *
     * @param commonBaseVO 参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/bgInfo/list")
    ResultDataVO getBgInfoList(CodeReplInfoVO commonBaseVO) throws CommonApplicationException;

    /**
     * 地区部
     *
     * @param commonBaseVO 参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/regionCode/list")
    ResultDataVO regionCodeList(CodeReplInfoVO commonBaseVO) throws CommonApplicationException;

    /**
     * 代表处
     *
     * @param commonBaseVO 参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/repofficeCode/list")
    ResultDataVO repofficeCodeList(CodeReplInfoVO commonBaseVO) throws CommonApplicationException;


    /**
     * 国内/海外
     *
     * @param commonBaseVO 参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/overseaFlag/list")
    ResultDataVO overseaFlagList(CodeReplInfoVO commonBaseVO) throws CommonApplicationException;

    /**
     * 主力编码列表
     *
     * @param commonBaseVO 参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/mainFlag/list")
    ResultDataVO mainFlagList(CodeReplInfoVO commonBaseVO) throws CommonApplicationException;

    /**
     * 年度月度各层级下拉框
     *
     * @param commonBaseVO 参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/list")
    ResultDataVO dropDownList(CommonBaseVO commonBaseVO) throws CommonApplicationException;

    /**
     * 编码替代各层级下拉框
     *
     * @param codeReplInfoVO 参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/codeReplace/list")
    ResultDataVO dropDownCodeReplaceList(CodeReplInfoVO codeReplInfoVO) throws CommonApplicationException;

    /**
     * 勾稽管理各层级下拉框
     *
     * @param interLockInfoVO 参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("/interLock/list")
    ResultDataVO dropDownInterLockList(InterLockInfoVO interLockInfoVO) throws CommonApplicationException;

    /**
     * 勾稽管理Spart列表
     *
     * @param interLockInfoVO 参数
     * @return ResultDataVO ResultDataVO
     */
    @POST
    @Path("interLock/spartList")
    ResultDataVO interLockSpartList(InterLockInfoVO interLockInfoVO);

    @POST
    @Path("interLock/spartValidNum")
    ResultDataVO interLockSpartVaildNum(InterLockInfoVO interLockInfoVO) throws CommonApplicationException;

    /**
     * 获取lv0编码
     *
     * @return String
     */
    @POST
    @Path("/lv0/code")
    ResultDataVO lv0CodeDropdown(CommonBaseVO commonViewVO) throws CommonApplicationException;

}
