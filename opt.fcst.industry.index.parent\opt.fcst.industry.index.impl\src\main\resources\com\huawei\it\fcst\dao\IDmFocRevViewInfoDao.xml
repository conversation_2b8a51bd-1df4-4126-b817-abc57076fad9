<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IDmFocViewInfoDao">
    <sql id="rev_weight_rate">
        <if test='viewFlag != null and viewFlag == "4"'>
            <choose>
                <when test='groupLevel == "LV3"'>
                    DISTINCT lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name, lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name, lv4_prod_rnd_team_code AS groupCode, lv4_prod_rd_team_cn_name AS groupCnName, 'LV4' AS groupLevel
                </when>
                <when test='groupLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name, lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS groupLevel,weight_rate
                </when>
                <when test='groupLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                    groupLevel,weight_rate
                </when>
                <when test='groupLevel == "CEG"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <when test='groupLevel == "LV0"'>
                    DISTINCT l3_ceg_code AS groupCode, l3_ceg_short_cn_name AS groupCnName, 'CEG' AS groupLevel,weight_rate
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "5"'>
            <choose>
                <when test='groupLevel == "LV3"'>
                    DISTINCT lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name, lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name, lv4_prod_rnd_team_code AS groupCode, lv4_prod_rd_team_cn_name AS groupCnName, 'LV4' AS groupLevel
                </when>
                <when test='groupLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS groupLevel,weight_rate
                </when>
                <when test='groupLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                    groupLevel,weight_rate
                </when>
                <when test='groupLevel == "MODL"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <when test='groupLevel == "CEG"'>
                    DISTINCT l4_ceg_code AS groupCode, l4_ceg_short_cn_name AS groupCnName, 'MODL' AS groupLevel
                </when>
                <when test='groupLevel == "LV0"'>
                    DISTINCT l3_ceg_code AS groupCode, l3_ceg_short_cn_name AS groupCnName, 'CEG' AS groupLevel,weight_rate
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "6"'>
            <choose>
                <when test='groupLevel == "LV3"'>
                    DISTINCT lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name, lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name, lv4_prod_rnd_team_code AS groupCode, lv4_prod_rd_team_cn_name AS groupCnName, 'LV4' AS groupLevel
                </when>
                <when test='groupLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS groupLevel,weight_rate
                </when>
                <when test='groupLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                    groupLevel,weight_rate
                </when>
                <when test='groupLevel == "CATEGORY"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <when test='groupLevel == "MODL" and (dataType ==null or dataType=="")'>
                    DISTINCT category_code AS groupCode, category_code || ' ' || category_cn_name AS groupCnName, 'CATEGORY' AS groupLevel
                </when>
                <when test='groupLevel == "MODL" and dataType !=null and dataType!="" and dataType =="CATEGORY"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode,  lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <when test='groupLevel == "MODL" and dataType !=null and dataType!="" and dataType =="ITEM"'>
                    DISTINCT category_code AS groupCode, category_code || ' ' || category_cn_name AS groupCnName, 'CATEGORY' AS groupLevel
                </when>
                <when test='groupLevel == "CEG"'>
                    DISTINCT l4_ceg_code AS groupCode, l4_ceg_short_cn_name AS groupCnName, 'MODL' AS groupLevel
                </when>
                <when test='groupLevel == "LV0"'>
                    DISTINCT l3_ceg_code AS groupCode, l3_ceg_short_cn_name AS groupCnName, 'CEG' AS groupLevel,weight_rate
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
    </sql>

    <sql id="rev_absolute_weight">
        <if test='viewFlag != null and viewFlag == "4"'>
            <choose>
                <when test='groupLevel == "LV3"'>
                    DISTINCT lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code AS groupCode, lv4_prod_rd_team_cn_name AS groupCnName, 'LV3' AS groupLevel
                </when>
                <when test='groupLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS groupLevel,absolute_weight
                </when>
                <when test='groupLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                    groupLevel,absolute_weight
                </when>
                <when test='groupLevel == "CEG"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <when test='groupLevel == "LV0"'>
                    DISTINCT l3_ceg_code AS groupCode, l3_ceg_short_cn_name AS groupCnName, 'CEG' AS groupLevel,absolute_weight
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "5"'>
            <choose>
                <when test='groupLevel == "LV3"'>
                    DISTINCT lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code AS groupCode, lv4_prod_rd_team_cn_name AS groupCnName, 'LV4' AS groupLevel
                </when>
                <when test='groupLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS groupLevel,absolute_weight
                </when>
                <when test='groupLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                    groupLevel,absolute_weight
                </when>
                <when test='groupLevel == "MODL"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <when test='groupLevel == "CEG"'>
                    DISTINCT l4_ceg_code AS groupCode, l4_ceg_short_cn_name AS groupCnName, 'MODL' AS groupLevel
                </when>
                <when test='groupLevel == "LV0"'>
                    DISTINCT l3_ceg_code AS groupCode, l3_ceg_short_cn_name AS groupCnName, 'CEG' AS groupLevel,absolute_weight
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "6"'>
            <choose>
                <when test='groupLevel == "LV3"'>
                    DISTINCT lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code AS groupCode, lv4_prod_rd_team_cn_name AS groupCnName, 'LV4' AS groupLevel
                </when>
                <when test='groupLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS groupLevel,absolute_weight
                </when>
                <when test='groupLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                    groupLevel,absolute_weight
                </when>
                <when test='groupLevel == "CATEGORY"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <when test='groupLevel == "MODL" and (dataType ==null or dataType=="")'>
                    DISTINCT category_code AS groupCode, category_code || ' ' || category_cn_name AS groupCnName, 'CATEGORY' AS groupLevel
                </when>
                <when test='groupLevel == "MODL" and dataType !=null and dataType!="" and dataType =="CATEGORY"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode,  lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <when test='groupLevel == "MODL" and dataType !=null and dataType!="" and dataType =="ITEM"'>
                    DISTINCT category_code AS groupCode, category_code || ' ' || category_cn_name AS groupCnName, 'CATEGORY' AS groupLevel
                </when>
                <when test='groupLevel == "CEG"'>
                    DISTINCT l4_ceg_code AS groupCode, l4_ceg_short_cn_name AS groupCnName, 'MODL' AS groupLevel
                </when>
                <when test='groupLevel == "LV0"'>
                    DISTINCT l3_ceg_code AS groupCode, l3_ceg_short_cn_name AS groupCnName, 'CEG' AS groupLevel,absolute_weight
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
    </sql>

    <select id="revViewInfoList" resultMap="resultMap">
        SELECT
        <if test='isMultipleSelect == false'>
            <include refid="rev_weight_rate"></include>
        </if>
        <if test='isMultipleSelect == true'>
            <include refid="rev_absolute_weight"></include>
        </if>
        <choose>
            <when test='groupLevel =="LV0"'>
                from fin_dm_opt_foi.${tablePreFix}_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_rev_annual_weight_t weight
                on amp.l3_ceg_code = weight.group_code
                and weight.view_flag = #{viewFlag,jdbcType=VARCHAR}
                and amp.lv0_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                AND weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                AND weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
                AND weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                WHERE amp.view_flag = #{revViewFlag,jdbcType=VARCHAR} and amp.del_flag ='N' and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                AND amp.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
            <when test='viewFlag == "4" and groupLevel == "CEG"'>
                from fin_dm_opt_foi.${tablePreFix}_view_info_d amp
                WHERE amp.view_flag = #{revViewFlag,jdbcType=VARCHAR} and amp.del_flag ='N' and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                AND amp.group_level = #{groupLevel,jdbcType=VARCHAR}
            </when>
            <when test='viewFlag == "5" and groupLevel =="MODL"'>
                from fin_dm_opt_foi.${tablePreFix}_view_info_d amp
                WHERE amp.view_flag = #{revViewFlag,jdbcType=VARCHAR} and amp.del_flag ='N' and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                AND amp.group_level = #{groupLevel,jdbcType=VARCHAR}
            </when>
            <when test='viewFlag == "5" and groupLevel == "CEG"'>
                from fin_dm_opt_foi.${tablePreFix}_view_info_d amp
                WHERE amp.view_flag = #{revViewFlag,jdbcType=VARCHAR} and amp.del_flag ='N' and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                AND amp.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
            <when test='viewFlag == "6" and groupLevel == "CATEGORY"'>
                from fin_dm_opt_foi.${tablePreFix}_view_info_d amp
                WHERE amp.view_flag = #{revViewFlag,jdbcType=VARCHAR} and amp.del_flag ='N' and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                AND amp.group_level = #{groupLevel,jdbcType=VARCHAR}
            </when>
            <when test='viewFlag == "6" and (groupLevel == "CEG" || groupLevel =="MODL")'>
                <if test='dataType ==null or dataType ==""'>
                    from fin_dm_opt_foi.${tablePreFix}_view_info_d amp
                    WHERE amp.view_flag = #{revViewFlag,jdbcType=VARCHAR} and amp.del_flag ='N' and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                    AND amp.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                </if>
                <if test='dataType !=null and dataType !="" and dataType=="ITEM"'>
                    from fin_dm_opt_foi.${tablePreFix}_view_info_d amp
                    WHERE amp.view_flag = #{revViewFlag,jdbcType=VARCHAR} and amp.del_flag ='N' and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                    AND amp.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                </if>
                <if test='dataType !=null and dataType !="" and dataType=="CATEGORY"'>
                    from fin_dm_opt_foi.${tablePreFix}_view_info_d amp
                    WHERE amp.view_flag = #{revViewFlag,jdbcType=VARCHAR} and amp.del_flag ='N' and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                    AND amp.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                </if>
            </when>
            <when test='groupLevel == "LV1"'>
                from fin_dm_opt_foi.${tablePreFix}_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_rev_annual_weight_t weight
                on amp.lv2_prod_rnd_team_code = weight.group_code
                and weight.view_flag = #{viewFlag,jdbcType=VARCHAR}
                and amp.lv1_prod_rnd_team_code = weight.parent_code
                <if test='purCodeList != null and purCodeList.size() > 0'>
                    <foreach collection='purCodeList' item="code" open="and weight.pur_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                AND weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                AND weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
                AND weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                WHERE amp.view_flag = #{revViewFlag,jdbcType=VARCHAR} and amp.del_flag ='N' and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
            </when>
            <when test='groupLevel == "LV2"'>
                from fin_dm_opt_foi.${tablePreFix}_view_info_d amp
                left join fin_dm_opt_foi.${tablePreFix}_rev_annual_weight_t weight
                on amp.lv3_prod_rnd_team_code = weight.group_code
                and weight.view_flag = #{viewFlag,jdbcType=VARCHAR}
                and amp.lv2_prod_rnd_team_code = weight.parent_code
                <if test='purCodeList != null and purCodeList.size() > 0'>
                    <foreach collection='purCodeList' item="code" open="and weight.pur_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                AND weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                AND weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
                AND weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                WHERE amp.view_flag = #{revViewFlag,jdbcType=VARCHAR} and amp.del_flag ='N' and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
            </when>
            <when test='groupLevel == "LV3"'>
                from fin_dm_opt_foi.${tablePreFix}_view_info_d amp
                WHERE amp.view_flag = #{revViewFlag,jdbcType=VARCHAR} and amp.del_flag ='N' and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
            </when>
        </choose>
        AND amp.del_flag ='N'
        AND amp.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        AND amp.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        AND amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        <choose>
            <when test='groupLevel == "CEG" and groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND amp.l3_ceg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='groupLevel == "MODL" and groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND amp.l4_ceg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='groupLevel == "CATEGORY" and groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND amp.category_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='purLevel == "CEG" and purCodeList != null and purCodeList.size() > 0'>
            <foreach collection='purCodeList' item="code" open="AND amp.l3_ceg_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='purLevel == "MODL" and purCodeList != null and purCodeList.size() > 0'>
            <foreach collection='purCodeList' item="code" open="AND amp.l4_ceg_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='purLevel == "CATEGORY" and purCodeList != null and purCodeList.size() > 0'>
            <foreach collection='purCodeList' item="code" open="AND amp.category_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamLevel == "LV1" and teamCodeList != null and teamCodeList.size() > 0'>
            <foreach collection='teamCodeList' item="code" open="AND amp.lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamLevel == "LV2" and teamCodeList != null and teamCodeList.size() > 0'>
            <foreach collection='teamCodeList' item="code" open="AND amp.lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamLevel == "LV3" and teamCodeList != null and teamCodeList.size() > 0'>
            <foreach collection='teamCodeList' item="code" open="AND amp.lv3_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='viewFlag == "4" and groupLevel == "CEG" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND amp.lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='viewFlag == "5" and groupLevel == "MODL" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND amp.lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='viewFlag == "6" and groupLevel == "CATEGORY" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND amp.lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND amp.lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='isMultipleSelect == false'>
            <if test='groupLevel != "CATEGORY" and groupLevel != "CEG" and groupLevel != "MODL" and groupLevel != "LV3"'>
                order by weight_rate desc
            </if>
        </if>
        <if test='isMultipleSelect == true'>
            <if test='groupLevel != "CATEGORY" and groupLevel != "CEG" and groupLevel != "MODL" and groupLevel != "LV3"'>
                order by absolute_weight desc
            </if>
        </if>

    </select>

  <sql id = "rev_month_weightRate">
      <if test='viewFlag != null and viewFlag == "4"'>
          <choose>
              <when test='groupLevel == "LV3"'>
                  DISTINCT lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code AS groupCode, lv4_prod_rd_team_cn_name AS groupCnName, 'LV4' AS groupLevel
              </when>
              <when test='groupLevel == "LV2"'>
                  DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS groupLevel,weight.weight_rate
              </when>
              <when test='teamLevel == "LV1"'>
                  DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                  groupLevel,weight.weight_rate
              </when>
              <when test='groupLevel == "CEG"'>
                  DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
              </when>
              <when test='teamLevel == "LV0"'>
                  DISTINCT top_l3_ceg_code AS groupCode, top_l3_ceg_short_cn_name AS groupCnName, 'CEG' AS groupLevel,weight.weight_rate
              </when>
              <otherwise>
              </otherwise>
          </choose>
      </if>
      <if test='viewFlag != null and viewFlag == "5"'>
          <choose>
              <when test='groupLevel == "LV3"'>
                  DISTINCT lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code AS groupCode, lv4_prod_rd_team_cn_name AS groupCnName, 'LV4' AS groupLevel
              </when>
              <when test='groupLevel == "LV2"'>
                  DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS groupLevel,weight.weight_rate
              </when>
              <when test='teamLevel == "LV1"'>
                  DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                  groupLevel,weight.weight_rate
              </when>
              <when test='groupLevel == "MODL"'>
                  DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
              </when>
              <when test='groupLevel == "CEG"'>
                  DISTINCT top_l4_ceg_code AS groupCode, top_l4_ceg_short_cn_name AS groupCnName, 'MODL' AS groupLevel
              </when>
              <when test='teamLevel == "LV0"'>
                  DISTINCT top_l3_ceg_code AS groupCode, top_l3_ceg_short_cn_name AS groupCnName, 'CEG' AS groupLevel,weight.weight_rate
              </when>
              <otherwise>
              </otherwise>
          </choose>
      </if>
      <if test='viewFlag != null and viewFlag == "6"'>
          <choose>
              <when test='groupLevel == "LV3"'>
                  DISTINCT lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code AS groupCode, lv4_prod_rd_team_cn_name AS groupCnName, 'LV4' AS groupLevel
              </when>
              <when test='groupLevel == "LV2"'>
                  DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS groupLevel,weight.weight_rate
              </when>
              <when test='teamLevel == "LV1"'>
                  DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                  groupLevel,weight.weight_rate
              </when>
              <when test='groupLevel == "CATEGORY"'>
                  DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
              </when>
              <when test='groupLevel == "MODL"'>
                  DISTINCT top_category_code AS groupCode, top_category_code || ' ' || top_category_cn_name AS groupCnName, 'CATEGORY' AS groupLevel
              </when>
              <when test='groupLevel == "CEG"'>
                  DISTINCT top_l4_ceg_code AS groupCode, top_l4_ceg_short_cn_name AS groupCnName, 'MODL' AS groupLevel
              </when>
              <when test='teamLevel == "LV0"'>
                  DISTINCT top_l3_ceg_code AS groupCode, top_l3_ceg_short_cn_name AS groupCnName, 'CEG' AS groupLevel,weight.weight_rate
              </when>
              <otherwise>
              </otherwise>
          </choose>
      </if>
  </sql>

    <sql id = "rev_month_AbsoluteRate">
        <if test='viewFlag != null and viewFlag == "4"'>
            <choose>
                <when test='groupLevel == "LV3"'>
                    DISTINCT lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code AS groupCode, lv4_prod_rd_team_cn_name AS groupCnName, 'LV4' AS groupLevel
                </when>
                <when test='groupLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS groupLevel,weight.absolute_weight
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                    groupLevel,weight.absolute_weight
                </when>
                <when test='groupLevel == "CEG"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT top_l3_ceg_code AS groupCode, top_l3_ceg_short_cn_name AS groupCnName, 'CEG' AS groupLevel,weight.absolute_weight
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "5"'>
            <choose>
                <when test='groupLevel == "LV3"'>
                    DISTINCT lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code AS groupCode, lv4_prod_rd_team_cn_name AS groupCnName, 'LV4' AS groupLevel
                </when>
                <when test='groupLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS groupLevel,weight.absolute_weight
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                    groupLevel,weight.absolute_weight
                </when>
                <when test='groupLevel == "MODL"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <when test='groupLevel == "CEG"'>
                    DISTINCT top_l4_ceg_code AS groupCode, top_l4_ceg_short_cn_name AS groupCnName, 'MODL' AS groupLevel
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT top_l3_ceg_code AS groupCode, top_l3_ceg_short_cn_name AS groupCnName, 'CEG' AS groupLevel,weight.absolute_weight
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "6"'>
            <choose>
                <when test='groupLevel == "LV3"'>
                    DISTINCT lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code AS groupCode, lv4_prod_rd_team_cn_name AS groupCnName, 'LV4' AS groupLevel
                </when>
                <when test='groupLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS groupLevel,weight.absolute_weight
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                    groupLevel,weight.absolute_weight
                </when>
                <when test='groupLevel == "CATEGORY"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <when test='groupLevel == "MODL"'>
                    DISTINCT top_category_code AS groupCode, top_category_code || ' ' || top_category_cn_name AS groupCnName, 'CATEGORY' AS groupLevel
                </when>
                <when test='groupLevel == "CEG"'>
                    DISTINCT top_l4_ceg_code AS groupCode, top_l4_ceg_short_cn_name AS groupCnName, 'MODL' AS groupLevel
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT top_l3_ceg_code AS groupCode, top_l3_ceg_short_cn_name AS groupCnName, 'CEG' AS groupLevel,weight.absolute_weight
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
    </sql>

    <select id="revViewInfoListForMonth" resultMap="resultMap">
        SELECT
        <if test='isMultipleSelect == false'>
            <include refid="rev_month_weightRate"></include>
        </if>
        <if test='isMultipleSelect == true'>
            <include refid="rev_month_AbsoluteRate"></include>
        </if>
        <choose>
            <when test='groupLevel =="LV0"'>
                from fin_dm_opt_foi.${tablePreFix}_top_item_info_t amp left join fin_dm_opt_foi.${tablePreFix}_rev_annual_weight_t weight
                on amp.top_l3_ceg_code = weight.group_code
                and weight.view_flag = #{viewFlag,jdbcType=VARCHAR}
                and amp.lv0_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                AND weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                AND weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
                AND weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                WHERE amp.view_flag = '6'
            </when>
            <when test='viewFlag == "4" and groupLevel == "CEG"'>
                from fin_dm_opt_foi.${tablePreFix}_top_item_info_t amp
                WHERE amp.view_flag = '6'
            </when>
            <when test='viewFlag == "5" and (groupLevel == "CEG"|| groupLevel == "MODL")'>
                from fin_dm_opt_foi.${tablePreFix}_top_item_info_t amp
                WHERE amp.view_flag = '6'
            </when>
            <when test='viewFlag == "6" and (groupLevel == "CEG" || groupLevel =="MODL" || groupLevel == "CATEGORY")'>
                from fin_dm_opt_foi.${tablePreFix}_top_item_info_t amp
                WHERE amp.view_flag = '6'
                and amp.top_l4_ceg_code is not null
            </when>
            <when test='groupLevel == "LV1"'>
                from fin_dm_opt_foi.${tablePreFix}_top_item_info_t amp left join fin_dm_opt_foi.${tablePreFix}_rev_annual_weight_t weight
                on amp.lv2_prod_rnd_team_code = weight.group_code
                and weight.view_flag = #{viewFlag,jdbcType=VARCHAR}
                <if test='purCodeList != null and purCodeList.size() > 0'>
                    <foreach collection='purCodeList' item="code" open="and weight.pur_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                and amp.lv1_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR}
                and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                AND weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                AND weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
                AND weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                WHERE amp.view_flag = '6'
            </when>
            <when test='groupLevel == "LV2"'>
                from fin_dm_opt_foi.${tablePreFix}_top_item_info_t amp
                left join fin_dm_opt_foi.${tablePreFix}_rev_annual_weight_t weight
                on amp.lv3_prod_rnd_team_code = weight.group_code
                and weight.view_flag = #{viewFlag,jdbcType=VARCHAR}
                <if test='purCodeList != null and purCodeList.size() > 0'>
                    <foreach collection='purCodeList' item="code" open="and weight.pur_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                and amp.lv2_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR}
                and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                AND weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                AND weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
                AND weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                WHERE amp.view_flag = '6'
            </when>
            <when test='groupLevel == "LV3"'>
                from fin_dm_opt_foi.${tablePreFix}_top_item_info_t amp
                WHERE amp.view_flag = '6'
            </when>
        </choose>
        and amp.del_flag ='N'
        and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        and amp.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        and amp.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        and amp.version_id=#{monthVersionId,jdbcType=NUMERIC}
        and amp.is_top_flag='Y'
        and amp.double_flag ='Y'
        <choose>
            <when test='groupLevel == "CEG" and groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND amp.top_l3_ceg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='groupLevel == "MODL" and groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND amp.top_l4_ceg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='groupLevel == "CATEGORY" and groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND amp.top_category_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='purLevel == "CEG" and purCodeList != null and purCodeList.size() > 0'>
            <foreach collection='purCodeList' item="code" open="AND amp.top_l3_ceg_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='purLevel == "MODL" and purCodeList != null and purCodeList.size() > 0'>
            <foreach collection='purCodeList' item="code" open="AND amp.top_l4_ceg_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='purLevel == "CATEGORY" and purCodeList != null and purCodeList.size() > 0'>
            <foreach collection='purCodeList' item="code" open="AND amp.top_category_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamLevel == "LV1" and teamCodeList != null and teamCodeList.size() > 0'>
            <foreach collection='teamCodeList' item="code" open="AND amp.lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamLevel == "LV2" and teamCodeList != null and teamCodeList.size() > 0'>
            <foreach collection='teamCodeList' item="code" open="AND amp.lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamLevel == "LV3" and teamCodeList != null and teamCodeList.size() > 0'>
            <foreach collection='teamCodeList' item="code" open="AND amp.lv3_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='viewFlag == "4" and groupLevel == "CEG" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND amp.lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='viewFlag == "5" and groupLevel == "MODL" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND amp.lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='viewFlag == "6" and groupLevel == "CATEGORY" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND amp.lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND amp.lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='isMultipleSelect == false'>
            <if test='groupLevel != "CATEGORY" and groupLevel != "CEG" and groupLevel != "MODL" and groupLevel != "LV3"'>
                order by weight_rate desc
            </if>
        </if>
        <if test='isMultipleSelect == true'>
            <if test='groupLevel != "CATEGORY" and groupLevel != "CEG" and groupLevel != "MODL" and groupLevel != "LV3"'>
                order by absolute_weight desc
            </if>
        </if>
    </select>

    <select id="revViewInfoListForTopCate" resultMap="resultMap">
        SELECT
        <if test='isMultipleSelect == false'>
            <include refid="rev_month_weightRate"></include>
        </if>
        <if test='isMultipleSelect == true'>
            <include refid="rev_month_AbsoluteRate"></include>
        </if>
        <choose>
            <when test='groupLevel =="LV0"'>
                from fin_dm_opt_foi.${tablePreFix}_top_cate_info_t amp left join fin_dm_opt_foi.${tablePreFix}_rev_annual_weight_t weight
                on amp.top_l3_ceg_code = weight.group_code
                and weight.view_flag = #{viewFlag,jdbcType=VARCHAR}
                and amp.lv0_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                AND weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                AND weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
                AND weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                WHERE amp.view_flag = '6'
            </when>
            <when test='viewFlag == "4" and groupLevel == "CEG"'>
                from fin_dm_opt_foi.${tablePreFix}_top_cate_info_t amp
                WHERE amp.view_flag = '6'
            </when>
            <when test='viewFlag == "5" and (groupLevel == "CEG"|| groupLevel == "MODL")'>
                from fin_dm_opt_foi.${tablePreFix}_top_cate_info_t amp
                WHERE amp.view_flag = '6'
            </when>
            <when test='viewFlag == "6" and (groupLevel == "CEG" || groupLevel =="MODL" || groupLevel == "CATEGORY")'>
                from fin_dm_opt_foi.${tablePreFix}_top_cate_info_t amp
                WHERE amp.view_flag = '6'
                and amp.top_l4_ceg_code is not null
            </when>
            <when test='groupLevel == "LV1"'>
                from fin_dm_opt_foi.${tablePreFix}_top_cate_info_t amp left join fin_dm_opt_foi.${tablePreFix}_rev_annual_weight_t weight
                on amp.lv2_prod_rnd_team_code = weight.group_code
                and weight.view_flag = #{viewFlag,jdbcType=VARCHAR}
                <if test='purCodeList != null and purCodeList.size() > 0'>
                    <foreach collection='purCodeList' item="code" open="and weight.pur_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                and amp.lv1_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR}
                and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                AND weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                AND weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
                AND weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                WHERE amp.view_flag = '6'
            </when>
            <when test='groupLevel == "LV2"'>
                from fin_dm_opt_foi.${tablePreFix}_top_cate_info_t amp
                left join fin_dm_opt_foi.${tablePreFix}_rev_annual_weight_t weight
                on amp.lv3_prod_rnd_team_code = weight.group_code
                and weight.view_flag = #{viewFlag,jdbcType=VARCHAR}
                <if test='purCodeList != null and purCodeList.size() > 0'>
                    <foreach collection='purCodeList' item="code" open="and weight.pur_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                and amp.lv2_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR}
                and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                AND weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                AND weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
                AND weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                WHERE amp.view_flag = '6'
            </when>
            <when test='groupLevel == "LV3"'>
                from fin_dm_opt_foi.${tablePreFix}_top_cate_info_t amp
                WHERE amp.view_flag = '6'
            </when>
        </choose>
        and amp.del_flag ='N'
        and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        and amp.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        and amp.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        and amp.version_id=#{monthVersionId,jdbcType=NUMERIC}
        and amp.is_top_flag='Y'
        and amp.double_flag ='Y'
        <choose>
            <when test='groupLevel == "CEG" and groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND amp.top_l3_ceg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='groupLevel == "MODL" and groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND amp.top_l4_ceg_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='groupLevel == "CATEGORY" and groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND amp.top_category_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='purLevel == "CEG" and purCodeList != null and purCodeList.size() > 0'>
            <foreach collection='purCodeList' item="code" open="AND amp.top_l3_ceg_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='purLevel == "MODL" and purCodeList != null and purCodeList.size() > 0'>
            <foreach collection='purCodeList' item="code" open="AND amp.top_l4_ceg_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='purLevel == "CATEGORY" and purCodeList != null and purCodeList.size() > 0'>
            <foreach collection='purCodeList' item="code" open="AND amp.top_category_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamLevel == "LV1" and teamCodeList != null and teamCodeList.size() > 0'>
            <foreach collection='teamCodeList' item="code" open="AND amp.lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamLevel == "LV2" and teamCodeList != null and teamCodeList.size() > 0'>
            <foreach collection='teamCodeList' item="code" open="AND amp.lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamLevel == "LV3" and teamCodeList != null and teamCodeList.size() > 0'>
            <foreach collection='teamCodeList' item="code" open="AND amp.lv3_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='viewFlag == "4" and groupLevel == "CEG" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND amp.lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='viewFlag == "5" and groupLevel == "MODL" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND amp.lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='viewFlag == "6" and groupLevel == "CATEGORY" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND amp.lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND amp.lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='isMultipleSelect == false'>
            <if test='groupLevel != "CATEGORY" and groupLevel != "CEG" and groupLevel != "MODL" and groupLevel != "LV3"'>
                order by weight_rate desc
            </if>
        </if>
        <if test='isMultipleSelect == true'>
            <if test='groupLevel != "CATEGORY" and groupLevel != "CEG" and groupLevel != "MODL" and groupLevel != "LV3"'>
                order by absolute_weight desc
            </if>
        </if>
    </select>

    <select id="revViewInfoKeyWordList" resultMap="resultMap">
        SELECT
        <if test='groupLevel == "CATEGORY"'>
            DISTINCT l3_ceg_code,l3_ceg_short_cn_name as l3_ceg_cn_name,l4_ceg_code,l4_ceg_short_cn_name as l4_ceg_cn_name, category_code, category_code || ' ' || category_cn_name AS
            category_cn_name
        </if>
        from fin_dm_opt_foi.${tablePreFix}_view_info_d
        WHERE view_flag = '3' and del_flag ='N'
        AND caliber_flag = #{caliberFlag}
        AND oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        AND lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        AND group_level = 'CATEGORY'
        <if test="keyWord != null and keyWord != ''">
            AND (UPPER(category_code) LIKE CONCAT(CONCAT('%', UPPER(#{keyWord})) ,'%')
            OR UPPER(category_cn_name) LIKE CONCAT(CONCAT('%', UPPER(#{keyWord})) ,'%')
            )
        </if>
    </select>

    <select id="revViewInfoKeyWordForMonth" resultMap="resultMap">
        SELECT
        <if test='groupLevel == "CATEGORY"'>
            DISTINCT TOP_L3_CEG_CODE as L3_CEG_CODE ,TOP_L3_CEG_SHORT_CN_NAME as L3_CEG_CN_NAME,
            TOP_L4_CEG_CODE as L4_CEG_CODE ,TOP_L4_CEG_SHORT_CN_NAME as L4_CEG_CN_NAME,
            TOP_CATEGORY_CODE as CATEGORY_CODE, TOP_CATEGORY_CODE || ' ' || TOP_CATEGORY_CN_NAME AS category_cn_name
        </if>
        from fin_dm_opt_foi.${tablePreFix}_top_item_info_t
        where del_flag = 'N'
        AND IS_TOP_FLAG ='Y'
        AND DOUBLE_FLAG ='Y'
        AND version_id = #{monthVersionId}
        AND view_flag = '6'
        AND caliber_flag = #{caliberFlag}
        AND oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        AND lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        <if test="keyWord != null and keyWord != ''">
            AND (UPPER(TOP_CATEGORY_CODE) LIKE CONCAT(CONCAT('%', UPPER(#{keyWord})) ,'%')
            OR UPPER(TOP_CATEGORY_CN_NAME) LIKE CONCAT(CONCAT('%', UPPER(#{keyWord})) ,'%')
            )
        </if>
    </select>
</mapper>