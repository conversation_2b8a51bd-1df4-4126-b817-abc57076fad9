/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.relation;

import com.huawei.it.fcst.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * DmDimFoiItemCatgModlCegVO Class
 *
 * <AUTHOR>
 * @since 2023/3/10
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "指数预测维度关系表映射实体类")
public class DmDimFoiItemCatgModlCegVO extends BaseVO implements Serializable {
    private static final long serialVersionUID = -2361910256025647471L;

    /**
     * 采购组织编码            
     **/
    @ApiModelProperty(value = "采购组织编码")
    private String l2CegCode;

    /**
     * 采购组织中文名称                 
     **/
    @ApiModelProperty(value = "采购组织中文名称")
    private String l2CegCnName;

    /**
     * 专家团编码
     **/
    @ApiModelProperty(value = "专家团编码")
    private String l3CegCode;

    /**
     * 专家团（Group LV3简称）
     **/
    @ApiModelProperty(value = "专家团（Group LV3简称）")
    private String l3CegShortCnName;

    /**
     * 专家团中文名称
     **/
    @ApiModelProperty(value = "专家团中文名称")
    private String l3CegCnName;

    /**
     * 模块编码            
     **/
    @ApiModelProperty(value = "模块编码")
    private String l4CegCode;

    /**
     * 模块（Group LV4简称）
     **/
    @ApiModelProperty(value = "模块（Group LV4简称）")
    private String l4CegShortCnName;

    /**
     * 模块中文名称
     **/
    @ApiModelProperty(value = "模块中文名称")
    private String l4CegCnName;

    /**
     * 品类名称
     **/
    @ApiModelProperty(value = "品类名称")
    private String categoryName;

    /**
     * 品类编码
     **/
    @ApiModelProperty(value = "品类编码")
    private String categoryCode;

    /**
     * ITEM编码
     **/
    @ApiModelProperty(value = "ITEM编码")
    private String itemCode;

    /**
     * ITEM名称
     **/
    @ApiModelProperty(value = "ITEM名称")
    private String itemName;

    @ApiModelProperty(value = "Group层级（LV2：生产采购、LV3：专家团、LV4：模块、category：品类、ITEM：规格品、Supplier：供应商）")
    private String groupLevel;

    @ApiModelProperty(value = "版本ID")
    private Long versionId;
}
