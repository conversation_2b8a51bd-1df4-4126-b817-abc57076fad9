<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IDmFocEnergyCustomCombDao">
    <resultMap type="com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO" id="resultMap">
        <result property="pageFlag" column="page_flag"/>
        <result property="lv0ProdRndTeamCode" column="LV0_PROD_RND_TEAM_CODE" />
        <result property="lv0ProdRdTeamCnName" column="LV0_PROD_RD_TEAM_CN_NAME" />
        <result property="lv1ProdRndTeamCode" column="LV1_PROD_RND_TEAM_CODE" />
        <result property="lv1ProdRdTeamCnName" column="LV1_PROD_RD_TEAM_CN_NAME" />
        <result property="lv2ProdRndTeamCode" column="LV2_PROD_RND_TEAM_CODE" />
        <result property="lv2ProdRdTeamCnName" column="LV2_PROD_RD_TEAM_CN_NAME" />
        <result property="lv3ProdRndTeamCode" column="LV3_PROD_RND_TEAM_CODE" />
        <result property="lv3ProdRdTeamCnName" column="LV3_PROD_RD_TEAM_CN_NAME" />
        <result property="lv4ProdRndTeamCode" column="LV4_PROD_RND_TEAM_CODE" />
        <result property="lv4ProdRdTeamCnName" column="LV4_PROD_RD_TEAM_CN_NAME" />
        <result property="l1Name" column="l1_name"/>
        <result property="l2Name" column="l2_name"/>
        <result property="coaCode" column="coa_code"/>
        <result property="coaCnName" column="coa_cn_name"/>
        <result property="dimensionCode" column="dimension_code"/>
        <result property="dimensionCnName" column="dimension_cn_name"/>
        <result property="dimensionSubCategoryCode" column="dimension_subcategory_code"/>
        <result property="dimensionSubCategoryCnName" column="dimension_subcategory_cn_name"/>
        <result property="dimensionSubDetailCode" column="dimension_sub_detail_code"/>
        <result property="dimensionSubDetailCnName" column="dimension_sub_detail_cn_name"/>
        <result property="groupLevel" column="group_level"/>
        <result property="groupCnName" column="group_cn_name"/>
        <result property="groupCode" column="group_code"/>
        <result property="userId" column="user_id"/>
        <result property="roleId" column="role_id"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="caliberFlag" column="caliber_flag"/>
        <result property="granularityType" column="granularity_type"/>
        <result property="overseaFlag" column="oversea_flag"/>
        <result property="enableFlag" column="enable_flag"/>
        <result property="subEnableFlag" column="sub_enable_flag"/>
        <result property="lv0ProdListCode" column="lv0_prod_list_code"/>
        <result property="lv0ProdListCnName" column="lv0_prod_list_cn_name"/>
        <result property="l3CegCnName" column="TOP_L3_CEG_SHORT_CN_NAME"/>
        <result property="l3CegCode" column="top_l3_ceg_code"/>
        <result property="l3CegCnName" column="top_l3_ceg_cn_name"/>
        <result property="l3CegCnName" column="L3_CEG_SHORT_CN_NAME"/>
        <result property="l3CegCode" column="L3_CEG_CODE"/>
        <result property="l3CegCnName" column="l3_ceg_cn_name"/>
        <result property="l4CegCnName" column="TOP_L4_CEG_SHORT_CN_NAME"/>
        <result property="l4CegCode" column="TOP_L4_CEG_CODE"/>
        <result property="l4CegCnName" column="top_l4_ceg_cn_name"/>
        <result property="l4CegCnName" column="L4_CEG_SHORT_CN_NAME"/>
        <result property="l4CegCode" column="L4_CEG_CODE"/>
        <result property="l4CegCnName" column="l4_ceg_cn_name"/>
        <result property="categoryCode" column="category_code"/>
        <result property="categoryCnName" column="category_cn_name"/>
        <result property="categoryCode" column="top_category_code"/>
        <result property="categoryCnName" column="top_category_cn_name"/>
        <result property="isSeparate" column="is_separate"/>
        <result property="creationDate" column="creation_date"/>
        <result property="createdBy" column="created_by"/>
        <result property="lastUpdateDdate" column="last_update_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="customId" column="custom_id"/>
        <result property="customCnName" column="custom_cn_name"/>
        <result property="parentCode" column="parent_code"/>
        <result property="spartCnName" column="spart_cn_name"/>
        <result property="spartCode" column="spart_code"/>
        <result property="connectCode" column="connectCode"/>
        <result property="connectParentCode" column="connectParentCode"/>
        <result property="shippingObjectCode" column="shipping_object_code"/>
        <result property="shippingObjectCnName" column="shipping_object_cn_name"/>
        <result property="manufactureObjectCode" column="manufacture_object_code"/>
        <result property="manufactureObjectCnName" column="manufacture_object_cn_name"/>
        <result property="shippingObjectCode" column="top_shipping_object_code"/>
        <result property="shippingObjectCnName" column="top_shipping_object_cn_name"/>
        <result property="manufactureObjectCode" column="top_manufacture_object_code"/>
        <result property="manufactureObjectCnName" column="top_manufacture_object_cn_name"/>
    </resultMap>

    <select id="prodEnergyTeamCodeForDimensionMonth" resultMap="resultMap">
        SELECT
        <if test='groupLevel == "LV0"'>
            DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv0_prod_rnd_team_code AS group_code,lv0_prod_rd_team_cn_name AS group_cn_name,'LV0' AS group_level,
            lv0_prod_rnd_team_code AS connectCode
        </if>
        <if test='groupLevel == "LV1" and lv0Flag =="Y"'>
            DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv1_prod_rnd_team_code AS group_code,lv1_prod_rd_team_cn_name AS group_cn_name,'LV1' AS group_level,
            lv0_prod_rnd_team_code AS parent_code,
            CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectCode, lv0_prod_rnd_team_code AS connectParentCode
        </if>
        <if test='groupLevel == "LV1" and lv0Flag =="N"'>
            DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv1_prod_rnd_team_code AS group_code,lv1_prod_rd_team_cn_name AS group_cn_name,'LV1' AS group_level,
            CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectCode
        </if>
        <if test='groupLevel == "LV2"'>
            <choose>
                <when test='viewFlag != null and viewFlag != "0" and viewFlag != "1" and viewFlag != "2" and viewFlag != "9"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS group_code,lv2_prod_rd_team_cn_name AS group_cn_name,'LV2' AS group_level,
                    lv1_prod_rnd_team_code AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code) AS connectCode, CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code)  AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "LV3"'>
            <choose>
                <when test='viewFlag != null and (viewFlag == "6" or viewFlag == "7" or viewFlag == "8" or viewFlag == "11" or viewFlag == "12")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,
                    lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,
                    lv3_prod_rd_team_cn_name, lv3_prod_rnd_team_code AS
                    group_code,lv3_prod_rd_team_cn_name AS group_cn_name,'LV3' AS group_level,lv2_prod_rnd_team_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code)  AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "LV4"'>
            <choose>
                <when test='viewFlag != null and viewFlag == "12"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,
                    lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name, lv4_prod_rnd_team_code,
                    lv4_prod_rd_team_cn_name,lv4_prod_rnd_team_code AS group_code,lv4_prod_rd_team_cn_name AS group_cn_name,'LV4' AS group_level,
                    lv3_prod_rnd_team_code AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,'#*#',lv4_prod_rnd_team_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code)  AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "COA"'>
            <choose>
                <when test='viewFlag != null and viewFlag == "12"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,
                    lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,
                    lv3_prod_rd_team_cn_name, coa_code,coa_cn_name,coa_code AS
                    group_code,coa_cn_name AS group_cn_name,'COA' AS group_level,lv3_prod_rnd_team_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,'#*#',coa_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code)  AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "DIMENSION"'>
            <choose>
                <when test='viewFlag != null and (viewFlag == "0" or viewFlag == "1" or viewFlag == "2" or viewFlag == "9")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,
                    dimension_code,dimension_cn_name, dimension_code AS
                    group_code,dimension_cn_name AS group_cn_name,'DIMENSION' AS group_level,lv1_prod_rnd_team_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',dimension_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code)  AS connectParentCode
                </when>
                <when test='viewFlag != null and (viewFlag == "3" or viewFlag == "4" or viewFlag == "5" or viewFlag == "10")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,
                    dimension_code,dimension_cn_name, dimension_code AS
                    group_code,dimension_cn_name AS group_cn_name,'DIMENSION' AS group_level,lv2_prod_rnd_team_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',dimension_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code) AS connectParentCode
                </when>
                <when test='viewFlag != null and (viewFlag == "6" or viewFlag == "7" or viewFlag == "8" or viewFlag == "11")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,
                    dimension_code,dimension_cn_name, dimension_code AS
                    group_code,dimension_cn_name AS group_cn_name,'DIMENSION' AS group_level,lv3_prod_rnd_team_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,'#*#',dimension_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code)  AS connectParentCode
                </when>
                <when test='industryOrg == "ENERGY" and viewFlag != null and viewFlag == "12"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,coa_code,coa_cn_name,
                    dimension_code,dimension_cn_name, dimension_code AS
                    group_code,dimension_cn_name AS group_cn_name,'DIMENSION' AS group_level,coa_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,'#*#',coa_code,'#*#',dimension_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,'#*#',coa_code)  AS connectParentCode
                </when>
                <when test='industryOrg == "IAS" and viewFlag != null and viewFlag == "12"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,
                    dimension_code,dimension_cn_name, dimension_code AS group_code,dimension_cn_name AS group_cn_name,'DIMENSION' AS group_level,
                    lv4_prod_rnd_team_code AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,'#*#',lv4_prod_rnd_team_code,'#*#',dimension_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,'#*#',lv4_prod_rnd_team_code)  AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "SUBCATEGORY"'>
            <choose>
                <when test='viewFlag != null and (viewFlag == "1" or viewFlag =="2" or viewFlag =="9")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUBCATEGORY_CODE AS
                    group_code,DIMENSION_SUBCATEGORY_CN_NAME AS group_cn_name,'SUBCATEGORY' AS group_level,dimension_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',dimension_code)  AS connectParentCode
                </when>
                <when test='viewFlag != null and (viewFlag == "4" or viewFlag =="5" or viewFlag =="10")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUBCATEGORY_CODE AS
                    group_code,DIMENSION_SUBCATEGORY_CN_NAME AS group_cn_name,'SUBCATEGORY' AS group_level,dimension_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',dimension_code)  AS connectParentCode
                </when>
                <when test='industryOrg == "ENERGY" and viewFlag != null and (viewFlag == "7" or viewFlag =="8" or viewFlag =="11" or viewFlag =="12")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,dimension_code,dimension_cn_name, coa_code,coa_cn_name,DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUBCATEGORY_CODE AS
                    group_code,DIMENSION_SUBCATEGORY_CN_NAME AS group_cn_name,'SUBCATEGORY' AS group_level,dimension_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,DECODE(coa_code,'','','#*#' || coa_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,DECODE(coa_code,'','','#*#' || coa_code),'#*#',dimension_code)  AS connectParentCode
                </when>
                <when test='industryOrg == "IAS" and viewFlag != null and (viewFlag == "7" or viewFlag =="8" or viewFlag =="11" or viewFlag =="12")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,dimension_code,dimension_cn_name,
                    DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUBCATEGORY_CODE AS group_code,DIMENSION_SUBCATEGORY_CN_NAME AS group_cn_name,'SUBCATEGORY' AS group_level,
                    dimension_code AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),'#*#',dimension_code)  AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "SUB_DETAIL"'>
            <choose>
                <when test='viewFlag != null and (viewFlag == "2" or viewFlag == "9")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,
                    DIMENSION_SUB_DETAIL_CODE AS group_code,DIMENSION_SUB_DETAIL_CN_NAME AS group_cn_name,'SUB_DETAIL' AS group_level,DIMENSION_SUBCATEGORY_CODE AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE)  AS connectParentCode
                </when>
                <when test='viewFlag != null and (viewFlag == "5" or viewFlag == "10")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,
                    DIMENSION_SUB_DETAIL_CODE AS group_code,DIMENSION_SUB_DETAIL_CN_NAME AS group_cn_name,'SUB_DETAIL' AS group_level,DIMENSION_SUBCATEGORY_CODE AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE)  AS connectParentCode
                </when>
                <when test='industryOrg == "ENERGY" and viewFlag != null and (viewFlag == "8" or viewFlag == "11" or viewFlag =="12")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,
                    coa_code,coa_cn_name,DIMENSION_SUB_DETAIL_CODE AS group_code,DIMENSION_SUB_DETAIL_CN_NAME AS group_cn_name,'SUB_DETAIL' AS group_level,DIMENSION_SUBCATEGORY_CODE AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,DECODE(coa_code,'','','#*#' || coa_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,DECODE(coa_code,'','','#*#' || coa_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE)  AS connectParentCode
                </when>
                <when test='industryOrg == "IAS" and viewFlag != null and (viewFlag == "8" or viewFlag == "11" or viewFlag =="12")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,dimension_code,dimension_cn_name,
                    DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,
                    DIMENSION_SUB_DETAIL_CODE AS group_code,DIMENSION_SUB_DETAIL_CN_NAME AS group_cn_name,'SUB_DETAIL' AS group_level,DIMENSION_SUBCATEGORY_CODE AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE)  AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "SPART"'>
            <choose>
                <when test='industryOrg == "ENERGY" and viewFlag != null and (viewFlag == "9" or viewFlag == "10" or viewFlag == "11" or viewFlag == "12")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,SPART_CODE,SPART_CN_NAME,
                    coa_code,coa_cn_name,SPART_CODE AS group_code,SPART_CN_NAME AS group_cn_name,'SPART' AS group_level,DIMENSION_SUB_DETAIL_CODE AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' || coa_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE,'#*#',SPART_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' || coa_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE) AS connectParentCode
                </when>
                <when test='industryOrg == "IAS" and viewFlag != null and (viewFlag == "9" or viewFlag == "10" or viewFlag == "11" or viewFlag == "12")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,
                    lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,dimension_code,dimension_cn_name,
                    DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,SPART_CODE,SPART_CN_NAME,
                    SPART_CODE AS group_code,SPART_CN_NAME AS group_cn_name,'SPART' AS group_level,DIMENSION_SUB_DETAIL_CODE AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE,'#*#',SPART_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "CEG"'>
            <choose>
                <when test='viewFlag != null and (viewFlag == "0" or viewFlag =="3" or viewFlag =="6")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name, dimension_code,dimension_cn_name, TOP_L3_CEG_CODE,TOP_L3_CEG_SHORT_CN_NAME,TOP_L3_CEG_CODE AS
                    group_code,TOP_L3_CEG_SHORT_CN_NAME AS group_cn_name,'CEG' AS group_level,dimension_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),'#*#',dimension_code,'#*#',TOP_L3_CEG_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),'#*#',dimension_code) AS connectParentCode
                </when>
                <when test='viewFlag != null and (viewFlag == "1" or viewFlag =="4" or viewFlag =="7")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name, dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,TOP_L3_CEG_CODE,TOP_L3_CEG_SHORT_CN_NAME,TOP_L3_CEG_CODE AS
                    group_code,TOP_L3_CEG_SHORT_CN_NAME AS group_cn_name,'CEG' AS group_level,DIMENSION_SUBCATEGORY_CODE AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',TOP_L3_CEG_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectParentCode
                </when>
                <when test='viewFlag != null and (viewFlag == "2" or viewFlag =="5" or viewFlag =="8")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name, dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE, DIMENSION_SUB_DETAIL_CN_NAME,TOP_L3_CEG_CODE,TOP_L3_CEG_SHORT_CN_NAME,TOP_L3_CEG_CODE AS
                    group_code,TOP_L3_CEG_SHORT_CN_NAME AS group_cn_name,'CEG' AS group_level,DIMENSION_SUB_DETAIL_CODE AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE,'#*#',TOP_L3_CEG_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE) AS connectParentCode
                </when>
                <when test='industryOrg == "ENERGY" and viewFlag != null and (viewFlag == "9" or viewFlag =="10" or viewFlag =="11" or viewFlag == "12")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name, dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE, DIMENSION_SUB_DETAIL_CN_NAME,SPART_CODE,SPART_CN_NAME,TOP_L3_CEG_CODE,TOP_L3_CEG_SHORT_CN_NAME,TOP_L3_CEG_CODE AS
                    group_code,TOP_L3_CEG_SHORT_CN_NAME AS group_cn_name,coa_code,coa_cn_name,'CEG' AS group_level,SPART_CODE AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' || coa_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE,'#*#',SPART_CODE,'#*#',TOP_L3_CEG_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' || coa_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE,'#*#',SPART_CODE) AS connectParentCode
                </when>
                <when test='industryOrg == "IAS" and viewFlag != null and (viewFlag == "9" or viewFlag =="10" or viewFlag =="11" or viewFlag == "12")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name, lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,dimension_code,dimension_cn_name,
                    DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE, DIMENSION_SUB_DETAIL_CN_NAME,SPART_CODE,SPART_CN_NAME,TOP_L3_CEG_CODE,TOP_L3_CEG_SHORT_CN_NAME,
                    TOP_L3_CEG_CODE AS group_code,TOP_L3_CEG_SHORT_CN_NAME AS group_cn_name,'CEG' AS group_level,SPART_CODE AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE,'#*#',SPART_CODE,'#*#',TOP_L3_CEG_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE,'#*#',SPART_CODE) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "MODL"'>
            <choose>
                <when test='industryOrg == "ENERGY"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,
                    lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,
                    dimension_code,dimension_cn_name,coa_code,coa_cn_name,
                    DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,SPART_CODE,SPART_CN_NAME,
                    DIMENSION_SUB_DETAIL_CN_NAME,TOP_L3_CEG_CODE,TOP_L3_CEG_SHORT_CN_NAME,TOP_L4_CEG_CODE,TOP_L4_CEG_SHORT_CN_NAME,TOP_L4_CEG_CODE
                    AS group_code,TOP_L4_CEG_SHORT_CN_NAME AS group_cn_name,'MODL' AS group_level,TOP_L3_CEG_CODE AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' || coa_code),DECODE(dimension_code,'','','#*#' || dimension_code),DECODE(DIMENSION_SUBCATEGORY_CODE,'','','#*#' || DIMENSION_SUBCATEGORY_CODE),DECODE(DIMENSION_SUB_DETAIL_CODE,'','','#*#' || DIMENSION_SUB_DETAIL_CODE),DECODE(SPART_CODE,'','','#*#' || SPART_CODE),'#*#',TOP_L3_CEG_CODE,'#*#',TOP_L4_CEG_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' || coa_code),DECODE(dimension_code,'','','#*#' || dimension_code),DECODE(DIMENSION_SUBCATEGORY_CODE,'','','#*#' || DIMENSION_SUBCATEGORY_CODE),DECODE(DIMENSION_SUB_DETAIL_CODE,'','','#*#' || DIMENSION_SUB_DETAIL_CODE),DECODE(SPART_CODE,'','','#*#' || SPART_CODE),'#*#',TOP_L3_CEG_CODE) AS connectParentCode
                </when>
                <when test='industryOrg == "IAS"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,
                    lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,
                    dimension_code,dimension_cn_name,
                    DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,SPART_CODE,SPART_CN_NAME,
                    DIMENSION_SUB_DETAIL_CN_NAME,TOP_L3_CEG_CODE,TOP_L3_CEG_SHORT_CN_NAME,TOP_L4_CEG_CODE,TOP_L4_CEG_SHORT_CN_NAME,TOP_L4_CEG_CODE
                    AS group_code,TOP_L4_CEG_SHORT_CN_NAME AS group_cn_name,'MODL' AS group_level,TOP_L3_CEG_CODE AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),DECODE(dimension_code,'','','#*#' || dimension_code),DECODE(DIMENSION_SUBCATEGORY_CODE,'','','#*#' || DIMENSION_SUBCATEGORY_CODE),DECODE(DIMENSION_SUB_DETAIL_CODE,'','','#*#' || DIMENSION_SUB_DETAIL_CODE),DECODE(SPART_CODE,'','','#*#' || SPART_CODE),'#*#',TOP_L3_CEG_CODE,'#*#',TOP_L4_CEG_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),DECODE(dimension_code,'','','#*#' || dimension_code),DECODE(DIMENSION_SUBCATEGORY_CODE,'','','#*#' || DIMENSION_SUBCATEGORY_CODE),DECODE(DIMENSION_SUB_DETAIL_CODE,'','','#*#' || DIMENSION_SUB_DETAIL_CODE),DECODE(SPART_CODE,'','','#*#' || SPART_CODE),'#*#',TOP_L3_CEG_CODE) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "CATEGORY"'>
            <choose>
                <when test='industryOrg == "ENERGY"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,
                    lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,
                    dimension_code,dimension_cn_name,coa_code,coa_cn_name,
                    DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,SPART_CODE,SPART_CN_NAME,
                    DIMENSION_SUB_DETAIL_CN_NAME,TOP_L3_CEG_CODE,TOP_L3_CEG_SHORT_CN_NAME,TOP_L4_CEG_CODE,TOP_L4_CEG_SHORT_CN_NAME,TOP_CATEGORY_CODE,TOP_CATEGORY_CN_NAME,TOP_CATEGORY_CODE
                    AS group_code,TOP_CATEGORY_CN_NAME AS group_cn_name,'CATEGORY' AS group_level,TOP_L4_CEG_CODE AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' || coa_code),DECODE(dimension_code,'','','#*#' || dimension_code),DECODE(DIMENSION_SUBCATEGORY_CODE,'','','#*#' || DIMENSION_SUBCATEGORY_CODE),DECODE(DIMENSION_SUB_DETAIL_CODE,'','','#*#' || DIMENSION_SUB_DETAIL_CODE),DECODE(SPART_CODE,'','','#*#' || SPART_CODE),'#*#',TOP_L3_CEG_CODE,'#*#',TOP_L4_CEG_CODE,'#*#',top_category_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' || coa_code),DECODE(dimension_code,'','','#*#' || dimension_code),DECODE(DIMENSION_SUBCATEGORY_CODE,'','','#*#' || DIMENSION_SUBCATEGORY_CODE),DECODE(DIMENSION_SUB_DETAIL_CODE,'','','#*#' || DIMENSION_SUB_DETAIL_CODE),DECODE(SPART_CODE,'','','#*#' || SPART_CODE),'#*#',TOP_L3_CEG_CODE,'#*#',TOP_L4_CEG_CODE) AS connectParentCode
                </when>
                <when test='industryOrg == "IAS"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,
                    lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,
                    dimension_code,dimension_cn_name,DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,SPART_CODE,SPART_CN_NAME,
                    DIMENSION_SUB_DETAIL_CN_NAME,TOP_L3_CEG_CODE,TOP_L3_CEG_SHORT_CN_NAME,TOP_L4_CEG_CODE,TOP_L4_CEG_SHORT_CN_NAME,TOP_CATEGORY_CODE,TOP_CATEGORY_CN_NAME,
                    TOP_CATEGORY_CODE AS group_code,TOP_CATEGORY_CN_NAME AS group_cn_name,'CATEGORY' AS group_level,TOP_L4_CEG_CODE AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),DECODE(dimension_code,'','','#*#' || dimension_code),DECODE(DIMENSION_SUBCATEGORY_CODE,'','','#*#' || DIMENSION_SUBCATEGORY_CODE),DECODE(DIMENSION_SUB_DETAIL_CODE,'','','#*#' || DIMENSION_SUB_DETAIL_CODE),DECODE(SPART_CODE,'','','#*#' || SPART_CODE),'#*#',TOP_L3_CEG_CODE,'#*#',TOP_L4_CEG_CODE,'#*#',top_category_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),DECODE(dimension_code,'','','#*#' || dimension_code),DECODE(DIMENSION_SUBCATEGORY_CODE,'','','#*#' || DIMENSION_SUBCATEGORY_CODE),DECODE(DIMENSION_SUB_DETAIL_CODE,'','','#*#' || DIMENSION_SUB_DETAIL_CODE),DECODE(SPART_CODE,'','','#*#' || SPART_CODE),'#*#',TOP_L3_CEG_CODE,'#*#',TOP_L4_CEG_CODE) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        from fin_dm_opt_foi.${tablePreFix}_dms_top_item_info_t
        where del_flag = 'N'
        AND IS_TOP_FLAG ='Y'
        AND DOUBLE_FLAG ='Y'
        AND version_id = #{monthVersionId,jdbcType=NUMERIC}
        AND view_flag = #{viewFlag,jdbcType=VARCHAR}
        AND caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        <if test='overseaFlag != null and overseaFlag != ""'>
            AND oversea_flag = #{overseaFlag}
        </if>
        <if test='lv0ProdListCode != null and lv0ProdListCode != ""'>
            AND lv0_prod_list_code = #{lv0ProdListCode}
        </if>
        <if test='lv0ProdRndTeamCode != null and lv0ProdRndTeamCode != ""'>
            AND lv0_prod_rnd_team_code = #{lv0ProdRndTeamCode}
        </if>
        <if test='lv1ProdRndTeamCode != null and lv1ProdRndTeamCode != ""'>
            AND lv1_prod_rnd_team_code = #{lv1ProdRndTeamCode}
        </if>
        <if test='lv2ProdRndTeamCode != null and lv2ProdRndTeamCode != ""'>
            AND lv2_prod_rnd_team_code = #{lv2ProdRndTeamCode}
        </if>
        <if test='lv3ProdRndTeamCode != null and lv3ProdRndTeamCode != ""'>
            AND lv3_prod_rnd_team_code = #{lv3ProdRndTeamCode}
        </if>
        <if test='lv4ProdRndTeamCode != null and lv4ProdRndTeamCode != ""'>
            AND lv4_prod_rnd_team_code = #{lv4ProdRndTeamCode}
        </if>
        <if test='coaCode != null and coaCode != ""'>
            AND coa_code = #{coaCode}
        </if>
        <if test='dimensionCode != null and dimensionCode != ""'>
            AND dimension_code = #{dimensionCode}
        </if>
        <if test='dimensionSubCategoryCode != null and dimensionSubCategoryCode != ""'>
            AND dimension_subcategory_code = #{dimensionSubCategoryCode}
        </if>
        <if test='dimensionSubDetailCode != null and dimensionSubDetailCode != ""'>
            AND dimension_sub_detail_code = #{dimensionSubDetailCode}
        </if>
        <if test='spartCode != null and spartCode != ""'>
            AND spart_code = #{spartCode}
        </if>
        <if test='l3CegCode != null and l3CegCode != ""'>
            AND top_l3_ceg_code = #{l3CegCode}
        </if>
        <if test='l4CegCode != null and l4CegCode != ""'>
            AND top_l4_ceg_code = #{l4CegCode}
        </if>
        <if test='categoryCode != null and categoryCode != ""'>
            AND top_category_code = #{categoryCode}
        </if>
        <if test='keyWord != null and keyWord != ""'>
            AND group_cn_name LIKE CONCAT(CONCAT('%', #{keyWord}::text ,'%'))
        </if>
        <!-- 存在lv1DimensionSet=no permission，且lv2是最大权限的情况，所以需要只走下面第二个判断逻辑-->
        <if test='groupLevel =="LV1" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel =="LV2" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <!-- 除去如上两种情况，其他的需要走后面两种情况-->
        <choose>
            <when test='lv1DimensionSet != null and lv1DimensionSet.size() == 1 and lv1DimensionSet.contains("NO_PERMISSION")'>

            </when>
            <otherwise>
                <if test='groupLevel !="LV0" and groupLevel !="LV1" and groupLevel !="LV2" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
                    <foreach collection='lv1DimensionSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
            </otherwise>
        </choose>
        <if test='groupLevel !="LV0" and groupLevel !="LV1" and groupLevel !="LV2" and (viewFlag !="0" and viewFlag !="1" and viewFlag !="2" and viewFlag !="9")  and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>

        <if test='lv1ProdRndTeamCodeSet != null and lv1ProdRndTeamCodeSet.size() > 0'>
            <foreach collection='lv1ProdRndTeamCodeSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv2ProdRndTeamCodeSet != null and lv2ProdRndTeamCodeSet.size() > 0'>
            <foreach collection='lv2ProdRndTeamCodeSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv3ProdRndTeamCodeSet != null and lv3ProdRndTeamCodeSet.size() > 0'>
            <foreach collection='lv3ProdRndTeamCodeSet' item="code" open="AND lv3_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv1CodeList != null and lv1CodeList.size() > 0'>
            <foreach collection='lv1CodeList' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv2CodeList != null and lv2CodeList.size() > 0'>
            <foreach collection='lv2CodeList' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv3CodeList != null and lv3CodeList.size() > 0'>
            <foreach collection='lv3CodeList' item="code" open="AND lv3_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionCodeList != null and dimensionCodeList.size() > 0'>
            <foreach collection='dimensionCodeList' item="code" open="AND dimension_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionSubcategoryCodeList != null and dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='dimensionSubcategoryCodeList' item="code" open="AND dimension_subcategory_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionSubDetailCodeList != null and dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='dimensionSubDetailCodeList' item="code" open="AND dimension_sub_detail_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <choose>
            <when test='groupLevel=="CEG" or groupLevel=="MODL" or groupLevel=="CATEGORY"'>
                <if test='connectDimensionCodeList != null and connectDimensionCodeList.size() > 0 and tablePreFix =="dm_foc"'>
                    <foreach collection='connectDimensionCodeList' item="code"
                             open="AND concat(lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#' ,lv3_prod_rnd_team_code,'#*#', dimension_code, '#*#',dimension_subcategory_code, '#*#', dimension_sub_detail_code,'#*#', spart_code) IN (" close=")"
                             index="index" separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='connectDimensionCodeList != null and connectDimensionCodeList.size() > 0 and tablePreFix =="dm_foc_energy"'>
                    <foreach collection='connectDimensionCodeList' item="code"
                             open="AND concat(lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#' ,lv3_prod_rnd_team_code,'#*#', coa_code,'#*#', dimension_code, '#*#',dimension_subcategory_code, '#*#', dimension_sub_detail_code,'#*#', spart_code) IN (" close=")"
                             index="index" separator=",">
                        #{code}
                    </foreach>
                </if>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='groupCodeList != null and groupCodeList.size() > 0'>
            <choose>
                <when test='groupLevel =="CATEGORY"'>
                    <foreach collection='groupCodeList' item="code" open="AND l4_ceg_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='groupLevel =="MODL"'>
                    <foreach collection='groupCodeList' item="code" open="AND l3_ceg_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='groupLevel =="CEG" and (viewFlag =="0" or viewFlag =="3" or viewFlag =="6")'>
                    <foreach collection='groupCodeList' item="code" open="AND dimension_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='groupLevel =="CEG" and (viewFlag =="1" or viewFlag =="4" or viewFlag =="7")'>
                    <foreach collection='groupCodeList' item="code" open="AND dimension_subcategory_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='groupLevel =="CEG" and (viewFlag =="2" or viewFlag =="5" or viewFlag =="8")'>
                    <foreach collection='groupCodeList' item="code" open="AND dimension_sub_detail_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='groupLevel =="CEG" and (viewFlag =="9" or viewFlag =="10" or viewFlag =="11")'>
                    <foreach collection='groupCodeList' item="code" open="AND spart_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='groupLevel =="SUBCATEGORY"'>
                    <foreach collection='groupCodeList' item="code" open="AND dimension_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='groupLevel =="SUB_DETAIL"'>
                    <foreach collection='groupCodeList' item="code" open="AND dimension_subcategory_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
    </select>

    <select id="prodEnergyTeamCodeForDimensionAnnual" resultMap="resultMap">
        SELECT
        <if test='groupLevel == "LV0"'>
            DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv0_prod_rnd_team_code AS group_code,lv0_prod_rd_team_cn_name AS group_cn_name,'LV0' AS group_level,lv0_prod_rnd_team_code AS connectCode
        </if>
        <if test='groupLevel == "LV1" and lv0Flag =="Y"'>
            DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv1_prod_rnd_team_code AS group_code,lv1_prod_rd_team_cn_name AS group_cn_name,'LV1' AS group_level,
            lv0_prod_rnd_team_code AS parent_code,
            CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectCode, lv0_prod_rnd_team_code AS connectParentCode
        </if>
        <if test='groupLevel == "LV1" and lv0Flag =="N"'>
            DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv1_prod_rnd_team_code AS group_code,lv1_prod_rd_team_cn_name AS group_cn_name,'LV1' AS group_level,
            CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectCode
        </if>
        <if test='groupLevel == "LV2"'>
            <choose>
                <when test='viewFlag != null and viewFlag != "0" and viewFlag != "1" and viewFlag != "2" and viewFlag != "9"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS group_code,lv2_prod_rd_team_cn_name AS group_cn_name,'LV2' AS group_level,
                    lv1_prod_rnd_team_code AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code) AS connectCode, CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "LV3"'>
            <choose>
                <when test='viewFlag != null and (viewFlag == "6" or viewFlag == "7" or viewFlag == "8" or viewFlag == "11" or viewFlag == "12")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,
                    lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,
                    lv3_prod_rd_team_cn_name, lv3_prod_rnd_team_code AS
                    group_code,lv3_prod_rd_team_cn_name AS group_cn_name,'LV3' AS group_level,lv2_prod_rnd_team_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "LV4"'>
            <choose>
                <when test='viewFlag != null and viewFlag == "12"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,
                    lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name, lv4_prod_rnd_team_code AS
                    group_code,lv4_prod_rd_team_cn_name AS group_cn_name,'LV4' AS group_level,lv3_prod_rnd_team_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,'#*#',lv4_prod_rnd_team_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "COA"'>
            <choose>
                <when test='viewFlag != null and viewFlag == "12"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,
                    coa_code,coa_cn_name, coa_code AS
                    group_code,coa_cn_name AS group_cn_name,'COA' AS group_level,lv3_prod_rnd_team_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,'#*#',coa_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "DIMENSION"'>
            <choose>
                <when test='viewFlag != null and (viewFlag == "0" or viewFlag == "1" or viewFlag == "2" or viewFlag == "9")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,
                    dimension_code,dimension_cn_name, dimension_code AS
                    group_code,dimension_cn_name AS group_cn_name,'DIMENSION' AS group_level,lv1_prod_rnd_team_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',dimension_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectParentCode
                </when>
                <when test='viewFlag != null and (viewFlag == "3" or viewFlag == "4" or viewFlag == "5" or viewFlag == "10")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,
                    dimension_code,dimension_cn_name, dimension_code AS
                    group_code,dimension_cn_name AS group_cn_name,'DIMENSION' AS group_level,lv2_prod_rnd_team_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',dimension_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code) AS connectParentCode
                </when>
                <when test='viewFlag != null and (viewFlag == "6" or viewFlag == "7" or viewFlag == "8" or viewFlag == "11")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,
                    dimension_code,dimension_cn_name, dimension_code AS
                    group_code,dimension_cn_name AS group_cn_name,'DIMENSION' AS group_level,lv3_prod_rnd_team_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,'#*#',dimension_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code) AS connectParentCode
                </when>
                <when test='industryOrg=="ENERGY" and viewFlag != null and viewFlag == "12"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,coa_code,coa_cn_name,
                    dimension_code,dimension_cn_name, dimension_code AS
                    group_code,dimension_cn_name AS group_cn_name,'DIMENSION' AS group_level,coa_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,'#*#',coa_code,'#*#',dimension_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,'#*#',coa_code)  AS connectParentCode
                </when>
                <when test='industryOrg=="IAS" and viewFlag != null and viewFlag == "12"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,
                    lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,dimension_code,dimension_cn_name, dimension_code AS
                    group_code,dimension_cn_name AS group_cn_name,'DIMENSION' AS group_level,lv4_prod_rnd_team_code AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,'#*#',lv4_prod_rnd_team_code,'#*#',dimension_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,'#*#',lv4_prod_rnd_team_code)  AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "SUBCATEGORY"'>
            <choose>
                <when test='viewFlag != null and (viewFlag == "1" or viewFlag =="2" or viewFlag =="9")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUBCATEGORY_CODE AS
                    group_code,DIMENSION_SUBCATEGORY_CN_NAME AS group_cn_name,'SUBCATEGORY' AS group_level,dimension_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',dimension_code) AS connectParentCode
                </when>
                <when test='viewFlag != null and (viewFlag == "4" or viewFlag =="5" or viewFlag =="10")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUBCATEGORY_CODE AS
                    group_code,DIMENSION_SUBCATEGORY_CN_NAME AS group_cn_name,'SUBCATEGORY' AS group_level,dimension_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',dimension_code) AS connectParentCode
                </when>
                <when test='industryOrg == "ENERGY" and viewFlag != null and (viewFlag == "7" or viewFlag =="8" or viewFlag =="11" or viewFlag =="12")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,coa_code,coa_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUBCATEGORY_CODE AS
                    group_code,DIMENSION_SUBCATEGORY_CN_NAME AS group_cn_name,'SUBCATEGORY' AS group_level,dimension_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,DECODE(coa_code,'','','#*#' || coa_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,DECODE(coa_code,'','','#*#' || coa_code),'#*#',dimension_code) AS connectParentCode
                </when>
                <when test='industryOrg == "IAS" and viewFlag != null and (viewFlag == "7" or viewFlag =="8" or viewFlag =="11" or viewFlag =="12")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,dimension_code,dimension_cn_name,
                    DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUBCATEGORY_CODE AS group_code,DIMENSION_SUBCATEGORY_CN_NAME AS group_cn_name,'SUBCATEGORY' AS group_level,
                    dimension_code AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),'#*#',dimension_code) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "SUB_DETAIL"'>
            <choose>
                <when test='viewFlag != null and (viewFlag == "2" or viewFlag == "9")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,
                    DIMENSION_SUB_DETAIL_CODE AS group_code,DIMENSION_SUB_DETAIL_CN_NAME AS group_cn_name,'SUB_DETAIL' AS group_level,DIMENSION_SUBCATEGORY_CODE AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectParentCode
                </when>
                <when test='viewFlag != null and (viewFlag == "5" or viewFlag == "10")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,
                    DIMENSION_SUB_DETAIL_CODE AS group_code,DIMENSION_SUB_DETAIL_CN_NAME AS group_cn_name,'SUB_DETAIL' AS group_level,DIMENSION_SUBCATEGORY_CODE AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectParentCode
                </when>
                <when test='industryOrg == "ENERGY" and viewFlag != null and (viewFlag == "8" or viewFlag == "11" or viewFlag =="12")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,
                    coa_code,coa_cn_name,DIMENSION_SUB_DETAIL_CODE AS group_code,DIMENSION_SUB_DETAIL_CN_NAME AS group_cn_name,'SUB_DETAIL' AS group_level,DIMENSION_SUBCATEGORY_CODE AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,DECODE(coa_code,'','','#*#' || coa_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,DECODE(coa_code,'','','#*#' || coa_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectParentCode
                </when>
                <when test='industryOrg == "IAS" and viewFlag != null and (viewFlag == "8" or viewFlag == "11" or viewFlag =="12")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,dimension_code,dimension_cn_name,
                    DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,
                    DIMENSION_SUB_DETAIL_CODE AS group_code,DIMENSION_SUB_DETAIL_CN_NAME AS group_cn_name,'SUB_DETAIL' AS group_level,DIMENSION_SUBCATEGORY_CODE AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "SPART"'>
            <choose>
                <when test='industryOrg == "ENERGY" and viewFlag != null and (viewFlag == "9" or viewFlag == "10" or viewFlag == "11" or viewFlag =="12")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,SPART_CODE,SPART_CN_NAME,coa_code,coa_cn_name,
                    SPART_CODE AS group_code,SPART_CN_NAME AS group_cn_name,'SPART' AS group_level,DIMENSION_SUB_DETAIL_CODE AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' || coa_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE,'#*#',SPART_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' || coa_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE) AS connectParentCode
                </when>
                <when test='industryOrg == "IAS" and viewFlag != null and (viewFlag == "9" or viewFlag == "10" or viewFlag == "11" or viewFlag =="12")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,
                    DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,SPART_CODE,SPART_CN_NAME,SPART_CODE AS group_code,SPART_CN_NAME AS group_cn_name,'SPART' AS group_level,
                    DIMENSION_SUB_DETAIL_CODE AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE,'#*#',SPART_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "CEG"'>
            <choose>
                <when test='viewFlag != null and (viewFlag == "0" or viewFlag =="3" or viewFlag =="6")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name, dimension_code,dimension_cn_name, L3_CEG_CODE,L3_CEG_SHORT_CN_NAME,L3_CEG_CODE AS
                    group_code,L3_CEG_SHORT_CN_NAME AS group_cn_name,'CEG' AS group_level,dimension_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),'#*#',dimension_code,'#*#',L3_CEG_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),'#*#',dimension_code) AS connectParentCode
                </when>
                <when test='viewFlag != null and (viewFlag == "1" or viewFlag =="4" or viewFlag =="7")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name, dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,L3_CEG_CODE,L3_CEG_SHORT_CN_NAME,L3_CEG_CODE AS
                    group_code,L3_CEG_SHORT_CN_NAME AS group_cn_name,'CEG' AS group_level,DIMENSION_SUBCATEGORY_CODE AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',L3_CEG_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectParentCode
                </when>
                <when test='viewFlag != null and (viewFlag == "2" or viewFlag =="5" or viewFlag =="8")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name, dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE, DIMENSION_SUB_DETAIL_CN_NAME,L3_CEG_CODE,L3_CEG_SHORT_CN_NAME,L3_CEG_CODE AS
                    group_code,L3_CEG_SHORT_CN_NAME AS group_cn_name,'CEG' AS group_level,DIMENSION_SUB_DETAIL_CODE AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE,'#*#',L3_CEG_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE) AS connectParentCode
                </when>
                <when test='industryOrg == "ENERGY" and viewFlag != null and (viewFlag == "9" or viewFlag =="10" or viewFlag =="11" or viewFlag=="12")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,coa_code,coa_cn_name, dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE, DIMENSION_SUB_DETAIL_CN_NAME,SPART_CN_NAME,SPART_CODE,L3_CEG_CODE,L3_CEG_SHORT_CN_NAME,L3_CEG_CODE AS
                    group_code,L3_CEG_SHORT_CN_NAME AS group_cn_name,'CEG' AS group_level,SPART_CODE AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' || coa_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE,'#*#',SPART_CODE,'#*#',L3_CEG_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' || coa_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE,'#*#',SPART_CODE) AS connectParentCode
                </when>
                <when test='industryOrg == "IAS" and viewFlag != null and (viewFlag == "9" or viewFlag =="10" or viewFlag =="11" or viewFlag=="12")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name, dimension_code,dimension_cn_name,
                    DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE, DIMENSION_SUB_DETAIL_CN_NAME,SPART_CN_NAME,SPART_CODE,L3_CEG_CODE,L3_CEG_SHORT_CN_NAME,L3_CEG_CODE AS
                    group_code,L3_CEG_SHORT_CN_NAME AS group_cn_name,'CEG' AS group_level,SPART_CODE AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE,'#*#',SPART_CODE,'#*#',L3_CEG_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE,'#*#',SPART_CODE) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "MODL"'>
            <choose>
                <when test='industryOrg == "ENERGY"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,
                    lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,
                    dimension_code,dimension_cn_name,coa_code,coa_cn_name,
                    DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,
                    DIMENSION_SUB_DETAIL_CN_NAME,SPART_CN_NAME,SPART_CODE,L3_CEG_CODE,L3_CEG_SHORT_CN_NAME,L4_CEG_CODE,L4_CEG_SHORT_CN_NAME,L4_CEG_CODE
                    AS group_code,L4_CEG_SHORT_CN_NAME AS group_cn_name,'MODL' AS group_level,L3_CEG_CODE AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' || coa_code),DECODE(dimension_code,'','','#*#' || dimension_code),DECODE(DIMENSION_SUBCATEGORY_CODE,'','','#*#' || DIMENSION_SUBCATEGORY_CODE),DECODE(DIMENSION_SUB_DETAIL_CODE,'','','#*#' || DIMENSION_SUB_DETAIL_CODE),DECODE(SPART_CODE,'','','#*#' || SPART_CODE),'#*#',L3_CEG_CODE,'#*#',L4_CEG_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' || coa_code),DECODE(dimension_code,'','','#*#' || dimension_code),DECODE(DIMENSION_SUBCATEGORY_CODE,'','','#*#' || DIMENSION_SUBCATEGORY_CODE),DECODE(DIMENSION_SUB_DETAIL_CODE,'','','#*#' || DIMENSION_SUB_DETAIL_CODE),DECODE(SPART_CODE,'','','#*#' || SPART_CODE),'#*#',L3_CEG_CODE) AS connectParentCode
                </when>
                <when test='industryOrg == "IAS"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,
                    lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,
                    dimension_code,dimension_cn_name,DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,
                    DIMENSION_SUB_DETAIL_CN_NAME,SPART_CN_NAME,SPART_CODE,L3_CEG_CODE,L3_CEG_SHORT_CN_NAME,L4_CEG_CODE,L4_CEG_SHORT_CN_NAME,L4_CEG_CODE
                    AS group_code,L4_CEG_SHORT_CN_NAME AS group_cn_name,'MODL' AS group_level,L3_CEG_CODE AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),DECODE(dimension_code,'','','#*#' || dimension_code),DECODE(DIMENSION_SUBCATEGORY_CODE,'','','#*#' || DIMENSION_SUBCATEGORY_CODE),DECODE(DIMENSION_SUB_DETAIL_CODE,'','','#*#' || DIMENSION_SUB_DETAIL_CODE),DECODE(SPART_CODE,'','','#*#' || SPART_CODE),'#*#',L3_CEG_CODE,'#*#',L4_CEG_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),DECODE(dimension_code,'','','#*#' || dimension_code),DECODE(DIMENSION_SUBCATEGORY_CODE,'','','#*#' || DIMENSION_SUBCATEGORY_CODE),DECODE(DIMENSION_SUB_DETAIL_CODE,'','','#*#' || DIMENSION_SUB_DETAIL_CODE),DECODE(SPART_CODE,'','','#*#' || SPART_CODE),'#*#',L3_CEG_CODE) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "CATEGORY"'>
            <choose>
                <when test='industryOrg == "ENERGY"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,
                    lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,
                    dimension_code,dimension_cn_name,coa_code,coa_cn_name,
                    DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,
                    DIMENSION_SUB_DETAIL_CN_NAME,SPART_CODE,SPART_CN_NAME,L3_CEG_CODE,L3_CEG_SHORT_CN_NAME,L4_CEG_CODE,L4_CEG_SHORT_CN_NAME,CATEGORY_CODE,CATEGORY_CN_NAME,CATEGORY_CODE
                    AS group_code,CATEGORY_CN_NAME AS group_cn_name,'CATEGORY' AS group_level,L4_CEG_CODE AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' || coa_code),DECODE(dimension_code,'','','#*#' || dimension_code),DECODE(DIMENSION_SUBCATEGORY_CODE,'','','#*#' || DIMENSION_SUBCATEGORY_CODE),DECODE(DIMENSION_SUB_DETAIL_CODE,'','','#*#' || DIMENSION_SUB_DETAIL_CODE),DECODE(SPART_CODE,'','','#*#' || SPART_CODE),'#*#',L3_CEG_CODE,'#*#',L4_CEG_CODE,'#*#',category_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' || coa_code),DECODE(dimension_code,'','','#*#' || dimension_code),DECODE(DIMENSION_SUBCATEGORY_CODE,'','','#*#' || DIMENSION_SUBCATEGORY_CODE),DECODE(DIMENSION_SUB_DETAIL_CODE,'','','#*#' || DIMENSION_SUB_DETAIL_CODE),DECODE(SPART_CODE,'','','#*#' || SPART_CODE),'#*#',L3_CEG_CODE,'#*#',L4_CEG_CODE) AS connectParentCode
                </when>
                <when test='industryOrg == "IAS"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,
                    lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,
                    dimension_code,dimension_cn_name,DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,
                    DIMENSION_SUB_DETAIL_CN_NAME,SPART_CODE,SPART_CN_NAME,L3_CEG_CODE,L3_CEG_SHORT_CN_NAME,L4_CEG_CODE,L4_CEG_SHORT_CN_NAME,CATEGORY_CODE,CATEGORY_CN_NAME,
                    CATEGORY_CODE AS group_code,CATEGORY_CN_NAME AS group_cn_name,'CATEGORY' AS group_level,L4_CEG_CODE AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),DECODE(dimension_code,'','','#*#' || dimension_code),DECODE(DIMENSION_SUBCATEGORY_CODE,'','','#*#' || DIMENSION_SUBCATEGORY_CODE),DECODE(DIMENSION_SUB_DETAIL_CODE,'','','#*#' || DIMENSION_SUB_DETAIL_CODE),DECODE(SPART_CODE,'','','#*#' || SPART_CODE),'#*#',L3_CEG_CODE,'#*#',L4_CEG_CODE,'#*#',category_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),DECODE(dimension_code,'','','#*#' || dimension_code),DECODE(DIMENSION_SUBCATEGORY_CODE,'','','#*#' || DIMENSION_SUBCATEGORY_CODE),DECODE(DIMENSION_SUB_DETAIL_CODE,'','','#*#' || DIMENSION_SUB_DETAIL_CODE),DECODE(SPART_CODE,'','','#*#' || SPART_CODE),'#*#',L3_CEG_CODE,'#*#',L4_CEG_CODE) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        from fin_dm_opt_foi.${tablePreFix}_DMS_VIEW_INFO_D
        where del_flag = 'N'
        and view_flag = #{viewFlag,jdbcType=VARCHAR} and del_flag ='N'  and caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        <if test='overseaFlag != null and overseaFlag != ""'>
            AND oversea_flag = #{overseaFlag}
        </if>
        <if test='lv0ProdListCode != null and lv0ProdListCode != ""'>
            AND lv0_prod_list_code = #{lv0ProdListCode}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            AND group_level = #{groupLevel}
        </if>
        <if test='lv0ProdRndTeamCode != null and lv0ProdRndTeamCode != ""'>
            AND lv0_prod_rnd_team_code = #{lv0ProdRndTeamCode}
        </if>
        <if test='lv1ProdRndTeamCode != null and lv1ProdRndTeamCode != ""'>
            AND lv1_prod_rnd_team_code = #{lv1ProdRndTeamCode}
        </if>
        <if test='lv2ProdRndTeamCode != null and lv2ProdRndTeamCode != ""'>
            AND lv2_prod_rnd_team_code = #{lv2ProdRndTeamCode}
        </if>
        <if test='lv3ProdRndTeamCode != null and lv3ProdRndTeamCode != ""'>
            AND lv3_prod_rnd_team_code = #{lv3ProdRndTeamCode}
        </if>
        <if test='lv4ProdRndTeamCode != null and lv4ProdRndTeamCode != ""'>
            AND lv4_prod_rnd_team_code = #{lv4ProdRndTeamCode}
        </if>
        <if test='coaCode != null and coaCode != ""'>
            AND coa_code = #{coaCode}
        </if>
        <if test='dimensionCode != null and dimensionCode != ""'>
            AND dimension_code = #{dimensionCode}
        </if>
        <if test='dimensionSubCategoryCode != null and dimensionSubCategoryCode != ""'>
            AND dimension_subcategory_code = #{dimensionSubCategoryCode}
        </if>
        <if test='dimensionSubDetailCode != null and dimensionSubDetailCode != ""'>
            AND dimension_sub_detail_code = #{dimensionSubDetailCode}
        </if>
        <if test='spartCode != null and spartCode != ""'>
            AND spart_code = #{spartCode}
        </if>
        <if test='l3CegCode != null and l3CegCode != ""'>
            AND l3_ceg_code = #{l3CegCode}
        </if>
        <if test='l4CegCode != null and l4CegCode != ""'>
            AND l4_ceg_code = #{l4CegCode}
        </if>
        <if test='categoryCode != null and categoryCode != ""'>
            AND category_code = #{categoryCode}
        </if>
        <if test='keyWord != null and keyWord != ""'>
            AND group_cn_name LIKE CONCAT(CONCAT('%', #{keyWord}::text ,'%'))
        </if>
        <!-- 存在lv1DimensionSet=no permission，且lv2是最大权限的情况，所以需要只走下面第二个判断逻辑-->
        <if test='groupLevel =="LV1" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel =="LV2" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <!-- 除去如上两种情况，其他的需要走后面两种情况-->
        <choose>
            <when test='lv1DimensionSet != null and lv1DimensionSet.size() == 1 and lv1DimensionSet.contains("NO_PERMISSION")'>

            </when>
            <otherwise>
                <if test='groupLevel !="LV0" and groupLevel !="LV1" and groupLevel !="LV2" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
                    <foreach collection='lv1DimensionSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
            </otherwise>
        </choose>
        <if test='groupLevel !="LV0" and groupLevel !="LV1" and groupLevel !="LV2" and (viewFlag !="0" and viewFlag !="1" and viewFlag !="2" and viewFlag !="9")  and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        
        <if test='lv1ProdRndTeamCodeSet != null and lv1ProdRndTeamCodeSet.size() > 0'>
            <foreach collection='lv1ProdRndTeamCodeSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv2ProdRndTeamCodeSet != null and lv2ProdRndTeamCodeSet.size() > 0'>
            <foreach collection='lv2ProdRndTeamCodeSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv1CodeList != null and lv1CodeList.size() > 0'>
            <foreach collection='lv1CodeList' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv2CodeList != null and lv2CodeList.size() > 0'>
            <foreach collection='lv2CodeList' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv3CodeList != null and lv3CodeList.size() > 0'>
            <foreach collection='lv3CodeList' item="code" open="AND lv3_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionCodeList != null and dimensionCodeList.size() > 0'>
            <foreach collection='dimensionCodeList' item="code" open="AND dimension_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionSubcategoryCodeList != null and dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='dimensionSubcategoryCodeList' item="code" open="AND dimension_subcategory_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionSubDetailCodeList != null and dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='dimensionSubDetailCodeList' item="code" open="AND dimension_sub_detail_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='spartCodeList != null and spartCodeList.size() > 0'>
            <foreach collection='spartCodeList' item="code" open="AND spart_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <choose>
            <when test='groupLevel=="CEG" or groupLevel=="MODL" or groupLevel=="CATEGORY"'>
                <if test='connectDimensionCodeList != null and connectDimensionCodeList.size() > 0 and tablePreFix =="dm_foc"'>
                    <foreach collection='connectDimensionCodeList' item="code"
                             open="AND concat(lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#' ,lv3_prod_rnd_team_code,'#*#', dimension_code, '#*#',dimension_subcategory_code, '#*#', dimension_sub_detail_code,'#*#', spart_code) IN (" close=")"
                             index="index" separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='connectDimensionCodeList != null and connectDimensionCodeList.size() > 0 and tablePreFix =="dm_foc_energy"'>
                    <foreach collection='connectDimensionCodeList' item="code"
                             open="AND concat(lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#' ,lv3_prod_rnd_team_code,'#*#', coa_code,'#*#', dimension_code, '#*#',dimension_subcategory_code, '#*#', dimension_sub_detail_code,'#*#', spart_code) IN (" close=")"
                             index="index" separator=",">
                        #{code}
                    </foreach>
                </if>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='groupCodeList != null and groupCodeList.size() > 0'>
            <choose>
                <when test='groupLevel =="CATEGORY"'>
                    <foreach collection='groupCodeList' item="code" open="AND l4_ceg_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='groupLevel =="MODL"'>
                    <foreach collection='groupCodeList' item="code" open="AND l3_ceg_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='groupLevel =="CEG" and (viewFlag =="0" or viewFlag =="3" or viewFlag =="6")'>
                    <foreach collection='groupCodeList' item="code" open="AND dimension_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='groupLevel =="CEG" and (viewFlag =="1" or viewFlag =="4" or viewFlag =="7")'>
                    <foreach collection='groupCodeList' item="code" open="AND dimension_subcategory_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='groupLevel =="CEG" and (viewFlag =="2" or viewFlag =="5" or viewFlag =="8")'>
                    <foreach collection='groupCodeList' item="code" open="AND dimension_sub_detail_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='groupLevel =="CEG" and (viewFlag =="9" or viewFlag =="10" or viewFlag =="11")'>
                    <foreach collection='groupCodeList' item="code" open="AND spart_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='groupLevel =="SUBCATEGORY"'>
                    <foreach collection='groupCodeList' item="code" open="AND dimension_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='groupLevel =="SUB_DETAIL"'>
                    <foreach collection='groupCodeList' item="code" open="AND dimension_subcategory_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
    </select>

    <select id="groupCodeForEnergyDimensionAnnualList" resultMap="resultMap">
        SELECT
        <if test='groupLevel == "LV0"'>
            DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv0_prod_rnd_team_code AS group_code,lv0_prod_rd_team_cn_name AS group_cn_name,'LV0' AS group_level,lv0_prod_rnd_team_code AS connectCode
        </if>
        <if test='groupLevel == "LV1"'>
            DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv1_prod_rnd_team_code AS group_code,lv1_prod_rd_team_cn_name AS group_cn_name,'LV1' AS group_level,
            CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectCode
        </if>
        <if test='groupLevel == "LV2"'>
            <choose>
                <when test='viewFlag != null and viewFlag != "0" and viewFlag != "1" and viewFlag != "2" and viewFlag != "9"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS group_code,lv2_prod_rd_team_cn_name AS group_cn_name,'LV2' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code) AS connectCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "LV3"'>
            <choose>
                <when test='viewFlag != null and (viewFlag == "6" or viewFlag == "7" or viewFlag == "8" or viewFlag == "11" or viewFlag == "12")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,
                    lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,
                    lv3_prod_rd_team_cn_name, lv3_prod_rnd_team_code AS
                    group_code,lv3_prod_rd_team_cn_name AS group_cn_name,'LV3' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code) AS connectCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "LV4"'>
            DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
            lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,
            lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,
            lv3_prod_rd_team_cn_name, lv4_prod_rnd_team_code,
            lv4_prod_rd_team_cn_name,lv4_prod_rnd_team_code AS
            group_code,lv4_prod_rd_team_cn_name AS group_cn_name,'LV4' AS group_level,
            CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,'#*#',lv4_prod_rnd_team_code) AS connectCode
        </if>
        <if test='groupLevel == "COA"'>
            DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
            lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,coa_code,coa_cn_name,
            coa_code AS group_code,coa_cn_name AS group_cn_name,'COA' AS group_level,
            CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,lv2_prod_rnd_team_code,lv3_prod_rnd_team_code,'#*#',coa_code) AS connectCode
        </if>
        <if test='groupLevel == "DIMENSION"'>
            <choose>
                <when test='industryOrg == "ENERGY"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,coa_code,coa_cn_name,
                    dimension_code,dimension_cn_name, dimension_code AS
                    group_code,dimension_cn_name AS group_cn_name,'DIMENSION' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' || coa_code),'#*#',dimension_code) AS connectCode
                </when>
                <when test='industryOrg == "IAS"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,
                    dimension_code,dimension_cn_name, dimension_code AS
                    group_code,dimension_cn_name AS group_cn_name,'DIMENSION' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),'#*#',dimension_code) AS connectCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "SUBCATEGORY"'>
            <choose>
                <when test='industryOrg == "ENERGY"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,coa_code,coa_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUBCATEGORY_CODE AS
                    group_code,DIMENSION_SUBCATEGORY_CN_NAME AS group_cn_name,'SUBCATEGORY' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' || coa_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectCode
                </when>
                <when test='industryOrg == "IAS"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUBCATEGORY_CODE AS
                    group_code,DIMENSION_SUBCATEGORY_CN_NAME AS group_cn_name,'SUBCATEGORY' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "SUB_DETAIL"'>
            <choose>
                <when test='industryOrg == "ENERGY"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,coa_code,coa_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,
                    DIMENSION_SUB_DETAIL_CODE AS group_code,DIMENSION_SUB_DETAIL_CN_NAME AS group_cn_name,'SUB_DETAIL' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' || coa_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE) AS connectCode
                </when>
                <when test='industryOrg == "IAS"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,
                    DIMENSION_SUB_DETAIL_CODE AS group_code,DIMENSION_SUB_DETAIL_CN_NAME AS group_cn_name,'SUB_DETAIL' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE) AS connectCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "SPART"'>
            <choose>
                <when test='industryOrg == "ENERGY"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,coa_code,coa_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,SPART_CODE,SPART_CN_NAME,
                    SPART_CODE AS group_code,SPART_CN_NAME AS group_cn_name,'SPART' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' || coa_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE,'#*#',SPART_CODE) AS connectCode
                </when>
                <when test='industryOrg == "IAS"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,SPART_CODE,SPART_CN_NAME,
                    SPART_CODE AS group_code,SPART_CN_NAME AS group_cn_name,'SPART' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE,'#*#',SPART_CODE) AS connectCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "CEG"'>
            <choose>
                <when test='industryOrg == "ENERGY"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name, dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE, DIMENSION_SUB_DETAIL_CN_NAME,L3_CEG_CODE,L3_CEG_SHORT_CN_NAME,L3_CEG_CODE AS
                    group_code,L3_CEG_SHORT_CN_NAME AS group_cn_name,'CEG' AS group_level,SPART_CODE,SPART_CN_NAME,coa_code,coa_cn_name,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' || coa_code),DECODE(dimension_code,'','','#*#' || dimension_code),DECODE(DIMENSION_SUBCATEGORY_CODE,'','','#*#' || DIMENSION_SUBCATEGORY_CODE) ,DECODE(DIMENSION_SUB_DETAIL_CODE,'','','#*#' || DIMENSION_SUB_DETAIL_CODE),DECODE(SPART_CODE,'','','#*#' || SPART_CODE),'#*#',L3_CEG_CODE) AS connectCode
                </when>
                <when test='industryOrg == "IAS"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name, lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE, DIMENSION_SUB_DETAIL_CN_NAME,L3_CEG_CODE,L3_CEG_SHORT_CN_NAME,L3_CEG_CODE AS
                    group_code,L3_CEG_SHORT_CN_NAME AS group_cn_name,'CEG' AS group_level,SPART_CODE,SPART_CN_NAME,coa_code,coa_cn_name,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),DECODE(dimension_code,'','','#*#' || dimension_code),DECODE(DIMENSION_SUBCATEGORY_CODE,'','','#*#' || DIMENSION_SUBCATEGORY_CODE) ,DECODE(DIMENSION_SUB_DETAIL_CODE,'','','#*#' || DIMENSION_SUB_DETAIL_CODE),DECODE(SPART_CODE,'','','#*#' || SPART_CODE),'#*#',L3_CEG_CODE) AS connectCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "MODL"'>
            <choose>
                <when test='industryOrg == "ENERGY"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,
                    lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,coa_code,coa_cn_name,
                    dimension_code,dimension_cn_name,SPART_CODE,SPART_CN_NAME,
                    DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,
                    DIMENSION_SUB_DETAIL_CN_NAME,L3_CEG_CODE,L3_CEG_SHORT_CN_NAME,L4_CEG_CODE,L4_CEG_SHORT_CN_NAME,L4_CEG_CODE
                    AS group_code,L4_CEG_SHORT_CN_NAME AS group_cn_name,'MODL' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' || coa_code),DECODE(dimension_code,'','','#*#' || dimension_code),DECODE(DIMENSION_SUBCATEGORY_CODE,'','','#*#' || DIMENSION_SUBCATEGORY_CODE),DECODE(DIMENSION_SUB_DETAIL_CODE,'','','#*#' || DIMENSION_SUB_DETAIL_CODE),DECODE(SPART_CODE,'','','#*#' || SPART_CODE),'#*#',L3_CEG_CODE,'#*#',L4_CEG_CODE) AS connectCode
                </when>
                <when test='industryOrg == "IAS"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,
                    lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,
                    dimension_code,dimension_cn_name,SPART_CODE,SPART_CN_NAME,
                    DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,
                    DIMENSION_SUB_DETAIL_CN_NAME,L3_CEG_CODE,L3_CEG_SHORT_CN_NAME,L4_CEG_CODE,L4_CEG_SHORT_CN_NAME,L4_CEG_CODE
                    AS group_code,L4_CEG_SHORT_CN_NAME AS group_cn_name,'MODL' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),DECODE(dimension_code,'','','#*#' || dimension_code),DECODE(DIMENSION_SUBCATEGORY_CODE,'','','#*#' || DIMENSION_SUBCATEGORY_CODE),DECODE(DIMENSION_SUB_DETAIL_CODE,'','','#*#' || DIMENSION_SUB_DETAIL_CODE),DECODE(SPART_CODE,'','','#*#' || SPART_CODE),'#*#',L3_CEG_CODE,'#*#',L4_CEG_CODE) AS connectCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "CATEGORY"'>
            <choose>
                <when test='industryOrg == "ENERGY"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,
                    lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,
                    dimension_code,dimension_cn_name,SPART_CODE,SPART_CN_NAME,
                    DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,
                    DIMENSION_SUB_DETAIL_CN_NAME,L3_CEG_CODE,L3_CEG_SHORT_CN_NAME,L4_CEG_CODE,L4_CEG_SHORT_CN_NAME,CATEGORY_CODE,CATEGORY_CN_NAME,CATEGORY_CODE
                    AS group_code,CATEGORY_CN_NAME AS group_cn_name,'CATEGORY' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' || coa_code),DECODE(dimension_code,'','','#*#' || dimension_code),DECODE(DIMENSION_SUBCATEGORY_CODE,'','','#*#' || DIMENSION_SUBCATEGORY_CODE),DECODE(DIMENSION_SUB_DETAIL_CODE,'','','#*#' || DIMENSION_SUB_DETAIL_CODE),DECODE(SPART_CODE,'','','#*#' || SPART_CODE),'#*#',L3_CEG_CODE,'#*#',L4_CEG_CODE,'#*#',category_code) AS connectCode
                </when>
                <when test='industryOrg == "IAS"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,
                    lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,
                    dimension_code,dimension_cn_name,SPART_CODE,SPART_CN_NAME,lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,
                    DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,
                    DIMENSION_SUB_DETAIL_CN_NAME,L3_CEG_CODE,L3_CEG_SHORT_CN_NAME,L4_CEG_CODE,L4_CEG_SHORT_CN_NAME,CATEGORY_CODE,CATEGORY_CN_NAME,CATEGORY_CODE
                    AS group_code,CATEGORY_CN_NAME AS group_cn_name,'CATEGORY' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),DECODE(dimension_code,'','','#*#' || dimension_code),DECODE(DIMENSION_SUBCATEGORY_CODE,'','','#*#' || DIMENSION_SUBCATEGORY_CODE),DECODE(DIMENSION_SUB_DETAIL_CODE,'','','#*#' || DIMENSION_SUB_DETAIL_CODE),DECODE(SPART_CODE,'','','#*#' || SPART_CODE),'#*#',L3_CEG_CODE,'#*#',L4_CEG_CODE,'#*#',category_code) AS connectCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        from fin_dm_opt_foi.${tablePreFix}_DMS_VIEW_INFO_D
        where del_flag = 'N'
        and view_flag = #{viewFlag,jdbcType=VARCHAR} and del_flag ='N'  and caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        <if test='overseaFlag != null and overseaFlag != ""'>
            AND oversea_flag = #{overseaFlag}
        </if>
        <if test='lv0ProdListCode != null and lv0ProdListCode != ""'>
            AND lv0_prod_list_code = #{lv0ProdListCode}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            AND group_level = #{groupLevel}
        </if>
        <if test='(groupLevel == "DIMENSION" or groupLevel == "SUBCATEGORY" or groupLevel == "SUB_DETAIL" or groupLevel == "SPART") and (viewFlag =="0" or viewFlag =="1" or viewFlag =="2" or viewFlag =="9") and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='(groupLevel == "DIMENSION" or groupLevel == "SUBCATEGORY" or groupLevel == "SUB_DETAIL" or groupLevel == "SPART") and (viewFlag =="3" or viewFlag =="4" or viewFlag =="5" or viewFlag =="10") and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        
        <if test='(groupLevel == "CEG" or groupLevel == "MODL" or groupLevel == "CATEGORY") and (viewFlag =="0" or viewFlag =="1" or viewFlag =="2" or viewFlag =="9") and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='(groupLevel == "CEG" or groupLevel == "MODL" or groupLevel == "CATEGORY") and (viewFlag =="3" or viewFlag =="4" or viewFlag =="5" or viewFlag =="10") and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel == "LV1" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel == "LV2" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        
        <if test='lv1ProdRndTeamCodeSet != null and lv1ProdRndTeamCodeSet.size() > 0'>
            <foreach collection='lv1ProdRndTeamCodeSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv2ProdRndTeamCodeSet != null and lv2ProdRndTeamCodeSet.size() > 0'>
            <foreach collection='lv2ProdRndTeamCodeSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv1CodeList != null and lv1CodeList.size() > 0'>
            <foreach collection='lv1CodeList' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv2CodeList != null and lv2CodeList.size() > 0'>
            <foreach collection='lv2CodeList' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv3CodeList != null and lv3CodeList.size() > 0'>
            <foreach collection='lv3CodeList' item="code" open="AND lv3_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='coaCodeList != null and coaCodeList.size() > 0'>
            <foreach collection='coaCodeList' item="code" open="AND coa_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionCodeList != null and dimensionCodeList.size() > 0'>
            <foreach collection='dimensionCodeList' item="code" open="AND dimension_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionSubcategoryCodeList != null and dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='dimensionSubcategoryCodeList' item="code" open="AND dimension_subcategory_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionSubDetailCodeList != null and dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='dimensionSubDetailCodeList' item="code" open="AND dimension_sub_detail_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='spartCodeList != null and spartCodeList.size() > 0'>
            <foreach collection='spartCodeList' item="code" open="AND spart_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <choose>
            <when test='groupLevel=="CEG" or groupLevel=="MODL" or groupLevel=="CATEGORY"'>
                <if test='connectDimensionCodeList != null and connectDimensionCodeList.size() > 0 and tablePreFix =="dm_foc"'>
                    <foreach collection='connectDimensionCodeList' item="code"
                             open="AND concat(lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#' ,lv3_prod_rnd_team_code,'#*#', dimension_code, '#*#',dimension_subcategory_code, '#*#', dimension_sub_detail_code,'#*#', spart_code) IN (" close=")"
                             index="index" separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='connectDimensionCodeList != null and connectDimensionCodeList.size() > 0 and tablePreFix =="dm_foc_energy"'>
                    <foreach collection='connectDimensionCodeList' item="code"
                             open="AND concat(lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#' ,lv3_prod_rnd_team_code,'#*#', coa_code, '#*#', dimension_code,'#*#',dimension_subcategory_code, '#*#', dimension_sub_detail_code,'#*#', spart_code) IN (" close=")"
                             index="index" separator=",">
                        #{code}
                    </foreach>
                </if>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='groupCodeList != null and groupCodeList.size() > 0'>
            <choose>
                <when test='groupLevel =="CATEGORY"'>
                    <foreach collection='groupCodeList' item="code" open="AND l4_ceg_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='groupLevel =="MODL"'>
                    <foreach collection='groupCodeList' item="code" open="AND l3_ceg_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='groupLevel =="CEG" and (viewFlag =="0" or viewFlag =="3" or viewFlag =="6")'>
                    <foreach collection='groupCodeList' item="code" open="AND dimension_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='groupLevel =="CEG" and (viewFlag =="1" or viewFlag =="4" or viewFlag =="7")'>
                    <foreach collection='groupCodeList' item="code" open="AND dimension_subcategory_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='groupLevel =="CEG" and (viewFlag =="2" or viewFlag =="5" or viewFlag =="8")'>
                    <foreach collection='groupCodeList' item="code" open="AND dimension_sub_detail_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='groupLevel =="CEG" and (viewFlag =="9" or viewFlag =="10" or viewFlag =="11")'>
                    <foreach collection='groupCodeList' item="code" open="AND spart_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='groupLevel =="SUBCATEGORY"'>
                    <foreach collection='groupCodeList' item="code" open="AND dimension_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='groupLevel =="SUB_DETAIL"'>
                    <foreach collection='groupCodeList' item="code" open="AND dimension_subcategory_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
    </select>

    <select id="groupCodeForEnergyDimensionMonthList" resultMap="resultMap">
        SELECT
        <if test='groupLevel == "LV0"'>
            DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv0_prod_rnd_team_code AS group_code,lv0_prod_rd_team_cn_name AS group_cn_name,'LV0' AS group_level,
            lv0_prod_rnd_team_code AS connectCode
        </if>
        <if test='groupLevel == "LV1"'>
            DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv1_prod_rnd_team_code AS group_code,lv1_prod_rd_team_cn_name AS group_cn_name,'LV1' AS group_level,
            CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectCode
        </if>
        <if test='groupLevel == "LV2"'>
            <choose>
                <when test='viewFlag != null and viewFlag != "0" and viewFlag != "1" and viewFlag != "2" and viewFlag != "9"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS group_code,lv2_prod_rd_team_cn_name AS group_cn_name,'LV2' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code) AS connectCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "LV3"'>
            <choose>
                <when test='viewFlag != null and (viewFlag == "6" or viewFlag == "7" or viewFlag == "8" or viewFlag == "11" or viewFlag == "12")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,
                    lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,
                    lv3_prod_rd_team_cn_name, lv3_prod_rnd_team_code AS
                    group_code,lv3_prod_rd_team_cn_name AS group_cn_name,'LV3' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code) AS connectCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "LV4"'>
            DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
            lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,
            lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,
            lv3_prod_rd_team_cn_name, lv4_prod_rnd_team_code,
            lv4_prod_rd_team_cn_name,lv4_prod_rnd_team_code AS
            group_code,lv4_prod_rd_team_cn_name AS group_cn_name,'LV4' AS group_level,
            CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,'#*#',lv4_prod_rnd_team_code) AS connectCode
        </if>
        <if test='groupLevel == "COA"'>
            DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
            lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,
            coa_code,coa_cn_name, coa_code AS
            group_code,coa_cn_name AS group_cn_name,'COA' AS group_level,
            CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),'#*#',coa_code) AS connectCode
        </if>
        <if test='groupLevel == "DIMENSION"'>
            <choose>
                <when test='industryOrg == "ENERGY"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,
                    dimension_code,dimension_cn_name, coa_code,coa_cn_name, dimension_code AS
                    group_code,dimension_cn_name AS group_cn_name,'DIMENSION' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' || coa_code),'#*#',dimension_code) AS connectCode
                </when>
                <when test='industryOrg == "IAS"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,
                    dimension_code,dimension_cn_name, lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name, dimension_code AS
                    group_code,dimension_cn_name AS group_cn_name,'DIMENSION' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),'#*#',dimension_code) AS connectCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "SUBCATEGORY"'>
            <choose>
                <when test='industryOrg == "ENERGY"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,coa_code,coa_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUBCATEGORY_CODE AS
                    group_code,DIMENSION_SUBCATEGORY_CN_NAME AS group_cn_name,'SUBCATEGORY' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' || coa_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectCode
                </when>
                <when test='industryOrg == "IAS"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUBCATEGORY_CODE AS
                    group_code,DIMENSION_SUBCATEGORY_CN_NAME AS group_cn_name,'SUBCATEGORY' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "SUB_DETAIL"'>
            <choose>
                <when test='industryOrg == "ENERGY"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,coa_code,coa_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,
                    DIMENSION_SUB_DETAIL_CODE AS group_code,DIMENSION_SUB_DETAIL_CN_NAME AS group_cn_name,'SUB_DETAIL' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' || coa_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE) AS connectCode
                </when>
                <when test='industryOrg == "IAS"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,
                    DIMENSION_SUB_DETAIL_CODE AS group_code,DIMENSION_SUB_DETAIL_CN_NAME AS group_cn_name,'SUB_DETAIL' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE) AS connectCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "SPART"'>
            <choose>
                <when test='industryOrg == "ENERGY"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,coa_code,coa_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,SPART_CODE,SPART_CN_NAME,
                    SPART_CODE AS group_code,SPART_CN_NAME AS group_cn_name,'SPART' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' || coa_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE,'#*#',SPART_CODE) AS connectCode
                </when>
                <when test='industryOrg == "IAS"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,SPART_CODE,SPART_CN_NAME,
                    SPART_CODE AS group_code,SPART_CN_NAME AS group_cn_name,'SPART' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE,'#*#',SPART_CODE) AS connectCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "CEG"'>
            <choose>
                <when test='industryOrg == "ENERGY"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name, coa_code,coa_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE, DIMENSION_SUB_DETAIL_CN_NAME,TOP_L3_CEG_CODE,TOP_L3_CEG_SHORT_CN_NAME,TOP_L3_CEG_CODE AS
                    group_code,TOP_L3_CEG_SHORT_CN_NAME AS group_cn_name,'CEG' AS group_level,PART_CODE,SPART_CN_NAME,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' || coa_code),DECODE(dimension_code,'','','#*#' || dimension_code),DECODE(DIMENSION_SUBCATEGORY_CODE,'','','#*#' || DIMENSION_SUBCATEGORY_CODE),DECODE(DIMENSION_SUB_DETAIL_CODE,'','','#*#' || DIMENSION_SUB_DETAIL_CODE),DECODE(SPART_CODE,'','','#*#' || SPART_CODE),'#*#',TOP_L3_CEG_CODE) AS connectCode
                </when>
                <when test='industryOrg == "IAS"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name, lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE, DIMENSION_SUB_DETAIL_CN_NAME,TOP_L3_CEG_CODE,TOP_L3_CEG_SHORT_CN_NAME,TOP_L3_CEG_CODE AS
                    group_code,TOP_L3_CEG_SHORT_CN_NAME AS group_cn_name,'CEG' AS group_level,PART_CODE,SPART_CN_NAME,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),DECODE(dimension_code,'','','#*#' || dimension_code),DECODE(DIMENSION_SUBCATEGORY_CODE,'','','#*#' || DIMENSION_SUBCATEGORY_CODE),DECODE(DIMENSION_SUB_DETAIL_CODE,'','','#*#' || DIMENSION_SUB_DETAIL_CODE),DECODE(SPART_CODE,'','','#*#' || SPART_CODE),'#*#',TOP_L3_CEG_CODE) AS connectCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "MODL"'>
            <choose>
                <when test='industryOrg == "ENERGY"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,
                    lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,coa_code,coa_cn_name,
                    dimension_code,dimension_cn_name,
                    DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,SPART_CODE,SPART_CN_NAME,
                    DIMENSION_SUB_DETAIL_CN_NAME,TOP_L3_CEG_CODE,TOP_L3_CEG_SHORT_CN_NAME,TOP_L4_CEG_CODE,TOP_L4_CEG_SHORT_CN_NAME,TOP_L4_CEG_CODE
                    AS group_code,TOP_L4_CEG_SHORT_CN_NAME AS group_cn_name,'MODL' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' || coa_code),DECODE(dimension_code,'','','#*#' || dimension_code),DECODE(DIMENSION_SUBCATEGORY_CODE,'','','#*#' || DIMENSION_SUBCATEGORY_CODE),DECODE(DIMENSION_SUB_DETAIL_CODE,'','','#*#' || DIMENSION_SUB_DETAIL_CODE),DECODE(SPART_CODE,'','','#*#' || SPART_CODE),'#*#',TOP_L3_CEG_CODE,'#*#',TOP_L4_CEG_CODE) AS connectCode
                </when>
                <when test='industryOrg == "IAS"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,
                    lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,
                    dimension_code,dimension_cn_name,
                    DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,SPART_CODE,SPART_CN_NAME,
                    DIMENSION_SUB_DETAIL_CN_NAME,TOP_L3_CEG_CODE,TOP_L3_CEG_SHORT_CN_NAME,TOP_L4_CEG_CODE,TOP_L4_CEG_SHORT_CN_NAME,TOP_L4_CEG_CODE
                    AS group_code,TOP_L4_CEG_SHORT_CN_NAME AS group_cn_name,'MODL' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),DECODE(dimension_code,'','','#*#' || dimension_code),DECODE(DIMENSION_SUBCATEGORY_CODE,'','','#*#' || DIMENSION_SUBCATEGORY_CODE),DECODE(DIMENSION_SUB_DETAIL_CODE,'','','#*#' || DIMENSION_SUB_DETAIL_CODE),DECODE(SPART_CODE,'','','#*#' || SPART_CODE),'#*#',TOP_L3_CEG_CODE,'#*#',TOP_L4_CEG_CODE) AS connectCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "CATEGORY"'>
            <choose>
                <when test='industryOrg == "ENERGY"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,
                    lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,coa_code,coa_cn_name,
                    dimension_code,dimension_cn_name,
                    DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,SPART_CODE,SPART_CN_NAME,
                    DIMENSION_SUB_DETAIL_CN_NAME,TOP_L3_CEG_CODE,TOP_L3_CEG_SHORT_CN_NAME,TOP_L4_CEG_CODE,TOP_L4_CEG_SHORT_CN_NAME,TOP_CATEGORY_CODE,TOP_CATEGORY_CN_NAME,TOP_CATEGORY_CODE
                    AS group_code,TOP_CATEGORY_CN_NAME AS group_cn_name,'CATEGORY' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' || coa_code),DECODE(dimension_code,'','','#*#' || dimension_code),DECODE(DIMENSION_SUBCATEGORY_CODE,'','','#*#' || DIMENSION_SUBCATEGORY_CODE),DECODE(DIMENSION_SUB_DETAIL_CODE,'','','#*#' || DIMENSION_SUB_DETAIL_CODE),DECODE(SPART_CODE,'','','#*#' || SPART_CODE),'#*#',TOP_L3_CEG_CODE,'#*#',TOP_L4_CEG_CODE,'#*#',top_category_code) AS connectCode
                </when>
                <when test='industryOrg == "IAS"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,
                    lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,
                    dimension_code,dimension_cn_name,
                    DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,SPART_CODE,SPART_CN_NAME,
                    DIMENSION_SUB_DETAIL_CN_NAME,TOP_L3_CEG_CODE,TOP_L3_CEG_SHORT_CN_NAME,TOP_L4_CEG_CODE,TOP_L4_CEG_SHORT_CN_NAME,TOP_CATEGORY_CODE,TOP_CATEGORY_CN_NAME,TOP_CATEGORY_CODE
                    AS group_code,TOP_CATEGORY_CN_NAME AS group_cn_name,'CATEGORY' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' || lv4_prod_rnd_team_code),DECODE(dimension_code,'','','#*#' || dimension_code),DECODE(DIMENSION_SUBCATEGORY_CODE,'','','#*#' || DIMENSION_SUBCATEGORY_CODE),DECODE(DIMENSION_SUB_DETAIL_CODE,'','','#*#' || DIMENSION_SUB_DETAIL_CODE),DECODE(SPART_CODE,'','','#*#' || SPART_CODE),'#*#',TOP_L3_CEG_CODE,'#*#',TOP_L4_CEG_CODE,'#*#',top_category_code) AS connectCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        from fin_dm_opt_foi.${tablePreFix}_dms_top_item_info_t
        where del_flag = 'N'
        AND IS_TOP_FLAG ='Y'
        AND DOUBLE_FLAG ='Y'
        AND view_flag = #{viewFlag,jdbcType=VARCHAR}
        AND caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        <if test='industryOrg != null and industryOrg != "" and industryOrg == "ICT"'>
            AND version_id = #{monthVersionId,jdbcType=NUMERIC}
        </if>
        <if test='industryOrg != null and industryOrg != "" and industryOrg == "ENERGY"'>
            AND version_id = #{energyMonthVersionId,jdbcType=NUMERIC}
        </if>
        <if test='overseaFlag != null and overseaFlag != ""'>
            AND oversea_flag = #{overseaFlag}
        </if>
        <if test='lv0ProdListCode != null and lv0ProdListCode != ""'>
            AND lv0_prod_list_code = #{lv0ProdListCode}
        </if>
        <if test='(groupLevel == "DIMENSION" or groupLevel == "SUBCATEGORY" or groupLevel == "SUB_DETAIL" or groupLevel == "SPART") and (viewFlag =="0" or viewFlag =="1" or viewFlag =="2" or viewFlag =="9") and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='(groupLevel == "DIMENSION" or groupLevel == "SUBCATEGORY" or groupLevel == "SUB_DETAIL" or groupLevel == "SPART") and (viewFlag =="3" or viewFlag =="4" or viewFlag =="5" or viewFlag =="10") and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='(groupLevel == "CEG" or groupLevel == "MODL" or groupLevel == "CATEGORY") and (viewFlag =="0" or viewFlag =="1" or viewFlag =="2" or viewFlag =="9") and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='(groupLevel == "CEG" or groupLevel == "MODL" or groupLevel == "CATEGORY") and (viewFlag =="3" or viewFlag =="4" or viewFlag =="5" or viewFlag =="10") and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel == "LV1" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel == "LV2" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        
        <if test='lv1ProdRndTeamCodeSet != null and lv1ProdRndTeamCodeSet.size() > 0'>
            <foreach collection='lv1ProdRndTeamCodeSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv2ProdRndTeamCodeSet != null and lv2ProdRndTeamCodeSet.size() > 0'>
            <foreach collection='lv2ProdRndTeamCodeSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv3ProdRndTeamCodeSet != null and lv3ProdRndTeamCodeSet.size() > 0'>
            <foreach collection='lv3ProdRndTeamCodeSet' item="code" open="AND lv3_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv1CodeList != null and lv1CodeList.size() > 0'>
            <foreach collection='lv1CodeList' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv2CodeList != null and lv2CodeList.size() > 0'>
            <foreach collection='lv2CodeList' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv3CodeList != null and lv3CodeList.size() > 0'>
            <foreach collection='lv3CodeList' item="code" open="AND lv3_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='coaCodeList != null and coaCodeList.size() > 0'>
            <foreach collection='coaCodeList' item="code" open="AND coa_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionCodeList != null and dimensionCodeList.size() > 0'>
            <foreach collection='dimensionCodeList' item="code" open="AND dimension_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionSubcategoryCodeList != null and dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='dimensionSubcategoryCodeList' item="code" open="AND dimension_subcategory_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionSubDetailCodeList != null and dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='dimensionSubDetailCodeList' item="code" open="AND dimension_sub_detail_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='spartCodeList != null and spartCodeList.size() > 0'>
            <foreach collection='spartCodeList' item="code" open="AND spart_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <choose>
            <when test='groupLevel=="CEG" or groupLevel=="MODL" or groupLevel=="CATEGORY"'>
                <if test='connectDimensionCodeList != null and connectDimensionCodeList.size() > 0 and tablePreFix =="dm_foc"'>
                    <foreach collection='connectDimensionCodeList' item="code"
                             open="AND concat(lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#' ,lv3_prod_rnd_team_code,'#*#', dimension_code, '#*#',dimension_subcategory_code, '#*#', dimension_sub_detail_code,'#*#', spart_code) IN (" close=")"
                             index="index" separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='connectDimensionCodeList != null and connectDimensionCodeList.size() > 0 and tablePreFix =="dm_foc_energy"'>
                    <foreach collection='connectDimensionCodeList' item="code"
                             open="AND concat(lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#' ,lv3_prod_rnd_team_code,'#*#', coa_code,'#*#', dimension_code, '#*#',dimension_subcategory_code, '#*#', dimension_sub_detail_code,'#*#', spart_code) IN (" close=")"
                             index="index" separator=",">
                        #{code}
                    </foreach>
                </if>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='groupCodeList != null and groupCodeList.size() > 0'>
            <choose>
                <when test='groupLevel =="CATEGORY"'>
                    <foreach collection='groupCodeList' item="code" open="AND l4_ceg_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='groupLevel =="MODL"'>
                    <foreach collection='groupCodeList' item="code" open="AND l3_ceg_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='groupLevel =="CEG" and (viewFlag =="0" or viewFlag =="3" or viewFlag =="6")'>
                    <foreach collection='groupCodeList' item="code" open="AND dimension_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='groupLevel =="CEG" and (viewFlag =="1" or viewFlag =="4" or viewFlag =="7")'>
                    <foreach collection='groupCodeList' item="code" open="AND dimension_subcategory_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='groupLevel =="CEG" and (viewFlag =="2" or viewFlag =="5" or viewFlag =="8")'>
                    <foreach collection='groupCodeList' item="code" open="AND dimension_sub_detail_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='groupLevel =="CEG" and (viewFlag =="9" or viewFlag =="10" or viewFlag =="11")'>
                    <foreach collection='groupCodeList' item="code" open="AND spart_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='groupLevel =="SUBCATEGORY"'>
                    <foreach collection='groupCodeList' item="code" open="AND dimension_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='groupLevel =="SUB_DETAIL"'>
                    <foreach collection='groupCodeList' item="code" open="AND dimension_subcategory_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
    </select>
</mapper>
