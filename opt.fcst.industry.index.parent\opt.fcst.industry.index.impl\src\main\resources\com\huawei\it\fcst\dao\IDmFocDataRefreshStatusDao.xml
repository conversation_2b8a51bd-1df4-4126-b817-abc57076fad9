<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IDmFocDataRefreshStatusDao">
    <resultMap type="com.huawei.it.fcst.industry.index.vo.combination.DmFocDataRefreshStatus" id="resultMap">
        <result property="delFlag" column="del_flag"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="createdBy" column="created_by"/>
        <result property="taskId" column="task_id"/>
        <result property="roleId" column="role_id"/>
        <result property="status" column="status"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="taskFlag" column="task_flag"/>

    </resultMap>

    <sql id="tableName">
        dm_foc_data_refresh_status_t
    </sql>

    <sql id="allFields">
        del_flag,
        last_updated_by,
        creation_date,
        created_by,
        role_id,
        task_id,
        status,
        last_update_date,
        task_flag
    </sql>

    <sql id="allValues">
        #{delFlag,jdbcType=VARCHAR},
        #{lastUpdatedBy,jdbcType=BIGINT},
        #{creationDate,jdbcType=TIMESTAMP},
        #{createdBy,jdbcType=BIGINT},
        #{roleId,jdbcType=BIGINT},
        #{taskId,jdbcType=BIGINT},
        #{status,jdbcType=VARCHAR},
        #{lastUpdateDate,jdbcType=TIMESTAMP},
        #{taskFlag,jdbcType=VARCHAR}
    </sql>

    <sql id="uniqueKeyField">
        task_id=#{taskId,jdbcType=BIGINT}
    </sql>

    <select id="getDataRefrashKey" resultType="java.lang.Long">
        SELECT nextval('fin_dm_opt_foi.dm_foc_data_refresh_status_s')
    </select>

    <sql id="setValues">
        <if test='delFlag != null'>
            del_flag = #{delFlag,jdbcType=VARCHAR},
        </if>
        <if test='lastUpdatedBy != null'>
            last_updated_by = #{lastUpdatedBy,jdbcType=BIGINT},
        </if>
        <if test='creationDate != null'>
            creation_date = #{creationDate,jdbcType=TIMESTAMP},
        </if>
        <if test='createdBy != null'>
            created_by = #{createdBy,jdbcType=BIGINT},
        </if>
        <if test='status != null'>
            status = #{status,jdbcType=VARCHAR},
        </if>
        <if test='lastUpdateDate != null'>
            last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
        </if>
    </sql>

    <select id="findDmFocDataRefreshStatusById" parameterType="java.lang.Long" resultMap="resultMap">
        SELECT
        <include refid="allFields"/>
        FROM fin_dm_opt_foi.dm_foc_data_refresh_status_t
        WHERE
         task_id = #{taskId,jdbcType=BIGINT}
        <if test='taskFlag != null and taskFlag !=""'>
            and task_flag = #{taskFlag}
        </if>
    </select>

    <select id="findDmFocDataRefreshStatus" resultMap="resultMap">
        SELECT
        <include refid="allFields"/>
        FROM fin_dm_opt_foi.dm_foc_data_refresh_status_t
        where status = 'TASK_INIT'
        <if test='userId != null'>
             and created_by = #{userId}
        </if>
        <if test='roleId != null'>
            and role_id = #{roleId}
        </if>
        <if test='taskFlag != null and taskFlag!=""'>
            and task_flag = #{taskFlag}
        </if>
        <if test='taskFlag == null'>
            and task_flag = 'COMB'
        </if>
        order by creation_date desc limit 1
    </select>

    <select id="findDmFocDataRefreshStatusByDay" resultMap="resultMap">
        SELECT
        <include refid="allFields"/>
        FROM fin_dm_opt_foi.dm_foc_data_refresh_status_t
        where (status = 'TASK_SUCCESS' or status = 'TASK_FAIL')
        <if test='userId != null'>
            and created_by = #{userId}
        </if>
        <if test='roleId != null'>
            and role_id = #{roleId}
        </if>
        <if test='taskFlag != null and taskFlag!=""'>
            and task_flag = #{taskFlag}
        </if>
        and TO_CHAR(creation_date , 'yyyyMMdd') = TO_CHAR(NOW() , 'yyyyMMdd')
        order by creation_date desc limit 1
    </select>

    <insert id="createDmFocDataRefreshStatus">
        INSERT INTO fin_dm_opt_foi.dm_foc_data_refresh_status_t
        (<include refid="allFields"/>)
        VALUES
        (<include refid="allValues"/>)
    </insert>

    <update id="updateDmFocDataRefreshStatus">
        UPDATE fin_dm_opt_foi.dm_foc_data_refresh_status_t
        <set> <include refid="setValues"/></set>
        WHERE
        <include refid="uniqueKeyField"/>
    </update>
</mapper>
