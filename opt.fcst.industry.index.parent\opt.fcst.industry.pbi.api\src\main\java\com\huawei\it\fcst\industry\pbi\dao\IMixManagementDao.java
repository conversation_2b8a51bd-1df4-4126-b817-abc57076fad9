/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.dao;

import com.huawei.it.fcst.industry.pbi.vo.mix.DmFocMixResultVO;
import com.huawei.it.fcst.industry.pbi.vo.mix.MixSearchVO;

import java.util.List;

/**
 * IMixManagementDao Class
 *
 * <AUTHOR>
 * @since 2024/7/5
 */
public interface IMixManagementDao {

    List<DmFocMixResultVO> distributeCustomChartMultiType(MixSearchVO mixSearchVO);

    List<DmFocMixResultVO> distributeChartMultiType(MixSearchVO mixSearchVO);

    List<DmFocMixResultVO> findCustomPriceIndexMultiType(MixSearchVO mixSearchVO);

    List<DmFocMixResultVO> findPriceIndexMultiType(MixSearchVO mixSearchVO);

    List<DmFocMixResultVO> findPriceIndexMinLevelMultiType(MixSearchVO mixSearchVO);

    List<DmFocMixResultVO> getSummaryCombCurrentPriceIndex(MixSearchVO mixSearchVO);
}
