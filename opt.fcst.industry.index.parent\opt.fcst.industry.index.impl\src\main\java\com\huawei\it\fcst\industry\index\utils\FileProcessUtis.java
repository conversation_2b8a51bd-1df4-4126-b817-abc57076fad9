/*
 *
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.industry.index.utils;

import com.huawei.his.jalor.helper.StoreHelper;
import com.huawei.it.his.jalor.store.IStoreService;
import com.huawei.it.his.jalor.store.StoreMetadata;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @since 2022/10/28
 */
public class FileProcessUtis {
    private static final Logger LOGGER = LoggerFactory.getLogger(FileProcessUtis.class);

    /**
     * 上传文件
     *
     * @param file 参数
     * @param userId 参数
     * @return String
     * @throws CommonApplicationException
     */
    public static String uploadToS3(File file, String userId) throws CommonApplicationException {
        final IStoreService storeService = StoreHelper.getStoreService();
        StoreMetadata metadata = new StoreMetadata();
        metadata.setFileName(file.getName());
        metadata.setFileSize(file.length());
        metadata.setUserId(userId);
        try (InputStream is = new FileInputStream(file)) {
            return storeService.store(is, metadata); // 带S3的文件路径
        } catch (IOException e) {
            LOGGER.error("can't found the file, {}", e.getMessage());
            throw new CommonApplicationException("com.huawei.findl.comomn.00010008");
        }
    }

}
