/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.utils;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.security.NoSuchAlgorithmException;

/**
 * AesGcmUtilTest Class
 *
 * <AUTHOR>
 * @since 2023/4/26
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class AesGcmUtilTest {
    @InjectMocks
    private AesGcmUtil aesGcmUtil;

    @Test
    public void encryptGcm() {
        String hexPlainKey=null;
        String plainData="5521";
        String s = aesGcmUtil.encryptGcm(hexPlainKey, plainData);
        Assert.assertNotNull(s);
    }

    @Test
    public void encryptGcm2Test() {
        String hexPlainKey="787878";
        String plainData="5521";
        String s= null;
        try {
            s = aesGcmUtil.encryptGcm(hexPlainKey, plainData);
        } catch (Exception e) {
            e.printStackTrace();
        }
        Assert.assertNull(s);
    }

    @Test
    public void generateSecureRandomByte() throws NoSuchAlgorithmException {
        int byteSize=1023;
        byte[] bytes = aesGcmUtil.generateSecureRandomByte(byteSize);
        Assert.assertNotNull(bytes);
    }

    @Test
    public void getBase64Encode() {
        String hexPlainKey=null;
        String base64Encode = aesGcmUtil.getBase64Encode(hexPlainKey);
        Assert.assertNotNull(base64Encode);
    }

    @Test
    public void getBase64Encode2Test() {
        String hexPlainKey="12333";
        String base64Encode = aesGcmUtil.getBase64Encode(hexPlainKey);
        Assert.assertNotNull(base64Encode);
    }
}