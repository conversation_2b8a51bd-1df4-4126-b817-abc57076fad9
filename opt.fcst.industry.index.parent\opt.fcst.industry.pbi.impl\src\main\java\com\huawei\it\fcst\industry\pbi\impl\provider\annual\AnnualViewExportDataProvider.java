/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.provider.annual;

import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.industry.pbi.dao.IAnnualCustomDao;
import com.huawei.it.fcst.industry.pbi.enums.GroupLevelEnum;
import com.huawei.it.fcst.industry.pbi.impl.annual.AnnualAmpPbiService;
import com.huawei.it.fcst.export.IExcelExportDataProvider;
import com.huawei.it.fcst.industry.pbi.utils.FcstIndustryUtil;
import com.huawei.it.fcst.industry.pbi.vo.annual.AnnualAnalysisVO;
import com.huawei.it.fcst.industry.pbi.vo.annual.DmFocAnnualAmpVO;
import com.huawei.it.fcst.export.vo.ExcelExportContext;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.exception.impl.CommonApplicationException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import javax.inject.Inject;
import javax.inject.Named;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * AnnualViewExportDataProvider Class 导出 成本涨跌一览表
 *
 * <AUTHOR>
 * @since 2024/7/5
 */
@Named("IExcelExport.AnnualViewExportDataProvider")
public class AnnualViewExportDataProvider implements IExcelExportDataProvider {

    @Inject
    private IAnnualCustomDao annualCustomDao;

    @Inject
    private AnnualAmpPbiService annualAmpPbiService;

    @Override
    public List<?> getData(Serializable conditionObject, PageVO pageVO) throws CommonApplicationException {

        AnnualAnalysisVO annualVO = (AnnualAnalysisVO) conditionObject;
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        BeanUtils.copyProperties(annualVO, annualAnalysisVO);
        annualAnalysisVO.setParentCodeList(annualAnalysisVO.getGroupCodeList());
        annualAnalysisVO.setParentLevel(annualAnalysisVO.getGroupLevel());

        String nextGroupLevel = FcstIndustryUtil.getViewNextGroupLevel(annualAnalysisVO);
        annualAnalysisVO.setGroupLevel(nextGroupLevel);
        // 设置重量级团队code为null
        annualAmpPbiService.resetProdRndTeamCode(annualAnalysisVO);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();

        List<DmFocAnnualAmpVO> dmFocAnnualAmpList = new ArrayList<>();
        String combTablePreFix = annualAmpPbiService.getCombTablePreFix(annualAnalysisVO);
        // 虚化的表取值
        annualAnalysisVO.setCombTablePreFix(combTablePreFix);
        if (annualAnalysisVO.getIsNeedBlur()) {
            // 走虚化且是最细力度的时候，需要取正常结果表数据
            if (CommonConstant.MIN_GROUP_LEVEL.stream().anyMatch(level -> level.equals(annualAnalysisVO.getParentLevel()))) {
                dmFocAnnualAmpVOList.addAll(annualAmpPbiService.overviewListMinLevelNotContainsCustom(annualAnalysisVO));
                annualAmpPbiService.setGroupCnNameMixLevel(dmFocAnnualAmpVOList);
            } else {
                dmFocAnnualAmpVOList.addAll(annualCustomDao.industryCombCustomExcel(annualAnalysisVO));
                annualAmpPbiService.setParentCnNameDimensionLevel(dmFocAnnualAmpVOList);
            }
        }
        if (!annualAnalysisVO.getIsContainComb()) {
            if (!CommonConstant.MIN_GROUP_LEVEL.contains(annualAnalysisVO.getParentLevel()) && CollectionUtils.isNotEmpty(annualAnalysisVO.getParentCodeList())) {
                dmFocAnnualAmpVOList.addAll(annualAmpPbiService.overviewListNotContainsCustom(annualAnalysisVO));
                annualAmpPbiService.setGroupCnNameDimensionLevel(dmFocAnnualAmpVOList);
            }
        } else {
            // 只选汇总组合
            List<DmFocAnnualAmpVO> dmAnnualAmpList = annualCustomDao.industrySummaryCombList(annualAnalysisVO);
            if (annualAnalysisVO.getIsMultipleSelect()) {
                dmAnnualAmpList.forEach(dm -> dm.setGroupCnName(dm.getGroupCnName() + "(" + dm.getParentCnName() + "(" + dm.getCustomCnName() + ")" + ")"));
            } else {
                dmFocAnnualAmpVOList.forEach(dm->dm.setGroupCnName(dm.getGroupCnName()+"("+dm.getParentCnName() +")"));
            }
            dmFocAnnualAmpVOList.addAll(dmAnnualAmpList);
            annualAnalysisVO.setGroupLevel(GroupLevelEnum.SPART.getValue());
        }
        // 展示量纲信息时需要加上对应的名称，格式为：code空格name
        setDimensionInfo(dmFocAnnualAmpVOList);
        List<String> threeYearList = annualAnalysisVO.getYearList().stream().sorted(Comparator.comparing(String::valueOf)).collect(Collectors.toList());
        for (String year : threeYearList) {
            annualAnalysisVO.setPeriodYear(year);
            // 筛选出对应年份
            List<DmFocAnnualAmpVO> annualAmpAndWeightYear = dmFocAnnualAmpVOList.stream().filter(annual -> annualAnalysisVO.getPeriodYear().equals(annual.getPeriodYear())).collect(Collectors.toList());
            // 设置权重*涨跌序号
            List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = annualAmpPbiService.dealGroupLevelWeightAndAmp(annualAmpAndWeightYear, annualAnalysisVO, false, null);
            // 设置无效的涨跌幅提示语
            annualAmpPbiService.setNoEffectiveAmp(dmFocAnnualAmpVOResult, annualAnalysisVO, "excel");
            dmFocAnnualAmpList.addAll(dmFocAnnualAmpVOResult);
        }
        return dmFocAnnualAmpList;
    }

    private void setDimensionInfo(List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList) {
        dmFocAnnualAmpVOList.stream().forEach(item -> {
            if (StringUtils.isNotBlank(item.getDimensionCode())) {
                item.setDimensionCode(item.getDimensionCode() + " " + item.getDimensionCnName());
            }
            if (StringUtils.isNotBlank(item.getDimensionSubCategoryCode())) {
                item.setDimensionSubCategoryCode(item.getDimensionSubCategoryCode() + " " + item.getDimensionSubCategoryCnName());
            }
        });
    }

    @Override
    public Map<String, Object> getHeader(Serializable conditionObject, Map<String, Object> parameters) throws ApplicationException {
        ExcelExportContext context = (ExcelExportContext) conditionObject;
        AnnualAnalysisVO annualAnalysisVO = (AnnualAnalysisVO) context.getConditionObject();
        // 自定义表头
        Map<String, Object> headMap = new HashMap<>();
        headMap.put("displayName", annualAnalysisVO.getDisplayName());
        headMap.put("name", annualAnalysisVO.getNextGroupName());
        headMap.put("costTypeCnName", annualAnalysisVO.getCostTypeCnName());
        headMap.put("granularityTypeCnName", annualAnalysisVO.getGranularityTypeCnName());
        headMap.put("softwareMarkCnName", annualAnalysisVO.getSoftwareMarkCnName());
        headMap.put("overseaFlagCnName", annualAnalysisVO.getOverseaFlagCnName());
        headMap.put("bgCnName", annualAnalysisVO.getBgCnName());
        headMap.put("actualMonth", annualAnalysisVO.getActualMonth());
        headMap.put("regionCnName", annualAnalysisVO.getRegionCnName());
        headMap.put("repofficeCnName", annualAnalysisVO.getRepofficeCnName());
        headMap.put("mainFlagCnName", annualAnalysisVO.getMainFlagCnName());
        headMap.put("orderCnName", annualAnalysisVO.getOrderCnName());
        headMap.put("codeAttributesCnName", annualAnalysisVO.getCodeAttributesCnName());
        headMap.put("ytdFlagCnName", annualAnalysisVO.getYtdFlagCnName());
        return headMap;
    }
}
