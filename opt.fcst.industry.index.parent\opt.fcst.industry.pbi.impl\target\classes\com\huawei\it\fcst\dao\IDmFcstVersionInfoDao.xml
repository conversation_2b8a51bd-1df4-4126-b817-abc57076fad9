<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.pbi.dao.IDmFcstVersionInfoDao">
    <resultMap type="com.huawei.it.fcst.industry.pbi.vo.version.DmFcstVersionInfoDTO" id="resultMap">
        <result property="delFlag" column="del_flag"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="versionType" column="version_type"/>
        <result property="parentVersionId" column="parent_version_id"/>
        <result property="dataType" column="data_type"/>
        <result property="version" column="version"/>
        <result property="versionId" column="version_id"/>
        <result property="creationDate" column="creation_date"/>
        <result property="version" column="version"/>
        <result property="createdBy" column="created_by"/>
        <result property="status" column="status"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="step" column="step"/>
        <result property="isRunning" column="is_running"/>
    </resultMap>

    <sql id="allFields">
        del_flag,
        last_updated_by,
        version_type,
        parent_version_id,
        data_type,
        version_id,
        creation_date,
        version,
        created_by,
        status,
        last_update_date,
        step,
        is_running
    </sql>

    <sql id="allValues">
        'N',
        #{lastUpdatedBy,jdbcType=VARCHAR},
        #{versionType,jdbcType=VARCHAR},
        #{parentVersionId,jdbcType=NUMERIC},
        #{dataType,jdbcType=VARCHAR},
        #{versionId,jdbcType=NUMERIC},
        NOW(),
        #{version,jdbcType=VARCHAR},
        #{createdBy,jdbcType=VARCHAR},
        #{status,jdbcType=NUMERIC},
        NOW(),
        #{step,jdbcType=INTEGER},
        #{isRunning,jdbcType=VARCHAR}
    </sql>

    <sql id="uniqueKeyField">
        version_id=#{versionId,jdbcType=NUMERIC}
    </sql>

    <sql id="setValues">
        <if test='delFlag != null'>
            del_flag = #{delFlag,jdbcType=VARCHAR},
        </if>
        <if test='lastUpdatedBy != null'>
            last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
        </if>
        <if test='versionType != null'>
            version_type = #{versionType,jdbcType=VARCHAR},
        </if>
        <if test='parentVersionId != null'>
            parent_version_id = #{parentVersionId,jdbcType=NUMERIC},
        </if>
        <if test='dataType != null'>
            data_type = #{dataType,jdbcType=VARCHAR},
        </if>
        <if test='versionId != null'>
            version_id = #{versionId,jdbcType=NUMERIC},
        </if>
        <if test='creationDate != null'>
            creation_date = #{creationDate,jdbcType=TIMESTAMP},
        </if>
        <if test='version != null'>
            version = #{version,jdbcType=VARCHAR},
        </if>
        <if test='createdBy != null'>
            created_by = #{createdBy,jdbcType=VARCHAR},
        </if>
        <if test='status != null'>
            status = #{status,jdbcType=NUMERIC},
        </if>
        <if test='lastUpdateDate != null'>
            last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
        </if>
        <if test='step != null'>
            step = #{step,jdbcType=INTEGER},
        </if>
        <if test='isRunning != null'>
            is_running = #{isRunning,jdbcType=VARCHAR},
        </if>
    </sql>

    <sql id="searchFields">
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test='_parameter.get("0").delFlag != null'>
                AND del_flag LIKE CONCAT(CONCAT('%', #{0.delFlag,jdbcType=VARCHAR}) ,'%')
            </if>
            <if test='_parameter.get("0").lastUpdatedBy != null'>
                AND last_updated_by LIKE CONCAT(CONCAT('%', #{0.lastUpdatedBy,jdbcType=VARCHAR}) ,'%')
            </if>
            <if test='_parameter.get("0").versionType != null'>
                AND version_type LIKE CONCAT(CONCAT('%', #{0.versionType,jdbcType=VARCHAR}) ,'%')
            </if>
            <if test='_parameter.get("0").parentVersionId != null'>
                AND parent_version_id=#{0.parentVersionId,jdbcType=NUMERIC}
            </if>
            <if test='_parameter.get("0").dataType != null'>
                AND data_type LIKE CONCAT(CONCAT('%', #{0.dataType,jdbcType=VARCHAR}) ,'%')
            </if>
            <if test='_parameter.get("0").versionId != null'>
                AND version_id=#{0.versionId,jdbcType=NUMERIC}
            </if>
            <if test='_parameter.get("0").creationDate != null'>
                AND creation_date=#{0.creationDate,jdbcType=TIMESTAMP}
            </if>
            <if test='_parameter.get("0").version != null'>
                AND version LIKE CONCAT(CONCAT('%', #{0.version,jdbcType=VARCHAR}) ,'%')
            </if>
            <if test='_parameter.get("0").createdBy != null'>
                AND created_by LIKE CONCAT(CONCAT('%', #{0.createdBy,jdbcType=VARCHAR}) ,'%')
            </if>
            <if test='_parameter.get("0").status != null'>
                AND status=#{0.status,jdbcType=NUMERIC}
            </if>
            <if test='_parameter.get("0").lastUpdateDate != null'>
                AND last_update_date=#{0.lastUpdateDate,jdbcType=TIMESTAMP}
            </if>
            <if test='_parameter.get("0").step != null'>
                AND step=#{0.step,jdbcType=INTEGER}
            </if>
        </trim>
    </sql>
    <select id="findVersionIdByDataType" resultMap="resultMap">
        SELECT version_id,
               last_updated_by,
               version_type,
               parent_version_id,
               data_type,
               creation_date,
               version,
               created_by,
               status,
               last_update_date
        FROM fin_dm_opt_foi.dm_fcst_ict_version_info_t
        WHERE del_flag = 'N'
          AND is_running = 'N'
          AND status = 1
          AND data_type = #{dataType}
          AND version_type IN ('AUTO', 'FINAL')
        ORDER BY creation_date DESC
            LIMIT 1
    </select>

    <select id="findDmFocVersionDTOById" parameterType="java.lang.Long" resultMap="resultMap">
        SELECT
        <include refid="allFields"/>
        FROM fin_dm_opt_foi.dm_fcst_ict_version_info_t
        WHERE
        <include refid="uniqueKeyField"/>
        and del_flag = 'N'
        AND is_running = 'N'
    </select>

    <select id="findPlanVersionList" resultMap="resultMap">
        SELECT
        <include refid="allFields"/>
        FROM fin_dm_opt_foi.dm_fcst_ict_version_info_t
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            del_flag = 'N'
            AND is_running = 'N'
            <if test='lastUpdateStr != null'>
                and to_char(last_update_date,'yyyy-mm-dd') =#{lastUpdateStr,jdbcType=DATE}
            </if>
            <if test='status != null'>
                AND status = #{status,jdbcType=NUMERIC}
            </if>
            <if test='versionType != null'>
                AND version_type = #{versionType,jdbcType=VARCHAR}
            </if>
            <if test='dataType != null'>
                AND data_type = #{dataType,jdbcType=VARCHAR}
            </if>
            <if test='versionId != null'>
                AND version_id = #{versionId,jdbcType=NUMERIC}
            </if>
            <if test='version != null'>
                AND version =#{version,jdbcType=VARCHAR}
            </if>
        </trim>
        order by last_update_date desc
    </select>

    <select id="getVersionKey" resultType="java.lang.Long">
        SELECT fin_dm_opt_foi.dm_fcst_ict_version_info_s.nextval
    </select>

    <select id="findMixActualMonth" resultType="java.lang.String">
        SELECT IFNULL(max(period_id),0)
        FROM fin_dm_opt_foi.dm_fcst_ict_psp_${granularityType}_mon_cost_idx_t
        WHERE del_flag = 'N'
          AND version_id =(
            SELECT version_id
            FROM fin_dm_opt_foi.dm_fcst_ict_version_info_t
            WHERE del_flag = 'N'
              AND is_running = 'N'
              AND status = 1
              AND data_type = 'MONTH'
              AND version_type IN ('AUTO', 'FINAL')
            ORDER BY creation_date DESC
            LIMIT 1
            )
    </select>

    <select id="findAnnualActualMonth" resultType="java.lang.String">
        SELECT IFNULL(max(period_id),0)
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.tablePreFix}_mon_cost_idx_t
        WHERE del_flag = 'N'
          AND version_id =(
            SELECT version_id
            FROM fin_dm_opt_foi.dm_fcst_ict_version_info_t
            WHERE del_flag = 'N'
              AND is_running = 'N'
              AND status = 1
              AND data_type = 'MONTH'
              AND version_type IN ('AUTO', 'FINAL')
            ORDER BY creation_date DESC
            LIMIT 1
            )
    </select>

    <update id="updateDmFcstVersionInfoDTO" parameterType="com.huawei.it.fcst.industry.pbi.vo.version.DmFcstVersionInfoDTO">
        UPDATE fin_dm_opt_foi.dm_fcst_ict_version_info_t
        <set> <include refid="setValues"/></set>
        WHERE
        <include refid="uniqueKeyField"/>
    </update>

    <insert id="createDmFcstVersionInfoDTO" parameterType="com.huawei.it.fcst.industry.pbi.vo.version.DmFcstVersionInfoDTO">
        INSERT INTO fin_dm_opt_foi.dm_fcst_ict_version_info_t
        (<include refid="allFields"/>)
        VALUES
        (<include refid="allValues"/>)
    </insert>

    <select id="findVersionListByVerName" resultMap="resultMap">
        SELECT <include refid="allFields"/>
        FROM fin_dm_opt_foi.dm_fcst_ict_version_info_t
        WHERE del_flag = 'N'
        <if test='versionType != null and versionType != ""'>
            AND version_type = #{versionType,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            AND version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='parentVersionId != null'>
            AND parent_version_id =#{parentVersionId,jdbcType=NUMERIC}
        </if>
        <if test='dataType != null and dataType != ""'>
            AND data_type= #{dataType,jdbcType=VARCHAR}
        </if>
        <if test='status != null'>
            AND status = #{status,jdbcType=NUMERIC}
        </if>
        <if test='version != null and version != ""'>
            AND SUBSTR(version, 0, 8) = #{version,jdbcType=VARCHAR}
        </if>
        ORDER BY version DESC
    </select>

    <select id="findVersionList" resultMap="resultMap">
        SELECT <include refid="allFields"/>
        FROM fin_dm_opt_foi.dm_fcst_ict_version_info_t
        WHERE del_flag = 'N'
        <if test='versionType != null and versionType != ""'>
            AND version_type = #{versionType,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            AND version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='parentVersionId != null'>
            AND parent_version_id =#{parentVersionId,jdbcType=NUMERIC}
        </if>
        <if test='dataType != null and dataType != ""'>
            AND data_type= #{dataType,jdbcType=VARCHAR}
        </if>
        <if test='version != null and version != ""'>
            AND version LIKE CONCAT(CONCAT('%', #{version,jdbcType=VARCHAR}) ,'%')
        </if>
        <if test='status != null'>
            AND status = #{status,jdbcType=NUMERIC}
        </if>
        <if test='step != null'>
            AND step = #{step,jdbcType=INTEGER}
        </if>
        ORDER BY last_update_date DESC
    </select>

</mapper>
