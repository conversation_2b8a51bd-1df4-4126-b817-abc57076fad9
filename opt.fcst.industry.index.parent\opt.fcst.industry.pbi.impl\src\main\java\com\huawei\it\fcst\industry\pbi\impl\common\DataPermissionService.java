/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.common;

import com.huawei.it.fcst.industry.pbi.constant.CommonConstant;
import com.huawei.it.fcst.constant.Constant;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstIndusDimInfoDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstIrbDimInfoDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstProdDimInfoDao;
import com.huawei.it.fcst.industry.pbi.enums.IndustryConstEnum;
import com.huawei.it.fcst.industry.pbi.service.common.IDataPermissionService;
import com.huawei.it.fcst.industry.pbi.vo.common.CommonBaseVO;
import com.huawei.it.fcst.industry.pbi.vo.common.DataPermissionsVO;
import com.huawei.it.fcst.industry.pbi.vo.drop.DmFcstDimInfoVO;
import com.huawei.it.fcst.util.UserInfoUtils;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.KeyValuePairVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.lookup.LookupItemVO;
import com.huawei.it.jalor5.lookup.service.ILookupItemQueryService;
import com.huawei.it.jalor5.security.ProgramItemVO;
import com.huawei.it.jalor5.security.ProgramVO;
import com.huawei.it.jalor5.security.RoleVO;
import com.huawei.it.jalor5.security.UserVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Named;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/8
 */
@Slf4j
@Named("dataPermissionService")
@JalorResource(code = "dataPermissionService", desc = "NEW ICT-用户数据范围")
public class DataPermissionService implements IDataPermissionService {

    @Autowired
    private ILookupItemQueryService lookupItemQueryService;

    @Autowired
    private IDmFcstIrbDimInfoDao dmFcstIrbDimInfoDao;

    @Autowired
    private IDmFcstIndusDimInfoDao dmFcstIndusDimInfoDao;

    @Autowired
    private IDmFcstProdDimInfoDao dmFcstProdDimInfoDao;

    public static final String NO_PERMISSION = "NO_PERMISSION";

    public static final String ALL_CONDITION = "@ALLCONDITION@";

    @Override
    @JalorOperation(code = "getIndustryCurrentRoleDataPermission", desc = "查询产业成本当前登录角色数据范围权限")
    public DataPermissionsVO getCurrentRoleDataPermission() {
        log.info(">>>Begin CommonService::getCurrentRoleDataPermission");
        DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        log.info(">>>CurrentRoleInfo:<{}-{}>", currentRole.getRoleId(), currentRole.getRoleName());
        dataPermissionsVO.setRoleId(currentRole.getRoleId());
        dataPermissionsVO.setRoleName(currentRole.getRoleName());
        List<ProgramVO> currentPrograms = currentUser.getCurrentPrograms();
        Set<String> dimensionValues = new HashSet<>();
        Set<String> costTypeValues = new HashSet<>();
        Set<String> pspCostTypeValues = new HashSet<>();
        // 判断是否总体查看人
        List<LookupItemVO> overallViewerRoleLsit = null;
        try {
            overallViewerRoleLsit = lookupItemQueryService.findItemListByClassify("OVERALL_VIEWER_ROLE");
        } catch (ApplicationException ex) {
            log.error("get lookupItemQueryService error: {}", ex.getMessage());
        }
        List<String> roleNameList = overallViewerRoleLsit.stream().map(LookupItemVO::getItemName).collect(Collectors.toList());
        if (roleNameList.contains(currentRole.getRoleName())) {
            dimensionValues.add(CommonConstant.PAGE_FLAG_ALL);
            setAllCostTypeValue(dataPermissionsVO);
        }
        for (ProgramVO programVO : currentPrograms) {
            Set<String> singleDimensionValues = new HashSet<>();
            List<ProgramItemVO> programItemVOList = programVO.getItems();
            for (ProgramItemVO programItemVO : programItemVOList) {
                // 依据角色不同设置维度信息
                setDimensionWithRole(dataPermissionsVO, currentRole, dimensionValues, singleDimensionValues, programItemVO);
                // 成本类型数据范围
                setCostTypeSet(costTypeValues, pspCostTypeValues, programItemVO,dataPermissionsVO);
            }
        }
        setAllDimensionPermission(dataPermissionsVO, dimensionValues);
        return dataPermissionsVO;
    }

    private void setDimensionWithRole(DataPermissionsVO dataPermissionsVO, RoleVO currentRole, Set<String> dimensionValues, Set<String> singleDimensionValues, ProgramItemVO programItemVO) {
        // 角色区分
        if(CommonConstant.SPECIAL_ROLES.contains(currentRole.getRoleName())){
            if (Constant.StrEnum.PROD_DIMENSION_CODE.getValue().equals(programItemVO.getDimension().getDimensionCode())) {
                dimensionValues.addAll(programItemVO.getValues().stream().map(KeyValuePairVO::getKey).collect(Collectors.toSet()));
                singleDimensionValues.addAll(programItemVO.getValues().stream().map(KeyValuePairVO::getKey).collect(Collectors.toSet()));
            }
            if (CommonConstant.REGION_ANALYST_IND.equals(currentRole.getRoleName()) && Constant.StrEnum.LOCATION_DIMENSION_CODE.getValue().equals(programItemVO.getDimension().getDimensionCode())) {
                setLocationDimensionPermission(dataPermissionsVO, programItemVO, singleDimensionValues);
            }
        } else {
            // 其余角色的数据范围
            if (Constant.StrEnum.DIMENSION_CODE.getValue().equals(programItemVO.getDimension().getDimensionCode())) {
                dimensionValues.addAll(programItemVO.getValues().stream().map(KeyValuePairVO::getKey).collect(Collectors.toSet()));
            }
        }
    }

    private void setCostTypeSet(Set<String> costTypeValues, Set<String> pspCostTypeValues, ProgramItemVO programItemVO,DataPermissionsVO dataPermissionsVO) {
        if (Constant.StrEnum.COST_TYPE_CODE.getValue().equals(programItemVO.getDimension().getDimensionCode())) {
            costTypeValues.addAll(programItemVO.getValues().stream().map(KeyValuePairVO::getKey).collect(Collectors.toSet()));
            if (CollectionUtils.isEmpty(costTypeValues) || costTypeValues.contains(CommonConstant.PAGE_FLAG_ALL) || costTypeValues.contains(ALL_CONDITION)) {
                setAllCostTypeValue(dataPermissionsVO);
            } else {
                dataPermissionsVO.setCostTypeSet(costTypeValues);
            }
        } else if (Constant.StrEnum.PSP_COST_TYPE_CODE.getValue().equals(programItemVO.getDimension().getDimensionCode())) {
                pspCostTypeValues.addAll(programItemVO.getValues().stream().map(KeyValuePairVO::getKey).collect(Collectors.toSet()));
                if (pspCostTypeValues.contains(CommonConstant.PAGE_FLAG_ALL) || pspCostTypeValues.contains(ALL_CONDITION)) {
                    String[] costTypeArr = {IndustryConstEnum.COST_TYPE.PSP.getValue()};
                    Set<String> costTypeList = Arrays.stream(costTypeArr).collect(Collectors.toSet());
                    dataPermissionsVO.setCostTypeSet(costTypeList);
                } else {
                    dataPermissionsVO.setCostTypeSet(pspCostTypeValues);
                }
        } else {
            if (!CommonConstant.SPECIAL_ROLES.contains(dataPermissionsVO.getRoleName())) {
                setAllCostTypeValue(dataPermissionsVO);
            }
        }
    }

    private void setAllCostTypeValue(DataPermissionsVO dataPermissionsVO) {
        String [] allCostTypeArr = {IndustryConstEnum.COST_TYPE.PSP.getValue(), IndustryConstEnum.COST_TYPE.STD.getValue()};
        Set<String> allCostTypeList = Arrays.stream(allCostTypeArr).collect(Collectors.toSet());
        dataPermissionsVO.setCostTypeSet(allCostTypeList);
    }

    private void setAllDimensionPermission(DataPermissionsVO dataPermissionsVO, Set<String> dimensionValues) {
        if (CollectionUtils.isNotEmpty(dimensionValues)) {
            if (!dimensionValues.contains(CommonConstant.PAGE_FLAG_ALL) && !dimensionValues.contains(ALL_CONDITION)) {
                // 解析维度code编码
                parsingDimensionCode(dataPermissionsVO, dimensionValues);
            }
        } else {
            dataPermissionsVO.getLv0DimensionSet().add(NO_PERMISSION);
            dataPermissionsVO.getLv1DimensionSet().add(NO_PERMISSION);
            dataPermissionsVO.getLv2DimensionSet().add(NO_PERMISSION);
            dataPermissionsVO.getLv3DimensionSet().add(NO_PERMISSION);
            dataPermissionsVO.getOverseaFlagDimensionSet().add(NO_PERMISSION);
            dataPermissionsVO.getRegionCodeDimensionSet().add(NO_PERMISSION);
            dataPermissionsVO.getRepofficeCodeDimensionSet().add(NO_PERMISSION);
        }
    }

    private void setLocationDimensionPermission(DataPermissionsVO dataPermissionsVO, ProgramItemVO programItemVO, Set<String> singleDimensionValues) {
        Set<String> dimensionValues = new HashSet<>();
        dimensionValues.addAll(programItemVO.getValues().stream().map(KeyValuePairVO::getKey).collect(Collectors.toSet()));
        DataPermissionsVO singlePermissionsVO = new DataPermissionsVO();
        if (CollectionUtils.isNotEmpty(dimensionValues)) {
            if (!dimensionValues.contains(CommonConstant.PAGE_FLAG_ALL) && !dimensionValues.contains(ALL_CONDITION)) {
                // 解析 国内海外，地区部，代表处
                // 创建一个新vo，设置为当前变量，第二个循环进来变成new vo
                parsingLocationDimensionCode(dataPermissionsVO, dimensionValues, singlePermissionsVO);
            }
        } else {
            dataPermissionsVO.getOverseaFlagDimensionSet().add(NO_PERMISSION);
            dataPermissionsVO.getRegionCodeDimensionSet().add(NO_PERMISSION);
            dataPermissionsVO.getRepofficeCodeDimensionSet().add(NO_PERMISSION);
        }
        // 组装map
        combineLocationMap(singleDimensionValues, singlePermissionsVO, dataPermissionsVO);
    }

    private void combineLocationMap(Set<String> singleDimensionValues, DataPermissionsVO singlePermissionsVO, DataPermissionsVO dataPermissionsVO) {
        Set<String> lv2DimensionSet = new HashSet<>();
        Set<String> lv3DimensionSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(singleDimensionValues)) {
            if (!singleDimensionValues.contains(CommonConstant.PAGE_FLAG_ALL) && !singleDimensionValues.contains(ALL_CONDITION)) {
                Set<String> bgDimensionSet = singlePermissionsVO.getBgDimensionSet();
                bgDimensionSet(singleDimensionValues, bgDimensionSet);
                lv2DimensionSet(singleDimensionValues, lv2DimensionSet);
                lv3DimensionSet(singleDimensionValues, lv3DimensionSet);
            }
        }
        Map<String, Map<String, Set<String>>> locationMap = dataPermissionsVO.getLocationMap();
        Set<String> overseaFlagDimensionSet = singlePermissionsVO.getOverseaFlagDimensionSet();
        overseaFlagDimensionSet.removeIf(model -> model.equals(NO_PERMISSION));
        Set<String> bgDimensionSet = singlePermissionsVO.getBgDimensionSet();
        if (bgDimensionSet.contains(NO_PERMISSION) || bgDimensionSet.size() == 0) {
            CommonBaseVO commonBaseVO = new CommonBaseVO();
            commonBaseVO.setLv2DimensionSet(lv2DimensionSet);
            commonBaseVO.setLv3DimensionSet(lv3DimensionSet);
            bgDimensionSet = dmFcstIrbDimInfoDao.getBgCodeForSpecial(commonBaseVO);
            bgDimensionSet.removeIf(model -> model.equals(CommonConstant.GR));
        }
        Set<String> regionCodeDimensionSet = singlePermissionsVO.getRegionCodeDimensionSet();
        regionCodeDimensionSet.removeIf(model -> model.equals(NO_PERMISSION));
        for (String overseaFlag : overseaFlagDimensionSet) {
            if (locationMap.containsKey(overseaFlag)) {
                Map<String, Set<String>> bgMap = locationMap.get(overseaFlag);
                for (String bg : bgDimensionSet) {
                    if (bgMap.containsKey(bg)) {
                        Set<String> regionSet = bgMap.get(bg);
                        regionSet.addAll(regionCodeDimensionSet);
                        bgMap.put(bg, regionSet);
                    } else {
                        bgMap.put(bg, regionCodeDimensionSet);
                    }
                }
            } else {
                Map<String, Set<String>> bgMap = new HashMap<>();
                for (String bg : bgDimensionSet) {
                    bgMap.put(bg, regionCodeDimensionSet);
                }
                locationMap.put(overseaFlag, bgMap);
            }
        }
        dataPermissionsVO.getLocationMap().putAll(locationMap);
    }

    private void parsingDimensionCode(DataPermissionsVO dataPermissionsVO, Set<String> dimensionValues) {
        Set<String> lv0DimensionSet = dataPermissionsVO.getLv0DimensionSet();
        Set<String> lv1DimensionSet = dataPermissionsVO.getLv1DimensionSet();
        Set<String> lv2DimensionSet = dataPermissionsVO.getLv2DimensionSet();
        Set<String> lv3DimensionSet = dataPermissionsVO.getLv3DimensionSet();
        Set<String> bgDimensionSet = dataPermissionsVO.getBgDimensionSet();
        lv0DimensionSet(dimensionValues, lv0DimensionSet);
        lv1DimensionSet(dimensionValues, lv1DimensionSet);
        lv2DimensionSet(dimensionValues, lv2DimensionSet );
        lv3DimensionSet(dimensionValues, lv3DimensionSet);
        bgDimensionSet(dimensionValues, bgDimensionSet);
    }

    private void parsingLocationDimensionCode(DataPermissionsVO dataPermissionsVO, Set<String> dimensionValues, DataPermissionsVO singlePermissionsVO) {

        Set<String> overseaFlagValues = new HashSet<>();
        Set<String> regionValues = new HashSet<>();
        Set<String> repofficeCodeValues = new HashSet<>();
        dimensionValues.forEach(value -> {
            if (value.contains("_OVERSEA_ICT")) {
                overseaFlagValues.add(value);
            }
            if (value.contains("_REGION_ICT")) {
                regionValues.add(value);
            }
            if (value.contains("_REPOFFICE_ICT")) {
                repofficeCodeValues.add(value);
            }
        });
        overseaFlagDimensionSet(overseaFlagValues, singlePermissionsVO);
        regionDimensionSet(regionValues, singlePermissionsVO);
        repofficeDimensionSet(repofficeCodeValues, singlePermissionsVO);
        dataPermissionsVO.getOverseaFlagDimensionSet().addAll(singlePermissionsVO.getOverseaFlagDimensionSet());
        dataPermissionsVO.getRegionCodeDimensionSet().addAll(singlePermissionsVO.getRegionCodeDimensionSet());
        dataPermissionsVO.getRepofficeCodeDimensionSet().addAll(singlePermissionsVO.getRepofficeCodeDimensionSet());
        dataPermissionsVO.getRegionCodeDimensionTrueSet().addAll(singlePermissionsVO.getRegionCodeDimensionTrueSet());
    }

    private void bgDimensionSet(Set<String> dimensionValues, Set<String> bgDimensionSet) {
        for (String bgCode : dimensionValues) {
            if (bgCode.contains("_BG_ICT")) {
                bgDimensionSet.add(bgCode.split("_")[0]);
            }
        }
        if (bgDimensionSet.size() == 0) {
            bgDimensionSet.add(NO_PERMISSION);
        }
    }

    private void overseaFlagDimensionSet(Set<String> dimensionValues, DataPermissionsVO singlePermissionsVO) {
        Set<String> overseaFlagSet = new HashSet<>();
        for (String overseaFlag : dimensionValues) {
            overseaFlagSet.add(overseaFlag.split("_")[0]);
        }
        if (overseaFlagSet.size() == 0) {
            overseaFlagSet.add(NO_PERMISSION);
        }
        singlePermissionsVO.getOverseaFlagDimensionSet().addAll(overseaFlagSet);
    }

    private void regionDimensionSet(Set<String> dimensionValues, DataPermissionsVO singlePermissionsVO) {
        Set<String> overseaFlagSet = new HashSet<>();
        Set<String> regionCodeSingleSet = new HashSet<>();
        for (String region : dimensionValues) {
            String regionCode = region.split("_")[0];
            regionCodeSingleSet.add(regionCode);
            String overseaFlag = region.split("_")[1];
            overseaFlagSet.add(overseaFlag);
        }
        if (regionCodeSingleSet.size() == 0) {
            regionCodeSingleSet.add(NO_PERMISSION);
        }
        singlePermissionsVO.getOverseaFlagDimensionSet().addAll(overseaFlagSet);
        singlePermissionsVO.getRegionCodeDimensionTrueSet().addAll(regionCodeSingleSet);
        singlePermissionsVO.getRegionCodeDimensionSet().addAll(regionCodeSingleSet);
    }

    private void repofficeDimensionSet(Set<String> dimensionValues, DataPermissionsVO singlePermissionsVO) {
        Set<String> regionCodeSet = new HashSet<>();
        Set<String> overseaFlagSet = new HashSet<>();
        Set<String> repofficeSingleSet = new HashSet<>();
        for (String repoffice : dimensionValues) {
            String repofficeCode = repoffice.split("_")[0];
            repofficeSingleSet.add(repofficeCode);
            String regionCode = repoffice.split("_")[1];
            regionCodeSet.add(regionCode);
            String overseaFlag = repoffice.split("_")[2];
            overseaFlagSet.add(overseaFlag);
        }
        if (repofficeSingleSet.size() == 0) {
            repofficeSingleSet.add(NO_PERMISSION);
        }
        singlePermissionsVO.getRepofficeCodeDimensionSet().addAll(repofficeSingleSet);
        singlePermissionsVO.getRegionCodeDimensionSet().addAll(regionCodeSet);
        singlePermissionsVO.getOverseaFlagDimensionSet().addAll(overseaFlagSet);
    }

    private void lv3DimensionSet(Set<String> dimensionValues, Set<String> lv3DimensionSet) {
        for (String lv3rodCode : dimensionValues) {
            if (lv3rodCode.contains("_L3_ICT")) {
                lv3DimensionSet.add(lv3rodCode.split("_")[0]);
            }
        }
        if (lv3DimensionSet.size() == 0) {
            lv3DimensionSet.add(NO_PERMISSION);
        }
    }

    private void lv2DimensionSet(Set<String> dimensionValues, Set<String> lv2DimensionSet) {
        for (String lv2ProdCode : dimensionValues) {
            if (lv2ProdCode.contains("_L2_ICT")) {
                lv2DimensionSet.add(lv2ProdCode.split("_")[0]);
            }
        }
        if (lv2DimensionSet.size() == 0) {
            lv2DimensionSet.add(NO_PERMISSION);
        }
    }

    private void lv1DimensionSet(Set<String> dimensionValues, Set<String> lv1DimensionSet) {
        for (String lv1ProdCode : dimensionValues) {
            if (lv1ProdCode.contains("_L1_ICT")) {
                lv1DimensionSet.add(lv1ProdCode.split("_")[0]);
            }
        }
        if (lv1DimensionSet.size() == 0) {
            lv1DimensionSet.add(NO_PERMISSION);
        }
    }

    private void lv0DimensionSet(Set<String> dimensionValues, Set<String> lv0DimensionSet) {
        for (String lv0ProdCode : dimensionValues) {
            if (lv0ProdCode.contains("_L0_ICT")) {
                lv0DimensionSet.add(lv0ProdCode.split("_")[0]);
            }
        }
        if (lv0DimensionSet.size() == 0) {
            lv0DimensionSet.add(NO_PERMISSION);
        }
    }

    /**
     *
     * @return ResultDataVO
     */
    @Override
    @JalorOperation(code = "getGranularityType", desc = "获取pbi目录树")
    public ResultDataVO getGranularityType() {
        DataPermissionsVO dataPermissionsVO = getCurrentRoleDataPermission();
        Set<String> lv1DimensionSet = dataPermissionsVO.getLv1DimensionSet();
        Set<String> lv2DimensionSet = dataPermissionsVO.getLv2DimensionSet();
        Set<String> lv3DimensionSet = dataPermissionsVO.getLv3DimensionSet();
        Set<String> ganularityTypeSet = new HashSet<>();
        // 行销分析师和区域分析师
        if (CommonConstant.MARKETING_ANALYST_IND.equals(dataPermissionsVO.getRoleName()) || CommonConstant.REGION_ANALYST_IND.equals(dataPermissionsVO.getRoleName())) {
            ganularityTypeSet.add(IndustryConstEnum.GRANULARITY_TYPE.PROD.getValue());
            return ResultDataVO.success(ganularityTypeSet);
        }
        if (CollectionUtils.isEmpty(lv1DimensionSet) && CollectionUtils.isEmpty(lv2DimensionSet) && CollectionUtils.isEmpty(lv3DimensionSet)) {
            ganularityTypeSet.add(IndustryConstEnum.GRANULARITY_TYPE.IRB.getValue());
            ganularityTypeSet.add(IndustryConstEnum.GRANULARITY_TYPE.INDUS.getValue());
            ganularityTypeSet.add(IndustryConstEnum.GRANULARITY_TYPE.PROD.getValue());
        } else if (CollectionUtils.isNotEmpty(lv1DimensionSet) && !lv1DimensionSet.contains(NO_PERMISSION)){
            for (String lv1Dimension : lv1DimensionSet) {
                String[] lv1Str = lv1Dimension.split("_");
                if (lv1Str.length != 0) {
                    ganularityTypeSet.add(lv1Str[0]);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(lv2DimensionSet)) {
            CommonBaseVO baseVO = new CommonBaseVO();
            baseVO.setLv2DimensionSet(lv2DimensionSet);
            getGanularityType(ganularityTypeSet, baseVO);
        }
        if (CollectionUtils.isNotEmpty(lv3DimensionSet)) {
            CommonBaseVO baseVO = new CommonBaseVO();
            baseVO.setLv3DimensionSet(lv3DimensionSet);
            getGanularityType(ganularityTypeSet, baseVO);
        }
        return ResultDataVO.success(ganularityTypeSet);
    }

    @Override
    @JalorOperation(code = "getCostType", desc = "获取成本类型")
    public ResultDataVO getCostType() {
        DataPermissionsVO dataPermissionsVO = getCurrentRoleDataPermission();
        return ResultDataVO.success(dataPermissionsVO.getCostTypeSet());
    }

    private void getGanularityType(Set<String> ganularityTypeSet, CommonBaseVO baseVO) {
        // 重量级团队目录树
        List<DmFcstDimInfoVO> lv1AndLv2CodeList = dmFcstIrbDimInfoDao.getLv1AndLv2CodeList(baseVO);
        if (CollectionUtils.isNotEmpty(lv1AndLv2CodeList)) {
            ganularityTypeSet.add(IndustryConstEnum.GRANULARITY_TYPE.IRB.getValue());
        }
        // 销售目录树
        List<DmFcstDimInfoVO> lv1AndLv2CodeList1 = dmFcstProdDimInfoDao.getLv1AndLv2CodeList(baseVO);
        if (CollectionUtils.isNotEmpty(lv1AndLv2CodeList1)) {
            ganularityTypeSet.add(IndustryConstEnum.GRANULARITY_TYPE.PROD.getValue());
        }
        // 产业目录树
        List<DmFcstDimInfoVO> lv1AndLv2CodeList2 = dmFcstIndusDimInfoDao.getLv1AndLv2CodeList(baseVO);
        if (CollectionUtils.isNotEmpty(lv1AndLv2CodeList2)) {
            ganularityTypeSet.add(IndustryConstEnum.GRANULARITY_TYPE.INDUS.getValue());
        }
    }

    /**
     *
     * @return ResultDataVO
     */
    @Override
    @JalorOperation(code = "getlv0Permission", desc = "校验LV0层级权限")
    public ResultDataVO getlv0Permission(CommonBaseVO commonBaseVO) {
        List<DmFcstDimInfoVO> allProdDimensionList = getLv1AndLv2VOList(commonBaseVO);
        DataPermissionsVO dataPermissionsVO = getCurrentRoleDataPermission();
        Set<String> lv2DimensionSet = dataPermissionsVO.getLv2DimensionSet();
        Set<String> lv1CodeList = allProdDimensionList.stream().filter(item -> commonBaseVO.getLv0ProdRndTeamCode().equals(item.getLv0Code())).map(DmFcstDimInfoVO::getLv1Code).collect(Collectors.toSet());
        Boolean hasAllLv0Permission = false;
        if (lv2DimensionSet.containsAll(lv1CodeList) || CollectionUtils.isEmpty(lv2DimensionSet)) {
            hasAllLv0Permission = true;
        }
        return ResultDataVO.success(hasAllLv0Permission);
    }

    /**
     * 获取当前登陆用户的角色对应的数据维度范围
     * 树型结构从iauth下发的维度，可能存在LV1的子没勾选全，所有当前的Lv1就没权限，需要过滤LV1code编码
     *
     * @return DataPermissionsVO 维度对象
     */
    @Override
    public DataPermissionsVO getDimensionList(CommonBaseVO commonBaseVO) {
        DataPermissionsVO dataPermissionsVO = getCurrentRoleDataPermission();
        List<DmFcstDimInfoVO> allProdDimensionList = getLv1AndLv2VOList(commonBaseVO);
        Set<String> lv1DimensionSet = new HashSet<>();
        for (String lv1Code : dataPermissionsVO.getLv1DimensionSet()) {
            Set<String> lv2ProdCodeSet = allProdDimensionList.stream()
                    .filter(item -> lv1Code.equals(item.getLv1Code()))
                    .map(DmFcstDimInfoVO::getLv2Code).collect(Collectors.toSet());
            if (dataPermissionsVO.getLv2DimensionSet().size() == 0) {
                lv1DimensionSet.add(lv1Code);
                continue;
            }
            if (dataPermissionsVO.getLv2DimensionSet().containsAll(lv2ProdCodeSet)) {
                lv1DimensionSet.add(lv1Code);
            }
        }
        dataPermissionsVO.setLv1DimensionSet(lv1DimensionSet);
        return dataPermissionsVO;
    }

    public List<DmFcstDimInfoVO> getLv1AndLv2VOList(CommonBaseVO commonBaseVO) {
        IndustryConstEnum.GRANULARITY_TYPE granularityType = IndustryConstEnum.getGranularityType(commonBaseVO.getGranularityType());
        List<DmFcstDimInfoVO> dmFcstDimInfoVOList = new ArrayList<>();
        switch (granularityType) {
            case IRB:
                dmFcstDimInfoVOList = dmFcstIrbDimInfoDao.getLv1AndLv2CodeList(null);
                break;
            case INDUS:
                dmFcstDimInfoVOList = dmFcstIndusDimInfoDao.getLv1AndLv2CodeList(null);
                break;
            case PROD:
                dmFcstDimInfoVOList = dmFcstProdDimInfoDao.getLv1AndLv2CodeList(null);
                break;
        }
        return dmFcstDimInfoVOList;
    }

    public List<DmFcstDimInfoVO> reverseFindLv1Code(CommonBaseVO commonBaseVO) {
        IndustryConstEnum.GRANULARITY_TYPE granularityType = IndustryConstEnum.getGranularityType(commonBaseVO.getGranularityType());
        List<DmFcstDimInfoVO> dmFcstDimInfoVOList = new ArrayList<>();
        switch (granularityType) {
            case IRB:
                dmFcstDimInfoVOList = dmFcstIrbDimInfoDao.reverseFindLv1Code(commonBaseVO);
                break;
            case INDUS:
                dmFcstDimInfoVOList = dmFcstIndusDimInfoDao.reverseFindLv1Code(commonBaseVO);
                break;
            case PROD:
                dmFcstDimInfoVOList = dmFcstProdDimInfoDao.reverseFindLv1Code(commonBaseVO);
                break;
        }
        return dmFcstDimInfoVOList;
    }

    public List<DmFcstDimInfoVO> reverseFindLv1CodeMonth(CommonBaseVO commonBaseVO) {
        IndustryConstEnum.GRANULARITY_TYPE granularityType = IndustryConstEnum.getGranularityType(commonBaseVO.getGranularityType());
        List<DmFcstDimInfoVO> dmFcstDimInfoVOList = new ArrayList<>();
        switch (granularityType) {
            case IRB:
                dmFcstDimInfoVOList = dmFcstIrbDimInfoDao.reverseFindLv1CodeMonth(commonBaseVO);
                break;
            case INDUS:
                dmFcstDimInfoVOList = dmFcstIndusDimInfoDao.reverseFindLv1CodeMonth(commonBaseVO);
                break;
            case PROD:
                dmFcstDimInfoVOList = dmFcstProdDimInfoDao.reverseFindLv1CodeMonth(commonBaseVO);
                break;
        }
        return dmFcstDimInfoVOList;
    }
}
