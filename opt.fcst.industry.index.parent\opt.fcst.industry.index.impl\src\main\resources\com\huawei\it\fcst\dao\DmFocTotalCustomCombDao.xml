<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IDmFocTotalCustomCombDao">

    <resultMap type="com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO" id="resultMap">
        <result property="pageFlag" column="page_flag"/>
        <result property="lv0ProdRndTeamCode" column="LV0_PROD_RND_TEAM_CODE" />
        <result property="lv0ProdRdTeamCnName" column="LV0_PROD_RD_TEAM_CN_NAME" />
        <result property="lv1ProdRndTeamCode" column="LV1_PROD_RND_TEAM_CODE" />
        <result property="lv1ProdRdTeamCnName" column="LV1_PROD_RD_TEAM_CN_NAME" />
        <result property="lv2ProdRndTeamCode" column="LV2_PROD_RND_TEAM_CODE" />
        <result property="lv2ProdRdTeamCnName" column="LV2_PROD_RD_TEAM_CN_NAME" />
        <result property="lv3ProdRndTeamCode" column="LV3_PROD_RND_TEAM_CODE" />
        <result property="lv3ProdRdTeamCnName" column="LV3_PROD_RD_TEAM_CN_NAME" />
        <result property="lv4ProdRndTeamCode" column="LV4_PROD_RND_TEAM_CODE" />
        <result property="lv4ProdRdTeamCnName" column="LV4_PROD_RD_TEAM_CN_NAME" />
        <result property="l1Name" column="l1_name"/>
        <result property="l2Name" column="l2_name"/>
        <result property="dimensionCode" column="dimension_code"/>
        <result property="dimensionCnName" column="dimension_cn_name"/>
        <result property="dimensionSubCategoryCode" column="dimension_subcategory_code"/>
        <result property="dimensionSubCategoryCnName" column="dimension_subcategory_cn_name"/>
        <result property="dimensionSubDetailCode" column="dimension_sub_detail_code"/>
        <result property="dimensionSubDetailCnName" column="dimension_sub_detail_cn_name"/>
        <result property="groupLevel" column="group_level"/>
        <result property="groupCnName" column="group_cn_name"/>
        <result property="groupCode" column="group_code"/>
        <result property="userId" column="user_id"/>
        <result property="roleId" column="role_id"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="caliberFlag" column="caliber_flag"/>
        <result property="granularityType" column="granularity_type"/>
        <result property="overseaFlag" column="oversea_flag"/>
        <result property="enableFlag" column="enable_flag"/>
        <result property="subEnableFlag" column="sub_enable_flag"/>
        <result property="lv0ProdListCode" column="lv0_prod_list_code"/>
        <result property="lv0ProdListCnName" column="lv0_prod_list_cn_name"/>
        <result property="isSeparate" column="is_separate"/>
        <result property="creationDate" column="creation_date"/>
        <result property="createdBy" column="created_by"/>
        <result property="lastUpdateDdate" column="last_update_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="customId" column="custom_id"/>
        <result property="customCnName" column="custom_cn_name"/>
        <result property="parentCode" column="parent_code"/>
        <result property="connectCode" column="connectCode"/>
        <result property="connectParentCode" column="connectParentCode"/>
        <result property="spartCnName" column="spart_cn_name"/>
        <result property="spartCode" column="spart_code"/>
        <result property="coaCode" column="coa_code"/>
        <result property="coaCnName" column="coa_cn_name"/>
    </resultMap>

    <select id="prodTeamCodeForGeneral" resultMap="resultMap">
        SELECT
        <if test='viewFlag != null and viewFlag == "0"'>
            <choose>
                <when test='groupLevel == "LV0"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv0_prod_rnd_team_code AS group_code,lv0_prod_rd_team_cn_name AS group_cn_name,'LV0' AS group_level,lv0_prod_rnd_team_code AS connectCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "1"'>
            <choose>
                <when test='groupLevel == "LV0"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv0_prod_rnd_team_code AS group_code,lv0_prod_rd_team_cn_name AS group_cn_name,'LV0' AS group_level,lv0_prod_rnd_team_code AS connectCode
                </when>
                <when test='groupLevel == "LV1" and lv0Flag =="Y"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv1_prod_rnd_team_code AS group_code,lv1_prod_rd_team_cn_name AS group_cn_name,'LV1' AS group_level,lv0_prod_rnd_team_code AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectCode, lv0_prod_rnd_team_code AS connectParentCode
                </when>
                <when test='groupLevel == "LV1" and lv0Flag =="N"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv1_prod_rnd_team_code AS group_code,lv1_prod_rd_team_cn_name AS group_cn_name,'LV1' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "2"'>
            <choose>
                <when test='groupLevel == "LV0"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv0_prod_rnd_team_code as group_code,lv0_prod_rd_team_cn_name as group_cn_name,'LV0' AS group_level,lv0_prod_rnd_team_code AS connectCode
                </when>
                <when test='groupLevel == "LV1" and lv0Flag =="Y"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv1_prod_rnd_team_code AS group_code,lv1_prod_rd_team_cn_name AS group_cn_name,'LV1' AS group_level,lv0_prod_rnd_team_code AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectCode, lv0_prod_rnd_team_code AS connectParentCode
                </when>
                <when test='groupLevel == "LV1" and lv0Flag =="N"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv1_prod_rnd_team_code AS group_code,lv1_prod_rd_team_cn_name AS group_cn_name,'LV1' AS group_level,lv0_prod_rnd_team_code AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectCode
                </when>
                <when test='groupLevel == "LV2"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code, lv2_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS group_code,lv2_prod_rd_team_cn_name AS group_cn_name,'LV2' AS group_level,lv1_prod_rnd_team_code AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code) AS connectCode, CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and (viewFlag == "3" or viewFlag == "7")'>
            <choose>
                <when test='groupLevel == "LV0"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv0_prod_rnd_team_code as group_code,lv0_prod_rd_team_cn_name as group_cn_name,'LV0' AS group_level,lv0_prod_rnd_team_code AS connectCode
                </when>
                <when test='groupLevel == "LV1" and lv0Flag =="Y"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv1_prod_rnd_team_code AS group_code,lv1_prod_rd_team_cn_name AS group_cn_name,'LV1' AS group_level,lv0_prod_rnd_team_code AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectCode, lv0_prod_rnd_team_code AS connectParentCode
                </when>
                <when test='groupLevel == "LV1" and lv0Flag =="N"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv1_prod_rnd_team_code AS group_code,lv1_prod_rd_team_cn_name AS group_cn_name,'LV1' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectCode
                </when>
                <when test='groupLevel == "LV2"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code, lv2_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS group_code,lv2_prod_rd_team_cn_name AS group_cn_name,'LV2' AS group_level,lv1_prod_rnd_team_code AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectParentCode
                </when>
                <when test='groupLevel == "LV3"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code, lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code, lv3_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS group_code,lv3_prod_rd_team_cn_name AS group_cn_name,'LV3' AS group_level,lv2_prod_rnd_team_code AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code) AS connectParentCode
                </when>
                <when test='groupLevel == "LV4"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code, lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code, lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code, lv4_prod_rd_team_cn_name,lv4_prod_rnd_team_code AS group_code,lv4_prod_rd_team_cn_name AS group_cn_name,
                    'LV4' AS group_level,lv3_prod_rnd_team_code AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,'#*#',lv4_prod_rnd_team_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        from fin_dm_opt_foi.${tablePreFix}_TOTAL_VIEW_INFO_D
        WHERE view_flag = #{viewFlag,jdbcType=VARCHAR} and del_flag ='N'
        and caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        and page_flag = #{pageFlag,jdbcType=VARCHAR}
        <if test='groupLevel != null and groupLevel != ""'>
            AND group_level = #{groupLevel}
        </if>
        <if test='overseaFlag != null and overseaFlag != ""'>
            AND oversea_flag = #{overseaFlag}
        </if>
        <if test='lv0ProdListCode != null and lv0ProdListCode != ""'>
            AND lv0_prod_list_code = #{lv0ProdListCode}
        </if>
        <if test='lv0ProdRndTeamCode != null and lv0ProdRndTeamCode != ""'>
            AND lv0_prod_rnd_team_code = #{lv0ProdRndTeamCode}
        </if>
        <if test='lv1ProdRndTeamCode != null and lv1ProdRndTeamCode != ""'>
            AND lv1_prod_rnd_team_code = #{lv1ProdRndTeamCode}
        </if>
        <if test='lv2ProdRndTeamCode != null and lv2ProdRndTeamCode != ""'>
            AND lv2_prod_rnd_team_code = #{lv2ProdRndTeamCode}
        </if>
        <if test='lv3ProdRndTeamCode != null and lv3ProdRndTeamCode != ""'>
            AND lv3_prod_rnd_team_code = #{lv3ProdRndTeamCode}
        </if>
        <if test='lv4ProdRndTeamCode != null and lv4ProdRndTeamCode != ""'>
            AND lv4_prod_rnd_team_code = #{lv4ProdRndTeamCode}
        </if>
        <if test='keyWord != null and keyWord != ""'>
            AND group_cn_name LIKE CONCAT(CONCAT('%', #{keyWord}::text ,'%'))
        </if>
        <!-- 存在lv1DimensionSet=no permission，且lv2是最大权限的情况，所以需要只走下面第二个判断逻辑-->
        <if test='groupLevel =="LV1" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel =="LV2" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <!-- 除去如上两种情况，其他的需要走后面两种情况-->
        <choose>
            <when test='lv1DimensionSet != null and lv1DimensionSet.size() == 1 and lv1DimensionSet.contains("NO_PERMISSION")'>

            </when>
            <otherwise>
                <if test='viewFlag!= "0" and groupLevel != "LV0" and groupLevel != "LV1" and groupLevel != "LV2" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
                    <foreach collection='lv1DimensionSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
            </otherwise>
        </choose>
        <if test='viewFlag!= "0" and viewFlag!= "1" and groupLevel != "LV0"  and groupLevel != "LV1" and groupLevel != "LV2" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv1ProdRndTeamCodeSet != null and lv1ProdRndTeamCodeSet.size() > 0'>
            <foreach collection='lv1ProdRndTeamCodeSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv2ProdRndTeamCodeSet != null and lv2ProdRndTeamCodeSet.size() > 0'>
            <foreach collection='lv2ProdRndTeamCodeSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv3ProdRndTeamCodeSet != null and lv3ProdRndTeamCodeSet.size() > 0'>
            <foreach collection='lv3ProdRndTeamCodeSet' item="code" open="AND lv3_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv1CodeList != null and lv1CodeList.size() > 0'>
            <foreach collection='lv1CodeList' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv2CodeList != null and lv2CodeList.size() > 0'>
            <foreach collection='lv2CodeList' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv3CodeList != null and lv3CodeList.size() > 0'>
            <foreach collection='lv3CodeList' item="code" open="AND lv3_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
    </select>

    <select id="prodTeamCodeForProfit" resultMap="resultMap">
        SELECT
        <if test='viewFlag != null and viewFlag == "0"'>
            <choose>
                <when test='groupLevel == "LV0"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv0_prod_rnd_team_code AS group_code,lv0_prod_rd_team_cn_name AS group_cn_name,'LV0' AS group_level,lv0_prod_rnd_team_code AS connectCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "1"'>
            <choose>
                <when test='groupLevel == "LV0"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv0_prod_rnd_team_code AS group_code,lv0_prod_rd_team_cn_name AS group_cn_name,'LV0' AS group_level,lv0_prod_rnd_team_code AS connectCode
                </when>
                <when test='groupLevel == "LV1" and lv0Flag =="Y"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv1_prod_rnd_team_code AS group_code,lv1_prod_rd_team_cn_name AS group_cn_name,'LV1' AS group_level,lv0_prod_rnd_team_code AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectCode, lv0_prod_rnd_team_code AS connectParentCode
                </when>
                <when test='groupLevel == "LV1" and lv0Flag =="N"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv1_prod_rnd_team_code AS group_code,lv1_prod_rd_team_cn_name AS group_cn_name,'LV1' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "2"'>
            <choose>
                <when test='groupLevel == "LV0"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv0_prod_rnd_team_code as group_code,lv0_prod_rd_team_cn_name as group_cn_name,'LV0' AS group_level,lv0_prod_rnd_team_code AS connectCode
                </when>
                <when test='groupLevel == "LV1" and lv0Flag =="Y"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv1_prod_rnd_team_code AS group_code,lv1_prod_rd_team_cn_name AS group_cn_name,'LV1' AS group_level,lv0_prod_rnd_team_code AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectCode, lv0_prod_rnd_team_code AS connectParentCode
                </when>
                <when test='groupLevel == "LV1" and lv0Flag =="N"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv1_prod_rnd_team_code AS group_code,lv1_prod_rd_team_cn_name AS group_cn_name,'LV1' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectCode
                </when>
                <when test='groupLevel == "LV2"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code, lv2_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS group_code,lv2_prod_rd_team_cn_name AS group_cn_name,'LV2' AS group_level,lv1_prod_rnd_team_code AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code) AS connectCode, CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "3"'>
            <choose>
                <when test='groupLevel == "LV0"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv0_prod_rnd_team_code as group_code,lv0_prod_rd_team_cn_name as group_cn_name,'LV0' AS group_level,lv0_prod_rnd_team_code AS connectCode
                </when>
                <when test='groupLevel == "LV1" and lv0Flag =="Y"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv1_prod_rnd_team_code AS group_code,lv1_prod_rd_team_cn_name AS group_cn_name,'LV1' AS group_level,lv0_prod_rnd_team_code AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectCode, lv0_prod_rnd_team_code AS connectParentCode
                </when>
                <when test='groupLevel == "LV1" and lv0Flag =="N"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv1_prod_rnd_team_code AS group_code,lv1_prod_rd_team_cn_name AS group_cn_name,'LV1' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectCode
                </when>
                <when test='groupLevel == "LV2"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code, lv2_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS group_code,lv2_prod_rd_team_cn_name AS group_cn_name,'LV2' AS group_level,lv1_prod_rnd_team_code AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code) AS connectCode, CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectParentCode
                </when>
                <when test='groupLevel == "L1"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code, lv2_prod_rd_team_cn_name,l1_name,l1_name AS group_code,l1_name AS group_cn_name,'L1' AS group_level,lv2_prod_rnd_team_code AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',l1_name) AS connectCode, CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "4"'>
            <choose>
                <when test='groupLevel == "LV0"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv0_prod_rnd_team_code as group_code,lv0_prod_rd_team_cn_name as group_cn_name,'LV0' AS group_level,lv0_prod_rnd_team_code AS connectCode
                </when>
                <when test='groupLevel == "LV1" and lv0Flag =="Y"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv1_prod_rnd_team_code AS group_code,lv1_prod_rd_team_cn_name AS group_cn_name,'LV1' AS group_level,lv0_prod_rnd_team_code AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectCode, lv0_prod_rnd_team_code AS connectParentCode
                </when>
                <when test='groupLevel == "LV1" and lv0Flag =="N"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv1_prod_rnd_team_code AS group_code,lv1_prod_rd_team_cn_name AS group_cn_name,'LV1' AS group_level,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectCode
                </when>
                <when test='groupLevel == "LV2"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code, lv2_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS group_code,lv2_prod_rd_team_cn_name AS group_cn_name,'LV2' AS group_level,lv1_prod_rnd_team_code AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code) AS connectCode, CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectParentCode
                </when>
                <when test='groupLevel == "L1"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code, lv2_prod_rd_team_cn_name,l1_name,l1_name AS group_code,l1_name AS group_cn_name,'L1' AS group_level,lv2_prod_rnd_team_code AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',l1_name) AS connectCode, CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code) AS connectParentCode
                </when>
                <when test='groupLevel == "L2"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code, lv2_prod_rd_team_cn_name,l1_name, l2_name,l2_name AS group_code,l2_name AS group_cn_name,'L2' AS group_level,l1_name AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',l1_name,'#*#',l2_name) AS connectCode, CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',l1_name) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        from fin_dm_opt_foi.${tablePreFix}_TOTAL_PFT_VIEW_INFO_D
        WHERE view_flag = #{viewFlag,jdbcType=VARCHAR} and del_flag ='N'
        and caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        and page_flag = #{pageFlag,jdbcType=VARCHAR}
        <if test='groupLevel != null and groupLevel != ""'>
            AND group_level = #{groupLevel}
        </if>
        <if test='overseaFlag != null and overseaFlag != ""'>
            AND oversea_flag = #{overseaFlag}
        </if>
        <if test='lv0ProdListCode != null and lv0ProdListCode != ""'>
            AND lv0_prod_list_code = #{lv0ProdListCode}
        </if>
        <if test='lv0ProdRndTeamCode != null and lv0ProdRndTeamCode != ""'>
            AND lv0_prod_rnd_team_code = #{lv0ProdRndTeamCode}
        </if>
        <if test='lv1ProdRndTeamCode != null and lv1ProdRndTeamCode != ""'>
            AND lv1_prod_rnd_team_code = #{lv1ProdRndTeamCode}
        </if>
        <if test='lv2ProdRndTeamCode != null and lv2ProdRndTeamCode != ""'>
            AND lv2_prod_rnd_team_code = #{lv2ProdRndTeamCode}
        </if>
        <if test='l1Name != null and l1Name != ""'>
            AND l1_name = #{l1Name}
        </if>
        <if test='l2Name != null and l2Name != ""'>
            AND l2_name = #{l2Name}
        </if>
        <if test='keyWord != null and keyWord != ""'>
            AND group_cn_name LIKE CONCAT(CONCAT('%', #{keyWord}::text ,'%'))
        </if>
        <!-- 存在lv1DimensionSet=no permission，且lv2是最大权限的情况，所以需要只走下面第二个判断逻辑-->
        <if test='groupLevel =="LV1" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel =="LV2" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <!-- 除去如上两种情况，其他的需要走后面两种情况-->
        <choose>
            <when test='lv1DimensionSet != null and lv1DimensionSet.size() == 1 and lv1DimensionSet.contains("NO_PERMISSION")'>

            </when>
            <otherwise>
                <if test='viewFlag!= "0" and groupLevel != "LV0" and groupLevel != "LV1" and groupLevel != "LV2" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
                    <foreach collection='lv1DimensionSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
            </otherwise>
        </choose>
        <if test='viewFlag!= "0" and viewFlag!= "1" and groupLevel != "LV0"  and groupLevel != "LV1" and groupLevel != "LV2" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv1ProdRndTeamCodeSet != null and lv1ProdRndTeamCodeSet.size() > 0'>
            <foreach collection='lv1ProdRndTeamCodeSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv2ProdRndTeamCodeSet != null and lv2ProdRndTeamCodeSet.size() > 0'>
            <foreach collection='lv2ProdRndTeamCodeSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv1CodeList != null and lv1CodeList.size() > 0'>
            <foreach collection='lv1CodeList' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv2CodeList != null and lv2CodeList.size() > 0'>
            <foreach collection='lv2CodeList' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='l1NameList != null and l1NameList.size() > 0'>
            <foreach collection='l1NameList' item="code" open="AND l1_name IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='l2NameList != null and l2NameList.size() > 0'>
            <foreach collection='l2NameList' item="code" open="AND l2_name IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupCodeList != null and groupCodeList.size() > 0'>
            <choose>
                <when test='groupLevel =="L2"'>
                    <foreach collection='groupCodeList' item="code" open="AND l1_name IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='groupLevel =="L1"'>
                    <foreach collection='groupCodeList' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
    </select>

    <select id="prodTeamCodeForDimension" resultMap="resultMap">
        SELECT
        <if test='groupLevel == "LV0"'>
            DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv0_prod_rnd_team_code AS group_code,lv0_prod_rd_team_cn_name AS group_cn_name,'LV0' AS group_level,lv0_prod_rnd_team_code AS connectCode
        </if>
        <if test='groupLevel == "LV1" and lv0Flag =="Y"'>
            DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv1_prod_rnd_team_code AS group_code,lv1_prod_rd_team_cn_name AS group_cn_name,'LV1' AS group_level,
            lv0_prod_rnd_team_code AS parent_code,
            CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectCode, lv0_prod_rnd_team_code AS connectParentCode
        </if>
        <if test='groupLevel == "LV1" and lv0Flag =="N"'>
            DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv1_prod_rnd_team_code AS group_code,lv1_prod_rd_team_cn_name AS group_cn_name,'LV1' AS group_level,
            CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectCode
        </if>
        <if test='groupLevel == "LV2"'>
            <choose>
                <when test='viewFlag != null and viewFlag != "0" and viewFlag != "1" and viewFlag != "2" and viewFlag != "9"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS group_code,lv2_prod_rd_team_cn_name AS group_cn_name,'LV2' AS group_level,
                    lv1_prod_rnd_team_code AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code) AS connectCode, CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "LV3"'>
            <choose>
                <when test='viewFlag != null and (viewFlag == "6" or viewFlag == "7" or viewFlag == "8" or viewFlag == "11")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,
                    lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,
                    lv3_prod_rd_team_cn_name, lv3_prod_rnd_team_code AS
                    group_code,lv3_prod_rd_team_cn_name AS group_cn_name,'LV3' AS group_level,lv2_prod_rnd_team_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "DIMENSION"'>
            <choose>
                <when test='viewFlag != null and (viewFlag == "0" or viewFlag == "1" or viewFlag == "2" or viewFlag == "9")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,
                    dimension_code,dimension_cn_name, dimension_code AS
                    group_code,dimension_cn_name AS group_cn_name,'DIMENSION' AS group_level,lv1_prod_rnd_team_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',dimension_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectParentCode
                </when>
                <when test='viewFlag != null and (viewFlag == "3" or viewFlag == "4" or viewFlag == "5" or viewFlag == "10")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,
                    dimension_code,dimension_cn_name, dimension_code AS
                    group_code,dimension_cn_name AS group_cn_name,'DIMENSION' AS group_level,lv2_prod_rnd_team_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',dimension_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code) AS connectParentCode
                </when>
                <when test='viewFlag != null and (viewFlag == "6" or viewFlag == "7" or viewFlag == "8" or viewFlag == "11")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,
                    dimension_code,dimension_cn_name, dimension_code AS
                    group_code,dimension_cn_name AS group_cn_name,'DIMENSION' AS group_level,lv3_prod_rnd_team_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,'#*#',dimension_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "SUBCATEGORY"'>
            <choose>
                <when test='viewFlag != null and (viewFlag == "1" or viewFlag =="2" or viewFlag =="9")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUBCATEGORY_CODE AS
                    group_code,DIMENSION_SUBCATEGORY_CN_NAME AS group_cn_name,'SUBCATEGORY' AS group_level,dimension_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',dimension_code) AS connectParentCode
                </when>
                <when test='viewFlag != null and (viewFlag == "4" or viewFlag =="5" or viewFlag =="10")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUBCATEGORY_CODE AS
                    group_code,DIMENSION_SUBCATEGORY_CN_NAME AS group_cn_name,'SUBCATEGORY' AS group_level,dimension_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',dimension_code) AS connectParentCode
                </when>
                <when test='viewFlag != null and (viewFlag == "7" or viewFlag =="8" or viewFlag =="11")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUBCATEGORY_CODE AS
                    group_code,DIMENSION_SUBCATEGORY_CN_NAME AS group_cn_name,'SUBCATEGORY' AS group_level,dimension_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,'#*#',dimension_code) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "SUB_DETAIL"'>
            <choose>
                <when test='viewFlag != null and (viewFlag == "2" or viewFlag=="9")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,
                    DIMENSION_SUB_DETAIL_CODE AS group_code,DIMENSION_SUB_DETAIL_CN_NAME AS group_cn_name,'SUB_DETAIL' AS group_level,DIMENSION_SUBCATEGORY_CODE AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectParentCode
                </when>
                <when test='viewFlag != null and (viewFlag == "5" or viewFlag=="10")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,
                    DIMENSION_SUB_DETAIL_CODE AS group_code,DIMENSION_SUB_DETAIL_CN_NAME AS group_cn_name,'SUB_DETAIL' AS group_level,DIMENSION_SUBCATEGORY_CODE AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectParentCode
                </when>
                <when test='viewFlag != null and (viewFlag == "8" or viewFlag=="11")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,
                    DIMENSION_SUB_DETAIL_CODE AS group_code,DIMENSION_SUB_DETAIL_CN_NAME AS group_cn_name,'SUB_DETAIL' AS group_level,DIMENSION_SUBCATEGORY_CODE AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "SPART"'>
            <choose>
                <when test='viewFlag != null and (viewFlag == "9" or viewFlag == "10" or viewFlag == "11")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,SPART_CODE,SPART_CN_NAME,
                    SPART_CODE AS group_code,SPART_CN_NAME AS group_cn_name,'SPART' AS group_level,DIMENSION_SUB_DETAIL_CODE AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE,'#*#',SPART_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        from fin_dm_opt_foi.DM_FOC_TOTAL_DMS_VIEW_INFO_D
        where del_flag = 'N'
        and view_flag = #{viewFlag,jdbcType=VARCHAR} and del_flag ='N'
        and caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        and page_flag = #{pageFlag,jdbcType=VARCHAR}
        <if test='overseaFlag != null and overseaFlag != ""'>
            AND oversea_flag = #{overseaFlag}
        </if>
        <if test='lv0ProdListCode != null and lv0ProdListCode != ""'>
            AND lv0_prod_list_code = #{lv0ProdListCode}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            AND group_level = #{groupLevel}
        </if>
        <if test='lv0ProdRndTeamCode != null and lv0ProdRndTeamCode != ""'>
            AND lv0_prod_rnd_team_code = #{lv0ProdRndTeamCode}
        </if>
        <if test='lv1ProdRndTeamCode != null and lv1ProdRndTeamCode != ""'>
            AND lv1_prod_rnd_team_code = #{lv1ProdRndTeamCode}
        </if>
        <if test='lv2ProdRndTeamCode != null and lv2ProdRndTeamCode != ""'>
            AND lv2_prod_rnd_team_code = #{lv2ProdRndTeamCode}
        </if>
        <if test='lv3ProdRndTeamCode != null and lv3ProdRndTeamCode != ""'>
            AND lv3_prod_rnd_team_code = #{lv3ProdRndTeamCode}
        </if>
        <if test='dimensionCode != null and dimensionCode != ""'>
            AND dimension_code = #{dimensionCode}
        </if>
        <if test='dimensionSubCategoryCode != null and dimensionSubCategoryCode != ""'>
            AND dimension_subcategory_code = #{dimensionSubCategoryCode}
        </if>
        <if test='dimensionSubDetailCode != null and dimensionSubDetailCode != ""'>
            AND dimension_sub_detail_code = #{dimensionSubDetailCode}
        </if>
        <if test='spartCode != null and spartCode != ""'>
            AND spart_code = #{spartCode}
        </if>
        <!-- 存在lv1DimensionSet=no permission，且lv2是最大权限的情况，所以需要只走下面第二个判断逻辑-->
        <if test='groupLevel =="LV1" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel =="LV2" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <!-- 除去如上两种情况，其他的需要走后面两种情况-->
        <choose>
            <when test='lv1DimensionSet != null and lv1DimensionSet.size() == 1 and lv1DimensionSet.contains("NO_PERMISSION")'>

            </when>
            <otherwise>
                <if test='groupLevel !="LV0" and groupLevel !="LV1" and groupLevel !="LV2" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
                    <foreach collection='lv1DimensionSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
            </otherwise>
        </choose>
        <if test='groupLevel !="LV0" and groupLevel !="LV1" and groupLevel !="LV2" and (viewFlag !="0" and viewFlag !="1" and viewFlag !="2" and viewFlag !="9")  and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv1ProdRndTeamCodeSet != null and lv1ProdRndTeamCodeSet.size() > 0'>
            <foreach collection='lv1ProdRndTeamCodeSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv2ProdRndTeamCodeSet != null and lv2ProdRndTeamCodeSet.size() > 0'>
            <foreach collection='lv2ProdRndTeamCodeSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv1CodeList != null and lv1CodeList.size() > 0'>
            <foreach collection='lv1CodeList' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv2CodeList != null and lv2CodeList.size() > 0'>
            <foreach collection='lv2CodeList' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv3CodeList != null and lv3CodeList.size() > 0'>
            <foreach collection='lv3CodeList' item="code" open="AND lv3_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionCodeList != null and dimensionCodeList.size() > 0'>
            <foreach collection='dimensionCodeList' item="code" open="AND dimension_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionSubcategoryCodeList != null and dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='dimensionSubcategoryCodeList' item="code" open="AND dimension_subcategory_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionSubDetailCodeList != null and dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='dimensionSubDetailCodeList' item="code" open="AND dimension_sub_detail_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='spartCodeList != null and spartCodeList.size() > 0'>
            <foreach collection='spartCodeList' item="code" open="AND spart_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupCodeList != null and groupCodeList.size() > 0'>
            <choose>
                <when test='groupLevel =="SUBCATEGORY"'>
                    <foreach collection='groupCodeList' item="code" open="AND dimension_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='groupLevel =="SUB_DETAIL"'>
                    <foreach collection='groupCodeList' item="code" open="AND dimension_subcategory_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
    </select>

    <select id="prodEnergyTeamCodeForDimension" resultMap="resultMap">
        SELECT
        <if test='groupLevel == "LV0"'>
            DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv0_prod_rnd_team_code AS group_code,lv0_prod_rd_team_cn_name AS group_cn_name,'LV0' AS group_level,lv0_prod_rnd_team_code AS connectCode
        </if>
        <if test='groupLevel == "LV1" and lv0Flag =="Y"'>
            DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv1_prod_rnd_team_code AS group_code,lv1_prod_rd_team_cn_name AS group_cn_name,'LV1' AS group_level,
            lv0_prod_rnd_team_code AS parent_code,
            CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectCode, lv0_prod_rnd_team_code AS connectParentCode
        </if>
        <if test='groupLevel == "LV1" and lv0Flag =="N"'>
            DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv1_prod_rnd_team_code AS group_code,lv1_prod_rd_team_cn_name AS group_cn_name,'LV1' AS group_level,
            CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectCode
        </if>
        <if test='groupLevel == "LV2"'>
            <choose>
                <when test='viewFlag != null and viewFlag != "0" and viewFlag != "1" and viewFlag != "2" and viewFlag != "9"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name, lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS group_code,lv2_prod_rd_team_cn_name AS group_cn_name,'LV2' AS group_level,
                    lv1_prod_rnd_team_code AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code) AS connectCode, CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "LV3"'>
            <choose>
                <when test='viewFlag != null and (viewFlag == "6" or viewFlag == "7" or viewFlag == "8" or viewFlag == "11" or viewFlag == "12")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,
                    lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,
                    lv3_prod_rd_team_cn_name, lv3_prod_rnd_team_code AS
                    group_code,lv3_prod_rd_team_cn_name AS group_cn_name,'LV3' AS group_level,lv2_prod_rnd_team_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "LV4"'>
            <choose>
                <when test='viewFlag != null and viewFlag == "12"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,
                    lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name, lv4_prod_rnd_team_code,
                    lv4_prod_rd_team_cn_name,lv4_prod_rnd_team_code AS group_code,lv4_prod_rd_team_cn_name AS group_cn_name,'LV4' AS group_level,
                    lv3_prod_rnd_team_code AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,'#*#',lv4_prod_rnd_team_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "COA"'>
            <choose>
                <when test='viewFlag != null and viewFlag == "12"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,
                    lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,
                    lv3_prod_rd_team_cn_name, coa_code,coa_cn_name,coa_code AS
                    group_code,coa_cn_name AS group_cn_name,'COA' AS group_level,lv3_prod_rnd_team_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,'#*#',coa_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "DIMENSION"'>
            <choose>
                <when test='viewFlag != null and (viewFlag == "0" or viewFlag == "1" or viewFlag == "2" or viewFlag == "9")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,
                    dimension_code,dimension_cn_name, dimension_code AS
                    group_code,dimension_cn_name AS group_cn_name,'DIMENSION' AS group_level,lv1_prod_rnd_team_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',dimension_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code) AS connectParentCode
                </when>
                <when test='viewFlag != null and (viewFlag == "3" or viewFlag == "4" or viewFlag == "5" or viewFlag == "10")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,
                    dimension_code,dimension_cn_name, dimension_code AS
                    group_code,dimension_cn_name AS group_cn_name,'DIMENSION' AS group_level,lv2_prod_rnd_team_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',dimension_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code) AS connectParentCode
                </when>
                <when test='viewFlag != null and (viewFlag == "6" or viewFlag == "7" or viewFlag == "8" or viewFlag == "11")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,
                    dimension_code,dimension_cn_name, dimension_code AS
                    group_code,dimension_cn_name AS group_cn_name,'DIMENSION' AS group_level,lv3_prod_rnd_team_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,'#*#',dimension_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code) AS connectParentCode
                </when>
                <when test='industryOrg == "ENERGY" and viewFlag != null and viewFlag == "12"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,
                    dimension_code,dimension_cn_name, coa_code,coa_cn_name,dimension_code AS
                    group_code,dimension_cn_name AS group_cn_name,'DIMENSION' AS group_level,coa_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,'#*#',coa_code,'#*#',dimension_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,'#*#',coa_code) AS connectParentCode
                </when>
                <when test='industryOrg == "IAS" and viewFlag != null and viewFlag == "12"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,
                    lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,dimension_code,dimension_cn_name, dimension_code AS
                    group_code,dimension_cn_name AS group_cn_name,'DIMENSION' AS group_level,
                    lv4_prod_rnd_team_code AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,'#*#',lv4_prod_rnd_team_code,'#*#',dimension_code) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,'#*#',lv4_prod_rnd_team_code) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "SUBCATEGORY"'>
            <choose>
                <when test='viewFlag != null and (viewFlag == "1" or viewFlag =="2" or viewFlag =="9")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUBCATEGORY_CODE AS
                    group_code,DIMENSION_SUBCATEGORY_CN_NAME AS group_cn_name,'SUBCATEGORY' AS group_level,dimension_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',dimension_code) AS connectParentCode
                </when>
                <when test='viewFlag != null and (viewFlag == "4" or viewFlag =="5" or viewFlag =="10")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUBCATEGORY_CODE AS
                    group_code,DIMENSION_SUBCATEGORY_CN_NAME AS group_cn_name,'SUBCATEGORY' AS group_level,dimension_code AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',dimension_code) AS connectParentCode
                </when>
                <when test='industryOrg == "ENERGY" and viewFlag != null and (viewFlag == "7" or viewFlag =="8" or viewFlag =="11" or viewFlag =="12")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUBCATEGORY_CODE AS
                    group_code,DIMENSION_SUBCATEGORY_CN_NAME AS group_cn_name,'SUBCATEGORY' AS group_level,dimension_code AS
                    parent_code,coa_code,coa_cn_name,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,DECODE(coa_code,'','','#*#' ||coa_code ),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,DECODE(coa_code,'','','#*#' ||coa_code ),'#*#',dimension_code) AS connectParentCode
                </when>
                <when test='industryOrg == "IAS" and viewFlag != null and (viewFlag == "7" or viewFlag =="8" or viewFlag =="11" or viewFlag =="12")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,dimension_code,dimension_cn_name,
                    DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUBCATEGORY_CODE AS group_code,DIMENSION_SUBCATEGORY_CN_NAME AS group_cn_name,
                    'SUBCATEGORY' AS group_level,dimension_code AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,DECODE(lv4_prod_rnd_team_code,'','','#*#' ||lv4_prod_rnd_team_code ),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,DECODE(lv4_prod_rnd_team_code,'','','#*#' ||lv4_prod_rnd_team_code ),'#*#',dimension_code) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "SUB_DETAIL"'>
            <choose>
                <when test='viewFlag != null and (viewFlag == "2" or viewFlag=="9")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,
                    DIMENSION_SUB_DETAIL_CODE AS group_code,DIMENSION_SUB_DETAIL_CN_NAME AS group_cn_name,'SUB_DETAIL' AS group_level,DIMENSION_SUBCATEGORY_CODE AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectParentCode
                </when>
                <when test='viewFlag != null and (viewFlag == "5" or viewFlag=="10")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,
                    DIMENSION_SUB_DETAIL_CODE AS group_code,DIMENSION_SUB_DETAIL_CN_NAME AS group_cn_name,'SUB_DETAIL' AS group_level,DIMENSION_SUBCATEGORY_CODE AS
                    parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectParentCode
                </when>
                <when test='industryOrg == "ENERGY" and viewFlag != null and (viewFlag == "8" or viewFlag=="11" or viewFlag=="12")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,
                    DIMENSION_SUB_DETAIL_CODE AS group_code,DIMENSION_SUB_DETAIL_CN_NAME AS group_cn_name,'SUB_DETAIL' AS group_level,DIMENSION_SUBCATEGORY_CODE AS
                    parent_code,coa_code,coa_cn_name,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,DECODE(coa_code,'','','#*#' ||coa_code ),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,DECODE(coa_code,'','','#*#' ||coa_code ),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectParentCode
                </when>
                <when test='industryOrg == "IAS" and viewFlag != null and (viewFlag == "8" or viewFlag=="11" or viewFlag=="12")'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,
                    DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,DIMENSION_SUB_DETAIL_CODE AS group_code,DIMENSION_SUB_DETAIL_CN_NAME AS group_cn_name,'SUB_DETAIL' AS group_level,
                    DIMENSION_SUBCATEGORY_CODE AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,DECODE(lv4_prod_rnd_team_code,'','','#*#' ||lv4_prod_rnd_team_code ),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,'#*#',lv2_prod_rnd_team_code,'#*#',lv3_prod_rnd_team_code,DECODE(lv4_prod_rnd_team_code,'','','#*#' ||lv4_prod_rnd_team_code ),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='groupLevel == "SPART"'>
            <choose>
                <when test='industryOrg == "ENERGY"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,dimension_code,dimension_cn_name, DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,SPART_CODE,SPART_CN_NAME,
                    SPART_CODE AS group_code,SPART_CN_NAME AS group_cn_name,'SPART' AS group_level,DIMENSION_SUB_DETAIL_CODE AS
                    parent_code,coa_code,coa_cn_name,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' ||coa_code ),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE,'#*#',SPART_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(coa_code,'','','#*#' ||coa_code ),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE) AS connectParentCode
                </when>
                <when test='industryOrg == "IAS"'>
                    DISTINCT lv0_prod_rnd_team_code,lv0_prod_rd_team_cn_name,
                    lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name, lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code,lv4_prod_rd_team_cn_name,dimension_code,dimension_cn_name,
                    DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,SPART_CODE,SPART_CN_NAME,
                    SPART_CODE AS group_code,SPART_CN_NAME AS group_cn_name,'SPART' AS group_level,DIMENSION_SUB_DETAIL_CODE AS parent_code,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' ||lv4_prod_rnd_team_code ),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE,'#*#',SPART_CODE) AS connectCode,
                    CONCAT(lv0_prod_rnd_team_code,'#*#',lv1_prod_rnd_team_code,DECODE(lv2_prod_rnd_team_code,'','','#*#' || lv2_prod_rnd_team_code),DECODE(lv3_prod_rnd_team_code,'','','#*#' || lv3_prod_rnd_team_code),DECODE(lv4_prod_rnd_team_code,'','','#*#' ||lv4_prod_rnd_team_code ),'#*#',dimension_code,'#*#',DIMENSION_SUBCATEGORY_CODE,'#*#',DIMENSION_SUB_DETAIL_CODE) AS connectParentCode
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        from fin_dm_opt_foi.${tablePreFix}_TOTAL_DMS_VIEW_INFO_D
        where del_flag = 'N'
        and view_flag = #{viewFlag,jdbcType=VARCHAR} and del_flag ='N'
        and caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        and page_flag = #{pageFlag,jdbcType=VARCHAR}
        <if test='overseaFlag != null and overseaFlag != ""'>
            AND oversea_flag = #{overseaFlag}
        </if>
        <if test='lv0ProdListCode != null and lv0ProdListCode != ""'>
            AND lv0_prod_list_code = #{lv0ProdListCode}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            AND group_level = #{groupLevel}
        </if>
        <if test='lv0ProdRndTeamCode != null and lv0ProdRndTeamCode != ""'>
            AND lv0_prod_rnd_team_code = #{lv0ProdRndTeamCode}
        </if>
        <if test='lv1ProdRndTeamCode != null and lv1ProdRndTeamCode != ""'>
            AND lv1_prod_rnd_team_code = #{lv1ProdRndTeamCode}
        </if>
        <if test='lv2ProdRndTeamCode != null and lv2ProdRndTeamCode != ""'>
            AND lv2_prod_rnd_team_code = #{lv2ProdRndTeamCode}
        </if>
        <if test='lv3ProdRndTeamCode != null and lv3ProdRndTeamCode != ""'>
            AND lv3_prod_rnd_team_code = #{lv3ProdRndTeamCode}
        </if>
        <if test='lv4ProdRndTeamCode != null and lv4ProdRndTeamCode != ""'>
            AND lv4_prod_rnd_team_code = #{lv4ProdRndTeamCode}
        </if>
        <if test='coaCode != null and coaCode != ""'>
            AND coa_code = #{coaCode}
        </if>
        <if test='dimensionCode != null and dimensionCode != ""'>
            AND dimension_code = #{dimensionCode}
        </if>
        <if test='dimensionSubCategoryCode != null and dimensionSubCategoryCode != ""'>
            AND dimension_subcategory_code = #{dimensionSubCategoryCode}
        </if>
        <if test='dimensionSubDetailCode != null and dimensionSubDetailCode != ""'>
            AND dimension_sub_detail_code = #{dimensionSubDetailCode}
        </if>
        <if test='spartCode != null and spartCode != ""'>
            AND spart_code = #{spartCode}
        </if>
        <!-- 存在lv1DimensionSet=no permission，且lv2是最大权限的情况，所以需要只走下面第二个判断逻辑-->
        <if test='groupLevel =="LV1" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel =="LV2" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <!-- 除去如上两种情况，其他的需要走后面两种情况-->
        <choose>
            <when test='lv1DimensionSet != null and lv1DimensionSet.size() == 1 and lv1DimensionSet.contains("NO_PERMISSION")'>

            </when>
            <otherwise>
                <if test='groupLevel !="LV0" and groupLevel !="LV1" and groupLevel !="LV2" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
                    <foreach collection='lv1DimensionSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
            </otherwise>
        </choose>
        <if test='groupLevel !="LV0" and groupLevel !="LV1" and groupLevel !="LV2" and (viewFlag !="0" and viewFlag !="1" and viewFlag !="2" and viewFlag !="9")  and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv1ProdRndTeamCodeSet != null and lv1ProdRndTeamCodeSet.size() > 0'>
            <foreach collection='lv1ProdRndTeamCodeSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv2ProdRndTeamCodeSet != null and lv2ProdRndTeamCodeSet.size() > 0'>
            <foreach collection='lv2ProdRndTeamCodeSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv1CodeList != null and lv1CodeList.size() > 0'>
            <foreach collection='lv1CodeList' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv2CodeList != null and lv2CodeList.size() > 0'>
            <foreach collection='lv2CodeList' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='lv3CodeList != null and lv3CodeList.size() > 0'>
            <foreach collection='lv3CodeList' item="code" open="AND lv3_prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionCodeList != null and dimensionCodeList.size() > 0'>
            <foreach collection='dimensionCodeList' item="code" open="AND dimension_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionSubcategoryCodeList != null and dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='dimensionSubcategoryCodeList' item="code" open="AND dimension_subcategory_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='dimensionSubDetailCodeList != null and dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='dimensionSubDetailCodeList' item="code" open="AND dimension_sub_detail_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='spartCodeList != null and spartCodeList.size() > 0'>
            <foreach collection='spartCodeList' item="code" open="AND spart_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupCodeList != null and groupCodeList.size() > 0'>
            <choose>
                <when test='groupLevel =="SUBCATEGORY"'>
                    <foreach collection='groupCodeList' item="code" open="AND dimension_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='groupLevel =="SUB_DETAIL"'>
                    <foreach collection='groupCodeList' item="code" open="AND dimension_subcategory_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
    </select>

</mapper>
