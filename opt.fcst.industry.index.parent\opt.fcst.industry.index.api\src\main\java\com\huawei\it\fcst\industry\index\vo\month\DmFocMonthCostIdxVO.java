/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.month;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huawei.it.fcst.annotations.ExportAttribute;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * The Entity of DmFocMonthCostIdxT
 *
 * <AUTHOR>
 * @since 2023/03/10
 */
@Getter
@Setter
@NoArgsConstructor
public class DmFocMonthCostIdxVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("版本ID")
    private Long versionId;

    @ApiModelProperty("会计年")
    private Long periodYear;

    @ApiModelProperty("基期会计期")
    @ExportAttribute(sort = 0, dataType = "Number")
    private Long periodId;

    @ApiModelProperty("基期")
    private Long basePeriodId;

    @ApiModelProperty("重量级团队CODE")
    private String prodRndTeamCode;

    @ApiModelProperty("重量级团队中文名称")
    private String prodRndTeamCnName;

    @ApiModelProperty("重量级团队lv0中文名称")
    private String lv0ProdRdTeamCnName;

    @ApiModelProperty("重量级团队lv0编码")
    private String lv0ProdRndTeamCode;

    @ApiModelProperty("重量级团队lv1中文名称")
    private String lv1ProdRdTeamCnName;

    @ApiModelProperty("重量级团队lv1编码")
    private String lv1ProdRndTeamCode;

    @ApiModelProperty("重量级团队lv2中文名称")
    private String lv2ProdRdTeamCnName;

    @ApiModelProperty("重量级团队lv2编码")
    private String lv2ProdRndTeamCode;

    @ApiModelProperty("重量级团队lv3中文名称")
    private String lv3ProdRdTeamCnName;

    @ApiModelProperty("重量级团队lv3编码")
    private String lv3ProdRndTeamCode;

    private String lv4ProdRdTeamCnName;

    private String lv4ProdRndTeamCode;

    @ApiModelProperty("专家团中文名称")
    private String l3CegCnName;

    @ApiModelProperty("专家团编码")
    private String l3CegCode;

    @ApiModelProperty("模块中文名称")
    private String l4CegCnName;

    @ApiModelProperty("模块编码")
    private String l4CegCode;

    @ApiModelProperty("品类中文名称")
    private String categoryCnName;

    @ApiModelProperty("品类编码")
    private String categoryCode;

    @ApiModelProperty("发货对象中文名称")
    private String shippingObjectCnName;

    @ApiModelProperty("发货对象编码")
    private String shippingObjectCode;

    @ApiModelProperty("制造对象中文名称")
    private String manufactureObjectCnName;

    @ApiModelProperty("制造对象编码")
    private String manufactureObjectCode;

    @ApiModelProperty("采购层级CODE")
    private String purCode;

    @ApiModelProperty("采购层级中文名称")
    private String purCnName;

    @ApiModelProperty("分层级CODE")
    @ExportAttribute(sort = 4)
    private String groupCode;

    @ApiModelProperty("分层级中文名称")
    @ExportAttribute(sort = 3)
    private String groupCnName;

    @ApiModelProperty("GROUP层级(ICT/LV1/LV2/CEG/CATEGORY/ITEM)")
    private String groupLevel;

    @ApiModelProperty("成本指数")
    @ExportAttribute(sort = 5, dataType = "Number")
    private Double costIndex;

    @ApiModelProperty("父级CODE")
    private String parentCode;

    @ApiModelProperty("父级name")
    @ExportAttribute(sort = 2)
    private String parentCnName;

    @ApiModelProperty("删除标识(未删除：N，已删除：Y)")
    private String delFlag;

    @ApiModelProperty("创建人")
    private String createdBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    @ApiModelProperty("创建日期")
    private Timestamp creationDate;

    @ApiModelProperty("最后更新人")
    private String lastUpdatedBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    @ApiModelProperty("最后更新日期")
    private Timestamp lastUpdateDate;

    @ApiModelProperty("视角标识，用于区分不同视角下的品类数据(0: ICT层级, 1:ICT-LV1层级, 2:ICT-LV1-LV2层级)")
    private String viewFlag;

    @ApiModelProperty("补齐标识, 用于区分补齐数据和原始数据(Y:补齐数据, N:原始数据)")
    private String appendFlag;

    @ApiModelProperty("权重")
    private Double weightRate;

    @ApiModelProperty("权重占比")
    private String weightPercent;

    @ApiModelProperty("实际数：S，预测数：Y")
    private String scenarioFlag;

    @ApiModelProperty("l1")
    private String l1Name;

    @ApiModelProperty("l2")
    private String l2Name;

    @ApiModelProperty("量纲颗粒度code")
    private String dmsCode;

    @ApiModelProperty("量纲颗粒度名称")
    private String dmsCnName;

    @ApiModelProperty("COA编码")
    private String coaCode;

    @ApiModelProperty("COA名称")
    private String coaCnName;

    @ApiModelProperty("量纲颗粒度code")
    private String dimensionCode;

    @ApiModelProperty("量纲颗粒度名称")
    private String dimensionCnName;

    @ApiModelProperty("量纲颗粒度code")
    private String dimensionSubCategoryCode;

    @ApiModelProperty("量纲颗粒度名称")
    private String dimensionSubCategoryCnName;

    @ApiModelProperty("量纲颗粒度code")
    private String dimensionSubDetailCode;

    @ApiModelProperty("量纲颗粒度名称")
    private String dimensionSubDetailCnName;

    @ApiModelProperty("spartCode")
    private String spartCode;

    @ApiModelProperty("spart名称")
    private String spartCnName;

    @ApiModelProperty("汇总组合名称")
    private String customCnName;

    @ApiModelProperty("汇总组合id")
    private Long customId;

    // 是否包含汇总组合
    private Boolean isContainComb;

    // 提供前端返回值是否汇总组合
    private String isComb;

    /**
     * 顶部的成本类型下拉框:
     *      制造成本：MANUFACTURE，采购成本：PURCHASE，总成本：TOTAL
     */
    @ExportAttribute(sort = 1)
    private String costType;
}
