<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IDmFocTotalMonthCostIdxDao">
    <resultMap type="com.huawei.it.fcst.industry.index.vo.month.DmFocMonthCostIdxVO" id="resultMap">
        <result property="id" column="id"/>
        <result property="versionId" column="version_id"/>
        <result property="periodYear" column="period_year"/>
        <result property="periodId" column="period_id"/>
        <result property="basePeriodId" column="base_period_id"/>
        <result property="prodRndTeamCode" column="prod_rnd_team_code"/>
        <result property="prodRndTeamCnName" column="prod_rnd_team_cn_name"/>
        <result property="lv0ProdRdTeamCnName" column="lv0_prod_rd_team_cn_name"/>
        <result property="lv0ProdRndTeamCode" column="lv0_prod_rnd_team_code"/>
        <result property="lv1ProdRdTeamCnName" column="lv1_prod_rd_team_cn_name"/>
        <result property="lv1ProdRndTeamCode" column="lv1_prod_rnd_team_code"/>
        <result property="lv2ProdRdTeamCnName" column="lv2_prod_rd_team_cn_name"/>
        <result property="lv2ProdRndTeamCode" column="lv2_prod_rnd_team_code"/>
        <result property="lv3ProdRdTeamCnName" column="lv3_prod_rd_team_cn_name"/>
        <result property="lv3ProdRndTeamCode" column="lv3_prod_rnd_team_code"/>
        <result property="lv4ProdRdTeamCnName" column="lv4_prod_rd_team_cn_name"/>
        <result property="lv4ProdRndTeamCode" column="lv4_prod_rnd_team_code"/>
        <result property="groupCode" column="group_code"/>
        <result property="groupCnName" column="group_cn_name"/>
        <result property="groupLevel" column="group_level"/>
        <result property="costIndex" column="cost_index"/>
        <result property="parentCode" column="parent_code"/>
        <result property="parentCnName" column="parent_cn_name"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="appendFlag" column="append_flag"/>
        <result property="weightRate" column="weight_rate"/>
        <result property="weightPercent" column="weight_percent"/>
        <result property="scenarioFlag" column="scenario_flag"/>
        <result property="l1Name" column="l1_name"/>
        <result property="l2Name" column="l2_name"/>
        <result property="dmsCode" column="dms_code"/>
        <result property="dmsCnName" column="dms_cn_name"/>
        <result property="coaCode" column="coa_code"/>
        <result property="coaCnName" column="coa_cn_name"/>
        <result property="dimensionCode" column="dimension_code"/>
        <result property="dimensionCnName" column="dimension_cn_name"/>
        <result property="dimensionSubCategoryCode" column="dimension_subcategory_code"/>
        <result property="dimensionSubCategoryCnName" column="dimension_subcategory_cn_name"/>
        <result property="dimensionSubDetailCode" column="dimension_sub_detail_code"/>
        <result property="dimensionSubDetailCnName" column="dimension_sub_detail_cn_name"/>
        <result property="spartCode" column="spart_code"/>
        <result property="spartCnName" column="spart_cn_name"/>
        <result property="customId" column="custom_id"/>
        <result property="customCnName" column="custom_cn_name"/>
        <result property="costType" column="cost_type"/>
    </resultMap>

    <sql id="allField">
        distinct
        <if test = 'searchParamsVO.granularityType == "P"'>
            l1_name,l2_name,
        </if>
        <if test = 'searchParamsVO.granularityType == "D"'>
            <if test='searchParamsVO.industryOrg == "ENERGY"'>
                coa_code, coa_cn_name,
            </if>
            dimension_code,dimension_cn_name,dimension_subcategory_code,
            dimension_subcategory_cn_name,dimension_sub_detail_code,dimension_sub_detail_cn_name,
            spart_code,spart_cn_name,
        </if>
        prod_rnd_team_cn_name,prod_rnd_team_code,
        group_level,
        group_code,
        group_cn_name,
        period_year,
        period_id,
        ROUND(cost_index, 2) AS cost_index,
        version_id,
        'T' AS cost_type,
        last_update_date
    </sql>


    <sql id="compareAllField">
        distinct info.lv0_prod_rd_team_cn_name,info.lv0_prod_rnd_team_code,
        <choose>
            <when test='searchParamsVO.teamLevel == "LV1"'>
                info.lv1_prod_rd_team_cn_name,info.lv1_prod_rnd_team_code,
            </when>
            <when test='searchParamsVO.teamLevel == "LV2"'>
                info.lv1_prod_rd_team_cn_name,info.lv1_prod_rnd_team_code,info.lv2_prod_rd_team_cn_name,info.lv2_prod_rnd_team_code,
            </when>
            <when test='searchParamsVO.teamLevel == "LV3"'>
                info.lv1_prod_rd_team_cn_name,info.lv1_prod_rnd_team_code,
                info.lv2_prod_rd_team_cn_name,info.lv2_prod_rnd_team_code,
                info.lv3_prod_rd_team_cn_name,info.lv3_prod_rnd_team_code,
            </when>
            <when test='searchParamsVO.industryOrg == "IAS" and searchParamsVO.teamLevel == "LV4"'>
                info.lv1_prod_rd_team_cn_name,info.lv1_prod_rnd_team_code,
                info.lv2_prod_rd_team_cn_name,info.lv2_prod_rnd_team_code,
                info.lv3_prod_rd_team_cn_name,info.lv3_prod_rnd_team_code,
                info.lv4_prod_rd_team_cn_name,info.lv4_prod_rnd_team_code,
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test = 'searchParamsVO.granularityType == "P"'>
            t.l1_name, t.l2_name,
        </if>
        <if test = 'searchParamsVO.granularityType == "D"'>
            <if test='searchParamsVO.industryOrg == "ENERGY"'>
                t.coa_code, t.coa_cn_name,
            </if>
            t.dimension_code,t.dimension_cn_name,t.dimension_subcategory_code,
            t.dimension_subcategory_cn_name,t.dimension_sub_detail_code,t.dimension_sub_detail_cn_name,
            t.spart_code,t.spart_cn_name,
        </if>
        t.prod_rnd_team_cn_name, t.prod_rnd_team_code,
        t.group_level,
        t.group_code,
        t.group_cn_name,
        t.period_year,
        t.period_id,
        ROUND(t.cost_index, 2) AS cost_index,
        t.version_id,
        'T' AS cost_type,
        t.last_update_date
    </sql>

    <sql id="multiAllField">
        <if test='searchParamsVO.granularityType == "U"'>
            SELECT DISTINCT T1.prod_rnd_team_cn_name,T1.prod_rnd_team_code, T1.parent_code,
            T1.parent_cn_name,T1.period_id,T1.group_level, T1.group_code,T1.group_cn_name,
            ROUND(T1.cost_index, 2) AS cost_index,
            T1.append_flag,
            T2.weight_rate,
            'T' AS cost_type,
            ROUND(T2.weight_rate * 100, 2) || '%' AS weight_percent
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_total_month_cost_idx_t T1
            LEFT JOIN fin_dm_opt_foi.${searchParamsVO.tablePreFix}_total_month_weight_t T2
        </if>
        <if test='searchParamsVO.granularityType == "P"'>
            SELECT DISTINCT T1.prod_rnd_team_cn_name,T1.prod_rnd_team_code,T1.l1_name,T1.l2_name,
            T1.parent_code,T1.parent_cn_name,T1.period_id, T1.group_level, T1.group_code,T1.group_cn_name,
            ROUND(T1.cost_index, 2) AS cost_index,
            T1.append_flag,
            T2.weight_rate,
            'T' AS cost_type,
            ROUND(T2.weight_rate * 100, 2) || '%' AS weight_percent
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_total_pft_month_cost_idx_t T1
            LEFT JOIN fin_dm_opt_foi.${searchParamsVO.tablePreFix}_total_pft_month_weight_t T2
        </if>
        <if test='searchParamsVO.granularityType == "D"'>
            SELECT DISTINCT T1.prod_rnd_team_cn_name,T1.prod_rnd_team_code,
            <if test='searchParamsVO.industryOrg == "ENERGY"'>
                t1.coa_code, t1.coa_cn_name,
            </if>
            t1.dimension_code,t1.dimension_cn_name,
            t1.dimension_subcategory_code,t1.dimension_subcategory_cn_name,
            t1.dimension_sub_detail_code,t1.dimension_sub_detail_cn_name,
            t1.spart_code,t1.spart_cn_name,
            T1.parent_code,T1.parent_cn_name,
            T1.period_id,T1.group_level, T1.group_code,T1.group_cn_name,
            ROUND(T1.cost_index, 2) AS cost_index,
            T1.append_flag,
            T2.weight_rate,
            'T' AS cost_type,
            ROUND(T2.weight_rate * 100, 2) || '%' AS weight_percent
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_total_dms_month_cost_idx_t T1
            LEFT JOIN fin_dm_opt_foi.${searchParamsVO.tablePreFix}_total_dms_month_weight_t T2
        </if>
    </sql>

    <sql id="itemMultiFiled">
        <if test='searchParamsVO.granularityType == "U"'>
            SELECT DISTINCT T1.prod_rnd_team_cn_name,T1.prod_rnd_team_code,
            T1.parent_code,T1.parent_cn_name,
            T1.period_id, T1.group_level, T1.group_code,T1.group_cn_name,
            'T' AS cost_type,
            ROUND(T1.cost_index, 2) AS cost_index,
            T1.append_flag
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_total_month_cost_idx_t T1
        </if>
        <if test='searchParamsVO.granularityType == "P"'>
            SELECT DISTINCT T1.prod_rnd_team_cn_name,T1.prod_rnd_team_code,T1.l1_name,T1.l2_name,
            T1.parent_code,T1.parent_cn_name,
            T1.period_id, T1.group_level, T1.group_code,T1.group_cn_name,
            'T' AS cost_type,
            ROUND(T1.cost_index, 2) AS cost_index,
            T1.append_flag
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_total_pft_month_cost_idx_t T1
        </if>
        <if test='searchParamsVO.granularityType == "D"'>
            SELECT DISTINCT T1.prod_rnd_team_cn_name,T1.prod_rnd_team_code,
            <if test='searchParamsVO.industryOrg == "ENERGY"'>
                t1.coa_code, t1.coa_cn_name,
            </if>
            t1.dimension_code,t1.dimension_cn_name,
            t1.dimension_subcategory_code,t1.dimension_subcategory_cn_name,
            t1.dimension_sub_detail_code,t1.dimension_sub_detail_cn_name,
            t1.spart_code,t1.spart_cn_name,
            T1.parent_code,T1.parent_cn_name,
            T1.period_id, T1.group_level, T1.group_code,T1.group_cn_name,
            'T' AS cost_type,
            ROUND(T1.cost_index, 2) AS cost_index,
            T1.append_flag
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_total_dms_month_cost_idx_t T1
        </if>
    </sql>

    <select id="findTotalPriceIdxByBasePeriodId" resultType="int">
        SELECT COUNT(1)
        <if test='searchParamsVO.granularityType == "U"'>
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_total_month_cost_idx_t
        </if>
        <if test='searchParamsVO.granularityType == "P"'>
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_total_pft_month_cost_idx_t
        </if>
        <if test='searchParamsVO.granularityType == "D"'>
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_total_dms_month_cost_idx_t
        </if>
        WHERE del_flag = 'N'
        <if test='searchParamsVO.basePeriodId != null'>
            AND base_period_id = #{searchParamsVO.basePeriodId}
        </if>
        <if test='searchParamsVO.periodstartTime != null and searchParamsVO.periodstartTime != ""'>
            AND period_id <![CDATA[ >= ]]> #{searchParamsVO.periodStartTime}
        </if>
        <if test='searchParamsVO.periodEndTime != null and searchParamsVO.periodEndTime != ""'>
            AND period_id <![CDATA[ <= ]]> #{searchParamsVO.periodEndTime}
        </if>
        <if test='searchParamsVO.prodRndTeamCodeList != null and searchParamsVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='searchParamsVO.prodRndTeamCodeList' item="code" open="AND prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
            AND lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
            AND caliber_Flag = #{searchParamsVO.caliberFlag}
        </if>
        <if test='searchParamsVO.versionId != null and searchParamsVO.versionId != ""'>
            AND version_id = #{searchParamsVO.versionId}
        </if>
        <if test='searchParamsVO.groupCodeList != null and searchParamsVO.groupCodeList.size() > 0'>
            <foreach collection='searchParamsVO.groupCodeList' item="code" open="AND group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
            <if test='searchParamsVO.parentLevel != null and searchParamsVO.parentLevel != ""'>
                AND group_level = #{searchParamsVO.parentLevel,jdbcType=VARCHAR}
            </if>
        </if>
        <if test='searchParamsVO.subGroupCodeList != null and searchParamsVO.subGroupCodeList.size() > 0'>
            <foreach collection='searchParamsVO.subGroupCodeList' item="code" open="AND group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
            <if test='searchParamsVO.groupLevel != null and searchParamsVO.groupLevel != ""'>
                AND group_level = #{searchParamsVO.groupLevel,jdbcType=VARCHAR}
            </if>
        </if>
        <if test='searchParamsVO.l1NameList != null and searchParamsVO.l1NameList.size() > 0'>
            <foreach collection='searchParamsVO.l1NameList' item="code" open="AND l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l2NameList != null and searchParamsVO.l2NameList.size() > 0'>
            <foreach collection='searchParamsVO.l2NameList' item="code" open="AND l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionCodeList != null and searchParamsVO.dimensionCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionCodeList' item="code" open="AND dimension_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubcategoryCodeList != null and searchParamsVO.dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubcategoryCodeList' item="code" open="AND dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.spartCodeList != null and searchParamsVO.spartCodeList.size() > 0'>
            <foreach collection='searchParamsVO.spartCodeList' item="code" open="AND spart_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubDetailCodeList != null and searchParamsVO.dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubDetailCodeList' item="code" open="AND dimension_sub_detail_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.parentCodeList != null and searchParamsVO.parentCodeList.size() > 0'>
            <foreach collection='searchParamsVO.parentCodeList' item="code" open="AND parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
            AND oversea_flag = #{searchParamsVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.viewFlag != null'>
            AND view_flag = #{searchParamsVO.viewFlag}
        </if>
    </select>

    <sql id="searchFiled">
        <choose>
                <when test ='searchParamsVO.granularityType == "U"'>
                    left join fin_dm_opt_foi.${searchParamsVO.tablePreFix}_total_view_info_d info
                </when>
                <when test ='searchParamsVO.granularityType == "P"'>
                    left join fin_dm_opt_foi.${searchParamsVO.tablePreFix}_total_pft_view_info_d info
                </when>
                <when test ='searchParamsVO.granularityType == "D"'>
                    left join fin_dm_opt_foi.${searchParamsVO.tablePreFix}_total_dms_view_info_d info
                </when>
            </choose>
            <choose>
                <when test ='searchParamsVO.teamLevel == "LV0"'>
                    on t.prod_rnd_team_code = info.lv0_prod_rnd_team_code
                </when>
                <when test ='searchParamsVO.teamLevel == "LV1"'>
                    on t.prod_rnd_team_code = info.lv1_prod_rnd_team_code
                </when>
                <when test ='searchParamsVO.teamLevel == "LV2"'>
                    on t.prod_rnd_team_code = info.lv2_prod_rnd_team_code
                </when>
                <when test ='searchParamsVO.teamLevel == "LV3"'>
                    on t.prod_rnd_team_code = info.lv3_prod_rnd_team_code
                </when>
                <when test='searchParamsVO.industryOrg == "IAS" and searchParamsVO.teamLevel == "LV4"'>
                    on t.prod_rnd_team_code = info.lv4_prod_rnd_team_code
                </when>
            </choose>
            and info.del_flag = 'N'
            and info.page_flag = 'MONTH'
            and t.view_flag = info.view_flag
            and t.caliber_flag = info.caliber_flag
            and t.oversea_flag = info.oversea_flag
            and t.lv0_prod_list_code = info.lv0_prod_list_code
        <if test='searchParamsVO.industryOrg == "ENERGY" and searchParamsVO.viewFlag == "12"
                and searchParamsVO.groupLevel != "LV0" and searchParamsVO.groupLevel != "LV1"
                and searchParamsVO.groupLevel != "LV2" and searchParamsVO.groupLevel != "LV3"'>
            and t.coa_code = info.coa_code
        </if>
            <choose>
                <when test='searchParamsVO.groupLevel == "LV0"'>
                    and t.group_code = info.lv0_prod_rnd_team_code
                </when>
                <when test='searchParamsVO.groupLevel == "LV1"'>
                    and t.group_code = info.lv1_prod_rnd_team_code
                </when>
                <when test='searchParamsVO.groupLevel == "LV2"'>
                    and t.group_code = info.lv2_prod_rnd_team_code
                </when>
                <when test='searchParamsVO.groupLevel == "LV3"'>
                    and t.group_code = info.lv3_prod_rnd_team_code
                </when>
                <when test='searchParamsVO.industryOrg == "IAS" and searchParamsVO.groupLevel == "LV4"'>
                    and t.group_code = info.lv4_prod_rnd_team_code
                </when>
                <when test='searchParamsVO.groupLevel == "L1"'>
                    and t.l1_name = info.l1_name
                    and t.group_code = info.l1_name
                </when>
                <when test='searchParamsVO.groupLevel == "L2"'>
                    and t.l1_name = info.l1_name
                    and t.l2_name = info.l2_name
                    and t.group_code = info.l2_name
                </when>
                <when test='searchParamsVO.groupLevel == "DIMENSION"'>
                    and t.dimension_code = info.dimension_code
                    and t.group_code = info.dimension_code
                </when>
                <when test='searchParamsVO.groupLevel == "SUBCATEGORY"'>
                    and t.dimension_code = info.dimension_code
                    and t.dimension_subcategory_code = info.dimension_subcategory_code
                    and t.group_code = info.dimension_subcategory_code
                </when>
                <when test='searchParamsVO.groupLevel == "SUB_DETAIL"'>
                    and t.dimension_code = info.dimension_code
                    and t.dimension_subcategory_code = info.dimension_subcategory_code
                    and t.dimension_sub_detail_code = info.dimension_sub_detail_code
                    and t.group_code = info.dimension_sub_detail_code
                </when>
                <when test='searchParamsVO.groupLevel == "SPART"'>
                    and t.dimension_code = info.dimension_code
                    and t.dimension_subcategory_code = info.dimension_subcategory_code
                    and t.dimension_sub_detail_code = info.dimension_sub_detail_code
                    and t.spart_code = info.spart_code
                    and t.group_code = info.spart_code
                </when>
                <otherwise>
                </otherwise>
            </choose>
    </sql>

    <select id="findTotalPriceIndexVOList" resultMap="resultMap">
        SELECT
        <include refid="allField"/>
        <if test='searchParamsVO.granularityType == "U"'>
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_total_month_cost_idx_t
        </if>
        <if test='searchParamsVO.granularityType == "P"'>
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_total_pft_month_cost_idx_t
        </if>
        <if test='searchParamsVO.granularityType == "D"'>
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_total_dms_month_cost_idx_t
        </if>
        WHERE del_flag = 'N'
        <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
            AND caliber_Flag = #{searchParamsVO.caliberFlag}
        </if>
        <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
            and lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.coaCodeList != null and searchParamsVO.coaCodeList.size() > 0'>
            <foreach collection='searchParamsVO.coaCodeList' item="code" open="AND coa_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionCodeList != null and searchParamsVO.dimensionCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionCodeList' item="code" open="AND dimension_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubcategoryCodeList != null and searchParamsVO.dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubcategoryCodeList' item="code" open="AND dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubDetailCodeList != null and searchParamsVO.dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubDetailCodeList' item="code" open="AND dimension_sub_detail_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.spartCodeList != null and searchParamsVO.spartCodeList.size() > 0'>
            <foreach collection='searchParamsVO.spartCodeList' item="code" open="AND spart_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l1NameList != null and searchParamsVO.l1NameList.size() > 0'>
            <foreach collection='searchParamsVO.l1NameList' item="code" open="AND l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l2NameList != null and searchParamsVO.l2NameList.size() > 0'>
            <foreach collection='searchParamsVO.l2NameList' item="code" open="AND l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.prodRndTeamCodeList != null and searchParamsVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='searchParamsVO.prodRndTeamCodeList' item="code" open="AND prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupCodeList != null and searchParamsVO.groupCodeList != ""'>
            <foreach collection='searchParamsVO.groupCodeList' item="code" open="AND group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.basePeriodId != null'>
            AND base_period_id = #{searchParamsVO.basePeriodId}
        </if>
        <if test='searchParamsVO.versionId != null'>
            AND version_id = #{searchParamsVO.versionId}
        </if>
        <if test='searchParamsVO.periodStartTime != null and searchParamsVO.periodStartTime != ""'>
            AND period_id <![CDATA[ >= ]]> #{searchParamsVO.periodStartTime}
        </if>
        <if test='searchParamsVO.periodEndTime != null and searchParamsVO.periodEndTime != ""'>
            AND period_id <![CDATA[ <= ]]> #{searchParamsVO.periodEndTime}
        </if>
        <if test='searchParamsVO.groupLevel =="LV1" and searchParamsVO.lv1DimensionSet != null and searchParamsVO.lv1DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv1DimensionSet' item="code" open="AND prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupLevel =="LV2" and searchParamsVO.lv2DimensionSet != null and searchParamsVO.lv2DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv2DimensionSet' item="code" open="AND prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
            and oversea_flag = #{searchParamsVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.viewFlag != null'>
            AND view_flag = #{searchParamsVO.viewFlag}
        </if>
        <if test='searchParamsVO.groupLevel != null and searchParamsVO.groupLevel != ""'>
            AND group_level = #{searchParamsVO.groupLevel}
        </if>
        ORDER BY period_id
    </select>

    <select id="findTotalComparePriceIndexVOList" resultMap="resultMap">
        select
        <include refid="compareAllField"/>
        <if test='searchParamsVO.granularityType == "U"'>
            from fin_dm_opt_foi.${searchParamsVO.tablePreFix}_total_month_cost_idx_t t
            <include refid="searchFiled"/>
        </if>
        <if test='searchParamsVO.granularityType == "P"'>
            from fin_dm_opt_foi.${searchParamsVO.tablePreFix}_total_pft_month_cost_idx_t t
            <include refid="searchFiled"/>
        </if>
        <if test='searchParamsVO.granularityType == "D"'>
            from fin_dm_opt_foi.${searchParamsVO.tablePreFix}_total_dms_month_cost_idx_t t
            <include refid="searchFiled"/>
        </if>
        WHERE t.del_flag = 'N'
        and info.version_id = #{searchParamsVO.monthVersionId}
        <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
            AND t.caliber_Flag = #{searchParamsVO.caliberFlag}
        </if>
        <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
            and t.lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.coaCodeList != null and searchParamsVO.coaCodeList.size() > 0'>
            <foreach collection='searchParamsVO.coaCodeList' item="code" open="AND t.coa_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionCodeList != null and searchParamsVO.dimensionCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionCodeList' item="code" open="AND t.dimension_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubcategoryCodeList != null and searchParamsVO.dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubcategoryCodeList' item="code" open="AND t.dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubDetailCodeList != null and searchParamsVO.dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubDetailCodeList' item="code" open="AND t.dimension_sub_detail_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.spartCodeList != null and searchParamsVO.spartCodeList.size() > 0'>
            <foreach collection='searchParamsVO.spartCodeList' item="code" open="AND t.spart_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l1NameList != null and searchParamsVO.l1NameList.size() > 0'>
            <foreach collection='searchParamsVO.l1NameList' item="code" open="AND t.l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l2NameList != null and searchParamsVO.l2NameList.size() > 0'>
            <foreach collection='searchParamsVO.l2NameList' item="code" open="AND t.l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.prodRndTeamCodeList != null and searchParamsVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='searchParamsVO.prodRndTeamCodeList' item="code" open="AND t.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupCodeList != null and searchParamsVO.groupCodeList != ""'>
            <foreach collection='searchParamsVO.groupCodeList' item="code" open="AND t.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.basePeriodId != null'>
            AND t.base_period_id = #{searchParamsVO.basePeriodId}
        </if>
        <if test='searchParamsVO.versionId != null'>
            AND t.version_id = #{searchParamsVO.versionId}
        </if>
        <if test='searchParamsVO.periodStartTime != null and searchParamsVO.periodStartTime != ""'>
            AND t.period_id <![CDATA[ >= ]]> #{searchParamsVO.periodStartTime}
        </if>
        <if test='searchParamsVO.periodEndTime != null and searchParamsVO.periodEndTime != ""'>
            AND t.period_id <![CDATA[ <= ]]> #{searchParamsVO.periodEndTime}
        </if>
        <if test='searchParamsVO.groupLevel =="LV1" and searchParamsVO.lv1DimensionSet != null and searchParamsVO.lv1DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv1DimensionSet' item="code" open="AND t.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupLevel =="LV2" and searchParamsVO.lv2DimensionSet != null and searchParamsVO.lv2DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv2DimensionSet' item="code" open="AND t.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupLevel != null and searchParamsVO.groupLevel != ""'>
            AND t.group_level = #{searchParamsVO.groupLevel}
        </if>
        <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
            and t.oversea_flag = #{searchParamsVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.viewFlag != null'>
            AND t.view_flag = #{searchParamsVO.viewFlag}
        </if>
        ORDER BY t.period_id
    </select>

    <select id="findTotalPriceIndexByMultiDim" resultMap="resultMap">
        <choose>
            <when test='searchParamsVO.groupLevel == "ITEM" or searchParamsVO.groupLevel == "SPART"'>
                <include refid="itemMultiFiled"></include>
                WHERE T1.del_flag = 'N'
            </when>
            <otherwise>
                <include refid="multiAllField"/>
                ON T1.group_code = T2.group_code
                AND T1.prod_rnd_team_code = T2.prod_rnd_team_code
                AND T1.PARENT_CODE = T2.PARENT_CODE
                AND T2.del_flag = 'N' AND T2.COST_TYPE = 'T'
                WHERE T1.del_flag = 'N'
                AND T2.version_id = #{searchParamsVO.versionId}
                <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
                    AND T2.lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode}
                </if>
                <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
                    AND T2.caliber_Flag = #{searchParamsVO.caliberFlag}
                </if>
                <if test='searchParamsVO.coaCodeList != null and searchParamsVO.coaCodeList.size() > 0'>
                    <foreach collection='searchParamsVO.coaCodeList' item="code" open="AND T2.coa_code IN (" close=")" index="index" separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='searchParamsVO.dimensionCodeList != null and searchParamsVO.dimensionCodeList.size() > 0'>
                    <foreach collection='searchParamsVO.dimensionCodeList' item="code" open="AND T2.dimension_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='searchParamsVO.dimensionSubcategoryCodeList != null and searchParamsVO.dimensionSubcategoryCodeList.size() > 0'>
                    <foreach collection='searchParamsVO.dimensionSubcategoryCodeList' item="code" open="AND T2.dimension_subcategory_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='searchParamsVO.dimensionSubDetailCodeList != null and searchParamsVO.dimensionSubDetailCodeList.size() > 0'>
                    <foreach collection='searchParamsVO.dimensionSubDetailCodeList' item="code" open="AND T2.dimension_sub_detail_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='searchParamsVO.spartCodeList != null and searchParamsVO.spartCodeList.size() > 0'>
                    <foreach collection='searchParamsVO.spartCodeList' item="code" open="AND T2.spart_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='searchParamsVO.prodRndTeamCodeList != null and searchParamsVO.prodRndTeamCodeList.size() > 0'>
                    <foreach collection='searchParamsVO.prodRndTeamCodeList' item="code" open="AND T2.prod_rnd_team_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='searchParamsVO.l1NameList != null and searchParamsVO.l1NameList.size() > 0'>
                    <foreach collection='searchParamsVO.l1NameList' item="code" open="AND T2.l1_name IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='searchParamsVO.l2NameList != null and searchParamsVO.l2NameList.size() > 0'>
                    <foreach collection='searchParamsVO.l2NameList' item="code" open="AND T2.l2_name IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='searchParamsVO.parentCodeList != null and searchParamsVO.parentCodeList.size() > 0'>
                    <foreach collection='searchParamsVO.parentCodeList' item="code" open="AND T2.parent_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
                    AND T2.oversea_flag = #{searchParamsVO.overseaFlag}
                </if>
                AND T2.view_flag = #{searchParamsVO.viewFlag}
                <if test='searchParamsVO.groupLevel != null and searchParamsVO.groupLevel !=""'>
                    AND T2.group_level = #{searchParamsVO.groupLevel}
                </if>
            </otherwise>
        </choose>
        AND T1.version_id = #{searchParamsVO.versionId}
        <if test='searchParamsVO.basePeriodId != null'>
            AND T1.base_period_id = #{searchParamsVO.basePeriodId}
        </if>
        <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
            AND T1.caliber_Flag = #{searchParamsVO.caliberFlag}
        </if>
        <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
            and T1.lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.dimensionCodeList != null and searchParamsVO.dimensionCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionCodeList' item="code" open="AND T1.dimension_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubcategoryCodeList != null and searchParamsVO.dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubcategoryCodeList' item="code" open="AND T1.dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubDetailCodeList != null and searchParamsVO.dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubDetailCodeList' item="code" open="AND T1.dimension_sub_detail_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.spartCodeList != null and searchParamsVO.spartCodeList.size() > 0'>
            <foreach collection='searchParamsVO.spartCodeList' item="code" open="AND T1.spart_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.prodRndTeamCodeList != null and searchParamsVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='searchParamsVO.prodRndTeamCodeList' item="code" open="AND T1.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l1NameList != null and searchParamsVO.l1NameList.size() > 0'>
            <foreach collection='searchParamsVO.l1NameList' item="code" open="AND T1.l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l2NameList != null and searchParamsVO.l2NameList.size() > 0'>
            <foreach collection='searchParamsVO.l2NameList' item="code" open="AND T1.l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.parentCodeList != null and searchParamsVO.parentCodeList.size() > 0'>
            <foreach collection='searchParamsVO.parentCodeList' item="code" open="AND T1.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.subGroupCodeList != null and searchParamsVO.subGroupCodeList.size() > 0'>
            <foreach collection='searchParamsVO.subGroupCodeList' item="code" open="AND T1.group_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.periodStartTime != null and searchParamsVO.periodStartTime != ""'>
            AND T1.period_id <![CDATA[ >= ]]> #{searchParamsVO.periodStartTime}
        </if>
        <if test='searchParamsVO.periodEndTime != null and searchParamsVO.periodEndTime != ""'>
            AND T1.period_id <![CDATA[ <= ]]> #{searchParamsVO.periodEndTime}
        </if>
        <if test='searchParamsVO.groupLevel =="LV1" and searchParamsVO.lv1DimensionSet != null and searchParamsVO.lv1DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv1DimensionSet' item="code" open="AND T1.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupLevel =="LV2" and searchParamsVO.lv2DimensionSet != null and searchParamsVO.lv2DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv2DimensionSet' item="code" open="AND T1.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
            and T1.oversea_flag = #{searchParamsVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        AND T1.view_flag = #{searchParamsVO.viewFlag}
        <if test='searchParamsVO.groupLevel != null and searchParamsVO.groupLevel != ""'>
            AND T1.group_level = #{searchParamsVO.groupLevel}
        </if>
        <if test='searchParamsVO.coaCodeList != null and searchParamsVO.coaCodeList.size() > 0'>
            <foreach collection='searchParamsVO.coaCodeList' item="code" open="AND T1.coa_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        ORDER BY T1.group_code, T1.period_id
    </select>

    <select id="findTotalAmpMadePriceIndexChart" resultMap="resultMap">
        <include refid="multiAllField"/>
        ON T1.group_code = T2.group_code
        AND T1.prod_rnd_team_code = T2.prod_rnd_team_code
        AND T1.PARENT_CODE = T2.PARENT_CODE
        <if test='searchParamsVO.industryOrg != null and searchParamsVO.granularityType =="D" and searchParamsVO.industryOrg =="ENERGY"'>
            AND nvl ( T1.coa_code, 'snull' ) = nvl ( T2.coa_code, 'snull' )
        </if>
        AND T2.del_flag = 'N' AND T2.COST_TYPE = 'T'
        WHERE T1.del_flag = 'N'
        AND T2.version_id = #{searchParamsVO.versionId}
        <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
            AND T2.lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode}
        </if>
        <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
            AND T2.caliber_Flag = #{searchParamsVO.caliberFlag}
        </if>
        <if test='searchParamsVO.coaCodeList != null and searchParamsVO.coaCodeList.size() > 0'>
            <foreach collection='searchParamsVO.coaCodeList' item="code" open="AND T2.coa_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionCodeList != null and searchParamsVO.dimensionCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionCodeList' item="code" open="AND T2.dimension_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubcategoryCodeList != null and searchParamsVO.dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubcategoryCodeList' item="code"
                     open="AND T2.dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubDetailCodeList != null and searchParamsVO.dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubDetailCodeList' item="code"
                     open="AND T2.dimension_sub_detail_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.spartCodeList != null and searchParamsVO.spartCodeList.size() > 0'>
            <foreach collection='searchParamsVO.spartCodeList' item="code" open="AND T2.spart_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.prodRndTeamCodeList != null and searchParamsVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='searchParamsVO.prodRndTeamCodeList' item="code" open="AND T2.prod_rnd_team_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l1NameList != null and searchParamsVO.l1NameList.size() > 0'>
            <foreach collection='searchParamsVO.l1NameList' item="code" open="AND T2.l1_name IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l2NameList != null and searchParamsVO.l2NameList.size() > 0'>
            <foreach collection='searchParamsVO.l2NameList' item="code" open="AND T2.l2_name IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.parentCodeList != null and searchParamsVO.parentCodeList.size() > 0'>
            <foreach collection='searchParamsVO.parentCodeList' item="code" open="AND T2.parent_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
            AND T2.oversea_flag = #{searchParamsVO.overseaFlag}
        </if>
        AND T2.view_flag = #{searchParamsVO.viewFlag}
        <if test='searchParamsVO.groupLevel != null and searchParamsVO.groupLevel !=""'>
            AND T2.group_level = #{searchParamsVO.groupLevel}
        </if>
        AND T1.version_id = #{searchParamsVO.versionId}
        <if test='searchParamsVO.basePeriodId != null'>
            AND T1.base_period_id = #{searchParamsVO.basePeriodId}
        </if>
        <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
            AND T1.caliber_Flag = #{searchParamsVO.caliberFlag}
        </if>
        <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
            and T1.lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.dimensionCodeList != null and searchParamsVO.dimensionCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionCodeList' item="code" open="AND T1.dimension_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubcategoryCodeList != null and searchParamsVO.dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubcategoryCodeList' item="code"
                     open="AND T1.dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubDetailCodeList != null and searchParamsVO.dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubDetailCodeList' item="code"
                     open="AND T1.dimension_sub_detail_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.spartCodeList != null and searchParamsVO.spartCodeList.size() > 0'>
            <foreach collection='searchParamsVO.spartCodeList' item="code" open="AND T1.spart_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.prodRndTeamCodeList != null and searchParamsVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='searchParamsVO.prodRndTeamCodeList' item="code" open="AND T1.prod_rnd_team_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l1NameList != null and searchParamsVO.l1NameList.size() > 0'>
            <foreach collection='searchParamsVO.l1NameList' item="code" open="AND T1.l1_name IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l2NameList != null and searchParamsVO.l2NameList.size() > 0'>
            <foreach collection='searchParamsVO.l2NameList' item="code" open="AND T1.l2_name IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.parentCodeList != null and searchParamsVO.parentCodeList.size() > 0'>
            <foreach collection='searchParamsVO.parentCodeList' item="code" open="AND T1.parent_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.subGroupCodeList != null and searchParamsVO.subGroupCodeList.size() > 0'>
            <foreach collection='searchParamsVO.subGroupCodeList' item="code" open="AND T1.group_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.begin != null and searchParamsVO.begin != "" and searchParamsVO.end != null and searchParamsVO.end != ""'>
            AND (T1.period_id <![CDATA[ = ]]> #{searchParamsVO.begin} or T1.period_id <![CDATA[ = ]]> #{searchParamsVO.end})
        </if>
        <if test='searchParamsVO.groupLevel =="LV1" and searchParamsVO.lv1DimensionSet != null and searchParamsVO.lv1DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv1DimensionSet' item="code" open="AND T1.prod_rnd_team_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupLevel =="LV2" and searchParamsVO.lv2DimensionSet != null and searchParamsVO.lv2DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv2DimensionSet' item="code" open="AND T1.prod_rnd_team_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
            and T1.oversea_flag = #{searchParamsVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        AND T1.view_flag = #{searchParamsVO.viewFlag}
        <if test='searchParamsVO.groupLevel != null and searchParamsVO.groupLevel != ""'>
            AND T1.group_level = #{searchParamsVO.groupLevel}
        </if>
        <if test='searchParamsVO.coaCodeList != null and searchParamsVO.coaCodeList.size() > 0'>
            <foreach collection='searchParamsVO.coaCodeList' item="code" open="AND T1.coa_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        ORDER BY T1.group_code, T1.period_id
    </select>

    <select id="findPriceIndexExpData" resultMap="resultMap">
        <if test='searchParamsVO.granularityType == "U"'>
            SELECT
            t.prod_rnd_team_cn_name,
            t.prod_rnd_team_code,
            t.parent_code,
            t.parent_cn_name,
            t.period_id,
            t.group_code,
            t.group_cn_name,
            t.group_level,
            'T' AS cost_type,
            ROUND(t.cost_index, 2) AS cost_index
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_total_month_cost_idx_t t
        </if>
        <if test='searchParamsVO.granularityType == "P"'>
            SELECT
            t.prod_rnd_team_cn_name,
            t.prod_rnd_team_code,
            t.parent_code,
            t.parent_cn_name,
            t.l1_name,
            t.l2_name,
            t.period_id,
            t.group_code,
            t.group_cn_name,
            t.group_level,
            'T' AS cost_type,
            ROUND(t.cost_index, 2) AS cost_index
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_total_pft_month_cost_idx_t t
        </if>
        <if test='searchParamsVO.granularityType == "D"'>
            SELECT
            t.prod_rnd_team_cn_name,
            t.prod_rnd_team_code,
            t.parent_code,
            t.parent_cn_name,
            <if test='searchParamsVO.industryOrg == "ENERGY"'>
                t.coa_code, t.coa_cn_name,
            </if>
            t.dimension_code,t.dimension_cn_name,
            t.dimension_subcategory_code,t.dimension_subcategory_cn_name,
            t.dimension_sub_detail_code,t.dimension_sub_detail_cn_name,
            t.spart_code,t.spart_cn_name,
            t.period_id,
            t.group_code,
            t.group_cn_name,
            t.group_level,
            'T' AS cost_type,
            ROUND(t.cost_index, 2) AS cost_index
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_total_dms_month_cost_idx_t t
        </if>
        WHERE t.del_flag = 'N'
        <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
            and t.lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode}
        </if>
        <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
            AND t.caliber_Flag = #{searchParamsVO.caliberFlag}
        </if>
        <if test='searchParamsVO.basePeriodId != null and searchParamsVO.basePeriodId != ""'>
            AND t.base_period_id = #{searchParamsVO.basePeriodId}
        </if>
        <if test='searchParamsVO.versionId != null'>
            AND t.version_id = #{searchParamsVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='searchParamsVO.coaCodeList != null and searchParamsVO.coaCodeList.size() > 0'>
            <foreach collection='searchParamsVO.coaCodeList' item="code" open="AND t.coa_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionCodeList != null and searchParamsVO.dimensionCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionCodeList' item="code" open="AND t.dimension_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubcategoryCodeList != null and searchParamsVO.dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubcategoryCodeList' item="code" open="AND t.dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubDetailCodeList != null and searchParamsVO.dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubDetailCodeList' item="code" open="AND t.dimension_sub_detail_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubDetailCodeList != null and searchParamsVO.dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubDetailCodeList' item="code" open="AND t.dimension_sub_detail_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.spartCodeList != null and searchParamsVO.spartCodeList.size() > 0'>
            <foreach collection='searchParamsVO.spartCodeList' item="code" open="AND t.spart_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.prodRndTeamCodeList != null and searchParamsVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='searchParamsVO.prodRndTeamCodeList' item="code" open="AND t.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l1NameList != null and searchParamsVO.l1NameList.size() > 0'>
            <foreach collection='searchParamsVO.l1NameList' item="code" open="AND t.l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l2NameList != null and searchParamsVO.l2NameList.size() > 0'>
            <foreach collection='searchParamsVO.l2NameList' item="code" open="AND t.l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.parentCodeList != null and searchParamsVO.parentCodeList.size() > 0'>
            <foreach collection='searchParamsVO.parentCodeList' item="code" open="AND t.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.periodStartTime != null and searchParamsVO.periodStartTime != ""'>
            AND t.period_id <![CDATA[ >= ]]> #{searchParamsVO.periodStartTime}
        </if>
        <if test='searchParamsVO.periodEndTime != null and searchParamsVO.periodEndTime != ""'>
            AND t.period_id <![CDATA[ <= ]]> #{searchParamsVO.periodEndTime}
        </if>
        <if test='searchParamsVO.groupLevel =="LV1" and searchParamsVO.lv1DimensionSet != null and searchParamsVO.lv1DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv1DimensionSet' item="code" open="AND t.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupLevel =="LV2" and searchParamsVO.lv2DimensionSet != null and searchParamsVO.lv2DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv2DimensionSet' item="code" open="AND t.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
            and t.oversea_flag = #{searchParamsVO.overseaFlag}
        </if>
        AND t.view_flag = #{searchParamsVO.viewFlag}
        <if test='searchParamsVO.groupLevel != null and searchParamsVO.groupLevel != ""'>
            AND t.group_level = #{searchParamsVO.groupLevel}
        </if>
        ORDER BY t.period_id
    </select>

</mapper>
