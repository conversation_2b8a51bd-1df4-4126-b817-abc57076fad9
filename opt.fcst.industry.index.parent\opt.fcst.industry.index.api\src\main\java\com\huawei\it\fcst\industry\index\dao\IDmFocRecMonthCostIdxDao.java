/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.month.DmFocMonthCostIdxVO;
import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * The DAO to access DmFocMonthCostIdxT entity
 *
 * <AUTHOR>
 * @Auto-generated by Jalor Studio
 * @since 2023-03-10 11:14:15
 */
public interface IDmFocRecMonthCostIdxDao {

    /**
     * [查询反向视角价格指数（多维度）图数据]
     *     多维度：多专家团、多品类、多Item
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<DmFocMonthCostIdxVO> findRevPriceIndexChartByMultiDim(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);

    /**
     * 反向视角，需要对数据查询
     *
     * @param monthAnalysisVO 查询参数VO
     * @return list
     */
    List<DmFocMonthCostIdxVO> findRevPriceIndexVOList(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);

    int findRecPriceIndexByBasePeriodId(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);

    List<DmFocMonthCostIdxVO> findRevPriceIndexExpData(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);

    List<DmFocMonthCostIdxVO> findRevAmpPurchasePriceIndexChart(@Param("searchParamsVO") MonthAnalysisVO monthAnalysisVO);
}
