<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.pbi.dao.IctMonthCostAmtDao">
    <resultMap type="com.huawei.it.fcst.industry.pbi.vo.month.IctMonthAnalysisVO" id="resultMap">
        <result property="basePeriodId" column="base_period_id"/>
        <result property="versionId" column="version_id"/>
        <result property="customId" column="custom_id"/>
        <result property="customCnName" column="custom_cn_name"/>
        <result property="granularityType" column="granularity_type"/>
        <result property="periodYear" column="period_year"/>
        <result property="periodId" column="period_id"/>
        <result property="prodRndTeamCode" column="prod_rnd_team_code"/>
        <result property="prodRdTeamCnName" column="prod_rd_team_cn_name"/>
        <result property="prodListCode" column="prod_list_code"/>
        <result property="prodListCnName" column="prod_list_cn_name"/>
        <result property="industryCatgCode" column="industry_catg_code"/>
        <result property="industryCatgCnName" column="industry_catg_cn_name"/>
        <result property="dimensionCode" column="dimension_code"/>
        <result property="dimensionCnName" column="dimension_cn_name"/>
        <result property="dimensionSubcategoryCode" column="dimension_subcategory_code"/>
        <result property="dimensionSubcategoryCnName" column="dimension_subcategory_cn_name"/>
        <result property="dimensionSubDetailCode" column="dimension_sub_detail_code"/>
        <result property="dimensionSubDetailCnName" column="dimension_sub_detail_cn_name"/>
        <result property="groupCode" column="group_code"/>
        <result property="groupCnName" column="group_cn_name"/>
        <result property="groupLevel" column="group_level"/>
        <result property="rmbCostAmt" column="rmb_cost_amt"/>
        <result property="actualQty" column="actual_qty"/>
        <result property="parentCode" column="parent_code"/>
        <result property="parentCnName" column="parent_cn_name"/>
        <result property="mainFlag" column="main_flag"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="appendFlag" column="append_flag"/>
        <result property="regionCode" column="region_code"/>
        <result property="regionCnName" column="region_cn_name"/>
        <result property="repofficeCode" column="repoffice_code"/>
        <result property="repofficeCnName" column="repoffice_cn_name"/>
        <result property="bgCode" column="bg_code"/>
        <result property="bgCnName" column="bg_cn_name"/>
        <result property="overseaFlag" column="oversea_flag"/>
        <result property="codeAttributes" column="code_attributes"/>
    </resultMap>

    <sql id="costDistributionFields">
        DISTINCT
        period_id,
        group_code,
        group_cn_name,
        group_level,
        ROUND(rmb_cost_amt / 10000, 2) AS rmb_cost_amt
    </sql>

    <select id="findCostAmtVOList" resultMap="resultMap">
        SELECT
        <include refid="costDistributionFields"/>
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.tablePreFix}_mon_cost_amt_t
        WHERE del_flag = 'N'
        <include refid="searchWhere"/>
        ORDER BY period_id
    </select>
    
    <sql id="searchWhere">
        <if test='monthAnalysisVO.periodStartTime != null'>
            AND period_id <![CDATA[ >= ]]> #{monthAnalysisVO.periodStartTime}
        </if>
        <if test='monthAnalysisVO.periodEndTime != null'>
            AND period_id <![CDATA[ <= ]]> #{monthAnalysisVO.periodEndTime}
        </if>
        <if test='monthAnalysisVO.versionId != null'>
            AND version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='monthAnalysisVO.viewFlag != null and monthAnalysisVO.viewFlag != ""'>
            AND view_flag = #{monthAnalysisVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            AND oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
            AND bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
            AND region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
            AND repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.mainFlag != null and monthAnalysisVO.mainFlag != ""'>
            AND main_flag = #{monthAnalysisVO.mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.codeAttributes != null and monthAnalysisVO.codeAttributes != ""'>
            AND code_attributes = #{monthAnalysisVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupLevel != null and monthAnalysisVO.groupLevel != ""'>
            and group_level = #{monthAnalysisVO.groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupCodeList != null and monthAnalysisVO.groupCodeList != ""'>
            <foreach collection='monthAnalysisVO.groupCodeList' item="code" open="AND group_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <choose>
            <when test='monthAnalysisVO.costType=="PSP"'>
                <if test='monthAnalysisVO.granularityType=="IRB" and monthAnalysisVO.prodRndTeamCodeList != null and monthAnalysisVO.prodRndTeamCodeList.size() > 0'>
                    <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND prod_rnd_team_code IN ("
                             close=")" index="index" separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='monthAnalysisVO.granularityType=="PROD" and monthAnalysisVO.prodRndTeamCodeList != null and monthAnalysisVO.prodRndTeamCodeList.size() > 0'>
                    <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND prod_list_code IN ("
                             close=")" index="index" separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='monthAnalysisVO.granularityType=="INDUS" and monthAnalysisVO.prodRndTeamCodeList != null and monthAnalysisVO.prodRndTeamCodeList.size() > 0'>
                    <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND industry_catg_code IN ("
                             close=")" index="index" separator=",">
                        #{code}
                    </foreach>
                </if>
            </when>
            <when test='monthAnalysisVO.costType=="STANDARD"'>
                <if test='monthAnalysisVO.granularityType=="IRB" and monthAnalysisVO.prodRndTeamCodeList != null and monthAnalysisVO.prodRndTeamCodeList.size() > 0'>
                    <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND prod_rnd_team_code IN ("
                             close=")" index="index" separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='monthAnalysisVO.granularityType=="INDUS" and monthAnalysisVO.prodRndTeamCodeList != null and monthAnalysisVO.prodRndTeamCodeList.size() > 0'>
                    <foreach collection='monthAnalysisVO.prodRndTeamCodeList' item="code" open="AND industry_catg_code IN ("
                             close=")" index="index" separator=",">
                        #{code}
                    </foreach>
                </if>
            </when>
        </choose>
        <if test='monthAnalysisVO.dimensionCode != null and monthAnalysisVO.dimensionCode != ""'>
            AND dimension_code = #{monthAnalysisVO.dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.dimensionSubcategoryCode != null and monthAnalysisVO.dimensionSubcategoryCode != ""'>
            AND dimension_subcategory_code = #{monthAnalysisVO.dimensionSubcategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.dimensionSubDetailCode != null and monthAnalysisVO.dimensionSubDetailCode != ""'>
            AND dimension_sub_detail_code = #{monthAnalysisVO.dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
    </sql>

    <select id="findBlurCostAmtVOList" resultMap="resultMap">
        SELECT
            period_id,
            group_code,
            group_cn_name,
            group_level,
            ROUND(SUM(rmb_cost_amt) / 10000, 2) AS rmb_cost_amt
        FROM fin_dm_opt_foi.dm_fcst_ict_${monthAnalysisVO.blurTablePreFix}_base_cus_mon_cost_amt_t
        WHERE del_flag = 'N'
        <include refid="blurSearchWhere"/>
        GROUP BY period_id, group_code, group_cn_name, group_level
        ORDER BY period_id
    </select>

    <sql id="blurSearchWhere">
        <if test='monthAnalysisVO.periodStartTime != null'>
            AND period_id <![CDATA[ >= ]]> #{monthAnalysisVO.periodStartTime}
        </if>
        <if test='monthAnalysisVO.periodEndTime != null'>
            AND period_id <![CDATA[ <= ]]> #{monthAnalysisVO.periodEndTime}
        </if>
        <if test='monthAnalysisVO.versionId != null'>
            AND version_id = #{monthAnalysisVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='monthAnalysisVO.customId != null'>
            AND custom_id = #{monthAnalysisVO.customId,jdbcType=NUMERIC}
        </if>
        <if test='monthAnalysisVO.customCnName != null and monthAnalysisVO.customCnName != ""'>
            AND custom_cn_name = #{monthAnalysisVO.customCnName,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.granularityType != null and monthAnalysisVO.granularityType != ""'>
            AND granularity_type = #{monthAnalysisVO.granularityType,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.parentLevel != null and monthAnalysisVO.parentLevel != ""'>
            AND parent_level = #{monthAnalysisVO.parentLevel,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.viewFlag != null and monthAnalysisVO.viewFlag != ""'>
            AND view_flag = #{monthAnalysisVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            AND oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.bgCode != null and monthAnalysisVO.bgCode != ""'>
            AND bg_code = #{monthAnalysisVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.regionCode != null and monthAnalysisVO.regionCode != ""'>
            AND region_code = #{monthAnalysisVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.repofficeCode != null and monthAnalysisVO.repofficeCode != ""'>
            AND repoffice_code = #{monthAnalysisVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.mainFlag != null and monthAnalysisVO.mainFlag != ""'>
            AND main_flag = #{monthAnalysisVO.mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.codeAttributes != null and monthAnalysisVO.codeAttributes != ""'>
            AND code_attributes = #{monthAnalysisVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupLevel != null and monthAnalysisVO.groupLevel != ""'>
            and group_level = #{monthAnalysisVO.groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupCodeList != null and monthAnalysisVO.groupCodeList != ""'>
            <foreach collection='monthAnalysisVO.groupCodeList' item="code" open="AND group_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
    </sql>

</mapper>
