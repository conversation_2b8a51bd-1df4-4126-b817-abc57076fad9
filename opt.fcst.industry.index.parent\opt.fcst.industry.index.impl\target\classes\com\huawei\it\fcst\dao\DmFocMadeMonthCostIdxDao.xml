<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IDmFocMadeMonthCostIdxDao">
    <resultMap type="com.huawei.it.fcst.industry.index.vo.month.DmFocMonthCostIdxVO" id="resultMap">
        <result property="id" column="id"/>
        <result property="versionId" column="version_id"/>
        <result property="periodYear" column="period_year"/>
        <result property="periodId" column="period_id"/>
        <result property="basePeriodId" column="base_period_id"/>
        <result property="prodRndTeamCode" column="prod_rnd_team_code"/>
        <result property="prodRndTeamCnName" column="prod_rnd_team_cn_name"/>
        <result property="lv0ProdRdTeamCnName" column="lv0_prod_rd_team_cn_name"/>
        <result property="lv0ProdRndTeamCode" column="lv0_prod_rnd_team_code"/>
        <result property="lv1ProdRdTeamCnName" column="lv1_prod_rd_team_cn_name"/>
        <result property="lv1ProdRndTeamCode" column="lv1_prod_rnd_team_code"/>
        <result property="lv2ProdRdTeamCnName" column="lv2_prod_rd_team_cn_name"/>
        <result property="lv2ProdRndTeamCode" column="lv2_prod_rnd_team_code"/>
        <result property="lv3ProdRdTeamCnName" column="lv3_prod_rd_team_cn_name"/>
        <result property="lv3ProdRndTeamCode" column="lv3_prod_rnd_team_code"/>
        <result property="lv4ProdRdTeamCnName" column="lv4_prod_rd_team_cn_name"/>
        <result property="lv4ProdRndTeamCode" column="lv4_prod_rnd_team_code"/>
        <result property="groupCode" column="group_code"/>
        <result property="groupCnName" column="group_cn_name"/>
        <result property="groupLevel" column="group_level"/>
        <result property="costIndex" column="cost_index"/>
        <result property="parentCode" column="parent_code"/>
        <result property="parentCnName" column="parent_cn_name"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="appendFlag" column="append_flag"/>
        <result property="weightRate" column="weight_rate"/>
        <result property="weightPercent" column="weight_percent"/>
        <result property="scenarioFlag" column="scenario_flag"/>
        <result property="shippingObjectCnName" column="shipping_object_cn_name"/>
        <result property="shippingObjectCode" column="shipping_object_code"/>
        <result property="manufactureObjectCnName" column="manufacture_object_cn_name"/>
        <result property="manufactureObjectCode" column="manufacture_object_code"/>
        <result property="l1Name" column="l1_name"/>
        <result property="l2Name" column="l2_name"/>
        <result property="dmsCode" column="dms_code"/>
        <result property="dmsCnName" column="dms_cn_name"/>
        <result property="coaCode" column="coa_code"/>
        <result property="coaCnName" column="coa_cn_name"/>
        <result property="dimensionCode" column="dimension_code"/>
        <result property="dimensionCnName" column="dimension_cn_name"/>
        <result property="dimensionSubCategoryCode" column="dimension_subcategory_code"/>
        <result property="dimensionSubCategoryCnName" column="dimension_subcategory_cn_name"/>
        <result property="dimensionSubDetailCode" column="dimension_sub_detail_code"/>
        <result property="dimensionSubDetailCnName" column="dimension_sub_detail_cn_name"/>
        <result property="spartCode" column="spart_code"/>
        <result property="spartCnName" column="spart_cn_name"/>
        <result property="customId" column="custom_id"/>
        <result property="customCnName" column="custom_cn_name"/>
        <result property="costType" column="cost_type"/>
        <result property="isComb" column="isComb"/>
    </resultMap>


    <sql id="allField">
        distinct
        <if test = 'searchParamsVO.granularityType == "P"'>
            l1_name,l2_name,
        </if>
        <if test = 'searchParamsVO.granularityType == "D"'>
            dms_code,dms_cn_name,
            <if test='searchParamsVO.industryOrg == "ENERGY"'>
                coa_code, coa_cn_name,
            </if>
            dimension_code,dimension_cn_name,
            dimension_subcategory_code,
            dimension_subcategory_cn_name,
            dimension_sub_detail_code,dimension_sub_detail_cn_name,
            spart_code,spart_cn_name,
        </if>
        prod_rnd_team_cn_name,prod_rnd_team_code,
        shipping_object_code,
        shipping_object_cn_name,
        manufacture_object_code,
        manufacture_object_cn_name,
        group_cn_name,
        group_level,
        group_code,
        period_year,
        period_id,
        ROUND(cost_index, 2) AS cost_index,
        version_id,
        'M' AS cost_type,
        last_update_date
    </sql>

    <sql id="compareAllField">
        distinct info.lv0_prod_rd_team_cn_name,info.lv0_prod_rnd_team_code,
        <choose>
            <when test='searchParamsVO.teamLevel == "LV1"'>
                info.lv1_prod_rd_team_cn_name,info.lv1_prod_rnd_team_code,
            </when>
            <when test='searchParamsVO.teamLevel == "LV2"'>
                info.lv1_prod_rd_team_cn_name,info.lv1_prod_rnd_team_code,info.lv2_prod_rd_team_cn_name,info.lv2_prod_rnd_team_code,
            </when>
            <when test='searchParamsVO.teamLevel == "LV3"'>
                info.lv1_prod_rd_team_cn_name,info.lv1_prod_rnd_team_code,
                info.lv2_prod_rd_team_cn_name,info.lv2_prod_rnd_team_code,
                info.lv3_prod_rd_team_cn_name,info.lv3_prod_rnd_team_code,
            </when>
            <when test='searchParamsVO.industryOrg == "IAS" and searchParamsVO.teamLevel == "LV4"'>
                info.lv1_prod_rd_team_cn_name,info.lv1_prod_rnd_team_code,
                info.lv2_prod_rd_team_cn_name,info.lv2_prod_rnd_team_code,
                info.lv3_prod_rd_team_cn_name,info.lv3_prod_rnd_team_code,
                info.lv4_prod_rd_team_cn_name,info.lv4_prod_rnd_team_code,
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test = 'searchParamsVO.granularityType == "P"'>
            t.l1_name, t.l2_name,
        </if>
        <if test = 'searchParamsVO.granularityType == "D"'>
            t.dms_code,t.dms_cn_name,
            <if test='searchParamsVO.industryOrg == "ENERGY"'>
                t.coa_code, t.coa_cn_name,
            </if>
            t.dimension_code,t.dimension_cn_name,t.dimension_subcategory_code,
            t.dimension_subcategory_cn_name,t.dimension_sub_detail_code,
            t.dimension_sub_detail_cn_name,t.spart_code,t.spart_cn_name,
        </if>
        t.prod_rnd_team_cn_name,t.prod_rnd_team_code,
        t.shipping_object_code,t.shipping_object_cn_name,
        t.manufacture_object_code,t.manufacture_object_cn_name,
        t.group_level,
        t.group_code,
        t.group_cn_name,
        t.period_year,
        t.period_id,
        ROUND(t.cost_index, 2) AS cost_index,
        t.version_id,
        'M' AS cost_type,
        t.last_update_date
    </sql>

    <sql id="searchFiled">
        <choose>
                <when test ='searchParamsVO.granularityType == "U"'>
                    left join fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_top_item_info_t info
                </when>
                <when test ='searchParamsVO.granularityType == "P"'>
                    left join fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_pft_top_item_info_t info
                </when>
                <when test ='searchParamsVO.granularityType == "D"'>
                    left join fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_dms_top_item_info_t info
                </when>
            </choose>
            <choose>
                <when test ='searchParamsVO.teamLevel == "LV0"'>
                    on t.prod_rnd_team_code = info.lv0_prod_rnd_team_code
                </when>
                <when test ='searchParamsVO.teamLevel == "LV1"'>
                    on t.prod_rnd_team_code = info.lv1_prod_rnd_team_code
                </when>
                <when test ='searchParamsVO.teamLevel == "LV2"'>
                    on t.prod_rnd_team_code = info.lv2_prod_rnd_team_code
                </when>
                <when test ='searchParamsVO.teamLevel == "LV3"'>
                    on t.prod_rnd_team_code = info.lv3_prod_rnd_team_code
                </when>
                <when test='searchParamsVO.industryOrg == "IAS" and searchParamsVO.teamLevel == "LV4"'>
                    on t.prod_rnd_team_code = info.lv4_prod_rnd_team_code
                </when>
            </choose>
            and info.del_flag = 'N'
            and info.is_top_flag ='Y'
            and info.double_flag ='Y'
            and t.view_flag = info.view_flag
            and t.caliber_flag = info.caliber_flag
            and t.oversea_flag = info.oversea_flag
            and t.lv0_prod_list_code = info.lv0_prod_list_code
            and t.version_id = info.version_id
            <choose>
                <when test='searchParamsVO.groupLevel == "LV0"'>
                    and t.group_code = info.lv0_prod_rnd_team_code
                </when>
                <when test='searchParamsVO.groupLevel == "LV1"'>
                    and t.group_code = info.lv1_prod_rnd_team_code
                </when>
                <when test='searchParamsVO.groupLevel == "LV2"'>
                    and t.group_code = info.lv2_prod_rnd_team_code
                </when>
                <when test='searchParamsVO.groupLevel == "LV3"'>
                    and t.group_code = info.lv3_prod_rnd_team_code
                </when>
                <when test='searchParamsVO.industryOrg == "IAS" and searchParamsVO.groupLevel == "LV4"'>
                    and t.group_code = info.lv4_prod_rnd_team_code
                </when>
                <when test='searchParamsVO.groupLevel == "L1"'>
                    and t.l1_name = info.l1_name
                    and t.group_code = info.l1_name
                </when>
                <when test='searchParamsVO.groupLevel == "L2"'>
                    and t.l1_name = info.l1_name
                    and t.l2_name = info.l2_name
                    and t.group_code = info.l2_name
                </when>
                <when test='searchParamsVO.groupLevel == "SHIPPING_OBJECT" and searchParamsVO.granularityType == "U"'>
                    and t.shipping_object_code = info.top_shipping_object_code
                    and t.group_code = info.top_shipping_object_code
                </when>
                <when test='searchParamsVO.groupLevel == "MANUFACTURE_OBJECT" and searchParamsVO.granularityType == "U"'>
                    and t.shipping_object_code = info.top_shipping_object_code
                    and t.manufacture_object_code = info.top_manufacture_object_code
                    and t.group_code = info.top_manufacture_object_code
                </when>
                <otherwise>
                </otherwise>
            </choose>
        <if test='searchParamsVO.granularityType == "P"'>
        <choose>
            <when test='searchParamsVO.viewFlag == "0" or searchParamsVO.viewFlag == "1" or searchParamsVO.viewFlag == "2"'>
                <if test='searchParamsVO.groupLevel == "SHIPPING_OBJECT"'>
                    and t.shipping_object_code = info.top_shipping_object_code
                    and t.group_code = info.top_shipping_object_code
                </if>
                <if test='searchParamsVO.groupLevel == "MANUFACTURE_OBJECT"'>
                    and t.shipping_object_code = info.top_shipping_object_code
                    and t.manufacture_object_code = info.top_manufacture_object_code
                    and t.group_code = info.top_manufacture_object_code
                </if>
            </when>
           <when test='searchParamsVO.groupLevel == "SHIPPING_OBJECT" and searchParamsVO.viewFlag == "3"'>
               and t.l1_name = info.l1_name
               and t.shipping_object_code = info.top_shipping_object_code
               and t.group_code = info.top_shipping_object_code
           </when>
           <when test='searchParamsVO.groupLevel == "MANUFACTURE_OBJECT" and searchParamsVO.viewFlag == "3"'>
               and t.l1_name = info.l1_name
               and t.shipping_object_code = info.top_shipping_object_code
               and t.manufacture_object_code = info.top_manufacture_object_code
               and t.group_code = info.top_manufacture_object_code
           </when>
           <when test='searchParamsVO.groupLevel == "SHIPPING_OBJECT" and searchParamsVO.viewFlag == "4"'>
               and t.l1_name = info.l1_name
               and t.l2_name = info.l2_name
               and t.shipping_object_code = info.top_shipping_object_code
               and t.group_code = info.top_shipping_object_code
           </when>
           <when test='searchParamsVO.groupLevel == "MANUFACTURE_OBJECT" and searchParamsVO.viewFlag == "4"'>
               and t.l1_name = info.l1_name
               and t.l2_name = info.l2_name
               and t.shipping_object_code = info.top_shipping_object_code
               and t.manufacture_object_code = info.top_manufacture_object_code
               and t.group_code = info.top_manufacture_object_code
           </when>
        </choose>
        </if>
        <if test='searchParamsVO.granularityType == "D"'>
            <if test='searchParamsVO.industryOrg == "ENERGY" and searchParamsVO.viewFlag == "12"
                    and searchParamsVO.groupLevel != "LV0" and searchParamsVO.groupLevel != "LV1"
                    and searchParamsVO.groupLevel != "LV2" and searchParamsVO.groupLevel != "LV3"'>
                and t.coa_code = info.coa_code
            </if>
            <choose>
                <when test='searchParamsVO.groupLevel == "DIMENSION"'>
                    and t.dimension_code = info.dimension_code
                    and t.group_code = info.dimension_code
                </when>
                <when test='searchParamsVO.groupLevel == "SUBCATEGORY"'>
                    and t.dimension_code = info.dimension_code
                    and t.dimension_subcategory_code = info.dimension_subcategory_code
                    and t.group_code = info.dimension_subcategory_code
                </when>
                <when test='searchParamsVO.groupLevel == "SUB_DETAIL"'>
                    and t.dimension_code = info.dimension_code
                    and t.dimension_subcategory_code = info.dimension_subcategory_code
                    and t.dimension_sub_detail_code = info.dimension_sub_detail_code
                    and t.group_code = info.dimension_sub_detail_code
                </when>
                <when test='searchParamsVO.groupLevel == "SPART"'>
                    and t.dimension_code = info.dimension_code
                    and t.dimension_subcategory_code = info.dimension_subcategory_code
                    and t.dimension_sub_detail_code = info.dimension_sub_detail_code
                    and t.spart_code = info.spart_code
                    and t.group_code = info.spart_code
                </when>
            <when test='searchParamsVO.viewFlag == "0" or searchParamsVO.viewFlag == "3" or searchParamsVO.viewFlag == "6"'>
                <if test='searchParamsVO.groupLevel == "SHIPPING_OBJECT"'>
                    and t.dimension_code = info.dimension_code
                    and t.shipping_object_code = info.top_shipping_object_code
                    and t.group_code = info.top_shipping_object_code
                </if>
                <if test='searchParamsVO.groupLevel == "MANUFACTURE_OBJECT"'>
                    and t.dimension_code = info.dimension_code
                    and t.shipping_object_code = info.top_shipping_object_code
                    and t.manufacture_object_code = info.top_manufacture_object_code
                    and t.group_code = info.top_manufacture_object_code
                </if>
            </when>
            <when test='searchParamsVO.viewFlag == "1" or searchParamsVO.viewFlag == "4" or searchParamsVO.viewFlag == "7"'>
                <if test='searchParamsVO.groupLevel == "SHIPPING_OBJECT"'>
                    and t.dimension_code = info.dimension_code
                    and t.dimension_subcategory_code = info.dimension_subcategory_code
                    and t.shipping_object_code = info.top_shipping_object_code
                    and t.group_code = info.top_shipping_object_code
                </if>
                <if test='searchParamsVO.groupLevel == "MANUFACTURE_OBJECT"'>
                    and t.dimension_code = info.dimension_code
                    and t.dimension_subcategory_code = info.dimension_subcategory_code
                    and t.shipping_object_code = info.top_shipping_object_code
                    and t.manufacture_object_code = info.top_manufacture_object_code
                    and t.group_code = info.top_manufacture_object_code
                </if>
            </when>
            <when test='searchParamsVO.viewFlag == "2" or searchParamsVO.viewFlag == "5" or searchParamsVO.viewFlag == "8"'>
                <if test='searchParamsVO.groupLevel == "SHIPPING_OBJECT"'>
                    and t.dimension_code = info.dimension_code
                    and t.dimension_subcategory_code = info.dimension_subcategory_code
                    and t.dimension_sub_detail_code = info.dimension_sub_detail_code
                    and t.shipping_object_code = info.top_shipping_object_code
                    and t.group_code = info.top_shipping_object_code
                </if>
                <if test='searchParamsVO.groupLevel == "MANUFACTURE_OBJECT"'>
                    and t.dimension_code = info.dimension_code
                    and t.dimension_subcategory_code = info.dimension_subcategory_code
                    and t.dimension_sub_detail_code = info.dimension_sub_detail_code
                    and t.shipping_object_code = info.top_shipping_object_code
                    and t.manufacture_object_code = info.top_manufacture_object_code
                    and t.group_code = info.top_manufacture_object_code
                </if>
            </when>
            <when test='searchParamsVO.viewFlag == "9" or searchParamsVO.viewFlag == "10" or searchParamsVO.viewFlag == "11" or searchParamsVO.viewFlag == "12"'>
                <if test='searchParamsVO.groupLevel == "SHIPPING_OBJECT"'>
                    and t.dimension_code = info.dimension_code
                    and t.dimension_subcategory_code = info.dimension_subcategory_code
                    and t.dimension_sub_detail_code = info.dimension_sub_detail_code
                    and t.spart_code = info.spart_code
                    and t.shipping_object_code = info.top_shipping_object_code
                    and t.group_code = info.top_shipping_object_code
                </if>
                <if test='searchParamsVO.groupLevel == "MANUFACTURE_OBJECT"'>
                    and t.dimension_code = info.dimension_code
                    and t.dimension_subcategory_code = info.dimension_subcategory_code
                    and t.dimension_sub_detail_code = info.dimension_sub_detail_code
                    and t.spart_code = info.spart_code
                    and t.shipping_object_code = info.top_shipping_object_code
                    and t.manufacture_object_code = info.top_manufacture_object_code
                    and t.group_code = info.top_manufacture_object_code
                </if>
            </when>
            </choose>
        </if>
    </sql>

    <sql id="multiAllField">
        <if test='searchParamsVO.granularityType == "U"'>
            SELECT DISTINCT T1.prod_rnd_team_cn_name,T1.prod_rnd_team_code, T1.parent_code,
            T1.parent_cn_name,T1.period_id,T1.group_level, T1.group_code,T1.group_cn_name,
            ROUND(T1.cost_index, 2) AS cost_index,
            T1.append_flag,
            T2.weight_rate,
            'M' AS cost_type,
            ROUND(T2.weight_rate * 100, 2) || '%' AS weight_percent
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_month_cost_idx_t T1
            LEFT JOIN fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_month_weight_t T2
        </if>
        <if test='searchParamsVO.granularityType == "P"'>
            SELECT DISTINCT T1.prod_rnd_team_cn_name,T1.prod_rnd_team_code,T1.l1_name,T1.l2_name,T1.parent_code,
            T1.parent_cn_name,T1.period_id, T1.group_level, T1.group_code,T1.group_cn_name,
            ROUND(T1.cost_index, 2) AS cost_index,
            T1.append_flag,
            T2.weight_rate,
            'M' AS cost_type,
            ROUND(T2.weight_rate * 100, 2) || '%' AS weight_percent
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_pft_month_cost_idx_t T1
            LEFT JOIN fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_pft_month_weight_t T2
        </if>
        <if test='searchParamsVO.granularityType == "D"'>
            SELECT DISTINCT T1.prod_rnd_team_cn_name,T1.prod_rnd_team_code,t1.dms_code,t1.dms_cn_name,
            <if test='searchParamsVO.industryOrg == "ENERGY"'>
                t1.coa_code, t1.coa_cn_name,
            </if>
            t1.dimension_code,t1.dimension_cn_name,
            t1.dimension_subcategory_code,t1.dimension_subcategory_cn_name,
            t1.dimension_sub_detail_code,t1.dimension_sub_detail_cn_name,
            t1.spart_code,t1.spart_cn_name,
            T1.parent_code,T1.parent_cn_name,
            T1.period_id,T1.group_level, T1.group_code,T1.group_cn_name,
            ROUND(T1.cost_index, 2) AS cost_index,
            T1.append_flag,
            T2.weight_rate,
            'M' AS cost_type,
            ROUND(T2.weight_rate * 100, 2) || '%' AS weight_percent
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_dms_month_cost_idx_t T1
            LEFT JOIN fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_dms_month_weight_t T2
        </if>
    </sql>

    <sql id="itemMultiFiled">
        <if test='searchParamsVO.granularityType == "U"'>
            SELECT DISTINCT T1.prod_rnd_team_cn_name,T1.prod_rnd_team_code,
            T1.parent_code,T1.parent_cn_name,
            T1.period_id, T1.group_level, T1.group_code,T1.group_cn_name,
            'M' AS cost_type,
            ROUND(T1.cost_index, 2) AS cost_index,
            T1.append_flag
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_month_cost_idx_t T1
        </if>
        <if test='searchParamsVO.granularityType == "P"'>
            SELECT DISTINCT T1.prod_rnd_team_cn_name,T1.prod_rnd_team_code,T1.l1_name,T1.l2_name,
            T1.parent_code,T1.parent_cn_name,
            T1.period_id, T1.group_level, T1.group_code,T1.group_cn_name,
            'M' AS cost_type,
            ROUND(T1.cost_index, 2) AS cost_index,
            T1.append_flag
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_pft_month_cost_idx_t T1
        </if>
        <if test='searchParamsVO.granularityType == "D"'>
            SELECT DISTINCT T1.prod_rnd_team_cn_name,T1.prod_rnd_team_code,t1.dms_code,t1.dms_cn_name,
            <if test='searchParamsVO.industryOrg == "ENERGY"'>
                t1.coa_code, t1.coa_cn_name,
            </if>
            t1.dimension_code,t1.dimension_cn_name,
            t1.dimension_subcategory_code,t1.dimension_subcategory_cn_name,
            t1.dimension_sub_detail_code,t1.dimension_sub_detail_cn_name,
            t1.spart_code,t1.spart_cn_name,
            T1.parent_code,T1.parent_cn_name,
            T1.period_id, T1.group_level,T1.group_code,T1.group_cn_name,
            'M' AS cost_type,
            ROUND(T1.cost_index, 2) AS cost_index,
            T1.append_flag
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_dms_month_cost_idx_t T1
        </if>
    </sql>

    <select id="findMadePriceIdxByBasePeriodId" resultType="int">
        SELECT COUNT(1)
        <if test='searchParamsVO.granularityType == "U"'>
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_month_cost_idx_t
        </if>
        <if test='searchParamsVO.granularityType == "P"'>
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_pft_month_cost_idx_t
        </if>
        <if test='searchParamsVO.granularityType == "D"'>
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_dms_month_cost_idx_t
        </if>
        WHERE del_flag = 'N'
        <if test='searchParamsVO.basePeriodId != null'>
            AND base_period_id = #{searchParamsVO.basePeriodId}
        </if>
        <if test='searchParamsVO.shippingObjectCode != null and searchParamsVO.shippingObjectCode != ""'>
            AND shipping_object_code = #{searchParamsVO.shippingObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.manufactureObjectCode != null and searchParamsVO.manufactureObjectCode != ""'>
            AND manufacture_object_code = #{searchParamsVO.manufactureObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.periodstartTime != null and searchParamsVO.periodstartTime != ""'>
            AND period_id <![CDATA[ >= ]]> #{searchParamsVO.periodStartTime}
        </if>
        <if test='searchParamsVO.periodEndTime != null and searchParamsVO.periodEndTime != ""'>
            AND period_id <![CDATA[ <= ]]> #{searchParamsVO.periodEndTime}
        </if>
        <if test='searchParamsVO.prodRndTeamCodeList != null and searchParamsVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='searchParamsVO.prodRndTeamCodeList' item="code" open="AND prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
            AND lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
            AND caliber_Flag = #{searchParamsVO.caliberFlag}
        </if>
        <if test='searchParamsVO.versionId != null and searchParamsVO.versionId != ""'>
            AND version_id = #{searchParamsVO.versionId}
        </if>
        <if test='searchParamsVO.groupCodeList != null and searchParamsVO.groupCodeList.size() > 0'>
            <foreach collection='searchParamsVO.groupCodeList' item="code" open="AND group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
            <if test='searchParamsVO.parentLevel != null and searchParamsVO.parentLevel != ""'>
                AND group_level = #{searchParamsVO.parentLevel,jdbcType=VARCHAR}
            </if>
        </if>
        <if test='searchParamsVO.subGroupCodeList != null and searchParamsVO.subGroupCodeList.size() > 0'>
            <foreach collection='searchParamsVO.subGroupCodeList' item="code" open="AND group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
            <if test='searchParamsVO.groupLevel != null and searchParamsVO.groupLevel != ""'>
                AND group_level = #{searchParamsVO.groupLevel,jdbcType=VARCHAR}
            </if>
        </if>
        <if test='searchParamsVO.l1NameList != null and searchParamsVO.l1NameList.size() > 0'>
            <foreach collection='searchParamsVO.l1NameList' item="code" open="AND l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l2NameList != null and searchParamsVO.l2NameList.size() > 0'>
            <foreach collection='searchParamsVO.l2NameList' item="code" open="AND l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionCodeList != null and searchParamsVO.dimensionCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionCodeList' item="code" open="AND dimension_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubcategoryCodeList != null and searchParamsVO.dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubcategoryCodeList' item="code" open="AND dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubDetailCodeList != null and searchParamsVO.dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubDetailCodeList' item="code" open="AND dimension_sub_detail_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.spartCodeList != null and searchParamsVO.spartCodeList.size() > 0'>
            <foreach collection='searchParamsVO.spartCodeList' item="code" open="AND spart_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.parentCodeList != null and searchParamsVO.parentCodeList.size() > 0'>
            <foreach collection='searchParamsVO.parentCodeList' item="code" open="AND parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
            AND oversea_flag = #{searchParamsVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.viewFlag != null'>
            AND view_flag = #{searchParamsVO.viewFlag}
        </if>
    </select>

    <select id="findMadeCombPriceIdxByBasePeriodId" resultType="int">
        SELECT COUNT(1)
        FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_custom_month_cost_index_t
        WHERE del_flag = 'N'
        <if test='searchParamsVO.granularityType != null'>
            AND granularity_type = #{searchParamsVO.granularityType}
        </if>
        <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
            AND caliber_Flag = #{searchParamsVO.caliberFlag}
        </if>
        <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
            and lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.periodstartTime != null and searchParamsVO.periodstartTime != ""'>
            AND period_id <![CDATA[ >= ]]> #{searchParamsVO.periodStartTime}
        </if>
        <if test='searchParamsVO.periodEndTime != null and searchParamsVO.periodEndTime != ""'>
            AND period_id <![CDATA[ <= ]]> #{searchParamsVO.periodEndTime}
        </if>
        <if test='searchParamsVO.customIdList != null and searchParamsVO.customIdList.size() > 0'>
            <foreach collection='searchParamsVO.customIdList' item="code" open="AND custom_id IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.combParentCodeList != null and searchParamsVO.combParentCodeList.size() > 0'>
            <foreach collection='searchParamsVO.combParentCodeList' item="code" open="AND custom_id || '_##' || parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.combinaSubGroupCodeList != null and searchParamsVO.combinaSubGroupCodeList.size() > 0'>
            <foreach collection='searchParamsVO.combinaSubGroupCodeList' item="code" open="AND custom_id || '_##' || group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.combinaCodeList != null and searchParamsVO.combinaCodeList.size() > 0'>
            <foreach collection='searchParamsVO.combinaCodeList' item="code" open="AND custom_id || '_##' || group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.basePeriodId != null'>
            AND base_period_id = #{searchParamsVO.basePeriodId}
        </if>
        <if test='searchParamsVO.versionId != null'>
            AND version_id = #{searchParamsVO.versionId}
        </if>
        <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
            and oversea_flag = #{searchParamsVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.viewFlag != null'>
            AND view_flag = #{searchParamsVO.viewFlag}
        </if>
    </select>

    <select id="findMadePriceIndexVOList" resultMap="resultMap">
        SELECT
        <include refid="allField"/>
        <if test='searchParamsVO.granularityType == "U"'>
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_month_cost_idx_t
        </if>
        <if test='searchParamsVO.granularityType == "P"'>
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_pft_month_cost_idx_t
        </if>
        <if test='searchParamsVO.granularityType == "D"'>
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_dms_month_cost_idx_t
        </if>
        WHERE del_flag = 'N'
        <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
            AND caliber_Flag = #{searchParamsVO.caliberFlag}
        </if>
        <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
            AND lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.shippingObjectCode != null and searchParamsVO.shippingObjectCode != ""'>
            AND shipping_object_code = #{searchParamsVO.shippingObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.manufactureObjectCode != null and searchParamsVO.manufactureObjectCode != ""'>
            AND manufacture_object_code = #{searchParamsVO.manufactureObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.coaCodeList != null and searchParamsVO.coaCodeList.size() > 0'>
            <foreach collection='searchParamsVO.coaCodeList' item="code" open="AND coa_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionCodeList != null and searchParamsVO.dimensionCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionCodeList' item="code" open="AND dimension_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubcategoryCodeList != null and searchParamsVO.dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubcategoryCodeList' item="code" open="AND dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubDetailCodeList != null and searchParamsVO.dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubDetailCodeList' item="code" open="AND dimension_sub_detail_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.spartCodeList != null and searchParamsVO.spartCodeList.size() > 0'>
            <foreach collection='searchParamsVO.spartCodeList' item="code" open="AND spart_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l1NameList != null and searchParamsVO.l1NameList.size() > 0'>
            <foreach collection='searchParamsVO.l1NameList' item="code" open="AND l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l2NameList != null and searchParamsVO.l2NameList.size() > 0'>
            <foreach collection='searchParamsVO.l2NameList' item="code" open="AND l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.prodRndTeamCodeList != null and searchParamsVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='searchParamsVO.prodRndTeamCodeList' item="code" open="AND prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupCodeList != null and searchParamsVO.groupCodeList != ""'>
            <foreach collection='searchParamsVO.groupCodeList' item="code" open="AND group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.basePeriodId != null'>
            AND base_period_id = #{searchParamsVO.basePeriodId}
        </if>
        <if test='searchParamsVO.versionId != null'>
            AND version_id = #{searchParamsVO.versionId}
        </if>
        <if test='searchParamsVO.periodstartTime != null and searchParamsVO.periodstartTime != ""'>
            AND period_id <![CDATA[ >= ]]> #{searchParamsVO.periodStartTime}
        </if>
        <if test='searchParamsVO.periodEndTime != null and searchParamsVO.periodEndTime != ""'>
            AND period_id <![CDATA[ <= ]]> #{searchParamsVO.periodEndTime}
        </if>
        <if test='searchParamsVO.groupLevel =="LV1" and searchParamsVO.lv1DimensionSet != null and searchParamsVO.lv1DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv1DimensionSet' item="code" open="AND prod_rnd_team_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupLevel =="LV2" and searchParamsVO.lv2DimensionSet != null and searchParamsVO.lv2DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv2DimensionSet' item="code" open="AND prod_rnd_team_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
            AND oversea_flag = #{searchParamsVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.viewFlag != null'>
            AND view_flag = #{searchParamsVO.viewFlag}
        </if>
        <if test='searchParamsVO.groupLevel != null and searchParamsVO.groupLevel != ""'>
            AND group_level = #{searchParamsVO.groupLevel}
        </if>
        ORDER BY period_id
    </select>

    <select id="findMadeComparePriceIndexVOList" resultMap="resultMap">
        select
        <include refid="compareAllField"/>
        <if test='searchParamsVO.granularityType == "U"'>
            from fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_month_cost_idx_t t
            <include refid="searchFiled"/>
        </if>
        <if test='searchParamsVO.granularityType == "P"'>
            from fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_pft_month_cost_idx_t t
            <include refid="searchFiled"/>
        </if>
        <if test='searchParamsVO.granularityType == "D"'>
            from fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_dms_month_cost_idx_t t
            <include refid="searchFiled"/>
        </if>
        WHERE t.del_flag = 'N'
        <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
            AND t.caliber_Flag = #{searchParamsVO.caliberFlag}
        </if>
        <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
            and t.lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.shippingObjectCode != null and searchParamsVO.shippingObjectCode != ""'>
            and t.shipping_object_code = #{searchParamsVO.shippingObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.manufactureObjectCode != null and searchParamsVO.manufactureObjectCode != ""'>
            and t.manufacture_object_code = #{searchParamsVO.manufactureObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.coaCodeList != null and searchParamsVO.coaCodeList.size() > 0'>
            <foreach collection='searchParamsVO.coaCodeList' item="code" open="AND t.coa_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionCodeList != null and searchParamsVO.dimensionCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionCodeList' item="code" open="AND t.dimension_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubcategoryCodeList != null and searchParamsVO.dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubcategoryCodeList' item="code" open="AND t.dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubDetailCodeList != null and searchParamsVO.dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubDetailCodeList' item="code" open="AND t.dimension_sub_detail_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.spartCodeList != null and searchParamsVO.spartCodeList.size() > 0'>
            <foreach collection='searchParamsVO.spartCodeList' item="code" open="AND t.spart_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l1NameList != null and searchParamsVO.l1NameList.size() > 0'>
            <foreach collection='searchParamsVO.l1NameList' item="code" open="AND t.l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l2NameList != null and searchParamsVO.l2NameList.size() > 0'>
            <foreach collection='searchParamsVO.l2NameList' item="code" open="AND t.l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.prodRndTeamCodeList != null and searchParamsVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='searchParamsVO.prodRndTeamCodeList' item="code" open="AND t.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupCodeList != null and searchParamsVO.groupCodeList != ""'>
            <foreach collection='searchParamsVO.groupCodeList' item="code" open="AND t.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.basePeriodId != null'>
            AND t.base_period_id = #{searchParamsVO.basePeriodId}
        </if>
        <if test='searchParamsVO.versionId != null'>
            AND t.version_id = #{searchParamsVO.versionId}
        </if>
        <if test='searchParamsVO.periodstartTime != null and searchParamsVO.periodstartTime != ""'>
            AND t.period_id <![CDATA[ >= ]]> #{searchParamsVO.periodStartTime}
        </if>
        <if test='searchParamsVO.periodEndTime != null and searchParamsVO.periodEndTime != ""'>
            AND t.period_id <![CDATA[ <= ]]> #{searchParamsVO.periodEndTime}
        </if>
        <if test='searchParamsVO.groupLevel =="LV1" and searchParamsVO.lv1DimensionSet != null and searchParamsVO.lv1DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv1DimensionSet' item="code" open="AND t.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupLevel =="LV2" and searchParamsVO.lv2DimensionSet != null and searchParamsVO.lv2DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv2DimensionSet' item="code" open="AND t.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupLevel != null and searchParamsVO.groupLevel != ""'>
            AND t.group_level = #{searchParamsVO.groupLevel}
        </if>
        <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
            and t.oversea_flag = #{searchParamsVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.viewFlag != null'>
            AND t.view_flag = #{searchParamsVO.viewFlag}
        </if>
        ORDER BY t.period_id
    </select>

    <select id="findMadeCombPriceIndexVOList" resultMap="resultMap">
        SELECT
        DISTINCT
        custom_id,
        custom_cn_name,
        group_level,
        group_code,
        period_year,
        period_id,
        group_cn_name,
        ROUND(cost_index, 2) AS cost_index,
        version_id,
        'M' AS cost_type,
        last_update_date,
        'Y' AS isComb
        FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_custom_month_cost_index_t
        WHERE del_flag = 'N'
        <if test='searchParamsVO.granularityType != null'>
            AND granularity_type = #{searchParamsVO.granularityType}
        </if>
        <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
            AND caliber_Flag = #{searchParamsVO.caliberFlag}
        </if>
        <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
            and lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.customIdList != null and searchParamsVO.customIdList.size() > 0'>
            <foreach collection='searchParamsVO.customIdList' item="code" open="AND custom_id IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.combinaCodeList != null and searchParamsVO.combinaCodeList.size() > 0'>
            <foreach collection='searchParamsVO.combinaCodeList' item="code" open="AND custom_id || '_##' || group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.basePeriodId != null'>
            AND base_period_id = #{searchParamsVO.basePeriodId}
        </if>
        <if test='searchParamsVO.versionId != null'>
            AND version_id = #{searchParamsVO.versionId}
        </if>
        <if test='searchParamsVO.periodStartTime != null and searchParamsVO.periodStartTime != ""'>
            AND period_id <![CDATA[ >= ]]> #{searchParamsVO.periodStartTime}
        </if>
        <if test='searchParamsVO.periodEndTime != null and searchParamsVO.periodEndTime != ""'>
            AND period_id <![CDATA[ <= ]]> #{searchParamsVO.periodEndTime}
        </if>
        <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
            and oversea_flag = #{searchParamsVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.viewFlag != null'>
            AND view_flag = #{searchParamsVO.viewFlag}
        </if>
        <if test='searchParamsVO.groupLevel != null and searchParamsVO.groupLevel != ""'>
            AND group_level = #{searchParamsVO.groupLevel}
        </if>
        ORDER BY period_id
    </select>

    <select id="findMadePriceIndexByMultiDim" resultMap="resultMap">
        <choose>
            <when test='searchParamsVO.groupLevel == "ITEM" or searchParamsVO.groupLevel == "SPART"'>
                <include refid="itemMultiFiled"></include>
                WHERE T1.del_flag = 'N'
            </when>
            <otherwise>
                <include refid="multiAllField"/>
                ON T1.group_code = T2.group_code
                AND T1.prod_rnd_team_code = T2.prod_rnd_team_code
                AND T1.oversea_flag = T2.oversea_flag
                AND T1.lv0_prod_list_code = T2.lv0_prod_list_code
                AND T1.caliber_Flag = T2.caliber_Flag
                AND T1.group_level = T2.group_level
                AND T1.PARENT_CODE = T2.PARENT_CODE
                AND T2.del_flag = 'N' AND T2.period_year_type = 'S'
                WHERE T1.del_flag = 'N'
                AND T2.version_id = #{searchParamsVO.versionId}
                AND T2.view_flag = #{searchParamsVO.viewFlag}
                and nvl(T1.shipping_object_code,'snull') = nvl(T2.shipping_object_code,'snull')
                and nvl(T1.manufacture_object_code,'snull') = nvl(T2.manufacture_object_code,'snull')
                <if test='searchParamsVO.coaCodeList != null and searchParamsVO.coaCodeList.size() > 0'>
                    <foreach collection='searchParamsVO.coaCodeList' item="code" open="AND T2.coa_code IN (" close=")" index="index" separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='searchParamsVO.dimensionCodeList != null and searchParamsVO.dimensionCodeList.size() > 0'>
                    <foreach collection='searchParamsVO.dimensionCodeList' item="code" open="AND T2.dimension_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='searchParamsVO.dimensionSubcategoryCodeList != null and searchParamsVO.dimensionSubcategoryCodeList.size() > 0'>
                    <foreach collection='searchParamsVO.dimensionSubcategoryCodeList' item="code" open="AND T2.dimension_subcategory_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='searchParamsVO.dimensionSubDetailCodeList != null and searchParamsVO.dimensionSubDetailCodeList.size() > 0'>
                    <foreach collection='searchParamsVO.dimensionSubDetailCodeList' item="code" open="AND T2.dimension_sub_detail_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='searchParamsVO.spartCodeList != null and searchParamsVO.spartCodeList.size() > 0'>
                    <foreach collection='searchParamsVO.spartCodeList' item="code" open="AND T2.spart_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='searchParamsVO.prodRndTeamCodeList != null and searchParamsVO.prodRndTeamCodeList.size() > 0'>
                    <foreach collection='searchParamsVO.prodRndTeamCodeList' item="code" open="AND T2.prod_rnd_team_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='searchParamsVO.l1NameList != null and searchParamsVO.l1NameList.size() > 0'>
                    <foreach collection='searchParamsVO.l1NameList' item="code" open="AND T2.l1_name IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='searchParamsVO.l2NameList != null and searchParamsVO.l2NameList.size() > 0'>
                    <foreach collection='searchParamsVO.l2NameList' item="code" open="AND T2.l2_name IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
                <if test='searchParamsVO.parentCodeList != null and searchParamsVO.parentCodeList.size() > 0'>
                    <foreach collection='searchParamsVO.parentCodeList' item="code" open="AND T2.parent_code IN (" close=")" index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>

            </otherwise>
        </choose>
        AND T1.version_id = #{searchParamsVO.versionId}
        <if test='searchParamsVO.basePeriodId != null'>
            AND T1.base_period_id = #{searchParamsVO.basePeriodId}
        </if>
        <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
            AND T1.caliber_Flag = #{searchParamsVO.caliberFlag}
        </if>
        <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
            and T1.lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.shippingObjectCode != null and searchParamsVO.shippingObjectCode != ""'>
            AND T1.shipping_object_code = #{searchParamsVO.shippingObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.manufactureObjectCode != null and searchParamsVO.manufactureObjectCode != ""'>
            AND T1.manufacture_object_code = #{searchParamsVO.manufactureObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.coaCodeList != null and searchParamsVO.coaCodeList.size() > 0'>
            <foreach collection='searchParamsVO.coaCodeList' item="code" open="AND T1.coa_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionCodeList != null and searchParamsVO.dimensionCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionCodeList' item="code" open="AND T1.dimension_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubcategoryCodeList != null and searchParamsVO.dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubcategoryCodeList' item="code" open="AND T1.dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubDetailCodeList != null and searchParamsVO.dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubDetailCodeList' item="code" open="AND T1.dimension_sub_detail_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.spartCodeList != null and searchParamsVO.spartCodeList.size() > 0'>
            <foreach collection='searchParamsVO.spartCodeList' item="code" open="AND T1.spart_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.prodRndTeamCodeList != null and searchParamsVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='searchParamsVO.prodRndTeamCodeList' item="code" open="AND T1.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l1NameList != null and searchParamsVO.l1NameList.size() > 0'>
            <foreach collection='searchParamsVO.l1NameList' item="code" open="AND T1.l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l2NameList != null and searchParamsVO.l2NameList.size() > 0'>
            <foreach collection='searchParamsVO.l2NameList' item="code" open="AND T1.l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.parentCodeList != null and searchParamsVO.parentCodeList.size() > 0'>
            <foreach collection='searchParamsVO.parentCodeList' item="code" open="AND T1.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.subGroupCodeList != null and searchParamsVO.subGroupCodeList.size() > 0'>
            <foreach collection='searchParamsVO.subGroupCodeList' item="code" open="AND T1.group_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.periodStartTime != null and searchParamsVO.periodStartTime != ""'>
            AND T1.period_id <![CDATA[ >= ]]> #{searchParamsVO.periodStartTime}
        </if>
        <if test='searchParamsVO.periodEndTime != null and searchParamsVO.periodEndTime != ""'>
            AND T1.period_id <![CDATA[ <= ]]> #{searchParamsVO.periodEndTime}
        </if>
        <if test='searchParamsVO.groupLevel =="LV1" and searchParamsVO.lv1DimensionSet != null and searchParamsVO.lv1DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv1DimensionSet' item="code" open="AND T1.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupLevel =="LV2" and searchParamsVO.lv2DimensionSet != null and searchParamsVO.lv2DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv2DimensionSet' item="code" open="AND T1.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
            and T1.oversea_flag = #{searchParamsVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        AND T1.view_flag = #{searchParamsVO.viewFlag}
        <if test='searchParamsVO.groupLevel != null and searchParamsVO.groupLevel != ""'>
            AND T1.group_level = #{searchParamsVO.groupLevel}
        </if>
        ORDER BY T1.group_code, T1.period_id
    </select>

    <select id="findMadePriceIndexNormalByMultiDim" resultMap="resultMap">
        with itemLevel as (
        select
        <choose>
            <when test='searchParamsVO.granularityType == "U"' >
                <if test='searchParamsVO.viewFlag == "1" and searchParamsVO.parentLevel == "LV1"'>
                    distinct top_item_code,
                    lv1_prod_rnd_team_code as parent_code,
                    lv1_prod_rd_team_cn_name as parent_cn_name,
                    lv1_prod_rnd_team_code as prod_rnd_team_code,
                    lv1_prod_rd_team_cn_name as prod_rnd_team_cn_name
                </if>
                <if test='searchParamsVO.viewFlag == "2" and searchParamsVO.parentLevel == "LV1"'>
                    distinct top_item_code,
                    lv1_prod_rnd_team_code as parent_code,
                    lv1_prod_rd_team_cn_name as parent_cn_name,
                    lv2_prod_rnd_team_code as prod_rnd_team_code,
                    lv2_prod_rd_team_cn_name as prod_rnd_team_cn_name
                </if>
                <if test='searchParamsVO.viewFlag == "3" and searchParamsVO.parentLevel == "LV1"'>
                    distinct top_item_code,
                    lv1_prod_rnd_team_code as parent_code,
                    lv1_prod_rd_team_cn_name as parent_cn_name,
                    lv3_prod_rnd_team_code as prod_rnd_team_code,
                    lv3_prod_rd_team_cn_name as prod_rnd_team_cn_name
                </if>
                <if test='searchParamsVO.viewFlag == "2" and searchParamsVO.parentLevel == "LV2"'>
                    distinct top_item_code,
                    lv2_prod_rnd_team_code as parent_code,
                    lv2_prod_rd_team_cn_name as parent_cn_name,
                    lv2_prod_rnd_team_code as prod_rnd_team_code,
                    lv2_prod_rd_team_cn_name as prod_rnd_team_cn_name
                </if>
                <if test='searchParamsVO.viewFlag == "3" and searchParamsVO.parentLevel == "LV2"'>
                    distinct top_item_code,
                    lv2_prod_rnd_team_code as parent_code,
                    lv2_prod_rd_team_cn_name as parent_cn_name,
                    lv3_prod_rnd_team_code as prod_rnd_team_code,
                    lv3_prod_rd_team_cn_name as prod_rnd_team_cn_name
                </if>
                <if test='searchParamsVO.viewFlag == "3" and searchParamsVO.parentLevel == "LV3"'>
                    distinct top_item_code,
                    lv3_prod_rnd_team_code as parent_code,
                    lv3_prod_rd_team_cn_name as parent_cn_name,
                    lv3_prod_rnd_team_code as prod_rnd_team_code,
                    lv3_prod_rd_team_cn_name as prod_rnd_team_cn_name
                </if>
                <if test='(searchParamsVO.parentLevel =="SHIPPING_OBJECT" or searchParamsVO.parentLevel == "MANUFACTURE_OBJECT") and searchParamsVO.teamLevel =="LV1"'>
                    distinct top_item_code,top_manufacture_object_code,lv1_prod_rnd_team_code as prod_rnd_team_code
                </if>
                <if test='(searchParamsVO.parentLevel =="SHIPPING_OBJECT" or searchParamsVO.parentLevel == "MANUFACTURE_OBJECT") and searchParamsVO.teamLevel =="LV2"'>
                    distinct top_item_code,top_manufacture_object_code,lv2_prod_rnd_team_code as prod_rnd_team_code
                </if>
                <if test='(searchParamsVO.parentLevel =="SHIPPING_OBJECT" or searchParamsVO.parentLevel == "MANUFACTURE_OBJECT") and searchParamsVO.teamLevel =="LV3"'>
                    distinct top_item_code,top_manufacture_object_code,lv3_prod_rnd_team_code as prod_rnd_team_code
                </if>
            </when>
            <when test='searchParamsVO.granularityType == "P"' >
                <if test='searchParamsVO.viewFlag == "1" and searchParamsVO.parentLevel == "LV1"'>
                    distinct top_item_code,
                    lv1_prod_rnd_team_code as parent_code,
                    lv1_prod_rd_team_cn_name as parent_cn_name,
                    lv1_prod_rnd_team_code as prod_rnd_team_code,
                    lv1_prod_rd_team_cn_name as prod_rnd_team_cn_name
                </if>
                <if test='(searchParamsVO.viewFlag == "2" or searchParamsVO.viewFlag == "3" or searchParamsVO.viewFlag == "4") and searchParamsVO.parentLevel == "LV1"'>
                    distinct top_item_code,
                    lv1_prod_rnd_team_code as parent_code,
                    lv1_prod_rd_team_cn_name as parent_cn_name,
                    lv2_prod_rnd_team_code as prod_rnd_team_code,
                    lv2_prod_rd_team_cn_name as prod_rnd_team_cn_name
                </if>
                <if test='(searchParamsVO.viewFlag == "2" or searchParamsVO.viewFlag == "3" or searchParamsVO.viewFlag == "4") and searchParamsVO.parentLevel == "LV2"'>
                    distinct top_item_code,
                    lv2_prod_rnd_team_code as parent_code,
                    lv2_prod_rd_team_cn_name as parent_cn_name,
                    lv2_prod_rnd_team_code as prod_rnd_team_code,
                    lv2_prod_rd_team_cn_name as prod_rnd_team_cn_name
                </if>
                <if test='(searchParamsVO.parentLevel =="SHIPPING_OBJECT" or searchParamsVO.parentLevel == "MANUFACTURE_OBJECT") and searchParamsVO.teamLevel =="LV1"'>
                    distinct top_item_code,top_manufacture_object_code,lv1_prod_rnd_team_code as prod_rnd_team_code
                </if>
                <if test='(searchParamsVO.parentLevel =="SHIPPING_OBJECT" or searchParamsVO.parentLevel == "MANUFACTURE_OBJECT") and searchParamsVO.teamLevel =="LV2"'>
                    distinct top_item_code,top_manufacture_object_code,lv2_prod_rnd_team_code as prod_rnd_team_code
                </if>
                <if test='(searchParamsVO.parentLevel =="L1" or searchParamsVO.parentLevel == "L2") and searchParamsVO.teamLevel =="LV2"'>
                    distinct top_item_code,lv2_prod_rnd_team_code as prod_rnd_team_code
                </if>
                <if test='(searchParamsVO.parentLevel =="L1" or searchParamsVO.parentLevel == "L2") and searchParamsVO.teamLevel =="LV1"'>
                    distinct top_item_code,lv1_prod_rnd_team_code as prod_rnd_team_code
                </if>
            </when>
            <when test='searchParamsVO.granularityType == "D"' >
                <if test='(searchParamsVO.viewFlag == "0" or searchParamsVO.viewFlag == "1"  or searchParamsVO.viewFlag == "2" or searchParamsVO.viewFlag == "9") and searchParamsVO.parentLevel == "LV1"'>
                    distinct top_item_code,
                    lv1_prod_rnd_team_code as parent_code,
                    lv1_prod_rd_team_cn_name as parent_cn_name,
                    lv1_prod_rnd_team_code as prod_rnd_team_code,
                    lv1_prod_rd_team_cn_name as prod_rnd_team_cn_name
                </if>
                <if test='(searchParamsVO.viewFlag != "0" and searchParamsVO.viewFlag != "1" and searchParamsVO.viewFlag != "2" and searchParamsVO.viewFlag != "9") and searchParamsVO.parentLevel == "LV1"'>
                    distinct top_item_code,
                    lv1_prod_rnd_team_code as parent_code,
                    lv1_prod_rd_team_cn_name as parent_cn_name,
                    lv2_prod_rnd_team_code as prod_rnd_team_code,
                    lv2_prod_rd_team_cn_name as prod_rnd_team_cn_name
                </if>
                <if test='(searchParamsVO.viewFlag == "3" or searchParamsVO.viewFlag == "4" or searchParamsVO.viewFlag == "5" or searchParamsVO.viewFlag == "10") and searchParamsVO.parentLevel == "LV2"'>
                    distinct top_item_code,
                    lv2_prod_rnd_team_code as parent_code,
                    lv2_prod_rd_team_cn_name as parent_cn_name,
                    lv2_prod_rnd_team_code as prod_rnd_team_code,
                    lv2_prod_rd_team_cn_name as prod_rnd_team_cn_name
                </if>
                <if test='(searchParamsVO.viewFlag != "3" and searchParamsVO.viewFlag != "4" and searchParamsVO.viewFlag != "5" and searchParamsVO.viewFlag != "10") and searchParamsVO.parentLevel == "LV2"'>
                    distinct top_item_code,
                    lv2_prod_rnd_team_code as parent_code,
                    lv2_prod_rd_team_cn_name as parent_cn_name,
                    lv3_prod_rnd_team_code as prod_rnd_team_code,
                    lv3_prod_rd_team_cn_name as prod_rnd_team_cn_name
                </if>
                <if test='(searchParamsVO.viewFlag == "6" or searchParamsVO.viewFlag == "7" or searchParamsVO.viewFlag == "8" or searchParamsVO.viewFlag == "11") and searchParamsVO.parentLevel == "LV3"'>
                    distinct top_item_code,
                    lv3_prod_rnd_team_code as parent_code,
                    lv3_prod_rd_team_cn_name as parent_cn_name,
                    lv3_prod_rnd_team_code as prod_rnd_team_code,
                    lv3_prod_rd_team_cn_name as prod_rnd_team_cn_name
                </if>
                <if test='(searchParamsVO.parentLevel =="SHIPPING_OBJECT" or searchParamsVO.parentLevel == "MANUFACTURE_OBJECT") and searchParamsVO.teamLevel =="LV1"'>
                    distinct top_item_code,top_manufacture_object_code,lv1_prod_rnd_team_code as prod_rnd_team_code
                </if>
                <if test='(searchParamsVO.parentLevel =="SHIPPING_OBJECT" or searchParamsVO.parentLevel == "MANUFACTURE_OBJECT") and searchParamsVO.teamLevel =="LV2"'>
                    distinct top_item_code,top_manufacture_object_code,lv2_prod_rnd_team_code as prod_rnd_team_code
                </if>
                <if test='(searchParamsVO.parentLevel =="SHIPPING_OBJECT" or searchParamsVO.parentLevel == "MANUFACTURE_OBJECT") and searchParamsVO.teamLevel =="LV3"'>
                    distinct top_item_code,top_manufacture_object_code,lv3_prod_rnd_team_code as prod_rnd_team_code
                </if>
                <if test='(searchParamsVO.parentLevel =="DIMENSION" or searchParamsVO.parentLevel == "SUBCATEGORY" or  searchParamsVO.parentLevel == "SUB_DETAIL" or  searchParamsVO.parentLevel == "SPART") and searchParamsVO.teamLevel =="LV1"'>
                    distinct top_item_code,lv1_prod_rnd_team_code as prod_rnd_team_code
                </if>
                <if test='(searchParamsVO.parentLevel =="DIMENSION" or searchParamsVO.parentLevel == "SUBCATEGORY" or  searchParamsVO.parentLevel == "SUB_DETAIL" or  searchParamsVO.parentLevel == "SPART") and searchParamsVO.teamLevel =="LV2"'>
                    distinct top_item_code,lv2_prod_rnd_team_code as prod_rnd_team_code
                </if>
                <if test='(searchParamsVO.parentLevel =="DIMENSION" or searchParamsVO.parentLevel == "SUBCATEGORY" or  searchParamsVO.parentLevel == "SUB_DETAIL" or  searchParamsVO.parentLevel == "SPART") and searchParamsVO.teamLevel =="LV3"'>
                    distinct top_item_code,lv3_prod_rnd_team_code as prod_rnd_team_code
                </if>
            </when>
        </choose>
        <if test='searchParamsVO.granularityType == "U"'>
            from fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_top_item_info_t
        </if>
        <if test='searchParamsVO.granularityType == "P"'>
            from fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_pft_top_item_info_t
        </if>
        <if test='searchParamsVO.granularityType == "D"'>
            from fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_dms_top_item_info_t
        </if>
        where del_flag = 'N'
        AND version_id = #{searchParamsVO.versionId}
        AND caliber_Flag = #{searchParamsVO.caliberFlag}
        AND is_top_flag ='Y'
        AND double_flag ='Y'
        <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
            and lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode}
        </if>
        <if test='searchParamsVO.shippingObjectCode != null and searchParamsVO.shippingObjectCode != ""'>
            and top_shipping_object_code = #{searchParamsVO.shippingObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.manufactureObjectCode != null and searchParamsVO.manufactureObjectCode != ""'>
            and top_manufacture_object_code = #{searchParamsVO.manufactureObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.subGroupCodeList!= null and searchParamsVO.subGroupCodeList.size() > 0'>
            <foreach collection='searchParamsVO.subGroupCodeList' item="code" open="AND top_item_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.parentLevel == "LV1" and searchParamsVO.parentCodeList!= null and searchParamsVO.parentCodeList.size() > 0'>
            <foreach collection='searchParamsVO.parentCodeList' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.parentLevel == "LV2" and searchParamsVO.parentCodeList!= null and searchParamsVO.parentCodeList.size() > 0'>
            <foreach collection='searchParamsVO.parentCodeList' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.parentLevel == "LV3" and searchParamsVO.parentCodeList!= null and searchParamsVO.parentCodeList.size() > 0'>
            <foreach collection='searchParamsVO.parentCodeList' item="code" open="AND lv3_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l1NameList!= null and searchParamsVO.l1NameList.size() > 0'>
            <foreach collection='searchParamsVO.l1NameList' item="code" open="AND l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l2NameList!= null and searchParamsVO.l2NameList.size() > 0'>
            <foreach collection='searchParamsVO.l2NameList' item="code" open="AND l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.coaCodeList != null and searchParamsVO.coaCodeList.size() > 0'>
            <foreach collection='searchParamsVO.coaCodeList' item="code" open="AND coa_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionCodeList != null and searchParamsVO.dimensionCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionCodeList' item="code" open="AND dimension_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubcategoryCodeList != null and searchParamsVO.dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubcategoryCodeList' item="code" open="AND dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubDetailCodeList != null and searchParamsVO.dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubDetailCodeList' item="code" open="AND dimension_sub_detail_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.spartCodeList != null and searchParamsVO.spartCodeList.size() > 0'>
            <foreach collection='searchParamsVO.spartCodeList' item="code" open="AND spart_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.parentLevel == "SHIPPING_OBJECT" and searchParamsVO.parentCodeList!= null and searchParamsVO.parentCodeList.size() > 0'>
            <foreach collection='searchParamsVO.parentCodeList' item="code" open="AND top_shipping_object_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.parentLevel == "MANUFACTURE_OBJECT" and searchParamsVO.parentCodeList!= null and searchParamsVO.parentCodeList.size() > 0'>
            <foreach collection='searchParamsVO.parentCodeList' item="code" open="AND top_manufacture_object_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.teamLevel =="LV1" and searchParamsVO.prodRndTeamCodeList != null and searchParamsVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='searchParamsVO.prodRndTeamCodeList' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.teamLevel =="LV2" and searchParamsVO.prodRndTeamCodeList != null and searchParamsVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='searchParamsVO.prodRndTeamCodeList' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.teamLevel =="LV3" and searchParamsVO.prodRndTeamCodeList != null and searchParamsVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='searchParamsVO.prodRndTeamCodeList' item="code" open="AND lv3_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.teamLevel =="LV1" and searchParamsVO.lv1DimensionSet != null and searchParamsVO.lv1DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv1DimensionSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.teamLevel =="LV2" and searchParamsVO.lv2DimensionSet != null and searchParamsVO.lv2DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv2DimensionSet' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
            and oversea_flag = #{searchParamsVO.overseaFlag}
        </if>
        AND view_flag = #{searchParamsVO.viewFlag}
        )
        SELECT DISTINCT
        <if test='searchParamsVO.parentLevel =="LV1" or searchParamsVO.parentLevel == "LV2" or searchParamsVO.parentLevel == "LV3"'>
            T2.parent_code,
            T2.parent_cn_name,
        </if>
        T1.prod_rnd_team_cn_name,
        T1.prod_rnd_team_code,
        <if test='searchParamsVO.granularityType == "P"'>
            T1.l1_name,T1.l2_name,
        </if>
        <if test='searchParamsVO.granularityType == "D"'>
            T1.dms_code,T1.dms_cn_name,
            <if test='searchParamsVO.industryOrg == "ENERGY"'>
                t1.coa_code, t1.coa_cn_name,
            </if>
            T1.dimension_code,T1.dimension_cn_name,
            T1.dimension_subcategory_code,T1.dimension_subcategory_cn_name,
            T1.dimension_sub_detail_code,T1.dimension_sub_detail_cn_name,
            T1.spart_code,T1.spart_cn_name,
        </if>
        T1.period_id,
        T1.group_level,
        T1.group_code,
        'M' AS cost_type,
        T1.group_cn_name,
        ROUND(T1.cost_index, 2) AS cost_index,
        T1.append_flag
        <if test='searchParamsVO.granularityType == "U"'>
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_month_cost_idx_t T1
        </if>
        <if test='searchParamsVO.granularityType == "P"'>
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_pft_month_cost_idx_t T1
        </if>
        <if test='searchParamsVO.granularityType == "D"'>
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_dms_month_cost_idx_t T1
        </if>
        INNER JOIN itemLevel T2 on T1.group_code = T2.top_item_code
        and T1.prod_rnd_team_code = T2.prod_rnd_team_code
        <choose>
            <when test='searchParamsVO.parentLevel =="SHIPPING_OBJECT" or searchParamsVO.parentLevel == "MANUFACTURE_OBJECT"'>
                and T1.parent_code = T2.top_manufacture_object_code
            </when>
        </choose>
        WHERE T1.del_flag = 'N'
        AND T1.version_id = #{searchParamsVO.versionId}
        <if test='searchParamsVO.basePeriodId != null'>
            AND T1.base_period_id = #{searchParamsVO.basePeriodId}
        </if>
        <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
            AND T1.caliber_Flag = #{searchParamsVO.caliberFlag}
        </if>
        <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
            and T1.lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.periodStartTime != null and searchParamsVO.periodStartTime != ""'>
            AND T1.period_id <![CDATA[ >= ]]> #{searchParamsVO.periodStartTime}
        </if>
        <if test='searchParamsVO.periodEndTime != null and searchParamsVO.periodEndTime != ""'>
            AND T1.period_id <![CDATA[ <= ]]> #{searchParamsVO.periodEndTime}
        </if>
        <if test='searchParamsVO.l1NameList!= null and searchParamsVO.l1NameList.size() > 0'>
            <foreach collection='searchParamsVO.l1NameList' item="code" open="AND T1.l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l2NameList!= null and searchParamsVO.l2NameList.size() > 0'>
            <foreach collection='searchParamsVO.l2NameList' item="code" open="AND T1.l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.coaCodeList != null and searchParamsVO.coaCodeList.size() > 0'>
            <foreach collection='searchParamsVO.coaCodeList' item="code" open="AND T1.coa_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionCodeList != null and searchParamsVO.dimensionCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionCodeList' item="code" open="AND T1.dimension_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubcategoryCodeList != null and searchParamsVO.dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubcategoryCodeList' item="code" open="AND T1.dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubDetailCodeList != null and searchParamsVO.dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubDetailCodeList' item="code" open="AND T1.dimension_sub_detail_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.spartCodeList != null and searchParamsVO.spartCodeList.size() > 0'>
            <foreach collection='searchParamsVO.spartCodeList' item="code" open="AND T1.spart_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
            and T1.oversea_flag = #{searchParamsVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        AND T1.view_flag = #{searchParamsVO.viewFlag}
        AND T1.group_level = 'ITEM'
        ORDER BY T1.group_code, T1.period_id
    </select>

    <select id="findMadePriceIndexCombByMultiDim" resultMap="resultMap">
        SELECT DISTINCT
        T1.custom_id,
        T1.custom_cn_name,
        T1.parent_code,
        T1.parent_cn_name,
        T1.parent_level,
        T1.period_id,
        T1.group_level,
        T1.group_code,
        'M' AS cost_type,
        T1.group_cn_name,
        IFNULL(ROUND(T1.cost_index,2), 0) AS cost_index,
        T1.append_flag
        FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_custom_month_cost_index_t T1
        WHERE T1.del_flag = 'N'
        AND T1.version_id = #{searchParamsVO.versionId}
        <if test='searchParamsVO.granularityType != null'>
            AND T1.granularity_type = #{searchParamsVO.granularityType}
        </if>
        <if test='searchParamsVO.basePeriodId != null'>
            AND T1.base_period_id = #{searchParamsVO.basePeriodId}
        </if>
        <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
            AND T1.caliber_Flag = #{searchParamsVO.caliberFlag}
        </if>
        <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
            and T1.lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.parentLevel != null and searchParamsVO.parentLevel != ""'>
            AND T1.parent_level = #{searchParamsVO.parentLevel}
        </if>
        <if test='searchParamsVO.customIdList != null and searchParamsVO.customIdList.size() > 0'>
            <foreach collection='searchParamsVO.customIdList' item="code" open="AND T1.custom_id IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.combinaCodeList != null and searchParamsVO.combinaCodeList.size() > 0'>
            <foreach collection='searchParamsVO.combinaCodeList' item="code" open="AND T1.custom_id || '_##' || T1.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.combinaSubGroupCodeList != null and searchParamsVO.combinaSubGroupCodeList.size() > 0'>
            <foreach collection='searchParamsVO.combinaSubGroupCodeList' item="code" open="AND T1.custom_id || '_##' || T1.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.periodStartTime != null and searchParamsVO.periodStartTime != ""'>
            AND T1.period_id <![CDATA[ >= ]]> #{searchParamsVO.periodStartTime}
        </if>
        <if test='searchParamsVO.periodEndTime != null and searchParamsVO.periodEndTime != ""'>
            AND T1.period_id <![CDATA[ <= ]]> #{searchParamsVO.periodEndTime}
        </if>
        <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
            and T1.oversea_flag = #{searchParamsVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        AND T1.view_flag = #{searchParamsVO.viewFlag}
        ORDER BY T1.group_code, T1.period_id
    </select>


    <select id="findAllItemCodeForAll" resultMap="resultMap">
        SELECT DISTINCT group_code,custom_id
        FROM fin_dm_opt_foi.${monthAnalysisVO.tablePrefix}_made_custom_month_cost_index_t
        WHERE del_flag = 'N'
        AND granularity_type = #{monthAnalysisVO.granularityType}
        AND version_id = #{monthAnalysisVO.versionId}
        <if test='monthAnalysisVO.granularityType != null'>
            AND granularity_type = #{monthAnalysisVO.granularityType}
        </if>
        <if test='monthAnalysisVO.basePeriodId != null'>
            AND base_period_id = #{monthAnalysisVO.basePeriodId}
        </if>
        <if test='monthAnalysisVO.caliberFlag!= null and monthAnalysisVO.caliberFlag != ""'>
            AND caliber_Flag = #{monthAnalysisVO.caliberFlag}
        </if>
        <if test='monthAnalysisVO.lv0ProdListCode != null and monthAnalysisVO.lv0ProdListCode != ""'>
            AND lv0_prod_list_code = #{monthAnalysisVO.lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.lv0ProdListCode != null and monthAnalysisVO.lv0ProdListCode != ""'>
            AND lv0_prod_list_code = #{monthAnalysisVO.lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupCode != null and monthAnalysisVO.groupCode != ""'>
            AND (UPPER(group_code) LIKE CONCAT(CONCAT('%', UPPER(#{monthAnalysisVO.groupCode})) ,'%')
            OR UPPER(group_code) LIKE CONCAT(CONCAT('%', UPPER(#{monthAnalysisVO.groupCode})) ,'%'))
        </if>
        <if test='monthAnalysisVO.parentLevel != null and monthAnalysisVO.parentLevel != ""'>
            AND parent_level = #{monthAnalysisVO.parentLevel}
        </if>
        <if test='monthAnalysisVO.customIdList != null and monthAnalysisVO.customIdList.size() > 0'>
            <foreach collection='monthAnalysisVO.customIdList' item="code" open="AND custom_id IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.combinaCodeList != null and monthAnalysisVO.combinaCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.combinaCodeList' item="code" open="AND custom_id || '_##' || parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            and oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        AND view_flag = #{monthAnalysisVO.viewFlag}
        UNION ALL
        SELECT DISTINCT top_item_code as group_code, 0 as custom_id
        <choose>
            <when test='monthAnalysisVO.granularityType == "U"'>
                FROM fin_dm_opt_foi.${monthAnalysisVO.tablePrefix}_made_top_item_info_t
            </when>
            <when test='monthAnalysisVO.granularityType == "P"'>
                FROM fin_dm_opt_foi.${monthAnalysisVO.tablePrefix}_made_pft_top_item_info_t
            </when>
            <when test='monthAnalysisVO.granularityType == "D"'>
                FROM fin_dm_opt_foi.${monthAnalysisVO.tablePrefix}_made_dms_top_item_info_t
            </when>
        </choose>
        WHERE view_flag = #{monthAnalysisVO.viewFlag,jdbcType=VARCHAR}
        AND del_flag ='N'
        AND caliber_flag = #{monthAnalysisVO.caliberFlag,jdbcType=VARCHAR}
        AND oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        AND lv0_prod_list_code = #{monthAnalysisVO.lv0ProdListCode,jdbcType=VARCHAR}
        AND version_id = #{monthAnalysisVO.versionId}
        AND is_top_flag ='Y'
        AND double_flag ='Y'
        <if test='monthAnalysisVO.groupCode != null and monthAnalysisVO.groupCode != ""'>
            AND (UPPER(top_item_code) LIKE CONCAT(CONCAT('%', UPPER(#{monthAnalysisVO.groupCode})) ,'%')
            OR UPPER(top_item_code) LIKE CONCAT(CONCAT('%', UPPER(#{monthAnalysisVO.groupCode})) ,'%'))
        </if>
        <if test='monthAnalysisVO.parentLevel == "L1" and monthAnalysisVO.parentCodeList != null and monthAnalysisVO.parentCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code" open="AND l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.parentLevel == "L2" and monthAnalysisVO.parentCodeList != null and monthAnalysisVO.parentCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code" open="AND l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.teamLevel == "LV1" and monthAnalysisVO.teamCodeList!= null and monthAnalysisVO.teamCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.teamCodeList' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.teamLevel == "LV2" and monthAnalysisVO.teamCodeList!= null and monthAnalysisVO.teamCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.teamCodeList' item="code" open="AND lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.teamLevel == "LV3" and monthAnalysisVO.teamCodeList!= null and monthAnalysisVO.teamCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.teamCodeList' item="code" open="AND lv3_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.parentLevel == "COA" and monthAnalysisVO.parentCodeList != null and monthAnalysisVO.parentCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code" open="AND coa_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.parentLevel == "DIMENSION" and monthAnalysisVO.parentCodeList != null and monthAnalysisVO.parentCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code" open="AND dimension_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.parentLevel == "SUBCATEGORY" and monthAnalysisVO.parentCodeList != null and monthAnalysisVO.parentCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code" open="AND dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.parentLevel == "SUB_DETAIL" and monthAnalysisVO.parentCodeList != null and monthAnalysisVO.parentCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code" open="AND dimension_sub_detail_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.parentLevel == "SPART" and monthAnalysisVO.parentCodeList != null and monthAnalysisVO.parentCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code" open="AND spart_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.parentLevel == "CEG" and monthAnalysisVO.parentCodeList!= null and monthAnalysisVO.parentCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code" open="AND top_l3_ceg_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.parentLevel == "MODL" and monthAnalysisVO.parentCodeList!= null and monthAnalysisVO.parentCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code" open="AND top_l4_ceg_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.parentLevel == "CATEGORY" and monthAnalysisVO.parentCodeList!= null and monthAnalysisVO.parentCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code" open="AND top_category_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.lv1DimensionSet != null and monthAnalysisVO.lv1DimensionSet.size() > 0'>
            <foreach collection='monthAnalysisVO.lv1DimensionSet' item="code" open="AND lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        ORDER BY group_code
        LIMIT #{pageVO.pageSize} OFFSET #{pageVO.startIndex} - 1
    </select>

    <select id="findAllItemCodeForComb" resultMap="resultMap">
        SELECT DISTINCT group_code,custom_id,custom_cn_name
        FROM fin_dm_opt_foi.${monthAnalysisVO.tablePrefix}_made_custom_month_cost_index_t
        WHERE del_flag = 'N'
        AND version_id = #{monthAnalysisVO.versionId}
        <if test='monthAnalysisVO.granularityType != null'>
            AND granularity_type = #{monthAnalysisVO.granularityType}
        </if>
        <if test='monthAnalysisVO.basePeriodId != null'>
            AND base_period_id = #{monthAnalysisVO.basePeriodId}
        </if>
        <if test='monthAnalysisVO.caliberFlag!= null and monthAnalysisVO.caliberFlag != ""'>
            AND caliber_Flag = #{monthAnalysisVO.caliberFlag}
        </if>
        <if test='monthAnalysisVO.lv0ProdListCode != null and monthAnalysisVO.lv0ProdListCode != ""'>
            and lv0_prod_list_code = #{monthAnalysisVO.lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupCode != null and monthAnalysisVO.groupCode != ""'>
            AND (UPPER(group_code) LIKE CONCAT(CONCAT('%', UPPER(#{monthAnalysisVO.groupCode})) ,'%')
            OR UPPER(group_code) LIKE CONCAT(CONCAT('%', UPPER(#{monthAnalysisVO.groupCode})) ,'%'))
        </if>
        <if test='monthAnalysisVO.parentLevel != null and monthAnalysisVO.parentLevel != ""'>
            AND parent_level = #{monthAnalysisVO.parentLevel}
        </if>
        <if test='monthAnalysisVO.customIdList != null and monthAnalysisVO.customIdList.size() > 0'>
            <foreach collection='monthAnalysisVO.customIdList' item="code" open="AND custom_id IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.combinaCodeList != null and monthAnalysisVO.combinaCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.combinaCodeList' item="code" open="AND custom_id || '_##' || parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            and oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        AND view_flag = #{monthAnalysisVO.viewFlag}
        order by group_code
        LIMIT #{pageVO.pageSize} OFFSET #{pageVO.startIndex} - 1
    </select>

    <select id="findAllItemCodeForCombCount" resultType="int">
        SELECT count(1) FROM (
        SELECT DISTINCT group_code,custom_id
        FROM fin_dm_opt_foi.${monthAnalysisVO.tablePrefix}_made_custom_month_cost_index_t T1
        WHERE T1.del_flag = 'N'
        AND T1.version_id = #{monthAnalysisVO.versionId}
        <if test='monthAnalysisVO.granularityType != null'>
            AND T1.granularity_type = #{monthAnalysisVO.granularityType}
        </if>
        <if test='monthAnalysisVO.basePeriodId != null'>
            AND T1.base_period_id = #{monthAnalysisVO.basePeriodId}
        </if>
        <if test='monthAnalysisVO.caliberFlag!= null and monthAnalysisVO.caliberFlag != ""'>
            AND T1.caliber_Flag = #{monthAnalysisVO.caliberFlag}
        </if>
        <if test='monthAnalysisVO.lv0ProdListCode != null and monthAnalysisVO.lv0ProdListCode != ""'>
            and T1.lv0_prod_list_code = #{monthAnalysisVO.lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.groupCode != null and monthAnalysisVO.groupCode != ""'>
            AND (UPPER(T1.group_code) LIKE CONCAT(CONCAT('%', UPPER(#{monthAnalysisVO.groupCode})) ,'%')
            OR UPPER(T1.group_code) LIKE CONCAT(CONCAT('%', UPPER(#{monthAnalysisVO.groupCode})) ,'%'))
        </if>
        <if test='monthAnalysisVO.parentLevel != null and monthAnalysisVO.parentLevel != ""'>
            AND T1.parent_level = #{monthAnalysisVO.parentLevel}
        </if>
        <if test='monthAnalysisVO.customIdList != null and monthAnalysisVO.customIdList.size() > 0'>
            <foreach collection='monthAnalysisVO.customIdList' item="code" open="AND T1.custom_id IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.combinaCodeList != null and monthAnalysisVO.combinaCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.combinaCodeList' item="code" open="AND T1.custom_id || '_##' || T1.parent_code IN ("
                     close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            and T1.oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        AND T1.view_flag = #{monthAnalysisVO.viewFlag}
        )
    </select>

    <select id="findAllItemCodeForAllCount" resultType="int">
        SELECT count(1) FROM (
        SELECT DISTINCT group_code,custom_id
        FROM fin_dm_opt_foi.${monthAnalysisVO.tablePrefix}_made_custom_month_cost_index_t T1
        WHERE T1.del_flag = 'N'
        AND T1.version_id = #{monthAnalysisVO.versionId}
        <if test='monthAnalysisVO.granularityType != null'>
            AND T1.granularity_type = #{monthAnalysisVO.granularityType}
        </if>
        <if test='monthAnalysisVO.basePeriodId != null'>
            AND T1.base_period_id = #{monthAnalysisVO.basePeriodId}
        </if>
        <if test='monthAnalysisVO.caliberFlag!= null and monthAnalysisVO.caliberFlag != ""'>
            AND T1.caliber_Flag = #{monthAnalysisVO.caliberFlag}
        </if>
        <if test='monthAnalysisVO.lv0ProdListCode != null and monthAnalysisVO.lv0ProdListCode != ""'>
            and T1.lv0_prod_list_code = #{monthAnalysisVO.lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='monthAnalysisVO.parentLevel != null and monthAnalysisVO.parentLevel != ""'>
            AND T1.parent_level = #{monthAnalysisVO.parentLevel}
        </if>
        <if test='monthAnalysisVO.customIdList != null and monthAnalysisVO.customIdList.size() > 0'>
            <foreach collection='monthAnalysisVO.customIdList' item="code" open="AND T1.custom_id IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.combinaCodeList != null and monthAnalysisVO.combinaCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.combinaCodeList' item="code" open="AND T1.custom_id || '_##' || T1.parent_code IN ("
                     close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.overseaFlag != null and monthAnalysisVO.overseaFlag != ""'>
            and T1.oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        AND T1.view_flag = #{monthAnalysisVO.viewFlag}
        union all
        SELECT DISTINCT top_item_code as group_code, 0 as custom_id
        <choose>
            <when test='monthAnalysisVO.granularityType == "U"'>
                from fin_dm_opt_foi.${monthAnalysisVO.tablePrefix}_top_item_info_t
            </when>
            <when test='monthAnalysisVO.granularityType == "P"'>
                from fin_dm_opt_foi.${monthAnalysisVO.tablePrefix}_pft_top_item_info_t
            </when>
            <when test='monthAnalysisVO.granularityType == "D"'>
                from fin_dm_opt_foi.${monthAnalysisVO.tablePrefix}_dms_top_item_info_t
            </when>
        </choose>
        where is_top_flag = 'Y'
        and double_flag = 'Y'
        and del_flag ='N'
        and caliber_flag = #{monthAnalysisVO.caliberFlag,jdbcType=VARCHAR}
        and version_id = #{monthAnalysisVO.versionId}
        and lv0_prod_list_code = #{monthAnalysisVO.lv0ProdListCode,jdbcType=VARCHAR}
        <if test='monthAnalysisVO.parentLevel == "L1" and monthAnalysisVO.parentCodeList != null and monthAnalysisVO.parentCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code" open="AND l1_name IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.parentLevel == "L2" and monthAnalysisVO.parentCodeList != null and monthAnalysisVO.parentCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code" open="AND l2_name IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.teamLevel == "LV1" and monthAnalysisVO.teamCodeList!= null and monthAnalysisVO.teamCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.teamCodeList' item="code" open="AND lv1_prod_rnd_team_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.teamLevel == "LV2" and monthAnalysisVO.teamCodeList!= null and monthAnalysisVO.teamCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.teamCodeList' item="code" open="AND lv2_prod_rnd_team_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.teamLevel == "LV3" and monthAnalysisVO.teamCodeList!= null and monthAnalysisVO.teamCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.teamCodeList' item="code" open="AND lv3_prod_rnd_team_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.parentLevel == "DIMENSION" and monthAnalysisVO.parentCodeList != null and monthAnalysisVO.parentCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code" open="AND dimension_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.parentLevel == "SUBCATEGORY" and monthAnalysisVO.parentCodeList != null and monthAnalysisVO.parentCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code"
                     open="AND dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.parentLevel == "SUB_DETAIL" and monthAnalysisVO.parentCodeList != null and monthAnalysisVO.parentCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code"
                     open="AND dimension_sub_detail_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.parentLevel == "SPART" and monthAnalysisVO.parentCodeList != null and monthAnalysisVO.parentCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code"
                     open="AND spart_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.parentLevel == "CEG" and monthAnalysisVO.parentCodeList!= null and monthAnalysisVO.parentCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code" open="AND top_l3_ceg_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.parentLevel == "MODL" and monthAnalysisVO.groupCodeList!= null and monthAnalysisVO.groupCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code" open="AND top_l4_ceg_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.parentLevel == "CATEGORY" and monthAnalysisVO.parentCodeList!= null and monthAnalysisVO.parentCodeList.size() > 0'>
            <foreach collection='monthAnalysisVO.parentCodeList' item="code" open="AND top_category_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.lv1DimensionSet != null and monthAnalysisVO.lv1DimensionSet.size() > 0'>
            <foreach collection='monthAnalysisVO.lv1DimensionSet' item="code" open="AND lv1_prod_rnd_team_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='monthAnalysisVO.lv2DimensionSet != null and monthAnalysisVO.lv2DimensionSet.size() > 0'>
            <foreach collection='monthAnalysisVO.lv2DimensionSet' item="code" open="AND lv2_prod_rnd_team_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        and oversea_flag = #{monthAnalysisVO.overseaFlag,jdbcType=VARCHAR}
        and view_flag = #{monthAnalysisVO.viewFlag,jdbcType=VARCHAR}
        )
    </select>

    <select id="findPriceIndexExpData" resultMap="resultMap">
        <if test='searchParamsVO.granularityType == "U"'>
            SELECT
            t.prod_rnd_team_cn_name,
            t.prod_rnd_team_code,
            t.parent_code,
            t.parent_cn_name,
            t.period_id,
            t.group_code,
            t.group_cn_name,
            t.group_level,
            'M' AS cost_type,
            ROUND(t.cost_index, 2) AS cost_index
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_month_cost_idx_t t
        </if>
        <if test='searchParamsVO.granularityType == "P"'>
            SELECT
            t.prod_rnd_team_cn_name,
            t.prod_rnd_team_code,
            t.parent_code,
            t.parent_cn_name,
            t.l1_name,
            t.l2_name,
            t.period_id,
            t.group_code,
            t.group_cn_name,
            t.group_level,
            'M' AS cost_type,
            ROUND(t.cost_index, 2) AS cost_index
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_pft_month_cost_idx_t t
        </if>
        <if test='searchParamsVO.granularityType == "D"'>
            SELECT
            t.prod_rnd_team_cn_name,
            t.prod_rnd_team_code,
            t.parent_code,
            t.parent_cn_name,
            <if test='searchParamsVO.industryOrg == "ENERGY"'>
                t.coa_code, t.coa_cn_name,
            </if>
            t.dimension_code,t.dimension_cn_name,
            t.dimension_subcategory_code,t.dimension_subcategory_cn_name,
            t.dimension_sub_detail_code,t.dimension_sub_detail_cn_name,
            t.spart_code,t.spart_cn_name,
            t.period_id,
            t.group_code,
            t.group_level,
            t.group_cn_name,
            'M' AS cost_type,
            ROUND(t.cost_index, 2) AS cost_index
            FROM fin_dm_opt_foi.${searchParamsVO.tablePreFix}_made_dms_month_cost_idx_t t
        </if>
        WHERE t.del_flag = 'N'
        <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
            and t.lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode}
        </if>
        <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
            AND t.caliber_Flag = #{searchParamsVO.caliberFlag}
        </if>
        <if test='searchParamsVO.basePeriodId != null and searchParamsVO.basePeriodId != ""'>
            AND t.base_period_id = #{searchParamsVO.basePeriodId}
        </if>
        <if test='searchParamsVO.versionId != null'>
            AND t.version_id = #{searchParamsVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='searchParamsVO.shippingObjectCode != null and searchParamsVO.shippingObjectCode != ""'>
            and t.shipping_object_code = #{searchParamsVO.shippingObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.manufactureObjectCode != null and searchParamsVO.manufactureObjectCode != ""'>
            and t.manufacture_object_code = #{searchParamsVO.manufactureObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.coaCodeList != null and searchParamsVO.coaCodeList.size() > 0'>
            <foreach collection='searchParamsVO.coaCodeList' item="code" open="AND t.coa_code IN (" close=")" index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionCodeList != null and searchParamsVO.dimensionCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionCodeList' item="code" open="AND t.dimension_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubcategoryCodeList != null and searchParamsVO.dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubcategoryCodeList' item="code" open="AND t.dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubDetailCodeList != null and searchParamsVO.dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubDetailCodeList' item="code" open="AND t.dimension_sub_detail_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.spartCodeList != null and searchParamsVO.spartCodeList.size() > 0'>
            <foreach collection='searchParamsVO.spartCodeList' item="code" open="AND t.spart_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.prodRndTeamCodeList != null and searchParamsVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='searchParamsVO.prodRndTeamCodeList' item="code" open="AND t.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l1NameList != null and searchParamsVO.l1NameList.size() > 0'>
            <foreach collection='searchParamsVO.l1NameList' item="code" open="AND t.l1_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l2NameList != null and searchParamsVO.l2NameList.size() > 0'>
            <foreach collection='searchParamsVO.l2NameList' item="code" open="AND t.l2_name IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.parentCodeList != null and searchParamsVO.parentCodeList.size() > 0'>
            <foreach collection='searchParamsVO.parentCodeList' item="code" open="AND t.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.periodStartTime != null and searchParamsVO.periodStartTime != ""'>
            AND t.period_id <![CDATA[ >= ]]> #{searchParamsVO.periodStartTime}
        </if>
        <if test='searchParamsVO.periodEndTime != null and searchParamsVO.periodEndTime != ""'>
            AND t.period_id <![CDATA[ <= ]]> #{searchParamsVO.periodEndTime}
        </if>
        <if test='searchParamsVO.groupLevel =="LV1" and searchParamsVO.lv1DimensionSet != null and searchParamsVO.lv1DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv1DimensionSet' item="code" open="AND t.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupLevel =="LV2" and searchParamsVO.lv2DimensionSet != null and searchParamsVO.lv2DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv2DimensionSet' item="code" open="AND t.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
            and t.oversea_flag = #{searchParamsVO.overseaFlag}
        </if>
        AND t.view_flag = #{searchParamsVO.viewFlag}
        <if test='searchParamsVO.groupLevel != null and searchParamsVO.groupLevel != ""'>
            AND t.group_level = #{searchParamsVO.groupLevel}
        </if>
        ORDER BY t.period_id
    </select>

    <select id="findStartEndTime" resultType="java.util.Map">
        SELECT MIN(PERIOD_ID) AS start, MAX(PERIOD_ID) AS end
        <choose>
            <when test='granularityType == "P"'>
                FROM FIN_DM_OPT_FOI.${tablePreFix}_MADE_PFT_MID_MONTH_ITEM_T
            </when>
            <when test='granularityType == "D"'>
                FROM FIN_DM_OPT_FOI.${tablePreFix}_MADE_DMS_MID_MONTH_ITEM_T
            </when>
            <otherwise>
                FROM FIN_DM_OPT_FOI.${tablePreFix}_MADE_MID_MONTH_ITEM_T
            </otherwise>
        </choose>
    </select>

    <select id="findAmpMadePriceIndexChart" resultMap="resultMap">
        <include refid="multiAllField"/>
        ON T1.group_code = T2.group_code
        AND T1.prod_rnd_team_code = T2.prod_rnd_team_code
        AND T1.oversea_flag = T2.oversea_flag
        AND T1.lv0_prod_list_code = T2.lv0_prod_list_code
        AND T1.caliber_Flag = T2.caliber_Flag
        AND T1.group_level = T2.group_level
        AND T1.PARENT_CODE = T2.PARENT_CODE
        <if test='searchParamsVO.industryOrg != null and searchParamsVO.granularityType =="D" and searchParamsVO.industryOrg =="ENERGY"'>
            AND nvl ( T1.coa_code, 'snull' ) = nvl ( T2.coa_code, 'snull' )
        </if>
        AND nvl(T1.shipping_object_code,'snull') = nvl(T2.shipping_object_code,'snull')
        AND nvl(T1.manufacture_object_code,'snull') = nvl(T2.manufacture_object_code,'snull')
        AND T2.del_flag = 'N' AND T2.period_year_type = 'S'
        WHERE T1.del_flag = 'N'
        AND T2.version_id = #{searchParamsVO.versionId}
        AND T2.view_flag = #{searchParamsVO.viewFlag}
        <if test='searchParamsVO.coaCodeList != null and searchParamsVO.coaCodeList.size() > 0'>
            <foreach collection='searchParamsVO.coaCodeList' item="code" open="AND T1.coa_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.spartCodeList != null and searchParamsVO.spartCodeList.size() > 0'>
            <foreach collection='searchParamsVO.spartCodeList' item="code" open="AND T1.spart_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionCodeList != null and searchParamsVO.dimensionCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionCodeList' item="code" open="AND T2.dimension_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubcategoryCodeList != null and searchParamsVO.dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubcategoryCodeList' item="code"
                     open="AND T2.dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubDetailCodeList != null and searchParamsVO.dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubDetailCodeList' item="code"
                     open="AND T2.dimension_sub_detail_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.spartCodeList != null and searchParamsVO.spartCodeList.size() > 0'>
            <foreach collection='searchParamsVO.spartCodeList' item="code" open="AND T2.spart_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.prodRndTeamCodeList != null and searchParamsVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='searchParamsVO.prodRndTeamCodeList' item="code" open="AND T2.prod_rnd_team_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l1NameList != null and searchParamsVO.l1NameList.size() > 0'>
            <foreach collection='searchParamsVO.l1NameList' item="code" open="AND T2.l1_name IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l2NameList != null and searchParamsVO.l2NameList.size() > 0'>
            <foreach collection='searchParamsVO.l2NameList' item="code" open="AND T2.l2_name IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.parentCodeList != null and searchParamsVO.parentCodeList.size() > 0'>
            <foreach collection='searchParamsVO.parentCodeList' item="code" open="AND T2.parent_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        AND T1.version_id = #{searchParamsVO.versionId}
        AND T1.view_flag = #{searchParamsVO.viewFlag}
        <if test='searchParamsVO.basePeriodId != null'>
            AND T1.base_period_id = #{searchParamsVO.basePeriodId}
        </if>
        <if test='searchParamsVO.caliberFlag!= null and searchParamsVO.caliberFlag != ""'>
            AND T1.caliber_Flag = #{searchParamsVO.caliberFlag}
        </if>
        <if test='searchParamsVO.overseaFlag != null and searchParamsVO.overseaFlag != ""'>
            and T1.oversea_flag = #{searchParamsVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.lv0ProdListCode != null and searchParamsVO.lv0ProdListCode != ""'>
            and T1.lv0_prod_list_code = #{searchParamsVO.lv0ProdListCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.groupLevel != null and searchParamsVO.groupLevel != ""'>
            AND T1.group_level = #{searchParamsVO.groupLevel}
        </if>
        <if test='searchParamsVO.shippingObjectCode != null and searchParamsVO.shippingObjectCode != ""'>
            AND T1.shipping_object_code = #{searchParamsVO.shippingObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.manufactureObjectCode != null and searchParamsVO.manufactureObjectCode != ""'>
            AND T1.manufacture_object_code = #{searchParamsVO.manufactureObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='searchParamsVO.dimensionCodeList != null and searchParamsVO.dimensionCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionCodeList' item="code" open="AND T1.dimension_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubcategoryCodeList != null and searchParamsVO.dimensionSubcategoryCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubcategoryCodeList' item="code"
                     open="AND T1.dimension_subcategory_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.dimensionSubDetailCodeList != null and searchParamsVO.dimensionSubDetailCodeList.size() > 0'>
            <foreach collection='searchParamsVO.dimensionSubDetailCodeList' item="code"
                     open="AND T1.dimension_sub_detail_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.spartCodeList != null and searchParamsVO.spartCodeList.size() > 0'>
            <foreach collection='searchParamsVO.spartCodeList' item="code" open="AND T1.spart_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.prodRndTeamCodeList != null and searchParamsVO.prodRndTeamCodeList.size() > 0'>
            <foreach collection='searchParamsVO.prodRndTeamCodeList' item="code" open="AND T1.prod_rnd_team_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l1NameList != null and searchParamsVO.l1NameList.size() > 0'>
            <foreach collection='searchParamsVO.l1NameList' item="code" open="AND T1.l1_name IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.l2NameList != null and searchParamsVO.l2NameList.size() > 0'>
            <foreach collection='searchParamsVO.l2NameList' item="code" open="AND T1.l2_name IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.parentCodeList != null and searchParamsVO.parentCodeList.size() > 0'>
            <foreach collection='searchParamsVO.parentCodeList' item="code" open="AND T1.parent_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.subGroupCodeList != null and searchParamsVO.subGroupCodeList.size() > 0'>
            <foreach collection='searchParamsVO.subGroupCodeList' item="code" open="AND T1.group_code IN (" close=")"
                     index="index" separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.periodStartTime != null and searchParamsVO.periodStartTime != ""'>
            AND T1.period_id <![CDATA[ >= ]]> #{searchParamsVO.periodStartTime}
        </if>
        <if test='searchParamsVO.periodEndTime != null and searchParamsVO.periodEndTime != ""'>
            AND T1.period_id <![CDATA[ <= ]]> #{searchParamsVO.periodEndTime}
        </if>
        <if test='searchParamsVO.begin != null and searchParamsVO.begin != "" and searchParamsVO.end != null and searchParamsVO.end != ""'>
            AND (T1.period_id <![CDATA[ = ]]> #{searchParamsVO.begin} or T1.period_id <![CDATA[ = ]]> #{searchParamsVO.end})
        </if>
        <if test='searchParamsVO.groupLevel =="LV1" and searchParamsVO.lv1DimensionSet != null and searchParamsVO.lv1DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv1DimensionSet' item="code" open="AND T1.prod_rnd_team_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='searchParamsVO.groupLevel =="LV2" and searchParamsVO.lv2DimensionSet != null and searchParamsVO.lv2DimensionSet.size() > 0'>
            <foreach collection='searchParamsVO.lv2DimensionSet' item="code" open="AND T1.prod_rnd_team_code IN ("
                     close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        ORDER BY T1.group_code, T1.period_id
    </select>

</mapper>
