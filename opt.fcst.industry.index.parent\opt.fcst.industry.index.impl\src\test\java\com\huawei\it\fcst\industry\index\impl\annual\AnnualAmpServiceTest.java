/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.annual;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.verify;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.when;

import com.huawei.it.fcst.industry.index.config.ExecutorConfig;
import com.huawei.it.fcst.industry.index.dao.IAnnualAmpDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeViewInfoDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocVersionDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocViewInfoDao;
import com.huawei.it.fcst.industry.index.dao.IMadeAnnualAmpDao;
import com.huawei.it.fcst.industry.index.dao.ITotalAnnualAmpDao;
import com.huawei.it.fcst.industry.index.impl.common.CommonService;
import com.huawei.it.fcst.industry.index.impl.month.AsyncQueryService;
import com.huawei.it.fcst.config.ConfigUtil;
import com.huawei.it.fcst.industry.index.utils.ExcelUtil;
import com.huawei.it.fcst.industry.index.utils.FcstIndexUtil;
import com.huawei.it.fcst.industry.index.utils.FileProcessUtis;
import com.huawei.it.fcst.industry.index.utils.TestUtils;
import com.huawei.it.fcst.industry.index.vo.annual.AnnualAnalysisVO;
import com.huawei.it.fcst.industry.index.vo.annual.AnnualParamVO;
import com.huawei.it.fcst.industry.index.vo.annual.DmFocAnnualAmpVO;
import com.huawei.it.fcst.industry.index.vo.common.AbstractExcelTitleVO;
import com.huawei.it.fcst.industry.index.vo.common.CommonViewVO;
import com.huawei.it.fcst.industry.index.vo.common.DataPermissionsVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFoiImpExpRecordVO;
import com.huawei.it.fcst.industry.index.vo.common.HeaderVo;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.fcst.industry.index.vo.config.DmFocVersionInfoDTO;
import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.security.UserVO;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletResponse;

/**
 * AnnualAmpServiceTest Class
 *
 * <AUTHOR>
 * @since 2023/4/20
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({ConfigUtil.class,FileProcessUtis.class,StatisticsExcelService.class,FcstIndexUtil.class})
public class AnnualAmpServiceTest {

    private static final Logger LOGGER = LogManager.getLogger(AnnualAmpServiceTest.class);

    @InjectMocks
    private AnnualAmpService annualAmpService;

    @Mock
    private IAnnualAmpDao annualAmpDao;

    @Mock
    private IDmFocViewInfoDao dmFocViewInfoDao;

    @Mock
    private IDmFocVersionDao dmFocVersionDao;

    @Mock
    private ExcelUtil excelUtil;

    @Mock
    private ConfigUtil configUtil;

    @Mock
    private StatisticsExcelService statisticsExcelService;

    @Mock
    private AnnualCommonService annualCommonService;

    @Mock
    private IDmFocMonthCostIdxDao iDmFocMonthCostIdxDao;

    @Mock
    private CommonService commonService;

    @Mock
    private AsyncQueryService asyncQueryService;

    @Mock
    private ITotalAnnualAmpDao totalAnnualAmpDao;

    @Mock
    private IMadeAnnualAmpDao madeAnnualAmpDao;

    @Mock
    private IDmFocMadeViewInfoDao dmFocMadeViewInfoDao;

    @Mock
    private ExecutorConfig executorConfig;

    private String plainText = "12adadadsadssaasddsaasdsdaasdfsd1223aaaa";

    private JSONObject arg;

    @Before
    public void before() throws Exception {
        MockitoAnnotations.openMocks(this);
        UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);
        arg = TestUtils.getTestArg(
                "/com/huawei/it/fcst/industry/index/impl/annual/AnnualAmpService/industryCostList.json");
    }

    @Test
    public void allIndustryCost() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("ICT");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        List<String> list = new ArrayList<>();
        list.add("1175D");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("1.0");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO.setGroupLevel("LV1");
        dmFocAnnualAmpVO.setGroupCode("1216541");
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCostTwoTest() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("LV1");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        List<String> list = new ArrayList<>();
        list.add("1175D");
        annualAnalysisVO.setGroupCodeList(list);
        List<String> teamCodelist = new ArrayList<>();
        teamCodelist.add("10011");
        annualAnalysisVO.setTeamCodeList(teamCodelist);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("1.0");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));

        dmFocAnnualAmpVO.setGroupLevel("LV1");
        dmFocAnnualAmpVO.setGroupCode("11065");
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO2.setGroupLevel("LV1");
        dmFocAnnualAmpVO2.setGroupCode("11011");
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO3.setGroupLevel("DIMENSION");
        dmFocAnnualAmpVO3.setGroupCode("13654");
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCosThreeTest() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("LV2");
        annualAnalysisVO.setIsMultipleSelect(false);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        List<String> list = new ArrayList<>();
        list.add("1175D");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost4Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("ITEM");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        List<String> list = new ArrayList<>();
        list.add("1175D");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("1.0");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));

        dmFocAnnualAmpVO.setGroupLevel("LV1");
        dmFocAnnualAmpVO.setGroupCode("113556");
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost5Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("ITEM");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        List<String> list = new ArrayList<>();
        list.add("1175D");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0%");
        dmFocAnnualAmpVO.setStatusCode("1");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO.setGroupCode("13554");
        dmFocAnnualAmpVO.setGroupLevel("LV1");
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0%");
        dmFocAnnualAmpVO2.setStatusCode("1");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO2.setGroupCode("13554");
        dmFocAnnualAmpVO2.setGroupLevel("LV1");
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0%");
        dmFocAnnualAmpVO3.setStatusCode("1");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO3.setGroupCode("13554");
        dmFocAnnualAmpVO3.setGroupLevel("LV1");
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost6Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("ITEM");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        List<String> list = new ArrayList<>();
        list.add("1175D");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0%");
        dmFocAnnualAmpVO.setStatusCode("2");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO.setGroupLevel("LV1");
        dmFocAnnualAmpVO.setGroupCode("110056");
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0%");
        dmFocAnnualAmpVO2.setStatusCode("2");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO2.setGroupLevel("LV1");
        dmFocAnnualAmpVO2.setGroupCode("11011");
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0%");
        dmFocAnnualAmpVO3.setStatusCode("2");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));

        dmFocAnnualAmpVO3.setGroupLevel("LV1");
        dmFocAnnualAmpVO3.setGroupCode("134311");
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost7Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("ITEM");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        List<String> list = new ArrayList<>();
        list.add("1175D");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0");
        dmFocAnnualAmpVO.setStatusCode("3");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));

        dmFocAnnualAmpVO.setGroupLevel("LV1");
        dmFocAnnualAmpVO.setGroupCode("11011");
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0%");
        dmFocAnnualAmpVO2.setStatusCode("3");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));

        dmFocAnnualAmpVO2.setGroupLevel("LV1");
        dmFocAnnualAmpVO2.setGroupCode("11041");
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0%");
        dmFocAnnualAmpVO3.setStatusCode("3");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));

        dmFocAnnualAmpVO3.setGroupLevel("LV1");
        dmFocAnnualAmpVO3.setGroupCode("110781");
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost8Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("ITEM");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        List<String> list = new ArrayList<>();
        list.add("1175D");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0");
        dmFocAnnualAmpVO.setStatusCode("4");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO.setGroupLevel("LV1");
        dmFocAnnualAmpVO.setGroupCode("156441");
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0%");
        dmFocAnnualAmpVO2.setStatusCode("4");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO2.setGroupLevel("LV1");
        dmFocAnnualAmpVO2.setGroupCode("16899");
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0%");
        dmFocAnnualAmpVO3.setStatusCode("4");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));

        dmFocAnnualAmpVO3.setGroupLevel("LV1");
        dmFocAnnualAmpVO3.setGroupCode("15644");
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost9Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("ITEM");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        List<String> list = new ArrayList<>();
        list.add("1175D");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0");
        dmFocAnnualAmpVO.setStatusCode("5");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));

        dmFocAnnualAmpVO.setGroupLevel("LV1");
        dmFocAnnualAmpVO.setGroupCode("110108");
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0%");
        dmFocAnnualAmpVO2.setStatusCode("5");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));

        dmFocAnnualAmpVO2.setGroupLevel("LV1");
        dmFocAnnualAmpVO2.setGroupCode("11891");
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0%");
        dmFocAnnualAmpVO3.setStatusCode("5");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));

        dmFocAnnualAmpVO3.setGroupLevel("LV1");
        dmFocAnnualAmpVO3.setGroupCode("110110");
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost10Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("ITEM");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        List<String> list = new ArrayList<>();
        list.add("1175D");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0%");
        dmFocAnnualAmpVO.setStatusCode("6");
        dmFocAnnualAmpVO.setGroupLevel("LV1");
        dmFocAnnualAmpVO.setGroupCode("15634");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0%");
        dmFocAnnualAmpVO2.setStatusCode("6");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO2.setGroupLevel("LV1");
        dmFocAnnualAmpVO2.setGroupCode("14444");
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0%");
        dmFocAnnualAmpVO3.setStatusCode("6");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO3.setGroupLevel("LV1");
        dmFocAnnualAmpVO3.setGroupCode("15684");
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost11Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("ITEM");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        List<String> list = new ArrayList<>();
        list.add("1175D");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0%");
        dmFocAnnualAmpVO.setStatusCode("7");
        dmFocAnnualAmpVO.setGroupLevel("LV1");
        dmFocAnnualAmpVO.setGroupCode("15644");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0%");
        dmFocAnnualAmpVO2.setStatusCode("7");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO2.setGroupLevel("LV1");
        dmFocAnnualAmpVO2.setGroupCode("15874");
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0%");
        dmFocAnnualAmpVO3.setStatusCode("7");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO3.setGroupLevel("LV1");
        dmFocAnnualAmpVO3.setGroupCode("15659");
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);

        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost12Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("ITEM");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        List<String> list = new ArrayList<>();
        list.add("1175D");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0%");
        dmFocAnnualAmpVO.setStatusCode("0");
        dmFocAnnualAmpVO.setGroupLevel("LV1");
        dmFocAnnualAmpVO.setGroupCode("15644");

        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));

        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0%");
        dmFocAnnualAmpVO2.setStatusCode("0");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO2.setGroupLevel("LV1");
        dmFocAnnualAmpVO2.setGroupCode("156401");

        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0%");
        dmFocAnnualAmpVO3.setStatusCode("0");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO3.setGroupLevel("LV1");
        dmFocAnnualAmpVO3.setGroupCode("15478");
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);

        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost13Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("ITEM");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        List<String> list = new ArrayList<>();
        list.add("1175D");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0%");
        dmFocAnnualAmpVO.setStatusCode("8");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO.setGroupLevel("LV1");
        dmFocAnnualAmpVO.setGroupCode("15644");
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0%");
        dmFocAnnualAmpVO2.setStatusCode("8");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO2.setGroupLevel("LV1");
        dmFocAnnualAmpVO2.setGroupCode("18564");
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0%");
        dmFocAnnualAmpVO3.setStatusCode("8");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO3.setGroupLevel("LV1");
        dmFocAnnualAmpVO3.setGroupCode("17884");
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost14Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("ITEM");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        List<String> list = new ArrayList<>();
        list.add("1175D");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0%");
        dmFocAnnualAmpVO.setStatusCode("9");
        dmFocAnnualAmpVO.setGroupLevel("LV1");
        dmFocAnnualAmpVO.setGroupCode("1234534");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0%");
        dmFocAnnualAmpVO2.setStatusCode("9");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO2.setGroupLevel("LV1");
        dmFocAnnualAmpVO2.setGroupCode("10001");
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0%");
        dmFocAnnualAmpVO3.setStatusCode("9");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO3.setGroupLevel("LV1");
        dmFocAnnualAmpVO3.setGroupCode("102111");
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost15Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("ITEM");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        List<String> list = new ArrayList<>();
        list.add("1175D");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0%");
        dmFocAnnualAmpVO.setStatusCode("10");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO.setGroupLevel("LV1");
        dmFocAnnualAmpVO.setGroupCode("1002111");
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0%");
        dmFocAnnualAmpVO2.setStatusCode("10");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO2.setGroupLevel("LV1");
        dmFocAnnualAmpVO2.setGroupCode("1021011");
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0%");
        dmFocAnnualAmpVO3.setStatusCode("10");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO3.setGroupLevel("LV1");
        dmFocAnnualAmpVO3.setGroupCode("1021110");
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost16Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("ITEM");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        List<String> list = new ArrayList<>();
        list.add("1175D");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0%");
        dmFocAnnualAmpVO.setStatusCode("11");
        dmFocAnnualAmpVO.setGroupCode("13335");
        dmFocAnnualAmpVO.setGroupLevel("LV1");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0%");
        dmFocAnnualAmpVO2.setStatusCode("11");
        dmFocAnnualAmpVO2.setGroupCode("13331");
        dmFocAnnualAmpVO2.setGroupLevel("LV1");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0%");
        dmFocAnnualAmpVO3.setStatusCode("11");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO3.setGroupCode("13334");
        dmFocAnnualAmpVO3.setGroupLevel("LV1");
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost17Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("ITEM");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        List<String> list = new ArrayList<>();
        list.add("1175D");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0%");
        dmFocAnnualAmpVO.setStatusCode("12");
        dmFocAnnualAmpVO.setGroupCode("12221");
        dmFocAnnualAmpVO.setGroupLevel("LV1");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0%");
        dmFocAnnualAmpVO2.setStatusCode("12");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO2.setGroupCode("12222");
        dmFocAnnualAmpVO2.setGroupLevel("LV1");
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0%");
        dmFocAnnualAmpVO3.setStatusCode("12");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO3.setGroupCode("12222");
        dmFocAnnualAmpVO3.setGroupLevel("LV1");
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost18Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("ITEM");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        List<String> list = new ArrayList<>();
        list.add("1175D");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0%");
        dmFocAnnualAmpVO.setStatusCode("13");
        dmFocAnnualAmpVO.setGroupCode("111112");
        dmFocAnnualAmpVO.setGroupLevel("LV1");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0%");
        dmFocAnnualAmpVO2.setStatusCode("13");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO2.setGroupCode("111114");
        dmFocAnnualAmpVO2.setGroupLevel("LV1");
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0%");
        dmFocAnnualAmpVO3.setStatusCode("13");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));

        dmFocAnnualAmpVO3.setGroupCode("111134");
        dmFocAnnualAmpVO3.setGroupLevel("LV1");
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost19Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("ITEM");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        List<String> list = new ArrayList<>();
        list.add("1175D");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0%");
        dmFocAnnualAmpVO.setStatusCode("14");
        dmFocAnnualAmpVO.setGroupCode("11222");
        dmFocAnnualAmpVO.setGroupLevel("LV1");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0%");
        dmFocAnnualAmpVO2.setStatusCode("14");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO2.setGroupCode("11333");
        dmFocAnnualAmpVO2.setGroupLevel("LV1");
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0%");
        dmFocAnnualAmpVO3.setStatusCode("14");
        dmFocAnnualAmpVO3.setGroupCode("11444");
        dmFocAnnualAmpVO3.setGroupLevel("LV1");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost20Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("ITEM");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        List<String> list = new ArrayList<>();
        list.add("1175D");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0%");
        dmFocAnnualAmpVO.setStatusCode("15");
        dmFocAnnualAmpVO.setGroupCode("111110");
        dmFocAnnualAmpVO.setGroupLevel("LV1");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0%");
        dmFocAnnualAmpVO2.setStatusCode("15");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO2.setGroupCode("11222");
        dmFocAnnualAmpVO2.setGroupLevel("LV1");
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0%");
        dmFocAnnualAmpVO3.setStatusCode("15");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO3.setGroupCode("11444");
        dmFocAnnualAmpVO3.setGroupLevel("LV1");
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost21Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        List<String> list = new ArrayList<>();
        list.add("1175D");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0%");
        dmFocAnnualAmpVO.setStatusCode("0");
        dmFocAnnualAmpVO.setGroupCode("12258");
        dmFocAnnualAmpVO.setGroupLevel("LV1");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0%");
        dmFocAnnualAmpVO2.setStatusCode("0");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO2.setGroupCode("555421");
        dmFocAnnualAmpVO2.setGroupLevel("LV1");
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0%");
        dmFocAnnualAmpVO3.setStatusCode("0");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO3.setGroupCode("16554");
        dmFocAnnualAmpVO3.setGroupLevel("LV1");
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost22Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        List<String> list = new ArrayList<>();
        list.add("1175D");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0%");
        dmFocAnnualAmpVO.setStatusCode("1");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO.setGroupLevel("LV1");
        dmFocAnnualAmpVO.setGroupCode("32565");
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0%");
        dmFocAnnualAmpVO2.setStatusCode("1");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO2.setGroupLevel("LV1");
        dmFocAnnualAmpVO2.setGroupCode("54451");
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0%");
        dmFocAnnualAmpVO3.setStatusCode("1");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO3.setGroupLevel("LV1");
        dmFocAnnualAmpVO3.setGroupCode("544511");
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost23Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        List<String> list = new ArrayList<>();
        list.add("1175D");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0%");
        dmFocAnnualAmpVO.setStatusCode("2");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO.setGroupLevel("LV1");
        dmFocAnnualAmpVO.setGroupCode("54451");
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0%");
        dmFocAnnualAmpVO2.setStatusCode("2");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO2.setGroupLevel("LV1");
        dmFocAnnualAmpVO2.setGroupCode("2554");
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0%");
        dmFocAnnualAmpVO3.setStatusCode("2");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO3.setGroupLevel("LV1");
        dmFocAnnualAmpVO3.setGroupCode("54451");
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost24Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        List<String> list = new ArrayList<>();
        list.add("1175D");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0%");
        dmFocAnnualAmpVO.setStatusCode("3");
        dmFocAnnualAmpVO.setGroupLevel("LV1");
        dmFocAnnualAmpVO.setGroupCode("1216541");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0%");
        dmFocAnnualAmpVO2.setStatusCode("3");
        dmFocAnnualAmpVO2.setGroupLevel("LV1");
        dmFocAnnualAmpVO2.setGroupCode("1216541");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0%");
        dmFocAnnualAmpVO3.setStatusCode("3");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO3.setGroupLevel("LV1");
        dmFocAnnualAmpVO3.setGroupCode("1216541");
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost25Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        List<String> list = new ArrayList<>();
        list.add("1175D");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0%");
        dmFocAnnualAmpVO.setStatusCode("4");
        dmFocAnnualAmpVO.setGroupLevel("LV1");
        dmFocAnnualAmpVO.setGroupCode("2213");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0%");
        dmFocAnnualAmpVO2.setStatusCode("4");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO2.setGroupLevel("LV1");
        dmFocAnnualAmpVO2.setGroupCode("2213");
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0%");
        dmFocAnnualAmpVO3.setStatusCode("4");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO3.setGroupLevel("LV1");
        dmFocAnnualAmpVO3.setGroupCode("1216541");
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost26Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        List<String> list = new ArrayList<>();
        list.add("1175D");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0%");
        dmFocAnnualAmpVO.setStatusCode("5");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO.setGroupLevel("LV1");
        dmFocAnnualAmpVO.setGroupCode("444545");
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0%");
        dmFocAnnualAmpVO2.setStatusCode("5");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO2.setGroupLevel("LV1");
        dmFocAnnualAmpVO2.setGroupCode("45554");
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0%");
        dmFocAnnualAmpVO3.setStatusCode("5");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO3.setGroupLevel("LV1");
        dmFocAnnualAmpVO3.setGroupCode("11");
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost27Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        List<String> list = new ArrayList<>();
        list.add("1175D");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0%");
        dmFocAnnualAmpVO.setStatusCode("6");
        dmFocAnnualAmpVO.setGroupCode("16554");
        dmFocAnnualAmpVO.setGroupLevel("LV1");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0%");
        dmFocAnnualAmpVO2.setStatusCode("6");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO2.setGroupCode("125456");
        dmFocAnnualAmpVO2.setGroupLevel("LV1");
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0%");
        dmFocAnnualAmpVO3.setStatusCode("6");
        dmFocAnnualAmpVO3.setGroupCode("22133");
        dmFocAnnualAmpVO3.setGroupLevel("LV1");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost28Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        List<String> list = new ArrayList<>();
        list.add("1175D");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0%");
        dmFocAnnualAmpVO.setStatusCode("7");
        dmFocAnnualAmpVO.setGroupLevel("LV1");
        dmFocAnnualAmpVO.setGroupCode("544511");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0%");
        dmFocAnnualAmpVO2.setStatusCode("7");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO2.setGroupLevel("LV1");
        dmFocAnnualAmpVO2.setGroupCode("61512");
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0%");
        dmFocAnnualAmpVO3.setStatusCode("7");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO3.setGroupLevel("LV1");
        dmFocAnnualAmpVO3.setGroupCode("45545");
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost29Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setGranularityType("U");
        annualAnalysisVO.setViewFlag("4");
        List<String> list = new ArrayList<>();
        list.add("1175D");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0%");
        dmFocAnnualAmpVO.setStatusCode("7");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0%");
        dmFocAnnualAmpVO2.setStatusCode("7");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0%");
        dmFocAnnualAmpVO3.setStatusCode("7");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost30Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("LV1");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setGranularityType("U");
        annualAnalysisVO.setViewFlag("4");
        List<String> list = new ArrayList<>();
        List<String> teamCodelist = new ArrayList<>();
        teamCodelist.add("10011");
        annualAnalysisVO.setTeamCodeList(teamCodelist);
        list.add("1175D_##天空蓝");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0%");
        dmFocAnnualAmpVO.setStatusCode("7");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0%");
        dmFocAnnualAmpVO2.setStatusCode("7");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0%");
        dmFocAnnualAmpVO3.setStatusCode("7");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost31Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("LV1");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsMultipleSelect(false);
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setGranularityType("U");
        annualAnalysisVO.setViewFlag("4");
        List<String> list = new ArrayList<>();
        list.add("1175D_##null");
        annualAnalysisVO.setGroupCodeList(list);
        List<String> teamCodelist = new ArrayList<>();
        teamCodelist.add("10011");
        annualAnalysisVO.setTeamCodeList(teamCodelist);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0%");
        dmFocAnnualAmpVO.setStatusCode("7");
        dmFocAnnualAmpVO.setGroupCnName("元器件");
        dmFocAnnualAmpVO.setCustomCnName("海鲜类");
        dmFocAnnualAmpVO.setGroupLevel("LV1");
        dmFocAnnualAmpVO.setGroupCode("1216541");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0%");
        dmFocAnnualAmpVO2.setStatusCode("7");
        dmFocAnnualAmpVO2.setGroupCnName("元器件");
        dmFocAnnualAmpVO2.setCustomCnName("海鲜类");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO2.setGroupLevel("LV1");
        dmFocAnnualAmpVO2.setGroupCode("40477");
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0%");
        dmFocAnnualAmpVO3.setStatusCode("7");
        dmFocAnnualAmpVO3.setGroupCnName("元器件");
        dmFocAnnualAmpVO3.setCustomCnName("海鲜类");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO3.setGroupLevel("LV1");
        dmFocAnnualAmpVO3.setGroupCode("121541");
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCombCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost32Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setGranularityType("P");
        annualAnalysisVO.setViewFlag("4");
        annualAnalysisVO.setMultiLevel("L1");
        List<String> list = new ArrayList<>();
        list.add("1175D_##null");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0%");
        dmFocAnnualAmpVO.setStatusCode("7");
        dmFocAnnualAmpVO.setGroupCnName("元器件");
        dmFocAnnualAmpVO.setCustomCnName("海鲜类");
        dmFocAnnualAmpVO.setGroupLevel("LV1");
        dmFocAnnualAmpVO.setGroupCode("1216541");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0%");
        dmFocAnnualAmpVO2.setStatusCode("7");
        dmFocAnnualAmpVO2.setGroupCnName("元器件");
        dmFocAnnualAmpVO2.setCustomCnName("海鲜类");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO2.setGroupLevel("LV1");
        dmFocAnnualAmpVO2.setGroupCode("44777");
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0%");
        dmFocAnnualAmpVO3.setStatusCode("7");
        dmFocAnnualAmpVO3.setGroupCnName("元器件");
        dmFocAnnualAmpVO3.setCustomCnName("海鲜类");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO3.setGroupLevel("LV1");
        dmFocAnnualAmpVO3.setGroupCode("65454");
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCombCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost33Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setGranularityType("P");
        annualAnalysisVO.setViewFlag("4");
        annualAnalysisVO.setMultiLevel("L2");
        List<String> list = new ArrayList<>();
        list.add("1175D_##null");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0%");
        dmFocAnnualAmpVO.setStatusCode("7");
        dmFocAnnualAmpVO.setGroupCnName("元器件");
        dmFocAnnualAmpVO.setCustomCnName("海鲜类");
        dmFocAnnualAmpVO.setL1Name("元气弹");
        dmFocAnnualAmpVO.setL2Name("元气弹");
        dmFocAnnualAmpVO.setGroupLevel("LV1");
        dmFocAnnualAmpVO.setGroupCode("12141");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0%");
        dmFocAnnualAmpVO2.setStatusCode("7");
        dmFocAnnualAmpVO2.setGroupCnName("元器件");
        dmFocAnnualAmpVO2.setCustomCnName("海鲜类");
        dmFocAnnualAmpVO2.setL1Name("元气弹");
        dmFocAnnualAmpVO2.setL2Name("天线");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO2.setGroupLevel("LV1");
        dmFocAnnualAmpVO2.setGroupCode("45445");
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0%");
        dmFocAnnualAmpVO3.setStatusCode("7");
        dmFocAnnualAmpVO3.setGroupCnName("元器件");
        dmFocAnnualAmpVO3.setCustomCnName("海鲜类");
        dmFocAnnualAmpVO3.setL1Name("元气弹");
        dmFocAnnualAmpVO3.setL2Name("天线");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO3.setGroupLevel("LV1");
        dmFocAnnualAmpVO3.setGroupCode("64456");
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCombCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost34Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setGranularityType("D");
        annualAnalysisVO.setViewFlag("4");
        List<String> list = new ArrayList<>();
        list.add("1175D_##null");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0%");
        dmFocAnnualAmpVO.setStatusCode("7");
        dmFocAnnualAmpVO.setGroupCnName("元器件");
        dmFocAnnualAmpVO.setCustomCnName("海鲜类");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO.setGroupLevel("LV1");
        dmFocAnnualAmpVO.setGroupCode("1216541");
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0%");
        dmFocAnnualAmpVO2.setStatusCode("7");
        dmFocAnnualAmpVO2.setGroupCnName("元器件");
        dmFocAnnualAmpVO2.setCustomCnName("海鲜类");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO2.setGroupLevel("LV1");
        dmFocAnnualAmpVO2.setGroupCode("1216541");
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0%");
        dmFocAnnualAmpVO3.setStatusCode("7");
        dmFocAnnualAmpVO3.setGroupCnName("元器件");
        dmFocAnnualAmpVO3.setCustomCnName("海鲜类");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO3.setGroupLevel("LV1");
        dmFocAnnualAmpVO3.setGroupCode("1216541");
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCombCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void allIndustryCost35Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setCostType("T");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setGranularityType("D");
        annualAnalysisVO.setViewFlag("4");
        List<String> list = new ArrayList<>();
        list.add("1175D_##null");
        annualAnalysisVO.setGroupCodeList(list);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0%");
        dmFocAnnualAmpVO.setStatusCode("7");
        dmFocAnnualAmpVO.setGroupCnName("元器件");
        dmFocAnnualAmpVO.setCustomCnName("海鲜类");
        List<String> threeYears = getThreeYears();
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("2.0%");
        dmFocAnnualAmpVO2.setStatusCode("7");
        dmFocAnnualAmpVO2.setGroupCnName("元器件");
        dmFocAnnualAmpVO2.setCustomCnName("海鲜类");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("3.0%");
        dmFocAnnualAmpVO3.setStatusCode("7");
        dmFocAnnualAmpVO3.setGroupCnName("元器件");
        dmFocAnnualAmpVO3.setCustomCnName("海鲜类");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO2);
        dmFocAnnualAmpVOList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCost(annualAnalysisVO);
        PowerMockito.doReturn(dmFocAnnualAmpVOList).when(annualAmpDao).allIndustryCombCost(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.allIndustryCost(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void multiIndustryCostChart() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGroupLevel("ICT");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setIsContainComb(false);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<String> threeYears = getThreeYears();
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = new ArrayList();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0");
        dmFocAnnualAmpVO.setWeightRate(null);
        dmFocAnnualAmpVO.setGroupCode("code1");
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVOResult.add(dmFocAnnualAmpVO);
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("1.0");
        dmFocAnnualAmpVO2.setWeightRate("2.0");
        dmFocAnnualAmpVO2.setGroupCode("code1");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVOResult.add(dmFocAnnualAmpVO2);
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("1.0");
        dmFocAnnualAmpVO3.setWeightRate("3.0");
        dmFocAnnualAmpVO3.setGroupCode("code1");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVOResult.add(dmFocAnnualAmpVO3);
        DmFocAnnualAmpVO dmFocAnnualAmpVO5 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO5.setAnnualAmp("1.0");
        dmFocAnnualAmpVO5.setWeightRate("4.0");
        dmFocAnnualAmpVO5.setGroupCode("code");
        dmFocAnnualAmpVO5.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVOResult.add(dmFocAnnualAmpVO5);
        DmFocAnnualAmpVO dmFocAnnualAmpVO6 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO6.setAnnualAmp("1.0");
        dmFocAnnualAmpVO6.setWeightRate("5.0");
        dmFocAnnualAmpVO6.setGroupCode("code");
        dmFocAnnualAmpVO6.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVOResult.add(dmFocAnnualAmpVO6);
        PowerMockito.doReturn(dmFocAnnualAmpVOResult).when(annualAmpDao).multiIndustryCostChart(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.multiIndustryCostChart(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void multiIndustryCostChart0Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setGroupLevel("ICT");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setIsContainComb(false);

        ResultDataVO resultDataVO = annualAmpService.multiIndustryCostChart(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void multiIndustryCostChart01Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setGroupLevel("ICT");
        annualAnalysisVO.setCostType("P");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setIsContainComb(false);
        List<String> list = new ArrayList<>();
        list.add("1111");
        annualAnalysisVO.setSubGroupCodeList(list);

        ResultDataVO resultDataVO = annualAmpService.multiIndustryCostChart(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void multiIndustryCostChart2Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(3);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGroupLevel("CATEGORY");
        annualAnalysisVO.setCostType("P");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(true);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(annualAnalysisVO.getCostType(),annualAnalysisVO.getIndustryOrg());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = getDmFocAnnualAmpVOS(threeYears);
        PowerMockito.doReturn(dmFocAnnualAmpVOResult).when(annualAmpDao).multiIndustryCostChart(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.multiIndustryCostChart(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @NotNull
    private List<DmFocAnnualAmpVO> getDmFocAnnualAmpVOS(List<String> threeYears) {
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = new ArrayList();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0");
        dmFocAnnualAmpVO.setWeightRate("1.0");
        dmFocAnnualAmpVO.setGroupCode("code1");
        dmFocAnnualAmpVO.setGroupCnName("code1");
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVOResult.add(dmFocAnnualAmpVO);
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("1.0");
        dmFocAnnualAmpVO2.setWeightRate("1.0");
        dmFocAnnualAmpVO2.setGroupCode("code1");
        dmFocAnnualAmpVO2.setGroupCnName("code1");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVOResult.add(dmFocAnnualAmpVO2);
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("1.0");
        dmFocAnnualAmpVO3.setWeightRate(null);
        dmFocAnnualAmpVO3.setGroupCode("code1");
        dmFocAnnualAmpVO3.setGroupCnName("code1");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVOResult.add(dmFocAnnualAmpVO3);
        DmFocAnnualAmpVO dmFocAnnualAmpVO5 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO5.setAnnualAmp("1.0");
        dmFocAnnualAmpVO5.setWeightRate("2.0");
        dmFocAnnualAmpVO5.setGroupCode("ddd");
        dmFocAnnualAmpVO5.setGroupCnName("多多");
        dmFocAnnualAmpVO5.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVOResult.add(dmFocAnnualAmpVO5);
        DmFocAnnualAmpVO dmFocAnnualAmpVO6 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO6.setAnnualAmp("1.0");
        dmFocAnnualAmpVO6.setWeightRate("0*");
        dmFocAnnualAmpVO6.setGroupCode("ddd");
        dmFocAnnualAmpVO6.setGroupCnName("多多");
        dmFocAnnualAmpVO6.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVOResult.add(dmFocAnnualAmpVO6);
        DmFocAnnualAmpVO dmFocAnnualAmpVO7 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO7.setAnnualAmp("1.0");
        dmFocAnnualAmpVO7.setWeightRate("2.0");
        dmFocAnnualAmpVO7.setGroupCode("aa");
        dmFocAnnualAmpVO7.setGroupCnName("啊啊");
        dmFocAnnualAmpVO7.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVOResult.add(dmFocAnnualAmpVO7);
        DmFocAnnualAmpVO dmFocAnnualAmpVO8 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO8.setAnnualAmp("1.0");
        dmFocAnnualAmpVO8.setWeightRate("2.0");
        dmFocAnnualAmpVO8.setGroupCode("aa");
        dmFocAnnualAmpVO8.setGroupCnName("啊啊");
        dmFocAnnualAmpVO8.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVOResult.add(dmFocAnnualAmpVO8);
        DmFocAnnualAmpVO dmFocAnnualAmpVO9 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO9.setAnnualAmp("1.0");
        dmFocAnnualAmpVO9.setWeightRate("0.1");
        dmFocAnnualAmpVO9.setGroupCode("aa");
        dmFocAnnualAmpVO9.setGroupCnName("啊啊");
        dmFocAnnualAmpVO9.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVOResult.add(dmFocAnnualAmpVO9);
        DmFocAnnualAmpVO dmFocAnnualAmpVO10 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO10.setAnnualAmp("1.0");
        dmFocAnnualAmpVO10.setWeightRate("1.0");
        dmFocAnnualAmpVO10.setGroupCode("hh");
        dmFocAnnualAmpVO10.setGroupCnName("哈哈");
        dmFocAnnualAmpVO10.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVOResult.add(dmFocAnnualAmpVO10);
        DmFocAnnualAmpVO dmFocAnnualAmpVO11 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO11.setAnnualAmp("1.0");
        dmFocAnnualAmpVO11.setWeightRate("2.0");
        dmFocAnnualAmpVO11.setGroupCode("hh");
        dmFocAnnualAmpVO11.setGroupCnName("哈哈");
        dmFocAnnualAmpVO11.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVOResult.add(dmFocAnnualAmpVO11);
        DmFocAnnualAmpVO dmFocAnnualAmpVO12 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO12.setAnnualAmp("1.0");
        dmFocAnnualAmpVO12.setWeightRate("0.1");
        dmFocAnnualAmpVO12.setGroupCode("hh");
        dmFocAnnualAmpVO12.setGroupCnName("哈哈");
        dmFocAnnualAmpVO12.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVOResult.add(dmFocAnnualAmpVO12);
        return dmFocAnnualAmpVOResult;
    }

    @Test
    public void multiIndustryCostChart3Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGroupLevel("ICT");
        annualAnalysisVO.setCostType("P");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setIsMultipleSelect(false);
        List<String> list = new ArrayList<>();
        list.add("1111");
        annualAnalysisVO.setSubGroupCodeList(list);
        annualAnalysisVO.setReverseLv1Flag(true);

        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(annualAnalysisVO.getCostType(),annualAnalysisVO.getIndustryOrg());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = new ArrayList();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp(null);
        dmFocAnnualAmpVO.setWeightRate("1.0");
        dmFocAnnualAmpVO.setGroupCode("code1");
        dmFocAnnualAmpVO.setGroupCnName("code1");
        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVOResult.add(dmFocAnnualAmpVO);

        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("0*");
        dmFocAnnualAmpVO2.setWeightRate("1.0");
        dmFocAnnualAmpVO2.setGroupCode("code1");
        dmFocAnnualAmpVO2.setGroupCnName("code1");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVOResult.add(dmFocAnnualAmpVO2);

        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("1.0");
        dmFocAnnualAmpVO3.setWeightRate("0.1");
        dmFocAnnualAmpVO3.setGroupCode("code1");
        dmFocAnnualAmpVO3.setGroupCnName("code1");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVOResult.add(dmFocAnnualAmpVO3);

        DmFocAnnualAmpVO dmFocAnnualAmpVO5 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO5.setAnnualAmp("1.0");
        dmFocAnnualAmpVO5.setWeightRate("0*");
        dmFocAnnualAmpVO5.setGroupCode("ddd");
        dmFocAnnualAmpVO5.setGroupCnName("多多");
        dmFocAnnualAmpVO5.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVOResult.add(dmFocAnnualAmpVO5);

        DmFocAnnualAmpVO dmFocAnnualAmpVO6 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO6.setAnnualAmp("1.0");
        dmFocAnnualAmpVO6.setWeightRate(null);
        dmFocAnnualAmpVO6.setGroupCode("ddd");
        dmFocAnnualAmpVO6.setGroupCnName("多多");
        dmFocAnnualAmpVO6.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVOResult.add(dmFocAnnualAmpVO6);

        PowerMockito.doReturn(dmFocAnnualAmpVOResult).when(annualAmpDao).multiIndustryCostChart(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.multiIndustryCostChart(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void multiIndustryCostChart4Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setCostType("P");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setGranularityType("U");
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setIsMultipleSelect(true);
        List<String> list = new ArrayList<>();
        list.add("1111");
        annualAnalysisVO.setSubGroupCodeList(list);
        annualAnalysisVO.setReverseLv1Flag(true);

        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = new ArrayList();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("lala");
        dmFocAnnualAmpVO.setGroupCode("1196F");
        dmFocAnnualAmpVOResult.add(dmFocAnnualAmpVO);
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(annualAnalysisVO.getCostType(),annualAnalysisVO.getIndustryOrg());
        PowerMockito.doReturn(dmFocAnnualAmpVOResult).when(annualAmpDao).multiIndustryCostChart(annualAnalysisVO);
        PowerMockito.doReturn(dmFocAnnualAmpVOResult).when(annualAmpDao).multiIndustryCostChartMultiSelect(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.multiIndustryCostChart(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void multiIndustryCostChart5Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setCostType("P");
        List<String> groupCodeList = new ArrayList<>();
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setIsMultipleSelect(false);
        List<String> list = new ArrayList<>();
        list.add("1111");
        annualAnalysisVO.setSubGroupCodeList(list);
        annualAnalysisVO.setReverseLv1Flag(true);
        annualAnalysisVO.setGranularityType("P");

        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = new ArrayList();

        PowerMockito.doReturn(dmFocAnnualAmpVOResult).when(annualAmpDao).multiIndustryCostChart(annualAnalysisVO);
        // 计算最近的三年
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(annualAnalysisVO.getCostType(),annualAnalysisVO.getIndustryOrg());
        ResultDataVO resultDataVO = annualAmpService.multiIndustryCostChart(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void multiIndustryCostChart6Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag(null);
        annualAnalysisVO.setGroupLevel("CEG");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setIsMultipleSelect(false);
        List<String> list = new ArrayList<>();
        list.add("1111");
        annualAnalysisVO.setSubGroupCodeList(list);
        annualAnalysisVO.setReverseLv1Flag(true);
        annualAnalysisVO.setGranularityType("D");

        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = new ArrayList();

        PowerMockito.doReturn(dmFocAnnualAmpVOResult).when(annualAmpDao).multiIndustryCostChart(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.multiIndustryCostChart(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void multiIndustryCostChart7Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setCostType("P");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setIsMultipleSelect(false);
        List<String> list = new ArrayList<>();
        list.add("1111");
        annualAnalysisVO.setSubGroupCodeList(list);
        annualAnalysisVO.setReverseLv1Flag(true);

        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> groupCodeOrderList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setGroupCode("code1");
        dmFocAnnualAmpVO.setWeightRate("2.0");
        dmFocAnnualAmpVO.setGroupCnName("tyngn");
        groupCodeOrderList.add(dmFocAnnualAmpVO);
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setGroupCode("code2");
        dmFocAnnualAmpVO2.setWeightRate("1.2");
        dmFocAnnualAmpVO2.setGroupCnName("fdgtyngn");
        groupCodeOrderList.add(dmFocAnnualAmpVO2);

        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setGroupCode("code3");
        dmFocAnnualAmpVO3.setWeightRate("1.2");
        dmFocAnnualAmpVO3.setGroupCnName("dfdsttyngn");
        groupCodeOrderList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(groupCodeOrderList).when(annualAmpDao).findGroupCodeOrderByWeight(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(annualAnalysisVO.getCostType(),annualAnalysisVO.getIndustryOrg());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = new ArrayList();
        PowerMockito.doReturn(dmFocAnnualAmpVOResult).when(annualAmpDao).multiIndustryCostChart(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.multiIndustryCostChart(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void multiIndustryCostChart8Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(2);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setCostType("P");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setIsMultipleSelect(false);
        List<String> list = new ArrayList<>();
        list.add("1111");
        annualAnalysisVO.setSubGroupCodeList(list);
        annualAnalysisVO.setReverseLv1Flag(true);

        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> groupCodeOrderList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setGroupCode("code1");
        dmFocAnnualAmpVO.setWeightRate("2.0");
        groupCodeOrderList.add(dmFocAnnualAmpVO);
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setGroupCode("code2");
        dmFocAnnualAmpVO2.setWeightRate("1.2");
        groupCodeOrderList.add(dmFocAnnualAmpVO2);
        PowerMockito.doReturn(groupCodeOrderList).when(annualAmpDao).findGroupCodeOrderByWeight(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(annualAnalysisVO.getCostType(),annualAnalysisVO.getIndustryOrg());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = new ArrayList();
        PowerMockito.doReturn(dmFocAnnualAmpVOResult).when(annualAmpDao).multiIndustryCostChart(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.multiIndustryCostChart(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);

        annualAnalysisVO.setIsContainComb(true);
        resultDataVO = annualAmpService.multiIndustryCostChart(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void multiIndustryCostChart9Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(2);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setCostType("T");
        annualAnalysisVO.setCostSubType("T");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setIsMultipleSelect(false);
        List<String> list = new ArrayList<>();
        list.add("1111");
        annualAnalysisVO.setSubGroupCodeList(list);
        annualAnalysisVO.setReverseLv1Flag(true);

        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> groupCodeOrderList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setGroupCode("code1");
        dmFocAnnualAmpVO.setWeightRate("2.0");
        groupCodeOrderList.add(dmFocAnnualAmpVO);
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setGroupCode("code2");
        dmFocAnnualAmpVO2.setWeightRate("1.2");
        groupCodeOrderList.add(dmFocAnnualAmpVO2);
        PowerMockito.doReturn(groupCodeOrderList).when(annualAmpDao).findGroupCodeOrderByWeight(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(annualAnalysisVO.getCostType(),annualAnalysisVO.getIndustryOrg());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = new ArrayList();
        PowerMockito.doReturn(dmFocAnnualAmpVOResult).when(annualAmpDao).multiIndustryCostChart(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.multiIndustryCostChart(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);

        PowerMockito.doReturn(dmFocAnnualAmpVOResult).when(totalAnnualAmpDao).findTotalGroupCodeOrderByWeight(annualAnalysisVO);

        annualAnalysisVO.setIsContainComb(true);
        resultDataVO = annualAmpService.multiIndustryCostChart(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void multiIndustryCostChart10Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(2);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setCostType("T");
        annualAnalysisVO.setCostSubType("M");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setIsMultipleSelect(false);
        List<String> list = new ArrayList<>();
        list.add("1111");
        annualAnalysisVO.setSubGroupCodeList(list);
        annualAnalysisVO.setReverseLv1Flag(true);

        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> groupCodeOrderList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setGroupCode("code1");
        dmFocAnnualAmpVO.setWeightRate("2.0");
        groupCodeOrderList.add(dmFocAnnualAmpVO);
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setGroupCode("code2");
        dmFocAnnualAmpVO2.setWeightRate("1.2");
        groupCodeOrderList.add(dmFocAnnualAmpVO2);
        PowerMockito.doReturn(groupCodeOrderList).when(annualAmpDao).findGroupCodeOrderByWeight(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(annualAnalysisVO.getCostType(),annualAnalysisVO.getIndustryOrg());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = new ArrayList();
        PowerMockito.doReturn(dmFocAnnualAmpVOResult).when(annualAmpDao).multiIndustryCostChart(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.multiIndustryCostChart(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);

        PowerMockito.doReturn(dmFocAnnualAmpVOResult).when(madeAnnualAmpDao).findMadeGroupCodeOrderByWeight(annualAnalysisVO);

        annualAnalysisVO.setIsContainComb(true);
        resultDataVO = annualAmpService.multiIndustryCostChart(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void multiIndustryCostChart11Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(2);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setCostType("T");
        annualAnalysisVO.setCostSubType("P");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setIsMultipleSelect(false);
        List<String> list = new ArrayList<>();
        list.add("1111");
        annualAnalysisVO.setSubGroupCodeList(list);
        annualAnalysisVO.setReverseLv1Flag(true);

        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> groupCodeOrderList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setGroupCode("code1");
        dmFocAnnualAmpVO.setWeightRate("2.0");
        groupCodeOrderList.add(dmFocAnnualAmpVO);
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setGroupCode("code2");
        dmFocAnnualAmpVO2.setWeightRate("1.2");
        groupCodeOrderList.add(dmFocAnnualAmpVO2);
        PowerMockito.doReturn(groupCodeOrderList).when(annualAmpDao).findGroupCodeOrderByWeight(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(annualAnalysisVO.getCostType(),annualAnalysisVO.getIndustryOrg());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = new ArrayList();
        PowerMockito.doReturn(dmFocAnnualAmpVOResult).when(annualAmpDao).multiIndustryCostChart(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.multiIndustryCostChart(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);

        PowerMockito.doReturn(dmFocAnnualAmpVOResult).when(annualAmpDao).findGroupCodeOrderByWeight(annualAnalysisVO);

        annualAnalysisVO.setIsContainComb(true);
        resultDataVO = annualAmpService.multiIndustryCostChart(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void multiIndustryCostChart12Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(2);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setCostType("M");
        annualAnalysisVO.setCostSubType("P");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setIsMultipleSelect(false);
        List<String> list = new ArrayList<>();
        list.add("1111");
        annualAnalysisVO.setSubGroupCodeList(list);
        annualAnalysisVO.setReverseLv1Flag(true);

        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> groupCodeOrderList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setGroupCode("code1");
        dmFocAnnualAmpVO.setWeightRate("2.0");
        groupCodeOrderList.add(dmFocAnnualAmpVO);
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setGroupCode("code2");
        dmFocAnnualAmpVO2.setWeightRate("1.2");
        groupCodeOrderList.add(dmFocAnnualAmpVO2);
        PowerMockito.doReturn(groupCodeOrderList).when(annualAmpDao).findGroupCodeOrderByWeight(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(annualAnalysisVO.getCostType(),annualAnalysisVO.getIndustryOrg());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = new ArrayList();
        PowerMockito.doReturn(dmFocAnnualAmpVOResult).when(annualAmpDao).multiIndustryCostChart(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.multiIndustryCostChart(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);

        PowerMockito.doReturn(dmFocAnnualAmpVOResult).when(madeAnnualAmpDao).findMadeGroupCodeOrderByWeight(annualAnalysisVO);

        annualAnalysisVO.setIsContainComb(true);
        resultDataVO = annualAmpService.multiIndustryCostChart(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void multiIndustryCostChart13Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(2);
        annualAnalysisVO.setViewFlag("4");
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setCostType("M");
        annualAnalysisVO.setCostSubType("P");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setIsMultipleSelect(false);
        List<String> list = new ArrayList<>();
        list.add("1111");
        annualAnalysisVO.setSubGroupCodeList(list);
        annualAnalysisVO.setReverseLv1Flag(true);
        annualAnalysisVO.setGranularityType("U");

        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> groupCodeOrderList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setGroupCode("code1");
        dmFocAnnualAmpVO.setWeightRate("2.0");
        groupCodeOrderList.add(dmFocAnnualAmpVO);
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setGroupCode("code2");
        dmFocAnnualAmpVO2.setWeightRate("1.2");
        groupCodeOrderList.add(dmFocAnnualAmpVO2);
        PowerMockito.doReturn(groupCodeOrderList).when(annualAmpDao).findGroupCodeOrderByWeight(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(annualAnalysisVO.getCostType(),annualAnalysisVO.getIndustryOrg());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = new ArrayList();
        PowerMockito.doReturn(dmFocAnnualAmpVOResult).when(annualAmpDao).multiIndustryCostChart(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.multiIndustryCostChart(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);

        PowerMockito.doReturn(dmFocAnnualAmpVOResult).when(madeAnnualAmpDao).findMadeRevGroupCodeOrderByWeight(annualAnalysisVO);

        annualAnalysisVO.setIsContainComb(true);
        resultDataVO = annualAmpService.multiIndustryCostChart(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void multiIndustryCostChart14Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(2);
        annualAnalysisVO.setViewFlag("4");
        annualAnalysisVO.setGroupLevel("LV2");
        annualAnalysisVO.setCostType("M");
        annualAnalysisVO.setCostSubType("P");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setIsMultipleSelect(true);
        List<String> list = new ArrayList<>();
        list.add("1111");
        annualAnalysisVO.setSubGroupCodeList(list);
        annualAnalysisVO.setReverseLv1Flag(true);
        annualAnalysisVO.setGranularityType("U");

        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> groupCodeOrderList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setGroupCode("code1");
        dmFocAnnualAmpVO.setWeightRate("2.0");
        groupCodeOrderList.add(dmFocAnnualAmpVO);
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setGroupCode("code2");
        dmFocAnnualAmpVO2.setWeightRate("1.2");
        groupCodeOrderList.add(dmFocAnnualAmpVO2);
        PowerMockito.doReturn(groupCodeOrderList).when(annualAmpDao).findGroupCodeOrderByWeight(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(annualAnalysisVO.getCostType(),annualAnalysisVO.getIndustryOrg());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = new ArrayList();
        List<DmFocViewInfoVO> itemList = new ArrayList<>();

        PowerMockito.doReturn(dmFocAnnualAmpVOResult).when(annualAmpDao).multiIndustryCostChart(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.multiIndustryCostChart(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);

        PowerMockito.doReturn(itemList).when(dmFocMadeViewInfoDao).getMadeAllNormalItemCode(annualAnalysisVO);

        annualAnalysisVO.setIsContainComb(true);
        resultDataVO = annualAmpService.multiIndustryCostChart(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void multiIndustryCostChart15Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(2);
        annualAnalysisVO.setViewFlag("4");
        annualAnalysisVO.setGroupLevel("LV2");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setCostSubType("P");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setIsMultipleSelect(true);
        List<String> list = new ArrayList<>();
        list.add("1111");
        annualAnalysisVO.setSubGroupCodeList(list);
        annualAnalysisVO.setReverseLv1Flag(true);
        annualAnalysisVO.setGranularityType("U");

        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        List<DmFocAnnualAmpVO> groupCodeOrderList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setGroupCode("code1");
        dmFocAnnualAmpVO.setWeightRate("2.0");
        groupCodeOrderList.add(dmFocAnnualAmpVO);
        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setGroupCode("code2");
        dmFocAnnualAmpVO2.setWeightRate("1.2");
        groupCodeOrderList.add(dmFocAnnualAmpVO2);
        PowerMockito.doReturn(groupCodeOrderList).when(annualAmpDao).findGroupCodeOrderByWeight(any());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(annualAnalysisVO.getCostType(),annualAnalysisVO.getIndustryOrg());
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = new ArrayList();
        List<DmFocViewInfoVO> itemList = new ArrayList<>();

        PowerMockito.doReturn(dmFocAnnualAmpVOResult).when(annualAmpDao).multiIndustryCostChart(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.multiIndustryCostChart(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);

        PowerMockito.doReturn(itemList).when(dmFocMadeViewInfoDao).getMadeAllNormalItemCode(annualAnalysisVO);

        annualAnalysisVO.setIsContainComb(true);
        resultDataVO = annualAmpService.multiIndustryCostChart(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void setMutliSearchParamsVO0Test() throws Exception {
        JSONObject arg = TestUtils.getTestArg("/com/huawei/it/fcst/industry/index/impl/annual/AnnualAmpService/allIndustryCost.json");
        DmFocVersionInfoDTO dmFocVersionInfoDTO = JSONObject.parseObject(arg.getString("DmFocVersionInfoDTO"),DmFocVersionInfoDTO.class);
        AnnualAnalysisVO annualAnalysisVO = JSONObject.parseObject(arg.getString("AnnualAnalysisVO"),AnnualAnalysisVO.class);
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(annualAnalysisVO.getCostType(),annualAnalysisVO.getIndustryOrg());
        PowerMockito.when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(dmFocVersionInfoDTO);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGroupLevel("ITEM");
        Whitebox.invokeMethod(annualAmpService, "setMutliSearchParamsVO", annualAnalysisVO);
        assertThatNoException();
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setGroupLevel("CATEGORY");
        Whitebox.invokeMethod(annualAmpService, "setMutliSearchParamsVO", annualAnalysisVO);
        assertThatNoException();
        annualAnalysisVO.setGroupLevel("MODL");
        Whitebox.invokeMethod(annualAmpService, "setMutliSearchParamsVO", annualAnalysisVO);
        assertThatNoException();
        annualAnalysisVO.setGroupLevel("CEG");
        Whitebox.invokeMethod(annualAmpService, "setMutliSearchParamsVO", annualAnalysisVO);
        assertThatNoException();
    }

    @Test
    public void industryCostList0Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGroupLevel("CEG");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> threeYears = getThreeYears();
        annualAnalysisVO.setYear(threeYears.get(0));
        annualAnalysisVO.setIsContainComb(false);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = new ArrayList();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp(null);
        dmFocAnnualAmpVO.setWeightRate(null);
        dmFocAnnualAmpVO.setGroupCode("code1");
        dmFocAnnualAmpVO.setGroupCnName("code1");
        dmFocAnnualAmpVO.setStatusCode("2");

        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO);

        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("0*");
        dmFocAnnualAmpVO2.setWeightRate(null);
        dmFocAnnualAmpVO2.setGroupCode("code1");
        dmFocAnnualAmpVO2.setGroupCnName("code1");
        dmFocAnnualAmpVO2.setStatusCode("2");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO2.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO2);

        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("1.0");
        dmFocAnnualAmpVO3.setWeightRate(null);
        dmFocAnnualAmpVO3.setGroupCode("code2");
        dmFocAnnualAmpVO3.setGroupCnName("code2");
        dmFocAnnualAmpVO3.setStatusCode("3");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO3.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO3);

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        ResultDataVO resultDataVO = annualAmpService.industryCostList(annualAnalysisVO);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void industryCostListTest() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGroupLevel("CEG");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> threeYears = getThreeYears();
        annualAnalysisVO.setYear(threeYears.get(0));
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(true);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = new ArrayList();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp(null);
        dmFocAnnualAmpVO.setWeightRate(null);
        dmFocAnnualAmpVO.setGroupCode("code1");
        dmFocAnnualAmpVO.setGroupCnName("code1");
        dmFocAnnualAmpVO.setStatusCode("2");

        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO);

        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("0*");
        dmFocAnnualAmpVO2.setWeightRate(null);
        dmFocAnnualAmpVO2.setGroupCode("code1");
        dmFocAnnualAmpVO2.setGroupCnName("code1");
        dmFocAnnualAmpVO2.setStatusCode("2");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO2.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO2);

        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("1.0");
        dmFocAnnualAmpVO3.setWeightRate(null);
        dmFocAnnualAmpVO3.setGroupCode("code2");
        dmFocAnnualAmpVO3.setGroupCnName("code2");
        dmFocAnnualAmpVO3.setStatusCode("3");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO3.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO3);
        PageVO pageVO = new PageVO();
        pageVO.setPageSize(10);
        PagedResult<DmFocAnnualAmpVO> PagedResult=new PagedResult<>();
        PagedResult.setPageVO(pageVO);
        PagedResult.setResult(dmFocAnnualAmpVOPagedResult);

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        PowerMockito.doReturn(PagedResult).when(annualAmpDao).industryCombCharPage(annualAnalysisVO,pageVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.industryCostList(annualAnalysisVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void industryCostList3Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setOrderColumn("weightRate");
        annualAnalysisVO.setOrderMethod("asc");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> threeYears = getThreeYears();
        annualAnalysisVO.setYear(threeYears.get(0));
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = new ArrayList();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp(null);
        dmFocAnnualAmpVO.setWeightRate("1.0");
        dmFocAnnualAmpVO.setGroupCode("code1");
        dmFocAnnualAmpVO.setGroupCnName("code1");
        dmFocAnnualAmpVO.setStatusCode("2");

        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO);

        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("0*");
        dmFocAnnualAmpVO2.setWeightRate("0.8");
        dmFocAnnualAmpVO2.setGroupCode("code1");
        dmFocAnnualAmpVO2.setGroupCnName("code1");
        dmFocAnnualAmpVO2.setStatusCode("2");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO2.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO2);

        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("1.0");
        dmFocAnnualAmpVO3.setWeightRate("1.0");
        dmFocAnnualAmpVO3.setGroupCode("code2");
        dmFocAnnualAmpVO3.setGroupCnName("code2");
        dmFocAnnualAmpVO3.setStatusCode("3");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO3.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO3);

        DmFocAnnualAmpVO dmFocAnnualAmpVO4 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO4.setAnnualAmp("1.0");
        dmFocAnnualAmpVO4.setWeightRate("0*");
        dmFocAnnualAmpVO4.setGroupCode("code2");
        dmFocAnnualAmpVO4.setGroupCnName("code2");
        dmFocAnnualAmpVO4.setStatusCode("3");
        dmFocAnnualAmpVO4.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO4.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO4);

        DmFocAnnualAmpVO dmFocAnnualAmpVO5 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO5.setAnnualAmp("1.0");
        dmFocAnnualAmpVO5.setWeightRate(null);
        dmFocAnnualAmpVO5.setGroupCode("code3");
        dmFocAnnualAmpVO5.setGroupCnName("code3");
        dmFocAnnualAmpVO5.setStatusCode("4");
        dmFocAnnualAmpVO5.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO5.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO5);

        DmFocAnnualAmpVO dmFocAnnualAmpVO6 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO6.setAnnualAmp("1.0");
        dmFocAnnualAmpVO6.setWeightRate("0*");
        dmFocAnnualAmpVO6.setGroupCode("code4");
        dmFocAnnualAmpVO6.setGroupCnName("code4");
        dmFocAnnualAmpVO6.setStatusCode("3");
        dmFocAnnualAmpVO6.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO6.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO6);

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.industryCostList(annualAnalysisVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void industryCostList4Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setOrderColumn("weightRate");
        annualAnalysisVO.setOrderMethod("desc");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> threeYears = getThreeYears();
        annualAnalysisVO.setYear(threeYears.get(0));
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = getAmpVOList(threeYears);

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.industryCostList(annualAnalysisVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void industryCostList5Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setOrderColumn("groupCnName");
        annualAnalysisVO.setOrderMethod("asc");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> threeYears = getThreeYears();
        annualAnalysisVO.setYear(threeYears.get(0));
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = new ArrayList();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp(null);
        dmFocAnnualAmpVO.setWeightRate("1.0");
        dmFocAnnualAmpVO.setGroupCode("code1");
        dmFocAnnualAmpVO.setGroupCnName("中文");
        dmFocAnnualAmpVO.setStatusCode("2");

        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO);

        DmFocAnnualAmpVO dmFocAnnualAmpVO7 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO7.setAnnualAmp("1.0");
        dmFocAnnualAmpVO7.setWeightRate("1.1");
        dmFocAnnualAmpVO7.setGroupCode("code5");
        dmFocAnnualAmpVO7.setGroupCnName("会计");
        dmFocAnnualAmpVO7.setStatusCode("3");
        dmFocAnnualAmpVO7.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO7.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO7);

        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("0*");
        dmFocAnnualAmpVO2.setWeightRate("0.8");
        dmFocAnnualAmpVO2.setGroupCode("code1");
        dmFocAnnualAmpVO2.setGroupCnName("吃饭");
        dmFocAnnualAmpVO2.setStatusCode("2");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO2.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO2);

        DmFocAnnualAmpVO dmFocAnnualAmpVO5 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO5.setAnnualAmp("1.0");
        dmFocAnnualAmpVO5.setWeightRate("1.0");
        dmFocAnnualAmpVO5.setGroupCode("code3");
        dmFocAnnualAmpVO5.setGroupCnName("时间");
        dmFocAnnualAmpVO5.setStatusCode("4");
        dmFocAnnualAmpVO5.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO5.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO5);

        DmFocAnnualAmpVO dmFocAnnualAmpVO4 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO4.setAnnualAmp("1.0");
        dmFocAnnualAmpVO4.setWeightRate("0*");
        dmFocAnnualAmpVO4.setGroupCode("code2");
        dmFocAnnualAmpVO4.setGroupCnName("睡觉");
        dmFocAnnualAmpVO4.setStatusCode("3");
        dmFocAnnualAmpVO4.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO4.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO4);

        DmFocAnnualAmpVO dmFocAnnualAmpVO6 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO6.setAnnualAmp("1.0");
        dmFocAnnualAmpVO6.setWeightRate("0*");
        dmFocAnnualAmpVO6.setGroupCode("code4");
        dmFocAnnualAmpVO6.setGroupCnName("才能");
        dmFocAnnualAmpVO6.setStatusCode("3");
        dmFocAnnualAmpVO6.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO6.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO6);

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.industryCostList(annualAnalysisVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void industryCostList6Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setOrderColumn("groupCnName");
        annualAnalysisVO.setOrderMethod("desc");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> threeYears = getThreeYears();
        annualAnalysisVO.setYear(threeYears.get(0));
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = getAmpVOList(threeYears);

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.industryCostList(annualAnalysisVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @NotNull
    private List<DmFocAnnualAmpVO> getAmpVOList(List<String> threeYears) {
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = new ArrayList();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp(null);
        dmFocAnnualAmpVO.setWeightRate("1.0");
        dmFocAnnualAmpVO.setGroupCode("code1");
        dmFocAnnualAmpVO.setGroupCnName("code1");
        dmFocAnnualAmpVO.setStatusCode("2");

        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO);

        DmFocAnnualAmpVO dmFocAnnualAmpVO7 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO7.setAnnualAmp("1.0");
        dmFocAnnualAmpVO7.setWeightRate("1.1");
        dmFocAnnualAmpVO7.setGroupCode("code5");
        dmFocAnnualAmpVO7.setGroupCnName("code5");
        dmFocAnnualAmpVO7.setStatusCode("3");
        dmFocAnnualAmpVO7.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO7.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO7);

        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("0*");
        dmFocAnnualAmpVO2.setWeightRate("0.8");
        dmFocAnnualAmpVO2.setGroupCode("code1");
        dmFocAnnualAmpVO2.setGroupCnName("code1");
        dmFocAnnualAmpVO2.setStatusCode("2");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO2.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO2);

        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("1.0");
        dmFocAnnualAmpVO3.setWeightRate("1.0");
        dmFocAnnualAmpVO3.setGroupCode("code2");
        dmFocAnnualAmpVO3.setGroupCnName("code2");
        dmFocAnnualAmpVO3.setStatusCode("3");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO3.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO3);

        DmFocAnnualAmpVO dmFocAnnualAmpVO5 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO5.setAnnualAmp("1.0");
        dmFocAnnualAmpVO5.setWeightRate("1.0");
        dmFocAnnualAmpVO5.setGroupCode("code3");
        dmFocAnnualAmpVO5.setGroupCnName("code3");
        dmFocAnnualAmpVO5.setStatusCode("4");
        dmFocAnnualAmpVO5.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO5.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO5);

        DmFocAnnualAmpVO dmFocAnnualAmpVO4 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO4.setAnnualAmp("1.0");
        dmFocAnnualAmpVO4.setWeightRate("0*");
        dmFocAnnualAmpVO4.setGroupCode("code2");
        dmFocAnnualAmpVO4.setGroupCnName("code2");
        dmFocAnnualAmpVO4.setStatusCode("3");
        dmFocAnnualAmpVO4.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO4.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO4);

        DmFocAnnualAmpVO dmFocAnnualAmpVO6 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO6.setAnnualAmp("1.0");
        dmFocAnnualAmpVO6.setWeightRate("0*");
        dmFocAnnualAmpVO6.setGroupCode("code4");
        dmFocAnnualAmpVO6.setGroupCnName("code4");
        dmFocAnnualAmpVO6.setStatusCode("3");
        dmFocAnnualAmpVO6.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO6.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO6);
        return dmFocAnnualAmpVOPagedResult;
    }

    @Test
    public void industryCostList7Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setOrderColumn("groupCode");
        annualAnalysisVO.setOrderMethod("asc");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> threeYears = getThreeYears();
        annualAnalysisVO.setYear(threeYears.get(0));
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = getAnnualAmpVOList(threeYears);

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.industryCostList(annualAnalysisVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @NotNull
    private List<DmFocAnnualAmpVO> getAnnualAmpVOList(List<String> threeYears) {
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = new ArrayList();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp(null);
        dmFocAnnualAmpVO.setWeightRate("1.0");
        dmFocAnnualAmpVO.setGroupCode("code1");
        dmFocAnnualAmpVO.setGroupCnName("code1");
        dmFocAnnualAmpVO.setStatusCode("2");

        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO);

        DmFocAnnualAmpVO dmFocAnnualAmpVO7 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO7.setAnnualAmp("1.0");
        dmFocAnnualAmpVO7.setWeightRate("1.1");
        dmFocAnnualAmpVO7.setGroupCode("1111");
        dmFocAnnualAmpVO7.setGroupCnName("1111");
        dmFocAnnualAmpVO7.setStatusCode("3");
        dmFocAnnualAmpVO7.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO7.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO7);

        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("0*");
        dmFocAnnualAmpVO2.setWeightRate("0.8");
        dmFocAnnualAmpVO2.setGroupCode("code1");
        dmFocAnnualAmpVO2.setGroupCnName("code1");
        dmFocAnnualAmpVO2.setStatusCode("2");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO2.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO2);

        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("1.0");
        dmFocAnnualAmpVO3.setWeightRate("1.0");
        dmFocAnnualAmpVO3.setGroupCode("222");
        dmFocAnnualAmpVO3.setGroupCnName("222");
        dmFocAnnualAmpVO3.setStatusCode("3");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO3.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO3);

        DmFocAnnualAmpVO dmFocAnnualAmpVO5 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO5.setAnnualAmp("1.0");
        dmFocAnnualAmpVO5.setWeightRate("1.0");
        dmFocAnnualAmpVO5.setGroupCode("33333");
        dmFocAnnualAmpVO5.setGroupCnName("33333");
        dmFocAnnualAmpVO5.setStatusCode("4");
        dmFocAnnualAmpVO5.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO5.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO5);

        DmFocAnnualAmpVO dmFocAnnualAmpVO4 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO4.setAnnualAmp("1.0");
        dmFocAnnualAmpVO4.setWeightRate("0*");
        dmFocAnnualAmpVO4.setGroupCode("code2");
        dmFocAnnualAmpVO4.setGroupCnName("code2");
        dmFocAnnualAmpVO4.setStatusCode("3");
        dmFocAnnualAmpVO4.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO4.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO4);

        DmFocAnnualAmpVO dmFocAnnualAmpVO6 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO6.setAnnualAmp("1.0");
        dmFocAnnualAmpVO6.setWeightRate("0*");
        dmFocAnnualAmpVO6.setGroupCode("code4");
        dmFocAnnualAmpVO6.setGroupCnName("code4");
        dmFocAnnualAmpVO6.setStatusCode("3");
        dmFocAnnualAmpVO6.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO6.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO6);
        return dmFocAnnualAmpVOPagedResult;
    }

    @Test
    public void industryCostList8Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setOrderColumn("groupCode");
        annualAnalysisVO.setOrderMethod("desc");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> threeYears = getThreeYears();
        annualAnalysisVO.setYear(threeYears.get(0));
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = getFocAnnualAmpVOList(threeYears);

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.industryCostList(annualAnalysisVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @NotNull
    private List<DmFocAnnualAmpVO> getFocAnnualAmpVOList(List<String> threeYears) {
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = new ArrayList();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp(null);
        dmFocAnnualAmpVO.setWeightRate("1.0");
        dmFocAnnualAmpVO.setGroupCode("11");
        dmFocAnnualAmpVO.setGroupCnName("code1");
        dmFocAnnualAmpVO.setStatusCode("2");

        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO);

        DmFocAnnualAmpVO dmFocAnnualAmpVO7 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO7.setAnnualAmp("1.0");
        dmFocAnnualAmpVO7.setWeightRate("1.1");
        dmFocAnnualAmpVO7.setGroupCode("22");
        dmFocAnnualAmpVO7.setGroupCnName("code5");
        dmFocAnnualAmpVO7.setStatusCode("3");
        dmFocAnnualAmpVO7.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO7.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO7);

        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("0*");
        dmFocAnnualAmpVO2.setWeightRate("0.8");
        dmFocAnnualAmpVO2.setGroupCode("code1");
        dmFocAnnualAmpVO2.setGroupCnName("code1");
        dmFocAnnualAmpVO2.setStatusCode("2");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO2.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO2);

        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("1.0");
        dmFocAnnualAmpVO3.setWeightRate("1.0");
        dmFocAnnualAmpVO3.setGroupCode("33");
        dmFocAnnualAmpVO3.setGroupCnName("33");
        dmFocAnnualAmpVO3.setStatusCode("3");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO3.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO3);

        DmFocAnnualAmpVO dmFocAnnualAmpVO5 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO5.setAnnualAmp("1.0");
        dmFocAnnualAmpVO5.setWeightRate("1.0");
        dmFocAnnualAmpVO5.setGroupCode("code3");
        dmFocAnnualAmpVO5.setGroupCnName("code3");
        dmFocAnnualAmpVO5.setStatusCode("4");
        dmFocAnnualAmpVO5.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO5.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO5);

        DmFocAnnualAmpVO dmFocAnnualAmpVO4 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO4.setAnnualAmp("1.0");
        dmFocAnnualAmpVO4.setWeightRate("0*");
        dmFocAnnualAmpVO4.setGroupCode("code2");
        dmFocAnnualAmpVO4.setGroupCnName("code2");
        dmFocAnnualAmpVO4.setStatusCode("3");
        dmFocAnnualAmpVO4.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO4.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO4);

        DmFocAnnualAmpVO dmFocAnnualAmpVO6 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO6.setAnnualAmp("1.0");
        dmFocAnnualAmpVO6.setWeightRate("0*");
        dmFocAnnualAmpVO6.setGroupCode("code4");
        dmFocAnnualAmpVO6.setGroupCnName("code4");
        dmFocAnnualAmpVO6.setStatusCode("3");
        dmFocAnnualAmpVO6.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO6.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO6);
        return dmFocAnnualAmpVOPagedResult;
    }

    @Test
    public void industryCostList9Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setOrderColumn("annualAmp");
        annualAnalysisVO.setOrderMethod("asc");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> threeYears = getThreeYears();
        annualAnalysisVO.setYear(threeYears.get(0));
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = getDmFocAnnualAmpVOList(threeYears);

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.industryCostList(annualAnalysisVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void industryCostList10Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setOrderColumn("annualAmp");
        annualAnalysisVO.setOrderMethod("desc");
        List<String> threeYears = getThreeYears();
        annualAnalysisVO.setYear(threeYears.get(0));
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = getDmFocAnnualAmpVOList(threeYears);

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.industryCostList(annualAnalysisVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void industryCostList11Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setOrderColumn("weightAnnualAmpPercent");
        annualAnalysisVO.setOrderMethod("asc");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> threeYears = getThreeYears();
        annualAnalysisVO.setYear(threeYears.get(0));
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = getDmFocAnnualAmpVOList(threeYears);

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.industryCostList(annualAnalysisVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void industryCostList12Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setOrderColumn("weightAnnualAmpPercent");
        annualAnalysisVO.setOrderMethod("desc");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> threeYears = getThreeYears();
        annualAnalysisVO.setYear(threeYears.get(0));
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = getDmFocAnnualAmpVOList(threeYears);

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.industryCostList(annualAnalysisVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @NotNull
    private List<DmFocAnnualAmpVO> getDmFocAnnualAmpVOList(List<String> threeYears) {
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = new ArrayList();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp(null);
        dmFocAnnualAmpVO.setWeightRate("1.0");
        dmFocAnnualAmpVO.setGroupCode("code1");
        dmFocAnnualAmpVO.setGroupCnName("code1");
        dmFocAnnualAmpVO.setStatusCode("2");

        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO);

        DmFocAnnualAmpVO dmFocAnnualAmpVO7 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO7.setAnnualAmp("1.0");
        dmFocAnnualAmpVO7.setWeightRate("1.1");
        dmFocAnnualAmpVO7.setGroupCode("code5");
        dmFocAnnualAmpVO7.setGroupCnName("code5");
        dmFocAnnualAmpVO7.setStatusCode("3");
        dmFocAnnualAmpVO7.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO7.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO7);

        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("0*");
        dmFocAnnualAmpVO2.setWeightRate("0.8");
        dmFocAnnualAmpVO2.setGroupCode("code1");
        dmFocAnnualAmpVO2.setGroupCnName("code1");
        dmFocAnnualAmpVO2.setStatusCode("2");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO2.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO2);

        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("0.6");
        dmFocAnnualAmpVO3.setWeightRate("1.0");
        dmFocAnnualAmpVO3.setGroupCode("code2");
        dmFocAnnualAmpVO3.setGroupCnName("code2");
        dmFocAnnualAmpVO3.setStatusCode("3");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO3.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO3);

        DmFocAnnualAmpVO dmFocAnnualAmpVO5 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO5.setAnnualAmp("0*");
        dmFocAnnualAmpVO5.setWeightRate("1.0");
        dmFocAnnualAmpVO5.setGroupCode("code3");
        dmFocAnnualAmpVO5.setGroupCnName("code3");
        dmFocAnnualAmpVO5.setStatusCode("4");
        dmFocAnnualAmpVO5.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO5.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO5);

        DmFocAnnualAmpVO dmFocAnnualAmpVO4 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO4.setAnnualAmp("1.0");
        dmFocAnnualAmpVO4.setWeightRate("0*");
        dmFocAnnualAmpVO4.setGroupCode("code2");
        dmFocAnnualAmpVO4.setGroupCnName("code2");
        dmFocAnnualAmpVO4.setStatusCode("3");
        dmFocAnnualAmpVO4.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO4.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO4);

        DmFocAnnualAmpVO dmFocAnnualAmpVO6 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO6.setAnnualAmp(null);
        dmFocAnnualAmpVO6.setWeightRate("0*");
        dmFocAnnualAmpVO6.setGroupCode("code4");
        dmFocAnnualAmpVO6.setGroupCnName("code4");
        dmFocAnnualAmpVO6.setStatusCode("3");
        dmFocAnnualAmpVO6.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO6.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO6);
        return dmFocAnnualAmpVOPagedResult;
    }

    @Test
    public void industryCostList13Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGroupLevel("CATEGORY");
        List<String> threeYears = getThreeYears();
        annualAnalysisVO.setYear(threeYears.get(0));
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = new ArrayList();

        DmFocAnnualAmpVO dmFocAnnualAmpVO7 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO7.setAnnualAmp("1.0");
        dmFocAnnualAmpVO7.setWeightRate("1.1");
        dmFocAnnualAmpVO7.setGroupCode("code5");
        dmFocAnnualAmpVO7.setGroupCnName("code5");
        dmFocAnnualAmpVO7.setStatusCode("3");
        dmFocAnnualAmpVO7.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO7.setWeightAnnualAmpPercent("0.55");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO7);

        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("0.6");
        dmFocAnnualAmpVO3.setWeightRate("1.0");
        dmFocAnnualAmpVO3.setGroupCode("code2");
        dmFocAnnualAmpVO3.setGroupCnName("code2");
        dmFocAnnualAmpVO3.setStatusCode("3");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO3.setWeightAnnualAmpPercent("0.22");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO3);

        DmFocAnnualAmpVO dmFocAnnualAmpVO5 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO5.setAnnualAmp("0*");
        dmFocAnnualAmpVO5.setWeightRate("1.0");
        dmFocAnnualAmpVO5.setGroupCode("code3");
        dmFocAnnualAmpVO5.setGroupCnName("code3");
        dmFocAnnualAmpVO5.setStatusCode("4");
        dmFocAnnualAmpVO5.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO5.setWeightAnnualAmpPercent("0.445");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO5);

        DmFocAnnualAmpVO dmFocAnnualAmpVO6 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO6.setAnnualAmp(null);
        dmFocAnnualAmpVO6.setWeightRate("0*");
        dmFocAnnualAmpVO6.setGroupCode("code4");
        dmFocAnnualAmpVO6.setGroupCnName("code4");
        dmFocAnnualAmpVO6.setStatusCode("3");
        dmFocAnnualAmpVO6.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO6.setWeightAnnualAmpPercent("0.44");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO6);

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.industryCostList(annualAnalysisVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void industryCostList14Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGroupLevel("CATEGORY");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> threeYears = getThreeYears();
        annualAnalysisVO.setYear(threeYears.get(0));
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = new ArrayList();

        DmFocAnnualAmpVO dmFocAnnualAmpVO7 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO7.setAnnualAmp("1.0");
        dmFocAnnualAmpVO7.setWeightRate("1.1");
        dmFocAnnualAmpVO7.setGroupCode("code5");
        dmFocAnnualAmpVO7.setGroupCnName("code5");
        dmFocAnnualAmpVO7.setStatusCode("3");
        dmFocAnnualAmpVO7.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO7.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO7);

        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("0.6");
        dmFocAnnualAmpVO3.setWeightRate("1.0");
        dmFocAnnualAmpVO3.setGroupCode("code2");
        dmFocAnnualAmpVO3.setGroupCnName("code2");
        dmFocAnnualAmpVO3.setStatusCode("3");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO3.setWeightAnnualAmpPercent("2.2");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO3);

        DmFocAnnualAmpVO dmFocAnnualAmpVO5 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO5.setAnnualAmp("0*");
        dmFocAnnualAmpVO5.setWeightRate("1.0");
        dmFocAnnualAmpVO5.setGroupCode("code3");
        dmFocAnnualAmpVO5.setGroupCnName("code3");
        dmFocAnnualAmpVO5.setStatusCode("4");
        dmFocAnnualAmpVO5.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO5.setWeightAnnualAmpPercent("1.6");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO5);

        DmFocAnnualAmpVO dmFocAnnualAmpVO6 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO6.setAnnualAmp(null);
        dmFocAnnualAmpVO6.setWeightRate("0*");
        dmFocAnnualAmpVO6.setGroupCode("code4");
        dmFocAnnualAmpVO6.setGroupCnName("code4");
        dmFocAnnualAmpVO6.setStatusCode("3");
        dmFocAnnualAmpVO6.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO6.setWeightAnnualAmpPercent("6.2");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO6);

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.industryCostList(annualAnalysisVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void industryCostList15Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGroupLevel("CATEGORY");
        List<String> groupCodeList = new ArrayList<>();
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> threeYears = getThreeYears();
        annualAnalysisVO.setYear(threeYears.get(0));
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(true);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = getVos(threeYears);

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.industryCostList(annualAnalysisVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void industryCostList16Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag(null);
        annualAnalysisVO.setGroupLevel("CATEGORY");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> threeYears = getThreeYears();
        annualAnalysisVO.setYear(threeYears.get(0));
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = getVos(threeYears);

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.industryCostList(annualAnalysisVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @NotNull
    private List<DmFocAnnualAmpVO> getVos(List<String> threeYears) {
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = new ArrayList();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp(null);
        dmFocAnnualAmpVO.setWeightRate("1.0");
        dmFocAnnualAmpVO.setGroupCode("code1");
        dmFocAnnualAmpVO.setGroupCnName("code1");
        dmFocAnnualAmpVO.setStatusCode("2");

        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO);

        DmFocAnnualAmpVO dmFocAnnualAmpVO7 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO7.setAnnualAmp("1.0");
        dmFocAnnualAmpVO7.setWeightRate("1.1");
        dmFocAnnualAmpVO7.setGroupCode("code5");
        dmFocAnnualAmpVO7.setGroupCnName("code5");
        dmFocAnnualAmpVO7.setStatusCode("3");
        dmFocAnnualAmpVO7.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO7.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO7);

        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("0*");
        dmFocAnnualAmpVO2.setWeightRate("0.8");
        dmFocAnnualAmpVO2.setGroupCode("code1");
        dmFocAnnualAmpVO2.setGroupCnName("code1");
        dmFocAnnualAmpVO2.setStatusCode("2");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO2.setWeightAnnualAmpPercent(null);
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO2);

        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("0.6");
        dmFocAnnualAmpVO3.setWeightRate("1.0");
        dmFocAnnualAmpVO3.setGroupCode("code2");
        dmFocAnnualAmpVO3.setGroupCnName("code2");
        dmFocAnnualAmpVO3.setStatusCode("3");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO3.setWeightAnnualAmpPercent("2.2");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO3);

        DmFocAnnualAmpVO dmFocAnnualAmpVO5 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO5.setAnnualAmp("0*");
        dmFocAnnualAmpVO5.setWeightRate("1.0");
        dmFocAnnualAmpVO5.setGroupCode("code3");
        dmFocAnnualAmpVO5.setGroupCnName("code3");
        dmFocAnnualAmpVO5.setStatusCode("4");
        dmFocAnnualAmpVO5.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO5.setWeightAnnualAmpPercent("1.6");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO5);

        DmFocAnnualAmpVO dmFocAnnualAmpVO4 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO4.setAnnualAmp("1.0");
        dmFocAnnualAmpVO4.setWeightRate("0*");
        dmFocAnnualAmpVO4.setGroupCode("code2");
        dmFocAnnualAmpVO4.setGroupCnName("code2");
        dmFocAnnualAmpVO4.setStatusCode("3");
        dmFocAnnualAmpVO4.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO4.setWeightAnnualAmpPercent("0*");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO4);

        DmFocAnnualAmpVO dmFocAnnualAmpVO6 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO6.setAnnualAmp(null);
        dmFocAnnualAmpVO6.setWeightRate("0*");
        dmFocAnnualAmpVO6.setGroupCode("code4");
        dmFocAnnualAmpVO6.setGroupCnName("code4");
        dmFocAnnualAmpVO6.setStatusCode("3");
        dmFocAnnualAmpVO6.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO6.setWeightAnnualAmpPercent("6.2");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO6);
        return dmFocAnnualAmpVOPagedResult;
    }

    @Test
    public void industryCostList17Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGranularityType("U");
        annualAnalysisVO.setCaliberFlag("R");
        annualAnalysisVO.setGroupLevel("CATEGORY");
        annualAnalysisVO.setOrderColumn("weightAnnualAmpPercent");
        annualAnalysisVO.setOrderMethod("asc");
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(true);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> threeYears = getThreeYears();
        annualAnalysisVO.setYear(threeYears.get(0));
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = getAnnualAmpVOS(threeYears);

        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.industryCostList(annualAnalysisVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void industryCostList18Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGranularityType("U");
        annualAnalysisVO.setCaliberFlag("R");
        annualAnalysisVO.setGroupLevel("CATEGORY");
        annualAnalysisVO.setOrderColumn("weightAnnualAmpPercent");
        annualAnalysisVO.setOrderMethod("desc");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> threeYears = getThreeYears();
        annualAnalysisVO.setYear(threeYears.get(0));
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = getAnnualAmpVOS(threeYears);

        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.industryCostList(annualAnalysisVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void industryCostList19Test() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGranularityType("U");
        annualAnalysisVO.setCaliberFlag("R");
        annualAnalysisVO.setGroupLevel("CEG");
        annualAnalysisVO.setOrderColumn("weightAnnualAmpPercent");
        annualAnalysisVO.setOrderMethod("desc");
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(true);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> threeYears = getThreeYears();
        annualAnalysisVO.setYear(threeYears.get(0));
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = getAmpVOS(threeYears);

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.industryCostList(annualAnalysisVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @NotNull
    private List<DmFocAnnualAmpVO> getAmpVOS(List<String> threeYears) {
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = new ArrayList();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp(null);
        dmFocAnnualAmpVO.setWeightRate("1.0");
        dmFocAnnualAmpVO.setGroupCode("code1");
        dmFocAnnualAmpVO.setGroupCnName("code1");
        dmFocAnnualAmpVO.setStatusCode("2");

        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO);

        DmFocAnnualAmpVO dmFocAnnualAmpVO7 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO7.setAnnualAmp("0.0");
        dmFocAnnualAmpVO7.setWeightRate("1.1");
        dmFocAnnualAmpVO7.setGroupCode("code5");
        dmFocAnnualAmpVO7.setGroupCnName("code5");
        dmFocAnnualAmpVO7.setStatusCode("4");
        dmFocAnnualAmpVO7.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO7.setWeightAnnualAmpPercent("0.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO7);

        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("0*");
        dmFocAnnualAmpVO2.setWeightRate("0.8");
        dmFocAnnualAmpVO2.setGroupCode("code1");
        dmFocAnnualAmpVO2.setGroupCnName("code1");
        dmFocAnnualAmpVO2.setStatusCode("2");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO2.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO2);

        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("0.6");
        dmFocAnnualAmpVO3.setWeightRate("1.0");
        dmFocAnnualAmpVO3.setGroupCode("code2");
        dmFocAnnualAmpVO3.setGroupCnName("code2");
        dmFocAnnualAmpVO3.setStatusCode("3");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO3.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO3);

        DmFocAnnualAmpVO dmFocAnnualAmpVO5 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO5.setAnnualAmp("0*");
        dmFocAnnualAmpVO5.setWeightRate("1.0");
        dmFocAnnualAmpVO5.setGroupCode("code3");
        dmFocAnnualAmpVO5.setGroupCnName("code3");
        dmFocAnnualAmpVO5.setStatusCode("4");
        dmFocAnnualAmpVO5.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO5.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO5);

        DmFocAnnualAmpVO dmFocAnnualAmpVO4 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO4.setAnnualAmp("1.0");
        dmFocAnnualAmpVO4.setWeightRate("0*");
        dmFocAnnualAmpVO4.setGroupCode("code2");
        dmFocAnnualAmpVO4.setGroupCnName("code2");
        dmFocAnnualAmpVO4.setStatusCode("3");
        dmFocAnnualAmpVO4.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO4.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO4);

        DmFocAnnualAmpVO dmFocAnnualAmpVO6 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO6.setAnnualAmp(null);
        dmFocAnnualAmpVO6.setWeightRate("0*");
        dmFocAnnualAmpVO6.setGroupCode("code4");
        dmFocAnnualAmpVO6.setGroupCnName("code4");
        dmFocAnnualAmpVO6.setStatusCode("3");
        dmFocAnnualAmpVO6.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO6.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO6);
        return dmFocAnnualAmpVOPagedResult;
    }

    @Test
    public void industryCostList20Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGranularityType("U");
        annualAnalysisVO.setCaliberFlag("R");
        annualAnalysisVO.setGroupLevel("CATEGORY");
        annualAnalysisVO.setOrderColumn("weightRate");
        annualAnalysisVO.setOrderMethod("asc");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> threeYears = getThreeYears();
        annualAnalysisVO.setYear(threeYears.get(0));
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = getAnnualAmpVOS(threeYears);

        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.industryCostList(annualAnalysisVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @NotNull
    private List<DmFocAnnualAmpVO> getAnnualAmpVOS(List<String> threeYears) {
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = new ArrayList();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp(null);
        dmFocAnnualAmpVO.setWeightRate("1.0");
        dmFocAnnualAmpVO.setGroupCode("code1");
        dmFocAnnualAmpVO.setGroupCnName("code1");
        dmFocAnnualAmpVO.setStatusCode("2");

        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO);

        DmFocAnnualAmpVO dmFocAnnualAmpVO7 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO7.setAnnualAmp("1.0");
        dmFocAnnualAmpVO7.setWeightRate("1.1");
        dmFocAnnualAmpVO7.setGroupCode("code5");
        dmFocAnnualAmpVO7.setGroupCnName("code5");
        dmFocAnnualAmpVO7.setStatusCode("3");
        dmFocAnnualAmpVO7.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO7.setWeightAnnualAmpPercent("0.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO7);

        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("0*");
        dmFocAnnualAmpVO2.setWeightRate("0.8");
        dmFocAnnualAmpVO2.setGroupCode("code1");
        dmFocAnnualAmpVO2.setGroupCnName("code1");
        dmFocAnnualAmpVO2.setStatusCode("2");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(2));
        dmFocAnnualAmpVO2.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO2);

        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("0.6");
        dmFocAnnualAmpVO3.setWeightRate("1.0");
        dmFocAnnualAmpVO3.setGroupCode("code2");
        dmFocAnnualAmpVO3.setGroupCnName("code2");
        dmFocAnnualAmpVO3.setStatusCode("3");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO3.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO3);

        DmFocAnnualAmpVO dmFocAnnualAmpVO5 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO5.setAnnualAmp("0*");
        dmFocAnnualAmpVO5.setWeightRate("1.0");
        dmFocAnnualAmpVO5.setGroupCode("code3");
        dmFocAnnualAmpVO5.setGroupCnName("code3");
        dmFocAnnualAmpVO5.setStatusCode("4");
        dmFocAnnualAmpVO5.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO5.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO5);

        DmFocAnnualAmpVO dmFocAnnualAmpVO4 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO4.setAnnualAmp("1.0");
        dmFocAnnualAmpVO4.setWeightRate("0*");
        dmFocAnnualAmpVO4.setGroupCode("code2");
        dmFocAnnualAmpVO4.setGroupCnName("code2");
        dmFocAnnualAmpVO4.setStatusCode("3");
        dmFocAnnualAmpVO4.setPeriodYear(threeYears.get(1));
        dmFocAnnualAmpVO4.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO4);

        DmFocAnnualAmpVO dmFocAnnualAmpVO6 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO6.setAnnualAmp(null);
        dmFocAnnualAmpVO6.setWeightRate("0*");
        dmFocAnnualAmpVO6.setGroupCode("code4");
        dmFocAnnualAmpVO6.setGroupCnName("code4");
        dmFocAnnualAmpVO6.setStatusCode("3");
        dmFocAnnualAmpVO6.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO6.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO6);
        return dmFocAnnualAmpVOPagedResult;
    }

    @Test
    public void industryCostList21Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGranularityType("U");
        annualAnalysisVO.setCaliberFlag("R");
        annualAnalysisVO.setGroupLevel("CATEGORY");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> threeYears = getThreeYears();
        annualAnalysisVO.setYear(threeYears.get(0));
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = getFocAnnualAmpVOS(threeYears);

        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.industryCostList(annualAnalysisVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @NotNull
    private List<DmFocAnnualAmpVO> getFocAnnualAmpVOS(List<String> threeYears) {
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = new ArrayList();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp(null);
        dmFocAnnualAmpVO.setWeightRate("1.0");
        dmFocAnnualAmpVO.setGroupCode("code13");
        dmFocAnnualAmpVO.setGroupCnName("code13");
        dmFocAnnualAmpVO.setStatusCode("2");

        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO.setWeightAnnualAmpPercent("1.3");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO);

        DmFocAnnualAmpVO dmFocAnnualAmpVO7 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO7.setAnnualAmp("1.0");
        dmFocAnnualAmpVO7.setWeightRate(null);
        dmFocAnnualAmpVO7.setGroupCode("code5");
        dmFocAnnualAmpVO7.setGroupCnName("code5");
        dmFocAnnualAmpVO7.setStatusCode("3");
        dmFocAnnualAmpVO7.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO7.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO7);

        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("0*");
        dmFocAnnualAmpVO2.setWeightRate("0.8");
        dmFocAnnualAmpVO2.setGroupCode("code1");
        dmFocAnnualAmpVO2.setGroupCnName("code1");
        dmFocAnnualAmpVO2.setStatusCode("2");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO2.setWeightAnnualAmpPercent("1.2");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO2);

        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("0.6");
        dmFocAnnualAmpVO3.setWeightRate(null);
        dmFocAnnualAmpVO3.setGroupCode("code2");
        dmFocAnnualAmpVO3.setGroupCnName("code2");
        dmFocAnnualAmpVO3.setStatusCode("4");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO3.setWeightAnnualAmpPercent("0.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO3);

        DmFocAnnualAmpVO dmFocAnnualAmpVO5 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO5.setAnnualAmp("0*");
        dmFocAnnualAmpVO5.setWeightRate("1.0");
        dmFocAnnualAmpVO5.setGroupCode("code3");
        dmFocAnnualAmpVO5.setGroupCnName("code3");
        dmFocAnnualAmpVO5.setStatusCode("4");
        dmFocAnnualAmpVO5.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO5.setWeightAnnualAmpPercent("0.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO5);

        DmFocAnnualAmpVO dmFocAnnualAmpVO4 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO4.setAnnualAmp("1.0");
        dmFocAnnualAmpVO4.setWeightRate("0.8");
        dmFocAnnualAmpVO4.setGroupCode("code8");
        dmFocAnnualAmpVO4.setGroupCnName("code8");
        dmFocAnnualAmpVO4.setStatusCode("4");
        dmFocAnnualAmpVO4.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO4.setWeightAnnualAmpPercent("0.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO4);

        DmFocAnnualAmpVO dmFocAnnualAmpVO6 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO6.setAnnualAmp(null);
        dmFocAnnualAmpVO6.setWeightRate(null);
        dmFocAnnualAmpVO6.setGroupCode("code4");
        dmFocAnnualAmpVO6.setGroupCnName("code4");
        dmFocAnnualAmpVO6.setStatusCode("3");
        dmFocAnnualAmpVO6.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO6.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO6);
        return dmFocAnnualAmpVOPagedResult;
    }

    @Test
    public void industryCostList22Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGranularityType("U");
        annualAnalysisVO.setCaliberFlag("R");
        annualAnalysisVO.setGroupLevel("CATEGORY");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> threeYears = getThreeYears();
        annualAnalysisVO.setYear(threeYears.get(0));
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = getVoList(threeYears);

        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.industryCostList(annualAnalysisVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void industryCostList23Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGranularityType("U");
        annualAnalysisVO.setCaliberFlag("R");
        annualAnalysisVO.setGroupLevel("CATEGORY");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> threeYears = getThreeYears();
        annualAnalysisVO.setYear(threeYears.get(0));
        annualAnalysisVO.setCostType("P");
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = getVoList(threeYears);

        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.industryCostList(annualAnalysisVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void industryCostList24Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGranularityType("U");
        annualAnalysisVO.setCaliberFlag("R");
        annualAnalysisVO.setGroupLevel("CATEGORY");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> threeYears = getThreeYears();
        annualAnalysisVO.setYear(threeYears.get(0));
        annualAnalysisVO.setCostType("M");
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = getVoList(threeYears);

        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.industryCostList(annualAnalysisVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void industryCostList25Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGranularityType("U");
        annualAnalysisVO.setCaliberFlag("R");
        annualAnalysisVO.setGroupLevel("CATEGORY");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(false);
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> threeYears = getThreeYears();
        annualAnalysisVO.setYear(threeYears.get(0));
        annualAnalysisVO.setCostType("M");
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = getVoList(threeYears);

        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.industryCostList(annualAnalysisVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void industryCostList26Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGranularityType("U");
        annualAnalysisVO.setCaliberFlag("R");
        annualAnalysisVO.setGroupLevel("CATEGORY");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setIsMultipleSelect(false);
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> threeYears = getThreeYears();
        annualAnalysisVO.setYear(threeYears.get(0));
        annualAnalysisVO.setCostType("M");
        annualAnalysisVO.setSubGroupCodeList(groupCodeList);
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = getVoList(threeYears);

        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(madeAnnualAmpDao).madeIndustryCostList(annualAnalysisVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.industryCostList(annualAnalysisVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void industryCostList27Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGranularityType("U");
        annualAnalysisVO.setCaliberFlag("R");
        annualAnalysisVO.setGroupLevel("CATEGORY");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setIsMultipleSelect(false);
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> threeYears = getThreeYears();
        annualAnalysisVO.setYear(threeYears.get(0));
        annualAnalysisVO.setCostType("T");
        annualAnalysisVO.setSubGroupCodeList(groupCodeList);
        annualAnalysisVO.setCostSubType("T");
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = getVoList(threeYears);

        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.industryCostList(annualAnalysisVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void industryCostList28Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGranularityType("U");
        annualAnalysisVO.setCaliberFlag("R");
        annualAnalysisVO.setGroupLevel("CATEGORY");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setIsMultipleSelect(false);
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> threeYears = getThreeYears();
        annualAnalysisVO.setYear(threeYears.get(0));
        annualAnalysisVO.setCostType("T");
        annualAnalysisVO.setSubGroupCodeList(groupCodeList);
        annualAnalysisVO.setCostSubType("M");
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = getVoList(threeYears);

        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.industryCostList(annualAnalysisVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void industryCostList29Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setGranularityType("U");
        annualAnalysisVO.setCaliberFlag("R");
        annualAnalysisVO.setGroupLevel("CATEGORY");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setIsMultipleSelect(false);
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> threeYears = getThreeYears();
        annualAnalysisVO.setYear(threeYears.get(0));
        annualAnalysisVO.setCostType("T");
        annualAnalysisVO.setSubGroupCodeList(groupCodeList);
        annualAnalysisVO.setCostSubType("P");
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = getVoList(threeYears);

        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.industryCostList(annualAnalysisVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void industryCostList30Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("4");
        annualAnalysisVO.setGranularityType("U");
        annualAnalysisVO.setCaliberFlag("R");
        annualAnalysisVO.setGroupLevel("CATEGORY");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setIsMultipleSelect(false);
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> threeYears = getThreeYears();
        annualAnalysisVO.setYear(threeYears.get(0));
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setSubGroupCodeList(groupCodeList);
        annualAnalysisVO.setCostSubType("P");
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = getVoList(threeYears);

        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.industryCostList(annualAnalysisVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void industryCostList31Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setPageIndex(1);
        annualAnalysisVO.setPageSize(10);
        annualAnalysisVO.setViewFlag("4");
        annualAnalysisVO.setGranularityType("U");
        annualAnalysisVO.setCaliberFlag("R");
        annualAnalysisVO.setGroupLevel("CATEGORY");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("code1");
        groupCodeList.add("code2");
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setIsMultipleSelect(false);
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        List<String> threeYears = getThreeYears();
        annualAnalysisVO.setYear(threeYears.get(0));
        annualAnalysisVO.setCostType("M");
        annualAnalysisVO.setSubGroupCodeList(groupCodeList);
        annualAnalysisVO.setCostSubType("P");
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = getVoList(threeYears);

        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.industryCostList(annualAnalysisVO);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void methodReflect1Test() throws Exception {

        List<String> threeYears = getThreeYears();
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOResult = getVoList(threeYears);

        // 反射，私有方法
        Whitebox.invokeMethod(annualAmpService, "setParentCnNameDimensionLevel", dmFocAnnualAmpVOResult);

        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setMaxValue("0");

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult2 = new ArrayList();
        DmFocAnnualAmpVO dmFocAnnualAmpVO1 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO1.setAnnualAmp(null);
        dmFocAnnualAmpVO1.setWeightRate("1.0");
        dmFocAnnualAmpVO1.setGroupCode("code13");
        dmFocAnnualAmpVO1.setGroupCnName("code13");
        dmFocAnnualAmpVO1.setStatusCode("2");

        dmFocAnnualAmpVO1.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO1.setWeightAnnualAmpPercent("1.3");
        dmFocAnnualAmpVOPagedResult2.add(dmFocAnnualAmpVO1);

        DmFocAnnualAmpVO dmFocAnnualAmpVO22 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO22.setAnnualAmp("0*");
        dmFocAnnualAmpVO22.setWeightRate("1.2");
        dmFocAnnualAmpVO22.setGroupCode("code1");
        dmFocAnnualAmpVO22.setGroupCnName("code1");
        dmFocAnnualAmpVO22.setStatusCode("2");
        dmFocAnnualAmpVO22.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO22.setWeightAnnualAmpPercent("0*");
        dmFocAnnualAmpVOPagedResult2.add(dmFocAnnualAmpVO22);

        Whitebox.invokeMethod(annualAmpService, "setWeightAnnualAmpPercent", annualAnalysisVO, dmFocAnnualAmpVOPagedResult2);

        AnnualAnalysisVO annualAnalysis = new AnnualAnalysisVO();
        annualAnalysis.setMaxValue("1");

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult3 = new ArrayList();
        DmFocAnnualAmpVO dmFocAnnualAmpVO11 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO11.setAnnualAmp(null);
        dmFocAnnualAmpVO11.setWeightRate("1.0");
        dmFocAnnualAmpVO11.setGroupCode("code13");
        dmFocAnnualAmpVO11.setGroupCnName("code13");
        dmFocAnnualAmpVO11.setStatusCode("2");

        dmFocAnnualAmpVO11.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO11.setWeightAnnualAmpPercent("0.0013");
        dmFocAnnualAmpVOPagedResult3.add(dmFocAnnualAmpVO11);

        DmFocAnnualAmpVO dmFocAnnualAmpVO222 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO222.setAnnualAmp("0*");
        dmFocAnnualAmpVO222.setWeightRate("1.2");
        dmFocAnnualAmpVO222.setGroupCode("code1");
        dmFocAnnualAmpVO222.setGroupCnName("code1");
        dmFocAnnualAmpVO222.setStatusCode("2");
        dmFocAnnualAmpVO222.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO222.setWeightAnnualAmpPercent("0*");
        dmFocAnnualAmpVOPagedResult3.add(dmFocAnnualAmpVO222);

        Whitebox.invokeMethod(annualAmpService, "setWeightAnnualAmpPercent", annualAnalysis, dmFocAnnualAmpVOPagedResult3);

        AnnualAnalysisVO annualVO = new AnnualAnalysisVO();
        annualVO.setReverseSymbol(false);
        annualVO.setCostType("M");
        annualVO.setGranularityType("U");
        annualVO.setGroupLevel("MODL");
        List<DmFocAnnualAmpVO> dmFocAnnualAmpResult = new ArrayList();
        Whitebox.invokeMethod(annualAmpService, "getAnnualAmpOrderByWeightExcel", annualVO, dmFocAnnualAmpResult);


        AnnualAnalysisVO annualVO2 = new AnnualAnalysisVO();
        annualVO2.setReverseSymbol(false);
        annualVO2.setCostType("M");
        annualVO2.setGranularityType("P");
        annualVO2.setGroupLevel("MODL");
        List<DmFocAnnualAmpVO> dmFocAnnualResult = new ArrayList();
        Whitebox.invokeMethod(annualAmpService, "getAnnualAmpOrderByWeightExcel", annualVO2, dmFocAnnualResult);

        AnnualAnalysisVO annualVO3 = new AnnualAnalysisVO();
        annualVO3.setReverseSymbol(false);
        annualVO3.setCostType("T");
        annualVO3.setGranularityType("D");
        annualVO3.setGroupLevel("MODL");
        List<DmFocAnnualAmpVO> dmFocAnnualResult2 = new ArrayList();
        Whitebox.invokeMethod(annualAmpService, "getAnnualAmpOrderByWeightExcel", annualVO3, dmFocAnnualResult2);

        AnnualAnalysisVO annualVO4 = new AnnualAnalysisVO();
        annualVO4.setReverseSymbol(true);
        annualVO4.setCostType("M");
        annualVO4.setGranularityType("D");
        annualVO4.setGroupLevel("MODL");
        List<DmFocAnnualAmpVO> dmFocAnnualResult4 = new ArrayList();
        Whitebox.invokeMethod(annualAmpService, "getAnnualAmpOrderByWeightExcel", annualVO4, dmFocAnnualResult4);
        Assertions.assertNull(null);
    }

    @Test
    public void distributeCostChart() {
        AnnualAnalysisVO annualAnalysisVO=new AnnualAnalysisVO();
        annualAnalysisVO.setIsMultipleSelect(true);
        DmFocVersionInfoDTO dmFocVersionInfoDTO=new DmFocVersionInfoDTO();
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(any());

        ResultDataVO resultDataVO = annualAmpService.distributeCostChart(annualAnalysisVO);
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void distributeCostChart1T() {
        AnnualAnalysisVO annualAnalysisVO=new AnnualAnalysisVO();
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsMultipleSelect(false);
        DmFocVersionInfoDTO dmFocVersionInfoDTO=new DmFocVersionInfoDTO();
        PowerMockito.doReturn(dmFocVersionInfoDTO).when(dmFocVersionDao).findAnnualVersion(any());

        ResultDataVO resultDataVO = annualAmpService.distributeCostChart(annualAnalysisVO);
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void getCompareAmpCost() throws InterruptedException {
        List<AnnualAnalysisVO> annualAnalysisList = new ArrayList<>();
        AnnualAnalysisVO annualAnalysisVO=new AnnualAnalysisVO();
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisList.add(annualAnalysisVO);

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.getCompareAmpCost(annualAnalysisList);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void getCompareAmpCost1T() throws InterruptedException {
        List<AnnualAnalysisVO> annualAnalysisList = new ArrayList<>();
        AnnualAnalysisVO annualAnalysisVO=new AnnualAnalysisVO();
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisList.add(annualAnalysisVO);

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.getCompareAmpCost(annualAnalysisList);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void getCompareAmpCost2T() throws InterruptedException {
        List<AnnualAnalysisVO> annualAnalysisList = new ArrayList<>();
        AnnualAnalysisVO annualAnalysisVO=new AnnualAnalysisVO();
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setLv0ProdRndTeamCode("lv0");
        annualAnalysisVO.setLv1ProdRndTeamCode("lv1");
        annualAnalysisVO.setLv2ProdRndTeamCode("lv2");
        annualAnalysisVO.setLv3ProdRndTeamCode("lv3");

        annualAnalysisList.add(annualAnalysisVO);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.getCompareAmpCost(annualAnalysisList);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assert.assertNotNull(resultDataVO);
    }

    @Test
    public void methodReflect2Test() throws Exception {

        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setGranularityType("U");
        annualAnalysisVO.setViewFlag("1");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setIsMultipleSelect(false);
        Whitebox.invokeMethod(annualAmpService, "getIndustryAmpCost", annualAnalysisVO);

        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        AnnualAnalysisVO annualAnalysisVO11 = new AnnualAnalysisVO();
        annualAnalysisVO11.setGranularityType("U");
        annualAnalysisVO11.setViewFlag("1");
        annualAnalysisVO11.setCostType("M");
        annualAnalysisVO11.setIsContainComb(true);
        annualAnalysisVO11.setIsMultipleSelect(false);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("null_##11");
        annualAnalysisVO11.setGroupCodeList(groupCodeList);

        List<String> teamCodeList = new ArrayList<>();
        teamCodeList.add("10121_##11");
        annualAnalysisVO11.setTeamCodeList(teamCodeList);
        Whitebox.invokeMethod(annualAmpService, "getIndustryAmpCost", annualAnalysisVO11);

        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        AnnualAnalysisVO annualAnalysisVO2 = new AnnualAnalysisVO();
        annualAnalysisVO2.setGranularityType("U");
        annualAnalysisVO2.setViewFlag("1");
        annualAnalysisVO2.setCostType("T");
        annualAnalysisVO2.setIsContainComb(false);
        annualAnalysisVO2.setIsMultipleSelect(false);
        Whitebox.invokeMethod(annualAmpService, "getIndustryAmpCost", annualAnalysisVO2);

        Assertions.assertNull(null);
    }

    @NotNull
    private List<DmFocAnnualAmpVO> getVoList(List<String> threeYears) {
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = new ArrayList();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp(null);
        dmFocAnnualAmpVO.setWeightRate("1.0");
        dmFocAnnualAmpVO.setGroupCode("code13");
        dmFocAnnualAmpVO.setGroupCnName("code13");
        dmFocAnnualAmpVO.setStatusCode("2");

        dmFocAnnualAmpVO.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO.setWeightAnnualAmpPercent("1.3");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO);

        DmFocAnnualAmpVO dmFocAnnualAmpVO7 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO7.setAnnualAmp("1.0");
        dmFocAnnualAmpVO7.setWeightRate("1.2");
        dmFocAnnualAmpVO7.setGroupCode("code5");
        dmFocAnnualAmpVO7.setGroupCnName("code5");
        dmFocAnnualAmpVO7.setParentLevel("DIMENSION");
        dmFocAnnualAmpVO7.setStatusCode("3");
        dmFocAnnualAmpVO7.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO7.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO7);

        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("0*");
        dmFocAnnualAmpVO2.setWeightRate("1.2");
        dmFocAnnualAmpVO2.setGroupCode("code1");
        dmFocAnnualAmpVO2.setGroupCnName("code1");
        dmFocAnnualAmpVO2.setStatusCode("2");
        dmFocAnnualAmpVO2.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO2.setWeightAnnualAmpPercent("1.2");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO2);

        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("0.6");
        dmFocAnnualAmpVO3.setWeightRate(null);
        dmFocAnnualAmpVO3.setGroupCode("code2");
        dmFocAnnualAmpVO3.setGroupCnName("code2");
        dmFocAnnualAmpVO3.setGroupLevel("DIMENSION");
        dmFocAnnualAmpVO3.setStatusCode("4");
        dmFocAnnualAmpVO3.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO3.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO3);

        DmFocAnnualAmpVO dmFocAnnualAmpVO5 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO5.setAnnualAmp("0*");
        dmFocAnnualAmpVO5.setWeightRate("1.0");
        dmFocAnnualAmpVO5.setGroupCode("code3");
        dmFocAnnualAmpVO5.setGroupCnName("code3");
        dmFocAnnualAmpVO5.setStatusCode("4");
        dmFocAnnualAmpVO5.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO5.setWeightAnnualAmpPercent("0.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO5);

        DmFocAnnualAmpVO dmFocAnnualAmpVO4 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO4.setAnnualAmp("1.0");
        dmFocAnnualAmpVO4.setWeightRate(null);
        dmFocAnnualAmpVO4.setGroupCode("code8");
        dmFocAnnualAmpVO4.setGroupCnName("code8");
        dmFocAnnualAmpVO4.setStatusCode("4");
        dmFocAnnualAmpVO4.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO4.setWeightAnnualAmpPercent("0.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO4);

        DmFocAnnualAmpVO dmFocAnnualAmpVO6 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO6.setAnnualAmp(null);
        dmFocAnnualAmpVO6.setWeightRate(null);
        dmFocAnnualAmpVO6.setGroupCode("code4");
        dmFocAnnualAmpVO6.setGroupCnName("code4");
        dmFocAnnualAmpVO6.setStatusCode("3");
        dmFocAnnualAmpVO6.setPeriodYear(threeYears.get(0));
        dmFocAnnualAmpVO6.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO6);
        return dmFocAnnualAmpVOPagedResult;
    }

    @Test
    public void exportDetail() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        HttpServletResponse response = null;
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        annualAnalysisVO.setFileName("年度分析1");
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setLv3CegCnName("ceg1");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsCompareFlag(false);
        annualAnalysisVO.setIsShowChildContent(true);
        annualAnalysisVO.setIsShowHistogramChart(true);
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(false);
        annualAnalysisVO.setIsTotalChildChart(true);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("11");
        groupCodeList.add("22");
        groupCodeList.add("33");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setOverseaFlag("G");

        ResultDataVO bgInfoList = new ResultDataVO();
        List<DmFocViewInfoVO> dataList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setLv0ProdListCode("code1");
        dataList.add(dmFocViewInfoVO);
        bgInfoList.setData(dataList);
        PowerMockito.doReturn(bgInfoList).when(annualCommonService).getBgInfoList(any());
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","CEG");
        map.put("nextGroupName","name1");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);

        List<DmFocAnnualAmpVO> currentAnnualAmpList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0");
        dmFocAnnualAmpVO.setPeriodYear(getThreeYears().get(0));
        dmFocAnnualAmpVO.setStatusCode("4");

        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("1.0");
        dmFocAnnualAmpVO2.setPeriodYear(getThreeYears().get(1));
        dmFocAnnualAmpVO2.setStatusCode("4");
        currentAnnualAmpList.add(dmFocAnnualAmpVO);
        currentAnnualAmpList.add(dmFocAnnualAmpVO2);
        PowerMockito.doReturn(currentAnnualAmpList).when(annualAmpDao).allIndustryCost(any());
        List<DmFocAnnualAmpVO> annualAmpAndWeightList = getAmpVOS();
        PowerMockito.doReturn(annualAmpAndWeightList).when(annualAmpDao).industryCostList(any());

        List<DmFocAnnualAmpVO> annualAmpChartList = getVos();

        DmFoiImpExpRecordVO expRecordVO = new DmFoiImpExpRecordVO();
        expRecordVO.setFileSourceKey("122aa");
        expRecordVO.setFileName("年度分析1");
        expRecordVO.setRecordNum(3);
        expRecordVO.setFileSize("1");
        expRecordVO.setUserId("test1");

        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);

        DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv1DimensionSet(lv1DimensionSet);
        Set<String> lv2DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(dataPermissionsVO).when(commonService).getDimensionList(any(),any(),any());
        List<String> list = new ArrayList<>();
        list.add("10011");
        annualAnalysisVO.setTeamCodeList(list);
        ResultDataVO resultDataVO = annualAmpService.exportDetail(annualAnalysisVO, response);
        Assertions.assertNotNull(resultDataVO);
    }

    @NotNull
    private List<DmFocAnnualAmpVO> getVos() {
        List<DmFocAnnualAmpVO> annualAmpChartList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO7 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO7.setAnnualAmp("1.0");
        dmFocAnnualAmpVO7.setGroupCnName("name1");
        dmFocAnnualAmpVO7.setGroupCode("code1");
        dmFocAnnualAmpVO7.setPeriodYear(getThreeYears().get(0));
        annualAmpChartList.add(dmFocAnnualAmpVO7);

        DmFocAnnualAmpVO dmFocAnnualAmpVO8 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO8.setAnnualAmp("1.3");
        dmFocAnnualAmpVO8.setGroupCnName("name1");
        dmFocAnnualAmpVO8.setGroupCode("code1");
        dmFocAnnualAmpVO8.setPeriodYear(getThreeYears().get(1));
        annualAmpChartList.add(dmFocAnnualAmpVO8);

        DmFocAnnualAmpVO dmFocAnnualAmpVO9 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO9.setAnnualAmp("1.1");
        dmFocAnnualAmpVO9.setGroupCnName("name1");
        dmFocAnnualAmpVO9.setGroupCode("code1");
        dmFocAnnualAmpVO9.setPeriodYear(getThreeYears().get(2));
        annualAmpChartList.add(dmFocAnnualAmpVO9);
        return annualAmpChartList;
    }

    @NotNull
    private List<DmFocAnnualAmpVO> getAmpVOS() {
        List<DmFocAnnualAmpVO> annualAmpAndWeightList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO4 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO4.setAnnualAmp("1.0");
        dmFocAnnualAmpVO4.setWeightRate("1.0");
        dmFocAnnualAmpVO4.setWeightAnnualAmpPercent("0.5");
        dmFocAnnualAmpVO4.setPeriodYear(getThreeYears().get(0));
        dmFocAnnualAmpVO4.setGroupCode("code1");
        dmFocAnnualAmpVO4.setGroupCnName("name1");
        annualAmpAndWeightList.add(dmFocAnnualAmpVO4);

        DmFocAnnualAmpVO dmFocAnnualAmpVO5 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO5.setAnnualAmp("2.0");
        dmFocAnnualAmpVO5.setWeightRate("0.8");
        dmFocAnnualAmpVO5.setWeightAnnualAmpPercent("1.0");
        dmFocAnnualAmpVO5.setPeriodYear(getThreeYears().get(0));
        dmFocAnnualAmpVO5.setGroupCode("code2");
        dmFocAnnualAmpVO5.setGroupCnName("name2");
        annualAmpAndWeightList.add(dmFocAnnualAmpVO5);

        DmFocAnnualAmpVO dmFocAnnualAmpVO6 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO6.setAnnualAmp("3.0");
        dmFocAnnualAmpVO6.setWeightRate("0.9");
        dmFocAnnualAmpVO6.setWeightAnnualAmpPercent("2.0");
        dmFocAnnualAmpVO6.setPeriodYear(getThreeYears().get(0));
        dmFocAnnualAmpVO6.setGroupCode("code3");
        dmFocAnnualAmpVO6.setGroupCnName("name3");
        annualAmpAndWeightList.add(dmFocAnnualAmpVO6);
        return annualAmpAndWeightList;
    }


    @Test
    public void exportDetail2Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        HttpServletResponse response = null;
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        annualAnalysisVO.setFileName("年度分析1");
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsShowChildContent(true);
        annualAnalysisVO.setIsShowHistogramChart(false);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setIsTotalChildChart(true);
        annualAnalysisVO.setIsMultipleSelect(false);
        annualAnalysisVO.setIsCompareFlag(false);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("11");
        groupCodeList.add("22");
        groupCodeList.add("33");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setOverseaFlag("G");

        ResultDataVO bgInfoList = new ResultDataVO();
        List<DmFocViewInfoVO> dataList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setLv0ProdListCode("code1");
        dataList.add(dmFocViewInfoVO);
        bgInfoList.setData(dataList);
        PowerMockito.doReturn(bgInfoList).when(annualCommonService).getBgInfoList(any());
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","CEG");
        map.put("nextGroupName","name1");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);

        DmFoiImpExpRecordVO expRecordVO = new DmFoiImpExpRecordVO();
        expRecordVO.setFileSourceKey("122aa");
        expRecordVO.setFileName("年度分析1");
        expRecordVO.setRecordNum(3);
        expRecordVO.setFileSize("1");
        expRecordVO.setUserId("test1");

        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);

        DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv1DimensionSet(lv1DimensionSet);
        Set<String> lv2DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(dataPermissionsVO).when(commonService).getDimensionList(any(),any(),any());

        ResultDataVO resultDataVO = annualAmpService.exportDetail(annualAnalysisVO, response);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void exportDetail21Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        HttpServletResponse response = null;
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        annualAnalysisVO.setFileName("年度分析1");
        annualAnalysisVO.setViewFlag("0");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setLv3CegCnName("ceg1");
        annualAnalysisVO.setCategoryCnName("name1");
        annualAnalysisVO.setIsShowChildContent(true);
        annualAnalysisVO.setIsShowHistogramChart(false);
        annualAnalysisVO.setIsTotalChildChart(true);
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(false);
        annualAnalysisVO.setIsCompareFlag(false);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("11");
        groupCodeList.add("22");
        groupCodeList.add("33");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setOverseaFlag("G");

        ResultDataVO bgInfoList = new ResultDataVO();
        List<DmFocViewInfoVO> dataList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setLv0ProdListCode("code1");
        dataList.add(dmFocViewInfoVO);
        bgInfoList.setData(dataList);
        PowerMockito.doReturn(bgInfoList).when(annualCommonService).getBgInfoList(any());
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","CEG");
        map.put("nextGroupName","name1");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);

        DmFoiImpExpRecordVO expRecordVO = new DmFoiImpExpRecordVO();
        expRecordVO.setFileSourceKey("122aa");
        expRecordVO.setFileName("年度分析1");
        expRecordVO.setRecordNum(3);
        expRecordVO.setFileSize("1");
        expRecordVO.setUserId("test1");

        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);

        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();
        DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv1DimensionSet(lv1DimensionSet);
        Set<String> lv2DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(dataPermissionsVO).when(commonService).getDimensionList(any(),any(),any());

        ResultDataVO resultDataVO = annualAmpService.exportDetail(annualAnalysisVO, response);
        Assertions.assertNotNull(resultDataVO);
    }

    @NotNull
    private List<DmFocAnnualAmpVO> getAnnualAmpVOS() {
        List<DmFocAnnualAmpVO> currentAnnualAmpList = new ArrayList<>();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0");
        dmFocAnnualAmpVO.setPeriodYear(getThreeYears().get(0));
        dmFocAnnualAmpVO.setStatusCode("4");

        DmFocAnnualAmpVO dmFocAnnualAmpVO2 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO2.setAnnualAmp("1.0");
        dmFocAnnualAmpVO2.setPeriodYear(getThreeYears().get(1));
        dmFocAnnualAmpVO2.setStatusCode("4");
        DmFocAnnualAmpVO dmFocAnnualAmpVO3 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO3.setAnnualAmp("1.0");
        dmFocAnnualAmpVO3.setPeriodYear(getThreeYears().get(2));
        dmFocAnnualAmpVO3.setStatusCode("4");

        currentAnnualAmpList.add(dmFocAnnualAmpVO);
        currentAnnualAmpList.add(dmFocAnnualAmpVO2);
        currentAnnualAmpList.add(dmFocAnnualAmpVO3);
        PowerMockito.doReturn(currentAnnualAmpList).when(annualAmpDao).allIndustryCost(any());
        List<DmFocAnnualAmpVO> annualAmpAndWeightList = getAmpVOS();
        PowerMockito.doReturn(annualAmpAndWeightList).when(annualAmpDao).industryCostList(any());

        List<DmFocAnnualAmpVO> annualAmpChartList = getVos();
        return annualAmpChartList;
    }

    @Test
    public void exportDetail3Test() throws Exception {
        HttpServletResponse response = null;
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        AnnualAnalysisVO annualAnalysisVO = getAnnualAnalysisVO();
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsTotalChildChart(true);
        annualAnalysisVO.setIsCompareFlag(false);
        ResultDataVO bgInfoList = new ResultDataVO();
        List<DmFocViewInfoVO> dataList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setLv0ProdListCode("code1");
        dataList.add(dmFocViewInfoVO);
        bgInfoList.setData(dataList);
        PowerMockito.doReturn(bgInfoList).when(annualCommonService).getBgInfoList(any());
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","ITEM");
        map.put("nextGroupName","name1");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = getFocAnnualAmpVOS();
        List<DmFocAnnualAmpVO> currentAnnualAmpList = new ArrayList();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0");
        dmFocAnnualAmpVO.setPeriodYear(getThreeYears().get(0));
        dmFocAnnualAmpVO.setStatusCode("1");
        currentAnnualAmpList.add(dmFocAnnualAmpVO);
        PowerMockito.doReturn(currentAnnualAmpList).when(annualAmpDao).allIndustryCost(any());
        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        DmFoiImpExpRecordVO expRecordVO = new DmFoiImpExpRecordVO();
        expRecordVO.setFileSourceKey("122aa");
        expRecordVO.setFileName("年度分析1");
        expRecordVO.setRecordNum(3);
        expRecordVO.setFileSize("1");
        expRecordVO.setUserId("test1");
        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);
        DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv1DimensionSet(lv1DimensionSet);
        Set<String> lv2DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(dataPermissionsVO).when(commonService).getDimensionList(any(),any(),any());
        ResultDataVO resultDataVO = annualAmpService.exportDetail(annualAnalysisVO, response);
        Assertions.assertNotNull(resultDataVO);
    }

    @NotNull
    private AnnualAnalysisVO getAnnualAnalysisVO() {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        annualAnalysisVO.setFileName("年度分析2");
        annualAnalysisVO.setViewFlag("1");
        annualAnalysisVO.setCaliberFlag("R");
        annualAnalysisVO.setGranularityType("U");
        List<String> lv1 = new ArrayList<>();
        lv1.add("lv1");
        annualAnalysisVO.setLv1ProdRdTeamCnName(lv1);
        List<String> lv3 = new ArrayList<>();
        lv3.add("lv3");
        annualAnalysisVO.setLv3ProdRdTeamCnName(lv3);
        annualAnalysisVO.setGroupLevel("ITEM");
        annualAnalysisVO.setIsShowChildContent(true);
        annualAnalysisVO.setIsShowHistogramChart(false);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(false);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("11");
        groupCodeList.add("22");
        groupCodeList.add("33");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setOverseaFlag("G");
        return annualAnalysisVO;
    }

    @NotNull
    private List<DmFocAnnualAmpVO> getFocAnnualAmpVOS() {
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = new ArrayList();
        DmFocAnnualAmpVO dmFocAnnualAmpVO5 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO5.setAnnualAmp("0*");
        dmFocAnnualAmpVO5.setWeightRate("1.0");
        dmFocAnnualAmpVO5.setGroupCode("code3");
        dmFocAnnualAmpVO5.setGroupCnName("code3");
        dmFocAnnualAmpVO5.setStatusCode("4");
        dmFocAnnualAmpVO5.setPeriodYear(getThreeYears().get(0));
        dmFocAnnualAmpVO5.setWeightAnnualAmpPercent("1.6");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO5);

        DmFocAnnualAmpVO dmFocAnnualAmpVO4 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO4.setAnnualAmp("1.0");
        dmFocAnnualAmpVO4.setWeightRate("0*");
        dmFocAnnualAmpVO4.setGroupCode("code2");
        dmFocAnnualAmpVO4.setGroupCnName("code2");
        dmFocAnnualAmpVO4.setStatusCode("3");
        dmFocAnnualAmpVO4.setPeriodYear(getThreeYears().get(1));
        dmFocAnnualAmpVO4.setWeightAnnualAmpPercent("0*");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO4);

        DmFocAnnualAmpVO dmFocAnnualAmpVO6 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO6.setAnnualAmp(null);
        dmFocAnnualAmpVO6.setWeightRate("0*");
        dmFocAnnualAmpVO6.setGroupCode("code4");
        dmFocAnnualAmpVO6.setGroupCnName("code4");
        dmFocAnnualAmpVO6.setStatusCode("3");
        dmFocAnnualAmpVO6.setPeriodYear(getThreeYears().get(0));
        dmFocAnnualAmpVO6.setWeightAnnualAmpPercent("6.2");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO6);
        return dmFocAnnualAmpVOPagedResult;
    }

    @Test
    public void exportDetail32Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        HttpServletResponse response = null;
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        annualAnalysisVO.setFileName("年度分析2");
        annualAnalysisVO.setViewFlag("1");
        annualAnalysisVO.setIsCompareFlag(false);
        annualAnalysisVO.setCaliberFlag("R");
        annualAnalysisVO.setGranularityType("U");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsShowChildContent(true);
        annualAnalysisVO.setIsShowHistogramChart(false);
        annualAnalysisVO.setIsMultipleSelect(false);
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsTotalChildChart(true);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("11");
        groupCodeList.add("22");
        groupCodeList.add("33");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setOverseaFlag("G");

        ResultDataVO bgInfoList = new ResultDataVO();
        List<DmFocViewInfoVO> dataList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setLv0ProdListCode("code1");
        dataList.add(dmFocViewInfoVO);
        bgInfoList.setData(dataList);
        PowerMockito.doReturn(bgInfoList).when(annualCommonService).getBgInfoList(any());
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();
        List<String> lv1 = new ArrayList<>();
        lv1.add("lv1");
        annualAnalysisVO.setLv1ProdRdTeamCnName(lv1);
        List<String> lv3 = new ArrayList<>();
        lv3.add("lv3");
        annualAnalysisVO.setLv3ProdRdTeamCnName(lv3);
        annualAnalysisVO.setGroupLevel("ITEM");
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","ITEM");
        map.put("nextGroupName","name1");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();

        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = getDmFocAnnualAmpVOS();

        List<DmFocAnnualAmpVO> currentAnnualAmpList = new ArrayList();
        DmFocAnnualAmpVO dmFocAnnualAmpVO = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO.setAnnualAmp("0.0");
        dmFocAnnualAmpVO.setPeriodYear(getThreeYears().get(0));
        dmFocAnnualAmpVO.setStatusCode("1");
        currentAnnualAmpList.add(dmFocAnnualAmpVO);
        PowerMockito.doReturn(currentAnnualAmpList).when(annualAmpDao).allIndustryCost(any());

        PowerMockito.doReturn(dmFocAnnualAmpVOPagedResult).when(annualAmpDao).industryCostList(annualAnalysisVO);
        DmFoiImpExpRecordVO expRecordVO = new DmFoiImpExpRecordVO();
        expRecordVO.setFileSourceKey("122aa");
        expRecordVO.setFileName("年度分析1");
        expRecordVO.setRecordNum(3);
        expRecordVO.setFileSize("1");
        expRecordVO.setUserId("test1");

        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);


        DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv1DimensionSet(lv1DimensionSet);
        Set<String> lv2DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(dataPermissionsVO).when(commonService).getDimensionList(any(),any(),any());

        ResultDataVO resultDataVO = annualAmpService.exportDetail(annualAnalysisVO, response);
        Assertions.assertNotNull(resultDataVO);
    }

    @NotNull
    private List<DmFocAnnualAmpVO> getDmFocAnnualAmpVOS() {
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOPagedResult = new ArrayList();
        DmFocAnnualAmpVO dmFocAnnualAmpVO5 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO5.setAnnualAmp("0*");
        dmFocAnnualAmpVO5.setWeightRate("1.0");
        dmFocAnnualAmpVO5.setGroupCode("code");
        dmFocAnnualAmpVO5.setGroupCnName("code");
        dmFocAnnualAmpVO5.setStatusCode("4");
        dmFocAnnualAmpVO5.setPeriodYear(getThreeYears().get(0));
        dmFocAnnualAmpVO5.setWeightAnnualAmpPercent("1.6");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO5);

        DmFocAnnualAmpVO dmFocAnnualAmpVO4 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO4.setAnnualAmp("1.0");
        dmFocAnnualAmpVO4.setWeightRate("0*");
        dmFocAnnualAmpVO4.setGroupCode("1.2");
        dmFocAnnualAmpVO4.setGroupCnName("1.2");
        dmFocAnnualAmpVO4.setStatusCode("3");
        dmFocAnnualAmpVO4.setPeriodYear(getThreeYears().get(1));
        dmFocAnnualAmpVO4.setWeightAnnualAmpPercent("0*");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO4);

        DmFocAnnualAmpVO dmFocAnnualAmpVO6 = new DmFocAnnualAmpVO();
        dmFocAnnualAmpVO6.setAnnualAmp(null);
        dmFocAnnualAmpVO6.setWeightRate("0*");
        dmFocAnnualAmpVO6.setGroupCode("14");
        dmFocAnnualAmpVO6.setGroupCnName("14");
        dmFocAnnualAmpVO6.setStatusCode("3");
        dmFocAnnualAmpVO6.setPeriodYear(getThreeYears().get(0));
        dmFocAnnualAmpVO6.setWeightAnnualAmpPercent("6.2");
        dmFocAnnualAmpVOPagedResult.add(dmFocAnnualAmpVO6);
        return dmFocAnnualAmpVOPagedResult;
    }

    @Test
    public void exportDetail4Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        HttpServletResponse response = null;
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        annualAnalysisVO.setFileName("年度分析2");
        annualAnalysisVO.setViewFlag("1");
        annualAnalysisVO.setLv3CegCnName("ceg1");
        annualAnalysisVO.setIsCompareFlag(false);
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsShowChildContent(true);
        annualAnalysisVO.setIsShowHistogramChart(true);
        annualAnalysisVO.setIsMultipleSelect(false);
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsTotalChildChart(true);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("11");
        groupCodeList.add("22");
        groupCodeList.add("33");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setOverseaFlag("G");

        ResultDataVO bgInfoList = new ResultDataVO();
        List<DmFocViewInfoVO> dataList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setLv0ProdListCode("code1");
        dataList.add(dmFocViewInfoVO);
        bgInfoList.setData(dataList);
        PowerMockito.doReturn(bgInfoList).when(annualCommonService).getBgInfoList(any());
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();
        List<String> lv1 = new ArrayList<>();
        lv1.add("lv1");
        annualAnalysisVO.setLv1ProdRdTeamCnName(lv1);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","CEG");
        map.put("nextGroupName","name1");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);

        DmFoiImpExpRecordVO expRecordVO = new DmFoiImpExpRecordVO();
        expRecordVO.setFileSourceKey("122aa");
        expRecordVO.setFileName("年度分析1");
        expRecordVO.setRecordNum(3);
        expRecordVO.setFileSize("1");
        expRecordVO.setUserId("test1");

        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);

        DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv1DimensionSet(lv1DimensionSet);
        Set<String> lv2DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(dataPermissionsVO).when(commonService).getDimensionList(any(),any(),any());
        List<String> list = new ArrayList<>();
        list.add("10011");
        annualAnalysisVO.setTeamCodeList(list);
        ResultDataVO resultDataVO = annualAmpService.exportDetail(annualAnalysisVO, response);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void exportDetail5Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        HttpServletResponse response = null;
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        annualAnalysisVO.setFileName("年度分析2");
        annualAnalysisVO.setViewFlag("1");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsCompareFlag(false);
        annualAnalysisVO.setGranularityType("P");
        List<String> l1NameList = new ArrayList<>();
        annualAnalysisVO.setL1NameList(l1NameList);
        annualAnalysisVO.setIsShowChildContent(true);
        annualAnalysisVO.setIsShowHistogramChart(true);
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(false);
        annualAnalysisVO.setIsTotalChildChart(true);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("11");
        groupCodeList.add("22");
        groupCodeList.add("33");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setOverseaFlag("G");
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","CEG");
        map.put("nextGroupName","name1");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);
        DmFoiImpExpRecordVO expRecordVO = new DmFoiImpExpRecordVO();
        expRecordVO.setFileSourceKey("122aa");
        expRecordVO.setFileName("年度分析1");
        expRecordVO.setRecordNum(3);
        expRecordVO.setFileSize("1");
        expRecordVO.setUserId("test1");
        ResultDataVO bgInfoList = new ResultDataVO();
        List<DmFocViewInfoVO> dataList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setLv0ProdListCode("code1");
        dataList.add(dmFocViewInfoVO);
        bgInfoList.setData(dataList);
        PowerMockito.doReturn(bgInfoList).when(annualCommonService).getBgInfoList(any());
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();

        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);

        DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv1DimensionSet(lv1DimensionSet);
        Set<String> lv2DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(dataPermissionsVO).when(commonService).getDimensionList(any(),any(),any());
        List<String> list = new ArrayList<>();
        list.add("10011");
        annualAnalysisVO.setTeamCodeList(list);
        ResultDataVO resultDataVO = annualAmpService.exportDetail(annualAnalysisVO, response);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void exportDetail6Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        HttpServletResponse response = null;
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        annualAnalysisVO.setFileName("年度分析2");
        annualAnalysisVO.setViewFlag("2");
        annualAnalysisVO.setGranularityType("P");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsCompareFlag(false);
        annualAnalysisVO.setIsShowChildContent(true);
        annualAnalysisVO.setIsShowHistogramChart(true);
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(false);
        annualAnalysisVO.setIsTotalChildChart(true);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("11");
        groupCodeList.add("22");
        groupCodeList.add("33");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setOverseaFlag("G");

        ResultDataVO bgInfoList = new ResultDataVO();
        List<DmFocViewInfoVO> dataList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setLv0ProdListCode("code1");
        dataList.add(dmFocViewInfoVO);
        bgInfoList.setData(dataList);
        PowerMockito.doReturn(bgInfoList).when(annualCommonService).getBgInfoList(any());
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();
        List<String> l2NameList = new ArrayList<>();
        annualAnalysisVO.setL2NameList(l2NameList);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","CEG");
        map.put("nextGroupName","name1");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);

        DmFoiImpExpRecordVO expRecordVO = new DmFoiImpExpRecordVO();
        expRecordVO.setFileSourceKey("122aa");
        expRecordVO.setFileName("年度分析1");
        expRecordVO.setRecordNum(3);
        expRecordVO.setFileSize("1");
        expRecordVO.setUserId("test1");

        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);

        DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv1DimensionSet(lv1DimensionSet);
        Set<String> lv2DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(dataPermissionsVO).when(commonService).getDimensionList(any(),any(),any());
        List<String> list = new ArrayList<>();
        list.add("10011");
        annualAnalysisVO.setTeamCodeList(list);
        ResultDataVO resultDataVO = annualAmpService.exportDetail(annualAnalysisVO, response);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void exportDetail7Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        HttpServletResponse response = null;
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        annualAnalysisVO.setFileName("年度分析2");
        annualAnalysisVO.setViewFlag("2");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsCompareFlag(false);
        annualAnalysisVO.setIsShowChildContent(true);
        annualAnalysisVO.setIsShowHistogramChart(true);
        annualAnalysisVO.setIsMultipleSelect(false);
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsTotalChildChart(true);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("11");
        groupCodeList.add("22");
        groupCodeList.add("33");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setOverseaFlag("G");

        ResultDataVO bgInfoList = new ResultDataVO();
        List<DmFocViewInfoVO> dataList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setLv0ProdListCode("code1");
        dataList.add(dmFocViewInfoVO);
        bgInfoList.setData(dataList);
        PowerMockito.doReturn(bgInfoList).when(annualCommonService).getBgInfoList(any());
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();
        List<String> lv1 = new ArrayList<>();
        lv1.add("lv1");
        annualAnalysisVO.setLv1ProdRdTeamCnName(lv1);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());
        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","CEG");
        map.put("nextGroupName","name1");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);

        DmFoiImpExpRecordVO expRecordVO = new DmFoiImpExpRecordVO();
        expRecordVO.setFileSourceKey("122aa");
        expRecordVO.setFileName("年度分析1");
        expRecordVO.setRecordNum(3);
        expRecordVO.setFileSize("1");
        expRecordVO.setUserId("test1");

        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);

        DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv1DimensionSet(lv1DimensionSet);
        Set<String> lv2DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(dataPermissionsVO).when(commonService).getDimensionList(any(),any(),any());
        List<String> list = new ArrayList<>();
        list.add("10011");
        annualAnalysisVO.setTeamCodeList(list);
        ResultDataVO resultDataVO = annualAmpService.exportDetail(annualAnalysisVO, response);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void exportDetail8Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        HttpServletResponse response = null;
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        annualAnalysisVO.setFileName("年度分析2");
        annualAnalysisVO.setViewFlag("2");
        annualAnalysisVO.setIsShowChildContent(true);
        annualAnalysisVO.setIsCompareFlag(false);
        annualAnalysisVO.setIsShowHistogramChart(true);
        annualAnalysisVO.setIsContainComb(false);
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(false);
        annualAnalysisVO.setIsTotalChildChart(true);
        annualAnalysisVO.setCostType("P");
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("11");
        groupCodeList.add("22");
        groupCodeList.add("33");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setOverseaFlag("G");

        ResultDataVO bgInfoList = new ResultDataVO();
        List<DmFocViewInfoVO> dataList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setLv0ProdListCode("code1");
        dataList.add(dmFocViewInfoVO);
        bgInfoList.setData(dataList);
        PowerMockito.doReturn(bgInfoList).when(annualCommonService).getBgInfoList(any());
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();

        List<String> lv1 = new ArrayList<>();
        lv1.add("lv1");
        annualAnalysisVO.setLv1ProdRdTeamCnName(lv1);
        List<String> lv2 = new ArrayList<>();
        lv2.add("lv2");
        annualAnalysisVO.setLv2ProdRdTeamCnName(lv2);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","CEG");
        map.put("nextGroupName","name1");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);
        DmFoiImpExpRecordVO expRecordVO = new DmFoiImpExpRecordVO();
        expRecordVO.setFileSourceKey("122aa");
        expRecordVO.setFileName("年度分析1");
        expRecordVO.setRecordNum(3);
        expRecordVO.setFileSize("1");
        expRecordVO.setUserId("test1");

        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);

        DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv1DimensionSet(lv1DimensionSet);
        Set<String> lv2DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv2DimensionSet(lv2DimensionSet);
        List<String> list = new ArrayList<>();
        list.add("10011");
        annualAnalysisVO.setTeamCodeList(list);
        PowerMockito.doReturn(dataPermissionsVO).when(commonService).getDimensionList(any(),any(),any());
        ResultDataVO resultDataVO = annualAmpService.exportDetail(annualAnalysisVO, response);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void exportDetail9Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        HttpServletResponse response = null;
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        annualAnalysisVO.setFileName("年度分析2");
        annualAnalysisVO.setViewFlag("2");
        annualAnalysisVO.setIsCompareFlag(false);
        annualAnalysisVO.setCostType("P");
        List<String> lv1 = new ArrayList<>();
        lv1.add("lv1");
        annualAnalysisVO.setLv1ProdRdTeamCnName(lv1);
        List<String> lv2 = new ArrayList<>();
        lv2.add("lv2");
        annualAnalysisVO.setLv2ProdRdTeamCnName(lv2);
        annualAnalysisVO.setLv3CegCnName("ceg1");
        annualAnalysisVO.setIsShowChildContent(false);
        annualAnalysisVO.setIsShowHistogramChart(true);
        annualAnalysisVO.setIsTotalChildChart(true);
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(false);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("11");
        groupCodeList.add("22");
        groupCodeList.add("33");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setOverseaFlag("G");

        ResultDataVO bgInfoList = new ResultDataVO();
        List<DmFocViewInfoVO> dataList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setLv0ProdListCode("code1");
        dataList.add(dmFocViewInfoVO);
        bgInfoList.setData(dataList);
        PowerMockito.doReturn(bgInfoList).when(annualCommonService).getBgInfoList(any());
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","CEG");
        map.put("nextGroupName","name1");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);

        DmFoiImpExpRecordVO expRecordVO = new DmFoiImpExpRecordVO();
        expRecordVO.setFileSourceKey("122aa");
        expRecordVO.setFileName("年度分析1");
        expRecordVO.setRecordNum(3);
        expRecordVO.setFileSize("1");
        expRecordVO.setUserId("test1");
        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);

        DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv1DimensionSet(lv1DimensionSet);
        Set<String> lv2DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(dataPermissionsVO).when(commonService).getDimensionList(any(),any(),any());
        List<String> list = new ArrayList<>();
        list.add("10011");
        annualAnalysisVO.setTeamCodeList(list);
        ResultDataVO resultDataVO = annualAmpService.exportDetail(annualAnalysisVO, response);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void exportDetail10Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        HttpServletResponse response = null;
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        annualAnalysisVO.setFileName("年度分析2");
        annualAnalysisVO.setViewFlag("2");
        annualAnalysisVO.setIsCompareFlag(false);
        annualAnalysisVO.setCostType("T");
        List<String> lv1 = new ArrayList<>();
        lv1.add("lv1");
        annualAnalysisVO.setLv1ProdRdTeamCnName(lv1);
        List<String> lv2 = new ArrayList<>();
        lv2.add("lv2");
        annualAnalysisVO.setLv2ProdRdTeamCnName(lv2);
        annualAnalysisVO.setLv3CegCnName("ceg1");
        annualAnalysisVO.setIsShowChildContent(false);
        annualAnalysisVO.setIsShowHistogramChart(true);
        annualAnalysisVO.setIsTotalChildChart(false);
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(false);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("11");
        groupCodeList.add("22");
        groupCodeList.add("33");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setOverseaFlag("G");

        ResultDataVO bgInfoList = new ResultDataVO();
        List<DmFocViewInfoVO> dataList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setLv0ProdListCode("code1");
        dataList.add(dmFocViewInfoVO);
        bgInfoList.setData(dataList);
        PowerMockito.doReturn(bgInfoList).when(annualCommonService).getBgInfoList(any());
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","CEG");
        map.put("nextGroupName","name1");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);

        DmFoiImpExpRecordVO expRecordVO = new DmFoiImpExpRecordVO();
        expRecordVO.setFileSourceKey("122aa");
        expRecordVO.setFileName("年度分析1");
        expRecordVO.setRecordNum(3);
        expRecordVO.setFileSize("1");
        expRecordVO.setUserId("test1");
        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);

        DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv1DimensionSet(lv1DimensionSet);
        Set<String> lv2DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(dataPermissionsVO).when(commonService).getDimensionList(any(),any(),any());
        List<String> list = new ArrayList<>();
        list.add("10011");
        annualAnalysisVO.setTeamCodeList(list);
        ResultDataVO resultDataVO = annualAmpService.exportDetail(annualAnalysisVO, response);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void exportDetail11Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        HttpServletResponse response = null;
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        annualAnalysisVO.setFileName("年度分析2");
        annualAnalysisVO.setViewFlag("3");
        annualAnalysisVO.setIsCompareFlag(false);
        annualAnalysisVO.setCostType("T");
        List<String> lv1 = new ArrayList<>();
        lv1.add("lv1");
        annualAnalysisVO.setLv1ProdRdTeamCnName(lv1);
        List<String> lv2 = new ArrayList<>();
        lv2.add("lv2");
        annualAnalysisVO.setLv2ProdRdTeamCnName(lv2);
        annualAnalysisVO.setLv3CegCnName("ceg1");
        annualAnalysisVO.setIsShowChildContent(true);
        annualAnalysisVO.setIsShowHistogramChart(false);
        annualAnalysisVO.setIsTotalChildChart(false);
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(false);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("11");
        groupCodeList.add("22");
        groupCodeList.add("33");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setOverseaFlag("G");

        ResultDataVO bgInfoList = new ResultDataVO();
        List<DmFocViewInfoVO> dataList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setLv0ProdListCode("code1");
        dataList.add(dmFocViewInfoVO);
        bgInfoList.setData(dataList);
        PowerMockito.doReturn(bgInfoList).when(annualCommonService).getBgInfoList(any());
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","CEG");
        map.put("nextGroupName","name1");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);

        DmFoiImpExpRecordVO expRecordVO = new DmFoiImpExpRecordVO();
        expRecordVO.setFileSourceKey("122aa");
        expRecordVO.setFileName("年度分析1");
        expRecordVO.setRecordNum(3);
        expRecordVO.setFileSize("1");
        expRecordVO.setUserId("test1");
        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);

        DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv1DimensionSet(lv1DimensionSet);
        Set<String> lv2DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(dataPermissionsVO).when(commonService).getDimensionList(any(),any(),any());
        List<String> list = new ArrayList<>();
        list.add("10011");
        annualAnalysisVO.setTeamCodeList(list);
        ResultDataVO resultDataVO = annualAmpService.exportDetail(annualAnalysisVO, response);
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void exportDetail12Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        HttpServletResponse response = null;
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        annualAnalysisVO.setFileName("年度分析2");
        annualAnalysisVO.setViewFlag("3");
        annualAnalysisVO.setIsCompareFlag(false);
        annualAnalysisVO.setCostType("M");
        List<String> lv1 = new ArrayList<>();
        lv1.add("lv1");
        annualAnalysisVO.setLv1ProdRdTeamCnName(lv1);
        List<String> lv2 = new ArrayList<>();
        lv2.add("lv2");
        annualAnalysisVO.setLv2ProdRdTeamCnName(lv2);
        annualAnalysisVO.setLv3CegCnName("ceg1");
        annualAnalysisVO.setIsShowChildContent(true);
        annualAnalysisVO.setIsShowHistogramChart(false);
        annualAnalysisVO.setIsTotalChildChart(true);
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsMultipleSelect(true);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("11");
        groupCodeList.add("22");
        groupCodeList.add("33");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setOverseaFlag("G");

        ResultDataVO bgInfoList = new ResultDataVO();
        List<DmFocViewInfoVO> dataList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setLv0ProdListCode("code1");
        dataList.add(dmFocViewInfoVO);
        bgInfoList.setData(dataList);
        PowerMockito.doReturn(bgInfoList).when(annualCommonService).getBgInfoList(any());
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","CEG");
        map.put("nextGroupName","name1");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);

        DmFoiImpExpRecordVO expRecordVO = new DmFoiImpExpRecordVO();
        expRecordVO.setFileSourceKey("122aa");
        expRecordVO.setFileName("年度分析1");
        expRecordVO.setRecordNum(3);
        expRecordVO.setFileSize("1");
        expRecordVO.setUserId("test1");
        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);

        DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv1DimensionSet(lv1DimensionSet);
        Set<String> lv2DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(dataPermissionsVO).when(commonService).getDimensionList(any(),any(),any());
        List<String> list = new ArrayList<>();
        list.add("10011");
        annualAnalysisVO.setTeamCodeList(list);
        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.exportDetail(annualAnalysisVO, response);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void exportDetail13Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        HttpServletResponse response = null;
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        annualAnalysisVO.setFileName("年度分析2");
        annualAnalysisVO.setViewFlag("2");
        annualAnalysisVO.setCostType("P");
        annualAnalysisVO.setIsCompareFlag(true);
        annualAnalysisVO.setIsShowChildContent(true);
        annualAnalysisVO.setIsShowHistogramChart(true);
        annualAnalysisVO.setIsMultipleSelect(false);
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsTotalChildChart(true);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("11");
        groupCodeList.add("22");
        groupCodeList.add("33");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setOverseaFlag("G");
        List<AnnualParamVO> annualParamList = new ArrayList<>();
        AnnualParamVO analysisVO = new AnnualParamVO();
        analysisVO.setGroupCnNameList(groupCodeList);
        annualParamList.add(analysisVO);
        annualAnalysisVO.setAnnualParamList(annualParamList);

        ResultDataVO bgInfoList = new ResultDataVO();
        List<DmFocViewInfoVO> dataList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setLv0ProdListCode("code1");
        dataList.add(dmFocViewInfoVO);
        bgInfoList.setData(dataList);
        PowerMockito.doReturn(bgInfoList).when(annualCommonService).getBgInfoList(any());
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();
        List<String> lv1 = new ArrayList<>();
        lv1.add("lv1");
        annualAnalysisVO.setLv1ProdRdTeamCnName(lv1);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","CEG");
        map.put("nextGroupName","name1");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);

        DmFoiImpExpRecordVO expRecordVO = new DmFoiImpExpRecordVO();
        expRecordVO.setFileSourceKey("122aa");
        expRecordVO.setFileName("年度分析1");
        expRecordVO.setRecordNum(3);
        expRecordVO.setFileSize("1");
        expRecordVO.setUserId("test1");

        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);

        DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv1DimensionSet(lv1DimensionSet);
        Set<String> lv2DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(dataPermissionsVO).when(commonService).getDimensionList(any(),any(),any());
        List<String> list = new ArrayList<>();
        list.add("10011");
        annualAnalysisVO.setTeamCodeList(list);

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.exportDetail(annualAnalysisVO, response);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    @Test
    public void exportDetail14Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        HttpServletResponse response = null;
        DmFocVersionInfoDTO annualVersion = new DmFocVersionInfoDTO();
        annualVersion.setVersionId(11L);
        annualAnalysisVO.setFileName("年度分析2");
        annualAnalysisVO.setViewFlag("2");
        annualAnalysisVO.setCostType("M");
        annualAnalysisVO.setIsCompareFlag(true);
        annualAnalysisVO.setIsShowChildContent(true);
        annualAnalysisVO.setIsShowHistogramChart(false);
        annualAnalysisVO.setIsMultipleSelect(false);
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setIsTotalChildChart(true);
        List<String> groupCodeList = new ArrayList<>();
        groupCodeList.add("11");
        groupCodeList.add("22");
        groupCodeList.add("33");
        annualAnalysisVO.setGroupCodeList(groupCodeList);
        annualAnalysisVO.setOverseaFlag("G");
        List<AnnualParamVO> annualParamList = new ArrayList<>();
        AnnualParamVO analysisVO = new AnnualParamVO();
        analysisVO.setGroupCnNameList(groupCodeList);
        annualParamList.add(analysisVO);
        annualAnalysisVO.setAnnualParamList(annualParamList);

        ResultDataVO bgInfoList = new ResultDataVO();
        List<DmFocViewInfoVO> dataList = new ArrayList<>();
        DmFocViewInfoVO dmFocViewInfoVO = new DmFocViewInfoVO();
        dmFocViewInfoVO.setLv0ProdListCode("code1");
        dataList.add(dmFocViewInfoVO);
        bgInfoList.setData(dataList);
        PowerMockito.doReturn(bgInfoList).when(annualCommonService).getBgInfoList(any());
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();
        List<String> lv1 = new ArrayList<>();
        lv1.add("lv1");
        annualAnalysisVO.setLv1ProdRdTeamCnName(lv1);
        PowerMockito.doReturn(annualVersion).when(dmFocVersionDao).findAnnualVersion(any());

        mockStatic(FcstIndexUtil.class);
        Map map = new HashMap();
        map.put("nextGroupLevel","CEG");
        map.put("nextGroupName","name1");
        when(FcstIndexUtil.class, "getNextGroupLevel", any()).thenReturn(map);

        DmFoiImpExpRecordVO expRecordVO = new DmFoiImpExpRecordVO();
        expRecordVO.setFileSourceKey("122aa");
        expRecordVO.setFileName("年度分析1");
        expRecordVO.setRecordNum(3);
        expRecordVO.setFileSize("1");
        expRecordVO.setUserId("test1");

        when(excelUtil.expSelectColumnExcel(any(),any())).thenReturn(expRecordVO);

        DataPermissionsVO dataPermissionsVO = new DataPermissionsVO();
        Set<String> lv1DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv1DimensionSet(lv1DimensionSet);
        Set<String> lv2DimensionSet  = new HashSet<>();
        dataPermissionsVO.setLv2DimensionSet(lv2DimensionSet);
        PowerMockito.doReturn(dataPermissionsVO).when(commonService).getDimensionList(any(),any(),any());
        List<String> list = new ArrayList<>();
        list.add("10011");
        annualAnalysisVO.setTeamCodeList(list);

        ResultDataVO resultDataVO = new ResultDataVO();
        try {
            resultDataVO = annualAmpService.exportDetail(annualAnalysisVO, response);
        } catch (Exception e) {
            LOGGER.error("入参清单数据版本为空");
        }
        Assertions.assertNotNull(resultDataVO);
    }

    private List<String> getThreeYears() {
        List<String> threeYears = new ArrayList<>();
        for (int i = 0; i <= 2; i++) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy");
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());
            calendar.add(Calendar.YEAR, -(2 - i));
            Date date = calendar.getTime();
            String year = simpleDateFormat.format(date);
            threeYears.add(year);
        }
        return threeYears;
    }

    @Test
    public void industryCostList1Test() throws Exception {
        int intArg = arg.getInteger("int");
        int intArg0 = arg.getInteger("int");
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();
        DmFocVersionInfoDTO dmFocVersionInfoDTO = JSONObject.parseObject(arg.getString("DmFocVersionInfoDTO"),DmFocVersionInfoDTO.class);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),DmFocAnnualAmpVO.class);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList0 = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),DmFocAnnualAmpVO.class);
        PagedResult<DmFocAnnualAmpVO> pagedResult = JSONObject.parseObject(arg.getString("PagedResult<DmFocAnnualAmpVO>"),new TypeReference<PagedResult<DmFocAnnualAmpVO>>(){});
        AnnualAnalysisVO annualAnalysisVO = JSONObject.parseObject(arg.getString("AnnualAnalysisVO"),AnnualAnalysisVO.class);
        when(annualAmpDao.industryCostAllList(any(AnnualAnalysisVO.class))).thenReturn(dmFocAnnualAmpVOList);
        when(annualAmpDao.industryCostAllItemCount(any(AnnualAnalysisVO.class))).thenReturn(intArg);
        when(annualAmpDao.industryCostCombItemCount(any(AnnualAnalysisVO.class))).thenReturn(intArg0);
        when(annualAmpDao.industryCombCharPage(any(AnnualAnalysisVO.class),any(PageVO.class))).thenReturn(pagedResult);
        when(annualAmpDao.industryCostList(any(AnnualAnalysisVO.class))).thenReturn(dmFocAnnualAmpVOList0);
        when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(dmFocVersionInfoDTO);
        ResultDataVO result = annualAmpService.industryCostList(annualAnalysisVO);
        Assertions.assertNotNull(result);
    }


    @Test
    public void profitMutliChartCnNameTest() throws Exception {
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),DmFocAnnualAmpVO.class);
        AnnualAnalysisVO annualAnalysisVO = JSONObject.parseObject(arg.getString("AnnualAnalysisVO"),AnnualAnalysisVO.class);
        annualAnalysisVO.setGroupLevel("L2");
        Whitebox.invokeMethod(annualAmpService, "profitMutliChartCnName", annualAnalysisVO, dmFocAnnualAmpVOList);
        assertThatNoException();
        dmFocAnnualAmpVOList.forEach(obj -> obj.setL1Name("其他"));
        Whitebox.invokeMethod(annualAmpService, "profitMutliChartCnName", annualAnalysisVO, dmFocAnnualAmpVOList);
        assertThatNoException();
        annualAnalysisVO.setGroupLevel("L1");
        Whitebox.invokeMethod(annualAmpService, "profitMutliChartCnName", annualAnalysisVO, dmFocAnnualAmpVOList);
        assertThatNoException();
        dmFocAnnualAmpVOList.forEach(obj -> obj.setL1Name("test"));
        Whitebox.invokeMethod(annualAmpService, "profitMutliChartCnName", annualAnalysisVO, dmFocAnnualAmpVOList);
        assertThatNoException();
    }

    @Test
    public void allIndustryChartCnNameTest() throws Exception {
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),DmFocAnnualAmpVO.class);
        AnnualAnalysisVO annualAnalysisVO = JSONObject.parseObject(arg.getString("AnnualAnalysisVO"),AnnualAnalysisVO.class);
        annualAnalysisVO.setGroupLevel("L2");
        Whitebox.invokeMethod(annualAmpService, "allIndustryChartCnName", annualAnalysisVO, dmFocAnnualAmpVOList);
        assertThatNoException();
        dmFocAnnualAmpVOList.forEach(obj -> obj.setL1Name("其他"));
        Whitebox.invokeMethod(annualAmpService, "allIndustryChartCnName", annualAnalysisVO, dmFocAnnualAmpVOList);
        assertThatNoException();
        annualAnalysisVO.setGroupLevel("L1");
        Whitebox.invokeMethod(annualAmpService, "allIndustryChartCnName", annualAnalysisVO, dmFocAnnualAmpVOList);
        assertThatNoException();
        dmFocAnnualAmpVOList.forEach(obj -> obj.setL1Name("test"));
        Whitebox.invokeMethod(annualAmpService, "allIndustryChartCnName", annualAnalysisVO, dmFocAnnualAmpVOList);
        assertThatNoException();
        dmFocAnnualAmpVOList.clear();
        Whitebox.invokeMethod(annualAmpService, "allIndustryChartCnName", annualAnalysisVO, dmFocAnnualAmpVOList);
        assertThatNoException();
        annualAnalysisVO.setGroupLevel("L2");
        Whitebox.invokeMethod(annualAmpService, "allIndustryChartCnName", annualAnalysisVO, dmFocAnnualAmpVOList);
        assertThatNoException();
    }

    @Test
    public void dimensionAllChartCnNameTest() throws Exception {
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),DmFocAnnualAmpVO.class);
        AnnualAnalysisVO annualAnalysisVO = JSONObject.parseObject(arg.getString("AnnualAnalysisVO"),AnnualAnalysisVO.class);
        annualAnalysisVO.setGroupLevel("DIMENSION");
        Whitebox.invokeMethod(annualAmpService, "dimensionAllChartCnName", annualAnalysisVO, dmFocAnnualAmpVOList);
        assertThatNoException();
        annualAnalysisVO.setGroupLevel("SUBCATEGORY");
        Whitebox.invokeMethod(annualAmpService, "dimensionAllChartCnName", annualAnalysisVO, dmFocAnnualAmpVOList);
        assertThatNoException();
        annualAnalysisVO.setGroupLevel("SUB_DETAIL");
        Whitebox.invokeMethod(annualAmpService, "dimensionAllChartCnName", annualAnalysisVO, dmFocAnnualAmpVOList);
        assertThatNoException();
    }

    @Test
    public void dimensionMutliSubCharCnNameTest() throws Exception {
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),DmFocAnnualAmpVO.class);
        AnnualAnalysisVO annualAnalysisVO = JSONObject.parseObject(arg.getString("AnnualAnalysisVO"),AnnualAnalysisVO.class);
        annualAnalysisVO.setGroupLevel("DIMENSION");
        Whitebox.invokeMethod(annualAmpService, "dimensionMutliSubCharCnName", annualAnalysisVO, dmFocAnnualAmpVOList);
        assertThatNoException();
        annualAnalysisVO.setGroupLevel("SUBCATEGORY");
        Whitebox.invokeMethod(annualAmpService, "dimensionMutliSubCharCnName", annualAnalysisVO, dmFocAnnualAmpVOList);
        assertThatNoException();
        annualAnalysisVO.setGroupLevel("SUB_DETAIL");
        Whitebox.invokeMethod(annualAmpService, "dimensionMutliSubCharCnName", annualAnalysisVO, dmFocAnnualAmpVOList);
        assertThatNoException();
    }

    @Test
    public void profitGroupCnNameStrTest() throws Exception {
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),DmFocAnnualAmpVO.class);
        AnnualAnalysisVO annualAnalysisVO = JSONObject.parseObject(arg.getString("AnnualAnalysisVO"),AnnualAnalysisVO.class);
        annualAnalysisVO.setGranularityType("C");
        annualAnalysisVO.setMultiLevel("L1");
        dmFocAnnualAmpVOList.forEach(obj -> obj.setL1Name("其他"));
        Whitebox.invokeMethod(annualAmpService, "profitGroupCnNameStr", annualAnalysisVO,
                dmFocAnnualAmpVOList, "all");
        assertThatNoException();
        Whitebox.invokeMethod(annualAmpService, "profitGroupCnNameStr", annualAnalysisVO,
                dmFocAnnualAmpVOList, "test");
        assertThatNoException();
        Whitebox.invokeMethod(annualAmpService, "profitGroupCnNameStr", annualAnalysisVO,
                dmFocAnnualAmpVOList, "test");
        assertThatNoException();
        dmFocAnnualAmpVOList.forEach(obj -> obj.setL1Name("其他11"));
        Whitebox.invokeMethod(annualAmpService, "profitGroupCnNameStr", annualAnalysisVO,
                dmFocAnnualAmpVOList, "test");
        assertThatNoException();

        annualAnalysisVO.setMultiLevel("L2");
        Whitebox.invokeMethod(annualAmpService, "profitGroupCnNameStr", annualAnalysisVO,
                dmFocAnnualAmpVOList, "test");
        assertThatNoException();
        Whitebox.invokeMethod(annualAmpService, "profitGroupCnNameStr", annualAnalysisVO,
                dmFocAnnualAmpVOList, "test");
        assertThatNoException();

        annualAnalysisVO.setMultiLevel("L3");
        Whitebox.invokeMethod(annualAmpService, "profitGroupCnNameStr", annualAnalysisVO,
                dmFocAnnualAmpVOList, "test");
        assertThatNoException();
    }

    @Test
    public void setPromptMessageTest() throws Exception {
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),DmFocAnnualAmpVO.class);
        dmFocAnnualAmpVOList.forEach(obj -> obj.setAnnualAmp("0.0"));
        Whitebox.invokeMethod(annualAmpService, "setPromptMessage", dmFocAnnualAmpVOList, "test", "ITEM");
        assertThatNoException();
        dmFocAnnualAmpVOList.forEach(obj -> obj.setAnnualAmp("0.0"));
        Whitebox.invokeMethod(annualAmpService, "setPromptMessage", dmFocAnnualAmpVOList, "test", "test");
        assertThatNoException();
    }

    @Test
    public void distinguishIfCombineForMutilTest() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = JSONObject.parseObject(arg.getString("AnnualAnalysisVO"),AnnualAnalysisVO.class);
        Whitebox.invokeMethod(annualAmpService, "distinguishIfCombineForMutil", annualAnalysisVO);
        assertThatNoException();
    }

    @Test
    public void multiChildNotContainsCustomCombTest() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = JSONObject.parseObject(arg.getString("AnnualAnalysisVO"),AnnualAnalysisVO.class);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),DmFocAnnualAmpVO.class);
        when(annualAmpDao.findGroupCodeOrderByWeight(any(AnnualAnalysisVO.class))).thenReturn(new ArrayList<>());
        when(annualAmpDao.multiIndustryCostChart(any(AnnualAnalysisVO.class))).thenReturn(dmFocAnnualAmpVOList);
        annualAnalysisVO.setIsMultipleSelect(false);
        annualAnalysisVO.setReverseLv1Flag(true);
        Whitebox.invokeMethod(annualAmpService, "multiChildNotContainsCustomComb", annualAnalysisVO, new PageVO());
        verify(commonService, atLeastOnce()).setProdRndTeamCode(any(MonthAnalysisVO.class));
    }

    @Test
    public void multiChildNotContainsCombExcelTest() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = JSONObject.parseObject(arg.getString("AnnualAnalysisVO"),AnnualAnalysisVO.class);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),DmFocAnnualAmpVO.class);
        when(annualAmpDao.multiIndustryCostChartMultiSelect(any(AnnualAnalysisVO.class))).thenReturn(dmFocAnnualAmpVOList);
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setReverseLv1Flag(true);
        Whitebox.invokeMethod(annualAmpService, "multiChildNotContainsCombExcel", annualAnalysisVO, "test");
        verify(commonService, atLeastOnce()).setProdRndTeamCode(any(MonthAnalysisVO.class));
        annualAnalysisVO.setIsMultipleSelect(false);
        annualAnalysisVO.setReverseLv1Flag(false);
        Whitebox.invokeMethod(annualAmpService, "multiChildNotContainsCombExcel", annualAnalysisVO, "test");
        verify(commonService, atLeastOnce()).setProdRndTeamCode(any(MonthAnalysisVO.class));
    }

    @Test
    public void overviewListContainsCombTest() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = JSONObject.parseObject(arg.getString("AnnualAnalysisVO"),AnnualAnalysisVO.class);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),DmFocAnnualAmpVO.class);
        PagedResult<DmFocAnnualAmpVO> pageResult = new PagedResult<>();
        pageResult.setResult(dmFocAnnualAmpVOList);
        when(annualAmpDao.industryCombCharPage(any(AnnualAnalysisVO.class), any(PageVO.class))).thenReturn(pageResult);
        annualAnalysisVO.setIsMultipleSelect(false);
        Whitebox.invokeMethod(annualAmpService, "overviewListContainsComb", annualAnalysisVO, new PageVO());
        verify(annualAmpDao, atLeastOnce()).industryCombCharPage(any(AnnualAnalysisVO.class), any(PageVO.class));
        dmFocAnnualAmpVOList.forEach(obj -> {obj.setParentCnName("ddddsfds");});
        Whitebox.invokeMethod(annualAmpService, "overviewListContainsComb", annualAnalysisVO, new PageVO());
        verify(annualAmpDao, atLeastOnce()).industryCombCharPage(any(AnnualAnalysisVO.class), any(PageVO.class));
        dmFocAnnualAmpVOList.forEach(obj -> {obj.setParentCnName(null);});
        Whitebox.invokeMethod(annualAmpService, "overviewListContainsComb", annualAnalysisVO, new PageVO());
        verify(annualAmpDao, atLeastOnce()).industryCombCharPage(any(AnnualAnalysisVO.class), any(PageVO.class));
    }

    @Test
    public void mutliCombSelectAnnualAmpResultTest() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = JSONObject.parseObject(arg.getString("AnnualAnalysisVO"),AnnualAnalysisVO.class);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),DmFocAnnualAmpVO.class);

        List<DmFocAnnualAmpVO> allItemList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),DmFocAnnualAmpVO.class);
        when(annualAmpDao.industryCostAllList(any(AnnualAnalysisVO.class))).thenReturn(allItemList);

        allItemList.forEach(obj -> obj.setIsHasComb("normal"));
        Whitebox.invokeMethod(annualAmpService, "mutliCombSelectAnnualAmpResult", annualAnalysisVO, new PageVO(), dmFocAnnualAmpVOList);
        verify(annualAmpDao, atLeastOnce()).industryCostAllItemCount(any(AnnualAnalysisVO.class));
        allItemList.forEach(obj -> obj.setIsHasComb("comb"));
        Whitebox.invokeMethod(annualAmpService, "mutliCombSelectAnnualAmpResult", annualAnalysisVO, new PageVO(), dmFocAnnualAmpVOList);
        verify(annualAmpDao, atLeastOnce()).industryCostAllItemCount(any(AnnualAnalysisVO.class));

        annualAnalysisVO.setParentCodeList(new ArrayList<>());
        Whitebox.invokeMethod(annualAmpService, "mutliCombSelectAnnualAmpResult", annualAnalysisVO, new PageVO(), dmFocAnnualAmpVOList);
        verify(annualAmpDao, atLeastOnce()).industryCostCombItemCount(any(AnnualAnalysisVO.class));

        annualAnalysisVO.setCombinaCodeList(new ArrayList<>());
        Whitebox.invokeMethod(annualAmpService, "mutliCombSelectAnnualAmpResult", annualAnalysisVO, new PageVO(), dmFocAnnualAmpVOList);
        verify(annualAmpDao, atLeastOnce()).industryCostCombItemCount(any(AnnualAnalysisVO.class));
    }

    @Test
    public void industryCostListContainsCombTest() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = JSONObject.parseObject(arg.getString("AnnualAnalysisVO"),AnnualAnalysisVO.class);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),
                DmFocAnnualAmpVO.class);
        when(annualAmpDao.industryCombCharlist(any(AnnualAnalysisVO.class))).thenReturn(dmFocAnnualAmpVOList);
        when(annualAmpDao.industryCostAllList(any(AnnualAnalysisVO.class))).thenReturn(dmFocAnnualAmpVOList);
        when(annualAmpDao.industryCostNormalList(any(AnnualAnalysisVO.class))).thenReturn(dmFocAnnualAmpVOList);
        when(annualAmpDao.industryCombCharlist(any(AnnualAnalysisVO.class))).thenReturn(dmFocAnnualAmpVOList);
        annualAnalysisVO.setIsMultipleSelect(false);
        List<DmFocAnnualAmpVO> result = Whitebox.invokeMethod(annualAmpService, "industryCostListContainsComb", annualAnalysisVO);
        Assertions.assertNotNull(result);
        annualAnalysisVO.setIsMultipleSelect(true);
        result = Whitebox.invokeMethod(annualAmpService, "industryCostListContainsComb", annualAnalysisVO);
        Assertions.assertNotNull(result);
        List<String> codes = annualAnalysisVO.getCombinaCodeList();
        annualAnalysisVO.setCombinaCodeList(new ArrayList<>());
        result = Whitebox.invokeMethod(annualAmpService, "industryCostListContainsComb", annualAnalysisVO);
        Assertions.assertNotNull(result);
        annualAnalysisVO.setParentCodeList(new ArrayList<>());
        result = Whitebox.invokeMethod(annualAmpService, "industryCostListContainsComb", annualAnalysisVO);
        Assertions.assertNotNull(result);
        annualAnalysisVO.setParentCodeList(new ArrayList<>());
        annualAnalysisVO.setCombinaCodeList(codes);
        result = Whitebox.invokeMethod(annualAmpService, "industryCostListContainsComb", annualAnalysisVO);
        Assertions.assertNotNull(result);
    }

    @Test
    public void setCombParentCnNameTest() throws Exception {
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),DmFocAnnualAmpVO.class);
        dmFocAnnualAmpVOList.forEach(obj -> obj.setParentCnName("ddd"));
        Whitebox.invokeMethod(annualAmpService, "setCombParentCnName", dmFocAnnualAmpVOList);
        assertThatNoException();
        dmFocAnnualAmpVOList.forEach(obj -> obj.setParentCnName(null));
        Whitebox.invokeMethod(annualAmpService, "setCombParentCnName", dmFocAnnualAmpVOList);
        assertThatNoException();
    }

    @Test
    public void purchaseLevelOrderByWithParentTest() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = JSONObject.parseObject(arg.getString("AnnualAnalysisVO"),AnnualAnalysisVO.class);
        annualAnalysisVO.setGranularityType("P");
        Whitebox.invokeMethod(annualAmpService, "purchaseLevelOrderByWithParent", annualAnalysisVO);
        assertThatNoException();
        annualAnalysisVO.setParentLevel("LV1");
        annualAnalysisVO.setCostType("P");
        Whitebox.invokeMethod(annualAmpService, "purchaseLevelOrderByWithParent", annualAnalysisVO);
        verify(annualAmpDao, atLeastOnce()).findProdteamCodeOrderByWeight(any(AnnualAnalysisVO.class));
        annualAnalysisVO.setParentLevel("LV2");
        annualAnalysisVO.setCostType("P");
        Whitebox.invokeMethod(annualAmpService, "purchaseLevelOrderByWithParent", annualAnalysisVO);
        verify(annualAmpDao, atLeastOnce()).findProdteamCodeOrderByWeight(any(AnnualAnalysisVO.class));

        annualAnalysisVO.setParentLevel("L1");
        annualAnalysisVO.setCostType("P");
        Whitebox.invokeMethod(annualAmpService, "purchaseLevelOrderByWithParent", annualAnalysisVO);
        verify(annualAmpDao, atLeastOnce()).findGranularityTypeOrderByWeight(any(AnnualAnalysisVO.class));
        annualAnalysisVO.setParentLevel("L2");
        annualAnalysisVO.setCostType("P");
        Whitebox.invokeMethod(annualAmpService, "purchaseLevelOrderByWithParent", annualAnalysisVO);
        verify(annualAmpDao, atLeastOnce()).findGranularityTypeOrderByWeight(any(AnnualAnalysisVO.class));


        annualAnalysisVO.setGranularityType("D");
        Whitebox.invokeMethod(annualAmpService, "purchaseLevelOrderByWithParent", annualAnalysisVO);
        assertThatNoException();
        annualAnalysisVO.setParentLevel("SUB_DETAIL");
        annualAnalysisVO.setCostType("P");
        Whitebox.invokeMethod(annualAmpService, "purchaseLevelOrderByWithParent", annualAnalysisVO);
        verify(annualAmpDao, atLeastOnce()).findGranularityTypeOrderByWeight(any(AnnualAnalysisVO.class));
        annualAnalysisVO.setParentLevel("SUBCATEGORY");
        annualAnalysisVO.setCostType("P");
        Whitebox.invokeMethod(annualAmpService, "purchaseLevelOrderByWithParent", annualAnalysisVO);
        verify(annualAmpDao, atLeastOnce()).findGranularityTypeOrderByWeight(any(AnnualAnalysisVO.class));
        annualAnalysisVO.setParentLevel("DIMENSION");
        annualAnalysisVO.setCostType("P");
        Whitebox.invokeMethod(annualAmpService, "purchaseLevelOrderByWithParent", annualAnalysisVO);
        verify(annualAmpDao, atLeastOnce()).findGranularityTypeOrderByWeight(any(AnnualAnalysisVO.class));
    }

    @Test
    public void mutilHasCustomCombAndNormalTest() throws Exception {
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),DmFocAnnualAmpVO.class);
        List<DmFocViewInfoVO> itemList = JSONObject.parseArray(arg.getString("List<DmFocViewInfoVO>"), DmFocViewInfoVO.class);
        AnnualAnalysisVO annualAnalysisVO = JSONObject.parseObject(arg.getString("AnnualAnalysisVO"),AnnualAnalysisVO.class);
        when(dmFocViewInfoDao.getAllItemCode(any(AnnualAnalysisVO.class))).thenReturn(itemList);
        when(dmFocViewInfoDao.getAllCombItemCode(any(AnnualAnalysisVO.class))).thenReturn(itemList);
        when(dmFocViewInfoDao.getAllNormalItemCode(any(AnnualAnalysisVO.class))).thenReturn(itemList);

        Whitebox.invokeMethod(annualAmpService, "mutilHasCustomCombAndNormal", annualAnalysisVO, new PageVO(),
                dmFocAnnualAmpVOList);
        verify(dmFocViewInfoDao, atLeastOnce()).getAllItemCode(any(AnnualAnalysisVO.class));

        List<String> codes = annualAnalysisVO.getCombinaCodeList();
        annualAnalysisVO.setCombinaCodeList(new ArrayList<>());
        Whitebox.invokeMethod(annualAmpService, "mutilHasCustomCombAndNormal", annualAnalysisVO, new PageVO(),
                dmFocAnnualAmpVOList);
        verify(dmFocViewInfoDao, atLeastOnce()).getAllNormalItemCode(any(AnnualAnalysisVO.class));

        annualAnalysisVO.setParentCodeList(new ArrayList<>());
        Whitebox.invokeMethod(annualAmpService, "mutilHasCustomCombAndNormal", annualAnalysisVO, new PageVO(),
                dmFocAnnualAmpVOList);
        verify(dmFocViewInfoDao, atLeastOnce()).getAllNormalItemCode(any(AnnualAnalysisVO.class));

        annualAnalysisVO.setParentCodeList(new ArrayList<>());
        annualAnalysisVO.setCombinaCodeList(codes);
        Whitebox.invokeMethod(annualAmpService, "mutilHasCustomCombAndNormal", annualAnalysisVO, new PageVO(),
                dmFocAnnualAmpVOList);
        verify(dmFocViewInfoDao, atLeastOnce()).getAllCombItemCode(any(AnnualAnalysisVO.class));
    }

    @Test
    public void multiChildContainsCombExcelTest() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = JSONObject.parseObject(arg.getString("AnnualAnalysisVO"),AnnualAnalysisVO.class);
        Whitebox.invokeMethod(annualAmpService, "multiChildContainsCombExcel", annualAnalysisVO);
        assertThatNoException();
        annualAnalysisVO.setIsMultipleSelect(true);
        Whitebox.invokeMethod(annualAmpService, "multiChildContainsCombExcel", annualAnalysisVO);
        assertThatNoException();
    }

    @Test
    public void orderColumnAndLimitPageTest() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = JSONObject.parseObject(arg.getString("AnnualAnalysisVO"),AnnualAnalysisVO.class);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),DmFocAnnualAmpVO.class);
        List<DmFocAnnualAmpVO> annList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),
                DmFocAnnualAmpVO.class);
        dmFocAnnualAmpVOList.addAll(annList);
        annualAnalysisVO.setOrderColumn("groupCnName");
        PageVO page = new PageVO();
        List<DmFocAnnualAmpVO> result = Whitebox.invokeMethod(annualAmpService, "orderColumnAndLimitPage",
                dmFocAnnualAmpVOList, annualAnalysisVO, page);
        Assertions.assertNotNull(result);
        annualAnalysisVO.setOrderColumn("groupCode");
        result = Whitebox.invokeMethod(annualAmpService, "orderColumnAndLimitPage",dmFocAnnualAmpVOList, annualAnalysisVO,
                page);
        Assertions.assertNotNull(result);
        annualAnalysisVO.setOrderColumn("annualAmp");
        result = Whitebox.invokeMethod(annualAmpService, "orderColumnAndLimitPage",dmFocAnnualAmpVOList, annualAnalysisVO,
                page);
        Assertions.assertNotNull(result);
        annualAnalysisVO.setOrderMethod("asc");
        annualAnalysisVO.setOrderColumn("groupCnName");
        result = Whitebox.invokeMethod(annualAmpService, "orderColumnAndLimitPage",dmFocAnnualAmpVOList, annualAnalysisVO,
                page);
        Assertions.assertNotNull(result);
        annualAnalysisVO.setOrderColumn("groupCode");
        result = Whitebox.invokeMethod(annualAmpService, "orderColumnAndLimitPage",dmFocAnnualAmpVOList, annualAnalysisVO,
                page);
        Assertions.assertNotNull(result);
        annualAnalysisVO.setOrderColumn("annualAmp");
        result = Whitebox.invokeMethod(annualAmpService, "orderColumnAndLimitPage",dmFocAnnualAmpVOList, annualAnalysisVO,
                page);
        Assertions.assertNotNull(result);
        dmFocAnnualAmpVOList.forEach(obj -> obj.setAnnualAmp("0*"));
        result = Whitebox.invokeMethod(annualAmpService, "orderColumnAndLimitPage",dmFocAnnualAmpVOList, annualAnalysisVO,
                page);
        Assertions.assertNotNull(result);
    }

    @Test
    public void sortWeightBarAndRecordListTest() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = JSONObject.parseObject(arg.getString("AnnualAnalysisVO"),
                AnnualAnalysisVO.class);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),
                DmFocAnnualAmpVO.class);
        List<DmFocAnnualAmpVO> annList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),
                DmFocAnnualAmpVO.class);
        dmFocAnnualAmpVOList.addAll(annList);
        List<DmFocAnnualAmpVO> result = Whitebox.invokeMethod(annualAmpService, "sortWeightBarAndRecordList",
                dmFocAnnualAmpVOList, annualAnalysisVO);
        Assertions.assertNotNull(result);
        dmFocAnnualAmpVOList.forEach(obj -> obj.setWeightRate("0*"));
        result = Whitebox.invokeMethod(annualAmpService, "sortWeightBarAndRecordList", dmFocAnnualAmpVOList,
                annualAnalysisVO);
        Assertions.assertNotNull(result);
    }

    @Test
    public void sortByWeightAnnualBarTest() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = JSONObject.parseObject(arg.getString("AnnualAnalysisVO"),
                AnnualAnalysisVO.class);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),
                DmFocAnnualAmpVO.class);
        List<DmFocAnnualAmpVO> annList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),
                DmFocAnnualAmpVO.class);
        dmFocAnnualAmpVOList.addAll(annList);
        List<DmFocAnnualAmpVO> result = Whitebox.invokeMethod(annualAmpService, "sortByWeightAnnualBar",
                dmFocAnnualAmpVOList, annualAnalysisVO, false);
        Assertions.assertNotNull(result);
        result = Whitebox.invokeMethod(annualAmpService, "sortByWeightAnnualBar",
                dmFocAnnualAmpVOList, annualAnalysisVO, true);
        Assertions.assertNotNull(result);
        dmFocAnnualAmpVOList.forEach(obj -> obj.setWeightAnnualAmpPercent("0*"));
        result = Whitebox.invokeMethod(annualAmpService, "sortByWeightAnnualBar", dmFocAnnualAmpVOList,
                annualAnalysisVO, false);
        Assertions.assertNotNull(result);

        annualAnalysisVO.setGroupLevel("ITEM");
        result = Whitebox.invokeMethod(annualAmpService, "sortByWeightAnnualBar", dmFocAnnualAmpVOList,
                annualAnalysisVO, false);
        Assertions.assertNotNull(result);
        result = Whitebox.invokeMethod(annualAmpService, "sortByWeightAnnualBar", dmFocAnnualAmpVOList,
                annualAnalysisVO, true);
        Assertions.assertNotNull(result);
    }

    @Test
    public void sortByWeightRateBarTest() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = JSONObject.parseObject(arg.getString("AnnualAnalysisVO"),
                AnnualAnalysisVO.class);
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),
                DmFocAnnualAmpVO.class);
        List<DmFocAnnualAmpVO> annList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),
                DmFocAnnualAmpVO.class);
        dmFocAnnualAmpVOList.addAll(annList);
        List<DmFocAnnualAmpVO> result = Whitebox.invokeMethod(annualAmpService, "sortByWeightRateBar",
                dmFocAnnualAmpVOList, annualAnalysisVO);
        Assertions.assertNotNull(result);
        annualAnalysisVO.setOrderMethod("asc");
        result = Whitebox.invokeMethod(annualAmpService, "sortByWeightRateBar", dmFocAnnualAmpVOList,
                annualAnalysisVO);
        Assertions.assertNotNull(result);
        dmFocAnnualAmpVOList.forEach(obj -> obj.setWeightRate("0*"));
        result = Whitebox.invokeMethod(annualAmpService, "sortByWeightRateBar", dmFocAnnualAmpVOList,
                annualAnalysisVO);
        Assertions.assertNotNull(result);
    }

    @Test
    public void compareColumnTest() throws Exception {
        int result = Whitebox.invokeMethod(annualAmpService, "compareColumn", "", "");
        assertThat(result).isEqualTo(0);
        result = Whitebox.invokeMethod(annualAmpService, "compareColumn", "", "1");
        assertThat(result).isEqualTo(-1);
        result = Whitebox.invokeMethod(annualAmpService, "compareColumn", "1", "");
        assertThat(result).isEqualTo(1);
        result = Whitebox.invokeMethod(annualAmpService, "compareColumn", "1", "1%");
        assertThat(result).isEqualTo(0);
        result = Whitebox.invokeMethod(annualAmpService, "compareColumn", "1", "2");
        assertThat(result).isEqualTo(-1);
        result = Whitebox.invokeMethod(annualAmpService, "compareColumn", "0*", "0*");
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void exportAnnualDetailTest() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = JSONObject.parseObject(arg.getString("AnnualAnalysisVO"),
                AnnualAnalysisVO.class);
        annualAnalysisVO.setIsShowHistogramChart(true);
        annualAnalysisVO.setIsTotalChildChart(true);
        annualAnalysisVO.setIsCompareFlag(false);
        List<DmFocViewInfoVO> itemList = JSONObject.parseArray(arg.getString("List<DmFocViewInfoVO>"), DmFocViewInfoVO.class);
        List<DmFocAnnualAmpVO> currentAnnualAmpList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"), DmFocAnnualAmpVO.class);
        ResultDataVO bgInfoList = new ResultDataVO();
        bgInfoList.setData(itemList);
        DmFoiImpExpRecordVO expRecordVO = new DmFoiImpExpRecordVO();
        when(excelUtil.expSelectColumnExcel(any(List.class), any())).thenReturn(expRecordVO);
        when(annualCommonService.getBgInfoList(any(CommonViewVO.class))).thenReturn(bgInfoList);
        when(dmFocVersionDao.findAnnualVersion(any())).thenReturn(new DmFocVersionInfoDTO());
        List<String> threeYears = new ArrayList<>();
        threeYears.add("2021");
        threeYears.add("2022");
        threeYears.add("2023");
        PowerMockito.doReturn(threeYears).when(annualCommonService).getThreeYears(annualAnalysisVO.getCostType(),annualAnalysisVO.getIndustryOrg());
        when(annualAmpDao.allIndustryCost(any(AnnualAnalysisVO.class))).thenReturn(currentAnnualAmpList);
        Whitebox.invokeMethod(annualAmpService, "exportAnnualDetail", annualAnalysisVO, null);
        verify(statisticsExcelService, atLeastOnce()).insertExportExcelRecord(any(DmFoiImpExpRecordVO.class));
        annualAnalysisVO.setCaliberFlag("R");
        Whitebox.invokeMethod(annualAmpService, "exportAnnualDetail", annualAnalysisVO, null);
        verify(statisticsExcelService, atLeastOnce()).insertExportExcelRecord(any(DmFoiImpExpRecordVO.class));
    }

    @Test
    public void setHeaderTest() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = new AnnualAnalysisVO();
        List<AbstractExcelTitleVO> titleVoList = new ArrayList<>();
        List<HeaderVo> headers = new ArrayList<>();
        Set<String> titles = new HashSet<>();
        annualAnalysisVO.setGranularityType("U");
        annualAnalysisVO.setCostType("P");
        Whitebox.invokeMethod(annualAmpService, "setHeader", titleVoList, headers, titles, annualAnalysisVO);
        assertThatNoException();
        annualAnalysisVO.setGranularityType("P");
        Whitebox.invokeMethod(annualAmpService, "setHeader", titleVoList, headers, titles, annualAnalysisVO);
        assertThatNoException();
    }

    @Test
    public void beanToMapTest() throws Exception {
        DmFocAnnualAmpVO vo = new DmFocAnnualAmpVO();
        Map result = Whitebox.invokeMethod(AnnualAmpService.class, "beanToMap", vo);
        Assertions.assertNotNull(result);
    }

    @Test
    public void typeOneTest() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = JSONObject.parseObject(arg.getString("AnnualAnalysisVO"),
                AnnualAnalysisVO.class);
        List<Map> dataList = new ArrayList<>();
        List<String> threeYears = Arrays.asList("2023", "09", "16");
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),
                DmFocAnnualAmpVO.class);
        annualAnalysisVO.setIsContainComb(true);
        Whitebox.invokeMethod(annualAmpService, "typeOne", annualAnalysisVO, dataList, threeYears);
        assertThatNoException();
        when(annualAmpDao.allIndustryCost(any(AnnualAnalysisVO.class))).thenReturn(dmFocAnnualAmpVOList);
        annualAnalysisVO.setIsContainComb(false);
        Whitebox.invokeMethod(annualAmpService, "typeOne", annualAnalysisVO, dataList, threeYears);
        assertThatNoException();

        annualAnalysisVO.setIsMultipleSelect(true);
        dmFocAnnualAmpVOList.forEach(obj -> obj.setPeriodYear("16"));
        Whitebox.invokeMethod(annualAmpService, "typeOne", annualAnalysisVO, dataList, threeYears);
        assertThatNoException();
    }

    @Test
    public void typeTwoTest() throws Exception {
        mockStatic(FcstIndexUtil.class);
        AnnualAnalysisVO annualAnalysisVO = JSONObject.parseObject(arg.getString("AnnualAnalysisVO"),
                AnnualAnalysisVO.class);
        List<Map> dataList = new ArrayList<>();
        List<String> threeYears = Arrays.asList("2023", "09", "16");
        annualAnalysisVO.setIsMultipleSelect(true);
        annualAnalysisVO.setIsContainComb(false);
        when(FcstIndexUtil.getNextGroupLevelByView(any(String.class), any(String.class),
                any(String.class), any(String.class))).thenReturn("CEG");
        Whitebox.invokeMethod(annualAmpService, "typeTwo", annualAnalysisVO, dataList, threeYears);
        assertThatNoException();

        when(FcstIndexUtil.getNextGroupLevelByView(any(String.class), any(String.class),
                any(String.class), any(String.class))).thenReturn("MODL");
        Whitebox.invokeMethod(annualAmpService, "typeTwo", annualAnalysisVO, dataList, threeYears);
        assertThatNoException();

        when(FcstIndexUtil.getNextGroupLevelByView(any(String.class), any(String.class),
                any(String.class), any(String.class))).thenReturn("CATEGORY");
        Whitebox.invokeMethod(annualAmpService, "typeTwo", annualAnalysisVO, dataList, threeYears);
        assertThatNoException();

        when(FcstIndexUtil.getNextGroupLevelByView(any(String.class), any(String.class),
                any(String.class), any(String.class))).thenReturn("ITEM");
        Whitebox.invokeMethod(annualAmpService, "typeTwo", annualAnalysisVO, dataList, threeYears);
        assertThatNoException();

        when(FcstIndexUtil.getNextGroupLevelByView(any(String.class), any(String.class),
                any(String.class), any(String.class))).thenReturn("test");
        Whitebox.invokeMethod(annualAmpService, "typeTwo", annualAnalysisVO, dataList, threeYears);
        assertThatNoException();

        annualAnalysisVO.setIsContainComb(true);
        Whitebox.invokeMethod(annualAmpService, "typeTwo", annualAnalysisVO, dataList, threeYears);
        assertThatNoException();
    }

    @Test
    public void setDataCombListTest() throws Exception {
        List<Map> dataList = new ArrayList<>();
        List<String> threeYears = Arrays.asList("2023", "09", "16");
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),
                DmFocAnnualAmpVO.class);
        dmFocAnnualAmpVOList.forEach(obj -> obj.setPeriodYear("16"));
        Whitebox.invokeMethod(annualAmpService, "setDataCombList", dataList, threeYears, dmFocAnnualAmpVOList);
        assertThatNoException();
        dmFocAnnualAmpVOList.forEach(obj -> {obj.setGroupCode(null); obj.setAnnualAmp(null);});
        Whitebox.invokeMethod(annualAmpService, "setDataCombList", dataList, threeYears, dmFocAnnualAmpVOList);
        assertThatNoException();

    }

    @Test
    public void setDataListTest() throws Exception {
        List<Map> dataList = new ArrayList<>();
        List<String> threeYears = Arrays.asList("2023", "09", "16");
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),
                DmFocAnnualAmpVO.class);
        dmFocAnnualAmpVOList.forEach(obj -> obj.setPeriodYear("16"));
        HashMap<String, List<DmFocAnnualAmpVO>> resultColumnMap = new HashMap<>();
        resultColumnMap.put("test", dmFocAnnualAmpVOList);
        Set<String> resultRowSet = new HashSet<>();
        resultRowSet.add("test");
        Whitebox.invokeMethod(annualAmpService, "setDataList", dataList, threeYears, resultColumnMap, resultRowSet);
        assertThatNoException();
        dmFocAnnualAmpVOList.forEach(obj -> {obj.setGroupCode(null); obj.setAnnualAmp(null);});
        Whitebox.invokeMethod(annualAmpService, "setDataList", dataList, threeYears, resultColumnMap, resultRowSet);
        assertThatNoException();

    }

    @Test
    public void typeThreeTest() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = JSONObject.parseObject(arg.getString("AnnualAnalysisVO"),
                AnnualAnalysisVO.class);
        List<Map> dataList = new ArrayList<>();
        List<String> threeYears = Arrays.asList("16");
        annualAnalysisVO.setReverseLv1Flag(true);
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setGroupLevel("ITEM");
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),
                DmFocAnnualAmpVO.class);
        List<DmFocAnnualAmpVO> annList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),
                DmFocAnnualAmpVO.class);
        dmFocAnnualAmpVOList.addAll(annList);
        dmFocAnnualAmpVOList.forEach(obj -> obj.setPeriodYear("16"));
        mockStatic(FcstIndexUtil.class);
        when(FcstIndexUtil.getNextGroupLevelByView(any(String.class), any(String.class),
                any(String.class), any(String.class))).thenReturn("ITEM");
        when(annualAmpDao.industryCostList(any(AnnualAnalysisVO.class))).thenReturn(dmFocAnnualAmpVOList);
        when(annualAmpDao.industryRevCostList(any(AnnualAnalysisVO.class))).thenReturn(dmFocAnnualAmpVOList);
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();
        Whitebox.invokeMethod(annualAmpService, "typeThree", annualAnalysisVO, dataList, threeYears);
        verify(commonService, atLeastOnce()).setProdRndTeamCode(any(MonthAnalysisVO.class));

        dmFocAnnualAmpVOList.forEach(obj -> obj.setWeightRate("16"));
        annualAnalysisVO.setIsContainComb(false);
        Whitebox.invokeMethod(annualAmpService, "typeThree", annualAnalysisVO, dataList, threeYears);
        verify(commonService, atLeastOnce()).setProdRndTeamCode(any(MonthAnalysisVO.class));
        annualAnalysisVO.setReverseSymbol(false);
    }

    @Test
    public void typeThree2Test() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = JSONObject.parseObject(arg.getString("AnnualAnalysisVO"),
                AnnualAnalysisVO.class);
        List<Map> dataList = new ArrayList<>();
        List<String> threeYears = Arrays.asList("2022","2023","2024");
        annualAnalysisVO.setReverseLv1Flag(true);
        annualAnalysisVO.setIsContainComb(true);
        annualAnalysisVO.setGroupLevel("ITEM");
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),
                DmFocAnnualAmpVO.class);
        List<DmFocAnnualAmpVO> annList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),
                DmFocAnnualAmpVO.class);
        dmFocAnnualAmpVOList.addAll(annList);
        dmFocAnnualAmpVOList.forEach(obj -> obj.setPeriodYear("2024"));
        mockStatic(FcstIndexUtil.class);
        when(FcstIndexUtil.getNextGroupLevelByView(any(String.class), any(String.class),
                any(String.class), any(String.class))).thenReturn("ITEM");
        when(annualAmpDao.industryCostList(any(AnnualAnalysisVO.class))).thenReturn(dmFocAnnualAmpVOList);
        when(annualAmpDao.industryRevCostList(any(AnnualAnalysisVO.class))).thenReturn(dmFocAnnualAmpVOList);
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();
        annualAnalysisVO.setReverseSymbol(false);
        Whitebox.invokeMethod(annualAmpService, "typeThree", annualAnalysisVO, dataList, threeYears);
        verify(commonService, atLeastOnce()).setProdRndTeamCode(any(MonthAnalysisVO.class));
    }

    @Test
    public void encryptionWeightValueTest() throws Exception {
        mockStatic(ConfigUtil.class);
        when(ConfigUtil.class, "getInstance").thenReturn(configUtil);
        PowerMockito.doReturn(plainText).when(configUtil).get32PlainText();
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),
                DmFocAnnualAmpVO.class);
        AnnualAnalysisVO annualAnalysisVO = JSONObject.parseObject(arg.getString("AnnualAnalysisVO"),
                AnnualAnalysisVO.class);
        List<Integer> numList = Arrays.asList(1);
        annualAnalysisVO.setIsContainComb(true);
        Whitebox.invokeMethod(annualAmpService, "encryptionWeightValue", dmFocAnnualAmpVOList, true,numList,
                annualAnalysisVO);
        assertThatNoException();
        dmFocAnnualAmpVOList.forEach(obj -> obj.setWeightAnnualAmpPercent(null));
        Whitebox.invokeMethod(annualAmpService, "encryptionWeightValue", dmFocAnnualAmpVOList, true,numList,
                annualAnalysisVO);
        assertThatNoException();
    }

    @Test
    public void sortByWeightAnnualAmpTest() throws Exception {
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),
                DmFocAnnualAmpVO.class);
        List<DmFocAnnualAmpVO> annList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),
                DmFocAnnualAmpVO.class);
        dmFocAnnualAmpVOList.addAll(annList);
        Whitebox.invokeMethod(annualAmpService, "sortByWeightAnnualAmp", dmFocAnnualAmpVOList);
        assertThatNoException();
        dmFocAnnualAmpVOList.forEach(obj -> obj.setWeightAnnualAmpPercent(null));
        Whitebox.invokeMethod(annualAmpService, "sortByWeightAnnualAmp", dmFocAnnualAmpVOList);
        assertThatNoException();
        dmFocAnnualAmpVOList.get(0).setWeightAnnualAmpPercent("1");
        Whitebox.invokeMethod(annualAmpService, "sortByWeightAnnualAmp", dmFocAnnualAmpVOList);
        assertThatNoException();
        dmFocAnnualAmpVOList.get(1).setWeightAnnualAmpPercent("2");
        Whitebox.invokeMethod(annualAmpService, "sortByWeightAnnualAmp", dmFocAnnualAmpVOList);
        assertThatNoException();
        dmFocAnnualAmpVOList.get(0).setWeightAnnualAmpPercent(null);
        Whitebox.invokeMethod(annualAmpService, "sortByWeightAnnualAmp", dmFocAnnualAmpVOList);
        assertThatNoException();
        dmFocAnnualAmpVOList.get(1).setWeightAnnualAmpPercent("0*");
        Whitebox.invokeMethod(annualAmpService, "sortByWeightAnnualAmp", dmFocAnnualAmpVOList);
        assertThatNoException();
        dmFocAnnualAmpVOList.get(0).setWeightAnnualAmpPercent("0*");
        Whitebox.invokeMethod(annualAmpService, "sortByWeightAnnualAmp", dmFocAnnualAmpVOList);
        assertThatNoException();
    }

    @Test
    public void sortByWeightTest() throws Exception {
        List<DmFocAnnualAmpVO> dmFocAnnualAmpVOList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),
                DmFocAnnualAmpVO.class);
        List<DmFocAnnualAmpVO> annList = JSONObject.parseArray(arg.getString("List<DmFocAnnualAmpVO>"),
                DmFocAnnualAmpVO.class);
        dmFocAnnualAmpVOList.addAll(annList);
        Whitebox.invokeMethod(annualAmpService, "sortByWeight", dmFocAnnualAmpVOList);
        assertThatNoException();
        dmFocAnnualAmpVOList.forEach(obj -> obj.setWeightRate(null));
        Whitebox.invokeMethod(annualAmpService, "sortByWeight", dmFocAnnualAmpVOList);
        assertThatNoException();
        dmFocAnnualAmpVOList.get(0).setWeightRate("1");
        Whitebox.invokeMethod(annualAmpService, "sortByWeight", dmFocAnnualAmpVOList);
        assertThatNoException();
        dmFocAnnualAmpVOList.get(1).setWeightRate("2");
        Whitebox.invokeMethod(annualAmpService, "sortByWeight", dmFocAnnualAmpVOList);
        assertThatNoException();
        dmFocAnnualAmpVOList.get(0).setWeightRate(null);
        Whitebox.invokeMethod(annualAmpService, "sortByWeight", dmFocAnnualAmpVOList);
        assertThatNoException();
        dmFocAnnualAmpVOList.get(0).setWeightRate("%");
        Whitebox.invokeMethod(annualAmpService, "sortByWeight", dmFocAnnualAmpVOList);
        assertThatNoException();
        dmFocAnnualAmpVOList.get(1).setWeightRate("%");
        Whitebox.invokeMethod(annualAmpService, "sortByWeight", dmFocAnnualAmpVOList);
        assertThatNoException();
    }

    @Test
    public void sortByGroupCnNameTest() throws Exception {
        DmFocAnnualAmpVO ann1 = new DmFocAnnualAmpVO();
        ann1.setGroupCnName("单独");
        DmFocAnnualAmpVO ann2 = new DmFocAnnualAmpVO();
        ann2.setGroupCnName("单独");
        int result = Whitebox.invokeMethod(annualAmpService, "sortByGroupCnName", ann1, ann2);
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void getGroupKeyTest() throws Exception {
        DmFocAnnualAmpVO ann = new DmFocAnnualAmpVO();
        String result = Whitebox.invokeMethod(annualAmpService, "getGroupKey", ann);
        assertThat(result).isNotNull();
    }

    @Test
    public void setGroupHeaderTest() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = JSONObject.parseObject(arg.getString("AnnualAnalysisVO"),
                AnnualAnalysisVO.class);
        List<HeaderVo> header = new ArrayList<>();
        Whitebox.invokeMethod(annualAmpService, "setGroupHeader", annualAnalysisVO, header);
        assertThatNoException();
        annualAnalysisVO.setGroupLevel("CATEGORY");
        Whitebox.invokeMethod(annualAmpService, "setGroupHeader", annualAnalysisVO, header);
        assertThatNoException();
        annualAnalysisVO.setGroupLevel("MODL");
        Whitebox.invokeMethod(annualAmpService, "setGroupHeader", annualAnalysisVO, header);
        assertThatNoException();
        annualAnalysisVO.setIsContainComb(true);
        Whitebox.invokeMethod(annualAmpService, "setGroupHeader", annualAnalysisVO, header);
        assertThatNoException();
    }

    @Test
    public void getAmpAndWeightByChildHeaderTest() throws Exception {
        AnnualAnalysisVO annualAnalysisVO = JSONObject.parseObject(arg.getString("AnnualAnalysisVO"),
                AnnualAnalysisVO.class);
        Whitebox.invokeMethod(annualAmpService, "getAmpAndWeightByChildHeader", annualAnalysisVO);
        assertThatNoException();
        annualAnalysisVO.setGroupLevel("CATEGORY");
        Whitebox.invokeMethod(annualAmpService, "getAmpAndWeightByChildHeader", annualAnalysisVO);
        assertThatNoException();
        annualAnalysisVO.setIsContainComb(true);
        Whitebox.invokeMethod(annualAmpService, "getAmpAndWeightByChildHeader", annualAnalysisVO);
        assertThatNoException();
        annualAnalysisVO.setGroupLevel("CATEGORY");
        Whitebox.invokeMethod(annualAmpService, "getAmpAndWeightByChildHeader", annualAnalysisVO);
        assertThatNoException();
    }

}