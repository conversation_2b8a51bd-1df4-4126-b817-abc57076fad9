/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.vo.replace;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.huawei.it.fcst.industry.pbi.annotations.ExcelValid;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 新旧编码替换维表导入VO实体类
 *
 * <AUTHOR>
 * @since 2024-09-27
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReplaceImportVO implements Serializable {

    private static final long serialVersionUID = 6598378633291250787L;

    @ExcelIgnore
    private Long id;

    @ExcelProperty(value = "BG编码")
    private String bgCode;

    @ExcelProperty(value = "*BG")
    @ExcelValid(message = "BG名称不能为空！")
    private String bgCnName;

    @ExcelProperty(value = "*L1")
    @ExcelValid(message = "L1名称不能为空！")
    private String lv1CnName;

    @ExcelProperty(value = "L1编码")
    private String lv1Code;

    @ExcelProperty(value = "*L2")
    @ExcelValid(message = "L2名称不能为空！")
    private String lv2CnName;

    @ExcelProperty(value = "L2编码")
    private String lv2Code;

    @ExcelProperty(value = "*L3")
    @ExcelValid(message = "L3名称不能为空！")
    private String lv3CnName;

    @ExcelProperty(value = "L3编码")
    private String lv3Code;

    @ExcelProperty(value = "*L3.5")
    @ExcelValid(message = "L3.5名称不能为空！")
    private String lv4CnName;

    @ExcelProperty(value = "L3.5编码")
    private String lv4Code;

    @ExcelProperty(value = "产品")
    private String prodCnName;

    @ExcelIgnore
    private String prodCode;

    @ExcelProperty(value = "*替换关系名称")
    @ExcelValid(message = "替换关系名称不能为空！")
    private String replaceRelationName;

    @ExcelProperty(value = "*新老编码替换类型")
    @ExcelValid(message = "新老编码替换类型不能为空！")
    private String replaceRelationType;

    @ExcelProperty(value = "*老编码")
    @ExcelValid(message = "老编码不能为空！")
    private String oldSpartCode;

    @ExcelProperty(value = "老编码描述")
    private String oldSpartDesc;

    @ExcelProperty(value = "*新编码")
    @ExcelValid(message = "新编码不能为空！")
    private String newSpartCode;

    @ExcelProperty(value = "新编码描述")
    private String newSpartDesc;

    @ExcelProperty(value = "*关系")
    @ExcelValid(message = "关系不能为空！")
    private String relationType;

    @ExcelProperty(value = "新编码上市时间")
    private String newSpartGtmDate;

    @ExcelProperty(value = "切换开始时间")
    private String replaceBeginDate;

    @ExcelIgnore
    private String gtsType;

    @ExcelIgnore
    private Long versionId;

    @ExcelIgnore
    private String delFlag;

    @ExcelIgnore
    private Long createdBy;

    @ExcelIgnore
    private Date creationDate;

    @ExcelIgnore
    private Long lastUpdatedBy;

    @ExcelIgnore
    private Date lastUpdateDate;

    @ExcelProperty(value = "错误信息")
    private String errorMessage;
}
