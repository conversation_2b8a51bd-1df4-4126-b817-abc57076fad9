/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.guide.DmFocGuideInfoVO;
import com.huawei.it.fcst.industry.index.vo.guide.GuideParamVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;

/**
 * IDmFocGuideInfoDao Class
 *
 * <AUTHOR>
 * @since 2024/2/29
 */
public interface IDmFocGuideInfoDao {

    PagedResult<DmFocGuideInfoVO> findGuideInfoList(GuideParamVO guideParamVO, PageVO pageVO);

    Long getGuideKey();

    void deleteGuide(GuideParamVO guideParamVO);

    void saveGuideInfo(GuideParamVO guideParamVO);
}
