/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.month;

import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.mockito.ArgumentMatchers.any;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.when;

import com.huawei.it.fcst.industry.index.impl.annual.StatisticsExcelService;
import com.huawei.it.fcst.industry.index.utils.ExcelExportUtil;
import com.huawei.it.fcst.industry.index.utils.FileProcessUtis;
import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.it.jalor5.security.UserVO;

import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * MonthExportServiceTest Class
 *
 * <AUTHOR>
 * @since 2023/11/7
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest( {FileProcessUtis.class})
public class MonthExportServiceTest {
    private static final Logger LOGGER = LoggerFactory.getLogger(MonthExportService.class);
    @InjectMocks
    private MonthExportService monthExportService;

    @Mock
    private StatisticsExcelService statisticsExcelService;

    @Mock
    private AsyncExportService asyncExportService;

    @Before
    public void before() throws Exception {
        MockitoAnnotations.openMocks(this);
        com.huawei.it.jalor5.security.UserVO user = new UserVO();
        user.setUserId(9999L);
        user.setUserAccount("xtestAccount");
        user.setUserCN("testCN");
        user.setEmployeeNumber("testCN");
        user.setScope("testScope");
        user.setAppName("testAppName");
        RequestContext testRequestContext = new RequestContext();
        testRequestContext.setUser(user);
        RequestContextManager.setCurrent(testRequestContext);
    }

    @Test
    public void exportData() throws Exception {
        MonthAnalysisVO monthAnalysisVO =new MonthAnalysisVO();
        monthAnalysisVO.setIsShowPriceChart(false);
        monthAnalysisVO.setCostType("p");
        monthAnalysisVO.setIndustryOrg("IAS");
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        String groupCnName="元器";
        IRequestContext current = RequestContextManager.getCurrent();
        Long userId=1175L;
        Future<Integer> multiIdxTotal=new Future<Integer>() {
            @Override
            public boolean cancel(boolean mayInterruptIfRunning) {
                return false;
            }

            @Override
            public boolean isCancelled() {
                return false;
            }

            @Override
            public boolean isDone() {
                return true;
            }

            @Override
            public Integer get() throws InterruptedException, ExecutionException {
                return 10;
            }

            @Override
            public Integer get(long timeout, @NotNull TimeUnit unit)
                throws InterruptedException, ExecutionException, TimeoutException {
                return null;
            }
        };
        mockStatic(FileProcessUtis.class);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 0, groupCnName, current);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        monthExportService.exportData(monthAnalysisVO, workbook, groupCnName, current, userId);
        assertThatNoException();
    }

    @Test
    public void exportData2T() throws Exception {
        MonthAnalysisVO monthAnalysisVO =new MonthAnalysisVO();
        monthAnalysisVO.setIsShowPriceChart(true);
        monthAnalysisVO.setIsShowChildContent(false);
        monthAnalysisVO.setCostType("T");
        monthAnalysisVO.setIndustryOrg("IAS");
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        String groupCnName="元器";
        IRequestContext current = RequestContextManager.getCurrent();
        Long userId=1175L;
        Future<Integer> multiIdxTotal=new Future<Integer>() {
            @Override
            public boolean cancel(boolean mayInterruptIfRunning) {
                return false;
            }

            @Override
            public boolean isCancelled() {
                return false;
            }

            @Override
            public boolean isDone() {
                return true;
            }

            @Override
            public Integer get() throws InterruptedException, ExecutionException {
                return 10;
            }

            @Override
            public Integer get(long timeout, @NotNull TimeUnit unit)
                throws InterruptedException, ExecutionException, TimeoutException {
                return null;
            }
        };
        mockStatic(FileProcessUtis.class);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 0, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillPriceIdxSheet(workbook, 0, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMonthYoySheet(workbook, 1, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        monthExportService.exportData(monthAnalysisVO, workbook, groupCnName, current, userId);
        assertThatNoException();
    }

    @Test
    public void exportData3T() throws Exception {
        MonthAnalysisVO monthAnalysisVO =new MonthAnalysisVO();
        monthAnalysisVO.setIsShowPriceChart(true);
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsTotalChildChart(false);
        monthAnalysisVO.setCostType("T");
        monthAnalysisVO.setIndustryOrg("IAS");
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        String groupCnName="元器";
        IRequestContext current = RequestContextManager.getCurrent();
        Long userId=1175L;
        Future<Integer> multiIdxTotal=new Future<Integer>() {
            @Override
            public boolean cancel(boolean mayInterruptIfRunning) {
                return false;
            }

            @Override
            public boolean isCancelled() {
                return false;
            }

            @Override
            public boolean isDone() {
                return true;
            }

            @Override
            public Integer get() throws InterruptedException, ExecutionException {
                return 10;
            }

            @Override
            public Integer get(long timeout, @NotNull TimeUnit unit)
                throws InterruptedException, ExecutionException, TimeoutException {
                return null;
            }
        };
        mockStatic(FileProcessUtis.class);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 0, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillPriceIdxSheet(workbook, 0, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMonthYoySheet(workbook, 1, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        monthExportService.exportData(monthAnalysisVO, workbook, groupCnName, current, userId);
        assertThatNoException();
    }

    @Test
    public void exportData4T() throws Exception {
        MonthAnalysisVO monthAnalysisVO =new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setIsShowPriceChart(true);
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsTotalChildChart(true);
        monthAnalysisVO.setIsContainComb(true);
        monthAnalysisVO.setCostType("M");
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        String groupCnName="元器";
        IRequestContext current = RequestContextManager.getCurrent();
        Long userId=1175L;
        Future<Integer> multiIdxTotal=new Future<Integer>() {
            @Override
            public boolean cancel(boolean mayInterruptIfRunning) {
                return false;
            }

            @Override
            public boolean isCancelled() {
                return false;
            }

            @Override
            public boolean isDone() {
                return true;
            }

            @Override
            public Integer get() throws InterruptedException, ExecutionException {
                return 10;
            }

            @Override
            public Integer get(long timeout, @NotNull TimeUnit unit)
                throws InterruptedException, ExecutionException, TimeoutException {
                return null;
            }
        };
        mockStatic(FileProcessUtis.class);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 0, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillPriceIdxSheet(workbook, 0, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMonthYoySheet(workbook, 1, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate2Sheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 4, groupCnName, current);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        monthExportService.exportData(monthAnalysisVO, workbook, groupCnName, current, userId);
        assertThatNoException();
    }

    @Test
    public void exportData5T() throws Exception {
        MonthAnalysisVO monthAnalysisVO =new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setIsShowPriceChart(true);
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsTotalChildChart(true);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("M");
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setViewFlag("5");
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        String groupCnName="元器";
        IRequestContext current = RequestContextManager.getCurrent();
        Long userId=1175L;
        Future<Integer> multiIdxTotal=new Future<Integer>() {
            @Override
            public boolean cancel(boolean mayInterruptIfRunning) {
                return false;
            }

            @Override
            public boolean isCancelled() {
                return false;
            }

            @Override
            public boolean isDone() {
                return true;
            }

            @Override
            public Integer get() throws InterruptedException, ExecutionException {
                return 10;
            }

            @Override
            public Integer get(long timeout, @NotNull TimeUnit unit)
                throws InterruptedException, ExecutionException, TimeoutException {
                return null;
            }
        };
        mockStatic(FileProcessUtis.class);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 0, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillPriceIdxSheet(workbook, 0, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMonthYoySheet(workbook, 1, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate2Sheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 4, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        monthExportService.exportData(monthAnalysisVO, workbook, groupCnName, current, userId);
        assertThatNoException();
    }

    @Test
    public void exportData6T() throws Exception {
        MonthAnalysisVO monthAnalysisVO =new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setIsShowPriceChart(true);
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsTotalChildChart(true);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("M");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setViewFlag("5");
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        String groupCnName="元器";
        IRequestContext current = RequestContextManager.getCurrent();
        Long userId=1175L;
        Future<Integer> multiIdxTotal=new Future<Integer>() {
            @Override
            public boolean cancel(boolean mayInterruptIfRunning) {
                return false;
            }

            @Override
            public boolean isCancelled() {
                return false;
            }

            @Override
            public boolean isDone() {
                return true;
            }

            @Override
            public Integer get() throws InterruptedException, ExecutionException {
                return 10;
            }

            @Override
            public Integer get(long timeout, @NotNull TimeUnit unit)
                throws InterruptedException, ExecutionException, TimeoutException {
                return null;
            }
        };
        mockStatic(FileProcessUtis.class);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 0, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillPriceIdxSheet(workbook, 0, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMonthYoySheet(workbook, 1, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate2Sheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 4, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        monthExportService.exportData(monthAnalysisVO, workbook, groupCnName, current, userId);
        assertThatNoException();
    }

    @Test
    public void exportData7T() throws Exception {
        MonthAnalysisVO monthAnalysisVO =new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setIsShowPriceChart(true);
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsTotalChildChart(true);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("M");
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setViewFlag("1");
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        String groupCnName="元器";
        IRequestContext current = RequestContextManager.getCurrent();
        Long userId=1175L;
        Future<Integer> multiIdxTotal=new Future<Integer>() {
            @Override
            public boolean cancel(boolean mayInterruptIfRunning) {
                return false;
            }

            @Override
            public boolean isCancelled() {
                return false;
            }

            @Override
            public boolean isDone() {
                return true;
            }

            @Override
            public Integer get() throws InterruptedException, ExecutionException {
                return 10;
            }

            @Override
            public Integer get(long timeout, @NotNull TimeUnit unit)
                throws InterruptedException, ExecutionException, TimeoutException {
                return null;
            }
        };
        mockStatic(FileProcessUtis.class);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 0, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillPriceIdxSheet(workbook, 0, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMonthYoySheet(workbook, 1, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate2Sheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 4, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        monthExportService.exportData(monthAnalysisVO, workbook, groupCnName, current, userId);
        assertThatNoException();
    }

    @Test
    public void exportData8T() throws Exception {
        MonthAnalysisVO monthAnalysisVO =new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setIsShowPriceChart(true);
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsTotalChildChart(true);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("M");
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setViewFlag("4");
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        String groupCnName="元器";
        IRequestContext current = RequestContextManager.getCurrent();
        Long userId=1175L;
        Future<Integer> multiIdxTotal=new Future<Integer>() {
            @Override
            public boolean cancel(boolean mayInterruptIfRunning) {
                return false;
            }

            @Override
            public boolean isCancelled() {
                return false;
            }

            @Override
            public boolean isDone() {
                return true;
            }

            @Override
            public Integer get() throws InterruptedException, ExecutionException {
                return 10;
            }

            @Override
            public Integer get(long timeout, @NotNull TimeUnit unit)
                throws InterruptedException, ExecutionException, TimeoutException {
                return null;
            }
        };
        mockStatic(FileProcessUtis.class);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 0, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillPriceIdxSheet(workbook, 0, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMonthYoySheet(workbook, 1, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate2Sheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 4, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        monthExportService.exportData(monthAnalysisVO, workbook, groupCnName, current, userId);
        assertThatNoException();
    }

    @Test
    public void exportData9T() throws Exception {
        MonthAnalysisVO monthAnalysisVO =new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setIsShowPriceChart(true);
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsTotalChildChart(true);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("M");
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setViewFlag("6");
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        String groupCnName="元器";
        IRequestContext current = RequestContextManager.getCurrent();
        Long userId=1175L;
        Future<Integer> multiIdxTotal=new Future<Integer>() {
            @Override
            public boolean cancel(boolean mayInterruptIfRunning) {
                return false;
            }

            @Override
            public boolean isCancelled() {
                return false;
            }

            @Override
            public boolean isDone() {
                return true;
            }

            @Override
            public Integer get() throws InterruptedException, ExecutionException {
                return 10;
            }

            @Override
            public Integer get(long timeout, @NotNull TimeUnit unit)
                throws InterruptedException, ExecutionException, TimeoutException {
                return null;
            }
        };
        mockStatic(FileProcessUtis.class);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 0, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillPriceIdxSheet(workbook, 0, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMonthYoySheet(workbook, 1, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate2Sheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 4, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        monthExportService.exportData(monthAnalysisVO, workbook, groupCnName, current, userId);
        assertThatNoException();
    }

    @Test
    public void exportData10T() throws Exception {
        MonthAnalysisVO monthAnalysisVO =new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setIsShowPriceChart(true);
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsTotalChildChart(true);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("T");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setViewFlag("5");
        monthAnalysisVO.setGroupLevel("LV3");
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        String groupCnName="元器";
        IRequestContext current = RequestContextManager.getCurrent();
        Long userId=1175L;
        Future<Integer> multiIdxTotal=new Future<Integer>() {
            @Override
            public boolean cancel(boolean mayInterruptIfRunning) {
                return false;
            }

            @Override
            public boolean isCancelled() {
                return false;
            }

            @Override
            public boolean isDone() {
                return true;
            }

            @Override
            public Integer get() throws InterruptedException, ExecutionException {
                return 10;
            }

            @Override
            public Integer get(long timeout, @NotNull TimeUnit unit)
                throws InterruptedException, ExecutionException, TimeoutException {
                return null;
            }
        };
        mockStatic(FileProcessUtis.class);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 0, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillPriceIdxSheet(workbook, 0, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMonthYoySheet(workbook, 1, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate2Sheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 4, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate1Sheet(monthAnalysisVO,workbook, 4, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillHeapTemplate1Sheet(monthAnalysisVO,workbook, 5, groupCnName, current);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        monthExportService.exportData(monthAnalysisVO, workbook, groupCnName, current, userId);
        assertThatNoException();
    }

    @Test
    public void exportData11T() throws Exception {
        MonthAnalysisVO monthAnalysisVO =new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setIsShowPriceChart(true);
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsTotalChildChart(true);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("M");
        monthAnalysisVO.setGranularityType("P");
        monthAnalysisVO.setViewFlag("5");
        monthAnalysisVO.setGroupLevel("LV3");
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        String groupCnName="元器";
        IRequestContext current = RequestContextManager.getCurrent();
        Long userId=1175L;
        Future<Integer> multiIdxTotal=new Future<Integer>() {
            @Override
            public boolean cancel(boolean mayInterruptIfRunning) {
                return false;
            }

            @Override
            public boolean isCancelled() {
                return false;
            }

            @Override
            public boolean isDone() {
                return true;
            }

            @Override
            public Integer get() throws InterruptedException, ExecutionException {
                return 10;
            }

            @Override
            public Integer get(long timeout, @NotNull TimeUnit unit)
                throws InterruptedException, ExecutionException, TimeoutException {
                return null;
            }
        };
        mockStatic(FileProcessUtis.class);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 0, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillPriceIdxSheet(workbook, 0, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMonthYoySheet(workbook, 1, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate2Sheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 4, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate1Sheet(monthAnalysisVO,workbook, 4, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillHeapTemplate1Sheet(monthAnalysisVO,workbook, 5, groupCnName, current);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        monthExportService.exportData(monthAnalysisVO, workbook, groupCnName, current, userId);
        assertThatNoException();
    }

    @Test
    public void exportData12T() throws Exception {
        MonthAnalysisVO monthAnalysisVO =new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setIsShowPriceChart(true);
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsTotalChildChart(true);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("M");
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setViewFlag("5");
        monthAnalysisVO.setGroupLevel("CEG");
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        String groupCnName="元器";
        IRequestContext current = RequestContextManager.getCurrent();
        Long userId=1175L;
        Future<Integer> multiIdxTotal=new Future<Integer>() {
            @Override
            public boolean cancel(boolean mayInterruptIfRunning) {
                return false;
            }

            @Override
            public boolean isCancelled() {
                return false;
            }

            @Override
            public boolean isDone() {
                return true;
            }

            @Override
            public Integer get() throws InterruptedException, ExecutionException {
                return 10;
            }

            @Override
            public Integer get(long timeout, @NotNull TimeUnit unit)
                throws InterruptedException, ExecutionException, TimeoutException {
                return null;
            }
        };
        mockStatic(FileProcessUtis.class);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 0, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillPriceIdxSheet(workbook, 0, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMonthYoySheet(workbook, 1, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate2Sheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 4, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate1Sheet(monthAnalysisVO,workbook, 4, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillHeapTemplate1Sheet(monthAnalysisVO,workbook, 5, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillHeapTemplate2Sheet(monthAnalysisVO,workbook, 5, groupCnName, current);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        monthExportService.exportData(monthAnalysisVO, workbook, groupCnName, current, userId);
        assertThatNoException();
    }

    @Test
    public void exportData13T() throws Exception {
        MonthAnalysisVO monthAnalysisVO =new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setIsShowPriceChart(true);
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsTotalChildChart(true);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("T");
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setViewFlag("5");
        monthAnalysisVO.setGroupLevel("CEG");
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        String groupCnName="元器";
        IRequestContext current = RequestContextManager.getCurrent();
        Long userId=1175L;
        Future<Integer> multiIdxTotal=new Future<Integer>() {
            @Override
            public boolean cancel(boolean mayInterruptIfRunning) {
                return false;
            }

            @Override
            public boolean isCancelled() {
                return false;
            }

            @Override
            public boolean isDone() {
                return true;
            }

            @Override
            public Integer get() throws InterruptedException, ExecutionException {
                return 10;
            }

            @Override
            public Integer get(long timeout, @NotNull TimeUnit unit)
                throws InterruptedException, ExecutionException, TimeoutException {
                return null;
            }
        };
        mockStatic(FileProcessUtis.class);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 0, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillPriceIdxSheet(workbook, 0, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMonthYoySheet(workbook, 1, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate2Sheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 4, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate1Sheet(monthAnalysisVO,workbook, 4, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillHeapTemplate1Sheet(monthAnalysisVO,workbook, 5, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillHeapTemplate2Sheet(monthAnalysisVO,workbook, 5, groupCnName, current);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        monthExportService.exportData(monthAnalysisVO, workbook, groupCnName, current, userId);
        assertThatNoException();
    }

    @Test
    public void exportData14T() throws Exception {
        MonthAnalysisVO monthAnalysisVO =new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setIsShowPriceChart(true);
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsTotalChildChart(true);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("T");
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setViewFlag("5");
        monthAnalysisVO.setGroupLevel("DIMENSION");
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        String groupCnName="元器";
        IRequestContext current = RequestContextManager.getCurrent();
        Long userId=1175L;
        Future<Integer> multiIdxTotal=new Future<Integer>() {
            @Override
            public boolean cancel(boolean mayInterruptIfRunning) {
                return false;
            }

            @Override
            public boolean isCancelled() {
                return false;
            }

            @Override
            public boolean isDone() {
                return true;
            }

            @Override
            public Integer get() throws InterruptedException, ExecutionException {
                return 10;
            }

            @Override
            public Integer get(long timeout, @NotNull TimeUnit unit)
                throws InterruptedException, ExecutionException, TimeoutException {
                return null;
            }
        };
        mockStatic(FileProcessUtis.class);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 0, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillPriceIdxSheet(workbook, 0, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMonthYoySheet(workbook, 1, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate2Sheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 4, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate1Sheet(monthAnalysisVO,workbook, 4, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillHeapTemplate1Sheet(monthAnalysisVO,workbook, 5, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillHeapTemplate2Sheet(monthAnalysisVO,workbook, 5, groupCnName, current);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        monthExportService.exportData(monthAnalysisVO, workbook, groupCnName, current, userId);
        assertThatNoException();
    }

    @Test
    public void exportData15T() throws Exception {
        MonthAnalysisVO monthAnalysisVO =new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setIsShowPriceChart(true);
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsTotalChildChart(true);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("T");
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setViewFlag("5");
        monthAnalysisVO.setGroupLevel("SUBCATEGORY");
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        String groupCnName="元器";
        IRequestContext current = RequestContextManager.getCurrent();
        Long userId=1175L;
        Future<Integer> multiIdxTotal=new Future<Integer>() {
            @Override
            public boolean cancel(boolean mayInterruptIfRunning) {
                return false;
            }

            @Override
            public boolean isCancelled() {
                return false;
            }

            @Override
            public boolean isDone() {
                return true;
            }

            @Override
            public Integer get() throws InterruptedException, ExecutionException {
                return 10;
            }

            @Override
            public Integer get(long timeout, @NotNull TimeUnit unit)
                throws InterruptedException, ExecutionException, TimeoutException {
                return null;
            }
        };
        mockStatic(FileProcessUtis.class);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 0, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillPriceIdxSheet(workbook, 0, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMonthYoySheet(workbook, 1, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate2Sheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 4, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate1Sheet(monthAnalysisVO,workbook, 4, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillHeapTemplate1Sheet(monthAnalysisVO,workbook, 5, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillHeapTemplate2Sheet(monthAnalysisVO,workbook, 5, groupCnName, current);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        monthExportService.exportData(monthAnalysisVO, workbook, groupCnName, current, userId);
        assertThatNoException();
    }

    @Test
    public void exportData16T() throws Exception {
        MonthAnalysisVO monthAnalysisVO =new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setIsShowPriceChart(true);
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsTotalChildChart(true);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("T");
        monthAnalysisVO.setGranularityType("D");
        monthAnalysisVO.setViewFlag("5");
        monthAnalysisVO.setGroupLevel("SUB_DETAIL");
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        String groupCnName="元器";
        IRequestContext current = RequestContextManager.getCurrent();
        Long userId=1175L;
        Future<Integer> multiIdxTotal=new Future<Integer>() {
            @Override
            public boolean cancel(boolean mayInterruptIfRunning) {
                return false;
            }

            @Override
            public boolean isCancelled() {
                return false;
            }

            @Override
            public boolean isDone() {
                return true;
            }

            @Override
            public Integer get() throws InterruptedException, ExecutionException {
                return 10;
            }

            @Override
            public Integer get(long timeout, @NotNull TimeUnit unit)
                throws InterruptedException, ExecutionException, TimeoutException {
                return null;
            }
        };
        mockStatic(FileProcessUtis.class);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 0, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillPriceIdxSheet(workbook, 0, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMonthYoySheet(workbook, 1, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate2Sheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 4, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate1Sheet(monthAnalysisVO,workbook, 4, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillHeapTemplate1Sheet(monthAnalysisVO,workbook, 5, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillHeapTemplate2Sheet(monthAnalysisVO,workbook, 5, groupCnName, current);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        monthExportService.exportData(monthAnalysisVO, workbook, groupCnName, current, userId);
        assertThatNoException();
    }

    @Test
    public void exportData17T() throws Exception {
        MonthAnalysisVO monthAnalysisVO =new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setIsShowPriceChart(true);
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsTotalChildChart(true);
        monthAnalysisVO.setIsContainComb(true);
        monthAnalysisVO.setCostType("T");
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setViewFlag("5");
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        String groupCnName="元器";
        IRequestContext current = RequestContextManager.getCurrent();
        Long userId=1175L;
        Future<Integer> multiIdxTotal=new Future<Integer>() {
            @Override
            public boolean cancel(boolean mayInterruptIfRunning) {
                return false;
            }

            @Override
            public boolean isCancelled() {
                return false;
            }

            @Override
            public boolean isDone() {
                return true;
            }

            @Override
            public Integer get() throws InterruptedException, ExecutionException {
                return 10;
            }

            @Override
            public Integer get(long timeout, @NotNull TimeUnit unit)
                throws InterruptedException, ExecutionException, TimeoutException {
                return null;
            }
        };
        mockStatic(FileProcessUtis.class);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 0, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillPriceIdxSheet(workbook, 0, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMonthYoySheet(workbook, 1, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate2Sheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 4, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        monthExportService.exportData(monthAnalysisVO, workbook, groupCnName, current, userId);
        assertThatNoException();
    }

    @Test
    public void exportData18T() throws Exception {
        MonthAnalysisVO monthAnalysisVO =new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setIsShowPriceChart(true);
        monthAnalysisVO.setIsShowChildContent(true);
        monthAnalysisVO.setIsTotalChildChart(true);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("T");
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setViewFlag("6");
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        String groupCnName="元器";
        IRequestContext current = RequestContextManager.getCurrent();
        Long userId=1175L;
        Future<Integer> multiIdxTotal=new Future<Integer>() {
            @Override
            public boolean cancel(boolean mayInterruptIfRunning) {
                return false;
            }

            @Override
            public boolean isCancelled() {
                return false;
            }

            @Override
            public boolean isDone() {
                return true;
            }

            @Override
            public Integer get() throws InterruptedException, ExecutionException {
                return 10;
            }

            @Override
            public Integer get(long timeout, @NotNull TimeUnit unit)
                throws InterruptedException, ExecutionException, TimeoutException {
                return null;
            }
        };
        mockStatic(FileProcessUtis.class);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 0, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillPriceIdxSheet(workbook, 0, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMonthYoySheet(workbook, 1, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate2Sheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 4, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 1, groupCnName, current);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        monthExportService.exportData(monthAnalysisVO, workbook, groupCnName, current, userId);
        assertThatNoException();
    }

    @Test
    public void exportData19T() throws Exception {
        MonthAnalysisVO monthAnalysisVO =new MonthAnalysisVO();
        monthAnalysisVO.setIndustryOrg("IAS");
        monthAnalysisVO.setIsShowPriceChart(true);
        monthAnalysisVO.setIsShowChildContent(false);
        monthAnalysisVO.setIsTotalChildChart(true);
        monthAnalysisVO.setIsContainComb(false);
        monthAnalysisVO.setCostType("M");
        monthAnalysisVO.setGranularityType("U");
        monthAnalysisVO.setViewFlag("6");
        XSSFWorkbook workbook = ExcelExportUtil.getWorkbookByTemplate("excel.export.template/IndustryCostIdxExpTemplate2UT.xlsx");
        String groupCnName="元器";
        IRequestContext current = RequestContextManager.getCurrent();
        Long userId=1175L;
        Future<Integer> multiIdxTotal=new Future<Integer>() {
            @Override
            public boolean cancel(boolean mayInterruptIfRunning) { return false;
            }

            @Override
            public boolean isCancelled() { return false;
            }

            @Override
            public boolean isDone() { return true;
            }

            @Override
            public Integer get() throws InterruptedException, ExecutionException { return 10;
            }

            @Override
            public Integer get(long timeout, @NotNull TimeUnit unit) throws InterruptedException, ExecutionException, TimeoutException { return null;
            }
        };
        mockStatic(FileProcessUtis.class);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMultiIndexTemplate1Sheet(monthAnalysisVO,workbook, 0, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillPriceIdxSheet(workbook, 0, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillMonthYoySheet(workbook, 1, groupCnName,monthAnalysisVO, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillWeightTemplate2Sheet(monthAnalysisVO,workbook, 3, groupCnName, current);
        PowerMockito.doReturn(multiIdxTotal).when(asyncExportService).fillDistributeCostSheet(monthAnalysisVO,workbook, 2, groupCnName, current);
        when(FileProcessUtis.class, "uploadToS3", any(),any()).thenReturn("aaaa-bbbcc33444");
        monthExportService.exportData(monthAnalysisVO, workbook, groupCnName, current, userId);
        assertThatNoException();
    }

}