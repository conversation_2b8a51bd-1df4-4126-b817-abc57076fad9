/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.config.FomManufactureDimVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * IFomManufactureDimDao Class
 *
 * <AUTHOR>
 * @since 2023/10/18
 */
public interface IFomManufactureDimDao {

    PagedResult<FomManufactureDimVO> findManufactureByPage(@Param("fomManufactureDimVO")FomManufactureDimVO fomManufactureDimVO, @Param("pageVO")PageVO pageVO);

    List<FomManufactureDimVO> findDropDownList(FomManufactureDimVO fomManufactureDimVO);

    List<FomManufactureDimVO> findItemDropDownList(FomManufactureDimVO fomManufactureDimVO);

    List<FomManufactureDimVO> findMfcDropDownList(FomManufactureDimVO fomManufactureDimVO);

    String syncDimMadeFunction(@Param("industryOrg")String industryOrg);
}
