/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.export.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 导出Sheet页签信息和bean信息
 *
 * <AUTHOR>
 * @since 202407
 */
@Getter
@Setter
public class SheetBeanMetaVO implements Serializable {
    static final long serialVersionUID = -6430539698949161871L;

    // 模板名称
    private String templateName;

    // 模板 sheet 顺序
    private Integer sheetNo;

    // 对应的数据bean 对象
    private String beanName;

    // 名称
    private String sheetName;

    // 是否动态列
    private Boolean dynamicDeader;

    /**
     * 默认构造器
     */
    public SheetBeanMetaVO() {
    }

    /**
     * 参数构造
     *
     * @param templateName  模板名称
     * @param sheetNo       sheetNo
     * @param beanName      service 处理类
     * @param sheetName     sheetName
     * @param dynamicDeader 是否动态列
     */
    public SheetBeanMetaVO(String templateName, Integer sheetNo, String beanName, String sheetName,
            Boolean dynamicDeader) {
        this.templateName = templateName;
        this.sheetNo = sheetNo;
        this.beanName = beanName;
        this.sheetName = sheetName;
        this.dynamicDeader = dynamicDeader;
    }
}