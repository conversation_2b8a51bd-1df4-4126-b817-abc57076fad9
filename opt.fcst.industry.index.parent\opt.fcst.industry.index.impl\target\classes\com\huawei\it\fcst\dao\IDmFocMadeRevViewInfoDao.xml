<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IDmFocMadeViewInfoDao">
    <sql id="rev_weight_rate">
        <if test='viewFlag != null and viewFlag == "4"'>
            <choose>
                <when test='groupLevel == "LV3"'>
                    DISTINCT lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name, lv4_prod_rnd_team_code AS groupCode, lv4_prod_rd_team_cn_name AS groupCnName, 'LV4' AS groupLevel
                </when>
                <when test='groupLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name, lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS groupLevel,weight_rate
                </when>
                <when test='groupLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                    groupLevel,weight_rate
                </when>
                <when test='groupLevel == "SHIPPING_OBJECT"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <when test='groupLevel == "LV0"'>
                    DISTINCT amp.shipping_object_code AS groupCode, amp.shipping_object_cn_name AS groupCnName, 'SHIPPING_OBJECT' AS groupLevel,weight_rate
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "5"'>
            <choose>
                <when test='groupLevel == "LV3"'>
                    DISTINCT lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code AS groupCode, lv4_prod_rd_team_cn_name AS groupCnName, 'LV4' AS groupLevel
                </when>
                <when test='groupLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS groupLevel,weight_rate
                </when>
                <when test='groupLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                    groupLevel,weight_rate
                </when>
                <when test='groupLevel == "MANUFACTURE_OBJECT"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <when test='groupLevel == "SHIPPING_OBJECT"'>
                    DISTINCT amp.manufacture_object_code AS groupCode, amp.manufacture_object_cn_name AS groupCnName, 'MANUFACTURE_OBJECT' AS groupLevel
                </when>
                <when test='groupLevel == "LV0"'>
                    DISTINCT amp.shipping_object_code AS groupCode, amp.shipping_object_cn_name AS groupCnName, 'SHIPPING_OBJECT' AS groupLevel,weight_rate
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
    </sql>

    <sql id="rev_absolute_weight">
        <if test='viewFlag != null and viewFlag == "4"'>
            <choose>
                <when test='groupLevel == "LV3"'>
                    DISTINCT lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code AS groupCode, lv4_prod_rd_team_cn_name AS groupCnName, 'LV4' AS groupLevel
                </when>
                <when test='groupLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS groupLevel,absolute_weight
                </when>
                <when test='groupLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                    groupLevel,absolute_weight
                </when>
                <when test='groupLevel == "SHIPPING_OBJECT"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <when test='groupLevel == "LV0"'>
                    DISTINCT amp.shipping_object_code AS groupCode, amp.shipping_object_cn_name AS groupCnName, 'SHIPPING_OBJECT' AS groupLevel,absolute_weight
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "5"'>
            <choose>
                <when test='groupLevel == "LV3"'>
                    DISTINCT lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code AS groupCode, lv4_prod_rd_team_cn_name AS groupCnName, 'LV4' AS groupLevel
                </when>
                <when test='groupLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS groupLevel,absolute_weight
                </when>
                <when test='groupLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                    groupLevel,absolute_weight
                </when>
                <when test='groupLevel == "MANUFACTURE_OBJECT"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <when test='groupLevel == "SHIPPING_OBJECT"'>
                    DISTINCT amp.manufacture_object_code AS groupCode, amp.manufacture_object_cn_name AS groupCnName, 'MANUFACTURE_OBJECT' AS groupLevel
                </when>
                <when test='groupLevel == "LV0"'>
                    DISTINCT amp.shipping_object_code AS groupCode, amp.shipping_object_cn_name AS groupCnName, 'SHIPPING_OBJECT' AS groupLevel,absolute_weight
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
    </sql>

    <select id="madeRevViewInfoList" resultMap="resultMap">
        SELECT
        <if test='isMultipleSelect == false'>
            <include refid="rev_weight_rate"></include>
        </if>
        <if test='isMultipleSelect == true'>
            <include refid="rev_absolute_weight"></include>
        </if>
        <choose>
            <when test='groupLevel =="LV0"'>
                from fin_dm_opt_foi.${tablePreFix}_made_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_made_rev_annual_weight_t weight
                on amp.shipping_object_code = weight.group_code
                and weight.view_flag = #{viewFlag,jdbcType=VARCHAR}
                and amp.lv0_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR}
                and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                AND weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                AND weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
                AND weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                WHERE amp.view_flag = #{revViewFlag,jdbcType=VARCHAR} and amp.del_flag ='N' and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                AND amp.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
            <when test='viewFlag == "4" and groupLevel == "SHIPPING_OBJECT"'>
                from fin_dm_opt_foi.${tablePreFix}_made_view_info_d amp
                WHERE amp.view_flag = #{revViewFlag,jdbcType=VARCHAR} and amp.del_flag ='N' and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                AND amp.group_level = #{groupLevel,jdbcType=VARCHAR}
            </when>
            <when test='viewFlag == "5" and groupLevel == "SHIPPING_OBJECT"'>
                from fin_dm_opt_foi.${tablePreFix}_made_view_info_d amp
                WHERE amp.view_flag = #{revViewFlag,jdbcType=VARCHAR} and amp.del_flag ='N' and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                AND amp.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
            </when>
            <when test='viewFlag == "5" and groupLevel =="MANUFACTURE_OBJECT"'>
                from fin_dm_opt_foi.${tablePreFix}_made_view_info_d amp
                WHERE amp.view_flag = #{revViewFlag,jdbcType=VARCHAR} and amp.del_flag ='N' and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                AND amp.group_level = #{groupLevel,jdbcType=VARCHAR}
            </when>
            <when test='groupLevel == "LV1"'>
                from fin_dm_opt_foi.${tablePreFix}_made_view_info_d amp left join fin_dm_opt_foi.${tablePreFix}_made_rev_annual_weight_t weight
                on amp.lv2_prod_rnd_team_code = weight.group_code
                and weight.view_flag = #{viewFlag,jdbcType=VARCHAR}
                and amp.lv1_prod_rnd_team_code = weight.parent_code
                and nvl(amp.shipping_object_code,'snull') =  nvl(weight.shipping_object_code,'snull')
                and nvl(amp.manufacture_object_code,'snull') =  nvl(weight.manufacture_object_code,'snull')
                <if test='shippingObjectCode != null and shippingObjectCode != ""'>
                    and weight.shipping_object_code = #{shippingObjectCode,jdbcType=VARCHAR}
                </if>
                <if test='manufactureObjectCode != null and manufactureObjectCode != ""'>
                    and weight.manufacture_object_code = #{manufactureObjectCode,jdbcType=VARCHAR}
                </if>
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                AND weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                AND weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
                AND weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                WHERE amp.view_flag = #{revViewFlag,jdbcType=VARCHAR} and amp.del_flag ='N' and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
            </when>
            <when test='groupLevel == "LV2"'>
                from fin_dm_opt_foi.${tablePreFix}_made_view_info_d amp
                left join fin_dm_opt_foi.${tablePreFix}_made_rev_annual_weight_t weight
                on amp.lv3_prod_rnd_team_code = weight.group_code
                and weight.view_flag = #{viewFlag,jdbcType=VARCHAR}
                and amp.lv2_prod_rnd_team_code = weight.parent_code
                and nvl(amp.shipping_object_code,'snull') =  nvl(weight.shipping_object_code,'snull')
                and nvl(amp.manufacture_object_code,'snull') =  nvl(weight.manufacture_object_code,'snull')
                <if test='shippingObjectCode != null and shippingObjectCode != ""'>
                    and weight.shipping_object_code = #{shippingObjectCode,jdbcType=VARCHAR}
                </if>
                <if test='manufactureObjectCode != null and manufactureObjectCode != ""'>
                    and weight.manufacture_object_code = #{manufactureObjectCode,jdbcType=VARCHAR}
                </if>
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                AND weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                AND weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
                AND weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                WHERE amp.view_flag = #{revViewFlag,jdbcType=VARCHAR} and amp.del_flag ='N' and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
            </when>
            <when test='groupLevel == "LV3"'>
                from fin_dm_opt_foi.${tablePreFix}_made_view_info_d amp
                WHERE amp.view_flag = #{revViewFlag,jdbcType=VARCHAR} and amp.del_flag ='N' and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
            </when>
        </choose>
        AND amp.del_flag ='N'
        AND amp.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        AND amp.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        AND amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        <choose>
            <when test='groupLevel == "SHIPPING_OBJECT" and groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND amp.shipping_object_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='groupLevel == "MANUFACTURE_OBJECT" and groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND amp.manufacture_object_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='shippingObjectCode != null and shippingObjectCode != ""'>
            and amp.shipping_object_code = #{shippingObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='manufactureObjectCode != null and manufactureObjectCode != ""'>
            and amp.manufacture_object_code = #{manufactureObjectCode,jdbcType=VARCHAR}
        </if>
        <include refid="prod_rnd_team_code_level"></include>
        <include refid="permission_level"></include>
        <if test='isMultipleSelect == false'>
            <if test='groupLevel != "SHIPPING_OBJECT" and groupLevel != "MANUFACTURE_OBJECT" and groupLevel != "LV3"'>
                order by weight_rate desc
            </if>
        </if>
        <if test='isMultipleSelect == true'>
            <if test='groupLevel != "SHIPPING_OBJECT" and groupLevel != "MANUFACTURE_OBJECT" and groupLevel != "LV3"'>
                order by absolute_weight desc
            </if>
        </if>

    </select>
    <!-- 重量级团队维度控制 -->
    <sql id ="prod_rnd_team_code_level">
        <if test='teamLevel == "LV1" and teamCodeList != null and teamCodeList.size() > 0'>
            <foreach collection='teamCodeList' item="code" open="AND amp.lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamLevel == "LV2" and teamCodeList != null and teamCodeList.size() > 0'>
            <foreach collection='teamCodeList' item="code" open="AND amp.lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamLevel == "LV3" and teamCodeList != null and teamCodeList.size() > 0'>
            <foreach collection='teamCodeList' item="code" open="AND amp.lv3_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
    </sql>
    <!-- 重量级团队LV1和LV2控制权限 -->
    <sql id ="permission_level">
        <if test='viewFlag == "4" and groupLevel == "SHIPPING_OBJECT" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND amp.lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='viewFlag == "5" and groupLevel == "MANUFACTURE_OBJECT" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND amp.lv1_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='teamLevel == "LV1" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND amp.lv2_prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
    </sql>
  <sql id = "rev_month_weightRate">
      <if test='viewFlag != null and viewFlag == "4"'>
          <choose>
              <when test='groupLevel == "LV3"'>
                  DISTINCT lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code AS groupCode, lv4_prod_rd_team_cn_name AS groupCnName, 'LV4' AS groupLevel
              </when>
              <when test='groupLevel == "LV2"'>
                  DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS groupLevel,weight.weight_rate
              </when>
              <when test='teamLevel == "LV1"'>
                  DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                  groupLevel,weight.weight_rate
              </when>
              <when test='groupLevel == "SHIPPING_OBJECT"'>
                  DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
              </when>
              <when test='teamLevel == "LV0"'>
                  DISTINCT amp.top_shipping_object_code AS groupCode, amp.top_shipping_object_cn_name AS groupCnName, 'SHIPPING_OBJECT' AS groupLevel,weight.weight_rate
              </when>
              <otherwise>
              </otherwise>
          </choose>
      </if>
      <if test='viewFlag != null and viewFlag == "5"'>
          <choose>
              <when test='groupLevel == "LV3"'>
                  DISTINCT lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code AS groupCode, lv4_prod_rd_team_cn_name AS groupCnName, 'LV4' AS groupLevel
              </when>
              <when test='groupLevel == "LV2"'>
                  DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS groupLevel,weight.weight_rate
              </when>
              <when test='teamLevel == "LV1"'>
                  DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                  groupLevel,weight.weight_rate
              </when>
              <when test='groupLevel == "MANUFACTURE_OBJECT"'>
                  DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
              </when>
              <when test='groupLevel == "SHIPPING_OBJECT"'>
                  DISTINCT amp.top_manufacture_object_code AS groupCode, amp.top_manufacture_object_cn_name AS groupCnName, 'MANUFACTURE_OBJECT' AS groupLevel
              </when>
              <when test='teamLevel == "LV0"'>
                  DISTINCT amp.top_shipping_object_code AS groupCode, amp.top_shipping_object_cn_name AS groupCnName, 'SHIPPING_OBJECT' AS groupLevel,weight.weight_rate
              </when>
              <otherwise>
              </otherwise>
          </choose>
      </if>
  </sql>

    <sql id = "rev_month_AbsoluteRate">
        <if test='viewFlag != null and viewFlag == "4"'>
            <choose>
                <when test='groupLevel == "LV3"'>
                    DISTINCT lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code AS groupCode, lv4_prod_rd_team_cn_name AS groupCnName, 'LV4' AS groupLevel
                </when>
                <when test='groupLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS groupLevel,weight.absolute_weight
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                    groupLevel,weight.absolute_weight
                </when>
                <when test='groupLevel == "SHIPPING_OBJECT"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT amp.top_shipping_object_code AS groupCode, amp.top_shipping_object_cn_name AS groupCnName, 'SHIPPING_OBJECT' AS groupLevel,weight.absolute_weight
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test='viewFlag != null and viewFlag == "5"'>
            <choose>
                <when test='groupLevel == "LV3"'>
                    DISTINCT lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code,lv3_prod_rd_team_cn_name,lv4_prod_rnd_team_code AS groupCode, lv4_prod_rd_team_cn_name AS groupCnName, 'LV4' AS groupLevel
                </when>
                <when test='groupLevel == "LV2"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code,lv2_prod_rd_team_cn_name,lv3_prod_rnd_team_code AS groupCode, lv3_prod_rd_team_cn_name AS groupCnName, 'LV3' AS groupLevel,weight.absolute_weight
                </when>
                <when test='teamLevel == "LV1"'>
                    DISTINCT lv1_prod_rnd_team_code,lv1_prod_rd_team_cn_name,lv2_prod_rnd_team_code AS groupCode, lv2_prod_rd_team_cn_name AS groupCnName, 'LV2' AS
                    groupLevel,weight.absolute_weight
                </when>
                <when test='groupLevel == "MANUFACTURE_OBJECT"'>
                    DISTINCT lv1_prod_rnd_team_code AS groupCode, lv1_prod_rd_team_cn_name AS groupCnName, 'LV1' AS groupLevel
                </when>
                <when test='groupLevel == "SHIPPING_OBJECT"'>
                    DISTINCT amp.top_manufacture_object_code AS groupCode, amp.top_manufacture_object_cn_name AS groupCnName, 'MANUFACTURE_OBJECT' AS groupLevel
                </when>
                <when test='teamLevel == "LV0"'>
                    DISTINCT amp.top_shipping_object_code AS groupCode, amp.top_shipping_object_cn_name AS groupCnName, 'SHIPPING_OBJECT' AS groupLevel,weight.absolute_weight
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
    </sql>

    <select id="madeRevViewInfoListForMonth" resultMap="resultMap">
        SELECT
        <if test='isMultipleSelect == false'>
            <include refid="rev_month_weightRate"></include>
        </if>
        <if test='isMultipleSelect == true'>
            <include refid="rev_month_AbsoluteRate"></include>
        </if>
        <choose>
            <when test='groupLevel =="LV0"'>
                from fin_dm_opt_foi.${tablePreFix}_made_top_item_info_t amp left join fin_dm_opt_foi.${tablePreFix}_made_rev_annual_weight_t weight
                on amp.top_shipping_object_code = weight.group_code
                and weight.view_flag = #{viewFlag,jdbcType=VARCHAR}
                and amp.lv0_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                AND weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                AND weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
                AND weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                WHERE amp.view_flag = '5'
            </when>
            <when test='viewFlag == "4" and groupLevel == "SHIPPING_OBJECT"'>
                from fin_dm_opt_foi.${tablePreFix}_made_top_item_info_t amp
                WHERE amp.view_flag = '5'
            </when>
            <when test='viewFlag == "5" and (groupLevel == "SHIPPING_OBJECT"|| groupLevel == "MANUFACTURE_OBJECT")'>
                from fin_dm_opt_foi.${tablePreFix}_made_top_item_info_t amp
                WHERE amp.view_flag = '5'
            </when>
            <when test='groupLevel == "LV1"'>
                from fin_dm_opt_foi.${tablePreFix}_made_top_item_info_t amp left join fin_dm_opt_foi.${tablePreFix}_made_rev_annual_weight_t weight
                on amp.lv2_prod_rnd_team_code = weight.group_code
                and weight.view_flag = #{viewFlag,jdbcType=VARCHAR}
                and nvl(amp.top_shipping_object_code,'snull') =  nvl(weight.shipping_object_code,'snull')
                and nvl(amp.top_manufacture_object_code,'snull') =  nvl(weight.manufacture_object_code,'snull')
                <if test='shippingObjectCode != null and shippingObjectCode != ""'>
                    and weight.shipping_object_code = #{shippingObjectCode,jdbcType=VARCHAR}
                </if>
                <if test='manufactureObjectCode != null and manufactureObjectCode != ""'>
                    and weight.manufacture_object_code = #{manufactureObjectCode,jdbcType=VARCHAR}
                </if>
                and amp.lv1_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR}
                and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                AND weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                AND weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
                AND weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                WHERE amp.view_flag = '5'
            </when>
            <when test='groupLevel == "LV2"'>
                from fin_dm_opt_foi.${tablePreFix}_made_top_item_info_t amp
                left join fin_dm_opt_foi.${tablePreFix}_made_rev_annual_weight_t weight
                on amp.lv3_prod_rnd_team_code = weight.group_code
                and weight.view_flag = #{viewFlag,jdbcType=VARCHAR}
                and nvl(amp.top_shipping_object_code,'snull') =  nvl(weight.shipping_object_code,'snull')
                and nvl(amp.top_manufacture_object_code,'snull') =  nvl(weight.manufacture_object_code,'snull')
                <if test='shippingObjectCode != null and shippingObjectCode != ""'>
                    and weight.shipping_object_code = #{shippingObjectCode,jdbcType=VARCHAR}
                </if>
                <if test='manufactureObjectCode != null and manufactureObjectCode != ""'>
                    and weight.manufacture_object_code = #{manufactureObjectCode,jdbcType=VARCHAR}
                </if>
                and amp.lv2_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR}
                and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                AND weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                AND weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
                AND weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                WHERE amp.view_flag = '5'
            </when>
            <when test='groupLevel == "LV3"'>
                from fin_dm_opt_foi.${tablePreFix}_made_top_item_info_t amp
                WHERE amp.view_flag = '5'
            </when>
        </choose>
        and amp.del_flag ='N'
        and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        and amp.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        and amp.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        and amp.version_id=#{monthVersionId,jdbcType=NUMERIC}
        and amp.is_top_flag='Y'
        and amp.double_flag ='Y'
        <choose>
            <when test='groupLevel == "SHIPPING_OBJECT" and groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND amp.top_shipping_object_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='groupLevel == "MANUFACTURE_OBJECT" and groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND amp.top_manufacture_object_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='shippingObjectCode != null and shippingObjectCode != ""'>
            and amp.top_shipping_object_code = #{shippingObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='manufactureObjectCode != null and manufactureObjectCode != ""'>
            and amp.top_manufacture_object_code = #{manufactureObjectCode,jdbcType=VARCHAR}
        </if>
        <include refid="prod_rnd_team_code_level"></include>
        <include refid="permission_level"></include>
        <if test='isMultipleSelect == false'>
            <if test='groupLevel != "SHIPPING_OBJECT" and groupLevel != "MANUFACTURE_OBJECT" and groupLevel != "LV3"'>
                order by weight_rate desc
            </if>
        </if>
        <if test='isMultipleSelect == true'>
            <if test='groupLevel != "SHIPPING_OBJECT" and groupLevel != "MANUFACTURE_OBJECT" and groupLevel != "LV3"'>
                order by absolute_weight desc
            </if>
        </if>
    </select>

    <select id="madeObjectRevViewInfoList" resultMap="resultMap">
        SELECT
        <if test='isMultipleSelect == false'>
            <include refid="rev_month_weightRate"></include>
        </if>
        <if test='isMultipleSelect == true'>
            <include refid="rev_month_AbsoluteRate"></include>
        </if>
        <choose>
            <when test='groupLevel =="LV0"'>
                from fin_dm_opt_foi.${tablePreFix}_top_made_info_t amp left join fin_dm_opt_foi.${tablePreFix}_made_rev_annual_weight_t weight
                on amp.top_shipping_object_code = weight.group_code
                and weight.view_flag = #{viewFlag,jdbcType=VARCHAR}
                and amp.lv0_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR} and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                AND weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                AND weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
                AND weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                WHERE amp.view_flag = '5'
            </when>
            <when test='viewFlag == "4" and groupLevel == "SHIPPING_OBJECT"'>
                from fin_dm_opt_foi.${tablePreFix}_top_made_info_t amp
                WHERE amp.view_flag = '5'
            </when>
            <when test='viewFlag == "5" and (groupLevel == "SHIPPING_OBJECT"|| groupLevel == "MANUFACTURE_OBJECT")'>
                from fin_dm_opt_foi.${tablePreFix}_top_made_info_t amp
                WHERE amp.view_flag = '5'
            </when>
            <when test='groupLevel == "LV1"'>
                from fin_dm_opt_foi.${tablePreFix}_top_made_info_t amp left join fin_dm_opt_foi.${tablePreFix}_made_rev_annual_weight_t weight
                on amp.lv2_prod_rnd_team_code = weight.group_code
                and weight.view_flag = #{viewFlag,jdbcType=VARCHAR}
                and nvl(amp.top_shipping_object_code,'snull') =  nvl(weight.shipping_object_code,'snull')
                and nvl(amp.top_manufacture_object_code,'snull') =  nvl(weight.manufacture_object_code,'snull')
                <if test='shippingObjectCode != null and shippingObjectCode != ""'>
                    and weight.shipping_object_code = #{shippingObjectCode,jdbcType=VARCHAR}
                </if>
                <if test='manufactureObjectCode != null and manufactureObjectCode != ""'>
                    and weight.manufacture_object_code = #{manufactureObjectCode,jdbcType=VARCHAR}
                </if>
                and amp.lv1_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR}
                and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                AND weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                AND weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
                AND weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                WHERE amp.view_flag = '5'
            </when>
            <when test='groupLevel == "LV2"'>
                from fin_dm_opt_foi.${tablePreFix}_top_made_info_t amp
                left join fin_dm_opt_foi.${tablePreFix}_made_rev_annual_weight_t weight
                on amp.lv3_prod_rnd_team_code = weight.group_code
                and weight.view_flag = #{viewFlag,jdbcType=VARCHAR}
                and nvl(amp.top_shipping_object_code,'snull') =  nvl(weight.shipping_object_code,'snull')
                and nvl(amp.top_manufacture_object_code,'snull') =  nvl(weight.manufacture_object_code,'snull')
                <if test='shippingObjectCode != null and shippingObjectCode != ""'>
                    and weight.shipping_object_code = #{shippingObjectCode,jdbcType=VARCHAR}
                </if>
                <if test='manufactureObjectCode != null and manufactureObjectCode != ""'>
                    and weight.manufacture_object_code = #{manufactureObjectCode,jdbcType=VARCHAR}
                </if>
                and amp.lv2_prod_rnd_team_code = weight.parent_code
                and weight.period_year=#{periodYear,jdbcType=VARCHAR}
                and weight.version_id=#{versionId,jdbcType=NUMERIC}
                and weight.del_flag='N' and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
                AND weight.group_level = #{nextGroupLevel,jdbcType=VARCHAR}
                AND weight.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
                AND weight.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
                WHERE amp.view_flag = '5'
            </when>
            <when test='groupLevel == "LV3"'>
                from fin_dm_opt_foi.${tablePreFix}_top_made_info_t amp
                WHERE amp.view_flag = '5'
            </when>
        </choose>
        and amp.del_flag ='N'
        and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        and amp.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        and amp.lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        and amp.version_id=#{monthVersionId,jdbcType=NUMERIC}
        and amp.is_top_flag='Y'
        and amp.double_flag ='Y'
        <choose>
            <when test='groupLevel == "SHIPPING_OBJECT" and groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND amp.top_shipping_object_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <when test='groupLevel == "MANUFACTURE_OBJECT" and groupCodeList != null and groupCodeList.size() > 0'>
                <foreach collection='groupCodeList' item="code" open="AND amp.top_manufacture_object_code IN (" close=")" index="index"
                         separator=",">
                    #{code}
                </foreach>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test='shippingObjectCode != null and shippingObjectCode != ""'>
            and amp.top_shipping_object_code = #{shippingObjectCode,jdbcType=VARCHAR}
        </if>
        <if test='manufactureObjectCode != null and manufactureObjectCode != ""'>
            and amp.top_manufacture_object_code = #{manufactureObjectCode,jdbcType=VARCHAR}
        </if>
        <include refid="prod_rnd_team_code_level"></include>
        <include refid="permission_level"></include>
        <if test='isMultipleSelect == false'>
            <if test='groupLevel != "SHIPPING_OBJECT" and groupLevel != "MANUFACTURE_OBJECT" and groupLevel != "LV3"'>
                order by weight_rate desc
            </if>
        </if>
        <if test='isMultipleSelect == true'>
            <if test='groupLevel != "SHIPPING_OBJECT" and groupLevel != "MANUFACTURE_OBJECT" and groupLevel != "LV3"'>
                order by absolute_weight desc
            </if>
        </if>
    </select>

    <select id="madeRevViewInfoKeyWordList" resultMap="resultMap">
        SELECT
        <if test='groupLevel == "MANUFACTURE_OBJECT"'>
            DISTINCT shipping_object_code,shipping_object_cn_name,manufacture_object_code,manufacture_object_cn_name
        </if>
        from fin_dm_opt_foi.${tablePreFix}_made_view_info_d
        WHERE view_flag = '3' and del_flag ='N'
        AND caliber_flag = #{caliberFlag}
        AND oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        AND lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        AND group_level = 'CATEGORY'
        <if test="keyWord != null and keyWord != ''">
            AND (UPPER(manufacture_object_code) LIKE CONCAT(CONCAT('%', UPPER(#{keyWord})) ,'%')
            OR UPPER(manufacture_object_cn_name) LIKE CONCAT(CONCAT('%', UPPER(#{keyWord})) ,'%')
            )
        </if>
    </select>

    <select id="madeRevViewInfoKeyWordForMonth" resultMap="resultMap">
        SELECT
        <if test='groupLevel == "MANUFACTURE_OBJECT"'>
            DISTINCT top_shipping_object_code as shipping_object_code ,top_shipping_object_cn_name as shipping_object_cn_name,
            top_manufacture_object_code as manufacture_object_code ,top_manufacture_object_cn_name as manufacture_object_cn_name
        </if>
        from fin_dm_opt_foi.${tablePreFix}_made_top_item_info_t
        where del_flag = 'N'
        AND IS_TOP_FLAG ='Y'
        AND DOUBLE_FLAG ='Y'
        AND version_id = #{monthVersionId}
        AND view_flag = '6'
        AND caliber_flag = #{caliberFlag}
        AND oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        AND lv0_prod_list_code = #{lv0ProdListCode,jdbcType=VARCHAR}
        <if test="keyWord != null and keyWord != ''">
            AND (UPPER(top_manufacture_object_code) LIKE CONCAT(CONCAT('%', UPPER(#{keyWord})) ,'%')
            OR UPPER(top_manufacture_object_cn_name) LIKE CONCAT(CONCAT('%', UPPER(#{keyWord})) ,'%')
            )
        </if>
    </select>
</mapper>