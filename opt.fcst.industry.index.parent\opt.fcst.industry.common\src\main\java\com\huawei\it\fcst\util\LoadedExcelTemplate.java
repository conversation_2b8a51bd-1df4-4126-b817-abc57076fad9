/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.util;

import cn.hutool.poi.excel.WorkbookUtil;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;

import com.huawei.it.fcst.exeption.TemplateNotFundException;
import com.huawei.it.jalor5.core.exception.ApplicationException;

import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.io.InputStream;
import java.text.MessageFormat;

public class LoadedExcelTemplate {
    private static final String TEMPLATE_PATH = "excel/export/template/{0}.xlsx";

    /**
     * 导出模板加载
     *
     * @param filePath     文件路径
     * @param templateName 模板路径
     * @return excel
     * @throws ApplicationException
     */
    public static ExcelWriter getExcelWriterTemplate(String filePath, String templateName) throws ApplicationException {
        try (InputStream inputStream = new ClassPathResource(
                MessageFormat.format(TEMPLATE_PATH, templateName)).getInputStream()) {
            return EasyExcel.write(filePath).withTemplate(inputStream).excelType(ExcelTypeEnum.XLSX).build();
        } catch (IOException ex) {
            throw new TemplateNotFundException("not found template.", ex);
        }
    }

    ;

    /**
     * 导出模板加载
     *
     * @param templateName 模板路径
     * @return Workbook
     * @throws ApplicationException 异常信息
     */
    public static Workbook getExcelTemplate(String templateName) throws ApplicationException {
        try (InputStream inputStream = new ClassPathResource(
                MessageFormat.format(TEMPLATE_PATH, templateName)).getInputStream()) {
            return WorkbookUtil.createBook(inputStream);
        } catch (IOException ex) {
            throw new TemplateNotFundException("not found template.", ex);
        }
    }
}
