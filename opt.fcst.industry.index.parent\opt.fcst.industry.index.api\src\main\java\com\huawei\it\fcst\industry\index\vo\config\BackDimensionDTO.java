/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.config;

import com.huawei.it.fcst.industry.index.vo.relation.DmDimCatgModlCegIctVO;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * BackDimensionDTO Class
 *
 * <AUTHOR>
 * @since 2023/3/15
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "配置管理页面维度关系出参")
public class BackDimensionDTO {
    /**
     *
     * 版本对象
     */
    private DmFocVersionInfoDTO dmFocVersionInfoDTO;

    /**
     *
     * 错误列表
     */
    private List<DmDimCatgModlCegIctVO> errorList;
}
