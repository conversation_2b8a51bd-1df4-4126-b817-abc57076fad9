/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.impl.template;

import com.huawei.it.fcst.export.IExcelTemplateBeanManager;
import com.huawei.it.fcst.export.vo.ExcelTemplateBeanManager;
import com.huawei.it.fcst.export.vo.SheetBeanMetaVO;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 历史SPART清单导出模板
 *
 * <AUTHOR>
 * @since 2024-11-7
 */
@Getter
public enum HistorySpartListTemplateEnum implements IExcelTemplateBeanManager {
    CONFIG_01("01", "HistorySpartListExportTemplate", "价格指数-产业-ICT-SPART清单", "价格指数-产业-ICT-SPART清单") {
        @Override
        public ExcelTemplateBeanManager getExcelTemPlateBeanManager() {
            ExcelTemplateBeanManager excelTemplateBeanManager = new ExcelTemplateBeanManager();
            List<SheetBeanMetaVO> configList = new ArrayList<>();
            configList.add(new SheetBeanMetaVO(CONFIG_01.templateName, 0, "HistorySpartExportDataProvider", "历史SPART清单", Boolean.FALSE));
            excelTemplateBeanManager.setSheetBeans(configList);
            excelTemplateBeanManager.setTemplateName(CONFIG_01.templateName);
            excelTemplateBeanManager.setModuleType(CONFIG_01.moduleType);
            excelTemplateBeanManager.setDesc(CONFIG_01.desc);
            return excelTemplateBeanManager;
        }
    };
    private String code;
    private String templateName;
    private String moduleType;
    private String desc;

    HistorySpartListTemplateEnum(String code, String templateName, String moduleType, String desc) {
        this.code = code;
        this.templateName = templateName;
        this.moduleType = moduleType;
        this.desc = desc;
    }

}
