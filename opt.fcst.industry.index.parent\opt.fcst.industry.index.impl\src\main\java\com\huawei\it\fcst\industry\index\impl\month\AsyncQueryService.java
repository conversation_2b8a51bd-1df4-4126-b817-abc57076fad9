/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.month;

import com.huawei.it.fcst.industry.index.dao.IAnnualAmpDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMadeMonthYoyDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMonthAccCostAmpDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocMonthYoyDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTotalMonthCostIdxDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocTotalMonthYoyDao;
import com.huawei.it.fcst.industry.index.dao.IDmFocVersionDao;
import com.huawei.it.fcst.industry.index.dao.IMadeAnnualAmpDao;
import com.huawei.it.fcst.industry.index.dao.IStandardDao;
import com.huawei.it.fcst.industry.index.dao.ITotalAnnualAmpDao;
import com.huawei.it.fcst.industry.index.utils.ObjectCopyUtil;
import com.huawei.it.fcst.industry.index.vo.annual.AnnualAnalysisVO;
import com.huawei.it.fcst.industry.index.vo.annual.DmFocAnnualAmpVO;
import com.huawei.it.fcst.industry.index.vo.month.DmFocMonthCostIdxVO;
import com.huawei.it.fcst.industry.index.vo.month.DmFocMonthYoyVO;
import com.huawei.it.fcst.industry.index.vo.month.MonthAnalysisVO;
import com.huawei.it.fcst.industry.index.vo.standard.StandardAnalysisVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.scheduling.annotation.EnableAsync;

import javax.inject.Inject;
import javax.inject.Named;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

/**
 * AsyncExportService Class
 *
 * <AUTHOR>
 * @since 2023/03/15
 */
@Slf4j
@EnableAsync
@Named(value = "asyncQueryService")
public class AsyncQueryService {
    @Inject
    private IDmFocMonthCostIdxDao dmFocMonthCostIdxDao;

    @Inject
    private IDmFocMadeMonthCostIdxDao dmFocMadeMonthCostIdxDao;

    @Inject
    private IDmFocMonthAccCostAmpDao dmFocMonthAccCostAmpDao;

    @Inject
    private IDmFocTotalMonthCostIdxDao dmFocTotalMonthCostIdxDao;

    @Inject
    private IAnnualAmpDao annualAmpDao;

    @Inject
    private IMadeAnnualAmpDao madeAnnualAmpDao;

    @Inject
    private ITotalAnnualAmpDao totalAnnualAmpDao;

    @Inject
    private IDmFocMonthYoyDao dmFocMonthYoyDao;

    @Inject
    private IDmFocMadeMonthYoyDao dmFocMadeMonthYoyDao;

    @Inject
    private IDmFocTotalMonthYoyDao dmFocTotalMonthYoyDao;

    @Inject
    private IDmFocVersionDao dmFocVersionDao;

    @Inject
    private IStandardDao iStandardDao;

    /**
     * 查询采购成本下的产业成本指数
     *
     * @param monthAnalysisVO 参数VO
     */
    @Async("asyncServiceExecutor")
    public Future<List<DmFocMonthCostIdxVO>> findPurchaseCostIdxList(MonthAnalysisVO monthAnalysisVO) {
        log.info(">>>Begin AsyncQueryService::findPurchaseCostIdxList");
        List<DmFocMonthCostIdxVO> dmFocPriceIndexVOList = new ArrayList<>();
        if (monthAnalysisVO.getCompareAnalysisVO().getIsCompareFlag()) {
            dmFocPriceIndexVOList = dmFocMonthCostIdxDao.findCompareDmFocPriceIndexVOList(monthAnalysisVO);
        } else {
            dmFocPriceIndexVOList = dmFocMonthCostIdxDao.findDmFocPriceIndexVOList(monthAnalysisVO);
        }
        return new AsyncResult<>(dmFocPriceIndexVOList);
    }



    /**
     * 查询月度累计成本涨跌图
     *
     * @param standardAnalysisVO 参数VO
     */
    @Async("asyncServiceExecutor")
    public Future<List<DmFocAnnualAmpVO>> findMonthAccCostAmpList(StandardAnalysisVO standardAnalysisVO) {
        log.info(">>>Begin AsyncQueryService::findMonthAccCostAmpList");
        return new AsyncResult<>(iStandardDao.findDmFocMonthAccCostAmpList(standardAnalysisVO));
    }

    /**
     * 查询制造成本下的产业成本指数 无预测数
     *
     * @param monthAnalysisVO 参数VO
     */
    @Async("asyncServiceExecutor")
    public Future<List<DmFocMonthCostIdxVO>> findMadeCostIdxList(MonthAnalysisVO monthAnalysisVO) {
        log.info(">>>Begin AsyncQueryService::findMadeCostIdxList");
        MonthAnalysisVO paramsVO = ObjectCopyUtil.copy(monthAnalysisVO, MonthAnalysisVO.class);
        setStartEndTime(paramsVO);
        List<DmFocMonthCostIdxVO> dmFocPriceIndexVOList = new ArrayList<>();
        if (monthAnalysisVO.getCompareAnalysisVO().getIsCompareFlag()) {
            dmFocPriceIndexVOList = dmFocMadeMonthCostIdxDao.findMadeComparePriceIndexVOList(paramsVO);
        } else {
            dmFocPriceIndexVOList = dmFocMadeMonthCostIdxDao.findMadePriceIndexVOList(paramsVO);
        }
        return new AsyncResult<>(dmFocPriceIndexVOList);
    }

    private void setStartEndTime(MonthAnalysisVO paramsVO) {
        Map<String, Long> startEndTime = dmFocMadeMonthCostIdxDao.findStartEndTime(paramsVO.getGranularityType(), paramsVO.getTablePreFix());
        paramsVO.setPeriodStartTime(Integer.parseInt(startEndTime.get("start").toString()));
        paramsVO.setPeriodEndTime(Integer.parseInt(startEndTime.get("end").toString()));
    }

    private void setEndTime(MonthAnalysisVO paramsVO) {
        Long actualMonthNum = dmFocMonthCostIdxDao.findActualMonthNum(paramsVO.getTablePreFix());
        paramsVO.setPeriodEndTime(Integer.valueOf(actualMonthNum.toString()));
    }

    /**
     * 查询总成本下的产业成本指数 无预测数
     *
     * @param monthAnalysisVO 参数VO
     */
    @Async("asyncServiceExecutor")
    public Future<List<DmFocMonthCostIdxVO>> findTotalCostIdxList(MonthAnalysisVO monthAnalysisVO) {
        log.info(">>>Begin AsyncQueryService::findTotalCostIdxList");
        MonthAnalysisVO paramsVO = ObjectCopyUtil.copy(monthAnalysisVO, MonthAnalysisVO.class);
        setEndTime(paramsVO);
        paramsVO.setMonthVersionId(dmFocVersionDao.findAnnualVersion(monthAnalysisVO.getTablePreFix()).getVersionId());
        List<DmFocMonthCostIdxVO> dmFocPriceIndexVOList = new ArrayList<>();
        if (monthAnalysisVO.getCompareAnalysisVO().getIsCompareFlag()) {
            dmFocPriceIndexVOList = dmFocTotalMonthCostIdxDao.findTotalComparePriceIndexVOList(paramsVO);
        } else {
            dmFocPriceIndexVOList = dmFocTotalMonthCostIdxDao.findTotalPriceIndexVOList(paramsVO);
        }

        return new AsyncResult<>(dmFocPriceIndexVOList);
    }


    /**
     * 查询采购成本下的年度涨跌多子项 按照权重排序
     *
     * @param annualAnalysisVO 参数VO
     */
    @Async("asyncServiceExecutor")
    public Future<List<DmFocAnnualAmpVO>> multiIndustryCostChartMultiSelect(AnnualAnalysisVO annualAnalysisVO) {
        log.info(">>>Begin AsyncQueryService::multiIndustryCostChartMutilSelect");
        return new AsyncResult<>(annualAmpDao.multiIndustryCostChartMultiSelect(annualAnalysisVO));
    }

    /**
     * 查询制造成本下的年度涨跌多子项 按照权重排序
     *
     * @param annualAnalysisVO 参数VO
     */
    @Async("asyncServiceExecutor")
    public Future<List<DmFocAnnualAmpVO>> madeMultiIndustryCostChartMultiSelect(AnnualAnalysisVO annualAnalysisVO) {
        log.info(">>>Begin AsyncQueryService::madeMultiIndustryCostChartMutilSelect");
        AnnualAnalysisVO paramsVO = ObjectCopyUtil.copy(annualAnalysisVO, AnnualAnalysisVO.class);
        return new AsyncResult<>(madeAnnualAmpDao.madeMultiIndustryCostChartMultiSelect(paramsVO));
    }

    /**
     * 查询总成本下的年度涨跌多子项  按照权重排序
     *
     * @param annualAnalysisVO 参数VO
     */
    @Async("asyncServiceExecutor")
    public Future<List<DmFocAnnualAmpVO>> totalMultiIndustryCostChartMultiSelect(AnnualAnalysisVO annualAnalysisVO) {
        log.info(">>>Begin AsyncQueryService::totalMultiIndustryCostChartMutilSelect");
        AnnualAnalysisVO paramsVO = ObjectCopyUtil.copy(annualAnalysisVO, AnnualAnalysisVO.class);
        return new AsyncResult<>(totalAnnualAmpDao.totalMultiIndustryCostChartMutilSelect(paramsVO));
    }

    /**
     * 查询采购成本下的年度涨跌幅 按照权重排序
     *
     * @param annualAnalysisVO 参数VO
     */
    @Async("asyncServiceExecutor")
    public Future<List<DmFocAnnualAmpVO>> allIndustryCost(AnnualAnalysisVO annualAnalysisVO) {
        log.info(">>>Begin AsyncQueryService::allIndustryCost");
        return new AsyncResult<>(annualAmpDao.allIndustryCost(annualAnalysisVO));
    }

    /**
     * 查询制造成本下的年度涨跌幅 按照权重排序
     *
     * @param annualAnalysisVO 参数VO
     */
    @Async("asyncServiceExecutor")
    public Future<List<DmFocAnnualAmpVO>> madeAllIndustryCost(AnnualAnalysisVO annualAnalysisVO) {
        log.info(">>>Begin AsyncQueryService::madeAllIndustryCost");
        AnnualAnalysisVO paramsVO = ObjectCopyUtil.copy(annualAnalysisVO, AnnualAnalysisVO.class);
        return new AsyncResult<>(madeAnnualAmpDao.madeAllIndustryCost(paramsVO));
    }

    /**
     * 查询总成本下的年度涨跌幅  按照权重排序
     *
     * @param annualAnalysisVO 参数VO
     */
    @Async("asyncServiceExecutor")
    public Future<List<DmFocAnnualAmpVO>> totalAllIndustryCost(AnnualAnalysisVO annualAnalysisVO) {
        log.info(">>>Begin AsyncQueryService::totalAllIndustryCost");
        AnnualAnalysisVO paramsVO = ObjectCopyUtil.copy(annualAnalysisVO, AnnualAnalysisVO.class);
        return new AsyncResult<>(totalAnnualAmpDao.totalAllIndustryCost(paramsVO));
    }

    /**
     * 查询总成本下的产业成本指数
     *
     * @param monthAnalysisVO 参数VO
     */
    public void findTotalCostIdxDataList(List<DmFocMonthCostIdxVO> priceIndexVOList, MonthAnalysisVO monthAnalysisVO) {
        log.info(">>>Begin AsyncQueryService::findTotalCostIdxDataList");
        Future<List<DmFocMonthCostIdxVO>> purchaseCostIdxList = findPurchaseCostIdxList(monthAnalysisVO);
        Future<List<DmFocMonthCostIdxVO>> madeCostIdxList = findMadeCostIdxList(monthAnalysisVO);
        Future<List<DmFocMonthCostIdxVO>> totalCostIdxList = findTotalCostIdxList(monthAnalysisVO);
        while (true) {
            if (purchaseCostIdxList.isDone() && madeCostIdxList.isDone() && totalCostIdxList.isDone()) {
                break;
            }
        }
        try {
            priceIndexVOList.addAll(purchaseCostIdxList.get());
            priceIndexVOList.addAll(madeCostIdxList.get());
            priceIndexVOList.addAll(totalCostIdxList.get());
        } catch (Exception ex) {
            log.error(">>>>>An exception occurs with AsyncQueryService::findTotalCostIdxDataList");
            log.error(ex.getMessage());
        }
    }

    /**
     * 查询采购成本下的产业成本指数-同比
     *
     * @param monthAnalysisVO 参数VO
     */
    @Async("asyncServiceExecutor")
    public Future<List<DmFocMonthYoyVO>> findMonthPurchaseYoyList(MonthAnalysisVO monthAnalysisVO) {
        log.info(">>>Begin AsyncQueryService::findMonthPurchaseYoyList");
        monthAnalysisVO.setYoyFlag("YOY");
        return new AsyncResult<>(dmFocMonthYoyDao.findDmFocMonthYoyVOList(monthAnalysisVO));
    }

    /**
     * 查询采购成本下的产业成本指数-环比
     *
     * @param monthAnalysisVO 参数VO
     */
    @Async("asyncServiceExecutor")
    public Future<List<DmFocMonthYoyVO>> findMonthPurchasePopList(MonthAnalysisVO monthAnalysisVO) {
        log.info(">>>Begin AsyncQueryService::findMonthPurchasePopList");
        monthAnalysisVO.setYoyFlag("POP");
        return new AsyncResult<>(dmFocMonthYoyDao.findDmFocMonthYoyVOList(monthAnalysisVO));
    }

    /**
     * 查询制造成本下的产业成本指数-同比 无预测数
     *
     * @param monthAnalysisVO 参数VO
     */
    @Async("asyncServiceExecutor")
    public Future<List<DmFocMonthYoyVO>> findMonthManufactureYoyList(MonthAnalysisVO monthAnalysisVO) {
        log.info(">>>Begin AsyncQueryService::findMonthManufactureYoyList");
        MonthAnalysisVO paramsVO = ObjectCopyUtil.copy(monthAnalysisVO, MonthAnalysisVO.class);
        paramsVO.setYoyFlag("YOY");
        setStartEndTime(paramsVO);
        return new AsyncResult<>(dmFocMadeMonthYoyDao.findMadeMonthYoyVOList(paramsVO));
    }

    /**
     * 查询制造成本下的产业成本指数-环比 无预测数
     *
     * @param monthAnalysisVO 参数VO
     */
    @Async("asyncServiceExecutor")
    public Future<List<DmFocMonthYoyVO>> findMonthManufacturePopList(MonthAnalysisVO monthAnalysisVO) {
        log.info(">>>Begin AsyncQueryService::findMonthManufacturePopList");
        MonthAnalysisVO paramsVO = ObjectCopyUtil.copy(monthAnalysisVO, MonthAnalysisVO.class);
        paramsVO.setYoyFlag("POP");
        setStartEndTime(paramsVO);
        return new AsyncResult<>(dmFocMadeMonthYoyDao.findMadeMonthYoyVOList(paramsVO));
    }

    /**
     * 查询总成本下的产业成本指数-同比 无预测数
     *
     * @param monthAnalysisVO 参数VO
     */
    @Async("asyncServiceExecutor")
    public Future<List<DmFocMonthYoyVO>> findMonthTotalYoyList(MonthAnalysisVO monthAnalysisVO) {
        log.info(">>>Begin AsyncQueryService::findMonthTotalYoyList");
        MonthAnalysisVO paramsVO = ObjectCopyUtil.copy(monthAnalysisVO, MonthAnalysisVO.class);
        paramsVO.setYoyFlag("YOY");
        setEndTime(paramsVO);
        return new AsyncResult<>(dmFocTotalMonthYoyDao.findTotalMonthYoyVOList(paramsVO));
    }

    /**
     * 查询三个成本类型的数据
     *
     * @param standardAnalysisVO 参数VO
     */
    public void findMonthAccAllDataList(List<DmFocAnnualAmpVO> priceIndexVOList, StandardAnalysisVO standardAnalysisVO) {
        log.info(">>>Begin AsyncQueryService::findMonthAccAllDataList");
        standardAnalysisVO.setCostType("STD");
        Future<List<DmFocAnnualAmpVO>> standardCostIdxList = findMonthAccCostAmpList(standardAnalysisVO);
        standardAnalysisVO.setCostType("SAME");
        Future<List<DmFocAnnualAmpVO>> sameCodeCostIdxList = findMonthAccCostAmpList(standardAnalysisVO);
        standardAnalysisVO.setCostType("REPLACE");
        Future<List<DmFocAnnualAmpVO>> reSearchCostIdxList = findMonthAccCostAmpList(standardAnalysisVO);
        while (true) {
            if (standardCostIdxList.isDone() && sameCodeCostIdxList.isDone() && reSearchCostIdxList.isDone()) {
                break;
            }
        }
        try {
            priceIndexVOList.addAll(standardCostIdxList.get());
            priceIndexVOList.addAll(sameCodeCostIdxList.get());
            priceIndexVOList.addAll(reSearchCostIdxList.get());
        } catch (Exception ex) {
            log.error(">>>>>An exception occurs with AsyncQueryService::findMonthAccAllDataList");
            log.error(ex.getMessage());
        }
    }

    /**
     * 查询总成本下的产业成本指数-环比 无预测数
     *
     * @param monthAnalysisVO 参数VO
     */
    @Async("asyncServiceExecutor")
    public Future<List<DmFocMonthYoyVO>> findMonthTotalPopList(MonthAnalysisVO monthAnalysisVO) {
        log.info(">>>Begin AsyncQueryService::findMonthTotalPopList");
        MonthAnalysisVO paramsVO = ObjectCopyUtil.copy(monthAnalysisVO, MonthAnalysisVO.class);
        paramsVO.setYoyFlag("POP");
        setEndTime(paramsVO);
        return new AsyncResult<>(dmFocTotalMonthYoyDao.findTotalMonthYoyVOList(paramsVO));
    }

    /**
     * 查询总成本下的产业成本指数-同比/环比
     *
     * @param monthAnalysisVO 参数VO
     * @param yoyList 同比list
     * @param popList 环比list
     */
    public void findTotalYoyAndPopDataList(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthYoyVO> yoyList, List<DmFocMonthYoyVO> popList) {
        log.info(">>>Begin AsyncQueryService::findTotalYoyAndPopDataList");
        Future<List<DmFocMonthYoyVO>> monthPurchaseYoyList = new AsyncResult<>(new ArrayList<>());
        Future<List<DmFocMonthYoyVO>> monthManufactureYoyList =  new AsyncResult<>(new ArrayList<>());
        Future<List<DmFocMonthYoyVO>> monthTotalYoyList =  new AsyncResult<>(new ArrayList<>());
        Future<List<DmFocMonthYoyVO>> monthPurchasePopList = new AsyncResult<>(new ArrayList<>());
        Future<List<DmFocMonthYoyVO>> monthManufacturePopList = new AsyncResult<>(new ArrayList<>());
        Future<List<DmFocMonthYoyVO>> monthTotalPopList = new AsyncResult<>(new ArrayList<>());
        if ("YOY".equals(monthAnalysisVO.getYoyOrPop())) {
            monthPurchaseYoyList = findMonthPurchaseYoyList(monthAnalysisVO);
            monthManufactureYoyList = findMonthManufactureYoyList(monthAnalysisVO);
            monthTotalYoyList =  findMonthTotalYoyList(monthAnalysisVO);
        }
        if ("POP".equals(monthAnalysisVO.getYoyOrPop())) {
            monthPurchasePopList = findMonthPurchasePopList(monthAnalysisVO);
            monthManufacturePopList = findMonthManufacturePopList(monthAnalysisVO);
            monthTotalPopList = findMonthTotalPopList(monthAnalysisVO);
        }
        if (StringUtils.isBlank(monthAnalysisVO.getYoyOrPop())) {
            monthPurchaseYoyList = findMonthPurchaseYoyList(monthAnalysisVO);
            monthPurchasePopList = findMonthPurchasePopList(monthAnalysisVO);
            monthManufactureYoyList = findMonthManufactureYoyList(monthAnalysisVO);
            monthManufacturePopList = findMonthManufacturePopList(monthAnalysisVO);
            monthTotalYoyList = findMonthTotalYoyList(monthAnalysisVO);
            monthTotalPopList = findMonthTotalPopList(monthAnalysisVO);
        }
        getYoyAndPopList(monthAnalysisVO, yoyList, popList, monthPurchaseYoyList, monthManufactureYoyList, monthTotalYoyList, monthPurchasePopList, monthManufacturePopList, monthTotalPopList);
    }

    private void getYoyAndPopList(MonthAnalysisVO monthAnalysisVO, List<DmFocMonthYoyVO> yoyList, List<DmFocMonthYoyVO> popList, Future<List<DmFocMonthYoyVO>> monthPurchaseYoyList, Future<List<DmFocMonthYoyVO>> monthManufactureYoyList, Future<List<DmFocMonthYoyVO>> monthTotalYoyList, Future<List<DmFocMonthYoyVO>> monthPurchasePopList, Future<List<DmFocMonthYoyVO>> monthManufacturePopList, Future<List<DmFocMonthYoyVO>> monthTotalPopList) {
        while (true) {
            if (StringUtils.isBlank(monthAnalysisVO.getYoyOrPop())) {
                boolean yoyFlag = monthPurchaseYoyList.isDone() && monthManufactureYoyList.isDone() && monthTotalYoyList.isDone();
                boolean popFlag = monthPurchasePopList.isDone() && monthManufacturePopList.isDone() && monthTotalPopList.isDone();
                if (yoyFlag && popFlag) {
                    break;
                }
            }
            if ("YOY".equals(monthAnalysisVO.getYoyOrPop())) {
                boolean yoyFlag = monthPurchaseYoyList.isDone() && monthManufactureYoyList.isDone() && monthTotalYoyList.isDone();
                if (yoyFlag) {
                    break;
                }
            }
            if ("POP".equals(monthAnalysisVO.getYoyOrPop())) {
                boolean popFlag = monthPurchasePopList.isDone() && monthManufacturePopList.isDone() && monthTotalPopList.isDone();
                if (popFlag) {
                    break;
                }
            }
        }
        try {
            if (StringUtils.isBlank(monthAnalysisVO.getYoyOrPop())) {
                yoyList.addAll(monthPurchaseYoyList.get());
                yoyList.addAll(monthManufactureYoyList.get());
                yoyList.addAll(monthTotalYoyList.get());
                popList.addAll(monthPurchasePopList.get());
                popList.addAll(monthManufacturePopList.get());
                popList.addAll(monthTotalPopList.get());
            }
            if ("YOY".equals(monthAnalysisVO.getYoyOrPop())) {
                yoyList.addAll(monthPurchaseYoyList.get());
                yoyList.addAll(monthManufactureYoyList.get());
                yoyList.addAll(monthTotalYoyList.get());
            }
            if ("POP".equals(monthAnalysisVO.getYoyOrPop())) {
                popList.addAll(monthPurchasePopList.get());
                popList.addAll(monthManufacturePopList.get());
                popList.addAll(monthTotalPopList.get());
            }
        } catch (Exception ex) {
            log.error(">>>>>An exception occurs with AsyncQueryService::findTotalYoyAndPopDataList");
            log.error(ex.getMessage());
        }
    }

    /**
     * 查询总成本下的产业年度涨跌多子项
     *
     * @param annualAnalysisVO 参数VO
     */
    public void findMultiTotalAnnualAmpDataList(List<DmFocAnnualAmpVO> allAnnualAmpDataList, List<DmFocAnnualAmpVO> totalAnnualAmpDataList,AnnualAnalysisVO annualAnalysisVO) {
        log.info(">>>Begin AsyncQueryService::findTotalAnnualAmpDataList");
        Future<List<DmFocAnnualAmpVO>> multiIndustryCost = multiIndustryCostChartMultiSelect(annualAnalysisVO);
        Future<List<DmFocAnnualAmpVO>> madeMultiIndustryCost = madeMultiIndustryCostChartMultiSelect(annualAnalysisVO);
        Future<List<DmFocAnnualAmpVO>> totalMultiIndustryCost = totalMultiIndustryCostChartMultiSelect(annualAnalysisVO);
        while (true) {
            if (multiIndustryCost.isDone() && madeMultiIndustryCost.isDone() && totalMultiIndustryCost.isDone()) {
                break;
            }
        }
        try {
            // 三个成本的数据汇总在一起
            allAnnualAmpDataList.addAll(totalMultiIndustryCost.get());
            allAnnualAmpDataList.addAll(madeMultiIndustryCost.get());
            allAnnualAmpDataList.addAll(multiIndustryCost.get());
            // 总成本的数据
            totalAnnualAmpDataList.addAll(totalMultiIndustryCost.get());
        } catch (Exception ex) {
            log.error(">>>>>An exception occurs with AsyncQueryService::findTotalAnnualAmpDataList");
            log.error(ex.getMessage());
        }
    }

    /**
     * 查询总成本下的产业年度涨跌幅
     *
     * @param annualAnalysisVO 参数VO
     */
    public void findTotalAllIndustryCost(List<DmFocAnnualAmpVO> allAnnualIndustryCost,AnnualAnalysisVO annualAnalysisVO) {
        log.info(">>>Begin AsyncQueryService::findTotalAllIndustryCost");
        Future<List<DmFocAnnualAmpVO>> allIndustryCost = allIndustryCost(annualAnalysisVO);
        Future<List<DmFocAnnualAmpVO>> madeAllIndustryCost = madeAllIndustryCost(annualAnalysisVO);
        Future<List<DmFocAnnualAmpVO>> totalAllIndustryCost = totalAllIndustryCost(annualAnalysisVO);
        while (true) {
            if (allIndustryCost.isDone() && madeAllIndustryCost.isDone() && totalAllIndustryCost.isDone()) {
                break;
            }
        }
        try {
            // 三个成本的数据汇总在一起
            allAnnualIndustryCost.addAll(allIndustryCost.get());
            allAnnualIndustryCost.addAll(madeAllIndustryCost.get());
            allAnnualIndustryCost.addAll(totalAllIndustryCost.get());
        } catch (Exception ex) {
            log.error(">>>>>An exception occurs with AsyncQueryService::findTotalAllIndustryCost");
            log.error(ex.getMessage());
        }
    }

    public void findOverviewDataList(List<DmFocAnnualAmpVO> dmFocAnnualResultList, StandardAnalysisVO standardAnalysisVO) {

        Future<List<DmFocAnnualAmpVO>> overviewStdAmpList = findOverviewStdAmpList(standardAnalysisVO);
        Future<List<DmFocAnnualAmpVO>> overviewSameAmpList = findOverviewSameAmpList(standardAnalysisVO);
        Future<List<DmFocAnnualAmpVO>> overviewReplaceAmpList = findOverviewReplaceAmpList(standardAnalysisVO);
        while (true) {
            if (overviewStdAmpList.isDone() && overviewSameAmpList.isDone() && overviewReplaceAmpList.isDone()) {
                break;
            }
        }
        try {
            // 三个成本的数据汇总在一起
            dmFocAnnualResultList.addAll(overviewStdAmpList.get());
            dmFocAnnualResultList.addAll(overviewSameAmpList.get());
            dmFocAnnualResultList.addAll(overviewReplaceAmpList.get());
        } catch (Exception ex) {
            log.error(">>>>>An exception occurs with AsyncQueryService::findOverviewDataList");
            log.error(ex.getMessage());
        }
    }

    @Async("asyncServiceExecutor")
    public Future<List<DmFocAnnualAmpVO>> findOverviewStdAmpList(StandardAnalysisVO standardAnalysisVO) {
        log.info(">>>Begin AsyncQueryService::findOverviewStdAmpList");
        List<DmFocAnnualAmpVO> overviewStdAmpList = iStandardDao.industryStdAmpCostList(standardAnalysisVO);
        return new AsyncResult<>(overviewStdAmpList);
    }

    @Async("asyncServiceExecutor")
    public Future<List<DmFocAnnualAmpVO>> findOverviewSameAmpList(StandardAnalysisVO standardAnalysisVO) {
        log.info(">>>Begin AsyncQueryService::findOverviewSameAmpList");
        List<DmFocAnnualAmpVO> overvieSameAmpList = iStandardDao.industrySameAmpCostList(standardAnalysisVO);
        return new AsyncResult<>(overvieSameAmpList);
    }

    @Async("asyncServiceExecutor")
    public Future<List<DmFocAnnualAmpVO>> findOverviewReplaceAmpList(StandardAnalysisVO standardAnalysisVO) {
        log.info(">>>Begin AsyncQueryService::findOverviewReplaceAmpList");
        List<DmFocAnnualAmpVO> overvieReplaceAmpList = iStandardDao.industryReplaceAmpCostList(standardAnalysisVO);
        return new AsyncResult<>(overvieReplaceAmpList);
    }

    public void findMultiStandDataList(List<DmFocAnnualAmpVO> dmFocAnnualAmpResultList, StandardAnalysisVO standardAnalysisVO) {

        Future<List<DmFocAnnualAmpVO>> multiStdAmpList = findMultiStdAmpList(standardAnalysisVO);
        Future<List<DmFocAnnualAmpVO>> multiSameAmpList = findMultiSameAmpList(standardAnalysisVO);
        Future<List<DmFocAnnualAmpVO>> multiReplaceAmpList = findMultiReplaceAmpList(standardAnalysisVO);
        while (true) {
            if (multiStdAmpList.isDone() && multiSameAmpList.isDone() && multiReplaceAmpList.isDone()) {
                break;
            }
        }
        try {
            // 三个成本的数据汇总在一起
            dmFocAnnualAmpResultList.addAll(multiStdAmpList.get());
            dmFocAnnualAmpResultList.addAll(multiSameAmpList.get());
            dmFocAnnualAmpResultList.addAll(multiReplaceAmpList.get());
        } catch (Exception ex) {
            log.error(">>>>>An exception occurs with AsyncQueryService::findMultiStandDataList");
            log.error(ex.getMessage());
        }
    }

    public void findCurrentStandDataList(List<DmFocAnnualAmpVO> annualAmpVOList, StandardAnalysisVO standardAnalysisVO) {
        log.info(">>>Begin AsyncQueryService::findCurrentStandDataList");
        Future<List<DmFocAnnualAmpVO>> stdAmpList = findStdAmpVOList(standardAnalysisVO);
        Future<List<DmFocAnnualAmpVO>> sameAmpList = findSameAmpVOList(standardAnalysisVO);
        Future<List<DmFocAnnualAmpVO>> replaceAmpList = findReplaceAmpVOList(standardAnalysisVO);
        while (true) {
            if (stdAmpList.isDone() && sameAmpList.isDone() && replaceAmpList.isDone()) {
                break;
            }
        }
        try {
            annualAmpVOList.addAll(stdAmpList.get());
            annualAmpVOList.addAll(sameAmpList.get());
            annualAmpVOList.addAll(replaceAmpList.get());
        } catch (Exception ex) {
            log.error(">>>>>An exception occurs with AsyncQueryService::findCurrentStandDataList");
            log.error(ex.getMessage());
        }
    }

    @Async("asyncServiceExecutor")
    public Future<List<DmFocAnnualAmpVO>> findMultiStdAmpList(StandardAnalysisVO standardAnalysisVO) {
        log.info(">>>Begin AsyncQueryService::findMultiStdAmpList");
        List<DmFocAnnualAmpVO> stdAmpList = iStandardDao.multiStdAmpCostChart(standardAnalysisVO);
        return new AsyncResult<>(stdAmpList);
    }

    @Async("asyncServiceExecutor")
    public Future<List<DmFocAnnualAmpVO>> findMultiSameAmpList(StandardAnalysisVO standardAnalysisVO) {
        log.info(">>>Begin AsyncQueryService::findMultiSameAmpList");
        List<DmFocAnnualAmpVO> sameAmpList = iStandardDao.multiSameAmpCostChart(standardAnalysisVO);
        return new AsyncResult<>(sameAmpList);
    }

    @Async("asyncServiceExecutor")
    public Future<List<DmFocAnnualAmpVO>> findMultiReplaceAmpList(StandardAnalysisVO standardAnalysisVO) {
        log.info(">>>Begin AsyncQueryService::findMultiReplaceAmpList");
        List<DmFocAnnualAmpVO> replaceAmpList = iStandardDao.multiReplaceAmpCostChart(standardAnalysisVO);
        return new AsyncResult<>(replaceAmpList);
    }

    @Async("asyncServiceExecutor")
    public Future<List<DmFocAnnualAmpVO>> findStdAmpVOList(StandardAnalysisVO standardAnalysisVO) {
        log.info(">>>Begin AsyncQueryService::findStdAmpVOList");
        List<DmFocAnnualAmpVO> stdAmpList = iStandardDao.findCurrentStdAmpCost(standardAnalysisVO);
        return new AsyncResult<>(stdAmpList);
    }

    @Async("asyncServiceExecutor")
    public Future<List<DmFocAnnualAmpVO>> findSameAmpVOList(StandardAnalysisVO standardAnalysisVO) {
        log.info(">>>Begin AsyncQueryService::findSameAmpVOList");
        List<DmFocAnnualAmpVO> stdAmpList = iStandardDao.findCurrentSameAmpCost(standardAnalysisVO);
        return new AsyncResult<>(stdAmpList);
    }


    @Async("asyncServiceExecutor")
    public Future<List<DmFocAnnualAmpVO>> findReplaceAmpVOList(StandardAnalysisVO standardAnalysisVO) {
        log.info(">>>Begin AsyncQueryService::findSameAmpVOList");
        List<DmFocAnnualAmpVO> stdAmpList = iStandardDao.findCurrentReplaceAmpCost(standardAnalysisVO);
        return new AsyncResult<>(stdAmpList);
    }

}