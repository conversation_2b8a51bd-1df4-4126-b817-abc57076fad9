/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.enums;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * GroupLevelEnumUTest Class
 *
 * <AUTHOR>
 * @since 2023/4/13
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
public class GroupLevelEnumUTest {

    @Test
    public void getInstance() {
        GroupLevelEnumU levelEnum = GroupLevelEnumU.getInstance("LV1");
        Assert.assertNotNull(levelEnum);
    }

    @Test
    public void getInstanceTest() {
        GroupLevelEnumU levelEnum = GroupLevelEnumU.getInstance("");
        Assert.assertNull(levelEnum);
    }

}