/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.common.CommonViewVO;
import com.huawei.it.fcst.industry.index.vo.common.DmFocViewInfoVO;

import java.util.List;

/**
 * IDmFocViewInfoDao
 *
 * <AUTHOR>
 * @since 2023/3/14
 */
public interface IDmFocDmsViewInfoDao {
    List<DmFocViewInfoVO> viewInfoList(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> viewInfoListForMonth(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> viewInfoListForTopCate(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> viewFlagInfoDmsList(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> viewInfoKeyWordList(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> viewInfoKeyWordForMonth(CommonViewVO commonViewVO);

    List<DmFocViewInfoVO> viewInfoKeyWordForTopCate(CommonViewVO commonViewVO);
}
