/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.impl.dataprocess;

import com.huawei.it.fcst.config.ConfigUtil;
import com.huawei.it.fcst.industry.index.dao.IDataCipherTextDao;
import com.huawei.it.fcst.industry.index.utils.SdkClient;
import com.huawei.it.fcst.industry.index.vo.ciphertext.CipherTextDataVO;
import com.huawei.it.fcst.util.DictConfigUtil;
import com.huawei.it.jalor5.core.request.IRequestContext;
import com.huawei.it.jalor5.core.request.impl.RequestContextManager;
import com.huawei.kmssdk.api.KmsClient;
import com.huawei.kmssdk.entity.DecryptResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;

import javax.inject.Named;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Future;
import java.util.concurrent.Semaphore;
import java.util.stream.Collectors;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023年03月23日
 */
@Slf4j
@Named("taskExecutorProcess")
public class TaskExecutorProcessService {
    @Autowired
    private IDataCipherTextDao iDataCipherTextDao;

    /**
     * 执行kms 解密逻辑，解密数据落库进行gauss加密
     *
     * @param dataEncrypt 参数
     * @param context     参数
     * @return 结果
     */
    @Async("taskExecutor")
    public Future<Object> process(List<CipherTextDataVO> dataEncrypt, IRequestContext context, String keyStr, Date date, Semaphore semaphore, String targetTableName) {
        try {
            long t1 = System.currentTimeMillis();
            RequestContextManager.setCurrent(context);
            final KmsClient kmsClient = SdkClient.INSTANCE.getInstance();
            List<String> rmbCostAmt = dataEncrypt.stream().map(CipherTextDataVO::getRmbCostAmt).collect(Collectors.toList());
            List<String> rmbFactRateGcAmt = dataEncrypt.stream().map(CipherTextDataVO::getRmbFactRateGcAmt).collect(Collectors.toList());
            final List<DecryptResult<String>> rmbCostAmtResults = kmsClient.blukDataDecryption(rmbCostAmt, String.class);
            final List<DecryptResult<String>> rmbFactRateGcAmtResults = kmsClient.blukDataDecryption(rmbFactRateGcAmt, String.class);
            if (dataEncrypt.size() != rmbCostAmtResults.size() || dataEncrypt.size() != rmbFactRateGcAmtResults.size()) {
                log.error("decryption error {}", "The original data collection size is inconsistent with the data collection size after decryption");
                return new AsyncResult("error");
            }
            for (int i = 0; i < dataEncrypt.size(); i++) {
                CipherTextDataVO cipherTextDataVO = dataEncrypt.get(i);
                cipherTextDataVO.setRmbCostAmt(rmbCostAmtResults.get(i).getPlainText());
                cipherTextDataVO.setRmbFactRateGcAmt(rmbFactRateGcAmtResults.get(i).getPlainText());
                cipherTextDataVO.setCreationDate(date);
            }
            long t2 = System.currentTimeMillis();
            log.info("decryption：{}", t2 - t1);
            int num = iDataCipherTextDao.saveData(dataEncrypt, keyStr, targetTableName);
            log.info("insert：{}", System.currentTimeMillis() - t2);

            String clearDataEncryptEnable = DictConfigUtil.getProperty("clear.dataEncrypt.enable", "true");
            if ("true".equals(clearDataEncryptEnable)) {
                dataEncrypt.clear();
            }
            return new AsyncResult<>(num);
        } catch (Exception exception) {
            log.error("kms解密失败:{}", dataEncrypt.stream().map(CipherTextDataVO::getPrimaryId).collect(Collectors.joining("-")));
            log.error("process error {}", exception.getMessage());
            return new AsyncResult("error");
        } finally {
            semaphore.release(1);
        }
    }

    /**
     * 执行kms 解密逻辑，解密数据落库进行gauss加密
     *
     * @param dataEncrypt 参数
     * @return 结果
     */
    @Async("taskExecutor")
    public void process(List<CipherTextDataVO> dataEncrypt, String targetTableName) {
        try {
            long t1 = System.currentTimeMillis();
            log.info("save period_id {}", dataEncrypt.get(0).getPeriodId());
            final KmsClient kmsClient = SdkClient.INSTANCE.getInstance();
            List<String> rmbCostAmt = dataEncrypt.stream().map(CipherTextDataVO::getRmbCostAmt).collect(Collectors.toList());
            List<String> rmbFactRateGcAmt = dataEncrypt.stream().map(CipherTextDataVO::getRmbFactRateGcAmt).collect(Collectors.toList());
            final List<DecryptResult<String>> rmbCostAmtResults = kmsClient.blukDataDecryption(rmbCostAmt, String.class);
            final List<DecryptResult<String>> rmbFactRateGcAmtResults = kmsClient.blukDataDecryption(rmbFactRateGcAmt, String.class);
            if (dataEncrypt.size() != rmbCostAmtResults.size() || dataEncrypt.size() != rmbFactRateGcAmtResults.size()) {
                log.error("decryption error {}", "The original data collection size is inconsistent with the data collection size after decryption");
            }
            Date time = new Date();
            for (int i = 0; i < dataEncrypt.size(); i++) {
                CipherTextDataVO cipherTextDataVO = dataEncrypt.get(i);
                cipherTextDataVO.setRmbCostAmt(rmbCostAmtResults.get(i).getPlainText());
                cipherTextDataVO.setRmbFactRateGcAmt(rmbFactRateGcAmtResults.get(i).getPlainText());
                cipherTextDataVO.setCreationDate(time);
            }
            long t2 = System.currentTimeMillis();
            log.info("decryption：{}", t2 - t1);
            String keyStr = ConfigUtil.getInstance().get16PlainText();
            iDataCipherTextDao.saveData(dataEncrypt, keyStr, targetTableName);
            log.info("insert：{}", System.currentTimeMillis() - t2);
        } catch (Exception exception) {
            log.error("process error {}", exception.getMessage());
            log.error("kms解密失败:{}", dataEncrypt.stream().map(CipherTextDataVO::getPrimaryId).collect(Collectors.joining("-")));
        }
    }
}
