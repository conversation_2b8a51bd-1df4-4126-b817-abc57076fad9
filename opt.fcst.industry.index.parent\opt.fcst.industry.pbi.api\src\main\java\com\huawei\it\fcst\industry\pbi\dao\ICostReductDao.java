/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.dao;

import com.huawei.it.fcst.industry.pbi.vo.config.CostReductVO;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ICodeReplacementDao Class
 *
 * @since 2024-07-04
 */
public interface ICostReductDao {

    List<CostReductVO> findCostReductDropDown(CostReductVO CostReductVO);

    PagedResult<CostReductVO> findCostReductByPage(CostReductVO CostReductVO, PageVO pageVO);

    PagedResult<CostReductVO> findCostReductExpByPage(CostReductVO CostReductVO, PageVO pageVO);

    void batchInsertCostReductVOs(@Param("list") List<CostReductVO> formalDataList, @Param("userId")Long userId);

    int deleteCostReductDataList(@Param("list")List<CostReductVO> unofficialDataList);

    int deleteCostRedDataListByVersionId(Long versionId);

    void updateCostReductDataList(@Param("list")List<CostReductVO> updateDataList, @Param("userId")Long userId);

    int getRecordSize(CostReductVO CostReductVO);

    List<String> getSpecialInfoList(String versionId);

    String getVersionName(CostReductVO costReductVO);

    void copyHisData(@Param("newVersionId")String newVersionId, @Param("oldVersionId")String oldVersionId, @Param("userId") Long userId);

    List<CostReductVO> getGroupLevelInfo(CostReductVO costReductVO);
}
