/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.pbi.impl.common;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.huawei.it.fcst.constant.Constant;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstDataRefreshStatusDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstIrbDimInfoDao;
import com.huawei.it.fcst.industry.pbi.dao.IDmFcstVersionInfoDao;
import com.huawei.it.fcst.industry.pbi.dao.IctMonthCostIdxDao;
import com.huawei.it.fcst.industry.pbi.service.common.IIctCommonService;
import com.huawei.it.fcst.industry.pbi.vo.common.CommonBaseVO;
import com.huawei.it.fcst.industry.pbi.vo.common.DmFcstDataRefreshStatus;
import com.huawei.it.fcst.industry.pbi.vo.version.DmFcstVersionInfoDTO;
import com.huawei.it.fcst.util.UserInfoUtils;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.registry.service.IRegistryQueryService;
import com.huawei.it.jalor5.security.RoleVO;
import com.huawei.it.jalor5.security.UserVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Inject;
import javax.inject.Named;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * IctCommonService Class
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Slf4j
@Named("ictCommonService")
@JalorResource(code = "IctCommonService", desc = "产业成本指数（ICT）公共服务")
public class IctCommonService implements IIctCommonService {

    private static final Pattern PATTERN = Pattern.compile("[0-9]*");

    @Inject
    private IDmFcstDataRefreshStatusDao dataRefreshStatusDao;

    @Inject
    private IDmFcstVersionInfoDao versionInfoDao;

    @Inject
    private IctMonthCostIdxDao monthCostIdxDao;

    @Autowired
    private IDmFcstIrbDimInfoDao dmFcstIrbDimInfoDao;

    @Inject
    private IRegistryQueryService registryQueryService;

    @Override
    public Long getVersionId(String dataType) {
        log.info("Begin IctCommonService::getVersionId and dataType={}", dataType);
        DmFcstVersionInfoDTO dmFocVersionVO = versionInfoDao.findVersionIdByDataType(dataType);
        dmFocVersionVO = Optional.ofNullable(dmFocVersionVO).orElse(new DmFcstVersionInfoDTO());
        return dmFocVersionVO.getVersionId();
    }

    @Override
    public Long createNewVersionInfo(String dataType) {
        DmFcstVersionInfoDTO versionInfoDTO = DmFcstVersionInfoDTO.builder()
                .status(0L)
                .isRunning("N")
                .versionType("ADJUST")
                .dataType(dataType)
                .versionId(versionInfoDao.getVersionKey())
                .version(getVerionName(dataType))
                .build();
        versionInfoDTO.setCreatedBy(UserInfoUtils.getUserId());
        versionInfoDTO.setLastUpdatedBy(UserInfoUtils.getUserId());
        versionInfoDao.createDmFcstVersionInfoDTO(versionInfoDTO);
        return versionInfoDTO.getVersionId();
    }

    @Override
    public DmFcstDataRefreshStatus saveDataRefreshStatus(String taskFlag) {
        DmFcstDataRefreshStatus dataRefreshStatus = new DmFcstDataRefreshStatus();
        dataRefreshStatus.setTaskId(dataRefreshStatusDao.getDataRefrashKey());
        dataRefreshStatus.setStatus(Constant.TaskStatus.INIT.getValue());
        dataRefreshStatus.setDelFlag("N");
        dataRefreshStatus.setTaskFlag(taskFlag);
        dataRefreshStatus.setCreatedBy(UserInfoUtils.getUserId());
        dataRefreshStatus.setLastUpdatedBy(UserInfoUtils.getUserId());
        dataRefreshStatus.setUserId(UserInfoUtils.getUserId());
        dataRefreshStatus.setCreationDate(new Date());
        dataRefreshStatus.setLastUpdateDate(new Date());
        dataRefreshStatusDao.createDmFcstDataRefreshStatus(dataRefreshStatus);
        return dataRefreshStatus;
    }

    /**
     * 根据数据类型升序生成版本名称
     *
     * @param dataType 数据类型
     * @return String version name
     */
    private String getVerionName(String dataType) {
        // 查询今天的版本信息
        String dateStr = DateUtil.today().replace("-", "");
        DmFcstVersionInfoDTO build = DmFcstVersionInfoDTO.builder()
                .dataType(dataType).version(dateStr).build();
        List<DmFcstVersionInfoDTO> versionList = versionInfoDao.findVersionListByVerName(build);
        if (CollectionUtils.isEmpty(versionList) || StringUtils.isBlank(versionList.get(0).getVersion())) {
            return dateStr.concat("-001");
        }
        return getVersionNameSub(versionList.get(0).getVersion(), dateStr);
    }

    /**
     * 根据传入版本名称升序生成新的版本名称
     *
     * @param inputVersionName 版本名称 形如 20240712-001
     * @param dateStr          当天日期字符串 yyyyMMdd
     * @return String version name
     */
    private String getVersionNameSub(String inputVersionName, String dateStr) {
        if (inputVersionName.contains("-")) {
            String versionNumber = inputVersionName.substring(inputVersionName.indexOf("-") + 1);
            if (PATTERN.matcher(versionNumber).matches()) {
                if (NumberUtil.parseInt(versionNumber) < 9) {
                    return dateStr.concat("-00") + (NumberUtil.parseInt(versionNumber) + 1);
                } else if (NumberUtil.parseInt(versionNumber) < 99) {
                    return dateStr.concat("-0") + (NumberUtil.parseInt(versionNumber) + 1);
                } else {
                    return dateStr.concat("-") + (NumberUtil.parseInt(versionNumber) + 1);
                }
            } else {
                return dateStr.concat("-001");
            }
        }
        return dateStr.concat("-001");
    }

    @Override
    @JalorOperation(code = "findActualPeriodId", desc = "查询切换基期实际数的开始和结束时间")
    public ResultDataVO findActualPeriodId() {
        log.info("==>Begin IctCommonService#findActualPeriodId");
        return ResultDataVO.success(monthCostIdxDao.findStartEndTime(getVersionId("MONTH")));
    }

    @Override
    @JalorOperation(code = "findRefreshTime", desc = "查询系统刷新时间")
    public ResultDataVO findRefreshTime() {
        log.info("==>Begin IctCommonService#findRefreshTime");
        return ResultDataVO.success(dmFcstIrbDimInfoDao.findRefreshTime());
    }

    @Override
    @JalorOperation(code = "currentDataRefreshStatus", desc = "当前用户当前角色的taskId查询")
    public ResultDataVO currentDataRefreshStatus(DmFcstDataRefreshStatus dataRefreshStatus) {
        Long userId = UserInfoUtils.getUserId();
        UserVO currentUser = UserInfoUtils.getCurrentUser();
        RoleVO currentRole = currentUser.getCurrentRole();
        Integer roleId = currentRole.getRoleId();
        dataRefreshStatus.setUserId(userId);
        dataRefreshStatus.setRoleId(roleId);
        DmFcstDataRefreshStatus dmFocDataRefreshStatus =
                dataRefreshStatusDao.findDmFcstDataRefreshStatus(dataRefreshStatus);
        return ResultDataVO.success(dmFocDataRefreshStatus);
    }

    public void setExcelDisplayName(CommonBaseVO commonBaseVO) {
        String displayName = "ICT产业";
        // 名称拼接
        String l1CnName = getConnectName(commonBaseVO.getLv1ProdRdTeamCnNameList(), displayName, "L1");
        String l2CnName = getConnectName(commonBaseVO.getLv2ProdRdTeamCnNameList(), l1CnName, "L2");
        String l3CnName = getConnectName(commonBaseVO.getLv3ProdRdTeamCnNameList(), l2CnName, "L3");
        String connectName = getConnectName(commonBaseVO.getLv4ProdRdTeamCnNameList(), l3CnName, "L3.5");
        if ("PROD_SPART".equals(commonBaseVO.getViewFlag())) {
            connectName = getConnectName(commonBaseVO.getSpartCodeList(), connectName, "SPART");
        }
        if ("DIMENSION".equals(commonBaseVO.getViewFlag())) {
            if (StringUtils.isNotEmpty(commonBaseVO.getDimensionCnName())) {
                connectName = connectName + "-" + commonBaseVO.getDimensionCnName();
            }
            if (StringUtils.isNotEmpty(commonBaseVO.getDimensionSubCategoryCnName())) {
                connectName = connectName + "-" + commonBaseVO.getDimensionSubCategoryCnName();
            }
            if (StringUtils.isNotEmpty(commonBaseVO.getDimensionSubDetailCnName())) {
                connectName = connectName + "-" + commonBaseVO.getDimensionSubDetailCnName();
            }
        }
        commonBaseVO.setDisplayName(connectName);
    }

    private String getConnectName(List<String> prodRdTeamCnNameList, String displayName, String groupLevel) {
        if (CollectionUtils.isNotEmpty(prodRdTeamCnNameList)) {
            if (prodRdTeamCnNameList.size() == 1) {
                displayName = displayName + "-" + prodRdTeamCnNameList.get(0);
            } else {
                String prodRdTeamCnNameStr = prodRdTeamCnNameList.stream().collect(Collectors.joining(","));
                displayName = displayName + "-多" + groupLevel + "(" + prodRdTeamCnNameStr + ")";
            }
        }
        return displayName;
    }

    @Override
    public Map<String, Object> getHeaderMap(String moduleType) throws ApplicationException {
        Map<String, Object> headerMap = new HashMap<>();
        headerMap.put("module", moduleType);
        // 获取Excel导入的最大条数
        int maxRowNum = NumberUtil.parseInt(registryQueryService.findValueByPath("Jalor.Excel.ExcelImportMaxCount", true));
        headerMap.put("maxRowNum", maxRowNum);
        return headerMap;
    }
}