/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.common;

/**
 * Constants Class
 *
 * <AUTHOR>
 * @since 2023/3/15
 */
public enum Constants {
    /** The LIST. */
    LIST("list"),
    
    /** The BEFOREPROXYCLASS. */
    BEFOREPROXYCLASS("beforeProxyClass"),
    
    /** The AFTERPROXYCLASS. */
    AFTERPROXYCLASS("afterProxyClass"),
    
    /** The BOUNDSQL. */
    BOUNDSQL("boundSql"),
    
    /** The SHOWSQL. */
    SHOWSQL("showSql"),
    
    /** The CODEASPECT. */
    CODEASPECT("codeAspect"),
    
    /** The PATH. */
    PATH("path"),
    
    /** The FILE. */
    FILE("file"),
    
    /** The METHOD. */
    METHOD("method"),
    
    /** The CACHEFLAG. */
    CACHEFLAG("cacheFlag"),
    
    /** The EXPFLAG. */
    EXPFLAG("expFlag"),
    
    /** The DATEFORMAT_SS. */
    DATEFORMAT_SS("yyyy-MM-dd HH:mm:ss"),

    /** The DATEFORMAT_DD. */
    DATEFORMAT_DD("yyyy-MM-dd"),

    /** The DATEFORMAT. */
    DATEFORMAT("yyyyMMddHHmmss"),

    /** The SUCCESS. */
    SUCCESS("SUCCESS"),

    /** The RUNNING. */
    RUNNING("RUNNING"),

    /** The FAIL. */
    FAIL("FAIL"),

    /** The HTMLSPLIT. */
    HTMLSPLIT("&"),
    
    /** The SNULL. */
    SNULL("SNULL"),

    /** The DATEFORMATDD. */
    DATEFORMATDD("yyyyMMdd"),

    /** The DATEFORMATMM. */
    DATEFORMATMM("yyyyMM"),

    /** The FH. */
    FH(";"),

    /** The DH. */
    DH(","),

    /** The YH. */
    YH(":"),

    /** The AI. */
    AI("AI"),

    /** The TRUE. */
    TRUE("true"),

    /** The FALSE. */
    FALSE("false"),

    /** The CONTENT_TYPE. */
    CONTENT_TYPE("Content-Type"),

    /** The AUTH. */
    AUTH("Authorization"),

    /** The HTML_BYTE. */
    HTML_BYTE("text/html;charset=UTF-8"),

    /** The JSON_BYTE. */
    JSON_BYTE("application/json;charset=UTF-8"),

    /** The XLSX. */
    XLSX(".xlsx"),

    /** The _EXP. */
    EXP("_EXP"),

    /** The XLS. */
    XLS(".xls"),

    /** The XML. */
    XML(".xml"),

    /** The DEFAULT. */
    DEFAULT(""),

    /** The ISNULL. */
    ISNULL("ISNULL"),

    /** The STATUS. */
    STATUS("status"),

    /** The MESSAGE. */
    MESSAGE("message"),

    /** The ERROR. */
    ERROR("ERROR"),

    /** The DATE. */
    DATE("DATE"),

    /** The PERCENTAGE. */
    PERCENTAGE("PERCENTAGE"),

    /** The NUMBER. */
    NUMBER("NUMBER"),

    /** The ROWNUMBER. */
    ROWNUMBER("rowNumber"),

    /** The ROLEID. */
    ROLEID("roleId"),

    /** The VARCHAR. */
    VARCHAR("VARCHAR"),

    /** The _. */
    XHX("_"),

    /** The HX. */
    HX("-"),

    /** The XX. */
    XX("*"),

    /** The Y. */
    YES("Y"),

    /** The N. */
    NO("N"),

    /** The USERID. */
    USERID("USERID"),

    /** The USERID. */
    REQUEST_PARAMS("requestParams"),

    /** The USERID. */
    READ_EXCEPTION("read exception"),

    /** The START_INDEX. */
    START_INDEX("startIndex"),

    /** The END_INDEX. */
    END_INDEX("endIndex"),

    /** The PAGE_INDEX. */
    PAGE_INDEX("pageIndex"),

    /** The RESULT. */
    RESULT("result"),

    /** The PARAMS. */
    PARAMS("params"),

    /** The CUR_PAGE. */
    CUR_PAGE("curPage"),

    /** The PAGEINDEX. */
    PAGEINDEX("pageIndex"),

    /** The TOTAL. */
    TOTAL("total"),

    /** The SQL. */
    SQL("sql"),

    /** The PAGE_SIZE. */
    PAGE_SIZE("pageSize"),

    /** The ONE. */
    ONE("1"),

    /** The TWO. */
    TWO("2"),
    /** The sign. */
    SING("%"),

    /** The TYPE. */
    TYPE("type");

    private String value;

    Constants(
        String value) {
        this.value = value;
    }

    /**
     * [服务名称]getValue
     * <AUTHOR>
     *
     * @return String
     */
    public String getValue() {
        return value;
    }

    /**
     * [服务名称]setValue
     * <AUTHOR>
     *
     * @param value void
     */
    public void setValue(String value) {
        this.value = value;
    }
}
