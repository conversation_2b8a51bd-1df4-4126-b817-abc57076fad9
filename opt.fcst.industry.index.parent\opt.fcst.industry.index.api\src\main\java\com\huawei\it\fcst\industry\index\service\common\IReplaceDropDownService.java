/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.service.common;

import com.huawei.it.fcst.industry.index.vo.common.CommonDropDownVO;
import com.huawei.it.fcst.industry.index.vo.common.ResultDataVO;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import io.swagger.annotations.Api;

import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * ICommonService Interface
 *
 * <AUTHOR>
 * @since 2024/09/2
 */
@Path("/dropDown")
@Api(value = "公共服务接口")
@Produces(MediaType.APPLICATION_JSON)
public interface IReplaceDropDownService {
    /**
     * 获取会计期年份下拉框
     *
     * @return String
     */
    @GET
    @Path("/replace/periodYear/list")
    ResultDataVO getAnnualPeriodYear();

    /**
     * 不同层级的code下拉框
     *
     * @param commonViewVO 参数
     * @return ResultDataVO ResultDataVO
     */
    @Path("/replace/list")
    @POST
    ResultDataVO dropDownList(CommonDropDownVO commonViewVO) throws ApplicationException;

    /**
     * 视角下拉框
     *
     * @return ResultDataVO
     */
    @Path("/replace/viewFlagInfo/list")
    @POST
    ResultDataVO viewFlagInfoList(CommonDropDownVO commonViewVO) throws ApplicationException;

}