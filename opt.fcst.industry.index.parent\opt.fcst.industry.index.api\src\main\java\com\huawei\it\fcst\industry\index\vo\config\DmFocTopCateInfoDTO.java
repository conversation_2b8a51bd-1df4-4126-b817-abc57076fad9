/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.industry.index.vo.config;

import com.huawei.it.fcst.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * DmFocTopCateInfoDTO Class
 *
 * <AUTHOR>
 * @since 2023/3/14
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "TOP品类清单(月度分析) 实体类映射")
public class DmFocTopCateInfoDTO extends BaseVO {
    /**
     * 会计年(区间值, 例如:2022-2023, 2021，2022，2023)
     **/
    @ApiModelProperty("period_year")
    private String periodYear;

    /**
     * 重量级团队LV0编码
     **/
    @ApiModelProperty("lv0_prod_rnd_team_code")
    private String lv0ProdRndTeamCode;

    /**
     * 重量级团队LV0中文名称
     **/
    @ApiModelProperty("lv0_prod_rd_team_cn_name")
    private String lv0ProdRdTeamCnName;

    /**
     * 重量级团队LV3编码
     **/
    @ApiModelProperty("lv3_prod_rnd_team_code")
    private String lv3ProdRndTeamCode;

    /**
     * 重量级团队LV3中文名称
     **/
    @ApiModelProperty("lv3_prod_rd_team_cn_name")
    private String lv3ProdRdTeamCnName;

    /**
     * 重量级团队LV4编码
     **/
    @ApiModelProperty("lv4_prod_rnd_team_code")
    private String lv4ProdRndTeamCode;

    /**
     * 重量级团队LV3中文名称
     **/
    @ApiModelProperty("lv4_prod_rd_team_cn_name")
    private String lv4ProdRdTeamCnName;

    /**
     * 重量级团队LV1中文名称
     **/
    @ApiModelProperty("lv1_prod_rd_team_cn_name")
    private String lv1ProdRdTeamCnName;

    /**
     * 重量级团队LV1编码
     **/
    @ApiModelProperty("lv1_prod_rnd_team_code")
    private String lv1ProdRndTeamCode;

    /**
     * 重量级团队LV2中文名称
     **/
    @ApiModelProperty("lv2_prod_rd_team_cn_name")
    private String lv2ProdRdTeamCnName;

    /**
     * 重量级团队LV2编码
     **/
    @ApiModelProperty("lv2_prod_rnd_team_code")
    private String lv2ProdRndTeamCode;

    /**
     * 品类名称
     **/
    @ApiModelProperty("top_category_cn_name")
    private String topCategoryCnName;

    /**
     * 品类编码
     **/
    @ApiModelProperty("top_category_code")
    private String topCategoryCode;

    /**
     * 专家团编码
     **/
    @ApiModelProperty("top_l3_ceg_code")
    private String topL3CegCode;

    /**
     * 专家团中文名称
     **/
    @ApiModelProperty("top_l3_ceg_cn_name")
    private String topL3CegCnName;

    /**
     * 专家团（Group LV3简称）
     **/
    @ApiModelProperty("top_l3_ceg_short_cn_name")
    private String topL3CegShortCnName;

    /**
     * 模块编码
     **/
    @ApiModelProperty("top_l4_ceg_code")
    private String topL4CegCode;

    /**
     * 模块中文名称
     **/
    @ApiModelProperty("top_l4_ceg_cn_name")
    private String topL4CegCnName;

    /**
     * 模块（Group LV4简称）
     **/
    @ApiModelProperty("top_l4_ceg_short_cn_name")
    private String topL4CegShortCnName;

    /**
     * 主键
     **/
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 版本id
     **/
    @ApiModelProperty("version_id")
    private Long versionId;

    /**
     * 版本名称
     **/
    @ApiModelProperty("version_name")
    private String versionName;

    /**
     * 排序ID，用于不同视角下的TOP品类排序
     **/
    @ApiModelProperty("view_sort_id")
    private String viewSortId;

    /**
     * 视角标识，用于区分不同视角下的品类数据(0: LV0层级, 1:LV0-LV1层级, 2:LV0-LV1-LV2层级)
     **/
    @ApiModelProperty("view_flag")
    private String viewFlag;

    /**
     * 父级CODE
     **/
    @ApiModelProperty("parent_code")
    private String parentCode;


    /**
     * top数量
     **/
    @ApiModelProperty(value = "top数量")
    private Long topNums;

    /**
     * 盈利颗粒度L1
     **/
    @ApiModelProperty("l1_name")
    private String l1Name;

    /**
     * 盈利颗粒度L2
     **/
    @ApiModelProperty("l2_name")
    private String l2Name;

    @ApiModelProperty(value = "维度类型（U：通用/ P盈利颗粒度/ D量纲颗粒度）")
    private String granularityType;

    @ApiModelProperty(value = "业务口径（R:收入时点/C：发货成本）")
    private String caliberFlag;

    /**
     *
     *  报告期集合
     */
    List<String> yearPeriodList;

    private String periodYear0;
    private String periodYear1;
    private String periodYear2;
    private String periodYear3;
    private String periodYear4;

    /**
     *  量纲编码
     */
    private String dimensionCode;

    /**
     *  量纲中文名称
     */
    private String dimensionCnName;

    /**
     *  量纲子类编码
     */
    private String dimensionSubCategoryCode;

    /**
     *  量纲子类中文名称
     */
    private String dimensionSubCategoryCnName;

    /**
     *  量纲子类明细编码
     */
    private String dimensionSubDetailCode;

    /**
     *  量纲子类明细中文名称
     */
    private String dimensionSubDetailCnName;

    /**
     *  spart中文名称
     */
    private String spartCnName;

    /**
     *  spart层级编码
     */
    private String spartCode;

    /**
     *  coa层级编码
     */
    private String coaCode;

    /**
     *  coa中文名称
     */
    private String coaCnName;

    /**
     * BG编码
     **/
    @ApiModelProperty("lv0_prod_list_code")
    private String lv0ProdListCode;

    /**
     * 是否为TOP类, Y:表示是, N:表示否
     **/
    private String topFlag;

    /**
     * 国内海外标识(I:国内/O:海外/G:全球)
     **/
    @ApiModelProperty("oversea_flag")
    private String overseaFlag;

    /**
     * 发货对象编码
     **/
    @ApiModelProperty("top_shipping_object_code")
    private String topShippingObjectCode;

    /**
     * 制造对象编码
     **/
    @ApiModelProperty("top_manufacture_object_code")
    private String topManufactureObjectCode;

    /**
     * 发货对象名称
     **/
    @ApiModelProperty("top_shipping_object_cn_name")
    private String topShippingObjectCnName;

    /**
     * 制造对象名称
     **/
    @ApiModelProperty("top_manufacture_object_cn_name")
    private String topManufactureObjectCnName;

    private String weight0;

    private String weight1;

    private String weight2;

    private String weight3;

    private String weight4;

    private String errorMessage;

    private String industryOrg;

    private String tablePreFix;

    private Integer curPage;

    private Integer pageSize;

}
