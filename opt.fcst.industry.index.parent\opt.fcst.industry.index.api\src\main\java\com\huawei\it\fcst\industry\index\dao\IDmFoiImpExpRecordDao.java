/*
 *
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 *
 */

package com.huawei.it.fcst.industry.index.dao;

import com.huawei.it.fcst.industry.index.vo.common.DmFoiImpExpRecordVO;

/**
 * IDmFoiImpExpRecordDao
 *
 * <AUTHOR>
 * @since 2022/10/13
 */
public interface IDmFoiImpExpRecordDao {
    void insertStatisticsImportRecord(DmFoiImpExpRecordVO dmFoiImpExpRecordVO);

    void insertStatisticsExportRecord(DmFoiImpExpRecordVO dmFoiImpExpRecordVO);

}
