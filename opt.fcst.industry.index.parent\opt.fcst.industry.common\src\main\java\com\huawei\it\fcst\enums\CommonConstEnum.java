/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.it.fcst.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2022/6/7
 */
public interface CommonConstEnum {

    enum STATUS_FLAG {
        D("D", "待计算"),
        Y("Y", "已计算"),
        N("N", "失败"),
        ING("ING", "计算中");
        @Getter
        @Setter
        private String value;
        @Getter
        @Setter
        private String desc;
        STATUS_FLAG(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    enum ModifyType {
        INSERT("INSERT","新增"),
        MODIFY("MODIFY","修改"),
        REVOKE("REVOKE","撤销");
        @Getter
        @Setter
        private String value;
        @Getter
        @Setter
        private String desc;
        ModifyType(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    enum OVERSEA_FLAG {
        G("G", "全球"),
        N("N", "国内"),
        Y("Y", "海外");
        @Getter
        @Setter
        private String value;
        @Getter
        @Setter
        private String desc;
        OVERSEA_FLAG(String value,String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    static ModifyType getModifyType(String key){
        for (ModifyType modifyType : ModifyType.values()) {
            if (modifyType.getValue().equalsIgnoreCase(key)) {
                return modifyType;
            }
        }
        return null;
    }

    static String getModifyTypeValue(String key){
        for (ModifyType modifyType : ModifyType.values()) {
            if (modifyType.getDesc().equalsIgnoreCase(key)) {
                return modifyType.getValue();
            }
        }
        return null;
    }

    static OVERSEA_FLAG getOverSeaFlag(String key){
        for (OVERSEA_FLAG overseaFlag : OVERSEA_FLAG.values()) {
            if (overseaFlag.getValue().equalsIgnoreCase(key)) {
                return overseaFlag;
            }
        }
        return null;
    }
}
