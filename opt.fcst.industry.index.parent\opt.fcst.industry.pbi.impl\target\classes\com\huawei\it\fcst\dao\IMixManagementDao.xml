<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.pbi.dao.IMixManagementDao">
    <resultMap type="com.huawei.it.fcst.industry.pbi.vo.mix.DmFocMixResultVO" id="resultMap">
        <result property="periodYear" column="period_year"/>
        <result property="periodId" column="period_id"/>
        <result property="prodRndTeamCode" column="prod_rnd_team_code"/>
        <result property="prodRndTeamCnName" column="prod_rnd_team_cn_name"/>
        <result property="parentLevel" column="parent_level"/>
        <result property="parentCode" column="parent_code"/>
        <result property="parentCnName" column="parent_cn_name"/>
        <result property="groupCode" column="group_code"/>
        <result property="groupCnName" column="group_cn_name"/>
        <result property="groupLevel" column="group_level"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDdate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="dimensionCode" column="dimension_code"/>
        <result property="dimensionCnName" column="dimension_cn_name"/>
        <result property="dimensionSubCategoryCode" column="dimension_subcategory_code"/>
        <result property="dimensionSubCategoryCnName" column="dimension_subcategory_cn_name"/>
        <result property="dimensionSubDetailCode" column="dimension_sub_detail_code"/>
        <result property="dimensionSubDetailCnName" column="dimension_sub_detail_cn_name"/>
        <result property="rmbCostAmt" column="rmb_cost_amt"/>
        <result property="costIndex" column="cost_index"/>
        <result property="costType" column="costType"/>
        <result property="weightRate" column="weight_rate"/>
        <result property="rmbCostAmt" column="rmb_cost_amt"/>
        <result property="pspRmbCostAmt" column="psp_rmb_cost_amt"/>
        <result property="stdRmbCostAmt" column="std_rmb_cost_amt"/>
        <result property="gapPspStd" column="gap_psp_std"/>
        <result property="ratioPspStd" column="ratio_psp_std"/>
    </resultMap>

    <select id="distributeChartMultiType" resultMap="resultMap">
        select
        period_id,dimension_code,dimension_cn_name,
        dimension_subcategory_code,dimension_subcategory_cn_name,
        dimension_sub_detail_code,dimension_sub_detail_cn_name,
        group_level,group_code,group_cn_name,parent_code,parent_cn_name,
        sum(rmb_cost_amt) /10000 as rmb_cost_amt,
        <choose>
            <when test='granularityType == "IRB"'>
                prod_rd_team_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "INDUS"'>
                industry_catg_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "PROD"'>
                prod_list_cn_name as prod_rnd_team_cn_name
            </when>
            <otherwise>
            </otherwise>
        </choose>
        from fin_dm_opt_foi.DM_FCST_ICT_${tablePreFix}_MON_COST_AMT_T
        where del_flag = 'N'
        <if test='overseaFlag != null and overseaFlag != ""'>
            and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag != null and mainFlag != ""'>
            and main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes != null and codeAttributes != ""'>
            and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='groupCode != null and groupCode != ""'>
            and group_code = #{groupCode,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='dimensionCode != null and dimensionCode != ""'>
            and dimension_code = #{dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubCategoryCode != null and dimensionSubCategoryCode != ""'>
            and dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubDetailCode != null and dimensionSubDetailCode != ""'>
            and dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <choose>
            <when test='granularityType == "IRB"'>
                <if test='prodRndTeamCodeList != null and prodRndTeamCodeList.size() > 0'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND prod_rnd_team_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
            </when>
            <when test='granularityType == "INDUS"'>
                <if test='prodRndTeamCodeList != null and prodRndTeamCodeList.size() > 0'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND industry_catg_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
            </when>
            <when test='granularityType == "PROD"'>
                <if test='prodRndTeamCodeList != null and prodRndTeamCodeList.size() > 0'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND prod_list_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </if>
            </when>
            <otherwise>
            </otherwise>
        </choose>
        group by period_id,dimension_code,dimension_cn_name,
        dimension_subcategory_code,dimension_subcategory_cn_name,
        dimension_sub_detail_code,dimension_sub_detail_cn_name,
        group_level,group_code,group_cn_name,parent_code,parent_cn_name,
        prod_rnd_team_cn_name
        order by period_id
    </select>

    <select id="distributeCustomChartMultiType" resultMap="resultMap">
        select
        period_id,group_level,group_code,group_cn_name,parent_code,parent_cn_name,parent_level,
        sum(rmb_cost_amt) /10000 as rmb_cost_amt
        from fin_dm_opt_foi.DM_FCST_ICT_${costType}_BASE_CUS_MON_COST_AMT_T
        where del_flag = 'N'
        <if test='costType=="PSP" and pspCustomId != null'>
            AND custom_id = #{pspCustomId}
        </if>
        <if test='costType =="STD" and stdCustomId != null'>
            AND custom_id = #{stdCustomId}
        </if>
        <if test='granularityType != null and granularityType != ""'>
            AND GRANULARITY_TYPE = #{granularityType}
        </if>
        <if test='overseaFlag != null and overseaFlag != ""'>
            and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='groupCode != null and groupCode != ""'>
            and group_code = #{groupCode,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag != null and mainFlag != ""'>
            and main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes != null and codeAttributes != ""'>
            and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        group by
        period_id,group_level,group_code,group_cn_name,parent_code,parent_cn_name,parent_level
        order by period_id
    </select>

    <select id="gapCostMultiType" resultMap="resultMap">
        select
        period_id,dimension_code,dimension_cn_name,
        dimension_subcategory_code,dimension_subcategory_cn_name,
        dimension_sub_detail_code,dimension_sub_detail_cn_name,
        group_level,group_code,group_cn_name,parent_code,parent_cn_name,
        ROUND(sum(gap_psp_std)/10000, 2) as gap_psp_std, ROUND(sum(ratio_psp_std)*100, 2) as ratio_psp_std,
        <choose>
            <when test='granularityType == "IRB"'>
                prod_rd_team_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "INDUS"'>
                industry_catg_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "PROD"'>
                prod_list_cn_name as prod_rnd_team_cn_name
            </when>
            <otherwise>
            </otherwise>
        </choose>
        from fin_dm_opt_foi.DM_FCST_ICT_${granularityType}_COST_GAP_DETAIL_T
        where del_flag = 'N'
        <if test='overseaFlag != null and overseaFlag != ""'>
            and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag != null and mainFlag != ""'>
            and main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes != null and codeAttributes != ""'>
            and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='groupCode != null and groupCode != ""'>
            and group_code = #{groupCode,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='dimensionCode != null and dimensionCode != ""'>
            and dimension_code = #{dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubCategoryCode != null and dimensionSubCategoryCode != ""'>
            and dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubDetailCode != null and dimensionSubDetailCode != ""'>
            and dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='pspRate != null'>
            AND ratio_psp_std <![CDATA[ > ]]> #{pspRate}
        </if>
        <if test='prodRndTeamCodeList != null and prodRndTeamCodeList.size() > 0'>
            <foreach collection='prodRndTeamCodeList' item="code" open="AND prod_rnd_team_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        group by period_id,dimension_code,dimension_cn_name,
        dimension_subcategory_code,dimension_subcategory_cn_name,
        dimension_sub_detail_code,dimension_sub_detail_cn_name,
        group_level,group_code,group_cn_name,parent_code,parent_cn_name,
        prod_rnd_team_cn_name
        order by period_id
    </select>

    <select id="findCustomPriceIndexMultiType" resultMap="resultMap">
        select
        group_level,group_code,group_cn_name,parent_cn_name,parent_code,
        period_id,ROUND(cost_index, 2) as cost_index
        from fin_dm_opt_foi.DM_FCST_ICT_${costType}_BASE_CUS_MON_COST_IDX_T
        where del_flag = 'N'
        <if test='costType=="PSP" and pspCustomId != null'>
            AND custom_id = #{pspCustomId}
        </if>
        <if test='costType =="STD" and stdCustomId != null'>
            AND custom_id = #{stdCustomId}
        </if>
        <if test='granularityType != null and granularityType != ""'>
            and granularity_type = #{granularityType,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag != null and overseaFlag != ""'>
            and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag != null and mainFlag != ""'>
            and main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes != null and codeAttributes != ""'>
            and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='groupCode != null and groupCode != ""'>
            and group_code = #{groupCode,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='parentCode != null and parentCode != ""'>
            and parent_code = #{parentCode,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        order by group_cn_name,parent_code,period_id
    </select>

    <select id="findPriceIndexMultiType" resultMap="resultMap">
        select
        dimension_code,dimension_cn_name,
        dimension_subcategory_code,dimension_subcategory_cn_name,
        dimension_sub_detail_code,dimension_sub_detail_cn_name,
        group_level,group_code,group_cn_name,parent_cn_name,parent_code,
        period_id, ROUND(cost_index, 2) as cost_index,
        <choose>
            <when test='granularityType == "IRB"'>
                prod_rd_team_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "INDUS"'>
                industry_catg_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "PROD"'>
                prod_list_cn_name as prod_rnd_team_cn_name
            </when>
            <otherwise>
            </otherwise>
        </choose>
        from fin_dm_opt_foi.DM_FCST_ICT_${costType}_${granularityType}_MON_COST_IDX_T
        where del_flag = 'N'
        <if test='basePeriodId != null'>
            and base_period_id = #{basePeriodId}
        </if>
        <if test='overseaFlag != null and overseaFlag != ""'>
            and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag != null and mainFlag != ""'>
            and main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes != null and codeAttributes != ""'>
            and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='groupCode != null and groupCode != ""'>
            and group_code = #{groupCode,jdbcType=VARCHAR}
        </if>
        <if test='parentCode != null and parentCode != ""'>
            and parent_code = #{parentCode,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='dimensionCode != null and dimensionCode != ""'>
            and dimension_code = #{dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubCategoryCode != null and dimensionSubCategoryCode != ""'>
            and dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubDetailCode != null and dimensionSubDetailCode != ""'>
            and dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='prodRndTeamCodeList != null and prodRndTeamCodeList.size() > 0'>
            <choose>
                <when test='granularityType == "IRB"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND prod_rnd_team_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='granularityType == "INDUS"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND industry_catg_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='granularityType == "PROD"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND prod_list_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        order by group_cn_name,period_id
    </select>

    <select id="findPriceIndexMinLevelMultiType" resultMap="resultMap">
        select
        dimension_code,dimension_cn_name,
        dimension_subcategory_code,dimension_subcategory_cn_name,
        dimension_sub_detail_code,dimension_sub_detail_cn_name,
        group_level,group_code,group_cn_name,parent_cn_name,parent_code,
        period_id, ROUND(cost_index, 2) as cost_index,
        <choose>
            <when test='granularityType == "IRB"'>
                prod_rd_team_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "INDUS"'>
                industry_catg_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "PROD"'>
                prod_list_cn_name as prod_rnd_team_cn_name
            </when>
            <otherwise>
            </otherwise>
        </choose>
        from fin_dm_opt_foi.dm_fcst_ict_${costType}_${granularityType}_mon_mid_cost_idx_t
        where del_flag = 'N'
        <if test='basePeriodId != null'>
            and base_period_id = #{basePeriodId}
        </if>
        <if test='overseaFlag != null and overseaFlag != ""'>
            and oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag != null and mainFlag != ""'>
            and main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes != null and codeAttributes != ""'>
            and code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='groupCode != null and groupCode != ""'>
            and group_code = #{groupCode,jdbcType=VARCHAR}
        </if>
        <if test='parentCode != null and parentCode != ""'>
            and parent_code = #{parentCode,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='dimensionCode != null and dimensionCode != ""'>
            and dimension_code = #{dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubCategoryCode != null and dimensionSubCategoryCode != ""'>
            and dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubDetailCode != null and dimensionSubDetailCode != ""'>
            and dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='prodRndTeamCodeList != null and prodRndTeamCodeList.size() > 0'>
            <choose>
                <when test='teamLevel == "LV0"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND lv0_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='teamLevel == "LV1"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND lv1_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='teamLevel == "LV2"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND lv2_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='teamLevel == "LV3"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND lv3_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='teamLevel == "LV4"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND lv4_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        order by group_cn_name,prod_rnd_team_cn_name,period_id
    </select>

    <select id="findPriceIndexCodePage" resultMap="resultMap">
        select DISTINCT group_level,group_code,group_cn_name from (
        select DISTINCT
        t1.group_level,t1.group_code,t1.group_cn_name
        from fin_dm_opt_foi.DM_FCST_ICT_PSP_${searchVO.granularityType}_MON_COST_IDX_T t1
        where t1.del_flag = 'N'
        <if test='searchVO.overseaFlag != null and searchVO.overseaFlag != ""'>
            and t1.oversea_flag = #{searchVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.bgCode != null and searchVO.bgCode != ""'>
            and t1.bg_code = #{searchVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.regionCode != null and searchVO.regionCode != ""'>
            and t1.region_code = #{searchVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.repofficeCode != null and searchVO.repofficeCode != ""'>
            and t1.repoffice_code = #{searchVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.mainFlag != null and searchVO.mainFlag != ""'>
            and t1.main_flag = #{searchVO.mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.codeAttributes != null and searchVO.codeAttributes != ""'>
            and t1.code_attributes = #{searchVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.groupLevel != null and searchVO.groupLevel != ""'>
            and t1.group_level = #{searchVO.groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.parentCode != null and searchVO.parentCode != ""'>
            and t1.parent_code = #{searchVO.parentCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.viewFlag != null and searchVO.viewFlag != ""'>
            and t1.view_flag = #{searchVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.dimensionCode != null and searchVO.dimensionCode != ""'>
            and t1.dimension_code = #{searchVO.dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.dimensionSubCategoryCode != null and searchVO.dimensionSubCategoryCode != ""'>
            and t1.dimension_subcategory_code = #{searchVO.dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.dimensionSubDetailCode != null and searchVO.dimensionSubDetailCode != ""'>
            and t1.dimension_sub_detail_code = #{searchVO.dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.versionId != null'>
            and t1.version_id = #{searchVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='searchVO.basePeriodId != null'>
            and t1.base_period_id = #{searchVO.basePeriodId}
        </if>
        <if test='searchVO.prodRndTeamCodeList != null and searchVO.prodRndTeamCodeList.size() > 0'>
            <choose>
                <when test='searchVO.granularityType == "IRB"'>
                    <foreach collection='searchVO.prodRndTeamCodeList' item="code" open="AND t1.prod_rnd_team_code IN ("
                             close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='searchVO.granularityType == "INDUS"'>
                    <foreach collection='searchVO.prodRndTeamCodeList' item="code" open="AND t1.industry_catg_code IN ("
                             close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='searchVO.granularityType == "PROD"'>
                    <foreach collection='searchVO.prodRndTeamCodeList' item="code" open="AND t1.prod_list_code IN ("
                             close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        union all
        select DISTINCT
        t1.group_level,t1.group_code,t1.group_cn_name
        from fin_dm_opt_foi.DM_FCST_ICT_STD_${searchVO.granularityType}_MON_COST_IDX_T t1
        where t1.del_flag = 'N'
        <if test='searchVO.overseaFlag != null and searchVO.overseaFlag != ""'>
            and t1.oversea_flag = #{searchVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.bgCode != null and searchVO.bgCode != ""'>
            and t1.bg_code = #{searchVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.regionCode != null and searchVO.regionCode != ""'>
            and t1.region_code = #{searchVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.repofficeCode != null and searchVO.repofficeCode != ""'>
            and t1.repoffice_code = #{searchVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.mainFlag != null and searchVO.mainFlag != ""'>
            and t1.main_flag = #{searchVO.mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.codeAttributes != null and searchVO.codeAttributes != ""'>
            and t1.code_attributes = #{searchVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.groupLevel != null and searchVO.groupLevel != ""'>
            and t1.group_level = #{searchVO.groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.parentCode != null and searchVO.parentCode != ""'>
            and t1.parent_code = #{searchVO.parentCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.viewFlag != null and searchVO.viewFlag != ""'>
            and t1.view_flag = #{searchVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.dimensionCode != null and searchVO.dimensionCode != ""'>
            and t1.dimension_code = #{searchVO.dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.dimensionSubCategoryCode != null and searchVO.dimensionSubCategoryCode != ""'>
            and t1.dimension_subcategory_code = #{searchVO.dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.dimensionSubDetailCode != null and searchVO.dimensionSubDetailCode != ""'>
            and t1.dimension_sub_detail_code = #{searchVO.dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.versionId != null'>
            and t1.version_id = #{searchVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='searchVO.basePeriodId != null'>
            and t1.base_period_id = #{searchVO.basePeriodId}
        </if>
        <if test='searchVO.prodRndTeamCodeList != null and searchVO.prodRndTeamCodeList.size() > 0'>
            <choose>
                <when test='searchVO.granularityType == "IRB"'>
                    <foreach collection='searchVO.prodRndTeamCodeList' item="code" open="AND t1.prod_rnd_team_code IN ("
                             close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='searchVO.granularityType == "INDUS"'>
                    <foreach collection='searchVO.prodRndTeamCodeList' item="code" open="AND t1.industry_catg_code IN ("
                             close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='searchVO.granularityType == "PROD"'>
                    <foreach collection='searchVO.prodRndTeamCodeList' item="code" open="AND t1.prod_list_code IN ("
                             close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        )
        order by group_code
        LIMIT #{pageVO.pageSize} OFFSET #{pageVO.mysqlStartIndex}
    </select>

    <select id="findPriceIndexCodePageCount" resultType="int">
        select count(1) from (
        select distinct group_level,group_code,group_cn_name from (
        select distinct t1.group_level,t1.group_code,t1.group_cn_name
        from fin_dm_opt_foi.DM_FCST_ICT_PSP_${searchVO.granularityType}_MON_COST_IDX_T t1
        where t1.del_flag = 'N'
        <if test='searchVO.overseaFlag != null and searchVO.overseaFlag != ""'>
            and t1.oversea_flag = #{searchVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.bgCode != null and searchVO.bgCode != ""'>
            and t1.bg_code = #{searchVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.regionCode != null and searchVO.regionCode != ""'>
            and t1.region_code = #{searchVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.repofficeCode != null and searchVO.repofficeCode != ""'>
            and t1.repoffice_code = #{searchVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.mainFlag != null and searchVO.mainFlag != ""'>
            and t1.main_flag = #{searchVO.mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.codeAttributes != null and searchVO.codeAttributes != ""'>
            and t1.code_attributes = #{searchVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.groupLevel != null and searchVO.groupLevel != ""'>
            and t1.group_level = #{searchVO.groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.parentCode != null and searchVO.parentCode != ""'>
            and t1.parent_code = #{searchVO.parentCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.viewFlag != null and searchVO.viewFlag != ""'>
            and t1.view_flag = #{searchVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.dimensionCode != null and searchVO.dimensionCode != ""'>
            and t1.dimension_code = #{searchVO.dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.dimensionSubCategoryCode != null and searchVO.dimensionSubCategoryCode != ""'>
            and t1.dimension_subcategory_code = #{searchVO.dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.dimensionSubDetailCode != null and searchVO.dimensionSubDetailCode != ""'>
            and t1.dimension_sub_detail_code = #{searchVO.dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.versionId != null'>
            and t1.version_id = #{searchVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='searchVO.basePeriodId != null'>
            and t1.base_period_id = #{searchVO.basePeriodId}
        </if>
        <if test='searchVO.prodRndTeamCodeList != null and searchVO.prodRndTeamCodeList.size() > 0'>
            <choose>
                <when test='searchVO.granularityType == "IRB"'>
                    <foreach collection='searchVO.prodRndTeamCodeList' item="code" open="AND t1.prod_rnd_team_code IN ("
                             close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='searchVO.granularityType == "INDUS"'>
                    <foreach collection='searchVO.prodRndTeamCodeList' item="code" open="AND t1.industry_catg_code IN ("
                             close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='searchVO.granularityType == "PROD"'>
                    <foreach collection='searchVO.prodRndTeamCodeList' item="code" open="AND t1.prod_list_code IN ("
                             close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        union all
        select distinct t1.group_level,t1.group_code,t1.group_cn_name
        from fin_dm_opt_foi.DM_FCST_ICT_STD_${searchVO.granularityType}_MON_COST_IDX_T t1
        where t1.del_flag = 'N'
        <if test='searchVO.overseaFlag != null and searchVO.overseaFlag != ""'>
            and t1.oversea_flag = #{searchVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.bgCode != null and searchVO.bgCode != ""'>
            and t1.bg_code = #{searchVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.regionCode != null and searchVO.regionCode != ""'>
            and t1.region_code = #{searchVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.repofficeCode != null and searchVO.repofficeCode != ""'>
            and t1.repoffice_code = #{searchVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.mainFlag != null and searchVO.mainFlag != ""'>
            and t1.main_flag = #{searchVO.mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.codeAttributes != null and searchVO.codeAttributes != ""'>
            and t1.code_attributes = #{searchVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.groupLevel != null and searchVO.groupLevel != ""'>
            and t1.group_level = #{searchVO.groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.parentCode != null and searchVO.parentCode != ""'>
            and t1.parent_code = #{searchVO.parentCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.viewFlag != null and searchVO.viewFlag != ""'>
            and t1.view_flag = #{searchVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.dimensionCode != null and searchVO.dimensionCode != ""'>
            and t1.dimension_code = #{searchVO.dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.dimensionSubCategoryCode != null and searchVO.dimensionSubCategoryCode != ""'>
            and t1.dimension_subcategory_code = #{searchVO.dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.dimensionSubDetailCode != null and searchVO.dimensionSubDetailCode != ""'>
            and t1.dimension_sub_detail_code = #{searchVO.dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.versionId != null'>
            and t1.version_id = #{searchVO.versionId,jdbcType=NUMERIC}
        </if>
        <if test='searchVO.basePeriodId != null'>
            and t1.base_period_id = #{searchVO.basePeriodId}
        </if>
        <if test='searchVO.prodRndTeamCodeList != null and searchVO.prodRndTeamCodeList.size() > 0'>
            <choose>
                <when test='searchVO.granularityType == "IRB"'>
                    <foreach collection='searchVO.prodRndTeamCodeList' item="code" open="AND t1.prod_rnd_team_code IN ("
                             close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='searchVO.granularityType == "INDUS"'>
                    <foreach collection='searchVO.prodRndTeamCodeList' item="code" open="AND t1.industry_catg_code IN ("
                             close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='searchVO.granularityType == "PROD"'>
                    <foreach collection='searchVO.prodRndTeamCodeList' item="code" open="AND t1.prod_list_code IN ("
                             close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        )
        )
    </select>

    <select id="findCustomPriceIndexCodePage" resultMap="resultMap">
        select distinct group_level,group_code,group_cn_name,costType from (
        select
        distinct t1.group_level,t1.group_code,t1.group_cn_name,'PSP' as costType
        from fin_dm_opt_foi.DM_FCST_ICT_PSP_BASE_CUS_MON_COST_IDX_T t1
        where t1.del_flag = 'N'
        <if test='searchVO.customId != null'>
            and t1.custom_id = #{searchVO.customId}
        </if>
        <if test='searchVO.viewFlag != null and searchVO.viewFlag != "" and searchVO.viewFlag == "PROD_SPART"'>
            and t1.group_level = 'SPART'
        </if>
        <if test='searchVO.viewFlag != null and searchVO.viewFlag != "" and searchVO.viewFlag == "DIMENSION"'>
            and t1.group_level = 'SUB_DETAIL'
        </if>
        <if test='searchVO.granularityType != null and searchVO.granularityType != ""'>
            and t1.granularity_type = #{searchVO.granularityType,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.overseaFlag != null and searchVO.overseaFlag != ""'>
            and t1.oversea_flag = #{searchVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.bgCode != null and searchVO.bgCode != ""'>
            and t1.bg_code = #{searchVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.regionCode != null and searchVO.regionCode != ""'>
            and t1.region_code = #{searchVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.repofficeCode != null and searchVO.repofficeCode != ""'>
            and t1.repoffice_code = #{searchVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.mainFlag != null and searchVO.mainFlag != ""'>
            and t1.main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.codeAttributes != null and searchVO.codeAttributes != ""'>
            and t1.code_attributes = #{searchVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.groupCode != null and searchVO.groupCode != ""'>
            and t1.group_code = #{searchVO.groupCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.parentCode != null and searchVO.parentCode != ""'>
            and t1.parent_code = #{searchVO.parentCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.viewFlag != null and searchVO.viewFlag != ""'>
            and t1.view_flag = #{searchVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.versionId != null'>
            and t1.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        union all
        select
        distinct t1.group_level,t1.group_code,t1.group_cn_name,'STD' as costType
        from fin_dm_opt_foi.DM_FCST_ICT_STD_BASE_CUS_MON_COST_IDX_T t1
        where t1.del_flag = 'N'
        <if test='searchVO.customId != null'>
            and t1.custom_id = #{searchVO.customId}
        </if>
        <if test='searchVO.viewFlag != null and searchVO.viewFlag != "" and searchVO.viewFlag == "PROD_SPART"'>
            and t1.group_level = 'SPART'
        </if>
        <if test='searchVO.viewFlag != null and searchVO.viewFlag != "" and searchVO.viewFlag == "DIMENSION"'>
            and t1.group_level = 'SUB_DETAIL'
        </if>
        <if test='searchVO.granularityType != null and searchVO.granularityType != ""'>
            and t1.granularity_type = #{searchVO.granularityType,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.overseaFlag != null and searchVO.overseaFlag != ""'>
            and t1.oversea_flag = #{searchVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.bgCode != null and searchVO.bgCode != ""'>
            and t1.bg_code = #{searchVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.regionCode != null and searchVO.regionCode != ""'>
            and t1.region_code = #{searchVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.repofficeCode != null and searchVO.repofficeCode != ""'>
            and t1.repoffice_code = #{searchVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.mainFlag != null and searchVO.mainFlag != ""'>
            and t1.main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.codeAttributes != null and searchVO.codeAttributes != ""'>
            and t1.code_attributes = #{searchVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.groupCode != null and searchVO.groupCode != ""'>
            and t1.group_code = #{searchVO.groupCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.parentCode != null and searchVO.parentCode != ""'>
            and t1.parent_code = #{searchVO.parentCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.viewFlag != null and searchVO.viewFlag != ""'>
            and t1.view_flag = #{searchVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.versionId != null'>
            and t1.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        )
        order by group_code
        LIMIT #{pageVO.pageSize} OFFSET #{pageVO.mysqlStartIndex}
    </select>

    <select id="findCustomPriceIndexCodePageCount" resultType="int">
        select count(1) from (
        select distinct group_level,group_code,group_cn_name from (
        select
        distinct t1.group_level,t1.group_code,t1.group_cn_name
        from fin_dm_opt_foi.DM_FCST_ICT_PSP_BASE_CUS_MON_COST_IDX_T t1
        where t1.del_flag = 'N'
        <if test='searchVO.customId != null'>
            and t1.custom_id = #{searchVO.customId}
        </if>
        <if test='searchVO.viewFlag != null and searchVO.viewFlag != "" and searchVO.viewFlag == "PROD_SPART"'>
            and t1.group_level = 'SPART'
        </if>
        <if test='searchVO.viewFlag != null and searchVO.viewFlag != "" and searchVO.viewFlag == "DIMENSION"'>
            and t1.group_level = 'SUB_DETAIL'
        </if>
        <if test='searchVO.granularityType != null and searchVO.granularityType != ""'>
            and t1.granularity_type = #{searchVO.granularityType,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.overseaFlag != null and searchVO.overseaFlag != ""'>
            and t1.oversea_flag = #{searchVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.bgCode != null and searchVO.bgCode != ""'>
            and t1.bg_code = #{searchVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.regionCode != null and searchVO.regionCode != ""'>
            and t1.region_code = #{searchVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.repofficeCode != null and searchVO.repofficeCode != ""'>
            and t1.repoffice_code = #{searchVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.mainFlag != null and searchVO.mainFlag != ""'>
            and t1.main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.codeAttributes != null and searchVO.codeAttributes != ""'>
            and t1.code_attributes = #{searchVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.groupCode != null and searchVO.groupCode != ""'>
            and t1.group_code = #{searchVO.groupCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.parentCode != null and searchVO.parentCode != ""'>
            and t1.parent_code = #{searchVO.parentCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.viewFlag != null and searchVO.viewFlag != ""'>
            and t1.view_flag = #{searchVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.versionId != null'>
            and t1.version_id = #{searchVO.versionId,jdbcType=NUMERIC}
        </if>
        union all
        select
        distinct t1.group_level,t1.group_code,t1.group_cn_name
        from fin_dm_opt_foi.DM_FCST_ICT_STD_BASE_CUS_MON_COST_IDX_T t1
        where t1.del_flag = 'N'
        <if test='searchVO.customId != null'>
            and t1.custom_id = #{searchVO.customId}
        </if>
        <if test='searchVO.viewFlag != null and searchVO.viewFlag != "" and searchVO.viewFlag == "PROD_SPART"'>
            and t1.group_level = 'SPART'
        </if>
        <if test='searchVO.viewFlag != null and searchVO.viewFlag != "" and searchVO.viewFlag == "DIMENSION"'>
            and t1.group_level = 'SUB_DETAIL'
        </if>
        <if test='searchVO.granularityType != null and searchVO.granularityType != ""'>
            and t1.granularity_type = #{searchVO.granularityType,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.overseaFlag != null and searchVO.overseaFlag != ""'>
            and t1.oversea_flag = #{searchVO.overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.bgCode != null and searchVO.bgCode != ""'>
            and t1.bg_code = #{searchVO.bgCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.regionCode != null and searchVO.regionCode != ""'>
            and t1.region_code = #{searchVO.regionCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.repofficeCode != null and searchVO.repofficeCode != ""'>
            and t1.repoffice_code = #{searchVO.repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.mainFlag != null and searchVO.mainFlag != ""'>
            and t1.main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.codeAttributes != null and searchVO.codeAttributes != ""'>
            and t1.code_attributes = #{searchVO.codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.groupCode != null and searchVO.groupCode != ""'>
            and t1.group_code = #{searchVO.groupCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.parentCode != null and searchVO.parentCode != ""'>
            and t1.parent_code = #{searchVO.parentCode,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.viewFlag != null and searchVO.viewFlag != ""'>
            and t1.view_flag = #{searchVO.viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='searchVO.versionId != null'>
            and t1.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        )
        )
    </select>

    <select id="findMultiPriceIndexByPage" resultMap="resultMap">
        select
        t1.dimension_code,t1.dimension_cn_name,
        t1.dimension_subcategory_code,t1.dimension_subcategory_cn_name,
        t1.dimension_sub_detail_code,t1.dimension_sub_detail_cn_name,
        t1.group_level,t1.group_code,t1.group_cn_name,t1.parent_cn_name,
        t1.period_id,ROUND(t1.cost_index, 2) as cost_index,t2.weight_rate,'PSP' as costType,
        <choose>
            <when test='granularityType == "IRB"'>
                t1.prod_rd_team_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "INDUS"'>
                t1.industry_catg_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "PROD"'>
                t1.prod_list_cn_name as prod_rnd_team_cn_name
            </when>
            <otherwise>
            </otherwise>
        </choose>
        from fin_dm_opt_foi.DM_FCST_ICT_PSP_${granularityType}_MON_COST_IDX_T t1
        left join fin_dm_opt_foi.DM_FCST_ICT_PSP_${granularityType}_MON_WEIGHT_T t2
        on t1.view_flag = t2.view_flag and t1.group_code = t2.group_code
        and t1.group_level = t2.group_level
        and t1.parent_code = t2.parent_code
        and t1.version_id =t2.version_id
        and t2.period_year = #{periodYear}
        and t1.oversea_flag = t2.oversea_flag
        and t1.bg_code = t2.bg_code
        and t1.region_code = t2.region_code
        and t1.repoffice_code = t2.repoffice_code
        and nvl(t1.dimension_code,'snull') = nvl( t2.dimension_code,'snull')
        and nvl(t1.dimension_subcategory_code,'snull') = nvl( t2.dimension_subcategory_code,'snull')
        and nvl(t1.dimension_sub_detail_code,'snull') = nvl( t2.dimension_sub_detail_code,'snull')
        and nvl ( t1.main_flag, 'snull' ) = nvl ( t2.main_flag, 'snull' )
        and nvl ( t1.code_attributes, 'snull' ) = nvl ( t2.code_attributes, 'snull' )
        <choose>
            <when test='granularityType == "IRB"'>
                and t1.prod_rnd_team_code = t2.prod_rnd_team_code
            </when>
            <when test='granularityType == "INDUS"'>
                and t1.industry_catg_code = t2.industry_catg_code
            </when>
            <when test='granularityType == "PROD"'>
                and t1.prod_list_code = t2.prod_list_code
            </when>
            <otherwise>
            </otherwise>
        </choose>
        where t1.del_flag = 'N'
        <if test='overseaFlag != null and overseaFlag != ""'>
            and t1.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and t1.bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and t1.region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and t1.repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag != null and mainFlag != ""'>
            and t1.main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes != null and codeAttributes != ""'>
            and t1.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and t1.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='parentCode != null and parentCode != ""'>
            and t1.parent_code = #{parentCode,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and t1.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='dimensionCode != null and dimensionCode != ""'>
            and t1.dimension_code = #{dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubCategoryCode != null and dimensionSubCategoryCode != ""'>
            and t1.dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubDetailCode != null and dimensionSubDetailCode != ""'>
            and t1.dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and t1.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='basePeriodId != null'>
            and t1.base_period_id = #{basePeriodId}
        </if>
        <if test='groupCodeList != null and groupCodeList.size() > 0'>
            <foreach collection='groupCodeList' item="code" open="AND t1.group_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='prodRndTeamCodeList != null and prodRndTeamCodeList.size() > 0'>
            <choose>
                <when test='granularityType == "IRB"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND t1.prod_rnd_team_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='granularityType == "INDUS"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND t1.industry_catg_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='granularityType == "PROD"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND t1.prod_list_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        union all
        select
        t1.dimension_code,t1.dimension_cn_name,
        t1.dimension_subcategory_code,t1.dimension_subcategory_cn_name,
        t1.dimension_sub_detail_code,t1.dimension_sub_detail_cn_name,
        t1.group_level,t1.group_code,t1.group_cn_name,t1.parent_cn_name,
        t1.period_id,ROUND(t1.cost_index, 2) as cost_index,t2.weight_rate,'STD' as costType,
        <choose>
            <when test='granularityType == "IRB"'>
                t1.prod_rd_team_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "INDUS"'>
                t1.industry_catg_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "PROD"'>
                t1.prod_list_cn_name as prod_rnd_team_cn_name
            </when>
            <otherwise>
            </otherwise>
        </choose>
        from fin_dm_opt_foi.DM_FCST_ICT_STD_${granularityType}_MON_COST_IDX_T t1
        left join fin_dm_opt_foi.DM_FCST_ICT_STD_${granularityType}_MON_WEIGHT_T t2
        on t1.view_flag = t2.view_flag and t1.group_code = t2.group_code
        and t1.group_level = t2.group_level
        and t1.parent_code = t2.parent_code
        and t1.version_id =t2.version_id
        and t2.period_year = #{periodYear}
        and t1.oversea_flag = t2.oversea_flag
        and t1.bg_code = t2.bg_code
        and t1.region_code = t2.region_code
        and t1.repoffice_code = t2.repoffice_code
        and nvl(t1.dimension_code,'snull') = nvl( t2.dimension_code,'snull')
        and nvl(t1.dimension_subcategory_code,'snull') = nvl( t2.dimension_subcategory_code,'snull')
        and nvl(t1.dimension_sub_detail_code,'snull') = nvl( t2.dimension_sub_detail_code,'snull')
        and nvl ( t1.main_flag, 'snull' ) = nvl ( t2.main_flag, 'snull' )
        and nvl ( t1.code_attributes, 'snull' ) = nvl ( t2.code_attributes, 'snull' )
        <choose>
            <when test='granularityType == "IRB"'>
                and t1.prod_rnd_team_code = t2.prod_rnd_team_code
            </when>
            <when test='granularityType == "INDUS"'>
                and t1.industry_catg_code = t2.industry_catg_code
            </when>
            <when test='granularityType == "PROD"'>
                and t1.prod_list_code = t2.prod_list_code
            </when>
            <otherwise>
            </otherwise>
        </choose>
        where t1.del_flag = 'N'
        <if test='overseaFlag != null and overseaFlag != ""'>
            and t1.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and t1.bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and t1.region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and t1.repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag != null and mainFlag != ""'>
            and t1.main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes != null and codeAttributes != ""'>
            and t1.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and t1.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='parentCode != null and parentCode != ""'>
            and t1.parent_code = #{parentCode,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and t1.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='dimensionCode != null and dimensionCode != ""'>
            and t1.dimension_code = #{dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubCategoryCode != null and dimensionSubCategoryCode != ""'>
            and t1.dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubDetailCode != null and dimensionSubDetailCode != ""'>
            and t1.dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and t1.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='basePeriodId != null'>
            and t1.base_period_id = #{basePeriodId}
        </if>
        <if test='groupCodeList != null and groupCodeList.size() > 0'>
            <foreach collection='groupCodeList' item="code" open="AND t1.group_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='prodRndTeamCodeList != null and prodRndTeamCodeList.size() > 0'>
            <choose>
                <when test='granularityType == "IRB"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND t1.prod_rnd_team_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='granularityType == "INDUS"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND t1.industry_catg_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='granularityType == "PROD"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND t1.prod_list_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        order by period_id
    </select>

    <select id="findCustomMultiPriceIndexByPage" resultMap="resultMap">
        select
        t1.dimension_code,t1.dimension_cn_name,
        t1.dimension_subcategory_code,t1.dimension_subcategory_cn_name,
        t1.dimension_sub_detail_code,t1.dimension_sub_detail_cn_name,
        t1.group_level,t1.group_code,t1.group_cn_name,t1.parent_cn_name,
        t1.period_id,ROUND(t1.cost_index, 2) as cost_index,t2.weight_rate
        from fin_dm_opt_foi.DM_FCST_ICT_PSP_BASE_CUS_MON_COST_IDX_T t1
        left join fin_dm_opt_foi.DM_FCST_ICT_PSP_BASE_CUS_MON_WEIGHT_T t2
        on t1.view_flag = t2.view_flag and t1.group_code = t2.group_code
        and t1.granularityType = t2.granularityType
        and t2.period_year = #{periodYear}
        and t1.custom_id = t2.custom_id
        and t1.group_level = t2.group_level
        and t1.parent_code = t2.parent_code
        and t1.parent_level = t2.parent_level
        and t1.version_id =t2.version_id and t1.period_id = t2.period_id
        and t1.oversea_flag = t2.oversea_flag
        and t1.bg_code = t2.bg_code
        and t1.region_code = t2.region_code
        and t1.repoffice_code = t2.repoffice_code
        and nvl ( t1.main_flag, 'snull' ) = nvl ( t2.main_flag, 'snull' )
        and nvl ( t1.code_attributes, 'snull' ) = nvl ( t2.code_attributes, 'snull' )
        where t1.del_flag = 'N'
        <if test='customId != null'>
            and t1.custom_id = #{customId}
        </if>
        <if test='viewFlag != null and viewFlag != "" and viewFlag == "PROD_SPART"'>
            and t1.group_level = 'SPART'
        </if>
        <if test='viewFlag != null and viewFlag != "" and viewFlag == "DIMENSION"'>
            and t1.group_level = 'SUB_DETAIL'
        </if>
        <if test='granularityType != null and granularityType != ""'>
            and t1.granularity_type = #{granularityType,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag != null and overseaFlag != ""'>
            and t1.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and t1.bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and t1.region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and t1.repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag != null and mainFlag != ""'>
            and t1.main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes != null and codeAttributes != ""'>
            and t1.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='groupCode != null and groupCode != ""'>
            and t1.group_code = #{groupCode,jdbcType=VARCHAR}
        </if>
        <if test='parentCode != null and parentCode != ""'>
            and t1.parent_code = #{parentCode,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and t1.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and t1.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='groupCodeList != null and groupCodeList.size() > 0'>
            <foreach collection='groupCodeList' item="code" open="AND t1.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        union all
        select
        t1.dimension_code,t1.dimension_cn_name,
        t1.dimension_subcategory_code,t1.dimension_subcategory_cn_name,
        t1.dimension_sub_detail_code,t1.dimension_sub_detail_cn_name,
        t1.group_level,t1.group_code,t1.group_cn_name,t1.parent_cn_name,
        t1.period_id,ROUND(t1.cost_index, 2) as cost_index,t2.weight_rate
        from fin_dm_opt_foi.DM_FCST_ICT_STD_BASE_CUS_MON_COST_IDX_T t1
        left join fin_dm_opt_foi.DM_FCST_ICT_STD_BASE_CUS_MON_WEIGHT_T t2
        on t1.view_flag = t2.view_flag and t1.group_code = t2.group_code
        and t1.granularityType = t2.granularityType
        and t2.period_year = #{periodYear}
        and t1.custom_id = t2.custom_id
        and t1.group_level = t2.group_level
        and t1.parent_code = t2.parent_code
        and t1.parent_level = t2.parent_level
        and t1.version_id =t2.version_id and t1.period_id = t2.period_id
        and t1.oversea_flag = t2.oversea_flag
        and t1.bg_code = t2.bg_code
        and t1.region_code = t2.region_code
        and t1.repoffice_code = t2.repoffice_code
        and nvl ( t1.main_flag, 'snull' ) = nvl ( t2.main_flag, 'snull' )
        and nvl ( t1.code_attributes, 'snull' ) = nvl ( t2.code_attributes, 'snull' )
        where t1.del_flag = 'N'
        <if test='customId != null'>
            and t1.custom_id = #{customId}
        </if>
        <if test='viewFlag != null and viewFlag != "" and viewFlag == "PROD_SPART"'>
            and t1.group_level = 'SPART'
        </if>
        <if test='viewFlag != null and viewFlag != "" and viewFlag == "DIMENSION"'>
            and t1.group_level = 'SUB_DETAIL'
        </if>
        <if test='granularityType != null and granularityType != ""'>
            and t1.granularity_type = #{granularityType,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag != null and overseaFlag != ""'>
            and t1.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and t1.bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and t1.region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and t1.repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag != null and mainFlag != ""'>
            and t1.main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes != null and codeAttributes != ""'>
            and t1.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='groupCode != null and groupCode != ""'>
            and t1.group_code = #{groupCode,jdbcType=VARCHAR}
        </if>
        <if test='parentCode != null and parentCode != ""'>
            and t1.parent_code = #{parentCode,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and t1.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and t1.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='groupCodeList != null and groupCodeList.size() > 0'>
            <foreach collection='groupCodeList' item="code" open="AND t1.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        order by period_id
    </select>

    <select id="findMultiPriceIndexMultiType" resultMap="resultMap">
        select
        t1.dimension_code,t1.dimension_cn_name,
        t1.dimension_subcategory_code,t1.dimension_subcategory_cn_name,
        t1.dimension_sub_detail_code,t1.dimension_sub_detail_cn_name,
        t1.group_level,t1.group_code,t1.group_cn_name,t1.parent_cn_name,t1.parent_code,
        t1.period_id,ROUND(t1.cost_index, 2) as cost_index,t2.weight_rate,
        <choose>
            <when test='granularityType == "IRB"'>
                t1.prod_rd_team_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "INDUS"'>
                t1.industry_catg_cn_name as prod_rnd_team_cn_name
            </when>
            <when test='granularityType == "PROD"'>
                t1.prod_list_cn_name as prod_rnd_team_cn_name
            </when>
            <otherwise>
            </otherwise>
        </choose>
        from fin_dm_opt_foi.DM_FCST_ICT_${costType}_${granularityType}_MON_COST_IDX_T t1
        left join fin_dm_opt_foi.DM_FCST_ICT_${costType}_${granularityType}_MON_WEIGHT_T t2
        on t1.view_flag = t2.view_flag and t1.group_code = t2.group_code
        and t1.group_level = t2.group_level
        and t1.parent_code = t2.parent_code
        and t1.version_id =t2.version_id
        and t2.period_year = #{periodYear}
        and t1.oversea_flag = t2.oversea_flag
        and t1.bg_code = t2.bg_code
        and t1.region_code = t2.region_code
        and t1.repoffice_code = t2.repoffice_code
        and nvl(t1.dimension_code,'snull') = nvl( t2.dimension_code,'snull')
        and nvl(t1.dimension_subcategory_code,'snull') = nvl( t2.dimension_subcategory_code,'snull')
        and nvl(t1.dimension_sub_detail_code,'snull') = nvl( t2.dimension_sub_detail_code,'snull')
        and nvl ( t1.main_flag, 'snull' ) = nvl ( t2.main_flag, 'snull' )
        and nvl ( t1.code_attributes, 'snull' ) = nvl ( t2.code_attributes, 'snull' )
        <choose>
            <when test='granularityType == "IRB"'>
                and t1.prod_rnd_team_code = t2.prod_rnd_team_code
            </when>
            <when test='granularityType == "INDUS"'>
                and t1.industry_catg_code = t2.industry_catg_code
            </when>
            <when test='granularityType == "PROD"'>
                and t1.prod_list_code = t2.prod_list_code
            </when>
            <otherwise>
            </otherwise>
        </choose>
        where t1.del_flag = 'N'
        <if test='overseaFlag != null and overseaFlag != ""'>
            and t1.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and t1.bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and t1.region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and t1.repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag != null and mainFlag != ""'>
            and t1.main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes != null and codeAttributes != ""'>
            and t1.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and t1.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='parentCode != null and parentCode != ""'>
            and t1.parent_code = #{parentCode,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and t1.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='dimensionCode != null and dimensionCode != ""'>
            and t1.dimension_code = #{dimensionCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubCategoryCode != null and dimensionSubCategoryCode != ""'>
            and t1.dimension_subcategory_code = #{dimensionSubCategoryCode,jdbcType=VARCHAR}
        </if>
        <if test='dimensionSubDetailCode != null and dimensionSubDetailCode != ""'>
            and t1.dimension_sub_detail_code = #{dimensionSubDetailCode,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and t1.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='basePeriodId != null'>
            and t1.base_period_id = #{basePeriodId}
        </if>
        <if test='prodRndTeamCodeList != null and prodRndTeamCodeList.size() > 0'>
            <choose>
                <when test='granularityType == "IRB"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND t1.prod_rnd_team_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='granularityType == "INDUS"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND t1.industry_catg_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <when test='granularityType == "PROD"'>
                    <foreach collection='prodRndTeamCodeList' item="code" open="AND t1.prod_list_code IN (" close=")"
                             index="index"
                             separator=",">
                        #{code}
                    </foreach>
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        order by t1.group_cn_name,t1.period_id
    </select>

    <select id="findCustomMultiPriceIndexMultiType" resultMap="resultMap">
        select
        t1.dimension_code,t1.dimension_cn_name,
        t1.dimension_subcategory_code,t1.dimension_subcategory_cn_name,
        t1.dimension_sub_detail_code,t1.dimension_sub_detail_cn_name,
        t1.group_level,t1.group_code,t1.group_cn_name,t1.parent_cn_name,
        t1.period_id,ROUND(t1.cost_index, 2) as cost_index,t2.weight_rate
        from fin_dm_opt_foi.DM_FCST_ICT_${costType}_BASE_CUS_MON_COST_IDX_T t1
        left join fin_dm_opt_foi.DM_FCST_ICT_${costType}_BASE_CUS_MON_WEIGHT_T t2
        on t1.view_flag = t2.view_flag and t1.group_code = t2.group_code
        and t1.granularityType = t2.granularityType
        and t2.period_year = #{periodYear}
        and t1.custom_id = t2.custom_id
        and t1.group_level = t2.group_level
        and t1.parent_code = t2.parent_code
        and t1.parent_level = t2.parent_level
        and t1.version_id =t2.version_id and t1.period_id = t2.period_id
        and t1.oversea_flag = t2.oversea_flag
        and t1.bg_code = t2.bg_code
        and t1.region_code = t2.region_code
        and t1.repoffice_code = t2.repoffice_code
        and nvl ( t1.main_flag, 'snull' ) = nvl ( t2.main_flag, 'snull' )
        and nvl ( t1.code_attributes, 'snull' ) = nvl ( t2.code_attributes, 'snull' )
        where t1.del_flag = 'N'
        <if test='customId != null'>
            and t1.custom_id = #{customId}
        </if>
        <if test='viewFlag != null and viewFlag != "" and viewFlag == "PROD_SPART"'>
            and t1.group_level = 'SPART'
        </if>
        <if test='viewFlag != null and viewFlag != "" and viewFlag == "DIMENSION"'>
            and t1.group_level = 'SUB_DETAIL'
        </if>
        <if test='granularityType != null and granularityType != ""'>
            and t1.granularity_type = #{granularityType,jdbcType=VARCHAR}
        </if>
        <if test='overseaFlag != null and overseaFlag != ""'>
            and t1.oversea_flag = #{overseaFlag,jdbcType=VARCHAR}
        </if>
        <if test='bgCode != null and bgCode != ""'>
            and t1.bg_code = #{bgCode,jdbcType=VARCHAR}
        </if>
        <if test='regionCode != null and regionCode != ""'>
            and t1.region_code = #{regionCode,jdbcType=VARCHAR}
        </if>
        <if test='repofficeCode != null and repofficeCode != ""'>
            and t1.repoffice_code = #{repofficeCode,jdbcType=VARCHAR}
        </if>
        <if test='mainFlag != null and mainFlag != ""'>
            and t1.main_flag = #{mainFlag,jdbcType=VARCHAR}
        </if>
        <if test='codeAttributes != null and codeAttributes != ""'>
            and t1.code_attributes = #{codeAttributes,jdbcType=VARCHAR}
        </if>
        <if test='groupCode != null and groupCode != ""'>
            and t1.group_code = #{groupCode,jdbcType=VARCHAR}
        </if>
        <if test='parentCode != null and parentCode != ""'>
            and t1.parent_code = #{parentCode,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and t1.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and t1.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        order by t1.group_cn_name,t1.period_id
    </select>

</mapper>