/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.it.fcst.industry.price.impl.month;

import com.huawei.it.fcst.enums.ResultCodeEnum;
import com.huawei.it.fcst.export.IExcelTemplateBeanManager;
import com.huawei.it.fcst.export.IExportProcessorService;
import com.huawei.it.fcst.industry.price.dao.IPriceMonthCostIdxDao;
import com.huawei.it.fcst.industry.price.dao.IPriceMonthRateDao;
import com.huawei.it.fcst.industry.price.dao.IPriceVarifyTaskDao;
import com.huawei.it.fcst.industry.price.enums.GroupLevelEnum;
import com.huawei.it.fcst.industry.price.impl.annual.AnnualAmpPriceService;
import com.huawei.it.fcst.industry.price.impl.common.PriceDataPermissionService;
import com.huawei.it.fcst.industry.price.impl.template.MonthAnalysisTemplateEnum;
import com.huawei.it.fcst.industry.price.service.common.IPriceCommonService;
import com.huawei.it.fcst.industry.price.service.month.IPriceMonthAnalysisService;
import com.huawei.it.fcst.industry.price.utils.FcstIndustryUtil;
import com.huawei.it.fcst.industry.price.vo.month.PriceMonthAnalysisVO;
import com.huawei.it.fcst.util.ObjectCopyUtil;
import com.huawei.it.fcst.vo.DmFocVarifyTaskVO;
import com.huawei.it.fcst.vo.ResultDataVO;
import com.huawei.it.jalor5.core.annotation.JalorOperation;
import com.huawei.it.jalor5.core.annotation.JalorResource;
import com.huawei.it.jalor5.core.base.PageVO;
import com.huawei.it.jalor5.core.base.PagedResult;
import com.huawei.it.jalor5.core.exception.ApplicationException;
import com.huawei.it.jalor5.core.log.Audit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import javax.inject.Inject;
import javax.inject.Named;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * IctMonthAnalysisService Class
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Slf4j
@Named("priceMonthAnalysisService")
@JalorResource(code = "PriceMonthAnalysisService", desc = "定价指数-综合指数分析服务")
public class PriceMonthAnalysisService implements IPriceMonthAnalysisService {

    private static final String NEXT_GROUP_LEVEL = "nextGroupLevel";

    private static final String NEXT_GROUP_NAME = "nextGroupName";

    private static final String OVERSEA_FLAG = "{0}：{1}";

    private static final String REG_OR_DT = "{0}：{1}";

    private static final String REP_OR_SN = "{0}：{1}";

    @Inject
    private IPriceCommonService priceCommonService;

    @Inject
    private IPriceMonthCostIdxDao priceMonthCostIdxDao;

    @Inject
    private IPriceMonthRateDao priceMonthRateDao;

    @Inject
    private IPriceVarifyTaskDao priceVarifyTaskDao;

    @Inject
    private AnnualAmpPriceService annualAmpPriceService;

    @Inject
    private AsyncPriceMonthService asyncPriceMonthService;

    @Inject
    private IExportProcessorService exportProcessorService;

    @Inject
    private PriceDataPermissionService priceDataPermissionService;

    @Override
    @JalorOperation(code = "getMultiBoxList", desc = "定价指数图（多子项）正常维度多选下拉框")
    public ResultDataVO getMultiBoxList(PriceMonthAnalysisVO monthAnalysisVO) throws ApplicationException {
        log.info("==>Begin PriceMonthAnalysisService#getMultiBoxList");
        // 设置用户权限
        priceDataPermissionService.setUserPermission(monthAnalysisVO);
        // 必填字段校验
        if (verifyParameters(monthAnalysisVO)) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        // 查询区间年
        List<String> yearList = annualAmpPriceService.getYearList();
        if (CollectionUtils.isNotEmpty(yearList) && yearList.size() >= 2) {
            monthAnalysisVO.setIntervalYear(yearList.get(0));
        }
        monthAnalysisVO.setVersionId(priceCommonService.getVersionId("MONTH"));
        // 正常维度设置值
        monthAnalysisVO.setParentLevel(monthAnalysisVO.getGroupLevel());
        monthAnalysisVO.setParentCodeList(monthAnalysisVO.getGroupCodeList());
        Map<String, String> groupLevelMap = getGroupLevelMap(monthAnalysisVO);
        // 正常维度查询
        monthAnalysisVO.setGroupLevel(groupLevelMap.get(NEXT_GROUP_LEVEL));
        List<PriceMonthAnalysisVO> dropdownList = priceMonthCostIdxDao.findMultiBoxList(monthAnalysisVO);
        return ResultDataVO.success(getPagedResult(monthAnalysisVO, dropdownList));
    }

    @NotNull
    private PagedResult<PriceMonthAnalysisVO> getPagedResult(PriceMonthAnalysisVO monthAnalysisVO, List<PriceMonthAnalysisVO> dropdownList) {
        PageVO pageVO = new PageVO();
        pageVO.setCurPage(monthAnalysisVO.getPageIndex());
        pageVO.setPageSize(monthAnalysisVO.getPageSize());
        pageVO.setTotalRows(dropdownList.size());
        // 分页
        List<PriceMonthAnalysisVO> pageWeightVOList = dropDownBoxPageList(dropdownList, pageVO);
        PagedResult<PriceMonthAnalysisVO> combDropDownPageList = new PagedResult<>();
        combDropDownPageList.setResult(pageWeightVOList);
        combDropDownPageList.setPageVO(pageVO);
        return combDropDownPageList;
    }

    @NotNull
    private List<PriceMonthAnalysisVO> dropDownBoxPageList(List<PriceMonthAnalysisVO> dropdownList, PageVO pageVO) {
        int count = dropdownList.size();
        int curPage = pageVO.getCurPage() - 1;
        int pageSize = pageVO.getPageSize();
        List<PriceMonthAnalysisVO> dropDownBoxPageList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dropdownList) && count > 0) {
            int fromIndex = curPage * pageSize;
            int totalIndex = (curPage + 1) * pageSize;
            if (totalIndex > count) {
                totalIndex = count;
            }
            dropDownBoxPageList = dropdownList.subList(fromIndex, totalIndex);
        }
        return dropDownBoxPageList;
    }

    private boolean verifyParameters(PriceMonthAnalysisVO monthAnalysisVO) {
        // 区域维度校验
        if (!StringUtils.isAllBlank(monthAnalysisVO.getOverseaFlag(), monthAnalysisVO.getRegionCode(), monthAnalysisVO.getRepofficeCode())) {
            return ObjectUtils.isEmpty(monthAnalysisVO.getBasePeriodId())
                    || StringUtils.isAnyBlank(monthAnalysisVO.getGranularityType(), monthAnalysisVO.getBgCode(),
                    monthAnalysisVO.getOverseaFlag(), monthAnalysisVO.getRegionCode(), monthAnalysisVO.getRepofficeCode());
        }
        // 系统部维度校验
        return ObjectUtils.isEmpty(monthAnalysisVO.getBasePeriodId())
                || StringUtils.isAnyBlank(monthAnalysisVO.getGranularityType(), monthAnalysisVO.getBgCode(),
                monthAnalysisVO.getSignTopCustCategoryCode(),  monthAnalysisVO.getSignSubsidiaryCustcatgCnName());
    }

    /**
     * 获取层级映射
     *
     * @param monthAnalysisVO param VO
     * @return map
     */
    @NotNull
    private Map<String, String> getGroupLevelMap(PriceMonthAnalysisVO monthAnalysisVO) {
        Map<String, String> groupLevelMap = new HashMap<>();
        // 设置虚化时的子层级名称
        if (monthAnalysisVO.getIsNeedBlur()) {
            groupLevelMap = FcstIndustryUtil.getBlurNextGroupLevel(monthAnalysisVO);
        } else {
            // 非最小层级时才有下一层级的名称
            if (!"SPART".equals(monthAnalysisVO.getGroupLevel())) {
                groupLevelMap = FcstIndustryUtil.getNextGroupLevel(monthAnalysisVO);
            }
        }
        return groupLevelMap;
    }

    @Override
    @JalorOperation(code = "getMultiDropdownList", desc = "定价指数图（多子项）虚化维度多选下拉框")
    public ResultDataVO getMultiDropdownList(PriceMonthAnalysisVO monthAnalysisVO) throws ApplicationException {
        log.info("==>Begin PriceMonthAnalysisService#getMultiDropdownList");
        // 必填字段校验 不走虚化逻辑也无需调用此方法
        if ( !monthAnalysisVO.getIsNeedBlur() || verifyParameters(monthAnalysisVO)) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        // 查询区间年
        List<String> yearList = annualAmpPriceService.getYearList();
        if (CollectionUtils.isNotEmpty(yearList) && yearList.size() >= 2) {
            monthAnalysisVO.setIntervalYear(yearList.get(0));
        }
        monthAnalysisVO.setVersionId(priceCommonService.getVersionId("MONTH"));
        PageVO pageVO = new PageVO();
        pageVO.setCurPage(monthAnalysisVO.getPageIndex());
        pageVO.setPageSize(monthAnalysisVO.getPageSize());
        // 虚化，子层级需要取虚化权重结果表本身层级的数据
        monthAnalysisVO.setSubGroupCodeList(monthAnalysisVO.getGroupCodeList());
        return ResultDataVO.success(priceMonthCostIdxDao.findBlurMultiBoxList(monthAnalysisVO, pageVO));
    }

    @Override
    @JalorOperation(code = "getPriceIndexChart", desc = "定价指数图查询")
    public ResultDataVO getPriceIndexChart(PriceMonthAnalysisVO monthAnalysisVO) throws ApplicationException {
        log.info("==>Begin PriceMonthAnalysisService#getPriceIndexChart");
        // 设置用户权限
        priceDataPermissionService.setUserPermission(monthAnalysisVO);
        // 必填字段校验
        if (verifyParameters(monthAnalysisVO)) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        // 设置版本ID和报告期开始时间与结束时间
        setQueryParams(monthAnalysisVO);
        // 虚化处理
        if (monthAnalysisVO.getIsNeedBlur()) {
            return ResultDataVO.success(priceMonthCostIdxDao.findBlurPriceIndexVOList(monthAnalysisVO));
        }
        return ResultDataVO.success(priceMonthCostIdxDao.findPriceIndexVOList(monthAnalysisVO));
    }

    private void setQueryParams(PriceMonthAnalysisVO monthAnalysisVO) {
        monthAnalysisVO.setVersionId(priceCommonService.getVersionId("MONTH"));
        String actualMonth = priceMonthCostIdxDao.findActualMonth();
        if (StringUtils.isNotBlank(actualMonth)) {
            FcstIndustryUtil.handlePeriod(monthAnalysisVO, actualMonth);
        }
    }

    @Override
    @JalorOperation(code = "getMultiPriceIndexChart", desc = "查询定价指数图（多子项）")
    public ResultDataVO getMultiPriceIndexChart(PriceMonthAnalysisVO monthAnalysisVO) throws ApplicationException {
        log.info("==>Begin PriceMonthAnalysisService#getMultiPriceIndexChart");
        // 设置用户权限
        priceDataPermissionService.setUserPermission(monthAnalysisVO);
        // 必填字段校验
        if (verifyParameters(monthAnalysisVO)) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        // 设置版本ID和报告期开始时间与结束时间
        setQueryParams(monthAnalysisVO);
        // 设置区间年
        List<String> yearList = annualAmpPriceService.getYearList();
        if (CollectionUtils.isNotEmpty(yearList) && yearList.size() >= 2) {
            monthAnalysisVO.setIntervalYear(yearList.get(0));
        }
        List<PriceMonthAnalysisVO> multiPriceIndexVOList = new ArrayList<>();
        // 虚化处理
        if (monthAnalysisVO.getIsNeedBlur()) {
            // 虚化因为是一对多，需要解析subGroupCodeList["parentCode#groupCode"]，如：["PDCG800881#01075111"]
            if (CollectionUtils.isNotEmpty(monthAnalysisVO.getSubGroupCodeList())) {
                List<String> parentCodeList = monthAnalysisVO.getSubGroupCodeList().stream()
                        .map(item -> item.split("#")[0]).collect(Collectors.toList());
                monthAnalysisVO.setParentCodeList(parentCodeList);
            }
            // 查询的是实际指数表与虚化权重表关联出来的数据
            multiPriceIndexVOList.addAll(priceMonthCostIdxDao.findBlurMultiPriceIndexVOList(monthAnalysisVO));
        } else {
            // 正常维度
            monthAnalysisVO.setParentCodeList(monthAnalysisVO.getGroupCodeList());
            monthAnalysisVO.setParentLevel(monthAnalysisVO.getGroupLevel());
            // 非最小层级时才需要设置下一层级
            if (!"SPART".equals(monthAnalysisVO.getGroupLevel())) {
                Map<String, String> groupLevelMap = FcstIndustryUtil.getNextGroupLevel(monthAnalysisVO);
                monthAnalysisVO.setGroupLevel(groupLevelMap.get(NEXT_GROUP_LEVEL));
            }
            // 查询正常指数结果表数据
            multiPriceIndexVOList.addAll(priceMonthCostIdxDao.findMultiPriceIndexVOList(monthAnalysisVO));
        }
        multiPriceIndexVOList = Optional.ofNullable(multiPriceIndexVOList).orElse(new ArrayList<>());
        List<List<PriceMonthAnalysisVO>> dataList = new ArrayList<>();
        // 虚化的时候需要根据groupCode和parentCode分组
        multiPriceIndexVOList.stream().collect(Collectors.groupingBy(item -> getGroupingBy(monthAnalysisVO.getIsNeedBlur(), item)))
                .entrySet().forEach(entry -> dataList.add(entry.getValue()));
        return ResultDataVO.success(dataList);
    }

    private String getGroupingBy(boolean isNeedBlur, PriceMonthAnalysisVO monthAnalysisVO) {
        return isNeedBlur ? monthAnalysisVO.getGroupCode().concat(monthAnalysisVO.getParentCode()) : monthAnalysisVO.getGroupCode();
    }

    @Override
    @JalorOperation(code = "getPriceIndexYoyAndPopChart", desc = "查询定价指数同环比图")
    public ResultDataVO getPriceIndexYoyAndPopChart(PriceMonthAnalysisVO monthAnalysisVO) throws ApplicationException {
        log.info("==>Begin PriceMonthAnalysisService#getPriceIndexYoyAndPopChart");
        // 设置用户权限
        priceDataPermissionService.setUserPermission(monthAnalysisVO);
        // 必填字段校验
        if (verifyParameters(monthAnalysisVO)) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        // 设置版本ID和报告期开始时间与结束时间
        monthAnalysisVO.setVersionId(priceCommonService.getVersionId("MONTH"));
        // 检查基期开始时间和结束时间是否传参，若没有，则默认取报告期24个月的开始时间和结束时间
        String actualMonth = priceMonthCostIdxDao.findActualMonth();
        if (StringUtils.isNotBlank(actualMonth)) {
            FcstIndustryUtil.handlePeriodForMonthYoy(monthAnalysisVO, actualMonth);
        }
        // 虚化场景
        if (monthAnalysisVO.getIsNeedBlur()) {
            return ResultDataVO.success(priceMonthRateDao.findBlurPriceRateVOList(monthAnalysisVO));
        }
        // 正常场景
        return ResultDataVO.success(priceMonthRateDao.findPriceRateVOList(monthAnalysisVO));
    }

    @Override
    @JalorOperation(code = "switchBasePeriodId", desc = "定价指数-综合指数分析-切换基期")
    public ResultDataVO switchBasePeriodId(PriceMonthAnalysisVO monthAnalysisVO) throws ApplicationException {
        log.info("==>Begin PriceMonthAnalysisService#switchBasePeriodId");
        // 设置用户权限
        priceDataPermissionService.setUserPermission(monthAnalysisVO);
        // 必填字段校验
        if (verifyParameters(monthAnalysisVO)) {
            return ResultDataVO.failure(ResultCodeEnum.PARAM_ERROR);
        }
        // 设置版本ID和报告期开始时间与结束时间
        setQueryParams(monthAnalysisVO);
        // 任务状态初始化设置
        DmFocVarifyTaskVO varifyTaskVO = new DmFocVarifyTaskVO();
        varifyTaskVO.setTaskType("price_data_index");
        String success = "SUCCESS";
        // 判断数据是否准备好，准备好了则不调用切换基期函数，否则调用切换基期函数
        if (isDataIsOk(monthAnalysisVO)) {
            varifyTaskVO.setStatus(success);
        } else {
            PriceMonthAnalysisVO noramlAnalysisVO = new PriceMonthAnalysisVO();
            BeanUtils.copyProperties(monthAnalysisVO, noramlAnalysisVO);
            varifyTaskVO.setStatus("PROCESSING");
            refreshDataByFunction(noramlAnalysisVO, varifyTaskVO);
        }
        return ResultDataVO.success(varifyTaskVO);
    }

    /**
     * 调用函数刷新数据
     *
     * @param noramlAnalysisVO 页面参数VO
     * @param varifyTaskVO vo
     */
    private void refreshDataByFunction(PriceMonthAnalysisVO noramlAnalysisVO, DmFocVarifyTaskVO varifyTaskVO) {
        log.info("==>Begin PriceMonthAnalysisService#refreshDataByFunction");
        if (null == varifyTaskVO.getTaskId()) {
            varifyTaskVO.setTaskId(priceVarifyTaskDao.getVerifyTaskId());
            varifyTaskVO.setPeriodId(noramlAnalysisVO.getBasePeriodId());
            priceVarifyTaskDao.insertVerifyTask(varifyTaskVO);
        } else {
            priceVarifyTaskDao.updateVerifyTask(varifyTaskVO);
        }
        // 异步调用函数
        asyncPriceMonthService.refreshDataByFunction(noramlAnalysisVO, varifyTaskVO);
    }

    /**
     * 判断数据是否准备好
     *
     * @param monthAnalysisVO 查询参数
     * @return boolean true or false
     */
    public boolean isDataIsOk(PriceMonthAnalysisVO monthAnalysisVO) {
        // 默认基期不切换
        if (ifBasePeriodId(monthAnalysisVO)) {
            return true;
        }
        // 单选的切换基期数据量设置
        int monthCount = getMonthCount(monthAnalysisVO);
        // 多选的切换基期数据量设置
        if (CollectionUtils.isEmpty(monthAnalysisVO.getSubGroupCodeList())) {
            if (monthAnalysisVO.getIsMultipleSelect() && CollectionUtils.isNotEmpty(monthAnalysisVO.getGroupCodeList())) {
                monthCount = monthCount * (monthAnalysisVO.getGroupCodeList().size());
            }
        }
        int priceIndexCount = monthAnalysisVO.getIsNeedBlur()
                ? priceMonthCostIdxDao.findBlurIndexCount(monthAnalysisVO)
                : priceMonthCostIdxDao.findCostIndexCount(monthAnalysisVO);
        // 多子项的数据量检查
        int multiCostIndexCount = getMultiCostIndexCount(monthAnalysisVO);
        if (priceIndexCount == 0 || multiCostIndexCount == 0) {
            return false;
        }
        return priceIndexCount >= monthCount;
    }

    private int getMultiCostIndexCount (PriceMonthAnalysisVO monthAnalysisVO) {
        PriceMonthAnalysisVO paramsVO = ObjectCopyUtil.copy(monthAnalysisVO, PriceMonthAnalysisVO.class);
        paramsVO.setParentCodeList(paramsVO.getGroupCodeList());
        paramsVO.setParentLevel(paramsVO.getGroupLevel());
        // 查询正常指数结果表数据
        return paramsVO.getIsNeedBlur()
                ? priceMonthCostIdxDao.findBlurMultiIndexCount(paramsVO)
                : priceMonthCostIdxDao.findMultiCostIndexCount(paramsVO);
    }

    private int getMonthCount(PriceMonthAnalysisVO monthAnalysisVO) {
        int result = 0;
        SimpleDateFormat format = new SimpleDateFormat("yyyyMM");
        Calendar from = Calendar.getInstance();
        Calendar to = Calendar.getInstance();
        try {
            from.setTime(format.parse(String.valueOf(monthAnalysisVO.getPeriodStartTime())));
            to.setTime(format.parse(String.valueOf(monthAnalysisVO.getPeriodEndTime())));
            int fromYear = from.get(Calendar.YEAR);
            int toYear = to.get(Calendar.YEAR);
            int fromMonth = from.get(Calendar.MONTH);
            int toMonth = to.get(Calendar.MONTH);
            result = (toYear - fromYear) * 12 + toMonth - fromMonth + 1 ;
        } catch (ParseException ex) {
            log.error(">>>An exception occurred when invoking the getMonthCount method.{} ", ex.getLocalizedMessage());
        }
        return result;
    }

    private boolean ifBasePeriodId(PriceMonthAnalysisVO monthAnalysisVO) {
        String basePeriodId = priceMonthCostIdxDao.findBasePeriodId(monthAnalysisVO);
        return basePeriodId.equals(String.valueOf(monthAnalysisVO.getBasePeriodId()));
    }

    @Override
    @JalorOperation(code = "detailDataExport", desc = "定价指数-综合指数分析-数据下载")
    @Audit(module = "priceMonthAnalysisService-detailDataExport", operation = "detailDataExport", message = "定价指数-综合指数分析-数据下载")
    public ResultDataVO detailDataExport(PriceMonthAnalysisVO monthAnalysisVO, HttpServletResponse response) throws ApplicationException {
        log.info("==>Begin PriceMonthAnalysisService#detailDataExport");
        // 设置用户权限
        priceDataPermissionService.setUserPermission(monthAnalysisVO);
        // 获取导出模板
        IExcelTemplateBeanManager monthAnalysisTemplate = getMonthAnalysisTemplateEnum(monthAnalysisVO);
        // 设置标题
        priceCommonService.setTitleDisplayName(monthAnalysisVO);
        // 设置区域维度或系统部维度名称
        setRegOrDtDimCnName(monthAnalysisVO);
        // 设置子层级中文名称
        setSubCnName(monthAnalysisVO);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("exportFileName", "综合指数分析-明细数据_" + System.currentTimeMillis());
        paramMap.put("exportModuleName", "价格指数-产业-ICT-综合指数分析");
        exportProcessorService.fillEasyExcelExport(response, monthAnalysisTemplate, monthAnalysisVO, paramMap);
        return ResultDataVO.success();
    }

    private void setSubCnName(PriceMonthAnalysisVO monthAnalysisVO) {
        Map<String, String> groupLevelMap = getGroupLevelMap(monthAnalysisVO);
        monthAnalysisVO.setGroupCnName(GroupLevelEnum.getInstance(monthAnalysisVO.getGroupLevel()).getName());
        monthAnalysisVO.setNextGroupLevel(groupLevelMap.get(NEXT_GROUP_LEVEL));
        if (GroupLevelEnum.SPART.getValue().equals(monthAnalysisVO.getNextGroupLevel())) {
            monthAnalysisVO.setSubCnName(groupLevelMap.get(NEXT_GROUP_NAME) + "编码");
        } else {
            monthAnalysisVO.setSubCnName(groupLevelMap.get(NEXT_GROUP_NAME) + "名称");
        }
    }

    private void setRegOrDtDimCnName(PriceMonthAnalysisVO monthAnalysisVO) {
        if (StringUtils.isAnyBlank(monthAnalysisVO.getSignTopCustCategoryCode(),
                monthAnalysisVO.getSignSubsidiaryCustcatgCnName())) {
            // 区域维度
            monthAnalysisVO.setOverseaFlagCnName(MessageFormat
                    .format(OVERSEA_FLAG, "国内/海外", monthAnalysisVO.getOverseaFlagCnName()));
            monthAnalysisVO.setRegOrDtCnName(MessageFormat
                    .format(REG_OR_DT, "地区部", monthAnalysisVO.getRegionCnName()));
            monthAnalysisVO.setRepOrSnCnName(MessageFormat
                    .format(REP_OR_SN, "代表处", monthAnalysisVO.getRepofficeCnName()));
        } else {
            // 系统部维度
            monthAnalysisVO.setRegOrDtCnName(MessageFormat
                    .format(REG_OR_DT, "大T系统部", monthAnalysisVO.getSignTopCustCategoryCnName()));
            monthAnalysisVO.setRepOrSnCnName(MessageFormat
                    .format(REP_OR_SN, "子网系统", monthAnalysisVO.getSignSubsidiaryCustcatgCnName()));
        }
    }

    @NotNull
    private IExcelTemplateBeanManager getMonthAnalysisTemplateEnum(PriceMonthAnalysisVO monthAnalysisVO) {
        if (monthAnalysisVO.getIsMultipleSelect()) {
            return GroupLevelEnum.SPART.getValue().equals(monthAnalysisVO.getGroupLevel()) && !monthAnalysisVO.getIsNeedBlur()
                    ? MonthAnalysisTemplateEnum.MONTH_CODE_04
                    : MonthAnalysisTemplateEnum.MONTH_CODE_02;
        }
        return GroupLevelEnum.SPART.getValue().equals(monthAnalysisVO.getGroupLevel()) && !monthAnalysisVO.getIsNeedBlur()
                ? MonthAnalysisTemplateEnum.MONTH_CODE_03
                : MonthAnalysisTemplateEnum.MONTH_CODE_01;
    }

    @Override
    public Map<String, Object> getHeaderInfo(PriceMonthAnalysisVO monthAnalysisVO) {
        // 自定义表头
        Map<String,Object> headMap = new HashMap<>();
        headMap.put("displayName", monthAnalysisVO.getDisplayName());
        headMap.put("subCnName", monthAnalysisVO.getSubCnName());
        headMap.put("granularityTypeCnName", monthAnalysisVO.getGranularityTypeCnName());
        headMap.put("bgCnName", monthAnalysisVO.getBgCnName());
        headMap.put("overseaFlagCnName", monthAnalysisVO.getOverseaFlagCnName());
        headMap.put("regOrDtCnName", monthAnalysisVO.getRegOrDtCnName());
        headMap.put("repOrSnCnName", monthAnalysisVO.getRepOrSnCnName());
        headMap.put("groupCnName", monthAnalysisVO.getGroupCnName());
        headMap.put("basePeriodId", FcstIndustryUtil.getActualMonth(monthAnalysisVO.getBasePeriodId().toString()));
        headMap.put("actualMonth", FcstIndustryUtil.getActualMonth(priceMonthCostIdxDao.findActualMonth()));
        return headMap;
    }
}