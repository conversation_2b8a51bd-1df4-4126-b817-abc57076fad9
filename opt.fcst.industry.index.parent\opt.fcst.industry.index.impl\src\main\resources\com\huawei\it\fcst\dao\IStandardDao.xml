<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.it.fcst.industry.index.dao.IStandardDao">
    <resultMap type="com.huawei.it.fcst.industry.index.vo.annual.DmFocAnnualAmpVO" id="annualResultMap">
        <result property="id" column="id"/>
        <result property="versionId" column="version_id"/>
        <result property="periodYear" column="period_year"/>
        <result property="prodRndTeamCode" column="prod_rnd_team_code"/>
        <result property="prodRndTeamCnName" column="prod_rnd_team_cn_name"/>
        <result property="lv0ProdRndTeamCnName" column="lv0_prod_rd_team_cn_name"/>
        <result property="lv0ProdRndTeamCode" column="lv0_prod_rnd_team_code"/>
        <result property="lv1ProdRndTeamCnName" column="lv1_prod_rd_team_cn_name"/>
        <result property="lv1ProdRndTeamCode" column="lv1_prod_rnd_team_code"/>
        <result property="lv2ProdRndTeamCnName" column="lv2_prod_rd_team_cn_name"/>
        <result property="lv2ProdRndTeamCode" column="lv2_prod_rnd_team_code"/>
        <result property="lv3ProdRndTeamCnName" column="lv3_prod_rd_team_cn_name"/>
        <result property="lv3ProdRndTeamCode" column="lv3_prod_rnd_team_code"/>
        <result property="lv4ProdRndTeamCode" column="lv4_prod_rnd_team_code"/>
        <result property="lv4ProdRndTeamCnName" column="lv4_prod_rd_team_cn_name"/>
        <result property="parentLevel" column="parent_level"/>
        <result property="parentCode" column="parent_code"/>
        <result property="parentCnName" column="parent_cn_name"/>
        <result property="groupCode" column="group_code"/>
        <result property="groupCnName" column="group_cn_name"/>
        <result property="groupLevel" column="group_level"/>
        <result property="costType" column="cost_type"/>
        <result property="annualAmp" column="annual_amp"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="appendFlag" column="append_flag"/>
        <result property="weightRate" column="weight_rate"/>
        <result property="statusCode" column="status_code"/>
        <result property="rmbCostAmt" column="rmb_cost_amt"/>
        <result property="rmbCostPer" column="rmb_cost_per"/>
        <result property="groupCodeAndName" column="groupCodeAndName"/>
        <result property="weightAnnualAmpPercent" column="weightAnnualAmpPercent"/>
        <result property="appendYear" column="append_year"/>
        <result property="periodId" column="period_id"/>
        <result property="costIndex" column="cost_index"/>
    </resultMap>

    <sql id="allField">
        distinct
        parent_code,
        parent_cn_name,
        group_cn_name,
        group_level,
        group_code,
        period_year,
        period_id,
        cost_index*100 AS cost_index,
        version_id,
        last_update_date,
        #{costType} as cost_type
    </sql>

    <select id="findCurrentSameAmpCost" resultMap="annualResultMap">
        select distinct
        amp.group_cn_name,amp.period_year,amp.group_level,amp.group_code,amp.parent_code, amp.parent_cn_name,
        CONCAT(ROUND(amp.annual_amp*100,1),'%') as annual_amp,'SAME' as cost_type,
        status.status_code,status.append_year
        from fin_dm_opt_foi.dm_foc_total_annual_amp_t amp
        left join fin_dm_opt_foi.dm_foc_total_annual_status_code_t status
        on amp.group_code = status.group_code and amp.group_level = status.group_level
        and amp.period_year = status.period_year and amp.version_id = status.version_id
        and amp.view_flag = status.view_flag
        and amp.caliber_flag = status.caliber_flag and amp.parent_code = status.parent_code
        and amp.OVERSEA_FLAG = status.OVERSEA_FLAG
        and amp.LV0_PROD_LIST_CODE = status.LV0_PROD_LIST_CODE
        where amp.del_flag = 'N' and amp.OVERSEA_FLAG = 'G' and amp.LV0_PROD_LIST_CODE = 'GR'
        <if test='caliberFlag != null and caliberFlag != ""'>
            and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and amp.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and amp.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='periodYear != null and periodYear != ""'>
            and amp.period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and amp.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='groupCodeList != null and groupCodeList.size() > 0'>
            <foreach collection='groupCodeList' item="code" open="AND amp.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND amp.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        <include refid="standardUserPermission"></include>
        order by amp.period_year
    </select>

    <sql id = "standardUserPermission">
        <if test='groupLevel =="LV0" and lv0DimensionSet != null and lv0DimensionSet.size() > 0'>
            <foreach collection='lv0DimensionSet' item="code" open="AND amp.group_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel =="LV1" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND amp.group_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel =="LV2" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND amp.group_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
    </sql>

    <select id="findStandardGroupCodeOrderByWeight" resultMap="annualResultMap">
        select weight.group_code, weight.group_cn_name,
        <if test='costType =="STD"'>
            ROUND( SUM(rmb_cost_per * 100), 1 ) weight_rate
            from fin_dm_opt_foi.DM_FOC_REPL_STANDARD_ANNL_AMP_T amp
            LEFT JOIN fin_dm_opt_foi.DM_FOC_REPL_ANNL_COST_PER_T weight
            ON amp.group_code = weight.group_code
            AND amp.group_level = weight.group_level
            AND amp.version_id = weight.version_id
            AND amp.parent_code = weight.parent_code
            AND amp.version_id = weight.version_id
            AND amp.period_year = weight.period_year
            AND amp.view_flag = weight.view_flag
            AND amp.caliber_flag = weight.caliber_flag
            AND amp.data_type = 'TOTAL'
        </if>
        <if test='costType =="SAME"'>
            ROUND( SUM(weight_rate * 100), 1 ) weight_rate
            from fin_dm_opt_foi.dm_foc_total_annual_weight_t weight
        </if>
        <if test='costType =="REPLACE"'>
            ROUND( SUM(weight_rate * 100), 1 ) weight_rate
            from fin_dm_opt_foi.DM_FOC_REPL_ANNL_WEIGHT_T weight
        </if>
        where weight.del_flag = 'N'
        <if test='costType =="SAME"'>
            and weight.OVERSEA_FLAG = 'G' and weight.LV0_PROD_LIST_CODE = 'GR'
        </if>
        <if test='caliberFlag != null and caliberFlag != ""'>
            and weight.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and weight.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and weight.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and weight.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='year != null and year !=""'>
            and weight.period_year = #{year,jdbcType=VARCHAR}
        </if>
        <if test='parentCodeList != null and parentCodeList.size() > 0'>
            <foreach collection='parentCodeList' item="code" open="AND weight.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel =="LV0" and lv0DimensionSet != null and lv0DimensionSet.size() > 0'>
            <foreach collection='lv0DimensionSet' item="code" open="AND weight.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel =="LV1" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND weight.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel =="LV2" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND weight.prod_rnd_team_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        group by weight.group_code, weight.group_cn_name
        ORDER BY weight_rate DESC,weight.group_cn_name
    </select>

    <select id="multiStdAmpCostChart" resultMap="annualResultMap">
        select amp.parent_code,amp.parent_cn_name,
        amp.period_year, 'STD' as cost_type,
        CONCAT(ROUND( SUM ( amp.annual_amp*100 ), 1 ),'%') AS annual_amp,
        amp.group_code, amp.group_cn_name, amp.group_level,
        CONCAT(ROUND(SUM ( cost.rmb_cost_per*100 ),1),'%') AS weight_rate,
        CONCAT (amp.group_code, ' ', amp.group_cn_name ) AS groupCodeAndName,
        status.status_code, max(status.append_year) AS append_year
        from fin_dm_opt_foi.DM_FOC_REPL_STANDARD_ANNL_AMP_T amp
        left join fin_dm_opt_foi.DM_FOC_REPL_ANNL_COST_PER_T cost
        on amp.group_code = cost.group_code
        and amp.group_level = cost.group_level
        and amp.version_id = cost.version_id
        and amp.parent_code = cost.parent_code
        and amp.period_year = cost.period_year
        and amp.view_flag = cost.view_flag
        and amp.caliber_flag = cost.caliber_flag
        left join fin_dm_opt_foi.DM_FOC_REPL_STANDARD_ANNL_STATUS_T status
        on amp.group_code = status.group_code and amp.group_level = status.group_level
        and amp.period_year = status.period_year and amp.version_id = status.version_id
        and amp.parent_code = status.parent_code
        and amp.view_flag = status.view_flag
        and amp.caliber_flag = status.caliber_flag
        and amp.data_type = status.data_type
        where amp.del_flag = 'N' and amp.data_type ='TOTAL'
        <if test='caliberFlag != null and caliberFlag != ""'>
            and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and amp.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and amp.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='periodYear != null and periodYear != ""'>
            and amp.period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and amp.version_id = #{versionId}
        </if>
        <if test='parentCodeList != null and parentCodeList.size() > 0'>
            <foreach collection='parentCodeList' item="code" open="AND amp.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND amp.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        <if test='groupCodeOrder != null and groupCodeOrder != ""'>
            <foreach collection="groupCodeOrder.split(',')" item="item" open="and amp.group_code IN (" close=")"
                     index="index" separator=",">
                #{item}
            </foreach>
        </if>
        <include refid="standardUserPermission"></include>
        GROUP BY amp.parent_code,amp.parent_cn_name,
        amp.group_level,amp.group_code,amp.group_cn_name,groupCodeAndName,amp.period_year,status.status_code
        order by locate(amp.group_code, #{groupCodeOrder}), weight_rate desc,amp.parent_cn_name,amp.group_cn_name,amp.period_year
    </select>

    <select id="multiSameAmpCostChart" resultMap="annualResultMap">
        select amp.parent_code,amp.parent_cn_name,
        amp.period_year, 'SAME' as cost_type,
        CONCAT(ROUND( SUM ( amp.annual_amp*100 ), 1 ),'%') AS annual_amp,
        amp.group_code, amp.group_cn_name, amp.group_level,
        <if test='isMultipleSelect == false'>
            CONCAT(ROUND(SUM ( weight.weight_rate*100 ),1),'%') AS weight_rate,
        </if>
        <if test='isMultipleSelect == true'>
            CONCAT(ROUND(SUM ( weight.absolute_weight*100 ),1),'%') AS weight_rate,
        </if>
        CONCAT (amp.group_code, ' ', amp.group_cn_name ) AS groupCodeAndName,
        status.status_code,weight.append_flag, max(status.append_year) AS append_year
        from fin_dm_opt_foi.dm_foc_total_annual_amp_t amp
        left join fin_dm_opt_foi.dm_foc_total_annual_weight_t weight
        on amp.group_code = weight.group_code
        and amp.group_level = weight.group_level
        and amp.version_id = weight.version_id
        and amp.parent_code = weight.parent_code
        and amp.period_year = weight.period_year
        and amp.view_flag = weight.view_flag
        and amp.caliber_flag = weight.caliber_flag
        and amp.OVERSEA_FLAG = weight.OVERSEA_FLAG
        and amp.LV0_PROD_LIST_CODE = weight.LV0_PROD_LIST_CODE
        and weight.cost_type ='T'
        left join fin_dm_opt_foi.dm_foc_total_annual_status_code_t status
        on amp.group_code = status.group_code and amp.group_level = status.group_level
        and amp.period_year = status.period_year and amp.version_id = status.version_id
        and amp.parent_code = status.parent_code
        and amp.view_flag = status.view_flag
        and amp.caliber_flag = status.caliber_flag
        and amp.OVERSEA_FLAG = status.OVERSEA_FLAG
        and amp.LV0_PROD_LIST_CODE = status.LV0_PROD_LIST_CODE
        where amp.del_flag = 'N' and amp.OVERSEA_FLAG = 'G' and amp.LV0_PROD_LIST_CODE = 'GR'
        <if test='caliberFlag != null and caliberFlag != ""'>
            and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and amp.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and amp.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='periodYear != null and periodYear != ""'>
            and amp.period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and amp.version_id = #{versionId}
        </if>
        <if test='parentCodeList != null and parentCodeList.size() > 0'>
            <foreach collection='parentCodeList' item="code" open="AND amp.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND amp.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        <if test='groupCodeOrder != null and groupCodeOrder != ""'>
            <foreach collection="groupCodeOrder.split(',')" item="item" open="and amp.group_code IN (" close=")"
                     index="index" separator=",">
                #{item}
            </foreach>
        </if>
        <include refid="standardUserPermission"></include>
        GROUP BY amp.parent_code,amp.parent_cn_name,weight.append_flag,
        amp.group_level,amp.group_code,amp.group_cn_name,groupCodeAndName,amp.period_year,status.status_code
        order by locate(amp.group_code, #{groupCodeOrder}), weight_rate desc,amp.parent_cn_name,amp.group_cn_name,amp.period_year
    </select>

    <select id="multiReplaceAmpCostChart" resultMap="annualResultMap">
        select amp.parent_code,amp.parent_cn_name,
        amp.period_year, 'REPLACE' as cost_type,
        CONCAT(ROUND( SUM ( amp.annual_amp*100 ), 1 ),'%') AS annual_amp,
        amp.group_code, amp.group_cn_name, amp.group_level,
        <if test='isMultipleSelect == false'>
            CONCAT(ROUND(SUM ( weight.weight_rate*100 ),1),'%') AS weight_rate,
        </if>
        <if test='isMultipleSelect == true'>
            CONCAT(ROUND(SUM ( weight.absolute_weight*100 ),1),'%') AS weight_rate,
        </if>
        CONCAT (amp.group_code, ' ', amp.group_cn_name ) AS groupCodeAndName,
        status.status_code,weight.append_flag, max(status.append_year) AS append_year
        from fin_dm_opt_foi.DM_FOC_REPL_ANNL_AMP_T amp
        left join fin_dm_opt_foi.DM_FOC_REPL_ANNL_WEIGHT_T weight
        on amp.group_code = weight.group_code
        and amp.group_level = weight.group_level
        and amp.version_id = weight.version_id
        and amp.parent_code = weight.parent_code
        and amp.period_year = weight.period_year
        and amp.view_flag = weight.view_flag
        and amp.caliber_flag = weight.caliber_flag
        left join fin_dm_opt_foi.DM_FOC_REPL_ANNL_STATUS_T status
        on amp.group_code = status.group_code and amp.group_level = status.group_level
        and amp.period_year = status.period_year and amp.version_id = status.version_id
        and amp.parent_code = status.parent_code
        and amp.view_flag = status.view_flag
        and amp.caliber_flag = status.caliber_flag
        and amp.data_type = status.data_type
        where amp.del_flag = 'N' and amp.data_type = 'TOTAL'
        <if test='caliberFlag != null and caliberFlag != ""'>
            and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and amp.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and amp.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='periodYear != null and periodYear != ""'>
            and amp.period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and amp.version_id = #{versionId}
        </if>
        <if test='parentCodeList != null and parentCodeList.size() > 0'>
            <foreach collection='parentCodeList' item="code" open="AND amp.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND amp.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        <if test='groupCodeOrder != null and groupCodeOrder != ""'>
            <foreach collection="groupCodeOrder.split(',')" item="item" open="and amp.group_code IN (" close=")"
                     index="index" separator=",">
                #{item}
            </foreach>
        </if>
        <include refid="standardUserPermission"></include>
        GROUP BY amp.parent_code,amp.parent_cn_name,weight.append_flag,
        amp.group_level,amp.group_code,amp.group_cn_name,groupCodeAndName,amp.period_year,status.status_code
        order by locate(amp.group_code, #{groupCodeOrder}), weight_rate desc,amp.parent_cn_name,amp.group_cn_name,amp.period_year
    </select>

    <select id="industryStdAmpCostList" resultMap="annualResultMap">
        select parent_code,parent_cn_name,period_year,annual_amp,group_code,group_level,group_cn_name,
        weight_rate, status_code, annual_amp * weight_rate/100 as weightAnnualAmpPercent, append_year,
        'STD' as cost_type from
        (select amp.group_level,
        amp.parent_code,amp.parent_cn_name,amp.period_year,
        ROUND(SUM(amp.annual_amp*100),1) as annual_amp,amp.group_code,
        amp.group_cn_name,
        ROUND(SUM(cost.rmb_cost_per*100),1) as weight_rate,
        status.status_code,status.append_year
        from fin_dm_opt_foi.DM_FOC_REPL_STANDARD_ANNL_AMP_T amp
        left join fin_dm_opt_foi.DM_FOC_REPL_ANNL_COST_PER_T cost
        on amp.group_code = cost.group_code
        and amp.group_level = cost.group_level
        and amp.parent_code = cost.parent_code
        and amp.version_id =cost.version_id and amp.period_year = cost.period_year
        and amp.view_flag = cost.view_flag
        and amp.caliber_flag = cost.caliber_flag
        left join fin_dm_opt_foi.DM_FOC_REPL_STANDARD_ANNL_STATUS_T status
        on amp.group_code = status.group_code
        and amp.group_level = status.group_level
        and amp.parent_code = status.parent_code
        and amp.version_id = status.version_id and amp.period_year = status.period_year
        and amp.view_flag = status.view_flag
        and amp.caliber_flag = status.caliber_flag
        and amp.data_type = status.data_type
        where amp.del_flag = 'N'
        <if test='caliberFlag != null and caliberFlag != ""'>
            and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and amp.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and amp.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and amp.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='dataType != null and dataType != ""'>
            and amp.data_type = #{dataType,jdbcType=VARCHAR}
        </if>
        <if test='periodYear != null and periodYear !=""'>
            and amp.period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='year != null and year !=""'>
            and amp.period_year = #{year,jdbcType=VARCHAR}
        </if>
        <if test='parentCodeList != null and parentCodeList.size() > 0'>
            <foreach collection='parentCodeList' item="code" open="AND amp.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND amp.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        <include refid="standardUserPermission"></include>
        group by
        amp.group_level,
        amp.parent_code,amp.parent_cn_name,amp.period_year,
        amp.group_code,
        amp.group_cn_name,
        status.status_code,
        status.append_year
        )
        order by period_year,weight_rate desc,group_cn_name
    </select>

    <select id="industrySameAmpCostList" resultMap="annualResultMap">
        select parent_code,parent_cn_name,period_year,annual_amp,group_code,group_level,group_cn_name,
        weight_rate, status_code, annual_amp * weight_rate/100 as weightAnnualAmpPercent, append_year,
        append_flag,'SAME' as cost_type from
        (select amp.group_level,
        amp.parent_code,amp.parent_cn_name,amp.period_year,
        ROUND(SUM (amp.annual_amp*100),1) as annual_amp,amp.group_code,
        amp.group_cn_name,
        <if test='isMultipleSelect == false'>
            ROUND(SUM ( weight.weight_rate*100 ),1) AS weight_rate,
        </if>
        <if test='isMultipleSelect == true'>
            ROUND(SUM ( weight.absolute_weight*100 ),1) AS weight_rate,
        </if>
        status.status_code,weight.append_flag, status.append_year
        <if test='dataType == "TOTAL"'>
            from fin_dm_opt_foi.dm_foc_total_annual_amp_t amp
        </if>
        <if test='dataType == "YTD"'>
            from fin_dm_opt_foi.DM_FOC_REPL_SAME_TOTAL_YTD_AMP_T amp
        </if>
        left join fin_dm_opt_foi.dm_foc_total_annual_weight_t weight
        on amp.group_code = weight.group_code
        and amp.group_level = weight.group_level
        and amp.parent_code = weight.parent_code
        and amp.version_id =weight.version_id and amp.period_year = weight.period_year
        and amp.view_flag = weight.view_flag
        and amp.caliber_flag = weight.caliber_flag
        and weight.cost_type = 'T'
        <if test='dataType == "TOTAL"'>
            and amp.OVERSEA_FLAG = weight.OVERSEA_FLAG
            and amp.LV0_PROD_LIST_CODE = weight.LV0_PROD_LIST_CODE
        </if>
        <if test='dataType == "YTD"'>
            and weight.OVERSEA_FLAG = 'G'
            and weight.LV0_PROD_LIST_CODE ='GR'
        </if>
        <if test='dataType == "TOTAL"'>
            left join fin_dm_opt_foi.dm_foc_total_annual_status_code_t status
        </if>
        <if test='dataType == "YTD"'>
            left join fin_dm_opt_foi.DM_FOC_REPL_SAME_TOTAL_YTD_STATUS_T status
        </if>
        on amp.group_code = status.group_code
        and amp.group_level = status.group_level
        and amp.parent_code = status.parent_code
        and amp.version_id = status.version_id and amp.period_year = status.period_year
        and amp.view_flag = status.view_flag
        and amp.caliber_flag = status.caliber_flag
        <if test='dataType == "TOTAL"'>
            and amp.OVERSEA_FLAG = status.OVERSEA_FLAG
            and amp.LV0_PROD_LIST_CODE = status.LV0_PROD_LIST_CODE
        </if>
        where amp.del_flag = 'N'
        <if test='dataType == "TOTAL"'>
            and amp.OVERSEA_FLAG = 'G' and amp.LV0_PROD_LIST_CODE = 'GR'
        </if>
        <if test='caliberFlag != null and caliberFlag != ""'>
            and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and amp.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and amp.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and amp.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='periodYear != null and periodYear !=""'>
            and amp.period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='year != null and year !=""'>
            and amp.period_year = #{year,jdbcType=VARCHAR}
        </if>
        <if test='parentCodeList != null and parentCodeList.size() > 0'>
            <foreach collection='parentCodeList' item="code" open="AND amp.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND amp.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        <include refid="standardUserPermission"></include>
        group by
        amp.group_level,
        amp.parent_code,
        amp.parent_cn_name,
        amp.period_year,
        amp.group_code,
        amp.group_cn_name,
        status.status_code,
        weight.append_flag,
        status.append_year
        )
        order by period_year,weight_rate desc,group_cn_name
    </select>

    <select id="industryReplaceAmpCostList" resultMap="annualResultMap">
        select parent_code,parent_cn_name,period_year,annual_amp,group_code,group_level,group_cn_name,
        weight_rate, status_code, annual_amp * weight_rate/100 as weightAnnualAmpPercent, append_year,
        append_flag,'REPLACE' as cost_type from
        (select amp.group_level,
        amp.parent_code,amp.parent_cn_name,amp.period_year,
        ROUND(SUM(amp.annual_amp * 100), 1 ) AS annual_amp,amp.group_code,
        amp.group_cn_name,
        <if test='isMultipleSelect == false'>
            ROUND(SUM ( weight.weight_rate*100 ),1) AS weight_rate,
        </if>
        <if test='isMultipleSelect == true'>
            ROUND(SUM ( weight.absolute_weight*100 ),1) AS weight_rate,
        </if>
        status.status_code,weight.append_flag, status.append_year
        from fin_dm_opt_foi.DM_FOC_REPL_ANNL_AMP_T amp
        left join fin_dm_opt_foi.DM_FOC_REPL_ANNL_WEIGHT_T weight
        on amp.group_code = weight.group_code
        and amp.group_level = weight.group_level
        and amp.parent_code = weight.parent_code
        and amp.version_id =weight.version_id and amp.period_year = weight.period_year
        and amp.view_flag = weight.view_flag
        and amp.caliber_flag = weight.caliber_flag
        left join fin_dm_opt_foi.DM_FOC_REPL_ANNL_STATUS_T status
        on amp.group_code = status.group_code
        and amp.group_level = status.group_level
        and amp.parent_code = status.parent_code
        and amp.version_id = status.version_id and amp.period_year = status.period_year
        and amp.view_flag = status.view_flag
        and amp.caliber_flag = status.caliber_flag
        and amp.data_type = status.data_type
        where amp.del_flag = 'N'
        <if test='caliberFlag != null and caliberFlag != ""'>
            and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and amp.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and amp.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and amp.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='dataType != null and dataType !=""'>
            and amp.data_type = #{dataType,jdbcType=VARCHAR}
        </if>
        <if test='periodYear != null and periodYear !=""'>
            and amp.period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='year != null and year !=""'>
            and amp.period_year = #{year,jdbcType=VARCHAR}
        </if>
        <if test='parentCodeList != null and parentCodeList.size() > 0'>
            <foreach collection='parentCodeList' item="code" open="AND amp.parent_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND amp.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        <include refid="standardUserPermission"></include>
        group by
        amp.group_level,
        amp.parent_code,
        amp.parent_cn_name,
        amp.period_year,
        amp.group_code,
        amp.group_cn_name,
        status.status_code,
        weight.append_flag,
        status.append_year
        )
        order by period_year,weight_rate desc,group_cn_name
    </select>

    <select id="distributeAmpCostChart" resultMap="annualResultMap">
        select distinct
        group_level,group_code,group_cn_name,parent_code,parent_cn_name,
        period_year,CONCAT(ROUND(rmb_cost_per*100,1),'%') as rmb_cost_per
        from fin_dm_opt_foi.DM_FOC_REPL_ANNL_COST_PER_T
        where del_flag = 'N'
        <if test='caliberFlag != null and caliberFlag != ""'>
            and caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='periodYear != null and periodYear != ""'>
            and period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='groupCodeList != null and groupCodeList.size() > 0'>
            <foreach collection='groupCodeList' item="code" open="AND group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        <if test='groupLevel =="LV0" and lv0DimensionSet != null and lv0DimensionSet.size() > 0'>
            <foreach collection='lv0DimensionSet' item="code" open="AND group_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel =="LV1" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND group_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel =="LV2" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND group_code IN (" close=")"
                     index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        order by period_year
    </select>

    <select id="findCurrentStdAmpCost" resultMap="annualResultMap">
        select distinct amp.group_level,amp.parent_code, amp.parent_cn_name,
        amp.group_cn_name,amp.period_year,amp.group_level,amp.group_code,
        CONCAT(ROUND(amp.annual_amp*100,1),'%') as annual_amp, 'STD' as cost_type,
        status.status_code,status.append_year
        from fin_dm_opt_foi.DM_FOC_REPL_STANDARD_ANNL_AMP_T amp left join
        fin_dm_opt_foi.DM_FOC_REPL_STANDARD_ANNL_STATUS_T status
        on amp.group_code = status.group_code and amp.group_level = status.group_level
        and amp.period_year = status.period_year and amp.version_id = status.version_id
        and amp.view_flag = status.view_flag
        and amp.caliber_flag = status.caliber_flag and amp.parent_code = status.parent_code
        and amp.data_type = status.data_type
        where amp.del_flag = 'N' and amp.data_type= 'TOTAL'
        <if test='caliberFlag != null and caliberFlag != ""'>
            and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and amp.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and amp.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='periodYear != null and periodYear != ""'>
            and amp.period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and amp.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='groupCodeList != null and groupCodeList.size() > 0'>
            <foreach collection='groupCodeList' item="code" open="AND amp.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND amp.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        <include refid="standardUserPermission"></include>
        order by amp.period_year
    </select>

    <select id="findCurrentReplaceAmpCost" resultMap="annualResultMap">
        select distinct amp.group_level,amp.parent_code, amp.parent_cn_name,
        amp.group_cn_name,amp.period_year,amp.group_level,amp.group_code,
        CONCAT(ROUND(amp.annual_amp*100,1),'%') as annual_amp, 'REPLACE' as cost_type,
        status.status_code,status.append_year
        from fin_dm_opt_foi.DM_FOC_REPL_ANNL_AMP_T amp left join
        fin_dm_opt_foi.DM_FOC_REPL_ANNL_STATUS_T status
        on amp.group_code = status.group_code and amp.group_level = status.group_level
        and amp.period_year = status.period_year and amp.version_id = status.version_id
        and amp.view_flag = status.view_flag
        and amp.caliber_flag = status.caliber_flag and amp.parent_code = status.parent_code
        and amp.data_type = status.data_type
        where amp.del_flag = 'N' and amp.data_type= 'TOTAL'
        <if test='caliberFlag != null and caliberFlag != ""'>
            and amp.caliber_flag = #{caliberFlag,jdbcType=VARCHAR}
        </if>
        <if test='viewFlag != null and viewFlag != ""'>
            and amp.view_flag = #{viewFlag,jdbcType=VARCHAR}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and amp.group_level = #{groupLevel,jdbcType=VARCHAR}
        </if>
        <if test='periodYear != null and periodYear != ""'>
            and amp.period_year = #{periodYear,jdbcType=VARCHAR}
        </if>
        <if test='versionId != null'>
            and amp.version_id = #{versionId,jdbcType=NUMERIC}
        </if>
        <if test='groupCodeList != null and groupCodeList.size() > 0'>
            <foreach collection='groupCodeList' item="code" open="AND amp.group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='yearList != null and yearList.size() > 0'>
            <foreach collection='yearList' item="year" open="AND amp.period_year IN (" close=")"
                     index="index" separator=",">
                #{year}
            </foreach>
        </if>
        <include refid="standardUserPermission"></include>
        order by amp.period_year
    </select>

    <select id="getStandAnnualPeriodYear" resultType="java.lang.String">
        select distinct period_year
        from fin_dm_opt_foi.DM_FOC_REPL_STANDARD_ANNL_AMP_T where version_id = #{versionId,jdbcType=NUMERIC}
        order by period_year desc limit 3
    </select>

    <select id="findDmFocMonthAccCostAmpList" resultMap="annualResultMap">
        SELECT
        <include refid="allField"/>
        <if test='costType == "SAME"'>
            FROM fin_dm_opt_foi.dm_foc_repl_same_total_mtd_index_t
        </if>
        <if test='costType == "REPLACE"'>
            FROM fin_dm_opt_foi.dm_foc_repl_mtd_index_t
        </if>
        <if test='costType == "STD"'>
            FROM fin_dm_opt_foi.dm_foc_repl_standard_mtd_index_t
        </if>
        WHERE del_flag = 'N'
        <if test='caliberFlag!= null and caliberFlag != ""'>
            AND caliber_Flag = #{caliberFlag}
        </if>
        <if test='groupCodeList != null and groupCodeList.size() > 0'>
            <foreach collection='groupCodeList' item="code" open="AND group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='(costType == "STD" or costType == "REPLACE") and versionId != null'>
            AND version_id = #{versionId}
        </if>
        <if test='costType == "SAME" and monthVersionId != null'>
            AND version_id = #{monthVersionId}
        </if>
        <if test='periodStartTime != null and periodStartTime != ""'>
            AND period_id <![CDATA[ >= ]]> #{periodStartTime}
        </if>
        <if test='periodEndTime != null and periodEndTime != ""'>
            AND period_id <![CDATA[ <= ]]> #{periodEndTime}
        </if>
        <if test='groupLevel =="LV0" and lv0DimensionSet != null and lv0DimensionSet.size() > 0'>
            <foreach collection='lv0DimensionSet' item="code" open="AND group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel =="LV1" and lv1DimensionSet != null and lv1DimensionSet.size() > 0'>
            <foreach collection='lv1DimensionSet' item="code" open="AND group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='groupLevel =="LV2" and lv2DimensionSet != null and lv2DimensionSet.size() > 0'>
            <foreach collection='lv2DimensionSet' item="code" open="AND group_code IN (" close=")" index="index"
                     separator=",">
                #{code}
            </foreach>
        </if>
        <if test='viewFlag != null'>
            and view_flag = #{viewFlag}
        </if>
        <if test='groupLevel != null and groupLevel != ""'>
            and group_level = #{groupLevel}
        </if>
        ORDER BY group_code,group_cn_name,period_id
    </select>

    <select id="findActualMonthNum" resultType="java.lang.Long">
        SELECT IFNULL(max(period_id),0) period_id
        FROM fin_dm_opt_foi.dm_foc_repl_standard_mtd_index_t t
        WHERE t.del_flag = 'N'
          AND t.version_id =(
            SELECT version_id
            FROM fin_dm_opt_foi.dm_foc_version_info_t
            WHERE del_flag = 'N'
              AND status = 1
              AND data_type = 'CATEGORY'
              AND version_type IN ('AUTO', 'FINAL')
            ORDER BY creation_date DESC
            LIMIT 1
            )
    </select>

    <select id="findMonAccVersion" resultType="java.lang.Long">
        SELECT version_id
        FROM fin_dm_opt_foi.dm_foc_version_info_t
        WHERE del_flag = 'N'
          AND status = 1
          AND data_type = 'ITEM'
        ORDER BY creation_date DESC
            LIMIT 1 OFFSET 0
    </select>

</mapper>